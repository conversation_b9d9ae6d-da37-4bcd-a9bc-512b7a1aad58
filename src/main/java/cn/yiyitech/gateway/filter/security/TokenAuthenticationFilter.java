package cn.yiyitech.gateway.filter.security;

import cn.yiyitech.gateway.constants.JwtConstants;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class TokenAuthenticationFilter implements GlobalFilter, Ordered {

    @Autowired
    private StringRedisTemplate redisTemplate;

    private static final long TOKEN_EXPIRE_TIME = 12 * 60; // 12小时

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String token = request.getHeaders().getFirst(JwtConstants.AUTH_TOKEN);

        if (StringUtils.isBlank(token)) {
            return chain.filter(exchange);
        }

        // 验证token，依次尝试验证普通token、dept token和admin token
        return validateMainToken(token)
                .flatMap(valid -> {
                    if (valid) {
                        // 更新token过期时间
                        refreshTokenExpiration(JwtConstants.TOKEN_SESSION + token);
                        return chain.filter(exchange);
                    }

                    // 尝试验证dept token
                    return validateDeptToken(token)
                            .flatMap(deptValid -> {
                                if (deptValid) {
                                    // 更新dept token过期时间
                                    refreshTokenExpiration(JwtConstants.TOKEN_SESSION_DEPT + token);
                                    return chain.filter(exchange);
                                }

                                // 尝试验证admin token
                                return validateAdminToken(token)
                                        .flatMap(adminValid -> {
                                            if (adminValid) {
                                                // 更新admin token过期时间
                                                refreshTokenExpiration(JwtConstants.TOKEN_SESSION + token);
                                                return chain.filter(exchange);
                                            }
                                            // 所有token验证都失败
                                            return unauthorizedResponse(exchange);
                                        });
                            });
                });
    }

    private Mono<Boolean> validateMainToken(String token) {
        return Mono.fromCallable(() -> {
            String tokenKey = JwtConstants.TOKEN_SESSION + token;
            Object value = redisTemplate.opsForHash().get(tokenKey, "mainUser");
            log.info("Validating main token: {}, value: {}", tokenKey, value);
            return value != null;
        }).subscribeOn(Schedulers.boundedElastic());
    }

    private Mono<Boolean> validateDeptToken(String token) {
        return Mono.fromCallable(() -> {
            String tokenKey = JwtConstants.TOKEN_SESSION_DEPT + token;
            Object value = redisTemplate.opsForHash().get(tokenKey, "token");
            log.info("Validating dept token: {}, value: {}", tokenKey, value);
            return value != null;
        }).subscribeOn(Schedulers.boundedElastic());
    }

    private Mono<Boolean> validateAdminToken(String token) {
        return Mono.fromCallable(() -> {
            String tokenKey = JwtConstants.TOKEN_SESSION + token;
            Object value = redisTemplate.opsForHash().get(tokenKey, "adminToken");
            log.info("Validating admin token: {}, value: {}", tokenKey, value);
            return value != null;
        }).subscribeOn(Schedulers.boundedElastic());
    }

    private void refreshTokenExpiration(String tokenKey) {
        redisTemplate.expire(tokenKey, TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
    }

    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        String responseBody = "{\"code\":401,\"message\":\"未登录，请先登录\"}";
        DataBuffer buffer = response.bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8));
        log.info("*************请先登录*************************");
        return response.writeWith(Mono.just(buffer));
    }

    @Override
    public int getOrder() {
        return -90; // 在白名单过滤器之后执行
    }
}
