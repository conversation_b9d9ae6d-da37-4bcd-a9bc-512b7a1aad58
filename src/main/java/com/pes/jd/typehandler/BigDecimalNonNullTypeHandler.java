package com.pes.jd.typehandler;

import com.pes.jd.util.AppContext;
import org.apache.ibatis.executor.result.ResultMapException;
import org.apache.ibatis.type.BigDecimalTypeHandler;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/13 2:42 PM
 * @since 1.0.0
 */
public class BigDecimalNonNullTypeHandler extends BigDecimalTypeHandler {

    @Override
    public BigDecimal getResult(ResultSet rs, String columnName) throws SQLException {
        BigDecimal result;
        try {
            result = getNullableResult(rs, columnName);
        } catch (Exception e) {
            throw new ResultMapException("Error attempting to get column '" + columnName + "' from result set.  Cause: " + e, e);
        }
        if (rs.wasNull()) {
            if (AppContext.currentContext().getMybatisMapFlag()){
                return BigDecimal.ZERO;
            }
            return null;
        } else {
            return result;
        }
    }
}
