package com.pes.jd.typehandler;

import com.pes.jd.util.AppContext;
import org.apache.ibatis.executor.result.ResultMapException;
import org.apache.ibatis.type.IntegerTypeHandler;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/14 10:07 AM
 * @since 1.0.0
 */
public class IntegerNonNullTypeHandler extends IntegerTypeHandler {

    @Override
    public Integer getResult(ResultSet rs, String columnName) throws SQLException {
        Integer result;
        try {
            result = getNullableResult(rs, columnName);
        } catch (Exception e) {
            throw new ResultMapException("Error attempting to get column '" + columnName + "' from result set.  Cause: " + e, e);
        }
        if (rs.wasNull()) {
            if (AppContext.currentContext().getMybatisMapFlag()){
                return Integer.valueOf(0);
            }
            return null;
        } else {
            return result;
        }
    }
}
