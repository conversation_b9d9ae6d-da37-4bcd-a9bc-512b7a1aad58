package com.pes.jd.typehandler;

import com.pes.jd.util.AppContext;
import org.apache.ibatis.executor.result.ResultMapException;
import org.apache.ibatis.type.DoubleTypeHandler;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/13 2:09 PM
 * @since 1.0.0
 */
public class DoubleNonNullTypeHandler extends DoubleTypeHandler {

    private final static Double ZERO = Double.valueOf(0.0);

    @Override
    public Double getResult(ResultSet rs, String columnName) throws SQLException {
        Double result;
        try {
            result = getNullableResult(rs, columnName);
        } catch (Exception e) {
            throw new ResultMapException("Error attempting to get column '" + columnName + "' from result set.  Cause: " + e, e);
        }
        if (rs.wasNull()) {
            if (AppContext.currentContext().getMybatisMapFlag()){
                return ZERO;
            }
            return null;
        } else {
            return result;
        }
    }

}
