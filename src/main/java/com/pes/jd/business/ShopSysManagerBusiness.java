package com.pes.jd.business;

import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.GroupParam;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.CsNickLstForRealTimeVO;
import com.pes.jd.model.VO.MultiShopGroupVO;
import com.pes.jd.ms.domain.Data.master.DeptShop;
import com.pes.jd.ms.domain.Response.RestApiResponse2;

import java.util.Date;
import java.util.List;

public interface ShopSysManagerBusiness {

	ApiResponse updateShopCustomerServices(MasterServiceShopQuery shop, String nickArrStr, String groupId, Integer type) throws Exception;

	ApiResponse selectShopSubUserForCsSetting(MasterServiceShopQuery shop, String nick) throws Exception;

	ApiResponse batchUpdateShopCs(MasterServiceShopQuery shop, String nickArrStr, String groupId, Integer type, Integer operateType)
			throws Exception;

	List<ShopQuery> selectShopGroupMemberShops(ShopQuery mainShop) throws Exception;

	List<String> selectCsNickByShopIdByGroupIdByCsNick(MasterServiceShopQuery shop, String groupId, String csNick) throws Exception;

	List<GroupDTO> queryShopCsGroups(MasterServiceShopQuery shop, String groupName) throws Exception;

	ApiResponse createOrUpdateShopCsGroup(ShopQuery shop, Long groupId, String groupName) throws Exception;

	ApiResponse deleteCsGroup(ShopQuery shop, Long groupId) throws Exception;

	List<CsDTO> selectShopCs(MasterServiceShopQuery shop, String operateType, String type) throws Exception;

	ApiResponse refreshShopSubUsers(MasterServiceShopQuery shop) throws Exception;

	List<MultiShopGroupVO> selectMultiShopGroups(ShopQuery shop) throws Exception;

	ApiResponse updateIsShowMultipleShop(ShopQuery mainShop, ShopUserDTO mainSeller) throws Exception;

	ApiResponse selectShopGroupsOfOwner(ShopQuery mainShop) throws Exception;

	ApiResponse createNewShopGroup(ShopQuery mainShop, String shopGroupName) throws Exception;

	ApiResponse updateShopGroupName(ShopQuery mainShop, String shopGroupId, String shopGroupName) throws Exception;

	ApiResponse updateShopGroupMutualWatch(String shopGroupId, Boolean mutualWatch) throws Exception;

	ApiResponse deleteShopGroup(ShopQuery mainShop, String shopGroupId) throws Exception;

	ApiResponse sendInviteRequestForShopGroup(ShopQuery mainShop, String inviteShopInfo, String shopGroupId) throws Exception;

	ApiResponse moveShopToShopGroup(ShopQuery mainShop, String shopIds, String shopGroupId) throws Exception;

	ApiResponse shopGroupSystemSettingSynchronization(ShopQuery selectedShop, String toShopIds, String syncType) throws Exception;

	ApiResponse selectJoinedMyGroupShopList(ShopQuery mainShop, String shopGroupId) throws Exception;

	ApiResponse selectJoinGroupList(ShopQuery mainShop) throws Exception;

	ApiResponse selectShopAccredit(ShopQuery mainShop) throws Exception;

	ApiResponse updateAuthorizeShopStatus(ShopQuery mainShop, Integer status, String requestId) throws Exception;

	//start-------------------performance settings------------------
	ApiResponse updatePerformanceSettings(ShopQuery selectedShop, ShopSystemsettingDTO shopSystemsettingDTO, String requestUrl) throws Exception;

	ApiResponse addFliterBuynick(ShopQuery selectedShop, String nick, String currentUser) throws Exception;

	ApiResponse deleteFliterBuynick(ShopQuery selectedShop, String nick) throws Exception;

	ApiResponse getSystemSettingForQN(ShopQuery selectedShop) throws Exception;
	ApiResponse queryIsModifyEnquiryAndOutStockValidTime(ShopQuery selectedShop) throws Exception;

	ApiResponse selectCategoryLst(ShopQuery selectedShop) throws Exception;

	ApiResponse selectGoodsinfoLst(ShopQuery selectedShop,
                                   String categoryId,
                                   String name,
                                   Integer status) throws Exception;

	ApiResponse addGoodsFilter(ShopQuery selectedShop,
                               String goodList) throws Exception;

	ApiResponse deleteGoodsFilter(ShopQuery selectedShop,
                                  String numIid) throws Exception;

	//end-------------------performance settings------------------


	ApiResponse updateAdminSubUserType(String nick, String type) throws Exception;



	// ----------------权限设置------------

	/**
	 * 获取权限的基本信息
	 */
	ApiResponse searchBaseTreeInfo(ShopQuery shop, String nick, Long userId)throws Exception;

	/**
	 * 	获取子账户权限集合
	 */
	ApiResponse searchShopAccountInfo(ShopQuery shop, String nick, Integer flag, Long userId)throws Exception;
	/**
	 * 	增加修改删除权限
	 */
	ApiResponse insertOrUpdatePermission(ShopQuery shop, Integer type, String body)throws Exception;

	//------------------------------访问码设置------------------------------

	/**
	 *  设置访问码
	 */
	ApiResponse settingVisitCode(String visitCode, String visitEmail, Integer flag, ShopQuery shop)throws Exception;
	/**
	 *  验证访问码
	 */
	ApiResponse validVisitCode(String visitCode, ShopQuery shop)throws Exception;
	/**
	 *  找回访问码
	 */
	ApiResponse findVisitCode(ShopQuery shop)throws Exception;


	/**
	 *  根据shopId获取访问码email
	 */
	ApiResponse findEmail(ShopQuery shop) throws Exception;




	// ---------------------------------------- { 用户日志 } --------------------------------------------

	/**
	 *  用户登录日志查询
	 */
	ApiResponse selectLoginLog(ShopQuery shop, Date startDate, Date endDate, String nick) throws Exception;
	/**
	 *  用户登录日志详情
	 */
	ApiResponse selectLoginLogDetails(ShopQuery shop, Date startDate, Date endDate, String nick)throws Exception;
	/**
	 *  用户操作日志查询
	 */
	ApiResponse selectOperationLog(ShopQuery shop, Date startDate, Date endDate, String nick, String optType)throws Exception;
	/**
	 *  用户操作日志插入
	 */
	ApiResponse insertOperationLog(ShopUserDTO shopdto, ShopQuery shop, String optContent, String optType)throws Exception;

	List<String> selectCsNickByShopIdByGroupIdByCsNickForSub(UserShopQuery shop, String groupId, String csNick)
			throws Exception;

	ApiResponse updateCs(MasterServiceShopQuery shop, String csNick, String simpleNick, Integer type, String groupId)
			throws Exception;

	ApiResponse selectGroupCsByGroupParam(MasterServiceShopQuery shop, GroupParam param) throws Exception;

	List<CsDTO> selectShopCswwSimpleNames(ShopQuery shop, Integer type) throws Exception;

	ApiResponse selectUserByShopId(ShopQuery shop) throws Exception;

	List<UserQuery> selectCsNickByShopIdByGroupIdByCsNickForQuery(MasterServiceShopQuery shop, String groupId,
                                                                  String csNick) throws Exception;

	List<UserQuery> selectCsNickByShopIdByGroupIdByCsNickForSubQuery(UserShopQuery shop, String groupId, String csNick) throws Exception;

	List<UserQuery> selectCsNickByShopIdByGroupIdByCsNickForSubQueryNew(UserShopQuery shop, String groupId, String csNick) throws Exception;

	ApiResponse selectShopAccountForShop(ShopQuery shopQuery, String includeMainAccount) throws Exception;

	ApiResponse lockCs(MasterServiceShopQuery shop, String csNickJson, Integer operateType) throws Exception;

	ShopQuery getShopInfo(Long shopId) throws Exception;

	ApiResponse selectMultiShopGroupByShopId(MasterServiceShopQuery shop) throws Exception;

	RealtimeShopDTO getRealTimeShopByShopId(Long shopId) throws Exception;

	List<CsNickLstForRealTimeVO> selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(UserShopQuery shop, String groupId, String csNick) throws Exception;

	ApiResponse getShopInfoByShopNameForPlugin(String shopName) throws Exception;

	ApiResponse selectCsByShopIdByTypeForPlugin(Long shopId, Integer type) throws Exception;


	ApiResponse selectGroupCsByGroupParamForPlugin(MasterServiceShopQuery shop, GroupParam param)throws Exception;

	RestApiResponse2<Object> batchUpdateCsofGroup(Long shopId, String groupId, String csNicks) throws  Exception;

	List<DeptShop> selectDeptShopInfoByDeptId(String shopName, Long deptId, String id, String type) throws Exception;

    String selectGroupIdByShopId(String shopId);

    ApiResponse selectCategoryLstV2(ShopQuery shopQuery)throws  Exception;
}
