package com.pes.jd.business;


import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.ms.domain.Data.rtsub.ShopClosureConversion;
import com.pes.jd.ms.domain.Data.rtsub.ShopUseConversion;
import com.pes.jd.ms.domain.Data.shopdata.ShopCsDuty;
import com.pes.jd.ms.domain.Data.shopdata.ShopOvDay;
import com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Result.master.ShopLoginResult;
import com.pes.jd.ms.domain.Result.master.ShopSubScribeResult;

import java.util.Date;
import java.util.List;

public interface ShopOperateDetailBussiness {

	RestApiResponse2<ShopSubScribeResult> selectShopSubScribeForUseAnalysis(UserAnalysisParam param)
			throws Exception;


	RestApiResponse2<List<ShopUseConversion>> selectShopConverUserInfo(String db, List<CauseShop> causeShopLst,
                                                                       CustConversionParam param) throws Exception;


	RestApiResponse2<List<ShopSubScribe>> selectShopSubScribeDetailForUseAnalysis(UserAnalysisParam param)
			throws Exception;


	RestApiResponse2<List<ShopCsDuty>> selectCsLoginLogForShopUserAnalysis(String db, String schemaId,
                                                                           UserAnalysisParam param) throws Exception;


	RestApiResponse2<ShopLoginResult> selectCsLoginDetailForShopUserAnalysis(UserAnalysisParam param) throws Exception;


	RestApiResponse2<List<ShopUrge>> selectUrgeShopByShopId(Long shopId) throws Exception;
	RestApiResponse2<List<ShopOvDay>> slectMultiShopSaleAmount(String db, List<CauseShop> causeShopLst, Date startDate, Date endDate) throws Exception;

	RestApiResponse2<List<ShopClosureConversion>> selectUrgeCompareClosureShopLst(String db, List<ShopUrge> urgeShopLst, Date startDate, Date endDate, Integer taskType) throws Exception;

	RestApiResponse2<List<ShopUseAnalysis>> selectShopUseAnalysisByShopIdSetByDate(String db, List<CauseShop> causeShopLst, Date startDate, Date endDate) throws Exception;

	ApiResponse selectShopAuthExpiredLst(String startDate, String endDate, String nick, Integer shopType)throws Exception;
}
