package com.pes.jd.business.opt;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSSClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.constants.OssClientConstants;
import com.pes.jd.model.DTO.MarketingActivityLogDTO;
import com.pes.jd.model.DTO.MarketingActivityV2DTO;
import com.pes.jd.model.DTO.MarketingActivityV2RespDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.AliyunOSSClientUtil;
import com.pes.jd.util.DateTimeUtil;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

@Service
public class MarketingActivityOpt {

    private static Logger logger = LoggerFactory.getLogger(MarketingActivityOpt.class);

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    public ApiResponse getEffectDate() {
        try {

            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Object> responseTypeRef = usermgrRestTemplate.getRest(serviceId,"/marketingActivity/v2/getEffectDate", new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if(responseTypeRef.getSuccess()){
                return ApiResponse.ofSuccess(responseTypeRef.getData());
            }
        } catch (Exception e) {
            throw e;
        }
        return ApiResponse.ofFail(ApiCodeEnum.CODE_ERROR_1002);
    }

    public ApiResponse getFrequency() {
        try {

            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Object> responseTypeRef = usermgrRestTemplate.getRest(serviceId,"/marketingActivity/v2/getFrequency", new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if(responseTypeRef.getSuccess()){
                return ApiResponse.ofSuccess(responseTypeRef.getData());
            }
        } catch (Exception e) {
            throw e;
        }
        return ApiResponse.ofFail(ApiCodeEnum.CODE_ERROR_1002);
    }

    public ApiResponse getOrderType() {
        try {

            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Object> responseTypeRef = usermgrRestTemplate.getRest(serviceId,"/marketingActivity/v2/getOrderType", new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if(responseTypeRef.getSuccess()){
                return ApiResponse.ofSuccess(responseTypeRef.getData());
            }
        } catch (Exception e) {
            throw e;
        }
        return ApiResponse.ofFail(ApiCodeEnum.CODE_ERROR_1002);
    }

    public ApiResponse getAppVersion() {
        try {

            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Object> responseTypeRef = usermgrRestTemplate.getRest(serviceId,"/marketingActivity/v2/getAppVersion", new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if(responseTypeRef.getSuccess()){
                return ApiResponse.ofSuccess(responseTypeRef.getData());
            }
        } catch (Exception e) {
            throw e;
        }
        return ApiResponse.ofFail(ApiCodeEnum.CODE_ERROR_1002);
    }

    public ApiResponse insert(MarketingActivityV2DTO dto) throws Exception {
        //上传图片到阿里云
        String fileName = uploadImageToAliyun((MultipartFile) dto.getActivityContent());

        //同步保存到数据库
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("activityName", dto.getActivityName())
                .put("activityContent", OssClientConstants.DOWNURL + fileName)
                .put("startDate", dto.getStartDate())
                .put("endDate", dto.getEndDate())
                .put("version", dto.getVersion())
                .put("orderType", dto.getOrderType())
                .put("effectDate", dto.getEffectDate())
                .put("frequency", dto.getFrequency())
                .put("shopType", CommonConstants.POP_TYPE)
                .put("url", dto.getUrl())
                .put("activitySort", dto.getActivitySort())
                .toRequestEntity();


        logger.info("===>body: {}", JSONObject.toJSONString(body.getBody()));
        logger.info("===>header: {}", JSONObject.toJSONString(body.getHeaders()));

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/v2/insert", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {});

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }

        return response;
    }

    public ApiResponse deleteByPrimaryKey(Long id) throws HttpClientErrorException {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", id)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/v2/delete", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {});

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }
        return response;
    }

    public ApiResponse update(MarketingActivityV2DTO dto) throws Exception {
        String imageUrl = "";
        try{
            //上传图片到阿里云
            if(dto.getActivityContent() instanceof MultipartFile){
                imageUrl = OssClientConstants.DOWNURL + uploadImageToAliyun((MultipartFile)dto.getActivityContent());
            }else {
                imageUrl = (String) dto.getActivityContent();
            }
        } catch (ClassCastException e) {
            imageUrl = null;
        }
        //同步保存到数据库
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", dto.getId())
                .put("activityName", dto.getActivityName())
                .put("activityContent", imageUrl)
                .put("startDate", dto.getStartDate())
                .put("endDate", dto.getEndDate())
                .put("orderType", dto.getOrderType())
                .put("effectDate", dto.getEffectDate())
                .put("frequency", dto.getFrequency())
                .put("version", dto.getVersion())
                .put("url", dto.getUrl())
                .put("enableSwitch", dto.getEnableSwitch())
                .put("shopType", CommonConstants.POP_TYPE)
                .put("activitySort", dto.getActivitySort())
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);
        logger.info("===>body: {}", JSONObject.toJSONString(body.getBody()));
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/v2/update", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {
            });
            logger.info("===>error: {}", JSONObject.toJSONString(responseTypeRef));;
            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }else {
                response.setRpMsg(responseTypeRef.getRpMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        return response;
    }

    public ApiResponse updateEnable(Long id, Boolean enableSwitch) throws HttpClientErrorException {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", id)
                .put("enableSwitch", enableSwitch)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/v2/updateEnable", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {
            });

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }
        return response;
    }

    public ApiResponse selectActivityByActivityNameAndDate(Date startDate, Date endDate, String activityName) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("startDate", DateUtils.formatYMdHms(DateUtil.getStartTimeOfDate(startDate)))
                .put("endDate", DateUtils.formatYMdHms(DateUtil.getEndTimeOfDate(endDate)))
                .put("activityName", activityName)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        response.setSuccess(Boolean.FALSE);
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<List<MarketingActivityV2RespDTO>> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/v2/selectActivityByActivityNameAndDate", body, new ParameterizedTypeReference<RestResponseTypeRef<List<MarketingActivityV2RespDTO>>>() {
            });
            logger.info("===>error: {}", JSONObject.toJSONString(responseTypeRef));;
            if(responseTypeRef.getSuccess()){
                List<MarketingActivityV2RespDTO> activityLst = responseTypeRef.getData();
                Map<String,Object> data = Maps.newHashMap();
                data.put("activityLst", activityLst);
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
                response.setSuccess(Boolean.TRUE);
                return response;
            }else {
                response.setRpMsg(responseTypeRef.getRpMsg());
            }
        } catch (Exception e) {
            throw e;
        }
        return response;
    }

    public List<MarketingActivityV2RespDTO> selectEnableActivity(Long shopId, String csNick, LocalDateTime now) {
        try {
        	 HttpEntity<Object> body = RequestEntityBuilder.builder()
                     .put("shopType", CommonConstants.POP_TYPE)
                     .put("shopId", shopId)
                     .put("csNick", csNick)
                     .put("currentTime", DateTimeUtil.localDateTimeFormatyMdHms(now))
                     .toRequestEntity();
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<List<MarketingActivityV2RespDTO>> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/v2/selectEnableActivity", body, new ParameterizedTypeReference<RestResponseTypeRef<List<MarketingActivityV2RespDTO>>>() {
            });
            logger.info("======>selectEnableActivity: {}", JSONObject.toJSONString(responseTypeRef));
            if(responseTypeRef.getSuccess()){
                List<MarketingActivityV2RespDTO> activityLst =  responseTypeRef.getData();
                return activityLst;
            }
        } catch (Exception e) {
            throw e;
        }
        return Lists.newArrayListWithCapacity(0);
    }

    public List<ShopSubScribe> selectShopSubscribeByShopId(Long shopId)  {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .toRequestEntity();
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<List<ShopSubScribe>> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/shop/Subscribe/selectShopSubscribeByShopId", body, new ParameterizedTypeReference<RestResponseTypeRef<List<ShopSubScribe>>>() {
            });

            if(responseTypeRef.getSuccess()){
                List<ShopSubScribe> subLst = responseTypeRef.getData();
                return subLst;
            }
        } catch (Exception e) {
            throw e;
        }
        return Lists.newArrayListWithCapacity(0);
    }

    /**
     * 将图片上传到阿里云
     * @param file 图片文件
     * @return
     * @throws Exception
     */
    private String uploadImageToAliyun(MultipartFile file) throws Exception{

        OSSClient ossClient = AliyunOSSClientUtil.getOSSClient();

        //String name = file.getOriginalFilename();
        String substring = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")).toLowerCase();
        Random random = new Random();
        String name = random.nextInt(10000) + System.currentTimeMillis() + substring;

        try {
            InputStream inputStream = file.getInputStream();
            AliyunOSSClientUtil.uploadFile2OSS(inputStream, name, ossClient);
        } catch (Exception e) {
            throw new Exception("图片上传失败");
        }
        return name;
    }

    public ApiResponse insertLog(MarketingActivityLogDTO dto) throws Exception {

        //同步保存到数据库
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("activityName", dto.getActivityName())
                .put("activityId", dto.getActivityId())
                .put("shopId", dto.getShopId())
                .put("csNick", dto.getCsNick())
                .put("optType", dto.getOptType())
                .toRequestEntity();

        logger.info("===>body: {}", JSONObject.toJSONString(body.getBody()));

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        try {
            response = usermgrRestTemplate.postRest(serviceId, "/marketingActivityLog/insert", body);
            if(response.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            logger.error("web template  updateOrInsert error:{}", e.getMessage(), e);
            throw e;
        }
        return response;
    }

    public ApiResponse summary(String startDate, String endDate){
        //同步保存到数据库
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        try {
            ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId, "/marketingActivityLog/summary", body);
            if(apiResponse.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, apiResponse.getData());
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            logger.error("web template  updateOrInsert error:{}", e.getMessage(), e);
            throw e;
        }
        return response;
    }
}
