package com.pes.jd.business;

import com.pes.jd.model.Response.ApiResponse;

/**
 * @Author: aiJun
 * @Date: 2019-06-24 14:05
 * @Version 1.0
 */
public interface TaskAssignDBBusiness {
    ApiResponse pullAssignDB(String dbName, String schemaId, String startDateStr, String endDateStr, Integer shopType);

    ApiResponse cleanJobPullRecord(String dbName, String schemaId, String startDateStr, String endDateStr);

    ApiResponse searchJobPullRecord(String dbName, String schemaId, String startDateStr, String endDateStr, Integer status, String nick);

    ApiResponse searchAllDBNameAndAllSchemaId(Integer type);

    ApiResponse calShopUseCondition(String startDateStr, String endDateStr, String shopId, String dbName, String schemaId, Integer shopType);
}
