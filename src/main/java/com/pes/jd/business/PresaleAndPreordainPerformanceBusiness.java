package com.pes.jd.business;

import com.pes.jd.model.Query.ShopQuery;

import java.util.Map;

public interface PresaleAndPreordainPerformanceBusiness {

    Object selectShopPresale(ShopQuery selectedShop, String startDate, String endDate, String skuIdOrName, Integer enquiryValidDurationTime) throws Exception;

    Object selectShopPresaleDetails(ShopQuery selectedShop, String activityId, Long skuId, String startDate, String endDate, Integer type) throws Exception;

    Object selectCsPresale(ShopQuery selectedShop, String startDate, String endDate, Map<String, String> csNickMap, String skuIdOrName, Integer enquiryValidDurationTime) throws Exception;

    Object selectCsPresaleDetails(ShopQuery selectedShop, String activityId, Long skuId, String startDate, String endDate, String csNick) throws Exception;
}
