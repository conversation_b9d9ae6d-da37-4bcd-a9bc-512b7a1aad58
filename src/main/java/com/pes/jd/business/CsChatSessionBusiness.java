package com.pes.jd.business;

import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/21 10:01 AM
 * @since 1.0.0
 */
public interface CsChatSessionBusiness {

    /**
     *  拉取 chatSession 数据并  入库
     */
    int insertBatchChatSession(JobShopQuery jobShop, Date startDate, Date endDate, String csNick) throws Exception;

    /**
     *  查询指定时间段 chatSession 数据
     */
    List<CsChatSessionDO> searchAllByTime(Date beginDate, Date endDate,Long shopId,String schemaId);

}
