package com.pes.jd.business;

import com.pes.jd.model.Param.DotParam;
import com.pes.jd.model.Response.ApiResponse;

import java.util.Date;

public interface DotBusiness {
    /**
     * 保存打点信息
     * @param dotParam
     * @return
     */
    ApiResponse dotInfoSave(DotParam dotParam) throws Exception;

    ApiResponse getDotInfoList(Date startTime, Date endTime, String pageName) throws Exception;

    ApiResponse getDotInfoDetails(String shopName, String nick, String pageVisitName, Date sDate, Date eDate) throws Exception;

    ApiResponse getUserInfo(Date startTime, Date endTime) throws Exception;
}
