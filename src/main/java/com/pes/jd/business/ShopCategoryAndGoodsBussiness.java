package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface ShopCategoryAndGoodsBussiness {

	void pullShopCategory(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void pullShopGood(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void pullShopSku(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void pullShopPresaleSku(JobShopQuery jobShop) throws Exception;

	void pullShopCategoryV2(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
}
