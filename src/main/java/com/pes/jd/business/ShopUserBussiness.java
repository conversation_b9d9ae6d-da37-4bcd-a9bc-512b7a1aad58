package com.pes.jd.business;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.ms.domain.Data.rtsub.ShopUseConversion;
import com.pes.jd.ms.domain.Result.task.dispatching.ShopUserLoginResult;

import java.util.Date;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月06 15:31:31<br>
 */
public interface ShopUserBussiness {
    ShopUserLoginResult getShopLoginSubscribe(Long shopId, Date startDate, Date endDate) throws Exception;

     List<ShopUseConversion> selectShopUseConverAnalysis(JobShopDTO shop, Date startDate, Date endDate) throws Exception;

     List<ShopSubScribe>  selectShopSubscribe(Long shopId) throws Exception;

     List<ShopUserDTO> queryMainAccountList(Long shopId);
}
