package com.pes.jd.business;

import com.volcengine.service.SignableRequest;
import org.apache.http.NameValuePair;

import java.util.List;
import java.util.Map;

public interface KnowledgVolcengine {


    SignableRequest prepareRequest(String host, String path, String method, List<NameValuePair> params, String body);

    /**
     * 搜索知识库
     */
    Map<String, Object> searchKnowledge(String collectionName, String query, String docId, double denseWeight, int limit, String projectName);

    /**
     * 添加单个节点
     */
    Map<String, Object> addPoint(String collectionName, String docId, String key, String value, String projectName);

    /**
     * 批量添加节点
     */
    Map<String, Object> addPointsBatch(String collectionName, List<Map<String, Object>> points, String projectName);

    /**
     * 列出节点
     */
    Map<String, Object> listPoints(String collectionName, int offset, int limit, List<String> docIds, String projectName);

    /**
     * 列出文档
     */
    Map<String, Object> listDocs(String collectionName, String docName, int offset, int limit, List<String> docType, String projectName);

    /**
     * 根据文档名查找文档ID
     */
    String findDocIdByName(String collectionName, String docName, String projectName);

}
