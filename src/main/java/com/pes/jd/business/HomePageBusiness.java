package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.ConversionSummaryDTO;
import com.pes.jd.model.DTO.TaskNumDTO;
import com.pes.jd.model.DTO.UpSaleDTO;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/29 4:23 PM
 * @since 1.0.0
 *
 *  Rest
 */
public interface HomePageBusiness {
    /**
     *   获取自定义列基本信息
     */
    ApiResponse searchAllWithCategory(ShopQuery shop, String name) throws Exception;

    /**
     *  查看自定义报表信息
     *
     *        @param type 报表类型
     *        1: 店铺
     *        2: 客服
     *
     */
    ApiResponse searchAllByShopId(ShopQuery shop, Integer type) throws Exception;


    /**
     *  增加修改报表
     * @param type 1 增加  2修改
     */
    ApiResponse insertOrUpdateCustomReport(ShopQuery shop, String report, Integer type, String nick) throws Exception;

    ApiResponse deleteCustomReport(ShopQuery shop, Long id) throws Exception;

    ApiResponse insertCustomReport(String dbField) throws DBNotExistException;

    List<UpSaleDTO> querySaleAmount(ShopQuery shopQuery, String startDate, String endDate);

    TaskNumDTO queryTaskNum(ShopQuery shopQuery, String startDateStr, String endDateStr);

    ApiResponse queryMenuByShopId(String shopId);

    ApiResponse queryMenuByShopIdOfSimple(String shopId);

    ApiResponse updateShopMenuHome(String shopId, String ids);

    ApiResponse updateShopMenuHomeOfSimple(String shopId, String ids);

    ConversionSummaryDTO searchConversionSummary(ShopQuery shopQuery, String date);
}
