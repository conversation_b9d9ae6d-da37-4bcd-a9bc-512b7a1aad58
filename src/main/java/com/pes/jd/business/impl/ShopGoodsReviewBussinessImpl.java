package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.ShopGoodsReviewBussiness;
import com.pes.jd.dao.*;
import com.pes.jd.data.converter.ShopGoodsReviewConverter;
import com.pes.jd.model.DO.ShopGoodsReviewDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service
public class ShopGoodsReviewBussinessImpl implements ShopGoodsReviewBussiness {

	private Logger logger = LoggerFactory.getLogger(ShopGoodsReviewBussinessImpl.class);

	@Resource
	private ShopGoodsReviewDao shopGoodsReviewDao;

	@Resource
	private OrderDao orderDao;

	@Resource
	private CsOrderEvaluateDao csOrderEvaluateDao;

	@Resource
	private CsOrderIndexDao csOrderIndexDao;

	@Resource
	private ShopGoodsReviewConverter shopGoodsReviewConverter;

	@Resource
	private ShopOrderEvaluateDao shopOrderEvaluateDao;

	@Resource
	private OrderDetailDao orderDetailDao;

	@Resource
	private CsOrderBindDao csOrderBindDao;

	@Override
	public void pullShopGoodsReview(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
			throws Exception {
		long s = System.currentTimeMillis();
		try {
			shopGoodsReviewConverter.persistShopGoodsReview(jobShop, jobDate.getDate());
		} catch (Exception e) {
//			logger.error("【{}】handle shop goodsReview error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if (logger.isDebugEnabled()) {
			logger.debug("订单的中差评基础数据获取	pull shop goodsReview end,time:{} ms", (e - s));
		}

	}

//	@Override
//	public void handleShopCsOrderReview(JobShopQuery jobShop, Date startDate, Date endDate, boolean isDelData)
//			throws Exception {
//		long s = System.currentTimeMillis();
//		JobShopDTO shop = shop.getJobShop();
//		List<CsDTO> csLst = shop.getCsLst();
//		try {
//			List<Date> dateList = DateUtil.splitDate(startDate, endDate);
//			for (Date date : dateList) {
//				startDate = date;
//				endDate = DateUtil.getEndTimeOfDate(date);
//				List<CsOrderEvaluateDTO> csOrderEvaluateDOLst = new ArrayList<CsOrderEvaluateDTO>();
//				// 查询客服绑定的订单
//				List<OrderDTO> retCsOrderLst = orderDao.selectShopCsOrderByCsByDate(jobShop, csLst, startDate, endDate);
//				if (CollectionUtils.isNotEmpty(retCsOrderLst)) {
//					Map<String, List<OrderDTO>> csOrderMap = retCsOrderLst.stream()
//							.collect(Collectors.groupingBy(OrderDTO::getSellerNick));
//					Map<String, List<ShopGoodsReviewDTO>> goodsReviewMap = new HashMap<String, List<ShopGoodsReviewDTO>>();
//					for (Entry<String, List<OrderDTO>> csOrderEntry : csOrderMap.entrySet()) {
//						//根据  orderId 查询客服下订单的首次聊天时间
//						List<CsOrderIndexDTO> retCsOrderIndexLst = csOrderIndexDao.selectOrderIndexByOrderIdByCs(jobShop,
//								csOrderEntry.getKey(), csOrderEntry.getValue());
//						List<ShopGoodsReviewDTO> goodsReviewLst = Lists.newArrayList();
//						if (CollectionUtils.isNotEmpty(retCsOrderIndexLst)) {
//							ShopGoodsReviewDTO goodReview = null;
//							//确定订单属于售前、售中、售后
//							for (CsOrderIndexDTO csOrderIndex : retCsOrderIndexLst) {
//								goodReview = new ShopGoodsReviewDTO();
//								goodReview.setSellerNick(csOrderIndex.getCsNick());
//								goodReview.setShopId(shop.getShopId());
//								goodReview.setOrderId(csOrderIndex.getOrderId());
//								
//								long firstChatTime = csOrderIndex.getFirstChatDate().getTime();
//								long orderCreatedTime = csOrderIndex.getOrderCreated().getTime();
//								long orderPayTime = csOrderIndex.getOrderPayDate().getTime();
//								if (firstChatTime > orderCreatedTime) {
//									goodReview.setOrderType(CommonConstants.ORDER_EVALUATION_TYPE_PRE);
//								} else if (firstChatTime > orderPayTime) {
//									goodReview.setOrderType(CommonConstants.ORDER_EVALUATION_TYPE_BET);
//								} else {
//									goodReview.setOrderType(CommonConstants.ORDER_EVALUATION_TYPE_AFTER);
//								}
//								goodsReviewLst.add(goodReview);
//							}
//						}
//						goodsReviewMap.put(csOrderEntry.getKey(), goodsReviewLst);
//					}
//					for (Entry<String, List<ShopGoodsReviewDTO>> goodsReviewEntry : goodsReviewMap.entrySet()) {
//						//根据orderId查询每个客服下的中差评
//						List<ShopGoodsReviewDTO> orderSkuReviewLst = shopGoodsReviewDao.selectShopCsReviewByOrderId(jobShop,
//								goodsReviewEntry.getValue());
//						if (CollectionUtils.isNotEmpty(orderSkuReviewLst)) {
//							//统计客服每天售前、售中、售后的中差评
//							CsOrderEvaluateDTO csOrderEvaluate = calShopGoodsReview(goodsReviewEntry.getValue(),
//									orderSkuReviewLst, goodsReviewEntry.getKey(), shop.getShopId(), date);
//							csOrderEvaluateDOLst.add(csOrderEvaluate);
//						}
//					}
//					if (CollectionUtils.isEmpty(csOrderEvaluateDOLst)) {
//						logger.info("{} CsOrderEvaluate insertList is Empty", shop.getJobShop().getTitle());
//					} else {
//						int insertCsOrderEvaluateNum = csOrderEvaluateDao.insertCsOrderEvaluate(shop.getJobShop(),
//								csOrderEvaluateDOLst);
//						logger.info("{} CsOrderEvaluate insertCsOrderEvaluateNum = {} ", shop.getJobShop().getTitle(),
//								insertCsOrderEvaluateNum);
//					}
//				} else {
//					// 客服绑定的订单查询为空
//					logger.info("{} bindCsOrder is Empty", shop.getJobShop().getTitle());
//				}
//				
//			}
//		} catch (Exception e) {
//			logger.error("【{}】handle shop CsOrderEvaluate error", shop.getJobShop().getTitle(), e);
//			throw e;
//		}
//		long e = System.currentTimeMillis();
//		logger.info("handle shop csOrderReview end,time:{}", (e - s) / 1000);
//		
//	}
	
	@Override
	public void handleShopCsOrderReview(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
			throws Exception {
		long s = System.currentTimeMillis();
		try {
			//给交易评价表对应的绑定指定的客服
			handleShopOrderSkuEvaluate(jobShop, jobDate,isDelData);
			handleShopCsDayOrderEvaluate(jobShop, jobDate,isDelData);
		} catch (Exception e) {
			logger.error("【{}】handle shop CsOrderEvaluate error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("客服绑定订单中差评计算	handle shopCs orderReview end,time:{} ms", (e - s));
		}

	}

	private void handleShopOrderSkuEvaluate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
		Date date = jobDate.getDate();
		Date endDate = DateUtil.getEndTimeOfDate(date);
		JobShopDTO shop = jobShop.getShop();
		List<ShopGoodsReviewDO> insertGoodsReviewDOLst = new ArrayList<>();
		//交易评价表基础数据获取
		List<ShopGoodsReviewDO> shopGoodsReviewLst=shopGoodsReviewDao.selectShopCsOrderBySendTime(jobShop,date);
		if (CollectionUtils.isNotEmpty(shopGoodsReviewLst)) {
			//------------------------------计算店铺维度中差评数量------------------------------
			int goodEvaluateNum = 0;
			int neutralEvaluateNum = 0;
			int badEvaluateNum = 0;
			for (ShopGoodsReviewDO shopGoodsReview : shopGoodsReviewLst) {
				switch (shopGoodsReview.getScore()) {
					case CommonConstants.EVALUATION_SCORE_GOOD_5:
					case CommonConstants.EVALUATION_SCORE_GOOD_4:
						goodEvaluateNum++;
						break;
					case CommonConstants.EVALUATION_SCORE_NEUTRAL_3:
					case CommonConstants.EVALUATION_SCORE_NEUTRAL_2:
						neutralEvaluateNum++;
						break;
					case CommonConstants.EVALUATION_SCORE_BAD_1:
						badEvaluateNum++;
						break;
					default:
//					logger.debug("未定义的中差评分值 score = {}", shopGoodsReview.getScore());
						break;
				}
			}
			ShopOrderEvaluateDTO shopOrderEvaluate = new ShopOrderEvaluateDTO(jobShop.getShop().getShopId(), date, goodEvaluateNum, neutralEvaluateNum, badEvaluateNum);

			if (isDelData) {
				shopOrderEvaluateDao.deleteShopOrderEvaluateByDateByShopId(shop,date);
			}
			shopOrderEvaluateDao.insertShopOrderEvaluate(shop,date,shopOrderEvaluate);
			//------------------------------计算店铺维度中差评数量------------------------------
//------------------------------中差评匹配订单入库，未匹配到订单的评价直接入库------------------------------
			Date queryOrderStartDate = DateUtil.getDateByPeriod(DateUtil.getStartTimeOfDate(date), CommonConstants.DAY_EVAL_ORDER);

			//查询中差评绑定订单
			List<ShopGoodsReviewDO> selectOrdergoodsReviewList = orderDetailDao.selectOrderIdListBySkuidList(shop,queryOrderStartDate,endDate,shopGoodsReviewLst);
			List<ShopGoodsReviewDO> ordergoodsReviewList = Optional.ofNullable(selectOrdergoodsReviewList).orElse(Lists.newArrayList());
			Set<Long> csOrderIdSet = ordergoodsReviewList
					.stream()
					.map(ShopGoodsReviewDO::getOrderId)
					.collect(Collectors.toSet());
			List<Long> orderIdLst = Lists.newArrayList(csOrderIdSet);
			//根据订单号查询接待客服
			List<ShopGoodsReviewDO> shopGoodsCsLstByOrderId = csOrderBindDao.selectShopCsOrderBindCsNickByOrderIdLst(shop,queryOrderStartDate,endDate,orderIdLst);

			List<ShopGoodsReviewDO> orderBindGoodsReviewLst = Lists.newArrayList();
			//将中差评对应到接待客服
			if(CollectionUtils.isEmpty(shopGoodsCsLstByOrderId)){
//				logger.debug("date={},根据订单评价查询到订单{}条,但并未查询到绑定客服",date,ordergoodsReviewList.size());
				orderBindGoodsReviewLst.addAll(ordergoodsReviewList);
			}else{
				for (ShopGoodsReviewDO orderBindGoods : ordergoodsReviewList) {
					for (int i = 0; i < shopGoodsCsLstByOrderId.size(); i++) {
						ShopGoodsReviewDO csOrderbind = shopGoodsCsLstByOrderId.get(i);
						if(csOrderbind.getOrderId().equals(orderBindGoods.getOrderId())){
							orderBindGoods.setCsNick(csOrderbind.getCsNick());
							orderBindGoodsReviewLst.add(orderBindGoods);
							break;
						}
						if( i == shopGoodsCsLstByOrderId.size() - 1){
							//未匹配到绑定客服，静默下单评价：客服为空
							orderBindGoodsReviewLst.add(orderBindGoods);
						}
					}
				}
			}

			if (CollectionUtils.isNotEmpty(orderBindGoodsReviewLst)) {
				for (ShopGoodsReviewDO shopGoods : shopGoodsReviewLst) {
					Boolean isQueryOrder = false;
					for (ShopGoodsReviewDO orderGoods : orderBindGoodsReviewLst) {
						if (shopGoods.getSkuId().equals(orderGoods.getSkuId())
								&& shopGoods.getBuyerNick().equalsIgnoreCase(orderGoods.getBuyerNick())) {
							isQueryOrder = true;
							shopGoods.setOrderCreated(orderGoods.getOrderCreated());
							shopGoods.setOrderPayTime(orderGoods.getOrderPayTime());
							shopGoods.setOrderId(orderGoods.getOrderId());
							shopGoods.setCsNick(orderGoods.getCsNick());
							insertGoodsReviewDOLst.add(shopGoods);
							break;
						}
					}
					if(!isQueryOrder){
						insertGoodsReviewDOLst.add(shopGoods);
					}
				}
			}else{
				insertGoodsReviewDOLst.addAll(shopGoodsReviewLst);
			}
		}
		if (CollectionUtils.isEmpty(insertGoodsReviewDOLst)) {
//			logger.debug("数据转换层：~~~~~~~~店铺{},评价接口可转换数据为空", shopId);
		} else {
			// 转换的数据插入数据库
			int deleteNum = shopGoodsReviewDao.deleteShopGoodsReviewByDateByShopId(shop, date);
			if(logger.isDebugEnabled()){
				//			logger.debug("~~~~~~~~~~~~~~~~~店铺{}指定更新时间内删除的评价(中差评)记录条数为：{}", shopId, deleteNum);
			}
			int insertNum = shopGoodsReviewDao.insertShopGoodsReviewList(shop, date, insertGoodsReviewDOLst);
			if(logger.isDebugEnabled()){
				//			logger.debug("~~~~~~~~~~~~~~~~~店铺{}插入的登陆记录条数为：{},评价(中差评)记录数据转换结束", shopId, insertNum);
			}
		}
		//------------------------------中差评匹配订单入库，未匹配到订单的评价直接入库------------------------------

	}

	private void handleShopCsDayOrderEvaluate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception{
		Date date = jobDate.getDate();
		Date endDate = DateUtil.getEndTimeOfDate(date);
		JobShopDTO shop = jobShop.getShop();
		JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE,CommonConstants.CS_STATUS_NOT_LOCK);
		
		List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
		if(targetCsLst.isEmpty()){
			return;
		}
		
		//根据当前时间（评价时间）往前推180天
		Date finalQueryStartDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(date,CommonConstants.DAY_EVAL_ORDER));
		
		List<CsOrderEvaluateDTO> csOrderEvaluateDOLst = new ArrayList<CsOrderEvaluateDTO>();
		// 查询客服绑定的中差评订单（查询pes_order_sku_evaluate表）
		List<ShopGoodsReviewDTO> orderSkuReviewLst = shopGoodsReviewDao.selectShopCsOrderByCsBySendTime(shop, targetCsLst, date);
		if (CollectionUtils.isNotEmpty(orderSkuReviewLst)) {
			//按客服分组
			Map<String, List<ShopGoodsReviewDTO>> orderSkuReviewMap = orderSkuReviewLst.stream()
					.collect(Collectors.groupingBy(ShopGoodsReviewDTO::getCsNick));
			Map<String, List<ShopGoodsReviewDTO>> goodsReviewMap = new HashMap<String, List<ShopGoodsReviewDTO>>();
			for (Entry<String, List<ShopGoodsReviewDTO>> orderSkuReviewEntry : orderSkuReviewMap.entrySet()) {
				//根据  orderId 查询客服下订单的首次聊天时间....按月分表倒着查，查够数目就返回
				List<CsOrderIndexDTO> retCsOrderIndexLst = csOrderIndexDao.selectOrderIndexByOrderIdByCs(shop,finalQueryStartDate,endDate,orderSkuReviewEntry.getKey(), orderSkuReviewEntry.getValue());
				List<ShopGoodsReviewDTO> goodsReviewLst = Lists.newArrayList();
				if (CollectionUtils.isNotEmpty(retCsOrderIndexLst)) {
					ShopGoodsReviewDTO goodReview;
					//确定订单属于售前、售中、售后
					for (CsOrderIndexDTO csOrderIndex : retCsOrderIndexLst) {
						goodReview = new ShopGoodsReviewDTO();
						goodReview.setCsNick(csOrderIndex.getCsNick());
						goodReview.setShopId(shop.getShopId());
						goodReview.setOrderId(csOrderIndex.getOrderId());
						if(csOrderIndex.getFirstChatDate() == null || csOrderIndex.getOrderCreated() == null || csOrderIndex.getOrderPayDate() == null){
							continue;
						}
						long firstChatTime = csOrderIndex.getFirstChatDate().getTime();
						long orderCreatedTime = csOrderIndex.getOrderCreated().getTime();
						long orderPayTime = csOrderIndex.getOrderPayDate().getTime();
						if (firstChatTime < orderCreatedTime) {
							goodReview.setOrderType(CommonConstants.ORDER_EVALUATION_TYPE_PRE);
						} else if (firstChatTime < orderPayTime) {
							goodReview.setOrderType(CommonConstants.ORDER_EVALUATION_TYPE_BET);
						} else {
							goodReview.setOrderType(CommonConstants.ORDER_EVALUATION_TYPE_AFTER);
						}
						goodsReviewLst.add(goodReview);
					}
				}
				goodsReviewMap.put(orderSkuReviewEntry.getKey(), goodsReviewLst);
			}
			for (Entry<String, List<ShopGoodsReviewDTO>> goodsReviewEntry : goodsReviewMap.entrySet()) {
				List<ShopGoodsReviewDTO> csOrderSkuReviewLst = orderSkuReviewMap.get(goodsReviewEntry.getKey());
				if (CollectionUtils.isNotEmpty(csOrderSkuReviewLst)) {
					//统计客服每天售前、售中、售后的中差评
					CsOrderEvaluateDTO csOrderEvaluate = calShopGoodsReview(goodsReviewEntry.getValue(),
							csOrderSkuReviewLst, goodsReviewEntry.getKey(), shop.getShopId(), date);
					csOrderEvaluateDOLst.add(csOrderEvaluate);
				}
			}
			if (CollectionUtils.isEmpty(csOrderEvaluateDOLst)) {
				if(logger.isDebugEnabled()){
					logger.debug("{} CsOrderEvaluate insertList is Empty", shop.getTitle());
				}

			} else {
				if(isDelData){
					int deleteNum = csOrderEvaluateDao.deleteCsOrderEvaluateByDateByShop(shop,date);
					if(logger.isDebugEnabled()){
						logger.debug("{} CsOrderEvaluate deleteNum = {} ", shop.getTitle(),deleteNum);
					}

				}
				int insertCsOrderEvaluateNum = csOrderEvaluateDao.insertCsOrderEvaluate(shop,date,
						csOrderEvaluateDOLst);
				if(logger.isDebugEnabled()){
					logger.debug("{} CsOrderEvaluate insertCsOrderEvaluateNum = {} ", shop.getTitle(),
							insertCsOrderEvaluateNum);
				}

			}
		} else {
//			 客服绑定的订单查询为空
			if(logger.isDebugEnabled()){
				logger.debug("{} bindCsOrder is Empty", jobShop.getShop().getTitle());
			}
		}
		
	
	}

	private CsOrderEvaluateDTO calShopGoodsReview(List<ShopGoodsReviewDTO> goodsReviewLst,
			List<ShopGoodsReviewDTO> orderSkuReviewLst, String csNick, Long shopId, Date date) throws Exception {
		int totalGoodEvaluateNum = 0;
		int totalNeutralEvaluateNum = 0;
		int totalBadEvaluateNum = 0;
		int preGoodEvaluateNum = 0;
		int preNeutralEvaluateNum = 0;
		int preBadEvaluateNum = 0;
		int betGoodEvaluateNum = 0;
		int betNeutralEvaluateNum = 0;
		int betBadEvaluateNum = 0;
		int afterGoodEvaluateNum = 0;
		int afterNeutralEvaluateNum = 0;
		int afterBadEvaluateNum = 0;
		//一个商品一条评价
		for (ShopGoodsReviewDTO retGoodsReview : orderSkuReviewLst) {
			// 看商品对应是售前售中售后
			for (ShopGoodsReviewDTO goodsReview : goodsReviewLst) {
				if (retGoodsReview.getOrderId().equals(goodsReview.getOrderId())) {
					if (CommonConstants.ORDER_EVALUATION_TYPE_PRE.equals(goodsReview.getOrderType())) {
						switch (retGoodsReview.getScore()) {
						case CommonConstants.EVALUATION_SCORE_GOOD_5:
						case CommonConstants.EVALUATION_SCORE_GOOD_4:
							preGoodEvaluateNum++;
							break;
						case CommonConstants.EVALUATION_SCORE_NEUTRAL_3:
						case CommonConstants.EVALUATION_SCORE_NEUTRAL_2:
							preNeutralEvaluateNum++;
							break;
						case CommonConstants.EVALUATION_SCORE_BAD_1:
							preBadEvaluateNum++;
							break;
						default:
							logger.info("未定义的中差评分值 score = {}", goodsReview.getScore());
							break;
						}
						break;
					}
					if (CommonConstants.ORDER_EVALUATION_TYPE_BET.equals(goodsReview.getOrderType())) {
						switch (retGoodsReview.getScore()) {
						case CommonConstants.EVALUATION_SCORE_GOOD_5:
						case CommonConstants.EVALUATION_SCORE_GOOD_4:
							betGoodEvaluateNum++;
							break;
						case CommonConstants.EVALUATION_SCORE_NEUTRAL_3:
						case CommonConstants.EVALUATION_SCORE_NEUTRAL_2:
							betNeutralEvaluateNum++;
							break;
						case CommonConstants.EVALUATION_SCORE_BAD_1:
							betBadEvaluateNum++;
							break;
						default:
							logger.info("未定义的中差评分值 score = {}", goodsReview.getScore());
							break;
						}
						break;
					}
					if (CommonConstants.ORDER_EVALUATION_TYPE_AFTER.equals(goodsReview.getOrderType())) {
						switch (retGoodsReview.getScore()) {
						case CommonConstants.EVALUATION_SCORE_GOOD_5:
						case CommonConstants.EVALUATION_SCORE_GOOD_4:
							afterGoodEvaluateNum++;
							break;
						case CommonConstants.EVALUATION_SCORE_NEUTRAL_3:
						case CommonConstants.EVALUATION_SCORE_NEUTRAL_2:
							afterNeutralEvaluateNum++;
							break;
						case CommonConstants.EVALUATION_SCORE_BAD_1:
							afterBadEvaluateNum++;
							break;
						default:
							logger.info("未定义的中差评分值 score = {}", goodsReview.getScore());
							break;
						}
						break;
					}
				}
			}
		}
		totalGoodEvaluateNum = preGoodEvaluateNum + betGoodEvaluateNum + afterGoodEvaluateNum;
		totalNeutralEvaluateNum = preNeutralEvaluateNum + betNeutralEvaluateNum + afterNeutralEvaluateNum;
		totalBadEvaluateNum = preBadEvaluateNum + betBadEvaluateNum + afterBadEvaluateNum;
		CsOrderEvaluateDTO orderEvaluateDO = new CsOrderEvaluateDTO(totalGoodEvaluateNum, totalNeutralEvaluateNum,
				totalBadEvaluateNum, preGoodEvaluateNum, preNeutralEvaluateNum, preBadEvaluateNum, betGoodEvaluateNum,
				betNeutralEvaluateNum, betBadEvaluateNum, afterGoodEvaluateNum, afterNeutralEvaluateNum,
				afterBadEvaluateNum);
		orderEvaluateDO.setShopId(shopId);
		orderEvaluateDO.setDate(DateUtil.yyyyMMddFormat(date));
		orderEvaluateDO.setCsNick(csNick);
		return orderEvaluateDO;
	}

}
