package com.pes.jd.business.impl;

import com.pes.jd.business.ShopCategoryAndGoodsBussiness;
import com.pes.jd.data.converter.ShopCategoryAndGoodAndSkuDataConverter;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ShopCategoryAndGoodsImpl implements ShopCategoryAndGoodsBussiness{

	private static final Logger logger = LoggerFactory.getLogger(ShopCategoryAndGoodsImpl.class);

	@Resource
	private ShopCategoryAndGoodAndSkuDataConverter shopCategoryAndGoodAndSkuDataConverter;

	@Override
	public void pullShopCategory(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		try {
			shopCategoryAndGoodAndSkuDataConverter.persistShopCategory(jobShop, isDelData);
		} catch (Exception e) {
			logger.error("【{}】batch ShopCategory error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("pull ShopCategory end, time  ： {} s", (e - s)/1000);
		}
	}

	@Override
	public void pullShopCategoryV2(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		try {
			shopCategoryAndGoodAndSkuDataConverter.persistShopCategoryV2(jobShop, isDelData);
		} catch (Exception e) {
			logger.error("【{}】batch ShopCategoryV2 error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("pull ShopCategoryV2 end, time  ： {} s", (e - s)/1000);
		}
	}


	@Override
	public void pullShopGood(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		try {
			if(logger.isDebugEnabled()){
				logger.debug("拉取商品 start");
			}
            shopCategoryAndGoodAndSkuDataConverter.persistShopGoodOfEverydayByThread(jobShop, jobDate,isDelData);
//			shopCategoryAndGoodAndSkuDataConverter.persistALLShopGood(jobShop, jobDate,isDelData);
		} catch (Exception e) {
			logger.error("【{}】==>[{}]batch pullShopGood error", jobShop.getShop().getTitle(), jobShop.getShop().getShopId(), e);
			//五期优化——商品拉取刚上线不抛异常，防止影响主业务。
//			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("拉取商品 end 耗时{}s", (e - s)/1000);
			logger.debug("pull ShopGood end, time  ： {} s", (e - s)/1000);
		}
	}


	@Override
	public void pullShopSku(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		try {
//			shopCategoryAndGoodAndSkuDataConverter.persistShopGoodsSku(jobShop, isDelData);
			shopCategoryAndGoodAndSkuDataConverter.persistAllShopGoodsSkuByThread(jobShop, jobDate,isDelData);
		} catch (Exception e) {
			logger.error("【{}】batch pullShopGoodSku error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("pull ShopSku end, time  ： {} s", (e - s)/1000);
		}
	}

	@Override
	public void  pullShopPresaleSku(JobShopQuery jobShop) throws Exception {
		long s = System.currentTimeMillis();
		try {
			shopCategoryAndGoodAndSkuDataConverter.updateShopPresaleSku(jobShop);
		} catch (Exception e) {
			logger.error("【{}】batch pullShopPresaleSku error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("pull pullShopPresaleSku end, time  ： {} s", (e - s)/1000);
		}
	}

}
