package com.pes.jd.business.impl;


import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.CsTeamIntimePerformanceReqBusiness;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class CsTeamIntimePerformanceReqBusinessImpl implements CsTeamIntimePerformanceReqBusiness {

	@Autowired
	private PopRtSubRestTemplate popRtSubRestTemplate;
	
	@Override
	public ApiResponse searchCsTeamIntimePerformanceReq(ShopQuery shop, String date){
		//TODO
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", shopCommonParam)
				.put("date", date)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/pes/selectCsTeamIntimePerformance", body);

	}

}
