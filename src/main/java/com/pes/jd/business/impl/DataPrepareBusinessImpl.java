package com.pes.jd.business.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.pes.jd.Constants.JobPullRecordStatusConstants;
import com.pes.jd.business.*;
import com.pes.jd.config.PersistentRoutingDataSource;
import com.pes.jd.dao.JobRecordDao;
import com.pes.jd.exception.CsIsEmptyException;
import com.pes.jd.exception.TokenOverdueException;
import com.pes.jd.model.BO.CalTypeDateBO;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DO.JobCalRecordDO;
import com.pes.jd.model.DO.JobPullRecordDO;
import com.pes.jd.model.DTO.JobPullRecordDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.DataTypeEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.BeanUtils;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.yiyitech.support.datasource.RoutingDataSource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Date;
import java.util.List;

import static com.pes.jd.util.ServiceDateUtil.getNewJobDate;

/**
 * 绩效分类 - 业务类
 *
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
@Service
public class DataPrepareBusinessImpl implements DataPrepareBusiness {
    private static final Logger logger = LoggerFactory.getLogger(DataPrepareBusinessImpl.class);

    @Resource
    private CsChatHandleBussiness csChatHandleBussiness;

    @Resource
    private CsAssitIndexHandlerBussiness csAssitIndexHandlerBussiness;

    @Resource
    private CsPerformanceHandleBusiness csPerformanceHandleBusiness;

    @Resource
    private CsToOrderIndexHandlerBussiness csToOrderIndexHandlerBussiness;

    @Resource
    private ShopPerformanceHandleBusiness shopPerformanceHandleBusiness;

    @Resource
    private CsEvalHandleBussiness csEvalHandleBussiness;

    @Resource
    private OrderHandleBussiness orderHandleBussiness;

    @Resource
    private LoginlogBusiness loginlogBusiness;

    @Resource
    private ShopGoodsReviewBussiness shopGoodsReviewBussiness;

    @Resource
    private ShopCategoryAndGoodsBussiness shopCategoryAndGoodsBussiness;

    @Resource
    private GoodsHandleBusinessImpl goodsHandleBusiness;

    @Resource
    private OrderRefundHandleBussiness orderRefundHandleBussiness;

    @Resource
    private ChatHandleBusiness chatHandleBusiness;

    @Resource
    private ShopPresaleOrderBusiness shopPresaleOrderBusiness;

    @Resource
    private CsLeaveMsgBusiness csLeaveMsgBusiness;

    @Resource
    private GoodsSaleHandBussiness goodsSaleHandBussiness;

    @Resource
    private JobRecordDao jobRecordDao;

    @Resource
    private OrderGoodsEvaluateHandleBusiness orderGoodsEvaluateHandleBusiness;

    @Resource
    private ShopSubcribeHandBusiness shopSubcribeHandBusiness;

    @Resource
    private NoticeMessageBusiness noticeMessageBusiness;

    @Resource
    private ShopUserAnalysisHandBussiness shopUserAnalysisHandBussiness;

    @Resource
    private SentimentAnalysisBusiness sentimentAnalysisBusiness;

    @Resource
    private ReserveActivityBussiness reserveActivityBusiness;

    @Resource
    private PresaleAndPreordainPerformanceHandleBusiness presaleAndPreordainPerformanceHandleBusiness;

    @Resource
    private ShopGoodsRateHandBussiness shopGoodsRateHandBussiness;

    @Resource
    private CsTypeDayBusiness csTypeDayBusiness;

    @Resource
    private ChatClassfiyBusiness chatClassfiyBusiness;

    @Resource
    private UserPortraitBusiness userPortraitBusiness;

    @Resource
    private JdAddressBusiness jdAddressBusiness;

    @Resource
    private CsChatlogBusiness csChatlogBusiness;


    @Override
    public void pullShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData,boolean isYd) {
        long s = System.currentTimeMillis();
        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        Date modified = new Date();//操作时间
        JobPullRecordDTO existPullRecord = jobRecordDao.getJobPullRecordByShopIdAndDate(jobShop.getShop(), jobDate.getDate());
        JobPullRecordDO pullRecord;
        if (existPullRecord == null) {
            pullRecord = new JobPullRecordDO(shop.getShopId(), jobDate.getDate(), modified, JobPullRecordStatusConstants.UNEXECUTED);
            pullRecord.init();
            jobRecordDao.insertPullJobRecord(shop, pullRecord, jobDate.getDate());
        } else {
            pullRecord = dtoConversionDo(existPullRecord);
            pullRecord.setMsg("每日job预生成");
            pullRecord.setResult(Boolean.FALSE);
            pullRecord.setConsumeTime(0L);
        }
        jobShop.setPullRecord(pullRecord);

        if (logger.isDebugEnabled()) {
            logger.debug("【{}】-【{}】,{} - {} - {} - pull data start ", jobShop.getShopIndex(), DateUtils.formatYMd(jobDate.getDate()), shop.getTitle(), shop.getDb(), shop.getSchemaId());
        }
        try {
            //判断拉任务是否失效
            if (JobPullRecordStatusConstants.INVALID.equals(pullRecord.getRunStatus())) {
                if (logger.isDebugEnabled()) {
                    logger.debug("[{}] pull data fail, pull job is cancel", jobShop.getShop().getTitle());
                }
                pullRecord.setResult(Boolean.TRUE);
                pullRecord.setMsg("任务被取消");
                return;
            }
            //六十天前的数据不给拉
            int maxDay = 90;
            if (jobDate.getDate().before(DateFormatUtils.getDateByPeriod(DateFormatUtils.getStartTimeOfDate(new Date()), -maxDay))) {
                logger.info("拉取时间超过{}天，不支持重新拉取数据", maxDay);
                pullRecord.setResult(Boolean.TRUE);
                pullRecord.setMsg("拉取时间超过" + maxDay + "天，不支持重新拉取数据");
                return;
            }

            //变更pullRecord状态
            updatePullJobRecordStatus(jobShop.getShop(), jobDate.getDate(), JobPullRecordStatusConstants.EXECUTING);

            //判断商铺是否有设置客服集合
            if (CollectionUtils.isEmpty(jobShop.getCsLst())) {
                throw new CsIsEmptyException("没有设置客服,任务取消");
            }
            /*
             * 判断商铺是否token已过期或者不存在
             */
            long s1 = System.currentTimeMillis();
            shopSubcribeHandBusiness.pullShopSubcribeInfo(jobShop.getShop().getSessionKey());
            long e1 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f1-time={}ms - method[pullShopSubcribeInfo-api]", (e1 - s1));
            /*
             *  聊天会话，聊天对象 ，聊天记录 (in use) - 邵美
             */
            long s2 = System.currentTimeMillis();
            csChatHandleBussiness.pullChatPeersAndChatlogs(jobShop, jobDate, isDelData);
            long e2 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f2-time={}ms - method[pullChatPeersAndChatlogs-api]", (e2 - s2));
            pullRecord.setChatPeerFlag(true);

            /*
             *  客服评价更新 -(与客服评价逻辑相同，时间点不同，前七天)(in use) - 王凯
             */
            long s20 = System.currentTimeMillis();
            csEvalHandleBussiness.pullShopUpdateCsEvalDetails(jobShop, jobDate, isDelData);
            long e20 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f20-time={}ms - method[pullShopUpdateCsEvalDetails-api]", (e20 - s20));

            pullRecord.setUpdateCsEvalFlag(true);

            /*
             *  客服评价详情  (客服评价拉取+客服每日评价汇总)(in use)    - 王凯
             */
            long s21 = System.currentTimeMillis();
            csEvalHandleBussiness.pullShopCsEvalDetails(jobShop, jobDate, isDelData);
            long e21 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f21-time={}ms - method[pullShopCsEvalDetails-api]", (e21 - s21));


            /*8
              * 店铺用户群体画像
             */
            long s22 = System.currentTimeMillis();
            userPortraitBusiness.handleUserPortraitStatistics(jobShop, jobDate, isDelData);
            long e22 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f22-time={}ms - method[UserPortraitStatistics-api]", (e22 - s22));



            //京东地址更新
            long s23 = System.currentTimeMillis();
            jdAddressBusiness.handleJdAddress(jobShop,true);
            long e23 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f23-time={}ms - method[UserPortraitStatistics-api]", (e23 - s23));




            pullRecord.setCsEvalFlag(true);


//            /*
//             * 店铺类目 (in use) - 少鹏
//             */
//            long s3 = System.currentTimeMillis();
//            shopCategoryAndGoodsBussiness.pullShopCategory(jobShop, jobDate, isDelData);
//            long e3 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f3-time={}ms - method[pullShopCategory-api]", (e3 - s3));
//
//            pullRecord.setShopCategoryFlag(true);
//
//            /*
//             * 店铺商品SKU (in use) - 少鹏
//             */
//            long s4 = System.currentTimeMillis();
//            shopCategoryAndGoodsBussiness.pullShopSku(jobShop, jobDate, isDelData);
//            long e4 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f4-time={}ms - method[pullShopSku-api]", (e4 - s4));
//
//            pullRecord.setShopSkuFlag(true);
//
//            /*
//             * 保存店铺类目版2 (in use)
//             */
//
//            long s6 = System.currentTimeMillis();
//            shopCategoryAndGoodsBussiness.pullShopCategoryV2(jobShop, jobDate, isDelData);
//            long e6 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f3-time={}ms - method[pullShopCategoryV2-api]", (e6 - s6));
//            pullRecord.setShopCategoryV2Flag(true);
//            System.out.println("店铺类目V2拉取成功");




//            //预售活动拉取
//            long s23 = System.currentTimeMillis();
//            shopCategoryAndGoodsBussiness.pullShopPresaleSku(jobShop);
//            long e23 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f4.1-time={}ms - method[pullShopPresaleSku-api]", (e23 - s23));

//            //预约活动拉取
//            long s24 = System.currentTimeMillis();
//            reserveActivityBusiness.pullReserveActivity(jobShop, isDelData);
//            long e24 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f4.2-time={}ms - method[pullReserveActivity-api]", (e24 - s24));

//            /*
//             * 店铺商品 (in use) - 少鹏
//             */
//            long s5 = System.currentTimeMillis();
//            shopCategoryAndGoodsBussiness.pullShopGood(jobShop, jobDate, isDelData);
//            long e5 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f5-time={}ms - method[pullShopGood-api]", (e5 - s5));

        //    pullRecord.setShopGoodFlag(true);

            ///*
            // *  店铺DSR信息 (in use) - 少鹏
            // */
            //try {
            //    long s6 = System.currentTimeMillis();
            //    shopDsrHandleBussiness.pullShopDsr(jobShop, jobDate, isDelData);
            //    long e6 = System.currentTimeMillis();
            //    logger.info("pop-job-invoke-api-"+shopId+"-f6-time={}ms - method[pullShopDsr-api]", (e6 - s6));
            //    pullRecord.setShopDsrFlag(true);
            //}catch (Exception e){
            //    pullRecord.setShopDsrFlag(false);
            //    logger.error("【{}】-【{}】,{} - pull shop dsr data error ", jobShop.getShopIndex(), DateUtils.formatYMd(jobDate.getDate()), shop.getTitle(), e);
            //}

//            /*
//             * 售后退款-按申请补充数据-拉取 (in use) - 少鹏
//             */
//            long s17 = System.currentTimeMillis();
//            orderRefundHandleBussiness.pullAscServiceOrderRefundDataByOrderId(jobShop, jobDate, isDelData);
//            long e17 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f17-time={}ms - method[pullAscServiceOrderRefundDataByOrderId-api]", (e17 - s17));

//            /*TODO 必须在普通订单前获取
//             * 预售订单拉取(按修改时间拉取，下单时间保存)(in use) - 王凯 - 暂时没有预售商品数据，只进行了造数据测试
//             */
//            long s11 = System.currentTimeMillis();
//            shopPresaleOrderBusiness.pullPresaleOrderByDate(jobShop, jobDate, isDelData);
//            long e11 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f11-time={}ms - method[pullPresaleOrderByDate-api]", (e11 - s11));
//            pullRecord.setOrderPresaleFlag(true);
//
//            //指定店铺和数据同步的时间
//            List<JobDateQuery> jobDateList = getNewJobDate(jobDate);

//            if(jobShop.getShop().getColType() == 100){
//                long orderStartTime = System.currentTimeMillis();
//
//                logger.info("{} 从云鼎获取数据开始",jobShop.getShop().getTitle());
//                //创建时间维度拉取   -3
//                long s7 = System.currentTimeMillis();
//                orderHandleBussiness.pushShopOrderFromJd(jobShop, jobDate, 0, isDelData);
//                long e7 = System.currentTimeMillis();
//                logger.info("pop-job-invoke-jd-yunding-" + shopId + "-f7-time={}ms - method[pushShopOrderFromJd-created-api]", (e7 - s7));
//
//                //修改时间维度拉取  -4
//                long s8 = System.currentTimeMillis();
//                orderHandleBussiness.pushShopOrderFromJd(jobShop, jobDate, 1, isDelData);
//                long e8 = System.currentTimeMillis();
//                logger.info("pop-job-invoke-jd-yunding-" + shopId + "-f8-time={}ms - method[pushShopOrderFromJd-modifity-api]", (e8 - s8));
//                logger.info("{} 从云鼎获取订单数据结束！总耗时 {} ms",jobShop.getShop().getTitle(),(e8 - s7));
//                for (JobDateQuery newJobDate : jobDateList) {
//                    //TODO 订单拉取顺序不能改变 1-2-3-4
//                    /*
//                     *  未付款的订单维度 (in use) - 少鹏  -1
//                     */
//                    long start1 = System.currentTimeMillis();
//                    logger.info("{} 从接口获取订单数据开始", jobShop.getShop().getTitle());
//                    orderHandleBussiness.pullShopNoPayOrderInfo(jobShop, newJobDate, isDelData);
//                    long end1 = System.currentTimeMillis();
//                    logger.info("pop-job-invoke-api-" + shopId + "-f7-time={}ms - method[pullShopNoPayOrderInfo-api]", (end1 - start1));
//                    pullRecord.setNoPayOrderFlag(true);
//
//                    /*
//                     *  消息队列中取消消息插入 (in use) - 少鹏 -2
//                     */
//                    long start2 = System.currentTimeMillis();
//                    orderHandleBussiness.updateOrderAndPresaleOrder(jobShop, newJobDate, isDelData, false);
//                    long end2 = System.currentTimeMillis();
//                    logger.info("pop-job-invoke-api-" + shopId + "-f10-time={}ms - method[updateOrderAndPresaleOrder-api]", (end2 - start2));
//
//                    /*
//                     *  创建的订单，下单维度 (in use) - 少鹏  -3
//                     */
//                    long start3 = System.currentTimeMillis();
//                    orderHandleBussiness.pullShopOrderInfo(jobShop, newJobDate, isDelData);
//                    long end3 = System.currentTimeMillis();
//                    logger.info("pop-job-invoke-api-" + shopId + "-f8-time={}ms - method[pullShopOrderInfo-api]", (end3 - start3));
//                    pullRecord.setOrderCreatedFlag(true);
//
//                    /*
//                     *  增量的订单，修改时间维度 (in use) - 少鹏 -4
//                     */
//                    long s9 = System.currentTimeMillis();
//                    orderHandleBussiness.pullShopIncrementOrder(jobShop, newJobDate, isDelData);
//                    long e9 = System.currentTimeMillis();
//                    logger.info("pop-job-invoke-api-" + shopId + "-f9-time={}ms - method[pullShopIncrementOrder-api]", (e9 - s9));
//                    pullRecord.setOrderModifyFlag(true);
//                    logger.info("{} 从接口获取订单数据结束！总耗时 {} ms",jobShop.getShop().getTitle(),(e9 - s7));
//                }
//                logger.info("shopId :{}, fcs店铺拉取三天订单总耗时 :{}ms", jobShop.getShop().getShopId(), System.currentTimeMillis() - orderStartTime);
//            }else{
//                if (!isYd) {
//                    long orderStartTime = System.currentTimeMillis();
//                    for (JobDateQuery newJobDate : jobDateList) {
//                        //TODO 订单拉取顺序不能改变 1-2-3-4
//                        /*
//                         *  未付款的订单维度 (in use) - 少鹏  -1
//                         */
//                        long s7 = System.currentTimeMillis();
//                        logger.info("{} 从接口获取订单数据开始", jobShop.getShop().getTitle());
//                        orderHandleBussiness.pullShopNoPayOrderInfo(jobShop, newJobDate, isDelData);
//                        long e7 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-api-" + shopId + "-f7-time={}ms - method[pullShopNoPayOrderInfo-api]", (e7 - s7));
//                        pullRecord.setNoPayOrderFlag(true);
//
//                        /*
//                         *  消息队列中取消消息插入 (in use) - 少鹏 -2
//                         */
//                        long s10 = System.currentTimeMillis();
//                        orderHandleBussiness.updateOrderAndPresaleOrder(jobShop, newJobDate, isDelData, false);
//                        long e10 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-api-" + shopId + "-f10-time={}ms - method[updateOrderAndPresaleOrder-api]", (e10 - s10));
//
//                        /*
//                         *  创建的订单，下单维度 (in use) - 少鹏  -3
//                         */
//                        long s8 = System.currentTimeMillis();
//                        orderHandleBussiness.pullShopOrderInfo(jobShop, newJobDate, isDelData);
//                        long e8 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-api-" + shopId + "-f8-time={}ms - method[pullShopOrderInfo-api]", (e8 - s8));
//                        pullRecord.setOrderCreatedFlag(true);
//
//                        /*
//                         *  增量的订单，修改时间维度 (in use) - 少鹏 -4
//                         */
//                        long s9 = System.currentTimeMillis();
//                        orderHandleBussiness.pullShopIncrementOrder(jobShop, newJobDate, isDelData);
//                        long e9 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-api-" + shopId + "-f9-time={}ms - method[pullShopIncrementOrder-api]", (e9 - s9));
//                        pullRecord.setOrderModifyFlag(true);
//                        logger.info("{} 从接口获取订单数据结束！总耗时 {} ms",jobShop.getShop().getTitle(),(e9 - s7));
//                    }
//                    logger.info("shopId :{}, 从接口拉取三天订单总耗时 :{}ms", jobShop.getShop().getShopId(), System.currentTimeMillis() - orderStartTime);
//                } else {
//                    long orderStartTime = System.currentTimeMillis();
////                    for (JobDateQuery newJobDate : jobDateList) {
//                        logger.info("{} 从云鼎获取数据开始",jobShop.getShop().getTitle());
//
//                        /*
//                         *  消息队列中取消消息插入 (in use) - 少鹏 -2
//                         */
//                        long s10 = System.currentTimeMillis();
//                        orderHandleBussiness.updateOrderAndPresaleOrder(jobShop, jobDate, isDelData, true);
//                        long e10 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-api-" + shopId + "-f10-time={}ms - method[updateOrderAndPresaleOrder-api]", (e10 - s10));
//
//                        //创建时间维度拉取   -3
//                        long s7 = System.currentTimeMillis();
//                        orderHandleBussiness.pushShopOrderFromJd(jobShop, jobDate, 0, isDelData);
//                        long e7 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-jd-yunding-" + shopId + "-f7-time={}ms - method[pushShopOrderFromJd-created-api]", (e7 - s7));
//
//                        //修改时间维度拉取  -4
//                        long s8 = System.currentTimeMillis();
//                        orderHandleBussiness.pushShopOrderFromJd(jobShop, jobDate, 1, isDelData);
//                        long e8 = System.currentTimeMillis();
//                        logger.info("pop-job-invoke-jd-yunding-" + shopId + "-f8-time={}ms - method[pushShopOrderFromJd-modifity-api]", (e8 - s8));
//                        logger.info("{} 从云鼎获取订单数据结束！总耗时 {} ms",jobShop.getShop().getTitle(),(e8 - s7));
////                    }
//                    logger.info("shopId :{}, 从云鼎拉取三天订单总耗时 :{}ms", jobShop.getShop().getShopId(), System.currentTimeMillis() - orderStartTime);
//                }
//            }

//            long refundAndLeaveMessageStartTime = System.currentTimeMillis();
//
//            /*
//             * 退款-按申请维度-拉取 (in use) - 少鹏
//             */
//            long s13 = System.currentTimeMillis();
//            orderRefundHandleBussiness.pullOrderRefundApplyData(jobShop, jobDate, isDelData);
//            long e13 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f13-time={}ms - method[pullOrderRefundApplyData-api]", (e13 - s13));
//
//            pullRecord.setOrderRefundApplyFlag(true);
//
//            /*
//             * 退款-按审核维度-拉取 (in use) - 少鹏
//             */
//            long s14 = System.currentTimeMillis();
//            orderRefundHandleBussiness.pullOrderRefundCheckData(jobShop, jobDate, isDelData);
//            long e14 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f14-time={}ms - method[pullOrderRefundCheckData-api]", (e14 - s14));
//            pullRecord.setOrderRefundCheckFlag(true);
//
//            /*
//             * 售后退款-按申请维度-拉取 (in use) - 少鹏
//             */
//            long s15 = System.currentTimeMillis();
//            orderRefundHandleBussiness.pullAscServiceOrderRefundApplyData(jobShop, jobDate, isDelData);
//            long e15 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f15-time={}ms - method[pullAscServiceOrderRefundApplyData-api]", (e15 - s15));
//            pullRecord.setAscOrderRefundApplyFlag(true);
//
//            /*
//             * 售后退款-按审核维度-拉取 (in use) - 少鹏
//             */
//            long s16 = System.currentTimeMillis();
//            orderRefundHandleBussiness.pullAscServiceOrderRefundCheckData(jobShop, jobDate, isDelData);
//            long e16 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f16-time={}ms - method[pullAscServiceOrderRefundCheckData-api]", (e16 - s16));
//
//            pullRecord.setAscOrderRefundCheckFlag(true);
//
//            /*
//             * 订单的中差评基础数据获取 (in use)  - 王凯
//             */
//            long s12 = System.currentTimeMillis();
//            shopGoodsReviewBussiness.pullShopGoodsReview(jobShop, jobDate, isDelData);
//            long e12 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f12-time={}ms - method[pullShopGoodsReview-api]", (e12 - s12));

//            pullRecord.setOrderEvaluationFlag(true);
//                /*
//                 * 留言拉取：咨询留言，分配留言(in use)  - 王凯
//                 */
//            for (JobDateQuery newJobDate : jobDateList) {
//                long s18 = System.currentTimeMillis();
//                if(!(newJobDate.getDate().getTime() == jobDate.getDate().getTime())){
//                    csTypeDayBusiness.handleCsTypeDay(jobShop, newJobDate, isDelData);
//                }
//                csLeaveMsgBusiness.pullCsLeaveMsgInfo(jobShop, newJobDate, isDelData);
//                long e18 = System.currentTimeMillis();
//                logger.info("pop-job-invoke-api-" + shopId + "-f18-time={}ms - method[pullCsLeaveMsgInfo-api]", (e18 - s18));
//            }
//
//            pullRecord.setLeaveMsgFlag(true);
//            logger.info("shopId :{}, 退款和留言拉取三天的数据总耗时 :{}ms", jobShop.getShop().getShopId(), System.currentTimeMillis() - refundAndLeaveMessageStartTime);

//
//            /*
//             * 邀评拉取  - (放到客服评价接口之前：统计邀评量)(in use)    - 王凯
//             */
//            long s19 = System.currentTimeMillis();
//            csEvalHandleBussiness.pullShopCsSendEval(jobShop, jobDate, isDelData);
//            long e19 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f19-time={}ms - method[pullShopCsSendEval-api]", (e19 - s19));
//
//            pullRecord.setCsSendEvalFlag(true);


//            /*
//             *  订单备注拉取  (含订单插旗)(in use)    - 王凯
//             */
//            long s22 = System.currentTimeMillis();
//            orderHandleBussiness.pullShopOrderRemark(jobShop, jobDate, isDelData);
//            long e22 = System.currentTimeMillis();
//            logger.info("pop-job-invoke-api-" + shopId + "-f22-time={}ms - method[pullShopOrderRemark-api]", (e22 - s22));
//
//            pullRecord.setOrderRemarkFlag(true);

            /*
             * 拉取运营经理管理的店铺 -lhc
             */
//            shopPopHandBusiness.pullShopPop(jobShop, jobDate, isDelData);
//            pullRecord.setPopShopFlag(true);


//            try {
//                long s26 = System.currentTimeMillis();
//                shopGoodsRateHandBussiness.pullShopGoodsRate(jobShop, jobDate, isDelData);
//                long e26 = System.currentTimeMillis();
//                logger.info("pop-job-invoke-api-" + shopId + "-f26-time={}ms - method[pullShopGoodsRate-api]", (e26 - s26));
//            } catch (Exception e) {
//                logger.error("pop-job-invoke-api-" + shopId + "-f26-time={}ms - method[pullShopGoodsRate-api]", e.getMessage(), e);
//            }
            pullRecord.setResult(Boolean.TRUE);//拉取成功
            pullRecord.setRunStatus(JobPullRecordStatusConstants.SUCCESS);

        } catch (TokenOverdueException e) {
            logger.info("shop {} job fail, token is overdue", jobShop.getShop().getTitle());
            try {
                noticeMessageBusiness.sendUpdateShopAuthStatusSuccessMessage(jobShop.getShop(), "authExpiredFlag");
            } catch (Exception ignore) {
                logger.error("authExpiredFlag msg send fail");
            }
            pullRecord.setMsg("token已过期");
            pullRecord.setResult(Boolean.FALSE);
            pullRecord.setRunStatus(JobPullRecordStatusConstants.INVALID);
//            updatePullJobRecordStatus(shop, jobDate.getDate(), JobPullRecordStatusConstants.INVALID);
        } catch (CsIsEmptyException e) {

            logger.info("[{}] pull data fail, csLst is empty", jobShop.getShop().getTitle());
            pullRecord.setMsg(e.getErrMsg());
            pullRecord.setResult(Boolean.FALSE);
            pullRecord.setRunStatus(JobPullRecordStatusConstants.INVALID);
//            updatePullJobRecordStatus(jobShop.getShop(), jobDate.getDate(), JobPullRecordStatusConstants.INVALID);
        } catch (Exception e) {
            if (StringUtils.isNotBlank(e.getMessage())) {
                try {
                    pullRecord.setMsg(e.getMessage().substring(0, Math.min(e.getMessage().length(), 125)));
                } catch (Exception ignored) {
                }
            }
            pullRecord.setResult(Boolean.FALSE);
            pullRecord.setRunStatus(JobPullRecordStatusConstants.FAIL);
//            updatePullJobRecordStatus(jobShop.getShop(), jobDate.getDate(), JobPullRecordStatusConstants.FAIL);
            logger.error("{},{} pull data fail error:{}", shop.getTitle(), shop.getDb(), e.getMessage(), e);
        } finally {
            pullRecord.setConsumeTime(System.currentTimeMillis() - s);
            updatePullJobRecord(pullRecord, jobShop.getShop(), jobDate.getDate());
        }
        long e = System.currentTimeMillis();
        logger.info("pop-job-invoke-api-" + shopId + "-f23-time={}ms - method[pullTotal-api]", (e - s));
        if (logger.isDebugEnabled()) {

            logger.debug("【{}】-【{}】,{} - {} - {} - pull data success,time{}s ", jobShop.getShopIndex(), DateUtils.formatYMd(jobDate.getDate()), shop.getTitle(), shop.getDb(), shop.getSchemaId(), (e - s) / 1000);
        }
    }


    /***
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     * @throws Exception
     */
    private void pullshopNoPayOrder(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        long s7 = System.currentTimeMillis();
        orderHandleBussiness.pullShopNoPayOrderInfo(jobShop, jobDate, isDelData);
        long e7 = System.currentTimeMillis();
        logger.info("pop-job-invoke-api-" + jobShop.getShop().getShopId() + "-f7-time={}ms - method[pullShopNoPayOrderInfo-api]", (e7 - s7));

    }


    @Override
    public void calShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {

        JobShopDTO shop = jobShop.getShop();
        Long shopId = jobShop.getShop().getShopId();
        if (logger.isDebugEnabled()) {

            logger.debug("【{}】-【{}】,{} - {} - {} - cal data start ", jobShop.getShopIndex(), DateUtils.formatYMd(jobDate.getDate()), shop.getTitle(), shop.getDb(), shop.getSchemaId());
        }
        Date modified = new Date();//操作时间
        JobCalRecordDO calRecord = new JobCalRecordDO(jobShop.getShop().getShopId(), jobDate.getDate(), modified);
        calRecord.setResult(Boolean.FALSE);
        jobShop.setCalRecord(calRecord);
        long s = System.currentTimeMillis();

        try {

            try {
                long s51 = System.currentTimeMillis();
                chatClassfiyBusiness.handleClassfiyAnalysis(jobShop, jobDate, isDelData);
                long e52 = System.currentTimeMillis();
                logger.info("pop-job-cal-api-" + shopId + "-f51-time={}ms - method[handleClassfiyAnalysis-api]", (e52 - s51));
            } catch (Exception e) {
                logger.error("sentiment analysis data error {}: ", e.getMessage(), e);
            }

            try {
                long s53 = System.currentTimeMillis();
                csChatlogBusiness.findSidOfCsServiceEvaluation(jobShop,jobDate);
                long e53 = System.currentTimeMillis();
                logger.info("pop-job-cal-api-" + shopId + "-f53-time={}ms - method[findSidOfCsServiceEvaluation-api]", (e53 - s53));
            } catch (Exception e) {
                logger.error("评价拉取sid {}: ", e.getMessage(), e);
            }




//            //计算出差评的会话id (根据最后一次的客户与客服的交流id来得到)
//
//            csChatlogBusiness.findSidOfCsServiceEvaluation();



//            /*
//             * 客服服务指标，绩效计算
//             */
//
//            calShopPesData(jobShop, jobDate, isDelData);
//
//
//            /*
//             *  订单过滤,询单流失处理,订单中差评计算,客服落实数据的报表
//             */
//            calShopCommonData(jobShop, jobDate, isDelData);
//
//            /*
//             * 商品数据，推荐数据计算
//             */
//            calShopGoodsAndSkuRelevantData(jobShop, jobDate, isDelData);
//
//            //退款数据计算
//            //客服维度
//            long s45 = System.currentTimeMillis();
//            orderRefundHandleBussiness.handleShopCsTeamDailyRefund(jobShop, jobDate, isDelData);
//            long e45 = System.currentTimeMillis();
//            logger.info("pop-job-cal-api-" + shopId + "-f45-time={}ms - method[handleShopCsTeamDailyRefund-api]", (e45 - s45));
//
//
//            //店铺维度
//            long s46 = System.currentTimeMillis();
//            orderRefundHandleBussiness.handleShopDailyRefund(jobShop, jobDate, isDelData);
//            long e46 = System.currentTimeMillis();
//            logger.info("pop-job-cal-api-" + shopId + "-f46-time={}ms - method[handleShopDailyRefund-api]", (e46 - s46));
//
//            //更新店铺使用分析表的店铺销售额&所选时间订购有效天数
//            long s47 = System.currentTimeMillis();
//            shopUserAnalysisHandBussiness.updateShopUseAnalysisShopSale(jobShop, jobDate);
//            long e47 = System.currentTimeMillis();
//            logger.info("pop-job-cal-api-" + shopId + "-f47-time={}ms - method[updateShopUseAnalysisShopSale-api]", (e47 - s47));
//
//            //生成情感分析原始数据
//            try {
//                long s48 = System.currentTimeMillis();
//                sentimentAnalysisBusiness.handleSentimentAnalysis(jobShop, jobDate, isDelData);
//                long e48 = System.currentTimeMillis();
//                logger.info("pop-job-cal-api-" + shopId + "-f48-time={}ms - method[handleSentimentAnalysis-api]", (e48 - s48));
//            } catch (Exception e) {
//                logger.error("sentiment analysis data error {}: ", e.getMessage(), e);
//            }
//
//            long s49 = System.currentTimeMillis();
//            presaleAndPreordainPerformanceHandleBusiness.handlePerformanceForPreordain(jobShop, jobDate, isDelData);
//            long e49 = System.currentTimeMillis();
//            logger.info("pop-job-cal-api-" + shopId + "-f49-time={}ms - method[handlePerformanceForPreordain-api]", (e49 - s49));
//            presaleAndPreordainPerformanceHandleBusiness.handlePerformanceForPresale(jobShop, jobDate, isDelData);
//            long s50 = System.currentTimeMillis();
//            logger.info("pop-job-cal-api-" + shopId + "-f50-time={}ms - method[handlePerformanceForPresale-api]", (s50 - e49));

            //JOB计算成功
            calRecord.setResult(Boolean.TRUE);

        } catch (Exception e) {
            if (StringUtils.isNotBlank(e.getMessage())) {
                try {
                    calRecord.setMsg(e.getMessage().substring(0, Math.min(e.getMessage().length(), 200)));
                } catch (Exception ignored) {
                }
            }
            if (logger.isDebugEnabled()) {

                logger.debug("【{}】-【{}】,{} - {} - {} - cal data fail ", jobShop.getShopIndex(), DateUtils.formatYMd(jobDate.getDate()), shop.getTitle(), shop.getDb(), shop.getSchemaId());
            }
            throw e;
        } finally {
            long ee = System.currentTimeMillis();
            calRecord.setConsumeTime(ee - s);
            handleJobCalRecord(calRecord, jobShop.getShop(), jobDate.getDate());
        }
        long e = System.currentTimeMillis();

        logger.info("pop-job-cal-api-" + shopId + "-f49-time={}ms - method[totalCal-api]", (e - s));
        if (logger.isDebugEnabled()) {

            logger.debug("【{}】-【{}】,{} - {} - {} - cal data success,time:{}s ", jobShop.getShopIndex(), DateUtils.formatYMd(jobDate.getDate()), shop.getTitle(), shop.getDb(), shop.getSchemaId(), (e - s) / 1000);
        }
    }


    private void calShopCommonData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {
        Long shopId = jobShop.getShop().getShopId();
        JobCalRecordDO calRecord = jobShop.getCalRecord();
        PerformanceRuleValidDateBO validDate = jobDate.getValidDate();

        /*
         *  订单过滤 (in use) - 少鹏
         */
       /* long s35 = System.currentTimeMillis();
        orderHandleBussiness.handleShopOrderFilter(jobShop, jobDate, isDelData);
        long e35 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-"+shopId+"-f35-time={}ms - method[handleShopOrderFilter-api]", (e35 - s35));
        calRecord.setOrderFilteFlag(Boolean.TRUE);*/

        /*
         * 下单未付款分析(in use)  - 王凯
         * 【19号跑18号的数据，算17号的最终流失记录和18号临时流失记录】
         */
//        if(jobDate.getNeedCalFinalData()){
//            jobDate.getOrderLossDates().add(validDate.getToOrderedThenPayValidDate());//最终流失数据
//        }
//        jobDate.getOrderLossDates().add(validDate.getDate());
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getOrderLossDates().addAll(jobDate.getDates());
        } else {
            jobDate.getOrderLossDates().add(validDate.getDate());
        }
        long s36 = System.currentTimeMillis();
        chatHandleBusiness.handleOrderLoss(jobShop, jobDate, isDelData);
        long e36 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f36-time={}ms - method[handleOrderLoss-api]", (e36 - s36));
        calRecord.setOrderLossFlag(Boolean.TRUE);

        /*
         * 出库流失处理(出库流失)(in use)  - 王凯
         */
//        if(jobDate.getNeedCalFinalData()){
//            jobDate.getOutStockLossDates().add(validDate.getEnquiry2OutstackValidDate());
//        }
//        jobDate.getOutStockLossDates().add(validDate.getDate());
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getOutStockLossDates().addAll(jobDate.getDates());
        } else {
            jobDate.getOutStockLossDates().add(validDate.getDate());
        }
        long s37 = System.currentTimeMillis();
        chatHandleBusiness.handleOrderOutStockLoss(jobShop, jobDate, isDelData);
        long e37 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f37-time={}ms - method[handleOrderOutStockLoss-api]", (e37 - s37));
        calRecord.setOutstockLossFlag(Boolean.TRUE);

        //店铺（买家去重）&&客服每日流失统计
        long s38 = System.currentTimeMillis();
        chatHandleBusiness.calculateShopCsLossData(jobShop, jobDate, isDelData);
        long e38 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f38-time={}ms - method[calculateShopCsLossData-api]", (e38 - s38));
        calRecord.setTeamLossFlag(Boolean.TRUE);

        /*
         * 客服绑定订单中差评计算  (in use) - 王凯
         */
        long s39 = System.currentTimeMillis();
        shopGoodsReviewBussiness.handleShopCsOrderReview(jobShop, jobDate, isDelData);
        long e39 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f39-time={}ms - method[handleShopCsOrderReview-api]", (e39 - s39));
        calRecord.setCsOrderEvalFlag(Boolean.TRUE);

        /**
         *  客服落实，同时也计算店铺落实  (in use) - 晴风
         */
        long s40 = System.currentTimeMillis();
        csToOrderIndexHandlerBussiness.handleShopCsOrderBindIndex(jobShop, jobDate, isDelData);
        long e40 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f40-time={}ms - method[handleShopCsOrderBindIndex-api]", (e40 - s40));
        calRecord.setCsOrderBindIndexFlag(Boolean.TRUE);

    }

    /**
     * 客服服务指标，绩效计算
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     * @throws Exception
     */
    private void calShopPesData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {
        JobCalRecordDO calRecord = jobShop.getCalRecord();
        PerformanceRuleValidDateBO validDate = jobDate.getValidDate();
        Long shopId = jobShop.getShop().getShopId();


        long s35 = System.currentTimeMillis();
        orderHandleBussiness.handleShopOrderFilter(jobShop, jobDate, isDelData);
        long e35 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f24.0-time={}ms - method[handleShopOrderFilter-api]", (e35 - s35));
        calRecord.setOrderFilteFlag(Boolean.TRUE);

        /*
         * 聊天处理(接待处理，接待过滤，会话处理，会话指标，接待指标)(in use) - deru_zi
         * 【今日19号-算昨日 - 18号数据】
         */

        long s24 = System.currentTimeMillis();
        chatHandleBusiness.handleCommonChat(jobShop, jobDate, isDelData);
        long e24 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f24-time={}ms - method[handleCommonChat-api]", (e24 - s24));
        calRecord.setCommonChatFlag(Boolean.TRUE);

        //计算邀评量 张爱俊 2019/04/28
        JobShopDTO shop = jobShop.getShop();
        String schemaId = shop.getSchemaId();
        long s25 = System.currentTimeMillis();
        orderGoodsEvaluateHandleBusiness.handleCalInviteEvaluation(jobShop, jobDate, schemaId, isDelData);
        long e25 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f25-time={}ms - method[handleCalInviteEvaluation-api]", (e25 - s25));

        /*
         *  客服绩效指标 (in use) - deru_zi
         *  维护的维度如下：
         *  【今日19号-算昨日：计算18号数据，询单-下单：计算15号数据，询单-付款：计算14号数据】
         */
//        if (jobDate.getNeedCalFinalData()) {
//            jobDate.getCsOrderIndexDates().add(validDate.getEnquiry2PayValidDate());
//            jobDate.getCsOrderIndexDates().add(validDate.getEnquiry2OrderedValidDate());
//        }
//            jobDate.getCsOrderIndexDates().add(validDate.getDate());
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getCsOrderIndexDates().addAll(jobDate.getDates());
        } else {
            jobDate.getCsOrderIndexDates().add(validDate.getDate());
        }
        long s26 = System.currentTimeMillis();
        csPerformanceHandleBusiness.handleShopCsOrderIndex(jobShop, jobDate, isDelData);
        long e26 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f26-time={}ms - method[handleShopCsOrderIndex-api]", (e26 - s26));

        calRecord.setCsOrderIndexFlag(Boolean.TRUE);


        /*
         *  客服绩效绑定 (in use) - deru_zi
         *  维护的维度如下：
         *   【今日19号-算昨日：计算18号数据，询单-下单：计算15号数据，询单-付款：计算14号数据】
         */
//        if(jobDate.getNeedCalFinalData()){
//            jobDate.getCsOrderBindDates().add(validDate.getEnquiry2PayValidDate());
//            jobDate.getCsOrderBindDates().add(validDate.getEnquiry2OrderedValidDate());
//        }
//        jobDate.getCsOrderBindDates().add(validDate.getDate());
        jobDate.getCsOrderBindDates().addAll(jobDate.getDates());
        long s27 = System.currentTimeMillis();
        csPerformanceHandleBusiness.handleShopCsOrderBind(jobShop, jobDate, isDelData);
        long e27 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f27-time={}ms - method[handleShopCsOrderBind-api]", (e27 - s27));
        calRecord.setCsOrderBindFlag(Boolean.TRUE);


        /*
         * 询单处理 (in use) - deru_zi
         * 【今日19号-只算昨日 - 18号数据】
         */

//        if(jobDate.getNeedCalFinalData()){
//            jobDate.getCsEnquiryDates().add(validDate.getEnquiry2PayValidDate());
//            jobDate.getCsEnquiryDates().add(validDate.getEnquiry2OrderedValidDate());
//        }
//        jobDate.getCsEnquiryDates().add(validDate.getDate());
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getCsEnquiryDates().addAll(jobDate.getDates());
        } else {
            jobDate.getCsEnquiryDates().add(validDate.getDate());
        }
        long s28 = System.currentTimeMillis();
        chatHandleBusiness.handleEnquiryChat(jobShop, jobDate, isDelData);
        long e28 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f28-time={}ms - method[handleEnquiryChat-api]", (e28 - s28));
        calRecord.setEnquiryChatFlag(Boolean.TRUE);

        /*
         * 最终数据(聊天对象，最终下单，聊天失败)处理 (in use) - deru_zi
         * 【今日19号-只算昨日 - 18号数据】
         */

        if (validDate.getBrforeAndEqualsEnquiryLimitDate()) {
            jobDate.getCsFinalDataDates().add(validDate.getDate());
        } else {
            jobDate.getCsFinalDataDates().add(validDate.getEnquiry2OrderedValidDate());
        }
//        chatHandleBusiness.handleFinalData(jobShop, jobDate);

        /*
         * 询单流失处理(询单流失分析)(in use)  - 王凯
         */
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getCsEnquiryLossForEnquiryDates().addAll(jobDate.getDates());
        } else {
            jobDate.getCsEnquiryLossForEnquiryDates().add(validDate.getDate());
        }
        long s29 = System.currentTimeMillis();
        chatHandleBusiness.handleEnquiryLoss(jobShop, jobDate, isDelData);
        long e29 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f29-time={}ms - method[handleEnquiryLoss-api]", (e29 - s29));
        calRecord.setEnquiryLossFlag(Boolean.TRUE);

        /*
         *  客服绩效计算(询单维度)  (in use) - deru_zi
         *  【今日19号-算昨日：计算18号数据，询单-下单：计算15号数据，询单-付款：计算14号数据，询单-出库：计算10号数据】
         */
//        if (jobDate.getNeedCalFinalData()) {
//            jobDate.getCsPerformanceDates().add(validDate.getEnquiry2PayValidDate());
//            jobDate.getCsPerformanceDates().add(validDate.getEnquiry2OrderedValidDate());
//        }
//            jobDate.getCsPerformanceDates().add(validDate.getDate());
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getCsPerformanceDates().addAll(jobDate.getDates());
        } else {
            jobDate.getCsPerformanceDates().add(validDate.getDate());
        }
        long s30 = System.currentTimeMillis();
        csPerformanceHandleBusiness.handleShopCsPerformance(jobShop, jobDate, isDelData);
        long e30 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f30-time={}ms - method[handleShopCsPerformance-api]", (e30 - s30));
        calRecord.setCsPerformanceFlag(Boolean.TRUE);

        /*
         *  客服绩效计算(促成下单维度)  (in use) - deru_zi
         *  【今日19号-算昨日：计算18号数据，下单-付款：计算17号数据，下单-出库：计算14号数据】
         */
        if (jobDate.getNeedCalFinalData()) {
            jobDate.getCsPerformanceForToOrderCalTypeDateSet().add(new CalTypeDateBO(validDate.getToOrderedThenOutstackValidDate(), DataTypeEnum.TYPE_OUT_STACK));
            jobDate.getCsPerformanceForToOrderCalTypeDateSet().add(new CalTypeDateBO(validDate.getToOrderedThenPayValidDate(), DataTypeEnum.TYPE_PAID));
        }
        jobDate.getCsPerformanceForToOrderCalTypeDateSet().add(new CalTypeDateBO(validDate.getDate(), DataTypeEnum.TYPE_ORDERED));
        long s31 = System.currentTimeMillis();
        csPerformanceHandleBusiness.handleShopCsPerformanceForToOrder(jobShop, jobDate, isDelData);
        long e31 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f31-time={}ms - method[handleShopCsPerformanceForToOrder-api]", (e31 - s31));
        calRecord.setCsTorderPerformance(Boolean.TRUE);


        /*
         *  客服绩效计算(销售/出库维度数据)  (in use) - deru_zi
         *  【今日19号-算昨日：计算18号数据，下单-付款：计算17号数据，下单-出库：计算14号数据】
         */
        jobDate.getShopCsSaleAndOutStackDataSet().add(validDate.getToOrderedThenOutstackValidDate());
        jobDate.getShopCsSaleAndOutStackDataSet().add(validDate.getToOrderedThenPayValidDate());
        jobDate.getShopCsSaleAndOutStackDataSet().addAll(jobDate.getDates());
        long s32 = System.currentTimeMillis();
        csPerformanceHandleBusiness.handleShopCsSaleAndOutStackData(jobShop, jobDate, isDelData);
        long e32 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f32-time={}ms - method[handleShopCsSaleAndOutStackData-api]", (e32 - s32));
//        calRecord.setCsTorderPerformance(Boolean.TRUE);

        /*
         *  店铺每日绩效  (in use) - deru_zi
         *  【今日19号-算昨日：计算18号数据】
         */
        long s33 = System.currentTimeMillis();
        shopPerformanceHandleBusiness.handleShopDayOverview(jobShop, jobDate, isDelData);
        long e33 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f33-time={}ms - method[handleShopDayOverview-api]", (e33 - s33));
        calRecord.setShopDayOverviewFlag(Boolean.TRUE);

        /**
         *  协助指标 缓存 -- 晴风
         */
        long s34 = System.currentTimeMillis();
        csAssitIndexHandlerBussiness.handlerCsAssitIndex(jobShop, jobDate, isDelData);
        long e34 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f34-time={}ms - method[handlerCsAssitIndex-api]", (e34 - s34));
        calRecord.setAssitIndexFlag(true);
    }


    /**
     * 商品数据，推荐数据计算
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     * @throws Exception
     */

    private void calShopGoodsAndSkuRelevantData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {

        JobCalRecordDO calRecord = jobShop.getCalRecord();
        /*
         * 商品咨询和推荐 (in use)    - 邵美
         */
        Long shopId = jobShop.getShop().getShopId();
        long s41 = System.currentTimeMillis();
        goodsHandleBusiness.handleRecommentAndConsultGoods(jobShop, jobDate, isDelData);
        long e41 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f41-time={}ms - method[handleRecommentAndConsultGoods-api]", (e41 - s41));

        calRecord.setCsGoodsHandleFlag(Boolean.TRUE);

        /*
         * 商品咨询和推荐汇总 (in use) - 邵美
         */
        long s42 = System.currentTimeMillis();
        goodsHandleBusiness.handleCsRecommentBuyerConsultGoodsSummary(jobShop, jobDate, isDelData);
        long e42 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f42-time={}ms - method[handleCsRecommentBuyerConsultGoodsSummary-api]", (e42 - s42));
        calRecord.setCsGoodsSumFlag(Boolean.TRUE);

        /*
         * 商品咨询汇总 - v2
         */
        goodsHandleBusiness.handleCsConsultGoodsSummaryV2(jobShop, jobDate, isDelData);
        logger.info("pop-job-cal-api-" + shopId + "-f42-time={}ms - method[handleCsRecommentBuyerConsultGoodsSummary-api]", (e42 - s42));
        calRecord.setCsGoodsSumFlag(Boolean.TRUE);

        List<JobDateQuery> jobDateList = getNewJobDate(jobDate);

        /*
         * 客服/静默商品销售明细  (in use) 	- 邵美
         */
        long s43 = System.currentTimeMillis();
        for (JobDateQuery newJobDate : jobDateList) {
            goodsSaleHandBussiness.handCsAndSlienGoodsSaleDetail(jobShop, newJobDate, isDelData);
        }
        long e43 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f43-time={}ms - method[handCsAndSlienGoodsSaleDetail-api]", (e43 - s43));
        calRecord.setCsSlientSaleFlag(Boolean.TRUE);

        /*
         *客服/静默商品销售汇总  (in use)  - 邵美
         */
        long s44 = System.currentTimeMillis();
        for (JobDateQuery newJobDate : jobDateList) {
            goodsSaleHandBussiness.handCsAndSlientGoodsSale(jobShop, newJobDate, isDelData);
        }
        long e44 = System.currentTimeMillis();
        logger.info("pop-job-cal-api-" + shopId + "-f44-time={}ms - method[handCsAndSlientGoodsSale-api]", (e44 - s44));
        calRecord.setCsSilentGoodsSumFlag(Boolean.TRUE);

    }

    @Override
    public void pullShopLoginLogData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        JobShopDTO shop = jobShop.getShop();
        logger.info("【{}】{} - {} - {} - pull login log data start ", jobShop.getShopIndex(), shop.getTitle(), shop.getDb(), shop.getSchemaId());

        try {

            // 登陆记录基础数据拉取 - 王凯 - 自测通过
            loginlogBusiness.pullShopLoginlogInfo(jobShop, jobDate, isDelData);
            // 客服每日登陆记录汇总入库 - 王凯 - 自测通过
            loginlogBusiness.calculateShopCsDutyRecord(jobShop, jobDate, isDelData);
            logger.info("【{}】{} - {} - {} - pull login log data success ", jobShop.getShopIndex(), shop.getTitle(), shop.getDb(), shop.getSchemaId());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("【{}】{} - {} - {} - pull login log data fail ", jobShop.getShopIndex(), shop.getTitle(), shop.getDb(), shop.getSchemaId());
            throw e;
        }
    }



    //插入job计算数据记录
    private void handleJobCalRecord(JobCalRecordDO jobCalRecord, JobShopDTO jobShop, Date date) {
        int m = jobRecordDao.deleteJobCalRecordByShopIdAndDate(jobShop, date);
        if (logger.isDebugEnabled()) {

            logger.debug("{},{} JobCal record delete num {}", jobShop.getTitle(), jobShop.getDb(), m);
        }
        int n = jobRecordDao.insertJobCalRecord(jobShop, jobCalRecord, date);
        if (logger.isDebugEnabled()) {

            logger.debug("{},{} JobCal record insert num ", jobShop.getTitle(), jobShop.getDb(), n);
        }
    }


    @Override
    public void pullShopRealtimeData(JobShopQuery jobShop, Boolean isDelData) {

        JobShopDTO shop = jobShop.getShop();
        //获取数据的结束时间
        Date nowDate = Date.from(Instant.now());

        Date startDate = DateUtil.getStartTimeOfDate(nowDate);
        Date endDate = DateUtil.getEndTimeOfDate(nowDate);
//		Date previousGetDataTime = jobShop.getPreviousGetDataTime();
//
//		if(!DateUtil.isSameDate(previousGetDataTime, nowDate)){
//			previousGetDataTime = startDate;
//		}
    }

    //更新pes_job_pull_record表状态
    private void updatePullJobRecordStatus(JobShopDTO jobShop, Date date, Integer status) {
        try {
            jobRecordDao.updateJobPullRecordStatusByShopIdAndDate(jobShop, date, status);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //更新pes_job_pull_record表基础数据记录
    private int updatePullJobRecord(JobPullRecordDO jobPullRecord, JobShopDTO jobShop, Date date) {
        int count = jobRecordDao.updateJobPullRecordByShopIdAndDate(jobPullRecord, jobShop, date);
        logger.info("{},{} jobrecord update num {}", jobShop.getTitle(), jobShop.getDb(), count);
        return count;
    }

    private JobPullRecordDO dtoConversionDo(JobPullRecordDTO jobPullRecordDTO) {
        JobPullRecordDO jobPullRecordDO = new JobPullRecordDO();
        BeanUtils.copyProperties(jobPullRecordDTO, jobPullRecordDO);
        return jobPullRecordDO;
    }

}
