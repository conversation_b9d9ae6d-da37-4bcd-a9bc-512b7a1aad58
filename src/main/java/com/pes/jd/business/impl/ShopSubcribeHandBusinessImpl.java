package com.pes.jd.business.impl;

import com.pes.jd.business.ShopSubcribeHandBusiness;
import com.pes.jd.data.converter.SubscribeConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class ShopSubcribeHandBusinessImpl implements ShopSubcribeHandBusiness {

	@Autowired
	private SubscribeConverter subscribeConverter;
	
	@Override
	public void pullShopSubcribeInfo(String session) throws Exception {
		subscribeConverter.getSubscribeInfo(session);
	}

}
