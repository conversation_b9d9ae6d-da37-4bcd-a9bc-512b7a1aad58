package com.pes.jd.business.impl;

import com.pes.jd.business.ShopPvUvDayHandleBussiness;
import com.pes.jd.dao.JobPullApiRecordDao;
import com.pes.jd.data.converter.ShopPvUvConverter;
import com.pes.jd.model.DO.JobPullApiRecordDO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ShopPvUvDayHandleBussinessImpl implements ShopPvUvDayHandleBussiness{

	private Logger logger = LoggerFactory.getLogger(ShopPvUvDayHandleBussinessImpl.class);
	
	@Resource
	private ShopPvUvConverter shopPvUvConverter;

	@Resource
	private JobPullApiRecordDao jobPullApiRecordDao;

	@Override
	public void pullShopPvUvDayInfo(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
		long s = System.currentTimeMillis();
		logger.info("start pull ShopPvUvDay");
		JobPullApiRecordDO jobPullApiRecordDO = new JobPullApiRecordDO();
		jobPullApiRecordDO.setShopId(jobShop.getShop().getShopId());
		jobPullApiRecordDO.setDate(jobDate.getDate());
		jobPullApiRecordDO.setType(Byte.valueOf("1"));
		jobPullApiRecordDO.setModified(new Date());

		try {
			shopPvUvConverter.getShopPvUvDay(jobShop.getShop(), jobDate.getDate(), isDelData);
			jobPullApiRecordDO.setResult(Byte.valueOf("1"));
		} catch (Exception e) {
			jobPullApiRecordDO.setResult(Byte.valueOf("0"));
			String message = e.getMessage();
			if (StringUtils.isNotBlank(message)){
				try {
					int length = Math.min(message.length(), 125);
					jobPullApiRecordDO.setMsg(message.substring(0,length));
				}catch (Exception ee){
					ee.printStackTrace();
				}
			}
			logger.error("【{}】 handleShopPvUvDay error", jobShop.getShop().getTitle(), e);
		}

		try{//防止报错不影响主业务
			jobPullApiRecordDao.deleteByShopIdAndDate(jobPullApiRecordDO.getShopId(), jobPullApiRecordDO.getDate(), jobPullApiRecordDO.getType(), jobShop.getShop().getSchemaId());
			jobPullApiRecordDao.insert(jobPullApiRecordDO, jobPullApiRecordDO.getDate(), jobShop.getShop().getSchemaId());
		}catch (Exception e){
			logger.error(e.getMessage(),e);
		}

		long e = System.currentTimeMillis();
		logger.info("handle ShopPvUvDay end,time:{}", (e - s) / 1000);

	}

}
