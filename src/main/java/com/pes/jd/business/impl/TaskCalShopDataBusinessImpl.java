package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.TaskCalShopDataBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse;
import com.pes.jd.ms.domain.Result.task.dispatching.MessageSendResult;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.DispatchingRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

/**
 * @Author: aiJun
 * @Date: 2019-06-25 15:31
 * @Version 1.0
 */
@Service
public class TaskCalShopDataBusinessImpl implements TaskCalShopDataBusiness {
    private static Logger logger = LoggerFactory.getLogger(TaskCalShopDataBusinessImpl.class);

    @Autowired
    private DispatchingRestTemplate dispatchingRestTemplate;

    private final String TASK_CAL_ALL_SHOPDATA_SERVICE_URL = "/task/calShopDataMessage/sendCalAllShopDataMessage";
    @Override
    public ApiResponse calAllShopData(String startDateStr, String endDateStr) {

        {

            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("startDateStr",startDateStr)
                    .put("endDateStr",endDateStr)
                    .put("type", TaskJobDispatchEnum.SHOP_DATA_CAL.getType())
                    .toRequestEntity();

            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

            RestApiResponse<MessageSendResult> restApiResponse;
            try {
                long s = System.currentTimeMillis();
                restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_CAL_ALL_SHOPDATA_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>(){});
                long e = System.currentTimeMillis();
                logger.info("send message calAllShopData need time={}ms",(e-s));
            } catch (HttpClientErrorException e) {
                logger.error(e.getMessage(),e);
                throw e;
            }

            System.out.println(restApiResponse);
            logger.info(restApiResponse.getSuccess()+"");
            if(null!=restApiResponse && restApiResponse.getSuccess()){
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
            }
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
        }
    }
}
