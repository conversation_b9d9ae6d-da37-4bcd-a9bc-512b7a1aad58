package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.OrderSkuEvaluateBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.OrderSkuEvaluateParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class OrderSkuEvaluateBusinessImpl implements OrderSkuEvaluateBusiness{

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;
	
	@Override
	public ApiResponse selectOrderSkuEvaluate(ShopQuery shop, OrderSkuEvaluateParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("param", param)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/cs/evaluate/selectOrderSkuEvaluate", body);
	}

	@Override
	public ApiResponse selectOrderSkuEvaluateInfo(ShopQuery shop, String startDate,String endDate,String id,String orderId) throws DBNotExistException {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("id", id)
                .put("orderId", orderId)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/cs/evaluate/selectOrderSkuEvaluateInfo", body);
	}

}
