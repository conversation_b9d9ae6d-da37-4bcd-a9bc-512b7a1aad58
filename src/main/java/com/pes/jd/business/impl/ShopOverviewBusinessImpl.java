package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.Constants.GoodFilterEnum;
import com.pes.jd.business.GoodsHandleBusiness;
import com.pes.jd.business.ShopOverviewBusiness;
import com.pes.jd.dao.*;
import com.pes.jd.model.DO.ShopDayOverviewDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ShopOverviewBusinessImpl implements ShopOverviewBusiness {
    @Resource
    private ShopOverviewDao shopOverviewDao;

    @Resource
    private ShopOvDayDao ShopOvDayDao;

    @Resource
    private OrderFilterDao orderFilterDao;

    @Resource
    private PresaleOrderDao presaleOrderDao;

    @Resource
    private GoodsHandleBusiness goodsHandleBusiness;

    @Resource
    private OrderDao orderDao;
    @Override
    public ShopDayOverviewDO getShopDayOverview(JobShopQuery jobShop, Date date) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        ShopDayOverviewDO dayOv = new ShopDayOverviewDO(shop.getShopId(), date);
        dayOv.setSaleAmount(0D);
        dayOv.setSaleBuyerNum(0);
        dayOv.setSaleOrderNum(0);
        dayOv.setConsignNum(0);
        dayOv.setOrderedAmount(0D);
        dayOv.setOrderedNum(0);
        dayOv.setCfmGoodsOAmount(0D);
        dayOv.setCfmGoodstONum(0);
        dayOv.setSaleAmountPresale(0D);
        dayOv.setSaleAmountPreordain(0D);
        Date startDate = DateUtil.getStartTimeOfDate(date);
        Date endDate = DateUtil.getEndTimeOfDate(date);
        int orderPaidValidDays = 1; //下单到付款
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //付款时间
        validDateRange.setAdjustPaidStartDate(DateUtil.getDateByPeriod(startDate, -orderPaidValidDays));
        validDateRange.setAdjustPaidEndDate(endDate);
        //商品过滤TODO  优化sql  上面查到的订单集合
        List<OrderFilterDTO> orderFilters = orderFilterDao.queryPesOrdersByShopId(jobShop.getShop(), startDate, endDate);
        //店铺数据 - 下单维度
        List<CreatedOrderDTO> createdOredrLst = shopOverviewDao.selectShopCreatedOredrLst(shop, date, validDateRange);
        if (CollectionUtils.isNotEmpty(createdOredrLst)) {
            double payment = 0;//当日下单当日付款销售额
            int orderNum = 0;//当日销售订单数
            int goodsNum = 0;//当日销售件数
            Set<String> buyerSet = Sets.newHashSet(); // 销售人数（去重
            for (CreatedOrderDTO order : createdOredrLst) {
                orderNum++;
                double orderPayment = BaseUtils.getNonNull(order.getOrderPayment());
                int orderGoodsNum = BaseUtils.getNonNull(order.getOrderGoodsNum());
                //商品过滤（销售额、销售件数
                for (OrderFilterDTO filter : orderFilters) {
                    if (order.getOrderId() == null || filter.getOrderId() == null
                            || !order.getOrderId().equals(filter.getOrderId())) continue;
                    //bug2865
                    //orderPayment -= BaseUtils.getNonNull(filter.getPrice());
                    orderGoodsNum -= BaseUtils.getNonNull(filter.getNum());
                }
                payment += Math.max(orderPayment, 0.0);
                goodsNum += Math.max(orderGoodsNum, 0);

                buyerSet.add(order.getBuyerNick());
            }
            dayOv.setOrderedNum(orderNum);
            dayOv.setOrderedAmount(payment);
            dayOv.setOrderedGoodsNum(goodsNum);
            dayOv.setOrderedBuyerNum(buyerSet.size());
        }
        //付款时间
        validDateRange.setAdjustPaidStartDate(DateUtil.getDateByPeriod(startDate, -7));
        validDateRange.setAdjustPaidEndDate(endDate);
        List<SaleOrderDTO> saleOrderLst = shopOverviewDao.selectShopSaleOrderLst(shop, date, validDateRange);
        //过滤预售付定金未付尾款
        List<Long> presaleOdIds = presaleOrderDao.selectShopBargainNoBalanceOdIds(shop, date, validDateRange);
        Set<Long> odIdFilter = Sets.newHashSet(presaleOdIds);
        saleOrderLst.removeIf(t -> odIdFilter.contains(t.getOrderId()));

        List<CreatedOrderDTO> goodsToPaySaleOrderLst = null;
        if (CollectionUtils.isNotEmpty(createdOredrLst)) {//下单在指定时间内的货到付款订单集合
            goodsToPaySaleOrderLst = createdOredrLst.stream()
                    .filter(x -> x.getPayType() == 1).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(saleOrderLst) || CollectionUtils.isNotEmpty(goodsToPaySaleOrderLst)) {
            double payment = 0;
            int orderNum = 0;
            int goodsNum = 0;
            double postFee = 0;
            double paymentPreordain = 0;//预约销售额
            Set<String> buyerSet = Sets.newHashSet();//销售买家

            if (CollectionUtils.isNotEmpty(saleOrderLst)) {//当日付款的订单
                Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, date, date, saleOrderLst.stream().map(SaleOrderDTO::getOrderId).collect(Collectors.toList()));
                Collection<Long> filterOrderIdsOfFinal = (Collection<Long>) filterMap.get(GoodFilterEnum.FILTER_ORDERIDS_OF_FINAL.getKey());
                Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
                Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
                for (SaleOrderDTO order : saleOrderLst) {
                    orderNum++;
                    int orderGoodsNum = 0;
                    double orderPayment = BaseUtils.getNonNull(order.getOrderPayment());
                    List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(order.getOrderId());
                    if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                        for (OrderDetailDTO dto : orderDetailDTOS) {
                            orderGoodsNum += BaseUtils.getNonNull(dto.getItemNum());
                        }
                    }
                    //商品过滤（销售额、销售件数
                    for (OrderFilterDTO filter : orderFilters) {
                        if (order.getOrderId() == null || filter.getOrderId() == null
                                || !order.getOrderId().equals(filter.getOrderId())) continue;
                        //bug2865
//                        orderPayment -= BaseUtils.getNonNull(filter.getPrice());
                        orderGoodsNum -= BaseUtils.getNonNull(filter.getNum());
                    }
                    if (filterOrderIdsOfFinal.contains(order.getOrderId())) orderGoodsNum = 0;
                    payment += Math.max(orderPayment, 0.0);
                    goodsNum += Math.max(orderGoodsNum, 0);
                    postFee += BaseUtils.getNonNull(order.getOrderPostFee());
                    buyerSet.add(order.getBuyerNick());

                    if (order.getOrderType() != null && order.getOrderType() == 2) {//预约订单
                        paymentPreordain += Math.max(orderPayment, 0.0);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(goodsToPaySaleOrderLst)) {//当日货到付款的订单
                for (CreatedOrderDTO order : goodsToPaySaleOrderLst) {
                    orderNum++;

                    double orderPayment = BaseUtils.getNonNull(order.getOrderPayment());
                    int orderGoodsNum = BaseUtils.getNonNull(order.getOrderGoodsNum());
                    //商品过滤（销售额、销售件数
                    for (OrderFilterDTO filter : orderFilters) {
                        if (order.getOrderId() == null || filter.getOrderId() == null
                                || !order.getOrderId().equals(filter.getOrderId())) continue;
                        //bug2865
//                        orderPayment -= BaseUtils.getNonNull(filter.getPrice());
                        orderGoodsNum -= BaseUtils.getNonNull(filter.getNum());
                    }
                    payment += Math.max(orderPayment, 0.0);
                    goodsNum += Math.max(orderGoodsNum, 0);
                    postFee += BaseUtils.getNonNull(order.getOrderPostFee());
                    buyerSet.add(order.getBuyerNick());

                    if (order.getOrderType() != null && order.getOrderType() == 2) {//预约订单
                        paymentPreordain += Math.max(orderPayment, 0.0);
                    }
                }
            }
            dayOv.setSaleOrderNum(orderNum);
            dayOv.setSaleBuyerNum(buyerSet.size());
            dayOv.setSaleAmount(payment);
            dayOv.setSaleGoodsNum(goodsNum);
            dayOv.setOrderPostFee(postFee);
            dayOv.setSaleAmountPreordain(paymentPreordain);
        }
        /*
         * 统计预售销售额
         */
        Date period = DateUtils.getDateByPeriod(startDate, -60);//预售付尾款 - 下单时间
        List<Long> presaleOrderIds = presaleOrderDao.selectOrderIdByShopIdAndBalanceTime(shop,
                period, startDate, endDate);
        List<SaleOrderDTO> presaleList = shopOverviewDao.selectPaymentByOrderIds(shop, presaleOrderIds, period, endDate);
        List<Long> directTradeId = Optional.ofNullable(orderDao.selectOrderDirectTradeId(shop,1L,period, endDate)).orElse(new ArrayList<>(0));

        if (CollectionUtils.isNotEmpty(presaleList)) {
            double paymentPresale = 0.0;

            for (SaleOrderDTO presale : presaleList) {
                //过滤父订单
                if(directTradeId.contains(presale.getOrderId())){
                    continue;
                }
                double orderPayment = BaseUtils.getNonNull(presale.getOrderPayment());
                //商品过滤（销售额、销售件数
                for (OrderFilterDTO filter : orderFilters) {
                    if (presale.getOrderId() == null || filter.getOrderId() == null
                            || !presale.getOrderId().equals(filter.getOrderId())) continue;
                    //bug2865
//                    orderPayment -= BaseUtils.getNonNull(filter.getPrice());
                }

                if (presale.getOrderType() != null && presale.getOrderType() == 1) {//订单表中校验是否为预售订单
                    paymentPresale += Math.max(orderPayment, 0.0);
                }
            }
            dayOv.setSaleAmountPresale(paymentPresale);
        }
        // 出库时间
        validDateRange.setAdjustOutStockStartDate(DateUtil.getDateByPeriod(startDate, -CommonConstants.OUT_STOCK_DAY));
        validDateRange.setAdjustOutStockEndDate(endDate);
        List<OutStockOrderDTO> outStockOrderLst = shopOverviewDao.selectShopOutStockOrderLst(shop, date, validDateRange);
        if (CollectionUtils.isNotEmpty(outStockOrderLst)) {
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, date, date, outStockOrderLst.stream().map(OutStockOrderDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            double payment = 0;
            int orderNum = 0;
            int goodsNum = 0;
            Set<String> buyerSet = Sets.newHashSet();
            for (OutStockOrderDTO dto : outStockOrderLst) {
                orderNum++;
                payment += dto.getPayment();
                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(dto.getOrderId());
                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                    for (OrderDetailDTO dto1 : orderDetailDTOS) {
                        goodsNum += BaseUtils.getNonNull(dto1.getItemNum());
                    }
                }
                buyerSet.add(dto.getBuyerNick());
            }

            dayOv.setOutStockNum(buyerSet.size());
            dayOv.setOutStockGoodsNum(goodsNum);
            dayOv.setOutStockOrderNum(orderNum);
            dayOv.setOutStockAmount(payment);
        }

        dayOv.setCfmGoodstONum(0);
        dayOv.setCfmGoodsOAmount(0D);

/*		final int confirmGoodsValidDays = 15;
		// 确认收货时间
		validDateRange.setAdjustConfirmGoodsStartDate(DateUtil.getDateByPeriod(startDate, 0- sys.getOutStockValidDurationTime() - confirmGoodsValidDays));
		validDateRange.setAdjustConfirmGoodsEndDate(endDate);

		List<ConfirmGoodsOrderDTO> confirmGoodsPesLst = shopOverviewDao.selectShopConfirmGoodsOrderLst(shop, date, validDateRange);
		if (CollectionUtils.isNotEmpty(confirmGoodsPesLst)) {
			double payment = 0;
			int orderNum = 0;
			int goodsNum = 0;
			Set<String> buyerSet = Sets.newHashSet();
			for (ConfirmGoodsOrderDTO dto : confirmGoodsPesLst) {
				orderNum++;
				payment += dto.getPayment();
				goodsNum += dto.getNum();
				buyerSet.add(dto.getBuyerNick());
			}

			if (dayOv != null) {
				dayOv.setCfmGoodstONum(orderNum);
				dayOv.setCfmGoodsOAmount(payment);
			}
		}*/
        return dayOv;
    }

    @Override
    public int handleInsertShopDayOverview(JobShopQuery jobShop, Date date,
                                           ShopDayOverviewDO dayOverview, boolean isDelData) {

        JobShopDTO shop = jobShop.getShop();
        int num1 = ShopOvDayDao.deleteShopDayOverviewByDate(shop, date);
//		logger.info("batch delete shop shopOvDay num:{}",num1);
        int num2 = ShopOvDayDao.insertShopDayOverview(shop, date, dayOverview);
//		logger.info("batch insert shop shopOvDay num:{}",num2);
        return num2;
    }
}
