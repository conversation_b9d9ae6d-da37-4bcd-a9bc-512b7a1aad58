package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.pes.jd.business.AssistServiceAnalysisBusiness;
import com.pes.jd.business.CsOrderIndexBusiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AssistServiceAnalysisBusinessImpl implements AssistServiceAnalysisBusiness {


	@Autowired
	private CsOrderIndexBusiness csOrderIndexBusiness;

	@Autowired
	private ShopSysManagerBusiness shopSysManagerBusiness;
	
	/**
	 * 协助服务分析查询
	 * @throws Exception 
	 */
	@SuppressWarnings({"serial" })
	@Override
	public ApiResponse searchCsOrderIndex(UserShopQuery shop, String csNicks, String startDate, String endDate, AssistServiceQuery assistServiceQuery, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam)
			throws Exception {
		List<CsDTO> csLst=	shopSysManagerBusiness.selectShopCswwSimpleNames(shop.getSelectedShop(), null);
		ApiResponse apiResponse = csOrderIndexBusiness.selectCsOrderIndex(shop.getSelectedShop(), csNicks, startDate, endDate, assistServiceQuery, sortPageQuery, orderInfoLogUploadParam);
		Map<String, Object> data = Maps.newHashMap();
		String jsonStr = JSON.toJSONString(apiResponse.getData().get("csAssistServiceAnalysisVO"));
		DataAnalysisVO<CsOrderIndexDTO> assistVO = new Gson().fromJson(jsonStr, new TypeToken<DataAnalysisVO<CsOrderIndexDTO>>() {}.getType());
		List<CsOrderIndexDTO> assistList = assistVO.getDataList();
		if(CollectionUtils.isNotEmpty(assistList)){
			if(CollectionUtils.isNotEmpty(csLst)){
				Map<String, String> simpleNameMap = csLst.stream()
						.collect(Collectors.toMap(CsDTO::getNick, cs -> cs.getCsSimpleNick()));
				for(CsOrderIndexDTO assist :assistList){
					assist.setCsSimpleNick(simpleNameMap.get(assist.getCsNick()));
				}
			}
		}else{
			assistVO = new DataAnalysisVO<>();
			assistVO.setCount(0);
			assistVO.setDataList(new ArrayList<>());
			assistVO.setPageFlag(true);
		}
		data.put("csAssistServiceAnalysisVO", assistVO);
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
	}

}
