package com.pes.jd.business.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.business.UserPortraitBusiness;
import com.pes.jd.dao.CsChatpeerDao;
import com.pes.jd.dao.UserPortraitStatisticsDao;
import com.pes.jd.data.api.UserPortraitOperator;
import com.pes.jd.model.DO.UserPortraitLabel;
import com.pes.jd.model.DO.UserPortraitStatistics;
import com.pes.jd.model.DTO.CommonCsChatpeerDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class UserPortraitBusinessImpl implements UserPortraitBusiness {

    private Logger logger= LoggerFactory.getLogger(UserPortraitBusinessImpl.class);

    @Autowired
    private CsChatpeerDao csChatpeerDao;
    @Autowired
    private UserPortraitOperator userPortraitOperator;
    @Autowired
    private UserPortraitStatisticsDao userPortraitStatisticsDao;

    //这个接口只能单线程请求。会触发限流
    ExecutorService executor = Executors.newFixedThreadPool(2);


    /**
     *
     * @param jobShop
     * @param jobDate  对应日期的数据
     * @param isDelData
     * @throws Exception
     */
    @Override
    public void handleUserPortraitStatistics(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        Date startDate = jobDate.getStartDate();
        Date endDate = jobDate.getEndDate();
        DateUtil.splitDate(startDate, endDate).forEach(date->{
        JobShopDTO shop = jobShop.getShop();
        List<CommonCsChatpeerDTO> csChatpeerDOList =csChatpeerDao.selectBuyerNickByDate(shop,DateUtil.getStartTimeOfDate(date),DateUtil.getEndTimeOfDate(date));
        List<String> buyerNickList = csChatpeerDOList.stream().map(x -> x.getBuyerNick()).collect(Collectors.toList());
            try {
            //塞入任务
            List<CompletableFuture<UserPortraitLabel>> futures = buyerNickList.stream()
                    .map(buyerNick -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return userPortraitOperator.getUserLabelData(
                                    shop.getSessionKey(), buyerNick, buyerNick, buyerNick, null);
                        } catch (Exception e) {
                            logger.error("获取用户画像数据失败, buyerNick: {}", buyerNick, e);
                            return null;
                        }
                    }, executor))
                    .collect(Collectors.toList());

            // 等待所有任务完成并收集结果
            List<UserPortraitLabel> userPortraitLabelList = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (userPortraitLabelList.isEmpty()) {
                logger.warn("店铺 {} 没有获取到有效的用户画像数据", shop.getShopId());
                return;
            }
            // 创建统计对象并计算统计数据
            UserPortraitStatistics statistics = createUserPortraitStatistics(
                    shop.getShopId(), date, userPortraitLabelList);


            // 保存统计数据
            if(isDelData){
                userPortraitStatisticsDao.deleteUserPortraitStatistics(shop.getShopId(), date);
            }
            userPortraitStatisticsDao.insertUserPortraitStatistics(statistics);

            logger.info("完成店铺 {} 用户画像统计，处理用户数: {}",
                    shop.getShopId(), userPortraitLabelList.size());


        } catch (Exception e){
                logger.error("处理用户画像失败", e);
        }
        });
    }

    /**
     * 创建用户画像统计数据
     */
    private UserPortraitStatistics createUserPortraitStatistics(Long shopId, Date statisticsDate,
                                                                List<UserPortraitLabel> userLabels) {
        UserPortraitStatistics statistics = new UserPortraitStatistics();
        statistics.setShopId(shopId);
        statistics.setStatisticsDate(convertToLocalDate(statisticsDate));
        statistics.setTotalUsers(userLabels.size());
        statistics.setCreatedTime(LocalDateTime.now());
        statistics.setUpdatedTime(LocalDateTime.now());

        // 用于计算平均客单价
        List<BigDecimal> unitPrices = new ArrayList<>();
        Map<String, Integer> regionMap = new HashMap<>();

        // 遍历所有用户画像数据进行统计
        for (UserPortraitLabel label : userLabels) {
            // 年龄分布统计 ...
            calculateAgeDistribution(statistics, label);

            // 性别分布统计 ...
            calculateGenderDistribution(statistics, label);

            // 婚姻状况统计 ...
            calculateMarriageDistribution(statistics, label);

            // 职业分布统计 ...
            calculateProfessionDistribution(statistics, label);

            //用户群体类型 ...
            calculateGroupDistribution(statistics,label);

            // 孩子数量 ...
            calculateChildrenDistribution(statistics, label);

            // 地区分布统计 ...
            calculateRegionDistribution(label, regionMap);

            // 大促预售购买统计 ...
            calculatePresaleDistribution(statistics, label);

            // 平台促销敏感人群统计 ...
            calculatePromotionSensitivityDistribution(statistics, label);

            // 大促高消费金额统计 ...
            calculateHighConsumptionDistribution(statistics, label);

            // 新品偏好人群统计 ...
            calculateNewProductPreferenceDistribution(statistics, label);

            // 价格敏感度统计
            calculatePriceSensitivityDistribution(statistics, label);

            // 喜欢大促购买人群统计
            calculatePromotionBehaviorDistribution(statistics, label);

            // 好评率统计
            calculateCommentDistribution(statistics, label);

            // 客单价统计
            calculateUnitPriceData(label, unitPrices, statistics);


        }

        // 设置地区分布JSON
        setRegionDistributionJson(statistics, regionMap);

        // 计算平均客单价
        calculateAverageUnitPrice(statistics, unitPrices);

        return statistics;
    }


    /**
     * 年龄分布统计
     */
    private void calculateAgeDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        Integer age = label.getL01Age1093();
        if (age == null) return;

        switch (age) {
            case 0: statistics.setAge0To15(statistics.getAge0To15() + 1); break;
            case 1: statistics.setAge16To20(statistics.getAge16To20() + 1); break;
            case 2: statistics.setAge21To25(statistics.getAge21To25() + 1); break;
            case 3: statistics.setAge26To30(statistics.getAge26To30() + 1); break;
            case 4: statistics.setAge31To35(statistics.getAge31To35() + 1); break;
            case 5: statistics.setAge36To40(statistics.getAge36To40() + 1); break;
            case 6: statistics.setAge41To45(statistics.getAge41To45() + 1); break;
            case 7: statistics.setAge46To50(statistics.getAge46To50() + 1); break;
            case 8: statistics.setAge51To55(statistics.getAge51To55() + 1); break;
            case 9: statistics.setAge56To60(statistics.getAge56To60() + 1); break;
            case 10: statistics.setAge61To65(statistics.getAge61To65() + 1); break;
            case 11: statistics.setAge66To70(statistics.getAge66To70() + 1); break;
            case 12: statistics.setAgeOver71(statistics.getAgeOver71() + 1); break;
            default: logger.error("不正常年龄分布数据: {}",age); break;
        }
    }

    /**
     * 性别分布统计
     */
    private void calculateGenderDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        Integer gender = label.getL01Sex1104();
        if (gender == null) return;

        if (gender == 0) { // 女性
            statistics.setGenderFemale(statistics.getGenderFemale() + 1);
        }else if (gender == 1) { // 男性
            statistics.setGenderMale(statistics.getGenderMale() + 1);
        } else{
            logger.error("不正常性别统计数据: {}",gender);
        }
    }

    /**
     * 婚姻状况统计
     */
    private void calculateMarriageDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String marriage = label.getUlpBaseMarriage();
        if (marriage == null) return;

        if ("0".equals(marriage)) { // 未婚
            statistics.setMarriageSingle(statistics.getMarriageSingle() + 1);
        }else if ("1".equals(marriage)) { // 已婚
            statistics.setMarriageMarried(statistics.getMarriageMarried() + 1);
        } else{
            logger.error("不正常婚姻状况统计数据: {}",marriage);
        }
    }

    /**
     * 职业分布统计
     */
    private void calculateProfessionDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String profession = label.getL01Profession1139();
        if (profession == null) return;

        switch (profession) {
            case "a": statistics.setProfessionFinance(statistics.getProfessionFinance() + 1); break;
            case "b": statistics.setProfessionMedical(statistics.getProfessionMedical() + 1); break;
            case "d": statistics.setProfessionEmployee(statistics.getProfessionEmployee() + 1); break;
            case "e": statistics.setProfessionWorker(statistics.getProfessionWorker() + 1); break;
            case "f": statistics.setProfessionTeacher(statistics.getProfessionTeacher() + 1); break;
            case "g": statistics.setProfessionFarmer(statistics.getProfessionFarmer() + 1); break;
            case "h": statistics.setProfessionStudent(statistics.getProfessionStudent() + 1); break;
            case "i": statistics.setProfessionIndividual(statistics.getProfessionIndividual() + 1); break;
            case "m": statistics.setProfessionUrbanOther(statistics.getProfessionUrbanOther() + 1); break;
            case "n": statistics.setProfessionRuralOther(statistics.getProfessionRuralOther() + 1); break;
            default: logger.error("不正常职业分布统计数据: {}",profession); break;
        }
    }

    /**
     * 孩子数量统计
     */
    private void calculateChildrenDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String childNum = label.getSearchChildNum();
        if (childNum == null) return;

        switch (childNum) {
            case "1": statistics.setChildrenOne(statistics.getChildrenOne() + 1); break;
            case "2": statistics.setChildrenTwo(statistics.getChildrenTwo() + 1); break;
            case "3": statistics.setChildrenThree(statistics.getChildrenThree() + 1); break;
            case "4": statistics.setChildrenFour(statistics.getChildrenFour() + 1); break;
            case "未知": statistics.setChildrenUnknown(statistics.getChildrenUnknown() + 1); break;
            default: logger.error("不正常孩子数量统计接口: {}",childNum);
        }
    }

    /**
     * 价格敏感度统计
     */
    private void calculatePriceSensitivityDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String priceSensitivity = label.getL01Label26137();
        if (priceSensitivity == null) return;

        try {
            int sensitivity = Integer.parseInt(priceSensitivity);
            switch (sensitivity) {
                case 0: statistics.setPriceSensitivityLuxury(statistics.getPriceSensitivityLuxury() + 1); break;
                case 1: statistics.setPriceSensitivityHighCollar(statistics.getPriceSensitivityHighCollar() + 1); break;
                case 2: statistics.setPriceSensitivityHighQuality(statistics.getPriceSensitivityHighQuality() + 1); break;
                case 3: statistics.setPriceSensitivityUrbanCollar(statistics.getPriceSensitivityUrbanCollar() + 1); break;
                case 4: statistics.setPriceSensitivityMass(statistics.getPriceSensitivityMass() + 1); break;
                case 5: statistics.setPriceSensitivityLowPower(statistics.getPriceSensitivityLowPower() + 1); break;
                case 6: statistics.setPriceSensitivityInactive(statistics.getPriceSensitivityInactive() + 1); break;
                default: logger.error("不正常价格敏感度统计数据: {}",priceSensitivity); break;
            }
        } catch (NumberFormatException e) {
            logger.debug("无法解析价格敏感度: {}", priceSensitivity);
        }
    }

    /**
     * 喜欢大促购买人群
     */
    private void calculatePromotionBehaviorDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String promotionFlag = label.getL1Flag32486();
        if (promotionFlag == null) return;

        switch (promotionFlag) {
            case "0": statistics.setPromotionLowValue(statistics.getPromotionLowValue() + 1); break;
            case "1": statistics.setPromotionStable(statistics.getPromotionStable() + 1); break;
            case "2": statistics.setPromotionRising(statistics.getPromotionRising() + 1); break;
            case "3": statistics.setPromotionDeclining(statistics.getPromotionDeclining() + 1); break;
            case "4": statistics.setPromotionHesitant(statistics.getPromotionHesitant() + 1); break;
            default: logger.error("不正常大促购买行为统计数据: {}",promotionFlag); break;
        }
    }

    /**
     * 大促预售购买统计
     */
    private void calculatePresaleDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String presale = label.getL1PreSaleGroup33750();
        if (presale == null) return;

        if ("1".equals(presale)) {
            statistics.setPresaleYes(statistics.getPresaleYes() + 1);
        }else if ("0".equals(presale)) {
            statistics.setPresaleNo(statistics.getPresaleNo() + 1);
        } else{
            logger.error("不正常购买数据: {}",presale);
        }
    }

    /**
     * 平台促销敏感度统计
     */
    private void calculatePromotionSensitivityDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String promotionSensitivity = label.getL1PromotionGroup33756();
        if (promotionSensitivity == null) return;

        switch (promotionSensitivity) {
            case "0": statistics.setPromotionSensitivityUnknown(statistics.getPromotionSensitivityUnknown() + 1); break;
            case "1": statistics.setPromotionNotSensitive(statistics.getPromotionNotSensitive() + 1); break;
            case "2": statistics.setPromotionLightlySensitive(statistics.getPromotionLightlySensitive() + 1); break;
            case "3": statistics.setPromotionModeratelySensitive(statistics.getPromotionModeratelySensitive() + 1); break;
            case "4": statistics.setPromotionHighlySensitive(statistics.getPromotionHighlySensitive() + 1); break;
            case "5": statistics.setPromotionVerySensitive(statistics.getPromotionVerySensitive() + 1); break;
            default: logger.error("不正常促销敏感度统计数据: {}",promotionSensitivity); break;
        }
    }

    /**
     * 大促高消费统计
     */
    private void calculateHighConsumptionDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String highConsumption = label.getL1HighGmv33751();
        if (highConsumption == null) return;

        if ("1".equals(highConsumption)) {
            statistics.setHighConsumptionYes(statistics.getHighConsumptionYes() + 1);
        }else if ("0".equals(highConsumption)) {
            statistics.setHighConsumptionNo(statistics.getHighConsumptionNo() + 1);
        }else{
            logger.error("不正常高消费数据: {}",highConsumption);
        }
    }

    /**
     * 新品偏好人群统计
     */
    private void calculateNewProductPreferenceDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        // 潜在新品偏好
        String potential = label.getL01IsPotential131();
        if (potential != null) {
            if ("1".equals(potential)) {
                statistics.setNewProductPotentialYes(statistics.getNewProductPotentialYes() + 1);
            }else if ("0".equals(potential)) {
                statistics.setNewProductPotentialNo(statistics.getNewProductPotentialNo() + 1);
            }else{
                logger.error("不正常潜在新品偏好数据: {}",potential);
            }
        }

        // 中度新品偏好
        String medium = label.getL01IsMediumLevel132();
        if (medium != null) {
            if ("1".equals(medium)) {
                statistics.setNewProductMediumYes(statistics.getNewProductMediumYes() + 1);
            }else if ("0".equals(medium)) {
                statistics.setNewProductMediumNo(statistics.getNewProductMediumNo() + 1);
            } else{
                logger.error("不正常中度新品偏好数据: {}",medium);
            }
        }

        // 重度新品偏好
        String severe = label.getL01IsSevereLevel133();
        if (severe != null) {
            if ("1".equals(severe)) {
                statistics.setNewProductSevereYes(statistics.getNewProductSevereYes() + 1);
            }else if ("0".equals(severe)) {
                statistics.setNewProductSevereNo(statistics.getNewProductSevereNo() + 1);
            }else{
                logger.error("不正常重度新品偏好数据: {}",severe);
            }

        }
    }

    /**
     * 好评率统计
     */
    private void calculateCommentDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String comment = label.getL1CommentGroup33749();
        if (comment == null) return;

        switch (comment) {
            case "1": statistics.setCommentHigh(statistics.getCommentHigh() + 1); break;
            case "2": statistics.setCommentMedium(statistics.getCommentMedium() + 1); break;
            case "3": statistics.setCommentLow(statistics.getCommentLow() + 1); break;
            case "0": statistics.setCommentUnknown(statistics.getCommentUnknown() + 1); break;
            default: logger.error("不正常好评率数据: {}", comment); break;
        }
    }

    /**
     * 人群分布统计
     */
    private void calculateGroupDistribution(UserPortraitStatistics statistics, UserPortraitLabel label) {
        String groupType = label.getDcGroupType();
        if (groupType == null) return;

        switch (groupType) {
            case "学生一族":
                statistics.setStudentGroup(statistics.getStudentGroup() + 1);
                break;
            case "银发一族":
                statistics.setElderlyGroup(statistics.getElderlyGroup() + 1);
                break;
            case "都市家庭":
                statistics.setUrbanFamilyGroup(statistics.getUrbanFamilyGroup() + 1);
                break;
            case "小镇家庭":
                statistics.setTownFamilyGroup(statistics.getTownFamilyGroup() + 1);
                break;
            case "都市Z世代":
                statistics.setUrbanGenZGroup(statistics.getUrbanGenZGroup() + 1);
                break;
            case "都市中产":
                statistics.setUrbanMiddleClassGroup(statistics.getUrbanMiddleClassGroup() + 1);
                break;
            case "都市蓝领":
                statistics.setUrbanBlueCollarGroup(statistics.getUrbanBlueCollarGroup() + 1);
                break;
            case "小镇中年":
                statistics.setTownMiddleAgedGroup(statistics.getTownMiddleAgedGroup() + 1);
                break;
            case "小镇青年":
                statistics.setTownYouthGroup(statistics.getTownYouthGroup() + 1);
                break;
            case "小镇中产":
                statistics.setTownMiddleClassGroup(statistics.getTownMiddleClassGroup() + 1);
                break;
            default:
                logger.error("未知的人群类型: {}", groupType); break;
        }
    }



    /**
     * 客单价数据统计
     */
    private void calculateUnitPriceData(UserPortraitLabel label, List<BigDecimal> unitPrices,
                                        UserPortraitStatistics statistics) {
        String unitPriceStr = label.getOrdLast3mKdj();
        if (unitPriceStr != null) {
            try {
                BigDecimal unitPrice = new BigDecimal(unitPriceStr);
                unitPrices.add(unitPrice);

                // 假设高客单价标准为大于等于1000元
                if (unitPrice.compareTo(new BigDecimal("1000")) >= 0) {
                    statistics.setHighUnitPriceUsers(statistics.getHighUnitPriceUsers() + 1);
                }
            } catch (NumberFormatException e) {
                logger.debug("无法解析客单价: {}", unitPriceStr);
            }
        }
    }

    /**
     * 地区分布统计
     */
    private void calculateRegionDistribution(UserPortraitLabel label, Map<String, Integer> regionMap) {
        Integer provinceCode = label.getL01Provincecode1108();
        Integer cityCode = label.getL01Citycode1107();
        Integer areaCode = label.getL01Areacode1106();

        if (provinceCode != null) {
            String key = "province_" + provinceCode;
            regionMap.put(key, regionMap.getOrDefault(key, 0) + 1);
        }

        if (cityCode != null) {
            String key = "city_" + cityCode;
            regionMap.put(key, regionMap.getOrDefault(key, 0) + 1);
        }

        if (areaCode != null) {
            String key = "area_" + areaCode;
            regionMap.put(key, regionMap.getOrDefault(key, 0) + 1);
        }
    }


    /**
     * 设置地区分布JSON
     */
    private void setRegionDistributionJson(UserPortraitStatistics statistics, Map<String, Integer> regionMap) {
        if (!regionMap.isEmpty()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                String regionJson = objectMapper.writeValueAsString(regionMap);
                statistics.setRegionDistribution(regionJson);
            } catch (Exception e) {
                logger.warn("转换地区分布为JSON失败", e);
            }
        }
    }

    /**
     * 计算平均客单价
     */
    private void calculateAverageUnitPrice(UserPortraitStatistics statistics, List<BigDecimal> unitPrices) {
        if (!unitPrices.isEmpty()) {
            BigDecimal sum = unitPrices.stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal average = sum.divide(new BigDecimal(unitPrices.size()), 2, RoundingMode.HALF_UP);
            statistics.setAvgCustomerUnitPrice(average);
        }
    }


    /**
     * 日期转换工具方法
     */
    private LocalDate convertToLocalDate(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }






}
