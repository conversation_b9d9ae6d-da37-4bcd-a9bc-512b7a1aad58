package com.pes.jd.business.impl;


import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.SgBussiness;
import com.pes.jd.model.BO.LoginResultBO;
import com.pes.jd.model.DTO.SgShopDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Param.LoginUserParam;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import java.util.List;


/**
 * 
 *  微服务入口   登录信息
 *
 */

@Service
public class SgBussinessImpl implements SgBussiness {
	
	private Logger logger = LoggerFactory.getLogger(SgBussinessImpl.class);

	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;

	@Override
	public LoginResultBO sgLogin(LoginUserParam userParam) throws Exception {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("userParam", userParam)
    			.toRequestEntity();

		LoginResultBO  loginResultBO = null;
		try {
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			RestResponseTypeRef<LoginResultBO> res = usermgrRestTemplate.postRest(serviceId,"/login/sgLogin", body, new ParameterizedTypeReference<RestResponseTypeRef<LoginResultBO>>() {
			});
			if(res.getSuccess())
				loginResultBO =  res.getData();
		} catch (HttpClientErrorException e) {
			logger.error("login/sgLogin error {}",e.getMessage(),e);
			throw e;
		}
		
		return loginResultBO;
	}

	@Override
	public List<SgShopDTO> queryPopShopInfoLst(){
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopType", 0)
				.toRequestEntity();

		List<SgShopDTO> list = Lists.newArrayList();
		try {
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			RestResponseTypeRef<List<SgShopDTO>> res = usermgrRestTemplate.postRest(serviceId,"/shop/queryShopInfoLst", body, new ParameterizedTypeReference<RestResponseTypeRef<List<SgShopDTO>>>() {
			});

			if(res.getSuccess() && CollUtil.isNotEmpty(res.getData()))
				list.addAll(res.getData());
		} catch (HttpClientErrorException e) {
			logger.error("login/queryPopShopInfoLst error {}",e.getMessage(),e);
			throw e;
		}

		return list;
	}

	@Override
	public List<SgShopDTO> querySelfShopInfoLst(){
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopType", 1)
				.toRequestEntity();

		List<SgShopDTO> list = Lists.newArrayList();
		try {
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			RestResponseTypeRef<List<SgShopDTO>> res = usermgrRestTemplate.postRest(serviceId,"/shop/queryShopInfoLst", body, new ParameterizedTypeReference<RestResponseTypeRef<List<SgShopDTO>>>() {
			});

			if(res.getSuccess() && CollUtil.isNotEmpty(res.getData()))
				list.addAll(res.getData());
		} catch (HttpClientErrorException e) {
			logger.error("login/querySelfShopInfoLst error {}",e.getMessage(),e);
			throw e;
		}

		return list;
	}

	@Override
	public Boolean checkAuthentication(ShopDTO shop, String userPin) {

		logger.info("shop=【{}】-[{}]-->userPin=[{}] is checkAuthentication start", shop.getTitle(), shop.getShopId(), userPin);
		Boolean isAuthentication = Boolean.FALSE;

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("userPin", userPin)
				.put("sessionKey", shop.getSessionKey())
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDb(),ApplicationServiceNameEnum.POP_SERVICE_SUB.getName());
		RestResponseTypeRef<Boolean> restResponse = popSubRestTemplate.postRest(serviceId,"/sg/checkAuthentication", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {});
		if (restResponse.getSuccess()) {
			isAuthentication = restResponse.getData();
		}else {
			logger.error("shop=【{}】-[{}]-->userPin=[{}] is checkAuthentication end result=[{}]", shop.getTitle(), shop.getShopId(), userPin,restResponse.getRpMsg());
		}
		logger.info("shop=【{}】-[{}]-->userPin=[{}] is checkAuthentication end result=[{}]", shop.getTitle(), shop.getShopId(), userPin,isAuthentication);
		return isAuthentication;
	}

}
