package com.pes.jd.business.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.KnowledgVolcengine;
import com.volcengine.auth.ISignerV4;
import com.volcengine.auth.impl.SignerV4Impl;
import com.volcengine.model.Credentials;
import com.volcengine.service.SignableRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class KnowledgVolcengineImpl implements KnowledgVolcengine {

    String ak = "AKLTY2MzMDA4ZjYwOGM4NDIxNThlMTM2OTMyNjQwYWU1MWQ";
    String sk = "WXpCa01tTTNOVFk1TURobE5HTTBORGxtT1RSbE1EZzBZMlJqWm1Ka01EWQ==";
    private final String HOST = "api-knowledgebase.mlp.cn-beijing.volces.com";

    @Override
    public  SignableRequest prepareRequest(String host, String path, String method, List<NameValuePair> params, String body) {
        SignableRequest request = new SignableRequest();
        request.setMethod(method);
        request.setHeader("Accept", "application/json");
        request.setHeader("Content-Type", "application/json");
        request.setHeader("Host", host);
        request.setEntity(new StringEntity(body, "utf-8"));

        URIBuilder builder = request.getUriBuilder();
        builder.setScheme("https");
        builder.setHost(host);
        builder.setPath(path);
        if (params != null) {
            builder.setParameters(params);
        }

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(2000).build();
        request.setConfig(requestConfig);

        Credentials credentials = new Credentials("cn-north-1", "air");
        credentials.setAccessKeyID(ak);
        credentials.setSecretAccessKey(sk);
        ISignerV4 signer = new SignerV4Impl();

        try {
            signer.sign(request, credentials);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return request;
    }



    /**
     * 执行HTTP请求并解析响应
     */
    private Map<String, Object> executeRequest(SignableRequest signableRequest) {
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(signableRequest.getURI());

            // 设置请求头
            for (org.apache.http.Header header : signableRequest.getAllHeaders()) {
                httpPost.setHeader(header.getName(), header.getValue());
            }

            // 设置请求体
            httpPost.setEntity(signableRequest.getEntity());

            // 执行请求
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String responseBody = EntityUtils.toString(entity, "UTF-8");

            // 解析响应
            return JSON.parseObject(responseBody);
        } catch (IOException e) {
            throw new RuntimeException("Error during API call: " + e.getMessage(), e);
        }
    }


    @Override
    public Map<String, Object> searchKnowledge(String collectionName, String query, String docId, double denseWeight, int limit, String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            projectName = "default";
        }

        // 构建请求参数
        Map<String, Object> queryParam = new HashMap<>();
        Map<String, Object> docFilter = new HashMap<>();
        docFilter.put("op", "must");
        docFilter.put("field", "doc_id");
        docFilter.put("conds", ListUtil.of(docId));                    //???????????????????
        queryParam.put("doc_filter", docFilter);

        Map<String, Object> payload = new HashMap<>();
        payload.put("name", collectionName);
        payload.put("project", projectName);
        payload.put("query", query);
        payload.put("limit", limit);
        payload.put("dense_weight", denseWeight);
        payload.put("query_param", queryParam);

        // 准备请求
        String path = "/api/knowledge/collection/search_knowledge";
        String body = JSON.toJSONString(payload);
        SignableRequest request = prepareRequest(HOST, path, "POST", null, body);

        // 执行请求
        return executeRequest(request);
    }

    @Override
    public Map<String, Object> addPoint(String collectionName, String docId, String key, String value, String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            projectName = "default";
        }

        // 构建字段列表
        List<Map<String, String>> fields = new ArrayList<>();
        Map<String, String> keyField = new HashMap<>();
        keyField.put("field_name", "key");
        keyField.put("field_value", key);
        fields.add(keyField);

        Map<String, String> valueField = new HashMap<>();
        valueField.put("field_name", "value");
        valueField.put("field_value", value);
        fields.add(valueField);

        // 构建请求参数
        Map<String, Object> payload = new HashMap<>();
        payload.put("collection_name", collectionName);
        payload.put("project", projectName);
        payload.put("doc_id", docId);
        payload.put("chunk_type", "structured");
        payload.put("fields", fields);

        // 准备请求
        String path = "/api/knowledge/point/add";
        String body = JSON.toJSONString(payload);
        SignableRequest request = prepareRequest(HOST, path, "POST", null, body);

        // 执行请求
        return executeRequest(request);
    }

    @Override
    public Map<String, Object> addPointsBatch(String collectionName, List<Map<String, Object>> points, String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            projectName = "default";
        }

        // 构建请求参数
        Map<String, Object> payload = new HashMap<>();
        payload.put("collection_name", collectionName);
        payload.put("project", projectName);
        payload.put("points", points);

        // 准备请求
        String path = "/api/knowledge/point/batch_add";
        String body = JSON.toJSONString(payload);
        SignableRequest request = prepareRequest(HOST, path, "POST", null, body);

        // 执行请求
        return executeRequest(request);
    }

    @Override
    public Map<String, Object> listPoints(String collectionName, int offset, int limit, List<String> docIds, String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            projectName = "default";
        }

        // 构建请求参数
        Map<String, Object> payload = new HashMap<>();
        payload.put("collection_name", collectionName);
        payload.put("project", projectName);
        payload.put("offset", offset);
        payload.put("limit", limit);

        // 如果提供了docIds，添加到请求中
        if (docIds != null && !docIds.isEmpty()) {
            payload.put("doc_ids", docIds);
        }

        // 准备请求
        String path = "/api/knowledge/point/list";
        String body = JSON.toJSONString(payload);
        SignableRequest request = prepareRequest(HOST, path, "POST", null, body);

        // 执行请求
        return executeRequest(request);
    }

    @Override
    public Map<String, Object> listDocs(String collectionName, String docName, int offset, int limit, List<String> docType, String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            projectName = "default";
        }

        // 构建请求参数
        Map<String, Object> payload = new HashMap<>();
        payload.put("collection_name", collectionName);
        payload.put("project", projectName);
        payload.put("offset", offset);
        payload.put("limit", limit);

        // 如果提供了docType，添加到请求中
        if (docType != null && !docType.isEmpty()) {
            payload.put("doc_type", docType);
        }

        // 准备请求
        String path = "/api/knowledge/doc/list";
        String body = JSON.toJSONString(payload);
        SignableRequest request = prepareRequest(HOST, path, "POST", null, body);

        // 执行请求
        return executeRequest(request);
    }


    @Override
    public String findDocIdByName(String collectionName, String docName, String projectName) {
        if (projectName == null || projectName.isEmpty()) {
            projectName = "default";
        }
        docName = docName+".jsonl";

        Map<String, Object> result = listDocs(collectionName, null, 0, 100, null, projectName);

        // 检查响应是否包含错误代码
        if (result.containsKey("code") && ((Number)result.get("code")).intValue() != 0) {
            return null;
        }

        // 从响应中获取文档列表
        JSONObject data = (JSONObject) result.get("data");
        if (data != null) {
            List<Map<String, Object>> docList = (List<Map<String, Object>>) data.get("doc_list");
            if (docList != null) {
                for (Map<String, Object> doc : docList) {
                    if (docName.equals(doc.get("doc_name"))) {
                        return (String) doc.get("doc_id");
                    }
                }
            }
        }

        return null;  // 如果未找到匹配项，返回null
    }




}
