package com.pes.jd.business.impl;


import com.pes.jd.model.Query.JobShopQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
//店铺经营分类描述
public class BusinessCategoryBusinessImpl {

    //生成描述
    public void generateDescribe(JobShopQuery jobShop) {
        String businessCategory = jobShop.getShop().getBusinessCategory();
        if(StringUtils.isBlank(businessCategory)){
            //查询出商品表里的300条商品名称

            //询问ai,总结分类描述

            //存档 并且塞入jobshop
        }

    }
}
