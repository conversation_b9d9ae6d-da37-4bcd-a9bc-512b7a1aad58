package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.dao.sub.ShopGoodsDao;
import com.pes.jd.data.api.RemoteService;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodNameDTO;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
//店铺经营分类描述
public class BusinessCategoryBusinessImpl {

    private static final Logger logger = LoggerFactory.getLogger(BusinessCategoryBusinessImpl.class);

    @Autowired
    private ShopGoodsDao shopGoodsDao;

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    //生成描述
    public void generateDescribe(JobShopQuery jobShop) {
        String businessCategory = jobShop.getShop().getBusinessCategory();
        if(StringUtils.isBlank(businessCategory)){
            logger.info("店铺 {} 的经营分类描述为空，开始生成分类描述", jobShop.getShop().getShopId());

            try {
                //查询出商品表里的300条商品名称,商品名称要塞入set提前去下重复.
                Set<String> productNames = getProductNamesFromShop(jobShop.getShop());

                if (productNames.isEmpty()) {
                    logger.warn("店铺 {} 没有商品数据，无法生成分类描述", jobShop.getShop().getShopId());
                    return;
                }

                //询问ai,总结分类描述
                String categoryDescription = generateCategoryDescriptionByAI(productNames);

                if (StringUtils.isNotBlank(categoryDescription)) {
                    //更新shop表 并且塞入jobshop
                    updateShopBusinessCategory(jobShop, categoryDescription);
                    logger.info("成功为店铺 {} 生成并更新经营分类描述: {}", jobShop.getShop().getShopId(), categoryDescription);
                } else {
                    logger.warn("AI生成的分类描述为空，店铺ID: {}", jobShop.getShop().getShopId());
                }

            } catch (Exception e) {
                logger.error("生成店铺分类描述失败，店铺ID: {}", jobShop.getShop().getShopId(), e);
            }
        } else {
            logger.info("店铺 {} 已有经营分类描述: {}", jobShop.getShop().getShopId(), businessCategory);
        }
    }
