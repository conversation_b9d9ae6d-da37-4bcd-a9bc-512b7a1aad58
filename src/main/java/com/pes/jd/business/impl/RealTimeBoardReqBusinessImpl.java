package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.RealTimeBoardReBusiness;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Param.SubscribeParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DeptShopVO;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.DateUtils;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class RealTimeBoardReqBusinessImpl implements RealTimeBoardReBusiness {

	private static final Logger logger = LoggerFactory.getLogger(RealtimeBoardBussinessImpl.class);

	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;

	@Autowired
	private PopRtSubRestTemplate popRtSubRestTemplate;
	
	@Override
	public ApiResponse selectSubScribe(SubscribeParam param) {
		ApiResponse apiResponse = null;
		
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/selectShopSubscribeInfoForRt", body);
		return apiResponse;

	}

	@Override
	public ApiResponse selectSale(String dbName,List<ShopDTO> shops){
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RestOperator.getJsonEntity(shops);
		try {
			String serviceId = RestOperator.getMSServiceId(dbName, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			apiResponse = popRtSubRestTemplate.postRest(serviceId, "/realTimeBoard/selectSale", body);
		} catch (Exception e) {
			logger.error("select sale error postRest{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

    @Override
    public ApiResponse selectSaleInfo(String dbName, List<ShopDTO> shops, String shopParam, Date date, Date designation) {
        String uri = RestOperator.mergeUriArguments
                ("/realTimeBoard/search_sale_info","shopParam", shopParam, "date", DateUtils.formatYMd(date), "designation", DateUtils.formatYMd(designation));
        String serviceId = RestOperator.getMSServiceId(dbName, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
        return popRtSubRestTemplate.postRest(serviceId, uri, RestOperator.getJsonEntity(shops));
    }

	/**
	 * @description 重写查询rt_sub服务端,店铺数据接口
	 * <AUTHOR>
	 * @date 2019/10/22 13:34
	 * @param
	 * @return
	 */
	@Override
	public ApiResponse  selectSaleInfoNew(String dbName,  List<ShopDTO> dtos,  Date date, Date designation){
		String uri = RestOperator.mergeUriArguments
				("/realTimeBoard/selectSaleInfoNew", "date", DateUtils.formatYMdHms(date), "designation", DateUtils.formatYMd(designation), "detail","false");
		String serviceId = RestOperator.getMSServiceId(dbName, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, uri, RestOperator.getJsonEntity(dtos));
	}

	@Override
	public ApiResponse selectSaleInfoNew3(String dbName, List<ShopDTO> dtos, Date date) {
		String uri = RestOperator.mergeUriArguments
				("/realTimeBoard/selectSaleInfo2", "date", DateUtils.formatYMdHms(date));
		String serviceId = RestOperator.getMSServiceId(dbName, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, uri, RestOperator.getJsonEntity(dtos));
	}

	@Override
	public ApiResponse selectService(String dbName,List<ShopDTO> shops){
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RestOperator.getJsonEntity(shops);
		try {
			String serviceId = RestOperator.getMSServiceId(dbName, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			apiResponse = popRtSubRestTemplate.postRest(serviceId, "/realTimeBoard/selectService", body);
		} catch (Exception e) {
			logger.error("select service error postRest{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectServiceDetailByShop(String dbName, List<DeptShopVO> shopVOS) throws Exception {
		List<ShopDTO> shops =  new ArrayList<>();
		if(!shopVOS.isEmpty()){
			for(DeptShopVO vo : shopVOS){
				ShopDTO dto = new ShopDTO();
				BeanUtils.copyProperties(vo, dto);
				shops.add(dto);
			}
		}

		HttpEntity<Object> body = RestOperator.getJsonEntity(shops);
		try {
			String serviceId = RestOperator.getMSServiceId(dbName, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			return popRtSubRestTemplate.postRest(serviceId, "/realTimeBoard/selectServiceDetailByShop", body);
		} catch (Exception e) {
			logger.error("select service error postRest{}",e.getMessage(),e);
			throw e;
		}
	}

}
