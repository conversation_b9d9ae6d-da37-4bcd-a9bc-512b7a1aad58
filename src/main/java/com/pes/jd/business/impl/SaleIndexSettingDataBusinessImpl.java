package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.pes.jd.business.SaleIndexSettingBusiness;
import com.pes.jd.business.SaleIndexSettingDataBusiness;
import com.pes.jd.model.DO.SaleIndexSettingDO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.SaleIndexSettingParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.JacksonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class SaleIndexSettingDataBusinessImpl implements SaleIndexSettingDataBusiness {

	@Autowired
	private SaleIndexSettingBusiness saleIndexSettingBusiness;
	
	@Override
	public SaleIndexSettingDO searchSaleIndexSetting(ShopQuery shop, Date date) throws Exception{
		ApiResponse apiResponse = saleIndexSettingBusiness.selectSaleIndexSetting(shop, date);
		SaleIndexSettingDO saleIndex = null;
		
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			saleIndex = JacksonUtils.json2pojo(JSON.toJSONString(apiResponse.getData().get("saleIndex")), SaleIndexSettingDO.class);
		}else{
			saleIndex = new SaleIndexSettingDO();
			saleIndex.setCsSaleTarget(0.0);
			saleIndex.setShopSaleTarget(0.0);
		}
		return saleIndex;
	}

	@Override
	public ApiResponse insertSaleIndexSetting(ShopQuery shop, SaleIndexSettingParam param) throws Exception{
		return  saleIndexSettingBusiness.insertSaleIndexSetting(shop, param);

	}

}
