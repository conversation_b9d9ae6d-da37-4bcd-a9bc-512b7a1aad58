package com.pes.jd.business.impl;

import com.pes.jd.business.ShopPerformanceBusiness;
import com.pes.jd.business.ShopSpecialSummaryPerformanceBusiness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 店铺绩效 - 专项报表
 * 不允许发Rest请求 ,业务包装处理类
 */
@Service
public class ShopSpecialSummaryPerformanceBusinessImpl implements ShopSpecialSummaryPerformanceBusiness {
	
	@Autowired
	private ShopPerformanceBusiness shopPerformanceBusiness;
}
