package com.pes.jd.business.impl;

import com.pes.jd.business.CsCustRecommendConsultSkuBusiness;
import com.pes.jd.mapper.CsCustRecommendConsultSkuMapper;
import com.pes.jd.model.DO.CsCustRecommendConsultSkuDO;
import com.pes.jd.model.DTO.CsCustRecommendConsultSkuDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @Author: aiJun
 * @Date: 2019-07-29 17:17
 * @Version 1.0
 */
@Service
public class CsCustRecommendConsultSkuBusinessImpl implements CsCustRecommendConsultSkuBusiness {
    @Autowired
    private CsCustRecommendConsultSkuMapper csCustRecommendConsultSkuMapper;
    @Override
    public int deleteByPrimaryKey(Long id) {
        return csCustRecommendConsultSkuMapper.deleteCsCustRecommendConsultSku(id);
    }

    @Override
    public int insert(CsCustRecommendConsultSkuDO record) {
        return csCustRecommendConsultSkuMapper.insertCsCustRecommendConsultSku(record);
    }


    @Override
    public CsCustRecommendConsultSkuDO selectByPrimaryKey(Long id) {
        return csCustRecommendConsultSkuMapper.selectCsCustRecommendConsultSku(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CsCustRecommendConsultSkuDO record) {
        return csCustRecommendConsultSkuMapper.updateCsCustRecommendConsultSku(record);
    }

    @Override
    public List<CsCustRecommendConsultSkuDTO> selectLstByBuyerNick(JobShopDTO shop, Collection<String> enquiryLossCustSet, Date date) {
        if (CollectionUtils.isEmpty(enquiryLossCustSet)) {
            return new ArrayList<>();
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CUST_RECOMMEND_CONSULT_SKU.getName());
        return csCustRecommendConsultSkuMapper.selectLstByBuyerNick(tableName,shop.getShopId(),enquiryLossCustSet,date);
    }

}
