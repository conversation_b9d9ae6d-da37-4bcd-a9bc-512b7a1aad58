package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.business.CsTypeDayBusiness;
import com.pes.jd.business.PerformanceRuleBusiness;
import com.pes.jd.dao.CsTypeDayDao;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.CsTypeDayDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CsTypeDayBusinessImpl implements CsTypeDayBusiness {

    @Resource
    private CsTypeDayDao csTypeDayDao;

    @Resource
    private PerformanceRuleBusiness performanceRuleBusiness;

    @Override
    public void handleCsTypeDay(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        List<CsTypeDayDTO> csTypeDayLst = new ArrayList<>();
        //获取存在的历史数据
        List<CsTypeDayDTO> csTypeDayDTOS = csTypeDayDao.selectCsLockStatusByShopByDate(jobShop.getShop(), jobDate.getDate());
        Map<String, CsTypeDayDTO> dtoMap =Optional.ofNullable(csTypeDayDTOS).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CsTypeDayDTO::getCsNick, dto -> dto, (v1, v2) -> v2));
        // 记录每日客服状态 并且给当日客服状态赋值
        for (CsDTO dto : jobShop.getCsLst()) {
            CsTypeDayDTO csTypeDay = new CsTypeDayDTO();
            csTypeDay.setShopId(dto.getShopId());
            csTypeDay.setDate(jobDate.getDate());
            csTypeDay.setCsNick(dto.getNick());
            //客服锁定
            if (dto.getLockTime() == null
                    || DateUtil.getStartTimeOfDate(dto.getLockTime()).getTime() > DateUtil.getStartTimeOfDate(jobDate.getDate()).getTime())
                csTypeDay.setIsLock(false);
            else csTypeDay.setIsLock(true);
            CsTypeDayDTO csTypeDayDTO =Optional.ofNullable(dtoMap).orElse(new HashMap<>()).get(dto.getNick());
            if(csTypeDayDTO == null){
                csTypeDay.setCsType(dto.getType());
                csTypeDayLst.add(csTypeDay);
                continue;
            }
            //将当日客服的状态赋值给客服
            dto.setType(csTypeDayDTO.getCsType());
            dto.setCsStatus(Optional.ofNullable(csTypeDayDTO.getIsLock()).orElse(false) == true ? (byte) 2 : (byte) 1);
            //给历史数据赋值
            csTypeDay.setCsType(csTypeDayDTO.getCsType());
            csTypeDayLst.add(csTypeDay);
        }
        csTypeDayDao.deleteByShopIdAndDate(jobShop.getShop(), jobDate.getDate(), jobDate.getStartDate(), jobDate.getEndDate());
        csTypeDayDao.batchInsertCsTypeDay(jobShop.getShop(), jobDate.getDate(), csTypeDayLst);
        //查询指定日期客服锁定详情
        getCsLockStatus(jobShop, jobDate);
    }


    private void getCsLockStatus(JobShopQuery jobShop, JobDateQuery jobDate) {

        PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
        Set<Date> dateSet = Sets.newHashSet();
        dateSet.add(validDate.getDate());

        //询单-下单
        dateSet.add(validDate.getEnquiry2OrderedValidDate());
        //询单-付款
        dateSet.add(validDate.getEnquiry2PayValidDate());
        //询单-出库
        dateSet.add(validDate.getEnquiry2OutstackValidDate());
        /*
         *  以今天是19号为例-算昨日18号数据
         *  询单有效时长是3天；下单-出库有效时长是4天
         *
         *  (询单的客户下了单，以下单时间为准的组成下单维度)-计算最终数据
         *	下单-付款			往前推【付款有效时长1天】 - 算17号
         *	下单-出库			往前推【出库有效时长】 - 算14号
         *	下单-确认收货		往前推【出库有效时长】 + 【确认收货有效时长】 - 算7号
         */
        //下单-付款
        dateSet.add(validDate.getToOrderedThenPayValidDate());
        //下单-出库
        dateSet.add(validDate.getToOrderedThenOutstackValidDate());
//        fix:489
        dateSet.addAll(jobDate.getDates());
        List<Date> dateLst = Lists.newArrayList(dateSet);
        Map<Date, List<CsTypeDayDTO>> csTypeDayMap = selectCsLockStatusByShopByDate(jobShop.getShop(), dateLst);
        jobDate.setCsTypeDayMap(csTypeDayMap);
    }

    @Override
    public Map<Date, List<CsTypeDayDTO>> selectCsLockStatusByShopByDate(JobShopDTO shop, List<Date> dateLst) {
        Map<Date, List<CsTypeDayDTO>> csTypeDayMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(dateLst)) {
            return csTypeDayMap;
        }
        for (Date date : dateLst) {
            csTypeDayMap.put(date, csTypeDayDao.selectCsLockStatusByShopByDate(shop, date));
        }
        return csTypeDayMap;
    }
}
