package com.pes.jd.business.impl;

import com.pes.jd.business.ShopAccountBussiness;
import com.pes.jd.business.ShopAccountHandleBussiness;
import com.pes.jd.data.api.SubUserOperator;
import com.pes.jd.model.BO.DataPreparedStatus;
import com.pes.jd.model.DTO.ShopAccountDTO;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.TO.SubUserGetTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ShopAccountHandleBussinessImpl implements ShopAccountHandleBussiness{
	
	private Logger logger = LoggerFactory.getLogger(ShopAccountHandleBussinessImpl.class);
    @Resource
	private SubUserOperator subUserOperator;
	
    @Resource
	private ShopAccountBussiness shopAccountBussiness;
	
	@Override
	public void pullShopShopAccount(JobShopQuery jobShop) throws Exception {
		
		long s = System.currentTimeMillis();
		
		DataPreparedStatus result = new DataPreparedStatus();
		int effectedRows = 0;
		result.setShopId(jobShop.getShop().getShopId() + "");
		result.setShopTitle(jobShop.getShop().getTitle());
		result.setDataType("subuser");
		SubUserGetTO subUserGetTo = subUserOperator.getVenderSubUsers(jobShop.getShop().getUserId(), jobShop.getShop().getShopId(),  jobShop.getShop().getSessionKey());
		List<ShopAccountDTO> subUsers = subUserGetTo.getCustSubUsers();
		result.setApiNum(subUserGetTo.getNum());
		result.setApiRetryNum(subUserGetTo.getRetryNum());
		try {
			//TODO  服务助手还不确定
			effectedRows = shopAccountBussiness.insertShopAccountOfShop(subUsers, jobShop.getShop().getShopId());
		} catch (Exception e) {
			logger.error("pull shop ShopAccount error", e);
			result.setPrepareStatus("failure");
			throw e;
		}
		result.setPrepareStatus("success");
		result.setEffectedRowsNum(effectedRows);
		
		long e = System.currentTimeMillis();
		logger.info("pull shop ShopAccount end,time:{}",(e-s)/1000);
	}

}
