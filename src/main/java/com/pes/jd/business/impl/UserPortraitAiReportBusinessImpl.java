package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.business.UserPortraitAiReportBusiness;
import com.pes.jd.dao.UserPortraitAiReportDao;
import com.pes.jd.data.api.RemoteService;
import com.pes.jd.model.DO.UserPortraitAiReport;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.RegionInfoDTO;
import com.pes.jd.model.DTO.UserPortraitStatisticsDTO;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.Result.RegionDistributionResult;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserPortraitAiReportBusinessImpl implements UserPortraitAiReportBusiness {

    private static final Logger logger = LoggerFactory.getLogger(UserPortraitAiReportBusinessImpl.class);

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Autowired
    private UserPortraitAiReportDao userPortraitAiReportDao;

    @Autowired
    private RemoteService remoteService;

    @Autowired
    private ObjectMapper objectMapper;

    // 系统提示词模板
    private static String systemPromptTemplate;

    // 图表类型映射
    private static final Map<Integer, String> CHART_TYPE_NAMES = new HashMap<>();
    static {
        CHART_TYPE_NAMES.put(1, "年龄分布");
        CHART_TYPE_NAMES.put(2, "性别分布");
        CHART_TYPE_NAMES.put(3, "婚姻状况");
        CHART_TYPE_NAMES.put(4, "职业分布");
        CHART_TYPE_NAMES.put(5, "用户群体类型");
        CHART_TYPE_NAMES.put(6, "孩子数量");
        CHART_TYPE_NAMES.put(7, "所在地区信息");
        CHART_TYPE_NAMES.put(8, "大促预售购买敏感人群占比");
        CHART_TYPE_NAMES.put(9, "平台促销敏感人群占比");
        CHART_TYPE_NAMES.put(10, "大促高消费金额人群");
        CHART_TYPE_NAMES.put(11, "新品偏好人群占比");

        // 加载系统提示词模板
        try {
            systemPromptTemplate = StreamUtils.copyToString(
                    new ClassPathResource("user_portrait_system_prompt.txt").getInputStream(),
                    StandardCharsets.UTF_8
            );
        } catch (IOException e) {
            logger.error("Failed to load user_portrait_system_prompt.txt", e);
            systemPromptTemplate = "你是一位专业的电商数据分析师，请分析用户画像数据并提供运营建议。";
        }
    }

    @Override
    public void handlePortraitAiAnalysis(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        JobShopDTO shop = jobShop.getShop();
        String startDate = cn.hutool.core.date.DateUtil.format(jobDate.getStartDate(),"yyyy-MM-dd");
        String endDate = cn.hutool.core.date.DateUtil.format(jobDate.getEndDate(),"yyyy-MM-dd");

        logger.info("开始处理店铺 {} 的用户画像AI分析报告，日期范围：{} - {}",
                shop.getShopId(), startDate, endDate);

        try {
            // 如果需要删除已有数据
            if (isDelData) {
                LocalDate localStartDate = jobDate.getStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                LocalDate localEndDate = jobDate.getEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                userPortraitAiReportDao.deleteUserPortraitAiReportByShopIdAndDateRange(
                        shop.getShopId(), localStartDate, localEndDate, shop.getSchemaId());
                logger.info("已删除店铺 {} 在日期范围 {} - {} 的AI报告数据", shop.getShopId(), localStartDate, localEndDate);
            }

            // 1. 远程调用获取用户画像统计数据 (10张图表)
            UserPortraitStatisticsDTO portraitStatistics = getPortraitStatisticsFromRemote(shop, startDate, endDate);

            // 2. 远程调用获取地区分布数据 (第11张图表)
            RegionDistributionResult regionDistribution = getRegionDistributionFromRemote(shop, startDate, endDate);

            if (portraitStatistics == null || regionDistribution == null) {
                logger.warn("店铺 {} 在日期范围 {} - {} 没有用户画像统计数据", shop.getShopId(), startDate, endDate);
                return;
            }

            // 获取店铺主营类目
            String mainCategory = getShopMainCategory(shop);

            // 3. 拆分成11个部分，各部分喂给AI并保存
            generateAiReportsForAllCharts(shop, portraitStatistics, regionDistribution, mainCategory, jobDate.getStartDate());

            logger.info("完成店铺 {} 的用户画像AI分析报告生成", shop.getShopId());

        } catch (Exception e) {
            logger.error("处理用户画像AI分析报告失败", e);
            throw new RuntimeException("处理用户画像AI分析报告失败", e);
        }
    }

    /**
     * 通过远程调用获取用户画像统计数据
     *
     */
    private UserPortraitStatisticsDTO getPortraitStatisticsFromRemote(JobShopDTO shop, String startDate, String endDate) {
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("shopId", shop.getShopId());
            requestMap.put("schemaId", shop.getSchemaId());
            requestMap.put("dbName", shop.getDb());
            requestMap.put("startDate", startDate);
            requestMap.put("endDate", endDate);

            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);

            RestResponseTypeRef<Object> response = usermgrRestTemplate.postRest(
                    serviceId,
                    "/userPortraitStatistics/queryPortraitStatisticsByDate",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if(response.getSuccess() && response.getData() != null){
                // 将返回的Object转换为UserPortraitStatisticsDTO
                UserPortraitStatisticsDTO result = objectMapper.convertValue(
                        response.getData(), UserPortraitStatisticsDTO.class);
                logger.info("远程调用获取用户画像统计数据成功，店铺ID: {}", shop.getShopId());
                return result;
            }

            logger.warn("远程调用获取用户画像统计数据失败，返回null");
            return null;
        } catch (Exception e) {
            logger.error("远程调用获取用户画像统计数据失败", e);
            return null;
        }
    }

    /**
     * 通过远程调用获取地区分布数据
     *
     */
    private RegionDistributionResult getRegionDistributionFromRemote(JobShopDTO shop, String startDate, String endDate) {
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("shopId", shop.getShopId());
            requestMap.put("schemaId", shop.getSchemaId());
            requestMap.put("dbName", shop.getDb());
            requestMap.put("startDate", startDate);
            requestMap.put("endDate", endDate);

            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);

            RestResponseTypeRef<Object> response = usermgrRestTemplate.postRest(
                    serviceId,
                    "/userPortraitStatistics/queryRegionDistributionByDate",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if(response.getSuccess() && response.getData() != null){
                // 将返回的Object转换为RegionDistributionResult
                RegionDistributionResult result = objectMapper.convertValue(
                        response.getData(), RegionDistributionResult.class);
                logger.info("远程调用获取地区分布数据成功，店铺ID: {}", shop.getShopId());
                return result;
            }
            logger.warn("远程调用获取地区分布数据失败，返回null");
            return null;
        } catch (Exception e) {
            logger.error("远程调用获取地区分布数据失败", e);
            return null;
        }
    }

    /**
     * 为所有图表类型生成AI分析报告
     */
    private void generateAiReportsForAllCharts(JobShopDTO shop, UserPortraitStatisticsDTO portraitStatistics,
                                               RegionDistributionResult regionDistribution, String mainCategory,
                                               Date reportDate) throws Exception {
        LocalDate localReportDate = reportDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 生成前10个图表的AI报告（基于UserPortraitStatisticsDTO）
        for (int chartType = 1; chartType <= 10; chartType++) {
            try {
                generateAiReportForChart(shop, portraitStatistics, null, mainCategory, chartType, localReportDate);
            } catch (Exception e) {
                logger.error("生成图表类型 {} 的AI报告失败：{}", chartType, e.getMessage(), e);
                // 继续处理其他图表类型
            }
        }

        // 生成第11个图表的AI报告（地区分布）
        try {
            generateAiReportForChart(shop, null, regionDistribution, mainCategory, 11, localReportDate);
        } catch (Exception e) {
            logger.error("生成地区分布图表的AI报告失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 为单个图表类型生成AI分析报告
     */
    private void generateAiReportForChart(JobShopDTO shop, UserPortraitStatisticsDTO portraitStatistics,
                                          RegionDistributionResult regionDistribution, String mainCategory,
                                          int chartType, LocalDate reportDate) throws Exception {

        String chartTitle = CHART_TYPE_NAMES.get(chartType);
        List<UserPortraitStatisticsDTO.PieChartData> chartData;

        if (chartType == 11) {
            // 地区分布图表
            chartData = buildRegionChartData(regionDistribution);
        } else {
            // 其他10个图表
            chartData = buildChartData(portraitStatistics, chartType);
        }

        if (chartData == null || chartData.isEmpty()) {
            logger.warn("图表类型 {} 没有数据，跳过AI分析", chartType);
            return;
        }

        // 将图表数据转换为JSON
        String chartDataJson = objectMapper.writeValueAsString(chartData);

        // 调用AI服务生成分析报告
        String aiAnalysis = callAiForAnalysis(mainCategory, chartTitle, chartDataJson);

        if (aiAnalysis != null && !aiAnalysis.trim().isEmpty()) {
            // 保存AI分析报告
            saveAiReport(shop, chartType, chartTitle, aiAnalysis, reportDate);
            logger.info("成功生成并保存图表类型 {} ({}) 的AI分析报告", chartType, chartTitle);
        } else {
            logger.warn("图表类型 {} ({}) 的AI分析结果为空", chartType, chartTitle);
        }
    }

    /**
     * 调用AI服务进行分析
     */
    private String callAiForAnalysis(String mainCategory, String chartTitle, String chartDataJson) {
        try {
            // 使用系统提示词模板构建提示词
            String systemPrompt = systemPromptTemplate
                    .replace("{main_category}", mainCategory)
                    .replace("{chart_title}", chartTitle)
                    .replace("{chart_data}", chartDataJson);

            // 构建用户消息
            String userMessage = String.format(
                "请分析以下用户画像数据：\n主营类目：%s\n图表标题：%s\n数据：%s",
                mainCategory, chartTitle, chartDataJson
            );

            // 调用AI服务
            String aiResponse = remoteService.callAiAnalysis(systemPrompt, userMessage);

            if (aiResponse != null) {
                // 解析AI响应
                JSONObject jsonResponse = JSONObject.parseObject(aiResponse);
                if (jsonResponse != null && jsonResponse.containsKey("choices")) {
                    return jsonResponse.getJSONArray("choices")
                            .getJSONObject(0)
                            .getJSONObject("message")
                            .getString("content");
                }
            }

            return null;
        } catch (Exception e) {
            logger.error("调用AI服务分析失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存AI分析报告
     */
    private void saveAiReport(JobShopDTO shop, int chartType, String chartTitle,
                              String aiAnalysis, LocalDate reportDate) throws Exception {
        UserPortraitAiReport report = new UserPortraitAiReport();
        report.setShopId(shop.getShopId());
        report.setReportDate(reportDate);
        report.setReportType(1); // 1-日报
        report.setChartType(chartType);
        report.setReportContent(aiAnalysis);
        report.setCreatedTime(LocalDateTime.now());

        userPortraitAiReportDao.insertUserPortraitAiReport(report, shop.getSchemaId());
    }

    /**
     * 获取店铺主营类目
     */
    private String getShopMainCategory(JobShopDTO shop) {



        return "电子产品";
    }

    /**
     * 根据图表类型构建图表数据
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildChartData(UserPortraitStatisticsDTO statistics, int chartType) {
        if (statistics == null) {
            return new ArrayList<>();
        }

        switch (chartType) {
            case 1: // 年龄分布 (PieChartData)
                return statistics.getAgeDistribution();
            case 2: // 性别分布 (PieChartData)
                return statistics.getGenderDistribution();
            case 3: // 婚姻状况 (PieChartData)
                return statistics.getMarriageDistribution();
            case 4: // 职业分布 (BarChartData -> 转换为PieChartData)
                return convertBarChartToPieChart(statistics.getProfessionDistribution());
            case 5: // 用户群体类型 (BarChartData -> 转换为PieChartData)
                return convertBarChartToPieChart(statistics.getUserGroupDistribution());
            case 6: // 孩子数量 (PieChartData)
                return statistics.getChildrenDistribution();
            case 7: // 所在地区信息 (这个会在第11个图表中处理)
                return new ArrayList<>();
            case 8: // 大促预售购买敏感人群占比 (PieChartData)
                return statistics.getPresaleDistribution();
            case 9: // 平台促销敏感人群占比 (PieChartData)
                return statistics.getPromotionSensitivityDistribution();
            case 10: // 大促高消费金额人群 (PieChartData)
                return statistics.getHighConsumptionDistribution();
            case 11: // 新品偏好人群占比 (PieChartData)
                return statistics.getNewProductPreferenceDistribution();
            default:
                return new ArrayList<>();
        }
    }

    /**
     * 将BarChartData转换为PieChartData
     * 因为AI分析时统一使用PieChartData格式
     */
    private List<UserPortraitStatisticsDTO.PieChartData> convertBarChartToPieChart(List<UserPortraitStatisticsDTO.BarChartData> barChartDataList) {
        if (barChartDataList == null || barChartDataList.isEmpty()) {
            return new ArrayList<>();
        }

        return barChartDataList.stream()
                .map(barData -> new UserPortraitStatisticsDTO.PieChartData(
                        barData.getName(),
                        barData.getValue(),
                        barData.getPercentage()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 构建地区分布图表数据
     * 根据实际的RegionDistributionResult结构进行修正
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildRegionChartData(RegionDistributionResult regionDistribution) {
        if (regionDistribution == null || regionDistribution.getProvinces() == null || regionDistribution.getProvinces().isEmpty()) {
            return new ArrayList<>();
        }

        List<UserPortraitStatisticsDTO.PieChartData> chartDataList = new ArrayList<>();

        // 计算总用户数，用于计算百分比
        int totalUsers = regionDistribution.getProvinces().stream()
                .mapToInt(RegionInfoDTO::getUserCount)
                .sum();

        // 构建省级分布数据
        for (RegionInfoDTO province : regionDistribution.getProvinces()) {
            if (province.getUserCount() != null && province.getUserCount() > 0) {
                double percentage = totalUsers > 0 ?
                    (double) province.getUserCount() / totalUsers * 100 : 0.0;

                chartDataList.add(new UserPortraitStatisticsDTO.PieChartData(
                        province.getAreaName(),
                        province.getUserCount(),
                        Math.round(percentage * 100) / 100.0 // 保留两位小数
                ));
            }
        }

        // 按用户数量降序排序，便于AI分析
        chartDataList.sort((a, b) -> b.getValue().compareTo(a.getValue()));

        return chartDataList;
    }
}
