package com.pes.jd.business.impl;

import com.pes.jd.business.UserPortraitAiReportBusiness;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public class UserPortraitAiReportBusinessImpl implements UserPortraitAiReportBusiness {

    @Override
    public void handlePortraitAiAnalysis(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {

        //远程调用 /web-report/userPortraitStatistics/queryPortraitStatisticsByDate  10张图表


        //地图图表  userPortraitStatistics/queryRegionDistributionByDate

        //拆分成11个部分,各部分喂给ai并报错


    }
}
