package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.SgConversionRestBussiness;
import com.pes.jd.model.DTO.ShopOrderSaleAmountDTO;
import com.pes.jd.model.DTO.ShopSgDetailDTO;
import com.pes.jd.model.DTO.ShopUseConverDTO;
import com.pes.jd.model.Query.MServiceQuery;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年02月25 16:06:06<br>
 */
@Service
public class SgConversionRestBussinessImpl implements SgConversionRestBussiness {
    @Resource
    private UsermgrRestTemplate usermgrRestTemplate;

    @Resource
    private PopRtSubRestTemplate popRtSubRestTemplate;

    @Resource
    private PopSubRestTemplate popSubRestTemplate;

    @Override
    public List<ShopSgDetailDTO> selectSgUrgeInfoLst(String db, List<ShopUrge>  shopLst, String startDate,String endDate,Integer orderType,Boolean isInsert) {
        List<ShopSgDetailDTO> result=null;
        MServiceQuery ms=new MServiceQuery(db);
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("urgeShopLst", shopLst)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("orderType", orderType)
                .put("isInsert", isInsert)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        RestResponseTypeRef<List<ShopSgDetailDTO>> restResponse = usermgrRestTemplate.postRest(serviceId,"/conv/selectSgUrgeInfoLst",body, new ParameterizedTypeReference<RestResponseTypeRef<List<ShopSgDetailDTO>>>(){});
        if(restResponse.getSuccess()){
            result=restResponse.getData();
        }else{
            result=Lists.newArrayList();
        }
        return result;
    }

    @Override
    public List<ShopSgDetailDTO> selectSgUrgeInfoLstNew(String db, List<ShopUrge>  shopLst, String startDate,String endDate,Integer orderType,Boolean isInsert) {
        List<ShopSgDetailDTO> result=null;
        MServiceQuery ms=new MServiceQuery(db);
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("urgeShopLst", shopLst)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("orderType", orderType)
                .put("isInsert", isInsert)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(db, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
        RestResponseTypeRef<List<ShopSgDetailDTO>> restResponse = popRtSubRestTemplate.postRest(serviceId,"/conv/selectSgUrgeInfoLstNew",body, new ParameterizedTypeReference<RestResponseTypeRef<List<ShopSgDetailDTO>>>(){});
        if(restResponse.getSuccess()){
            result=restResponse.getData();
        }else{
            result=Lists.newArrayList();
        }
        return result;
    }

    @Override
    public List<ShopUrge> selectUrgeShopByShopIdLstByType(List<Long> shopIdLst,Integer type){
        List<ShopUrge> result=null;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopIdLst", shopIdLst)
                .put("type", type)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        RestResponseTypeRef<List<ShopUrge>> restResponse = usermgrRestTemplate.postRest(serviceId,"/shop/selectUrgeShopByShopIdLstByType",body,
                new ParameterizedTypeReference<RestResponseTypeRef<List<ShopUrge>>>(){});
        if(restResponse.getSuccess()){
            result=restResponse.getData();
        }else{
            result=Lists.newArrayList();
        }
        return result;
    }

    @Override
    public List<ShopUseConverDTO> selectSgShopUseConvertLst(String db, List<ShopUrge>  shopLst, String startDate,String endDate) throws Exception {
        List<ShopUseConverDTO> result=null;
        MServiceQuery ms=new MServiceQuery(db);
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("urgeShopLst", shopLst)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(db, ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
        RestResponseTypeRef<List<ShopUseConverDTO>> restResponse = popRtSubRestTemplate.postRest(serviceId,"/conv/selectSgShopUseConvertLst",body, new ParameterizedTypeReference<RestResponseTypeRef<List<ShopUseConverDTO>>>(){});
        if(restResponse.getSuccess()){
            result=restResponse.getData();
        }else{
            result=Lists.newArrayList();
        }
        return result;
    }

    @Override
    public List<ShopOrderSaleAmountDTO> selecShopOrderSaleAmountLst(String db, List<ShopUrge>  shopLst, String startDate, String endDate) throws Exception {
        List<ShopOrderSaleAmountDTO> result=null;
        MServiceQuery ms=new MServiceQuery(db);
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("urgeShopLst", shopLst)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(db, ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        RestResponseTypeRef<List<ShopOrderSaleAmountDTO>> restResponse = popSubRestTemplate.postRest(serviceId,"/shop/ov/selecShopOrderSaleAmountLst",body, new ParameterizedTypeReference<RestResponseTypeRef<List<ShopOrderSaleAmountDTO>>>(){});
        if(restResponse.getSuccess()){
            result=restResponse.getData();
        }else{
            result=Lists.newArrayList();
        }
        return result;
    }


}
