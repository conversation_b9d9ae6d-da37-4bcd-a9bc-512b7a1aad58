package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ReceiveFilterAnalysisBussiness;
import com.pes.jd.model.Param.CustomerReceiveParam;
import com.pes.jd.model.Param.ReceiveFilterParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

/**
 * 接待过滤分析（微服务入口）
 */
@Service
public class ReceiveFilterAnalysisBussinessImpl implements ReceiveFilterAnalysisBussiness {
    private static final Logger logger = LoggerFactory.getLogger(ReceiveFilterAnalysisBussinessImpl.class);

    @Autowired
    private PopSubRestTemplate popSubRestTemplate;

    @Override
    public ApiResponse selectReceiveFilter(ShopQuery shop, ReceiveFilterParam param){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        ApiResponse apiResponse = null;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("param", param)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        try {
            apiResponse = popSubRestTemplate.postRest(serviceId, "/data/analysis/reciveFilterRecord", body);
        } catch (Exception e) {
            logger.error("/data/analysis/reciveFilterRecord error:{}", e.getMessage(), e);
            throw e;
        }


        return apiResponse;
    }


    //按会话
    @Override
    public ApiResponse selectReceiveFilterSession(ShopQuery shop, ReceiveFilterParam param) {
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        ApiResponse apiResponse = null;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("param", param)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        try {
            apiResponse = popSubRestTemplate.postRest(serviceId, "/data/analysis/reciveFilterRecordSession", body);
        } catch (Exception e) {
            logger.error("/data/analysis/reciveFilterRecordSession error:{}", e.getMessage(), e);
            throw e;
        }


        return apiResponse;
    }

    @Override
    public ApiResponse searchCustomerReciveRecordLst(ShopQuery shop, CustomerReceiveParam param, SortPageQuery sortPageQuery){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        ApiResponse apiResponse = null;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("param", param)
                .put("sortPageQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        try {
            apiResponse = popSubRestTemplate.postRest(serviceId, "/data/analysis/customerReceiveRecord", body);
        } catch (Exception e) {
            logger.error("/data/analysis/customerReceiveRecord error:{}", e.getMessage(), e);
            throw e;
        }
        return apiResponse;
    }

    @Override
    public ApiResponse serachCustomerReceiveRecordForChatSession(ShopQuery shop, CustomerReceiveParam param, SortPageQuery sortPageQuery){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        ApiResponse apiResponse;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("param", param)
                .put("sortQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        try {
            String uri = "/data/analysis/customerReceiveRecordForChatSession";
            apiResponse = popSubRestTemplate.postRest(serviceId, uri, body);
        } catch (Exception e) {
            logger.error("/data/analysis/customerReceiveRecordForChatSession error:{}", e.getMessage(), e);
            throw e;
        }
        return apiResponse;
    }
}
