package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopRemindBlackListBusiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.model.DTO.ShopRemindBlackListDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.ShopSmsBacklist;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import com.yiyitech.support.rpc.RestOperator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ShopRemindBlackListBusinessImpl implements ShopRemindBlackListBusiness {

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;
    @Autowired
    private ShopSysManagerBusiness shopSysManagerBusiness;
    @Override
    public Object batchInsert(String shopId, String buyerNicks, String type, Long userId) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .put("buyerNicks",buyerNicks)
                .put("type", type)
                .put("userId", userId)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/blacklist/batchInsert", body, new ParameterizedTypeReference<RestApiResponse2<Object>>() {});
    }

    @Override
    public Object selectShopRemindBlackList(MasterServiceShopQuery shop, String buyerNick, Date startDate, Date endDate) throws Exception {
        RestApiResponse2<List<ShopRemindBlackListDTO>> resp = getRestApiResponse(shop.getSelectedShop().getShopId(), buyerNick, startDate, endDate);
        if (resp != null && resp.getSuccess()) {
            List<ShopRemindBlackListDTO> shopRemindBlackList = resp.getData().get("result");
            if (CollectionUtils.isNotEmpty(shopRemindBlackList)) {
                ApiResponse userApi = shopSysManagerBusiness.selectUserByShopId(shop.getSelectedShop());
                List<ShopUserDTO> userLst = null;
                if (userApi.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
                    Object obj = userApi.getData().get("result");
                    if (obj != null) {
                        userLst = JacksonUtils.objTolist(obj, ShopUserDTO.class);
                    }
                    if (CollectionUtils.isNotEmpty(userLst)) {
                        Map<Long, String> userNameMap = userLst.stream().collect(Collectors.toMap(ShopUserDTO::getUserId, ShopUserDTO::getNick));
                        for (ShopRemindBlackListDTO back : shopRemindBlackList) {
                            back.setOperatorStr(userNameMap.get(back.getOperator()));
                        }
                    }

                }
            }
        }
        return resp;
    }

    @Override
    public Object deleteShopRemindBlackListById(Long id) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", id)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/blacklist/deleteShopRemindBlackListById", body, new ParameterizedTypeReference<RestApiResponse2<Object>>() {});
    }

    @Override
    public String selectManualMerchandisingBlacklistList(Long shopId) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            RestApiResponse2<Object> apiResponse = usermgrRestTemplate.postRestOfResult2(serviceId, "/blacklist/selectManualMerchandisingBlacklistList", body, new ParameterizedTypeReference<RestApiResponse2<Object>>() {
        });
        String manualMerchandisingBlacklistString = "";
        if(apiResponse != null && apiResponse.getSuccess()){
            manualMerchandisingBlacklistString = JSONObject.toJSONString(apiResponse.getData().get("result"));
        }
        return manualMerchandisingBlacklistString;
    }

    private RestApiResponse2<List<ShopRemindBlackListDTO>> getRestApiResponse(Long shopId, String buyerNick, Date startDate, Date endDate) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .put("buyerNick", buyerNick)
                .put("startDate", DateUtils.formatYMdHms(DateUtils.getStartTimeOfDate(startDate)))
                .put("endDate", DateUtils.formatYMdHms(DateUtils.getEndTimeOfDate(endDate)))
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/blacklist/selectShopRemindBlackList", body, new ParameterizedTypeReference<RestApiResponse2<List<ShopRemindBlackListDTO>>>() {});
    }
}
