package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.Constants.GoodFilterEnum;
import com.pes.jd.Constants.PesConstants;
import com.pes.jd.business.GoodsHandleBusiness;
import com.pes.jd.business.OrderRefundHandleBussiness;
import com.pes.jd.dao.*;
import com.pes.jd.data.converter.OrderRefundConverter;
import com.pes.jd.model.BO.OrderRefundBO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service
public class OrderRefundHandleBussinessImpl implements OrderRefundHandleBussiness {
    private final Logger logger = LoggerFactory.getLogger(OrderRefundHandleBussinessImpl.class);

    @Resource
    private OrderRefundConverter orderRefundConverter;

    @Resource
    private OrderRefundDao orderRefundDao;

    @Resource
    private CsRefundDayDao csRefundDayDao;

    @Resource
    private ShopRefundDayDao shopRefundDayDao;

    @Resource
    private OrderFilterDao orderFilterDao;

    @Resource
    private OrderDao orderDao;

    @Resource
    private GoodsHandleBusiness goodsHandleBusiness;

    @Resource
    private PresaleOrderDao presaleOrderDao;

    @Override
    public void pullOrderRefundApplyData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {

        long s = System.currentTimeMillis();
        try {
            Date startDate = jobDate.getStartDate();
            if(jobDate.getNeedCalFinalData()){
                startDate = DateUtils.getDateByPeriod(startDate, -CommonConstants.REFUND_PULL_CAL_DATE);
            }
            Map<String, OrderRefundBO> orderRefundApplyData = orderRefundConverter.pullOrderRefundApplyData(jobShop, startDate, jobDate.getEndDate(),
                    isDelData);

            for (String year_month : orderRefundApplyData.keySet()) {
                OrderRefundBO groupByOrderRefundDataBO = orderRefundApplyData.get(year_month);
                if (groupByOrderRefundDataBO == null) {
                    continue;
                }
                Date date = DateUtils.parseYMd(year_month);

                /*
                 * 处理退款订单
                 */
                // TODO("check")
                handleOrderRefundByFile(jobShop, date, isDelData, groupByOrderRefundDataBO);
            }

        } catch (Exception e) {
            logger.error("【{}】 handle shop Order error", jobShop.getShop().getTitle(), e);
            throw e;
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("handle shop Refund Apply end,time:{} s", (e - s) / 1000);
        }
    }

    @Override
    public void pullOrderRefundCheckData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {
        long s = System.currentTimeMillis();
        try {
            Date startDate = jobDate.getStartDate();
            if(jobDate.getNeedCalFinalData()){
                startDate = DateUtils.getDateByPeriod(startDate, -CommonConstants.REFUND_PULL_CAL_DATE);
            }
            Map<String, OrderRefundBO> orderRefundCheckData = orderRefundConverter.pullOrderRefundCheckData(jobShop, startDate, jobDate.getEndDate(),
                    isDelData);

            for (String year_month : orderRefundCheckData.keySet()) {
                OrderRefundBO groupByOrderRefundDataBO = orderRefundCheckData.get(year_month);
                if (groupByOrderRefundDataBO == null) {
                    continue;
                }
                Date date = DateUtils.parseYMd(year_month);
                /*
                 * 处理退款订单
                 */
                // TODO("check")
                handleOrderRefundByFile(jobShop, date, isDelData, groupByOrderRefundDataBO);
            }
        } catch (Exception e) {
            logger.error("【{}】 handle shop Order error", jobShop.getShop().getTitle(), e);
            throw e;
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("handle shop OrderRefundCheck end,time:{}", (e - s) / 1000);
        }

    }

    private void handleOrderRefundByFile(JobShopQuery jobShop, Date jobDate, boolean isDelData,
                                         OrderRefundBO orderRefundApplyData) {

        JobShopDTO shop = jobShop.getShop();

        FileOutputStream fis;
        InputStreamReader isr = null;
        OutputStreamWriter osw;
        BufferedWriter bw;
        File file;

        try {
            file = new File(System.currentTimeMillis() + "orderRefund.txt");
            if (!file.exists()) {
                if (file.createNewFile()) {
                    if (logger.isDebugEnabled()) {
                        logger.debug("create file success");
                    }
                }
            }
            fis = new FileOutputStream(file);
            osw = new OutputStreamWriter(fis, StandardCharsets.UTF_8);
            bw = new BufferedWriter(osw);
        } catch (Exception e) {
            logger.error("handle order refund by file (init stream error)", e);
            return;
        }

        StringBuilder orderRefundFileSql = orderRefundApplyData.getOrderRefundFileSql();
        try {

            for (int i = 0; i < CommonConstants.RECALLAPI_TIMES; i++) {

                try {
                    bw.write(orderRefundFileSql.toString());
                    bw.flush();
                    if (isDelData) {
                        int num = orderRefundDao.deleteOrderRefundByOrderRefundIdLst(shop, orderRefundApplyData.getOrderRefundIdLst(), jobDate);
                        int num2 = orderRefundDao.deleteOrderRefundByOrderIdLst(shop, new ArrayList<>(orderRefundApplyData.getOrderIdSet()), jobDate);
                        logger.info("==========>refund del by orderIds size:{}", num2);
                        if (logger.isDebugEnabled()) {
                            logger.debug(" delete order refund num :{}", num);
                        }
                    }
                    int rows = orderRefundDao.persistOrderRefundByFile(shop, file.getAbsolutePath(), jobDate);
                    if (logger.isDebugEnabled()) {
                        logger.debug(" insert order refund num :{}", rows);
                    }

                    break;
                } catch (Exception e) {
                    logger.error("handle order refund by file (insert data error)", e);
                    if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                        throw new RuntimeException(e);
                    }
                }
            }

        } finally {
            if (file.delete()) {
                if (logger.isDebugEnabled()) {
                    logger.debug("delete file success");
                }
            }
            closeAll(fis, isr, osw, bw, file);
        }
    }

    public void closeAll(FileOutputStream fis, InputStreamReader isr, OutputStreamWriter osw, BufferedWriter bw,
                         File file) {
        if (bw != null) {
            try {
                bw.close();
            } catch (IOException e) {
                logger.error("close order refund file error)", e);
            }
        }
        if (isr != null) {
            try {
                isr.close();
            } catch (IOException e) {
                logger.error("close order refund file error)", e);
            }
        }

        if (fis != null) {
            try {
                fis.close();
            } catch (IOException e) {
                logger.error("closeorder refund file error)", e);
            }
        }
        if (file != null) {
            if (file.delete()) {
                if (logger.isDebugEnabled()) {
                    logger.debug("delete file success");
                }
            }
        }
    }

    // 客服维度每日退款
    @Override
    public void handleShopCsTeamDailyRefund(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        List<Date> calDateArr = new ArrayList<>();
        List<Date> dates = jobDate.getCommonDates();
        if (jobDate.getNeedCalFinalData()) {
            //退款往前重拉两天所以这里也重算两天
            for (int i = CommonConstants.REFUND_PULL_CAL_DATE; i >= 0; i--) {
                calDateArr.add(DateUtils.getDateByPeriod(dates.get(0), -i));
            }
        } else {
            calDateArr = dates;
        }
        for (Date date : calDateArr) {
            Date sDate = date;
            Date eDate = DateUtil.getEndTimeOfDate(date);
            if (CollectionUtils.isEmpty(jobShop.getCsLst())) {
                return;
            }
            List<OrderFilterDTO> orderFilters = orderFilterDao.queryPesOrdersByShopId(jobShop.getShop(), date, eDate);
            // 根据时间查询店铺所有客服落实的订单的退款
            List<CsOrderRefundDTO> applyRefundLst = orderRefundDao.queryCsRelatedApplyRefund(jobShop.getShop(), sDate, eDate);

            //presale od filter
            Date presaleStart = DateUtil.getDateByPeriod(sDate, CommonConstants.ORDER_REFUND_DELAY_DAYS);
            List<Long> presaleOdIds = presaleOrderDao.selectOrderIdsByCreated(jobShop.getShop(), presaleStart, eDate);
            Set<Long> presaleOdSet = Sets.newHashSet(presaleOdIds);

            applyRefundLst.removeIf(t -> presaleOdSet.contains(t.getOrderId()));

            //售前
            List<CsOrderRefundDTO> preSaleCompleteRefundLst = Optional.ofNullable(orderRefundDao.queryCsRelatedCompletedRefund(jobShop.getShop(), sDate, eDate))
                    .orElse(new ArrayList<>(0));

            Set<Long> orderIds = Sets.newHashSet();
            //售前退款的订单
            Map<Long, List<CsOrderRefundDTO>> preSaleCompleteRefundMap = Maps.newHashMap();
            for (CsOrderRefundDTO dto : preSaleCompleteRefundLst) {
                Long orderId = dto.getOrderId();
                orderIds.add(orderId);
                List<CsOrderRefundDTO> corLst = preSaleCompleteRefundMap.get(orderId);
                if (CollUtil.isEmpty(corLst)) {
                    corLst = new ArrayList<>();
                    corLst.add(dto);
                } else {
                    corLst.add(dto);

                }
                preSaleCompleteRefundMap.put(orderId, corLst);
            }

            Date startDate = DateUtil.getDateByPeriod(jobDate.getStartDate(), -91);

            List<OrderDTO> orderDTOS = Optional.ofNullable(orderDao.selectOrderByShopIdAndOrderIds(orderIds, jobShop.getShop(), startDate, jobDate.getEndDate()))
                    .orElse(new ArrayList<>(0));

            //售後
            List<CsOrderRefundDTO> afeSaleCompleteRefundLst = Optional.ofNullable(orderRefundDao.queryCsRelatedCompletedAscServiceRefund(jobShop.getShop(), sDate, eDate))
                    .orElse(new ArrayList<>(0));

            Set<Long> orderIdsBe = Sets.newHashSet();
            //售后退款的订单
            Map<Long, List<CsOrderRefundDTO>> afeSaleCompleteRefundMap = Maps.newHashMap();
            for (CsOrderRefundDTO dto : afeSaleCompleteRefundLst) {
                orderIdsBe.add(dto.getOrderId());

                Long orderId = dto.getOrderId();
                orderIds.add(orderId);
                List<CsOrderRefundDTO> corLst = afeSaleCompleteRefundMap.get(orderId);
                if (CollUtil.isEmpty(corLst)) {
                    corLst = new ArrayList<>();
                    corLst.add(dto);
                } else {
                    corLst.add(dto);

                }
                afeSaleCompleteRefundMap.put(orderId, corLst);
            }

            List<OrderDTO> orderDTOList = Optional.ofNullable(orderDao.selectOrderByShopIdAndOrderIds(orderIdsBe, jobShop.getShop(), startDate, jobDate.getEndDate()))
                    .orElse(new ArrayList<>(0));

            orderDTOS.addAll(orderDTOList);

            Map<Long, OrderDTO> orderMap = orderDTOS.stream()
                    .collect(Collectors.toMap(OrderDTO::getOrderId, orderDTO -> orderDTO, (o1, o2) -> o2));

            List<CsOrderRefundDTO> refundOrderList = Lists.newArrayList();
            if (MapUtils.isNotEmpty(preSaleCompleteRefundMap)) {
                for (List<CsOrderRefundDTO> corLst : preSaleCompleteRefundMap.values()) {
                    if(CollUtil.isEmpty(corLst)){
                        continue;
                    }
                    refundOrderList.addAll(corLst);
                }
            }
            if (MapUtils.isNotEmpty(afeSaleCompleteRefundMap)) {
                for (List<CsOrderRefundDTO> corLst : afeSaleCompleteRefundMap.values()) {
                    if(CollUtil.isEmpty(corLst)){
                        continue;
                    }
                    refundOrderList.addAll(corLst);
                }
            }

            Iterator<CsOrderRefundDTO> iterator = refundOrderList.iterator();
            while (iterator.hasNext()) {
                CsOrderRefundDTO refundData = iterator.next();
                OrderDTO orderDTO = orderMap.get(refundData.getOrderId());
                if (null == orderDTO) {
                    iterator.remove();
                    continue;
                }
                if (orderDTO.getPayType() != null && 1 == orderDTO.getPayType()) {
                    continue;
                }
                if (null != orderDTO.getPayTime()) {
                    continue;
                }
                if (null != orderDTO.getPayment() && orderDTO.getPayment() > 0.001) {
                    continue;
                }
                iterator.remove();
            }
            if (isDelData) {
                csRefundDayDao.deltCsRefundDayByDateAndShopId(jobShop.getShop(), date);
            }

            refundOrderList.removeIf(t -> presaleOdSet.contains(t.getOrderId()));

            csRefundDayDao.batchInsertCsRefundDay(calCsRefundDay(jobShop, date, refundOrderList, applyRefundLst, orderFilters), jobShop.getShop(), date);
        }
    }

    // 店铺维度的退款shopRefundDayDao
    @Override
    public void handleShopDailyRefund(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        List<Date> dates = jobDate.getCommonDates();
        for (Date date : dates) {
            Date sDate = date;
            Date eDate = DateUtil.getEndTimeOfDate(date);

            List<OrderFilterDTO> orderFilters = orderFilterDao.queryPesOrdersByShopId(jobShop.getShop(), sDate, eDate);
            //presale od filter
            Date presaleStart = DateUtil.getDateByPeriod(sDate, CommonConstants.ORDER_REFUND_DELAY_DAYS);
            List<Long> presaleOdIds = presaleOrderDao.selectOrderIdsByCreated(jobShop.getShop(), presaleStart, eDate);
            Set<Long> presaleOdSet = Sets.newHashSet(presaleOdIds);
            // 根据时间查询店铺所有客服落实的订单的退款
            // 根据时间查询店铺所有的退款
            List<ShopOrderRefundDTO> applyRefunds = orderRefundDao.selectShopApplyRefunds(jobShop.getShop(), jobDate);
            //presale od filter
            applyRefunds.removeIf(t -> presaleOdSet.contains(t.getOrderId()));

            List<ShopOrderRefundDTO> complatedRefunds = new ArrayList<>();

            //售前
            List<ShopOrderRefundDTO> preSaleCompleteRefundLst = orderRefundDao.selectShopCompletedRefunds(jobShop.getShop(), jobDate);

            complatedRefunds.addAll(preSaleCompleteRefundLst);

            Set<Long> preSaleOds = preSaleCompleteRefundLst.stream().map(ShopOrderRefundDTO::getOrderId).collect(Collectors.toSet());
            //售後
            List<ShopOrderRefundDTO> afeSaleCompleteRefundLst = orderRefundDao.selectShopCompletedAscServiceRefunds(jobShop.getShop(), jobDate);

            //过滤重复订单
            afeSaleCompleteRefundLst.removeIf(t -> preSaleOds.contains(t.getOrderId()));

            complatedRefunds.addAll(afeSaleCompleteRefundLst);
            //presale od filter
            complatedRefunds.removeIf(t -> presaleOdSet.contains(t.getOrderId()));

            ShopRefundDayDTO srov = calShopRefund(jobShop, jobDate.getDate(), applyRefunds, complatedRefunds, orderFilters);
            List<ShopRefundDayDTO> refundOverviewLst = new ArrayList<>(1);
            refundOverviewLst.add(srov);
            if (isDelData) {
                shopRefundDayDao.deltShopRefundDayByDateAndShopId(jobShop.getShop(), jobDate.getDate());
            }
            shopRefundDayDao.batchInsertShopRefundDay(refundOverviewLst, jobShop.getShop());
        }
    }

    private List<CsRefundDayDTO> calCsRefundDay(JobShopQuery jobShop, Date date, List<CsOrderRefundDTO> complatedRefunds,
                                                List<CsOrderRefundDTO> applyRefunds, List<OrderFilterDTO> orderFilters) {
        List<CsRefundDayDTO> CsRefundDayDTOLst = new ArrayList<>();
        Map<String, CsRefundDayDTO> csRefundMap = new HashMap<>();
        // 将所有客服放在map集合中
        if (CollectionUtils.isNotEmpty(jobShop.getCsLst())) {
            CsRefundDayDTO csrov;
            for (CsDTO cs : jobShop.getCsLst()) {
                csrov = new CsRefundDayDTO();
                csrov.setDate(date);
                csrov.setShopId(jobShop.getShop().getShopId());
                csrov.setCsNick(cs.getNick());
                CsRefundDayDTOLst.add(csrov);
                csRefundMap.put(csrov.getCsNick(), csrov);
            }
        } else {
            return null;
        }
        if (CollectionUtils.isNotEmpty(applyRefunds)) {

            Map<String, List<CsOrderRefundDTO>> csMap = applyRefunds.stream()
                    .collect(Collectors.groupingBy(CsOrderRefundDTO::getCsNick));
            CsRefundDayDTO srov;
            for (Entry<String, List<CsOrderRefundDTO>> entry : csMap.entrySet()) {

                srov = csRefundMap.get(entry.getKey());
                if (srov == null) {
                    continue;
                }
                int applyRefundNum = 0;
                int applyRefundProductNum = 0;
                double applyRefundAmount = 0;
                Set<String> applyBuyerSet = new HashSet<>(applyRefunds.size());
                List<CsOrderRefundDTO> csRefunds = entry.getValue();
                if (CollectionUtils.isNotEmpty(csRefunds)) {
                    for (CsOrderRefundDTO refund : csRefunds) {
                        Double refundAmount = refund.getRefundAmount();
                        int num = refund.getRefundNum();
                        //订单过滤
                        boolean isOrderFilter = false;
                        if (CollectionUtils.isNotEmpty(orderFilters)) {
                            List<Long> filterOrderIds = orderFilters.stream().map(OrderFilterDTO::getOrderId).collect(Collectors.toList());
                            if (filterOrderIds.contains(refund.getOrderId())) {
                                isOrderFilter = true;
                            }
                        }
                        if (!isOrderFilter) {
                            applyRefundProductNum += num;
                        }
                        applyRefundNum++;
                        applyRefundAmount += refundAmount;
                        applyBuyerSet.add(refund.getBuyerNick());
                    }
                }

                srov.setApplyRefundNum(Math.max(applyRefundNum, 0));// 申请退款笔数
                srov.setApplyRefundProductNum(Math.max(applyRefundProductNum, 0));// 申请退款件数
                srov.setApplyRefundAmount(applyRefundAmount > 0 ? applyRefundAmount : 0);
                srov.setApplyRefundBuyerNum(applyBuyerSet.size());
            }

        }

        if (CollectionUtils.isNotEmpty(complatedRefunds)) {

            Map<String, List<CsOrderRefundDTO>> csMap = complatedRefunds.stream()
                    .collect(Collectors.groupingBy(CsOrderRefundDTO::getCsNick));
            CsRefundDayDTO srov;
            for (Entry<String, List<CsOrderRefundDTO>> entry : csMap.entrySet()) {
                srov = csRefundMap.get(entry.getKey());
                if (srov == null) {
                    continue;
                }

                int completeRefundNum = 0;
                int completedRefundProductNum = 0;
                double completedRefundAmount = 0;
                long totalRefundDuration = 0L;
                Set<String> completeBuyerSet = new HashSet<>(complatedRefunds.size());
                List<CsOrderRefundDTO> csRefunds = entry.getValue();
                if (CollectionUtils.isNotEmpty(csRefunds)) {
                    for (CsOrderRefundDTO refund : entry.getValue()) {
                        Double refundAmount = refund.getRefundAmount();
                        int num = refund.getRefundNum();
                        //订单过滤
                        boolean isOrderFilter = false;
                        if (CollectionUtils.isNotEmpty(orderFilters)) {
                            List<Long> filterOrderIds = orderFilters.stream().map(OrderFilterDTO::getOrderId).collect(Collectors.toList());
                            if (filterOrderIds.contains(refund.getOrderId())) {
                                isOrderFilter = true;
                            }

                        }
                        if (!isOrderFilter) {
                            completedRefundProductNum += num;
                        }
                        completedRefundAmount += refundAmount;
                        completeRefundNum++;
                        completeBuyerSet.add(refund.getBuyerNick());
                        totalRefundDuration += refund.getTotalRefundDuration();
                    }
                }
                srov.setCompletedRefundNum(Math.max(completeRefundNum, 0));// 完成退款笔数
                srov.setCompletedRefundProductNum(Math.max(completedRefundProductNum, 0));// 完成退款件数
                srov.setCompletedRefundAmount(completedRefundAmount > 0 ? completedRefundAmount : 0);
                srov.setTotalRefundDuration(totalRefundDuration);
                srov.setCompletedRefundBuyerNum(completeBuyerSet.size());
//                srov.setTotalRefundDuration(totalRefundDuration);
            }
        }
        return CsRefundDayDTOLst;
    }

    /**
     * 处理一个订单order，多个退款
     */
    private List<CsOrderRefundDTO> filteCsRefundLst(List<CsOrderRefundDTO> refundLst, int type) {
        if (CollectionUtils.isEmpty(refundLst)) {
            return null;
        }
        List<CsOrderRefundDTO> resultRefundLst = new ArrayList<>(refundLst.size());
        Map<Long, List<CsOrderRefundDTO>> refundOrderMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(refundLst)) {
            Long orderId;
            for (CsOrderRefundDTO refund : refundLst) {
                orderId = refund.getOrderId();
                if (refundOrderMap.get(orderId) == null) {
                    List<CsOrderRefundDTO> ll = new ArrayList<>();
                    ll.add(refund);
                    refundOrderMap.put(orderId, ll);
                } else {
                    refundOrderMap.get(orderId).add(refund);
                }

            }
        }

        Set<Entry<Long, List<CsOrderRefundDTO>>> orderEntrySet = refundOrderMap.entrySet();
        if (CollectionUtils.isNotEmpty(orderEntrySet)) {
            List<CsOrderRefundDTO> refunds;
            for (Entry<Long, List<CsOrderRefundDTO>> entry : orderEntrySet) {
                refunds = entry.getValue();
                Collections.sort(refunds);
                if (type == 1) {// 完成的退款,取最小的
                    resultRefundLst.add(refunds.get(0));
                } else {// 申请的退款,取最大的
                    resultRefundLst.add(refunds.get(refunds.size() - 1));
                }
            }
        }
        return resultRefundLst;
    }

    /**
     * 店铺退款 calShopRefund:(店铺退款). <br/>
     */
    private ShopRefundDayDTO calShopRefund(JobShopQuery jobShop, Date date, List<ShopOrderRefundDTO> applyRefunds,
                                           List<ShopOrderRefundDTO> complatedRefunds, List<OrderFilterDTO> orderFilters) {

        ShopRefundDayDTO srov = new ShopRefundDayDTO();
        srov.setShopId(jobShop.getShop().getShopId());
        srov.setDate(date);
        //applyRefunds = filteRefundLst(applyRefunds, 2);
        //complatedRefunds = filteRefundLst(complatedRefunds, 1);

        if (CollectionUtils.isNotEmpty(applyRefunds)) {
            int applyRefundNum = 0;
            int applyRefundProductNum = 0;
            double applyRefundAmount = 0;
            Set<String> applyBuyerSet = new HashSet<>();
            for (ShopOrderRefundDTO refund : applyRefunds) {
                Double refundAmount = refund.getRefundAmount();
                int num = refund.getRefundNum();
                if (CollectionUtils.isNotEmpty(orderFilters)) {
                    for (OrderFilterDTO pesOrderFilter : orderFilters) {
                        if (refund.getOrderId().equals(pesOrderFilter.getOrderId())) {
                            if (refund.getOrderId().equals(pesOrderFilter.getOrderId())) {
                                if (refund.getType() == 2) {
                                    num = num - num;
//                                    refundAmount = refundAmount - refundAmount;
                                } else {
                                    num = num - pesOrderFilter.getNum();
//                                    refundAmount = refundAmount - pesOrderFilter.getPrice();
                                }
                            }
                        }
                    }
                }

                applyRefundProductNum += num;
                applyRefundAmount += refundAmount;
                applyRefundNum++;
                applyBuyerSet.add(refund.getBuyerNick());
            }
            srov.setApplyRefundNum(Math.max(applyRefundNum, 0));// 申请退款笔数
            srov.setApplyRefundProductNum(Math.max(applyRefundProductNum, 0));// 申请退款件数
            srov.setApplyRefundAmount(applyRefundAmount > 0 ? applyRefundAmount : 0);
            srov.setApplyRefundBuyerNum(applyBuyerSet.size());
        } else {
            srov.setApplyRefundNum(0);// 申请退款笔数
            srov.setApplyRefundProductNum(0);// 申请退款件数
            srov.setApplyRefundAmount(0.0);
            srov.setApplyRefundBuyerNum(0);
        }
        if (CollectionUtils.isNotEmpty(complatedRefunds)) {
            List<Long> orderIdList = complatedRefunds.stream().map(ShopOrderRefundDTO::getOrderId).collect(Collectors.toList());
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop,
                    date,
                    date,
                    orderIdList);
            Collection<Long> filterOrderIdsOfFinal = (Collection<Long>) filterMap.get(GoodFilterEnum.FILTER_ORDERIDS_OF_FINAL.getKey());

            Date startDate = DateUtil.getDateByPeriod(date, -PesConstants.ORDER_REFUND_MODIFIED_DATE_NEW_YEAR);

            List<OrderDTO> orderDTOList = Optional.ofNullable(orderDao.selectOrderByShopIdAndOrderIds(new HashSet<>(orderIdList), jobShop.getShop(), startDate, date))
                    .orElse(new ArrayList<>(0));

            Set<Long> orderIdSet = orderDTOList.stream().map(OrderDTO::getOrderId).collect(Collectors.toSet());

            int completeRefundNum = 0;
            int completedRefundProductNum = 0;
            double completedRefundAmount = 0;
            long totalRefundDuration = 0;
            Set<String> completeBuyerSet = new HashSet<>();
            for (ShopOrderRefundDTO refund : complatedRefunds) {
                Double refundAmount = refund.getRefundAmount();
                int num = refund.getRefundNum();
                if (CollectionUtils.isNotEmpty(orderFilters)) {
                    for (OrderFilterDTO pesOrderFilter : orderFilters) {
                        if (refund.getOrderId().equals(pesOrderFilter.getOrderId())) {
                            if (refund.getOrderId().equals(pesOrderFilter.getOrderId())) {
                                if (refund.getType() == 2) {
                                    num = num - num;
//                                    refundAmount = refundAmount - refundAmount;
                                } else {
                                    num = num - pesOrderFilter.getNum();
//                                    refundAmount = refundAmount - pesOrderFilter.getPrice();
                                }
                            }
                        }
                    }
                }
                //过滤掉未获取到的订单
                if(!orderIdSet.contains(refund.getOrderId())){continue;}
                if (!filterOrderIdsOfFinal.contains(refund.getOrderId())) {
                    completedRefundProductNum += num;//退款件数
                }
                completedRefundAmount += refundAmount;
                completeRefundNum++;
                completeBuyerSet.add(refund.getBuyerNick());
                //fixme bug2569 暂时处理，等后续工单反馈，问题单号：14661195
                if(refund.getTotalRefundDuration() < 0) continue;
                totalRefundDuration += refund.getTotalRefundDuration();

            }
            srov.setCompletedRefundNum(Math.max(completeRefundNum, 0));// 完成退款笔数
            srov.setCompletedRefundGoodsNum(Math.max(completedRefundProductNum, 0));// 完成退款件数
            srov.setCompletedRefundAmount(completedRefundAmount > 0 ? completedRefundAmount : 0);
            srov.setTotalRefundDuration(totalRefundDuration);
            srov.setCompletedRefundBuyerNum(completeBuyerSet.size());
        } else {
            srov.setCompletedRefundNum(0);// 完成退款笔数
            srov.setCompletedRefundGoodsNum(0);// 完成退款件数
            srov.setCompletedRefundAmount(0.0);
            srov.setTotalRefundDuration(0L);
            srov.setCompletedRefundBuyerNum(0);
        }
        return srov;
    }

    private List<ShopOrderRefundDTO> filteRefundLst(List<ShopOrderRefundDTO> refundLst, int type) {
        if (CollectionUtils.isEmpty(refundLst)) {
            return new ArrayList<>();
        }
        List<ShopOrderRefundDTO> resultRefundLst = new ArrayList<>(refundLst.size());

        Map<Long, List<ShopOrderRefundDTO>> refundOrderMap = new HashMap<>();
        Long orderId;
        for (ShopOrderRefundDTO refund : refundLst) {
            orderId = refund.getOrderId();
            if (refundOrderMap.get(orderId) == null) {
                List<ShopOrderRefundDTO> ll = new ArrayList<>(2);
                ll.add(refund);
                refundOrderMap.put(orderId, ll);
            } else {
                refundOrderMap.get(orderId).add(refund);
            }
        }

        Set<Entry<Long, List<ShopOrderRefundDTO>>> orderEntrySet = refundOrderMap.entrySet();
        if (CollectionUtils.isNotEmpty(orderEntrySet)) {
            List<ShopOrderRefundDTO> refunds;
            for (Entry<Long, List<ShopOrderRefundDTO>> entry : orderEntrySet) {
                refunds = entry.getValue();
                Collections.sort(refunds);
                if (type == 1) {// 完成的退款,取最小的
                    resultRefundLst.add(refunds.get(0));
                } else {// 申请的退款,取最大的
                    resultRefundLst.add(refunds.get(refunds.size() - 1));
                }
            }
        }

        return resultRefundLst;
    }

    @Override
    public void pullAscServiceOrderRefundApplyData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {
        long s = System.currentTimeMillis();
        try {
            Date startDate = jobDate.getStartDate();
            if(jobDate.getNeedCalFinalData()){
                startDate = DateUtils.getDateByPeriod(startDate, -CommonConstants.REFUND_PULL_CAL_DATE);
            }
            Map<String, OrderRefundBO> orderRefundApplyData = orderRefundConverter.pullAscServiceOrderRefundApplyData(jobShop, startDate, jobDate.getEndDate(), isDelData);

            for (String year_month : orderRefundApplyData.keySet()) {
                System.out.println(year_month);
                OrderRefundBO groupByOrderRefundDataBO = orderRefundApplyData.get(year_month);
                if (groupByOrderRefundDataBO == null) {
                    continue;
                }
                Date date = DateUtils.parseYMd(year_month);
                /*
                 * 处理售后退款订单
                 */
                // TODO("check")
                handleAscServiceOrderRefundByFile(jobShop, date, isDelData, groupByOrderRefundDataBO);
            }
        } catch (Exception e) {
            logger.error("【{}】 handle shop AscServiceOrderRefundApply error", jobShop.getShop().getTitle(), e);
            throw e;
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("handle shop AscServiceOrderRefundApply end,time:{}", (e - s) / 1000);
        }

    }

    @Override
    public void pullAscServiceOrderRefundCheckData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {
        long s = System.currentTimeMillis();
        try {
            Date startDate = jobDate.getStartDate();
            if(jobDate.getNeedCalFinalData()){
                startDate = DateUtils.getDateByPeriod(startDate, -CommonConstants.REFUND_PULL_CAL_DATE);
            }
            Map<String, OrderRefundBO> orderRefundCheckData = orderRefundConverter.pullAscServiceOrderRefundCheckData(jobShop, startDate, jobDate.getEndDate(), isDelData);

            for (String year_month : orderRefundCheckData.keySet()) {
                System.out.println(year_month);
                OrderRefundBO groupByOrderRefundDataBO = orderRefundCheckData.get(year_month);
                if (groupByOrderRefundDataBO == null) {
                    continue;
                }
                Date date = DateUtils.parseYMd(year_month);
                /*
                 * 处理售后退款订单
                 */
                // TODO("check")
                handleAscServiceOrderRefundByFile(jobShop, date, isDelData, groupByOrderRefundDataBO);
            }
        } catch (Exception e) {
            logger.error("【{}】 handle shop AscServiceOrderRefundCheck error", jobShop.getShop().getTitle(), e);
            throw e;
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("handle shop AscServiceOrderRefundCheck end,time:{}", (e - s) / 1000);
        }

    }


    private void handleAscServiceOrderRefundByFile(JobShopQuery jobShop, Date jobDate, boolean isDelData,
                                                   OrderRefundBO orderRefundApplyData) {

        JobShopDTO shop = jobShop.getShop();

        FileOutputStream fis;
        InputStreamReader isr = null;
        OutputStreamWriter osw;
        BufferedWriter bw;
        File file;

        try {
            file = new File(System.currentTimeMillis() + "AscServiceorderRefund.txt");
            if (!file.exists()) {
                if (file.createNewFile()) {
                    //logger.info("create file success");
                }
            }
            fis = new FileOutputStream(file);
            osw = new OutputStreamWriter(fis, StandardCharsets.UTF_8);
            bw = new BufferedWriter(osw);
        } catch (Exception e) {
            logger.error("handle AscServiceOrder refund by file (init stream error)", e);
            return;
        }

        StringBuilder orderRefundFileSql = orderRefundApplyData.getOrderRefundFileSql();
        try {

            for (int i = 0; i < CommonConstants.RECALLAPI_TIMES; i++) {

                try {
                    bw.write(orderRefundFileSql.toString());
                    bw.flush();
                    if (isDelData) {
                        int num = orderRefundDao.deleteOrderRefundByOrderRefundIdLst(shop,
                                orderRefundApplyData.getOrderRefundIdLst(), jobDate);
                        if (logger.isDebugEnabled()) {
                            logger.debug(" delete AscServiceOrder refund num :{}", num);
                        }
                    }
                    int rows = orderRefundDao.persistAscServiceOrderRefundByFile(shop, file.getAbsolutePath(), jobDate);
                    if (logger.isDebugEnabled()) {
                        logger.debug(" insert AscServiceOrder refund num :{}", rows);
                    }

                    break;
                } catch (Exception e) {
                    logger.error("handle AscServiceOrder refund by file (insert data error)", e);
                    if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                        throw new RuntimeException(e);
                    }
                }
            }

        } finally {
            if (file.delete()) {
                if (logger.isDebugEnabled()) {
                    logger.debug("delete AscServiceOrder file success");
                }
            }
            closeAll(fis, isr, osw, bw, file);
        }
    }

    @Override
    public void pullAscServiceOrderRefundDataByOrderId(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception {

        long s = System.currentTimeMillis();
        try {
            Date startDate = jobDate.getStartDate();
            if(jobDate.getNeedCalFinalData()){
                startDate = DateUtils.getDateByPeriod(startDate, -CommonConstants.REFUND_PULL_CAL_DATE);
            }
            List<OrderRefundDTO> orderRefundList = orderRefundDao.selectAscOrderRefundByDate(jobShop.getShop(), startDate, jobDate.getEndDate());
            if (CollectionUtils.isNotEmpty(orderRefundList)) {
                Set<Long> orderRefundIdSet = orderRefundList.stream().map(OrderRefundDTO::getOrderId)
                        .collect(Collectors.toSet());
                Map<String, OrderRefundBO> ascServiceOrderRefundDataByOrder = orderRefundConverter.pullAscServiceOrderRefundByDate(jobShop, orderRefundIdSet, isDelData);
                for (String year_month : ascServiceOrderRefundDataByOrder.keySet()) {
                    System.out.println(year_month);
                    OrderRefundBO groupByOrderRefundDataBO = ascServiceOrderRefundDataByOrder.get(year_month);
                    if (groupByOrderRefundDataBO == null) {
                        continue;
                    }
                    Date date = DateUtils.parseYMd(year_month);
                    /*
                     * 处理售后退款订单补充数据
                     */
                    // TODO("check")
                    handleAscServiceOrderRefundByFile(jobShop, date, isDelData, groupByOrderRefundDataBO);
                }
            }

        } catch (Exception e) {
            logger.error("【{}】 handle shop AscServiceOrderRefundByOrderId error", jobShop.getShop().getTitle(), e);
            throw e;
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("handle shop AscServiceOrderRefundApply end,time:{}s", (e - s) / 1000);
        }

    }

}
