package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.VersionControlBusiness;
import com.pes.jd.model.DTO.SysettingAppVersionDTO;
import com.pes.jd.model.VO.PesAppVersionMenuVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: ms-wb
 * @description:
 * @author: ALan
 * @create: 2019-05-19
 */

@Service
public class VersionControlBusinessImpl implements VersionControlBusiness {

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;


    @Override
    public RestApiResponse2<List<PesAppVersionMenuVO>> searchMenuForLst(String versionIdType) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("versionIdType",versionIdType)
                .put("type",0)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/versionControl/searchMenuForLst", body, new ParameterizedTypeReference<RestApiResponse2<List<PesAppVersionMenuVO>>>() {
        });
    }

    @Override
    public RestApiResponse2<List<SysettingAppVersionDTO>> batchAppVersionForLst() {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return  usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/versionControl/batchAppVersionForLst", body, new ParameterizedTypeReference<RestApiResponse2<List<SysettingAppVersionDTO>>>() {
        });
    }

    @Override
    public RestApiResponse2<List<String>> selectFunctionPointByShopIdForLst(String shopId) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId",shopId)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return  usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/versionControl/selectFunctionPointByShopIdForLst", body, new ParameterizedTypeReference<RestApiResponse2<List<String>>>() {
        });
    }

    @Override
    public RestApiResponse2<List<Long>> searchFunctionByfunctionNameAndByShopId(String funName, String shopIds) {
                 HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("funName",funName)
                .put("shopIds",shopIds)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return  usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/versionControl/searchFunctionByfunctionNameAndByShopId", body, new ParameterizedTypeReference<RestApiResponse2<List<Long>>>() {
        });
    }

    @Override
    public RestApiResponse2<String> batchUpdateVersionControl(String ids, String versionIdType) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("ids",ids)
                .put("versionIdType",versionIdType)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return  usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/versionControl/batchUpdateVersionControl", body, new ParameterizedTypeReference<RestApiResponse2<String>>() {
        });
    }
}
