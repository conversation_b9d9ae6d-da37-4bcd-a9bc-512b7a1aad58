package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.pes.jd.Constants.GoodFilterEnum;
import com.pes.jd.business.CsToOrderIndexHandlerBussiness;
import com.pes.jd.business.GoodsHandleBusiness;
import com.pes.jd.dao.CsOrderBindDao;
import com.pes.jd.dao.CsToOrderIndexDao;
import com.pes.jd.dao.ShopTeamToOrderIndexDao;
import com.pes.jd.model.DO.CsToOrderIndexDO;
import com.pes.jd.model.DO.ShopTeamToOrderIndexDO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.CsOrderBindDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.OrderDetailDTO;
import com.pes.jd.model.Enum.JudgeRuleTypeEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2019/2/3 10:05 AM
 * @since 1.0.0
 */
@SuppressWarnings("Duplicates")
@Service
public class CsToOrderIndexHandlerBussinessImpl implements CsToOrderIndexHandlerBussiness {

    private static final Logger logger = LoggerFactory.getLogger(CsPerformanceHandleBusinessImpl.class);

    @Resource
    private CsOrderBindDao csOrderBindDao;

    @Resource
    private CsToOrderIndexDao csToOrderIndexDao;

    @Resource
    private ShopTeamToOrderIndexDao shopTeamToOrderIndexDao;

    @Resource
    private GoodsHandleBusiness goodsHandleBusiness;

    @Override
    public void handleShopCsOrderBindIndex(
            JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();

        List<Date> dates = jobDate.getCommonDates();
        if (dates.isEmpty()) {
            logger.warn("req dates is empty");
            return;
        }
        JobShopDTO shop = jobShop.getShop();
        final String schemaId = shop.getSchemaId();

        List<CsDTO> csLst = jobShop.getCsLst().stream()
                .filter(e -> e.getType() == 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(csLst)) {
            logger.warn("shop csLst is empty");
            return;
        }
        // 拉取order_bind 数据，然后按天，每个客服计算入库
        List<CsOrderBindDTO> csOrderBindDTOS = csOrderBindDao.
                searchShopDate(jobShop.getShop(), jobDate.getStartDate(), jobDate.getEndDate());
        // key orderId -> list{CsOrderBindDTO}
        Map<Long, List<CsOrderBindDTO>> csOrderBindMapOrderId =
                csOrderBindDTOS.stream().collect(Collectors.groupingBy(CsOrderBindDTO::getOrderId));
        // key date -> list{CsOrderBindDTO}
        Map<Date, List<CsOrderBindDTO>> csOrderBindMapDate =
                csOrderBindDTOS.stream().collect(Collectors.groupingBy(CsOrderBindDTO::getDate));
        for (Date date : dates) {
            /*保存店铺的落实信息*/
            List<CsToOrderIndexDO> days = new ArrayList<>(csLst.size());
            for (CsDTO csDTO : csLst) {
                CsToOrderIndexDO indexDO = doComputer(csOrderBindMapOrderId, csOrderBindMapDate, date, csDTO, jobShop);
                if (indexDO != null) {
                    if (isDelData) {
                        csToOrderIndexDao.deleteByShopIdNickDate(shop.getShopId(), shop.getSchemaId(), csDTO.getNick(), date);
                    }
                    csToOrderIndexDao.insert(indexDO,schemaId);
                    days.add(indexDO);
                }
            }
            /*缓存计算店铺的落实信息*/
            if (CollectionUtils.isNotEmpty(days)){
                ShopTeamToOrderIndexDO indexDO = new ShopTeamToOrderIndexDO();
                initShopTeamOrderIndex(indexDO);
                for (CsToOrderIndexDO dayOrderIndex : days) {
                    BeanUtils.countPropertyVal(dayOrderIndex,indexDO);
                }
                indexDO.setDate(date);
                indexDO.setShopId(shop.getShopId());
                if (isDelData) {
                    shopTeamToOrderIndexDao.deleteByShopIdDate(shop.getShopId(), shop.getSchemaId(), date);
                }
                shopTeamToOrderIndexDao.insert(indexDO,schemaId);
            }
        }
        if(logger.isDebugEnabled()){

            logger.debug("计算客服落实，耗时{}秒", TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()-s));
        }
    }

    private void initShopTeamOrderIndex(ShopTeamToOrderIndexDO indexDO) {
        indexDO.setToOrderedPaidNum(0);
        indexDO.setToOrderedPaidGoodsNum(0);
        indexDO.setToOrderedPaidOrderNum(0);
        indexDO.setToOrderedPaidAmount(0.0D);
        indexDO.setToOrderedNotPaidNum(0);
        indexDO.setToOrderedNotPaidGoodsNum(0);
        indexDO.setToOrderedNotPaidOrderNum(0);
        indexDO.setToOrderedNotPaidAmount(0.0D);
        indexDO.setToPaidNum(0);
        indexDO.setToPaidGoodsNum(0);
        indexDO.setToPaidOrderNum(0);
        indexDO.setToPaidAmount(0.0D);
        indexDO.setSilentOrderToPaidNum(0);
        indexDO.setSilentOrderToPaidGoodsNum(0);
        indexDO.setSilentOrderToPaidOrderNum(0);
        indexDO.setSilentOrderToPaidAmount(0.0D);
        indexDO.setSilentToFolowupNum(0);
        indexDO.setSilentToFolowupGoodsNum(0);
        indexDO.setSilentToFolowupOrderNum(0);
        indexDO.setSilentToFolowupAmount(0.0D);
    }

    private CsToOrderIndexDO doComputer(
            Map<Long,
            List<CsOrderBindDTO>> csOrderBindMap,
            Map<Date,List<CsOrderBindDTO>> csOrderBindMapDate, Date date, CsDTO csDTO, JobShopQuery jobShop) {

        List<CsOrderBindDTO> csOrderBindLst = csOrderBindMapDate.get(date);
        Map<Long, List<CsOrderBindDTO>> csOrderBindLstGroupOrder = csOrderBindMap;
        String nick = csDTO.getNick();

        if (CollectionUtils.isEmpty(csOrderBindLst)){
            return null;
        }

        CsToOrderIndexDO indexDO = new CsToOrderIndexDO();

        indexDO.setShopId(jobShop.getShop().getShopId());
        indexDO.setDate(date);
        indexDO.setCsNick(nick);

        int practicalOrderedPaidItemNum = 0;
        int practicalOrderedPaidOrderedNum = 0;
        Set<String> practicalOrderedPaidPeople = new HashSet<>();
        double practicalOrderedPaidAmount = 0.0;

        int practicalOrderedOthersPaidItemNum = 0;
        int practicalOrderedOthersPaidOrderedNum = 0;
        Set<String> practicalOrderedOthersPaidPeople = new HashSet<>();
        double practicalOrderedOthersPaidAmount = 0.0;

        int othersPracticalOrderedSelfPaidItemNum = 0;
        int othersPracticalOrderedSelfPaidOrderedNum = 0;
        Set<String> othersPracticalOrderedSelfPaidPeople = new HashSet<>();
        double othersPracticalOrderedSelfPaidAmount = 0.0;

        int silenceOrderedSelfPaidItemNum = 0;
        int silenceOrderedSelfPaidOrderedNum = 0;
        Set<String> silenceOrderedSelfPaidPeople = new HashSet<>();
        double silenceOrderedSelfPaidAmount = 0.0;

        int silenceOrderedSelfItemNum = 0;
        int silenceOrderedSelfOrderNum = 0;
        Set<String> silenceOrderedSelfPeople = new HashSet<>();
        double silenceOrderedSelfAmount = 0.0;

        // 静默下单
        final Integer silenceOrdered = 12,silenceOrdered1 = 11;
        // 全静默跟进
        final Integer silenceAll = 3;
        Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop,
                date, date,
                csOrderBindLst.stream().map(CsOrderBindDTO::getOrderId).collect(Collectors.toList()));
        Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
        for (CsOrderBindDTO orderBind : csOrderBindLst) {
            // 静默订单
            if (Objects.equals(orderBind.getSilentFlag(),silenceOrdered)||Objects.equals(orderBind.getSilentFlag(),silenceOrdered1)){
                List<CsOrderBindDTO> csOrderBindDTOS = csOrderBindLstGroupOrder.get(orderBind.getOrderId());
                if (CollectionUtils.isNotEmpty(csOrderBindDTOS)) {
                    for (CsOrderBindDTO csOrderBindDTO : csOrderBindDTOS) {
                        //本人落实付款
                        if (Objects.equals(nick, csOrderBindDTO.getCsNick()) &&
                                Objects.equals(csOrderBindDTO.getType(), JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType())) {
                            List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(csOrderBindDTO.getOrderId());
                            if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                                for (OrderDetailDTO dto : orderDetailDTOS) {
                                    silenceOrderedSelfPaidItemNum += BaseUtils.getNonNull(dto.getItemNum());
                                }
                            }
                            silenceOrderedSelfPaidOrderedNum++;
                            silenceOrderedSelfPaidPeople.add(csOrderBindDTO.getBuyerNick());
                            silenceOrderedSelfPaidAmount += csOrderBindDTO.getOrderPayment();
                        }
                    }
                }
            }else if (Objects.equals(orderBind.getType(),silenceAll)) {
                // 全静默订单
                // 本人跟进
                if (Objects.equals(nick, orderBind.getCsNick())) {
                    List<CsOrderBindDTO> csOrderBindDTOS = csOrderBindLstGroupOrder.get(orderBind.getOrderId());
                    if (CollectionUtils.isNotEmpty(csOrderBindDTOS)) {
                        CsOrderBindDTO csOrderBindDTO = csOrderBindDTOS.get(0);
                        List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(csOrderBindDTO.getOrderId());
                        if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                            for (OrderDetailDTO dto : orderDetailDTOS) {
                                silenceOrderedSelfItemNum += BaseUtils.getNonNull(dto.getItemNum());
                            }
                        }
                        silenceOrderedSelfOrderNum++;
                        silenceOrderedSelfPeople.add(csOrderBindDTO.getBuyerNick());
                        silenceOrderedSelfAmount += csOrderBindDTO.getOrderPayment();
                    }
                }
            }


            // 本人订单
            if (Objects.equals(orderBind.getCsNick(),nick)){
                // 本人落实下单
                if (Objects.equals(orderBind.getType(), JudgeRuleTypeEnum.ORDER_BIND_TYPE_ORDER.getType())) {
                    List<CsOrderBindDTO> csOrderBindDTOS = csOrderBindLstGroupOrder.get(orderBind.getOrderId());
                    if (CollectionUtils.isNotEmpty(csOrderBindDTOS)) {
                        for (CsOrderBindDTO csOrderBindDTO : csOrderBindDTOS) {
                            // 本人落实下单本人落实付款
                            if (Objects.equals(csOrderBindDTO.getType(), JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType())
                                    && Objects.equals(csOrderBindDTO.getCsNick(), orderBind.getCsNick())) {
                                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(csOrderBindDTO.getOrderId());
                                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                                    for (OrderDetailDTO dto : orderDetailDTOS) {
                                        practicalOrderedPaidItemNum += BaseUtils.getNonNull(dto.getItemNum());
                                    }
                                }
                                practicalOrderedPaidOrderedNum++;
                                practicalOrderedPaidPeople.add(csOrderBindDTO.getBuyerNick());
                                //定金尾款同一天
                                if(null == csOrderBindDTO.getOrderPayment()){
                                    practicalOrderedPaidAmount += csOrderBindDTO.getOrderValidPayment();
                                }else{
                                    practicalOrderedPaidAmount += csOrderBindDTO.getOrderPayment();
                                }
                            }
                            // 本人落实下单他人落实付款
                            if (Objects.equals(csOrderBindDTO.getType(), JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType())
                                    && !Objects.equals(csOrderBindDTO.getCsNick(), orderBind.getCsNick())) {
                                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(csOrderBindDTO.getOrderId());
                                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                                    for (OrderDetailDTO dto : orderDetailDTOS) {
                                        practicalOrderedOthersPaidItemNum += BaseUtils.getNonNull(dto.getItemNum());
                                    }
                                }
                                practicalOrderedOthersPaidOrderedNum++;
                                practicalOrderedOthersPaidPeople.add(csOrderBindDTO.getBuyerNick());
                                if(null == csOrderBindDTO.getOrderPayment()) {
                                    othersPracticalOrderedSelfPaidAmount += csOrderBindDTO.getOrderValidPayment();
                                }else{
                                    othersPracticalOrderedSelfPaidAmount += csOrderBindDTO.getOrderPayment();
                                }
                            }
                        }
                    }

                }
            }else {
                // 他人订单
                // 他人落实下单
                if (Objects.equals(orderBind.getType(), JudgeRuleTypeEnum.ORDER_BIND_TYPE_ORDER.getType())) {
                    List<CsOrderBindDTO> csOrderBindDTOS = csOrderBindLstGroupOrder.get(orderBind.getOrderId());
                    if (CollectionUtils.isNotEmpty(csOrderBindDTOS)) {
                        for (CsOrderBindDTO csOrderBindDTO : csOrderBindDTOS) {
                            // 他人落实下单 本人落实付款
                            if (Objects.equals(csOrderBindDTO.getType(), JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType())
                                    && Objects.equals(csOrderBindDTO.getCsNick(), nick)) {
                                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(csOrderBindDTO.getOrderId());
                                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                                    for (OrderDetailDTO dto : orderDetailDTOS) {
                                        othersPracticalOrderedSelfPaidItemNum += BaseUtils.getNonNull(dto.getItemNum());
                                    }
                                }
                                othersPracticalOrderedSelfPaidOrderedNum++;
                                othersPracticalOrderedSelfPaidPeople.add(csOrderBindDTO.getBuyerNick());
                                if(null == csOrderBindDTO.getOrderPayment()){
                                    othersPracticalOrderedSelfPaidAmount += csOrderBindDTO.getOrderValidPayment();
                                }else{
                                    othersPracticalOrderedSelfPaidAmount += csOrderBindDTO.getOrderPayment();
                                }
                            }
                        }
                    }
                }
            }
        }

        // 本人落实下单，本人落实付款
        indexDO.setToOrderedPaidNum(practicalOrderedPaidPeople.size());
        indexDO.setToOrderedPaidGoodsNum(practicalOrderedPaidItemNum);
        indexDO.setToOrderedPaidOrderNum(practicalOrderedPaidOrderedNum);
        indexDO.setToOrderedPaidAmount(practicalOrderedPaidAmount);
        //本人落实下单，他人落实付款
        indexDO.setToOrderedNotPaidNum(practicalOrderedOthersPaidPeople.size());
        indexDO.setToOrderedNotPaidGoodsNum(practicalOrderedOthersPaidItemNum);
        indexDO.setToOrderedNotPaidOrderNum(practicalOrderedOthersPaidOrderedNum);
        indexDO.setToOrderedNotPaidAmount(practicalOrderedOthersPaidAmount);
        // 他人落实下单，本人落实付款
        indexDO.setToPaidNum(othersPracticalOrderedSelfPaidPeople.size());
        indexDO.setToPaidGoodsNum(othersPracticalOrderedSelfPaidItemNum);
        indexDO.setToPaidOrderNum(othersPracticalOrderedSelfPaidOrderedNum);
        indexDO.setToPaidAmount(othersPracticalOrderedSelfPaidAmount);
        // 静默下单本人落实付款
        indexDO.setSilentOrderToPaidNum(silenceOrderedSelfPaidPeople.size());
        indexDO.setSilentOrderToPaidGoodsNum(silenceOrderedSelfPaidItemNum);
        indexDO.setSilentOrderToPaidOrderNum(silenceOrderedSelfPaidOrderedNum);
        indexDO.setSilentOrderToPaidAmount(silenceOrderedSelfPaidAmount);
        // 全静默订单本人跟进
        indexDO.setSilentToFolowupNum(silenceOrderedSelfPeople.size());
        indexDO.setSilentToFolowupGoodsNum(silenceOrderedSelfItemNum);
        indexDO.setSilentToFolowupOrderNum(silenceOrderedSelfOrderNum);
        indexDO.setSilentToFolowupAmount(silenceOrderedSelfAmount);

        return indexDO;
    }

}
