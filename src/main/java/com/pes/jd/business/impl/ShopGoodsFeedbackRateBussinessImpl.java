package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopGoodsFeedbackRateBussiness;
import com.pes.jd.model.DTO.ShopGoodsFeedbackRateDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Param.ShopGoodsRateParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年04月16 11:07:07<br>
 */
@Service
public class ShopGoodsFeedbackRateBussinessImpl implements ShopGoodsFeedbackRateBussiness {
   @Resource
   private PopSubRestTemplate popSubRestTemplate;
    @Override
    public DataAnalysisVO<ShopGoodsFeedbackRateDTO> searchShopGoodsFeedbackRateLst(ShopCommonParam shop, ShopGoodsRateParam param, SortPageQuery sortPageQuery) throws Exception {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("sortPageQuery", sortPageQuery)
                .put("param", param)
                .put("shop", shop)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(),ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        RestResponseTypeRef<DataAnalysisVO<ShopGoodsFeedbackRateDTO>> resp=    popSubRestTemplate.postRest(serviceId,"/data/analysis/searchShopGoodsFeedbackRateLst", body, new ParameterizedTypeReference<RestResponseTypeRef<DataAnalysisVO<ShopGoodsFeedbackRateDTO>>>() {});
        if(resp.getSuccess()){
            return resp.getData();
        }else{
            DataAnalysisVO<ShopGoodsFeedbackRateDTO> result=new DataAnalysisVO<>();
            result.setCount(0);
            result.setDataList(Lists.newArrayList());
            result.setPageFlag(true);
            return result;
        }
    }
}
