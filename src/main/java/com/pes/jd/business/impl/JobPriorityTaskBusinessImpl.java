package com.pes.jd.business.impl;

import com.pes.jd.business.*;
import com.pes.jd.dao.*;
import com.pes.jd.data.converter.OrderNotPayConverter;
import com.pes.jd.data.converter.SingleFbpOrderConverter;
import com.pes.jd.data.converter.SinglePopOrderConverter;
import com.pes.jd.model.BO.OrderInfoBO;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DO.JobCalRecordDO;
import com.pes.jd.model.DO.JobPullApiRecordDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.reponse.ApiResponse;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;


@Service
public class JobPriorityTaskBusinessImpl implements JobPriorityTaskBusiness {

    private Logger logger = LoggerFactory.getLogger(JobPriorityTaskBusinessImpl.class);

    @Resource
    private DataPrepareBusiness dataPrepareBusiness;

    @Resource
    private OrderDao orderDao;

    @Resource
    private OrderDetailDao orderDetailDao;

    @Resource
    private OrderNotPayConverter orderNotPayConverter;

    @Resource
    private SinglePopOrderConverter singlePopOrderConverter;

    @Resource
    private SingleFbpOrderConverter singleFbpOrderConverter;

    @Resource
    private ShopSubcribeHandBusiness shopSubcribeHandBusiness;

    @Resource
    private OrderOutStockTimeDao orderOutStockTimeDao;

    @Resource
    private CsTypeDayBusiness csTypeDayBusiness;

    @Resource
    private ShopPvUvDayHandleBussiness shopPvUvDayHandleBussiness;

    @Resource
    private NoticeMessageBusiness noticeMessageBusiness;

    @Resource
    private PerformanceRuleBusiness performanceRuleBusiness;

    @Resource
    private CsChatHandleBussiness csChatHandleBussiness;

    @Resource
    private ChatHandleBusiness chatHandleBusiness;

    @Resource
    private ShopGoodsReviewBussiness shopGoodsReviewBussiness;

    @Resource
    private CustomerOrderDao customerOrderDao;

	@Resource
	private JobPullApiRecordDao jobPullApiRecordDao;

    @Resource
    private JobRecordDao jobRecordDao;
    /**
     * 首次登陆，初始化数据
     */
    @Override
    public Object initShopData(JobShopQuery jobShop, JobDateQuery jobDate,
                               boolean isDelData) throws Exception {
        jobShop.setHand(Boolean.FALSE);

        PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
        jobDate.setValidDate(validDate);

        jobDate.getCommonDates().add(jobDate.getDate());
        jobDate.getDates().addAll(DateUtil.splitDate(validDate.getEnquiry2PayValidDate(), validDate.getDate()));

        String title = jobShop.getShop().getTitle();
//        try {
//            noticeMessageBusiness.sendInitShopDataSuccessMessage(jobShop.getShop(), 1, "initDataFlag");
//        } catch (Exception e) {
//            logger.error(title+"pop-sendInitShopDataSuccessMessage-异常", e);
//        }
//        logger.info(title+"pop-sendInitShopDataSuccessMessage-success");
        /*
         * 客服每日类型(售前 | 售后 - 温辉
         */
        csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

        dataPrepareBusiness.pullShopData(jobShop, jobDate, isDelData,false);

        dataPrepareBusiness.calShopData(jobShop, jobDate, isDelData);
        return null;
    }

	/**
	 * 拉取并计算数据
	 * @param jobShop
	 * @param jobDate
	 * @param isDelData
	 * @return
	 * @throws Exception
	 */
	@Override
	public Object pullAndCalShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData,boolean isYd) throws Exception {

        jobShop.setHand(Boolean.FALSE);

        PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
        jobDate.setValidDate(validDate);

        jobDate.getCommonDates().add(jobDate.getDate());
        jobDate.getDates().addAll(DateUtil.splitDate(validDate.getEnquiry2PayValidDate(), validDate.getDate()));

		/*
		 * 客服每日类型(售前 | 售后 - 温辉
		 */
		long s = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

            logger.debug("[{}] pull data start",jobShop.getShop().getTitle());
        }

        csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

        dataPrepareBusiness.pullShopData(jobShop, jobDate, isDelData,isYd);

		if(jobShop.getPullRecord().getResult()){
			dataPrepareBusiness.calShopData(jobShop, jobDate, isDelData);
		}else{
            //feature_v28 失败后更新cal执行结果
            JobShopDTO shop = jobShop.getShop();
            JobCalRecordDTO existCalRecord = jobRecordDao.getJobCalRecordByShopIdAndDate(shop, jobDate.getDate());
            if(existCalRecord != null){
                JobCalRecordDO calRecord = new JobCalRecordDO(shop.getShopId(), jobDate.getDate(), new Date());
                calRecord.setResult(Boolean.FALSE);
                calRecord.setMsg("前置拉取任务失败");
                jobRecordDao.deleteJobCalRecordByShopIdAndDate(shop, jobDate.getDate());
                jobRecordDao.insertJobCalRecord(shop, calRecord, jobDate.getDate());
            }
        }
		if(logger.isDebugEnabled()){

            logger.debug("[{}] pull and cal data finish, time:{}s",jobShop.getShop().getTitle(),(System.currentTimeMillis()-s)/1000);
        }
        return null;
    }

    /**
     * 计算数据
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     * @return
     * @throws Exception
     */
    @Override
    public Object calShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {

        jobShop.setHand(Boolean.FALSE);
        PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
        jobDate.setValidDate(validDate);

        jobDate.getCommonDates().add(jobDate.getDate());
        jobDate.getDates().addAll(DateUtil.splitDate(validDate.getEnquiry2PayValidDate(), validDate.getDate()));

        /*
         * 客服每日类型(售前 | 售后 - 温辉
         */
        csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

        dataPrepareBusiness.calShopData(jobShop, jobDate, isDelData);

        return null;
    }

    /**
     * 手动拉取登陆记录
     */
    @Override
    public Object pullShopLoginLogDataForHand(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        logger.info("手动拉取登陆记录job");
        final boolean isHand = true;
        dataPrepareBusiness.pullShopLoginLogData(jobShop, jobDate, isDelData);
        return null;
    }


    /**
     * 每日登陆记录job
     */
    @Override
    public void pullShopLoginLogData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        /*
         * 客服每日类型(售前 | 售后 - 温辉
         */
        csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

        dataPrepareBusiness.pullShopLoginLogData(jobShop, jobDate, isDelData);
    }

	/**
	 * 值班记录每日job，根据有效天延迟时间拉取店铺值班记录
	 */
	@Override
	public Object pullShopLoginLogDataByDelayHours(JobShopQuery jobShop, JobDateQuery jobDate, 
			boolean isDelData) {
        System.out.println("shop loginLog by schedulingTimeDot   start");
		JobPullApiRecordDO jobPullApiRecordDO = new JobPullApiRecordDO();
		jobPullApiRecordDO.setShopId(jobShop.getShop().getShopId());
		jobPullApiRecordDO.setDate(jobDate.getDate());
		jobPullApiRecordDO.setType(Byte.valueOf("2"));
		jobPullApiRecordDO.setModified(new Date());
		try {
            /*
             * 判断商铺是否token已过期或者不存在
             */
            long s1 = System.currentTimeMillis();
            shopSubcribeHandBusiness.pullShopSubcribeInfo(jobShop.getShop().getSessionKey());
            long e1 = System.currentTimeMillis();
            logger.info("job-invoke-api-{}-f1-time={}ms - method[pullShopSubcribeInfo-api]", jobShop.getShop().getShopId(), (e1 - s1));


            /*
             * 客服每日类型(售前 | 售后 - 温辉
             */
            csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

            dataPrepareBusiness.pullShopLoginLogData(jobShop, jobDate, isDelData);

			jobPullApiRecordDO.setResult(Byte.valueOf("1"));
		}catch (Exception e){
			jobPullApiRecordDO.setResult(Byte.valueOf("0"));
			String message = e.getMessage();
			if (StringUtils.isNotBlank(message)){
				try {
					int length = message.length()>125?125:message.length();
					jobPullApiRecordDO.setMsg(message.substring(0,length));
				}catch (Exception ee){
					ee.printStackTrace();
				}
			}

			e.printStackTrace();
		}
		try {
			jobPullApiRecordDao.deleteByShopIdAndDate(jobPullApiRecordDO.getShopId(), jobPullApiRecordDO.getDate(), jobPullApiRecordDO.getType(), jobShop.getShop().getSchemaId());
			jobPullApiRecordDao.insert(jobPullApiRecordDO, jobDate.getDate(), jobShop.getShop().getSchemaId());
		}catch (Exception e){
			logger.error(e.getMessage(),e);
		}
		return null;
	}

    @Override
    public Object pullChatLog(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        try {
            /*
             * 客服每日类型(售前 | 售后 - 温辉
             */
            csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

            /*
             *  聊天会话，聊天对象 ，聊天记录 (in use) - 邵美
             */
            csChatHandleBussiness.pullChatPeersAndChatlogs(jobShop, jobDate, isDelData);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public Object calChatLog(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        jobShop.setHand(Boolean.FALSE);
        PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
        jobDate.setValidDate(validDate);

        jobDate.getCommonDates().add(jobDate.getDate());
        jobDate.getDates().addAll(DateUtil.splitDate(validDate.getEnquiry2PayValidDate(), validDate.getDate()));

        /*
         * 客服每日类型(售前 | 售后 - 温辉
         */
        csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);
        /*
         * 聊天处理(接待处理，接待过滤，会话处理，会话指标，接待指标)(in use) - deru_zi
         * 【今日19号-算昨日 - 18号数据】
         */
        chatHandleBusiness.handleCommonChat(jobShop, jobDate, isDelData);


        return null;
    }

    @Override
    public int pullOrderOutStockTime(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception {
        Date orderStartDate = DateFormatUtils.parseYMdHms(orderCreatedTime);
        OrderDTO orderModel = null;
        try {
            if (shop.getColType() == 0) {
//                orderModel = singlePopOrderConverter.getSinglePopOrder(shop, orderId).getOrder();
            } else {
//                orderModel = singleFbpOrderConverter.getSingleFbpOrder(shop, orderId).getOrder();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        if (orderModel == null || orderModel.getOrderId() == null) {
            orderModel = new OrderDTO();
            orderModel.setOrderId(orderId);
            orderModel.setStatus("WAIT_GOODS_RECEIVE_CONFIRM");
            orderModel.setModified(new Date());
            orderModel.setOrderType(0);
            orderModel.setCreated(orderStartDate);
        } else {
            if (!"WAIT_GOODS_RECEIVE_CONFIRM".equals(orderModel.getStatus())) {
                orderModel.setStatus("WAIT_GOODS_RECEIVE_CONFIRM");
            }
        }

        int orderNum = 0;
        int orderGoodSkuNum = 0;
        orderNum += orderDao.updateOrderByOrderId(shop, orderModel.getOrderId(), orderModel.getModified(), orderCreatedTime, orderModel.getStatus());
        logger.debug("修改订单表中出库时间数据条数{}", orderNum);
        //orderGoodSkuNum += orderDetailDao.updateOrderByOrderId(shop, orderId, orderModel.getModified(), orderCreatedTime);
        orderOutStockTimeDao.deleteOrderOutStockTime(shop, orderModel.getCreated(), orderId);
        OrderOutStockTimeDTO orderOutStockTimeDTO = new OrderOutStockTimeDTO();
        orderOutStockTimeDTO.setCreated(orderStartDate);
        orderOutStockTimeDTO.setOrderId(orderId);
        orderOutStockTimeDTO.setShopId(shop.getShopId());
        orderOutStockTimeDTO.setOutStockTime(orderModel.getModified());
        orderOutStockTimeDTO.setOrderType(orderModel.getOrderType());
        orderOutStockTimeDTO.setStatus(orderModel.getStatus());
        int outStocktimeNum = orderOutStockTimeDao.insertOrderOutStockTime(shop, orderModel.getCreated(), orderOutStockTimeDTO);
        logger.debug("插入出库时间表中出库时间数据条数{}", outStocktimeNum);
        return orderNum + orderGoodSkuNum;
    }


    @Override
    public Object pullShopPvUvDataForHand(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        logger.info("拉取pvuvjob开始 shopId :{} date :{}", jobShop.getShop().getShopId(), jobDate.getDate());
        shopPvUvDayHandleBussiness.pullShopPvUvDayInfo(jobShop, jobDate, isDelData);
        return null;
    }

    @Override
    public ApiResponse pullOrderCancel(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception {
        OrderInfoBO orderModel;
        if (shop.getColType() == 0) {
            orderModel = singlePopOrderConverter.getSinglePopOrder(shop, orderId);
        } else {
            orderModel = singleFbpOrderConverter.getSingleFbpOrder(shop, orderId);
        }

        if (orderModel.getOrder() == null) {
            return getReturnInfo(true, "search order", "0");
        }

        Date orderCreatedDate = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(orderModel.getOrder().getCreated()));
        orderDao.deleteOrderCanecelByOrderId(shop, orderId, orderCreatedDate);
        orderModel.getOrder().setStatus("TRADE_CANCELED");
        int m = orderDao.insertOrderCancel(shop, orderCreatedDate, orderModel.getOrder());
        logger.debug("insert order cancel num{}", m);
        orderDetailDao.deleteOrderCancelGoodsSkuByOid(orderId, shop, orderCreatedDate);
        int n = orderDetailDao.insertOrderCancelGoodsSku(shop, orderCreatedDate, orderModel.getOrderDetail());
        logger.debug("insert order good sku cancel num{}", n);
        return getReturnInfo(true, "search order", "0");
    }

    private ApiResponse getReturnInfo(boolean successFlag, String msg, String code) {
        ApiResponse apiResponse = new ApiResponse();
        apiResponse.setSuccess(successFlag);
        apiResponse.setRpMsg(msg);
        apiResponse.setRpCode(code);
        return apiResponse;
    }

    /**
    计算店铺中差评
     */
    @Override
    public Object calOrderEvaluate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        jobShop.setHand(Boolean.FALSE);
        PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
        jobDate.setValidDate(validDate);

        jobDate.getCommonDates().add(jobDate.getDate());
        jobDate.getDates().addAll(DateUtil.splitDate(validDate.getEnquiry2PayValidDate(), validDate.getDate()));

        /*
         * 客服每日类型(售前 | 售后 - 温辉
         */
        csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);
        /*
         * 客服绑定订单中差评计算  (in use) - 王凯
         */
        shopGoodsReviewBussiness.handleShopCsOrderReview(jobShop, jobDate, isDelData);
        return null;
    }

    @Override
    public int pullOrderCreate(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception {
        OrderInfoBO orderModel = new OrderInfoBO();
        try {
            orderModel = orderNotPayConverter.getOrderNotPay(shop, orderId);
        } catch (Exception ex) {
            if(logger.isDebugEnabled()){
                logger.debug("pullOrderCreate  getOrderNotPay error============");
            }
        }
         if (orderModel.getOrder() == null) {
           if (shop.getColType() == 1) {
                orderModel = singleFbpOrderConverter.getSingleFbpOrder(shop, orderId);
             } else {
                 orderModel = singlePopOrderConverter.getSinglePopOrder(shop, orderId);
             }
        }
        if (orderModel.getOrder() == null) {
            if(logger.isDebugEnabled()){
                logger.debug("pullOrderCreate  订单不存在{}",orderId);
            }
            return 0;
        }
        Date orderCreatedDate = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(orderModel.getOrder().getCreated()));
       int count = customerOrderDao.selectOrderById(shop, orderId, orderCreatedDate);
       if(count>0) {
    	   customerOrderDao.deleteOrderByOrderId(shop, orderId, orderCreatedDate);
       }
        orderModel.getOrder().setStatus("TRADE_CREATED");
        int num = customerOrderDao.insertOrder(shop, orderCreatedDate, orderModel.getOrder());
        if(logger.isDebugEnabled()){
            logger.debug("insert order create num{}", num);
        }
        orderDetailDao.deleteOrderGoodsSkuByOid(orderId, shop, orderCreatedDate);
        int result = orderDetailDao.insertOrderGoodSku(shop, orderCreatedDate, orderModel.getOrderDetail());
        if(logger.isDebugEnabled()){
            logger.debug("insert order good sku Create num {}", result);
        }
        //订单对应 用户信息和发票信息对应入库
        customerOrderDao.deleteOrderConsignByOrderId(shop, orderId, orderCreatedDate);
        if(null!=orderModel.getOrderConsignDTO()) {
        	customerOrderDao.insertOrderConsign(shop,orderCreatedDate,orderModel.getOrderConsignDTO());
        }
        customerOrderDao.deleteOrderInvoiceByOrderId(shop, orderId, orderCreatedDate);
        if(null!=orderModel.getOrderInvoiceDTO() && null!=orderModel.getOrderInvoiceDTO().getOrderId()) {
        	customerOrderDao.insertOrderInvoice(shop, orderCreatedDate, orderModel.getOrderInvoiceDTO());
        }else {
//        	插入一条空记录
        	OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
        	orderInvoiceDTO.setInvoiceType(new Byte("0"));
        	orderInvoiceDTO.setOrderId(orderModel.getOrder().getOrderId());
        	customerOrderDao.insertOrderInvoice(shop, orderCreatedDate, orderInvoiceDTO);

        }

        //处理订单留言信息
        OrderDetailInfoDTO orderDetailInfoDTO = new OrderDetailInfoDTO();
        orderDetailInfoDTO.setCustRemark(orderModel.getOrder().getComment());
        orderDetailInfoDTO.setOrderCreated(new Date());
        orderDetailInfoDTO.setOrderId(orderModel.getOrder().getOrderId());
        orderDetailInfoDTO.setShopId(orderModel.getOrder().getShopId());

        customerOrderDao.delOrderDetail(shop, orderCreatedDate, orderDetailInfoDTO);

        customerOrderDao.insertOrderDetail(shop, orderCreatedDate, orderDetailInfoDTO);


        return 1;
    }

    @Override
    public int pullOrderPay(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception {
        OrderInfoBO orderModel;
        if (shop.getColType() == 1) {
            orderModel = singleFbpOrderConverter.getSingleFbpOrder(shop, orderId);
        } else {
            orderModel = singlePopOrderConverter.getSinglePopOrder(shop, orderId);
        }
        if (orderModel.getOrder() == null) {
            if(logger.isDebugEnabled()){
                logger.debug("pullOrderPay  订单不存在{}",orderId);
            }
            return 0;
        }
        Date orderCreatedDate = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(orderModel.getOrder().getCreated()));
        customerOrderDao.deleteOrderByOrderId(shop, orderId, orderCreatedDate);
        orderModel.getOrder().setStatus("TRADE_PAYMENT");
        int num = customerOrderDao.insertOrder(shop, orderCreatedDate, orderModel.getOrder());
        logger.debug("insert order pay num{}", num);
        orderDetailDao.deleteOrderGoodsSkuByOid(orderId, shop, orderCreatedDate);
        int result = orderDetailDao.insertOrderGoodSku(shop, orderCreatedDate, orderModel.getOrderDetail());
        logger.debug("insert order sku pay num{}", result);
        //订单对应 用户信息和发票信息对应入库
        customerOrderDao.deleteOrderConsignByOrderId(shop, orderId, orderCreatedDate);
        if (null != orderModel.getOrderConsignDTO()) {
            customerOrderDao.insertOrderConsign(shop, orderCreatedDate, orderModel.getOrderConsignDTO());
        }
        customerOrderDao.deleteOrderInvoiceByOrderId(shop, orderId, orderCreatedDate);
        if(null!=orderModel.getOrderInvoiceDTO() && null!=orderModel.getOrderInvoiceDTO().getOrderId()) {
        	customerOrderDao.insertOrderInvoice(shop, orderCreatedDate, orderModel.getOrderInvoiceDTO());
        }else {
//        	插入一条空记录
        	OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
        	orderInvoiceDTO.setInvoiceType(new Byte("0"));
        	orderInvoiceDTO.setOrderId(orderModel.getOrder().getOrderId());
        	customerOrderDao.insertOrderInvoice(shop, orderCreatedDate, orderInvoiceDTO);
        }


        //处理订单留言信息
        OrderDetailInfoDTO orderDetailInfoDTO = new OrderDetailInfoDTO();
        orderDetailInfoDTO.setCustRemark(orderModel.getOrder().getComment());
        orderDetailInfoDTO.setOrderCreated(new Date());
        orderDetailInfoDTO.setOrderId(orderModel.getOrder().getOrderId());
        orderDetailInfoDTO.setShopId(orderModel.getOrder().getShopId());

        customerOrderDao.delOrderDetail(shop, orderCreatedDate, orderDetailInfoDTO);

        customerOrderDao.insertOrderDetail(shop, orderCreatedDate, orderDetailInfoDTO);

        return 1;
    }

    @Override
    public int pullOrderFinish(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception {
        OrderInfoBO orderModel;
        if (shop.getColType() == 1) {
            orderModel = singleFbpOrderConverter.getSingleFbpOrder(shop, orderId);
        } else {
            orderModel = singlePopOrderConverter.getSinglePopOrder(shop, orderId);
        }
        if (orderModel.getOrder() == null) {
        	logger.error("pullOrderFinish  订单不存在{}",orderId);
            return 0;
        }
        Date orderCreatedDate = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(orderModel.getOrder().getCreated()));
        customerOrderDao.deleteOrderByOrderId(shop, orderId, orderCreatedDate);
        orderModel.getOrder().setStatus("FINISHED_L");
        int num = customerOrderDao.insertOrder(shop, orderCreatedDate, orderModel.getOrder());
        logger.debug("insert order Finish num{}", num);
        orderDetailDao.deleteOrderGoodsSkuByOid(orderId, shop, orderCreatedDate);
        int result = orderDetailDao.insertOrderGoodSku(shop, orderCreatedDate, orderModel.getOrderDetail());
        logger.debug("insert order sku Finish num{}", result);
        //订单对应 用户信息和发票信息对应入库
        customerOrderDao.deleteOrderConsignByOrderId(shop, orderId, orderCreatedDate);
        if (null != orderModel.getOrderConsignDTO()) {
            customerOrderDao.insertOrderConsign(shop, orderCreatedDate, orderModel.getOrderConsignDTO());
        }
        customerOrderDao.deleteOrderInvoiceByOrderId(shop, orderId, orderCreatedDate);
        if(null!=orderModel.getOrderInvoiceDTO() && null!=orderModel.getOrderInvoiceDTO().getOrderId()) {
        	customerOrderDao.insertOrderInvoice(shop, orderCreatedDate, orderModel.getOrderInvoiceDTO());
        }else {
//        	插入一条空记录
        	OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
        	orderInvoiceDTO.setInvoiceType(new Byte("0"));
        	orderInvoiceDTO.setOrderId(orderModel.getOrder().getOrderId());
        	customerOrderDao.insertOrderInvoice(shop, orderCreatedDate, orderInvoiceDTO);

        }
        //处理订单留言信息
        OrderDetailInfoDTO orderDetailInfoDTO = new OrderDetailInfoDTO();
        orderDetailInfoDTO.setCustRemark(orderModel.getOrder().getComment());
        orderDetailInfoDTO.setOrderCreated(new Date());
        orderDetailInfoDTO.setOrderId(orderModel.getOrder().getOrderId());
        orderDetailInfoDTO.setShopId(orderModel.getOrder().getShopId());

        customerOrderDao.delOrderDetail(shop, orderCreatedDate, orderDetailInfoDTO);

        customerOrderDao.insertOrderDetail(shop, orderCreatedDate, orderDetailInfoDTO);

        return 1;
    }

}

