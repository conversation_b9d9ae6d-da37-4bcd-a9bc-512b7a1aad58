package com.pes.jd.business.impl;

import com.pes.jd.business.ShopGoodSkuHandleBussiness;
import com.pes.jd.data.converter.ShopCategoryAndGoodAndSkuDataConverter;
import com.pes.jd.model.Param.GoodskuParam;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: aiJun
 * @Date: 2019-05-28 13:16
 * @Version 1.0
 */
@Service
public class ShopGoodSkuHandleBussinessImpl implements ShopGoodSkuHandleBussiness {

    private static Logger logger = LoggerFactory.getLogger(ShopGoodSkuHandleBussinessImpl.class);

    @Resource
    private ShopCategoryAndGoodAndSkuDataConverter shopCategoryAndGoodAndSkuDataConverter;

    @Override
    public void pullGoodSkus(JobShopQuery jobShop, GoodskuParam goodskuParamPojo, boolean isDelData) throws Exception {
        logger.info("aSinglePull goods skus");

        JobDateQuery jobDate= new JobDateQuery();
        jobDate.setStartDate(DateFormatUtils.getStartTimeOfDate(DateFormatUtils.parseYMd(goodskuParamPojo.getStartDateStr())));
        jobDate.setEndDate(DateFormatUtils.getEndTimeOfDate(DateFormatUtils.parseYMd(goodskuParamPojo.getEndDateStr())));
        shopCategoryAndGoodAndSkuDataConverter.persistAllShopGoodsSkuByThread(jobShop, jobDate,isDelData);
//        if (goodskuParamPojo.getPullType().equals("1")){//按照时间不sku
//            shopCategoryAndGoodAndSkuDataConverter.persistShopGoodsSku(jobShop,goodskuParamPojo,isDelData);
//        }else if(goodskuParamPojo.getPullType().equals("2")){//拉取goodSku
//            shopCategoryAndGoodAndSkuDataConverter.persistShopGood(jobShop,goodskuParamPojo,isDelData);
//        }

    }
}
