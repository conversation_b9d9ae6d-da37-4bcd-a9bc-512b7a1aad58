package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.PayManagerService;
import com.pes.jd.model.Param.PayParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

/**
 * @Author: aiJun
 * @Date: 2019-09-21 1:02
 * @Version 1.0
 */
@Service
public class PayManagerServiceImpl implements PayManagerService {
    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Override
    public ApiResponse buySmsService(PayParam payParam) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("payParam", JSONObject.toJSON(payParam))
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRest(serviceId, "/payManager/wxpay/buySmsService", body);
    }

    @Override
    public ApiResponse checkOrderStatus(String orderId) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("orderId", orderId)
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRest(serviceId, "/payManager/wxpay/checkOrderStatus", body);
    }
}
