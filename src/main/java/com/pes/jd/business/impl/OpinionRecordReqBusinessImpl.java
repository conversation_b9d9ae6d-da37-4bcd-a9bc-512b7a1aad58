package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.OpinionRecordReqBusiness;
import com.pes.jd.model.Param.OpinionParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class OpinionRecordReqBusinessImpl implements OpinionRecordReqBusiness {
	
	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;

	@Override
	public ApiResponse insertOpinionRecord(OpinionParam param) throws Exception{
		HttpEntity<Object> body = RequestEntityBuilder.builder()
    			.put("param", param).toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		ApiResponse	apiResponse = usermgrRestTemplate.postRest(serviceId, "/opinion/insertOpinionRecord", body);
		return apiResponse;
	}
}
