package com.pes.jd.business.impl;

import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.CsChatSessionServiceIndexHandlerBusiness;
import com.pes.jd.business.PerformanceRuleBusiness;
import com.pes.jd.dao.*;
import com.pes.jd.framework.StopWatch;
import com.pes.jd.model.BO.ChatSessionsBO;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.MapUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@SuppressWarnings({"UnclearExpression", "SpellCheckingInspection", "Duplicates"})
@Service
public class CsChatSessionServiceIndexHandlerBusinessImpl implements CsChatSessionServiceIndexHandlerBusiness {

    private static final Logger logger = LoggerFactory.getLogger(CsPerformanceHandleBusinessImpl.class);

    @Resource
    private CsChatlogDao chatlogDao;

    @javax.annotation.Resource
    private CsChatSessionServiceIndexDao csChatSessionServiceIndexDao;

    @Resource
    private CsChatSessionDao csChatSessionDao;

    @Resource
    private CsChatpeerDao csChatpeerDao;

    @Resource
    private ShopTeamSessionServiceIndexDao shopTeamSessionServiceIndexDao;

    @Resource
    private ReceiveSessionNumHourlyDao receiveSessionNumHourlyDao;

    @Resource
    private PerformanceRuleBusiness performanceRuleBusiness;

    @Resource
    private ReceiveSessionPressureDao receiveSessionPressureDao;

    @Resource
    private CsServiceSendEvalDao csServiceSendEvalDao;

    @Resource
    private CsServiceEvaluationDetailDao csServiceEvaluationDetailDao;

    @Resource
    private CsLeaveMsgDao csLeaveMsgDao;

    /*指定接待压力计算是否在前后几分钟*/
    private static final int RECEIVE_FLAG_CAL = 2;

    private static final ThreadLocal<StopWatch> STOP_WATCH = ThreadLocal.withInitial(StopWatch::new);

    @SuppressWarnings("Duplicates")
    @Override
    @Deprecated
    public void handleCsSessionReceiveQuality(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        com.pes.jd.framework.StopWatch stopWatch = STOP_WATCH.get();
        stopWatch.start("handler cs session receiveQuality");
        List<Date> dates = jobDate.getCommonDates();
        if (dates.isEmpty()) {
            logger.warn("req dates is empty");
            return;
        }
        List<CsDTO> csLst = jobShop.getCsLst();
        /*预处理*/
        JobShopDTO shop = jobShop.getShop();
        /*按照每天，每个客服，多个聊天会话,每个聊天会话,多个聊天*/
        for (Date date : dates) {
            final Long shopId = shop.getShopId();
            final String schemaId = shop.getSchemaId();

            date = DateUtils.truncate(date,Calendar.DATE);
            final Date startDate = date;
            final Date endDate = com.pes.jd.util.DateUtils.getEndTimeOfDate(date);



            List<CsChatSessionServiceIndexDO> dayCsChatSessionServiceIndex = new ArrayList<>(40);
            final List<CsChatSessionDO> dayCsChatSessions = new ArrayList<>(csLst.size()*20);
            final List<ChatlogDTO> chatLogs = new ArrayList<>(csLst.size()*100);
            if (isDelData) {
                shopTeamSessionServiceIndexDao.deleteDate(shopId, schemaId, date);
                csChatSessionServiceIndexDao.deleteByTimePoint(date, schemaId, shopId, null);
                receiveSessionPressureDao.deleteByTimePoint(date, DateUtils.addDays(date, 1), schemaId, shop.getShopId(), null);
                receiveSessionNumHourlyDao.deleteByTimePoint(date, schemaId, shop.getShopId(), null);
            }
            for (CsDTO csDTO : csLst) {

                logger.debug("会话计算：{} 开始",csDTO.getNick());
                final String nick = csDTO.getNick();
                /*接待过滤的*/
                final Set<String> filterNick = csChatpeerDao.searchFillterBuyer(schemaId, shop.getShopId(), date, nick);
                logger.debug("查询{}客服对应的chatpeer 耗时{}秒",nick,stopWatch.watchSeconds());
                /*客服留言*/
                final List<CsLeaveMsgDTO> csLeaveMsgs = csLeaveMsgDao.searchByDate(startDate, endDate, shopId, schemaId,nick);
                logger.debug("查询{}客服对应的留言 耗时{}秒",nick,stopWatch.watchSeconds());
                /*客服对应的会话，和会话对应的聊天记录 session 按照开始时间排序过了*/
                final List<CsChatSessionDTO> nickChatSessions = csChatSessionDao.searchAllByTime(startDate, endDate, shopId, schemaId, nick);
                logger.debug("查询{}客服对应会话 耗时{}秒",nick,stopWatch.watchSeconds());
                final List<ChatlogDTO> nickChatLogs = chatlogDao.searchAllByTime(shopId, startDate, DateUtils.addDays(endDate, 1/*chatlog推1天*/), schemaId, nick);
                logger.debug("查询{}客服对应聊天记录 耗时{}秒",nick,stopWatch.watchSeconds());
                // check chatLog 排序
                final Map<CsChatSessionDTO, List<ChatlogDTO>> chatSessions =
                        MapUtils.extract(nickChatSessions, CsChatSessionDO::getSid,nickChatLogs, ChatlogDTO::getSid);
                logger.debug("处理{}客服对应聊天会话（将一个会话和多个聊天记录关联） 耗时{}秒",nick,stopWatch.watchSeconds());
                /*客服评价*/
                final List<CsServiceEvaluationDetailDTO> csServiceEvaluation = csServiceEvaluationDetailDao.searchByDate(shopId, startDate, endDate, schemaId,nick);
                logger.debug("查询{}客服对应客服评价 耗时{}秒",nick,stopWatch.watchSeconds());
                /*客服邀评*/
                final List<CsServiceSendEvalDO> csServiceSendEval = csServiceSendEvalDao.searchByDate(shopId, startDate, endDate, schemaId,nick);
                logger.debug("查询{}客服对应客服邀评 耗时{}秒",nick,stopWatch.watchSeconds());
                if (org.apache.commons.collections.MapUtils.isEmpty(chatSessions)){
                    logger.debug("会话计算：{} 结束，无会话",csDTO.getNick());
                    continue;
                }
                /*聚合 chatSession chatLog*/
                dayCsChatSessions.addAll(nickChatSessions);
                chatLogs.addAll(nickChatLogs);




                date = DateUtils.truncate(date,Calendar.DAY_OF_MONTH);
                /*对应客服下的聊天记录和聊天会话*/
                /*计算chat-session-service-index 同时计算chat-session*/
                calChatSession(
                        filterNick/*此客服接待过滤掉的买家nick*/,
                        jobShop,
                        chatSessions/*这天->这个客服->聊天会话->多个聊天记录*/,
                        csLeaveMsgs/*这天->这个客服->对应的留言记录*/,
                        csServiceEvaluation/*这天->这个客服->对应的服务评价*/,
                        csServiceSendEval/*这天->这个客服->对应的发送评价记录*/
                );
                // 此对象只为了传递数据
                ChatSessionsBO chatSessionsBO = new ChatSessionsBO();
                /*计算接待压力*/
                calReceivePressure(jobShop,csDTO,date,chatSessions.keySet(),chatSessionsBO);
                /*计算客服服务指标*/
                calChatSessionService(jobShop,csDTO,date,chatSessions,dayCsChatSessionServiceIndex,chatSessionsBO);
                logger.debug("会话计算：{} 结束,耗时{}秒",csDTO.getNick(),stopWatch.watchSeconds());
            }
            // 处理转发
            handlerForword(jobShop, date,dayCsChatSessionServiceIndex, dayCsChatSessions, chatLogs);
            // 添加客服每日 服务指标
            csChatSessionServiceIndexDao.insertBatch(date,dayCsChatSessionServiceIndex,schemaId);
            /*店铺的客服服务指标汇总数据*/
            handlerShop(csLst,jobShop, date,dayCsChatSessionServiceIndex, dayCsChatSessions);
        }
        logger.info("计算客服会话服务指标结束, 耗时 {}秒 ",(stopWatch.stopSeconds()));
        STOP_WATCH.remove();
    }

    private void handlerShop(List<CsDTO> csLst, JobShopQuery jobShop, Date date, List<CsChatSessionServiceIndexDO> csChatSessionServiceIndexDos, List<CsChatSessionDO> csChatSessionDOS) {
        final JobShopDTO shop = jobShop.getShop();
        final Long shopId = shop.getShopId();
        final String schemaId = shop.getSchemaId();
        Date startDate = date;
        Date endDate = com.pes.jd.util.DateUtils.getEndTimeOfDate(date);
        /*店铺级别的工作量缓存*/
        ShopTeamSessionServiceIndexDO shopTeamSessionServiceIndexDO = new ShopTeamSessionServiceIndexDO();
        CommonUtils.countBean(csChatSessionServiceIndexDos, () -> shopTeamSessionServiceIndexDO);
        shopTeamSessionServiceIndexDO.setShopId(shopId);
        shopTeamSessionServiceIndexDO.setDate(date);
        /*留言咨询( 覆盖逻辑，先使用直接查表的方式，然后chatSession不缓存 )*/
        shopTeamSessionServiceIndexDO.setLeaveMsgAdvisorySessionNum(
                (int) csChatSessionDOS.stream().filter(k->k.getConsult()&&k.getSessionType() ==2).count()
        );
        shopTeamSessionServiceIndexDO.setLeaveMsgAdvisorySessionNum(csLeaveMsgDao.leaveConsult(
                shopId, startDate, endDate, schemaId
        ));
        //首次平均响应、平均响应、平均会话时长 不可以直接聚合，需要除一下
        shopTeamSessionServiceIndexDO.setAvgRespTimeFirst(shopTeamSessionServiceIndexDO.getAvgRespTimeFirst() == null ? 0 : shopTeamSessionServiceIndexDO.getAvgRespTimeFirst() / csLst.size());
        shopTeamSessionServiceIndexDO.setAvgRespTime(shopTeamSessionServiceIndexDO.getAvgRespTime() == null ? 0 : shopTeamSessionServiceIndexDO.getAvgRespTime() / csLst.size());
        shopTeamSessionServiceIndexDO.setAvgSessionDurationTime(shopTeamSessionServiceIndexDO.getAvgSessionDurationTime() == null ? 0 : shopTeamSessionServiceIndexDO.getAvgSessionDurationTime() / csLst.size());
        shopTeamSessionServiceIndexDao.insert(shopTeamSessionServiceIndexDO, schemaId);
    }

    @Deprecated
    private void handlerForword(JobShopQuery jobShopQuery, Date date,
                                List<CsChatSessionServiceIndexDO> csChatSessionServiceIndexLst,
                                List<CsChatSessionDO> csChatSessionLst,
                                List<ChatlogDTO> chatlogLst) {
        // 转出的会话
        List<CsChatSessionDO> forWordOut = csChatSessionLst.stream().filter(k->k.getSessionType()==2).collect(Collectors.toList());

        // 顾客id ->  ( key:chatSession value:(List)chatLog )
        Map<String, Map<CsChatSessionDO, List<ChatlogDTO>>> buyerSessions = new HashMap<>();
        JobShopDTO shop = jobShopQuery.getShop();
        final Long shopId = shop.getShopId();
        final String schemaId = shop.getSchemaId();
        /*找转入*/
        if(CollectionUtils.isNotEmpty(csChatSessionLst)){
            final MultiValueMap<CsChatSessionDO, ChatlogDTO> chatSession =
                    MapUtils.extract(csChatSessionLst, CsChatSessionDO::getSid, chatlogLst, ChatlogDTO::getSid);
            buyerSessions = MapUtils.extract(chatSession, CsChatSessionDO::getCustomer);
        }
        // 转入的会话ID
        Set<String> forWordInSet = new HashSet<>();
        /*根据转出找转入*/
        for (CsChatSessionDO csChatSessionDO : forWordOut) {
            final Map<CsChatSessionDO, List<ChatlogDTO>> csChatSessionDOListMap = buyerSessions.get(csChatSessionDO.getCustomer());
            computerForword(csChatSessionDOListMap,csChatSessionDO,forWordInSet);
        }
        /*修改转入 会话*/
        if (CollectionUtils.isNotEmpty(forWordInSet)) {
            csChatSessionDao.updateSetForwordFlag(forWordInSet, 1, schemaId, date);
        }
        /*重算客服服务指标 转入量 转出量 直接接待量*/
        Boolean leaveMessageSwitch = jobShopQuery.getShopSystemsetting().getLeaveMessageSwitchWithNull();
        Integer sessionType=null;
        if(leaveMessageSwitch){//留言过滤开启的话转出量只算在线会话
            sessionType = CommonConstants.CHAT_SESSION_TYPE_ONLINE;
        }
        for (CsChatSessionServiceIndexDO csChatSessionServiceIndexDo : csChatSessionServiceIndexLst) {
            final List<Integer> flags = csChatSessionDao.searchForWordInOutAndDirect(schemaId, shopId, csChatSessionServiceIndexDo.getCsNick(), date, sessionType);
            csChatSessionServiceIndexDo.setDirectReceiveSessionNum(flags.get(2));
            csChatSessionServiceIndexDo.setForwardOutSessionNum(flags.get(0));
            csChatSessionServiceIndexDo.setForwardInSessionNum(flags.get(1));
        }
    }

    private void calChatSessionService(JobShopQuery jobShop,
                                       CsDTO csDTO,
                                       Date date,
                                       Map<CsChatSessionDTO,
                                            List<ChatlogDTO>> chatSessions,
                                       List<CsChatSessionServiceIndexDO> csChatSessionServiceIndexDos,
                                       ChatSessionsBO chatSessionsBO) {

        CsChatSessionServiceIndexDO seviceIndex = new CsChatSessionServiceIndexDO();
        seviceIndex.setCsNick(csDTO.getNick());
        seviceIndex.setDate(date);
        seviceIndex.setMaxReceiveSessionNum(chatSessionsBO.getMaxReceive());
        final JobShopDTO shop = jobShop.getShop();
        final Long shopId = shop.getShopId();
        seviceIndex.setShopId(shopId);
        /*留言分配逻辑覆盖*/
        seviceIndex.setLeaveMsgSessionNum(csLeaveMsgDao.leaveAssign(
                shopId,date, com.pes.jd.util.DateUtils.getEndTimeOfDate(date),shop.getSchemaId(),csDTO.getNick()
        ));

        final ShopSystemsettingDTO shopSystemsetting = jobShop.getShopSystemsetting();
        // 表中未计算列：回复率 答问比(存了计算这两个列需要的字段)
        final Set<CsChatSessionDTO> csChatSession = chatSessions.keySet();
        /*会话总数 平均响应时间在快速应答时间中的会话数*/
        int sessionSum = csChatSession.size(),forFastInAvgResTime = 0;
        /*咨询量(暂时弃用，使用接待量+接待过滤量) 接待量 顾客发起量 客服主动跟进量 总消息数*/
        int consultSessionNum = 0,receiveSessionNum = 0,custConsultSessionNum = 0,csToCustSessionNum = 0,chatNum = 0;
        /*客服 顾客消息数 客服字数 客服人工回复消息数 慢响应量 长接待量*/
        int csChatNum = 0,custChatNum = 0,csWordNum = 0,buyerChatReply = 0,slowRespSessionNum = 0,longRespSessionNum = 0;
        /*首次响应时间*/
        double avgRespTimeFirst = 0D;
        /*会话回合数 每次会话买家说话到客服回复间隔时间 会话时长(s)*/
        long sessionCount = 0L , sessionTimeCount = 0L , sessionDurationTime = 0L;
        /*系统设置中的快速应答时间*/
        final long quickResponseTime = (shopSystemsetting.getQuickResponseTime());
        /*留言接待量 留言分配量 */
        int levelMsgReciveNum = 0,leaveMsgSessionNum = 0;
        /*接待时长*/
        double receiveSessionDurationTime = 0.0;
        /*接待过滤量*/
        int receiveFilterNum = 0;
        /*未回复量*/
        int nonReply = 0;
        /**
         * 转接处理： 如果transfer 是1 的话就是转出
         *     然后根据当前的聊天会话，去找同一个买家，在这个会话之后，不同客服的聊天会话，将聊天会话设置为转入
         *     默认为直接接待
         */
        for (Map.Entry<CsChatSessionDTO, List<ChatlogDTO>> csChatSessionDOListEntry : chatSessions.entrySet()) {
            /*计算每个客服的，当天会话的数据*/
            final List<ChatlogDTO> chatlogs = csChatSessionDOListEntry.getValue();
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            /*接待过滤不计算*/
            if (chatSession.getReceiveFilter()){
                receiveFilterNum++;
                continue;
            }
            receiveSessionNum = chatSession.getReceive() ? receiveSessionNum+1 : receiveSessionNum;
            /*顾客发起量*/
            custConsultSessionNum = chatSession.getReceiveStartType() == 2 ? custConsultSessionNum+1 : custConsultSessionNum;
            /*客服主动跟进*/
            csToCustSessionNum = chatSession.getCsToCustSessionNum() ? csToCustSessionNum+1 : csToCustSessionNum ;
            /*总消息数量*/
            chatNum += chatSession.getChatNum();
            /*客服 顾客消息数*/
            csChatNum+=chatSession.getCsChatNum();
            custChatNum+=chatSession.getCustChatNum();
            /*客服字数*/
            csWordNum+=chatSession.getCsWordNum();
            buyerChatReply+=chatSession.getBuyerChatReply();
            slowRespSessionNum = chatSession.getSlowResp() ? slowRespSessionNum + 1 : slowRespSessionNum;
            longRespSessionNum = chatSession.getLongReceive() ? longRespSessionNum + 1 : longRespSessionNum;
            avgRespTimeFirst += chatSession.getAvgRespTimeFirst();
            sessionCount += chatSession.getSessionCount();
            sessionTimeCount += chatSession.getSessionTimeCount();
            final Date beginDatetime = chatSession.getSessionBeginTime();
            final Date endTime = specProcessSessionEndTime(chatSession, chatlogs);
            long sessionEndTime = 0;
            if(Objects.nonNull(endTime)){
                sessionEndTime = endTime.getTime() - beginDatetime.getTime();
            }
            sessionDurationTime += TimeUnit.MILLISECONDS.toSeconds(sessionEndTime);
            forFastInAvgResTime = quickResponseTime > chatSession.getAvgRespTime() ? forFastInAvgResTime+1 : forFastInAvgResTime;
            /*留言接待*/
            final Integer sessionType = chatSession.getSessionType();
            levelMsgReciveNum = chatSession.getReceive()&&(sessionType == 2) ? levelMsgReciveNum+1 : levelMsgReciveNum;
            receiveSessionDurationTime+=chatSession.getSessionDurationTime();
            nonReply+=chatSession.getNonReply()?1:0;
            /*留言咨询*/
            if (sessionType == 2 && chatSession.getConsult()){
//                leaveMsgAdvisorySessionNum.increment();
            }
        }
        consultSessionNum = receiveFilterNum+receiveSessionNum;
        seviceIndex.setConsultSessionNum(consultSessionNum);
        seviceIndex.setReceiveSessionNum(receiveSessionNum);
        seviceIndex.setReceiveSessionDurationTime( receiveSessionDurationTime);
        seviceIndex.setCustConsultSessionNum(custConsultSessionNum);
        seviceIndex.setCsToCustSessionNum(csToCustSessionNum);
        seviceIndex.setChatNum(chatNum);
        seviceIndex.setCsChatNum(csChatNum);
        seviceIndex.setCustChatNum(custChatNum);
        seviceIndex.setCsWordNum(csWordNum);
        /*未回复量*/
        seviceIndex.setNonReplySessionNum(nonReply);
        seviceIndex.setSlowRespSessionNum(slowRespSessionNum);
        seviceIndex.setLongRespSessionNum(longRespSessionNum);
        /*平均回复消息数 客服回复消息数/会话数*/
        final int sessionS = chatSessions.size();
        seviceIndex.setAvgCsMsgSessionNum(sessionS == 0 ? 0D : buyerChatReply*1.0/sessionS);
        /*首次平均响应*/
        seviceIndex.setAvgRespTimeFirst(sessionSum == 0 ? 0D : (avgRespTimeFirst/sessionSum));
        /*平均响应时间*/
        seviceIndex.setAvgRespTime(sessionCount == 0 ? 0D : (sessionTimeCount*1.0/sessionCount));
        /*平均会话时间*/
        double avgSessionDurationTime = sessionSum == 0 ? 0D : (sessionDurationTime * 1.0 / sessionSum);
        if(avgSessionDurationTime<0){
            avgSessionDurationTime = 0;
        }
        seviceIndex.setAvgSessionDurationTime(avgSessionDurationTime);
        /*在前端计算，存储基本字段 快速应答率 平均响应时间在快速应答时间中的会话数/总会话数*/
        seviceIndex.setAvgRespInQuickTime(forFastInAvgResTime);
        seviceIndex.setSessionNum(sessionSum);

        /*留言接待量*/
        seviceIndex.setLeaveMsgReceiveSessionNum(levelMsgReciveNum);
        csChatSessionServiceIndexDos.add(seviceIndex);
    }


    private boolean calChatSession(
            Set<String> filterNick/*接待过滤的数据*/,
            JobShopQuery jobShop,
            Map<CsChatSessionDTO, List<ChatlogDTO>> chatSessions/*聊天会话->多个聊天记录*/,
            List<CsLeaveMsgDTO> csLeaveMsgs/*留言记录*/,
            List<CsServiceEvaluationDetailDTO> csServiceEvaluation/*服务评价*/,
            List<CsServiceSendEvalDO> csServiceSendEval/*发送评价*/) {

        final ShopSystemsettingDTO shopSystemsetting = jobShop.getShopSystemsetting();
        /*客服转发过滤开关*/
        final Boolean csForwardSwitch = shopSystemsetting.getCsForwardSwitch();
        /*客服转发过滤句数 客服最多回复 {}句（含）*/
        final Integer csForwardNum = shopSystemsetting.getCsForwardNum();
        // 过滤，只需要有分配时间的
        final MultiValueMap<String, CsLeaveMsgDTO> sidLeaveMap =
                MapUtils.extractForMap(csLeaveMsgs, CsLeaveMsgDTO::getSid, v -> Objects.nonNull(v.getAllocationTime()));
        // 服务评价
        final MultiValueMap<String, CsServiceEvaluationDetailDTO> evaluationMap = MapUtils.extract(csServiceEvaluation, CsServiceEvaluationDetailDTO::getSid);
        // 邀评
        final MultiValueMap<String, CsServiceSendEvalDO> serviceSendEvalMap = MapUtils.extract(csServiceSendEval, CsServiceSendEvalDO::getSid);


        /**
         * 转接处理： 如果transfer 是1 的话就是转出
         *     然后根据当前的聊天会话，去找同一个买家，在这个会话之后，不同客服的聊天会话，将聊天会话设置为转入
         *     默认为直接接待
         */
        for (Map.Entry<CsChatSessionDTO, List<ChatlogDTO>> csChatSessionDOListEntry : chatSessions.entrySet()) {
            /*计算每个客服的，当天会话的数据*/
            final List<ChatlogDTO> chatlogs = csChatSessionDOListEntry.getValue();
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            final Boolean transfer = chatSession.getTransfer();
            /*留言分配量*/
            final List<CsLeaveMsgDTO> lvsm = sidLeaveMap.get(chatSession.getSid());
            if (!org.springframework.util.CollectionUtils.isEmpty(lvsm)){
                chatSession.setAssign(Boolean.TRUE);
                //留言 言分配
                chatSession.setLeaveMsgSession(true);
            }
            if (transfer){
                /*客服说话 direction = 0  客服转发过滤*/
                if(CollectionUtils.isNotEmpty(chatlogs)&&csForwardSwitch){
                    final long count = chatlogs.stream().filter(k -> k.getDirection() == 0).count();
                    /*转发过滤了*/
                    if (count<=csForwardNum){
                        chatSession.setForwordFilter(true);
                    }
                }
                /*转出*/
                chatSession.setForwardType(2);
            }
            if (filterNick.contains(chatSession.getCustomer())/*接待过滤掉了*/){
                chatSession.setReceive(false);
                chatSession.setConsult(false);
                chatSession.setReceiveFilter(true);
                continue;
            }
            /*计算chatSession各种指标*/
            calChatSessionData(shopSystemsetting, chatlogs, chatSession);
            /*邀评数量*/
            int count = 0;
            if (org.apache.commons.collections.MapUtils.isNotEmpty(serviceSendEvalMap)){
                count+=serviceSendEvalMap.getOrDefault(chatSession.getSid(),Collections.emptyList()).size();
            }
            chatSession.setSendEvalNum(count);
            /*评价数量 评价分数 好评数量*/
            int evalNum = 0,evalCode = 0,satisfiedEvalNum = 0;
            if (org.apache.commons.collections.MapUtils.isNotEmpty(evaluationMap)){
                for (CsServiceEvaluationDetailDTO csServiceEvaluationDetailDTO :
                        evaluationMap.getOrDefault(chatSession.getSid(),Collections.emptyList())) {
                    evalNum++;
                    final Integer evalCode1 = csServiceEvaluationDetailDTO.getEvalCode();
                    evalCode+= evalCode1;
                    satisfiedEvalNum += evalCode1>=75 ? 1 : 0;
                }
            }
            chatSession.setEvalCode(evalCode);
            chatSession.setSatisfiedEvalNum(satisfiedEvalNum);
            chatSession.setEvalNum(evalNum);

        }
        // 更新所有的会话
        csChatSessionDao.updateByPrimaryKeySelective(jobShop.getShop().getSchemaId(),new ArrayList<>(chatSessions.keySet()));
        return Boolean.TRUE;
    }

    /**
     *  根据转出找转入
     */
    private void computerForword(Map<CsChatSessionDO, List<ChatlogDTO>> csChatSessionDOListMap,
                                 CsChatSessionDO chatSession,/*转出*/Set<String> forWordInSet) {
        // 注意点 chatsession 必须是排序的，也就是说上述的map 必须是linkedhashmap
        boolean flag = false;
        for (Map.Entry<CsChatSessionDO, List<ChatlogDTO>> csChatSessionDO : csChatSessionDOListMap.entrySet()) {

            /*如果开始寻找*/
            final CsChatSessionDO key = csChatSessionDO.getKey();
            final String sid = key.getSid();

            if (flag){
                if (!key.getTransfer()){
                    /*转入*/
                    forWordInSet.add(sid);
                    return;
                }
            }

            if (Objects.equals(sid,chatSession.getSid())){
                flag = true;
            }

        }

    }

    /**
     *  处理EndTime 为 null 的情况
     */
    private Date specProcessSessionEndTime(CsChatSessionDO chatSession, List<ChatlogDTO> chatlogs) {
        Date endDatetime = chatSession.getSessionEndTime();
        if (endDatetime == null){
            if (CollectionUtils.isNotEmpty(chatlogs))
                endDatetime  = chatlogs.get(chatlogs.size()-1).getChatTime();
        }
        return endDatetime;
    }

    /**
     * 计算接待压力与分时接待
     */
    private Integer calReceivePressure(
            JobShopQuery shopQuery,
            CsDTO csDTO,
            Date jobDate,
            Set<CsChatSessionDTO> chatSession,
            ChatSessionsBO chatSessionsBO) {
        chatSessionsBO.setMaxReceive(0);
        if (org.springframework.util.CollectionUtils.isEmpty(chatSession)){
            return 0;
        }
        final JobShopDTO shop = shopQuery.getShop();
        final String schemaId = shop.getSchemaId();

        ShopSystemsettingDTO sys = shopQuery.getShopSystemsetting();
        /**留言过滤开关，开启之后留言的会话不算分时接待**/
        Boolean leaveMessageSwitch = sys.getLeaveMessageSwitchWithNull();
        List<TimeSection> timeSections = new ArrayList<>(chatSession.size());
        for (CsChatSessionDTO csChatSessionDO : chatSession) {
            if (csChatSessionDO.getReceive()) {
                if (leaveMessageSwitch && CommonConstants.CHAT_SESSION_TYPE_LEAVEMESSAGE.equals(csChatSessionDO.getSessionType()))
                    continue;
                /*将接待的 会话的时间点保存在集合中*/
                timeSections.add(
                        new TimeSection(
                                csChatSessionDO.getBeginDatetime(),
                                csChatSessionDO.getEndDatetime(),
                                RECEIVE_FLAG_CAL
                        )
                );
            }
        }
        /*接待压力数据*/
        List<ReceiveSessionPressureDO> receiveSessionPressures = new ArrayList<>(200);
        Date date = DateUtils.setMinutes(DateUtils.truncate(jobDate, Calendar.DAY_OF_MONTH),RECEIVE_FLAG_CAL);

        for (Date index = date; index.getTime() < DateUtils.addDays(date,1).getTime(); index = DateUtils.addMinutes(index,RECEIVE_FLAG_CAL*2)) {
            /*计算指定日期job的接待压力*/
            final int count = calTimeSharing(index, timeSections);
            if (count>0){
                ReceiveSessionPressureDO receiveSessionPressureDO = new ReceiveSessionPressureDO();
                receiveSessionPressures.add(receiveSessionPressureDO);
                receiveSessionPressureDO.setCsNick(csDTO.getNick());
                receiveSessionPressureDO.setReceiveDot(index);
                receiveSessionPressureDO.setShopId(csDTO.getShopId());
                receiveSessionPressureDO.setReceiveSessionNum(count);
            }
        }

        /*计算分时接待*/
        ReceiveSessionNumHourlyDO receiveSessionNumHourly = new ReceiveSessionNumHourlyDO();
        for (int i = 0; i < 24; i++) {
            int count = 0;
            for (TimeSection timeSection : timeSections) {
                count += timeSection.include(date,i) ? 1 : 0;
            }
            receiveSessionNumHourly.setCsNick(csDTO.getNick());
            receiveSessionNumHourly.setDate(jobDate);
            receiveSessionNumHourly.setShopId(shopQuery.getShop().getShopId());
            try {
                final Method method = ReceiveSessionNumHourlyDO.class.getDeclaredMethod("setHour" + i, Integer.class);
                ReflectionUtils.makeAccessible(method);
                method.invoke(receiveSessionNumHourly,count);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        receiveSessionNumHourlyDao.insert(receiveSessionNumHourly,schemaId);

        if (CollectionUtils.isNotEmpty(receiveSessionPressures)){
            receiveSessionPressureDao.insertBatch(receiveSessionPressures,date, schemaId);
            receiveSessionPressures.sort((x,y)->{
                if (x.getReceiveSessionNum()>y.getReceiveSessionNum()){
                    return -1;
                }
                if (Objects.equals(x.getReceiveSessionNum(),y.getReceiveSessionNum())){
                    return 0;
                }
                return 1;
            });

            final Integer receiveSessionNum = receiveSessionPressures.get(0).getReceiveSessionNum();
            chatSessionsBO.setMaxReceive(receiveSessionNum);

            return receiveSessionNum;
        }
        return 0;
    }

    /**
     *  接待压力
     */
    private int calTimeSharing(Date timePoint, List<TimeSection> timeSections) {
        int count = 0;
        for (TimeSection timeSection : timeSections) {
            if (timeSection.include(timePoint)){
                count++;
            }
        }
        return count;
    }

    /**
     *  计算数据
     */
    private void calChatSessionData(ShopSystemsettingDTO shopSystemsetting,
                                    List<ChatlogDTO> chatlogDTOS,
                                    CsChatSessionDTO csChatSession) {
        /*慢响应时间*/
        final Integer slowResponseTime = shopSystemsetting.getSlowResponseTime();
        final long slowResponseTimeMillis = TimeUnit.SECONDS.toMillis(slowResponseTime);
        /*慢响应次数*/
        final Integer slowResponseTimesNum = shopSystemsetting.getSlowResponseTimesNum();
        /*最长等待时间*/
        final long maxWaitTime = TimeUnit.MINUTES.toMillis(shopSystemsetting.getMaxWaitTime());
        /*长接待设定*/
        final long longReceptionTime = TimeUnit.MINUTES.toMillis(shopSystemsetting.getLongReceptionTime());
        int slowCount = 0;
        /*标记上一次是谁说话*/
        String lastSay = null;
        final String csSayFlag = "CS_SAY";
        final String buyerSayFlag = "BUYER_SAY";
        /*首次，最后一次接待时间*/
        long firstReceiveTime = 0,lastReceiveTime = 0;
        /*计算首次响应时间（买家第一次说话时间，如果接待时间差超过最长等待时间，
        那此值，不会是'买家第一次说话的时间'，而是过滤掉超过最长等待时间的第一次接待的买家说话时间）*/
        long buyerFirstSendTime = 0;
        /*第一句发送的时间点   最后一句发送的时间点  会话回合数 每次会话买家说话到客服回复间隔时间(秒)*/
        long sendFirstTime = 0L , sendLastTime = 0L , sessionCount = 0L , sessionTimeCount = 0L;
        /*客服消息数 买家消息数 */
        int csChatNum = 0,buyerChatNum = 0;
        /*买家还是顾客发起 客服字数*/
        int receiveStartType = 0,csWordNum = 0;
        /* 标记买家说过话 只要有买家说话，客服的chatLog都为回复*/
        boolean buyerChated = false;
        /*客服人工回复数量*/
        int buyerChatReply = 0;
        /*chatlog 开始时间和结束时间*/
        Date beginReceive  = null;
        Date endReceive  = null;
        /*初始化属性*/
        {
            csChatSession.setSlowResp(false);
            csChatSession.setConsult(false);
            csChatSession.setReceive(false);
        }
        /*找第一个买家说话的记录*/
        boolean justOnce = true;
        /*买家发送聊天的时间*/
        Date buyerSendLogTime = null;
        if (CollectionUtils.isNotEmpty(chatlogDTOS)) {
            /*查询过滤，如果过滤了就不用计算*/

            for (ChatlogDTO chatLog : chatlogDTOS) {
                final boolean csAutoReplyBySysettingChat =
                        performanceRuleBusiness.isCsAutoReplyBySysettingChat(shopSystemsetting, chatLog.getMt(), chatLog.getContent());
                final boolean csSay = csSay(chatLog);
                final boolean buyerSay = buyerSay(chatLog);
                final long thisChatLogTime = chatLog.getChatTime().getTime();
                if (justOnce) {
                    sendFirstTime = thisChatLogTime;
                    receiveStartType = csSay ? 1 : 2;
                    beginReceive = chatLog.getChatTime();
                }
                endReceive = chatLog.getChatTime();
                /*最后发送聊天时间*/
                sendLastTime = thisChatLogTime;
                csChatNum += csSay ? 1 : 0;
                buyerChatNum += buyerSay ? 1 : 0;
                if (csSay) {
                    /*过滤自动回复*/
                    if (csAutoReplyBySysettingChat) {
                        continue;
                    }
                    /*客服主动跟进*/
                    csChatSession.setCsToCustSessionNum(true);
                    /*客服字数*/
                    csWordNum += chatLog.getContent().length();
                    if (buyerChated) {
                        buyerChatReply++;
                    }
                    /*上次如果是买家说话*/
                    if (Objects.equals(lastSay, buyerSayFlag)) {
                        /*设置未回复*/
                        csChatSession.setNonReply(false);
                        /*设置接待*/
                        csChatSession.setReceive(true);
                        /*最近对话回合的耗时*/
                        final long recentlySessionTime = (thisChatLogTime - buyerSendLogTime.getTime());
                        /*表示这次接待 超过了最长等待时间*/
                        final boolean gtMaxWaitTime = buyerSendLogTime != null && (thisChatLogTime - buyerSendLogTime.getTime()) >= maxWaitTime;
                        if (!gtMaxWaitTime) {
                            /*首次接待时间*/
                            if (firstReceiveTime == 0) {
                                firstReceiveTime = thisChatLogTime;
                            }
                            /*会话回合加1*/
                            ++sessionCount;
                            /*将每回合 买家说话到客服说话的间隔时间 都加起来，计算平均响应时间*/
                            sessionTimeCount += TimeUnit.MILLISECONDS.toSeconds(recentlySessionTime);
                        } else {
                            /*表示超过了最长等待时间*/
                            buyerFirstSendTime = 0;
                        }
                        /*最后接待时间*/
                        lastReceiveTime = thisChatLogTime;
                        /*计算慢响应，如果响应时间大于设定时间，并且这种情况的次数大于设定次数，则为慢响应*/
                        if (recentlySessionTime > slowResponseTimeMillis && ++slowCount >= slowResponseTimesNum) {
                            csChatSession.setSlowResp(true);
                        }
                    }
                    buyerSendLogTime = null;
                } else {
                    /*设置咨询*/
                    csChatSession.setConsult(true);
                    /*如果买家发送聊天时间为空，那么设置属性值*/
                    if (buyerSendLogTime == null) {
                        buyerSendLogTime = chatLog.getChatTime();
                    }
                    if (buyerFirstSendTime == 0) {
                        buyerFirstSendTime = thisChatLogTime;
                    }
                    buyerChated = true;
                    if (justOnce){
                        csChatSession.setNonReply(true);
                    }
                }
                /*标记此次说话是谁说的*/
                lastSay = csSay ? csSayFlag : buyerSayFlag;
                justOnce = false;
            }
        }
        /*处理结束时间*/
        csChatSession.setSessionEndTime(specProcessSessionEndTime(csChatSession,chatlogDTOS));
        /*接待会话时长*/
        long receiveTime = 0;
        if (beginReceive!=null && endReceive!=null){
            receiveTime = TimeUnit.MILLISECONDS.toSeconds(endReceive.getTime()-beginReceive.getTime());
        }
        csChatSession.setSessionReceiveDurationTime((double) receiveTime);
        /*会话时长 s*/
        csChatSession.setSessionDurationTime(sendFirstTime == sendLastTime ? 0D : TimeUnit.MILLISECONDS.toSeconds(sendLastTime - sendFirstTime));
        /*长接待*/
        csChatSession.setLongReceive(csChatSession.getSessionReceiveDurationTime()>=TimeUnit.MILLISECONDS.toSeconds(longReceptionTime));
        /*买家客服消息数*/
        csChatSession.setCsChatNum(csChatNum);
        csChatSession.setCustChatNum(buyerChatNum);
        /*总消息数*/
        csChatSession.setChatNum(csChatNum+buyerChatNum);
        /*会话发起方*/
        csChatSession.setReceiveStartType(receiveStartType);
        /*首次响应时间  (buyerFirstSendTime == 0 表示超过了最长等待时间)*/
        final double resFirst = buyerFirstSendTime == 0 ? 0 : TimeUnit.MILLISECONDS.toSeconds((firstReceiveTime - buyerFirstSendTime));
        csChatSession.setAvgRespTimeFirst(resFirst < 0 ? 0 : resFirst);
        /*平均响应时间*/
        csChatSession.setAvgRespTime((sessionCount == 0 || sessionTimeCount == 0) ? 0D : (double) sessionTimeCount / sessionCount);
        /*客服字数*/
        csChatSession.setCsWordNum(csWordNum);
        /*人工回复消息数量*/
        csChatSession.setBuyerChatReply(buyerChatReply);
        /*会话回合数 每次会话买家说话到客服回复间隔时间*/
        csChatSession.setSessionCount(sessionCount);
        csChatSession.setSessionTimeCount(sessionTimeCount);
        csChatSession.setBeginDatetime(beginReceive);
        csChatSession.setEndDatetime(endReceive);
    }

    private class TimeSection{
        Date begin;
        Date end;
        int minute;

        TimeSection(Date begin, Date end,int minute) {
            this.begin = begin;
            this.end = end;
            this.minute = minute;
        }

        /**
         *  该时间点的前后指定分钟，是否和开始时间和结束时间有交集
         */
        public boolean include(Date date){
            /*前后时间节点*/
            long b = DateUtils.addMinutes(date, -minute).getTime();
            long a = DateUtils.addMinutes(date, minute).getTime();
            return intersection(b, a);
        }


        /**
         *  判断该日期的 hour到hour+1 时，是否和开始时间和结束时间有交集
         * @param date
         * @param hour
         * @return
         */
        public boolean include(Date date,int hour){
            final Date bDate = hour == 0 ? date : DateUtils.setHours(date, hour);
            final Date aDate = hour + 1 == 24 ? DateUtils.addDays(date,1) : DateUtils.setHours(date, hour + 1);
            return intersection(bDate.getTime(),aDate.getTime());
        }

        /**
         *  判断交集
         * @param b 开始节点
         * @param a 结束节点
         * @return
         */
        private boolean intersection(long b, long a) {
            if (begin == null || end == null){
                return false;
            }
            final long beginTime = begin.getTime();
            final long endTime = end.getTime();
            if (beginTime>a || endTime<b){
                return false;
            }
            return true;
        }

    }

    /**
     *  时间排序 升序
     */
    private Comparator<ChatlogDTO> dateSort = (a,b)->{
        Date x = a.getChatTime();
        Date y = b.getChatTime();
        if (x==null || y == null || x.before(y)){
            return -1;
        }
        if (x.equals(y)){
            return 0;
        }
        return 1;
    };
    private boolean csSay(ChatlogDTO chatlogDTO){
        return chatlogDTO.getDirection() == 0;
    }
    private boolean buyerSay(ChatlogDTO chatlogDTO) {
        return chatlogDTO.getDirection() == 1;
    }


}