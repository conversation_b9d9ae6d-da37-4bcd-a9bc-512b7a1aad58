package com.pes.jd.business.impl;

import com.pes.jd.business.ShopPresaleOrderBusiness;
import com.pes.jd.data.converter.PresaleOrderConverter;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;

@Service
public class ShopPresaleOrderBusinessImpl implements ShopPresaleOrderBusiness{
private static Logger logger = LoggerFactory.getLogger(ShopPresaleOrderBusinessImpl.class);
	@Resource
	private PresaleOrderConverter presaleOrderConverter;
	//预售接口存在延迟获取数据天数
	private final static int PRESALE_ORDER_POSTPONE_DAY = 2;
	@Override
	public void pullPresaleOrderByDate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		//TODO 预售接口存在延迟获取数据日期往后推迟2天
		Date presaleOrderEndDate = DateFormatUtils.getDateByPeriod(jobDate.getEndDate(), PRESALE_ORDER_POSTPONE_DAY);
		presaleOrderConverter.pullPresaleOrderConverter(jobShop, jobDate.getStartDate(), presaleOrderEndDate, isDelData);
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("预售订单拉取 pre_order_pull end,time:{}s", (e - s) / 1000);
		}

	}

}
  
