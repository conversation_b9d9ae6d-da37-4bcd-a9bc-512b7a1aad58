package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.CsShopSystemSettingsBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

/**
 * @Author: aiJun
 * @Date: 2019-05-17 18:15
 * @Version 1.0
 */
@Service
public class CsShopSystemSettingsBusinessImpl implements CsShopSystemSettingsBusiness {

    private static Logger logger = LoggerFactory.getLogger(CsShopSystemSettingsBusinessImpl.class);

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;
    @Override
    public ApiResponse selectShopCsAndSystemSettings(Long shopId, String csGroup, String csNick, boolean needSystemSettings) throws DBNotExistException {
        ApiResponse apiResponse;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId+"")
                .put("groupId", StringUtils.isBlank(csGroup)?"":csGroup)
                .put("csNick", StringUtils.isBlank(csNick)?"":csNick)
                .put("needSystemSettings", needSystemSettings)
                .toRequestEntity();
//        final String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER);
        final String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        try {
            final long start = System.currentTimeMillis();
            apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/manage/selectShopCsAndSystemSettings", body);
            final long end = System.currentTimeMillis();
            System.out.println("/shop/manage/selectShopCsAndSystemSettings>>>> totalTime ={"+(end-start)+"ms}");
        } catch (HttpClientErrorException e) {
            logger.error("CsShopSystemSettingsBusinessImpl error:{}",e.getMessage(),e);
            throw e;
        }
        return apiResponse;
    }
}
