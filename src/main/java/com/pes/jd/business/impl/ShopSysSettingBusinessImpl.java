package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopSysSettingBusiness;
import com.pes.jd.data.api.VenderShopOperator;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.RequestUrlEnum;
import com.pes.jd.model.Param.GroupParam;
import com.pes.jd.model.Param.RealTimePerformanceSettingsParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.DeptShop;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.CommonDateUtils;
import com.pes.jd.util.EmailUtils;
import com.yiyitech.support.mail.AbstractMail;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 系统设置
 * (微服务调用入口)
 */
@SuppressWarnings("Duplicates")
@Service
public class ShopSysSettingBusinessImpl implements ShopSysSettingBusiness {

	private static final Logger logger = LoggerFactory.getLogger(ShopSysSettingBusinessImpl.class);


	@Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;

	@Resource
	private VenderShopOperator venderShopOperator;

	@Autowired
	private AbstractMail yiyiJavaMailSender;
	//客服设置------------------------------------------------------

	@Override
	public ApiResponse selectShopCs(ShopQuery shop, String groupId, String nick, String operateType,String type){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shop.getShopId())
				.put("groupId", groupId)
				.put("nick", nick)
				.put("operateType", operateType)
				.put("type", type)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectShopCs", body);
		} catch (HttpClientErrorException  e) {
			logger.error("/cs/manage/selectShopCs  error:"+e.getMessage(),e);
			throw e;
		}

		return apiResponse;
	}


	/**
	 *
	 * 设置单个客服
	 */
	@Override
	public ApiResponse updateShopCustomerServices(ShopQuery shop, String nickArrStr, String groupId, Integer type) {
		String shopId = String.valueOf(shop.getShopId());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("nickArrStr", nickArrStr)
				.put("groupId", groupId)
				.put("type", type)
				.put("subUserNum", shop.getSubuserNum())
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/updateShopCustomerServices", body);
		} catch (Exception e) {
			logger.error("/cs/manage/updateShopCustomerServices error："+e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * 查询店铺子账号来设置客服
	 *
	 */
	@Override
	public ApiResponse selectShopSubUserForCsSetting(ShopQuery shop, String nick){
		String shopId = String.valueOf(shop.getShopId());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("nick", nick)
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectShopSubUserForCsSetting", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/selectShopSubUserForCsSetting error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * 批量客服管理
	 *
	 * @param operateType
	 *            1:批量添加 2批量锁定
	 *
	 */
	@Override
	public ApiResponse batchUpdateShopCs(ShopQuery shop, String nickArrStr, String groupId, Integer type,
			Integer operateType) throws Exception {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("nickArrStr", nickArrStr)
				.put("groupId", groupId)
				.put("type", type)
				.put("operateType", operateType)
				.put("shopId", shop.getShopId())
				.put("subUserNum", shop.getSubuserNum())
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/batchUpdateShopCs", body);
		} catch (Exception e) {
			logger.error("/cs/manage/batchUpdateShopCs error:"+e.getMessage(),e);
			throw e;
		}

		return apiResponse;
	}

	/**
	 * 编辑客服信息
	 */
	@Override
	public ApiResponse updateCs(ShopQuery shop, String csNick, String simpleNick, Integer type, String groupId)
			throws Exception {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("csNick", csNick)
				.put("simpleNick", simpleNick)
				.put("type", type)
				.put("groupId", groupId)
				.put("shopId", shop.getShopId()).toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/updateCs", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/updateCs error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * 锁定或解锁客服
	 */
	@Override
	public ApiResponse lockCs(ShopQuery shopQuery, String csNickStr, Integer operateType){
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shopId", shopQuery.getShopId())
				.put("subuserNum", shopQuery.getSubuserNum())
				.put("operateType", operateType)
				.put("csNickStr", csNickStr)
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/lockCs", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/lockCs error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * @Description:（刷新子账号）
	 */
	@Override
	public ApiResponse refreshShopSubUsers(ShopQuery shop) {
		ApiResponse apiResponse = null;
		try {
			venderShopOperator.checkShopExpireBySessionKey(shop.getSessionKey());
		} catch (GainShopDataFailException e) {
			if(e.getErrorCode().equals("19")){
				if(StringUtils.isNotBlank(shop.getOptionSessionKey())) {
					shop.setSessionKey(shop.getOptionSessionKey());
					logger.info("shopName {} main SessionKey is expire",shop.getTitle());
				}else{
					logger.info("shopName {} sub SessionKey is empty no update sessionKey",shop.getTitle());
				}
			}

		}catch (Exception e) {
			logger.error("shopName:{} checkShopExpireBySessionKey error:{}",shop.getTitle(),e.getMessage(),e);
		}
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shopId", shop.getShopId()).
				put("sellerId", shop.getSellerId()).
				put("sellerNick", shop.getSellerNick()).
				put("sellerShowNick", shop.getSellerShowNick()).
				put("sessionKey", shop.getSessionKey()).
				toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			long s1 = System.currentTimeMillis();
			apiResponse = usermgrRestTemplate.postRest(serviceId,"/cs/manage/refreshShopSubUsers", body,new ParameterizedTypeReference<RestResponseTypeRef<ApiResponse>>(){}).getData();
			long s2 = System.currentTimeMillis();
			logger.info("time : {}",s2-s1);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/refreshShopSubUsers error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * 获取店铺下的客服信息
	 *
	 * @param type
	 */
	@Override
	public ApiResponse selectShopCswwSimpleNames(String dbName,List<ShopQuery> shopLst, Integer type){
		List<ShopCommonParam> shopParamsLst=Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(shopLst)){
			for (ShopQuery shop : shopLst) {
				ShopCommonParam shopParam=new ShopCommonParam();
				shopParam.setDbName(shop.getDbName());
				shopParam.setSchemaId(shop.getSchemaId());
				shopParam.setShopId(shop.getShopId());
				shopParamsLst.add(shopParam);
			}
		}
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shopLst", shopParamsLst).
				put("type", type)
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectShopCswwSimpleNames", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/selectShopCswwSimpleNames error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCsNickByShopIdByGroupIdByCsNick(ShopQuery shop ,
			String groupId,
		 String csNick) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shop.getShopId())
				.put("groupId", groupId)
				.put("csNick", csNick)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectCsNickByShopIdByGroupIdByCsNick", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/selectCsNickByShopIdByGroupIdByCsNick error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCsNickByShopIdByGroupIdByCsNickNew(ShopQuery shop ,
															 String groupId,
															 String csNick) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shop.getShopId())
				.put("groupId", groupId)
				.put("csNick", csNick)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectCsNickByShopIdByGroupIdByCsNickNew", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/selectCsNickByShopIdByGroupIdByCsNick error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCsNickByShopIdByGroupIdByCsNick(String dbName ,List<ShopQuery> shopLst,String groupId,
		 String csNick)  {
		List<ShopCommonParam> shopParamsLst=Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(shopLst)){
			for (ShopQuery shop : shopLst) {
				ShopCommonParam shopParam=new ShopCommonParam();
				shopParam.setDbName(shop.getDbName());
				shopParam.setSchemaId(shop.getSchemaId());
				shopParam.setShopId(shop.getShopId());
				shopParamsLst.add(shopParam);
			}
		}
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopLst", shopParamsLst)
				.put("groupId", groupId)
				.put("csNick", csNick)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectMultiCsNickByShopIdByGroupIdByCsNick", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/manage/selectMultiCsNickByShopIdByGroupIdByCsNick error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}
	//客服设置------------------------------------------------------


	//客服组设置----------------------------------------------------

	/**
	 * 客服组列表查询
	 */
	@Override
	public ApiResponse queryShopCsGroups(String  daName,List<ShopQuery> shopLst,String groupName) {
		List<ShopCommonParam> shopParamsLst=Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(shopLst)){
			for (ShopQuery shop : shopLst) {
				ShopCommonParam shopParam=new ShopCommonParam();
				shopParam.setDbName(shop.getDbName());
				shopParam.setSchemaId(shop.getSchemaId());
				shopParam.setShopId(shop.getShopId());
				shopParamsLst.add(shopParam);
			}
		}
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shopLst", shopParamsLst).
				put("groupName", groupName)
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/queryShopCsGroups", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/queryShopCsGroups error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse queryShopCsGroups(ShopQuery shop,String groupName){
		List<ShopCommonParam> shopParamsLst=Lists.newArrayList();
		if(shop!=null){
				ShopCommonParam shopParam=new ShopCommonParam();
				shopParam.setDbName(shop.getDbName());
				shopParam.setSchemaId(shop.getSchemaId());
				shopParam.setShopId(shop.getShopId());
				shopParamsLst.add(shopParam);
		}
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shopLst", shopParamsLst).
				put("groupName", groupName)
				.toRequestEntity();
		ApiResponse apiResponse = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/queryShopCsGroups", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/queryShopCsGroups error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}
	@Override
	public ApiResponse createOrUpdateShopCsGroup(ShopQuery shop, Long groupId,String groupName){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shopId", shop.getShopId())
				.put("groupId", groupId)
				.put("groupName", groupName)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/createOrUpdateShopCsGroup", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/createOrUpdateShopCsGroup error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse deleteCsGroup(ShopQuery shop, Long groupId){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("groupId", groupId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/deleteCsGroup", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/deleteCsGroup error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}
	@Override
	public ApiResponse selectShopGroups(ShopQuery shop) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder().toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectShopGroups", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/selectShopGroups error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * 获取多店铺组
	*
	 */
	@Override
	public ApiResponse selectShopGroupMemberShops(ShopQuery shop){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId", shop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectShopGroupMemberShops", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/selectShopGroupMemberShops error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	/**
	 * 获取通用的店铺，组，客服下拉框
	 */
	@Override
	public ApiResponse selectMultiShopGroups(String dbName,List<ShopQuery> shopLst){
		ApiResponse apiResponse;
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shopLst",shopLst)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectShopGroups", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/selectShopGroups error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectMultiShopGroupByShopId(ShopQuery shopQuery) {

		ShopCommonParam shop=new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder().
				put("shop",shop)
			    .toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectMultiShopGroupByShopId", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/selectMultiShopGroupByShopId error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}
	@Override
	public ApiResponse selectGroupCsByGroupParam(ShopQuery shop, GroupParam param){
		long s1=System.currentTimeMillis();
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param",param)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectGroupCsByGroupParam", body);
		} catch (HttpClientErrorException e) {
			logger.error("/cs/group/selectGroupCsByGroupParam error:{}",e.getMessage(),e);
			throw e;
		}
		logger.info("web 获取我的客服组花费:{}ms",System.currentTimeMillis()-s1);
		return apiResponse;
	}


	// ------------------------------------------{ 权限设置 BEGIN }------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ApiResponse searchBaseTreeInfo(ShopQuery shop, String nick, Long userId) {
	    final String uri = "/setting/permission/search_tree";
	    String uriWithArg = RestOperator.mergeUriArguments(uri,
				"shopId",String.valueOf(shop.getShopId()),
						"nick",nick,
                "userId",String.valueOf(userId)
		);
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId,uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
        return res;
	}
	/**
	 * {@inheritDoc}
	 */
	@Override
	public ApiResponse searchShopAccountInfo(ShopQuery shop, String nick, Integer flag, Long userId) {
        final String uri = "/setting/permission/search_shop_account_info";
        String uriWithArg = RestOperator.mergeUriArguments(uri,"shopId",String.valueOf(shop.getShopId()),"nick",nick,"flag",flag.toString(),"userId",String.valueOf(userId));
		String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId,uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
		return res;
	}
	/**
	 * {@inheritDoc}
	 */
	@Override
	public ApiResponse insertOrUpdatePermission(ShopQuery shop,Integer type, String body) {
        final String uri = "/setting/permission/insert_update_delete";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
				"type",String.valueOf(type),
				"shopId", Objects.requireNonNull(shop.getShopId(),"shopId 为空").toString());
		String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId,uriWithArg, RestOperator.getJsonEntity(body));
		return res;
	}

    // ------------------------------------------{ 权限设置 END }------------------------------------------------


    // ------------------------------------------{ 访问码设置 BEGIN }------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse settingVisitCode(String visitCode, String visitEmail, Integer flag, ShopQuery shop) {
        final String uri = "/setting/permission/visit_code/setting";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
                "visitCode",(visitCode),
                "visitEmail",(visitEmail),
                "flag",String.valueOf(flag),
                "shopId",String.valueOf(shop.getShopId())
        );
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
        return res;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse validVisitCode(String visitCode, ShopQuery shop){
        final String uri = "/setting/permission/visit_code/valid";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
                "visitCode",visitCode,"shopId",String.valueOf(shop.getShopId()));
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
        return res;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse findVisitCode(ShopQuery shop) {
    	/*先获取email*/
		ApiResponse resEmail = findEmail(shop);
		final Map<String, Object> data = resEmail.getData();
		if (data == null){
			throw new RuntimeException("获取email失败");
		}
		final String email = (String) data.get("result");
		final String visitCode = String.valueOf(Math.random()).substring(2, 8);
		try {
			Assert.notNull(email,"email is null");
			Assert.notNull(visitCode,"visitCode is null");
			yiyiJavaMailSender.sendEmail(String.format(EmailUtils.emailContent,visitCode),email,"重置密码");
		} catch (Exception e) {
			logger.error(" send find code email error  ",e);
			throw new RuntimeException(" send find code email error ");
		}
		/*修改访问吗*/
        final String uri = "/setting/permission/visit_code/find";
		String uriWithArg = RestOperator.mergeUriArguments(uri,
                "shopId",String.valueOf(shop.getShopId()),
				"code", visitCode
				);
		String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		return usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
    }

	@Override
	public ApiResponse findEmail(ShopQuery shop){
		final String uriEmail = "/setting/permission/visit_code/get_email";
		String uriWithArg = RestOperator.mergeUriArguments(uriEmail,
				"shopId",String.valueOf(shop.getShopId())
		);
		String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		final ApiResponse rest = usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
		final String rpMsg = rest.getRpMsg();
		ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, ImmutableMap.of("email",rpMsg));
		return rest;
	}

	/**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse selectLoginLog(ShopQuery shop, Date startDate, Date endDate, String nick) {
        final String uri = "/user/log/login/select";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
                "shopId",String.valueOf(shop.getShopId()),
                "startDate", CommonDateUtils.formatYMd(startDate),
                "endDate", CommonDateUtils.formatYMd(endDate),
                "nick",nick
        );
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
        return res;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse selectLoginLogDetails(ShopQuery shop, Date startDate, Date endDate, String nick) {
        final String uri = "/user/log/login/detail";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
                "shopId",String.valueOf(shop.getShopId()),
                "startDate", CommonDateUtils.formatYMd(startDate),
                "endDate", CommonDateUtils.formatYMd(endDate),
                "nick",nick
        );
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
        return res;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse selectOperationLog(ShopQuery shop, Date startDate, Date endDate, String nick, String optType){
        final String uri = "/user/log/operation/select";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
                "shopId",String.valueOf(shop.getShopId()),
                "startDate", CommonDateUtils.formatYMd(startDate),
                "endDate", CommonDateUtils.formatYMd(endDate),
                "nick",nick,
				"optType",optType
        );
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId, uriWithArg,RequestEntityBuilder.builder().toRequestEntity());
        return res;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    public ApiResponse insertOperationLog(ShopUserDTO shopDto,ShopQuery shop, String optContent,String optType) {
        final String uri = "/user/log/operation/insert";
        String uriWithArg = RestOperator.mergeUriArguments(uri,
                "shopId",shop.getShopId().toString(),
                "optType", optType,
                "nick",shopDto.getNick(),

                "userId",String.valueOf(shopDto.getUserId())
        );
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("optContent", optContent)
				.toRequestEntity();
        String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse res = usermgrRestTemplate.postRest(msServiceId, uriWithArg,body);
        return res;
    }

    // ------------------------------------------{ 访问码设置 END }------------------------------------------------

    @Override
	public ApiResponse updateIsShowMultipleShop(ShopQuery mainShop, ShopUserDTO currentUser) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("currentUser",currentUser)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/showShops", body);
		return apiResponse;
	}

	@Override
	public ApiResponse selectShopGroupsOfOwner(ShopQuery mainShop) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/selectShopGroupsOfOwner", body);
		return apiResponse;
	}

	@Override
	public ApiResponse createNewShopGroup(ShopQuery mainShop, String shopGroupName) {
		ApiResponse apiResponse;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.put("shopGroupName", shopGroupName)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/createNewShopGroup", body);
		return apiResponse;
	}

	@Override
	public ApiResponse updateShopGroupName(ShopQuery mainShop, String shopGroupId, String shopGroupName){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.put("shopGroupId", shopGroupId)
				.put("shopGroupName", shopGroupName)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId( ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/updateShopGroupName", body);
		return apiResponse;
	}

	@Override
	public ApiResponse updateShopGroupMutualWatch(String shopGroupId, Boolean mutualWatch){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopGroupId",shopGroupId)
				.put("mutualWatch", mutualWatch)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/updateShopGroupMutualWatch", body);
		return apiResponse;
	}

	@Override
	public ApiResponse deleteShopGroup(ShopQuery mainShop, String shopGroupId) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.put("shopGroupId", shopGroupId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/deleteShopGroup", body);
		return apiResponse;
	}

	@Override
	public ApiResponse sendInviteRequestForShopGroup(ShopQuery mainShop, String inviteShopInfo, String shopGroupId){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.put("inviteShopInfo", inviteShopInfo)
				.put("shopGroupId", shopGroupId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/sendInviteRequestForShopGroup", body);
		return apiResponse;
	}

	@Override
	public ApiResponse moveShopToShopGroup(ShopQuery mainShop, String moveShopIds, String shopGroupId) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.put("moveShopIds", moveShopIds)
				.put("shopGroupId", shopGroupId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/moveShopToShopGroup", body);
		return apiResponse;
	}

	@Override
	public ApiResponse shopGroupSystemSettingSynchronization(ShopQuery selectShop, String toShopIds, String syncType) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("fromShopId",selectShop.getShopId()+"")
				.put("toShopIds", toShopIds)
				.put("syncType", syncType)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/shopGroupSystemSettingSynchronization", body);
		return apiResponse;
	}

	@Override
	public ApiResponse selectJoinedMyGroupShopList(ShopQuery mainShop, String shopGroupId){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.put("shopGroupId", shopGroupId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/selectJoinedMyGroupShopList", body);
		return apiResponse;
	}

	@Override
	public ApiResponse selectJoinGroupList(ShopQuery mainShop) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/selectJoinGroupList", body);
		return apiResponse;
	}

	@Override
	public ApiResponse selectShopAccredit(ShopQuery mainShop)  {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("mainShopId",mainShop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/selectShopAccredit", body);
		return apiResponse;
	}

	@Override
	public ApiResponse updateAuthorizeShopStatus(ShopQuery mainShop, Integer status, String requestId) {
		ApiResponse apiResponse;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("status",status)
				.put("requestId",requestId)
				.put("mainShopId",mainShop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shopGroup/updateAuthorizeShopStatus", body);
		return apiResponse;
	}

	@Override
	public ApiResponse updateAdminSubUserType(String nick, String type) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("nick",nick)
				.put("type",type)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/sysetting/updateSubUserType", body);
		return apiResponse;
	}


	@Override
	public ApiResponse updatePerformanceSettings(ShopQuery selectedShop, ShopSystemsettingDTO shopSystemsettingDTO, String requestUrl)
			throws Exception {
		ApiResponse apiResponse = new ApiResponse();
		String params = JSON.toJSONString(shopSystemsettingDTO);
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.put("shopSystemsettingDTO",params)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, requestUrl, body);
		 } catch (Exception e) {
			logger.error("updatePerformanceSettings:{}",e.getMessage(),e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse addFliterBuynick(ShopQuery selectedShop, String nick,String currentUser) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.put("nick",nick)
				.put("currentUser",currentUser)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_ADDFLITERBUYNICK.getName(), body);
		 } catch (Exception e) {
			logger.error("addFliterBuynick:{}",e.getMessage(),e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse deleteFliterBuynick(ShopQuery selectedShop, String nick) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.put("nick",nick)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_DELETEFLITERBUYNICK.getName(), body);
		 } catch (Exception e) {
			logger.error("selectCategoryLst:{}",e.getMessage(),e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse getSystemSettingForQN(ShopQuery selectedShop) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_QUERYSYSTEMSETFORQN.getName(), body);
		 } catch (Exception e) {
			logger.error("selectCategoryLst:{}",e.getMessage(),e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse queryIsModifyEnquiryAndOutStockValidTime(ShopQuery selectedShop)  {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_QUERYISMODIFYENQUIRYANDOUTSTOCKVALIDTIME.getName(), body);
		} catch (HttpClientErrorException e) {
			logger.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_03_07.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_03_07.getMsg());
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCategoryLst(ShopQuery selectedShop) {
		ApiResponse apiResponse = new ApiResponse();
		ShopCommonParam shopParam = new ShopCommonParam(selectedShop.getShopId(), selectedShop.getSchemaId(), selectedShop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopStr", shopParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(selectedShop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		try {
			apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_GOODINFO_SELECTCATEGORYLST.getName(), body);
		} catch (Exception e) {
			logger.error("selectCategoryLst:{}",e.getMessage(),e);
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCategoryLstV2(ShopQuery selectedShop) {
		ApiResponse apiResponse = new ApiResponse();
		ShopCommonParam shopParam = new ShopCommonParam(selectedShop.getShopId(), selectedShop.getSchemaId(), selectedShop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopStr", shopParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(selectedShop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		try {
			apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_GOODINFO_SELECTCATEGORYLSTV2.getName(), body);
		} catch (Exception e) {
			logger.error("selectCategoryLst:{}",e.getMessage(),e);
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectGoodsinfoLst(ShopQuery selectedShop, String categoryId, String name, Integer status) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.put("categoryId", categoryId)
				.put("name", name)
				.put("status", status)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(selectedShop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		try {
			apiResponse = popSubRestTemplate.postRest(serviceId, RequestUrlEnum.GOODINFO_SELECTGOODSINFOLST.getName(), body);
		 } catch (Exception e) {
			logger.error("selectGoodsinfoLst:{}",e.getMessage(),e);
		}
		return apiResponse;
	}

	@Override
	public ApiResponse addGoodsFilter(ShopQuery selectedShop, String goodList) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.put("goodList", goodList)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_ADDGOODSFILTER.getName(), body);
        } catch (HttpClientErrorException e) {
           logger.error("addGoodsFilter {}", e.getMessage());
        }
		return apiResponse;
	}

	@Override
	public ApiResponse deleteGoodsFilter(ShopQuery selectedShop, String numIid) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", selectedShop.getShopId())
				.put("numIid", numIid)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_DELETEGOODSFILTER.getName(), body);
        } catch (HttpClientErrorException e) {
        	logger.error("deleteGoodsFilter", e.getMessage());
        }
		return apiResponse;
	}

	@Override
	public ApiResponse selectUserByShopId(ShopQuery shop)  {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shopId",shop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/user/manage/selectUser", body);
		return apiResponse;
	}

	@Override
	public ApiResponse selectShopAccountForShop(ShopQuery shop, String includeMainAccount)  {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shop.getShopId())
				.put("includeMainAccount", includeMainAccount)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/sysetting/selectShopAccountForShop", body);
		return apiResponse;
	}

	@Override
	public ShopDTO getShopInfo(Long shopId) {
		ApiResponse apiResponse;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId( ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/getShopInfo", body);
			if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
				ShopDTO shopDto = JSON.parseObject(JSON.toJSONString(apiResponse.getData().get("shop")), ShopDTO.class);
				return shopDto;
			}
		} catch (HttpClientErrorException e) {
			logger.error("getShopInfo error:{}",e.getMessage(),e);
			throw e;
		}

		return null;
	}
	@Override
	public ApiResponse getRealTimeShopByShopId(Long shopId){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/getRealTimeShopByShopId", body);
		} catch (HttpClientErrorException e) {
			logger.error("getRealTimeShopByShopId error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCsCountByShopIdByType(Long shopId,Integer type){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("type", type)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectCsCountByShopIdByType", body);
		} catch (HttpClientErrorException e) {
			logger.error("selectCsCountByShopIdByType error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse selectCsByShopIdByTypeForPlugin(Long shopId,Integer type){
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("type", type)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/manage/selectCsByShopIdByType", body);
		} catch (HttpClientErrorException e) {
			logger.error("selectCsByShopIdByType error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public ApiResponse getShopInfoByShopNameForPlugin(String  shopName) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopName", shopName)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/getShopInfoForPlugin", body);
		} catch (HttpClientErrorException e) {
			logger.error("getShopInfoByShopNameForPlugin error:{}",e.getMessage(),e);
			throw e;
		}

		return apiResponse;
	}


    @Override
    public RestApiResponse2<List<DeptShop>> selectDeptShopInfoByDeptId(String shopName,Long deptId,String id,String type) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("deptId", deptId)
                .put("shopName", shopName)
                .put("id", id)
                .put("type", type)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        RestApiResponse2<List<DeptShop>> resp=null;
		try {
			resp = usermgrRestTemplate.postRestOfResult2(serviceId, "/dept/shop/selectDeptShopInfoByDeptId", body,
					new ParameterizedTypeReference<RestApiResponse2<List<DeptShop>>>() {
					});
		} catch (Exception e) {
			logger.error("selectDeptShopInfoByDeptId error:{}", e.getMessage(), e);
			throw e;
		}

		return resp;
    }


	@Override
	public ApiResponse selectShopSettingForLoginCs(Long shopId) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/sysetting/selectShopCsLoginSetting", body);
		} catch (HttpClientErrorException e) {
			logger.error("getShopSettingForLoginCs error:{}",e.getMessage(),e);
			throw e;
		}

		return apiResponse;
	}



	@Override
	public ApiResponse realTimePerformanceSettings(ShopQuery shop, RealTimePerformanceSettingsParam param) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shop.getShopId())
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/sysetting/realTimePerformanceSettings", body);
		} catch (HttpClientErrorException e) {
			logger.error("realTimePerformanceSettings error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}


	@Override
	public ApiResponse selectRealTimePerformanceSettings(ShopQuery shop) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shop.getShopId())
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/sysetting/selectRealTimePerformanceSettings", body);
		} catch (HttpClientErrorException e) {
			logger.error("selectRealTimePerformanceSettings error:{}",e.getMessage(),e);
			throw e;
		}
		return apiResponse;
	}


	@Override
	public ApiResponse selectGroupCsByGroupParamForPlugin(ShopQuery shop, GroupParam param){
		long s1=System.currentTimeMillis();
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param",param)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectGroupCsByGroupParam", body);
		} catch (HttpClientErrorException e) {
			logger.error(" plugin /cs/group/selectGroupCsByGroupParam error:{}",e.getMessage(),e);
			throw e;
		}
		logger.info("web plugin 获取我的客服组花费:{}ms",System.currentTimeMillis()-s1);
		return apiResponse;
	}

    @Override
    public RestApiResponse2<Object> batchUpdateCsofGroup(Long shopId, String groupId, String csNicks) {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("groupId", groupId)
				.put("csNicks", csNicks)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		RestApiResponse2<Object> resp=null;
		try {
			resp = usermgrRestTemplate.postRestOfResult2(serviceId, "/cs/manage/batchUpdateCsofGroup", body, new ParameterizedTypeReference<RestApiResponse2<Object>>() {});
		} catch (Exception e) {
			logger.error("batchUpdateCsofGroup error:{}", e.getMessage(), e);
			throw e;
		}

		return resp;
    }


	@Override
	public RestApiResponse2<List<DeptShop>> selectDeptShopInfoByDeptId(String shopName, Long deptId) {
		   HttpEntity<Object> body = RequestEntityBuilder.builder()
	                .put("deptId", deptId)
	                .put("shopName", shopName)
	                .toRequestEntity();
	        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
	        RestApiResponse2<List<DeptShop>> resp=null;
			try {
				resp = usermgrRestTemplate.postRestOfResult2(serviceId, "/dept/shop/selectDeptShopInfoByDeptId", body,
						new ParameterizedTypeReference<RestApiResponse2<List<DeptShop>>>() {
						});
			} catch (Exception e) {
				logger.error("selectDeptShopInfoByDeptId error:{}", e.getMessage(), e);
				throw e;
			}

			return resp;
	}


	@Override
	public Integer selectCsCountByShopIdByStatus(Long shopId, Integer status) {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("status", status)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

		RestResponseTypeRef<Integer> restResponse = usermgrRestTemplate.postRest(serviceId,"/cs/manage/selectCsCountByShopIdByStatus", body,
				new ParameterizedTypeReference<RestResponseTypeRef<Integer>>(){});
		Integer count=0;
		if(restResponse.getSuccess()){
			count=restResponse.getData();
		}
		return count;
	}

    @Override
    public ApiResponse addGoodsFilterOfSpu(ShopQuery selectedShop, String goodList, List<ShopGoodsSku> data) {
        ApiResponse apiResponse = new ApiResponse();
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", selectedShop.getShopId())
                .put("goodList", goodList)
                .put("shopGoodsSku", data)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        try {
            apiResponse = usermgrRestTemplate.postRest(serviceId, RequestUrlEnum.SHOP_SYSETTING_ADDGOODSFILTEROFSPU.getName(), body);
        } catch (HttpClientErrorException e) {
            logger.error("addGoodsFilterOfSpu {}", e.getMessage());
        }
        return apiResponse;
    }

	@Override
	public String selectGroupIdByShopId(String shopId) {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId, "/cs/group/selectGroupIdByShopId", body);
		if(apiResponse.getRpCode().equals("1001")){
			return apiResponse.getData().get("result").toString();
		}
		return null;
	}

}
