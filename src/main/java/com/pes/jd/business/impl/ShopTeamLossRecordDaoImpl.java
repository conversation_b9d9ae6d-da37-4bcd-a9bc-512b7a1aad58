package com.pes.jd.business.impl;

import com.pes.jd.business.ShopTeamLossRecordDao;
import com.pes.jd.mapper.ShopTeamLossRecordMapper;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopTeamLossRecordDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

@Repository
public class ShopTeamLossRecordDaoImpl implements ShopTeamLossRecordDao {

	@Resource
	private ShopTeamLossRecordMapper shopTeamLossRecordMapper;
	
	@Override
	public int insertShopTeamLossRecord(JobShopDTO shop, ShopTeamLossRecordDTO shopLoss) {
		if(shopLoss == null){
			return 0;
		}
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_LOSS_RECORD.getName());
		return shopTeamLossRecordMapper.insertShopTeamLossRecord(shopLoss,tableName);
	}

	@Override
	public int deleteShopTeamLossRecordByShopByDate(JobShopDTO shop, Date date, int lossType) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_LOSS_RECORD.getName());
		return shopTeamLossRecordMapper.deleteShopTeamLossRecordByShopByDate(shop.getShopId(),date,lossType,tableName);
	}

}
  
