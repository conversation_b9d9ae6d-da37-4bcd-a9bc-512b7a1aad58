package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.*;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.dao.*;
import com.pes.jd.exception.SysSettingException;
import com.pes.jd.framework.StopWatch;
import com.pes.jd.model.BO.*;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.OrderStatusEnum;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.task.executor.KeyWordCheckExecutor;
import com.pes.jd.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 生成情感分析原始数据
 *
 */
@Service
public class SentimentAnalysisBusinessImpl implements SentimentAnalysisBusiness {

	private static Logger logger = LoggerFactory.getLogger(SentimentAnalysisBusinessImpl.class);

	@Resource
	private CsChatlogDao csChatlogDao;

	@Resource
	private SentimentAnalysisDao sentimentAnalysisDao;
	/**
	 *
	 * @param jobShop
	 * @param jobDate
	 * @param isDelData
	 * @return
	 */
	@Override
	public void handleSentimentAnalysis(JobShopQuery jobShop, JobDateQuery jobDate,
			boolean isDelData) {

		//获取时间
		Date date = jobDate.getDate();
		Date startDate = DateUtils.getStartTimeOfDate(date);
		Date endDate = DateUtils.getEndTimeOfDate(date);

		//查询数据库 该店铺当天聊天记录
		List<ChatlogDTO> chatlogs = csChatlogDao.selectChatLogByShopIdByDate(jobShop.getShop(), startDate, endDate);
		List<String> sysKeywordList = jobShop.getSysWordLst();
		if (CollectionUtils.isEmpty(sysKeywordList)||CollectionUtils.isEmpty(chatlogs)) {
			return ;
		}

		Set<String> keywordSet = Sets.newHashSet();
		if (CollectionUtils.isNotEmpty(sysKeywordList)) {
			keywordSet.addAll(sysKeywordList);//去重
		}
		List<String> keywordList = Lists.newArrayList(keywordSet);

		if (CollectionUtils.isNotEmpty(chatlogs)) {
			List<SentimentAnalysisDO> sAList = Lists.newArrayList();
			//顾客聊天记录--顾客命中的敏感词
			List<ChatlogDTO> buyerChatlogs = chatlogs.stream().filter(ele -> ele.getDirection() == 1).collect(Collectors.toList());
			List<SentimentAnalysisDO> buyerSALst = hitKeyWord(keywordList, buyerChatlogs, CommonConstants.WARNING_BUYER_RAIL, jobShop.getShop().getTitle());
			if(!buyerSALst.isEmpty()) sAList.addAll(buyerSALst);

			//客服聊天记录--客服命中的敏感词
			List<ChatlogDTO> csChatlogs = chatlogs.stream().filter(ele -> ele.getDirection() != 1).collect(Collectors.toList());
			List<SentimentAnalysisDO> csSALst = hitKeyWord(keywordList, csChatlogs, CommonConstants.WARNING_CS_VIOLATION, jobShop.getShop().getTitle());
			if(!csSALst.isEmpty()) sAList.addAll(csSALst);

			if(!sAList.isEmpty()){
				//删除date下所有记录
				sentimentAnalysisDao.batchDeleteSentimentAnalysisByDate(jobShop.getShop(), date);
				//增加date下所有记录
				sentimentAnalysisDao.batchInsertSentimentAnalysis(jobShop.getShop(), sAList);
			}
		}
	}

	/**
	 * 聊天内容匹配到敏感词库
	 * @param keywordList
	 * @param chatLogs
	 * @param warningType
	 * @return
	 */
	private List<SentimentAnalysisDO> hitKeyWord(List<String> keywordList, List<ChatlogDTO> chatLogs, Byte warningType, String shopTitle){
		List<SentimentAnalysisDO> sentimentAnalysisLst = Lists.newArrayList();
		if(!chatLogs.isEmpty()){
			for (ChatlogDTO chatLog : chatLogs) {
				String content = this.removeLabel(chatLog.getContent());
				for (String keyword : keywordList) {
					if (content.contains(keyword)) {
						SentimentAnalysisDO sentiment = SentimentAnalysisDO.builder().shopId(chatLog.getShopId())
																					.shopTitle(shopTitle)
																					.csNick(chatLog.getCsNick())
																					.customer(chatLog.getBuyer())
																					.sid(chatLog.getSid())
																					.warningTime(chatLog.getChatTime())
																					.warningType(warningType)
																					.keyword(keyword)
																					.content(content)
																					.status(CommonConstants.SENTIMENT_UNPROCESSED_STATUS)
																					.sentimentType(CommonConstants.SENTIMENT_DEFAULT_TYPE)
																					.date(DateUtils.getStartTimeOfDate(chatLog.getChatTime()))
																					.build();


						sentimentAnalysisLst.add(sentiment);
					}
				}
			}
		}

		return sentimentAnalysisLst;
	}

	//因为发卡片会有标签，出去干扰的标签
	private String removeLabel(String chatlogContent) {
		return chatlogContent.replaceAll("<[^>]*>", "");
	}

}
