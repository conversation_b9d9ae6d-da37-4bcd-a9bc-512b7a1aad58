package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.CsReceiveQualityBusiness;
import com.pes.jd.business.PerformanceRuleBusiness;
import com.pes.jd.dao.CsBuyerServiceIndexDao;
import com.pes.jd.dao.CsChatlogDao;
import com.pes.jd.dao.CsServiceIndexDao;
import com.pes.jd.model.BO.CsBuyerServiceIndexBO;
import com.pes.jd.model.DO.CsBuyerServiceIndexDO;
import com.pes.jd.model.DO.CsServiceIndexDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客服服务质量 业务列类
 * 客服未回复，平均等待时间，客服字数
 * <AUTHOR>
 *
 */
@Service
public class CsReceiveQualityBusinessImpl implements CsReceiveQualityBusiness {
	
	private Logger logger = LoggerFactory.getLogger(CsReceiveQualityBusinessImpl.class);

	@Resource
	private CsChatlogDao chatlogDao;

	@Resource
	private CsServiceIndexDao csServiceIndexDao;
	
	@Resource
	private CsBuyerServiceIndexDao csBuyerServiceIndexDao;
	
	@Resource
	private PerformanceRuleBusiness performanceRuleBusiness;



	/**
	 * 处理客服服务质量（客服未回复，平均等待时间，客服字数）
	 * @param isDelData
	 */
	@Override
	public void handleCsReceiveQuality(JobShopQuery jobShop, JobDateQuery jobDate,
									   boolean isDelData) {

		long s = System.currentTimeMillis();

		ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

		Integer maxWaitTime = sys.getMaxWaitTime();

		List<Date> dates = jobDate.getCommonDates();
		if(CollectionUtils.isEmpty(dates)){
			logger.error("req dates is empty");
			return;
		}
		JobShopDTO shop = jobShop.getShop();
		for (Date date : dates) {

			Date sDate = date;
			Date eDate =  DateUtil.getEndTimeOfDate(date);



			JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE,CommonConstants.CS_STATUS_NOT_LOCK);
			List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
			if(CollectionUtils.isEmpty(targetCsLst)){
				continue;
			}
			List<CsBuyerServiceIndexDO> dailyCsBuyerServiceIndexLst = Lists.newArrayList();
			List<CsServiceIndexDO> csServiceIndexLst = Lists.newArrayListWithCapacity(targetCsLst.size());
			for (CsDTO cs : targetCsLst) {
				String csNick = cs.getNick();



				List<ReceiveQualityChatlogDTO> chatLogLst = chatlogDao.selectShopCsChatLogLst(shop, csNick, sDate, eDate);

				//买家服务指标
				if(CollectionUtils.isNotEmpty(chatLogLst)){

					List<CsBuyerServiceIndexDO> csBuyerServiceIndexLst = calCsBuyerServiceIndexFromChatlogs(jobShop, date, csNick, chatLogLst);
					if(CollectionUtils.isNotEmpty(csBuyerServiceIndexLst)){
						dailyCsBuyerServiceIndexLst.addAll(csBuyerServiceIndexLst);
						//客服服务指标
						CsServiceIndexDO cssi = calCsServiceIndex(jobShop, date, csNick, csBuyerServiceIndexLst);
						if(cssi != null){
							csServiceIndexLst.add(cssi);
						}
					}
				}
			}

			int num1 = csBuyerServiceIndexDao.deleteShopCsBuyerServiceIndexByDate(shop, date);
			logger.debug("delete Shop CsBuyerServiceIndex num:{}",num1);
			int num2 = csBuyerServiceIndexDao.batchInsertCsBuyerServiceIndex(shop, date, dailyCsBuyerServiceIndexLst);
			logger.debug("batch Insert CsBuyerServiceIndex num:{}",num2);

			int num3 = csServiceIndexDao.deleteShopCsServiceIndexByDate(shop, date);
			logger.debug("delete Shop CsServiceIndex num:{}",num3);
			int num4 = csServiceIndexDao.batchInsertCsServiceIndex(shop, date, csServiceIndexLst);
			logger.debug("batch Insert CsServiceIndex num:{}",num4);
		}

		long e = System.currentTimeMillis();
		logger.info("handle shop CsServiceIndex end,time:{}s",(e-s)/1000);
		return;
	}
	
	/**
	 * 
	 * @Title: calCsAvgWaitTimeFromChatlogs 
	 * @Description: (计算平均首次等待时间和平均等待时间，过滤自动回复)
	 * @return List<CsServiceIndexDO>    返回类型
	 */
	private List<CsBuyerServiceIndexDO> calCsBuyerServiceIndexFromChatlogs(JobShopQuery jobShop, Date date, String csNick,
			List<ReceiveQualityChatlogDTO> chatLogLst){
		//店铺
		JobShopDTO shop = jobShop.getShop();
		//系统设置
		ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
		/*
		 * 具体的系统设置项
		 */
		long maxWaitTimeMillisecond = sys.getMaxWaitTime() * 60000L;
		
		Map<String, CsBuyerServiceIndexBO> buyerNickMap = Maps.newHashMap();
		if(CollectionUtils.isNotEmpty(chatLogLst)){
			int chatLogSize = chatLogLst.size();
			
			List<CsBuyerServiceIndexBO> buyerIndexTempLst = Lists.newArrayList();
			String buyerNick = null;
			CsBuyerServiceIndexBO buyerIndex = null;

			//计算平均等待时间，首次响应时间
			for (int i = 0; i < chatLogSize; i++) {

				ReceiveQualityChatlogDTO chatlog = chatLogLst.get(i);
				
				buyerNick = chatlog.getBuyer();
				//TODO(过滤内部聊天)
				buyerIndex = buyerNickMap.get(buyerNick);//to_id 买家
				if(buyerIndex == null){
					buyerIndex = new CsBuyerServiceIndexBO(buyerNick);
					buyerNickMap.put(buyerNick, buyerIndex);
					buyerIndexTempLst.add(buyerIndex);
				}
				
				if(chatlog.getDirection() == 1){//买家->卖家(买家到客服)
					
					if(buyerIndex.isFlag()){//flag:默认为true
						buyerIndex.setBuyerChatDate(chatlog.getChatTime());
						//找第一个买家说话的log,然后找客服说话
						buyerIndex.setFlag(false);
						if(buyerIndex.getFirstConsultTime() == null){
							buyerIndex.setFirstConsultTime(chatlog.getChatTime());
						}
					}
				}else if(chatlog.getDirection() == 0){//卖家->买家，客服说话
					if(chatlog.getContent() != null){
						
						if(buyerIndex != null && !buyerIndex.isFlag()){
							if (performanceRuleBusiness.isCsAutoReplyBySysettingChat(sys, chatlog.getMt(), chatlog.getContent())) { // 表示开启自动回复过滤功能
								//自动回复不算客服回复，继续寻找下一个客服回复
								buyerIndex.setFlag(false);	
								
							}else {
								//表示本次是客服在买家说完后首次回复
								buyerIndex.setNonReply(false);

								buyerIndex.setCsReplyDate(chatlog.getChatTime());
								
								long intervalTime = buyerIndex.getCsReplyDate().getTime() - buyerIndex.getBuyerChatDate().getTime();
								if(performanceRuleBusiness.isValidCsResponse(intervalTime, maxWaitTimeMillisecond)){
									buyerIndex.setTotalChatIntervalTime(buyerIndex.getTotalChatIntervalTime() + intervalTime);
									buyerIndex.setTotalChatSegmentNum(buyerIndex.getTotalChatSegmentNum() + 1);
								}
								if(buyerIndex.getFirstReplyDate() == null){

									if(performanceRuleBusiness.isValidCsResponse(chatlog.getChatTime(), buyerIndex.getFirstConsultTime(), maxWaitTimeMillisecond)){
										buyerIndex.setFirstReplyDate(chatlog.getChatTime());
									}else{
										buyerIndex.setFirstConsultTime(null);
									}
								}
								buyerIndex.setFlag(true);
							}
						}
					}
				}
			}
			//计算客服字数，客服消息数
			for (int i = 0; i < chatLogSize; i++) {
				ReceiveQualityChatlogDTO chatlog = chatLogLst.get(i);
				buyerNick = chatlog.getBuyer();
				//TODO(过滤内部聊天 undo)
				buyerIndex = buyerNickMap.get(buyerNick);//to_id 买家
				if(buyerIndex == null){
					buyerIndex = new CsBuyerServiceIndexBO(buyerNick);
					buyerNickMap.put(buyerNick, buyerIndex);
					buyerIndexTempLst.add(buyerIndex);
				}
				if(buyerIndex.getSessionStartDate() == null){
					buyerIndex.setSessionStartDate(chatlog.getChatTime());
				}
					
				buyerIndex.setSessionEndDate(chatlog.getChatTime());
				
				if(chatlog.getDirection() == 1){//(买家到客服-表示买家说话)
					buyerIndex.setBuyerChatNum(buyerIndex.getBuyerChatNum()+1);
					
				}else if(chatlog.getDirection() == 0){//客服->买家

					if(chatlog.getContent() != null){
						//@TODO(自动回复过滤-首次响应时间，平均响应时间，客服消息数，客服字数)
						boolean isAutoReply =  performanceRuleBusiness.isCsAutoReplyBySysettingChat(sys, chatlog.getMt(), chatlog.getContent());
						if (isAutoReply) { // 表示开启自动回复过滤功能	
							//计算客服字数过滤掉自动回复
						}else {
							if(buyerIndex.getFirstReceiveDate() == null){
								buyerIndex.setFirstReceiveDate(chatlog.getChatTime());
							}
							buyerIndex.setLastReceiveDate(chatlog.getChatTime());
							long wordNum = buyerIndex.getCsWordNum()+chatlog.getContent().replace(" ", "").length();
							buyerIndex.setCsWordNum(wordNum);
							buyerIndex.setCsReplyNum(buyerIndex.getCsReplyNum() + 1);
						}
					}
				}
			}
			
			if(CollectionUtils.isNotEmpty(buyerIndexTempLst)){
				List<CsBuyerServiceIndexDO> buyerIndexLst = Lists.newArrayList();
				CsBuyerServiceIndexDO cssi;
				for(CsBuyerServiceIndexBO att : buyerIndexTempLst){
					cssi = new CsBuyerServiceIndexDO(shop.getShopId(),date,csNick,att.getBuyerNick());
					cssi.setAvgWaitTime(att.getTotalChatSegmentNum() > 0 ? att.getTotalChatIntervalTime()*1.0D / att.getTotalChatSegmentNum() / 1000D : 0D);
					if(att.getFirstReplyDate() == null){
						cssi.setAvgWaitTimeFirst(0D);
					}else{
						cssi.setAvgWaitTimeFirst((att.getFirstReplyDate().getTime() - att.getFirstConsultTime().getTime())*1.0D/ 1000D);
					}
					
					cssi.setIsNonReply(att.isNonReply());
					cssi.setCsReplyNum(att.getCsReplyNum());
					cssi.setBuyerChatNum(att.getBuyerChatNum());
					cssi.setCsWordNum(att.getCsWordNum());
					cssi.setSessionTime((att.getSessionEndDate().getTime() - att.getSessionStartDate().getTime())/1000);
					cssi.setFirstReceiveDate(att.getFirstReceiveDate());
					cssi.setLastReceiveDate(att.getLastReceiveDate());
					buyerIndexLst.add(cssi);
				}
				return buyerIndexLst;
			}
		}
		return null;
	}
	
	
	
	/**
	 * 
	 * @Title: calCsAvgWaitTimeFromChatlogs 
	 * @Description: (计算平均首次等待时间和平均等待时间，过滤自动回复)
	 * @return int    返回类型
	 */
	private CsServiceIndexDO calCsServiceIndex(JobShopQuery jobShop, Date date, String csNick,
			List<CsBuyerServiceIndexDO> csServiceIndexLst){
		JobShopDTO shop = jobShop.getShop();
		
		if(CollectionUtils.isEmpty(csServiceIndexLst)){
			return null;
		}
		
		CsServiceIndexDO csServiceIndex = new CsServiceIndexDO(shop.getShopId(),date,csNick);
		double totalAvgWaitTime = 0D;
		double totalAvgWaitTimeFirst = 0D;
		int effectiveNum = 0;
		int totalCsReplyNum = 0;
		int totalBuyerChatNum = 0;
		long totalWordNum = 0L;
		long totalSessionTime = 0L; 
		int totalNonReplyNum = 0;
		for (CsBuyerServiceIndexDO cbsi : csServiceIndexLst) {
			if(cbsi.getAvgWaitTime() >0){
				effectiveNum++;
			}
			totalAvgWaitTime += cbsi.getAvgWaitTime();
			totalAvgWaitTimeFirst += cbsi.getAvgWaitTimeFirst();
			
			totalCsReplyNum += cbsi.getCsReplyNum();
			totalBuyerChatNum += cbsi.getBuyerChatNum();
			totalWordNum += cbsi.getCsWordNum();
			totalSessionTime += cbsi.getSessionTime();
			if(cbsi.getIsNonReply()){
				totalNonReplyNum++;
			}
		}
		
		csServiceIndex.setAvgWaitTime(effectiveNum>0?totalAvgWaitTime / effectiveNum:0D);
		csServiceIndex.setAvgWaitTimeFirst(effectiveNum>0?totalAvgWaitTimeFirst / effectiveNum:0D);
		csServiceIndex.setCsReplyNum(totalCsReplyNum);
		csServiceIndex.setBuyerChatNum(totalBuyerChatNum);
		csServiceIndex.setCsWordNum(totalWordNum);
		csServiceIndex.setSessionTime(totalSessionTime);
		csServiceIndex.setNonReplyNum(totalNonReplyNum);
		csServiceIndex.setQaRate(totalBuyerChatNum>0?totalCsReplyNum*1.0/totalBuyerChatNum:0D);
		return csServiceIndex;
	}
/*	
	*//**
	 * @Title: calCsNoReplyNumFromChatLogs 
	 * @Description: (计算未回复数)
	 * @param csId
	 * @param csnrn
	 * @param chatlogList
	 * @param sys
	 * @param subUserIdsStr
	 * @return
	 *//*
	public CsNoreplyDO calCsNoReplyNumFromChatLogs(JobShopQuery jobShop, Date date, String csNick, Date startDate, Date endDate, 
			List<ReceiveQualityChatlogDTO> chatLogLst, boolean isDelData, List<Date> csServicePeriodLst){
		
		ShopSystemsettingDTO sys = shop.getShopSystemsetting();
		boolean isFilteAutoReply = sys.getAutoReplySwitch();
		
		JobShopDTO shop = shop.getJobShop();
		
		CsNoreplyDO csnr = new CsNoreplyDO(shop.getShopId(),date,csNick);
		
		List<String>  forwordOutBuyerLst = csChatpeerDao.selectShopCsForwordOutBuyerByDate(jobShop, csNick, startDate, endDate);
		Set<String> forwordOutBuyerSet = new HashSet<String>(forwordOutBuyerLst);
		//按照买家进行分组
		Map<String,List<ReceiveQualityChatlogDTO>> buyerChatLogMap = new HashMap<String, List<ReceiveQualityChatlogDTO>>();
		for(ReceiveQualityChatlogDTO chatLog : chatLogLst){
			//如果不在有效绩效时间段内，不参与计算
			if(!checkChatLogIsValid(chatLog, csServicePeriodLst)){
				continue;
			}
			String buyerNick = chatLog.getBuyer();
			if(buyerChatLogMap.containsKey(buyerNick)){
				buyerChatLogMap.get(buyerNick).add(chatLog);
			}else{
				List<ReceiveQualityChatlogDTO> ls = new ArrayList<ReceiveQualityChatlogDTO>();
				ls.add(chatLog);
				buyerChatLogMap.put(buyerNick, ls);
			}
		}
		
		int noReplyNum = 0;
		String noReplyBuyers = "";
		//遍历map 判断客服是否回复
		for(Map.Entry<String, List<ReceiveQualityChatlogDTO>> entry : buyerChatLogMap.entrySet()){
			String buyerNick = entry.getKey();
			if(forwordOutBuyerSet.contains(buyerNick)){
				continue;
			}
			
			//TODO(过滤内部聊天)
			List<ReceiveQualityChatlogDTO> buyerList = entry.getValue();
			boolean replyFlag = false;
			//判断客服是否回复
			ReceiveQualityChatlogDTO chatlog = null;
			for (int i=0, size= buyerList.size();i<size; i++) {
				chatlog = buyerList.get(i);
				if( chatlog.getDirection() == 0 ){//客服说话
					if(!isFilteAutoReply){//没有开启自动回复过滤
						replyFlag = true;
						break;
					}else if(isCsOwnReply(buyerList, i, chatlog)){
						replyFlag = true;
						break;
					}
					
				}
			}
			if(!replyFlag){
				//是否记作未回复人数
				noReplyNum ++;
				noReplyBuyers += buyerNick+",";
			}
		}
		if (noReplyNum > 0 && StringUtils.isNotBlank(noReplyBuyers)) {
			csnr.setNoReplyNum(noReplyNum);
			csnr.setNoReplyCsNick(noReplyBuyers.substring(0, noReplyBuyers.length() - 1));
		}
		return csnr;
	}*/

	
	/**
	 * 判断是否是客服自己回复的
	 * @param chatlogs
	 * @param index
	 * @param targetCsChatlog
	 * @return
	 */
/*	public static boolean isCsOwnReply(List<ReceiveQualityChatlogDTO> chatlogs, int index, ReceiveQualityChatlogDTO targetCsChatlog){
		
		if(index == 0){
			return true;
		}
		ReceiveQualityChatlogDTO preBuyerChatlog = null;
		for (int i = index-1; i >= 0; i--) {
			if(	chatlogs.get(i).getDirection() == 1){//买家说话
				preBuyerChatlog = chatlogs.get(i);
				break;
			}
		}
		if(preBuyerChatlog == null){
			return true;
		}
		long csChatTime = targetCsChatlog.getChatTime().getTime();
		if(preBuyerChatlog.getChatTime().getTime() != csChatTime || targetCsChatlog.getType()==10){
			return true; 
		}
		return false;
	}*/

	
	
	
}
