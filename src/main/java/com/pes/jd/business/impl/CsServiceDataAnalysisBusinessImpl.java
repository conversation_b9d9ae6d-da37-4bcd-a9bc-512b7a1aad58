package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.CsServiceDataAnalysisBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Enum.RequestUrlEnum;
import com.pes.jd.model.Param.CsReceiveUnSendEvalParam;
import com.pes.jd.model.Param.CsServiceEvalParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.JacksonUtils;
import com.yiyitech.support.rpc.RestOperator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 客服服务分析
 * 微服务入口
 */
@Service
public class CsServiceDataAnalysisBusinessImpl implements CsServiceDataAnalysisBusiness {

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;

	@Autowired
	private PopRtSubRestTemplate popRtSubRestTemplate;

	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;

    @Override
    public ApiResponse searchChatpeerLst(ShopQuery shop, String startDate, String endDate, List<UserQuery> csLst, String buyerNick,
                                        String keyWord) throws Exception {
    	ShopCommonParam shopParam = new ShopCommonParam(shop.getShopId(),shop.getSchemaId(),shop.getDbName()); 
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopParam)
                .put("csLst", csLst)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("buyerNick", buyerNick)
                .put("keyWord", keyWord)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        ApiResponse apiResponse = popSubRestTemplate.postRest(serviceId, "/data/analysis/searchChatpeerLst", body);
        return apiResponse;
    }

	@Override
	public ApiResponse selectCsWarn(ShopQuery selectedShop, String startDateStr, String endDateStr, List<CsDTO> csLst, String csNickListStr, String customer, Byte warningType, String keyword) throws DBNotExistException {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", selectedShop)
				.put("startDate", startDateStr)
				.put("endDate", endDateStr)
				.put("csNickListStr", csNickListStr)
				.put("customer", customer)
				.put("type", warningType)
				.put("csLst", csLst)
				.put("keyword", keyword)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(selectedShop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/service/selectCsWarn", body);
	}

	@Override
	public ApiResponse selectCsWarnFromRtDb(ShopQuery selectedShop, String startDateStr, String endDateStr, List<CsDTO> csLst, String csNickListStr, String customer, Byte warningType, String keyword) throws DBNotExistException {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", selectedShop)
				.put("startDate", startDateStr)
				.put("endDate", endDateStr)
				.put("csNickListStr", csNickListStr)
				.put("customer", customer)
				.put("type", warningType)
				.put("csLst", csLst)
				.put("keyword", keyword)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(selectedShop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/service/selectCsWarnFromRtDb", body);
	}

	@Override
    public ApiResponse searchChatlogLst(ShopQuery shop, String startDate, String endDate, UserQuery userQuery, ShopSystemsettingDTO shopSystemsetting, String buyerNick, String sid, Boolean dbFlag, boolean isPlugin, Integer direction, boolean realtimeSlowResp, Boolean warnAnalyze, String keyword) throws Exception {
        String serviceId;
        ApiResponse apiResponse;
    	ShopCommonParam shopParam = new ShopCommonParam(shop.getShopId(),dbFlag?shop.getSchemaId():shop.getRtSchemaId(),dbFlag?shop.getDbName():shop.getRtDbName());
    	//-----------------接待质量分析>客服违规量分析/顾客敏感量分析  start
        if (null != warnAnalyze && warnAnalyze.equals(Boolean.TRUE)) {
            serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("shop", shopParam)
                    .put("userQuery", userQuery)
                    .put("buyerNick", buyerNick)
                    .put("sid", sid)
                    .put("startDate", startDate)
                    .put("endDate", endDate)
                    .put("keyWordLst", keyword)
                    .put("direction", direction)
                    .put("isSlowResp", realtimeSlowResp)
                    .toRequestEntity();
            return popSubRestTemplate.postRest(serviceId, "/data/analysis/chatlogLstForWarning", body);
        }
        //-----------------接待质量分析>客服违规量分析/顾客敏感量分析    end


        HttpEntity<Object> body = RequestEntityBuilder.builder()
    			.put("shop", shopParam)
    			.put("userQuery", userQuery)
    			.put("buyerNick", buyerNick)
    			.put("sid", sid)
    			.put("startDate", startDate)
    			.put("endDate", endDate)
    			.put("shopSystemsetting", shopSystemsetting)
                .put("isSlowResp", realtimeSlowResp)
    			.toRequestEntity();
    	if(dbFlag){
        	serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        	apiResponse = isPlugin?popSubRestTemplate.postRest(serviceId, "/data/analysis/chatlogLstForPlugin", body):
					popSubRestTemplate.postRest(serviceId, "/data/analysis/chatlogLst", body);

    	}else{
        	serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			if (isPlugin) {
				apiResponse = popRtSubRestTemplate.postRest(serviceId, "/pes/cs/chatlogLstForPlugin", body);
			} else {
				List<String> keywordLst = new ArrayList<>();
				//获取敏感词
				String msServiceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
				HttpEntity<Object> tbody = RequestEntityBuilder.builder()
						.put("shopId", shopParam.getShopId())
						.toRequestEntity();
				ApiResponse masterResponse = usermgrRestTemplate.postRest(msServiceId, "/shop/getKeyWord", tbody);
				if (masterResponse.getSuccess()) {
					List<String> keyWord = JacksonUtils.objTolist(masterResponse.getData().get("keyWord"), String.class);
					if(CollectionUtils.isNotEmpty(keyWord)){
						keywordLst.addAll(keyWord);
					}
				}
				body = RequestEntityBuilder.builder()
						.put("shop", shopParam)
						.put("userQuery", userQuery)
						.put("buyerNick", buyerNick)
						.put("sid", sid)
						.put("startDate", startDate)
						.put("endDate", endDate)
						.put("keyWordLst", keywordLst)
						.put("direction", direction)
						.put("shopSystemsetting", shopSystemsetting)
						.put("realtimeSlowResp", realtimeSlowResp)
						.toRequestEntity();
				//查询具体的聊天记录
				apiResponse = popRtSubRestTemplate.postRest(serviceId, "/pes/cs/chatlogLst", body);
			}

    	}
    	return apiResponse;
    }
    

    @Override
    public ApiResponse selectChatSessionLst(ShopQuery shop, String startDate, String endDate, String csNickListStr, String customer, String type) throws Exception {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shop)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("csNickListStr", csNickListStr)
                .put("customer", customer)
                .put("type", type)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/service/selectChatSessionLst", body);
    }


	@Override
	public ApiResponse selectCsServiceEval(ShopQuery shop, CsServiceEvalParam param,SortPageQuery sortPageQuery) throws DBNotExistException {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam).put("param", param).put("sortPageQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/service/selectCsServiceEvaluate", body);
	}


	@Override
	public ApiResponse selectCsReceiveUnSendEval(ShopQuery shop, CsReceiveUnSendEvalParam param,SortPageQuery sortPageQuery) throws DBNotExistException {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam).put("param", param).put("sortPageQuery", sortPageQuery)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/service/selectCsReceiveUnSendEval", body);
	}

    @Override
    public ApiResponse selectLeaveMessage(ShopQuery shop, String startDateStr, String endDateStr, String csNickListStr, String customer, Integer selectType) throws Exception {

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shop)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("csNickListStr", csNickListStr)
                .put("customer", customer)
                .put("selectType", selectType)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, RequestUrlEnum.DATA_ANALYSIS_SELECTLEAVEMESSAGE.getName(), body);
    }

	@Override
	public ApiResponse searchChatlogLstForConver(ShopQuery shop, String startDate, String endDate,
												 List<UserQuery> userQuery, String buyerNick, String sid, Boolean dbFlag, boolean isPlugin) throws Exception {
		ShopCommonParam shopParam = new ShopCommonParam(shop.getShopId(),dbFlag?shop.getSchemaId():shop.getRtSchemaId(),dbFlag?shop.getDbName():shop.getRtDbName()); 
    	HttpEntity<Object> body = RequestEntityBuilder.builder()
    			.put("shop", shopParam)
    			.put("userQuery", userQuery)
    			.put("buyerNick", buyerNick)
    			.put("sid", sid)
    			.put("startDate", startDate)
    			.put("endDate", endDate)
    			.toRequestEntity();
    	String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
    	ApiResponse	apiResponse = popSubRestTemplate.postRest(serviceId, "/data/analysis/searchChatlogLstForConver", body);
    	return apiResponse;
	}

	@Override
	public ApiResponse searchChatlogLstForConverForRt(ShopQuery shop, String startDate, String endDate,
			List<UserQuery> userQuery, String buyerNick, String sid, Boolean dbFlag, boolean isPlugin) throws Exception {
		ShopCommonParam shopParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
    	HttpEntity<Object> body = RequestEntityBuilder.builder()
    			.put("shop", shopParam)
    			.put("userQuery", userQuery)
    			.put("buyerNick", buyerNick)
    			.put("sid", sid)
    			.put("startDate", startDate)
    			.put("endDate", endDate)
    			.toRequestEntity();
    	String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
    	ApiResponse	apiResponse = popRtSubRestTemplate.postRest(serviceId, "/data/analysis/searchChatlogLstForConver", body);
    	return apiResponse;
	}

}
