package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.SmsManagerBusiness;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class SmsManagerBusinessImpl implements SmsManagerBusiness {

	private static final Logger logger = LoggerFactory.getLogger(SmsManagerBusinessImpl.class);
	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;

	@Autowired
	private PopRtSubRestTemplate popRtSubRestTemplate;

	@Override
	public ApiResponse queryRechargeRecord(String startDate, String endDate, String nick, String orderId, Integer payWay, Integer orderStatus){
		
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("startDate", startDate)
				.put("endDate", endDate)
				.put("nick", nick)
				.put("orderId", orderId)
				.put("payWay", payWay)
				.put("orderStatus", orderStatus)
				.toRequestEntity();
		
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/queryRechargeRecord", body);
		return apiResponse;
	}

	@Override
	public ApiResponse recharge(String nick, Long shopId, Integer number, Double orderFee) {

		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("nick", nick)
				.put("shopId", shopId)
				.put("number", number)
				.put("orderFee", orderFee)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/recharge", body);
		return apiResponse;
	}

	@Override
	public ApiResponse queryShopSmsCount(Long shopId)  {

		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/queryShopSmsCount", body);
		return apiResponse;
	}

	@Override
	public ApiResponse querySmsBalanceCount() {

		ApiResponse apiResponse = null;

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/querySmsBalanceCount", null);
		return apiResponse;
	}

	@Override
	public ApiResponse selectSmsWordByNick(String nick)  {
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("nick", nick)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/selectSmsWordByNick", body);
		return apiResponse;
	}

	@Override
	public ApiResponse queryNotAiditSmsWordCount()  {
		ApiResponse apiResponse = null;

		String serviceId = RestOperator.getRemoteServiceId( ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/queryNotAiditSmsWordCount", null);
		return apiResponse;
	}

	@Override
	public ApiResponse auditSmsWord(Long id, String auditRemark, Integer auditStutas) {
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("id", id)
				.put("auditRemark", auditRemark)
				.put("auditStutas", auditStutas)
				.toRequestEntity();

		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/sms/auditSmsWord", body);
		return apiResponse;
	}

	@Override
	public ApiResponse searchMsgSendInfoByShopIdAndTimeAndBuyNickAndStatus(ShopQuery shop, String startDate, String endDate, String buyerNick, Integer status) throws Exception {
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", shop)
				.put("startDate", startDate)
				.put("endDate", endDate)
				.put("buyerNick", buyerNick)
				.put("status", status)
				.toRequestEntity();

		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/sms/searchMsgSendInfoByShopIdAndTimeAndBuyNickAndStatus", body);
		return apiResponse;
	}

	@Override
	public ApiResponse queryShopSmsCountAndPrice(Long shopId) {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/sms/queryShopSmsCountAndPrice", body);
	}

}
