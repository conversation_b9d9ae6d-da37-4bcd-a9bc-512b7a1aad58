package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.OrderGoodsEvaluateHandleBusiness;
import com.pes.jd.dao.CsChatSessionDao;
import com.pes.jd.dao.CsSendEvalDao;
import com.pes.jd.dao.CsServiceEvaluationDao;
import com.pes.jd.dao.CsServiceEvaluationDetailDao;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: aiJun
 * @Date: 2019-05-05 15:22
 * @Version 1.0
 */
@Service
public class OrderGoodsEvaluateHandleBusinessImpl implements OrderGoodsEvaluateHandleBusiness {
    private static Logger logger = LoggerFactory.getLogger(OrderGoodsEvaluateHandleBusinessImpl.class);

    @Resource
    private CsChatSessionDao csChatSessionDao;

    @Resource
    private CsServiceEvaluationDetailDao csServiceEvaluationDetailDao;

    @Resource
    private CsSendEvalDao csSendEvalDao;

    @Resource
    private CsServiceEvaluationDao csServiceEvaluationDao;
    @Override
    public void handleCalInviteEvaluation(JobShopQuery jobShop, JobDateQuery jobDate, String schemaId, boolean isDelData) {
            long s = System.currentTimeMillis();
            Date startDate = DateFormatUtils.getDateByPeriod(jobDate.getDate(), -6);
            Date endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.getDateByPeriod(jobDate.getDate(), -0));
            ArrayList<Date> dates = DateUtil.splitDate(startDate, endDate);
            for (Date date : dates) {
                try {
                    calInviteEvaluationAndbBtchCsEvals(jobShop, jobDate, date, schemaId,isDelData);
                } catch (Exception e) {
                    logger.error("【{}】batch update handle cs evaluate error", jobShop.getShop().getTitle(), e);
                    throw e;
                }
            }
            long e = System.currentTimeMillis();
            if(logger.isDebugEnabled()){

                logger.debug("计算邀评 calInviteEvaluation,time:{}",(e-s));
            }

    }

    private void calInviteEvaluationAndbBtchCsEvals(JobShopQuery jobShop, JobDateQuery jobDate, Date date, String schemaId, boolean isDelData) {
        //计算邀评率
        JobShopDTO shop = jobShop.getShop();

        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_ALL,CommonConstants.CS_STATUS_NOT_LOCK);
        List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);

        if(targetCsLst.isEmpty()){
//            logger.warn("店铺客服为空");
            return ;
        }

        Long shopId = shop.getShopId();
        Date startDate = DateUtil.getStartTimeOfDate(date);
        Date endDate = DateUtil.getEndTimeOfDate(date);
        // 获取所有chatSession
        List<CsChatSessionDTO> csCSLst = csChatSessionDao.selectReceiveCsChatSessionLstByDate(shop, targetCsLst, date, startDate, endDate);
        List<String> needCalSidLst=new ArrayList<>();
        // 邀评量/评价量基于接待量计算
        // 邀评量
        List<CsSendEvalDTO> queryCsSendEvalLst = new ArrayList<>();
        // 评价量
        List<CsServiceEvaluationDetailDTO> queryEvalDetailLstNew = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(csCSLst)) {
            //满意率 = (非常满意数+满意数)/评价量，不含转出量。
            Map<String, List<CsChatSessionDTO>> sidMap = csCSLst.stream().collect(Collectors.groupingBy(CsChatSessionDTO::getSid));
            needCalSidLst = new ArrayList<>(sidMap.keySet());
            queryCsSendEvalLst = csSendEvalDao.selectCsSendEvalByDate(shop, needCalSidLst, startDate, endDate);
            queryEvalDetailLstNew = csServiceEvaluationDetailDao.searchByDateAndShopId(shopId, startDate, endDate, schemaId, needCalSidLst);
        }

        Map<String, List<CsSendEvalDTO>> csSendEvalMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(queryCsSendEvalLst) && CollectionUtils.isNotEmpty(needCalSidLst)) {
            csSendEvalMap = queryCsSendEvalLst.stream().collect(Collectors.groupingBy(CsSendEvalDTO::getCsNick));
        }

        Map<String, List<CsServiceEvaluationDetailDTO>> csEvalDetailMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(queryEvalDetailLstNew) && CollectionUtils.isNotEmpty(needCalSidLst)) {
            csEvalDetailMap = queryEvalDetailLstNew.stream().collect(Collectors.groupingBy(CsServiceEvaluationDetailDTO::getCsNick));
        }

        List<CsServiceEvaluationDTO> csEvalLst = Lists.newArrayList();
        for (CsDTO cs : targetCsLst) {
            CsServiceEvaluationDTO csEval = new CsServiceEvaluationDTO(shopId,cs.getNick(),date);
            csEval.init();
            int evalSendNum = 0;//邀评量
            if(csSendEvalMap.containsKey(cs.getNick())){
                List<CsSendEvalDTO> csSendEvalLst = csSendEvalMap.get(cs.getNick());
                evalSendNum = csSendEvalLst.size();
            }
            csEval.setEvalSendNum(evalSendNum);
            if(csEvalDetailMap.containsKey(cs.getNick())){
                int verySatisfiedNum = 0;
                int satisfiedNum = 0;
                int generalNum = 0;
                int dissatisfiedNum = 0;
                int veryDissatisfiedNum = 0;
                int evalReplyNum = 0;//评价量
                for (CsServiceEvaluationDetailDTO csEvalDetail : csEvalDetailMap.get(cs.getNick())) {
                    evalReplyNum++;
                    if (csEvalDetail.getEvalCode() == CommonConstants.CS_EVALUATION_VERY_DISSATISFIED) {
                        veryDissatisfiedNum++;
                    }
                    if (csEvalDetail.getEvalCode() == CommonConstants.CS_EVALUATION_DISSATISFIED) {
                        dissatisfiedNum++;
                    }
                    if (csEvalDetail.getEvalCode() == CommonConstants.CS_EVALUATION_GENERAL) {
                        generalNum++;
                    }
                    if (csEvalDetail.getEvalCode() == CommonConstants.CS_EVALUATION_SATISFIED) {
                        satisfiedNum++;
                    }
                    if (csEvalDetail.getEvalCode() == CommonConstants.CS_EVALUATION_VERY_SATISFIED) {
                        verySatisfiedNum++;
                    }
                }
                csEval.setEvalReplyNum(evalReplyNum);
                csEval.setVerySatisfiedNum(verySatisfiedNum);
                csEval.setSatisfiedNum(satisfiedNum);
                csEval.setGeneralNum(generalNum);
                csEval.setDissatisfiedNum(dissatisfiedNum);
                csEval.setVeryDissatisfiedNum(veryDissatisfiedNum);
            }
            csEvalLst.add(csEval);
        }

        // 判断是否需要删除之前的记录
        if (isDelData) {
            //删除历史每日客服评价统计
            int deleteCsEvalNum = csServiceEvaluationDao.deleteCsEvalsByShopIdByDate(shop, date);
//            logger.debug("deleteCsEvalNum:{}", deleteCsEvalNum);
        }

        int addCsEvalNum = csServiceEvaluationDao.batchCsEvals(shop, date, csEvalLst);
//        logger.debug("addCsEvalNum:{}", addCsEvalNum);

    }
}
