package com.pes.jd.business.impl;

import com.pes.jd.business.DatabaseBusiness;
import com.pes.jd.dao.DatabaseDao;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobShopQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DatabaseBusinessImpl implements DatabaseBusiness {
	
	@Resource
	private DatabaseDao databaseDao;

	@Override
	public int initCreateShopAllTable(JobShopQuery jobShop) {
		JobShopDTO shop = jobShop.getShop();
		return databaseDao.initCreateShopAllTable(shop);
	}

	@Override
	public int createTable(JobShopQuery jobShop, String originTableName) {
		return databaseDao.createTable(jobShop.getShop(), originTableName);
	}
}
