package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.FunctionWhiteBusiness;
import com.pes.jd.model.DTO.ShopWipFunctionModuleDTO;
import com.pes.jd.model.VO.FunctionModuleWhiteVO;
import com.pes.jd.model.VO.ShopVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: ms-pes-jd  web
 * @description:
 * @author: ALan
 * @create: 2019-05-21 10:38
 */
@Service
public class FunctionWhiteBusinessImpl implements FunctionWhiteBusiness {

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Override
    public RestApiResponse2<List<FunctionModuleWhiteVO>> searchFunctionWhiteLstByShopId(String shopId) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId",shopId)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/searchFunctionWhiteLstByShopId", body, new ParameterizedTypeReference<RestApiResponse2<List<FunctionModuleWhiteVO>>>() {
        });

    }

    @Override
    public RestApiResponse2<List<FunctionModuleWhiteVO>> searchFunctionWhiteForLst() {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("type", 0)  // pop 店铺
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/searchFunctionWhiteForLst", body, new ParameterizedTypeReference<RestApiResponse2<List<FunctionModuleWhiteVO>>>() {
        });
    }

    @Override
    public RestApiResponse2<List<FunctionModuleWhiteVO>> searchFunctionWhiteAddShopForLst() {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("type",0)  // pop 店铺
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/searchFunctionWhiteAddShopForLst", body, new ParameterizedTypeReference<RestApiResponse2<List<FunctionModuleWhiteVO>>>() {
        });
    }

    @Override
    public RestApiResponse2<List<ShopWipFunctionModuleDTO>> batchAllWipFunction() {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/batchAllWipFunction", body, new ParameterizedTypeReference<RestApiResponse2<List<ShopWipFunctionModuleDTO>>>() {
        });
    }

    @Override
    public RestApiResponse2<List<ShopVO>> selectAllNotWhiteShopForLst() {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("type", 0)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/selectAllNotWhiteShopForLst", body, new ParameterizedTypeReference<RestApiResponse2<List<ShopVO>>>() {
        });
    }

    @Override
    public RestApiResponse2<String> insertFunctionModuleWhiteForLst(String arrShopId, String functionId) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("arrShopId",arrShopId)
                .put("functionIds",functionId)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/insertFunctionModuleWhiteForLst", body, new ParameterizedTypeReference<RestApiResponse2<String>>() {
        });
    }

    @Override
    public RestApiResponse2<String> batchDeleteFunctionModuleWhite(String fmwIds) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("fmwIds",fmwIds)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        return usermgrRestTemplate.postRestOfResult2(serviceId, "/sys/functionWhite/batchDeleteFunctionModuleWhite", body, new ParameterizedTypeReference<RestApiResponse2<String>>() {
        });
    }
}
