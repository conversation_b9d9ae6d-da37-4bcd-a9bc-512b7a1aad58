package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopAccountBussiness;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;

import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: aiJun
 * @Date: 2019-09-19 23:56
 * @Version 1.0
 */
@Service
@Slf4j
public class ShopAccountBussinessImpl implements ShopAccountBussiness {

    @Resource
    private UsermgrRestTemplate usermgrRestTemplate;

    @Resource
    private RedisOperator<String,Object> redisOperator;

    @Override
    public void getUserRolePutRedis(ShopUserDTO currentUser, String tokenKey) {
        ApiResponse apiResponse;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("userNick", currentUser.getNick())
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/user/selectUserRole", body);
        if (apiResponse != null && apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
            String role = (String) apiResponse.getData().get("role");
            Map<String, Object> sessionMap = new HashMap<>();
            sessionMap.put("role", role);
            redisOperator.putAll(tokenKey, sessionMap);
            redisOperator.expire(tokenKey, 12 * 60, TimeUnit.MINUTES);
        }
    }

}
