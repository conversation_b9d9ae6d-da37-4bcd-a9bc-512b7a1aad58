package com.pes.jd.business.impl;

import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.ShopAccountBussiness;
import com.pes.jd.dao.ShopAccountDao;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
@Service
public class ShopAccountBussinessImpl implements ShopAccountBussiness{

	@Resource
	private ShopAccountDao shopAccountDao;
	
	@Override
	public List<ShopAccountDTO> selectShopAccountByShopId(Long shopId) {
		return shopAccountDao.selectShopAccountByShopId(shopId);
	}

	@Override
	public int insertShopAccountOfShop(List<ShopAccountDTO> subUsers, Long shopId) {
		List<ShopAccountDTO> shopAccountLocal = selectShopAccountByShopId(shopId);
		if (shopAccountLocal != null) {
			for (ShopAccountDTO subUser : shopAccountLocal) {
				if (CommonConstants.SUB_USER_TYPE_MANAGE.equals(subUser.getRole())) {
					for (ShopAccountDTO subUserNew : subUsers) {
						if (subUser.getNick().equals(subUserNew.getNick())) {
							subUserNew.setRole(CommonConstants.SUB_USER_TYPE_MANAGE);
							break;
						}
					}
				}
			}
		}
		deleteShopAccountOfShop(shopId+ "");
		return shopAccountDao.insertShopAccountOfShop(subUsers);
	}

	@Override
	public int deleteShopAccountOfShop(String shopId) {
		return shopAccountDao.deleteShopAccountOfShop(shopId);
	}

}
