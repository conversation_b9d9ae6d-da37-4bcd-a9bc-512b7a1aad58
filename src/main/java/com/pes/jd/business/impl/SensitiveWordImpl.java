package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.SensitiveWordBusiness;
import com.pes.jd.data.api.ApiCheckOperator;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.reponse.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: aiJun
 * @Date: 2019-07-23 10:33
 * @Version 1.0
 */
@Service
public class SensitiveWordImpl implements SensitiveWordBusiness {

    @Autowired
    private ApiCheckOperator apiCheckOperator;

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Override
    public Map<?, ?> getSensitiveWordLst(JobShopDTO jobShopDTO) {
        Map keyWordMap = new HashMap<>();
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", jobShopDTO.getShopId())
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId,"/sensitiveWord/getWordLst", body);
        if (apiResponse != null && apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
            keyWordMap = apiResponse.getData();
        }
        return keyWordMap;
    }

    @Override
    public boolean isRiskSensitiveWordCheck(String content) throws Exception {
        /* 紧急停掉咚咚敏感词校验接口 */
        //return apiCheckOperator.isRiskSensitiveWordCheck("seller",content);
        return false;
    }
}
