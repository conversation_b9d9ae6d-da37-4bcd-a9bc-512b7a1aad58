package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.business.ShopGoodsGroupBussiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.business.ShopSysSettingBusiness;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GroupParam;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.CsNickLstForRealTimeVO;
import com.pes.jd.model.VO.GoodsGroupSkuVO;
import com.pes.jd.model.VO.GoodsGroupVO;
import com.pes.jd.model.VO.MultiShopGroupVO;
import com.pes.jd.ms.domain.Data.master.DeptShop;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;


@Service
public class ShopSysManagerBusinessImpl implements ShopSysManagerBusiness {

	private static final Logger logger=LoggerFactory.getLogger(ShopSysManagerBusinessImpl.class);
	@Autowired
	private ShopSysSettingBusiness shopSysSettingBusiness;

	@Autowired
	private ShopGoodsGroupBussiness shopGoodsGroupBussiness;
	/**
	 * 客服信息查询
	 */
	@Override
	public List<CsDTO> selectShopCs(MasterServiceShopQuery shop,String operateType,String type) throws Exception {
		String groupId=shop.getGroupId();
		String csNick=shop.getCsNick();
		List<CsDTO> csNickLst= Lists.newArrayList();
		ApiResponse apiResponse = shopSysSettingBusiness.selectShopCs(shop.getSelectedShop(), groupId, csNick,
				operateType,type);
		if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())
				&& apiResponse.getData().get("csLst") != null) {
			@SuppressWarnings("unchecked")
			List<CsDTO> nickLst = (List<CsDTO>) apiResponse.getData().get("csLst");
			if (CollectionUtils.isNotEmpty(nickLst)) {
				csNickLst.addAll(nickLst);
			}

		} else {
			logger.error("selectShopCs error:{}", apiResponse.getRpMsg());
		}
		return csNickLst;

	}

	/**
	 *
	 *设置单个客服
	 */
	@Override
	public ApiResponse updateShopCustomerServices(MasterServiceShopQuery shop,String nickArrStr,String groupId,Integer type) throws Exception {

		return 	shopSysSettingBusiness.updateShopCustomerServices(shop.getSelectedShop(), nickArrStr, groupId,type);
	}

	/**
	 * 查询店铺子账号来设置客服
	 *
	 */
	@Override
	public ApiResponse selectShopSubUserForCsSetting(MasterServiceShopQuery shop, String nick) throws Exception {

		return shopSysSettingBusiness.selectShopSubUserForCsSetting(shop.getSelectedShop(), nick);
	}

	/**
	 * 批量客服管理
	 *
	 * @param operateType
	 *            1:批量添加 2批量锁定
	 *
	 */
	@Override
	public ApiResponse batchUpdateShopCs(MasterServiceShopQuery shop, String nickArrStr, String groupId, Integer type,
			Integer operateType) throws Exception {

		return shopSysSettingBusiness.batchUpdateShopCs(shop.getSelectedShop(), nickArrStr, groupId, type, operateType);
	}

	/**
	 * 编辑客服信息
	 */
	@Override
	public ApiResponse updateCs(MasterServiceShopQuery shop, String csNick, String simpleNick, Integer type, String groupId)
			throws Exception {

		return shopSysSettingBusiness.updateCs(shop.getSelectedShop(), csNick, simpleNick, type, groupId);
	}

	/**
	 * 锁定或解锁客服
	 */
	@Override
	public ApiResponse lockCs(MasterServiceShopQuery shop, String csNickJson, Integer operateType) throws Exception {
		return 	shopSysSettingBusiness.lockCs(shop.getSelectedShop(),csNickJson,operateType);

	}

	/**
	 * @Description:（刷新子账号）
	 *
	 */
	@Override
	public ApiResponse refreshShopSubUsers(MasterServiceShopQuery shop) throws Exception {

		return shopSysSettingBusiness.refreshShopSubUsers(shop.getCurrentShop());
	}

	/**
	 * 获取店铺下的客服信息
	 *
	 * @param type
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<CsDTO> selectShopCswwSimpleNames(ShopQuery shop, Integer type) throws Exception{
		List<CsDTO> csMultiLst=Lists.newArrayList();
		List<CsDTO> csLst=null;
//		List<ShopQuery> memberShopLst = selectShopGroupMemberShops(shop);

//		修改多店铺组 自营客服相同问题  目前功能只能查询单个店铺  pop同步修改
		List<ShopQuery> memberShopLst = new ArrayList<ShopQuery>();
		memberShopLst.add(shop);

		Map<String, List<ShopQuery>> shopMap = memberShopLst.stream()
				.collect(Collectors.groupingBy(ShopQuery::getDbName));
		for (Entry<String, List<ShopQuery>> entry : shopMap.entrySet()) {

		ApiResponse apiResponse=	shopSysSettingBusiness.selectShopCswwSimpleNames(entry.getKey(),entry.getValue(), type);
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			csLst=JacksonUtils.objTolist(apiResponse.getData().get("csLst"),CsDTO.class);
			if(CollectionUtils.isNotEmpty(csLst)){
				for (CsDTO cs : csLst) {
					cs.setNick(cs.getNick().toLowerCase());
				}
				csMultiLst.addAll(csLst);
			}
		}else{
			logger.error("selectShopCswwSimpleNames error :{}",apiResponse.getRpMsg());
		}
		}
		return csMultiLst;
	}


	/**
	 * 客服组查询
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<GroupDTO> queryShopCsGroups(MasterServiceShopQuery shop,String groupName) throws Exception{

		List<GroupDTO> multiGroupLst=Lists.newArrayList();
		List<GroupDTO> groupLst=null;
		if(shop.isSelectedShop()){
			ApiResponse apiResponse=	shopSysSettingBusiness.queryShopCsGroups(shop.getSelectedShop(), groupName);
			if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
				groupLst=(List<GroupDTO>) apiResponse.getData().get("groups");
				if(CollectionUtils.isNotEmpty(groupLst)){
					multiGroupLst.addAll(groupLst);
				}

			}else{
				logger.error("query group lst error:{}",apiResponse.getRpMsg());
			}
		}else{
			List<ShopQuery> memberShopLst = selectShopGroupMemberShops(shop.getMainShop());
			Map<String, List<ShopQuery>> shopMap = memberShopLst.stream()
					.collect(Collectors.groupingBy(ShopQuery::getDbName));
			for (Entry<String, List<ShopQuery>> entry : shopMap.entrySet()) {
			ApiResponse apiResponse=shopSysSettingBusiness.queryShopCsGroups(entry.getKey(),entry.getValue(), groupName);
				if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
					groupLst=(List<GroupDTO>) apiResponse.getData().get("groups");
					if(CollectionUtils.isNotEmpty(groupLst)){
						multiGroupLst.addAll(groupLst);
					}
				}else{
					logger.error("query group lst error:{}",apiResponse.getRpMsg());
				}
			}
		}
		return multiGroupLst;

	}

	/**
	 * 创建/修改组
	 */
	@Override
	public ApiResponse createOrUpdateShopCsGroup(ShopQuery shop, Long groupId, String groupName) throws Exception{
		return shopSysSettingBusiness.createOrUpdateShopCsGroup(shop, groupId, groupName);
	}

	/**
	 * 删除客服组
	 */
	@Override
	public ApiResponse deleteCsGroup(ShopQuery shop, Long groupId) throws Exception{
		return shopSysSettingBusiness.deleteCsGroup(shop, groupId);
	}

	/**
	 *
	 * 获取多店铺组
	 */
	@Override
	public List<ShopQuery> selectShopGroupMemberShops(ShopQuery mainShop) throws Exception {
		List<ShopQuery> shops = Lists.newArrayList();
		shops.add(mainShop);
		Set<Long> shopIdSet = Sets.newHashSet();
		shopIdSet.add(mainShop.getShopId());
		ShopQuery shopQuery = null;
		ApiResponse apiResponse = shopSysSettingBusiness.selectShopGroupMemberShops(mainShop);
		if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode()) && apiResponse.getData() != null) {
			if (apiResponse.getData().get("multiShopLst") != null) {
				Map<String, Object> map = apiResponse.getData();
				Object obj = map.get("multiShopLst");
				List<ShopDTO> memberShops = JSON.parseArray(JSON.toJSONString(obj), ShopDTO.class);
				if (CollectionUtils.isNotEmpty(memberShops)) {
					for (ShopDTO shop : memberShops) {
						if (shopIdSet.add(shop.getShopId())) {
							shopQuery = new ShopQuery();
							shopQuery.setDbName(shop.getDb());
							shopQuery.setSchemaId(shop.getSchemaId());
							shopQuery.setShopId(shop.getShopId());
							shopQuery.setRtSchemaId(shop.getRtSchemaId());
							shopQuery.setRtDbName(shop.getRtDb());
							shopQuery.setTitle(shop.getTitle());
							shopQuery.setLastGetDateTime(shop.getPreviousGetDataTime());
							shopQuery.setSubuserNum(shop.getSubuserNum());
							shopQuery.setSessionKey(shop.getSessionKey());
							shopQuery.setColType(shop.getColType());
							shopQuery.setOptionSessionKey(shop.getOptionSessionKey());
							shops.add(shopQuery);
						}
					}
				}
			}
		} else {
			logger.error("get multi shops error:{}", apiResponse.getRpMsg());
			return new ArrayList<ShopQuery>(0);
		}
		return shops;
	}

	/**
	 * 根据shopId,groupId,csNick查询客服 用于master查询
	 */
	@Override
	public List<String> selectCsNickByShopIdByGroupIdByCsNick(MasterServiceShopQuery shop ,String groupId,String csNick) throws Exception {
		List<UserQuery> userShopLst= selectCsNickLst(shop.isSelectedShop(), shop.getSelectedShop(), shop.getMainShop(), groupId, csNick);
		Set<String> userSets=Sets.newHashSet();
		for (UserQuery shopQuery : userShopLst) {
			userSets.add(shopQuery.getNick());
		}
		return new ArrayList<>(userSets);
	}

	@Override
	public List<UserQuery> selectCsNickByShopIdByGroupIdByCsNickForQuery(MasterServiceShopQuery shop ,String groupId,String csNick) throws Exception {
		List<UserQuery> userShopLst= selectCsNickLst(shop.isSelectedShop(), shop.getSelectedShop(), shop.getMainShop(), groupId, csNick);
		return userShopLst;
	}
	/**
	 * 根据shopId,groupId,csNick查询客服 用于sub查询
	 */
	@Override
	public List<String> selectCsNickByShopIdByGroupIdByCsNickForSub(UserShopQuery shop ,String groupId,String csNick) throws Exception {
		List<UserQuery> userShopLst= selectCsNickLst(shop.isSelectedShop(), shop.getSelectedShop(), shop.getMainShop(), groupId, csNick);
		Set<String> userSets=Sets.newHashSet();
		for (UserQuery shopQuery : userShopLst) {
			userSets.add(shopQuery.getNick());
		}
		return new ArrayList<>(userSets);
	}
	@Override
	public List<UserQuery> selectCsNickByShopIdByGroupIdByCsNickForSubQuery(UserShopQuery shop ,String groupId,String csNick) throws Exception {
		List<UserQuery> userShopLst= selectCsNickLst(shop.isSelectedShop(), shop.getSelectedShop(), shop.getMainShop(), groupId, csNick);
		/*List<UserQuery> userLst = Lists.newArrayList();
		Set<String> nickSet = new HashSet<>();
		for(UserQuery user : userShopLst){
			if(nickSet.add(user.getGroupId().toString())){
				userLst.add(user);
			}
		}*/
		return userShopLst;
	}

	@Override
	public List<UserQuery> selectCsNickByShopIdByGroupIdByCsNickForSubQueryNew(UserShopQuery shop ,String groupId,String csNick) throws Exception {
		List<UserQuery> userShopLst= selectCsNickLstNew(shop.isSelectedShop(), shop.getSelectedShop(), shop.getMainShop(), groupId, csNick);
		/*List<UserQuery> userLst = Lists.newArrayList();
		Set<String> nickSet = new HashSet<>();
		for(UserQuery user : userShopLst){
			if(nickSet.add(user.getGroupId().toString())){
				userLst.add(user);
			}
		}*/
		return userShopLst;
	}

	@SuppressWarnings("unchecked")
	private List<UserQuery> selectCsNickLst(Boolean isSelectShop,ShopQuery selectShop,ShopQuery mainShop,String groupId,String csNick) throws Exception{
		List<UserQuery> csMultilst = Lists.newArrayList();
		List<UserQuery> nick = null;
		if (isSelectShop) {
			ApiResponse apiResponse = shopSysSettingBusiness.selectCsNickByShopIdByGroupIdByCsNick(selectShop, StringUtils.isBlank(groupId)?"":groupId,
					StringUtils.isBlank(csNick)?"":csNick);
			if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				Object obj = apiResponse.getData().get("csNickLst");
				if (obj != null) {
					nick = JacksonUtils.objTolist(obj, UserQuery.class);
					csMultilst.addAll(nick);
				}

			} else {
				logger.error("get csNickLst error:{}", apiResponse.getRpMsg());
			}
		}
		return csMultilst;
	}

	@SuppressWarnings("unchecked")
	private List<UserQuery> selectCsNickLstNew(Boolean isSelectShop,ShopQuery selectShop,ShopQuery mainShop,String groupId,String csNick) throws Exception{
		List<UserQuery> csMultilst = Lists.newArrayList();
		List<UserQuery> nick = null;
		if (isSelectShop) {
			ApiResponse apiResponse = shopSysSettingBusiness.selectCsNickByShopIdByGroupIdByCsNickNew(selectShop, StringUtils.isBlank(groupId)?"":groupId,
					StringUtils.isBlank(csNick)?"":csNick);
			if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				Object obj = apiResponse.getData().get("csNickLst");
				if (obj != null) {
					nick = JacksonUtils.objTolist(obj, UserQuery.class);
					csMultilst.addAll(nick);
				}

			} else {
				logger.error("get csNickLst error:{}", apiResponse.getRpMsg());
			}
		}
		return csMultilst;
	}

	/**
	 *获取店铺，组，客服下拉框集合
	 */
	@Override
	public List<MultiShopGroupVO> selectMultiShopGroups(ShopQuery shop) throws Exception {
		List<MultiShopGroupVO> multiShopLst = Lists.newArrayList();
		List<ShopQuery> memberShopLst = selectShopGroupMemberShops(shop);
		Map<String, List<ShopQuery>> shopMap = memberShopLst.stream()
				.collect(Collectors.groupingBy(ShopQuery::getDbName));
		for (Entry<String, List<ShopQuery>> entry : shopMap.entrySet()) {
			ApiResponse apiResponse = shopSysSettingBusiness.selectMultiShopGroups(entry.getKey(), entry.getValue());
			if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				List<MultiShopGroupVO> multiShopVo = JSON.parseArray(
						JSON.toJSONString(apiResponse.getData().get("multiShopInfo")), MultiShopGroupVO.class);
				for (MultiShopGroupVO multiShopGroupVO : multiShopVo) {
					List<GoodsGroupVO> goodsGroupLst = multiShopGroupVO.getGoodsGroupLst();

                    // 拿到全部的
					Map<Long, ShopGoodsSkuDTO> goodsGroupSkuUrlMap = null;
					for (GoodsGroupVO goodsGroupVO : goodsGroupLst) {
						if(goodsGroupVO==null){
							continue;
						}
						List<GoodsGroupSkuVO> groupSkuLst=	goodsGroupVO.getShopGoodsSkuLst();
						if (CollectionUtils.isEmpty(groupSkuLst)) {
							continue;
						}
						// 全部赋值sku'商品的url赋值
						if (StringUtils.isEmpty(goodsGroupVO.getGoodsGroupId())) {
							List<ShopGoodsSkuDTO> goodsSkuLst = selectGoodsSkuLstByShopLstBySkuIdLst(entry.getKey(),
									entry.getValue(), groupSkuLst);
//							goodsGroupSkuUrlMap = goodsSkuLst.stream()
//									.collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, crov -> crov));
							goodsGroupSkuUrlMap=goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, (p) -> p,(k,v)->v));
							for (GoodsGroupSkuVO goods : groupSkuLst) {
								ShopGoodsSkuDTO model = goodsGroupSkuUrlMap.get(goods.getSkuId());
								if(model!=null){
									goods.setSkuName(model.getSkuName());
									goods.setImageUrl(model.getImageUrl());
								}
							}
						}
						// 每个组里的sku'商品的url赋值
						if (goodsGroupSkuUrlMap != null) {
							for (GoodsGroupSkuVO goods : groupSkuLst) {
								ShopGoodsSkuDTO model = goodsGroupSkuUrlMap.get(goods.getSkuId());
								if(model!=null){
									goods.setSkuName(model.getSkuName());
									goods.setImageUrl(model.getImageUrl());
								}
							}
						}
					}
				}
				if (CollectionUtils.isNotEmpty(multiShopVo)) {
					multiShopLst.addAll(multiShopVo);
				}
			} else {
				logger.error("get multi shopgroup lst error:{}", apiResponse.getRpMsg());
			}
		}
		return multiShopLst;
	}

	/**
	 *获取店铺，组，客服下拉框集合
	 */
	@Override
	public ApiResponse selectMultiShopGroupByShopId(MasterServiceShopQuery shop) throws Exception {

		return shopSysSettingBusiness.selectMultiShopGroupByShopId(shop.getSelectedShop());
	}
	@Override
	public ApiResponse selectGroupCsByGroupParam(MasterServiceShopQuery shop,GroupParam param)throws Exception{
		return shopSysSettingBusiness.selectGroupCsByGroupParam(shop.getSelectedShop(), param);
	}
	private List<ShopGoodsSkuDTO> selectGoodsSkuLstByShopLstBySkuIdLst(String dbName,List<ShopQuery> shopLst,List<GoodsGroupSkuVO> groupSkuLst) throws Exception{
		ApiResponse apiResponse=shopGoodsGroupBussiness.selectGoodsSkuLstBySkuIdLst(dbName, shopLst,groupSkuLst);
		List<ShopGoodsSkuDTO> goodsGroupSkuLst;

		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			goodsGroupSkuLst = JSON.parseArray(JSON.toJSONString(apiResponse.getData().get("selectedGoodsSkuLst")),ShopGoodsSkuDTO.class);
		}else{
			goodsGroupSkuLst= new ArrayList<>(0);
		}
		return goodsGroupSkuLst;
	}
	@Override
	public ApiResponse updateIsShowMultipleShop(ShopQuery mainShop, ShopUserDTO currentUser) throws Exception {
		return shopSysSettingBusiness.updateIsShowMultipleShop(mainShop,currentUser);
	}

	@Override
	public ApiResponse selectShopGroupsOfOwner(ShopQuery mainShop) throws Exception {
		return shopSysSettingBusiness.selectShopGroupsOfOwner(mainShop);
	}

	@Override
	public ApiResponse createNewShopGroup(ShopQuery mainShop, String shopGroupName) throws Exception {
		return shopSysSettingBusiness.createNewShopGroup(mainShop,shopGroupName);
	}

	@Override
	public ApiResponse updateShopGroupName(ShopQuery mainShop, String shopGroupId, String shopGroupName) throws Exception {
		return shopSysSettingBusiness.updateShopGroupName(mainShop,shopGroupId,shopGroupName);
	}

	@Override
	public ApiResponse updateShopGroupMutualWatch(String shopGroupId, Boolean mutualWatch) throws Exception {
		return shopSysSettingBusiness.updateShopGroupMutualWatch(shopGroupId,mutualWatch);
	}

	@Override
	public ApiResponse deleteShopGroup(ShopQuery mainShop, String shopGroupId) throws Exception {
		return shopSysSettingBusiness.deleteShopGroup(mainShop,shopGroupId);
	}

	@Override
	public ApiResponse sendInviteRequestForShopGroup(ShopQuery mainShop, String inviteShopInfo, String shopGroupId) throws Exception {
		return shopSysSettingBusiness.sendInviteRequestForShopGroup(mainShop,inviteShopInfo,shopGroupId);
	}

	@Override
	public ApiResponse moveShopToShopGroup(ShopQuery mainShop, String moveShopIds, String shopGroupId) throws Exception {
		return shopSysSettingBusiness.moveShopToShopGroup(mainShop,moveShopIds,shopGroupId);
	}

	@Override
	public ApiResponse shopGroupSystemSettingSynchronization(ShopQuery selectShop, String toShopIds, String syncType) throws Exception {
		return shopSysSettingBusiness.shopGroupSystemSettingSynchronization(selectShop,toShopIds,syncType);
	}

	@Override
	public ApiResponse selectJoinedMyGroupShopList(ShopQuery mainShop, String shopGroupId) throws Exception {
		return shopSysSettingBusiness.selectJoinedMyGroupShopList(mainShop,shopGroupId);
	}

	@Override
	public ApiResponse selectJoinGroupList(ShopQuery mainShop) throws Exception {
		return shopSysSettingBusiness.selectJoinGroupList(mainShop);
	}

	@Override
	public ApiResponse selectShopAccredit(ShopQuery mainShop) throws Exception {
		return shopSysSettingBusiness.selectShopAccredit(mainShop);
	}

	@Override
	public ApiResponse updateAuthorizeShopStatus(ShopQuery mainShop, Integer status, String requestId)
			throws Exception {
		return shopSysSettingBusiness.updateAuthorizeShopStatus(mainShop,status,requestId);
	}

	@Override
	public ApiResponse updateAdminSubUserType(String nick,String type) throws Exception {
		return shopSysSettingBusiness.updateAdminSubUserType(nick,type);
	}

	@Override
	public ApiResponse searchBaseTreeInfo(ShopQuery shop, String nick, Long userId) throws Exception {
		return shopSysSettingBusiness.searchBaseTreeInfo(shop,nick,userId);
	}

	@Override
	public ApiResponse searchShopAccountInfo(ShopQuery shop, String nick, Integer flag, Long userId) throws Exception {
		return shopSysSettingBusiness.searchShopAccountInfo(shop,nick,flag,userId);
	}

	@Override
	public ApiResponse insertOrUpdatePermission(ShopQuery shop, Integer type, String body) throws Exception {
		return shopSysSettingBusiness.insertOrUpdatePermission(shop, type, body);
	}

	@Override
	public ApiResponse settingVisitCode(String visitCode, String visitEmail, Integer flag, ShopQuery shop) throws Exception {
		return shopSysSettingBusiness.settingVisitCode(visitCode, visitEmail, flag, shop);
	}

	@Override
	public ApiResponse validVisitCode(String visitCode, ShopQuery shop) throws Exception {
		return shopSysSettingBusiness.validVisitCode(visitCode, shop);
	}

	@Override
	public ApiResponse findVisitCode(ShopQuery shop) throws Exception {
		return shopSysSettingBusiness.findVisitCode(shop);
	}

	@Override
	public ApiResponse findEmail(ShopQuery shop) throws Exception {
		return shopSysSettingBusiness.findEmail(shop);
	}

	@Override
	public ApiResponse selectLoginLog(ShopQuery shop, Date startDate, Date endDate, String nick) throws Exception {
		return shopSysSettingBusiness.selectLoginLog(shop, startDate, endDate, nick);
	}

	@Override
	public ApiResponse selectLoginLogDetails(ShopQuery shop, Date startDate, Date endDate, String nick) throws Exception {
		return shopSysSettingBusiness.selectLoginLogDetails(shop, startDate, endDate, nick);
	}

	@Override
	public ApiResponse selectOperationLog(ShopQuery shop, Date startDate, Date endDate, String nick,String optType) throws Exception {
		return shopSysSettingBusiness.selectOperationLog(shop, startDate, endDate, nick, optType);
	}

	@Override
	public ApiResponse insertOperationLog(ShopUserDTO shopdto, ShopQuery shop, String optContent, String optType) throws Exception {
		return shopSysSettingBusiness.insertOperationLog(shopdto, shop, optContent, optType);
	}
	@Override
	public ApiResponse updatePerformanceSettings(ShopQuery selectedShop, ShopSystemsettingDTO shopSystemsettingDTO, String requestUrl)
			throws Exception {
		return shopSysSettingBusiness.updatePerformanceSettings(selectedShop, shopSystemsettingDTO, requestUrl);
	}

	@Override
	public ApiResponse addFliterBuynick(ShopQuery selectedShop, String nick,String currentUser) throws Exception {
		return shopSysSettingBusiness.addFliterBuynick(selectedShop, nick,currentUser);
	}

	@Override
	public ApiResponse deleteFliterBuynick(ShopQuery selectedShop, String nick) throws Exception {
		return shopSysSettingBusiness.deleteFliterBuynick(selectedShop, nick);
	}

	@Override
	public ApiResponse getSystemSettingForQN(ShopQuery selectedShop) throws Exception {
		return shopSysSettingBusiness.getSystemSettingForQN(selectedShop);
	}
	@Override
	public ApiResponse queryIsModifyEnquiryAndOutStockValidTime(ShopQuery selectedShop) throws Exception {
		return shopSysSettingBusiness.queryIsModifyEnquiryAndOutStockValidTime(selectedShop);
	}

	@Override
	public ApiResponse selectCategoryLst(ShopQuery selectedShop) throws Exception {
		return shopSysSettingBusiness.selectCategoryLst(selectedShop);
	}

	@Override
	public ApiResponse selectCategoryLstV2(ShopQuery selectedShop) throws Exception {
		return shopSysSettingBusiness.selectCategoryLstV2(selectedShop);
	}

	@Override
	public ApiResponse selectGoodsinfoLst(ShopQuery selectedShop, String categoryId, String name, Integer status)
			throws Exception {
		return shopSysSettingBusiness.selectGoodsinfoLst(selectedShop, categoryId, name, status);
	}

	@Override
	public ApiResponse addGoodsFilter(ShopQuery selectedShop, String goodList) throws Exception {
		return shopSysSettingBusiness.addGoodsFilter(selectedShop, goodList);
	}

	@Override
	public ApiResponse deleteGoodsFilter(ShopQuery selectedShop, String numIid) throws Exception {
		return shopSysSettingBusiness.deleteGoodsFilter(selectedShop, numIid);

	}

	@Override
	public ApiResponse selectUserByShopId(ShopQuery shop) throws Exception {
		return shopSysSettingBusiness.selectUserByShopId(shop);
	}

	@Override
	public ApiResponse selectShopAccountForShop(ShopQuery shop, String includeMainAccount) throws Exception {
		return shopSysSettingBusiness.selectShopAccountForShop(shop,includeMainAccount);
	}

	@Override
	public ShopQuery getShopInfo(Long shopId) throws Exception{
		ShopDTO shopDTO = shopSysSettingBusiness.getShopInfo(shopId);
		ShopQuery shop=null;
		if(shopDTO != null){
			shop=new ShopQuery();
			shop.setShopId(shopDTO.getShopId());
			shop.setDbName(shopDTO.getDb());
			shop.setSchemaId(shopDTO.getSchemaId());
			shop.setRtSchemaId(shopDTO.getRtSchemaId());
			shop.setRtDbName(shopDTO.getRtDb());
			shop.setSessionKey(shopDTO.getSessionKey());
			shop.setColType(shopDTO.getColType());
			shop.setTitle(shopDTO.getTitle());
			shop.setLastGetDateTime(shopDTO.getPreviousGetDataTime());
			shop.setOptionSessionKey(shopDTO.getOptionSessionKey());
			shop.setVenderId(shopDTO.getVenderId());
		}
		return shop;
	}

	@Override
	public RealtimeShopDTO getRealTimeShopByShopId(Long shopId) throws Exception{
		RealtimeShopDTO shop=null;
		ApiResponse apiResponse=shopSysSettingBusiness.getRealTimeShopByShopId(shopId);
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			Object obj=	apiResponse.getData().get("shop");
			if(obj!=null){
				shop=JSON.parseObject(JSON.toJSONString(obj), RealtimeShopDTO.class);
			}
		}else{
			logger.error("getRealTimeShopByShopId error msg:"+apiResponse.getRpMsg());
		}
		return shop;
	}


	/**
	 * 返回实时绩效分时绩效正常的csNickLst
	 */
	@Override
	public List<CsNickLstForRealTimeVO> selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(UserShopQuery shop ,String groupId,String csNick) throws Exception {
		List<UserQuery> userShopLst= selectCsNickLst(shop.isSelectedShop(), shop.getSelectedShop(), shop.getMainShop(), groupId, csNick);
		Set<String> userSets=Sets.newHashSet();
		List<CsNickLstForRealTimeVO> csNickLst = Lists.newArrayList();
		for (UserQuery shopQuery : userShopLst) {
			if(shopQuery.getCsStatus()==1){
				CsNickLstForRealTimeVO csNickVO = new CsNickLstForRealTimeVO();
				csNickVO.setCsNick(shopQuery.getNick());
				csNickVO.setType(shopQuery.getType());
				if(userSets.add(csNickVO.getCsNick())){
					csNickLst.add(csNickVO);
				}
			}
		}
		return csNickLst;
	}

	@Override
	public ApiResponse selectCsByShopIdByTypeForPlugin(Long shopId,Integer type) throws Exception{
		return shopSysSettingBusiness.selectCsByShopIdByTypeForPlugin(shopId, type);
	}
	@Override
	public ApiResponse getShopInfoByShopNameForPlugin(String shopName) throws Exception{
		return shopSysSettingBusiness.getShopInfoByShopNameForPlugin(shopName);
	}

	@Override
    public List<DeptShop> selectDeptShopInfoByDeptId(String shopName,Long deptId, String id, String type) throws Exception {
		RestApiResponse2<List<DeptShop>> resp=    shopSysSettingBusiness.selectDeptShopInfoByDeptId(shopName,deptId,id,type);
		List<DeptShop> deptShopLst=null;
		if(resp.getSuccess()){
			deptShopLst=resp.getData().get("result");
		}
		return deptShopLst;
	}

	@Override
	public String selectGroupIdByShopId(String shopId) {
		return shopSysSettingBusiness.selectGroupIdByShopId(shopId);
	}

	@Override
	public ApiResponse selectGroupCsByGroupParamForPlugin(MasterServiceShopQuery shop,GroupParam param)throws Exception{
		return shopSysSettingBusiness.selectGroupCsByGroupParamForPlugin(shop.getSelectedShop(), param);
	}
	@Override
	public RestApiResponse2<Object> batchUpdateCsofGroup(Long shopId, String groupId, String csNicks) throws Exception {
		return shopSysSettingBusiness.batchUpdateCsofGroup(shopId,groupId,csNicks);
	}

	public List<DeptShop> selectDeptShopInfoByDeptId(String shopName, Long deptId) throws Exception {
		RestApiResponse2<List<DeptShop>> resp=    shopSysSettingBusiness.selectDeptShopInfoByDeptId(shopName,deptId);
		List<DeptShop> deptShopLst=null;
		if(resp.getSuccess()){
			deptShopLst=resp.getData().get("result");
		}
		return deptShopLst;
	}



}
