package com.pes.jd.business.impl;

import com.pes.jd.business.CsGoodsSaleDetailBusiness;
import com.pes.jd.business.CsGoodsSaleIndexBusiness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GoodsSaleBusinessImpl {

	@Autowired
	private CsGoodsSaleIndexBusiness csGoodsSaleIndexBusiness;
	
	private Logger logger = LoggerFactory.getLogger(ReceiveDataAnalysisBusinessImpl.class);

	
	@Autowired
	private CsGoodsSaleDetailBusiness csGoodsSaleDetailBusiness;
	
//	@SuppressWarnings("unchecked")
//	@Override
//	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexList(UserShopQuery shop, Date startDate, Date endDate,
//			String csNickListStr, Long categoryId) throws DBNotExistException {
//		List<CsGoodsSaleIndexDTO> csGoodsSaleIndexList = null;
//		ApiResponse apiResponse = csGoodsSaleIndexBusiness.selectCsGoodsSaleIndexList(shop.getSelectedShop(), startDate, endDate, csNickListStr, categoryId);
//		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
//			csGoodsSaleIndexList = (List<CsGoodsSaleIndexDTO>) apiResponse.getData().get("csGoodsSaleIndexList");
//		}else {
//			logger.error("query group lst error:{}",apiResponse.getRpMsg());
//		}
//		
//		return csGoodsSaleIndexList;
//	}
//
//	@SuppressWarnings("unchecked")
//	@Override
//	public List<CsGoodsSaleIndexDetailDTO> selectCsGoodsSaleIndexDetailList(UserShopQuery shop, Date startDate,
//			Date endDate, String csNickListStr, Long categoryId,Long id) throws DBNotExistException {
//		List<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailList =null;
//		ApiResponse apiResponse = csGoodsSaleDetailBusiness.selectCsGoodsSaleIndexDetailList(shop.getSelectedShop(), startDate, endDate, csNickListStr, categoryId,id);
//		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
//			csGoodsSaleIndexDetailList = (List<CsGoodsSaleIndexDetailDTO>) apiResponse.getData().get("csGoodsSaleIndexDetailList");
//		}else {
//			logger.error("query group lst error:{}",apiResponse.getRpMsg());
//		}
//		return csGoodsSaleIndexDetailList;
//	}

}
