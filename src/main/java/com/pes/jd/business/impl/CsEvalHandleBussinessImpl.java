package com.pes.jd.business.impl;

import com.pes.jd.business.CsEvalHandleBussiness;
import com.pes.jd.data.converter.CsEvalDataConverter;
import com.pes.jd.data.converter.CsSendEvalDataConverter;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;

@Service
public class CsEvalHandleBussinessImpl implements CsEvalHandleBussiness{
	private Logger logger = LoggerFactory.getLogger(CsEvalHandleBussinessImpl.class);
	
	@Resource
	private CsEvalDataConverter csEvalDataConverter;
	@Resource
	private CsSendEvalDataConverter csSendEvalDataConverter;
	
	/**
	 * TODO 拉取客服邀评详情
	 */
	@Override
	public void pullShopCsSendEval(JobShopQuery shop, JobDateQuery jobDate, boolean isDelData)
			throws Exception {
		long s = System.currentTimeMillis();
		//每日job：邀评获取--拉取三天 4号算3号数据，邀评要拉取1-3三天的数据
		ArrayList<Date> dates = DateUtil.splitDate(DateUtil.getDateByPeriod(jobDate.getDate(), -2), jobDate.getDate());
		try {
			for (Date date : dates) {
				//csSendEvalDataConverter.pullCsSendEvals(shop, date, isDelData);
			}
		} catch (Exception e) {
			logger.error("【{}】handle cs send evaluate error", shop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("邀评拉取耗时 	handle cs send evaluate end,time:{} ms", (e - s));
		}
	}
	
	/**  
	 * TODO 
	 * 拉取客服评价详情
	 */
	@Override
	public void pullShopCsEvalDetails(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		
		try {
			csEvalDataConverter.pullCsEvalDetails(jobShop, jobDate, jobDate.getDate(), isDelData);
		} catch (Exception e) {
			logger.error("【{}】handle cs evaluate error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("客服评价详情拉取		handle cs evaluate end,time:{}",(e-s));
		}

	}

	/**  
	 * TODO 
	 * 批量更新7天前的客服评价信息
	 */
	@Override
	public void pullShopUpdateCsEvalDetails(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();


		Date startDate = DateFormatUtils.getDateByPeriod(jobDate.getDate(), -7);
		Date endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.getDateByPeriod(jobDate.getDate(), -1));

		ArrayList<Date> dates = DateUtil.splitDate(startDate, endDate);


		for (Date date : dates) {
			long ss=System.currentTimeMillis();
			try {
				csEvalDataConverter.updateCsEvalDetails(jobShop, jobDate, date, isDelData);
			} catch (Exception e) {
				logger.error("【{}】batch update handle cs evaluate error", jobShop.getShop().getTitle(), e);
				throw e;
			}
			long ee=System.currentTimeMillis();
			if(logger.isDebugEnabled()){

				logger.debug("{}的，客服评价信息获取耗时：{}ms",DateFormatUtils.formatYMd(date),(ee-ss));
			}
		}


		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("客服评价更新7天前的客服评价信息拉取	batch update handle cs evaluate end,time:{}",(e-s));
		}
	}

}
