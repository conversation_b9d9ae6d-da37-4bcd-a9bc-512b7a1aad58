package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.Constants.PesConstants;
import com.pes.jd.business.GoodsSaleHandBussiness;
import com.pes.jd.dao.*;
import com.pes.jd.model.DO.CsGoodsSaleIndexDO;
import com.pes.jd.model.DO.CsGoodsSaleIndexDetailDO;
import com.pes.jd.model.DO.SlientGoodsSaleIndexDO;
import com.pes.jd.model.DO.SlientGoodsSaleIndexDetailDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service
public class GoodsSaleHandBussinessImpl implements GoodsSaleHandBussiness {
    private static final Logger logger = LoggerFactory.getLogger(GoodsSaleHandBussinessImpl.class);
    /**
     * 客服商品销售明细
     */
    @Resource
    private CsOrderBindDao csOrderBindDao;
    @Resource
    private OrderDetailDao orderDetailDao;
    @Resource
    private CsGoodsSaleIndexDao csGoodsSaleIndexDao;
    @Resource
    private CsGoodsSaleIndexDetailDao csGoodsSaleIndexDetailDao;
    @Resource
    private SlientGoodsSaleIndexDao slientGoodsSaleIndexDao;
    @Resource
    private SlientGoodsSaleIndexDetailDao slientGoodsSaleIndexDetailDao;
    @Resource
    private OrderDao orderDao;
    @Resource
    private PresaleOrderDao presaleOrderDao;
    @Resource
    private ShopGoodsSkuDao shopGoodsSkuDao;

    /**
     * @Description:（处理客服商品/静默商品销售明细）
     */
    @Override
    public void handCsAndSlienGoodsSaleDetail(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();
        Date date = jobDate.getDate();
        Date startDate = jobDate.getStartDate();
        Date endDate = jobDate.getEndDate();
        List<CsDTO> csLst = jobShop.getCsLst().stream()
                .filter(e -> e.getType() == 1)
                .collect(Collectors.toList());
        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        if (CollectionUtils.isEmpty(csLst)) {
            if (logger.isDebugEnabled()) {
                logger.debug("csLst is empty");
            }
            return;
        }
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Integer enquiryValidDurationTime = sys.getEnquiryValidDurationTime();
        // TODO(询单-付款)
        //Date enquiryDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(startDate, 0 - enquiryValidDurationTime-1));
        if (isDelData) {
            // 删除
            int deleteDetailNum = csGoodsSaleIndexDetailDao.deleteCsGoodsSaleIndexDetailByShopIdByDate(shop, startDate, endDate);
            if (logger.isDebugEnabled()) {
                logger.debug("delete CsGoodsSaleIndexDetail num：{}", deleteDetailNum);
            }
            int deleteSlientNum = slientGoodsSaleIndexDetailDao.deleteSlientGoodsSaleIndexDetailByShopIdByDate(shop, startDate, endDate);
            if (logger.isDebugEnabled()) {
                logger.debug("delete SlientGoodsSaleIndexDetail num：{}", deleteSlientNum);
            }
        }
        //查询父订单
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置查询数据的有效时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单的有效时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime() + 1 - ValidDateRangeQuery.validPresaleOrderBalancePayDays));
//        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, 0 - sys.getEnquiryValidDurationTime() + 1 - validDateRange.validOrderPayDays));
        validDateRange.setAdjustEnquiryEndDate(endDate);
        validDateRange.setAdjustStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime() + 1 - ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustEndDate(endDate);
        List<Long> parentOrders = new ArrayList<>();
        //父订单
        List<OrderDTO> parentOrderLst = orderDao.selectParentOrderToPayCsSaleOrderLstByShop(shop, validDateRange);
        if (CollUtil.isNotEmpty(parentOrderLst)) {
            parentOrders.addAll(parentOrderLst.stream().map(OrderDTO::getDirectTradeId).collect(Collectors.toList()));
        }
        long s1 = System.currentTimeMillis();
        List<OrderDetailDTO> orderGoodsSkuLst = Lists.newArrayList();
        //查询某段时间内付款的订单(在线支付->普通订单和预售) 付款维度8号
        if (logger.isDebugEnabled()) {
            logger.debug("---------------------select onLine order start-----------------------");
        }
        Date preDates = DateUtils.getDateByPeriod(startDate, -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE - enquiryValidDurationTime - 1);
        List<OrderDTO> onLinePayAndPreOrderLst = orderDao.selectShopOrderByShopIdByPayTime(shop, preDates, startDate, endDate);
        //过滤出暂停订单
        Map<Long, Double> pauseOrderAndPayment = onLinePayAndPreOrderLst.stream()
                .filter(t -> "ZanTing".equals(t.getStatus()) || "PAUSE".equals(t.getStatus()) || "TRADE_CANCELED".equals(t.getStatus()))
                .filter(t -> t.getPayment() > 0)
                .filter(t -> t.getSellerDiscount() == 0)
                .filter(t -> t.getTotalFee() == 0)
                .collect(Collectors.toMap(OrderDTO::getOrderId, OrderDTO::getPayment));
        long e1 = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("get selectShopOrderByShopIdByPayTime order time :{}ms size:{}", e1 - s1, onLinePayAndPreOrderLst.size());
        }
        if (CollectionUtils.isNotEmpty(onLinePayAndPreOrderLst)) {
            onLinePayAndPreOrderLst = onLinePayAndPreOrderLst.stream().filter(ele -> !parentOrders.contains(ele.getOrderId())).collect(Collectors.toList());
            Map<Long, OrderDTO> onLinePayAndPreOrderMap = onLinePayAndPreOrderLst.stream().collect(Collectors.toMap(OrderDTO::getOrderId, c -> c, (c1, c2) -> c2));
            Set<Long> orderIdSet = onLinePayAndPreOrderLst.stream().map(OrderDTO::getOrderId).collect(Collectors.toSet());
            //查询某段时间内付款的订单详情(在线支付)
            long s5 = System.currentTimeMillis();
            List<OrderDetailDTO> onLinePayAndPreOrderGoodsSkuLst = orderDetailDao.selectOrderGoodsSkuByShopIdLstAndDate(shop, preDates, orderIdSet, startDate, endDate);
            long e5 = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {
                logger.debug("get orderDetail selectOrderGoodsSkuByShopIdLstAndDate time:{}ms", e5 - s5);
            }
            if (CollectionUtils.isNotEmpty(onLinePayAndPreOrderGoodsSkuLst)) {
                onLinePayAndPreOrderGoodsSkuLst = onLinePayAndPreOrderGoodsSkuLst.stream().filter(ele -> !parentOrders.contains(ele.getOrderId())).collect(Collectors.toList());
                for (OrderDetailDTO orderDetail : onLinePayAndPreOrderGoodsSkuLst) {
                    OrderDTO order = onLinePayAndPreOrderMap.get(orderDetail.getOrderId());

                    if (order != null) {
                        //这个字段已废弃，暂时不用
                        orderDetail.setOutStockTime(order.getOutStockTime());
                    }
                }
                orderGoodsSkuLst.addAll(onLinePayAndPreOrderGoodsSkuLst);
            }
        }
        //(货到付款->普通订单)下单维度8号
        long s2 = System.currentTimeMillis();
        List<OrderDTO> cashOrderLst = orderDao.selectShopOrderByShopIdByCreated(shop, startDate, endDate, 1);
        long e2 = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("get selectShopOrderByShopIdByCreated order time:{}ms", e2 - s2);
        }
        if (CollectionUtils.isNotEmpty(cashOrderLst)) {
            Map<Long, OrderDTO> cashOrderMap = cashOrderLst.stream().collect(Collectors.toMap(OrderDTO::getOrderId, c -> c, (oldValue, newValue) -> newValue));
            Set<Long> cashOrderIdSet = cashOrderLst.stream().map(OrderDTO::getOrderId).collect(Collectors.toSet());
            //查询某段时间内付款的订单详情(货到付款)
            List<OrderDetailDTO> cashOrderPayGoodsSkuLst = orderDetailDao.selectOrderGoodsSkuByShopIdLstAndCreateDate(shop, cashOrderIdSet, startDate, endDate);
            if (CollectionUtils.isNotEmpty(cashOrderPayGoodsSkuLst)) {
                for (OrderDetailDTO orderDetail : cashOrderPayGoodsSkuLst) {
                    OrderDTO order = cashOrderMap.get(orderDetail.getOrderId());
                    if (order != null) {
                        orderDetail.setOutStockTime(order.getOutStockTime());
                    }
                }
                orderGoodsSkuLst.addAll(cashOrderPayGoodsSkuLst);
            }
        }
        //1.查出今天付尾款款的预售订单
        ValidDateRangeQuery tValidDateRange = new ValidDateRangeQuery();
        tValidDateRange.setAdjustStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime() + 1 - ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        tValidDateRange.setAdjustEndDate(endDate);
        tValidDateRange.setStartDate(startDate);
        tValidDateRange.setEndDate(endDate);
        List<PresaleOrderDTO> payBalanceOrderIdLst = presaleOrderDao.selectOrderIdByShopIdAndPayBalanceTime(shop, tValidDateRange);
        //付定金未付尾款的数据
        List<PresaleOrderDTO> payBalanceOrderIdLst2 = presaleOrderDao.selectOrderIdByShopIdAndPayBalanceTime2(shop, tValidDateRange);
        Map<Long, PresaleOrderDTO> presaleOdMap = payBalanceOrderIdLst2.stream().collect(Collectors.toMap(PresaleOrderDTO::getOrderId, t -> t, (o, n) -> n));
        Set<Long> presaleOdIdsFilter = presaleOdMap.keySet();
        // 静默的订单
        List<OrderDetailDTO> slientdOrderLst = Lists.newArrayList();
        Set<Long> csPesOrderIdSet = Sets.newHashSet();
        // ----------------客服商品销售明细job开始--------------------------------------
        List<CsGoodsSaleIndexDetailDO> csGoodsSaleIndexDetailLst = Lists.newArrayList();
        for (CsDTO cs : csLst) {
            String csNick = cs.getNick();
            // 绩效客服绑定的订单(在询单有效期内,付款时间为当前查询时间段)
            List<CsOrderBindDTO> csOrderBindLst = Lists.newArrayList();
            //客服绩效订单
            //货到付款 l
            long s3 = System.currentTimeMillis();
            List<CsOrderBindDTO> cashOrderBindLst = csOrderBindDao.selectShopCsOrderBindOrderIdLstForCashPesCs(shop, csNick, startDate, endDate, CommonConstants.YES_PES_ORDER);
            if (logger.isDebugEnabled()) {
                long e3 = System.currentTimeMillis();
                logger.debug("csNick:{} selectShopCsOrderBindOrderIdLstForCashPesCs time:{}ms", csNick, e3 - s3);
            }
            if (CollectionUtils.isNotEmpty(cashOrderBindLst)) {
                csOrderBindLst.addAll(cashOrderBindLst);
            }
            //预售(包含在线订单的推迟日期)
            long s4 = System.currentTimeMillis();
            Date preDate = DateUtils.getDateByPeriod(startDate, -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE - enquiryValidDurationTime - 1);
            List<CsOrderBindDTO> preOrderBindLst = csOrderBindDao.selectShopCsOrderBindOrderIdLstByValidPayTimeForPesCs(shop, csNick, preDate, startDate, endDate, CommonConstants.YES_PES_ORDER);
            if (logger.isDebugEnabled()) {
                long e4 = System.currentTimeMillis();
                if (logger.isDebugEnabled()) {
                    logger.debug("csNick:{} selectShopCsOrderBindOrderIdLstByValidPayTimeForPesCs time:{}ms,preOrderBindLst size：{}", csNick, e4 - s4, preOrderBindLst.size());
                }
            }
            if (CollectionUtils.isNotEmpty(preOrderBindLst)) {
                csOrderBindLst.addAll(preOrderBindLst);
            }
            if (CollectionUtils.isEmpty(csOrderBindLst)) {
                continue;
            }
            Set<Long> pesOrderIdSet = csOrderBindLst.stream().map(CsOrderBindDTO::getOrderId).collect(Collectors.toSet());
            //给静默用
            csPesOrderIdSet.addAll(pesOrderIdSet);
            Map<Long, Date> buyerChatDateMap = csOrderBindLst.stream().collect(Collectors.toMap(CsOrderBindDTO::getOrderId, CsOrderBindDTO::getDate, (k1, k2) -> k1));
            // 绩效客服的订单
            List<OrderDetailDTO> csBingDingOrderLst = Lists.newArrayList();
            for (OrderDetailDTO order : orderGoodsSkuLst) {
                if (pesOrderIdSet.contains(order.getOrderId())) {
                    csBingDingOrderLst.add(order);
                }
            }
            //fix:1045 预售在当天付尾款的TRADE_CANCELED订单 从预售表取付款时间来处理
            handTradeCanceledOrder(payBalanceOrderIdLst, shop, sys, csBingDingOrderLst,csNick);

            //暂停订单处理：金额分摊
            Set<Long> pauseOrderIds = pauseOrderAndPayment.keySet();
            Map<Long, List<OrderDetailDTO>> pauseOrderGroup = csBingDingOrderLst.stream()
                    .filter(t -> pauseOrderIds.contains(t.getOrderId()))
                    .filter(t -> t.getItemPrice() > 0)
                    .filter(t -> t.getItemNum() > 0)
                    .collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            Map<String, Double> orderSkuItemPrice = new HashMap<>();
            for (Entry<Long, List<OrderDetailDTO>> entry : pauseOrderGroup.entrySet()) {
                List<OrderDetailDTO> pauseGoodsSkuLst = entry.getValue();
                //按 itemPrice和 itemNum计算金额分摊
                double totalPrice = 0;
                double payment = pauseOrderAndPayment.get(entry.getKey());
                Map<Integer, Double> indexAndItemPrice = new HashMap<>();
                for(int i =0; i < pauseGoodsSkuLst.size(); i++){
                    double itemPrice = pauseGoodsSkuLst.get(i).getItemPrice();
                    Integer itemNum = pauseGoodsSkuLst.get(i).getItemNum();
                    double itemTotalPrice = itemPrice * itemNum;
                    totalPrice += itemTotalPrice;
                    indexAndItemPrice.put(i, itemTotalPrice);
                }
                //计算分摊
                for(Integer thisIndex: indexAndItemPrice.keySet()){
                    double thisPrice = indexAndItemPrice.get(thisIndex);
                    OrderDetailDTO thisDetailDTO = pauseGoodsSkuLst.get(thisIndex);
                    double itemPrice = payment * (thisPrice / totalPrice);
//                    pauseGoodsSkuLst.get(thisIndex).setItemPrice(new BigDecimal(itemPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
                    orderSkuItemPrice.put(thisDetailDTO.getOrderId() + "-" + thisDetailDTO.getItemSkuId()
                            , new BigDecimal(itemPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
                }
            }

            CsGoodsSaleIndexDetailDO csSaleDetail;
            // 根据skuId分组
            Map<String, List<OrderDetailDTO>> orderGoodsMap = csBingDingOrderLst.stream()
                    .collect(Collectors.groupingBy(OrderDetailDTO::getItemSkuId));
            for (Entry<String, List<OrderDetailDTO>> entry : orderGoodsMap.entrySet()) {
                Long skuId = Long.valueOf(entry.getKey());
                List<OrderDetailDTO> goodsSkuLst = entry.getValue();
                for (OrderDetailDTO orderDetail : goodsSkuLst) {
                    csSaleDetail = new CsGoodsSaleIndexDetailDO();
                    csSaleDetail.setShopId(shopId);
                    csSaleDetail.setDate(date);
                    csSaleDetail.setCsNick(csNick);
                    csSaleDetail.setSkuId(skuId);
                    csSaleDetail.setCustomer(orderDetail.getBuyerNick());
                    csSaleDetail.setOrderId(orderDetail.getOrderId());
                    csSaleDetail.setOutStockTime(orderDetail.getOutStockTime());
                    if (buyerChatDateMap != null && buyerChatDateMap.get(csSaleDetail.getOrderId()) != null) {
                        csSaleDetail.setEnquiryDate(buyerChatDateMap.get(csSaleDetail.getOrderId()));
                    }
                    csSaleDetail.setOrderId(orderDetail.getOrderId());
                    double jdPrice = orderDetail.getItemPrice() == null ? 0.0 : orderDetail.getItemNum() * orderDetail.getItemPrice();
                    double sellerRate = orderDetail.getTotalFee() == null ? 0.0 : orderDetail.getTotalFee() > 0.0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
                    Double saleAmount = jdPrice - (sellerRate * (orderDetail.getSellerDiscount() == null ? 0.0 : orderDetail.getSellerDiscount()));
                    if(presaleOdIdsFilter.contains(orderDetail.getOrderId())){
                        continue;
//                        csSaleDetail.setSaleAmount(presaleOdMap.get(orderDetail.getOrderId()).getPayBargainReal());
                    } else if(pauseOrderIds.contains(orderDetail.getOrderId()) && orderDetail.getItemPrice() != 0){
                        csSaleDetail.setSaleAmount(null == orderSkuItemPrice.get(orderDetail.getOrderId() + "-" + skuId) ? 0 : orderSkuItemPrice.get(orderDetail.getOrderId() + "-" + skuId));
                    }else{
                        csSaleDetail.setSaleAmount(saleAmount);
                    }
                    csSaleDetail.setSaleGoodsNum(orderDetail.getItemNum());
                    csGoodsSaleIndexDetailLst.add(csSaleDetail);
                }

            }
        }
        //客服商品销售明细
        int detailNum = csGoodsSaleIndexDetailDao.batchInsertCsGoodsSaleIndexDetail(shop, date, csGoodsSaleIndexDetailLst);
        // ----------------客服商品销售明细job结束--------------------------------------

        //----------------静默商品销售明细job--------------------------------------
        for (OrderDetailDTO order : orderGoodsSkuLst) {
            if (!csPesOrderIdSet.contains(order.getOrderId())) {
                slientdOrderLst.add(order);
            }
        }
        //暂停订单处理：金额分摊
        Set<Long> pauseOrderIdsForSilent = pauseOrderAndPayment.keySet();
        Map<Long, List<OrderDetailDTO>> silentPauseOrderGroup = slientdOrderLst.stream()
                .filter(t -> pauseOrderIdsForSilent.contains(t.getOrderId()))
                .filter(t -> t.getItemPrice() > 0)
                .filter(t -> t.getItemNum() > 0)
                .collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
        Map<String, Double> orderSkuItemPriceForSilent = new HashMap<>();
        for (Entry<Long, List<OrderDetailDTO>> entry : silentPauseOrderGroup.entrySet()) {
            List<OrderDetailDTO> pauseGoodsSkuLst = entry.getValue();
            //按 itemPrice和 itemNum计算金额分摊
            double totalPrice = 0;
            double payment = pauseOrderAndPayment.get(entry.getKey());
            Map<Integer, Double> indexAndItemPrice = new HashMap<>();
            for(int i =0; i < pauseGoodsSkuLst.size(); i++){
                double itemPrice = pauseGoodsSkuLst.get(i).getItemPrice();
                Integer itemNum = pauseGoodsSkuLst.get(i).getItemNum();
                double itemTotalPrice = itemPrice * itemNum;
                totalPrice += itemTotalPrice;
                indexAndItemPrice.put(i, itemTotalPrice);
            }
            //计算分摊
            for(Integer thisIndex: indexAndItemPrice.keySet()){
                double thisPrice = indexAndItemPrice.get(thisIndex);
                OrderDetailDTO thisDetailDTO = pauseGoodsSkuLst.get(thisIndex);
                double itemPrice = payment * (thisPrice / totalPrice) / thisDetailDTO.getItemNum();
//                    pauseGoodsSkuLst.get(thisIndex).setItemPrice(new BigDecimal(itemPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
                orderSkuItemPriceForSilent.put(thisDetailDTO.getOrderId() + "-" + thisDetailDTO.getItemSkuId()
                        , new BigDecimal(itemPrice).setScale(2, RoundingMode.HALF_UP).doubleValue());
            }
        }

        SlientGoodsSaleIndexDetailDO slientSaleDetail;
        // 根据skuId分组
        Map<String, List<OrderDetailDTO>> slientOrderGoodsMap = slientdOrderLst.stream()
                .collect(Collectors.groupingBy(OrderDetailDTO::getItemSkuId));
        List<SlientGoodsSaleIndexDetailDO> slientGoodsSaleIndexDetailLst = Lists.newArrayList();
        for (Entry<String, List<OrderDetailDTO>> entry : slientOrderGoodsMap.entrySet()) {
            Long skuId = Long.valueOf(entry.getKey());
            List<OrderDetailDTO> slientGoodsSkuLst = entry.getValue();
            for (OrderDetailDTO slinetOrder : slientGoodsSkuLst) {
                slientSaleDetail = new SlientGoodsSaleIndexDetailDO();
                slientSaleDetail.setShopId(shopId);
                slientSaleDetail.setDate(date);
                slientSaleDetail.setSkuId(skuId);
                slientSaleDetail.setCustomer(slinetOrder.getBuyerNick());
                slientSaleDetail.setOrderId(slinetOrder.getOrderId());
                slientSaleDetail.setOutStockTime(slinetOrder.getOutStockTime());
                double jdPrice = slinetOrder.getItemPrice() == null ? 0.0 : slinetOrder.getItemNum() * slinetOrder.getItemPrice();
                double sellerRate = slinetOrder.getTotalFee() == null ? 0.0 : slinetOrder.getTotalFee() > 0.0 ? jdPrice / slinetOrder.getTotalFee() : 0.0;
                Double saleAmount = jdPrice - (sellerRate * (slinetOrder.getSellerDiscount() == null ? 0.0 : slinetOrder.getSellerDiscount()));
                slientSaleDetail.setSaleAmount(saleAmount);
                slientSaleDetail.setSaleGoodsNum(slinetOrder.getItemNum());
                if(pauseOrderIdsForSilent.contains(slientSaleDetail.getOrderId()) && slinetOrder.getItemPrice() != 0){
                    slientSaleDetail.setSaleAmount(null == orderSkuItemPriceForSilent.get(slientSaleDetail.getOrderId() + "-" + skuId) ?
                            0 : orderSkuItemPriceForSilent.get(slientSaleDetail.getOrderId() + "-" + skuId));
                }else{
                    slientSaleDetail.setSaleAmount(saleAmount);
                }
                slientGoodsSaleIndexDetailLst.add(slientSaleDetail);
            }
        }
        //静默商品销售明细
        int slientNum = slientGoodsSaleIndexDetailDao.batchInsertSlientGoodsSaleIndexDetail(shop, date, slientGoodsSaleIndexDetailLst);
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("******shopName:{} hand csGoodsSaleDetail/SlientGoodsSaleDetail end time :{} ms, insert  CsGoodsSaleIndexDetail num :{},SlientGoodsSaleIndexDetail num : {}", shop.getTitle(), (e - s), detailNum, slientNum);
        }
    }

    private void handTradeCanceledOrder(List<PresaleOrderDTO> payBalanceOrderIdLst, JobShopDTO shop, ShopSystemsettingDTO sys, List<OrderDetailDTO> csBingDingOrderLst, String csNick) {
        //.终止的状态的订单
        if (CollUtil.isNotEmpty(payBalanceOrderIdLst)) {
            //已经有明细的不需要处理了
            payBalanceOrderIdLst = payBalanceOrderIdLst.stream()
                    .filter(ele -> !csBingDingOrderLst.stream().map(OrderDetailDTO::getOrderId).collect(Collectors.toList()).contains(ele.getOrderId())).collect(Collectors.toList());
            Map<Date, List<PresaleOrderDTO>> dateOrderMap = payBalanceOrderIdLst.stream().collect(Collectors.groupingBy(ele -> DateFormatUtils.getStartTimeOfDate(ele.getCreateTime())));
            List<OrderDetailDTO> tradeCanceledOrder = new ArrayList<>();
            for (Entry<Date, List<PresaleOrderDTO>> entry : dateOrderMap.entrySet()) {
                Date createDate = entry.getKey();
                List<OrderDTO> orderLst = orderDao.selectTradeCanceledOrderByOrderIdLst(shop, createDate, entry.getValue().stream().map(PresaleOrderDTO::getOrderId).collect(Collectors.toList()));
                if (CollUtil.isNotEmpty(orderLst)) {//取消终止订单看看有没有被算绩效,算绩效了需要算sku维度数据
                    List<Long> needOrderSkuDetailOrderIds = csOrderBindDao.selectIsPesTradeCanceledOrder(shop,
                            DateUtil.getDateByPeriod(createDate, -sys.getEnquiryValidDurationTime()),
                            DateUtil.getDateByPeriod(createDate, +sys.getEnquiryValidDurationTime()),
                            orderLst.stream().map(OrderDTO::getOrderId).collect(Collectors.toList()), csNick);
                    if (CollUtil.isNotEmpty(needOrderSkuDetailOrderIds)) {
                        List<OrderDetailDTO> tradeCanceleDetail = orderDetailDao.selectOrderGoodsSkuByOrderIdLst(shop, createDate,
                                needOrderSkuDetailOrderIds);
                        if (CollUtil.isNotEmpty(tradeCanceleDetail)) {
                            tradeCanceledOrder.addAll(tradeCanceleDetail);
                        }
                    }
                }
            }
            if (CollUtil.isNotEmpty(tradeCanceledOrder)) {//给sku价格赋值
                List<ShopGoodSkuDTO> goodSkuLst = shopGoodsSkuDao.selectByShopIdAndSkuIds(shop, tradeCanceledOrder.stream().map(ele -> Long.valueOf(ele.getItemSkuId())).collect(Collectors.toSet()));
                if (CollUtil.isNotEmpty(goodSkuLst)) {
                    Map<Long, Double> skuPriceMap = goodSkuLst.stream().filter(ele -> !Objects.isNull(ele.getSkuId())).collect(Collectors.toMap(ShopGoodSkuDTO::getSkuId, ShopGoodSkuDTO::getPrice, (oldValue, newValue) -> oldValue));
                    tradeCanceledOrder.forEach(orderDetail -> {
                        if (BaseUtils.getNonNull(orderDetail.getTotalFee()) == 0.0D) {
                            orderDetail.setTotalFee(BaseUtils.getNonNull(skuPriceMap.get(Long.valueOf(orderDetail.getItemSkuId()))));
                        }
                        if (BaseUtils.getNonNull(orderDetail.getItemPrice()) == 0.0D) {
                            orderDetail.setItemPrice(BaseUtils.getNonNull(skuPriceMap.get(Long.valueOf(orderDetail.getItemSkuId()))));
                        }
                    });
                }
                csBingDingOrderLst.addAll(tradeCanceledOrder);
            }
        }
    }

    /**
     * @Description:（处理客服商品/静默商品销售汇总）
     */
    @Override
    public void handCsAndSlientGoodsSale(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();

        List<CsDTO> csLst = jobShop.getCsLst().stream()
                .filter(e -> e.getType() == 1)
                .collect(Collectors.toList());

        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        if (CollectionUtils.isEmpty(csLst)) {
            logger.info("csLst is empty");
            return;
        }
        Date date = jobDate.getDate();
        Date startDate = jobDate.getStartDate();
        Date endDate = jobDate.getEndDate();
        List<CsGoodsSaleIndexDO> csGoodsSaleIndexLst = Lists.newArrayList();
        List<SlientGoodsSaleIndexDO> slientGoodsSaleIndexLst = Lists.newArrayList();
        if (isDelData) {
            // 删除
            csGoodsSaleIndexDao.deleteCsGoodsSaleIndexByShopIdByDate(shop, startDate, endDate);
            slientGoodsSaleIndexDao.deleteSlientGoodsSaleIndexByShopIdByDate(shop, startDate, endDate);
        }
        for (CsDTO cs : csLst) {
            String csNick = cs.getNick();
            //----------------客服商品销售汇总job开始--------------------------------------
            //绩效客服销售的商品
            List<CsGoodsSaleIndexDetailDTO> csGoodsSalaeLst = csGoodsSaleIndexDetailDao.selectCsGoodsSaleIndexDetailByCsNickByDate(shop, csNick, startDate, endDate);
            if (CollectionUtils.isEmpty(csGoodsSalaeLst)) {
                continue;
            }
            CsGoodsSaleIndexDO csSaleDetail;
            //根据skuId分组
            Map<Long, List<CsGoodsSaleIndexDetailDTO>> goodsSaleMap = csGoodsSalaeLst.stream().collect(Collectors.groupingBy(CsGoodsSaleIndexDetailDTO::getSkuId));
            for (Entry<Long, List<CsGoodsSaleIndexDetailDTO>> entry : goodsSaleMap.entrySet()) {
                Long skuId = entry.getKey();
                csSaleDetail = new CsGoodsSaleIndexDO();
                csSaleDetail.setShopId(shopId);
                csSaleDetail.setDate(date);
                csSaleDetail.setCsNick(csNick);
                csSaleDetail.setSkuId(skuId);
                Integer saleGoodsNum = 0;
                Double saleAmount = 0.0;
                Set<String> buyerNickSet = Sets.newHashSet();
                List<CsGoodsSaleIndexDetailDTO> goodsSaleSkuLst = entry.getValue();
                if (CollectionUtils.isNotEmpty(goodsSaleSkuLst)) {
                    for (CsGoodsSaleIndexDetailDTO goodsSale : goodsSaleSkuLst) {
                        buyerNickSet.add(goodsSale.getCustomer());
                        saleGoodsNum += goodsSale.getSaleGoodsNum();
                        saleAmount += goodsSale.getSaleAmount();
                    }
                }
                csSaleDetail.setPurchaseBuyerNum(buyerNickSet.size());
                csSaleDetail.setSaleAmount(saleAmount);
                csSaleDetail.setSaleGoodsNum(saleGoodsNum);
                csGoodsSaleIndexLst.add(csSaleDetail);
            }
            //----------------客服商品销售汇总job结束--------------------------------------
        }
        //客服商品销售汇总
        int detailNum = csGoodsSaleIndexDao.batchInsertCsGoodsSaleIndex(shop, date, csGoodsSaleIndexLst);
        //----------------店铺维度 静默商品销售汇总job --------------------------------------
        List<SlientGoodsSaleIndexDetailDTO> slientGoodsSaleLst = slientGoodsSaleIndexDetailDao.selectSlientGoodsSaleIndexByShopIdByDate(shop, startDate, endDate);
        if (CollectionUtils.isEmpty(slientGoodsSaleLst)) {
            return;
        }
        //过滤预售付定金未付尾款
        List<Long> unbalancedIds = presaleOrderDao.selectOrderIdsUnbalance(shop, startDate, endDate);
        Set<Long> filterOrderIds = new HashSet<>(unbalancedIds);
        slientGoodsSaleLst.removeIf(t -> filterOrderIds.contains(t.getOrderId()));

        SlientGoodsSaleIndexDO slientSale;
        //根据skuId分组
        Map<Long, List<SlientGoodsSaleIndexDetailDTO>> slientOrderGoodsMap = slientGoodsSaleLst.stream().collect(Collectors.groupingBy(SlientGoodsSaleIndexDetailDTO::getSkuId));
        for (Entry<Long, List<SlientGoodsSaleIndexDetailDTO>> entry : slientOrderGoodsMap.entrySet()) {
            Long skuId = entry.getKey();
            slientSale = new SlientGoodsSaleIndexDO();
            slientSale.setShopId(shopId);
            slientSale.setDate(date);
            slientSale.setSkuId(skuId);
            List<SlientGoodsSaleIndexDetailDTO> goodsSaleSkuLst = entry.getValue();
            Set<String> slientBuyerNickSet = Sets.newHashSet();
            Integer saleGoodsNum = 0;
            Double saleAmount = 0.0;
            if (CollectionUtils.isNotEmpty(goodsSaleSkuLst)) {
                for (SlientGoodsSaleIndexDetailDTO goodsSale : goodsSaleSkuLst) {
                    slientBuyerNickSet.add(goodsSale.getCustomer());
                    saleGoodsNum += goodsSale.getSaleGoodsNum();
                    saleAmount += goodsSale.getSaleAmount();
                }
            }
            slientSale.setPurchaseBuyerNum(slientBuyerNickSet.size());
            slientSale.setSaleAmount(saleAmount);
            slientSale.setSaleGoodsNum(saleGoodsNum);
            slientGoodsSaleIndexLst.add(slientSale);

        }
        //静默商品销售汇总
        int slientNum = slientGoodsSaleIndexDao.batchInsertSlientGoodsSaleIndex(shop, date, slientGoodsSaleIndexLst);
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("******shopName:{} hand csGoodsSaleIndex/SlientGoodsSaleIndex end time :{} ms, insert CsGoodsSaleIndexDetail num： {},SlientGoodsSaleIndex num：{}", shop.getTitle(), (e - s), detailNum, slientNum);
        }
    }

}
