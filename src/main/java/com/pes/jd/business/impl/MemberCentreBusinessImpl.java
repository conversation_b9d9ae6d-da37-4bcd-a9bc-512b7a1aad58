package com.pes.jd.business.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.MemberCentreBusiness;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.constants.MsgSystemConstants;
import com.pes.jd.jwt.JwtConstants;
import com.pes.jd.model.DTO.MemberCentreDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.ms.utils.MsgServiceUtil;

import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class MemberCentreBusinessImpl implements MemberCentreBusiness {

	private static final Logger logger = LoggerFactory.getLogger(MemberCentreBusinessImpl.class);
	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;
	@Resource
	private RedisOperator<String,Object> redisOperator;

	private static final String verifRedisPrefix = "VERIF_CODE_";

	@Override
	public ApiResponse insert(Long shopId, String shopTitle, String userNick, String contact, String phoneNumber, String verifCode, Integer position, String qq, String email) {
		ApiResponse apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
		apiResponse.setSuccess(Boolean.FALSE);
		//验证验证码是否过期
		judgePhoneVerif(apiResponse, phoneNumber);

		//验证验证码
		if(!verifCode(verifCode, phoneNumber)){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1006);
			apiResponse.setSuccess(Boolean.FALSE);

			return apiResponse;
		}

		HttpEntity<Object> body = RequestEntityBuilder.builder()
									.put("shopId", shopId)
									.put("shopTitle", shopTitle)
									.put("userNick", userNick)
									.put("contact", contact)
									.put("phoneNumber", phoneNumber)
									.put("position", position)
									.put("qq", qq)
									.put("email", email)
									.toRequestEntity();

		try{
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			RestResponseTypeRef<Optional> res = usermgrRestTemplate.postRest(serviceId,"/memberCentre/insert", body, new ParameterizedTypeReference<RestResponseTypeRef<Optional>>() {});

			if(res.getSuccess()){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
				apiResponse.setSuccess(Boolean.TRUE);
			}
		} catch (Exception e) {
			logger.error("web member insert error :" + e.getMessage(), e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse update(String contact, String phoneNumber, String verifCode, Integer position, Long id, String qq, String qqEmail) {
		ApiResponse apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
		apiResponse.setSuccess(Boolean.FALSE);
		//验证验证码是否过期
//		judgePhoneVerif(apiResponse, phoneNumber);
//
//		//验证验证码
//		if(StrUtil.isNotEmpty(phoneNumber) && StrUtil.isNotEmpty(verifCode) && !verifCode(verifCode, phoneNumber)){
//			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1006);
//			apiResponse.setSuccess(Boolean.FALSE);
//
//			return apiResponse;
//		}

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("contact", contact)
				.put("phoneNumber", phoneNumber)
				.put("position", position)
				.put("id", id)
				.put("qq", qq)
				.put("qqEmail", qqEmail)
				.toRequestEntity();

		try{
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			RestResponseTypeRef<Optional> res = usermgrRestTemplate.postRest(serviceId,"/memberCentre/update", body, new ParameterizedTypeReference<RestResponseTypeRef<Optional>>() {});

			if(res.getSuccess()){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
				apiResponse.setSuccess(Boolean.TRUE);
			}
		} catch (Exception e) {
			logger.error("web member update error :" + e.getMessage(), e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse getMemberInfoByShopId(Long shopId, String nick, HttpServletRequest request) {
		ApiResponse apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
		apiResponse.setSuccess(Boolean.FALSE);

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("nick", nick)
				.toRequestEntity();

		try{
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

			RestResponseTypeRef<MemberCentreDTO> res = usermgrRestTemplate.postRest(serviceId,"/memberCentre/getMemberInfoByShopId", body, new ParameterizedTypeReference<RestResponseTypeRef<MemberCentreDTO>>() {
			});

			if(res.getSuccess()){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
				apiResponse.setSuccess(Boolean.TRUE);
				Map<String, Object> data = Maps.newHashMap();
				MemberCentreDTO member = res.getData();
				boolean memberFlag = false;
				if(member == null){
					String headerAuthToken = request.getHeader(JwtConstants.AUTH_TOKEN.toLowerCase());
					String tokenKey =  JwtConstants.TOKEN_SESSION + headerAuthToken;
					Integer sysAdminFlag = (Integer) redisOperator.getHashKey(tokenKey, "sysAdminFlag");
					if(sysAdminFlag == 2)
						memberFlag =  true;

				}
				data.put("memberFlag", memberFlag);
				data.put("memberInfo", Optional.ofNullable(member).orElse(new MemberCentreDTO()));
				apiResponse.setData(data);
			}
		} catch (Exception e) {
			logger.error("web getMemberInfoByShopId error :" + e.getMessage(), e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse selectMemberInfo(Date startDate, Date endDate, String nick,
										Integer position, String status) {
		ApiResponse apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
		apiResponse.setSuccess(Boolean.FALSE);

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("startDate", DateUtils.formatYMdHms(DateUtil.getStartTimeOfDate(startDate)))
				.put("endDate", DateUtils.formatYMdHms(DateUtil.getEndTimeOfDate(endDate)))
				.put("nick", nick)
				.put("position", position)
				.put("status", status)
				.toRequestEntity();

		try{
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			RestResponseTypeRef<List<MemberCentreDTO>> res = usermgrRestTemplate.postRest(serviceId,"/memberCentre/selectMemberInfoBySubDateAndShopAndPosition", body, new ParameterizedTypeReference<RestResponseTypeRef<List<MemberCentreDTO>>>() {
			});

			if(res.getSuccess()){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
				apiResponse.setSuccess(Boolean.TRUE);
				Map<String, Object> data = Maps.newHashMap();
				data.put("memberInfoLst", Optional.ofNullable(res.getData()).orElse(Lists.newArrayListWithCapacity(0)));
				apiResponse.setData(data);
			}
		} catch (Exception e) {
			logger.error("web selectMemberInfo error :" + e.getMessage(), e);
		}

		return apiResponse;
	}

	@Override
	public ApiResponse sendSmsVerifCode(String phoneNumber) {
		ApiResponse apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
		apiResponse.setSuccess(Boolean.FALSE);

		try{
			//组装短信内容 验证码
			int verifCode = (int)(Math.random()*8999) +1000;

			String content = "【客服魔方】 您的会员验证码是：" + verifCode + "，祝生意兴隆";

			MsgServiceUtil.sendMobileMsg(MsgSystemConstants.ENTERPRISE_ID, MsgSystemConstants.LOGIN_NAME, MsgSystemConstants.PASSWORD, MsgSystemConstants.SENDURL, content, phoneNumber);

			redisOperator.set(verifRedisPrefix + phoneNumber, verifCode + "");
			redisOperator.expire(verifRedisPrefix + phoneNumber, 5l, TimeUnit.MINUTES);

			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			logger.error("web sendSmsVerifCode error :" + e.getMessage(), e);
		}

		return apiResponse;
	}

	@Override
	public void insertForInitialization(String shopId, String title, String csNick, String phone, String qq, String email) {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("title", title)
				.put("csNick", csNick)
				.put("phone", phone)
				.put("qq", qq)
				.put("email", email)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		usermgrRestTemplate.postRest(serviceId, "/memberCentre/insertForInitialization", body);

	}

	@Override
	public boolean judgePopUpOrNot(Long shopId, String userPin, String shopName) throws Exception {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId)
				.put("userPin", userPin)
				.put("shopName", shopName)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId, "/memberCentre/judgePopUpOrNot", body);

		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			Object result = apiResponse.getData().get("result");
			return Boolean.parseBoolean(result.toString());
		}
		return false;
	}

	/**
	 * 验证 传入是否正确
	 * @param verifCode
	 * @param phoneNumber
	 * @return
	 */
	private Boolean verifCode(String verifCode, String phoneNumber){
		return verifCode.equals(redisOperator.get(verifRedisPrefix + phoneNumber));
	}

	/**
	 * 验证验证码是否过期
	 * @param phoneNumber
	 * @return
	 */
	private ApiResponse judgePhoneVerif(ApiResponse apiResponse, String phoneNumber){
		//验证验证码是否过期
		if(redisOperator.get(verifRedisPrefix + phoneNumber) == null){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1007);
			apiResponse.setSuccess(Boolean.FALSE);
		}
		return apiResponse;
	}
}
