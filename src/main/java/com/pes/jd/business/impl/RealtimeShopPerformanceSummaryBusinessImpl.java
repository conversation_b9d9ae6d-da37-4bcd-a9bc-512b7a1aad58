package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.*;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.jwt.JwtConstants;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.ConvertRemindTypeEnum;
import com.pes.jd.model.Enum.OrderTypeEnum;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.CustConversionTwoParam;
import com.pes.jd.model.Param.ShopMnoitorParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.constant.PesCommonConstant;
import com.pes.jd.ms.domain.Data.master.ShopAutoAllocatedSettingDTO;
import com.pes.jd.ms.domain.Data.master.ShopSettingBatchRemindCno;
import com.pes.jd.ms.domain.Data.rtsub.CsNickTaskCount;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Result.rtsub.CsAllocateResult;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实时绩效 - 业务包装类
 * 不允许发Rest请求 , 业务包装处理类
 */
@Service
public class RealtimeShopPerformanceSummaryBusinessImpl implements RealtimeShopPerformanceSummaryBusiness {
    private static final Logger logger = LoggerFactory.getLogger(RealtimeShopPerformanceSummaryBusinessImpl.class);

    @Resource
    private RealtimeShopPerformanceBusiness realtimeShopPerformanceBusiness;
    @Resource
    private RealtimeCsPerformanceMonitorBusiness realtimeCsPerformanceMonitorBusiness;
    @Resource
    private RealTimeCustConversionBusiness realTimeCustConversionBusiness;
    @Resource
    private ShopSysManagerBusiness shopSysManagerBusiness;
    @Resource
    private ShopBatchRemindBusiness shopBatchRemindBusiness;
    @Resource
    private ShopGoodsSkuInfoBusiness shopGoodsSkuInfoBusiness;
    @Resource
    private ShopAutoAllocatedSettingBussiness shopAutoAllocatedSettingBussiness;
    @Resource
    private RedisOperator<String,Object> redisOperator;
    @Resource
    private ShopSettingBatchRemindCnoBussiness shopSettingBatchRemindCnoBussiness;
    @Resource
    private GoodsQueryOfSpuBusiness goodsQueryOfSpuBusiness;
    @Resource
    private CustomerReserveBusiness customerReserveBusiness;
    @Resource
    private ShopReservePresaleBusiness shopReservePresaleBusiness;
    @Resource
    private SmartFollowUpBusiness smartFollowUpBusiness;
    @Resource
    private ShopRemindBlackListBusiness shopRemindBlackListBusiness;


    /**
     * 我的绩效
     */
    @Override
    public ApiResponse selectCurrentUserRealtimePerformance(UserShopQuery shop, String csNick, Set<String> preCsNickSet, Set<String> csNickSet, Date startDate, Date endDate) throws Exception {
        return realtimeCsPerformanceMonitorBusiness.selectCurrentUserRealtimePerformance(shop.getSelectedShop(), csNick, preCsNickSet, csNickSet, startDate, endDate);
    }

    /**
     * 店铺实时绩效
     */
    @Override
    public ApiResponse selectShopRealtimePerformance(UserShopQuery shop, Date startDate, Date endDate) throws Exception {
        return realtimeShopPerformanceBusiness.selectShopRealtimePerformance(shop.getSelectedShop(), startDate, endDate);
    }

    /**
     * 实时监控-不良接待
     *
     * @throws Exception
     */
    @Override
    public ApiResponse selectBadReceiveMnoitor(ShopQuery shopQuery, List<UserQuery> selectShopCs, Date startDate, Date endDate,
                                               String warningType, ShopSystemsettingDTO sys) throws Exception {
        ApiResponse apiResponse = realtimeCsPerformanceMonitorBusiness.selectBadReceiveMnoitor(shopQuery, selectShopCs, startDate, endDate, warningType);
        //设置告警相关
        if (sys != null && apiResponse.getData() != null) {
            apiResponse.getData().put("openWarn", sys.getOpenWarn());
            apiResponse.getData().put("warnAcceptCs", sys.getWarnAcceptCs());
        }

        return apiResponse;
    }

    @Override
    public ApiResponse selectShopMnoitor(ShopQuery shopQuery, ShopMnoitorParam shopMnoitorParam) throws Exception {
        ApiResponse apiResponse = realtimeCsPerformanceMonitorBusiness.selectShopMnoitor(shopQuery, shopMnoitorParam);
        return apiResponse;
    }

    @Override
    public ApiResponse serachNeededAllocatedCsConversionList(UserShopQuery shop, CustConversionParam param) throws Exception {

        if (param.getDimension().equals(PesCommonConstant.DIMENSION_SPU_BYTE)) {
            //spu维度 通过spuId查出所有的sku_id
            List<Long> skuIds = goodsQueryOfSpuBusiness.selectSkuIdsByWarids(shop.getSelectedShop(), param);
            param.setSkuLst(skuIds);
        }

         handle( shop,param);

        ApiResponse apiResponse = realTimeCustConversionBusiness.serachNeededAllocatedCsConversionList(shop.getSelectedShop(), param);
        if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
            Object obj = apiResponse.getData().get("result");
            if (obj != null) {
                List<CsConvertPoolDTO> csConversionLst = JacksonUtils.objTolist(obj, CsConvertPoolDTO.class);

                if(CollectionUtils.isNotEmpty(csConversionLst)){
                    List<Long> skuIdLst=Lists.newArrayList();
                    for (CsConvertPoolDTO csConvertPoolDTO : csConversionLst) {
                        if(CollectionUtils.isNotEmpty(csConvertPoolDTO.getSkuIdSet())){
                            skuIdLst.addAll(csConvertPoolDTO.getSkuIdSet());
                        }
                    }
                    List<CsDTO> csLst = shopSysManagerBusiness.selectShopCswwSimpleNames(shop.getSelectedShop(), null);
                    Map<String, String> simpleNameMap=Maps.newHashMap();
                    if (CollectionUtils.isNotEmpty(csLst)) {
                        simpleNameMap = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, cs -> cs.getCsSimpleNick()));
                    }
                    Map<Long, String> goodsMap=Maps.newHashMap();
                    if(CollectionUtils.isNotEmpty(skuIdLst)){
                        //查询商品信息
                        ApiResponse apiResponse2 = shopGoodsSkuInfoBusiness.selectShopGoodsSkuInfo(shop.getSelectedShop(),skuIdLst);
                        if(apiResponse2.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
                            List<ShopGoodsSkuDTO> goodsSkuLst = JacksonUtils.objTolist(apiResponse2.getData().get("goodsSkuLst"), ShopGoodsSkuDTO.class);
                            if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
                                //获得商品名称
                                goodsMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, ShopGoodsSkuDTO::getSkuName,(o,n )-> n));
                            }
                        }
                    }

                    for(CsConvertPoolDTO conv : csConversionLst){
                        if (conv == null) {
                            continue;
                        }
                        conv.setCsSimpleNick(simpleNameMap.get(conv.getCsNick()));
                        if(CollectionUtils.isNotEmpty(conv.getSkuIdSet())){
                            Set<Long> skuIdSet=conv.getSkuIdSet();
                            StringBuffer sb=new StringBuffer();
                            for (Long skuId : skuIdSet) {
                                if(goodsMap.get(skuId)!=null){
                                    sb.append(goodsMap.get(skuId)+",");
                                }
                            }
                            conv.setSkuName(sb.length()>0?sb.substring(0, sb.length()-1):null);
                            conv.setSkuIds(StringUtils.join(skuIdSet,","));
                        }

                    }
                    apiResponse.getData().put("result", csConversionLst);
                }
            }
        }

        return apiResponse;
    }

    private Boolean  returnResp(CustConversionParam param){
        if(param.getWareType().equals(OrderTypeEnum.GENERATE_ORDER.getType())){
            if(param.getAllocateIsRemind()){
                return true;
            }
            if(param.getType().equals("1")){
               if(param.getCnoRemind()){
                   return true;
               }
            }
            else if(param.getType().equals("2")){
                if(param.getRemind()){
                    return true;
                }
            }
        }else if(param.getWareType().equals(OrderTypeEnum.RESERVER_ORDER.getType())){
            if(param.getReserveAutoRemind()){
                return true;
            }
            if(param.getType().equals("1")){
                if(param.getReserveCnoRemind()){
                    return true;
                }
            }else if(param.getType().equals("2")){
                if(param.getReserveBatchRemind()){
                    return true;
                }
            }
        }else if(param.getWareType().equals(OrderTypeEnum.PRESALE_ORDER.getType())){
            if(param.getType().equals("1")){
                if(param.getPresaleCnoRemind()){
                    return true;
                }
            }else if(param.getType().equals("3")){
                if(param.getPresaleBarginRemind()){
                    return true;
                }
            }else if(param.getType().equals("4")){
                if(param.getPresaleBatchRemind()){
                    return true;
                }
            }
        }
        return false;
    }
    private void handle(UserShopQuery shop, CustConversionParam param) {
        Long shopId = shop.getSelectedShop().getShopId();
//      增加判断 商品类型 分别判断  自动分配和批量提醒
        Boolean isReserveAutoRemind=false;
        Boolean isReserveBatchRemind=false;
        Boolean isReserveCnoRemind=false;
        Boolean isPresaleAutoRemind=false;
        Boolean isPresaleBatchRemind=false;
        Boolean isPresaleBarginRemind=false;
        Boolean isPresaleCnoRemind=false;
        Boolean isAutoRemind=false;
        Boolean isBatchRemind=false;
        Boolean isCnoBatchRemind=false;
        Boolean isReserveRemind=false;
        if (OrderTypeEnum.RESERVER_ORDER.getType().equals(param.getWareType())) {
//      	预约
            String object = smartFollowUpBusiness.selectShopBatchRemindTaskState(shopId, param.getTaskType());
            if(!object.equals("noTask")){
                boolean flag = Boolean.parseBoolean(object);
                isReserveCnoRemind = flag;
                isReserveBatchRemind = flag;
                isReserveRemind = flag;
            }else{
                RestApiResponse2<ShopSettingBatchRemindReserveDTO> remindResp = shopBatchRemindBusiness.selectShopReserveBatchRemindSetting(shopId);
                if (remindResp.getSuccess()) {
                    ShopSettingBatchRemindReserveDTO remind = remindResp.getData().get("result");
                    if (null != remind) {
                        if (null != remind.getIsRseUncRemind()) {
                            isReserveCnoRemind=remind.getIsRseUncRemind();
                        }
                        if (null != remind.getIsRseUnpRemind()) {
                            isReserveBatchRemind=remind.getIsRseUnpRemind();
                        }
                        if(null != remind.getIsReserveRemind()){
                            isReserveRemind=remind.getIsReserveRemind();
                        }
                    }
                }
            }

            //判断自动分配
            ApiResponse allocatesResp = shopAutoAllocatedSettingBussiness.selectAppointmentAutoAllocatedSetting(shopId);
            if (allocatesResp.getSuccess()) {
                AppointmentAutoAllocatedSettingDTO auto = (AppointmentAutoAllocatedSettingDTO) allocatesResp.getData().get("result");
                if (auto != null&&auto.getAutoAllocated()!=null) {
                    isReserveAutoRemind=auto.getAutoAllocated();
                }
            }

        } else if (OrderTypeEnum.PRESALE_ORDER.getType().equals(param.getWareType())) {

            //预售
            String object = smartFollowUpBusiness.selectShopBatchRemindTaskState(shopId, param.getTaskType());
            if(!object.equals("noTask")){
                boolean flag = Boolean.parseBoolean(object);
                isPresaleCnoRemind = flag;
                isPresaleBarginRemind = flag;
                isPresaleBatchRemind = flag;
            }else{
                RestApiResponse2<ShopSettingBatchRemindPresaleDTO> remindResp = shopBatchRemindBusiness.selectShopPresaleBatchRemindSetting(shopId);
                if (remindResp.getSuccess()) {
                    ShopSettingBatchRemindPresaleDTO remind = (ShopSettingBatchRemindPresaleDTO) remindResp.getData().get("result");
                    if (null != remind) {

                        if (null != remind.getIsUnpoRemind() ) {
                            isPresaleCnoRemind=remind.getIsUnpoRemind();
                        }

                        if (null != remind.getIsBargainRemind()) {
                            isPresaleBarginRemind=remind.getIsBargainRemind();
                        }

                        if (null != remind.getIsBalanceRemind()) {
                            isPresaleBatchRemind=remind.getIsBalanceRemind();
                        }
                    }
                }
            }
            ApiResponse allocatesResp = shopAutoAllocatedSettingBussiness.selectAdvanceAutoAllocatedSetting(shopId);
            if (allocatesResp.getSuccess()) {
                AdvanceAutoAllocatedSettingDTO auto = (AdvanceAutoAllocatedSettingDTO) allocatesResp.getData().get("result");
                if (auto != null) {
                    if (auto.getAutoAllocated() != null ) {
                        isPresaleAutoRemind=auto.getAutoAllocated();
                    }
                }
            }

        } else {
//      	默认普通
            String object = smartFollowUpBusiness.selectShopBatchRemindTaskState(shopId, param.getTaskType());
            if(!object.equals("noTask")){
                boolean flag = Boolean.parseBoolean(object);
                isBatchRemind = flag;
                isCnoBatchRemind = flag;
            }else{
                RestApiResponse2<ShopSettingBatchRemindDTO> remindResp = shopBatchRemindBusiness.selectShopBatchRemindSetting(shopId);
                if (remindResp.getSuccess()) {
                    ShopSettingBatchRemindDTO remind = remindResp.getData().get("result");
                    if (remind != null && remind.getIsRemind() != null ) {
                        isBatchRemind=remind.getIsRemind();

                    }
                }

                RestApiResponse2<ShopSettingBatchRemindCno> cnoResp = shopSettingBatchRemindCnoBussiness.selectShopSettingBatchRemindCnoByShopId(shopId);
                if (cnoResp != null && cnoResp.getSuccess()) {
                    ShopSettingBatchRemindCno cno = cnoResp.getData().get("result");
                    if (cno != null) {
                        if (cno.getIsRemind() != null ) {
                            isCnoBatchRemind=cno.getIsRemind();
                        }
                    }
                }
            }

            RestApiResponse2<ShopAutoAllocatedSettingDTO> allocatesResp = shopAutoAllocatedSettingBussiness.selectShopAutoAllocatedSettingByShopId(shopId);
            if (allocatesResp.getSuccess()) {
                ShopAutoAllocatedSettingDTO auto = allocatesResp.getData().get("result");
                if (auto != null) {
                    if (auto.getIsAutoAllocated() != null ) {
                        isAutoRemind=auto.getIsAutoAllocated();

                    }
                }
            }

        }
        param.setReserveCnoRemind(isReserveCnoRemind);
        param.setReserveBatchRemind(isReserveBatchRemind);
        param.setReserveAutoRemind(isReserveAutoRemind);
        param.setRemind(isBatchRemind);
        param.setAllocateIsRemind(isAutoRemind);
        param.setCnoRemind(isCnoBatchRemind);
        param.setPresaleAutoRemind(isPresaleAutoRemind);
        param.setPresaleCnoRemind(isPresaleCnoRemind);
        param.setPresaleBatchRemind(isPresaleBatchRemind);
        param.setPresaleBarginRemind(isPresaleBarginRemind);
        param.setReserveRemind(isReserveRemind);

    }

    private String getType(String type) {
        switch(type){
            case "1":
                type = "1";
                break;
            case "2":
            case "3":
                type = "2";
                break;
            case "4":
            case "12":
            case "15":
                type = "7";
                break;
            case "5":
            case "6":
                type = "8";
                break;
            case "7":
                type = "3";
                break;
            case "8":
            case "9":
                type = "4";
                break;
            case "10":
            case "11":
                type = "5";
                break;
            case "14":
                type = "6";
        }
        return type;
    }


    @Override
    public RestApiResponse2<CsAllocateResult> saveAllocatedCsConversionLst(UserShopQuery shop, ShopUserDTO user, String csConversions, Integer flag) throws Exception {
        String manualMerchandisingBlacklistString = shopRemindBlackListBusiness.selectManualMerchandisingBlacklistList(shop.getSelectedShop().getShopId());
        RestApiResponse2<CsAllocateResult> resp = realTimeCustConversionBusiness.saveAllocatedCsConversionLst(shop.getSelectedShop(), user, csConversions, flag, manualMerchandisingBlacklistString);
        if (resp.getSuccess()) {
            CsAllocateResult result = resp.getData().get("result");
            List<CsNickTaskCount> taskCountLst = result.getCsNickTaskCountLst();
            //分配客服有新任务存入redis,有效期1天
            if (CollectionUtils.isNotEmpty(taskCountLst)) {
                for (CsNickTaskCount task : taskCountLst) {
                    redisOperator.put(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG + task.getCsNick(), "taskFlag", task.getIsNewTaskFlag());
                    redisOperator.expire(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG + task.getCsNick(), 12 * 60, TimeUnit.MINUTES);
                }
            }
        }
        return resp;
    }


    @Override
    public RestApiResponse2<CsAllocateResult> oneStepSaveAllocatedCsConversionLst(UserShopQuery shop, ShopUserDTO user, CustConversionParam param) throws Exception {
        handle(shop, param);
        if (returnResp(param)) {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        }

        String manualMerchandisingBlacklistString = shopRemindBlackListBusiness.selectManualMerchandisingBlacklistList(shop.getSelectedShop().getShopId());
        RestApiResponse2<CsAllocateResult> resp = realTimeCustConversionBusiness.oneStepSaveAllocatedCsConversionLst(shop.getSelectedShop(), user, param, manualMerchandisingBlacklistString);
        if (resp.getSuccess()) {
            CsAllocateResult result = resp.getData().get("result");
            List<CsNickTaskCount> taskCountLst = result.getCsNickTaskCountLst();
            //分配客服有新任务存入redis,有效期1天
            if (CollectionUtils.isNotEmpty(taskCountLst)) {
                for (CsNickTaskCount task : taskCountLst) {
                    redisOperator.put(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG + task.getCsNick(), "taskFlag", task.getIsNewTaskFlag());
                    redisOperator.expire(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG + task.getCsNick(), 12 * 60, TimeUnit.MINUTES);
                }
            }
        }
        return resp;
    }
    
    public static <T> RestApiResponse2<T> apiResponse(ApiCodeEnum apiCodeEnum, RestApiResponse2<T> baseResponse2) {
        baseResponse2.of(apiCodeEnum.getCode(), apiCodeEnum.getMsg());
        return baseResponse2;
    }

    @Override
    public RestApiResponse2<CsAllocateResult> batchSaveAllocatedCsConversionLst(UserShopQuery shop, ShopUserDTO user, CustConversionParam param) throws Exception {
        handle(shop, param);
        if (returnResp(param)) {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        }
        String manualMerchandisingBlacklistString = shopRemindBlackListBusiness.selectManualMerchandisingBlacklistList(shop.getSelectedShop().getShopId());
        RestApiResponse2<CsAllocateResult> resp = realTimeCustConversionBusiness.batchSaveAllocatedCsConversionLst(shop.getSelectedShop(), user, param, manualMerchandisingBlacklistString);
        if (resp.getSuccess()) {
            CsAllocateResult result = resp.getData().get("result");
            List<CsNickTaskCount> taskCountLst = result.getCsNickTaskCountLst();
            //分配客服有新任务存入redis,有效期1天
            if (CollectionUtils.isNotEmpty(taskCountLst)) {
                for (CsNickTaskCount task : taskCountLst) {
                    redisOperator.put(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG + task.getCsNick(), "taskFlag", task.getIsNewTaskFlag());
                    redisOperator.expire(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG + task.getCsNick(), 12 * 60, TimeUnit.MINUTES);
                }
            }
        }
        return resp;
    }

    @Override
    public ApiResponse getToolsSettingByShopId(UserShopQuery shop) throws Exception {
        return realTimeCustConversionBusiness.getToolsSettingByShopId(shop.getSelectedShop());
    }

    @Override
    public ApiResponse setShopToolsSetting(UserShopQuery shop, String xiadanWord,
                                           String payWord, Boolean isSendGoodsUrl) throws Exception {
        return realTimeCustConversionBusiness.setShopToolsSetting(shop.getSelectedShop(), xiadanWord, payWord, isSendGoodsUrl);

    }

    @Override
    public ApiResponse serachAllocatedConvertedLst(UserShopQuery shop, CustConversionParam param) throws Exception {
        ShopQuery selectedShop = shop.getSelectedShop();
        ApiResponse apiResponse = realTimeCustConversionBusiness.serachAllocatedConvertedLst(selectedShop, param);
        ApiResponse userApi = shopSysManagerBusiness.selectUserByShopId(selectedShop);
        List<ShopUserDTO> userLst = null;
        if (userApi.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
            Object obj = userApi.getData().get("result");
            if (obj != null) {
                userLst = JacksonUtils.objTolist(obj, ShopUserDTO.class);
            }
        } else {
            logger.error(userApi.getRpMsg());
        }
        if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {

            Object obj = apiResponse.getData().get("csConversionLst");
            if (obj != null) {
                List<CsConversionDTO> csConversionLst = JacksonUtils.objTolist(obj, CsConversionDTO.class);
                if(CollectionUtils.isEmpty(csConversionLst)) return apiResponse;

                List<CsDTO> csLst = shopSysManagerBusiness.selectShopCswwSimpleNames(shop.getSelectedShop(), null);
                if (CollectionUtils.isNotEmpty(csLst)) {
                    //话术列表
                    Map<Long, String> wordMap = Maps.newHashMap();
                    List<ShopRemindWordDTO> shopRemindWordList = shopBatchRemindBusiness.selectShopRemindWordLst(selectedShop.getShopId(), param.getOrderType());
                    if(CollectionUtils.isNotEmpty(shopRemindWordList)){
                        wordMap = shopRemindWordList.stream().collect(Collectors.toMap(ShopRemindWordDTO::getId, ShopRemindWordDTO::getContent, (n1,n2)->n2));
                    }

                    Map<String, String> simpleNameMap = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
                    Map<Long, String> userNameMap = null;
                    if (CollectionUtils.isNotEmpty(userLst)) {
                        userNameMap = userLst.stream().collect(Collectors.toMap(ShopUserDTO::getUserId, ShopUserDTO::getNick));
                    }

                    List<String> collect = Lists.newArrayList();
                    for (CsConversionDTO dto : csConversionLst) {
                        if(StringUtils.isNotBlank(dto.getSmsWordId()))
                            collect.add(dto.getSmsWordId());

                        if(StringUtils.isNotBlank(dto.getWordId()))
                            collect.add(dto.getWordId());
                    }
                    Map<Long, ShopBatchRemindTaskDTO> newWordMap = getNewDDWordMap(smartFollowUpBusiness.selectByIds(selectedShop.getShopId() + "", StringUtils.join(collect, ",")));
                    //取skuIdList
                    List<Long> skuIdLst=Lists.newArrayList();
                    toSkuIdList(skuIdLst, csConversionLst);

                    //商品列表
                    ApiResponse response = shopGoodsSkuInfoBusiness.selectShopGoodsSkuInfo(selectedShop, skuIdLst);
                    Map<Long, String> goodsMap = toGoodsMap(response);

                    for (CsConversionDTO csCon : csConversionLst) {
                        csCon.setCsSimpleNick(simpleNameMap.get(csCon.getCsNick()));
                        csCon.setAllocatedSimpleCsNick(simpleNameMap.get(csCon.getAllocatedCsNick()));
                        if (csCon.getOperator() == null && csCon.getRemindType() == ConvertRemindTypeEnum.BATCH_REMIND.getType()) {
                            csCon.setOperatorStr("--");
                        } else {
                            if (userNameMap != null) {
                                csCon.setOperatorStr(userNameMap.get(csCon.getOperator()));
                            }
                        }
                        //取话术名称
                        if (csCon.getWordId() != null && !csCon.getWordId().equals("null")) {
                            if (csCon.getCreated().before(DateFormatUtils.parseYMdHms(CommonConstants.SMART_FOLLOWUP_ONLINE_DATE)) || csCon.getRemindType() == 1) {//上线前原表取
                                if (wordMap.get(Long.valueOf(csCon.getWordId())) != null) {
                                    csCon.setWordName(wordMap.get(Long.valueOf(csCon.getWordId())));
                                } else {
                                    csCon.setWordName("--");
                                }
                            }else {// 上线后新表取
                                ShopBatchRemindTaskDTO task = null;
                                if (csCon.getSendRemindType() == 2) {
                                    task = newWordMap.get(Long.valueOf(csCon.getSmsWordId()));
                                } else {
                                    task = newWordMap.get(Long.valueOf(csCon.getWordId()));
                                }
                                if (task != null) {
                                    if (csCon.getSendRemindType() == 2) {//n2咨询下单未付款\r\n3静默下单未付款
                                        if (csCon.getTaskType().equals(2)) {
                                            csCon.setWordName(task.getConsultWord());
                                        } else if (csCon.getTaskType().equals(3)) {
                                            csCon.setWordName(task.getSilenceWord());
                                        }
                                    } else if (csCon.getSendRemindType() == 1) {
                                        csCon.setWordName(task.getRemindWord());
                                    } else if (csCon.getSendRemindType() == 3) {
                                        csCon.setWordName(task.getRemindWord());
                                    }
                                } else {
                                    csCon.setWordName("--");
                                }
                            }
                        }else if(csCon.getSmsWordId() != null && !csCon.getSmsWordId().equals("null") && csCon.getSendRemindType() == 2){
                            ShopBatchRemindTaskDTO task = newWordMap.get(Long.valueOf(csCon.getSmsWordId()));
                            if(task != null){
                                if (csCon.getTaskType().equals(2)) {
                                    csCon.setWordName(task.getConsultWord());
                                } else if (csCon.getTaskType().equals(3)) {
                                    csCon.setWordName(task.getSilenceWord());
                                }
                            }else{
                                csCon.setWordName("--");
                            }
                        }else{
                            csCon.setWordName("--");
                        }
                        //取商品名称
                        if(csCon.getSkuId() != null && !csCon.getSkuId().equals("null")){
                            if(goodsMap.get(Long.valueOf(csCon.getSkuId())) != null){
                                csCon.setSkuNames(goodsMap.get(Long.valueOf(csCon.getSkuId())));
                            }else{
                                csCon.setSkuNames("--");
                            }
                        }else{
                            csCon.setSkuNames("--");
                        }


                        if(csCon.getSkuId() == null || csCon.getSkuId().equals("null")){
                            csCon.setSkuId("--");
                        }
                    }
                    apiResponse.getData().put("csConversionLst", csConversionLst);
                }

            }

        }
        return apiResponse;
    }

    private Map<Long, String> toGoodsMap(ApiResponse response) throws Exception {
        Map<Long, String> goodsMap = Maps.newHashMap();
        if(response.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
            List<ShopGoodsSkuDTO> goodsSkuLst = JacksonUtils.objTolist(response.getData().get("goodsSkuLst"), ShopGoodsSkuDTO.class);
            if(CollectionUtils.isNotEmpty(goodsSkuLst)){
                //获得商品名称
                goodsMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, ShopGoodsSkuDTO::getSkuName,(o,n )-> n));
            }
        }
        return goodsMap;
    }

    private void toSkuIdList(List<Long> skuIdLst, List<CsConversionDTO> csConversionLst) {
        for (CsConversionDTO csCon : csConversionLst) {
            if(CollectionUtils.isNotEmpty(csCon.getSkuIdSet())){
                skuIdLst.addAll(csCon.getSkuIdSet());
            }
            if(csCon.getSkuId()!=null && !csCon.getSkuId().equals("null")){
                skuIdLst.add(Long.valueOf(csCon.getSkuId()));
            }
        }
    }

    @Override
    public ApiResponse serachCsConversionTaskTotal(UserShopQuery shop, CustConversionParam param) throws Exception {
    return realTimeCustConversionBusiness.serachCsConversionTaskTotal(shop.getSelectedShop(), param);
    }

    @Override
    public ApiResponse serachAllocateCsConversionForOneWarn(ShopQuery shop, CustConversionParam param) throws Exception {
        Map<String, Object> actMap = shopReservePresaleBusiness.selectShopReservePresale(shop);
        param.setResMap(actMap);
        ApiResponse apiResponse = realTimeCustConversionBusiness.serachAllocateCsConversionForOneWarn(shop, param);

        if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
            Object obj = apiResponse.getData().get("csConversionLst");
            if (obj != null) {
                //取该店铺默认话术
                ShopRemindWordDTO wordParam = new ShopRemindWordDTO(shop.getShopId());
                wordParam.setIsDefault(true);
                wordParam.setCsNick(param.getCsNickLst().get(0));
                RestApiResponse2<List<ShopRemindWordDTO>> resp = shopBatchRemindBusiness.selectShopRemindWordLst(wordParam);
                List<ShopRemindWordDTO> words = Lists.newArrayList();
                if (resp.getSuccess()) {
                    words = resp.getData().get(RestApiResponse2.RESULT_SINGLE_NAME);
                }
                List<CsConversionDTO> csConversionLst = JacksonUtils.objTolist(obj, CsConversionDTO.class);
                List<Long> skuIdLst=Lists.newArrayList();
                for (CsConversionDTO cs : csConversionLst) {
                    if(CollectionUtils.isNotEmpty(cs.getSkuIdSet())){
                        skuIdLst.addAll(cs.getSkuIdSet());
                    }
                    if(cs.getSkuId()!=null && !cs.getSkuId().equals("null")){
                        skuIdLst.add(Long.valueOf(cs.getSkuId()));
                    }
                }
                Map<Long, String> goodsMap=Maps.newHashMap();
                if(CollectionUtils.isNotEmpty(skuIdLst)){
                    //查询商品信息
                    ApiResponse apiResponse2 = shopGoodsSkuInfoBusiness.selectShopGoodsSkuInfo(shop,skuIdLst);
                    if(apiResponse2.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
                        List<ShopGoodsSkuDTO> goodsSkuLst = JacksonUtils.objTolist(apiResponse2.getData().get("goodsSkuLst"), ShopGoodsSkuDTO.class);
                        if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
                            //获得商品名称
                            goodsMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, ShopGoodsSkuDTO::getSkuName,(o,n )-> n));
                        }
                    }
                }
                for (CsConversionDTO csConversionDTO : csConversionLst) {
                    // 若话术列表不为空，根据类型设置话术内容
                    if(CollectionUtils.isNotEmpty(words)){
                        Map<Integer, ShopRemindWordDTO> shopRemindWordMap = words.stream().collect(Collectors.toMap(ShopRemindWordDTO::getType, Function.identity(), (v1, v2) -> v2));
                        if(csConversionDTO.getTaskType()!=null){
                            Integer taskType = csConversionDTO.getTaskType();
                            if(3 == taskType){
                                taskType = 2;
                            }else if(6 == taskType){
                                taskType = 5;
                            }else if(9 == taskType){
                                taskType = 8;
                            }else if(11 == taskType){
                                taskType = 10;
                            }else if(12 == taskType){
                                taskType = 4;
                            }else if(15 == taskType){
                                taskType = 4;
                            }else if(14 == taskType){
                                taskType = 12;
                            }

                            if(MapUtils.isNotEmpty(shopRemindWordMap)){
                                if(shopRemindWordMap.get(taskType)!= null){
                                   // csConversionDTO.setRemindWord(shopRemindWordMap.get(taskType).getContent());//设置话术内容
                                    csConversionDTO.setCouponId(shopRemindWordMap.get(taskType).getCouponId());
                                    csConversionDTO.setCouponStartTime(shopRemindWordMap.get(taskType).getCouponStartTime());
                                    csConversionDTO.setCouponEndTime(shopRemindWordMap.get(taskType).getCouponEndTime());
                                }
                            }
                        }
                    }

                    if(CollectionUtils.isNotEmpty(csConversionDTO.getSkuIdSet())){
//                        Set<Long> skuIdSet=csConversionDTO.getSkuIdSet();
                        StringBuffer sb=new StringBuffer();
//                        for (Long skuId : skuIdSet) {
//                            if(goodsMap.get(skuId)!=null){
//                                sb.append(goodsMap.get(skuId)+",");
//                            }
//                        }
                        if(csConversionDTO.getSkuId() == null){
                            for (Long skuId : csConversionDTO.getSkuIdSet()) {
                                csConversionDTO.setSkuId(String.valueOf(skuId));
                            }
                        }
                        String skuId = csConversionDTO.getSkuId();
                        if(skuId != null && !skuId.equals("null")){
                            sb.append(goodsMap.get(Long.valueOf(skuId)));
                            csConversionDTO.setSkuNames(sb.length()>0?sb.substring(0, sb.length()-1):null);
                        }else{
                            csConversionDTO.setSkuNames(null);
                        }
                    }

                    //商品sku需要验证是不是本店铺的，不是就置为""
                    if(csConversionDTO.getSkuId()!=null){
                        if(!goodsMap.containsKey(csConversionDTO.getSkuId())){
                            csConversionDTO.setGoodsUrl("");
                        }
                    }


                }
                //按分配时间从升序
                csConversionLst.sort(Comparator.comparing(CsConversionDTO::getCreated, Comparator.nullsLast(Date::compareTo)));
                apiResponse.getData().put("csConversionLst", csConversionLst);
            }

        }

        return apiResponse;
    }

    @Override
    public ApiResponse setOneWarnCsConversion(ShopQuery shop, CustConversionParam param, String operateType) throws Exception {
        if("1".equals(operateType)){
            if("1".equals(param.getType())||"4".equals(param.getType())||"7".equals(param.getType())||"12".equals(param.getType())||"15".equals(param.getType())){
              List<OrderLatelyDTO> orderLst = realTimeCustConversionBusiness.getOrderLatelyDate(shop,param.getBuyerNick());
              if(CollectionUtils.isNotEmpty(orderLst)){
                  param.setOrderLst(orderLst);
              }
              else{
                      logger.info("buyerNick：{} web 消息途径未实时获取到订单 ",param.getBuyerNick());
              }

            }
            if("14".equals(param.getType())){
                List<CustomerReserveDTO> customerReserveLst =customerReserveBusiness.selectReserveBuyerLst(shop,param.getBuyerNick());
                param.setCustomerReserveLst(customerReserveLst);
            }else{
                logger.info("buyerNick：{} web sub服务未实时获取到预约信息 ",param.getBuyerNick());
            }
        }
        if(StringUtils.isNotBlank(param.getActivityId())){
           ShopReservePresaleDTO active= realTimeCustConversionBusiness.selectShopReserveAndPresaleByActivityId(shop,param.getActivityId());
            param.setActive(active);
        }
        return realTimeCustConversionBusiness.setOneWarnCsConversion(shop, param, operateType);
    }

    @Override
    public ApiResponse warnDispatcher(UserShopQuery shopQuery, String warnType, String csNick) throws Exception {
        return realtimeCsPerformanceMonitorBusiness.warnDispatcher(shopQuery.getSelectedShop(), warnType, csNick);
    }

    @Override
    public ApiResponse updateWarnSetting(ShopQuery shop, Boolean openWarn, String warnAcceptCs) throws Exception {
        return realtimeCsPerformanceMonitorBusiness.updateWarnSetting(shop, openWarn, warnAcceptCs);
    }

    @Override
    public ApiResponse updateAllocateCsNickById(ShopQuery shop, CustConversionParam param) throws Exception {
        return realTimeCustConversionBusiness.updateAllocateCsNickById(shop, param);
    }

    @Override
    public ApiResponse batchUpdateAllocateCsNick(ShopQuery shop, CustConversionParam param) throws Exception {
        return realTimeCustConversionBusiness.batchUpdateAllocateCsNick(shop, param);
    }

    @Override
    public ApiResponse serachCsConversionTaskTotalNew(UserShopQuery shop, CustConversionParam param) {
        return realTimeCustConversionBusiness.serachCsConversionTaskTotalNew(shop.getSelectedShop(), param);
    }

    @Override
    public ApiResponse serachAllocatedConvertedLstNew(UserShopQuery shopQuery, CustConversionTwoParam param, String followUpType) throws Exception {
        ShopQuery shop = shopQuery.getSelectedShop();
        String wordId = param.getWordId();
        if (StrUtil.isNotEmpty(wordId)) {//当前查询不需要这个字段，调的原接口直接封装参数就行
            param.setWordId(null);
        }
        ApiResponse apiResponse = realTimeCustConversionBusiness.serachAllocatedConvertedLstNew(shop, param);
        ApiResponse userApi = shopSysManagerBusiness.selectUserByShopId(shop);
        List<ShopUserDTO> userLst = null;
        if (userApi.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
            Object obj = userApi.getData().get("result");
            if (obj != null) {
                userLst = JacksonUtils.objTolist(obj, ShopUserDTO.class);
            }
        } else {
            logger.error(userApi.getRpMsg());
        }
        if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {

            Object obj = apiResponse.getData().get("csConversionLst");
            if (obj != null) {
                List<CsDTO> csLst = shopSysManagerBusiness.selectShopCswwSimpleNames(shopQuery.getSelectedShop(), null);
                if (CollectionUtils.isNotEmpty(csLst)) {
                    //话术列表
                    Map<Long, String> wordMap = Maps.newHashMap();
                    List<ShopRemindWordDTO> shopRemindWordList = shopBatchRemindBusiness.selectShopRemindWordLst(shop.getShopId(), "");
                    if (CollectionUtils.isNotEmpty(shopRemindWordList)) {
                        wordMap = shopRemindWordList.stream().collect(Collectors.toMap(ShopRemindWordDTO::getId, ShopRemindWordDTO::getContent, (n1, n2) -> n2));
                    }

                    Map<String, String> simpleNameMap = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
                    Map<Long, String> userNameMap = null;
                    if (CollectionUtils.isNotEmpty(userLst)) {
                        userNameMap = userLst.stream().collect(Collectors.toMap(ShopUserDTO::getUserId, ShopUserDTO::getNick));
                    }
                    List<CsConversionDTO> csConversionLst = JacksonUtils.objTolist(obj, CsConversionDTO.class);
                    CsConversionDTO total = new CsConversionDTO();
                    CsConversionDTO avg = new CsConversionDTO();
                    //取skuIdList
                    List<Long> skuIdLst = Lists.newArrayList();
                    toSkuIdList(skuIdLst, csConversionLst);

                    //商品列表
                    ApiResponse response = shopGoodsSkuInfoBusiness.selectShopGoodsSkuInfo(shop, skuIdLst);
                    Map<Long, String> goodsMap = toGoodsMap(response);
                    if (CollUtil.isNotEmpty(csConversionLst)) {
                        //话术列表
                        Map<Long, ShopBatchRemindTaskDTO> newWordMap = getNewDDWordMap(smartFollowUpBusiness.selectByIds(shop.getShopId() + "", csConversionLst.stream().map(CsConversionDTO::getWordId).collect(Collectors.joining(","))));
                        for (CsConversionDTO csCon : csConversionLst) {
                            csCon.setCsSimpleNick(simpleNameMap.get(csCon.getCsNick()));
                            csCon.setAllocatedSimpleCsNick(simpleNameMap.get(csCon.getAllocatedCsNick()));
                            if (csCon.getOperator() == null && csCon.getRemindType().equals(ConvertRemindTypeEnum.BATCH_REMIND.getType())) {
                                csCon.setOperatorStr("--");
                            } else {
                                if (userNameMap != null) {
                                    csCon.setOperatorStr(userNameMap.get(csCon.getOperator()));
                                }
                            }
                            //取话术内容
                            if (csCon.getWordId() != null && !csCon.getWordId().equals("null")) {
                                if (csCon.getCreated().before(DateFormatUtils.parseYMdHms(CommonConstants.SMART_FOLLOWUP_ONLINE_DATE)) || followUpType.equals("1")) {//上线前原表取
                                    if (wordMap.get(Long.valueOf(csCon.getWordId())) != null) {
                                        csCon.setWordName(wordMap.get(Long.valueOf(csCon.getWordId())));
                                    } else {
                                        csCon.setWordName("--");
                                    }
                                } else {// 上线后新表取
                                    ShopBatchRemindTaskDTO task = newWordMap.get(Long.valueOf(csCon.getWordId()));
                                    if (task != null) {
                                        if (CommonConstants.SMS_SEND.equals(Integer.valueOf(followUpType))) {
//                                            n2咨询下单未付款\r\n3静默下单未付款
                                            if (csCon.getTaskType().equals(2)) {
                                                csCon.setWordName(task.getConsultWord());
                                            } else if (csCon.getTaskType().equals(3)) {
                                                csCon.setWordName(task.getSilenceWord());
                                            }
                                        } else if(CommonConstants.DD_SEND.equals(Integer.valueOf(followUpType))){
                                            csCon.setWordName(task.getRemindWord());
                                        }
                                    } else {
                                        csCon.setWordName("--");
                                    }
                                }
                            }
                            //取商品名称
                            if (csCon.getSkuId() != null && !csCon.getSkuId().equals("null")) {
                                if (goodsMap.get(Long.valueOf(csCon.getSkuId())) != null) {
                                    csCon.setSkuNames(goodsMap.get(Long.valueOf(csCon.getSkuId())));
                                } else {
                                    csCon.setSkuNames("--");
                                }
                            } else {
                                csCon.setSkuNames("--");
                            }
                            if (csCon.getSkuId() == null || csCon.getSkuId().equals("null")) {
                                csCon.setSkuId("--");
                            }
                        }
                    }
                    //有话术关键词需要按话术关键词过滤一次
                    if (CollUtil.isNotEmpty(csConversionLst) && StrUtil.isNotEmpty(wordId)) {
                        csConversionLst = csConversionLst.stream().filter(ele -> ele.getWordName().contains(wordId.trim())).collect(Collectors.toList());
                    }
                    apiResponse.getData().put("csConversionLst", csConversionLst);
                    double totalPayment = 0.0d;
                    for (CsConversionDTO dto : csConversionLst) {
                        totalPayment += BaseUtils.getNonNull(dto.getPayment());
                    }
                    total.setPayment(totalPayment);
                    apiResponse.getData().put("total", total);
                    avg.setPayment(totalPayment == 0.0d ? 0.0 : totalPayment / csConversionLst.size());
                    apiResponse.getData().put("avg", avg);
                }
            }

        }
        return apiResponse;
    }

    private Map<Long, ShopBatchRemindTaskDTO> getNewDDWordMap(List<ShopBatchRemindTaskDTO> newWordSet) {
        Map<Long, ShopBatchRemindTaskDTO> resultMap = new HashMap<>(newWordSet.size());
        for (ShopBatchRemindTaskDTO dto : newWordSet) {
            resultMap.put(dto.getId(), dto);
        }
        return resultMap;
    }

    @Override
    public Map<Integer, List<ShopRemindWordDTO>> selectShopRemindWordLst(ShopQuery shop, String origin, String csNick) {
        ShopRemindWordDTO dto = new ShopRemindWordDTO();
        dto.setShopId(shop.getShopId());
        dto.setIsDefault(null);
        dto.setFromPage(2);
        dto.setCsNick(csNick);
        List<ShopRemindWordDTO> remindLst = null;
        RestApiResponse2<List<ShopRemindWordDTO>> resp = shopBatchRemindBusiness.selectShopRemindWordLst(shop,dto,origin);
        if (resp.getSuccess()) {
            remindLst = resp.getData().get(RestApiResponse2.RESULT_SINGLE_NAME);
        }
        Map<Integer, List<ShopRemindWordDTO>> result = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(remindLst)) {
            result = remindLst.stream().collect(Collectors.groupingBy(ShopRemindWordDTO::getType));
        }
        return result;
    }

    @Override
	public Map<String, Object> selectLoginCs(UserShopQuery shopQuery,List<UserQuery> userQueries, ShopUserDTO user) throws Exception {
        ApiResponse apiResponse = realTimeCustConversionBusiness.selectShopLoginCs(shopQuery,userQueries, user);
		Map<String, Object> data = Maps.newHashMap();
		Map<Long, List<UserQuery>> csMap = null;
		if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
    		List<UserQuery> userQueLst = JacksonUtils.objTolist(apiResponse.getData().get("userQueLst"), UserQuery.class);
    		System.out.println("在线客服数量："+userQueLst.size());
    		logger.info("在线客服数量:{}",userQueLst.size());
    		if(CollectionUtils.isNotEmpty(userQueLst)){
    			csMap = userQueLst.stream().collect(Collectors.groupingBy(UserQuery::getGroupId));
    		}else{
    			csMap = new HashMap<>(0);
    		}
    	}
		data.put("csMap", csMap);
    	return data;
	}

    @Override
    public Map<String, Object> selectShopPoolTaskCount(ShopQuery shop, CustConversionParam param, String csNick) throws Exception {
        long s1=System.currentTimeMillis();
        Map<String, Object> result = Maps.newHashMap();
        int count = 0;
        int sumCount = 0;
        //查询redis 客服是否有新任务
        Object obj = redisOperator.getHashKey(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG+csNick , "taskFlag");
        Boolean newFlag = false;
        if (obj != null) {
            newFlag = (Boolean) obj;
        }
        //不存在新任务分配总数从redis获取
        if (!newFlag) {
            Object objCount = redisOperator.getHashKey(JwtConstants.TOKEN_SESSION_NEW_TASK_COUNT+ csNick , "taskSumCount");
            if(objCount!=null){
                sumCount=(Integer)objCount;
            }
        } else {
            //存在新任务走查询
            RestApiResponse2<CsNickTaskCount> resp = realTimeCustConversionBusiness.selectShopPoolTaskCount(shop, param, csNick);
            //放入redis中
            if (resp != null && resp.getSuccess()) {
                CsNickTaskCount task = resp.getData().get("result");
                count=task.getCount();
                sumCount=task.getSumCount();
                //存在新任务去dao层查询，将查询的数据分配数量存入redis
                redisOperator.put(JwtConstants.TOKEN_SESSION_NEW_TASK_COUNT+task.getCsNick(), "taskSumCount",task.getSumCount() );
                redisOperator.expire(JwtConstants.TOKEN_SESSION_NEW_TASK_COUNT+task.getCsNick(),12*60,TimeUnit.MINUTES);
                //分配的任务标示，原先的flag更改为false
                redisOperator.put(JwtConstants.TOKEN_SESSION_NEW_TASK_FLAG+task.getCsNick(), "taskFlag", false);
            }

        }
        result.put("isNewTaskFlag",newFlag);
        result.put("count",count);
        result.put("sumCount",sumCount );
        if(logger.isDebugEnabled()){
            if(newFlag){
                logger.debug("web csNick ：{} 查询任务分配数量走dao层耗时：{}ms ",csNick,(System.currentTimeMillis()-s1));
            }else{
                logger.debug("web csNick ：{} 查询任务分配数量走redis耗时：{}ms ",csNick,(System.currentTimeMillis()-s1));
            }

        }
        return result;
    }


}
