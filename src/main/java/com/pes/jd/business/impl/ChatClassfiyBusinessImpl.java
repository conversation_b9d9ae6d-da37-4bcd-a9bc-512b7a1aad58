package com.pes.jd.business.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.ChatClassfiyBusiness;
import com.pes.jd.config.PersistentRoutingDataSource;
import com.pes.jd.dao.ChatClassifyDao;
import com.pes.jd.dao.CsChatSessionDao;
import com.pes.jd.dao.CsChatlogDao;
import com.pes.jd.dao.CsChatpeerDao;
import com.pes.jd.data.api.RemoteService;
import com.pes.jd.model.DO.ChatClassifyDO;
import com.pes.jd.model.DTO.CsChatSessionDTO;
import com.pes.jd.model.DTO.CsChatlogDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.XmlParserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;

@Service
public class ChatClassfiyBusinessImpl implements ChatClassfiyBusiness {

    private static final Logger logger = LoggerFactory.getLogger(ChatClassfiyBusinessImpl.class);
    private static final int THREAD_POOL_SIZE = 15;


    private static final List<String> CATEGORY_LIST = Arrays.asList(
            "尺码推荐", "商品信息咨询", "物流方式", "换货问题", "问候类", "商品推荐",
            "商品价格咨询", "催退款", "下单问题", "订单咨询", "退货规则咨询", "催发货", "售后咨询",
            "优惠咨询", "商品品质问题", "运费险问题", "退款流程咨询", "退差价", "赠品问题",
            "快递问题", "补贴", "国补", "优惠咨询", "补差价", "转人工", "以旧换新", "安装方式", "返现", "其他"
    );

    @Resource
    private CsChatpeerDao csChatpeerDao;

    @Resource
    private CsChatSessionDao csChatSessionDao;
    @Resource
    private CsChatlogDao chatlogDao;
    @Resource
    private ChatClassifyDao chatClassifyDao;
    @Resource
    private RemoteService remoteService;

    //向量库召回token
    private Double rankScore = 0.58;

    @Override
    public void handleClassfiyAnalysis(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {

        String targetDataSource = determineDataSource(jobShop.getShop().getDb());
        Long shopId = jobShop.getShop().getShopId();

            List<CsChatSessionDTO> sessions;
            Date date = jobDate.getStartDate();
        if(isDelData){
            chatClassifyDao.delChatClassify(jobShop.getShop(),date);
        }
            sessions =  csChatSessionDao.searchAllByTime(jobDate.getStartDate(),jobDate.getEndDate(), shopId,jobShop.getShop().getSchemaId(),null);
            if (sessions.isEmpty()) {
                logger.info("No chat sessions found for shop {} on date {}", shopId, date);
                return;
            }

        //sessions = sessions.subList(0, 10);
        logger.info("Found {} chat sessions for shop {} on date {}", sessions.size(), shopId, date);


            ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);

            try {
                List<CompletableFuture<List<CsChatlogDTO>>> futures = new ArrayList<>();

                for (CsChatSessionDTO session : sessions) {
                    String sid = session.getSid();
                    logger.info("sid loop:{}", sid);
                    Long skuId = session.getSkuId();

                    if (sid != null && !sid.isEmpty()) {
                        // Create a CompletableFuture for each session
                        CompletableFuture<List<CsChatlogDTO>> future = CompletableFuture.supplyAsync(() -> {
                            try{

                                PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey(targetDataSource);
                                String chatPairs;
                                List<CsChatlogDTO> chatLogs;
                                logger.debug("sid:{}", sid);
                                chatLogs =  chatlogDao.selectShopCsChatLogLstByShopIdAndSid(jobShop.getShop(),jobDate.getStartDate(),jobDate.getEndDate(),sid);
                                chatPairs = this.processLogsToJsonl(chatLogs);
                                try {
                                    if (!chatPairs.isEmpty()) {
                                        logger.debug("Processing chat pairs for session {}, length: {} characters", sid, chatPairs.length());
                                        processChatJsonl(chatPairs, jobShop.getShop(), sid, skuId, LocalDateTimeUtil.of(date));
                                    } else {
                                        logger.warn("No chat pairs to process for session {}", sid);
                                    }
                                } catch (Exception e) {
                                    logger.error("Error processing chat pairs: {}", e.getMessage(), e);
                                }
                                return chatLogs;
                            }finally {
                                PersistentRoutingDataSource.DataSourceKeyHolder.clear();
                            }

                        }, executor);

                        futures.add(future);
                    }
                }

                // Wait for all futures to complete with a timeout
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                        futures.toArray(new CompletableFuture[0]));

                try {
                    allFutures.get(30, TimeUnit.MINUTES);
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    logger.error("Error waiting for chat processing tasks: {}", e.getMessage(), e);
                    throw new RuntimeException("Failed to complete chat processing tasks", e);
                }

                // Process classifications as before

                long s51 = System.currentTimeMillis();
                processClassifications(jobShop.getShop(), shopId, date);
                long e51 = System.currentTimeMillis();
                logger.info("知识库优化耗费时间{}", e51 - s51);


            } finally {
                // Shutdown the executor service
                executor.shutdown();
                try {
                    if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                        executor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }


    }



    /**
     * Process chat logs to create JSONL formatted string of customer-service interactions
     *
     * @param chatLogs List of chat logs to process
     * @return JSONL formatted string
     */
    private String processLogsToJsonl(List<CsChatlogDTO> chatLogs) {
        StringBuilder jsonlBuilder = new StringBuilder();
        boolean hasCustomerContent = false;
        for (CsChatlogDTO log : chatLogs) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", log.getId().toString());
            jsonObject.put("content", log.getContent());
            // Set direction based on the direction value
            if (log.getDirection() == 1) {
                // Skip if content is empty
                if (log.getContent() == null || log.getContent().trim().isEmpty()) {
                    continue;
                }
                hasCustomerContent = true;
                jsonObject.put("direction", "customer");
            } else {
                jsonObject.put("direction", "customer_service");
            }
            // Add the JSON object as a line in the JSONL format
            if (hasCustomerContent) {
                jsonlBuilder.append(jsonObject.toJSONString()).append("\n");
            }
        }

        return jsonlBuilder.toString();
    }



    /**
     * Process JSONL data with AI service to classify chat content
     *
     * @param jsonlData JSONL formatted chat data
     * @param sid the session ID
     */
    private void processChatJsonl(String jsonlData, JobShopDTO shop, String sid, Long skuId, LocalDateTime date) {
        // Call the AI service with the JSONL data
        //String aiResponse = remoteService.callChatCompletions(String.join(",", CATEGORY_LIST), jsonlData);
        //使用前缀缓存
        String aiResponse = remoteService.callContextChatCompletions(String.join(",", CATEGORY_LIST), jsonlData);

        try {
            JSONObject jsonResponse = null;
            try{
                 jsonResponse = JSONObject.parseObject(aiResponse);
            }
            catch (Exception e){
                logger.error("ai回复格式错误:\n回答内容:\n {} \n分析内容:\n {} \n",aiResponse ,jsonlData);
            }
            String responseText = jsonResponse.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            Integer totalToken = jsonResponse.getJSONObject("usage").getInteger("total_tokens");
            logger.debug("JSONL data for chat session: {}", jsonlData.substring(0, Math.min(1000, jsonlData.length())) + "...");
            logger.debug("Result of knowledge (raw): {}", responseText);

            // Parse the XML response
            try {
                List<Map<String, String>> results = XmlParserUtils.extractResults(responseText);
                logger.info("Extracted {} results from XML response", results.size());

                for (Map<String, String> result : results) {
                    String chatlogId = result.get("chatlog_id");
                    String custContent = result.get("cust_content");
                    String csContent = result.get("cs_content");
                    String tag = result.get("tag");
                    String tagDesc = result.get("tag_desc");

                    if (chatlogId != null && tag != null) {
                        logger.info("Processing result: chatlog_id={}, tag={}, tag_desc={}", chatlogId, tag, tagDesc);


                        ChatClassifyDO chatClassify = ChatClassifyDO.builder()
                                .shopId(shop.getShopId())
                                .date(date.toLocalDate())
                                .sid(sid)
                                .chatlogId(Long.parseLong(chatlogId))
                                .classify(tag)
                                .classifyExtra(tagDesc)
                                .buyerContent(custContent != null ? custContent : "")
                                .csContent(csContent != null ? csContent : "")
                                .skuId(skuId)
                                .totalTokens(totalToken)
                                .created(LocalDateTime.now())
                                .build();


                        chatClassifyDao.insertChatClassify(shop,DateUtil.date(date),chatClassify);
                    }
                }
            } catch (Exception e) {
                logger.error("Error parsing XML response: {}", e.getMessage(), e);
            }
        } catch (Exception e) {
            logger.error("Error processing JSONL data: {}", e.getMessage(), e);
        }
    }



    private void processClassifications(JobShopDTO shop, Long shopId, Date date) {

        try {
            logger.info("开始从知识库优化分类");
            for (String category : CATEGORY_LIST) {
                logger.debug("Processing classifications with category: {}", category);

                List<ChatClassifyDO> classifyEntries = chatClassifyDao.findByShopIdAndClassify(shop, date, category);

                if (classifyEntries.isEmpty()) {
                    continue;
                }

                logger.debug("Found {} classifications for shop {} on date {} with category {}",
                        classifyEntries.size(), shopId, date, category);

                for (ChatClassifyDO entry : classifyEntries) {

                    try {

                        Map<String, Object> classify = remoteService.searchDocByName(
                                "Classify", entry.getClassify(), entry.getClassify(), entry.getClassifyExtra());

                        if (classify != null && classify.containsKey("results")) {
                            List<Map<String, Object>> results = (List<Map<String, Object>>) classify.get("results");
                            if (!results.isEmpty()) {
                                Map<String, Object> firstResult = results.get(0);
                                double score = 0.0;
                                if (firstResult.containsKey("score")) {
                                    score = Double.parseDouble(firstResult.get("score").toString());
                                }
                                if (score > rankScore) {
                                    if (firstResult.containsKey("table_chunk_fields")) {
                                        List<Map<String, Object>> fields = (List<Map<String, Object>>) firstResult.get("table_chunk_fields");
                                        for (Map<String, Object> field : fields) {
                                            if ("value".equals(field.get("field_name"))) {
                                               String value = (String) field.get("field_value");
                                                entry.setClassifyExtra(value);
                                                if(! value.equals(entry.getClassifyExtra() )){   //召回分数过低
                                                    chatClassifyDao.updateById(shop,entry,date);
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    if (firstResult.containsKey("table_chunk_fields")) {
                                        List<Map<String, Object>> fields = (List<Map<String, Object>>) firstResult.get("table_chunk_fields");
                                        for (Map<String, Object> field : fields) {
                                            if ("value".equals(field.get("field_name"))) {
                                                String value = (String) field.get("field_value");
                                                entry.setClassifyExtra(value);
                                                if(! value.equals(entry.getClassifyExtra() )){   //召回分数过低
                                                    chatClassifyDao.updateById(shop,entry,date);
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                addPointToDocument(classify, entry, category);
                            }
                        }
                        logger.debug("Processed classification for session {}, category: {}",
                                entry.getSid(), category);

                    } catch (Exception e) {
                        logger.error("Error processing classification {}: {}",
                                entry.getId(), e.getMessage(), e);
                    }
                }
                logger.info("{}处理完毕",category);
            }

            logger.info("Completed processing classifications for shop {} on date {}", shopId, date);
        } catch (Exception e) {
            logger.error("Error in processClassifications: {}", e.getMessage(), e);
        }
    }



    private void addPointToDocument(Map<String, Object> classify, ChatClassifyDO entry, String category) {
        try {

            if (classify.containsKey("doc_id")) {
                String docId = classify.get("doc_id").toString();
                logger.debug("Found document ID: {} for category {}", docId, category);

                Map<String, Object> addPointResult = remoteService.addPointToDoc(
                        "Classify", docId, entry.getClassify(), entry.getClassifyExtra(), "default");

                logger.info("Added new point to document for category {}: {}",
                        category, addPointResult);
            } else {
                logger.warn("No document ID found in search results for category {}", category);
            }
        } catch (Exception ex) {
            logger.error("Failed to add point to document for category {}: {}",
                    category, ex.getMessage(), ex);
        }
    }


    private String determineDataSource(String db) {
        if (db.contains("1")) {
            return "db1";
        } else if (db.contains("2")) {
            return "db2";
        }
        return "default"; // 默认数据源
    }


}
