package com.pes.jd.business.impl;


import com.pes.jd.business.CsPerformanceBusiness;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.dao.CsPerformanceDao;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.VO.CsPerformanceVo;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.beans.FeatureDescriptor;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/3 3:47 PM
 * @since 1.0.0
 */
@Service
public class CsPerformanceBusinessImpl implements CsPerformanceBusiness {

    @Resource
    private CsPerformanceDao csPerformanceDao;

    @Resource
    private ShopManageBusiness shopManageBusiness;

    private final static Logger LOGGER = LoggerFactory.getLogger(CsPerformanceBusinessImpl.class);

    private final static ThreadLocal<ContextService<String,Object>> PERFORMANCE_CAL_CONTEXT =
            ThreadLocal.withInitial(ContextService::new);

    /**
     * get Field Value Method  -> str Write Method
     */
    private final List<VoSet> METHOD_VO_STR_SET = new ArrayList<>();

    {
        Class<CsPerformanceVo> clazz = CsPerformanceVo.class;
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(clazz);
        Map<String, PropertyDescriptor> collect =
                Arrays.stream(propertyDescriptors).collect(Collectors.toMap(FeatureDescriptor::getDisplayName, v -> v, (x, y) -> {
            throw new RuntimeException();
        }));
        PropertyDescriptor[] strMethods = Arrays.stream(propertyDescriptors).filter(
                k -> k.getDisplayName().endsWith("Str")).toArray(PropertyDescriptor[]::new);
        for (PropertyDescriptor strMethod : strMethods) {
            String displayName = strMethod.getDisplayName();
            PropertyDescriptor propertyDescriptor = collect.get(displayName.substring(0, displayName.length() - 3));
            VoSet vo = new VoSet(
                    strMethod.getReadMethod(),
                    strMethod.getWriteMethod(),
                    propertyDescriptor.getReadMethod());
            METHOD_VO_STR_SET.add(vo);

        }
    }

    @Override
    public List<CsPerformanceVo> searchClientServicePerformance(String properties, Date startDate, Date endDate, Long shopId, List<String> nicks, Integer queryType) {
        before(properties, startDate, endDate, shopId, nicks);
        final Integer csDimension = 1;
        final Integer dateDimension = 2;
        String dimension;
        if (Objects.equals(csDimension,queryType)){
            dimension = "cs_nick";
        }else if (Objects.equals(dateDimension,queryType)){
            dimension = "date";
        }else {
            throw new IllegalArgumentException(" error query type ");
        }
        return doCsPerformanceService(startDate, endDate, shopId, nicks,dimension);
    }

    private void before(String properties, Date startDate, Date endDate, Long shopId, List<String> nicks) {
        ContextService<String, Object> contextService = PERFORMANCE_CAL_CONTEXT.get();
        contextService.put("properties",properties);
        contextService.put("startDate",startDate);
        contextService.put("endDate",endDate);
        contextService.put("shopId",shopId);
        contextService.put("nicks",nicks);
        contextService.put("tableName","pes_cs_performance");
        // 获取所有的配置
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        contextService.put("shopQuery",jobShop);

        ShopSystemsettingDTO shopSystemsetting = jobShop.getShopSystemsetting();
        // 询单有效时长 单位/天
        contextService.put("enquiryValidDurationTime", shopSystemsetting.getEnquiryValidDurationTime());
        // 出库有效市场 单位/天
        contextService.put("outStockValidDurationTime", shopSystemsetting.getOutStockValidDurationTime());
        // TODO 值班记录
        List<CsDutyRecordDTO> csDutyRecordLst = new ArrayList<>();
        contextService.put("CsDutyRecordLst",csDutyRecordLst);
        // TODO 服务评价
        List<CsServiceEvaluationDTO> csServiceEvaluationLst = new ArrayList<>();
        contextService.put("csServiceEvaluationLst",csServiceEvaluationLst);
        Map<EntryService, CsServiceEvaluationDTO> csServiceEvaluationMap = csServiceEvaluationLst.stream().collect(Collectors.toMap(
                k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                v -> v,
                (x, y) -> {
                    LOGGER.warn(" 重复的key ");
                    return x;
                }
        ));
        contextService.put("csServiceEvaluationMap",csServiceEvaluationMap);
        // TODO 退款数据
        List<CsRefundDayDTO> csRefundDayS = new ArrayList<>();
        contextService.put("csRefundDayS",csRefundDayS);
        Map<EntryService, CsRefundDayDTO> csRefundDayMap = csRefundDayS.stream().collect(Collectors.toMap(
                k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                v -> v,
                (x, y) -> {
                    LOGGER.warn(" 重复的key ");
                    return x;
                }
        ));
        contextService.put("csRefundDayMap",csRefundDayMap);
        // TODO 流失分析
        List<LossEnquiryRecordDTO> csLossRecordDTO = new ArrayList<>();
        contextService.put("csLossRecordDTO",csLossRecordDTO);
        LinkedMultiValueMap<EntryService, LossEnquiryRecordDTO> csLossRecordMap = new LinkedMultiValueMap();
        csLossRecordDTO.forEach(e-> csLossRecordMap.add(
                EntryService.of(e.getShopId(),e.getCsNick(),e.getDate()),
                e
        ));
        contextService.put("csLossRecordMap",csLossRecordMap);
        // TODO 订单绑定
        List<CsOrderBindDTO> csOrderBindLst = new ArrayList<>();
        contextService.put("csOrderBindLst",csOrderBindLst);

    }
    private List<CsPerformanceVo> doCsPerformanceService(
            Date startDate, Date endDate, Long shopId, List<String> nicks,String dimension){
        // 客服绩效 基础数据 -> 一个人或一天为一条数据
        List<CsPerformanceDTO> csPerformance =
                getCsPerformance(startDate, endDate, shopId, nicks,dimension);
        // 团队 平均数据 -> 查询出来的整个团队
        ContextService<String,Object> countOrAvgData =
                getCountOrAvgData(getCsPerformance(startDate, endDate, shopId,dimension));

        return fillData(csPerformance,countOrAvgData);
    }

    @SuppressWarnings("AlibabaMethodTooLong")
    private List<CsPerformanceVo> fillData(
            List<CsPerformanceDTO> csPerformance, ContextService<String, Object> countOrAvgData) {

        final ContextService<String, Object> contextService = PERFORMANCE_CAL_CONTEXT.get();
        final Date startDate = contextService.getDate("startDate");
        final Date endDate = contextService.getDate("endDate");
        final String validStr = "延迟计算";
        // 询单有效时长
        final Integer enquiryValidDurationTime = contextService.getInteger("enquiryValidDurationTime");
        // 出库有效时长
        final Integer outStockValidDurationTime = contextService.getInteger("outStockValidDurationTime");
        // 团队销售额
        final Double teamSaleAmount = countOrAvgData.getDouble("teamSaleAmount");
        // 团队出库金额
        final Double teamOutStockAmount = countOrAvgData.getDouble("teamOutStockAmount");
        // 获取满意率
        final Map<EntryService, CsServiceEvaluationDTO> serviceEvaluation = contextService
                .getEntryServiceCsServiceEvaluationDTOMap("csServiceEvaluationMap");
        // 获取退款
        final Map<EntryService, CsRefundDayDTO> csRefundDayMap = contextService
                .getEntryServiceCsRefundDayDTOMap("csRefundDayMap");
        // 获取流失数据
        final LinkedMultiValueMap<EntryService, LossEnquiryRecordDTO> csLossRecordMap = contextService
                .getEntryServiceLossRecordDTOMap("csLossRecordMap");
        // 客服绑定数据
        final List<CsOrderBindDTO> csOrderBindLst = contextService.getCsOrderBindDTOList("csOrderBindLst");

        //  copy 属性 忽略字段 （ 满意率,退款数据 ）
        final String[] copyIgnore = {
                "shopId","date","csNick"
        };
        csPerformance.forEach(e->{

            CsPerformanceVo vo = new CsPerformanceVo();
            setterDefault(vo);
            // copy 绩效属性
            BeanUtils.copyProperties(e,vo);

            final Long shopId = vo.getShopId();
            final String csNick = vo.getCsNick();
            final Date date = vo.getDate();
            EntryService entry = EntryService.of(
                     shopId, csNick, date
            );

            // 满意率
            final CsServiceEvaluationDTO csServiceEvaluation = serviceEvaluation.get(entry);
            // 退款数据
            final CsRefundDayDTO csRefundDay = csRefundDayMap.get(entry);
            // 流失数据
            final List<LossEnquiryRecordDTO> LossEnquiryRecordDTO = csLossRecordMap.get(entry);


            LossEnquiryRecordDTO.forEach(loss->{

            });

            // copy 满意率字段
            BeanUtils.copyProperties(csServiceEvaluation,vo,copyIgnore);
            // copy 退款数据
            BeanUtils.copyProperties(csRefundDay,vo,copyIgnore);


            csOrderBindLst.forEach(element->{

            });

            // 销售额
            final Double saleAmount = BaseUtils.getNonNull(vo.getSaleAmount());
            // 销售人数
            final Integer saleBuyerNum = BaseUtils.getNonNull(vo.getSaleBuyerNum());
            // 客服销售量
            final Integer saleGoodsNum = BaseUtils.getNonNull(vo.getSaleGoodsNum());
            // 出库人数
            final Integer outStockNum = BaseUtils.getNonNull(vo.getOutStockNum());
            // 出库订单数
            final Integer outStockOrderNum = BaseUtils.getNonNull(vo.getOutStockOrderNum());
            // 出库金额
            final Double outStockAmount = BaseUtils.getNonNull(vo.getOutStockAmount());
            // 出库件数
            final Integer outStockGoodsNum = BaseUtils.getNonNull(vo.getOutStockGoodsNum());
            // 询单→当日下单人数
            final Integer orderedNumToday = BaseUtils.getNonNull(vo.getOrderedNumToday());
            // 询单→当日下单金额
            final Double orderedAmountToday = BaseUtils.getNonNull(vo.getOrderedAmountToday());
            // 询单→当日的下单件数
            final Integer orderedGoodsNumToday = BaseUtils.getNonNull(vo.getOrderedGoodsNumToday());
            // 询单人数
            final Integer enquiryNum = BaseUtils.getNonNull(vo.getEnquiryNum());
            // 当日咨询，当日下单，当日或次日付款的人数
            final Integer paidNumTodayNext = BaseUtils.getNonNull(vo.getPaidNumTodayNext());
            // 当日咨询，最终落实付款的人数
            final Integer paidNumFinal = BaseUtils.getNonNull(vo.getPaidNumFinal());
            // 当日咨询 最终落实下单的人数
            final Integer orderedNumFinal = BaseUtils.getNonNull(vo.getOrderedNumFinal());
            // 当日询单 当天落实下单 并且 付了款的人数
            final Integer paidNumToday = BaseUtils.getNonNull(vo.getPaidNumToday());
            // 退款笔数
            final Integer completedRefundNum = BaseUtils.getNonNull(vo.getCompletedRefundNum());
            // 成交笔数
            final Integer saleOrderSkuNum = BaseUtils.getNonNull(vo.getSaleOrderSkuNum());

            final boolean oneDayFlag = DateUtils.afterBalanceTime(endDate,DateUtils.now(),1,TimeUnit.DAYS);
            final boolean twoDayFlag = DateUtils.afterBalanceTime(endDate,DateUtils.now(),2,TimeUnit.DAYS);
            final boolean enquiryNumFlag = DateUtils.afterBalanceTime(endDate,DateUtils.now(),enquiryValidDurationTime, TimeUnit.DAYS);
            // 询单有效时长+出库有效时长+1
            final boolean outStockEnquiryFlag = DateUtils.afterBalanceTime(endDate,DateUtils.now(),outStockValidDurationTime+enquiryValidDurationTime+1, TimeUnit.DAYS);
            final boolean outStockFlag = DateUtils.afterBalanceTime(endDate,DateUtils.now(),outStockValidDurationTime, TimeUnit.DAYS);


            // ===================================== { 销售数据 } ========================================
            // 个人销售占比
            BaseUtils.setFlagStr(teamSaleAmount,vo,"setSaleAmountPercentStr");
            if (teamSaleAmount != 0) {
                vo.setSaleAmountPercent(saleAmount / teamSaleAmount);
                vo.setSaleAmountPercentStr(BaseUtils.percent(vo.getSaleAmountPercent()));
            }
            // ===================================== { 客单价 } ========================================

            //销售客单价（元/件） 本客服客单价=本客服销售额/本客服销售人数
            BaseUtils.setFlagStr(saleBuyerNum,vo,"setSaleGuestAvgAmountStr");
            if (saleBuyerNum != 0) {
                vo.setSaleGuestAvgAmount(saleAmount / saleBuyerNum);
            }
            //销售客件数（件/人） 本客服客件数=本客服销售量/本客服销售人数
            BaseUtils.setFlagStr(saleBuyerNum,vo,"setSaleGuestAvgGoodsStr");
            if (saleBuyerNum != 0) {
                vo.setSaleGuestAvgGoods(saleGoodsNum / saleBuyerNum);
            }
            //销售件均价（元/件） 本客服件均价=本客服销售额/本客服销售量
            BaseUtils.setFlagStr(saleGoodsNum,vo,"setSaleGoodsAvgAmountStr");
            if (saleGoodsNum != 0){
                vo.setSaleGoodsAvgAmount(saleAmount / saleGoodsNum);
            }
            // 本客服出库客单价=本客服促成出库金额/促成出库人数
            BaseUtils.setFlagStr(outStockNum,vo,"setOutStockGuestAvgAmountStr");
            if (outStockNum != 0){
                vo.setOutStockGuestAvgAmount(outStockAmount / outStockNum);
            }
            //出库客件数（件/人） 本客服出库客件数=本客服促成出库件数/促成出库人数
            BaseUtils.setFlagStr(outStockNum,vo,"setOutStockGuestItemNumStr");
            if (outStockNum != 0){
                vo.setOutStockGuestItemNum(outStockGoodsNum / outStockNum);
            }
            // 本客服出库件均价=本客服促成出库金额/促成出库件数
            BaseUtils.setFlagStr(outStockGoodsNum,vo,"setOutStockItemAvgAmountStr");
            if (outStockGoodsNum != 0){
                vo.setOutStockItemAvgAmount(outStockAmount / outStockGoodsNum);
            }
            // 下单件均价（元/件）客服促成下单金额/下单件数
            BaseUtils.setFlagStr(orderedGoodsNumToday,vo,"setOrderItemAvgAmountStr");
            if (orderedGoodsNumToday != 0){
                vo.setOrderItemAvgAmount(orderedAmountToday / orderedGoodsNumToday);
            }
            // 下单客单价（元/人） 客服促成下单金额/下单人数
            BaseUtils.setFlagStr(orderedNumToday,vo,"setOrderedGuestAvgPriceStr");
            if (orderedNumToday != 0){
                vo.setOrderedGuestAvgPrice(orderedAmountToday / orderedNumToday);
            }
            // 下单客件数（件/人） 客服促成下单件数/下单人数
            BaseUtils.setFlagStr(orderedNumToday,vo,"setOrderedGuestAvgAmountStr");
            if (orderedNumToday != 0){
                vo.setOrderedGuestAvgAmount(orderedGoodsNumToday * 1.0 / orderedNumToday);
            }

            // ===================================== { 转化率 } ========================================

            // 询单→次日付款转化率 询单→次日付款转化率=当日或次日付款人数/询单人数（当前询单有效时长配置为*天，该数据须延迟*天统计，当前可查看*年*月*日（含）的数据）
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToTomorrowStr");
            if (enquiryNum != 0){
                vo.setQueryToTomorrow(paidNumTodayNext * 1.0 / enquiryNum);
                vo.setQueryToTomorrowStr(BaseUtils.percent(vo.getQueryToTomorrow()));
            }
            // 询单→最终付款转化率
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToFinalPaidStr");
            if (enquiryNum != 0){
                vo.setQueryToFinalPaid(paidNumFinal * 1.0 / enquiryNum);
                vo.setQueryToFinalPaidStr(BaseUtils.percent(vo.getQueryToFinalPaid()));
            }
            // 询单→当日下单转化率  orderedNumToday
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToOrderedTodayStr");
            if (enquiryNum != 0){
                vo.setQueryToOrderedToday(orderedNumToday * 1.0 / enquiryNum);
                vo.setQueryToOrderedTodayStr(BaseUtils.percent(vo.getQueryToOrderedToday()));
            }
            // 询单→最终下单转化率 orderedNumFinal
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToFinalPaidStr");
            if (enquiryNum != 0){
                vo.setQueryToFinalPaid(orderedNumFinal * 1.0 / enquiryNum);
                vo.setQueryToFinalPaidStr(BaseUtils.percent(vo.getQueryToFinalPaid()));
            }
            // 下单→当日付款转化率 paidNumToday orderedNumToday
            BaseUtils.setFlagStr(orderedNumToday,vo,"setOrderedToPaidStr");
            if (orderedNumToday != 0){
                vo.setOrderedToPaid(paidNumToday * 1.0 / orderedNumToday);
                vo.setOrderedToPaidStr(BaseUtils.percent(vo.getOrderedToPaid()));
            }
            // 下单→最终付款转化率
            BaseUtils.setFlagStr(orderedNumToday,vo,"setOrderedToPaidFinalStr");
            if (orderedNumToday != 0){
                vo.setOrderedToPaidFinal(paidNumFinal * 1.0 / orderedNumToday);
                vo.setOrderedToPaidFinalStr(BaseUtils.percent(vo.getOrderedToPaidFinal()));
            }
            if (oneDayFlag){
                vo.setOrderedToPaidFinalStr(validStr);
            }
            // 询单→当日下单转化率  当日下单人数/询单人数 orderedNumToday/enquiryNum
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToOrderedTodayStr");
            if (enquiryNum != 0){
                vo.setQueryToOrderedToday(orderedNumToday * 1.0 / enquiryNum);
                vo.setQueryToOrderedTodayStr(BaseUtils.percent(vo.getQueryToOrderedToday()));
            }
            // 询单→最终下单转化率 最终下单人数/询单人数  orderedNumFinal/enquiryNum
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToFinalOrderedStr");
            if (enquiryNum != 0){
                vo.setQueryToFinalOrdered(orderedNumFinal * 1.0 / enquiryNum);
                vo.setQueryToFinalOrderedStr(BaseUtils.percent(vo.getQueryToFinalOrdered()));
            }
            // 下单→当日付款转化率 当日付款人数/下单人数  paidNumToday/orderedNumToday
            BaseUtils.setFlagStr(orderedNumToday,vo,"setOrderedToPaidStr");
            if (orderedNumToday != 0){
                vo.setOrderedToPaid(paidNumToday * 1.0 / orderedNumToday);
                vo.setOrderedToPaidStr(BaseUtils.percent(vo.getOrderedToPaid()));
            }
            // 下单→最终付款转化率 orderedToPaidFinal 最终付款人数/下单人数  paidNumFinal/orderedNumToday
            BaseUtils.setFlagStr(orderedNumToday,vo,"setOrderedToPaidFinalStr");
            if (orderedNumToday != 0){
                vo.setOrderedToPaidFinal(paidNumFinal * 1.0 / orderedNumToday);
                vo.setOrderedToPaidFinalStr(BaseUtils.percent(vo.getOrderedToPaidFinal()));
            }
            // 询单→出库转化率 queryToOutStock  outStockNum/enquiryNum
            BaseUtils.setFlagStr(enquiryNum,vo,"setQueryToOutStockStr");
            if (enquiryNum != 0){
                vo.setQueryToOutStock(paidNumFinal * 1.0 / enquiryNum);
                vo.setQueryToOutStockStr(BaseUtils.percent(vo.getQueryToOutStock()));
            }
            if (outStockEnquiryFlag){
                vo.setQueryToOutStockStr(validStr);
            }


            // ===================================== { 下单→出库 } ========================================
            // TODO  暂时接口没给全

            // ===================================== { 客服落实付款 } ========================================
            // 件数 done
            // 订单数 done
            // 人数 saleBuyerNum done
            // 金额
            // TODO

            // ===================================== { 工作量 } ========================================
            // TODO

            // ===================================== { 协助服务 } ========================================
            // TODO

            // ===================================== { 出库数据 } ========================================
            // TODO 最终出库人数
            // 个人出库金额占比 personalOutStockAmountPercent D
            BaseUtils.setFlagStr(enquiryNum,vo,"setPersonalOutStockAmountPercentStr");
            if (teamOutStockAmount != 0){
                vo.setPersonalOutStockAmountPercent(outStockAmount * 1.0 / teamOutStockAmount);
                vo.setPersonalOutStockAmountPercentStr(BaseUtils.percent(vo.getPersonalOutStockAmountPercent()));
            }

            // ===================================== { 满意率 } ========================================
            // 邀评字段  TODO


            // ===================================== { 退款数据 } ========================================
            // 退款率 completedRefundNum/saleOrderSkuNum
            BaseUtils.setFlagStr(saleOrderSkuNum,vo,"setRefundPercentStr");
            if (saleOrderSkuNum != 0){
                vo.setRefundPercent(completedRefundNum * 1.0 / saleOrderSkuNum);
                vo.setRefundPercentStr(BaseUtils.percent(vo.getRefundPercent()));
            }

            // ===================================== { 流失数据 } ========================================
            // enquiryLossNum 询单流失人数


            /**
             * 询单有效时长
             */
            BaseUtils.setFlagString(enquiryNumFlag,vo,new String[]{
                "setEnquiryNumStr","setQueryToTomorrowStr","setQueryToFinalPaidStr",
                    "setQueryToOrderedTodayStr","setQueryToFinalPaidStr",
                    "setQueryToOrderedTodayStr","setQueryToFinalOrderedStr",
                    "orderedNumFinalStr","orderedAmountFinalStr"

            },validStr);

            /**
             * 延迟一天的flag 今天可以看前天的数据
             */
            BaseUtils.setFlagString(twoDayFlag,vo,new String[]{
                    "setOrderedToPaidFinalStr","paidNumTodayNextStr",
                    "setToOrderedPaidNumFinalStr","setToOrderedPaidGoodsFinalStr",
                    "setToOrderedPaidAmountFinalStr"
            },validStr);

            /**
             *  出库有效时长
             */
            BaseUtils.setFlagString(outStockFlag,vo,new String[]{

            },validStr);

            /**
             *  将所有 带有str后缀的方法检测是否为空，如果为null，那么使用源方法赋值(去掉Str后缀的方法)
             */
            doCheck(vo);
        });
        return null;
    }

    private void setterDefault(CsPerformanceVo vo) {
    }

    private void doCheck(Object bean){
        METHOD_VO_STR_SET.forEach(e->{
            e.change(bean);
        });
    }

    private ContextService<String, Object> getCountOrAvgData(List<CsPerformanceDTO> csPerformance) {
        ContextService<String, Object> result = new ContextService<>();
        // 团队销售额
        double teamSaleAmount = 0.0;
        // 团队出库金额
        double teamOutStockAmount = 0.0;
        for (CsPerformanceDTO e : csPerformance) {
            teamSaleAmount+=BaseUtils.getNonNull(e.getSaleAmount());
            teamOutStockAmount+=BaseUtils.getNonNull(e.getOutStockAmount());
        }
        result.put("teamSaleAmount",teamSaleAmount);
        result.put("teamOutStockAmount",teamOutStockAmount);
        return result;
    }

    /**
     *  获取客服数据  nicks可以为空，如果nick为空，查询整个店铺下的所有
     */
    private List<CsPerformanceDTO> getCsPerformance(
            Date startDate, Date endDate, Long shopId, List<String> nicks,String dimension){
        String tableName = PERFORMANCE_CAL_CONTEXT.get().getNonNullString("tableName");
        return csPerformanceDao.searchByDateShopCs(nicks, shopId, startDate, endDate,dimension,tableName);
    }
    private List<CsPerformanceDTO> getCsPerformance(
            Date startDate, Date endDate, Long shopId,String dimension){
        String tableName = PERFORMANCE_CAL_CONTEXT.get().getNonNullString("tableName");
        return csPerformanceDao.searchByDateShopCs(null, shopId, startDate, endDate,dimension,tableName);
    }

    private <K,V> LinkedMultiValueMap<K,V> getNonNull(LinkedMultiValueMap<K,V> d){
        if (d == null){
            return new LinkedMultiValueMap<>();
        }
        return d;
    }

    private <K> List<K> getNonNull(List<K> d){
        if (d == null){
            return new ArrayList<>();
        }
        return d;
    }

    private static class EntryService{

        private Long shopId;

        private String csNick;

        private Date date;

        static EntryService of(Long shopId, String csNick, Date date){
            return new EntryService(shopId,csNick,date);
        }

        EntryService(Long shopId, String csNick, Date date) {
            this.shopId = shopId;
            this.csNick = csNick;
            this.date = date;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            EntryService that = (EntryService) o;
            return Objects.equals(shopId, that.shopId) &&
                    Objects.equals(csNick, that.csNick) &&
                    Objects.equals(date, that.date);
        }

        @Override
        public int hashCode() {
            return Objects.hash(shopId, csNick, date);
        }
    }

    private static class VoSet{
        /**
         *  str get 方法
         */
        private Method valid;
        /**
         *  str set 方法
         */
        private Method str;
        /**
         *  source get 方法
         */
        private Method source;

        VoSet(Method valid, Method str, Method source) {
            ReflectionUtils.makeAccessible(valid);
            ReflectionUtils.makeAccessible(str);
            ReflectionUtils.makeAccessible(source);
            this.valid = valid;
            this.str = str;
            this.source = source;
        }

        void change(Object bean){
            try {
                doChange(bean);
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        private void doChange(Object bean) throws Exception{
            if (Objects.isNull(valid.invoke(bean))){
                str.invoke(bean,String.valueOf(source.invoke(bean)));
            }
        }

    }

    @SuppressWarnings("unchecked")
    private static class ContextService<K,V> extends BaseUtils.BaseMap<K,V>{

        Map<EntryService, CsServiceEvaluationDTO> getEntryServiceCsServiceEvaluationDTOMap(String key){
            V v = get(key);
            return (Map<EntryService, CsServiceEvaluationDTO>) v;
        }

        Map<EntryService, CsRefundDayDTO> getEntryServiceCsRefundDayDTOMap(String key){
            V v = get(key);
            return (Map<EntryService, CsRefundDayDTO>) v;
        }

        LinkedMultiValueMap<EntryService, LossEnquiryRecordDTO> getEntryServiceLossRecordDTOMap(String key){
            V v = get(key);
            return (LinkedMultiValueMap<EntryService, LossEnquiryRecordDTO>) v;
        }

        List<CsOrderBindDTO> getCsOrderBindDTOList(String key){
            V v = get(key);
            return (List<CsOrderBindDTO>) v;
        }

        LinkedMultiValueMap<EntryService,CsOrderBindDTO> getEntryServiceCsOrderBindDTO(String key){
            V v = get(key);
            return (LinkedMultiValueMap<EntryService,CsOrderBindDTO>) v;
        }

        LinkedMultiValueMap<Long,CsOrderBindDTO> getOrderCsOrderBindDTO(String key){
            V v = get(key);
            return (LinkedMultiValueMap<Long,CsOrderBindDTO>) v;
        }

    }

}
