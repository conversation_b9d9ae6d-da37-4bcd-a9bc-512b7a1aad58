package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.OrderBussiness;
import com.pes.jd.dao.OrderDao;
import com.pes.jd.dao.PresaleOrderDao;
import com.pes.jd.model.DTO.BuyerOrderDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtJobRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class OrderBussinessImpl implements OrderBussiness {

    @Resource
    private OrderDao orderDao;

    @Resource
    private PresaleOrderDao presaleOrderDao;

    @Resource
    private PopRtJobRestTemplate popRtJobRestTemplate;

    private static final String PULL_HISTORY_ORDER = "/task/message/pullHistoryOrder";

    @Override
    public List<String> selectShopOrderLstByBuyersAndDateForAfterSale(Long shopId, String schemaId,
                                                                     List<String> buyerLst, Date startDate,
                                                                     Date endDate) {

        Set<BuyerOrderDTO> buyerOrderSet = Sets.newHashSet();
        List<String> afterSaleBuyerSet = Lists.newArrayList();
        ValidDateRangeQuery vDate = new ValidDateRangeQuery();
        vDate.setStartDate(startDate);
        vDate.setEndDate(endDate);
        JobShopDTO jobShop = new JobShopDTO();
        jobShop.setShopId(shopId);
        jobShop.setSchemaId(schemaId);
        List<Long> presaleFilterOrderIds = Optional.ofNullable(orderDao.selectOrderDirectTradeId(jobShop, 1L, startDate, endDate))
                .orElse(new ArrayList<>(0));
        //查询预售付定金
        List<BuyerOrderDTO> presaleBargainPaidOrderLst =
                presaleOrderDao.selectShopBargainPaidOrderLstByBuyersAndDate(jobShop, buyerLst, vDate,new HashSet<>(presaleFilterOrderIds));

        if (CollectionUtils.isNotEmpty(presaleBargainPaidOrderLst)) {
            for (BuyerOrderDTO order : presaleBargainPaidOrderLst) {
                order.setPreSale(Boolean.TRUE);//预售
            }
            buyerOrderSet.addAll(presaleBargainPaidOrderLst);
        }

        //查询订单表
        List<BuyerOrderDTO> ordinaryBuyerOrderLst = orderDao.selectShopOrderLstByBuyersAndDateForAfterSale(jobShop,
                                                                                                           buyerLst,
                                                                                                           vDate);
        if (CollectionUtils.isNotEmpty(ordinaryBuyerOrderLst)) {
            buyerOrderSet.addAll(ordinaryBuyerOrderLst);
        }

        if (CollectionUtils.isNotEmpty(buyerOrderSet)) {
            for (BuyerOrderDTO order : buyerOrderSet) {
                if (order.getPreSale()) {
                    afterSaleBuyerSet.add(order.getBuyerNick());
                } else {
                    // 如果买家在售后期内有下单并付款（货到付款），则不计入询单人数
                    if ((order.getPayType() != null && order.getPayType() == 1) || order.getPayTime() != null) {
                        afterSaleBuyerSet.add(order.getBuyerNick());
                    }
                }

            }
        }

        return afterSaleBuyerSet;
    }

    @Override
    public void initHistoryOrderData(Long shopId, String startDateStr, String endDateStr, String rtDb, String rtSchemaId) {
        try {
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("shopId", shopId)
                    .put("startDateStr", startDateStr)
                    .put("endDateStr", endDateStr)
                    .put("initFlag", "true")
                    .toRequestEntity();
            String serviceId = RestOperator.getServiceId(rtDb, rtSchemaId, ApplicationServiceNameEnum.PROVIDER_RT_TASK.getName());
            popRtJobRestTemplate.postRest(serviceId, PULL_HISTORY_ORDER, body);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
