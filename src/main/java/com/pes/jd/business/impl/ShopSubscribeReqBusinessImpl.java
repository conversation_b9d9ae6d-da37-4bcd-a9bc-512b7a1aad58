package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopSubscribeReqBusiness;
import com.pes.jd.model.Param.SubscribeParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class ShopSubscribeReqBusinessImpl implements ShopSubscribeReqBusiness {

	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;
	
	@Override
	public ApiResponse selectShopSubscribe(SubscribeParam param){
		ApiResponse apiResponse = null;
		
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/selectShopSubscribeInfo", body);
		return apiResponse;

	}

}
