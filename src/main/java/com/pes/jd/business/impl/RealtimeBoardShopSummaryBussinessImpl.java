package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.RealtimeBoardBussiness;
import com.pes.jd.business.RealtimeBoardShopSummaryBussiness;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.VO.ShopConversionTaskAvgVO;
import com.pes.jd.ms.domain.Data.master.DeptShop;
import com.pes.jd.ms.domain.Data.master.ShopConversionTaskTotal;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

@Service
public class RealtimeBoardShopSummaryBussinessImpl implements RealtimeBoardShopSummaryBussiness {
	private static Logger logger = LoggerFactory.getLogger(RealtimeBoardShopSummaryBussinessImpl.class);
	@Resource
	private RealtimeBoardBussiness realtimeBoardBussiness;
	
	@Resource
	private ShopSysManagerBusinessImpl shopSysManagerBusinessImpl;
	
	@Override
	public Map<String, Object> selectShopCsUrgeInfo(String shopName,Long deptId ,CustConversionParam param, String id, String type) throws Exception{
		
		List<DeptShop> deptShopQueryLst=	shopSysManagerBusinessImpl.selectDeptShopInfoByDeptId(shopName,deptId,id,type);
		
		Map<String, Object> result=Maps.newHashMap();
		ShopConversionTaskTotal total=new ShopConversionTaskTotal();
		ShopConversionTaskAvgVO avg=new ShopConversionTaskAvgVO();
		List<ShopConversionTaskTotal> taskLst=Lists.newArrayList();
		result.put("total", total);
		result.put("avg", avg);
		result.put("result", taskLst);
		if(CollectionUtils.isEmpty(deptShopQueryLst)){
			return result;
		}
		 int unallocatedConvsNum=0;//待分配数
		 int allocatedConvsNum=0;//分配任务数
		 int executedConvsNum=0;//执行任务数
		 int convSucceedNum=0;//转化成功数
		 double saleAmount=0.0;//催付销售额
		 double notUrgeSaleAmount=0.0;//店铺内不属于催化的销售额
		 double shopSaleAmount=0.0;
		 int count=0;
		Map<String, List<DeptShop>> shopMap=deptShopQueryLst.stream().collect(Collectors.groupingBy(DeptShop::getRtDb));
		for (Entry<String, List<DeptShop>> deptShopEntry : shopMap.entrySet()) {
			String db=deptShopEntry.getKey();
			List<DeptShop> deptShopLst=deptShopEntry.getValue();
			if(CollectionUtils.isEmpty(deptShopLst)){
				continue;
			}
			Map<Long, String> deptShopMap=deptShopLst.stream().collect(Collectors.toMap(DeptShop::getShopId, c->c.getShopName()));
			RestApiResponse2<List<ShopConversionTaskTotal>> shopConverResp=	realtimeBoardBussiness.selectShopCsUrgeInfo(db, deptShopLst, param);
			List<ShopConversionTaskTotal> shopConverLst=null;
			if(shopConverResp.getSuccess()){
				shopConverLst=shopConverResp.getData().get("result");
				if(CollectionUtils.isEmpty(shopConverLst)){
					continue;
				}
				
				for (ShopConversionTaskTotal task : shopConverLst) {
					
					Boolean flagero=false;
					if(task==null){
						continue;
					}
					if(task.getUnallocatedConvsNum()>0){
						flagero=true;
					}
					if(task.getAllocatedConvsNum()>0){
						flagero=true;
					}
					if(task.getExecutedRate()>0.0){
						flagero=true;
					}
					if(task.getConvSucceedRate()>0.0){
						flagero=true;
					}
					if(task.getSaleAmount()>0.0){
						flagero=true;
					}
					if(task.getShopUrgeRate()>0.0){
						flagero=true;
					}
					if(flagero){
						count++;
					}
					if (StringUtils.isNotBlank(deptShopMap.get(task.getShopId()))) {
						task.setShopName(deptShopMap.get(task.getShopId()));
					}
					if (task.getUnallocatedConvsNum() > 0) {
						unallocatedConvsNum += task.getUnallocatedConvsNum();
					}
					if (task.getAllocatedConvsNum() > 0) {
						allocatedConvsNum += task.getAllocatedConvsNum();
					}
					if (task.getExecutedConvsNum() > 0) {
						executedConvsNum += task.getExecutedConvsNum();
					}
					if(task.getConvSucceedNum()>0){
						convSucceedNum+=task.getConvSucceedNum();
					}
					if (task.getSaleAmount() > 0.0) {
						saleAmount += task.getSaleAmount();
					}
					if (task.getShopSaleAmount() > 0.0) {
						shopSaleAmount+=task.getShopSaleAmount();
					}
					taskLst.add(task);
				}
			}
		}
		notUrgeSaleAmount=shopSaleAmount-saleAmount;
		total.setUnallocatedConvsNum(unallocatedConvsNum);
		total.setAllocatedConvsNum(allocatedConvsNum);
		total.setConvSucceedNum(convSucceedNum);
		total.setExecutedConvsNum(executedConvsNum);
		total.setSaleAmount(saleAmount);
		total.setShopNotUrgeSaleAmount(shopSaleAmount-saleAmount);
		total.setShopSaleAmount(shopSaleAmount);
		
		avg.setUnallocatedConvsNum(count>0 ?unallocatedConvsNum/(double) count:0.0);
		avg.setAllocatedConvsNum(count>0? allocatedConvsNum/(double) count:0.0);
		avg.setExecutedRate(allocatedConvsNum>0? executedConvsNum/(double) allocatedConvsNum *100.0:0.0);
		avg.setConvSucceedRate(allocatedConvsNum>0.0? convSucceedNum/(double)allocatedConvsNum*100.0:0.0);
		avg.setShopUrgeRate(notUrgeSaleAmount>0? saleAmount/(double)notUrgeSaleAmount*100.0:0.0);
		avg.setSaleAmount(count>0? saleAmount/(double)count:0.0);
		avg.setShopSaleAmount(count>0? shopSaleAmount/(double)count:0.0);
		logger.info("selectShopCsUrgeInfo count:{}",count);
		return result;
		
	}
}
