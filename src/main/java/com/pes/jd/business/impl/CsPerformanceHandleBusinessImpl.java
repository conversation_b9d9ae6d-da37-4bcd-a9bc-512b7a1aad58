
package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.Constants.GoodFilterEnum;
import com.pes.jd.Constants.PesConstants;
import com.pes.jd.business.CsPerformanceHandleBusiness;
import com.pes.jd.business.GoodsHandleBusiness;
import com.pes.jd.business.PerformanceRuleBusiness;
import com.pes.jd.dao.*;
import com.pes.jd.model.BO.*;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.JudgeRuleAscriptionEnum;
import com.pes.jd.model.Enum.JudgeRuleEnum;
import com.pes.jd.model.Enum.JudgeRuleTypeEnum;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.*;
import com.yiyitech.support.task.AsyncTask;
import com.yiyitech.support.task.AsyncTaskUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.pes.jd.util.ServiceDateUtil.calNewOrderIndexDate;

/**
 * 客服绩效 - 业务类
 *
 * <AUTHOR>
 */
@Service
public class CsPerformanceHandleBusinessImpl implements CsPerformanceHandleBusiness {
    private static final Logger logger = LoggerFactory.getLogger(CsPerformanceHandleBusinessImpl.class);
    private static final int CREATED_ORDER_CS=1;//落实下单
    private static final int PAY_ORDER_CS=2; //落实付款
    private static final int SILENT_ORDER_CS=3; //静默订单
    @Resource
    private CsChatlogDao chatlogDao;
    @Resource
    private CsChatpeerDao csChatpeerDao;
    @Resource
    private CsOrderIndexDao csOrderIndexDao;
    @Resource
    private OrderDao orderDao;
    @Resource
    private PresaleOrderDao presaleOrderDao;
    @Resource
    private OrderFilterDao orderFilterDao;
    @Resource
    private CsPerformanceDao csPerformanceDao;
    @Resource
    private CsTOrderPerformanceDao csTOrderPerformanceDao;
    @Resource
    private OrderAssociatedDao orderAssociatedDao;
    @Resource
    private CsOrderBindDao csOrderBindDao;
    @Resource
    private PerformanceRuleBusiness performanceRuleBusiness;
    @Resource
    private ShopTeamPerformanceDao shopTeamPerformanceDao;
    @Resource
    private ShopTeamTOrderPerformanceDao shopTeamTOrderPerformanceDao;
    @Resource
    private OrderRemarkDao orderRemarkDao;
    @Resource
    private GoodsHandleBusiness goodsHandleBusiness;

    /**
     * 客服绩效计算
     *
     * @param isDelData
     */
    @Override
    public void handleShopCsPerformance(JobShopQuery jobShop,
                                        JobDateQuery jobDate, boolean isDelData) {

        long s = System.currentTimeMillis();

        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        List<Date> dates = jobDate.getCsPerformanceDates();
        if (CollectionUtils.isEmpty(dates)) {
            logger.warn("req dates is empty");
            return;
        }


        for (Date date : dates) {

            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_ALL, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }

            List<CsPerformanceDO> csDayPerformanceLst = Lists.newArrayListWithCapacity(targetCsLst.size());

            PerformanceBO performanceBO = new PerformanceBO();
            ShopTeamPerformanceDataBO shopTeamPerformanceDataBO = new ShopTeamPerformanceDataBO(shopId, date);
            performanceBO.setShopTeamPerformanceDataBO(shopTeamPerformanceDataBO);

            ShopTeamPerformanceDO shopTeamPerformance = new ShopTeamPerformanceDO(shopId, date);
            performanceBO.setShopTeamPerformance(shopTeamPerformance);
            initShopTeamPerformance(shopTeamPerformance);

            String csNick;
            for (CsDTO cs : targetCsLst) {
                csNick = cs.getNick();

                CsPerformanceDO csPerformance = new CsPerformanceDO(shopId, date, csNick);
                initCsPerformance(csPerformance);
                performanceBO.setCsPerformance(csPerformance);
                csDayPerformanceLst.add(csPerformance);


                /*
                 * 计算接待指标数据
                 * （未考虑预售）
                 */
                Set<String> enquiryBuyerSet = assembledCurrentDayReceiveServiceDataForEnquiry(jobShop, date, csNick, performanceBO);

                if (cs.getType() == 1) {
                    /*
                     * 询单维度：计算当日数据
                     * （未考虑预售）
                     */
                    this.assembledCurrentDayOrderedDataForEnquiry(jobShop, date, csNick, performanceBO, enquiryBuyerSet);

                    /*
                     * 询单维度：计算询单下单最终数据 - 推询单有效时长
                     * （未考虑预售）
                     */
                    this.assembledFinalOrderedDataForEnquiry(jobShop, date, csNick, performanceBO, enquiryBuyerSet);

                    /*
                     * 询单维度：计算询单下单付款后最终出库的数据
                     * （需要考虑预售付尾款的间隔时间，影响出库的时间）
                     */
                    this.assembledFinalOutStockDataForEnquiry(jobShop, date, csNick, performanceBO, enquiryBuyerSet);
                }


            }

            csPerformanceDao.deleteShopCsPerformanceByDate(shop, date);
            csPerformanceDao.insertTargetDateCsPerformances(shop, date, csDayPerformanceLst);

            /*
             * 封装店铺客服团队数据
             */
            assembledShopTeamPerformanceData(shopTeamPerformanceDataBO, shopTeamPerformance);

            shopTeamPerformanceDao.deleteShopTeamPerformance(shop, date);
            shopTeamPerformanceDao.insertShopTeamPerformance(shop, date, shopTeamPerformance);
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("handle shop CsPerformance end,time:{}s", (e - s) / 1000);
        }
    }


    private void assembledShopTeamPerformanceData(ShopTeamPerformanceDataBO bo,
                                                  ShopTeamPerformanceDO teamPes) {

        teamPes.setDirectReceiveNum(bo.getDirectReceiveNumSet().size());
        teamPes.setForwardInNum(bo.getForwardInNumSet().size());// 转入接待人数
        teamPes.setForwardOutNum(bo.getForwardOutNumSet().size());// 转出接待人数
        teamPes.setConsultNum(bo.getConsultNumSet().size());
        teamPes.setReceiveNum(bo.getReceiveNumSet().size());// 接待人数
        teamPes.setEnquiryNum(bo.getEnquiryNumSet().size());// 询单人数
        teamPes.setOrderedNumToday(bo.getOrderedNumTodaySet().size());
        teamPes.setOrderedGoodsNumToday(bo.getOrderedGoodsNumToday());
        teamPes.setOrderedAmountToday(bo.getOrderedAmountToday());
        teamPes.setOrderedNumFinal(bo.getOrderedNumFinalSet().size());
        teamPes.setOrderedGoodsNumFinal(bo.getOrderedGoodsNumFinal());
        teamPes.setOrderedAmountFinal(bo.getOrderedAmountFinal());
        teamPes.setPaidNumToday(bo.getPaidNumTodaySet().size());
        teamPes.setPaidAmountToday(bo.getPaidAmountToday());
        teamPes.setPaidGoodsNumToday(bo.getPaidGoodsNumToday());
        teamPes.setPaidNumTodayNext(bo.getPaidNumTodayNextSet().size());
        teamPes.setPaidNumFinal(bo.getPaidNumFinalSet().size());
        teamPes.setPaidGoodsNumFinal(bo.getPaidGoodsNumFinal());
        teamPes.setPaidAmountFinal(bo.getPaidAmountFinal());
        teamPes.setOutStockOrderBuyerNumFinal(bo.getOutStockOrderBuyerNumFinalSet().size());
        teamPes.setOutStockOrderGoodsNumFinal(bo.getOutStockOrderGoodsNumFinal());
        teamPes.setOutStockOrderNumFinal(bo.getOutStockOrderNumFinal());
        teamPes.setOutStockOrderAmountFinal(bo.getOutStockOrderAmountFinal());
    }

    private void initCsPerformance(CsPerformanceDO csPerformance) {
        csPerformance.setDirectReceiveNum(0);
        csPerformance.setForwardInNum(0);// 转入接待人数
        csPerformance.setForwardOutNum(0);// 转出接待人数
        csPerformance.setConsultNum(0);
        csPerformance.setReceiveNum(0);// 接待人数
        csPerformance.setEnquiryNum(0);// 询单人数
        csPerformance.setOrderedNumToday(0);
        csPerformance.setOrderedGoodsNumToday(0);
        csPerformance.setOrderedAmountToday(0D);
        csPerformance.setOrderedNumFinal(0);
        csPerformance.setOrderedGoodsNumFinal(0);
        csPerformance.setOrderedAmountFinal(0D);
        csPerformance.setPaidNumToday(0);
        csPerformance.setPaidAmountToday(0D);
        csPerformance.setPaidGoodsNumToday(0);
        csPerformance.setPaidNumTodayNext(0);
        csPerformance.setPaidNumFinal(0);
        csPerformance.setPaidGoodsNumFinal(0);
        csPerformance.setPaidAmountFinal(0D);
        csPerformance.setReceiveRate(0D);
        csPerformance.setConversionRate(0D);
    }

    private void initShopTeamPerformance(ShopTeamPerformanceDO csPerformance) {
        csPerformance.setDirectReceiveNum(0);
        csPerformance.setForwardInNum(0);// 转入接待人数
        csPerformance.setForwardOutNum(0);// 转出接待人数
        csPerformance.setConsultNum(0);
        csPerformance.setReceiveNum(0);// 接待人数
        csPerformance.setEnquiryNum(0);// 询单人数
        csPerformance.setOrderedNumToday(0);
        csPerformance.setOrderedGoodsNumToday(0);
        csPerformance.setOrderedAmountToday(0D);
        csPerformance.setOrderedNumFinal(0);
        csPerformance.setOrderedGoodsNumFinal(0);
        csPerformance.setOrderedAmountFinal(0D);
        csPerformance.setPaidNumToday(0);
        csPerformance.setPaidAmountToday(0D);
        csPerformance.setPaidGoodsNumToday(0);
        csPerformance.setPaidNumTodayNext(0);
        csPerformance.setPaidNumFinal(0);
        csPerformance.setPaidGoodsNumFinal(0);
        csPerformance.setPaidAmountFinal(0D);
    }

    /**
     * 客服绩效计算(促成下单绩效)
     *
     * @param isDelData
     */
    @Override
    public void handleShopCsPerformanceForToOrder(JobShopQuery jobShop,
                                                  JobDateQuery jobDate, boolean isDelData) {

        long s = System.currentTimeMillis();

        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        Set<CalTypeDateBO> dateSet = jobDate.getCsPerformanceForToOrderCalTypeDateSet();
        if (CollectionUtils.isEmpty(dateSet)) {
//            logger.error("req dates is empty");
            return;
        }

        for (Iterator<CalTypeDateBO> iter = dateSet.iterator(); iter.hasNext(); ) {
            CalTypeDateBO calTypeDateBO = iter.next();
            Date date = calTypeDateBO.getDate();

            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }

            List<CsTOrderPerformanceDO> csTOrderDayPerformanceLst = Lists.newArrayListWithCapacity(targetCsLst.size());

            PerformanceBO performanceBO = new PerformanceBO();
            ShopTeamTOrderPerformanceDataBO shopTeamTOrderPerformanceDataBO = new ShopTeamTOrderPerformanceDataBO(shopId, date);
            performanceBO.setShopTeamTOrderPerformanceDataBO(shopTeamTOrderPerformanceDataBO);

            ShopTeamTOrderPerformanceDO shopTeamPerformance = new ShopTeamTOrderPerformanceDO(shopId, date);
            performanceBO.setShopTeamTOrderPerformance(shopTeamPerformance);
            initShopTeamTOrderPerformance(shopTeamPerformance);

            String csNick;
            for (CsDTO cs : targetCsLst) {
                csNick = cs.getNick();

                CsTOrderPerformanceDO csTOrderPes = new CsTOrderPerformanceDO(shop.getShopId(), date, csNick);
                csTOrderDayPerformanceLst.add(csTOrderPes);
                intiCsTOrderPerformance(csTOrderPes);

                performanceBO.setCsTOrderPerformance(csTOrderPes);


//                if(calTypeDateBO.getDataTypeEnum().equals(DataTypeEnum.TYPE_ORDERED)){

                /*
                 * 促成订单-维度：计算下单数据
                 * （不需要考虑预售）
                 */
                this.assembledCurrentDayToOrderedDataForOrder(jobShop, date, csNick, performanceBO);

                /*
                 * 促成订单-维度：计算下单并付款的当日数据
                 * （不需要考虑预售）
                 */
                this.assembledToOrderedAndTodayPaidOrderDataForOrder(jobShop, date, csNick, performanceBO);

//                }

//                if(calTypeDateBO.getDataTypeEnum().equals(DataTypeEnum.TYPE_PAID)){

                /*
                 * 促成订单-维度：计算下单并付款的最终数据
                 */
                this.assembledToOrderedAndFinalPaidOrderDataForOrder(jobShop, date, csNick, performanceBO);

//                }


//                if(calTypeDateBO.getDataTypeEnum().equals(DataTypeEnum.TYPE_OUT_STACK)){

                /*
                 * 促成订单-维度：计算下单并出库的最终数据
                 */
                this.assembledToOrderedAndFinalOutStockOrderDataForOrder(jobShop, date, csNick, performanceBO);
//                }
            }

       //     csTOrderPerformanceDao.deleteCsTOrderPerformances(shop, date);
            csTOrderPerformanceDao.insertCsTOrderPerformances(shop, date, csTOrderDayPerformanceLst);

            /*
             * 封装店铺客服团队数据
             */
            assembledShopTeamTOrderPerformanceData(shopTeamTOrderPerformanceDataBO, shopTeamPerformance);

           // shopTeamTOrderPerformanceDao.deleteShopTeamTOrderPerformance(shop, date);
            shopTeamTOrderPerformanceDao.insertShopTeamTOrderPerformance(shop, shopTeamPerformance);
        }

        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("handle shop cs ToOrder performance end,time:{}s", (e - s) / 1000);
        }
    }


    /**
     * 客服绩效计算(更新客服销售额)
     *
     * @param isDelData
     * @throws Exception
     */
    @Override
    public void handleShopCsSaleAndOutStackData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {

        long s = System.currentTimeMillis();

        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        Set<Date> dates = jobDate.getShopCsSaleAndOutStackDataSet();
        if (CollectionUtils.isEmpty(dates)) {
//            logger.error("req dates is empty");
            return;
        }

        List<Date> dateList = new ArrayList<>(dates);
//        dateList.sort(Comparator.comparing(Date::getTime).reversed());
        Date startDate = dateList.get(0);
        Date endDate = dateList.get(dateList.size() - 1);

        List<OrderDTO> presaleParentOrderList = orderDao.selectParentOrderForPresaleOrderByShopIdAndDateAndBuyerNick(shop, com.pes.jd.util.DateUtils.getDateByPeriod(startDate, -49), com.pes.jd.util.DateUtils.getDateByPeriod(startDate, -3), com.pes.jd.util.DateUtils.getEndTimeOfDate(endDate));
        List<OrderDTO> parentOrderLst = orderDao.selectParentOrderToPayCsSaleOrderLstNew(shop, DateUtil.getDateByPeriod(startDate, -jobShop.getShopSystemsetting().getEnquiryValidDurationTime() + 1), com.pes.jd.util.DateUtils.getEndTimeOfDate(endDate));

        List<Long> parentOrderIdList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(parentOrderLst)){
            parentOrderIdList = parentOrderLst.stream().map(OrderDTO::getDirectTradeId).collect(Collectors.toList());
        }
        for (OrderDTO dto : presaleParentOrderList) {
            parentOrderIdList.add(dto.getTradeId());
            parentOrderIdList.add(dto.getDirectTradeId());
        }
        for (Date date : dates) {

            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }

            List<CsTOrderPerformanceDO> csPfLst = csTOrderPerformanceDao.selectCsTOrderPerformanceLstForUpdate(shop, date);
            if (CollectionUtils.isEmpty(csPfLst)) {
                continue;
            }
            Map<String, CsTOrderPerformanceDO> csPfMap = csPfLst.stream().collect(Collectors.toMap(CsTOrderPerformanceDO::getCsNick, ele -> ele));

            PerformanceBO performanceBO = new PerformanceBO();
            ShopTeamTOrderPerformanceDataBO teamPfBO = new ShopTeamTOrderPerformanceDataBO(shopId, date);
            performanceBO.setShopTeamTOrderPerformanceDataBO(teamPfBO);

            ShopTeamTOrderPerformanceDO teamPf = shopTeamTOrderPerformanceDao.selectShopTeamTOrderPerformanceForUpdate(shop, date);
            teamPf.setSaleAmount(0D);
            teamPf.setSaleOrderNum(0);
            teamPf.setSaleGoodsNum(0);
            teamPf.setSaleBuyerNum(0);
            teamPf.setSaleSkuNum(0);
            teamPf.setPostFee(0D);
            teamPf.setOutStockNum(0);
            teamPf.setOutStockAmount(0D);
            teamPf.setOutStockGoodsNum(0);
            teamPf.setOutStockOrderNum(0);
            performanceBO.setShopTeamTOrderPerformance(teamPf);

            String csNick;
            for (CsDTO cs : targetCsLst) {
                csNick = cs.getNick();

                CsTOrderPerformanceDO csTOrderPes = csPfMap.get(csNick);
                if (csTOrderPes != null) {

                    csTOrderPes.setSaleAmount(0D);
                    csTOrderPes.setSaleOrderNum(0);
                    csTOrderPes.setSaleGoodsNum(0);
                    csTOrderPes.setSaleBuyerNum(0);
                    csTOrderPes.setSaleSkuNum(0);
                    csTOrderPes.setPostFee(0D);
                    csTOrderPes.setOutStockNum(0);
                    csTOrderPes.setOutStockAmount(0D);
                    csTOrderPes.setOutStockGoodsNum(0);
                    csTOrderPes.setOutStockOrderNum(0);
                    csPfLst.add(csTOrderPes);

                    performanceBO.setCsTOrderPerformance(csTOrderPes);

                    /*
                     * @TODO(预售已处理)
                     * 订单付款-维度：计算付款数据(销售数据)
                     */
                    this.assembledFinalOrderSaleData(jobShop, date, csNick, performanceBO, parentOrderIdList);

                    /*
                     * @TODO(预售已处理)
                     * 订单出库-维度：计算出库数据
                     */
                    this.assembledOutStockData(jobShop, date, csNick, performanceBO);

                    /*
                     * @TODO(预售未处理，业务未使用)
                     * 订单确认收货-维度：计算确认收货数据
                     * this.assembledConfirmOrderEndData(jobShop, date, csNick, performanceBO);
                     */
                }

            }

            csTOrderPerformanceDao.batchUpdateShopCsSaleAndOutStackData(shop, date, csPfLst);

            /*
             * 封装店铺客服团队销售/出库数据
             */
            assembledShopTeamTOrderSaleAndOutStackData(teamPfBO, teamPf);

            shopTeamTOrderPerformanceDao.updateShopTeamSaleAndOutStackData(shop, teamPf);
        }

            long e = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {

                logger.debug("handle shop cs toOrder sale and outstack performance end,time:{}s", (e - s) / 1000);
            }
        }

    private void intiCsTOrderPerformance(CsTOrderPerformanceDO csTOrderPes) {
        csTOrderPes.setToOrderedNum(0);
        csTOrderPes.setToOrderedOrderNum(0);
        csTOrderPes.setToOrderedGoodsNum(0);
        csTOrderPes.setToOrderedAmount(0D);
        csTOrderPes.setToOrderedPaidNumToday(0);
        csTOrderPes.setToOrderedPaidOrderNumToday(0);
        csTOrderPes.setToOrderedPaidGoodsToday(0);
        csTOrderPes.setToOrderedPaidAmountToday(0D);
        csTOrderPes.setToOrderedPaidNumFinal(0);
        csTOrderPes.setToOrderedPaidOrderNumFinal(0);
        csTOrderPes.setToOrderedPaidGoodsFinal(0);
        csTOrderPes.setToOrderedPaidAmountFinal(0D);
        csTOrderPes.setSaleAmount(0D);
        csTOrderPes.setSaleOrderNum(0);
        csTOrderPes.setSaleGoodsNum(0);
        csTOrderPes.setSaleBuyerNum(0);
        csTOrderPes.setOutStockNum(0);
        csTOrderPes.setOutStockAmount(0D);
        csTOrderPes.setOutStockGoodsNum(0);
        csTOrderPes.setOutStockOrderNum(0);
        csTOrderPes.setCfmGoodsOrderNum(0);
        csTOrderPes.setCfmGoodsAmount(0D);
        csTOrderPes.setCfmGoodsNum(0);
        csTOrderPes.setCfmGoodsBuyerNum(0);

    }

    private void initShopTeamTOrderPerformance(ShopTeamTOrderPerformanceDO shopTeamPerformance) {
        shopTeamPerformance.setToOrderedNum(0);
        shopTeamPerformance.setToOrderedOrderNum(0);
        shopTeamPerformance.setToOrderedGoodsNum(0);
        shopTeamPerformance.setToOrderedAmount(0D);
        shopTeamPerformance.setToOrderedPaidNumToday(0);
        shopTeamPerformance.setToOrderedPaidOrderNumToday(0);
        shopTeamPerformance.setToOrderedPaidGoodsToday(0);
        shopTeamPerformance.setToOrderedPaidAmountToday(0D);
        shopTeamPerformance.setToOrderedPaidNumFinal(0);
        shopTeamPerformance.setToOrderedPaidOrderNumFinal(0);
        shopTeamPerformance.setToOrderedPaidGoodsFinal(0);
        shopTeamPerformance.setToOrderedPaidAmountFinal(0D);
        shopTeamPerformance.setSaleAmount(0D);
        shopTeamPerformance.setSaleOrderNum(0);
        shopTeamPerformance.setSaleGoodsNum(0);
        shopTeamPerformance.setSaleBuyerNum(0);
        shopTeamPerformance.setOutStockAmount(0D);
        shopTeamPerformance.setOutStockGoodsNum(0);
        shopTeamPerformance.setOutStockOrderNum(0);
        shopTeamPerformance.setCfmGoodsOrderNum(0);
        shopTeamPerformance.setCfmGoodsAmount(0D);
        shopTeamPerformance.setCfmGoodsNum(0);
        shopTeamPerformance.setCfmGoodsBuyerNum(0);
        shopTeamPerformance.setPostFee(0D);
    }


    private void assembledShopTeamTOrderSaleAndOutStackData(
            ShopTeamTOrderPerformanceDataBO bo,
            ShopTeamTOrderPerformanceDO teamPes) {

        teamPes.setSaleAmount(bo.getSaleAmount());
        teamPes.setSaleOrderNum(bo.getSaleOrderNum());
        teamPes.setSaleGoodsNum(bo.getSaleGoodsNum());
        teamPes.setSaleBuyerNum(bo.getSaleBuyerNumSet().size());

        teamPes.setOutStockAmount(bo.getOutStockAmount());
        teamPes.setOutStockGoodsNum(bo.getOutStockGoodsNum());
        teamPes.setOutStockOrderNum(bo.getOutStockOrderNum());
        teamPes.setOutStockNum(bo.getOutStockNumSet().size());

        teamPes.setCfmGoodsOrderNum(bo.getCfmGoodsOrderNum());
        teamPes.setCfmGoodsAmount(bo.getCfmGoodsAmount());
        teamPes.setCfmGoodsNum(bo.getCfmGoodsNum());
        teamPes.setCfmGoodsBuyerNum(bo.getCfmGoodsBuyerNumSet().size());
        teamPes.setPostFee(bo.getPostFee());//客服团队邮费
    }

    private void assembledShopTeamTOrderPerformanceData(ShopTeamTOrderPerformanceDataBO bo,
                                                        ShopTeamTOrderPerformanceDO teamPes) {

        teamPes.setToOrderedNum(bo.getToOrderedNumSet().size());
        teamPes.setToOrderedOrderNum(bo.getToOrderedOrderNum());
        teamPes.setToOrderedGoodsNum(bo.getToOrderedGoodsNum());
        teamPes.setToOrderedAmount(bo.getToOrderedAmount());
        teamPes.setToOrderedPaidNumToday(bo.getToOrderedPaidNumTodaySet().size());
        teamPes.setToOrderedPaidOrderNumToday(bo.getToOrderedPaidOrderNumToday());
        teamPes.setToOrderedPaidGoodsToday(bo.getToOrderedPaidGoodsToday());
        teamPes.setToOrderedPaidAmountToday(bo.getToOrderedPaidAmountToday());
        teamPes.setToOrderedPaidNumFinal(bo.getToOrderedPaidNumFinalSet().size());
        teamPes.setToOrderedPaidOrderNumFinal(bo.getToOrderedPaidOrderNumFinal());
        teamPes.setToOrderedPaidGoodsFinal(bo.getToOrderedPaidGoodsFinal());
        teamPes.setToOrderedPaidAmountFinal(bo.getToOrderedPaidAmountFinal());
        teamPes.setToOrderedOutStockNum(bo.getToOrderedOutStockNumSet().size());
        teamPes.setToOrderedOutStockAmount(bo.getToOrderedOutStockAmount());
        teamPes.setToOrderedOutStockGoodsNum(bo.getToOrderedOutStockGoodsNum());
        teamPes.setToOrderedOutStockOrderNum(bo.getToOrderedOutStockOrderNum());
        teamPes.setPostFee(bo.getPostFee());//客服团队邮费

        assembledShopTeamTOrderSaleAndOutStackData(bo, teamPes);
    }

    /**
     * 计算当日接待指标数据
     *
     * @param date
     * @param csNick
     */
    private Set<String> assembledCurrentDayReceiveServiceDataForEnquiry(JobShopQuery jobShop, Date date, String csNick,
                                                                        PerformanceBO performanceBO) {

        JobShopDTO shop = jobShop.getShop();
        CsPerformanceDO csPerformance = performanceBO.getCsPerformance();
        /*
         * 订单创建在订单落实那天
         */
        List<PesCalChatpeerDTO> chatPeers = csChatpeerDao.selectShopCsChatpeerLstForCsPesCal(shop, date, csNick);
        if (CollectionUtils.isNotEmpty(chatPeers)) {

            ShopTeamPerformanceDataBO teamPesDataBO = performanceBO.getShopTeamPerformanceDataBO();

            int consultNum = 0;// 客服当天咨询人数
            int receiveNum = 0;// 客服当天接待人数
            int directReceiveNum = 0;
            int forwardInNum = 0;// 转入接待人数
            int forwardOutNum = 0;// 转出接待人数
            int enquiryNum = 0;// 转出接待人数

            for (PesCalChatpeerDTO cp : chatPeers) {


                if (cp.getIsConsult()) {
                    // 算询单
                    consultNum++;
                    teamPesDataBO.getConsultNumSet().add(cp.getBuyerNick());

                    if (cp.getIsReceive()) {
                        // 算接待
                        receiveNum++;
                        teamPesDataBO.getReceiveNumSet().add(cp.getBuyerNick());

                        if (cp.getForwardFlag() == 1) {
                            // 转入
                            forwardInNum++;
                            teamPesDataBO.getForwardInNumSet().add(cp.getBuyerNick());

                        } else if (cp.getForwardFlag() == 2) {
                            // 转出
                            forwardOutNum++;
                            teamPesDataBO.getForwardOutNumSet().add(cp.getBuyerNick());
                        } else {
                            // 直接接待
                            directReceiveNum++;
                            teamPesDataBO.getDirectReceiveNumSet().add(cp.getBuyerNick());

                        }

                        if (cp.getIsEnquiry()) {
                            // 算询单
                            enquiryNum++;
                            teamPesDataBO.getEnquiryNumSet().add(cp.getBuyerNick());

                        }
                    }
                }
            }
            csPerformance.setConsultNum(consultNum);
            csPerformance.setReceiveNum(receiveNum);
            csPerformance.setDirectReceiveNum(directReceiveNum);
            csPerformance.setForwardInNum(forwardInNum);
            csPerformance.setForwardOutNum(forwardOutNum);
            csPerformance.setEnquiryNum(enquiryNum);
            return chatPeers.stream()
                    .filter(PesCalChatpeerDTO::getIsEnquiry)
                    .map(PesCalChatpeerDTO::getBuyerNick)
                    .collect(Collectors.toSet());
        }

        return Sets.newHashSet();
    }


    /**
     * 计算当日数据
     *
     * @param date
     * @param csNick
     */
    private void assembledCurrentDayOrderedDataForEnquiry(JobShopQuery jobShop, Date date, String csNick,
                                                          PerformanceBO performanceBO, Set<String> enquiryBuyerSet) {
        JobShopDTO shop = jobShop.getShop();
        CsPerformanceDO csPerformance = performanceBO.getCsPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        /*
         * 订单创建在订单落实那天
         */
        List<CsOrderBindInfoDTO> currentDayCsBindOrderLst = csOrderBindDao.selectShopCurrentDayCsOrderBindLstForPesCal(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(currentDayCsBindOrderLst)) {

            //团队绩效
            ShopTeamPerformanceDataBO teamPesDataBO = performanceBO.getShopTeamPerformanceDataBO();

            // 当日询单 当日下单人数
            int orderNumToday = 0;
            int orderedGoodsNumToday = 0;
            // 当日询单 当天落实下单 并且 付了款的人数
            int paidNumToday;
            // 当日询单 当日落实下单的金额
            double orderAmountToday = 0;
            // 当日询单 当日落实下单 并且当天付了款的金额
            double paidAmountToday = 0;
            int paidGoodsNumToday = 0;


            // 得到店铺当日要过滤的orders
            Set<String> orderedBuyerNickSet = Sets.newHashSetWithExpectedSize(currentDayCsBindOrderLst.size());
            Set<String> paidBuyerNickSet = Sets.newHashSetWithExpectedSize(currentDayCsBindOrderLst.size());
            for (CsOrderBindInfoDTO orderBind : currentDayCsBindOrderLst) {

                if (enquiryBuyerSet.contains(orderBind.getBuyerNick())) {// 询单
                    if (!orderBind.getIsGoodsFilter()) {
                        orderedBuyerNickSet.add(orderBind.getBuyerNick());
                        orderAmountToday += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
                    }
                    orderedGoodsNumToday += orderBind.getOrderGoodsNum() == null ? 0 : orderBind.getOrderGoodsNum();
                    Date payDate = orderBind.getOrderPayDate();

                    if (payDate != null && (payDate.compareTo(endDate) <= 0)) {
                        paidBuyerNickSet.add(orderBind.getBuyerNick());
                        if (!orderBind.getIsGoodsFilter()) {
                            paidAmountToday += nonNull(orderBind::getOrderPayment, zeroDouble);
                            paidGoodsNumToday += nonNull(orderBind::getOrderGoodsNum, zeroInt);
                        }

                    }
                }
            }
            orderNumToday = orderedBuyerNickSet.size();
            paidNumToday = paidBuyerNickSet.size();

            csPerformance.setOrderedNumToday(orderNumToday);
            csPerformance.setOrderedGoodsNumToday(orderedGoodsNumToday);
            csPerformance.setOrderedAmountToday(orderAmountToday);
            csPerformance.setPaidNumToday(paidNumToday);
            csPerformance.setPaidAmountToday(paidAmountToday);
            csPerformance.setPaidGoodsNumToday(paidGoodsNumToday);

            teamPesDataBO.getOrderedNumTodaySet().addAll(orderedBuyerNickSet);
            teamPesDataBO.setOrderedGoodsNumToday(orderedGoodsNumToday + teamPesDataBO.getOrderedGoodsNumToday());
            teamPesDataBO.setOrderedAmountToday(orderAmountToday + teamPesDataBO.getOrderedAmountToday());
            teamPesDataBO.getPaidNumTodaySet().addAll(paidBuyerNickSet);
            teamPesDataBO.setPaidAmountToday(paidAmountToday + teamPesDataBO.getPaidAmountToday());
            teamPesDataBO.setPaidGoodsNumToday(paidGoodsNumToday + teamPesDataBO.getPaidGoodsNumToday());

        }

    }

    private Supplier<Double> zeroDouble = () -> 0.0;
    private Supplier<Integer> zeroInt = () -> 0;
    private Supplier<Long> zeroLong = () -> 0L;

    private <T> T nonNull(Supplier<T> valid, Supplier<T> defaultVal) {
        final T v = valid.get();
        return v == null ? defaultVal.get() : v;
    }

    /**
     * 计算询单最终下单的数据
     *
     * @param date
     * @param csNick
     */
    private void assembledFinalOrderedDataForEnquiry(JobShopQuery jobShop, Date date, String csNick,
                                                     PerformanceBO performanceBO, Set<String> enquiryBuyerSet) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsPerformanceDO csPerformance = performanceBO.getCsPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);

        //设置询单有效的开始时间
        validDateRange.setAdjustEnquiryStartDate(startDate);
        validDateRange.setAdjustEnquiryEndDate(DateUtil.getDateByPeriod(endDate, sys.getEnquiryValidDurationTime()));

        //次日时间
        Date nextDayDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(endDate, 1));

        /*
         * 按绩效时间查询
         *
         */
        List<CsOrderBindInfoDTO> enquiryDayCsBindOrderLst = csOrderBindDao.selectShopCsOrderBindLstForPesCal(shop, csNick, date, validDateRange);

        // 获取当天当前客服下订单的买家的Nick集合 ,后面进行移除
        if (CollectionUtils.isNotEmpty(enquiryDayCsBindOrderLst)) {

            //团队绩效
            ShopTeamPerformanceDataBO teamPesDataBO = performanceBO.getShopTeamPerformanceDataBO();

            // 当日询单 最终落实下单的人数（当日+次日）
            int orderedNumFinal = 0;
            //当日询单 最终落实下单的金额
            double orderedAmountFinal = 0D;
            //当日询单 最终落实下单，并且最终付了款的金额
            double paidAmountFinal = 0D;
            //当日询单 最终落实下单，并且最终付了款的人数
            int paidNumFinal;

            // 咨询下单商品数
            int orderedGoodsNumFinal = 0;
            //咨询下单并付款的商品数
            int paidGoodsNumFinal = 0;

            Set<String> finalOrderedBuyerNickSet = new HashSet<>(enquiryDayCsBindOrderLst.size());
            Set<String> finalPaidBueyNickSet = new HashSet<>();

            //当日或次日付款人数
            Set<String> todayAndNextDayPaidBueyNickSet = new HashSet<>();

            // 便历已下单买家，删除接待人数中的已下单买家
            // 去除前一天询单然后下单的买家
            Iterator<CsOrderBindInfoDTO> it = enquiryDayCsBindOrderLst.iterator();
            CsOrderBindInfoDTO orderBind;
            while (it.hasNext()) {
                orderBind = it.next();

                if (enquiryBuyerSet.contains(orderBind.getBuyerNick())) {//询单
                    finalOrderedBuyerNickSet.add(orderBind.getBuyerNick());

                    if (!orderBind.getIsGoodsFilter()) {
                        orderedAmountFinal += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
                        orderedGoodsNumFinal += orderBind.getOrderGoodsNum() == null ? 0 : orderBind.getOrderGoodsNum();
                    }
                    if (orderBind.getOrderPayDate() == null) {
                        if (orderBind.getPayType() != null && orderBind.getPayType() == 1) {
                            if (orderBind.getOrderCreated().getTime() <= nextDayDate.getTime() && orderBind.getOrderCreated().getTime() >= startDate.getTime()) {
                                todayAndNextDayPaidBueyNickSet.add(orderBind.getBuyerNick());
                            }

                            finalPaidBueyNickSet.add(orderBind.getBuyerNick());
                            if (!orderBind.getIsGoodsFilter()) {
                                paidAmountFinal += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
                                paidGoodsNumFinal += orderBind.getOrderGoodsNum() == null ? 0 : orderBind.getOrderGoodsNum();
                            }
                        }
                    } else {
                        if (orderBind.getOrderPayDate().getTime() >= startDate.getTime()) {
                            if (orderBind.getOrderPayDate().getTime() <= nextDayDate.getTime()) {
                                todayAndNextDayPaidBueyNickSet.add(orderBind.getBuyerNick());
                            }
                            finalPaidBueyNickSet.add(orderBind.getBuyerNick());
                            if (!orderBind.getIsGoodsFilter()) {
                                paidAmountFinal += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
                                paidGoodsNumFinal += orderBind.getOrderGoodsNum() == null ? 0 : orderBind.getOrderGoodsNum();
                            }

                        }
                    }
                }
            }

            List<CsOrderBindInfoDTO> csOrderBindOrderPayDateLst = Optional.ofNullable(csOrderBindDao.selectShopCsOrderBindByOrderPayDateAndType(shop, csNick, date, "2",validDateRange)).orElse(new ArrayList<>(0));
            //询单维度
            List<CsOrderBindInfoDTO> enquiryCsOrderBindOrderPayDateLst = csOrderBindOrderPayDateLst .stream()
                    .filter(csOrderBindInfoDTO -> Optional.ofNullable(enquiryBuyerSet).orElse(new HashSet<>()).contains(csOrderBindInfoDTO.getBuyerNick()))
                    .collect(Collectors.toList());
            Set<String> enquiryCsOrderBindCsNick = enquiryCsOrderBindOrderPayDateLst.stream()
                    .filter(csOrderBindInfoDTO -> (csOrderBindInfoDTO.getOrderPayDate().getTime() >= startDate.getTime()))
                    .map(CsOrderBindInfoDTO::getBuyerNick)
                    .collect(Collectors.toSet());
            finalPaidBueyNickSet.addAll(enquiryCsOrderBindCsNick);
            Set<String> todayAndNextDayPaidBueyNick = enquiryCsOrderBindOrderPayDateLst.stream()
                    .filter(csOrderBindInfoDTO -> (csOrderBindInfoDTO.getOrderPayDate().getTime() >= startDate.getTime()) && (csOrderBindInfoDTO.getOrderPayDate().getTime() <= nextDayDate.getTime()))
                    .map(CsOrderBindInfoDTO::getBuyerNick)
                    .collect(Collectors.toSet());
            todayAndNextDayPaidBueyNickSet.addAll(todayAndNextDayPaidBueyNick);

            orderedNumFinal = finalOrderedBuyerNickSet.size();
            paidNumFinal = finalPaidBueyNickSet.size();
            csPerformance.setOrderedNumFinal(orderedNumFinal);
            csPerformance.setOrderedAmountFinal(orderedAmountFinal);
            csPerformance.setOrderedGoodsNumFinal(orderedGoodsNumFinal);
            csPerformance.setPaidNumFinal(paidNumFinal);
            csPerformance.setPaidAmountFinal(paidAmountFinal);
            csPerformance.setPaidGoodsNumFinal(paidGoodsNumFinal);
            csPerformance.setPaidNumTodayNext(todayAndNextDayPaidBueyNickSet.size());

            teamPesDataBO.getOrderedNumFinalSet().addAll(finalOrderedBuyerNickSet);
            teamPesDataBO.setOrderedAmountFinal(orderedAmountFinal + teamPesDataBO.getOrderedAmountFinal());
            teamPesDataBO.setOrderedGoodsNumFinal(orderedGoodsNumFinal + teamPesDataBO.getOrderedGoodsNumFinal());
            teamPesDataBO.getPaidNumFinalSet().addAll(finalPaidBueyNickSet);
            teamPesDataBO.setPaidAmountFinal(paidAmountFinal + teamPesDataBO.getPaidAmountFinal());
            teamPesDataBO.setPaidGoodsNumFinal(paidGoodsNumFinal + teamPesDataBO.getPaidGoodsNumFinal());
            teamPesDataBO.getPaidNumTodayNextSet().addAll(todayAndNextDayPaidBueyNickSet);
        }
    }

    /**
     * 询单-> 出库 (最终数据)
     * 计算询单最终出库的数据
     *
     * @param date
     * @param csNick
     */
    private void assembledFinalOutStockDataForEnquiry(JobShopQuery jobShop, Date date, String csNick,
                                                      PerformanceBO performanceBO, Set<String> enquiryBuyerSet) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Integer outStockValidDays = sys.getOutStockValidDurationTime();

        CsPerformanceDO csPerformance = performanceBO.getCsPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        //出库有效时长-->用来判断是否是出库流失
        Date endOutValid = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(date, outStockValidDays - 1));

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);

        //设置询单有效的开始时间
        validDateRange.setAdjustEnquiryStartDate(startDate);
        validDateRange.setAdjustEnquiryEndDate(DateUtil.getDateByPeriod(endDate, sys.getEnquiryValidDurationTime()));
        /*
         * 按绩效时间查询
         * 包含落实的预售订单(落实下单，落实付定金)
         */
        List<CsOrderBindInfoDTO> enquiryDayCsBindOrderLst = csOrderBindDao.selectShopCsOrderBindLstForPesCal(shop, csNick, date, validDateRange);

        // 获取当天当前客服下订单的买家的Nick集合 ,后面进行移除
        if (CollectionUtils.isNotEmpty(enquiryDayCsBindOrderLst)) {

            //团队绩效
            ShopTeamPerformanceDataBO teamPesDataBO = performanceBO.getShopTeamPerformanceDataBO();

            List<Long> orderIdLst = enquiryDayCsBindOrderLst.stream()
                    .map(CsOrderBindInfoDTO::getOrderId)
                    .collect(Collectors.toList());

            //@TODO(检查出库有效期) - UNDO
            Set<Long> orderSet = orderDao.selectShopOutStockOrderLstByOrderIdSet(shop, startDate, endDate, orderIdLst, endOutValid);

            Set<String> bueyNickSet = Sets.newHashSet();

            int outStockOrderBuyerNumFinal = 0;
            int outStockOrderNumFinal = 0;
            int outStockOrderGoodsNumFinal = 0;
            double outStockOrderAmountFinal = 0;

            // 便历已下单买家，删除接待人数中的已下单买家
            // 去除前一天询单然后下单的买家
            Iterator<CsOrderBindInfoDTO> it = enquiryDayCsBindOrderLst.iterator();
            CsOrderBindInfoDTO orderBind;
            while (it.hasNext()) {
                orderBind = it.next();

                if (enquiryBuyerSet.contains(orderBind.getBuyerNick())) {//询单
                    if (orderSet.contains(orderBind.getOrderId())) {//出库
                        bueyNickSet.add(orderBind.getBuyerNick());

                        if (!orderBind.getIsGoodsFilter()) {
                            outStockOrderGoodsNumFinal += orderBind.getOrderGoodsNum() == null ? 0 : orderBind.getOrderGoodsNum();
                            outStockOrderAmountFinal += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
                        }
                        outStockOrderNumFinal++;
                    }
                }
            }
            outStockOrderBuyerNumFinal = bueyNickSet.size();
            csPerformance.setOutStockOrderBuyerNumFinal(outStockOrderBuyerNumFinal);
            csPerformance.setOutStockOrderGoodsNumFinal(outStockOrderGoodsNumFinal);
            csPerformance.setOutStockOrderNumFinal(outStockOrderNumFinal);
            csPerformance.setOutStockOrderAmountFinal(outStockOrderAmountFinal);

            teamPesDataBO.getOutStockOrderBuyerNumFinalSet().addAll(bueyNickSet);
            teamPesDataBO.setOutStockOrderGoodsNumFinal(outStockOrderGoodsNumFinal + teamPesDataBO.getOutStockOrderGoodsNumFinal());
            teamPesDataBO.setOutStockOrderNumFinal(outStockOrderNumFinal + teamPesDataBO.getOutStockOrderNumFinal());
            teamPesDataBO.setOutStockOrderAmountFinal(outStockOrderAmountFinal + teamPesDataBO.getOutStockOrderAmountFinal());
        }
    }


    /**
     * 计算销售数据
     *
     * @param date
     * @param csNick
     */
    private void assembledFinalOrderSaleData(JobShopQuery jobShop, Date date, String csNick,
                                             PerformanceBO performanceBO, List<Long> parentOrderIdList) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置查询数据的有效时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单的有效时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime() + 1 - ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustEnquiryEndDate(endDate);

        List<CsSaleOrderDTO> allCsSaleOrderLst = Lists.newArrayList();

        //@TODO (正常订单-按付款时间查询, 预售订单按付尾款时间查询)
        List<CsSaleOrderDTO> csSaleOrderLst = csOrderBindDao.selectShopCsSaleOrderLst(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(csSaleOrderLst)) {
            //去重
            Set<Long> orderSet = new HashSet<>();
            for (CsSaleOrderDTO csSaleOrderDTO : csSaleOrderLst) {
                if (orderSet.contains(csSaleOrderDTO.getOrderId())) {
                    continue;
                }
                orderSet.add(csSaleOrderDTO.getOrderId());
                allCsSaleOrderLst.add(csSaleOrderDTO);
            }
        }

      /*  //设置查询数据的有效时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置付尾款的有效时间
        validDateRange.setAdjustPaidStartDate(DateUtil.getDateByPeriod(startDate, 0 - validDateRange.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustPaidEndDate(endDate);
        //设置询单的有效时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, 0 - validDateRange.validPresaleOrderBalancePayDays - sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);

        //@TODO(预售付尾款) - 按预售付尾款时间查询 - 查预售表，订单绑定表
        List<CsSaleOrderDTO> csPresaleBalanePayOrderLst = orderAssociatedDao.selectShopCsPresaleBalanePayOrderLst(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(csPresaleBalanePayOrderLst)) {

            for (CsSaleOrderDTO csSaleOrder : csPresaleBalanePayOrderLst) {
                csSaleOrder.setPreSale(Boolean.TRUE);//默认是false
            }
            allCsSaleOrderLst.addAll(csPresaleBalanePayOrderLst);
        }*/

        //设置询单有效的开始时间
        validDateRange.setAdjustStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime() + 1));
        validDateRange.setAdjustEndDate(endDate);
        long f2 = System.currentTimeMillis();
        //货到付款
        List<CsSaleOrderDTO> goodsToPayCsSaleOrderLst = csOrderBindDao.selectShopGoodsToPayCsSaleOrderLst(shop, csNick, date, validDateRange);
        if (logger.isDebugEnabled()) {
            logger.debug(" -->assembledFinalOrderSaleData#selectShopGoodsToPayCsSaleOrderLst time:{}ms", (System.currentTimeMillis() - f2));
        }

        if (CollectionUtils.isNotEmpty(goodsToPayCsSaleOrderLst)) {
            allCsSaleOrderLst.addAll(goodsToPayCsSaleOrderLst);
        }
        if (CollectionUtils.isNotEmpty(allCsSaleOrderLst)) {
            /* *fix:258处理父订单拆单问题，屏蔽父订单*/
//            List<OrderDTO> parentOrderLst = orderDao.selectParentOrderToPayCsSaleOrderLst(shop, csNick, validDateRange);
            if (CollUtil.isNotEmpty(parentOrderIdList)) {
//                List<Long> parentOrderIds = parentOrderLst.stream().map(OrderDTO::getDirectTradeId).collect(Collectors.toList());
                allCsSaleOrderLst = allCsSaleOrderLst.stream().filter(ele -> !parentOrderIdList.contains(ele.getOrderId())).collect(Collectors.toList());
            }
            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();

            int saleOrderNum = 0;
            Set<String> saleBuyerNumSet = Sets.newHashSet();
            int saleGoodsNum = 0;
            double saleAmount = 0;
            double orderPostFee = 0;
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, startDate, endDate, allCsSaleOrderLst.stream().map(CsSaleOrderDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            for (CsSaleOrderDTO order : allCsSaleOrderLst) {
                if (order != null) {
                    saleOrderNum++;
                    saleBuyerNumSet.add(order.getBuyerNick());
                    List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(order.getOrderId());
                    if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                        for (OrderDetailDTO dto : orderDetailDTOS) {
                            saleGoodsNum += BaseUtils.getNonNull(dto.getItemNum());
                        }
                    }
//                    saleGoodsNum += order.getOrderGoodsNum() == null ? 0 : order.getOrderGoodsNum();
                    /*if (order.getPreSale()) {
                        saleAmount = (order.getOrderPayment() == null ? 0 : order.getOrderPayment()) + (order.getOrderBalancePayment() == null ? 0 : order.getOrderBalancePayment());//定金 + 尾款
                    } else {
                        saleAmount += order.getOrderPayment() == null ? 0 : order.getOrderPayment();//付款金额
                    }*/
                    saleAmount += order.getOrderValidPayment() == null ? 0 : order.getOrderValidPayment();//付款金额
                    orderPostFee += order.getOrderPostFee() == null ? 0 : order.getOrderPostFee();
                }
            }
            csTOrderPes.setSaleOrderNum(saleOrderNum);
            csTOrderPes.setSaleBuyerNum(saleBuyerNumSet.size());
            csTOrderPes.setSaleGoodsNum(saleGoodsNum);
            csTOrderPes.setSaleAmount(saleAmount);
            csTOrderPes.setPostFee(orderPostFee);

            teamPes.setSaleOrderNum(saleOrderNum + teamPes.getSaleOrderNum());
            teamPes.getSaleBuyerNumSet().addAll(saleBuyerNumSet);
            teamPes.setSaleGoodsNum(saleGoodsNum + teamPes.getSaleGoodsNum());
            teamPes.setSaleAmount(saleAmount + teamPes.getSaleAmount());
            teamPes.setPostFee(orderPostFee + teamPes.getPostFee());
        }


    }

    private Map<Long, Integer> initOrderSaleGoodsMap() {
        Map<Long, Integer> map = new HashMap<>();
        map.put(132672467481L,2);
        map.put(132677572883L,1);
        map.put(132681100177L,10);
        map.put(132682469490L,2);
        map.put(132683647578L,1);
        map.put(132693292029L,1);
        map.put(132693342941L,1);
        map.put(132694130710L,1);
        map.put(132694261271L,2);
        map.put(132695045873L,1);
        map.put(132695693236L,1);
        map.put(132697917852L,1);
        map.put(138413652655L,1);
        map.put(138419592076L,2);
        map.put(138469825187L,1);
        map.put(138471709121L,1);
        map.put(138474218433L,2);
        map.put(138478716619L,2);
        map.put(138479046368L,9);
        map.put(138483747335L,9);
        return map;
    }


    /**
     * 出库数据
     *
     * @param jobShop
     * @param date
     * @param csNick
     * @param performanceBO
     */
    private void assembledOutStockData(JobShopQuery jobShop, Date date, String csNick,
                                       PerformanceBO performanceBO) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

//        List<OutStockOrderDTO> allOutStockOrderLst = Lists.newArrayList();

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();

        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单有效的开始时间
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        Integer outStockValidDays = sys.getOutStockValidDurationTime();
        //TODO(check 出库数据的时间)
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -(enquiryValidDays - 1 + outStockValidDays)));
        // validDateRange.setAdjustEnquiryEndDate(DateUtil.getDateByPeriod(endDate, 0 - outStockValidDays));
        validDateRange.setAdjustEnquiryEndDate(endDate);
        validDateRange.setAdjustOutStockStartDate(DateUtil.getDateByPeriod(startDate, -(outStockValidDays)));
        validDateRange.setAdjustOutStockEndDate(endDate);

        long f1 = System.currentTimeMillis();
        //根据订单表查 endTime
        List<OutStockOrderDTO> outStockOrderLst = orderAssociatedDao.selectShopCsOutStockOrderInfoLst(shop, csNick, date, validDateRange);

        if (logger.isDebugEnabled()) {
            logger.debug(" -->assembledOutStockData#selectShopCsOutStockOrderInfoLst time:{}ms", (System.currentTimeMillis() - f1));
        }
//        if (CollectionUtils.isNotEmpty(outStockOrderLst)) {
//            for (OutStockOrderDTO stockOrder : outStockOrderLst) {
//                stockOrder.setPreSale(Boolean.FALSE);//默认是false
//            }
//            allOutStockOrderLst.addAll(outStockOrderLst);
//        }

/*        //设置查询目标数据的有效时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置预售绩效绑定有效的有效时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, 0 - validDateRange.validPresaleOrderBalancePayDays - outStockValidDays - sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);
        //设置预售出库有效的有效时间
        validDateRange.setAdjustOutStockStartDate(DateUtil.getDateByPeriod(startDate, 0 - validDateRange.validPresaleOrderBalancePayDays - outStockValidDays));
        validDateRange.setAdjustOutStockEndDate(endDate);
        long f2 = System.currentTimeMillis();
        //@TODO(预售付尾款) - 按预售付尾款时间查询 - 查预售表，订单绑定表
        List<OutStockOrderDTO> csPresaleBalanePayOrderLst = orderAssociatedDao.selectShopCsPresaleOutStackOrderLst(shop, csNick, date, validDateRange);
        long l2 = System.currentTimeMillis();
        logger.info(" -->assembledOutStockData#selectShopCsPresaleOutStackOrderLst time:{}ms",(l2-f2));
        if (CollectionUtils.isNotEmpty(csPresaleBalanePayOrderLst)) {
            for (OutStockOrderDTO stockOrder : csPresaleBalanePayOrderLst) {
                stockOrder.setPreSale(Boolean.TRUE);//默认是false
            }
            allOutStockOrderLst.addAll(csPresaleBalanePayOrderLst);
        }*/


        if (CollectionUtils.isNotEmpty(outStockOrderLst)) {
            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();
            int outStockGoodsNum = 0;
            int outStockOrderNum = 0;
            double outStockOrderAmount = 0;
            Set<String> buyerSet = Sets.newHashSetWithExpectedSize(outStockOrderLst.size());
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, startDate, endDate, outStockOrderLst.stream().map(OutStockOrderDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            for (OutStockOrderDTO order : outStockOrderLst) {
                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(order.getOrderId());
                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                    for (OrderDetailDTO dto : orderDetailDTOS) {
                        outStockGoodsNum += BaseUtils.getNonNull(dto.getItemNum());
                    }
                }
                outStockOrderNum++;
                if (order.getPreSale()) {
                    outStockOrderAmount = (order.getOrderBargainPayment() == null ? 0 : order.getOrderBargainPayment()) + (order.getOrderBalancePayment() == null ? 0 : order.getOrderBalancePayment());//定金 + 尾款
                } else {
                    outStockOrderAmount += order.getPayment() == null ? 0 : order.getPayment();//付款金额
                }
                buyerSet.add(order.getBuyerNick());
            }

            csTOrderPes.setOutStockGoodsNum(outStockGoodsNum);
            csTOrderPes.setOutStockNum(buyerSet.size());
            csTOrderPes.setOutStockOrderNum(outStockOrderNum);
            csTOrderPes.setOutStockAmount(outStockOrderAmount);

            teamPes.setOutStockGoodsNum(outStockGoodsNum + teamPes.getOutStockGoodsNum());
            teamPes.getOutStockNumSet().addAll(buyerSet);
            teamPes.setOutStockOrderNum(outStockOrderNum + teamPes.getOutStockOrderNum());
            teamPes.setOutStockAmount(outStockOrderAmount + teamPes.getOutStockAmount());
        }
    }

    /**
     * 确认收货订单数据
     *
     * @param jobShop
     * @param date
     * @param csNick
     * @param performanceBO
     */
    private void assembledConfirmOrderEndData(JobShopQuery jobShop, Date date, String csNick,
                                              PerformanceBO performanceBO) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();

        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);

        //设置询单有效的开始时间
        final Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        final Integer outStockValidDays = sys.getOutStockValidDurationTime();

        final Integer confirmGoodsValiddays = 7;
        //TODO(check 收货的时间)
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -(enquiryValidDays + outStockValidDays + confirmGoodsValiddays)));
        validDateRange.setAdjustEnquiryEndDate(DateUtil.getDateByPeriod(endDate, -(outStockValidDays + confirmGoodsValiddays)));
        validDateRange.setAdjustConfirmGoodsStartDate(DateUtil.getDateByPeriod(startDate, -(outStockValidDays + confirmGoodsValiddays)));
        validDateRange.setAdjustConfirmGoodsEndDate(endDate);

        //根据订单表查 endTime
        List<ConfirmGoodsOrderDTO> cfmOrderLst = orderAssociatedDao.selectShopCsConfirmGoodsOrderInfoLst(shop, date, csNick, validDateRange);

        if (CollectionUtils.isNotEmpty(cfmOrderLst)) {

            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();

            int conformGoodsNum = 0;
            int conformGoodsOrderNum = 0;
            double conformGoodsOrderAmount = 0;

            Set<String> buyerSet = Sets.newHashSetWithExpectedSize(cfmOrderLst.size());
            for (ConfirmGoodsOrderDTO order : cfmOrderLst) {
                conformGoodsNum += order.getNum();
                conformGoodsOrderNum++;
                conformGoodsOrderAmount += order.getPayment();
                buyerSet.add(order.getBuyerNick());
            }

            csTOrderPes.setCfmGoodsNum(conformGoodsNum);
            csTOrderPes.setCfmGoodsBuyerNum(buyerSet.size());
            csTOrderPes.setCfmGoodsOrderNum(conformGoodsOrderNum);
            csTOrderPes.setCfmGoodsAmount(conformGoodsOrderAmount);

            teamPes.setCfmGoodsNum(conformGoodsNum + teamPes.getCfmGoodsNum());
            teamPes.getCfmGoodsBuyerNumSet().addAll(buyerSet);
            teamPes.setCfmGoodsOrderNum(conformGoodsOrderNum + teamPes.getCfmGoodsOrderNum());
            teamPes.setCfmGoodsAmount(conformGoodsOrderAmount + teamPes.getCfmGoodsAmount());
        }
    }

    /**
     * 订单维度：促成下单的订单数据
     *
     * @param date
     * @param csNick
     */
    private void assembledCurrentDayToOrderedDataForOrder(JobShopQuery jobShop, Date date, String csNick, PerformanceBO performanceBO) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        /*
         * 促成下单的订单绑定列表
         */
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();

        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单有效的开始时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);
        List<CsOrderBindInfoDTO> csBindOrderLst = csOrderBindDao.selectCurrentDayToOrderedOrderLstForOrder(shop, sys, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(csBindOrderLst)) {
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, date, date, csBindOrderLst.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();
            int toOrderedNum;
            int toOrderedOrderNum = 0;
            int toOrderedGoodsNum = 0;
            double toOrderedAmount = 0;
            Set<String> buyerNickSet = Sets.newHashSetWithExpectedSize(csBindOrderLst.size());
            for (CsOrderBindInfoDTO orderBind : csBindOrderLst) {
                buyerNickSet.add(orderBind.getBuyerNick());
                toOrderedOrderNum++;
                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(orderBind.getOrderId());
                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                    for (OrderDetailDTO dto : orderDetailDTOS) {
                        toOrderedGoodsNum += BaseUtils.getNonNull(dto.getItemNum());
                    }
                }
                toOrderedAmount += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
            }
            toOrderedNum = buyerNickSet.size();
            csTOrderPes.setToOrderedNum(toOrderedNum);
            csTOrderPes.setToOrderedOrderNum(toOrderedOrderNum);
            csTOrderPes.setToOrderedGoodsNum(toOrderedGoodsNum);
            csTOrderPes.setToOrderedAmount(toOrderedAmount);

            teamPes.getToOrderedNumSet().addAll(buyerNickSet);
            teamPes.setToOrderedOrderNum(toOrderedOrderNum + teamPes.getToOrderedOrderNum());
            teamPes.setToOrderedGoodsNum(toOrderedGoodsNum + teamPes.getToOrderedGoodsNum());
            teamPes.setToOrderedAmount(toOrderedAmount + teamPes.getToOrderedAmount());


        }
    }

    /**
     * 订单维度：促成下单的订单的付款订单数据(当天)
     *
     * @param jobShop
     * @param date
     * @param csNick
     * @param performanceBO
     */
    private void assembledToOrderedAndTodayPaidOrderDataForOrder(JobShopQuery jobShop, Date date, String csNick, PerformanceBO performanceBO) {

        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        /*
         * 促成下单并且当天付款的订单绑定列表
         */
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);

        //设置询单有效的开始时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);

        List<CsOrderBindInfoDTO> csBindOrderLst = csOrderBindDao.selectCurrentDayToOrderedOrderLstForOrder(shop, sys, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(csBindOrderLst)) {
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, startDate, endDate, csBindOrderLst.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();
            int toOrderedPaidNumToday;
            int toOrderedPaidOrderNumToday = 0;
            int toOrderedPaidGoodsToday = 0;
            double toOrderedPaidAmountToday = 0D;

            Set<String> buyerNickSet = Sets.newHashSetWithExpectedSize(csBindOrderLst.size());
            for (CsOrderBindInfoDTO orderBind : csBindOrderLst) {
                //货到付款 && 付款在今天
                if (orderBind.getPayType() != null && orderBind.getPayType() == 1
                        || (orderBind.getOrderPayDate() != null && (DateUtil.getEndTimeOfDate(orderBind.getOrderPayDate()).getTime() == endDate.getTime()))) {
                    buyerNickSet.add(orderBind.getBuyerNick());
                    toOrderedPaidOrderNumToday++;
                    List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(orderBind.getOrderId());
                    if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                        for (OrderDetailDTO dto : orderDetailDTOS) {
                            toOrderedPaidGoodsToday += BaseUtils.getNonNull(dto.getItemNum());
                        }
                    }
                    toOrderedPaidAmountToday += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
                }
            }
            toOrderedPaidNumToday = buyerNickSet.size();

            csTOrderPes.setToOrderedPaidNumToday(toOrderedPaidNumToday);
            csTOrderPes.setToOrderedPaidOrderNumToday(toOrderedPaidOrderNumToday);
            csTOrderPes.setToOrderedPaidGoodsToday(toOrderedPaidGoodsToday);
            csTOrderPes.setToOrderedPaidAmountToday(toOrderedPaidAmountToday);

            teamPes.getToOrderedPaidNumTodaySet().addAll(buyerNickSet);
            teamPes.setToOrderedPaidOrderNumToday(toOrderedPaidOrderNumToday + teamPes.getToOrderedPaidOrderNumToday());
            teamPes.setToOrderedPaidGoodsToday(toOrderedPaidGoodsToday + teamPes.getToOrderedPaidGoodsToday());
            teamPes.setToOrderedPaidAmountToday(toOrderedPaidAmountToday + teamPes.getToOrderedPaidAmountToday());
        }
    }

    /**
     * 订单维度：促成下单的订单的付款订单数据(最终：下单日+1)
     *
     * @param jobShop
     * @param date
     * @param csNick
     * @param performanceBO
     */
    private void assembledToOrderedAndFinalPaidOrderDataForOrder(JobShopQuery jobShop, Date date, String csNick, PerformanceBO performanceBO) {

        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        /*
         * 促成下单并且最终付款的订单绑定列表
         */
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单有效的开始时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);

        List<CsOrderBindInfoDTO> allCsBindOrderLst = Lists.newArrayList();
        /*
         * 促成下单并且最终付款的订单绑定列表
         */
        List<CsOrderBindInfoDTO> csBindOrderLst = csOrderBindDao.selectToOrderedAndPaidOrderLstForOrder(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(csBindOrderLst)) {
            allCsBindOrderLst.addAll(csBindOrderLst);
        }
        //货到付款
        List<CsOrderBindInfoDTO> goodsToPayCsBindOrderLst = csOrderBindDao.selectGoodsToPayToOrderedAndPaidOrderLstForOrder(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(goodsToPayCsBindOrderLst)) {
            allCsBindOrderLst.addAll(goodsToPayCsBindOrderLst);
        }

        if (CollectionUtils.isNotEmpty(allCsBindOrderLst)) {
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, date, date, allCsBindOrderLst.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();

            int toOrderedPaidNumFinal;
            int toOrderedPaidOrderNumFinal = 0;
            int toOrderedPaidGoodsFinal = 0;
            double toOrderedPaidAmountFinal = 0;

            Set<String> buyerNickSet = Sets.newHashSetWithExpectedSize(allCsBindOrderLst.size());
            for (CsOrderBindInfoDTO orderBind : allCsBindOrderLst) {
//				Date payTime = orderBind.getOrderPayDate();
//				if (payTime != null) {//最终付款的订单
                buyerNickSet.add(orderBind.getBuyerNick());
                toOrderedPaidOrderNumFinal++;
                List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(orderBind.getOrderId());
                if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                    for (OrderDetailDTO dto : orderDetailDTOS) {
                        toOrderedPaidGoodsFinal += BaseUtils.getNonNull(dto.getItemNum());
                    }
                }
                toOrderedPaidAmountFinal += orderBind.getOrderPayment() == null ? 0 : orderBind.getOrderPayment();
//				}
            }
            toOrderedPaidNumFinal = buyerNickSet.size();

            csTOrderPes.setToOrderedPaidNumFinal(toOrderedPaidNumFinal);
            csTOrderPes.setToOrderedPaidOrderNumFinal(toOrderedPaidOrderNumFinal);
            csTOrderPes.setToOrderedPaidGoodsFinal(toOrderedPaidGoodsFinal);
            csTOrderPes.setToOrderedPaidAmountFinal(toOrderedPaidAmountFinal);

            teamPes.getToOrderedPaidNumFinalSet().addAll(buyerNickSet);
            teamPes.setToOrderedPaidOrderNumFinal(toOrderedPaidOrderNumFinal + teamPes.getToOrderedPaidOrderNumFinal());
            teamPes.setToOrderedPaidGoodsFinal(toOrderedPaidGoodsFinal + teamPes.getToOrderedPaidGoodsFinal());
            teamPes.setToOrderedPaidAmountFinal(toOrderedPaidAmountFinal + teamPes.getToOrderedPaidAmountFinal());
        }
    }

    /**
     * 订单维度：促成下单的订单的出库的订单数据(最终)
     *
     * @param date
     * @param csNick
     * @TODO(可优化)
     */
    private void assembledToOrderedAndFinalOutStockOrderDataForOrder(JobShopQuery jobShop, Date date, String csNick,
                                                                     PerformanceBO performanceBO) {

        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsTOrderPerformanceDO csTOrderPes = performanceBO.getCsTOrderPerformance();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        Integer outStockValidDays = sys.getOutStockValidDurationTime();
        //出库有效时长-->用来判断是否是出库流失
        Date endOutValid = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(date, outStockValidDays - 1));

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单有效的开始时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);


        List<CsOrderBindInfoDTO> allCsBindOrderLst = Lists.newArrayList();
        /*
         * 促成下单并且最终付款的订单绑定列表
         */
        List<CsOrderBindInfoDTO> csBindOrderLst = csOrderBindDao.selectToOrderedAndPaidOrderLstForOrder(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(csBindOrderLst)) {
            allCsBindOrderLst.addAll(csBindOrderLst);
        }

        //货到付款
        List<CsOrderBindInfoDTO> goodsToPayCsBindOrderLst = csOrderBindDao.selectGoodsToPayToOrderedAndPaidOrderLstForOrder(shop, csNick, date, validDateRange);
        if (CollectionUtils.isNotEmpty(goodsToPayCsBindOrderLst)) {
            allCsBindOrderLst.addAll(goodsToPayCsBindOrderLst);
        }

        if (CollectionUtils.isNotEmpty(allCsBindOrderLst)) {

            ShopTeamTOrderPerformanceDataBO teamPes = performanceBO.getShopTeamTOrderPerformanceDataBO();

            List<Long> orderIdLst = allCsBindOrderLst.stream()
                    .map(CsOrderBindInfoDTO::getOrderId)
                    .collect(Collectors.toList());

            //设置询单有效的开始时间
            validDateRange.setAdjustOutStockStartDate(startDate);
            validDateRange.setAdjustOutStockEndDate(DateUtil.getDateByPeriod(endDate, outStockValidDays));
            Optional<Set<Long>> outStockOrderIdLstOption = orderDao.selectShopOutStockOrderIdLstByOrderIdLst(shop, orderIdLst, validDateRange, endOutValid);
            if (outStockOrderIdLstOption.isPresent()) {
                Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop,
                        startDate, endDate,
                        allCsBindOrderLst.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList()));
                Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
                Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
                Set<Long> outStockOrderIdSet = outStockOrderIdLstOption.get();
                Set<String> buyerNickSet = Sets.newHashSet();
                double toOrderedOutStockAmount = 0;
                int toOrderedOutStockGoodsNum = 0;
                int toOrderedOutStockOrderNum = 0;
                if (CollectionUtils.isNotEmpty(outStockOrderIdSet)) {
                    for (CsOrderBindInfoDTO csob : allCsBindOrderLst) {
                        if (outStockOrderIdSet.contains(csob.getOrderId())) {
                            buyerNickSet.add(csob.getBuyerNick());
                            List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(csob.getOrderId());
                            if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                                for (OrderDetailDTO dto : orderDetailDTOS) {
                                    toOrderedOutStockGoodsNum += BaseUtils.getNonNull(dto.getItemNum());
                                }
                            }
                            toOrderedOutStockAmount += csob.getOrderPayment() == null ? 0 : csob.getOrderPayment();
                            toOrderedOutStockOrderNum++;
                        }
                    }
                }

                csTOrderPes.setToOrderedOutStockNum(buyerNickSet.size());
                csTOrderPes.setToOrderedOutStockGoodsNum(toOrderedOutStockGoodsNum);
                csTOrderPes.setToOrderedOutStockOrderNum(toOrderedOutStockOrderNum);
                csTOrderPes.setToOrderedOutStockAmount(toOrderedOutStockAmount);

                teamPes.getToOrderedOutStockNumSet().addAll(buyerNickSet);
                teamPes.setToOrderedOutStockGoodsNum(toOrderedOutStockGoodsNum + teamPes.getToOrderedOutStockGoodsNum());
                teamPes.setToOrderedOutStockOrderNum(toOrderedOutStockOrderNum + teamPes.getToOrderedOutStockOrderNum());
                teamPes.setToOrderedOutStockAmount(toOrderedOutStockAmount + teamPes.getToOrderedOutStockAmount());

            } else {
                csTOrderPes.setToOrderedOutStockNum(0);
                csTOrderPes.setToOrderedOutStockGoodsNum(0);
                csTOrderPes.setToOrderedOutStockOrderNum(0);
                csTOrderPes.setToOrderedOutStockAmount(0D);
            }
        }
    }

    /**
     * 客服与订单绩效绑定
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     * @throws Exception
     */
    @Override
    public void handleShopCsOrderBind(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {

        long s = System.currentTimeMillis();
        List<Date> dates = jobDate.getCsOrderBindDates();
        if (dates.isEmpty()) {
            logger.warn("req dates is empty");
            return;
        }

        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        int judgeRule = sys.getJudgeRule();
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        //询单有效期小于三天要用三天来算，因为重拉三天前的订单信息会有三天前的付款询单有效期设置低于三天少算
        List<Date> calDate = calNewOrderIndexDate(jobDate, enquiryValidDays, dates);
        Date startTime = calDate.get(0);
        Date endTime = com.pes.jd.util.DateUtils.getEndTimeOfDate(calDate.get(calDate.size() - 1));

        List<OrderDTO> presaleParentOrderList = orderDao.selectParentOrderForPresaleOrderByShopIdAndDateAndBuyerNickTwo(jobShop.getShop(), com.pes.jd.util.DateUtils.getDateByPeriod(startTime, -49), com.pes.jd.util.DateUtils.getDateByPeriod(endTime, enquiryValidDays));
        //判定绩效时过滤父订单
        List<Long> parentOrderIdList = Lists.newArrayList();
        for (OrderDTO dto : presaleParentOrderList) {
            parentOrderIdList.add(dto.getTradeId());
            parentOrderIdList.add(dto.getDirectTradeId());
        }
        // 按每日维度
        for (Date date : calDate) {
            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) { continue; }
            Date startDate = date;
            Date endDate = DateUtil.getEndTimeOfDate(date);
            csOrderBindDao.deleteShopCsOrderBindByDate(shop, date);
            Date adjustStartDay = DateUtil.getDateByPeriod(startDate, -enquiryValidDays);
            Date adjustEndDay = DateUtil.getDateByPeriod(endDate, enquiryValidDays - 1);
            if (CollectionUtils.isNotEmpty(targetCsLst)) {

                List<AsyncTask> taskList = new ArrayList<>(targetCsLst.size());
                int task = 0;
                for (CsDTO cs : targetCsLst) {
                    AsyncTask asyncTask = new AsyncTask(task) {
                        @Override
                        public Object run() {
                            String csNick = cs.getNick();
                            //询单期接待的聊天对象
                            List<CsOrderBindChatpeerDTO> chatpeers = csChatpeerDao.selectShopCsReceivedChatpeerLstByDateForCsOrderBind(shop, date, date, csNick);
                            if (CollectionUtils.isNotEmpty(chatpeers)) {
                                List<String> buyerNickLst = chatpeers.stream().map(CsOrderBindChatpeerDTO::getBuyerNick).distinct().collect(Collectors.toList());
                                // @TODO(单个客服接待的买家订单指标(可能包含其他客服的指标（一单咨询多个客服）)) 包含预售付尾款的
                                List<CsOrderIndexDTO> csBuyerOrderIndexLst = csOrderIndexDao.selectShopCsOrderIndexLstByDateRangeAndBuyerLst(shop, adjustStartDay, adjustEndDay, buyerNickLst);
                                // @TODO(查询询单有效期内不算接待的聊天对象，计算全静默的时候：下单付款前的聊天如果都被接待过滤，也算全静默 )
                                List<CommonCsChatpeerDTO> receiveFilterChatPeerLst = csChatpeerDao.selectReceiveFilterChatPeerByDateAndCsNickAndBuyerLst(shop, adjustStartDay, adjustEndDay, csNick, buyerNickLst);
                                if (CollectionUtils.isNotEmpty(csBuyerOrderIndexLst)) {
                                    for (CsOrderIndexDTO csOrderIndex : csBuyerOrderIndexLst) {
                                        //初始化值
                                        csOrderIndex.setBindOrderCreated(Boolean.FALSE);
                                        csOrderIndex.setBindOrderPaid(Boolean.FALSE);
                                        csOrderIndex.setOrderBannerFilte(Boolean.FALSE);
                                    }
                                    //处理 指标指标绑定落实下单、付款时 是否算接待
                                    handleCsOrderIndexIsBindReceive(shop, date, startDate, adjustEndDay, csBuyerOrderIndexLst);
                                    CsOrderBindBO csOrderBindBO = assembledCsOrderBindByJudgeRuleAscription(jobShop, dates.get(0), date, csNick, csBuyerOrderIndexLst, chatpeers, parentOrderIdList, receiveFilterChatPeerLst);
                                    // TODO(处理协助&付款咨询)
                                    updateCsOrderIndexs(jobShop, date, csNick, csBuyerOrderIndexLst);
                                    // TODO(根据判定规则，处理每个客服下的订单绑定)
                                    csOrderPesBind(jobShop, date, csOrderBindBO, judgeRule);
                                    List<CsOrderBindDO> bindLst = Optional.ofNullable(csOrderBindBO.getAllOrderBindLst()).orElse(new ArrayList<>(0)).stream().map(CsOrderBindJudgeBO::getCsOrderBind).collect(Collectors.toList());
                                    csOrderBindDao.batchInsertCsOrderBind(shop, date, bindLst);
                                }
                            }
                            return csNick;
                        }
                    };
                    taskList.add(asyncTask);
                    task++;
                }
                if (CollUtil.isNotEmpty(taskList)) {
                    Object[] objects = AsyncTaskUtil.runAll(taskList);
                    Arrays.stream(objects).filter(Objects::nonNull).forEach(callBack -> {
                        String nick = (String) callBack;
                    });
                }
            }
            //补已经被落实的订单
//            csOrderBindDao.batchInsertCsOrderBind(shop, date, sourceCsOrderBind);

            //处理下单优先  一个订单被多个客服绑定
            if(PesConstants.JUDGE_RULE_ORDER_FIRST==judgeRule){
                handleMoreBindOfRuleOrderFirst(shop, adjustStartDay, adjustEndDay);
            }
            //处理付款判定  一个订单被多个客服绑定
            else if (PesConstants.JUDGE_RULE_PAY == judgeRule) {
                handleMoreBindOfRulePay(shop, adjustStartDay, adjustEndDay);
            }
            //@TODO 处理预售付尾款之前聊天没算落实付款的情况
            handlePresalePayBalance(shop, sys, date, endDate, adjustStartDay, adjustEndDay);
            //@TODO 处理落实付尾款算了绩效，落实付定金没算绩效的情况（将绩效算给落实付定金）
            handleBalancePayIsPesOrder(shop,sys,startDate,endDate);
            //@TODO(维护：order_valid_pay_date)
            long s2 = System.currentTimeMillis();
            ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
            //设置查询数据的有效时间
            validDateRange.setStartDate(startDate);
            validDateRange.setEndDate(endDate);
            //设置付尾款的有效时间
            validDateRange.setAdjustPaidStartDate(DateUtil.getDateByPeriod(startDate, -ValidDateRangeQuery.validPresaleOrderBalancePayDays));
            validDateRange.setAdjustPaidEndDate(endDate);
            //设置询单的有效时间
            validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -ValidDateRangeQuery.validPresaleOrderBalancePayDays - sys.getEnquiryValidDurationTime()));
            validDateRange.setAdjustEnquiryEndDate(endDate);
            List<BuyerOrderDTO> presaleBalancePaidOrderLst = orderAssociatedDao.selectShopPresaleBalancePayBindOrderLst(shop, date, validDateRange);
            if (CollectionUtils.isNotEmpty(presaleBalancePaidOrderLst)) {

                Map<Date, List<BuyerOrderDTO>> monthMap = presaleBalancePaidOrderLst.stream().collect(Collectors.groupingBy(ele -> DateUtil.getYM(ele.getCreated())));

                for (Map.Entry<Date, List<BuyerOrderDTO>> monthDate : monthMap.entrySet()) {

                    csOrderBindDao.batchUpdateCsOrderBindOrderValidPayInfo(shop, monthDate.getKey(), monthDate.getValue());
                }
            }
            //fix 2975 更新预售付定金，未付尾款数据
            List<BuyerOrderDTO> presaleBargainPaidOrderLst = orderAssociatedDao.selectShopPresaleBargainPayBindOrderLst(shop, date, validDateRange);
            if (CollectionUtils.isNotEmpty(presaleBargainPaidOrderLst)) {

                Map<Date, List<BuyerOrderDTO>> monthMap = presaleBargainPaidOrderLst.stream().collect(Collectors.groupingBy(ele -> DateUtil.getYM(ele.getCreated())));

                for (Map.Entry<Date, List<BuyerOrderDTO>> monthDate : monthMap.entrySet()) {

                    csOrderBindDao.batchUpdateCsOrderBindOrderValidPayInfoForBargain(shop, monthDate.getKey(), monthDate.getValue());
                }
            }

            long e2 = System.currentTimeMillis();
            long e = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {

                logger.debug("update shop CsOrderBind OrderValidIndex end,time:{} s", (e2 - s2) / 1000);
                logger.debug("handle shop CsOrderBind end,time:{} s", (e - s) / 1000);
            }

        }
    }

    /**
     * 处理付款判定  一个订单被多个客服绑定
     * @param shop
     * @param adjustStartDay
     * @param adjustEndDay
     */
    private void handleMoreBindOfRulePay(JobShopDTO shop, Date adjustStartDay, Date adjustEndDay) {
        List<CsOrderBindDTO> csOrderBind = csOrderBindDao.selectOrderBindPesOrderByShopIdAndDate(shop, adjustStartDay, adjustEndDay);
        if (CollUtil.isEmpty(csOrderBind)) {
            return;
        }
        Map<Integer, List<CsOrderBindDTO>> pesOrderMap = Optional.ofNullable(csOrderBind).orElse(new ArrayList<>(0)).stream().collect(Collectors.groupingBy(CsOrderBindDTO::getType));
        //落实付款  重复绑定的数据要删除，绑定给最近的一条
        Map<String, List<CsOrderBindDTO>> groupByMap = Optional.ofNullable(pesOrderMap.get(PAY_ORDER_CS)).orElse(new ArrayList<>(0)).stream().collect(Collectors.groupingBy(e -> e.getCsNick() + CommonConstants.DELIMITER + e.getBuyerNick() + CommonConstants.DELIMITER + e.getOrderId()));
        //某一天的算绩效的落实付款的绑定
        Map<String, List<CsOrderBindDTO>> csOrderBindDTOMap = new HashMap<>();
        List<String> bindOrder = Lists.newArrayList();
        //预售多次落实付定金并且不在同一天需要删除的订单
        Set<Long> deletePresaleBindOrder = new HashSet<>(csOrderBind.size());
        Map<Long, List<CsOrderBindDTO>> presaleBargainOrder = Optional.ofNullable(csOrderBind).orElse(new ArrayList<>(0)).stream().filter(ele -> BaseUtils.getNonNull(ele.getPresale())).collect(Collectors.groupingBy(CsOrderBindDTO::getOrderId));
        groupByMap.forEach((k, v) -> {
            if (CollUtil.isNotEmpty(v) && v.size() > 1) {
                for (CsOrderBindDTO csOrderBindDTO : v) {
                    Long orderId = csOrderBindDTO.getOrderId();
                    List<CsOrderBindDTO> tBargainBind = presaleBargainOrder.get(orderId);
                    if (CollUtil.isNotEmpty(tBargainBind) && tBargainBind.size() > 1) {
                        deletePresaleBindOrder.add(orderId);
                    }
                    String yearMonth;
                    try {
                        yearMonth = DateFormatUtils.formatYMd(csOrderBindDTO.getDate());
                    } catch (ParseException e) {
                        throw new RuntimeException("ParseException {}", e);
                    }
                    if (csOrderBindDTOMap.containsKey(yearMonth)) {
                        if (!bindOrder.contains(k)) {
                            csOrderBindDTOMap.get(yearMonth).add(csOrderBindDTO);
                            bindOrder.add(k);
                            break;
                        }
                    }
                    List<CsOrderBindDTO> dtos = new ArrayList<>();
                    dtos.add(csOrderBindDTO);
                    csOrderBindDTOMap.put(yearMonth, dtos);
                }
            }
        });

        csOrderBindDTOMap.forEach((yearMonth, csOrderBindDTOs) -> {
            Date yearMonthDate;
            try {
                yearMonthDate = DateFormatUtils.parseYMd(yearMonth);
            } catch (ParseException e) {
                throw new RuntimeException("ParseException {}", e);
            }
            if (csOrderBindDTOs == null) return;
            List<Long> ids = csOrderBindDTOs.stream().filter(ele -> !BaseUtils.getNonNull(ele.getPresale()) ||
                    BaseUtils.getNonNull(ele.getPresale()) && deletePresaleBindOrder.contains(ele.getOrderId()) && ele.getOrderValidPayTime() == null
            ).map(CsOrderBindDTO::getId).collect(Collectors.toList());
            csOrderBindDao.deleteCsOrderBindPesOrderById(shop, yearMonthDate, ids);
        });
    }

    /**
     * 处理下单优先  一个订单被多个客服绑定
     * @param shop
     * @param adjustStartDay
     * @param adjustEndDay
     * @throws ParseException
     */
    private void handleMoreBindOfRuleOrderFirst(JobShopDTO shop, Date adjustStartDay, Date adjustEndDay) throws ParseException {
        List<CsOrderBindDTO> csOrderBindDTOS = csOrderBindDao.selectOrderBindPesOrderByShopIdAndDate(shop, adjustStartDay, adjustEndDay);
        Map<Integer, List<CsOrderBindDTO>> pesOrderMap = Optional.ofNullable(csOrderBindDTOS).orElse(new ArrayList<>(0)).stream().collect(Collectors.groupingBy(CsOrderBindDTO::getType));
        //获取落实下单客服和订单 ID
        Set<Long> orderIdSet = Optional.ofNullable(pesOrderMap.get(CREATED_ORDER_CS)).orElse(new ArrayList<>(0)).stream().map(CsOrderBindDTO::getOrderId).collect(Collectors.toSet());
        //落实付款  过滤掉落实下单客服订单
        List<CsOrderBindDTO> payCsOrder = Optional.ofNullable(pesOrderMap.get(PAY_ORDER_CS)).orElse(new ArrayList<>(0)).stream().filter(csOrderBindDTO-> orderIdSet.contains(csOrderBindDTO.getOrderId())).collect(Collectors.toList());
        //静默订单 过滤掉落实下单客服订单
        List<CsOrderBindDTO> silentOrder = Optional.ofNullable(pesOrderMap.get(SILENT_ORDER_CS)).orElse(new ArrayList<>(0)).stream().filter(csOrderBindDTO-> orderIdSet.contains(csOrderBindDTO.getOrderId())).collect(Collectors.toList());
        List<CsOrderBindDTO> cbdtos=new ArrayList();
        cbdtos.addAll(payCsOrder);
        cbdtos.addAll(silentOrder);
        Map<String, List<CsOrderBindDTO>> csOrderBindDTOMap = new HashMap<>();
        for (CsOrderBindDTO csOrderBindDTO : cbdtos) {
            String yearMonth = DateFormatUtils.formatYMd(csOrderBindDTO.getDate());
            if(csOrderBindDTOMap.containsKey(yearMonth)){
                csOrderBindDTOMap.get(yearMonth).add(csOrderBindDTO);
                continue;
            }
            List<CsOrderBindDTO> dtos = new ArrayList<>();
            dtos.add(csOrderBindDTO);
            csOrderBindDTOMap.put(yearMonth, dtos);
        }
        csOrderBindDTOMap.forEach((yearMonth,csOrderBindDTOs)->{
            Date yearMonthDate;
            try {
                yearMonthDate= DateFormatUtils.parseYMd(yearMonth);
            } catch (ParseException e) {
               throw new RuntimeException("ParseException {}",e);
            }
            if (csOrderBindDTOs==null) return;
            //重复绑定处理
            Map<Long, Long> kidVOrderid = csOrderBindDTOs.stream().collect(Collectors.toMap(CsOrderBindDTO::getId, CsOrderBindDTO::getOrderId, (oldValue, newValue) -> oldValue));
            List<CsOrderBindInfoDTO> hasBindInfo = csOrderBindDao.selectBindOrderByOrderIdAndType(shop, new ArrayList<>(kidVOrderid.values()), adjustStartDay, DateFormatUtils.getDateByPeriod(yearMonthDate, -1), PAY_ORDER_CS);
            //询单有效期内绑定过落实付款的不能重复绑定
            List<Long> deleteOrderBindId = Lists.newArrayList();
            List<Long> updateOrderBindId = Lists.newArrayList();
            List<Long> hasBindOrderid = Lists.newArrayList();
            if (CollUtil.isNotEmpty(hasBindInfo)) {
                hasBindOrderid = hasBindInfo.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList());
            }

            for (Entry<Long, Long> entry : kidVOrderid.entrySet()) {
                Long id = entry.getKey();
                Long orderId = entry.getValue();
                if (CollUtil.isNotEmpty(hasBindOrderid)) {
                    if (hasBindOrderid.contains(orderId)) {
                        deleteOrderBindId.add(id);
                    } else {
                        updateOrderBindId.add(id);
                    }
                } else {
                    updateOrderBindId.add(id);
                }

            }
            csOrderBindDao.deleteCsOrderBindPesOrderById(shop, yearMonthDate, deleteOrderBindId);
            csOrderBindDao.updateCsOrderBindPesOrderById(shop, yearMonthDate, updateOrderBindId, false);
        });
    }

    /**
     * 处理落实付尾款算了绩效，落实付定金没算绩效的情况（将绩效算给落实付定金）
     * @param shop
     * @param sys
     * @param startDate
     * @param endDate
     */
    private void handleBalancePayIsPesOrder(JobShopDTO shop, ShopSystemsettingDTO sys, Date startDate, Date endDate) {
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置查询数据的有效时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(endDate);
        //设置询单的有效时间
        validDateRange.setAdjustEnquiryStartDate(DateUtil.getDateByPeriod(startDate, -ValidDateRangeQuery.validPresaleOrderBalancePayDays - sys.getEnquiryValidDurationTime()));
        validDateRange.setAdjustEnquiryEndDate(endDate);
        //获取预售付尾款绑定的记录
        List<Long> orderIds = new ArrayList<>();
        Optional<List<CsOrderBindDTO>> balanceOptional = Optional.ofNullable(csOrderBindDao.selectBalancePayOrderBindByDateAndTypeAndPesOrderAndOrderIds(shop, validDateRange, 4, 1, orderIds));
        if(balanceOptional.isPresent()) {
            List<CsOrderBindDTO> balanceBindLst = balanceOptional.get();
            List<Long> orderIdLst = balanceBindLst.stream().map(CsOrderBindDTO::getOrderId).collect(Collectors.toList());
            Optional<List<CsOrderBindDTO>> bargainOptional = Optional.ofNullable(csOrderBindDao.selectBalancePayOrderBindByDateAndTypeAndPesOrderAndOrderIds(shop, validDateRange, 2, 0, orderIdLst));
            List<CsOrderBindDTO> createBindOds = csOrderBindDao.selectBalancePayOrderBindByDateAndTypeAndPesOrderAndOrderIds(shop, validDateRange, 1, 1, orderIdLst);
            Set<Long> createBindOdIds = createBindOds.stream().map(CsOrderBindDTO::getOrderId).collect(Collectors.toSet());
            Optional<List<CsOrderBindDTO>> bargainOptional2 = Optional.ofNullable(csOrderBindDao.selectBalancePayOrderBindByDateAndTypeAndPesOrderAndOrderIds(shop, validDateRange, 1, 0, orderIdLst));
            if (bargainOptional.isPresent()) {
                List<CsOrderBindDTO> csOrderBindDTOS = bargainOptional.get();
                Set<Long> uniqueOrderIds = csOrderBindDTOS.stream().map(CsOrderBindDTO::getOrderId).collect(Collectors.toSet());
                if(bargainOptional2.isPresent()){
                    List<CsOrderBindDTO> csOrderBindsType1 = bargainOptional2.get();
                    csOrderBindsType1.stream().filter(t -> !uniqueOrderIds.contains(t.getOrderId())).forEach(csOrderBindDTOS::add);
                }
                Map<Long, List<CsOrderBindDTO>> balanceOrderMap = balanceBindLst.stream().collect(Collectors.groupingBy(CsOrderBindDTO::getOrderId));
                //更新预售付尾款不算绩效的容器
                List<CsOrderBindDTO> updateBalanceNotPesOrder = new ArrayList<>();
                //更新预售预售付定金算绩效的容器
                List<CsOrderBindDTO> updateBargainPesOrder = new ArrayList<>();

                for (CsOrderBindDTO bargainCsBind : bargainOptional.get()) {
                    Optional<List<CsOrderBindDTO>> updateBalanceOptional = Optional.ofNullable(balanceOrderMap.get(bargainCsBind.getOrderId()));
                    if(updateBalanceOptional.isPresent()) {
                        updateBalanceNotPesOrder.addAll(updateBalanceOptional.get());
                        updateBargainPesOrder.add(bargainCsBind);
                    }
                }
                if (CollectionUtils.isNotEmpty(updateBalanceNotPesOrder)) {
                    Map<Date, List<CsOrderBindDTO>> monthMap = updateBalanceNotPesOrder.stream().collect(Collectors.groupingBy(ele -> DateUtil.getYM(ele.getDate())));
                    for (Map.Entry<Date, List<CsOrderBindDTO>> monthDate : monthMap.entrySet()) {
                        csOrderBindDao.batchUpdateCsOrderBindOrderPesOrder(shop, monthDate.getKey(), monthDate.getValue().stream().map(CsOrderBindDTO::getId).collect(Collectors.toList()),0);
                    }
                }
                if (CollectionUtils.isNotEmpty(updateBargainPesOrder)) {
                    updateBargainPesOrder.removeIf(t -> createBindOdIds.contains(t.getOrderId()));
                    Map<Date, List<CsOrderBindDTO>> monthMap = updateBargainPesOrder.stream().collect(Collectors.groupingBy(ele -> DateUtil.getYM(ele.getDate())));
                    for (Map.Entry<Date, List<CsOrderBindDTO>> monthDate : monthMap.entrySet()) {
                        csOrderBindDao.batchUpdateCsOrderBindOrderPesOrder(shop, monthDate.getKey(), monthDate.getValue().stream().map(CsOrderBindDTO::getId).collect(Collectors.toList()),1);
                    }
                }
            }

        }

    }

    private void handlePresalePayBalance(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date endDate, Date adjustStartDay, Date adjustEndDay) throws Exception {
        Boolean orderFlagSwitch = sys.getOrderFlagSwitch();
        Integer orderFlag = sys.getOrderFlag();
        //处理预售订单没有询单有效期付尾款没有绑定算落实付款
        List<CsOrderIndexDTO> presaleOrderIndex = csOrderIndexDao.selectPresaleOrderIndexByShopIdAndDate(shop, date, endDate);
        if(CollUtil.isEmpty(presaleOrderIndex)){
            return;
        }
            List<Long> bindOrderLst = csOrderBindDao.selectShopBindOrderIdsByOrderIdSetAndDate(shop, presaleOrderIndex.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toList()), adjustStartDay, adjustEndDay);
            List<CsOrderIndexDTO> collect = presaleOrderIndex.stream().filter(ele -> {
                if (CollUtil.isNotEmpty(bindOrderLst)) {
                    return !bindOrderLst.contains(ele.getOrderId());
                } else {
                    return ele.getOrderId() != null;
                }
            }).collect(Collectors.toList());
            // 只有一个指标并且在询单有效期内付尾款打上落实付款并算绩效
            if (CollUtil.isNotEmpty(collect)) {
                List<CsOrderIndexDTO> bindOrderIndexLst = new ArrayList<>();
                Map<Long, List<CsOrderIndexDTO>> collect1 = collect.stream().collect(Collectors.groupingBy(CsOrderIndexDTO::getOrderId));
                for (Entry<Long, List<CsOrderIndexDTO>> entry : collect1.entrySet()) {
                    List<CsOrderIndexDTO> orderIndex = entry.getValue();
                    if (CollUtil.isNotEmpty(orderIndex) && orderIndex.size() == 1) {
                        bindOrderIndexLst.addAll(orderIndex);

                    }
                }
                if (CollUtil.isNotEmpty(bindOrderIndexLst)) {
                    List<PresaleOrderDTO> presaleOrder = presaleOrderDao.selectShopOrderBlancePayTimeInAdjustDate(shop, adjustStartDay, adjustEndDay, bindOrderIndexLst.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toList()));
                    Map<String, List<CsOrderBindDO>> csOrderBindDTOMap = new HashMap<>();
                    Map<Long, PresaleOrderDTO> preOrder = presaleOrder.stream().collect((Collectors.toMap(PresaleOrderDTO::getOrderId, a -> a, (k1, k2) -> k1)));
                    Set<Long> payOrderIdSet = preOrder.keySet();
                    //fix:查询预售付尾款的订单信息
                    //设置预售的有效时间
                    Date presaleStartDate = DateUtil.getDateByPeriod(date, -ValidDateRangeQuery.validPresaleOrderBalancePayDays);
                    //(询单有效时长)
                    Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
                    Date presaleEndDate = DateUtil.getDateByPeriod(date, enquiryValidDays == null ? 0 : enquiryValidDays - 1);
                    List<PresaleOrderDTO> presaleOrderDTOS = presaleOrderDao.selectPresaleOrderByOrderId(shop, new ArrayList<>(payOrderIdSet), presaleStartDate, presaleEndDate);
                    //获取付全款的订单ID
                    Set<Long> fullAmountOrderIds = presaleOrderDTOS.stream().filter(ele -> ele.getOrderPayType() == 1)
                            .map(PresaleOrderDTO::getOrderId).collect(Collectors.toSet());
                    if (CollUtil.isNotEmpty(payOrderIdSet)) {
                        for (CsOrderIndexDTO csOrderIndexDTO : bindOrderIndexLst) {
                            //预售付全款的过滤掉 fix_bug2565
                            if(fullAmountOrderIds.contains(csOrderIndexDTO.getOrderId())){
                                continue;
                            }
                            /*
                             * 插旗订单，不算绩效
                             */
                            if (performanceRuleBusiness.isOrderBannerMark(orderFlagSwitch, orderFlag, csOrderIndexDTO)) {
                                continue;
                            }
                            if (payOrderIdSet.contains(csOrderIndexDTO.getOrderId())) {
                                if (csOrderIndexDTO.getFirstReplyDate() != null && csOrderIndexDTO.getOrderPayDate() != null && csOrderIndexDTO.getOrderPayDate().before(csOrderIndexDTO.getFirstReplyDate()))
                                    continue;
                                PresaleOrderDTO pOrder = preOrder.get(csOrderIndexDTO.getOrderId());
                                String yearMonth = DateFormatUtils.formatYMd(csOrderIndexDTO.getDate());
                                CsOrderBindDO bind = new CsOrderBindDO(shop.getShopId(), date, csOrderIndexDTO.getOrderId());
                                bind.setBalancePay(true);
                                bind.setBuyerNick(csOrderIndexDTO.getBuyerNick());
                                bind.setCsNick(csOrderIndexDTO.getCsNick());
                                bind.setOrderCreated(csOrderIndexDTO.getOrderCreated());
                                bind.setOrderPayment(pOrder.getPayBalanceReal());
                                bind.setOrderPayDate(pOrder.getBalanceTime());
                                bind.setOrderValidPayTime(pOrder.getBalanceTime());
                                bind.setOrderValidPayment(pOrder.getPayBalanceReal());
                                bind.setOrderPostFee(pOrder.getFreight());
                                bind.setOrderGoodsNum(csOrderIndexDTO.getOrderGoodsNum());
                                bind.setSilentFlag(csOrderIndexDTO.getSilentFlag());
                                bind.setPresale(csOrderIndexDTO.getPreSale());
                                bind.setIsGoodsFilter(csOrderIndexDTO.getIsGoodsFilte());//商品过滤
                                if (sys.getJudgeRule() == PesConstants.JUDGE_RULE_ORDER_FIRST || sys.getJudgeRule() == PesConstants.JUDGE_RULE_PAY) {
                                    bind.setIsPesOrder(Boolean.TRUE);
                                } else {
                                    bind.setIsPesOrder(Boolean.FALSE);
                                }
                                //1.付款前接待时间在付定金之前落实付定金  2.付款前接待时间在付尾款之前落实付尾款
                                Date bplastReplyDate = csOrderIndexDTO.getBpLastReplyDate();
                                if(bplastReplyDate==null){
                                    continue;
                                }
                                if(pOrder.getBargainTime()==null&& pOrder.getBalanceTime()==null){
                                    continue;
                                }
                                if(pOrder.getBargainTime()!=null){
                                    if (bplastReplyDate.before(pOrder.getBargainTime())) {
                                        bind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType());
                                    }
                                }

                                if(pOrder.getBalanceTime()!=null){
                                    if (bplastReplyDate.before(pOrder.getBalanceTime())) {
                                        bind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_BALANCE_PAY.getType());
                                    }
                                }
                                if (bind.getType() == null) {
                                    continue;
                                }
                                bind.setPayType(csOrderIndexDTO.getPayType());
//                                bind.setOrderType(2);//1普通订单，2 预售订单，3预约订单
                                bind.setOrderFilteFlag(csOrderIndexDTO.getOrderFlag());
                                if (csOrderBindDTOMap.containsKey(yearMonth)) {
                                    csOrderBindDTOMap.get(yearMonth).add(bind);
                                    continue;
                                }
                                List<CsOrderBindDO> dtos = new ArrayList<>();
                                dtos.add(bind);
                                csOrderBindDTOMap.put(yearMonth, dtos);
                            }

                        }
                        if (CollUtil.isNotEmpty(csOrderBindDTOMap)) {
                            //入库
                            csOrderBindDTOMap.forEach((yearMonth, csOrderBindDTOs) -> {
                                Date yearMonthDate;
                                try {
                                    yearMonthDate = DateFormatUtils.parseYMd(yearMonth);
                                } catch (ParseException e) {
                                    throw new RuntimeException("ParseException {}", e);
                                }
                                if (csOrderBindDTOs == null) return;
                                csOrderBindDao.batchInsertCsOrderBind2(shop, yearMonthDate, csOrderBindDTOs);
                            });
                        }
                }
            }
        }
    }

    private synchronized void synDelete(List<CsOrderBindDO> sourceCsOrderBind, List<CsOrderBindDO> bindLst) {//已经绑定过的按原来的绑定为准
        bindLst.removeIf(ele -> sourceCsOrderBind.stream().map(CsOrderBindDO::getOrderId).collect(Collectors.toList()).contains(ele.getOrderId()));
    }

    /**
     * 处理全款支付的预售订单，有绩效客服也算绑定付尾款绑定
     * @param jobShop
     * @param date
     * @param csOrderBindBO
     * @return
     */
    private List<CsOrderBindDO> handleFullPaymentisBalancePay(JobShopQuery jobShop, Date date, CsOrderBindBO csOrderBindBO) {
        List<CsOrderBindDO> bindLst = csOrderBindBO.getAllOrderBindLst().stream().map(CsOrderBindJudgeBO::getCsOrderBind).collect(Collectors.toList());
        if(CollUtil.isEmpty(bindLst)||CollUtil.isEmpty(bindLst.stream().filter(CsOrderBindDO::getIsPesOrder).collect(Collectors.toList())))return Lists.newArrayList();
        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(DateUtil.getDateByPeriod(endDate, jobShop.getShopSystemsetting().getEnquiryValidDurationTime() - 1));
        //设置有效的插旗开始结束时长
        validDateRange.setAdjustOrderReMarkStartDate(startDate);
        validDateRange.setAdjustOrderReMarkEndDate(DateUtil.getDateByPeriod(endDate, 1));
        //设置预售的有效时间
        validDateRange.setAdjustStartDate(DateUtil.getDateByPeriod(validDateRange.getStartDate(), -ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustEndDate(validDateRange.getEndDate());

        List<String> buyerLst = bindLst.stream().map(CsOrderBindDO::getBuyerNick).collect(Collectors.toList());//买家
        //        查询付全款的预售订单
        List<BuyerOrderDTO> fullPaymentOrderIdLst = presaleOrderDao.selectFullPaymentOrderLstByBuyersAndDate(jobShop.getShop(), buyerLst, validDateRange);
        if(CollUtil.isEmpty(fullPaymentOrderIdLst))return Lists.newArrayList();
        Map<Long, BuyerOrderDTO> orderMap = fullPaymentOrderIdLst.stream().collect(Collectors.groupingBy(BuyerOrderDTO::getOrderId, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
        List<CsOrderBindDO> resultBind = new ArrayList<>(orderMap.size());
        //付全款的预售订单添加一条落实付尾款
        csOrderBindBO.getAllOrderBindLst().forEach(ele-> {
            CsOrderBindDO csOrderBind = ele.getCsOrderBind();
            if (csOrderBind.getIsPesOrder() && orderMap.keySet().contains(csOrderBind.getOrderId())) {
                BuyerOrderDTO order = orderMap.get(csOrderBind.getOrderId());
                csOrderBind.setOrderValidPayment(BaseUtils.getNonNull(order.getPayBargainReal()) + BaseUtils.getNonNull(order.getPayBalanceReal()));
                csOrderBind.setOrderValidPayTime(order.getBargainTime());
                CsOrderBindDO temp = new CsOrderBindDO();
                BeanUtils.copyProperties(csOrderBind, temp);
                temp.setIsPesOrder(Boolean.FALSE);
                temp.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_BALANCE_PAY.getType());
                resultBind.add(temp);
            }
        });

        return resultBind;
    }


    /**
     * 处理 指标绑定落实下单、付款时 是否算接待
     */
    private void handleCsOrderIndexIsBindReceive(JobShopDTO shop, Date date, Date startDate, Date adjustEndDay, List<CsOrderIndexDTO> csBuyerOrderIndexLst) {
        //获取下单前客服 最后接待的买家
        Set<String> bcLastReplyBuyerNickSet = getBcLastReplyBuyerNickSet(date, csBuyerOrderIndexLst);
        if (CollUtil.isEmpty(bcLastReplyBuyerNickSet)) {
            return;
        }
        List<CsOrderBindChatpeerDTO> bcLastReplyChatpeers = csChatpeerDao.selectConsultByDateAndCsNickAndBuyer(shop, DateUtil.getDateByPeriod(startDate, 1), adjustEndDay, bcLastReplyBuyerNickSet);
        Map<String, List<CsOrderBindChatpeerDTO>> chatPeerMap = bcLastReplyChatpeers.stream().collect(Collectors.groupingBy(CsOrderBindChatpeerDTO::getBuyerNick));
        for (CsOrderIndexDTO index : csBuyerOrderIndexLst) {
            //预售或 售后 跳出
            if (index.getBalancePay() || index.getAfterSale()) {
                continue;
            }
            if (date.equals(index.getDate()) && index.getBcLastReplyDate() != null
                    && index.getBcLastReplyDate().after(DateUtil.getEndTimeOfDate(index.getDate()))) {
                boolean bindReceive = false;
                if (CollectionUtils.isNotEmpty(chatPeerMap.get(index.getBuyerNick()))) {
                    for (CsOrderBindChatpeerDTO dto : chatPeerMap.get(index.getBuyerNick())) {
                        if (dto.getCsNick().equals(index.getCsNick()) && index.getDate().equals(dto.getDate()) && dto.getIsReceive()) {
                            bindReceive = true;
                            break;
                        }
                    }
                }
                //指标绑定落实下单时不算接待
                index.setBindOrderCreatedReceive(bindReceive);
            }
            if (date.equals(index.getDate()) && index.getBpLastReplyDate() != null
                    && index.getBpLastReplyDate().after(DateUtil.getEndTimeOfDate(index.getDate()))) {
                boolean bindReceive = false;
                if (CollectionUtils.isNotEmpty(chatPeerMap.get(index.getBuyerNick()))) {
                    for (CsOrderBindChatpeerDTO dto : chatPeerMap.get(index.getBuyerNick())) {
                        if (dto.getCsNick().equals(index.getCsNick()) && index.getDate().equals(dto.getDate()) && dto.getIsReceive()) {
                            bindReceive = true;
                            break;
                        }
                    }
                }
                //指标绑定落实付款时不算接待
                index.setBindOrderPaidReceive(bindReceive);
            }
        }
    }

    /**
     * 获取下单前客服 最后接待的买家
     * @param date
     * @param csBuyerOrderIndexLst
     * @return
     */
    private Set<String> getBcLastReplyBuyerNickSet(Date date, List<CsOrderIndexDTO> csBuyerOrderIndexLst) {
        Set<String> bcLastReplyBuyerNickSet = new HashSet<>();
        for (CsOrderIndexDTO csOrderIndex : csBuyerOrderIndexLst) {
            //初始化值 指标绑定落实下单时算接待
            csOrderIndex.setBindOrderCreatedReceive(true);
            //初始化值 指标绑定落实付款时算接待
            csOrderIndex.setBindOrderPaidReceive(true);
            //预售或 售后 跳出
            if (csOrderIndex.getBalancePay() || csOrderIndex.getAfterSale()) {
                continue;
            }
            if (date.equals(csOrderIndex.getDate()) && csOrderIndex.getBcLastReplyDate() != null
                    && csOrderIndex.getBcLastReplyDate().after(DateUtil.getEndTimeOfDate(csOrderIndex.getDate()))) {
                bcLastReplyBuyerNickSet.add(csOrderIndex.getBuyerNick());
            }
            if (date.equals(csOrderIndex.getDate()) && csOrderIndex.getBpLastReplyDate() != null
                    && csOrderIndex.getBpLastReplyDate().after(DateUtil.getEndTimeOfDate(csOrderIndex.getDate()))) {
                bcLastReplyBuyerNickSet.add(csOrderIndex.getBuyerNick());
            }
        }
        return bcLastReplyBuyerNickSet;
    }

    private boolean isNextDayEnquiry(Boolean enquiryLossSwitch, List<CsOrderIndexDTO> buyerOrderIndexLst, Date date) {
        /*
         * 存在一个在当天当天咨询后当天下单、当天咨询第二天下单的就算询单，否则就跳过(过滤不符合要求的询单)
         */
        if (enquiryLossSwitch) {
            Date lastReplyDate;
            for (CsOrderIndexDTO orderIndex : buyerOrderIndexLst) {

                lastReplyDate = orderIndex.getLastReplyDate();
                if (lastReplyDate != null) {
                    if (DateUtils.isSameDay(lastReplyDate, date)) {
                        return false;
                    }
                }
            }
            return true;
        }
        return false;
    }


    /**
     * 客服绩效订单处理
     *
     * @param jobShop
     * @param date
     * @param csOrderBindBO
     * @param judgeRule
     */
    private void csOrderPesBind(JobShopQuery jobShop,
                                Date date,
                                CsOrderBindBO csOrderBindBO,
                                int judgeRule) {
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        final Boolean silentUrgepaySwitch = sys.getSilentUrgepaySwitch();

        //落实下单的订单集合
        Set<Long> toOrderedOrderIdSet = csOrderBindBO.getToOrderedOrderIdSet();
        //落实付款的订单集合
        Set<Long> toPayOrderIdSet = csOrderBindBO.getToPayOrderIdSet();

        CsOrderBindDO csOrderBind;
        switch (JudgeRuleEnum.getEnumByType(judgeRule)) {
            // 下单判定
            case JUDGE_RULE_ORDER:
                // 落实下单
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsToOrderedOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();

                    if (csOrderBind.getOrderFilteFlag() == 0) {//无过滤则绑定
                        csOrderBind.setIsPesOrder(Boolean.TRUE);
                    }
                }
                //全静默绑定
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getToSilentAllOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();

                    if (!csOrderBind.getBalancePay()) {//非付尾款订单
                        if (toOrderedOrderIdSet.contains(csOrderBind.getOrderId()) ||
                                toPayOrderIdSet.contains(csOrderBind.getOrderId())) {
                            //什么也不做
                        } else {
                            if (sys.getSilentAllSwitch() && BaseUtils.getNonNull(sys.getIsBindSilentAllOrder())) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }
                    }
                }
                break;
            // 下单优先判定
            case JUDGE_RULE_ORDER_FIRST:
                // 落实下单
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsToOrderedOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();
                    if (csOrderBind.getOrderFilteFlag() == 0) {//无过滤则绑定
                        csOrderBind.setIsPesOrder(Boolean.TRUE);
                    }
                }

                // 落实付款
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsToPaidOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();

                    //啥也不做，被落实下单算绩效，不能再被落实付款去算绩效
                    if (toOrderedOrderIdSet.contains(csOrderBind.getOrderId())) continue;

                    if (!csOrderBind.getBalancePay() || csOrderBindJudge.getAllPay()) {//非预售付尾款的订单 全款订单
                        if (csOrderBind.getOrderFilteFlag() == 0) {//无过滤则绑定
                            //@TODO(催付：静默下单，跟进限制违规，不算落实付款)
                            if (!performanceRuleBusiness.isUrgePayLimitOrder(silentUrgepaySwitch, csOrderBindJudge)) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }

                    }
                }

                // 预售落实付尾款
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsPresaleOrderBalancePayBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();
                    if (csOrderBind.getBalancePay() && !csOrderBindJudge.getAllPay()) {//预售付尾款的订单

                        //啥也不做，被落实下单算绩效，不能再被落实付款去算绩效
                        if (toOrderedOrderIdSet.contains(csOrderBind.getOrderId())) continue;

                        if (csOrderBind.getOrderFilteFlag() == 0) {//无过滤则绑定
                            //@TODO(催付：静默下单，跟进限制违规，不算落实付款)
                            if (!performanceRuleBusiness.isUrgePayLimitOrder(silentUrgepaySwitch, csOrderBindJudge)) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }

                    }
                }

                //全静默绑定
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getToSilentAllOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();
                    if (!csOrderBind.getBalancePay()) {//非付尾款订单
                        if (toOrderedOrderIdSet.contains(csOrderBind.getOrderId())
                                || toPayOrderIdSet.contains(csOrderBind.getOrderId())) {
                        } else {

                            if (sys.getSilentAllSwitch() && BaseUtils.getNonNull(sys.getIsBindSilentAllOrder()) && !csOrderBindJudge.getOrderFlag()) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
//                                fix:1202
                            } else if (sys.getSilentAllSwitch() && !BaseUtils.getNonNull(sys.getIsBindSilentAllOrder()) && csOrderBindJudge.getPesOrder() && !csOrderBindJudge.getOrderFlag()) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }
                    }

                }

                break;
            // 付款判定
            case JUDGE_RULE_PAY:
                // 落实付款
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsToPaidOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();
                    if (!csOrderBind.getBalancePay() || csOrderBindJudge.getAllPay()) {//非预售付尾款的订单
                        if (csOrderBind.getOrderFilteFlag() == 0) {//无过滤则绑定
                            //@TODO(催付：静默下单，跟进限制违规，不算落实付款)
                            if (!performanceRuleBusiness.isUrgePayLimitOrder(silentUrgepaySwitch, csOrderBindJudge)) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }
                    }

                }

                // 预售落实付尾款
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsPresaleOrderBalancePayBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();
                    //预售付尾款的订单并且不是付全款的订单
                    if (csOrderBind.getBalancePay() && !csOrderBindJudge.getAllPay()) {

                        if (csOrderBind.getOrderFilteFlag() == 0) {//无过滤则绑定
                            //@TODO(催付：静默下单，跟进限制违规，不算落实付款)
                            if (!performanceRuleBusiness.isUrgePayLimitOrder(silentUrgepaySwitch, csOrderBindJudge)) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }

                    }
                }
                //全静默绑定
                for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getToSilentAllOrderBindLst()) {
                    csOrderBind = csOrderBindJudge.getCsOrderBind();
                    if (!csOrderBind.getBalancePay()) {//非付尾款订单
                        if (toOrderedOrderIdSet.contains(csOrderBind.getOrderId())
                                || toPayOrderIdSet.contains(csOrderBind.getOrderId())) {
                        } else {

                            if (sys.getSilentAllSwitch() && BaseUtils.getNonNull(sys.getIsBindSilentAllOrder()) && !csOrderBindJudge.getOrderFlag()) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
//                                fix:1202
                            } else if (sys.getSilentAllSwitch() && !BaseUtils.getNonNull(sys.getIsBindSilentAllOrder()) && csOrderBindJudge.getPesOrder() && !csOrderBindJudge.getOrderFlag()) {
                                csOrderBind.setIsPesOrder(Boolean.TRUE);
                            }
                        }
                    }
                }
                break;
            default:
                logger.error("judge rule error,judgeRule={}", judgeRule);
                break;
        }

        //@TODO(维护字段：付尾款的is_pes_order)
        Set<Long> orderIdSet = csOrderBindBO.getAllOrderBindLst().stream()
                .filter(x -> x.getCsOrderBind().getBalancePay() || x.getCsOrderBind().getPayType() == 1 || x.getCsOrderBind().getPayType() == 5)//付尾款
                .map(x -> x.getCsOrderBind().getOrderId())
                .collect(Collectors.toSet());
        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        validDateRange.setAdjustStartDate(DateUtil.getDateByPeriod(date, -ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustEndDate(date);

        //已被绑定的预售的订单
        Set<Long> pesOrderIdSet = csOrderBindDao.selectShopBindOrderIdsByOrderIdSet(jobShop.getShop(), orderIdSet, validDateRange);
        if (CollectionUtils.isNotEmpty(pesOrderIdSet)) {
            for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getAllOrderBindLst()) {
                csOrderBind = csOrderBindJudge.getCsOrderBind();
                if (csOrderBind.getPresale() != null && csOrderBind.getPresale()) {

                    if (pesOrderIdSet.contains(csOrderBind.getOrderId())) {
                        csOrderBind.setIsPesOrder(Boolean.FALSE);
                    }
                }

                //货到付款和公司转账类型订单
                if (csOrderBind.getPayType() == 1 || csOrderBind.getPayType() == 5) {

                    if (csOrderBind.getType() == 2 && pesOrderIdSet.contains(csOrderBind.getOrderId())) {
                        csOrderBind.setIsPesOrder(Boolean.FALSE);
                    }
                }
            }
        }
        Set<Long> preSaleNotPayOrderIdSet = csOrderBindBO.getAllOrderBindLst().stream()
                .filter(x -> x.getCsOrderBind().getPresale() && !x.getCsOrderBind().getBalancePay())//预售（下单或者付定金）
                .map(x -> x.getCsOrderBind().getOrderId())
                .collect(Collectors.toSet());

        Date endDate = com.pes.jd.ms.utils.DateUtils.getEndTimeOfDate(new Date());
        List<BuyerOrderDTO> presaleBalancePayOrderLst = presaleOrderDao.selectPresaleOrderInfoByOrderLst(jobShop.getShop(), new ArrayList<>(preSaleNotPayOrderIdSet), date, endDate);
        if (CollectionUtils.isNotEmpty(presaleBalancePayOrderLst)) {
            Map<Long, BuyerOrderDTO> presaleBalancePayOrderMap = presaleBalancePayOrderLst.stream()
                    .collect(Collectors.toMap(BuyerOrderDTO::getOrderId, x -> x, (x1, x2) -> x1));
            BuyerOrderDTO temp;
            for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsToOrderedOrderBindLst()) {
                csOrderBind = csOrderBindJudge.getCsOrderBind();
                if (csOrderBind.getPresale() != null && csOrderBind.getPresale()) {
                    temp = presaleBalancePayOrderMap.get(csOrderBind.getOrderId());
                    if (temp != null && temp.getBalancePayTime()!=null) {
                        double payment = NumberUtil.add(temp.getBargainPayment() == null ? 0.00 : temp.getBargainPayment(), temp.getBalancePayment() == null ? 0.00 : temp.getBalancePayment());
                        csOrderBind.setOrderValidPayment(payment);
                        csOrderBind.setOrderValidPayTime(temp.getBalancePayTime());
                    }
                }
            }
            for (CsOrderBindJudgeBO csOrderBindJudge : csOrderBindBO.getCsToPaidOrderBindLst()) {
                csOrderBind = csOrderBindJudge.getCsOrderBind();
                if (csOrderBind.getPresale() != null && csOrderBind.getPresale()) {
                    temp = presaleBalancePayOrderMap.get(csOrderBind.getOrderId());
                    if (temp != null && temp.getBalancePayTime()!=null) {
                        double payment = NumberUtil.add(temp.getBargainPayment() == null ? 0.00:temp.getBargainPayment(), temp.getBalancePayment() == null ? 0.00 : temp.getBalancePayment());
                        csOrderBind.setOrderValidPayment(payment);
                        csOrderBind.setOrderValidPayTime(temp.getBalancePayTime());
                    }
                }
            }
        }
    }

    private void initChatpeerInfo(CsOrderBindChatpeerDTO cp) {
        cp.setIsAfterSale(false);
        cp.setIsCsConsultFirst(false);
        cp.setIsCsSingleChatFilter(false);
        cp.setIsAssist(false);
        cp.setIsNextDayPes(false);
        cp.setIsOrderCreated(false);
        cp.setIsAfterSale(false);
        cp.setIsEnquiry(false);
        cp.setIsNextDayPes(false);
    }


    private void updateCsOrderIndexs(JobShopQuery jobShop, Date date, String csNick, List<CsOrderIndexDTO> csOrderIndeLst) {

        if (CollectionUtils.isNotEmpty(csOrderIndeLst)) {

            List<CsOrderIndexDTO> filtedLst = csOrderIndeLst.stream().filter(ele -> csNick.equals(ele.getCsNick()) && date.equals(ele.getDate()) &&
                    (ele.getAssitOrderCreate() != null || ele.getAssitOrderInFollowup() != null || ele.getAssitOrderPay() != null)
            ).collect(Collectors.toList());

            List<List<CsOrderIndexDTO>> lst = CollectionUtil.smallToLst(filtedLst, CommonConstants.BATCH_UPDATE_LIMIT_NUM);
            for (List<CsOrderIndexDTO> subLst : lst) {
                csOrderIndexDao.updateCsOrderIndexsForAssist(jobShop.getShop(), date, subLst);
            }
        }
    }


    private void updateCsAftersaleReceive(JobShopQuery jobShop, Date date, String csNick, List<CsOrderIndexDTO> csOrderIndeLst) {

        if (CollectionUtils.isNotEmpty(csOrderIndeLst)) {

            List<CsOrderIndexDTO> lst = csOrderIndeLst.stream().filter(CsOrderIndexDTO::getAfterSale).collect(Collectors.toList());
            int num = csChatpeerDao.batchUpdateCsAfterSaleReceive(jobShop.getShop(), date, lst);
            logger.info("batch update cs after sale receive num:{}", num);
        }
    }


    /**
     * 根据客服和买家的最后聊天时间判定绩效
     *
     * @param
     * @param jobShop
     * @param sDate
     * @param date
     * @param csNick
     * @param csOrderIndexLst
     * @param chatpeers
     * @param receiveFilterChatPeerLst
     * @return
     * @throws ParseException
     */
    private CsOrderBindBO assembledCsOrderBindByJudgeRuleAscription(JobShopQuery jobShop,
                                                                    Date sDate, Date date,
                                                                    String csNick,
                                                                    List<CsOrderIndexDTO> csOrderIndexLst,
                                                                    List<CsOrderBindChatpeerDTO> chatpeers,
                                                                    List<Long> parentOrderIdList,
                                                                    List<CommonCsChatpeerDTO> receiveFilterChatPeerLst) {
        JobShopDTO shop = jobShop.getShop();
        Date edate = DateUtil.getEndTimeOfDate(date);
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //静默下单催付时间限制开关
        //静默下单催付时间限制
        Boolean orderFlagSwitch = sys.getOrderFlagSwitch();
        Integer orderFlag = sys.getOrderFlag();
        CsOrderBindBO bo = new CsOrderBindBO();
        //落实下单集合
        List<CsOrderBindJudgeBO> csToOrderedOrderBindLst = Lists.newArrayList();
        bo.setCsToOrderedOrderBindLst(csToOrderedOrderBindLst);
        //落实付款集合
        List<CsOrderBindJudgeBO> csToPaidOrderBindLst = Lists.newArrayList();
        bo.setCsToPaidOrderBindLst(csToPaidOrderBindLst);
        //全静默绑定集合
        List<CsOrderBindJudgeBO> toSilentAllOrderBindLst = Lists.newArrayList();
        bo.setToSilentAllOrderBindLst(toSilentAllOrderBindLst);
        //落实预售付尾款集合
        List<CsOrderBindJudgeBO> csPresaleOrderBalancePayBindLst = Lists.newArrayList();
        bo.setCsPresaleOrderBalancePayBindLst(csPresaleOrderBalancePayBindLst);
        //所有落实的订单
        List<CsOrderBindJudgeBO> allOrderBindLst = Lists.newArrayList();
        bo.setAllOrderBindLst(allOrderBindLst);
        //已被落实下单的订单ID集合
        Set<Long> toOrderedOrderIdSet = Sets.newHashSet();
        bo.setToOrderedOrderIdSet(toOrderedOrderIdSet);
        //已被落实付款的订单ID集合
        Set<Long> toPayOrderIdSet = Sets.newHashSet();
        bo.setToPayOrderIdSet(toPayOrderIdSet);
        //已被插旗的订单ID集合
        Set<Long> toPlaceFlagOrderIdSet = Sets.newHashSet();
        //区分是否是是预售付尾款
        Map<Boolean, List<CsOrderIndexDTO>> balancePayMap = csOrderIndexLst.stream().collect(Collectors.groupingBy(CsOrderIndexDTO::getBalancePay));
        //根据订单分组
        Map<Long, List<CsOrderIndexDTO>> orderIndexMap = csOrderIndexLst.stream().collect(Collectors.groupingBy(CsOrderIndexDTO::getOrderId));

        //设置预售的有效时间
        Date presaleStartDate = DateUtil.getDateByPeriod(date, -ValidDateRangeQuery.validPresaleOrderBalancePayDays);
        //(询单有效时长)
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        Date presaleEndDate = DateUtil.getDateByPeriod(date, enquiryValidDays == null ? 0 : enquiryValidDays - 1);
        //预售付尾款的订单指标信息
        List<CsOrderIndexDTO> presaleBalancePay = Optional.ofNullable(balancePayMap.get(Boolean.TRUE)).orElse(new ArrayList<>(0));
        List<Long> presaleBalancePayOrderIds = presaleBalancePay.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toList());
        //非预售付尾款的订单指标信息
        List<CsOrderIndexDTO> noPresaleBalancePay = Optional.ofNullable(balancePayMap.get(Boolean.FALSE)).orElse(new ArrayList<>(0));
        List<Long> noPresaleBalancePayOrderIds = noPresaleBalancePay.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toList());
        //fix:查询预售付尾款的订单信息
        List<PresaleOrderDTO> presaleOrderDTOS = presaleOrderDao.selectPresaleOrderByOrderId(jobShop.getShop(), presaleBalancePayOrderIds, presaleStartDate, presaleEndDate);
        List<PresaleOrderDTO> noPresaleOrderDTOS = presaleOrderDao.selectPresaleOrderByOrderId(jobShop.getShop(), noPresaleBalancePayOrderIds, presaleStartDate, presaleEndDate);
        //从付尾款的预售订单中，过滤出全款支付的订单
        List<PresaleOrderDTO> fullaMountPresaleOrderDTOS = presaleOrderDTOS.stream().filter(ele -> ele.getOrderPayType() == 1).collect(Collectors.toList());//全款支付的订单信息
        List<Long> presaleOrderIds = Optional.ofNullable(fullaMountPresaleOrderDTOS).orElse(new ArrayList<>(0)).stream().map(PresaleOrderDTO::getOrderId).collect(Collectors.toList());
        List<CsOrderIndexDTO> fullaMountCsOrderIndexDTO = presaleBalancePay.stream().filter(csOrderIndexDTO -> presaleOrderIds.contains(csOrderIndexDTO.getOrderId())).collect(Collectors.toList());
        //所有预售订单分组
        List<PresaleOrderDTO> allPresaleOrderDTOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(presaleOrderDTOS)) {
            allPresaleOrderDTOS.addAll(presaleOrderDTOS);
        }
        if (CollUtil.isNotEmpty(noPresaleOrderDTOS)) {
            allPresaleOrderDTOS.addAll(noPresaleOrderDTOS);
        }
        Map<Long, PresaleOrderDTO> presaleLMap = allPresaleOrderDTOS.stream().collect(Collectors.toMap(PresaleOrderDTO::getOrderId, v -> v, (x, y) -> x));

        if (cn.hutool.core.collection.CollectionUtil.isEmpty(balancePayMap.get(Boolean.FALSE))) {
            //处理预售付全款的订单
            balancePayMap.put(Boolean.FALSE, fullaMountCsOrderIndexDTO);
        } else {
            balancePayMap.get(Boolean.FALSE).addAll(fullaMountCsOrderIndexDTO);
        }
        //移除重复数据, 防止重复绑定
        List<CsOrderIndexDTO> csOrderIndexDTOSForRemove =  balancePayMap.get(Boolean.TRUE);
        if(CollectionUtils.isNotEmpty(csOrderIndexDTOSForRemove)){
            Set<Long> toCompareOrderIds = fullaMountCsOrderIndexDTO.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toSet());
            csOrderIndexDTOSForRemove.removeIf(csOrderIndexDTO -> toCompareOrderIds.contains(csOrderIndexDTO.getOrderId()));
        }
        //预售付尾款绑定的订单
        Set<Long> balancePayBindOrderSet = new HashSet<>();
        //先算付尾款的订单再算不是付尾款的顺序不能错
        List<CsOrderIndexDTO> balanceCsOrderIndex = balancePayMap.get(Boolean.TRUE);
        //@TODO(预售付尾款处理)
        if(CollUtil.isNotEmpty(balanceCsOrderIndex)) {
            //包含其他客服指标（一单对应多个客服）
            Map<Long, List<CsOrderIndexDTO>> orderMap = balanceCsOrderIndex.stream().collect(Collectors.groupingBy(CsOrderIndexDTO::getOrderId));
            balancePayHandle(sDate,
                    date,
                    csNick,
                    chatpeers,
                    shop,
                    edate,
                    sys,
                    orderFlagSwitch,
                    orderFlag,
                    csToOrderedOrderBindLst,
                    csPresaleOrderBalancePayBindLst,
                    csToPaidOrderBindLst,
                    allOrderBindLst,
                    toOrderedOrderIdSet,
                    toPayOrderIdSet,
                    toPlaceFlagOrderIdSet,
                    orderIndexMap,
                    presaleOrderDTOS,
                    presaleLMap,
                    orderMap,
                    balancePayBindOrderSet);
        }
        List<CsOrderIndexDTO> noBalanceCsOrderIndex = balancePayMap.get(Boolean.FALSE);
        //@TODO(非预售付尾款处理)
        if(CollUtil.isNotEmpty(noBalanceCsOrderIndex)) {
            //包含其他客服指标（一单对应多个客服）
            Map<Long, List<CsOrderIndexDTO>> orderMap = noBalanceCsOrderIndex.stream().collect(Collectors.groupingBy(CsOrderIndexDTO::getOrderId));
            nonBalancePayHandle(sDate, date,
                    csNick,
                    chatpeers,
                    parentOrderIdList,
                    receiveFilterChatPeerLst,
                    shop,
                    edate,
                    sys,
                    orderFlagSwitch,
                    orderFlag,
                    csToOrderedOrderBindLst,
                    csPresaleOrderBalancePayBindLst,
                    csToPaidOrderBindLst,
                    toSilentAllOrderBindLst,
                    allOrderBindLst,
                    toOrderedOrderIdSet,
                    toPayOrderIdSet,
                    toPlaceFlagOrderIdSet,
                    presaleLMap,
                    orderMap,
                    orderIndexMap,
                    presaleOrderDTOS,
                    balancePayBindOrderSet);
        }
        /* ** fix:247  插旗订单不进行落实下单、落实付款绑定*/
        allOrderBindLst.forEach(ele -> {
            CsOrderBindDO csOrderBind = ele.getCsOrderBind();
            if (toPlaceFlagOrderIdSet.contains(csOrderBind.getOrderId())) {
                if (JudgeRuleTypeEnum.ORDER_BIND_TYPE_ORDER.getType() == csOrderBind.getType() || JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType() == csOrderBind.getType()) {
                    csOrderBind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_DEFAULT.getType());
                } else if (JudgeRuleTypeEnum.ORDER_BIND_TYPE_SILENTALL.getType() == csOrderBind.getType()) {
                    //全静默跟进插旗的订单不算绩效
                    ele.setPesOrder(Boolean.FALSE);
                    //插旗订单
                    ele.setOrderFlag(Boolean.TRUE);
                }
            }
        });
        return bo;
    }

    /**
     * 非预售付尾款处理
     * @param sDate
     * @param date
     * @param csNick
     * @param parentOrderIdList
     * @param receiveFilterChatPeerLst
     * @param shop
     * @param edate
     * @param sys
     * @param orderFlagSwitch
     * @param orderFlag
     * @param csToOrderedOrderBindLst
     * @param csToPaidOrderBindLst
     * @param toSilentAllOrderBindLst
     * @param allOrderBindLst
     * @param toOrderedOrderIdSet
     * @param toPayOrderIdSet
     * @param toPlaceFlagOrderIdSet
     * @param presaleLMap
     * @param orderMap
     */
    private void nonBalancePayHandle(Date sDate, Date date, String csNick,
                                     List<CsOrderBindChatpeerDTO> chatpeers,
                                     List<Long> parentOrderIdList,
                                     List<CommonCsChatpeerDTO> receiveFilterChatPeerLst,
                                     JobShopDTO shop, Date edate, ShopSystemsettingDTO sys,
                                     Boolean orderFlagSwitch, Integer orderFlag,
                                     List<CsOrderBindJudgeBO> csToOrderedOrderBindLst,
                                     List<CsOrderBindJudgeBO> csPresaleOrderBalancePayBindLst,
                                     List<CsOrderBindJudgeBO> csToPaidOrderBindLst,
                                     List<CsOrderBindJudgeBO> toSilentAllOrderBindLst, List<CsOrderBindJudgeBO> allOrderBindLst,
                                     Set<Long> toOrderedOrderIdSet,
                                     Set<Long> toPayOrderIdSet,
                                     Set<Long> toPlaceFlagOrderIdSet,
                                     Map<Long, PresaleOrderDTO> presaleLMap,
                                     Map<Long, List<CsOrderIndexDTO>> orderMap,
                                     Map<Long, List<CsOrderIndexDTO>> orderIndexMap,
                                     List<PresaleOrderDTO> presaleOrderDTOS,
                                     Set<Long> balancePayBindOrderSet) {
        Map<Long, List<PresaleOrderDTO>> presaleOrderMap = presaleOrderDTOS.stream().collect(Collectors.groupingBy(PresaleOrderDTO::getOrderId));
        for (Entry<Long, List<CsOrderIndexDTO>> orderEntry : orderMap.entrySet()) {
            Long orderId = orderEntry.getKey();
            if (parentOrderIdList.contains(orderId)) continue;
            //预售订单信息,用来判断用来过滤尾款期之外的指标
            List<PresaleOrderDTO> presaleOrder = presaleOrderMap.get(orderId);

            //boolean orderBannerFlag = false;
            //一个订单 对应多个客服
            List<CsOrderIndexDTO> orderCsIndexLst = orderEntry.getValue();
            if (orderCsIndexLst == null) return;
            for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
                /*
                 * 插旗订单，不算绩效
                 */
                if (performanceRuleBusiness.isOrderBannerMark(orderFlagSwitch, orderFlag, csOrderIndex)) {
                    csOrderIndex.setOrderBannerFilte(Boolean.TRUE);
                    //orderBannerFlag = true;
                    //@TODO(协助)
                    if (judgeIsAssist(csOrderIndex, CommonConstants.JUDGE_ASSIT_ORDER_PAY)) {
                        csOrderIndex.setAssitOrderPay(Boolean.TRUE);
                    }
                    if (judgeIsAssist(csOrderIndex, CommonConstants.JUDGE_ASSIT_ORDER_CREATE)) {
                        csOrderIndex.setAssitOrderCreate(Boolean.TRUE);
                    }
                    /* ** fix:247  插旗订单不进行落实下单、落实付款绑定*/
                    toPlaceFlagOrderIdSet.add(csOrderIndex.getOrderId());
                }
            }
            //if (!orderBannerFlag) {
            //预售订单
            PresaleOrderDTO presaleOrderDTO = presaleLMap.get(orderId);
            //落实下单
            CsOrderBindJudgeBO csOrderCreatedbind = performanceRuleBusiness.csOrderCreatedBind(shop, sys, date, edate, csNick, orderId, orderCsIndexLst, toOrderedOrderIdSet, presaleOrderDTO);
            if (csOrderCreatedbind != null) {
                csToOrderedOrderBindLst.add(csOrderCreatedbind);
                allOrderBindLst.add(csOrderCreatedbind);
            }
            //落实付款
            CsOrderBindJudgeBO csOrderPaybind = performanceRuleBusiness.csOrderPayBind(shop, sys, date, edate, csNick, orderId, orderCsIndexLst, toPayOrderIdSet, presaleOrderDTO);
            if (csOrderPaybind != null) {
                csToPaidOrderBindLst.add(csOrderPaybind);
                allOrderBindLst.add(csOrderPaybind);
            }
            //全静默绑定
            CsOrderBindJudgeBO csSilentAllBind = performanceRuleBusiness.csSilentAllBind(shop, sys, date, edate, csNick, orderId, orderCsIndexLst, receiveFilterChatPeerLst, presaleOrderDTO);
            if (csSilentAllBind != null) {
                toSilentAllOrderBindLst.add(csSilentAllBind);
                allOrderBindLst.add(csSilentAllBind);
            }
            //落实付尾款(预售)
//            if (null != presaleOrderDTO && null != presaleOrderDTO.getBalanceTime() && !balancePayBindOrderSet.contains(orderId)) {
//                CsOrderBindJudgeBO balanceBind = performanceRuleBusiness.csPresaleOrderBalancePayBind(shop,
//                        sys, date, edate,
//                        csNick, orderId, orderIndexMap.get(orderId), toPayOrderIdSet, presaleOrder,
//                        chatpeers, sDate);
//                if (balanceBind != null) {
//                    balancePayBindOrderSet.add(orderId);
//                    csPresaleOrderBalancePayBindLst.add(balanceBind);
//                    allOrderBindLst.add(balanceBind);
//                    //全款打上落实付尾款的时候打上落实付定金
////                1：全款 2: 非全款
//                    if (Objects.nonNull(presaleOrderDTO) && presaleOrderDTO.getOrderPayType() == 1) {
//                        balanceBind.setAllPay(true);
//                        CsOrderBindJudgeBO bargainBindBo = new CsOrderBindJudgeBO();
//                        com.pes.jd.util.BeanUtils.copyProperties(balanceBind, bargainBindBo);
//                        CsOrderBindDO bargainBindDO = new CsOrderBindDO();
//                        com.pes.jd.util.BeanUtils.copyProperties(balanceBind.getCsOrderBind(), bargainBindDO);
//                        bargainBindDO.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType());
//                        bargainBindBo.setCsOrderBind(bargainBindDO);
//                        bargainBindBo.setBindPayType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType());
//                        csToPaidOrderBindLst.add(bargainBindBo);
//                        allOrderBindLst.add(bargainBindBo);
//                    }
//                }
//            }
            //}
            /* *售后的订单不算协助*/
            orderCsIndexLst.forEach(ele -> {
                if (ele.getAfterSale()) {
                    ele.setAssitOrderPay(Boolean.FALSE);
                    ele.setAssitOrderCreate(Boolean.FALSE);
                }
            });
        }
    }

    /**
     * 预售付尾款处理
     * @param sDate
     * @param date
     * @param csNick
     * @param chatpeers
     * @param shop
     * @param edate
     * @param sys
     * @param orderFlagSwitch
     * @param orderFlag
     * @param csToOrderedOrderBindLst
     * @param csPresaleOrderBalancePayBindLst
     * @param allOrderBindLst
     * @param toOrderedOrderIdSet
     * @param toPayOrderIdSet
     * @param toPlaceFlagOrderIdSet
     * @param orderIndexMap
     * @param presaleOrderDTOS
     * @param presaleLMap
     * @param orderMap
     */
    private void balancePayHandle(Date sDate, Date date, String csNick,
                                  List<CsOrderBindChatpeerDTO> chatpeers,
                                  JobShopDTO shop, Date edate, ShopSystemsettingDTO sys,
                                  Boolean orderFlagSwitch, Integer orderFlag,
                                  List<CsOrderBindJudgeBO> csToOrderedOrderBindLst,
                                  List<CsOrderBindJudgeBO> csPresaleOrderBalancePayBindLst,
                                  List<CsOrderBindJudgeBO> csToPaidOrderBindLst,
                                  List<CsOrderBindJudgeBO> allOrderBindLst,
                                  Set<Long> toOrderedOrderIdSet, Set<Long> toPayOrderIdSet,
                                  Set<Long> toPlaceFlagOrderIdSet,
                                  Map<Long, List<CsOrderIndexDTO>> orderIndexMap,
                                  List<PresaleOrderDTO> presaleOrderDTOS,
                                  Map<Long, PresaleOrderDTO> presaleLMap,
                                  Map<Long, List<CsOrderIndexDTO>> orderMap,
                                  Set<Long> balancePayBindOrderSet) {
        Map<Long, List<PresaleOrderDTO>> presaleOrderMap = presaleOrderDTOS.stream().collect(Collectors.groupingBy(PresaleOrderDTO::getOrderId));
        //查询订单对应的sku
        for (Entry<Long, List<CsOrderIndexDTO>> orderEntry : orderMap.entrySet()) {
            Long orderId = orderEntry.getKey();
            //预售订单信息,用来判断用来过滤尾款期之外的指标
            List<PresaleOrderDTO> presaleOrder = presaleOrderMap.get(orderId);
            //一个订单 对应多个客服
            List<CsOrderIndexDTO> orderCsIndexLst = orderEntry.getValue();
            // 是插旗的订单不用算绩效，客服会算为协助
            for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
                /*
                 * 插旗订单，不算绩效
                 */
                if (performanceRuleBusiness.isOrderBannerMark(orderFlagSwitch, orderFlag, csOrderIndex)) {
                    csOrderIndex.setOrderBannerFilte(Boolean.TRUE);
                    //@TODO(协助)
                    if (judgeIsAssist(csOrderIndex, CommonConstants.JUDGE_ASSIT_ORDER_PAY)) {
                        csOrderIndex.setAssitOrderPay(Boolean.TRUE);
                    }
                    if (judgeIsAssist(csOrderIndex, CommonConstants.JUDGE_ASSIT_ORDER_CREATE)) {
                        csOrderIndex.setAssitOrderCreate(Boolean.TRUE);
                    }
                    /* ** fix:247  插旗订单不进行落实下单、落实付款绑定*/
                    toPlaceFlagOrderIdSet.add(csOrderIndex.getOrderId());
                }
            }

            //预售订单
            PresaleOrderDTO presaleOrderDTO = presaleLMap.get(orderId);
            //落实下单
            CsOrderBindJudgeBO csOrderCreatedbind = performanceRuleBusiness.csOrderCreatedBind(shop, sys, date, edate, csNick, orderId, orderCsIndexLst, toOrderedOrderIdSet, presaleOrderDTO);
            if (csOrderCreatedbind != null) {
                csToOrderedOrderBindLst.add(csOrderCreatedbind);
                allOrderBindLst.add(csOrderCreatedbind);
            }
            if (!balancePayBindOrderSet.contains(orderId)) {
                //落实付尾款(预售)
                CsOrderBindJudgeBO balanceBind = performanceRuleBusiness.csPresaleOrderBalancePayBind(shop,
                        sys, date, edate,
                        csNick, orderId, orderIndexMap.get(orderId), toPayOrderIdSet, presaleOrder,
                        chatpeers, sDate);
                if (balanceBind != null) {
                    balancePayBindOrderSet.add(orderId);
                    csPresaleOrderBalancePayBindLst.add(balanceBind);
                    allOrderBindLst.add(balanceBind);
                    //全款打上落实付尾款的时候打上落实付定金
//                1：全款 2: 非全款
//                    if (Objects.nonNull(presaleOrderDTO) && presaleOrderDTO.getOrderPayType() == 1) {
//                        balanceBind.setAllPay(true);
//                        CsOrderBindJudgeBO bargainBindBo = new CsOrderBindJudgeBO();
//                        com.pes.jd.util.BeanUtils.copyProperties(balanceBind, bargainBindBo);
//                        CsOrderBindDO bargainBindDO = new CsOrderBindDO();
//                        com.pes.jd.util.BeanUtils.copyProperties(balanceBind.getCsOrderBind(), bargainBindDO);
//                        bargainBindDO.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType());
//                        bargainBindBo.setCsOrderBind(bargainBindDO);
//                        bargainBindBo.setBindPayType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType());
//                        csToPaidOrderBindLst.add(bargainBindBo);
//                        allOrderBindLst.add(bargainBindBo);
//                    }
                }
            }

        }
    }

    /**
     * 客服与订单关系
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     */
    @Override
    public void handleShopCsOrderIndex(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();
        List<Date> dates = jobDate.getCsOrderIndexDates();
        //询单有效期小于三天要用三天来算，因为重拉三天前的订单信息会有三天前的付款询单有效期设置低于三天少算
        List<Date> calDate = calNewOrderIndexDate(jobDate, jobShop.getShopSystemsetting().getEnquiryValidDurationTime(), dates);
        if (CollectionUtils.isEmpty(calDate)) {
            logger.warn("req dates is empty");
            return;
        }
        JobShopDTO shop = jobShop.getShop();
        for (Date date : calDate) {
            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }
            List<CsOrderIndexDO> dayTeamOrderIndexLst = Lists.newArrayList();
            List<AsyncTask> taskList = new ArrayList<>(targetCsLst.size());
            int task = 0;
            for (CsDTO cs : targetCsLst) {
                AsyncTask asyncTask = new AsyncTask(task) {
                    @SneakyThrows
                    @Override
                    public Object run() {
                        String csNick = cs.getNick();
                        // (不包含 1:空聊天 2:团队内部过滤 3: 绩效系统客服过滤 等等其他过滤掉的... 【接待】) 询单有效期内的聊天记录
                        List<CsOrderIndexChatpeerDTO> chatpeers = csChatpeerDao.selectReceiveChatpeersByShopAndDateForCsOrderIndex(shop, date, date, csNick);
                        try {
                            if (CollectionUtils.isNotEmpty(chatpeers)) {
                                List<String> buyerLst = chatpeers.stream()
                                        .map(CsOrderIndexChatpeerDTO::getBuyerNick)
                                        .distinct()
                                        .collect(Collectors.toList());
                                List<CsOrderIndexDO> lst = assembledCsOrderIndex(jobShop, date, csNick, buyerLst);
                                if (CollectionUtils.isEmpty(lst)) {
                                    return null;
                                }
                                //@TODO(处理订单过滤标识)
                                List<Long> csOrderIndexOrderIds = lst.stream().
                                        map(CsOrderIndexDO::getOrderId).collect(Collectors.toList());
                                List<Long> orderFilterIds = orderFilterDao.selectOrderFilterByOrderIds(csOrderIndexOrderIds, shop, date);
                                if (CollectionUtils.isNotEmpty(orderFilterIds)) {
                                    Set<Long> orderFilterIdSet = Sets.newHashSet(orderFilterIds);
                                    for (CsOrderIndexDO orderIndex : lst) {
                                        orderIndex.setGoodsFilte(false);
                                        if (orderFilterIdSet.contains(orderIndex.getOrderId())) {
                                            orderIndex.setGoodsFilte(true);
                                        }
                                    }
                                }
                                return lst;
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                            throw e;
                        }
                        return null;
                    }
                };
                taskList.add(asyncTask);
                task++;
            }
            if (CollUtil.isNotEmpty(taskList)) {
                Object[] objects = AsyncTaskUtil.runAll(taskList);
                Arrays.stream(objects).filter(Objects::nonNull).forEach(callBack -> {
                    List<CsOrderIndexDO> csOrderIndexLst = (List<CsOrderIndexDO>) callBack;
                    dayTeamOrderIndexLst.addAll(csOrderIndexLst);
                });

            }
            csOrderIndexDao.deleteShopCsOrderIndexByDate(shop, date);
            if (CollectionUtils.isNotEmpty(dayTeamOrderIndexLst)) {
                //fix:1836 订单只要下单前有聊天 多个客服都不会算静默
                //非静默的订单ID
                Set<Long> noSilentOrderIdLst = dayTeamOrderIndexLst.stream().filter(ele -> ele.getSilentFlag() == 0).map(CsOrderIndexDO::getOrderId).collect(Collectors.toSet());
                List<CsOrderIndexDO> presaleSameDayOds = new ArrayList<>();
                dayTeamOrderIndexLst.forEach(orderIndex -> {
                    if (noSilentOrderIdLst.contains(orderIndex.getOrderId())) {
                        orderIndex.setSilentFlag(0);
                    }
                    if(orderIndex.getPreSale() && orderIndex.getBalancePay()
                            && null != orderIndex.getBpFirstReplyDate() && DateUtil.isSameDate(orderIndex.getOrderCreated(), orderIndex.getBpFirstReplyDate())){
                        presaleSameDayOds.add(orderIndex);
                    }
                });
                //预售 订单对应多个（先判断2个）客服，且预售尾款都在同一天
                Map<Long, List<CsOrderIndexDO>> presaleSameDayMapping = presaleSameDayOds.stream().collect(Collectors.groupingBy(CsOrderIndexDO::getOrderId));
                Set<Entry<Long, List<CsOrderIndexDO>>> entries = presaleSameDayMapping.entrySet();
                for(Entry<Long, List<CsOrderIndexDO>> thisEntry: entries){
                    if(thisEntry.getValue().size() == 2){
                        thisEntry.getValue().sort(Comparator.comparing(CsOrderIndexDO::getBpFirstReplyDate));
                        thisEntry.getValue().get(0).setBalancePay(Boolean.FALSE);
                        thisEntry.getValue().get(0).setAssitOrderPay(Boolean.TRUE);
                        thisEntry.getValue().get(0).setAssitOrderInFollowup(Boolean.FALSE);
//                        thisEntry.getValue().get(0).setOrderPayment(null);
                    }
                }
                csOrderIndexDao.batchInsertCsOrderIndex(shop, date, dayTeamOrderIndexLst);
            } else {
                if (logger.isDebugEnabled()) {

                    logger.debug("there is no CsOrderIndex list to be inserted");
                }
            }
        }
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("handle shop CsOrderIndex end,time:{}s", (e - s) / 1000);
        }
    }




    /**
     * 下单判定 && 付款判定
     *
     * @param jobShop
     * @param date
     * @param csNick
     * @param buyerLst
     * @return
     */
    private List<CsOrderIndexDO> assembledCsOrderIndex(JobShopQuery jobShop, Date date, String csNick, List<String> buyerLst) {

        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //(询单有效时长)
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();

        Date startDate = date;
        Date endDate = DateUtil.getEndTimeOfDate(date);

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(startDate);
        validDateRange.setEndDate(DateUtil.getDateByPeriod(endDate, enquiryValidDays - 1));

        //设置有效的插旗开始结束时长
        validDateRange.setAdjustOrderReMarkStartDate(DateUtil.getDateByPeriod(startDate, -ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustOrderReMarkEndDate(DateUtil.getDateByPeriod(endDate, enquiryValidDays));

        //设置预售的有效时间
        validDateRange.setAdjustStartDate(DateUtil.getDateByPeriod(validDateRange.getStartDate(), -ValidDateRangeQuery.validPresaleOrderBalancePayDays));
        validDateRange.setAdjustEndDate(validDateRange.getEndDate());
        //排除父订单
        List<Long> presaleFilterOrderIds = Optional.ofNullable(orderDao.selectOrderDirectTradeId(shop, 1L, validDateRange.getAdjustStartDate(), validDateRange.getAdjustEndDate()))
                .orElse(new ArrayList<>(0));
        //----------------------- 预售下单/付定金 START ---------------------------//
        //@TODO buyerOrderMap 添加数据有先后顺序 ,先预售后普通后面有处理
        Map<Long,BuyerOrderDTO> buyerOrderMap = Maps.newHashMap();
        // @TODO(根据(询单有效期)查询【预售付定金】的订单数据)
        List<BuyerOrderDTO> presaleBargainPaidOrderLst = presaleOrderDao.selectShopBargainPaidOrderLstByBuyersAndDate(shop, buyerLst, validDateRange,new HashSet<>(presaleFilterOrderIds));
        if (CollectionUtils.isNotEmpty(presaleBargainPaidOrderLst)) {
            for (BuyerOrderDTO order : presaleBargainPaidOrderLst) {
                //预售
                order.setPreSale(Boolean.TRUE);
                order.setBalancePay(Boolean.FALSE);
                order.setPayTime(order.getBargainTime());
                buyerOrderMap.put(order.getOrderId(),order);
            }
        }
        // @TODO(根据询(询单有效期)查询【预售下单】的订单数据)
        List<Long> filterPresaleCreatedOrderLst = new ArrayList<>(buyerOrderMap.keySet());
        filterPresaleCreatedOrderLst.addAll(presaleFilterOrderIds);
        List<BuyerOrderDTO> presaleCreatedOrderLst = presaleOrderDao.selectShopCreatedPresaleOrderLstByBuyersAndDate(shop, buyerLst,filterPresaleCreatedOrderLst,validDateRange);
        if (CollectionUtils.isNotEmpty(presaleCreatedOrderLst)) {
            for (BuyerOrderDTO order : presaleCreatedOrderLst) {
                //预售
                order.setPreSale(Boolean.TRUE);
                order.setBalancePay(Boolean.FALSE);
                order.setPayTime(order.getBargainTime());
                buyerOrderMap.put(order.getOrderId(),order);
            }
        }
        List<Long> filterOrderIds = Optional.ofNullable(orderDao.selectOrderDirectTradeId(shop, 0L, validDateRange.getStartDate(), validDateRange.getEndDate()))
                .orElse(new ArrayList<>(0));
        //----------------------- 预售下单/付定金 END ---------------------------//
        //-----------------------（普通订单下单） START ---------------------------//
        // @TODO(根据(询单有效期)查询【下单】的订单数据)
        List<BuyerOrderDTO> csRelatedOrderLst = orderDao.selectShopCreatedOrderLstByBuyersAndDate(shop, buyerLst, validDateRange,new HashSet<>(filterOrderIds));
        if (CollectionUtils.isNotEmpty(csRelatedOrderLst)) {
            for (BuyerOrderDTO order : csRelatedOrderLst) {
                if (order.getOrderType() == null||order.getOrderId() == null) { continue; }
                //buyerOrderDTO !=null 是预售订单
                BuyerOrderDTO buyerOrderDTO = buyerOrderMap.get(order.getOrderId());
                if(buyerOrderDTO == null && order.getOrderType() !=1){
                    //非预售
                    order.setPreSale(Boolean.FALSE);
                    order.setBalancePay(Boolean.FALSE);
                    buyerOrderMap.put(order.getOrderId(),order);
                    continue;
                }
                //预售
                boolean status = false;
                if(buyerOrderDTO == null && order.getOrderType() == 1){
                    order.setPreSale(Boolean.TRUE);
                    order.setBalancePay(Boolean.FALSE);
                    buyerOrderMap.put(order.getOrderId(),order);
                    status=true;
                }
                if (order.getPayTime() == null || validDateRange.getEndDate().compareTo(order.getPayTime()) != 1) {
                    continue;
                }
                if(!cn.hutool.core.date.DateUtil.isSameDay(date,order.getPayTime())){
                    continue;
                }
                if(status){
                    order.setBalancePayTime(order.getPayTime());
                    order.setBalancePay(Boolean.TRUE);
                    continue;
                }
                if(buyerOrderDTO == null){
                    continue;
                }
                buyerOrderDTO.setBalancePayTime(order.getPayTime());
                buyerOrderDTO.setBalancePay(Boolean.TRUE);
            }
        }
        // @TODO(根据(询单有效期)查询【普通订单付款 && 预售订单付尾款】的订单数据)
        List<BuyerOrderDTO> csRelatedOPaidOrderLst = orderDao.selectShopPaidOrderLstByBuyersAndDate(shop, buyerLst, validDateRange,new HashSet<>(filterOrderIds));
        if (CollectionUtils.isNotEmpty(csRelatedOPaidOrderLst)) {
            for (BuyerOrderDTO order : csRelatedOPaidOrderLst) {
                BuyerOrderDTO buyerOrderDTO = buyerOrderMap.get(order.getOrderId());
                if (buyerOrderDTO == null) {
                    //预售
                    if (order.getOrderType() == 1) {
                        order.setPreSale(Boolean.TRUE);
                        buyerOrderMap.put(order.getOrderId(), order);
                        if(!cn.hutool.core.date.DateUtil.isSameDay(date,order.getPayTime())){
                            continue;
                        }
                        order.setBalancePay(Boolean.TRUE);
                        order.setBalancePayTime(order.getPayTime());
                        continue;
                    }
                    //普通
                    order.setPreSale(Boolean.FALSE);
                    order.setBalancePay(Boolean.FALSE);
                    buyerOrderMap.put(order.getOrderId(), order);
                    continue;
                }
                //预售
                //bug2704
                if(buyerOrderDTO.getPreSale()
                        && cn.hutool.core.date.DateUtil.isSameDay(date,order.getPayTime())
//                            || Math.abs(DateUtil.date2Date(date, order.getPayTime())) <= enquiryValidDays)
                        && buyerOrderDTO.getOrderPayType() != 1
                ){
                    //bug 2975
                    if(null == buyerOrderDTO.getBalancePayTime()){
                        buyerOrderDTO.setBalancePay(Boolean.FALSE);
                    }else{
                        buyerOrderDTO.setBalancePay(Boolean.TRUE);
                        buyerOrderDTO.setBalancePayTime(order.getPayTime());
                    }
                    continue;
                }
                //普通
                buyerOrderDTO.setBalancePay(Boolean.FALSE);
            }
        }
        return preHandleCsOrderIndexForJudgeRuleType(jobShop, date, csNick, validDateRange, new HashSet<>(buyerOrderMap.values()));
    }

    private List<CsOrderIndexDO> preHandleCsOrderIndexForJudgeRuleType(JobShopQuery jobShop, Date date, String csNick,
                                                                       ValidDateRangeQuery validDateRange, Set<BuyerOrderDTO> buyerOrderSet) {

        if (CollectionUtils.isNotEmpty(buyerOrderSet)) {
            JobShopDTO shop = jobShop.getShop();

            //@TODO(插旗处理) 从orderRemark表中获取
            List<Long> csOrderIndexOrderIds = buyerOrderSet.stream()
                    .map(BuyerOrderDTO::getOrderId)
                    .collect(Collectors.toList());

            List<OrderRemarkDTO> orderRemarkList = orderRemarkDao.selectOrderRemarkInfoByOrderIdList(shop, csOrderIndexOrderIds, validDateRange);

            List<CsOrderIndexDO> csOrderIndexLst = Lists.newArrayListWithCapacity(buyerOrderSet.size());
            CsOrderIndexDO csOrderIndex;
            for (BuyerOrderDTO order : buyerOrderSet) {
                if (CollectionUtils.isNotEmpty(orderRemarkList)) {
                    for (OrderRemarkDTO orderRemarkDTO : orderRemarkList) {
                        if (order.getOrderId().equals(orderRemarkDTO.getOrderId())) {
                            order.setSellerFlag(orderRemarkDTO.getFlag());
                            break;
                        }
                    }
                }
                //新建客服关系指标对象
                csOrderIndex = new CsOrderIndexDO(shop.getShopId(), date, csNick, order.getBuyerNick().toLowerCase());
                csOrderIndexLst.add(csOrderIndex);
                //初始化数据
                initCsOrderIndex(csOrderIndex, order);

                if (order.getPayType() == null) {
                    order.setPayType(0);
                }
                packageCsOrderIndexForJudgeRuleType(jobShop, csNick, order, csOrderIndex);
            }
            return csOrderIndexLst;
        }
        return null;
    }

    private void initCsOrderIndex(CsOrderIndexDO csOrderIndex, BuyerOrderDTO order) {

        csOrderIndex.setOrderId(order.getOrderId());
        csOrderIndex.setOrderCreated(order.getCreated());

        csOrderIndex.setOrderStatus(order.getStatus() == null ? "" : order.getStatus());

        csOrderIndex.setPreSale(order.getPreSale());
        csOrderIndex.setBalancePay(order.getPreSale() && order.getBalancePay());

        if (order.getPreSale()) {//预售

            if (order.getBalancePay()) {//预售付尾款

                csOrderIndex.setOrderPayDate(order.getBalancePayTime());
                csOrderIndex.setOrderPayment(order.getPayment());
            } else {

                csOrderIndex.setOrderPayDate(order.getBargainTime());
                csOrderIndex.setOrderPayment(order.getBargainPayment());
            }
//          issues 340 客服绩效的客服销售明细未展示
            if (order.getOrderPayType() != null && order.getOrderPayType() == 1) {//全款支付 付款金额的定金加尾款
                csOrderIndex.setOrderPayment(BaseUtils.getNonNull(order.getBargainPayment()) + BaseUtils.getNonNull(order.getBalancePayment()));
            }
        } else {

            csOrderIndex.setOrderPayDate(order.getPayTime());
            csOrderIndex.setOrderPayment(order.getPayment());
        }


        csOrderIndex.setOrderGoodsNum(order.getNum());
        csOrderIndex.setOrderPostFee(order.getPostFee());
        csOrderIndex.setOrderFlag(order.getSellerFlag() != null ? order.getSellerFlag() : 0);//插旗
        csOrderIndex.setSilentFlag(0);//非静默
        csOrderIndex.setUrgepayFlag(0);
        csOrderIndex.setBcChatNum(0);
        csOrderIndex.setBcReplyNum(0);
        csOrderIndex.setBcChatRoundNum(0);
        csOrderIndex.setBpChatNum(0);
        csOrderIndex.setBpReplyNum(0);
        csOrderIndex.setBpChatRoundNum(0);
        csOrderIndex.setGoodsFilte(false);
        csOrderIndex.setAftersale(Boolean.FALSE);
        csOrderIndex.setAssitOrderCreate(Boolean.FALSE);
        csOrderIndex.setAssitOrderInFollowup(Boolean.FALSE);
        csOrderIndex.setAssitOrderPay(Boolean.FALSE);
        csOrderIndex.setPayType(order.getPayType() == null ? 0 : order.getPayType());
    }


    /**
     * 按聊天时间判定归属进行订单绑定
     *
     * @param jobShop
     * @param csNick
     * @param order
     * @param csOrderIndex
     */
    private void packageCsOrderIndexForJudgeRuleType(
            JobShopQuery jobShop,
            String csNick,
            BuyerOrderDTO order,
            CsOrderIndexDO csOrderIndex) {

        JobShopDTO shop = jobShop.getShop();

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        int ascriptionJudgeRule = sys.getJudgeRuleAscription();
        int judgeRule = sys.getJudgeRule();
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();

        Date sdateOfJudgeRule;
        Date edateOfJudgeRule;
        //询单日
        sdateOfJudgeRule = com.pes.jd.util.DateUtils.getStartTimeOfDate(com.pes.jd.util.DateUtils.getDateByPeriod(order.getCreated(), -enquiryValidDays + 1));
        if (order.getBalancePay()) {//预售付尾款
            if (order.getBalancePayTime() == null) {
                return;
            }
            edateOfJudgeRule = DateUtil.getEndTimeOfDate(order.getBalancePayTime());
        } else {
            if (order.getPayTime() == null) {
                edateOfJudgeRule = DateUtil.getEndTimeOfDate(order.getCreated());
            } else {
                edateOfJudgeRule = DateUtil.getEndTimeOfDate(order.getPayTime());
            }
        }


        //查询下单买家在下单/付款之前有没有跟当前客服聊天
        List<OrderChatlogInfoDTO> chatLogLst = chatlogDao.selectChatLogLstByCsAndBuyerAndDateForCsOrerIndex(shop, csNick, order.getBuyerNick().toLowerCase(),
                sdateOfJudgeRule, edateOfJudgeRule);
        if (CollectionUtils.isEmpty(chatLogLst)) {
            return;
        }

        //fix519:查询最后一天单口相声/转发过滤过滤的chatpeer.被过滤的会话不用算一些指标
        List<CommonCsChatpeerDTO> csSingleChatFilterChatPeer = csChatpeerDao.selectFilterChatPeer(shop, csNick, order.getBuyerNick().toLowerCase(),
                sdateOfJudgeRule, edateOfJudgeRule);
        //fix:1051查询下单买家在下单/付款之前暗语过滤的天不用算回合数
        List<CommonCsChatpeerDTO> watchwordFilterChatPeer = csChatpeerDao.selectWatchwordFilterChatPeer(shop, csNick, order.getBuyerNick().toLowerCase(),
                sdateOfJudgeRule, edateOfJudgeRule);
        if (ascriptionJudgeRule == JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT.getType()) {
            /*
             * 按聊天时间判定归属
             */
            calCsOrderIndexFromChatlogLst(jobShop, order, chatLogLst, csOrderIndex, csSingleChatFilterChatPeer);
            if (CollectionUtils.isNotEmpty(chatLogLst)) {
                calSilentIndex(jobShop, order, csOrderIndex, chatLogLst, csNick, sdateOfJudgeRule, edateOfJudgeRule);
            }


        } else if (ascriptionJudgeRule == JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_ROUND.getType()) {
            /*
             * 按回合数判定归属
             */
            calCsOrderIndexFromChatlogLst(jobShop, order, chatLogLst, csOrderIndex, csSingleChatFilterChatPeer);
            //静默指标
            if (CollectionUtils.isNotEmpty(chatLogLst)) {
                calSilentIndex(jobShop, order, csOrderIndex, chatLogLst, csNick, sdateOfJudgeRule, edateOfJudgeRule);
            }
            calChatRoundNum(jobShop, order, chatLogLst, csOrderIndex, judgeRule, watchwordFilterChatPeer);
        }

    }

    /**
     * 按聊天时间判定
     * @param jobShop
     * @param order
     * @param chatLogs
     * @param csOrderIndex
     * @param csSingleChatFilterChatPeer
     */
    private void calCsOrderIndexFromChatlogLst(JobShopQuery jobShop, BuyerOrderDTO order, List<OrderChatlogInfoDTO> chatLogs,
                                               CsOrderIndexDO csOrderIndex, List<CommonCsChatpeerDTO> csSingleChatFilterChatPeer) {

        if (CollectionUtils.isNotEmpty(chatLogs)) {

            //最早，最晚指标
            calStartAndEndIndex(jobShop, order, csOrderIndex, chatLogs);
            //下单前后的指标
            calBeforeOrderCreatedIndex(jobShop, order, csOrderIndex, chatLogs, csSingleChatFilterChatPeer);
            //付款前后的指标
            calBeforeOrderPayIndex(jobShop, order, csOrderIndex, chatLogs, csSingleChatFilterChatPeer);
            //售后指标
            calAfterSaleIndex(jobShop, order, csOrderIndex, chatLogs);
        }
    }

    /**
     * 最早，最晚指标
     *
     * @param jobShop
     * @param order
     * @param csOrderIndex
     * @param chatLogs
     */
    private void calStartAndEndIndex(JobShopQuery jobShop,
                                     BuyerOrderDTO order,
                                     CsOrderIndexDO csOrderIndex,
                                     List<OrderChatlogInfoDTO> chatLogs) {
        final ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        OrderChatlogInfoDTO chatlog;
        for (int i = 0, size = chatLogs.size(); i < size; i++) {
            chatlog = chatLogs.get(i);

            if (!isCsAutoReplyChat(sys.getAutoReplySwitch(), chatlog.getMt(), chatlog.getContent())) {

                if (chatlog != null) {
                    if (chatlog.getDirection() == 0) {// 客服->买家
                        if (csOrderIndex.getFirstReplyDate() == null) {
                            csOrderIndex.setFirstReplyDate(chatlog.getTime());// 客服首次回复
                        }
                        csOrderIndex.setLastReplyDate(chatlog.getTime());// 客服最后的回复时间
                    } else if (chatlog.getDirection() == 1) {// 买家 -> 客服
                        if (csOrderIndex.getFirstChatDate() == null) {
                            csOrderIndex.setFirstChatDate(chatlog.getTime());//// 买家首次咨询
                        }
                        csOrderIndex.setLastChatDate(chatlog.getTime());//// 买家最后的咨询
                    }
                }

            }

        }
    }


    /**
     * 下单前后的指标
     * @param jobShop
     * @param order
     * @param csOrderIndex
     * @param chatLogs
     * @param csSingleChatFilterChatPeer
     */
    private void calBeforeOrderCreatedIndex(JobShopQuery jobShop,
                                            BuyerOrderDTO order,
                                            CsOrderIndexDO csOrderIndex,
                                            List<OrderChatlogInfoDTO> chatLogs,
                                            List<CommonCsChatpeerDTO> csSingleChatFilterChatPeer) {

        final ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        boolean isFilteAutoReply = sys.getAutoReplySwitch();
        OrderChatlogInfoDTO chatlog;
        for (int i = 0, size = chatLogs.size(); i < size; i++) {
            chatlog = chatLogs.get(i);
            if (!isCsAutoReplyChat(sys.getAutoReplySwitch(), chatlog.getMt(), chatlog.getContent())) {
                    /*
                     * 下单前的一些必备属性 start
                     */
                    if (chatlog.getTime().getTime()<=order.getCreated().getTime()) {// 下单前
                        if (chatlog.getDirection() == 1) {// 买家说话
                            if (csOrderIndex.getBcFirstChatDate() == null) {
                                csOrderIndex.setBcFirstChatDate(chatlog.getTime());
                            }
                            csOrderIndex.setBcLastChatDate(chatlog.getTime());
                                csOrderIndex.setBcChatNum(csOrderIndex.getBcChatNum() + 1);
                        } else if (chatlog.getDirection() == 0) {// 客服->买家
                            if (chatlog.getContent() != null) {

                                if (!performanceRuleBusiness.isCsAutoReplyChat(isFilteAutoReply, chatlog.getMt(), chatlog.getContent())) {
                                    csOrderIndex.setBcReplyNum(csOrderIndex.getBcReplyNum() + 1);

                                    if (csOrderIndex.getBcFirstReplyDate() == null) {
                                        csOrderIndex.setBcFirstReplyDate(chatlog.getTime());
                                    }
                                    if (CollUtil.isNotEmpty(csSingleChatFilterChatPeer)) {//被单口相声过滤的会话不算下单前最后回复时间
                                        for(CommonCsChatpeerDTO thisSinglePeer: csSingleChatFilterChatPeer){
                                            Date firstChatDate = thisSinglePeer.getFirstChatDate();
                                            Date lastChatDate = thisSinglePeer.getLastChatDate();
                                            if(null == firstChatDate || null == lastChatDate){
                                                break;
                                            }else if(chatlog.getTime().compareTo(lastChatDate) <= 0
                                                && chatlog.getTime().compareTo(firstChatDate) >= 0){
                                                break;
                                            }else{
                                                csOrderIndex.setBcLastReplyDate(chatlog.getTime());
                                            }
                                        }
//                                        csSingleChatFilterChatPeer.stream().filter(ele->{//时间在聊天开始跟结束内的时间不需要算
//                                            Date firstChatDate = ele.getFirstChatDate();
//                                            Date lastChatDate = ele.getLastChatDate();
//                                            return finalChatlog.getTime().after(lastChatDate) || finalChatlog.getTime().before(firstChatDate);
//                                        }).findFirst().ifPresent(ele-> csOrderIndex.setBcLastReplyDate(finalChatlog.getTime()));
                                    } else {
                                        csOrderIndex.setBcLastReplyDate(chatlog.getTime());
                                    }

                                }
                            }
                        }
                    } else {

                        if (chatlog.getDirection() == 1) {
                            if (csOrderIndex.getAcFirstChatDate() == null) {
                                csOrderIndex.setAcFirstChatDate(chatlog.getTime());
                            }
                        } else if (chatlog.getDirection() == 0) {
                            if (csOrderIndex.getAcFirstReplyDate() == null) {
                                csOrderIndex.setAcFirstReplyDate(chatlog.getTime());
                            }
                        }
                    }
            }
        }
    }
    /**
     * 付款前后的指标
     * @param jobShop
     * @param order
     * @param csOrderIndex
     * @param chatLogs
     * @param csSingleChatFilterChatPeer
     */
    private void calBeforeOrderPayIndex(JobShopQuery jobShop,
                                        BuyerOrderDTO order,
                                        CsOrderIndexDO csOrderIndex,
                                        List<OrderChatlogInfoDTO> chatLogs, List<CommonCsChatpeerDTO> csSingleChatFilterChatPeer) {

        final ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        Date toPayDate = toPayDate(order);
        boolean isToPay = toPayType(order, toPayDate);

        OrderChatlogInfoDTO chatlog;
        for (int i = 0, size = chatLogs.size(); i < size; i++) {
            chatlog = chatLogs.get(i);
            if (!isCsAutoReplyChat(sys.getAutoReplySwitch(), chatlog.getMt(), chatlog.getContent())) {

                if (isToPay) {
                    if (chatlog.getTime().getTime() <= toPayDate.getTime()) {

                        // 付款前
                        if (chatlog.getDirection() == 1) {// 买家说话

                            csOrderIndex.setBpLastChatDate(chatlog.getTime());
                                csOrderIndex.setBpChatNum(csOrderIndex.getBpChatNum() + 1);
                        } else if (chatlog.getDirection() == 0) {// 客服->买家

                            if (chatlog.getContent() != null) {

                                if (!performanceRuleBusiness.isCsAutoReplyChat(sys.getAutoReplySwitch(), chatlog.getMt(), chatlog.getContent())) {
                                    csOrderIndex.setBpReplyNum(csOrderIndex.getBpReplyNum() + 1);

                                    if (csOrderIndex.getBpFirstReplyDate() == null) {
                                        csOrderIndex.setBpFirstReplyDate(chatlog.getTime());
                                    }
                                    if (CollUtil.isNotEmpty(csSingleChatFilterChatPeer)) {//被单口相声过滤的会话不算下单前最后回复时间
                                        for(CommonCsChatpeerDTO thisSinglePeer: csSingleChatFilterChatPeer){
                                            Date firstChatDate = thisSinglePeer.getFirstChatDate();
                                            Date lastChatDate = thisSinglePeer.getLastChatDate();
                                            if(null == firstChatDate || null == lastChatDate) {
                                                break;
                                            }else if(chatlog.getTime().compareTo(lastChatDate) <= 0
                                                    && chatlog.getTime().compareTo(firstChatDate) >= 0){
                                                break;
                                            }else{
                                                csOrderIndex.setBpLastReplyDate(chatlog.getTime());
                                            }
                                        }
//                                        OrderChatlogInfoDTO finalChatlog = chatlog;
//                                        csSingleChatFilterChatPeer.stream().filter(ele -> {//时间在聊天开始跟结束内的时间不需要算
//                                            Date firstChatDate = ele.getFirstChatDate();
//                                            Date lastChatDate = ele.getLastChatDate();
//                                            return finalChatlog.getTime().after(lastChatDate) || finalChatlog.getTime().before(firstChatDate);
//                                        }).findFirst().ifPresent(ele -> csOrderIndex.setBpLastReplyDate(finalChatlog.getTime()));
                                    } else {
                                        csOrderIndex.setBpLastReplyDate(chatlog.getTime());
                                    }
                                }
                            }
                        }
                    }

                    //全静默-付款后咨询
                    if (chatlog.getTime().after(toPayDate)) {

                        if (chatlog.getDirection() == 0) {
                            if (csOrderIndex.getApFirstReplyDate() == null) {
                                csOrderIndex.setApFirstReplyDate(chatlog.getTime());// 付款后客服首次回复
                            }
                        } else if (chatlog.getDirection() == 1) {
                            if (csOrderIndex.getApFirstChatDate() == null) {
                                csOrderIndex.setApFirstChatDate(chatlog.getTime());// 付款后买家首次咨询
                            }
                        }
                    }
                }
            }

        }
    }


    /**
     * 售后指标
     *
     * @param jobShop
     * @param order
     * @param csOrderIndex
     * @param chatLogs
     */
    private void calAfterSaleIndex(JobShopQuery jobShop,
                                   BuyerOrderDTO order,
                                   CsOrderIndexDO csOrderIndex,
                                   List<OrderChatlogInfoDTO> chatLogs) {

        final ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        Date toPayDate = toPayDate(order);
        boolean isToPay = toPayType(order, toPayDate);

        OrderChatlogInfoDTO chatlog;
        boolean isFindCurrentDayCsChat = false;
        for (int i = 0, size = chatLogs.size(); i < size; i++) {
            chatlog = chatLogs.get(i);
            //不是自动回复时执行
            if (!isCsAutoReplyChat(sys.getAutoReplySwitch(), chatlog.getMt(), chatlog.getContent())) {

                if (toPayDate != null) {
                    //@TODO(售后判断,当天客服第一条回复记录)
                    if (!isFindCurrentDayCsChat
                            && chatlog.getDirection() == 0
                            && chatlog.getTime().after(csOrderIndex.getDate())) {

                        if (chatlog.getTime().after(toPayDate)) {
                            csOrderIndex.setAftersale(Boolean.TRUE);
                            break;
                        }
                        isFindCurrentDayCsChat = true;
                    }
                }
            }

        }

        if (!isFindCurrentDayCsChat) {
            boolean isCurrentDayBuyerFirstChat = false;
            for (int i = 0, size = chatLogs.size(); i < size; i++) {
                chatlog = chatLogs.get(i);

                if (toPayDate != null) {
                    //@TODO(售后判断,当天顾客第一条回复记录)
                    if (!isCurrentDayBuyerFirstChat
                            && chatlog.getDirection() == 1
                            && chatlog.getTime().after(csOrderIndex.getDate())) {

                        if (chatlog.getTime().after(toPayDate)) {
                            csOrderIndex.setAftersale(Boolean.TRUE);
                        }
                        isCurrentDayBuyerFirstChat = true;
                    }
                }
            }
        }
    }

    /**
     * 静默指标
     *
     * @param jobShop
     * @param order
     * @param csOrderIndex
     * @param chatLogs
     */
    private void calSilentIndex(JobShopQuery jobShop,
                                BuyerOrderDTO order,
                                CsOrderIndexDO csOrderIndex,
                                List<OrderChatlogInfoDTO> chatLogs,
                                String csNick,
                                Date startDate, Date endDate) {

        final ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Date toPayDate = toPayDate(order);
        boolean isToPay = toPayType(order, toPayDate);

        OrderChatlogInfoDTO chatlog;
        boolean isModifyFlag = false;//判断是否修改过参数
        for (int i = 0, size = chatLogs.size(); i < size; i++) {
            chatlog = chatLogs.get(i);

            if (!isCsAutoReplyChat(sys.getAutoReplySwitch(), chatlog.getMt(), chatlog.getContent())) {
                if (!order.getBalancePay()) {

                    //不是预售付尾款的订单
                    if (i <= 0) {
                        //不区分顾客还是客服
                        if (chatlog.getTime().after(order.getCreated())) {
                            csOrderIndex.setSilentFlag(1);//下单后聊天
                            isModifyFlag = true;
                        }

                        if (isToPay && chatlog.getTime().after(toPayDate)) {
                            csOrderIndex.setSilentFlag(2);//付款后聊天
                            isModifyFlag = false;
                        }
                    }
                }
            }
        }
        //fix_bug_2370 针对静默订单处理
        //下单前询单有效期内有聊天，不算静默订单
        if(isModifyFlag && csOrderIndex.getSilentFlag() == 1){
            //去掉csNick并扩大chatLog查询范围， 只查一条
            List<OrderChatlogInfoDTO> chatLogByBuyerTop1 = chatlogDao.selectChatLogLstByBuyerAndDateForCsOrerIndex(jobShop.getShop(), order.getBuyerNick().toLowerCase(),
                    startDate, endDate);
            if(chatLogByBuyerTop1.size() > 0){
                OrderChatlogInfoDTO chatLogByBuyer = chatLogByBuyerTop1.get(0);
                //这边复制上面的代码
                if (!isCsAutoReplyChat(sys.getAutoReplySwitch(), chatLogByBuyer.getMt(), chatLogByBuyer.getContent())) {
                    if (!order.getBalancePay()) {
                        //判读下单前是否有其他客服聊天, 且聊天时间大于下单时间
                        if (!StringUtils.equals(csNick, chatLogByBuyer.getCsNick()) && chatLogByBuyer.getTime().before(order.getCreated())) {
                            //有则不算静默
                            csOrderIndex.setSilentFlag(0);
                        }
                    }
                }
            }
        }

        if (!order.getBalancePay()) {//不是预售付尾款的订单
            //@TODO(静默处理)
            csOrderIndex.setSilentFlag(performanceRuleBusiness.getSilentOrderUrgepayFlag(sys.getSilentUrgepayTime(), csOrderIndex));
            //@TODO(催付处理)
            csOrderIndex.setUrgepayFlag(performanceRuleBusiness.getOrderUrgepayFlag(csOrderIndex.getSilentFlag(), csOrderIndex));
        }
    }

    //判断是否为自动回复
    private boolean isCsAutoReplyChat(boolean autoReplySwitch, Byte mt, String content) {
        return performanceRuleBusiness.isCsAutoReplyChat(autoReplySwitch, mt, content);
    }


    /**
     * 有效付款时间
     *
     * @param order
     * @return
     */

    private Date toPayDate(BuyerOrderDTO order) {
        return (order.getPayType() != null && order.getPayType() == 1) ? order.getCreated() : (order.getBalancePay() ? order.getBalancePayTime() : order.getPayTime());
    }


    /**
     * @param order
     * @param toPayDate
     * @return
     */

    public Boolean toPayType(BuyerOrderDTO order, Date toPayDate) {
        return toPayDate != null || order.getPayType() == 1;
    }

    /**
     * 计算出客服与买家的聊天回合数，从第一次买家说话开始算起
     *
     *  @param jobShop
     * @param order
     * @param chatLogLst
     * @param csOrderIndex
     * @param judgeRule
     */
    private void calChatRoundNum(JobShopQuery jobShop, BuyerOrderDTO order, List<OrderChatlogInfoDTO> chatLogLst,
                                 CsOrderIndexDO csOrderIndex, int judgeRule,
                                 List<CommonCsChatpeerDTO> watchwordFilterChatPeer) {

        if (CollectionUtils.isEmpty(chatLogLst)) {
            return;
        }
        Boolean balancePay = order.getBalancePay();//预售付尾款


        /*
         * 为下单优先准备
         */
        int sayFlag = 0;
        int chatRoundNum = 0;
        boolean notFindFirstSaidBuyer = true;
        int chatlogIndex = 0;
        ChatRoundNumBO bo = new ChatRoundNumBO(sayFlag, chatRoundNum, notFindFirstSaidBuyer, chatlogIndex);

        JudgeRuleEnum judgeRuleEnum = JudgeRuleEnum.getEnumByType(judgeRule);
        Date targetDate;
        switch (judgeRuleEnum) {
            case JUDGE_RULE_ORDER://下单判定

                if (!balancePay) {
                    targetDate = order.getCreated();
                    packageChatRoundNum(jobShop, targetDate, chatLogLst, bo,watchwordFilterChatPeer);
                    csOrderIndex.setBcChatRoundNum(bo.getChatRoundNum());
                }
                break;
            case JUDGE_RULE_ORDER_FIRST://下单优先判定

                if (!balancePay) {
                    targetDate = order.getCreated();
                    packageChatRoundNum(jobShop, targetDate, chatLogLst, bo,watchwordFilterChatPeer);
                    csOrderIndex.setBcChatRoundNum(bo.getChatRoundNum());
                }
                targetDate = balancePay ? order.getBalancePayTime() : order.getPayTime();
                packageChatRoundNum(jobShop, targetDate, chatLogLst, bo,watchwordFilterChatPeer);
                csOrderIndex.setBpChatRoundNum(bo.getChatRoundNum());
                break;
            case JUDGE_RULE_PAY://付款判定
                if (!balancePay) {
                    targetDate = order.getCreated();
                    packageChatRoundNum(jobShop, targetDate, chatLogLst, bo,watchwordFilterChatPeer);
                    csOrderIndex.setBcChatRoundNum(bo.getChatRoundNum());
                }
                targetDate = balancePay ? order.getBalancePayTime() : ((order.getPayType() != null && order.getPayType() == 1) ? order.getCreated() : order.getPayTime());
                packageChatRoundNum(jobShop, targetDate, chatLogLst, bo,watchwordFilterChatPeer);
                csOrderIndex.setBpChatRoundNum(bo.getChatRoundNum());
                break;
            default:
                logger.error("judge rule error:{}", judgeRule);
                break;
        }

    }

    private ChatRoundNumBO packageChatRoundNum(JobShopQuery jobShop, Date targerOrderDate,
                                               List<OrderChatlogInfoDTO> chatLogLst, ChatRoundNumBO bo, List<CommonCsChatpeerDTO> watchwordFilterChatPeer) {

        if (targerOrderDate == null) {
            return bo;
        }

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        boolean isFilteAutoReply = sys.getAutoReplySwitch();

        final int BUYER_SAY_FLAG = 1;
        final int CS_SAY_FLAG = -1;

        int chatlogIndex = bo.getChatlogIndex();
        int chatRoundNum = bo.getChatRoundNum();
        int sayFlag = bo.getSayFlag();
        boolean notFindFirstSaidBuyer = bo.isNotFindFirstSaidBuyer();

        boolean isAutoReply = false;
        OrderChatlogInfoDTO chatlog;
        //日期客服买家
        Map<String, CommonCsChatpeerDTO> groupByMap = null;
        if(CollUtil.isNotEmpty(watchwordFilterChatPeer)){
            groupByMap = watchwordFilterChatPeer.stream().collect(Collectors.toMap(ele -> ele.getDate() + CommonConstants.DELIMITER + ele.getCsNick() + ele.getBuyerNick(), val -> val, (oldValue, newValue) -> oldValue));
        }
        for (int size = chatLogLst.size(); chatlogIndex < size; chatlogIndex++) {
            chatlog = chatLogLst.get(chatlogIndex);
            if (chatlog != null) {

                if (chatlog.getTime().before(targerOrderDate)) {
                    if (chatlog.getDirection() == 0) {
                        if (chatlog.getContent() != null) {
                            if (isFilteAutoReply) {
//								if (chatlog.getContent().startsWith(autoReplyMark)) {
//									isAutoReply = true;
//								}
                                if (chatlog.getMt() == 2 || chatlog.getMt() == 3) {
                                    isAutoReply = true;
                                }
                                if (isAutoReply) { // 表示开启自动回复过滤功能
                                    isAutoReply = false;
                                } else {

                                    if (!notFindFirstSaidBuyer) {
                                        if (CS_SAY_FLAG != sayFlag) {
                                            String key = DateFormatUtils.getStartTimeOfDate(chatlog.getTime()) + CommonConstants.DELIMITER + chatlog.getCsNick() + chatlog.getBuyer();
                                            if (CollUtil.isNotEmpty(groupByMap) && groupByMap.get(key) != null) {
                                                logger.info("是被暗语过滤的客服={}", key);
                                            } else {
                                                chatRoundNum++;
                                            }
                                            sayFlag = CS_SAY_FLAG;
                                        }
                                    }
                                }
                            } else {
                                if (!notFindFirstSaidBuyer) {
                                    if (CS_SAY_FLAG != sayFlag) {
                                        String key = DateFormatUtils.getStartTimeOfDate(chatlog.getTime()) + CommonConstants.DELIMITER + chatlog.getCsNick() + chatlog.getBuyer();
                                        if (CollUtil.isNotEmpty(groupByMap) && groupByMap.get(key) != null) {
                                            logger.info("是被暗语过滤的客服={}", key);
                                        } else {
                                            chatRoundNum++;
                                        }
                                        sayFlag = CS_SAY_FLAG;
                                    }
                                }
                            }
                        }

                    } else if (chatlog.getDirection() == 1) {//买家说
                        notFindFirstSaidBuyer = false;
                        if (BUYER_SAY_FLAG != sayFlag) {
                            sayFlag = BUYER_SAY_FLAG;
                        }
                    }
                } else {
                    break;
                }
            }
        }
        bo.setChatlogIndex(chatlogIndex);
        bo.setChatRoundNum(chatRoundNum);
        bo.setNotFindFirstSaidBuyer(notFindFirstSaidBuyer);
        bo.setSayFlag(sayFlag);
        return bo;
    }

    private boolean judgeIsAssist(CsOrderIndexDTO csOrderIndex, int type) {
        if(csOrderIndex.getFirstReplyDate() != null){
            if(type == CommonConstants.JUDGE_ASSIT_ORDER_CREATE){
                return csOrderIndex.getOrderCreated() != null && csOrderIndex.getFirstReplyDate().before(csOrderIndex.getOrderCreated());
            }else{
                return csOrderIndex.getOrderPayDate() != null && csOrderIndex.getFirstReplyDate().before(csOrderIndex.getOrderPayDate());
            }
        }
        return false;
    }

    public static void main(String[] args) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        System.out.println(DateUtil.date2Date(cal.getTime(), new Date()));
    }

}

