package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.PresaleAndPreordainPerformanceHandleBusiness;
import com.pes.jd.dao.*;
import com.pes.jd.model.BO.CsPerformancePreordainBO;
import com.pes.jd.model.BO.CsPerformancePresaleBO;
import com.pes.jd.model.BO.ShopPerformancePresaleBO;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class PresaleAndPreordainPerformanceHandleBusinessImpl implements PresaleAndPreordainPerformanceHandleBusiness {

    private static final Logger logger = LoggerFactory.getLogger(PresaleAndPreordainPerformanceHandleBusinessImpl.class);

    @Resource
    private PresaleActivityDao presaleActivityDao;
    @Resource
    private ReserveActivityDao reserveActivityDao;

    @Resource
    private CsChatSessionDao csChatSessionDao;

    @Resource
    private CsChatpeerDao csChatpeerDao;

    @Resource
    private CsOrderBindDao csOrderBindDao;

    @Resource
    private OrderDetailDao orderDetailDao;

    @Resource
    private OrderDao orderDao;

    @Resource
    private PresaleOrderDao presaleOrderDao;

    @Resource
    private ShopGoodsSkuDao shopGoodsSkuDao;

    @Resource
    private ShopPerformancePresaleDao shopPerformancePresaleDao;

    @Resource
    private CsPerformancePresaleDao csPerformancePresaleDao;

    @Resource
    private ShopPerformancePreordainDao shopPerformancePreordainDao;

    @Resource
    private CsPerformancePreordainDao csPerformancePreordainDao;

    /**
     * 8.2 预售订单绩效
     *
     * @param jobShop   shop
     * @param jobDate   date
     * @param isDelData del
     * @throws Exception e
     */
    @Override
    public void handlePerformanceForPresale(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {

        long s = System.currentTimeMillis();

        JobShopDTO shop = jobShop.getShop();
        Date date = jobDate.getDate();
        logger.info("8.2 - {}, {}, {}, 预售订单绩效开始...", shop.getTitle(), shop.getShopId(), jobDate.getDate());

        //查询该店铺当天所有类型未锁定客服
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_ALL, CommonConstants.CS_STATUS_NOT_LOCK);
        List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
        if (CollectionUtils.isEmpty(targetCsLst)) return;
        Set<String> noLockCsNicks = targetCsLst.stream().map(CsDTO::getNick).collect(Collectors.toSet());

        //查询该店铺当天的预售活动
        List<PresaleActivityDTO> allActivitys = presaleActivityDao.selectByShopIdAndDateForPresalePerformance(shop, date);
        if (CollectionUtils.isEmpty(allActivitys)) {
            logger.info("8.2 - {}, {}, {}, 店铺在这天无预售活动", shop.getTitle(), shop.getShopId(), jobDate.getDate());
            return;
        }
        logger.info("8.2 - {}, {}, {}, 店铺在这天预约活动共 {} 个", shop.getTitle(), shop.getShopId(), jobDate.getDate(), allActivitys.size());

        //提取活动列表中所有skuId
        Set<Long> skuIds = allActivitys.stream().map(PresaleActivityDTO::getSkuId).collect(Collectors.toSet());
        Map<Long, String> skuMap = shopGoodsSkuDao.selectByShopIdAndSkuIds(shop, skuIds)
                .stream().collect(Collectors.groupingBy(ShopGoodSkuDTO::getSkuId,
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSkuName())));
        //Set<Long> skuIdSession = new HashSet<>();
        //Set<Long> skuIdOrder = new HashSet<>();
        ////若活动列表 > 100 则剔除 无咨询并且无下单 的活动
        //if (allActivitys.size() > 100) {
        //    PresaleActivityDTO activityPeriod = presaleActivityDao.selectActivityPeriodByShopIdAndDateForPresalePerformance(shop, date);
        //    Date startTime = activityPeriod.getPresaleStartTime();
        //    Date endTime = activityPeriod.getPresaleEndTime();
        //
        //    //查询定金期 所有关于预售活动sku的会话
        //    List<CsChatSessionDO> sessionList = csChatSessionDao.selectByShopIdAndDateAndSkuIds(shop, startTime, endTime, skuIds);
        //    skuIdSession = sessionList.stream().map(CsChatSessionDO::getSkuId).collect(Collectors.toSet());
        //    List<PresaleOrderDTO> todayCreatedOrders = presaleOrderDao.selectByTimeAndBuyerNicksForPresalePerformance(shop, "create_time", startTime, endTime, null, null, skuIds);
        //    skuIdOrder = todayCreatedOrders.stream().map(PresaleOrderDTO::getSkuId).collect(Collectors.toSet());
        //}

        Map<Long, List<PresaleActivityDTO>> activityMapBySku = allActivitys.stream()
                .collect(Collectors.groupingBy(PresaleActivityDTO::getSkuId));

        // 若同个sku在不同预售活动下并且时间有交叉，则过滤掉前一个预售活动
        Map<String, Long> removeActivityIdMap = new HashMap<>();
        for (Map.Entry<Long, List<PresaleActivityDTO>> entry : activityMapBySku.entrySet()) {
            List<PresaleActivityDTO> value = entry.getValue();
            if (value.size() == 1) continue;
            for (PresaleActivityDTO activity1 : value) {
                for (PresaleActivityDTO activity2 : value) {
                    // 同一活动对象跳过
                    if (activity1.equals(activity2)) continue;
                    // 校验两个活动是否有周期交叉情况,有则记录非最后创建的,用于剔除
                    if (DateUtil.intersectOfTime(activity1.getPresaleStartTime(), activity1.getBalanceEndTime(), activity2.getPresaleStartTime(), activity2.getBalanceEndTime())) {
                        if (activity1.getCreateTime().after(activity2.getCreateTime())) {
                            removeActivityIdMap.put(activity2.getActivityId(), activity2.getSkuId());
                        } else {
                            removeActivityIdMap.put(activity1.getActivityId(), activity1.getSkuId());
                        }
                    }
                }
            }
        }

        //根据以上生成条件提出
        ListIterator<PresaleActivityDTO> iterator = allActivitys.listIterator();
        while (iterator.hasNext()) {
            PresaleActivityDTO activity = iterator.next();
            if (activity.getActivityId() == null
                    // 剔除，商品表中没有的商品 活动
                    || activity.getSkuId() == null || !skuMap.containsKey(activity.getSkuId())
                    //// 剔除 无咨询并且无下单 的活动
                    //|| (!skuIdSession.contains(activity.getSkuId()) && !skuIdOrder.contains(activity.getSkuId()))
                    //剔除活动取消 且 取消时间 > 当天的
                    || (activity.getCancelTime() != null && activity.getCancelTime().after(date))
                    // 剔除 同一sku 活动周期有交叉的前一个或几个活动
                    || (removeActivityIdMap.containsKey(activity.getActivityId()) && removeActivityIdMap.get(activity.getActivityId()).equals(activity.getSkuId()))) {
                iterator.remove();
                continue;
            }
            activity.setSkuName(skuMap.get(activity.getSkuId()));
        }
        logger.info("8.2 - {}, {}, {}, 店铺在这天预约活动 过滤后共 {} 个", shop.getTitle(), shop.getShopId(), jobDate.getDate(), allActivitys.size());
        if (CollectionUtils.isEmpty(allActivitys)) return;
        //预售活动按activityId分组
        Map<String, List<PresaleActivityDTO>> allActivityMap = allActivitys.stream()
                .collect(Collectors.groupingBy(PresaleActivityDTO::getActivityId));
        //开线程池
        final int taskThreadCount = Math.min(allActivityMap.size(), 50);
        ExecutorService executorService = new ThreadPoolExecutor(taskThreadCount, taskThreadCount, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(allActivityMap.size()));
        CompletionService<String> completionService = new ExecutorCompletionService<>(executorService);

        //循环活动列表
        for (Map.Entry<String, List<PresaleActivityDTO>> next : allActivityMap.entrySet()) {
            completionService.submit(() -> {
                String activityId = next.getKey();
                List<PresaleActivityDTO> activitys = next.getValue();//

                Date bargainStartTime = activitys.get(0).getPresaleStartTime();
                Date bargainEndTime = activitys.get(0).getPresaleEndTime();
                Date balanceEndTime = activitys.get(0).getBalanceEndTime();

                //该预售活动中所有skuId
                Set<Long> activitySkuIds = activitys.stream().map(PresaleActivityDTO::getSkuId).collect(Collectors.toSet());

                //当前活动定金期开始至当前
                ArrayList<Date> bargainDates = DateUtil.splitDate(bargainStartTime,
                        bargainEndTime.after(jobDate.getEndDate()) ? jobDate.getEndDate() : bargainEndTime);

                //查询定金期 所有关于预售活动sku的会话
                Map<Date, List<CsChatSessionDO>> sessionMap = csChatSessionDao.selectByShopIdAndDateAndSkuIds(shop, bargainDates.get(0), bargainDates.get(bargainDates.size() - 1), activitySkuIds)
                        .stream().collect(Collectors.groupingBy(CsChatSessionDO::getDate));
                //该活动定金期内
                Map<Date, List<PresaleOrderDTO>> createdOrderMap = presaleOrderDao.selectByTimeAndBuyerNicksForPresalePerformance(shop, "create_time", bargainStartTime, bargainEndTime, null, null, activitySkuIds)
                        .stream().collect(Collectors.groupingBy(dto -> DateUtil.getStartTimeOfDate(dto.getCreateTime())));
                Map<Date, List<PresaleOrderDTO>> bargainOrderMap = presaleOrderDao.selectByTimeAndBuyerNicksForPresalePerformance(shop, "bargain_time", bargainStartTime, bargainEndTime, null, null, activitySkuIds)
                        .stream().collect(Collectors.groupingBy(dto -> DateUtil.getStartTimeOfDate(dto.getBargainTime())));

                //店铺下某一活动定金期内(1或多个sku) 的 店铺 预售绩效
                Set<ShopPerformancePresaleDO> shopPerformancesSet = new HashSet<>(activitys.size());
                //店铺下某一活动定金期内(1或多个sku) 的 客服 预售绩效
                Set<CsPerformancePresaleDO> csPerformancesSet = new HashSet<>(activitys.size());

                for (Date countDate : bargainDates) {
                    Date countDateEnd = DateUtil.getEndTimeOfDate(countDate);
                    List<PresaleOrderDTO> todayCreatedOrders = createdOrderMap.get(countDate) == null ? Lists.newArrayList() : createdOrderMap.get(countDate);
                    List<PresaleOrderDTO> todayBargainOrders = bargainOrderMap.get(countDate) == null ? Lists.newArrayList() : bargainOrderMap.get(countDate);
                    //预售店铺绩效
                    shopPerformancesSet.addAll(countShopPerformancePresale(shop, activitys, countDate, countDateEnd, todayCreatedOrders, todayBargainOrders));
                    //预售客服绩效
                    csPerformancesSet.addAll(countCsPerformancePresale(shop, jobDate.getEndDate(),
                            noLockCsNicks, activitys, countDate, countDateEnd, sessionMap.get(countDate), todayCreatedOrders, todayBargainOrders));
                }
                int shopDel = shopPerformancePresaleDao.deleteByShopIdAndDateAndActivityId(shop,
                        bargainDates.get(0), bargainDates.get(bargainDates.size() - 1), activityId);
                int shopInsert = shopPerformancePresaleDao.batchInsertShopPerformancePresale(shop, shopPerformancesSet);

                int csDel = csPerformancePresaleDao.deleteByShopIdAndDateAndActivityId(shop,
                        bargainDates.get(0), bargainDates.get(bargainDates.size() - 1), activityId);
                int csInsert = csPerformancePresaleDao.batchInsertCsPerformancePresale(shop, csPerformancesSet);

                logger.info("8.2 - {}, {}, {}, 预售绩效{}活动 - shopDel={}, shopInsert={}, csDel={}, csInsert={}", shop.getTitle(), shop.getShopId(), jobDate.getDate(), activityId, shopDel, shopInsert, csDel, csInsert);
                return activityId;
            });
        }
        executorService.shutdown();
        try {
            for (Map.Entry<String, List<PresaleActivityDTO>> next : allActivityMap.entrySet()) {
                String activityId = completionService.take().get();
            }
        } catch (Exception e) {
            executorService.shutdownNow();
            logger.error("csOrderIndex error", e);
            throw e;
        }
        long e = System.currentTimeMillis();
        logger.info("8.2 - {}, {}, {}, 预售绩效计算 - 耗时：{}ms", shop.getTitle(), shop.getShopId(), jobDate.getDate(), e - s);

    }


    /**
     * 8.2 预售店铺绩效
     *
     * @param shop      shop
     * @param activitys 活动列表(同一活动Id下 不同sku的实例列表
     * @param dateStart 计算日期(在定金期内
     */
    private Set<ShopPerformancePresaleDO> countShopPerformancePresale(JobShopDTO shop, List<PresaleActivityDTO> activitys, Date dateStart, Date dateEnd, List<PresaleOrderDTO> todayCreatedOrders, List<PresaleOrderDTO> todayBargainOrders) {
        long s = System.currentTimeMillis();

        //店铺下某一活动某一天(1或多个sku) 的预售绩效
        Set<ShopPerformancePresaleDO> performancesSet = new HashSet<>(activitys.size());

        Set<ShopPerformancePresaleBO> performances = new HashSet<>(activitys.size());
        for (PresaleActivityDTO activity : activitys) {
            ShopPerformancePresaleBO bo = new ShopPerformancePresaleBO(activity.getActivityId(), activity.getSkuId(), dateStart);
            bo.setSkuName(activity.getSkuName());
            performances.add(bo);
        }

        //定金期 付定金时间查询 预售订单
        //List<PresaleOrderDTO> todayBargainOrders = presaleOrderDao.selectByTimeAndBuyerNicksForPresalePerformance(shop, "bargain_time", dateStart, dateEnd, null, null,
        //        activitys.stream().map(PresaleActivityDTO::getSkuId).collect(Collectors.toSet()));
        //logger.info("8.2 - {}, {}, {}, 预售店铺绩效-当日付定金订单：{}", shop.getTitle(), shop.getShopId(), dateStart,
        //todayBargainOrders.size());
        PresaleActivityDTO activity0 = activitys.get(0);
        Date bargainStartTime = activity0.getPresaleStartTime();
        List<Long> directTradeId = Optional.ofNullable(orderDao.selectOrderDirectTradeId(shop,1L,bargainStartTime, dateEnd)).orElse(new ArrayList<>(0));

        for (ShopPerformancePresaleBO bo : performances) {
            for (PresaleOrderDTO order : todayCreatedOrders) {

                if (order == null || order.getSkuId() == null || StringUtils.isEmpty(order.getBuyerNick())
                        || !bo.getSkuId().equals(order.getSkuId())) continue;
                //过滤父订单
                if(directTradeId.contains(order.getOrderId())){
                    continue;
                }
                bo.getOrderedBuyerSet().add(order.getBuyerNick());
                bo.setOrderedSkuNum(bo.getOrderedSkuNum() + BaseUtils.getNonNull(order.getGoodsNum()));
                if (order.getBargainTime() != null) {
                    bo.getOrderedBargainBuyerSet().add(order.getBuyerNick());
                    bo.setOrderedBargainSkuNum(bo.getOrderedBargainSkuNum() + BaseUtils.getNonNull(order.getGoodsNum()));
                    bo.setOrderedBargainAmount(bo.getOrderedBargainAmount() + BaseUtils.getNonNull(order.getPayBargainReal()));
                }
                if (order.getBalanceTime() != null || BaseUtils.getNonNull(order.getPayBalanceReal()) > 0) {
                    bo.getOrderedBalanceBuyerSet().add(order.getBuyerNick());
                    bo.setOrderedBalanceSkuNum(bo.getOrderedBalanceSkuNum() + BaseUtils.getNonNull(order.getGoodsNum()));
                    bo.setOrderedBalanceAmount(bo.getOrderedBalanceAmount() + BaseUtils.getNonNull(order.getPayBalanceReal()));
                }
            }

            for (PresaleOrderDTO order : todayBargainOrders) {
                if (order == null || order.getSkuId() == null || StringUtils.isEmpty(order.getBuyerNick())
                        || !bo.getSkuId().equals(order.getSkuId())) continue;
                bo.getToOrderedBargainBuyerSet().add(order.getBuyerNick());
                if (order.getBalanceTime() != null || BaseUtils.getNonNull(order.getPayBalanceReal()) > 0) {
                    bo.getToOrderedBalanceBuyerSet().add(order.getBuyerNick());
                }
            }

            ShopPerformancePresaleDO presaleDO = new ShopPerformancePresaleDO(shop.getShopId(), dateStart, bo.getSkuId(), bo.getActivityId());
            presaleDO.setSkuName(bo.getSkuName());
            presaleDO.setOrderedBuyerNum(bo.getOrderedBuyerSet().size());
            presaleDO.setOrderedSkuNum(bo.getOrderedSkuNum());
            presaleDO.setOrderedBargainBuyerNum(bo.getOrderedBargainBuyerSet().size());
            presaleDO.setOrderedBargainSkuNum(bo.getOrderedBargainSkuNum());
            presaleDO.setOrderedBargainAmount(bo.getOrderedBargainAmount());
            presaleDO.setOrderedBalanceBuyerNum(bo.getOrderedBalanceBuyerSet().size());
            presaleDO.setOrderedBalanceSkuNum(bo.getOrderedBalanceSkuNum());
            presaleDO.setOrderedBalanceAmount(bo.getOrderedBalanceAmount());
            presaleDO.setToOrderedBargainBuyerNum(bo.getToOrderedBargainBuyerSet().size());
            presaleDO.setToOrderedBalanceBuyerNum(bo.getToOrderedBalanceBuyerSet().size());
            performancesSet.add(presaleDO);
        }
        long e = System.currentTimeMillis();
        //logger.info("8.2 - {}, {}, {}, 预售店铺绩效计算耗时：{}ms", shop.getTitle(), shop.getShopId(), dateStart,
        //        e - s);
        return performancesSet;

    }


    private Set<CsPerformancePresaleDO> countCsPerformancePresale(JobShopDTO shop, Date jobEndDate, Set<String> noLockCsNicks,
                                                                  List<PresaleActivityDTO> activitys,
                                                                  Date dateStart, Date dateEnd, List<CsChatSessionDO> sessions, List<PresaleOrderDTO> todayCreatedOrders, List<PresaleOrderDTO> todayBargainOrders) {
        long s = System.currentTimeMillis();
        Set<CsPerformancePresaleBO> performances = new HashSet<>();

        Map<Long, List<PresaleActivityDTO>> activityMap = activitys.stream().collect(Collectors.groupingBy(PresaleActivityDTO::getSkuId));

        PresaleActivityDTO activity0 = activitys.get(0);
        Date bargainStartTime = activity0.getPresaleStartTime();
        Date bargainEndTime = activity0.getPresaleEndTime();
        List<Long> directTradeId = Optional.ofNullable(orderDao.selectOrderDirectTradeId(shop,1L,bargainStartTime, dateEnd)).orElse(new ArrayList<>(0));
        if (CollectionUtils.isNotEmpty(sessions)) {
            //预售绩效临时容器模型 Map<skuId, Map<csNick, Set<buyerNick>>>
            Map<Long, Map<String, Set<String>>> skuToCsToBuyerMap = new HashMap<>();
            for (PresaleActivityDTO activity : activitys) {
                Map<String, Set<String>> csToBuyer = new HashMap<>();
                skuToCsToBuyerMap.put(activity.getSkuId(), csToBuyer);
            }
            for (CsChatSessionDO session : sessions) {
                if (StringUtils.isBlank(session.getCsNick()) || StringUtils.isBlank(session.getCustomer())
                        || session.getSkuId() == null
                        || !noLockCsNicks.contains(session.getCsNick()) ||
                        !skuToCsToBuyerMap.containsKey(session.getSkuId())) continue;

                //按照预售绩效容器模型，填充数据
                Map<String, Set<String>> csToBuyer = skuToCsToBuyerMap.get(session.getSkuId());
                if (csToBuyer.containsKey(session.getCsNick())) {
                    csToBuyer.get(session.getCsNick()).add(session.getCustomer());
                } else {
                    Set<String> buyers = new HashSet<>();
                    buyers.add(session.getCustomer());
                    csToBuyer.put(session.getCsNick(), buyers);
                }
            }
            //参与预售商品session的客服，用于查询chatpeers
            List<String> presaleCss = skuToCsToBuyerMap.values().stream().map(Map::keySet)
                    .flatMap(Collection::stream).collect(Collectors.toList());

            //查询所有关于预售活动sku的聊天关系
            List<CommonCsChatpeerDTO> chatpeers = csChatpeerDao.selectByShopIdAndDateAndCsNicks(shop, dateStart, presaleCss);
            //logger.info("8.2 - {}, {}, {}, ----预售客服绩效-关于预售活动sku的Chatpeer：{}", shop.getTitle(), shop.getShopId(), dateStart, chatpeers.size());

            //当日所有询单的买家昵称集合
            Set<String> enquiryBuyers = new HashSet<>();
            //TODO 填充当天咨询数、询单数
            for (PresaleActivityDTO activity : activitys) {
                Map<String, Set<String>> csToBuyer = skuToCsToBuyerMap.get(activity.getSkuId());
                for (CommonCsChatpeerDTO chatpeer : chatpeers) {
                    if (chatpeer == null || chatpeer.getCsNick() == null || chatpeer.getBuyerNick() == null
                            || chatpeer.getConsult() == null || !chatpeer.getConsult()
                            || !csToBuyer.containsKey(chatpeer.getCsNick())
                            || !csToBuyer.get(chatpeer.getCsNick()).contains(chatpeer.getBuyerNick()))
                        continue;//排除非咨询

                    CsPerformancePresaleBO performanceBO = null;
                    for (CsPerformancePresaleBO bo : performances) {
                        if (bo.getActivityId().equals(activity.getActivityId()) && bo.getSkuId().equals(activity.getSkuId())
                                && bo.getCsNick().equals(chatpeer.getCsNick()) && bo.getDate().equals(dateStart)) {
                            performanceBO = bo;
                        }
                    }
                    if (performanceBO == null) {
                        performanceBO = new CsPerformancePresaleBO(activity.getActivityId(), activity.getSkuId(), chatpeer.getCsNick(), dateStart);
                        performanceBO.setSkuName(activity.getSkuName());
                        performances.add(performanceBO);
                    }
                    performanceBO.getConsultBuyerSet().add(chatpeer.getBuyerNick());
                    if (chatpeer.getEnquiry()) {
                        performanceBO.getEnquiryBuyerSet().add(chatpeer.getBuyerNick());
                        enquiryBuyers.add(chatpeer.getBuyerNick());
                    }
                }
            }
            //定金期 下单时间查询 预售订单
            List<PresaleOrderDTO> createdOrders = CollectionUtils.isEmpty(enquiryBuyers) ? Lists.newArrayList() :
                    presaleOrderDao.selectByTimeAndBuyerNicksForPresalePerformance(shop, "create_time", dateStart, bargainEndTime, enquiryBuyers, null, activityMap.keySet());

            for (CsPerformancePresaleBO bo : performances) {
                if (CollectionUtils.isEmpty(bo.getEnquiryBuyerSet())) continue;
                for (PresaleOrderDTO order : createdOrders) {
                    if (order == null || order.getSkuId() == null || StringUtils.isEmpty(order.getBuyerNick())
                            || !bo.getSkuId().equals(order.getSkuId())
                            || !bo.getEnquiryBuyerSet().contains(order.getBuyerNick())) continue;
                    bo.getEnquiryOrderedBuyerSet().add(order.getBuyerNick());
                    bo.setEnquiryOrderedSkuNum(bo.getEnquiryOrderedSkuNum() + BaseUtils.getNonNull(order.getGoodsNum()));
                    if (order.getBargainTime() != null) {
                        bo.getEnquiryOrderedBargainBuyerSet().add(order.getBuyerNick());
                        bo.setEnquiryOrderedBargainAmount(bo.getEnquiryOrderedBargainAmount() + BaseUtils.getNonNull(order.getPayBargainReal()));
                        bo.setEnquiryOrderedBargainSkuNum(bo.getEnquiryOrderedBargainSkuNum() + BaseUtils.getNonNull(order.getGoodsNum()));
                    }

                    if (order.getBalanceTime() != null || BaseUtils.getNonNull(order.getPayBalanceReal()) > 0) {
                        bo.getEnquiryOrderedBalanceBuyerSet().add(order.getBuyerNick());
                        bo.setEnquiryOrderedBalanceAmount(bo.getEnquiryOrderedBalanceAmount() + BaseUtils.getNonNull(order.getPayBalanceReal()));
                        bo.setEnquiryOrderedBalanceSkuNum(bo.getEnquiryOrderedBalanceSkuNum() + BaseUtils.getNonNull(order.getGoodsNum()));
                    }
                }
            }
        }

        Map<Long, List<PresaleOrderDTO>> todayBargainMap = todayBargainOrders.stream()
                .filter(dto -> activityMap.containsKey(dto.getSkuId())).collect(Collectors.groupingBy(PresaleOrderDTO::getOrderId));
        List<CsOrderBindDO> binds = csOrderBindDao.selectPresaleBindByOrderIdsForPresalePerformance(
                shop, DateUtils.addDays(dateStart, -1), bargainEndTime, todayBargainMap.keySet(), 2);

        for (CsOrderBindDO bind : binds) {
            //过滤父订单
            if(directTradeId.contains(bind.getOrderId())){
                continue;
            }
            for (PresaleOrderDTO dto : todayBargainMap.get(bind.getOrderId())) {
                boolean have = false;
                for (CsPerformancePresaleBO bo : performances) {
                    if (bo.getSkuId().equals(dto.getSkuId()) && bo.getCsNick().equals(bind.getCsNick())) {
                        have = true;
                        bo.getToOrderedBargainBuyerSet().add(dto.getBuyerNick());
                        if (dto.getBalanceTime() != null || BaseUtils.getNonNull(dto.getPayBalanceReal()) > 0) {
                            bo.getToOrderedBalanceBuyerSet().add(dto.getBuyerNick());
                        }
                    }
                }

                if (have) continue;
                CsPerformancePresaleBO bo = new CsPerformancePresaleBO(activity0.getActivityId(), dto.getSkuId(), bind.getCsNick(), dateStart);
                if (performances.contains(bo)) continue;
                performances.add(bo);

                bo.setSkuName(activityMap.get(dto.getSkuId()).get(0).getSkuName());
                bo.getToOrderedBargainBuyerSet().add(bind.getBuyerNick());
                if (dto.getBalanceTime() != null || BaseUtils.getNonNull(dto.getPayBalanceReal()) > 0) {
                    bo.getToOrderedBalanceBuyerSet().add(dto.getBuyerNick());
                }
            }
        }

        Map<Long, List<PresaleOrderDTO>> todayCreatedOrderMap = todayCreatedOrders.stream().collect(Collectors.groupingBy(PresaleOrderDTO::getOrderId));

        List<CsOrderBindDO> todayCreatedOrderBinds = csOrderBindDao.selectPresaleBindByOrderIdsForPresalePerformance(
                shop, DateUtils.addDays(dateStart, -1), dateEnd, todayCreatedOrderMap.keySet(), 1);

        for (CsOrderBindDO bind : todayCreatedOrderBinds) {
            //过滤父订单
            if(directTradeId.contains(bind.getOrderId())){
                continue;
            }
            for (PresaleOrderDTO dto : todayCreatedOrderMap.get(bind.getOrderId())) {
                //若此订单sku 非预售活动列表中sku 则跳出
                if (!activityMap.containsKey(dto.getSkuId())) continue;

                boolean have = false;
                for (CsPerformancePresaleBO bo : performances) {
                    if (bo.getSkuId().equals(dto.getSkuId()) && bo.getCsNick().equals(bind.getCsNick())) {
                        have = true;
                        bo.getOrderedBuyerSet().add(dto.getBuyerNick());
                        bo.setOrderedSkuNum(bo.getOrderedSkuNum() + dto.getGoodsNum());
                        if (dto.getBargainTime() != null) {
                            bo.getOrderedBargainBuyerSet().add(dto.getBuyerNick());
                            bo.setOrderedBargainSkuNum(bo.getOrderedBargainSkuNum() + dto.getGoodsNum());
                            bo.setOrderedBargainAmount(bo.getOrderedBargainAmount() + dto.getPayBargainReal());
                        }
                        if (dto.getBalanceTime() != null || BaseUtils.getNonNull(dto.getPayBalanceReal()) > 0) {
                            bo.getOrderedBalanceBuyerSet().add(dto.getBuyerNick());
                            bo.setOrderedBalanceSkuNum(bo.getOrderedBalanceSkuNum() + dto.getGoodsNum());
                            bo.setOrderedBalanceAmount(bo.getOrderedBalanceAmount() + dto.getPayBalanceReal());
                        }
                    }
                }
                if (have) continue;
                CsPerformancePresaleBO bo = new CsPerformancePresaleBO(activity0.getActivityId(), dto.getSkuId(), bind.getCsNick(), dateStart);
                if (performances.contains(bo)) continue;
                performances.add(bo);

                bo.setSkuName(activityMap.get(dto.getSkuId()).get(0).getSkuName());
                bo.getOrderedBuyerSet().add(dto.getBuyerNick());
                bo.setOrderedSkuNum(bo.getOrderedSkuNum() + dto.getGoodsNum());
                if (dto.getBargainTime() != null) {
                    bo.getOrderedBargainBuyerSet().add(dto.getBuyerNick());
                    bo.setOrderedBargainSkuNum(bo.getOrderedBargainSkuNum() + dto.getGoodsNum());
                    bo.setOrderedBargainAmount(bo.getOrderedBargainAmount() + dto.getPayBargainReal());
                }
                if (dto.getBalanceTime() != null || BaseUtils.getNonNull(dto.getPayBalanceReal()) > 0) {
                    bo.getOrderedBalanceBuyerSet().add(dto.getBuyerNick());
                    bo.setOrderedBalanceSkuNum(bo.getOrderedBalanceSkuNum() + dto.getGoodsNum());
                    bo.setOrderedBalanceAmount(bo.getOrderedBalanceAmount() + dto.getPayBalanceReal());
                }
            }
        }

        //店铺下某一活动定金期内某天(1或多个sku) 的 客服 预售绩效
        Set<CsPerformancePresaleDO> csPerformancePresaleSet = new HashSet<>(performances.size());
        for (CsPerformancePresaleBO bo : performances) {
            CsPerformancePresaleDO presaleDO = new CsPerformancePresaleDO(shop.getShopId(), bo.getDate(),
                    bo.getCsNick(), bo.getSkuId(), bo.getActivityId());
            presaleDO.setSkuName(bo.getSkuName());
            presaleDO.setConsultBuyerNum(bo.getConsultBuyerSet().size());
            presaleDO.setEnquiryBuyerNum(bo.getEnquiryBuyerSet().size());
            presaleDO.setEnquiryOrderedBuyerNum(bo.getEnquiryOrderedBuyerSet().size());
            presaleDO.setEnquiryOrderedSkuNum(bo.getEnquiryOrderedSkuNum());
            presaleDO.setEnquiryOrderedBargainBuyerNum(bo.getEnquiryOrderedBargainBuyerSet().size());
            presaleDO.setEnquiryOrderedBargainSkuNum(bo.getEnquiryOrderedBargainSkuNum());
            presaleDO.setEnquiryOrderedBargainAmount(bo.getEnquiryOrderedBargainAmount());
            presaleDO.setEnquiryOrderedBalanceBuyerNum(bo.getEnquiryOrderedBalanceBuyerSet().size());
            presaleDO.setEnquiryOrderedBalanceSkuNum(bo.getEnquiryOrderedBalanceSkuNum());
            presaleDO.setEnquiryOrderedBalanceAmount(bo.getEnquiryOrderedBalanceAmount());
            presaleDO.setToOrderedBargainBuyerNum(bo.getToOrderedBargainBuyerSet().size());
            presaleDO.setToOrderedBalanceBuyerNum(bo.getToOrderedBalanceBuyerSet().size());
            presaleDO.setOrderedBuyerNum(bo.getOrderedBuyerSet().size());
            presaleDO.setOrderedSkuNum(bo.getOrderedSkuNum());
            presaleDO.setOrderedBargainBuyerNum(bo.getOrderedBargainBuyerSet().size());
            presaleDO.setOrderedBargainSkuNum(bo.getOrderedBargainSkuNum());
            presaleDO.setOrderedBargainAmount(bo.getOrderedBargainAmount());
            presaleDO.setOrderedBalanceBuyerNum(bo.getOrderedBalanceBuyerSet().size());
            presaleDO.setOrderedBalanceSkuNum(bo.getOrderedBalanceSkuNum());
            presaleDO.setOrderedBalanceAmount(bo.getOrderedBalanceAmount());
            csPerformancePresaleSet.add(presaleDO);
        }

        long e = System.currentTimeMillis();
        //logger.info("8.2 - {}, {}, {}, 预售客服绩效计算耗时：{}ms", shop.getTitle(), shop.getShopId(), dateStart, e - s);
        return csPerformancePresaleSet;
    }

    /**
     * 8.2 预约订单绩效
     *
     * @param jobShop   shop
     * @param jobDate   date
     * @param isDelData del
     * @throws Exception e
     */
    @Override
    public void handlePerformanceForPreordain(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {

        //long s = System.currentTimeMillis();

        JobShopDTO shop = jobShop.getShop();
        Date date = jobDate.getDate();
        logger.info("8.2 - {}, {}, {}, 预约订单绩效效开始...", shop.getTitle(), shop.getShopId(), jobDate.getDate());

        //查询该店铺当天所有类型未锁定客服
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_ALL, CommonConstants.CS_STATUS_NOT_LOCK);
        List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
        if (CollectionUtils.isEmpty(targetCsLst)) return;
        Set<String> noLockCsNicks = targetCsLst.stream().map(CsDTO::getNick).collect(Collectors.toSet());


        List<ReserveActivityDTO> allActivitys = reserveActivityDao.selectByShopIdAndDateForReservePerformance(shop, date);
        //查询该店铺进行中的预约活动
        if (CollectionUtils.isEmpty(allActivitys)) {
            logger.info("8.2 - {}, {}, {}, 店铺在这天无预约活动", shop.getTitle(), shop.getShopId(), jobDate.getDate());
            return;
        }

        //提取活动列表中所有skuId
        Set<Long> skuIds = allActivitys.stream().map(ReserveActivityDTO::getSkuId).collect(Collectors.toSet());
        Map<Long, String> skuMap = shopGoodsSkuDao.selectByShopIdAndSkuIds(shop, skuIds)
                .stream().collect(Collectors.groupingBy(ShopGoodSkuDTO::getSkuId,
                        Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0).getSkuName())));
        ListIterator<ReserveActivityDTO> iterator = allActivitys.listIterator();
        while (iterator.hasNext()) {
            ReserveActivityDTO activity = iterator.next();
            if (activity.getActivityId() == null
                    || activity.getSkuId() == null || !skuMap.containsKey(activity.getSkuId())
                    || (activity.getCancelTime() != null && activity.getCancelTime().after(date))) //剔除活动取消 且 取消时间 > 当天的
                iterator.remove();
            activity.setSkuName(skuMap.get(activity.getSkuId()));

        }

        //预售活动按activityId分组
        Map<String, List<ReserveActivityDTO>> allActivityMap = allActivitys.stream()
                .collect(Collectors.groupingBy(ReserveActivityDTO::getActivityId));

        //循环活动列表
        for (Map.Entry<String, List<ReserveActivityDTO>> next : allActivityMap.entrySet()) {
            List<ReserveActivityDTO> activitys = next.getValue();//
            String activityId = next.getKey();
            //该预售活动中所有skuId
            Set<Long> activitySkuIds = activitys.stream().map(ReserveActivityDTO::getSkuId).collect(Collectors.toSet());


            Date startTime = activitys.get(0).getStartTime();
            Date endTime = activitys.get(0).getPanicbuyingEtime();

            //当前活动定金期开始至当前
            ArrayList<Date> dates = DateUtil.splitDate(startTime,
                    endTime.after(jobDate.getEndDate()) ? jobDate.getEndDate() : endTime);
            //查询定金期 所有关于预售活动sku的会话
            List<CsChatSessionDO> sessionList = csChatSessionDao.selectByShopIdAndDateAndSkuIds(shop,
                    dates.get(0), dates.get(dates.size() - 1),
                    activitySkuIds);
            Map<Date, List<CsChatSessionDO>> sessionMap = sessionList.stream().collect(Collectors.groupingBy(CsChatSessionDO::getDate));

            //店铺下某一活动期内(1或多个sku) 的 店铺预约绩效
            Set<ShopPerformancePreordainDO> shopPerformanceSet = new HashSet<>();
            //店铺下某一活动期内(1或多个sku) 的 店铺预约绩效
            Set<CsPerformancePreordainDO> csPerformanceSet = new HashSet<>();

            for (Date countDate : dates) {
                Date countDateEnd = DateUtil.getEndTimeOfDate(countDate);
                shopPerformanceSet.addAll(countShopPerformancePreordain(shop, activitys, countDate, countDateEnd));
                csPerformanceSet.addAll(countCsPerformancePreordain(shop, noLockCsNicks, activitys, countDate, countDateEnd, sessionMap.get(countDate)));
            }


            int shopDel = shopPerformancePreordainDao.deleteByShopIdAndDateAndActivityId(shop, dates.get(0), dates.get(dates.size() - 1), activityId);
            int shopInsert = shopPerformancePreordainDao.batchInsertShopPerformancePreordain(shop, shopPerformanceSet);


            int csDel = csPerformancePreordainDao.deleteByShopIdAndDateAndActivityId(shop, dates.get(0), dates.get(dates.size() - 1), activityId);
            int csInsert = csPerformancePreordainDao.batchInsertCsPerformancePreordain(shop, csPerformanceSet);

            logger.info("8.2 - {}, {}, {}, 预约绩效{}活动 - shopDel={}, shopInsert={}, csDel={}, csInsert={}", shop.getTitle(), shop.getShopId(), jobDate.getDate(), activityId, shopDel, shopInsert, csDel, csInsert);
        }
        //long e = System.currentTimeMillis();
        //logger.info("预约绩效计算：结束 - {}, {}, {}, {}ms", shop.getTitle(), shop.getShopId(), jobDate.getDate(), (e - s));
    }

    private Set<ShopPerformancePreordainDO> countShopPerformancePreordain(JobShopDTO shop, List<ReserveActivityDTO> activitys, Date dateStart, Date dateEnd) {


        Date panicbuyingStime = activitys.get(0).getPanicbuyingStime();
        Date panicbuyingEtime = activitys.get(0).getPanicbuyingEtime();

        //店铺下某一活动某一天(1或多个sku) 的 店铺预约绩效
        Set<ShopPerformancePreordainDO> performances = new HashSet<>(activitys.size());
        for (ReserveActivityDTO activity : activitys) {
            ShopPerformancePreordainDO bo = new ShopPerformancePreordainDO(shop.getShopId(), dateStart, activity.getSkuId(), activity.getActivityId());
            bo.setSkuName(activity.getSkuName());
            performances.add(bo);
        }
        if (dateStart.getTime() >= DateUtil.getStartTimeOfDate(panicbuyingStime).getTime()) {

            List<OrderDTO> orders = orderDao.selectPreordainOrderByCreatedAndBuyers(shop, dateStart, dateEnd, null);
            List<OrderDetailDTO> orderGoods = orderDetailDao.selectOrderGoodsSkuByShopIdLstAndCreateDate(shop,
                    orders.stream().map(OrderDTO::getOrderId).collect(Collectors.toSet()), dateStart, dateEnd);
            Map<Long, List<OrderDetailDTO>> orderGoodsMap = orderGoods.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));

            for (ShopPerformancePreordainDO bo : performances) {
                Set<String> orderedBuyerSet = new HashSet<>();
                Set<String> orderedPaidBuyerSet = new HashSet<>();
                for (OrderDTO order : orders) {
                    List<OrderDetailDTO> orderDetails = orderGoodsMap.get(order.getOrderId());
                    if (orderDetails == null) continue;
                    for (OrderDetailDTO detail : orderDetails) {
                        if (StringUtils.isEmpty(order.getBuyerNick()) || detail.getItemSkuId() == null
                                || !bo.getSkuId().toString().equals(detail.getItemSkuId()))
                            continue;
                        orderedBuyerSet.add(order.getBuyerNick());
                        bo.setOrderedSkuNum(bo.getOrderedSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                        if (order.getPayTime() != null) {
                            orderedPaidBuyerSet.add(order.getBuyerNick());
                            bo.setOrderedPaidSkuNum(bo.getOrderedPaidSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                            bo.setOrderedPaidAmount(bo.getOrderedPaidAmount() + BaseUtils.getNonNull(order.getPayment()));
                        }
                    }
                }
                bo.setOrderedBuyerNum(orderedBuyerSet.size());
                bo.setOrderedPaidBuyerNum(orderedPaidBuyerSet.size());
            }
        }
        return performances;
    }


    private Set<CsPerformancePreordainDO> countCsPerformancePreordain(JobShopDTO shop, Set<String> noLockCsNicks,
                                                                      List<ReserveActivityDTO> activitys,
                                                                      Date dateStart, Date dateEnd, List<CsChatSessionDO> sessions) {
        Map<Long, List<ReserveActivityDTO>> activityMap = activitys.stream().collect(Collectors.groupingBy(ReserveActivityDTO::getSkuId));

        Date panicbuyingStime = activitys.get(0).getPanicbuyingStime();//预约活动开始时间
        Date panicbuyingEtime = activitys.get(0).getPanicbuyingEtime();//预约活动结束时间


        Set<CsPerformancePreordainBO> performances = new HashSet<>();

        //当日所有询单的买家昵称集合
        Set<String> enquiryBuyers = new HashSet<>();
        if (CollectionUtils.isNotEmpty(sessions)) {
            //预售绩效临时容器模型 Map<skuId, Map<csNick, Set<buyerNick>>>
            Map<Long, Map<String, Set<String>>> skuToCsToBuyerMap = new HashMap<>();
            for (ReserveActivityDTO activity : activitys) {
                Map<String, Set<String>> csToBuyer = new HashMap<>();
                skuToCsToBuyerMap.put(activity.getSkuId(), csToBuyer);
            }

            for (CsChatSessionDO session : sessions) {
                if (StringUtils.isBlank(session.getCsNick()) || StringUtils.isBlank(session.getCustomer())
                        || session.getSkuId() == null
                        || !noLockCsNicks.contains(session.getCsNick()) ||
                        !skuToCsToBuyerMap.containsKey(session.getSkuId())) continue;

                //按照预售绩效容器模型，填充数据
                Map<String, Set<String>> csToBuyer = skuToCsToBuyerMap.get(session.getSkuId());
                if (csToBuyer.containsKey(session.getCsNick())) {
                    csToBuyer.get(session.getCsNick()).add(session.getCustomer());
                } else {
                    Set<String> buyers = new HashSet<>();
                    buyers.add(session.getCustomer());
                    csToBuyer.put(session.getCsNick(), buyers);
                }
            }

            //参与预售商品session的客服，用于查询chatpeers
            List<String> preordainCss = skuToCsToBuyerMap.values().stream().map(Map::keySet)
                    .flatMap(Collection::stream).collect(Collectors.toList());

            //查询所有关于预约活动sku的聊天关系
            List<CommonCsChatpeerDTO> chatpeers = csChatpeerDao.selectByShopIdAndDateAndCsNicks(shop, dateStart, preordainCss);

            //TODO 填充当天咨询数、询单数
            for (ReserveActivityDTO activity : activitys) {
                Map<String, Set<String>> csToBuyer = skuToCsToBuyerMap.get(activity.getSkuId());
                for (CommonCsChatpeerDTO chatpeer : chatpeers) {
                    if (chatpeer == null || chatpeer.getCsNick() == null || chatpeer.getBuyerNick() == null
                            || chatpeer.getConsult() == null || !chatpeer.getConsult()
                            || !csToBuyer.containsKey(chatpeer.getCsNick())
                            || !csToBuyer.get(chatpeer.getCsNick()).contains(chatpeer.getBuyerNick()))
                        continue;//排除非咨询

                    CsPerformancePreordainBO performanceBO = null;
                    for (CsPerformancePreordainBO bo : performances) {
                        if (bo.getActivityId().equals(activity.getActivityId()) && bo.getSkuId().equals(activity.getSkuId())
                                && bo.getCsNick().equals(chatpeer.getCsNick()) && bo.getDate().equals(dateStart)) {
                            performanceBO = bo;
                        }
                    }
                    if (performanceBO == null) {
                        performanceBO = new CsPerformancePreordainBO(activity.getActivityId(), activity.getSkuId(), chatpeer.getCsNick(), dateStart);
                        performanceBO.setSkuName(activity.getSkuName());
                        performances.add(performanceBO);
                    }
                    performanceBO.getConsultBuyerSet().add(chatpeer.getBuyerNick());
                    if (chatpeer.getEnquiry()) {
                        performanceBO.getEnquiryBuyerSet().add(chatpeer.getBuyerNick());
                        enquiryBuyers.add(chatpeer.getBuyerNick());
                    }
                }
            }
        }

        //当天若大于订购时间，则计算下单数据
        if (dateStart.getTime() >= DateUtil.getStartTimeOfDate(panicbuyingStime).getTime()) {
            List<OrderDTO> orders = new ArrayList<>();
            Set<Long> byselDetalSet = new HashSet<>();

            if (performances.size() > 0) {
                orders = orderDao.selectPreordainOrderByCreatedAndBuyers(shop, dateStart, panicbuyingEtime, enquiryBuyers);
                byselDetalSet.addAll(orders.stream().map(OrderDTO::getOrderId).collect(Collectors.toSet()));
            }
            //获取当日的下单
            Set<Long> todayCreatedOrderIds = orderDao.selectPreordainOrderByCreatedAndBuyers(shop, dateStart, dateEnd, null)
                    .stream().map(OrderDTO::getOrderId).collect(Collectors.toSet());
            //根据当日下单，获取客服下单绑定
            List<CsOrderBindDO> todayCreatedOrderBinds = csOrderBindDao.selectPresaleBindByOrderIdsForPresalePerformance(
                    shop, DateUtils.addDays(dateStart, -1), dateEnd, todayCreatedOrderIds, 1);
            byselDetalSet.addAll(todayCreatedOrderBinds.stream().map(CsOrderBindDO::getOrderId).collect(Collectors.toSet()));
            List<OrderDetailDTO> orderDetailList = orderDetailDao.selectOrderGoodsSkuByShopIdLstAndCreateDate(shop, byselDetalSet, dateStart, panicbuyingEtime);
            orderDetailList.removeIf(detail -> detail.getItemSkuId() == null || !activityMap.containsKey(Long.parseLong(detail.getItemSkuId())));

            //获取当日下单详情，和 计算日咨询卖家的下单详情
            Map<Long, List<OrderDetailDTO>> detailMap = orderDetailList.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));

            /*计算客服询单维度下单及付款数据*/
            for (CsPerformancePreordainBO bo : performances) {
                for (OrderDTO order : orders) {
                    List<OrderDetailDTO> details = detailMap.get(order.getOrderId());
                    if (details == null || StringUtils.isEmpty(order.getBuyerNick())
                            || !bo.getEnquiryBuyerSet().contains(order.getBuyerNick())) continue;
                    for (OrderDetailDTO detail : details) {
                        if (detail.getItemSkuId() == null
                                || !bo.getSkuId().equals(Long.parseLong(detail.getItemSkuId()))) continue;
                        bo.getEnquiryOrderedBuyerSet().add(order.getBuyerNick());
                        bo.setEnquiryOrderedSkuNum(bo.getEnquiryOrderedSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                        if (order.getPayTime() != null) {
                            bo.getEnquiryOrderedPaidBuyerSet().add(order.getBuyerNick());
                            bo.setEnquiryOrderedPaidSkuNum(bo.getEnquiryOrderedPaidSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                            bo.setEnquiryOrderedPaidAmount(bo.getEnquiryOrderedPaidAmount() + BaseUtils.getNonNull(order.getPayment()));
                        }
                    }
                }
            }

            /*计算客服下单维度数据*/
            for (CsOrderBindDO bind : todayCreatedOrderBinds) {
                List<OrderDetailDTO> details = detailMap.get(bind.getOrderId());
                if (CollectionUtils.isEmpty(details)) continue;
                for (OrderDetailDTO detail : details) {
                    boolean have = false;
                    for (CsPerformancePreordainBO bo : performances) {
                        if (!bo.getCsNick().equals(bind.getCsNick())
                                || !bo.getSkuId().equals(Long.parseLong(detail.getItemSkuId())))
                            continue;
                        have = true;
                        bo.getOrderedBuyerSet().add(bind.getBuyerNick());
                        bo.setOrderedSkuNum(bo.getOrderedSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                        if (BaseUtils.getNonNull(bind.getOrderPayment()) > 0) {
                            bo.getOrderedPaidBuyerSet().add(bind.getBuyerNick());
                            bo.setOrderedPaidSkuNum(bo.getOrderedPaidSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                            bo.setOrderedPaidAmount(bo.getOrderedPaidAmount() + BaseUtils.getNonNull(bind.getOrderPayment()));
                        }
                    }
                    if (have) continue;
                    CsPerformancePreordainBO bo = new CsPerformancePreordainBO(activitys.get(0).getActivityId(), Long.parseLong(detail.getItemSkuId()), bind.getCsNick(), dateStart);
                    if (performances.contains(bo)) continue;
                    performances.add(bo);

                    bo.setSkuName(activityMap.get(bo.getSkuId()).get(0).getSkuName());
                    bo.getOrderedBuyerSet().add(bind.getBuyerNick());
                    bo.setOrderedSkuNum(bo.getOrderedSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                    if (BaseUtils.getNonNull(bind.getOrderPayment()) > 0) {
                        bo.getOrderedPaidBuyerSet().add(bind.getBuyerNick());
                        bo.setOrderedPaidSkuNum(bo.getOrderedPaidSkuNum() + BaseUtils.getNonNull(detail.getItemNum()));
                        bo.setOrderedPaidAmount(bo.getOrderedPaidAmount() + BaseUtils.getNonNull(bind.getOrderPayment()));
                    }
                }
            }
        }
        //封装缓存数据库数据列表
        Set<CsPerformancePreordainDO> csPerformancePreordains = new HashSet<>(performances.size());
        for (CsPerformancePreordainBO bo : performances) {
            CsPerformancePreordainDO preordainDO = new CsPerformancePreordainDO(shop.getShopId(), bo.getDate(),
                    bo.getCsNick(), bo.getSkuId(), bo.getActivityId());
            preordainDO.setSkuName(bo.getSkuName());
            preordainDO.setConsultBuyerNum(bo.getConsultBuyerSet().size());
            preordainDO.setEnquiryBuyerNum(bo.getEnquiryBuyerSet().size());
            preordainDO.setEnquiryOrderedBuyerNum(bo.getEnquiryOrderedBuyerSet().size());
            preordainDO.setEnquiryOrderedSkuNum(bo.getEnquiryOrderedSkuNum());
            preordainDO.setEnquiryOrderedPaidBuyerNum(bo.getEnquiryOrderedPaidBuyerSet().size());
            preordainDO.setEnquiryOrderedPaidSkuNum(bo.getEnquiryOrderedPaidSkuNum());
            preordainDO.setEnquiryOrderedPaidAmount(bo.getEnquiryOrderedPaidAmount());
            preordainDO.setOrderedBuyerNum(bo.getOrderedBuyerSet().size());
            preordainDO.setOrderedSkuNum(bo.getOrderedSkuNum());
            preordainDO.setOrderedPaidBuyerNum(bo.getOrderedPaidBuyerSet().size());
            preordainDO.setOrderedPaidSkuNum(bo.getOrderedPaidSkuNum());
            preordainDO.setOrderedPaidAmount(bo.getOrderedPaidAmount());
            csPerformancePreordains.add(preordainDO);
        }
        return csPerformancePreordains;
    }
}
