package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.Constants.GoodFilterEnum;
import com.pes.jd.Constants.PesConstants;
import com.pes.jd.business.GoodsHandleBusiness;
import com.pes.jd.dao.*;
import com.pes.jd.model.BO.ChatLogThreadBO;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.GoodsPurchaseEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.ms.utils.DateUtils;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.pes.jd.util.ServiceDateUtil.calNewOrderIndexDate;


@Service
public class GoodsHandleBusinessImpl implements GoodsHandleBusiness {
	private Logger logger = LoggerFactory.getLogger(GoodsHandleBusinessImpl.class);
	@Resource
	private CsRecommendGoodsDao csRecommendGoodsDao;
	@Resource
	private CustConsultGoodsDao custConsultGoodsDao;
	@Resource
	private GoodsRecommendSummaryDao  goodsRecommendSummaryDao;
	@Resource
	private GoodsConsultSummaryDao goodsConsultSummaryDao;
	@Resource
	private CsOrderIndexDao csOrderIndexDao;
	@Resource
	private OrderDetailDao orderDetailDao;
	@Resource
	private CsChatpeerDao csChatpeerDao;
	@Resource
	private OrderDao orderDao;
	@Resource
	private CsOrderBindDao csOrderBindDao;
    @Resource
    private CsCustRecommendConsultSkuDao csCustRecommendConsultSkuDao;
	/**
	 * 商品咨询/推荐明细
	 */
	@Override
	public void handleRecommentAndConsultGoods(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
		long s=System.currentTimeMillis();

		List<CsDTO> csLst = jobShop.getCsLst();

		if (CollectionUtils.isEmpty(csLst)) {
			logger.info("shop of csLst is empty..");
			return ;
		}
		
		Date date = jobDate.getDate();
		Date startDate = jobDate.getStartDate();
		Date endDate = jobDate.getEndDate();
		
		if (isDelData) {
			// 删除客服推荐和客户咨询表数据
			int deleteRecomNum = csRecommendGoodsDao.deleteCsRecommendGoodsByShopIdAndDate(jobShop.getShop(), startDate,endDate);
			if(logger.isDebugEnabled()){

				logger.debug("delete csrecommendGoods num：{}", deleteRecomNum);
			}
			int consolutNum = custConsultGoodsDao.deleteCustConsultGoodsByShopIdAndDate(jobShop.getShop(), startDate,endDate);
			if(logger.isDebugEnabled()){

				logger.debug("delete custConsultGoods num：{}", consolutNum);
			}
		}
		
		handleCsRecommentBuyerConsultGoods(jobShop, csLst, date,  startDate, endDate);
		
		long e=System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("******shopName:{} hand CsRecomment/Consult time :{} ms",jobShop.getShop().getTitle(),(e-s));
		}
	}
	
	/**
	 * @Description:（商品推荐/咨询汇总） 
	 *
	 */
	@Override
	public void handleCsRecommentBuyerConsultGoodsSummary(JobShopQuery jobShop, JobDateQuery jobDate,
			boolean isDelData) {

		JobShopDTO shop = jobShop.getShop();
		Date date = jobDate.getDate();
		Date startDate = jobDate.getStartDate();
		Date endDate = jobDate.getEndDate();

		List<CsDTO> csNickLst = jobShop.getCsLst();

		if (CollectionUtils.isEmpty(csNickLst)) {
			if(logger.isDebugEnabled()){

				logger.debug("csNickLst is Empty");
			}
			return;
		}
		if (isDelData) {
			// 删除
			int recommentNum = goodsRecommendSummaryDao.deleteGoodsRecommendSummaryByShopIdAndByDate(shop, startDate,
					endDate);
			if(logger.isDebugEnabled()){

				logger.debug("delete GoodsRecommendSummary num:{}", recommentNum);
			}
			int consult = goodsConsultSummaryDao.deleteGoodsConsultSummaryByShopIdAndByDate(shop, startDate, endDate);
			if(logger.isDebugEnabled()){

				logger.debug("delete GoodsConsultSummary num:{}", consult);
			}
		}
		Map<String, List<OrderDetailDTO>> skuIdOrderDetailMap=Maps.newHashMap();
		List<OrderDetailDTO> orderDetailLst=orderDetailDao.selectShopOrderDetailForGoodsAnalysis(shop, startDate, endDate);
		if(CollectionUtils.isNotEmpty(orderDetailLst)){
			skuIdOrderDetailMap.putAll(orderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getItemSkuId)));
		}
		
		// -------客服推荐&咨询维度-----------------------------------------------------------------------------
		handCsRecommentAndConsultSummary(jobShop, date, startDate, endDate,skuIdOrderDetailMap);
	}

	/**
	 * @Description:（商品推荐/咨询汇总）
	 *
	 */
	@Override
	public void handleCsConsultGoodsSummaryV2(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
		long startTime = System.currentTimeMillis();
		List<Date> dates = jobDate.getCsOrderIndexDates();
		//copy from orderIndex
		//询单有效期小于三天要用三天来算，因为重拉三天前的订单信息会有三天前的付款询单有效期设置低于三天少算
		List<Date> calDate = calNewOrderIndexDate(jobDate, jobShop.getShopSystemsetting().getEnquiryValidDurationTime(), dates);
		if (CollectionUtils.isEmpty(calDate)) {
			logger.warn("req dates is empty");
			return;
		}
		JobShopDTO shop = jobShop.getShop();
		for (Date date : calDate) {
			Integer enquiryDays = jobShop.getShopSystemsetting().getEnquiryValidDurationTime();
			Date startDate = DateUtil.getStartTimeOfDate(date);
			Date endDate = DateUtil.getEndTimeOfDate(date);
			Date endEnquiryDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(DateUtil.getEndTimeOfDate(date), enquiryDays - 1));

			if (CollectionUtils.isEmpty(jobShop.getCsLst()))return;
			if (isDelData) {// 删除
				custConsultGoodsDao.deleteCustConsultGoodsByShopIdAndDateV2(shop, DateUtil.getStartTimeOfDate(date), DateUtil.getEndTimeOfDate(date));
				goodsConsultSummaryDao.deleteGoodsConsultSummaryByShopIdAndByDateV2(shop, DateUtil.getStartTimeOfDate(date), DateUtil.getEndTimeOfDate(date));
			}
			//pes_order_goods_sku
			List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectShopOrderDetailForGoodsAnalysis(shop, startDate, endEnquiryDate);
			//orderBind for enquiryDays
			List<CsOrderBindDTO> orderBindPayLst = csOrderBindDao.selectOrderBindPayOrderByShopIdAndDate(shop, startDate, endEnquiryDate);
			Map<String, List<CsOrderBindDTO>> payGroupByCsNick = orderBindPayLst.stream().collect(Collectors.groupingBy(CsOrderBindDTO::getCsNick));
			//查询咨询和推荐的商品, group by csNick
			List<CsCustRecommendConsultSkuDO> csCustRecommendLst = csCustRecommendConsultSkuDao.selectByEnquiryDaysAndType(shop, startDate, endDate, 2);
//			Map<String, List<CsCustRecommendConsultSkuDO>> csCustGroupByCsNick = csCustLst.stream().collect(Collectors.groupingBy(CsCustRecommendConsultSkuDO::getCsNick));
			Map<String, List<CsCustRecommendConsultSkuDO>> custRecommendMap = csCustRecommendLst.stream().collect(Collectors.groupingBy(CsCustRecommendConsultSkuDO::getCsNick));
			//查询询单（只要有数据，每个sku都算）
			List<CommonCsChatpeerDTO> chatPeerLst = csChatpeerDao.selectChatPeerByShopIdByDate(shop, date);
			Map<String, List<CommonCsChatpeerDTO>> receiveGroupByCsNick = chatPeerLst.stream().collect(Collectors.groupingBy(CommonCsChatpeerDTO::getCsNick));
			// -------客服推荐&咨询维度-----------------------------------------------------------------------------
			List<GoodsConsultSummaryDOV2> goodsConsultSummaryLst = Lists.newArrayList();
			List<CsDTO> csNickLst = jobShop.getCsLst();
			for (CsDTO cs: csNickLst) {
				if (!custRecommendMap.containsKey(cs.getNick())) continue;
				//计算
//				List<CsCustRecommendConsultSkuDO> thisCsCustRecommendConsultSku = csCustGroupByCsNick.get(cs.getNick());
				List<CsOrderBindDTO> thisPayLst = payGroupByCsNick.get(cs.getNick());
				List<CommonCsChatpeerDTO> thisPeerLst = receiveGroupByCsNick.get(cs.getNick());
				//custRecommendMap
				Map<Long, List<CsCustRecommendConsultSkuDO>> thisCustRecommendSkuMap = custRecommendMap.get(cs.getNick()).stream().collect(Collectors.groupingBy(CsCustRecommendConsultSkuDO::getSkuId));
				List<GoodsConsultSummaryDOV2> thisSummaryList = handConsultSummaryV2(shop, cs.getNick(), date, orderDetailLst, thisPayLst, thisCustRecommendSkuMap, thisPeerLst);
				if(CollectionUtils.isNotEmpty(thisSummaryList)){
					goodsConsultSummaryLst.addAll(thisSummaryList);
				}
			}
			goodsConsultSummaryDao.batchInsertGoodsConsultSummaryV2(shop, date, goodsConsultSummaryLst);
		}
		long endTime = System.currentTimeMillis();
		logger.info("time cost: {} s", (endTime - startTime) / 1000);
	}

	/**
	 * 根据订单获取最终需要因商品过滤的单号
	 * @param jobShop
	 * @param startDate
	 * @param endDate
	 * @param searchOrderIds
	 * @return
	 */
	@Override
	public Map<String, Object> getFilterOrderIdsOfFinal(JobShopQuery jobShop, Date startDate, Date endDate, List<Long> searchOrderIds) {
		Map<String, Object> resultMap = new HashMap<>();
		startDate = DateFormatUtils.getStartTimeOfDate(startDate);
		endDate = DateFormatUtils.getEndTimeOfDate(startDate);
		//过滤的商品 订单里面是过略的sku就过滤，如果有没过滤的sku就不过滤
		List<GoodsFilterDTO> goodFilterLst = jobShop.getGoodFilterLst();
		List<OrderDetailDTO> notFilterOrderDetailLst;
//        fix:649
		List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByShopIdLstAndDate(jobShop.getShop(),
				DateUtils.getDateByPeriod(startDate, -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE - jobShop.getShopSystemsetting().getEnquiryValidDurationTime() - 1),
				searchOrderIds,
				startDate,
				endDate);
		boolean haveFilterSku = CollUtil.isNotEmpty(goodFilterLst) && goodFilterLst.size() > 0;
		Set<Long> filterOrderIdsOfFinal = Sets.newHashSet();//最终需要过滤的订单
		if (haveFilterSku) {//需要过滤的sku
			List<Long> filterskuIds = goodFilterLst.stream().map(GoodsFilterDTO::getNumIid).collect(Collectors.toList());
			Map<String, List<OrderDetailDTO>> groupBySkuId = orderDetailLst.stream()
					.collect(Collectors.groupingBy(OrderDetailDTO::getItemSkuId));
			Map<Long, List<OrderDetailDTO>> groupByOrderId = orderDetailLst.stream()
					.collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
			Set<Long> filterOrderIds = Sets.newHashSet();
			//添加需要过滤的订单
			//①根据sku找出的需要过滤的订单
			for (Long skuId : filterskuIds) {
				List<OrderDetailDTO> orderDetails = groupBySkuId.get(skuId + "");
				if (CollUtil.isEmpty(orderDetails)) {
					continue;
				}
				orderDetails.forEach(ele -> filterOrderIds.add(ele.getOrderId()));
			}
			//②看看过滤的订单有没有其他sku，有的话不需要过滤
			if (CollUtil.isNotEmpty(filterOrderIds)) {
				for (Long filterOrderId : filterOrderIds) {
					List<OrderDetailDTO> orderDetailDTOS = groupByOrderId.get(filterOrderId);//过滤的订单
//                        订单内所有的sku都是要过滤的
					if (orderDetailDTOS.stream().filter(ele -> !filterskuIds.contains(Long.valueOf(ele.getItemSkuId()))).collect(Collectors.toList()).size() <= 0) {
						filterOrderIdsOfFinal.add(filterOrderId);
					}
				}
			}
			//不需要过滤的sku明细
			notFilterOrderDetailLst = orderDetailLst.stream().filter(ele -> !filterskuIds.contains(Long.valueOf(ele.getItemSkuId()))).collect(Collectors.toList());
		}else {//未开启都不需要过滤
			notFilterOrderDetailLst = orderDetailLst;
		}
		resultMap.put(GoodFilterEnum.FILTER_ORDERIDS_OF_FINAL.getKey(),filterOrderIdsOfFinal);
		resultMap.put(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey(),notFilterOrderDetailLst);
		return resultMap;
	}


	private List<GoodsConsultSummaryDO> handConsultSummary(JobShopDTO shop,String csNick,Date date,Date startDate,Date endDate,Map<String, List<OrderDetailDTO>> skuIdOrderDetailMap){
		Long shopId=shop.getShopId();
		List<GoodsConsultSummaryDO> goodsConsultSummaryLst = Lists.newArrayList();
		
			// 买家的咨询sku商品信息
				List<CustConsultGoodsDTO> custGoodsLst = custConsultGoodsDao.selectConsultGoodsPurchaseResultByCsNickAndDate(shop, csNick, startDate, endDate);
					if (CollectionUtils.isEmpty(custGoodsLst)) {
						return goodsConsultSummaryLst;
					}
					Map<Long, List<CustConsultGoodsDTO>> csConsultGoodsMap=custGoodsLst.stream().collect(Collectors.groupingBy(CustConsultGoodsDTO::getSkuId));
					GoodsConsultSummaryDO consultGoods = null;
					for (Entry<Long, List<CustConsultGoodsDTO>> entry : csConsultGoodsMap.entrySet()) {
						Long skuId=entry.getKey();
						List<CustConsultGoodsDTO> csConsultGoodsLst=entry.getValue();
						if(CollectionUtils.isEmpty(csConsultGoodsLst)){
							continue;
						}
						consultGoods = new GoodsConsultSummaryDO(0, 0);
						consultGoods.setCsNick(csNick);
						consultGoods.setDate(date);
						consultGoods.setShopId(shopId);
						consultGoods.setSkuId(skuId);
						Double purchasesAmount = 0.0;
						Integer purchasesNum = 0;
						Integer consultNum=0;
						Integer skuPurchasesBuyerNum=0;
						Set<String> consultBuyerSet=Sets.newHashSet();
						Set<String> purchaseBuyerSet=Sets.newHashSet();
						
						List<OrderDetailDTO> successOrderLst = skuIdOrderDetailMap.get(String.valueOf(skuId));
						for (CustConsultGoodsDTO goods : csConsultGoodsLst) {
							consultBuyerSet.add(goods.getCustomer());
							if (Objects.equals(goods.getResult(), GoodsPurchaseEnum.DEAL.getType())) {
								purchaseBuyerSet.add(goods.getCustomer());
								// 获取购买件数和购买金额
								if (CollectionUtils.isNotEmpty(successOrderLst)) {
									for (OrderDetailDTO order : successOrderLst) {
										if (order.getBuyerNick().equals(goods.getCustomer())
												&& order.getOrderId().equals(goods.getOrderId())) {
											purchasesNum += order.getItemNum();
											Double jdPrice=order.getItemPrice() == null ? 0.0: order.getItemNum()*Double.valueOf(order.getItemPrice()) ;
											Double  sellerRate=order.getTotalFee() == null ? 0.0: order.getTotalFee()>0.0?jdPrice/Double.valueOf(order.getTotalFee()):0.0;
											Double	saleAmount=jdPrice-(sellerRate*(order.getSellerDiscount() == null ? 0.0: Double.valueOf(order.getSellerDiscount())));
											purchasesAmount += saleAmount;
										}
									}
								}
							}
						}
						consultNum = consultBuyerSet.size();
						skuPurchasesBuyerNum = purchaseBuyerSet.size();
						consultGoods.setPurchasesGoodsNum(purchasesNum);
						consultGoods.setPurchasesAmount(purchasesAmount);
						consultGoods.setConsultNum(consultNum);
						consultGoods.setPurchasesBuyerNum(skuPurchasesBuyerNum);
						goodsConsultSummaryLst.add(consultGoods);
					}
					return goodsConsultSummaryLst;
	}

	private List<GoodsConsultSummaryDOV2> handConsultSummaryV2(JobShopDTO shop, String csNick, Date date,
															   List<OrderDetailDTO> orderDetailLst,
															   List<CsOrderBindDTO> thisOrderBindLst,
															   Map<Long, List<CsCustRecommendConsultSkuDO>> thisCustRecommendMap,
															   List<CommonCsChatpeerDTO> thisPeerLst){
		if(CollectionUtils.isEmpty(thisOrderBindLst))
			thisOrderBindLst = Lists.newArrayList();
		Set<String> paidOrderAndBuyer = thisOrderBindLst.stream().map(t -> t.getOrderId() + t.getBuyerNick()).collect(Collectors.toSet());
		Map<Long, String> paidOrderCsMap = thisOrderBindLst.stream().collect(Collectors.toMap(CsOrderBindDTO::getOrderId, o -> o.getCsNick() + o.getBuyerNick(), (o1, o2) -> o1));
		//过滤出付款后的数据
		Map<String, List<OrderDetailDTO>> paidDetailGroupBySku = orderDetailLst.stream()
				.filter(t -> paidOrderAndBuyer.contains(t.getOrderId() + t.getBuyerNick()))
				.collect(Collectors.groupingBy(OrderDetailDTO::getItemSkuId));
		//is_receive set
		Set<String> isReceiveSet = new HashSet<>();
		//is_enquiry set
		Set<String> isEnquirySet = new HashSet<>();
		if(CollectionUtils.isNotEmpty(thisPeerLst)){
			thisPeerLst.forEach(t -> {
				if(t.getReceive()){
					isReceiveSet.add(t.getCsNick() + t.getBuyerNick());
				}
				if(t.getEnquiry()){
					isEnquirySet.add(t.getCsNick() + t.getBuyerNick());
				}
			});
		}
		List<GoodsConsultSummaryDOV2> goodsConsultSummaryLst = Lists.newArrayList();
		List<CustConsultGoodsV2DO> custGoodsV2Lst = new ArrayList<>();
		for (Entry<Long, List<CsCustRecommendConsultSkuDO>> thisEntry : thisCustRecommendMap.entrySet()) {
			HashSet<String> repeatFilter = Sets.newHashSet();
			Long thisSkuId = thisEntry.getKey();
			List<CsCustRecommendConsultSkuDO> custRecommendLst = thisEntry.getValue();
			if(CollectionUtils.isEmpty(custRecommendLst))continue;

			GoodsConsultSummaryDOV2 consultGoodsSummary = new GoodsConsultSummaryDOV2(0, 0, 0, 0, 0.0d);
			consultGoodsSummary.setCsNick(csNick);
			consultGoodsSummary.setDate(date);
			consultGoodsSummary.setShopId(shop.getShopId());
			consultGoodsSummary.setSkuId(thisSkuId);
			double payAmount = 0.0;
			int payGoodsNum = 0;
			int isReceiveNum = 0;
			int isEnquiryNum = 0;
			Set<String> buyerSet = Sets.newHashSet();

			List<OrderDetailDTO> successOrderLst = paidDetailGroupBySku.get(String.valueOf(thisSkuId));
			for (CsCustRecommendConsultSkuDO thisCustRecommend : custRecommendLst) {
				if(!thisCustRecommend.getCsNick().equals(csNick))continue;
				CustConsultGoodsV2DO thisCustConsultV2 = new CustConsultGoodsV2DO();
				thisCustConsultV2.setIsEnquiry(0);
				thisCustConsultV2.setResult(0);
				thisCustConsultV2.setOrderId(null);
				thisCustConsultV2.setCsNick(thisCustRecommend.getCsNick());
				thisCustConsultV2.setShopId(shop.getShopId());
				thisCustConsultV2.setDate(date);
				thisCustConsultV2.setCustomer(thisCustRecommend.getBuyerNick());
				thisCustConsultV2.setSkuId(thisCustRecommend.getSkuId());
				String key = thisCustRecommend.getCsNick() + thisCustRecommend.getBuyerNick();

				String filterKey = thisCustRecommend.getSkuId() + key;
				if(!repeatFilter.contains(filterKey) && isReceiveSet.contains(key)){
					isReceiveNum += 1;
				}else {
					continue;
				}
				if(!repeatFilter.contains(filterKey) && isEnquirySet.contains(key)){
					thisCustConsultV2.setIsEnquiry(1);
					isEnquiryNum += 1;
				}
				repeatFilter.add(filterKey);
				//付款判定
				if (CollectionUtils.isNotEmpty(successOrderLst)) {
					for (OrderDetailDTO order : successOrderLst) {
						if (order.getBuyerNick().equals(thisCustRecommend.getBuyerNick())
								&& paidOrderCsMap.get(order.getOrderId()).equals(thisCustRecommend.getCsNick() + thisCustRecommend.getBuyerNick())){

							buyerSet.add(thisCustRecommend.getBuyerNick());

							payGoodsNum += order.getItemNum();
							double jdPrice = order.getItemPrice() == null ? 0.0: order.getItemNum() * order.getItemPrice() ;
							double sellerRate = order.getTotalFee() == null ? 0.0: order.getTotalFee() > 0.0?jdPrice / order.getTotalFee() : 0.0;
							double saleAmount = jdPrice - (sellerRate*(order.getSellerDiscount() == null ? 0.0: order.getSellerDiscount()));
							payAmount += saleAmount;
							thisCustConsultV2.setResult(1);
							thisCustConsultV2.setOrderId(order.getOrderId());
							break;
						}
					}
				}
				custGoodsV2Lst.add(thisCustConsultV2);
			}

			consultGoodsSummary.setPayGoodsNum(payGoodsNum);
			consultGoodsSummary.setPayAmount(payAmount);
			consultGoodsSummary.setPayNum(buyerSet.size());
			consultGoodsSummary.setEnquiryNum(isEnquiryNum);
			consultGoodsSummary.setReceiveNum(isReceiveNum);
			goodsConsultSummaryLst.add(consultGoodsSummary);
		}
		custConsultGoodsDao.batchInsertCustConsultGoodsV2(shop, date, custGoodsV2Lst);
		return goodsConsultSummaryLst;
	}
	private void handCsRecommentAndConsultSummary(JobShopQuery jobShop,Date date,Date startDate,
			Date endDate,Map<String, List<OrderDetailDTO>> skuIdOrderDetailMap) {
		long s=System.currentTimeMillis();
		List<GoodsRecommendSummaryDO> summaryLst = Lists.newArrayList();
		List<GoodsConsultSummaryDO> goodsConsultSummaryLst = Lists.newArrayList();
		JobShopDTO shop = jobShop.getShop();
		List<CsDTO> csNickLst = jobShop.getCsLst();
		
		for (CsDTO cs : csNickLst) {
			String csNick = cs.getNick();
			summaryLst.addAll(handCsRecommendSummary(shop, csNick, date, startDate, endDate, skuIdOrderDetailMap));
			goodsConsultSummaryLst.addAll(handConsultSummary(shop, csNick, date, startDate, endDate, skuIdOrderDetailMap));

		}
		int recommendSummaryNum = goodsRecommendSummaryDao.batchInsertGoodsRecommendSummary(shop,date, summaryLst);
		int consultNum = goodsConsultSummaryDao.batchInsertGoodsConsultSummary(shop,date, goodsConsultSummaryLst);
		long e=System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("******shopName:{},hand goodsConsult/reCommend Summary end time:{} ms, batch insert GoodsConsultSummary num:{}-------GoodsRecommendSummary num:{}", shop.getTitle(),
					(e-s),consultNum,recommendSummaryNum);
		}

	}

	private List<GoodsRecommendSummaryDO> handCsRecommendSummary(JobShopDTO shop,String csNick,Date date,Date startDate,Date endDate,Map<String, List<OrderDetailDTO>> skuIdOrderDetailMap){
		List<GoodsRecommendSummaryDO> summaryLst = Lists.newArrayList();
		Long shopId=shop.getShopId();
		long s1=System.currentTimeMillis();
		List<CsRecommendGoodsDTO> csRecommendLst = csRecommendGoodsDao.selectCsRecommentGoodsPurchaseResultByCsNickAndDate(shop, csNick, startDate, endDate);
		long e1=System.currentTimeMillis();
	//	logger.info("csNick:{} selectCsRecommentGoodsPurchaseResultByCsNickAndDate time:{}ms",csNick,e1-s1);
		if (CollectionUtils.isEmpty(csRecommendLst)) {
			return summaryLst;
		}
		Map<Long, List<CsRecommendGoodsDTO>> csReCommendSkuIdMap=csRecommendLst.stream().collect(Collectors.groupingBy(CsRecommendGoodsDTO::getSkuId));
		for (Entry<Long, List<CsRecommendGoodsDTO>> entry : csReCommendSkuIdMap.entrySet()) {
			Long skuId=entry.getKey();
			List<CsRecommendGoodsDTO> csRecommendGoodsLst=entry.getValue();
			if(CollectionUtils.isEmpty(csRecommendGoodsLst)){
				continue;
			}
			GoodsRecommendSummaryDO csAnalyis = new GoodsRecommendSummaryDO(0, 0);
			csAnalyis.setCsNick(csNick);
			csAnalyis.setDate(date);
			csAnalyis.setSkuId(skuId);
			csAnalyis.setShopId(shopId);
			Double purchasesAmount = 0.0;
			Integer purchasesNum = 0;
			Integer recommendNum=0;
			Integer skuPurchasesBuyerNum=0;
			Set<String> recommendBuyerSet=Sets.newHashSet();
			Set<String> purchaseBuyerSet=Sets.newHashSet();
			List<OrderDetailDTO> skuOrderDetailLst = skuIdOrderDetailMap.get(String.valueOf(skuId));
			
			for (CsRecommendGoodsDTO goods : csRecommendGoodsLst) {
				//推荐人数
				recommendBuyerSet.add(goods.getCustomer());
				if (Objects.equals(goods.getResult(),GoodsPurchaseEnum.DEAL.getType())) {
					//购买人数
					purchaseBuyerSet.add(goods.getCustomer());
					if (CollectionUtils.isNotEmpty(skuOrderDetailLst)) {
						for (OrderDetailDTO order : skuOrderDetailLst) {
							//剔除不是客服推荐商品下单的买家
							if(goods.getCustomer().equals(order.getBuyerNick())
									&&order.getOrderId().equals(goods.getOrderId())){
								purchasesNum += order.getItemNum();
								//获取商品销售额的算法 
								//数量*jdPrice -  （数量* jdPrice/orderTotalPrice * sellerDiscount  ）
								Double jdPrice=order.getItemPrice() == null ? 0.0: order.getItemNum()*Double.valueOf(order.getItemPrice()) ;
								Double  sellerRate=order.getTotalFee() == null ? 0.0: order.getTotalFee()>0.0?jdPrice/Double.valueOf(order.getTotalFee()):0.0;
								Double	saleAmount=jdPrice-(sellerRate*(order.getSellerDiscount() == null ? 0.0: Double.valueOf(order.getSellerDiscount())));
								purchasesAmount += saleAmount;
							}
							
						}
					}
				}
			}
			recommendNum = recommendBuyerSet.size();
			skuPurchasesBuyerNum=purchaseBuyerSet.size();
			csAnalyis.setRecommendNum(recommendNum);
			csAnalyis.setPurchasesBuyerNum(skuPurchasesBuyerNum);
			csAnalyis.setPurchasesAmount(purchasesAmount);
			csAnalyis.setPurchasesGoodsNum(purchasesNum);
			summaryLst.add(csAnalyis);
		}
		
		return summaryLst;
	
	}
	
	private void handleCsRecommentBuyerConsultGoods(JobShopQuery jobShop, List<CsDTO> csLst, Date date, Date targetDateStartDate,
			Date targetDateEndDate)  {
		int recommend;
		int consult;

		JobShopDTO shop = jobShop.getShop();
		ShopSystemsettingDTO sys=jobShop.getShopSystemsetting();
		
		//List<ChatlogDTO> chatLogDateLst=csChatlogDao.selectBuyerChatlogByShopIdAndDate(shop, targetDateStartDate, targetDateEndDate);
//		if(CollectionUtils.isEmpty(chatLogDateLst)){
//			logger.info("handleCsRecommentBuyerConsultGoods get chatLog is empty");
//			return ;
//		}
		
		List<CsRecommendGoodsDO> allCsNickRecommendGoodsLst = Lists.newArrayList();
		List<CustConsultGoodsDO> allBuyecustConsultGoodsLst = Lists.newArrayList();
		long s1=System.currentTimeMillis();
//		List<ShopGoodSkuDTO> shopGoodsLst=shopGoodsSkuDao.selectShopGoodsSkuByShopId(jobShop.getShop());
		long e1=System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("selectShopGoodsSkuByShopId  time :{} ms" ,e1-s1);
		}
//		Boolean csRecommendSwitch=sys.getCsRecommendSwitch();
//		Set<Long> shopGoodsSet=Sets.newHashSet();
//		if(CollectionUtils.isNotEmpty(shopGoodsLst)){
//			shopGoodsSet=shopGoodsLst.stream().map(ShopGoodSkuDTO::getSkuId).collect(Collectors.toSet());
//		}
//		String rex = "(?<=//item.jd.com/).*?(?=.html)|(?<=//item.m.jd.com/product/).*?(?=.html)";
//		Pattern pattern=Pattern.compile(rex);
		long s7=System.currentTimeMillis();

		//查询咨询和推荐的商品
        List<CsCustRecommendConsultSkuDO> csCustLst = csCustRecommendConsultSkuDao.selectCsCustRecommendConsultSku(shop, date);

		for (CsDTO cs : csLst) {
            String csNick = cs.getNick();
			List<CsCustRecommendConsultSkuDO> csCustList = csCustLst.stream().filter(ele -> ele.getCsNick().equals(csNick)).collect(Collectors.toList());
			if (CollectionUtils.isEmpty(csCustList)) continue;
			long s2 = System.currentTimeMillis();
            //获取是接待的买家
            List<String> receiveBuyerLst = csChatpeerDao.selectChatPeerLstByDateByCsNickForReceive(shop, csNick, date);
			csCustList.removeIf(sku -> CollectionUtils.isNotEmpty(receiveBuyerLst) && !receiveBuyerLst.contains(sku.getBuyerNick()));
			if (CollectionUtils.isEmpty(csCustList)) continue;
			long e2 = System.currentTimeMillis();
            //logger.info("csNick:{} get chatpeer time:{}ms",csNick,e2-s2);
            long s3 = System.currentTimeMillis();
            // 获取客服与买家的聊天内容
//            List<ChatlogDTO> csChatlogLst = selectChatLogByThreadByCsAndBuyer(shop, csNick, receiveBuyerLst, targetDateStartDate, targetDateEndDate);
            //List<ChatlogDTO> csChatlogLst = csChatlogDao.selectBuyerChatlogByCSNickAndDate(shop.getShopId(),shop.getSchemaId(), csNick,targetDateStartDate, targetDateEndDate,receiveBuyerLst);
            long e3 = System.currentTimeMillis();
            //logger.info("csNIck:{} selectChatLogByThreadByCsAndBuyer time:{} ms",csNick,e3-s3);
//            if (CollectionUtils.isEmpty(csChatlogLst)) {
//                continue;
//            }
            //客服推荐/咨询的所有的skuId
//            类型 1:客服推荐
            List<CsCustRecommendConsultSkuDO> csRecommendLst = csCustList.stream().filter(ele -> ele.getType() == 1).collect(Collectors.toList());
//            2：顾客咨询
            List<CsCustRecommendConsultSkuDO> buyerConsultLst = csCustList.stream().filter(ele -> ele.getType() == 2).collect(Collectors.toList());
//  客服推荐skuId
            Set<Long> csRecommendSkuIdAllSet = csRecommendLst.stream().map(CsCustRecommendConsultSkuDO::getSkuId).collect(Collectors.toSet());
//            顾客咨询skuId
            Set<Long> buyerConsultSkuIdAllSet = buyerConsultLst.stream().map(CsCustRecommendConsultSkuDO::getSkuId).collect(Collectors.toSet());

            List<CsRecommendGoodsDO> csNickRecommendGoodsLst = Lists.newArrayList();
            List<CustConsultGoodsDO> buyeCustConsultGoodsLst = Lists.newArrayList();
            CsRecommendGoodsDO csRecommendGoods;
            CustConsultGoodsDO custConsultGoods;

//			long s4=System.currentTimeMillis();
            for (CsCustRecommendConsultSkuDO csCustRecommendConsultSkuDO : csRecommendLst) {
                csRecommendGoods = new CsRecommendGoodsDO();
                csRecommendGoods.setCsNick(csNick);
                csRecommendGoods.setCustomer(csCustRecommendConsultSkuDO.getBuyerNick());
                csRecommendGoods.setSkuId(csCustRecommendConsultSkuDO.getSkuId());
                csRecommendGoods.setDate(date);
                csRecommendGoods.setResult(GoodsPurchaseEnum.NODEAL.getType());
                csRecommendGoods.setShopId(shop.getShopId());
                csNickRecommendGoodsLst.add(csRecommendGoods);
            }

            for (CsCustRecommendConsultSkuDO csCustRecommendConsultSkuDO : buyerConsultLst) {
                custConsultGoods = new CustConsultGoodsDO();
                custConsultGoods.setCsNick(csNick);
                custConsultGoods.setCustomer(csCustRecommendConsultSkuDO.getBuyerNick());
                custConsultGoods.setSkuId(csCustRecommendConsultSkuDO.getSkuId());
                custConsultGoods.setDate(date);
                custConsultGoods.setResult(GoodsPurchaseEnum.NODEAL.getType());
                custConsultGoods.setShopId(shop.getShopId());
                buyeCustConsultGoodsLst.add(custConsultGoods);
            }
//			long e4=System.currentTimeMillis();
//			logger.info("xunhuan hand skuId time ：{}ms",e4-s4);
            // --------------------------当天落实客服维度---------------------------------------
            //	long s5=System.currentTimeMillis();
            List<CsPurchaseGoodsResultDTO> csRecommendResultGoodsLst = bingDingCsNick(shop, csNick, csRecommendSkuIdAllSet, targetDateStartDate, targetDateEndDate);
            if (CollectionUtils.isNotEmpty(csRecommendResultGoodsLst)) {
                Map<String, List<CsPurchaseGoodsResultDTO>> csSkuIdBindMap = csRecommendResultGoodsLst.stream().collect(Collectors.groupingBy(CsPurchaseGoodsResultDTO::getBuyerNick));
                for (CsRecommendGoodsDO reCommend : csNickRecommendGoodsLst) {
                    List<CsPurchaseGoodsResultDTO> csSkuIdBindLst = csSkuIdBindMap.get(reCommend.getCustomer());
                    if (CollectionUtils.isEmpty(csSkuIdBindLst)) {
                        continue;
                    }
                    for (CsPurchaseGoodsResultDTO skuOrder : csSkuIdBindLst) {
                        if (Objects.equals(skuOrder.getSkuId(), reCommend.getSkuId())) {
                            reCommend.setOrderId(skuOrder.getOrderId());
                            if (skuOrder.getPayType() == 1) {
                                reCommend.setResult(GoodsPurchaseEnum.DEAL.getType());
                            }
                        }
                    }
                }
            }
            long e5 = System.currentTimeMillis();
            //logger.info("recommend bingDingCsNick time:{}ms",e5-s5);
            allCsNickRecommendGoodsLst.addAll(csNickRecommendGoodsLst);
            // --------------------------咨询当天落实客服维度---------------------------------------
            long s6 = System.currentTimeMillis();
            List<CsPurchaseGoodsResultDTO> csCoultResultGoodsLst = bingDingCsNick(shop, csNick, buyerConsultSkuIdAllSet, targetDateStartDate, targetDateEndDate);
            if (CollectionUtils.isNotEmpty(csCoultResultGoodsLst)) {
                Map<String, List<CsPurchaseGoodsResultDTO>> consultSkuIdBindMap = csCoultResultGoodsLst.stream().collect(Collectors.groupingBy(CsPurchaseGoodsResultDTO::getBuyerNick));
                for (CustConsultGoodsDO custCon : buyeCustConsultGoodsLst) {

                    List<CsPurchaseGoodsResultDTO> csSkuIdBindLst = consultSkuIdBindMap.get(custCon.getCustomer());
                    if (CollectionUtils.isEmpty(csSkuIdBindLst)) {
                        continue;
                    }
                    for (CsPurchaseGoodsResultDTO consultGoods : csSkuIdBindLst) {
                        if (Objects.equals(custCon.getSkuId(), consultGoods.getSkuId())) {
                            custCon.setOrderId(consultGoods.getOrderId());
                            if (consultGoods.getPayType() == 1) {
                                custCon.setResult(GoodsPurchaseEnum.DEAL.getType());
                            }
                        }
                    }
                }
            }
            allBuyecustConsultGoodsLst.addAll(buyeCustConsultGoodsLst);
            long e6 = System.currentTimeMillis();
            //logger.info("consult bingDingCsNick time:{}ms",e6-s6);
        }
		long e7=System.currentTimeMillis();
		//logger.info("xun huan hand time:{}ms",e7-s7);
		// 插入今天的咨询或推荐的数据
		recommend = csRecommendGoodsDao.batchInsertCsRecommendGoods(shop, date, allCsNickRecommendGoodsLst);
		
		
		consult = custConsultGoodsDao.batchInsertCustConsultGoods(shop, date, allBuyecustConsultGoodsLst);
	
		List<OrderDetailDTO> payOrderLst = orderDetailDao.selectOrderPaidDetailByPayTime(shop, targetDateStartDate,targetDateEndDate);
		
		if(CollectionUtils.isEmpty(payOrderLst)){
			return ;
		}
		Map<Long, List<OrderDetailDTO>> paidOrderMap = payOrderLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
	
		//获取前一天和当天落实绑定的客服推荐的商品
		Date yesterDaySDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(targetDateStartDate, 0 - PesConstants.GOODS_PAY));
		List<CsPurchaseGoodsResultDTO> csRecommdLst = csRecommendGoodsDao.selectCsRecommentByshopIdByDate(shop,yesterDaySDate, targetDateEndDate);
		List<CsPurchaseGoodsResultDTO> dealCsRecommdLst=Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(csRecommdLst)) {
			for (CsPurchaseGoodsResultDTO csRecomm : csRecommdLst) {
				if (csRecomm.getOrderId() == null) {
					continue;
				}
				List<OrderDetailDTO> orderDetailLst = paidOrderMap.get(csRecomm.getOrderId());
				if (CollectionUtils.isEmpty(orderDetailLst)) {
					continue;
				}
				//Map<String, OrderDetailDTO> skuMap=orderDetailLst.stream().collect(Collectors.toMap(OrderDetailDTO::getItemSkuId, c->c));
				Map<String, OrderDetailDTO> skuMap = orderDetailLst.stream().collect(Collectors.toMap(OrderDetailDTO::getItemSkuId, c->c, (sku1, sku2) -> sku1));
				if(skuMap.get(String.valueOf(csRecomm.getSkuId()))!=null){
					csRecomm.setResult(GoodsPurchaseEnum.DEAL.getType());
				}
				dealCsRecommdLst.add(csRecomm);
			}
		}
		csRecommendGoodsDao.batchUpdateCsRecommendGoods(shop, date, dealCsRecommdLst);
		
		List<CsPurchaseGoodsResultDTO> custConsultLst = custConsultGoodsDao.selectCsConsultByshopIdByDate(shop,yesterDaySDate, targetDateEndDate);
		List<CsPurchaseGoodsResultDTO> dealCustConsult=Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(custConsultLst)) {
			for (CsPurchaseGoodsResultDTO custCon : custConsultLst) {
				//剔除没有绑定的和货到付款(下单就算成功)
				if(custCon.getOrderId()==null){
					continue;
				}
				List<OrderDetailDTO> orderDetailLst=paidOrderMap.get(custCon.getOrderId());
				if(CollectionUtils.isEmpty(orderDetailLst)){
					continue;
				}
				Map<String, OrderDetailDTO> skuMap=orderDetailLst.stream().collect(Collectors.toMap(OrderDetailDTO::getItemSkuId, c->c, (sku1, sku2) -> sku1));
				if(skuMap.get(String.valueOf(custCon.getSkuId()))!=null){
					custCon.setResult(GoodsPurchaseEnum.DEAL.getType());
				}
				dealCustConsult.add(custCon);
			}
		}
		custConsultGoodsDao.batchUpdateCustConsultGoods(shop, date, dealCustConsult);
		if(logger.isDebugEnabled()){

			logger.debug("insert custConsultGoods num:{}*****insert csRecommendGoods num:{} ",consult,recommend);
		}
	}

	//是否是绑定的客服
	private List<CsPurchaseGoodsResultDTO> bingDingCsNick(JobShopDTO shop, String csNick, Set<Long> skuIdSet,
			Date startDate, Date endDate) {
		List<CsPurchaseGoodsResultDTO> luishiCsLst = Lists.newArrayList();
		if (CollectionUtils.isEmpty(skuIdSet)) {
			return luishiCsLst;
		}
		long s1=System.currentTimeMillis();
		List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderCreatedDetailByCreated(shop, skuIdSet,startDate, endDate);
		long e1=System.currentTimeMillis();
	//	logger.info("selectOrderCreatedDetailByCreated time:{}ms",e1-s1);
		if (CollectionUtils.isEmpty(orderDetailLst)) {
			return luishiCsLst;
		}
		Set<Long> orderIdSet = orderDetailLst.stream().map(OrderDetailDTO::getOrderId).collect(Collectors.toSet());
		//需要区分哪些是货到付款的数据
		long s2=System.currentTimeMillis();
		Set<Long> cashOrderIdSet=	orderDao.selectShopCashOrderByShopIdByOrderIdByCreated(shop, startDate, endDate, 1, orderIdSet);
		long e2=System.currentTimeMillis();
		//logger.info("selectShopCashOrderByShopIdByOrderIdByCreated time:{}ms",e2-s2);
		for (OrderDetailDTO order : orderDetailLst) {
			if(cashOrderIdSet.contains(order.getOrderId())){
				order.setPayType(1);
				
			}else{
				order.setPayType(4);
			}
		}
		long s3=System.currentTimeMillis();
		// 获取当天下单的指标
		List<GoodsAnalysisOrderIndexDTO> orderIndexLst = csOrderIndexDao.selectShopCsOrderIndexLstByCsNickAndOrderIdLstAndDate(shop, startDate, endDate, orderIdSet, csNick);
		long e3=System.currentTimeMillis();
		//logger.info("selectShopCsOrderIndexLstByCsNickAndOrderIdLstAndDate time:{}ms",e3-s3);
		if (CollectionUtils.isEmpty(orderIndexLst)) {
			return luishiCsLst;
		}
		Map<Long, List<GoodsAnalysisOrderIndexDTO>> todayOrderIndexMap=orderIndexLst.stream().collect(Collectors.groupingBy(GoodsAnalysisOrderIndexDTO::getOrderId));
		// 根据客服是否在最后聊天时间在下单前
		CsPurchaseGoodsResultDTO result = null;
		for (OrderDetailDTO order : orderDetailLst) {
			List<GoodsAnalysisOrderIndexDTO> csOrderIndexLst=todayOrderIndexMap.get(order.getOrderId());
			if(CollectionUtils.isNotEmpty(csOrderIndexLst)){
				for (GoodsAnalysisOrderIndexDTO goodsOrderIndex : csOrderIndexLst) {
					if(goodsOrderIndex.getBcLastReplyDate()==null){
						continue;
					}
					if(goodsOrderIndex.getBcLastReplyDate().before(order.getCreated()) || goodsOrderIndex.getBcLastReplyDate().getTime() == order.getCreated().getTime()){
						result = new CsPurchaseGoodsResultDTO(csNick, shop.getShopId());
						result.setDate(goodsOrderIndex.getDate());// 询单日期
						result.setBuyerNick(goodsOrderIndex.getBuyerNick());
						result.setSkuId(Long.valueOf(order.getItemSkuId()));
						result.setOrderId(goodsOrderIndex.getOrderId());
						if(order.getPayType()==1){
							result.setPayType(1);
						}else{
							result.setPayType(4);
						}
						luishiCsLst.add(result);
					}
				}
			}
		}
		return luishiCsLst;
	}
		
	private List<ChatlogDTO> selectChatLogByThreadByCsAndBuyer(JobShopDTO shop,String csNick,List<String> receiveBuyerLst,Date startDate,Date endDate) {
		List<ChatlogDTO> csChatLogLst=Lists.newArrayList();
		long s1=System.currentTimeMillis();
		if(CollectionUtils.isEmpty(receiveBuyerLst)){
			return csChatLogLst;
		}
		List<List<String>> splitBuerLst=CollectionUtil.avgAssignLst(receiveBuyerLst, CommonConstants.SPLIT_LIMIT_NUM);
		int groupNum=splitBuerLst.size();
		logger.info("thread groupNum :{}",groupNum);
		final int poolSize = groupNum > 200 ? 200 : groupNum;
		ExecutorService executorService = new ThreadPoolExecutor(poolSize, poolSize, 0L,
				TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(groupNum));

		CompletionService<Map<String, Object>> completionService = new ExecutorCompletionService<Map<String, Object>>(executorService);
		int t=0;
		
		for (List<String> receiveLst : splitBuerLst) {
			long s3=System.currentTimeMillis();
			ChatLogThreadBO cbi=new ChatLogThreadBO(shop.getShopId(), shop.getSchemaId(), csNick, receiveLst, startDate, endDate);
			completionService.submit(cbi);
			long e3=System.currentTimeMillis();
			//logger.info("groupNum:{} submit receiveLst size:{},time:{}ms",groupNum,receiveLst.size(),e3-s3);
		}

		try {
			for (int i = 0; i < groupNum; i++) {
				long s3=System.currentTimeMillis();
				Map<String, Object> result = completionService.take().get();
				if(result!=null){
					@SuppressWarnings("unchecked")
					List<ChatlogDTO> chatLogLst = (List<ChatlogDTO>) result.get("chatLogs");
					if(CollectionUtils.isNotEmpty(chatLogLst)){
						csChatLogLst.addAll(chatLogLst);
						long e3=System.currentTimeMillis();
					//	logger.info("get chatlog thread return time:{}ms",e3-s3);
					}
					
				}
			}
		} catch (Exception e) {
			executorService.shutdownNow();
			logger.error("start chatpeer Tasks error", e);
		} finally {
			executorService.shutdown();
		}
		long e1=System.currentTimeMillis();
		logger.info("csNIck:{} get chatlogs num:{}  thread end time:{}ms ",csNick,csChatLogLst.size(),e1-s1);
		return csChatLogLst;
	}
//	public static void main(String[] args) {
//		String rex = "(?<=//item.jd.com/).*?(?=.html)|(?<=//item.m.jd.com/product/).*?(?=.html)";
//		//String rex = "(?<=http://item.jd.com/)|(?<=http://item.m.jd.com/product/).*?(?=.html)";
//		//String rex="(?<=/).*?(?=@)";
//
//		String str="http://item.jd.com/1505283937.html--ttttttt---https://item.jd.com/1238701404.html";
//		List<String> skuIdLst=Lists.newArrayList();
//		Pattern pattern = Pattern.compile(rex);
//		Matcher match = pattern.matcher(str);
//		while(match.find()){
//			System.out.println(match.group());
//			skuIdLst.add(match.group());
//		}
////			System.out.println("string："+string);
////			int lastIndex=string.lastIndexOf("/")+1;
////			int htmlIndex=string.lastIndexOf(".");
////			System.out.println(string.substring(lastIndex, htmlIndex));
//
//		System.out.println(skuIdLst);
//
//	}
}
