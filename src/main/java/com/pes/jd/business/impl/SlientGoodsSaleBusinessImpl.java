package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.SlientGoodsSaleBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class SlientGoodsSaleBusinessImpl implements SlientGoodsSaleBusiness {

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;

	@Override
	public ApiResponse selectGoodsSaleIndexList(ShopQuery shop, GoodsConsultParam param) throws DBNotExistException {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/slientGoodsSale/selectGoodsSaleIndex", body);
	}

	@Override
	public ApiResponse selectGoodsSaleIndexDetailList(ShopQuery shop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("param", param)
				.put("sortPageQuery", sortPageQuery)
				.put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/slientGoodsSale/selectGoodsSaleIndexDetail", body);
	}

    @Override
    public ApiResponse selectGoodsSaleIndexListOfSpu(ShopQuery shop, GoodsConsultParam param){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
                .put("paramStr", param).toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/slientGoodsSale/selectGoodsSaleIndexOfSpu", body);
    }

    @Override
    public ApiResponse selectGoodsSaleIndexDetailListOfSpu(ShopQuery shop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
                .put("param", param)
                .put("sortPageQuery", sortPageQuery)
                .put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/slientGoodsSale/selectGoodsSaleIndexDetailOfSpu", body);
    }

}
