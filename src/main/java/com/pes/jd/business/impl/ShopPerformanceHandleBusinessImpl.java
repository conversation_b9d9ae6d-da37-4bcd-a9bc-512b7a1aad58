package com.pes.jd.business.impl;

import com.pes.jd.business.ShopOverviewBusiness;
import com.pes.jd.business.ShopPerformanceHandleBusiness;
import com.pes.jd.model.DO.ShopDayOverviewDO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 店铺绩效处理 - 业务类
 * <AUTHOR>
 *
 */
@Service
public class ShopPerformanceHandleBusinessImpl implements ShopPerformanceHandleBusiness {

	private final Logger logger = LoggerFactory.getLogger(ShopPerformanceHandleBusinessImpl.class);
	
	@Resource
	private ShopOverviewBusiness shopOverviewBusiness;
	
	@Override
	public void handleShopDayOverview(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
	
		long s = System.currentTimeMillis();
		
		List<Date> dates = jobDate.getDates();
		if (CollectionUtils.isEmpty(dates)) {
//			logger.error("req dates is empty");
			return;
		}

		for (Date date : dates) {
			ShopDayOverviewDO dayOverview = shopOverviewBusiness.getShopDayOverview(jobShop, date);
			shopOverviewBusiness.handleInsertShopDayOverview(jobShop, date, dayOverview, isDelData);
		}

		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("handle shop DayOverview end,time:{}", (e - s) / 1000);
		}
	}
}
