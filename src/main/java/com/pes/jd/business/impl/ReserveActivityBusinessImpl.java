package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ReserveActivityBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.MServiceQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;


/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/3/18
 * @Modified By:
 */
@Service
public class ReserveActivityBusinessImpl implements ReserveActivityBusiness {

    @Autowired
    private PopSubRestTemplate popSubRestTemplate;

    @Override
    public ApiResponse selectReserveActivityByShop(ShopQuery shop, String startDate, String endDate, String sku, String activityId, String type, String conditionType, String status) throws DBNotExistException {
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getSchemaId(),shop.getDbName());
        MServiceQuery ms=new MServiceQuery();
        ms.setDb(shop.getSchemaId());
        ms.setNode(shop.getDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("startDate",startDate)
                .put("endDate",endDate)
                .put("type",type)
                .put("activityId",activityId)
                .put("conditionType",conditionType)
                .put("sku", sku)
                .put("status", status)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/shop/performance/selectReserveActivityByShop", body);

    }
}
