package com.pes.jd.business.impl;

import com.pes.jd.business.ReserveActivityBussiness;
import com.pes.jd.business.ShopCategoryAndGoodsBussiness;
import com.pes.jd.data.converter.ReserveActivityDataConverter;
import com.pes.jd.data.converter.ShopCategoryAndGoodAndSkuDataConverter;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ReserveActivityBusinessImpl implements ReserveActivityBussiness {

	private static final Logger logger = LoggerFactory.getLogger(ReserveActivityBusinessImpl.class);
	
	@Resource
	private ReserveActivityDataConverter reserveActivityDataConverter;

	@Override
	public void pullReserveActivity(JobShopQuery jobShop, boolean isDelData) throws Exception {
		long s = System.currentTimeMillis();
		try {
			reserveActivityDataConverter.pullReserveActivity(jobShop);
		} catch (Exception e) {
			logger.error("【{}】batch pullReserveActivity error", jobShop.getShop().getTitle(), e);
			throw e;
		}
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("pull pullReserveActivity end, time  ： {} s", (e - s)/1000);
		}
	}
}
