package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.SgDiagnosticAnalysisBusiness;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: SgDiagnosticAnalysisBusiness
 * @Author: aiJun
 * @Description: TODO
 * @date: 2020-02-12  10:48
 */
@Service
public class SgDiagnosticAnalysisBusinessImpl implements SgDiagnosticAnalysisBusiness {
    private static Logger logger = LoggerFactory.getLogger(SgDiagnosticAnalysisBusinessImpl.class);

    @Resource
    private UsermgrRestTemplate usermgrRestTemplate;

    @Override
    public Object synSetting(String shopIdStr, String startDate, String endDate) {
        HttpEntity<Object> param = RequestEntityBuilder.builder()
                .put("shopIdStr", shopIdStr)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

        RestResponseTypeRef<Object> restResponse = usermgrRestTemplate.postRest(serviceId,"/sgSyn/batch_remind/selectRemindSettingHistory", param,
                new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {
                });
        if (restResponse.getSuccess()) {
            logger.info("系统设置获取成功 date=[{}]", restResponse.getData());
        } else {
            logger.error("系统设置获取失败 shopId=[{}] startDate=[{}] endDate=[{}] restResponse={}", shopIdStr, startDate, endDate, restResponse);
            throw new RuntimeException("系统设置获取失败");
        }
        return restResponse.getData();
    }

    @Override
    public Object synWord(String shopIdStr, String startDate, String endDate) {
        HttpEntity<Object> param = RequestEntityBuilder.builder()
                .put("shopIdStr", shopIdStr)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

        RestResponseTypeRef<Object> restResponse = usermgrRestTemplate.postRest(serviceId,"/sgSyn/batch_remind/selectRemindWordHistory", param,
                new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {
                });
        if (restResponse.getSuccess()) {
            logger.info("系统话术获取成功 date=[{}]", restResponse.getData());
        } else {
            logger.error("系统话术获取失败 shopId=[{}] startDate=[{}] endDate=[{}] restResponse={}", shopIdStr, startDate, endDate, restResponse);
            throw new RuntimeException("系统话术获取失败");
        }
        return restResponse;
    }
    @Override
    public Object synSetting2(String shopIdStr, String startDate, String endDate) {
        HttpEntity<Object> param = RequestEntityBuilder.builder()
                .put("shopIdStr", shopIdStr)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

        RestResponseTypeRef<Object> restResponse = usermgrRestTemplate.postRest(serviceId,"/sgSyn/batch_remind/selectRemindSettingHistory2", param,
                new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {
                });
        if (restResponse.getSuccess()) {
            logger.info("系统设置获取成功 date=[{}]", restResponse.getData());
        } else {
            logger.error("系统设置获取失败 shopId=[{}] startDate=[{}] endDate=[{}] restResponse={}", shopIdStr, startDate, endDate, restResponse);
            throw new RuntimeException("系统设置获取失败");
        }
        return restResponse.getData();
    }

    @Override
    public Object synWord2(String shopIdStr, String startDate, String endDate) {
        HttpEntity<Object> param = RequestEntityBuilder.builder()
                .put("shopIdStr", shopIdStr)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

        RestResponseTypeRef<Object> restResponse = usermgrRestTemplate.postRest(serviceId,"/sgSyn/batch_remind/selectRemindWordHistory2", param,
                new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {
                });
        if (restResponse.getSuccess()) {
            logger.info("系统话术获取成功 date=[{}]", restResponse.getData());
        } else {
            logger.error("系统话术获取失败 shopId=[{}] startDate=[{}] endDate=[{}] restResponse={}", shopIdStr, startDate, endDate, restResponse);
            throw new RuntimeException("系统话术获取失败");
        }
        return restResponse;
    }
}
