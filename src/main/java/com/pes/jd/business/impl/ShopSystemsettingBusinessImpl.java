/**  
 * Project Name:jd-pes  
 * File Name:SysSettingBusinessImpl.java  
 * Package Name:com.pes.jd.business.impl  
 * Date:2018年10月22日下午1:42:34  
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.pes.jd.business.impl;

import com.pes.jd.business.ShopSystemsettingBusiness;
import com.pes.jd.dao.ShopSystemsettingDao;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**  
 * 店铺系统设置 - 业务类
 * (咚咚设置 咚咚组设置 绩效设置 权限设置 店铺信息)
 * ClassName:SysSettingBusinessImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月22日 下午1:42:34 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class ShopSystemsettingBusinessImpl implements ShopSystemsettingBusiness {

	@Resource
	private ShopSystemsettingDao shopSystemsettingDao;
	
	@Override
	public ShopSystemsettingDTO getShopSystemsetting(Long shopId) {
		return shopSystemsettingDao.getShopSystemsetting(shopId);
	}
	

}
  
