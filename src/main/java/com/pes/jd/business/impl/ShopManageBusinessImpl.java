package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.*;
import com.pes.jd.data.api.VenderShopOperator;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.reponse.ApiResponse;
import com.pes.jd.mq.kafka.producer.ApiCalPushUtil;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 店铺维度 - 业务类
 * <AUTHOR>
 *
 */
@Service
public class ShopManageBusinessImpl implements ShopManageBusiness {
	private static Logger  logger = LoggerFactory.getLogger(ShopBusinessImpl.class);
	@Resource
	private ShopBusiness shopBusiness;
	@Resource
	private ShopSystemsettingBusiness shopSystemsettingBusiness;
	@Resource
	private CsManageBusiness csManageBusiness;
	@Resource
	private ShopAccountBussiness shopAccountBussiness;
	@Resource
	private UsermgrRestTemplate usermgrRestTemplate;
	@Resource
	private VenderShopOperator venderShopOperator;
	@Override
	public JobShopQuery getJobShop(Long shopId, Integer csType) {

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId", shopId+"")
				.toRequestEntity();
		ApiResponse apiResponse;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId,"/shop/getJobShop", body);
		} catch (Exception e) {
			logger.error("getJobShop error:{}",e.getMessage(),e);
			throw e;
		}

		if(!apiResponse.getSuccess()){
			logger.info("apiResponse={}",apiResponse);
			return null;
		}

		Map<String, Object> data = apiResponse.getData();
		JobShopDTO jobShopDTO = JSON.parseObject(JSON.toJSONString(data.get("shop")), JobShopDTO.class);
		ShopSystemsettingDTO shopSystemsetting = JSON.parseObject(JSON.toJSONString(data.get("shopSystemsetting")), ShopSystemsettingDTO.class);
		List<CsDTO> csLst = JSON.parseArray(JSON.toJSONString(data.get("csLst")), CsDTO.class);
		List<ShopAccountDTO> shopSubUserLst = JSON.parseArray(JSON.toJSONString(data.get("shopSubUserLst")), ShopAccountDTO.class);
        List<String> buyerFilterLst = JSON.parseArray(JSON.toJSONString(data.get("buyerFilterLst")), String.class);
        List<GoodsFilterDTO> goodFilterLst = JSON.parseArray(JSON.toJSONString(data.get("goodFilterLst")), GoodsFilterDTO.class);

		//---------获取敏感词----------------//
		List<String> sysWordLst = JSON.parseArray(JSON.toJSONString(data.get("sysWordLst")), String.class);

		try {
				venderShopOperator.checkShopExpireBySessionKey(jobShopDTO.getSessionKey());
		} catch (GainShopDataFailException e) {
			if("19".equals(e.getErrorCode())) {
				if (StringUtils.isNotBlank(jobShopDTO.getOptionSessionKey())) {
					jobShopDTO.setSessionKey(jobShopDTO.getOptionSessionKey());
					logger.info("shopName {} main SessionKey is expire", jobShopDTO.getTitle());
				} else {
					logger.info("shopName {} sub SessionKey is empty no update sessionKey", jobShopDTO.getTitle());
				}
			}
		}catch (Exception e) {
			logger.error("shopName:{} checkShopExpireBySessionKey error:{}",jobShopDTO.getTitle(),e.getMessage(),e);
		}
		JobShopQuery jobShop = new JobShopQuery(jobShopDTO);
		jobShop.setCsLst(csLst);
		jobShop.setShopSystemsetting(shopSystemsetting);
		jobShop.setShopSubUserLst(shopSubUserLst);
		jobShop.setBuyerFilterLst(buyerFilterLst);
		jobShop.setGoodFilterLst(goodFilterLst);

		jobShop.setSysWordLst(sysWordLst);
		//cache, using by kafka push
//		ApiCalPushUtil.addShop(jobShop.getShop().getSessionKey(), jobShop.getShop());
//		ApiCalPushUtil.addShop(jobShop.getShop().getOptionSessionKey(), jobShop.getShop());
		return jobShop;
	}

	@Override
	public JobShopDTO getJobShopInfoByVenderId(Long venderId) {
		return shopBusiness.getJobShopInfoByVenderId(venderId);
	}

	/**  
	 * getAllActiveJobShop:(根据有效天延迟时间获取活跃店铺集合). <br/>  
	 *
	 * @param hours		有效天延迟时间（单位：小时）
	 * @param csType	客服类型：1售前，2售后
	 * @return
	 * @since JDK 1.8  
	 */
	@Override
	public List<JobShopQuery> getAllActiveJobShop(Integer hours, Integer csType) {
		System.out.println("hours:"+hours+",csType"+csType);
		List<JobShopDTO> shopLst;
		if(hours == null){
			//拉取所有有效店铺值班记录数据
			shopLst = shopBusiness.getAllActiveJobShopInfo();
			System.out.println("拉取所有有效店铺值班记录数据");
		} else {
			//根据有效天延迟时间获取活跃店铺集合
			shopLst = shopBusiness.getActiveJobShopInfoByDelayTime(hours);
			System.out.println("拉取有效天延迟时间获取活跃店铺集合");
		}
		System.out.println("collection-of-all-shops-to-be-pulled"+shopLst);
		List<JobShopQuery> jobShopLst = Lists.newArrayList();
		JobShopQuery jobShop;
		ShopSystemsettingDTO shopSystemsetting;
		List<CsDTO> csLst;
		List<ShopAccountDTO> shopSubUserLst;
		for (JobShopDTO shop : shopLst) {
			Long shopId = shop.getShopId();
			jobShop = new JobShopQuery(shop);
			shopSystemsetting = shopSystemsettingBusiness.getShopSystemsetting(shopId);
			jobShop.setShopSystemsetting(shopSystemsetting);
			
			csLst = csManageBusiness.searchCsLstByShopIdAndType(shopId, csType);
			jobShop.setCsLst(csLst);
			
			shopSubUserLst = shopAccountBussiness.selectShopAccountByShopId(shopId);
			jobShop.setShopSubUserLst(shopSubUserLst);
			jobShopLst.add(jobShop);
		}
		return jobShopLst;
	}


	/**
	 * 只允许本地开发使用
	 * @param shopId
	 * @param csType
	 * @return
	 */
	@Override
	public JobShopQuery getLocalJobShop(Long shopId, Integer csType) {

		JobShopDTO shop = shopBusiness.getJobShopInfoById(shopId);

		JobShopQuery jobShop = new JobShopQuery(shop);
		ShopSystemsettingDTO shopSystemsetting = shopSystemsettingBusiness.getShopSystemsetting(shopId);
		jobShop.setShopSystemsetting(shopSystemsetting);

		List<CsDTO> csLst = csManageBusiness.searchCsLstByShopIdAndType(shopId, csType);
		jobShop.setCsLst(csLst);

		List<ShopAccountDTO> shopSubUserLst = shopAccountBussiness.selectShopAccountByShopId(shopId);
		jobShop.setShopSubUserLst(shopSubUserLst);
		return jobShop;
	}

    /**
     * 查询店铺基本信息，走缓存
     * @param shopId
     * @return
     */
	@Override
	public JobShopDTO getShopSplitByShopId(Long shopId) {

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId",shopId)
				.toRequestEntity();
		ApiResponse apiResponse;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId,"/shop/getShopSplitByShopId", body);
		} catch (Exception e) {
			logger.error("getShopSplitByShopId error:{}", e.getMessage(), e);
			throw e;
		}

		if (!apiResponse.getSuccess()) {
			return null;
		}
		Map<String, Object> data = apiResponse.getData();
		return JSON.parseObject(JSON.toJSONString(data.get("shop")), JobShopDTO.class);
	}

	@Override
	public List<JobShopDTO> getActiveShopLst(){
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.toRequestEntity();
		ApiResponse apiResponse;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId,"/shop/selectActiveShopLst", body);
		} catch (Exception e) {
			logger.error("selectActiveShopLst error:{}", e.getMessage(), e);
			throw e;
		}

		if (!apiResponse.getSuccess()) {
			return null;
		}
		Map<String, Object> data = apiResponse.getData();
		Object activeShopLst = data.get("activeShopLst");
		List<JobShopDTO> shopJobList = new ArrayList<>();
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		try {
			if (activeShopLst != null) {
				String jsonString = objectMapper.writeValueAsString(activeShopLst);
				shopJobList = objectMapper.readValue(jsonString,
						new TypeReference<List<JobShopDTO>>(){});
			}
		} catch (Exception e) {
			logger.error("selectActiveShopLst error:{}", e.getMessage(), e);
		}

		return shopJobList;
	}

	@Override
	public ShopSplitKeyDTO getShopSplitKeyInfo(Long aLong) {
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shopId",aLong)
				.toRequestEntity();
		ApiResponse apiResponse;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			apiResponse = usermgrRestTemplate.postRest(serviceId,"/shop/getShopSplitKeyInfo", body);
		} catch (Exception e) {
			logger.error("getShopSplitKeyInfo error:{}", e.getMessage(), e);
			throw e;
		}
		if (!apiResponse.getSuccess()) {
			return null;
		}
		Map<String, Object> data = apiResponse.getData();
		Object activeShopLst = data.get("shopSplitKeyDTO");
		ShopSplitKeyDTO shopSplitKeyDTO = new ShopSplitKeyDTO();
		ObjectMapper objectMapper = new ObjectMapper();
		try {
			if (activeShopLst != null) {
				String jsonString = objectMapper.writeValueAsString(activeShopLst);
				shopSplitKeyDTO = objectMapper.readValue(jsonString,ShopSplitKeyDTO.class );
			}
		} catch (Exception e) {
			logger.error("selectActiveShopLst error:{}", e.getMessage(), e);
		}

		return shopSplitKeyDTO;
	}
}
