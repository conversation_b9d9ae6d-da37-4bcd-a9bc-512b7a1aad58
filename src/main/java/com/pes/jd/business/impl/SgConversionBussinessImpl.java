package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.pes.jd.business.SgConversionBussiness;
import com.pes.jd.business.SgConversionRestBussiness;
import com.pes.jd.model.DTO.ShopOrderSaleAmountDTO;
import com.pes.jd.model.DTO.ShopSgDetailDTO;
import com.pes.jd.model.DTO.ShopUseConverDTO;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年02月25 17:03:03<br>
 */
@Service
public class SgConversionBussinessImpl implements SgConversionBussiness {
    @Resource
    private SgConversionRestBussiness sgConversionRestBussiness;


    @Override
    public List<ShopSgDetailDTO> selectSgUrgeInfoLst(String shopIds, String startDate, String endDate, Integer orderType,Boolean isInsert) throws Exception {
        List<ShopSgDetailDTO> resultLst= Lists.newArrayList();
        List<Long> shopIdLst = Arrays.stream(shopIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<ShopUrge> shopLst=  sgConversionRestBussiness.selectUrgeShopByShopIdLstByType(shopIdLst,0);

        Map<String, List<ShopUrge>> shopMap = shopLst.stream().collect(Collectors.groupingBy(ShopUrge::getRtDb));
        for (Map.Entry<String, List<ShopUrge>> entry : shopMap.entrySet()) {
            List<ShopUrge> urgeShopLst = entry.getValue();
            if (CollectionUtils.isEmpty(urgeShopLst)) {
                continue;
            }
            List<ShopSgDetailDTO> sgLst=  sgConversionRestBussiness.selectSgUrgeInfoLst(entry.getKey(),urgeShopLst,startDate,endDate,orderType,isInsert);
            if(CollectionUtils.isNotEmpty(sgLst)){
                resultLst.addAll(sgLst);
            }
        }
        return resultLst;
    }


    @Override
    public List<ShopSgDetailDTO> selectSgUrgeInfoLstNew(String shopIds, String startDate, String endDate, Integer orderType,Boolean isInsert) throws Exception {
        List<ShopSgDetailDTO> resultLst= Lists.newArrayList();
        List<Long> shopIdLst = Arrays.stream(shopIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<ShopUrge> shopLst=  sgConversionRestBussiness.selectUrgeShopByShopIdLstByType(shopIdLst,0);

        Map<String, List<ShopUrge>> shopMap = shopLst.stream().collect(Collectors.groupingBy(ShopUrge::getRtDb));
        for (Map.Entry<String, List<ShopUrge>> entry : shopMap.entrySet()) {
            List<ShopUrge> urgeShopLst = entry.getValue();
            if (CollectionUtils.isEmpty(urgeShopLst)) {
                continue;
            }
            List<ShopSgDetailDTO> sgLst=  sgConversionRestBussiness.selectSgUrgeInfoLstNew(entry.getKey(),urgeShopLst,startDate,endDate,orderType,isInsert);
            if(CollectionUtils.isNotEmpty(sgLst)){
                resultLst.addAll(sgLst);
            }
        }
        return resultLst;
    }

    @Override
    public List<ShopUseConverDTO> selectSgShopUseConvertLst(String shopIds, String startDate, String endDate) throws Exception {
        List<ShopUseConverDTO> resultLst= Lists.newArrayList();
        List<Long> shopIdLst = Arrays.stream(shopIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<ShopUrge> shopLst=  sgConversionRestBussiness.selectUrgeShopByShopIdLstByType(shopIdLst,0);

        Map<String, List<ShopUrge>> shopMap = shopLst.stream().collect(Collectors.groupingBy(ShopUrge::getRtDb));
        for (Map.Entry<String, List<ShopUrge>> entry : shopMap.entrySet()) {
            List<ShopUrge> urgeShopLst = entry.getValue();
            if (CollectionUtils.isEmpty(urgeShopLst)) {
                continue;
            }
            List<ShopUseConverDTO> sgLst=  sgConversionRestBussiness.selectSgShopUseConvertLst(entry.getKey(),urgeShopLst,startDate,endDate);
            if(CollectionUtils.isNotEmpty(sgLst)){
                resultLst.addAll(sgLst);
            }
        }
        return resultLst;
    }


    @Override
    public List<ShopOrderSaleAmountDTO> selecShopOrderSaleAmountLst(String shopIds, String startDate, String endDate) throws Exception {
        List<ShopOrderSaleAmountDTO> resultLst= Lists.newArrayList();
        List<Long> shopIdLst = Arrays.stream(shopIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<ShopUrge> shopLst=  sgConversionRestBussiness.selectUrgeShopByShopIdLstByType(shopIdLst,0);

        Map<String, List<ShopUrge>> shopMap = shopLst.stream().collect(Collectors.groupingBy(ShopUrge::getDb));
        for (Map.Entry<String, List<ShopUrge>> entry : shopMap.entrySet()) {
            List<ShopUrge> urgeShopLst = entry.getValue();
            if (CollectionUtils.isEmpty(urgeShopLst)) {
                continue;
            }
            List<ShopOrderSaleAmountDTO> shopOrderSaleAmountLst=  sgConversionRestBussiness.selecShopOrderSaleAmountLst(entry.getKey(),urgeShopLst,startDate,endDate);
            if(CollectionUtils.isNotEmpty(shopOrderSaleAmountLst)){
                resultLst.addAll(shopOrderSaleAmountLst);
            }
        }
        return resultLst;
    }

}
