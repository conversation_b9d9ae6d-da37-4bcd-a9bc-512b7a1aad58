package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.CsPerformanceInTimeBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Param.CsPerformanceInTimeParam;
import com.pes.jd.model.Param.RealTimePerformanceBordParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CsPerformanceInTimeBusinessImpl implements CsPerformanceInTimeBusiness {

	@Resource
	private PopRtSubRestTemplate popRtSubRestTemplate;

	@Override
	public ApiResponse selectCsPerformanceInTime(ShopQuery shop, CsPerformanceInTimeParam param, ShopSystemsettingDTO shopSystemsetting) throws DBNotExistException {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param", param)
				.put("shop", shopCommonParam)
				.put("shopSystemsettingDTO", shopSystemsetting)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		String uri = "/pes/cs/selectCsPerformanceCompare";
		return popRtSubRestTemplate.postRest(serviceId, uri, body);
	}

	@Override
	public ApiResponse selectCsPerformanceTimeSharing(ShopQuery shop, CsPerformanceInTimeParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param", param)
				.put("shop", shopCommonParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/pes/cs/selectCsPerformanceHourly", body);
	}

	@Override
	public ApiResponse selectCsPerformanceDetail(ShopQuery shop, CsPerformanceInTimeParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("param", param)
				.put("shop", shopCommonParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/pes/cs/selectCsPerformanceDetail", body);
	}

	@Override
	public ApiResponse selectRealTimePerformanceBord(ShopQuery shop,RealTimePerformanceBordParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/pes/selectRealTimePerformanceBord", body);

	}

//	@Override
//	public SendMsgResult selectRealTimePerformanceBord(ShopQuery shop, Date startDate, Date endDate,
//			List<String> csNickLst) throws Exception {
//		SendMsgResult result = new SendMsgResult(false);
//		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(),shop.getRtDbName());
//		HttpEntity<Object> body = RequestEntityBuilder.builder()
//				.put("shop", shopCommonParam)
//				.put("startDate", startDate)
//				.put("endDate", endDate)
//				.put("csnicks", csNickLst)
//				.toRequestEntity();
//		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_RT_SUB);
//
//		RestApiResponse<MessageSendResult> restApiResponse;
//		try {
//			restApiResponse = restApiTemplate.postRestOfResult(serviceId, "/pes/cs/selectShopRealTimePerformance", body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>(){});
//		} catch (HttpClientErrorException e) {
//			e.printStackTrace();
//			throw e;
//		}
//		if(restApiResponse.getSuccess()){
//            MessageSendResult apiResult = restApiResponse.getResult();
//            logger.info("init shop data msg result:{}",apiResult.getSendResult());
//
//        }
//		return result;
//	}



}
