package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.GoodsConsultBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class GoodsConsultBusinessImpl implements GoodsConsultBusiness {

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;

	@Override
	public ApiResponse selectGoodsConsultSummary(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultSummary", body);
	}

	@Override
	public ApiResponse selectGoodsConsultSummaryV3(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultSummaryV3", body);
	}

	@Override
	public ApiResponse selectGoodsConsultSummaryV2(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultSummaryV2", body);
	}

	@Override
	public ApiResponse selectGoodsConsultSummaryV4(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultSummaryV4", body);
	}

	@Override
	public ApiResponse selectCsConsultAnalysis(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectCsConsultAnalysis", body);
	}

	@Override
	public ApiResponse selectCsConsultAnalysisV3(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectCsConsultAnalysisV3", body);
	}

	@Override
	public ApiResponse selectCsConsultAnalysisV2(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectCsConsultAnalysisV2", body);
	}

	@Override
	public ApiResponse selectGoodsConsultDetail(ShopQuery shop, GoodsConsultParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultDetail", body);
	}

	@Override
	public ApiResponse selectGoodsConsultDetailV3(ShopQuery shop, GoodsConsultParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultDetailV3", body);
	}

	@Override
	public ApiResponse selectGoodsConsultDetailV2(ShopQuery shop, GoodsConsultParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultDetailV2", body);
	}


	@Override
	public ApiResponse selectGoodsConsultDetailV4(ShopQuery shop, GoodsConsultParam param) {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultDetailV4", body);
	}
    @Override
    public ApiResponse selectGoodsConsultSummaryOfSpu(ShopQuery shop, GoodsConsultParam param){

        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
                .put("paramStr", param).toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultSummaryOfSpu", body);
    }

	@Override
	public ApiResponse selectGoodsConsultSummaryOfSpuV2(ShopQuery shop, GoodsConsultParam param){

		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultSummaryOfSpuV2", body);
	}

    @Override
    public ApiResponse selectCsConsultAnalysisOfSpu(ShopQuery shop, GoodsConsultParam param){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
                .put("paramStr", param).toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectCsConsultAnalysisOfSpu", body);
    }

	@Override
	public ApiResponse selectCsConsultAnalysisOfSpuV2(ShopQuery shop, GoodsConsultParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectCsConsultAnalysisOfSpuV2", body);
	}

    /**
     * 商品咨询明细 spu维度
     * @param shop
     * @param param
     * @return
     * @throws DBNotExistException
     */
    @Override
    public ApiResponse selectGoodsConsultDetailOfSpu(ShopQuery shop, GoodsConsultParam param){
        ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
                .put("paramStr", param).toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultDetailOfSpu", body);
    }

	@Override
	public ApiResponse selectGoodsConsultDetailOfSpuV2(ShopQuery shop, GoodsConsultParam param){
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("paramStr", param).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/consult/goods/selectGoodsConsultDetailOfSpuV2", body);
	}

}
