package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.CsOrderIndexBusiness;
import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class CsOrderIndexBusinessImpl implements CsOrderIndexBusiness {
	
	@Autowired
	private PopSubRestTemplate popSubRestTemplate;
	

	@Override
	public ApiResponse selectCsOrderIndex(ShopQuery shop, String csNicks, String startDate, String endDate, AssistServiceQuery assistServiceQuery, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws DBNotExistException {
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shop", shopCommonParam)
				.put("csNicks", csNicks)
				.put("startDate", startDate)
				.put("endDate", endDate)
				.put("sortPageQuery", sortPageQuery)
				.put("assistServiceQuery", assistServiceQuery)
				.put("orderInfoLogUploadParamStr", orderInfoLogUploadParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		return popSubRestTemplate.postRest(serviceId, "/cs/order/selectCsOrderIndex", body);
	}

}
