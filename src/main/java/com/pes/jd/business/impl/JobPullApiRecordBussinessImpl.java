package com.pes.jd.business.impl;

import com.pes.jd.business.JobPullApiRecordBussiness;
import com.pes.jd.dao.JobPullApiRecordDao;
import com.pes.jd.model.DO.JobPullApiRecordDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: aiJun
 * @Date: 2019-08-12 15:13
 * @Version 1.0
 */
@Service
public class JobPullApiRecordBussinessImpl implements JobPullApiRecordBussiness {
    @Autowired
    private JobPullApiRecordDao jobPullApiRecordDao;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return jobPullApiRecordDao.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.insert(record);
    }

    @Override
    public int insertSelective(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.insertSelective(record);
    }

    @Override
    public JobPullApiRecordDO selectByPrimaryKey(Long id) {
        return jobPullApiRecordDao.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.updateByPrimaryKey(record);
    }
}
