package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSON;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopGoodsSkuInfoBusiness;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SkuQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.constant.PesCommonConstant;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.util.HttpUtils;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
@Service
public class ShopGoodsSkuInfoBusinessImpl implements ShopGoodsSkuInfoBusiness {

    @Autowired
    private PopSubRestTemplate popSubRestTemplate;
    @Override
    public ApiResponse searchShopGoodsSku(
            String categoryId, Long level, String skuName, String status,
            Integer pageSize, Integer pageNum, ShopQuery shopQuery, Long ownSkuId,
            String propertity, String sortDirection, String associativeStatus, Integer addStatus, Byte dimension) throws Exception {
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/search",
                "propertity", propertity, "sortDirection", sortDirection, "associativeStatus", associativeStatus,
                "categoryId", categoryId, "level", level.toString(), "status", status, "pageSize", String.valueOf(pageSize), "pageNum", String.valueOf(pageNum), "skuName", String.valueOf(skuName), "ownSkuId", String.valueOf(ownSkuId), "addStatus", String.valueOf(addStatus), "dimension", String.valueOf(dimension)
        ), HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query)));
    }

    @Override
    public ApiResponse deleteSkuLabel(ShopQuery shopQuery, Long labelId, Byte dimension) throws Exception {
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        if (PesCommonConstant.DIMENSION_SKU_BYTE.equals(dimension)) {//sku维度删除知识点
            return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/delete_sku_label",
                    "labelId", String.valueOf(labelId)
            ), HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query)));
        } else {//spu维度添加知识点
            return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/delete_good_label",
                    "labelId", String.valueOf(labelId)
            ), HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query)));
        }
    }

    @Override
    public ApiResponse updateSkuLabel(ShopQuery shopQuery, String label, Byte dimension) {
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        if (PesCommonConstant.DIMENSION_SKU_BYTE.equals(dimension)) {//sku维度添加知识点
            return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/update_sku_label")
                    , HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query), "label", label));
        } else {//spu维度添加知识点
            return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/update_good_label")
                    , HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query), "label", label));
        }
    }

    @Override
    public ApiResponse insertSkuLabel(ShopQuery shopQuery, String label, Byte dimension){
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        if (PesCommonConstant.DIMENSION_SKU_BYTE.equals(dimension)) {//sku维度添加知识点
            return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/insert_sku_label"
            ), HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query), "label", label));
        } else {//spu维度添加知识点
            return popSubRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/shop_sku/insert_good_label"
            ), HttpUtils.getHttpEntityForm("skuQuery", JSON.toJSONString(query), "label", label));
        }

    }

    @Override
    public ApiResponse insertRecommend(ShopQuery shopQuery, String recommend){
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        return popSubRestTemplate.postRest(serviceId,RestOperator.mergeUriArguments("/shop_sku/insert_recommend"
        ),HttpUtils.getHttpEntityForm("skuQuery",JSON.toJSONString(query),"recommend",recommend));
    }

    @Override
    public ApiResponse deleteRecommend(ShopQuery shopQuery, Long skuId){
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        return popSubRestTemplate.postRest(serviceId,RestOperator.mergeUriArguments("/shop_sku/delete_recommend",
                "skuId", String.valueOf(skuId)
        ),HttpUtils.getHttpEntityForm("skuQuery",JSON.toJSONString(query)));
    }

    @Override
    public ApiResponse settingAssociative(ShopQuery shopQuery, Long skuId, String addedSkuIds){
        String serviceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
        SkuQuery query = SkuQuery.newBuilder().dbName(shopQuery.getDbName()).schemaId(shopQuery.getSchemaId()).shopId(shopQuery.getShopId()).build();
        return popSubRestTemplate.postRest(serviceId,RestOperator.mergeUriArguments("/shop_sku/setting_associative",
                "skuId", String.valueOf(skuId),"addedSkuIds",addedSkuIds
        ),HttpUtils.getHttpEntityForm("skuQuery",JSON.toJSONString(query)));
    }

	@Override
	public ApiResponse selectShopGoodsSkuInfo(ShopQuery shop, List<Long> skuIdLst){
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("skuIdLst", skuIdLst)
				.put("shop", shopCommonParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		apiResponse = popSubRestTemplate.postRest(serviceId, "/shopGoodSku/selectShopGoodsSkuInfo", body);
		return apiResponse;
	}
}
