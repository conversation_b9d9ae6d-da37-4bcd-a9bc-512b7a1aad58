package com.pes.jd.business.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.RealtimeMonitorBusiness;
import com.pes.jd.business.SensitiveWordBusiness;
import com.pes.jd.dao.CsChatSessionDao;
import com.pes.jd.dao.CsChatlogDao;
import com.pes.jd.dao.CsLoginlogDao;
import com.pes.jd.dao.CsWarningDao;
import com.pes.jd.model.DO.CsWarningDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.task.executor.KeyWordCheckExecutor;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.DateFormatUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * ClassName:RealTimeMonitorBusinessImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 *
 * @see
 * @since JDK 1.8
 */
@Service
public class RealtimeMonitorBusinessImpl implements RealtimeMonitorBusiness {

    private static Logger logger = LoggerFactory.getLogger(RealtimeMonitorBusinessImpl.class);
    @Autowired
    private CsWarningDao csWarningDao;

    @Autowired
    private CsChatSessionDao csChatSessionDao;

    @Autowired
    private SensitiveWordBusiness sensitiveWordBusiness;

    @Autowired
    @Qualifier("taskExecutor")
    private ExecutorService taskExecutor;

    @Autowired
    private CsLoginlogDao csLoginlogDao;

    @Override
    public void calculateShopBadReceive(JobShopQuery shop,
                                        JobDateQuery jobDate,
                                        boolean isDelData,
                                        String csNick,
                                        Date sdate,
                                        Date edate,
                                        Map<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionMap) {
        Set<String> keywordSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(shop.getSysWordLst())) {
            keywordSet.addAll(shop.getSysWordLst());
        }
        if (CollectionUtils.isNotEmpty(shop.getCustomWordLst())) {
            keywordSet.addAll(shop.getCustomWordLst());
        }
        List<String> keywordList = Lists.newArrayList(keywordSet);
        JobShopDTO jobShopDTO = shop.getShop();

//        long s = System.currentTimeMillis();
        //遍历结算所有的不良接待
        if (isDelData) {
            int deleteNum = csWarningDao.batchDeleteCsWarningByDateByTypeAndCs(jobShopDTO, sdate,
                    edate, CommonConstants.WARNING_CS_VIOLATION, csNick);
            deleteNum += csWarningDao.batchDeleteCsWarningByDateByTypeAndCs(jobShopDTO, sdate,
                    edate, CommonConstants.WARNING_BUYER_RAIL, csNick);
//			logger.info("realTime calculateShopBadReceive delete num = {} ,startDate = {}", deleteNum,
//					DateFormatUtils.formatYMdHms(jobDate.getStartDate()));
        }
        csChatSessionMap.entrySet().forEach(ele -> calAndInsertBadReceive(ele, jobShopDTO, isDelData, csNick, sdate, edate, keywordList));
//        long e = System.currentTimeMillis();
//        logger.info("计算所有的不良接待 计算耗时:{}ms", e - s);
    }

    private void calAndInsertBadReceive(Map.Entry<CsChatSessionDTO,
            List<CsChatlogDTO>> ele,
                                        JobShopDTO jobShopDTO,
                                        Boolean isDelData,
                                        String csNick,
                                        Date startDate,
                                        Date endDate, List<String> keywordList) {
        CsChatSessionDTO csChatSession = ele.getKey();
        List<CsChatlogDTO> shopChatlog = ele.getValue();
        if (CollectionUtils.isEmpty(shopChatlog)) {
            return;
        }
        List<CsWarningDO> csWarningList = Lists.newArrayList();
        Set<String> warningBuyerRailSet = Sets.newHashSet();// 顾客辱骂
        CsWarningDO csWarning;
//        //客服聊天记录
//        List<CsChatlogDTO> csChatlog = new ArrayList<>(shopChatlog.size());
        long countNum = 0;
        long cs = 0;
        long buyer = 0;
        long sss = System.currentTimeMillis();
        //顾客说话
        List<CsChatlogDTO> customerSays = shopChatlog.stream()
                .filter(chatLog -> chatLog.getDirection() == 1)
                .collect(Collectors.toList());
        //客服说话
        List<CsChatlogDTO> csSays = shopChatlog.stream()
                .filter(chatLog -> chatLog.getDirection() == 0)
                .collect(Collectors.toList());

        //客户命中的敏感词
        CsChatlogDTO csChatlogDTO;
        Set<String> hitKeyWord = Sets.newHashSet();
        for (int i = 0; i < customerSays.size(); i++) {
            csChatlogDTO = customerSays.get(i);
            String content = this.removeLabel(csChatlogDTO.getContent());
            for (String keyword : keywordList) {
                if (content.contains(keyword)) {
                    hitKeyWord.add(keyword);
                }
            }

        }
        //---命中新增一条记录
        if (CollectionUtils.isNotEmpty(hitKeyWord)) {
            csWarning = new CsWarningDO();
            csWarning.setDate(startDate);
            csWarning.setShopId(csChatSession.getShopId());
            csWarning.setCsNick(csNick);
            csWarning.setCustomer(csChatSession.getCustomer());
            csWarning.setSid(csChatSession.getSid());
            csWarning.setSessionBeginTime(csChatSession.getSessionBeginTime());
            csWarning.setSessionEndTime(csChatSession.getEndDatetime());
            csWarning.setWarningType(CommonConstants.WARNING_BUYER_RAIL);
            //关键词插入
            doJoinKeyword(csWarning, hitKeyWord);
            csWarningList.add(csWarning);
        }

//        for (CsChatlogDTO chatlog : shopChatlog) {
//            if (chatlog.getDirection() == 1) {
//                long sssss = System.currentTimeMillis();
//                List<String> hitKeyWord = new ArrayList();
//                //校验对话中匹配多少关键词
//                String chatlogContent = chatlog.getContent();
//                //去除标签
//                String content = this.removeLabel(chatlogContent);
//                for (String keyword : keywordList) {
//                    if (content.contains(keyword)) {
//                        hitKeyWord.add(keyword);
//                    }
//                }
//
//                //未匹配到关键词跳过
//                if (CollectionUtils.isEmpty(hitKeyWord)) {
//                    continue;
//                }
//
//                // 客户说话,并且本次job未向该客服发送过告警 收集敏感词
//                for (String keyword : keywordList) {
//                    if (content.contains(keyword)) {
//                        System.out.println("keyword={" + keyword + "},content={" + chatlog.getContent() + "}");
//                        countNum++;
//                        // 匹配到顾客辱骂关键字
//                        csWarning = new CsWarningDO();
//                        csWarning.setDate(startDate);
//                        csWarning.setShopId(chatlog.getShopId());
//                        csWarning.setCsNick(chatlog.getCsNick());
//                        csWarning.setCustomer(chatlog.getBuyerNick());
//                        csWarning.setSid(chatlog.getSid());
//                        csWarning.setSessionBeginTime(csChatSession.getSessionBeginTime());
//                        csWarning.setSessionEndTime(csChatSession.getEndDatetime());
//                        csWarning.setWarningType(CommonConstants.WARNING_BUYER_RAIL);
//
//                        //关键词插入
//                        doJoinKeyword(csWarning, hitKeyWord);
//                        csWarningList.add(csWarning);
//                    }
//                }
//
//
//
//
//
//                long eeeee = System.currentTimeMillis();
//                buyer += (eeeee - sssss);
//            } else {
//                // 客服说话,并且本次job未向该客服发送过告警
//                csChatlog.add(chatlog);
//            }
//        }
        long eee = System.currentTimeMillis();
//			logger.info("匹配买家聊天记录的敏感词耗时【{}】毫秒 客服匹配耗时【{}】毫秒，顾客匹配耗时【{}】毫秒",(eee-sss),cs,buyer);
//		logger.info("日期{}-->{}匹配到的敏感词总数为：{}",DateFormatUtils.formatYMdHms(jobDate.getStartDate()),DateFormatUtils.formatYMdHms(jobDate.getEndDate()),countNum);

        //处理关键词校验
        List<CsWarningDO> jdCsWarningLst = this.doCheckJdKeyWordByThread(jobShopDTO, startDate, endDate, csSays, keywordList, jobShopDTO.getSessionKey(), csChatSession, csNick);
        if (CollectionUtils.isNotEmpty(jdCsWarningLst)) {
            csWarningList.addAll(jdCsWarningLst);
        }

        //相同的类型，相同Sid存在多次违规，只记录第一次
        //去除这两天按类型已经记录过的会话  暂时在查询的维度做处理	不需要动rt-job	_zaj
//		doRemoveIsSave(jobShop, jobDate, csWarningList);


        if (CollectionUtils.isNotEmpty(csWarningList)) {
            int insertNum = csWarningDao.batchInsertCsWarning(jobShopDTO, startDate, csWarningList);
//            try {
//                logger.info("realTime calculateShopBadReceive insert num = {} ,startDate = {},endDate={}", insertNum,
//                        DateFormatUtils.formatYMdHms(startDate), DateFormatUtils.formatYMdHms(endDate));
//            } catch (ParseException e) {
//                e.printStackTrace();
//            }
        }

    }

//	/**
//	 * 已经保存过的SID 和 type 不用再保存了
//	 * @param jobShop
//	 * @param jobDate
//	 * @param csWarningList
//	 */
//	private void doRemoveIsSave(JobShopDTO jobShop, JobDateQuery jobDate, List<CsWarningDO> csWarningList) {
//		if (CollectionUtils.isEmpty(csWarningList)) {
//			return;
//		}
//		//因为慢响应可以查询两天所以要校验两天的数据
//		Date startDate = DateUtils.getDateByPeriod(jobDate.getStartDate(), -1);
//		List<CsWarningDO> allWarning = csWarningDao.selectTotalWarnByShopIdAndDate(jobShop, jobDate, startDate);
//		if (CollectionUtils.isEmpty(allWarning)) {
//			return;
//		}
//		csWarningList.removeIf(realWarn -> {
//			for (CsWarningDO dbWarn : allWarning) {
//				if (realWarn.getSid().equals(dbWarn.getSid()) && realWarn.getWarningType().equals(dbWarn.getWarningType())) {
//					return true;
//				}
//
//			}
//			return false;
//		});
//
//	};

    //因为发卡片会有标签，出去干扰的标签
    private String removeLabel(String chatlogContent) {
        return chatlogContent.replaceAll("<[^>]*>", "");
    }


    private CsWarningDO getCsWarningDo(CsWarningDO csWarning) {
        if (csWarning == null) {
            csWarning = new CsWarningDO();
        }
        return csWarning;
    }

    private void doJoinKeyword(CsWarningDO csWarning, Collection<String> hitKeywordLst) {
        if (CollectionUtils.isEmpty(hitKeywordLst)) {
            return;
        }
        String keyWordStr = hitKeywordLst.stream().collect(Collectors.joining(","));
        csWarning.setKeyword(keyWordStr);
    }

    private List<CsWarningDO> doCheckJdKeyWordByThread(JobShopDTO jobShop, Date startDate, Date endDate, List<CsChatlogDTO> csChatlog, List<String> keywordList, String sessionKey, CsChatSessionDTO csChatSession, String csNick) {
        if (CollectionUtils.isEmpty(csChatlog)) return new ArrayList<>();
//		logger.info("多线程执行JD接口校验关键词 start");
        long s = System.currentTimeMillis();
        List<CsWarningDO> resultCwList = new ArrayList<>();
        List<CsChatlogDTO> warnChatLogs = new ArrayList<>();
        List<CsChatlogDTO> chatLogLst;
        List<List<CsChatlogDTO>> chatLogLsts = CollectionUtil.avgAssignLst(csChatlog, csChatlog.size());
        CompletionService<Map<String, Object>> completionService = new ExecutorCompletionService<>(taskExecutor);
        AtomicBoolean containSensitiveWord = new AtomicBoolean(false);
        for (int i = 0; i < chatLogLsts.size(); i++) {
            chatLogLst = chatLogLsts.get(i);
            String name = "handleCheckJdKeyWord-".concat(String.valueOf(jobShop.getShopId())).concat("-").concat(String.valueOf((i + 1)));//声明线程名称 eg:handleCheckJdKeyWord-shopId-1
//			logger.info("线程{}->JD敏感词校验第{}批聊天记录,shopId={} {}  ", (i + 1), (i + 1), jobShop.getShopId(), jobShop.getTitle());
            KeyWordCheckExecutor keyWordCheckExecutor = new KeyWordCheckExecutor(jobShop, startDate, endDate, chatLogLst, keywordList, name, chatLogLsts.size(), csChatSession, containSensitiveWord, csNick);
            completionService.submit(keyWordCheckExecutor);
        }
        try {
            for (int i = 0; i < chatLogLsts.size(); i++) {
                Map<String, Object> result = completionService.take().get();
                if (result != null) {
//                    List<CsChatlogDTO> warnChatLogLst = (List<CsChatlogDTO>) result.get("warnChatLogs");
                    List<CsWarningDO> CsWarningLst = (List<CsWarningDO>) result.get("CsWarningLst");
                    if (CollectionUtils.isNotEmpty(CsWarningLst)) {
                        resultCwList.addAll(CsWarningLst);
                    }
//                    if (CollectionUtils.isNotEmpty(warnChatLogLst)) {
//                        warnChatLogs.addAll(warnChatLogLst);
//                    }
                }

            }
        } catch (Exception e) {
            logger.error("start doCheckJdKeyWordByThread error", e);
        }
        long e = System.currentTimeMillis();
//		logger.info("多线程执行JD接口校验关键词 end  处理聊天记录数=【{}】条，匹配上的聊天记录数=【{}】条，耗时=【{}】毫秒", (csChatlog.size()), (warnChatLogs.size()),(e-s));

        return resultCwList;
    }


    public List<String> splitCustomerServiceList(List<CsDTO> csList, int length) {
        if (CollectionUtils.isEmpty(csList)) {
            return Lists.newArrayList();
        }
        List<String> resultList = new ArrayList<>();
        if (length <= 0) {
            resultList.add(concat(csList, 0, csList.size()));
        } else {
            for (int i = 0; i < csList.size(); i = i + length) {
                resultList.add(concat(csList, i, length));
            }
        }
        return resultList;
    }

    private String concat(List<CsDTO> csList, int startIndex, int length) {

        int endIndex = startIndex + length;
        if (endIndex > csList.size()) {
            endIndex = csList.size();
        }
        StringBuilder sb = new StringBuilder();
        while (startIndex < endIndex - 1) {
            sb.append(csList.get(startIndex).getNick());
            sb.append(",");
            startIndex++;
        }
        sb.append(csList.get(endIndex - 1).getNick());
        return sb.toString();
    }


}
