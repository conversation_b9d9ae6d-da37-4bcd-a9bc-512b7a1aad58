package com.pes.jd.business.impl;

import cn.hutool.core.thread.NamedThreadFactory;
import com.google.common.collect.Lists;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.business.ShopGoodsRateHandBussiness;
import com.pes.jd.dao.ShopGoodsFeedbackRateDao;
import com.pes.jd.dao.ShopGoodsSkuDao;
import com.pes.jd.data.converter.ShopGoodsSummarysDateConverter;
import com.pes.jd.model.BO.ShopGoodsRateBO;
import com.pes.jd.model.DO.ShopGoodsFeedbackRateDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodSkuDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.DateFormatUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年04月14 20:36:36<br>
 */
@Service
public class ShopGoodsRateHandBussinessImpl implements ShopGoodsRateHandBussiness {

    private static Logger logger = LoggerFactory.getLogger(ShopGoodsRateHandBussinessImpl.class);

    @Resource
    private ShopGoodsSkuDao shopGoodsSkuDao;
    @Resource
    private ShopGoodsFeedbackRateDao shopGoodsFeedbackRateDao;
    @Resource
    private ShopGoodsSummarysDateConverter shopGoodsSummarysDateConverter;



    public int batchHandShopGoodsRate(JobShopQuery jobShop, JobDateQuery jobDate, List<ShopGoodSkuDTO> goodsSkuLst) throws Exception {
        if (CollectionUtils.isEmpty(goodsSkuLst)) {
            logger.info("shopId:{} goodsSkuLst is null;", jobShop.getShop().getShopId());
            return 0;
        }
        logger.info("shopId:{},date:{} goodsSkuLst size:{} ",jobShop.getShop().getShopId(),jobDate.getDate(),goodsSkuLst.size());
        long s5=System.currentTimeMillis();
        List<ShopGoodsFeedbackRateDO> resultLst = Lists.newArrayList();
        List<String> skuIdLst=Lists.newArrayList(goodsSkuLst.stream().map(c -> c.getSkuId() + "").collect(Collectors.toSet()));
        Map<Long, List<ShopGoodSkuDTO>> wareSkuMap = goodsSkuLst.stream().collect(Collectors.groupingBy(ShopGoodSkuDTO::getWareId));
        List<String> spuOfSkuLst = Lists.newArrayList();
        for (Map.Entry<Long, List<ShopGoodSkuDTO>> entry : wareSkuMap.entrySet()) {
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                spuOfSkuLst.add(String.valueOf(entry.getValue().get(0).getSkuId()));
            }
        }
        if(logger.isDebugEnabled()){
            logger.debug("get spu time :{}ms",System.currentTimeMillis()-s5);
        }
        List<ShopGoodsFeedbackRateDO> skuGoodsRateLst=null;
        List<ShopGoodsFeedbackRateDO> spuGoodsRateLst=null;

        if(goodsSkuLst.size()>CommonConstants.BATCH_THREAD_LIMIT_NUM){
            long s2=System.currentTimeMillis();
            skuGoodsRateLst = pullShopGoodsRateByThread(jobShop.getShop(), jobDate.getDate(), skuIdLst, 1);
            if(logger.isDebugEnabled()){
                logger.debug("pull sku time :{}ms",System.currentTimeMillis()-s2);
            }

            long s3=System.currentTimeMillis();
            spuGoodsRateLst = pullShopGoodsRateByThread(jobShop.getShop(), jobDate.getDate(), spuOfSkuLst, 2);

            if(logger.isDebugEnabled()) {
                logger.debug("pull spu time :{}ms",System.currentTimeMillis()-s3);
            }

        }else{
            skuGoodsRateLst = pullShopGoodsRate(jobShop.getShop(), jobDate.getDate(), skuIdLst, 1);
            spuGoodsRateLst= pullShopGoodsRate(jobShop.getShop(), jobDate.getDate(), spuOfSkuLst, 2);
        }

        if (CollectionUtils.isNotEmpty(skuGoodsRateLst)) {
            logger.info("shopId:{} pull skuRate size:{}",jobShop.getShop().getShopId(),skuGoodsRateLst.size());
            resultLst.addAll(skuGoodsRateLst);
        }
        long s4=System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(spuGoodsRateLst)) {
            logger.info("shopId:{} pull spuRate size:{}",jobShop.getShop().getShopId(),spuGoodsRateLst.size());
            Map<Long, Long> skuWareMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodSkuDTO::getSkuId, ShopGoodSkuDTO::getWareId, (oldValue, newValue) -> newValue));
            for (ShopGoodsFeedbackRateDO spu : spuGoodsRateLst) {
                if (skuWareMap.get(spu.getGoodsId()) != null) {
                    //替换拉取中的sku换成spu入库
                    spu.setGoodsId(skuWareMap.get(spu.getGoodsId()));
                }
            }
            resultLst.addAll(spuGoodsRateLst);
        }        if(logger.isDebugEnabled()){
            logger.debug("merge spu time :{}ms",System.currentTimeMillis()-s4);
        }

        long s7=System.currentTimeMillis();
        int num=0;
        try {
            num= insertShopGoodSkuWithFile(jobShop,jobDate.getDate(),resultLst);
        } catch (Exception e) {
            logger.error("shopId:{},date:{} insertShopGoodSkuWithFile error:{}",jobShop.getShop().getShopId(),jobDate.getDate(),e.getMessage(),e);
            throw e;
        }
        if(logger.isDebugEnabled()) {
            logger.debug("insert rate time :{}ms", System.currentTimeMillis() - s7);
        }
        return num;
    }
    @Override
    public void pullShopGoodsRate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception {
        long s1 = System.currentTimeMillis();
        int deleteNum = 0;
        if (isDelData) {
            long s4=System.currentTimeMillis();
            List<Long> ids = shopGoodsFeedbackRateDao.selectShopGoodsFeedbackIdsByShopIdByDate(jobShop.getShop(), jobDate.getDate());
            if(logger.isDebugEnabled()){
                logger.debug("select shopGoodsRate id time：{} ms",System.currentTimeMillis()-s4);
            }
            long s0=System.currentTimeMillis();
            deleteNum = shopGoodsFeedbackRateDao.deleteByShopIdByIds(jobShop.getShop(), jobDate.getDate(), ids);
            if(logger.isDebugEnabled()){
                logger.debug("delete shopGoodsRate time：{} ms",System.currentTimeMillis()-s0);
            }
        }
        long s8=System.currentTimeMillis();
       Integer count= shopGoodsSkuDao.selectSkuCountByShopIdByStatus(jobShop.getShop(),1);
       if(logger.isDebugEnabled()){
           logger.debug("查询数量 time：{} ms",System.currentTimeMillis()-s8);
       }
        List<ShopGoodSkuDTO> resultLst=Lists.newArrayList();
//        List<ShopGoodSkuDTO> skulst1  = shopGoodsSkuDao.selectShopGoodsSkuByShopIdByStatus(jobShop.getShop(),1,0,130659);
//        logger.info("一次性查所有 time：{} ms",System.currentTimeMillis()-s2);
        //进行分批查询
        int num=0;
        long s3=System.currentTimeMillis();
        int pageSize=CommonConstants.BATCH_SELECT_LIMIT_NUM;
        if(count!=null&&count>CommonConstants.BATCH_SELECT_LIMIT_NUM){
            for (int i = 0; i < (int)Math.ceil((float)count/(float)CommonConstants.BATCH_SELECT_LIMIT_NUM); i++) {
                int currentPage=i*pageSize;
                List<ShopGoodSkuDTO> skulst  = shopGoodsSkuDao.selectShopGoodsSkuByShopIdByStatus(jobShop.getShop(),1,currentPage,pageSize);
                if(CollectionUtils.isNotEmpty(skulst)){
                    resultLst.addAll(skulst);
                }
            }
        }else{
            List<ShopGoodSkuDTO> skulst  = shopGoodsSkuDao.selectShopGoodsSkuByShopIdByStatus(jobShop.getShop(),1,0,pageSize);
            if(CollectionUtils.isNotEmpty(skulst)){
                resultLst.addAll(skulst);
            }
        }
        logger.info("batch select shop goods sku time：{} ms",System.currentTimeMillis()-s3);
        num+=batchHandShopGoodsRate(jobShop,jobDate,resultLst);
        logger.info("shopId:{} date:{} pullShopGoodsRate ,insertNum:{} deleteNum:{} time :{}ms",jobShop.getShop().getShopId(),jobDate.getDate(),num,deleteNum,(System.currentTimeMillis()-s1));
    }

    public List<ShopGoodsFeedbackRateDO> pullShopGoodsRate(JobShopDTO shop, Date date,List<String> skuIdLst, Integer type) throws Exception {
        List<ShopGoodsFeedbackRateDO> skuFeedRateResultLst = Lists.newArrayList();
        List<List<String>> spuSplitLst= CollectionUtil.smallToLst(skuIdLst,100);
        for (List<String> list : spuSplitLst) {
            List<ShopGoodsFeedbackRateDO> skuLst= shopGoodsSummarysDateConverter.pullShopGoodsFeedbackRateLst(shop, date, type,list);
            if(CollectionUtils.isNotEmpty(skuLst)){
                skuFeedRateResultLst.addAll(skuLst);
            }
        }
        return skuFeedRateResultLst;
    }

    public List<ShopGoodsFeedbackRateDO> pullShopGoodsRateByThread(JobShopDTO shop, Date date,List<String> skuIdLst, Integer type) throws Exception {
        List<ShopGoodsFeedbackRateDO> skuFeedRateResultLst = Lists.newArrayList();
        //多线程去跑数据
        List<List<String>> skuSplitLst= CollectionUtil.avgAssignLst(skuIdLst,80);
        int groupNum = skuSplitLst.size();
        final int poolSize = Math.min(groupNum,80);
        ExecutorService executorService = new ThreadPoolExecutor(poolSize, poolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(groupNum),
                new NamedThreadFactory("pop-goods-rate", false));

        CompletionService<Map<String, Object>> completionService = new ExecutorCompletionService<Map<String, Object>>(executorService);
        for (List<String> list : skuSplitLst) {
            ShopGoodsRateBO cdi = new ShopGoodsRateBO( shop, date,type,list);
            completionService.submit(cdi);
        }
       executorService.shutdown();
        try {
            for (int i = 0; i < groupNum; i++) {
                Map<String, Object> result = completionService.take().get();
                if(result!=null){
                    @SuppressWarnings("unchecked")
                    List<ShopGoodsFeedbackRateDO> goodsFeedRateLst = (List<ShopGoodsFeedbackRateDO>) result.get("goodsFeedRateLst");
                    if(CollectionUtils.isNotEmpty(goodsFeedRateLst)){
                        skuFeedRateResultLst.addAll(goodsFeedRateLst);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("shopId；{} pullShopGoodsRateByThread error:{}",shop.getShopId(),e.getMessage(),e);
            executorService.shutdownNow();
            throw e;
        }
        return skuFeedRateResultLst;
    }

    public int insertShopGoodSkuWithFile(JobShopQuery jobShop,Date date, List<ShopGoodsFeedbackRateDO> shopGoodsRateLst) {
        JobShopDTO shop = jobShop.getShop();
        int insertNum = 0;
        File file = null;
        FileOutputStream fis = null;
        InputStreamReader isr = null;
        OutputStreamWriter osw = null;
        BufferedWriter bw = null;
        for (int i = 0; i < CommonConstants.RECALLAPI_TIMES; i++) {
            try {
                file = new File(System.currentTimeMillis() + "shopsku.txt");
                if (!file.exists()) {
                    file.createNewFile();
                }
                fis = new FileOutputStream(file);
                osw = new OutputStreamWriter(fis, "utf-8");
                bw = new BufferedWriter(osw);
                // 写数到文件
                if (logger.isDebugEnabled()) {
                    logger.debug("sku 开始写入到文件中...");
                }
                long ss = System.currentTimeMillis();
                writeShopGoodsRateFile(shop, shopGoodsRateLst, bw);
                long ee = System.currentTimeMillis();
                if (logger.isDebugEnabled()) {
                    logger.debug("sku 写入到文件中完成 耗时{}ms...", (ee - ss));
                }
                try {
                    ss = System.currentTimeMillis();
                    if (logger.isDebugEnabled()) {
                        logger.debug("sku 通过文本插入到数据库开始...");
                    }
                    insertNum = shopGoodsFeedbackRateDao.insertShopGoodsFeedbackRateByFile(jobShop.getShop(),date, file.getAbsolutePath());
                    ee = System.currentTimeMillis();
                    if (logger.isDebugEnabled()) {
                        logger.debug("sku 通过文本插入到数据库结束 耗时:{}s,插入条数为：{}条...", (ee - ss) / 1000, insertNum);
                        logger.debug("····write insertShopGoodsFeedbackRateByFile successful·····");
                    }
                } catch (Exception e) {
                    logger.error("insertShopGoodsFeedbackRateByFile error ", e.getMessage(), e);
                }
                break;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                    logger.error("write insertShopGoodsFeedbackRateByFile  exception:", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            } finally {
                closeAll(fis, isr, bw, file);
            }
        }
        return insertNum;
    }

    public void writeShopGoodsRateFile(JobShopDTO shop, List<ShopGoodsFeedbackRateDO> shopGoodsFeedbackRateLst, BufferedWriter bw) throws IOException, ParseException {
        for (ShopGoodsFeedbackRateDO shopGoodsRate : shopGoodsFeedbackRateLst) {
            StringBuilder sql = new StringBuilder();
            sql.append(shop.getShopId());
            sql.append(CommonConstants.LOAD_DATA_INFILE_SEPARATOR);
            sql.append(DateFormatUtils.format_Ymd(shopGoodsRate.getDate()));
            sql.append(CommonConstants.LOAD_DATA_INFILE_SEPARATOR);
            sql.append(shopGoodsRate.getGoodsId());
            sql.append(CommonConstants.LOAD_DATA_INFILE_SEPARATOR);
            sql.append(shopGoodsRate.getGoodRate());
            sql.append(CommonConstants.LOAD_DATA_INFILE_SEPARATOR);
            sql.append(shopGoodsRate.getType());
            sql.append(CommonConstants.LOAD_DATA_INFILE_LINE_END);
            bw.write(sql.toString());
        }

        bw.flush();
    }

    public void closeAll(FileOutputStream fis, InputStreamReader isr, BufferedWriter bw, File file) {
        if (bw != null) {
            try {
                bw.close();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
        if (isr != null) {
            try {
                isr.close();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }

        if (fis != null) {
            try {
                fis.close();
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
            }
        }
        if (file.exists()) {
            file.delete();
        }
    }

}
