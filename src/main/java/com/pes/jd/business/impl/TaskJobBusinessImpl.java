package com.pes.jd.business.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.netflix.client.ClientException;
import com.pes.jd.builder.JsonBuilder;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopBusiness;
import com.pes.jd.business.TaskJobBusiness;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.SendMsgResult;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse;
import com.pes.jd.ms.domain.Result.task.dispatching.MessageSendResult;
import com.pes.jd.ms.domain.Result.task.dispatching.TestRestResult;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.ms.utils.MQMessageUtil;
import com.pes.jd.rest.DispatchingRestTemplate;
import com.pes.jd.rest.PopRtJobRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Task-Job 消息发送中心
 */
@SuppressWarnings("Duplicates")
@Service
public class TaskJobBusinessImpl implements TaskJobBusiness {
    private final static Logger logger = LoggerFactory.getLogger(TaskJobBusinessImpl.class);

    private final String TASK_JOBR_EMOTE_SERVICE_URL = "/task/message/sendTaskJobMessage";

    private final String TASK_JOBR_ERROR_SERVICE_URL = "/task/errorShopMessage/sendTaskJobMessage";

    private final String TASK_JOBR_PULLORCALCHATLOG_EMOTE_SERVICE_URL = "/task/message/sendChatlogTaskJobMessage";

    private final String TASK_JOBR_PULLANDCALREFUNDDATA_URL = "/task/message/sendRefundPullAndCalTaskJobMessage";

    private final String TASK_JOBR_EMOTE_BY_NICK_SERVICE_URL = "/task/message/sendTaskJobMessageByNick";

    private final String TASK_JOBR_EMOTE_BY_SHOPID_SERVICE_URL = "/realTimeJob/pullRealTimrJobByShopId";

    private final String TASK_JOBR_DUTYLOG_SERVICE_URL = "/task/message/sendDutylogTaskJobMessage";

    private final String PULL_AND_CAL_RTJOB = "/task/message/pullAndCalRtJob";

    private final String PULL_HISTORY_ORDER = "/task/message/pullHistoryOrder";

    private final String PULL_FAILED_HISTORY_ORDER = "/task/errorShopMessage/pullFailedHistoryOrder";


    @Resource
    private DispatchingRestTemplate dispatchingRestTemplate;
    @Resource
    private PopRtJobRestTemplate popRtJobRestTemplate;
    @Autowired
    private RedisOperator<String, Object> redisOperator;
    @Resource
    private ShopBusiness shopBusiness;
    /**
     * 客服绩效-拉取
     *
     * @param shopId
     * @param
     * @param
     * @return
     */
    @Override
    public SendMsgResult initShopData(Long shopId, String startDateStr, String endDateStr) throws Exception {
        SendMsgResult sendMsgResult = new SendMsgResult();
        sendTaskJobMessageOfRedisForHisOd(shopId,startDateStr,endDateStr);
        sendMsgResult.setMessageId("加入初始化队列成功");
        return  sendMsgResult;
    }


    public void sendTaskJobMessageOfRedisForHisOd(Long shopId, String startDateStr, String endDateStr) throws Exception {

        ApiResponse apiResponse = shopBusiness.listShopInfoByShopIds(shopId + "");
        Object ShopDotList = apiResponse.getData().get("result");
        List<ShopDTO> shopDTOList = new ArrayList<>();
        ObjectMapper objectMapper = new ObjectMapper();
        ShopDTO shopDTO = null;
        try {
            if (ShopDotList != null) {
                String jsonString = objectMapper.writeValueAsString(ShopDotList);
                shopDTOList = objectMapper.readValue(jsonString,
                        new TypeReference<List<ShopDTO>>(){});
            }
        } catch (Exception e) {
            logger.error("selectActiveShopLst error:{}", e.getMessage(), e);
        }
        if(shopDTOList != null && shopDTOList.size() > 0) {
             shopDTO = shopDTOList.get(0);

        }
        String handleType = TaskJobDispatchEnum.SHOP_DATA_INIT.getType();
        boolean isYd = false;

        ShopDTO key = shopDTO;
        String tag;
        try {
            /* *****************自营跟pop需要两个不同的tag***************/
            int shopType = key.getType();
            if (CommonConstants.SHOP_TYPE_SELF == shopType) {
                tag = MQMessageUtil.getSelfTaskJobTag(key.getDb(), key.getSchemaId());
            } else {
                tag = MQMessageUtil.getTaskJobTag(key.getDb(), key.getSchemaId());
            }
            String param = JsonBuilder.builder()
                    .put("shopId", shopId+"")
                    .put("startDateStr", startDateStr)
                    .put("endDateStr", endDateStr)
                    .put("subDeadLineDateStr", null)
                    .put("type", handleType)
                    .put("isYd",isYd)
                    .toJsonString();
            logger.info("redis--->job.key={}", tag);
            logger.info("redis--->job.param={}", param);
            redisOperator.rightPush(tag, param);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
    }


    /**
     * 店铺数据-拉取与计算
     *
     * @param shopId
     * @param
     * @param
     * @return
     */
    @Override
    public SendMsgResult pullAndCalShopData(Long shopId, String startDateStr, String endDateStr) {

        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public SendMsgResult pullAndCalAllErrorShopData(String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_ERROR_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public SendMsgResult pullPvUv(Long shopId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_PVUV.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        System.out.println(restApiResponse.getResult().getMessageId());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public ApiResponse pullPvUvByNick(String nick, String startDateStr, String endDateStr) {

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("nick", nick)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_PVUV.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<Boolean> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_BY_NICK_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<Boolean>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        if (restApiResponse.getSuccess()) {
            logger.info("pullPvUvByNick msg result:{}", restApiResponse.getResult());
            ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
            response.setSuccess(Boolean.TRUE);
            return response;
        }
        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_PVUV_RECORD);
    }

    @Override
    public ApiResponse pullDutylogByNick(String nick, String startDateStr, String endDateStr) {

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("nick", nick)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_DUTYLOG.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<Boolean> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_BY_NICK_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<Boolean>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        if (restApiResponse.getSuccess()) {
            logger.info("pullPvUvByNick msg result:{}", restApiResponse.getResult());
            ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
            response.setSuccess(Boolean.TRUE);
            return response;
        }
        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_DUTY_RECORD);
    }

    @Override
    public SendMsgResult pullOrCalChatlog(Long shopId, String startDateStr, String endDateStr, String pullOrCalAll, String pullOrCalType) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("pullOrCalAll", pullOrCalAll)
                .put("type", CommonConstants.SHOP_DATA_PULL.equals(pullOrCalType) ? TaskJobDispatchEnum.TASK_JOB_PULLCHATLOG.getType() : TaskJobDispatchEnum.TASK_JOB_CALCHATLOG.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_PULLORCALCHATLOG_EMOTE_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        System.out.println(restApiResponse.getResult().getMessageId());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal Chatlog shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public SendMsgResult pullDutyLog(Long shopId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_DUTYLOG.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    /**
     * 店铺数据-计算
     *
     * @param shopId
     * @param startDateStr
     * @param endDateStr
     * @return
     * @throws ClientException
     */
    @Override
    public SendMsgResult calShopData(Long shopId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.SHOP_DATA_CAL.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        System.out.println(restApiResponse.getResult().getMessageId());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public SendMsgResult calAllErrorShopData(String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.SHOP_DATA_CAL.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_ERROR_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        System.out.println(restApiResponse.getResult().getMessageId());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }


    //----------------------------------------------- 实时JOB START -----------------------------------------------------------------------//

    /**
     * 店铺数据-拉取与计算(实时)
     *
     * @param shopId
     * @param
     * @param
     * @return
     */
    @Override
    public SendMsgResult pullAndCalRtShopData(Long shopId, String startDateStr, String endDateStr) {

        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        System.out.println(restApiResponse.getResult().getMessageId());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal shop data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }
    //----------------------------------------------- 实时JOB END -----------------------------------------------------------------------//


    @Override
    public TestRestResult testTaskDispatching(Long shopId) {

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<TestRestResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, "/task/message/testRest", body, new ParameterizedTypeReference<RestApiResponse<TestRestResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            TestRestResult apiResult = restApiResponse.getResult();
            System.out.println("Msg:" + apiResult.getMsg());
        }
        return restApiResponse.getResult();
    }

    @Override
    public SendMsgResult pullgoodSku(Long shopId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_GOODSKU.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, "/task/message/pullgoodsku", body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pullgoodSku result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public SendMsgResult calOrderEvaluate(long shopId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId + "")
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_CAL_ORDER_EVALUATE.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, "/task/message/calOrderEvaluate", body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("calOrderEvaluate result:{}", apiResult.getSendResult());
        }
        return result;
    }


    @Override
    public ApiResponse upDateShopStatus(String shopId) {
        ApiResponse apiResponse = new ApiResponse();
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shopId", shopId).toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());
        try {
            apiResponse = dispatchingRestTemplate.postRest(serviceId,
                    "/task/message/updateShopStatus", body);
        } catch (Exception e) {
            logger.error("upDateShopStatus:{}", e.getMessage(), e);
        }
        return apiResponse;
    }

    @Override
    public ApiResponse upDateShopPop(String shopId) {
        ApiResponse apiResponse = new ApiResponse();
        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shopId", shopId).toRequestEntity();
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());
        try {
            apiResponse = dispatchingRestTemplate.postRest(serviceId,
                    "/task/message/updateShopPop", body);
        } catch (Exception e) {
            logger.error("updateShopPop:{}", e.getMessage(), e);
        }
        return apiResponse;
    }


    @Override
    public SendMsgResult pullAndCalRefundData(String shopId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder().put("shopId", shopId)
                .put("startDateStr", startDateStr).put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_PULLANDCALREFUNDDATA.getType()).toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_PULLANDCALREFUNDDATA_URL, body,
                    new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
                    });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull pullAndCalRefundData msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public SendMsgResult pullDutylogByDb(String dbName, String schemaId, String startDateStr, String endDateStr) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("dbName", dbName)
                .put("schemaId", schemaId)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("type", TaskJobDispatchEnum.TASK_JOB_DUTYLOG.getType())
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_DUTYLOG_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        System.out.println(restApiResponse.getSuccess());
        if (restApiResponse.getSuccess()) {
            MessageSendResult apiResult = restApiResponse.getResult();
            logger.info("pull and cal shop dutylog data msg result:{}", apiResult.getSendResult());
        }
        return result;
    }

    @Override
    public ApiResponse pullRealTimrJobByShopId(Long shopId, String startDateStr, String endDateStr) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<Boolean> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, TASK_JOBR_EMOTE_BY_SHOPID_SERVICE_URL, body, new ParameterizedTypeReference<RestApiResponse<Boolean>>() {
            });
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        System.out.println(restApiResponse);
        if (restApiResponse.getSuccess()) {
            logger.info("pullRealTimrJobByShopId msg result:{}", restApiResponse.getResult());
            ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
            response.setSuccess(Boolean.TRUE);
            return response;
        }
        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_DUTY_RECORD);
    }

    @Override
    public ApiResponse pullAndCalRtJob(String startDateStr, String endDateStr, String shopId, String dbName,
                                       String schemaId) {

        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .put("shopId", shopId)
                .put("db", dbName)
                .put("schemaId", schemaId)
                .put("shopType", 0)
                .put("distinguishType", 33)
                .toRequestEntity();

        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());

        RestApiResponse<MessageSendResult> restApiResponse;
        try {
            restApiResponse = dispatchingRestTemplate.postRestOfResult(serviceId, PULL_AND_CAL_RTJOB, body, new ParameterizedTypeReference<RestApiResponse<MessageSendResult>>() {
            });
        } catch (HttpClientErrorException e) {
            logger.error(e.getMessage(), e);
            throw e;
        }

        if (null != restApiResponse && restApiResponse.getSuccess()) {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
    }

    /**
     * 初始化历史订单
     *
     * @param shopId
     * @param
     * @param
     * @return
     */
    @Override
    public ApiResponse initHistoryOrderData(Long shopId, String startDateStr, String endDateStr, String rtDb, String rtSchemaId) {
        ApiResponse apiResponse;
        try {
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("shopId", shopId)
                    .put("startDateStr", startDateStr)
                    .put("endDateStr", endDateStr)
                    .put("initFlag", "true")
                    .toRequestEntity();
            String serviceId = RestOperator.getServiceId(rtDb, rtSchemaId, ApplicationServiceNameEnum.PROVIDER_RT_TASK.getName());
            apiResponse = popRtJobRestTemplate.postRest(serviceId, PULL_HISTORY_ORDER, body);
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        return apiResponse;
    }

    @Override
    public ApiResponse pullFailedHistoryOrder(Long shopId, String startDateStr, String endDateStr) {
        ApiResponse apiResponse;
        try {
            RequestEntityBuilder.Builder builder = RequestEntityBuilder.builder();
            if(null != shopId){
                builder.put("shopId", shopId + "");
            }
            HttpEntity<Object> body = builder
                    .put("startDateStr", startDateStr)
                    .put("endDateStr", endDateStr)
                    .put("shopType", CommonConstants.POP_TYPE)
                    .toRequestEntity();
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());
            apiResponse = dispatchingRestTemplate.postRest(serviceId, PULL_FAILED_HISTORY_ORDER, body);
        } catch (HttpClientErrorException e) {
            e.printStackTrace();
            throw e;
        }
        return apiResponse;
    }

}
