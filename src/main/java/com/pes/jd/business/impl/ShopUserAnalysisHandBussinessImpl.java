package com.pes.jd.business.impl;

import com.pes.jd.business.ShopUserAnalysisHandBussiness;
import com.pes.jd.business.ShopUserBussiness;
import com.pes.jd.dao.PresaleActivityDao;
import com.pes.jd.dao.ReserveActivityDao;
import com.pes.jd.dao.ShopOvDayDao;
import com.pes.jd.dao.ShopUseAnalysisDao;
import com.pes.jd.model.DO.ShopUseAnalysisDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopDayOverviewDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.ms.domain.Data.job.ShopLoginInfo;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.ms.domain.Data.rtsub.ShopUseConversion;
import com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis;
import com.pes.jd.ms.domain.Result.task.dispatching.ShopUserLoginResult;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月06 15:20:20<br>
 */
@Service
public class ShopUserAnalysisHandBussinessImpl implements ShopUserAnalysisHandBussiness {
    private static Logger logger = LoggerFactory.getLogger(ShopUserAnalysisHandBussinessImpl.class);
   @Resource
   private ShopUserBussiness shopUserBussiness;
   @Resource
   private ShopOvDayDao shopOvDayDao;
   @Resource
   private ShopUseAnalysisDao shopUseAnalysisDao;

   @Resource
   private PresaleActivityDao presaleActivityDao;
    @Resource
    private ReserveActivityDao reserveActivityDao;

    @Override
    public void handShopUserAnalysis(JobShopDTO shop, JobDateQuery jobDate, boolean isDelData) throws Exception {
//        int a = 1/0;
        long s1=System.currentTimeMillis();
        logger.info("startDate:{},endDate:{} shopId:{} handShopUserAnalysis start",jobDate.getStartDate(),jobDate.getEndDate(),shop.getShopId());
        int deleteNum=0;
        int insertNum=0;
        Date startDate=jobDate.getStartDate();
        Date  endDate=jobDate.getEndDate();

        //获取登录天数&订购天数的数据
        ShopUserLoginResult userLoginResult= shopUserBussiness.getShopLoginSubscribe(shop.getShopId(), startDate,endDate);
        ShopLoginInfo loginInfo=null;
        List<ShopSubScribe> shopSubScribeLst=null;
        if(userLoginResult.getLoginInfo()!=null){
           loginInfo= userLoginResult.getLoginInfo();
           shopSubScribeLst=userLoginResult.getShopSubScribeLst();
        }
        //获取店铺销售
        ShopDayOverviewDTO shopDay= shopOvDayDao.selectShopShopDayOverviewByShopIdAndDate(shop,jobDate.getDate());
        //获取催付信息
        List<ShopUseConversion> shopUserConverLst=  shopUserBussiness.selectShopUseConverAnalysis(shop,startDate,endDate);
        ShopUseConversion shopUserCon=new ShopUseConversion();
        if(CollectionUtils.isNotEmpty(shopUserConverLst)){
            shopUserCon=shopUserConverLst.get(0);
        }
        ShopUseAnalysisDO shopUser=new ShopUseAnalysisDO();
        shopUser.setShopId(shop.getShopId());
        shopUser.setDate(jobDate.getDate());
        //有效订购天数
        shopUser.setScreenValidSubscribeNum(isInOrderDate(shopSubScribeLst,startDate,endDate));


        if(shopUser.getScreenValidSubscribeNum() == 0) return;

        //汇总整合数据
        packageShopUserAnalysis(shopUser,loginInfo,shopDay,shopUserCon);

        if(isDelData){
            deleteNum =  shopUseAnalysisDao.deleteShopUseAnalysisByshopIdByDate(shop,jobDate.getDate());
        }
         insertNum=  shopUseAnalysisDao.insertShopUseAnalysis(shop,jobDate.getDate(),shopUser);

        //更新数据
        calShopUseAmalysis(shop,jobDate);
        logger.info("startDate:{},endDate:{}, shopId:{} handShopUserAnalysis end insertNum:{} ,deleteNum:{},time:{}ms",jobDate.getStartDate(),jobDate.getEndDate(),shop.getShopId(),insertNum,deleteNum,System.currentTimeMillis()-s1);
    }

    private void calShopUseAmalysis(JobShopDTO shop, JobDateQuery jobDate) throws Exception {
        Date sDate=DateUtils.getStartTimeOfDate(jobDate.getDate());
        Date eDate=DateUtils.getEndTimeOfDate(jobDate.getDate());
        Integer preCount=   presaleActivityDao.selectCountByShopIdAndDate(shop,sDate,eDate);
        Integer reserveCount= reserveActivityDao.selectCountByShopIdAndDate(shop,sDate,eDate);
        //普通类型默认2天
        int day=2;
        //简单做存在活动直接往前推48天更新催付相关数
        if(preCount>0||reserveCount>0){
            logger.info("【{}】 date；{} has activity ",shop.getTitle(),jobDate.getDate());
            day=48;
        }
        int num=0;
        Date bDate=DateUtils.getDateByPeriod(jobDate.getDate(),0-day);
        List<Date> dates=DateUtils.splitDate(bDate,jobDate.getDate());
        for (Date date : dates) {
            Date startDate=DateUtils.getStartTimeOfDate(date);
            Date  endDate=DateUtils.getEndTimeOfDate(date);
            ShopUseAnalysis su = shopUseAnalysisDao.selectShopUseAnalysisByShopIdByDate(shop,date);
            if(su==null){
                logger.info("【{}】 date；{} shopUser is empty not update ",shop.getTitle(),date);
                continue ;
            }
            //获取店铺销售
            ShopDayOverviewDTO shopDay= shopOvDayDao.selectShopShopDayOverviewByShopIdAndDate(shop,date);
            //获取催付信息
            List<ShopUseConversion> shopUserConverLst=  shopUserBussiness.selectShopUseConverAnalysis(shop,startDate,endDate);
            ShopUseConversion shopUserCon=new ShopUseConversion();
            if(CollectionUtils.isNotEmpty(shopUserConverLst)){
                shopUserCon=shopUserConverLst.get(0);
            }
            ShopUseAnalysisDO shopUser=new ShopUseAnalysisDO();
            shopUser.setShopId(shop.getShopId());
            shopUser.setDate(date);
            packShopUrgeInfo(shopUser,shopDay,shopUserCon);

            num+= shopUseAnalysisDao.updateShopUseShopUrgeByShopIdByDate(shop,date,shopUser);
        }
        logger.info("startDate:{},endDate:{}, shopId:{} calShopUseAmalysis end updateNum：{}",bDate,jobDate.getDate(),shop.getShopId(),num);
    }

    /**
     * 重算催付数据已经有效的订购天数
     * @param shop
     * @param jobDate
     * @throws Exception
     */
    @Override
    public void calShopUserAnalysis(JobShopDTO shop, JobDateQuery jobDate) throws Exception{
        long s1=System.currentTimeMillis();
        Date sDate=DateUtils.getStartTimeOfDate(jobDate.getDate());
        Date eDate=DateUtils.getEndTimeOfDate(jobDate.getDate());
        int day=2;
        Integer preCount=   presaleActivityDao.selectCountByShopIdAndDate(shop,sDate,eDate);
        Integer reserveCount= reserveActivityDao.selectCountByShopIdAndDate(shop,sDate,eDate);
        if(preCount>0||reserveCount>0){
            logger.info("【{}】 date；{} has activity ",shop.getTitle(),jobDate.getDate());
            day=48;
        }
        int num=0;
        Date bDate=DateUtils.getDateByPeriod(jobDate.getDate(),0-day);
        List<Date> dates=DateUtils.splitDate(bDate,jobDate.getDate());
        for (Date date : dates) {
            Date startDate=DateUtils.getStartTimeOfDate(date);
            Date  endDate=DateUtils.getEndTimeOfDate(date);
            ShopUseAnalysis su = shopUseAnalysisDao.selectShopUseAnalysisByShopIdByDate(shop,date);
            if(su==null){
                logger.info("【{}】 date；{} shopUser is empty not update ",shop.getTitle(),date);
                continue ;
            }
            //获取店铺销售
            ShopDayOverviewDTO shopDay= shopOvDayDao.selectShopShopDayOverviewByShopIdAndDate(shop,date);
            //获取催付信息
            List<ShopUseConversion> shopUserConverLst=  shopUserBussiness.selectShopUseConverAnalysis(shop,startDate,endDate);
            ShopUseConversion shopUserCon=new ShopUseConversion();
            if(CollectionUtils.isNotEmpty(shopUserConverLst)){
                shopUserCon=shopUserConverLst.get(0);
            }
            ShopUseAnalysisDO shopUser=new ShopUseAnalysisDO();
            shopUser.setShopId(shop.getShopId());
            shopUser.setDate(date);
            packShopUrgeInfo(shopUser,shopDay,shopUserCon);

            num+= shopUseAnalysisDao.updateShopUseShopUrgeByShopIdByDate(shop,date,shopUser);
        }
        logger.info("date:{}, shopId:{} calShopUserAnalysis end updateNum：{} time:{}ms",jobDate.getDate(),shop.getShopId(),num,(System.currentTimeMillis()-s1));
    }
    private void packShopUrgeInfo(ShopUseAnalysisDO sa,ShopDayOverviewDTO shopDay,ShopUseConversion shopUserCon){
        //店铺总销售额
        Double shopAllSaleAmount=0.0;
        //普通店铺销售额
        Double shopSaleAmount=0.0;
        Double reserveShopSaleAmount=0.0;
        Double presaleShopSaleAmount=0.0;
        if(shopDay!=null){
            shopAllSaleAmount=shopDay.getSaleAmount()==null?0.0:shopDay.getSaleAmount();
            reserveShopSaleAmount=shopDay.getSaleAmountPreordain()==null?0.0:shopDay.getSaleAmountPreordain();
            presaleShopSaleAmount=shopDay.getSaleAmountPresale()==null?0.0:shopDay.getSaleAmountPresale();
            shopSaleAmount= Math.max((shopAllSaleAmount - reserveShopSaleAmount - presaleShopSaleAmount), 0.0);
        }
        Integer oneStepUserNum=0;
        Integer oneStpeExecuteNum=0;
        Integer oneStepAllocatedNum=0;
        Double oneStepUrgeAmount=0.0;
        Integer batchRemindUserNum=0;
        Integer batchRemindExecuteNum=0;
        Integer batchRemindAllocatedNum=0;
        Double batchRemindUrgeAmount=0.0;
        Integer smsUseDayNum=0;
        Double batchUrgeOrderAmount=0.0;
        Double smsUrgeAmount=0.0;
        Integer smsSendSuccessNum=0;
        Integer smsSendValidNum=0;
        Double auotUrgeAmount=0.0;
        Integer allAllocateNum=0;
        Integer allExecuteNum=0;

        Double reserveBatchRemindUrgeAmount=0.0;
        Double presaleBatchRemindUrgeAmount=0.0;

        Double reserveBatchUrgeOrderAmount=0.0;
        Double presaleBatchUrgeOrderAmount=0.0;

        Integer reserveBatchRemindAllocatedNum=0;
        Integer reserveBatchRemindExecuteNum=0;
        Integer presaleBatchRemindAllocatedNum=0;
        Integer presaleBatchRemindExecuteNum=0;

        Integer reserveAllAllocateNum=0;
        Integer reserveAllExecutedNum=0;
        Integer presaleAllAllocateNum=0;
        Integer presaleAllExecutedNum=0;

        Double reserveAuotUrgeAmount=0.0;
        Double presaleAuotUrgeAmount=0.0;

        Integer reserveBatchRemindUserNum=0;
        Integer presaleatchRemindUserNum=0;

        Double sumUrgeAmount=0.0;
        Double reserveSumUrgeAmount=0.0;
        Double presaleSumUrgeAmount=0.0;


         Double reserveOneStepUrgeAmount=0.0;
         Double presaleOneStepUrgeAmount=0.0;
        if(shopUserCon!=null){
            oneStepUserNum=shopUserCon.getOneStepUserUrgeDay()==null?0:shopUserCon.getOneStepUserUrgeDay();
            oneStpeExecuteNum=shopUserCon.getOneStepExecutetNum()==null?0:shopUserCon.getOneStepExecutetNum();
            oneStepAllocatedNum=shopUserCon.getOneStepAllocateNum()==null?0:shopUserCon.getOneStepAllocateNum();
            oneStepUrgeAmount=shopUserCon.getOneStepUrgeAmount()==null?0.0:shopUserCon.getOneStepUrgeAmount();

            batchRemindUserNum=shopUserCon.getBatchUserUrgeDay()==null?0:shopUserCon.getBatchUserUrgeDay();
            batchRemindExecuteNum=shopUserCon.getBatchExecuteNum()==null?0:shopUserCon.getBatchExecuteNum();
            batchRemindAllocatedNum=shopUserCon.getBatchAllocateNum()==null?0:shopUserCon.getBatchAllocateNum();
            batchRemindUrgeAmount=shopUserCon.getBatchUrgeAmount()==null?0:shopUserCon.getBatchUrgeAmount();

            smsUseDayNum=shopUserCon.getSmsUseUrgeDayNum()==null?0:shopUserCon.getSmsUseUrgeDayNum();
            smsSendSuccessNum=shopUserCon.getSmsSendSuccessNum()==null?0:shopUserCon.getSmsSendSuccessNum();
            smsSendValidNum=shopUserCon.getSmsSendValidNum()==null?0:shopUserCon.getSmsSendValidNum();
            smsUrgeAmount=shopUserCon.getSmsUrgeAmount()==null?0.0:shopUserCon.getSmsUrgeAmount();

            batchUrgeOrderAmount=shopUserCon.getBatchUrgeOrderAmount()==null?0.0:shopUserCon.getBatchUrgeOrderAmount();
            auotUrgeAmount=shopUserCon.getAuotUrgeAmount()==null?0.0:shopUserCon.getAuotUrgeAmount();
            allAllocateNum=shopUserCon.getAllAllocateNum()==null?0:shopUserCon.getAllAllocateNum();
            allExecuteNum=shopUserCon.getAllExecutedNum()==null?0:shopUserCon.getAllExecutedNum();
            sumUrgeAmount=shopUserCon.getSumUrgeAmount()==null?0.0:shopUserCon.getSumUrgeAmount();

            reserveAllAllocateNum=shopUserCon.getReserveAllAllocateConvsNum()==null?0:shopUserCon.getReserveAllAllocateConvsNum();
            reserveAllExecutedNum=shopUserCon.getReserveAllExecutedConvsNum()==null?0:shopUserCon.getReserveAllExecutedConvsNum();
            reserveAuotUrgeAmount=shopUserCon.getReserveAuotUrgeAmount()==null?0.0:shopUserCon.getReserveAuotUrgeAmount();
            reserveBatchRemindAllocatedNum=shopUserCon.getReserveBatchAllocatedConvsNum()==null?0:shopUserCon.getReserveBatchAllocatedConvsNum();
            reserveBatchRemindExecuteNum=shopUserCon.getReserveBatchExecutedConvsNum()==null?0:shopUserCon.getReserveBatchExecutedConvsNum();
            reserveBatchRemindUrgeAmount=shopUserCon.getReserveBatchUrgeAmount()==null?0.0:shopUserCon.getReserveBatchUrgeAmount();
            reserveBatchRemindUserNum=shopUserCon.getReserveBatchUserUrgeDayNum()==null?0:shopUserCon.getReserveBatchUserUrgeDayNum();
            reserveBatchUrgeOrderAmount=shopUserCon.getReserveBatchUrgeOrderAmount()==null?0.0:shopUserCon.getReserveBatchUrgeOrderAmount();
            reserveSumUrgeAmount=shopUserCon.getReserveSumUrgeAmount()==null?0.0:shopUserCon.getReserveSumUrgeAmount();
            reserveOneStepUrgeAmount=shopUserCon.getReserveOneStepUrgeAmount()==null?0.0:shopUserCon.getReserveOneStepUrgeAmount();

            presaleAllAllocateNum=shopUserCon.getPresaleAllAllocateConvsNum()==null?0:shopUserCon.getPresaleAllAllocateConvsNum();
            presaleAllExecutedNum=shopUserCon.getPresaleAllExecutedConvsNum()==null?0:shopUserCon.getPresaleAllExecutedConvsNum();
            presaleatchRemindUserNum=shopUserCon.getPresaleBatchUserUrgeDayNum()==null?0:shopUserCon.getPresaleBatchUserUrgeDayNum();
            presaleAuotUrgeAmount=shopUserCon.getPresaleAuotUrgeAmount()==null?0.0:shopUserCon.getPresaleAuotUrgeAmount();
            presaleBatchRemindAllocatedNum=shopUserCon.getPresaleBatchAllocatedConvsNum()==null?0:shopUserCon.getPresaleBatchAllocatedConvsNum();
            presaleBatchRemindExecuteNum=shopUserCon.getPresaleBatchExecutedConvsNum()==null?0:shopUserCon.getPresaleBatchExecutedConvsNum();
            presaleBatchRemindUrgeAmount=shopUserCon.getPresaleBatchUrgeAmount()==null?0.0:shopUserCon.getPresaleBatchUrgeAmount();
            presaleBatchUrgeOrderAmount=shopUserCon.getPresaleBatchUrgeOrderAmount()==null?0.0:shopUserCon.getPresaleBatchUrgeOrderAmount();
            presaleSumUrgeAmount=shopUserCon.getPresaleSumUrgeAmount()==null?0.0:shopUserCon.getPresaleSumUrgeAmount();
            presaleOneStepUrgeAmount=shopUserCon.getPresaleOneStepUrgeAmount()==null?0.0:shopUserCon.getPresaleOneStepUrgeAmount();

        }
        sa.setBatchRemindAllocatedNum(batchRemindAllocatedNum);
        sa.setBatchRemindExecuteNum(batchRemindExecuteNum);
        sa.setBatchRemindUrgeAmount(batchRemindUrgeAmount);
        sa.setBatchRemindUserNum(batchRemindUserNum);

        sa.setOneStepAllocatedNum(oneStepAllocatedNum);
        sa.setOneStepExecuteNum(oneStpeExecuteNum);
        sa.setOneStepUrgeAmount(oneStepUrgeAmount);
        sa.setOneStepUserNum(oneStepUserNum);



        sa.setSmsUrgeAmount(smsUrgeAmount);
        sa.setSmsUseNum(smsUseDayNum);
        sa.setSmsSendSuccessNum(smsSendSuccessNum);
        sa.setSmsSendValidNum(smsSendValidNum);
        sa.setBatchUrgeOrderAmount(batchUrgeOrderAmount);
        sa.setAutoUrgeAmount(auotUrgeAmount);
        sa.setAllAllocateNum(allAllocateNum);
        sa.setAllExecutedNum(allExecuteNum);
        sa.setSumUrgeAmount(sumUrgeAmount);

        sa.setReserveAllAllocateNum(reserveAllAllocateNum);
        sa.setReserveAllExecutedNum(reserveAllExecutedNum);
        sa.setReserveAuotUrgeAmount(reserveAuotUrgeAmount);
        sa.setReserveBatchRemindAllocatedNum(reserveBatchRemindAllocatedNum);
        sa.setReserveBatchRemindExecuteNum(reserveBatchRemindExecuteNum);
        sa.setReserveBatchRemindUrgeAmount(reserveBatchRemindUrgeAmount);
        sa.setReserveBatchUrgeOrderAmount(reserveBatchUrgeOrderAmount);
        sa.setReserveBatchRemindUserNum(reserveBatchRemindUserNum);
        sa.setReserveSumUrgeAmount(reserveSumUrgeAmount);

        sa.setPresalAllExecutedNum(presaleAllExecutedNum);
        sa.setPresaleAllAllocateNum(presaleAllAllocateNum);
        sa.setPresaleAuotUrgeAmount(presaleAuotUrgeAmount);
        sa.setPresaleBatchRemindAllocatedNum(presaleBatchRemindAllocatedNum);
        sa.setPresaleBatchRemindExecuteNum(presaleBatchRemindExecuteNum);
        sa.setPresaleBatchRemindUrgeAmount(presaleBatchRemindUrgeAmount);
        sa.setPresaleBatchUrgeOrderAmount(presaleBatchUrgeOrderAmount);
        sa.setPresaleBatchRemindUserNum(presaleatchRemindUserNum);
        sa.setPresaleSumUrgeAmount(presaleSumUrgeAmount);

        sa.setShopSaleAmount(shopSaleAmount);
        sa.setReserveShopSaleAmount(reserveShopSaleAmount);
        sa.setPresaleShopSaleAmount(presaleShopSaleAmount);

        sa.setPresaleOneStepUrgeAmount(presaleOneStepUrgeAmount);
        sa.setReserveOneStepUrgeAmount(reserveOneStepUrgeAmount);

    }
    private void packageShopUserAnalysis(ShopUseAnalysisDO sa, ShopLoginInfo loginInfo,ShopDayOverviewDTO shopDay,ShopUseConversion shopUserCon){
        Integer loginNum=0;
        Integer loginUseNum=0;

        if(loginInfo!=null&&loginInfo.getNum()!=null){
            loginNum=loginInfo.getNum();
        }
        //封装带转化池催付数据和销售额
        packShopUrgeInfo(sa,shopDay,shopUserCon);

        if(loginNum>0||sa.getOneStepUserNum()>0||sa.getBatchRemindUserNum()>0||sa.getSmsUseNum()>0||sa.getReserveBatchRemindUserNum()>0||sa.getPresaleBatchRemindUserNum()>0){
            loginUseNum++;
        }
        sa.setLoginUseNum(loginUseNum);
        sa.setLoginNum(loginNum);

    }

//    public static void main(String[] args) throws ParseException {
//        ArrayList<ShopSubScribe> shopSubscrLst = new ArrayList<>();
//        ShopSubScribe shopSubScribe1 = new ShopSubScribe();
//        ShopSubScribe shopSubScribe2 = new ShopSubScribe();
//        shopSubscrLst.add(shopSubScribe1);
//        shopSubscrLst.add(shopSubScribe2);
//        shopSubScribe1.setOrderStatus(4);
//        shopSubScribe2.setOrderStatus(4);
//        shopSubScribe1.setStartTime(DateFormatUtils.parseYMdHms("2019-11-27 11:02:35"));
//        shopSubScribe1.setEndTime(DateFormatUtils.parseYMdHms("2020-11-21 11:02:35"));
//
//        shopSubScribe2.setStartTime(DateFormatUtils.parseYMdHms("2019-05-30 18:08:38"));
//        shopSubScribe2.setEndTime(DateFormatUtils.parseYMdHms("2019-11-26 18:08:38"));
//        Date startTimeOfDate = DateFormatUtils.getStartTimeOfDate(DateFormatUtils.parseYMd("2019-11-26"));
//        Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(DateFormatUtils.parseYMd("2019-11-26"));
////        System.out.println(getScreenValidSubscribeDay(shopSubscrLst, startTimeOfDate, endTimeOfDate));
//        System.out.println(isInOrderDate(shopSubscrLst, startTimeOfDate, endTimeOfDate));
//    }

    private Integer getScreenValidSubscribeDay(List<ShopSubScribe> shopSubscrLst,Date startDate,Date endDate){
        Integer screenValidSubscribeDay=0;
       if(CollectionUtils.isEmpty(shopSubscrLst)){
           return screenValidSubscribeDay;
       }
        List<ShopSubScribe> validSubscribeLst = shopSubscrLst.stream().filter(c -> c.getOrderStatus() != null && c.getOrderStatus() == 4).collect(Collectors.toList());
        validSubscribeLst.sort(Comparator.comparing(ShopSubScribe::getStartTime, Comparator.nullsFirst(Date::compareTo)));
        //订购时间排序
        boolean firstFlag = true;
        boolean nextFlag = false;
        boolean resultFlag = false;
        Date firstDate = null;
        Date lastDate = null;
        int count = 0;
        for (int i = 0; i < validSubscribeLst.size(); i++) {
            ShopSubScribe sub = validSubscribeLst.get(i);
            if (endDate.compareTo(sub.getStartTime()) < 0) {
                continue;
            }
            if (startDate.compareTo(sub.getEndTime()) > 0) {
                continue;
            }
            //开始时间小于订购时间
            if (sub.getStartTime().compareTo(startDate) >= 0) {
                if (firstFlag) {
                    firstDate = sub.getStartTime();
                    firstFlag = false;
                }
                //开始时间大于订购时间
            } else if (sub.getStartTime().compareTo(startDate) <= 0) {
                if (firstFlag) {
                    firstDate = startDate;
                    firstFlag = false;
                }
            }
            if (endDate.compareTo(sub.getEndTime()) <= 0) {
                if (count < 1) {
                    lastDate = endDate;
                    nextFlag = false;
                    resultFlag = true;
                    count++;
                }
            } else if (endDate.compareTo(sub.getEndTime()) >= 0) {
                lastDate = sub.getEndTime();
                nextFlag = true;
                if (i == validSubscribeLst.size() - 1) {
                    nextFlag = false;
                    resultFlag = true;
                }
            }
            if (i < validSubscribeLst.size() - 1) {
                int day = subscribeDay(validSubscribeLst.get(i).getEndTime(), validSubscribeLst.get(i + 1).getStartTime());
                if (day > 0) {
                    //表示有间隔，先累计前一段的,还要继续
                    nextFlag = false;
                    //resultFlag = true;
                    //if (resultFlag && !nextFlag) {
                    screenValidSubscribeDay += DateUtils.splitDate(firstDate, lastDate).size();
                    firstDate = validSubscribeLst.get(i + 1).getStartTime();
                    resultFlag = false;
                    //}

                }

            }

            if (resultFlag && !nextFlag) {
                screenValidSubscribeDay += DateUtils.splitDate(firstDate, lastDate).size();
                resultFlag = false;
            }
        }
        return screenValidSubscribeDay;
    }

    //判断计算日是否在订购日期之内
    private Integer isInOrderDate(List<ShopSubScribe> shopSubscrLst,Date startDate,Date endDate) {
        Integer screenValidSubscribeDay = 0;
        if (CollectionUtils.isEmpty(shopSubscrLst)) {
            return screenValidSubscribeDay;
        }
        //需要校验是否是退款时间内，需求如此。(*^▽^*)
        List<ShopSubScribe> validSubscribeLst = shopSubscrLst.stream().filter(c -> c.getOrderStatus() != null
                && (c.getOrderStatus() == 4||c.getOrderStatus() == 16||c.getOrderStatus() == 64))
                .collect(Collectors.toList());
        validSubscribeLst.sort(Comparator.comparing(ShopSubScribe::getStartTime, Comparator.nullsFirst(Date::compareTo)));
        Date startTimeOfDate;
        Date endTimeOfDate;

        for (ShopSubScribe shopSubScribe : validSubscribeLst) {
            Optional<Date> orderStartTime = Optional.ofNullable(shopSubScribe.getStartTime());
            Optional<Date> orderEndTime = Optional.ofNullable(shopSubScribe.getEndTime());
            if (orderStartTime.isPresent() && orderEndTime.isPresent()) {
                startTimeOfDate = DateUtils.getStartTimeOfDate(orderStartTime.get());
                endTimeOfDate = DateUtils.getEndTimeOfDate(orderEndTime.get());
                if (startDate.compareTo(startTimeOfDate) >= 0 && endDate.compareTo(endTimeOfDate) <= 0) {
                    screenValidSubscribeDay = 1;
                    break;
                }
            }

        }
        return screenValidSubscribeDay;
    }



    public static int subscribeDay(Date startDate, Date endDate) {
        int subScribeDay = 0;
        if (startDate == null || endDate == null) {
            return subScribeDay;
        }
        return subScribeDay = (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 反向更新店铺使用分析的店铺销售额和所选时间段有效地订购日期
     * @param shopQuery
     * @param jobDate
     */
    @Override
    public void updateShopUseAnalysisShopSale(JobShopQuery shopQuery,JobDateQuery jobDate) {
        JobShopDTO jobShop=shopQuery.getShop();
        List<Date> dateLst= jobDate.getDates();
        if(CollectionUtils.isEmpty(dateLst)){
            return;
        }
        int num=0;
        try {
            //获取订购天数的数据
            List<ShopSubScribe> shopSubScribeLst= shopUserBussiness.selectShopSubscribe(jobShop.getShopId());
            Integer screenValidNum=isInOrderDate(shopSubScribeLst,jobDate.getStartDate(),jobDate.getEndDate());
            for (Date date : dateLst) {
                ShopUseAnalysis shopUser = shopUseAnalysisDao.selectShopUseAnalysisByShopIdByDate(jobShop,date);
                if(shopUser==null){
                    if(logger.isDebugEnabled()){
                        logger.debug("【{}】 date:{} shopUser is empty not update ",jobShop.getTitle(),date);
                    }
                    continue ;
                }
                //获取店铺销售额
                Double shopSaleAmount=0.0;
                Double presaleShopSaleAmount=0.0;
                Double reserveShopSaleAmount=0.0;
                ShopDayOverviewDTO shopDay= shopOvDayDao.selectShopShopDayOverviewByShopIdAndDate(jobShop,date);
                if(shopDay!=null){
                    shopSaleAmount=Math.max(shopDay.getSaleAmount()-shopDay.getSaleAmountPresale()-shopDay.getSaleAmountPreordain(),0.0);
                    presaleShopSaleAmount=shopDay.getSaleAmountPresale();
                    reserveShopSaleAmount=shopDay.getSaleAmountPreordain();
                }
                ShopUseAnalysisDO su=new ShopUseAnalysisDO();
                su.setShopId(jobShop.getShopId());
                su.setDate(date);
                su.setShopSaleAmount(shopSaleAmount);
                su.setPresaleShopSaleAmount(presaleShopSaleAmount);
                su.setReserveShopSaleAmount(reserveShopSaleAmount);
                if(org.apache.commons.lang3.time.DateUtils.isSameDay(date,jobDate.getDate())){
                    su.setScreenValidSubscribeNum(screenValidNum);
                    num += shopUseAnalysisDao.updateShopUseShopUrgeByShopIdByDate(jobShop,date,su);
                }else{
                    num += shopUseAnalysisDao.updateShopUseShopUrgeByShopIdByDate(jobShop,date,su);
                }
            }
            logger.info("job date:{} 【{}】 updateNum:{} update 询单有效期内 shop-use-analysis ",DateUtils.formatYMd(jobDate.getDate()),jobShop.getTitle(),num);
        } catch (Exception e) {
            logger.info("job date:{} 【{}】  update shop-use-analysis error:{}",DateUtils.formatYMd(jobDate.getDate()),jobShop.getTitle(),e.getMessage(),e);
        }


    }
}
