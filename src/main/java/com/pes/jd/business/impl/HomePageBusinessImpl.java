package com.pes.jd.business.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.HomePageBusiness;
import com.pes.jd.model.DTO.ConversionSummaryDTO;
import com.pes.jd.model.DTO.ShopTeamOrderPerformanceConciseDTO;
import com.pes.jd.model.DTO.TaskNumDTO;
import com.pes.jd.model.DTO.UpSaleDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.BatchRemindSetting;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.pes.jd.rest.PopSubRestTemplate;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.redis.RedisCache;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/29 4:24 PM
 * @since 1.0.0
 */
@Service
public class HomePageBusinessImpl implements HomePageBusiness {

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;
    @Autowired
    private PopRtSubRestTemplate popRtSubRestTemplate;
    @Autowired
    private PopSubRestTemplate popSubRestTemplate;
    @Autowired
    private RedisCache redisCache;

    private Integer redisDBNum = 2;
    @Override
    public ApiResponse searchAllWithCategory(ShopQuery shop, String name){
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId,RestOperator.mergeUriArguments("/home/<USER>/report/base-info",
                "name",name,
                "shopId",shop.getShopId().toString()
        ), RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ApiResponse searchAllByShopId(ShopQuery shop, Integer type) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate
                .postRest(serviceId, RestOperator.mergeUriArguments("/home/<USER>/report/info",
                        "shopId",shop.getShopId().toString(),"type",String.valueOf(type)),RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ApiResponse insertOrUpdateCustomReport(ShopQuery shop, String report, Integer type,String nick){
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate
                .postRest(serviceId,
                        RestOperator.mergeUriArguments("/home/<USER>/report/insert-or-update",
                                "type",type.toString(),
                                "nick",nick
                        ),
                        RestOperator.getJsonEntity(report)
                );
        return apiResponse;
    }

    @Override
    public ApiResponse deleteCustomReport(ShopQuery shop,Long id) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate
                .postRest(serviceId, RestOperator.mergeUriArguments("/home/<USER>/report/delete",
                        "id",id.toString()),RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ApiResponse insertCustomReport(String fieldsStr){
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate
                .postRest(serviceId, RestOperator.mergeUriArguments("/home/<USER>/report/insertField",
                        "fieldsStr",fieldsStr),RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public List<UpSaleDTO> querySaleAmount(ShopQuery shopQuery, String startDateStr, String endDateStr) {
        List<UpSaleDTO> upSaleDTO = null;
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getRtSchemaId(), shopQuery.getRtDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shopQuery.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
        RestResponseTypeRef<List<UpSaleDTO>> restResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/querySaleAmount", body, new ParameterizedTypeReference<RestResponseTypeRef<List<UpSaleDTO>>>() {
        });
        if (restResponse.getSuccess()) {
            upSaleDTO = restResponse.getData();
        } else {
            upSaleDTO = Lists.newArrayList();
        }

        return upSaleDTO;
    }

    @Override
    public TaskNumDTO queryTaskNum(ShopQuery shopQuery, String startDateStr, String endDateStr) {
        TaskNumDTO taskNumDTO = null;
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getRtSchemaId(), shopQuery.getRtDbName());
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopCommonParam)
                .put("startDateStr", startDateStr)
                .put("endDateStr", endDateStr)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shopQuery.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
        RestResponseTypeRef<TaskNumDTO> restResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/queryTaskNum", body, new ParameterizedTypeReference<RestResponseTypeRef<TaskNumDTO>>() {
        });
        if (restResponse.getSuccess()) {
            taskNumDTO = restResponse.getData();
        } else {
            taskNumDTO = new TaskNumDTO();
            taskNumDTO.setConversionTaskNum(0);
            taskNumDTO.setAllocationTaskNum(0);
            taskNumDTO.setRemindTaskNum(0);
            taskNumDTO.setSuccessTaskNum(0);
        }

        return taskNumDTO;
    }

    @Override
    public ApiResponse queryMenuByShopId(String shopId) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId,RestOperator.mergeUriArguments("/home/<USER>/showMenu",
                "shopId",shopId
        ), RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ApiResponse queryMenuByShopIdOfSimple(String shopId) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId,RestOperator.mergeUriArguments("/home/<USER>/showMenuSimple",
                "shopId",shopId
        ), RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ApiResponse updateShopMenuHome(String shopId, String ids) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/home/<USER>/updateMenu",
                "shopId", shopId,
                "ids", ids
        ), RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ApiResponse updateShopMenuHomeOfSimple(String shopId, String ids) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId, RestOperator.mergeUriArguments("/home/<USER>/updateMenuSimple",
                "shopId", shopId,
                "ids", ids
        ), RequestEntityBuilder.builder().toRequestEntity());
        return apiResponse;
    }

    @Override
    public ConversionSummaryDTO searchConversionSummary(ShopQuery shopQuery, String date) {
        boolean isRemind = judgeIsRemind(shopQuery.getShopId());

        ConversionSummaryDTO conversionSummaryDTO = new ConversionSummaryDTO();
        conversionSummaryDTO.setRemind(isRemind);
        if(isRemind){
            ShopCommonParam shopCommonParamRt = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getRtSchemaId(), shopQuery.getRtDbName());

            String serviceId = RestOperator.getMSServiceId(shopQuery.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
            HttpEntity<Object> bodyRt = RequestEntityBuilder.builder()
                    .put("shop", shopCommonParamRt)
                    .put("date", date)
                    .toRequestEntity();

            RestResponseTypeRef<ConversionSummaryDTO> restResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/conversionSummary", bodyRt, new ParameterizedTypeReference<RestResponseTypeRef<ConversionSummaryDTO>>() {
            });

            if(restResponse.getSuccess()){
                conversionSummaryDTO = restResponse.getData();
                conversionSummaryDTO.setRemind(true);
            }else{
                conversionSummaryDTO.setUpTaskPayment(0.0);
                conversionSummaryDTO.setSuccessRate(0.0);
                conversionSummaryDTO.setEffectiveExecTasks(0);
                conversionSummaryDTO.setSilentTasks(0);
                conversionSummaryDTO.setConsultTasks(0);
                conversionSummaryDTO.setSuccessTasks(0);
                conversionSummaryDTO.setSilentSuccessTasks(0);
                conversionSummaryDTO.setConsultSuccessTasks(0);
            }
        }else{
            ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());
            String subServiceId = RestOperator.getMSServiceId(shopQuery.getDbName(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("shop", shopCommonParam)
                    .put("date", date)
                    .toRequestEntity();
            RestResponseTypeRef<ShopTeamOrderPerformanceConciseDTO> doubleRestResponseTypeRef = popSubRestTemplate.postRest(subServiceId, "/shop/performance/selectShopSaleAmount", body, new ParameterizedTypeReference<RestResponseTypeRef<ShopTeamOrderPerformanceConciseDTO>>() {
            });
            if(doubleRestResponseTypeRef.getSuccess() && doubleRestResponseTypeRef.getData() != null){
                ShopTeamOrderPerformanceConciseDTO data = doubleRestResponseTypeRef.getData();
                conversionSummaryDTO.setNumberOfLostOrders(data.getNumberOfLostOrders());
                conversionSummaryDTO.setLostSales(data.getLostSales());
            }else{
                conversionSummaryDTO.setNumberOfLostOrders(0);
                conversionSummaryDTO.setLostSales(0);
            }
        }
        return conversionSummaryDTO;
    }

    private boolean judgeIsRemind(Long shopId) {
        String key = "job-batchremindsetting-"+shopId;
        String batchRemindSetting = redisCache.get(key, redisDBNum);
        if(StringUtils.isNotBlank(batchRemindSetting)){
            BatchRemindSetting setting = JSONObject.parseObject(batchRemindSetting, BatchRemindSetting.class);
            if(setting != null){
                ShopSettingBatchRemindDTO shopSettingBatchRemind = setting.getShopSettingBatchRemind();
                if(shopSettingBatchRemind != null && shopSettingBatchRemind.getIsRemind()){
                    return true;
                }else if(setting.getShopSettingBatchRemindCno() != null && setting.getShopSettingBatchRemindCno().getIsRemind()){
                    return true;
                }else if(setting.getShopSettingBatchRemindReserve() != null){
                    ShopSettingBatchRemindReserveDTO shopSettingBatchRemindReserve = setting.getShopSettingBatchRemindReserve();
                    if(shopSettingBatchRemindReserve.getIsReserveRemind() || shopSettingBatchRemindReserve.getIsRseUncRemind() || shopSettingBatchRemindReserve.getIsRseUnpRemind()) return true;
                }
                if(setting.getShopSettingBatchRemindPresale() != null){
                    ShopSettingBatchRemindPresaleDTO shopSettingBatchRemindPresale = setting.getShopSettingBatchRemindPresale();
                    return shopSettingBatchRemindPresale.getIsBalanceRemind() || shopSettingBatchRemindPresale.getIsBargainRemind() || shopSettingBatchRemindPresale.getIsUnpoRemind();
                }
            }else{
                return getBatchRemindSettingFromDb(shopId);
            }
        }else{
            return getBatchRemindSettingFromDb(shopId);
        }
        return false;
    }

    private boolean getBatchRemindSettingFromDb(Long shopId) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop", shopId)
                .toRequestEntity();
        ApiResponse apiResponse = usermgrRestTemplate.postRest(serviceId, "/shop/batch_remind/selectShopBatchRemindSettings", body);

        if(apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
            Object result = apiResponse.getData().get("result");
            return Boolean.parseBoolean(result.toString());
        }
        return false;
    }
}
