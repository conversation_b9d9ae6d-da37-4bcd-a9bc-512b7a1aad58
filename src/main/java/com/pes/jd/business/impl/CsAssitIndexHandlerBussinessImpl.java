package com.pes.jd.business.impl;

import com.pes.jd.business.CsAssitIndexHandlerBussiness;
import com.pes.jd.dao.CsAssitIndexDao;
import com.pes.jd.dao.CsOrderIndexDao;
import com.pes.jd.dao.ShopTeamAssitIndexDao;
import com.pes.jd.model.DO.CsAssitIndexDO;
import com.pes.jd.model.DO.ShopTeamAssitIndexDO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.CsOrderIndexForSearchDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Service
public class CsAssitIndexHandlerBussinessImpl implements CsAssitIndexHandlerBussiness {
    private static final Logger logger = LoggerFactory.getLogger(CsAssitIndexHandlerBussiness.class);
    @Resource
    private CsOrderIndexDao csOrderIndexDao;

    @Resource
    private CsAssitIndexDao csAssitIndexDao;

    @Resource
    private ShopTeamAssitIndexDao shopTeamAssitIndexDao;

    @Override
    public void handlerCsAssitIndex(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("handler CsAssitIndexHandlerBussinessImpl");
        List<Date> dates = jobDate.getCommonDates();
        if (dates.isEmpty()) {
            logger.error("req dates is empty");
            return;
        }
        JobShopDTO shop = jobShop.getShop();

        List<CsDTO> csLst = jobShop.getCsLst().stream()
                .filter(e -> e.getType() == 1)
                .collect(Collectors.toList());

        for (Date date : dates) {
            List<CsAssitIndexDO> forShop = new ArrayList<>();
            date = DateUtils.truncate(date, Calendar.DATE);
            final List<CsOrderIndexForSearchDTO> csOrderIndex = csOrderIndexDao.searchByDateShop(shop.getSchemaId(), shop.getShopId(),
                    date, com.pes.jd.util.DateUtils.getEndTimeOfDate(date));
            if (CollectionUtils.isEmpty(csOrderIndex)) {
                return;
            }
            final Map<String, List<CsOrderIndexForSearchDTO>> csOrderIndexMap =
                    csOrderIndex.stream().collect(Collectors.groupingBy(CsOrderIndexForSearchDTO::getCsNick));
            if (isDelData) {
                csAssitIndexDao.deleteByShopDate(date, shop.getShopId(), shop.getSchemaId());
            }
            for (CsDTO csDTO : csLst) {
                final List<CsOrderIndexForSearchDTO> csOrderIndexDTOS = csOrderIndexMap.get(csDTO.getNick());
                if (CollectionUtils.isEmpty(csOrderIndexDTOS)) {
                    continue;
                }
                CsAssitIndexDO csAssitIndexDO = new CsAssitIndexDO();
                csAssitIndexDO.setShopId(shop.getShopId());
                csAssitIndexDO.setDate(date);
                csAssitIndexDO.setCsNick(csDTO.getNick());
                csAssitIndexDO.setAssitOrderCreateNum(0);
                csAssitIndexDO.setAssitOrderCreateAmount(0.0D);
                csAssitIndexDO.setAssitOrderPayNum(0);
                csAssitIndexDO.setAssitOrderPayAmount(0.0D);
                csAssitIndexDO.setAssitOrderFollowupNum(0);
                csAssitIndexDO.setAssitOrderFollowupAmount(0.0D);


                Set<String> assitOrderCreateNum = new HashSet<>();
                Set<String> assitOrderPayNum = new HashSet<>();
                Set<String> assitOrderFollowupNum = new HashSet<>();
                for (CsOrderIndexForSearchDTO orderIndex : csOrderIndexDTOS) {
                    if (orderIndex.getIsAssitOrderCreate() != null && orderIndex.getIsAssitOrderCreate()) {
                        /*协助下单*/
                        assitOrderCreateNum.add(orderIndex.getBuyerNick());
                        csAssitIndexDO.setAssitOrderCreateAmount((csAssitIndexDO.getAssitOrderCreateAmount() == null?0:csAssitIndexDO.getAssitOrderCreateAmount()) + (orderIndex.getOrderPayment() == null?0:orderIndex.getOrderPayment()));
                    }
                    if (orderIndex.getIsAssitOrderCreate() != null && orderIndex.getIsAssitOrderPay()) {
                        /*协助付款*/
                        assitOrderPayNum.add(orderIndex.getBuyerNick());
                        csAssitIndexDO.setAssitOrderPayAmount((csAssitIndexDO.getAssitOrderPayAmount() == null?0:csAssitIndexDO.getAssitOrderPayAmount()) + (orderIndex.getOrderPayment() == null?0:orderIndex.getOrderPayment()));
                    }
                    if (orderIndex.getIsAssitOrderCreate() != null && orderIndex.getIsAssitOrderInFollowup()) {
                        /*协助跟进*/
                        assitOrderFollowupNum.add(orderIndex.getBuyerNick());
                        csAssitIndexDO.setAssitOrderFollowupAmount((csAssitIndexDO.getAssitOrderFollowupAmount()==null?0:csAssitIndexDO.getAssitOrderFollowupAmount()) + (orderIndex.getOrderPayment() == null?0:orderIndex.getOrderPayment()));
                    }
                }
                csAssitIndexDO.setAssitOrderCreateNum(assitOrderCreateNum.size());
                csAssitIndexDO.setAssitOrderPayNum(assitOrderPayNum.size());
                csAssitIndexDO.setAssitOrderFollowupNum(assitOrderFollowupNum.size());
                csAssitIndexDao.insert(csAssitIndexDO, shop.getSchemaId());
                forShop.add(csAssitIndexDO);
            }
            if (isDelData){
                shopTeamAssitIndexDao.deleteByDateShopId(shop.getShopId(),shop.getSchemaId(),date);
            }
            ShopTeamAssitIndexDO shopTeamAssitIndexDO = new ShopTeamAssitIndexDO();
            CommonUtils.computerAndCopy(forShop,shopTeamAssitIndexDO);
            shopTeamAssitIndexDO.setShopId(shop.getShopId());
            shopTeamAssitIndexDO.setDate(date);
            shopTeamAssitIndexDao.insert(shopTeamAssitIndexDO,shop.getSchemaId());
        }
        stopWatch.stop();
        if(logger.isDebugEnabled()){

            logger.debug("服务指标计算耗时：{}秒", TimeUnit.MILLISECONDS.toSeconds(stopWatch.getLastTaskTimeMillis()));
        }
    }
}
