package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.Constants.GoodFilterEnum;
import com.pes.jd.Constants.PesConstants;
import com.pes.jd.business.*;
import com.pes.jd.config.RedisOperator;
import com.pes.jd.dao.*;
import com.pes.jd.model.BO.ChatBO;
import com.pes.jd.model.BO.ChatRoundNumBO;
import com.pes.jd.model.BO.CsBuyerServiceIndexBO;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.OrderStatusEnum;
import com.pes.jd.model.Query.JobCsStatusQuery;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.*;
import com.yiyitech.support.task.AsyncTask;
import com.yiyitech.support.task.AsyncTaskUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.pes.jd.model.Enum.JudgeRuleTypeEnum.ORDER_BIND_TYPE_SILENTALL;
import static com.pes.jd.util.ServiceDateUtil.calNewOrderIndexDate;

/**
 * 接待规则/过滤
 */
@SuppressWarnings("Duplicates")
@Service
public class ChatHandleBusinessImpl implements ChatHandleBusiness {

    private static final Logger logger = LoggerFactory.getLogger(ChatHandleBusinessImpl.class);
    private static final String DELIMITER = ";;;";
    @Resource
    private CsChatpeerDao csChatpeerDao;

    @Resource
    private CsChatlogDao chatlogDao;

    @Resource
    private PerformanceRuleBusiness performanceRuleBusiness;

    @Resource
    private ShopWatchwordDao shopWatchwordDao;

    @Resource
    private LossOrderRecordDao lossOrderRecordDao;

    @Resource
    private CsOrderBindDao csOrderBindDao;

    @Resource
    private CsOrderIndexDao csOrderIndexDao;

    @Resource
    private OrderDao orderDao;

    @Resource
    private CsLossRecordDao csLossRecordDao;

    @Resource
    private LossEnquiryRecordDao lossEnquiryRecordDao;

    @Resource
    private RedisOperator redisOperator;

    @Resource
    private ShopTeamLossRecordDao shopTeamLossRecordDao;

    @Resource
    private CsChatSessionServiceIndexDao csChatSessionServiceIndexDao;

    @Resource
    private CsChatSessionDao csChatSessionDao;

    @Resource
    private ShopTeamSessionServiceIndexDao shopTeamSessionServiceIndexDao;

    @Resource
    private ReceiveSessionNumHourlyDao receiveSessionNumHourlyDao;

    @Resource
    private ReceiveSessionPressureDao receiveSessionPressureDao;

    @Resource
    private CsServiceSendEvalDao csServiceSendEvalDao;

    @Resource
    private CsServiceEvaluationDetailDao csServiceEvaluationDetailDao;

    @Resource
    private CsLeaveMsgDao csLeaveMsgDao;

    @Resource
    private CsServiceIndexDao csServiceIndexDao;

    @Resource
    private CsBuyerServiceIndexDao csBuyerServiceIndexDao;

    @Resource
    private PresaleOrderDao presaleOrderDao;

    @Resource
    private CsCustRecommendConsultSkuBusiness csCustRecommendConsultSkuBusiness;

    @Resource
    private GoodsHandleBusiness goodsHandleBusiness;

    /*指定接待压力计算是否在前后几分钟*/
    private static final int RECEIVE_FLAG_CAL = 2;

    /**
     * 过滤客服【主动联系买家】的 询单，如果该客户买家已经被落实，则算询单，否则在买家首次回复识别天数内不算询单流失
     *
     * @return
     */
    @Override
    public void handleCsConsultFirstToEnquiry(JobShopQuery jobShop, JobDateQuery jobDate,
                                              boolean isDelData) {


        JobShopDTO shop = jobShop.getShop();
        List<CsDTO> csLst = jobShop.getCsLst();

        List<Date> dates = jobDate.getCommonDates();
        if (CollectionUtils.isEmpty(dates)) {
            return;
        }
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Boolean enquiryLossSwitch = sys.getEnquiryLossSwitch();
        Integer csToCustLossValidTime = sys.getCsToCustLossValidTime();
        if (!enquiryLossSwitch) {
            return;
        }

        for (Date date : dates) {

            Date sDate = date;
            Date lossValidEndDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(date, csToCustLossValidTime));

            if (CollectionUtils.isEmpty(csLst)) {
                return;
            }


            String csNick;
            List<EnquiryChatpeerDTO> csFirstToConsultChatpeerLst;
            List<LossEnquiryRecordDTO> enquiryLossList = Lists.newArrayList();
            for (CsDTO cs : csLst) {
                csNick = cs.getNick();

                csFirstToConsultChatpeerLst = csChatpeerDao.selectShopCsFirstToConsultBuyerNickLst(shop, date, csNick);

                //落实下单Set
                Set<String> csFirstToConsultBuyerSet = Optional.ofNullable(csFirstToConsultChatpeerLst)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(EnquiryChatpeerDTO::getBuyerNick)
                        .collect(Collectors.toSet());
                List<String> csFirstToConsultBuyerLst = Lists.newArrayList(csFirstToConsultBuyerSet);
                /*
                 * 订单绑定处理
                 */
                List<BuyerOrderDTO> orderLst = orderDao.selectShopCreatedOrderLstByBuyersAndDateForFirstConsult(shop, sDate, lossValidEndDate, csFirstToConsultBuyerLst);

                //落实下单Set
                Set<String> csOrderedPesBuyerSet = Optional.ofNullable(orderLst)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .map(BuyerOrderDTO::getBuyerNick)
                        .collect(Collectors.toSet());

                /*
                 * 流失记录处理
                 */
                LossEnquiryRecordDTO lossRecordDO = new LossEnquiryRecordDTO();
                for (EnquiryChatpeerDTO buyerNickChatpeer : csFirstToConsultChatpeerLst) {
                    if (!csOrderedPesBuyerSet.contains(buyerNickChatpeer.getBuyerNick())) {
                        //记录流失记录
                        //询单流失的
                        lossRecordDO.setShopId(buyerNickChatpeer.getShopId());
                        lossRecordDO.setCsNick(buyerNickChatpeer.getCsNick());
                        lossRecordDO.setCustomer(buyerNickChatpeer.getBuyerNick());
                        lossRecordDO.setDate(buyerNickChatpeer.getDate());
                        lossRecordDO.setChatType(CommonConstants.CS_INITIATIVE_CHAT);
                        lossRecordDO.setChatNum(buyerNickChatpeer.getChatNum());
                        lossRecordDO.setStartDateTime(buyerNickChatpeer.getFirstChatDate());
                        lossRecordDO.setEndDateTime(buyerNickChatpeer.getLastChatDate());
                        enquiryLossList.add(lossRecordDO);
                    }

                }
            }
            if (isDelData) {
                //询单流失历史记录删除
                int deleteNum = lossEnquiryRecordDao.deleteShopLossRecordByDate(shop, jobDate.getStartDate(), jobDate.getEndDate(), CommonConstants.CS_INITIATIVE_CHAT);
                logger.info("shop【{}】,enquiryLoss cs initiative chat delete num = {}", shop.getTitle(), deleteNum);
            }
            int insertNum = lossEnquiryRecordDao.insertBatchShopLossRecord(shop, enquiryLossList, lossValidEndDate);
            logger.info("shop【{}】,enquiryLoss cs initiative chat insert num = {}", shop.getTitle(), insertNum);
        }
    }


    /**
     * 咨询，接待处理
     *
     * @param isDelData
     */
    @Override
    public void redisCommonChat(JobShopQuery jobShop, JobDateQuery jobDate,
                                boolean isDelData) {

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        JobShopDTO shop = jobShop.getShop();
        List<CsDTO> csLst = jobShop.getCsLst().stream()
                .filter(e -> e.getType() == 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(csLst)) {
            return;
        }
        List<Date> dates = jobDate.getCommonDates();
        if (CollectionUtils.isEmpty(dates)) {
            return;
        }


        for (Date date : dates) {

            Date sDate = date;
            Date eDate = DateUtil.getEndTimeOfDate(date);

            for (CsDTO cs : csLst) {
                String csNick = cs.getNick();

                List<CommonCsChatpeerDTO> cpLst = csChatpeerDao.selectShopCsChatpeerLstByDate(shop, date, csNick);
                if (CollectionUtils.isEmpty(cpLst)) {
                    continue;
                }

                //@TODO(可优化)
                List<CsChatlogDTO> chatlogLst = chatlogDao.selectShopCsChatLogLstForConsultHandle(shop, csNick, sDate, eDate);
                if (CollectionUtils.isEmpty(chatlogLst)) {
                    continue;
                }

                Map<String, List<CommonCsChatpeerDTO>> buyerMap = cpLst.stream().collect(Collectors.groupingBy(CommonCsChatpeerDTO::getBuyerNick));

                long f = System.currentTimeMillis();

                redisOperator.putAll("common-chat-" + csNick, buyerMap);

                long l = System.currentTimeMillis();
                System.out.println("chatlog to redis:" + (l - f));

                long f2 = System.currentTimeMillis();

                redisOperator.getHashEntries("common-" + csNick);

                long l2 = System.currentTimeMillis();
                System.out.println("redis to chatlog:" + (l2 - f2));


                long f3 = System.currentTimeMillis();

                redisOperator.put("commom-chatlog", csNick, chatlogLst);

                long l3 = System.currentTimeMillis();
                System.out.println("chatlog to redis:" + (l3 - f3));


                long f4 = System.currentTimeMillis();

                redisOperator.getHashKey("commom-chatlog", csNick);

                long l4 = System.currentTimeMillis();
                System.out.println("redis to chatlog:" + (l4 - f4));


            }
        }
    }

    /**
     * 咨询，接待处理
     *
     * @param isDelData
     */
    @Override
    public void handleCommonChat(JobShopQuery jobShop, JobDateQuery jobDate,
                                 boolean isDelData) {
        long s = System.currentTimeMillis();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        boolean crossDayChatSwitch = sys.getEnquiryLossSwitch();
        Boolean csForwardAftersellSwitch = sys.getCsForwardAftersellSwitch();
        /*留言过滤开关，开启之后留言的会话不算分时接待**/
        Boolean leaveMessageSwitch = sys.getLeaveMessageSwitchWithNull();
        JobShopDTO shop = jobShop.getShop();
        List<Date> dates = jobDate.getCommonDates();
        if (CollectionUtils.isEmpty(dates)) {
            return;
        }
        List<ShopAccountDTO> shopSubUserLst = jobShop.getShopSubUserLst();
        Set<String> accountSet = shopSubUserLst.stream()
                .map(ShopAccountDTO::getNick)
                .collect(Collectors.toSet());
        Long shopId = shop.getShopId();
        String schemaId = shop.getSchemaId();
//        fix:1993 算第一天的时候第二天没有未回复，所以往前多算一天
        Date beforeDate = DateFormatUtils.getDateByPeriod(dates.get(0), -1);
        List<Date> calDates = new ArrayList<>();
        calDates.add(beforeDate);
        calDates.addAll(dates);
        for (Date date : calDates) {
            List<Date> crossChatDateLst = new ArrayList<>();
            Date sdate = date;
            Date edate = DateUtil.getEndTimeOfDate(date);
            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_ALL, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }
            Date adjustStartDate;
            Date nextDate = DateUtils.addDays(date, 1);
            if (crossDayChatSwitch) {
                crossChatDateLst.add(date);
                crossChatDateLst.add(nextDate);
                adjustStartDate = DateUtils.addMinutes(date, -sys.getMaxWaitTime());
            } else {
                adjustStartDate = date;
            }

            Set<String> filtedBuyerNickSet = this.getFiltedBuyerSet(jobShop);
            //Set<String> watchwordSet = this.getWatchwordSet(jobShop, date);
            Map<String, Set<String>> watchwordMap = this.getWatchwordMap(jobShop, date);

            //---------------客服服务质量(会话维度-每日) START ----------------//
            List<CsChatSessionServiceIndexDO> dayCsChatSessionServiceIndexLst = new ArrayList<>(40);
            List<CsChatSessionDTO> dayCsChatSessionLst = new ArrayList<>(targetCsLst.size() * 20);
            List<CsChatlogDTO> allChatLogLst = Lists.newArrayList();
            List<CommonCsChatpeerDTO> allCpLst = Lists.newArrayList();
            //---------------客服服务质量(会话维度-每日) END ----------------//

            //---------------客服服务质量(人维度-每日)----------------//
            List<CsServiceIndexDO> csServiceIndexLst = Lists.newArrayListWithCapacity(targetCsLst.size());
            //---------------客服服务质量(人维度-每日)----------------//

            if (isDelData) {
                shopTeamSessionServiceIndexDao.deleteDate(shopId, schemaId, date);
                csChatSessionServiceIndexDao.deleteByTimePoint(date, schemaId, shopId, null);
                receiveSessionPressureDao.deleteByTimePoint(date, nextDate, schemaId, shop.getShopId(), null);
                receiveSessionNumHourlyDao.deleteByTimePoint(date, schemaId, shop.getShopId(), null);
            }
            //重算需要用到的Lst容器
            List<Map<CsChatSessionDTO, List<CsChatlogDTO>>> dayRecalculateCsChatSessionMap = new ArrayList<>();


            for (CsDTO cs : targetCsLst) {
                String csNick = cs.getNick();

                long s0 = System.currentTimeMillis();
                List<CommonCsChatpeerDTO> cpLst = csChatpeerDao.selectShopCsChatpeerLstByDate(shop, date, csNick);
                long e0 = System.currentTimeMillis();
                if (logger.isDebugEnabled()) {

                    logger.debug("select chatpeer num:{},time:{}ms", cpLst.size(), (e0 - s0));
                }
                if (CollectionUtils.isEmpty(cpLst)) {
                    // 没有聊天记录也需要补充一条记录
                    CsChatSessionServiceIndexDO csChatSessionServiceIndexDO = CsChatSessionServiceIndexDO.getInstance();
                    dayCsChatSessionServiceIndexLst.add(csChatSessionServiceIndexDO);
                    csChatSessionServiceIndexDO.setShopId(shopId);
                    csChatSessionServiceIndexDO.setCsNick(csNick);
                    csChatSessionServiceIndexDO.setDate(date);
                    continue;
                }

                allCpLst.addAll(cpLst);

                List<ChatBO> cpBOLst = Lists.newArrayListWithCapacity(cpLst.size());
                for (CommonCsChatpeerDTO cp : cpLst) {

                    //初始化默认值
                    //默认是询单和接待
                    cp.setChatFlag(0);
                    cp.setForwardFlag(0);
                    cp.setForwardFilteFlag(0);
                    cp.setWatchwordBuyer(Boolean.FALSE);
                    cp.setFilteredBuyer(Boolean.FALSE);
                    cp.setCsSingleChatFilter(Boolean.FALSE);
                    cp.setCustSingleChatFilter(Boolean.FALSE);
                    cp.setConsult(Boolean.TRUE);
                    cp.setReceive(Boolean.TRUE);
                    cp.setEnquiry(Boolean.FALSE);
                    cp.setCsConsultFirst(Boolean.FALSE);


                    cp.setCsChatFirstFlag(0);
                    cp.setCsActiveChatFail(Boolean.FALSE);
                    cp.setCsActiveUrgepayFail(Boolean.FALSE);

                    cp.setPes(Boolean.FALSE);
                    cp.setTeamPes(Boolean.FALSE);
                    cp.setNextDayPes(Boolean.FALSE);
                    cp.setAssist(Boolean.FALSE);
                    cp.setAfterSale(Boolean.FALSE);
                    cp.setOrderCreated(Boolean.FALSE);
                    cp.setCsOfflineMsgFilter(Boolean.FALSE);
                    cp.setMaAutoReplyFilter(Boolean.FALSE);
                    cp.setChatNum(0);
                    cp.setBuyerChatNum(0);

                    cp.setFirstChatDate(null);
                    cp.setLastChatDate(null);
                    cp.setCrossChat(Boolean.FALSE);
                    cp.setCrossChatFilter(Boolean.FALSE);

                    cp.setCustLeaveMessageFilter(Boolean.FALSE);

                    ChatBO bo = new ChatBO();
                    bo.setBuyerNick(cp.getBuyerNick());
                    bo.setCsNick(cp.getCsNick());
                    bo.setCsChatpeer(cp);
                    cpBOLst.add(bo);
                }

                //按客服进行店铺聊天对象分组
                Map<String, ChatBO> buyerCpBOMap = cpBOLst.stream()
                        .collect(Collectors.toMap(ChatBO::getBuyerNick, ele -> ele, (oldValue, newValue) -> newValue));

                Date adjustEnddate = DateUtils.addDays(edate, 1);

				/*long ss = System.currentTimeMillis();
				//@TODO(可优化)
				List<CsChatlogDTO> totalChatlogLst = chatlogDao.selectShopCsChatLogLstForConsultHandle(shop, csNick, adjustStartDate, adjustEnddate);
				if(CollectionUtils.isEmpty(totalChatlogLst)){
					continue;
				}

				long ll = System.currentTimeMillis();
				System.out.println("totalChatlogLst1="+totalChatlogLst.size()+":time="+(ll-ss)+"ms");*/


                long s1 = System.currentTimeMillis();
                List<CsChatlogDTO> totalChatlogLst = chatlogDao.selectShopCsChatLogLstByBuyerNickLstForConsultHandle(shop, csNick, Lists.newArrayList(buyerCpBOMap.keySet()), adjustStartDate, adjustEnddate);
                if (CollectionUtils.isEmpty(totalChatlogLst)) {//该客服名下所有的都是空聊天
                    //空聊天不算接待
                    cpBOLst.stream().filter(ele -> ele.getCsNick().equals(csNick)).forEach(ele -> ele.getCsChatpeer().setReceive(Boolean.FALSE));
                    continue;
                }

                long e1 = System.currentTimeMillis();
                if (logger.isDebugEnabled()) {

                    logger.debug("select chatlog num:{},time:{}ms", totalChatlogLst.size(), (e1 - s1));
                }


                //聊天对象相关的聊天记录
                List<CsChatlogDTO> chatPeerRelatedChatLogLst = totalChatlogLst.stream().filter(ele -> (ele.getChatTime().compareTo(date) >= 0 && ele.getChatTime().compareTo(edate) <= 0)).collect(Collectors.toList());

                //聊天会话相关的聊天记录
                List<CsChatlogDTO> chatSessionRelatedChatLogLst = totalChatlogLst.stream().filter(ele -> (ele.getChatTime().compareTo(date) >= 0)).collect(Collectors.toList());

               /* //垮天聊天相关的聊天记录
				List<CsChatlogDTO> crossChatRelatedChatLogLst = totalChatlogLst.stream().filter(ele -> {
					return (ele.getChatTime().compareTo(edate) <= 0);
                }).collect(Collectors.toList());*/


                //聊天对象
                Map<String, List<CsChatlogDTO>> chatPeerBuyerMap = chatPeerRelatedChatLogLst.stream()
                        .collect(Collectors.groupingBy(CsChatlogDTO::getBuyerNick));

                //跨天聊天
                Map<String, List<CsChatlogDTO>> crossChatBuyerMap = totalChatlogLst.stream()
                        .collect(Collectors.groupingBy(CsChatlogDTO::getBuyerNick));

                //聊天对象相关的聊天记录按买家分组
                for (Map.Entry<String, List<CsChatlogDTO>> entry : chatPeerBuyerMap.entrySet()) {
                    //咨询客户的聊天记录
                    ChatBO chatBO = buyerCpBOMap.get(entry.getKey());
                    if (chatBO == null) {
                        System.err.println("=====>chat peer buyer:" + entry.getKey());
                    } else {

                        chatBO.setChatPeerRelatedChatLogLst(entry.getValue());
                    }
                }

                //垮台聊天相关的聊天记录按买家分组
                for (Map.Entry<String, List<CsChatlogDTO>> entry : crossChatBuyerMap.entrySet()) {
                    //咨询客户的聊天记录
                    ChatBO chatBO = buyerCpBOMap.get(entry.getKey());
                    if (chatBO == null) {
                        System.err.println("=====>cross chat buyer:" + entry.getKey());
                    } else {

                        chatBO.setCrossChatRelatedChatLogLst(entry.getValue());
                    }
                }

                /*
                 * @TODO(最早，最晚回复聊天时间，买家句数)
                 */
                csCommonHandle(jobShop, csNick, cpBOLst, sdate);

                //TODO 售后账号不参与过滤
                if (!sys.getAftersellAcountFilter() || cs.getType() == 1) {

                    //客服下线时间
                    Date csOfflineDatetime = getCsOfflineDatetime(jobShop, chatPeerRelatedChatLogLst);

                    /*
                     * @TODO(自家账号过滤,绩效软件客服过滤,空聊天过滤,京东官方客服过滤)
                     */
                    handleCsBaseFilter(jobShop, cpBOLst, accountSet);

                    /*
                     * @TODO(指定顾客过滤)
                     */
                    handleCustomerFilter(csNick, cpBOLst, filtedBuyerNickSet);

                    /*
                     * @TODO(客服离线消息过滤)
                     */
                    handleCsOfflineMsgFilter(jobShop, date, cpBOLst, csOfflineDatetime);

                    /*
                     * @TODO(暗语过滤)
                     */
                    handleCsWatchwordFilter(jobShop, csNick, cpBOLst, watchwordMap);

                    /*
                     * @TODO(主号自动回复过滤)
                     */
                    handleMainAccountAutoReplyFilter(jobShop, cpBOLst);

                    /*
                     * @TODO(顾客单句过滤)
                     */
                    handleCustomerSingleChatFilter(jobShop, cpBOLst);

                    /*
                     * @TODO(客服单口相声过滤)
                     */
                    handleCsSingleChatFilter(jobShop, cpBOLst);

                    /*
                     * @TODO(顾客留言过滤)
                     */
                    //handleLeaveMessageFilter(jobShop, date, csNick, cpBOLst);
                }

                int recordNum = 1;
                if (crossDayChatSwitch) {
                    for (Date crossDate : crossChatDateLst) {
                        //跨天聊天
                        Boolean statusFlag = Boolean.FALSE;
                        if (recordNum == 2) {
                            statusFlag = Boolean.TRUE;
                        }
                        List<String> crossBuyerNicks = handleCrossDayChat(jobShop, crossDate, cpBOLst, statusFlag);
                        //2021-10-27 判断用户是否下单，如果下单则不算跨天
                        Long checkOrderT1 = System.currentTimeMillis();
                        if(CollectionUtils.isNotEmpty(crossBuyerNicks)) {
                            List<String> targetBuyerNicks = new ArrayList<>(crossBuyerNicks.size());
                            //判断跨天用户是否下单
                            if (checkCreateOrderBeforeCross(jobShop, crossBuyerNicks, DateUtil.getDateByPeriod(crossDate, -1), targetBuyerNicks)) {
                                csChatpeerDao.updateChatpeersCrossChat(shop, csNick, DateUtil.getDateByPeriod(crossDate, -1), crossBuyerNicks);
                                //删除询单流失表中得历史数据（跨天聊天）
                                lossEnquiryRecordDao.deleteShopLossRecordByDateAndBuyerLst(shop, DateUtil.getDateByPeriod(crossDate, -1), csNick, crossBuyerNicks);
                            }
                            // 2021-10-27 重算更新一次
                            if(CollectionUtils.isNotEmpty(targetBuyerNicks)){
                                csChatpeerDao.updateChatpeersCrossChatRecount(shop, csNick, DateUtil.getDateByPeriod(crossDate, -1), targetBuyerNicks);
                                lossEnquiryRecordDao.deleteShopLossRecordByDateAndBuyerLst(shop, DateUtil.getDateByPeriod(crossDate, -1), csNick, targetBuyerNicks);
                            }
                            Long checkOrderT2 = System.currentTimeMillis();
                            logger.info("checkCreateOrderBeforeCross time cost:{}", checkOrderT2 - checkOrderT1);
                        }
                        recordNum++;
                    }
                }

                //客服留言
                List<CsLeaveMsgDTO> csLeaveMsgLst = csLeaveMsgDao.searchByDate(sdate, edate, shopId, schemaId, csNick);

                /*客服对应的会话，和会话对应的聊天记录 session 按照开始时间排序过了*/
                Date sessionStartDate = sdate;
                if(shop.getShopId() == 10054339L){
                    sessionStartDate = DateFormatUtils.getDateByPeriod(sdate, -1);
                }
                List<CsChatSessionDTO> csChatSessionLst = csChatSessionDao.searchAllByTime(sessionStartDate, edate, shopId, schemaId, csNick);


                Map<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionMap =
                        MapUtils.extract(csChatSessionLst, CsChatSessionDO::getSid, chatSessionRelatedChatLogLst, CsChatlogDTO::getSid);
                if (org.apache.commons.collections.MapUtils.isEmpty(csChatSessionMap)) {
                    continue;
                }
                dayRecalculateCsChatSessionMap.add(csChatSessionMap);

                /*聚合 chatSession chatLog*/
                dayCsChatSessionLst.addAll(csChatSessionLst);
                allChatLogLst.addAll(chatSessionRelatedChatLogLst);

                // 处理聊天会话转发
                //计算聊天会话
                calChatSession(jobShop, cs, sdate, edate, cpLst, csChatSessionMap, csLeaveMsgLst, cpBOLst);

                //计算接待压力
                Integer maxReceiveSessionNum = calReceivePressure(jobShop, cs, date, csChatSessionMap);
                //计算客服服务指标
                CsChatSessionServiceIndexDO csChatSessionServiceIndex = calChatSessionServiceIndex(jobShop, cs, date, csChatSessionMap, maxReceiveSessionNum);
                if (csChatSessionServiceIndex != null) {
                    int sayFlag = 0;
                    int chatRoundNum = 0;
                    boolean notFindFirstSaidBuyer = true;
                    int chatlogIndex = 0;
                    ChatRoundNumBO bo = new ChatRoundNumBO(sayFlag, chatRoundNum, notFindFirstSaidBuyer, chatlogIndex);
                    List<OrderChatlogInfoDTO> chatLogLst = initOrderChatlogInfoDTOBy(chatPeerRelatedChatLogLst);
					/*
					计算回合数
					 */
                    packageChatRoundNum(jobShop, adjustEnddate, chatLogLst, bo);
                    csChatSessionServiceIndex.setChatRoundNum(bo.getChatRoundNum());

                    dayCsChatSessionServiceIndexLst.add(csChatSessionServiceIndex);
                }


                //---------------------------------- 接待质量 START --------------------//

                List<CsBuyerServiceIndexDO> csBuyerServiceIndexLst = calCsBuyerServiceIndexFromChatlogs(jobShop, date, csNick, chatPeerRelatedChatLogLst);
                if (CollectionUtils.isNotEmpty(csBuyerServiceIndexLst)) {
                    //客服服务指标
                    CsServiceIndexDO cssi = calCsServiceIndex(jobShop, date, csNick, csBuyerServiceIndexLst);
                    if (cssi != null) {
                        csServiceIndexLst.add(cssi);
                    }
                }

                csBuyerServiceIndexDao.deleteShopCsBuyerServiceIndexByDate(shop, date);
//				logger.debug("delete Shop CsBuyerServiceIndex num:{}",num1);
                csBuyerServiceIndexDao.batchInsertCsBuyerServiceIndex(shop, date, csBuyerServiceIndexLst);
//				logger.debug("batch Insert CsBuyerServiceIndex num:{}",num2);
                //---------------------------------- 接待质量 END --------------------//
                if (leaveMessageSwitch) {
                    if (!crossDayChatSwitch) {
                        adjustStartDate = DateUtils.addMinutes(date, -10);
                    }
                    //处理有聊天记录没有会话的情况不算接待
                    List<CsChatSessionDTO> totalChatSessionLst = csChatSessionDao.selectShopCsChatSessionLstByBuyerNickLstForConsultHandle(shop, csNick, Lists.newArrayList(buyerCpBOMap.keySet()), adjustStartDate, adjustEnddate);
                    if (CollectionUtils.isEmpty(totalChatSessionLst)) {//该客服名下所有的都是空聊天
                        //空聊天不算接待
                        cpBOLst.stream().filter(ele -> ele.getCsNick().equals(csNick)).forEach(ele -> ele.getCsChatpeer().setReceive(Boolean.FALSE));
                    }
                }
            }

            // 处理聊天会话转发
            long s3 = System.currentTimeMillis();
            handlerForword(shop, jobShop.getCsLst(), date, dayCsChatSessionServiceIndexLst, dayCsChatSessionLst, allChatLogLst);
            long e3 = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {

                logger.debug("handle chatsession forward end,time:{}ms", (e3 - s3));
            }

            //----------------------------------- 接待质量(会话维度) START --------------------//


            // 添加客服每日 服务指标
            csChatSessionServiceIndexDao.insertBatch(date, dayCsChatSessionServiceIndexLst, schemaId);

            /*店铺的客服服务指标汇总数据*/
            handlerShopTeamServiceIndex(jobShop, date, dayCsChatSessionLst, dayCsChatSessionServiceIndexLst, targetCsLst);

            //----------------------------------- 接待质量(会话维度) END --------------------//

            //----------------------------------------将顾客转发给售后客服行为的售前客服 进行打标 start------------------------------------//
            long s6 = System.currentTimeMillis();
            // 将顾客转发给售后客服行为的售前客服 进行打标
            handlerChatpeerPreSaleForword2AfterSale(jobShop, date, targetCsLst, dayCsChatSessionLst, allCpLst, csForwardAftersellSwitch);
            long e6 = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {

                logger.debug("handle chatpeer forward end,time:{}ms", (e6 - s6));
            }
            //----------------------------------------将顾客转发给售后客服行为的售前客服 进行打标   end------------------------------------//

            if (leaveMessageSwitch) {
//                人维度的接待处理
                //----------------------------------------处理留言过滤 所有会话都为留言则整个聊天关系不算接待 start------------------------------------//
                long s7 = System.currentTimeMillis();
                handleLeaveMessageForReceive(dayCsChatSessionLst, allCpLst);
                long e7 = System.currentTimeMillis();
                if (logger.isDebugEnabled()) {
                    logger.debug("handle handleLeaveMessageForReceive end,time:{}ms", (e7 - s7));
                }
                //----------------------------------------处理留言过滤 所有会话都为留言则整个聊天关系不算接待   end------------------------------------//
            }

            long s4 = System.currentTimeMillis();
            // 处理聊天对象转发
            handlerChatpeerForword(dayCsChatSessionLst, allCpLst);
            long e4 = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {

                logger.debug("handle chatpeer forward end,time:{}ms", (e4 - s4));
            }

            //----------------------------------- 接待质量(人维度) START --------------------//

            csServiceIndexDao.deleteShopCsServiceIndexByDate(shop, date);
//			logger.debug("delete Shop CsServiceIndex num:{}",num3);
            csServiceIndexDao.batchInsertCsServiceIndex(shop, date, csServiceIndexLst);
//			logger.debug("batch Insert CsServiceIndex num:{}",num4);


//			logger.info("handle shop CsServiceIndex end,time:{}s",(e-s)/1000);
            //----------------------------------- 接待质量(人维度) END --------------------//

            long s5 = System.currentTimeMillis();
            int num = csChatpeerDao.updateChatpeerInfoForCommonChat(shop, date, allCpLst);
            long e5 = System.currentTimeMillis();
            long e = System.currentTimeMillis();
            if (logger.isDebugEnabled()) {

                logger.debug("handle chatpeer CommonChat end, update num:{},time:{}ms", num, (e5 - s5));
                logger.debug("handle chatpeer CommonChat end, time:{}ms", (e - s));
            }

        }
    }


    /**
     * 处理留言过滤 所有会话都为留言则整个聊天关系不算接待
     *
     * @param dayCsChatSessionLst
     * @param allCpLst
     */
    private void handleLeaveMessageForReceive(List<CsChatSessionDTO> dayCsChatSessionLst, List<CommonCsChatpeerDTO> allCpLst) {
        if (CollUtil.isEmpty(dayCsChatSessionLst) || CollUtil.isEmpty(allCpLst)) return;
        /*
         * 筛出不是留言的会话。只要存在就不用处理留言不算接待
         */
        List<CsChatSessionDTO> onlineChatSession = dayCsChatSessionLst.stream().filter(ele -> !CommonConstants.CHAT_SESSION_TYPE_LEAVEMESSAGE.equals(ele.getSessionType())).collect(Collectors.toList());
        List<CsChatSessionDTO> leavemessageChatSession = dayCsChatSessionLst.stream().filter(ele -> CommonConstants.CHAT_SESSION_TYPE_LEAVEMESSAGE.equals(ele.getSessionType())).collect(Collectors.toList());
//        在线会话
        List<String> haveOnlineChatSessionPeer = onlineChatSession.stream().map(ele -> ele.getCsNick() + DELIMITER + ele.getCustomer()).collect(Collectors.toList());
//        留言会话
        List<String> leavemessageChatSessionPeer = leavemessageChatSession.stream().map(ele -> ele.getCsNick() + DELIMITER + ele.getCustomer()).collect(Collectors.toList());
        allCpLst.forEach(ele -> {
            String onlineCsPeer = ele.getCsNick() + DELIMITER + ele.getBuyerNick();
            if (leavemessageChatSessionPeer.contains(onlineCsPeer) && !haveOnlineChatSessionPeer.contains(onlineCsPeer)) {
                ele.setReceive(Boolean.FALSE);
                ele.setCustLeaveMessageFilter(Boolean.TRUE);
            }
        });
    }

//    private void handlerForwordAndRecalculateWorkload(JobShopQuery jobShopQuery, List<CsDTO> csLst, Date date,
//                                                      List<CsChatSessionServiceIndexDO> csChatSessionServiceIndexLst,
//                                                      List<CsChatSessionDTO> csChatSessionLst,
//                                                      List<CsChatlogDTO> chatlogLst, List<Map<CsChatSessionDTO, List<CsChatlogDTO>>> dayRecalculateCsChatSessionMap) {
//        if (CollectionUtils.isEmpty(chatlogLst) || CollectionUtils.isEmpty(csChatSessionLst)) {
//            return;
//        }
//        // 转出的会话
//        List<CsChatSessionDTO> forWordOut = csChatSessionLst.stream().filter(CsChatSessionDO::getTransfer).collect(Collectors.toList());
//
//        // 顾客id ->  ( key:chatSession value:(List)chatLog )
//        Map<String, Map<CsChatSessionDTO, List<CsChatlogDTO>>> buyerSessions = new HashMap<>();
//        JobShopDTO shop = jobShopQuery.getShop();
//        final Long shopId = shop.getShopId();
//        final String schemaId = shop.getSchemaId();
//        /*找转入*/
//        if (CollectionUtils.isNotEmpty(csChatSessionLst)) {
//            final MultiValueMap<CsChatSessionDTO, CsChatlogDTO> chatSession =
//                    MapUtils.extract(csChatSessionLst, CsChatSessionDO::getSid, chatlogLst, CsChatlogDTO::getSid);
//            buyerSessions = MapUtils.extract(chatSession, CsChatSessionDO::getCustomer);
//        }
//        // 转入的会话ID
//        Set<String> forWordInSet = new HashSet<>();
//        /*根据转出找转入*/
//        final Map<String, CsDTO> nickCsDto = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, v -> v, (x, y) -> x));
//        for (CsChatSessionDTO csChatSessionDO : forWordOut) {
//            Map<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListMap = buyerSessions.get(csChatSessionDO.getCustomer());
//            computerForword(csChatSessionDOListMap, csChatSessionDO, forWordInSet, nickCsDto);
//        }
//        /*修改转入 会话*/
//        if (CollectionUtils.isNotEmpty(forWordInSet)) {
//            csChatSessionDao.updateSetForwordFlag(forWordInSet, 1, schemaId, date);
//        }
//        /*重算客服服务指标 转入量 转出量 直接接待量*/
//        for (CsChatSessionServiceIndexDO csChatSessionServiceIndexDo : csChatSessionServiceIndexLst) {
//            //9.2期 - 开启留言配置：留言过滤，   包含（ 咨询量、转出量     未回复）不计入留言
//            boolean leaveMessageSwitch = jobShopQuery.getShopSystemsetting().getLeaveMessageSwitch() != null ? jobShopQuery.getShopSystemsetting().getLeaveMessageSwitch() : false;
//            final List<Integer> flags = csChatSessionDao.searchForWordInOutAndDirect(schemaId, shopId, csChatSessionServiceIndexDo.getCsNick(), date, leaveMessageSwitch ? 1 : null);
//            Integer directReceiveSessionNum = flags.get(2);
//            Integer forwardOutSessionNum = flags.get(0);
//            Integer forwardInSessionNum = flags.get(1);
//
//            //当留言过滤开启，直接接待量需去除留言过滤
////            Integer leaveMsgFilterNum = leaveMessageSwitch ? BaseUtils.getNonNull(csChatSessionServiceIndexDo.getLeaveMsgFilterNum()) : 0;
////            directReceiveSessionNum = directReceiveSessionNum
////                    > BaseUtils.getNonNull(csChatSessionServiceIndexDo.getLeaveMsgSessionNum())
////                    ? directReceiveSessionNum - leaveMsgFilterNum : 0;
////            forwardOutSessionNum = forwardOutSessionNum
////                    > forwardOutSessionNum? forwardOutSessionNum - leaveMsgFilterNum : 0;
//
//            csChatSessionServiceIndexDo.setDirectReceiveSessionNum(directReceiveSessionNum);
//            csChatSessionServiceIndexDo.setForwardOutSessionNum(forwardOutSessionNum);
//            csChatSessionServiceIndexDo.setForwardInSessionNum(forwardInSessionNum);
//            if (forwardInSessionNum > 0) {
//                //有转发重算工作量
//                logger.info("shopId={},csNick={}.重算工作量。start", csChatSessionServiceIndexDo.getShopId(), csChatSessionServiceIndexDo.getCsNick());
//                calWorkload(jobShopQuery, date, dayRecalculateCsChatSessionMap, shop, csChatSessionServiceIndexDo);
//                logger.info("shopId={},csNick={}.重算工作量。  end", csChatSessionServiceIndexDo.getShopId(), csChatSessionServiceIndexDo.getCsNick());
//            }
//        }
//    }

    /**
     * 留言开关开启重算工作量
     *
     * @param jobShop
     * @param csChatSessionServiceIndexDo
     * @param chatSessions
     * @param forwardOutLst
     */
    private void recalculatingWorkload2(JobShopQuery jobShop, CsChatSessionServiceIndexDO csChatSessionServiceIndexDo, Map<CsChatSessionDTO, List<CsChatlogDTO>> chatSessions, List<String> forwardOutLst) {
        if (CollectionUtils.isEmpty(forwardOutLst) || chatSessions.isEmpty()) return;
        //转出字段重新赋值
        for (String forwardOutSid : forwardOutLst) {
            for (Entry<CsChatSessionDTO, List<CsChatlogDTO>> entry : chatSessions.entrySet()) {
                CsChatSessionDTO key = entry.getKey();
                if (forwardOutSid.equals(key.getSid())) {
                    key.setForwardType(CommonConstants.FORWARD_IN);
                }
            }
        }

        final JobShopDTO shop = jobShop.getShop();
        final Long shopId = shop.getShopId();
        csChatSessionServiceIndexDo.setShopId(shopId);
        final ShopSystemsettingDTO shopSystemsetting = jobShop.getShopSystemsetting();
        /*会话总数(接待) 平均响应时间在快速应答时间中的会话数*/
        int sessionSum = 0, forFastInAvgResTime = 0;
        /*咨询量(暂时弃用，使用接待量+接待过滤量) 接待量 顾客发起量 客服主动跟进量 总消息数*/
        int consultSessionNum = 0, receiveSessionNum = 0, custConsultSessionNum = 0, csToCustSessionNum = 0, chatNum = 0;
        /*客服 顾客消息数 客服字数 客服人工回复消息数 慢响应量 长接待量*/
        int csChatNum = 0, custChatNum = 0, csWordNum = 0, buyerChatReply = 0, slowRespSessionNum = 0, longRespSessionNum = 0;
        /*首次响应时间*/
        double avgRespTimeFirst = 0D;
        /*会话回合数 每次会话买家说话到客服回复间隔时间 会话时长(s)*/
        long sessionCount = 0L, sessionTimeCount = 0L, sessionDurationTime = 0L;
        /*系统设置中的快速应答时间*/
        final long quickResponseTime = (shopSystemsetting.getQuickResponseTime());
        /*留言接待量 留言分配量 */
        int levelMsgReciveNum = 0, leaveMsgSessionNum = 0;
        /*接待时长*/
        double receiveSessionDurationTime = 0.0;
        /*接待过滤量*/
        int receiveFilterNum = 0;
        /*空聊天过滤量*/
        int emptyChatrNum = 0;
        /*未回复量*/
        int nonReply = 0;
        // 聊天回合数
        int chatRoundNum = 0;
//        forwardOutSessionNum - leaveMsgSessionNum1 - emptyChatrNum
        /*计算咚咚咨询量参数，一通会话属于转出，留言分配，空聊天只算一次*/
        int ddConsultSessionNumParam = 0;

        //留言接待过滤量
        int leaveMsgFilterNum = 0;

        /**
         * 转接处理： 如果transfer 是1 的话就是转出
         *     然后根据当前的聊天会话，去找同一个买家，在这个会话之后，不同客服的聊天会话，将聊天会话设置为转入
         *     默认为直接接待
         */

        for (Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListEntry : chatSessions.entrySet()) {
            /*计算每个客服的，当天会话的数据*/
//            final List<CsChatlogDTO> chatlogs = csChatSessionDOListEntry.getValue();
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            final List<CsChatlogDTO> csChatLogDtos = csChatSessionDOListEntry.getValue();
            final Date beginDatetime = chatSession.getSessionBeginTime();
            final Date endTime = chatSession.getSessionEndTime();
            long sessionEndTime = 0;
            if (Objects.nonNull(endTime)) {
                sessionEndTime = endTime.getTime() - beginDatetime.getTime();
            }
            //计算空聊天
            boolean isEmptyChatByFilter = isEmptyChatByFilter(shopSystemsetting.getAutoReplySwitch(), csChatLogDtos);
            /************留言分配 start************/
            if (chatSession.getLeaveMsgSession()) {
                leaveMsgSessionNum++;
            }
            if (isEmptyChatByFilter) {
                emptyChatrNum++;
            }

            if (chatSession.getLeaveMsgFilter()) {
                leaveMsgFilterNum++;
            }

            //算咚咚咨询
            if ((!isNotForward(chatSession) || isEmptyChatByFilter || chatSession.getLeaveMsgSession()) && !chatSession.getLeaveMsgFilter()) {
                ddConsultSessionNumParam++;
            }
            /************留言分配   end************/
            /*接待过滤不计算*/
            if (!chatSession.getReceive()) {
                receiveFilterNum++;
                //响应时间指数需要算
                //释义：咨询量中，（本）客服未回复顾客的会话数，转出量及留言分配量不计入统计
                if (isNotForward(chatSession) && isNotMessageAllocation(chatSession) && chatSession.getNonReply() && !chatSession.getLeaveMsgFilter()) {
//                    未回复量   剔除留言的会话
                    nonReply += 1;
                }

//            平均响应(s)=响应总时长/响应次数   （基于咚管家接待量）
//            释义：客服回复消息与顾客消息之间时间差的平均值。转出量及留言分配量不计入统计，是否剔除自动回复依据商家配置，默认剔除
//            首次平均响应（s）=第一次响应总时长/第一次响应总次数（基于咚管家接待量）
//            释义：客服对顾客第一次回复用时的平均值 （转出量及留言分配量不计入统计）是否剔除自动回复依据商家配置，默认剔除
                if (!isEmptyChatByFilter && isNotForward(chatSession) && isNotMessageAllocation(chatSession) && !chatSession.getLeaveMsgFilter()) {
                    sessionCount += chatSession.getSessionCount();

                    //首次平均响应
                    avgRespTimeFirst += chatSession.getAvgRespTimeFirst();
                    //平均响应时间
                    sessionTimeCount += chatSession.getSessionTimeCount();
//                    //     @TODO    配合测试添加的log 测试完需要删除
//                    logger.info("test----------------->[{}]--[{}]--[{}]--[{}]",chatSession.getSid(),chatSession.getCsNick(),chatSession.getCustomer(),chatSession.getSessionTimeCount());
                    //平均会话时长
                    sessionDurationTime += TimeUnit.MILLISECONDS.toSeconds(sessionEndTime);
                    //                快速应答率需要剔除未回复
                    if (!chatSession.getNonReply()) {
                        //计算 平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 平均响应时间
//			forFastInAvgResTime = quickResponseTime > chatSession.getAvgRespTime() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                        //计算 首次平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 首次平均响应时间
                        forFastInAvgResTime = quickResponseTime >= chatSession.getAvgRespTimeFirst() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                    }
                }
                continue;
            }

            receiveSessionNum++;
            sessionSum++;
            /*顾客发起量*/
            custConsultSessionNum = chatSession.getReceiveStartType() == 2 ? custConsultSessionNum + 1 : custConsultSessionNum;
            /*客服主动跟进*/
            csToCustSessionNum = chatSession.getReceiveStartType() == 1 ? csToCustSessionNum + 1 : csToCustSessionNum;
            /*总消息数量*/
            chatNum += chatSession.getChatNum();
            /*客服 顾客消息数*/
            csChatNum += chatSession.getCsChatNum();
            custChatNum += chatSession.getCustChatNum();
            /*客服字数*/
            csWordNum += chatSession.getCsWordNum();
            buyerChatReply += chatSession.getBuyerChatReply();
            slowRespSessionNum = chatSession.getSlowResp() ? slowRespSessionNum + 1 : slowRespSessionNum;
            longRespSessionNum = chatSession.getLongReceive() ? longRespSessionNum + 1 : longRespSessionNum;

            /*留言接待*/
            final Integer sessionType = chatSession.getSessionType();

            levelMsgReciveNum = chatSession.getReceive() && (sessionType == 2) && chatSession.getLeaveMsgSession()
                    ? levelMsgReciveNum + 1 : levelMsgReciveNum;
            receiveSessionDurationTime += chatSession.getSessionReceiveDurationTime();
            chatRoundNum += chatSession.getSessionCount();
            //释义：咨询量中，（本）客服未回复顾客的会话数，转出量及留言分配量不计入统计
            if (isNotForward(chatSession) && isNotMessageAllocation(chatSession) && chatSession.getNonReply() && !chatSession.getLeaveMsgFilter()) {
//                    未回复量   剔除留言的会话
                nonReply += 1;
            }

//            平均响应(s)=响应总时长/响应次数   （基于咚管家接待量）
//            释义：客服回复消息与顾客消息之间时间差的平均值。转出量及留言分配量不计入统计，是否剔除自动回复依据商家配置，默认剔除
//            首次平均响应（s）=第一次响应总时长/第一次响应总次数（基于咚管家接待量）
//            释义：客服对顾客第一次回复用时的平均值 （转出量及留言分配量不计入统计）是否剔除自动回复依据商家配置，默认剔除
            if (!isEmptyChatByFilter && isNotForward(chatSession) && isNotMessageAllocation(chatSession) && !chatSession.getLeaveMsgFilter()) {
                sessionCount += chatSession.getSessionCount();

                //首次平均响应
                avgRespTimeFirst += chatSession.getAvgRespTimeFirst();
                //平均响应时间
                sessionTimeCount += chatSession.getSessionTimeCount();
//                //     @TODO    配合测试添加的log 测试完需要删除
//                logger.info("test----------------->[{}]--[{}]--[{}]--[{}]",chatSession.getSid(),chatSession.getCsNick(),chatSession.getCustomer(),chatSession.getSessionTimeCount());
                //平均会话时长
                sessionDurationTime += TimeUnit.MILLISECONDS.toSeconds(sessionEndTime);
//                快速应答率需要剔除未回复
                if (!chatSession.getNonReply()) {
                    //计算 平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 平均响应时间
//			forFastInAvgResTime = quickResponseTime > chatSession.getAvgRespTime() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                    //计算 首次平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 首次平均响应时间
                    forFastInAvgResTime = quickResponseTime >= chatSession.getAvgRespTimeFirst() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                }
            }
        }
//            接待量
        receiveSessionNum = chatSessions.keySet().stream().filter(ele -> ele.getReceive() && !ele.getLeaveMsgFilter()).collect(Collectors.toList()).size();
//            咨询量
        consultSessionNum = chatSessions.keySet().stream().filter(ele -> !ele.getLeaveMsgFilter()).collect(Collectors.toList()).size();
        //9.2期 - 开启留言配置：留言过滤，   包含（ 咨询量、接待量、转出量     未回复）不计入留言
        //consultSessionNum -= chatSessions.keySet().stream().filter(session -> session.getSessionType() == 2).count();
        //receiveSessionNum -= chatSessions.keySet().stream().filter(session -> session.getSessionType() == 2 && session.getReceive()).count();

        Integer ddReceiveSessionNum = BaseUtils.getNonNull(csChatSessionServiceIndexDo.getDdReceiveSessionNum());
        ddReceiveSessionNum = ddReceiveSessionNum > leaveMsgSessionNum ? ddReceiveSessionNum - leaveMsgFilterNum : 0;
        csChatSessionServiceIndexDo.setDdReceiveSessionNum(ddReceiveSessionNum);
        csChatSessionServiceIndexDo.setConsultSessionNum(Math.max(consultSessionNum, 0));
        csChatSessionServiceIndexDo.setReceiveSessionNum(Math.max(receiveSessionNum, 0));
        csChatSessionServiceIndexDo.setReceiveSessionDurationTime(receiveSessionDurationTime);
        csChatSessionServiceIndexDo.setCustConsultSessionNum(custConsultSessionNum);
        csChatSessionServiceIndexDo.setCsToCustSessionNum(csToCustSessionNum);
        csChatSessionServiceIndexDo.setChatNum(chatNum);
        csChatSessionServiceIndexDo.setCsChatNum(csChatNum);
        csChatSessionServiceIndexDo.setCustChatNum(custChatNum);
        csChatSessionServiceIndexDo.setCsWordNum(csWordNum);
        //        留言分配量
        csChatSessionServiceIndexDo.setLeaveMsgSessionNum(leaveMsgSessionNum);
        /*未回复量*/
        csChatSessionServiceIndexDo.setNonReplySessionNum(nonReply);
        csChatSessionServiceIndexDo.setSlowRespSessionNum(slowRespSessionNum);
        csChatSessionServiceIndexDo.setLongRespSessionNum(longRespSessionNum);
        /*平均回复消息数 客服回复消息数/会话数*/
        csChatSessionServiceIndexDo.setChatRoundNum(chatRoundNum);
        /* 去除转出量及留言分配量 平均回复消息数 客服回复消息数/会话数 */
        csChatSessionServiceIndexDo.setChatRoundNumNoLeave(sessionCount);
        //去除留言类型会话的总数量
        int sessionNumWithOutLevel = Math.max(sessionSum - levelMsgReciveNum, 0);
        csChatSessionServiceIndexDo.setAvgCsMsgSessionNum(sessionSum == 0 ? 0D : csChatNum * 1.0 / sessionSum);
        /*首次平均响应*/
        csChatSessionServiceIndexDo.setRespTimeFirstCount(avgRespTimeFirst);
        csChatSessionServiceIndexDo.setAvgRespTimeFirst(sessionNumWithOutLevel == 0 ? 0D : (avgRespTimeFirst / sessionNumWithOutLevel));
        /*平均响应时间*/
        csChatSessionServiceIndexDo.setRespTimeCount((double) sessionTimeCount);
        csChatSessionServiceIndexDo.setAvgRespTime(sessionCount == 0 ? 0D : (sessionTimeCount * 1.0 / sessionCount));
        /*平均会话时间*/
        csChatSessionServiceIndexDo.setSessionDurationTimeCount((double) sessionDurationTime);
        double avgSessionDurationTime = sessionNumWithOutLevel == 0 ? 0D : (sessionDurationTime * 1.0 / sessionNumWithOutLevel);
        if (avgSessionDurationTime < 0) {
            avgSessionDurationTime = 0;
        }
        csChatSessionServiceIndexDo.setAvgSessionDurationTime(avgSessionDurationTime);
        /*在前端计算，存储基本字段 快速应答率 平均响应时间在快速应答时间中的会话数/总会话数*/
        csChatSessionServiceIndexDo.setAvgRespInQuickTime(forFastInAvgResTime);
        csChatSessionServiceIndexDo.setSessionNum(sessionSum);
        /*空聊天数*/
        csChatSessionServiceIndexDo.setEmptyChatNum(emptyChatrNum);
        /*留言接待量*/
        csChatSessionServiceIndexDo.setLeaveMsgReceiveSessionNum(levelMsgReciveNum);
//        咚管家咨询量=魔方咨询量-魔方转出量—魔方留言分配量；（消息数大于0的，即剔除空聊天）
//        咚管家接待量=魔方咨询量-魔方转出量—魔方留言分配量—魔方未回复量
        doCalDdConsultAndReceiveSession(csChatSessionServiceIndexDo, leaveMsgSessionNum, ddConsultSessionNumParam);
    }

    private void recalculatingWorkload(JobShopQuery jobShop, CsChatSessionServiceIndexDO csChatSessionServiceIndexDo, Map<CsChatSessionDTO, List<CsChatlogDTO>> chatSessions, List<String> forwardOutLst) {
        if (CollectionUtils.isEmpty(forwardOutLst) || chatSessions.isEmpty()) return;

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Boolean leaveMessageSwitch = sys.getLeaveMessageSwitch() != null ? sys.getLeaveMessageSwitch() : false;

        //转出字段重新赋值
        for (String forwardOutSid : forwardOutLst) {
            for (Entry<CsChatSessionDTO, List<CsChatlogDTO>> entry : chatSessions.entrySet()) {
                CsChatSessionDTO key = entry.getKey();
                if (forwardOutSid.equals(key.getSid())) {
                    key.setForwardType(CommonConstants.FORWARD_IN);
                }
            }
        }

        final JobShopDTO shop = jobShop.getShop();
        final Long shopId = shop.getShopId();
        csChatSessionServiceIndexDo.setShopId(shopId);
        final ShopSystemsettingDTO shopSystemsetting = jobShop.getShopSystemsetting();
        /*会话总数(接待) 平均响应时间在快速应答时间中的会话数*/
        int sessionSum = 0, forFastInAvgResTime = 0;
        /*咨询量(暂时弃用，使用接待量+接待过滤量) 接待量 顾客发起量 客服主动跟进量 总消息数*/
        int consultSessionNum = 0, receiveSessionNum = 0, custConsultSessionNum = 0, csToCustSessionNum = 0, chatNum = 0;
        /*客服 顾客消息数 客服字数 客服人工回复消息数 慢响应量 长接待量*/
        int csChatNum = 0, custChatNum = 0, csWordNum = 0, buyerChatReply = 0, slowRespSessionNum = 0, longRespSessionNum = 0;
        /*首次响应时间*/
        double avgRespTimeFirst = 0D;
        /*会话回合数 每次会话买家说话到客服回复间隔时间 会话时长(s)*/
        long sessionCount = 0L, sessionTimeCount = 0L, sessionDurationTime = 0L;
        /*系统设置中的快速应答时间*/
        final long quickResponseTime = (shopSystemsetting.getQuickResponseTime());
        /*留言接待量 留言分配量 */
        int levelMsgReciveNum = 0, leaveMsgSessionNum = 0;
        /*接待时长*/
        double receiveSessionDurationTime = 0.0;
        /*接待过滤量*/
        int receiveFilterNum = 0;
        /*空聊天过滤量*/
        int emptyChatrNum = 0;
        /*未回复量*/
        int nonReply = 0;
        // 聊天回合数
        int chatRoundNum = 0;
//        forwardOutSessionNum - leaveMsgSessionNum1 - emptyChatrNum
        /*计算咚咚咨询量参数，一通会话属于转出，留言分配，空聊天只算一次*/
        int ddConsultSessionNumParam = 0;

        //留言接待过滤量
        int leaveMsgFilterNum = 0;

        /**
         * 转接处理： 如果transfer 是1 的话就是转出
         *     然后根据当前的聊天会话，去找同一个买家，在这个会话之后，不同客服的聊天会话，将聊天会话设置为转入
         *     默认为直接接待
         */

        for (Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListEntry : chatSessions.entrySet()) {
            /*计算每个客服的，当天会话的数据*/
//            final List<CsChatlogDTO> chatlogs = csChatSessionDOListEntry.getValue();
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            final List<CsChatlogDTO> csChatLogDtos = csChatSessionDOListEntry.getValue();
            final Date beginDatetime = chatSession.getSessionBeginTime();
            final Date endTime = chatSession.getSessionEndTime();
            long sessionEndTime = 0;
            if (Objects.nonNull(endTime)) {
                sessionEndTime = endTime.getTime() - beginDatetime.getTime();
            }
            //计算空聊天
            boolean isEmptyChatByFilter = isEmptyChatByFilter(shopSystemsetting.getAutoReplySwitch(), csChatLogDtos);
            /************留言分配 start************/
            if (chatSession.getLeaveMsgSession()) {
                leaveMsgSessionNum++;
            }

            if (chatSession.getLeaveMsgFilter() && chatSession.getReceive()) {
                leaveMsgFilterNum++;
            }

            if (isEmptyChatByFilter) {
                emptyChatrNum++;
            }
            //算咚咚咨询
            if (!isNotForward(chatSession) || isEmptyChatByFilter || chatSession.getLeaveMsgSession()) {
                ddConsultSessionNumParam++;
            }
            /************留言分配   end************/
            /*接待过滤不计算*/
            if (!chatSession.getReceive()) {
                receiveFilterNum++;
                //响应时间指数需要算
                //释义：咨询量中，（本）客服未回复顾客的会话数，转出量及留言分配量不计入统计
                if (isNotForward(chatSession) && isNotMessageAllocation(chatSession) && chatSession.getNonReply()) {
                    nonReply += 1;
                }

//            平均响应(s)=响应总时长/响应次数   （基于咚管家接待量）
//            释义：客服回复消息与顾客消息之间时间差的平均值。转出量及留言分配量不计入统计，是否剔除自动回复依据商家配置，默认剔除
//            首次平均响应（s）=第一次响应总时长/第一次响应总次数（基于咚管家接待量）
//            释义：客服对顾客第一次回复用时的平均值 （转出量及留言分配量不计入统计）是否剔除自动回复依据商家配置，默认剔除
                if (!isEmptyChatByFilter && isNotForward(chatSession) && isNotMessageAllocation(chatSession)) {
                    sessionCount += chatSession.getSessionCount();

                    //首次平均响应
                    avgRespTimeFirst += chatSession.getAvgRespTimeFirst();
                    //平均响应时间
                    sessionTimeCount += chatSession.getSessionTimeCount();
//                    //     @TODO    配合测试添加的log 测试完需要删除
//                    logger.info("test----------------->[{}]--[{}]--[{}]--[{}]",chatSession.getSid(),chatSession.getCsNick(),chatSession.getCustomer(),chatSession.getSessionTimeCount());
                    //平均会话时长
                    sessionDurationTime += TimeUnit.MILLISECONDS.toSeconds(sessionEndTime);
                    //                快速应答率需要剔除未回复
                    if (!chatSession.getNonReply()) {
                        //计算 平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 平均响应时间
//			forFastInAvgResTime = quickResponseTime > chatSession.getAvgRespTime() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                        //计算 首次平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 首次平均响应时间
                        forFastInAvgResTime = quickResponseTime >= chatSession.getAvgRespTimeFirst() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                    }
                }
                continue;
            }

            receiveSessionNum++;
            sessionSum++;
            /*顾客发起量*/
            custConsultSessionNum = chatSession.getReceiveStartType() == 2 ? custConsultSessionNum + 1 : custConsultSessionNum;
            /*客服主动跟进*/
            csToCustSessionNum = chatSession.getReceiveStartType() == 1 ? csToCustSessionNum + 1 : csToCustSessionNum;
            /*总消息数量*/
            chatNum += chatSession.getChatNum();
            /*客服 顾客消息数*/
            csChatNum += chatSession.getCsChatNum();
            custChatNum += chatSession.getCustChatNum();
            /*客服字数*/
            csWordNum += chatSession.getCsWordNum();
            buyerChatReply += chatSession.getBuyerChatReply();
            slowRespSessionNum = chatSession.getSlowResp() ? slowRespSessionNum + 1 : slowRespSessionNum;
            longRespSessionNum = chatSession.getLongReceive() ? longRespSessionNum + 1 : longRespSessionNum;

            /*留言接待*/
            final Integer sessionType = chatSession.getSessionType();

            levelMsgReciveNum = chatSession.getReceive() && (sessionType == 2) && chatSession.getLeaveMsgSession()
                    ? levelMsgReciveNum + 1 : levelMsgReciveNum;
            receiveSessionDurationTime += chatSession.getSessionReceiveDurationTime();
            chatRoundNum += chatSession.getSessionCount();
            //释义：咨询量中，（本）客服未回复顾客的会话数，转出量及留言分配量不计入统计
            if (isNotForward(chatSession) && isNotMessageAllocation(chatSession) && chatSession.getNonReply()) {
                nonReply += 1;
            }

//            平均响应(s)=响应总时长/响应次数   （基于咚管家接待量）
//            释义：客服回复消息与顾客消息之间时间差的平均值。转出量及留言分配量不计入统计，是否剔除自动回复依据商家配置，默认剔除
//            首次平均响应（s）=第一次响应总时长/第一次响应总次数（基于咚管家接待量）
//            释义：客服对顾客第一次回复用时的平均值 （转出量及留言分配量不计入统计）是否剔除自动回复依据商家配置，默认剔除
            if (!isEmptyChatByFilter && isNotForward(chatSession) && isNotMessageAllocation(chatSession)) {
                sessionCount += chatSession.getSessionCount();

                //首次平均响应
                avgRespTimeFirst += chatSession.getAvgRespTimeFirst();
                //平均响应时间
                sessionTimeCount += chatSession.getSessionTimeCount();
                //平均会话时长
                sessionDurationTime += TimeUnit.MILLISECONDS.toSeconds(sessionEndTime);
//                快速应答率需要剔除未回复
                if (!chatSession.getNonReply()) {
                    //计算 平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 平均响应时间
//			forFastInAvgResTime = quickResponseTime > chatSession.getAvgRespTime() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                    //计算 首次平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 首次平均响应时间
                    forFastInAvgResTime = quickResponseTime >= chatSession.getAvgRespTimeFirst() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
                }
            }
        }
        consultSessionNum = receiveFilterNum + receiveSessionNum;
        if (leaveMessageSwitch) {
            consultSessionNum = consultSessionNum > leaveMsgSessionNum ? consultSessionNum - leaveMsgFilterNum : 0;
            receiveSessionNum = receiveSessionNum > leaveMsgSessionNum ? receiveSessionNum - leaveMsgFilterNum : 0;
            //9.2期 - 开启留言配置：留言过滤，   包含（ 咨询量、接待量、转出量     未回复）不计入留言
            //consultSessionNum -= chatSessions.keySet().stream().filter(session -> session.getSessionType() == 2).count();
            //receiveSessionNum -= chatSessions.keySet().stream().filter(session -> session.getSessionType() == 2 && session.getReceive()).count();

            Integer ddReceiveSessionNum = BaseUtils.getNonNull(csChatSessionServiceIndexDo.getDdReceiveSessionNum());
            ddReceiveSessionNum = ddReceiveSessionNum > leaveMsgSessionNum ? ddReceiveSessionNum - leaveMsgFilterNum : 0;
            csChatSessionServiceIndexDo.setDdReceiveSessionNum(ddReceiveSessionNum);
        }
        csChatSessionServiceIndexDo.setConsultSessionNum(Math.max(consultSessionNum, 0));
        csChatSessionServiceIndexDo.setReceiveSessionNum(Math.max(receiveSessionNum, 0));
        csChatSessionServiceIndexDo.setReceiveSessionDurationTime(receiveSessionDurationTime);
        csChatSessionServiceIndexDo.setCustConsultSessionNum(custConsultSessionNum);
        csChatSessionServiceIndexDo.setCsToCustSessionNum(csToCustSessionNum);
        csChatSessionServiceIndexDo.setChatNum(chatNum);
        csChatSessionServiceIndexDo.setCsChatNum(csChatNum);
        csChatSessionServiceIndexDo.setCustChatNum(custChatNum);
        csChatSessionServiceIndexDo.setCsWordNum(csWordNum);
        //        留言分配量
        csChatSessionServiceIndexDo.setLeaveMsgSessionNum(leaveMsgSessionNum);
        /*未回复量*/
        csChatSessionServiceIndexDo.setNonReplySessionNum(nonReply);
        csChatSessionServiceIndexDo.setSlowRespSessionNum(slowRespSessionNum);
        csChatSessionServiceIndexDo.setLongRespSessionNum(longRespSessionNum);
        /*平均回复消息数 客服回复消息数/会话数*/
        csChatSessionServiceIndexDo.setChatRoundNum(chatRoundNum);
        /* 去除转出量及留言分配量 平均回复消息数 客服回复消息数/会话数 */
        csChatSessionServiceIndexDo.setChatRoundNumNoLeave(sessionCount);
        //去除留言类型会话的总数量
        int sessionNumWithOutLevel = Math.max(sessionSum - levelMsgReciveNum, 0);
        csChatSessionServiceIndexDo.setAvgCsMsgSessionNum(sessionSum == 0 ? 0D : csChatNum * 1.0 / sessionSum);
        /*首次平均响应*/
        csChatSessionServiceIndexDo.setRespTimeFirstCount(avgRespTimeFirst);
        csChatSessionServiceIndexDo.setAvgRespTimeFirst(sessionNumWithOutLevel == 0 ? 0D : (avgRespTimeFirst / sessionNumWithOutLevel));
        /*平均响应时间*/
        csChatSessionServiceIndexDo.setRespTimeCount((double) sessionTimeCount);
        csChatSessionServiceIndexDo.setAvgRespTime(sessionCount == 0 ? 0D : (sessionTimeCount * 1.0 / sessionCount));
        /*平均会话时间*/
        csChatSessionServiceIndexDo.setSessionDurationTimeCount((double) sessionDurationTime);
        double avgSessionDurationTime = sessionNumWithOutLevel == 0 ? 0D : (sessionDurationTime * 1.0 / sessionNumWithOutLevel);
        if (avgSessionDurationTime < 0) {
            avgSessionDurationTime = 0;
        }
        csChatSessionServiceIndexDo.setAvgSessionDurationTime(avgSessionDurationTime);
        /*在前端计算，存储基本字段 快速应答率 平均响应时间在快速应答时间中的会话数/总会话数*/
        csChatSessionServiceIndexDo.setAvgRespInQuickTime(forFastInAvgResTime);
        csChatSessionServiceIndexDo.setSessionNum(sessionSum);
        /*空聊天数*/
        csChatSessionServiceIndexDo.setEmptyChatNum(emptyChatrNum);
        /*留言接待量*/
        csChatSessionServiceIndexDo.setLeaveMsgReceiveSessionNum(levelMsgReciveNum);
//        咚管家咨询量=魔方咨询量-魔方转出量—魔方留言分配量；（消息数大于0的，即剔除空聊天）
//        咚管家接待量=魔方咨询量-魔方转出量—魔方留言分配量—魔方未回复量
        doCalDdConsultAndReceiveSession(csChatSessionServiceIndexDo, leaveMsgSessionNum, ddConsultSessionNumParam);
    }

    private void doCalDdConsultAndReceiveSession(CsChatSessionServiceIndexDO seviceIndex, int leaveMsgSessionNumNotNeed, int ddConsultSessionNumParam) {
        int consultSessionNum = BaseUtils.getNonNull(seviceIndex.getConsultSessionNum());
        int nonReplySessionNum = BaseUtils.getNonNull(seviceIndex.getNonReplySessionNum());
        int ddConsultSessionNum = consultSessionNum - ddConsultSessionNumParam;
        seviceIndex.setDdConsultSessionNum(Math.max(ddConsultSessionNum, 0));
        seviceIndex.setDdReceiveSessionNum(Math.max(ddConsultSessionNum - nonReplySessionNum, 0));
    }

    /**
     * 将顾客转发给售后客服行为的售前客服 进行打标
     *
     * @param jobShop
     * @param date
     * @param targetCsLst
     * @param dayCsChatSessionLst
     * @param allCpLst
     * @param csForwardAftersellSwitch
     */
    private void handlerChatpeerPreSaleForword2AfterSale(JobShopQuery jobShop, Date date, List<CsDTO> targetCsLst, List<CsChatSessionDTO> dayCsChatSessionLst, List<CommonCsChatpeerDTO> allCpLst, Boolean csForwardAftersellSwitch) {
        List<String> preSaleCs = targetCsLst.stream().filter(ele -> ele.getType() == CommonConstants.CS_TYPE_SALE_PRE).map(CsDTO::getNick).collect(Collectors.toList());

        Map<String, List<CommonCsChatpeerDTO>> csChatPeerMap = allCpLst.stream()
                .collect(Collectors.groupingBy(CommonCsChatpeerDTO::getCsNick));

        Map<String, List<CsChatSessionDTO>> csMap = dayCsChatSessionLst.stream()
                .collect(Collectors.groupingBy(CsChatSessionDTO::getCsNick));

        //---------售后客服转入的顾客Nick
        List<String> afterIntoBuyer = getPreIntoBuyerLst(targetCsLst, csMap);

        //给售前客服，有转出给售后的客服打标
        for (Entry<String, List<CsChatSessionDTO>> csEntry : csMap.entrySet()) {
            String csNick = csEntry.getKey();
            if (!preSaleCs.contains(csNick)) {
                continue;
            }
            //客服下对着的所有买家的聊天对象的集合
            List<CommonCsChatpeerDTO> csBuyerChatPeerLst = csChatPeerMap.get(csNick);
            if (CollectionUtils.isNotEmpty(csBuyerChatPeerLst)) {
                List<String> forword2AfterSaleBuyer = new ArrayList<>();
                for (CsChatSessionDTO csChatSessionDTO : csEntry.getValue()) {
                    //类型是转出 并且转出的客服转给了售后
                    if (csChatSessionDTO.getForwardType() == CommonConstants.CHAT_SESSION_FORWARD_TYPE_OUT
                            && afterIntoBuyer.contains(csChatSessionDTO.getCustomer())) {
                        forword2AfterSaleBuyer.add(csChatSessionDTO.getCustomer());
                    }
                }
                if (CollectionUtils.isEmpty(forword2AfterSaleBuyer)) continue;

                //打标转发给售前转发给售后客服
                csBuyerChatPeerLst.forEach(ele -> {
                    if (ele.getCsNick().equals(csNick) && forword2AfterSaleBuyer.contains(ele.getBuyerNick())) {
                        ele.setForwardFlag(CommonConstants.PRESALE_FORWORD2_AFTERSALECS);
                        //转售后客服顾客不计入询单人数 开关开启之后会算 售后
                        if (csForwardAftersellSwitch.equals(Boolean.TRUE)) {
                            ele.setReceive(Boolean.TRUE);
                            ele.setEnquiry(Boolean.FALSE);
                        }
                    }
                });

            }
        }

    }

    /**
     * 售后客服转入的顾客Nick
     *
     * @param allCpLst
     * @param csMap
     * @return
     */
    private List<String> getPreIntoBuyerLst(List<CsDTO> allCpLst, Map<String, List<CsChatSessionDTO>> csMap) {

        List<String> afterSaleCs = allCpLst.stream().filter(ele -> ele.getType() == CommonConstants.CS_TYPE_SALE_AFTER).map(CsDTO::getNick).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterSaleCs)) return Collections.EMPTY_LIST;
        //售前客服转入的顾客Nick
        List<String> buyerNick = new ArrayList<>();
        for (Entry<String, List<CsChatSessionDTO>> csEntry : csMap.entrySet()) {
            String csNick = csEntry.getKey();
            if (!afterSaleCs.contains(csNick)) {
                continue;
            }
            for (CsChatSessionDTO csChatSessionDTO : csEntry.getValue()) {
                if (csChatSessionDTO.getForwardType() == CommonConstants.CHAT_SESSION_FORWARD_TYPE_INTO) {
                    buyerNick.add(csChatSessionDTO.getCustomer());
                }
            }


        }
        return buyerNick;
    }

    private List<OrderChatlogInfoDTO> initOrderChatlogInfoDTOBy(List<CsChatlogDTO> chatPeerRelatedChatLogLst) {

        List<OrderChatlogInfoDTO> chatLogLst = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(chatPeerRelatedChatLogLst)) {
            for (CsChatlogDTO csChatlogDTO : chatPeerRelatedChatLogLst) {
                OrderChatlogInfoDTO orderChatlogInfoDTO = new OrderChatlogInfoDTO();

                orderChatlogInfoDTO.setTime(csChatlogDTO.getChatTime());
                orderChatlogInfoDTO.setDirection(csChatlogDTO.getDirection());
                orderChatlogInfoDTO.setContent(csChatlogDTO.getContent());
                orderChatlogInfoDTO.setSid(csChatlogDTO.getSid());
                orderChatlogInfoDTO.setMt(csChatlogDTO.getMt());
                chatLogLst.add(orderChatlogInfoDTO);
            }

        }
        return chatLogLst;
    }


    //计算聊天回合数
    private ChatRoundNumBO packageChatRoundNum(JobShopQuery jobShop, Date targerOrderDate,
                                               List<OrderChatlogInfoDTO> chatLogLst, ChatRoundNumBO bo) {
        if (targerOrderDate == null) {
            return bo;
        }

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        boolean isFilteAutoReply = sys.getAutoReplySwitch();
        String autoReplyMark = sys.getAutoReplyMark();

        final int BUYER_SAY_FLAG = 1;
        final int CS_SAY_FLAG = -1;

        int chatlogIndex = bo.getChatlogIndex();
        int chatRoundNum = bo.getChatRoundNum();
        int sayFlag = bo.getSayFlag();
        boolean notFindFirstSaidBuyer = bo.isNotFindFirstSaidBuyer();

        Map<String, List<OrderChatlogInfoDTO>> map = chatLogLst.stream().collect(Collectors.groupingBy(OrderChatlogInfoDTO::getSid));
        //解决按照时间计算回合数会出现，多个用户穿插聊天
        for (List<OrderChatlogInfoDTO> chatLogs : map.values()) {
            boolean isAutoReply = false;

            for (OrderChatlogInfoDTO chatlog : chatLogs) {
                chatlogIndex++;

                if (chatlog.getTime().before(targerOrderDate)) {
                    if (chatlog.getDirection() == 0) {
                        if (StringUtils.isNotBlank(chatlog.getContent())) {
                            if (isFilteAutoReply) {
//                                if (chatlog.getContent().startsWith(autoReplyMark)) {
//                                    isAutoReply = true;
//                                }
                                if (chatlog.getMt() == 2 || chatlog.getMt() == 3) {
                                    isAutoReply = true;
                                }
                                if (isAutoReply) { // 表示开启自动回复过滤功能
                                    isAutoReply = false;
                                } else {

                                    if (!notFindFirstSaidBuyer) {
                                        if (CS_SAY_FLAG != sayFlag) {
                                            chatRoundNum++;
                                            sayFlag = CS_SAY_FLAG;
                                        }
                                    }
                                }
                            } else {
                                if (!notFindFirstSaidBuyer) {
                                    if (CS_SAY_FLAG != sayFlag) {
                                        chatRoundNum++;
                                        sayFlag = CS_SAY_FLAG;
                                    }
                                }
                            }
                        }

                    } else if (chatlog.getDirection() == 1) {//买家说
                        notFindFirstSaidBuyer = false;
                        if (BUYER_SAY_FLAG != sayFlag) {
                            sayFlag = BUYER_SAY_FLAG;
                        }
                    }
                } else {
                    break;
                }
            }
        }
        bo.setChatlogIndex(chatlogIndex);
        bo.setChatRoundNum(chatRoundNum);
        bo.setNotFindFirstSaidBuyer(notFindFirstSaidBuyer);
        bo.setSayFlag(sayFlag);
        return bo;
    }


    /**
     * @return List<CsServiceIndexDO>    返回类型
     * @Title: calCsAvgWaitTimeFromChatlogs
     * @Description: (计算平均首次等待时间和平均等待时间 ， 过滤自动回复)
     * @Title: calCsAvgWaitTimeFromChatlogs
     * @Description: (计算平均首次等待时间和平均等待时间 ， 过滤自动回复)
     */
    private List<CsBuyerServiceIndexDO> calCsBuyerServiceIndexFromChatlogs(JobShopQuery jobShop, Date date, String csNick,
                                                                           List<CsChatlogDTO> chatLogLst) {
        //店铺
        JobShopDTO shop = jobShop.getShop();
        //系统设置
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        /*
         * 具体的系统设置项
         */
        long maxWaitTimeMillisecond = sys.getMaxWaitTime() * 60000L;

        Map<String, CsBuyerServiceIndexBO> buyerNickMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(chatLogLst)) {
            int chatLogSize = chatLogLst.size();

            List<CsBuyerServiceIndexBO> buyerIndexTempLst = Lists.newArrayList();
            String buyerNick;
            CsBuyerServiceIndexBO buyerIndex;
            //计算平均等待时间，首次响应时间
            for (int i = 0; i < chatLogSize; i++) {

                CsChatlogDTO chatlog = chatLogLst.get(i);

                buyerNick = chatlog.getBuyerNick();
                //TODO(过滤内部聊天)
                buyerIndex = buyerNickMap.get(buyerNick);//to_id 买家
                if (buyerIndex == null) {
                    buyerIndex = new CsBuyerServiceIndexBO(buyerNick);
                    buyerNickMap.put(buyerNick, buyerIndex);
                    buyerIndexTempLst.add(buyerIndex);
                }

                if (chatlog.getDirection() == 1) {//买家->卖家(买家到客服)

                    if (buyerIndex.isFlag()) {//flag:默认为true
                        buyerIndex.setBuyerChatDate(chatlog.getChatTime());
                        //找第一个买家说话的log,然后找客服说话
                        buyerIndex.setFlag(false);
                        if (buyerIndex.getFirstConsultTime() == null) {
                            buyerIndex.setFirstConsultTime(chatlog.getChatTime());
                        }
                    }
                } else if (chatlog.getDirection() == 0) {//卖家->买家，客服说话
                    if (chatlog.getContent() != null) {

                        if (buyerIndex != null && !buyerIndex.isFlag()) {
                            if (performanceRuleBusiness.isCsAutoReplyBySysettingChat(sys, chatlog.getMt(), chatlog.getContent())) { // 表示开启自动回复过滤功能
                                //自动回复不算客服回复，继续寻找下一个客服回复
                                buyerIndex.setFlag(false);

                            } else {
                                //表示本次是客服在买家说完后首次回复
                                buyerIndex.setNonReply(false);

                                buyerIndex.setCsReplyDate(chatlog.getChatTime());

                                long intervalTime = buyerIndex.getCsReplyDate().getTime() - buyerIndex.getBuyerChatDate().getTime();
                                if (performanceRuleBusiness.isValidCsResponse(intervalTime, maxWaitTimeMillisecond)) {
                                    buyerIndex.setTotalChatIntervalTime(buyerIndex.getTotalChatIntervalTime() + intervalTime);
                                    buyerIndex.setTotalChatSegmentNum(buyerIndex.getTotalChatSegmentNum() + 1);
                                }
                                if (buyerIndex.getFirstReplyDate() == null) {

                                    if (performanceRuleBusiness.isValidCsResponse(chatlog.getChatTime(), buyerIndex.getFirstConsultTime(), maxWaitTimeMillisecond)) {
                                        buyerIndex.setFirstReplyDate(chatlog.getChatTime());
                                    } else {
                                        buyerIndex.setFirstConsultTime(null);
                                    }
                                }
                                buyerIndex.setFlag(true);
                            }
                        }
                    }
                }
            }
            //计算客服字数，客服消息数
            for (int i = 0; i < chatLogSize; i++) {
                CsChatlogDTO chatlog = chatLogLst.get(i);
                buyerNick = chatlog.getBuyerNick();
                //TODO(过滤内部聊天 undo)
                buyerIndex = buyerNickMap.get(buyerNick);//to_id 买家
                if (buyerIndex == null) {
                    buyerIndex = new CsBuyerServiceIndexBO(buyerNick);
                    buyerNickMap.put(buyerNick, buyerIndex);
                    buyerIndexTempLst.add(buyerIndex);
                }
                if (buyerIndex.getSessionStartDate() == null) {
                    buyerIndex.setSessionStartDate(chatlog.getChatTime());
                }

                buyerIndex.setSessionEndDate(chatlog.getChatTime());

                if (chatlog.getDirection() == 1) {//(买家到客服-表示买家说话)
                    buyerIndex.setBuyerChatNum(buyerIndex.getBuyerChatNum() + 1);

                } else if (chatlog.getDirection() == 0) {//客服->买家

                    if (chatlog.getContent() != null) {
                        //@TODO(自动回复过滤-首次响应时间，平均响应时间，客服消息数，客服字数)
                        boolean isAutoReply = performanceRuleBusiness.isCsAutoReplyBySysettingChat(sys, chatlog.getMt(), chatlog.getContent());
                        if (isAutoReply) { // 表示开启自动回复过滤功能
                            //计算客服字数过滤掉自动回复
                        } else {
                            if (buyerIndex.getFirstReceiveDate() == null) {
                                buyerIndex.setFirstReceiveDate(chatlog.getChatTime());
                            }
                            buyerIndex.setLastReceiveDate(chatlog.getChatTime());
                            long wordNum = buyerIndex.getCsWordNum() + chatlog.getContent().replace(" ", "").length();
                            buyerIndex.setCsWordNum(wordNum);
                            buyerIndex.setCsReplyNum(buyerIndex.getCsReplyNum() + 1);
                        }
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(buyerIndexTempLst)) {
                List<CsBuyerServiceIndexDO> buyerIndexLst = Lists.newArrayList();
                CsBuyerServiceIndexDO cssi;
                for (CsBuyerServiceIndexBO att : buyerIndexTempLst) {
                    cssi = new CsBuyerServiceIndexDO(shop.getShopId(), date, csNick, att.getBuyerNick());
                    cssi.setAvgWaitTime(att.getTotalChatSegmentNum() > 0 ? att.getTotalChatIntervalTime() * 1.0D / att.getTotalChatSegmentNum() / 1000D : 0D);
                    if (att.getFirstReplyDate() == null) {
                        cssi.setAvgWaitTimeFirst(0D);
                    } else {
                        cssi.setAvgWaitTimeFirst((att.getFirstReplyDate().getTime() - att.getFirstConsultTime().getTime()) * 1.0D / 1000D);
                    }

                    cssi.setIsNonReply(att.isNonReply());
                    cssi.setCsReplyNum(att.getCsReplyNum());
                    cssi.setBuyerChatNum(att.getBuyerChatNum());
                    cssi.setCsWordNum(att.getCsWordNum());
                    cssi.setSessionTime((att.getSessionEndDate().getTime() - att.getSessionStartDate().getTime()) / 1000);
                    cssi.setFirstReceiveDate(att.getFirstReceiveDate());
                    cssi.setLastReceiveDate(att.getLastReceiveDate());
                    buyerIndexLst.add(cssi);
                }
                return buyerIndexLst;
            }
        }
        return null;
    }

    /**
     * @return int    返回类型
     * @Title: calCsAvgWaitTimeFromChatlogs
     * @Description: (计算平均首次等待时间和平均等待时间 ， 过滤自动回复)
     */
    private CsServiceIndexDO calCsServiceIndex(JobShopQuery jobShop, Date date, String csNick,
                                               List<CsBuyerServiceIndexDO> csServiceIndexLst) {
        JobShopDTO shop = jobShop.getShop();

        if (CollectionUtils.isEmpty(csServiceIndexLst)) {
            return null;
        }

        CsServiceIndexDO csServiceIndex = new CsServiceIndexDO(shop.getShopId(), date, csNick);
        double totalAvgWaitTime = 0D;
        double totalAvgWaitTimeFirst = 0D;
        int effectiveNum = 0;
        int totalCsReplyNum = 0;
        int totalBuyerChatNum = 0;
        long totalWordNum = 0L;
        long totalSessionTime = 0L;
        int totalNonReplyNum = 0;
        for (CsBuyerServiceIndexDO cbsi : csServiceIndexLst) {
            if (cbsi.getAvgWaitTime() > 0) {
                effectiveNum++;
            }
            totalAvgWaitTime += cbsi.getAvgWaitTime();
            totalAvgWaitTimeFirst += cbsi.getAvgWaitTimeFirst();

            totalCsReplyNum += cbsi.getCsReplyNum();
            totalBuyerChatNum += cbsi.getBuyerChatNum();
            totalWordNum += cbsi.getCsWordNum();
            totalSessionTime += cbsi.getSessionTime();
            if (cbsi.getIsNonReply()) {
                totalNonReplyNum++;
            }
        }

        csServiceIndex.setAvgWaitTime(effectiveNum > 0 ? totalAvgWaitTime / effectiveNum : 0D);
        csServiceIndex.setAvgWaitTimeFirst(effectiveNum > 0 ? totalAvgWaitTimeFirst / effectiveNum : 0D);
        csServiceIndex.setCsReplyNum(totalCsReplyNum);
        csServiceIndex.setBuyerChatNum(totalBuyerChatNum);
        csServiceIndex.setCsWordNum(totalWordNum);
        csServiceIndex.setSessionTime(totalSessionTime);
        csServiceIndex.setNonReplyNum(totalNonReplyNum);
        csServiceIndex.setQaRate(totalBuyerChatNum > 0 ? totalCsReplyNum * 1.0 / totalBuyerChatNum : 0D);
        return csServiceIndex;
    }

    /**
     * 封装会话里聊天维度，聊天对象，跨天聊天的聊天记录
     *
     * @param date
     * @param edate
     * @param csNick
     * @param totalChatlogLst
     * @param buyerCpBOMap
     */
    private Date handleChatLogForBusinessCal(JobShopQuery jobShop, Date date, Date edate, String csNick, List<CsChatlogDTO> totalChatlogLst,
                                             Map<String, ChatBO> buyerCpBOMap) {

        //聊天对象相关的聊天记录
        List<CsChatlogDTO> chatPeerRelatedChatLogLst = totalChatlogLst.stream().filter(ele -> {
            return (ele.getChatTime().compareTo(date) >= 0 && ele.getChatTime().compareTo(edate) <= 0);
        }).collect(Collectors.toList());

        //聊天会话相关的聊天记录
        List<CsChatlogDTO> chatSessionRelatedChatLogLst = totalChatlogLst.stream().filter(ele -> {
            return (ele.getChatTime().compareTo(date) >= 0);
        }).collect(Collectors.toList());

        //垮台聊天相关的聊天记录
        List<CsChatlogDTO> crossChatRelatedChatLogLst = totalChatlogLst.stream().filter(ele -> {
            return (ele.getChatTime().compareTo(edate) <= 0);
        }).collect(Collectors.toList());


        //聊天对象
        Map<String, List<CsChatlogDTO>> chatPeerBuyerMap = chatPeerRelatedChatLogLst.stream()
                .collect(Collectors.groupingBy(CsChatlogDTO::getBuyerNick));

        //跨天聊天
        Map<String, List<CsChatlogDTO>> crossChatBuyerMap = crossChatRelatedChatLogLst.stream()
                .collect(Collectors.groupingBy(CsChatlogDTO::getBuyerNick));

        //会话
        Map<String, List<CsChatlogDTO>> chatSessionBuyerMap = chatSessionRelatedChatLogLst.stream()
                .collect(Collectors.groupingBy(CsChatlogDTO::getBuyerNick));

        //聊天对象相关的聊天记录按买家分组
        for (Map.Entry<String, List<CsChatlogDTO>> entry : chatPeerBuyerMap.entrySet()) {
            //咨询客户的聊天记录
            ChatBO chatBO = buyerCpBOMap.get(entry.getKey());
            if (chatBO == null) {
                System.err.println("=====>entry.getKey():" + entry.getKey());
            } else {

                chatBO.setChatPeerRelatedChatLogLst(entry.getValue());
            }
        }
        //聊天会话相关的聊天记录按买家分组
        for (Map.Entry<String, List<CsChatlogDTO>> entry : chatSessionBuyerMap.entrySet()) {
            //咨询客户的聊天记录
            ChatBO chatBO = buyerCpBOMap.get(entry.getKey());
            if (chatBO == null) {
                System.err.println("=====>entry.getKey():" + entry.getKey());
            } else {

                chatBO.setChatSessionRelatedChatLogLst(entry.getValue());
            }
        }

        //垮台聊天相关的聊天记录按买家分组
        for (Map.Entry<String, List<CsChatlogDTO>> entry : crossChatBuyerMap.entrySet()) {
            //咨询客户的聊天记录
            ChatBO chatBO = buyerCpBOMap.get(entry.getKey());
            if (chatBO == null) {
                System.err.println("=====>entry.getKey():" + entry.getKey());
            } else {

                chatBO.setCrossChatRelatedChatLogLst(entry.getValue());
            }
        }


        Date csOfflineDatetime = getCsOfflineDatetime(jobShop, chatPeerRelatedChatLogLst);

        return csOfflineDatetime;
    }

    /**
     * 处理接待对象的转发过滤
     *
     * @param dayCsChatSessionLst
     * @param allCpLst
     */
    private void handlerChatpeerForword(List<CsChatSessionDTO> dayCsChatSessionLst, List<CommonCsChatpeerDTO> allCpLst) {


        Map<String, List<CommonCsChatpeerDTO>> csChatPeerMap = allCpLst.stream()
                .collect(Collectors.groupingBy(CommonCsChatpeerDTO::getCsNick));

        Map<String, List<CsChatSessionDTO>> csMap = dayCsChatSessionLst.stream()
                .collect(Collectors.groupingBy(CsChatSessionDTO::getCsNick));

        for (Entry<String, List<CsChatSessionDTO>> csEntry : csMap.entrySet()) {
            //客服下对着的所有买家的聊天对象的集合
            List<CommonCsChatpeerDTO> csBuyerChatPeerLst = csChatPeerMap.get(csEntry.getKey());
            if (CollectionUtils.isNotEmpty(csBuyerChatPeerLst)) {

                //客服下所有买家的聊天会话集合按买家分组
                Map<String, List<CsChatSessionDTO>> buyerMap = csEntry.getValue().stream()
                        .collect(Collectors.groupingBy(CsChatSessionDTO::getCustomer));


                for (CommonCsChatpeerDTO buyerCp : csBuyerChatPeerLst) {


                    //客服下，按买家分组的的所有会话，只要存在一个不是转发的会话，该接待对象就不是转发过滤
                    List<CsChatSessionDTO> csBuyerChatSessionLst = buyerMap.get(buyerCp.getBuyerNick());
                    if (CollectionUtils.isNotEmpty(csBuyerChatSessionLst)) {
                        boolean isForwordFilter = Boolean.TRUE;
                        for (CsChatSessionDTO csChatSession : csBuyerChatSessionLst) {
                            if (!csChatSession.getForwordFilter()) {
                                isForwordFilter = Boolean.FALSE;
                                break;
                            }
                        }
                        if (isForwordFilter) {
                            buyerCp.setForwardFilteFlag(1);//转发过滤，不算接待
                            buyerCp.setReceive(Boolean.FALSE);
                        }
                    }

                }
            }
        }

    }

    /**
     * 处理店铺客服团队的服务指标
     *
     * @param jobShop
     * @param date
     * @param csChatSessionLst
     * @param csChatSessionServiceIndexLst
     * @param targetCsLst
     */
    private void handlerShopTeamServiceIndex(
            JobShopQuery jobShop,
            Date date,
            List<CsChatSessionDTO> csChatSessionLst,
            List<CsChatSessionServiceIndexDO> csChatSessionServiceIndexLst, List<CsDTO> targetCsLst) {

        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        String schemaId = shop.getSchemaId();

        Date startDate = date;
        Date endDate = com.pes.jd.util.DateUtils.getEndTimeOfDate(date);
        /*店铺级别的工作量缓存*/
        ShopTeamSessionServiceIndexDO shopTeamSessionServiceIndexDO = new ShopTeamSessionServiceIndexDO();
        CommonUtils.countBean(csChatSessionServiceIndexLst, () -> shopTeamSessionServiceIndexDO);
        shopTeamSessionServiceIndexDO.setShopId(shopId);
        shopTeamSessionServiceIndexDO.setDate(date);
        /*留言咨询( 覆盖逻辑，先使用直接查表的方式，然后chatSession不缓存 )*/
        shopTeamSessionServiceIndexDO.setLeaveMsgAdvisorySessionNum(
                (int) csChatSessionLst.stream().filter(k -> k.getConsult() && k.getSessionType() == 2).count()
        );

        shopTeamSessionServiceIndexDO.setLeaveMsgAdvisorySessionNum(csLeaveMsgDao.leaveConsult(
                shopId, startDate, endDate, schemaId
        ));
        //首次平均响应、平均响应、平均会话时长 不可以直接聚合，需要除一下
//        分子加分子/分母加分母
        shopTeamSessionServiceIndexDO.setAvgRespTimeFirst(BaseUtils.getNonNull(shopTeamSessionServiceIndexDO.getSessionNum()) == 0 ? 0 : shopTeamSessionServiceIndexDO.getRespTimeFirstCount() / shopTeamSessionServiceIndexDO.getSessionNum());
        shopTeamSessionServiceIndexDO.setAvgRespTime(BaseUtils.getNonNull(shopTeamSessionServiceIndexDO.getChatRoundNum()) == 0 ? 0 : shopTeamSessionServiceIndexDO.getRespTimeCount() / shopTeamSessionServiceIndexDO.getChatRoundNum());
        shopTeamSessionServiceIndexDO.setAvgSessionDurationTime(BaseUtils.getNonNull(shopTeamSessionServiceIndexDO.getSessionNum()) == 0 ? 0 : shopTeamSessionServiceIndexDO.getSessionDurationTimeCount() / shopTeamSessionServiceIndexDO.getSessionNum());
        shopTeamSessionServiceIndexDao.insert(shopTeamSessionServiceIndexDO, schemaId);
    }

    private void handlerForword(JobShopDTO shop, List<CsDTO> csLst, Date date,
                                List<CsChatSessionServiceIndexDO> csChatSessionServiceIndexLst,
                                List<CsChatSessionDTO> csChatSessionLst,
                                List<CsChatlogDTO> chatlogLst) {
        if (CollectionUtils.isEmpty(chatlogLst) || CollectionUtils.isEmpty(csChatSessionLst)) {
            return;
        }
        // 转出的会话
        List<CsChatSessionDTO> forWordOut = csChatSessionLst.stream().filter(CsChatSessionDO::getTransfer).collect(Collectors.toList());

        // 顾客id ->  ( key:chatSession value:(List)chatLog )
        Map<String, Map<CsChatSessionDTO, List<CsChatlogDTO>>> buyerSessions = new HashMap<>();

        final Long shopId = shop.getShopId();
        final String schemaId = shop.getSchemaId();
        /*找转入*/
        if (CollectionUtils.isNotEmpty(csChatSessionLst)) {
            final MultiValueMap<CsChatSessionDTO, CsChatlogDTO> chatSession =
                    MapUtils.extract(csChatSessionLst, CsChatSessionDO::getSid, chatlogLst, CsChatlogDTO::getSid);
            buyerSessions = MapUtils.extract(chatSession, CsChatSessionDO::getCustomer);
        }
        // 转入的会话ID
        Set<String> forWordInSet = new HashSet<>();
        /*根据转出找转入*/
        final Map<String, CsDTO> nickCsDto = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, v -> v, (x, y) -> x));
        for (CsChatSessionDTO csChatSessionDO : forWordOut) {
            Map<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListMap = buyerSessions.get(csChatSessionDO.getCustomer());
            if (CollUtil.isEmpty(csChatSessionDOListMap)) continue;
            computerForword(csChatSessionDOListMap, csChatSessionDO, forWordInSet, nickCsDto);
        }
        /*修改转入 会话*/
        if (CollectionUtils.isNotEmpty(forWordInSet)) {
            csChatSessionDao.updateSetForwordFlag(forWordInSet, 1, schemaId, date);
        }
        /*重算客服服务指标 转入量 转出量 直接接待量*/
        for (CsChatSessionServiceIndexDO csChatSessionServiceIndexDo : csChatSessionServiceIndexLst) {
            final List<Integer> flags = csChatSessionDao.searchForWordInOutAndDirect(schemaId, shopId, csChatSessionServiceIndexDo.getCsNick(), date, null);
            Integer directReceiveSessionNum = flags.get(2);
            Integer forwardOutSessionNum = flags.get(0);
            Integer forwardInSessionNum = flags.get(1);
            csChatSessionServiceIndexDo.setDirectReceiveSessionNum(directReceiveSessionNum);
            csChatSessionServiceIndexDo.setForwardOutSessionNum(forwardOutSessionNum);
            csChatSessionServiceIndexDo.setForwardInSessionNum(forwardInSessionNum);
//            doCalDdConsultAndReceiveSession(csChatSessionServiceIndexDo);//咚咚咨询量十二期不需要了
        }
    }

    /*
√    1、咨询量：所选时间内，顾客成功与本客服建立咨询关系并发送了消息的会话总数；即已分配给客服且顾客成功发起咨询的所有会话数； 咨询量=接待量+接待过滤量；
√    2、接待量：所选时间内，本客服接待的会话数 （不包含接待过滤量；接待过滤规则，可点此查看设置）
√    3、直接接待量：所选时间内，本客服直接接待的会话数 （基于接待量统计）
√    4、转入量：所选时间内，被店铺其他客服转入本客服接待的会话数 （基于接待量统计）
√    5、转出量：所选时间内，本客服转发给其他客服的会话数（基于接待量统计）
√    6、顾客发起量：所选时间内，本客服接待的顾客中由顾客发起聊天的会话数（基于接待量统计）
√    7、客服主动跟进量：所选时间内，本客服接待的顾客中由客服主动跟进的会话数（基于接待量统计）
√    8、总消息数：总消息数=顾客消息数+客服消息数（基于接待量统计）
√    9、回合数： 所选时间内，本客服服务过程中产生的回合数，顾客和客服一问一答或一问多答即算一个回合（基于接待量统计）
√    10、顾客消息数：本客服接待的顾客发出的消息条数（基于接待量统计）
√    11、客服消息数：本客服接待顾客时发出的消息条数
√    12、答问比：答问比=客服消息数/顾客消息数
√    13、客服字数：本客服接待顾客时发出的消息所包含的中文、英文等字符的总数 （基于接待量统计，且包含空格、制表符、换行回车等空白字符）
√    14、平均回复消息数：平均回复消息数=客服消息数／接待量
√    15、同时最大接待量：同时接待：一个客服在某一时刻前后两分钟内有聊天的会话数；客服同时接待的详细数据可以通过接待压力分析查看。 最大同时接待：在所选时间内，本客服同时接待的最大值（基于接待量统计）
√    16、未回复量：接待量中，本客服未回复顾客的会话数，
√    17、回复率：（接待量-未回复量）/接待量，
√    18、快速应答率：本客服首次回复在指定时间内（当前为30s，可点此设置）的有人工接待（接待量-未回复）/接待量的占比；avgRespInQuickTime/receiveSessionNum
√    19、留言分配量：顾客发起留言咨询并在24小时内成功分配给本客服的会话量,T+2指标,即顾客留言后第三天可在报表中查看最终数据
√    20、留言接待量：顾客发起留言咨询并在24小时内被本客服人工回复的会话量,T+2指标,即顾客留言后第三天可在报表中查看最终数据
√    21、留言响应率：留言响应率=留言接待量／留言分配量 T+2指标，即顾客留言后第三天可在报表中查看最终数据
√    22、慢响应：如果客服在接待一个顾客时，某几次回复比较慢，会被判定为慢响应（具体判定的规则支持自定义，可点此设置）； 慢响应量是指本客服接待的会话被判定为慢响应的会话数（基于接待量）。
√    23、长接待量：如果客服在接待一个会话时，用时比较长，会被判定为长接待（判定的规则支持自定义，可点此设置）； 长接待量：指本客服接待的会话中被判定为长接待的会话数。
√    24、首次平均响应：本客服对顾客第一次回复用时的平均值 （基于接待量，判定的规则支持自定义，可点此设置）
√    25、平响：本客服回复消息与顾客消息之间时间差的平均值（基于接待量，判定的规则支持自定义，可点此设置）
√    26、平均会话时长：本客服平均每通会话从会话建立到会话关闭的时间间隔。会话时长=会话关闭时间-会话开始时间（包含10分钟会话等待时间，留言接待的不计入）
    * */
    private CsChatSessionServiceIndexDO calChatSessionServiceIndex(
            JobShopQuery jobShop,
            CsDTO csDTO,
            Date date,
            Map<CsChatSessionDTO, List<CsChatlogDTO>> chatSessions,
            Integer maxReceiveSessionNum) {

        CsChatSessionServiceIndexDO seviceIndex = new CsChatSessionServiceIndexDO();
        seviceIndex.setCsNick(csDTO.getNick());
        seviceIndex.setDate(date);
        seviceIndex.setMaxReceiveSessionNum(maxReceiveSessionNum);//最大同时接待量
        final JobShopDTO shop = jobShop.getShop();
        final Long shopId = shop.getShopId();
        seviceIndex.setShopId(shopId);
        /*留言分配量*/
        int leaveMsgSessionNum = csLeaveMsgDao.leaveAssign(shopId, date, com.pes.jd.util.DateUtils.getEndTimeOfDate(date), shop.getSchemaId(), csDTO.getNick());
        seviceIndex.setLeaveMsgSessionNum(leaveMsgSessionNum);
        final ShopSystemsettingDTO shopSystemsetting = jobShop.getShopSystemsetting();
        int sessionSum = 0, //会话总数(接待)
                forFastInAvgResTime = 0,//平均响应时间在快速应答时间中的会话数
                avgRespInQuickSessionNum = 0;//符合30秒应答的总咨询数
        int consultSessionNum, //咨询量(使用接待量+接待过滤量)
                receiveSessionNum = 0, //接待量
                custConsultSessionNum = 0, //顾客发起量
                csToCustSessionNum = 0; //客服主动跟进量
        int csChatNum = 0,//客服 消息数
                custChatNum = 0,//顾客消息数
                csWordNum = 0,//客服字数
                slowRespSessionNum = 0,//慢响应量
                longRespSessionNum = 0;//长接待量
        /*首次响应时间*/
        double avgRespTimeFirst = 0D;
        /*会话回合数 每次会话买家说话到客服回复间隔时间 会话时长(s)*/
        long sessionCount = 0L, sessionTimeCount = 0L, sessionDurationTime = 0L;
        /*系统设置中的快速应答时间*/
        final long quickResponseTime = (shopSystemsetting.getQuickResponseTime());
        /*接待时长*/
        double receiveSessionDurationTime = 0.0;
        /*接待过滤量*/
        int receiveFilterNum = 0;
        /*未回复量*/
        int nonReply = 0;
        // 聊天回合数
        int chatRoundNum = 0;
        /**
         * 转接处理： 如果transfer 是1 的话就是转出
         *     然后根据当前的聊天会话，去找同一个买家，在这个会话之后，不同客服的聊天会话，将聊天会话设置为转入
         *     默认为直接接待
         */
        List<String> receiveSid = new ArrayList<>();//接待的会话id用来计算留言接待量
        for (Map.Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListEntry : chatSessions.entrySet()) {
            CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            if (chatSession.getReceive()) {
                receiveSid.add(chatSession.getSid());
            }
        }
        //留言接待的会话
        List<String> receiveSid2 = csLeaveMsgDao.leaveAssignSidByDateAndSid(receiveSid, shopId, date, com.pes.jd.util.DateUtils.getEndTimeOfDate(date), shop.getSchemaId(), csDTO.getNick());
        for (Map.Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListEntry : chatSessions.entrySet()) {
            /*计算每个客服的，当天会话的数据*/
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            /*接待过滤不计算*/
            if (!chatSession.getReceive()) {
                receiveFilterNum++;
                continue;
            }
            receiveSessionNum = receiveSessionNum + 1;
            sessionSum++;
            /*顾客发起量*/
            custConsultSessionNum = chatSession.getReceiveStartType() == 2 ? custConsultSessionNum + 1 : custConsultSessionNum;
            /*客服主动跟进*/
            csToCustSessionNum = chatSession.getReceiveStartType() == 1 ? csToCustSessionNum + 1 : csToCustSessionNum;
            /*客服消息数*/
            csChatNum += chatSession.getCsChatNum();
            /*顾客消息数*/
            custChatNum += chatSession.getCustChatNum();
            /*客服字数*/
            csWordNum += chatSession.getCsWordNum();
            slowRespSessionNum = chatSession.getSlowResp() ? slowRespSessionNum + 1 : slowRespSessionNum;
            longRespSessionNum = chatSession.getLongReceive() ? longRespSessionNum + 1 : longRespSessionNum;
            //未回复量
            nonReply = chatSession.getNonReply() ? nonReply + 1 : nonReply;
            final Date beginDatetime = chatSession.getSessionBeginTime();
            final Date endTime = chatSession.getSessionEndTime();
            long sessionEndTime = 0;
            if (Objects.nonNull(endTime)) {
                sessionEndTime = endTime.getTime() - beginDatetime.getTime();
            }
            receiveSessionDurationTime += chatSession.getSessionReceiveDurationTime();
            sessionCount += chatSession.getSessionCount();

            //首次平均响应
            avgRespTimeFirst += chatSession.getAvgRespTimeFirst();
            //平均响应时间
//            fix:787
            if (!chatSession.getNonReply() && !receiveSid2.contains(chatSession.getSid())) {
                chatRoundNum += chatSession.getSessionCount();
                sessionTimeCount += chatSession.getSessionTimeCount();
            }
            //平均会话时长
            sessionDurationTime += TimeUnit.MILLISECONDS.toSeconds(sessionEndTime);
            //计算 平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 平均响应时间
//			forFastInAvgResTime = quickResponseTime > chatSession.getAvgRespTime() ? forFastInAvgResTime + 1 : forFastInAvgResTime;
            //计算 首次平均响应时间在快速应答时间中的会话数，若 系统设置中的快速应答时间 > 首次平均响应时间
            forFastInAvgResTime = quickResponseTime >= chatSession.getAvgRespTimeFirst() && !chatSession.getNonReply() && !receiveSid2.contains(chatSession.getSid()) ? forFastInAvgResTime + 1 : forFastInAvgResTime;
            if (quickResponseTime >= chatSession.getAvgRespTimeFirst()) avgRespInQuickSessionNum++;
        }
        consultSessionNum = receiveFilterNum + receiveSessionNum;
        seviceIndex.setConsultSessionNum(consultSessionNum);
        seviceIndex.setReceiveSessionNum(receiveSessionNum);
        seviceIndex.setReceiveSessionDurationTime(receiveSessionDurationTime);
        seviceIndex.setCustConsultSessionNum(custConsultSessionNum);
        seviceIndex.setCsToCustSessionNum(csToCustSessionNum);
        seviceIndex.setCsChatNum(csChatNum);
        seviceIndex.setCustChatNum(custChatNum);
        seviceIndex.setChatNum(csChatNum + custChatNum);//总消息数：总消息数=顾客消息数+客服消息数（基于接待量统计）
        seviceIndex.setCsWordNum(csWordNum);
        /*未回复量*/
        seviceIndex.setNonReplySessionNum(nonReply);
        seviceIndex.setSlowRespSessionNum(slowRespSessionNum);
        seviceIndex.setLongRespSessionNum(longRespSessionNum);
        /*平均回复消息数 客服回复消息数/会话数*/
        seviceIndex.setChatRoundNum(chatRoundNum);
        seviceIndex.setChatRoundNumNoLeave(sessionCount);
        //去除留言类型会话的总数量
        seviceIndex.setAvgCsMsgSessionNum(receiveSessionNum == 0 ? 0D : csChatNum * 1.0 / receiveSessionNum);//平均回复消息数=客服消息数／接待量   cs_chat_num/receive_session_num
        /*首次平均响应*/
        seviceIndex.setRespTimeFirstCount(avgRespTimeFirst);
        seviceIndex.setAvgRespTimeFirst(sessionSum == 0 ? 0D : (avgRespTimeFirst / sessionSum));
        /*平均响应时间*/
        seviceIndex.setRespTimeCount((double) sessionTimeCount);
        seviceIndex.setAvgRespTime(sessionCount == 0 ? 0D : (sessionTimeCount * 1.0 / sessionCount));
        /*平均会话时间*/
        seviceIndex.setSessionDurationTimeCount((double) sessionDurationTime);
        double avgSessionDurationTime = sessionSum == 0 ? 0D : (sessionDurationTime * 1.0 / sessionSum);
        seviceIndex.setAvgSessionDurationTime(BaseUtils.getNonNull(avgSessionDurationTime));
        /*在前端计算，存储基本字段 快速应答率 平均响应时间在快速应答时间中的会话数/总会话数*/
        seviceIndex.setAvgRespInQuickTime(forFastInAvgResTime);
        seviceIndex.setSessionNum(sessionSum);
        seviceIndex.setDdConsultSessionNum(avgRespInQuickSessionNum);

        seviceIndex.setLeaveMsgReceiveSessionNum(csLeaveMsgDao.leaveAssignByDateAndSid(receiveSid, shopId, date, com.pes.jd.util.DateUtils.getEndTimeOfDate(date), shop.getSchemaId(), csDTO.getNick()));
        return seviceIndex;
    }

    /**
     * 判断是否是空聊天
     *
     * @param autoReplySwitch
     * @param cls
     * @return boolean
     */
    private boolean isEmptyChatByFilter(boolean autoReplySwitch, List<CsChatlogDTO> cls) {

        if (CollectionUtils.isEmpty(cls)) {
            return true;
        }
        for (CsChatlogDTO cl : cls) {
            if (cl.getDirection() == 1) {
                return false;
            }
            if (!isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 不是转出
     *
     * @param chatSession
     * @return
     */
    private boolean isNotForward(CsChatSessionDTO chatSession) {
        /*转接类型 0：直接接待    1：转入接待   2转出接待',*/
        return chatSession.getForwardType() != 2;
    }

    /**
     * 不是留言分配
     *
     * @param chatSession
     * @return
     */
    private boolean isNotMessageAllocation(CsChatSessionDTO chatSession) {
        return chatSession.getSessionType() != 2 && !chatSession.getLeaveMsgSession();
    }

    /**
     * 计算聊天会话
     *
     * @param jobShop
     * @param csChatSessionMap
     * @param csLeaveMsgLst
     * @return
     */
    private void calChatSession(
            JobShopQuery jobShop,
            CsDTO cs,
            Date startDate,
            Date endDate,
            List<CommonCsChatpeerDTO> cpLst,
            Map<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionMap,
            List<CsLeaveMsgDTO> csLeaveMsgLst,
            List<ChatBO> cpBOLst) {
        String csNick = cs.getNick();

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        Boolean leaveMessageSwitch = sys.getLeaveMessageSwitch() != null ? sys.getLeaveMessageSwitch() : false;

        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        String schemaId = shop.getSchemaId();

        //客服转发过滤开关
        final Boolean csForwardSwitch = (!sys.getAftersellAcountFilter() || cs.getType() == 1)//售后账号不参与过滤
                && BooleanUtils.toBooleanDefaultIfNull(sys.getCsForwardSwitch(), false);
        final Boolean autoReplySwitch = sys.getAutoReplySwitch();
        /*客服转发过滤句数 客服最多回复 {}句（含）*/
        final Integer csForwardNum = sys.getCsForwardNum();

        /*接待过滤的*/
        final Set<String> noReceivedBuyerNickSet = cpLst.stream().filter(k -> !k.getReceive())
                .map(CommonCsChatpeerDTO::getBuyerNick)
                .collect(Collectors.toSet());

//		这天->这个客服->对应的服务评价
        //客服评价
        List<CsServiceEvaluationDetailDTO> csServiceEvaluationLst = csServiceEvaluationDetailDao.searchByDate(shop.getShopId(), startDate, endDate, schemaId, csNick);

        /*
         * 客服邀评
         * 这天->这个客服->对应的发送评价记录
         */
        List<CsServiceSendEvalDO> csServiceSendEvalLst = csServiceSendEvalDao.searchByDate(shopId, startDate, endDate, schemaId, csNick);


        // 过滤，只需要有分配时间的
        final MultiValueMap<String, CsLeaveMsgDTO> sidLeaveMap =
                MapUtils.extractForMap(csLeaveMsgLst, CsLeaveMsgDTO::getSid, v -> Objects.nonNull(v.getAllocationTime()));
        // 服务评价
        final MultiValueMap<String, CsServiceEvaluationDetailDTO> evaluationMap = MapUtils.extract(csServiceEvaluationLst, CsServiceEvaluationDetailDTO::getSid);
        // 邀评
        final MultiValueMap<String, CsServiceSendEvalDO> serviceSendEvalMap = MapUtils.extract(csServiceSendEvalLst, CsServiceSendEvalDO::getSid);

        final Set<Entry<CsChatSessionDTO, List<CsChatlogDTO>>> csChatSessionLog = csChatSessionMap.entrySet();
        /*留言过滤字段重置*/
        csChatSessionLog.forEach(ele -> ele.getKey().setLeaveMsgFilter(Boolean.FALSE));

        /**
         * 转接处理： 如果transfer 是1 的话就是转出
         *     然后根据当前的聊天会话，去找同一个买家，在这个会话之后，不同客服的聊天会话，将聊天会话设置为转入
         *     默认为直接接待
         */
        //人维度的留言过滤
        Set<String> receiveLeaveMsgBuyerSet = Sets.newHashSet();
        Set<String> leaveMsgBuyerFilterSet = new HashSet<>();
        if (leaveMessageSwitch) {
            Set<String> leaveMsgBuyerSet = leaveMsgBuyerFilter(csChatSessionLog, sidLeaveMap);
            if (CollUtil.isNotEmpty(leaveMsgBuyerSet)) {
                leaveMsgBuyerFilterSet.addAll(leaveMsgBuyerSet);
            }
        }
        for (Map.Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListEntry : csChatSessionLog) {
            /*计算每个客服的，当天会话的数据*/
            final List<CsChatlogDTO> chatlogs = csChatSessionDOListEntry.getValue();
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            final Boolean transfer = chatSession.getTransfer();
            /*留言分配量*/
            final List<CsLeaveMsgDTO> lvsm = sidLeaveMap.get(chatSession.getSid());
            if (!org.springframework.util.CollectionUtils.isEmpty(lvsm)) {
                chatSession.setAssign(Boolean.TRUE);
                //留言 言分配
                chatSession.setLeaveMsgSession(true);
            }

            boolean forword = false;
            if (transfer) {
                /*客服说话 direction = 0  客服转发过滤*/
                if (CollectionUtils.isNotEmpty(chatlogs) && csForwardSwitch) {
                    long count = 0;
                    if (autoReplySwitch) {
                        count = chatlogs.stream().filter(k -> k.getDirection() == 0 && k.getMt() != 2 && k.getMt() != 3).count();
                    } else {
                        count = chatlogs.stream().filter(k -> k.getDirection() == 0).count();
                    }
                    /*转发过滤了*/
                    if (count <= csForwardNum) {
                        chatSession.setForwordFilter(true);
                        forword = true;
                    }
                }
                /*转出*/
                chatSession.setForwardType(2);
            }
            /*计算chatSession各种指标*/
            calChatSessionData(sys, chatlogs, chatSession);

            /*邀评数量*/
            int count = 0;
            if (org.apache.commons.collections.MapUtils.isNotEmpty(serviceSendEvalMap)) {
                count += serviceSendEvalMap.getOrDefault(chatSession.getSid(), Collections.emptyList()).size();
            }
            chatSession.setSendEvalNum(count);
            /*评价数量 评价分数 好评数量*/
            int evalNum = 0, evalCode = -1, satisfiedEvalNum = 0;
            if (org.apache.commons.collections.MapUtils.isNotEmpty(evaluationMap)) {
                for (CsServiceEvaluationDetailDTO csServiceEvaluationDetailDTO :
                        evaluationMap.getOrDefault(chatSession.getSid(), Collections.emptyList())) {
                    evalNum++;
                    final Integer evalCode1 = csServiceEvaluationDetailDTO.getEvalCode();
                    evalCode = evalCode == -1 ? 0 : evalCode;
                    evalCode += evalCode1;
                    satisfiedEvalNum += evalCode1 >= 75 ? 1 : 0;
                }
            }
            chatSession.setEvalCode(evalCode);
            chatSession.setSatisfiedEvalNum(satisfiedEvalNum);
            chatSession.setEvalNum(evalNum);
            chatSession.setConsult(true);

            //接待过滤
            if (noReceivedBuyerNickSet.contains(chatSession.getCustomer())) {
                chatSession.setReceive(false);
                chatSession.setReceiveFilter(true);
            } else if (forword) {
                chatSession.setReceive(false);
            } else if (chatSession.getLeaveMsgFilter()) {
                chatSession.setReceive(false);
                chatSession.setReceiveFilter(false);
            } else {
                chatSession.setReceive(true);
                chatSession.setReceiveFilter(false);
            }
            //留言过滤开启
            if (leaveMessageSwitch) {
//                leaveMsgBuyerFilterOfSession(csChatSessionDOListEntry);
                if (!CollectionUtils.isEmpty(lvsm) && !leaveMsgBuyerFilterSet.contains(chatSession.getCustomer())) {
                    receiveLeaveMsgBuyerSet.add(chatSession.getCustomer());
                }
            }
        }
        //处理人维度的留言过滤
        handleLeaveMessageFilter(jobShop, csNick, cpBOLst, receiveLeaveMsgBuyerSet);

        // 更新所有的会话
        csChatSessionDao.updateByPrimaryKeySelective(jobShop.getShop().getSchemaId(), new ArrayList<>(csChatSessionMap.keySet()));
    }

    private Set<String> leaveMsgBuyerFilter(Set<Entry<CsChatSessionDTO, List<CsChatlogDTO>>> csChatSessionLog, MultiValueMap<String, CsLeaveMsgDTO> sidLeaveMap) {
        Set<String> leaveMsgBuyerFilterSet = Sets.newHashSet();
        for (Map.Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListEntry : csChatSessionLog) {
            final CsChatSessionDTO chatSession = csChatSessionDOListEntry.getKey();
            /*留言会话不算接待**/
            if (CommonConstants.CHAT_SESSION_TYPE_LEAVEMESSAGE.equals(chatSession.getSessionType())) {
                chatSession.setReceive(Boolean.FALSE);
                chatSession.setLeaveMsgFilter(Boolean.TRUE);
            }
            /*留言分配量*/
            final List<CsLeaveMsgDTO> lvsm = sidLeaveMap.get(chatSession.getSid());
            if (org.springframework.util.CollectionUtils.isEmpty(lvsm)) {
                leaveMsgBuyerFilterSet.add(chatSession.getCustomer());
            }
        }
        return leaveMsgBuyerFilterSet;
    }


    /**
     * 根据转出找转入
     */
    private void computerForword(Map<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDOListMap,
                                 CsChatSessionDTO chatSession,/*转出*/Set<String> forWordInSet, Map<String, CsDTO> nickCsDto) {
        boolean flag = false;
        // 需要排序
        LinkedHashMap<CsChatSessionDTO, List<CsChatlogDTO>> sortContainer = new LinkedHashMap<>();
        List<CsChatSessionDTO> csChatSessionDTOList = new ArrayList<>(csChatSessionDOListMap.keySet()).stream()
                .filter(csChatSessionDTO -> csChatSessionDTO != null)
                .collect(Collectors.toList());
        final ArrayList<CsChatSessionDTO> csChatSessionDTOS = new ArrayList<>(csChatSessionDTOList);
        try {
            csChatSessionDTOS.sort((x, y) -> {
                final Date end = y.getSessionEndTime();
                final Date begin = x.getSessionBeginTime();
                if (begin == null && end == null) {
                    return 0;
                }
                if (begin == null) {
                    return -1;
                }
                if (end == null) {
                    return 1;
                }
                Long beginTime = begin.getTime();
                Long endTime = end.getTime();
                return beginTime.compareTo(endTime);
            });

        } catch (Exception e) {
            logger.error("computerForword sort error");
            logger.error("{}", e);
            //throw new RuntimeException(e);
            csChatSessionDTOS.sort(Comparator.comparing(CsChatSessionDTO::getSessionEndTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed().thenComparing(CsChatSessionDTO::getSessionBeginTime, Comparator.nullsFirst(Comparator.naturalOrder())).reversed());
        }
        csChatSessionDTOS.forEach(e -> sortContainer.put(e, csChatSessionDOListMap.get(e)));
        for (Map.Entry<CsChatSessionDTO, List<CsChatlogDTO>> csChatSessionDO : sortContainer.entrySet()) {

            /*如果开始寻找*/
            final CsChatSessionDO key = csChatSessionDO.getKey();
            final String sid = key.getSid();

            // 查找 需要转出会话的转入会话，不可以是同一个人
            if (flag && !Objects.equals(key.getCsNick(), chatSession.getCsNick())) {
                if (!key.getTransfer()) {
                    /*转入*/
                    final CsDTO csDTO = nickCsDto.get(key.getCsNick());
                    if (csDTO != null) {
                        chatSession.setForwordAfterSale(csDTO.getType() == (byte) 2);
                    }
                    logger.debug("\n 是否售后转发:{} nick:{}  sid:{} 转发到 nick:{} sid:{}\n", chatSession.getForwordAfterSale(),
                            chatSession.getCsNick(), chatSession.getSid(), key.getCsNick(), key.getSid());
                    forWordInSet.add(sid);
                    return;
                }
            }

            if (Objects.equals(sid, chatSession.getSid())) {
                flag = true;
            }

        }

    }

    /**
     * 处理EndTime 为 null 的情况
     */
    private Date specProcessSessionEndTime(CsChatSessionDO chatSession, List<CsChatlogDTO> chatlogs) {
        Date endDatetime = chatSession.getSessionEndTime();
        if (endDatetime == null) {
            if (CollectionUtils.isNotEmpty(chatlogs)) {
                endDatetime = (Date) chatlogs.get(chatlogs.size() - 1).getChatTime().clone();
                if (chatSession.getSessionType() != 2) {//若会话不是留言，会话结束时间向后推十分钟
                    endDatetime.setTime(endDatetime.getTime() + TimeUnit.MINUTES.toMillis(10L));
                }
            }
        }
        return endDatetime;
    }

    /**
     * 计算接待压力与分时接待
     */
    private Integer calReceivePressure(
            JobShopQuery jobShop,
            CsDTO cs,
            Date date,
            Map<CsChatSessionDTO, List<CsChatlogDTO>> chatSessionMap) {

        if (org.springframework.util.CollectionUtils.isEmpty(chatSessionMap)) {
            return 0;
        }
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        /**留言过滤开关，开启之后留言的会话不算分时接待**/
        Boolean leaveMessageSwitch = sys.getLeaveMessageSwitchWithNull();
        JobShopDTO shop = jobShop.getShop();
        String schemaId = shop.getSchemaId();
        List<TimeSection> timeSections = new ArrayList<>(chatSessionMap.size());
        for (CsChatSessionDTO csChatSessionDO : chatSessionMap.keySet()) {
            if (leaveMessageSwitch && CommonConstants.CHAT_SESSION_TYPE_LEAVEMESSAGE.equals(csChatSessionDO.getSessionType()))
                continue;
            if (csChatSessionDO.getReceive()) {
                /*将接待的 会话的时间点保存在集合中*/
                timeSections.add(
                        new TimeSection(
                                csChatSessionDO.getSessionBeginTime(),
                                csChatSessionDO.getSessionEndTime(),
                                RECEIVE_FLAG_CAL
                        )
                );
            }
        }
        /*接待压力数据*/
        List<ReceiveSessionPressureDO> receiveSessionPressures = new ArrayList<>(200);
        Date adjustDate = DateUtils.setMinutes(DateUtils.truncate(date, Calendar.DAY_OF_MONTH), RECEIVE_FLAG_CAL);

        for (Date index = adjustDate; index.getTime() < DateUtils.addDays(adjustDate, 1).getTime(); index = DateUtils.addMinutes(index, RECEIVE_FLAG_CAL * 2)) {
            /*计算指定日期job的接待压力*/
            int count = calTimeSharing(index, timeSections);
            if (count > 0) {
                ReceiveSessionPressureDO receiveSessionPressureDO = new ReceiveSessionPressureDO();
                receiveSessionPressures.add(receiveSessionPressureDO);
                receiveSessionPressureDO.setCsNick(cs.getNick());
                receiveSessionPressureDO.setReceiveDot(index);
                receiveSessionPressureDO.setShopId(cs.getShopId());
                receiveSessionPressureDO.setReceiveSessionNum(count);
            }
        }

        /*计算分时接待*/
        ReceiveSessionNumHourlyDO receiveSessionNumHourly = new ReceiveSessionNumHourlyDO();
        for (int i = 0; i < 24; i++) {
            int count = 0;
            for (TimeSection timeSection : timeSections) {
                count += timeSection.include(adjustDate, i) ? 1 : 0;
            }
            receiveSessionNumHourly.setCsNick(cs.getNick());
            receiveSessionNumHourly.setDate(date);
            receiveSessionNumHourly.setShopId(shop.getShopId());
            try {
                Method method = ReceiveSessionNumHourlyDO.class.getDeclaredMethod("setHour" + i, Integer.class);
                ReflectionUtils.makeAccessible(method);
                method.invoke(receiveSessionNumHourly, count);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        receiveSessionNumHourlyDao.insert(receiveSessionNumHourly, schemaId);

        if (CollectionUtils.isNotEmpty(receiveSessionPressures)) {
            receiveSessionPressureDao.insertBatch(receiveSessionPressures, adjustDate, schemaId);
            receiveSessionPressures.sort((x, y) -> {
                if (x.getReceiveSessionNum() > y.getReceiveSessionNum()) {
                    return -1;
                }
                if (Objects.equals(x.getReceiveSessionNum(), y.getReceiveSessionNum())) {
                    return 0;
                }
                return 1;
            });

            Integer receiveSessionNum = receiveSessionPressures.get(0).getReceiveSessionNum();
            return receiveSessionNum;
        }
        return 0;
    }

    /**
     * 接待压力
     */
    private int calTimeSharing(Date timePoint, List<TimeSection> timeSections) {
        int count = 0;
        for (TimeSection timeSection : timeSections) {
            if (timeSection.include(timePoint)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 计算数据
     */
    private void calChatSessionData(ShopSystemsettingDTO shopSystemsetting,
                                    List<CsChatlogDTO> chatlogDTOS,
                                    CsChatSessionDTO csChatSession) {
        /*慢响应时间*/
        final Integer slowResponseTime = shopSystemsetting.getSlowResponseTime();
        final long slowResponseTimeMillis = TimeUnit.SECONDS.toMillis(slowResponseTime);
        /*慢响应次数*/
        final Integer slowResponseTimesNum = shopSystemsetting.getSlowResponseTimesNum();
        /*最长等待时间*/
        final long maxWaitTime = TimeUnit.MINUTES.toMillis(shopSystemsetting.getMaxWaitTime());
        /*长接待设定*/
        final long longReceptionTime = TimeUnit.MINUTES.toMillis(shopSystemsetting.getLongReceptionTime());
        int slowCount = 0;
        /*标记上一次是谁说话*/
        String lastSay = null;
        final String csSayFlag = "CS_SAY";
        final String buyerSayFlag = "BUYER_SAY";
        /*首次，最后一次接待时间*/
        long firstReceiveTime = 0, lastReceiveTime = 0;
        /*计算首次响应时间（买家第一次说话时间，如果接待时间差超过最长等待时间，
        那此值，不会是'买家第一次说话的时间'，而是过滤掉超过最长等待时间的第一次接待的买家说话时间）*/
        long buyerFirstSendTime = 0, csFirstSendTime = 0;
        /*第一句发送的时间点   最后一句发送的时间点  会话回合数 每次会话买家说话到客服回复间隔时间(秒)*/
        long sendFirstTime = 0L, sendLastTime = 0L, sessionCount = 0L, sessionTimeCount = 0L;
        /*客服消息数 买家消息数 */
        int csChatNum = 0, buyerChatNum = 0;
        /*买家还是顾客发起 客服字数*/
        int receiveStartType = 0, csWordNum = 0;
        /* 标记买家说过话 只要有买家说话，客服的chatLog都为回复*/
        boolean buyerChated = false;
        /*客服人工回复数量*/
        int buyerChatReply = 0;
        /*chatlog 开始时间和结束时间*/
        Date beginReceive = null;
        Date endReceive = null;
        /*初始化属性*/
        {
            csChatSession.setReceive(false);
            csChatSession.setSlowResp(false);
        }
        /*找第一个买家说话的记录*/
        boolean justOnce = true;
//		客服首次回复--不算自动回复
        boolean justOnceOfCs = true;
        boolean lmgFlag = true;
//        首次超过最大等待时间的标识
        boolean gtMaxWaitTimeOfFirst = false;
        //第一次进去算首响
        boolean firstCalRespOfFirst = true;
        /*买家发送聊天的时间*/
        Date buyerSendLogTime = null;
        long maxSlowRespTime = 0L;
        if (CollectionUtils.isNotEmpty(chatlogDTOS)) {
            /*查询过滤，如果过滤了就不用计算*/

            for (CsChatlogDTO chatLog : chatlogDTOS) {
                final boolean csAutoReplyBySysettingChat =
                        performanceRuleBusiness.isCsAutoReplyBySysettingChat(shopSystemsetting, chatLog.getMt(), chatLog.getContent());
                final boolean csSay = csSay(chatLog);//此句是否为客服说话
                final boolean buyerSay = buyerSay(chatLog);//此句是否为买家说话
                final long thisChatLogTime = chatLog.getChatTime().getTime();//此句聊天时间点
                if (justOnce) {
                    sendFirstTime = thisChatLogTime;
                    receiveStartType = csSay ? 1 : 2;
                    beginReceive = chatLog.getChatTime();
                }
                endReceive = chatLog.getChatTime();
                /*最后发送聊天时间*/
                sendLastTime = thisChatLogTime;
                if (!csAutoReplyBySysettingChat) {
                    csChatNum += csSay ? 1 : 0;
                }
                buyerChatNum += buyerSay ? 1 : 0;
                if (csSay) {
                    if (csChatSession.getSessionType() == 2 && lmgFlag) {
                        // 留言的话需要将客服回复第一句话设置为 sessionBeginTime
                        csChatSession.setSessionBeginTime(chatLog.getChatTime());
                        lmgFlag = false;
                    }
                    /*过滤自动回复*/
                    if (csAutoReplyBySysettingChat) {
                        continue;
                    }
                    /*客服的首次回复时间-排除自动回复*/
                    if (justOnceOfCs && buyerFirstSendTime != 0) {
                        csFirstSendTime = chatLog.getChatTime().getTime();
                        justOnceOfCs = false;
                    }
                    /*设置未回复*/
                    boolean notNonReply = csChatSession.getSessionType().equals(CommonConstants.CHAT_SESSION_TYPE_ONLINE);
                    if (notNonReply) {//未回复
                        csChatSession.setNonReply(false);
                    }

                    /*客服主动跟进*/
                    csChatSession.setCsToCustSessionNum(true);
                    /*客服字数*/
                    int csWordNum1 = chatLog.getContent().length() == 0 ? 1 : chatLog.getContent().length();
                    csWordNum += csWordNum1;
                    if (buyerChated) {
                        buyerChatReply++;
                    }
                    /*上次如果是买家说话*/
                    if (Objects.equals(lastSay, buyerSayFlag)) {
                        /*设置接待*/
                        csChatSession.setReceive(true);

                        assert buyerSendLogTime != null;
                        /*最近对话回合的耗时*/
                        final long recentlySessionTime = (thisChatLogTime - buyerSendLogTime.getTime());
                        /*表示这次接待 超过了最长等待时间*/
                        final boolean gtMaxWaitTime = recentlySessionTime >= maxWaitTime;
                        if (!gtMaxWaitTime) {
                            /*首次接待时间*/
                            if (firstReceiveTime == 0) {
                                firstReceiveTime = thisChatLogTime;
                            }
                            /*会话回合加1*/
                            ++sessionCount;
                            /*将每回合 买家说话到客服说话的间隔时间 都加起来，计算平均响应时间*/
                            sessionTimeCount += TimeUnit.MILLISECONDS.toSeconds(recentlySessionTime);
                        } else {
                            /*表示超过了最长等待时间 首次超过最大等待时间才会影响首响*/
                            if (buyerFirstSendTime != 0 && firstCalRespOfFirst && gtMaxWaitTimeOfFirst) {
                                buyerFirstSendTime = 0;
                            }
                            gtMaxWaitTimeOfFirst = false;
                        }
//                        /*最后接待时间*/
//                        lastReceiveTime = thisChatLogTime;
                        /*计算慢响应，如果响应时间大于设定时间，并且这种情况的次数大于设定次数，则为慢响应*/
                        //feature18 留言不算慢响应
                        // 超过最长等待时间就不算慢响应
                        if (csChatSession.getSessionType() != 2 && recentlySessionTime < longReceptionTime &&
                                recentlySessionTime > slowResponseTimeMillis &&
                                ++slowCount >= slowResponseTimesNum) {
                            csChatSession.setSlowResp(true);
                            if (recentlySessionTime > maxSlowRespTime) {
                                maxSlowRespTime = recentlySessionTime;
                            }
                        }
                    }
                    firstCalRespOfFirst = false;
                    buyerSendLogTime = null;
                } else {
                    /*设置咨询*/
                    csChatSession.setConsult(true);
                    /*如果买家发送聊天时间为空，那么设置属性值*/
                    if (buyerSendLogTime == null) {
                        buyerSendLogTime = chatLog.getChatTime();
                    }
                    if (buyerFirstSendTime == 0) {
                        buyerFirstSendTime = thisChatLogTime;
                    }
                    buyerChated = true;
                    if (justOnce) {
                        csChatSession.setNonReply(csChatSession.getSessionType() == 1);
                    }
                }
                /*标记此次说话是谁说的*/
                lastSay = csSay ? csSayFlag : buyerSayFlag;
                justOnce = false;
            }
        }
        /*处理结束时间*/
        csChatSession.setSessionEndTime(specProcessSessionEndTime(csChatSession, chatlogDTOS));
        // 如果开始时间大于结束时间，将开始时间赋值为结束时间
        if (csChatSession.getSessionEndTime() != null) {
            if (csChatSession.getSessionEndTime().getTime() < csChatSession.getSessionBeginTime().getTime()) {
                csChatSession.setSessionBeginTime(csChatSession.getSessionEndTime());
            }
        }

        /*接待会话时长*/
        long receiveTime = 0;
        if (beginReceive != null && endReceive != null) {
            receiveTime = TimeUnit.MILLISECONDS.toSeconds(endReceive.getTime() - beginReceive.getTime());
        }
        csChatSession.setSessionReceiveDurationTime(receiveTime < 0 ? receiveTime : 0D);
        /*会话时长 s*/
        final Date sessionEndTime = csChatSession.getSessionEndTime();
        final Date sessionBeginTime = csChatSession.getSessionBeginTime();
        if (sessionBeginTime != null && sessionEndTime != null) {
            csChatSession.setSessionDurationTime((double) TimeUnit.MILLISECONDS.toSeconds(sessionEndTime.getTime() - sessionBeginTime.getTime()));
        } else {
            if (sessionEndTime == null) {
                csChatSession.setSessionEndTime(sessionBeginTime);
                csChatSession.setSessionDurationTime((double) 0);
            }
            logger.warn("shopId: {} ,nick: {} , sid: {} , {} = null , please check data chat log!!!!! ",
                    csChatSession.getShopId(),
                    csChatSession.getCsNick(),
                    csChatSession.getSid(),
                    sessionBeginTime == null ? "sessionBeginTime" : "sessionEndTime");
        }
        //慢响应次数
        csChatSession.setSlowRespNum(slowCount);
        //最长慢响应时间
        csChatSession.setMaxSlowRespTime((double) maxSlowRespTime);
        /*长接待*/
        csChatSession.setLongReceive(csChatSession.getSessionDurationTime() >= TimeUnit.MILLISECONDS.toSeconds(longReceptionTime));
        /*买家客服消息数*/
        csChatSession.setCsChatNum(csChatNum);
        csChatSession.setCustChatNum(buyerChatNum);
        /*总消息数*/
        csChatSession.setChatNum(csChatNum + buyerChatNum);
        /*会话发起方*/
        csChatSession.setReceiveStartType(receiveStartType);
        /*首次响应时间  (buyerFirstSendTime == 0 表示超过了最长等待时间)*/
//        final double resFirst = buyerFirstSendTime == 0 ? 0 : TimeUnit.MILLISECONDS.toSeconds((firstReceiveTime - buyerFirstSendTime));
        //        首次响应时间=首次接待的时间-会话创建的时间
//        long sessionBeginTimeTime = 0L;
//        if (sessionBeginTime != null) {
//            sessionBeginTimeTime = sessionBeginTime.getTime();
//        }
        //客服首次回复在会话开始之后并且不是未回复的会话算首次平均响应时间
        double resFirst = buyerFirstSendTime == 0 ? 0 :
                ((csFirstSendTime - buyerFirstSendTime) < 0 || csChatSession.getNonReply() ? 0 : TimeUnit.MILLISECONDS.toSeconds((csFirstSendTime - buyerFirstSendTime)));
        if (resFirst > maxWaitTime / 1000) {
            resFirst = 0;
        }
        csChatSession.setAvgRespTimeFirst(csChatSession.getSessionType() == 2 || resFirst < 0 ? 0 : resFirst);
        /*平均响应时间*/
//        买家说话到客服说话的间隔时间/回合数
        csChatSession.setAvgRespTime((csChatSession.getSessionType() == 2 || sessionCount == 0 || sessionTimeCount == 0) ? 0D
                : (double) sessionTimeCount / sessionCount);
        /*客服字数*/
        csChatSession.setCsWordNum(csWordNum);
        /*人工回复消息数量*/
        csChatSession.setBuyerChatReply(buyerChatReply);
        /*会话回合数 每次会话买家说话到客服回复间隔时间*/
        csChatSession.setSessionCount(sessionCount);
        csChatSession.setSessionTimeCount(sessionTimeCount);
        csChatSession.setBeginDatetime(beginReceive);
        csChatSession.setEndDatetime(endReceive);
    }

    private class TimeSection {
        Date begin;
        Date end;
        int minute;

        TimeSection(Date begin, Date end, int minute) {
            this.begin = begin;
            this.end = end;
            this.minute = minute;
        }

        /**
         * 该时间点的前后指定分钟，是否和开始时间和结束时间有交集
         */
        public boolean include(Date date) {
            /*前后时间节点*/
            long b = DateUtils.addMinutes(date, -minute).getTime();
            long a = DateUtils.addMinutes(date, minute).getTime();
            return intersection(b, a);
        }


        /**
         * 判断该日期的 hour到hour+1 时，是否和开始时间和结束时间有交集
         *
         * @param date
         * @param hour
         * @return
         */
        public boolean include(Date date, int hour) {
            final Date bDate = hour == 0 ? date : DateUtils.setHours(date, hour);
            final Date aDate = hour + 1 == 24 ? DateUtils.addDays(date, 1) : DateUtils.setHours(date, hour + 1);
            return intersection(bDate.getTime(), aDate.getTime());
        }

        /**
         * 判断交集
         *
         * @param b 开始节点
         * @param a 结束节点
         * @return
         */
        private boolean intersection(long b, long a) {
            if (begin == null || end == null) {
                return false;
            }
            final long beginTime = begin.getTime();
            final long endTime = end.getTime();
            if (beginTime > a || endTime < b) {
                return false;
            }
            return true;
        }

    }

    /**
     * 时间排序 升序
     */
    private Comparator<ChatlogDTO> dateSort = (a, b) -> {
        Date x = a.getChatTime();
        Date y = b.getChatTime();
        if (x == null || y == null || x.before(y)) {
            return -1;
        }
        if (x.equals(y)) {
            return 0;
        }
        return 1;
    };

    private boolean csSay(CsChatlogDTO chatlogDTO) {
        return chatlogDTO.getDirection() == 0;
    }

    private boolean buyerSay(CsChatlogDTO chatlogDTO) {
        return chatlogDTO.getDirection() == 1;
    }

    /**
     * 询单聊天处理
     * 客服和订单的关系处理好后，从接待的客户里，计算出询单买家
     *
     * @param isDelData
     */
    @Override
    public void handleEnquiryChat(JobShopQuery jobShop, JobDateQuery jobDate,
                                  boolean isDelData) {

        long s = System.currentTimeMillis();

        List<Date> dates = jobDate.getCsEnquiryDates();
        if (dates.isEmpty()) {
            logger.warn("req dates is empty");
            return;
        }
        JobShopDTO shop = jobShop.getShop();
        // 按每日维度
        for (Date date : dates) {

            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }

            List<AsyncTask> taskList = new ArrayList<>(targetCsLst.size());
            int task = 0;
            for (CsDTO cs : targetCsLst) {

                AsyncTask asyncTask = new AsyncTask(task) {
                    @Override
                    public Object run() {
                        String csNick = cs.getNick();

                        //接待的客户
                        List<ReceivedChatpeerDTO> chatpeers = csChatpeerDao.selectShopCsChatpeerLstByDateForEnquiry(shop, date, csNick);

                        if (CollectionUtils.isNotEmpty(chatpeers)) {
                            for (ReceivedChatpeerDTO cp : chatpeers) {
                                // 初始化默认值
                                initChatpeerInfo(cp);
                            }

                            // @TODO(单个客服接待的买家订单指标
                            List<CsOrderIndexDTO> csOrderIndexLst = csOrderIndexDao
                                    .selectShopCsOrderIndexLstForEnquiry(jobShop.getShop(), date, csNick);

                            // TODO(计算询单)
                            judgeBuyerEnquiry(jobShop, cs, date, chatpeers, csOrderIndexLst);

                            // TODO(更新接待对象的询单指标 )
                            int num = csChatpeerDao.updateChatpeerInfoForEnquiry(shop, date, chatpeers);
                            if (logger.isDebugEnabled()) {
                                logger.debug("batch update Chatpeer receive index num:{}", num);
                            }
                        }
                        return csNick;
                    }
                };
                taskList.add(asyncTask);
                task++;
            }
            if (CollUtil.isNotEmpty(taskList)) {
                Object[] objects = AsyncTaskUtil.runAll(taskList);
                Arrays.stream(objects).filter(Objects::nonNull).forEach(callBack -> {
                    String csNick = (String) callBack;
                });
            }
            //@TODO处理预售付尾款时间跟chatpeer首次聊天时间相等的情况为售后
            List<ReceivedChatpeerDTO> chatpeer = balancePayAfterSaleHandle(shop, date);
            //@TODO处理预售付尾款时间跟chatpeer首次聊天时间相等的情况为售后
            if (chatpeer == null) return;
            csChatpeerDao.batchUpdateAfterSale(shop, date, chatpeer.stream().filter(ele -> Objects.nonNull(ele.getAfterSale()) && ele.getAfterSale()).collect(Collectors.toList()));
        }
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("handle shop EnquiryChat end,time:{}s", (e - s) / 1000);
        }
    }

    /**
     * 处理预售付尾款时间跟chatpeer首次聊天时间相等的情况为售后
     *
     * @param shop
     * @param date
     * @return
     */
    private List<ReceivedChatpeerDTO> balancePayAfterSaleHandle(JobShopDTO shop, Date date) {
        List<ReceivedChatpeerDTO> chatpeer = new ArrayList<>();
        List<CsOrderIndexDTO> csBuyerOrderIndexLst = csOrderIndexDao.selectBalancePayShopCsOrderIndexLstByDate(shop, date);
        if (CollUtil.isEmpty(csBuyerOrderIndexLst)) return chatpeer;
        chatpeer = csChatpeerDao.selectReceiveChatpeersByShopAndDateAndBuyerAndCustomer(shop, date, csBuyerOrderIndexLst);
        if (CollUtil.isEmpty(chatpeer)) return chatpeer;
        Map<String, List<CsOrderIndexDTO>> collect = csBuyerOrderIndexLst.stream().collect(Collectors.groupingBy(ele -> ele.getCsNick() + DELIMITER + ele.getBuyerNick()));
        chatpeer.forEach(ele -> {
            List<CsOrderIndexDTO> tLst = collect.get(ele.getCsNick() + DELIMITER + ele.getBuyerNick());
            if (CollUtil.isNotEmpty(tLst)) {
                for (CsOrderIndexDTO csOrderIndexDTO : tLst) {
                    if (Objects.nonNull(csOrderIndexDTO.getOrderPayDate()) &&
                            Objects.nonNull(ele.getFirstChatDate()) &&
                            csOrderIndexDTO.getOrderPayDate().getTime() == ele.getFirstChatDate().getTime()) {
                        ele.setAfterSale(Boolean.TRUE);
                        break;
                    }
                }
            }
        });
        return chatpeer;
    }


    /**
     * 处理最终
     * 客服和订单的关系处理好后，从接待的客户里，计算出询单买家
     * @param shop
     * @param startDate
     * @param endDate
     * @param isDelData
     */
    /**
     * 处理最终
     * 客服和订单的关系处理好后，从接待的客户里，计算出询单买家
     */
    @Override
    public void handleFinalData(JobShopQuery jobShop, JobDateQuery jobDate) {

        long s = System.currentTimeMillis();

        List<Date> dates = jobDate.getCsFinalDataDates();
        if (dates.isEmpty()) {
            logger.warn("req dates is empty");
            return;
        }
        JobShopDTO shop = jobShop.getShop();

        // 按每日维度
        for (Date date : dates) {

            JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }

            Date endDate = DateUtil.getEndTimeOfDate(date);

            for (CsDTO cs : targetCsLst) {
                String csNick = cs.getNick();

                //接待的客户
                List<SimpleChatpeerDTO> csChatpeers = csChatpeerDao.selectShopCsOrderUnCreatedChatpeerLst(shop, date, csNick);

                if (CollectionUtils.isNotEmpty(csChatpeers)) {

                    //初始化数据
                    for (SimpleChatpeerDTO cp : csChatpeers) {
                        cp.setOrderCreated(Boolean.FALSE);
                        cp.setCsActiveChatFail(Boolean.FALSE);
                        cp.setCsActiveUrgepayFail(Boolean.FALSE);
                        cp.setCrossChatFail(Boolean.FALSE);
                    }

                    List<String> buyerNickLst = csChatpeers.stream()
                            .map(SimpleChatpeerDTO::getBuyerNick)
                            .collect(Collectors.toList());

                    // @TODO(单个客服接待的买家订单指标
                    List<SimpleOrderIndexDTO> csOrderIndexLst = csOrderIndexDao
                            .selectShopCsOrderIndexOrderIdLstByCsAndDateAndBuyerLst(shop, date, date, endDate, csNick, buyerNickLst);

                    List<SimpleChatpeerDTO> needUpdatedCpLst = calFinalData(jobShop, csChatpeers, csOrderIndexLst);

                    // TODO(更新接待对象的询单指标 )
                    csChatpeerDao.updateChatpeerInfoForFinalData(shop, date, needUpdatedCpLst);
//					logger.debug("batch update Chatpeer final data num:{}", num);
                }
            }
        }

        long e = System.currentTimeMillis();
        logger.info("handle shop EnquiryChat end,time:{}s", (e - s) / 1000);
    }


    private void initChatpeerInfo(ReceivedChatpeerDTO cp) {
        cp.setIsAfterSale(false);
        cp.setIsCsConsultFirst(false);
        cp.setIsCsSingleChatFilter(false);
        cp.setIsAssist(false);
        cp.setIsNextDayPes(false);
        cp.setIsOrderCreated(false);
        cp.setIsEnquiry(false);
        cp.setIsNextDayPes(false);
    }


    /**
     * 询单指标计算
     *
     * @param date
     */
    private void judgeBuyerEnquiry(
            JobShopQuery jobShop,
            CsDTO cs,
            Date date,
            //当天接待的顾客
            List<ReceivedChatpeerDTO> chatpeers,
            //客服维度下，按买家维度拆分
            List<CsOrderIndexDTO> csOrderIndexLst) {
        if (CollectionUtils.isEmpty(chatpeers)) {
            return;
        }

        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        Boolean crossChatLossSwitch = sys.getEnquiryLossSwitch();

        Boolean activeFollowUpSwitch = sys.getActiveFollowUpSwitch();//客服主动跟进不算询单流失开关

        Boolean silentUrgepaySwitch = sys.getSilentUrgepaySwitch();

        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();

        Boolean orderFlagSwitch = sys.getOrderFlagSwitch();//插旗过滤开关

        //咨询当天操作过将顾客转发给售后客服行为的售前客服，且顾客询单时长内未下单，
        // 此顾客将不会计为此售前客服的询单人数（如符合转发过滤规则，优先转发过滤规则）
        //Boolean csForwardAftersellSwitch = sys.getCsForwardAftersellSwitch();
        //售后账号不参与过滤
        final boolean csForwardAftersellSwitch = (!sys.getAftersellAcountFilter() || cs.getType() == 1)
                && BooleanUtils.toBooleanDefaultIfNull(sys.getCsForwardAftersellSwitch(), false);

        Integer orderFlag = sys.getOrderFlag();

        // 客服维度下，按买家维度拆分
        Map<String, List<CsOrderIndexDTO>> buyerOrderIndexMap = Optional.ofNullable(csOrderIndexLst)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.groupingBy(e -> e.getBuyerNick().toLowerCase()));

        // 1.维护订单是否创建字段 2.根据所有下单指标统计是否是售后订单
        chatpeersIsOrderCreateAndAfterSaleHandle(date, chatpeers, enquiryValidDays, buyerOrderIndexMap);

        /*
         * 所有接待人数-已经下单的人数 的这些买家里面有一些是售后咨询的买家，需要过滤掉。
         * 查询这些买家在用户自定义的售后时间内有没有在店铺内下过单，如果有的话，则不计入询单人数
         */
        bySellAfterDoAfterSaleHandle(date, chatpeers, shop, sys);

        /*
         * @TODO
         *       ①付完定金售后有效期再来咨询的算售后服务
         *       ②预售全静默付全款之后再来咨询算售后
         */
        presaleAfterSaleHandle(date, chatpeers, shop, sys);

        List<String> nonOrderBuyerLst = Lists.newArrayList();
        //TODO(询单无下单)
        for (ReceivedChatpeerDTO cp : chatpeers) {
            if (cp.getIsReceive() && !cp.getIsOrderCreated()) {
                if (!cp.getIsAfterSale()) {

                    if (!cp.getCrossChat()) {
                        cp.setIsEnquiry(Boolean.TRUE);
                        nonOrderBuyerLst.add(cp.getBuyerNick());
                    } else {
                        cp.setIsEnquiry(Boolean.FALSE);
                    }
                }

                //售前将顾客转发给售后客服    不算询单 算售后
                if (csForwardAftersellSwitch && cp.getForwardFlag() == CommonConstants.PRESALE_FORWORD2_AFTERSALECS) {
                    cp.setIsEnquiry(Boolean.FALSE);
                    cp.setIsAfterSale(Boolean.TRUE);
                }
            }
        }
        //中间变量
        List<CsOrderIndexDTO> buyerOrderIndexLst;
        //有下单
        // 当天咨询有效期内下单的买家 算询单
        for (ReceivedChatpeerDTO cp : chatpeers) {

            if (cp.getIsReceive() && !cp.getIsAfterSale()) {
                buyerOrderIndexLst = buyerOrderIndexMap.get(cp.getBuyerNick());
                if (buyerOrderIndexLst != null) {
                    boolean existNotAssitOrder = Boolean.FALSE;
                    boolean existNotFlagOrder = Boolean.FALSE;

                    if (orderFlagSwitch) {
                        for (CsOrderIndexDTO orderIndex : buyerOrderIndexLst) {
                            if (!orderFlag.equals(orderIndex.getOrderFlag())) {
                                existNotFlagOrder = Boolean.TRUE;
                            }
                        }
                    }


                    for (CsOrderIndexDTO orderIndex : buyerOrderIndexLst) {
                        if (orderIndex.getBcFirstChatDate() != null) {

                            //TODO(判定客服是否聊天，来算询单 ,只要存在一个单在下单前说话，就算询单)
                            if (orderIndex.getAssitOrderCreate() != null && !orderIndex.getAssitOrderCreate()) {
                                existNotAssitOrder = Boolean.TRUE;
                                break;
                            }
                        } else if (orderIndex.getBcFirstReplyDate() != null) {

                            //TODO(判定客服是否聊天，来算询单 ,只要存在一个单在下单前说话，就算询单)
                            if (orderIndex.getAssitOrderCreate() != null && !orderIndex.getAssitOrderCreate()) {//不是协助的订单
                                existNotAssitOrder = Boolean.TRUE;
                                break;
                            }
                        }
                    }

                    boolean isChatBeforeOrderCreated = Boolean.FALSE;
                    for (CsOrderIndexDTO orderIndex : buyerOrderIndexLst) {
                        if (orderIndex.getBcLastReplyDate() != null
                                && orderIndex.getBcLastReplyDate().compareTo(date) >= 0
                                && orderIndex.getAssitOrderCreate() != null && !orderIndex.getAssitOrderCreate()) {

                            isChatBeforeOrderCreated = Boolean.TRUE;
                            break;
                        }
                    }

                    if (isChatBeforeOrderCreated && existNotAssitOrder && !cp.getCrossChat()) {
                        if (orderFlagSwitch) {
                            if (existNotFlagOrder) {
                                cp.setIsEnquiry(Boolean.TRUE);
                            }
                        } else {
                            cp.setIsEnquiry(Boolean.TRUE);
                        }

                    }
                }
                if(cp.getCrossChat() && cp.getIsOrderCreated() && cp.getBuyerNick().equals("caqna") && !cp.getIsAfterSale()){
                    cp.setIsEnquiry(Boolean.TRUE);
                }
            }
        }

        //--------------------------------------- 计算最终失败数据 ----------------------------------------------------------//

        if (PerformanceRuleValidDateBO.isBeforeOrEqualsEnquiryLimitDate(date, enquiryValidDays)) {

            if (silentUrgepaySwitch) {
                // @TODO(处理主动催付失败)
                List<CsOrderIndexDTO> buyerOrderIndexLsts;
                CsOrderIndexDTO targetOrder;
                for (ReceivedChatpeerDTO cp : chatpeers) {

                    if (!cp.getAfterSale()) {

                        buyerOrderIndexLsts = buyerOrderIndexMap.get(cp.getBuyerNick());
                        if (CollectionUtils.isNotEmpty(buyerOrderIndexLsts)) {
                            targetOrder = buyerOrderIndexLsts.get(0);
                            for (int i = 1, size = buyerOrderIndexLsts.size(); i < size; i++) {
                                //看最后一单状态，最后一单，付了款那就催付成功
                                if (buyerOrderIndexLsts.get(i).getOrderCreated().after(targetOrder.getOrderCreated())) {
                                    targetOrder = buyerOrderIndexLsts.get(i);
                                }
                            }
                            if (targetOrder.getOrderPayDate() == null) {
                                //0:没有催付,1:客服主动催付,2:非客服主动催付
                                if (targetOrder.getUrgepayFlag() == 1) {
                                    cp.setCsActiveUrgepayFail(Boolean.TRUE);
                                }
                            }
                        }
                    }
                }
            }

            if (crossChatLossSwitch) {
                // @TODO(处理跨天聊天失败)
                for (ReceivedChatpeerDTO chatpeer : chatpeers) {
                    if (!chatpeer.getAfterSale()) {
                        if (chatpeer.getCrossChat()) {
                            chatpeer.setCrossChatFail(!chatpeer.getOrderCreated());//跨天聊天失败
                            if (chatpeer.getCrossChatFail()) {
                                chatpeer.setEnquiry(Boolean.FALSE);
                            }
                        }
                    }
                }
            }

            if (activeFollowUpSwitch) {
                // @TODO(处理主动聊天失败)
                for (ReceivedChatpeerDTO chatpeer : chatpeers) {
                    if (!chatpeer.getAfterSale()) {//售前接待
                        if (chatpeer.getCsChatFirstFlag() == 1) {//主动联系

                            chatpeer.setCsActiveChatFail(!chatpeer.getOrderCreated());//主动联系成功或者失败
                            if (chatpeer.getCsActiveChatFail()) {
                                chatpeer.setEnquiry(Boolean.FALSE);
                            }
                        }
                    }
                }
            }
            //------------------------------------------------------------------------------------------------//
        }
    }

    /**
     * ①付完定金售后有效期再来咨询的算售后服务
     * ②预售全静默付全款之后再来咨询算售后
     *
     * @param date
     * @param chatpeers
     * @param shop
     * @param sys
     */
    private void presaleAfterSaleHandle(Date date,
                                        List<ReceivedChatpeerDTO> chatpeers,
                                        JobShopDTO shop,
                                        ShopSystemsettingDTO sys) {
        if (CollUtil.isEmpty(chatpeers)) {
            return;
        }
        Date afterSaleStartDate = DateUtil.getDateByPeriod(date, sys.getSellAfter() * (-1));
        Date afterSaleEndDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(date, -1));

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(afterSaleStartDate);
        validDateRange.setEndDate(afterSaleEndDate);
        //查询售后天数内付定金的指标
        Optional<List<CsOrderBindInfoDTO>> optional = Optional.ofNullable(csOrderBindDao.selectBargainPaymentPresaleBindByOrderPayDate(shop, validDateRange, chatpeers.stream().filter(ReceivedChatpeerDTO::getIsReceive).map(ReceivedChatpeerDTO::getBuyerNick).collect(Collectors.toList())));
        if (optional.isPresent()) {
            List<CsOrderBindInfoDTO> csOrderBindLst = optional.get();
            Map<String, List<CsOrderBindInfoDTO>> buyerMap = csOrderBindLst.stream().collect(Collectors.groupingBy(CsOrderBindInfoDTO::getBuyerNick));
            //设置预售的有效时间
            Date presaleStartDate = DateUtil.getDateByPeriod(date, -ValidDateRangeQuery.validPresaleOrderBalancePayDays);
            //(询单有效时长)
            Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
            Date presaleEndDate = DateUtil.getDateByPeriod(date, enquiryValidDays == null ? 0 : enquiryValidDays - 1);
            //全款支付的订单
            List<Long> fullPayOrder = new ArrayList<>();
            //fix:查询预售付尾款的订单信息
            Optional<List<PresaleOrderDTO>> pOptional = Optional.ofNullable(presaleOrderDao.selectPresaleOrderByOrderId(shop, csOrderBindLst.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList()), presaleStartDate, presaleEndDate));
            if (pOptional.isPresent()) {
                fullPayOrder = pOptional.get().stream().filter(ele -> ele.getOrderPayType() == 1).map(PresaleOrderDTO::getOrderId).collect(Collectors.toList());
            }
            //如果当天有下单记录，则不算售后
            Set<String> todayBuyerFilter = null;
            List<String> todayBuyers = csOrderBindLst.stream().map(CsOrderBindInfoDTO::getBuyerNick).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(todayBuyers)){
                List<CsOrderBindInfoDTO> todayBinds = csOrderBindDao.selectPaymentBindToday(shop, date, todayBuyers);
                todayBuyerFilter = todayBinds.stream().map(CsOrderBindInfoDTO::getBuyerNick).collect(Collectors.toSet());
            }

            for (ReceivedChatpeerDTO chatpeer : chatpeers) {
                if (!chatpeer.getIsReceive()) {
                    return;
                }
                String buyerNick = chatpeer.getBuyerNick();
                Optional<List<CsOrderBindInfoDTO>> bindOptional = Optional.ofNullable(buyerMap.get(buyerNick));
                if (bindOptional.isPresent()) {
                    for (CsOrderBindInfoDTO bind : bindOptional.get()) {
                        //当天有下单，则不算售后
                        if(todayBuyerFilter != null && todayBuyerFilter.contains(buyerNick)) continue;
                        if (
                            //聊天时间在付定金之后就是售后
                                (null != chatpeer.getLastChatDate()
                                        && null != bind.getOrderPayDate()
                                        && chatpeer.getLastChatDate().after(bind.getOrderPayDate()))
                                        ||
                                        //聊天时间在付尾款之后就是售后
                                        (null != chatpeer.getLastChatDate()
                                                && null != bind.getOrderValidPayTime()
                                                && chatpeer.getLastChatDate().after(bind.getOrderValidPayTime()))
                                        ||
                                        //静默付全款之后再聊天算售后
                                        (bind.getType() != null && bind.getType() == ORDER_BIND_TYPE_SILENTALL.getType() && fullPayOrder.contains(bind.getOrderId()))
                        ) {
                            chatpeer.setAfterSale(true);
                            break;
                        }
                    }
                }
            }
        }
    }

    private void bySellAfterDoAfterSaleHandle(Date date, List<ReceivedChatpeerDTO> chatpeers,
                                              JobShopDTO shop,
                                              ShopSystemsettingDTO sys) {
        List<String> notOrderBuyerLst = new ArrayList<>();
        for (ReceivedChatpeerDTO cp : chatpeers) {
            if (cp.getIsReceive()
                    && !cp.getIsOrderCreated()
                    && !cp.getIsAfterSale()) {
                notOrderBuyerLst.add(cp.getBuyerNick());
            }
        }
        if (CollectionUtils.isEmpty(notOrderBuyerLst)) {
            return;
        }
        // 售后日期要跟系统设定绑定(售后有效期内是否有付款行为)
        Date afterSaleStartDate = DateUtil.getDateByPeriod(date, sys.getSellAfter() * (-1));
        Date afterSaleEndDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(date, -1));

        ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
        //设置询单有效的开始时间
        validDateRange.setStartDate(afterSaleStartDate);
        validDateRange.setEndDate(afterSaleEndDate);


        //查询预售付定金
        List<BuyerOrderDTO> presaleBargainPaidOrderLst = presaleOrderDao.selectShopBargainPaidOrderLstByBuyersAndDateForCalEnquiry(shop, notOrderBuyerLst, validDateRange);

        Set<BuyerOrderDTO> buyerOrderSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(presaleBargainPaidOrderLst)) {
            for (BuyerOrderDTO order : presaleBargainPaidOrderLst) {
                order.setPreSale(Boolean.TRUE);//预售
            }
            buyerOrderSet.addAll(presaleBargainPaidOrderLst);
        }

        //查询订单表
        List<BuyerOrderDTO> ordinaryBuyerOrderLst = orderDao.selectShopOrderLstByBuyersAndDateForAfterSale(shop, notOrderBuyerLst, validDateRange);

        if (CollectionUtils.isNotEmpty(ordinaryBuyerOrderLst)) {
            buyerOrderSet.addAll(ordinaryBuyerOrderLst);
        }

        if (CollectionUtils.isNotEmpty(buyerOrderSet)) {
            Set<String> afterSaleBuyerSet = Sets.newHashSet();
            for (BuyerOrderDTO order : buyerOrderSet) {
                if (order.getPreSale()) {
                    afterSaleBuyerSet.add(order.getBuyerNick().toLowerCase());
                } else {
                    // 如果买家在售后期内有下单并付款（货到付款），则不计入询单人数
                    if ((order.getPayType() != null && order.getPayType() == 1) || order.getPayTime() != null) {
                        afterSaleBuyerSet.add(order.getBuyerNick().toLowerCase());
                    }
                }

            }

            for (ReceivedChatpeerDTO cp : chatpeers) {
                if (afterSaleBuyerSet.contains(cp.getBuyerNick())) {
                    cp.setIsAfterSale(Boolean.TRUE);
                }
            }
        }
    }

    private void chatpeersIsOrderCreateAndAfterSaleHandle(Date date, List<ReceivedChatpeerDTO> chatpeers, Integer enquiryValidDays, Map<String, List<CsOrderIndexDTO>> buyerOrderIndexMap) {
        final long dayMilliseconds = 86400000;
        for (ReceivedChatpeerDTO cp : chatpeers) {
            if (buyerOrderIndexMap.containsKey(cp.getBuyerNick())) {
                List<CsOrderIndexDTO> tempLst = buyerOrderIndexMap.get(cp.getBuyerNick());
                if (CollectionUtils.isNotEmpty(tempLst)) {

                    //设置询单有效期内的下单标志
                    for (CsOrderIndexDTO csOrderIndex : tempLst) {
                        if (csOrderIndex.getOrderCreated().getTime() - date.getTime() < enquiryValidDays * dayMilliseconds) {
                            cp.setIsOrderCreated(Boolean.TRUE);//询单有效期内下单
                        }
                    }

                    int size = tempLst.size();
                    boolean isAfterSale = Boolean.TRUE;
                    int cancelIndexNum = 0;

                    for (CsOrderIndexDTO csOrderIndex : tempLst) {
                        if (csOrderIndex.getAfterSale() != null && !csOrderIndex.getAfterSale()) {

                            if (OrderStatusEnum.TRADE_CANCELED.getStatus().equals(csOrderIndex.getOrderStatus())) {
                                if (csOrderIndex.getOrderPayDate() == null) {
                                    if (csOrderIndex.getBcFirstChatDate() == null) {
                                        cancelIndexNum++;
                                    } else {
                                        isAfterSale = Boolean.FALSE;
                                        break;
                                    }
                                } else {
                                    isAfterSale = Boolean.FALSE;
                                    break;
                                }
                            } else {
                                isAfterSale = Boolean.FALSE;
                                break;
                            }
                        }
                    }
//                    cp.setIsAfterSale(cancelIndexNum != 0 && cancelIndexNum < size ? isAfterSale : Boolean.FALSE);
                    cp.setIsAfterSale(cancelIndexNum < size ? isAfterSale : Boolean.FALSE);
                }
            }
        }
    }


    /**
     * 计算最终数据
     */
    private List<SimpleChatpeerDTO> calFinalData(
            JobShopQuery jobShop,
            List<SimpleChatpeerDTO> csChatpeers,
            List<SimpleOrderIndexDTO> csOrderIndexLst) {

        if (CollectionUtils.isNotEmpty(csChatpeers)) {
            ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

            Boolean activeFollowUpSwitch = sys.getActiveFollowUpSwitch();//客服主动跟进不算询单流失开关

            Boolean crossChatLossSwitch = sys.getEnquiryLossSwitch();

            // 客服维度下，按买家维度拆分
            Map<String, List<SimpleOrderIndexDTO>> buyerOrderIndexMap = Optional.ofNullable(csOrderIndexLst)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .collect(Collectors.groupingBy(SimpleOrderIndexDTO::getBuyerNick));

            for (SimpleChatpeerDTO cp : csChatpeers) {
                if (buyerOrderIndexMap.containsKey(cp.getBuyerNick())) {
                    List<SimpleOrderIndexDTO> tempLst = buyerOrderIndexMap.get(cp.getBuyerNick());
                    if (CollectionUtils.isNotEmpty(tempLst)) {
                        cp.setOrderCreated(Boolean.TRUE);//询单有效期内最终下单
                    }
                }
            }

            if (activeFollowUpSwitch) {
                // @TODO(处理主动催付失败)
                List<SimpleOrderIndexDTO> buyerOrderIndexLst;
                for (SimpleChatpeerDTO cp : csChatpeers) {

                    if (!cp.getAfterSale()) {

                        buyerOrderIndexLst = buyerOrderIndexMap.get(cp.getBuyerNick());
                        if (CollectionUtils.isNotEmpty(buyerOrderIndexLst)) {

                            SimpleOrderIndexDTO targetOrder = buyerOrderIndexLst.get(0);

                            for (int i = 1, size = buyerOrderIndexLst.size(); i < size; i++) {

                                //看最后一单状态，最后一单，付了款那就催付成功
                                if (buyerOrderIndexLst.get(i).getOrderCreated().after(targetOrder.getOrderCreated())) {
                                    targetOrder = buyerOrderIndexLst.get(i);
                                }
                            }

                            if (targetOrder.getOrderPayDate() == null) {
                                //0:没有催付,1:客服主动催付,2:非客服主动催付
                                if (activeFollowUpSwitch && targetOrder.getUrgepayFlag() == 1) {
                                    cp.setCsActiveUrgepayFail(Boolean.TRUE);
                                }
                            }
                        }
                    }
                }
            }

            if (crossChatLossSwitch) {
                // @TODO(处理跨天聊天失败)
                for (SimpleChatpeerDTO chatpeer : csChatpeers) {

                    if (!chatpeer.getAfterSale()) {
                        if (chatpeer.getCrossChat()) {
                            chatpeer.setCrossChatFail(!chatpeer.getOrderCreated());//跨天聊天失败
                            if (chatpeer.getCrossChatFail()) {
                                chatpeer.setEnquiry(Boolean.FALSE);
                            }

                        }
                    }
                }
            }

            if (activeFollowUpSwitch) {
                // @TODO(处理主动聊天失败)
                for (SimpleChatpeerDTO chatpeer : csChatpeers) {

                    if (!chatpeer.getAfterSale() && !chatpeer.getCrossChatFilter()) {//售前接待,不是跨天失败的
                        if (chatpeer.getCsChatFirstFlag() == 1) {//主动联系

                            chatpeer.setCsActiveChatFail(!chatpeer.getOrderCreated());//主动联系成功或者失败
                            if (chatpeer.getCsActiveChatFail()) {
                                chatpeer.setEnquiry(Boolean.FALSE);
                            }
                        }
                    }
                }
            }

        }
        return csChatpeers.stream().filter(ele -> ele.getOrderCreated() || ele.getCsActiveChatFail() || ele.getCsActiveUrgepayFail() || ele.getCrossChatFail()).collect(Collectors.toList());
    }

    /**
     * 获取过滤买家
     *
     * @param jobShop
     * @return
     */
    private Set<String> getFiltedBuyerSet(JobShopQuery jobShop) {
        // 买家过滤
        Set<String> filtedBuyerNickSet;
        List<String> filterBuyerNickLst = jobShop.getBuyerFilterLst();
        if (CollectionUtils.isNotEmpty(filterBuyerNickLst)) {
            filtedBuyerNickSet = filterBuyerNickLst.stream().map(String::toLowerCase).collect(Collectors.toSet());
        } else {
            filtedBuyerNickSet = Sets.newHashSet();
        }
        return filtedBuyerNickSet;
    }

    private boolean isCsAutoReplyChat(boolean autoReplySwitch, Byte mt, String content) {
        return performanceRuleBusiness.isCsAutoReplyChat(autoReplySwitch, mt, content);
    }

    /**
     * 自家账号过滤,绩效软件客服过滤,空聊天过滤,京东官方客服过滤,转发处理
     *
     * @param cpBOLst
     * @param accountSet
     */
    private void handleCsBaseFilter(JobShopQuery jobShop,
                                    List<ChatBO> cpBOLst,
                                    Set<String> accountSet) {


        if (CollectionUtils.isEmpty(cpBOLst)) {
            if (logger.isDebugEnabled()) {

                logger.debug("cs chatpeer num is empty,break");
            }
            return;
        }

        //系统设置
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        //遍历客服名下 聊天所有聊天对象
        CommonCsChatpeerDTO chatPeer;
        for (ChatBO chatBO : cpBOLst) {
            chatPeer = chatBO.getCsChatpeer();
            if (chatPeer != null) {

                List<CsChatlogDTO> cls = chatBO.getChatPeerRelatedChatLogLst();

                /*
                 * @TODO(自家账号过滤,绩效软件客服过滤,空聊天过滤,京东官方客服过滤)
                 */
                Integer chatFlag = performanceRuleBusiness.getBuyerChatFlag(sys, accountSet, cls, chatPeer.getBuyerNick());
                chatPeer.setChatFlag(chatFlag);
                if (chatFlag != 0) {
                    chatPeer.setReceive(Boolean.FALSE);
                }
            }
        }
    }

    /**
     * 最早，最晚回复聊天时间，买家句数
     *
     * @param csNick
     * @param cpBOLst
     * @param sdate
     */
    private void csCommonHandle(JobShopQuery jobShop, String csNick,
                                List<ChatBO> cpBOLst, Date sdate) {

        if (CollectionUtils.isEmpty(cpBOLst)) {
            if (logger.isDebugEnabled()) {

                logger.debug("cs chatpeer num is empty,break");
            }
            return;
        }

        // 系统设置
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        boolean autoReplySwitch = sys.getAutoReplySwitch();
        //客服主动聊天不算询单
        Boolean activeFollowUpSwitch = sys.getActiveFollowUpSwitch();
        //顾客回复有效天数(包含客服主动聊天当天：值为1天的话，就是客服主动聊天当天有效)
        Integer custFirstReplyDay = sys.getCustFirstReplyDay();
        /**
         * 客服维度主动联系不算询单流失天数内客服主动联系的买家
         */
        Set<String> activeFollowUpBuyerSet = null;
        //处理客服主动聊天的延时天数问题
        if (activeFollowUpSwitch && custFirstReplyDay > 1) {//等于1表示当天，不用往前查
            Date activeFollowUpStartDate = DateUtil.getDateByPeriod(sdate, -custFirstReplyDay + 1);
            Date activeFollowUpEndDate = DateUtil.getDateByPeriod(sdate, -1);
            csChatpeerDao.selectConsultByDateAndCsNickAndBuyer(jobShop.getShop(), activeFollowUpStartDate, activeFollowUpEndDate, null);
            List<SimpleChatpeerDTO> activeFollowUpLst = csChatpeerDao.selectcsChatFirstChatPeerLstByDateByCsNick(jobShop.getShop(), activeFollowUpStartDate, activeFollowUpEndDate, csNick);
            activeFollowUpBuyerSet = activeFollowUpLst.stream().map(SimpleChatpeerDTO::getBuyerNick).collect(Collectors.toSet());
        }

        // 遍历客服名下 聊天所有聊天对象
        for (ChatBO chatBO : cpBOLst) {
            CommonCsChatpeerDTO chatPeer = chatBO.getCsChatpeer();
            if (chatPeer != null) {

                if (chatPeer.getChatFlag() == 1) {//空聊天跳过
                    continue;
                }

                boolean notSetFirstChatProperty = true;
                List<CsChatlogDTO> cls = chatBO.getChatPeerRelatedChatLogLst();
                if (CollectionUtils.isNotEmpty(cls)) {

                    chatPeer.setCsChatFirstFlag(2);//非客服主动聊天

                    int size = cls.size();

                    int buyerChatNum = 0;
                    int chatNum = 0;
                    for (int i = 0; i < size; i++) {
                        CsChatlogDTO cl = cls.get(i);

                        if (cl.getDirection() == 1) {
                            buyerChatNum++;
                        }

                        if (cl.getDirection() == 1) {
                            chatNum++;
                            if (notSetFirstChatProperty) {
                                chatPeer.setFirstChatDate(cl.getChatTime());// 首次接待时间（不区分客服还是买家）
                                chatPeer.setCsChatFirstFlag(2);//客服非主动聊天
                                chatPeer.setCsConsultFirst(Boolean.FALSE);
                                notSetFirstChatProperty = false;
                            }
                        } else if (cl.getDirection() == 0) {// 客服说话

                            if (isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())) { // 表示开启自动回复过滤功能
                            } else {
                                chatNum++;

                                if (notSetFirstChatProperty) {
                                    chatPeer.setFirstChatDate(cl.getChatTime());
                                    chatPeer.setCsChatFirstFlag(1);//客服主动聊天
                                    chatPeer.setCsConsultFirst(Boolean.TRUE);
                                    notSetFirstChatProperty = false;
                                }
                            }
                        }
                    }
                    chatPeer.setBuyerChatNum(buyerChatNum);
                    chatPeer.setChatNum(chatNum);

                    boolean notSetLastChatDateProperty = true;
                    for (int i = size - 1; i >= 0; i--) {
                        CsChatlogDTO cl = cls.get(i);

                        if (cl.getDirection() == 1) {
                            if (notSetLastChatDateProperty) {
                                chatPeer.setLastChatDate(cl.getChatTime());
                                notSetLastChatDateProperty = false;
                                break;
                            }
                        } else if (cl.getDirection() == 0) {// 客服说话

                            if (isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())) { // 表示开启自动回复过滤功能
                                // 自动回复 - 什么也不做
                            } else {
                                if (notSetLastChatDateProperty) {
                                    chatPeer.setLastChatDate(cl.getChatTime());
                                    notSetLastChatDateProperty = false;
                                    break;
                                }
                            }
                        }
                    }

                }

            }
            //处理客服主动聊天的延时天数问题
            if (activeFollowUpSwitch && custFirstReplyDay > 1) {//等于1表示当天，不用往前查
                if (CollUtil.isNotEmpty(activeFollowUpBuyerSet) && activeFollowUpBuyerSet.contains(chatPeer.getBuyerNick()))
                    chatPeer.setCsChatFirstFlag(1);//客服主动聊天
            }
        }
    }


    /*	*//**
     * 转发处理
     * @param jobShop
     * @param date
     * @param forwardChatPeerLst
     *//*
	private void handleCsForwardFilter(JobShopQuery jobShop, Date date, List<CommonCsChatpeerDTO> forwardChatPeerLst) {

		JobShopDTO shop = jobShop.getShop();
		if(CollectionUtils.isEmpty(forwardChatPeerLst)){
			logger.debug("have no forward ChatPeer to handle,return");
			return ;
		}

		Map<String, CsDTO> csMap = jobShop.getCsLst().stream()
				.collect(Collectors.toMap(CsDTO::getNick, ele->ele));

		//客服转入，推导出转发者
		performanceRuleBusiness.handleForwardChat(shop, date, forwardChatPeerLst, csMap);
	}*/

    /**
     * 获取客服的下线时间
     *
     * @param jobShop
     * @param csChatLogLst
     * @return
     */
    private Date getCsOfflineDatetime(JobShopQuery jobShop,
                                      List<CsChatlogDTO> csChatLogLst) {
        Boolean autoReplySwitch = jobShop.getShopSystemsetting().getAutoReplySwitch();
        CsChatlogDTO cl;
        for (int i = csChatLogLst.size() - 1; i > 0; i--) {
            cl = csChatLogLst.get(i);
            if (cl.getDirection() == 0
                    && !isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())) {
                return cl.getChatTime();
            }
        }
        return null;
    }

    /**
     * 暗语客户集合
     */
    private Map<String, Set<String>> getWatchwordMap(JobShopQuery jobShop, Date date) {

        // 根据开关获取所有需要设置买家id
        if (jobShop.getShopSystemsetting().getCsWatchwordSwitch()) {
            List<ShopWatchwordDTO> shopWatchwordLst = shopWatchwordDao.selectShopWatchwordLstByShopAndDate(jobShop.getShop(), date);
            if (CollectionUtils.isNotEmpty(shopWatchwordLst)) {
                Map<String, Set<String>> map = new HashMap<>();
                //以csNick分组
                Map<String, List<ShopWatchwordDTO>> shopWatchwordMap = shopWatchwordLst.stream()
                        .collect(Collectors.groupingBy(ShopWatchwordDTO::getCsNick));

                shopWatchwordMap.forEach((csNick, list) -> {//循环分组后集合
                    //封装 过滤后 当前客服下 的买家昵称列表
                    Set<String> buyNickSet = list.stream().map(ShopWatchwordDTO::getBuyerNick).collect(Collectors.toSet());
                    map.put(csNick, buyNickSet);
                });
                return map;
            }
        }
        return null;
    }

    /**
     * 暗语过滤
     */
    private void handleCsWatchwordFilter(JobShopQuery jobShop,
                                         String csNick, List<ChatBO> cpBOLst,
                                         Map<String, Set<String>> watchwordMap) {
        if (watchwordMap == null || watchwordMap.size() == 0 || !watchwordMap.containsKey(csNick)) return;
        if (!jobShop.getShopSystemsetting().getCsWatchwordSwitch()) return;

        for (ChatBO bo : cpBOLst) {
            if (bo.getCsChatpeer() != null && watchwordMap.get(csNick).contains(bo.getCsChatpeer().getBuyerNick())) {
                if (logger.isDebugEnabled()) {

                    logger.debug("---->shopId={} : 暗语过滤 csNick={}, buyerNick={}", jobShop.getShop().getShopId(), csNick, bo.getCsChatpeer().getBuyerNick());
                }
                bo.getCsChatpeer().setWatchwordBuyer(Boolean.TRUE);
                bo.getCsChatpeer().setReceive(Boolean.FALSE);
            }
        }
    }

    /**
     * 指定顾客过滤
     *
     * @param csNick
     * @param cpBOLst
     * @param filtedBuyerNickSet
     */
    private void handleCustomerFilter(String csNick,
                                      List<ChatBO> cpBOLst,
                                      Set<String> filtedBuyerNickSet) {
        CommonCsChatpeerDTO csChatpeer;
        for (ChatBO bo : cpBOLst) {
            csChatpeer = bo.getCsChatpeer();
            if (csChatpeer != null && filtedBuyerNickSet.contains(csChatpeer.getBuyerNick())) {
                logger.info(" ***buyer nick filte csNick={}***", csNick);
                csChatpeer.setFilteredBuyer(true);
                csChatpeer.setReceive(Boolean.FALSE);
            }
        }
    }


    /**
     * 设置客服/客户的咨询时间
     * 客服还是客户 主动咨询
     *
     * @param cpBOLst
     * @param jobShop
     */
    private void packageConsultChatInfo(JobShopQuery jobShop,
                                        List<ChatBO> cpBOLst) {

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Boolean autoReplySwitch = sys.getAutoReplySwitch();
        for (ChatBO bo : cpBOLst) {

            List<CsChatlogDTO> csChatLogLst = bo.getChatPeerRelatedChatLogLst();

            if (CollectionUtils.isNotEmpty(csChatLogLst)) {

                int size = csChatLogLst.size();
                for (int i = 0; i < size; i++) {
                    CsChatlogDTO cl = csChatLogLst.get(i);
                    if (cl.getDirection() == 0) {
                        if (!isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())) {
                            bo.setIsBuyerFirstConsult(false);
                            break;
                        }
                    } else if (cl.getDirection() == 1) {
                        bo.setIsBuyerFirstConsult(true);
                        break;
                    }
                }

                //客服咨询,回复时间
                for (int i = 0; i < size; i++) {
                    CsChatlogDTO cl = csChatLogLst.get(i);
                    if (cl.getDirection() == 0) {
                        if (!isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())) {
                            bo.setFirstReplyTime(cl.getChatTime());
                            break;
                        }
                    }
                }

                //买家咨询时间
                for (int i = 0; i < size; i++) {
                    CsChatlogDTO cl = csChatLogLst.get(i);
                    if (cl.getDirection() == 1) {
                        bo.setFirstChatTime(cl.getChatTime());
                        break;
                    }
                }
            } else {

                bo.setIsBuyerFirstConsult(false);
            }

        }
    }


    /**
     * 客服离线消息过滤
     *
     * @param date
     * @param cpBOLst
     * @param csOfflineDatetime
     */
    private void handleCsOfflineMsgFilter(JobShopQuery jobShop, Date date,
                                          List<ChatBO> cpBOLst, Date csOfflineDatetime) {


        if (csOfflineDatetime == null) {
            CommonCsChatpeerDTO csChatpeer;
            for (ChatBO bo : cpBOLst) {
                csChatpeer = bo.getCsChatpeer();
                if (csChatpeer != null) {
                    csChatpeer.setCsOfflineMsgFilter(Boolean.TRUE);
                    csChatpeer.setReceive(Boolean.FALSE);
                }
            }
            return;
        }

        if (jobShop.getShopSystemsetting().getCsOfflineFilteCustMsgSwitch()) {
            /*
             * 设置客服/客户的咨询时间
             * 客服还是客户 主动咨询
             */
            packageConsultChatInfo(jobShop, cpBOLst);

            /*
             * 客服离线
             */
            CommonCsChatpeerDTO csChatpeer;
            for (ChatBO bo : cpBOLst) {
                if (bo.getIsBuyerFirstConsult() != null
                        && bo.getIsBuyerFirstConsult()) {

                    if (bo.getFirstChatTime() != null
                            && bo.getFirstChatTime().after(csOfflineDatetime)) {
                        csChatpeer = bo.getCsChatpeer();
                        if (csChatpeer != null) {
                            csChatpeer.setCsOfflineMsgFilter(Boolean.TRUE);
                            csChatpeer.setReceive(Boolean.FALSE);
                        }
                    }
                }
            }
        }
    }

    /**
     * 主号主动回复过滤
     *
     * @param cpBOLst
     */
    private void handleMainAccountAutoReplyFilter(JobShopQuery jobShop,
                                                  List<ChatBO> cpBOLst) {

        String mainAccountNick = jobShop.getShop().getSellerNick();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();

        if (!sys.getMainAccountAutoReplySwitch()) {
            return;
        }
        String mainAccountAutoReplyContent = sys.getMainAccountAutoReplyContent();
        if (StringUtils.isBlank(mainAccountAutoReplyContent)) {
            return;
        }

        boolean isOnlyMainAccountAutoReply;
        CommonCsChatpeerDTO csChatpeer;
        for (ChatBO bo : cpBOLst) {
            isOnlyMainAccountAutoReply = true;
            csChatpeer = bo.getCsChatpeer();

            if (csChatpeer != null) {
                /*
                 * TODO(主号接待)
                 */
                if (mainAccountNick.equals(csChatpeer.getCsNick())) {


                    List<CsChatlogDTO> csChatLogLst = bo.getChatPeerRelatedChatLogLst();
                    if (CollectionUtils.isNotEmpty(csChatLogLst)) {
                        int size = csChatLogLst.size();
                        CsChatlogDTO cl;
                        for (int i = 0; i < size; i++) {
                            cl = csChatLogLst.get(i);
                            if (cl != null && cl.getDirection() == 0
                                    && !mainAccountAutoReplyContent.equals(cl.getContent())) {
                                isOnlyMainAccountAutoReply = false;
                                break;
                            }
                        }
                        if (isOnlyMainAccountAutoReply) {
                            csChatpeer.setMaAutoReplyFilter(Boolean.TRUE);
                            csChatpeer.setReceive(Boolean.FALSE);
                        }
                    }


                }
            }


        }
    }

    /**
     * 顾客单句过滤
     *
     * @param cpBOLst
     */
    private void handleCustomerSingleChatFilter(JobShopQuery jobShop,
                                                List<ChatBO> cpBOLst) {

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Boolean custSigchatSwitch = sys.getCustSigchatSwitch();
        if (custSigchatSwitch) {

            Integer sigchatMinReplyNum = sys.getSigchatMinReplyNum();

            boolean isBuyerFirst;
            int csChatNum;
            int buyerChatNum;

            boolean isCsReplyNumOk;
            CommonCsChatpeerDTO csChatpeer;
            for (ChatBO bo : cpBOLst) {
                isBuyerFirst = false;
                csChatNum = 0;
                buyerChatNum = 0;
                isCsReplyNumOk = false;
                /*
                 * TODO(顾客单句过滤)
                 */
                List<CsChatlogDTO> csChatLogLst = bo.getChatPeerRelatedChatLogLst();
                if (CollectionUtils.isNotEmpty(csChatLogLst)) {
                    int size = csChatLogLst.size();

                    CsChatlogDTO cl;
                    for (int i = 0; i < size; i++) {
                        cl = csChatLogLst.get(i);

                        if (cl != null) {

                            if (i == 0 && cl.getDirection() == 1) {//第一句是买家说话
                                //顾客首次咨询
                                isBuyerFirst = true;
                                buyerChatNum++;
                            } else {
                                if (isBuyerFirst) {
                                    if (cl.getDirection() == 0) {//客服说话
                                        csChatNum++;
                                        if (csChatNum >= sigchatMinReplyNum) {
                                            //客服回复指定句数以上，客户无回应。则不算接待
                                            isCsReplyNumOk = true;
                                        }
                                    } else if (cl.getDirection() == 1) {
                                        buyerChatNum++;
                                        break;
                                    }
                                } else {
                                    break;
                                }
                            }
                        }
                    }

                    if (isBuyerFirst && isCsReplyNumOk && buyerChatNum == 1) {
                        csChatpeer = bo.getCsChatpeer();
                        if (csChatpeer != null) {
                            csChatpeer.setCustSingleChatFilter(Boolean.TRUE);
                            csChatpeer.setReceive(Boolean.FALSE);
                        }
                    }
                }
            }
        }

    }

    /**
     * 客服单口相声过滤
     *
     * @param cpBOLst
     */
    private void handleCsSingleChatFilter(JobShopQuery jobShop,
                                          List<ChatBO> cpBOLst) {
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Boolean csSingleChatFilter = sys.getCsSingleChatFilter();
        if (csSingleChatFilter) {
            Integer custSingleChatNum = sys.getCustSingleChatNum();
            int buyerChatNum;
            CommonCsChatpeerDTO csChatpeer;
            for (ChatBO bo : cpBOLst) {
                buyerChatNum = 0;
                /*
                 * TODO(顾客单句过滤)
                 */
                List<CsChatlogDTO> csChatLogLst = bo.getChatPeerRelatedChatLogLst();
                if (CollectionUtils.isNotEmpty(csChatLogLst)) {
                    int size = csChatLogLst.size();
                    CsChatlogDTO cl;
                    for (int i = 0; i < size; i++) {

                        cl = csChatLogLst.get(i);
                        if (cl != null) {
                            if (cl.getDirection() == 1) {//第一句是买家说话

                                buyerChatNum++;
                            }
                        }
                    }
                    if (buyerChatNum <= custSingleChatNum) {
                        csChatpeer = bo.getCsChatpeer();
                        if (csChatpeer != null) {
                            csChatpeer.setCsSingleChatFilter(Boolean.TRUE);
                            csChatpeer.setReceive(Boolean.FALSE);
                        }
                    }
                }
            }
        }

    }

    /**
     * 留言过滤
     *
     * @param jobShop
     * @param cpBOLst
     */
    private void handleLeaveMessageFilter(JobShopQuery jobShop, String csNick, List<ChatBO> cpBOLst, Set<String> filterBuyer) {
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Boolean leaveMessageSwitch = sys.getLeaveMessageSwitch() != null ? sys.getLeaveMessageSwitch() : false;

        if (leaveMessageSwitch) {
            if (CollectionUtils.isNotEmpty(filterBuyer)) {
                CommonCsChatpeerDTO csChatpeer;
                for (ChatBO bo : cpBOLst) {
                    if (csNick.equals(bo.getCsNick())) {
                        if (filterBuyer.contains(bo.getBuyerNick())) {
                            csChatpeer = bo.getCsChatpeer();
                            if (csChatpeer.getReceive()) {
                                csChatpeer.setCustLeaveMessageFilter(Boolean.TRUE);
                                csChatpeer.setReceive(Boolean.FALSE);
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 询单流失
     * （询单有效日）
     * 19号算18号数据：传入日期15号
     */
    @Override
    public void handleEnquiryLoss(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //客服主动聊天不算询单
        //顾客回复有效天数(包含客服主动聊天当天：值为1天的话，就是客服主动聊天当天有效)
//        Integer custFirstReplyDay = sys.getCustFirstReplyDay();

        //job重算日期：当天和前推一个询单有效期
        List<Date> dates = jobDate.getCsEnquiryLossForEnquiryDates();
        if (dates.isEmpty()) {
//			logger.warn("req dates is empty");
            return;
        }
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
        for (Date date : dates) {
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (targetCsLst.isEmpty()) {
                continue;
            }
            if (isDelData) {
                //询单流失历史记录删除
                lossEnquiryRecordDao.deleteShopLossRecordByDate(shop, DateUtil.getStartTimeOfDate(date), DateUtil.getEndTimeOfDate(date), null);
            }
            Date startDate = date;
            Date endDate = DateUtil.getEndTimeOfDate(date);
            ValidDateRangeQuery validDateRange = new ValidDateRangeQuery();
            validDateRange.setEnquiryValidDurationTime(sys.getEnquiryValidDurationTime());
            validDateRange.setSellAfter(sys.getSellAfter());

            //设置计算天的开始时间
            validDateRange.setStartDate(startDate);
            validDateRange.setEndDate(endDate);

            //设置询单有效的开始时间
            validDateRange.setAdjustEnquiryStartDate(startDate);
            validDateRange.setAdjustEnquiryEndDate(DateUtil.getDateByPeriod(endDate, sys.getEnquiryValidDurationTime()));
            List<LossEnquiryRecordDTO> lossRecordLst = Lists.newArrayList();
            //查询属于询单的聊天对象
            List<EnquiryChatpeerDTO> enquiryLossChatPeerList = csChatpeerDao.selectShopCsChatpeerLstByDateForEnquiryLoss(shop, targetCsLst, date);

            //预售订单定金期的开始结束时间
            validDateRange.setAdjustPresaleStartDate(DateUtil.getDateByPeriod(startDate, -PesConstants.ORDER_PRESALE_PAY_BARGAIN_DATE));
            validDateRange.setAdjustPresaleEndDate(endDate);

            //客服主动过滤集合
            List<Long> activeFollowUpEnquiryChatpeerIdLst = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(enquiryLossChatPeerList)) {
//                List<Date> custFirstReplyDates = Lists.newArrayList();
//                if (custFirstReplyDay > 1) {//等于1表示当天，不用往前查
//                    Date queryCustFirstReplyStartDate = DateUtil.getDateByPeriod(startDate, -custFirstReplyDay + 1);
//                    custFirstReplyDates = DateUtil.splitDate(queryCustFirstReplyStartDate, DateUtil.getDateByPeriod(startDate, -1));
//                }
                Map<String, List<EnquiryChatpeerDTO>> csCustEnquiryChatpeerMap = enquiryLossChatPeerList
                        .stream()
                        .filter(x -> x.getCsChatFirstFlag() != 0)
                        .collect(Collectors.groupingBy(EnquiryChatpeerDTO::getCsNick));

                //咨询后询单有效期内下单的买家
                List<String> csBindOrderBuyerLst = new ArrayList<>();
                Optional<List<String>> createBuyerOptional = Optional.ofNullable(csOrderBindDao.selectShopCsOrderBindLstForEnquiryLoss(shop, csCustEnquiryChatpeerMap.keySet(), date, validDateRange));
                createBuyerOptional.ifPresent(csBindOrderBuyerLst::addAll);
                //查询在询单有效期和售后天数内并且在定金期有下单付定金的买家不算询单流失（排除付全款的情况）
                Optional<List<String>> bargainPayBuyer = Optional.ofNullable(csOrderBindDao.selectDistinctBuyerOfBargainPayNoInEnquiryAndAfterSale(shop, validDateRange, date));
                bargainPayBuyer.ifPresent(csBindOrderBuyerLst::addAll);
                logger.info("=======zaj>>>>>当天用户有落实付尾款的订单不算流失 start 查询落实付尾款的用户");
                //当天用户有落实付尾款的订单不算流失
                Optional<List<String>> bindPayBalance = Optional.ofNullable(csOrderBindDao.selectDistinctBuyerOfBindPayBalance(shop, date));
                logger.info("=======zaj>>>>>当天用户有落实付尾款的订单不算流失  end");
                bindPayBalance.ifPresent(csBindOrderBuyerLst::addAll);
                for (Entry<String, List<EnquiryChatpeerDTO>> entry : csCustEnquiryChatpeerMap.entrySet()) {
                    List<EnquiryChatpeerDTO> enquiryChatpeerLst = entry.getValue();

                    List<EnquiryChatpeerDTO> notCreatedOrderChatPeerLst = Lists.newArrayList();
                    Set<String> notCreatedOrderBuyerSets = Sets.newHashSet();
                    /*
                     * 按绩效时间查询:客服绑定的下单买家
                     */
                    for (EnquiryChatpeerDTO chatpeer : enquiryChatpeerLst) {
                        if (!csBindOrderBuyerLst.contains(chatpeer.getBuyerNick())) {
                            //询单未绑定下单的
                            notCreatedOrderBuyerSets.add(chatpeer.getBuyerNick());
                            notCreatedOrderChatPeerLst.add(chatpeer);
                        }
                    }

                   /* Set<String> csNotLossByeryNickSets = Sets.newHashSet();
					// 客服主动聊天不算询单的买家(当日的不做统计)
					if (activeFollowUpSwitch && custFirstReplyDay > 1){//等于1表示当天，不用往前查
						//非客服主动聊天且未下单的chatpeer集合
						List<EnquiryChatpeerDTO> notCreatedOrderNotCsFirstChatPeerLst =
								notCreatedOrderChatPeerLst
								.stream()
								.filter(e -> e.getCsChatFirstFlag() == 2)
								.collect(Collectors.toList());
						//（非当日客服主动说话，买家未回复的买家集合）
						csNotLossByeryNickSets = calCsNotLossByeryNickSets(shop, csNick, notCreatedOrderNotCsFirstChatPeerLst,
								custFirstReplyDates, notCreatedOrderNotCsFirstChatPeerLst.size());
                    }*/

                    LossEnquiryRecordDTO lossRecordDO;
                    for (EnquiryChatpeerDTO chatPeer : notCreatedOrderChatPeerLst) {
                        if (notCreatedOrderBuyerSets.contains(chatPeer.getBuyerNick())) {
                          /*  if (activeFollowUpSwitch && chatPeer.getCsChatFirstFlag() == 1) {
                                // 客服主动找客户聊天开关开启后,客服主动找客户聊天不算询单流失(当日主动聊天)
                                activeFollowUpEnquiryChatpeerIdLst.add(chatPeer.getId());
                                continue;
                            }*/
                            /*if (csNotLossByeryNickSets.contains(chatPeer.getBuyerNick())) {
                                // 客服主动找客户聊天开关开启后,客服主动找客户聊天不算询单流失(非当日主动聊天)
                                activeFollowUpEnquiryChatpeerIdLst.add(chatPeer.getId());
                                continue;
                            }*/
                            // 询单流失的
                            lossRecordDO = new LossEnquiryRecordDTO();
                            lossRecordDO.setShopId(chatPeer.getShopId());
                            lossRecordDO.setCsNick(chatPeer.getCsNick());
                            lossRecordDO.setCustomer(chatPeer.getBuyerNick());
                            lossRecordDO.setDate(chatPeer.getDate());
                            lossRecordDO.setChatType(chatPeer.getCsChatFirstFlag() == 2
                                    ? CommonConstants.CUSTOMER_INITIATIVE_CHAT : CommonConstants.CS_INITIATIVE_CHAT);
                            lossRecordDO.setChatNum(chatPeer.getChatNum());
                            lossRecordDO.setStartDateTime(chatPeer.getFirstChatDate());
                            lossRecordDO.setEndDateTime(chatPeer.getLastChatDate());
                            //	添加		会话时长_单位：秒
                            if (chatPeer.getLastChatDate() != null) {
                                lossRecordDO.setConsumeTime((chatPeer.getLastChatDate().getTime() - chatPeer.getFirstChatDate().getTime()) / 1000);
                            } else {
                                lossRecordDO.setConsumeTime(0L);
                            }
                            lossRecordLst.add(lossRecordDO);
                        }
                    }

                }
            }
            //保存客服和对应顾客的skuId逗号分隔
            //根据客服昵称查询所有的咨询的sku的记录
            Set<String> enquiryLossCustSet = lossRecordLst.stream().map(LossEnquiryRecordDTO::getCustomer).collect(Collectors.toSet());
            List<CsCustRecommendConsultSkuDTO> csCustRecommendConsultSku = csCustRecommendConsultSkuBusiness.selectLstByBuyerNick(shop, enquiryLossCustSet, date);
            //---------拼接咨询的sku逗号分隔
            for (LossEnquiryRecordDTO lossEnquiryRecordDTO : lossRecordLst) {
                List<Long> skuIdsLst = new ArrayList<>();//订单sku集合
                String skuIdsStr = "";
                for (int i = 0; i < csCustRecommendConsultSku.size(); i++) {
                    CsCustRecommendConsultSkuDTO csCustRecommendConsultSkuDTO = csCustRecommendConsultSku.get(i);
                    if (lossEnquiryRecordDTO.getCsNick().equals(csCustRecommendConsultSkuDTO.getCsNick())
                            && lossEnquiryRecordDTO.getCustomer().equals(csCustRecommendConsultSkuDTO.getBuyerNick())) {
                        skuIdsLst.add(csCustRecommendConsultSkuDTO.getSkuId());
                    }
                }
                if (CollectionUtils.isNotEmpty(skuIdsLst)) {
                    skuIdsLst = skuIdsLst.stream().distinct().collect(Collectors.toList());
                    //添加sku属性
                    skuIdsStr = getLstStr(skuIdsLst);
                }
                lossEnquiryRecordDTO.setSkuIds(skuIdsStr);
            }


            int updateNum = csChatpeerDao.updateChatPeerInfoIsEnquiryStatusByIdLst(shop, activeFollowUpEnquiryChatpeerIdLst, date);
            if (logger.isDebugEnabled()) {

                logger.debug("shop:【{}】,enquiryLoss customer initiative chat updateNum = {}", shop.getTitle(), updateNum);
            }
            int insertNum = lossEnquiryRecordDao.insertBatchShopLossRecord(shop, lossRecordLst, date);
            if (logger.isDebugEnabled()) {

                logger.debug("shop:【{}】,enquiryLoss customer initiative chat insert num = {}", shop.getTitle(), insertNum);
            }
        }
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("询单流失计算耗时 loss_calculation_of_inquiry_order：{} ms", (e - s));
        }
    }

//    private Set<String> calCsNotLossByeryNickSets(JobShopDTO shop, String csNick,
//                                                  List<EnquiryChatpeerDTO> enquiryChatpeerLst, List<Date> custFirstReplyDates, int enquiryBuyerNum) {
//        Set<String> notLossEnquiryBuyerSets = Sets.newHashSet();
//        if (custFirstReplyDates.isEmpty() || enquiryChatpeerLst.isEmpty()) {
//            return notLossEnquiryBuyerSets;
//        }
//        // 未判定的买家集合
//        List<EnquiryChatpeerDTO> toDayNotFirstReplyLst = enquiryChatpeerLst;
//        for (int i = custFirstReplyDates.size() - 1; i >= 0; i--) {
//            List<EnquiryChatpeerDTO> nextDayNotFirstReplyLst = Lists.newArrayList();
//            // 已判定的买家集合，传入日期买家说话的集合:已经找到主动聊天，或者主动聊天不算询单失效的买家记录
//            List<EnquiryChatpeerDTO> custFirstReplyLst = csChatpeerDao.selectCustFirstReplyByCsByCustomerByDate(shop,
//                    csNick, toDayNotFirstReplyLst, custFirstReplyDates.get(i));
//            if (CollectionUtils.isNotEmpty(custFirstReplyLst)) {
//                // 主动聊天不算询单的买家
//                notLossEnquiryBuyerSets.addAll(custFirstReplyLst.stream().filter(e -> e.getCsChatFirstFlag() == 1 && e.getBuyerChatNum() == 0)
//                        .map(EnquiryChatpeerDTO::getBuyerNick).collect(Collectors.toSet()));
//
//                // 已确定所有的询单买家
//                if (enquiryBuyerNum == notLossEnquiryBuyerSets.size()) {
//                    break;
//                }
//
//                // 查询到聊天关系：1：主动询单，2：主动聊天不算询单失效
//                Set<String> buyerChatSets = custFirstReplyLst.stream().map(EnquiryChatpeerDTO::getBuyerNick)
//                        .collect(Collectors.toSet());
//
//                // 从所有的询单买家中剔除已确定的买家，未确定的买家继续跑下一天
//                for (EnquiryChatpeerDTO chatPeer : toDayNotFirstReplyLst) {
//                    if (!buyerChatSets.contains(chatPeer.getBuyerNick())) {
//                        nextDayNotFirstReplyLst.add(chatPeer);
//                    }
//                }
//                toDayNotFirstReplyLst = nextDayNotFirstReplyLst;
//            }
//        }
//        return notLossEnquiryBuyerSets;
//    }


    /**
     * handleOrderOutStockLoss:(出库流失：下单维度). <br/>
     *
     * @param isDelData
     * @since JDK 1.8
     */
    @Override
    public void handleOrderOutStockLoss(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();
        JobShopDTO shop = jobShop.getShop();
        List<CsDTO> csLst = jobShop.getCsLst().stream()
                .filter(e -> e.getType() == 1)
                .collect(Collectors.toList());
        List<Date> dates = jobDate.getOutStockLossDates();
        if (csLst.isEmpty()) {
            logger.warn("csLst is empty");
            return;
        }
        if (dates.isEmpty()) {
            logger.warn("req dates is empty");
            return;
        }
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //询单有效时长
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        //出库有效时长
        Integer outStockValidDays = sys.getOutStockValidDurationTime();

        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
        //出库流失：传入日期是下单日期
        for (Date date : dates) {
//			if(DateUtils.isSameDay(DateUtil.getDateByPeriod(date, outStockValidDays),new Date())){
//				continue;
//			}

            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);

            if (targetCsLst.isEmpty()) {
                continue;
            }

            ValidDateRangeQuery drq = new ValidDateRangeQuery();
            drq.setStartDate(DateUtil.getStartTimeOfDate(date));
            drq.setEndDate(DateUtil.getEndTimeOfDate(date));
            if (isDelData) {
                lossOrderRecordDao.deleteShopLossOrderByCratedDateByType(shop, drq, CommonConstants.LOSS_TYPE_OUT_STOCK);
//				logger.debug("shop：【{}】,handleSilentOrderLoss delete num = {}",shop.getTitle(),deleteNum);
            }
            List<LossOrderRecordDTO> outStockLossList = Lists.newArrayList();
            //询单->出库有效时长
            Date startEnquiryValid = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(date, -enquiryValidDays));
            //出库有效时长-->用来判断是否是出库流失
            Date endOutValid = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(date, outStockValidDays - 1));
            Date endEnquiryValid = DateUtil.getEndTimeOfDate(date);
            drq.setAdjustOutStockStartDate(startEnquiryValid);
            drq.setAdjustOutStockEndDate(endEnquiryValid);

            //查询客服询单有效期内所有的落实付款的订单
            List<Long> csBindOrderIdLst = csOrderBindDao.selectShopCsOrderBindOrderIdLstByDateRange(shop, targetCsLst, drq);

            //根据订单查询所有被绑定的客服
            List<CsOrderBindInfoDTO> csOrderBindInfoDTOS = csOrderBindDao.selectShopCsOrderBindOrderLstByDateRange(shop, targetCsLst, drq);

            List<OrderDTO> outStockLossLst = orderDao.selectShopCsOutStockLossOrderByOrderId(shop, csBindOrderIdLst, drq, endOutValid);
            if (CollectionUtils.isNotEmpty(outStockLossLst)) {
                LossOrderRecordDTO outStockLoss;
                for (OrderDTO orderDTO : outStockLossLst) {
                    outStockLoss = new LossOrderRecordDTO();
                    outStockLoss.setShopId(shop.getShopId());
                    outStockLoss.setDate(date);
                    //设置出库流失的客服
                    if (CollectionUtils.isNotEmpty(csOrderBindInfoDTOS)) {
                        a:
                        for (CsOrderBindInfoDTO csOrderBindInfoDTO : csOrderBindInfoDTOS) {
                            if (csOrderBindInfoDTO.getOrderId().equals(orderDTO.getOrderId())) {
                                outStockLoss.setCsNick(csOrderBindInfoDTO.getCsNick());
                                break a;
                            }
                        }
                    }
                    outStockLoss.setCustomer(orderDTO.getBuyerNick());
                    outStockLoss.setOrderCreated(orderDTO.getCreated());
                    outStockLoss.setOrderGoodsNum(orderDTO.getNum());
                    outStockLoss.setOrderPayment(orderDTO.getPayment() == null ? 0 : orderDTO.getPayment());
                    outStockLoss.setOrderId(orderDTO.getOrderId());
                    outStockLoss.setType(CommonConstants.LOSS_TYPE_OUT_STOCK);
                    outStockLossList.add(outStockLoss);
                }
            }
            lossOrderRecordDao.insertBatchShopLossOrderEnquiryLoss(shop, outStockLossList, date);
//			logger.debug("shop：【{}】,handleOrderOutStockLoss outStockLoss insert num = {}",shop.getTitle(),insertNum);
        }
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("出库流失计算耗时 handle order out stockloss time：{} ms", (e - s));
        }
    }

    /**
     * handleEnquiryOrderLoss:(询单下单流失：下单维度). <br/>
     *
     * @param jobShop
     * @param csLst
     * @param date      下单->付款 的下单日期		今日(19号)算昨日（18号）数据：传入的  date 是（17号）
     * @param isDelData
     * @since JDK 1.8
     */
    private void handleEnquiryOrderLoss(JobShopQuery jobShop, List<CsDTO> csLst, Date date, boolean isDelData, List<Long> parentOrderIdList) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        /*
         * 下单流失：传入日期是下单日期，流失：下单后到第二天结束未付款的算流失
         */
        ValidDateRangeQuery drq = new ValidDateRangeQuery();
        drq.setStartDate(DateUtil.getStartTimeOfDate(date));
        drq.setEndDate(DateUtil.getEndTimeOfDate(date));
        // 询单-付款(14号)
        Date enquiry2PayValidDate = DateUtil.getDateByPeriod(date, 1 - enquiryValidDays);

        drq.setAdjustEnquiryStartDate(DateUtil.getStartTimeOfDate(enquiry2PayValidDate));
        if (isDelData) {
            // 询单下单流失历史记录删除
            lossOrderRecordDao.deleteShopLossOrderByCratedDateByType(shop, drq,
                    CommonConstants.LOSS_TYPE_ENQUIRY_ORDER);
//			logger.debug("shop：【{}】,handleEnquiryOrderLoss delete num = {}", shop.getTitle(), deleteNum);
        }

        List<LossOrderRecordDTO> insertLossOrderDOList = Lists.newArrayList();
        insertLossOrderDOList.addAll(conversionOrderEnquiryLoss(shop, csLst, drq, parentOrderIdList));

        lossOrderRecordDao.insertBatchShopLossOrderEnquiryLoss(shop,
                insertLossOrderDOList, date);
//		logger.debug("shop：【{}】,handleEnquiryLoss OrderEnquiryLoss insert num = {}", shop.getTitle(),
//				insertOrderEnquiryLossNum);
    }

    /**
     * handleSilentOrderLoss:(静默下单流失：下单维度). <br/>
     * 今日（19号）算昨日（18号）的数据，可确定前日（17号）所有的静默下单流失
     * 静默下单流失：传入日期是下单日期：date = （17号）
     * 注：客户当天只要付款过一单就不算下单未付款流失
     *
     * @param jobShop
     * @param date
     * @param isDelData
     * @since JDK 1.8
     */
    private void handleSilentOrderLoss(JobShopQuery jobShop, Date date, boolean isDelData) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //询单有效时长
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        //静默催付开关
        Boolean silentUrgepaySwitch = sys.getSilentUrgepaySwitch();
        //静默可催付时长
        Integer silentUrgepayTime = sys.getSilentUrgepayTime();
        // 询单-付款(14号)
        Date enquiry2PayValidDate = DateUtil.getDateByPeriod(date, 1 - enquiryValidDays);

        ValidDateRangeQuery drq = new ValidDateRangeQuery();
        drq.setStartDate(DateUtil.getStartTimeOfDate(date));
        drq.setEndDate(DateUtil.getEndTimeOfDate(date));
        drq.setAdjustEnquiryStartDate(enquiry2PayValidDate);
        // 删除当日（17号）历史数据
        if (isDelData) {
            // 静默下单流失历史记录删除
            int deleteNum = lossOrderRecordDao.deleteShopLossOrderByCratedDateByType(shop, drq, CommonConstants.LOSS_TYPE_SILENT_ORDER);
            if (logger.isDebugEnabled()) {

                logger.debug("店铺：【{}】,handleSilentOrderLoss delete num = {}", shop.getTitle(), deleteNum);
            }
        }
        List<LossOrderRecordDTO> insertLossOrderDOList = Lists.newArrayList();
        // 查询静默下单，静默流失的订单（下单后无客服介入）
        List<SilentOrderLossDTO> silentOrderNoChatLossDTO = orderDao.selectShopSilentCreatedOrderNoChatLossByDate(shop,
                drq);
        // 查询静默下单，下单后与客服聊天后流失的订单（下单后有客服介入）
        List<SilentOrderLossDTO> silentOrderHasChatLossDTO = csOrderIndexDao
                .selectShopSilentCreatedOrderHasChatLossByDate(shop, drq);

        //---拿出预售付完定金的订单
        List<Long> payEarnestMoneyPresaleOrderId = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(silentOrderNoChatLossDTO)) {
            final List<Long> presaleOrder = silentOrderNoChatLossDTO.stream().filter(silentOrderNoChatLoss -> silentOrderNoChatLoss.getOrderType() == 1).map(SilentOrderLossDTO::getOrderId).collect(Collectors.toList());//订单类型 0 普通订单 1预售订单
            if (CollectionUtils.isNotEmpty(presaleOrder)) {
                final List<Long> preOrder = presaleOrderDao.selectPayEarnestMoneyPresaleOrderByOrderLst(shop, drq, presaleOrder);
                if (CollectionUtils.isNotEmpty(preOrder)) {
                    payEarnestMoneyPresaleOrderId.addAll(preOrder);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(silentOrderHasChatLossDTO)) {
            final List<Long> presaleOrder = silentOrderHasChatLossDTO.stream().filter(silentOrderHasChatLoss -> Boolean.TRUE.equals(silentOrderHasChatLoss.getPresale())).map(SilentOrderLossDTO::getOrderId).collect(Collectors.toList());//Presale：false:非预售 true:预售订单
            if (CollectionUtils.isNotEmpty(presaleOrder)) {
                final List<Long> preOrder = presaleOrderDao.selectPayEarnestMoneyPresaleOrderByOrderLst(shop, drq, presaleOrder);
                if (CollectionUtils.isNotEmpty(preOrder)) {
                    payEarnestMoneyPresaleOrderId.addAll(preOrder);
                }
            }
        }


        if (CollectionUtils.isNotEmpty(payEarnestMoneyPresaleOrderId)) {
            //排除已交定金的预售单
            if (CollectionUtils.isNotEmpty(silentOrderNoChatLossDTO)) {
                final Iterator<SilentOrderLossDTO> iterator = silentOrderNoChatLossDTO.iterator();
                while (iterator.hasNext()) {
                    final SilentOrderLossDTO next = iterator.next();
                    if (payEarnestMoneyPresaleOrderId.contains(next.getOrderId())) {
                        iterator.remove();
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(silentOrderHasChatLossDTO)) {
                final Iterator<SilentOrderLossDTO> iterator = silentOrderHasChatLossDTO.iterator();
                while (iterator.hasNext()) {
                    final SilentOrderLossDTO next = iterator.next();
                    if (payEarnestMoneyPresaleOrderId.contains(next.getOrderId())) {
                        iterator.remove();
                    }
                }
            }

        }
//--------------------预售订单和非预售订单分开---------------------//
        List<SilentOrderLossDTO> silentOrderLossDTOList = new ArrayList<>(silentOrderNoChatLossDTO.size() + silentOrderHasChatLossDTO.size());
        silentOrderLossDTOList.addAll(silentOrderNoChatLossDTO);
        silentOrderLossDTOList.addAll(silentOrderHasChatLossDTO);
        //预售订单
        List<SilentOrderLossDTO> preOrder = this.getPreOrder(silentOrderNoChatLossDTO, silentOrderHasChatLossDTO);
        //非预售订单
        List<SilentOrderLossDTO> noPreOrder = this.getNoPreOrder(silentOrderNoChatLossDTO, silentOrderHasChatLossDTO);
        //普通只留下订单状态为取消的订单，预售订单流失满足要求的订单
        List<SilentOrderLossDTO> orderPayLoss = keepCancelledOrdersForSilent(shop, drq, noPreOrder);
        orderPayLoss.addAll(preOrder);

        //1.客户当天只要付款过一单该客服其他下单的订单就不算流失
        //2.当天只要有打上售后的客服就不算流失
        List<SilentOrderLossDTO> finalOrderPayLoss = this.doRemoveCustomerServiceThatPaidOnTodayForSilentLoss(shop, drq, orderPayLoss);

        insertLossOrderDOList.addAll(conversionSilentOrderLoss(finalOrderPayLoss, silentUrgepaySwitch, silentUrgepayTime));

        int insertOrderEnquiryLossNum = lossOrderRecordDao.insertBatchShopLossOrderEnquiryLoss(shop,
                insertLossOrderDOList, date);
        if (logger.isDebugEnabled()) {

            logger.debug("shop：【{}】,handleEnquiryLoss OrderEnquiryLoss insert num = {}", shop.getTitle(),
                    insertOrderEnquiryLossNum);
        }
    }

    /**
     * 不算流失的情况
     * 1.静默流失订单移除当天付完款的客户所下的其他单
     * 2.当天只要有打上售后的客服就不算流失
     *
     * @param shop
     * @param drq
     * @param orderPayLoss
     * @return
     */
    private List<SilentOrderLossDTO> doRemoveCustomerServiceThatPaidOnTodayForSilentLoss(JobShopDTO
                                                                                                 shop, ValidDateRangeQuery drq, List<SilentOrderLossDTO> orderPayLoss) {
        if (CollectionUtils.isEmpty(orderPayLoss)) return orderPayLoss;
        Set<String> buyerNickSet = orderPayLoss.stream().map(SilentOrderLossDTO::getBuyerNick).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(buyerNickSet)) return orderPayLoss;
        List<String> paidBuyerNick = orderDao.selectBuyerNickWasTodayPlacedAndPaid(shop, drq, buyerNickSet);
        paidBuyerNick.addAll(csOrderIndexDao.selectAfterSaleBuyer(shop, drq, buyerNickSet));
        if (CollectionUtils.isEmpty(paidBuyerNick)) return orderPayLoss;
        return orderPayLoss.stream().filter(ele -> !paidBuyerNick.contains(ele.getBuyerNick())).collect(Collectors.toList());
    }

    /**
     * 询单下单未付款流失订单移除当天付完款的客户所下的其他单
     *
     * @param shop
     * @param drq
     * @param csOrderBindInfo
     * @return
     */
    private List<CsOrderBindInfoDTO> doRemoveCustomerServiceThatPaidOnTodayForEnquiryLoss(JobShopDTO
                                                                                                  shop, ValidDateRangeQuery drq, List<CsOrderBindInfoDTO> csOrderBindInfo) {
        if (CollectionUtils.isEmpty(csOrderBindInfo)) return csOrderBindInfo;
        Set<String> buyerNickSet = csOrderBindInfo.stream().map(CsOrderBindInfoDTO::getBuyerNick).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(buyerNickSet)) return csOrderBindInfo;
        List<String> paidBuyerNick = orderDao.selectBuyerNickWasTodayPlacedAndPaid(shop, drq, buyerNickSet);
        if (CollectionUtils.isEmpty(paidBuyerNick)) return csOrderBindInfo;
        return csOrderBindInfo.stream().filter(ele -> !paidBuyerNick.contains(ele.getBuyerNick())).collect(Collectors.toList());
    }

    private List<SilentOrderLossDTO> getNoPreOrder
            (List<SilentOrderLossDTO> silentOrderNoChatLossDTO, List<SilentOrderLossDTO> silentOrderHasChatLossDTO) {
        if (CollectionUtils.isEmpty(silentOrderNoChatLossDTO) && CollectionUtils.isEmpty(silentOrderHasChatLossDTO))
            return new ArrayList<>();
        List<SilentOrderLossDTO> preOrder = new ArrayList<>();
        List<SilentOrderLossDTO> silentOrderLossPreOrder = silentOrderNoChatLossDTO.stream().filter(silentOrderNoChatLoss -> silentOrderNoChatLoss.getOrderType() == 0).collect(Collectors.toList());//订单类型 0 普通订单 1预售订单
        List<SilentOrderLossDTO> silentOrderHasChatLossPreOrder = silentOrderHasChatLossDTO.stream().filter(silentOrderHasChatLoss -> Boolean.FALSE.equals(silentOrderHasChatLoss.getPresale())).collect(Collectors.toList());//Presale：false:非预售 true:预售订单
        preOrder.addAll(silentOrderLossPreOrder);
        preOrder.addAll(silentOrderHasChatLossPreOrder);
        return preOrder;
    }

    private List<SilentOrderLossDTO> getPreOrder
            (List<SilentOrderLossDTO> silentOrderNoChatLossDTO, List<SilentOrderLossDTO> silentOrderHasChatLossDTO) {
        if (CollectionUtils.isEmpty(silentOrderNoChatLossDTO) && CollectionUtils.isEmpty(silentOrderHasChatLossDTO))
            return new ArrayList<>();
        List<SilentOrderLossDTO> preOrder = new ArrayList<>();
        List<SilentOrderLossDTO> silentOrderLossPreOrder = silentOrderNoChatLossDTO.stream().filter(silentOrderNoChatLoss -> silentOrderNoChatLoss.getOrderType() == 1).collect(Collectors.toList());//订单类型 0 普通订单 1预售订单
        List<SilentOrderLossDTO> silentOrderHasChatLossPreOrder = silentOrderHasChatLossDTO.stream().filter(silentOrderHasChatLoss -> Boolean.TRUE.equals(silentOrderHasChatLoss.getPresale())).collect(Collectors.toList());//Presale：false:非预售 true:预售订单
        preOrder.addAll(silentOrderLossPreOrder);
        preOrder.addAll(silentOrderHasChatLossPreOrder);
        return preOrder;
    }

    /**
     * conversionOrderEnquiryLoss:(询单下单未付款数据转换). <br/>
     *
     * @param csLst
     * @return
     */

    private List<LossOrderRecordDTO> conversionOrderEnquiryLoss(JobShopDTO shop, List<CsDTO> csLst, ValidDateRangeQuery drq, List<Long> parentOrderIdList) {
        List<LossOrderRecordDTO> enquiryOrderLossList = Lists.newArrayList();
//		//询单有效期内下单未付款的订单(17号下单未付款的订单)
//		List<CsOrderBindInfoDTO> retCsOrderBindLst = csOrderBindDao.selectShopCsOrderBindByOrderCreatedDateForLossRecord(shop,csLst,drq);
        //---2019/05/06zaj:询单下单未付款分析排除已经付定金了的预售订单
//		List<Long> downPaymentOrderId = presaleOrderDao.selectDownPaymentOrderIdByCreateTime(shop,drq);

        //询单有效期内下单未付款的订单(17号下单未付款的订单)
        List<CsOrderBindInfoDTO> retCsOrderBindLst = csOrderBindDao.selectShopCsOrderBindByOrderCreatedDateForLossRecord(shop, csLst, drq);

        //---拿出预售的订单 不算流失的订单
        List<Long> payEarnestMoneyPresaleOrderId = null;
        if (CollectionUtils.isNotEmpty(retCsOrderBindLst)) {
            final List<Long> presaleOrder = retCsOrderBindLst.stream().filter(CsOrderBindInfo -> Boolean.TRUE.equals(CsOrderBindInfo.getPresale())).map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(presaleOrder)) {
                payEarnestMoneyPresaleOrderId = presaleOrderDao.selectPayEarnestMoneyPresaleOrderByOrderLst(shop, drq, presaleOrder);
            }
        }


        if (CollectionUtils.isNotEmpty(payEarnestMoneyPresaleOrderId) && CollectionUtils.isNotEmpty(retCsOrderBindLst)) {
            final Iterator<CsOrderBindInfoDTO> iterator = retCsOrderBindLst.iterator();
            while (iterator.hasNext()) {
                final CsOrderBindInfoDTO next = iterator.next();
                if (payEarnestMoneyPresaleOrderId.contains(next.getOrderId())) {
                    iterator.remove();
                }
            }
        }

        //--------------------预售订单和非预售订单分开---------------------//
        //预售订单
        List<CsOrderBindInfoDTO> preOrder = retCsOrderBindLst.stream().filter(csOrderBindInfo -> Boolean.TRUE.equals(csOrderBindInfo.getPresale())).collect(Collectors.toList());
        //非预售订单
        List<CsOrderBindInfoDTO> otherOrder = retCsOrderBindLst.stream().filter(csOrderBindInfo -> Boolean.FALSE.equals(csOrderBindInfo.getPresale())).collect(Collectors.toList());

        //普通只留下订单状态为取消的订单，预售订单流失满足要求的订单
        List<CsOrderBindInfoDTO> csOrderBindInfo = keepCancelledOrders(shop, drq, otherOrder);
        csOrderBindInfo.addAll(preOrder);

        //---客户当天只要付款过一单该客服其他下单的订单就不算流失
        List<CsOrderBindInfoDTO> finalCsOrderBindInfo = this.doRemoveCustomerServiceThatPaidOnTodayForEnquiryLoss(shop, drq, csOrderBindInfo);


        //落实下单Set
        Set<Long> csOrderBindOrderIdSet = Optional.ofNullable(finalCsOrderBindInfo)
                .orElse(Lists.newArrayList())
                .stream()
                .map(CsOrderBindInfoDTO::getOrderId)
                .collect(Collectors.toSet());
        List<Long> csOrderBindOrderIdLst = Lists.newArrayList(csOrderBindOrderIdSet);

        //查询对应订单的聊天时间
        List<CsOrderBindInfoDTO> csOrderIndexLst = csOrderIndexDao.selectShopCsOrderIndexByOrderIdLstForLossRecord(shop, csOrderBindOrderIdLst, drq);

        List<CsOrderBindInfoDTO> csOrderBindLst = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(csOrderIndexLst)) {
            b:
            for (CsOrderBindInfoDTO bind : retCsOrderBindLst) {
                for (CsOrderBindInfoDTO index : csOrderIndexLst) {
                    if (bind.getOrderId().toString().equals(index.getOrderId().toString())) {
                        bind.setAcFirstReplyDate(index.getAcFirstReplyDate());
                        bind.setAcFirstChatDate(index.getAcFirstChatDate());
                        bind.setFirstChatDate(index.getFirstChatDate());
                        bind.setLastChatDate(index.getLastChatDate());
                        csOrderBindLst.add(bind);
                        continue b;
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(csOrderBindLst)) {
            LossOrderRecordDTO lossOrderEnquiryDO;
            for (CsOrderBindInfoDTO csOrderBind : csOrderBindLst) {
                if (parentOrderIdList.contains(csOrderBind.getOrderId())) continue;

                lossOrderEnquiryDO = new LossOrderRecordDTO();
                if (csOrderBind.getAcFirstReplyDate() != null && csOrderBind.getAcFirstChatDate() != null) {
                    lossOrderEnquiryDO.setIsChatAfterOrdered(true);
                    if (csOrderBind.getAcFirstReplyDate().after(csOrderBind.getAcFirstChatDate())) {
                        //客户说话
                        lossOrderEnquiryDO.setOrderChatType(CommonConstants.CUSTOMER_INITIATIVE_CHAT);
                    } else {
                        //客服先说
                        lossOrderEnquiryDO.setOrderChatType(CommonConstants.CS_INITIATIVE_CHAT);
                    }
                } else if (csOrderBind.getAcFirstReplyDate() != null) {
                    lossOrderEnquiryDO.setIsChatAfterOrdered(true);
                    lossOrderEnquiryDO.setOrderChatType(CommonConstants.CS_INITIATIVE_CHAT);
                } else if (csOrderBind.getAcFirstChatDate() != null) {
                    lossOrderEnquiryDO.setIsChatAfterOrdered(true);
                    lossOrderEnquiryDO.setOrderChatType(CommonConstants.CUSTOMER_INITIATIVE_CHAT);
                } else {
                    lossOrderEnquiryDO.setIsChatAfterOrdered(false);
                }
                lossOrderEnquiryDO.setShopId(csOrderBind.getShopId());
                lossOrderEnquiryDO.setCsNick(csOrderBind.getCsNick());
                lossOrderEnquiryDO.setCustomer(csOrderBind.getBuyerNick());
                lossOrderEnquiryDO.setDate(csOrderBind.getDate());
                lossOrderEnquiryDO.setOrderCreated(csOrderBind.getOrderCreated());
                lossOrderEnquiryDO.setOrderPayment(csOrderBind.getOrderPayment() == null ? 0 : csOrderBind.getOrderPayment());
                lossOrderEnquiryDO.setOrderGoodsNum(csOrderBind.getOrderGoodsNum());
                lossOrderEnquiryDO.setOrderId(csOrderBind.getOrderId());
                lossOrderEnquiryDO.setStartDateTime(csOrderBind.getFirstChatDate());
                lossOrderEnquiryDO.setEndDateTime(csOrderBind.getLastChatDate());
                lossOrderEnquiryDO.setType(CommonConstants.LOSS_TYPE_ENQUIRY_ORDER);
                //通过 OrderPayment赋值为total_fee    根据订单id查询相应的订单
//				Double totalFee = orderDao.selectTotalFeeByOrderId(shop,drq.getStartDate(), drq.getEndDate(),csOrderBind.getOrderId());
//				lossOrderEnquiryDO.setOrderPayment(totalFee == null?0:totalFee);
                enquiryOrderLossList.add(lossOrderEnquiryDO);
            }
        }
        return enquiryOrderLossList;
    }

    private List<CsOrderBindInfoDTO> keepCancelledOrders(JobShopDTO shop, ValidDateRangeQuery
            drq, List<CsOrderBindInfoDTO> retCsOrderBindLst) {
        List<CsOrderBindInfoDTO> finalCsOrderBindInfo = new ArrayList<>();
        if (CollectionUtils.isEmpty(retCsOrderBindLst)) {
            return new ArrayList<>();
        }
        List<Long> orderIdLst = retCsOrderBindLst.stream().map(CsOrderBindInfoDTO::getOrderId).collect(Collectors.toList());
        //获取所有的订单信息
        List<OrderDTO> orderLst = orderDao.selectCanceledOrderByOrderLst(shop, drq, orderIdLst);
        List<OrderDTO> cancelOrderLst = orderDao.filterCancelOrder(orderLst);
        a:
        for (OrderDTO cancelOrderDTO : cancelOrderLst) {
            for (CsOrderBindInfoDTO csOrderBindInfoDTO : retCsOrderBindLst) {
                if (cancelOrderDTO.getOrderId().equals(csOrderBindInfoDTO.getOrderId())) {
                    finalCsOrderBindInfo.add(csOrderBindInfoDTO);
                    continue a;
                }
            }
        }
        return finalCsOrderBindInfo;
    }

    private List<SilentOrderLossDTO> keepCancelledOrdersForSilent(JobShopDTO shop, ValidDateRangeQuery
            drq, List<SilentOrderLossDTO> noPreOrder) {
        List<SilentOrderLossDTO> silentOrderLossDTO = new ArrayList<>();
        if (CollectionUtils.isEmpty(noPreOrder)) {
            return new ArrayList<>();
        }
        List<Long> orderIdLst = noPreOrder.stream().map(SilentOrderLossDTO::getOrderId).collect(Collectors.toList());
        //获取所有的订单信息
        List<OrderDTO> orderLst = orderDao.selectCanceledOrderByOrderLst(shop, drq, orderIdLst);
        List<OrderDTO> cancelOrderLst = orderDao.filterCancelOrder(orderLst);
        a:
        for (OrderDTO cancelOrderDTO : cancelOrderLst) {
            for (SilentOrderLossDTO silentOrderLoss : noPreOrder) {
                if (cancelOrderDTO.getOrderId().equals(silentOrderLoss.getOrderId())) {
                    silentOrderLossDTO.add(silentOrderLoss);
                    continue a;
                }
            }
        }
        return silentOrderLossDTO;
    }

    /**
     * conversionSilentOrderLoss:(静默流失数据转换). <br/>
     *
     * @param silentOrderLossList
     * @param silentUrgepayTime
     * @param silentUrgepaySwitch
     * @return
     * @since JDK 1.8
     */
    private List<LossOrderRecordDTO> conversionSilentOrderLoss
    (List<SilentOrderLossDTO> silentOrderLossList, Boolean silentUrgepaySwitch, Integer silentUrgepayTime) {
        List<LossOrderRecordDTO> insertSilentLossList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(silentOrderLossList)) {
            for (SilentOrderLossDTO silentOrderLoss : silentOrderLossList) {
                LossOrderRecordDTO lossRecord = new LossOrderRecordDTO();
                // 判断下单后是否有客服说话，判断是否是客服主动说话
                if (silentOrderLoss.getAcFirstReplyDate() != null && silentOrderLoss.getAcFirstChatDate() != null) {
                    lossRecord.setIsChatAfterOrdered(true);
                    if (silentOrderLoss.getAcFirstReplyDate().after(silentOrderLoss.getAcFirstChatDate())) {
                        //客户说话
                        lossRecord.setOrderChatType(CommonConstants.CUSTOMER_INITIATIVE_CHAT);
                    } else {
                        //客服先说
                        lossRecord.setOrderChatType(CommonConstants.CS_INITIATIVE_CHAT);
                    }
                } else if (silentOrderLoss.getAcFirstReplyDate() != null) {
                    lossRecord.setIsChatAfterOrdered(true);
                    lossRecord.setOrderChatType(CommonConstants.CS_INITIATIVE_CHAT);
                } else if (silentOrderLoss.getAcFirstChatDate() != null) {
                    lossRecord.setIsChatAfterOrdered(true);
                    lossRecord.setOrderChatType(CommonConstants.CUSTOMER_INITIATIVE_CHAT);
                } else {
                    lossRecord.setIsChatAfterOrdered(false);
                }
                // 静默下单后有聊天的，聊天时间赋值
                if (lossRecord.getIsChatAfterOrdered()) {
                    lossRecord.setCsNick(silentOrderLoss.getCsNick());
                    lossRecord.setStartDateTime(silentOrderLoss.getFirstChatDate());
                    lossRecord.setEndDateTime(silentOrderLoss.getLastChatDate());
                }
                lossRecord.setOrderCreated(silentOrderLoss.getCreated());
                // 静默下单催付时间限制过滤  : 静默下单催付时间限制开启，可允许催付时间内催付，算主动催付失败，不算流失
                if (silentUrgepaySwitch // 静默下单催付时间限制开启
                        && lossRecord.getOrderChatType() != null    // 下单后有聊天
                        && lossRecord.getOrderChatType() == CommonConstants.CS_INITIATIVE_CHAT // 客服主动聊天
                        && (lossRecord.getOrderCreated().getTime() + silentUrgepayTime * 60 * 1000) < lossRecord.getStartDateTime().getTime()) {// 在可催付返回外
                    continue;
                }
                lossRecord.setShopId(silentOrderLoss.getShopId());
                lossRecord.setCustomer(silentOrderLoss.getBuyerNick());
                lossRecord.setDate(silentOrderLoss.getDate());
                lossRecord.setOrderGoodsNum(silentOrderLoss.getOrderGoodsNum() == null ? 0 : silentOrderLoss.getOrderGoodsNum());
                lossRecord.setOrderId(silentOrderLoss.getOrderId());
                lossRecord.setOrderPayment(silentOrderLoss.getPayment() == null ? 0 : silentOrderLoss.getPayment());
                lossRecord.setType(CommonConstants.LOSS_TYPE_SILENT_ORDER);
                insertSilentLossList.add(lossRecord);
            }
        }
        return insertSilentLossList;
    }

    @Override
    public void calculateShopCsLossData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        long s = System.currentTimeMillis();
        if (jobShop.getCsLst().isEmpty()) {
            logger.error("shop csLst isEntry");
            return;
        }
        //计算店铺&&客服询单流失相关
        calculateShopCsEnquiryLossDate(jobShop, jobDate, isDelData);
        //计算店铺&&客服下单流失相关
        calculateShopCsOrderLossDate(jobShop, jobDate, isDelData);
        //计算店铺&&客服出库流失相关
        calculateShopCsOutLossDate(jobShop, jobDate, isDelData);
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("客服每日流失统计	cal shop/cs loss data time：{} ms", (e - s));
        }
    }

    private void calculateShopCsOutLossDate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        List<Date> dates = jobDate.getOutStockLossDates();
        if (dates.isEmpty()) {
            logger.warn("calculateShopCsEnquiryLossData dates is empty");
            return;
        }
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
        for (Date date : dates) {
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }
            List<CsLossRecordDTO> csLossList = Lists.newArrayList();
            ValidDateRangeQuery drq = new ValidDateRangeQuery();
            drq.setStartDate(DateUtil.getStartTimeOfDate(date));
            drq.setEndDate(DateUtil.getEndTimeOfDate(date));
            if (isDelData) {
                shopTeamLossRecordDao.deleteShopTeamLossRecordByShopByDate(shop, date, CommonConstants.LOSS_CS_OUT_STOCK);
                csLossRecordDao.deleteShopCsLossRecordByDate(shop, drq.getStartDate(), drq.getEndDate(),
                        CommonConstants.LOSS_CS_OUT_STOCK);
//				logger.info("calculateShopCsLossData {}-{} 内 deleteNum = {}", jobDate.getStartDate(), jobDate.getEndDate(),
//						deleteNum);
            }
            List<LossOrderRecordDTO> selectLossOutStockOrderLst = lossOrderRecordDao.selectLossEnquiryOrderByDate(shop, date, CommonConstants.LOSS_TYPE_OUT_STOCK, drq);
            List<LossOrderRecordDTO> lossOutStockOrderLst = Optional.ofNullable(selectLossOutStockOrderLst)
                    .orElse(Lists.newArrayList());
            //客服流失统计（同一客服下，买家去重）
            Map<String, List<LossOrderRecordDTO>> csOutStockOrderLossMap = lossOutStockOrderLst.stream().collect(Collectors.groupingBy(LossOrderRecordDTO::getCsNick));
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop, date, date, lossOutStockOrderLst.stream().map(LossOrderRecordDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            for (CsDTO cs : targetCsLst) {
                String csNick = cs.getNick();
                CsLossRecordDTO csLoss = new CsLossRecordDTO();
                csLoss.setCsNick(csNick);
                csLoss.setDate(date);
                csLoss.setShopId(shop.getShopId());
                if (csOutStockOrderLossMap.containsKey(csNick)) {
                    List<LossOrderRecordDTO> csOutStockOrderLosslist = csOutStockOrderLossMap.get(cs.getNick());
                    conversionCsOrderLoss(csLoss, csOutStockOrderLosslist, CommonConstants.LOSS_CS_OUT_STOCK, needCalSku);
                    csLoss.setOrderNum(csOutStockOrderLosslist.size());
                    csLossList.add(csLoss);
                }
            }
            //店铺流失统计（店铺下：买家去重）
            Set<String> shopOutStockLossCustomerSers = Sets.newHashSet();
            int orderGoodsNum = 0;
            double orderSaleAmount = 0;
            for (LossOrderRecordDTO shopOutStockOrderLoss : lossOutStockOrderLst) {
                shopOutStockLossCustomerSers.add(shopOutStockOrderLoss.getCustomer());
                orderGoodsNum += shopOutStockOrderLoss.getOrderGoodsNum() == null ? 0 : shopOutStockOrderLoss.getOrderGoodsNum();
                orderSaleAmount += shopOutStockOrderLoss.getOrderPayment() == null ? 0 : shopOutStockOrderLoss.getOrderPayment();
            }
            ShopTeamLossRecordDTO shopOutStockOrderLoss = new ShopTeamLossRecordDTO(shop.getShopId(), date, lossOutStockOrderLst.size(), shopOutStockLossCustomerSers.size(), orderGoodsNum, orderSaleAmount, CommonConstants.LOSS_CS_OUT_STOCK);
            shopTeamLossRecordDao.insertShopTeamLossRecord(shop, shopOutStockOrderLoss);
            csLossRecordDao.insertBatchShopCsLossRecord(shop, csLossList, date);
//			logger.info("calculateShopCsLossData {}-{} 内 insertNum = {}",drq.getStartDate(),drq.getEndDate(),insertNum);
        }
    }

    private void calculateShopCsOrderLossDate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        JobShopDTO shop = jobShop.getShop();
        ValidDateRangeQuery drq = new ValidDateRangeQuery();
        List<Date> dates = jobDate.getOrderLossDates();
        if (dates.isEmpty()) {
            logger.warn("calculateShopCsEnquiryLossData dates is empty");
            return;
        }
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
        for (Date date : dates) {
            List<CsLossRecordDTO> csLossList = Lists.newArrayList();
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }
            drq.setStartDate(DateUtil.getStartTimeOfDate(date));
            drq.setEndDate(DateUtil.getEndTimeOfDate(date));
            if (isDelData) {
                shopTeamLossRecordDao.deleteShopTeamLossRecordByShopByDate(shop, date, CommonConstants.LOSS_CS_ENQUIRY_ORDER);
                csLossRecordDao.deleteShopCsLossRecordByDate(shop, drq.getStartDate(), drq.getEndDate(),
                        CommonConstants.LOSS_CS_ENQUIRY_ORDER);
            }
            List<LossOrderRecordDTO> selectLossEnquiryOrderLst = lossOrderRecordDao.selectLossEnquiryOrderByDate(shop, date, CommonConstants.LOSS_TYPE_ENQUIRY_ORDER, drq);
            List<LossOrderRecordDTO> lossEnquiryOrderLst = Optional.ofNullable(selectLossEnquiryOrderLst).orElse(Lists.newArrayList());
            //客服流失统计
            Map<String, List<LossOrderRecordDTO>> csEnquiryOrderLossMap = lossEnquiryOrderLst
                    .stream()
                    .collect(Collectors.groupingBy(LossOrderRecordDTO::getCsNick));
            Map<String, Object> filterMap = goodsHandleBusiness.getFilterOrderIdsOfFinal(jobShop,
                    date,
                    date,
                    lossEnquiryOrderLst.stream().map(LossOrderRecordDTO::getOrderId).collect(Collectors.toList()));
            Collection<OrderDetailDTO> notFilterOrderDetailLst = (Collection<OrderDetailDTO>) filterMap.get(GoodFilterEnum.NOT_FILTER_ORDER_DETAIL.getKey());
            Map<Long, List<OrderDetailDTO>> needCalSku = notFilterOrderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getOrderId));
            for (CsDTO cs : targetCsLst) {
                String csNick = cs.getNick();
                CsLossRecordDTO csLoss = new CsLossRecordDTO();
                csLoss.setCsNick(csNick);
                csLoss.setDate(date);
                csLoss.setShopId(shop.getShopId());
                if (csEnquiryOrderLossMap.containsKey(csNick)) {
                    List<LossOrderRecordDTO> csLossOrderEnquirylist = csEnquiryOrderLossMap.get(cs.getNick());
                    conversionCsOrderLoss(csLoss, csLossOrderEnquirylist, CommonConstants.LOSS_CS_ENQUIRY_ORDER, needCalSku);
                    csLossList.add(csLoss);
                } else {
                    // logger.info("客服：{} 在 {} 号，没有下单流失", csNick, date);
                }
            }
            //店铺流失统计
            Set<String> shopLossCustomerSers = Sets.newHashSet();
            int orderGoodsNum = 0;
            double orderSaleAmount = 0;
            for (LossOrderRecordDTO shopEnquiryOrderLoss : lossEnquiryOrderLst) {
                shopLossCustomerSers.add(shopEnquiryOrderLoss.getCustomer());
                orderGoodsNum += shopEnquiryOrderLoss.getOrderGoodsNum() == null ? 0 : shopEnquiryOrderLoss.getOrderGoodsNum();
                orderSaleAmount += shopEnquiryOrderLoss.getOrderPayment() == null ? 0 : shopEnquiryOrderLoss.getOrderPayment();
            }
            ShopTeamLossRecordDTO shopEnquiryOrderLoss = new ShopTeamLossRecordDTO(shop.getShopId(), date, lossEnquiryOrderLst.size(), shopLossCustomerSers.size(), orderGoodsNum, orderSaleAmount, CommonConstants.LOSS_CS_ENQUIRY_ORDER);
            shopTeamLossRecordDao.insertShopTeamLossRecord(shop, shopEnquiryOrderLoss);
            csLossRecordDao.insertBatchShopCsLossRecord(shop, csLossList, date);
            // 计算客服询单下单流失相关统计---------------------------------------end
        }
    }

    /**
     * calculateShopCsEnquiryLossDate:(计算店铺&&客服询单流失相关信息). <br/>
     */
    private void calculateShopCsEnquiryLossDate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        JobShopDTO shop = jobShop.getShop();
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        CsLossRecordDTO csLoss;
        //计算客服询单流失相关统计---------------------------------------start
        List<Date> dates = jobDate.getCsEnquiryLossForEnquiryDates();
        if (dates.isEmpty()) {
            logger.warn("calculateShopCsEnquiryLossData dates is empty");
            return;
        }
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
        for (Date date : dates) {
            List<CsLossRecordDTO> csLossList = Lists.newArrayList();
//			if(DateUtils.isSameDay(DateUtil.getDateByPeriod(date, enquiryValidDays-1),new Date())){
//				continue;
//			}
            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            if (CollectionUtils.isEmpty(targetCsLst)) {
                continue;
            }
            ValidDateRangeQuery drq = new ValidDateRangeQuery();
            drq.setStartDate(DateUtil.getStartTimeOfDate(date));
            drq.setEndDate(DateUtil.getEndTimeOfDate(date));
            if (isDelData) {
                shopTeamLossRecordDao.deleteShopTeamLossRecordByShopByDate(shop, date, CommonConstants.LOSS_CS_ENQUIRY);
                csLossRecordDao.deleteShopCsLossRecordByDate(shop, drq.getStartDate(), drq.getEndDate(),
                        CommonConstants.LOSS_CS_ENQUIRY);
//				logger.debug("calculateShopCsLossData {}-{}  deleteCsLossRecordNum = {}", jobDate.getStartDate(), jobDate.getEndDate(),
//						deleteCsLossRecordNum);
            }

            List<LossEnquiryRecordDTO> selectEnquiryLostLst = lossEnquiryRecordDao.selectEnquiryLostRecordLstByDate(shop, date, drq);
            List<LossEnquiryRecordDTO> enquiryLostLst = Optional.ofNullable(selectEnquiryLostLst).orElse(Lists.newArrayList());
            //客服统计
            Map<String, List<LossEnquiryRecordDTO>> csEnquiryLossMap = enquiryLostLst
                    .stream()
                    .collect(Collectors
                            .groupingBy(LossEnquiryRecordDTO::getCsNick));

            for (CsDTO cs : targetCsLst) {
                String csNick = cs.getNick();
                csLoss = new CsLossRecordDTO();
                csLoss.setCsNick(csNick);
                csLoss.setDate(date);
                csLoss.setShopId(shop.getShopId());
                if (csEnquiryLossMap.containsKey(csNick)) {
                    csLoss.setCustomerNum(csEnquiryLossMap.get(cs.getNick()).size());
                    csLoss.setType(CommonConstants.LOSS_CS_ENQUIRY);
                    csLossList.add(csLoss);
                } else {
//					logger.debug("csNick：{} in date = {}，enquiryRecord is Entry", csNick, date);
                }
            }
            //店铺统计
            Set<String> customerSets = enquiryLostLst
                    .stream()
                    .map(LossEnquiryRecordDTO::getCustomer)
                    .collect(Collectors.toSet());
            ShopTeamLossRecordDTO shopLoss = new ShopTeamLossRecordDTO(shop.getShopId(), date, customerSets.size());
            shopTeamLossRecordDao.insertShopTeamLossRecord(shop, shopLoss);
            csLossRecordDao.insertBatchShopCsLossRecord(shop, csLossList, date);
//			logger.debug("calculateShopCsLossData {}-{}  insertCsLossRecordNum = {}",drq.getStartDate(),drq.getEndDate(),insertCsLossRecordNum);
            //计算客服询单流失相关统计---------------------------------------end
        }
    }

    private void conversionCsOrderLoss(CsLossRecordDTO csLoss, List<LossOrderRecordDTO> csLossOrderEnquirylist,
                                       int type, Map<Long, List<OrderDetailDTO>> needCalSku) {
        List<String> customerList = Lists.newArrayList();
        double lossPayment = 0.0;
        int payGoodsLossNum = 0;
        for (LossOrderRecordDTO outStockLoss : csLossOrderEnquirylist) {
            lossPayment += outStockLoss.getOrderPayment() == null ? 0 : outStockLoss.getOrderPayment();
            List<OrderDetailDTO> orderDetailDTOS = needCalSku.get(outStockLoss.getOrderId());
            if (CollUtil.isNotEmpty(orderDetailDTOS)) {
                for (OrderDetailDTO dto : orderDetailDTOS) {
                    payGoodsLossNum += BaseUtils.getNonNull(dto.getItemNum());
                }
            }
            if (!customerList.contains(outStockLoss.getCustomer())) {
                customerList.add(outStockLoss.getCustomer());
            }
        }
        csLoss.setOrderNum(csLossOrderEnquirylist.size());
        csLoss.setCustomerNum(customerList.size());
        csLoss.setOrderSaleAmount(lossPayment);
        csLoss.setOrderGoodsNum(payGoodsLossNum);
        csLoss.setType(type);
    }

    @Override
    public void handleOrderLoss(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        List<Date> orderLossDates = jobDate.getOrderLossDates();
        //询单有效期小于三天要用三天来算，因为重拉三天前的订单信息会有三天前的付款询单有效期设置低于三天少算
        List<Date> calDate = calNewOrderIndexDate(jobDate, jobShop.getShopSystemsetting().getEnquiryValidDurationTime(), orderLossDates);
        if (calDate.isEmpty()) {
            return;
        }
//        dates.sort(Comparator.comparing(Date::getTime).reversed());
        Date startDate = calDate.get(0);
        Date endDate = calDate.get(calDate.size() - 1);

        List<OrderDTO> presaleParentOrderList = orderDao.selectParentOrderForPresaleOrderByShopIdAndDateAndBuyerNick(jobShop.getShop(), com.pes.jd.util.DateUtils.getDateByPeriod(startDate, -49), com.pes.jd.util.DateUtils.getDateByPeriod(startDate, -3), com.pes.jd.util.DateUtils.getEndTimeOfDate(endDate));
        List<OrderDTO> parentOrderLst = orderDao.selectParentOrderToPayCsSaleOrderLstNew(jobShop.getShop(), DateUtil.getDateByPeriod(startDate, -jobShop.getShopSystemsetting().getEnquiryValidDurationTime() + 1), com.pes.jd.util.DateUtils.getEndTimeOfDate(endDate));

        List<Long> parentOrderIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(parentOrderLst)) {
            parentOrderIdList = parentOrderLst.stream().map(OrderDTO::getDirectTradeId).collect(Collectors.toList());
        }
        for (OrderDTO dto : presaleParentOrderList) {
            parentOrderIdList.add(dto.getTradeId());
            parentOrderIdList.add(dto.getDirectTradeId());
        }
        JobCsStatusQuery csStatusQuery = new JobCsStatusQuery(CommonConstants.CS_TYPE_SALE_PRE, CommonConstants.CS_STATUS_NOT_LOCK);
        long s = System.currentTimeMillis();

        for (Date date : calDate) {

            List<CsDTO> targetCsLst = CommonUtils.getTargetCsLst(jobShop, jobDate, date, csStatusQuery);
            //询单下单未付款
            handleEnquiryOrderLoss(jobShop, targetCsLst, date, isDelData, parentOrderIdList);
            //静默下单未付款
            handleSilentOrderLoss(jobShop, date, isDelData);
        }
        long e = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {

            logger.debug("下单未付款分析计算 order non payment analysis and calculation end,time:{}s", (e - s) / 1000);
        }

    }

    /**
     * 防止店铺系统设置从2天改成15天，会遗留询单流失脏数据
     *
     * @param jobShop
     * @param jobDate
     * @param isDelData
     */
    @Override
    public void deleteShopLossRecord(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) {
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        JobShopDTO shop = jobShop.getShop();
        Date dateByPeriod = DateUtil.getDateByPeriod(new Date(), 1 - enquiryValidDays);
        lossEnquiryRecordDao.deleteShopLossRecordByDate(shop, DateUtil.getStartTimeOfDate(dateByPeriod), DateUtil.getEndTimeOfDate(new Date()), null);
    }

    private List<String> handleCrossDayChat(JobShopQuery jobShop, Date date,
                                            List<ChatBO> cpBOLst, Boolean statusFlag) {

        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //业务分割点
        int businessSplittingPoints = sys.getSchedulingTimeDot();

        if (businessSplittingPoints == 0) {
            return null;
        }

        //业务分割时间
        Date busSplitTime = DateUtils.addMinutes(date, businessSplittingPoints * 60);

        //0点前后最大等待时间
        Date sMinTime = DateUtils.addMinutes(date, -sys.getMaxWaitTime());

        Date eMaxTime = DateUtils.addMinutes(date, sys.getMaxWaitTime());

        //计算日期结束时间
        Date endDate = DateFormatUtils.getEndTimeOfDate(date);

        List<String> crossChatBuyerLst = Lists.newArrayList();

        CommonCsChatpeerDTO chatPeer;
        List<CsChatlogDTO> chatLogLst;
        for (ChatBO chatBO : cpBOLst) {
            chatPeer = chatBO.getCsChatpeer();
            chatLogLst = chatBO.getCrossChatRelatedChatLogLst();
            if (CollectionUtils.isEmpty(chatLogLst)) {
                continue;
            }
            boolean beforeSpitChat = compareDate(sMinTime, date, chatLogLst);  //0点之前最大等待时间
            if (!beforeSpitChat) {
                continue;
            }
            boolean afterSplitChat = compareDate(date, eMaxTime, chatLogLst);  //0点之后最大等待时间
            if (!afterSplitChat) {
                continue;
            }


            boolean nextDayChatFilter = compareDate(busSplitTime, endDate, chatLogLst); //业务天分隔点到第二天24时
            if (nextDayChatFilter) {
                chatPeer.setCrossChatFilter(Boolean.FALSE);
            } else {
                if (!statusFlag) {
                    chatPeer.setCrossChatFilter(Boolean.TRUE);
                }
                crossChatBuyerLst.add(chatPeer.getBuyerNick());
            }
        }

        return crossChatBuyerLst;

    }

    boolean compareDate(Date startDate, Date endDate, List<CsChatlogDTO> chatlogList) {
        boolean compareFlag = false;
        for (CsChatlogDTO csChatlogDTO : chatlogList) {
            Date chatTime = csChatlogDTO.getChatTime();
            //fix:2109 买家聊天才不会算跨天
            if (chatTime.after(startDate) && chatTime.before(endDate) && Objects.equals(csChatlogDTO.getDirection(), CommonConstants.BUYER_SAY)) {
                compareFlag = true;
                break;
            }
        }
        return compareFlag;
    }

    /**
     * 获取集合的字符串拼接格式
     *
     * @param list
     * @return 例：1,2,3,4,5
     */
    private static String getLstStr(List<Long> list) {
        StringBuilder orderLstBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                orderLstBuilder.append(list.get(i) + "");
            } else {
                orderLstBuilder.append("," + list.get(i));
            }
        }
        return orderLstBuilder.toString();
    }

    /**
     * 判断用户是否在跨天前下单
     * @param jobShop
     * @param crossBuyerNicks
     * @param crossDate
     */
    private Boolean checkCreateOrderBeforeCross(JobShopQuery jobShop, List<String> crossBuyerNicks, Date crossDate, List<String> targetBuyerNicks){
        logger.info("crossChatBuyerNickLst.size():{}", crossBuyerNicks.size());
        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        //业务分割点
        int businessSplittingPoints = sys.getSchedulingTimeDot() == null ? 0 : sys.getSchedulingTimeDot();
        //业务分割时间
        Date busSplitTime = DateUtils.addMinutes(crossDate, businessSplittingPoints * 60);
        JobShopDTO shop = jobShop.getShop();
        //计算日期结束时间
        Date endTime = DateFormatUtils.getEndTimeOfDate(crossDate);
        //查询有无下单
        HashSet<String> buyerSet = new HashSet<>(crossBuyerNicks);
        logger.info("busSplitTime:{}", busSplitTime);
        logger.info("endTime:{}", endTime);
        logger.info("shop:{}:{}", shop.getShopId(), shop.getSchemaId());
        logger.info("buyerSet:{}", buyerSet.toString());
        List<OrderDTO> orderDTOS = orderDao.selectOrdersByDateScopeAndBuyerNicks(shop, busSplitTime, endTime, buyerSet);
        if(CollectionUtils.isNotEmpty(orderDTOS)){
            orderDTOS.forEach(t -> {
                logger.info("orderDTOS: buyerNick:{}", t.getBuyerNick());
                if(buyerSet.contains(t.getBuyerNick())){
                    buyerSet.remove(t.getBuyerNick());
                    targetBuyerNicks.add(t.getBuyerNick());
                }
            });
        }
        if(!CollectionUtils.isEmpty(buyerSet)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public static void main(String[] args) {
        byte mt = 3;
        System.out.println((int) mt != 3);
    }
}
