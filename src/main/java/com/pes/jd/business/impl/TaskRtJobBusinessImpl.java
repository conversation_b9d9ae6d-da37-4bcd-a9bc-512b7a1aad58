package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.TaskRtJobBusiness;
import com.pes.jd.model.Result.SendMsgResult;
import com.pes.jd.ms.domain.Result.task.dispatching.MessageSendResult;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.DispatchingRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TaskRtJobBusinessImpl implements TaskRtJobBusiness {
    private final static Logger logger = LoggerFactory.getLogger(TaskRtJobBusinessImpl.class);

    @Resource
    private DispatchingRestTemplate dispatchingRestTemplate;


    @Override
    public SendMsgResult invokeTaskRtJob(Long shopId, String handleType, String rtDb, String rtSchemaId) {
        SendMsgResult result = new SendMsgResult(false);

        HttpEntity<Object> param = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .put("handleType",handleType)
                .put("rtDb",rtDb)
                .put("rtSchemaId",rtSchemaId)
                .toRequestEntity();

        RestResponseTypeRef<MessageSendResult> restResponseTypeRef;
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());
            restResponseTypeRef = dispatchingRestTemplate.postRest(serviceId,"/task/message/invokeTaskRtJob", param, new ParameterizedTypeReference<RestResponseTypeRef<MessageSendResult>>(){});
        } catch (Exception e) {
            logger.error("rtjob rpc task scheduler error",e);
            throw e;
        }
        if(restResponseTypeRef.getSuccess()){
            MessageSendResult apiResult = restResponseTypeRef.getData();
            logger.info("pull and cal shop rt data msg result:{}",apiResult.getSendResult());
        }
        return result;
    }
}
