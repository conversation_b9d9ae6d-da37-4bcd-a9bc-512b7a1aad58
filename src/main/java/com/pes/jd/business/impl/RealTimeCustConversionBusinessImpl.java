package com.pes.jd.business.impl;


import com.alibaba.fastjson.JSON;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.RealTimeCustConversionBusiness;
import com.pes.jd.data.api.VenderShopOperator;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.OrderLatelyDTO;
import com.pes.jd.model.DTO.ShopReservePresaleDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.CustConversionTwoParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.rtsub.CsNickTaskCount;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Result.rtsub.CsAllocateResult;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.MessageRestTemplate;
import com.pes.jd.rest.PopRtSubRestTemplate;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 实时绩效-代转换池（微服务）
 * <AUTHOR>
 *
 */
@Service
public class RealTimeCustConversionBusinessImpl implements RealTimeCustConversionBusiness {
	private static final Logger logger = LoggerFactory.getLogger(RealTimeCustConversionBusinessImpl.class);

	@Resource
	private PopRtSubRestTemplate popRtSubRestTemplate;
	@Resource
	private MessageRestTemplate messageRestTemplate;
	@Resource
	private PopSubRestTemplate popSubRestTemplate;
	@Resource
	private VenderShopOperator venderShopOperator;
	@Override
	public ApiResponse serachNeededAllocatedCsConversionList(ShopQuery shop, CustConversionParam param) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/conv/serachNeededAllocatedCsConversionList", body);
	}

	@Override
	public RestApiResponse2<CsAllocateResult> saveAllocatedCsConversionLst(ShopQuery shop, ShopUserDTO user, String csConversions, Integer flag, String manualMerchandisingBlacklistString) throws Exception {
		try {
			venderShopOperator.checkShopExpireBySessionKey(shop.getSessionKey());
		} catch (GainShopDataFailException e) {
			if (e.getErrorCode().equals("19")) {
				if (StringUtils.isNotBlank(shop.getOptionSessionKey())) {
					shop.setSessionKey(shop.getOptionSessionKey());
					logger.info("shopName {} main SessionKey is expire", shop.getTitle());
				} else {
					logger.info("shopName {} sub SessionKey is empty no update sessionKey", shop.getTitle());
				}
			}
		} catch (Exception e) {
			logger.error("shopName:{} checkShopExpireBySessionKey error:{}", shop.getTitle(), e.getMessage(), e);
		}
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		RestApiResponse2<CsAllocateResult> apiResponse = new RestApiResponse2<>();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", shopCommonParam)
				.put("user", user)
				.put("csConversions", csConversions)
				.put("colType", shop.getColType())
				.put("flag", flag)
				.put("manualMerchandisingBlacklistString", manualMerchandisingBlacklistString)
				.toRequestEntity();
		try {
			String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			apiResponse = popRtSubRestTemplate.postRestOfResult2(serviceId, "/conv/saveAllocatedCsConversionLst", body, new ParameterizedTypeReference<RestApiResponse2<CsAllocateResult>>() {
			});
		} catch (Exception e) {
			logger.error("saveAllocatedCsConversionLst error:{}", e.getMessage(), e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public RestApiResponse2<CsAllocateResult> oneStepSaveAllocatedCsConversionLst(ShopQuery shop, ShopUserDTO user, CustConversionParam param, String manualMerchandisingBlacklistString) {
		try {
			venderShopOperator.checkShopExpireBySessionKey(shop.getSessionKey());
		} catch (GainShopDataFailException e) {
			if (e.getErrorCode().equals("19")) {
				if (StringUtils.isNotBlank(shop.getOptionSessionKey())) {
					shop.setSessionKey(shop.getOptionSessionKey());
					logger.info("shopName {} main SessionKey is expire", shop.getTitle());
				} else {
					logger.info("shopName {} sub SessionKey is empty no update sessionKey", shop.getTitle());
				}
			}
		} catch (Exception e) {
			logger.error("shopName:{} checkShopExpireBySessionKey error:{}", shop.getTitle(), e.getMessage(), e);
		}
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		param.setColType(shop.getColType());
		RestApiResponse2<CsAllocateResult> apiResponse = new RestApiResponse2<>();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", shopCommonParam)
				.put("user", user)
				.put("param", param)
				.put("manualMerchandisingBlacklistString", manualMerchandisingBlacklistString)
				.toRequestEntity();
		try {
			String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			apiResponse = popRtSubRestTemplate.postRestOfResult2(serviceId, "/conv/oneStepSaveAllocatedCsConversionLst", body, new ParameterizedTypeReference<RestApiResponse2<CsAllocateResult>>() {
			});
		} catch (Exception e) {
			logger.error("oneStepSaveAllocatedCsConversionLst error:{}", e.getMessage(), e);
			throw e;
		}
		return apiResponse;
	}

	@Override
	public RestApiResponse2<CsAllocateResult> batchSaveAllocatedCsConversionLst(ShopQuery shop, ShopUserDTO user, CustConversionParam param, String manualMerchandisingBlacklistString) {
		try {
			venderShopOperator.checkShopExpireBySessionKey(shop.getSessionKey());
		} catch (GainShopDataFailException e) {
			if (e.getErrorCode().equals("19")) {
				if (StringUtils.isNotBlank(shop.getOptionSessionKey())) {
					shop.setSessionKey(shop.getOptionSessionKey());
					logger.info("shopName {} main SessionKey is expire", shop.getTitle());
				} else {
					logger.info("shopName {} sub SessionKey is empty no update sessionKey", shop.getTitle());
				}
			}
		} catch (Exception e) {
			logger.error("shopName:{} checkShopExpireBySessionKey error:{}", shop.getTitle(), e.getMessage(), e);
		}
		ShopCommonParam shopCommonParam = new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		param.setColType(shop.getColType());
		RestApiResponse2<CsAllocateResult> apiResponse = new RestApiResponse2<>();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", shopCommonParam)
				.put("user", user)
				.put("param", param)
				.put("manualMerchandisingBlacklistString", manualMerchandisingBlacklistString)
				.toRequestEntity();
		try {
			String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			apiResponse = popRtSubRestTemplate.postRestOfResult2(serviceId, "/conv/batchSaveAllocatedCsConversionLst", body, new ParameterizedTypeReference<RestApiResponse2<CsAllocateResult>>() {
			});
		} catch (Exception e) {
			logger.error("batchSaveAllocatedCsConversionLst error:{}", e.getMessage(), e);
			throw e;
		}
		return apiResponse;
	}

	@Deprecated
	@Override
	public ApiResponse getToolsSettingByShopId(ShopQuery shop)  {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/getToolsSettingByShopId", body);
		return apiResponse;
	}
	@Deprecated
	@Override
	public ApiResponse setShopToolsSetting(ShopQuery shop,String xiadanWord,String payWord,
			Boolean sendGoodsUrl) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		ApiResponse apiResponse = new ApiResponse();

		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("payWord", payWord)
				.put("xiadanWord", xiadanWord)

				.put("sendGoodsUrl", sendGoodsUrl)
				.put("isSendGoodsUrl", sendGoodsUrl)
				.toRequestEntity();

		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/setShopToolsSetting", body);
		return apiResponse;
	}
	
	@Override
	public ApiResponse serachAllocatedConvertedLst(ShopQuery shop,CustConversionParam param) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/conv/serachAllocatedConvertedLst", body);
	}
	@Override
	public ApiResponse serachAllocatedConvertedLstNew(ShopQuery shop, CustConversionTwoParam param) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/conv/serachAllocatedConvertedLstNew", body);
	}
	
	
	@Override
	public ApiResponse serachAllocateCsConversionForOneWarn(ShopQuery shop,CustConversionParam param) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.put("csNick", param.getCsNickLst().get(0))
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/conv/serachAllocateCsConversionForOneWarn", body);
	}
	@Override
	public ApiResponse serachCsConversionTaskTotal(ShopQuery shop,CustConversionParam param){
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		shopCommonParam.setRd(shop.getDbName());
		shopCommonParam.setRs(shop.getSchemaId());
		ApiResponse apiResponse;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/serachCsConversionTaskTotal", body);
		return apiResponse;
	}
	@Override
	public ApiResponse setOneWarnCsConversion(ShopQuery shop,CustConversionParam param,String operateType) {
		try {
			venderShopOperator.checkShopExpireBySessionKey(shop.getSessionKey());
		} catch (GainShopDataFailException e) {
			if(e.getErrorCode().equals("19")) {
				if (StringUtils.isNotBlank(shop.getOptionSessionKey())) {
					shop.setSessionKey(shop.getOptionSessionKey());
					logger.info("shopName {} main SessionKey is expire", shop.getTitle());
				} else {
					logger.info("shopName {} sub SessionKey is empty no update sessionKey", shop.getTitle());
				}
			}
		}catch (Exception e) {
			logger.error("shopName:{} checkShopExpireBySessionKey error:{}",shop.getTitle(),e.getMessage(),e);
		}
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("sessionKey",shop.getSessionKey())
				.put("param", param)
				.put("operateType", operateType)
				.put("colType", shop.getColType())
				.put("shop",shopCommonParam)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/setOneWarnCsConversion", body);
		return apiResponse;
	}
	@Override
	public ApiResponse updateAllocateCsNickById(ShopQuery shop,CustConversionParam param){
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(), shop.getRtDbName());
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/updateAllocateCsNickById", body);
		return apiResponse;
	}

	@Override
	public ApiResponse selectShopLoginCs(UserShopQuery shopQuery,List<UserQuery> userQueries,ShopUserDTO user) {
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("userQueries", userQueries)
				.put("user", user)
				.put("shop", shopQuery.getSelectedShop())
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shopQuery.getSelectedShop().getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		try {
			apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/selectShopLoginCs", body);
		} catch (HttpClientErrorException e) {
			throw e;
		}
		return apiResponse;
	}

	@Override
	public RestApiResponse2<CsNickTaskCount> selectShopPoolTaskCount(ShopQuery shop, CustConversionParam param, String csNick) {
		long s1=System.currentTimeMillis();
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(), shop.getRtDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.put("csNick", csNick)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			RestApiResponse2<CsNickTaskCount> resp= popRtSubRestTemplate.postRestOfResult2(serviceId, "/conv/selectShopPoolTaskCount", body, new ParameterizedTypeReference<RestApiResponse2<CsNickTaskCount>>() {});
			if(logger.isDebugEnabled()){
				logger.debug("微服务接受 分配数量耗时：{}ms",System.currentTimeMillis()-s1);
			}
			return resp;

	}

	@Override
	public ApiResponse batchUpdateAllocateCsNick(ShopQuery shop,CustConversionParam param) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(),shop.getRtSchemaId(), shop.getRtDbName());
		ApiResponse apiResponse = new ApiResponse();
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/batchUpdateAllocateCsNick", body);
		return apiResponse;
	}


	@Override
	public List<OrderLatelyDTO> getOrderLatelyDate(ShopQuery shop, String buyerNick)  {
		List<OrderLatelyDTO> orderLst=null;
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(),shop.getSchemaId(), shop.getDbName());
		ApiResponse apiResponse = null;
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("buyerNick", buyerNick)
				.put("shop",shopCommonParam)
				.toRequestEntity();
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.RT_MESSAGE.getName());
		apiResponse = messageRestTemplate.postRest(serviceId, "/OrderLately/getOrderLatelyDate", body);
		if(apiResponse!=null&&apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
			Map<String, Object> data = apiResponse.getData();
			if(MapUtils.isNotEmpty(data)){
				orderLst = JSON.parseArray(JSON.toJSONString(data.get("result")), OrderLatelyDTO.class);
			}

		}
		return orderLst;
	}

    @Override
    public ApiResponse serachNeededAllocatedCsConversionListOfSpu(ShopQuery shop, CustConversionParam param){
        ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
        ApiResponse apiResponse;
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shop",shopCommonParam)
                .put("param", param)
                .toRequestEntity();
        String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
        apiResponse = popRtSubRestTemplate.postRest(serviceId, "/conv/serachNeededAllocatedCsConversionList", body);
        return apiResponse;
    }

	@Override
	public ShopReservePresaleDTO selectShopReserveAndPresaleByActivityId(ShopQuery shop,String activityId)  {
		ShopCommonParam param=new ShopCommonParam(shop.getShopId(),shop.getSchemaId(),shop.getDbName());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop", param)
				.put("activityId",activityId)
				.toRequestEntity();

		RestResponseTypeRef<ShopReservePresaleDTO> resp = null;
		try {
			String serviceId = RestOperator.getMSServiceId(shop.getDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
			resp=popSubRestTemplate.postRest(serviceId,"/shop/reservePresale/selectShopReserveAndPresaleByActivityId", body, new ParameterizedTypeReference<RestResponseTypeRef<ShopReservePresaleDTO>>() {});
			if(resp.getSuccess()){
				return resp.getData();
			}
		} catch (Exception e) {
			logger.error("selectShopReserveAndPresaleByActivityId error:{}", e.getMessage(), e);
		}

		return null;
	}

	@Override
	public ApiResponse serachCsConversionTaskTotalNew(ShopQuery shop, CustConversionParam param) {
		ShopCommonParam shopCommonParam=new ShopCommonParam(shop.getShopId(), shop.getRtSchemaId(), shop.getRtDbName());
		shopCommonParam.setSessionKey(shop.getSessionKey());
		shopCommonParam.setRd(shop.getDbName());
		shopCommonParam.setRs(shop.getSchemaId());
		HttpEntity<Object> body = RequestEntityBuilder.builder()
				.put("shop",shopCommonParam)
				.put("param", param)
				.toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getRtDbName(), ApplicationServiceNameEnum.PROVIDER_RT_SUB.getName());
		return popRtSubRestTemplate.postRest(serviceId, "/conv/serachCsConversionTaskTotalNew", body);
	}
}
