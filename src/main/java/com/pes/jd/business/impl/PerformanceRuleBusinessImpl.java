package com.pes.jd.business.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.business.PerformanceRuleBusiness;
import com.pes.jd.dao.CsChatpeerDao;
import com.pes.jd.dao.CsOrderBindDao;
import com.pes.jd.model.BO.CsOrderBindJudgeBO;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DO.CsOrderBindDO;
import com.pes.jd.model.DO.CsOrderIndexDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.JudgeRuleAscriptionEnum;
import com.pes.jd.model.Enum.JudgeRuleTypeEnum;
import com.pes.jd.model.Enum.SystemCustomerServiceEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务规则计算
 *
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
@Service
public class PerformanceRuleBusinessImpl implements PerformanceRuleBusiness {
    private static final Logger logger = LoggerFactory.getLogger(CsPerformanceHandleBusinessImpl.class);
    public static final String DELIMITER = "&&&";
    @Resource
    private CsChatpeerDao csChatpeerDao;
    @Resource
    private CsOrderBindDao csOrderBindDao;
    /**
     * 自家账号过滤/绩效软件客服过滤/空聊天过滤/京东官方客服过滤
     *
     * @param sys
     * @param accountSet
     * @param cls
     * @param buyerNick
     * @return Integer
     */
    @Override
    public Integer getBuyerChatFlag(ShopSystemsettingDTO sys, Set<String> accountSet,
                                    List<CsChatlogDTO> cls, String buyerNick) {

        Set<String> sysCsSet = Sets.newHashSet();
        sysCsSet.add(SystemCustomerServiceEnum.CS1.getName());

        Set<String> platformSysCsSet = Sets.newHashSet();
        platformSysCsSet.add(SystemCustomerServiceEnum.CS1.getName());

        if (isEmptyChatByFilter(sys.getAutoReplySwitch(), cls)) {
            //@TODO(空聊天)
            return 1;
        } else if (sysCsSet.contains(buyerNick)) {
            //@TODO(绩效软件客服账号聊天)
            return 3;

        } else if (platformSysCsSet.contains(buyerNick)) {
            //@TODO(平台软件客服账号聊天)
            return 4;

        } else if (accountSet.contains(buyerNick)) {
            //@TODO(自家账号聊天)
            return 2;

        }
        return 0;
    }

    /**
     * 判断是否是空聊天
     * @param autoReplySwitch
     * @param cls
     * @return boolean
     */
    private boolean isEmptyChatByFilter(boolean autoReplySwitch, List<CsChatlogDTO> cls) {

        if(CollectionUtils.isEmpty(cls)){
            return true;
        }
        for (CsChatlogDTO cl : cls) {
            if(cl.getDirection() == 1){
                return false;
            }
            if(!isCsAutoReplyChat(autoReplySwitch, cl.getMt(), cl.getContent())){
                return false;
            }
        }
        return true;
    }

    /**
     * 客服普调聊天，自动回复过滤
     *
     * @param isFilteAutoReply
     * @param mt
     * @param content
     * @return boolean
     */
    @Override
    public boolean isCsAutoReplyChat(boolean isFilteAutoReply, Byte mt, String content) {
        if (content == null) {
            return false;
        }
//        return isFilteAutoReply && ( 2 == mt || 3 == mt ||
//                "【此消息为欢迎卡片或富文本模板答案，聊天记录中暂不支持展示】".equals(content));
        return isFilteAutoReply && (2 == mt || 3 == mt);
    }

    /**
     * 自动回复过滤 - 系统设置
     *
     * @param sys
     * @param mt
     * @param content
     * @return boolean
     */
    @Override
    public boolean isCsAutoReplyBySysettingChat(ShopSystemsettingDTO sys, Byte mt, String content) {
        if (content == null) {
            return false;
        }
        return isCsAutoReplyChat(sys.getAutoReplySwitch(), mt, content);
    }

//    /**
//     * 客服普调聊天，自动回复过滤
//     *
//     * @param isFilteAutoReply
//     * @param autoReplyMark
//     * @return boolean
//     */
//    @Override
//    public boolean isCsAutoReplyChat(boolean isFilteAutoReply, String autoReplyMark, String content) {
//        if (content == null) {
//            return false;
//        }
//        return isFilteAutoReply && (content.startsWith(autoReplyMark) ||
//                "【此消息为欢迎卡片或富文本模板答案，聊天记录中暂不支持展示】".equals(content));
//    }

//    /**
//     * 自动回复过滤 - 系统设置
//     *
//     * @return boolean
//     */
//    @Override
//    public boolean isCsAutoReplyBySysettingChat(ShopSystemsettingDTO sys, String content) {
//        if (content == null) {
//            return false;
//        }
//        return isCsAutoReplyChat(sys.getAutoReplySwitch(), sys.getAutoReplyMark(), content);
//    }
//
//    /**
//     * 主号自动回复过滤
//     *
//     * @return boolean
//     */
//    @Override
//    public boolean isMainAccountAutoReplyChat(ShopSystemsettingDTO sys, String content) {
//        if (content == null) {
//            return false;
//        }
//        return isCsAutoReplyChat(sys.getAutoReplySwitch(), sys.getAutoReplyMark(), content);
//    }


    /**
     * 客服有效接待 (根据最大等时间判定)
     *
     * @param csReplyDate
     * @param buyerChatDate
     * @param maxWaitTimeMillisecond
     * @return
     */
    @Override
    public boolean isValidCsResponse(long intervalTime, long maxWaitTimeMillisecond) {

        if (intervalTime <= maxWaitTimeMillisecond) {
            return true;
        }
        return false;
    }


    /**
     * 客服有效接待 (根据最大等时间判定)
     *
     * @param csReplyDate
     * @param buyerChatDate
     * @param maxWaitTimeMillisecond
     * @return
     */
    @Override
    public boolean isValidCsResponse(Date csReplyDate, Date buyerChatDate, long maxWaitTimeMillisecond) {

        if ((csReplyDate.getTime() - buyerChatDate.getTime()) <= maxWaitTimeMillisecond) {
            return true;
        }
        return false;
    }

//    /**
//     * 主号自动回复过滤
//     *
//     * @param isFilteAutoReply
//     * @param autoReplyMark
//     * @param buyerNick
//     * @return boolean
//     */
//    @Override
//    public boolean isMainAccountAutoReplyChat(ShopSystemsettingDTO sys, Byte mt, String content) {
//        if (content == null) {
//            return false;
//        }
//        return isCsAutoReplyChat(sys.getAutoReplySwitch(), mt, content);
//    }


    /*	*//**
     * 处理转发
     * @param shop
     * @param date
     * @param forwardChatPeerLst
     * @param csMap
     *//*
	@Override
	public void handleForwardChat(
			JobShopDTO shop, Date date, List<CommonCsChatpeerDTO> forwardChatPeerLst,
			Map<String,CsDTO> csMap){

		if(CollectionUtils.isEmpty(forwardChatPeerLst)){
			return ;
		}

		List<String> buyerLst = forwardChatPeerLst.stream()
				.map(CommonCsChatpeerDTO::getBuyerNick)
				.collect(Collectors.toList());

		Map<String, List<CommonCsChatpeerDTO>> buyerMap = forwardChatPeerLst.stream()
				.collect(Collectors.groupingBy(CommonCsChatpeerDTO::getBuyerNick));

		//@TODO(往前推 12个小时 查 转入转出)
		Date startDate = new Date(date.getTime() - 12*60*60*1000);
		Date endDate = DateUtil.getEndTimeOfDate(date);

		//查出不是这个客服的聊天记录cs
		List<ChatlogDTO> forwardChatLogLst = csChatlogDao.selectShopForwardChatLogLstByBuyerLstAndDate(shop, buyerLst, startDate, endDate);
		if (CollectionUtils.isEmpty(forwardChatLogLst)) {
			return ;
		}
		for (ChatlogDTO cl : forwardChatLogLst) {
			List<CommonCsChatpeerDTO> cpLst = buyerMap.get(cl.getCsNick());

			if (cpLst != null) {
				for (CommonCsChatpeerDTO cp : cpLst) {

					if(cp.getCsNick().equals(cl.getCsNick())){

						switch (ChatLogTypeEnum.TYPE_CS_TRANSFER_OUT_MSG.getType()){
							case 24:cp.setForwardFlag(CommonConstants.FORWARD_OUT);
							case 25:cp.setForwardFlag(CommonConstants.FORWARD_IN);
							default:cp.setForwardFlag(CommonConstants.FORWARD_NON);
						}
					}
				}
			}
		}
		return ;
	}*/

    /*	*//**
     * 转入/转出/限制
     * @param fwdChatLst
     * @param cpBOMap
     * @param sys
     *//*
	@Override
	public void handleForwardChats(List<ForwardChatLogBO> fwdChatLst,
			Map<String,List<ChatBO>> cpBOMap,
			ShopSystemsettingDTO sys){

		if(CollectionUtils.isEmpty(fwdChatLst)){
			return;
		}
		int csFwdChatNum = sys.getCsForwardNum();
		for (ForwardChatLogBO forward : fwdChatLst) {

			if(forward == null || forward.getForwarder() == null){
				System.out.println(" ***forwarder is null");
				continue;
			}
			List<ChatBO> cps = cpBOMap.get(forward.getForwarder());
			if(cps == null){
//				System.out.println(" ***handle forward error ! target cs:"+clog.getForwarder());
				continue;
			}
			int csWordBeforeFwd = 0;
			ChatBO cpBO = null;
			CommonCsChatpeerDTO cp = null;
			for (int i = 0,size = cps.size(); i < size; i++) {
				cpBO = cps.get(i);
				cp = cpBO.getCsChatpeer();
				if(cp.getBuyerNick().equals(forward.getBuyerNick())){
					//找到转出的那个cp
					cp.setForwardFlag(CommonConstants.FORWARD_OUT);
					if(forward.getTargetCsType() == CommonConstants.CUSTOMER_SERVICE_TYPE_POST){
						break;
					}
					List<CsChatlogDTO> chatlogs = cpBO.getCsChatLogLst();
					if(CollectionUtils.isEmpty(chatlogs))
						continue;
					for (CsChatlogDTO log : chatlogs) {
						if(log.getDirection() ==0 && !log.getChatTime().after(forward.getLastChatTime())){
							csWordBeforeFwd++;
							if(csWordBeforeFwd >= csFwdChatNum){
//								cp.setForwardFilteFlag(CommonConstants.CHAT_PEER_STATUS_1);
								break;
							}
						}
					}
				}
			}
		}
	}*/


    /**
     * @param silentUrgepaySwitch
     * @param silentUrgepayTime
     * @param csOrderIndex
     * @return boolean
     */
    @Override
    public Integer getSilentOrderUrgepayFlag(Integer silentUrgepayTime, CsOrderIndexDO csOrderIndex) {

        //下单后催
        if (csOrderIndex.getSilentFlag() == 1
                && csOrderIndex.getAcFirstReplyDate() != null) {
            if (csOrderIndex.getOrderPayDate() == null
                    || (!csOrderIndex.getOrderPayDate().before(csOrderIndex.getAcFirstReplyDate()))) {

                if (csOrderIndex.getAcFirstReplyDate().getTime() - csOrderIndex.getOrderCreated().getTime() <= silentUrgepayTime * 60 * 1000) {
                    return 11;//静默下单（催付限制时间内去催付，违规）
                } else {
                    return 12;//静默下单（催付限制时间外去催付，合理）
                }
            }
        }
        //没有催付，则返回原来的状态
        return csOrderIndex.getSilentFlag();
    }

    /**
     * 获取订单催付标识
     *
     * @param silentUrgepaySwitch
     * @param silentUrgepayTime
     * @param csOrderIndex
     * @return boolean
     */
    @Override
    public Integer getOrderUrgepayFlag(Integer silentFlag, CsOrderIndexDO csOrderIndex) {

        //下单后催
        if (silentFlag == 11 || silentFlag == 12) {//客服有催付
            if (csOrderIndex.getAcFirstReplyDate() != null && csOrderIndex.getAcFirstChatDate() != null) {
                if (csOrderIndex.getAcFirstReplyDate().before(csOrderIndex.getAcFirstChatDate())) {
                    return 1;//客服主动催付
                } else {
                    return 2;//非客服主动催付
                }
            }
            if (csOrderIndex.getAcFirstReplyDate() != null && csOrderIndex.getAcFirstChatDate() == null) {
                return 1;
            }
        }
        //没有催付
        return 0;
    }

    /**
     * 绑定有催付限制的订单
     *
     * @param silentUrgepaySwitch
     * @return
     */
    @Override
    public boolean isUrgePayLimitOrder(Boolean silentUrgepaySwitch, CsOrderBindJudgeBO csOrderBindJudge) {
        if (silentUrgepaySwitch) {
            return csOrderBindJudge.getUrgepayFlag() != null
                    && csOrderBindJudge.getSilentFlag() != null
                    && csOrderBindJudge.getUrgepayFlag() == 1 && csOrderBindJudge.getSilentFlag() == 11;
        }
        return false;
    }

    /**
     * 判定是否需要绑定全静默订单
     *
     * @param csOrderIndex
     * @param sys
     * @return boolean
     */
    @Override
    public boolean isBindSilentAllOrder(CsOrderIndexDTO csOrderIndex, ShopSystemsettingDTO sys) {

        if (csOrderIndex.getApFirstReplyDate() != null
                && csOrderIndex.getApFirstChatDate() != null) {

            if (sys.getSilentAllFollowUpTime() == null) {
                return false;
            }


            Integer silentAllFollowUpMS = sys.getSilentAllFollowUpTime() * 60 * 1000;
            //客服主动跟进和买家说话都在规定时间内（如10分钟）
            if (csOrderIndex.getOrderPayDate() != null) {
                if (csOrderIndex.getApFirstReplyDate().getTime() - csOrderIndex.getOrderPayDate().getTime() <= silentAllFollowUpMS
                        && csOrderIndex.getApFirstChatDate().getTime() - csOrderIndex.getOrderPayDate().getTime() <= silentAllFollowUpMS) {
                    return true;
                }
            } else {
                if (csOrderIndex.getApFirstReplyDate().getTime() - csOrderIndex.getOrderCreated().getTime() <= silentAllFollowUpMS
                        && csOrderIndex.getApFirstChatDate().getTime() - csOrderIndex.getOrderCreated().getTime() <= silentAllFollowUpMS) {
                    return true;
                }
            }

        }
        return false;
    }


    /**
     * 插旗订单，不算绩效
     *
     * @param orderFlagSwitch
     * @param orderFlag
     * @param csOrderIndex
     * @return
     */
    @Override
    public boolean isOrderBannerMark(Boolean orderFlagSwitch, Integer orderFlag, CsOrderIndexDTO csOrderIndex) {
        if (orderFlagSwitch
                && orderFlag.equals(csOrderIndex.getOrderFlag())) {
            return true;
        }
        return false;
    }

    /**
     * 协助下单
     *
     * @param csOrderIndex
     * @return
     */
    @Override
    public boolean isAssitOrderCreate(CsOrderIndexDTO csOrderIndex) {

        if (csOrderIndex.getAssitOrderCreate()) {
            return true;
        }
        return false;
    }

    /**
     * 插旗订单，不算绩效
     *
     * @param orderFlagSwitch
     * @param orderFlag
     * @param csOrderIndex
     * @return
     */
    @Override
    public boolean hasAssit(CsOrderIndexDTO csOrderIndex) {


        if (csOrderIndex.getAssitOrderCreate()
                || csOrderIndex.getAssitOrderInFollowup()
                || csOrderIndex.getAssitOrderPay()) {
            return true;
        }
        return false;
    }

    /**
     * 过滤客服【主动联系买家】的 询单，如果该客户买家已经被落实，则算询单，否则在买家首次回复识别天数内不算询单流失
     *
     * @param orderFlagSwitch
     * @param orderFlag
     * @param csOrderIndex
     * @return
     */
    @Override
    public void handleCsActiveConvFailData(JobShopDTO shop, String csNick, Date date,
                                           List<ReceivedChatpeerDTO> chatpeers) {

        for (ReceivedChatpeerDTO chatpeer : chatpeers) {

            if (chatpeer.getCsChatFirstFlag() == 1) {//主动联系

                if (chatpeer.getIsOrderCreated()) {//
                    chatpeer.setCsActiveChatFail(Boolean.FALSE);//主动联系成功
                } else {
                    chatpeer.setCsActiveChatFail(Boolean.TRUE);//主动联系失败
                }
//				cs_active_urgepay_fail
                if (chatpeer.getIsCsSingleChatFilter()) {
                }
            }


        }

        /*
         * 订单绑定处理
         */
        List<String> toOrderBindedbuyerNickLst = csOrderBindDao.selectShopCsToOrderBindBuyerNickLstForEnquiry(shop, csNick, date);
        //落实下单Set
        Set<String> csOrderedPesBuyerSet = Optional.ofNullable(toOrderBindedbuyerNickLst)
                .orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toSet());

        for (ReceivedChatpeerDTO cp : chatpeers) {

            if (cp.getIsCsConsultFirst()) {
                if (cp.getIsOrderCreated()) {
                    if (!csOrderedPesBuyerSet.contains(cp.getBuyerNick())) {
                        cp.setIsEnquiry(false);
                    }
                }
            }
        }
    }

    /**
     * 落实下单
     *
     * @param shop
     * @param sys
     * @param date
     * @param edate
     * @param csNick
     * @param orderId
     * @param orderCsIndexLst
     * @param toOrderedOrderIdSet
     * @param presaleOrderDTO
     * @return
     */
    @Override
    public CsOrderBindJudgeBO csOrderCreatedBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
                                                 String csNick,
                                                 Long orderId,
                                                 List<CsOrderIndexDTO> orderCsIndexLst,
                                                 Set<Long> toOrderedOrderIdSet, PresaleOrderDTO presaleOrderDTO) {
        final Integer judgeRuleAscription = sys.getJudgeRuleAscription();
        if (CollectionUtils.isEmpty(orderCsIndexLst)) return null;
        //TODO(落实下单 )
//			if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT.getType() == judgeRuleAscription) {
//				// 若按最后聊天时间判定归属,则按照下单前最晚接待时间排序
//				orderCsIndexLst.sort(Comparator.comparing(CsOrderIndexDTO::getBcLastReplyDate, Comparator.nullsLast(Comparator.naturalOrder())));
//			}

        CsOrderIndexDTO toOrderCsOrderIndex = null;
        for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {

//                if(!csOrderIndex.getEnquiryIndex()){
//                    continue;
//                }

            if (csOrderIndex.getAfterSale()) {
                continue;
            }
            if (toOrderCsOrderIndex == null) {
                if (csOrderIndex.getBcLastReplyDate() != null) {//下单前  客服 最后待时间
                    toOrderCsOrderIndex = csOrderIndex;
                }
            } else {
                if (csOrderIndex.getBcLastReplyDate() != null) {
                    boolean change = false;
                    if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT
                            .getType() == judgeRuleAscription) {// 按最后聊天时间判定归属

                        //>0 当前指标时间最后聊天晚
                        int flag = isTargetDateCompareToSourceDate(csOrderIndex.getBcLastReplyDate(), toOrderCsOrderIndex.getBcLastReplyDate());//前面大后面小返回-1
                        if (flag == 0) {
                            if (DateUtil.isSameDate(csOrderIndex.getBcLastReplyDate(), date)) {//指标最后聊天时间是今天
                                if (csOrderIndex.getDate().equals(date)) {
                                    change = true;
                                }
                            } else if (csOrderIndex.getDate().after(toOrderCsOrderIndex.getDate())) {
                                change = true;
                            }
                        } else if (flag > 0) {
                            change = true;
                        }
                        //询单有效期内一个客服如果有多个指则将绩效判定给最后一天的指标
                        change = this.checkSameCsIndexNeedChange(toOrderCsOrderIndex, csOrderIndex, change);
                    } else if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_ROUND
                            .getType() == judgeRuleAscription) { // 按回合数判定归属

                        int flag1 = isTargetNumCompareToSourceNum(csOrderIndex.getBcChatRoundNum(), toOrderCsOrderIndex.getBcChatRoundNum());
                        if (flag1 == 0) {

                            int flag2 = isTargetDateCompareToSourceDate(csOrderIndex.getBcLastReplyDate(), toOrderCsOrderIndex.getBcLastReplyDate());
                            if (flag2 == 0) {
                                if (DateUtil.isSameDate(csOrderIndex.getBcLastReplyDate(), date)) {
                                    if (csOrderIndex.getDate().equals(date)) {
                                        change = true;
                                    }
                                } else if (csOrderIndex.getDate().after(toOrderCsOrderIndex.getDate())) {
                                    change = true;
                                }

                            } else if (flag2 > 0) {
                                change = true;
                            }
                        } else if (flag1 > 0) {
                            change = true;
                        }
                    } else {
                        logger.error("judgeRule ascription error,ascriptionJudgeRule:{}", judgeRuleAscription);
                        return null;
                    }
                    if (change) {
                        toOrderCsOrderIndex = csOrderIndex;
                    }
                }
            }
        }

        //绑定落实下单客服
        if (toOrderCsOrderIndex != null) {

            toOrderCsOrderIndex.setTeamCsBindOrderCreated(Boolean.TRUE);

            for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
                if (csOrderIndex.getAfterSale()) {
                    continue;
                }

                if (csOrderIndex.getBcLastReplyDate() != null) {

                    if (csOrderIndex == toOrderCsOrderIndex) {

                        if (csNick.equals(toOrderCsOrderIndex.getCsNick())) { //客服对比
                            if (date.equals(toOrderCsOrderIndex.getDate())) { //日期对比
                                if (toOrderCsOrderIndex.getBcLastReplyDate().compareTo(date) >= 0) {//下单前  客服 最后待时间'

                                    if (toOrderCsOrderIndex.getBcLastReplyDate().compareTo(edate) <= 0) {

                                        //@TODO(落实下单)
                                        toOrderCsOrderIndex.setBindOrderCreated(Boolean.TRUE);
                                    } else {

                                        //补漏 指标绑定落实下单时不算接待
                                        if (toOrderCsOrderIndex.getBindOrderCreatedReceive() != null && !toOrderCsOrderIndex.getBindOrderCreatedReceive()) {
                                            //@TODO(落实下单)
                                            toOrderCsOrderIndex.setBindOrderCreated(Boolean.TRUE);
                                        } else {
                                            //@TODO(协助下单处理)
                                            toOrderCsOrderIndex.setAssitOrderCreate(Boolean.TRUE);
                                        }
                                    }

                                } else {

                                    boolean havePreDayIndexs = false;
                                    for (CsOrderIndexDTO tempIndex : orderCsIndexLst) {

                                        if (tempIndex.getDate().before(date)) {
                                            havePreDayIndexs = true;
                                            break;
                                        }
                                    }
                                    if (!havePreDayIndexs) {
                                        toOrderCsOrderIndex.setTeamCsBindOrderCreated(Boolean.FALSE);
                                    }
                                }

                            }

                        }
                    } else {

                        if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT.getType() == judgeRuleAscription) {//最后聊天时间
                            if (csOrderIndex.getDate().compareTo(toOrderCsOrderIndex.getDate()) <= 0) {
                                if (csOrderIndex.getBcLastReplyDate() != null) {
                                    csOrderIndex.setAssitOrderCreate(Boolean.TRUE);
                                }
                            }
                        } else if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_ROUND.getType() == judgeRuleAscription) {//回合数

                            if (csOrderIndex.getBcLastReplyDate() != null) {
                                csOrderIndex.setAssitOrderCreate(Boolean.TRUE);
                            }
                        }
                    }
                }
            }

            if (toOrderCsOrderIndex.getTeamCsBindOrderCreated() && toOrderCsOrderIndex.getBindOrderCreated()) {

                if (!toOrderedOrderIdSet.add(orderId)) {
                    logger.error("order multi bind,orderId:{}", orderId);
                }
            }

            if (toOrderCsOrderIndex.getBindOrderCreated()) {

                CsOrderBindDO bind = new CsOrderBindDO(shop.getShopId(), date, orderId);
                CsOrderBindJudgeBO bindBO = new CsOrderBindJudgeBO(bind);
                bindBO.setUrgepayFlag(toOrderCsOrderIndex.getUrgepayFlag());
                bindBO.setSilentFlag(toOrderCsOrderIndex.getSilentFlag());

                bind.setBalancePay(toOrderCsOrderIndex.getBalancePay());
                bind.setBuyerNick(toOrderCsOrderIndex.getBuyerNick());
                bind.setCsNick(toOrderCsOrderIndex.getCsNick());

                bind.setOrderCreated(toOrderCsOrderIndex.getOrderCreated());
                bind.setOrderPayment(toOrderCsOrderIndex.getOrderPayment());
                bind.setOrderPayDate(toOrderCsOrderIndex.getOrderPayDate());
                if (toOrderCsOrderIndex.getPreSale() != null && toOrderCsOrderIndex.getPreSale()) {
                    //什么也不做，留着后面更新
                } else {
                    bind.setOrderValidPayTime(toOrderCsOrderIndex.getOrderPayDate());
                    bind.setOrderValidPayment(toOrderCsOrderIndex.getOrderPayment());
                }
                bind.setOrderPostFee(toOrderCsOrderIndex.getOrderPostFee());
                bind.setOrderGoodsNum(toOrderCsOrderIndex.getOrderGoodsNum());
                bind.setSilentFlag(toOrderCsOrderIndex.getSilentFlag());
                bind.setOrderFilteFlag(0);
                if (toOrderCsOrderIndex.getOrderBannerFilte()) {
                    bind.setOrderFilteFlag(1);//插旗
                }

                bind.setPresale(toOrderCsOrderIndex.getPreSale());
                bind.setIsGoodsFilter(toOrderCsOrderIndex.getIsGoodsFilte());//商品过滤
                bind.setIsPesOrder(Boolean.FALSE);
                bind.setPayType(toOrderCsOrderIndex.getPayType());

                bind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_ORDER.getType());
                bindBO.setAllPay(Objects.nonNull(presaleOrderDTO) && BaseUtils.getNonNull(presaleOrderDTO.getOrderPayType()) == 1);
                return bindBO;
            }
        }

        return null;
    }

    /**
     * 落实付款
     *
     * @param shop
     * @param sys
     * @param date
     * @param edate
     * @param csNick
     * @param orderId
     * @param orderCsIndexLst
     * @param toPayOrderIdSet
     * @param presaleOrderDTO
     * @return
     */
    @Override
    public CsOrderBindJudgeBO csOrderPayBind(JobShopDTO shop,
                                             ShopSystemsettingDTO sys,
                                             Date date,
                                             Date edate,
                                             String csNick,
                                             Long orderId,
                                             List<CsOrderIndexDTO> orderCsIndexLst,
                                             Set<Long> toPayOrderIdSet,
                                             PresaleOrderDTO presaleOrderDTO) {

        final Integer judgeRuleAscription = sys.getJudgeRuleAscription();
//        静默下单跟进限制开关
        Boolean silentUrgepaySwitch = sys.getSilentUrgepaySwitch();
        //存在上一条指标才需要打协助跟进
        boolean havePreviousIndex = false;
        // TODO(协助跟进)
        if (orderCsIndexLst != null) {
            for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
                if (null != presaleOrderDTO) {  //预售订单单独处理协助跟进
                    if (havePreviousIndex) {
                        if (csOrderIndex.getAcFirstReplyDate() != null) {
                            if (csOrderIndex.getOrderPayDate() == null) {//未付款
                                //@TODO(协助跟进)
                                csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);
                            } else if (csOrderIndex.getAcFirstReplyDate().before(csOrderIndex.getOrderPayDate())) {

                                //@TODO(协助跟进)
                                csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);
                            }
                        }
                    } else {
                        if (null != presaleOrderDTO.getBargainTime() || csOrderIndex.getAcFirstReplyDate() != null && csOrderIndex.getOrderPayDate() == null) {//超出询单期间，付了定金的打协助
                            //@TODO(协助跟进)
                            csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);
                        } else {
                            //@TODO(协助跟进)
                            csOrderIndex.setAssitOrderInFollowup(Boolean.FALSE);
                        }
                    }
                    havePreviousIndex = true;
                } else {//普通订单的协助跟进
                    if (csOrderIndex.getAcFirstReplyDate() != null
                            && csOrderIndex.getAcFirstReplyDate().getTime() >= date.getTime()
                            && csOrderIndex.getAcFirstReplyDate().getTime() <= edate.getTime()) {
                        if (csOrderIndex.getOrderPayDate() == null) {//未付款
                            //@TODO(协助跟进)
                            csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);
                        } else if (csOrderIndex.getSilentFlag() != 11 && csOrderIndex.getAcFirstReplyDate().before(csOrderIndex.getOrderPayDate())) {
                            //@TODO(协助跟进)
                            csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);

                        } else if (csOrderIndex.getSilentFlag() == 11 && silentUrgepaySwitch) {
//                            下单后首次聊天时间
                            Optional<Date> acFirstReplyDate = Optional.ofNullable(csOrderIndex.getAcFirstReplyDate());
//                            下单后首次顾客找客服聊天时间
                            Optional<Date> acFirstChatDate = Optional.ofNullable(csOrderIndex.getAcFirstChatDate());
                            //只算客服主动说话才算跟进 1.下单后客服说话顾客没说话 2.下单后顾客说话客服没说话打协助跟进
                            if ((!acFirstChatDate.isPresent() && acFirstReplyDate.isPresent())
                                    || (acFirstChatDate.isPresent() && !acFirstReplyDate.isPresent())) {
                                //@TODO(协助跟进)
                                csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);
                            } else if (acFirstReplyDate.isPresent() && acFirstChatDate.isPresent()) {
                                if (acFirstReplyDate.get().before(acFirstChatDate.get())) {
                                    //@TODO(协助跟进)
                                    csOrderIndex.setAssitOrderInFollowup(Boolean.TRUE);
                                }
                            }
                        }
                    }
                }
            }
        }

        //@TODO(落实付款)
        if (CollectionUtils.isNotEmpty(orderCsIndexLst)) {
//            // 若按最后聊天时间判定归属,则按照付款前最后回复时间 正序
//            if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT.getType() == judgeRuleAscription) {
//                orderCsIndexLst.sort(Comparator.comparing(CsOrderIndexDTO::getBpLastReplyDate, Comparator.nullsLast(Comparator.naturalOrder())));
//            }

            CsOrderIndexDTO toPayCsOrderIndex = null;
            for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
                if (csOrderIndex.getAfterSale() || (csOrderIndex.getSilentFlag() == 11 && silentUrgepaySwitch && BaseUtils.getNonNull(csOrderIndex.getAssitOrderInFollowup()))) {
                    continue;
                }

                if (csOrderIndex.getOrderPayDate() != null
                        || csOrderIndex.getPayType() == 1) { //付款或者货到付款
                    //落实付款
                    if (toPayCsOrderIndex == null) {
                        if (csOrderIndex.getBpLastReplyDate() != null) {
                            toPayCsOrderIndex = csOrderIndex;
                        }
                    } else {
                        if (csOrderIndex.getBpLastReplyDate() != null) {
                            boolean change = false;
                            if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT
                                    .getType() == judgeRuleAscription) { // 按最后聊天时间判定归属

                                int flag = isTargetDateCompareToSourceDate(csOrderIndex.getBpLastReplyDate(), toPayCsOrderIndex.getBpLastReplyDate());
                                if (flag == 0) {
                                    if (DateUtil.isSameDate(csOrderIndex.getBpLastReplyDate(), date)) {
                                        if (csOrderIndex.getDate().equals(date)) {
                                            change = true;
                                        }
                                    } else if (csOrderIndex.getDate().after(toPayCsOrderIndex.getDate())) {
                                        change = true;
                                    }
                                } else if (flag > 0) {
                                    change = true;
                                }
                                //询单有效期内一个客服如果有多个指则将绩效判定给最后一天的指标
                                change = this.checkSameCsIndexNeedChange(toPayCsOrderIndex, csOrderIndex, change);
                            } else if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_ROUND
                                    .getType() == judgeRuleAscription) { // 按回合数判定归属

                                int flag1 = isTargetNumCompareToSourceNum(csOrderIndex.getBpChatRoundNum(), toPayCsOrderIndex.getBpChatRoundNum());
                                if (flag1 == 0) {

                                    int flag2 = isTargetDateCompareToSourceDate(csOrderIndex.getBpLastReplyDate(), toPayCsOrderIndex.getBpLastReplyDate());
                                    if (flag2 == 0) {
                                        if (DateUtil.isSameDate(csOrderIndex.getBpLastReplyDate(), date)) {
                                            change = true;
                                        } else if (csOrderIndex.getDate().after(toPayCsOrderIndex.getDate())) {
                                            change = true;
                                        }

                                    } else if (flag2 > 0) {
                                        change = true;
                                    }
                                } else if (flag1 > 0) {
                                    change = true;
                                }
                                //询单有效期内一个客服如果有多个指标则将绩效判定给最后一天的指标
                                change = this.checkSameCsIndexNeedChange(toPayCsOrderIndex, csOrderIndex, change);
                            } else {
                                logger.error("judgeRule ascription error,ascriptionJudgeRule:{}", judgeRuleAscription);
                                return null;
                            }

                            if (change) {
                                toPayCsOrderIndex = csOrderIndex;
                            }
                        }
                    }
                }
            }

            //绑定落实付款客服
            if (toPayCsOrderIndex != null) {
                for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {//11 12 10
                    if (csOrderIndex.getAfterSale() || (csOrderIndex.getSilentFlag() == 11 && silentUrgepaySwitch && BaseUtils.getNonNull(csOrderIndex.getAssitOrderInFollowup()))) {
                        continue;
                    }
                    if (csOrderIndex.getBpLastReplyDate() != null) {

                        if (csOrderIndex == toPayCsOrderIndex) {

                            if (csNick.equals(toPayCsOrderIndex.getCsNick())
                                    && date.equals(toPayCsOrderIndex.getDate())) {
                                if (toPayCsOrderIndex.getBpLastReplyDate() != null
                                        && toPayCsOrderIndex.getBpLastReplyDate().compareTo(edate) < 0) {
                                    //@TODO(落实付款)
                                    toPayCsOrderIndex.setBindOrderPaid(Boolean.TRUE);
                                } else {
                                    if (toPayCsOrderIndex.getBpLastReplyDate().compareTo(date) >= 0) {
                                        //补漏 指标绑定落实下单时不算接待
                                        if (toPayCsOrderIndex.getBindOrderPaidReceive() != null && !toPayCsOrderIndex.getBindOrderPaidReceive()) {
                                            //@TODO(落实下单)
                                            toPayCsOrderIndex.setBindOrderPaid(Boolean.TRUE);
                                        } else {
                                            //@TODO(协助下单处理)
                                            toPayCsOrderIndex.setAssitOrderPay(Boolean.TRUE);
                                        }
                                    }

                                }
                            }
                        } else {
                            if (csOrderIndex.getDate().compareTo(toPayCsOrderIndex.getDate()) <= 0) {
                                //按最后聊天时间判定是否协助
                                if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT.getType() == judgeRuleAscription
                                        && csOrderIndex.getBpLastReplyDate().compareTo(toPayCsOrderIndex.getBpLastReplyDate()) <= 0) {
                                    csOrderIndex.setAssitOrderPay(Boolean.TRUE);
                                }

                                //按回合数判定是否协助
                                else if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_ROUND.getType() == judgeRuleAscription
                                        && csOrderIndex.getBpLastReplyDate() != null) {
                                    csOrderIndex.setAssitOrderPay(Boolean.TRUE);
                                }
                            }
                        }
                    }
                }

                if (toPayOrderIdSet.add(orderId)) {

                    if (toPayCsOrderIndex.getBindOrderPaid()) {

                        CsOrderBindDO bind = new CsOrderBindDO(shop.getShopId(), date, orderId);
                        CsOrderBindJudgeBO bindBO = new CsOrderBindJudgeBO(bind);
                        bindBO.setUrgepayFlag(toPayCsOrderIndex.getUrgepayFlag());
                        bindBO.setSilentFlag(toPayCsOrderIndex.getSilentFlag());

                        bind.setBalancePay(toPayCsOrderIndex.getBalancePay());
                        bind.setBuyerNick(toPayCsOrderIndex.getBuyerNick());
                        bind.setCsNick(toPayCsOrderIndex.getCsNick());

                        bind.setOrderCreated(toPayCsOrderIndex.getOrderCreated());
                        bind.setOrderPayment(toPayCsOrderIndex.getOrderPayment());
                        bind.setOrderPayDate(toPayCsOrderIndex.getOrderPayDate());
                        if (toPayCsOrderIndex.getPreSale() != null && toPayCsOrderIndex.getPreSale()) {
                            //什么也不做，留着后面更新
                        } else {
                            bind.setOrderValidPayTime(toPayCsOrderIndex.getOrderPayDate());
                            bind.setOrderValidPayment(toPayCsOrderIndex.getOrderPayment());
                        }
                        bind.setOrderPostFee(toPayCsOrderIndex.getOrderPostFee());
                        bind.setOrderGoodsNum(toPayCsOrderIndex.getOrderGoodsNum());
                        bind.setSilentFlag(toPayCsOrderIndex.getSilentFlag());
                        bind.setOrderFilteFlag(0);
                        if (toPayCsOrderIndex.getOrderBannerFilte()) {
                            bind.setOrderFilteFlag(1);//插旗
                        }
                        bind.setPresale(toPayCsOrderIndex.getPreSale());
                        bind.setIsGoodsFilter(toPayCsOrderIndex.getIsGoodsFilte());//商品过滤
                        bind.setIsPesOrder(Boolean.FALSE);
                        bind.setPayType(toPayCsOrderIndex.getPayType());
                        bind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_PAY.getType());
                        bindBO.setAllPay(Objects.nonNull(presaleOrderDTO) && BaseUtils.getNonNull(presaleOrderDTO.getOrderPayType()) == 1);
                        return bindBO;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 询单有效期内一个客服如果有多个指标则将绩效判定给最后一天的指标
     *
     * @param toPayCsOrderIndex
     * @param csOrderIndex
     * @param change
     * @return
     */
    private boolean checkSameCsIndexNeedChange(CsOrderIndexDTO toPayCsOrderIndex, CsOrderIndexDTO csOrderIndex, boolean change) {
        if (csOrderIndex.getCsNick().equals(toPayCsOrderIndex.getCsNick()) && csOrderIndex.getDate().before(toPayCsOrderIndex.getDate())) {
            change = false;
        }
        return change;
    }

    /**
     * 落实付尾款
     *
     * @param shop
     * @param sys
     * @param date
     * @param edate
     * @param csNick
     * @param orderId
     * @param orderCsIndexLst
     * @param toPayOrderIdSet
     * @param presaleOrder
     * @param chatpeers
     * @param sDate //询单的开始时间
     * @return
     */
    @Override
    public CsOrderBindJudgeBO csPresaleOrderBalancePayBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
                                                           String csNick,
                                                           Long orderId,
                                                           List<CsOrderIndexDTO> orderCsIndexLst,
                                                           Set<Long> toPayOrderIdSet, List<PresaleOrderDTO> presaleOrder,
                                                           List<CsOrderBindChatpeerDTO> chatpeers, Date sDate) {
        if(CollUtil.isEmpty(orderCsIndexLst)) {
            return null;
        }
        final Integer judgeRuleAscription = sys.getJudgeRuleAscription();
        // TODO(落实付尾款)
        CsOrderIndexDTO toBalancePayCsOrderIndex = null;
        //落实付尾款绑定给付尾款的前一天
        for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
            if (csOrderIndex.getOrderPayDate() != null) {
                //落实付款
                if (toBalancePayCsOrderIndex == null && csOrderIndex.getBpLastReplyDate() != null) {
                    toBalancePayCsOrderIndex = csOrderIndex;
                } else {
                    //付款前的最后回复时间
                    if (csOrderIndex.getBpLastReplyDate() != null) {
                        boolean change = false;
                        // 按最后聊天时间判定归属
                        if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_CHAT
                                .getType() == judgeRuleAscription) {

                            int flag = isTargetDateCompareToSourceDate(csOrderIndex.getBpLastReplyDate(), toBalancePayCsOrderIndex.getBpLastReplyDate());
                            if (flag == 0) {
                                if (DateUtil.isSameDate(csOrderIndex.getDate(), date)) {
                                    change = true;
                                }
                            } else if (flag > 0) {
                                change = true;
                            }
                            // 按回合数判定归属
                        } else if (JudgeRuleAscriptionEnum.JUDGE_RULE_ASCRIPTION_TYPE_ROUND.getType() == judgeRuleAscription) {

                            int flag = isTargetNumCompareToSourceNum(csOrderIndex.getBpChatRoundNum(), toBalancePayCsOrderIndex.getBpChatRoundNum());
                            if (flag == 0) {
                                if (DateUtil.isSameDate(csOrderIndex.getDate(), date)) {
                                    change = true;
                                }
                            } else if (flag > 0) {
                                change = true;
                            }

                        } else {
                            logger.error("judgeRule ascription error,ascriptionJudgeRule:{}", judgeRuleAscription);
                            return null;
                        }
                        if (change) {
                            toBalancePayCsOrderIndex.setAssitOrderPay(true);
                            toBalancePayCsOrderIndex = csOrderIndex;
                        } else {
                            if (csOrderIndex.getFirstReplyDate() != null && csOrderIndex.getFirstReplyDate().before(csOrderIndex.getOrderPayDate())) {
                                // @TODO协助
                                csOrderIndex.setAssitOrderPay(true);
                            }
                        }
                    }
                }
            }
        }
        if (toBalancePayCsOrderIndex == null) {
            return null;
        }
        //绑定落实付款客服
        if (csNick.equals(toBalancePayCsOrderIndex.getCsNick())
                && date.equals(toBalancePayCsOrderIndex.getDate())
                && toBalancePayCsOrderIndex.getBpLastReplyDate() != null
                && toBalancePayCsOrderIndex.getBpLastReplyDate().compareTo(edate) < 0) {
            CsOrderBindDO bind = new CsOrderBindDO(shop.getShopId(), date, orderId);
            CsOrderBindJudgeBO bindBO = new CsOrderBindJudgeBO(bind);
            bindBO.setUrgepayFlag(toBalancePayCsOrderIndex.getUrgepayFlag());
            bindBO.setSilentFlag(toBalancePayCsOrderIndex.getSilentFlag());
            bind.setBalancePay(toBalancePayCsOrderIndex.getBalancePay());
            bind.setBuyerNick(toBalancePayCsOrderIndex.getBuyerNick());
            bind.setCsNick(toBalancePayCsOrderIndex.getCsNick());
            bind.setOrderCreated(toBalancePayCsOrderIndex.getOrderCreated());
            bind.setOrderPayment(toBalancePayCsOrderIndex.getOrderPayment());
            bind.setOrderPayDate(toBalancePayCsOrderIndex.getOrderPayDate());
            bind.setOrderValidPayTime(toBalancePayCsOrderIndex.getOrderPayDate());
            bind.setOrderValidPayment(toBalancePayCsOrderIndex.getOrderPayment());
            bind.setOrderPostFee(toBalancePayCsOrderIndex.getOrderPostFee());
            bind.setOrderGoodsNum(toBalancePayCsOrderIndex.getOrderGoodsNum());
            bind.setSilentFlag(toBalancePayCsOrderIndex.getSilentFlag());
            bind.setOrderFilteFlag(0);
            if (toBalancePayCsOrderIndex.getOrderBannerFilte()) {
                bind.setOrderFilteFlag(1);//插旗
            }
            bind.setPresale(toBalancePayCsOrderIndex.getPreSale());
            bind.setIsGoodsFilter(toBalancePayCsOrderIndex.getIsGoodsFilte());//商品过滤
            bind.setIsPesOrder(Boolean.FALSE);
            bind.setPayType(toBalancePayCsOrderIndex.getPayType());
            bind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_BALANCE_PAY.getType());
            //如果当天没有聊天对象绑定给有聊天对象那天
            List<CsOrderBindChatpeerDTO> collect = chatpeers.stream().filter(ele -> ele.getCsNick().equals(bind.getCsNick()) && ele.getBuyerNick().equals(bind.getBuyerNick()) && ele.getDate().getTime() == bind.getDate().getTime()).collect(Collectors.toList());
            //如果当天没有聊天关系就绑定给最近的一天
            if (CollUtil.isEmpty(collect)) {
                List<CsOrderBindChatpeerDTO> dto = chatpeers.stream().filter(ele -> ele.getCsNick().equals(bind.getCsNick()) && ele.getBuyerNick().equals(bind.getBuyerNick())).sorted(Comparator.comparing(CsOrderBindChatpeerDTO::getDate)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(dto)) {
                    for (int i = dto.size() - 1; i >= 0; i--) {
                        CsOrderBindChatpeerDTO csOrderBindChatpeerDTO = dto.get(i);
                        if (bind.getDate().getTime() >= csOrderBindChatpeerDTO.getDate().getTime()) {
                            bind.setDate(csOrderBindChatpeerDTO.getDate());
                            break;
                        }
                    }
                }
            }
            bindBO.setBindPayType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_BALANCE_PAY.getType());
            return bindBO;
        }
        return null;
    }
    /**
     * 全静默落实
     *
     * @param shop
     * @param sys
     * @param date
     * @param edate
     * @param csNick
     * @param orderId
     * @param orderCsIndexLst
     * @param receiveFilterChatPeerLst 询单有效期内接待过滤的聊天对象
     * @param presaleOrderDTO
     * @return
     */
    @Override
    public CsOrderBindJudgeBO csSilentAllBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
                                              String csNick,
                                              Long orderId,
                                              List<CsOrderIndexDTO> orderCsIndexLst,
                                              List<CommonCsChatpeerDTO> receiveFilterChatPeerLst, PresaleOrderDTO presaleOrderDTO) {
        Map<String, List<CommonCsChatpeerDTO>> filterBuyerMap = receiveFilterChatPeerLst.stream().collect(Collectors.groupingBy(CommonCsChatpeerDTO::getBuyerNick));
        //TODO(全静默)
        if (orderCsIndexLst != null) {
            CsOrderIndexDTO toSilentAllCsOrderIndex = null;
            for (CsOrderIndexDTO csOrderIndex : orderCsIndexLst) {
                if (csOrderIndex.getOrderPayDate() != null) {
                    if (csOrderIndex.getFirstReplyDate() != null && csOrderIndex.getFirstChatDate() != null
                            && csOrderIndex.getFirstReplyDate().before(csOrderIndex.getOrderPayDate())
                            && csOrderIndex.getFirstChatDate().before(csOrderIndex.getOrderPayDate())) {
                        toSilentAllCsOrderIndex = null;
                        break;
                    }
                    if (csOrderIndex.getFirstReplyDate() != null
                            && csOrderIndex.getFirstChatDate() != null
//                            fix:1955  全静默订单的判定剔除询单有效期内被接待过滤的聊天 该聊天被留言过滤会算全静默
                            && (csOrderIndex.getFirstReplyDate().after(csOrderIndex.getOrderPayDate())
                            ||
                            (isReceiveFilterIndex(csOrderIndex, filterBuyerMap)))
                            && csOrderIndex.getFirstChatDate().after(csOrderIndex.getOrderPayDate())) {
                        if (csOrderIndex.getFirstReplyDate().before(csOrderIndex.getFirstChatDate())) {//客服主动跟进
                            csOrderIndex.setCsActiveFollowIn(Boolean.TRUE);
                        }

                        if (this.isBindSilentAllOrder(csOrderIndex, sys)) {

                            if (toSilentAllCsOrderIndex == null) {
                                toSilentAllCsOrderIndex = csOrderIndex;
                            } else {
                                if (csOrderIndex.getApFirstReplyDate().before(toSilentAllCsOrderIndex.getApFirstReplyDate())) {
                                    toSilentAllCsOrderIndex = csOrderIndex;
                                }
                            }
                        }
                    }
                } else if (csOrderIndex.getPayType() == 1) {//货到付款
                    if (csOrderIndex.getFirstReplyDate() != null && csOrderIndex.getFirstChatDate() != null
                            && csOrderIndex.getFirstReplyDate().before(csOrderIndex.getOrderCreated())
                            && csOrderIndex.getFirstChatDate().before(csOrderIndex.getOrderCreated())) {
                        toSilentAllCsOrderIndex = null;
                        break;
                    }
                    if (csOrderIndex.getFirstReplyDate() != null
                            && csOrderIndex.getFirstChatDate() != null
                            && csOrderIndex.getFirstReplyDate().after(csOrderIndex.getOrderCreated())
                            && csOrderIndex.getFirstChatDate().after(csOrderIndex.getOrderCreated())) {

                        if (csOrderIndex.getFirstReplyDate().before(csOrderIndex.getFirstChatDate())) {//客服主动跟进
                            csOrderIndex.setCsActiveFollowIn(Boolean.TRUE);
                        }

                        if (this.isBindSilentAllOrder(csOrderIndex, sys)) {

                            if (toSilentAllCsOrderIndex == null) {
                                toSilentAllCsOrderIndex = csOrderIndex;
                            } else {
                                if (csOrderIndex.getApFirstReplyDate().before(toSilentAllCsOrderIndex.getApFirstReplyDate())) {
                                    toSilentAllCsOrderIndex = csOrderIndex;
                                }
                            }
                        }
                    }
                }

            }

            //绑定全静默订单
            if (toSilentAllCsOrderIndex != null) {

                if (csNick.equals(toSilentAllCsOrderIndex.getCsNick())
                        && date.equals(toSilentAllCsOrderIndex.getDate())) {

                    if (toSilentAllCsOrderIndex.getCsActiveFollowIn() && !sys.getIsBindSilentAllOrder()) {
                        return null;
                    }
                    CsOrderBindDO bind = new CsOrderBindDO(shop.getShopId(), date, orderId);
                    CsOrderBindJudgeBO bindBO = new CsOrderBindJudgeBO(bind);
                    bindBO.setUrgepayFlag(toSilentAllCsOrderIndex.getUrgepayFlag());
                    bindBO.setSilentFlag(toSilentAllCsOrderIndex.getSilentFlag());
                    //系统设置的不算客服销售--只统计顾客在有效时间内主动找客服聊天（且有效时间内有客服有回复），算绩效
                    if (toSilentAllCsOrderIndex.getApFirstChatDate().before(toSilentAllCsOrderIndex.getApFirstReplyDate())) {
                        bindBO.setCsActiveFollowIn(Boolean.TRUE);
                    }
                    bindBO.setPesOrder(Boolean.TRUE);
                    bind.setBalancePay(toSilentAllCsOrderIndex.getBalancePay());
                    bind.setBuyerNick(toSilentAllCsOrderIndex.getBuyerNick());
                    bind.setCsNick(toSilentAllCsOrderIndex.getCsNick());

                    bind.setOrderCreated(toSilentAllCsOrderIndex.getOrderCreated());
                    bind.setOrderPayment(toSilentAllCsOrderIndex.getOrderPayment());
                    bind.setOrderPayDate(toSilentAllCsOrderIndex.getOrderPayDate());
                    if (toSilentAllCsOrderIndex.getPreSale() != null && toSilentAllCsOrderIndex.getPreSale()) {
                        //什么也不做，留着后面更新
                    } else {
                        bind.setOrderValidPayTime(toSilentAllCsOrderIndex.getOrderPayDate());
                        bind.setOrderValidPayment(toSilentAllCsOrderIndex.getOrderPayment());
                    }
                    bind.setOrderPostFee(toSilentAllCsOrderIndex.getOrderPostFee());
                    bind.setOrderGoodsNum(toSilentAllCsOrderIndex.getOrderGoodsNum());
                    bind.setOrderFilteFlag(0);
                    if (toSilentAllCsOrderIndex.getOrderBannerFilte()) {
                        bind.setOrderFilteFlag(1);//插旗
                    }

                    bind.setPresale(toSilentAllCsOrderIndex.getPreSale());
                    bind.setIsGoodsFilter(toSilentAllCsOrderIndex.getIsGoodsFilte());//商品过滤
                    bind.setIsPesOrder(Boolean.FALSE);
                    bind.setPayType(toSilentAllCsOrderIndex.getPayType());
                    bind.setType(JudgeRuleTypeEnum.ORDER_BIND_TYPE_SILENTALL.getType());
                    bindBO.setAllPay(Objects.nonNull(presaleOrderDTO) && BaseUtils.getNonNull(presaleOrderDTO.getOrderPayType()) == 1);
                    return bindBO;
                }
            }
        }
        return null;
    }

    /**
     * 是接待过滤的指标
     * @param csOrderIndex
     * @param filterBuyerMap
     * @return
     */
    private boolean isReceiveFilterIndex(CsOrderIndexDTO csOrderIndex, Map<String, List<CommonCsChatpeerDTO>> filterBuyerMap) {
        List<CommonCsChatpeerDTO> buyerFilterPeer = filterBuyerMap.get(csOrderIndex.getBuyerNick());
        if (CollUtil.isNotEmpty(buyerFilterPeer)) {
            Date firstReplyDate = csOrderIndex.getFirstReplyDate();
            for (CommonCsChatpeerDTO commonCsChatpeerDTO : buyerFilterPeer) {
                if (commonCsChatpeerDTO.getFirstChatDate() != null && commonCsChatpeerDTO.getLastChatDate() != null) {
                    if (!firstReplyDate.before(commonCsChatpeerDTO.getFirstChatDate()) && !firstReplyDate.after(commonCsChatpeerDTO.getLastChatDate())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public PerformanceRuleValidDateBO getPerformanceRuleValidDate(JobShopQuery jobShop, JobDateQuery jobDate) {


        ShopSystemsettingDTO sys = jobShop.getShopSystemsetting();
        Integer enquiryValidDays = sys.getEnquiryValidDurationTime();
        Integer outStockValidDays = sys.getOutStockValidDurationTime();

        Date targetDate = jobDate.getDate();

        PerformanceRuleValidDateBO bo = new PerformanceRuleValidDateBO(targetDate);

        bo.setBrforeAndEqualsEnquiryLimitDate(PerformanceRuleValidDateBO.isBeforeOrEqualsEnquiryLimitDate(targetDate, enquiryValidDays));

        /*
         *  以今天是19号为例-算昨日18号数据
         *  询单有效时长是【3天】；下单-出库有效时长是【4天】
         *	(询单维度)-计算最终数据
         *	询单-下单			往前推【询单有效时长】 - 算16号
         *	询单-付款			往前推【询单有效时长】+【付款有效时长1天】- 算15号
         *	询单-出库			往前推【询单有效时长】+【付款有效时长1天】 + 【出库有效时长】 - 算15号
         */

        //询单-下单
        Date enquiry2OrderedValidDate = DateUtil.getDateByPeriod(targetDate, 0 - enquiryValidDays + 1);
        bo.setEnquiry2OrderedValidDate(enquiry2OrderedValidDate);
        //询单-付款
        Date enquiry2PayValidDate = DateUtil.getDateByPeriod(targetDate, 0 - enquiryValidDays);
        bo.setEnquiry2PayValidDate(enquiry2PayValidDate);
        //询单-出库
        Date enquiry2OutstackValidDate = DateUtil.getDateByPeriod(targetDate, 0 - enquiryValidDays - outStockValidDays);
        bo.setEnquiry2OutstackValidDate(enquiry2OutstackValidDate);
        /*
         *  以今天是19号为例-算昨日18号数据
         *  询单有效时长是3天；下单-出库有效时长是4天
         *
         *  (询单的客户下了单，以下单时间为准的组成下单维度)-计算最终数据
         *	下单-付款			往前推【付款有效时长1天】 - 算17号
         *	下单-出库			往前推【出库有效时长】 - 算14号
         *	下单-确认收货		往前推【出库有效时长】 + 【确认收货有效时长】 - 算7号
         */


        //下单-付款
        Date toOrderedThenPayValidDate = DateUtil.getDateByPeriod(targetDate, 0 - 1);
        bo.setToOrderedThenPayValidDate(toOrderedThenPayValidDate);
        //下单-出库
        Date toOrderedThenOutstackValidDate = DateUtil.getDateByPeriod(targetDate, 0 - outStockValidDays);
        bo.setToOrderedThenOutstackValidDate(toOrderedThenOutstackValidDate);
        return bo;
    }

    private boolean isTargetDateAfterSourceDate(Date targetDate, Date sourceDate) {
        return targetDate.after(sourceDate);
    }

    private int isTargetDateCompareToSourceDate(Date targetDate, Date sourceDate) {
        return targetDate.compareTo(sourceDate);
    }


    private boolean isTargetNumGreaterThanSourceNum(Integer targetNum, Integer sourceNum) {
        return targetNum > sourceNum;
    }

    private int isTargetNumCompareToSourceNum(Integer targetNum, Integer sourceNum) {
        return targetNum - sourceNum;
    }
}
