package com.pes.jd.business.impl;

import com.aliyun.oss.OSSClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.MarketingActivityBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.constants.OssClientConstants;
import com.pes.jd.model.DTO.MarketingActivityDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.pes.jd.util.AliyunOSSClientUtil;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 营销活动业务类
 *
 * @Author: yuanxun
 * @Date: 9:26 2019/10/15
 * @Description:
 */
@Service
public class MarketingActivityBusinessImpl implements MarketingActivityBusiness {
    private static Logger logger = LoggerFactory.getLogger(MarketingActivityBusinessImpl.class);

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Override
    public ApiResponse insert(String activityName, MultipartFile activityContent,
                              String startDate, String endDate,
                              String version, String orderType,String url) throws Exception {
        //上传图片到阿里云
        String fileName = uploadImageToAliyun(activityContent);

        //同步保存到数据库
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("activityName", activityName)
                .put("activityContent", OssClientConstants.DOWNURL + fileName)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("version", version)
                .put("orderType", orderType)
                .put("shopType", CommonConstants.POP_TYPE)
                .put("url", url)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/insert", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {});

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }

        return response;
    }

    @Override
    public ApiResponse deleteByPrimaryKey(Long id) throws HttpClientErrorException {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", id)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/delete", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {});

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }
        return response;
    }

    @Override
    public ApiResponse update(Long id, String activityName, Object activityContent,
                              String startDate, String endDate,
                              String version, String orderType,String url) throws Exception {
        String imageUrl;
        try{
            MultipartFile file = (MultipartFile)activityContent;
            //上传图片到阿里云
            imageUrl = OssClientConstants.DOWNURL + uploadImageToAliyun(file);
        } catch (ClassCastException e) {
            imageUrl = null;
        }


        //同步保存到数据库
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", id)
                .put("activityName", activityName)
                .put("activityContent", imageUrl)
                .put("startDate", startDate)
                .put("endDate", endDate)
                .put("version", version)
                .put("orderType", orderType)
                .put("url", url)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/update", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {
            });

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }
        return response;
    }

    @Override
    public ApiResponse updateEnable(Long id, Boolean enableSwitch) throws HttpClientErrorException {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("id", id)
                .put("enableSwitch", enableSwitch)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        response.setSuccess(Boolean.FALSE);

        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<Boolean> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/updateEnable", body, new ParameterizedTypeReference<RestResponseTypeRef<Boolean>>() {
            });

            if(responseTypeRef.getSuccess()){
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (HttpClientErrorException e) {
            throw e;
        }
        return response;
    }

    @Override
    public ApiResponse selectActivityByActivityNameAndDate(Date startDate, Date endDate, String activityName) {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("startDate", DateUtils.formatYMdHms(DateUtil.getStartTimeOfDate(startDate)))
                .put("endDate", DateUtils.formatYMdHms(DateUtil.getEndTimeOfDate(endDate)))
                .put("activityName", activityName)
                .toRequestEntity();

        ApiResponse response = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        response.setSuccess(Boolean.FALSE);
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<List<MarketingActivityDTO>> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/selectActivityByActivityNameAndDate", body, new ParameterizedTypeReference<RestResponseTypeRef<List<MarketingActivityDTO>>>() {
            });

            if(responseTypeRef.getSuccess()){
                List<MarketingActivityDTO> activityLst = responseTypeRef.getData();
                Map<String,Object> data = Maps.newHashMap();
                data.put("activityLst", activityLst);
                response = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
                response.setSuccess(Boolean.TRUE);
                return response;
            }
        } catch (Exception e) {
            throw e;
        }
        return response;
    }

    @Override
    public List<MarketingActivityDTO> selectEnableActivity() {
        try {
        	 HttpEntity<Object> body = RequestEntityBuilder.builder()
                     .put("shopType", CommonConstants.POP_TYPE)
                     .toRequestEntity();
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<List<MarketingActivityDTO>> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/marketingActivity/selectEnableActivity", body, new ParameterizedTypeReference<RestResponseTypeRef<List<MarketingActivityDTO>>>() {
            });

            if(responseTypeRef.getSuccess()){
                List<MarketingActivityDTO> activityLst =  responseTypeRef.getData();
                return activityLst;
            }
        } catch (Exception e) {
            throw e;
        }
        return Lists.newArrayListWithCapacity(0);
    }

    @Override
    public List<ShopSubScribe> selectShopSubscribeByShopId(Long shopId)  {
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopId", shopId)
                .toRequestEntity();
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());

            RestResponseTypeRef<List<ShopSubScribe>> responseTypeRef = usermgrRestTemplate.postRest(serviceId,"/shop/Subscribe/selectShopSubscribeByShopId", body, new ParameterizedTypeReference<RestResponseTypeRef<List<ShopSubScribe>>>() {
            });

            if(responseTypeRef.getSuccess()){
                List<ShopSubScribe> subLst = responseTypeRef.getData();
                return subLst;
            }
        } catch (Exception e) {
            throw e;
        }
        return Lists.newArrayListWithCapacity(0);
    }

    /**
     * 将图片上传到阿里云
     * @param file 图片文件
     * @return
     * @throws Exception
     */
    private String uploadImageToAliyun(MultipartFile file) throws Exception{

        OSSClient ossClient = AliyunOSSClientUtil.getOSSClient();

        //String name = file.getOriginalFilename();
        String substring = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")).toLowerCase();
        Random random = new Random();
        String name = random.nextInt(10000) + System.currentTimeMillis() + substring;

        try {
            InputStream inputStream = file.getInputStream();
            AliyunOSSClientUtil.uploadFile2OSS(inputStream, name, ossClient);
        } catch (Exception e) {
            throw new Exception("图片上传失败");
        }
        return name;
    }

}
