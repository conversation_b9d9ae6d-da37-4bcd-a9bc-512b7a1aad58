package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.OrderShipBusiness;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

@Service
public class OrderShipBusinessImpl implements OrderShipBusiness {

	@Autowired
	private PopSubRestTemplate popSubRestTemplate;
	@Override
	public ApiResponse getOrderShip(ShopQuery shop, String orderId){
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("shopStr", shop)
				.put("orderId", orderId).toRequestEntity();
		String serviceId = RestOperator.getMSServiceId(shop.getDbName(),ApplicationServiceNameEnum.PROVIDER_SUB.getName());
		ApiResponse apiResponse = popSubRestTemplate.postRest(serviceId, "/order/ship/getOrderShipInfo", body);
		return apiResponse;
	}

}
