package com.pes.jd.business.impl;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.business.ShopBusiness;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ShopBusinessImpl implements ShopBusiness {

    @Resource
    private UsermgrRestTemplate usermgrRestTemplate;
    @Override
    public ApiResponse listShopInfoByShopIds(String shopIds) {
        String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
        HttpEntity<Object> body = RequestEntityBuilder.builder()
                .put("shopIds", shopIds)
                .toRequestEntity();
        return usermgrRestTemplate.postRest(serviceId, "/shop/provideShopInfo", body);
    }
}
