package com.pes.jd.business.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.business.JdAddressBusiness;
import com.pes.jd.business.UserPortraitBusiness;
import com.pes.jd.dao.CsChatpeerDao;
import com.pes.jd.dao.JdAddressDao;
import com.pes.jd.dao.UserPortraitStatisticsDao;
import com.pes.jd.data.api.JdAddressOperator;
import com.pes.jd.data.api.UserPortraitOperator;
import com.pes.jd.mapper.JdAddressMapper;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.DO.UserPortraitLabel;
import com.pes.jd.model.DO.UserPortraitStatistics;
import com.pes.jd.model.DTO.CommonCsChatpeerDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class JdAddressBusinessImpl implements JdAddressBusiness {

    private Logger log= LoggerFactory.getLogger(JdAddressBusinessImpl.class);

    @Autowired
    private JdAddressDao jdAddressDao;

    @Autowired
    private JdAddressMapper jdAddressMapper;

    @Autowired
    private JdAddressOperator jdAddressOperator;

    private static final int BATCH_SIZE = 500;

    //估摸着不用每天去请求 30天一次吧
    private static final long ONE_MONTH_DAYS = 30;

    @Override
    public void handleJdAddress(JobShopQuery jobShop, boolean isDelData) throws Exception {
        JobShopDTO shop = jobShop.getShop();
        boolean needRefresh = false;

        LocalDateTime  latestCreateTime  =  jdAddressDao.getLatestCreateTime();
        if (latestCreateTime == null) {
            log.info("数据库中无地址数据，需要插入");
            needRefresh = true;
        }else{
            long daysBetween = ChronoUnit.DAYS.between(latestCreateTime, LocalDateTime.now());
            needRefresh = daysBetween >= ONE_MONTH_DAYS;
            log.info("最新数据时间：{}，距今 {} 天，{}需要刷新", latestCreateTime, daysBetween, needRefresh ? "" : "不");
        }

       if(needRefresh){
           log.info("开始刷新地址数据");
           List<JdAddress> allAddressData = jdAddressOperator.getAllAddressData(shop.getSessionKey());
              if(allAddressData != null && !allAddressData.isEmpty()){
                  if(isDelData){
                      jdAddressMapper.deleteAll();
                  }
                    batchInsertAddresses(allAddressData);
              }
       }
    }

    /**
     * 批量插入地址数据
     */
    private int batchInsertAddresses(List<JdAddress> addressList) {
        int totalInserted = 0;
        int totalSize = addressList.size();

        LocalDateTime now = LocalDateTime.now();
        addressList.forEach(address -> {
            if (address.getCreateTime() == null) {
                address.setCreateTime(now);
            }
        });

        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<JdAddress> batchList = addressList.subList(i, endIndex);

            try {
                int batchInserted = jdAddressMapper.batchInsert(batchList);
                totalInserted += batchInserted;

                log.debug("批量插入第 {}/{} 批，本批数量：{}，插入成功：{}",
                        (i / BATCH_SIZE + 1),
                        (totalSize + BATCH_SIZE - 1) / BATCH_SIZE,
                        batchList.size(),
                        batchInserted);
            } catch (Exception e) {
                log.error("批量插入第 {} 批数据失败，批次大小：{}",
                        (i / BATCH_SIZE + 1), batchList.size(), e);
                throw new RuntimeException("批量插入数据失败", e);
            }
        }
        return totalInserted;
    }
}
