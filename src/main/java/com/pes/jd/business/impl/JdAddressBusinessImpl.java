package com.pes.jd.business.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.business.JdAddressBusiness;
import com.pes.jd.data.api.JdAddressOperator;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Service
public class JdAddressBusinessImpl implements JdAddressBusiness {

    private Logger log = LoggerFactory.getLogger(JdAddressBusinessImpl.class);

    @Autowired
    private JdAddressOperator jdAddressOperator;

    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private static final int BATCH_SIZE = 500;

    //估摸着不用每天去请求 30天一次吧
    private static final long ONE_MONTH_DAYS = 30;

    private static final String REDIS_ADDRESS_EXPIRE_CHANNEL = "address_cache_expire";
    private static final String REDIS_ADDRESS_ALL_KEY = "address:all";
    private static final String REDIS_CACHE_VERSION_KEY = "address:cache:version";

    // 缓存过期时间（小时）
    private static final int CACHE_EXPIRE_HOURS = 24*30;

    @Override
    public void handleJdAddress(JobShopQuery jobShop, boolean isDelData) throws Exception {
        JobShopDTO shop = jobShop.getShop();
        boolean needRefresh = false;

        // 通过远程调用获取最新创建时间
        LocalDateTime latestCreateTime = getLatestCreateTimeFromRemote();
        long daysBetween = ChronoUnit.DAYS.between(latestCreateTime, LocalDateTime.now());
        if (daysBetween == 10000l) {
            log.info("数据库中无地址数据，需要插入");
            needRefresh = true;
        } else {
            needRefresh = daysBetween >= ONE_MONTH_DAYS;
            log.info("最新数据时间：{}，距今 {} 天，{}需要刷新", latestCreateTime, daysBetween, needRefresh ? "" : "不");
        }
        if (needRefresh) {
            log.info("开始刷新地址数据");
            List<JdAddress> allAddressData = jdAddressOperator.getAllAddressData(shop.getSessionKey());
            if (allAddressData != null && !allAddressData.isEmpty()) {
                if (isDelData) {
                    deleteAllFromRemote();
                }
                // 通过远程调用批量插入数据
                batchInsertAddresses(allAddressData);
                //更新缓存
                updateToRedis(allAddressData);
                //通知集群本地地址缓存过期
                sendCacheExpireNotification("refresh");
            }
        }
    }

    /**
     * 批量插入地址数据
     */
    private int batchInsertAddresses(List<JdAddress> addressList) {
        int totalInserted = 0;
        int totalSize = addressList.size();

        LocalDateTime now = LocalDateTime.now();
        addressList.forEach(address -> {
            if (address.getCreateTime() == null) {
                address.setCreateTime(now);
            }
        });

        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<JdAddress> batchList = addressList.subList(i, endIndex);
            try {
                batchInsertAddressesFromRemote(batchList);
                log.info("第{}批地址插入成功",i);
            } catch (Exception e) {
                log.error("批量插入第 {} 批数据失败，批次大小：{}",
                        (i / BATCH_SIZE + 1), batchList.size(), e);
                throw new RuntimeException("批量插入数据失败", e);
            }
        }
        return totalInserted;
    }

    /**
     * 通过远程调用获取最新创建时间
     */
    private LocalDateTime getLatestCreateTimeFromRemote() {
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> requestMap = new HashMap<>();
            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);

            RestResponseTypeRef<Map<String, Object>> response = usermgrRestTemplate.postRest(
                    serviceId,
                    "/jdAddress/getLatestCreateTime",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Map<String, Object>>>() {
                    });

            if (response.getSuccess() && response.getData() != null) {
                Map<String, Object> data = response.getData();
                String result = (String) data.get("result");

                if (result != null) {
                    LocalDateTime latestCreateTime = LocalDateTime.parse(result,
                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    log.info("远程调用获取最新创建时间成功: {}", latestCreateTime);
                    return latestCreateTime;
                }
            }
            log.warn("远程调用获取最新创建时间失败，返回null");
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("远程调用获取最新创建时间失败", e);
            return null;
        }
    }

    /**
     * 通过远程调用删除所有地址数据
     */
    private void deleteAllFromRemote() {
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> requestMap = new HashMap<>();
            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);

            RestResponseTypeRef<Map<String, Object>> response = usermgrRestTemplate.postRest(
                    serviceId,
                    "/jdAddress/deleteAll",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Map<String, Object>>>() {
                    });

            if (response.getSuccess() && response.getData() != null) {
                log.info("远程调用删除所有地址数据成功，删除{}条记录", response.getData());
            } else {
                log.warn("远程调用删除所有地址数据失败");
            }
        } catch (Exception e) {
            log.error("远程调用删除所有地址数据失败", e);
            throw new RuntimeException("删除地址数据失败", e);
        }
    }

    /**
     * 通过远程调用批量插入地址数据
     */
    private void batchInsertAddressesFromRemote(List<JdAddress> addressList) {
        try {
            // 设置创建时间
            LocalDateTime now = LocalDateTime.now();
            addressList.forEach(address -> {
                if (address.getCreateTime() == null) {
                    address.setCreateTime(now);
                }
            });

            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> param = new HttpEntity<>(addressList, headers);

            RestResponseTypeRef<Map<String, Object>> response = usermgrRestTemplate.postRest(
                    serviceId,
                    "/jdAddress/batchInsert",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Map<String, Object>>>() {
                    });

            if (response.getSuccess() && response.getData() != null) {
                log.info("远程调用批量插入地址数据成功，插入{}条记录", response.getData());
            } else {
                log.warn("远程调用批量插入地址数据失败");
                throw new RuntimeException("批量插入地址数据失败");
            }
        } catch (Exception e) {
            log.error("远程调用批量插入地址数据失败", e);
            throw new RuntimeException("批量插入地址数据失败", e);
        }
    }



    /**
     * 发送缓存失效通知
     * @param message 失效消息，可以是具体的缓存key或其他标识
     */
    public void sendCacheExpireNotification(String message) {
        try {
            stringRedisTemplate.convertAndSend(REDIS_ADDRESS_EXPIRE_CHANNEL, message);
            log.info("发送缓存失效通知成功: {}", message);
        } catch (Exception e) {
            log.error("发送缓存失效通知失败: {}", message, e);
        }
    }

    /**
     * 保存数据到Redis
     */
    private void updateToRedis(List<JdAddress> addresses) {
        try {
            // 1. 清理Redis缓存
            redisTemplate.delete(REDIS_ADDRESS_ALL_KEY);
            redisTemplate.delete(REDIS_CACHE_VERSION_KEY);
            String jsonStr = objectMapper.writeValueAsString(addresses);

            // 保存数据
            redisTemplate.opsForValue().set(REDIS_ADDRESS_ALL_KEY, jsonStr, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

            // 更新版本号
            long newVersion = System.currentTimeMillis();
            redisTemplate.opsForValue().set(REDIS_CACHE_VERSION_KEY, newVersion, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.info("数据已保存到Redis，缓存版本: {}", newVersion);
        } catch (Exception e) {
            log.error("保存数据到Redis失败", e);
        }
    }
}
