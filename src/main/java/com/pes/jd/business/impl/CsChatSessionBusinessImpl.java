package com.pes.jd.business.impl;

import com.pes.jd.business.CsChatSessionBusiness;
import com.pes.jd.dao.CsChatSessionDao;
import com.pes.jd.data.converter.ChatSessionConverter;
import com.pes.jd.model.BO.CsChatSessionBO;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.Query.JobShopQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/21 10:03 AM
 * @since 1.0.0
 */
@Service
public class CsChatSessionBusinessImpl implements CsChatSessionBusiness {

    @Resource
    private ChatSessionConverter chatSessionConverter;

    @Resource
    private CsChatSessionDao csChatSessionDao;

    @Override
    public int insertBatchChatSession(JobShopQuery jobShop, Date startDate, Date endDate, String csNick) throws Exception{
    CsChatSessionBO bo=	chatSessionConverter.getChatSessionLst(jobShop.getShop().getShopId(),jobShop.getShop().getSessionKey(), startDate, endDate, csNick);
    return csChatSessionDao.insertBatchCsChatSession(jobShop.getShop(), startDate, bo.getChatSessionLst());
    }

    @Override
    public List<CsChatSessionDO> searchAllByTime(Date beginDate, Date endDate,Long shopId,String schemaId) {

//        return csChatSessionDao.searchAllByTime(beginDate, endDate, shopId, schemaId, "");
        return null;
    }
}
