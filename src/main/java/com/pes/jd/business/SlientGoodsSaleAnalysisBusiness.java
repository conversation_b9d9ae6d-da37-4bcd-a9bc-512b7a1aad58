package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.SlientGoodsSaleIndexDTO;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.List;

public interface SlientGoodsSaleAnalysisBusiness {
	public List<SlientGoodsSaleIndexDTO> selectGoodsSaleIndexList(UserShopQuery shop, GoodsConsultParam param) throws DBNotExistException;
	
	ApiResponse selectGoodsSaleIndexDetailList(UserShopQuery shop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws DBNotExistException;
	
}
