package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;

public interface DealDataAnalysisBusiness {
    ApiResponse searchShopSaleAnalysisLst(UserShopQuery userShopQuery, ShopSaleParam shopSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse searchCsSaleAnalysisLst(UserShopQuery userShopQuery, CsSaleParam csSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;


    ApiResponse searchSilenceSaleAnalysisLst(UserShopQuery userShopQuery, SilenceSaleParam silenceSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse selectCsRefundAnalysisLst(ShopQuery shop, RefundAnalysisParam param, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse selectOrderPresaleLst(UserShopQuery userShopQuery, OrderPresaleParam orderPresaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse selectCsRefundAnalysisLstOfSpu(ShopQuery selectedShop, RefundAnalysisParam param, OrderInfoLogUploadParam orderInfoLogUploadParam) throws DBNotExistException;


    DataAnalysisVO<OrderPreordainDTO> selectOrderPredainLst(ShopQuery shopQuery, OrderPreOrdainParam orderPreOrdainParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception ;
}
