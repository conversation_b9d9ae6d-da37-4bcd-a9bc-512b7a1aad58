package com.pes.jd.business;

import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Param.CsLoginlogParam;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.List;

public interface CsSpecialPerformanceBusiness {

	ApiResponse queryCsDutyRecordCollect(UserShopQuery shopQuery, CsLoginlogParam csLoginlogParam, ShopSystemsettingDTO shopSystemsetting, String startDate, String endDate) throws Exception;

	ApiResponse queryCsDutyRecordDetail(UserShopQuery shopQuery, CsLoginlogParam csLoginlogParam, ShopSystemsettingDTO shopSystemsetting, String startDate, String endDate) throws Exception;

	ApiResponse queryCsLoginOperateDetail(UserShopQuery shopQuery, List<UserQuery> userQueryLst, ShopSystemsettingDTO shopSystemsetting, String startDate, String endDate) throws Exception;

}
