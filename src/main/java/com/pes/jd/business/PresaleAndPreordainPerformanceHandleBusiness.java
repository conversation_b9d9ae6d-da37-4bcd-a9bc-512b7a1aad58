package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface PresaleAndPreordainPerformanceHandleBusiness {


    /**
     * 客服绩效 之 预售订单
     *
     * @param jobShop   shop
     * @param jobDate   date
     * @param isDelData del
     * @throws Exception e
     */
    void handlePerformanceForPresale(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    /**
     * 客服绩效 之 预约订单
     *
     * @param jobShop   shop
     * @param jobDate   date
     * @param isDelData del
     * @throws Exception e
     */

    void handlePerformanceForPreordain(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
}
