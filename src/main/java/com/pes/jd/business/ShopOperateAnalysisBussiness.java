package com.pes.jd.business;

import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.ms.domain.Response.RestApiResponse2;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ShopOperateAnalysisBussiness {


	RestApiResponse2<List<ShopSubScribe>> selectShopSubScribeDetailForUseAnalysis(UserAnalysisParam param)
			throws Exception;

	Map<String, Object> selectCsLoginLogForShopUserAnalysis(Long shopId, UserAnalysisParam param) throws Exception;

	 RestApiResponse2<List<ShopUrge>> selectUrgeShopByShopId(Long shopId) throws Exception;

	Map<String, Object> selectUrgeCompareClosureShopLst(Long shopId, Date startDate, Date endDate, Integer type) throws Exception;

	Map<String, Object> selectShopOperateReportForUseAnalysisForOptiom(UserAnalysisParam param) throws Exception;

	ApiResponse selectShopAuthExpiredLst(String startDate, String endDate, String nick, Integer shopType)throws Exception;
}
