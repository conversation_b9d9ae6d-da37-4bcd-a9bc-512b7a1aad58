package com.pes.jd.business;

import com.pes.jd.model.Param.DeptInfoParam;
import com.pes.jd.model.Response.ApiResponse;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/3 13:46
 */
public interface DashboardBusiness {

    ApiResponse dashboardLogin(String username, String password, HttpServletRequest request) throws Exception;

    ApiResponse getDeptRelation(String id, String deptId, String type) throws Exception;

    void uploadExcelInfo(List<DeptInfoParam> deptInfos, String filerId) throws Exception;

    ApiResponse getUploadExcelInfo()throws Exception;

    ApiResponse uploadFilerInfo(String filerName)throws Exception;



}
