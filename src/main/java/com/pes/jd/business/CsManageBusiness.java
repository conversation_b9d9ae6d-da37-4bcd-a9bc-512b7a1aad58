  
package com.pes.jd.business;

import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.List;


/**  
 * 客服业务接口
 * ClassName:CsManageBusiness <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午2:46:44 <br/>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface CsManageBusiness {
	
	List<CsDTO> searchCsLstByShop(JobShopQuery jobShop, Integer csType);

	List<CsDTO> searchCsLstByShopIdAndType(Long shopId, Integer csType);

}
  
