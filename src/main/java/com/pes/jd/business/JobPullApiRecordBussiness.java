package com.pes.jd.business;


import com.pes.jd.model.DO.JobPullApiRecordDO;

public interface JobPullApiRecordBussiness {
    int deleteByPrimaryKey(Long id);

    int insert(JobPullApiRecordDO record);

    int insertSelective(JobPullApiRecordDO record);

    JobPullApiRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JobPullApiRecordDO record);

    int updateByPrimaryKey(JobPullApiRecordDO record);
}