package com.pes.jd.business;


import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;

public interface CsGoodsSaleBusiness {

	ApiResponse selectCsGoodsSaleIndexDetail(ShopQuery shop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam, Integer enquiryValidDurationTime) throws DBNotExistException;

	ApiResponse selectCsGoodsSaleIndex(ShopQuery shop, GoodsConsultParam param) throws DBNotExistException;

	ApiResponse selectCsSaleAnalysis(ShopQuery shop, GoodsConsultParam param) throws DBNotExistException;

    ApiResponse selectCsGoodsSaleIndexOfSpu(ShopQuery selectedShop, GoodsConsultParam param) throws DBNotExistException;

    ApiResponse selectCsGoodsSaleIndexDetailOfSpu(ShopQuery selectedShop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam, Integer enquiryValidDurationTime) throws DBNotExistException;

    ApiResponse selectCsGoodsSaleIndexV2(ShopQuery selectedShop, GoodsConsultParam param) throws DBNotExistException;

    ApiResponse selectCsGoodsSaleIndexDetailV2(ShopQuery selectedShop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam, Integer enquiryValidDurationTime) throws DBNotExistException;

    ;;
}
