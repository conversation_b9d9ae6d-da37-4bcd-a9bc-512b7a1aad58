package com.pes.jd.business;

import com.pes.jd.model.DTO.CsChatSessionDTO;
import com.pes.jd.model.DTO.CsChatlogDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RealtimeMonitorBusiness {

	void calculateShopBadReceive(JobShopQuery jobShop,
								 JobDateQuery jobDate,
								 boolean isDelData,
								 String csNick, Date sdate,
								 Date edate,
								 Map<CsChatSessionDTO,List<CsChatlogDTO>> csChatSessionMap);
}
  
