package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;

import java.util.List;


public interface DataAnalysisBusiness {

    ApiResponse searchEnquiryLostRecordLst(LossOrderParam lossOrderParam, String startDate, String endDate,
                                           Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds) throws Exception;

    ApiResponse saveOrUpdateLostRecordNote(UserShopQuery shopQuery, String dateStr, String buyerNick, String orderId,
                                           Long noteId, String note, Integer lostType) throws Exception;

    ApiResponse searchChatlogLst(UserShopQuery shopQuery, String startDate, String endDate, UserQuery userQuery, String buyerNick, String sid, ShopSystemsettingDTO shopSystemsetting, Boolean dbFlag, boolean isPlugin, Integer direction, boolean realtimeSlowResp, Boolean warnAnalyze, String keyword) throws Exception;

    ApiResponse searchChatlogLstForConver(UserShopQuery shopQuery, String startDate, String endDate, List<UserQuery> userQuery, String buyerNick, String sid, Boolean dbFlag, boolean isPlugin) throws Exception;

    
    ApiResponse selectChatSessionLst(UserShopQuery shop, String startDate, String endDate, String csNickListStr, String customer, String type) throws Exception;

    ApiResponse searchShopSaleAnalysisLst(UserShopQuery userShopQuery, ShopSaleParam shopSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse searchCsSaleAnalysisLst(UserShopQuery userShopQuery, CsSaleParam csSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse searchSilenceSaleAnalysisLst(UserShopQuery userShopQuery, SilenceSaleParam silenceSaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse searchEnquiryOrderLostRecordLst(LossOrderParam lossOrderParam, String startDate, String endDate, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse searchSilentOrderLostRecordLst(UserShopQuery shopQuery, String startDate, String endDate,
                                               LossOrderParam lossOrderParam, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    ApiResponse selectCsRefundAnalysisLst(UserShopQuery shop, RefundAnalysisParam param, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    /**
     * 查询预售订单分析列表
     */
    ApiResponse selectOrderPresaleLst(UserShopQuery userShopQuery, OrderPresaleParam orderPresaleParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;


    ApiResponse selectCsServiceEval(UserShopQuery shopQuery, CsServiceEvalParam param, SortPageQuery sortPageQuery) throws DBNotExistException, Exception;

    ApiResponse selectCsReceiveUnSendEval(UserShopQuery shopQuery, CsReceiveUnSendEvalParam param, SortPageQuery sortPageQuery) throws DBNotExistException, Exception;

    ApiResponse serachCustomerReceiveRecordForChatSession(UserShopQuery shop, ShopSystemsettingDTO system, CustomerReceiveParam param,
                                                          SortPageQuery sortPageQuery) throws Exception;

    ApiResponse searchCustomerReciveRecordLst(UserShopQuery shop, CustomerReceiveParam param,
                                              SortPageQuery sortPageQuery) throws Exception;

    ApiResponse selectLeaveMessage(UserShopQuery shopQuery, String startDateStr, String endDateStr, String csNickListStr, String customer, Integer selectType) throws Exception;

    ApiResponse searchChatpeerLst(UserShopQuery shopQuery, String startDate, String endDate, List<UserQuery> csLst,
                                  String buyerNick, String keyWord) throws Exception;



    ApiResponse selectCsWarn(UserShopQuery shop, String startDateStr, String endDateStr, String csNickListStr, String customer, Byte warningCsViolation, String keyword) throws Exception;

    ApiResponse selectCsWarnFromRtDb(UserShopQuery shop, String startDateStr, String endDateStr, String csNickListStr, String customer, Byte warningCsViolation, String keyword) throws Exception;

     DataAnalysisVO<OrderPreordainDTO> selectOrderPredianLst(UserShopQuery userShopQuery, OrderPreOrdainParam orderPreOrdainParam, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception ;

     //按客服
    ApiResponse selectReceiveFilter(UserShopQuery shop, ReceiveFilterParam param) throws Exception;
     //按会话维度查询
     ApiResponse selectReceiveFilterByChatSession(UserShopQuery shop, ReceiveFilterParam param) throws Exception;
}
