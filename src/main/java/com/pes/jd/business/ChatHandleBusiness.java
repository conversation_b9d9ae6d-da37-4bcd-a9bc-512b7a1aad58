package com.pes.jd.business;

import com.pes.jd.exception.SysSettingException;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface ChatHandleBusiness {

    void redisCommonChat(JobShopQuery jobShop, JobDateQuery jobDate,
                         boolean isDelData) throws SysSettingException;

    void handleCommonChat(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

	void handleEnquiryChat(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    void handleFinalData(JobShopQuery jobShop, JobDateQuery jobDate);

    void handleEnquiryLoss(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

	void handleCsConsultFirstToEnquiry(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

	void calculateShopCsLossData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

	void handleOrderOutStockLoss(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

	void handleOrderLoss(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

    void deleteShopLossRecord(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);
}
