package com.pes.jd.business.main.impl;


import com.google.common.collect.Lists;
import com.pes.jd.business.main.*;
import com.pes.jd.constants.PermissionConstants;
import com.pes.jd.data.connect.HomePageConfig;
import com.pes.jd.model.DO.CustomReportDO;
import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.model.DO.PesServicePermission;
import com.pes.jd.model.DO.PesUserMenuPermission;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.VO.PesMenuResourceVo;
import com.pes.jd.util.MapUtils;
import com.pes.jd.util.ReflectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.stream.Collectors;

import static com.pes.jd.constants.PermissionConstants.GENERATOR_MENU_RESOURCE_NAME;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/26 2:56 PM
 * @since 1.0.0
 */
@SuppressWarnings("Duplicates")
@Service
public class HomePageBusinessImpl implements HomePageBusiness {

    @Autowired
    private ReportPropertyBusiness reportPropertyBusiness;

    @Autowired
    private CustomReportBusiness customReportBusiness;

    @Autowired
    private CustomReportPropertyBusiness customReportPropertyBusiness;

    @Autowired
    private PerformanceSettingBussiness performanceSettingBussiness;

    @Autowired
    private PesMenuResourceBusiness pesMenuResourceBusiness;


    @Autowired
    private PesUserMenuPermissionBusiness pesUserMenuPermissionBusiness;

    @Autowired
    private PesUserServicePermissionBusiness pesUserServicePermissionBusiness;

    @Autowired
    private PesServicePermissionBusiness pesServicePermissionBusiness;

    @Autowired
    private ShopBusiness shopBusiness;

    @Autowired
    private ShopMenuHomeBusiness shopMenuHomeBusiness;

    @Autowired
    private HomePageConfig homePageConfig;

    private final static Logger LOGGER = LoggerFactory.getLogger(HomePageBusinessImpl.class);

    private final static String CS_DIFF = "consultNum,receiveNum,queryToFinalPaid,queryToOutStock,avgRespTimeFirst,avgRespTime,saleAmount,saleAmountPercent,saleGoodsNum,saleBuyerNum,saleGuestAvgAmount,outStockAmount,personalOutStockAmountPercent,outStockGoodsNum,outStockNum,outStockGuestAvgAmount,completedRefundAmount,completedRefundProductNum";

    private final static String SHOP_PANDECT = "shopSaleAmount,shopSaleBuyerNum,shopOutStockAmount,shopOutStockNum,pvNum,uvNum,shopDealPercent,shopOutStockPercent,consultPercent,csSaleAmount,csSaleAmountPercent,csSaleBuyerNum,csSaleGoodsNum,csSaleGuestAvgAmount,csOutStockAmount,csOutStockAmountPercent,csOutStockNum,csOutStockGoodsNum,csOutStockGuestAvgAmount,consultNum,receiveNum,enquiryNum,queryToFinalPaid,queryToOutStock,avgRespTimeFirst,avgRespTime,shopRefundAmount,shopRefundProductNum,csRefundAmount,csRefundProductNum,silenceSaleAmount,silenceSaleBuyerNum,silenceDealPercent,silenceOutStockAmount,silenceOutStockNum,productEvaluationDSR,serviceAttitudeDSR,logisticsSpeedDSR,afterSaleScore,disputeScore";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initUserPermission(String nick) {
        //首先判断该用户是否有权限
        if (pesUserMenuPermissionBusiness.selectCountByNick(nick) > 0) {
            return;
        }
        //为用户初始化 菜单权限(个人权限可以访问的报表)-> 菜单权限需要排除系统管理的菜单 数据权限(个人权限)
        final List<PesMenuResource> pesMenuResources = exclude(pesMenuResourceBusiness.searchPerson());
        final List<ShopAccountDTO> shopAccounts = getShopAccoutByNick(nick);
        pesUserMenuPermissionBusiness.insertByShopAccountsAndMenus(
                shopAccounts, pesMenuResources
        );
        /*添加权限*/
        final List<PesServicePermission> pesServicePermissions =
                pesServicePermissionBusiness.searchAll(null, Arrays.asList("个人数据", "导出权限"));
        pesUserServicePermissionBusiness.insertByShopAccounts(shopAccounts, pesServicePermissions);

    }

    /**
     * 排除系统菜单权限
     */
    private List<PesMenuResource> exclude(List<PesMenuResource> searchPerson) {

        final List<PesMenuResourceVo> pesMenuResourceVos = pesMenuResourceBusiness.searchAll((Long) null, true);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(pesMenuResourceVos)) {
            return searchPerson;
        }
        final List<PesMenuResourceVo> sysMenu = getSysMenu(pesMenuResourceVos);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(sysMenu)) {
            return searchPerson;
        }
        final Map<Long, PesMenuResourceVo> idMenu = sysMenu.stream().collect(Collectors.toMap(PesMenuResourceVo::getId, v -> v));
        List<PesMenuResource> res = new ArrayList<>(searchPerson.size());
        for (PesMenuResource person : searchPerson) {

            if (!idMenu.containsKey(person.getId())) {
                res.add(person);
            }

        }
        return res;
    }

    private void add(List<PesMenuResourceVo> result, List<PesMenuResourceVo> data) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(data)) {
            return;
        }
        for (PesMenuResourceVo pesMenuResourceVo : data) {
            result.add(pesMenuResourceVo);
            add(result, pesMenuResourceVo.getSubMenuLst());
        }
    }

    private List<PesMenuResourceVo> getSysMenu(List<PesMenuResourceVo> pesMenuResourceVos) {
        List<PesMenuResourceVo> res = new ArrayList<>();
        for (PesMenuResourceVo pesMenuResourceVo : pesMenuResourceVos) {
            if (pesMenuResourceVo.getTitle().equals("系统管理")) {
                res.add(pesMenuResourceVo);
                add(res, pesMenuResourceVo.getSubMenuLst());
                return res;
            }
        }
        return null;
    }

    private List<ShopAccountDTO> getShopAccoutByNick(String nick) {
        final ShopAccountDTO shopAccountDTO = new ShopAccountDTO();
        shopAccountDTO.setNick(nick);
        /*添加菜单权限*/
        return Arrays.asList(shopAccountDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void intiAdmin(String nick) {
        //首先判断该用户是否有权限
        if (pesUserMenuPermissionBusiness.selectCountByNick(nick) > 0) {
            return;
        }
        final List<PesMenuResource> pesMenuResourceVos = pesMenuResourceBusiness.searchAll();
        final List<PesServicePermission> pesServicePermissions = pesServicePermissionBusiness.searchAll(null, null);
        final List<ShopAccountDTO> shopAccounts = getShopAccoutByNick(nick);
        pesUserMenuPermissionBusiness.insertByShopAccountsAndMenus(shopAccounts, pesMenuResourceVos);
        pesUserServicePermissionBusiness.insertByShopAccounts(shopAccounts, pesServicePermissions);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public MultiValueMap<String, ReportPropertyDTO> searchAllWithCategory(String name, Long shopId) {
        /*获取系统设置*/
        final ShopSystemsettingDTO shopSystemsetting =
                performanceSettingBussiness.getShopSystemsettingByShopId(shopId);
        return transfor(reportPropertyBusiness.searchAllWithCategory(name, shopSystemsetting), shopId);
    }

    private MultiValueMap<String, ReportPropertyDTO> transfor(List<ReportPropertyDTO> searchAllWithCategory, Long shopId) {
        if (CollectionUtils.isEmpty(searchAllWithCategory)) {
            return new LinkedMultiValueMap<>();
        }
        MultiValueMap<String, ReportPropertyDTO> extract = MapUtils.extract(searchAllWithCategory, ReportPropertyDTO::getCategoryName);
        extract = sort(extract, shopId);
        return extract;
    }

    private MultiValueMap<String, ReportPropertyDTO> sort(MultiValueMap<String, ReportPropertyDTO> extract, Long shopId) {
        //查询 type 0-pop 1-自营
        Integer type = 0;
        ShopDTO shopDTO = shopBusiness.selectShopByShopId(shopId);
        if (null != shopDTO) {
            type = shopDTO.getType();
        }
        String[] sort;
        if (1 == type) {
            sort = new String[]{
                    "销售数据", "顾客数据", "客单价", "转化率", "询单→下单", "下单→付款", "下单→出库", "询单→付款", "客服落实下单", "客服落实付款",
                    "工作量", "值班记录", "协助服务", "出库数据", "中差评", "满意率", "退款数据", "流失数据", "其他"//去掉退款 ：,"退款数据"
            };

        } else {
            sort = new String[]{
                    "销售数据", "顾客数据", "客单价", "转化率", "询单→下单", "下单→付款", "下单→出库", "询单→付款", "客服落实下单", "客服落实付款",
                    "工作量", "值班记录", "协助服务", "出库数据", "中差评", "满意率", "退款数据", "流失数据", "其他"
            };
        }

        List<String> removeInfo = Arrays.asList("全静默订单本人跟进-金额", "全静默订单本人跟进-人数", "静默下单本人落实付款-件数", "静默下单本人落实付款-金额",
                "静默下单本人落实付款-人数", "全静默订单本人跟进-件数", "全静默订单本人跟进-订单数", "静默下单本人落实付款-订单数");

        MultiValueMap<String, ReportPropertyDTO> res = new LinkedMultiValueMap<>();
        for (String so : sort) {
            final List<ReportPropertyDTO> reportPropertyDTOS = extract.get(so);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(reportPropertyDTOS)) {
                if (1 == type) {
                    if ("销售数据".equals(so)) {
                        for (ReportPropertyDTO reportPropertyDTO : reportPropertyDTOS) {
                            String title = reportPropertyDTO.getTitle();
                            if ("销售量".equals(title)) {
                                List<ReportPropertyDTO.FilterJson> filter = reportPropertyDTO.getFilter();
                                for (int i = 0; i < filter.size(); i++) {
                                    ReportPropertyDTO.FilterJson filterJson = filter.get(i);
                                    if ("扣除退款件数".equals(filterJson.getTitle())) {
                                        // filter.remove(i);
                                    }
                                }
                            }
                            if ("销售额".equals(title)) {
                                List<ReportPropertyDTO.FilterJson> filter = reportPropertyDTO.getFilter();
                                for (int i = 0; i < filter.size(); i++) {
                                    ReportPropertyDTO.FilterJson filterJson = filter.get(i);
                                    if ("扣除退款".equals(filterJson.getTitle())) {
                                        // filter.remove(i);
                                    }
                                }
                            }
                        }
                    }
                    if ("客服落实付款".equals(so)) {
                        Iterator<ReportPropertyDTO> iterator = reportPropertyDTOS.iterator();
                        while (iterator.hasNext()) {
                            ReportPropertyDTO next = iterator.next();
                            if (removeInfo.contains(next.getTitlelong())) {
                                iterator.remove();
                            }
                        }
                    }
                }
                res.put(so, reportPropertyDTOS);
            }
        }
        return res;

    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<CustomReportDTO> searchAllByShopId(Integer type, Long shopId) {
        // 检查预定义报表
        checkPerDefine(shopId);

        // 查表
        List<CustomReportDTO> customReportDTOS = customReportBusiness.searchCustomReportByType(type, shopId);


        /*获取系统设置*/
        final ShopSystemsettingDTO shopSystemsetting =
                performanceSettingBussiness.getShopSystemsettingByShopId(shopId);


        // 查询所有的列（基本数据）
        List<ReportPropertyDTO> reportPropertyDTOS = reportPropertyBusiness.searchAllWithCategory(null, shopSystemsetting);
        Map<Long, ReportPropertyDTO> reportPropertyDTOSMaps =
                reportPropertyDTOS.stream().collect(Collectors.toMap(ReportPropertyDTO::getId, v -> v, (x, y) -> {
                    LOGGER.warn(" 重复的key -> cause table report property ");
                    return x;
                }));

        //过滤掉- 用户评价评分，客服咨询评分，物流履约评分，售后服务评分，交易纠纷评分
        Map<Long, ReportPropertyDTO> reportPropertyDTOSMaps2 = new HashMap<>();
        for (Map.Entry map : reportPropertyDTOSMaps.entrySet()) {
            ReportPropertyDTO reportPropertyDTO = (ReportPropertyDTO) map.getValue();
            if ("用户评价评分".equals(reportPropertyDTO.getTitle())) {

            } else if ("客服咨询评分".equals(reportPropertyDTO.getTitle())) {

            } else if ("物流履约评分".equals(reportPropertyDTO.getTitle())) {

            } else if ("售后服务评分".equals(reportPropertyDTO.getTitle())) {

            } else if ("交易纠纷评分".equals(reportPropertyDTO.getTitle())) {

            } else {
                reportPropertyDTOSMaps2.put((Long) map.getKey(), reportPropertyDTO);
            }
        }
        // 查寻所有表和列的对应关系
        List<CustomReportPropertyDTO> customReportPropertyDOS =
                null;
        try {
            customReportPropertyDOS = customReportPropertyBusiness.searchAllByReportIds(customReportDTOS.stream()
                    .map(CustomReportDTO::getId).collect(Collectors.toSet()));
            MultiValueMap<Object, CustomReportPropertyDTO> customReportPropertyDOSMaps =
                    ReflectionUtils.fillNonExe(customReportPropertyDOS, "customReportId");
            // 填充列数据-> 表
            customReportDTOS.forEach(e -> {
                List<ReportPropertyDTO> propertyDTOLst = Lists.newArrayList();
                // 该表对应的列的关系
                List<CustomReportPropertyDTO> customReportPropertys = customReportPropertyDOSMaps.get(e.getId());
                if (!CollectionUtils.isEmpty(customReportPropertys)) {
                    ReportPropertyDTO tempReportProperty = new ReportPropertyDTO();
                    customReportPropertys.forEach(k -> {
                        ReportPropertyDTO reportPropertyDTO = reportPropertyDTOSMaps2.get(k.getPropertyId());
                        if (null != reportPropertyDTO) {
                            BeanUtils.copyProperties(reportPropertyDTO, tempReportProperty);
                            reportPropertyDTO = getInitObject(reportPropertyDTO);
                            // 将  基本列的json 替换成 实际选择的json数据
                            reportPropertyDTO.setFilter(k.getFilter());
                            reportPropertyDTO.setFilterFlag(k.getFilterFlag());
                            reportPropertyDTO.setFilterJson(k.getFilterJson());
                            propertyDTOLst.add(reportPropertyDTO);
                            //-------------------------filter 缺少title字段
                            this.addTitleField(reportPropertyDTO, tempReportProperty);
                        }
                    });
                    e.setPropertyDTOLst(propertyDTOLst);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }


        return customReportDTOS;
    }

    //title属性填充
    private void addTitleField(ReportPropertyDTO reportPropertyDTO, ReportPropertyDTO tempReportProperty) {
        if (reportPropertyDTO.getFilter() == null || tempReportProperty.getFilter() == null) {
            return;
        }
        for (ReportPropertyDTO.FilterJson source : reportPropertyDTO.getFilter()) {
            for (ReportPropertyDTO.FilterJson temp : tempReportProperty.getFilter()) {
                if (source.getProperty().equals(temp.getProperty())) {
                    source.setTitle(temp.getTitle());
                }
            }
        }
    }

    private ReportPropertyDTO getInitObject(ReportPropertyDTO reportPropertyDTO) {
        ReportPropertyDTO dto = new ReportPropertyDTO();
        BeanUtils.copyProperties(reportPropertyDTO, dto);
        return dto;
    }


    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertOrUpdateCustomReport(CustomReportDTO report, Integer type, String nick) {
        final Integer add = 1;
        final Integer update = 2;

        List<CustomReportDTO> reports = customReportBusiness.selectByShopAndName(report.getShopId(), report.getName());
        if ((add.equals(type) && reports.size() > 0) || (update.equals(type) && reports.size() > 1)) {
            return -2;
        }
        if (Objects.equals(type, add)) {
            report.setStatus((byte) 1);
            doAddCustomReport(report, nick, true);
        } else if (Objects.equals(update, type)) {
            doUpdateCustomReport(report);
        }
        return 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteCustomReport(Long id) {
        //删除自定义报表+删除自定义报表的列+删除自定义报表对应的资源+删除自定义报表对应的用户资源
        final List<PesMenuResource> pesMenuResources = pesMenuResourceBusiness.selectByName(id);
        Assert.notEmpty(pesMenuResources, "无法查找对应ID对应的Name->menu resource表");
        final PesMenuResource menuResource = pesMenuResources.get(0);
        return customReportBusiness.deleteByPrimaryKeyWithProperty(id)
                + customReportPropertyBusiness.deleteByReportId(id)
                + pesMenuResourceBusiness.deleteByName(id)
                + pesUserMenuPermissionBusiness.deleteByMenuResourceId(menuResource.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkPerDefine(Long shopId) {
        if (customReportBusiness.checkPredefine(shopId, "客服对比") == 0) {
            final CustomReportDTO cs = new CustomReportDTO();
            cs.setShopId(shopId);
            String[] shopProperties = CS_DIFF.split(",");
            cs.setCustomReportPropertyLst(getCustomProperty(shopProperties, 2/*客服的*/));
            cs.setName("客服对比");
            cs.setStatus((byte) 2);
            cs.setType((byte) 2);
            doAddCustomReport(cs, null, false);
        }
        if (customReportBusiness.checkPredefine(shopId, "店铺总览") == 0) {
            final CustomReportDTO shop = new CustomReportDTO();
            shop.setShopId(shopId);
            String[] shopProperties = SHOP_PANDECT.split(",");
            shop.setCustomReportPropertyLst(getCustomProperty(shopProperties, 1/*店铺的*/));
            shop.setName("店铺总览");
            shop.setStatus((byte) 2);
            shop.setType((byte) 1);
            doAddCustomReport(shop, null, false);
        }
    }

    /**
     * 将新增的字段同步到#{店铺总览}的报表中
     *
     * @param fieldsStr
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertCustomReport(String fieldsStr, String reportName) {
        List<String> fields = Arrays.asList(fieldsStr.split(","));
        //获取要新增的字段的ID
        List<ReportPropertyDTO> propertyDTOS = reportPropertyBusiness.searchAllWithCategory(fields, 1);//type=1店铺维度，2客服维度字段
        List<Long> propertyIds = propertyDTOS.stream().map(ReportPropertyDTO::getId).collect(Collectors.toList());

        List<CustomReportPropertyDTO> customReportPropertyLst = new ArrayList<>();
        List<Long> reportId = customReportBusiness.searchReportIdByReportName(reportName);
        if (CollectionUtils.isEmpty(reportId)) {
            return 0;
        }
        //查出所有店铺的字段值
        List<CustomReportPropertyDTO> customReportPropertyDTOS = customReportPropertyBusiness.searchAllByReportIds(new HashSet<>(reportId));
        Map<Long, List<CustomReportPropertyDTO>> reportPropertyMap = customReportPropertyDTOS.stream().collect(Collectors.groupingBy(csp -> csp.getCustomReportId()));

        reportPropertyMap.forEach((customReportId, v) -> {
            List<Long> insertFields = new ArrayList<>(propertyIds.size());
            a:
            for (Long propertyId : propertyIds) {
                for (int i = 0; i < v.size(); i++) {
                    if (v.get(i).getPropertyId().equals(propertyId)) {
                        continue a;
                    }
                }
                insertFields.add(propertyId);
            }
            // 没有的字段需要保存到数据库中
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(insertFields)) {
                for (Long insertField : insertFields) {
                    CustomReportPropertyDTO customReportPropertyDTO = new CustomReportPropertyDTO();
                    customReportPropertyDTO.setPropertyId(insertField);
                    customReportPropertyDTO.setCustomReportId(customReportId);
                    customReportPropertyDTO.setFilterFlag((byte) 0);
                    customReportPropertyDTO.setStatus((byte) 1);
                    customReportPropertyLst.add(customReportPropertyDTO);
                }
            }
        });
        if (org.apache.commons.collections.CollectionUtils.isEmpty(customReportPropertyLst)) {
            return 0;
        }
        //如果店铺总览报表不包含此字段则插入
        int count = customReportPropertyBusiness.insertBatch(customReportPropertyLst);
        LOGGER.info("{}->报表插入字段数为:{}（个）", reportName, count);
        return count;
    }

    private List<CustomReportPropertyDTO> getCustomProperty(String[] shopProperties, Integer type) {
        final List<ReportPropertyDTO> reportPropertyDTOS =
                reportPropertyBusiness.searchAllWithCategory(Arrays.asList(shopProperties), type);

        final Map<String, ReportPropertyDTO> propertyMap = reportPropertyDTOS.stream().collect(Collectors.toMap(ReportPropertyDTO::getProperty, v -> v));
        List<ReportPropertyDTO> reports = new ArrayList<>();
        for (String shopProperty : shopProperties) {
            reports.add(propertyMap.get(shopProperty));
        }
        return reports.stream().map(k -> {
            CustomReportPropertyDTO dto = new CustomReportPropertyDTO();
            dto.setPropertyId(k.getId());
            dto.setFilterFlag((byte) 0);
            return dto;
        }).collect(Collectors.toList());

    }

    private void doUpdateCustomReport(CustomReportDTO report) {

        /**
         *  表-> 修改
         *  列-> 删除然后添加
         */
        final String message = "修改表信息失败";
        CustomReportDO entity = new CustomReportDO();
        BeanUtils.copyProperties(report, entity);
        customReportBusiness.updateByPrimaryKey(entity);
        Long id = report.getId();
        customReportPropertyBusiness.deleteByReportId(id);
        addCustomProperty(report, id, null, false);

        // 如果自定义报表，改变类型，那么对应的菜单权限也需要改变权限
        PesMenuResource pesMenuResource = new PesMenuResource();
        pesMenuResource.setName("menu:" + report.getId());
        final boolean person = Objects.equals(report.getType(), (byte) 2);
        pesMenuResource.setIsCsTeamData(person);
        pesMenuResource.setIsPersonalData(person);
        pesMenuResource.setIsShopData(true);
        pesMenuResourceBusiness.updatePermission(pesMenuResource);
    }

    /**
     * 添加自定义报表  其中 com.pes.jd.model.DTO.CustomReportDTO#customReportPropertyLst 只需要tableId 和 propertyId
     * （ 注意：是否需要字段过滤 filterFlag 如果没有就是 0，否则1  ，记得填充filterJson ）
     *
     * @param report
     * @param nick
     * @param addSource
     */
    private void doAddCustomReport(CustomReportDTO report, String nick, boolean addSource) {
        /**
         *  添加表
         *  添加字段关系
         */
        CustomReportDO entity = new CustomReportDO();
        BeanUtils.copyProperties(report, entity);
        customReportBusiness.insert(entity).intValue();
        Long tableId = entity.getId();
        addCustomProperty(report, tableId, nick, addSource);

    }

    private void addCustomProperty(CustomReportDTO report, Long tableId, String nick, Boolean addResource) {
        // 所有的列
        List<CustomReportPropertyDTO> customReportPropertyLst = report.getCustomReportPropertyLst();
        customReportPropertyLst.forEach(e -> e.setCustomReportId(tableId));
        customReportPropertyBusiness.insertBatch(customReportPropertyLst);
        if (addResource) {
            /*添加表，添加列之后，需要将该表添加到菜单权限中去*/
            final Byte type = report.getType();
            PesMenuResource menuResource = new PesMenuResource();
            menuResource.setTitle(report.getName());
            menuResource.setName(GENERATOR_MENU_RESOURCE_NAME.apply(tableId));
            menuResource.setSort(0);
            menuResource.setParentId(PermissionConstants.CUSTOM_REPORT_ID);
            menuResource.setNote(String.format("自定义生成->操作员：%s", nick));
            menuResource.setIsDefault(false);
            final boolean person = Objects.equals(type, (byte) 2);
            menuResource.setIsPersonalData(person);
            menuResource.setIsCsTeamData(person);
            menuResource.setIsShopData(true);
            menuResource.setIsShopMultipleData(false);
            menuResource.setShopId(report.getShopId());
            pesMenuResourceBusiness.insertMenuResource(menuResource);
            /*并且赋予操作员查看此表的权限*/
            PesUserMenuPermission pesUserMenuPermission = new PesUserMenuPermission();
            pesUserMenuPermission.setUserId(0L);
            pesUserMenuPermission.setCsNick(nick);
            pesUserMenuPermission.setMenuResourceId(menuResource.getId());
            pesUserMenuPermissionBusiness.insert(pesUserMenuPermission);
        }

    }

    @Override
    public List<ShopMenuHomeDTO> queryMenuByShopId(String shopId) {
        List<ShopMenuHomeDTO> shopMenuHomeList;
        shopMenuHomeList = shopMenuHomeBusiness.queryMenuByShopId(Long.parseLong(shopId), 1);
        // 1.从店铺菜单表中先取，为空的话，则从菜单表取默认10条，然后初始化到店铺菜单表
        if (CollectionUtils.isEmpty(shopMenuHomeList)) {
            String ids = homePageConfig.getIds();
            List<Long> list = Arrays.asList(ids.split(",")).stream()
                    .map(id -> Long.parseLong(id))
                    .collect(Collectors.toList());
            List<PesMenuResource> pesMenuResources = pesMenuResourceBusiness.queryMenuByIds(list);
            shopMenuHomeList = Lists.newArrayList();
            for (PesMenuResource pesMenuResource : pesMenuResources) {
                ShopMenuHomeDTO shopMenuHomeDTO = new ShopMenuHomeDTO();
                shopMenuHomeDTO.setShopId(Long.parseLong(shopId));
                shopMenuHomeDTO.setMenuId(pesMenuResource.getId());
                shopMenuHomeDTO.setCreateTime(new Date());
                shopMenuHomeDTO.setName(pesMenuResource.getName());
                shopMenuHomeDTO.setTitle(pesMenuResource.getTitle());
                shopMenuHomeDTO.setType(1);
                shopMenuHomeList.add(shopMenuHomeDTO);
            }
            // 入库
            shopMenuHomeBusiness.insertShopMenuHome(shopMenuHomeList);
        }

        return shopMenuHomeList;
    }

    @Override
    public List<ShopMenuHomeDTO> queryMenuByShopIdOfSimple(String shopId) {
        List<ShopMenuHomeDTO> shopMenuHomeList;
        shopMenuHomeList = shopMenuHomeBusiness.queryMenuByShopId(Long.parseLong(shopId), 2);
        // 1.从店铺菜单表中先取，为空的话，则从菜单表取默认10条，然后初始化到店铺菜单表
        if (CollectionUtils.isEmpty(shopMenuHomeList)) {
            String ids = homePageConfig.getSimpleIds();
            List<Long> list = Arrays.asList(ids.split(",")).stream()
                    .map(id -> Long.parseLong(id))
                    .collect(Collectors.toList());
            List<PesMenuResource> pesMenuResources = pesMenuResourceBusiness.queryMenuByIds(list);
            shopMenuHomeList = Lists.newArrayList();
            for (PesMenuResource pesMenuResource : pesMenuResources) {
                ShopMenuHomeDTO shopMenuHomeDTO = new ShopMenuHomeDTO();
                shopMenuHomeDTO.setShopId(Long.parseLong(shopId));
                shopMenuHomeDTO.setMenuId(pesMenuResource.getId());
                shopMenuHomeDTO.setCreateTime(new Date());
                shopMenuHomeDTO.setName(pesMenuResource.getName());
                shopMenuHomeDTO.setTitle(pesMenuResource.getTitle());
                shopMenuHomeDTO.setType(2);
                shopMenuHomeList.add(shopMenuHomeDTO);
            }
            // 入库
            shopMenuHomeBusiness.insertShopMenuHome(shopMenuHomeList);
        }

        return shopMenuHomeList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMenuByIds(String shopId, String ids) {
        // 先删除在添加
        shopMenuHomeBusiness.deleteShopMenuHome(shopId, 1);
        List<ShopMenuHomeDTO> shopMenuHomeList = Lists.newArrayList();
        List<ShopMenuHomeAscDTO> shopMenuHomeAscList= Lists.newArrayList();
        List<Long> list = Arrays.asList(ids.split(",")).stream()
                .map(id -> Long.parseLong(id))
                .collect(Collectors.toList());
        List<PesMenuResource> pesMenuResources = pesMenuResourceBusiness.queryMenuByIds(list);
        for (Long l : list) {
            for (PesMenuResource pesMenuResource : pesMenuResources) {
                if (l.equals(pesMenuResource.getId())) {
                    ShopMenuHomeAscDTO shopMenuHomeAscDTO = new ShopMenuHomeAscDTO();
                    shopMenuHomeAscDTO.setId(l);
                    shopMenuHomeAscDTO.setName(pesMenuResource.getName());
                    shopMenuHomeAscDTO.setTitle(pesMenuResource.getTitle());
                    shopMenuHomeAscList.add(shopMenuHomeAscDTO);
                }
            }
        }

        for (ShopMenuHomeAscDTO pesMenuResource : shopMenuHomeAscList) {
            ShopMenuHomeDTO shopMenuHomeDTO = new ShopMenuHomeDTO();
            shopMenuHomeDTO.setShopId(Long.parseLong(shopId));
            shopMenuHomeDTO.setMenuId(pesMenuResource.getId());
            shopMenuHomeDTO.setCreateTime(new Date());
            shopMenuHomeDTO.setName(pesMenuResource.getName());
            shopMenuHomeDTO.setTitle(pesMenuResource.getTitle());
            shopMenuHomeDTO.setType(1);
            shopMenuHomeList.add(shopMenuHomeDTO);
        }
        shopMenuHomeBusiness.insertShopMenuHome(shopMenuHomeList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMenuByIdsOfSimple(String shopId, String ids) {
        // 先删除在添加
        shopMenuHomeBusiness.deleteShopMenuHome(shopId, 2);
        List<ShopMenuHomeDTO> shopMenuHomeList = Lists.newArrayList();
        List<ShopMenuHomeAscDTO> shopMenuHomeAscList= Lists.newArrayList();
        List<Long> list = Arrays.asList(ids.split(",")).stream()
                .map(id -> Long.parseLong(id))
                .collect(Collectors.toList());
        List<PesMenuResource> pesMenuResources = pesMenuResourceBusiness.queryMenuByIds(list);
        for (Long l : list) {
            for (PesMenuResource pesMenuResource : pesMenuResources) {
                if (l.equals(pesMenuResource.getId())) {
                    ShopMenuHomeAscDTO shopMenuHomeAscDTO = new ShopMenuHomeAscDTO();
                    shopMenuHomeAscDTO.setId(l);
                    shopMenuHomeAscDTO.setName(pesMenuResource.getName());
                    shopMenuHomeAscDTO.setTitle(pesMenuResource.getTitle());
                    shopMenuHomeAscList.add(shopMenuHomeAscDTO);
                }
            }
        }

        for (ShopMenuHomeAscDTO pesMenuResource : shopMenuHomeAscList) {
            ShopMenuHomeDTO shopMenuHomeDTO = new ShopMenuHomeDTO();
            shopMenuHomeDTO.setShopId(Long.parseLong(shopId));
            shopMenuHomeDTO.setMenuId(pesMenuResource.getId());
            shopMenuHomeDTO.setCreateTime(new Date());
            shopMenuHomeDTO.setName(pesMenuResource.getName());
            shopMenuHomeDTO.setTitle(pesMenuResource.getTitle());
            shopMenuHomeDTO.setType(2);
            shopMenuHomeList.add(shopMenuHomeDTO);
        }
        shopMenuHomeBusiness.insertShopMenuHome(shopMenuHomeList);
    }
}
