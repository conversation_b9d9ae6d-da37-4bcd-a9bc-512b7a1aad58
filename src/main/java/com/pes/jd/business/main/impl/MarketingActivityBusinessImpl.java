package com.pes.jd.business.main.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.pes.jd.business.main.MarketingActivityBusiness;
import com.pes.jd.dao.main.MarketingActivityDao;
import com.pes.jd.model.DO.MarketingActivityDO;
import com.pes.jd.model.DTO.MarketingActivityDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 13:40 2019/10/29
 * @Description:
 */
@Service
public class MarketingActivityBusinessImpl implements MarketingActivityBusiness {

    @Autowired
    private MarketingActivityDao marketingActivityDao;

    @Override
    public int insert(String activityName, String activityContent,
                      Date startDate, Date endDate,
                      String version, String orderType,Integer shopType,String url) {
        MarketingActivityDO activity = new MarketingActivityDO();

        activity.setActivityName(activityName);
        activity.setActivityContent(activityContent);
        activity.setStartDate(startDate);
        activity.setEndDate(endDate);
        activity.setVersion(version);
        activity.setOrderType(orderType);
        activity.setCreateDate(DateUtil.date());
        activity.setEnableSwitch(false);
        activity.setType(shopType);
        activity.setUrl(url);
        return marketingActivityDao.insert(activity);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return marketingActivityDao.deleteByPrimaryKey(id);
    }

    @Override
    public int update(Long id, String activityName, String activityContent,
                      Date startDate, Date endDate,
                      String version, String orderType,String url) {

        MarketingActivityDO activity = new MarketingActivityDO();
        activity.setId(id);
        activity.setActivityName(activityName);
        activity.setActivityContent(activityContent);
        activity.setStartDate(startDate);
        activity.setEndDate(endDate);
        activity.setVersion(version);
        activity.setOrderType(orderType);
        activity.setUrl(url);
        return marketingActivityDao.updateByPrimaryKey(activity);
    }

    @Override
    public int updateEnable(Long id, Boolean enableSwitch) {

        MarketingActivityDO activity = new MarketingActivityDO();
        activity.setId(id);
        activity.setEnableSwitch(enableSwitch);
        return marketingActivityDao.updateByPrimaryKey(activity);
    }

    @Override
    public List<MarketingActivityDTO> selectEnableActivity(Integer shopType) {
        List<MarketingActivityDTO> marketingActivityDTOS = marketingActivityDao.selectEnableActivity(shopType);
        return marketingActivityDTOS.isEmpty() ? Lists.newArrayListWithExpectedSize(0) : marketingActivityDTOS;
    }

    @Override
    public List<MarketingActivityDTO> selectActivityByActivityNameAndDate(Date startDate, Date endDate, String activityName,Integer shopType) {
        List<MarketingActivityDTO> marketingActivityDTOS = marketingActivityDao.selectActivityByActivityNameAndDate(startDate, endDate, activityName,shopType);
        return marketingActivityDTOS.isEmpty() ? Lists.newArrayListWithExpectedSize(0) : marketingActivityDTOS;
    }

}
  
