package com.pes.jd.business.main;

import com.pes.jd.model.VO.FunctionModuleWhiteVO;
import com.pes.jd.model.VO.ShopVO;

import java.util.List;

/**
 * @program: ms-pes-jd
 * @description: 白名单业务接口
 * @author: ALan
 * @create: 2019-05-15 15:55
 */
public interface FunctionWhiteBusiness {

    List<FunctionModuleWhiteVO> searchFunctionWhiteLstByShopId(Long shopId);

    List<FunctionModuleWhiteVO> searchFunctionWhiteForLst(Integer type);

    List<FunctionModuleWhiteVO> searchFunctionWhiteAddShopForLst(Integer type);

    List<String> searchFunctionWhiteDisplayByShopId(Long shopId);

    List<ShopVO> selectAllNotWhiteShopForLst(String type);

    int insertFunctionModuleWhiteForLst(List<Long> shopIdLst, List<Long> functionIds);

    int batchDeleteFunctionModuleWhite(List<Long> fmwIds);


}
