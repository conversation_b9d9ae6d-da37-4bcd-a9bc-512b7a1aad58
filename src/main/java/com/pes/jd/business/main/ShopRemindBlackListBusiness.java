package com.pes.jd.business.main;

import com.pes.jd.model.DTO.ShopRemindBlackListDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ShopRemindBlackListBusiness {
    Set<String> batchInsert(Long shopId, String buyerNicks, String type, String userId) throws Exception;

    List<ShopRemindBlackListDTO> selectShopRemindBlackList(Long shopId, String buyerNick, Date startDate, Date endDate);

    void deleteShopRemindBlackListById(Long id);

    List<ShopRemindBlackListDTO> selectShopRemindBlacklistByShopId(Long shopId);

    List<String> selectManualMerchandisingBlacklistList(Long shopId);
}
