package com.pes.jd.business.main.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.business.main.CsManageBusiness;
import com.pes.jd.business.main.PesMenuResourceBusiness;
import com.pes.jd.business.main.ShopAccountBussiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.controller.PermissionSettingController;
import com.pes.jd.dao.main.*;
import com.pes.jd.data.api.SelfCsOperator;
import com.pes.jd.data.api.SubUserOperator;
import com.pes.jd.model.BO.ShopAccountBO;
import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.CsStatusEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.TO.SubUserGetTO;
import com.pes.jd.model.VO.PermissionSettingControlVo;
import com.pes.jd.util.CollectionUtil;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 客服相关 - 业务类
 * ClassName:CsManageBusinessImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 下午2:46:59 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@SuppressWarnings("Duplicates")
@Service
public class CsManageBusinessImpl implements CsManageBusiness {
    private Logger log = LoggerFactory.getLogger(CsManageBusinessImpl.class);
    @Resource
    private CsDao csDao;
    @Resource
    private ShopAccountDao shopAccountDao;
    @Resource
    private GroupDao groupDao;
    @Resource
    private GroupCsDao groupCsDao;
    @Resource
    private CsManagerDao csManagerDao;
    @Resource
    private SubUserOperator subUserOperator;
    @Resource
    private ShopAccountBussiness shopAccountBussiness;
    @Resource
    private ShopDao shopDao;
    @Resource
    private PesUserMenuPermissionDao pesUserMenuPermissionDao;
    @Resource
    private PesUserServicePermissionDao pesUserServicePermissionDao;
    @Resource
    private SelfCsOperator selfCsOperator;
    @Resource
    private PermissionSettingController permissionSettingController;
    @Resource
    private PesMenuResourceBusiness pesMenuResourceBusiness;
    @Resource
    private CsManageBusiness csManageBusiness;
    @Resource
    private RedisCache redisCache;
    private static String token = "FA87B8AEA3D38B8F1F98003A137BD66A4F8AD7DA256B46E9873591F09E60D280";

    private static final String selfType = "1";

    String cslstKey = CommonConstants.JOB_CSLST;

    @Override
    public int deleteShopAccountByNick(String nick) {
        return shopAccountDao.deleteShopAccountByNick(nick);
    }

    @Override
    public List<CsDTO> selectCsByShopId(Long shopId) {
        return csDao.selectCsByShopId(shopId);
    }

    @Override
    public List<ShopAccountDTO> selectShopAccountByShopIdByNickByStatus(Long shopId
            , String nick, Integer status, Integer flag, Boolean managerOverlay) {
        return shopAccountDao.selectShopAccountByShopIdByNickByStatus(shopId, nick, status, flag, managerOverlay);
    }

    @Override
    public List<ShopAccountDTO> searchNoPermission(Long shopId, String nick) {
        // 查询所有的子账号
        List<ShopAccountDTO> shopAccounts = shopAccountDao.selectShopAccountOfShopStatus(shopId, nick, false);
        if (CollectionUtils.isEmpty(shopAccounts)) {
            return Collections.emptyList();
        }
        // 查询有权限的nick
        List<PesUserMenuPermission> pesUserMenuPermissions =
                pesUserMenuPermissionDao.searchByNicks(shopAccounts.stream().map(ShopAccountDTO::getNick).collect(Collectors.toSet()));
//        List<PesUserServicePermission> pesUserServicePermissions = pesUserServicePermissionDao.selectByShopAccounts(shopAccounts);
//         有数据权限的nick
//        Set<String> dataNicks = pesUserServicePermissions.stream().map(PesUserServicePermission::getCsNick).collect(Collectors.toSet());
        // 有菜单权限的nick
        Set<String> nicks = pesUserMenuPermissions.stream().map(PesUserMenuPermission::getCsNick).collect(Collectors.toSet());
        return shopAccounts.stream().filter(k -> (!nicks.contains(k.getNick()))).collect(Collectors.toList());
    }

    @Override
    public List<ShopAccountDTO> searchHasPermission(Long shopId, String nick, boolean searchMainOrAdmin, boolean exactSearch) {
        // 查询所有的子账号
        List<ShopAccountDTO> shopAccounts = shopAccountDao.selectShopAccountOfShopStatus(shopId, nick, exactSearch);
        if (CollectionUtils.isEmpty(shopAccounts)) {
            return Collections.emptyList();
        }
        // 查询有权限的nick
        List<PesUserMenuPermission> pesUserMenuPermissions =
                pesUserMenuPermissionDao.searchByNicks(shopAccounts.stream().map(ShopAccountDTO::getNick).collect(Collectors.toSet()));
        Set<String> nicks = pesUserMenuPermissions.stream().map(PesUserMenuPermission::getCsNick).collect(Collectors.toSet());
        return shopAccounts.stream().filter(k ->
                nicks.contains(k.getNick()) || (searchMainOrAdmin && (k.getIsAccount() || Objects.equals(k.getRole(), "M")))).collect(Collectors.toList());
    }

    @Override
    public List<ShopAccountDTO> selectShopAccountByShopIdByNickByStatusForCsManager(Long shopId
            , String nick, Integer status) {
        return shopAccountDao.selectShopAccountByShopIdByNickByStatusForCsManager(shopId, nick, status);
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED)
    public void insertCS(Long shopId, String groupId, List<Cs> css) throws Exception {
        if (CollectionUtils.isNotEmpty(css)) {
            List<String> csNicks = new ArrayList<>(css.size());
            for (Cs cs : css) {
                cs.setNick(cs.getNick().toLowerCase());
                csNicks.add(cs.getNick());
            }
            csDao.batchInsertCs(css);
            batchInsertCsToGroup(shopId, groupId, csNicks);
            //删除缓存相关的数据,cs,方便额外新增客户后马上开始拉取数据的job
            redisCache.expire(cslstKey + shopId, 0, 0);
        }
        /************初始化的时候默认添加插件权限 start**************/
        try {
        	List<Cs> fincsList = new ArrayList<Cs>();
        	for(Cs cs:css) {
        		if(cs.getSource()!=null && cs.getSource().intValue()==0) {
        			fincsList.add(cs);
        		}
        	}
            doAddDefaultJurisdiction(fincsList, shopId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        /************初始化的时候默认添加插件权限 end**************/
    }

    /**
     * 更新多个客服到分组
     */
    @Override
    public int batchInsertCsToGroup(Long shopId, String groupIds, List<String> csNicks) throws Exception {
        if (CollectionUtils.isEmpty(csNicks) || StringUtils.isBlank(groupIds)) {
            return 0;
        }
        int row = 0;
        String groupIdArr[] = groupIds.split(",");
        for (String groupIdStr : groupIdArr) {
            Long gid = Long.valueOf(groupIdStr);
            List<GroupCs> cgcss = new ArrayList<>();
            // 查询这个组下的所有旺旺
            List<GroupCsDTO> shopGcss = groupCsDao.selectGroupCsByGroupId(gid);
            if (CollectionUtils.isNotEmpty(shopGcss)) {
                Set<String> existedGroupCsIdSet = new HashSet<>(shopGcss.size());
                for (GroupCsDTO gcs : shopGcss) {
                    existedGroupCsIdSet.add(gcs.getNick());
                }
                for (String csNick : csNicks) {
                    if (!existedGroupCsIdSet.contains(csNick)) {
                        GroupCs gcs = new GroupCs();
                        gcs.setNick(csNick);
                        gcs.setGroupId(gid);
                        gcs.setShopId(shopId);
                        cgcss.add(gcs);
                    }
                }
            } else {
                for (String csNick : csNicks) {
                    GroupCs gcs = new GroupCs();
                    gcs.setNick(csNick);
                    gcs.setGroupId(gid);
                    gcs.setShopId(shopId);
                    cgcss.add(gcs);
                }
            }
            row += groupCsDao.batchInsertGroupcs(cgcss);
        }

        return row;
    }


    @Override
    public Map<String, Object> saveShopCs(Long shopId, String groupId, String nickArrStr, String type)
            throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<Cs> css = null;
        List<ShopAccount> shopAccountLst = shopAccountDao.selectShopAccountOfShop(String.valueOf(shopId));
        Map<String, String> shopAccountMap = null;
        Map<String, Integer> shopAccountSourceMap = new HashMap<String, Integer>();
        if (CollectionUtils.isNotEmpty(shopAccountLst)) {
            for (ShopAccount shopAccount : shopAccountLst) {
                shopAccount.setNick(shopAccount.getNick().toLowerCase());
                shopAccountSourceMap.put(shopAccount.getNick(), shopAccount.getSource());
            }
            shopAccountMap = shopAccountLst.stream().collect(Collectors.toMap(ShopAccount::getNick, ShopAccount::getUserName, (value1, value2) -> {
                return value2;
            }));
        }
        if (StringUtils.isNotBlank(nickArrStr)) {
            List<String> csNickLsts = Arrays.asList(nickArrStr.split(","));
            Set<String> nickSet = Sets.newHashSet();
            for (String csNick : csNickLsts) {
                nickSet.add(csNick.toLowerCase());
            }
            List<String> nickList = Lists.newArrayList();
            // 查询已经设置了的客服
            List<CsDTO> existCsLst = csDao.selectCsByShopIdByNickByType(shopId, "", null);
            Set<String> existCsSet = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(existCsLst)) {
                for (CsDTO cs : existCsLst) {
                    existCsSet.add(cs.getNick().toLowerCase());
                }
            }
            for (String nick : nickSet) {
                if (!existCsSet.contains(nick)) {
                    nickList.add(nick);
                }
            }
            css = makeCsNickNew(nickList, shopId, type, shopAccountMap, shopAccountSourceMap);
            insertCS(shopId, groupId, css);
            //初始化的时候默认添加插件权限
            /************初始化的时候默认添加插件权限 start**************/
            try {
            	List<Cs> fincsList = new ArrayList<Cs>();
            	for(Cs cs:css) {
            		if(cs.getSource()!=null && cs.getSource().intValue()==0) {
            			fincsList.add(cs);
            		}
            	}
                doAddDefaultJurisdiction(fincsList, shopId);
            } catch (Exception e) {
                e.printStackTrace();
            }
            /************初始化的时候默认添加插件权限 end**************/
        }
        result.put("css", css);
        return result;
    }

    private void doAddDefaultJurisdiction(List<Cs> css, Long shopId) {
//	    {"type":0,"shopAccounts":[{"nick":"武极商务-小沆"}],"permissions":[{"id":1}],"menuResources":[{"id":239},{"id":241},{"id":242},{"id":395}]}
        /*
        a       "我的绩效(插件)"
        b       "绩效明细(插件)"
        c       "一键提醒(插件)"
        d       "顾客信息(插件)"
        需求初始化完成添加这些权限
         */
        long a = pesMenuResourceBusiness.selectSystemJurisdicteIdByTitle(CommonConstants.MYPERFORMANCE_PLUGIN_STR, -1);
        long b = pesMenuResourceBusiness.selectSystemJurisdicteIdByTitle(CommonConstants.PERFORMANCE_SUBSIDIARY_PLUGIN_STR, -1);
        long c = pesMenuResourceBusiness.selectSystemJurisdicteIdByTitle(CommonConstants.AKEY_REMIND_PLUGIN_STR, -1);
        long d = pesMenuResourceBusiness.selectSystemJurisdicteIdByTitle(CommonConstants.CUSTOMER_INFORMATION_PLUGIN_STR, -1);
        PermissionSettingControlVo permissionSettingControlVo = new PermissionSettingControlVo();
        List<ShopAccountDTO> shopAccounts = new ArrayList<>();
        List<PesServicePermission> permissions = new ArrayList<>();
        List<PesMenuResource> menuResource = new ArrayList<>();
        permissionSettingControlVo.setShopAccounts(shopAccounts);
        permissionSettingControlVo.setPermissions(permissions);
        permissionSettingControlVo.setMenuResources(menuResource);
        for (Cs cs : css) {
            String nick = cs.getNick();
            ShopAccountDTO shopAccountDTO = new ShopAccountDTO();
            shopAccountDTO.setNick(nick);
            shopAccounts.add(shopAccountDTO);

            PesServicePermission pesServicePermission = new PesServicePermission();
            pesServicePermission.setId(1L);//1-个人数据权限
            permissions.add(pesServicePermission);

            PesMenuResource pesMenuResource = new PesMenuResource();
            pesMenuResource.setId(a);
            PesMenuResource pesMenuResource1 = new PesMenuResource();
            pesMenuResource1.setId(b);
            PesMenuResource pesMenuResource2 = new PesMenuResource();
            pesMenuResource2.setId(c);
            PesMenuResource pesMenuResource3 = new PesMenuResource();
            pesMenuResource3.setId(d);
            menuResource.add(pesMenuResource);
            menuResource.add(pesMenuResource1);
            menuResource.add(pesMenuResource2);
            menuResource.add(pesMenuResource3);
        }
        permissionSettingController.insertOrUpdatePermission(0, permissionSettingControlVo, shopId);
    }

    @Override
    public ApiResponse batchUpdateShopCs(Long shopId, String groupId, String nickArrStr, String type, Integer operateType, String selfOptType) throws Exception {
        Map<String, Object> result = Maps.newHashMap();
        //该店铺下的子账号
        List<ShopAccount> shopAccountLst = shopAccountDao.selectShopAccountOfShop(String.valueOf(shopId));
        Set<String> shopCsNickSet = Sets.newHashSet();
        // 传过来的nick存在shopAccount中
        Map<String, String> shopAccountMap = null;

        Map<String, Integer> shopAccountSourceMap = new HashMap<String, Integer>();
        if (CollectionUtils.isNotEmpty(shopAccountLst)) {
            for (ShopAccount account : shopAccountLst) {
                account.setNick(account.getNick().toLowerCase());
                shopAccountSourceMap.put(account.getNick(), account.getSource());
            }
            shopAccountMap = shopAccountLst.stream().collect(Collectors.toMap(ShopAccount::getNick, ShopAccount::getUserName, (value1, value2) -> {
                return value2;
            }));
            for (ShopAccount subUser : shopAccountLst) {
                shopCsNickSet.add(subUser.getNick());
            }
        }
        List<String> successLst = Lists.newArrayList();
        List<String> existShopAccountLst = Lists.newArrayList();
        List<String> noExistShopAccountLst = Lists.newArrayList();
        List<String> notSetAccountLst = Lists.newArrayList();
        List<String> lockAccount = Lists.newArrayList();
        Set<String> allowlockAccountSet = Sets.newHashSet();
        Set<String> repeatCsLst = Sets.newHashSet();

        List<String> csNickLsts = Arrays.asList(nickArrStr.split(","));
        Set<String> nickSet = Sets.newHashSet();
        for (String csNick : csNickLsts) {
            nickSet.add(csNick);
        }

        int totalCount = nickSet.size();
        result.put("totalCount", totalCount);
        boolean faileFlag = false;
        int failNum = 0;


        for (String nick : nickSet) {
            nick = nick.toLowerCase();
            // 不存在客服验证

//			增加自营非自营判断  自营无需判断添加的时候 无需判断子账户包含对应的 nick
            if (selfType.equals(selfOptType)) {
//				自营判断
                //直接添加之后  会出现取
                existShopAccountLst.add(nick);
            } else {
                if (!shopCsNickSet.contains(nick)) {
                    faileFlag = true;
                    failNum++;
                    noExistShopAccountLst.add(nick);
                } else {
                    existShopAccountLst.add(nick);
                }
            }


        }
        //查询已经设置了的客服
        List<CsDTO> setCsLst = csDao.selectCsByShopIdByNickByType(shopId, "", null);

        Map<String, Integer> setCsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(setCsLst)) {
            for (CsDTO cs : setCsLst) {
                cs.setNick(cs.getNick().toLowerCase());
                allowlockAccountSet.add(cs.getNick());
            }
            setCsMap.putAll(setCsLst.stream().collect(Collectors.toMap(CsDTO::getNick, s -> s.getCsStatus())));
        }

        //批量添加
        if (operateType == 1) {
            List<Cs> css = null;
            //剔除已经设置了的客服以免重复添加
            if (CollectionUtils.isNotEmpty(setCsLst)) {
                for (String nick : existShopAccountLst) {
                    if (setCsMap.containsKey(nick)) {
                        Integer csStatus = setCsMap.get(nick);
                        if (CsStatusEnum.LOCK.getType().equals(csStatus + "")) {
                            // 账号已锁定失败
                            faileFlag = true;
                            failNum++;
                            lockAccount.add(nick);
                        } else {
                            // 重复添加失败
                            faileFlag = true;
                            failNum++;
                            repeatCsLst.add(nick);
                        }

                    } else {
                        successLst.add(nick);
                    }
                }

            } else {
                successLst.addAll(existShopAccountLst);
            }
            css = makeCsNickNew(successLst, shopId, type, shopAccountMap, shopAccountSourceMap);
            insertCS(shopId, groupId, css);

        } else {
            //批量锁定
            //添加了客服
            if (CollectionUtils.isNotEmpty(setCsLst)) {
                for (String nick : existShopAccountLst) {
                    //锁定的客服未添加
                    if (!setCsMap.containsKey(nick)) {
                        faileFlag = true;
                        notSetAccountLst.add(nick);
                        failNum++;
                    } else {
                        Integer csStatus = setCsMap.get(nick);
                        if (CsStatusEnum.LOCK.getType().equals(csStatus + "")) {
                            //账号重复锁定-失败
                            faileFlag = true;
                            failNum++;
                            lockAccount.add(nick);
                        } else {
                            successLst.add(nick);
                        }
                    }
                }
            } else {
                //未添加任何客服时
                faileFlag = true;
                notSetAccountLst.addAll(existShopAccountLst);
                failNum++;
            }
            List<Cs> csLst = csDao.selectCsByNickLst(successLst);
            for (Cs cs : csLst) {
                cs.setCsStatus(Integer.valueOf(CsStatusEnum.LOCK.getType()));
                cs.setModifiedDate(new Date());
                cs.setLockTime(new Date());
            }
            csDao.updateCsOfStatusByNickLst(csLst);
        }
        int successCount = totalCount - failNum;
        result.put("successCount", successCount);//成功数量
        result.put("failFlag", faileFlag);//true 表示存在失败
        result.put("failCount", failNum);//失败数量
        result.put("lockAccount", String.join(",", lockAccount));//添加时-已锁定账号，锁定时-重复锁定账号
        result.put("successAccount", String.join(",", successLst));//成功的账号
        result.put("notExistAccount", String.join(",", noExistShopAccountLst));//不存在的账号
        result.put("allowlockAccount", String.join(",", allowlockAccountSet));//锁定-已设置的账号
        result.put("notSetAccount", String.join(",", notSetAccountLst));//锁定-未设置账号
        result.put("repeatCsLst", String.join(",", repeatCsLst));//添加-重复添加账号
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, result);
    }

    private List<Cs> makeCsNick(List<String> nickLst, Long shopId, String type, Map<String, String> shopAccountMap) {

        List<Cs> css = Lists.newArrayList();
        for (String nick : nickLst) {
            Cs cs = new Cs();
            cs.setNick(nick.toLowerCase());
            cs.setShopId(shopId);
            if (shopAccountMap != null && StringUtils.isNotBlank(shopAccountMap.get(nick))) {
                cs.setSimpleName(shopAccountMap.get(nick));
            } else {
                cs.setSimpleName(nick);
            }
            cs.setSource(new Integer("0"));
            cs.setType(Integer.valueOf(type));
            cs.setCsStatus(Integer.valueOf(CsStatusEnum.NORMAL.getType()));
            cs.setModifiedDate(new Date());


            css.add(cs);
        }
        return css;
    }


    /**
     * @param nickLst
     * @param shopId
     * @param type
     * @param shopAccountMap
     * @param shopAccountSourceMap
     * @return
     */
    private List<Cs> makeCsNickNew(List<String> nickLst, Long shopId, String type, Map<String, String> shopAccountMap, Map<String, Integer> shopAccountSourceMap) {
        //自营客服添加 需要增加来源  shop_account中 source字段为准


        List<Cs> css = Lists.newArrayList();
        for (String nick : nickLst) {
            Cs cs = new Cs();
            cs.setNick(nick.toLowerCase());
            cs.setShopId(shopId);
            if (shopAccountMap != null && StringUtils.isNotBlank(shopAccountMap.get(nick))) {
                cs.setSimpleName(shopAccountMap.get(nick));
            } else {
                cs.setSimpleName(nick);
            }

            //增加source 判断 如果是 原有的 查询子账户接口  为商家后台，如果是 供应商为咚咚，否则为其他
            if (shopAccountMap != null && shopAccountMap.containsKey(nick)) {
                if (shopAccountSourceMap.containsKey(nick)) {
                    if (null != shopAccountSourceMap.get(nick) && shopAccountSourceMap.get(nick).intValue() == 1) {
                        //咚咚 供应商接口增加
                        cs.setSource(new Integer("2"));
                    } else {
                        cs.setSource(new Integer("0"));
                    }
                } else {
                    cs.setSource(new Integer("0"));
                }


            } else {
                cs.setSource(new Integer("1"));
            }

            cs.setType(Integer.valueOf(type));
            cs.setCsStatus(Integer.valueOf(CsStatusEnum.NORMAL.getType()));
            cs.setModifiedDate(new Date());


            css.add(cs);
        }
        return css;
    }


    private List<Cs> makeCsNickSelf(List<String> nickLst, Long shopId, String type, Map<String, String> shopAccountMap) {

        List<Cs> css = Lists.newArrayList();
        for (String nick : nickLst) {
            Cs cs = new Cs();
            cs.setNick(nick.toLowerCase());
            cs.setShopId(shopId);
            if (shopAccountMap != null && StringUtils.isNotBlank(shopAccountMap.get(nick))) {
                cs.setSimpleName(shopAccountMap.get(nick));
            } else {
                cs.setSimpleName(nick);
            }

            if (shopAccountMap != null && shopAccountMap.containsKey(nick)) {
                cs.setSource(new Integer("0"));
            } else {
                cs.setSource(new Integer("1"));
            }

            cs.setType(Integer.valueOf(type));
            cs.setCsStatus(Integer.valueOf(CsStatusEnum.NORMAL.getType()));
            cs.setModifiedDate(new Date());


            css.add(cs);
        }
        return css;
    }

    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED)
    public void updateCs(Long shopId, String groupIds, String nick, String simpleName, Integer type) throws Exception {
        groupCsDao.deleteGroupCsByNickByShopId(shopId, nick);
        String groupArr[] = groupIds.split(",");
        List<GroupCs> groupLst = Lists.newArrayList();
        GroupCs group = null;
        for (int i = 0; i < groupArr.length; i++) {
            group = new GroupCs();
            group.setGroupId(Long.valueOf(groupArr[i]));
            group.setNick(nick);
            group.setShopId(shopId);
            groupLst.add(group);
        }
        groupCsDao.batchInsertGroupcs(groupLst);
        csDao.updateCsByCsNickByshopId(shopId, nick, simpleName, type);
    }

    @Override
    public List<CsDTO> selectShopCs(String shopId, String groupId, String nick, String operateType, Integer type) throws Exception {
        List<CsDTO> csLst = csManagerDao.selectGroupCsByShopIdByGroupIdByNick(shopId, groupId, nick, operateType, type);
        if (CollectionUtils.isEmpty(csLst)) {
            return new ArrayList<CsDTO>(0);
        }
        List<GroupCsDTO> groupCsLst = csManagerDao.selectShopGroupCsByShopIdByGroupIdByNick(shopId, groupId, nick);
        Map<String, List<GroupCsDTO>> csNickGroupCsMap = null;
        if (CollectionUtils.isNotEmpty(groupCsLst)) {
            csNickGroupCsMap = groupCsLst.stream().collect(Collectors.groupingBy(GroupCsDTO::getNick));
        } else {
            csNickGroupCsMap = Maps.newHashMap();
        }
        for (CsDTO cs : csLst) {
            if (csNickGroupCsMap.get(cs.getNick()) != null) {
                List<GroupCsDTO> groupCsNickLst = csNickGroupCsMap.get(cs.getNick());
                StringBuilder csGroupNameSb = new StringBuilder();
                StringBuilder csGroupIdSb = new StringBuilder();
                for (int i = 0; i < groupCsNickLst.size(); i++) {
                    if (i == 0) {
                        csGroupNameSb.append(groupCsNickLst.get(i).getGroupName());
                        csGroupIdSb.append(groupCsNickLst.get(i).getGroupId());
                    } else {
                        csGroupNameSb.append("," + groupCsNickLst.get(i).getGroupName());
                        csGroupIdSb.append("," + groupCsNickLst.get(i).getGroupId());
                    }
                }
                cs.setGroupId(csGroupIdSb.toString());
                cs.setGroupName(csGroupNameSb.toString());
            }
        }
        return csLst;
    }

    @Override
    public void saveShopAccountInfoOfShop(String sessionKey, String sellerId, String sellerNick, String sellerShowNick, Long shopId) throws Exception {
        long t1 = System.currentTimeMillis();
        int totalAccount = subUserOperator
                .getVenderSubUsersOne(Long.valueOf(sellerId), shopId, sessionKey);
        long t2 = System.currentTimeMillis();
        log.info("accountNum -- {},first refresh time is -- {}",totalAccount,t2-t1);
        List<ShopAccount> subUsers = Lists.newArrayList();
        //子账号总数量大于2000时 使用多线程
        if(totalAccount<2000){
            subUsers = subUserOperator
                    .getVenderSubUsers(Long.valueOf(sellerId), shopId, sessionKey).getCustSubUsers();
            if (subUsers == null) {
                subUsers = new ArrayList<>(1);
            }
        }else{
            List<Integer> list = Lists.newArrayList();
            for (int i = 1; i < 501; i++) {
                list.add(i);
            }
            List<List<Integer>> lists = CollectionUtil.avgAssignLst(list, 10);
            final int poolSize = lists.size();
            ExecutorService executorService = new ThreadPoolExecutor(poolSize, poolSize, 0L,
                    TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(10));
            ExecutorCompletionService<SubUserGetTO> completionService = new ExecutorCompletionService<SubUserGetTO>(executorService);

            for (List<Integer> integers : lists) {
                ShopAccountBO shopAccountBo = new ShopAccountBO(integers, sessionKey, Long.valueOf(sellerId), shopId);
                completionService.submit(shopAccountBo);
            }
            try{
                for (int i = 0; i < 10; i++) {
                    List<ShopAccount> custSubUsers = completionService.take().get().getCustSubUsers();
                    for (ShopAccount custSubUser : custSubUsers) {
                        subUsers.add(custSubUser);
                    }
                }
            }catch(Exception e){
                executorService.shutdown();
                log.info("start saveShopAccount Tasks error {}",e);
            }finally{
                executorService.shutdown();
            }

        }
        ShopAccount manager = new ShopAccount();
        manager.setSellerId(Long.valueOf(sellerId));
        manager.setNick(sellerNick.toLowerCase());
        manager.setStatus(CommonConstants.SUB_USER_STATUS_NORMAL);
        manager.setShopId(shopId);
        manager.setRole(CommonConstants.SUB_USER_TYPE_MANAGE);
        manager.setUserName(sellerShowNick);
        manager.setIsAccount(true);
        manager.setCreated(new Date());
        manager.setSource(0);
        subUsers.add(manager);
        shopAccountBussiness.batchInsertSubUsersOfShop(subUsers, String.valueOf(shopId));
    }


    @Override
    @Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED)
    public void assignDefaultGroup(Long shopId, String csNick) {
        GroupDTO group = groupDao.getDefaulutGroupByshopId(shopId);
        ShopDTO shop = shopDao.selectShopByShopId(shopId);
        // 如果没有默认分组的店铺就新建默认分组
        if (group == null) {
            Group cg = new Group();
            cg.setShopId(shopId);
            cg.setGroupName("默认分组");
            cg.setIsDefault(true);// 0为不是默认的，1 true为默认的
            cg.setCreated(new Date());
            groupDao.insertGroup(cg);
            shop.setDefaultGroupId(cg.getGroupId());
        } else {
            shop.setDefaultGroupId(group.getGroupId());
        }

        // 查询主账号下的所有店铺的客服
        List<CsDTO> css = csDao.selectCsByShopIdByNickByType(shopId, "", null);
        // 查询主账号下的所有店铺的所有组的旺旺
        List<GroupCsDTO> shopGroupCss = groupCsDao.selectGroupCsByShopId(shopId);
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(shopGroupCss)) {
            Set<String> groupCsIdSet = new HashSet<>();

            for (GroupCsDTO cg : shopGroupCss) {
                String groupcsNick = cg.getNick().toLowerCase();
                groupCsIdSet.add(groupcsNick);
                // 查看登录用户是否有组
                if (cg.getNick().equals(csNick)) {
                    flag = true;
                }
            }
            // 过滤掉没有分组的旺旺，全部塞到默认分组里
            List<CsDTO> noGroupCsIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(css)) {
                for (CsDTO cs : css) {
                    if (!groupCsIdSet.contains(cs.getNick().toLowerCase())) {
                        noGroupCsIdList.add(cs);
                    }
                }
            }

            List<GroupCs> groupcsLst = Lists.newArrayList();
            GroupCs gcs = null;
            if (shop.getDefaultGroupId() != null && CollectionUtils.isNotEmpty(noGroupCsIdList)) {
                // 店铺下如果有没有分组的客服旺旺就分配到默认分组里
                for (CsDTO cgs : noGroupCsIdList) {
                    gcs = new GroupCs();
                    gcs.setShopId(cgs.getShopId());
                    gcs.setNick(cgs.getNick());
                    gcs.setGroupId(shop.getDefaultGroupId());
                    groupcsLst.add(gcs);
                }
                groupCsDao.batchInsertGroupcs(groupcsLst);
            }

        }
        // 用户登录，如果没有分组,分配默认分组
        if (!flag) {
            GroupCs dgcs = new GroupCs();
            dgcs.setShopId(shopId);
            dgcs.setNick(csNick);
            dgcs.setGroupId(shop.getDefaultGroupId());
            groupCsDao.insertGroupCs(dgcs);
        }
    }

    @Override
    public List<CsDTO> selectShopCswwSimpleNames(List<ShopCommonParam> shopList, Integer type) {

        List<CsDTO> csSimpleNames = csDao.searchCsByshopIdLstAndType(shopList, type);
        if (CollectionUtils.isNotEmpty(csSimpleNames)) {
            for (CsDTO cs : csSimpleNames) {
                if (cs.getCsSimpleNick() == null) {
                    cs.setCsSimpleNick(cs.getNick());
                }
            }
        }
        return csSimpleNames;
    }

    @Override
    public void lockCs(Long shopId, String csNickStr, String operateType) {
        List<String> csNickLst = Arrays.asList(csNickStr.split(","));
        String csStatus = "";
        // 1:锁定 2：解锁
        if (operateType.equals(CsStatusEnum.NORMAL.getType())) {
            csStatus = CsStatusEnum.LOCK.getType();
        } else {
            csStatus = CsStatusEnum.NORMAL.getType();
        }
        List<Cs> csLst = Lists.newArrayList();
        Cs csM = null;
        for (String csNick : csNickLst) {
            csM = new Cs();
            csM.setNick(csNick);
            csM.setCsStatus(Integer.valueOf(csStatus));
            if (operateType.equals(CsStatusEnum.NORMAL.getType())) {
                csM.setLockTime(new Date());
            } else {
                csM.setLockTime(null);
            }
            csM.setModifiedDate(new Date());
            csM.setShopId(shopId);
            csLst.add(csM);
        }
        csDao.updateCsOfStatusByNickLst(csLst);
    }

    @Override
    public List<CsDTO> selectCsByShopIdByTypeByCsStatus(Long shopId, Integer type, Integer csStatus) {
        return csDao.selectCsByShopIdByTypeByCsStatus(shopId, type, csStatus);
    }

    @Override
    public int selectCsCountByShopIdByType(Long shopId, Integer type) {

        return csDao.selectCsCountByShopIdByType(shopId, type);
    }

    @Override
    public void batchUpdateCsofGroup(Long shopId, String groupId, String csNicks) {
        List<String> csNickLst = Arrays.asList(csNicks.split(","));
        List<String> groupLst = Arrays.asList(groupId.split(","));

        //先删在增
        groupCsDao.deleteGroupCsByCsNickLst(shopId, csNickLst);
        List<GroupCs> gcsLst = Lists.newArrayList();

        for (String csNick : csNickLst) {
            for (String gid : groupLst) {
                GroupCs gc = new GroupCs();
                gc.setShopId(shopId);
                gc.setGroupId(Long.valueOf(gid));
                gc.setNick(csNick);
                gcsLst.add(gc);
            }

        }
        groupCsDao.batchInsertGroupcs(gcsLst);
    }

    @Override
    public int selectSampleSimpleNickBySimpleName(Long shopId, String simpleName, String csNick) {
        return csDao.selectSampleSimpleNickBySimpleName(shopId, simpleName, csNick);
    }

    @Override
    public void saveShopAccountInfoOfShopSelf(String sessionKey, String sellerId, String sellerNick,
                                              String sellerShowNick, Long shopId) throws Exception {

//		原有接口列表获取子账户
        List<ShopAccount> subUsers = subUserOperator
                .getVenderSubUsers(Long.valueOf(sellerId), shopId, sessionKey).getCustSubUsers();
        if (subUsers == null) {
            subUsers = new ArrayList<>(1);
        }


        Map<String, String> nickMap = new HashMap<String, String>();
        if (CollectionUtils.isNotEmpty(subUsers)) {
            nickMap = subUsers.stream().collect(Collectors.toMap(ShopAccount::getNick, ShopAccount::getNick));
        }


        //供应商客服列表
        Set<ShopAccount> accSet = new HashSet<ShopAccount>();
        try {
            //获取code
            //List<String> codeList = selfCsOperator.getCodeList(sessionKey);
            List<String> codeList =  new ArrayList<>();
            if (null != codeList && codeList.size() > 0) {
                //获取token
                //String tokenString = selfCsOperator.getToken(sessionKey);
                String tokenString = "1111";
                if (StringUtils.isBlank(tokenString)) {
                    tokenString = new String(token);
                }

                for (String code : codeList) {
                    List<ShopAccount> subUsersSelf = selfCsOperator.getVenderSubUsersSelf(Long.valueOf(sellerId), shopId, sessionKey,
                            tokenString, code).getCustSubUsers();
                    accSet.addAll(subUsersSelf);
                }
            }

        } catch (Exception e) {
            log.info("获取自营供应商账户失败", e);

        }

        //手动添加 ShopAccount
        for (ShopAccount shopAccount : accSet) {
            if (StringUtils.isNotBlank(shopAccount.getNick()) && !nickMap.containsKey(shopAccount.getNick())) {
                subUsers.add(shopAccount);
            }
        }


//		List<ShopAccount> resList = new ArrayList<ShopAccount>(accSet);

        ShopAccount manager = new ShopAccount();
        manager.setSellerId(Long.valueOf(sellerId));
        manager.setNick(sellerNick.toLowerCase());
        manager.setStatus(CommonConstants.SUB_USER_STATUS_NORMAL);
        manager.setShopId(shopId);
        manager.setRole(CommonConstants.SUB_USER_TYPE_MANAGE);
        manager.setUserName(sellerShowNick);
        manager.setIsAccount(true);
        manager.setCreated(new Date());
        manager.setSource(0);
        subUsers.add(manager);


        shopAccountBussiness.batchInsertSubUsersOfShop(subUsers, String.valueOf(shopId));

    }

    @Override
    public Integer selectCsCountByShopIdByCsStatus(Long shopId, Integer status) {
        return csDao.selectCsCountByShopIdByCsStatus(shopId, status);
    }
    
    
    @Override
    public List<Cs> selectShopCsLists(Long shopId, String csNicks) {
        return csDao.selectShopCsLists(shopId, csNicks);
    }

    @Override
    public List<CsDTO> searchCsLstByShopIdAndType(Long shopId, int csType){
        return csDao.selectCsLstByShopIdAndType(shopId, csType);
    }

}
  
