package com.pes.jd.business.main;

import com.pes.jd.model.DTO.ShopDBRelationDTO;

import java.util.List;

/**
 * @Author: aiJun
 * @Date: 2019-05-15 18:56
 * @Version 1.0
 */
public interface ShopDBRelationBusiness {

    List<ShopDBRelationDTO> searchShopDBRelationByType(int type);

    List<ShopDBRelationDTO> searchShopDBNameAndSchemaIdByType(int type);

    List<ShopDBRelationDTO> searchShopDBRelationByDbNameAndSchemaIdAndType(String dbName, String schemaId, int type);
}
