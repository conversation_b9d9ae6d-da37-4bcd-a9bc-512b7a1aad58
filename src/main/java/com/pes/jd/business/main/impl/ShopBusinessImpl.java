package com.pes.jd.business.main.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.business.main.*;
import com.pes.jd.dao.main.*;
import com.pes.jd.model.BO.ShopDbAndSchemeIdBO;
import com.pes.jd.model.DO.Shop;
import com.pes.jd.model.DO.ShopDetail;
import com.pes.jd.model.DO.ShopSmsWordDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.JdSystemPageParam;
import com.pes.jd.model.Result.JobShopResult;
import com.pes.jd.model.VO.AdminShopVO;
import com.pes.jd.model.VO.ShopVO;
import com.pes.jd.ms.domain.Data.master.*;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Data.task.dispatching.ShopLocationInfo;
import com.yiyitech.support.util.IDUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 店铺信息
 * ClassName:ShopBusinessImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月22日 下午1:25:34 <br/>
 * <AUTHOR>
 * @version
 * @since    JDK 1.8
 * @see
 */
@Service("shopBusiness")
public class ShopBusinessImpl implements ShopBusiness {

	@Resource
	private ShopDao shopDao;
	@Resource
	private ShopDetailDao shopDetailDao;
	@Resource
	private RealTimeShopDao realTimeShopDao;
	@Resource
	private KeywordSensitiveDao keywordSensitiveDao;
	@Resource
	private ShopSensitiveWordDao shopSensitiveWordDao;
	@Resource
	private ShopSystemsettingBusiness shopSystemsettingBusiness;
	@Resource
	private CsManageBusiness csManageBusiness;
	@Resource
	private ShopAccountBussiness shopAccountBussiness;
	@Resource
	private ShopSettingBatchRemindBusiness shopSettingBatchRemindBusiness;
	@Resource
	private ShopAutoAllocatedSettingBussiness shopAutoAllocatedSettingBussiness;
	@Resource
	private ShopSmsSettingBussiness shopSmsSettingBussiness;
	@Resource
	private CsDao csDao;
	@Resource
	private ShopUrgeDao shopUrgeDao;
	@Resource
	private InterfaceControlBusiness interfaceControlBusiness;
	@Resource
	private SmsManagerBusiness smsManagerBusiness;
	@Resource
	private ShopRemindBlackListBusiness shopRemindBlackListBusiness;
	@Override
	public ShopDTO selectShopByUserNick(String userNick) {
		return shopDao.selectShopByUserNick(userNick);
	}
	@Override
	public int insertShop(ShopDTO shopDTO) {
//		ShopDTO sh=	shopDao.selectShopByShopId(shopDTO.getShopId());
//		if(sh!=null){
//			shopDao.deleteShopByShopId(shopDTO.getShopId());
//		}
		Shop shop = new Shop();
		shop.setSellerNick(shopDTO.getSellerNick());
		shop.setUserId(shopDTO.getUserId());
		shop.setShopId(shopDTO.getShopId());
		shop.setTitle(shopDTO.getTitle());
		shop.setSessionKey(shopDTO.getSessionKey());
		shop.setStatus(shopDTO.getStatus());
		shop.setSubuserNum(shopDTO.getSubuserNum());
		shop.setRealtimeSwitch(shopDTO.getRealtimeSwitch());
		shop.setFetchFlag(0);
		shop.setPreviousGetDataTime(shopDTO.getPreviousGetDataTime());
		shop.setInitDataFlag(shopDTO.getInitDataFlag());
		shop.setSchemaId(shopDTO.getSchemaId());
		shop.setCreateTableFlag(0);// 默认为0 未初始化表
		shop.setColType(shopDTO.getColType());
		shop.setDb(shopDTO.getDb());
		shop.setRtSchemaId(shopDTO.getRtSchemaId());
		shop.setRtDb(shopDTO.getRtDb());
		shop.setVenderId(shopDTO.getVenderId());
		shop.setSubscribeDeadLine(shopDTO.getSubscribeDeadLine());
		shop.setType(shopDTO.getType());
		shop.setRefreshSessionKey(shopDTO.getRefreskSessionKey());
		long id = IDUtil.getId(1L, 1L);
		shop.setId(id);
		shopDTO.setId(id);
		return shopDao.insertShop(shop);
	}
	@Override
	public int updateShop(Shop shop) {
		return shopDao.updateShopById(shop);
	}

    @Override
    public List<ShopVO> searchAllShop() {
        return shopDao.searchAllShop();
    }

    @Override
	public int updateShopDetailByShopId(ShopDetail shopDetail) {

		return shopDetailDao.updateShopDetailByShopId(shopDetail);
	}

	@Override
	public ShopDTO selectShopByShopId(Long shopId) {
		return shopDao.selectShopByShopId(shopId);
	}

    @Override
    public ShopMsgDataDTO getShopInfoForVenderId(Long venderId) {
		ShopDTO shopDTO = shopDao.getShopInfoForVenderId(venderId);
		return convertShopData(shopDTO);
	}



    public ShopMsgDataDTO convertShopData(ShopDTO shopDTO){
		ShopMsgDataDTO shopMsgDataDTO = null;
		if(null != shopDTO){
			shopMsgDataDTO = new ShopMsgDataDTO();
			shopMsgDataDTO.setShopId(shopDTO.getShopId());
			shopMsgDataDTO.setUserId(shopDTO.getUserId());
			shopMsgDataDTO.setSellerNick(shopDTO.getSellerNick());
			shopMsgDataDTO.setTitle(shopDTO.getTitle());
			shopMsgDataDTO.setSessionKey(shopDTO.getSessionKey());
			shopMsgDataDTO.setStatus(shopDTO.getStatus());
			shopMsgDataDTO.setSubuserNum(shopDTO.getSubuserNum());
			shopMsgDataDTO.setRealtimeSwitch(shopDTO.getRealtimeSwitch());
			shopMsgDataDTO.setFetchFlag(shopDTO.getFetchFlag());
			shopMsgDataDTO.setInitDataFlag(shopDTO.getInitDataFlag());
			shopMsgDataDTO.setPreviousGetDataTime(shopDTO.getPreviousGetDataTime());
			shopMsgDataDTO.setPreFetchRealtime(shopDTO.getPreFetchRealtime());
			shopMsgDataDTO.setLastConsumedTime(shopDTO.getLastConsumedTime());
			shopMsgDataDTO.setSchemaId(shopDTO.getSchemaId());
			shopMsgDataDTO.setDb(shopDTO.getDb());
			shopMsgDataDTO.setVenderId(shopDTO.getVenderId());
			shopMsgDataDTO.setRtSchemaId(shopDTO.getRtSchemaId());
			shopMsgDataDTO.setRtDb(shopDTO.getRtDb());
			shopMsgDataDTO.setColType(shopDTO.getColType());
			shopMsgDataDTO.setType(shopDTO.getType());
			shopMsgDataDTO.setCreateTableFlag(shopDTO.getCreateTableFlag());
			shopMsgDataDTO.setRefreskSessionKey(shopDTO.getRefreskSessionKey());
			shopMsgDataDTO.setOptionSessionKey(shopDTO.getOptionSessionKey());
		}
		return shopMsgDataDTO;
	}

    @Override
	public ShopLocationInfo getShopLocationInfo(Long shopId) {
		ShopDTO localShop = shopDao.selectShopByShopId(shopId);

		ShopLocationInfo shopInfo = new ShopLocationInfo(localShop.getShopId());
		shopInfo.setDb(localShop.getDb());
		shopInfo.setSchemaId(localShop.getSchemaId());
		return shopInfo;
	}

	@Override
	public ShopDetail getShopDetailByShopId(Long shopId) {
		return shopDetailDao.getShopDetailByShopId(shopId);
	}
	@Override
	public List<AdminShopVO> selectShopByNickOrTitle(String nick, String type) {
		return shopDao.selectShopByNickOrTitle(nick,type);
	}
	@Override
	public int updateShopCreateFlagByShopId(Long shopId) {
		return shopDao.updateShopCreateTableFlagByShopId(shopId);
	}

	@Override
	public void clearVisitCode(ShopDetail shopDetail) {
		shopDetailDao.clearVisitCode(shopDetail);
	}

	@Override
	public RealtimeShopDTO getRealTimeShopByShopId(Long shopId) {
		return realTimeShopDao.getRealTimeShopByShopId(shopId);
	}

	@Override
	public ShopDTO selectShopByShopNameorShopId(String shopName){
		return shopDao.selectShopByShopNameorShopId(shopName);
	}
	@Override
	public List<ShopDTO> selectShopByShopNameorShopId(List<Long> shopIdLst, String shopName,Long shopId1) {

		return shopDao.selectShopByShopNameorShopId(shopIdLst, shopName,shopId1);
	}

	@Override
	public List<ShopVO> selectShopByDateByNickOrTitle(JdSystemPageParam jdSystemPageParam) {
		//判断在哪里查找
		if (jdSystemPageParam.isIgnoreDate()) {//无订购时间的店铺
			return shopDao.selectNoDateShopByDateByNickOrTitle(jdSystemPageParam);
		}
		//有订购时间的店铺
		return shopDao.selectShopByDateByNickOrTitle(jdSystemPageParam);
	}

    @Override
    public Set<String> getKeyWord(Long shopId) {
		Set<String> keyWord= Sets.newHashSet();
		List<String> sysWordLst = keywordSensitiveDao.selectKeyWordByScope(1);
		List<String> customWordLst = shopSensitiveWordDao.selectSensitiveWordByShopId(shopId);
		if(CollectionUtils.isNotEmpty(sysWordLst)){
			keyWord.addAll(sysWordLst);
		}
		if(CollectionUtils.isNotEmpty(customWordLst)){
			keyWord.addAll(customWordLst);
		}
		return keyWord;
    }

	@Override
	public List<ShopDTO> selectShopByIds(List<Long> shopIdLst,String shopParam) {
		return shopDao.selectShopByIds(shopIdLst,shopParam);
	}

	@Override
	public List<String> getRtDbLstByIds(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size) {
		return shopDao.getRtDbLstByIds(shopIdLst, queryParam,  startIndex,  size);
	}

    @Override
    public List<BoardMnoitorParamDTO> getShopListByIdsAndRtdb(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size, String rtDb) {
        return shopDao.getShopListByIdsAndRtdb(shopIdLst, queryParam, startIndex, size, rtDb);
    }

    @Override
	public List<BoardMnoitorParamDTO> getShopListByIds(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size) {
		return shopDao.getShopListByIds(shopIdLst, queryParam,  startIndex,  size);
	}
	@Override
	public int selectShopNumsByDeptIdAndTitle(List<Long> shopIds, String queryParam) {
		return shopDao.selectShopNumsByDeptIdAndTitle(shopIds, queryParam);
	}

	@Override
	public List<ShopDbAndSchemeIdBO> selectShopDbAndSchemaIdByShopTitle(String shopTitle, Integer type) {
		return shopDao.selectShopDbAndSchemaIdByShopTitle(shopTitle, type);
	}

	@Override
	public List<ShopDbAndSchemeIdBO> selectDbByShopIds(List<Long> shopIdLst) {
		return shopDao.selectDbByShopIds(shopIdLst);
	}

	@Override
	public List<SgShopDTO> queryShopInfoLst(Integer shopType) {
		List<SgShopDTO> result = Lists.newArrayList();
		List<ShopDTO> shopDTOLst = shopDao.queryShopInfoLst(shopType);

		for(ShopDTO shop : shopDTOLst){
			SgShopDTO sgShopDTO = new SgShopDTO();
			sgShopDTO.setShopId(shop.getShopId());
			sgShopDTO.setVenderId(shop.getVenderId());
			if("active".equals(shop.getStatus())){
				sgShopDTO.setStatus(1);//1:活跃,2:过期
			} else {
				sgShopDTO.setStatus(2);//1:活跃,2:过期
			}

			result.add(sgShopDTO);
		}

		return result;
	}

    @Override
    public List<ShopUrge> selectUrgeShopByShopIdLstByType(List<Long> shopIdLst, Integer type) {

		return shopDao.selectUrgeShopByShopIdLstByType(shopIdLst,type);
    }

	@Override
	public void updateInitFlag(Long shopId) {
		shopDao.updateInitFlag(shopId);
	}
	public List<Shop> queryShopInfoList(Integer shopType) {
		return shopDao.queryShopInfoList(shopType);
	}

	@Override
	public JobShopDTO getJobShopInfoById(Long shopId) {
		return shopDao.getJobShopInfoById(shopId);
	}

	@Override
	public JobShopResult getJobShop(Long shopId) {
		JobShopResult jobShop = new JobShopResult(shopDao.getJobShopInfoById(shopId));
		JobShopDTO shop = jobShop.getShop();
		List<ShopSmsWordDTO> shopSmsWordLst = new ArrayList<>();
		jobShop.setShopSystemsetting(shopSystemsettingBusiness.getShopSystemsetting(shopId));
		jobShop.setBuyerFilterLst(shopSystemsettingBusiness.selectFilterBuyerNickLstByShopId(shopId));
		jobShop.setGoodFilterLst(shopSystemsettingBusiness.selectPesGoodsByShopId(shopId));
		jobShop.setShopSettingBatchRemind(shopSettingBatchRemindBusiness.selectByShopId(shop));
		jobShop.setShopAutoAllocatedSetting(shopAutoAllocatedSettingBussiness.selectShopAutoAllocatedSettingByShopId(shopId));
		jobShop.setShopUrge(shopUrgeDao.selectUrgeShopByShopId(shopId));
		ShopSmsSetting shopSmsSetting = shopSmsSettingBussiness.selectShopSmsSettingByShopId(shopId);
		jobShop.setShopSmsSetting(shopSmsSetting);
		if (null != shopSmsSetting) {
			shopSmsWordLst.add(shopSmsSettingBussiness.queryShopSmsWordById(shopSmsSetting.getUnpcWordId()));//'普通下单未付款话术'
			shopSmsWordLst.add(shopSmsSettingBussiness.queryShopSmsWordById(shopSmsSetting.getSlinetWordId()));//'静默下单未付款话术'
		}
		jobShop.setShopSmsWordLst(shopSmsWordLst);
		jobShop.setShopRemindBlacklistList(shopRemindBlackListBusiness.selectShopRemindBlacklistByShopId(shopId));
		ShopSettingBatchRemindCno shopSettingBatchRemindCno = shopSettingBatchRemindBusiness.selectShopSettingBatchRemindCnoByShopId(jobShop.getShop());
		jobShop.setShopSettingBatchRemindCno(shopSettingBatchRemindCno);
		jobShop.setShopAutoAdvanceAllocatedSetting(shopAutoAllocatedSettingBussiness.selectShopAutoAdvanceAllocatedSettingByShopId(shopId));
		jobShop.setShopAutoAppointmentAllocatedSetting(shopAutoAllocatedSettingBussiness.selectShopAutoAppointmentAllocatedSettingByShopId(shopId));
		jobShop.setInterfaceControlLst(interfaceControlBusiness.selectAll());
		//自定义的敏感词库
		jobShop.setCustomWordLst(shopSensitiveWordDao.selectSensitiveWordByShopId(shopId));
		//剩余短信数量
		Map<String, Object> smsNumMap = smsManagerBusiness.queryShopSmsRemainingCount(shopId);
		jobShop.setSmsNum(Integer.valueOf(smsNumMap.get("remainingNumber")+""));
		return jobShop;
	}

	@Override
	public List<CsDTO> getCsLst(Long shopId) {
		return csManageBusiness.searchCsLstByShopIdAndType(shopId, 1);
	}

    @Override
    public List<ShopAccountDTO> getShopSubUserLst(Long shopId){
		return shopAccountBussiness.selectShopAccountByShopId(shopId);
	}

	@Override
	public BatchRemindSetting getBatchRemindSetting(Long shopId, JobShopDTO shop) {
		BatchRemindSetting batchRemindSetting = new BatchRemindSetting();
		batchRemindSetting.setShopSettingBatchRemind(shopSettingBatchRemindBusiness.selectByShopId(shop));
		batchRemindSetting.setShopSettingBatchRemindPresale(shopSettingBatchRemindBusiness.selectShopSettingBatchRemindPresaleByShopId(shop));
		batchRemindSetting.setShopSettingBatchRemindReserve(shopSettingBatchRemindBusiness.selectShopSettingBatchRemindReserveByShopId(shop));
		batchRemindSetting.setShopSettingBatchRemindCno(shopSettingBatchRemindBusiness.selectShopSettingBatchRemindCnoByShopId(shop));
		return batchRemindSetting;
	}

	@Override////查询系统默认敏感词校验
	public List<String> getSysWordLst() {
		return keywordSensitiveDao.selectKeyWordByScope(1);
	}

	@Override
	public List<JobShopResult> getAllActiveJobShop(Integer shopType) {
		List<JobShopDTO> jobShopLst = shopDao.getAllActiveJobShop(shopType);
		List<JobShopResult> shopResultLst = Lists.newArrayList();
		for (JobShopDTO shop : jobShopLst) {
			JobShopResult shopResult = new JobShopResult(shop);
			ShopSystemsettingDTO shopSystemsetting = shopSystemsettingBusiness.getShopSystemsetting(shop.getShopId());
			shopResult.setShopSystemsetting(shopSystemsetting);

			List<CsDTO> csLst = csManageBusiness.searchCsLstByShopIdAndType(shop.getShopId(), 1);
			shopResult.setCsLst(csLst);

			List<ShopAccountDTO> shopSubUserLst = shopAccountBussiness.selectShopAccountByShopId(shop.getShopId());
			shopResult.setShopSubUserLst(shopSubUserLst);

			List<String> buyerFilterLst = shopSystemsettingBusiness.selectFilterBuyerNickLstByShopId(shop.getShopId());
			shopResult.setBuyerFilterLst(buyerFilterLst);

			List<GoodsFilterDTO> goodFilterLst = shopSystemsettingBusiness.selectPesGoodsByShopId(shop.getShopId());
			shopResult.setGoodFilterLst(goodFilterLst);

			ShopSettingBatchRemindDTO shopSettingBatchRemind = shopSettingBatchRemindBusiness.selectByShopId(shop);
			shopResult.setShopSettingBatchRemind(shopSettingBatchRemind);

			shopResultLst.add(shopResult);
		}
		return shopResultLst;
	}


	@Override
	public List<JobShopDTO> selectActiveShopLst() {

		List<JobShopDTO> jobShopLst = shopDao.getAllActiveJobShop(null);
		return jobShopLst;
	}

	@Override
	public PullSubscribeDTO getAllActiveJobShopByShopId(Long shopId) {

		return shopDao.getAllActiveJobShopByShopId(shopId);
	}

	@Override
	public List<String> selectShopCsNickLst(Long shopId, String groupId) {
		if (StrUtil.isNotBlank(groupId)) {
			return csDao.selectCsNickByGroupId(Long.valueOf(groupId));
		} else {
			return csDao.selectCsNickByShopId(shopId);
		}
	}

	@Override
	public JobShopDTO getShopSplitByShopId(Long shopId) {
		return shopDao.getShopSplitByShopId(shopId);
	}

	@Override
	public ShopSplitKeyDTO getShopSplitKeyInfo(Long shopId) {
		return shopDao.getShopSplitKeyInfo(shopId);
	}

	@Override
	public int getTypeByShopId(String shopId) {
		return shopDao.getTypeByShopId(shopId);
	}

    @Override
    public ShopDTO selectShopBySessionKey(String sessionKey) {
        return shopDao.selectShopBySessionKey(sessionKey);
    }

	@Override
	public List<ShopDTO> listShopInfoByShopIds(List<Long> shopIdList) {
		if(CollectionUtils.isEmpty(shopIdList))
			return new ArrayList<>();
		return shopDao.listShopInfoByShopIds(shopIdList);
	}
}

