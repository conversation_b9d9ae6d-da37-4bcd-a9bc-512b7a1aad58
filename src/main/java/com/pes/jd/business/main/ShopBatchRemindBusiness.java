package com.pes.jd.business.main;

import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO;

import java.util.List;

/**
 * 插件管理 - 批量提醒
 */
public interface ShopBatchRemindBusiness {

    int updateShopBatchRemindSetting(ShopSettingBatchRemindDTO dto);

    int updateShopPresaleBatchRemindSetting(ShopSettingBatchRemindPresaleDTO dto);

    int updateShopReserveBatchRemindSetting(ShopSettingBatchRemindReserveDTO dto);

    ShopSettingBatchRemindDTO selectShopBatchRemindSetting(Long shopId);

    ShopSettingBatchRemindPresaleDTO selectShopPresaleBatchRemindSetting(Long shopId);

    ShopSettingBatchRemindReserveDTO selectShopReserveBatchRemindSetting(Long shopId);

    ApiResponse addShopRemindWord(ShopRemindWordDTO dto) throws Exception;

    int deleteShopRemindWord(Long id, String csNick);

    int updateShopRemindWord(ShopRemindWordDTO dto) throws Exception;

    List<ShopRemindWordDTO> selectShopRemindWordLst(ShopRemindWordDTO dto);

    List<ShopRemindWordDTO> selectShopRemindWordLstForPlugin(ShopRemindWordDTO dto);

    int addDefaultRemindWordLst(Long shopId);

    List<Long> selectShopRemindWordGoodsByWordId(Long wordId);

     int updateShopRemindIsUsing(Long shopId);

    List<Long> selectShopRemindWordGoodsByShopId(Long shopId);

	int addSelfShopRemindWord(ShopRemindWordDTO dto)throws Exception;

    List<ShopRemindWordDTO> selectShopBatchRemindSettingWord(Long shopId, String name, String csNick);

    List<ShopRemindWordDTO> selectShopRemindWordLstByShopIdAndOrderType(Long shopId, Long type);

    boolean selecttShopBatchRemindSetting(Long shopId);

    int insertDefaultRemindWord(Long shop);

    ApiResponse addTopTime(Long shopId, Long id);

    List<ShopRemindWordDTO> selectShopRemindWordLstFromCsRemindWord(ShopRemindWordDTO dto);

    List<ShopRemindWordDTO> selectShopRemindWordLstFromShopRemindWord(ShopRemindWordDTO dto);

    int batchInsertShopCsRemindWord(List<ShopRemindWordDTO> oldData);

    int selectShopRemindWordCountFromCsRemindWord(ShopRemindWordDTO dto);

    void checkShopCsRemindWordDb(ShopRemindWordDTO dto);
}
