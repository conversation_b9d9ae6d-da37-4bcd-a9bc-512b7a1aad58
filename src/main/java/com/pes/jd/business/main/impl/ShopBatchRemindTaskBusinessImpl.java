package com.pes.jd.business.main.impl;

import cn.hutool.core.collection.CollUtil;
import com.pes.jd.business.main.ShopBatchRemindTaskBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopBatchRemindTaskDao;
import com.pes.jd.model.DO.ShopBatchRemindTaskDO;
import com.pes.jd.model.DTO.ShopBatchRemindTaskDTO;
import com.pes.jd.model.Param.TaskParam;
import com.pes.jd.util.CollectionUtil;
import com.yiyitech.support.redis.RedisCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ShopBatchRemindTaskBusinessImpl implements ShopBatchRemindTaskBusiness {
    private static Logger logger = LoggerFactory.getLogger(ShopBatchRemindTaskBusinessImpl.class);
    @Resource
    private ShopBatchRemindTaskDao shopBatchRemindTaskDao;

    @Resource
    private RedisCache redisCache;

    /**
     * 通过ID查询单条数据
     *
     * @param shopId
     * @param id     主键
     * @return 实例对象
     */
    @Override
    public ShopBatchRemindTaskDTO queryById(Long shopId, Long id) {
        return this.shopBatchRemindTaskDao.queryById(shopId, id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit  查询条数
     * @return 对象列表
     */
    @Override
    public List<ShopBatchRemindTaskDTO> queryAllByLimit(int offset, int limit) {
        return this.shopBatchRemindTaskDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param shopBatchRemindTask 实例对象
     * @return 实例对象
     */
    @Override
    public ShopBatchRemindTaskDO insert(ShopBatchRemindTaskDO shopBatchRemindTask) {
        this.shopBatchRemindTaskDao.insert(shopBatchRemindTask);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + shopBatchRemindTask.getShopId(), 0, 0);
        return shopBatchRemindTask;
    }

    /**
     * 修改数据
     *
     * @param shopBatchRemindTask 实例对象
     * @return 实例对象
     */
    @Override
    public ShopBatchRemindTaskDTO update(ShopBatchRemindTaskDO shopBatchRemindTask) {
        this.shopBatchRemindTaskDao.update(shopBatchRemindTask);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + shopBatchRemindTask.getShopId(), 0, 0);
        return this.queryById(shopBatchRemindTask.getShopId(), shopBatchRemindTask.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param shopId
     * @param id     主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long shopId, Long id) {
        boolean b = this.shopBatchRemindTaskDao.deleteById(shopId, id) > 0;
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + shopId, 0, 0);
        return b;
    }

    @Override
    public List<ShopBatchRemindTaskDTO> queryLstByShopId(Long shopId) {
        return this.shopBatchRemindTaskDao.queryLstByShopId(shopId);
    }

    @Override
    public List<ShopBatchRemindTaskDTO> queryLstByShopIdAndSendType(TaskParam param) {
        return this.shopBatchRemindTaskDao.queryLstByShopIdAndSendType(param);
    }

    @Override
    public int updateIsRemind(Long shopId, Long id, Integer isRemind) {
        int i = this.shopBatchRemindTaskDao.updateIsRemind(shopId, id, isRemind);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + shopId, 0, 0);
        return i;
    }

    @Override
    public int insertLst(List<ShopBatchRemindTaskDTO> insertLst) {
        if (CollUtil.isEmpty(insertLst)) {
            return 0;
        }
        int num = 0;
        for (List<ShopBatchRemindTaskDTO> tInsertLst : CollectionUtil.smallToLst(insertLst, CommonConstants.BIG_DATA_INSERT_NUM)) {
            num += this.shopBatchRemindTaskDao.insertLst(tInsertLst);
        }
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + insertLst.get(0).getShopId(), 0, 0);
        return num;
    }

    @Override
    public int updateIsRemindByShopId(TaskParam param, int isRemind) {
        int i = this.shopBatchRemindTaskDao.updateIsRemindByShopId(param.getShopId(), param.getSendType(), isRemind);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + param.getShopId(), 0, 0);
        return i;
    }

    @Override
    public int updateIsRemindByParam(TaskParam param, int targetStatus, int isRemind) {
        int i = this.shopBatchRemindTaskDao.updateIsRemindByParam(param.getShopId(), param.getSendType(), targetStatus, isRemind);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + param.getShopId(), 0, 0);
        return i;
    }

    @Override
    public int selectTaskNumByShopIdAndSendTypeAndIsRemind(Long shopId, Integer sendType, int isRemind) {
        return this.shopBatchRemindTaskDao.selectTaskNumByShopIdAndSendTypeAndIsRemind(shopId,sendType,isRemind);
    }

    @Override
    public int getShopBatchRemindTask(Long shopId, String name) {
        return this.shopBatchRemindTaskDao.getShopBatchRemindTask(shopId, name);
    }

    @Override
    public List<ShopBatchRemindTaskDTO> queryShopBatchRemindTaskByIds(String shopId, String ids) {
        return this.shopBatchRemindTaskDao.queryShopBatchRemindTaskByIds(shopId, ids);
    }

    @Override
    public List<ShopBatchRemindTaskDTO> queryShopBatchRemindTaskByDdWord(String shopId, String word) {
        return this.shopBatchRemindTaskDao.queryShopBatchRemindTaskByDdWord(shopId, word);
    }

    @Override
    public int selectShopRemindTaskReserve(Long shopId) {
        return shopBatchRemindTaskDao.selectShopRemindTaskReserve(shopId);
    }

    @Override
    public List<ShopBatchRemindTaskDTO> selectShopRemindTaskListByType(Long shopId, Long type) {
        return shopBatchRemindTaskDao.selectShopRemindTaskListByType(shopId, type);
    }

    @Override
    public int selectTaskNumByShopIdAndSendTypeAndTaskTypeAndIsRemind(Long shopId, Integer sendType, Integer taskType, int remind) {
        return shopBatchRemindTaskDao.selectTaskNumByShopIdAndSendTypeAndTaskTypeAndIsRemind(shopId, sendType, taskType, remind);
    }

    @Override
    public int getDefaultShopBatchRemindTask(Long shopId) {
        return shopBatchRemindTaskDao.getDefaultShopBatchRemindTask(shopId);
    }

    @Override
    public void updateShopBatchRemindTaskByIds(String shopId, List<Long> toUpdateIdList, Integer isRemind) {
        shopBatchRemindTaskDao.updateShopBatchRemindTaskByIds(toUpdateIdList, isRemind);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + shopId, 0, 0);
    }

    @Override
    public void updateShopBatchRemindTaskByIdsByIsremind(String shopId, List<Long> toUpdateIdList, Integer targetIsRemind, Integer isRemind) {
        shopBatchRemindTaskDao.updateShopBatchRemindTaskByIdsByIsremind(toUpdateIdList, targetIsRemind, isRemind);
        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + shopId, 0, 0);
    }

    @Override
    public List<ShopBatchRemindTaskDTO> selectShopBatchRemindTaskListByTypes(Long shopId, List<Integer> taskTypeList) {
        return shopBatchRemindTaskDao.selectShopBatchRemindTaskListByTypes(shopId, taskTypeList);
    }

    @Override
    public int selectSmsConsultWordTaskNum(String shopId, Long sendType, Long consultWordId) {
        return shopBatchRemindTaskDao.selectSmsConsultWordTaskNum(shopId, sendType, consultWordId);
    }

    @Override
    public int selectSmsSilenceWordTaskNum(String shopId, Long sendType, Long silenceWordId) {
        return shopBatchRemindTaskDao.selectSmsSilenceWordTaskNum(shopId, sendType, silenceWordId);
    }

    @Override
    public void readNotice(Long shopId) {
        shopBatchRemindTaskDao.readNotice(shopId);
    }

    @Override
    public void clickPrompt(Long id) {
        shopBatchRemindTaskDao.clickPrompt(id);
    }

    @Override
    public void updateNoticeAndPromptById(Long shopId, List<Long> needToUpdateTaskIdList, int status) {
        shopBatchRemindTaskDao.updateNoticeAndPromptById(shopId, needToUpdateTaskIdList, status);
    }

    @Override
    public int selectDefaultTask(Long shopId) {
        return shopBatchRemindTaskDao.selectDefaultTask(shopId);
    }

    @Override
    public void updateTaskToDisplayed(Long shopId, int firstPrompt) {
        shopBatchRemindTaskDao.updateTaskToDisplayed(shopId, firstPrompt);
    }

    @Override
    public ShopBatchRemindTaskDTO queryByFlag(Long shopId, Integer flag) {
       // return this.shopBatchRemindTaskDao.queryByFlag(shopId, flag);
        return null;
    }
}