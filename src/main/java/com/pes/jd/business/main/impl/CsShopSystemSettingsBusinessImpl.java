package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.CsBusiness;
import com.pes.jd.business.main.CsShopSystemSettingsBusiness;
import com.pes.jd.dao.main.ShopDao;
import com.pes.jd.dao.main.ShopSystemsettingDao;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Query.UserQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: aiJun
 * @Date: 2019-05-17 17:39
 * @Version 1.0
 */
@Service
public class CsShopSystemSettingsBusinessImpl implements CsShopSystemSettingsBusiness {

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private ShopSystemsettingDao shopSystemsettingDao;

    @Autowired
    private CsBusiness csBusiness;


    @Override
//    @Cacheable(cacheNames ="common",key = "'usrmgr-shop-'+#shopId")
    public Map<String, Object> selectShopCsAndSystemSettings(String shopId, String groupId, String csNick, boolean needSystemSettings) {
        final Map<String, Object> data = new HashMap<>();
        final ShopDTO shopDTO = shopDao.selectShopByShopIdFromRedis(Long.parseLong(shopId));
        List<UserQuery> nickLst = new ArrayList<>();
        /**
         * 支持多个客服查询
         */
        if (StringUtils.isEmpty(csNick)) {
            nickLst = csBusiness.getNicksFromRedis(shopId, groupId, csNick);
        } else {
            String[] csNickLst = csNick.split(",");
            for (String cNick : csNickLst) {
                List<UserQuery> userQueryLst = csBusiness.getNicksFromRedis(shopId, groupId, cNick);
                if (!CollectionUtils.isEmpty(userQueryLst)) {
                    nickLst.addAll(userQueryLst);
                }
            }
            nickLst = nickLst.stream().distinct().collect(Collectors.toList());
        }

        data.put("shop",shopDTO);
        data.put("csNickLst", nickLst);
        if (needSystemSettings) {
            final ShopSystemsettingDTO shopSystemsetting = shopSystemsettingDao.getShopSystemsettingByShopIdFromRedis(Long.parseLong(shopId));
            data.put("shopSystemsettingDTO",shopSystemsetting);
        }else {
            data.put("shopSystemsettingDTO",new ShopSystemsettingDTO());
        }
        return data;
    }
}
