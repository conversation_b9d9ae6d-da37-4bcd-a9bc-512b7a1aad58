package com.pes.jd.business.main.impl;

import com.google.common.collect.Lists;
import com.pes.jd.business.main.GroupBusiness;
import com.pes.jd.dao.main.*;
import com.pes.jd.model.DO.Group;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GroupParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.*;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**  
 * 客服分组 - 业务类
 * ClassName:GroupBusinessImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月22日 下午1:56:56 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class GroupBusinessImpl implements GroupBusiness {

	private static final Logger logger = LoggerFactory.getLogger(GroupBusinessImpl.class);

	@Autowired
	private GroupDao groupDao;
	
	@Autowired
	private GroupCsDao groupCsDao;
	@Autowired
	private CsDao csDao;
	@Autowired
	private CsManagerDao csManagerDao;
	@Autowired
	private ShopGroupMemberDao shopGroupMemberDao;
	
	@Autowired
	private GoodsGroupDao goodsGroupDao;
	@Autowired
	private GoodsGroupSkuDao goodsGroupSkuDao;

	@Autowired
	private ShopDao shopDao;
	@Override
	public GroupDTO getCsGroupByGroupName(Long shopId, String groupName, String groupId) {
		  
		return groupDao.getCsGroupByGroupName(shopId, groupName, groupId);
	}
	
	@Transactional
	@Override
	public int createShopCsGroup(String groupName, String shopId, boolean flag) {
		Group cg = new Group();
		cg.setShopId(Long.valueOf(shopId));
		cg.setGroupName(groupName);
		cg.setIsDefault(flag);// 0为不是默认的，1为默认的
		cg.setCreated(new Date());
		return groupDao.insertGroup(cg);

	}
	@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED)
	@Override
	public int updateShopGroup(Long groupId, String groupName, String shopId)throws Exception {
		// 修改之前先将原先的组的旺旺移到默认分组里
		int row=0;
		Group group=	groupDao.getGroupByGroupId(groupId);
		// 拿到原先店铺下的默认groupId(暂时不用)
//		GroupDTO defaultGroup = groupDao.getDefaulutGroupByshopId(group.getShopId());
//		List<GroupCsDTO> noUpdateBeforeGroupCsList=groupCsDao.selectGroupCsByGroupId(groupId);
//		row+=groupCsDao.deleteGroupCsByGroupId(groupId);
//		if (CollectionUtils.isNotEmpty(noUpdateBeforeGroupCsList)) {
//			List<GroupCs> gcsLst=new ArrayList<GroupCs>();
//			for (GroupCsDTO gcs : noUpdateBeforeGroupCsList) {
//				// 将原先组里的旺旺都移到默认分组里
//				GroupCs gs=new GroupCs();
//				gs.setGroupId(defaultGroup.getGroupId());
//				gs.setShopId(gcs.getShopId());
//				gs.setNick(gcs.getNick());
//				gcsLst.add(gs);
//			}
//			row+=groupCsDao.batchInsertGroupcs(gcsLst);
//		}
		// 修改店铺以及组名
		group.setShopId(Long.valueOf(shopId));
		group.setGroupName(groupName);
		group.setModified(new Date());
		row+= groupDao.updateGroupBySelective(group);
		return row;
	}
	@Override
	public ApiResponse deleteCsGroup(Long groupId) {
//		int row = 0;
//		Group group = groupDao.getGroupByGroupId(groupId);
//		GroupDTO defaultGroup = groupDao.getDefaulutGroupByshopId(group.getShopId());
		List<GroupCsDTO> groupCsList = groupCsDao.selectGroupCsByGroupId(groupId);
		if(CollectionUtils.isNotEmpty(groupCsList)){
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_16);
		}else{
			groupDao.deleteGroupByGroupId(groupId);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		}
		//		row += groupCsDao.deleteGroupCsByGroupId(groupId);
//		if (CollectionUtils.isNotEmpty(noUpdateBeforeGroupCsList)) {
//			List<GroupCs> gcsLst = new ArrayList<GroupCs>();
//			for (GroupCsDTO gcs : noUpdateBeforeGroupCsList) {
//				// 将原先组里的旺旺都移到默认分组里
//				GroupCs gs = new GroupCs();
//				gs.setGroupId(defaultGroup.getGroupId());
//				gs.setShopId(gcs.getShopId());
//				gs.setNick(gcs.getNick());
//				gcsLst.add(gs);
//			}
//			row += groupCsDao.batchInsertGroupcs(gcsLst);
//		}
	
	}
	
	@Override
	public List<MultiShopGroupVO>	queryMultiShopGroupByCurrentShopId(List<ShopQuery> shops) throws Exception{
		List<MultiShopGroupVO> multiShopGroup = new ArrayList<MultiShopGroupVO>();
		for (ShopQuery custShop : shops) {
			MultiShopGroupVO msg = new MultiShopGroupVO();
			msg.setSubuserNum(custShop.getSubuserNum());
			Long shopId = custShop.getShopId();
			List<GroupVO> custGroupModelList = new ArrayList<>();
			List<CsInfoVO> csNickList;
			Set<String> allCsNickSet = new HashSet<>();
			List<CsInfoVO> allCsNickList = new ArrayList<>();
			GroupVO cml;
			CsInfoVO ck;
			List<CsDTO> alcs=csDao.selectCsByShopIdByTypeByCsStatus(shopId, null, 1);
			// 获取店铺内的所有正常状态的客服带上分组id
			List<GroupCsDTO> custGroupDataList=	csManagerDao.selectGroupDataList(shopId);
			// 获取店铺内所有的客服组
			List<GroupDTO> groupCsList = groupDao.selectGroup(shopId);

			for (CsDTO ca : alcs) {
				if (allCsNickSet.contains(ca.getNick())) {
					continue;
				} else {
					allCsNickSet.add(ca.getNick());
				}
				ck = new CsInfoVO();
				ck.setCsFlagType(ca.getType());
				ck.setCsSimpleNick(ca.getCsSimpleNick());
				ck.setCsNick(ca.getNick());
				allCsNickList.add(ck);
			}
			if(CollectionUtils.isNotEmpty(allCsNickList)){
				allCsNickList.sort((h1, h2) -> h1.getCsFlagType().compareTo(h2.getCsFlagType()));
			}
			//获取店铺的商品组
			List<GoodsGroupDTO>	goodsGroupLst= goodsGroupDao.selectGoodsGroupByShopId(shopId);
			List<GoodsGroupSkuVO> allGoodsGroupSkuLst=Lists.newArrayList();
			List<GoodsGroupSkuVO> goodsGroupSkuVoLst;
			GoodsGroupVO goodsGroupVo;
			GoodsGroupSkuVO   goodGroupSku;
			List<GoodsGroupVO> goodsGroupVoLst=Lists.newArrayList();
			if(CollectionUtils.isNotEmpty(goodsGroupLst)){
				//获取点铺组的商品sku
				List<GoodsGroupSkuDTO>   goodsGroupSkuLst=goodsGroupSkuDao.selectGoodsGroupSkuByShopId(shopId);
				
				for (GoodsGroupSkuDTO goodsGroupSkuDTO : goodsGroupSkuLst) {
					GoodsGroupSkuVO goodsGroupSkuVO=new GoodsGroupSkuVO();
					goodsGroupSkuVO.setGroupId(goodsGroupSkuDTO.getGroupId());
					goodsGroupSkuVO.setShopId(goodsGroupSkuDTO.getShopId());
					goodsGroupSkuVO.setSkuId(goodsGroupSkuDTO.getSkuId());
					goodsGroupSkuVO.setDimension(goodsGroupSkuDTO.getDimension());
					allGoodsGroupSkuLst.add(goodsGroupSkuVO);
				}
				
				for (GoodsGroupDTO goodsGroup : goodsGroupLst) {
					goodsGroupSkuVoLst=Lists.newArrayList();
					goodsGroupVo=new GoodsGroupVO();
					for (GoodsGroupSkuDTO GroupSku : goodsGroupSkuLst) {
						if(GroupSku.getGroupId().equals(goodsGroup.getGoodsGroupId())){
							goodGroupSku=new GoodsGroupSkuVO();
							goodGroupSku.setGroupId(GroupSku.getGroupId());
							goodGroupSku.setShopId(GroupSku.getShopId());
							goodGroupSku.setSkuId(GroupSku.getSkuId());
                            goodGroupSku.setDimension(GroupSku.getDimension());
							goodsGroupSkuVoLst.add(goodGroupSku);
						}
					}
                    goodsGroupVo.setDimension(goodsGroup.getDimension());
					goodsGroupVo.setGoodsGroupId(String.valueOf(goodsGroup.getGoodsGroupId()));
					goodsGroupVo.setGoodsGroupName(goodsGroup.getGoodsGroupName());
					goodsGroupVo.setShopGoodsSkuLst(goodsGroupSkuVoLst);
					goodsGroupVo.setGoodsNum(goodsGroupSkuVoLst.size());
					goodsGroupVo.setShopId(goodsGroup.getShopId());
					goodsGroupVoLst.add(goodsGroupVo);
				}
			}
			GoodsGroupVO allOfGoodsGroup=new GoodsGroupVO(shopId, "", "全部", allGoodsGroupSkuLst.size(),allGoodsGroupSkuLst);
			goodsGroupVoLst.add(0, allOfGoodsGroup);
			GroupVO allOfCustGroupInfo = new GroupVO("全部", "", shopId, allCsNickList);
			custGroupModelList.add(allOfCustGroupInfo);
			
			if(CollectionUtils.isNotEmpty(groupCsList)) {
				for (GroupDTO cp : groupCsList) {
					Long groupId = cp.getGroupId();
					cml = new GroupVO();
					csNickList = new ArrayList<>();
					for (GroupCsDTO ca : custGroupDataList) {
						ck = new CsInfoVO();
						if (ca.getGroupId().equals(groupId)) {
							ck.setCsFlagType(ca.getType());
							ck.setCsNick(ca.getSimpleName());
							ck.setCsSimpleNick(ca.getSimpleName());
							ck.setCsNick(ca.getNick());
							csNickList.add(ck);
						}
					}
					cml.setGroupId(groupId.toString());
					if(CollectionUtils.isNotEmpty(csNickList)){
						csNickList.sort(Comparator.comparing(CsInfoVO::getCsFlagType));
					}
					cml.setCsNickList(csNickList);
					cml.setGroupName(cp.getGroupName());
					cml.setShopId(shopId);
					custGroupModelList.add(cml);
				}
			}
			msg.setShopId(shopId);
			msg.setShopName(custShop.getTitle());
			msg.setMultiShopGroup(custGroupModelList);
			msg.setGoodsGroupLst(goodsGroupVoLst);
			multiShopGroup.add(msg);
		}
		return multiShopGroup;
	}
	
	
	
	@Override
	public List<MultiShopGroupVO>	queryMultiShopGroupByShopId(ShopCommonParam shop) throws Exception{
		List<MultiShopGroupVO> multiShopGroup = new ArrayList<MultiShopGroupVO>();
		Long shopId=shop.getShopId();
			MultiShopGroupVO msg = new MultiShopGroupVO();
			List<GroupVO> custGroupModelList = new ArrayList<>();
			List<CsInfoVO> csNickList = null;
			Set<String> allCsNickSet = new HashSet<>();
			List<CsInfoVO> allCsNickList = new ArrayList<>();
			GroupVO cml = null;
			CsInfoVO ck = null;
			List<CsDTO> alcs=csDao.selectCsByShopIdByTypeByCsStatus(shopId, null, 1);
			// 获取店铺内的所有正常状态的客服带上分组id
			List<GroupCsDTO> custGroupDataList=	csManagerDao.selectGroupDataList(shopId);
			// 获取店铺内所有的客服组
			List<GroupDTO> groupCsList = groupDao.selectGroup(shopId);

			for (CsDTO ca : alcs) {
				if (allCsNickSet.contains(ca.getNick())) {
					continue;
				} else {
					allCsNickSet.add(ca.getNick());
				}
				ck = new CsInfoVO();
				ck.setCsFlagType(ca.getType());
				ck.setCsSimpleNick(ca.getCsSimpleNick());
				ck.setCsNick(ca.getNick());
				allCsNickList.add(ck);
			}
			if(CollectionUtils.isNotEmpty(allCsNickList)){
				allCsNickList.sort((h1, h2) -> h1.getCsFlagType().compareTo(h2.getCsFlagType()));
			}
			GroupVO allOfCustGroupInfo = new GroupVO("全部", "", shopId, allCsNickList);
			custGroupModelList.add(allOfCustGroupInfo);
			
			if(CollectionUtils.isNotEmpty(groupCsList)) {
				for (GroupDTO cp : groupCsList) {
					Long groupId = cp.getGroupId();
					cml = new GroupVO();
					csNickList = new ArrayList<CsInfoVO>();
					for (GroupCsDTO ca : custGroupDataList) {
						ck = new CsInfoVO();
						if (ca.getGroupId().equals(groupId)) {
							ck.setCsFlagType(ca.getType());
							ck.setCsNick(ca.getSimpleName());
							ck.setCsSimpleNick(ca.getSimpleName());
							ck.setCsNick(ca.getNick());
							csNickList.add(ck);
						}
					}
					cml.setGroupId(groupId.toString());
					if(CollectionUtils.isNotEmpty(csNickList)){
						csNickList.sort((h1, h2) -> h1.getCsFlagType().compareTo(h2.getCsFlagType()));
					}
					cml.setCsNickList(csNickList);
					cml.setGroupName(cp.getGroupName());
					cml.setShopId(shopId);
					custGroupModelList.add(cml);
				}
			}
			msg.setShopId(shopId);
			msg.setMultiShopGroup(custGroupModelList);
			multiShopGroup.add(msg);
		return multiShopGroup;
	}

	@Override
	public Long selectGroupIdByShopId(Long shop) {
		return groupDao.selectGroupIdByShopId(shop);
	}


	@Override
	public List<ShopDTO> selectShopGroupMemberShops(Long mainShopId) {

		ShopDTO shopDTO = shopDao.selectShopByShopId(mainShopId);
		LinkedHashSet<ShopDTO> memberShopSet = new LinkedHashSet<>();
		//查询自己组下的所有成员店铺
		List<ShopDTO> memberShops = shopGroupMemberDao.selectShopGroupMemberShops(mainShopId,shopDTO.getType());
		if (CollectionUtils.isNotEmpty(memberShops)) {
			memberShopSet.addAll(memberShops);
		}
		//查询自己所属店铺组创建店铺
		List<ShopDTO> mainMemberShop = shopGroupMemberDao.selectShopGroupMemberShopsStartMutualWatchwithParent(mainShopId,shopDTO.getType());
		if (CollectionUtils.isNotEmpty(mainMemberShop)) {
			memberShopSet.addAll(mainMemberShop);
		}
		//查询所属店铺组的成员店铺
		if(CollectionUtils.isNotEmpty(mainMemberShop)){
			List<Long> groupShopIdLst = mainMemberShop.stream().map(ShopDTO::getShopId).collect(Collectors.toList());
			List<ShopDTO> childMemberShop = shopGroupMemberDao.selectShopGroupMemberShopsStartMutualWatch(groupShopIdLst,shopDTO.getType());
			if (CollectionUtils.isNotEmpty(childMemberShop)) {
				memberShopSet.addAll(childMemberShop);
			}
		}

		return new ArrayList<>(memberShopSet);
	}
	
	@Override
	public List<GroupCsDTO> selectGroupCsByGroupParam(GroupParam groupParam){
		long s1,s2,s3;
		s1=System.currentTimeMillis();
		List<GroupCsDTO> groupCsLst=groupCsDao.selectGroupCsByGroupParam(groupParam);
	logger.info("master selectGroupCsByGroupParam time :{}",(s2=System.currentTimeMillis())-s1);
		if(CollectionUtils.isNotEmpty(groupCsLst)){
			Set<Long> groupIdSet=groupCsLst.stream().map(GroupCsDTO::getGroupId).collect(Collectors.toSet());
			List<GroupDTO> groupLst=groupDao.selectGroupByShopIdByGroupIds(groupIdSet);
			if(CollectionUtils.isNotEmpty(groupLst)){
				Map<Long,String> groupNameMap=groupLst.stream().collect(Collectors.toMap(GroupDTO::getGroupId,GroupDTO::getGroupName,(o,n)->n));
				for (GroupCsDTO group : groupCsLst) {
						group.setGroupName(groupNameMap.get(group.getGroupId()));
					}
				}
			}
		logger.info("master 查询我的客服组总花费：{}ms",(s3=System.currentTimeMillis())-s2);
		return groupCsLst;
	}

	@Override
	public List<GroupDTO> queryShopCsGroups(List<ShopCommonParam> shopLst, String groupName) throws Exception {
		List<GroupDTO> groupLst=	groupDao.queryShopCsGroupsByShopIdLstByGroupName(shopLst, groupName);
		if(CollectionUtils.isNotEmpty(groupLst)){
			for (GroupDTO group : groupLst) {
				group.setModified(group.getModified()==null?group.getCreated():group.getModified());
			}
		}
		return groupLst;
	}
}
