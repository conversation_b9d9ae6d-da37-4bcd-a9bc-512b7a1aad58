package com.pes.jd.business.main.impl;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.MsgServiceBusiness;
import com.pes.jd.business.main.SmsManagerBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.*;
import com.pes.jd.model.DO.ShopBatchRemindTaskDO;
import com.pes.jd.model.DO.ShopSmsWordDO;
import com.pes.jd.model.DO.SmsOrderDO;
import com.pes.jd.model.DO.SmsServiceDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.AuditStatusEnum;
import com.pes.jd.model.Enum.SmsServiceStepEnum;
import com.pes.jd.model.Param.PayParam;
import com.pes.jd.model.VO.SmsServiceVO;
import com.pes.jd.ms.utils.RandomNumUtil;
import com.pes.jd.util.DateUtils;
import com.yiyitech.support.redis.RedisCache;
import com.yiyitech.support.util.BeanCopyUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: yuanxun
 * @Date: 11:15 2019/9/17
 * @Description:
 */
@Service
public class SmsManagerBusinessImpl implements SmsManagerBusiness {
    private static Logger logger = LoggerFactory.getLogger(SmsManagerBusinessImpl.class);
    @Autowired
    private SmsOrderDao smsOrderDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private ShopSmsWordDao smsWordDao;

    @Autowired
    private SmsServiceDao smsServiceDao;

    @Autowired
    private MsgServiceBusiness msgServiceBusiness;
    @Autowired
    private ShopBatchRemindTaskDao shopBatchRemindTaskDao;
    @Autowired
    private TransactionTemplate trx;
    @Resource
    private RedisCache redisCache;

    @Override
    public List<SmsOrderDTO> queryRechargeRecord(Date startDate, Date endDate, String nick, String orderId, Integer payWay, Integer orderStatus) {
        List<SmsOrderDTO> smsOrderDTOLst = smsOrderDao.queryRechargeRecord(DateUtils.getStartTimeOfDate(startDate),
                                                                        DateUtils.getEndTimeOfDate(endDate),
                                                                        StringUtils.isNotEmpty(nick) ? nick.trim() : nick, orderId, payWay, orderStatus);

        return smsOrderDTOLst.isEmpty() ? Collections.EMPTY_LIST : smsOrderDTOLst;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean recharge(String nick, Long shopId, Integer number, Double orderFee) {
        //条数价格检验
//        Boolean b = checkNumberAndPrice(number, orderFee);
//        if (b == null || !b) {
//            return false;
//        }

        Date date = new Date();
        ShopUserDTO shopUserDTO = userDao.selectUserByShopIdByMainAccount(shopId);
        SmsOrderDO record = new SmsOrderDO();
        record.setOrderId(RandomNumUtil.getOrderNo());
        record.setDate(date);
        record.setNumber(number);
        record.setOperator(shopUserDTO.getNick());
        record.setOrderFee(orderFee);
        record.setPayType(CommonConstants.PAY_TYPE_LINE_DOWN);
        record.setPayWay(CommonConstants.PAY_WAY_LINE_DOWN);
        record.setShopTitle(nick);
        record.setShopId(shopId);
        record.setOrderFee(orderFee);
        //订单类型判断
        if(number < 0){
            //退款-短信条数验证
            Map<String, Object> remainingCount = this.queryShopSmsRemainingCount(shopId);
            Long remainingNumber = (Long) remainingCount.get("remainingNumber");
            if(remainingNumber < Math.abs(number)){return false;}

            record.setOrderType(CommonConstants.SMS_SUB_TYPE);
        }else{
            record.setOrderType(CommonConstants.SMS_UNSUB_TYPE);
        }
        record.setOrderStatus(CommonConstants.SMS_SUCCESS_STATUS);
        record.setCreateTime(date);
        smsOrderDao.recharge(record);

        //增加或减少店铺剩余短信条数
        SmsServiceDTO smsServiceDTO = smsServiceDao.getByShopId(shopId);
        SmsServiceDO smsServiceDO = new SmsServiceDO();
        if(smsServiceDTO!=null){
            smsServiceDO.setId(smsServiceDTO.getId());
            smsServiceDO.setNumber(smsServiceDTO.getNumber()+number);

            smsServiceDao.updateByPrimaryKey(smsServiceDO);
        }else{
            smsServiceDO.setNumber(number.longValue());
            smsServiceDO.setUseNumber(0l);
            smsServiceDO.setShopId(shopId);

            smsServiceDao.insert(smsServiceDO);
        }

        return true;
    }

    @Override
    public Map<String, Object> queryShopSmsRemainingCount(Long shopId) {
        SmsServiceDTO smsServiceDTO = smsServiceDao.getByShopId(shopId);
        Map<String, Object> data = Maps.newHashMap();
        if(smsServiceDTO==null){
            data.put("useNumber", 0);
            data.put("remainingNumber", 0);
            return data;
        }
        data.put("useNumber", smsServiceDTO.getUseNumber());
        data.put("remainingNumber", smsServiceDTO.getNumber() - smsServiceDTO.getUseNumber());
        return data;
    }

    @Override
    public Map<String, Object> querySmsBalanceCount() {
        SmsServiceDTO smsServiceDTO = smsServiceDao.getSum();
        Map<String, Object> data = Maps.newHashMap();
        Long balance = 0l;
        Long shopBalance = 0l;
        if(smsServiceDTO!=null){
            shopBalance = smsServiceDTO.getNumber() - smsServiceDTO.getUseNumber();
        }
        try{
            balance = msgServiceBusiness.querySmsBalance();
        }catch (Exception e){
            e.printStackTrace();
        }

        data.put("shopBalance", shopBalance);
        data.put("balance", balance);
        return data;
    }

    @Override
    public List<ShopSmsWordDTO> selectSmsWordByNick(String nick) {
        List<ShopSmsWordDTO> shopSmsWordDTOList = smsWordDao.selectSmsWordByNick(StringUtils.isNotEmpty(nick) ? nick.trim() : nick);
        return shopSmsWordDTOList.isEmpty() ? Collections.EMPTY_LIST : shopSmsWordDTOList;
    }

    @Override
    public int queryNotAiditSmsWordCount() {
        return smsWordDao.queryNotAiditSmsWordCount();
    }

    @Override
    public void auditSmsWord(Long id, String auditRemark, Integer auditStutas) {
        trx.execute(status -> {
            //保存模板审核结果
            ShopSmsWordDO record = new ShopSmsWordDO();
            boolean taskUpdateFlag = true;
            while (true) {
                record.setId(id);
                record.setAuditRemark(auditRemark);
                record.setAuditStatus(auditStutas);
                record.setAuditTime(new Date());
                int updateWordResult = smsWordDao.auditSmsWord(record);
                //查询模板相关任务
                List<ShopBatchRemindTaskDTO> taskDTOList = shopBatchRemindTaskDao.searchByWordId(id);
                //根据模板状态变更任务状态
                for (ShopBatchRemindTaskDTO dto : taskDTOList) {
                    int judgeType = this.judgeTaskStatus(Integer.valueOf(dto.getConsultWordId()), Integer.valueOf(dto.getSilenceWordId()), new Date(), dto.getTaskStartTime(), dto.getIsPermanent());
                    int updateTaskResult = shopBatchRemindTaskDao.updateRemindById(dto.getId(), judgeType);
                    //bug2835 任务状态变更为 进行中 后需要清空缓存，不然无法获取到最新的任务状态
                    if(updateTaskResult > 0 && judgeType == CommonConstants.SMS_TASK_IN_PROGRESS){
                        redisCache.expire(CommonConstants.JOB_SHOP_REMINDTASK + dto.getShopId(), 0, 0);
                    }
                    if (updateTaskResult <= 0) {
                        taskUpdateFlag = false;
                        break;
                    }
                }
                if (updateWordResult >= 1 && taskUpdateFlag) {
                    break;
                }
            }
            return null;
        });
    }

    @Override
    public SmsServiceVO queryShopSmsCountAndPrice(Long shopId) {
        SmsServiceDTO smsService = smsServiceDao.getByShopId(shopId);
        return assembleSmsServiceVO(smsService);
    }

    private SmsServiceVO assembleSmsServiceVO(SmsServiceDTO smsServiceDTO) {
        SmsServiceVO smsServiceVO = new SmsServiceVO();
        smsServiceVO.setRemainNum(smsServiceDTO!=null?smsServiceDTO.getNumber()-smsServiceDTO.getUseNumber():0);
        smsServiceVO.setStep1(SmsServiceStepEnum.SMS_SERVICE_STEP1.getPrice());
        smsServiceVO.setStep2(SmsServiceStepEnum.SMS_SERVICE_STEP2.getPrice());
        smsServiceVO.setStep3(SmsServiceStepEnum.SMS_SERVICE_STEP3.getPrice());
        smsServiceVO.setStep4(SmsServiceStepEnum.SMS_SERVICE_STEP4.getPrice());
        smsServiceVO.setStep5(SmsServiceStepEnum.SMS_SERVICE_STEP5.getPrice());
        smsServiceVO.setStep5(SmsServiceStepEnum.SMS_SERVICE_STEP6.getPrice());
        return smsServiceVO;
    }

    @Override
    public double computePayAmount() {


        return 0;
    }

    @Override
    public int insertSmsOrder(PayParam payParam) {
//        ShopUserDTO shopUserDTO = userDao.selectUserByShopIdByMainAccount(shopId);
        SmsOrderDO record = doGetSmsOrderDO(payParam);
        return smsOrderDao.recharge(record);
    }

    @Override
    public int updateDateAndStatusByOrderId(String orderId, Date date, int status) {
        return smsOrderDao.updateDateAndStatusByOrderId(orderId,date,status);
    }

    @Override
    public List<SmsOrderDTO> selectSmsOrderByShopId(Long shopId) {
        return smsOrderDao.selectSmsOrderByShopId(shopId);
    }

    @Override
    public Byte selectOrderStatusBYOrderId(String orderId) {
        return smsOrderDao.selectOrderStatusBYOrderId(orderId);
    }

    @Override
    public SmsOrderDTO selectSmsOrderByOrderId(String orderid) {
        return smsOrderDao.selectSmsOrderByOrderId(orderid);
    }

    @Override
    public int updateSmsNumByShopId(Integer number, Long shopId) {
        return smsServiceDao.updateSmsNumByShopId(number,shopId);
    }

    @Override
    public void updateSmsUseNumber(Long shopId, Integer successCount) {
        int i = smsServiceDao.updateSmsUseNumber(shopId, successCount);

        if(logger.isDebugEnabled()){
            logger.info("更新短信使用数量成功：{} " , i);
        }
    }

    @Override
    public void updateSmsUseNumberByFailCount(Long shopId, Integer failCount) {
        int i = smsServiceDao.updateSmsUseNumberByFailCount(shopId, failCount);

        if(logger.isDebugEnabled()){
            logger.info("更新短信使用数量成功：{} " + i);
        }
    }

    public SmsOrderDO doGetSmsOrderDO(PayParam payParam) {
        SmsOrderDO record = new SmsOrderDO();
        record.setOrderId(payParam.getOrderId());
        record.setNumber(payParam.getNumber());
        record.setOperator(payParam.getOperator());
        record.setOrderFee(payParam.getOrderFe());
        record.setPayType(CommonConstants.PAY_TYPE_LINE_UP);
        record.setPayWay(CommonConstants.PAY_WAY_WX);
        record.setShopTitle(payParam.getShopTitle());
        record.setShopId(payParam.getShopId());
        record.setOrderType(CommonConstants.SMS_SUB_TYPE);
        record.setOrderStatus(CommonConstants.SMS_NOPAY_STATUS);
        record.setCreateTime(new Date());
        return record;
    }

//    private Boolean checkNumberAndPrice(Integer number, Double orderFee) {
//        Boolean reselt = null;
//        if (number <= CommonConstants.SMS_RECHARGE_NUMBEN_1000) {
//            reselt = orderFee.compareTo(number * SmsRechargePrice.LESS_1000.getPrice()) == 0 ? true : false;
//        } else if (CommonConstants.SMS_RECHARGE_NUMBEN_1000 < number && number <= CommonConstants.SMS_RECHARGE_NUMBEN_5000) {
//            reselt = orderFee.compareTo(number * SmsRechargePrice.MORE_1000_LESS_5000.getPrice()) == 0 ? true : false;
//        } else if (CommonConstants.SMS_RECHARGE_NUMBEN_5000 < number && number <= CommonConstants.SMS_RECHARGE_NUMBEN_10000) {
//            reselt = orderFee.compareTo(number * SmsRechargePrice.MORE_5000_LESS_10000.getPrice()) == 0 ? true : false;
//        } else if (CommonConstants.SMS_RECHARGE_NUMBEN_10000 < number && number <= CommonConstants.SMS_RECHARGE_NUMBEN_20000) {
//            reselt = orderFee.compareTo(number * SmsRechargePrice.MORE_10000_LESS_20000.getPrice()) == 0 ? true : false;
//        } else if (CommonConstants.SMS_RECHARGE_NUMBEN_20000 < number && number <= CommonConstants.SMS_RECHARGE_NUMBEN_30000) {
//            reselt = orderFee.compareTo(number * SmsRechargePrice.MORE_20000_LESS_30000.getPrice()) == 0 ? true : false;
//        } else if (CommonConstants.SMS_RECHARGE_NUMBEN_30000 < number) {
//            reselt = orderFee.compareTo(number * SmsRechargePrice.MORE_30000.getPrice()) == 0 ? true : false;
//        }
//
//        return reselt;
//    }


    @Override
    public int judgeTaskStatus(Integer consultWordId, Integer silenceWordId, Date nowDate, Date taskStartDate, Integer permanent) {
        List<Long> ids = new ArrayList<>();
        ids.add(Long.valueOf(consultWordId.toString()));
        ids.add(Long.valueOf(silenceWordId.toString()));
        List<ShopSmsWordDTO> shopSmsWordDTOLst = smsWordDao.searchByIds(ids);
        int returnFlag = CommonConstants.SMS_TASK_IN_PROGRESS;
        for (ShopSmsWordDTO dto : shopSmsWordDTOLst) {
            if (AuditStatusEnum.AUDIT_NOT_PASS.getType() == dto.getAuditStatus()) {
                return CommonConstants.SMS_TASK_NOT_PASS;
            }
            if (AuditStatusEnum.WAIT_AUDIT.getType() == dto.getAuditStatus()) {
                returnFlag = CommonConstants.SMS_TASK_IN_REVIEW;
            }
        }
        if (returnFlag == CommonConstants.SMS_TASK_IN_PROGRESS && permanent == 0 && nowDate.before(taskStartDate)) {
            returnFlag = CommonConstants.SMS_TASK_NOT_STARTED;
        }
        return returnFlag;
    }
}
