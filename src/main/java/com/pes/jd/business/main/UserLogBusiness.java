package com.pes.jd.business.main;

import com.pes.jd.model.VO.PesUserLoginLogVo;
import com.pes.jd.model.VO.PesUserOperationLogVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/12 3:00 PM
 * @since 1.0.0
 */
public interface UserLogBusiness {

    /**
     *  插入操作日志
     */
    int insertOperationLog(Long userId, String nick, Date datetime, Long shopId, String optType, String optContent);

    /**
     *  查询用户操作日志
     */
    List<PesUserOperationLogVo> searchOperationLogByTypeTimeNick(
            Long shopId,
            Date startDate,
            Date endDate,
            String nick,
            String optType);
    /**
     *  插入登录日志
     */
    int insertLoginLog(Long userId, String nick, Long shopId, Date loginTime);
    /**
     *  查询登录日志详情
     */
    List<PesUserLoginLogVo> searchLoginLogByTimeNick(
            Long shopId,
            Date startDate,
            Date endDate,
            String nick
    );

    /**
     *  查询登录日志次数   -  刚开始需要展示的列表
     */
    List<PesUserLoginLogVo> searchLoginCountLogByTimeNick(
            Long shopId,
            Date startDate,
            Date endDate,
            String nick
    );

    /**
     *  查询登录操作日志次数
     */
    int getUserOperationLogByNickAndShopAndTime(
            Long shopId,
            Date startDate,
            Date endDate,
            String type,
            String nick
    );
    /**
     *  查询登录操作日志次数
     */
    int getUserOperationLogByNickAndShop(
            Long shopId,
            String nick,
            String optType
    );

    /**
     *  查询登录日志次数
     */
    int getUserLoginCountByNickAndShopAndTime(
            Long shopId,
            Date startDate,
            Date endDate,
            String nick
    );
}
