package com.pes.jd.business.main;

import com.pes.jd.model.DO.PesUserLoginLog;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.VO.PesUserLoginLogVo;
import com.pes.jd.ms.domain.Result.master.ShopLoginResult;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/12 2:49 PM
 * @since 1.0.0
 */
public interface PesUserLoginLogBusiness {

    int insert(PesUserLoginLog record);

    List<PesUserLoginLog> searchLoginLogByTimeNick(
            String shopId,
            Date startDate,
            Date endDate,
            String nick
    );

	ShopLoginResult selectCsLoginDetailForShopUserAnalysis(UserAnalysisParam param);

    List<PesUserLoginLogVo> searchLoginCountLogByTimeNick(
            String shopId,
            Date startDate,
            Date endDate,
            String nick
    );

    int getUserLoginCountByNickAndShopAndTime(String shopId,
                                              Date startDate,
                                              Date endDate,
                                              String nick);
}
