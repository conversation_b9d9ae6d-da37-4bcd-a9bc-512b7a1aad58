package com.pes.jd.business.main;

import com.pes.jd.model.DTO.DeptFilerDTO;
import com.pes.jd.model.Param.DeptInfoParam;
import com.pes.jd.model.VO.DeptShopVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/3 14:16
 */
public interface DeptBusiness {

    boolean loginValiadte(String username, String password, Map map) throws Exception;

    boolean uploadExcelInfo(List<DeptInfoParam> deptInfoParamList, String filerId) throws Exception;

    Map<String, Object> getDeptRelation(String id, String deptId, String type)throws Exception;

    List<Long> getShopIdByDeptIdAndType(String id, String deptId, String type)throws Exception;

    List<DeptShopVO> getShopByDeptIdAndType(String id, String deptId, String type, String shopParam)throws Exception;

    List<DeptFilerDTO> listDeptFiler()throws Exception;

    boolean uploadFilerInfo(String filerName)throws Exception;

    List<Long> getShopIdByDeptIdAndTypeAndRtDb(String id, String deptId, String type, String rtDb) throws Exception;

    boolean selfLogin(String username, String password, Map map) throws Exception;

    List<Long> getSelfShopIdByDeptId(Integer deptId) throws Exception;
}
