  
package com.pes.jd.business.main;

import com.pes.jd.model.DO.UserReportProperty;

import java.util.List;

/**
 * ClassName:UserReportPropertyBusiness <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午2:50:38 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see
 */
public interface UserReportPropertyBusiness {
    List<String> getUserReportPropertyByUserReturnList(Integer type);

    UserReportProperty getUserReportPropertyByUser(Integer type);

    UserReportProperty getUserReportPropertyByUser(UserReportProperty record);

    int insertUserReportProperty(UserReportProperty record);

    int insertOrUpdate(UserReportProperty record);

}
  
