package com.pes.jd.business.main.impl;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.main.MarketingActivityV2Business;
import com.pes.jd.dao.main.MarketingActivityLogDao;
import com.pes.jd.dao.main.MarketingActivityV2Dao;
import com.pes.jd.dao.main.ShopDao;
import com.pes.jd.dao.main.ShopSubscribeDao;
import com.pes.jd.model.DO.MarketingActivityLog;
import com.pes.jd.model.DO.MarketingActivityV2;
import com.pes.jd.model.DTO.MarketingActivityV2DTO;
import com.pes.jd.model.DTO.MarketingActivityV2RespDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.util.ModelMapperUtils;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yuanxun
 * @Date: 13:40 2019/10/29
 * @Description:
 */
@Service
public class MarketingActivityV2BusinessImpl implements MarketingActivityV2Business {
    private final Logger logger = LoggerFactory.getLogger(MarketingActivityV2BusinessImpl.class);
    @Autowired
    private MarketingActivityV2Dao marketingActivityV2Dao;
    @Autowired
    private ShopSubscribeDao shopSubscribeDao;
    @Autowired
    private MarketingActivityLogDao marketingActivityLogDao;
    @Autowired
    private ShopDao shopDao;

    @Override
    public int insert(MarketingActivityV2DTO dto) {
        try {
            ModelMapper modelMapper = ModelMapperUtils.modelMapper();
            MarketingActivityV2 marketingActivityV2 = new MarketingActivityV2();
            modelMapper.map(dto, marketingActivityV2);
            marketingActivityV2.setEnableSwitch(Boolean.FALSE);
            //sort
            String sortedVersion = Arrays.stream(marketingActivityV2.getVersion().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .sorted()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            marketingActivityV2.setVersion(sortedVersion);
            marketingActivityV2.setCreated(LocalDateTime.now());
            logger.info("===>insert: {}", JSONObject.toJSONString(marketingActivityV2));
            return marketingActivityV2Dao.insert(marketingActivityV2);
        }catch (Exception e){
            logger.error(e.getMessage());
        }
        return 0;
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return marketingActivityV2Dao.deleteByPrimaryKey(id);
    }

    @Override
    public int update(MarketingActivityV2DTO updatedActivity) {
        // Retrieve existing entity by ID
        MarketingActivityV2 existingActivity = marketingActivityV2Dao.getByPrimaryKey(updatedActivity.getId());

        logger.info("===>update1: {}", JSONObject.toJSONString(existingActivity));

        // Update fields if they are not null
        if (updatedActivity.getActivityName() != null &&
                !updatedActivity.getActivityName().equals(existingActivity.getActivityName())) {
            existingActivity.setActivityName(updatedActivity.getActivityName());
        }

        if (updatedActivity.getActivityContent() != null &&
                !updatedActivity.getActivityContent().equals(existingActivity.getActivityContent())) {
            existingActivity.setActivityContent(updatedActivity.getActivityContent());
        }

        if (updatedActivity.getStartDate() != null &&
                !updatedActivity.getStartDate().equals(existingActivity.getStartDate())) {
            existingActivity.setStartDate(updatedActivity.getStartDate());
        }

        if (updatedActivity.getEndDate() != null &&
                !updatedActivity.getEndDate().equals(existingActivity.getEndDate())) {
            existingActivity.setEndDate(updatedActivity.getEndDate());
        }

        if (updatedActivity.getOrderType() != null &&
                !updatedActivity.getOrderType().equals(existingActivity.getOrderType())) {
            existingActivity.setOrderType(updatedActivity.getOrderType());
        }

        if (updatedActivity.getEffectDate() != null &&
                !updatedActivity.getEffectDate().equals(existingActivity.getEffectDate())) {
            existingActivity.setEffectDate(updatedActivity.getEffectDate());
        }

        if (updatedActivity.getFrequency() != null &&
                !updatedActivity.getFrequency().equals(existingActivity.getFrequency())) {
            existingActivity.setFrequency(updatedActivity.getFrequency());
        }

        if(updatedActivity.getVersion() != null){
            String sortedVersion = Arrays.stream(updatedActivity.getVersion().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .sorted()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            if (!sortedVersion.equals(existingActivity.getVersion())) {
                existingActivity.setVersion(sortedVersion);
            }
        }

        if (updatedActivity.getUrl() != null &&
                !updatedActivity.getUrl().equals(existingActivity.getUrl())) {
            existingActivity.setUrl(updatedActivity.getUrl());
        }

        if (updatedActivity.getEnableSwitch() != null &&
                !updatedActivity.getEnableSwitch().equals(existingActivity.getEnableSwitch())) {
            existingActivity.setEnableSwitch(updatedActivity.getEnableSwitch());
        }

        if (updatedActivity.getActivitySort() != null &&
                updatedActivity.getActivitySort() != existingActivity.getActivitySort().intValue()) {
            existingActivity.setActivitySort(updatedActivity.getActivitySort());
        }

        try {
            logger.info("===>update2: {}", JSONObject.toJSONString(existingActivity));
            return marketingActivityV2Dao.updateByPrimaryKey(existingActivity);
        }catch (Exception e){
            logger.error(e.getMessage());
        }
        return 0;
    }

    @Override
    public int updateEnable(Long id, Boolean enableSwitch) {

        MarketingActivityV2 activity = new MarketingActivityV2();
        activity.setId(id);
        activity.setEnableSwitch(enableSwitch);
        return marketingActivityV2Dao.updateByPrimaryKey(activity);
    }

    @Override
    public List<MarketingActivityV2RespDTO> selectEnableActivity(Long shopId, Integer shopType, LocalDateTime currentTime, String csNick) {
        List<String> versions = shopType == 0 ? MarketingActivityV2DTO.AppVersion.getPopVersions() : MarketingActivityV2DTO.AppVersion.getSelfVersions();
        List<MarketingActivityV2> activityLst = marketingActivityV2Dao.selectEnableActivity(shopType, currentTime);
        ModelMapper modelMapper = ModelMapperUtils.modelMapper();
        List<MarketingActivityV2RespDTO> result = new ArrayList<>();
        for(MarketingActivityV2 activity : activityLst){
            //fixme
            //弹出频率和活动弹框关闭操作
            Integer frequency = activity.getFrequency();
            LocalDate now = LocalDate.now();
            //判断周期内是否有数据
//            if(frequency != 0){
//
//            }
            //是否有不再提醒
            LocalDateTime neverRemindStart = now.minusDays(frequency == 0 ? frequency : frequency-1).atStartOfDay();
            List<MarketingActivityLog> neverRemindList = marketingActivityLogDao.queryByShopAndCsNick(shopId, csNick, neverRemindStart, activity.getId(), 4);
            logger.info("======>neverRemindList: {}", JSONObject.toJSONString(neverRemindList));
            //是否有不再提醒
            if(!neverRemindList.isEmpty()){
                continue;
            }
            LocalDateTime popStartDate = now.minusDays(frequency-1).atStartOfDay();
            List<MarketingActivityLog> activityLogList = marketingActivityLogDao.queryByShopAndCsNick(shopId, csNick, popStartDate, activity.getId(), 3);
            logger.info("======>activityLogList: {}", JSONObject.toJSONString(activityLogList));
            //频率内是否有弹框
            if(!activityLogList.isEmpty()){
                continue;
            }
            //到期时间是否满足
            ShopDTO shopDTO = shopDao.selectShopByShopId(shopId);
            Date subscribeDeadLine = shopDTO.getSubscribeDeadLine();
            LocalDate deadline = subscribeDeadLine.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            long daysBeforeDeadline = ChronoUnit.DAYS.between(LocalDate.now(), deadline);
            logger.info("======>daysBeforeDeadline: {}", daysBeforeDeadline);
            MarketingActivityV2DTO.EffectDate effectDateScope = MarketingActivityV2DTO.EffectDate.fromValue(activity.getEffectDate());
            //check scope
            if(daysBeforeDeadline <= effectDateScope.getScope().get(0) || daysBeforeDeadline > effectDateScope.getScope().get(1)){
                continue;
            }
            //用户类型是否匹配(试用/订购/续费)
            List<ShopSubScribe> subLst = shopSubscribeDao.selectValidShopSubScribeByShopIdOrderByStart(shopId, versions);
            if(subLst.isEmpty()) continue;
            ShopSubScribe latestSub = subLst.get(subLst.size() - 1);//get last sub
            String itemCode = latestSub.getItemCode();
            if(!MarketingActivityV2DTO.AppVersion.fromValue(activity.getVersion()).contains(itemCode)){
                continue;
            }
            Integer orderType = activity.getOrderType();
            logger.info("======>orderType: {}", orderType);
            if(orderType != 0){
                boolean isTryBefore = false;//是否试用
                boolean isContinueBuying = false; //是否订购
                int subType = 0; //订购类型
                //只有1条，且这条为7或15天为试用
                //只有1条，且这条不为7或15天，为订购
                //只有2条，且其中有1条为7或15天，为订购；都不为7或15天，为续订
                //大于等于3条，都为续订
                if(subLst.size() == 1){
                    if(Arrays.asList(7, 15).contains(Math.abs(subLst.get(0).getOrderCycle()))){
                        subType = 1;
                    }else{
                        subType = 2;
                    }
                }else if(subLst.size() == 2){
                    boolean match = subLst.stream().anyMatch(t -> Arrays.asList(7, 15).contains(Math.abs(t.getOrderCycle())));
                    if(match){
                        subType = 2;
                    }else{
                        subType = 3;
                    }
                }else{
                    subType = 3;
                }
                logger.info("======>subType: {}", subType);
                //订购类型不匹配
                if(orderType != subType){
                    continue;
                }
//                ShopSubScribe firstSub = subLst.get(0);
//                logger.info("======>firstSub: {}", JSONObject.toJSONString(firstSub));
//                if(Arrays.asList(7, 15).contains(Math.abs(firstSub.getOrderCycle()))){
//                    isTryBefore = true;
//                    subLst.remove(firstSub);//remove
//                }
//                if(!subLst.isEmpty()){
//                    isContinueBuying = true;
//                }
//                if(isTryBefore && isContinueBuying){//续费
//                    subType = 3;
//                }else if(isTryBefore){//试用
//                    subType = 1;
//                }else if(isContinueBuying){
//                    subType = 2; //订购
//                }else {
//                    continue;
//                }

            }
            MarketingActivityV2RespDTO thisDTO = new MarketingActivityV2RespDTO();
            modelMapper.map(activity, thisDTO);
            result.add(thisDTO);
        }
        return result;
    }

    @Override
    public List<MarketingActivityV2RespDTO> selectActivityByActivityNameAndDate(Date startDate, Date endDate, String activityName, Integer shopType) {
        List<MarketingActivityV2> marketingActivityV2List = marketingActivityV2Dao.selectActivityByActivityNameAndDate(startDate, endDate, activityName,shopType);
        if (marketingActivityV2List == null || marketingActivityV2List.isEmpty()) {
            return Collections.emptyList();
        }
        ModelMapper modelMapper = ModelMapperUtils.modelMapper();
        return marketingActivityV2List.stream().map(t -> {
            MarketingActivityV2RespDTO marketingActivityV2DTO = new MarketingActivityV2RespDTO();
            modelMapper.map(t, marketingActivityV2DTO);
            return marketingActivityV2DTO;
        }).collect(Collectors.toList());
    }

}
  
