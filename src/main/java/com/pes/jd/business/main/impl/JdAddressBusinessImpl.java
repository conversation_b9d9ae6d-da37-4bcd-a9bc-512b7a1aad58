package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.JdAddressBusiness;
import com.pes.jd.mapper.main.JdAddressMapper;
import com.pes.jd.model.DO.JdAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/3 14:16
 */

@Service
public class JdAddressBusinessImpl implements JdAddressBusiness {

    @Autowired
    private JdAddressMapper jdAddressMapper;

    @Override
    public List<JdAddress> getAllJdAddressData() {
        return jdAddressMapper.getAllJdAddressData();
    }

    @Override
    public int batchInsert(List<JdAddress> addressList) {
        return jdAddressMapper.batchInsert(addressList);
    }

    @Override
    public int deleteAll() {
        return jdAddressMapper.deleteAll();
    }

    @Override
    public LocalDateTime getLatestCreateTime() {
        return jdAddressMapper.getLatestCreateTime();
    }
}
