package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.ShopDBRelationBusiness;
import com.pes.jd.mapper.main.ShopDBRelationMapper;
import com.pes.jd.model.DTO.ShopDBRelationDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: aiJun
 * @Date: 2019-05-15 18:59
 * @Version 1.0
 */
@Service
public class ShopDBRelationBusinessImpl implements ShopDBRelationBusiness {

    @Resource
    private ShopDBRelationMapper shopDBRelationMapper;

    @Override
    public List<ShopDBRelationDTO> searchShopDBRelationByType(int type) {

        return shopDBRelationMapper.searchShopDBRelationByType(type);
    }

    @Override
    public List<ShopDBRelationDTO> searchShopDBNameAndSchemaIdByType(int type) {

        return shopDBRelationMapper.searchShopDBNameAndSchemaIdByType(type);
    }

    @Override
    public List<ShopDBRelationDTO> searchShopDBRelationByDbNameAndSchemaIdAndType(String dbName, String schemaId, int type) {

        return shopDBRelationMapper.searchShopDBRelationByDbNameAndSchemaIdAndType(dbName,schemaId,1);
    }
}
