package com.pes.jd.business.main.impl;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.pes.jd.business.main.TutorialVideoBusiness;
import com.pes.jd.dao.main.TutorialVideoDao;
import com.pes.jd.model.DTO.TutorialVideoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 15:27 2019/10/14
 * @Description:
 */
@Service
public class TutorialVideoBusinessImpl implements TutorialVideoBusiness {

    @Autowired
    private TutorialVideoDao tutorialVideoDao;

    @Override
    public List<TutorialVideoDTO> selectVideoByVideoName(String videoName) {
        List<TutorialVideoDTO> tutorialVideoDTOS = tutorialVideoDao.selectVideoByName(StrUtil.isNotBlank(videoName) ? videoName.trim() : videoName);
        return tutorialVideoDTOS.isEmpty() ? Lists.newArrayListWithExpectedSize(0) : tutorialVideoDTOS;
    }

}
  
