
package com.pes.jd.business.main;

import com.pes.jd.model.BO.ShopDbAndSchemeIdBO;
import com.pes.jd.model.DO.Shop;
import com.pes.jd.model.DO.ShopDetail;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.JdSystemPageParam;
import com.pes.jd.model.Result.JobShopResult;
import com.pes.jd.model.VO.AdminShopVO;
import com.pes.jd.model.VO.ShopVO;
import com.pes.jd.ms.domain.Data.master.BatchRemindSetting;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.ms.domain.Data.task.dispatching.ShopLocationInfo;

import java.util.List;
import java.util.Set;

/**
 * ClassName:ShopBusiness <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月22日 下午1:25:15 <br/>
 * <AUTHOR>
 * @version
 * @since    JDK 1.8
 * @see
 */
public interface ShopBusiness {
	int insertShop(ShopDTO shop);

	ShopDTO selectShopByUserNick(String userNick);

	int updateShop(Shop shop);

    List<ShopVO> searchAllShop();

	ShopDTO selectShopByShopId(Long shopId);

	ShopMsgDataDTO getShopInfoForVenderId(Long venderId);

	ShopLocationInfo getShopLocationInfo(Long shopId);

	int updateShopDetailByShopId(ShopDetail shopDetail);


    ShopDetail getShopDetailByShopId(Long shopId);

	List<AdminShopVO> selectShopByNickOrTitle(String nick, String type);

	int updateShopCreateFlagByShopId(Long shopId);

	void clearVisitCode(ShopDetail shopDetail);

	RealtimeShopDTO getRealTimeShopByShopId(Long shopId);

	ShopDTO selectShopByShopNameorShopId(String shopName);

	List<ShopDTO> selectShopByShopNameorShopId(List<Long> shopIdLst, String shopName, Long shopId1);

	List<ShopVO> selectShopByDateByNickOrTitle(JdSystemPageParam jdSystemPageParam);

	Set<String> getKeyWord(Long shopId);

	 List<BoardMnoitorParamDTO> getShopListByIds(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size);

	int selectShopNumsByDeptIdAndTitle(List<Long> shopIds, String queryParam);



	/**
	 * 根据id集合查询店铺dto
	 * @return
	 */
	List<ShopDTO> selectShopByIds(List<Long> shopIdLst, String shopParam);

    List<String> getRtDbLstByIds(List<Long> shopIds, String queryParam, Integer startIndex, Integer size);

    List<BoardMnoitorParamDTO> getShopListByIdsAndRtdb(List<Long> shopIds, String queryParam, Integer startIndex, Integer size, String rtDb);

	List<ShopDbAndSchemeIdBO> selectShopDbAndSchemaIdByShopTitle(String shopTitle, Integer type);

	List<ShopDbAndSchemeIdBO> selectDbByShopIds(List<Long> shopIdLst);

	List<SgShopDTO> queryShopInfoLst(Integer shopType);

	List<ShopUrge> selectUrgeShopByShopIdLstByType(List<Long> shopIdLst, Integer type);

	void updateInitFlag(Long shopId);

	List<Shop> queryShopInfoList(Integer shopType);

    JobShopDTO getJobShopInfoById(Long valueOf);

	JobShopResult getJobShop(Long shopId);

    List<CsDTO> getCsLst(Long shopId);

	List<ShopAccountDTO> getShopSubUserLst(Long shopId);

	BatchRemindSetting getBatchRemindSetting(Long shopId, JobShopDTO shop);

	List<String> getSysWordLst();

	List<JobShopResult> getAllActiveJobShop(Integer shopTyepInteger);

	PullSubscribeDTO getAllActiveJobShopByShopId(Long shopId);

	List<String> selectShopCsNickLst(Long shopId, String groupId);

	com.pes.jd.model.DTO.JobShopDTO getShopSplitByShopId(Long shopId);

	ShopSplitKeyDTO getShopSplitKeyInfo(Long shopId);

	int getTypeByShopId(String shopId);

	 ShopDTO selectShopBySessionKey(String sessionKey);

	List<ShopDTO> listShopInfoByShopIds(List<Long> shopIdList);

	List<JobShopDTO> selectActiveShopLst();
}

