package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.MenuDotBusiness;
import com.pes.jd.constants.PermissionConstants;
import com.pes.jd.dao.main.MenuDotDao;
import com.pes.jd.dao.main.PesUserLoginLogDao;
import com.pes.jd.dao.main.ShopDao;
import com.pes.jd.model.DO.MenuDotDO;
import com.pes.jd.model.DTO.MenuDotDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DotUserVO;
import com.pes.jd.model.VO.DotVO;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MenuDotBusinessImpl implements MenuDotBusiness {
    private static final Logger logger = LoggerFactory.getLogger(MenuDotBusinessImpl.class);

    @Autowired
    private MenuDotDao menuDotDao;

    @Autowired
    private ShopDao shopDao;

    @Autowired
    private PesUserLoginLogDao pesUserLoginLogDao;

    @Override
    public Integer inserMenuDot(MenuDotDO menuDotDO) throws Exception {
        Integer count = 0;
        try {
            Date sTimeOfDate = DateUtils.getStartTimeOfDate(new Date());

            menuDotDO.setDotTime(DateUtils.parseYMdHms(DateUtils.formatYMdHms(new Date())));
            //查询店铺名称
            ShopDTO shopDTO = shopDao.selectShopByShopId(menuDotDO.getShopId());
            if (null == shopDTO) {
                return 0;
            }

            //查询该客服属于当天中第几次登陆
            int loginNum = pesUserLoginLogDao.getUserLoginCountByNickAndShopAndTime(String.valueOf(menuDotDO.getShopId()), sTimeOfDate, sTimeOfDate, menuDotDO.getNick());
            menuDotDO.setShopName(shopDTO.getTitle());
            menuDotDO.setLoginNum(Long.valueOf(loginNum));
            count = menuDotDao.inserMenuDot(menuDotDO);
        } catch (Exception e) {
            logger.error(this.getClass().getName(), "inserMenuDot()-异常", e);
        }
        return count;
    }

    /**
     * 查询事件分析
     *
     * @param sDate
     * @param eDate
     * @return
     * @throws Exception
     */
    @Override
    public ApiResponse listMenuDotByDate(Date sDate, Date eDate, String pageName) throws Exception {

        try {
            Map result = new HashMap<>();
            List<MenuDotDTO> menuDotDTOS = menuDotDao.listMenuDotByDate(sDate, eDate, pageName);
            Map<String, DotVO> trendInfos = new HashMap<>();
            Map<String, DotVO> menuInfos = new HashMap<>();
            if (menuDotDTOS.isEmpty()) {
                result.put("trendInfos", trendInfos);
                result.put("menus", menuInfos);
                result.put("count", new DotVO());
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
            }
            //计算总指标
            Map<String, Object> totalIndex = calTotalIndex(menuDotDTOS);

            Map<Date, List<MenuDotDTO>> trendInfoForSource = menuDotDTOS.stream().collect(Collectors.groupingBy(MenuDotDTO::getDotTime));
            //1.趋势图数据计算
            calTrendInfos(trendInfos, trendInfoForSource);

            Map<String, List<MenuDotDTO>> menuInfoForSource = menuDotDTOS.stream().collect(Collectors.groupingBy(MenuDotDTO::getPageVisitName));
            //2.计算菜单数据
            calMenuInfos(menuInfos, menuInfoForSource, (Double) totalIndex.get("totalPageVisitTime"), (Double) totalIndex.get("totalNicks"), (Double) totalIndex.get("totalNicksAll"));
            //3.汇总数据计算
            DotVO dotVO = calCount(trendInfos);
            result.put("trendInfos", supplyInfo(trendInfos, sDate, eDate));
            result.put("menus", menuInfos);
            result.put("count", dotVO);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
        } catch (Exception e) {
            logger.error(this.getClass().getName(), "listMenuDotByDate()-异常", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
    }

    public Map<String, DotVO> supplyInfo(Map<String, DotVO> trendInfos, Date sDate, Date eDate) throws ParseException {
        String sDateForStr = DateUtils.formatYMd(sDate);
        String eDateForStr = DateUtils.formatYMd(eDate);
        List<String> dates = DateUtils.findDates(sDateForStr, eDateForStr);
        Map<String, DotVO> stringDotVOHashMap = new HashMap<>();

        for (String date : dates) {
            DotVO dotVO = trendInfos.get(date);
            if (null == dotVO) {
                stringDotVOHashMap.put(date, new DotVO(0.0, 0.0, 0.0, 0.0, "", 0.0, 0.0, 0.0, 0.0, 0.0, "", "", 0.0));
            } else {
                stringDotVOHashMap.put(date, dotVO);
            }
        }
        return new TreeMap<String, DotVO>(stringDotVOHashMap);
    }

    /**
     * 计算汇总数据
     *
     * @param trendInfos
     * @return
     */
    public DotVO calCount(Map<String, DotVO> trendInfos) {
        DotVO dotVO = new DotVO();
        //页面访问次数-汇总
        Double pageVisitTime = 0.0;
        //次均停留时间-汇总
        Double averageStopTime = 0.0;
        //店铺访问数-汇总
        Double shopVisitTime = 0.0;
        //访问用户数-汇总
        Double nickVisitTime = 0.0;

        for (Map.Entry maps : trendInfos.entrySet()) {
            DotVO value = (DotVO) maps.getValue();
            pageVisitTime += Double.valueOf(value.getPageVisitTime());
            averageStopTime += Double.valueOf(value.getAverageStopTime());
            shopVisitTime += Double.valueOf(value.getShopVisitTime());
            nickVisitTime += Double.valueOf(value.getNickVisitTime());
        }
        dotVO.setPageVisitTime(pageVisitTime);
        dotVO.setAverageStopTime(averageStopTime);
        dotVO.setShopVisitTime(shopVisitTime);
        dotVO.setNickVisitTime(nickVisitTime);
        return dotVO;
    }

    /**
     * 查询事件分析详情
     *
     * @param shopName
     * @param nick
     * @param pageVisitName
     * @return
     * @throws Exception
     */
    @Override
    public ApiResponse getDotInfoDetails(String shopName, String nick, String pageVisitName, Date startDate, Date endDate) throws Exception {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            List<DotVO> resultList = new ArrayList<>();
            List<MenuDotDTO> menuDotDTOS = menuDotDao.listMenuDotByShopNameAndNick(shopName, nick, pageVisitName, startDate, endDate);
            Map<Long, List<MenuDotDTO>> menuDotDTOSForShop = menuDotDTOS.stream().collect(Collectors.groupingBy(MenuDotDTO::getShopId));
            for (Map.Entry maps : menuDotDTOSForShop.entrySet()) {
                //客服昵称
                Long shopId = (Long) maps.getKey();
                List<MenuDotDTO> menuDotDTOList = (List<MenuDotDTO>) maps.getValue();
                Map<String, List<MenuDotDTO>> menuDotDTOSForNick = menuDotDTOList.stream().collect(Collectors.groupingBy(MenuDotDTO::getNick));
                for (Map.Entry maps2 : menuDotDTOSForNick.entrySet()) {
                    //客服昵称
                    String shopNameForSource = "";
                    DotVO dotVO = new DotVO();
                    String key = (String) maps2.getKey();
                    dotVO.setNick(key);
                    //店铺名称
                    List<MenuDotDTO> value = (List<MenuDotDTO>) maps.getValue();
                    MenuDotDTO menuDotDTO = value.get(0);
                    dotVO.setShopName(menuDotDTO.getShopName());
                    //访问次数
                    Integer size = value.size();
                    Double pageVisitTime = Double.valueOf(size);
                    dotVO.setPageVisitTime(pageVisitTime);
                    //访问时长
                    Double visitDuration = 0.0;
                    for (MenuDotDTO dotDTO : value) {
                        visitDuration += dotDTO.getPageStopTime();
                    }
                    dotVO.setVisitDuration(visitDuration);
                    resultList.add(dotVO);
                }
            }
            resultMap.put("nickInfos", resultList);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, resultMap);
        } catch (Exception e) {
            logger.error(this.getClass().getName(), "getDotInfoDetails()-异常", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
    }

    /**
     * 使用习惯信息查询
     *
     * @param sDate
     * @param eDate
     * @return
     * @throws Exception
     */
    @Override
    public ApiResponse getUserInfo(Date sDate, Date eDate) throws Exception {
        try {
            Map<String, Object> result = new HashMap<>();
            List<MenuDotDTO> menuDotDTOS = menuDotDao.listMenuDotByDate(sDate, eDate, null);
            if (menuDotDTOS.isEmpty()) {
                result.put("userRates", new ArrayList<DotUserVO>());
                result.put("visitDepths", new ArrayList<DotUserVO>());
                result.put("userDurations", new ArrayList<DotUserVO>());
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
            }
            //过滤longNum 为null的
            List<MenuDotDTO> menuDotDTOS1 = handlerLoginNum(menuDotDTOS);
            //获取打点数据
            Map<Long, List<MenuDotDTO>> menuDotForShop = menuDotDTOS1.stream().collect(Collectors.groupingBy(MenuDotDTO::getShopId));
            //使用频率
//            List<DotUserVO> dotUserVOS = calUserIndex(menuDotForShop,PermissionConstants.DOT_USER_TYPE_1,totalLoginCount);
            List<DotUserVO> dotUserVOS = calUserRates(menuDotForShop);
            //补充数据
            supplyUserData(dotUserVOS);
            result.put("userRates", sortedForUserIndex(dotUserVOS));
            //访问深度
//            List<DotUserVO> dotUserVOS1 = calUserIndex(menuDotForShop, PermissionConstants.DOT_USER_TYPE_2,totalLoginCount);
            List<DotUserVO> dotUserVOS1 = calVisitDepths(menuDotForShop);
            //补充数据
            supplyUserData(dotUserVOS1);
            result.put("visitDepths", sortedForUserIndex(dotUserVOS1));
            //使用时长
//            List<DotUserVO> dotUserVOS2 = calUserIndex(menuDotForShop, PermissionConstants.DOT_USER_TYPE_3,totalLoginCount);
            List<DotUserVO> dotUserVOS2 = calUserDurations(menuDotForShop);
            //补充数据
            supplyUserDataForUserDurations(dotUserVOS2);
            result.put("userDurations", sortedForUserIndex(dotUserVOS2));
            //使用间隔
//            List<DotUserVO> dotUserVOS3 = calUserIndex(menuDotForShop, PermissionConstants.DOT_USER_TYPE_4);
//            result.put("userIntervals",dotUserVOS3);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
        } catch (Exception e) {
            logger.error(this.getClass().getName(), "getUserInfo()-异常", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
    }

    /**
     * 过滤loginNum 为null
     *
     * @param menuDotDTOS
     * @return
     */
    public List<MenuDotDTO> handlerLoginNum(List<MenuDotDTO> menuDotDTOS) {
        List<MenuDotDTO> menuDotDTOS2 = new ArrayList<>();
        for (MenuDotDTO menuDotDTO : menuDotDTOS) {
            if (null != menuDotDTO.getLoginNum()) {
                menuDotDTOS2.add(menuDotDTO);
            }
        }
        return menuDotDTOS2;
    }

    public List<DotUserVO> calUserDurations(Map<Long, List<MenuDotDTO>> menuDotForShop) {
        List<DotUserVO> dotUserForTotal = new ArrayList<>();
        List<DotUserVO> dotUserVOList = new ArrayList<>();
        //总启动次数
        Double totalLoginNum = 0.0;
        for (Map.Entry maps : menuDotForShop.entrySet()) {
            Double loginNum = 0.0;
            List<MenuDotDTO> value = (List<MenuDotDTO>) maps.getValue();
            Map<Long, List<MenuDotDTO>> collect = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getLoginNum));
            for (Map.Entry maps2 : collect.entrySet()) {
                DotUserVO dotUserVO = new DotUserVO();
                List<MenuDotDTO> value1 = (List<MenuDotDTO>) maps2.getValue();
                //使用时长
                Double userTime = 0.0;
                for (MenuDotDTO menuDotDTO : value1) {
                    Long pageStopTime = (null == menuDotDTO.getPageStopTime() ? 0l : menuDotDTO.getPageStopTime());
                    userTime += pageStopTime;
                }
                Integer type = getTypeForTime(userTime == 0.0 ? 0.0 : userTime / 60);
                dotUserVO.setType(type);
                //启动次数
                dotUserVO.setLoginNum(1.0);
                totalLoginNum++;
                dotUserVOList.add(dotUserVO);
            }
        }

        //汇总后的计算
        Map<Integer, List<DotUserVO>> dotUserVoForSource = dotUserVOList.stream().collect(Collectors.groupingBy(DotUserVO::getType));
        for (Map.Entry maps : dotUserVoForSource.entrySet()) {
            DotUserVO dotUserVO = new DotUserVO();
            //类型
            dotUserVO.setType((Integer) maps.getKey());
            //启动次数
            Double index = 0.0;
            //启动用户数
            Double nickNum = 0.0;
            List<DotUserVO> value = (List<DotUserVO>) maps.getValue();
            for (DotUserVO userVO : value) {
                index += userVO.getLoginNum();
            }
            dotUserVO.setLoginNum(index);
            //所占比例
            dotUserVO.setUserProportion(totalLoginNum == 0.0 ? 0.0 : index / totalLoginNum);
            dotUserForTotal.add(dotUserVO);
        }
        return dotUserForTotal;
    }

    /**
     * 计算访问深度
     *
     * @param menuDotForShop
     * @return
     */
    public List<DotUserVO> calVisitDepths(Map<Long, List<MenuDotDTO>> menuDotForShop) {
        List<DotUserVO> dotUserForTotal = new ArrayList<>();
        List<DotUserVO> dotUserVOList = new ArrayList<>();
        //总启动次数
        Double totalLoginNum = 0.0;
        for (Map.Entry maps : menuDotForShop.entrySet()) {
            Double loginNum = 0.0;
            List<MenuDotDTO> value = (List<MenuDotDTO>) maps.getValue();
            Map<Long, List<MenuDotDTO>> collect = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getLoginNum));
            for (Map.Entry maps2 : collect.entrySet()) {
                DotUserVO dotUserVO = new DotUserVO();
                List<MenuDotDTO> value1 = (List<MenuDotDTO>) maps2.getValue();
                //访问次数
                int size = value1.size();
                Integer type = getType(size);
                dotUserVO.setType(type);
                //启动次数
                dotUserVO.setLoginNum(1.0);
                totalLoginNum++;
                dotUserVOList.add(dotUserVO);
            }
        }
        //汇总后的计算
        Map<Integer, List<DotUserVO>> dotUserVoForSource = dotUserVOList.stream().collect(Collectors.groupingBy(DotUserVO::getType));
        for (Map.Entry maps : dotUserVoForSource.entrySet()) {
            DotUserVO dotUserVO = new DotUserVO();
            //类型
            dotUserVO.setType((Integer) maps.getKey());
            //启动次数
            Double index = 0.0;
            //启动用户数
            Double nickNum = 0.0;
            List<DotUserVO> value = (List<DotUserVO>) maps.getValue();
            for (DotUserVO userVO : value) {
                index += userVO.getLoginNum();
            }
            dotUserVO.setLoginNum(index);
            //所占比例
            dotUserVO.setUserProportion(totalLoginNum == 0.0 ? 0.0 : index / totalLoginNum);
            dotUserForTotal.add(dotUserVO);
        }
        return dotUserForTotal;
    }

    /**
     * 计算使用频率
     *
     * @param menuDotForShop
     * @return
     */
    public List<DotUserVO> calUserRates(Map<Long, List<MenuDotDTO>> menuDotForShop) {
        List<DotUserVO> dotUserForTotal = new ArrayList<>();
        List<DotUserVO> dotUserVOList = new ArrayList<>();
        //总启动用户数
        Double totalNickNum = 0.0;
        for (Map.Entry maps : menuDotForShop.entrySet()) {
            DotUserVO dotUserVO = new DotUserVO();
            Double loginNum = 0.0;
            Integer loginNumForInt = 0;
            List<MenuDotDTO> value = (List<MenuDotDTO>) maps.getValue();
//            for (MenuDotDTO menuDotDTO : value) {
//                if(null == menuDotDTO.getLoginNum()){
//                    continue;
//                }
//            }
            Map<Long, List<MenuDotDTO>> collect = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getLoginNum));
            for (Map.Entry maps2 : collect.entrySet()) {
                //启动次数
                loginNum++;
                loginNumForInt++;
            }
            dotUserVO.setLoginNum(loginNum);
            Integer type = getType(loginNumForInt);
            dotUserVO.setType(type);
            //启动用户数
            int size = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getNick)).size();
            totalNickNum += Double.valueOf(size);
            dotUserVO.setNickNum(Double.valueOf(size));
            dotUserVOList.add(dotUserVO);
        }
        //汇总后的计算
        Map<Integer, List<DotUserVO>> dotUserVoForSource = dotUserVOList.stream().collect(Collectors.groupingBy(DotUserVO::getType));
        for (Map.Entry maps : dotUserVoForSource.entrySet()) {
            DotUserVO dotUserVO = new DotUserVO();
            //类型
            dotUserVO.setType((Integer) maps.getKey());
            //启动次数
            Double index = 0.0;
            //启动用户数
            Double nickNum = 0.0;
            List<DotUserVO> value = (List<DotUserVO>) maps.getValue();
            for (DotUserVO userVO : value) {
                index += userVO.getLoginNum();
                nickNum += userVO.getNickNum();
            }
            dotUserVO.setLoginNum(index);
            //启动用户数
            dotUserVO.setNickNum(nickNum);
            //所占比例
            dotUserVO.setUserProportion(totalNickNum == 0.0 ? 0.0 : nickNum / totalNickNum);
            dotUserForTotal.add(dotUserVO);
        }
        return dotUserForTotal;
    }

    /**
     * 使用习惯按照type 自然排序
     *
     * @param indexs
     * @return
     */
    public List<DotUserVO> sortedForUserIndex(List<DotUserVO> indexs) {
        List<DotUserVO> collect = indexs.stream().sorted(Comparator.comparing(DotUserVO::getType)).collect(Collectors.toList());
        return collect;
    }

    /**
     * 计算使用指标
     *
     * @param menuDotForShop
     * @param dotUserType
     * @return
     */
    public List<DotUserVO> calUserIndex(Map<Long, List<MenuDotDTO>> menuDotForShop, Integer dotUserType, Map<Long, Integer> totalLoginCount) {

        Double totalNickNum = 0.0;
        Double totalIndex = 0.0;
        List<DotUserVO> dotUserForTotal = new ArrayList<>();
        List<DotUserVO> dotUserForShop = new ArrayList<>();
        //启动次数
        for (Map.Entry maps : menuDotForShop.entrySet()) {
            Long userTime = 0l;
            Integer index = 0;
            DotUserVO dotUserVO = new DotUserVO();
            Long shopId = (Long) maps.getKey();
            List<MenuDotDTO> value = (List<MenuDotDTO>) maps.getValue();
            if (value.isEmpty()) {
                continue;
            }
            //启动用户数
            Integer nickNum = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getNick)).size();
            //总用户数
            totalNickNum += nickNum;
            for (MenuDotDTO menuDotDTO : value) {
                if (totalLoginCount.containsKey(shopId)) {
                    index = totalLoginCount.get(shopId);
                } else {
                    index = 0;
                }
                totalIndex += index;
                userTime += menuDotDTO.getPageStopTime();
            }
            Integer type = null;
            if (PermissionConstants.DOT_USER_TYPE_1.equals(dotUserType)) {
                type = getType(index);
            }
            if (PermissionConstants.DOT_USER_TYPE_2.equals(dotUserType)) {
                type = getType(value.size());
            }
            if (PermissionConstants.DOT_USER_TYPE_3.equals(dotUserType)) {
                type = getTypeForTime(Double.valueOf(String.valueOf(userTime)) / 60);
                logger.info("计算使用时长：dotUserType-{}", dotUserType);
                logger.info("计算使用时长：userTime-{}", userTime);
                logger.info("计算使用时长：Double.valueOf(String.valueOf(userTime))-{}", Double.valueOf(String.valueOf(userTime)));
                logger.info("计算使用时长：type-{}", type);
            }
            if (PermissionConstants.DOT_INDEX_TYPE_0.equals(type)) {
                return dotUserForTotal;
            }
            //启动次数
            dotUserVO.setLoginNum(Double.valueOf(index));
            //启动用户数
            dotUserVO.setNickNum(Double.valueOf(nickNum));
            //类型
            dotUserVO.setType(type);
            dotUserForShop.add(dotUserVO);
        }
        if (dotUserForShop.isEmpty()) {
            return dotUserForTotal;
        }
        //汇总后的计算
        Map<Integer, List<DotUserVO>> dotUserVoForSource = dotUserForShop.stream().collect(Collectors.groupingBy(DotUserVO::getType));
        for (Map.Entry maps : dotUserVoForSource.entrySet()) {
            DotUserVO dotUserVO = new DotUserVO();
            //类型
            dotUserVO.setType((Integer) maps.getKey());
            //启动次数
            Double index = 0.0;
            //启动用户数
            Double nickNum = 0.0;
            List<DotUserVO> value = (List<DotUserVO>) maps.getValue();
            for (DotUserVO userVO : value) {
                index += userVO.getLoginNum();
                nickNum += userVO.getNickNum();
            }
            dotUserVO.setLoginNum(index);
            //启动用户数
            dotUserVO.setNickNum(nickNum);
            //所占比例
            if (PermissionConstants.DOT_USER_TYPE_1.equals(dotUserType)) {
                dotUserVO.setUserProportion(totalNickNum == 0.0 ? 0.0 : nickNum / totalNickNum);
            } else {
                dotUserVO.setUserProportion(totalIndex != 0.0 ? index / totalIndex : 0.0);
            }
            dotUserForTotal.add(dotUserVO);
        }
        return dotUserForTotal;
    }

    /**
     * 补充使用习惯空数据
     *
     * @param dotUserForTotal
     */
    public void supplyUserDataForUserDurations(List<DotUserVO> dotUserForTotal) {
        Map<Integer, List<DotUserVO>> collect = dotUserForTotal.stream().collect(Collectors.groupingBy(DotUserVO::getType));
        Map<Integer, Integer> indexs = new HashMap<>();
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_0, 0);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_1, 1);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_2, 2);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_3, 3);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_4, 4);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_5, 5);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_6, 6);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_7, 7);
        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_0))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_0);
            dotUserForTotal.add(dotUserVO);
        }
        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_1))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_1);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_2))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_2);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_3))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_3);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_4))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_4);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_5))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_5);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_6))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_6);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_7))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_7);
            dotUserForTotal.add(dotUserVO);
        }
    }

    /**
     * 补充使用习惯空数据
     *
     * @param dotUserForTotal
     */
    public void supplyUserData(List<DotUserVO> dotUserForTotal) {
        Map<Integer, List<DotUserVO>> collect = dotUserForTotal.stream().collect(Collectors.groupingBy(DotUserVO::getType));
        Map<Integer, Integer> indexs = new HashMap<>();
        Integer index_01 = PermissionConstants.DOT_INDEX_TYPE_1;
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_1, 1);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_2, 2);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_3, 3);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_4, 4);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_5, 5);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_6, 6);
        indexs.put(PermissionConstants.DOT_INDEX_TYPE_7, 7);
        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_1))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_1);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_2))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_2);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_3))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_3);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_4))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_4);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_5))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_5);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_6))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_6);
            dotUserForTotal.add(dotUserVO);
        }

        if (null == collect.get(indexs.get(PermissionConstants.DOT_INDEX_TYPE_7))) {
            DotUserVO dotUserVO = new DotUserVO(0.0, 0.0, 0.0, PermissionConstants.DOT_INDEX_TYPE_7);
            dotUserForTotal.add(dotUserVO);
        }
    }

    public Integer getTypeForTime(Double index) {

        if (index > 30) {//30以上
            return PermissionConstants.DOT_INDEX_TYPE_7;
        }

        if (index > 25) {//26-30
            return PermissionConstants.DOT_INDEX_TYPE_6;
        }

        if (index > 20) { //21-25
            return PermissionConstants.DOT_INDEX_TYPE_5;
        }

        if (index > 15) { //16-20
            return PermissionConstants.DOT_INDEX_TYPE_4;
        }

        if (index > 10) { //11-15
            return PermissionConstants.DOT_INDEX_TYPE_3;
        }

        if (index > 5) { //6 -10
            return PermissionConstants.DOT_INDEX_TYPE_2;
        }

        if (index > 1) { //1 -5
            return PermissionConstants.DOT_INDEX_TYPE_1;
        }
        //一分钟以内
        return PermissionConstants.DOT_INDEX_TYPE_0;

    }

    /**
     * 获取类型
     *
     * @param index
     * @return
     */
    public Integer getType(Integer index) {

        if (index > 30) {//30以上
            return PermissionConstants.DOT_INDEX_TYPE_7;
        }

        if (index > 25) {//26-30
            return PermissionConstants.DOT_INDEX_TYPE_6;
        }

        if (index > 20) { //21-25
            return PermissionConstants.DOT_INDEX_TYPE_5;
        }

        if (index > 15) { //16-20
            return PermissionConstants.DOT_INDEX_TYPE_4;
        }

        if (index > 10) { //11-15
            return PermissionConstants.DOT_INDEX_TYPE_3;
        }

        if (index > 5) { //6-10
            return PermissionConstants.DOT_INDEX_TYPE_2;
        }

        if (index > 0) { //1-5
            return PermissionConstants.DOT_INDEX_TYPE_1;
        }
        return PermissionConstants.DOT_INDEX_TYPE_0;
    }

    /**
     * 计算总的指标
     *
     * @param menuDotDTOS
     */
    public Map<String, Object> calTotalIndex(List<MenuDotDTO> menuDotDTOS) {

        Map<String, Object> totalIndex = new HashMap<>();
        //总的页面数

        Double totalPageVisitTime = Double.valueOf(menuDotDTOS.stream().filter(e -> e.getPageStopTime() < 600).collect(Collectors.toList()).size());
        totalIndex.put("totalPageVisitTime", totalPageVisitTime);
        //总的店铺访问数
//        Double totalShopVisitTime = Double.valueOf(menuDotDTOS.stream().collect(Collectors.groupingBy(MenuDotDTO::getShopId)).size());
//        totalIndex.put("totalShopVisitTime",totalShopVisitTime);
        //总的访问用户数
        Double totalNicks = 0.0;
        //总的访问用户数(包含10分组以上的)
        Double totalNicksAll = 0.0;
        Map<Long, List<MenuDotDTO>> collectForShop = menuDotDTOS.stream().collect(Collectors.groupingBy(MenuDotDTO::getShopId));
        for (Map.Entry maps : collectForShop.entrySet()) {
            List<MenuDotDTO> collectForNick = (List<MenuDotDTO>) maps.getValue();
            totalNicks += collectForNick.stream().filter(e -> e.getPageStopTime() < 600).collect(Collectors.groupingBy(MenuDotDTO::getNick)).size();
            totalNicksAll += collectForNick.stream().collect(Collectors.groupingBy(MenuDotDTO::getNick)).size();
        }
        totalIndex.put("totalNicks", totalNicks);
        totalIndex.put("totalNicksAll", totalNicksAll);
        //总页面停留时间
//        Long totalPageStopTimeForLong=0l;
//        for (MenuDotDTO menuDotDTO : menuDotDTOS) {
//            totalPageStopTimeForLong+=menuDotDTO.getPageStopTime();
//        }
//        Double totalPageStopTime = Double.valueOf(totalPageStopTimeForLong);
//        totalIndex.put("totalPageStopTime",totalPageStopTime);
        return totalIndex;
    }

    /**
     * 计算趋势图数据
     *
     * @param trendInfos
     * @param trendInfoForSource
     */
    public void calTrendInfos(Map<String, DotVO> trendInfos, Map<Date, List<MenuDotDTO>> trendInfoForSource) {
        //页面总停留时间
//        Double totalPageStopTime = 0.0;
        for (Map.Entry<Date, List<MenuDotDTO>> maps : trendInfoForSource.entrySet()) {
            Date key = maps.getKey();
            //页面访问次数(针对于一天的)
            Double pageVisitTime = 0.0;
            //页面停留时间(针对于一天的)
            Double pageStopTime = 0.0;
            if (null != key) {
                DotVO dotVO = new DotVO();
                List<MenuDotDTO> value = maps.getValue();
                if (value.isEmpty()) {
                    return;
                }
//                pageVisitTime = Double.valueOf(value.size());
                for (MenuDotDTO menuDotDTO : value) {
                    if (menuDotDTO.getPageStopTime() < 600) {
                        pageStopTime += (null != menuDotDTO.getPageStopTime() ? Double.valueOf(menuDotDTO.getPageStopTime()) : 0.0);
                        pageVisitTime++;
                    }
                }
                //页面访问次数(针对于一天的)
                dotVO.setPageVisitTime(pageVisitTime);
                //
                //店铺访问数(针对于一天的)
                Integer shopVisitTime = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getShopId)).size();
                dotVO.setShopVisitTime(Double.valueOf(shopVisitTime));
                //访问用户数(针对于一天的)
                Integer nicks = value.stream().collect(Collectors.groupingBy(MenuDotDTO::getNick)).size();
                dotVO.setNickVisitTime(Double.valueOf(nicks));
                //次均停留时间
                dotVO.setAverageStopTime(pageStopTime / pageVisitTime);
                trendInfos.put(DateUtils.formatYMd(key), dotVO);
            }
        }
    }

    /**
     * 计算菜单数据指标
     *
     * @param menuInfos
     * @param menuInfoForSource
     * @param totalPageVisitTime
     * @param totalNicks
     */
    public void calMenuInfos(Map<String, DotVO> menuInfos, Map<String, List<MenuDotDTO>> menuInfoForSource, Double totalPageVisitTime, Double totalNicks, Double totalNicksAll) {

        //查询店铺订购数
        Integer count = shopDao.selectCountByStatus();
        if (count == 0) {
            return;
        }
        //总的次均停留时间
        Double totalAverageStopTime = 0.0;
        Double totalAverageStopTime2 = 0.0;
        //总店铺订购数
        Double totalShopOrder = Double.valueOf(null != count ? count : 0.0);
        Map<String, DotVO> menuInfo = new HashMap<>();
        for (Map.Entry<String, List<MenuDotDTO>> maps : menuInfoForSource.entrySet()) {
            String key = maps.getKey();
            List<MenuDotDTO> values = maps.getValue();
            //页面访问次数
            Integer pageVisitTime = values.stream().filter(e -> e.getPageStopTime() < 600).collect(Collectors.toList()).size();
            Double pageVisitTimeDouble = Double.valueOf(pageVisitTime);
            //访问店铺数（针对与同一个菜单下）
            Integer shopVisitTime = values.stream().collect(Collectors.groupingBy(MenuDotDTO::getShopId)).size();

            //根据nick去重 得出 访问用户数
            Integer visitNick = values.stream().collect(Collectors.groupingBy(MenuDotDTO::getNick)).size();
            Double visitNickForDouble = null != visitNick ? visitNick : 0.0;

            DotVO dotVO = new DotVO();
            //页面名称
            dotVO.setPageVisitName(key);
            //访问店铺数
            Double shopVisitTimeForDouble = Double.valueOf(null != shopVisitTime ? shopVisitTime : 0);
            dotVO.setShopVisitTime(shopVisitTimeForDouble);
            //访问店铺数占比
            dotVO.setShopVisitTimeProportion(shopVisitTimeForDouble / totalShopOrder);
            //访问用户数
            dotVO.setNickVisitTime(visitNickForDouble);
            //访问用户数占比
            dotVO.setNickVisitTimeProportion(visitNickForDouble / totalNicksAll);
            Double pageStopTime = 0.0;
            for (MenuDotDTO menuDotDTO : values) {
                if (menuDotDTO.getPageStopTime() < 600) {
                    pageStopTime += menuDotDTO.getPageStopTime();
                }
            }
            //人均使用时间
            dotVO.setPerCapitaUserTime(pageStopTime / totalNicks);
            //页面访问次数
            dotVO.setPageVisitTime(pageVisitTimeDouble);
            //访问次数占比
            dotVO.setPageVisitTimeProportion(pageVisitTimeDouble / totalPageVisitTime);
            totalAverageStopTime += pageStopTime / totalPageVisitTime;
            //次均停留时间
            dotVO.setAverageStopTime(pageVisitTimeDouble == 0.0 ? 0.0 : pageStopTime / pageVisitTimeDouble);
            totalAverageStopTime2 += pageVisitTimeDouble == 0.0 ? 0.0 : pageStopTime / pageVisitTimeDouble;
//            dotVO.setAverageStopTimeProportion();
            menuInfos.put(key, dotVO);
//            menuInfos.add(menuInfo);
        }
        //次均停留时间占比
        for (Map.Entry m : menuInfos.entrySet()) {
            DotVO value = (DotVO) m.getValue();
            value.setAverageStopTimeProportion(totalAverageStopTime2 == 0.0 ? 0.0 : value.getAverageStopTime() / totalAverageStopTime2);
        }
    }
}
