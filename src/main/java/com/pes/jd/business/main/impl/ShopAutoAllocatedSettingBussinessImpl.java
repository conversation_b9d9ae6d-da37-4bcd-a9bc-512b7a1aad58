package com.pes.jd.business.main.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.pes.jd.business.main.ShopAutoAllocatedSettingBussiness;
import com.pes.jd.business.main.ShopBatchRemindTaskBusiness;
import com.pes.jd.dao.main.ShopAutoAdvanceAllocatedSettingDao;
import com.pes.jd.dao.main.*;
import com.pes.jd.model.DO.ShopAutoAllocatedSettingDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.ShopAutoAllocatedSettingParam;
import com.pes.jd.ms.domain.Data.master.ShopAutoAllocatedSettingDTO;
import com.pes.jd.ms.domain.Data.master.ShopSettingBatchRemindCno;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年07月30 15:42:42<br>
 */
@Service
public class ShopAutoAllocatedSettingBussinessImpl implements ShopAutoAllocatedSettingBussiness {
    @Resource
    private ShopAutoAllocatedSettingDao  shopAutoAllocatedSettingDao;
    @Resource
    private ShopSettingBatchRemindCnoDao shopSettingBatchRemindCnoDao;
    @Resource
    private ShopSettingBatchRemindDao shopSettingBatchRemindDao;
    @Resource
    private AdvanceAutoAllocatedSettingDao advanceAutoAllocatedSettingDao;
    @Resource
    private AppointmentAutoAllocatedSettingDao appointmentAutoAllocatedSettingDao;
    @Resource
    private ShopPresaleSettingBatchRemindDao shopPresaleSettingBatchRemindDao;
    @Resource
    private ShopReserveSettingBatchRemindDao shopReserveSettingBatchRemindDao;
    @Resource
    private ShopAutoAdvanceAllocatedSettingDao shopAutoAdvanceAllocatedSettingDao;
    @Resource
    private ShopAutoAppointmentAllocatedSettingDao shopAutoAppointmentAllocatedSettingDao;
    @Resource
    private ShopBatchRemindTaskBusiness shopBatchRemindTaskBusiness;

    @Override
    public int deleteShopAutoAllocatedSetting(Long id) {
        return 0;
    }

    @Override
    public int saveOrUpdateShopAutoAllocatedSetting(ShopAutoAllocatedSettingParam param) {
        ShopAutoAllocatedSettingDO record=makeShopAutoAllocatedSetting(param);
            if(record.getId()==null){
                record.setStatus(1);
                record.setCreated(new Date());
                return shopAutoAllocatedSettingDao.insertShopAutoAllocatedSetting(record);
            }else{
                record.setModify(new Date());
                return shopAutoAllocatedSettingDao.updateShopAutoAllocatedSettingById(record);
            }

    }
    private ShopAutoAllocatedSettingDO makeShopAutoAllocatedSetting(ShopAutoAllocatedSettingParam param){
        ShopAutoAllocatedSettingDO record=new ShopAutoAllocatedSettingDO();
        if(param.getIsAutoAllocated().equals(0)){
            record.setIsAutoAllocated(false);
        }else{
            record.setIsAutoAllocated(true);
        }
        record.setId(param.getId());
        record.setShopId(param.getShopId());
        record.setCnoCsNick(param.getCnoCsNick());
        record.setCnoFlag(param.getCnoFlag());
        record.setCnoGroupId(param.getCnoGroupId());
        record.setOnpCsNick(param.getOnpCsNick());
        record.setOnpFlag(param.getOnpFlag());
        record.setOnpGroupId(param.getOnpGroupId());
        record.setSnpCsNick(param.getSnpCsNick());
        record.setSnpFlag(param.getSnpFlag());
        record.setSnpGroupId(param.getSnpGroupId());

        record.setCnoSpareCsNick(param.getCnoSpareCsNick());
        record.setCnoSpareGroupId(param.getCnoSpareGroupId());

        record.setOnpSpareCsNick(param.getOnpSpareCsNick());
        record.setOnpSpareGroupId(param.getOnpSpareGroupId());

        record.setSnpSpareCsNick(param.getSnpSpareCsNick());
        record.setSnpSpareGroupId(param.getSnpSpareGroupId());
        return record;
    }

    @Override
    public ShopAutoAllocatedSettingDO selectShopAutoAllocatedSetting(Long id) {
        return null;
    }

    @Override
    public ShopAutoAllocatedSettingDTO selectShopAutoAllocatedSettingByShopId(Long shopId) {
        ShopAutoAllocatedSettingDTO autoSetting= shopAutoAllocatedSettingDao.selectShopAutoAllocatedSettingByShopId(shopId);
        Boolean isConRemind=false;
        Boolean isOnpRemind=false;
        int defaultNum = shopBatchRemindTaskBusiness.getDefaultShopBatchRemindTask(shopId);
        if(defaultNum > 0){
            List<ShopBatchRemindTaskDTO> shopBatchRemindTaskList = shopBatchRemindTaskBusiness.selectShopBatchRemindTaskListByTypes(shopId, Lists.newArrayList(1,2));
            if(CollectionUtils.isNotEmpty(shopBatchRemindTaskList)){
                Map<Integer, List<ShopBatchRemindTaskDTO>> shopBatchRemindTaskMap = shopBatchRemindTaskList.stream().collect(Collectors.groupingBy(ShopBatchRemindTaskDTO::getTaskType));
                isConRemind = judgeTrueOfFalse(shopBatchRemindTaskMap, 1);
                isOnpRemind = judgeTrueOfFalse(shopBatchRemindTaskMap, 2);
            }
        }else{
            ShopSettingBatchRemindCno con= shopSettingBatchRemindCnoDao.selectShopSettingBatchRemindCnoByshopId(shopId);
            ShopSettingBatchRemindDTO onp=   shopSettingBatchRemindDao.selectByshopId(shopId);
            if(con!=null&&con.getIsRemind()!=null){
                isConRemind=con.getIsRemind();
            }
            if(onp!=null&&onp.getIsRemind()!=null){
                isOnpRemind=onp.getIsRemind();
            }
        }
        if(autoSetting!=null){
            autoSetting.setCnoRemind(isConRemind);
            autoSetting.setOnpRemind(isOnpRemind);
            autoSetting.setIsClick(isConRemind && isOnpRemind);
        }else{
            autoSetting=new ShopAutoAllocatedSettingDTO();
            autoSetting.setOnpRemind(false);
            autoSetting.setCnoRemind(false);
            autoSetting.setIsAutoAllocated(false);
            autoSetting.setShopId(shopId);
            autoSetting.setIsClick(isConRemind && isOnpRemind);
        }

        return autoSetting;
    }

    private Boolean judgeTrueOfFalse(Map<Integer, List<ShopBatchRemindTaskDTO>> shopBatchRemindTaskMap, Integer taskType) {
        Calendar calendar = Calendar.getInstance();
        if(CollectionUtils.isNotEmpty(shopBatchRemindTaskMap.get(taskType))){
            for (ShopBatchRemindTaskDTO dto : shopBatchRemindTaskMap.get(taskType)) {
                if(dto.getIsPermanent() == 1) return true;
                if(DateUtil.isIn(calendar.getTime(), dto.getTaskStartTime(), dto.getTaskEndTime())) return true;
            }
        }
        return false;
    }

    @Override
    public int closeShopAutoAllocatedSettingByShopId(Long shopId){
        ShopAutoAllocatedSettingDTO auto=selectShopAutoAllocatedSettingByShopId(shopId);
        if(auto==null){
            return 0;
        }
      return  shopAutoAllocatedSettingDao.updateCloseAutoAllocatedByShopId(shopId,false);
    }

	@Override
	public AppointmentAutoAllocatedSettingDTO selectAppointmentAutoAllocatedSettingByShopId(Long shopId) {
        Byte isOpenOrder = new Byte("0");
        Byte isOpenPay = new Byte("0");
        Byte isOpenAppointment = new Byte("0");

		AppointmentAutoAllocatedSettingDTO autoSetting= appointmentAutoAllocatedSettingDao.selectShopAutoAllocatedSettingByShopId(shopId);

		if(shopBatchRemindTaskBusiness.getDefaultShopBatchRemindTask(shopId) > 0){
            List<ShopBatchRemindTaskDTO> shopBatchRemindTaskList = shopBatchRemindTaskBusiness.selectShopBatchRemindTaskListByTypes(shopId, Lists.newArrayList(6, 7, 8));
            Map<Integer, List<ShopBatchRemindTaskDTO>> shopBatchRemindTaskMap = shopBatchRemindTaskList.stream().collect(Collectors.groupingBy(ShopBatchRemindTaskDTO::getTaskType));
            if(judgeTrueOfFalse(shopBatchRemindTaskMap, 6)) isOpenAppointment = new Byte("1");
            if(judgeTrueOfFalse(shopBatchRemindTaskMap, 7)) isOpenOrder = new Byte("1");
            if(judgeTrueOfFalse(shopBatchRemindTaskMap, 8)) isOpenPay = new Byte("1");
        }else{
            ShopSettingBatchRemindReserveDTO setting = shopReserveSettingBatchRemindDao.selectShopSettingBatchRemindReserveByShopId(shopId);
            if(setting != null){
                if(setting.getIsReserveRemind() != null && setting.getIsReserveRemind())
                    isOpenAppointment = new Byte("1");
                if(setting.getIsRseUncRemind() != null && setting.getIsRseUncRemind())
                    isOpenOrder = new Byte("1");
                if(setting.getIsRseUnpRemind() != null && setting.getIsRseUnpRemind())
                    isOpenPay = new Byte("1");
            }
        }
        if(autoSetting!=null){
            autoSetting.setIsOpenAppointment(isOpenAppointment);
            autoSetting.setIsOpenOrder(isOpenOrder);
            autoSetting.setIsOpenPay(isOpenPay);
            autoSetting.setIsClick(isOpenAppointment == 1 && isOpenOrder == 1 && isOpenPay == 1);
        }else{
            autoSetting=new AppointmentAutoAllocatedSettingDTO();
            autoSetting.setIsOpenOrder(new Byte("0"));
            autoSetting.setIsOpenPay(new Byte("0"));
            autoSetting.setIsOpenAppointment(new Byte("0"));
            autoSetting.setAutoAllocated(false);
            autoSetting.setShopId(shopId);
            autoSetting.setIsClick(isOpenAppointment == 1 && isOpenOrder == 1 && isOpenPay == 1);
        }

        return autoSetting;

	}


	@Override
	public AdvanceAutoAllocatedSettingDTO selectAdvanceAutoAllocatedSetting(Long shopId) {
        Byte isOpenOrder = new Byte("0");
        Byte isOpenEarnest = new Byte("0");
        Byte isOpenTail = new Byte("0");

		AdvanceAutoAllocatedSettingDTO autoSetting= advanceAutoAllocatedSettingDao.selectShopAutoAllocatedSettingByShopId(shopId);
		if(shopBatchRemindTaskBusiness.getDefaultShopBatchRemindTask(shopId) > 0){
            List<ShopBatchRemindTaskDTO> shopBatchRemindTaskList = shopBatchRemindTaskBusiness.selectShopBatchRemindTaskListByTypes(shopId, Lists.newArrayList(3,4,5));
            if(CollectionUtils.isNotEmpty(shopBatchRemindTaskList)){
                Map<Integer, List<ShopBatchRemindTaskDTO>> shopBatchRemindTaskMap = shopBatchRemindTaskList.stream().collect(Collectors.groupingBy(ShopBatchRemindTaskDTO::getTaskType));
                if(judgeTrueOfFalse(shopBatchRemindTaskMap, 3)) isOpenOrder = new Byte("1");
                if(judgeTrueOfFalse(shopBatchRemindTaskMap, 4)) isOpenEarnest = new Byte("1");
                if(judgeTrueOfFalse(shopBatchRemindTaskMap, 5)) isOpenTail = new Byte("1");
            }
        }else{
            ShopSettingBatchRemindPresaleDTO setting = shopPresaleSettingBatchRemindDao.selectShopSettingBatchRemindPresaleByShopId(shopId);
            if(setting != null) {
                if(setting.getIsUnpoRemind() != null && setting.getIsUnpoRemind())
                    isOpenOrder = new Byte("1");
                if(setting.getIsBargainRemind() != null && setting.getIsBargainRemind())
                    isOpenEarnest = new Byte("1");
                if(setting.getIsBalanceRemind() != null && setting.getIsBalanceRemind())
                    isOpenTail = new Byte("1");
            }
        }

	        if(autoSetting!=null){
                autoSetting.setIsOpenOrder(isOpenOrder);
                autoSetting.setIsOpenEarnest(isOpenEarnest);
                autoSetting.setIsOpentail(isOpenTail);
                autoSetting.setIsClick(isOpenOrder == 1 && isOpenEarnest == 1 && isOpenTail == 1);
	        }else{
	            autoSetting=new AdvanceAutoAllocatedSettingDTO();
	            autoSetting.setIsOpenOrder(new Byte("0"));
	            autoSetting.setIsOpenEarnest(new Byte("0"));
	            autoSetting.setIsOpentail(new Byte("0"));
	            autoSetting.setAutoAllocated(false);
	            autoSetting.setShopId(shopId);
	            autoSetting.setIsClick(isOpenOrder == 1 && isOpenEarnest == 1 && isOpenTail == 1);
	        }

		return autoSetting;


	}

	@Override
	public int saveOrUpdateAdvanceAutoAllocatedSetting(AdvanceAutoAllocatedSettingDTO param) {
          if(param.getId()==null){
        	  param.setStatus(new Byte("1"));
        	  param.setCreated(new Date());
              return advanceAutoAllocatedSettingDao.insertShopAutoAllocatedSetting(param);
          }else{
        	  param.setModify(new Date());
              return advanceAutoAllocatedSettingDao.updateShopAutoAllocatedSettingById(param);
          }
	}



	@Override
	public int saveOrUpdateAppointmentAutoAllocatedSetting(AppointmentAutoAllocatedSettingDTO record) {
          if(record.getId()==null){
              record.setStatus(new Byte("1"));
              record.setCreated(new Date());
              return appointmentAutoAllocatedSettingDao.insertShopAutoAllocatedSetting(record);
          }else{
              record.setModify(new Date());
              return appointmentAutoAllocatedSettingDao.updateShopAutoAllocatedSettingById(record);
          }
	}

    @Override
    public int updateCloseAdvanceAutoAllocatedByShopId(Long shopId, Boolean autoAllocated) {
        return advanceAutoAllocatedSettingDao.updateCloseAdvanceAutoAllocatedByShopId(shopId,autoAllocated);
    }

    @Override
    public int updateCloseAppointmentAutoAllocatedByShopId(Long shopId, Boolean autoAllocated) {
        return appointmentAutoAllocatedSettingDao.updateCloseAppointmentAutoAllocatedByShopId(shopId,autoAllocated);
    }

    @Override
    public ShopAutoAdvanceAllocatedSettingDTO selectShopAutoAdvanceAllocatedSettingByShopId(Long shopId) {
        return shopAutoAdvanceAllocatedSettingDao.selectShopAutoAdvanceAllocatedSettingByShopId(shopId);
    }

    @Override
    public ShopAutoAppointmentAllocatedSettingDTO selectShopAutoAppointmentAllocatedSettingByShopId(Long shopId) {
        return shopAutoAppointmentAllocatedSettingDao.selectShopAutoAppointmentAllocatedSettingByShopId(shopId);
    }
}
