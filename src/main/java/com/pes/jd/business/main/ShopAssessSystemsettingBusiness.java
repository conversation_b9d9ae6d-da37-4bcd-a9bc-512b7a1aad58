package com.pes.jd.business.main;

import com.pes.jd.model.DO.ShopAssessSystemsettingDO;
import com.pes.jd.model.DTO.ShopAssessSystemsettingDTO;

/**
 * @Author: aiJun
 * @Date: 2020/11/25 13:21
 * @Version 1.0
 */
public interface ShopAssessSystemsettingBusiness {
    int deleteByPrimaryKey(Long id);

    int insert(ShopAssessSystemsettingDO record);

    int insertSelective(ShopAssessSystemsettingDO record);

    ShopAssessSystemsettingDTO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ShopAssessSystemsettingDO record);

    int updateByPrimaryKey(ShopAssessSystemsettingDO record);

    ShopAssessSystemsettingDTO selectByShopId(Long shopId);

    int updateOrInsert(Long id, Long shopId, double satisfiedRateIndex, double avgRespIndex);
}
