package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.PerformanceSettingBussiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.BuyernickFilterDao;
import com.pes.jd.dao.main.GoodsFilterDao;
import com.pes.jd.dao.main.ShopSystemsettingDao;
import com.pes.jd.dao.main.ShopSystemsettingFieidLimitDao;
import com.pes.jd.model.DTO.BuyerNickFilterDTO;
import com.pes.jd.model.DTO.GoodsFilterDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.DTO.ShopSystemsettingFieidLimitDTO;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Title: PerformanceSettingBussinessImpl.java
 * @Package:com.pes.jd.business.impl
 * @Description:(绩效设置)
 * @author:Lsp
 * @date:2018年11月6日
 * @version:V1.8
 */
@Service
public class PerformanceSettingBussinessImpl implements PerformanceSettingBussiness {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceSettingBussinessImpl.class);

    @Autowired
    private GoodsFilterDao goodsFilterDao;

    @Autowired
    private ShopSystemsettingDao shopSystemsettingDao;

    @Autowired
    private BuyernickFilterDao buyernickFilterDao;

    @Autowired
    private ShopSystemsettingFieidLimitDao shopSystemsettingFieidLimitDao;

    @Override
    public int addPesGoods(List<GoodsFilterDTO> good) {
        return goodsFilterDao.addPesGoods(good);
    }

    @Override
    public int deletePesGoods(Long shopId, List<String> goodsIdList) {
        return goodsFilterDao.deletePesGoods(shopId, goodsIdList);
    }

    @Override
    public List<GoodsFilterDTO> getPesGoodsByShopId(Long shopId) {
        return goodsFilterDao.getPesGoodsByShopId(shopId);
    }

    @Override
    public List<GoodsFilterDTO> getExistedPesGoods(Long shopId, List<String> goodsUrl) {
        return goodsFilterDao.getExistedPesGoods(shopId, goodsUrl);
    }

    @Override
    public GoodsFilterDTO getGoodsByShopIdAndNumIid(Long shopId, Long numIid) {
        return goodsFilterDao.getGoodsByShopIdAndNumIid(shopId, numIid);
    }

    @Override
    public ShopSystemsettingDTO getShopSystemsettingByShopId(Long shopId) {
        return shopSystemsettingDao.getShopSystemsettingByShopId(shopId);
    }

    @Transactional
    @Override
    public int updateShopSystemsettingByShopId(ShopSystemsettingDTO shopSysInfo) throws Exception {
        ShopSystemsettingDTO shopSystemsetting = shopSystemsettingDao.getShopSystemsettingByShopId(shopSysInfo.getShopId());
        int update = 0;
        if (shopSystemsetting != null) {
            updateShopSystemsettingFieidLimit(shopSysInfo, shopSystemsetting);
            update = shopSystemsettingDao.updateShopSystemsettingByShopId(shopSysInfo);
        }
        return update;
    }

    /**
     * 更新 系统设置 询单有效时长、出库有效时长 字段 限制
     */
    public void updateShopSystemsettingFieidLimit(ShopSystemsettingDTO shopSysInfo, ShopSystemsettingDTO shopSystemsetting) {
        boolean isEnquiryValidDurationTime = shopSysInfo.getEnquiryValidDurationTime() != null;
        boolean isOutStockValidDurationTime = shopSysInfo.getOutStockValidDurationTime() != null;

        if (isEnquiryValidDurationTime || isOutStockValidDurationTime) {
            try {
                //若 更改了询单有效时长 或 出库有效时长 更新限制
                isEnquiryValidDurationTime = isEnquiryValidDurationTime && !shopSysInfo.getEnquiryValidDurationTime().equals(shopSystemsetting.getEnquiryValidDurationTime());
                isOutStockValidDurationTime = isOutStockValidDurationTime && !shopSysInfo.getOutStockValidDurationTime().equals(shopSystemsetting.getOutStockValidDurationTime());
                if (isEnquiryValidDurationTime || isOutStockValidDurationTime) {

                    ShopSystemsettingFieidLimitDTO shopSysSettingFieid = shopSystemsettingFieidLimitDao.selectByShopId(shopSysInfo.getShopId());
                    if (shopSysSettingFieid == null) {
                        shopSysSettingFieid = new ShopSystemsettingFieidLimitDTO();
                        shopSysSettingFieid.setShopId(shopSysInfo.getShopId());
                        shopSysSettingFieid.setEnquiryValidDurationTime(shopSysInfo.getEnquiryValidDurationTime());
                        shopSysSettingFieid.setOutStockValidDurationTime(shopSysInfo.getOutStockValidDurationTime());
                        shopSysSettingFieid.setModifyEnquiryOutStock(new Date());
                        shopSystemsettingFieidLimitDao.insert(shopSysSettingFieid);

                    } else {
                        Date canUpdate = DateUtil.getDateByPeriod(DateUtil.getEndTimeOfDate(shopSysSettingFieid.getModifyEnquiryOutStock()), CommonConstants.ENQUIRY_AND_OUTSTOCK_VALID_DURATION_TIME_LIMIT_DAY);
                        Date toDay = new Date();
                        if (DateFormatUtils.formatYMd(toDay).equals(DateFormatUtils.formatYMd(shopSysSettingFieid.getModifyEnquiryOutStock())) || toDay.getTime() > canUpdate.getTime()) {
                            shopSysSettingFieid.setModifyEnquiryOutStock(toDay);
                            shopSysSettingFieid.setEnquiryValidDurationTime(shopSysInfo.getEnquiryValidDurationTime());
                            shopSysSettingFieid.setOutStockValidDurationTime(shopSysInfo.getOutStockValidDurationTime());
                            shopSystemsettingFieidLimitDao.updateEnquiryAndOutStockValidDurationTimeByShopId(shopSysSettingFieid);
                        } else {
                            shopSysInfo.setEnquiryValidDurationTime(null);
                            shopSysInfo.setOutStockValidDurationTime(null);
                        }
                    }

                }
            } catch (Exception e) {
                logger.error("updateShopSystemsettingFieidLimit date format error{}", e);
            }
        }
    }

    /**
     * 查询 询单和出库有效时长修改限制
     */
    @Override
    public ShopSystemsettingFieidLimitDTO queryIsModifyEnquiryAndOutStockValidTime(Long shopId) throws ParseException {
        ShopSystemsettingFieidLimitDTO shopSystemsettingFieidLimit = shopSystemsettingFieidLimitDao.selectByShopId(shopId);
        if (shopSystemsettingFieidLimit != null) {
            Date canUpdate = DateUtil.getDateByPeriod(DateUtil.getStartTimeOfDate(shopSystemsettingFieidLimit.getModifyEnquiryOutStock()), CommonConstants.ENQUIRY_AND_OUTSTOCK_VALID_DURATION_TIME_LIMIT_DAY + 1);
            Date toDay = new Date();
            if (DateFormatUtils.formatYMd(toDay).equals(DateFormatUtils.formatYMd(shopSystemsettingFieidLimit.getModifyEnquiryOutStock())) || toDay.getTime() >= canUpdate.getTime()) {
                shopSystemsettingFieidLimit.setModifyEnquiryAndOutStockValidTime(true);
            } else {
                shopSystemsettingFieidLimit.setModifyEnquiryAndOutStockValidTime(false);
            }
            shopSystemsettingFieidLimit.setChangeableDate(new SimpleDateFormat("yyyy年MM月dd日").format(canUpdate));
            Long limitDaty = (canUpdate.getTime() - toDay.getTime()) / 86400000;
            shopSystemsettingFieidLimit.setLimitDay(limitDaty > 0 ? limitDaty.intValue() : CommonConstants.ENQUIRY_AND_OUTSTOCK_VALID_DURATION_TIME_LIMIT_DAY);
        } else {
            shopSystemsettingFieidLimit = new ShopSystemsettingFieidLimitDTO();
            shopSystemsettingFieidLimit.setShopId(shopId);
            shopSystemsettingFieidLimit.setModifyEnquiryAndOutStockValidTime(true);
            shopSystemsettingFieidLimit.setLimitDay(CommonConstants.ENQUIRY_AND_OUTSTOCK_VALID_DURATION_TIME_LIMIT_DAY);
            shopSystemsettingFieidLimit.setChangeableDate(DateUtils.formatYMd(new Date()));
        }
        return shopSystemsettingFieidLimit;
    }


    @Override
    public List<String> selectFilterBuyerNickLstByShopId(Long shopId) {
        return buyernickFilterDao.selectFilterBuyerNickLstByShopId(shopId);
    }

    @Override
    public int deleteByNickAndShopId(String buyerNick, Long shopId) {
        return buyernickFilterDao.deleteByNickAndShopId(buyerNick, shopId);
    }

    @Override
    public int insertBuyernickFilter(List<BuyerNickFilterDTO> buyernickFilter) {
        return buyernickFilterDao.insertBatchBuyernickFilter(buyernickFilter);
    }

    @Override
    public int updateWarnSetting(Long shopId, Boolean openWarn, String warnAcceptCs) {
        return shopSystemsettingDao.updateWarnSetting(shopId, openWarn, warnAcceptCs);
    }


}
