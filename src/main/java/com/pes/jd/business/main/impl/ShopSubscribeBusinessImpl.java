package com.pes.jd.business.main.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.business.main.ShopSubscribeBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.PesUserLoginLogDao;
import com.pes.jd.dao.main.ShopDao;
import com.pes.jd.dao.main.ShopSubscribeDao;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSubScribeDTO;
import com.pes.jd.model.Param.SubscribeParam;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.VO.ShopAuthExpiredVo;
import com.pes.jd.ms.domain.Data.job.ShopLoginInfo;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.master.ShopLogin;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;
import com.pes.jd.ms.domain.Result.master.ShopSubScribeResult;
import com.pes.jd.ms.domain.Result.task.dispatching.ShopUserLoginResult;
import com.pes.jd.util.CollectionUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ShopSubscribeBusinessImpl implements ShopSubscribeBusiness {
	private static final Logger logger = LoggerFactory.getLogger(ShopSubscribeBusinessImpl.class);
	@Autowired
	private ShopSubscribeDao shopSubscribeDao;
	@Autowired
	private ShopDao shopDao;
	@Autowired
	private PesUserLoginLogDao pesUserLoginLogDao;
	@Override
	public List<ShopSubScribeDTO> selectShopSubscribeByShopIdLstByDate(SubscribeParam param) {
		return shopSubscribeDao.selectShopSubscribeByShopIdLstByDate(param);
	}

	@Override
	public ShopSubScribeResult selectShopSubScribeForUseAnalysis(UserAnalysisParam param,String type) {
		//获取店铺信息
		ShopSubScribeResult result=new ShopSubScribeResult();
		long s=System.currentTimeMillis();
		List<CauseShop> causeShopLst =shopDao.selectShopInfoForShopUserAnalysis(param,type);
		long e=System.currentTimeMillis();
		logger.info("mster get shop time:{}ms",e-s);
		if(CollectionUtils.isEmpty(causeShopLst)){
			return result;
		}
		
		result.setCauseShopLst(causeShopLst);
		Set<Long>  shopIdSet=causeShopLst.stream().map(CauseShop::getShopId).collect(Collectors.toSet());
		param.setShopIdSet(shopIdSet);
		long s1=System.currentTimeMillis();
		//获取店铺的订购信息
		List<ShopSubScribe> shopSubScribeLst = selectSplitshopSubScribeLst(param);
		long e1=System.currentTimeMillis();
		logger.info("master get subScribe time :{}ms",e1-s1);
		if(CollectionUtils.isNotEmpty(shopSubScribeLst)){
			result.setShopSubScribeLst(shopSubScribeLst);
		}
		//获取店铺的登录数
		long s2=System.currentTimeMillis();
		List<ShopLogin> shopLoginLst = pesUserLoginLogDao.selectShopLoginForUserAnalysis(param);
		long e2=System.currentTimeMillis();
		logger.info("master get login time :{}ms",e2-s2);
		if(CollectionUtils.isNotEmpty(shopLoginLst)){
			result.setShopLoginLst(shopLoginLst);
		}
		return result;
	}



	@Override
	public List<ShopSubScribe> selectShopSubScribeDetailForUseAnalysis(UserAnalysisParam param) {
		List<ShopSubScribe> shopSubScribeLst = shopSubscribeDao.selectShopSubScribeForUseAnalysis(param);
		for(ShopSubScribe shopSubScribe : shopSubScribeLst){
			if("不限客服".equals(shopSubScribe.getVersion())){
				shopSubScribe.setVersion("不限客服基础版");
			}else if("不限账号".equals(shopSubScribe.getVersion())){
				shopSubScribe.setVersion("不限客服基础版");
			}else if("3个客服".equals(shopSubScribe.getVersion())){
				shopSubScribe.setVersion("3个客服基础版");
			}else if("3个账号".equals(shopSubScribe.getVersion())){
				shopSubScribe.setVersion("3个客服基础版");
			}
		}

		return shopSubScribeLst;
	}

	@Override
	public List<ShopSubScribeDTO> selectShopSubscribeByShopIdLstByDateForRt(SubscribeParam param) {
		return shopSubscribeDao.selectShopSubscribeByShopIdLstByDateForRt(param);
	}

	@Override
	public ShopUserLoginResult getShopLoginSubscribe(Long shopId, Date startDate, Date endDate) {
		ShopUserLoginResult result=new ShopUserLoginResult();
		//获取店铺的订购信息
		List<ShopSubScribe> shopSubScribeLst = shopSubscribeDao.selectShopSubScribeByShopId(shopId);
		if(CollectionUtils.isNotEmpty(shopSubScribeLst)){
			result.setShopSubScribeLst(shopSubScribeLst);
		}
		//获取店铺的登录数
		ShopLoginInfo loginInfo=	 pesUserLoginLogDao.selectShopLoginInfoByShopId(shopId,startDate,endDate);
		if(loginInfo!=null){
			result.setLoginInfo(loginInfo);
		}
		return result;
	}


	private List<ShopSubScribe> selectSplitshopSubScribeLst(UserAnalysisParam param){
		List<ShopSubScribe> result= Lists.newArrayList();
		List<List<Long>> lst = CollectionUtil.smallToLst(new ArrayList<>(param.getShopIdSet()), CommonConstants.BIG_DATA_SELECT_NUM);
		for (List<Long> shopLst : lst) {
			param.setShopIdSet(Sets.newHashSet(shopLst));
			List<ShopSubScribe> shopSubScribeLst = shopSubscribeDao.selectShopSubScribeForUseAnalysis(param);
			if(CollectionUtils.isNotEmpty(shopSubScribeLst)){
				result.addAll(shopSubScribeLst);
			}
		}
		return result;
	}
	@Override
	public List<ShopSubScribeDTO> getShopSubscribeInfoByShopId(Long shopId) {
		return shopSubscribeDao.getShopSubscribeInfoByShopId(shopId);
	}

	@Override
	public List<ShopAuthExpiredVo> selectShopAuthExpiredLst(Date startDate, Date endDate, String nick, Integer shopType) {
		List<ShopDTO> shopDTOLst = shopDao.selectShopAuthExpiredBySellerNickOrTitleAndTime(startDate, endDate, nick, shopType);
		List<ShopAuthExpiredVo> result = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(shopDTOLst)){
			for (ShopDTO dto: shopDTOLst) {
				ShopAuthExpiredVo vo = new ShopAuthExpiredVo();
				vo.setShopId(dto.getShopId());
				vo.setSelleNick(dto.getSellerNick());
				vo.setTitle(dto.getTitle());
				vo.setShopStatus(dto.getStatus());
				vo.setAuthDeadLine(dto.getAuthDeadLine()!=null?dto.getAuthDeadLine():null);
				List<ShopSubScribeDTO> shopSubscribeLst = shopSubscribeDao.getShopSubscribeInfoByShopId(dto.getShopId());
				if(CollectionUtils.isNotEmpty(shopSubscribeLst)){
					vo.setOrderDate(shopSubscribeLst.get(0).getStartTime());
					vo.setEndDate(shopSubscribeLst.get(0).getEndTime());
					vo.setOrderCycle(shopSubscribeLst.get(0).getOrderCycle());
					vo.setVersion(shopSubscribeLst.get(0).getVersion());
				}

				result.add(vo);
			}
		}

		return CollectionUtils.isNotEmpty(result)?result:Collections.EMPTY_LIST;
	}

	@Override
	public List<ShopSubScribe> selectShopSubscribe(Long shopId) {
		return shopSubscribeDao.selectShopSubScribeByShopId(shopId);
	}
}
