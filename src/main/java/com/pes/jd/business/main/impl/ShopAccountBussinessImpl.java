package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.ShopAccountBussiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopAccountDao;
import com.pes.jd.data.api.SubUserOperator;
import com.pes.jd.model.AO.LoginUserAO;
import com.pes.jd.model.DO.ShopAccount;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**  
 * ClassName:ShopAccountBussinessImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月29日 下午3:43:09 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class ShopAccountBussinessImpl implements ShopAccountBussiness {
	private Logger logger = LoggerFactory.getLogger(ShopAccountBussinessImpl.class);
	@Autowired
	private ShopAccountDao  shopAccountDao;
	
	@Autowired
	private SubUserOperator subUserOperator;
	@Override
	public void insertSubUsersOfShop(LoginUserAO loginUser, Long shopId) throws Exception {
		List<ShopAccount> subUsers = subUserOperator.getVenderSubUsers(Long.valueOf(loginUser.getSellerId()), shopId, loginUser.getSellerSessionKey()).getCustSubUsers();
		insertShopAccount(loginUser, shopId, subUsers);
	}

	@Override
	public void insertShopAccount(LoginUserAO loginUser,Long shopId,List<ShopAccount> shopAccounLst) throws Exception {
		if (CollectionUtils.isEmpty(shopAccounLst)) {
			shopAccounLst = new ArrayList<>(1);
		}
		ShopAccount manager = new ShopAccount();
		manager.setStatus(CommonConstants.SUB_USER_STATUS_NORMAL);
		manager.setNick(loginUser.getLoginUserNick().toLowerCase());
		manager.setShopId(shopId);
		manager.setRole(CommonConstants.SUB_USER_TYPE_MANAGE);
		manager.setSellerId(Long.valueOf(loginUser.getLoginUserId()));
		manager.setUserName(loginUser.getLoginShowUserNick());
		manager.setIsAccount(true);
		manager.setCreated(new Date());
		shopAccounLst.add(manager);
		batchInsertSubUsersOfShop(shopAccounLst, shopId + "");
		System.out.println("shopId:"+shopId+" batch insert shopAccounLst num:"+shopAccounLst.size());
	}
	@Override
	@Transactional(isolation=Isolation.DEFAULT,propagation=Propagation.REQUIRED)
	public int batchInsertSubUsersOfShop(List<ShopAccount> subUsers, String shopId) {
		List<ShopAccount> subUsersLocal = getAllSubUsersOfShop(shopId);
		if (subUsersLocal != null) {
			for (ShopAccount subUser : subUsersLocal) {
				if (CommonConstants.SUB_USER_TYPE_MANAGE.equals(subUser.getRole())) {
					for (ShopAccount subUserNew : subUsers) {
						subUser.setNick(subUser.getNick().toLowerCase());
						if (subUser.getNick().equals(subUserNew.getNick())) {
							subUserNew.setRole(CommonConstants.SUB_USER_TYPE_MANAGE);
							break;
						}
					}
				}
			}
		}

//		logger.info("test-source-bug"+shopId);
        if(null != subUsers && !subUsers.isEmpty()){
//			logger.info("test-source-bug1"+shopId);
            for (ShopAccount subUser : subUsers) {
                if(null == subUser.getSource()){
//					logger.info("test-source-bug2"+shopId);
                    subUser.setSource(0);
                }
            }
        }

		deleteSubUsersOfShop(shopId);
		return shopAccountDao.batchInsertSubUsersOfShop(subUsers);
	}
	
	@Override
	public List<ShopAccount> getAllSubUsersOfShop(String shopId) {
		return shopAccountDao.selectShopAccountOfShop(shopId);
	}
	
	@Override
	public void deleteSubUsersOfShop(String shopId) {
		 shopAccountDao.deleteShopAccountByShopId(shopId);
	}

	@Override
	public ShopAccount getShopAccountByNick(String nick) {
		List<ShopAccount> shopAccountByNick = shopAccountDao.getShopAccountByNick(nick);
		ShopAccount shopAccount = null;
		if (Objects.nonNull(shopAccountByNick) && !shopAccountByNick.isEmpty()) {
			if (shopAccountByNick.size() > 1) {
				for (ShopAccount account : shopAccountByNick) {
					if (Objects.equals("M",account.getRole()))
						return  account;
				}
                shopAccount= shopAccountByNick.get(0);
			}else {
				shopAccount = shopAccountByNick.get(0);
			}
		}
		return shopAccount;
	}


	@Override
	public ShopAccount getShopMainAccount(String shopId) {
		return shopAccountDao.getShopMainAccount(shopId);
	}

	@Override
	public ShopAccount getShopMainAccountByShopId(String shopId) {
		return shopAccountDao.getShopMainAccountByShopId(shopId);
	}

	@Override
	public List<ShopAccountDTO> selectShopAccountByShopId(Long shopId) {
		return shopAccountDao.selectShopAccountByShopId(shopId);
	}
}
  
