package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.ReportPropertyBusiness;
import com.pes.jd.dao.main.ReportPropertyDao;
import com.pes.jd.model.DTO.ReportPropertyDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/26 2:53 PM
 * @since 1.0.0
 */
@Service
public class ReportPropertyBusinessImpl implements ReportPropertyBusiness {

    @Autowired
    private ReportPropertyDao reportPropertyDao;


    @Override
    public List<ReportPropertyDTO> searchAllWithCategory(String name, ShopSystemsettingDTO shopSystemsetting) {
        List<ReportPropertyDTO>  list = new ArrayList<>();
        final List<ReportPropertyDTO> reportPropertyDTOS = reportPropertyDao.searchAllWithCategory(name,null,null);
        if(null != reportPropertyDTOS && !reportPropertyDTOS.isEmpty()){
            for (ReportPropertyDTO reportPropertyDTO : reportPropertyDTOS) {
                if("用户评价评分".equals(reportPropertyDTO.getTitle())){

                }else if("客服咨询评分".equals(reportPropertyDTO.getTitle())){

                }else if("物流履约评分".equals(reportPropertyDTO.getTitle())){

                }else if("售后服务评分".equals(reportPropertyDTO.getTitle())){

                }else if("交易纠纷评分".equals(reportPropertyDTO.getTitle())){

                }
                else {
                    list.add(reportPropertyDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list)){
            list.forEach(e->processDynamic(e,shopSystemsetting));
        }
        return list;
    }

    @Override
    public List<ReportPropertyDTO> searchAllWithCategory(List<String> property,Integer type) {
        return reportPropertyDao.searchAllWithCategory(null,property,type);
    }

    private DateTimeFormatter ymd = DateTimeFormatter.ofPattern("yyyy年MM月dd日");

    private void processDynamic(ReportPropertyDTO desc, ShopSystemsettingDTO setting) {
        if (StringUtils.isEmpty(desc.getDesc())) {
            return;
        }
        /*询单*/
        int advisory = setting.getEnquiryValidDurationTime();
        /*出库有效时长*/
        int outStock = setting.getOutStockValidDurationTime();
        /*快速应答设置*/
        int quick = setting.getQuickResponseTime();
        final LocalDate now = LocalDate.now();

        String origin = desc.getDesc();
        String result = origin;
        LocalDate delayTime = getDelayTime(advisory - 1, now);
        result = result.replace("当前询单有效时长配置为*天，该数据须延迟*-1天统计，当前可查看*年*月*日",
                String.format("当前询单有效时长配置为%s天，该数据须延迟%s天统计，当前可查看%s", advisory, advisory - 1 ,
                        ymd.format(delayTime)
                )
        );
        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }
        result = result.replace("当前询单有效时长配置为*天，该数据须延迟*-1天统计,当前可查看*年*月*日",
                String.format("当前询单有效时长配置为%s天，该数据须延迟%s天统计，当前可查看%s", advisory, advisory - 1 ,
                        ymd.format(delayTime)
                )
        );
        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }

        delayTime = getDelayTime(advisory, now);
        result = result.replace("当前询单有效时长配置为*天，该数据须延迟*天统计，当前可查看*年*月*日",
                String.format("当前询单有效时长配置为%s天，该数据须延迟%s天统计，当前可查看%s", advisory, advisory,
                        ymd.format(delayTime)
                )
        );

        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }
        delayTime = getDelayTime(outStock - 1,now);
        result = result.replace("当前出库有效时长配置为*天，该数据须延迟*-1天统计，当前可查看*年*月*日",
                String.format("当前出库有效时长配置为%s天，该数据须延迟%s天统计，当前可查看%s", outStock, outStock - 1,
                        ymd.format(delayTime)
                )
        );
        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }
        delayTime = getDelayTime(advisory + outStock - 2,now);
        result = result.replace("当前询单有效时长配置为*天，出库有效时长配置为$天，该数据须延迟*+$-2天统计，当前可查看*年*月*日",
                String.format("当前询单有效时长配置为%s天，出库有效时长配置为%s天，该数据须延迟%s天统计，当前可查看%s",
                        advisory, outStock, advisory + outStock - 2, ymd.format(delayTime)
                ));
        result = result.replace("当前询单有效时长为*天，出库有效时长为$天，该数据须延迟*+$-2天，当前可查看*年*月*日",
                String.format("当前询单有效时长为%s天，出库有效时长为%s天，该数据须延迟%s天，当前可查看%s",
                        advisory, outStock, advisory + outStock - 2, ymd.format(delayTime)
                ));

        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }
        delayTime = getDelayTime(outStock,now);
        result = result.replace("当前出库有效时长配置为*天，该数据须延迟*天统计，当前可查看*年*月*日",
                String.format("当前出库有效时长配置为%s天，该数据须延迟%s天统计，当前可查看%s",
                        outStock, outStock, ymd.format(delayTime)
                ));
        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }
        delayTime= getDelayTime(1, now);
        result = result.replace("该数据须延迟1天统计，当前可查看前天",
                String.format("该数据须延迟1天统计，当前可查看%s", ymd.format(delayTime))
        );
        result = result.replace("该数据须延迟1天统计，当前可查看*年*月*日",
                String.format("该数据须延迟1天统计，当前可查看%s", ymd.format(delayTime))
        );
        if (!origin.equals(result)){
            desc.setDesc(result);
            desc.setDelayTime(TimeUnit.DAYS.toMillis(delayTime.toEpochDay()));
            return;
        }
        result = result.replace("客服首次回复在指定时间内（当前为*",
                String.format("客服首次回复在指定时间内（当前为%s", quick)
        );
        desc.setDesc(result);
    }

    private LocalDate getDelayTime(int delay, LocalDate now) {
        return now.minusDays(delay+1);

    }

}
