package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.CustomReportBusiness;
import com.pes.jd.dao.main.CustomReportDao;
import com.pes.jd.model.DO.CustomReportDO;
import com.pes.jd.model.DTO.CustomReportDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/26 4:25 PM
 * @since 1.0.0
 */
@Service
public class CustomReportBusinessImpl implements CustomReportBusiness {
    @Autowired
    private CustomReportDao customReportDao;

    /**
     *  {@inheritDoc}
     */
    @Override
    public List<CustomReportDTO> searchCustomReportByType(Integer type,Long shopId) {
        return customReportDao.searchByType(type,shopId);
    }

    /**
     *  {@inheritDoc}
     */
    @Override
    public int deleteByPrimaryKeyWithProperty(Long id) {
        return customReportDao.deleteByPrimaryKeyWithProperty(id);
    }

    @Override
    public Long insert(CustomReportDO record) {
        return customReportDao.insert(record);
    }

    /**
     *  {@inheritDoc}
     */
    @Override
    public int updateByPrimaryKey(CustomReportDO record) {
        return customReportDao.updateByPrimaryKey(record);
    }

    @Override
    public int checkPredefine(Long shopId, String name) {
        return customReportDao.checkPredefine(shopId, name);
    }

    @Override
    public List<Long> searchReportIdByReportName(String reportName) {
        return customReportDao.searchReportIdByReportName(reportName);
    }

    @Override
    public List<CustomReportDTO> selectByShopAndName(Long shopId, String name) {
        return customReportDao.selectByShopAndName(shopId, name);
    }
}
