package com.pes.jd.business.main.impl;


import com.pes.jd.business.main.PayManagerService;
import com.pes.jd.business.main.SmsManagerBusiness;
import com.pes.jd.dao.main.SmsServiceDao;
import com.pes.jd.model.DO.SmsServiceDO;
import com.pes.jd.model.DTO.SmsOrderDTO;
import com.pes.jd.model.DTO.SmsServiceDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.PayEnum;
import com.pes.jd.model.Enum.SmsServiceStepEnum;
import com.pes.jd.model.Param.PayParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.sdk.wxpay.MyWXPayConfig;
import com.pes.jd.sdk.wxpay.WXPay;
import com.pes.jd.sdk.wxpay.WXPayUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: aiJun
 * @Date: 2019-09-21 1:02
 * @Version 1.0
 */
@Service
@Slf4j
public class PayManagerServiceImpl implements PayManagerService {

    private static final String SUCCESS = "SUCCESS";
    @Autowired
    private SmsManagerBusiness smsManagerBusiness;
    @Autowired
    private MyWXPayConfig myWXPayConfig;
    @Autowired
    private SmsServiceDao smsServiceDao;

    @Override
    public ApiResponse buySmsService(PayParam payParam) throws Exception {
        if (SmsServiceStepEnum.checkIsNormalReq(payParam)) {
            //正常订单->将订单资料插入到数据库
            WXPay wxpay = new WXPay(myWXPayConfig);
            DecimalFormat decimalFormat = new DecimalFormat("###################");
            double pay = payParam.getOrderFe() * 100;
            String payFee = decimalFormat.format(pay);
            log.info("付款金额为：{}分,订单Id={}", payFee, payParam.getOrderId());
            Map<String, String> data = new HashMap<>();
            //回调地址
            String callBackPath = payParam.getDomain() + PayEnum.WX_PAY_PARAM_NOTIFY_URL.getParamValue();
            data.put(PayEnum.WX_PAY_PARAM_BODY.getParamName(), PayEnum.WX_PAY_PARAM_BODY.getParamValue());
            data.put(PayEnum.WX_PAY_PARAM_DEVICE_INFO.getParamName(), PayEnum.WX_PAY_PARAM_DEVICE_INFO.getParamValue());
            data.put(PayEnum.WX_PAY_PARAM_FEE_TYPE.getParamName(), PayEnum.WX_PAY_PARAM_FEE_TYPE.getParamValue());
            data.put(PayEnum.WX_PAY_PARAM_NOTIFY_URL.getParamName(), callBackPath);
            data.put(PayEnum.WX_PAY_PARAM_TRADE_TYPE.getParamName(), PayEnum.WX_PAY_PARAM_TRADE_TYPE.getParamValue());  // 此处指定为扫码支付
            data.put(PayEnum.WX_PAY_PARAM_PRODUCT_ID.getParamName(), PayEnum.WX_PAY_PARAM_PRODUCT_ID.getParamValue());
            data.put(PayEnum.WX_PAY_PARAM_OUT_TRADE_NO.getParamName(), payParam.getOrderId());
            data.put(PayEnum.WX_PAY_PARAM_TOTAL_FEE.getParamName(), payFee);//1 指的是0.01
            data.put(PayEnum.WX_PAY_PARAM_SPBILL_CREATE_IP.getParamName(), payParam.getIp());
            Map<String, String> resp = wxpay.unifiedOrder(data);
            if (SUCCESS.equals(resp.get("return_code"))) {
                Map<String, Object> map = new HashMap<>();
                String payUrl = resp.get("code_url");
                map.put("codeUrl", payUrl);
                map.put("orderId", payParam.getOrderId());
                int i = smsManagerBusiness.insertSmsOrder(payParam);
                log.info("wxResp={},付款连接为：{},插入订单的结果为：[{}],回调地址为：【{}】", resp, payUrl, i, callBackPath);
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, map);
            } else {
                log.error("shopId={},shopTitle={},付款码获取失败，原因：【{}】", payParam.getShopId(), payParam.getShopTitle(), resp.get("return_msg"));
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_ZF_01_03);
            }
        }
        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SMS_01_02);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApiResponse handlerWxOrderPayResult(String notityXml) throws Exception {
        //获取校验签名并获取支付结果
        Map<String, String> data = new HashMap<>();
        data.put("data", getResultStr(notityXml));
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, data);
    }

    @Override
    public ApiResponse checkOrderStatus(String orderId) {
        Map<String, Object> data = new HashMap<>();
        Byte orderStatus = smsManagerBusiness.selectOrderStatusBYOrderId(orderId);
        data.put("orderStatus", orderStatus);
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
    }


    private String getResultStr(String notityXml) throws Exception {
        Map<String, String> notifyMap = WXPayUtil.xmlToMap(notityXml);  // 转换成map
        WXPay wxpay = new WXPay(myWXPayConfig);
        if (wxpay.isResponseSignatureValid(notifyMap)) {
            // 签名正确
            // 进行处理。
            // 注意特殊情况：订单已经退款，但收到了支付结果成功的通知，不应把商户侧订单状态从退款改成支付成功
            //判断处理结果是否成功
            if (SUCCESS.equals(notifyMap.get("return_code"))) {
                String orderId = notifyMap.get("out_trade_no");
                log.info("select 1");
                SmsOrderDTO smsOrderDTO = smsManagerBusiness.selectSmsOrderByOrderId(orderId);
                log.info("select 2");
//                int j = smsManagerBusiness.updateSmsNumByShopId(smsOrderDTO.getNumber(), smsOrderDTO.getShopId());
                Integer rechargeNum = smsOrderDTO.getNumber();
                Long shopId = smsOrderDTO.getShopId();
                //增加或减少店铺剩余短信条数
                SmsServiceDTO smsServiceDTO = smsServiceDao.getByShopId(shopId);
                SmsServiceDO smsServiceDO = new SmsServiceDO();
                int j;
                if (smsServiceDTO != null) {
                    smsServiceDO.setId(smsServiceDTO.getId());
                    smsServiceDO.setNumber(smsServiceDTO.getNumber() + rechargeNum);
                    j = smsServiceDao.updateByPrimaryKey(smsServiceDO);
                } else {
                    smsServiceDO.setNumber(rechargeNum.longValue());
                    smsServiceDO.setUseNumber(0L);
                    smsServiceDO.setShopId(shopId);
                    j = smsServiceDao.insert(smsServiceDO);
                }

                log.info("select 3");
                int i = smsManagerBusiness.updateDateAndStatusByOrderId(orderId, new Date(), 1);
                log.info("通过orderId={},更新订单状态-交易成功时间结果为：{},更新短信最新条数结果为{}", orderId, i, j);
                return WxCallbackXml.SUCCESS.getValue();
            } else {
                //失败

            }
            //不做任何操作

            return "";

        } else {
            // 签名错误，如果数据里没有sign字段，也认为是签名错误
            log.error("签名验证错误--->不执行任何操作");
            return "";
        }
    }

    public enum WxCallbackXml {
        SUCCESS("<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>");
        private String value;

        WxCallbackXml(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

}
