package com.pes.jd.business.main.impl;

import com.pes.jd.business.main.PesUserMenuPermissionBusiness;
import com.pes.jd.dao.main.PesUserMenuPermissionDao;
import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.model.DO.PesUserMenuPermission;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/17 2:05 PM
 * @since 1.0.0
 */
@Service
public class PesUserMenuPermissionBusinessImpl implements PesUserMenuPermissionBusiness {

    @Autowired
    private PesUserMenuPermissionDao pesUserMenuPermissionDao;

    @Override
    public void insertByShopAccountsAndMenus(List<ShopAccountDTO> shopAccounts, List<PesMenuResource> menus) {
        int update = pesUserMenuPermissionDao.insertByShopAccountsAndMenus(shopAccounts, menus);
    }

    @Override
    public int selectCountByNick(String nick) {
        return pesUserMenuPermissionDao.selectCountByNick(nick);
    }

    @Override
    public void deleteByShopAccounts(List<ShopAccountDTO> shopAccounts) {
        int update = pesUserMenuPermissionDao.deleteByShopAccounts(shopAccounts);
    }

    @Override
    public void truncatePermission(List<ShopAccountDTO> shopAccounts) {
        pesUserMenuPermissionDao.truncatePermission(shopAccounts);
    }

    @Override
    public void deleteByShopAccounts(List<ShopAccountDTO> shopAccounts, List<PesMenuResource> menus) {
        pesUserMenuPermissionDao.deleteByShopAccountsAndMenu(shopAccounts,menus);
    }

    @Override
    public List<PesUserMenuPermission> selectByShopAccounts(List<ShopAccountDTO> shopAccounts) {
        return pesUserMenuPermissionDao.selectByShopAccounts(shopAccounts);
    }

    @Override
    public int insert(PesUserMenuPermission pesUserMenuPermission) {
        return pesUserMenuPermissionDao.insert(pesUserMenuPermission);
    }

    @Override
    public int deleteByMenuResourceId(Long id) {
        return pesUserMenuPermissionDao.deleteByMenuResourceId(id);
    }

    @Override
    public PesUserMenuPermission selectByMenuId(Long menuId,String nick) {
        return pesUserMenuPermissionDao.selectByMenuId(menuId,nick);
    }
    
}
