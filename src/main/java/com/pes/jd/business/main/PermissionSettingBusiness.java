package com.pes.jd.business.main;

import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.model.DO.PesServicePermission;
import com.pes.jd.model.DTO.ShopAccountDTO;
import com.pes.jd.model.VO.PermissionSettingListVo;
import com.pes.jd.model.VO.PermissionSettingVo;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/14 4:59 PM
 * @since 1.0.0
 */
public interface PermissionSettingBusiness {

    /**
     *  获取页面需要的基本数据  数据权限对应的菜单  原始菜单树
     * @param shopId 为了获取当前店铺的所有子账号
     * @param nick
     * @param userId
     * @return
     */
    PermissionSettingVo searchBaseInfo(Long shopId, String nick, Long userId);

    void addSysMenu(String nick);

    void deleteSysMenu(String nick);

    /**
     *  处理子的菜单集合
     */
    List<PesMenuResource> processSubMenuResources(List<PesMenuResource> menuResources, Long shopId);

    /**
     *  添加子账号权限
     * @param shopAccounts 子账号
     * @param permissions 权限（ 功能，数据 ）
     * @param menuResources 菜单权限
     */
    void addShopAccountPermission(List<ShopAccountDTO> shopAccounts,
                                  List<PesServicePermission> permissions,
                                  List<PesMenuResource> menuResources);
    /**
     *  修改子账号权限
     * @param shopAccounts 子账号
     * @param permissions 权限（ 功能，数据 ）
     * @param menuResources 菜单权限
     */
    void updateShopAccountPermission(List<ShopAccountDTO> shopAccounts,
                                     List<PesServicePermission> permissions,
                                     List<PesMenuResource> menuResources);

    /**
     *  删除子账号权限
     * @param shopAccounts 子账号
     */
    void deleteShopAccountPermission(List<ShopAccountDTO> shopAccounts);

    /**
     *  获取子账号所有的权限列表
     *
     * @param flag 如果是1的话 管理员能查询出所有权限
     * @param userId
     */
    List<PermissionSettingListVo> searchShopAccountPermissionsByShopId(Long shopId, String nick, Integer flag, Long userId);

    /**
     *  访问码设置
     * @param flag  1 设置访问码  2 清除访问码
     */
    void settingCode(String visitCode, String visitEmail, Integer flag, Long shopId);

    /**
     * 访问码验证
     */
    boolean validateCode(String visitCode, Long shopId);

    /**
     * 访问码找回
     */
    void findCode(Long shopId, String code);

    /**
     *  获取shop的visit email
     */
    String findEmail(Long shopId);

    /**
     * 查看当前店铺是否有访问码
     */
    boolean getCode(Long shopId);

}
