package com.pes.jd.business.main;

import com.pes.jd.model.DO.Cs;
import com.pes.jd.model.Query.UserQuery;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/15 9:28 AM
 * @since 1.0.0
 */
public interface CsBusiness {

    List<UserQuery> getNicks(String shopId, String groupId, String nick);

    List<UserQuery> getNicksNew(String shopId, String groupId, String nick);

    List<UserQuery> getNicksFromRedis(String shopId, String groupId, String nick);

    List<Cs> searchCsByNicks(List<String> nicks);

    List<UserQuery> getBoardNicks(String shopId) throws Exception;

	List<UserQuery> getAllNick(Set<Long> useShopIdsList);

}
