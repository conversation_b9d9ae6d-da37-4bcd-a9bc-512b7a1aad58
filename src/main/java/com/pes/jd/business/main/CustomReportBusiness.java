package com.pes.jd.business.main;

import com.pes.jd.model.DO.CustomReportDO;
import com.pes.jd.model.DTO.CustomReportDTO;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/26 4:24 PM
 * @since 1.0.0
 */
public interface CustomReportBusiness {
    /**
     *   查询自定义报表
     * @param type 报表类型
     *  1: 店铺
     *  2: 客服
     * @param shopId 店铺ID
     */
    List<CustomReportDTO> searchCustomReportByType(Integer type, Long shopId);

    /**
     *  删除 自定义报表
     * @param id
     * @return
     */
    int deleteByPrimaryKeyWithProperty(Long id);

    /**
     *  添加 自定义报表
     * @param record
     * @return
     */
    Long insert(CustomReportDO record);

    /**
     *  update
     * @param record
     * @return
     */
    int updateByPrimaryKey(CustomReportDO record);

    /**
     *  检查该店铺是否含有预定义报表
     * @param shopId
     * @param name
     * @return
     */
    int checkPredefine(Long shopId, String name);

    /**
     * 通过报表名查询所有的报表ID
     * @param reportName
     * @return
     */
    List<Long> searchReportIdByReportName(String reportName);

    List<CustomReportDTO> selectByShopAndName(Long shopId, String name);

}
