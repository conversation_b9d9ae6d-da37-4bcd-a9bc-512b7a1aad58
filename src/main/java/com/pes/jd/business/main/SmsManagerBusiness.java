package com.pes.jd.business.main;


import com.pes.jd.model.DTO.ShopSmsWordDTO;
import com.pes.jd.model.DTO.SmsOrderDTO;
import com.pes.jd.model.Param.PayParam;
import com.pes.jd.model.VO.SmsServiceVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Anthor: yuanxun
 * @Date: 11:11 2019/9/17
 * @Description:
 */
public interface SmsManagerBusiness {

    List<SmsOrderDTO> queryRechargeRecord(Date startDate, Date endDate, String nick,
                                          String orderId, Integer payWay, Integer orderStatus);
    Boolean recharge(String nick, Long shopId, Integer number, Double orderFee);

    Map<String, Object> queryShopSmsRemainingCount(Long shopId);

    Map<String, Object> querySmsBalanceCount();

    List<ShopSmsWordDTO> selectSmsWordByNick(String nick);

    int queryNotAiditSmsWordCount();

    void auditSmsWord(Long id, String auditRemark, Integer auditStutas);

    SmsServiceVO queryShopSmsCountAndPrice(Long shopId);

    double computePayAmount();

    int insertSmsOrder(PayParam payParam);

    int updateDateAndStatusByOrderId(String orderId, Date date, int status);

    List<SmsOrderDTO> selectSmsOrderByShopId(Long shopId);

    Byte selectOrderStatusBYOrderId(String orderId);

    SmsOrderDTO selectSmsOrderByOrderId(String orderid);

    int updateSmsNumByShopId(Integer number, Long shopId);

    void updateSmsUseNumber(Long valueOf, Integer successCount);

    void updateSmsUseNumberByFailCount(Long valueOf, Integer failCount);

    int judgeTaskStatus(Integer consultWordId, Integer silenceWordId, Date nowDate, Date taskStartDate, Integer permanent);

}
