package com.pes.jd.business;

import com.pes.jd.model.Param.JdSystemPageParam;
import com.pes.jd.model.Response.ApiResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

public interface AdminBusiness {

	ApiResponse adminLogin(String username, String password, HttpServletRequest request) throws Exception;

	ApiResponse queryShopByAdmin(String nick) throws Exception;

    ApiResponse queryAllShop() throws Exception;

	ApiResponse sysLoginEntrance(String shopId, HttpServletRequest request) throws Exception;

	ApiResponse queryJdSystemPage(JdSystemPageParam jdSystemPageParam);

	ApiResponse queryEveryServer(HttpServletRequest request);

	boolean UploadExcelInfo(String name, MultipartFile file);
}
