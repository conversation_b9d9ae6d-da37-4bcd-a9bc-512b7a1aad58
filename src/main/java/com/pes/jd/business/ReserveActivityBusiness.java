package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/3/13
 * @Modified By:
 */
public interface ReserveActivityBusiness {
    ApiResponse selectReserveActivityByShop(
            ShopQuery shop,
            String startDate,
            String endDate,
            String sku,
            String activityId,
            String type,
            String conditionType,
            String status) throws DBNotExistException;
}
