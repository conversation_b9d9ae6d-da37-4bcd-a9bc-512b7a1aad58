  
package com.pes.jd.business;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopAccountDTO;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.List;

/**  
 * ClassName:ShopBusiness <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月22日 下午1:25:15 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface ShopBusiness {

	List<ShopAccountDTO> searchShopAccountLstByShopId(Long shopId);

	JobShopDTO getJobShopInfoById(Long shopId);

	JobShopQuery getJobShop(Long shopId, Integer csType);
	
	JobShopDTO getJobShopInfoByVenderId(Long venderId);

	List<JobShopDTO> getActiveJobShopInfoByDelayTime(Integer hours);

	List<JobShopDTO> getAllActiveJobShopInfo();
	
}
  
