package com.pes.jd.business;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.pes.jd.model.DTO.CsTypeDayDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface CsTypeDayBusiness {
    void handleCsTypeDay(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData);

	Map<Date, List<CsTypeDayDTO>> selectCsLockStatusByShopByDate(JobShopDTO shop, List<Date> dateLst);
}
