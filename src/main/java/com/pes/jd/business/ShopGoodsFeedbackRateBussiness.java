package com.pes.jd.business;

import com.pes.jd.model.DTO.ShopGoodsFeedbackRateDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Param.ShopGoodsRateParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.DataAnalysisVO;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年04月15 17:06:06<br>
 */
public interface ShopGoodsFeedbackRateBussiness {
    DataAnalysisVO<ShopGoodsFeedbackRateDTO> searchShopGoodsFeedbackRateLst(ShopCommonParam shop, ShopGoodsRateParam param, SortPageQuery sortPageQuery) throws Exception;
}
