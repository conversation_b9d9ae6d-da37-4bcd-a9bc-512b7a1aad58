package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface DataPrepareBusiness {

	void pullShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData,boolean isYd) throws Exception;

	void calShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void pullShopRealtimeData(JobShopQuery jobShop, Boolean isDelData) throws Exception;

	void pullShopLoginLogData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData ) throws Exception;



}
