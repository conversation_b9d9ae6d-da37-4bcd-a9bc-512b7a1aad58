package com.pes.jd.business;

import com.pes.jd.model.DO.CsCustRecommendConsultSkuDO;
import com.pes.jd.model.DTO.CsCustRecommendConsultSkuDTO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface CsCustRecommendConsultSkuBusiness {
	int deleteByPrimaryKey(Long id);

	int insert(CsCustRecommendConsultSkuDO record);

	CsCustRecommendConsultSkuDO selectByPrimaryKey(Long id);

	int updateByPrimaryKeySelective(CsCustRecommendConsultSkuDO record);

    List<CsCustRecommendConsultSkuDTO> selectLstByBuyerNick(JobShopDTO shop, Collection<String> enquiryLossCustSet, Date date);
}
