  
package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

/**  
 * ClassName:CsPerformanceBusiness <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午2:05:26 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface CsPerformanceHandleBusiness {

	void handleShopCsPerformance(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	void handleShopCsPerformanceForToOrder(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void handleShopCsOrderIndex(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    void handleShopCsSaleAndOutStackData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    void handleShopCsOrderBind(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

}
  
