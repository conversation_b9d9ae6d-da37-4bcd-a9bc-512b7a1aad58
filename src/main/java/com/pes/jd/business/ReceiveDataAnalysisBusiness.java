package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ReceiveDataAnalysisBusiness {

	List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummary(UserShopQuery shop, GoodsRecommedParam param) throws Exception;


	ApiResponse selectGoodsConsultSummary(UserShopQuery shop, GoodsConsultParam param) throws DBNotExistException, Exception;
	ApiResponse selectGoodsConsultSummaryV2(UserShopQuery shop, GoodsConsultParam param) throws DBNotExistException, Exception;
	ApiResponse selectCsConsultAnalysis(UserShopQuery shop, GoodsConsultParam param) throws Exception;
	ApiResponse selectCsConsultAnalysisV2(UserShopQuery shop, GoodsConsultParam param) throws Exception;


	ApiResponse selectGoodsConsultDetail(UserShopQuery shop, GoodsConsultParam param) throws Exception;
	ApiResponse selectGoodsConsultDetailV2(UserShopQuery shop, GoodsConsultParam param) throws Exception;

	ApiResponse selectGoodsRecommendAnalysis(UserShopQuery shop, GoodsRecommedParam param) throws Exception;

	ApiResponse selectGoodsRecommendDetail(UserShopQuery shop, GoodsRecommedParam param, SortPageQuery sortPageQuery) throws Exception;

	ApiResponse selectDealOrderDetailLst(UserShopQuery shop, GoodsRecommedParam param, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

	void exportGoodsRecommendSummary(HttpServletResponse response, UserShopQuery shop, GoodsRecommedParam param);

	void exportGoodsRecommendDetail(HttpServletResponse response, UserShopQuery shop, GoodsRecommedParam param);

	ApiResponse serachCustomerReceiveRecordForChatSession(UserShopQuery shop, ShopSystemsettingDTO system, CustomerReceiveParam param,
                                                          SortPageQuery sortPageQuery) throws Exception;


	ApiResponse searchCustomerReciveRecordLst(UserShopQuery shop, CustomerReceiveParam param,
                                              SortPageQuery sortPageQuery) throws Exception;

    //客服维度查询
	ApiResponse selectReceiveFilter(UserShopQuery shop, ReceiveFilterParam param) throws Exception;

	//按会话维度
    ApiResponse selectReceiveFilterByChatSes(UserShopQuery shop, ReceiveFilterParam param) throws Exception;

	ApiResponse processCategoryData(List<ShopCategoryTreeDTO> categoryTree, Long categoryId, List<GoodsConsultSummaryDTO> consultSummaryDTOs, String shopId);

	ApiResponse selectGoodsConsultSummaryV3(UserShopQuery shop, GoodsConsultParam param) throws Exception;

	ApiResponse selectGoodsConsultDetailV3(UserShopQuery shop, GoodsConsultParam param) throws Exception;

	List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryV2(UserShopQuery shop, GoodsRecommedParam param, List<ShopCategoryTreeDTO> shopCategoryTree) throws Exception;

	ApiResponse selectGoodsRecommendDetailV2(UserShopQuery shop, GoodsRecommedParam param, SortPageQuery sortPageQuery) throws Exception;

	ApiResponse selectGoodsConsultSummaryV4(UserShopQuery shop, GoodsConsultParam param) throws Exception;

	ApiResponse processCategoryDataV2(List<ShopCategoryTreeDTO> shopCategoryTree, Long aLong, List<GoodsConsultSummaryV2DTO> consultSummaryDTOs);

	ApiResponse selectGoodsConsultDetailV4(UserShopQuery shop, GoodsConsultParam param) throws Exception;

	ApiResponse selectCsConsultAnalysisV3(UserShopQuery shop, GoodsConsultParam param) throws Exception;
}
