package com.pes.jd.business;

import com.pes.jd.model.Param.CustomerReceiveParam;
import com.pes.jd.model.Param.ReceiveFilterParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;

public interface ReceiveFilterAnalysisBussiness {

	ApiResponse serachCustomerReceiveRecordForChatSession(ShopQuery shop, CustomerReceiveParam param,
                                                          SortPageQuery sortPageQuery) throws Exception;

	ApiResponse searchCustomerReciveRecordLst(ShopQuery shop, CustomerReceiveParam param, SortPageQuery sortPageQuery)
			throws Exception;

	ApiResponse selectReceiveFilter(ShopQuery shop, ReceiveFilterParam param) throws Exception;

	//按会话
    ApiResponse selectReceiveFilterSession(ShopQuery selectedShop, ReceiveFilterParam param);
}
