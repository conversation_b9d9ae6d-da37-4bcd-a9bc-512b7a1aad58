package com.pes.jd.business;

import com.pes.jd.model.DO.ShopSmsSettingDO;
import com.pes.jd.model.DO.ShopSmsWordDO;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.ms.domain.Data.master.ShopSmsBacklist;
import com.pes.jd.ms.domain.Data.master.ShopSmsSetting;
import com.pes.jd.ms.domain.Data.master.ShopSmsWord;
import com.pes.jd.ms.domain.Response.RestApiResponse2;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月16 11:44:44<br>
 */
public interface ShopSmsManagerBussiness {
    Object saveOrUpdateShopSmsWord(ShopSmsWordDO record) ;

    Object deleteShopSmsWord(Long id) ;

    RestApiResponse2<ShopSmsWord> selectShopSmsWordById(Long id)  ;

    RestApiResponse2<List<ShopSmsWord>> selectShopSmsWordByShopId(Long shopId, String type);

    RestApiResponse2<List<ShopSmsWord>> selectShopSmsWordByIdByType(Long id, String type);

    Object saveOrUpdateShopSmsSetting(ShopSmsSettingDO record) ;

    RestApiResponse2<ShopSmsSetting> selectShopSmsSettingByShopId(Long shopId)  ;


    Object batchInsertShopSmsBacklist(Long shopId, Long userId, String recordStr) ;

    Object deleteShopSmsBacklistById(Long id) ;

     RestApiResponse2<List<ShopSmsBacklist>> selectShopSmsBacklistByShopIdByBuyerNick(MasterServiceShopQuery shop, String buyerNick, Date startDate, Date endDate) throws  Exception;

    Map<Integer,List<ShopSmsWord>> selectShopSmsWordByShopIdByAuditstatus(Long shopId, Integer statusOne, Integer statusTwo);

    Map<Integer,List<ShopSmsWord>> selectShopSmsWordByShopIdByMap(HashMap<String, Object> dataMap);

    RestApiResponse2<Boolean> selectSmsSuccessOrderCountByShopId(Long shopId);
}
