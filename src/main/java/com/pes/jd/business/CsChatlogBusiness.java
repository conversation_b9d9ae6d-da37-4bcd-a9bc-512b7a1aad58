package com.pes.jd.business;

import com.pes.jd.model.DTO.ChatlogDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/21 3:27 PM
 * @since 1.0.0
 */
public interface CsChatlogBusiness {

    /**
     *  获取指定时间段  chatLog 数据
     */
    List<ChatlogDTO> searchAllByTime(Long shopId,
                                     Date beginDate, Date endDate,String schema);


    void findSidOfCsServiceEvaluation(JobShopQuery jobShop, JobDateQuery jobDate);
}
