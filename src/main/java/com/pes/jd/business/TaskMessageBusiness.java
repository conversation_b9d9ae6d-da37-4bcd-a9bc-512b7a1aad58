package com.pes.jd.business;


import com.pes.jd.model.DTO.JobShopDTO;
import com.yiyitech.support.jcq.SendResult;

public interface TaskMessageBusiness {

    SendResult sendTaskJobMessageOfRedis(String shopId, String startDateStr, String endDateStr, String handleType, boolean isYd, JobShopDTO shop) throws  Exception;
    SendResult sendTaskJobMessageOfRedisFromParams(String shopId, String startDateStr, String endDateStr, String handleType, boolean isYd, boolean popIsYd, JobShopDTO shop) throws  Exception;


}
