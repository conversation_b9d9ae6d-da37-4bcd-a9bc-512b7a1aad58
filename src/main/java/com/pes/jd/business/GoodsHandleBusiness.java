package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *客服商品推荐和客户商品咨询处理
 */
public interface GoodsHandleBusiness {

	void handleRecommentAndConsultGoods(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
			throws Exception;

	void handleCsRecommentBuyerConsultGoodsSummary(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	void handleCsConsultGoodsSummaryV2(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	Map<String, Object> getFilterOrderIdsOfFinal(JobShopQuery jobShop,
												 Date startDate,
												 Date endDate,
												 List<Long> searchOrderIds);
	
}
