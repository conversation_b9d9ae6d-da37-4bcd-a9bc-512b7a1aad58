package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.GlobalMnoitorParam;
import com.pes.jd.model.Param.ShopBoardMnoitorParam;
import com.pes.jd.model.Param.ShopMnoitorParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface RealtimeCsPerformanceMonitorBusiness {

	ApiResponse selectBadReceiveMnoitor(ShopQuery shop, List<UserQuery> selectShopCs, Date startDate, Date endDate, String warningType) throws Exception;

	ApiResponse selectShopMnoitor(ShopQuery selectedShop, ShopMnoitorParam shopMnoitorParam) throws Exception;

	ApiResponse selectGlobalMnoitor(ShopBoardMnoitorParam shopBoardMnoitorParam) throws Exception;

	ApiResponse selectGlobalMnoitor(List<ShopBoardMnoitorParam> shopBoardMnoitorParamLst) throws DBNotExistException;

	ApiResponse warnDispatcher(ShopQuery selectedShop, String warnType, String csNick) throws Exception;

	ApiResponse updateWarnSetting(ShopQuery shop, Boolean openWarn, String warnAcceptCs) throws Exception;

	ApiResponse selectCurrentUserRealtimePerformance(ShopQuery selectedShop, String csNick, Set<String> preCsNickSet,
                                                     Set<String> csNickSet, Date startDate, Date endDate) throws Exception;


    ApiResponse selectGlobalMnoitor(String rtDb, GlobalMnoitorParam globalMnoitorParam) throws DBNotExistException;
}
