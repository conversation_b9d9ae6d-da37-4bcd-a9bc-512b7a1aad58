package com.pes.jd.business;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月06 15:18:18<br>
 */
public interface ShopUserAnalysisHandBussiness {
    void handShopUserAnalysis(JobShopDTO jobShop, JobDateQuery jobDate,
                              boolean isDelData) throws Exception;

     void updateShopUseAnalysisShopSale(JobShopQuery jobShop, JobDateQuery jobDate) throws  Exception;

     void calShopUserAnalysis(JobShopDTO jobShop, JobDateQuery jobDate) throws Exception;
}
