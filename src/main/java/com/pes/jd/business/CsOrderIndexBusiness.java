package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;

public interface CsOrderIndexBusiness {
	ApiResponse selectCsOrderIndex(ShopQuery shop, String csNicks, String startDate, String endDate,
                                   AssistServiceQuery assistServiceQuery, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws DBNotExistException;
}
