package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.Map;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/3/12
 * @Modified By:
 */
public interface CsPerformancePreordainBusiness {

    ApiResponse selectCsPerformancePreordain(ShopQuery shop, Map csNickMap, String activityId, String sku) throws DBNotExistException;

    ApiResponse selectCsPerformancePreordainSkuDetailed(ShopQuery shop, Map csNickMap, String activityId, String skuId, String skuName) throws DBNotExistException;

}
