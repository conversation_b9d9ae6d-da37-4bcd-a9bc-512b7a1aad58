package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface CsEvalHandleBussiness {
	
	void pullShopCsEvalDetails(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	void pullShopUpdateCsEvalDetails(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void pullShopCsSendEval(JobShopQuery shop, JobDateQuery jobDate, boolean isDelData) throws Exception;

}
