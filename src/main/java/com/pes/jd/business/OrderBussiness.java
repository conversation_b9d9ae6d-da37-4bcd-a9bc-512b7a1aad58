package com.pes.jd.business;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.pes.jd.model.DTO.BuyerOrderDTO;


public interface OrderBussiness {

	List<String> selectShopOrderLstByBuyersAndDateForAfterSale(Long shopId, String schemaId, List<String> buyerLst,
																	 Date startDate, Date endDate);
	void initHistoryOrderData(Long shopId, String startDateStr, String endDateStr, String rtDb, String rtSchemaId);
}
