package com.pes.jd.business;

import com.pes.jd.model.DTO.GoodsGroupSkuDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuDTO;
import com.pes.jd.model.Param.TaskParam;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ShopGoodsManagerBussiness {

	ApiResponse selectGoodsGroup(MasterServiceShopQuery shop) throws Exception;

	ApiResponse deleteGoodsGroup(MasterServiceShopQuery shop, Long goodsGroupId) throws Exception;

	ApiResponse saveOrUpdateGoodsGroup(MasterServiceShopQuery shop, String goodsGroupId, String goodsGroupName,
                                       String skuIds, Long userId, Byte dimension) throws Exception;

	ApiResponse selectCategoryLst(MasterServiceShopQuery shop) throws Exception;


	List<GoodsGroupSkuDTO> selectSelectedGoodsSkuLstByGroupId(ShopQuery shop, Long groupId, Byte dimension) throws Exception;


	Map<String, Object> selectShopGoodsSkuLst(MasterServiceShopQuery shop, Long groupId, Long categoryId, Long level,
                                              String skuName, Integer status, String skuIds, Integer pageSize, Integer pageNum, Byte dimension) throws Exception;

	Map<String, Object> selectShopWordGoodsSkuLst(ShopQuery shop, Long wordId, Long categoryId, Long level, String skuName, Integer goodsType, Integer status, String skuIds, Integer pageSize, Integer pageNum) throws Exception;

	Map<String, Object> selectGoodsSkuListBySkuIdLst(ShopQuery shop, Set<Long> skuIdLst, Integer goodsType) throws Exception;

    Map<String, Object> selectShopWordGoodsSkuLstNew(MasterServiceShopQuery shop, TaskParam taskParam) throws Exception;

	ApiResponse goodsImportByExcel(ShopQuery shop, Integer goodsType, XSSFWorkbook workbook, Boolean initFlag);

	List<ShopGoodsSkuDTO> selectShopGoodsSkuIdLstByCategoryId(UserShopQuery shop, Long categoryId, Integer status, Long level) throws Exception;
}
