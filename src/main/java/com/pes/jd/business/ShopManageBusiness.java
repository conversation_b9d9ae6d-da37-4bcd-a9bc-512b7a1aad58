package com.pes.jd.business;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopSplitKeyDTO;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.List;

public interface ShopManageBusiness {

    JobShopQuery getLocalJobShop(Long shopId, Integer csType);

    JobShopQuery getJobShop(Long shopId, Integer csType);
	
	JobShopDTO getJobShopInfoByVenderId(Long venderId);

	List<JobShopQuery> getAllActiveJobShop(Integer hours, Integer csType);

     JobShopDTO getShopSplitByShopId(Long shopId);

    List<JobShopDTO> getActiveShopLst();

    ShopSplitKeyDTO getShopSplitKeyInfo(Long aLong);
}
