package com.pes.jd.business;

import com.pes.jd.model.BO.LoginResultBO;
import com.pes.jd.model.DO.ShopInfoDO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSubScribeDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Param.LoginUserParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.Date;
import java.util.List;

public interface LoginBussiness {

	ApiResponse getShopInfo(String userNick, String sessionKey) throws Exception;

	ApiResponse getMainShopMemberShop(ShopQuery shop, Long memberShopId) throws Exception;

	ApiResponse loginSuccessInitDate(ShopDTO shop, Long userId, String csNick) throws Exception;

	ApiResponse getShopInfo(ShopDTO currentShop) throws Exception;

	ApiResponse commonLogin(LoginUserParam userParam, String switchFlag, ShopInfoDO shopInfo, Integer flag)
			throws Exception;

	ApiResponse getUserInfoByShopIdByUserNick(Long shopId, String userNick) throws Exception;

	Boolean getUserOperationLogByNickAndShop(Long shopId, String nick, String type);

	Boolean getUserOperationLogByNickAndShopAndTime(Long shopId, Date startDate, Date endDate, String nick, String type);

	Boolean getUserLoginCountByNickAndShopAndTime(Long shopId, Date startDate, Date endDate, String nick);

	Boolean getUserOperationLog(Long shopId, Date startDate, Date endDate, String nick, String type);

	List<ShopSubScribeDTO> selectShopSubByshopId(Long shopId);

	List<ShopUserDTO> selectUserListByShopId(String shopId) throws Exception;

	void updateShopInitFlag(Long shopId);

	LoginResultBO mobileLogin(Long shopId, String pin);


}
