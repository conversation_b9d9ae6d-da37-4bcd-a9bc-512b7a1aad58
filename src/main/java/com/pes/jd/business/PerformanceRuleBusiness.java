package com.pes.jd.business;

import com.pes.jd.model.BO.CsOrderBindJudgeBO;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DO.CsOrderIndexDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface PerformanceRuleBusiness {
	
	boolean isCsAutoReplyChat(boolean isFilteAutoReply, Byte mt, String content);

	boolean isCsAutoReplyBySysettingChat(ShopSystemsettingDTO sys, Byte mt, String content);
//
//    boolean isCsAutoReplyChat(boolean isFilteAutoReply, String autoReplyMark, String content);

//    boolean isCsAutoReplyBySysettingChat(ShopSystemsettingDTO sys, String content);
//
//    boolean isMainAccountAutoReplyChat(ShopSystemsettingDTO sys, String content);
//
//    boolean isMainAccountAutoReplyChat(ShopSystemsettingDTO sys, Byte mt, String content);
	
	Integer getBuyerChatFlag(ShopSystemsettingDTO sys, Set<String> accountSet,
			List<CsChatlogDTO> cls, String buyerNick);

/*	void handleForwardChat(
			JobShopDTO shop, Date date, List<CommonCsChatpeerDTO> forwardChatPeerLst,
			Map<String, CsDTO> csMap);

	void handleForwardChats(List<ForwardChatLogBO> fwdChats, Map<String, List<ChatBO>> cpBOMap,
							ShopSystemsettingDTO sys);*/

	boolean isValidCsResponse(long intervalTime, long maxWaitTimeMillisecond);

	boolean isValidCsResponse(Date csReplyDate, Date buyerChatDate, long maxWaitTimeMillisecond);

	Integer getSilentOrderUrgepayFlag(Integer silentUrgepayTime, CsOrderIndexDO csOrderIndex);

	Integer getOrderUrgepayFlag(Integer silentUrgepayTime, CsOrderIndexDO csOrderIndex);

    boolean isUrgePayLimitOrder(Boolean silentUrgepaySwitch, CsOrderBindJudgeBO csOrderBindJudge);

    boolean isBindSilentAllOrder(CsOrderIndexDTO csOrderIndex, ShopSystemsettingDTO sys);

	boolean isOrderBannerMark(Boolean orderFlagSwitch, Integer orderFlag, CsOrderIndexDTO csOrderIndex);

    boolean isAssitOrderCreate(CsOrderIndexDTO csOrderIndex);

    boolean hasAssit(CsOrderIndexDTO csOrderIndex);

    void handleCsActiveConvFailData(JobShopDTO shop, String csNick, Date date,
                                    List<ReceivedChatpeerDTO> chatpeers);


	CsOrderBindJudgeBO csOrderCreatedBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
                                          String csNick,
                                          Long orderId,
                                          List<CsOrderIndexDTO> orderCsIndexLst,
                                          Set<Long> toOrderedOrderIdSet, PresaleOrderDTO presaleOrderDTO);

	CsOrderBindJudgeBO csOrderPayBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
                                      String csNick,
                                      Long orderId,
                                      List<CsOrderIndexDTO> orderCsIndexLst,
                                      Set<Long> toPayOrderIdSet, PresaleOrderDTO presaleOrderDTO);


	CsOrderBindJudgeBO csPresaleOrderBalancePayBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
													String csNick,
													Long orderId,
													List<CsOrderIndexDTO> orderCsIndexLst,
													Set<Long> toPayOrderIdSet, List<PresaleOrderDTO> presaleOrder, List<CsOrderBindChatpeerDTO> chatpeers, Date sDate);

	CsOrderBindJudgeBO csSilentAllBind(JobShopDTO shop, ShopSystemsettingDTO sys, Date date, Date edate,
									   String csNick,
									   Long orderId,
									   List<CsOrderIndexDTO> orderCsIndexLst, List<CommonCsChatpeerDTO> receiveFilterChatPeerLst, PresaleOrderDTO presaleOrderDTO);

    PerformanceRuleValidDateBO getPerformanceRuleValidDate(JobShopQuery jobShop, JobDateQuery jobDate);

}
