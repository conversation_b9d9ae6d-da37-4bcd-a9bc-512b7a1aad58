package com.pes.jd.business;

import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.CustConversionTwoParam;
import com.pes.jd.model.Param.ShopMnoitorParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Result.rtsub.CsAllocateResult;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface RealtimeShopPerformanceSummaryBusiness {

	ApiResponse selectBadReceiveMnoitor(ShopQuery shopQuery, List<UserQuery> userQueryLst, Date startDate, Date endDate, String warningType, ShopSystemsettingDTO sys) throws Exception;
	
	ApiResponse selectShopRealtimePerformance(UserShopQuery shop, Date startDate, Date endDate) throws Exception;

	ApiResponse selectShopMnoitor(ShopQuery shopQuery, ShopMnoitorParam shopMnoitorParam) throws Exception;

	ApiResponse serachNeededAllocatedCsConversionList(UserShopQuery shop, CustConversionParam param) throws Exception;


	RestApiResponse2<CsAllocateResult> saveAllocatedCsConversionLst(UserShopQuery shop, ShopUserDTO user, String csConversions, Integer flag)
			throws Exception;


	RestApiResponse2<CsAllocateResult> oneStepSaveAllocatedCsConversionLst(UserShopQuery shop, ShopUserDTO user, CustConversionParam param)
			throws Exception;


	RestApiResponse2<CsAllocateResult> batchSaveAllocatedCsConversionLst(UserShopQuery shop, ShopUserDTO user, CustConversionParam param)
			throws Exception;

	ApiResponse getToolsSettingByShopId(UserShopQuery shop) throws Exception;

	ApiResponse setShopToolsSetting(UserShopQuery shop, String xiadanWord,
                                    String payWord, Boolean isSendGoodsUrl) throws Exception;


	ApiResponse serachAllocatedConvertedLst(UserShopQuery shop, CustConversionParam param) throws Exception;


	ApiResponse serachCsConversionTaskTotal(UserShopQuery shop, CustConversionParam param) throws Exception;


	ApiResponse setOneWarnCsConversion(ShopQuery shop, CustConversionParam param, String operateType)
			throws Exception;

	ApiResponse warnDispatcher(UserShopQuery shopQuery, String warnType, String csNick) throws Exception;

	ApiResponse updateWarnSetting(ShopQuery shop, Boolean openWarn, String warnAcceptCs) throws Exception;

	ApiResponse serachAllocateCsConversionForOneWarn(ShopQuery shop, CustConversionParam param) throws Exception;

	ApiResponse selectCurrentUserRealtimePerformance(UserShopQuery shop, String csNick, Set<String> preCsNickSet,
                                                     Set<String> csNickSet, Date startDate, Date endDate) throws Exception;

	ApiResponse updateAllocateCsNickById(ShopQuery shop, CustConversionParam param) throws Exception;

	Map<Integer, List<ShopRemindWordDTO>> selectShopRemindWordLst(ShopQuery shop, String origin, String csNick)  throws Exception;

	Map<String, Object> selectLoginCs(UserShopQuery shopQuery, List<UserQuery> userQueries, ShopUserDTO user) throws Exception;

	Map<String,Object> selectShopPoolTaskCount(ShopQuery shop, CustConversionParam param, String csNick) throws Exception;

	ApiResponse batchUpdateAllocateCsNick(ShopQuery shop, CustConversionParam param) throws Exception;

    ApiResponse serachCsConversionTaskTotalNew(UserShopQuery shopQuery, CustConversionParam param);

    ApiResponse serachAllocatedConvertedLstNew(UserShopQuery shopQuery, CustConversionTwoParam param, String followUpType) throws Exception;
}
