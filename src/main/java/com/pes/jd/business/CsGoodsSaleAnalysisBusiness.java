package com.pes.jd.business;

import com.pes.jd.exception.DBNotExistException;
import com.pes.jd.model.DTO.CsGoodsSaleIndexDTO;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;

import java.util.List;

public interface CsGoodsSaleAnalysisBusiness {
	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndex(UserShopQuery shop, GoodsConsultParam param) throws DBNotExistException, Exception;

	public ApiResponse selectCsGoodsSaleIndexDetail(UserShopQuery shop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam, Integer enquiryValidDurationTime) throws DBNotExistException, Exception;

	public ApiResponse selectCsGoodsSaleIndexAnalysis(UserShopQuery shop, GoodsConsultParam param) throws DBNotExistException, Exception;

	List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexV2(UserShopQuery shop, GoodsConsultParam param) throws Exception;

	ApiResponse selectCsGoodsSaleIndexDetailV2(UserShopQuery shop, GoodsConsultParam param, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam, Integer enquiryValidDurationTime) throws Exception;
}
