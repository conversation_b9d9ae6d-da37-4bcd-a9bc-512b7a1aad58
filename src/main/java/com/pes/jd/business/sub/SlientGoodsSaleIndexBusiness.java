package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.SlientGoodsSaleIndexDTO;
import com.pes.jd.model.Param.ShopCommonParam;

import java.io.OutputStream;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface SlientGoodsSaleIndexBusiness {
	public List<SlientGoodsSaleIndexDTO> selectGoodsSaleIndexByDateBySku(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst) throws SQLException;

	void exportSlientGoodsSaleSummary(OutputStream out, String jsonParam) throws Exception;

    List<SlientGoodsSaleIndexDTO> selectGoodsSaleIndexByDateBySkuOfSpu(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst) throws SQLException;
}
