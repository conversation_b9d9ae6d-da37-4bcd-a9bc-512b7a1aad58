package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.CsDutyRecordPerformanceDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/15 10:51 AM
 * @since 1.0.0
 */
public interface CsDutyRecordBusiness {

    List<CsDutyRecordPerformanceDTO> searchForPerformance(
            Long shopId,
            Set<String> nicks,
            Date startDate,
            Date endDate,
            String schemaId,
            String queryType,
            Set<Date> filterDates, Integer schedulingTimeDot);

}
