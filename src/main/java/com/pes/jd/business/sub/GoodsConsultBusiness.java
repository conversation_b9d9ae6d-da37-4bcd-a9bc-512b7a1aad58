package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.ShopCommonParam;

import java.io.OutputStream;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface GoodsConsultBusiness {
	List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(ShopCommonParam shop, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList) throws SQLException;
	List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(ShopCommonParam shop, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList) throws SQLException;

	List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuId(ShopCommonParam shop, Long skuId, Date startDate, Date endDate, List<String> csNickLst) throws SQLException;
	List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV2(ShopCommonParam shop, Long skuId, Date startDate, Date endDate, List<String> csNickLst) throws SQLException;


	List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(ShopCommonParam shop,
                                                                                          Integer result,
                                                                                          List<Long> skuLst,
                                                                                          Date startDate,
                                                                                          Date endDate,
                                                                                          List<String> csNickList,
                                                                                          String customer) throws Exception;
	List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(ShopCommonParam shop,
																						  Integer result,
																						  List<Long> skuLst,
																						  Date startDate,
																						  Date endDate,
																						  List<String> csNickList,
																						  String customer) throws Exception;

	void exportGoodsConsultSummary(OutputStream out, String jsonParam) throws Exception;
	void exportGoodsConsultSummaryV2(OutputStream out, String jsonParam) throws Exception;

	void exportGoodsConsultDetail(OutputStream out, String jsonParam)throws Exception;
	void exportGoodsConsultDetailV2(OutputStream out, String jsonParam)throws Exception;

	void exportGoodsConsultSummaryV3(OutputStream out, String jsonParam) throws Exception;

	void exportGoodsConsultDetailV3(OutputStream out, String jsonParam) throws Exception;

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickOfSpu(ShopCommonParam shop, String groupId, List<CsDTO> csLst, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickLst) throws SQLException;
    List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickOfSpuV2(ShopCommonParam shop, String groupId, List<CsDTO> csLst, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickLst) throws SQLException;

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIdOfSpu(ShopCommonParam shop, Long skuId, Date startDate, Date endDate, List<String> csNickLst) throws SQLException;
    List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdOfSpuV2(ShopCommonParam shop, Long skuId, Date startDate, Date endDate, List<String> csNickLst) throws SQLException;

    List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuOfSpu(ShopCommonParam shop, List<CsDTO> csLst, Integer result, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickLst, String customer) throws SQLException, Exception;
    List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuOfSpuV2(ShopCommonParam shop, List<CsDTO> csLst, Integer result, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickLst, String customer) throws SQLException, Exception;

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(ShopCommonParam shop, List<Long> skuLst, Long categoryId, Date startDate, Date endDate, List<String> csNickLst) throws Exception;

	List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV3(ShopCommonParam shop, Integer result, List<Long> skuLst, Long categoryId, Date startDate, Date endDate, List<String> csNickLst, String customer)throws Exception;

	List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(ShopCommonParam shop, List<Long> skuLst, Long categoryId, Date startDate, Date endDate, List<String> csNickLst) throws IllegalAccessException, SQLException;

	List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4(ShopCommonParam shop, Integer result, List<Long> skuLst, Long categoryId, Date startDate, Date endDate, List<String> csNickLst, String customer) throws IllegalAccessException, SQLException;

	List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV3(ShopCommonParam shop, Long skuId, Long categoryId, Date startDate, Date endDate, List<String> csNickLst) throws IllegalAccessException, SQLException;

	;
}
