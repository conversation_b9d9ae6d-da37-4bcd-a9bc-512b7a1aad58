package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.CsChatSessionServiceIndexBusiness;
import com.pes.jd.constants.PerformanceConstans;
import com.pes.jd.dao.sub.CsChatSessionServiceIndexDao;
import com.pes.jd.model.DTO.CsChatSessionServiceIndexDTO;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/14 3:42 PM
 * @since 1.0.0
 */
@SuppressWarnings("Duplicates")
@Service
public class CsChatSessionServiceIndexBusinessImpl implements CsChatSessionServiceIndexBusiness {
    @Autowired
    private CsChatSessionServiceIndexDao csChatSessionServiceIndexDao;
    @Override
    public List<CsChatSessionServiceIndexDTO> searchByDateShopCs(
            Set<String> nicks, Long shopId, Date startDate, Date endDate, String queryType, String schemaId, Set<Date> filterDates) {
        List<CsChatSessionServiceIndexDTO> csChatSessionServiceIndexDTOS =
                csChatSessionServiceIndexDao.searchByDateShopCs(nicks, shopId, startDate, endDate, queryType, schemaId, filterDates);
        final String date = "date";
        if (Objects.equals(date, queryType)) {
            //如果是日期则无需聚合
            return csChatSessionServiceIndexDTOS;
        }
        return CommonUtils.polymerize(csChatSessionServiceIndexDTOS,
                this::getInitObject, CsChatSessionServiceIndexDTO::getCsNick, (x, y) -> x.setCsNick(y.getCsNick()), x -> x.setShopId(shopId),
                (x, y) -> {
                    final Double respTimeFirstCount = BaseUtils.getNonNull(x.getRespTimeFirstCount());
                    //        接待的会话
                    final Integer sessionNum = BaseUtils.getNonNull(x.getSessionNum());
                    //        未回复
                    final Integer nonReply = BaseUtils.getNonNull(x.getNonReplySessionNum());
                    //        留言的会话
                    final Integer leaveMsgReceiveSessionNum = BaseUtils.getNonNull(x.getLeaveMsgReceiveSessionNum());
                    // 首次平均响应：本客服对顾客第一次回复用时的平均值 （基于接待量，判定的规则支持自定义，可点此设置）
                    int sess = BaseUtils.getNonNull(sessionNum) - BaseUtils.getNonNull(nonReply) - BaseUtils.getNonNull(leaveMsgReceiveSessionNum);
                    x.setAvgRespTimeFirst(sess <= 0 ? 0 : respTimeFirstCount / sess);
                    // 平均响应时间：本客服回复消息与顾客消息之间时间差的平均值（基于接待量，判定的规则支持自定义，可点此设置）
                    final Double respTimeCount = BaseUtils.getNonNull(x.getRespTimeCount());
                    //        回合数
                    final Integer chatRoundNum = BaseUtils.getNonNull(x.getChatRoundNum());
                    x.setAvgRespTime(chatRoundNum <= 0 ? 0 : respTimeCount / chatRoundNum);
                    // 平均会话时长：本|客服平均每通会话从会话建立到会话关闭的时间间隔。会话时长=会话关闭时间-会话开始时间（包含10分钟会话等待时间，留言接待的不计入）
                    final Double sessionDurationTimeCount = BaseUtils.getNonNull(x.getSessionDurationTimeCount());
                    x.setAvgSessionDurationTime(sessionDurationTimeCount <= 0 ? 0 : sessionDurationTimeCount / sessionNum);
                }, PerformanceConstans.IGNORE_PROPERTIES);
    }

    private CsChatSessionServiceIndexDTO getInitObject() {
        CsChatSessionServiceIndexDTO dto = new CsChatSessionServiceIndexDTO();
        dto.setId(0L);
        dto.setShopId(0L);
        dto.setDate(null);
        dto.setCsNick("");
        dto.setConsultSessionNum(0);
        dto.setReceiveSessionNum(0);
        dto.setReceiveSessionDurationTime(0.0D);
        dto.setDirectReceiveSessionNum(0);
        dto.setForwardInSessionNum(0);
        dto.setForwardOutSessionNum(0);
        dto.setCustConsultSessionNum(0);
        dto.setCsToCustSessionNum(0);
        dto.setChatNum(0);
        dto.setCustChatNum(0);
        dto.setCsChatNum(0);
        dto.setCsWordNum(0);
        dto.setAvgCsMsgSessionNum(0.0D);
        dto.setMaxReceiveSessionNum(0);
        dto.setNonReplySessionNum(0);
        dto.setLeaveMsgSessionNum(0);
        dto.setLeaveMsgReceiveSessionNum(0);
        dto.setSlowRespSessionNum(0);
        dto.setLongRespSessionNum(0);
        dto.setAvgRespTimeFirst(0.0D);
        dto.setAvgRespTime(0.0D);
        dto.setAvgSessionDurationTime(0.0D);
        dto.setAvgRespInQuickTime(0);
        dto.setSessionNum(0);
        return dto;
    }
}
