package com.pes.jd.business.sub.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.ShopGoodsBusiness;
import com.pes.jd.dao.sub.ShopCategoryDao;
import com.pes.jd.dao.sub.ShopGoodsDao;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.ShopCommonParam;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service
public class ShopGoodsBusinessImpl implements ShopGoodsBusiness {

    @Resource
    private ShopGoodsDao shopGoodsDao;
    @Resource
    private ShopCategoryDao shopCategoryDao;
    @Override
    public List<ShopGoodsDTO> selectShopGoodsByShopId(ShopDTO shop, Long categoryId, String name, Integer status) {
        return shopGoodsDao.selectShopGoodsByShopId(shop, categoryId, name, status);
    }

    @Override
    public List<ShopGoodNameDTO> selectShopGoodsByShopIdByWareIds(ShopCommonParam shop, List<Long> wareIds) {
        return shopGoodsDao.selectShopGoodsByShopIdByWareIds(shop, wareIds);
    }

    @Override
    public List<ShopGoodNameDTO> selectShopGoodsByCategoryIdBySkuNameByStatus(ShopCommonParam shop, List<Long> categoryLst, String wareName, String status, List<Long> wareIdLst, Integer pageSize, Integer pageNum) {
        return shopGoodsDao.selectShopGoodsByCategoryIdBySkuNameByStatus(shop, categoryLst, wareName, status, wareIdLst, pageSize, pageNum);
    }

    @Override
    public IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(IPage<ShopGoodsSkuDTO> goodsSkuLstPage, Long shopId, String tableName, String joinTableName, List<Long> categoryLst, String skuName, String status, List<Long> excludeWareIds, List<Long> includeWareIds, List<Long> topSku, String propertity, String sortDirection) {
        return shopGoodsDao.selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(goodsSkuLstPage, shopId, tableName, joinTableName, categoryLst, skuName, status, excludeWareIds, includeWareIds, topSku, propertity, sortDirection);
    }

    @Override
    public Map<String, Object> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(ShopCommonParam shop, String categoryId, Long level, String skuName, String status, List<Long> skuIdLst, Integer pageSize, Integer pageNum) {
        Map<String, Object> result = Maps.newHashMap();
        List<Long> threeLevelParentLst = getLeaveCategory(shop, categoryId, level);
        List<ShopGoodsSkuDTO> goodsLst = shopGoodsDao.selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(shop, threeLevelParentLst, skuName, status, skuIdLst, pageSize, pageNum);
        int count = shopGoodsDao.selectCountShopGoods(shop, threeLevelParentLst, skuName, status, skuIdLst);
        result.put("goodsSkuLst", goodsLst);
        result.put("count", count);
        return result;
    }

    @Override
    public List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByWareIdsLst(ShopCommonParam shop, List<Long> wareIds) {
        return shopGoodsDao.selectShopGoodsSkuLstByWareIdsLst(shop, wareIds);
    }

    @Nullable
    public List<Long> getLeaveCategory(ShopCommonParam shop, String categoryId, Long level) {
        List<Long> threeLevelParentLst=null;
        if (StringUtils.isNotBlank(categoryId) && !level.equals(0L)) {
            if (level.equals(3L)) {
                threeLevelParentLst = Lists.newArrayList();
                threeLevelParentLst.add(Long.valueOf(categoryId));
            } else if (level.equals(1L)) {
                List<Long> parentLst = Lists.newArrayList();
                parentLst.add(Long.valueOf(categoryId));
                List<Long> twoLevelParentLst = getCategoryLst(shop, parentLst);
                threeLevelParentLst = getCategoryLst(shop, twoLevelParentLst);
            } else {
                List<Long> twoLevelParentLst = Lists.newArrayList();
                twoLevelParentLst.add(Long.valueOf(categoryId));
                threeLevelParentLst = getCategoryLst(shop, twoLevelParentLst);
            }
        }
        return threeLevelParentLst;
    }

    private List<Long> getCategoryLst(ShopCommonParam shop,List<Long> parentLst){
        List<ShopCategoryDTO> categorysLst = shopCategoryDao.selectCategoryIdByShopIdByParentId(shop,parentLst);
        List<Long> twoLevelParentLst=Lists.newArrayList();
        for (ShopCategoryDTO cat : categorysLst) {
            twoLevelParentLst.add(cat.getCategoryId());
        }
        return twoLevelParentLst;
    }
}
