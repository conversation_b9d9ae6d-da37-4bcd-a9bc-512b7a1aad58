package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.CustomerReceiveDTO;
import com.pes.jd.model.DTO.ShopGoodsFeedbackRateDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.JSON.EnquiryOrderLossVO;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.VO.*;

import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ShopDataAnalysisBussiness {


    List<ReceiveBuyerVO> searchChatpeerLst(ShopCommonParam shop, Date startDate, Date endDate, List<UserQuery> csLst, String buyerNickKeyword,
                                           String keyWord) throws Exception;

    List<NewChatLogVO> searchChatlogLst(ShopCommonParam shop, Date startDate, Date endDate, String sid, UserQuery userQuery, String buyerNick, ShopSystemsettingDTO shopSystemsetting, boolean isSlowResp);

    List<ChatLogVO> searchChatlogLstForPlugin(ShopCommonParam shop, Date startDate, Date endDate, String sid, UserQuery userQuery, String buyerNick);

    Map<String, Object> queryCsDutyRecordSituation(ShopCommonParam shop, CsLoginlogParam csLoginlogParam, Date startTime, Date endTime, Boolean dutyRidCsSwitch) throws Exception;

    Map<String, Object> queryCsDutyRecordSituationDetail(ShopCommonParam shop, CsLoginlogParam csLoginlogParam, Date startTime, Date endTime) throws Exception;

    Map<String, Object> queryCsLoginOperateDetail(ShopCommonParam shop, Date startTime, Date endTime,
                                                  UserQuery userQuery, Long delayTime) throws Exception;

    Map<String, Object> searchTradeRecordLst(UserShopQuery shop, Date startDate, Date endDate, String buyerNick, String sellerType, int start, int length, String groupId, String csNick);

    EnquiryLostVO searchEnquiryLostRecordLst(LossOrderParam lossOrderParam, Date sDate, Date eDate, Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds) throws Exception;

    Long saveOrUpdateLostRecordNote(ShopCommonParam shop, String buyerNick, Date date, Long noteId, String note,
                                    Integer lostType, String orderId);

    List<DealAnalysisVo> searchTradeFilterLst(UserShopQuery shop, Date startDate, Date endDate, String buyerNick, boolean filterType);


    //List<SilenceSaleAnalysisVO> searchSilenceSaleAnalysisLst(ShopQuery shop, Date startDate,
    //                                                         Date endDate, String buyerNick, String orderId, Integer orderType, Integer start, Integer length) throws Exception;


    List<SilenceLostRecord> searchSilentOrderLostRecordLst(LossOrderParam lossOrderParam, Date sDate, Date eDate, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;

    List<EnquiryOrderLossVO> searchEnquiryOrderLostRecordLst(LossOrderParam lossOrderParam, Date sDate, Date eDate, OrderInfoLogUploadParam orderInfoLogUploadParam);



    DataAnalysisVO<CustomerReceiveDTO> searchCustomerReciveRecordLst(ShopCommonParam shop, CustomerReceiveParam param,
                                                                     SortPageQuery sortQuery) throws Exception;

    void exportShopReciveRecordAnalysisLst(OutputStream out, String jsonParams) throws Exception;

    void exportReciveFilterAnalysisLst(OutputStream out, String jsonParams) throws Exception;



    void exportChatlogLstQuery(OutputStream out, String jsonParam) throws Exception;

    void exportEnquiryOrderLostRecord(OutputStream out, String jsonParams) throws Exception;

    void exportSilentOrderLostRecord(OutputStream out, String jsonParams) throws Exception;

    void exportEnquiryLostRecord(OutputStream out, String jsonParams) throws Exception;
    
    List<NewChatLogVO> searchChatlogLstForConver(ShopCommonParam shop, Date startDate, Date endDate, String sid, List<UserQuery> userQuery, String buyerNick);


    Map<String, Object> searchChatlogLst(ShopCommonParam shop, Date startDate, Date endDate, String sid, UserQuery userQuery, String buyerNick, List<String> keyWordLst, Integer direction);

    EnquiryLostVO searchEnquiryLostRecordLstOfSpu(LossOrderParam lossOrderParam, Date sDate, Date eDate, Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds);

    DataAnalysisVO<ShopGoodsFeedbackRateDTO> searchShopGoodsFeedbackRateLst(ShopCommonParam shop, ShopGoodsRateParam param, SortPageQuery sortPageQuery) throws Exception;


    //会话
    DataAnalysisVO<CsCustChatSessionVO> serachCustomerReceiveRecordForChatSession(ShopCommonParam shop, CustomerReceiveParam param,
                                                                                  SortPageQuery page) throws Exception;


    //按人数
    List<ReceiveFilterRecordVO> searchShopReciveFilterRecordLst(ShopCommonParam shop, ReceiveFilterParam param) throws Exception;

    //按会话
    List<ReceiveFilterRecordSessionVO> searchShopReciveFilterRecordLstSession(ShopCommonParam shop, ReceiveFilterParam receiveParam);
}
