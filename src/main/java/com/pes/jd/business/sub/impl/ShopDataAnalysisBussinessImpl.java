package com.pes.jd.business.sub.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.business.sub.*;
import com.pes.jd.dao.sub.CsOrderBindDao;
import com.pes.jd.dao.sub.CsOrderIndexDao;
import com.pes.jd.dao.sub.LostRecordNoteDao;
import com.pes.jd.dao.sub.OrderDao;
import com.pes.jd.model.DO.LostRecordNote;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.JSON.EnquiryOrderLossVO;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.*;
import com.pes.jd.office.excel.ExportExcel;
import com.pes.jd.office.excel.ExportExcelBean;
import com.pes.jd.office.param.ExeclColumnParam;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import com.pes.jd.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.OutputStream;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class ShopDataAnalysisBussinessImpl implements ShopDataAnalysisBussiness {
    private static final Logger logger = LoggerFactory.getLogger(ShopDataAnalysisBussinessImpl.class);

    @Autowired
    private LoginlogAnalysisBusiness loginlogAnalysisBusiness;

    @Autowired
    private ReceiveAnalysisBusiness receiveAnalysisBusiness;

    @Autowired
    private ChatAnalysisBusiness chatAnalysisBusiness;

    @Autowired
    private OrderDao orderDao;

    @Autowired
    private CsOrderBindDao csOrderBindDao;

    @Autowired
    private CsOrderIndexDao csOrderIndexDao;

    @Autowired
    private LostRecordNoteDao lostRecordNoteDao;

    @Autowired
    private LostRecordAnalysisBusiness lostRecordAnalysisBusiness;

    @Autowired
    private RestTemplate restTemplate;
    
    @Resource
    private ShopGoodsFeedbackRateBussiness shopGoodsFeedbackRateBussiness;

    //按人数
    @Override
    public List<ReceiveFilterRecordVO> searchShopReciveFilterRecordLst(ShopCommonParam shop, ReceiveFilterParam param) throws Exception {
        List<ReceiveFilterRecordVO> receiveLst = receiveAnalysisBusiness.searchShopReciveFilterRecordLst(shop, param);

        return receiveLst;
    }

    //按会话
    @Override
    public List<ReceiveFilterRecordSessionVO> searchShopReciveFilterRecordLstSession(ShopCommonParam shop, ReceiveFilterParam param) {
        List<ReceiveFilterRecordSessionVO> receiveLst = receiveAnalysisBusiness.searchShopReciveFilterRecordLstSession(shop, param);

        return receiveLst;
    }

    @Override
    public void exportShopReciveRecordAnalysisLst(OutputStream out, String jsonParams) throws Exception {

        // 解析json字符串格式 参数
        JSONObject object = JSONObject.parseObject(jsonParams);
        String operateType = object.getString("operateType");
        if (operateType.equals("1")) {
            receiveAnalysisBusiness.exportCustomerReceiveRecord(out, jsonParams);
        } else {
            // 会话维度
            receiveAnalysisBusiness.exportCustomerReceiveRecordForChatSession(out, jsonParams);
        }
    }

    @Override
    public void exportReciveFilterAnalysisLst(OutputStream out, String jsonParams) throws Exception {

        // 解析json字符串格式 参数
        JSONObject object = JSONObject.parseObject(jsonParams);
        String operateType = object.getString("operateType");
        if (operateType.equals("1")) {
            receiveAnalysisBusiness.exportReciveFilterAnalysis(out, jsonParams);
        } else {
            // 会话维度
            receiveAnalysisBusiness.exportReciveFilterAnalysisForChatSession(out, jsonParams);
        }
    }

    @Override
    public List<ReceiveBuyerVO> searchChatpeerLst(ShopCommonParam shop, Date startDate, Date endDate, List<UserQuery> csLst,
                                                  String buyerNickKeyword, String keyWord) throws Exception {
        if (CollectionUtils.isEmpty(csLst)) {
            return Lists.newArrayList();
        }
        List<String> chatPeerLst = chatAnalysisBusiness.searchReceiveChatpeerLst(shop, startDate, endDate,
                csLst, buyerNickKeyword, keyWord);
        Set<String> receiveBuyerSet = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(chatPeerLst)) {
            for (String cp : chatPeerLst) {
                receiveBuyerSet.add(cp.trim());
            }
        }
        UserQuery userQuery = csLst.get(0);//必选一个客服，不能多选
        List<String> receiveBuyerLst = Lists.newArrayList(receiveBuyerSet);
//        List<String> retNotEmptyChatLogBuyerLst = chatAnalysisBusiness.searchReceiveNotEmptyChatLogByChatPeerLst(shop, startDate, endDate, userQuery.getNick(), receiveBuyerLst);
        List<ReceiveBuyerVO> retNotEmptyChatLogBuyerLst = chatAnalysisBusiness.searchReceiveNotEmptyChatLogVOByChatPeerLst(shop, startDate, endDate, userQuery.getNick(), receiveBuyerLst);
//        List<ReceiveBuyerVO> retLst = Lists.newArrayList();
//        ReceiveBuyerVO receiveBuyer = null;
//        for (String buyer : retNotEmptyChatLogBuyerLst) {
//            receiveBuyer = new ReceiveBuyerVO();
//            receiveBuyer.setBuyerNick(buyer);
//            retLst.add(receiveBuyer);
//        }
//        return retLst;

        return retNotEmptyChatLogBuyerLst;
    }

    @Override
    public Map<String, Object> queryCsDutyRecordSituation(ShopCommonParam shop, CsLoginlogParam csLoginlogParam, Date startDate, Date endDate, Boolean dutyRidCsSwitch) throws Exception {
        return loginlogAnalysisBusiness.queryCsDutyRecordSituation(shop, csLoginlogParam, startDate, endDate,dutyRidCsSwitch);
    }

    @Override
    public Map<String, Object> queryCsDutyRecordSituationDetail(ShopCommonParam shop, CsLoginlogParam csLoginlogParam, Date startDate, Date endDate) throws Exception {
        return loginlogAnalysisBusiness.queryCsDutyRecordSituationDetail(shop, csLoginlogParam, startDate, endDate);
    }

    @Override
    public Map<String, Object> queryCsLoginOperateDetail(ShopCommonParam shop, Date startDate, Date endDate,
                                                         UserQuery shopCs, Long delayTime) throws Exception {
        return loginlogAnalysisBusiness.queryCsLoginOperateDetail(shop, startDate, endDate, shopCs, delayTime);
    }

    @Override
    public List<NewChatLogVO> searchChatlogLst(ShopCommonParam shop, Date startDate, Date endDate, String sid, UserQuery userQuery,
                                               String buyerNick, ShopSystemsettingDTO shopSystemsetting, boolean isSlowResp) {
        List<ChatLogDTO> chatlogLst = Lists.newArrayList();
        List<String> csNickLst = Lists.newArrayList();
        /*************计算慢响应需要用到的参数  start***************/
        //慢响应的毫秒值
        // /*慢响应时间*/
        final Integer slowResponseTime = shopSystemsetting.getSlowResponseTime();
        final long slowResponseTimeMillis = TimeUnit.SECONDS.toMillis(slowResponseTime);
//        /*慢响应次数  -slowResponseTime */
//        final Integer slowResponseTimesNum = shopSystemsetting.getSlowResponseTimesNum();
//        int slowCount = 0;
        /*标记上一次是谁说话*/
        String lastSay = null;
        String csSayFlag = "CS_SAY";
        String buyerSayFlag = "BUYER_SAY";
        /*长接待设定*/
        final long longReceptionTime = TimeUnit.MINUTES.toMillis(shopSystemsetting.getLongReceptionTime());

        /*计算首次响应时间（买家第一次说话时间，如果接待时间差超过最长等待时间，
        那此值，不会是'买家第一次说话的时间'，而是过滤掉超过最长等待时间的第一次接待的买家说话时间）*/
//        long buyerFirstSendTime = 0;
        /*************计算慢响应需要用到的参数  end***************/

        List<NewChatLogVO> newChatLogVOLst = Lists.newArrayList();
        csNickLst.add(userQuery.getNick());
        chatlogLst
                .addAll(chatAnalysisBusiness.searchBuyerChatlogs(shop, csNickLst, buyerNick, null, sid, startDate, endDate));
        if (CollectionUtils.isNotEmpty(chatlogLst)) {

            //将一次会话是一个人说的情况放入到list中
            ChatLogVO msg = null;
            Date sDate;
            Date eDate;
            List<List<ChatLogVO>> tempLst;
            List<ChatLogVO> chatLogVOLst = new ArrayList<>();
            //判断是否需要创建新的集合
            int flag = -1;
            NewChatLogVO<List<List<ChatLogVO>>> listNewChatLogVO;
            /*买家发送聊天的时间*/
            Date buyerSendLogTime = null;
            //根据sid分组
            Map<String, List<ChatLogDTO>> sidLst = chatlogLst.stream().collect(Collectors.groupingBy(ChatLogDTO::getSid));
            Set<Entry<String, List<ChatLogDTO>>> entries = sidLst.entrySet();
            for (Entry<String, List<ChatLogDTO>> entry : entries) {
                String sidResult = entry.getKey();
                listNewChatLogVO = new NewChatLogVO<>();
                newChatLogVOLst.add(listNewChatLogVO);
                tempLst = new ArrayList<>();
                listNewChatLogVO.setSid(sidResult);
                listNewChatLogVO.setChatLogVOLst(tempLst);
                List<ChatLogDTO> chatLogs = entry.getValue();
                for (int i = 0; i < chatLogs.size(); i++) {
                    ChatLogDTO chatLog = chatLogs.get(i);
                    final long thisChatLogTime = chatLog.getTime().getTime();
                    final boolean csAutoReplyBySysettingChat =
                            this.isCsAutoReplyBySysettingChat(shopSystemsetting, chatLog.getContent());
                    if (i == 0) {
                        sDate = chatLog.getTime();
                        flag = chatLog.getDirection();
                        listNewChatLogVO.setStartDate(sDate);
                        //---添加卖家昵称
                        listNewChatLogVO.setBuyerNick(chatLog.getBuyerNick());
                        listNewChatLogVO.setCsSimpleNick(userQuery.getSimpleName());
                        chatLogVOLst = new ArrayList<>();
                        tempLst.add(chatLogVOLst);
                    } else {
                        if (flag != chatLog.getDirection()) {
                            flag = chatLog.getDirection();
                            chatLogVOLst = new ArrayList<>();
                            tempLst.add(chatLogVOLst);
                        }
                    }

                    if (i == chatLogs.size() - 1) {
                        eDate = chatLog.getTime();
                        listNewChatLogVO.setEndDate(eDate);
                    }
                    msg = new ChatLogVO(shop.getShopId(), false);
                    msg.setBuyerNick(chatLog.getBuyerNick());
                    msg.setDirection(chatLog.getDirection());
                    msg.setCsNick(chatLog.getCsNick());
                    msg.setChatTime(chatLog.getTime());
                    msg.setContent(chatLog.getContent());
                    msg.setCsSimpleNick(userQuery.getSimpleName());
                    msg.setGroupId(userQuery.getGroupId());
                    if (chatLog.getDirection() == 0) {
                        //客服说话，变换位置，前端只用一个buyerNick字段
                        msg.setBuyerNick(msg.getCsSimpleNick());
                    }
                    /****************************判断是否是慢响应的聊天    start***************************************/
                    //标记这次是谁说话
                    boolean csSay = csSay(chatLog);

                    if (csSay) {
                        /*过滤自动回复*/
                        if (csAutoReplyBySysettingChat) {
                            chatLogVOLst.add(msg);
                            continue;
                        }


                        /*上次如果是买家说话*/
                        if (Objects.equals(lastSay, buyerSayFlag)) {
                            /*最近对话回合的耗时*/
                            final long recentlySessionTime = (thisChatLogTime - buyerSendLogTime.getTime());
                            /*计算慢响应，如果响应时间大于设定时间，并且这种情况的次数大于设定次数，则为慢响应*/
                            // 超过最长等待时间就不算慢响应  --job计算的时候有这个
                            /*表示这次接待 超过了最长等待时间*/
//                            if (recentlySessionTime > slowResponseTimeMillis &&
//                                    ++slowCount >= slowResponseTimesNum) {
//                                msg.setSlowResponseFlag(true);
//                            }
                            if (recentlySessionTime < longReceptionTime && isSlowResp && recentlySessionTime > slowResponseTimeMillis) {
                                msg.setSlowResponseFlag(true);
                            }
                        }
                        buyerSendLogTime = null;
                    } else {
                        /*如果买家发送聊天时间为空，那么设置属性值*/
                        if (buyerSendLogTime == null) {
                            buyerSendLogTime = chatLog.getTime();
                        }
//                        if (buyerFirstSendTime == 0) {
//                            buyerFirstSendTime = thisChatLogTime;
//                        }
                    }

                    /*标记此次说话是谁说的*/
                    lastSay = csSay ? csSayFlag : buyerSayFlag;
                    /****************************判断是否是慢响应的聊天    end  ***************************************/
                    chatLogVOLst.add(msg);

                }
            }

        }

        //按时间升序排列
        if (CollectionUtils.isNotEmpty(newChatLogVOLst)) {
            newChatLogVOLst = newChatLogVOLst.stream().sorted(Comparator.comparing(NewChatLogVO::getStartDate)).collect(Collectors.toList());
        }
        return newChatLogVOLst;
    }

    private boolean isCsAutoReplyBySysettingChat(ShopSystemsettingDTO sys, String content) {
        if(content == null){
            return false;
        }
        return isCsAutoReplyChat(sys.getAutoReplySwitch(), sys.getAutoReplyMark(), content);
    }

    public boolean isCsAutoReplyChat(boolean isFilteAutoReply, String autoReplyMark, String content){
        if(content == null){
            return false;
        }
        return isFilteAutoReply && (content.startsWith(autoReplyMark) ||
                "【此消息为欢迎卡片或富文本模板答案，聊天记录中暂不支持展示】".equals(content));
    }

    @Override
    public List<ChatLogVO> searchChatlogLstForPlugin(ShopCommonParam shop, Date startDate, Date endDate, String sid, UserQuery userQuery, String buyerNick) {


        List<ChatLogDTO> chatlogLst = Lists.newArrayList();
        List<String> csNickLst = Lists.newArrayList();
        csNickLst.add(userQuery.getNick());
        chatlogLst
                .addAll(chatAnalysisBusiness.searchBuyerChatlogs(shop, csNickLst, buyerNick, null, sid, startDate, endDate));
        List<ChatLogVO> msgs = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(chatlogLst)) {
            ChatLogVO msg = null;
            for (ChatLogDTO clog : chatlogLst) {
                msg = new ChatLogVO(shop.getShopId());
                msg.setBuyerNick(clog.getBuyerNick());
                msg.setDirection(clog.getDirection());
                msg.setCsNick(clog.getCsNick());
                msg.setChatTime(clog.getTime());
                msg.setContent(clog.getContent());
                msg.setCsSimpleNick(userQuery.getSimpleName());
                msg.setGroupId(userQuery.getGroupId());
                if (clog.getDirection() == 0) {
                    //客服说话，变换位置，前端只用一个buyerNick字段
                    msg.setBuyerNick(msg.getCsSimpleNick());
                }
                msgs.add(msg);
            }
        }
        return msgs;
    }

    /**
     * 聊天记录导出 TODO
     *
     * @throws Exception
     */
    @Override
    public void exportChatlogLstQuery(OutputStream out, String jsonParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);

        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        ShopCommonParam shop = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());
        Date startDate = jsonObject.getDate("startDate");
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        String buyerNick = jsonObject.getString("buyerNick");
        String keyWord = jsonObject.getString("keyWord");
        String csNick = jsonObject.getString("csNick");

        Map<String, Object> csSimpleNickMap = JacksonUtils.json2map(jsonObject.getString("csSimpleNickMap"));
        List<UserQuery> csNickInfo = Lists.newArrayList();
        UserQuery uq = new UserQuery();
        uq.setNick(csNick);
        if (csSimpleNickMap.containsKey(csNick)) {
            uq.setSimpleName((String) (csSimpleNickMap.get(csNick)));
            csNickInfo.add(uq);
        }
        List<ReceiveBuyerVO> searchChatpeerLst = searchChatpeerLst(shop, startDate, endDate, csNickInfo, buyerNick, keyWord);
        List<String> buyerLst = searchChatpeerLst
                .stream()
                .map(ReceiveBuyerVO::getBuyerNick)
                .collect(Collectors.toList());
        List<ChatLogDTO> chatlogLst = chatAnalysisBusiness.searchCsChatlogs(shop, csNick, buyerLst, startDate, endDate);

        if (CollectionUtils.isNotEmpty(chatlogLst)) {
            ExeclTableParam<ChatLogExportVO> tableParam = new ExeclTableParam<ChatLogExportVO>();
            List<ExeclColumnParam> columnParams = Lists.newArrayList();
            List<ChatLogExportVO> voLst = Lists.newArrayList();
            ChatLogExportVO vo = null;
            Map<String, List<ChatLogDTO>> chatLogBuyerMap = chatlogLst.stream().collect(Collectors.groupingBy(ChatLogDTO::getBuyerNick));
            for (Entry<String, List<ChatLogDTO>> buyerChatLogEntry : chatLogBuyerMap.entrySet()) {
                String org = buyerChatLogEntry.getKey();
                String startStr = org.substring(0, 1);
                String endStr = org.substring(org.length() - 1);
//                            String target = org.replaceAll("(?<=\\S{1})\\S(?=\\S{1})","*");
                String target = startStr + "***" + endStr;
                voLst.add(new ChatLogExportVO("聊天买家:" + target, "", null));
                for (ChatLogDTO chatlog : buyerChatLogEntry.getValue()) {
                    vo = new ChatLogExportVO(chatlog.getDirection() == 0 ? uq.getSimpleName() : target, chatlog.getContent(), chatlog.getTime());
                    voLst.add(vo);
                }
                voLst.add(new ChatLogExportVO("", "", null));
            }
            tableParam.setData(voLst);
            tableParam.setColumnParams(columnParams);
            columnParams.add(new ExeclColumnParam("发送者", "sender"));
            columnParams.add(new ExeclColumnParam("聊天内容", "content"));
            columnParams.add(new ExeclColumnParam("聊天时间", "chatTime", 2));

            String title = "聊天记录";
            ExportExcel exort = new ExportExcel();
            exort.execlExport(title, tableParam, out);
        }
    }

    @Override
    public Map<String, Object> searchTradeRecordLst(UserShopQuery shop, Date startDate, Date endDate, String buyerNick,
                                                    String sellerType, int start, int length, String groupId, String csNick) {
        Map<String, Object> map = new HashMap<String, Object>(2);
        if ("-1".equals(sellerType)) {
            List<DealAnalysisVo> dealAnalysisVoLst = null;
            int countNum = 0;
            // 全部
            if (StringUtils.isNotBlank(groupId) || StringUtils.isNotBlank(csNick)) {
                countNum = csOrderBindDao.getCsSaleTradeByDateAndBuyNickAndShopIdAcount(shop, startDate, endDate,
                        buyerNick);
                dealAnalysisVoLst = csOrderBindDao.selectCsSaleTradeByDateAndBuyNickAndShopId(shop, startDate, endDate,
                        buyerNick, start, length);
            } else {
                countNum = orderDao.getShopDealTradesCount(shop, startDate, endDate, buyerNick);
                dealAnalysisVoLst = orderDao.selectShopDealTradesByPage(shop, startDate, endDate, buyerNick, start,
                        length);
            }
            return ConversionDataMethod(shop.getCsNickInfo(), dealAnalysisVoLst, 1, countNum);
        } else if ("1".equals(sellerType)) {
            // 客服销售额
            int countNum = csOrderBindDao.getCsSaleTradeByDateAndBuyNickAndShopIdAcount(shop, startDate, endDate,
                    buyerNick);
            List<DealAnalysisVo> dealAnalysisVoLst = csOrderBindDao.selectCsSaleTradeByDateAndBuyNickAndShopId(shop,
                    startDate, endDate, buyerNick, start, length);
            return ConversionDataMethod(shop.getCsNickInfo(), dealAnalysisVoLst, 2, countNum);
        } else {
            if (StringUtils.isNotBlank(groupId) || StringUtils.isNotBlank(csNick)) {
                map.put("dealAnalysisVoLst", new ArrayList<DealAnalysisVo>());
                map.put("totalNum", 0);
                return map;
            }
            int countNum = orderDao.getSilenceSaleTradeCount(shop, startDate, endDate, buyerNick);
            List<DealAnalysisVo> dealAnalysisVoLst = orderDao.getSilenceSaleTradeByDateAndBuyNickAndShopId(shop,
                    startDate, endDate, buyerNick, start, length);
            return ConversionDataMethod(shop.getCsNickInfo(), dealAnalysisVoLst, 3, countNum);
        }

    }

    private Map<String, Object> ConversionDataMethod(List<UserQuery> csNickInfo, List<DealAnalysisVo> dealAnalysisVoLst,
                                                     int type, int totalNum) {
        Map<String, Object> map = new HashMap<String, Object>();
        int count = 0;
        if (CollectionUtils.isNotEmpty(dealAnalysisVoLst)) {
            Map<String, String> simpleNameMap = csNickInfo.stream()
                    .collect(Collectors.toMap(UserQuery::getNick, cs -> cs.getSimpleName()));
            Iterator<DealAnalysisVo> it = dealAnalysisVoLst.iterator();
            while (it.hasNext()) {
                DealAnalysisVo dvo = it.next();
                if (type == 1) {
                    if (StringUtils.isBlank(dvo.getCsNick())) {
                        if (CollectionUtils.isNotEmpty(csNickInfo) && csNickInfo.size() == 1) {
                            count++;
                            it.remove();
                        } else {
                            dvo.setSellerType("静默销售");
                        }
                    } else {
                        dvo.setSellerType("客服销售");
                    }
                } else if (type == 2) {
                    dvo.setSellerType("客服销售");
                } else {
                    dvo.setSellerType("静默销售");
                }
                dvo.setCsNick(simpleNameMap.get(dvo.getCsNick()));

            }

        }
        map.put("dealAnalysisVoLst", dealAnalysisVoLst);
        map.put("totalNum", totalNum - count);
        return map;
    }

    @Override
    public List<SilenceLostRecord> searchSilentOrderLostRecordLst(LossOrderParam lossOrderParam, Date startDate, Date endDate, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception {
        return lostRecordAnalysisBusiness.searchSilentOrderLostRecordLst(lossOrderParam, startDate, endDate, orderInfoLogUploadParam);
    }

    @Override
    public EnquiryLostVO searchEnquiryLostRecordLst(LossOrderParam lossOrderParam, Date startDate, Date endDate, Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds) throws Exception {
        return lostRecordAnalysisBusiness.searchEnquiryLostRecordLst(lossOrderParam, startDate, endDate, chatLimitNum, sortPageQuery, sessionDuration, skuIds);
    }

    @Override
    public Long saveOrUpdateLostRecordNote(ShopCommonParam shop, String buyerNick, Date date, Long noteId,
                                           String note, Integer lostType, String orderId) {
        LostRecordNoteDTO existedNote = lostRecordNoteDao.getShopBuyerLostRecordNoteByDate(shop, date, buyerNick,
                orderId, lostType);
        if (existedNote == null) {
            LostRecordNote lostRecordNote = new LostRecordNote();
            lostRecordNote.setBuyerNick(buyerNick);
            lostRecordNote.setDate(date);
            lostRecordNote.setNote(note);
            lostRecordNote.setLostType(lostType);
            lostRecordNote.setOrderId("".equals(orderId) ? null : Long.parseLong(orderId));
            lostRecordNote.setShopId(shop.getShopId());
            int insertNum = lostRecordNoteDao.insertLostRecordNoteForShop(shop, date, lostRecordNote);
            if (insertNum > 0) {
                LostRecordNoteDTO getLostRecordNote = lostRecordNoteDao.getShopBuyerLostRecordNoteByDate(shop, date,
                        buyerNick, orderId, lostType);
                return getLostRecordNote.getId();
            } else {
                return null;
            }
        } else {
            existedNote.setNote(note);
            int update = lostRecordNoteDao.updateLostRecordNoteForShop(shop, date, existedNote);
            if (update > 0) {
                LostRecordNoteDTO getLostRecordNote = lostRecordNoteDao.getShopBuyerLostRecordNoteByDate(shop, date,
                        buyerNick, "", lostType);
                return getLostRecordNote.getId();
            } else {
                return null;
            }
        }
    }

    @Override
    public List<DealAnalysisVo> searchTradeFilterLst(UserShopQuery shop, Date startDate, Date endDate, String buyerNick,
                                                     boolean filterType) {
        List<DealAnalysisVo> dealAnalysisVoLst = csOrderIndexDao
                .getCsSaleTradeByDateAndBuyNickAndIsMrnFilterAndShopId(shop, startDate, endDate, buyerNick, filterType);
        if (CollectionUtils.isEmpty(dealAnalysisVoLst)) {
            return new ArrayList<DealAnalysisVo>();
        }

        if (CollectionUtils.isNotEmpty(shop.getCsNickInfo())) {
            Map<String, String> simpleNameMap = shop.getCsNickInfo().stream()
                    .collect(Collectors.toMap(UserQuery::getNick, cs -> cs.getSimpleName()));
            for (DealAnalysisVo dvo : dealAnalysisVoLst) {
                if (dvo.getIsMrnFilter()) {
                    dvo.setFilterType("最低应答数过滤");
                } else {
                    dvo.setFilterType("--");
                }
                dvo.setCsNick(simpleNameMap.get(dvo.getCsNick()));
            }
        }

        return dealAnalysisVoLst;
    }

    /**
     * 获取店铺下的咚咚信息
     *
     * @param shopId
     * @param type
     */
    @SuppressWarnings("unchecked")
    public List<CsDTO> selectShopCswwSimpleNames(@RequestParam(required = true, name = "shopId") Long shopId,
                                                 @RequestParam(required = true, name = "type") Integer type) {
        String url = "http://jd-pes-master-provider/cs/manage/selectShopCswwSimpleNames";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<String, Object>();
        paramMap.add("shopId", shopId);
        paramMap.add("type", type);
        ApiResponse apiResponse = null;
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(paramMap);
        try {
            apiResponse = restTemplate.postForObject(url, request, ApiResponse.class);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        List<CsDTO> csLst = null;
        if (apiResponse != null && apiResponse.getData() != null) {
            csLst = (List<CsDTO>) apiResponse.getData().get("csLst");
        } else {
            csLst = Lists.newArrayList();
        }
        return csLst;
    }

    @Override
    public List<EnquiryOrderLossVO> searchEnquiryOrderLostRecordLst(LossOrderParam lossOrderParam, Date startDate, Date endDate, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        return lostRecordAnalysisBusiness.searchEnquiryOrderLostRecordLst(lossOrderParam, startDate, endDate, orderInfoLogUploadParam);
    }

    /**
     * TODO 询单流失分析导出		searchChatpeerLst
     */
    @Override
    public void exportEnquiryLostRecord(OutputStream out, String jsonParams) throws Exception {

        // 解析json字符串格式 参数
        JSONObject object = JSONObject.parseObject(jsonParams);
        // 获取店铺对象，其中有csNickList
        ShopQuery shopQuery = JacksonUtils.json2pojo(object.getString("shopQuery"), ShopQuery.class);
        List<String> csNickLst = shopQuery.getCsNickLst();
        Map<String, Object> csSimpleNickMap = JacksonUtils.json2map(object.getString("csSimpleNickMap"));
        List<UserQuery> csNickInfo = Lists.newArrayList();
        for (String csNick : csNickLst) {
            UserQuery uq = new UserQuery();
            uq.setNick(csNick);
            if (csSimpleNickMap.containsKey(csNick)) {
                uq.setSimpleName((String) (csSimpleNickMap.get(csNick)));
                csNickInfo.add(uq);
            }
        }
        Date startDate = DateUtil.getStartDateFromDateStr(object.getString("startDate"));
        Date endDate = DateUtil.getEndDateFromDateStr(object.getString("endDate"));
        Integer chatLimitNum = object.getInteger("chatLimitNum");
        String buyerNick = object.getString("buyerNick");
        String sortDirection = object.getString("sortDirection");
        String propertity = object.getString("propertity");
        Long currentPage = object.getLong("currentPage");
        String skuIds = object.getString("skuIds");
        Integer sessionDuration = object.getInteger("sessionDuration");
        SortPageQuery sortPageQuery = new SortPageQuery();
        sortPageQuery.setPropertity(propertity);
        sortPageQuery.setSortDirection(sortDirection);
        sortPageQuery.setCurrentPage(currentPage);
        sortPageQuery.setSize(0L);//导出查询所有，走前端分页
        LossOrderParam lossOrderParam = new LossOrderParam(csNickInfo, buyerNick, null, null, null);
        lossOrderParam.setShopId(shopQuery.getShopId());
        lossOrderParam.setSchemaId(shopQuery.getSchemaId());
        logger.info("exportEnquiryLostRecord query start......");
        /************确定维度 start ***************/
        EnquiryLostVO enquiryLostVo = getEnquiryLostVoByDimension(object, lossOrderParam, startDate, endDate, chatLimitNum, sortPageQuery, sessionDuration, skuIds);
        /************确定维度 end ***************/
        List<EnquiryLostRecordVO> enquiryLostRecordLst = null;
        if (enquiryLostVo != null) {
            enquiryLostRecordLst = enquiryLostVo.getEnquiryLostRecordList();
            //格式化数据跟前端同步
            doFormatData(enquiryLostRecordLst);
        }
        logger.info("exportEnquiryLostRecord query end......");

        logger.info("exportEnquiryLostRecord export start......");
        // 声明
        ExeclTableParam<EnquiryLostRecordVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        // 设置数据
        tableParam.setData(enquiryLostRecordLst);
        columnParams.add(new ExeclColumnParam("日期", "date", 1));
        columnParams.add(new ExeclColumnParam("开始时间", "startDate", 2));
        columnParams.add(new ExeclColumnParam("结束时间", "endDate", 2));

        columnParams.add(new ExeclColumnParam("对话句数", "chatNum"));
        columnParams.add(new ExeclColumnParam("会话时长", "consumeTimeStr"));

        columnParams.add(new ExeclColumnParam("顾客昵称", "buyerNick"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("备注", "note"));

        columnParams.add(new ExeclColumnParam("商品", "goodStr"));
        String sheetName = "询单流失记录";
        // 设置第一行显示头
        tableParam.setColumnParams(columnParams);
        ExportExcel exort = new ExportExcel();
        // 导出
        exort.execlExport(sheetName, tableParam, out);
        logger.info("exportEnquiryLostRecord export end......");
    }

    private EnquiryLostVO getEnquiryLostVoByDimension(JSONObject jsonObject, LossOrderParam lossOrderParam, Date startDate, Date endDate, Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds) throws Exception {
        Byte dimension = jsonObject.getByte("dimension");
        //获取spu维度下所有的sku
        if (dimension != null && dimension == 2) {//spu维度
            return searchEnquiryLostRecordLstOfSpu(lossOrderParam, startDate, endDate,
                    chatLimitNum, sortPageQuery, sessionDuration, skuIds);
        }
        return searchEnquiryLostRecordLst(lossOrderParam, startDate, endDate,
                chatLimitNum, sortPageQuery, sessionDuration, skuIds);
    }

    /**
     * 将sku集合拼接成新的字符串
     * @param enquiryLostRecordLst
     */
    private void doFormatData(List<EnquiryLostRecordVO> enquiryLostRecordLst) {
        if(CollectionUtils.isEmpty(enquiryLostRecordLst)) return;
        for (EnquiryLostRecordVO enquiryLostRecordVO : enquiryLostRecordLst) {

            //格式化秒钟
//            System.out.println("秒钟格式化----->>>>"+secToTime(enquiryLostRecordVO.getConsumeTime()));
            enquiryLostRecordVO.setConsumeTimeStr(secToTime(enquiryLostRecordVO.getConsumeTime()));

            List<String> goodsLst = enquiryLostRecordVO.getGoodsLst();
            if(CollectionUtils.isEmpty(goodsLst)) continue;
            String goodStr = goodsLst.stream().collect(Collectors.joining(","));
            enquiryLostRecordVO.setGoodStr(goodStr);

        }
    }


    public static String secToTime(long time) {
        String timeStr;
        long hour = 0;
        long minute = 0;
        long second = 0;
        if (time <= 0)
            return "0秒";
        else {
            minute = time / 60;
            if (minute < 60) {
                second = time % 60;
                timeStr = minute + "分" + second+"秒";
            } else {
                hour = minute / 60;
//                if (hour > 99)
//                    return "99小时59分59秒";
                minute = minute % 60;
                second = time - hour * 3600 - minute * 60;
                timeStr = hour + "小时" + minute + "分" + second+"秒";
            }
        }
        return timeStr;
    }

    /**
     * TODO 询单下单未付款流失分析导出
     */
    @Override
    public void exportEnquiryOrderLostRecord(OutputStream out, String jsonParams) throws Exception {

        // 解析json字符串格式 参数
        JSONObject object = JSONObject.parseObject(jsonParams);
        // 获取店铺对象，其中有csNickList

        ShopQuery shopQuery = JacksonUtils.json2pojo(object.getString("shopQuery"), ShopQuery.class);
        List<String> csNickLst = shopQuery.getCsNickLst();
        Map<String, Object> csSimpleNickMap = JacksonUtils.json2map(object.getString("csSimpleNickMap"));
        List<UserQuery> csNickInfo = Lists.newArrayList();
        for (String csNick : csNickLst) {
            UserQuery uq = new UserQuery();
            uq.setNick(csNick);
            if (csSimpleNickMap.containsKey(csNick)) {
                uq.setSimpleName((String) (csSimpleNickMap.get(csNick)));
                csNickInfo.add(uq);
            }
        }
        Date startDate = DateUtil.getStartDateFromDateStr(object.getString("startDate"));
        Date endDate = DateUtil.getEndDateFromDateStr(object.getString("endDate"));
        String orderId = object.getString("orderId");
        String orderType = object.getString("orderType");
        String buyerNick = object.getString("buyerNick");
        LossOrderParam lossOrderParam = new LossOrderParam(csNickInfo, buyerNick, orderId, orderType, null);
        lossOrderParam.setShopId(shopQuery.getShopId());
        lossOrderParam.setSchemaId(shopQuery.getSchemaId());
        logger.info("exportEnquiryOrderLostRecord query start......");
        List<EnquiryOrderLossVO> enquiryOrderLostRecordLst = lostRecordAnalysisBusiness
                .searchEnquiryOrderLostRecordLst(lossOrderParam, startDate, endDate, null);
        logger.info("exportEnquiryOrderLostRecord query end......");

        // 声明
        ExeclTableParam<EnquiryOrderLossVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        // 设置数据
        tableParam.setData(enquiryOrderLostRecordLst);
        columnParams.add(new ExeclColumnParam("开始时间", "startDate", 2));
        columnParams.add(new ExeclColumnParam("结束时间", "endDate", 2));
        columnParams.add(new ExeclColumnParam("顾客昵称", "buyerNick"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("订单编号", "orderId"));
        columnParams.add(new ExeclColumnParam("下单时间", "orderCreated", 2));
        columnParams.add(new ExeclColumnParam("订单金额(元)", "orderPayment", "%.2f"));
        columnParams.add(new ExeclColumnParam("订单状态", "orderType"));
        columnParams.add(new ExeclColumnParam("备注", "note"));
        logger.info("exportEnquiryOrderLostRecord export start......");
        String sheetName = "询单下单未付款流失记录";
        // 设置第一行显示头
        tableParam.setColumnParams(columnParams);
        //ExportExcel exort = new ExportExcel();
        //交给spring处理方便aop
        ExportExcelBean exort = SpringUtil.getBean(ExportExcelBean.class);
        //上传参数
        CommonLogUploadParam commonUpParam = JacksonUtils.json2pojo(object.getString("commonUpParam"), CommonLogUploadParam.class);
//        UploadDBOperationParam uploadParam = UploadDBOperationBusinessImpl.getParam(commonUpParam,null);
//        uploadParam.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_ENQUIRYORDERLOSTRECORD.getName());

        tableParam.setOrderInfoLogUploadParam(commonUpParam);
        // 导出
        exort.execlExport(sheetName, tableParam, out);
        logger.info("exportEnquiryOrderLostRecord export end......");

       // uploadDBOperationBusiness.upload(uploadParam);
    }

    /**
     * TODO 静默下单未付款流失分析导出
     */
    @Override
    public void exportSilentOrderLostRecord(OutputStream out, String jsonParams) throws Exception {
        // 解析json字符串格式 参数
        JSONObject object = JSONObject.parseObject(jsonParams);
        // 获取店铺对象，其中有csNickList

        ShopQuery shopQuery = JacksonUtils.json2pojo(object.getString("shopQuery"), ShopQuery.class);
        Map<String, Object> csSimpleNickMap = JacksonUtils.json2map(object.getString("csSimpleNickMap"));
        List<UserQuery> csNickInfo = Lists.newArrayList();
        for (Entry<String, Object> csEntry : csSimpleNickMap.entrySet()) {
            UserQuery uq = new UserQuery();
            uq.setNick(csEntry.getKey());
            uq.setSimpleName((String) csEntry.getValue());
            csNickInfo.add(uq);
        }
        Date startDate = DateUtil.getStartDateFromDateStr(object.getString("startDate"));
        Date endDate = DateUtil.getEndDateFromDateStr(object.getString("endDate"));
        String orderId = object.getString("orderId");
        String orderType = object.getString("orderType");
        String buyerNick = object.getString("buyerNick");
        String receiveStatus = object.getString("receiveStatus");
        LossOrderParam lossOrderParam = new LossOrderParam(csNickInfo, buyerNick, orderId, orderType, receiveStatus);
        lossOrderParam.setShopId(shopQuery.getShopId());
        lossOrderParam.setSchemaId(shopQuery.getSchemaId());
        logger.info("exportSilentOrderLostRecord query start......");
        List<SilenceLostRecord> silentOrderLostRecordLst = lostRecordAnalysisBusiness
                .searchSilentOrderLostRecordLst(lossOrderParam, startDate, endDate, null);
        logger.info("exportSilentOrderLostRecord query end......");

        // 声明
        ExeclTableParam<SilenceLostRecord> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        // 设置数据
        tableParam.setData(silentOrderLostRecordLst);
        logger.info("exportSilentOrderLostRecord export start......");
        columnParams.add(new ExeclColumnParam("订单编号", "orderId"));
        columnParams.add(new ExeclColumnParam("顾客昵称", "buyerNick"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("下单时间", "orderCreated", 2));
        columnParams.add(new ExeclColumnParam("开始时间", "startDate", 2));
        columnParams.add(new ExeclColumnParam("结束时间", "endDate", 2));
        columnParams.add(new ExeclColumnParam("订单金额(元)", "orderPayment", "%.2f"));
        columnParams.add(new ExeclColumnParam("订单状态", "orderType"));
        columnParams.add(new ExeclColumnParam("备注", "note"));

        String sheetName = "静默下单未付款流失记录";
        // 设置第一行显示头
        tableParam.setColumnParams(columnParams);
        //ExportExcel exort = new ExportExcel();
        //交给spring处理方便aop
        ExportExcelBean exort = SpringUtil.getBean(ExportExcelBean.class);
        //上传参数
        CommonLogUploadParam commonUpParam = JacksonUtils.json2pojo(object.getString("commonUpParam"), CommonLogUploadParam.class);
//        UploadDBOperationParam uploadParam = UploadDBOperationBusinessImpl.getParam(commonUpParam,null);
//        uploadParam.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_SILENCEORDERLOSTRECORD.getName());

        tableParam.setOrderInfoLogUploadParam(commonUpParam);
        // 导出
        exort.execlExport(sheetName, tableParam, out);
        logger.info("exportSilentOrderLostRecord export end......");

        //uploadDBOperationBusiness.upload(uploadParam);
    }

    @Override
    public DataAnalysisVO<CustomerReceiveDTO> searchCustomerReciveRecordLst(ShopCommonParam shop, CustomerReceiveParam param, SortPageQuery sortQuery) throws Exception {
        param.setPageFlag(true);
        return receiveAnalysisBusiness.searchCustomerReciveRecordLst(shop, param, sortQuery);
    }

    @Override
    public DataAnalysisVO<CsCustChatSessionVO> serachCustomerReceiveRecordForChatSession(ShopCommonParam shop, CustomerReceiveParam param, SortPageQuery page) throws Exception {
        return receiveAnalysisBusiness.serachCustomerReceiveRecordForChatSession(shop, param, page);
    }

	@Override
	public List<NewChatLogVO> searchChatlogLstForConver(ShopCommonParam shop, Date startDate, Date endDate, String sid,
                                                        List<UserQuery> userQuery, String buyerNick) {
		List<ChatLogDTO> chatlogLst = Lists.newArrayList();
        List<String> csNickLst = Lists.newArrayList();

        List<NewChatLogVO> newChatLogVOLst = Lists.newArrayList();
        for(UserQuery uQuery:userQuery){
            csNickLst.add(uQuery.getNick());
        }
        Map<String, String> userMap = userQuery.stream().collect(Collectors.toMap(UserQuery::getNick, c->c.getSimpleName(),(c1,c2)->c2));
        chatlogLst
                .addAll(chatAnalysisBusiness.searchBuyerChatlogs(shop, csNickLst, buyerNick, null, sid, startDate, endDate));
        if (CollectionUtils.isNotEmpty(chatlogLst)) {

            //将一次会话是一个人说的情况放入到list中
            ChatLogVO msg = null;
            Date sDate;
            Date eDate;
            List<List<ChatLogVO>> tempLst;
            List<ChatLogVO> chatLogVOLst = new ArrayList<>();
            //判断是否需要创建新的集合
            int flag = -1;
            NewChatLogVO<List<List<ChatLogVO>>> listNewChatLogVO;

            /*买家发送聊天的时间*/
            Date buyerSendLogTime = null;

            //根据sid分组
            Map<String, List<ChatLogDTO>> sidLst = chatlogLst.stream().collect(Collectors.groupingBy(ChatLogDTO::getSid));
            Set<Entry<String, List<ChatLogDTO>>> entries = sidLst.entrySet();
            for (Entry<String, List<ChatLogDTO>> entry : entries) {
                String sidResult = entry.getKey();
                listNewChatLogVO = new NewChatLogVO<>();
                newChatLogVOLst.add(listNewChatLogVO);
                tempLst = new ArrayList<>();
                listNewChatLogVO.setSid(sidResult);
                listNewChatLogVO.setChatLogVOLst(tempLst);
                List<ChatLogDTO> chatLogs = entry.getValue();
                for (int i = 0; i < chatLogs.size(); i++) {
                    ChatLogDTO chatLog = chatLogs.get(i);
                    if (i == 0) {
                        sDate = chatLog.getTime();
                        flag = chatLog.getDirection();
                        listNewChatLogVO.setStartDate(sDate);
                        //---添加卖家昵称
                        listNewChatLogVO.setBuyerNick(chatLog.getBuyerNick());
                        listNewChatLogVO.setCsSimpleNick(userMap.get(chatLog.getCsNick()));
                        chatLogVOLst = new ArrayList<>();
                        tempLst.add(chatLogVOLst);
                    } else {
                        if (flag != chatLog.getDirection()) {
                            flag = chatLog.getDirection();
                            chatLogVOLst = new ArrayList<>();
                            tempLst.add(chatLogVOLst);
                        }
                    }

                    if (i == chatLogs.size() - 1) {
                        eDate = chatLog.getTime();
                        listNewChatLogVO.setEndDate(eDate);
                    }
                    msg = new ChatLogVO(shop.getShopId(),false);
                    msg.setBuyerNick(chatLog.getBuyerNick());
                    msg.setDirection(chatLog.getDirection());
                    msg.setCsNick(chatLog.getCsNick());
                    msg.setChatTime(chatLog.getTime());
                    msg.setContent(chatLog.getContent());
                    msg.setCsSimpleNick(userMap.get(chatLog.getCsNick()));
                   // msg.setGroupId(userQuery.getGroupId());
                    if (chatLog.getDirection() == 0) {
                        //客服说话，变换位置，前端只用一个buyerNick字段
                        msg.setBuyerNick(msg.getCsSimpleNick());
                    }
                    chatLogVOLst.add(msg);

                }
            }
        }
          //按时间升序排列
            if (CollectionUtils.isNotEmpty(newChatLogVOLst)) {
                newChatLogVOLst = newChatLogVOLst.stream().sorted(Comparator.comparing(NewChatLogVO::getStartDate)).collect(Collectors.toList());
            }
            return newChatLogVOLst;

	}

    @Override
    public Map<String, Object> searchChatlogLst(ShopCommonParam shop, Date startDate, Date endDate, String sid, UserQuery userQuery, String buyerNick, List<String> keyWordLst, Integer direction) {
        List<ChatLogDTO> chatlogLst = Lists.newArrayList();
        List<String> csNickLst = Lists.newArrayList();
        /*************计算慢响应需要用到的参数  start***************/
        //慢响应的毫秒值
        // /*慢响应时间*/
//        final Integer slowResponseTime = shopSystemsetting.getSlowResponseTime();
//        final long slowResponseTimeMillis = TimeUnit.SECONDS.toMillis(slowResponseTime);
////        /*慢响应次数  -slowResponseTime */
////        final Integer slowResponseTimesNum = shopSystemsetting.getSlowResponseTimesNum();
////        int slowCount = 0;
//        /*标记上一次是谁说话*/
//        String lastSay = null;
//        String csSayFlag = "CS_SAY";
//        String buyerSayFlag = "BUYER_SAY";
//        /*长接待设定*/
//        final long longReceptionTime = TimeUnit.MINUTES.toMillis(shopSystemsetting.getLongReceptionTime());
//
//        /*计算首次响应时间（买家第一次说话时间，如果接待时间差超过最长等待时间，
//        那此值，不会是'买家第一次说话的时间'，而是过滤掉超过最长等待时间的第一次接待的买家说话时间）*/
////        long buyerFirstSendTime = 0;
        /*************计算慢响应需要用到的参数  end***************/

        List<NewChatLogVO> newChatLogVOLst = Lists.newArrayList();
        csNickLst.add(userQuery.getNick());
        chatlogLst
                .addAll(chatAnalysisBusiness.searchBuyerChatlogs(shop, csNickLst, buyerNick, null, sid, startDate, endDate));
        if (CollectionUtils.isNotEmpty(chatlogLst)) {

            //将一次会话是一个人说的情况放入到list中
            ChatLogVO msg;
            Date sDate;
            Date eDate;
            List<List<ChatLogVO>> tempLst;
            List<ChatLogVO> chatLogVOLst = new ArrayList<>();
            //判断是否需要创建新的集合
            int flag = -1;
            NewChatLogVO<List<List<ChatLogVO>>> listNewChatLogVO;
//            /*买家发送聊天的时间*/
//            Date buyerSendLogTime = null;
            //根据sid分组
            Map<String, List<ChatLogDTO>> sidLst = chatlogLst.stream().collect(Collectors.groupingBy(ChatLogDTO::getSid));
            Set<Entry<String, List<ChatLogDTO>>> entries = sidLst.entrySet();
            for (Entry<String, List<ChatLogDTO>> entry : entries) {
                String sidResult = entry.getKey();
                listNewChatLogVO = new NewChatLogVO<>();
                newChatLogVOLst.add(listNewChatLogVO);
                tempLst = new ArrayList<>();
                listNewChatLogVO.setSid(sidResult);
                listNewChatLogVO.setChatLogVOLst(tempLst);
                List<ChatLogDTO> chatLogs = entry.getValue();
                for (int i = 0; i < chatLogs.size(); i++) {
                    ChatLogDTO chatLog = chatLogs.get(i);
//                    final long thisChatLogTime = chatLog.getTime().getTime();
//                    final boolean csAutoReplyBySysettingChat =
//                            this.isCsAutoReplyBySysettingChat(shopSystemsetting, chatLog.getContent());
                    if (i == 0) {
                        sDate = chatLog.getTime();
                        flag = chatLog.getDirection();
                        listNewChatLogVO.setStartDate(sDate);
                        //---添加卖家昵称
                        listNewChatLogVO.setBuyerNick(chatLog.getBuyerNick());
                        listNewChatLogVO.setCsSimpleNick(userQuery.getSimpleName());
                        chatLogVOLst = new ArrayList<>();
                        tempLst.add(chatLogVOLst);
                    } else {
                        if (flag != chatLog.getDirection()) {
                            flag = chatLog.getDirection();
                            chatLogVOLst = new ArrayList<>();
                            tempLst.add(chatLogVOLst);
                        }
                    }

                    if (i == chatLogs.size() - 1) {
                        eDate = chatLog.getTime();
                        listNewChatLogVO.setEndDate(eDate);
                    }
                    msg = new ChatLogVO(shop.getShopId(), false);
                    msg.setBuyerNick(chatLog.getBuyerNick());
                    msg.setDirection(chatLog.getDirection());
                    msg.setCsNick(chatLog.getCsNick());
                    msg.setChatTime(chatLog.getTime());
                    msg.setContent(chatLog.getContent());
                    msg.setCsSimpleNick(userQuery.getSimpleName());
                    msg.setGroupId(userQuery.getGroupId());
                    if (chatLog.getDirection() == 0) {
                        //客服说话，变换位置，前端只用一个buyerNick字段
                        msg.setBuyerNick(msg.getCsSimpleNick());
                    }
                    /****************************判断是否是慢响应的聊天    start***************************************/
                    //----------查看是否包含关键词
                    //客服违规，顾客违规分开
                    if (chatLogs.get(i).getDirection().equals(direction)) {
                        if (CollectionUtils.isNotEmpty(keyWordLst)) {
                            for (String keyword : keyWordLst) {
                                if (msg.getContent().contains(keyword)) {
                                    msg.setKeyword(keyword);
                                }
                            }
                        }
                    }
                    //标记这次是谁说话
//                    boolean csSay = csSay(chatLog);
//
//                    if (csSay) {
//                        /*过滤自动回复*/
//                        if (csAutoReplyBySysettingChat) {
//                            chatLogVOLst.add(msg);
//                            continue;
//                        }


                        /*上次如果是买家说话*/
//                        if (Objects.equals(lastSay, buyerSayFlag)) {
//                            /*最近对话回合的耗时*/
//                            final long recentlySessionTime = (thisChatLogTime - buyerSendLogTime.getTime());
//                            /*计算慢响应，如果响应时间大于设定时间，并且这种情况的次数大于设定次数，则为慢响应*/
//                            // 超过最长等待时间就不算慢响应  --job计算的时候有这个
//                            /*表示这次接待 超过了最长等待时间*/
////                            if (recentlySessionTime > slowResponseTimeMillis &&
////                                    ++slowCount >= slowResponseTimesNum) {
////                                msg.setSlowResponseFlag(true);
////                            }
//                            if (recentlySessionTime < longReceptionTime && isSlowResp && recentlySessionTime > slowResponseTimeMillis) {
//                                msg.setSlowResponseFlag(true);
//                            }
//                        }
//                        buyerSendLogTime = null;
//                    } else {
//                        /*如果买家发送聊天时间为空，那么设置属性值*/
//                        if (buyerSendLogTime == null) {
//                            buyerSendLogTime = chatLog.getTime();
//                        }
////                        if (buyerFirstSendTime == 0) {
////                            buyerFirstSendTime = thisChatLogTime;
////                        }
//                    }

                    /*标记此次说话是谁说的*/
//                    lastSay = csSay ? csSayFlag : buyerSayFlag;
                    /****************************判断是否是慢响应的聊天    end  ***************************************/
                    chatLogVOLst.add(msg);

                }
            }

        }

        //按时间升序排列
        if (CollectionUtils.isNotEmpty(newChatLogVOLst)) {
            newChatLogVOLst = newChatLogVOLst.stream().sorted(Comparator.comparing(NewChatLogVO::getStartDate)).collect(Collectors.toList());
        }
        Map<String, Object> result = new HashMap<>();
        result.put("newChatLogVOLst",newChatLogVOLst);
        result.put("keywordLst",keyWordLst);
        return result;
    }

    @Override
    public EnquiryLostVO searchEnquiryLostRecordLstOfSpu(LossOrderParam lossOrderParam, Date startDate, Date endDate, Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds) {
        return lostRecordAnalysisBusiness.searchEnquiryLostRecordLstOfSpu(lossOrderParam, startDate, endDate, chatLimitNum, sortPageQuery, sessionDuration, skuIds);
    }

    private boolean csSay(ChatLogDTO chatlogDTO){
        return chatlogDTO.getDirection() == 0;
    }
    private boolean buyerSay(ChatLogDTO chatlogDTO) {
        return chatlogDTO.getDirection() == 1;
    }

    @Override
   public DataAnalysisVO<ShopGoodsFeedbackRateDTO> searchShopGoodsFeedbackRateLst(ShopCommonParam shop, ShopGoodsRateParam param, SortPageQuery sortPageQuery) throws Exception{

        return shopGoodsFeedbackRateBussiness.searchShopGoodsFeedbackRateLst(shop, param,true, sortPageQuery);
    }
}
