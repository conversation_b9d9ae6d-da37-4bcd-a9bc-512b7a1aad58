package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.UserPortraitStatisticsDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Result.RegionDistributionResult;

import java.util.Date;

public interface UserPortraitStatisticsBusiness {
    UserPortraitStatisticsDTO getPortraitStatisticsByDate(ShopCommonParam shop, Date startDate, Date endDate);

    RegionDistributionResult getRegionDistributionByDate(ShopCommonParam shop, Date startDate, Date endDate);
}
