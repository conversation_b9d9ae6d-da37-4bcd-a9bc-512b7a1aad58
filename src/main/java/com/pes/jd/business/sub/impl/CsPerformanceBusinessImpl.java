package com.pes.jd.business.sub.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.*;
import com.pes.jd.constants.PerformanceConstans;
import com.pes.jd.dao.sub.CsPerformanceDao;
import com.pes.jd.dao.sub.CsTorderPerformanceDao;
import com.pes.jd.dao.sub.CsTypeDayDao;
import com.pes.jd.dao.sub.PesShopOvDayDao;
import com.pes.jd.framework.StopWatch;
import com.pes.jd.framework.ThreadLocalHelper;
import com.pes.jd.model.Annotation.Property;
import com.pes.jd.model.Annotation.Table;
import com.pes.jd.model.DO.CsTypeDayDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.PerformanceParam;
import com.pes.jd.model.VO.CsPerformanceVo;
import com.pes.jd.model.VO.CsPerformanceVoAvg;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.BeanUtils;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.mutable.MutableBoolean;
import org.apache.commons.lang.mutable.MutableDouble;
import org.apache.commons.lang.mutable.MutableInt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.*;
import java.util.stream.Collectors;

import static com.pes.jd.model.VO.CsPerformanceVo.*;
import static com.pes.jd.model.VO.CsPerformanceVoAvg.getFilledAvg;
import static java.util.Calendar.DAY_OF_MONTH;
import static java.util.Calendar.MONTH;

/**
 * @<NAME_EMAIL>
 * @date 2019/1/3 3:47 PM
 * @since 1.0.0
 */
@SuppressWarnings({"Duplicates", "unchecked"})
@Service
public class CsPerformanceBusinessImpl implements CsPerformanceBusiness {
    @Resource
    private CsPerformanceDao csPerformanceDao;
    @Resource
    private ReceiveSessionNumHourlyBusiness receiveSessionNumHourlyBusiness;
    @Resource
    private CsServiceEvaluationBusiness csServiceEvaluationBusiness;
    @Resource
    private CsTypeDayDao csTypeDayDao;
    @Resource
    private CsRefundDayBusiness csRefundDayBusiness;
    @Resource
    private CsChatSessionServiceIndexBusiness csChatSessionServiceIndexBusiness;
    @Resource
    private CsDutyRecordBusiness csDutyRecordBusiness;
    @Resource
    private CsOrderEvaluateBusiness csOrderEvaluateBusiness;
    @Resource
    private CsLossRecordBusiness csLossRecordBusiness;
    @Resource
    private CsTorderPerformanceBusiness csTorderPerformanceBusiness;
    @Resource
    private CsAssitIndexBusiness csAssitIndexBusiness;
    @Resource
    private CsToOrderIndexBusiness csToOrderIndexBusiness;
    @Resource
    private PesShopOvDayDao pesShopOvDayDao;
    @Resource
    private CsTorderPerformanceDao csTorderPerformanceDao;

    private final static Logger LOGGER = LoggerFactory.getLogger(CsPerformanceBusinessImpl.class);

    private final static ThreadLocal<ContextService<String, Object>> context =
            ThreadLocal.withInitial(ContextService::new);

    private final static ThreadLocal<ContextService<String, Object>> cache =
            ThreadLocal.withInitial(ContextService::new);

    // 售后客服允许出现的数据
    static final Set<String> allowValueField = new HashSet<>(
//            Arrays.asList(
//            "consultNum", "receiveNum", "consultSessionNum", "receiveSessionNum", "directReceiveSessionNum",
//            "forwardInSessionNum", "forwardOutSessionNum", "custConsultSessionNum", "csToCustSessionNum",
//            "chatNum", "custChatNum", "csChatNum", "answerRatio", "csWordNum", "avgReplyMsg", "maxReceiveSessionNum",
//            "nonReplySessionNum", "responseRate", "rapidAnswerRate", "leaveMsgSessionNum", "leaveMsgReceiveSessionNum",
//            "leaveMsgResponseRate", "avgRespTime", "avgRespTimeFirst", "avgSessionDurationTime", "slowRespSessionNum",
//            "longRespSessionNum", "workDay", "rceiveDurationTime", "avgFirstOnlineDateTime", "avgLastOfflineDateTime",
//            "firstOnlineDateTime", "lastOfflineDatetime", "loginTimesNum", "avgLoginTimesNum",
//            "loginDurationTime", "avgLoginDurationTime", "rceiveDurationTime", "avgRceiveDurationTime", "hangupDurationTime",
//            "avgHangupDurationTime", "rceiveTimeRate", "offlineDurationTime", "inviteNum", "evalReplyNum", "verySatisfiedNum",
//            "satisfiedNum", "generalNum", "dissatisfiedNum", "veryDissatisfiedNum", "evaluationRate", "satisfactionRate", "inviteRate"
//            ,"avgRespInQuickTime","sessionNum","evalSendNum")
    );
    // 值班记录需要展示的字段，当查询这些字段的时候需要判断是否开启过滤所有的未上班客服
    static final Set<String> dutyPropertySet = new HashSet<>(
            Arrays.asList(
                    "rceiveTimeRate", "avgLastOfflineDateTime", "avgRceiveDurationTime", "loginDurationTime", "avgLoginTimesNum",
                    "offlineDurationTime", "loginTimesNum", "avgLoginDurationTime", "rceiveDurationTime", "firstOnlineDateTime",
                    "avgHangupDurationTime", "hangupDurationTime", "avgFirstOnlineDateTime", "lastOfflineDateTime", "workDay"
            )
    );

    private static boolean debug = false;

    static {
        final Field[] declaredFields = CsPerformanceVo.class.getDeclaredFields();
        process(
                new HashSet<>(Arrays.asList(WORKLOAD, SATISFACTION, RECEIVE_HOUR, EVALUATE, DUTY_HISTORY)),
                declaredFields,
                Arrays.stream(declaredFields).collect(Collectors.toMap(Field::getName, v -> v)),
                true, false
        );
        allowValueField.add("consultNum");
        allowValueField.add("receiveNum");
        allowValueField.add("avgRespInQuickTime");
        allowValueField.add("sessionNum");

        allowValueField.add("notFinalData");
        allowValueField.add("chatRoundNum");
        allowValueField.add("respTimeFirstCount");
        allowValueField.add("respTimeCount");
        allowValueField.add("chatRoundNumNoLeave");
        allowValueField.add("sessionDurationTimeCount");
    }

    public static void main(String[] args) {
        debug = true;
    }

    private static void process(Set<String> tables, Field[] declaredFields, Map<String, Field> nField, boolean tableValid, boolean add) {
        for (Field field : declaredFields) {
            if (field.getAnnotationsByType(CsPerformanceVo.Done.class).length != 0) {
                return;
            }
            final Annotation[] declaredAnnotations = field.getDeclaredAnnotations();
            final String name = field.getName();
            for (Annotation declaredAnnotation : declaredAnnotations) {

                final Class<? extends Annotation> aClass = declaredAnnotation.annotationType();
                if (aClass == Table.class && tableValid) {
                    final String tableName = field.getDeclaredAnnotation(Table.class).value();
                    add = true;
                    if (!tables.contains(tableName)) {
                        add = false;
                        continue;
                    }
                    final List<String> fls = getFls(tableName);
                    if (debug)
                        System.out.println(" 表： " + tableName);
                    process(tables, fls.stream().map(nField::get).toArray(Field[]::new), nField, false, true);
                }
                if (aClass == Property.class && add) {
                    if (debug)
                        System.out.println(name);
                    allowValueField.add(name);
                }
            }
            if (name.startsWith("hour") && add) {
                if (debug)
                    System.out.println(name);
                allowValueField.add(name);
            }
        }
    }

    /**
     * 过滤售后客服不显示  销售数据
     */
    private final Chain<CsPerformanceVo, CsPerformanceVoAvg> chain = new Chain<>(
            Arrays.asList(
                    /*过滤售后客服字段信息*/
                    new Interceptor<CsPerformanceVo, CsPerformanceVoAvg>() {

                        // 没有copy赋值的默认值
                        String defaultVal = "123456780";

                        /*售后客服可以显示的字段*/
                        @Override
                        CsPerformanceVo interceptor(CsPerformanceVo o) {
                            BeanUtils.setDefalutValue(o, defaultVal, allowValueField);
                            return null;
                        }

                        @Override
                        CsPerformanceVoAvg processAvg(CsPerformanceVoAvg o) {
                            BeanUtils.setDefalutValue(o, defaultVal, allowValueField);
                            return null;
                        }
                    }
            )
    );

    @Override
    public Map<?, ?> searchClientServicePerformance(
            PerformanceParam requestParam, Date startDate, Date endDate, Long shopId, Integer flag
            , String schemaId) {
        ContextService<String, Object> contextThread = context.get();
        contextThread.put("startDate", startDate);
        contextThread.put("endDate", endDate);
        final StopWatch stopWatch = ThreadLocalHelper.STOP_WATCH.get();
        ContextService<String, Object> property = new ContextService<>();
        //全天锁定的客服
        List<String> noLockCsNick = csTypeDayDao.searchNoLockCsNickByShopDate(schemaId, shopId, startDate, endDate);
        initProperties(requestParam, startDate, endDate, shopId, schemaId, property, flag,noLockCsNick);
        LOGGER.debug("填充初始化数据 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        /*计算绩效数据*/
        Map<String, Map<Object, Object>> result = doComputer(property);
        LOGGER.debug("计算绩效结束 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        /*计算按月查询*/
        if (Objects.equals(property.getString("dimension"), "date") && flag == 0) {
            doComputerMonth(result, property);
            LOGGER.debug("计算按月查询结束 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        }
        clear();
        return result;
    }

    @Override
    public Map<String,Object> selectCsAndShopSaleAmount(String shopId, Date startDate, Date endDate,String schema) throws Exception {
        try {
            HashMap<String, Object> result = Maps.newHashMap();
            //查询店铺销售额汇总
            Double shopSaleAmountForMonth = pesShopOvDayDao.selectShopSaleAmount(Long.valueOf(shopId), startDate, endDate,schema);
            result.put("shopSaleAmountForMonth",shopSaleAmountForMonth);
            //查询客服销售额汇总
            Double csSaleAmountForMonth = csTorderPerformanceDao.selectCsSaleAmount(Long.valueOf(shopId), startDate, endDate, schema);
            result.put("csSaleAmountForMonth",csSaleAmountForMonth);
            return result;
        } catch (Exception e) {
            LOGGER.error("selectCsAndShopSaleAmount()异常：",e);
            throw new Exception(e);
        }
    }


    /**
     * 按月汇总
     *
     * @return
     */
    private void doComputerMonth(
            Map<String, Map<Object, Object>> result, ContextService<String, Object> property) {

        final Collection<Object> res = result.get("result").values();

        Map<Object, Object> container = new LinkedHashMap<>();
        final MultiValueMap<Date, Object> monthVal =
                com.pes.jd.util.MapUtils.extract(res, k -> DateUtils.truncate(((CsPerformanceVo) k).getDate(), Calendar.MONTH));

        CsPerformanceVo tmp = new CsPerformanceVo();
        for (Map.Entry<Date, List<Object>> dateListEntry : monthVal.entrySet()) {
            final List<Object> value = dateListEntry.getValue();
            CsPerformanceVo vo = getFilled();
            for (Object o : value) {
                if (o instanceof CsPerformanceVo) {
                    BeanUtils.countPropertyVal(o, vo, BeanUtils.COUNT_PROCESS_SPEC, null, null);
                    vo.setDate(((CsPerformanceVo) o).getDate());
                } else {
                    throw new RuntimeException("type is valid");
                }
            }
            processSpecForMonth(vo, value, property);
            container.put(dateListEntry.getKey(), vo);
            // 按月查询  有些字段需要用每月的数据相加然后除以查询的数量
            tmp.setAvgRespTimeFirst(tmp.getAvgRespTimeFirst() == null ? vo.getAvgRespTimeFirst() : vo.getAvgRespTimeFirst() + tmp.getAvgRespTimeFirst());
            tmp.setAvgRespTime(tmp.getAvgRespTime() == null ? vo.getAvgRespTime() : vo.getAvgRespTime() + tmp.getAvgRespTime());
        }

        result.put("result", container);
        processAvgMonth((CsPerformanceVoAvg) result.get("avg").get("avg"), tmp, container.size());

    }

    /**
     * 按月查询  有些字段需要用每月的数据相加然后除以查询的数量
     *
     * @param avg
     * @param tmp
     * @param size
     */
    private void processAvgMonth(CsPerformanceVoAvg avg, CsPerformanceVo tmp, int size) {
        avg.setAvgRespTimeFirst(tmp.getAvgRespTimeFirst() / size);
        avg.setAvgRespTime(tmp.getAvgRespTime() / size);
    }

    /**
     * 按月查询 有些值不可以汇总，需要采取均值
     * <p>
     * 此处的情况比较复杂，有些字段是可以直接采用汇总相加的算法，有些字段必须是平均值的算法，
     * 此方法用来聚合处理
     *
     * @param vo
     * @param value
     * @param property
     */
    private void processSpecForMonth(CsPerformanceVo vo, List<Object> value,
                                     ContextService<String, Object> property) {
        vo.setAvgRespTime(vo.getAvgRespTime() / value.size());
        vo.setAvgRespTimeFirst(vo.getAvgRespTimeFirst() / value.size());

        // 同数据不同字段的赋值
        setProperty(vo);
        // 团队数据
        withTeam(vo, property, Boolean.FALSE, vo::getSaleAmountOrigin);

        CsPerformanceVoAvg avg = new CsPerformanceVoAvg();
        // 处理均值汇总
        processAvgAndCountSpec(vo, avg, property, Boolean.FALSE, 0,0, null);

        //这里有些平均，需要给值到月份查询的每个对象
        BeanUtils.copyProperties(avg, vo);

        // 汇总和均值的延迟数据
        processAvgAndCountDelay(vo, avg, property);


    }

    /**
     * 初始化数据
     */
    private void initProperties(PerformanceParam requestParam, Date startDate, Date endDate,
                                Long shopId, String schemaId, ContextService<String, Object> property, Integer flag, List<String> noLockCsNick) {
        final boolean monthSearch = flag == 0;
        property.put("monthSearch", monthSearch);
        property.put("monthSum", DateUtils.compareMonth(startDate, endDate) + 1);
        property.put("requestParam", requestParam);
        final Set<String> saleBefore = getKeys(requestParam.getAfterSaleNicks());
        property.put("afterSaleNicks", saleBefore);
        final String sysSetting = requestParam.getSysSetting();
        final ShopSystemsettingDTO shopSystemsettingDTO = JSON.parseObject(sysSetting, ShopSystemsettingDTO.class);
        property.put("shopSystemsetting", shopSystemsettingDTO);
        final List<PerformanceParam.FilterTimeBean> filterTime = requestParam.getFilterTime();
        Set<Date> filterDates = new HashSet<>();
        if (CollectionUtils.isNotEmpty(filterTime)) {
            for (PerformanceParam.FilterTimeBean filterTimeBean : filterTime) {
                fillDates(filterDates, filterTimeBean.getStartDate(), filterTimeBean.getEndDate());
            }
        }
        property.put("filterDates", filterDates);
        property.put("startDate", startDate);
        property.put("endDate", DateUtils.getEndTimeOfDate(endDate));
        property.put("shopId", shopId);
        final Map<String, String> nicksMap = requestParam.getNicks();
        Set<String> nicks = getKeys(nicksMap);
        final Set<Object> nickSs = new HashSet<>();
        nickSs.addAll(nicks);
        nickSs.addAll(saleBefore);
        if(CollUtil.isNotEmpty(noLockCsNick)){//剔除计算锁定的客服
            nickSs.removeIf(ele -> !noLockCsNick.contains(ele.toString()));
        }
        property.put("nicks", nickSs);
        property.put("nickWithSimpleName", nicksMap);
        property.put("schemaId", schemaId);
        String dimension;
        // 数据类型
        if (requestParam.isNickQuery()) {
            dimension = "date";
        } else {
            dimension = "cs_nick";
        }
        property.put("dimension", dimension);
        context.get().put("dimension", dimension);
        // 全部查询
        property.isComputerGuestAvg(true);
        property.isComputerConversion(true);
        property.isWorkablePay(true);
        property.isSatisfactionProportion(true);
        property.isWorkMeasure(true);
        property.isDutyHistory(true);
        property.isHelpingService(true);
        property.islossRecord(true);
        property.isMiddleBadappraised(true);
        property.isReceiveSessionNumHourly(true);
        property.isRefundDay(true);
    }

    private <K, V> Set<K> getKeys(Map<K, V> map) {
        if (map == null) {
            return new HashSet<>();
        }
        return map.keySet();
    }

    private void fillDates(Set<Date> filterDates, Date startDate, Date endDate) {
        for (Date i = DateUtils.truncate(startDate, Calendar.DAY_OF_MONTH);
             i.getTime() <= DateUtils.truncate(endDate, Calendar.DAY_OF_MONTH).getTime(); i = DateUtils.addDays(i, 1)) {
            filterDates.add(i);
        }
    }


    /**
     * 销售数据  这个里面调用的方法是有顺序的，勿乱动
     */
    @SuppressWarnings("AlibabaMethodTooLong")
    private Map<String, Map<Object, Object>> doComputer(ContextService<String, Object> properties) {
        final StopWatch stopWatch = ThreadLocalHelper.STOP_WATCH.get();
        // 绩效数据
        Date endDate = properties.getDate("endDate");
        Date startDate = properties.getDate("startDate");
        // copy bean properties ignore field
        properties.put("ignore", new String[]{
                "shopId", "date", "csNick"
        });
        // 系统设置
        final ShopSystemsettingDTO shopSystemsettingDTO = (ShopSystemsettingDTO) properties.get("shopSystemsetting");
        /*字段过滤，销售额、是否扣除退款、是否扣除邮费*/
        final PerformanceParam requestParam = properties.getVal("requestParam");
        final List<PerformanceParam.Filter> requestPropertiesFilter = requestParam.getFilter();
        final String dimension = properties.getString("dimension");
        final Long shopId = properties.getLong("shopId");
        // 填充团队数据
        csTeam(properties);
        LOGGER.debug("填充团队数据结束 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        // 获取vo，如果map中没有，会进行初始化
        Map<String, Map<Object, Object>> performanceValue = properties.getPerformanceValue();
        LOGGER.debug("初始化结果集结束 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        /*  key  一般为 date  or   nick   */
        Map<Object, Object> result = performanceValue.get("result");
        // 汇总数据
        CsPerformanceVo count = (CsPerformanceVo) performanceValue.get("count").get("count");
        // 平均数据
        CsPerformanceVoAvg avg = (CsPerformanceVoAvg) performanceValue.get("avg").get("avg");
        /* 绩效导出时间设置 */
        Map setting = new HashMap();
        setting.put("dutyRecordExportUnit", shopSystemsettingDTO.getDutyRecordExportUnit());
        performanceValue.put("setting", setting);

        final boolean cs = properties.isCs();
        final boolean date = !cs;

        final String schemaId = properties.getString("schemaId");
        // 查询客服每日状态-售前的状态
        final List<CsTypeDayDO> csTypeDays = csTypeDayDao.searchByShopDate(schemaId, shopId, startDate, endDate);
        Map<String, Map<Date, CsTypeDayDO>> csTypeMap =
                com.pes.jd.util.MapUtils.extract(csTypeDays, CsTypeDayDO::getCsNick, CsTypeDayDO::getDate);

        Date start = startDate;
        Date end = endDate;
        // 统计售前客服，在售后客服不允许出现的字段，总共有多少有效字段
        Map<String, Integer> propertyCount = new HashMap<>(allowValueField.size() * 2);
        // 将查询的绩效数据copy至vo对象
        for (Object performance : result.values()) {
            CsPerformanceVo target = (CsPerformanceVo) performance;
            // 时间段内是否含有售前客服
            MutableBoolean hasSaleBefore = new MutableBoolean(Boolean.FALSE);
            if (date) {
                end = start = target.getDate();
            }
            hasSaleBefore.setValue(getHasSaleBefore(target.getCsNick(), start, end, csTypeMap));

            // 获取数据的key
            EntryService entry = EntryService.of(shopId, target.getCsNick(), target.getDate());
            properties.put("entry", entry);
            // 询单维度绩效数据
            performanceBefore(properties);
            performance(target, properties);
            // 下单维度绩效数据
            toOrderPerformanceBefore(properties);
            toOrderPerformance(target, properties);

            // 客单价
            if (properties.computerGuestAvg()) {
                guestAvg(target);
            }

            // 转化率
            if (properties.computerConversion()) {
                computerConversion(target);
            }

            // 落实付款数据
            if (properties.workablePay()) {
                workablePayBefore(properties);
                workablePay(target, properties);
            }

            // 计算工作量    *
            if (properties.workMeasure()) {
                workMeasureBefore(properties);
                workMeasure(target, properties);
            }
            // 值班记录
            if (properties.dutyHistory()) {
                dutyHistoryBefore(properties);
                dutyHistory(target, properties);
            }

            // 协助服务
            if (properties.helpingService()) {
                helpingServiceBefore(properties);
                helpingService(target, properties);
            }

            // 满意率
            if (properties.satisfactionProportion()) {
                satisfactionProportionBefore(properties);
                satisfactionProportion(target, properties);
            }

            // 中差评
            if (properties.middleBadappraised()) {
                middleBadappraisedBefore(properties);
                middleBadappraised(target, properties);
            }
            // 流失数据
            if (properties.lossRecord()) {
                lossRecordBefore(properties);
                lossRecord(target, properties);
            }
            // 分时接待
            if (properties.receiveSessionNumHourly()) {
                receiveSessionNumHourlyBefore(properties);
                receiveSessionNumHourly(target, properties);
            }
            // 退款数据
            if (properties.refundDay()) {
                refundDayBefore(properties);
                refundDay(target, properties);
            }


            // 同数据不同字段的赋值
            setProperty(target);

            // 团队数据  这需要在下面方法之前
            withTeam(target, properties, Boolean.TRUE, null);
            // 字段计算过滤
            specialProcess(requestPropertiesFilter, target, dimension, startDate, endDate);

            // 延迟数据
            delay(target, endDate, properties);
            // 售后客服不显示销售数据
            chain.process(target, (x) -> {
                // 不含售前客服才过滤
                if (!hasSaleBefore.booleanValue()) {
                    return true;
                }
                return false;
            });
            // 汇总计算和的数据
            LOGGER.info("------target.getsale:"+target.getSaleAmount());
            LOGGER.info("============count:"+count.getSaleAmount());
            BeanUtils.<CsPerformanceVo>countPropertyVal(propertyCount, target, count, (vo) -> {
                // 客服维度不排除
                if (!properties.isCs()) {
                    return true;
                }
                return properties.containers(vo.getCsNick());
            });
            LOGGER.info("=====================count:"+count.getSaleAmount());

            if (Objects.equals("date", dimension)) {
                LOGGER.debug("计算日期{}结束 查询开始至现在耗时：{}毫秒", target.getDate(), stopWatch.watch());
            } else {
                LOGGER.debug("计算客服{}结束 查询开始至现在耗时：{}毫秒", target.getCsNick(), stopWatch.watch());
            }
        }
        // 剔除锁定(没有数据的)客服
        exclude(performanceValue, properties);
        // @todo 如果开启剔除上班天数为0的客服 则剔除
        if (shopSystemsettingDTO.getDutyRidCsSwitch())
            excludeNotOnDuty(performanceValue, properties, requestParam);
        LOGGER.debug("处理特殊综合结束 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        // 剔除后 result 会变
        result = performanceValue.get("result");
        // 处理均值汇总

        //总量要减去售后客服的数量
        int sizes = result.size();
        Collection<Object> values1 = result.values();
        if (CollectionUtils.isNotEmpty(values1) && properties.isCs()) {
            List<Object> csPerformance = Arrays.asList(values1.toArray());
            for (int i = 0; i < csPerformance.size(); i++) {
                CsPerformanceVo o = (CsPerformanceVo) csPerformance.get(i);
                if (o.getEnquiryNum() == 123456780) {
                    sizes--;
                }
            }
        }


        processAvgAndCountSpec(count, avg, properties, Boolean.TRUE,result.size(), sizes, propertyCount);
        LOGGER.debug("处理均值汇总 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        // 汇总和均值的延迟数据
        processAvgAndCountDelay(count, avg, properties);
        LOGGER.debug("汇总和均值的延迟数据 查询开始至现在耗时：{}毫秒", stopWatch.watch());
        // 售前排序
        final Collection<Object> values = result.values();
        if (CollectionUtils.isNotEmpty(values) && properties.isCs()) {
            final List<Object> csPer = Arrays.asList(values.toArray());

            //排序前让售后排在后面
            for (int i = 0; i < csPer.size(); i++) {
                CsPerformanceVo o = (CsPerformanceVo)csPer.get(i);
                if (o.getEnquiryNum() == 123456780) {
                    o.setEnquiryNum(-100);
                }
            }



            csPer.sort((x, y) -> {
                final CsPerformanceVo x1 = (CsPerformanceVo) x;
                final CsPerformanceVo x2 = (CsPerformanceVo) y;
                if (x1.getEnquiryNum() > x2.getEnquiryNum()) {
                    return -1;
                }
                if (x1.getEnquiryNum() == x2.getEnquiryNum()) {
                    return 0;
                }
                return 1;
            });


            //排序结束后 重新赋值给前端区分售后
            for (int i = 0; i < csPer.size(); i++) {
                CsPerformanceVo o = (CsPerformanceVo)csPer.get(i);
                if (o.getEnquiryNum() == -100) {
                    o.setEnquiryNum(123456780);
                }
            }



            performanceValue.put("result", com.pes.jd.util.MapUtils.extractForList(csPer, k -> {
                final CsPerformanceVo k1 = (CsPerformanceVo) k;
                return k1.getCsNick();
            }));
        }
        return performanceValue;
    }

    /**
     * 排除锁定的客服(查不到数据的客服)
     */
    private void exclude(Map<String, Map<Object, Object>> performanceValue,
                         ContextService<String, Object> properties) {
        if (!properties.isCs()) return;
        Map<Object, Object> result = performanceValue.get("result");
        Map<Object, Object> switchData = new HashMap<>();
        for (Map.Entry<Object, Object> objectObjectEntry : result.entrySet()) {
            final Object key = objectObjectEntry.getKey();
            if (properties.containers((String) key)) {
                switchData.put(key, objectObjectEntry.getValue());
            }
        }
        performanceValue.put("result", switchData);
    }

    /**
     * 排除排除未上班的客服
     */
    private void excludeNotOnDuty(Map<String, Map<Object, Object>> performanceValue,
                                  ContextService<String, Object> properties,
                                  PerformanceParam requeryProperty) {
        if (!properties.isCs()) return;
        //全部是值班记录的字段才剔除，有一个不是就不剔除
        boolean isExclude = true;

        for (int i = 0; i < requeryProperty.getProperty().size(); i++) {
            if (!dutyPropertySet.contains(requeryProperty.getProperty().get(i))) {
                isExclude = false;
                break;
            }
        }

        //对未上班的客服进行排除
        if (isExclude) {
            Map<Object, Object> result = performanceValue.get("result");
            Map<Object, Object> switchData = new HashMap<>();
            for (Map.Entry<Object, Object> objectObjectEntry : result.entrySet()) {
                final Object key = objectObjectEntry.getKey();
                CsPerformanceVo value = (CsPerformanceVo) objectObjectEntry.getValue();
                if (properties.containers((String) key) && value.getLoginTimesNum() != 0) {//客服未登陆则过滤
                    switchData.put(key, objectObjectEntry.getValue());
                }
            }
            performanceValue.put("result", switchData);
        }

    }

    /**
     * 时间段内 这个人是否是售前客服
     *
     * @param csNick
     * @param startDate
     * @param endDate
     * @param csTypeMap
     * @return
     */
    private boolean getHasSaleBefore(String csNick, Date startDate, Date endDate, Map<String, Map<Date, CsTypeDayDO>> csTypeMap) {
        final Map<Date, CsTypeDayDO> dateCsTypeDayDOMap = csTypeMap.get(csNick);
        if (!org.springframework.util.CollectionUtils.isEmpty(dateCsTypeDayDOMap)) {
            // 如果都是售后就返回false
            final List<Date> dates = DateUtils.splitDate(startDate, endDate);
            // 时间内每天都是售后，就是售后
            for (Date date : dates) {
                final CsTypeDayDO csTypeDayDO = dateCsTypeDayDOMap.get(date);
                if (csTypeDayDO == null || csTypeDayDO.getCsType() == (byte) 1) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    /**
     * 同数据不同字段的设置 冗余字段的处理
     */
    private void setProperty(CsPerformanceVo target) {
        target.setRefundProductNum(target.getCompletedRefundProductNum());
        target.setTransactionsNum(target.getSaleGoodsNum());
        target.setAllDay(target.getReceiveSessionNum());
    }

    /**
     * 延迟数据
     */
    private void delay(
            CsPerformanceVo vo,
            Date endDate, ContextService<String, Object> properties) {
        final Date now = new Date();
        Date compared = properties.isCs() ? endDate : vo.getDate();
        final ShopSystemsettingDTO shopSystemsettingDTO = (ShopSystemsettingDTO) properties.get("shopSystemsetting");
        // 动态获取询单有效时长
        long enquiryValidDurationTime = shopSystemsettingDTO.getEnquiryValidDurationTime();
        // 动态获取出库有效时长
        long outStockValidDurationTime = shopSystemsettingDTO.getOutStockValidDurationTime();

        //延时初始值
        vo.setNotFinalData(new ArrayList<>());
        // 延迟一天
        final boolean delayOneDay = DateUtils.afterBalanceTime(compared, now, 2, TimeUnit.DAYS);
        if (delayOneDay) {
            properties.put("delayOneDay", true);
//            vo.setOrderedToPaidFinal(placeHolderDouble);
//            vo.setToOrderedPaidNumFinal(placeHolderInt);
//            vo.setToOrderedPaidGoodsFinal(placeHolderInt);
//            vo.setToOrderedPaidAmountFinal(placeHolderDouble);
//            vo.setPaidNumTodayNext(placeHolderInt);
//            vo.setPaidLossNum(placeHolderInt);
//            vo.setPaidLossGoodsNum(placeHolderInt);
//            vo.setPaidLossMoney(placeHolderDouble);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("orderedToPaidFinal", "toOrderedPaidNumFinal", "toOrderedPaidGoodsFinal", "toOrderedPaidAmountFinal", "paidNumTodayNext", "paidLossNum", "paidLossGoodsNum", "paidLossMoney");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟询单有效期
        final boolean delayQuery = DateUtils.afterBalanceTime(compared, now, enquiryValidDurationTime + 1, TimeUnit.DAYS);
        if (delayQuery) {
            properties.put("delayQuery", true);
//            vo.setQueryToFinalPaid(placeHolderDouble);
//            vo.setPaidNumFinal(placeHolderInt);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("queryToFinalPaid", "paidNumFinal");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟询单有效期-1
        final boolean delayQueryDelayOne = DateUtils.afterBalanceTime(compared, now, enquiryValidDurationTime, TimeUnit.DAYS);
        if (delayQueryDelayOne) {
            properties.put("delayQueryDelayOne", true);
//            vo.setQueryToTomorrow(placeHolderDouble);
//            vo.setQueryToOrderedToday(placeHolderDouble);
//            vo.setQueryToFinalOrdered(placeHolderDouble);
//            vo.setOrderedNumFinal(placeHolderInt);
//            vo.setOrderedAmountFinal(placeHolderDouble);
//            vo.setEnquiryLossNum(placeHolderInt);
//            vo.setEnquiryNum(placeHolderInt);
//            vo.setAssitOrderCreateNum(placeHolderInt);
//            vo.setAssitOrderCreateAmount(placeHolderDouble);
//            vo.setAssitOrderPayNum(placeHolderInt);
//            vo.setAssitOrderPayAmount(placeHolderDouble);
//            vo.setAssitOrderFollowupNum(placeHolderInt);
//            vo.setAssitOrderFollowupAmount(placeHolderDouble);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("queryToTomorrow",
                    "queryToOrderedToday",
                    "queryToFinalOrdered",
                    "orderedNumFinal",
                    "orderedAmountFinal",
                    "enquiryLossNum",
                    "enquiryNum",
                    "assitOrderCreateNum",
                    "assitOrderCreateAmount",
                    "assitOrderPayNum",
                    "assitOrderPayAmount",
                    "assitOrderFollowupNum",
                    "assitOrderFollowupAmount");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟询单有效期+出库有效期-2
        final boolean delayQueryOutStack = DateUtils.afterBalanceTime(compared, now, enquiryValidDurationTime + outStockValidDurationTime - 1, TimeUnit.DAYS);
        if (delayQueryOutStack) {
            properties.put("delayQueryOutStack", true);
//            vo.setQueryToOutStock(placeHolderDouble);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("queryToOutStock");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟出库有效时长
        final boolean delayOutStack = DateUtils.afterBalanceTime(compared, now, outStockValidDurationTime + 1, TimeUnit.DAYS);
        if (delayOutStack) {
            properties.put("delayOutStack", true);
//            vo.setOutStockOrderBuyerNumFinal(placeHolderInt);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("outStockOrderBuyerNumFinal");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟出库有效时长-1
        final boolean delayOutStackOne = DateUtils.afterBalanceTime(compared, now, outStockValidDurationTime - 1 + 1, TimeUnit.DAYS);
        if (delayOutStackOne) {
            properties.put("delayOutStackOne", true);
//            vo.setToOrderedOutStockNum(placeHolderInt);
//            vo.setToOrderedOutStockGoodsNum(placeHolderInt);
//            vo.setToOrderedOutStockAmount(placeHolderDouble);
//            vo.setToOrderedOutStockOrderNum(placeHolderInt);
//
//            vo.setOutStackLossNum(placeHolderInt);
//            vo.setOutStackLossGoodsNum(placeHolderInt);
//            vo.setOutStackLossMoney(placeHolderDouble);
//            vo.setOutStackLossOrderNum(placeHolderInt);


            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("toOrderedOutStockNum",
                    "toOrderedOutStockGoodsNum",
                    "toOrderedOutStockAmount",
                    "toOrderedOutStockOrderNum",
                    "outStackLossNum",
                    "outStackLossGoodsNum",
                    "outStackLossMoney",
                    "outStackLossOrderNum");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟1天数据->根据当前时间
        final boolean delayTwoDay = DateUtils.afterBalanceTime(compared, now, 2, TimeUnit.DAYS);
        if (delayTwoDay) {
            properties.put("delayTwoDay", true);
//            vo.setLeaveMsgSessionNum(placeHolderInt);
//            vo.setLeaveMsgReceiveSessionNum(placeHolderInt);
//            vo.setLeaveMsgResponseRate(placeHolderDouble);

            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("leaveMsgSessionNum",
                    "leaveMsgReceiveSessionNum",
                    "leaveMsgResponseRate");
            vo.getNotFinalData().addAll(notFinalFieldLst);
        }
        //需要查询的字段才做是否展示最终数据返回
        doCheckFinalField(properties, vo);

    }

    private <T> void doCheckFinalField(ContextService<String, Object> properties, T t) {
        if (t instanceof CsPerformanceVo) {
            CsPerformanceVo vo = (CsPerformanceVo) t;
            List<String> notFinalData = vo.getNotFinalData();
            if (CollectionUtils.isEmpty(notFinalData)) {
                return;
            }
            PerformanceParam requestParam = (PerformanceParam) properties.get("requestParam");
            //需要查询的参数
            List<String> property = requestParam.getProperty();
            List<String> finalNotFinalData = new ArrayList<>();
            property.forEach(ele -> notFinalData.forEach(nEle -> {
                if (ele.equals(nEle)) {
                    finalNotFinalData.add(ele);
                }
            }));
            vo.setNotFinalData(finalNotFinalData);

        } else if (t instanceof CsPerformanceVoAvg) {
            CsPerformanceVoAvg vo = (CsPerformanceVoAvg) t;
            List<String> notFinalData = vo.getNotFinalData();
            if (CollectionUtils.isEmpty(notFinalData)) {
                return;
            }
            PerformanceParam requestParam = (PerformanceParam) properties.get("requestParam");
            //需要查询的参数
            List<String> property = requestParam.getProperty();
            List<String> finalNotFinalData = new ArrayList<>();
            property.forEach(ele -> notFinalData.forEach(nEle -> {
                if (ele.equals(nEle)) {
                    finalNotFinalData.add(ele);
                }
            }));
            vo.setNotFinalData(finalNotFinalData);
        }
    }

    private <T> List<T> getNotFinalField(T... notFinalField) {
        return new ArrayList<>(Arrays.asList(notFinalField));
    }

    private void processAvgAndCountDelay(CsPerformanceVo count,
                                         CsPerformanceVoAvg avg,
                                         ContextService<String, Object> properties) {

        //延时初始值
        count.setNotFinalData(new ArrayList<>());
        avg.setNotFinalData(new ArrayList<>());
        // 延迟一天
        final boolean delayOneDay = properties.getBoolean("delayOneDay");
        if (delayOneDay) {
//            count.setOrderedToPaidFinal(placeHolderDouble);
//            count.setToOrderedPaidNumFinal(placeHolderInt);
//            count.setToOrderedPaidGoodsFinal(placeHolderInt);
//            count.setToOrderedPaidAmountFinal(placeHolderDouble);
//            count.setPaidNumTodayNext(placeHolderInt);
//            count.setPaidLossNum(placeHolderInt);
//            count.setPaidLossGoodsNum(placeHolderInt);
//            count.setPaidLossMoney(placeHolderDouble);

//            avg.setOrderedToPaidFinal(placeHolderDouble);
//            avg.setToOrderedPaidNumFinal(placeHolderDouble);
//            avg.setToOrderedPaidGoodsFinal(placeHolderDouble);
//            avg.setToOrderedPaidAmountFinal(placeHolderDouble);
//            avg.setPaidNumTodayNext(placeHolderDouble);
//            avg.setPaidLossNum(placeHolderDouble);
//            avg.setPaidLossGoodsNum(placeHolderDouble);
//            avg.setPaidLossMoney(placeHolderDouble);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("orderedToPaidFinal", "toOrderedPaidNumFinal", "toOrderedPaidGoodsFinal", "toOrderedPaidAmountFinal", "paidNumTodayNext", "paidLossNum", "paidLossGoodsNum", "paidLossMoney");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟询单有效期
        final boolean delayQuery = properties.getBoolean("delayQuery");
        if (delayQuery) {
//            count.setQueryToFinalPaid(placeHolderDouble);
//            count.setPaidNumFinal(placeHolderInt);
//
//            avg.setQueryToFinalPaid(placeHolderDouble);
//            avg.setPaidNumFinal(placeHolderDouble);

            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("queryToFinalPaid", "paidNumFinal");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟询单有效期-1
        final boolean delayQueryDelayOne = properties.getBoolean("delayQueryDelayOne");
        if (delayQueryDelayOne) {
//            count.setQueryToTomorrow(placeHolderDouble);
//            count.setQueryToOrderedToday(placeHolderDouble);
//            count.setQueryToFinalOrdered(placeHolderDouble);
//            count.setOrderedNumFinal(placeHolderInt);
//            count.setOrderedAmountFinal(placeHolderDouble);
//            count.setEnquiryLossNum(placeHolderInt);
//            count.setEnquiryNum(placeHolderInt);
//            count.setAssitOrderCreateNum(placeHolderInt);
//            count.setAssitOrderCreateAmount(placeHolderDouble);
//            count.setAssitOrderPayNum(placeHolderInt);
//            count.setAssitOrderPayAmount(placeHolderDouble);
//            count.setAssitOrderFollowupAmount(placeHolderDouble);
//            count.setAssitOrderFollowupNum(placeHolderInt);
//
//            avg.setQueryToTomorrow(placeHolderDouble);
//            avg.setQueryToOrderedToday(placeHolderDouble);
//            avg.setQueryToFinalOrdered(placeHolderDouble);
//            avg.setOrderedNumFinal(placeHolderDouble);
//            avg.setOrderedAmountFinal(placeHolderDouble);
//            avg.setEnquiryLossNum(placeHolderDouble);
//            avg.setEnquiryNum(placeHolderDouble);
//            avg.setAssitOrderCreateNum(placeHolderDouble);
//            avg.setAssitOrderCreateAmount(placeHolderDouble);
//            avg.setAssitOrderPayNum(placeHolderDouble);
//            avg.setAssitOrderPayAmount(placeHolderDouble);
//            avg.setAssitOrderFollowupNum(placeHolderDouble);
//            avg.setAssitOrderFollowupAmount(placeHolderDouble);

            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("queryToTomorrow",
                    "queryToOrderedToday",
                    "queryToFinalOrdered",
                    "orderedNumFinal",
                    "orderedAmountFinal",
                    "enquiryLossNum",
                    "enquiryNum",
                    "assitOrderCreateNum",
                    "assitOrderCreateAmount",
                    "assitOrderPayNum",
                    "assitOrderPayAmount",
                    "assitOrderFollowupNum",
                    "assitOrderFollowupAmount");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟询单有效期+出库有效期-2
        final boolean delayQueryOutStack = properties.getBoolean("delayQueryOutStack");
        if (delayQueryOutStack) {
//            count.setQueryToOutStock(placeHolderDouble);
//
//            avg.setQueryToOutStock(placeHolderDouble);
            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("queryToOutStock");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟出库有效时长
        final boolean delayOutStack = properties.getBoolean("delayOutStack");
        if (delayOutStack) {
//            count.setOutStockOrderBuyerNumFinal(placeHolderInt);
//
//            avg.setOutStockOrderBuyerNumFinal(placeHolderDouble);

            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("outStockOrderBuyerNumFinal");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);

        }
        // 延迟出库有效时长 -1
        final boolean delayOutStackOne = properties.getBoolean("delayOutStackOne");
        if (delayOutStackOne) {

//            count.setOutStackLossNum(placeHolderInt);
//            count.setOutStackLossGoodsNum(placeHolderInt);
//            count.setOutStackLossMoney(placeHolderDouble);
//            count.setOutStackLossOrderNum(placeHolderInt);
//
//            count.setToOrderedOutStockNum(placeHolderInt);
//            count.setToOrderedOutStockGoodsNum(placeHolderInt);
//            count.setToOrderedOutStockAmount(placeHolderDouble);
//            count.setToOrderedOutStockOrderNum(placeHolderInt);
//
//
//            avg.setOutStackLossNum(placeHolderDouble);
//            avg.setOutStackLossGoodsNum(placeHolderDouble);
//            avg.setOutStackLossMoney(placeHolderDouble);
//            avg.setOutStackLossOrderNum(placeHolderDouble);
//
//            avg.setToOrderedOutStockNum(placeHolderDouble);
//            avg.setToOrderedOutStockGoodsNum(placeHolderDouble);
//            avg.setToOrderedOutStockAmount(placeHolderDouble);
//            avg.setToOrderedOutStockOrderNum(placeHolderDouble);

            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("toOrderedOutStockNum",
                    "toOrderedOutStockGoodsNum",
                    "toOrderedOutStockAmount",
                    "toOrderedOutStockOrderNum",
                    "outStackLossNum",
                    "outStackLossGoodsNum",
                    "outStackLossMoney",
                    "outStackLossOrderNum");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);
        }
        // 延迟1天数据->根据当前时间
        final boolean delayTwoDay = properties.getBoolean("delayTwoDay");
        if (delayTwoDay) {
//            count.setLeaveMsgSessionNum(placeHolderInt);
//            count.setLeaveMsgReceiveSessionNum(placeHolderInt);
//            count.setLeaveMsgResponseRate(placeHolderDouble);
//
//            avg.setLeaveMsgSessionNum(placeHolderDouble);
//            avg.setLeaveMsgReceiveSessionNum(placeHolderDouble);
//            avg.setLeaveMsgResponseRate(placeHolderDouble);

            //---临时变量 需要显示非最终数据的字段
            List<String> notFinalFieldLst = getNotFinalField("leaveMsgSessionNum",
                    "leaveMsgReceiveSessionNum",
                    "leaveMsgResponseRate");
            count.getNotFinalData().addAll(notFinalFieldLst);
            avg.getNotFinalData().addAll(notFinalFieldLst);
        }

        doCheckFinalField(properties, avg);
        doCheckFinalField(properties, count);
    }

    /**
     * 扣除退款 扣除邮费
     */
    private void specialProcess(List<PerformanceParam.Filter> requestPropertiesFilter,
                                CsPerformanceVo target, String dimension, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(requestPropertiesFilter)) {
            return;
        }
        for (PerformanceParam.Filter filter : requestPropertiesFilter) {
            processField(filter.getProperty(), target, dimension, startDate, endDate);
        }
    }

    private void processField(String property, CsPerformanceVo target, String dimension, Date startDate, Date endDate) {
        if (Objects.equals("date", dimension)) {
            if ("refund_amount".equals(property)) {
                /*扣除退款*/
                minusDouble(target, CsPerformanceVo::getSaleAmount, CsPerformanceVo::getCompletedRefundAmount, CsPerformanceVo::setSaleAmount);
            } else if ("post_fee".equals(property)) {
                /*扣除邮费*/
                minusDouble(target, CsPerformanceVo::getSaleAmount, CsPerformanceVo::getPostFee, CsPerformanceVo::setSaleAmount);
            } else if ("refund_product_num".equals(property)) {
                if(null == target.getCompletedRefundProductNum()){
                    target.setCompletedRefundProductNum(0);
                }
                /*扣除退款件数*/
                minusInt(target, CsPerformanceVo::getSaleGoodsNum, CsPerformanceVo::getCompletedRefundProductNum, CsPerformanceVo::setSaleGoodsNum);
            }
        } else {
            Map<String, Map<Date, CsTorderPerformanceDTO>> csDay = (Map<String, Map<Date, CsTorderPerformanceDTO>>) (context.get().get("csDay"));
            Map<String, Map<Date, CsRefundDayDTO>> csRefundDay = (Map<String, Map<Date, CsRefundDayDTO>>) context.get().get("csRefundDay");
            MutableDouble saleAmount = new MutableDouble();
            MutableInt saleGoodsNum = new MutableInt();
            // 如果查询的是全部客服，需要每天扣除之后相加
            String csNick = target.getCsNick();
            Map<Date, CsTorderPerformanceDTO> dateCsTorderPerformanceDTOMap = csDay.get(csNick);
            Map<Date, CsRefundDayDTO> dateCsRefundDayDTOMap = csRefundDay.get(csNick);
            if (property.equals("refund_amount")) {
                if (MapUtils.isNotEmpty(dateCsTorderPerformanceDTOMap) && MapUtils.isNotEmpty(dateCsRefundDayDTOMap)) {
                    for (Map.Entry<Date, CsTorderPerformanceDTO> dateCsTorderPerformanceDTOEntry :
                            dateCsTorderPerformanceDTOMap.entrySet()) {
                        Date key = dateCsTorderPerformanceDTOEntry.getKey();
                        CsTorderPerformanceDTO value = dateCsTorderPerformanceDTOEntry.getValue();
                        CsRefundDayDTO csRefundDayDTO = dateCsRefundDayDTOMap.get(key);
                        Double saleAmount1 = getZeroNull(value.getSaleAmount());
                        if (csRefundDayDTO != null) {
                            saleAmount.add((saleAmount1 = saleAmount1 - getZeroNull(csRefundDayDTO.getCompletedRefundAmount())));
                        } else {
                            saleAmount.add(saleAmount1);
                        }
                        // set 回去
                        value.setSaleAmount(saleAmount1);
                    }
                    target.setSaleAmount(saleAmount.doubleValue());
                }
            } else if (property.equals("post_fee")) {
                if (MapUtils.isNotEmpty(dateCsTorderPerformanceDTOMap)) {
                    for (Map.Entry<Date, CsTorderPerformanceDTO> dateCsTorderPerformanceDTOEntry :
                            dateCsTorderPerformanceDTOMap.entrySet()) {
                        CsTorderPerformanceDTO value = dateCsTorderPerformanceDTOEntry.getValue();
                        Double saleAmount1 = getZeroNull(target.getSaleAmount()) - getZeroNull(value.getPostFee());
                        // Double saleAmount1 = getZeroNull(value.getSaleAmount()) - getZeroNull(value.getPostFee());
                        target.setSaleAmount(saleAmount1);
                        // saleAmount.add(saleAmount1);
                        // set 回去
                        // value.setSaleAmount(saleAmount1 < 0 ? 0 : saleAmount1);
                    }
                    // target.setSaleAmount(saleAmount.doubleValue() < 0 ? 0 : saleAmount.doubleValue());
                }

            } else if (property.equals("refund_product_num")) {
                if (MapUtils.isNotEmpty(dateCsTorderPerformanceDTOMap)) {
                    for (Map.Entry<Date, CsTorderPerformanceDTO> dateCsTorderPerformanceDTOEntry :
                            dateCsTorderPerformanceDTOMap.entrySet()) {
                        Date key = dateCsTorderPerformanceDTOEntry.getKey();
                        CsTorderPerformanceDTO value = dateCsTorderPerformanceDTOEntry.getValue();
                        Integer saleGoodsNum1 = getZeroNull(value.getSaleGoodsNum());
                        if (dateCsRefundDayDTOMap != null && null != dateCsRefundDayDTOMap.get(key)) {
                            CsRefundDayDTO csRefundDayDTO = dateCsRefundDayDTOMap.get(key);
                            saleGoodsNum.add(saleGoodsNum1 - getZeroNull(csRefundDayDTO.getCompletedRefundProductNum()));
                        } else {
                            saleGoodsNum.add(saleGoodsNum1);
                        }
                    }
                    target.setSaleGoodsNum(saleGoodsNum.intValue());
                }
            }

        }
    }

    private int getZeroNull(Integer i) {
        return i == null ? 0 : i;
    }

    private double getZeroNull(Double i) {
        return i == null ? 0 : i;
    }

    private long getZeroNull(Long i) {
        return i == null ? 0 : i;
    }

    private <O> void minusDouble(O o, Function<O, Double> source, Function<O, Double> min, BiConsumer<O, Double> consumer) {
        final Double sur = source.apply(o);
        final Double minS = min.apply(o);
        if (sur == null || minS == null) {
            return;
        }
        double va = sur - minS;
//        if (va < 0) {
//            va = 0;
//        }
        consumer.accept(o, va);
    }

    private <O> void minusInt(O o, Function<O, Integer> source, Function<O, Integer> min, BiConsumer<O, Integer> consumer) {
        final Integer sur = source.apply(o);
        final Integer minS = min.apply(o);
        if (sur == null || minS == null) {
            return;
        }
        int va = sur - minS;
//        if (va < 0) {
//            va = 0;
//        }
        consumer.accept(o, va);
    }


    /**
     * 下单维度绩效
     */
    private void toOrderPerformance(CsPerformanceVo target, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsTorderPerformanceDTO> csTorderPerformanceDTOMap =
                (Map<EntryService, CsTorderPerformanceDTO>) properties.get("csTorderPerformanceDTOMap");
        if (MapUtils.isEmpty(csTorderPerformanceDTOMap)) {
            return;
        }
        CsTorderPerformanceDTO csPerformanceDTO = csTorderPerformanceDTOMap.get(entry);
        BeanUtils.copyProperties(csPerformanceDTO, target);
        target.setSaleAmountOrigin(target.getSaleAmount());
    }

    /**
     * 下单维度绩效前置数据
     */
    private void toOrderPerformanceBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("toOrderPerformanceBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsTorderPerformanceDTO> csTorderPerformanceDTOS = csTorderPerformanceBusiness.searchByDateShopNicks(
                nicks, shopId, startDate, endDate, schemaId, dimension, filterDates, context.get(),

                false);
        if (CollectionUtils.isNotEmpty(csTorderPerformanceDTOS)) {
            Map<EntryService, CsTorderPerformanceDTO> csTorderPerformanceDTOMap =
                    csTorderPerformanceDTOS.stream().collect(Collectors.toMap(
                            k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()), v -> v, (x, y) -> {
                                LOGGER.error("下单维度绩效重复数据 data1 : {} , data2 : {} ", x, y);
                                return x;
                            }
                    ));
            properties.put("csTorderPerformanceDTOMap", csTorderPerformanceDTOMap);
        }
        properties.put("csTorderPerformanceDTOS", csTorderPerformanceDTOS);
        properties.put("toOrderPerformanceBefore", true);
    }

    /**
     * 询单维度绩效前置数据
     */
    private void performanceBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("performanceBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsPerformanceDTO> csPerformanceDTOS = getCsPerformance(
                nicks, shopId, startDate, endDate, dimension, schemaId, filterDates
        );
        properties.addAll(csPerformanceDTOS, CsPerformanceDTO::getCsNick);
        if (CollectionUtils.isNotEmpty(csPerformanceDTOS)) {
            Map<EntryService, CsPerformanceDTO> csPerformanceMaps =
                    csPerformanceDTOS.stream().collect(Collectors.toMap(
                            k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()), v -> v, (x, y) -> {
                                LOGGER.error("询单维度绩效重复数据 data1 : {} , data2 : {} ", x, y);
                                return x;
                            })
                    );
            properties.put("csPerformanceMaps", csPerformanceMaps);
        }
        properties.put("csPerformanceDTOS", csPerformanceDTOS);
        properties.put("performanceBefore", true);
    }

    /**
     * 询单维度绩效
     */
    private void performance(CsPerformanceVo target, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsPerformanceDTO> csPerformanceMaps =
                (Map<EntryService, CsPerformanceDTO>) properties.get("csPerformanceMaps");
        if (MapUtils.isEmpty(csPerformanceMaps)) {
            return;
        }
        CsPerformanceDTO csPerformanceDTO = csPerformanceMaps.get(entry);
        BeanUtils.copyProperties(csPerformanceDTO, target);
    }

    /**
     * 处理特殊的汇总均值
     *
     * @param count
     * @param avg
     * @param properties
     * @param simple        是否是正常的日期查询
     * @param size
     * @param propertyCount
     */
    private void processAvgAndCountSpec(CsPerformanceVo count, CsPerformanceVoAvg avg,
                                        ContextService<String, Object> properties,
                                        Boolean simple, int size,int sizes, Map<String, Integer> propertyCount) {

        final Boolean monthSearch = (Boolean) properties.getOrDefault("monthSearch", false);
        if (monthSearch) {
            final Set<String> nicks = properties.getNicks();
            if (CollectionUtils.isNotEmpty(nicks) && nicks.size() == 1) {
                size = properties.getInteger("monthSum");
            }
            propertyCount = null;
        }
        // 将count的属性值取出，根据size 平均赋值给avg
        if (simple && size != 0)
            BeanUtils.avgPropertyValNew(count, avg, size,sizes, propertyCount, allowValueField);

        // 处理 a = b / c 情dd .况
        {
            guestAvg(count, avg);
            computerConversion(count, avg);
            workMeasureComputer(count, avg);
            refundDayComputer(count, avg);
            satisfactionProportionComputer(count, avg);
            if (simple)
                withTeam(count, avg, properties);
        }

        /*最早最晚上下线时间*/
        final LocalTime maxOnline = (LocalTime) properties.get("maxOnline");
        final LocalTime maxOffline = (LocalTime) properties.get("maxOffline");
        count.setFirstOnlineDateTime(maxOnline);
        count.setLastOfflineDateTime(maxOffline);


        final ShopSystemsettingDTO sys = (ShopSystemsettingDTO) properties.get("shopSystemsetting");
        // 业务天时间点
        final Integer schedulingTimeDot = sys.getSchedulingTimeDot();

        // 最早上线时间 的平均
        Long firstOnlineDatetime = properties.getLong("firstOnlineDatetime");
        if (Objects.nonNull(firstOnlineDatetime))
            avg.setFirstOnlineDateTime(LocalTime.ofSecondOfDay(firstOnlineDatetime /
                    properties.getLongOrDefault("firstOnlineDatetimeSize", 1L)).plusHours(schedulingTimeDot));
        // 最晚上线时间 的平均
        Long lastOfflineDatetime = properties.getLong("lastOfflineDatetime");
        if (Objects.nonNull(lastOfflineDatetime))
            avg.setLastOfflineDateTime(LocalTime.ofSecondOfDay(lastOfflineDatetime /
                    properties.getLongOrDefault("lastOfflineDatetimeSize", 1L)).plusHours(schedulingTimeDot));

        Long avgOfflineDatetime = properties.getLong("avgOfflineDatetime");
        if (Objects.nonNull(avgOfflineDatetime))
            avg.setAvgLastOfflineDateTime(LocalTime.ofSecondOfDay(avgOfflineDatetime /
                    properties.getLongOrDefault("avgOfflineDatetimeSize", 1L)).plusHours(schedulingTimeDot));

        Long avgOnlineDatetime = properties.getLong("avgOnlineDatetime");
        if (Objects.nonNull(avgOnlineDatetime))
            avg.setAvgFirstOnlineDateTime(LocalTime.ofSecondOfDay(avgOnlineDatetime /
                    properties.getLongOrDefault("avgOnlineDatetimeSize", 1L)).plusHours(schedulingTimeDot));

        Set<String> nicks = properties.getSearchsNick();
        if (nicks != null) {
            avg.setSaleAmountPercent(nicks.size() == 0 ? 0 : avg.getSaleAmountPercent() / nicks.size());
        } else {
            // avg.setSaleAmountPercent(0D);
        }
    }


    /**
     * 和团队有关的数据计算  - 》 每行数据的计算
     */
    private void withTeam(CsPerformanceVo vo, ContextService<String, Object> properties, Boolean simple, Supplier<Double> saleAmountSupplier) {
        final Object o = properties.get("team");
        if (o instanceof CsTorderPerformanceDTO) {
            final CsTorderPerformanceDTO team = (CsTorderPerformanceDTO) o;
            teamComputer(vo, team, saleAmountSupplier);
        }
        if (o instanceof Map) {
            Map<Date, CsTorderPerformanceDTO> dayCsperformanceInfo = (Map<Date, CsTorderPerformanceDTO>) o;
            if (org.springframework.util.CollectionUtils.isEmpty(dayCsperformanceInfo)) {
                return;
            }
            final Date date = vo.getDate();
            if (Objects.isNull(date)) {
                return;
            }
            CsTorderPerformanceDTO dayTeamInfo = dayCsperformanceInfo.get(date);
            // 月份的处理
            if (!simple) {
                Map<Date, CsTorderPerformanceDTO> monthCsPm = (Map<Date, CsTorderPerformanceDTO>) properties.get("team_month");
                if (org.springframework.util.CollectionUtils.isEmpty(monthCsPm)) {
                    return;
                }
                dayTeamInfo = monthCsPm.get(date);
            }

            teamComputer(vo, dayTeamInfo, saleAmountSupplier);
        }
    }

    private void teamComputer(CsPerformanceVo vo, CsTorderPerformanceDTO team, Supplier<Double> saleAmountSupplier) {
        if (team == null) {
            return;
        }
        final Double teamOutStockAmount = team.getOutStockAmount();
        if (teamOutStockAmount != 0) {
            vo.setPersonalOutStockAmountPercent(vo.getOutStockAmount() * 1.0 / teamOutStockAmount);
        }
        if (team.getSaleAmount() != 0) {
            vo.setSaleAmountPercent((saleAmountSupplier == null ? vo.getSaleAmount() : saleAmountSupplier.get()) / team.getSaleAmount());
        }
    }

    /**
     * 和团队有关的数据计算
     */
    private void withTeam(CsPerformanceVo cou, CsPerformanceVoAvg vo, ContextService<String, Object> properties) {
        final Object o = properties.get("team");
        if (o instanceof CsTorderPerformanceDTO) {
            final CsTorderPerformanceDTO team = (CsTorderPerformanceDTO) o;
            teamComputer(vo, team, cou);
        }
        if (o instanceof Map) {
            final Map<Date, CsTorderPerformanceDTO> dayCsperformanceInfo = (Map<Date, CsTorderPerformanceDTO>) o;
            if (org.springframework.util.CollectionUtils.isEmpty(dayCsperformanceInfo)) {
                return;
            }
            final List<CsTorderPerformanceDTO> csTorderPerformanceLst = new ArrayList<>(dayCsperformanceInfo.values());
            CsTorderPerformanceDTO count = csTorderPerformanceLst.size() > 0 ? csTorderPerformanceLst.get(0) : null;
            for (int i = 1; i < csTorderPerformanceLst.size(); i++) {
                BeanUtils.countPropertyVal(csTorderPerformanceLst.get(i), count);
            }
            teamComputer(vo, count, cou);
        }
    }

    /**
     * @param vo
     * @param team
     * @param cou
     */
    private void teamComputer(CsPerformanceVoAvg vo, CsTorderPerformanceDTO team, CsPerformanceVo cou) {
        if (team == null) {
            return;
        }
        final Double teamOutStockAmount = team.getOutStockAmount();
        if (teamOutStockAmount != 0) {
            vo.setPersonalOutStockAmountPercent(cou.getOutStockAmount() * 1.0 / teamOutStockAmount);
        } else {
//                vo.setPersonalOutStockAmountPercent(-1.0);
        }
        if (team.getSaleAmount() != 0) {
            // 这里除以 原生的数据
            vo.setSaleAmountPercent(cou.getSaleAmountOrigin() / team.getSaleAmount());
        } else {
//                vo.setSaleAmountPercent(-1.0);
        }
    }


    /**
     * 落实付款 前置数据
     */
    private void workablePayBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("workablePayBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsToOrderIndexDTO> csPerformanceDTOS = csToOrderIndexBusiness.searchByDateShopNicks(
                shopId, startDate, endDate, nicks, dimension, schemaId, filterDates
        );
        if (CollectionUtils.isNotEmpty(csPerformanceDTOS)) {
            Map<EntryService, CsToOrderIndexDTO> csToOrderIndexDTOMap =
                    csPerformanceDTOS.stream().collect(Collectors.toMap(
                            k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()), v -> v, (x, y) -> {
                                LOGGER.error("落实付款重复数据 data1 : {} , data2 : {} ", x, y);
                                return x;
                            })
                    );
            properties.put("csToOrderIndexDTOMap", csToOrderIndexDTOMap);
        }
        properties.put("csPerformanceDTOS", csPerformanceDTOS);
        properties.put("workablePayBefore", true);
    }

    /**
     * 落实付款
     */
    @SuppressWarnings("Duplicates")
    private void workablePay(CsPerformanceVo vo, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsToOrderIndexDTO> csToOrderIndexDTOMap =
                (Map<EntryService, CsToOrderIndexDTO>) properties.get("csToOrderIndexDTOMap");
        if (MapUtils.isEmpty(csToOrderIndexDTOMap)) {
            return;
        }
        CsToOrderIndexDTO csToOrderIndexDTO = csToOrderIndexDTOMap.get(entry);
        BeanUtils.copyProperties(csToOrderIndexDTO, vo);

    }

    /**
     * 工作量 前置数据
     */
    private void workMeasureBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("workMeasureBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        // 工作量
        List<CsChatSessionServiceIndexDTO> csChatSessionServiceIndex =
                csChatSessionServiceIndexBusiness.searchByDateShopCs(nicks, shopId, startDate, endDate, dimension, schemaId, filterDates);
        properties.addAll(csChatSessionServiceIndex, CsChatSessionServiceIndexDTO::getCsNick);
        if (CollectionUtils.isNotEmpty(csChatSessionServiceIndex)) {
            Map<EntryService, CsChatSessionServiceIndexDTO> csChatSessionServiceIndexMap = csChatSessionServiceIndex.stream().collect(
                    Collectors.toMap(k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()), v -> v, (x, y) -> {
                        LOGGER.error("CsChatSessionServiceIndex重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }));
            properties.put("csChatSessionServiceIndexMap", csChatSessionServiceIndexMap);
        }
        properties.put("csChatSessionServiceIndex", csChatSessionServiceIndex);
        properties.put("workMeasureBefore", true);
    }

    /**
     * 工作量
     */
    private void workMeasure(CsPerformanceVo vo, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsChatSessionServiceIndexDTO> csChatSessionServiceIndexMap =
                (Map<EntryService, CsChatSessionServiceIndexDTO>) properties.get("csChatSessionServiceIndexMap");
        if (MapUtils.isEmpty(csChatSessionServiceIndexMap)) {
            return;
        }
        CsChatSessionServiceIndexDTO csChatSessionServiceIndex = csChatSessionServiceIndexMap.get(entry);
        BeanUtils.copyProperties(csChatSessionServiceIndex, vo);
        workMeasureComputer(vo);

    }

    /**
     * 退款数据 前置数据
     */
    private void refundDayBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("refundDayBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsRefundDayDTO> csRefundDayDTOList =
                csRefundDayBusiness.searchAllDateShopGroup(
                        shopId, nicks, startDate, endDate, dimension, schemaId, filterDates, context.get());
        if (CollectionUtils.isNotEmpty(csRefundDayDTOList)) {
            Map<EntryService, CsRefundDayDTO> csRefundDayDTOListMap = csRefundDayDTOList.stream().collect(
                    Collectors.toMap(k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()), v -> v, (x, y) -> {
                        LOGGER.error("退款数据重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }));
            properties.put("csRefundDayDTOListMap", csRefundDayDTOListMap);
        }
        properties.put("csRefundDayDTOList", csRefundDayDTOList);
        properties.put("refundDayBefore", true);
    }

    /**
     * 退款数据
     */
    private void refundDay(CsPerformanceVo vo, ContextService<String, Object> properties) {

        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsRefundDayDTO> csRefundDayDTOListMap =
                (Map<EntryService, CsRefundDayDTO>) properties.get("csRefundDayDTOListMap");
        if (MapUtils.isEmpty(csRefundDayDTOListMap)) {
            return;
        }
        CsRefundDayDTO csRefundDayDTO = csRefundDayDTOListMap.get(entry);
        if (Objects.isNull(csRefundDayDTO)) {
            return;
        }
        BeanUtils.copyProperties(csRefundDayDTO, vo);
        refundDayComputer(vo);
    }


    /**
     * 值班记录 前置数据
     */
    private void dutyHistoryBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("dutyHistoryBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        final ShopSystemsettingDTO shopSystemsetting = (ShopSystemsettingDTO) properties.get("shopSystemsetting");
        // 业务天时间点
        final Integer schedulingTimeDot = shopSystemsetting.getSchedulingTimeDot();
        List<CsDutyRecordPerformanceDTO> csDutyRecordLst = csDutyRecordBusiness.searchForPerformance(
                shopId, nicks, startDate, endDate, schemaId, dimension, filterDates, schedulingTimeDot
        );
        if (CollectionUtils.isNotEmpty(csDutyRecordLst)) {
            Map<EntryService, CsDutyRecordPerformanceDTO> CsDutyRecordLstMap = csDutyRecordLst.stream().collect(Collectors.toMap(
                    k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                    v -> v,
                    (x, y) -> {
                        LOGGER.error("值班记录重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }
            ));
            properties.put("CsDutyRecordLstMap", CsDutyRecordLstMap);
        }
        properties.put("CsDutyRecordLst", csDutyRecordLst);
        properties.put("dutyHistoryBefore", true);
    }

    /**
     * 值班记录
     */
    private void dutyHistory(CsPerformanceVo vo, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsDutyRecordPerformanceDTO> csDutyRecordLstMap =
                (Map<EntryService, CsDutyRecordPerformanceDTO>) properties.get("CsDutyRecordLstMap");
        if (MapUtils.isEmpty(csDutyRecordLstMap)) {
            return;
        }
        final CsDutyRecordPerformanceDTO csDutyRecordPerformanceDTO = csDutyRecordLstMap.get(entry);
        if (Objects.isNull(csDutyRecordPerformanceDTO)) {
            return;
        }
        BeanUtils.copyProperties(csDutyRecordPerformanceDTO, vo, properties.getStringArray("ignore"));
        dutyHistoryComputer(vo, properties, csDutyRecordPerformanceDTO);
    }

    /**
     * 值班记录特殊计算
     */
    private void dutyHistoryComputer(CsPerformanceVo vo, ContextService<String, Object> properties, CsDutyRecordPerformanceDTO csDutyRecordPerformanceDTO) {
        final ShopSystemsettingDTO shopSystemsetting = (ShopSystemsettingDTO) properties.get("shopSystemsetting");
        // 接待时长
        final Long rceiveDurationTime = BaseUtils.getNonNull(vo.getRceiveDurationTime());
        // 登录时长
        final Long loginDurationTime = BaseUtils.getNonNull(vo.getLoginDurationTime());
        // 接待时长占比
        if (loginDurationTime != 0) {
            vo.setRceiveTimeRate(rceiveDurationTime * 1.0 / loginDurationTime);
        } else {
//            vo.setRceiveDurationTimeRate(-1.0);
        }
        /*业务时间点*/
        final Integer schedulingTimeDot = shopSystemsetting.getSchedulingTimeDot();
        // 最早上线
        final LocalTime firstOnlineDateTime = vo.getFirstOnlineDateTime();
        LocalTime firstOnlineDateTimeProcess = firstOnlineDateTime != null ?
                firstOnlineDateTime.minusHours(schedulingTimeDot) : null;
        // 最晚下线时间
        final LocalTime lastOfflineDateTime = vo.getLastOfflineDateTime();
        LocalTime lastOfflineDateTimeProcess = lastOfflineDateTime != null ?
                lastOfflineDateTime.minusHours(schedulingTimeDot) : null;

        // 最早上线汇总
        final LocalTime maxOnline = (LocalTime) properties.computeIfAbsent("maxOnline", (x) -> firstOnlineDateTime);
        LocalTime maxOnlineProcess = maxOnline == null ? null : maxOnline.minusHours(schedulingTimeDot);
        if (firstOnlineDateTimeProcess != null && maxOnlineProcess != null && firstOnlineDateTimeProcess.isBefore(maxOnlineProcess)) {
            properties.put("maxOnline", firstOnlineDateTime);
        }
        // 最晚下线时间汇总
        final LocalTime maxOffline = (LocalTime) properties.computeIfAbsent("maxOffline", (x) -> lastOfflineDateTime);
        LocalTime maxOfflineProcess = maxOffline == null ? null : maxOffline.minusHours(schedulingTimeDot);
        if (lastOfflineDateTimeProcess != null && maxOfflineProcess != null && lastOfflineDateTimeProcess.isAfter(maxOfflineProcess)) {
            properties.put("maxOffline", lastOfflineDateTime);
        }

        // 时间总和->计算localtime的平均时间
        dateProcess(
                CsDutyRecordPerformanceDTO::getFirstOnlineDateTime,
                properties,
                csDutyRecordPerformanceDTO,
                "firstOnlineDatetime",
                schedulingTimeDot,
                "firstOnlineDatetimeSize"
        );
        dateProcess(
                CsDutyRecordPerformanceDTO::getLastOfflineDateTime,
                properties,
                csDutyRecordPerformanceDTO,
                "lastOfflineDatetime",
                schedulingTimeDot, "lastOfflineDatetimeSize"
        );
        dateProcess(
                CsDutyRecordPerformanceDTO::getAvgLastOfflineDateTime,
                properties,
                csDutyRecordPerformanceDTO,
                "avgOfflineDatetime",
                schedulingTimeDot, "avgOfflineDatetimeSize");
        dateProcess(
                CsDutyRecordPerformanceDTO::getAvgFirstOnlineDateTime,
                properties,
                csDutyRecordPerformanceDTO,
                "avgOnlineDatetime",
                schedulingTimeDot, "avgOnlineDatetimeSize");
    }

    private void dateProcess(
            Function<CsDutyRecordPerformanceDTO, LocalTime> function,
            ContextService<String, Object> properties,
            CsDutyRecordPerformanceDTO csDutyRecordPerformanceDTO,
            String key, Integer schedulingTimeDot, String sizeKey) {
        LocalTime time = function.apply(csDutyRecordPerformanceDTO);
        Long val = properties.getLongOrDefault(key);
        Long valSize = properties.getLongOrDefault(sizeKey);
        if (time != null) {
            int second = time.minusHours(schedulingTimeDot).toSecondOfDay();
            valSize++;
            properties.put(key, val + second);
            properties.put(sizeKey, valSize);
        }

    }

    /**
     * 协助服务 前置数据
     */
    private void helpingServiceBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("helpingServiceBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsAssitIndexDTO> csAssitIndexDTOS =
                csAssitIndexBusiness.searchByDateShopNicks(shopId, startDate, endDate, nicks, dimension, schemaId, filterDates);
        if (CollectionUtils.isNotEmpty(csAssitIndexDTOS)) {
            Map<EntryService, CsAssitIndexDTO> csAssitIndexDTOMap = csAssitIndexDTOS.stream().collect(Collectors.toMap(
                    k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                    v -> v,
                    (x, y) -> {
                        LOGGER.error("协助服务重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }
            ));
            properties.put("csAssitIndexDTOMap", csAssitIndexDTOMap);
        }
        properties.put("csAssitIndexDTOS", csAssitIndexDTOS);
        properties.put("helpingServiceBefore", true);
    }

    /**
     * 协助服务
     */
    private void helpingService(CsPerformanceVo vo, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsAssitIndexDTO> csAssitIndexDTOMap =
                (Map<EntryService, CsAssitIndexDTO>) properties.get("csAssitIndexDTOMap");
        if (MapUtils.isEmpty(csAssitIndexDTOMap)) {
            return;
        }
        CsAssitIndexDTO csAssitIndexDTO = csAssitIndexDTOMap.get(entry);
        BeanUtils.copyProperties(csAssitIndexDTO, vo);
    }

    /**
     * 满意率 前置数据
     */
    private void satisfactionProportionBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("middleBadEvaluateBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsServiceEvaluationDTO> csServiceEvaluationLst =
                csServiceEvaluationBusiness.searchAllDateShopGroup(shopId, nicks, startDate, endDate, dimension, schemaId, filterDates);
        if (CollectionUtils.isNotEmpty(csServiceEvaluationLst)) {
            Map<EntryService, CsServiceEvaluationDTO> csServiceEvaluationMap = csServiceEvaluationLst.stream().collect(Collectors.toMap(
                    k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                    v -> v,
                    (x, y) -> {
                        LOGGER.error("满意率重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }
            ));
            properties.put("csServiceEvaluationMap", csServiceEvaluationMap);
        }
        properties.put("csServiceEvaluationLst", csServiceEvaluationLst);
        properties.put("middleBadEvaluateBefore", true);
    }

    /**
     * 满意率
     */
    private void satisfactionProportion(CsPerformanceVo vo, ContextService<String, Object> properties) {
        Map<EntryService, CsServiceEvaluationDTO> csServiceEvaluationMap =
                (Map<EntryService, CsServiceEvaluationDTO>) properties.get("csServiceEvaluationMap");
        EntryService entry = (EntryService) properties.get("entry");

        if (MapUtils.isNotEmpty(csServiceEvaluationMap)) {
            CsServiceEvaluationDTO csServiceEvaluation = csServiceEvaluationMap.get(entry);
            if (Objects.nonNull(csServiceEvaluation)) {
                BeanUtils.copyProperties(csServiceEvaluation, vo);
                satisfactionProportionComputer(vo);
            }
        }

    }


    /**
     * 分时接待 前置数据
     */
    private void receiveSessionNumHourlyBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("receiveSessionNumHourlyBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<ReceiveSessionNumHourlyDTO> receiveSessionNumHourlyLst =
                receiveSessionNumHourlyBusiness.searchAllDateShopGroup(shopId, nicks, startDate, endDate, dimension, schemaId, filterDates);
        if (CollectionUtils.isNotEmpty(receiveSessionNumHourlyLst)) {
            Map<EntryService, ReceiveSessionNumHourlyDTO> receiveSessionNumHourlyMap = receiveSessionNumHourlyLst.stream().collect(Collectors.toMap(
                    k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                    v -> v,
                    (x, y) -> {
                        LOGGER.error("分时接待重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }
            ));
            properties.put("receiveSessionNumHourlyMap", receiveSessionNumHourlyMap);
        }
        properties.put("receiveSessionNumHourlyLst", receiveSessionNumHourlyLst);
        properties.put("receiveSessionNumHourlyBefore", true);
    }

    /**
     * 分时接待
     */
    private void receiveSessionNumHourly(CsPerformanceVo vo, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, ReceiveSessionNumHourlyDTO> receiveSessionNumHourlyMap =
                (Map<EntryService, ReceiveSessionNumHourlyDTO>) properties.get("receiveSessionNumHourlyMap");
        if (MapUtils.isEmpty(receiveSessionNumHourlyMap)) {
            return;
        }
        ReceiveSessionNumHourlyDTO receiveSessionNumHourlyDTO = receiveSessionNumHourlyMap.get(entry);
        if (receiveSessionNumHourlyDTO != null) {
            BeanUtils.copyProperties(receiveSessionNumHourlyDTO, vo);
        }
    }


    /**
     * 团队数据
     */
    private void csTeam(ContextService<String, Object> properties) {
        // 绩效数据
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        /*查询团队绩效数据，如果是date类型，那么*/
        final String dimension = properties.getString("dimension");
        Long shopId = properties.getLong("shopId");
        String schemaId = properties.getString("schemaId");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        final List<CsTorderPerformanceDTO> torderPerformances =
                csTorderPerformanceBusiness.searchByDateShopNicks(null, shopId, startDate, endDate, schemaId, dimension, filterDates, context.get(), true);

        if (CollectionUtils.isEmpty(torderPerformances)) {
            return;
        }
        /*如果是date类型的话，那么需要将date分组的组内成员聚合*/
        if (Objects.equals(dimension, "date")) {
            Map<Date, List<CsTorderPerformanceDTO>> dayNicksPeroformance =
                    torderPerformances.stream().collect(Collectors.groupingBy(CsTorderPerformanceDTO::getDate));
            Map<Date, CsTorderPerformanceDTO> dateCsTorderPerformanceDTOMap =
                    CommonUtils.countMapValue(dayNicksPeroformance, CsTorderPerformanceDTO::getInstance);
            properties.put("team", dateCsTorderPerformanceDTOMap);
            if ((Boolean) properties.getOrDefault("monthSearch", false)) {
                dayNicksPeroformance =
                        torderPerformances.stream().collect(Collectors.groupingBy(k -> DateUtils.truncate(k.getDate(), MONTH)));
                dateCsTorderPerformanceDTOMap =
                        CommonUtils.countMapValue(dayNicksPeroformance, CsTorderPerformanceDTO::getInstance);
                properties.put("team_month", dateCsTorderPerformanceDTOMap);
            }
            return;
        }
        /*如果是nick类型，将所有的团队数据加起来*/
        final CsTorderPerformanceDTO team = CommonUtils.countBean(torderPerformances, CsTorderPerformanceDTO::getInstance);
        if (Objects.nonNull(team)) {
            properties.put("team", team);
        }
    }


    /**
     * 中差评前置数据
     */
    private void middleBadappraisedBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("middleBadappraisedBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");
        String dimension = properties.getString("dimension");
        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        List<CsOrderEvaluateDTO> csOrderEvaluate = csOrderEvaluateBusiness.searchDateShop(
                shopId, nicks, startDate, endDate, schemaId, dimension, filterDates
        );
        if (CollectionUtils.isNotEmpty(csOrderEvaluate)) {
            Map<EntryService, CsOrderEvaluateDTO> csOrderEvaluateMap = csOrderEvaluate.stream().collect(Collectors.toMap(

                    k -> EntryService.of(k.getShopId(), k.getCsNick(), k.getDate()),
                    v -> v,
                    (x, y) -> {
                        LOGGER.error("中差评重复数据 data1 : {} , data2 : {} ", x, y);
                        return x;
                    }
            ));
            properties.put("csOrderEvaluateMap", csOrderEvaluateMap);
        }
        properties.put("csOrderEvaluate", csOrderEvaluate);
        properties.put("middleBadappraisedBefore", true);
    }

    /**
     * 中差评
     */
    private void middleBadappraised(CsPerformanceVo vo, ContextService<String, Object> properties) {
        EntryService entry = (EntryService) properties.get("entry");
        Map<EntryService, CsOrderEvaluateDTO> csOrderEvaluateMap = (Map<EntryService, CsOrderEvaluateDTO>) properties.get("csOrderEvaluateMap");
        if (MapUtils.isEmpty(csOrderEvaluateMap)) {
            return;
        }
        CsOrderEvaluateDTO csOrderEvaluate = csOrderEvaluateMap.get(entry);
        BeanUtils.copyProperties(csOrderEvaluate, vo);
    }

    /**
     * 流失数据前置数据
     */
    private void lossRecordBefore(ContextService<String, Object> properties) {
        boolean flag = properties.getBoolean("lossRecordBefore");
        if (flag) {
            return;
        }
        Date startDate = properties.getDate("startDate");
        Date endDate = properties.getDate("endDate");
        Long shopId = properties.getLong("shopId");
        Set<String> nicks = properties.getNicks();
        String schemaId = properties.getString("schemaId");

        final Set<Date> filterDates = (Set<Date>) properties.get("filterDates");
        // 所有数据
        List<CsLossRecordDTO> csLossRecordDTOS = csLossRecordBusiness.searchByDateShopNicks(
                nicks, shopId, startDate, endDate, schemaId, filterDates
        );
        /**
         *  Map(key:csNick,value:Map(key: type(0：无 1：询单流失 3：付款流失 4：出库流失),value: Map(key:date,value:CsLossRecordDTO)))
         */
        final Map<String, Map<Object, Map<Date, CsLossRecordDTO>>> lossRecordMap =
                com.pes.jd.util.MapUtils.extractsSingle(csLossRecordDTOS,
                        CsLossRecordDTO::getCsNick,
                        CsLossRecordDTO::getType,
                        CsLossRecordDTO::getDate
                );
        properties.put("lossRecordMap", lossRecordMap);
        properties.put("lossRecordBefore", true);
    }

    /**
     * 流失数据
     */
    private void lossRecord(CsPerformanceVo vo, ContextService<String, Object> properties) {
        Map<String, Map<Object, Map<Date, CsLossRecordDTO>>> lossRecordMap =
                (Map<String, Map<Object, Map<Date, CsLossRecordDTO>>>) properties.get("lossRecordMap");
        if (Objects.isNull(lossRecordMap)) {
            return;
        }
        // key:type value:
        Map<Object, Map<Date, CsLossRecordDTO>> csMultiValueMap = lossRecordMap.get(vo.getCsNick());
        if (Objects.isNull(csMultiValueMap)) {
            return;
        }
        // 询单流失 询单流失人数
        final boolean cs = properties.isCs();
        this.<Integer, Integer, Integer, Double>doConsumerForLoss(csMultiValueMap, cs,
                vo.getDate(), (byte) 1, null, vo::setEnquiryLossNum, null, null);
        // 付款流失 付款流失订单数  付款流件数  付款流失金额
        this.<Integer, Integer, Integer, Double>doConsumerForLoss(csMultiValueMap,
                cs, vo.getDate(), (byte) 3, null, vo::setPaidLossNum, vo::setPaidLossGoodsNum, vo::setPaidLossMoney);
        // 出库流失 出库流失订单数  出库流失人数  出库流失件数  出库流失金额
        this.doConsumerForLoss(csMultiValueMap,
                cs, vo.getDate(), (byte) 4, vo::setOutStackLossOrderNum, vo::setOutStackLossNum, vo::setOutStackLossGoodsNum, vo::setOutStackLossMoney);
    }

    @SuppressWarnings("unchecked")
    private <ORDER, PERSON, ITEM_NUM, MONEY> void doConsumerForLoss(Map<Object, Map<Date, CsLossRecordDTO>> csMultiValueMap,
                                                                    boolean cs,
                                                                    Date date,
                                                                    byte type,
                                                                    Consumer<ORDER> order,
                                                                    Consumer<PERSON> person,
                                                                    Consumer<ITEM_NUM> itemNum,
                                                                    Consumer<MONEY> money) {
        if (Objects.nonNull(csMultiValueMap)) {
            Map<Date, CsLossRecordDTO> dateCsLossRecordDTOMap = csMultiValueMap.get(type);
            if (Objects.nonNull(dateCsLossRecordDTOMap)) {
                CsLossRecordDTO csLossRecordDTO = dateCsLossRecordDTOMap.get(date);
                if (Objects.nonNull(order)) {
                    Integer orderNum = csLossRecordDTO != null ? csLossRecordDTO.getOrderNum() : 0;
                    /*如果查询类型为客服*/
                    if (cs) {
                        orderNum = 0;
                        for (CsLossRecordDTO value : dateCsLossRecordDTOMap.values()) {
                            orderNum += value.getOrderNum();
                        }
                    }
                    order.accept((ORDER) Objects.requireNonNull(orderNum, "orderNum not be null"));
                }
                if (Objects.nonNull(person)) {
                    Integer customerNum = csLossRecordDTO != null ? csLossRecordDTO.getCustomerNum() : 0;
                    /*如果查询类型为客服*/
                    if (cs) {
                        customerNum = 0;
                        for (CsLossRecordDTO value : dateCsLossRecordDTOMap.values()) {
                            customerNum += value.getCustomerNum();
                        }
                    }
                    person.accept((PERSON) Objects.requireNonNull(customerNum, "customerNum not be null"));
                }
                if (Objects.nonNull(itemNum)) {
                    Integer orderGoodsNum = csLossRecordDTO != null ? csLossRecordDTO.getOrderGoodsNum() : 0;
                    /*如果查询类型为客服*/
                    if (cs) {
                        orderGoodsNum = 0;
                        for (CsLossRecordDTO value : dateCsLossRecordDTOMap.values()) {
                            orderGoodsNum += value.getOrderGoodsNum();
                        }
                    }
                    itemNum.accept((ITEM_NUM) Objects.requireNonNull(orderGoodsNum, "orderGoodsNum not be null"));
                }
                if (Objects.nonNull(money)) {
                    Double orderSaleAmount = csLossRecordDTO != null ? csLossRecordDTO.getOrderSaleAmount() : 0;
                    /*如果查询类型为客服*/
                    if (cs) {
                        orderSaleAmount = 0.0;
                        for (CsLossRecordDTO value : dateCsLossRecordDTOMap.values()) {
                            orderSaleAmount += value.getOrderSaleAmount();
                        }
                    }
                    money.accept((MONEY) Objects.requireNonNull(orderSaleAmount, "orderSaleAmount not be null"));
                }
            }
        }
    }


    private List<CsPerformanceDTO> getCsPerformance(
            Set<String> nicks, Long shopId, Date startDate, Date endDate,
            String dimension, String schemaId, Set<Date> filterDates) {
        List<CsPerformanceDTO> csPerformanceDTOS = csPerformanceDao.searchByDateShopCs(
                nicks, shopId, startDate, endDate, dimension, schemaId, filterDates);
        final String date = "date";
        if (Objects.equals(date, dimension)) {
            return csPerformanceDTOS;
        }
        /*如果是根据csNick，那么根据csNick聚合*/
        return CommonUtils.polymerize(csPerformanceDTOS,
                CsPerformanceDTO::getInstance, CsPerformanceDTO::getCsNick, (x, y) -> x.setCsNick(y.getCsNick()), x -> x.setShopId(shopId), null, PerformanceConstans.IGNORE_PROPERTIES);
    }

    /*================================================================================================*/
    /*                                        a = b / c                                               */
    /*================================================================================================*/

    /**
     * 客单价 -> 销售数据
     */
    private void guestAvg(CsPerformanceVo vo) {
        // 销售额
        final Double saleAmount = BaseUtils.getNonNull(vo.getSaleAmount());
        // 销售人数
        final Integer saleBuyerNum = BaseUtils.getNonNull(vo.getSaleBuyerNum());
        // 客服销售量
        final Integer saleGoodsNum = BaseUtils.getNonNull(vo.getSaleGoodsNum());
        // 出库人数
        final Integer outStockNum = BaseUtils.getNonNull(vo.getOutStockNum());
        // 出库金额
        final Double outStockAmount = BaseUtils.getNonNull(vo.getOutStockAmount());
        // 出库件数
        final Integer outStockGoodsNum = BaseUtils.getNonNull(vo.getOutStockGoodsNum());
        // 询单→当日下单人数
        final Integer orderedNumToday = BaseUtils.getNonNull(vo.getOrderedNumToday());
        // 询单→当日下单金额
        final Double orderedAmountToday = BaseUtils.getNonNull(vo.getOrderedAmountToday());
        // 询单→当日的下单件数
        final Integer orderedGoodsNumToday = BaseUtils.getNonNull(vo.getOrderedGoodsNumToday());
        // 询单人数
        //销售客单价（元/件） 本客服客单价=本客服销售额/本客服销售人数
        if (saleBuyerNum != 0) {
            vo.setSaleGuestAvgAmount(saleAmount / saleBuyerNum);
        } else {
//            vo.setSaleGuestAvgAmount(-1.0);
        }
        //销售客件数（件/人） 本客服客件数=本客服销售量/本客服销售人数
        if (saleBuyerNum != 0) {
            vo.setSaleGuestAvgGoods(saleGoodsNum * 1.0 / saleBuyerNum);
        } else {
//            vo.setSaleGuestAvgGoods(-1.0);
        }
        //销售件均价（元/件） 本客服件均价=本客服销售额/本客服销售量
        if (saleGoodsNum != 0) {
            vo.setSaleGoodsAvgAmount(saleAmount / saleGoodsNum);
        } else {
//            vo.setSaleGoodsAvgAmount(-1.0);
        }
        // 本客服出库客单价=本客服促成出库金额/促成出库人数
        if (outStockNum != 0) {
            vo.setOutStockGuestAvgAmount(outStockAmount / outStockNum);
        } else {
//            vo.setOutStockGuestAvgAmount(-1.0);
        }
        //出库客件数（件/人） 本客服出库客件数=本客服促成出库件数/促成出库人数
        if (outStockNum != 0) {
            vo.setOutStockGuestItemNum(outStockGoodsNum * 1.0 / outStockNum);
        } else {
//            vo.setOutStockGuestItemNum(-1.0);
        }
        // 本客服出库件均价=本客服促成出库金额/促成出库件数
        if (outStockGoodsNum != 0) {
            vo.setOutStockItemAvgAmount(outStockAmount / outStockGoodsNum);
        } else {
//            vo.setOutStockItemAvgAmount(-1.0);
        }
        // 下单件均价（元/件）客服促成下单金额/下单件数
        if (orderedGoodsNumToday != 0) {
            vo.setOrderItemAvgAmount(orderedAmountToday / orderedGoodsNumToday);
        } else {
//            vo.setOrderItemAvgAmount(-1.0);
        }
        // 下单客单价（元/人） 客服促成下单金额/下单人数
        if (orderedNumToday != 0) {
            vo.setOrderedGuestAvgPrice(orderedAmountToday / orderedNumToday);
        } else {
//            vo.setOrderedGuestAvgPrice(-1.0);
        }
        // 下单客件数（件/人） 客服促成下单件数/下单人数
        if (orderedNumToday != 0) {
            vo.setOrderedGuestAvgAmount(orderedGoodsNumToday * 1.0 / orderedNumToday);
        } else {
//            vo.setOrderedGuestAvgAmount(-1.0);
        }
    }


    /**
     * 转化率
     */
    private void computerConversion(CsPerformanceVo vo) {
        // 询单→当日下单人数
        final Integer orderedNumToday = BaseUtils.getNonNull(vo.getOrderedNumToday());
        // 询单人数
        final Integer enquiryNum = BaseUtils.getNonNull(vo.getEnquiryNum());
        // 当日咨询，当日下单，当日或次日付款的人数
        final Integer paidNumTodayNext = BaseUtils.getNonNull(vo.getPaidNumTodayNext());
        // 当日咨询，最终落实付款的人数
        final Integer paidNumFinal = BaseUtils.getNonNull(vo.getPaidNumFinal());
        // 当日咨询 最终落实下单的人数
        final Integer orderedNumFinal = BaseUtils.getNonNull(vo.getOrderedNumFinal());
        // 当日询单 当天落实下单 并且 付了款的人数
        final Integer toOrderedPaidNumToday = BaseUtils.getNonNull(vo.getToOrderedPaidNumToday());
        // 最终出库人数
        final Integer outStockOrderBuyerNumFinal = BaseUtils.getNonNull(vo.getOutStockOrderBuyerNumFinal());
        // 下单人数
        final Integer toOrderedNum = BaseUtils.getNonNull(vo.getToOrderedNum());

        // 最终付款人数
        final Integer toOrderedPaidNumFinal = BaseUtils.getNonNull(vo.getToOrderedPaidNumFinal());

        // 询单→次日付款转化率 询单→次日付款转化率=当日或次日付款人数/询单人数（当前询单有效时长配置为*天，该数据须延迟*天统计，当前可查看*年*月*日（含）的数据）
        if (enquiryNum != 0) {
            vo.setQueryToTomorrow(paidNumTodayNext * 1.0 / enquiryNum);
        } else {
//            vo.setPersonalOutStockAmountPercent(-1.0);
        }
        // 询单→最终付款转化率
        if (enquiryNum != 0) {
            vo.setQueryToFinalPaid(paidNumFinal * 1.0 / enquiryNum);
        } else {
//            vo.setQueryToFinalPaid(-1.0);
        }
        // 询单→当日下单转化率  orderedNumToday
        if (enquiryNum != 0) {
            vo.setQueryToOrderedToday(orderedNumToday * 1.0 / enquiryNum);
        } else {
//            vo.setQueryToOrderedToday(-1.0);
        }
        // 询单→最终下单转化率 orderedNumFinal
        if (enquiryNum != 0) {
            vo.setQueryToFinalOrdered(orderedNumFinal * 1.0 / enquiryNum);
        } else {
//            vo.setQueryToFinalPaid(-1.0);
        }
        // 询单→当日下单转化率  当日下单人数/询单人数 orderedNumToday/enquiryNum
        if (enquiryNum != 0) {
            vo.setQueryToOrderedToday(orderedNumToday * 1.0 / enquiryNum);
        } else {
//            vo.setQueryToOrderedToday(-1.0);
        }
        // 询单→最终下单转化率 最终下单人数/询单人数  orderedNumFinal/enquiryNum
        if (enquiryNum != 0) {
            vo.setQueryToFinalOrdered(orderedNumFinal * 1.0 / enquiryNum);
        } else {
//            vo.setQueryToFinalOrdered(-1.0);
        }
        // 下单→当日付款转化率 当日付款人数/下单人数  paidNumToday/orderedNumToday
        if (toOrderedNum != 0) {
            vo.setOrderedToPaid(toOrderedPaidNumToday * 1.0 / toOrderedNum);
        } else {
//            vo.setOrderedToPaid(-1.0);
        }
        // 下单→最终付款转化率 orderedToPaidFinal 最终付款人数/下单人数  paidNumFinal/orderedNumToday
        if (toOrderedNum != 0) {
            vo.setOrderedToPaidFinal(toOrderedPaidNumFinal * 1.0 / toOrderedNum);
        } else {
//            vo.setOrderedToPaidFinal(-1.0);
        }
        // 询单→出库转化率
        if (enquiryNum != 0) {
            vo.setQueryToOutStock(outStockOrderBuyerNumFinal * 1.0 / enquiryNum);
        } else {
//            vo.setQueryToOutStock(-1.0);
        }

    }


    /**
     * 工作量的特殊计算
     */
    private void workMeasureComputer(CsPerformanceVo vo) {
        //顾客消息数
        final Integer custChatNum = BaseUtils.getNonNull(vo.getCustChatNum());
        //客服消息数
        final Integer csChatNum = BaseUtils.getNonNull(vo.getCsChatNum());
        // 接待量
        final Integer receiveSessionNum = BaseUtils.getNonNull(vo.getReceiveSessionNum());
        // 未回复量
        final Integer nonReplySessionNum = BaseUtils.getNonNull(vo.getNonReplySessionNum());
        //回复率 （接待量-未回复量）/接待量
        vo.setResponseRate(receiveSessionNum <= 0 ? 0 : (receiveSessionNum - nonReplySessionNum) * 1.0 / receiveSessionNum);
        //答问比=客服消息数/顾客消息数 cs_chat_num/cust_chat_num
        vo.setAnswerRatio(custChatNum == 0 ? 0D : csChatNum * 1.0 / custChatNum);
//        留言接待量
        final Integer leaveMsgReceiveSessionNum = BaseUtils.getNonNull(vo.getLeaveMsgReceiveSessionNum());
        //留言分配量
        final Integer leaveMsgSessionNum = BaseUtils.getNonNull(vo.getLeaveMsgSessionNum());
//        留言响应率=留言接待量／留言分配量
        vo.setLeaveMsgResponseRate(leaveMsgSessionNum == 0 ? 0 : leaveMsgReceiveSessionNum * 1.0 / leaveMsgSessionNum);
//      快速应答的会话
        final Integer avgRespInQuickTime = BaseUtils.getNonNull(vo.getAvgRespInQuickTime());
//        接待的会话
        final Integer sessionNum = BaseUtils.getNonNull(vo.getSessionNum());
        //快速应答率 释义：（接待量-未回复）/接待量的占比
        int calSessionNum = sessionNum - nonReplySessionNum - leaveMsgReceiveSessionNum;
        double rapidAnswerRate = calSessionNum <= 0 ? 0 : avgRespInQuickTime * 1.0 / calSessionNum;
        vo.setRapidAnswerRate(rapidAnswerRate);
        LOGGER.info("csNick={},sessionNum={},nonReplySessioinNum={},rapidAnswerRate={}",
                vo.getCsNick(), sessionNum, nonReplySessionNum, rapidAnswerRate);
        if (vo.getRapidAnswerRate() > 1) vo.setRapidAnswerRate(1.0);
        //平均回复消息数=客服消息数／接待量
        vo.setAvgCsMsgSessionNum(receiveSessionNum == 0 ? 0.0 : csChatNum * 1.0 / receiveSessionNum);
        // 首次平均响应：本客服对顾客第一次回复用时的平均值 （基于接待量，判定的规则支持自定义，可点此设置）
        final Double respTimeFirstCount = BaseUtils.getNonNull(vo.getRespTimeFirstCount());
        int sess = BaseUtils.getNonNull(sessionNum) - BaseUtils.getNonNull(nonReplySessionNum) - BaseUtils.getNonNull(leaveMsgReceiveSessionNum);
        vo.setAvgRespTimeFirst(sess <= 0 ? 0 : respTimeFirstCount / sess);
        // 平均响应时间：本客服回复消息与顾客消息之间时间差的平均值（基于接待量，判定的规则支持自定义，可点此设置）
        final Double respTimeCount = BaseUtils.getNonNull(vo.getRespTimeCount());
//        回合数
        final Integer chatRoundNum = BaseUtils.getNonNull(vo.getChatRoundNum());
        vo.setAvgRespTime(chatRoundNum <= 0 ? 0 : respTimeCount / chatRoundNum);
        // 平均会话时长：本|客服平均每通会话从会话建立到会话关闭的时间间隔。会话时长=会话关闭时间-会话开始时间（包含10分钟会话等待时间，留言接待的不计入）
        final Double sessionDurationTimeCount = BaseUtils.getNonNull(vo.getSessionDurationTimeCount());
        vo.setAvgSessionDurationTime(sessionDurationTimeCount <= 0 ? 0 : sessionDurationTimeCount / sessionNum);
    }


    /**
     * 退款的特殊计算
     *
     * @param vo
     */
    private void refundDayComputer(CsPerformanceVo vo) {
        // 退款率
        Integer saleGoodsNum = vo.getSaleGoodsNum();
        if (saleGoodsNum != null && saleGoodsNum != 0) {
            vo.setRefundPercent(vo.getCompletedRefundProductNum() * 1.0 / saleGoodsNum);
        } else {
//            vo.setRefundPercent(-1.0);
        }
    }


    /**
     * 满意率特殊计算
     *
     * @param vo
     */
    private void satisfactionProportionComputer(CsPerformanceVo vo) {
        // 评价量
        final Integer evalReplyNum = vo.getEvalReplyNum();
        // 接待量
        final Integer receiveSessionNum = vo.getReceiveSessionNum();
        // 评价率
        vo.setEvaluationRate(receiveSessionNum == 0 ? 0 : 1.0 * evalReplyNum / receiveSessionNum);
        // 满意率
        vo.setSatisfactionRate(evalReplyNum == 0 ? 0 : (vo.getVerySatisfiedNum() + vo.getSatisfiedNum()) * 1.0 / evalReplyNum);
        // 邀评率
        vo.setInviteRate(receiveSessionNum == 0 ? 0 : vo.getEvalSendNum() * 1.0 / receiveSessionNum);
    }


    /*================================================================================================*/
    /*                                        a = count(b) / count(c)                                 */
    /*================================================================================================*/


    /**
     * 客单价 -> 销售数据
     */
    private void guestAvg(CsPerformanceVo vo, CsPerformanceVoAvg vag) {
        // 销售额
        final Double saleAmount = BaseUtils.getNonNull(vo.getSaleAmount());
        // 销售人数
        final Integer saleBuyerNum = BaseUtils.getNonNull(vo.getSaleBuyerNum());
        // 客服销售量
        final Integer saleGoodsNum = BaseUtils.getNonNull(vo.getSaleGoodsNum());
        // 出库人数
        final Integer outStockNum = BaseUtils.getNonNull(vo.getOutStockNum());
        // 出库金额
        final Double outStockAmount = BaseUtils.getNonNull(vo.getOutStockAmount());
        // 出库件数
        final Integer outStockGoodsNum = BaseUtils.getNonNull(vo.getOutStockGoodsNum());
        // 询单→当日下单人数
        final Integer orderedNumToday = BaseUtils.getNonNull(vo.getOrderedNumToday());
        // 询单→当日下单金额
        final Double orderedAmountToday = BaseUtils.getNonNull(vo.getOrderedAmountToday());
        // 询单→当日的下单件数
        final Integer orderedGoodsNumToday = BaseUtils.getNonNull(vo.getOrderedGoodsNumToday());
        // 询单人数
        //销售客单价（元/件） 本客服客单价=本客服销售额/本客服销售人数
        if (saleBuyerNum != 0) {
            vag.setSaleGuestAvgAmount(saleAmount / saleBuyerNum);
        } else {
//            vo.setSaleGuestAvgAmount(-1.0);
        }
        //销售客件数（件/人） 本客服客件数=本客服销售量/本客服销售人数
        if (saleBuyerNum != 0) {
            vag.setSaleGuestAvgGoods(saleGoodsNum * 1.0 / saleBuyerNum);
        } else {
//            vo.setSaleGuestAvgGoods(-1.0);
        }
        //销售件均价（元/件） 本客服件均价=本客服销售额/本客服销售量
        if (saleGoodsNum != 0) {
            vag.setSaleGoodsAvgAmount(saleAmount / saleGoodsNum);
        } else {
//            vo.setSaleGoodsAvgAmount(-1.0);
        }
        // 本客服出库客单价=本客服促成出库金额/促成出库人数
        if (outStockNum != 0) {
            vag.setOutStockGuestAvgAmount(outStockAmount / outStockNum);
        } else {
//            vo.setOutStockGuestAvgAmount(-1.0);
        }
        //出库客件数（件/人） 本客服出库客件数=本客服促成出库件数/促成出库人数
        if (outStockNum != 0) {
            vag.setOutStockGuestItemNum(outStockGoodsNum * 1.0 / outStockNum);
        } else {
//            vo.setOutStockGuestItemNum(-1.0);
        }
        // 本客服出库件均价=本客服促成出库金额/促成出库件数
        if (outStockGoodsNum != 0) {
            vag.setOutStockItemAvgAmount(outStockAmount / outStockGoodsNum);
        } else {
//            vo.setOutStockItemAvgAmount(-1.0);
        }
        // 下单件均价（元/件）客服促成下单金额/下单件数
        if (orderedGoodsNumToday != 0) {
            vag.setOrderItemAvgAmount(orderedAmountToday / orderedGoodsNumToday);
        } else {
//            vo.setOrderItemAvgAmount(-1.0);
        }
        // 下单客单价（元/人） 客服促成下单金额/下单人数
        if (orderedNumToday != 0) {
            vag.setOrderedGuestAvgPrice(orderedAmountToday / orderedNumToday);
        } else {
//            vo.setOrderedGuestAvgPrice(-1.0);
        }
        // 下单客件数（件/人） 客服促成下单件数/下单人数
        if (orderedNumToday != 0) {
            vag.setOrderedGuestAvgAmount(orderedGoodsNumToday * 1.0 / orderedNumToday);
        } else {
//            vo.setOrderedGuestAvgAmount(-1.0);
        }
    }


    /**
     * 转化率
     */
    private void computerConversion(CsPerformanceVo vo, CsPerformanceVoAvg vag) {
        // 询单→当日下单人数
        final Integer orderedNumToday = BaseUtils.getNonNull(vo.getOrderedNumToday());
        // 询单人数
        final Integer enquiryNum = BaseUtils.getNonNull(vo.getEnquiryNum());
        // 当日咨询，当日下单，当日或次日付款的人数
        final Integer paidNumTodayNext = BaseUtils.getNonNull(vo.getPaidNumTodayNext());
        // 当日咨询，最终落实付款的人数
        final Integer paidNumFinal = BaseUtils.getNonNull(vo.getPaidNumFinal());
        // 当日咨询 最终落实下单的人数
        final Integer orderedNumFinal = BaseUtils.getNonNull(vo.getOrderedNumFinal());
        // 当日询单 当天落实下单 并且 付了款的人数
        final Integer toOrderedPaidNumToday = BaseUtils.getNonNull(vo.getToOrderedPaidNumToday());
        // 最终出库人数
        final Integer outStockOrderBuyerNumFinal = BaseUtils.getNonNull(vo.getOutStockOrderBuyerNumFinal());
        // 下单人数
        final Integer toOrderedNum = BaseUtils.getNonNull(vo.getToOrderedNum());

        // 最终付款人数
        final Integer toOrderedPaidNumFinal = BaseUtils.getNonNull(vo.getToOrderedPaidNumFinal());

        // 询单→次日付款转化率 询单→次日付款转化率=当日或次日付款人数/询单人数（当前询单有效时长配置为*天，该数据须延迟*天统计，当前可查看*年*月*日（含）的数据）
        if (enquiryNum != 0) {
            vag.setQueryToTomorrow(paidNumTodayNext * 1.0 / enquiryNum);
        } else {
//            vag.setPersonalOutStockAmountPercent(-1.0);
        }
        // 询单→最终付款转化率
        if (enquiryNum != 0) {
            vag.setQueryToFinalPaid(paidNumFinal * 1.0 / enquiryNum);
        } else {
//            vag.setQueryToFinalPaid(-1.0);
        }
        // 询单→当日下单转化率  orderedNumToday
        if (enquiryNum != 0) {
            vag.setQueryToOrderedToday(orderedNumToday * 1.0 / enquiryNum);
        } else {
//            vag.setQueryToOrderedToday(-1.0);
        }
        // 询单→最终下单转化率 orderedNumFinal
        if (enquiryNum != 0) {
            vag.setQueryToFinalOrdered(orderedNumFinal * 1.0 / enquiryNum);
        } else {
//            vag.setQueryToFinalPaid(-1.0);
        }
        // 询单→当日下单转化率  当日下单人数/询单人数 orderedNumToday/enquiryNum
        if (enquiryNum != 0) {
            vag.setQueryToOrderedToday(orderedNumToday * 1.0 / enquiryNum);
        } else {
//            vag.setQueryToOrderedToday(-1.0);
        }
        // 询单→最终下单转化率 最终下单人数/询单人数  orderedNumFinal/enquiryNum
        if (enquiryNum != 0) {
            vag.setQueryToFinalOrdered(orderedNumFinal * 1.0 / enquiryNum);
        } else {
//            vag.setQueryToFinalOrdered(-1.0);
        }
        // 下单→当日付款转化率 当日付款人数/下单人数  paidNumToday/orderedNumToday
        if (toOrderedNum != 0) {
            vag.setOrderedToPaid(toOrderedPaidNumToday * 1.0 / toOrderedNum);
        } else {
//            vag.setOrderedToPaid(-1.0);
        }
        // 下单→最终付款转化率 orderedToPaidFinal 最终付款人数/下单人数  paidNumFinal/orderedNumToday
        if (toOrderedNum != 0) {
            vag.setOrderedToPaidFinal(toOrderedPaidNumFinal * 1.0 / toOrderedNum);
        } else {
//            vag.setOrderedToPaidFinal(-1.0);
        }
        // 询单→出库转化率
        if (enquiryNum != 0) {
            vag.setQueryToOutStock(outStockOrderBuyerNumFinal * 1.0 / enquiryNum);
        } else {
//            vag.setQueryToOutStock(-1.0);
        }

    }


    /**
     * 工作量的特殊计算
     */
    private void workMeasureComputer(CsPerformanceVo vo, CsPerformanceVoAvg vag) {
        /*回复率 答问比*/
        final Integer custChatNum = BaseUtils.getNonNull(vo.getCustChatNum());
        //客服消息数
        final Integer csChatNum = BaseUtils.getNonNull(vo.getCsChatNum());
        //答问比=客服消息数/顾客消息数 cs_chat_num/cust_chat_num
        vag.setAnswerRatio(custChatNum == 0 ? 0D : csChatNum * 1.0 / custChatNum);
        // 接待量
        final Integer receiveSessionNum = BaseUtils.getNonNull(vo.getReceiveSessionNum());
        // 未回复量
        final Integer nonReplySessionNum = BaseUtils.getNonNull(vo.getNonReplySessionNum());
        //回复率 （接待量-未回复量）/接待量
        vag.setResponseRate(receiveSessionNum <= 0 ? 0 : (receiveSessionNum - nonReplySessionNum) * 1.0 / receiveSessionNum);
        //        留言接待量
        final Integer leaveMsgReceiveSessionNum = BaseUtils.getNonNull(vo.getLeaveMsgReceiveSessionNum());
        //留言分配量
        final Integer leaveMsgSessionNum = BaseUtils.getNonNull(vo.getLeaveMsgSessionNum());
//        留言响应率=留言接待量／留言分配量
        vag.setLeaveMsgResponseRate(leaveMsgSessionNum == 0 ? 0 : leaveMsgReceiveSessionNum * 1.0 / leaveMsgSessionNum);
//      快速应答的会话
        final Integer avgRespInQuickTime = BaseUtils.getNonNull(vo.getAvgRespInQuickTime());
//        接待的会话
        final Integer sessionNum = BaseUtils.getNonNull(vo.getSessionNum());
        //快速应答率 释义：（接待量-未回复）/接待量的占比
        int calSessionNum = sessionNum - nonReplySessionNum - leaveMsgReceiveSessionNum;
        vag.setRapidAnswerRate(calSessionNum <= 0 ? 0 : avgRespInQuickTime * 1.0 / calSessionNum);
        if (vag.getRapidAnswerRate() > 1) vag.setRapidAnswerRate(1.0);
        //平均回复消息数=客服消息数／接待量
        vag.setAvgCsMsgSessionNum(receiveSessionNum == 0 ? 0.0 : csChatNum * 1.0 / receiveSessionNum);

        // 首次平均响应：本客服对顾客第一次回复用时的平均值 （基于接待量，判定的规则支持自定义，可点此设置）
        final Double respTimeFirstCount = BaseUtils.getNonNull(vo.getRespTimeFirstCount());
        vag.setAvgRespTimeFirst(calSessionNum <= 0 ? 0 : respTimeFirstCount / calSessionNum);
        // 平均响应时间：本客服回复消息与顾客消息之间时间差的平均值（基于接待量，判定的规则支持自定义，可点此设置）
        final Double respTimeCount = BaseUtils.getNonNull(vo.getRespTimeCount());
//        回合数
        final Integer chatRoundNum = BaseUtils.getNonNull(vo.getChatRoundNum());
        vag.setAvgRespTime(chatRoundNum <= 0 ? 0 : respTimeCount / chatRoundNum);
        final Double sessionDurationTimeCount = vo.getSessionDurationTimeCount();
        // 平均会话时长：本客服平均每通会话从会话建立到会话关闭的时间间隔。会话时长=会话关闭时间-会话开始时间（包含10分钟会话等待时间，留言接待的不计入）
        vag.setAvgSessionDurationTime(sessionDurationTimeCount <= 0 ? 0 : sessionDurationTimeCount / sessionNum);
    }

    /**
     * 退款的特殊计算
     *
     * @param vo
     */
    private void refundDayComputer(CsPerformanceVo vo, CsPerformanceVoAvg vag) {
        // 退款率
        Integer saleGoodsNum = vo.getSaleGoodsNum();
        if (saleGoodsNum != null && saleGoodsNum != 0) {
            vag.setRefundPercent(vo.getCompletedRefundProductNum() * 1.0 / saleGoodsNum);
        } else {
//            vo.setRefundPercent(-1.0);
        }
    }


    /**
     * 满意率特殊计算
     *
     * @param vo
     */
    private void satisfactionProportionComputer(CsPerformanceVo vo, CsPerformanceVoAvg vag) {
        // 评价量
        final Integer evalReplyNum = vo.getEvalReplyNum();
        // 接待量
        final Integer receiveSessionNum = vo.getReceiveSessionNum();
        // 评价率
        vag.setEvaluationRate(receiveSessionNum == 0 ? 0 : 1.0 * evalReplyNum / receiveSessionNum);
        // 满意率
        vag.setSatisfactionRate(evalReplyNum == 0 ? 0 : (vo.getVerySatisfiedNum() + vo.getSatisfiedNum()) * 1.0 / evalReplyNum);
        // 邀评率
        vag.setInviteRate(receiveSessionNum == 0 ? 0 : vo.getEvalSendNum() * 1.0 / receiveSessionNum);

    }


    /*================================================================================================*/
    /*================================================================================================*/


    private void clear() {
        context.remove();
        cache.remove();
    }

    private static class EntryService {

        private Long shopId;

        private String csNick;

        private Date date;

        static EntryService of(Long shopId, String csNick, Date date) {
            if (context.get().isCs()) {
                return new EntryService(shopId, csNick, null);
            }
            return new EntryService(shopId, csNick, date);
        }

        private EntryService(Long shopId, String csNick, Date date) {
            this.shopId = shopId;
            this.csNick = csNick;
            this.date = date;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            EntryService that = (EntryService) o;
            return Objects.equals(shopId, that.shopId) &&
                    Objects.equals(csNick, that.csNick) &&
                    (
                            date == that.date ||
                                    (date != null && DateUtils.truncate(date, DAY_OF_MONTH).equals(DateUtils.truncate(that.date, DAY_OF_MONTH)))
                    );
        }

        @Override
        public int hashCode() {
            return Objects.hash(shopId, csNick, date);
        }
    }

    @SuppressWarnings({"unchecked", "SpellCheckingInspection"})
    public static class ContextService<K, V> extends BaseUtils.BaseMap<K, V> {

        ContextService() {
            super(64);
        }


        /**
         * 存储查询出来的数据
         */
        private Set<String> searchsNick;

        <T> void addAll(List<T> vals, Function<T, String> function) {
            // 必须是nick维度
            if (!isCs() || org.springframework.util.CollectionUtils.isEmpty(vals)) return;
            for (T val : vals) {
                add(function.apply(val));
            }
        }

        void add(String nick) {
            if (searchsNick == null) {
                searchsNick = new HashSet<>();
            }
            searchsNick.add(nick);
        }

        boolean containers(String nicks) {
            if (searchsNick == null) {
                return false;
            }
            return searchsNick.contains(nicks);
        }

        Set<String> getSearchsNick() {
            return searchsNick;
        }


        /**
         * 计算客单价
         *
         * @return
         */
        Boolean computerGuestAvg() {
            V custAvg = get("custAvg");
            if (custAvg == null) {
                return false;
            }
            return (Boolean) custAvg;
        }

        void isComputerGuestAvg(Boolean flag) {
            put((K) "custAvg", (V) flag);
        }

        /**
         * 计算转化率
         *
         * @return
         */
        Boolean computerConversion() {
            V convertion = get("convertion");
            if (convertion == null) {
                return false;
            }
            return (Boolean) convertion;
        }

        void isComputerConversion(Boolean flag) {
            put((K) "convertion", (V) flag);
        }

        /**
         * 计算落实付款数据
         *
         * @return
         */
        Boolean workablePay() {
            V workablePay = get("workablePay");
            if (workablePay == null) {
                return false;
            }
            return (Boolean) workablePay;
        }

        void isWorkablePay(Boolean flag) {
            put((K) "workablePay", (V) flag);
        }

        /**
         * 满意率
         *
         * @return
         */
        Boolean satisfactionProportion() {
            V workablePay = get("satisfactionProportion");
            if (workablePay == null) {
                return false;
            }
            return (Boolean) workablePay;
        }

        void isSatisfactionProportion(Boolean flag) {
            put((K) "satisfactionProportion", (V) flag);
        }

        /**
         * 计算工作量
         *
         * @return
         */
        Boolean workMeasure() {
            V workMeasure = get("workMeasure");
            if (workMeasure == null) {
                return false;
            }
            return (Boolean) workMeasure;
        }

        void isWorkMeasure(Boolean flag) {
            put((K) "workMeasure", (V) flag);
        }

        /**
         * 值班记录
         *
         * @return
         */
        Boolean dutyHistory() {
            V dutyHistory = get("dutyHistory");
            if (dutyHistory == null) {
                return false;
            }
            return (Boolean) dutyHistory;
        }

        void isDutyHistory(Boolean flag) {
            put((K) "dutyHistory", (V) flag);
        }

        /**
         * 协助服务
         *
         * @return
         */
        Boolean helpingService() {
            V helpingService = get("helpingService");
            if (helpingService == null) {
                return false;
            }
            return (Boolean) helpingService;
        }

        void isHelpingService(Boolean flag) {
            put((K) "helpingService", (V) flag);
        }

        /**
         * 流失数据
         *
         * @return
         */
        Boolean lossRecord() {
            V helpingService = get("lossRecord");
            if (helpingService == null) {
                return false;
            }
            return (Boolean) helpingService;
        }

        void islossRecord(Boolean flag) {
            put((K) "lossRecord", (V) flag);
        }

        /**
         * 中差评
         *
         * @return
         */
        Boolean middleBadappraised() {
            V helpingService = get("middleBadappraised");
            if (helpingService == null) {
                return false;
            }
            return (Boolean) helpingService;
        }

        void isMiddleBadappraised(Boolean flag) {
            put((K) "middleBadappraised", (V) flag);
        }

        /**
         * 分时接待
         *
         * @return
         */
        Boolean receiveSessionNumHourly() {
            V helpingService = get("receiveSessionNumHourly");
            if (helpingService == null) {
                return false;
            }
            return (Boolean) helpingService;
        }

        void isReceiveSessionNumHourly(Boolean flag) {
            put((K) "receiveSessionNumHourly", (V) flag);
        }

        /**
         * 退款数据
         *
         * @return
         */
        Boolean refundDay() {
            V refundDay = get("refundDay");
            if (refundDay == null) {
                return false;
            }
            return (Boolean) refundDay;
        }

        void isRefundDay(Boolean flag) {
            put((K) "refundDay", (V) flag);
        }

        /**
         * 获取绩效初始化模板，第一次调用一定要在
         * 之后
         *
         * @return
         */
        Map<String, Map<Object, Object>> getPerformanceValue() {
            V performance = get("performance");
            if (Objects.isNull(performance)) {
                Date startDate = getDate((K) "startDate");
                Date endDate = getDate((K) "endDate");
                Set<String> nicks = getNicks();
                Assert.notNull(startDate, "startDate not be null");
                Assert.notNull(endDate, "endDate not be null");
                // 结果集
                Map<String, Map<Object, Object>> stringMapHashMap = new LinkedHashMap<>(32);
                Map<Object, Object> value;
                if (isDate()) {
                    value = initPerformanceVo(DateUtils.splitDate(startDate, endDate), (Set<Date>) get("filterDates"), (String) nicks.toArray()[0]);
                } else if (isCs()) {
                    Map<String, String> nickWithSimpleName = (Map<String, String>) get("nickWithSimpleName");
                    value = initPerformanceVo(nickWithSimpleName);
                } else {
                    throw new RuntimeException(" 当前查询类型错误 ");
                }
                stringMapHashMap.put("result", value);
                // 平均维度
                Map<Object, Object> avg = new HashMap<>(4);
                avg.put("avg", getFilledAvg());
                stringMapHashMap.put("avg", avg);
                // 汇总维度
                Map<Object, Object> count = new HashMap<>(4);
                count.put("count", getFilled());
                stringMapHashMap.put("count", count);
                // 加入map
                V performanceMap = (V) stringMapHashMap;
                put((K) "performance", performanceMap);
                performance = performanceMap;
            }
            return (Map<String, Map<Object, Object>>) performance;
        }

        private Map<Object, Object> initPerformanceVo(Map<String, String> nicks) {
            Map<Object, Object> res = new LinkedHashMap<>();
            for (Map.Entry<String, String> nick : nicks.entrySet()) {
                CsPerformanceVo csPerformanceVo = getFilled();
                final String nickString = nick.getKey();
                csPerformanceVo.setCsNick(nickString);
                final String simpleName = nick.getValue();
                csPerformanceVo.setSimpleName(simpleName == null ? nickString : simpleName);
                res.put(nickString, csPerformanceVo);
            }
            return res;
        }

        private Map<Object, Object> initPerformanceVo(List<Date> splitDate, Set<Date> exclude, String nick) {
            boolean excludeExists = CollectionUtils.isNotEmpty(exclude);
            Map<Object, Object> res = new LinkedHashMap<>();
            for (Date date : splitDate) {
                if (excludeExists && exclude.contains(date)) {
                    continue;
                }
                CsPerformanceVo csPerformanceVo = getFilled();
                csPerformanceVo.setDate(date);
                csPerformanceVo.setCsNick(nick);
                res.put(date, csPerformanceVo);
            }
            return res;
        }

        Set<String> getNicks() {
            V v = get("nicks");
            return (Set<String>) v;
        }

        /**
         * 日期维度
         */
        boolean isDate() {
            return "date".equals(get("dimension"));
        }

        /**
         * 客服维度
         */
        boolean isCs() {
            return "cs_nick".equals(get("dimension"));
        }


    }

    private static abstract class Interceptor<O, I> {
        abstract O interceptor(O o);

        abstract I processAvg(I i);
    }

    private static class Chain<O, I> {
        private List<Interceptor<O, I>> interceptors;

        public Chain(List<Interceptor<O, I>> interceptors) {
            this.interceptors = interceptors;
        }

        public O process(O o, Predicate<O> test) {
            O res = o;
            if (test.test(o)) {
                for (Interceptor<O, I> interceptor : interceptors) {
                    o = interceptor.interceptor(o);
                }
            }
            return res;
        }

        public I processAvg(I i, Predicate<I> test) {
            I res = i;
            if (test.test(i)) {
                for (Interceptor<O, I> interceptor : interceptors) {
                    i = interceptor.processAvg(i);
                }
            }
            return res;
        }
    }

}
