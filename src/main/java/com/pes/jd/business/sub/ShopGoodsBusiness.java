package com.pes.jd.business.sub;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopGoodNameDTO;
import com.pes.jd.model.DTO.ShopGoodsDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuDTO;
import com.pes.jd.model.Param.ShopCommonParam;

import java.util.List;
import java.util.Map;

public interface ShopGoodsBusiness {
	List<ShopGoodsDTO> selectShopGoodsByShopId(ShopDTO shop,
                                               Long categoryId,
                                               String name,
                                               Integer status);


	List<ShopGoodNameDTO> selectShopGoodsByShopIdByWareIds(ShopCommonParam shop, List<Long> wareIds);


	List<ShopGoodNameDTO> selectShopGoodsByCategoryIdBySkuNameByStatus(ShopCommonParam shop, List<Long> categoryLst, String wareName, String status, List<Long> wareIdLst, Integer pageNum, Integer pageSize);

    IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(IPage<ShopGoodsSkuDTO> goodsSkuLstPage, Long shopId, String tableName, String joinTableName, List<Long> categoryLst, String skuName, String status, List<Long> excludeWareIds, List<Long> includeWareIds, List<Long> topSku, String propertity, String sortDirection);

    Map<String, Object> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(ShopCommonParam shop, String categoryId, Long level, String skuName, String status, List<Long> skuIdLst, Integer pageSize, Integer pageNum);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByWareIdsLst(ShopCommonParam shop, List<Long> skuIdLst);
}
