package com.pes.jd.business.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.business.sub.CsWarningBusiness;
import com.pes.jd.mapper.sub.CsWarningMapper;
import com.pes.jd.model.DO.CsWarningDO;
import com.pes.jd.model.DTO.CsWarningDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class CsWarningBusinessImpl implements CsWarningBusiness {
	@Autowired
	private CsWarningMapper csWarningMapper;
	
	@Override
	public int batchInsertCsWarning(JobShopDTO shop, Date date,List<CsWarningDO> csWarningList) {
		if(csWarningList.isEmpty()){
			return 0;
		}
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), date, TableEnum.PES_CS_WARNING.getName());
		return csWarningMapper.batchInsertCsWarning(shop.getShopId(),csWarningList,tableName);
	}

	@Override
	public int batchDeleteCsWarningByDateByType(JobShopDTO shop, Date startDate, Date endDate,Byte type) {
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), startDate,TableEnum.PES_CS_WARNING.getName());
		return csWarningMapper.batchDeleteCsWarningByDateByType(shop.getShopId(),startDate,endDate,type,tableName);
	}

	@Override
	public int batchDeleteCsWarningByDateByTypeAndCs(JobShopDTO shop, Date startDate, Date endDate, Byte type, String csNick) {
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), startDate,TableEnum.PES_CS_WARNING.getName());
		return csWarningMapper.batchDeleteCsWarningByDateByTypeAndCs(shop.getShopId(),startDate,endDate,type,tableName,csNick);
	}

    @Override
    public List<CsWarningDTO> selectCsWarnLstByDateAndCsLstAndWarnType(ShopQuery shop, Date startDate, Date endDate, List<String> csNick, String customer, Byte type, String keyword) {
		List<CsWarningDTO> csWarning = Lists.newArrayList();
		List<CsWarningDTO> csWarningDTOS;
		if (StringUtils.isNotBlank(keyword)) {
			keyword = keyword.trim();
		}

		List<CommonUtils.DateRangeParam> tableNamesOfYear = CommonUtils.getTableNamesOfYear(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_WARNING.getName());
		for (CommonUtils.DateRangeParam dateRangeParam : tableNamesOfYear) {
			String tableName = dateRangeParam.getTableName();
			csWarningDTOS = csWarningMapper.selectCsWarnLstByDateAndCsLstAndWarnType(shop.getShopId(), startDate, endDate, type, tableName, customer,csNick,keyword);
			if(CollectionUtils.isEmpty(csWarningDTOS)){
				continue;
			}
			csWarning.addAll(csWarningDTOS);
		}
		return csWarning;
	}
//
//	@Override
//	public List<CsWarningDO> selectCsSlowResponseWarningLst(JobShopDTO shop, List<CsDTO> csLst, Date date,
//			int type) {
//		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), "pes_cs_warning");
//		return csWarningMapper.selectCsSlowResponseWarningLst(shop.getShopId(),csLst,DateUtil.getStartTimeOfDate(date),DateUtil.getEndTimeOfDate(date),type,tableName);
//	}
//
//	@Override
//	public List<CsWarningDO> selectTotalWarnByShopIdAndDate(JobShopDTO shop, JobDateQuery jobDate, Date startDate) {
//		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), "pes_cs_warning");
//		return csWarningMapper.selectTotalWarnByShopIdAndDate(shop.getShopId(), tableName, startDate, jobDate.getEndDate());
//	}

}
  
