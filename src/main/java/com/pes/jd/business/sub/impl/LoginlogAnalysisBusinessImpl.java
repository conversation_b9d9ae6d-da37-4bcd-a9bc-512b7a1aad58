package com.pes.jd.business.sub.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.LoginlogAnalysisBusiness;
import com.pes.jd.dao.sub.CsDutyRecordDao;
import com.pes.jd.dao.sub.CsLoginlogDao;
import com.pes.jd.model.DO.CsDutyRecordDO;
import com.pes.jd.model.DO.CsLoginlog;
import com.pes.jd.model.DTO.CsDutyRecordDTO;
import com.pes.jd.model.DTO.ShopCsLoginLogDTO;
import com.pes.jd.model.JSON.FilterTimeJSON;
import com.pes.jd.model.Param.CsLoginlogParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.Query.DateQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.VO.CsOperateData;
import com.pes.jd.ms.domain.Data.shopdata.ShopCsDuty;
import com.pes.jd.util.BaseUtils;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 登录分析 - 业务类 ClassName:LoginlogAnalysisBusinessImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月25日 下午2:01:50 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Service
public class LoginlogAnalysisBusinessImpl implements LoginlogAnalysisBusiness {
	private static final Logger logger = LoggerFactory.getLogger(LoginlogAnalysisBusinessImpl.class);
	private SimpleDateFormat ymd_hms = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private SimpleDateFormat ymd = new SimpleDateFormat("yyyy-MM-dd");

	@Autowired
	private CsLoginlogDao csLoginlogDao;

	@Autowired
	private CsDutyRecordDao csDutyRecordDao;

	@Override
	public Map<String, Object> queryCsLoginOperateDetail(ShopCommonParam shop, Date startDate, Date endDate,
			UserQuery userQuery, Long delayTime) throws Exception {
		Map<String, Object> retMapData = new HashMap<String, Object>();

		long delayStartTime = startDate.getTime() + delayTime * 60 * 60 * 1000;// 如果业务天为3，开始时间推迟到凌晨3点
		long delayEndTime = endDate.getTime() + delayTime * 60 * 60 * 1000;// 如果业务天为3，结束时间推迟到第二天凌晨2:59:59点
		Date delayStartDate = DateFormatUtils.parseYMdHms(ymd_hms.format(delayStartTime));
		Date delayEndDate = DateFormatUtils.parseYMdHms(ymd_hms.format(delayEndTime));
		List<String> csNicks = new ArrayList<String>();
		csNicks.add(userQuery.getNick());

		// 根据店铺+nick（可为空）查询指定时间(推迟三小时)内的登录记录
		List<CsLoginlog> retLoginlogLst = csLoginlogDao.queryCsLoginlogByCsNicksByShopIdByDate(shop, csNicks,
				delayStartDate, delayEndDate);
		List<CsLoginlog> csLoginlogLst = skipUnknownLoginType(retLoginlogLst);
		CsOperateData csOperateData = new CsOperateData();
		csOperateData.setCsNick(csNicks.get(0));
		csOperateData.setCsSimpleNick(userQuery.getSimpleName());
		csOperateData.setDate(startDate);
		csOperateData.setCsLoginlogLst(csLoginlogLst);
		retMapData.put("csOperateData", csOperateData);
		return retMapData;
	}

	/**
	 * skipUnknownLoginType:(过滤未知type：5). <br/>
	 */
	private List<CsLoginlog> skipUnknownLoginType(List<CsLoginlog> retLoginlogLst) {
		List<CsLoginlog> csLonginlogLst = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(retLoginlogLst)) {
			for (CsLoginlog loginlog : retLoginlogLst) {
				if (loginlog.getType() == 5) {
					// type = 5 未知操作，过滤
					continue;
				}
				csLonginlogLst.add(loginlog);
			}
		}
		return csLonginlogLst;
	}

	/**
	 * TODO 简单描述该方法的实现功能（查询登录记录详情报表）.
	 */
	@Override
	public Map<String, Object> queryCsDutyRecordSituationDetail(ShopCommonParam shopQuery, CsLoginlogParam csLoginlogParam, Date startDate, Date endDate) throws Exception {
		logger.info("queryCsDutyRecordSituationDetail   start.");
		if (CollectionUtils.isEmpty(csLoginlogParam.getUserQueryLst())){
			return new HashMap<>();
		}
		UserQuery userQuery = csLoginlogParam.getUserQueryLst().get(0);
		Integer delayTime = csLoginlogParam.getDelayTime();
		String expotrUnit = csLoginlogParam.getExportUnit();
		String dateType = csLoginlogParam.getDateType();
		List<FilterTimeJSON> filterTimes = Lists.newArrayList();
		if(StringUtils.isNotBlank(csLoginlogParam.getFilterTime())){
			filterTimes = JacksonUtils.json2list(csLoginlogParam.getFilterTime(), FilterTimeJSON.class);
		}

		Map<String, Object> retMapData = new HashMap<>();
		Long shopId = shopQuery.getShopId();
		Long groupId = userQuery.getGroupId();
		String csNick = userQuery.getNick();
		String simpleName = userQuery.getSimpleName();

		List<String> csNicks = new ArrayList<>();
		csNicks.add(csNick);
		List<DateQuery> dateQueryByFilterTimes = DateUtil.getDateQueryByFilterTimes(startDate, endDate, filterTimes);

		// 剔除过滤时间段查询客服登录信息
		List<CsDutyRecordDO> queryCsDutyRecordList = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(dateQueryByFilterTimes)) {
			for (DateQuery dateQuery : dateQueryByFilterTimes) {
				List<CsDutyRecordDO> retCsDutyRecordList = csDutyRecordDao.queryCsDutyRecordByCsNicksByDate(shopQuery,
						csNicks, dateQuery.getStartDate(), dateQuery.getEndDate());
				if (CollectionUtils.isNotEmpty(retCsDutyRecordList)) {
					queryCsDutyRecordList.addAll(retCsDutyRecordList);
				}
			}
		}

		// 根据店铺+nick（可为空）查询指定时间(推迟三小时)内的登录记录
		CsDutyRecordDTO retTotalCsDutyRecordDTO = new CsDutyRecordDTO(shopId, groupId, csNick, simpleName);
		CsDutyRecordDTO retAvgCsDutyRecordDTO = new CsDutyRecordDTO(shopId, groupId, csNick, simpleName);

		List<CsDutyRecordDTO> csDutyRecordLst = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(queryCsDutyRecordList)) {
			if(dateType.equals("0")){
				//按月份
				SimpleDateFormat ym = new SimpleDateFormat("yyyy-MM");
				Map<String,List<CsDutyRecordDO>> monthMap = Maps.newHashMap();
				for (CsDutyRecordDO csDutyRecord : queryCsDutyRecordList) {
					String dateMonthStr = ym.format(csDutyRecord.getDate());
					if(monthMap.containsKey(dateMonthStr)){
						monthMap.get(dateMonthStr).add(csDutyRecord);
					}else{
						List<CsDutyRecordDO> monthLoginlogLst = Lists.newArrayList();
						monthLoginlogLst.add(csDutyRecord);
						monthMap.put(dateMonthStr, monthLoginlogLst);
					}
				}
				for (Entry<String,List<CsDutyRecordDO>> monthEntry : monthMap.entrySet()) {
					CsDutyRecordDTO csMonth = new CsDutyRecordDTO(shopId, groupId, csNick, monthEntry.getKey());
					calculateTotalCsDutyRecordForDetail(monthEntry.getValue(), csMonth, delayTime);
					csMonth.setDate(ym.parse(monthEntry.getKey()));
					csDutyRecordLst.add(csMonth);
				}
			}else{
				//按日期
				csDutyRecordLst = calculateCsDytuRecordForDetail(queryCsDutyRecordList,groupId,csNick,expotrUnit);
			}
			calculateTotalCsDutyRecordForDetail(queryCsDutyRecordList, retTotalCsDutyRecordDTO, delayTime);
			calculateAvgCsDutyRecordForDetail(queryCsDutyRecordList, retAvgCsDutyRecordDTO, delayTime);
		}
		// 返回格式问题：和客服绩效其他报表保持一致
		retMapData.put("result", csDutyRecordLst);
		retMapData.put("count", retTotalCsDutyRecordDTO);
		retMapData.put("avg", retAvgCsDutyRecordDTO);
		retMapData.put("timeType", expotrUnit);
		logger.info("queryCsDutyRecordSituationDetail   end.");
		return retMapData;
	}

	private List<CsDutyRecordDTO> calculateCsDytuRecordForDetail(List<CsDutyRecordDO> queryCsDutyRecordList, Long groupId, String csNick, String expotrUnit) {
		List<CsDutyRecordDTO> csDutyRecordLst = Lists.newArrayList();
		for (CsDutyRecordDO csDutyRecordDO : queryCsDutyRecordList) {
			CsDutyRecordDTO csDutyRecordDTO = new CsDutyRecordDTO(csDutyRecordDO.getShopId(), groupId, csNick,null);
			csDutyRecordDTO.setDate(csDutyRecordDO.getDate());
			csDutyRecordDTO.setCsSimpleNick(ymd.format(csDutyRecordDO.getDate()));
			csDutyRecordDTO.setFirstOnlineDateTime(csDutyRecordDO.getFirstOnlineDateTime());
			csDutyRecordDTO.setLastOfflineDateTime(csDutyRecordDO.getLastOfflineDateTime());
			csDutyRecordDTO.setLoginTimesNum(csDutyRecordDO.getLoginTimesNum() * 1.0);
			csDutyRecordDTO.setLoginDurationTime(csDutyRecordDO.getLoginDurationTime());
			csDutyRecordDTO.setRceiveDurationTime(csDutyRecordDO.getRceiveDurationTime());
			csDutyRecordDTO.setHangupDurationTime(csDutyRecordDO.getHangupDurationTime());
			csDutyRecordDTO.setOfflineDurationTime(csDutyRecordDO.getOfflineDurationTime());
			csDutyRecordDTO.setRceiveTimeRate(csDutyRecordDO.getLoginDurationTime() > 0
					? csDutyRecordDO.getRceiveDurationTime() * 1.0 / csDutyRecordDO.getLoginDurationTime() : 0.0);
			csDutyRecordLst.add(csDutyRecordDTO);
		}
		return csDutyRecordLst;
	}

	/**
	 * TODO 简单描述该方法的实现功能（查询登录记录总览报表）.
	 */
	@Override
	public Map<String, Object> queryCsDutyRecordSituation(ShopCommonParam shop, CsLoginlogParam csLoginlogParam, Date startDate, Date endDate, Boolean dutyRidCsSwitch) throws Exception {
		Long shopId = shop.getShopId();
		List<UserQuery> shopCs = csLoginlogParam.getUserQueryLst();
		Integer delayTime = csLoginlogParam.getDelayTime();
		String exportUnit = csLoginlogParam.getExportUnit();
		List<FilterTimeJSON> filterTimes = Lists.newArrayList();
		if(StringUtils.isNotBlank(csLoginlogParam.getFilterTime())){
			filterTimes = JacksonUtils.json2list(csLoginlogParam.getFilterTime(), FilterTimeJSON.class);
		}

		Map<String, Object> retMapData = new HashMap<String, Object>();
		List<CsDutyRecordDTO> csDutyRecordLst = Lists.newArrayList();
		CsDutyRecordDTO retTotalCsDutyRecord = new CsDutyRecordDTO();
		CsDutyRecordDTO retAvgCsDutyRecord = new CsDutyRecordDTO();
		List<String> csNicks = new ArrayList<String>();

		if (CollectionUtils.isEmpty(shopCs)) {
			retMapData.put("result", csDutyRecordLst);
			retMapData.put("count", retTotalCsDutyRecord);
			retMapData.put("avg", retAvgCsDutyRecord);
			retMapData.put("timeType", exportUnit);
			return retMapData;
		}

		for (UserQuery cs : shopCs) {
			if (!csNicks.contains(cs.getNick())) {
				csNicks.add(cs.getNick());
			}
		}

		List<DateQuery> queryTimes = DateUtil.getDateQueryByFilterTimes(startDate, endDate, filterTimes);

		// 根据店铺+nick（可为空）查询指定业务天内的登录记录
		List<CsDutyRecordDO> queryCsDutyRecordDOList = Lists.newArrayList();
		for (DateQuery queryTime : queryTimes) {
			List<CsDutyRecordDO> retCsDutyRecordList = csDutyRecordDao.queryCsDutyRecordByCsNicksByDate(shop, csNicks,
					queryTime.getStartDate(), queryTime.getEndDate());
			if (CollectionUtils.isNotEmpty(retCsDutyRecordList)) {
				queryCsDutyRecordDOList.addAll(retCsDutyRecordList);
			}
		}

		List<CsDutyRecordDTO> retCsDutyRecordList = Lists.newArrayList();
		Map<String, List<CsDutyRecordDO>> csDutyRecordMap = Maps.newHashMap();
		if (CollectionUtils.isNotEmpty(queryCsDutyRecordDOList)) {
			csDutyRecordMap = queryCsDutyRecordDOList.stream()
					.collect(Collectors.groupingBy(CsDutyRecordDO::getCsNick));
		}
		CsDutyRecordDTO csDutyRecord = null;
		for (String csNick : csNicks) {
			if (csDutyRecordMap.containsKey(csNick)) {
				csDutyRecord = calculateTotalCsDutyRecordForCsNick(csNick, shopId, csDutyRecordMap.get(csNick),
						delayTime,exportUnit);
				retCsDutyRecordList.add(csDutyRecord);
			}
			//数据库没有该客服的值班信息，则该客服在选择时间段内，可能被锁定了，job没跑该客服的数据，不做统计
		}
		if (CollectionUtils.isNotEmpty(retCsDutyRecordList)) {
			//-------------------值班记录剔除未上班客服开关时剔除未上班的客服  start
			if (dutyRidCsSwitch) {
				retCsDutyRecordList = retCsDutyRecordList.stream().filter(csDutyRecordDTO -> csDutyRecordDTO.getWorkDay() > 0).collect(Collectors.toList());
			}
			//-------------------值班记录剔除未上班客服开关时剔除未上班的客服  end

//			int shopCsSize = shopCs.size();//店铺客服数
			//只计算正常的客服
			int shopCsSize = retCsDutyRecordList.size();
			retTotalCsDutyRecord = calculateTotalCsDutyRecordForAllCsNick(retCsDutyRecordList, shopId, delayTime, exportUnit);
			retAvgCsDutyRecord = calculateAvgCsDutyRecordForAllCsNick(retCsDutyRecordList, retTotalCsDutyRecord, shopId, shopCsSize,
					delayTime, exportUnit);
			retTotalCsDutyRecord.setAvgFirstOnlineDateTime(null);
			retTotalCsDutyRecord.setAvgLastOfflineDateTime(null);
		}

		// 添加咚咚昵称,groupId,导出单位为分组时处理数据
		if (CollectionUtils.isNotEmpty(retCsDutyRecordList)) {
			for (CsDutyRecordDTO retCsDutyRecord : retCsDutyRecordList) {
				for (UserQuery cs : shopCs) {
					if (retCsDutyRecord.getCsNick().equals(cs.getNick())) {
						retCsDutyRecord.setGroupId(cs.getGroupId());
						if (cs.getSimpleName() != null) {
							retCsDutyRecord.setCsSimpleNick(cs.getSimpleName());
						} else {
							retCsDutyRecord.setCsSimpleNick(cs.getNick());
						}
						csDutyRecordLst.add(retCsDutyRecord);
						break;
					}
				}
			}
		}
		retMapData.put("result", csDutyRecordLst);
		retMapData.put("count", retTotalCsDutyRecord);
		retMapData.put("avg", retAvgCsDutyRecord);
		retMapData.put("timeType", exportUnit);
		return retMapData;
	}

	/**
	 * calculateAvgCsDutyRecordForAllCsNick:(计算所有客服登录记录总览报表"均值"记录). <br/>
	 *
	 * @param retCsDutyRecordList
	 * @param retTotalCsDutyRecord
	 * @param shopId
	 * @param shopCsSize
	 * @param delayTime 
	 * @param exportUnit 
	 * @return
	 * @throws Exception
	 * @since JDK 1.8
	 */
	private CsDutyRecordDTO calculateAvgCsDutyRecordForAllCsNick(List<CsDutyRecordDTO> retCsDutyRecordList,
			CsDutyRecordDTO retTotalCsDutyRecord, Long shopId, Integer shopCsSize, Integer delayTime, String exportUnit) throws Exception {
		int activeCsNum = 0;// 有效客服人数--求均值(现在时间均值是用有效客服算的，客服上班天数，登录次数，登录时间都是除以店铺所有客服)
		Date firstOnlineDateTime = null;// 最早登录时间
		Date lastOfflineDateTime = null;// 最晚登出时间
		Date avgFirstOnlineDateTime = null;// 平均的 平均最早登录时间
		Date avgLastOfflineDateTime = null;// 平均的 平均最晚登出时间

		Long totalAvgFirstOnlineDateTime = 0L;
		Long totalAvgLastOnlineDateTime = 0L;
		for (CsDutyRecordDTO csDutyRecord : retCsDutyRecordList) {
			if (csDutyRecord.getWorkDay() <= 0) {
				continue;
			}
			activeCsNum++;
			// 最早上线时间
			if (csDutyRecord.getAvgFirstOnlineDateTime() != null) {
				Date nowDelayDate = ymd_hms.parse(
						ymd_hms.format(csDutyRecord.getAvgFirstOnlineDateTime().getTime() - delayTime * 60 * 60 * 1000));
				totalAvgFirstOnlineDateTime += nowDelayDate.getTime()
						- DateUtil.getStartTimeOfDate(nowDelayDate).getTime();
				Date avgFirstLoginDelayDate = null;
				if (firstOnlineDateTime != null) {
					avgFirstLoginDelayDate = ymd_hms.parse(ymd_hms.format(firstOnlineDateTime.getTime() - delayTime * 60 * 60 * 1000));
				}
				if (firstOnlineDateTime == null) {
					firstOnlineDateTime = csDutyRecord.getAvgFirstOnlineDateTime();
				} else {
					if (DateUtil.compareTimeOfDay(nowDelayDate, avgFirstLoginDelayDate)) {
						firstOnlineDateTime = csDutyRecord.getAvgFirstOnlineDateTime();
					}
				}
			}
			// 最晚下线时间
			if (csDutyRecord.getAvgLastOfflineDateTime() != null) {
				Date nowDelayDate = ymd_hms.parse(ymd_hms.format(csDutyRecord.getAvgLastOfflineDateTime().getTime() - delayTime * 60 * 60 * 1000));
				totalAvgLastOnlineDateTime += nowDelayDate.getTime()
						- DateUtil.getStartTimeOfDate(nowDelayDate).getTime();
				Date lastDelayDate = null;
				if (lastOfflineDateTime != null) {
					lastDelayDate = ymd_hms.parse(ymd_hms.format(lastOfflineDateTime.getTime() - delayTime * 60 * 60 * 1000));
				}
				if (lastOfflineDateTime == null) {
					lastOfflineDateTime = csDutyRecord.getAvgLastOfflineDateTime();
				} else {
					if (DateUtil.compareTimeOfDay(lastDelayDate, nowDelayDate)) {
						lastOfflineDateTime = csDutyRecord.getAvgLastOfflineDateTime();
					}
				}
			}
		}

		// 总览报表中，四个上下线时间以及四个日均值，汇总和均值是一致的，此处不做计算，方法外直接赋值汇总对象的
		CsDutyRecordDTO avgCsDutyRecord = new CsDutyRecordDTO(shopId, null, null, "均值");
		if (activeCsNum > 0 && shopCsSize >0) {
			avgFirstOnlineDateTime = ymd_hms.parse(ymd_hms.format(DateUtil.getStartTimeOfDate(firstOnlineDateTime).getTime()
							+ totalAvgFirstOnlineDateTime / activeCsNum + delayTime * 60 * 60 * 1000));
			avgLastOfflineDateTime = ymd_hms.parse(ymd_hms.format(DateUtil.getStartTimeOfDate(lastOfflineDateTime).getTime()
							+ totalAvgLastOnlineDateTime / activeCsNum + delayTime * 60 * 60 * 1000));
			avgCsDutyRecord.setWorkDay(retTotalCsDutyRecord.getWorkDay() / shopCsSize);
			avgCsDutyRecord.setLoginDurationTime(retTotalCsDutyRecord.getLoginDurationTime() / shopCsSize);
			avgCsDutyRecord.setRceiveDurationTime(retTotalCsDutyRecord.getRceiveDurationTime() / shopCsSize);
			avgCsDutyRecord.setHangupDurationTime(retTotalCsDutyRecord.getHangupDurationTime() / shopCsSize);
			avgCsDutyRecord.setOfflineDurationTime(retTotalCsDutyRecord.getOfflineDurationTime() / shopCsSize);
			avgCsDutyRecord.setLoginTimesNum(retTotalCsDutyRecord.getLoginTimesNum() / shopCsSize);
			avgCsDutyRecord.setRceiveTimeRate(retTotalCsDutyRecord.getRceiveTimeRate());

			// 上下线时间
			avgCsDutyRecord.setAvgFirstOnlineDateTime(avgFirstOnlineDateTime);
			avgCsDutyRecord.setAvgLastOfflineDateTime(avgLastOfflineDateTime);
			avgCsDutyRecord.setFirstOnlineDateTime(retTotalCsDutyRecord.getAvgFirstOnlineDateTime());
			avgCsDutyRecord.setLastOfflineDateTime(retTotalCsDutyRecord.getAvgLastOfflineDateTime());
			// 日均值
			avgCsDutyRecord.setAvgHangupDurationTime(retTotalCsDutyRecord.getAvgHangupDurationTime());
			avgCsDutyRecord.setAvgLoginDurationTime(retTotalCsDutyRecord.getAvgLoginDurationTime());
			avgCsDutyRecord.setAvgLoginTimesNum(retTotalCsDutyRecord.getAvgLoginTimesNum());
			avgCsDutyRecord.setAvgRceiveDurationTime(retTotalCsDutyRecord.getAvgRceiveDurationTime());
			//挂起次数
			avgCsDutyRecord.setSuspendNum(retTotalCsDutyRecord.getSuspendNum() / shopCsSize);
		}
		return avgCsDutyRecord;
	}

	/**
	 * calculateTotalCsDutyRecordForAllCsNick:(计算所有客服登录记录总览报表"汇总"记录). <br/>
	 *
	 * @param retCsDutyRecordList
	 * @param shopId
	 * @param delayTime
	 * @param delayTime 
	 * @param exportUnit 
	 * @return
	 * @throws Exception
	 * @since JDK 1.8
	 */
	private CsDutyRecordDTO calculateTotalCsDutyRecordForAllCsNick(List<CsDutyRecordDTO> retCsDutyRecordList,
			Long shopId, Integer delayTime, String exportUnit) throws Exception {
		Date firstOnlineDateTime = null;// 最早登录时间
		Date lastOfflineDateTime = null;// 最晚登出时间
		Date avgFirstOnlineDateTime = null;// 平均最早登录时间
		Date avgLastOfflineDateTime = null;// 平均最晚登出时间
		Long loginDurationTime = 0L;// 登录时长
		Long rceiveDurationTime = 0L;// 接待时长
		Long hangupDurationTime = 0L;// 挂起时长
		Long offlineDurationTime = 0L;
		int loginTimesNum = 0;
		int workDay = 0;
		int loginCsNum = 0;
		int suspendNum = 0;//挂起次数
		Long totalFirstOnlineDateTime = 0L;
		Long totalLastOnlineDateTime = 0L;
		for (CsDutyRecordDTO csDutyRecordDTO : retCsDutyRecordList) {
			if (csDutyRecordDTO.getWorkDay() <= 0) {
				continue;
			}
			loginCsNum ++;
			workDay += csDutyRecordDTO.getWorkDay();
			loginTimesNum += csDutyRecordDTO.getLoginTimesNum();
			loginDurationTime += csDutyRecordDTO.getLoginDurationTime();
			rceiveDurationTime += csDutyRecordDTO.getRceiveDurationTime();
			hangupDurationTime += csDutyRecordDTO.getHangupDurationTime();
			offlineDurationTime += csDutyRecordDTO.getOfflineDurationTime();
			suspendNum += BaseUtils.getNonNull(csDutyRecordDTO.getSuspendNum());
			// 最早上线时间
			if (csDutyRecordDTO.getFirstOnlineDateTime() != null) {
				Date nowDelayDate = ymd_hms.parse(
						ymd_hms.format(csDutyRecordDTO.getFirstOnlineDateTime().getTime() - delayTime * 60 * 60 * 1000));
				totalFirstOnlineDateTime += nowDelayDate.getTime()
						- DateUtil.getStartTimeOfDate(nowDelayDate).getTime();
				Date firstLoginDelayDate = null;
				if (firstOnlineDateTime != null) {
					firstLoginDelayDate = ymd_hms.parse(ymd_hms.format(firstOnlineDateTime.getTime() - delayTime * 60 * 60 * 1000));
				}
				if (firstOnlineDateTime == null) {
					firstOnlineDateTime = csDutyRecordDTO.getFirstOnlineDateTime();
				} else {
					if (DateUtil.compareTimeOfDay(nowDelayDate, firstLoginDelayDate)) {
						firstOnlineDateTime = csDutyRecordDTO.getFirstOnlineDateTime();
					}
				}
			}
			// 最晚下线时间
			if (csDutyRecordDTO.getLastOfflineDateTime() != null) {
				Date nowDelayDate = ymd_hms.parse(
						ymd_hms.format(csDutyRecordDTO.getLastOfflineDateTime().getTime() - delayTime * 60 * 60 * 1000));
				totalLastOnlineDateTime += nowDelayDate.getTime() - DateUtil.getStartTimeOfDate(nowDelayDate).getTime();
				Date lastDelayDate = null;
				if (lastOfflineDateTime != null) {
					lastDelayDate = ymd_hms.parse(ymd_hms.format(lastOfflineDateTime.getTime() - delayTime * 60 * 60 * 1000));
				}
				if (lastOfflineDateTime == null) {
					lastOfflineDateTime = csDutyRecordDTO.getLastOfflineDateTime();
				} else {
					if (DateUtil.compareTimeOfDay(lastDelayDate, nowDelayDate)) {
						lastOfflineDateTime = csDutyRecordDTO.getLastOfflineDateTime();
					}
				}
			}
		}
		if (loginCsNum > 0) {
			//平均最早上线时间除以客服个数
			avgFirstOnlineDateTime = ymd_hms.parse(ymd_hms.format(DateUtil.getStartTimeOfDate(firstOnlineDateTime).getTime()
							+ totalFirstOnlineDateTime / loginCsNum + delayTime * 60 * 60 * 1000));
			avgLastOfflineDateTime = ymd_hms.parse(ymd_hms.format(DateUtil.getStartTimeOfDate(lastOfflineDateTime).getTime()
							+ totalLastOnlineDateTime / loginCsNum + delayTime * 60 * 60 * 1000));
		}
		CsDutyRecordDTO totalCsDutyRecordDTO = new CsDutyRecordDTO(shopId, null, null, "汇总");
		totalCsDutyRecordDTO.setWorkDay(workDay * 1.0);
		totalCsDutyRecordDTO.setFirstOnlineDateTime(firstOnlineDateTime);
		totalCsDutyRecordDTO.setLastOfflineDateTime(lastOfflineDateTime);
		totalCsDutyRecordDTO.setAvgFirstOnlineDateTime(avgFirstOnlineDateTime);
		totalCsDutyRecordDTO.setAvgLastOfflineDateTime(avgLastOfflineDateTime);
		totalCsDutyRecordDTO.setLoginDurationTime(loginDurationTime);
		//日均登录时长除以所有客服的登录天数之和
		totalCsDutyRecordDTO.setAvgLoginDurationTime(workDay>0?Long.valueOf(loginDurationTime/workDay):0);
		totalCsDutyRecordDTO.setRceiveDurationTime(rceiveDurationTime);
		totalCsDutyRecordDTO.setAvgRceiveDurationTime(workDay>0?Long.valueOf(rceiveDurationTime/workDay):0);
		totalCsDutyRecordDTO.setHangupDurationTime(hangupDurationTime);
		totalCsDutyRecordDTO.setAvgHangupDurationTime(workDay>0?Long.valueOf(hangupDurationTime/workDay):0);
		totalCsDutyRecordDTO.setLoginTimesNum(loginTimesNum * 1.0);
		totalCsDutyRecordDTO.setAvgLoginTimesNum(workDay > 0 ? (loginTimesNum * 1.0 / workDay) : 0);
		totalCsDutyRecordDTO.setOfflineDurationTime(offlineDurationTime);
		totalCsDutyRecordDTO.setSuspendNum(suspendNum);
		return totalCsDutyRecordDTO;
	}

	/**
	 * calculateTotalCsDutyRecordForCsNick:(计算单个客服登录记录指定天内的汇总数据). <br/>
	 *
	 * @param csNick
	 * @param shopId
	 * @param csDutyRecordList
	 * @param delayTime
	 * @param exportUnit 
	 * @return
	 * @throws Exception
	 * @since JDK 1.8
	 */
	private CsDutyRecordDTO calculateTotalCsDutyRecordForCsNick(String csNick, Long shopId,
			List<CsDutyRecordDO> csDutyRecordList, Integer delayTime, String exportUnit) throws Exception {
		Date firstOnlineDateTime = null;// 最早登录时间
		Date lastOfflineDateTime = null;// 最晚登出时间
		Date avgFirstOnlineDateTime = null;// 平均最早登录时间
		Date avgLastOfflineDateTime = null;// 平均最晚登出时间
		Long loginDurationTime = 0L;// 登录时长
		Long rceiveDurationTime = 0L;// 接待时长
		Long hangupDurationTime = 0L;// 挂起时长
		Long offlineDurationTime = 0L;// 上班期间离线时长
		int loginTimesNum = 0;
		int workDay = 0;
		Long totalFirstOnlineDateTime = 0L;
		Long totalLastOnlineDateTime = 0L;

		int suspendNum = 0;

		if (CollectionUtils.isNotEmpty(csDutyRecordList)) {
			for (CsDutyRecordDO csDutyRecordDO : csDutyRecordList) {
				if (csDutyRecordDO == null) {
					continue;
				}
				// 工作天数
				if (csDutyRecordDO.getLoginTimesNum() > 0) {
					loginTimesNum += csDutyRecordDO.getLoginTimesNum();
					workDay++;
				} else {
					continue;
				}
				// 最早上线时间 -- 或使用最早上线时间与当天csDutyRecordDO.getDate()
				// 的差值，比较最小的为最早上线的时间
				if (csDutyRecordDO.getFirstOnlineDateTime() != null) {
					Date nowDelayDate = ymd_hms.parse(
							ymd_hms.format(csDutyRecordDO.getFirstOnlineDateTime().getTime() - delayTime * 60 * 60 * 1000));
					totalFirstOnlineDateTime += nowDelayDate.getTime()
							- DateUtil.getStartTimeOfDate(nowDelayDate).getTime();
					Date firstLoginDelayDate = null;
					if (firstOnlineDateTime != null) {
						firstLoginDelayDate = ymd_hms.parse(ymd_hms.format(firstOnlineDateTime.getTime() - delayTime * 60 * 60 * 1000));
					}
					if (firstOnlineDateTime == null) {
						firstOnlineDateTime = csDutyRecordDO.getFirstOnlineDateTime();
					} else {
						if (DateUtil.compareTimeOfDay(nowDelayDate, firstLoginDelayDate)) {
							firstOnlineDateTime = csDutyRecordDO.getFirstOnlineDateTime();
						}
					}
				}
				// 最晚下线时间
				if (csDutyRecordDO.getLastOfflineDateTime() != null) {
					Date nowDelayDate = ymd_hms.parse(
							ymd_hms.format(csDutyRecordDO.getLastOfflineDateTime().getTime() - delayTime * 60 * 60 * 1000));
					totalLastOnlineDateTime += nowDelayDate.getTime()
							- DateUtil.getStartTimeOfDate(nowDelayDate).getTime();
					Date lastDelayDate = null;
					if (lastOfflineDateTime != null) {
						lastDelayDate = ymd_hms.parse(ymd_hms.format(lastOfflineDateTime.getTime() - delayTime * 60 * 60 * 1000));
					}
					if (lastOfflineDateTime == null) {
						lastOfflineDateTime = csDutyRecordDO.getLastOfflineDateTime();
					} else {
						if (DateUtil.compareTimeOfDay(lastDelayDate, nowDelayDate)) {
							lastOfflineDateTime = csDutyRecordDO.getLastOfflineDateTime();
						}
					}
				}
				// 登录时长
				loginDurationTime += csDutyRecordDO.getLoginDurationTime();
				// 接待时长
				rceiveDurationTime += csDutyRecordDO.getRceiveDurationTime();
				// 挂起时长
				hangupDurationTime += csDutyRecordDO.getHangupDurationTime();
				// 上班期间离线时长
				offlineDurationTime += csDutyRecordDO.getOfflineDurationTime();

				suspendNum += BaseUtils.getNonNull(csDutyRecordDO.getSuspendNum());
			}
			if (workDay > 0) {
				//获取最早上线时间当天的00:00:00，加上评价时间再加上业务天延迟时间
				avgFirstOnlineDateTime = ymd_hms.parse(ymd_hms.format(DateUtil.getStartTimeOfDate(ymd_hms.parse(ymd_hms.format(firstOnlineDateTime.getTime() - delayTime * 60 * 60 * 1000))).getTime()
								+ totalFirstOnlineDateTime / workDay + delayTime * 60 * 60 * 1000));
				//获取最晚上线时间当天的00:00:00，加上评价时间再加上业务天延迟时间
				avgLastOfflineDateTime = ymd_hms.parse(ymd_hms.format(DateUtil.getStartTimeOfDate(ymd_hms.parse(ymd_hms.format(lastOfflineDateTime.getTime() - delayTime * 60 * 60 * 1000))).getTime()
								+ totalLastOnlineDateTime / workDay + delayTime * 60 * 60 * 1000));
			}
		}
		CsDutyRecordDTO calCsDutyRecord = new CsDutyRecordDTO(shopId, null, csNick, null);
		calCsDutyRecord.setWorkDay(workDay * 1.0);
		calCsDutyRecord.setDelayTime(delayTime);
		calCsDutyRecord.setFirstOnlineDateTime(firstOnlineDateTime);
		calCsDutyRecord.setLastOfflineDateTime(lastOfflineDateTime);
		calCsDutyRecord.setAvgFirstOnlineDateTime(avgFirstOnlineDateTime);
		calCsDutyRecord.setAvgLastOfflineDateTime(avgLastOfflineDateTime);
		calCsDutyRecord.setLoginDurationTime(loginDurationTime);
		calCsDutyRecord.setAvgLoginDurationTime(workDay > 0 ? (loginDurationTime / workDay) : 0);
		calCsDutyRecord.setRceiveDurationTime(rceiveDurationTime);
		calCsDutyRecord.setAvgRceiveDurationTime(workDay > 0 ? (rceiveDurationTime / workDay) : 0);
		calCsDutyRecord.setHangupDurationTime(hangupDurationTime);
		calCsDutyRecord.setAvgHangupDurationTime(workDay > 0 ? (hangupDurationTime / workDay) : 0);
		calCsDutyRecord.setLoginTimesNum(loginTimesNum * 1.0);
		calCsDutyRecord.setAvgLoginTimesNum(workDay > 0 ? (loginTimesNum * 1.0 / workDay) : 0);
		calCsDutyRecord.setRceiveTimeRate(loginDurationTime > 0 ? rceiveDurationTime * 1.0 / loginDurationTime : 0);
		calCsDutyRecord.setOfflineDurationTime(offlineDurationTime);
		calCsDutyRecord.setSuspendNum(suspendNum);
		return calCsDutyRecord;
	}

	/**
	 * calculateTotalCsDutyRecordForDetail:(计算值班详情汇总). <br/>
	 *
	 * @param queryCsDutyRecordDOList
	 * @param shopId
	 * @param delayTime
	 * @return
	 * @throws Exception
	 * @since JDK 1.8
	 */
	private CsDutyRecordDTO calculateTotalCsDutyRecordForDetail(List<CsDutyRecordDO> queryCsDutyRecordDOList,
			CsDutyRecordDTO csDutyRecord, Integer delayTime) throws Exception {
		Date firstOnlineDateTime = null;// 最早登录时间
		Date lastOfflineDateTime = null;// 最晚登出时间
		Long loginDurationTime = 0L;// 登录时长
		Long rceiveDurationTime = 0L;// 接待时长
		Long hangupDurationTime = 0L;// 挂起时长
		Long offlineDurationTime = 0L;// 上班期间离线时长
		int loginTimesNum = 0;
		logger.info("calculateTotalCsDutyRecordForDetail start");
		if (CollectionUtils.isNotEmpty(queryCsDutyRecordDOList)) {
			for (CsDutyRecordDO csDutyRecordDO : queryCsDutyRecordDOList) {
				if (csDutyRecordDO == null || csDutyRecordDO.getLoginTimesNum() <= 0) {
					continue;
				}
				loginTimesNum += csDutyRecordDO.getLoginTimesNum();
				// 最早上线时间
				if (csDutyRecordDO.getFirstOnlineDateTime() != null) {
					Date nowDelayDate = ymd_hms.parse(
							ymd_hms.format(csDutyRecordDO.getFirstOnlineDateTime().getTime() - delayTime * 60 * 60 * 1000));
					Date firstLoginDelayDate = null;
					if (firstOnlineDateTime != null) {
						firstLoginDelayDate = ymd_hms.parse(ymd_hms.format(firstOnlineDateTime.getTime() - delayTime * 60 * 60 * 1000));
					}
					if (firstOnlineDateTime == null) {
						firstOnlineDateTime = csDutyRecordDO.getFirstOnlineDateTime();
					} else if (DateUtil.compareTimeOfDay(nowDelayDate, firstLoginDelayDate)) {
						firstOnlineDateTime = csDutyRecordDO.getFirstOnlineDateTime();
					} else {
					}
				}
				// 最晚下线时间
				if (csDutyRecordDO.getLastOfflineDateTime() != null) {
					Date nowDelayDate = ymd_hms.parse(
							ymd_hms.format(csDutyRecordDO.getLastOfflineDateTime().getTime() - delayTime * 60 * 60 * 1000));
					Date lastDelayDate = null;
					if (lastOfflineDateTime != null) {
						lastDelayDate = ymd_hms.parse(ymd_hms.format(lastOfflineDateTime.getTime() - delayTime * 60 * 60 * 1000));
					}
					if (lastOfflineDateTime == null) {
						lastOfflineDateTime = csDutyRecordDO.getLastOfflineDateTime();
					} else if (DateUtil.compareTimeOfDay(lastDelayDate, nowDelayDate)) {
						lastOfflineDateTime = csDutyRecordDO.getLastOfflineDateTime();
					} else {
						// 不处理
					}
				}
				// 登录时长
				loginDurationTime += csDutyRecordDO.getLoginDurationTime();
				// 接待时长
				rceiveDurationTime += csDutyRecordDO.getRceiveDurationTime();
				// 挂起时长
				hangupDurationTime += csDutyRecordDO.getHangupDurationTime();
				// 上班期间离线时长
				offlineDurationTime += csDutyRecordDO.getOfflineDurationTime();
			}
		}

		csDutyRecord.setCsSimpleNick(StringUtils.isBlank(csDutyRecord.getCsSimpleNick())?"汇总":csDutyRecord.getCsSimpleNick());
		csDutyRecord.setLoginTimesNum(loginTimesNum * 1.0);
		csDutyRecord.setFirstOnlineDateTime(firstOnlineDateTime);
		csDutyRecord.setLastOfflineDateTime(lastOfflineDateTime);
		csDutyRecord.setLoginDurationTime(loginDurationTime);
		csDutyRecord.setRceiveDurationTime(rceiveDurationTime);
		csDutyRecord.setHangupDurationTime(hangupDurationTime);
		csDutyRecord.setOfflineDurationTime(offlineDurationTime);
		csDutyRecord.setRceiveTimeRate(rceiveDurationTime>0?rceiveDurationTime*1.0/loginDurationTime:0);
		logger.info("calculateTotalCsDutyRecordForDetail end");
		return csDutyRecord;
	}

	/**
	 * calculateAvgCsDutyRecordForDetail:(计算值班详情均值). <br/>
	 *
	 * @param queryCsDutyRecordDOList
	 * @param shopId
	 * @param delayTime
	 * @return
	 * @throws Exception
	 * @since JDK 1.8
	 */
	private CsDutyRecordDTO calculateAvgCsDutyRecordForDetail(List<CsDutyRecordDO> queryCsDutyRecordDOList,
			CsDutyRecordDTO csDutyRecord, Integer delayTime) throws Exception {
		Date date = null;
		Long totalFirstOnlineTime = 0L;
		Long totalLastOfflineTime = 0L;
		Date avgFirstOnlineDateTime = null;// 平均最早登录时间
		Date avgLastOfflineDateTime = null;// 平均最晚登出时间
		Long loginDurationTime = 0L;// 登录时长
		Long rceiveDurationTime = 0L;// 接待时长
		Long hangupDurationTime = 0L;// 挂起时长
		Long offlineDurationTime = 0L;// 上班期间离线时长
		int loginTimesNum = 0;
		int workDay = 0;
		for (CsDutyRecordDO csDutyRecordDO : queryCsDutyRecordDOList) {
			if (csDutyRecordDO == null || csDutyRecordDO.getLoginTimesNum() <= 0) {
				continue;
			}
			if (date == null) {
				date = DateUtil.getStartTimeOfDate(csDutyRecordDO.getDate());
			}
			loginTimesNum += csDutyRecordDO.getLoginTimesNum();
			workDay++;
			// 平均最早上线时间
			if (csDutyRecordDO.getFirstOnlineDateTime() != null) {
				Date nowDelayDate = ymd_hms.parse(
						ymd_hms.format(csDutyRecordDO.getFirstOnlineDateTime().getTime() - delayTime * 60 * 60 * 1000));
				totalFirstOnlineTime += nowDelayDate.getTime()
						- DateUtil.getStartTimeOfDate(csDutyRecordDO.getDate()).getTime();
			}
			// 平均最晚下线时间
			if (csDutyRecordDO.getLastOfflineDateTime() != null) {
				Date nowDelayDate = ymd_hms.parse(
						ymd_hms.format(csDutyRecordDO.getLastOfflineDateTime().getTime() - delayTime * 60 * 60 * 1000));
				totalLastOfflineTime += nowDelayDate.getTime()
						- DateUtil.getStartTimeOfDate(csDutyRecordDO.getDate()).getTime();
			}
			// 登录时长
			loginDurationTime += csDutyRecordDO.getLoginDurationTime();
			// 接待时长
			rceiveDurationTime += csDutyRecordDO.getRceiveDurationTime();
			// 挂起时长
			hangupDurationTime += csDutyRecordDO.getHangupDurationTime();
			// 上班期间离线时长
			offlineDurationTime += csDutyRecordDO.getOfflineDurationTime();
		}
		csDutyRecord.setCsSimpleNick("均值");
		if (workDay > 0) {
			avgFirstOnlineDateTime = ymd_hms.parse(ymd_hms.format(totalFirstOnlineTime / workDay + date.getTime() + delayTime * 60 * 60 * 1000));
			avgLastOfflineDateTime = ymd_hms.parse(ymd_hms.format(totalLastOfflineTime / workDay + date.getTime() + delayTime * 60 * 60 * 1000));
			csDutyRecord.setLoginTimesNum(loginTimesNum * 1.0 / workDay);
			csDutyRecord.setFirstOnlineDateTime(avgFirstOnlineDateTime);
			csDutyRecord.setLastOfflineDateTime(avgLastOfflineDateTime);
			csDutyRecord.setLoginDurationTime(loginDurationTime / workDay);
			csDutyRecord.setRceiveDurationTime(rceiveDurationTime / workDay);
			csDutyRecord.setHangupDurationTime(hangupDurationTime / workDay);
			csDutyRecord.setOfflineDurationTime(offlineDurationTime / workDay);
			csDutyRecord.setRceiveTimeRate(loginDurationTime > 0 ? (rceiveDurationTime * 1.0) / (loginDurationTime) : 0.0);
		}
		return csDutyRecord;
	}
	
	@Override
	public List<ShopCsDuty> selectCsLoginLogForShopUserAnalysis(String schemaId,UserAnalysisParam param) {
		List<ShopCsDuty> shopCsDutyLst = Lists.newArrayList();
		List<ShopCsLoginLogDTO> csLoginResultLst=	csLoginlogDao.selectCsLoginLogForShopUserAnalysis(schemaId, param);
		if(CollectionUtils.isNotEmpty(csLoginResultLst)){
			Map<Long, List<ShopCsLoginLogDTO>> shopCsLoginMap=csLoginResultLst.stream().collect(Collectors.groupingBy(ShopCsLoginLogDTO::getShopId));
			for (Entry<Long, List<ShopCsLoginLogDTO>> entry : shopCsLoginMap.entrySet()) {
				Long shopId=entry.getKey();
				List<ShopCsLoginLogDTO> csLoginLst=entry.getValue();
				if(CollectionUtils.isEmpty(csLoginLst)){
					continue;
				}
				Map<Date, List<ShopCsLoginLogDTO>> csLoginDateMap=csLoginLst.stream().collect(Collectors.groupingBy(ShopCsLoginLogDTO::getDate));
				for (Entry<Date, List<ShopCsLoginLogDTO>> dateEntry : csLoginDateMap.entrySet()) {
					Date date=dateEntry.getKey();
					List<ShopCsLoginLogDTO> dateLoginLst=dateEntry.getValue();
					if(CollectionUtils.isEmpty(dateLoginLst)){
						continue;
					}
					ShopCsDuty duty=new ShopCsDuty();
					duty.setDate(date);
					duty.setShopId(shopId);
					Set<String> dutyCsSet=dateLoginLst.stream().map(ShopCsLoginLogDTO::getCsNick).collect(Collectors.toSet());
					duty.setNum(dutyCsSet.size());
					duty.setCsDutySets(dutyCsSet);
					shopCsDutyLst.add(duty);
				}
			}
		} 
		
		return shopCsDutyLst;
	}
}
