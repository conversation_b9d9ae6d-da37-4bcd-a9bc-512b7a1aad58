package com.pes.jd.business.sub.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.business.sub.UserPortraitStatisticsBusiness;
import com.pes.jd.dao.sub.UserPortraitStatisticsDao;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.DO.UserPortraitStatistics;
import com.pes.jd.model.DTO.RegionInfoDTO;
import com.pes.jd.model.DTO.UserPortraitStatisticsDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Result.RegionDistributionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserPortraitStatisticsBusinessImpl implements UserPortraitStatisticsBusiness {

    @Autowired
    private UserPortraitStatisticsDao userPortraitStatisticsDao;

    @Autowired
    private JdAddressCacheBusinessV2Impl addressCacheService;

    private static final Logger logger = LoggerFactory.getLogger(UserPortraitStatisticsBusinessImpl.class);

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public UserPortraitStatisticsDTO getPortraitStatisticsByDate(ShopCommonParam shop, Date startDate, Date endDate) {

        List<UserPortraitStatistics> userPortraitStatistics =  userPortraitStatisticsDao.getPortraitStatisticsByDate(shop, startDate, endDate);
        if(CollectionUtil.isEmpty(userPortraitStatistics)){
            return new UserPortraitStatisticsDTO();
        }
        UserPortraitStatistics userPortraitStatistics1 = UserPortraitStatistics.mergeStatistics(userPortraitStatistics);
        UserPortraitStatisticsDTO userPortraitStatisticsDTO = buildResponseDTO(userPortraitStatistics1);


        return userPortraitStatisticsDTO;
    }

    /**
     * 获取地理分布信息
     * @param shop
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public RegionDistributionResult getRegionDistributionByDate(ShopCommonParam shop, Date startDate, Date endDate) {
        List<String> regionJsonList = userPortraitStatisticsDao.getRegionDistributionByShopAndDateRange(shop,startDate,endDate);
        if(CollectionUtil.isEmpty(regionJsonList)){
            return new RegionDistributionResult();
        }
        // 2. 合并所有JSON数据
        Map<String, Integer> mergedRegionData = mergeRegionData(regionJsonList);

        // 3. 解析并构建层级结构
        Map<Long, Integer> regionUserCounts = parseRegionData(mergedRegionData);

        // 4. 构建结果
        return buildRegionDistributionResult( regionUserCounts);

    }


    /**
     * 合并多个JSON数据
     */
    private Map<String, Integer> mergeRegionData(List<String> regionJsonList) {
        Map<String, Integer> mergedData = new HashMap<>();

        for (String jsonStr : regionJsonList) {
            if (!StringUtils.hasText(jsonStr)) {
                continue;
            }

            try {
                Map<String, Integer> dailyData = objectMapper.readValue(
                        jsonStr,
                        new TypeReference<Map<String, Integer>>() {}
                );

                for (Map.Entry<String, Integer> entry : dailyData.entrySet()) {
                    mergedData.merge(entry.getKey(), entry.getValue(), Integer::sum);
                }
            } catch (Exception e) {
                logger.warn("解析JSON数据失败: {}", jsonStr, e);
            }
        }

        return mergedData;
    }

    /**
     * 解析区域数据，提取区域ID和用户数量
     */
    private Map<Long, Integer> parseRegionData(Map<String, Integer> regionData) {
        Map<Long, Integer> result = new HashMap<>();

        for (Map.Entry<String, Integer> entry : regionData.entrySet()) {
            String key = entry.getKey();
            Integer userCount = entry.getValue();

            // 解析key，格式如：province_1, city_72, area_154
            Long areaId = extractAreaId(key);
            if (areaId != null) {
                result.put(areaId, userCount);
            }
        }

        return result;
    }

    /**
     * 从key中提取区域ID
     */
    private Long extractAreaId(String key) {
        try {
            if (key.startsWith("province_")) {
                return Long.valueOf(key.substring("province_".length()));
            } else if (key.startsWith("city_")) {
                return Long.valueOf(key.substring("city_".length()));
            } else if (key.startsWith("area_")) {
                return Long.valueOf(key.substring("area_".length()));
            }
        } catch (NumberFormatException e) {
            logger.warn("解析区域ID失败: {}", key);
        }
        return null;
    }

    /**
     * 构建响应DTO
     */
    private UserPortraitStatisticsDTO buildResponseDTO(UserPortraitStatistics statistics) {
        UserPortraitStatisticsDTO responseDTO = new UserPortraitStatisticsDTO();

        // 年龄分布
        responseDTO.setAgeDistribution(buildAgeDistribution(statistics));

        // 性别分布
        responseDTO.setGenderDistribution(buildGenderDistribution(statistics));

        // 婚姻状况分布
        responseDTO.setMarriageDistribution(buildMarriageDistribution(statistics));

        // 职业分布
        responseDTO.setProfessionDistribution(buildProfessionDistribution(statistics));

        // 用户群体类型分布
        responseDTO.setUserGroupDistribution(buildUserGroupDistribution(statistics));

        // 孩子数量分布
        responseDTO.setChildrenDistribution(buildChildrenDistribution(statistics));

        // 地区分布
        //responseDTO.setRegionDistribution(buildRegionDistribution(statistics));

        // 大促预售购买敏感人群占比
        responseDTO.setPresaleDistribution(buildPresaleDistribution(statistics));

        // 平台促销敏感人群占比
        responseDTO.setPromotionSensitivityDistribution(buildPromotionSensitivityDistribution(statistics));

        // 大促高消费金额人群
        responseDTO.setHighConsumptionDistribution(buildHighConsumptionDistribution(statistics));

        // 新品偏好人群占比
        responseDTO.setNewProductPreferenceDistribution(buildNewProductPreferenceDistribution(statistics));

        return responseDTO;
    }
    /**
     * 构建年龄分布数据
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildAgeDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> ageMap = new LinkedHashMap<>();
        ageMap.put("0-15岁", statistics.getAge0To15());
        ageMap.put("16-20岁", statistics.getAge16To20());
        ageMap.put("21-25岁", statistics.getAge21To25());
        ageMap.put("26-30岁", statistics.getAge26To30());
        ageMap.put("31-35岁", statistics.getAge31To35());
        ageMap.put("36-40岁", statistics.getAge36To40());
        ageMap.put("41-45岁", statistics.getAge41To45());
        ageMap.put("46-50岁", statistics.getAge46To50());
        ageMap.put("51-55岁", statistics.getAge51To55());
        ageMap.put("56-60岁", statistics.getAge56To60());
        ageMap.put("61-65岁", statistics.getAge61To65());
        ageMap.put("66-70岁", statistics.getAge66To70());
        ageMap.put("71岁及以上", statistics.getAgeOver71());

        return buildPieChartDataList(ageMap);
    }
    /**
     * 构建性别分布数据
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildGenderDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> genderMap = new LinkedHashMap<>();
        genderMap.put("男性", statistics.getGenderMale());
        genderMap.put("女性", statistics.getGenderFemale());

        // 计算未知性别数量
        int totalGender = statistics.getGenderMale() + statistics.getGenderFemale();
        int unknownGender = statistics.getTotalUsers() - totalGender;
        if (unknownGender > 0) {
            genderMap.put("未知", unknownGender);
        }

        return buildPieChartDataList(genderMap);
    }
    /**
     * 构建婚姻状况分布数据
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildMarriageDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> marriageMap = new LinkedHashMap<>();
        marriageMap.put("未婚", statistics.getMarriageSingle());
        marriageMap.put("已婚", statistics.getMarriageMarried());

        return buildPieChartDataList(marriageMap);
    }
    /**
     * 构建职业分布数据
     */
    private List<UserPortraitStatisticsDTO.BarChartData> buildProfessionDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> professionMap = new LinkedHashMap<>();
        professionMap.put("金融从业者", statistics.getProfessionFinance());
        professionMap.put("医务人员", statistics.getProfessionMedical());
        professionMap.put("公司职员", statistics.getProfessionEmployee());
        professionMap.put("工人", statistics.getProfessionWorker());
        professionMap.put("教职工", statistics.getProfessionTeacher());
        professionMap.put("农民", statistics.getProfessionFarmer());
        professionMap.put("学生", statistics.getProfessionStudent());
        professionMap.put("个体或服务业", statistics.getProfessionIndividual());
        professionMap.put("城市其他职业", statistics.getProfessionUrbanOther());
        professionMap.put("农村其他职业", statistics.getProfessionRuralOther());

        return buildBarChartDataList(professionMap);
    }
    /**
     * 构建用户群体类型分布数据
     */
    private List<UserPortraitStatisticsDTO.BarChartData> buildUserGroupDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> userGroupMap = new LinkedHashMap<>();
        userGroupMap.put("学生一族", statistics.getStudentGroup());
        userGroupMap.put("银发一族", statistics.getElderlyGroup());
        userGroupMap.put("都市家庭", statistics.getUrbanFamilyGroup());
        userGroupMap.put("小镇家庭", statistics.getTownFamilyGroup());
        userGroupMap.put("都市Z世代", statistics.getUrbanGenZGroup());
        userGroupMap.put("都市中产", statistics.getUrbanMiddleClassGroup());
        userGroupMap.put("都市蓝领", statistics.getUrbanBlueCollarGroup());
        userGroupMap.put("小镇中年", statistics.getTownMiddleAgedGroup());
        userGroupMap.put("小镇青年", statistics.getTownYouthGroup());
        userGroupMap.put("小镇中产", statistics.getTownMiddleClassGroup());

        return buildBarChartDataList(userGroupMap);
    }
    /**
     * 构建孩子数量分布数据
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildChildrenDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> childrenMap = new LinkedHashMap<>();
        childrenMap.put("未知", statistics.getChildrenUnknown());
        childrenMap.put("1个孩子", statistics.getChildrenOne());
        childrenMap.put("2个孩子", statistics.getChildrenTwo());
        childrenMap.put("3个孩子", statistics.getChildrenThree());
        childrenMap.put("4个孩子", statistics.getChildrenFour());

        return buildPieChartDataList(childrenMap);
    }
    /**
     * 构建地区分布数据
     */
    private Map<String, Object> buildRegionDistribution(UserPortraitStatistics statistics) {
        Map<String, Object> regionData = new HashMap<>();

        if (StringUtils.hasText(statistics.getRegionDistribution())) {
            try {
                Map<String, Integer> regionMap = objectMapper.readValue(
                        statistics.getRegionDistribution(),
                        new TypeReference<Map<String, Integer>>() {}
                );

                // 按省份、城市、区域分组
                Map<String, Integer> provinceMap = new HashMap<>();
                Map<String, Integer> cityMap = new HashMap<>();
                Map<String, Integer> areaMap = new HashMap<>();

                regionMap.forEach((key, value) -> {
                    if (key.startsWith("province_")) {
                        provinceMap.put(key.replace("province_", ""), value);
                    } else if (key.startsWith("city_")) {
                        cityMap.put(key.replace("city_", ""), value);
                    } else if (key.startsWith("area_")) {
                        areaMap.put(key.replace("area_", ""), value);
                    }
                });

                regionData.put("provinces", provinceMap);
                regionData.put("cities", cityMap);
                regionData.put("areas", areaMap);

            } catch (Exception e) {
                logger.warn("解析地区分布JSON失败", e);
            }
        }

        return regionData;
    }


    /**
     * 构建大促预售购买敏感人群占比
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildPresaleDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> presaleMap = new LinkedHashMap<>();
        presaleMap.put("购买预售", statistics.getPresaleYes());
        presaleMap.put("不购买预售", statistics.getPresaleNo());

        return buildPieChartDataList(presaleMap);
    }
    /**
     * 构建平台促销敏感人群占比
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildPromotionSensitivityDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> sensitivityMap = new LinkedHashMap<>();
        sensitivityMap.put("不敏感", statistics.getPromotionNotSensitive());
        sensitivityMap.put("轻度敏感", statistics.getPromotionLightlySensitive());
        sensitivityMap.put("中度敏感", statistics.getPromotionModeratelySensitive());
        sensitivityMap.put("高度敏感", statistics.getPromotionHighlySensitive());
        sensitivityMap.put("非常敏感", statistics.getPromotionVerySensitive());
        sensitivityMap.put("敏感度未知", statistics.getPromotionSensitivityUnknown());

        return buildPieChartDataList(sensitivityMap);
    }
    /**
     * 构建大促高消费金额人群
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildHighConsumptionDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> consumptionMap = new LinkedHashMap<>();
        consumptionMap.put("高消费用户", statistics.getHighConsumptionYes());
        consumptionMap.put("非高消费用户", statistics.getHighConsumptionNo());

        return buildPieChartDataList(consumptionMap);
    }
    /**
     * 构建新品偏好人群占比
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildNewProductPreferenceDistribution(UserPortraitStatistics statistics) {
        Map<String, Integer> preferenceMap = new LinkedHashMap<>();
        preferenceMap.put("潜在偏好", statistics.getNewProductPotentialYes());
        preferenceMap.put("无潜在偏好", statistics.getNewProductPotentialNo());
        preferenceMap.put("中度偏好", statistics.getNewProductMediumYes());
        preferenceMap.put("无中度偏好", statistics.getNewProductMediumNo());
        preferenceMap.put("重度偏好", statistics.getNewProductSevereYes());
        preferenceMap.put("无重度偏好", statistics.getNewProductSevereNo());

        return buildPieChartDataList(preferenceMap);
    }
    /**
     * 构建饼图数据列表
     */
    private List<UserPortraitStatisticsDTO.PieChartData> buildPieChartDataList(Map<String, Integer> dataMap) {
        int total = dataMap.values().stream().mapToInt(Integer::intValue).sum();

        return dataMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 0) // 过滤掉0值
                .map(entry -> {
                    double percentage = total > 0 ? (double) entry.getValue() / total * 100 : 0;
                    return new UserPortraitStatisticsDTO.PieChartData(
                            entry.getKey(),
                            entry.getValue(),
                            Math.round(percentage * 100) / 100.0 // 保留两位小数
                    );
                })
                .collect(Collectors.toList());
    }
    /**
     * 构建柱状图数据列表
     */
    private List<UserPortraitStatisticsDTO.BarChartData> buildBarChartDataList(Map<String, Integer> dataMap) {
        int total = dataMap.values().stream().mapToInt(Integer::intValue).sum();

        return dataMap.entrySet().stream()
                .filter(entry -> entry.getValue() > 0) // 过滤掉0值
                .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue())) // 按数量降序排列
                .map(entry -> {
                    double percentage = total > 0 ? (double) entry.getValue() / total * 100 : 0;
                    return new UserPortraitStatisticsDTO.BarChartData(
                            entry.getKey(),
                            entry.getValue(),
                            Math.round(percentage * 100) / 100.0 // 保留两位小数
                    );
                })
                .collect(Collectors.toList());
    }


    /**
     * 构建地理分布结果
     */
    private RegionDistributionResult buildRegionDistributionResult(
            Map<Long, Integer> regionUserCounts) {


        // 2. 构建省级层级结构
        List<RegionInfoDTO> provinces = buildProvinceHierarchy(regionUserCounts);

        return new RegionDistributionResult()
                .setProvinces(provinces);
    }

    /**
     * 构建所有区域信息（扁平化）
     */
    private List<RegionInfoDTO> buildAllRegionInfo(Map<Long, Integer> regionUserCounts) {
        List<RegionInfoDTO> allRegions = new ArrayList<>();

        for (Map.Entry<Long, Integer> entry : regionUserCounts.entrySet()) {
            Long areaId = entry.getKey();
            Integer userCount = entry.getValue();

            JdAddress address = addressCacheService.getAddressByAreaId(areaId);
            if (address != null) {
                RegionInfoDTO regionInfo = new RegionInfoDTO()
                        .setAreaId(areaId)
                        .setAreaName(address.getAreaName())
                        .setParentId(address.getParentId())
                        .setLevel(address.getLevel())
                        .setUserCount(userCount);

                allRegions.add(regionInfo);
            }
        }

        // 按层级和用户数量排序
        allRegions.sort((r1, r2) -> {
            int levelCompare = r1.getLevel().compareTo(r2.getLevel());
            if (levelCompare != 0) {
                return levelCompare;
            }
            return r2.getUserCount().compareTo(r1.getUserCount());
        });

        return allRegions;
    }


    /**
     * 构建省级层级结构
     */
    private List<RegionInfoDTO> buildProvinceHierarchy(Map<Long, Integer> regionUserCounts) {
        Map<Long, JdAddress> allProvinces = addressCacheService.getAllProvinces();
        Map<Long, JdAddress> allCities = addressCacheService.getAllCities();
        Map<Long, JdAddress> allAreas = addressCacheService.getAllAreas();

        List<RegionInfoDTO> provinces = new ArrayList<>();

        // 1. 直接从regionUserCounts中获取省份数据
        for (Map.Entry<Long, Integer> entry : regionUserCounts.entrySet()) {
            Long areaId = entry.getKey();
            Integer userCount = entry.getValue();

            // 检查是否为省份级别的数据
            JdAddress provinceAddress = allProvinces.get(areaId);
            if (provinceAddress != null) {
                RegionInfoDTO provinceInfo = new RegionInfoDTO()
                        .setAreaId(areaId)
                        .setAreaName(provinceAddress.getAreaName())
                        .setParentId(provinceAddress.getParentId())
                        .setLevel(provinceAddress.getLevel())
                        .setUserCount(userCount);

                // 构建该省份下的城市信息
                List<RegionInfoDTO> cities = buildCitiesForProvince(
                        areaId, regionUserCounts, allCities, allAreas);
                provinceInfo.setChildren(cities);

                provinces.add(provinceInfo);
            }
        }

        // 按用户数量降序排序
        provinces.sort((p1, p2) -> p2.getUserCount().compareTo(p1.getUserCount()));

        return provinces;
    }

    /**
     * 根据区域ID查找所属省份ID
     */
    private Long findProvinceId(Long areaId,
                                Map<Long, JdAddress> allProvinces,
                                Map<Long, JdAddress> allCities,
                                Map<Long, JdAddress> allAreas) {

        // 如果本身就是省份
        if (allProvinces.containsKey(areaId)) {
            return areaId;
        }

        // 如果是城市，查找父级省份
        JdAddress city = allCities.get(areaId);
        if (city != null && city.getParentId() != null) {
            return city.getParentId();
        }

        // 如果是区县，查找父级城市，再查找省份
        JdAddress area = allAreas.get(areaId);
        if (area != null && area.getParentId() != null) {
            JdAddress parentCity = allCities.get(area.getParentId());
            if (parentCity != null && parentCity.getParentId() != null) {
                return parentCity.getParentId();
            }
        }

        return null;
    }


    /**
     * 构建指定省份下的城市信息
     */
    private List<RegionInfoDTO> buildCitiesForProvince(
            Long provinceId,
            Map<Long, Integer> regionUserCounts,
            Map<Long, JdAddress> allCities,
            Map<Long, JdAddress> allAreas) {

        List<RegionInfoDTO> cities = new ArrayList<>();

        // 直接从regionUserCounts中查找该省份下的城市数据
        for (Map.Entry<Long, Integer> entry : regionUserCounts.entrySet()) {
            Long areaId = entry.getKey();
            Integer userCount = entry.getValue();

            JdAddress cityAddress = allCities.get(areaId);
            if (cityAddress != null && provinceId.equals(cityAddress.getParentId())) {
                RegionInfoDTO cityInfo = new RegionInfoDTO()
                        .setAreaId(areaId)
                        .setAreaName(cityAddress.getAreaName())
                        .setParentId(cityAddress.getParentId())
                        .setLevel(cityAddress.getLevel())
                        .setUserCount(userCount);

                // 构建该城市下的区县信息
                List<RegionInfoDTO> areas = buildAreasForCity(areaId, regionUserCounts, allAreas);
                cityInfo.setChildren(areas);

                cities.add(cityInfo);
            }
        }

        // 按用户数量降序排序
        cities.sort((c1, c2) -> c2.getUserCount().compareTo(c1.getUserCount()));

        return cities;
    }

    /**
     * 构建指定城市下的区县信息
     */
    private List<RegionInfoDTO> buildAreasForCity(
            Long cityId,
            Map<Long, Integer> regionUserCounts,
            Map<Long, JdAddress> allAreas) {

        List<RegionInfoDTO> areas = new ArrayList<>();

        // 直接从regionUserCounts中查找该城市下的区县数据
        for (Map.Entry<Long, Integer> entry : regionUserCounts.entrySet()) {
            Long areaId = entry.getKey();
            Integer userCount = entry.getValue();

            JdAddress areaAddress = allAreas.get(areaId);
            if (areaAddress != null && cityId.equals(areaAddress.getParentId())) {
                RegionInfoDTO areaInfo = new RegionInfoDTO()
                        .setAreaId(areaId)
                        .setAreaName(areaAddress.getAreaName())
                        .setParentId(areaAddress.getParentId())
                        .setLevel(areaAddress.getLevel())
                        .setUserCount(userCount);

                areas.add(areaInfo);
            }
        }

        // 按用户数量降序排序
        areas.sort((a1, a2) -> a2.getUserCount().compareTo(a1.getUserCount()));

        return areas;
    }

}
