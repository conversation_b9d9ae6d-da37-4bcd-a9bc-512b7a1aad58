package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.CsOrderIndexBusiness;
import com.pes.jd.constants.PerformanceConstans;
import com.pes.jd.dao.sub.CsOrderIndexDao;
import com.pes.jd.model.DTO.CsPerformanceOrderIndexDTO;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/21 3:25 PM
 * @since 1.0.0
 */
@Service
public class CsOrderIndexBusinessImpl implements CsOrderIndexBusiness {
    @Autowired
    private CsOrderIndexDao csOrderIndexDao;
    @Override
    public List<CsPerformanceOrderIndexDTO> searchDateShop(
            Set<String> nicks, Long shopId, Date startDate, Date endDate, String schemaId, String dimension) {
        List<CsPerformanceOrderIndexDTO> csPerformanceOrderIndexDTOS = csOrderIndexDao.searchDateShop(nicks, shopId, startDate, endDate, schemaId, dimension);
        final String date = "date";
        if (Objects.equals(date,dimension)){
            //如果是日期则无需聚合
            return csPerformanceOrderIndexDTOS;
        }
        return CommonUtils.polymerize(csPerformanceOrderIndexDTOS,
                this::getInitObject, CsPerformanceOrderIndexDTO::getCsNick,(x, y)-> x.setCsNick(y.getCsNick()),x->x.setShopId(shopId), null, PerformanceConstans.IGNORE_PROPERTIES);
    }

    private CsPerformanceOrderIndexDTO getInitObject() {
        CsPerformanceOrderIndexDTO dto = new CsPerformanceOrderIndexDTO();
        dto.setId(0L);
        dto.setShopId(0L);
        dto.setDate(null);
        dto.setCsNick(StringUtils.EMPTY);
        dto.setBuyerNick(StringUtils.EMPTY);
        dto.setOrderId(0L);
        dto.setOrderFlag(null);
        dto.setOrderCreated(null);
        dto.setOrderPayDate(null);
        dto.setOrderPayment(0d);
        dto.setOrderGoodsNum(0);
        dto.setOrderPostFee(0d);
        dto.setGoodsFilte(false);
        dto.setMrnFilter(false);
        dto.setBcChatNum(0);
        dto.setBcReplyNum(0);
        dto.setBcChatRoundNum(0);
        dto.setBcFirstReplyDate(null);
        dto.setBcLastReplyDate(null);
        dto.setBcFirstChatDate(null);
        dto.setBcLastChatDate(null);
        dto.setBpReplyNum(0);
        dto.setBpChatRoundNum(0);
        dto.setBpFirstReplyDate(null);
        dto.setBpLastChatDate(null);
        dto.setBpLastReplyDate(null);
        dto.setBpChatNum(0);
        dto.setAcFirstReplyDate(null);
        dto.setAcFirstChatDate(null);
        dto.setApFirstChatDate(null);
        dto.setApFirstReplyDate(null);
        dto.setLastReplyDate(null);
        dto.setFirstReplyDate(null);
        dto.setFirstChatDate(null);
        dto.setLastChatDate(null);
        dto.setAssitOrderCreate(false);
        dto.setAssitOrderInFollowup(false);
        dto.setAssitOrderPay(false);
        dto.setAidOrderNum(0);
        dto.setAidOrderAmount(0d);
        dto.setAidPayNum(0);
        dto.setAidPayAmount(0d);
        dto.setAidFollowNum(0);
        dto.setAidFollowAmount(0d);
        return dto;


    }
}
