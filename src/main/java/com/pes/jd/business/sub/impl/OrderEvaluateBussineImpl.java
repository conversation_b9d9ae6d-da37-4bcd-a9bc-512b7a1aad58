package com.pes.jd.business.sub.impl;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.OrderEvaluateBussiness;
import com.pes.jd.constants.SubTable;
import com.pes.jd.dao.sub.OrderEvaluateDao;
import com.pes.jd.model.Enum.QueryMode;
import com.pes.jd.util.AppContext;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/12 2:07 PM
 * @since 1.0.0
 */
@Service
public class OrderEvaluateBussineImpl implements OrderEvaluateBussiness {

    @Autowired
    private OrderEvaluateDao orderEvaluateDao;


    @Override
    public List<Map<String, Object>> selectByDateShopTypeNick(
            Date start, Date endDate, String shopId, String nick, QueryMode mode) {
        Map<String,Object> param = Maps.newHashMapWithExpectedSize(8);
        param.put("begin",start);
        param.put("end",endDate);
        param.put("shopId",shopId);
        param.put("nick",nick);
        param.put("tableName",
                CommonUtils.getTableName(AppContext.currentContext().getSchema()
                        , SubTable.PES_ORDER_EVALUATE.getName(),shopId));
        // TODO 中差评数据库标识未知  根据mode赋值
        param.put("result","unknown");
        return orderEvaluateDao.selectByDateShopTypeNick(param);
    }
}
