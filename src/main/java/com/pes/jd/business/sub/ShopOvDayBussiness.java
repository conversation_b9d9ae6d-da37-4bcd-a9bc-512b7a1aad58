package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.ShopOrderSaleAmountDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.ms.domain.Data.shopdata.ShopOvDay;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年08月08 15:34:34<br>
 */
public interface ShopOvDayBussiness {

    List<ShopOvDay> selectShopSaleAmountByShopIdByDate(String schemdId, List<Long> shopIdLst, Date startDate, Date endDate);

    List<ShopOvDay> selectMultiShopSaleAmount(List<CauseShop> causeShopLst, Date startDate, Date endDate);

    List<ShopOvDay> selectShopOrderSaleAmountByShopIdByDate(String schemdId, Set<Long> shopIdSet, Date date);

    List<ShopOrderSaleAmountDTO> selectMultiShopOrderSaleAmount(List<ShopUrge> causeShopLst, Date startDate, Date endDate);

    double selectShopSaleAmountByShopIdByDateNew(ShopCommonParam shopCommonParam, Date date);
}
