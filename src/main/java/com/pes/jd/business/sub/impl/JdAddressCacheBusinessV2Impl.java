package com.pes.jd.business.sub.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.mapper.sub.JdAddressMapper;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Service
public class JdAddressCacheBusinessV2Impl {

    private static final Logger log = LoggerFactory.getLogger(JdAddressCacheBusinessV2Impl.class);

    // Redis缓存Key常量
    private static final String REDIS_ADDRESS_ALL_KEY = "address:all";
    private static final String REDIS_ADDRESS_EXPIRE_CHANNEL = "address_cache_expire";
    private static final String REDIS_CACHE_VERSION_KEY = "address:cache:version";

    // 缓存过期时间（小时）
    private static final int CACHE_EXPIRE_HOURS = 24*30;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisMessageListenerContainer redisMessageListenerContainer;

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private UsermgrRestTemplate usermgrRestTemplate;

    // 本地缓存
    private final Map<Long, JdAddress> addressCache = new ConcurrentHashMap<>();
    private final Map<Integer, Map<Long, JdAddress>> addressByLevelCache = new ConcurrentHashMap<>();
    private final Map<Long, List<JdAddress>> childrenCache = new ConcurrentHashMap<>();

    // 缓存版本号（用于判断缓存是否需要更新）
    private volatile Long localCacheVersion = 0L;

    // 防止并发加载的锁
    private final ReentrantLock cacheLock = new ReentrantLock();

    @PostConstruct
    public void initCache() {
        // 注册Redis消息监听器
        setupRedisMessageListener();
        // 初始化缓存
       // loadCache();
    }

    @PreDestroy
    public void destroy() {
        // 清理资源
        addressCache.clear();
        addressByLevelCache.clear();
        childrenCache.clear();
    }

    /**
     * 设置Redis消息监听器，用于集群缓存失效通知
     */
    private void setupRedisMessageListener() {
        log.info("=== 开始设置Redis监听器 ===");
        // 创建监听器适配器
        MessageListenerAdapter adapter = new MessageListenerAdapter();
        adapter.setDelegate(this);
        adapter.setDefaultListenerMethod("handleCacheExpireMessage");
        adapter.setSerializer(new StringRedisSerializer());

        adapter.afterPropertiesSet();
        ChannelTopic topic = new ChannelTopic(REDIS_ADDRESS_EXPIRE_CHANNEL);
        redisMessageListenerContainer.addMessageListener(adapter, topic);

        log.info("=== Redis监听器设置完成 ===");
    }

    /**
     * 处理缓存失效消息
     */
    public void handleCacheExpireMessage(String message) {
        log.info("接收到缓存失效通知: {}", message);
        clearLocalCache();
    }

    /**
     * 加载缓存 - 三级缓存策略
     */
    private List<JdAddress> loadCache() {
        // 检查缓存版本
        if (isCacheValid()) {
            log.debug("本地缓存有效，直接返回");
            return new ArrayList<>(addressCache.values());
        }

        cacheLock.lock();
        try {
            // 双重检查
            if (isCacheValid()) {
                return new ArrayList<>(addressCache.values());
            }

            // 1. 先从Redis读取
            List<JdAddress> addresses = loadFromRedis();

            if (addresses != null && !addresses.isEmpty()) {
                log.info("从Redis缓存加载地址数据，共{}条记录", addresses.size());
                buildLocalCache(addresses);
                return addresses;
            }

            // 2. Redis为空，进行远程调用
            log.info("Redis缓存为空，开始远程调用获取数据...");
            addresses = loadFromRemote();

            if (addresses != null && !addresses.isEmpty()) {
                // 3. 保存到Redis并设置过期时间
                saveToRedis(addresses);
                buildLocalCache(addresses);
                log.info("远程调用成功，数据已缓存到Redis和本地，共{}条记录", addresses.size());
                return addresses;
            }

            log.error("无法获取地址数据");
            return Collections.emptyList();

        } catch (Exception e) {
            log.error("加载缓存失败", e);
            return Collections.emptyList();
        } finally {
            cacheLock.unlock();
        }
    }

    /**
     * 检查缓存是否有效
     */
    private boolean isCacheValid() {
        if (addressCache.isEmpty()) {
            return false;
        }

        // 检查Redis中的缓存版本号
        try {
            Long redisCacheVersion = (Long) redisTemplate.opsForValue().get(REDIS_CACHE_VERSION_KEY);
            return redisCacheVersion != null && localCacheVersion.equals(redisCacheVersion);
        } catch (Exception e) {
            log.warn("检查缓存版本失败", e);
            return false;
        }
    }

    /**
     * 从Redis加载数据
     */
    private List<JdAddress> loadFromRedis() {
        try {
            Object cachedData = redisTemplate.opsForValue().get(REDIS_ADDRESS_ALL_KEY);
            if (cachedData != null) {
                String jsonStr = cachedData.toString();
                List<JdAddress> addresses = objectMapper.readValue(jsonStr, new TypeReference<List<JdAddress>>() {});
                // 更新本地缓存版本号
                Long version = (Long) redisTemplate.opsForValue().get(REDIS_CACHE_VERSION_KEY);
                if (version != null) {
                    localCacheVersion = version;
                }
                return addresses;
            }
        } catch (Exception e) {
            log.error("从Redis加载缓存失败", e);
        }
        return null;
    }

    /**
     * 远程调用获取数据
     */
    private List<JdAddress> loadFromRemote() {
        try {
            String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            Map<String, Object> requestMap = new HashMap<>();
            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);
            RestResponseTypeRef<Map<String, Object>> response = usermgrRestTemplate.postRest(
                    serviceId,
                    "/jdAddress/getAll",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Map<String, Object>>>() {});
            if(response.getSuccess() && response.getData() != null){
                Map<String, Object> data = response.getData();
                Object result = data.get("result");
                if (result != null) {
                     List<JdAddress> addresses = objectMapper.convertValue(result,
                         new TypeReference<List<JdAddress>>() {});
                     return addresses;
                }
            }
            return null;
        } catch (Exception e) {
            log.error("远程调用获取地址数据失败", e);
            throw new RuntimeException("获取地址数据失败", e);
        }
    }

    /**
     * 保存数据到Redis
     */
    private void saveToRedis(List<JdAddress> addresses) {
        try {
            String jsonStr = objectMapper.writeValueAsString(addresses);

            // 保存数据
            redisTemplate.opsForValue().set(REDIS_ADDRESS_ALL_KEY, jsonStr, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

            // 更新版本号
            long newVersion = System.currentTimeMillis();
            redisTemplate.opsForValue().set(REDIS_CACHE_VERSION_KEY, newVersion, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            localCacheVersion = newVersion;

            log.info("数据已保存到Redis，缓存版本: {}", newVersion);
        } catch (Exception e) {
            log.error("保存数据到Redis失败", e);
        }
    }

    /**
     * 构建本地缓存
     */
    private void buildLocalCache(List<JdAddress> addresses) {
        // 清空现有缓存
        addressCache.clear();
        addressByLevelCache.clear();
        childrenCache.clear();

        // 构建基础缓存
        for (JdAddress address : addresses) {
            addressCache.put(address.getAreaId(), address);
        }

        // 按层级分组
        Map<Integer, List<JdAddress>> levelGroups = addresses.stream()
                .collect(Collectors.groupingBy(JdAddress::getLevel));

        for (Map.Entry<Integer, List<JdAddress>> entry : levelGroups.entrySet()) {
            Map<Long, JdAddress> levelMap = entry.getValue().stream()
                    .collect(Collectors.toMap(JdAddress::getAreaId, addr -> addr));
            addressByLevelCache.put(entry.getKey(), levelMap);
        }

        // 构建父子关系缓存
        Map<Long, List<JdAddress>> parentChildMap = addresses.stream()
                .filter(addr -> addr.getParentId() != null)
                .collect(Collectors.groupingBy(JdAddress::getParentId));

        childrenCache.putAll(parentChildMap);
        log.info("本地缓存构建完成");
    }

    /**
     * 清空本地缓存
     */
    private void clearLocalCache() {
        cacheLock.lock();
        try {
            addressCache.clear();
            addressByLevelCache.clear();
            childrenCache.clear();
            localCacheVersion = 0L;
            log.info("本地缓存已清空");
        } finally {
            cacheLock.unlock();
        }
    }

    /**
     * 手动刷新缓存（集群安全）
     */
    public void refreshCache() {
        log.info("开始手动刷新缓存...");

        // 1. 清理Redis缓存
        redisTemplate.delete(REDIS_ADDRESS_ALL_KEY);
        redisTemplate.delete(REDIS_CACHE_VERSION_KEY);

        // 2. 发送集群缓存失效通知
        redisTemplate.convertAndSend(REDIS_ADDRESS_EXPIRE_CHANNEL, "manual_refresh_" + System.currentTimeMillis());

        // 3. 重新加载缓存
        loadCache();

        log.info("缓存刷新完成");
    }

    // =================== 对外接口方法 ===================

    /**
     * 根据区域ID获取地址信息
     */
    public JdAddress getAddressByAreaId(Long areaId) {
        if (addressCache.isEmpty()) {
            loadCache();
        }
        return addressCache.get(areaId);
    }

    /**
     * 根据层级获取所有地址信息
     */
    public Map<Long, JdAddress> getAddressByLevel(Integer level) {
        if (addressByLevelCache.isEmpty()) {
            loadCache();
        }
        return addressByLevelCache.getOrDefault(level, new ConcurrentHashMap<>());
    }

    /**
     * 获取子区域列表
     */
    public List<JdAddress> getChildren(Long parentId) {
        if (childrenCache.isEmpty()) {
            loadCache();
        }
        return childrenCache.getOrDefault(parentId, Collections.emptyList());
    }

    /**
     * 获取所有省份
     */
    public Map<Long, JdAddress> getAllProvinces() {
        return getAddressByLevel(1);
    }

    /**
     * 获取所有城市
     */
    public Map<Long, JdAddress> getAllCities() {
        return getAddressByLevel(2);
    }

    /**
     * 获取所有区县
     */
    public Map<Long, JdAddress> getAllAreas() {
        return getAddressByLevel(3);
    }
}
