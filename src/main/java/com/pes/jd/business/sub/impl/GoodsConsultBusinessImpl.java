package com.pes.jd.business.sub.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.GoodsConsultBusiness;
import com.pes.jd.business.sub.ShopGoodsInfoBussiness;
import com.pes.jd.dao.sub.CsConsultGoodsDao;
import com.pes.jd.dao.sub.GoodsConsultSummaryDao;
import com.pes.jd.dao.sub.ShopGoodSkuDao;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCategoryTree;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.CustConsultGoodsV2VO;
import com.pes.jd.model.VO.CustConsultGoodsVO;
import com.pes.jd.model.VO.GoodsConsultSummaryV2VO;
import com.pes.jd.model.VO.GoodsConsultSummaryVO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.office.excel.ExportExcel;
import com.pes.jd.office.param.ExeclColumnParam;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.lang.String.valueOf;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2019年2月25日 下午5:49:14
 * @since
 */
@Service
public class GoodsConsultBusinessImpl implements GoodsConsultBusiness {

	@Autowired
	private GoodsConsultSummaryDao goodsConsultSummaryDao;

	@Autowired
	private CsConsultGoodsDao csConsultGoodsDao;

	@Autowired
	private ShopGoodSkuDao shopGoodSkuDao;

    @Autowired
    private ShopGoodsInfoBussiness shopGoodsInfoBussiness;


	@Override
	public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(ShopCommonParam shop,
			List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList) throws SQLException {
		if (CollectionUtils.isEmpty(csNickList)) {
			return new ArrayList<>(0);
		}
		List<GoodsConsultSummaryDTO> list = goodsConsultSummaryDao
				.selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(shop, skuLst, startDate, endDate, csNickList);
		list.removeAll(Collections.singleton(null));
		List<GoodsConsultSummaryDTO> goodsConsultSummaryList = Lists.newArrayList();

		if (CollectionUtils.isNotEmpty(list)) {
			for (GoodsConsultSummaryDTO goods : list) {
				ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(goods.getSkuId());
				shopGoodsDTO.setCategoryId(goods.getCategoryId());
				shopGoodsDTO.setImageUrl(goods.getImageUrl());
				shopGoodsDTO.setSkuName(goods.getSkuName());
				shopGoodsDTO.setShopId(goods.getShopId());
				goods.setGoodsDTO(shopGoodsDTO);
				goods.setBuyPercent(goods.getConsultNum() > 0
						? (goods.getPurchasesBuyerNum() / (double) goods.getConsultNum() * 100.0) : 0.0);
				goodsConsultSummaryList.add(goods);
			}
		}
		return goodsConsultSummaryList;
	}

    @Override
    public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(ShopCommonParam shop,
                                                                                            List<Long> skuLst,Long categoryId,Date startDate, Date endDate, List<String> csNickList) throws SQLException, IllegalAccessException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<>(0);
        }
        List<Long> categoryAndAllSubcategoryIds = null;
        if (categoryId != null) {
            categoryAndAllSubcategoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
        }
        List<GoodsConsultSummaryDTO> list = goodsConsultSummaryDao
                .selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(shop, skuLst,categoryAndAllSubcategoryIds, startDate, endDate, csNickList);
        list.removeAll(Collections.singleton(null));
        List<GoodsConsultSummaryDTO> goodsConsultSummaryList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(list)) {
            for (GoodsConsultSummaryDTO goods : list) {
                ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(goods.getSkuId());
                shopGoodsDTO.setCategoryId(goods.getCategoryId());
                shopGoodsDTO.setImageUrl(goods.getImageUrl());
                shopGoodsDTO.setSkuName(goods.getSkuName());
                shopGoodsDTO.setShopId(goods.getShopId());
                goods.setGoodsDTO(shopGoodsDTO);
                goods.setBuyPercent(goods.getConsultNum() > 0
                        ? (goods.getPurchasesBuyerNum() / (double) goods.getConsultNum() * 100.0) : 0.0);
                goodsConsultSummaryList.add(goods);
            }
        }
        return goodsConsultSummaryList;
    }



    @Override
    public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(ShopCommonParam shop, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList) throws SQLException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<>(0);
        }
        List<GoodsConsultSummaryV2DTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(shop, skuLst, startDate, endDate, csNickList);

        List<GoodsConsultSummaryV2DTO> returnList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(list)) {
            for (GoodsConsultSummaryV2DTO goods : list) {
                ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(goods.getSkuId());
                shopGoodsDTO.setCategoryId(goods.getCategoryId());
                shopGoodsDTO.setImageUrl(goods.getImageUrl());
                shopGoodsDTO.setSkuName(goods.getSkuName());
                shopGoodsDTO.setShopId(goods.getShopId());
                goods.setGoodsDTO(shopGoodsDTO);
                goods.setBuyPercent(goods.getEnquiryNum() > 0 ? (goods.getPayNum() / (double) goods.getEnquiryNum() * 100.0) : 0.0);
                returnList.add(goods);
            }
        }
        return returnList;
    }

    @Override
    public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(ShopCommonParam shop, List<Long> skuLst, Long categoryId, Date startDate, Date endDate, List<String> csNickList) throws IllegalAccessException, SQLException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<>(0);
        }

        List<Long> categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);

        List<GoodsConsultSummaryV2DTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(shop, skuLst, categoryIds, startDate, endDate, csNickList);

        List<GoodsConsultSummaryV2DTO> returnList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(list)) {
            for (GoodsConsultSummaryV2DTO goods : list) {
                ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(goods.getSkuId());
                shopGoodsDTO.setCategoryId(goods.getCategoryId());
                shopGoodsDTO.setImageUrl(goods.getImageUrl());
                shopGoodsDTO.setSkuName(goods.getSkuName());
                shopGoodsDTO.setShopId(goods.getShopId());
                goods.setGoodsDTO(shopGoodsDTO);
                goods.setBuyPercent(goods.getEnquiryNum() > 0 ? (goods.getPayNum() / (double) goods.getEnquiryNum() * 100.0) : 0.0);
                returnList.add(goods);
            }
        }
        return returnList;
    }

	@Override
	public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuId(ShopCommonParam shop, Long skuId,
			Date startDate, Date endDate, List<String> csNickLst) throws SQLException {
		List<GoodsConsultSummaryDTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryByShopIdByDateByskuId(shop,
				skuId, startDate, endDate, csNickLst);
		List<GoodsConsultSummaryDTO> goodsConsultSummaryList = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(list)) {
			for (GoodsConsultSummaryDTO goodsConsultSummary : list) {
				goodsConsultSummary.setBuyPercent(
						goodsConsultSummary.getConsultNum() > 0 ? (goodsConsultSummary.getPurchasesBuyerNum()
								/ (double) goodsConsultSummary.getConsultNum() * 100.0) : 0.0);
				goodsConsultSummaryList.add(goodsConsultSummary);
			}
		}
		return goodsConsultSummaryList;
	}


    @Override
    public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV3(ShopCommonParam shop, Long skuId, Long categoryId,
                                                                                         Date startDate, Date endDate, List<String> csNickLst) throws SQLException, IllegalAccessException {
        List<Long> categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
        List<GoodsConsultSummaryDTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryByShopIdByDateByskuIdV3(shop,
                skuId, categoryIds, startDate, endDate, csNickLst);
        List<GoodsConsultSummaryDTO> goodsConsultSummaryList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (GoodsConsultSummaryDTO goodsConsultSummary : list) {
                goodsConsultSummary.setBuyPercent(
                        goodsConsultSummary.getConsultNum() > 0 ? (goodsConsultSummary.getPurchasesBuyerNum()
                                / (double) goodsConsultSummary.getConsultNum() * 100.0) : 0.0);
                goodsConsultSummaryList.add(goodsConsultSummary);
            }
        }
        return goodsConsultSummaryList;
    }

    @Override
    public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV2(ShopCommonParam shop, Long skuId,
                                                                                       Date startDate, Date endDate, List<String> csNickLst) throws SQLException {
        List<GoodsConsultSummaryV2DTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryByShopIdByDateByskuIdV2(shop,
                skuId, startDate, endDate, csNickLst);
        return list;
//        List<GoodsConsultSummaryV2DTO> goodsConsultSummaryList = Lists.newArrayList();
//        if (CollectionUtils.isNotEmpty(list)) {
//            for (GoodsConsultSummaryV2DTO goodsConsultSummary : list) {
//                goodsConsultSummary.setBuyPercent(
//                        goodsConsultSummary.getReceiveNum() > 0 ? (goodsConsultSummary.getPayNum()
//                                / (double) goodsConsultSummary.getReceiveNum() * 100.0) : 0.0);
//                goodsConsultSummaryList.add(goodsConsultSummary);
//            }
//        }
//        return goodsConsultSummaryList;
    }

	@Override
	public List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(ShopCommonParam shop,
			Integer result, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList, String customer)
			throws SQLException {
		if (CollectionUtils.isEmpty(csNickList)) {
			return new ArrayList<CustConsultGoodsDTO>(0);
		}
		List<CustConsultGoodsDTO> custConsultGoodsDTOs = Lists.newArrayList();
		List<CustConsultGoodsDTO> consultGoodsList = csConsultGoodsDao
				.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(shop, result, skuLst, startDate, endDate,
						csNickList, customer);
		if (CollectionUtils.isNotEmpty(consultGoodsList)) {
			for (CustConsultGoodsDTO consultGoodsDTO : consultGoodsList) {
				consultGoodsDTO.setStartDate(startDate);
				consultGoodsDTO.setEndDate(endDate);
				consultGoodsDTO.setShopId(shop.getShopId());
				custConsultGoodsDTOs.add(consultGoodsDTO);
			}
		}
		return custConsultGoodsDTOs;
	}

    @Override
    public List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV3(ShopCommonParam shop,
                                                                                                 Integer result, List<Long> skuLst, Long categoryId,Date startDate, Date endDate, List<String> csNickList, String customer)
            throws SQLException, IllegalAccessException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<CustConsultGoodsDTO>(0);
        }
        List<Long> categoryIds = null;
        if (categoryId != null) {
            categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
        }

        List<CustConsultGoodsDTO> custConsultGoodsDTOs = Lists.newArrayList();
        List<CustConsultGoodsDTO> consultGoodsList = csConsultGoodsDao
                .selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV3(shop, result, skuLst,categoryIds, startDate, endDate,
                        csNickList, customer);
        if (CollectionUtils.isNotEmpty(consultGoodsList)) {
            for (CustConsultGoodsDTO consultGoodsDTO : consultGoodsList) {
                consultGoodsDTO.setStartDate(startDate);
                consultGoodsDTO.setEndDate(endDate);
                consultGoodsDTO.setShopId(shop.getShopId());
                custConsultGoodsDTOs.add(consultGoodsDTO);
            }
        }
        return custConsultGoodsDTOs;
    }




    @Override
    public List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(ShopCommonParam shop,
                                                                                                 Integer result, List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList, String customer) throws SQLException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<CustConsultGoodsV2DTO>(0);
        }
        List<CustConsultGoodsV2DTO> custConsultGoodsDTOs = Lists.newArrayList();
        List<CustConsultGoodsV2DTO> consultGoodsList = csConsultGoodsDao
                .selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(shop, result, skuLst, startDate, endDate,
                        csNickList, customer);
        if (CollectionUtils.isNotEmpty(consultGoodsList)) {
            for (CustConsultGoodsV2DTO consultGoodsDTO : consultGoodsList) {
                consultGoodsDTO.setStartDate(startDate);
                consultGoodsDTO.setEndDate(endDate);
                consultGoodsDTO.setShopId(shop.getShopId());
                custConsultGoodsDTOs.add(consultGoodsDTO);
            }
        }
        return custConsultGoodsDTOs;
    }


    @Override
    public List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4(ShopCommonParam shop,
                                                                                                     Integer result, List<Long> skuLst, Long categoryId, Date startDate, Date endDate, List<String> csNickList, String customer) throws SQLException, IllegalAccessException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<CustConsultGoodsV2DTO>(0);
        }
        List<Long> categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
        List<CustConsultGoodsV2DTO> custConsultGoodsDTOs = Lists.newArrayList();
        List<CustConsultGoodsV2DTO> consultGoodsList = csConsultGoodsDao
                .selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4(shop, result, skuLst, categoryIds, startDate, endDate,
                        csNickList, customer);
        if (CollectionUtils.isNotEmpty(consultGoodsList)) {
            for (CustConsultGoodsV2DTO consultGoodsDTO : consultGoodsList) {
                consultGoodsDTO.setStartDate(startDate);
                consultGoodsDTO.setEndDate(endDate);
                consultGoodsDTO.setShopId(shop.getShopId());
                custConsultGoodsDTOs.add(consultGoodsDTO);
            }
        }
        return custConsultGoodsDTOs;
    }

	private Double DecimalFormatForDouble(Double value) {
		DecimalFormat df = new DecimalFormat("#.00");
		String str = df.format(value);
		return Double.valueOf(str);
	}

	/**
	 * 商品咨询汇总导出
	 */
	@Override
	public void exportGoodsConsultSummary(OutputStream out, String jsonParam) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(jsonParam);
		ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
		ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
				shopQuery.getDbName());
		Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
		JSONArray array = jsonObject.getJSONArray("skuLst");
		List<Long> skuLst = array.toJavaList(Long.class);

        /************确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
		/************确定维度 end ***************/

		List<GoodsConsultSummaryDTO> goodsConsultSummaryList = goodsConsultSummaryDao
				.selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(shopCommonParam, newSkuLst, startDate, endDate,
						shopQuery.getCsNickLst());
		ExeclTableParam<GoodsConsultSummaryVO> tableParam = new ExeclTableParam<>();
		List<ExeclColumnParam> columnParams = Lists.newArrayList();
		List<GoodsConsultSummaryVO> voLst = Lists.newArrayList();
		for (GoodsConsultSummaryDTO consult : goodsConsultSummaryList) {
			GoodsConsultSummaryVO vo = new GoodsConsultSummaryVO();
			vo.setSkuId(valueOf(consult.getSkuId()));
			vo.setSkuName(consult.getSkuName());
			vo.setConsultNum(Double.valueOf(consult.getConsultNum()));
			vo.setPurchasesBuyerNum(Double.valueOf(consult.getPurchasesBuyerNum()));
			vo.setBuyPercent(valueOf(
                    consult.getConsultNum() > 0
                            ? DecimalFormatForDouble(
                            consult.getPurchasesBuyerNum() / Double.valueOf(consult.getConsultNum()) * 100.0)
                            : 0.0));
			vo.setPurchasesGoodsNum(Double.valueOf(consult.getPurchasesGoodsNum()));
			vo.setPurchasesAmount(consult.getPurchasesAmount());
			voLst.add(vo);
		}

//		添加均值和汇总
        addAvgAndCount(goodsConsultSummaryList,voLst);

		tableParam.setData(voLst);
		tableParam.setColumnParams(columnParams);
		columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
		columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
		columnParams.add(new ExeclColumnParam("咨询人数", "consultNum"));
		columnParams.add(new ExeclColumnParam("购买人数", "purchasesBuyerNum"));
        columnParams.add(new ExeclColumnParam("购买件数（件）", "purchasesGoodsNum"));
        columnParams.add(new ExeclColumnParam("购买占比（%）", "buyPercent"));
		columnParams.add(new ExeclColumnParam("购买金额（元）", "purchasesAmount", "%.2f"));

		String title = "商品咨询汇总";
		ExportExcel exort = new ExportExcel();
		exort.execlExport(title, tableParam, out);
	}

    /**
     * 商品咨询汇总导出
     */
    @Override
    public void exportGoodsConsultSummaryV3(OutputStream out, String jsonParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
                shopQuery.getDbName());
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        JSONArray array = jsonObject.getJSONArray("skuLst");
        List<Long> skuLst = array.toJavaList(Long.class);
        String categoryId = jsonObject.getString("categoryId");
        Long categoryIdtemp = null;
        if (StringUtils.isNotBlank(categoryId)) {
            categoryIdtemp = Long.valueOf(categoryId);
        }


        /************确定维度 start ***************/
        List<Long> newSkuLst = getSkuIdsByDimension(jsonObject, shopCommonParam, skuLst);
        /************确定维度 end ***************/

        List<GoodsConsultSummaryDTO> goodsConsultSummaryList = selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(shopCommonParam, newSkuLst, categoryIdtemp, startDate, endDate, shopQuery.getCsNickLst());

        ExeclTableParam<GoodsConsultSummaryVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        List<GoodsConsultSummaryVO> voLst = Lists.newArrayList();

        if (StringUtils.isBlank(categoryId)) {
            for (GoodsConsultSummaryDTO consult : goodsConsultSummaryList) {
                GoodsConsultSummaryVO vo = new GoodsConsultSummaryVO();
                vo.setSkuId(valueOf(consult.getSkuId()));
                vo.setSkuName(consult.getSkuName());
                vo.setConsultNum(Double.valueOf(consult.getConsultNum()));
                vo.setPurchasesBuyerNum(Double.valueOf(consult.getPurchasesBuyerNum()));
                vo.setBuyPercent(valueOf(
                        consult.getConsultNum() > 0
                                ? DecimalFormatForDouble(
                                consult.getPurchasesBuyerNum() / Double.valueOf(consult.getConsultNum()) * 100.0)
                                : 0.0));
                vo.setPurchasesGoodsNum(Double.valueOf(consult.getPurchasesGoodsNum()));
                vo.setPurchasesAmount(consult.getPurchasesAmount());
                voLst.add(vo);
            }

//		添加均值和汇总
            addAvgAndCount(goodsConsultSummaryList, voLst);

            tableParam.setData(voLst);
            tableParam.setColumnParams(columnParams);
            columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
            columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
            columnParams.add(new ExeclColumnParam("咨询人数", "consultNum"));
            columnParams.add(new ExeclColumnParam("购买人数", "purchasesBuyerNum"));
            columnParams.add(new ExeclColumnParam("购买件数（件）", "purchasesGoodsNum"));
            columnParams.add(new ExeclColumnParam("购买占比（%）", "buyPercent"));
            columnParams.add(new ExeclColumnParam("购买金额（元）", "purchasesAmount", "%.2f"));

            String title = "商品咨询汇总";
            ExportExcel exort = new ExportExcel();
            exort.execlExport(title, tableParam, out);
        } else {
            List<ShopCategoryTree> shopCategoryTrees = shopGoodsInfoBussiness.selectShopCategoryAndGoodsByShopIdV2(shopCommonParam);

            List<ShopCategoryTreeDTO> dtoTreeList = new ArrayList<>();
            dtoTreeList = JacksonUtils.objTolist(shopCategoryTrees, ShopCategoryTreeDTO.class);

            ApiResponse apiResponse = processCategoryData(dtoTreeList, Long.valueOf(categoryId), goodsConsultSummaryList);
            GoodsConsultSummaryAvgDTO avg = (GoodsConsultSummaryAvgDTO) apiResponse.getData().get("avg");
            GoodsConsultSummaryDTO count = (GoodsConsultSummaryDTO) apiResponse.getData().get("count");
            Object goodsConsultList = apiResponse.getData().get("goodsConsultList");
            List<GoodsConsultSummaryDTO> goodsConsultSummaryDTOS = JacksonUtils.objTolist(goodsConsultList, GoodsConsultSummaryDTO.class);

            // 遍历商品咨询数据并添加到Excel数据列表
            for (GoodsConsultSummaryDTO consult : goodsConsultSummaryDTOS) {
                GoodsConsultSummaryVO vo = new GoodsConsultSummaryVO();
                vo.setCategoryName(consult.getCategoryName());
                vo.setConsultNum(Double.valueOf(consult.getConsultNum()));
                vo.setPurchasesBuyerNum(Double.valueOf(consult.getPurchasesBuyerNum()));
                vo.setBuyPercent(valueOf(
                        consult.getConsultNum() > 0
                                ? DecimalFormatForDouble(
                                consult.getPurchasesBuyerNum() / Double.valueOf(consult.getConsultNum()) * 100.0)
                                : 0.0));
                vo.setPurchasesGoodsNum(Double.valueOf(consult.getPurchasesGoodsNum()));
                vo.setPurchasesAmount(consult.getPurchasesAmount());
                voLst.add(vo);
            }

            // 添加均值和汇总（直接从avg和count对象中获取）
            GoodsConsultSummaryVO avgVO = new GoodsConsultSummaryVO();
            avgVO.setCategoryName("平均");
            avgVO.setConsultNum(avg.getConsultNum());
            avgVO.setPurchasesBuyerNum(avg.getPurchasesBuyerNum());
            avgVO.setPurchasesGoodsNum(avg.getPurchasesGoodsNum());
            avgVO.setBuyPercent(String.format("%.2f", avg.getBuyPercent()));
            avgVO.setPurchasesAmount(avg.getPurchasesAmount());
            voLst.add(avgVO);

            GoodsConsultSummaryVO countVO = new GoodsConsultSummaryVO();
            countVO.setCategoryName("汇总");
            countVO.setConsultNum(Double.valueOf(count.getConsultNum()));
            countVO.setPurchasesBuyerNum(Double.valueOf(count.getPurchasesBuyerNum()));
            countVO.setPurchasesGoodsNum(Double.valueOf(count.getPurchasesGoodsNum()));
            countVO.setBuyPercent("--");
            countVO.setPurchasesAmount(count.getPurchasesAmount());
            voLst.add(countVO);


            tableParam.setData(voLst);
            tableParam.setColumnParams(columnParams);
            columnParams.add(new ExeclColumnParam("类目名称", "categoryName"));
            columnParams.add(new ExeclColumnParam("咨询人数", "consultNum"));
            columnParams.add(new ExeclColumnParam("购买人数", "purchasesBuyerNum"));
            columnParams.add(new ExeclColumnParam("购买件数（件）", "purchasesGoodsNum"));
            columnParams.add(new ExeclColumnParam("购买占比（%）", "buyPercent"));
            columnParams.add(new ExeclColumnParam("购买金额（元）", "purchasesAmount", "%.2f"));

            String title = "类目商品咨询汇总";
            ExportExcel exort = new ExportExcel();
            exort.execlExport(title, tableParam, out);

        }

    }

    //    均值
    private void calAvg(GoodsConsultSummaryAvgDTO avg, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        double num = consultSummaryDTOs.size() * 1.0;
        //咨询人数
        int consultNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
        avg.setConsultNum(consultNum / num);//咨询人数
        avg.setPurchasesBuyerNum(buyerNum / num);//购买人数
        avg.setPurchasesGoodsNum(consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum() / num);//购买件数
        avg.setBuyPercent(consultNum > 0 ? ((buyerNum * 1.0 / consultNum) * 100.0) : 0.0);//购买占比
        avg.setPurchasesAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum() / num);//购买金额
    }

    private void calCount(GoodsConsultSummaryDTO count, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        //咨询人数
        int consultNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
        count.setConsultNum(consultNum);//咨询人数
        count.setPurchasesBuyerNum(buyerNum);//购买人数
        count.setPurchasesGoodsNum(consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum());//购买件数
        count.setPurchasesAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum());//购买金额
    }


    public ApiResponse processCategoryData(List<ShopCategoryTreeDTO> categoryTree, Long categoryId, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        // 初始化类目映射
        Map<Long, ShopCategoryTreeDTO> categoryMap = new HashMap<>();
        buildCategoryMap(categoryTree, categoryMap);

        List<GoodsConsultSummaryDTO> result = new ArrayList<>();
        // 找到指定的类目
        ShopCategoryTreeDTO targetCategory = categoryMap.get(categoryId);

        // 根据类目层级处理数据
        if (targetCategory.getLevel() == 1L || targetCategory.getLevel() == 2L) {
            for (ShopCategoryTreeDTO secondLevel : targetCategory.getChildrens()) {
                result.add(aggregateCategory(secondLevel, consultSummaryDTOs));
            }
        }
        if (targetCategory.getLevel() == 3L) {
            // 三级类目
            result.add(aggregateCategory(targetCategory, consultSummaryDTOs));
        }
        Map<String, Object> data = Maps.newHashMap();
        GoodsConsultSummaryAvgDTO avg = new GoodsConsultSummaryAvgDTO();
        GoodsConsultSummaryDTO count = new GoodsConsultSummaryDTO();
        data.put("avg", avg);
        data.put("count", count);
        data.put("goodsConsultList", result);
        calAvg(avg, result);
        calCount(count, result);
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
    }


    private void buildCategoryMap(List<ShopCategoryTreeDTO> categories, Map<Long, ShopCategoryTreeDTO> categoryMap) {
        for (ShopCategoryTreeDTO category : categories) {
            categoryMap.put(category.getCategoryId(), category);
            if (category.getChildrens() != null) {
                buildCategoryMap(category.getChildrens(), categoryMap);
            }
        }
    }

    private GoodsConsultSummaryDTO aggregateCategory(ShopCategoryTreeDTO category, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        GoodsConsultSummaryDTO aggregated = new GoodsConsultSummaryDTO();
        aggregated.setCategoryId(category.getCategoryId());
        aggregated.setCategoryName(category.getName());
        // 创建一个 Map 来存储 categoryId 到 GoodsConsultSummaryDTO 列表的映射
        Map<Long, List<GoodsConsultSummaryDTO>> dtoMap = consultSummaryDTOs.stream()
                .collect(Collectors.groupingBy(GoodsConsultSummaryDTO::getCategoryId));
        // 递归查找属于当前分类及其子分类的所有 DTOs
        List<GoodsConsultSummaryDTO> categoryDTOs = findCategoryDTOs(category, dtoMap);

        // 聚合数据
        aggregated.setConsultNum(categoryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum());
        aggregated.setPurchasesBuyerNum(categoryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum());
        aggregated.setPurchasesGoodsNum(categoryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum());
        aggregated.setPurchasesAmount(categoryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum());

        // 计算购买占比
        if (aggregated.getConsultNum() > 0) {
            double buyPercent = (double) aggregated.getPurchasesBuyerNum() / aggregated.getConsultNum() * 100;
            aggregated.setBuyPercent(buyPercent);
        } else {
            aggregated.setBuyPercent(0.0);
        }

        return aggregated;
    }

    private List<GoodsConsultSummaryDTO> findCategoryDTOs(ShopCategoryTreeDTO category, Map<Long, List<GoodsConsultSummaryDTO>> dtoMap) {
        List<GoodsConsultSummaryDTO> result = new ArrayList<>();

        // 添加当前分类的所有 DTOs（如果存在）
        List<GoodsConsultSummaryDTO> currentDTOs = dtoMap.get(category.getCategoryId());
        if (currentDTOs != null) {
            result.addAll(currentDTOs);
        }
        // 递归处理子分类
        if (category.getChildrens() != null) {
            for (ShopCategoryTreeDTO child : category.getChildrens()) {
                result.addAll(findCategoryDTOs(child, dtoMap));
            }
        }
        return result;
    }



    /**
     * 商品咨询汇总导出
     */
    @Override
    public void exportGoodsConsultSummaryV2(OutputStream out, String jsonParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
                shopQuery.getDbName());
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        JSONArray array = jsonObject.getJSONArray("skuLst");
        List<Long> skuLst = array.toJavaList(Long.class);

        /************确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
        /************确定维度 end ***************/

        List<GoodsConsultSummaryV2DTO> goodsConsultSummaryList = goodsConsultSummaryDao
                .selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(shopCommonParam, newSkuLst, startDate, endDate,
                        shopQuery.getCsNickLst());
        ExeclTableParam<GoodsConsultSummaryV2VO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        List<GoodsConsultSummaryV2VO> voLst = Lists.newArrayList();
        for (GoodsConsultSummaryV2DTO consult : goodsConsultSummaryList) {
            GoodsConsultSummaryV2VO vo = new GoodsConsultSummaryV2VO();
            vo.setSkuId(valueOf(consult.getSkuId()));
            vo.setSkuName(consult.getSkuName());
            vo.setReceiveNum(Double.valueOf(consult.getReceiveNum()));
            vo.setEnquiryNum(Double.valueOf(consult.getEnquiryNum()));
            vo.setPayNum(Double.valueOf(consult.getPayNum()));
            vo.setBuyPercent(valueOf(
                    (consult.getReceiveNum() > 0 && consult.getEnquiryNum() > 0)
                            ? DecimalFormatForDouble(
                            consult.getPayNum() / Double.valueOf(consult.getEnquiryNum()) * 100.0)
                            : 0.0));
            vo.setPayGoodsNum(Double.valueOf(consult.getPayGoodsNum()));
            vo.setPayAmount(consult.getPayAmount());
            voLst.add(vo);
        }

//		添加均值和汇总
        addAvgAndCountV2(goodsConsultSummaryList,voLst);

        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
        columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
        columnParams.add(new ExeclColumnParam("接待人数", "receiveNum"));
        columnParams.add(new ExeclColumnParam("询单人数", "enquiryNum"));
        columnParams.add(new ExeclColumnParam("付款人数", "payNum"));
        columnParams.add(new ExeclColumnParam("询单->付款成功率", "buyPercent"));
        columnParams.add(new ExeclColumnParam("付款件数", "payGoodsNum"));
        columnParams.add(new ExeclColumnParam("金额", "payAmount", "%.2f"));

        String title = "商品咨询汇总（新）";
        ExportExcel exort = new ExportExcel();
        exort.execlExport(title, tableParam, out);
    }

    private void addAvgAndCount(List<GoodsConsultSummaryDTO> goodsConsultSummaryList, List<GoodsConsultSummaryVO> voLst) {
        addCount(voLst, goodsConsultSummaryList);
        addAvg(voLst, goodsConsultSummaryList);
    }

    private void addAvgAndCountV2(List<GoodsConsultSummaryV2DTO> goodsConsultSummaryList, List<GoodsConsultSummaryV2VO> voLst) {
        addCountV2(voLst, goodsConsultSummaryList);
        addAvgV2(voLst, goodsConsultSummaryList);
    }


    //    汇总 不需要展示率
    private void addCount(List<GoodsConsultSummaryVO> voLst, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        GoodsConsultSummaryVO count = new GoodsConsultSummaryVO();
        voLst.add(count);
        count.setSkuId("汇总");
        count.setSkuName("--");
        count.setConsultNum(0.0D);
        count.setPurchasesBuyerNum(0.0D);
        count.setPurchasesGoodsNum(0.0D);
        count.setBuyPercent("0");
        count.setPurchasesAmount(0.0D);
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        //咨询人数
        int consultNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
        count.setConsultNum((double) consultNum);//咨询人数
        count.setPurchasesBuyerNum((double) buyerNum);//购买人数
        count.setBuyPercent("--");//购买占比
        //购买件数
        count.setPurchasesGoodsNum((double) consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum());
        //购买金额
        count.setPurchasesAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum());
    }

    //    汇总 不需要展示率
    private void addCountV2(List<GoodsConsultSummaryV2VO> voLst, List<GoodsConsultSummaryV2DTO> consultSummaryDTOs) {
        GoodsConsultSummaryV2VO count = new GoodsConsultSummaryV2VO();
        voLst.add(count);
        count.setSkuId("汇总");
        count.setSkuName("--");
        count.setReceiveNum(0.0D);
        count.setEnquiryNum(0.0D);
        count.setPayNum(0.0D);
        count.setPayGoodsNum(0.0D);
        count.setBuyPercent("0");
        count.setPayAmount(0.0D);
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        //咨询人数
        int receiveNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getReceiveNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayNum).sum();
        int enquiryNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getEnquiryNum).sum();
        count.setReceiveNum((double) receiveNum);//咨询人数
        count.setPayNum((double) buyerNum);//购买人数
        count.setEnquiryNum((double) enquiryNum);//购买人数
        count.setBuyPercent("--");//购买占比
        //购买件数
        count.setPayGoodsNum((double) consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayGoodsNum).sum());
        //购买金额
        count.setPayAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryV2DTO::getPayAmount).sum());
    }

    //    均值
    private void addAvg(List<GoodsConsultSummaryVO> voLst, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        GoodsConsultSummaryVO count = new GoodsConsultSummaryVO();
        voLst.add(count);
        count.setSkuId("均值");
        count.setSkuName("--");
        count.setConsultNum(0.0D);
        count.setPurchasesBuyerNum(0.0D);
        count.setPurchasesGoodsNum(0.0D);
        count.setBuyPercent("0");
        count.setPurchasesAmount(0.0D);
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        double num = consultSummaryDTOs.size() * 1.0;
        //咨询人数
        int consultNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
        count.setConsultNum(Double.valueOf(String.format("%.2f", consultNum / num)));//咨询人数
        count.setPurchasesBuyerNum(Double.valueOf(String.format("%.2f", buyerNum / num)));//购买人数
        count.setPurchasesGoodsNum(Double.valueOf(String.format("%.2f",consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum() / num)));//购买件数
        count.setBuyPercent(String.format("%.2f", (consultNum > 0 ? ((buyerNum * 1.0 / consultNum) * 100.0) : 0.0)));//购买占比
        count.setPurchasesAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum() / num);//购买金额
    }
    //    均值
    private void addAvgV2(List<GoodsConsultSummaryV2VO> voLst, List<GoodsConsultSummaryV2DTO> consultSummaryDTOs) {
        GoodsConsultSummaryV2VO count = new GoodsConsultSummaryV2VO();
        voLst.add(count);
        count.setSkuId("均值");
        count.setSkuName("--");
        count.setReceiveNum(0.0D);
        count.setEnquiryNum(0.0D);
        count.setPayNum(0.0D);
        count.setPayGoodsNum(0.0D);
        count.setBuyPercent("0");
        count.setPayAmount(0.0D);
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        double num = consultSummaryDTOs.size() * 1.0;
        //咨询人数
        int receiveNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getReceiveNum).sum();
        int enquiryNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getEnquiryNum).sum();
        //购买人数
        int payNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayNum).sum();
        count.setReceiveNum(Double.valueOf(String.format("%.2f", receiveNum / num)));//接待人数
        count.setEnquiryNum(Double.valueOf(String.format("%.2f",  enquiryNum/ num)));//询单人数
        count.setPayNum(Double.valueOf(String.format("%.2f", payNum / num)));//购买人数
        count.setPayGoodsNum(Double.valueOf(String.format("%.2f",consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayGoodsNum).sum() / num)));//购买件数
        count.setBuyPercent(String.format("%.2f", (enquiryNum > 0 ? ((payNum * 1.0 / enquiryNum) * 100.0) : 0.0)));//购买占比
        count.setPayAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryV2DTO::getPayAmount).sum() / num);//购买金额
    }
    /****根据维度查询对应的sku***/
    private List<Long> getSkuIdsByDimension(JSONObject jsonObject, ShopCommonParam shopCommonParam, List<Long> skuLst) {
        Byte dimension = jsonObject.getByte("dimension");
        //获取spu维度下所有的sku
        if (dimension != null && dimension == 2) {//spu维度
            List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shopCommonParam, skuLst);
            return shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        }
        return skuLst;
    }

    /**
	 * 商品咨询明细导出
	 *
	 * @throws Exception
	 */
	@Override
	public void exportGoodsConsultDetail(OutputStream out, String jsonParam) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(jsonParam);
		ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
		ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
				shopQuery.getDbName());
		JSONArray array = jsonObject.getJSONArray("skuLst");
		List<Long> skuLst = array.toJavaList(Long.class);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));

		String customer = jsonObject.getString("customer");
		Integer result = jsonObject.getInteger("result");
		Map<String, String> csSimpleNickMap = JacksonUtils.json2map(jsonObject.getString("csSimpleNickMap"),
				String.class);

        /************确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
        /************确定维度 end ***************/

		List<CustConsultGoodsDTO> custConsultGoodsDTOs = csConsultGoodsDao
				.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(shopCommonParam, result, newSkuLst, startDate,
						endDate, shopQuery.getCsNickLst(), customer);
		ExeclTableParam<CustConsultGoodsVO> tableParam = new ExeclTableParam<>();
		List<ExeclColumnParam> columnParams = Lists.newArrayList();
		List<CustConsultGoodsVO> voLst = Lists.newArrayList();
		for (CustConsultGoodsDTO consult : custConsultGoodsDTOs) {
			CustConsultGoodsVO vo = new CustConsultGoodsVO();
			vo.setDate(consult.getDate());
			vo.setCustomer(consult.getCustomer());
			vo.setCsNick(consult.getCsNick());
			vo.setCsSimpleNick(csSimpleNickMap.get(consult.getCsNick()));
			vo.setSkuId(consult.getSkuId());
			vo.setSkuName(consult.getSkuName());
			vo.setResult(consult.getResult());
			voLst.add(vo);
		}
		tableParam.setData(voLst);
		tableParam.setColumnParams(columnParams);
		columnParams.add(new ExeclColumnParam("日期", "date", 1));
		columnParams.add(new ExeclColumnParam("顾客昵称", "customer"));
		columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
		columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
		columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
		columnParams.add(new ExeclColumnParam("咨询结果", "result"));

		String title = "商品咨询明细";
		ExportExcel exort = new ExportExcel();
		exort.execlExport(title, tableParam, out);
	}


    /**
     * 商品咨询明细导出
     *
     * @throws Exception
     */
    @Override
    public void exportGoodsConsultDetailV3(OutputStream out, String jsonParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
                shopQuery.getDbName());
        JSONArray array = jsonObject.getJSONArray("skuLst");
        List<Long> skuLst = array.toJavaList(Long.class);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));

        String customer = jsonObject.getString("customer");
        Integer result = jsonObject.getInteger("result");
        String categoryId = jsonObject.getString("categoryId");
        Long categoryIdtemp = null;
        if (StringUtils.isNotBlank(categoryId)) {
            categoryIdtemp = Long.valueOf(categoryId);
        }

        Map<String, String> csSimpleNickMap = JacksonUtils.json2map(jsonObject.getString("csSimpleNickMap"),
                String.class);

        /************确定维度 start ***************/
        List<Long> newSkuLst = getSkuIdsByDimension(jsonObject, shopCommonParam, skuLst);
        /************确定维度 end ***************/

        List<CustConsultGoodsDTO> custConsultGoodsDTOs = selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV3(shopCommonParam, result, newSkuLst, categoryIdtemp, startDate,
                endDate, shopQuery.getCsNickLst(), customer);


        ExeclTableParam<CustConsultGoodsVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        List<CustConsultGoodsVO> voLst = Lists.newArrayList();
        for (CustConsultGoodsDTO consult : custConsultGoodsDTOs) {
            CustConsultGoodsVO vo = new CustConsultGoodsVO();
            vo.setDate(consult.getDate());
            vo.setCustomer(consult.getCustomer());
            vo.setCsNick(consult.getCsNick());
            vo.setCsSimpleNick(csSimpleNickMap.get(consult.getCsNick()));
            vo.setSkuId(consult.getSkuId());
            vo.setSkuName(consult.getSkuName());
            vo.setResult(consult.getResult());
            voLst.add(vo);
        }
        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("日期", "date", 1));
        columnParams.add(new ExeclColumnParam("顾客昵称", "customer"));
        columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
        columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("咨询结果", "result"));

        String title = "商品咨询明细";
        ExportExcel exort = new ExportExcel();
        exort.execlExport(title, tableParam, out);
    }

    /**
     * 商品咨询明细导出
     *
     * @throws Exception
     */
    @Override
    public void exportGoodsConsultDetailV2(OutputStream out, String jsonParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
                shopQuery.getDbName());
        JSONArray array = jsonObject.getJSONArray("skuLst");
        List<Long> skuLst = array.toJavaList(Long.class);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));

        String customer = jsonObject.getString("customer");
        Integer result = jsonObject.getInteger("result");
        Map<String, String> csSimpleNickMap = JacksonUtils.json2map(jsonObject.getString("csSimpleNickMap"),
                String.class);

        /************确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
        /************确定维度 end ***************/

        List<CustConsultGoodsV2DTO> custConsultGoodsDTOs = csConsultGoodsDao
                .selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(shopCommonParam, result, newSkuLst, startDate,
                        endDate, shopQuery.getCsNickLst(), customer);
        ExeclTableParam<CustConsultGoodsV2VO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        List<CustConsultGoodsV2VO> voLst = Lists.newArrayList();
        for (CustConsultGoodsV2DTO consult : custConsultGoodsDTOs) {
            CustConsultGoodsV2VO vo = new CustConsultGoodsV2VO();
            vo.setDate(consult.getDate());
            vo.setCustomer(consult.getCustomer());
            vo.setCsNick(consult.getCsNick());
            vo.setCsSimpleNick(csSimpleNickMap.get(consult.getCsNick()));
            vo.setSkuId(consult.getSkuId());
            vo.setSkuName(consult.getSkuName());
            vo.setResult(consult.getResult());
            vo.setIsEnquiry(consult.getIsEnquiry());
            voLst.add(vo);
        }
        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("日期", "date", 1));
        columnParams.add(new ExeclColumnParam("顾客昵称", "customer"));
        columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
        columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("状态", "isEnquiry"));
        columnParams.add(new ExeclColumnParam("咨询结果", "result"));

        String title = "商品咨询明细（新）";
        ExportExcel exort = new ExportExcel();
        exort.execlExport(title, tableParam, out);
    }

    @Override
    public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickOfSpu(ShopCommonParam shop, String groupId, List<CsDTO> csLst, List<Long> wareIds, Date startDate, Date endDate, List<String> csNickList) throws SQLException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<>(0);
        }
        //获取spu维度下所有的sku
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
//        List<ShopGoodNameDTO> shopGoodNameDTOS = shopGoodsBusiness.selectShopGoodsByShopIdByWareIds(shop, wareIds);
        if (CollectionUtils.isEmpty(shopGoodsSkus)) {
            return new ArrayList<>(0);
        }
        //客服昵称map
//        Map<String, String> simpleNameMap=null;

//        if (CollectionUtils.isNotEmpty(csLst)) {
//            simpleNameMap = csLst.stream()
//                    .collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
//        }
        List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());

        List<GoodsConsultSummaryDTO> list = goodsConsultSummaryDao
                .selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(shop, skuLst, startDate, endDate, csNickList);
        list.removeAll(Collections.singleton(null));

        List<GoodsConsultSummaryDTO> resultSummaryOfSpu = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(list)) {
            List<GoodsConsultSummaryDTO> goodsConsultSummaryList = Lists.newArrayList();
            for (GoodsConsultSummaryDTO goods : list) {
                ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(goods.getSkuId());
                shopGoodsDTO.setCategoryId(goods.getCategoryId());
                shopGoodsDTO.setImageUrl(goods.getImageUrl());
                shopGoodsDTO.setSkuName(goods.getSkuName());
                shopGoodsDTO.setShopId(goods.getShopId());
                goods.setGoodsDTO(shopGoodsDTO);
                goods.setBuyPercent(goods.getConsultNum() > 0
                        ? (goods.getPurchasesBuyerNum() / (double) goods.getConsultNum() * 100.0) : 0.0);
                //赋值客服昵称
//                if (CollectionUtils.isNotEmpty(csLst)) {
//                    if (simpleNameMap != null) {
//                        goods.setCsSimpleNick(simpleNameMap.get(goods.getCsNick()));
//                        goods.setGroupId(groupId);
//                    }
//
//                }
                goodsConsultSummaryList.add(goods);
            }

            //Sku维度的数据封装成spu维度
            resultSummaryOfSpu = doHandlerVODomainOfSpu(goodsConsultSummaryList, shopGoodsSkus);
        }

        return resultSummaryOfSpu;
    }

    @Override
    public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickOfSpuV2(ShopCommonParam shop, String groupId, List<CsDTO> csLst, List<Long> wareIds, Date startDate, Date endDate, List<String> csNickList) throws SQLException {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<>(0);
        }
        //获取spu维度下所有的sku
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
//        List<ShopGoodNameDTO> shopGoodNameDTOS = shopGoodsBusiness.selectShopGoodsByShopIdByWareIds(shop, wareIds);
        if (CollectionUtils.isEmpty(shopGoodsSkus)) {
            return new ArrayList<>(0);
        }
        //客服昵称map
//        Map<String, String> simpleNameMap=null;

//        if (CollectionUtils.isNotEmpty(csLst)) {
//            simpleNameMap = csLst.stream()
//                    .collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
//        }
        List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());

        List<GoodsConsultSummaryV2DTO> list = goodsConsultSummaryDao
                .selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(shop, skuLst, startDate, endDate, csNickList);
        list.removeAll(Collections.singleton(null));

        List<GoodsConsultSummaryV2DTO> resultSummaryOfSpu = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(list)) {
            List<GoodsConsultSummaryV2DTO> goodsConsultSummaryList = Lists.newArrayList();
            for (GoodsConsultSummaryV2DTO goods : list) {
                ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(goods.getSkuId());
                shopGoodsDTO.setCategoryId(goods.getCategoryId());
                shopGoodsDTO.setImageUrl(goods.getImageUrl());
                shopGoodsDTO.setSkuName(goods.getSkuName());
                shopGoodsDTO.setShopId(goods.getShopId());
                goods.setGoodsDTO(shopGoodsDTO);
                goods.setBuyPercent(goods.getEnquiryNum() > 0
                        ? (goods.getPayNum() / (double) goods.getEnquiryNum() * 100.0) : 0.0);
                //赋值客服昵称
//                if (CollectionUtils.isNotEmpty(csLst)) {
//                    if (simpleNameMap != null) {
//                        goods.setCsSimpleNick(simpleNameMap.get(goods.getCsNick()));
//                        goods.setGroupId(groupId);
//                    }
//
//                }
                goodsConsultSummaryList.add(goods);
            }

            //Sku维度的数据封装成spu维度
            resultSummaryOfSpu = doHandlerVODomainOfSpuV2(goodsConsultSummaryList, shopGoodsSkus);
        }

        return resultSummaryOfSpu;
    }

    @Override
    public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIdOfSpu(ShopCommonParam shop, Long wareId, Date startDate, Date endDate, List<String> csNickLst) throws SQLException {
        List<Long> wareIds = new ArrayList<>();
        wareIds.add(wareId);
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
        List<Long> skuIds = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        List<GoodsConsultSummaryDTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryByShopIdByDateByskuIds(shop,
                skuIds, startDate, endDate, csNickLst);
        //单个客服的咨询详情
        Map<String, List<GoodsConsultSummaryDTO>> collect = list.stream().collect(groupingBy(GoodsConsultSummaryDTO::getCsNick));
        //spu维度汇总单个客服的数据
        List<GoodsConsultSummaryDTO> goodsConsultSummaryList = doHandlerVODomainOfSpu(collect,ele->{
            GoodsConsultSummaryDTO goodsConsultSummaryDTO = new GoodsConsultSummaryDTO();
            goodsConsultSummaryDTO.setCsNick(ele.getKey());
            List<GoodsConsultSummaryDTO> value = ele.getValue();
            //咨询人数
            int consultNum = value.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).reduce(0, Integer::sum);
            goodsConsultSummaryDTO.setConsultNum(consultNum);
            //购买人数
            int purchasesBuyerNum = value.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).reduce(0, Integer::sum);
            goodsConsultSummaryDTO.setPurchasesBuyerNum(purchasesBuyerNum);
            //购买件数
            int purchasesGoodsNum = value.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).reduce(0, Integer::sum);
            goodsConsultSummaryDTO.setPurchasesGoodsNum(purchasesGoodsNum);
            //购买金额
            double purchasesAmount = value.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).reduce(0, Double::sum);
            goodsConsultSummaryDTO.setPurchasesAmount(purchasesAmount);
            //购买占比
            goodsConsultSummaryDTO.setBuyPercent(
                    goodsConsultSummaryDTO.getConsultNum() > 0 ? (goodsConsultSummaryDTO.getPurchasesBuyerNum()
                            / (double) goodsConsultSummaryDTO.getConsultNum() * 100.0) : 0.0);
            return goodsConsultSummaryDTO;

        });
        return goodsConsultSummaryList;
    }

    @Override
    public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdOfSpuV2(ShopCommonParam shop, Long wareId, Date startDate, Date endDate, List<String> csNickLst) throws SQLException {
        List<Long> wareIds = new ArrayList<>();
        wareIds.add(wareId);
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
        List<Long> skuIds = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        List<GoodsConsultSummaryV2DTO> list = goodsConsultSummaryDao.selectGoodsConsultSummaryByShopIdByDateByskuIdsV2(shop,
                skuIds, startDate, endDate, csNickLst);
        //单个客服的咨询详情
        Map<String, List<GoodsConsultSummaryV2DTO>> collect = list.stream().collect(groupingBy(GoodsConsultSummaryV2DTO::getCsNick));
        //spu维度汇总单个客服的数据
        return doHandlerVODomainOfSpuV2(collect, ele->{
            GoodsConsultSummaryV2DTO goodsConsultSummaryDTO = new GoodsConsultSummaryV2DTO();
            goodsConsultSummaryDTO.setCsNick(ele.getKey());
            List<GoodsConsultSummaryV2DTO> value = ele.getValue();
            //咨询人数
            int receiveNum = value.stream().mapToInt(GoodsConsultSummaryV2DTO::getReceiveNum).reduce(0, Integer::sum);
            int enquiryNum = value.stream().mapToInt(GoodsConsultSummaryV2DTO::getEnquiryNum).reduce(0, Integer::sum);
            //购买人数
            int payNum = value.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayNum).reduce(0, Integer::sum);
            //购买件数
            int payGoodsNum = value.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayGoodsNum).reduce(0, Integer::sum);
            //购买金额
            double payAmount = value.stream().mapToDouble(GoodsConsultSummaryV2DTO::getPayAmount).reduce(0, Double::sum);
            goodsConsultSummaryDTO.setPayGoodsNum(payGoodsNum);
            goodsConsultSummaryDTO.setPayNum(payNum);
            goodsConsultSummaryDTO.setPayAmount(payAmount);
            goodsConsultSummaryDTO.setReceiveNum(receiveNum);
            goodsConsultSummaryDTO.setEnquiryNum(enquiryNum);
            //购买占比
            goodsConsultSummaryDTO.setBuyPercent(
                    goodsConsultSummaryDTO.getEnquiryNum() > 0 ? (goodsConsultSummaryDTO.getPayNum()
                            / (double) goodsConsultSummaryDTO.getEnquiryNum() * 100.0) : 0.0);
            return goodsConsultSummaryDTO;

        });
    }

    @Override//查询spu维度下所有的商品明细
    public List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuOfSpu(ShopCommonParam shop, List<CsDTO> csLst, Integer result, List<Long> wareIds, Date startDate, Date endDate, List<String> csNickLst, String customer) throws Exception {
        if (CollectionUtils.isEmpty(csNickLst)) {
            return new ArrayList<>(0);
        }
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
        List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());

        List<CustConsultGoodsDTO> consultGoodsList = csConsultGoodsDao
                .selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(shop, result, skuLst, startDate, endDate,
                        csNickLst, customer);
        List<CustConsultGoodsDTO> custConsultGoodsDTOs = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(consultGoodsList)) {
            for (CustConsultGoodsDTO consultGoodsDTO : consultGoodsList) {
                consultGoodsDTO.setStartDate(startDate);
                consultGoodsDTO.setEndDate(endDate);
                consultGoodsDTO.setShopId(shop.getShopId());
                custConsultGoodsDTOs.add(consultGoodsDTO);
            }
        }
        if(CollectionUtils.isNotEmpty(csLst)){
            Map<String, String> simpleNameMap = csLst.stream()
                    .collect(Collectors.toMap(CsDTO::getNick, cs -> cs.getCsSimpleNick()));
            for (CustConsultGoodsDTO consult : custConsultGoodsDTOs) {
                consult.setCsSimpleNick(simpleNameMap.get(consult.getCsNick().toLowerCase()));
            }
        }
        custConsultGoodsDTOs.sort(Comparator.comparing(CustConsultGoodsDTO::getDate));
        return custConsultGoodsDTOs;
    }

    @Override//查询spu维度下所有的商品明细
    public List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuOfSpuV2(ShopCommonParam shop, List<CsDTO> csLst, Integer result, List<Long> wareIds, Date startDate, Date endDate, List<String> csNickLst, String customer) throws Exception {
        if (CollectionUtils.isEmpty(csNickLst)) {
            return new ArrayList<>(0);
        }
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
        List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());

        List<CustConsultGoodsV2DTO> consultGoodsList = csConsultGoodsDao
                .selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(shop, result, skuLst, startDate, endDate,
                        csNickLst, customer);
        List<CustConsultGoodsV2DTO> custConsultGoodsDTOs = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(consultGoodsList)) {
            for (CustConsultGoodsV2DTO consultGoodsDTO : consultGoodsList) {
                consultGoodsDTO.setStartDate(startDate);
                consultGoodsDTO.setEndDate(endDate);
                consultGoodsDTO.setShopId(shop.getShopId());
                custConsultGoodsDTOs.add(consultGoodsDTO);
            }
        }
        if(CollectionUtils.isNotEmpty(csLst)){
            Map<String, String> simpleNameMap = csLst.stream()
                    .collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
            for (CustConsultGoodsV2DTO consult : custConsultGoodsDTOs) {
                consult.setCsSimpleNick(simpleNameMap.get(consult.getCsNick().toLowerCase()));
            }
        }
        custConsultGoodsDTOs.sort(Comparator.comparing(CustConsultGoodsV2DTO::getDate));
        return custConsultGoodsDTOs;
    }

    private <T> List<T> doHandlerVODomainOfSpu(Map<String, List<T>> data, Function<Map.Entry<String, List<T>>,T> function){
        List<T> result = new ArrayList<>();
        for (Map.Entry<String, List<T>> stringListEntry : data.entrySet()) {
            result.add(function.apply(stringListEntry));
        }
        return result;
    }

    private <T> List<T> doHandlerVODomainOfSpuV2(Map<String, List<T>> data, Function<Map.Entry<String, List<T>>,T> function){
        List<T> result = new ArrayList<>();
        for (Map.Entry<String, List<T>> stringListEntry : data.entrySet()) {
            result.add(function.apply(stringListEntry));
        }
        return result;
    }

    /**
     * sku维度的数据替换成spu的数据
     * @param goodsConsultSummaryList
     * @param shopGoodsSkus
     * @return
     */
    private List<GoodsConsultSummaryDTO> doHandlerVODomainOfSpu(List<GoodsConsultSummaryDTO> goodsConsultSummaryList, List<ShopGoodsSku> shopGoodsSkus) {
        List<GoodsConsultSummaryDTO> resultSummaryOfSpu = Lists.newArrayList();
        //wareMap Lst
        Map<Long, List<ShopGoodsSku>> wareMap = shopGoodsSkus.stream().collect(Collectors.groupingBy(ShopGoodsSku::getWareId));
// 将sku维度的数据替换成spu维度
        for (Map.Entry<Long, List<ShopGoodsSku>> longListEntry : wareMap.entrySet()) {
            Long wareID = longListEntry.getKey();
            List<ShopGoodsSku> goodSkus = longListEntry.getValue();
            ShopGoodsSku firstShopGoodSku = goodSkus.get(0);

            List<Long> skuIds = goodSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
            List<GoodsConsultSummaryDTO> needCal = goodsConsultSummaryList.stream().filter(ele -> skuIds.contains(ele.getSkuId())).collect(toList());

            GoodsConsultSummaryDTO goodsConsultSummaryDTO = new GoodsConsultSummaryDTO();
            goodsConsultSummaryDTO.setSkuId(wareID);//编号
            goodsConsultSummaryDTO.setSkuName(firstShopGoodSku.getWareName());//商品名称
            goodsConsultSummaryDTO.setShopId(firstShopGoodSku.getShopId());
            //咨询人数
            int consultNum = needCal.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
            //购买人数
            int buyerNum = needCal.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
            goodsConsultSummaryDTO.setConsultNum(consultNum);//咨询人数
            goodsConsultSummaryDTO.setPurchasesBuyerNum(buyerNum);//购买人数
            goodsConsultSummaryDTO.setPurchasesGoodsNum(needCal.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum());//购买件数
            goodsConsultSummaryDTO.setPurchasesAmount(needCal.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum());//购买金额
            goodsConsultSummaryDTO.setBuyPercent(consultNum > 0 ? (buyerNum / consultNum) * 100.0 : 0.0);//购买占比
            resultSummaryOfSpu.add(goodsConsultSummaryDTO);
        }


        return goodsConsultSummaryList;
    }

    private List<GoodsConsultSummaryV2DTO> doHandlerVODomainOfSpuV2(List<GoodsConsultSummaryV2DTO> goodsConsultSummaryList, List<ShopGoodsSku> shopGoodsSkus) {

        List<GoodsConsultSummaryV2DTO> resultSummaryOfSpu = Lists.newArrayList();
        //wareMap Lst
        Map<Long, List<ShopGoodsSku>> wareMap = shopGoodsSkus.stream().collect(Collectors.groupingBy(ShopGoodsSku::getWareId));
// 将sku维度的数据替换成spu维度
        for (Map.Entry<Long, List<ShopGoodsSku>> longListEntry : wareMap.entrySet()) {
            Long wareID = longListEntry.getKey();
            List<ShopGoodsSku> goodSkus = longListEntry.getValue();
            ShopGoodsSku firstShopGoodSku = goodSkus.get(0);

            List<Long> skuIds = goodSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
            List<GoodsConsultSummaryV2DTO> needCal = goodsConsultSummaryList.stream().filter(ele -> skuIds.contains(ele.getSkuId())).collect(toList());

            GoodsConsultSummaryV2DTO goodsConsultSummaryDTO = new GoodsConsultSummaryV2DTO();
            goodsConsultSummaryDTO.setSkuId(wareID);//编号
            goodsConsultSummaryDTO.setSkuName(firstShopGoodSku.getWareName());//商品名称
            goodsConsultSummaryDTO.setShopId(firstShopGoodSku.getShopId());
            //咨询人数
            int receiveNum = needCal.stream().mapToInt(GoodsConsultSummaryV2DTO::getReceiveNum).sum();
            int enquiryNum = needCal.stream().mapToInt(GoodsConsultSummaryV2DTO::getEnquiryNum).sum();
            //购买人数
            int payNum = needCal.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayNum).sum();
            goodsConsultSummaryDTO.setReceiveNum(receiveNum);//咨询人数
            goodsConsultSummaryDTO.setEnquiryNum(enquiryNum);//咨询人数
            goodsConsultSummaryDTO.setPayNum(payNum);//购买人数
            goodsConsultSummaryDTO.setPayGoodsNum(needCal.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayGoodsNum).sum());//购买件数
            goodsConsultSummaryDTO.setPayAmount(needCal.stream().mapToDouble(GoodsConsultSummaryV2DTO::getPayAmount).sum());//购买金额
            goodsConsultSummaryDTO.setBuyPercent(enquiryNum > 0 ? (payNum / enquiryNum) * 100.0 : 0.0);//购买占比
            resultSummaryOfSpu.add(goodsConsultSummaryDTO);
        }


        return goodsConsultSummaryList;
    }

}
