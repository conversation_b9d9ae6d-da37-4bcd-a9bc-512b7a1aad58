package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.CsLossRecordDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/28 10:10 AM
 * @since 1.0.0
 */
public interface CsLossRecordBusiness {

    List<CsLossRecordDTO> searchByDateShopNicks(
            Set<String> nicks,
            Long shopId,
            Date startDate,
            Date endDate,
            String schemaId,
            Set<Date> filterDates);

}
