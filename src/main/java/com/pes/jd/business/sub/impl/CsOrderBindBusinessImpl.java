package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.CsOrderBindBusiness;
import com.pes.jd.dao.sub.CsOrderBindDao;
import com.pes.jd.model.DTO.CsOrderBindDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/12 5:40 PM
 * @since 1.0.0
 */
@Service
public class CsOrderBindBusinessImpl implements CsOrderBindBusiness {
    @Autowired
    private CsOrderBindDao csOrderBindDao;
    @Override
    public List<Map<String, Object>> selectDayOrder(String shopId, String nick, Date beginDate, Date endDate) {
        return csOrderBindDao.selectDayOrder(shopId, nick, beginDate, endDate);
    }

    @Override
    public List<CsOrderBindDTO> searchShopDate(Long shopId, Set<String> nicks, String schema, Date startDate, Date endDate) {
        return csOrderBindDao.searchShopDate(shopId, nicks, schema, startDate, endDate);
    }
}
