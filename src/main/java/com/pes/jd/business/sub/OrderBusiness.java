package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.OrderDTO;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopBaseDataParam;
import com.pes.jd.model.Query.SortPageQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/19 9:45 PM
 * @since 1.0.0
 */
public interface OrderBusiness {
    List<OrderDTO> selectOrderInfoByOrderIdsAndStatus(
            Set<String> orderIds,
            String status,
            String shopId
    );

    /**
     * 完善订单状态
     *
     * @param orderLst 订单列表
     */
    void orderStateNotPayToCancel(List<OrderDTO> orderLst);

    Map<String, Object> selectOrderDeatilByShopIdAndBuyerNick(ShopBaseDataParam shopBaseDataParam, String buyerNick, Date date, Integer colType, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception;
}
