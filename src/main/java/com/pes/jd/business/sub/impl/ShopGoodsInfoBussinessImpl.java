package com.pes.jd.business.sub.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.ShopGoodsBusiness;
import com.pes.jd.business.sub.ShopGoodsInfoBussiness;
import com.pes.jd.business.sub.ShopGoodsLabelBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.sub.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.ShopCategoryTree;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SkuQuery;
import com.pes.jd.model.Result.ShopGoodsResult;
import com.pes.jd.model.VO.GoodsGroupSkuVO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("Duplicates")
@Service
public class ShopGoodsInfoBussinessImpl implements ShopGoodsInfoBussiness {

	@Autowired
	private ShopGoodsBusiness shopGoodsBusiness;

	@Autowired
	private ShopCategoryDao shopCategoryDao;

	@Autowired
	private ShopGoodSkuDao shopGoodSkuDao;

	@Autowired
	private ShopGoodsSkuLabelDao shopGoodsSkuLabelDao;

	@Autowired
	private ShopGoodsLabelBusiness shopGoodsLabelBusiness;

	@Autowired
    private ShopGoodsSkuAssociativeDao shopGoodsSkuAssociativeDao;

	@Autowired
    private ShopRecommendSkuDao shopRecommendSkuDao;

	@Autowired
    private ShopGoodsDao shopGoodsDao;

	private static final Logger LOGGER = LoggerFactory.getLogger(ShopGoodSkuBusinessImpl.class);



	@Override
	public List<ShopCategoryTree> selectShopCategoryAndGoodsByShopId(ShopCommonParam shopInfo) {

		List<ShopCategoryDTO> shopCategoryLst = shopCategoryDao.selectShopCategoryByShopId(shopInfo);
		List<ShopCategoryTree> parentCategoryLst = new ArrayList<>();
		List<ShopCategoryTree> allCategoryLst = new ArrayList<ShopCategoryTree>();
		ShopCategoryTree treeModel = null;
		for(ShopCategoryDTO item : shopCategoryLst){
			treeModel = new ShopCategoryTree();
			treeModel.setName(item.getName());
			treeModel.setParentId(item.getParentId());
			treeModel.setCategoryId(item.getCategoryId());
			treeModel.setLevel(item.getLevel());
			allCategoryLst.add(treeModel);
			if(item.getParentId().equals(0L)){
				parentCategoryLst.add(treeModel);
			}
		}
		List<ShopCategoryTree> treeList = new ArrayList<ShopCategoryTree>();
		if(parentCategoryLst.size() > 0){
			for(ShopCategoryTree parent : parentCategoryLst){
				treeList.add(treeRoot(allCategoryLst, parent));
			}
		}
		return treeList;
	}


	@Override
	public List<ShopCategoryTree> selectShopCategoryAndGoodsByShopIdV2(ShopCommonParam shopInfo) {
		List<ShopCategoryDTO> shopCategoryLst = shopCategoryDao.selectShopCategoryByShopIdV2(shopInfo);
		List<ShopCategoryTree> parentCategoryLst = new ArrayList<>();
		List<ShopCategoryTree> allCategoryLst = new ArrayList<ShopCategoryTree>();
		ShopCategoryTree treeModel = null;
		for(ShopCategoryDTO item : shopCategoryLst){
			treeModel = new ShopCategoryTree();
			treeModel.setName(item.getName());
			treeModel.setParentId(item.getParentId());
			treeModel.setCategoryId(item.getCategoryId());
			treeModel.setLevel(item.getLevel());
			allCategoryLst.add(treeModel);
			if(item.getParentId().equals(0L)){
				parentCategoryLst.add(treeModel);
			}
		}
		List<ShopCategoryTree> treeList = new ArrayList<ShopCategoryTree>();
		if(parentCategoryLst.size() > 0){
			for(ShopCategoryTree parent : parentCategoryLst){
				treeList.add(treeRoot(allCategoryLst, parent));
			}
		}
		return treeList;
	}

	/**
	 * 通过商铺的ID查询所有子类目包括自己
	 * @param shopInfo
	 * @param categoryId
	 * @return
	 */
	public List<Long> selectShopAllSubCategory(ShopCommonParam shopInfo,Long categoryId) {
		List<ShopCategoryTree> shopCategoryTrees = selectShopCategoryAndGoodsByShopIdV2(shopInfo);
		List<Long> categoryAndAllSubcategoryIds = getCategoryAndAllSubcategoryIds(shopCategoryTrees, categoryId);
		return categoryAndAllSubcategoryIds;
	}

	public List<Long> getCategoryAndAllSubcategoryIds(List<ShopCategoryTree> categoryList, Long targetCategoryId) {
		Map<Long, ShopCategoryTree> categoryMap = buildCategoryMap(categoryList);
		List<Long> categoryIds = new ArrayList<>();

		ShopCategoryTree targetCategory = categoryMap.get(targetCategoryId);
		if (targetCategory != null) {
			collectCategoryAndSubcategoryIds(targetCategory, categoryIds);
		}

		return categoryIds;
	}
	private  Map<Long, ShopCategoryTree> buildCategoryMap(List<ShopCategoryTree> categoryList) {
		Map<Long, ShopCategoryTree> categoryMap = new HashMap<>();
		for (ShopCategoryTree category : categoryList) {
			addCategoryToMap(category, categoryMap);
		}
		return categoryMap;
	}

	private  void addCategoryToMap(ShopCategoryTree category, Map<Long, ShopCategoryTree> categoryMap) {
		categoryMap.put(category.getCategoryId(), category);
		if (category.getChildrens() != null) {
			for (ShopCategoryTree child : category.getChildrens()) {
				addCategoryToMap(child, categoryMap);
			}
		}
	}

	private void collectCategoryAndSubcategoryIds(ShopCategoryTree category, List<Long> categoryIds) {
		categoryIds.add(category.getCategoryId());

		if (category.getChildrens() != null) {
			for (ShopCategoryTree child : category.getChildrens()) {
				collectCategoryAndSubcategoryIds(child, categoryIds);
			}
		}
	}



	@Override
	public List<ShopGoodsDTO> selectShopGoodsByShopIdAndCategoryIdAndNameAndStatus(ShopDTO shop, Long categoryId, String name, Integer status) {
		return shopGoodsBusiness.selectShopGoodsByShopId(shop, categoryId, name, status);
	}

	@Override
	public Map<String, Object> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(ShopCommonParam shop,String categoryId, Long level,String skuName, String status,List<Long> skuIdLst,Integer pageSize,Integer pageNum){
		 Map<String, Object> result=Maps.newHashMap();
		List<Long> threeLevelParentLst = getLeaveCategory(shop, categoryId, level);
		List<ShopGoodsSkuDTO> goodsSkuLst=	shopGoodSkuDao.selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(shop, threeLevelParentLst, skuName, status,skuIdLst, pageSize, pageNum);
		int count=shopGoodSkuDao.selectCountShopGoods(shop, threeLevelParentLst, skuName, status, skuIdLst);
		result.put("goodsSkuLst", goodsSkuLst);
		result.put("count", count);
		return result;
	}

	@Override
	public Map<String, Object> selectShopGoodsSkuIdLstByCategoryIdByStatus(ShopCommonParam shop, String categoryId, String status, Long level) {
		Map<String, Object> result=Maps.newHashMap();
		List<Long> threeLevelParentLst = getLeaveCategoryV2(shop, categoryId, level);
		List<ShopGoodsSkuDTO> goodsSkuIdLst=	shopGoodSkuDao.selectShopGoodsSkuIdLstByCategoryIdByStatus(shop, threeLevelParentLst, status);
		result.put("goodsSkuIdLst", goodsSkuIdLst);
		return result;
	}

	/**
     *
     * @param shop  店铺
     * @param categoryId     类别
     * @param level     类目级别
     * @param skuName   商品名称/商品ID
     * @param status  上架/下架/删除
     * @param excludeSkuIds
     * @param pageSize
     * @param pageNum
     * @param ownSkuId
     * @param propertity
     * @param sortDirection
     * @param associativeStatus
     * @return
     */
	@Override
	public Map<String, Object> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods(
            ShopCommonParam shop, String categoryId, Long level, String skuName, String status,
            List<Long> excludeSkuIds, Integer pageSize, Integer pageNum, Long ownSkuId,
            String propertity, String sortDirection, String associativeStatus){


	    // 查询ownSkuId关联的SkuId
        List<Long> topSku = null;
        if (ownSkuId!=null){
            LinkedMultiValueMap<Long,Long> ownSkuIdAss = new LinkedMultiValueMap<>();
            shopGoodsSkuAssociativeDao
                    .searchLstBySkuIds(Collections.singletonList(ownSkuId), shop.getSchemaId())
                    .forEach((x)->{
                        ownSkuIdAss.add(x.getSku1Id(),x.getSku2Id());
                        ownSkuIdAss.add(x.getSku2Id(),x.getSku1Id());
                    });
            topSku = ownSkuIdAss.get(ownSkuId);
            excludeSkuIds = Collections.singletonList(ownSkuId);
        }


		Map<String, Object> result=Maps.newHashMap();
		List<Long> threeLevelParentLst = getLeaveCategory(shop, categoryId, level);

        IPage<ShopGoodsSkuDTO> goodsSkuLstPage = new Page<>();
        goodsSkuLstPage.setCurrent(pageNum);
        goodsSkuLstPage.setSize(pageSize);
		shopGoodSkuDao
				.selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods
                        (goodsSkuLstPage,shop, threeLevelParentLst, skuName, status,excludeSkuIds,null, topSku,propertity,sortDirection);
        List<ShopGoodsSkuDTO> goodsSkuLst = goodsSkuLstPage.getRecords();
		goodsSkuLst = processSkuLabel(shop, goodsSkuLst);
        goodsSkuLst = processAssociative(shop,goodsSkuLst,associativeStatus);

//		int count=shopGoodSkuDao.selectCountShopGoodsForGoods(shop, threeLevelParentLst, skuName, status,excludeSkuIds,null,topSku);
		result.put("goodsSkuLst", goodsSkuLst);
        result.put("recommendCount",shopRecommendSkuDao.searchShopCount(shop.getShopId(),shop.getSchemaId()));
		result.put("count", goodsSkuLstPage.getTotal());
		return result;
	}

    private List<ShopGoodsSkuDTO> processAssociative(ShopCommonParam shop,
                                                     List<ShopGoodsSkuDTO> goodsSkuLst,
                                                     String associativeStatus) {
        if (CollectionUtils.isEmpty(goodsSkuLst)){
            return goodsSkuLst;
        }
	    Set<Long> skuIds = new HashSet<>();
        // 表明商品的关联关系
        LinkedMultiValueMap<Long,Long> skuSkuAssociativeMap = new LinkedMultiValueMap<>();
        final List<ShopGoodsSkuAssociativeDTO> shopGoodsSkuAssociativeLst =
                shopGoodsSkuAssociativeDao
                    .searchLstBySkuIds(
                        goodsSkuLst
                                .stream()
                                .map(ShopGoodsSkuDTO::getSkuId)
                                .collect(Collectors.toList()), shop.getSchemaId());

        if (CollectionUtils.isEmpty(shopGoodsSkuAssociativeLst)){
            return goodsSkuLst;
        }


        shopGoodsSkuAssociativeLst.forEach((x)->{
            skuIds.add(x.getSku1Id());
            skuIds.add(x.getSku2Id());
            skuSkuAssociativeMap.add(x.getSku1Id(),x.getSku2Id());
            skuSkuAssociativeMap.add(x.getSku2Id(),x.getSku1Id());
        });

        // TODO 是否只查询状态正常的商品

        final Map<Long, ShopGoodsSkuDTO> skuSkuShopMap = shopGoodSkuDao
                .selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods
                        (getAllPage(), shop, null, null, associativeStatus,null,
                                new ArrayList<>(skuIds),  null, null, null)
                .getRecords().stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, v -> v,(x,y)->{
                    LOGGER.error("重复的key \n {} \n {} \n",x,y);
                    return x;
                }));

        // 关联的商品也需要查出知识点
        processSkuLabel(shop,new ArrayList<>(skuSkuShopMap.values()));


        return goodsSkuLst.stream().peek(e->{
            final List<Long> associativeIds = skuSkuAssociativeMap.get(e.getSkuId());
            if (CollectionUtils.isNotEmpty(associativeIds)) {
                e.setAssociative(
                        associativeIds.stream()
                                .map(skuSkuShopMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                );
            }
        }).collect(Collectors.toList());

    }

    public static void main(String[] args) {

	    class A{
	        String name;

            public A(String name) {
                this.name = name;
            }
        }

        ArrayList<A> objects = new ArrayList<>();
	    objects.add(new A("111"));

        ArrayList<A> objects1 = new ArrayList<>(objects);
        A o = objects1.get(0);
        System.out.println(o.name);


    }


    private <T> IPage<T> getAllPage(){
	    IPage<T> page = new Page<>();
	    page.setSize(Long.MAX_VALUE);
	    page.setCurrent(1L);
	    return page;
    }

    /**
	 *  处理  商品知识点 sku维度
	 * @param shop
	 * @param goodsSkuLst
	 * @return
	 */
	@NotNull
	private List<ShopGoodsSkuDTO> processSkuLabel(ShopCommonParam shop, List<ShopGoodsSkuDTO> goodsSkuLst) {
	    if (CollectionUtils.isEmpty(goodsSkuLst)){
	        return goodsSkuLst;
        }
		// 查出商品的知识点
		Map<Long, List<ShopGoodsSkuLabelDTO>> labelGoodsSku = shopGoodsSkuLabelDao
				.searchGoodsSkuBySkuIds(
						shop.getShopId(),
						shop.getSchemaId(),
						goodsSkuLst
								.stream()
								.map(ShopGoodsSkuDTO::getSkuId)
								.collect(Collectors.toList()))
				.stream()
				.collect(
						Collectors.toMap(ShopGoodsSkuLabelDTO::getSkuId, Lists::newArrayList, (old, newV) -> {
							old.addAll(newV);
							return old;
						}, HashMap::new)
				);
		// 组合
		goodsSkuLst = goodsSkuLst.stream()
				.peek(v-> v.setShopGoodsSkuLabelLst(labelGoodsSku.get(v.getSkuId())))
				.collect(Collectors.toList());
		return goodsSkuLst;
	}

	/**
	 *  处理  商品知识点 spu维度
	 * @param shop
	 * @param goodsSkuLst
	 * @return
	 */
	@NotNull
	private List<ShopGoodsSkuDTO> processGoodsLabel(ShopCommonParam shop, List<ShopGoodsSkuDTO> goodsSkuLst) {
	    if (CollectionUtils.isEmpty(goodsSkuLst)){
	        return goodsSkuLst;
        }
		// 查出商品的知识点
		Map<Long, List<ShopGoodsSkuLabelDTO>> labelGoodsSku = shopGoodsLabelBusiness
				.searchGoodsSkuByWareid(
						shop.getShopId(),
						shop.getSchemaId(),
						goodsSkuLst
								.stream()
								.map(ShopGoodsSkuDTO::getSkuId)
								.collect(Collectors.toList()))
				.stream()
				.collect(
						Collectors.toMap(ShopGoodsSkuLabelDTO::getSkuId, Lists::newArrayList, (old, newV) -> {
							old.addAll(newV);
							return old;
						}, HashMap::new)
				);
		// 组合
		goodsSkuLst = goodsSkuLst.stream()
				.peek(v-> v.setShopGoodsSkuLabelLst(labelGoodsSku.get(v.getSkuId())))
				.collect(Collectors.toList());
		return goodsSkuLst;
	}

	@Nullable
	public List<Long> getLeaveCategory(ShopCommonParam shop, String categoryId, Long level) {
		List<Long> threeLevelParentLst=null;
		if (StringUtils.isNotBlank(categoryId) && !level.equals(0L)) {
			if (level.equals(3L)) {
				threeLevelParentLst = Lists.newArrayList();
				threeLevelParentLst.add(Long.valueOf(categoryId));
			} else if (level.equals(1L)) {
				List<Long> parentLst = Lists.newArrayList();
				parentLst.add(Long.valueOf(categoryId));
				List<Long> twoLevelParentLst = getCategoryLst(shop, parentLst);
				threeLevelParentLst = getCategoryLst(shop, twoLevelParentLst);
			} else {
				List<Long> twoLevelParentLst = Lists.newArrayList();
				twoLevelParentLst.add(Long.valueOf(categoryId));
				threeLevelParentLst = getCategoryLst(shop, twoLevelParentLst);
			}
		}
		return threeLevelParentLst;
	}

	private List<Long> getCategoryLst(ShopCommonParam shop,List<Long> parentLst){
		List<ShopCategoryDTO> categorysLst = shopCategoryDao.selectCategoryIdByShopIdByParentId(shop,parentLst);
		List<Long> twoLevelParentLst=Lists.newArrayList();
		for (ShopCategoryDTO cat : categorysLst) {
			twoLevelParentLst.add(cat.getCategoryId());
		}
		return twoLevelParentLst;
	}



	@Nullable
	public List<Long> getLeaveCategoryV2(ShopCommonParam shop, String categoryId, Long level) {
		List<Long> threeLevelParentLst=null;
		if (StringUtils.isNotBlank(categoryId) && !level.equals(0L)) {
			if (level.equals(3L)) {
				threeLevelParentLst = Lists.newArrayList();
				threeLevelParentLst.add(Long.valueOf(categoryId));
			} else if (level.equals(1L)) {
				List<Long> parentLst = Lists.newArrayList();
				parentLst.add(Long.valueOf(categoryId));
				List<Long> twoLevelParentLst = getCategoryLstV2(shop, parentLst);
				threeLevelParentLst = getCategoryLstV2(shop, twoLevelParentLst);
			} else {
				List<Long> twoLevelParentLst = Lists.newArrayList();
				twoLevelParentLst.add(Long.valueOf(categoryId));
				threeLevelParentLst = getCategoryLstV2(shop, twoLevelParentLst);
			}
		}
		return threeLevelParentLst;
	}

	private List<Long> getCategoryLstV2(ShopCommonParam shop,List<Long> parentLst){
		List<ShopCategoryDTO> categorysLst = shopCategoryDao.selectCategoryIdByShopIdByParentIdV2(shop,parentLst);
		List<Long> twoLevelParentLst=Lists.newArrayList();
		for (ShopCategoryDTO cat : categorysLst) {
			twoLevelParentLst.add(cat.getCategoryId());
		}
		return twoLevelParentLst;
	}







	@Override
	public List<ShopGoodsSkuDTO> selectShopGoodsSkuLstBySkuIdLst(ShopCommonParam shop,List<Long> skuIdLst){
		if(CollectionUtils.isEmpty(skuIdLst)){
			return new ArrayList<ShopGoodsSkuDTO>(0);
		}
		List<ShopGoodsSkuDTO> skuLst=shopGoodSkuDao.selectShopGoodsSkuLstBySkuIdLst(shop, skuIdLst);
		if(CollectionUtils.isEmpty(skuLst)){
			return new ArrayList<ShopGoodsSkuDTO>(0);
		}
		return skuLst;
	}


	@Override
	public List<ShopGoodsSkuDTO> selectGoodsSkuLstByShopLstBySkuIdLst(List<ShopCommonParam> shopLst,List<GoodsGroupSkuVO> goodsGroupLst){
		List<ShopGoodsSkuDTO> shopGoodsSkuLst=Lists.newArrayList();
		Map<Long, List<GoodsGroupSkuVO>> goodsGroupMap=goodsGroupLst.stream().collect(Collectors.groupingBy(GoodsGroupSkuVO::getShopId));
		for (ShopCommonParam shop : shopLst) {
			List<GoodsGroupSkuVO> groupSkuLst = goodsGroupMap.get(shop.getShopId());
			if(CollectionUtils.isNotEmpty(groupSkuLst)){
				List<Long> skuIdLst = Lists.newArrayList();
				for (GoodsGroupSkuVO goodsGroupSkuVO : groupSkuLst) {
					skuIdLst.add(goodsGroupSkuVO.getSkuId());
				}
				List<ShopGoodsSkuDTO> goodsLst=selectShopGoodsSkuLstBySkuIdLst(shop, skuIdLst);
				shopGoodsSkuLst.addAll(goodsLst);
			}

		}
		return shopGoodsSkuLst;
	}

	public static List<ShopCategoryTree> treeShopCategoryTreeList(List<ShopCategoryTree> sourceList){
        List<ShopCategoryTree> targetList=new ArrayList<>();
        if (sourceList==null) {
            return null;
        }
        List<ShopCategoryTree> pShopCategoryTrees=new ArrayList<>();
        for (ShopCategoryTree ShopCategoryTree : sourceList) {
            if(ShopCategoryTree.getParentId()==0l || ShopCategoryTree==null){
                pShopCategoryTrees.add(ShopCategoryTree);
            }
        }
        sourceList.removeAll(pShopCategoryTrees);
        for (int i = 0; i < pShopCategoryTrees.size(); i++) {
            ShopCategoryTree pShopCategoryTree=pShopCategoryTrees.get(i);
            List<ShopCategoryTree> cShopCategoryTrees=new ArrayList<>();
            for (ShopCategoryTree ShopCategoryTree : sourceList) {
                if(pShopCategoryTree.getCategoryId().equals(ShopCategoryTree.getParentId())){
                    cShopCategoryTrees.add(ShopCategoryTree);
                }
            }
            pShopCategoryTree.setChildrens(cShopCategoryTrees);
            sourceList.removeAll(cShopCategoryTrees);
        }
        return targetList;
    }

    public static List<ShopCategoryTree> treeRoot(List<ShopCategoryTree> sourceList){
        return sourceList;
    }


    /**
     * 递归获取菜单
     * treeRoot:( ). <br/>
     * <AUTHOR>
     * @param sourceList
     * @param rootShopCategoryTree
     * @return
     */
    public static ShopCategoryTree treeRoot(List<ShopCategoryTree> sourceList,ShopCategoryTree rootShopCategoryTree)
    {
        if (CollectionUtils.isEmpty(sourceList))
        {
            return null;
        }
        List<ShopCategoryTree> childList=new ArrayList<>();
        for (ShopCategoryTree ShopCategoryTree : sourceList) {
            if(rootShopCategoryTree.getCategoryId().equals(ShopCategoryTree.getParentId())){
                ShopCategoryTree ShopCategoryTreeChild = treeRoot(sourceList, ShopCategoryTree);
                ShopCategoryTreeChild.setLevel(ShopCategoryTree.getLevel());
                childList.add(ShopCategoryTreeChild);
            }
        }
        if(childList.size()==0){
            return rootShopCategoryTree;
        }
        rootShopCategoryTree.setChildrens(childList);
        return rootShopCategoryTree;
    }

	@Override
	public List<ShopGoodsSku> selectShopSkuByShopId(ShopCommonParam shop) {

		return shopGoodSkuDao.selectShopSkuByShopId(shop);
	}

    @Override
    public List<Long> selectSkuIdsByWareId(SkuQuery skuQuery, Long wareId) {
        return shopGoodSkuDao.selectSkuIdsByWareId(skuQuery, wareId);
    }

    /**
     *
     * @param shop  店铺
     * @param categoryId     类别
     * @param level     类目级别
     * @param skuName   商品名称/商品ID
     * @param status  上架/下架/删除
     * @param excludeSkuIds
     * @param pageSize
     * @param pageNum
     * @param ownSkuId
     * @param propertity
     * @param sortDirection
     * @param associativeStatus
     * @param addStatus 添加状态 1：已添加， 2：未添加， 为空或不传：全部
     * @param dimension 维度：SKU SPU 大写
     * @return
     */
    @Override
    public Map<String, Object> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(ShopCommonParam shop,
                                                                                                     String categoryId,
                                                                                                     Long level,
                                                                                                     String skuName,
                                                                                                     String status,
                                                                                                     List<Long> excludeSkuIds,
                                                                                                     Integer pageSize,
                                                                                                     Integer pageNum,
                                                                                                     Long ownSkuId,
                                                                                                     String propertity,
                                                                                                     String sortDirection,
                                                                                                     String associativeStatus,
                                                                                                     Integer addStatus,
                                                                                                     Byte dimension) {


        Map<String, Object> result=Maps.newHashMap();
        List<Long> threeLevelParentLst = getLeaveCategory(shop, categoryId, level);

        IPage<ShopGoodsSkuDTO> goodsSkuLstPage = new Page<>();
        goodsSkuLstPage.setCurrent(pageNum);
        goodsSkuLstPage.setSize(pageSize);

        List<ShopGoodsSkuDTO> goodsSkuLst = doHandlerGoodsSkuLst(goodsSkuLstPage, shop, threeLevelParentLst, skuName, status, excludeSkuIds, Lists.newArrayList(), null, propertity, sortDirection, addStatus, dimension, associativeStatus);

        result.put("goodsSkuLst", goodsSkuLst);
        result.put("recommendCount",shopRecommendSkuDao.searchShopCount(shop.getShopId(),shop.getSchemaId()));
        result.put("count", goodsSkuLstPage.getTotal());
        return result;
    }

    private List<ShopGoodsSkuDTO> doHandlerGoodsSkuLst(IPage<ShopGoodsSkuDTO> goodsSkuLstPage, ShopCommonParam shop,
                                                       List<Long> threeLevelParentLst, String skuName, String status, List<Long> excludeSkuIds, List<Long> includeSku,
                                                       List<Long> topSku, String propertity, String sortDirection,
                                                       Integer addStatus, Byte dimension, String associativeStatus) {
        List<ShopGoodsSkuDTO> goodsSkuLst;
        if (CommonConstants.DIMENSION_SKU_OF_BYTE.equals(dimension)) {
            LOGGER.info("aj_sku维度查询知识库");
            shopGoodSkuDao
                    .selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods
                            (goodsSkuLstPage, shop, threeLevelParentLst, skuName, status, excludeSkuIds, includeSku, topSku, propertity, sortDirection, addStatus, dimension);
            goodsSkuLst = goodsSkuLstPage.getRecords();
            goodsSkuLst = processSkuLabel(shop, goodsSkuLst);
        }else {//sku维度
            LOGGER.info("aj_spu维度查询知识库");
            shopGoodSkuDao
                    .selectShopGoodsLstByCategoryIdBySkuNameByStatusByAddStatusForGoods
                            (goodsSkuLstPage, shop, threeLevelParentLst, skuName, status, excludeSkuIds, includeSku, topSku, propertity, sortDirection, addStatus, dimension);
            goodsSkuLst = goodsSkuLstPage.getRecords();
            goodsSkuLst = processGoodsLabel(shop, goodsSkuLst);
        }
        goodsSkuLst = processAssociative(shop, goodsSkuLst, associativeStatus);
        return goodsSkuLst;
    }

//    public static void main(String[] args) {
//        List<ShopCategoryTree> sourceList=new ArrayList<>();
//
//        ShopCategoryTree ShopCategoryTree=new ShopCategoryTree();
//        ShopCategoryTree.setParentId(0l);
//        ShopCategoryTree.setCategoryId(1l);
//        ShopCategoryTree.setName("菜单一级");
//        sourceList.add(ShopCategoryTree);
//
//        ShopCategoryTree ShopCategoryTree2=new ShopCategoryTree();
//        ShopCategoryTree2.setParentId(1l);
//        ShopCategoryTree2.setCategoryId(2l);
//        ShopCategoryTree2.setName("菜单二级1");
//        sourceList.add(ShopCategoryTree2);
//
//        ShopCategoryTree ShopCategoryTree3=new ShopCategoryTree();
//        ShopCategoryTree3.setParentId(2l);
//        ShopCategoryTree3.setCategoryId(3l);
//        ShopCategoryTree3.setName("菜单三级");
//        sourceList.add(ShopCategoryTree3);
//
//        ShopCategoryTree ShopCategoryTree4=new ShopCategoryTree();
//        ShopCategoryTree4.setParentId(3l);
//        ShopCategoryTree4.setCategoryId(4l);
//        ShopCategoryTree4.setName("菜单四级");
//        sourceList.add(ShopCategoryTree4);
//
//        ShopCategoryTree ShopCategoryTree5=new ShopCategoryTree();
//        ShopCategoryTree5.setParentId(1l);
//        ShopCategoryTree5.setCategoryId(6l);
//        ShopCategoryTree5.setName("菜单二级2");
//        sourceList.add(ShopCategoryTree5);
//
//        ShopCategoryTree childrens = treeRoot(sourceList, ShopCategoryTree);
//        System.out.println(JSONObject.toJSON(childrens));
//    }


	@Override
	public List<ShopGoodNameDTO> selectShopGoodsByCategoryIdBySkuNameByStatus(ShopCommonParam shop,String categoryId, Long level,String wareName, String status,List<Long> wareIdLst,Integer pageSize,Integer pageNum){
		List<Long> threeLevelParentLst = getLeaveCategory(shop, categoryId, level);
		return	shopGoodsBusiness.selectShopGoodsByCategoryIdBySkuNameByStatus(shop, threeLevelParentLst, wareName, status,wareIdLst, pageSize, pageNum);
	}

	@Override
	public List<ShopGoodNameDTO> selectShopGoodsByShopIdByWareIds(ShopCommonParam shop, List<Long> wareIds) {
		return shopGoodsBusiness.selectShopGoodsByShopIdByWareIds(shop,wareIds);
	}
	@Override
	public ShopGoodsResult selectShopGoodsName(ShopCommonParam shop,List<Long> wareIds,List<Long> skuIds){
		ShopGoodsResult result=new ShopGoodsResult();

		List<ShopGoodNameDTO> skuLst=shopGoodSkuDao.selectShopGoodsSkuNameBySkuIdLst(shop, skuIds);
		result.setSkuLst(skuLst);
		List<ShopGoodNameDTO> spuLst=shopGoodsBusiness.selectShopGoodsByShopIdByWareIds(shop,wareIds);
		result.setSpuLst(spuLst);
		List<ShopGoodsSku> wareSkuLst=shopGoodSkuDao.selectShopGoodsSkuIdsByWareIdLst(shop,wareIds);
		result.setWareSkuIdLst(wareSkuLst);
		return result;
	}
	@Override
	public List<ShopGoodsSku> selectShopGoodsSkuByShopIdByWareIds(ShopCommonParam shop, List<Long> wareIds) {
		return shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop,wareIds);
	}

    @Override
    public List<ShopGoodsSkuDTO> selectGoodsSkuLstByShopLstBySkuIdLstOfSpu(List<ShopCommonParam> shopLst, List<GoodsGroupSkuVO> goodsGroupSPuLst) {
        List<ShopGoodsSkuDTO> shopGoodsSkuLst=Lists.newArrayList();
        Map<Long, List<GoodsGroupSkuVO>> goodsGroupMap=goodsGroupSPuLst.stream().collect(Collectors.groupingBy(GoodsGroupSkuVO::getShopId));
        for (ShopCommonParam shop : shopLst) {
            List<GoodsGroupSkuVO> groupSkuLst = goodsGroupMap.get(shop.getShopId());
            if(CollectionUtils.isNotEmpty(groupSkuLst)){
                List<Long> skuIdLst = Lists.newArrayList();
                for (GoodsGroupSkuVO goodsGroupSkuVO : groupSkuLst) {
                    skuIdLst.add(goodsGroupSkuVO.getSkuId());
                }
                List<ShopGoodNameDTO> shopGoodNameDTOS = shopGoodsDao.selectShopGoodsByShopIdByWareIds(shop, skuIdLst);
                List<ShopGoodsSkuDTO> goodsLst = spuToSkuVO(shopGoodNameDTOS);
                shopGoodsSkuLst.addAll(goodsLst);
            }

        }
        return shopGoodsSkuLst;
    }

    private List<ShopGoodsSkuDTO> spuToSkuVO(List<ShopGoodNameDTO> shopGoodNameDTOS) {
        if(CollectionUtils.isEmpty(shopGoodNameDTOS)){
            return new ArrayList<>(0);
        }
        List<ShopGoodsSkuDTO> skuLst = new ArrayList<>();
        for (ShopGoodNameDTO shopGoodNameDTO : shopGoodNameDTOS) {
            ShopGoodsSkuDTO shopGoodsSkuDTO = new ShopGoodsSkuDTO();
            shopGoodsSkuDTO.setSkuId(shopGoodNameDTO.getGoodsId());
            shopGoodsSkuDTO.setSkuName(shopGoodNameDTO.getName());
            shopGoodsSkuDTO.setImageUrl(shopGoodNameDTO.getImageUrl());
            skuLst.add(shopGoodsSkuDTO);
        }
        return skuLst;
    }

	@Override
	public List<ShopGoodsSku> selectShopSkuByShopIdAndSkuId(ShopCommonParam shop, List<Long> skuList) {
		return shopGoodSkuDao.selectShopSkuByShopIdAndSkuId(shop,skuList);
	}

}
