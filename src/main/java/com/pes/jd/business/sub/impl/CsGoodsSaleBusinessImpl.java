package com.pes.jd.business.sub.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.annotation.DBLog;
import com.pes.jd.annotation.OrderLog;
import com.pes.jd.business.sub.CsGoodsSaleBusiness;
import com.pes.jd.business.sub.ShopGoodsInfoBussiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.sub.CsGoodsSaleIndexDao;
import com.pes.jd.dao.sub.CsGoodsSaleIndexDetailDao;
import com.pes.jd.dao.sub.OrderDao;
import com.pes.jd.dao.sub.ShopGoodSkuDao;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.CommonLogUploadParam;
import com.pes.jd.model.Param.CsSaleParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.CsGoodsSaleIndexDetailVO;
import com.pes.jd.model.VO.CsGoodsSaleIndexVO;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.office.excel.ExportExcel;
import com.pes.jd.office.excel.ExportExcelBean;
import com.pes.jd.office.param.ExeclColumnParam;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import com.pes.jd.util.SpringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
public class CsGoodsSaleBusinessImpl implements CsGoodsSaleBusiness {
	@Resource
	private CsGoodsSaleIndexDao csGoodsSaleIndexDao;
	@Resource
	private CsGoodsSaleIndexDetailDao csGoodsSaleIndexDetailDao;
    @Resource
    private ShopGoodSkuDao shopGoodSkuDao;
    @Resource
	private OrderDao orderDao;
	@Resource
	private ShopGoodsInfoBussiness shopGoodsInfoBussiness;
	@Override
	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexCountByCsNickBySku(ShopCommonParam shop, Date startDate,
			Date endDate, List<String> csNickList, List<Long> skuLst) {
		if (CollectionUtils.isEmpty(csNickList)) {
			return new ArrayList<>(0);
		}
		List<CsGoodsSaleIndexDTO> csGoodsSaleIndexDTOs = csGoodsSaleIndexDao
				.selectCsGoodsSaleIndexCountByCsNickBySku(shop, startDate, endDate, csNickList, skuLst);
		if (CollectionUtils.isNotEmpty(csGoodsSaleIndexDTOs)) {
			for (CsGoodsSaleIndexDTO sale : csGoodsSaleIndexDTOs) {
				ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(sale.getSkuId());
				shopGoodsDTO.setCategoryId(sale.getCategoryId());
				shopGoodsDTO.setImageUrl(sale.getImageUrl());
				shopGoodsDTO.setSkuName(sale.getSkuName());
				shopGoodsDTO.setShopId(sale.getShopId());
				sale.setGoodsDTO(shopGoodsDTO);
			}
		}
		csGoodsSaleIndexDTOs.remove(Collections.singleton(null));
		return csGoodsSaleIndexDTOs;
	}

	@Override
	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexCountByCsNickBySkuV2(ShopCommonParam shop, Date startDate,
																				Date endDate, List<String> csNickList, List<Long> skuLst, Long categoryId) throws IllegalAccessException {
		if (CollectionUtils.isEmpty(csNickList)) {
			return new ArrayList<>(0);
		}
		List<Long> categoryIds = new ArrayList<>();
		if (categoryId != null) {
			categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
		}
		List<CsGoodsSaleIndexDTO> csGoodsSaleIndexDTOs = csGoodsSaleIndexDao
				.selectCsGoodsSaleIndexCountByCsNickBySkuV2(shop, startDate, endDate, csNickList, skuLst, categoryIds);

		if (CollectionUtils.isNotEmpty(csGoodsSaleIndexDTOs)) {
			for (CsGoodsSaleIndexDTO sale : csGoodsSaleIndexDTOs) {
				ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(sale.getSkuId());
				shopGoodsDTO.setCategoryId(sale.getCategoryId());
				shopGoodsDTO.setImageUrl(sale.getImageUrl());
				shopGoodsDTO.setSkuName(sale.getSkuName());
				shopGoodsDTO.setShopId(sale.getShopId());
				sale.setGoodsDTO(shopGoodsDTO);
			}
		}
		csGoodsSaleIndexDTOs.remove(Collections.singleton(null));
		return csGoodsSaleIndexDTOs;
	}

	@Override
	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexByCsNickByDateBySku(ShopCommonParam shop, Date startDate,
			Date endDate, Long skuId, String csNick) {
		return csGoodsSaleIndexDao.selectCsGoodsSaleIndexByCsNickByDateBySku(shop, startDate, endDate, skuId, csNick);
	}

	@Override
	@OrderLog(resultType = DataAnalysisVO.class,paramType = CsGoodsSaleIndexDetailDTO.class)
	@DBLog(value = "csGoodsSaleIndexDetail")
	public DataAnalysisVO<CsGoodsSaleIndexDetailDTO> selectCsGoodsSaleIndexDetailByCsNickByDateBySku(
			ShopCommonParam shop, Date startDate, Date endDate, List<String> csNickList, List<Long> skuLst,
			String orderId, SortPageQuery sortPageQuery, Integer enquiryValidDurationTime ,int type) {
		DataAnalysisVO<CsGoodsSaleIndexDetailDTO> dataAnalysisVO = new DataAnalysisVO<>();
		if (CollectionUtils.isEmpty(csNickList)) {
			return dataAnalysisVO;
		}
//		fix:645处理父订单拆单问题，屏蔽父订单*/
		List<Long> parentOrderId = null;
		List<OrderDTO> parentOrderLst = orderDao.selectParentOrderForCsSaleAnalysis(shop, new CsSaleParam(startDate,endDate,enquiryValidDurationTime));
		if (CollUtil.isNotEmpty(parentOrderLst)) {
			//fix_bug2525
			parentOrderId = parentOrderLst.stream().map(OrderDTO::getDirectTradeId).distinct().collect(Collectors.toList());
//			parentOrderId = parentOrderLst.stream().filter(ele -> ele.getOrderType() != 1).map(OrderDTO::getDirectTradeId).distinct().collect(Collectors.toList());
			if (CollUtil.isNotEmpty(parentOrderId) && StrUtil.isNotEmpty(orderId) && parentOrderId.contains(Long.valueOf(orderId))) {
				orderId = "-1";//如果查询的是一个父订单，使用一个查不到的数代替（订单编号不可能为负数）
			}
		}
		Integer count = csGoodsSaleIndexDetailDao.selectCsGoodsSaleCount(shop, startDate, endDate, csNickList, skuLst, orderId, parentOrderId);
		//type ==1 從前端查詢
		if (count > CommonConstants.FRONT_END_DIVIDE_COUNT && type ==1) {
			sortPageQuery.setCurrentPage(sortPageQuery.getCurrentPage() * sortPageQuery.getSize());
			sortPageQuery.setSort(true);
			if (!Strings.isNullOrEmpty(sortPageQuery.getPropertity())) {
				sortPageQuery.setField("date".equals(sortPageQuery.getPropertity()) ? sortPageQuery.getPropertity() : "date");
				sortPageQuery.setSortDirection(sortPageQuery.getSortDirection());
			} else {
				sortPageQuery.setField("date");
				sortPageQuery.setSortDirection("asc");
			}
			dataAnalysisVO.setPageFlag(false);
		} else {
			sortPageQuery.setSort(false);
			sortPageQuery.setCurrentPage(0L);
			sortPageQuery.setSize(0L);
			dataAnalysisVO.setPageFlag(true);
		}
		List<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailDTOs = Optional.ofNullable(getCsGoodsSaleIndexDetail(shop, startDate, endDate, csNickList, skuLst, orderId, sortPageQuery, parentOrderId)).orElse(new ArrayList<>(0));
        if(CollUtil.isNotEmpty(csGoodsSaleIndexDetailDTOs)){
            csGoodsSaleIndexDetailDTOs.sort(Comparator.comparing(CsGoodsSaleIndexDetailDTO::getDate));
        }
        dataAnalysisVO.setDataList(csGoodsSaleIndexDetailDTOs);
		dataAnalysisVO.setCount(count);
		return dataAnalysisVO;
	}


	@Override
	@OrderLog(resultType = DataAnalysisVO.class, paramType = CsGoodsSaleIndexDetailDTO.class)
	@DBLog(value = "csGoodsSaleIndexDetail")
	public DataAnalysisVO<CsGoodsSaleIndexDetailDTO> selectCsGoodsSaleIndexDetailByCsNickByDateBySkuV2(
			ShopCommonParam shop, Date startDate, Date endDate, List<String> csNickList, List<Long> skuLst,
			Long categoryId, String orderId, SortPageQuery sortPageQuery, Integer enquiryValidDurationTime, int type) throws IllegalAccessException {
		DataAnalysisVO<CsGoodsSaleIndexDetailDTO> dataAnalysisVO = new DataAnalysisVO<>();
		if (CollectionUtils.isEmpty(csNickList)) {
			return dataAnalysisVO;
		}
		List<Long> categoryIds = new ArrayList<>();
		if (categoryId != null) {
			categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
		}

//		fix:645处理父订单拆单问题，屏蔽父订单*/
		List<Long> parentOrderId = null;
		List<OrderDTO> parentOrderLst = orderDao.selectParentOrderForCsSaleAnalysis(shop, new CsSaleParam(startDate, endDate, enquiryValidDurationTime));
		if (CollUtil.isNotEmpty(parentOrderLst)) {
			//fix_bug2525
			parentOrderId = parentOrderLst.stream().map(OrderDTO::getDirectTradeId).distinct().collect(Collectors.toList());
//			parentOrderId = parentOrderLst.stream().filter(ele -> ele.getOrderType() != 1).map(OrderDTO::getDirectTradeId).distinct().collect(Collectors.toList());
			if (CollUtil.isNotEmpty(parentOrderId) && StrUtil.isNotEmpty(orderId) && parentOrderId.contains(Long.valueOf(orderId))) {
				orderId = "-1";//如果查询的是一个父订单，使用一个查不到的数代替（订单编号不可能为负数）
			}
		}
		Integer count = csGoodsSaleIndexDetailDao.selectCsGoodsSaleCountV2(shop, startDate, endDate, csNickList, skuLst, categoryIds, orderId, parentOrderId);
		//2025.4.10   后面没有写了

		//type ==1 從前端查詢
		if (count > CommonConstants.FRONT_END_DIVIDE_COUNT && type == 1) {
			sortPageQuery.setCurrentPage(sortPageQuery.getCurrentPage() * sortPageQuery.getSize());
			sortPageQuery.setSort(true);
			if (!Strings.isNullOrEmpty(sortPageQuery.getPropertity())) {
				sortPageQuery.setField("date".equals(sortPageQuery.getPropertity()) ? sortPageQuery.getPropertity() : "date");
				sortPageQuery.setSortDirection(sortPageQuery.getSortDirection());
			} else {
				sortPageQuery.setField("date");
				sortPageQuery.setSortDirection("asc");
			}
			dataAnalysisVO.setPageFlag(false);
		} else {
			sortPageQuery.setSort(false);
			sortPageQuery.setCurrentPage(0L);
			sortPageQuery.setSize(0L);
			dataAnalysisVO.setPageFlag(true);
		}
		List<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailDTOs = Optional.ofNullable(getCsGoodsSaleIndexDetail(shop, startDate, endDate, csNickList, skuLst, orderId, sortPageQuery, parentOrderId)).orElse(new ArrayList<>(0));
		if (CollUtil.isNotEmpty(csGoodsSaleIndexDetailDTOs)) {
			csGoodsSaleIndexDetailDTOs.sort(Comparator.comparing(CsGoodsSaleIndexDetailDTO::getDate));
		}
		dataAnalysisVO.setDataList(csGoodsSaleIndexDetailDTOs);
		dataAnalysisVO.setCount(count);
		return dataAnalysisVO;
	}

	/**
	 * 客服商品销售汇总导出
	 *
	 * @throws Exception
	 */
	@Override
	public void exportCsGoodsSaleSummary(OutputStream out, String jsonParam) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(jsonParam);
		// 获取店铺对象，其中有csNickList
		ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
		ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
				shopQuery.getDbName());
		Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
		JSONArray array = jsonObject.getJSONArray("skuLst");
		List<Long> skuLst = array.toJavaList(Long.class);
		Map<String, String> csSimpleNickMap = JacksonUtils.json2map(jsonObject.getString("csSimpleNickMap"),
				String.class);

        /************确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
        /************确定维度 end ***************/

		List<CsGoodsSaleIndexDTO> csGoodsSaleIndexDTOs = csGoodsSaleIndexDao.selectCsGoodsSaleIndexCountByCsNickBySku(
				shopCommonParam, startDate, endDate, shopQuery.getCsNickLst(), newSkuLst);
		ExeclTableParam<CsGoodsSaleIndexVO> tableParam = new ExeclTableParam<>();
		List<ExeclColumnParam> columnParams = Lists.newArrayList();
		List<CsGoodsSaleIndexVO> voLst = Lists.newArrayList();
		for (CsGoodsSaleIndexDTO saleSummary : csGoodsSaleIndexDTOs) {
			CsGoodsSaleIndexVO vo = new CsGoodsSaleIndexVO();
			vo.setCsNick(saleSummary.getCsNick());
			vo.setCsSimpleNick(csSimpleNickMap.get(saleSummary.getCsNick()));
			vo.setSkuId(saleSummary.getSkuId());
			vo.setSkuName(saleSummary.getSkuName());
			vo.setPurchaseBuyerNum(saleSummary.getPurchaseBuyerNum());
			vo.setSaleGoodsNum(saleSummary.getSaleGoodsNum());
			vo.setSaleAmount(saleSummary.getSaleAmount());
			voLst.add(vo);
		}
		tableParam.setData(voLst);
		tableParam.setColumnParams(columnParams);
		columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
		columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
		columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
		columnParams.add(new ExeclColumnParam("购买人数", "purchaseBuyerNum"));
		columnParams.add(new ExeclColumnParam("销售量", "saleGoodsNum"));
		columnParams.add(new ExeclColumnParam("销售额(元)", "saleAmount", "%.2f"));

		String title = "客服商品销售汇总";
		ExportExcel exort = new ExportExcel();
		exort.execlExport(title, tableParam, out);
	}


    /****根据维度查询对应的sku***/
    private List<Long> getSkuIdsByDimension(JSONObject jsonObject, ShopCommonParam shopCommonParam, List<Long> skuLst) {
        Byte dimension = jsonObject.getByte("dimension");
        //获取spu维度下所有的sku
        if (dimension != null && dimension == 2) {//spu维度
            List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shopCommonParam, skuLst);
            return shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        }
        return skuLst;
    }
	/**
	 * 客服商品销售明细导出
	 *
	 *
	 * @throws Exception
	 */
	@Override
	public void exportCsGoodsSaleDetail(OutputStream out, String jsonParam) throws Exception {
		JSONObject jsonObject = JSONObject.parseObject(jsonParam);
		// 获取店铺对象，其中有csNickList
		ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
		ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(),
				shopQuery.getDbName());
		Map<String, String> csSimpleNickMap = JacksonUtils.json2map(jsonObject.getString("csSimpleNickMap"),
				String.class);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
		JSONArray array = jsonObject.getJSONArray("skuLst");
		List<Long> skuLst = array.toJavaList(Long.class);
        String orderId = jsonObject.getString("orderId");
        SortPageQuery sortPageQuery = new SortPageQuery();
		sortPageQuery.setSize(0L);
        /* ***********确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
        /* ***********确定维度 end ***************/
		Integer enquiryValidDurationTime = jsonObject.getInteger("enquiryValidDurationTime");
		DataAnalysisVO<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailDTODataAnalysisVO = selectCsGoodsSaleIndexDetailByCsNickByDateBySku(shopCommonParam, startDate, endDate, shopQuery.getCsNickLst(), newSkuLst, orderId, sortPageQuery, enquiryValidDurationTime,0);
		ExeclTableParam<CsGoodsSaleIndexDetailVO> tableParam = new ExeclTableParam<>();
		List<ExeclColumnParam> columnParams = Lists.newArrayList();
		List<CsGoodsSaleIndexDetailVO> voLst = Lists.newArrayList();
		List<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailDTOs = Optional.ofNullable(csGoodsSaleIndexDetailDTODataAnalysisVO.getDataList()).orElse(new ArrayList<>(0));
		for (CsGoodsSaleIndexDetailDTO saleDetail : csGoodsSaleIndexDetailDTOs) {
			CsGoodsSaleIndexDetailVO vo = new CsGoodsSaleIndexDetailVO();
			vo.setCsNick(saleDetail.getCsNick());
			vo.setCsSimpleNick(csSimpleNickMap.get(saleDetail.getCsNick()));
			vo.setDate(saleDetail.getDate());
			vo.setOrderId(saleDetail.getOrderId());
			vo.setSkuId(saleDetail.getSkuId());
			vo.setSkuName(saleDetail.getSkuName());
			vo.setCustomer(saleDetail.getCustomer());
			vo.setSaleGoodsNum(saleDetail.getSaleGoodsNum());
			vo.setSaleAmount(saleDetail.getSaleAmount());
			vo.setOutStockTime(saleDetail.getOutStockTime());
			voLst.add(vo);
		}
		tableParam.setData(voLst);
		tableParam.setColumnParams(columnParams);
		columnParams.add(new ExeclColumnParam("日期", "date", 1));
		columnParams.add(new ExeclColumnParam("订单编号", "orderId"));
		columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
		columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
		columnParams.add(new ExeclColumnParam("顾客昵称", "customer"));
		columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
		columnParams.add(new ExeclColumnParam("销售量", "saleGoodsNum"));
		columnParams.add(new ExeclColumnParam("销售额(元)", "saleAmount", "%.2f"));
		columnParams.add(new ExeclColumnParam("出库时间", "outStockTime", 2));
		String title = "客服商品销售明细";
		//ExportExcel exort = new ExportExcel();
		//交给spring处理方便aop
		ExportExcelBean exort = SpringUtil.getBean(ExportExcelBean.class);

		//上传参数
		CommonLogUploadParam commonUpParam = JacksonUtils.json2pojo(jsonObject.getString("commonUpParam"), CommonLogUploadParam.class);
		tableParam.setOrderInfoLogUploadParam(commonUpParam);
		exort.execlExport(title, tableParam, out);
	}

    @Override
    public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexCountByCsNickBySkuOfSpu(ShopCommonParam shop, List<CsDTO> csLst, Date startDate, Date endDate, List<String> csNickList, List<Long> wareIds) {
        if (CollectionUtils.isEmpty(csNickList)) {
            return new ArrayList<>(0);
        }
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
        List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        List<CsGoodsSaleIndexDTO> csGoodsSaleIndexDTOs = csGoodsSaleIndexDao
                .selectCsGoodsSaleIndexCountByCsNickBySku(shop, startDate, endDate, csNickList, skuLst);
        if (CollectionUtils.isNotEmpty(csGoodsSaleIndexDTOs)) {
            for (CsGoodsSaleIndexDTO sale : csGoodsSaleIndexDTOs) {
                ShopGoodsSkuDTO shopGoodsDTO = new ShopGoodsSkuDTO(sale.getSkuId());
                shopGoodsDTO.setCategoryId(sale.getCategoryId());
                shopGoodsDTO.setImageUrl(sale.getImageUrl());
                shopGoodsDTO.setSkuName(sale.getSkuName());
                shopGoodsDTO.setShopId(sale.getShopId());
                sale.setGoodsDTO(shopGoodsDTO);
            }
        }
        csGoodsSaleIndexDTOs.remove(Collections.singleton(null));

        if (CollectionUtils.isNotEmpty(csLst)) {
            Map<String, CsDTO> simpleNameMap = csLst.stream()
                    .collect(Collectors.toMap(CsDTO::getNick, cs -> cs));
            for (CsGoodsSaleIndexDTO csGoodsSaleIndexDTO : csGoodsSaleIndexDTOs) {
                CsDTO cs = simpleNameMap.get(csGoodsSaleIndexDTO.getCsNick().toLowerCase());
                if (cs != null) {
                    csGoodsSaleIndexDTO.setCsSimpleNick(cs.getCsSimpleNick());
                    csGoodsSaleIndexDTO.setType(cs.getType());
                }
            }
            csGoodsSaleIndexDTOs.sort(Comparator.comparing(CsGoodsSaleIndexDTO::getType, Comparator.nullsLast(Integer::compareTo)));

        }
        return CollectionUtils.isNotEmpty(csGoodsSaleIndexDTOs)?csGoodsSaleIndexDTOs:new ArrayList<>(0);
    }

    @Override
    public DataAnalysisVO<CsGoodsSaleIndexDetailDTO> selectCsGoodsSaleIndexDetailByCsNickByDateBySkuOfSpu(ShopCommonParam shop,
																										  List<CsDTO> csLst,
																										  Date startDate,
																										  Date endDate,
																										  List<String> csNickList,
																										  List<Long> wareIds,
																										  String orderId,
																										  SortPageQuery sortPageQuery,
																										  Integer enquiryValidDurationTime,
																										  OrderInfoLogUploadParam orderInfoLogUploadParam) {

        DataAnalysisVO<CsGoodsSaleIndexDetailDTO> dataAnalysisVO = new DataAnalysisVO<>();
        if (CollectionUtils.isEmpty(csNickList)) {
            return dataAnalysisVO;
        }
        List<Long> skuLst = null;
        if (CollectionUtils.isNotEmpty(wareIds)) {
            List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
            skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        }
//		fix:645处理父订单拆单问题，屏蔽父订单*/
		List<Long> parentOrderId = null;
		List<OrderDTO> parentOrderLst = orderDao.selectParentOrderForCsSaleAnalysis(shop, new CsSaleParam(startDate,endDate,enquiryValidDurationTime));
		if (CollUtil.isNotEmpty(parentOrderLst)) {
			parentOrderId = parentOrderLst.stream().filter(ele -> ele.getOrderType() != 1).map(OrderDTO::getDirectTradeId).distinct().collect(Collectors.toList());
			if (CollUtil.isNotEmpty(parentOrderId) && StrUtil.isNotEmpty(orderId) && parentOrderId.contains(Long.valueOf(orderId))) {
				orderId = "-1";//如果查询的是一个父订单，使用一个查不到的数代替（订单编号不可能为负数）
			}
		}
		Integer count = csGoodsSaleIndexDetailDao.selectCsGoodsSaleCount(shop, startDate, endDate, csNickList, skuLst, orderId, parentOrderId);
        if (count > CommonConstants.FRONT_END_DIVIDE_COUNT) {
            sortPageQuery.setCurrentPage(sortPageQuery.getCurrentPage() * sortPageQuery.getSize());
            sortPageQuery.setSort(true);
            if (!Strings.isNullOrEmpty(sortPageQuery.getPropertity())) {
                sortPageQuery.setField("date".equals(sortPageQuery.getPropertity()) ? sortPageQuery.getPropertity() : "date");
                sortPageQuery.setSortDirection(sortPageQuery.getSortDirection());
            } else {
                sortPageQuery.setField("date");
                sortPageQuery.setSortDirection("asc");
            }
            dataAnalysisVO.setPageFlag(false);
        } else {
            sortPageQuery.setSort(false);
            sortPageQuery.setCurrentPage(0L);
            sortPageQuery.setSize(0L);
            dataAnalysisVO.setPageFlag(true);
        }
        List<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailDTOs = getCsGoodsSaleIndexDetail(shop, startDate, endDate, csNickList, skuLst, orderId,
                        sortPageQuery, parentOrderId);

        if (Strings.isNullOrEmpty(sortPageQuery.getPropertity())) {
            csGoodsSaleIndexDetailDTOs.sort(Comparator.comparing(CsGoodsSaleIndexDetailDTO::getDate));
        }
        dataAnalysisVO.setDataList(csGoodsSaleIndexDetailDTOs);
        dataAnalysisVO.setCount(count);

        List<CsGoodsSaleIndexDetailDTO> csSaleList = dataAnalysisVO.getDataList();
        if (CollectionUtils.isNotEmpty(csSaleList)) {
            if (CollectionUtils.isNotEmpty(csLst)) {
                Map<String, String> simpleNameMap = csLst.stream()
                        .collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
                for (CsGoodsSaleIndexDetailDTO csGoodsSaleDetail : csSaleList) {
                    csGoodsSaleDetail.setCsSimpleNick(simpleNameMap.get(csGoodsSaleDetail.getCsNick()));
                }
            }
        } else {
            dataAnalysisVO.setCount(0);
            dataAnalysisVO.setPageFlag(true);
            dataAnalysisVO.setDataList(new ArrayList<>(0));
        }
        return dataAnalysisVO;
    }

    private List<CsGoodsSaleIndexDetailDTO>  getCsGoodsSaleIndexDetail(
			ShopCommonParam shop, Date startDate, Date endDate, List<String> csNickList, List<Long> skuLst,
			String orderId, SortPageQuery sortPageQuery, List<Long> parentOrderId){
		List<CsGoodsSaleIndexDetailDTO> csGoodsSaleIndexDetailLst = csGoodsSaleIndexDetailDao
				.selectCsGoodsSaleIndexDetailByShopIdByCsNickByDateBySku(shop,
						startDate,
						endDate,
						csNickList,
						skuLst,
						orderId,
						sortPageQuery,
						parentOrderId);
		//填充出库时间，sku名称
		if(CollectionUtils.isNotEmpty(csGoodsSaleIndexDetailLst)){
			Set<Long>	skuIdSet= csGoodsSaleIndexDetailLst.stream().map(CsGoodsSaleIndexDetailDTO::getSkuId).collect(Collectors.toSet());
			List<ShopGoodsSkuDTO>goodsLst= shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shop,skuIdSet);
			Map<Long,String> skuNameMap= Maps.newHashMap();
			if(CollectionUtils.isNotEmpty(goodsLst)){
				skuNameMap=	goodsLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId,ShopGoodsSkuDTO::getSkuName,(o1,o2)->o2));
			}
			Set<Long>	orderIdSet= csGoodsSaleIndexDetailLst.stream().map(CsGoodsSaleIndexDetailDTO::getOrderId).collect(Collectors.toSet());
			List<OrderDTO> orderLst=	orderDao.selectOrderByShopIdByDateByOrderLst(shop,orderIdSet,startDate,endDate);
			Map<Long,OrderDTO> outDateMap= Maps.newHashMap();
			if(CollectionUtils.isNotEmpty(orderLst)){
				outDateMap=orderLst.stream().collect(Collectors.toMap(OrderDTO::getOrderId, c->c,(o1,o2)->o2));
			}
			for (CsGoodsSaleIndexDetailDTO dto : csGoodsSaleIndexDetailLst) {
				dto.setSkuName(skuNameMap.get(dto.getSkuId()));
				if(outDateMap.get(dto.getOrderId())!=null){
					dto.setOutStockTime(outDateMap.get(dto.getOrderId()).getOutStockTime());
				}
			}
		}
		return csGoodsSaleIndexDetailLst;
	}
}
