package com.pes.jd.business.sub.impl;

import com.google.common.collect.Sets;
import com.pes.jd.business.sub.ShopPerformanceBusiness;
import com.pes.jd.constants.OnlineConstants;
import com.pes.jd.dao.sub.*;
import com.pes.jd.model.BO.ShopPerformanceAvg;
import com.pes.jd.model.BO.ShopPerformanceBO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.CustomTypeEnum;
import com.pes.jd.model.JSON.FilterTimeJSON;
import com.pes.jd.model.Param.PerformanceParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 店铺绩效 - 业务类
 * ClassName:ShopPerformanceBusinessImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月22日 下午1:37:47 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@Service
public class ShopPerformanceBusinessImpl implements ShopPerformanceBusiness {

    @Autowired
    private ShopOvDayDao shopOvDayDao;
    @Autowired
    private ShopPvUvDayDao shopPvUvDayDao;
    @Autowired
    private ShopPerformanceDao shopPerformanceDao;
    @Autowired
    private PesShopDsrDao pesShopDsrDao;
    @Autowired
    private ShopTeamOvDayDao shopTeamOvDayDao;
    @Autowired
    private ReceiveSessionNumHourlyDao receiveSessionNumHourlyDao;
    @Autowired
    private CsOrderEvaluateDao csOrderEvaluateDao;
    @Autowired
    private CsServiceEvaluationDao csServiceEvaluationDao;

    @Override
    public Map<String, Object> selectShopPerformance(ShopQuery shop, Integer dateType, Date startDate, Date endDate, List<FilterTimeJSON> filterTimes, String[] propertys, List<PerformanceParam.Filter> filters, int enquiryValidDurationTime, int outStockValidDurationTime) throws Exception {
        boolean isShopDimension = !dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_DAY.getType()) && !dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_MONTH.getType());
        //初始化店铺绩效基础数据容器
        Map<String, ShopPerformanceBO> shopPerformanceBasic = new LinkedHashMap<>();
        List<String> dates = getSplitDateOrMonth(shop, dateType, startDate, endDate, isShopDimension, filterTimes, shopPerformanceBasic, null);
        //若日期被过滤时间覆盖，返回空对象
        if (CollectionUtils.isEmpty(dates)) {
            Map<String, Object> shopPer = new HashMap<>();
            shopPer.put("count", new HashMap<>());
            shopPer.put("avg", new HashMap<>());
            return shopPer;
        }
        //店铺数据
        List<ShopOvDayDTO> shopOvDayLst = shopOvDayDao.selectByShopIdAndDate(shop, dateType, dates);
        for (ShopOvDayDTO ov : shopOvDayLst) {
            String key = isShopDimension ? String.valueOf(ov.getShopId()) : ov.getDate();
            if (shopPerformanceBasic.containsKey(key)) {//判断是否包含该key的值
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setShopSaleAmount(ov.getSaleAmount());
                shopPerformanceBO.setShopSaleGoodsNum(ov.getSaleGoodsNum());
                shopPerformanceBO.setShopSaleBuyerNum(ov.getSaleBuyerNum());
                shopPerformanceBO.setShopSaleOrderNum(ov.getSaleOrderNum());
                shopPerformanceBO.setShopOutStockOrderNum(ov.getOutStockOrderNum());
                shopPerformanceBO.setShopOutStockGoodsNum(ov.getOutStockGoodsNum());
                shopPerformanceBO.setShopOutStockNum(ov.getOutStockNum());
                shopPerformanceBO.setShopOutStockAmount(ov.getOutStockAmount());
                shopPerformanceBO.setShopPostFee(ov.getOrderPostFee());
            }
        }
        //pvuv
        List<ShopPvUvDayDTO> shopPvUvDayLst = shopPvUvDayDao.selectPvUvNumByShopIdAndDate(shop, dateType, dates);
        for (ShopPvUvDayDTO pvUv : shopPvUvDayLst) {
            String key = isShopDimension ? String.valueOf(pvUv.getShopId()) : pvUv.getDate();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setPvNum(pvUv.getPv());
                shopPerformanceBO.setUvNum(pvUv.getUv());
            }
        }
        //店铺退款数据
        List<ShopRefundDayDTO> shopRefundDayLst = shopPerformanceDao.selectShopRefundByShopIdAndDate(shop, dateType, dates);
        for (ShopRefundDayDTO refund : shopRefundDayLst) {
            String key = isShopDimension ? String.valueOf(refund.getShopId()) : refund.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setShopRefundAmount(refund.getCompletedRefundAmount());
                shopPerformanceBO.setShopRefundBuyerNum(refund.getCompletedRefundBuyerNum());
                shopPerformanceBO.setShopRefundProductNum(refund.getCompletedRefundGoodsNum());
                shopPerformanceBO.setShopRefundNum(refund.getCompletedRefundNum());
            }
        }
        //询单维度 客服团队数据
        List<ShopTeamPerformanceDTO> shopTeamPerLst = shopPerformanceDao.selectShopTeamPerByShopIdAndDate(shop, dateType, dates);
        for (ShopTeamPerformanceDTO team : shopTeamPerLst) {
            String key = isShopDimension ? String.valueOf(team.getShopId()) : team.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setConsultNum(team.getConsultNum());
                shopPerformanceBO.setReceiveNum(team.getReceiveNum());
                shopPerformanceBO.setEnquiryNum(team.getEnquiryNum());
                shopPerformanceBO.setOrderedNumToday(team.getOrderedNumToday());
                shopPerformanceBO.setOrderedAmountToday(team.getPaidAmountToday());
                shopPerformanceBO.setOrderedAmountFinal(team.getPaidAmountFinal());
                shopPerformanceBO.setOrderedNumFinal(team.getOrderedNumFinal());
                shopPerformanceBO.setPaidNumFinal(team.getPaidNumFinal());
                shopPerformanceBO.setPaidNumTodayNext(team.getPaidNumTodayNext());
                shopPerformanceBO.setCsOutStockBuyerNumFinal(team.getOutStockOrderBuyerNumFinal());

            }
        }
        //下单维度 客服团队数据
        List<ShopTeamOrderPerformanceDTO> shopTeamOrderPerLst = shopPerformanceDao.selectShopTeamOrderPerByShopIdAndDate(shop, dateType, dates);
        for (ShopTeamOrderPerformanceDTO shopTeamOrder : shopTeamOrderPerLst) {
            String key = isShopDimension ? String.valueOf(shopTeamOrder.getShopId()) : shopTeamOrder.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setToOrderedNum(shopTeamOrder.getToOrderedNum());
                shopPerformanceBO.setToOrderedGoodsNum(shopTeamOrder.getToOrderedGoodsNum());
                shopPerformanceBO.setToOrderedAmount(shopTeamOrder.getToOrderedAmount());
                shopPerformanceBO.setToOrderedOrderNum(shopTeamOrder.getToOrderedOrderNum());

                shopPerformanceBO.setOrderedPaidNumToday(shopTeamOrder.getToOrderedPaidNumToday());
                shopPerformanceBO.setOrderedPaidGoodsToday(shopTeamOrder.getToOrderedPaidGoodsToday());
                shopPerformanceBO.setOrderedPaidAmountToday(shopTeamOrder.getToOrderedPaidAmountToday());
                shopPerformanceBO.setOrderedPaidOrdersToday(shopTeamOrder.getToOrderedPaidOrderNumToday());

                shopPerformanceBO.setOrderedPaidNumFinal(shopTeamOrder.getToOrderedPaidNumFinal());
                shopPerformanceBO.setOrderedPaidGoodsFinal(shopTeamOrder.getToOrderedPaidGoodsFinal());
                shopPerformanceBO.setOrderedPaidAmountFinal(shopTeamOrder.getToOrderedPaidAmountFinal());
                shopPerformanceBO.setOrderedPaidOrdersFinal(shopTeamOrder.getToOrderedPaidOrderNumFinal());

                shopPerformanceBO.setOrderedOutStockNum(shopTeamOrder.getToOrderedOutStockNum());
                shopPerformanceBO.setOrderedOutStockGoods(shopTeamOrder.getToOrderedOutStockGoodsNum());
                shopPerformanceBO.setOrderedOutStockAmount(shopTeamOrder.getToOrderedOutStockAmount());
                shopPerformanceBO.setOrderedOutStockOrders(shopTeamOrder.getToOrderedOutStockOrderNum());

                shopPerformanceBO.setCsOutStockAmount(shopTeamOrder.getOutStockAmount());
                shopPerformanceBO.setCsOutStockGoodsNum(shopTeamOrder.getOutStockGoodsNum());
                shopPerformanceBO.setCsOutStockNum(shopTeamOrder.getOutStockNum());
                shopPerformanceBO.setCsOutStockOrderNum(shopTeamOrder.getOutStockOrderNum());
                //客服团队销售数据
                shopPerformanceBO.setCsSaleAmount(shopTeamOrder.getSaleAmount());
                shopPerformanceBO.setCsSaleGoodsNum(shopTeamOrder.getSaleGoodsNum());
                shopPerformanceBO.setCsSaleBuyerNum(shopTeamOrder.getSaleBuyerNum());
                shopPerformanceBO.setCsSaleOrderNum(shopTeamOrder.getSaleOrderNum());
                shopPerformanceBO.setCsTransactionsNum(shopTeamOrder.getSaleGoodsNum());

                shopPerformanceBO.setCsPostFee(shopTeamOrder.getPostFee());
            }
        }
        //dsr数据
        List<ShopDsrDTO> shopDsrLst = pesShopDsrDao.selectByShopIdAndDate(shop, dateType, dates);
        boolean isQueryByMonth = dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_MONTH.getType());
        for (ShopDsrDTO shopDsr : shopDsrLst) {
            String key = isShopDimension ? String.valueOf(shopDsr.getShopId()) : shopDsr.getDateStr();
            if (isQueryByMonth) {
                if (shopPerformanceBasic.containsKey(key)) {
                    //获取指定月的指定天数
                    int dateNum = getDateNumByYYYYMMStr(key);
                    ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                    shopPerformanceBO.setProductEvaluationDSR(dateNum > 0 ? shopDsr.getItemScore() / dateNum : 0);
                    shopPerformanceBO.setServiceAttitudeDSR(dateNum > 0 ? shopDsr.getServiceScore() / dateNum : 0);
                    shopPerformanceBO.setLogisticsSpeedDSR(dateNum > 0 ? shopDsr.getDeliveryScore() / dateNum : 0);
                    shopPerformanceBO.setAfterSaleScore(dateNum > 0 ? shopDsr.getAfterSaleScore() / dateNum : 0);
                    shopPerformanceBO.setDisputeScore(dateNum > 0 ? shopDsr.getDisputeScore() / dateNum : 0);
                }
            } else {
                if (shopPerformanceBasic.containsKey(key)) {
                    ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                    shopPerformanceBO.setProductEvaluationDSR(shopDsr.getItemScore());
                    shopPerformanceBO.setServiceAttitudeDSR(shopDsr.getServiceScore());
                    shopPerformanceBO.setLogisticsSpeedDSR(shopDsr.getDeliveryScore());
                    shopPerformanceBO.setAfterSaleScore(shopDsr.getAfterSaleScore());
                    shopPerformanceBO.setDisputeScore(shopDsr.getDisputeScore());
                }
            }
        }
        //评论数据
        List<ShopOrderEvaluateDTO> shopOrderEvaluateLst = shopPerformanceDao.selectShopOrderEvaluateByShopIdAndDate(shop, dateType, dates);
        for (ShopOrderEvaluateDTO evaluate : shopOrderEvaluateLst) {
            String key = isShopDimension ? String.valueOf(evaluate.getShopId()) : evaluate.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setShopNeutralEvaluateNum(evaluate.getNeutralEvaluateNum());
                shopPerformanceBO.setShopBadEvaluateNum(evaluate.getBadEvaluateNum());
                shopPerformanceBO.setShopNeutralBadEvaluateNumTotal(evaluate.getNeutralEvaluateNum() + evaluate.getBadEvaluateNum());
            }
        }
        List<CsOrderEvaluateDTO> csOrderEvaluateLst = csOrderEvaluateDao.selectByShoIdAndDateForShopPerformance(shop, dateType, dates, startDate, endDate);
        if (isShopDimension && csOrderEvaluateLst.size() > 1) {
            //根据shopId分组 聚合
            csOrderEvaluateLst = CommonUtils.polymerize(csOrderEvaluateLst,
                    CsOrderEvaluateDTO::getInitObject, CsOrderEvaluateDTO::getShopId, (x, y) -> x.setShopId(y.getShopId()), x -> x.setShopId(shop.getShopId()), null, "shopId");
        }
        for (CsOrderEvaluateDTO evaluate : csOrderEvaluateLst) {
            String key = isShopDimension ? String.valueOf(evaluate.getShopId()) : evaluate.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setCsNeutralEvaluateNum(evaluate.getNeutralEvaluateNumTotal());
                shopPerformanceBO.setCsBadEvaluateNum(evaluate.getBadEvaluateNumTotal());
            }
        }
        //工作量
        List<ShopTeamSessionServiceIndexDTO> shopTeamSessionServiceIndexLst = shopPerformanceDao.selectShopTeamSessionServiceIndexByShopIdAndDate(shop, dateType, dates);
        for (ShopTeamSessionServiceIndexDTO session : shopTeamSessionServiceIndexLst) {
            String key = isShopDimension ? String.valueOf(session.getShopId()) : session.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setConsultSessionNum(session.getConsultSessionNum());
                shopPerformanceBO.setReceiveSessionNum(session.getReceiveSessionNum());
                shopPerformanceBO.setChatNum(session.getChatNum());
                shopPerformanceBO.setCustChatNum(session.getCustChatNum());
                shopPerformanceBO.setCsChatNum(session.getCsChatNum());
                shopPerformanceBO.setCsWordNum(session.getCsWordNum());
                shopPerformanceBO.setMaxReceiveSessionNum(session.getMaxReceiveSessionNum());
                shopPerformanceBO.setNonReplySessionNum(session.getNonReplySessionNum());
                shopPerformanceBO.setSlowRespSessionNum(session.getSlowRespSessionNum());
                shopPerformanceBO.setLongRespSessionNum(session.getLongRespSessionNum());

                shopPerformanceBO.setChatRoundNum(session.getChatRoundNum());
                shopPerformanceBO.setChatRoundNumNoLeave(session.getChatRoundNumNoLeave());
                shopPerformanceBO.setRespTimeFirstCount(session.getRespTimeFirstCount());
                shopPerformanceBO.setRespTimeCount(session.getRespTimeCount());
                shopPerformanceBO.setSessionDurationTimeCount(session.getSessionDurationTimeCount());
                shopPerformanceBO.setAvgRespTimeFirst(session.getAvgRespTimeFirst());
                shopPerformanceBO.setAvgRespTime(session.getAvgRespTime());
                shopPerformanceBO.setAvgSessionDurationTime(session.getAvgSessionDurationTime());

                shopPerformanceBO.setLeaveMsgSessionNum(session.getLeaveMsgSessionNum());
                shopPerformanceBO.setLeaveMsgReceiveSessionNum(session.getLeaveMsgReceiveSessionNum());
                shopPerformanceBO.setAdvisoryMessageNum(session.getLeaveMsgAdvisorySessionNum());
                shopPerformanceBO.setAvgRespInQuickTime(session.getAvgRespInQuickTime());
                shopPerformanceBO.setSessionNum(session.getSessionNum());

                shopPerformanceBO.setForwardInSessionNum(session.getForwardInSessionNum());
                shopPerformanceBO.setForwardOutSessionNum(session.getForwardOutSessionNum());
                shopPerformanceBO.setEmptyChatNum(session.getEmptyChatNum());

                shopPerformanceBO.setDdConsultSessionNum(session.getDdConsultSessionNum());
            }
        }
        //协助服务
        List<ShopTeamAssitIndexDTO> shopTeamAssitIndexLst = shopPerformanceDao.selectShopTeamAssitIndexByShopIdAndDate(shop, dateType, dates);
        for (ShopTeamAssitIndexDTO assit : shopTeamAssitIndexLst) {
            String key = isShopDimension ? String.valueOf(assit.getShopId()) : assit.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setAidOrderNum(assit.getAssitOrderCreateNum());
                shopPerformanceBO.setAidOrderAmount(assit.getAssitOrderCreateAmount());
                shopPerformanceBO.setAidFollowNum(assit.getAssitOrderFollowupNum());
                shopPerformanceBO.setAidFollowAmount(assit.getAssitOrderFollowupAmount());
                shopPerformanceBO.setAidPayNum(assit.getAssitOrderPayNum());
                shopPerformanceBO.setAidPayAmount(assit.getAssitOrderPayAmount());
            }
        }
        //客服团队退款
        List<CsRefundDayDTO> csRefundDayLst = shopPerformanceDao.selectCsRefundDayLstByShopIdAndDate(shop, dateType, dates, startDate, endDate);
        if (isShopDimension && csRefundDayLst.size() > 1) {
            //根据shopId分组 聚合
            csRefundDayLst = CommonUtils.polymerize(csRefundDayLst,
                    CsRefundDayDTO::getInitObject, CsRefundDayDTO::getShopId, (x, y) -> x.setShopId(y.getShopId()), x -> x.setShopId(shop.getShopId()), null, "shopId");
        }
        for (CsRefundDayDTO refund : csRefundDayLst) {
            String key = isShopDimension ? String.valueOf(refund.getShopId()) : refund.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setCsRefundNum(refund.getCompletedRefundNum());
                shopPerformanceBO.setCsRefundBuyerNum(refund.getCompletedRefundBuyerNum());
                shopPerformanceBO.setCsRefundProductNum(refund.getCompletedRefundProductNum());
                shopPerformanceBO.setCsRefundAmount(refund.getCompletedRefundAmount());
            }
        }
        //满意率
        List<CsServiceEvaluationDTO> csServiceEvaluationLst = csServiceEvaluationDao.selectByShopIdAndDateForShopPerformance(shop, dateType, dates, startDate, endDate);
        if (isShopDimension && csServiceEvaluationLst.size() > 1) {
            //根据shopId分组 聚合
            csServiceEvaluationLst = CommonUtils.polymerize(csServiceEvaluationLst,
                    CsServiceEvaluationDTO::getInitObject, CsServiceEvaluationDTO::getShopId, (x, y) -> x.setShopId(y.getShopId()), x -> x.setShopId(shop.getShopId()), null, "shopId");
        }
        for (CsServiceEvaluationDTO evaluation : csServiceEvaluationLst) {
            String key = isShopDimension ? String.valueOf(evaluation.getShopId()) : evaluation.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setInviteEvaluateNum(evaluation.getEvalSendNum());
                shopPerformanceBO.setEvaluateNum(evaluation.getEvalReplyNum());
                shopPerformanceBO.setVerySatisfiedNum(evaluation.getVerySatisfiedNum());
                shopPerformanceBO.setSatisfiedNum(evaluation.getSatisfiedNum());
                shopPerformanceBO.setGeneralNum(evaluation.getGeneralNum());
                shopPerformanceBO.setDissatisfiedNum(evaluation.getDissatisfiedNum());
                shopPerformanceBO.setVeryDissatisfiedNum(evaluation.getVeryDissatisfiedNum());
            }
        }
        //流失数据
        List<ShopTeamLossRecordDTO> shopTeamLossRecordDaoLst = shopPerformanceDao.selectTeamLossRecordByShopIdAndDate(shop, dateType, dates);
        for (ShopTeamLossRecordDTO dto : shopTeamLossRecordDaoLst) {
            String key = isShopDimension ? String.valueOf(dto.getShopId()) : dto.getDateStr();
            if (shopPerformanceBasic.containsKey(key)) {
                ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
                shopPerformanceBO.setEnquiryLossNum(dto.getEnquiryLossNum());
                shopPerformanceBO.setCsPracticalOrderedUnpaidPeople(dto.getCsPracticalOrderedUnpaidPeople());
                shopPerformanceBO.setCsPracticalOrderedUnpaidItemNum(dto.getCsPracticalOrderedUnpaidItemNum());
                shopPerformanceBO.setCsPracticalOrderedUnpaidAmount(dto.getCsPracticalOrderedUnpaidAmount());
                shopPerformanceBO.setCsPracticalOrderedUnpaidOrderedNum(dto.getCsPracticalOrderedUnpaidOrderedNum());
                shopPerformanceBO.setCsPracticalOrderedUnoutStockPeople(dto.getCsPracticalOrderedUnoutStockPeople());
                shopPerformanceBO.setCsPracticalOrderedUnoutStockItemNum(dto.getCsPracticalOrderedUnoutStockItemNum());
                shopPerformanceBO.setCsPracticalOrderedUnoutStockAmount(dto.getCsPracticalOrderedUnoutStockAmount());
                shopPerformanceBO.setCsPracticalOrderedUnoutStockOrderedNum(dto.getCsPracticalOrderedUnoutStockOrderedNum());
            }
        }
        //值班客服数&客服登录总时长
        List<ShopTeamOvDayDTO> shopTeamOvDayLst = shopTeamOvDayDao.selectByShopIdAndDateForShopPerformance(shop, dateType, dates);
        for (ShopTeamOvDayDTO dto : shopTeamOvDayLst) {
            String key = isShopDimension ? String.valueOf(dto.getShopId()) : dto.getDateStr();
            ShopPerformanceBO shopPerformanceBO = shopPerformanceBasic.get(key);
            shopPerformanceBO.setDutyCsNum(dto.getDutyCsNum());
            shopPerformanceBO.setLoginDurationTime(dto.getLoginDurationTime());
            shopPerformanceBO.setRceiveDurationTime(dto.getRceiveDurationTime());
        }
        return getShopPerformanceByProperties(shopPerformanceBasic, propertys, filters, isShopDimension, dateType, dates, startDate, enquiryValidDurationTime, outStockValidDurationTime, shop);
    }

    private int getDateNumByYYYYMMStr(String key) {
        String[] dateArray = key.split("-");
        String yearLStr = dateArray[0];
        String monthStr = dateArray[1];
        int currentMonth = LocalDateTime.now().getMonthValue();
        int currentYear = LocalDateTime.now().getYear();
        if (currentMonth == Integer.parseInt(monthStr) && currentYear == Integer.parseInt(yearLStr)) {
            return LocalDateTime.now().getDayOfMonth() - 1;
        }
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, Integer.parseInt(yearLStr));
        cal.set(Calendar.MONTH, Integer.parseInt(monthStr) - 1);//Java月份才0开始算
        return cal.getActualMaximum(Calendar.DATE);
    }

    /**
     * 分时接待量
     */
    @Override
    public Map<String, Object> selectShopReceiveSessionNumHourly(ShopQuery shopQuery, Integer dateType, Date startDate, Date endDate, List<FilterTimeJSON> filterTime) throws Exception {
        boolean isShopDimension = !dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_DAY.getType()) && !dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_MONTH.getType());
        //初始化店铺绩效基础数据容器
        Map<String, Object> receiveSessionNumHourlyMap = new LinkedHashMap<>();

        List<String> dates = getSplitDateOrMonth(shopQuery, dateType, startDate, endDate, isShopDimension, filterTime, null, receiveSessionNumHourlyMap);
        //若日期被过滤时间覆盖，返回空对象
        if (CollectionUtils.isEmpty(dates)) {
            receiveSessionNumHourlyMap.put("count", new HashMap<>());
            receiveSessionNumHourlyMap.put("avg", new HashMap<>());
            return receiveSessionNumHourlyMap;
        }
        List<ReceiveSessionNumHourlyDTO> receiveSessionNumHourlyLst = receiveSessionNumHourlyDao.selectShopReceiveSessionNumHourly(shopQuery, dateType, startDate, endDate, dates);

        if (isShopDimension && receiveSessionNumHourlyLst.size() > 1) {
            //根据shopId分组 聚合
            receiveSessionNumHourlyLst = CommonUtils.polymerize(receiveSessionNumHourlyLst,
                    ReceiveSessionNumHourlyDTO::getInitObject, ReceiveSessionNumHourlyDTO::getShopId, (x, y) -> x.setShopId(y.getShopId()), x -> x.setShopId(shopQuery.getShopId()), null, "shopId");
        }
        ReceiveSessionNumHourlyDTO count = isShopDimension ? null : ReceiveSessionNumHourlyDTO.getInitObject();
        ReceiveSessionNumHourlyAvgDTO avg = isShopDimension ? null : new ReceiveSessionNumHourlyAvgDTO();

        for (ReceiveSessionNumHourlyDTO dto : receiveSessionNumHourlyLst) {
            String key = isShopDimension ? String.valueOf(dto.getShopId()) : dto.getDateStr();
            if (receiveSessionNumHourlyMap.containsKey(key)) {
                ReceiveSessionNumHourlyDTO receiveSessionNumHourlyDTO = (ReceiveSessionNumHourlyDTO) receiveSessionNumHourlyMap.get(key);
                BeanUtils.copyProperties(dto, receiveSessionNumHourlyDTO);
                if (count != null) {
                    BeanUtils.countPropertyVal(dto, count, "id", "shopId");
                }
            }
        }

        List<ShopTeamSessionServiceIndexDTO> shopTeamSessionServiceIndexLst = shopPerformanceDao.selectShopTeamSessionServiceIndexByShopIdAndDate(shopQuery, dateType, dates);
        for (ShopTeamSessionServiceIndexDTO dto : shopTeamSessionServiceIndexLst) {
            String key = isShopDimension ? String.valueOf(dto.getShopId()) : dto.getDateStr();
            if (receiveSessionNumHourlyMap.containsKey(key)) {
                ReceiveSessionNumHourlyDTO receiveSessionNumHourlyDTO = (ReceiveSessionNumHourlyDTO) receiveSessionNumHourlyMap.get(key);
                receiveSessionNumHourlyDTO.setAllDay(dto.getReceiveSessionNum() == null ? 0 : dto.getReceiveSessionNum());
                if (count != null) {
                    count.setAllDay(count.getAllDay() + receiveSessionNumHourlyDTO.getAllDay());
                }
            }
        }
        if (count != null) {
            BeanUtils.avgPropertyVal(count, avg, receiveSessionNumHourlyMap.size(), null, null, "id", "shopId");
            receiveSessionNumHourlyMap.put("count", count);
            receiveSessionNumHourlyMap.put("avg", avg);
        }
        return receiveSessionNumHourlyMap;
    }

    @Override
    public ShopTeamOrderPerformanceConciseDTO selectShopSaleAmount(ShopCommonParam shopCommonParam, Date toDay) {
        ShopTeamOrderPerformanceConciseDTO shopTeamOrderPerformanceConcise = new ShopTeamOrderPerformanceConciseDTO();
        ShopTeamOrderPerformanceDTO shopTeamOrderPerformance = shopPerformanceDao.selectShopSaleAmount(shopCommonParam, toDay);
        if(shopTeamOrderPerformance == null){
            Date yesterday = DateUtils.getDateByPeriod(toDay, -1);
            ShopTeamOrderPerformanceDTO shopTeamOrderPerformanceConciseYesterday = shopPerformanceDao.selectShopSaleAmount(shopCommonParam, yesterday);
            if(shopTeamOrderPerformanceConciseYesterday == null) return null;
            shopTeamOrderPerformanceConcise = newShopTeamOrderPerformanceConcise(shopTeamOrderPerformance, shopCommonParam, yesterday);
        }else{
            shopTeamOrderPerformanceConcise = newShopTeamOrderPerformanceConcise(shopTeamOrderPerformance, shopCommonParam, toDay);
        }
        return shopTeamOrderPerformanceConcise;
    }

    private ShopTeamOrderPerformanceConciseDTO newShopTeamOrderPerformanceConcise(ShopTeamOrderPerformanceDTO shopTeamOrderPerformance, ShopCommonParam shopCommonParam, Date date) {
        ShopTeamOrderPerformanceConciseDTO shopTeamOrderPerformanceConcise = new ShopTeamOrderPerformanceConciseDTO();
        Integer toOrderedNum = shopTeamOrderPerformance.getToOrderedNum();
        Integer toOrderedOrderNum = shopTeamOrderPerformance.getToOrderedOrderNum();
        Integer toOrderedPaidNumToday = shopTeamOrderPerformance.getToOrderedPaidNumToday();
        Integer toOrderedPaidOrderNumToday = shopTeamOrderPerformance.getToOrderedPaidOrderNumToday();
        ShopOvDayDTO shopOvDay = shopOvDayDao.selectSaleAmountAndBuyerNumByShopId(shopCommonParam, date);
        shopTeamOrderPerformanceConcise.setNumberOfLostOrders(new Double(Math.ceil((toOrderedOrderNum - toOrderedPaidOrderNumToday) * 0.03)).intValue());
        if(shopOvDay != null){
            Double saleAmount = shopOvDay.getSaleAmount();
            Integer saleBuyerNum = shopOvDay.getSaleBuyerNum();
            shopTeamOrderPerformanceConcise.setLostSales(Math.round((toOrderedNum - toOrderedPaidNumToday) * (saleBuyerNum != 0 ? saleAmount/saleBuyerNum : 0) * 0.03));
        }else{
            shopTeamOrderPerformanceConcise.setNumberOfLostOrders(0);
        }
        return shopTeamOrderPerformanceConcise;
    }

    /**
     * 根据开始时间 和 结束时间 以日或月为单位切割
     */
    private List<String> getSplitDateOrMonth(ShopQuery shopQuery, Integer dateType, Date startDate, Date endDate, boolean isShopDimension, List<FilterTimeJSON> filterTimes, Map<String, ShopPerformanceBO> shopPerformanceBasic, Map<String, Object> receiveSessionNumHourlyMap) {
        ArrayList<String> dates = new ArrayList<>();
        if (dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_MONTH.getType())) {
            //获取月份集合
            int months = (endDate.getYear() - startDate.getYear()) * 12
                    + (endDate.getMonth() - startDate.getMonth());
            for (int i = 0; i <= months; i++) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(startDate);
                calendar.add(Calendar.MONTH, i);
                String format = new SimpleDateFormat("yyyy-MM").format(calendar.getTime());
                dates.add(format);
                //添加月份作为key
                if (shopPerformanceBasic != null)
                    shopPerformanceBasic.put(format, new ShopPerformanceBO());
                if (receiveSessionNumHourlyMap != null)
                    receiveSessionNumHourlyMap.put(format, ReceiveSessionNumHourlyDTO.getInitObject());
            }
        } else {
            if (isShopDimension) {
                //添加shopId作为key
                if (shopPerformanceBasic != null)
                    shopPerformanceBasic.put(String.valueOf(shopQuery.getShopId()), new ShopPerformanceBO());
                if (receiveSessionNumHourlyMap != null)
                    receiveSessionNumHourlyMap.put(String.valueOf(shopQuery.getShopId()), ReceiveSessionNumHourlyDTO.getInitObject());
            }

            //获取日期集合
            ArrayList<Date> dateLst = DateUtil.splitDate(startDate, endDate);
            //过滤时间
            Iterator<Date> iterator = dateLst.iterator();
            while (iterator.hasNext()) {
                Date next = iterator.next();
                boolean isFilter = false;
                for (FilterTimeJSON period : filterTimes) {
                    Date startTime = DateUtil.getStartTimeOfDate(period.getStartDate());
                    Date endTime = DateUtil.getEndTimeOfDate(period.getEndDate());
                    if (next.getTime() >= startTime.getTime() && next.getTime() <= endTime.getTime()) {
                        iterator.remove();
                        isFilter = true;
                        break;
                    }
                }
                if (!isFilter) {
                    String format = DateUtils.formatYMd(next);
                    dates.add(format);
                    if (dateType.equals(CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_DAY.getType())) {
                        //添加日期作为key
                        if (shopPerformanceBasic != null)
                            shopPerformanceBasic.put(format, new ShopPerformanceBO());
                        if (receiveSessionNumHourlyMap != null)
                            receiveSessionNumHourlyMap.put(format, ReceiveSessionNumHourlyDTO.getInitObject());
                    }

                }
            }
        }
        return dates;
    }

    private Set<Date> getFilterTimesSet(List<FilterTimeJSON> filterTimes) {
        Set<Date> filters = new HashSet<>();
        for (FilterTimeJSON period : filterTimes) {
            Date startTime = DateUtil.getStartTimeOfDate(period.getStartDate());
            Date endTime = DateUtil.getEndTimeOfDate(period.getEndDate());
            ArrayList<Date> dates = DateUtil.splitPeriod2List(startTime, endTime);
            filters.addAll(dates);
        }
        return filters;
    }

    /**
     * 获取最终result集合
     */
    private Map<String, Object> getShopPerformanceByProperties(Map<String, ShopPerformanceBO> shopPerformanceBasic, String[] propertys, List<PerformanceParam.Filter> filters, boolean isShopDimension, Integer dateType, List<String> dates, Date startDate, Integer enquiryValidDurationTime, Integer outStockValidDurationTime, ShopQuery shop) throws ParseException {
        //需要计算小数型数据
        String[] percents = {"csSaleAmountPercent", "silenceSaleAmountPercent", "consultPercent", "shopSaleGuestAvgAmount", "shopSaleGuestAvgGoods", "shopSaleGoodsAvgAmount", "shopOutStockGuestAvgAmount", "shopOutStockGuestItemNum", "shopOutStockItemAvgAmount", "csSaleGuestAvgAmount", "csSaleGuestAvgGoods", "csSaleGoodsAvgAmount"
                , "csOutStockGuestAvgAmount", "csOutStockGuestItemNum", "csOutStockItemAvgAmount", "silenceSaleGuestAvgAmount", "silenceSaleGuestAvgGoods", "silenceSaleGoodsAvgAmount", "silenceOutStockGuestAvgAmount", "silenceOutStockGuestItemNum", "silenceOutStockItemAvgAmount", "shopDealPercent", "shopOutStockPercent", "silenceDealPercent"
                , "queryToTomorrow", "queryToFinalPaid", "queryToOrderedToday", "queryToFinalOrdered", "orderedToPaid", "orderedToPaidFinal", "queryToOutStock", "orderItemAvgAmount", "orderedGuestAvgPrice", "orderedGuestAvgAmount", "avgReplyMsg", "answerRatio", "responseRate", "quickResponseRate", "leaveMsgReplyRate", "leaveMsgResponseRate"
                , "csOutStockAmountPercent", "silenceOutStockAmountPercent", "inviteEvaluateRate", "evaluateRate", "satisfactionRate", "csRefundPercent", "avgRespTimeFirst", "avgRespTime", "avgSessionDurationTime", "chatRoundNum", "afterSaleScore", "disputeScore"};


        Iterator<Map.Entry<String, ShopPerformanceBO>> iterator = shopPerformanceBasic.entrySet().iterator();
        Map<String, Object> shopPerformance = new LinkedHashMap<>();
        //店铺绩效汇总对象
        ShopPerformanceBO countBo = isShopDimension ? null : new ShopPerformanceBO();
        ShopPerformanceAvg avgBo = isShopDimension ? null : new ShopPerformanceAvg();
        //要显示非最终数据的字段
        List<String> notFinalDataField = null;
        while (iterator.hasNext()) {
            Map.Entry<String, ShopPerformanceBO> next = iterator.next();
            ShopPerformanceBO shopBoValue = next.getValue();
            ShopPerformanceBO shopBo = new ShopPerformanceBO();

            //要显示非最终数据的字段
            notFinalDataField = new ArrayList<>();
            shopBo.setNotFinalData(notFinalDataField);

            for (String property : propertys) {
                switchProperty(property, shopBo, next.getKey(), shopBoValue, filters, dateType, dates, enquiryValidDurationTime, outStockValidDurationTime, shop);
            }
            //计算汇总
            if (countBo != null) {
                BeanUtils.countPropertyVal(shopBo, countBo, BeanUtils.COUNT_PROCESS, null, null);
            }
            shopPerformance.put(next.getKey(), shopBo);
        }
        if (!isShopDimension) {
            countBo.setNotFinalData(notFinalDataField);
            shopPerformance.put("count", countBo);
            //计算平均值
            BeanUtils.avgPropertyVal(countBo, avgBo, shopPerformanceBasic.size(), null, null);
            List<String> percentList = Arrays.asList(percents);
            List<String> propertyList = new ArrayList<>(Arrays.asList(propertys));
            propertyList.retainAll(percentList);

            for (String property : propertyList) {
                if (StringUtils.equalsAny(property, "avgRespTimeFirst", "avgRespTime", "avgSessionDurationTime") && OnlineConstants.UPDATE_AVG.getTime() >= startDate.getTime())
                    continue;
                switchPropertyForAVG(property, avgBo, countBo);
            }
            avgBo.setNotFinalData(notFinalDataField);
            shopPerformance.put("avg", avgBo);
        }
        return shopPerformance;
    }

    private void switchProperty(String property, ShopPerformanceBO shopBo, String key, ShopPerformanceBO shopBoValue, List<PerformanceParam.Filter> filters, Integer dateType, List<String> dates, Integer enquiryValidDurationTime, Integer outStockValidDurationTime, ShopQuery shopQuery) throws ParseException {
        //静默销售数据计算
        double silenceSaleAmount1 = BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()) - BaseUtils.getNonNull(shopBoValue.getCsSaleAmount());
        shopBoValue.setSilenceSaleAmount(silenceSaleAmount1 > 0 ? silenceSaleAmount1 : 0.0);
        int silenceSaleGoodsNum1 = BaseUtils.getNonNull(shopBoValue.getShopSaleGoodsNum()) - BaseUtils.getNonNull(shopBoValue.getCsSaleGoodsNum());
        shopBoValue.setSilenceSaleGoodsNum(Math.max(silenceSaleGoodsNum1, 0));
        int silenceSaleBuyerNum1 = BaseUtils.getNonNull(shopBoValue.getShopSaleBuyerNum()) - BaseUtils.getNonNull(shopBoValue.getCsSaleBuyerNum());
        shopBoValue.setSilenceSaleBuyerNum(Math.max(silenceSaleBuyerNum1, 0));
        int silenceSaleOrderNum1 = BaseUtils.getNonNull(shopBoValue.getShopSaleOrderNum()) - BaseUtils.getNonNull(shopBoValue.getCsSaleOrderNum());
        shopBoValue.setSilenceSaleOrderNum(Math.max(silenceSaleOrderNum1, 0));
        //静默出库数据计算
        double silenceOutStockAmount1 = BaseUtils.getNonNull(shopBoValue.getShopOutStockAmount()) - BaseUtils.getNonNull(shopBoValue.getCsOutStockAmount());
        shopBoValue.setSilenceOutStockAmount(silenceOutStockAmount1 > 0 ? silenceOutStockAmount1 : 0.0);
        int silenceOutStockGoodsNum1 = BaseUtils.getNonNull(shopBoValue.getShopOutStockGoodsNum()) - BaseUtils.getNonNull(shopBoValue.getCsOutStockGoodsNum());
        shopBoValue.setSilenceOutStockGoodsNum(Math.max(silenceOutStockGoodsNum1, 0));
        int silenceOutStockNum1 = BaseUtils.getNonNull(shopBoValue.getShopOutStockNum()) - BaseUtils.getNonNull(shopBoValue.getCsOutStockNum());
        shopBoValue.setSilenceOutStockNum(Math.max(silenceOutStockNum1, 0));
        int silenceOutStockOrderNum1 = BaseUtils.getNonNull(shopBoValue.getShopOutStockOrderNum()) - BaseUtils.getNonNull(shopBoValue.getCsOutStockOrderNum());
        shopBoValue.setSilenceOutStockOrderNum(Math.max(silenceOutStockOrderNum1, 0));
        //静默中差评
        int silenceNeutralEvaluateNum1 = BaseUtils.getNonNull(shopBoValue.getShopNeutralEvaluateNum()) - BaseUtils.getNonNull(shopBoValue.getCsNeutralEvaluateNum());
        shopBoValue.setSilenceNeutralEvaluateNum(Math.max(silenceNeutralEvaluateNum1, 0));
        int silenceBadEvaluateNum1 = BaseUtils.getNonNull(shopBoValue.getShopBadEvaluateNum()) - BaseUtils.getNonNull(shopBoValue.getCsBadEvaluateNum());
        shopBoValue.setSilenceBadEvaluateNum(Math.max(silenceBadEvaluateNum1, 0));
        //静默退款数据计算
        int silenceRefundNum1 = BaseUtils.getNonNull(shopBoValue.getShopRefundNum()) - BaseUtils.getNonNull(shopBoValue.getCsRefundNum());
        shopBoValue.setSilenceRefundNum(Math.max(silenceRefundNum1, 0));
        int silenceRefundBuyerNum1 = BaseUtils.getNonNull(shopBoValue.getShopRefundBuyerNum()) - BaseUtils.getNonNull(shopBoValue.getCsRefundBuyerNum());
        shopBoValue.setSilenceRefundBuyerNum(Math.max(silenceRefundBuyerNum1, 0));
        int silenceRefundProductNum1 = BaseUtils.getNonNull(shopBoValue.getShopRefundProductNum()) - BaseUtils.getNonNull(shopBoValue.getCsRefundProductNum());
        shopBoValue.setSilenceRefundProductNum(Math.max(silenceRefundProductNum1, 0));
        double silenceRefundAmount1 = BaseUtils.getNonNull(shopBoValue.getShopRefundAmount()) - BaseUtils.getNonNull(shopBoValue.getCsRefundAmount());
        shopBoValue.setSilenceRefundAmount(silenceRefundAmount1 > 0 ? silenceRefundAmount1 : 0);

        //TODO:静默邮费计算
        shopBoValue.setSilencePostFee(BaseUtils.getNonNull(shopBoValue.getShopPostFee()) - BaseUtils.getNonNull(shopBoValue.getCsPostFee()));



        //非最终数据字段
        List<String> notFinalDataField = shopBo.getNotFinalData();
        //根据字段条件筛选
        switch (property) {
            case "shopSaleAmount":
                double shopSaleAmount = shopBoValue.getShopSaleAmount() == null ? 0.0 : shopBoValue.getShopSaleAmount();
                if (isFilters(filters, 5))
                    shopSaleAmount -= shopBoValue.getShopRefundAmount() == null ? 0.0 : shopBoValue.getShopRefundAmount();
                if (isFilters(filters, 6))
                    shopSaleAmount -= shopBoValue.getShopPostFee() == null ? 0.0 : shopBoValue.getShopPostFee();
                shopBo.setShopSaleAmount(shopSaleAmount);
                break;
            case "shopSaleGoodsNum":
                int shopSaleGoodsNum = shopBoValue.getShopSaleGoodsNum() == null ? 0 : shopBoValue.getShopSaleGoodsNum();
                if (shopSaleGoodsNum > 0 && isFilters(filters, 4))
                    shopSaleGoodsNum -= shopBoValue.getShopRefundProductNum() == null ? 0 : shopBoValue.getShopRefundProductNum();
                shopBo.setShopSaleGoodsNum(Math.max(shopSaleGoodsNum, 0));
                break;
            case "shopSaleBuyerNum":
                shopBo.setShopSaleBuyerNum(shopBoValue.getShopSaleBuyerNum() == null ? 0 : shopBoValue.getShopSaleBuyerNum());
                break;
            case "shopSaleOrderNum":
                shopBo.setShopSaleOrderNum(shopBoValue.getShopSaleOrderNum() == null ? 0 : shopBoValue.getShopSaleOrderNum());
                break;
            case "csSaleAmount":
                double csSaleAmount = shopBoValue.getCsSaleAmount() == null ? 0.0 : shopBoValue.getCsSaleAmount();
                if (isFilters(filters, 8))
                    csSaleAmount -= shopBoValue.getCsRefundAmount() == null ? 0.0 : shopBoValue.getCsRefundAmount();
                if (isFilters(filters, 9))
                    csSaleAmount -= shopBoValue.getCsPostFee() == null ? 0.0 : shopBoValue.getCsPostFee();
                shopBo.setCsSaleAmount(csSaleAmount);
                break;
            case "csSaleGoodsNum":
                int csSaleGoodsNum = shopBoValue.getCsSaleGoodsNum() == null ? 0 : shopBoValue.getCsSaleGoodsNum();
                if (csSaleGoodsNum > 0 && isFilters(filters, 7))
                    csSaleGoodsNum -= shopBoValue.getCsRefundProductNum() == null ? 0 : shopBoValue.getCsRefundProductNum();
                shopBo.setCsSaleGoodsNum(Math.max(csSaleGoodsNum, 0));
                break;
            case "csSaleBuyerNum":
                shopBo.setCsSaleBuyerNum(shopBoValue.getCsSaleBuyerNum() == null ? 0 : shopBoValue.getCsSaleBuyerNum());
                break;
            case "csSaleOrderNum":
                shopBo.setCsSaleOrderNum(shopBoValue.getCsSaleOrderNum() == null ? 0 : shopBoValue.getCsSaleOrderNum());
                break;
            case "csSaleAmountPercent":
//                shopBo.setCsSaleAmount();
                double csSaleAmountTemp = shopBo.getCsSaleAmount() != null && shopBo.getCsSaleAmount() > 0 ? shopBo.getCsSaleAmount() : BaseUtils.getNonNull(shopBoValue.getCsSaleAmount());
                if (shopBo.getShopSaleAmount() != null && shopBo.getShopSaleAmount() == 0.0) {
                    shopBo.setShopSaleAmount(0.0);
                } else {
                    if(null != shopQuery && null != shopQuery.getShopId()){
                        shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                    }else {
                        shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null && shopBo.getShopSaleAmount() > 0 ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                    }
                }

                shopBo.setCsSaleAmountPercent(shopBo.getShopSaleAmount() > 0 ? csSaleAmountTemp / shopBo.getShopSaleAmount() : 0);
                break;
            case "csTransactionsNum":
                shopBo.setCsTransactionsNum(shopBoValue.getCsTransactionsNum() == null ? 0 : shopBoValue.getCsTransactionsNum());
                break;
            case "silenceSaleAmount":
                double silenceSaleAmount = shopBoValue.getSilenceSaleAmount();
                if ( isFilters(filters, 2))
                    silenceSaleAmount -= shopBoValue.getSilenceRefundAmount() == null ? 0.0 : shopBoValue.getSilenceRefundAmount();
                if ( isFilters(filters, 3))
                    silenceSaleAmount -= shopBoValue.getSilencePostFee() == null ? 0.0 : shopBoValue.getSilencePostFee();
                shopBo.setSilenceSaleAmount(silenceSaleAmount);
                break;
            case "silenceSaleGoodsNum":
                int silenceSaleGoodsNum = shopBoValue.getSilenceSaleGoodsNum();
                if (silenceSaleGoodsNum > 0 && isFilters(filters, 1))
                    silenceSaleGoodsNum -= shopBoValue.getSilenceRefundProductNum() == null ? 0 : shopBoValue.getSilenceRefundProductNum();
                shopBo.setSilenceSaleGoodsNum(Math.max(silenceSaleGoodsNum, 0));
                break;
            case "silenceSaleBuyerNum":
                shopBo.setSilenceSaleBuyerNum(shopBoValue.getSilenceSaleBuyerNum());
                break;
            case "silenceSaleOrderNum":
                shopBo.setSilenceSaleOrderNum(shopBoValue.getSilenceSaleOrderNum());
                break;
            case "silenceSaleAmountPercent":
                shopBo.setSilenceSaleAmount(shopBo.getSilenceSaleAmount() != null && shopBo.getSilenceSaleAmount() > 0 ? shopBo.getSilenceSaleAmount() : BaseUtils.getNonNull(shopBoValue.getSilenceSaleAmount()));
                if(null != shopQuery && null != shopQuery.getShopId()){
                    shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                }else{
                    shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null && shopBo.getShopSaleAmount() > 0 ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                }
                shopBo.setSilenceSaleAmountPercent(shopBo.getShopSaleAmount() > 0 ? shopBo.getSilenceSaleAmount() / shopBo.getShopSaleAmount() : 0.0);
                break;
            case "pvNum":
                shopBo.setPvNum(shopBoValue.getPvNum() == null ? 0 : shopBoValue.getPvNum());
                break;
            case "uvNum":
                shopBo.setUvNum(shopBoValue.getUvNum() == null ? 0 : shopBoValue.getUvNum());
                break;
            case "consultNum":
                shopBo.setConsultNum(shopBoValue.getConsultNum() == null ? 0 : shopBoValue.getConsultNum());
                break;
            case "consultSessionNum":
                shopBo.setConsultSessionNum(shopBoValue.getConsultSessionNum() == null ? 0 : shopBoValue.getConsultSessionNum());
                break;
            case "receiveNum":
                shopBo.setReceiveNum(shopBoValue.getReceiveNum() == null ? 0 : shopBoValue.getReceiveNum());
                break;
            case "receiveSessionNum":
                shopBo.setReceiveSessionNum(shopBoValue.getReceiveSessionNum() == null ? 0 : shopBoValue.getReceiveSessionNum());
                break;
            case "enquiryNum":
                shopBo.setEnquiryNum(shopBoValue.getEnquiryNum() == null ? 0 : shopBoValue.getEnquiryNum());
//                if (isDealy(key, dateType, dates, enquiryValidDurationTime)) shopBo.setEnquiryNum(-1);
                if (isDealy(key, dateType, dates, enquiryValidDurationTime)) notFinalDataField.add("enquiryNum");
                break;
            case "consultPercent":
                if (shopBo.getConsultNum() == null)
                    shopBo.setConsultNum(BaseUtils.getNonNull(shopBoValue.getConsultNum()));
                if (shopBo.getUvNum() == null) shopBo.setUvNum(BaseUtils.getNonNull(shopBoValue.getUvNum()));
                shopBo.setConsultPercent(shopBo.getUvNum() > 0 ? (double) shopBo.getConsultNum() / shopBo.getUvNum() : 0.0);
                break;
            case "shopSaleGuestAvgAmount":
                if(null != shopQuery && null != shopQuery.getShopId()){
                    shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                }else {
                    shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null && shopBo.getShopSaleAmount() > 0 ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                }
                if (shopBo.getShopSaleBuyerNum() == null)
                    shopBo.setShopSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getShopSaleBuyerNum()));
                shopBo.setShopSaleGuestAvgAmount(shopBo.getShopSaleBuyerNum() > 0 ? shopBo.getShopSaleAmount() / shopBo.getShopSaleBuyerNum() : 0.0);
                break;
            case "shopSaleGuestAvgGoods":
                if (shopBo.getShopSaleGoodsNum() == null)
                    shopBo.setShopSaleGoodsNum(BaseUtils.getNonNull(shopBoValue.getShopSaleGoodsNum()));
                if (shopBo.getShopSaleBuyerNum() == null)
                    shopBo.setShopSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getShopSaleBuyerNum()));
                shopBo.setShopSaleGuestAvgGoods(shopBo.getShopSaleBuyerNum() > 0 ? (double) shopBo.getShopSaleGoodsNum() / shopBo.getShopSaleBuyerNum() : 0.0);
                break;
            case "shopSaleGoodsAvgAmount":
                if(null != shopQuery && null != shopQuery.getShopId()){
                    shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                }else{
                    shopBo.setShopSaleAmount(shopBo.getShopSaleAmount() != null && shopBo.getShopSaleAmount() > 0 ? shopBo.getShopSaleAmount() : BaseUtils.getNonNull(shopBoValue.getShopSaleAmount()));
                }
                if (shopBo.getShopSaleGoodsNum() == null)
                    shopBo.setShopSaleGoodsNum(BaseUtils.getNonNull(shopBoValue.getShopSaleGoodsNum()));
                shopBo.setShopSaleGoodsAvgAmount(shopBo.getShopSaleGoodsNum() > 0 ? shopBo.getShopSaleAmount() / shopBo.getShopSaleGoodsNum() : 0.0);
                break;
            case "shopOutStockGuestAvgAmount":
                shopBo.setShopOutStockAmount(BaseUtils.getNonNull(shopBoValue.getShopOutStockAmount()));
                shopBo.setShopOutStockNum(BaseUtils.getNonNull(shopBoValue.getShopOutStockNum()));
                shopBo.setShopOutStockGuestAvgAmount(shopBo.getShopOutStockNum() > 0 ? shopBo.getShopOutStockAmount() / shopBo.getShopOutStockNum() : 0.0);
                break;
            case "shopOutStockGuestItemNum":
                shopBo.setShopOutStockGoodsNum(BaseUtils.getNonNull(shopBoValue.getShopOutStockGoodsNum()));
                shopBo.setShopOutStockNum(BaseUtils.getNonNull(shopBoValue.getShopOutStockNum()));
                shopBo.setShopOutStockGuestItemNum(shopBo.getShopOutStockNum() > 0 ? (double) shopBo.getShopOutStockGoodsNum() / shopBo.getShopOutStockNum() : 0.0);
                break;
            case "shopOutStockItemAvgAmount":
                shopBo.setShopOutStockAmount(BaseUtils.getNonNull(shopBoValue.getShopOutStockAmount()));
                shopBo.setShopOutStockGoodsNum(BaseUtils.getNonNull(shopBoValue.getShopOutStockGoodsNum()));
                shopBo.setShopOutStockItemAvgAmount(shopBo.getShopOutStockGoodsNum() > 0 ? shopBo.getShopOutStockAmount() / shopBo.getShopOutStockGoodsNum() : 0.0);
                break;
            case "csSaleGuestAvgAmount":
                shopBo.setCsSaleAmount(shopBo.getCsSaleAmount() != null && shopBo.getCsSaleAmount() > 0 ? shopBo.getCsSaleAmount() : BaseUtils.getNonNull(shopBoValue.getCsSaleAmount()));
                shopBo.setCsSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getCsSaleBuyerNum()));
                shopBo.setCsSaleGuestAvgAmount(shopBo.getCsSaleBuyerNum() > 0 ? shopBo.getCsSaleAmount() / shopBo.getCsSaleBuyerNum() : 0.0);
                break;
            case "csSaleGuestAvgGoods":
                shopBo.setCsSaleGoodsNum(shopBo.getCsSaleGoodsNum() != null && shopBo.getCsSaleGoodsNum() > 0 ? shopBo.getCsSaleGoodsNum() : BaseUtils.getNonNull(shopBoValue.getCsSaleGoodsNum()));
                shopBo.setCsSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getCsSaleBuyerNum()));
                shopBo.setCsSaleGuestAvgGoods(shopBo.getCsSaleBuyerNum() > 0 ? (double) shopBo.getCsSaleGoodsNum() / shopBo.getCsSaleBuyerNum() : 0.0);
                break;
            case "csSaleGoodsAvgAmount":
                shopBo.setCsSaleAmount(shopBo.getCsSaleAmount() != null && shopBo.getCsSaleAmount() > 0 ? shopBo.getCsSaleAmount() : BaseUtils.getNonNull(shopBoValue.getCsSaleAmount()));
                shopBo.setCsSaleGoodsNum(shopBo.getCsSaleGoodsNum() != null && shopBo.getCsSaleGoodsNum() > 0 ? shopBo.getCsSaleGoodsNum() : BaseUtils.getNonNull(shopBoValue.getCsSaleGoodsNum()));
                shopBo.setCsSaleGoodsAvgAmount(shopBo.getCsSaleGoodsNum() > 0 ? shopBo.getCsSaleAmount() / shopBo.getCsSaleGoodsNum() : 0.0);
                break;
            case "csOutStockGuestAvgAmount":
                shopBo.setCsOutStockAmount(BaseUtils.getNonNull(shopBoValue.getCsOutStockAmount()));
                shopBo.setCsOutStockNum(BaseUtils.getNonNull(shopBoValue.getCsOutStockNum()));
                shopBo.setCsOutStockGuestAvgAmount(shopBo.getCsOutStockNum() > 0 ? shopBo.getCsOutStockAmount() / shopBo.getCsOutStockNum() : 0.0);
                break;
            case "csOutStockGuestItemNum":
                shopBo.setCsOutStockGoodsNum(BaseUtils.getNonNull(shopBoValue.getCsOutStockGoodsNum()));
                shopBo.setCsOutStockNum(BaseUtils.getNonNull(shopBoValue.getCsOutStockNum()));
                shopBo.setCsOutStockGuestItemNum(shopBo.getCsOutStockNum() > 0 ? (double) shopBo.getCsOutStockGoodsNum() / shopBo.getCsOutStockNum() : 0.0);
                break;
            case "csOutStockItemAvgAmount":
                shopBo.setCsOutStockAmount(BaseUtils.getNonNull(shopBoValue.getCsOutStockAmount()));
                shopBo.setCsOutStockGoodsNum(BaseUtils.getNonNull(shopBoValue.getCsOutStockGoodsNum()));
                shopBo.setCsOutStockItemAvgAmount(shopBo.getCsOutStockGoodsNum() > 0 ? shopBo.getCsOutStockAmount() / shopBo.getCsOutStockGoodsNum() : 0.0);
                break;
            case "silenceSaleGuestAvgAmount":
                shopBo.setSilenceSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getSilenceSaleBuyerNum()));
                shopBo.setSilenceSaleAmount(shopBo.getSilenceSaleAmount() != null && shopBo.getSilenceSaleAmount() > 0 ? shopBo.getSilenceSaleAmount() : BaseUtils.getNonNull(shopBoValue.getSilenceSaleAmount()));
                shopBo.setSilenceSaleGuestAvgAmount(shopBo.getSilenceSaleBuyerNum() > 0 ? shopBo.getSilenceSaleAmount() / shopBo.getSilenceSaleBuyerNum() : 0.0);
                break;
            case "silenceSaleGuestAvgGoods":
                shopBo.setSilenceSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getSilenceSaleBuyerNum()));
                shopBo.setSilenceSaleGoodsNum(shopBo.getSilenceSaleGoodsNum() != null && shopBo.getSilenceSaleGoodsNum() > 0 ? shopBo.getSilenceSaleGoodsNum() : BaseUtils.getNonNull(shopBoValue.getSilenceSaleGoodsNum()));
                shopBo.setSilenceSaleGuestAvgGoods(shopBo.getSilenceSaleBuyerNum() > 0 ? (double) shopBo.getSilenceSaleGoodsNum() / shopBo.getSilenceSaleBuyerNum() : 0.0);
                break;
            case "silenceSaleGoodsAvgAmount":
                shopBo.setSilenceSaleAmount(shopBo.getSilenceSaleAmount() != null && shopBo.getSilenceSaleAmount() > 0 ? shopBo.getSilenceSaleAmount() : BaseUtils.getNonNull(shopBoValue.getSilenceSaleAmount()));
                shopBo.setSilenceSaleGoodsNum(shopBo.getSilenceSaleGoodsNum() != null && shopBo.getSilenceSaleGoodsNum() > 0 ? shopBo.getSilenceSaleGoodsNum() : BaseUtils.getNonNull(shopBoValue.getSilenceSaleGoodsNum()));
                shopBo.setSilenceSaleGoodsAvgAmount(shopBo.getSilenceSaleGoodsNum() > 0 ? shopBo.getSilenceSaleAmount() / shopBo.getSilenceSaleGoodsNum() : 0.0);
                break;
            case "silenceOutStockGuestAvgAmount":
                shopBo.setSilenceOutStockNum(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockNum()));
                shopBo.setSilenceOutStockAmount(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockAmount()));
                shopBo.setSilenceOutStockGuestAvgAmount(shopBo.getSilenceOutStockNum() > 0 ? shopBo.getSilenceOutStockAmount() / shopBo.getSilenceOutStockNum() : 0.0);
                break;
            case "silenceOutStockGuestItemNum":
                shopBo.setSilenceOutStockNum(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockNum()));
                shopBo.setSilenceOutStockGoodsNum(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockGoodsNum()));
                shopBo.setSilenceOutStockGuestItemNum(shopBo.getSilenceOutStockNum() > 0 ? (double) shopBo.getSilenceOutStockGoodsNum() / shopBo.getSilenceOutStockNum() : 0.0);
                break;
            case "silenceOutStockItemAvgAmount":
                shopBo.setSilenceOutStockGoodsNum(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockGoodsNum()));
                shopBo.setSilenceOutStockAmount(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockAmount()));
                shopBo.setSilenceOutStockItemAvgAmount(shopBo.getSilenceOutStockGoodsNum() > 0 ? shopBo.getSilenceOutStockAmount() / shopBo.getSilenceOutStockGoodsNum() : 0.0);
                break;
            case "shopDealPercent":
                shopBo.setShopSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getShopSaleBuyerNum()));
                shopBo.setUvNum(BaseUtils.getNonNull(shopBoValue.getUvNum()));
                shopBo.setShopDealPercent(shopBo.getUvNum() > 0 ? (double) shopBo.getShopSaleBuyerNum() / shopBo.getUvNum() : 0.0);
                break;
            case "shopOutStockPercent":
                shopBo.setShopOutStockNum(BaseUtils.getNonNull(shopBoValue.getShopOutStockNum()));
                shopBo.setUvNum(BaseUtils.getNonNull(shopBoValue.getUvNum()));
                shopBo.setShopOutStockPercent(shopBo.getUvNum() > 0 ? (double) shopBo.getShopOutStockNum() / shopBo.getUvNum() : 0.0);
                break;
            case "silenceDealPercent":
                shopBo.setSilenceSaleBuyerNum(BaseUtils.getNonNull(shopBoValue.getSilenceSaleBuyerNum()));
                shopBo.setUvNum(BaseUtils.getNonNull(shopBoValue.getUvNum()));
                shopBo.setSilenceDealPercent(shopBo.getUvNum() > 0 ? (double) shopBo.getSilenceSaleBuyerNum() / shopBo.getUvNum() : 0.0);
                break;
            case "queryToTomorrow":
                shopBo.setPaidNumTodayNext(BaseUtils.getNonNull(shopBoValue.getPaidNumTodayNext()));
                shopBo.setEnquiryNum(BaseUtils.getNonNull(shopBoValue.getEnquiryNum()));
                shopBo.setQueryToTomorrow(shopBo.getEnquiryNum() > 0 ? (double) shopBo.getPaidNumTodayNext() / shopBo.getEnquiryNum() : 0.0);
                if (isDealy(key, dateType, dates, enquiryValidDurationTime)) {
                    notFinalDataField.add("queryToTomorrow");
                }
                break;
            case "queryToFinalPaid":
                shopBo.setPaidNumFinal(BaseUtils.getNonNull(shopBoValue.getPaidNumFinal()));
                shopBo.setEnquiryNum(BaseUtils.getNonNull(shopBoValue.getEnquiryNum()));
                shopBo.setQueryToFinalPaid(shopBo.getEnquiryNum() > 0 ? (double) shopBo.getPaidNumFinal() / shopBo.getEnquiryNum() : 0.0);
                if (isDealy(key, dateType, dates, enquiryValidDurationTime + 1)) {
                    notFinalDataField.add("queryToFinalPaid");
                }
                break;
            case "queryToOrderedToday":
                shopBo.setOrderedNumToday(BaseUtils.getNonNull(shopBoValue.getOrderedNumToday()));
                shopBo.setEnquiryNum(BaseUtils.getNonNull(shopBoValue.getEnquiryNum()));
                shopBo.setQueryToOrderedToday(shopBo.getEnquiryNum() > 0 ? (double) shopBo.getOrderedNumToday() / shopBo.getEnquiryNum() : 0.0);
                if (isDealy(key, dateType, dates, enquiryValidDurationTime)) {
                    notFinalDataField.add("queryToOrderedToday");
                }
                break;
            case "queryToFinalOrdered":
                shopBo.setOrderedNumFinal(BaseUtils.getNonNull(shopBoValue.getOrderedNumFinal()));
                shopBo.setEnquiryNum(BaseUtils.getNonNull(shopBoValue.getEnquiryNum()));
                shopBo.setQueryToFinalOrdered(shopBo.getEnquiryNum() > 0 ? (double) shopBo.getOrderedNumFinal() / shopBo.getEnquiryNum() : 0.0);
                if (isDealy(key, dateType, dates, enquiryValidDurationTime)) {
                    notFinalDataField.add("queryToFinalOrdered");
                }
                break;
            case "orderedToPaid":
                shopBo.setToOrderedNum(BaseUtils.getNonNull(shopBoValue.getToOrderedNum()));
                shopBo.setOrderedPaidNumToday(BaseUtils.getNonNull(shopBoValue.getOrderedPaidNumToday()));
                shopBo.setOrderedToPaid(shopBo.getToOrderedNum() > 0 ? (double) shopBo.getOrderedPaidNumToday() / shopBo.getToOrderedNum() : 0.0);
                break;
            case "orderedToPaidFinal":
                shopBo.setOrderedPaidNumFinal(BaseUtils.getNonNull(shopBoValue.getOrderedPaidNumFinal()));
                shopBo.setToOrderedNum(BaseUtils.getNonNull(shopBoValue.getToOrderedNum()));
                shopBo.setOrderedToPaidFinal(shopBo.getToOrderedNum() > 0 ? (double) shopBo.getOrderedPaidNumFinal() / shopBo.getToOrderedNum() : 0.0);
                if (isDealy(key, dateType, dates, 2)) {
                    notFinalDataField.add("orderedToPaidFinal");
                }
                break;
            case "queryToOutStock":
                shopBo.setCsOutStockBuyerNumFinal(BaseUtils.getNonNull(shopBoValue.getCsOutStockBuyerNumFinal()));
                shopBo.setEnquiryNum(BaseUtils.getNonNull(shopBoValue.getEnquiryNum()));
                shopBo.setQueryToOutStock(shopBo.getEnquiryNum() > 0 ? (double) shopBo.getCsOutStockBuyerNumFinal() / shopBo.getEnquiryNum() : 0.0);
                if (isDealy(key, dateType, dates, enquiryValidDurationTime + outStockValidDurationTime - 1)) {
                    notFinalDataField.add("queryToOutStock");
                }
                break;
            case "orderedNumToday":
                shopBo.setOrderedNumToday(BaseUtils.getNonNull(shopBoValue.getOrderedNumToday()));
                break;
            case "orderedAmountToday":
                shopBo.setOrderedAmountToday(BaseUtils.getNonNull(shopBoValue.getOrderedAmountToday()));
                break;
            case "orderedAmountFinal":
                shopBo.setOrderedAmountFinal(shopBoValue.getOrderedAmountFinal() == null ? 0.0 : shopBoValue.getOrderedAmountFinal());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("orderedAmountFinal");
                break;
            case "orderedNumFinal":
                shopBo.setOrderedNumFinal(shopBoValue.getOrderedNumFinal() == null ? 0 : shopBoValue.getOrderedNumFinal());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("orderedNumFinal");
                break;
            case "orderItemAvgAmount":
                shopBo.setToOrderedAmount(BaseUtils.getNonNull(shopBoValue.getToOrderedAmount()));
                shopBo.setToOrderedGoodsNum(BaseUtils.getNonNull(shopBoValue.getToOrderedGoodsNum()));
                shopBo.setOrderItemAvgAmount(shopBo.getToOrderedGoodsNum() > 0 ? (double) shopBo.getToOrderedAmount() / shopBo.getToOrderedGoodsNum() : 0.0);
                break;
            case "orderedGuestAvgPrice":
                shopBo.setToOrderedAmount(shopBoValue.getToOrderedAmount());
                shopBo.setToOrderedNum(shopBoValue.getToOrderedNum());
                shopBo.setOrderedGuestAvgPrice(shopBo.getToOrderedNum() > 0 ? (double) shopBo.getToOrderedAmount() / shopBo.getToOrderedNum() : 0.0);
                break;
            case "orderedGuestAvgAmount":
                shopBo.setToOrderedGoodsNum(shopBoValue.getToOrderedGoodsNum());
                shopBo.setToOrderedNum(shopBoValue.getToOrderedNum());
                shopBo.setOrderedGuestAvgAmount(shopBo.getToOrderedNum() > 0 ? (double) shopBo.getToOrderedGoodsNum() / shopBo.getToOrderedNum() : 0.0);
                break;
            case "orderedPaidNumToday":
                shopBo.setOrderedPaidNumToday(shopBoValue.getOrderedPaidNumToday() == null ? 0 : shopBoValue.getOrderedPaidNumToday());
                break;
            case "orderedPaidGoodsToday":
                shopBo.setOrderedPaidGoodsToday(shopBoValue.getOrderedPaidGoodsToday() == null ? 0 : shopBoValue.getOrderedPaidGoodsToday());
                break;
            case "orderedPaidAmountToday":
                shopBo.setOrderedPaidAmountToday(shopBoValue.getOrderedPaidAmountToday() == null ? 0.0 : shopBoValue.getOrderedPaidAmountToday());
                break;
            case "orderedPaidOrdersToday":
                shopBo.setOrderedPaidOrdersToday(shopBoValue.getOrderedPaidOrdersToday() == null ? 0 : shopBoValue.getOrderedPaidOrdersToday());
                break;
            case "orderedPaidNumFinal":
                shopBo.setOrderedPaidNumFinal(shopBoValue.getOrderedPaidNumFinal() == null ? 0 : shopBoValue.getOrderedPaidNumFinal());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("orderedPaidNumFinal");
                break;
            case "orderedPaidGoodsFinal":
                shopBo.setOrderedPaidGoodsFinal(shopBoValue.getOrderedPaidGoodsFinal() == null ? 0 : shopBoValue.getOrderedPaidGoodsFinal());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("orderedPaidGoodsFinal");
                break;
            case "orderedPaidAmountFinal":
                shopBo.setOrderedPaidAmountFinal(shopBoValue.getOrderedPaidAmountFinal() == null ? 0.0 : shopBoValue.getOrderedPaidAmountFinal());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("orderedPaidAmountFinal");
                break;
            case "orderedPaidOrdersFinal":
                shopBo.setOrderedPaidOrdersFinal(shopBoValue.getOrderedPaidOrdersFinal() == null ? 0 : shopBoValue.getOrderedPaidOrdersFinal());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("orderedPaidOrdersFinal");
                break;
            case "orderedOutStockNum":
                shopBo.setOrderedOutStockNum(shopBoValue.getOrderedOutStockNum() == null ? 0 : shopBoValue.getOrderedOutStockNum());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("orderedOutStockNum");
                break;
            case "orderedOutStockGoods":
                shopBo.setOrderedOutStockGoods(shopBoValue.getOrderedOutStockGoods() == null ? 0 : shopBoValue.getOrderedOutStockGoods());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("orderedOutStockGoods");
                break;
            case "orderedOutStockAmount":
                shopBo.setOrderedOutStockAmount(shopBoValue.getOrderedOutStockAmount() == null ? 0.0 : shopBoValue.getOrderedOutStockAmount());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("orderedOutStockAmount");
                break;
            case "orderedOutStockOrders":
                shopBo.setOrderedOutStockOrders(shopBoValue.getOrderedOutStockOrders() == null ? 0 : shopBoValue.getOrderedOutStockOrders());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("orderedOutStockOrders");
                break;
            case "paidNumTodayNext":
                shopBo.setPaidNumTodayNext(shopBoValue.getPaidNumTodayNext() == null ? 0 : shopBoValue.getPaidNumTodayNext());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("paidNumTodayNext");
                break;
            case "paidNumFinal":
                shopBo.setPaidNumFinal(shopBoValue.getPaidNumFinal() == null ? 0 : shopBoValue.getPaidNumFinal());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime + 1))
                    notFinalDataField.add("paidNumFinal");
                break;
            case "toOrderedNum":
                shopBo.setToOrderedNum(shopBoValue.getToOrderedNum() == null ? 0 : shopBoValue.getToOrderedNum());
                break;
            case "toOrderedGoodsNum":
                shopBo.setToOrderedGoodsNum(shopBoValue.getToOrderedGoodsNum() == null ? 0 : shopBoValue.getToOrderedGoodsNum());
                break;
            case "toOrderedAmount":
                shopBo.setToOrderedAmount(shopBoValue.getToOrderedAmount() == null ? 0.0 : shopBoValue.getToOrderedAmount());
                break;
            case "toOrderedOrderNum":
                shopBo.setToOrderedOrderNum(shopBoValue.getToOrderedOrderNum() == null ? 0 : shopBoValue.getToOrderedOrderNum());
                break;
            case "dutyCsNum":
                shopBo.setDutyCsNum(shopBoValue.getDutyCsNum() == null ? 0 : shopBoValue.getDutyCsNum());
                break;
            case "loginDurationTime":
                shopBo.setLoginDurationTime(shopBoValue.getLoginDurationTime() == null ? 0 : shopBoValue.getLoginDurationTime());
                break;
            case "rceiveDurationTime":
                shopBo.setRceiveDurationTime(shopBoValue.getRceiveDurationTime() == null ? 0 : shopBoValue.getRceiveDurationTime());
                break;
            case "chatNum":
                shopBo.setChatNum(shopBoValue.getChatNum() == null ? 0 : shopBoValue.getChatNum());
                break;
            case "custChatNum":
                shopBo.setCustChatNum(shopBoValue.getCustChatNum() == null ? 0 : shopBoValue.getCustChatNum());
                break;
            case "csChatNum":
                shopBo.setCsChatNum(shopBoValue.getCsChatNum() == null ? 0 : shopBoValue.getCsChatNum());
                break;
            case "avgReplyMsg":
                shopBo.setCsChatNum(BaseUtils.getNonNull(shopBoValue.getCsChatNum()));
                shopBo.setReceiveSessionNum(BaseUtils.getNonNull(shopBoValue.getReceiveSessionNum()));
                shopBo.setAvgReplyMsg(shopBo.getReceiveSessionNum() > 0 ? (double) shopBo.getCsChatNum() / shopBo.getReceiveSessionNum() : 0.0);
                break;
            case "answerRatio":
                shopBo.setCsChatNum(BaseUtils.getNonNull(shopBoValue.getCsChatNum()));
                shopBo.setCustChatNum(BaseUtils.getNonNull(shopBoValue.getCustChatNum()));
                shopBo.setAnswerRatio(shopBo.getCustChatNum() > 0 ? (double) shopBo.getCsChatNum() / shopBo.getCustChatNum() : 0.0);
                break;
            case "csWordNum":
                shopBo.setCsWordNum(shopBoValue.getCsWordNum() == null ? 0 : shopBoValue.getCsWordNum());
                break;
            case "maxReceiveSessionNum":
                shopBo.setMaxReceiveSessionNum(shopBoValue.getMaxReceiveSessionNum() == null ? 0 : shopBoValue.getMaxReceiveSessionNum());
                break;
            case "nonReplySessionNum":
                shopBo.setNonReplySessionNum(shopBoValue.getNonReplySessionNum() == null ? 0 : shopBoValue.getNonReplySessionNum());
                break;
            case "responseRate":
//                接待量
                Integer receiveSessionNum = BaseUtils.getNonNull(shopBoValue.getReceiveSessionNum());
                shopBo.setReceiveSessionNum(receiveSessionNum);
//                （咨询量-未回复量）/咨询量，转出量及留言分配量不计入统计
                shopBo.setConsultSessionNum(BaseUtils.getNonNull(shopBoValue.getConsultSessionNum()));
//                未回复量
                Integer nonReplySessionNum = BaseUtils.getNonNull(shopBoValue.getNonReplySessionNum());
                shopBo.setNonReplySessionNum(nonReplySessionNum);
                shopBo.setEmptyChatNum(BaseUtils.getNonNull(shopBoValue.getEmptyChatNum()));
                //转出量
                shopBo.setForwardOutSessionNum(BaseUtils.getNonNull(shopBoValue.getForwardOutSessionNum()));
                //留言分配量
                shopBo.setLeaveMsgSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgSessionNum()));
                shopBo.setResponseRate(receiveSessionNum == 0 ? 0 : (receiveSessionNum - nonReplySessionNum) * 1.0 / receiveSessionNum);
                break;
            case "slowRespSessionNum":
                shopBo.setSlowRespSessionNum(shopBoValue.getSlowRespSessionNum() == null ? 0 : shopBoValue.getSlowRespSessionNum());
                break;
            case "longRespSessionNum":
                shopBo.setLongRespSessionNum(shopBoValue.getLongRespSessionNum() == null ? 0 : shopBoValue.getLongRespSessionNum());
                break;
            case "quickResponseRate"://释义：根据设置30s（可点此设置）内有人工接待的（咨询量-未回复）/咨询量，转出量及留言分配量不计入统计
                shopBo.setSessionNum(BaseUtils.getNonNull(shopBoValue.getSessionNum()));
                shopBo.setLeaveMsgReceiveSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgReceiveSessionNum()));
                //留言分配量
                shopBo.setLeaveMsgSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgSessionNum()));
                shopBo.setForwardOutSessionNum(BaseUtils.getNonNull(shopBoValue.getForwardOutSessionNum()));
                shopBo.setNonReplySessionNum(BaseUtils.getNonNull(shopBoValue.getNonReplySessionNum()));
                shopBo.setConsultSessionNum(BaseUtils.getNonNull(shopBoValue.getConsultSessionNum()));
                shopBo.setEmptyChatNum(BaseUtils.getNonNull(shopBoValue.getEmptyChatNum()));
                shopBo.setAvgRespInQuickTime(BaseUtils.getNonNull(shopBoValue.getAvgRespInQuickTime()));
                int calSessionNum = shopBo.getSessionNum() - shopBo.getNonReplySessionNum() - BaseUtils.getNonNull(shopBoValue.getLeaveMsgReceiveSessionNum());
                shopBo.setQuickResponseRate(calSessionNum > 0 ? shopBo.getAvgRespInQuickTime() * 1.0 / calSessionNum : 0.0);
                break;
            case "avgRespTimeFirst":
                shopBo.setSessionNum(BaseUtils.getNonNull(shopBoValue.getSessionNum()));
                shopBo.setConsultSessionNum(BaseUtils.getNonNull(shopBoValue.getConsultSessionNum()));
                shopBo.setRespTimeFirstCount(BaseUtils.getNonNull(shopBoValue.getRespTimeFirstCount()));
                int calSessioinNum = shopBo.getSessionNum() - BaseUtils.getNonNull(shopBoValue.getNonReplySessionNum()) - BaseUtils.getNonNull(shopBoValue.getLeaveMsgReceiveSessionNum());
                shopBo.setAvgRespTimeFirst(
                        calSessioinNum >
                                0 ? shopBo.getRespTimeFirstCount() / calSessioinNum : 0.0);
                if (shopBo.getAvgRespTimeFirst() == 0)
                    shopBo.setAvgRespTimeFirst(BaseUtils.getNonNull(shopBoValue.getAvgRespTimeFirst()));
                break;
            case "avgRespTime":
                shopBo.setChatRoundNumNoLeave(shopBoValue.getChatRoundNumNoLeave() != null && shopBoValue.getChatRoundNumNoLeave() > 0 ? shopBoValue.getChatRoundNumNoLeave() : BaseUtils.getNonNull(shopBoValue.getChatRoundNum()));
                shopBo.setChatRoundNum(BaseUtils.getNonNull(shopBoValue.getChatRoundNum()));
                shopBo.setRespTimeCount(BaseUtils.getNonNull(shopBoValue.getRespTimeCount()));
                shopBo.setAvgRespTime(shopBo.getChatRoundNum() > 0 ? shopBo.getRespTimeCount() / shopBo.getChatRoundNum() : 0.0);
                if (shopBo.getAvgRespTime() == 0)
                    shopBo.setAvgRespTime(BaseUtils.getNonNull(shopBoValue.getAvgRespTime()));
                break;
            case "avgSessionDurationTime":
                shopBo.setConsultSessionNum(BaseUtils.getNonNull(shopBoValue.getConsultSessionNum()));
                shopBo.setSessionDurationTimeCount(BaseUtils.getNonNull(shopBoValue.getSessionDurationTimeCount()));
                shopBo.setLeaveMsgSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgSessionNum()));
                shopBo.setForwardOutSessionNum(BaseUtils.getNonNull(shopBoValue.getForwardOutSessionNum()));
                shopBo.setNonReplySessionNum(BaseUtils.getNonNull(shopBoValue.getNonReplySessionNum()));
                shopBo.setEmptyChatNum(BaseUtils.getNonNull(shopBoValue.getEmptyChatNum()));
                shopBo.setAvgSessionDurationTime(BaseUtils.getNonNull(shopBoValue.getSessionNum()) > 0 ? shopBo.getSessionDurationTimeCount() / BaseUtils.getNonNull(shopBoValue.getSessionNum()) : 0.0);
                if (shopBo.getAvgSessionDurationTime() == 0)
                    shopBo.setAvgSessionDurationTime(BaseUtils.getNonNull(shopBoValue.getAvgSessionDurationTime()));
                break;
            case "advisoryMessageNum":
                shopBo.setAdvisoryMessageNum(shopBoValue.getAdvisoryMessageNum() == null ? 0 : shopBoValue.getAdvisoryMessageNum());
                if (isDealy(key, dateType, dates, 2))
                    notFinalDataField.add("advisoryMessageNum");
                break;
            case "leaveMsgSessionNum":
                shopBo.setLeaveMsgSessionNum(shopBoValue.getLeaveMsgSessionNum() == null ? 0 : shopBoValue.getLeaveMsgSessionNum());
                if (isDealy(key, dateType, dates, 2))
                    notFinalDataField.add("leaveMsgSessionNum");
                break;
            case "leaveMsgReceiveSessionNum":
                shopBo.setLeaveMsgReceiveSessionNum(shopBoValue.getLeaveMsgReceiveSessionNum() == null ? 0 : shopBoValue.getLeaveMsgReceiveSessionNum());
                if (isDealy(key, dateType, dates, 2))
                    notFinalDataField.add("leaveMsgReceiveSessionNum");
                break;
            case "leaveMsgReplyRate":
                shopBo.setLeaveMsgReceiveSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgReceiveSessionNum()));
                shopBo.setAdvisoryMessageNum(BaseUtils.getNonNull(shopBoValue.getAdvisoryMessageNum()));
                shopBo.setLeaveMsgReplyRate(shopBo.getAdvisoryMessageNum() > 0 ? (double) shopBo.getLeaveMsgReceiveSessionNum() / shopBo.getAdvisoryMessageNum() : 0.0);
                if (isDealy(key, dateType, dates, 2)) {
                    notFinalDataField.add("leaveMsgReplyRate");
                }
                break;
            case "leaveMsgResponseRate":
                shopBo.setLeaveMsgReceiveSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgReceiveSessionNum()));
                shopBo.setLeaveMsgSessionNum(BaseUtils.getNonNull(shopBoValue.getLeaveMsgSessionNum()));
                shopBo.setLeaveMsgResponseRate(shopBo.getLeaveMsgSessionNum() > 0 ? (double) shopBo.getLeaveMsgReceiveSessionNum() / shopBo.getLeaveMsgSessionNum() : 0.0);
                if (isDealy(key, dateType, dates, 2)) {
                    notFinalDataField.add("leaveMsgResponseRate");
                }
                break;
            case "aidOrderNum":
                shopBo.setAidOrderNum(shopBoValue.getAidOrderNum() == null ? 0 : shopBoValue.getAidOrderNum());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("aidOrderNum");
                break;
            case "aidOrderAmount":
                shopBo.setAidOrderAmount(shopBoValue.getAidOrderAmount() == null ? 0 : shopBoValue.getAidOrderAmount());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("aidOrderAmount");
                break;
            case "aidFollowNum":
                shopBo.setAidFollowNum(shopBoValue.getAidFollowNum() == null ? 0 : shopBoValue.getAidFollowNum());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("aidFollowNum");
                break;
            case "aidFollowAmount":
                shopBo.setAidFollowAmount(shopBoValue.getAidFollowAmount() == null ? 0 : shopBoValue.getAidFollowAmount());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("aidFollowAmount");
                break;
            case "aidPayNum":
                shopBo.setAidPayNum(shopBoValue.getAidPayNum() == null ? 0 : shopBoValue.getAidPayNum());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime)) notFinalDataField.add("aidPayNum");
                break;
            case "aidPayAmount":
                shopBo.setAidPayAmount(shopBoValue.getAidPayAmount() == null ? 0 : shopBoValue.getAidPayAmount());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("aidPayAmount");
                break;
            case "shopOutStockAmount":
                shopBo.setShopOutStockAmount(shopBoValue.getShopOutStockAmount() == null ? 0.0 : shopBoValue.getShopOutStockAmount());
                break;
            case "shopOutStockGoodsNum":
                shopBo.setShopOutStockGoodsNum(shopBoValue.getShopOutStockGoodsNum() == null ? 0 : shopBoValue.getShopOutStockGoodsNum());
                break;
            case "shopOutStockNum":
                shopBo.setShopOutStockNum(shopBoValue.getShopOutStockNum() == null ? 0 : shopBoValue.getShopOutStockNum());
                break;
            case "shopOutStockOrderNum":
                shopBo.setShopOutStockOrderNum(shopBoValue.getShopOutStockOrderNum() == null ? 0 : shopBoValue.getShopOutStockOrderNum());
                break;
            case "csOutStockAmount":
                shopBo.setCsOutStockAmount(shopBoValue.getCsOutStockAmount() == null ? 0.0 : shopBoValue.getCsOutStockAmount());
                break;
            case "csOutStockGoodsNum":
                shopBo.setCsOutStockGoodsNum(shopBoValue.getCsOutStockGoodsNum() == null ? 0 : shopBoValue.getCsOutStockGoodsNum());
                break;
            case "csOutStockNum":
                shopBo.setCsOutStockNum(shopBoValue.getCsOutStockNum() == null ? 0 : shopBoValue.getCsOutStockNum());
                break;
            case "csOutStockOrderNum":
                shopBo.setCsOutStockOrderNum(shopBoValue.getCsOutStockOrderNum() == null ? 0 : shopBoValue.getCsOutStockOrderNum());
                break;
            case "csOutStockAmountPercent":
                shopBo.setCsOutStockAmount(BaseUtils.getNonNull(shopBoValue.getCsOutStockAmount()));
                shopBo.setShopOutStockAmount(BaseUtils.getNonNull(shopBoValue.getShopOutStockAmount()));
                shopBo.setCsOutStockAmountPercent(shopBo.getShopOutStockAmount() > 0 ? shopBo.getCsOutStockAmount() / shopBo.getShopOutStockAmount() : 0.0);
                break;
            case "silenceOutStockAmount":
                shopBo.setSilenceOutStockAmount(shopBoValue.getSilenceOutStockAmount());
                break;
            case "silenceOutStockGoodsNum":
                shopBo.setSilenceOutStockGoodsNum(shopBoValue.getSilenceOutStockGoodsNum());
                break;
            case "silenceOutStockNum":
                shopBo.setSilenceOutStockNum(shopBoValue.getSilenceOutStockNum());
                break;
            case "silenceOutStockOrderNum":
                shopBo.setSilenceOutStockOrderNum(shopBoValue.getSilenceOutStockOrderNum());
                break;
            case "silenceOutStockAmountPercent":
                shopBo.setShopOutStockAmount(BaseUtils.getNonNull(shopBoValue.getShopOutStockAmount()));
                shopBo.setSilenceOutStockAmount(BaseUtils.getNonNull(shopBoValue.getSilenceOutStockAmount()));
                shopBo.setSilenceOutStockAmountPercent(shopBo.getShopOutStockAmount() > 0 ? shopBo.getSilenceOutStockAmount() / shopBo.getShopOutStockAmount() : 0.0);
                break;
            case "csOutStockBuyerNumFinal":
                shopBo.setCsOutStockBuyerNumFinal(shopBoValue.getCsOutStockBuyerNumFinal() == null ? 0 : shopBoValue.getCsOutStockBuyerNumFinal());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("csOutStockBuyerNumFinal");
                break;
            case "shopNeutralEvaluateNum":
                shopBo.setShopNeutralEvaluateNum(shopBoValue.getShopNeutralEvaluateNum() == null ? 0 : shopBoValue.getShopNeutralEvaluateNum());
                break;
            case "shopBadEvaluateNum":
                shopBo.setShopBadEvaluateNum(shopBoValue.getShopBadEvaluateNum() == null ? 0 : shopBoValue.getShopBadEvaluateNum());
                break;
            case "shopNeutralBadEvaluateNumTotal":
                shopBo.setShopNeutralBadEvaluateNumTotal(shopBoValue.getShopNeutralBadEvaluateNumTotal() == null ? 0 : shopBoValue.getShopNeutralBadEvaluateNumTotal());
                break;
            case "csNeutralEvaluateNum":
                shopBo.setCsNeutralEvaluateNum(shopBoValue.getCsNeutralEvaluateNum() == null ? 0 : shopBoValue.getCsNeutralEvaluateNum());
                break;
            case "csBadEvaluateNum":
                shopBo.setCsBadEvaluateNum(shopBoValue.getCsBadEvaluateNum() == null ? 0 : shopBoValue.getCsBadEvaluateNum());
                break;
            case "csNeutralBadEvaluateNumTotal":
                shopBo.setCsNeutralBadEvaluateNumTotal(BaseUtils.getNonNull(shopBoValue.getCsNeutralEvaluateNum()) + BaseUtils.getNonNull(shopBoValue.getCsBadEvaluateNum()));
                break;
            case "silenceNeutralEvaluateNum":
                shopBo.setSilenceNeutralEvaluateNum(shopBoValue.getSilenceNeutralEvaluateNum());
                break;
            case "silenceBadEvaluateNum":
                shopBo.setSilenceBadEvaluateNum(shopBoValue.getSilenceBadEvaluateNum());
                break;
            case "silenceNeutralBadEvaluateNumTotal":
                shopBo.setSilenceNeutralBadEvaluateNumTotal(shopBoValue.getSilenceNeutralEvaluateNum() + shopBoValue.getSilenceBadEvaluateNum());
                break;
            case "inviteEvaluateNum":
                shopBo.setInviteEvaluateNum(shopBoValue.getInviteEvaluateNum() == null ? 0 : shopBoValue.getInviteEvaluateNum());
                break;
            case "evaluateNum":
                shopBo.setEvaluateNum(shopBoValue.getEvaluateNum() == null ? 0 : shopBoValue.getEvaluateNum());
                break;
            case "verySatisfiedNum":
                shopBo.setVerySatisfiedNum(shopBoValue.getVerySatisfiedNum() == null ? 0 : shopBoValue.getVerySatisfiedNum());
                break;
            case "satisfiedNum":
                shopBo.setSatisfiedNum(shopBoValue.getSatisfiedNum() == null ? 0 : shopBoValue.getSatisfiedNum());
                break;
            case "generalNum":
                shopBo.setGeneralNum(shopBoValue.getGeneralNum() == null ? 0 : shopBoValue.getGeneralNum());
                break;
            case "dissatisfiedNum":
                shopBo.setDissatisfiedNum(shopBoValue.getDissatisfiedNum() == null ? 0 : shopBoValue.getDissatisfiedNum());
                break;
            case "veryDissatisfiedNum":
                shopBo.setVeryDissatisfiedNum(shopBoValue.getVeryDissatisfiedNum() == null ? 0 : shopBoValue.getVeryDissatisfiedNum());
                break;
            case "inviteEvaluateRate":
                shopBo.setInviteEvaluateNum(BaseUtils.getNonNull(shopBoValue.getInviteEvaluateNum()));
                shopBo.setReceiveSessionNum(BaseUtils.getNonNull(shopBoValue.getReceiveSessionNum()));
                shopBo.setInviteEvaluateRate(shopBo.getReceiveSessionNum() > 0 ? (double) shopBo.getInviteEvaluateNum() / shopBoValue.getReceiveSessionNum() : 0.0);
                break;
            case "evaluateRate":
                shopBo.setEvaluateNum(BaseUtils.getNonNull(shopBoValue.getEvaluateNum()));
                shopBo.setReceiveSessionNum(BaseUtils.getNonNull(shopBoValue.getReceiveSessionNum()));
                shopBo.setEvaluateRate(shopBo.getReceiveSessionNum() > 0 ? (double) shopBo.getEvaluateNum() / shopBo.getReceiveSessionNum() : 0.0);
                break;
            case "satisfactionRate":
                shopBo.setVerySatisfiedNum(BaseUtils.getNonNull(shopBoValue.getVerySatisfiedNum()));
                shopBo.setSatisfiedNum(BaseUtils.getNonNull(shopBoValue.getSatisfiedNum()));
                shopBo.setEvaluateNum(BaseUtils.getNonNull(shopBoValue.getEvaluateNum()));
                shopBo.setSatisfactionRate(shopBo.getEvaluateNum() > 0 ? (double) (shopBo.getVerySatisfiedNum() + shopBo.getSatisfiedNum()) / shopBo.getEvaluateNum() : 0.0);
                break;
            case "shopRefundNum":
                shopBo.setShopRefundNum(shopBoValue.getShopRefundNum() == null ? 0 : shopBoValue.getShopRefundNum());
                break;
            case "shopRefundBuyerNum":
                shopBo.setShopRefundBuyerNum(shopBoValue.getShopRefundBuyerNum() == null ? 0 : shopBoValue.getShopRefundBuyerNum());
                break;
            case "shopRefundProductNum":
                shopBo.setShopRefundProductNum(shopBoValue.getShopRefundProductNum() == null ? 0 : shopBoValue.getShopRefundProductNum());
                break;
            case "shopRefundAmount":
                shopBo.setShopRefundAmount(shopBoValue.getShopRefundAmount() == null ? 0 : shopBoValue.getShopRefundAmount());
                break;
            case "csRefundNum":
                shopBo.setCsRefundNum(shopBoValue.getCsRefundNum() == null ? 0 : shopBoValue.getCsRefundNum());
                break;
            case "csRefundBuyerNum":
                shopBo.setCsRefundBuyerNum(shopBoValue.getCsRefundBuyerNum() == null ? 0 : shopBoValue.getCsRefundBuyerNum());
                break;
            case "csRefundProductNum":
                shopBo.setCsRefundProductNum(shopBoValue.getCsRefundProductNum() == null ? 0 : shopBoValue.getCsRefundProductNum());
                break;
            case "csRefundAmount":
                shopBo.setCsRefundAmount(shopBoValue.getCsRefundAmount() == null ? 0.0 : shopBoValue.getCsRefundAmount());
                break;
            case "csRefundPercent":
                shopBo.setCsRefundProductNum(BaseUtils.getNonNull(shopBoValue.getCsRefundProductNum()));
                shopBo.setCsTransactionsNum(BaseUtils.getNonNull(shopBoValue.getCsTransactionsNum()));
                shopBo.setCsRefundPercent(shopBo.getCsTransactionsNum() > 0 ? (double) shopBo.getCsRefundProductNum() / shopBo.getCsTransactionsNum() : 0.0);
                break;
            case "silenceRefundNum":
                shopBo.setSilenceRefundNum(shopBoValue.getSilenceRefundNum());
                break;
            case "silenceRefundBuyerNum":
                shopBo.setSilenceRefundBuyerNum(shopBoValue.getSilenceRefundBuyerNum());
                break;
            case "silenceRefundProductNum":
                shopBo.setSilenceRefundProductNum(shopBoValue.getSilenceRefundProductNum());
                break;
            case "silenceRefundAmount":
                shopBo.setSilenceRefundAmount(shopBoValue.getSilenceRefundAmount());
                break;
            case "enquiryLossNum":
                shopBo.setEnquiryLossNum(shopBoValue.getEnquiryLossNum() == null ? 0 : shopBoValue.getEnquiryLossNum());
                if (isDealy(key, dateType, dates, enquiryValidDurationTime))
                    notFinalDataField.add("enquiryLossNum");
                break;
            case "csPracticalOrderedUnpaidPeople":
                shopBo.setCsPracticalOrderedUnpaidPeople(shopBoValue.getCsPracticalOrderedUnpaidPeople() == null ? 0 : shopBoValue.getCsPracticalOrderedUnpaidPeople());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("csPracticalOrderedUnpaidPeople");
                break;
            case "csPracticalOrderedUnpaidItemNum":
                shopBo.setCsPracticalOrderedUnpaidItemNum(shopBoValue.getCsPracticalOrderedUnpaidItemNum() == null ? 0 : shopBoValue.getCsPracticalOrderedUnpaidItemNum());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("csPracticalOrderedUnpaidItemNum");
                break;
            case "csPracticalOrderedUnpaidAmount":
                shopBo.setCsPracticalOrderedUnpaidAmount(shopBoValue.getCsPracticalOrderedUnpaidAmount() == null ? 0 : shopBoValue.getCsPracticalOrderedUnpaidAmount());
                if (isDealy(key, dateType, dates, 2)) notFinalDataField.add("csPracticalOrderedUnpaidAmount");
                break;
            case "csPracticalOrderedUnpaidOrderedNum":
                shopBo.setCsPracticalOrderedUnpaidOrderedNum(shopBoValue.getCsPracticalOrderedUnpaidOrderedNum() == null ? 0 : shopBoValue.getCsPracticalOrderedUnpaidOrderedNum());
                if (isDealy(key, dateType, dates, 2))
                    notFinalDataField.add("csPracticalOrderedUnpaidOrderedNum");
                break;
            case "csPracticalOrderedUnoutStockPeople":
                shopBo.setCsPracticalOrderedUnoutStockPeople(shopBoValue.getCsPracticalOrderedUnoutStockPeople() == null ? 0 : shopBoValue.getCsPracticalOrderedUnoutStockPeople());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("csPracticalOrderedUnoutStockPeople");
                break;
            case "csPracticalOrderedUnoutStockItemNum":
                shopBo.setCsPracticalOrderedUnoutStockItemNum(shopBoValue.getCsPracticalOrderedUnoutStockItemNum() == null ? 0 : shopBoValue.getCsPracticalOrderedUnoutStockItemNum());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("csPracticalOrderedUnoutStockItemNum");
                break;
            case "csPracticalOrderedUnoutStockAmount":
                shopBo.setCsPracticalOrderedUnoutStockAmount(shopBoValue.getCsPracticalOrderedUnoutStockAmount() == null ? 0 : shopBoValue.getCsPracticalOrderedUnoutStockAmount());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("csPracticalOrderedUnoutStockAmount");
                break;
            case "csPracticalOrderedUnoutStockOrderedNum":
                shopBo.setCsPracticalOrderedUnoutStockOrderedNum(shopBoValue.getCsPracticalOrderedUnoutStockOrderedNum() == null ? 0 : shopBoValue.getCsPracticalOrderedUnoutStockOrderedNum());
                if (isDealy(key, dateType, dates, outStockValidDurationTime))
                    notFinalDataField.add("csPracticalOrderedUnoutStockOrderedNum");
                break;
            case "shopPostFee":
                shopBo.setShopPostFee(shopBoValue.getShopPostFee() == null ? 0.0 : shopBoValue.getShopPostFee());
                break;
            case "silencePostFee":
                shopBo.setSilencePostFee(BaseUtils.getNonNull(shopBoValue.getShopPostFee()) - BaseUtils.getNonNull(shopBoValue.getCsPostFee()));
                break;
            case "csPostFee":
                shopBo.setCsPostFee(shopBoValue.getCsPostFee() == null ? 0.0 : shopBoValue.getCsPostFee());
                break;
            case "productEvaluationDSR":
                shopBo.setProductEvaluationDSR(shopBoValue.getProductEvaluationDSR() == null ? 0.0 : shopBoValue.getProductEvaluationDSR());
                break;
            case "serviceAttitudeDSR":
                shopBo.setServiceAttitudeDSR(shopBoValue.getServiceAttitudeDSR() == null ? 0.0 : shopBoValue.getServiceAttitudeDSR());
                break;
            case "logisticsSpeedDSR":
                shopBo.setLogisticsSpeedDSR(shopBoValue.getLogisticsSpeedDSR() == null ? 0.0 : shopBoValue.getLogisticsSpeedDSR());
                break;
            case "chatRoundNum":
                shopBo.setChatRoundNum(shopBoValue.getChatRoundNum() == null ? 0 : shopBoValue.getChatRoundNum());
                break;
            case "afterSaleScore":
                shopBo.setAfterSaleScore(shopBoValue.getAfterSaleScore() == null ? 0 : shopBoValue.getAfterSaleScore());
                break;
            case "disputeScore":
                shopBo.setDisputeScore(shopBoValue.getDisputeScore() == null ? 0 : shopBoValue.getDisputeScore());
                break;
        }
    }

    /* 字段的特殊计算*/
    private void switchPropertyForAVG(String property, ShopPerformanceAvg avgBo, ShopPerformanceBO countBo) {
        switch (property) {
            case "csSaleAmountPercent":
                if (countBo.getCsSaleAmountPercent() > 0)
                    avgBo.setCsSaleAmountPercent(countBo.getCsSaleAmount() != null && countBo.getCsSaleAmount() > 0 && countBo.getShopSaleAmount() != null && countBo.getShopSaleAmount() > 0 ? countBo.getCsSaleAmount() / countBo.getShopSaleAmount() : 0);
                break;
            case "silenceSaleAmountPercent":
                if (countBo.getSilenceSaleAmountPercent() > 0)
                    avgBo.setSilenceSaleAmountPercent(countBo.getShopSaleAmount() != null && countBo.getSilenceSaleAmount() != null && countBo.getShopSaleAmount() > 0 ? countBo.getSilenceSaleAmount() / countBo.getShopSaleAmount() : 0.0);
                break;
            case "consultPercent":
                if (countBo.getConsultPercent() > 0)
                    avgBo.setConsultPercent(countBo.getConsultNum() != null && countBo.getConsultNum() > 0 && countBo.getUvNum() != null && countBo.getUvNum() > 0 ? (double) countBo.getConsultNum() / countBo.getUvNum() : 0.0);
                break;
            case "shopSaleGuestAvgAmount":
                if (countBo.getShopSaleGuestAvgAmount() > 0)
                    avgBo.setShopSaleGuestAvgAmount(countBo.getShopSaleAmount() != null && countBo.getShopSaleBuyerNum() != null && countBo.getShopSaleBuyerNum() > 0 ? countBo.getShopSaleAmount() / countBo.getShopSaleBuyerNum() : 0.0);
                break;
            case "shopSaleGuestAvgGoods":
                if (countBo.getShopSaleGuestAvgGoods() > 0)
                    avgBo.setShopSaleGuestAvgGoods(countBo.getShopSaleGoodsNum() != null && countBo.getShopSaleBuyerNum() != null && countBo.getShopSaleBuyerNum() > 0 ? (double) countBo.getShopSaleGoodsNum() / countBo.getShopSaleBuyerNum() : 0.0);
                break;
            case "shopSaleGoodsAvgAmount":
                if (countBo.getShopSaleGoodsAvgAmount() > 0)
                    avgBo.setShopSaleGoodsAvgAmount(countBo.getShopSaleAmount() != null && countBo.getShopSaleGoodsNum() != null && countBo.getShopSaleGoodsNum() > 0 ? countBo.getShopSaleAmount() / countBo.getShopSaleGoodsNum() : 0.0);
                break;
            case "shopOutStockGuestAvgAmount":
                if (countBo.getShopOutStockGuestAvgAmount() > 0)
                    avgBo.setShopOutStockGuestAvgAmount(countBo.getShopOutStockAmount() != null && countBo.getShopOutStockNum() != null && countBo.getShopOutStockNum() > 0 ? countBo.getShopOutStockAmount() / countBo.getShopOutStockNum() : 0.0);
                break;
            case "shopOutStockGuestItemNum":
                if (countBo.getShopOutStockGuestItemNum() > 0)
                    avgBo.setShopOutStockGuestItemNum(countBo.getShopOutStockGoodsNum() != null && countBo.getShopOutStockNum() != null && countBo.getShopOutStockNum() > 0 ? (double) countBo.getShopOutStockGoodsNum() / countBo.getShopOutStockNum() : 0.0);
                break;
            case "shopOutStockItemAvgAmount":
                if (countBo.getShopOutStockItemAvgAmount() > 0)
                    avgBo.setShopOutStockItemAvgAmount(countBo.getShopOutStockAmount() != null && countBo.getShopOutStockGoodsNum() != null && countBo.getShopOutStockGoodsNum() > 0 ? countBo.getShopOutStockAmount() / countBo.getShopOutStockGoodsNum() : 0.0);
                break;
            case "csSaleGuestAvgAmount":
                if (countBo.getCsSaleGuestAvgAmount() > 0)
                    avgBo.setCsSaleGuestAvgAmount(avgBo.getCsSaleAmount() != null && avgBo.getCsSaleBuyerNum() != null && avgBo.getCsSaleBuyerNum() > 0 ? avgBo.getCsSaleAmount() / avgBo.getCsSaleBuyerNum() : 0.0);
                break;
            case "csSaleGuestAvgGoods":
                if (countBo.getCsSaleGuestAvgGoods() > 0)
                    avgBo.setCsSaleGuestAvgGoods(countBo.getCsSaleGoodsNum() != null && countBo.getCsSaleBuyerNum() != null && countBo.getCsSaleBuyerNum() > 0 ? (double) countBo.getCsSaleGoodsNum() / countBo.getCsSaleBuyerNum() : 0.0);
                break;
            case "csSaleGoodsAvgAmount":
                if (countBo.getCsSaleGoodsAvgAmount() > 0)
                    avgBo.setCsSaleGoodsAvgAmount(countBo.getCsSaleAmount() != null && countBo.getCsSaleGoodsNum() != null && countBo.getCsSaleGoodsNum() > 0 ? countBo.getCsSaleAmount() / countBo.getCsSaleGoodsNum() : 0.0);
                break;
            case "csOutStockGuestAvgAmount":
                if (countBo.getCsOutStockGuestAvgAmount() > 0)
                    avgBo.setCsOutStockGuestAvgAmount(countBo.getCsOutStockAmount() != null && countBo.getCsOutStockNum() != null && countBo.getCsOutStockNum() > 0 ? countBo.getCsOutStockAmount() / countBo.getCsOutStockNum() : 0.0);
                break;
            case "csOutStockGuestItemNum":
                if (countBo.getCsOutStockGuestItemNum() > 0)
                    avgBo.setCsOutStockGuestItemNum(countBo.getCsOutStockGoodsNum() != null && countBo.getCsOutStockNum() != null && countBo.getCsOutStockNum() > 0 ? (double) countBo.getCsOutStockGoodsNum() / countBo.getCsOutStockNum() : 0.0);
                break;
            case "csOutStockItemAvgAmount":
                if (countBo.getCsOutStockItemAvgAmount() > 0)
                    avgBo.setCsOutStockItemAvgAmount(countBo.getCsOutStockAmount() != null && countBo.getCsOutStockGoodsNum() != null && countBo.getCsOutStockGoodsNum() > 0 ? countBo.getCsOutStockAmount() / countBo.getCsOutStockGoodsNum() : 0.0);
                break;
            case "silenceSaleGuestAvgAmount":
                if (countBo.getSilenceSaleGuestAvgAmount() > 0)
                    avgBo.setSilenceSaleGuestAvgAmount(countBo.getSilenceSaleBuyerNum() > 0 ? countBo.getSilenceSaleAmount() / countBo.getSilenceSaleBuyerNum() : 0.0);
                break;
            case "silenceSaleGuestAvgGoods":
                if (countBo.getSilenceSaleGuestAvgGoods() > 0)
                    avgBo.setSilenceSaleGuestAvgGoods(countBo.getSilenceSaleBuyerNum() > 0 ? (double) countBo.getSilenceSaleGoodsNum() / countBo.getSilenceSaleBuyerNum() : 0.0);
                break;
            case "silenceSaleGoodsAvgAmount":
                if (countBo.getSilenceSaleGoodsAvgAmount() > 0)
                    avgBo.setSilenceSaleGoodsAvgAmount(countBo.getSilenceSaleGoodsNum() > 0 ? countBo.getSilenceSaleAmount() / countBo.getSilenceSaleGoodsNum() : 0.0);
                break;
            case "silenceOutStockGuestAvgAmount":
                if (countBo.getSilenceOutStockGuestAvgAmount() > 0)
                    avgBo.setSilenceOutStockGuestAvgAmount(countBo.getSilenceOutStockNum() > 0 ? countBo.getSilenceOutStockAmount() / countBo.getSilenceOutStockNum() : 0.0);
                break;
            case "silenceOutStockGuestItemNum":
                if (countBo.getSilenceOutStockGuestItemNum() > 0)
                    avgBo.setSilenceOutStockGuestItemNum(countBo.getSilenceOutStockNum() > 0 ? (double) countBo.getSilenceOutStockGoodsNum() / countBo.getSilenceOutStockNum() : 0.0);
                break;
            case "shopDealPercent":
                if (countBo.getShopDealPercent() > 0)
                    avgBo.setShopDealPercent(countBo.getShopSaleBuyerNum() != null && countBo.getUvNum() != null && countBo.getUvNum() > 0 ? (double) countBo.getShopSaleBuyerNum() / countBo.getUvNum() : 0.0);
                break;
            case "shopOutStockPercent":
                if (countBo.getShopOutStockPercent() > 0)
                    avgBo.setShopOutStockPercent(countBo.getShopOutStockNum() != null && countBo.getUvNum() != null && countBo.getUvNum() > 0 ? (double) countBo.getShopOutStockNum() / countBo.getUvNum() : 0.0);
                break;
            case "silenceDealPercent":
                if (countBo.getSilenceDealPercent() > 0)
                    avgBo.setSilenceDealPercent(countBo.getUvNum() != null && countBo.getUvNum() > 0 ? (double) countBo.getSilenceSaleBuyerNum() / countBo.getUvNum() : 0.0);
                break;
            case "queryToTomorrow":
                if (countBo.getQueryToTomorrow() > 0)
                    avgBo.setQueryToTomorrow(countBo.getPaidNumTodayNext() != null && countBo.getEnquiryNum() != null && countBo.getEnquiryNum() > 0 ? (double) countBo.getPaidNumTodayNext() / countBo.getEnquiryNum() : 0.0);
                break;
            case "queryToFinalPaid":
                if (countBo.getQueryToFinalPaid() > 0)
                    avgBo.setQueryToFinalPaid(countBo.getPaidNumFinal() != null && countBo.getEnquiryNum() != null && countBo.getEnquiryNum() > 0 ? (double) countBo.getPaidNumFinal() / countBo.getEnquiryNum() : 0.0);
                break;
            case "queryToOrderedToday":
                if (countBo.getQueryToOrderedToday() > 0)
                    avgBo.setQueryToOrderedToday(countBo.getOrderedNumToday() != null && countBo.getEnquiryNum() != null && countBo.getEnquiryNum() > 0 ? (double) countBo.getOrderedNumToday() / countBo.getEnquiryNum() : 0.0);
                break;
            case "queryToFinalOrdered":
                if (countBo.getQueryToFinalOrdered() > 0)
                    avgBo.setQueryToFinalOrdered(countBo.getOrderedNumFinal() != null && countBo.getEnquiryNum() != null && countBo.getEnquiryNum() > 0 ? (double) countBo.getOrderedNumFinal() / countBo.getEnquiryNum() : 0.0);
                break;
            case "orderedToPaid":
                if (countBo.getOrderedToPaid() > 0)
                    avgBo.setOrderedToPaid(countBo.getOrderedPaidNumToday() != null && countBo.getToOrderedNum() != null && countBo.getToOrderedNum() > 0 ? (double) countBo.getOrderedPaidNumToday() / countBo.getToOrderedNum() : 0.0);
                break;
            case "orderedToPaidFinal":
                if (countBo.getOrderedToPaidFinal() > 0)
                    avgBo.setOrderedToPaidFinal(countBo.getOrderedPaidNumFinal() != null && countBo.getToOrderedNum() != null && countBo.getToOrderedNum() > 0 ? (double) countBo.getOrderedPaidNumFinal() / countBo.getToOrderedNum() : 0.0);
                break;
            case "queryToOutStock":
                if (countBo.getQueryToOutStock() > 0)
                    avgBo.setQueryToOutStock(countBo.getCsOutStockBuyerNumFinal() != null && countBo.getEnquiryNum() != null && countBo.getEnquiryNum() > 0 ? (double) countBo.getCsOutStockBuyerNumFinal() / countBo.getEnquiryNum() : 0.0);
                break;
            case "orderItemAvgAmount":
                if (countBo.getOrderItemAvgAmount() > 0)
                    avgBo.setOrderItemAvgAmount(countBo.getToOrderedAmount() != null && countBo.getToOrderedGoodsNum() != null && countBo.getToOrderedGoodsNum() > 0 ? (double) countBo.getToOrderedAmount() / countBo.getToOrderedGoodsNum() : 0.0);
                break;
            case "orderedGuestAvgPrice":
                if (countBo.getOrderedGuestAvgPrice() > 0)
                    avgBo.setOrderedGuestAvgPrice(countBo.getToOrderedAmount() != null && countBo.getToOrderedNum() != null && countBo.getToOrderedNum() > 0 ? (double) countBo.getToOrderedAmount() / countBo.getToOrderedNum() : 0.0);
                break;
            case "orderedGuestAvgAmount":
                if (countBo.getOrderedGuestAvgAmount() > 0)
                    avgBo.setOrderedGuestAvgAmount(countBo.getToOrderedGoodsNum() != null && countBo.getToOrderedNum() != null && countBo.getToOrderedNum() > 0 ? (double) countBo.getToOrderedGoodsNum() / countBo.getToOrderedNum() : 0.0);
                break;
            case "avgReplyMsg":
                if (countBo.getAvgReplyMsg() > 0)
                    avgBo.setAvgReplyMsg(countBo.getReceiveSessionNum() > 0 ? (double) countBo.getCsChatNum() / countBo.getReceiveSessionNum() : 0.0);
                break;
            case "answerRatio":
                if (countBo.getAnswerRatio() > 0)
                    avgBo.setAnswerRatio(countBo.getCsChatNum() != null && countBo.getCustChatNum() != null && countBo.getCustChatNum() > 0 ? (double) countBo.getCsChatNum() / countBo.getCustChatNum() : 0.0);
                break;
            case "responseRate":
                if (countBo.getResponseRate() > 0)
                    avgBo.setResponseRate(countBo.getReceiveSessionNum() != null && countBo.getReceiveSessionNum() > 0 && countBo.getNonReplySessionNum() != null ? (countBo.getReceiveSessionNum() - countBo.getNonReplySessionNum()) * 1.0 / countBo.getReceiveSessionNum() : 0.0);
                break;
            case "quickResponseRate":
                if (countBo.getQuickResponseRate() > 0) {
                    int calSessionNum = BaseUtils.getNonNull(countBo.getSessionNum()) - BaseUtils.getNonNull(countBo.getNonReplySessionNum()) - BaseUtils.getNonNull(countBo.getLeaveMsgReceiveSessionNum());
                    avgBo.setQuickResponseRate(calSessionNum > 0 ? (double) countBo.getAvgRespInQuickTime() / calSessionNum : 0.0);
                }
                break;
            case "leaveMsgReplyRate":
                if (countBo.getLeaveMsgReplyRate() > 0)
                    avgBo.setLeaveMsgReplyRate(countBo.getLeaveMsgReceiveSessionNum() != null && countBo.getAdvisoryMessageNum() != null && countBo.getAdvisoryMessageNum() > 0 ? (double) countBo.getLeaveMsgReceiveSessionNum() / countBo.getAdvisoryMessageNum() : 0.0);
                break;
            case "leaveMsgResponseRate":
                if (countBo.getLeaveMsgResponseRate() > 0)
                    avgBo.setLeaveMsgResponseRate(countBo.getLeaveMsgReceiveSessionNum() != null && countBo.getLeaveMsgSessionNum() != null && countBo.getLeaveMsgSessionNum() > 0 ? (double) countBo.getLeaveMsgReceiveSessionNum() / countBo.getLeaveMsgSessionNum() : 0.0);
                break;
            case "csOutStockAmountPercent":
                if (countBo.getCsOutStockAmountPercent() > 0)
                    avgBo.setCsOutStockAmountPercent(countBo.getCsOutStockAmount() != null && countBo.getShopOutStockAmount() != null && countBo.getShopOutStockAmount() > 0 ? countBo.getCsOutStockAmount() / countBo.getShopOutStockAmount() : 0.0);
                break;
            case "silenceOutStockAmountPercent":
                if (countBo.getSilenceOutStockAmountPercent() > 0)
                    avgBo.setSilenceOutStockAmountPercent(countBo.getShopOutStockAmount() != null && countBo.getShopOutStockAmount() > 0 ? countBo.getSilenceOutStockAmount() / countBo.getShopOutStockAmount() : 0.0);
                break;
            case "inviteEvaluateRate":
                if (countBo.getInviteEvaluateRate() > 0)
                    avgBo.setInviteEvaluateRate(countBo.getInviteEvaluateNum() != null && countBo.getReceiveSessionNum() != null && countBo.getReceiveSessionNum() > 0 ? (double) countBo.getInviteEvaluateNum() / countBo.getReceiveSessionNum() : 0.0);
                break;
            case "evaluateRate":
                if (countBo.getEvaluateRate() > 0)
                    avgBo.setEvaluateRate(countBo.getEvaluateNum() != null && countBo.getReceiveSessionNum() != null && countBo.getReceiveSessionNum() > 0 ? (double) countBo.getEvaluateNum() / countBo.getReceiveSessionNum() : 0.0);
                break;
            case "satisfactionRate":
                if (countBo.getSatisfactionRate() > 0) {
                    int satisfiedNum = (countBo.getVerySatisfiedNum() != null ? countBo.getVerySatisfiedNum() : 0) + (countBo.getSatisfiedNum() != null ? countBo.getSatisfiedNum() : 0);
                    avgBo.setSatisfactionRate(countBo.getEvaluateNum() != null && countBo.getEvaluateNum() > 0 ? (double) satisfiedNum / countBo.getEvaluateNum() : 0.0);
                }
                break;
            case "csRefundPercent":
                if (countBo.getCsRefundPercent() > 0)
                    avgBo.setCsRefundPercent(countBo.getCsRefundProductNum() != null && countBo.getCsTransactionsNum() != null && countBo.getCsTransactionsNum() > 0 ? (double) countBo.getCsRefundProductNum() / countBo.getCsTransactionsNum() : 0.0);
                break;
            case "avgRespTimeFirst":
                if (countBo.getAvgRespTimeFirst() > 0) {
                    int calSessionNum = BaseUtils.getNonNull(countBo.getSessionNum()) - BaseUtils.getNonNull(countBo.getNonReplySessionNum()) - BaseUtils.getNonNull(countBo.getLeaveMsgReceiveSessionNum());
                    avgBo.setAvgRespTimeFirst(calSessionNum > 0 ? countBo.getRespTimeFirstCount() / calSessionNum : 0.0);
                } else {
                    avgBo.setAvgRespTimeFirst(0.0);
                }
                break;
            case "avgRespTime":
                if (countBo.getAvgRespTime() > 0)
                    avgBo.setAvgRespTime(BaseUtils.getNonNull(countBo.getChatRoundNum()) != null && countBo.getRespTimeCount() != null && countBo.getChatRoundNum() > 0 ? (double) countBo.getRespTimeCount() / BaseUtils.getNonNull(countBo.getChatRoundNum()) : 0.0);
                break;
            case "avgSessionDurationTime":
                if (countBo.getAvgSessionDurationTime() > 0) {
                    avgBo.setAvgSessionDurationTime(BaseUtils.getNonNull(countBo.getSessionNum()) > 0 ? countBo.getSessionDurationTimeCount() / BaseUtils.getNonNull(countBo.getSessionNum()) : 0.0);
                }
                break;
        }
    }

    /**
     * 获取咚咚接待量=魔方咨询量-魔方转出量—魔方留言分配量—魔方未回复量
     * @param consultSessionNum
     * @param forwardOutSessionNum
     * @param leaveMsgSessionNum
     * @param noReplyNum
     * @return
     */
    private int getDdReceiveNum(Integer consultSessionNum, Integer forwardOutSessionNum, Integer leaveMsgSessionNum, Integer noReplyNum) {
        if(consultSessionNum==null){
            return 0;
        }
        int consultSessionNumInt=0;
        int forwardOutSessionNumInt=0;
        int leaveMsgSessionNumInt=0;
        int noReplyNumInt=0;
        if(forwardOutSessionNum!=null){
            forwardOutSessionNumInt=forwardOutSessionNum;
        }
        if(leaveMsgSessionNum!=null){
            leaveMsgSessionNumInt=leaveMsgSessionNum;
        }
        if(noReplyNum!=null){
            noReplyNumInt=noReplyNum;
        }
        return (consultSessionNum - forwardOutSessionNumInt - leaveMsgSessionNumInt - noReplyNumInt) > 0 ? (consultSessionNumInt - forwardOutSessionNumInt - leaveMsgSessionNumInt - noReplyNumInt) : 0;
    }

    /**
     * 获取咚咚咨询量=方咨询量-魔方转出量—魔方留言分配量
     * @param consultSessionNum
     * @param forwardOutSessionNum
     * @param leaveMsgSessionNum
     * @return
     */
    private int getDdConsultNum(Integer consultSessionNum, Integer forwardOutSessionNum, Integer leaveMsgSessionNum) {
        return (consultSessionNum - forwardOutSessionNum - leaveMsgSessionNum) > 0 ? (consultSessionNum - forwardOutSessionNum - leaveMsgSessionNum) : 0;
    }

    /**
     * 是否过滤
     */
    private boolean isFilters(List<PerformanceParam.Filter> filters, int id) {
        for (PerformanceParam.Filter filter : filters) {
            if (filter.isIsFilter() && filter.getId() == id) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判定是否延迟展示
     *
     * @param key      日期 2019-03-27 | 月份 2019-03
     * @param dateType 1 日期 | 2 月份
     * @param day      延迟天数
     */
    private boolean isDealy(String key, Integer dateType, List<String> dates, int day) throws ParseException {
        boolean isDealy = false;
        //月份纬度 数据是否延迟显示
        if (CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_MONTH.getType().equals(dateType)) {
            Date date = DateUtil.getStartDateOfMonth(key);
            Date dealy = DateUtil.getStartDateOfMonth(DateUtil.getDateByPeriod(new Date(), -day));
            if (date.getTime() >= dealy.getTime()) {
                isDealy = true;
            }
        } else {
            //若为店铺纬度 判断最后一天是否为延迟显示 否则日期纬度
            if (!CustomTypeEnum.SHOP_PERFORMANCE_DATE_TYPE_DAY.getType().equals(dateType))
                key = dates.get(dates.size() - 1);
            Date date = DateUtil.getStartDateFromDateStr(key);
            Date dealy = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(new Date(), -day));
            if (date.getTime() > dealy.getTime()) {
                isDealy = true;
            }
        }
        return isDealy;
    }

}
