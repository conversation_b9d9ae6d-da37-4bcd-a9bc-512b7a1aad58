package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.JobPullApiRecordBussiness;
import com.pes.jd.dao.sub.JobPullApiRecordDao;
import com.pes.jd.model.DO.JobPullApiRecordDO;
import com.pes.jd.model.VO.JobPullApiRecordVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Author: aiJun
 * @Date: 2019-08-12 15:13
 * @Version 1.0
 */
@Service
public class JobPullApiRecordBussinessImpl implements JobPullApiRecordBussiness {
    @Autowired
    private JobPullApiRecordDao jobPullApiRecordDao;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return jobPullApiRecordDao.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.insert(record);
    }

    @Override
    public int insertSelective(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.insertSelective(record);
    }

    @Override
    public JobPullApiRecordDO selectByPrimaryKey(Long id) {
        return jobPullApiRecordDao.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(JobPullApiRecordDO record) {
        return jobPullApiRecordDao.updateByPrimaryKey(record);
    }

    @Override
    @Transactional
    public List<JobPullApiRecordVO> searchJobPullApiRecordRecord(Long shopId, String schemaId, List<Date> dates, Integer type){
        List<JobPullApiRecordVO> jobRecordVOLst = new ArrayList<>();
        for (Date date : dates) {
            List<JobPullApiRecordVO> tempLst = jobPullApiRecordDao.searchJobPullApiRecordRecord(shopId, schemaId, date, type);
            if(CollectionUtils.isNotEmpty(tempLst)){
                jobRecordVOLst.addAll(tempLst);
            }
        }

        return CollectionUtils.isNotEmpty(jobRecordVOLst) ? jobRecordVOLst : Collections.EMPTY_LIST;
    }

    @Override
    @Transactional
    public List<JobPullApiRecordVO> searchJobPullApiRecordRecordBySchemaId(String schemaId, List<Date> dates, Integer type){
        List<JobPullApiRecordVO> jobRecordVOLst = new ArrayList<>();
        for (Date date : dates) {
            List<JobPullApiRecordVO> tempLst = jobPullApiRecordDao.searchJobPullApiRecordRecordBySchemaId(schemaId, date, type);
            if(CollectionUtils.isNotEmpty(tempLst)){
                jobRecordVOLst.addAll(tempLst);
            }
        }

        return CollectionUtils.isNotEmpty(jobRecordVOLst) ? jobRecordVOLst : Collections.EMPTY_LIST;
    }
}
