package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.LostRecordNoteBusiness;
import com.pes.jd.constants.SubTable;
import com.pes.jd.dao.sub.LostRecordNoteDao;
import com.pes.jd.model.DTO.LostRecordNoteDTO;
import com.pes.jd.util.AppContext;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/19 10:34 PM
 * @since 1.0.0
 */
@Service
public class LostRecordNoteBusinessImpl implements LostRecordNoteBusiness {

    @Autowired
    private LostRecordNoteDao lostRecordNoteDao;

    @Override
    public List<LostRecordNoteDTO> selectByOrderId(Set<String> orderIds,String shopId) {
        return lostRecordNoteDao.selectByOrderId(
                CommonUtils.getTableName(AppContext.currentContext().getSchema(), SubTable.PES_LOST_RECORD_NOTE.getName(),shopId),
                orderIds
        );
    }

    @Override
    public List<LostRecordNoteDTO> selectByBuyerNickDateShop(String buyerNick, String shopId, Date startDate, Date endDate) {
        return lostRecordNoteDao.selectByBuyerNickDateShop(
                CommonUtils.getTableName(AppContext.currentContext().getSchema(), SubTable.PES_LOST_RECORD_NOTE.getName(),shopId),
                buyerNick,shopId,startDate,endDate);
    }


}
