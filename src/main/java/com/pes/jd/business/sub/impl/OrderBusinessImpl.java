package com.pes.jd.business.sub.impl;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.*;
import com.pes.jd.business.sub.OrderBusiness;
import com.pes.jd.business.sub.ShopGoodSkuBusiness;
import com.pes.jd.constants.ItemConstants;
import com.pes.jd.dao.sub.OrderDao;
import com.pes.jd.data.api.DongDongOperator;
import com.pes.jd.data.convert.OrderDataConverter;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.CustomerOrderStatusEnum;
import com.pes.jd.model.Enum.InvoiceInfoEnum;
import com.pes.jd.model.Enum.OrderStatusEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopBaseDataParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.TO.OrderResultTO;
import com.pes.jd.model.VO.GoodsSkuInfoVO;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JdApiEncryptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/19 9:45 PM
 * @since 1.0.0
 */
@Service
@Slf4j
public class OrderBusinessImpl implements OrderBusiness {
    private static Logger logger = LoggerFactory.getLogger(OrderBusinessImpl.class);
    @Resource
    private OrderDao orderDao;
    @Resource
    private DongDongOperator dongDongOperator;
    @Resource
    private OrderDataConverter orderDataConverter;
    @Resource
    private ShopGoodSkuBusiness shopGoodSkuBusiness;

    @Override
    public List<OrderDTO> selectOrderInfoByOrderIdsAndStatus(Set<String> orderIds, String status, String shopId) {
        // TODO Auto-generated method stub
//		 return orderDao.selectOrderInfoByOrderIdsAndStatus(
//               CommonUtils.getTableName(AppContext.currentContext().getSchema(), SubTable.PES_ORDER.getName(),shopId),
//               orderIds,status
        return null;
    }

    /**
     * 完善订单状态 - 即 若在线付款订单超过1天 或 公司转账超过10天 状态变更为取消
     * <p>
     * 注意：订单列表中 下单时间、付款类型、订单类型、订单状态 字段不可为空
     */
    @Override
    public void orderStateNotPayToCancel(List<OrderDTO> orderLst) {
        if (orderLst == null || orderLst.size() == 0) return;
        Date now = new Date();
        Date yesterday = DateUtil.getStartTimeOfDate(DateUtils.getDateByPeriod(now, -1));//昨天0点
        Date sevendays = DateUtils.getDateByPeriod(now, -10);//10天前此刻
        for (OrderDTO order : orderLst) {
            if (order == null || order.getCreated() == null || order.getPayType() == null ||
                    order.getOrderType() == null || order.getOrderType() == 1 || // 订单类型 为空 或 预售订单
                    !StringUtils.equals(order.getStatus(), OrderStatusEnum.NO_PAY.getStatus())) continue;

            if ((4 == order.getPayType() && order.getCreated().getTime() < yesterday.getTime())
                    || (5 == order.getPayType() && order.getCreated().getTime() < sevendays.getTime())) {
                order.setStatus(OrderStatusEnum.TRADE_CANCELED.getStatus());
            }
        }
    }

    @Override
    public Map<String, Object> selectOrderDeatilByShopIdAndBuyerNick(ShopBaseDataParam shopBaseDataParam, String buyerNick, Date date, Integer colType, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws Exception {
        Map<String, Object> resMap = new HashMap<String, Object>();
        if (null == shopBaseDataParam || null == colType) {
            log.error("参数为空!!");
            resMap.put("orderList", new ArrayList<OrderDTO>());
            return resMap;
        }

        List<OrderDetailsDTO> orderDTOS = new ArrayList<>();
        List<String> orderIds = new ArrayList<>();
        String sessionKey = shopBaseDataParam.getSessionKey();
        String[] decoderIdAndKey = JdApiEncryptionUtils.getDecoderIdAndKey(sessionKey);
        String id = decoderIdAndKey[0];
        String key = decoderIdAndKey[1];
        Long venderId = shopBaseDataParam.getVenderId();
        Date date2 = DateUtils.getDateByPeriod(date, -300);
        String sTime = DateUtils.formatYMdHms(date);
        String eTime = DateUtils.formatYMdHms(DateUtils.getEndTimeOfDate(date2));
        String jsonParam = "[\"" + buyerNick + "\",\"" + venderId + "\"," + "\"" + sortPageQuery.getCurrentPage() +
                "\",\"" + sortPageQuery.getSize() + "\",\"" + eTime + "\",\"" + sTime + "\"]";
        //获取订单列表
        long s1 = System.currentTimeMillis();
        String json = dongDongOperator.getDongSmartSummary("queryOrderIds", jsonParam, shopBaseDataParam.getShopId());
        long e1 = System.currentTimeMillis();
        log.info("f2====OrderBussinessImpl=getOrderList===={}ms", (e1 - s1));
        String strip = StringUtils.strip(json, "[]");
        String strip2 = StringUtils.strip(strip, "\"\"");
        if (StringUtils.isNotBlank(strip2)) {
            String[] ids = strip.split(",");
            for (String id2 : ids) {
                orderIds.add(id2);
            }
        }

        if (orderIds.size() == 0) {
            resMap.put("orderList", new ArrayList<OrderDTO>());
            return resMap;
        }

        for (String id3 : orderIds) {
            Integer orderState = null;
            String orderPin = "";
            double jdPrice = 0.0;
            StringBuilder skuIds = new StringBuilder();
            //查询订单详细信息
            OrderDetailsDTO orderDetailsDTO = new OrderDetailsDTO();
            orderDetailsDTO.setOrderId(id3);
            long s3 = System.currentTimeMillis();
            OrderResultTO orderInfo = orderDataConverter.getOrderInfo(shopBaseDataParam, Long.valueOf(id3));
            long e3 = System.currentTimeMillis();
            log.info("f3====OrderBussinessImpl=getOrderList===={}ms", (e3 - s3));

            if (null == orderInfo) {
                orderDetailsDTO.setOrderId(id3);
                orderDTOS.add(orderDetailsDTO);
                continue;
            }

            //pop订单
            if (0 == colType) {
                OrderSearchInfo popOrder = orderInfo.getPopOrder();
                if (null == popOrder) {
                    orderDetailsDTO.setOrderId(id3);
                    orderDTOS.add(orderDetailsDTO);
                    continue;
                }

                orderDetailsDTO.setOrderId(popOrder.getOrderId());
                UserInfo consigneeInfo = popOrder.getConsigneeInfo();
                if (null != consigneeInfo) {
                    OrderConsignDTO orderConsignDTO = new OrderConsignDTO();
                    if(orderInfo.isEncryptFlag()){
                        orderConsignDTO.setFullName(consigneeInfo.getFullname());
                        orderConsignDTO.setMobile(consigneeInfo.getMobile());
                        orderConsignDTO.setFullAddress(consigneeInfo.getFullAddress());
                    }else{
                        orderConsignDTO.setFullName(JdApiEncryptionUtils.decoder(id, key, consigneeInfo.getFullname()));
                        orderConsignDTO.setMobile(JdApiEncryptionUtils.decoderPhone(id, key, consigneeInfo.getMobile()));
                        orderConsignDTO.setFullAddress(JdApiEncryptionUtils.decoder(id, key, consigneeInfo.getFullAddress()));
                    }
                    orderConsignDTO.setProvince(consigneeInfo.getProvince());
                    orderConsignDTO.setProvinceId(consigneeInfo.getProvinceId());
                    orderConsignDTO.setCity(consigneeInfo.getCity());
                    orderConsignDTO.setCityId(consigneeInfo.getCityId());
                    orderConsignDTO.setCounty(consigneeInfo.getCounty());
                    orderConsignDTO.setCountyId(consigneeInfo.getCountyId());
                    orderConsignDTO.setTown(consigneeInfo.getTown());
                    orderConsignDTO.setTownId(consigneeInfo.getTownId());
                    orderDetailsDTO.setOrderConsignDTO(orderConsignDTO);
                }

                orderState = CustomerOrderStatusEnum.getOrderState(popOrder.getOrderState());
                orderDetailsDTO.setWaybill(popOrder.getWaybill());
                orderDetailsDTO.setLogisticsId(popOrder.getLogisticsId());
                orderDetailsDTO.setPrice(popOrder.getOrderTotalPrice());
                orderDetailsDTO.setPostFree(popOrder.getFreightPrice());
                orderDetailsDTO.setComment(popOrder.getOrderRemark());
                orderDetailsDTO.setStatus(orderState);
                orderDetailsDTO.setOrderStateStr(CustomerOrderStatusEnum.getSubNameByDescribe(popOrder.getOrderState()));
                orderDetailsDTO.setOrderStateRemark(popOrder.getOrderStateRemark());
                orderDetailsDTO.setSellerDiscount(popOrder.getSellerDiscount());
                orderDetailsDTO.setOrderSellerPrice(popOrder.getOrderSellerPrice());
                orderDetailsDTO.setOrderTime(popOrder.getOrderStartTime());
                orderDetailsDTO.setPaymentTime(popOrder.getPaymentConfirmTime());
                orderDetailsDTO.setPayType(popOrder.getPayType());
                orderDetailsDTO.setOrderPayment(popOrder.getOrderPayment());
                if (orderState == 2 || orderState == 3 || orderState == 4 || orderState == 5 || orderState == 6) {
           			// 付款完成
                    orderDetailsDTO.setRemarkFlag(1);
                } else {
                    orderDetailsDTO.setRemarkFlag(0);
                }
                orderPin = popOrder.getPin();
                InvoiceEasyInfo invoiceEasyInfo = popOrder.getInvoiceEasyInfo();
                String invoiceInfo = popOrder.getInvoiceInfo();
                OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
                if ("不需要开具发票".equals(invoiceInfo)) {
                    orderInvoiceDTO.setInvoiceType(new Byte("0"));
                } else {
                    if (null != invoiceEasyInfo) {//发票信息
                        if (null != invoiceEasyInfo.getInvoiceType() && null != popOrder.getVatInfo() && "2".equals(invoiceEasyInfo.getInvoiceType())) {//增值税发票(专票)
                            VatIncoiceInfo vatInfo = popOrder.getVatInfo();
                            orderInvoiceDTO.setTitle(vatInfo.getInvoicePersonalName());
                            orderInvoiceDTO.setInvoiceContentId("增值税发票(专票)");
                            orderInvoiceDTO.setVatNo(vatInfo.getVatNo());
                            if(orderInfo.isEncryptFlag()){
                                orderInvoiceDTO.setConsigneePhone(vatInfo.getUserPhone());
                                orderInvoiceDTO.setVatBankAccount(vatInfo.getBankAccount());
                                orderInvoiceDTO.setVatUserAddress(vatInfo.getUserAddress());
                                orderInvoiceDTO.setVatUserName(vatInfo.getUserName());
                                orderInvoiceDTO.setVatUserPhone(vatInfo.getUserPhone());
                            }else{
                                orderInvoiceDTO.setConsigneePhone(JdApiEncryptionUtils.decoder(id, key, vatInfo.getUserPhone()));
                                orderInvoiceDTO.setVatBankAccount(JdApiEncryptionUtils.decoder(id, key, vatInfo.getBankAccount()));
                                orderInvoiceDTO.setVatUserAddress(JdApiEncryptionUtils.decoder(id, key, vatInfo.getUserAddress()));
                                orderInvoiceDTO.setVatUserName(JdApiEncryptionUtils.decoder(id, key, vatInfo.getUserName()));
                                orderInvoiceDTO.setVatUserPhone(JdApiEncryptionUtils.decoder(id, key, vatInfo.getUserPhone()));
                            }
                            orderInvoiceDTO.setVatAddressRegistered(vatInfo.getAddressRegIstered());
                            orderInvoiceDTO.setVatPhoneRegistered(vatInfo.getPhoneRegIstered());
                            orderInvoiceDTO.setVatDepositBank(vatInfo.getDepositBank());

                        } else if (null != invoiceEasyInfo.getInvoiceType() && "3".equals(invoiceEasyInfo.getInvoiceType())) {//电子发票
                            if(orderInfo.isEncryptFlag()){
                                orderInvoiceDTO.setTitle(invoiceEasyInfo.getInvoiceTitle());
                            }else{
                                orderInvoiceDTO.setTitle(JdApiEncryptionUtils.decoder(id, key, invoiceEasyInfo.getInvoiceTitle()));
                            }
                            orderInvoiceDTO.setInvoiceContentId(InvoiceInfoEnum.getName(invoiceEasyInfo.getInvoiceContentId()));
                            orderInvoiceDTO.setInvoiceCode(invoiceEasyInfo.getInvoiceCode());
                            orderInvoiceDTO.setConsigneePhone(invoiceEasyInfo.getInvoiceConsigneePhone());
                            orderInvoiceDTO.setConsigneeEmail(invoiceEasyInfo.getInvoiceConsigneeEmail());
                        } else if (null != invoiceEasyInfo.getInvoiceType() && "1".equals(invoiceEasyInfo.getInvoiceType())) {//普通发票
                            if(orderInfo.isEncryptFlag()){
                                orderInvoiceDTO.setTitle(invoiceEasyInfo.getInvoiceTitle());
                            }else{
                                orderInvoiceDTO.setTitle(JdApiEncryptionUtils.decoder(id, key, invoiceEasyInfo.getInvoiceTitle()));
                            }
                            orderInvoiceDTO.setInvoiceContentId(InvoiceInfoEnum.getName(invoiceEasyInfo.getInvoiceContentId()));
                            orderInvoiceDTO.setInvoiceCode(invoiceEasyInfo.getInvoiceCode());
                        }
                        orderInvoiceDTO.setInvoiceType(new Byte(invoiceEasyInfo.getInvoiceType()));//发票类型，0=不开发票、1=普通发票、2=增值税发票(专票)、3=电子发票
                        orderDetailsDTO.setOrderInvoiceDTO(orderInvoiceDTO);
                    }
                }

                //获取skuId
                List<GoosDetailInfoDTO> goosDetailInfoDTOS = Lists.newArrayList();
                List<ItemInfo> itemInfoList = popOrder.getItemInfoList();
                if (null == itemInfoList || itemInfoList.isEmpty()) {
                    orderDetailsDTO.setSkuInfo(new ArrayList<GoodsSkuInfoVO>());
                } else {
                    for (ItemInfo itemInfo : itemInfoList) {
                        skuIds.append(itemInfo.getSkuId() + ",");
                        jdPrice += Double.valueOf(itemInfo.getJdPrice());

                        ShopCommonParam shopCommonParam = new ShopCommonParam();
                        shopCommonParam.setSchemaId(shopBaseDataParam.getSchemaId());
                        shopCommonParam.setShopId(shopBaseDataParam.getShopId());
                        ShopGoodsSkuDTO shopGoodsSkuDTO = shopGoodSkuBusiness.queryShopGoodsInfoBySkuIdAndShopId(shopCommonParam, Long.parseLong(itemInfo.getSkuId()));
                        GoosDetailInfoDTO gdid = new GoosDetailInfoDTO();
                        gdid.setGoodsName(itemInfo.getSkuName());
                        if (null != shopGoodsSkuDTO) {
                            gdid.setImageUrl(shopGoodsSkuDTO.getImageUrl());
                        } else {
                            logger.info("shopId={},title={}的店铺没有skuid={}的信息", shopBaseDataParam.getShopId(), shopBaseDataParam.getTitle(), itemInfo.getSkuId());
                        }
                        gdid.setNum(Integer.parseInt(itemInfo.getItemTotal()));
                        gdid.setPrice(Double.parseDouble(itemInfo.getJdPrice()));
                        gdid.setPcUrl(String.format(ItemConstants.PC_ITEM,itemInfo.getSkuId()));
                        gdid.setPhoneUrl(String.format(ItemConstants.PHONE_ITEM,itemInfo.getSkuId()));
                        goosDetailInfoDTOS.add(gdid);
                    }
                }

                orderDetailsDTO.setDetailInfoList(goosDetailInfoDTOS);

                //获取物流公司名称
                /*if (StringUtils.isNotBlank(orderDetailsDTO.getLogisticsId())) {
                } else {
                    orderDetailsDTO.setLogisticsName("");
                }*/

            } else {//fbp订单
                OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
                OrderInfoFBP fbpOrder = orderInfo.getFbpOrder();
                orderDetailsDTO.setOrderId(fbpOrder.getOrderId());
                UserInfoFBP consigneeInfo = fbpOrder.getConsigneeInfo();
                if (null != consigneeInfo) {
                    OrderConsignDTO orderConsignDTO = new OrderConsignDTO();
                    orderConsignDTO.setFullName(JdApiEncryptionUtils.decoder(id, key, consigneeInfo.getFullname()));
                    orderConsignDTO.setMobile(JdApiEncryptionUtils.decoderPhone(id, key, consigneeInfo.getMobile()));
                    orderConsignDTO.setFullAddress(JdApiEncryptionUtils.decoder(id, key, consigneeInfo.getFullAddress()));
                    orderConsignDTO.setProvince(consigneeInfo.getProvince());
                    orderConsignDTO.setProvinceId(consigneeInfo.getProvinceId());
                    orderConsignDTO.setCity(consigneeInfo.getCity());
                    orderConsignDTO.setCityId(consigneeInfo.getCityId());
                    orderConsignDTO.setCounty(consigneeInfo.getCounty());
                    orderConsignDTO.setCountyId(consigneeInfo.getCountyId());
                    orderConsignDTO.setTown(consigneeInfo.getTown());
                    orderConsignDTO.setTownId(consigneeInfo.getTownId());
                    orderDetailsDTO.setOrderConsignDTO(orderConsignDTO);
                }

                orderDetailsDTO.setOrderTime(fbpOrder.getOrderStartTime());
                orderDetailsDTO.setPaymentTime(fbpOrder.getPaymentConfirmTime());
                orderDetailsDTO.setWaybill(fbpOrder.getWaybill());
                orderDetailsDTO.setLogisticsId(fbpOrder.getLogisticsId());
                orderDetailsDTO.setPrice(fbpOrder.getOrderTotalPrice());
                orderDetailsDTO.setPostFree(fbpOrder.getFreightPrice());
                orderDetailsDTO.setComment(fbpOrder.getOrderRemark());
                orderDetailsDTO.setStatus(CustomerOrderStatusEnum.getOrderState(fbpOrder.getOrderState()));
                orderDetailsDTO.setOrderStateStr(CustomerOrderStatusEnum.getSubNameByDescribe(fbpOrder.getOrderState()));
                orderDetailsDTO.setOrderStateRemark(fbpOrder.getOrderStateRemark());
                orderDetailsDTO.setSellerDiscount(fbpOrder.getSellerDiscount());
                orderDetailsDTO.setOrderSellerPrice(fbpOrder.getOrderSellerPrice());
                orderDetailsDTO.setOrderPayment(fbpOrder.getOrderPayment());
                //fbp订单暂时不提供发票信息，接口中无返回

                orderInvoiceDTO.setInvoiceType(new Byte("0"));//发票类型，0=不开发票、1=普通发票、2=增值税发票(专票)、3=电子发票
                orderInvoiceDTO.setTitle("暂无");
                orderInvoiceDTO.setInvoiceContentId("暂无");
                orderInvoiceDTO.setInvoiceCode("暂无");
                orderInvoiceDTO.setConsigneePhone("暂无");
                orderInvoiceDTO.setConsigneeEmail("暂无");
                orderInvoiceDTO.setVatAddressRegistered("暂无");
                orderInvoiceDTO.setVatPhoneRegistered("暂无");
                orderInvoiceDTO.setVatDepositBank("暂无");
                orderInvoiceDTO.setVatBankAccount("暂无");
                orderInvoiceDTO.setVatUserAddress("暂无");
                orderInvoiceDTO.setVatUserName("暂无");
                orderInvoiceDTO.setVatUserPhone("暂无");
                orderDetailsDTO.setOrderInvoiceDTO(orderInvoiceDTO);

                //获取skuId
                List<GoosDetailInfoDTO> goosDetailInfoDTOS = Lists.newArrayList();
                List<ItemInfoFBP> itemInfoList = fbpOrder.getItemInfoList();
                if (null == itemInfoList || itemInfoList.isEmpty()) {
                    orderDetailsDTO.setSkuInfo(new ArrayList<GoodsSkuInfoVO>());
                } else {
                    for (ItemInfoFBP itemInfo : itemInfoList) {
                        skuIds.append(itemInfo.getSkuId() + ",");
                        jdPrice += Double.valueOf(itemInfo.getJdPrice());

                        ShopCommonParam shopCommonParam = new ShopCommonParam();
                        shopCommonParam.setSchemaId(shopBaseDataParam.getSchemaId());
                        shopCommonParam.setShopId(shopBaseDataParam.getShopId());
                        ShopGoodsSkuDTO shopGoodsSkuDTO = shopGoodSkuBusiness.queryShopGoodsInfoBySkuIdAndShopId(shopCommonParam, Long.parseLong(itemInfo.getSkuId()));
                        GoosDetailInfoDTO gdid = new GoosDetailInfoDTO();
                        gdid.setGoodsName(itemInfo.getSkuName());
                        gdid.setImageUrl(shopGoodsSkuDTO.getImageUrl());
                        gdid.setNum(Integer.parseInt(itemInfo.getItemTotal()));
                        gdid.setPrice(Double.parseDouble(itemInfo.getJdPrice()));
                        gdid.setPcUrl(String.format(ItemConstants.PC_ITEM,itemInfo.getSkuId()));
                        gdid.setPhoneUrl(String.format(ItemConstants.PHONE_ITEM,itemInfo.getSkuId()));
                        goosDetailInfoDTOS.add(gdid);
                    }
                }

                orderDetailsDTO.setDetailInfoList(goosDetailInfoDTOS);
            }

            //获取sku信息
            orderDetailsDTO.setSkuIds(skuIds.toString());
            if (0 == colType) {
                if (1 == orderState) {
                    orderDetailsDTO.setOrderPayment(String.valueOf(jdPrice));
                } else if (7 == orderState) {
                    if ("4-在线支付".equals(orderDetailsDTO.getPayType())) {
                        orderDetailsDTO.setOrderStateStr("已下单");
                        orderDetailsDTO.setOrderStateRemark("已下单");
                    }
                }
            }

            orderDTOS.add(orderDetailsDTO);
        }
        resMap.put("orderList", orderDTOS);
        return resMap;
    }
}
