package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.ShopGoodsInfoBussiness;
import com.pes.jd.business.sub.ShopGoodsLabelBusiness;
import com.pes.jd.dao.sub.ShopGoodsLabelDao;
import com.pes.jd.dao.sub.ShopGoodsSkuLabelDao;
import com.pes.jd.model.DTO.ShopGoodsLabelDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.SkuQuery;
import com.pes.jd.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ShopGoodsLabelBusinessImpl implements ShopGoodsLabelBusiness {

    @Autowired
    private ShopGoodsLabelDao shopGoodsLabelDao;
    @Autowired
    private ShopGoodsInfoBussiness shopGoodsInfoBussiness;
    @Autowired
    private ShopGoodsSkuLabelDao shopGoodsSkuLabelDao;

    @Override//按照SPU添加的商品知识点，将会同步到每个对应的SKU单位商品下。按照sku单位商品添加或修改的知识点将不影响SPU
    @Transactional(rollbackFor = Exception.class)
    public Long insertGoodsLabel(ShopGoodsLabelDTO record, SkuQuery skuQuery) {
        List<Long> skuIds = shopGoodsInfoBussiness.selectSkuIdsByWareId(skuQuery, record.getWareId());
        if(CollectionUtils.isNotEmpty(skuIds)){
            List<Long> filterSkuIds = shopGoodsSkuLabelDao.searchGoodsLabelBySkuIds(skuQuery, skuIds, record.getLabel());
            if(CollectionUtils.isNotEmpty(filterSkuIds)){
                List<Long> finalSkuIds = skuIds.stream().filter(skuId -> !filterSkuIds.contains(skuId)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(finalSkuIds)){
                    List<ShopGoodsSkuLabelDTO> gkLst = getShopGoodsSkuLabelLst(record, finalSkuIds);
                    Long aLong = shopGoodsSkuLabelDao.insertSkuLabelLst(skuQuery, gkLst);
                    log.info("根据wareId 插入对应的sku维度知识点结果为={}", aLong);
                }
            }else{
                List<ShopGoodsSkuLabelDTO> gkLst = getShopGoodsSkuLabelLst(record, skuIds);
                Long aLong = shopGoodsSkuLabelDao.insertSkuLabelLst(skuQuery, gkLst);
                log.info("根据wareId 插入对应的sku维度知识点结果为={}", aLong);
            }
        }
        return shopGoodsLabelDao.insertGoodLabel(record, skuQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateGoodLabel(ShopGoodsLabelDTO record, SkuQuery skuQuery) {
        //更新对应spu维度的知识点 1.先更新对应的sku的知识点（通过对应知识点更新skuId的知识点）   2.更新
//        List<Long> skuIds = shopGoodsInfoBussiness.selectSkuIdsByWareId(skuQuery, record.getWareId());
//        if (CollectionUtils.isNotEmpty(skuIds)) {
//            ShopGoodsLabelDO shopGoodsLabelDO = shopGoodsLabelDao.selectByPrimaryKey(record, skuQuery);
            //根据店铺id skuId 原来的label 更新为新的label
//            int i = shopGoodsSkuLabelDao.updateSkuLabelByShopIdAndSkuIdsAndLabel(skuQuery, skuIds, shopGoodsLabelDO.getLabel(), record.getLabel());
        Long i = 0L;
        List<Long> skuIds = shopGoodsInfoBussiness.selectSkuIdsByWareId(skuQuery, record.getWareId());
        if(CollectionUtils.isNotEmpty(skuIds)){
            List<Long> filterSkuIds = shopGoodsSkuLabelDao.searchGoodsLabelBySkuIds(skuQuery, skuIds, record.getLabel());
            if(CollectionUtils.isNotEmpty(skuIds)){
                List<Long> finalSkuIds = skuIds.stream().filter(skuId -> !filterSkuIds.contains(skuId)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(finalSkuIds)){
                    for (Long skuId : finalSkuIds) {
                        ShopGoodsSkuLabelDTO shopGoodsSkuLabelDTO = new ShopGoodsSkuLabelDTO();
                        shopGoodsSkuLabelDTO.setShopId(record.getShopId());
                        shopGoodsSkuLabelDTO.setSkuId(skuId);
                        shopGoodsSkuLabelDTO.setLabel(record.getLabel());
                        i = shopGoodsSkuLabelDao.insertSkuLabel(shopGoodsSkuLabelDTO, skuQuery);
                    }
                }
            }
        }
        log.info("根据wareId 增加对应的sku维度知识点结果为={}", i);
//        }
        return shopGoodsLabelDao.updateGoodLabel(record, skuQuery);
    }

    @Override
    @Transactional
    public int deleteGoodsLabelAndSkuLabel(Long id, SkuQuery skuQuery) {
        if (id == null) {
            return 0;
        }
        //--------根据内容删除指定sku下的知识点
        ShopGoodsLabelDTO shopGoodsLabelDTO = shopGoodsLabelDao.selectGoodsLabelById(id, skuQuery);
        if (null != shopGoodsLabelDTO) {
            List<Long> skuIds = shopGoodsInfoBussiness.selectSkuIdsByWareId(skuQuery, shopGoodsLabelDTO.getWareId());
            if (CollectionUtils.isNotEmpty(skuIds)) {
                //根据原来的知识点删除指定sku维度的记录
                int i = shopGoodsSkuLabelDao.deleteSKuLabelBySkuIdsAndLabel(skuQuery, skuIds, shopGoodsLabelDTO.getLabel());
                log.info("根据wareId 删除对应的sku维度知识点结果为={}", i);
            }
        }
        return shopGoodsLabelDao.deleteGoodsLabelById(id, skuQuery);
    }

    @Override
    public List<ShopGoodsSkuLabelDTO> searchGoodsSkuByWareid(Long shopId, String schemaId, List<Long> wareIds) {
        String tableName = CommonUtils.getTableName(schemaId, TableEnum.PES_SHOP_GOODS_LABEL.getName());
        return shopGoodsLabelDao.searchByWareIds(new HashSet<>(wareIds), tableName, shopId);
    }

    //---根据构造所有需要插入到sku维度知识点的数据
    private List<ShopGoodsSkuLabelDTO> getShopGoodsSkuLabelLst(ShopGoodsLabelDTO record, List<Long> skuIds) {
        Long id = record.getId();
        Long shopId = record.getShopId();
        String label = record.getLabel();
        List<ShopGoodsSkuLabelDTO> resultLst= new ArrayList<>(skuIds.size());
        skuIds.forEach(ele-> {
            ShopGoodsSkuLabelDTO dto = new ShopGoodsSkuLabelDTO();
            dto.setId(id);
            dto.setSkuId(ele);
            dto.setShopId(shopId);
            dto.setLabel(label);
            resultLst.add(dto);
        });
        return resultLst;
    }
}
