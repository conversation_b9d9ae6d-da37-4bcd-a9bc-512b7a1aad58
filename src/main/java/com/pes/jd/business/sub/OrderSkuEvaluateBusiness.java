package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.DTO.OrderSkuEvaluateDTO;
import com.pes.jd.model.Param.OrderSkuEvaluateParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.VO.OrderSkuEvalVO;

import java.io.OutputStream;
import java.util.Date;
import java.util.List;

public interface OrderSkuEvaluateBusiness {
	public List<OrderSkuEvaluateDTO> selectOrderSkuEvaluateByDateByCsNickByScore(
            ShopCommonParam shop, OrderSkuEvaluateParam param);

	public OrderSkuEvalVO<CsOrderIndexDTO> selectEvaluateInfoByOrderIdByBuyer(Date startDate, Date endDate, ShopCommonParam shop, Integer id, Long orderId);

	//public List<CsOrderIndexDTO> selectCsReceptionByBuyerByOrderId(Date startDate,Date endDate,ShopCommonParam shop, Long orderId);

	void exportOrderSkuEvaluate(OutputStream out, String jsonParam) throws Exception;
}
