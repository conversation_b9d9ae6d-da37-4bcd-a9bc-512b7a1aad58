package com.pes.jd.business.sub.impl;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.getRemarkByCreateTime.OrderRemark;
import com.pes.jd.annotation.DBLog;
import com.pes.jd.annotation.OrderLog;
import com.pes.jd.business.sub.OrderCustomerBusiness;
import com.pes.jd.constants.ItemConstants;
import com.pes.jd.dao.sub.CustomerOrderDao;
import com.pes.jd.dao.sub.OrderRemarkDao;
import com.pes.jd.data.convert.OrderAddressInvoiceConverter;
import com.pes.jd.data.convert.OrderReMarkConverter;
import com.pes.jd.data.convert.RefundOperatorConverter;
import com.pes.jd.model.BO.OrderAddressInvoiceBO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.CustomerOrderStatusEnum;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2019-05-23 14:31
 */
@Service
public class OrderCustomerBusinessImpl implements OrderCustomerBusiness {
	
	private static final Logger logger = LoggerFactory.getLogger(OrderCustomerBusiness.class);


    @Autowired
    private OrderReMarkConverter converter;


    @Autowired
    private RefundOperatorConverter refundOperatorConverter;
    
    @Autowired
    private OrderAddressInvoiceConverter  orderAddressInvoiceConverter;
    
    @Autowired
    private CustomerOrderDao  customerOrderDao;
    
    
    @Autowired
    private OrderRemarkDao  orderRemarkDao;
    
    
    
    @Override
    public Map<String, Object> selectOrderDeatilByShopIdAndBuyerNick(ShopCommonParam shop, String buyerNick, Date createTime, Integer colType, SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) throws ParseException {
       Map<String, Object> resMap = new HashMap<String, Object>();
    	
    	List<OrderInfoDTO> orderInfoDTOS = Lists.newArrayList();
        
//      修改原有逻辑
//      地址、发票信息直接通过京东接口调用存储本地数据库
//    	logger.info("查询数据开始");
        Map<String, Integer> tableTotalRecordNumMap = customerOrderDao.selectOrderInfoByBuyerNickAndShopCount(shop, "pes_customer_order", buyerNick, createTime, createTime);
        resMap.put("count",tableTotalRecordNumMap.get("count"));
//        logger.info("查询数据开始");
        List<OrderDTO>  orderDTOList = customerOrderDao.selectOrderInfoByBuyerNickAndShop(shop, "pes_customer_order", buyerNick, createTime, createTime,sortPageQuery,tableTotalRecordNumMap,orderInfoLogUploadParam);
//        logger.info("查询数据结束");
        
        if(null==orderDTOList || orderDTOList.size()==0) {
        	resMap.put("orderList",new ArrayList<OrderDTO>());
        	return resMap;
        }
        List<Long>  orderList = orderDTOList.stream().map(OrderDTO::getOrderId).collect(Collectors.toList());
        Map<Long, OrderDTO> orderDTOMap = orderDTOList.stream().collect(Collectors.toMap(OrderDTO::getOrderId, OrderDTO -> OrderDTO));
        
        
        for (Long orders : orderList) {
        	OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
            OrderDTO orderDTO = orderDTOMap.get(orders);
            orderInfoDTO.setOrderId(orderDTO.getOrderId());
            orderInfoDTO.setOrderTime(orderDTO.getCreated());
            orderInfoDTO.setPostFree(orderDTO.getPostFee());
            
            orderInfoDTO.setPaymentTime(orderDTO.getPayTime());
            orderInfoDTO.setStatus(CustomerOrderStatusEnum.getType(orderDTO.getStatus())+"");
         	orderInfoDTO.setPrice(orderDTO.getPayment());
            
            List<GoosDetailInfoDTO> goosDetailInfoDTOS = Lists.newArrayList();
            
//            logger.info("查询商品开始");
            List<GoodsDTO> convertGoodsDTO = customerOrderDao.selectGoodsDetailInfoByOrderId(shop, "pes_order_goods_sku", 
            		orders, orderDTO.getCreated(),orderDTO.getCreated());
//            logger.info("查询商品结束");
            if(null!=convertGoodsDTO) {
            	 for (int i = 0; i < convertGoodsDTO.size(); i++) {
                     GoosDetailInfoDTO goosDetailInfoDTO = new GoosDetailInfoDTO();
                     goosDetailInfoDTO.setNum(convertGoodsDTO.get(i).getItemNum());
//                     List<GoodsDTO> convertGoodsDtailMap = goodsDetailMap.get(convertGoodsDTO.get(i).getItemSkuId());
//                     logger.info("查询商品详情开始");
                     List<GoodsDTO> convertGoodsDtailMap = customerOrderDao.selectGoodsDetailInfoBySkuId(convertGoodsDTO.get(i).getItemSkuId(),shop);
//                     logger.info("查询商品详情结束");
                     if(convertGoodsDtailMap != null) {
                    	 goosDetailInfoDTO.setGoodsName(convertGoodsDtailMap.get(0).getSkuName());
                    	 goosDetailInfoDTO.setImageUrl(convertGoodsDtailMap.get(0).getImageUrl());
                    	 goosDetailInfoDTO.setPcUrl(String.format(ItemConstants.PC_ITEM,convertGoodsDtailMap.get(0).getSkuId()));
                    	 goosDetailInfoDTO.setPhoneUrl(String.format(ItemConstants.PHONE_ITEM,convertGoodsDtailMap.get(0).getSkuId()));
                     }
                     goosDetailInfoDTO.setPrice(convertGoodsDTO.get(i).getItemPrice());
                     goosDetailInfoDTOS.add(goosDetailInfoDTO);
                 }
            }
            orderInfoDTO.setDetailInfoList(goosDetailInfoDTOS);
            
//            logger.info("查询京东接口未付款开始");
        	OrderAddressInvoiceBO notPayorderAddressInvoiceBO =  orderAddressInvoiceConverter.getNotPayOrderAddress(shop, orders,colType);
            
            
//            logger.info("查询京东接口未付款结束");
            if(StringUtils.isBlank(notPayorderAddressInvoiceBO.getOrderState())) {
//            	pop接口中获取数据
            	OrderAddressInvoiceBO orderAddressInvoiceBO =  this.getInoviceAndAddress(shop, orders, colType, orderDTO.getCreated());
                OrderInvoiceDTO  orderInvoiceDTO = orderAddressInvoiceBO.getOrderInvoiceDTO();
                OrderConsignDTO  orderConsignDTO = orderAddressInvoiceBO.getOrderConsignDTO();
                
                if(null!=orderInvoiceDTO && null!=orderInvoiceDTO.getInvoiceType()&& orderInvoiceDTO.getInvoiceType().byteValue()!=0) {
                	orderInfoDTO.setOrderInvoiceDTO(orderInvoiceDTO);
                }else {
                	orderInfoDTO.setOrderInvoiceDTO(null);
                }
                
                orderInfoDTO.setOrderConsignDTO(orderConsignDTO);
                orderInfoDTO.setStatus(CustomerOrderStatusEnum.getType(orderAddressInvoiceBO.getOrderState())+"");
                
                
                if(null!=orderAddressInvoiceBO.getPrice()) {
              	  orderInfoDTO.setPrice(orderAddressInvoiceBO.getPrice());
                }else {
              	  orderInfoDTO.setPrice(0.0);
                }
                
                
                if(null!=orderAddressInvoiceBO.getPostFee()) {
                	orderInfoDTO.setPostFree(orderAddressInvoiceBO.getPostFee());
                }
                
                
                if(StringUtils.isNotBlank(orderAddressInvoiceBO.getPaymentTime())) {
              		orderInfoDTO.setPaymentTime(DateFormatUtils.parseYMdHms(orderAddressInvoiceBO.getPaymentTime()));
                }
                orderAddressInvoiceBO.setComment(orderAddressInvoiceBO.getComment());
                
//                logger.info("查询地址记录开始");
             	int addrNum = customerOrderDao.selectAddrFlag(shop,orders);
//             	 logger.info("查询地址记录结束");
             	if(addrNum>=1) {
             		orderInfoDTO.setModifyAddressFlag(new Integer(0));
             	}else {
//                  修改订单收货地址（仅可修改SOP类型，且状态为等待出库的有效订单。）
             		if(CustomerOrderStatusEnum.WAIT_SELLER_STOCK_OUT.getSubName().equals(orderAddressInvoiceBO.getOrderState()) ||
             				CustomerOrderStatusEnum.DengDaiChuKu.getSubName().equals(orderAddressInvoiceBO.getOrderState())) {
             			orderInfoDTO.setModifyAddressFlag(new Integer(1));
             		}else {
             			orderInfoDTO.setModifyAddressFlag(new Integer(0));
             		}
             	}
                
        		int status = 0; 
        		if(orderAddressInvoiceBO.getOrderState()!=null) {
        			status = CustomerOrderStatusEnum.getType(orderAddressInvoiceBO.getOrderState());
        			if(status==0) {
        				  logger.error("订单状态 未定义  :{} ",orderAddressInvoiceBO.getOrderState());
        			}
        			
        		}
        		if ( status ==2 || status==3 || status==4 || status==5 ||status==6) {
        				orderInfoDTO.setRemarkFlag(new Integer("1"));
        		}else {
        			orderInfoDTO.setRemarkFlag(new Integer("0"));
        		}
             	
//        		区分 付款前后
//        		付款前取消订单
        		if(CustomerOrderStatusEnum.TRADE_CANCELED.getSubName().equals(orderAddressInvoiceBO.getOrderState())) {
    				orderInfoDTO.setRemarkFlag(new Integer("0"));
    			}
        		
        		
        		if(StringUtils.isNotBlank(orderAddressInvoiceBO.getPaymentTime())
        				) {
        			orderInfoDTO.setRemarkFlag(new Integer("1"));
        		}
        		
        		
                orderInfoDTO.setComment(orderAddressInvoiceBO.getComment());
                
                
                
                
            }else {
            	orderInfoDTO.setRemarkFlag(new Integer("0"));
            	
            	//未付款数据
//            	  logger.info("查询地址信息");
              List<OrderConsignDTO> conList= customerOrderDao.selectConsignDTO( shop,  orders, orderDTO.getCreated());
//              logger.info("查询地址信息结束");
              if(null!=conList && conList.size()>0) {
              	OrderConsignDTO  itemConsign = conList.get(0);
              	if(null==itemConsign || null==itemConsign.getFullAddress()) {
//              		未付款数据 获取不到地址数据说明是老数据没有通过mq消费，拉取到每日的数据
//              		 发送未付款订单数据重新获取
//              		logger.info("查询未付款");
//              		 notPayorderAddressInvoiceBO = orderAddressInvoiceConverter.getNotPayOrderAddress(shop, orders,colType);
//              		logger.info("查询未付款结束");
              		orderInfoDTO.setOrderConsignDTO(notPayorderAddressInvoiceBO.getOrderConsignDTO());
              	}else {
              		orderInfoDTO.setOrderConsignDTO(itemConsign);
              	}
//              	 orderInfoDTO.setHasAddress(1);
              }else {
            	  orderInfoDTO.setOrderConsignDTO(null);
//              	 orderInfoDTO.setHasAddress(0);
              }
            
              
//            List<OrderInvoiceDTO> invoiceList = customerOrderDao.selectOrderInvoice(shop,  orders, orderDTO.getCreated());
//            if(null!=invoiceList && invoiceList.size()>0) {
//            	OrderInvoiceDTO orderInvoiceDTO = invoiceList.get(0);
//            	if(null==orderInvoiceDTO || null==orderInvoiceDTO.getInvoiceType() || orderInvoiceDTO.getInvoiceType().byteValue()==0) {
            		orderInfoDTO.setOrderInvoiceDTO(null);
//            	}else {
//            		orderInfoDTO.setOrderInvoiceDTO(orderInvoiceDTO);
//            	}
//            	 orderInfoDTO.setHasInvoice(1);
//            }else {
//            	orderInfoDTO.setOrderInvoiceDTO(null);
//            }
              
            //查询留言
//            		logger.info("查询留言开始"); 
            List<OrderDetailInfoDTO> detailList =  customerOrderDao.selectOrderDetail(shop,orders,orderDTO.getCreated(),"pes_order_detail");
//            logger.info("查询留言结束"); 
            if(null!=detailList && detailList.size()>0) {
            	orderInfoDTO.setHasComment(1);
            	OrderDetailInfoDTO orderDetailInfoDTO = detailList.get(0);
            	if(null==orderDetailInfoDTO || null==orderDetailInfoDTO.getCustRemark()) {
            		orderInfoDTO.setComment("");
            	}else {
            		orderInfoDTO.setComment(detailList.get(0).getCustRemark());
            	}
            }else {
            	orderInfoDTO.setComment("");
            }
            
            orderInfoDTO.setModifyAddressFlag(new Integer("0"));
            
           }
            
//            最终更新订单状态 从出库和取消中取数据
//            int outStock = customerOrderDao.selectOrderStatu( shop,  orders,  orderDTO.getCreated(),"pes_order_out_stock_time");
//            
//            
//            String orderState = orderInfoDTO.getStatus();
//            if("1".equals(orderState)) {
//            	//未付款
//            	orderInfoDTO.setStatus(CustomerOrderStatusEnum.NO_PAY.getType().intValue()+"");
//            }else if("2".equals(orderState)){
////            		已付款
//            		orderInfoDTO.setStatus(CustomerOrderStatusEnum.WAIT_SELLER_STOCK_OUT.getType().intValue()+"");
//            	 if(outStock>0) {
//            		 //已出库
//                 	orderInfoDTO.setStatus(CustomerOrderStatusEnum.WAIT_GOODS_RECEIVE_CONFIRM.getType()+"");
//                 }
//            }
//               int cancel = customerOrderDao.selectOrderStatu( shop,  orders,  orderDTO.getCreated(),"pes_order_cancel");
//               if(cancel>0) {
//               	orderInfoDTO.setStatus(CustomerOrderStatusEnum.TRADE_CANCELED.getType()+"");
//               }
            
//            logger.info("查询备注开始"); 
            List<OrderRemarkDTO> list= orderRemarkDao.getOrderRemark(shop,orders,orderDTO.getCreated());
//            logger.info("查询备注结束"); 
            if(null!=list && list.size()>0) {
            	OrderRemarkDTO OrderRemarkDTO = list.get(0);
            	if(null!=OrderRemarkDTO) {
            		orderInfoDTO.setFlag(Integer.valueOf(OrderRemarkDTO.getFlag()));
            		orderInfoDTO.setMemo(OrderRemarkDTO.getRemark());
            	}else {
            		orderInfoDTO.setFlag(0);
                	orderInfoDTO.setMemo("");
            	}
            }else {
            	orderInfoDTO.setFlag(0);
            	orderInfoDTO.setMemo("");
            }
            
          
          orderInfoDTO.setHasAddress(1);
          orderInfoDTO.setHasComment(1);
          orderInfoDTO.setHasInvoice(1);
          
          //订单实际付款金额
//            if(null!=orderAddressInvoiceBO.getPrice() && orderAddressInvoiceBO.getPrice()>0) {
//            	orderInfoDTO.setPrice(orderAddressInvoiceBO.getPrice());
//            }else {
//            	 if(null==orderDTO.getPayment()  || orderDTO.getPayment().doubleValue()==0) {
//                 	orderInfoDTO.setPrice(orderDTO.getTotalFee());
//                 }else {
//                 	orderInfoDTO.setPrice(orderDTO.getPayment());
//                 }
//            }
//            
//            OrderInvoiceDTO  orderInvoiceDTO = orderAddressInvoiceBO.getOrderInvoiceDTO();
//            if(orderInvoiceDTO!=null) {
//            	
//            }
            
      
            
//            Integer orderStatus = CustomerOrderStatusEnum.getType(orderAddressInvoiceBO.getOrderState());
            
            
            
            
//            if(null!=orderStatus) {
//            	 //重新请求获取订单状态
//                orderInfoDTO.setStatus(orderStatus.intValue()+"");
////              添加/修改商家备注，可修改付款后的订单（如果存在则修改，不存在则新增，如果提交空值则尝试删除存在的商家备注）
//                if(orderStatus.intValue()!=1) {
//                	orderInfoDTO.setRemarkFlag(1);
//                }else {
//                	orderInfoDTO.setRemarkFlag(0);
//                }
////              查询地址修改
//             	int addrNum = customerOrderDao.selectAddrFlag(shop,orders);
//             	if(addrNum>=1) {
//             		orderInfoDTO.setModifyAddressFlag(new Integer(1));
//             	}else {
////                  修改订单收货地址（仅可修改SOP类型，且状态为等待出库的有效订单。）
//             		if(CustomerOrderStatusEnum.WAIT_SELLER_STOCK_OUT.getSubName().equals(orderAddressInvoiceBO.getOrderState()) ||
//             				CustomerOrderStatusEnum.DengDaiChuKu.getSubName().equals(orderAddressInvoiceBO.getOrderState())) {
//             			orderInfoDTO.setModifyAddressFlag(new Integer(0));
//             		}else {
//             			orderInfoDTO.setModifyAddressFlag(new Integer(2));
//             		}
//             	}
//            }
//            orderInfoDTO.setAddressDTO(orderAddressInvoiceBO.getAddressDTO());
//            orderInfoDTO.setInvoiceInfoDTO(orderAddressInvoiceBO.getInvoiceInfoDTO());
//            if(null!=orderAddressInvoiceBO) {
//            	orderInfoDTO.setComment(orderAddressInvoiceBO.getComment());
//            }
//             if(StringUtils.isBlank(orderInfoDTO.getMemo())) {
//            	 orderInfoDTO.setMemo("");
//             }
//             if(null==orderInfoDTO.getFlag()) {
//            	 orderInfoDTO.setFlag(new Integer("1"));
//             }

            
            
            
            orderInfoDTOS.add(orderInfoDTO);
            
            
        }
        
        resMap.put("orderList",orderInfoDTOS);
        return resMap;
    }


    @Override
	@OrderLog(paramType = OrderInfoLogUploadParam.class,resultType = void.class)
    @DBLog(value = "updateAddress")
    public void updateAddress(ShopCommonParam shopCommonParam, Long orderId, String buyer_Nick, ShippingAddressDTO shippingAddressDTOparam, Date created, OrderInfoLogUploadParam orderInfoLogUploadParam) {
    	customerOrderDao.updateAddress(shopCommonParam,orderId,buyer_Nick);
    	customerOrderDao.updateConsignAddr( shopCommonParam,  orderId,created,shippingAddressDTOparam);
    }
    
    
    
//    private FullAddressAreaDTO getAddressByParam(Integer level,String cityId,String provinceId,String countryId) {
//    	FullAddressAreaDTO fullAddressAreaDTO  = new FullAddressAreaDTO();
//    	try {
//    		HttpEntity<Object> body = RequestEntityBuilder.builder().put("level", level).put("cityId", cityId).put("provinceId", provinceId)
//        			.put("countryId", countryId).toRequestEntity();
//            String serverId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER);
//            CommonRestTemplate commonRestTemplate = SpringUtil.getBean("commonRestTemplate", CommonRestTemplate.class);
//            ApiResponse apiResponse =  commonRestTemplate.postRest(serverId, "/address/getAddressByParam", body);
//            JSONArray jarr =  JSONObject.parseArray(JSONObject.toJSONString(apiResponse.getData().get("list")))  ;
//            if(jarr!=null && jarr.size()>0) {
//            	fullAddressAreaDTO =   JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(jarr.get(0))), FullAddressAreaDTO.class) ;
//            }
//    	}catch(Exception e) {
//    		logger.error("OrderCustomerBusinessImpl.getAddressByParam error:{}",e.getMessage(),e);
//    	}
//        return fullAddressAreaDTO;
//    }


	@Override
	public List<OrderRemarkDTO> getOrderRemark(ShopCommonParam shopCommonParam, Long orderId,Date CreateTime) {
		return orderRemarkDao.getOrderRemark(shopCommonParam,orderId,CreateTime);
	}


	@Override
	public void insertOrderRemark(ShopCommonParam shopCommonParam, Date CreateTime, OrderRemarkDTO orderRemarkDTO) {
		orderRemarkDao.inert( shopCommonParam,  CreateTime,  orderRemarkDTO);
	}
	
	
	
	@Override
	public void modifyRemark(ShopCommonParam shopCommonParam, Date CreateTime, OrderRemark orderRemark) {
		orderRemarkDao.updateOrderRemark(shopCommonParam, orderRemark, CreateTime);
	}
	


	@Override
	public int getRefund(ShopCommonParam shop, Long orderId,Date orderTime) {
		  //判断发送售前售后
		  //订单类型货到付款 判断finishtime  没有售前，否则售后
		  //其他类型是否付款 没有付款则不用发直接返回， 否则 判断finishtime 没有则售前，有则售后
		List<OrderDTO> list = customerOrderDao.selectOrderById(shop,orderId,orderTime);
		if(null== list || list.size()==0) {
			return 0;
		}
		OrderDTO orderDTO = list.get(0);
		
		Integer status =   CustomerOrderStatusEnum.getType(orderDTO.getStatus());
		
		if(null!=status && status.intValue()==4) {
//			if(null==orderDTO.getEndTime()) {
//				
//			}
			orderDTO.setEndTime(new Date());
		}else {
			orderDTO.setEndTime(null);
			if(null==orderDTO.getPayTime()) {
				orderDTO.setPayTime(new Date());
			}
			
		}
		
		
		
		return  refundOperatorConverter.getResultRefundOperator(orderId,shop.getSessionKey(), orderDTO.getPayType(), orderDTO.getEndTime(),orderDTO.getPayTime());
		  
	}


	@Override
	public OrderAddressInvoiceBO getInoviceAndAddress(ShopCommonParam shop, Long orderId, Integer colType,Date orderTime) {
		//查询入库 如果有的话 
		
//		List<OrderConsignDTO> conList= customerOrderDao.selectConsignDTO(shop,orderId,orderTime);
//		
//		List<OrderInvoiceDTO> invoiceList = customerOrderDao.selectOrderInvoice(shop,  orderId, orderTime);
//		
//		List<OrderDetailInfoDTO> detailList =  customerOrderDao.selectOrderDetail(shop,orderId,orderTime,"pes_order_detail");
//		
//		
//		if(null!=conList && conList.size()>0 &&
//				null!=invoiceList && conList.size()>0 &&  null!=detailList && detailList.size()>0 
//				) {
//			
//		}
		
		
		OrderAddressInvoiceBO orderAddressInvoiceBO = orderAddressInvoiceConverter.getOrderAddressInvoice(shop, orderId,colType);
		
//		OrderConsignDTO orderConsignDTO = orderAddressInvoiceBO.getOrderConsignDTO();
//		if(null!=orderConsignDTO && null!=orderConsignDTO.getFullAddress()) {
//			customerOrderDao.delConsignDTO( shop, orderId, orderTime); 
//			orderConsignDTO.setCreatedTime(new Date());
//			orderConsignDTO.setOrderId(orderId);
//			customerOrderDao.insertConsignDTO( shop, orderId, orderTime,orderConsignDTO); 
//		}else {
//			orderAddressInvoiceBO.setOrderConsignDTO(null);
//		}
	
		
		
		
//		 OrderInvoiceDTO orderInvoiceDTO = orderAddressInvoiceBO.getOrderInvoiceDTO();
//		 if(null!=orderInvoiceDTO && null!=orderInvoiceDTO.getInvoiceType() && orderInvoiceDTO.getInvoiceType().byteValue()!=0) {
//			 customerOrderDao.delOrderInvoice(shop, orderId, orderTime);
//			 orderInvoiceDTO.setCreatedTime(new Date());
//			 orderInvoiceDTO.setOrderId(orderId);
//			 customerOrderDao.insertOrderInvoice(shop,  orderId, orderTime,orderInvoiceDTO);
//		 }else {
//			 orderAddressInvoiceBO.setOrderInvoiceDTO(null);
//		 }
		
		
		
//		 customerOrderDao.detOrderDetail(shop, orderId, orderTime);
//		 OrderDetailInfoDTO orderDetailInfoDTO = new OrderDetailInfoDTO();
//		 orderDetailInfoDTO.setCustRemark(orderAddressInvoiceBO.getComment());
//		 orderDetailInfoDTO.setOrderCreated(new Date());
//		 orderDetailInfoDTO.setOrderId(orderId);
//		 orderDetailInfoDTO.setShopId(shop.getShopId());
//		 
//		 customerOrderDao.insertOrderDetail(shop,orderId,orderTime,orderDetailInfoDTO);
		 
//		补充留言
		

		 
//		OrderAddressInvoiceBO OrderAddressInvoiceBO = new OrderAddressInvoiceBO();
//		int status = 0; 
//		if(orderAddressInvoiceBO.getOrderState()!=null) {
//			status = CustomerOrderStatusEnum.getType(orderAddressInvoiceBO.getOrderState());
//		}
		
//		
//		if ( status ==2 || status==3 || status==4) {
////			付款完成
//			orderAddressInvoiceBO.setRemarkFlag(1);
//		}else {
//			orderAddressInvoiceBO.setRemarkFlag(0);
//		}
//		
//		int addrNum = customerOrderDao.selectAddrFlag(shop,orderId);
//		if(addrNum>0) {
//			orderAddressInvoiceBO.setModifyAddressFlag(0);
//		}else {
//			if(CustomerOrderStatusEnum.WAIT_SELLER_STOCK_OUT.getSubName().equalsIgnoreCase(orderAddressInvoiceBO.getOrderState())||
//					CustomerOrderStatusEnum.DengDaiChuKu.getSubName().equalsIgnoreCase(orderAddressInvoiceBO.getOrderState())
//					) {
//				orderAddressInvoiceBO.setModifyAddressFlag(1);
//			}else {
//				orderAddressInvoiceBO.setModifyAddressFlag(0);
//			}
//		}
//		
		 return orderAddressInvoiceBO;
//		
		

	}


	@Override
	public List<OrderDTO> selectOrderById(ShopCommonParam shop, Long orderId, Date orderTime) {
		return customerOrderDao.selectOrderById(shop,orderId,orderTime);
	}


	@Override
	public OrderConsignDTO getOrderAddFlag(ShopCommonParam shop, Long valueOf, Date date,Long orderId,Integer colType) {
		OrderConsignDTO orderConsignDTO = new OrderConsignDTO();
		
//		orderConsignDTO.setModifyAddressFlag(new Integer(1));
//		return orderConsignDTO;
	 	int addrNum = customerOrderDao.selectAddrFlag(shop,orderId);
//	 	
	 	if(addrNum>0) {
	 		orderConsignDTO.setModifyAddressFlag(new Integer(0));
	 		
	 	}else {
	 		orderConsignDTO.setModifyAddressFlag(new Integer(1));
	 	}
//		
	 	return orderConsignDTO;
//	 	OrderAddressInvoiceBO orderAddressInvoiceBO = orderAddressInvoiceConverter.getOrderAddressInvoice(shop, orderId,colType);
//		if(StringUtils.isNoneBlank(orderAddressInvoiceBO.getOrderState())) {
////			修改订单收货地址（仅可修改SOP类型，且状态为等待出库的有效订单。）
//     		if(CustomerOrderStatusEnum.WAIT_SELLER_STOCK_OUT.getSubName().equals(orderAddressInvoiceBO.getOrderState()) ||
//     				CustomerOrderStatusEnum.DengDaiChuKu.getSubName().equals(orderAddressInvoiceBO.getOrderState())) {
//     			orderConsignDTO.setModifyAddressFlag(new Integer(1));
//     		}else {
//     			orderConsignDTO.setModifyAddressFlag(new Integer(0));
//     		}
//			
//		}
//		
//		return orderConsignDTO;
	}


	@Override
	@DBLog(value = "updateOrderRemarkInfo")
	public void updateOrderRemarkInfo(ShopCommonParam shop, Long orderTime, OrderRemark orderRemark,OrderInfoLogUploadParam orderInfoLogUploadParam) {
		orderRemarkDao.updateOrderRemark(shop, orderRemark, new Date(orderTime));
	}


	@Override
	public int selectOrderCount(ShopCommonParam shop, String buyer_nick, Long endTime) {
		 Map<String, Integer> map= customerOrderDao.selectOrderCount(shop,buyer_nick,new Date(endTime));
		 return map.get("count");
	}


	@Override
	public List<OrderDTO> getOrderList(ShopCommonParam shop, String buyerNick, Date date, SortPageQuery sortPageQuery) {
//		Map<String, Integer> tableTotalRecordNumMap = customerOrderDao.selectOrderCount(shop,buyerNick,date);
		List<OrderDTO>  orderDTOList = customerOrderDao.getOrderList(shop, TableEnum.PES_ORDER.getName(),
				buyerNick, date,sortPageQuery,new HashMap<String, Integer>());
		return orderDTOList;
	}


	@Override
	public List<GoodsDTO> getOrderGoodsSku(ShopCommonParam shop, Long orderId, Long orderTime) {
		return customerOrderDao.selectGoodsDetailInfoByOrderId(shop, "pes_order_goods_sku", 
				 orderId, new Date(orderTime),new Date(orderTime));
	}
    
       
    
}
