package com.pes.jd.business.sub.impl;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.CsServiceEvaluationBusiness;
import com.pes.jd.constants.PerformanceConstans;
import com.pes.jd.constants.SubTable;
import com.pes.jd.dao.sub.CsServiceEvaluationDao;
import com.pes.jd.model.DTO.CsServiceEvaluationDTO;
import com.pes.jd.util.AppContext;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/12 1:13 PM
 * @since 1.0.0
 */
@SuppressWarnings("Duplicates")
@Service
public class CsServiceEvaluationBusinessImpl implements CsServiceEvaluationBusiness {
    @Autowired
    private CsServiceEvaluationDao csServiceEvaluationDao;

    @Override
    public List<Map<String, Object>> csEvaluateDetail(Date startDate, Date endDate, String shopId, String nick) {
        Map<String,Object> param = Maps.newHashMapWithExpectedSize(8);
        param.put("begin",startDate);
        param.put("end",endDate);
        param.put("shopId",shopId);
        param.put("nick",nick);
        param.put("tableName", CommonUtils.getTableName(
                AppContext.currentContext().getSchema(), SubTable.PES_CS_SERVICE_EVALUATION.getName(),shopId));
        return csServiceEvaluationDao.selectByDateAndShopNick(param);
    }

    @Override
    public List<CsServiceEvaluationDTO> searchAllDateShopGroup(
            Long shopId, Set<String> nicks, Date startDate, Date endDate, String queryType, String schemaId, Set<Date> filterDates) {
        List<CsServiceEvaluationDTO> csServiceEvaluationDTOList = csServiceEvaluationDao.searchAllDateShopGroup(
                shopId, nicks, startDate, endDate, queryType, schemaId,filterDates
        );
        final String date = "date";
        if (Objects.equals(date,queryType)){
            //如果是日期则无需聚合
            return csServiceEvaluationDTOList;
        }
        return CommonUtils.polymerize(csServiceEvaluationDTOList,
                this::getInitObject, CsServiceEvaluationDTO::getCsNick,(x, y)-> x.setCsNick(y.getCsNick()),x->x.setShopId(shopId), null, PerformanceConstans.IGNORE_PROPERTIES);
    }

    private CsServiceEvaluationDTO getInitObject() {
        CsServiceEvaluationDTO dto = new CsServiceEvaluationDTO();
        dto.setId(0L);
        dto.setShopId(0L);
        dto.setCsNick(StringUtils.EMPTY);
        dto.setDate(null);
        dto.setMedalNum(0);
        dto.setVerySatisfiedNum(0);
        dto.setSatisfiedNum(0);
        dto.setGeneralNum(0);
        dto.setDissatisfiedNum(0);
        dto.setVeryDissatisfiedNum(0);
        return dto;
    }
}
