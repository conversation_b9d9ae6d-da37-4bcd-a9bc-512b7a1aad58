package com.pes.jd.business.sub.impl;

import com.pes.jd.business.sub.ChatClassifyBusiness;
import com.pes.jd.dao.sub.ChatClassifyDao;
import com.pes.jd.model.DO.CsServiceEvaluationDetail;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DO.CsChatlog;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.entity.ChatClassify;
import com.pes.jd.util.DateUtil;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementation of ChatClassifyBusiness
 * Provides classify statistics by date, grouping and summarizing chat classifications
 */
@Service
public class ChatClassifyBusinessImpl implements ChatClassifyBusiness {

    private static final Logger logger = LoggerFactory.getLogger(ChatClassifyBusinessImpl.class);

    // Initialize the category set directly with all categories
    private static final List<String> CATEGORY_SET = Arrays.asList(
            "尺码推荐", "商品信息咨询", "物流方式", "换货问题", "问候类", "商品推荐",
            "商品价格咨询", "催退款", "下单问题", "订单咨询", "退货规则咨询", "催发货", "售后咨询",
            "优惠咨询", "商品品质问题", "运费险问题", "退款流程咨询", "退差价", "赠品问题",
            "快递问题", "补贴", "国补", "优惠咨询", "补差价", "转人工", "以旧换新", "安装方式", "返现", "其他"
    );

    @Autowired
    private ChatClassifyDao chatClassifyDao;

    /**
     * Query classify statistics by shop and date range
     * Returns a list of maps with classify type and count, sorted by count in descending order
     * The 'total' entry participates in the ranking based on its count value
     *
     * @param shop shop common parameter containing shop ID
     * @param startDate start date of the current query range (inclusive)
     * @param endDate end date of the current query range (inclusive)
     * @param lastStartDate start date of the previous query range (inclusive)
     * @param lastEndDate end date of the previous query range (inclusive)
     * @return list of maps with classify, count, and countLast keys, sorted by count in descending order
     */
    @Override
    public List<Map<String, Object>> queryClassifyStatsByDateRange(ShopCommonParam shop, Date startDate, Date endDate,
                                                                  Date lastStartDate, Date lastEndDate) {
        // Get the raw data from the DAO for the current date range
        List<ChatClassify> rawClassifyList = chatClassifyDao.queryClassifyByShopAndDateRange(shop, startDate, endDate);

        // Get the raw data from the DAO for the previous date range
        List<ChatClassify> lastRawClassifyList = chatClassifyDao.queryClassifyByShopAndDateRange(shop, lastStartDate, lastEndDate);

        // Group by classify type and count occurrences for current period
        Map<String, Long> classifyCounts = rawClassifyList.stream()
                .collect(Collectors.groupingBy(ChatClassify::getClassify, Collectors.counting()));

        // Group by classify type and count occurrences for previous period
        Map<String, Long> lastClassifyCounts = lastRawClassifyList.stream()
                .collect(Collectors.groupingBy(ChatClassify::getClassify, Collectors.counting()));

        // Calculate the total count for current period
        long totalCount = classifyCounts.values().stream().mapToLong(Long::longValue).sum();

        // Calculate the total count for previous period
        long lastTotalCount = lastClassifyCounts.values().stream().mapToLong(Long::longValue).sum();

        // Create result map to store all categories
        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        // Add all categories from the current period
        for (Map.Entry<String, Long> entry : classifyCounts.entrySet()) {
            String classifyType = entry.getKey();

            // Skip empty classify
            if (classifyType == null || classifyType.isEmpty()) {
                continue;
            }

            // Only include categories in the predefined list
            if (!CATEGORY_SET.contains(classifyType) && !"total".equals(classifyType)) {
                continue;
            }

            Map<String, Object> statsMap = new HashMap<>(3);
            statsMap.put("classify", classifyType);
            statsMap.put("count", entry.getValue());
            statsMap.put("countLast", lastClassifyCounts.getOrDefault(classifyType, 0L));

            resultMap.put(classifyType, statsMap);
        }

        // Add categories from the previous period that are not in the current period
        for (Map.Entry<String, Long> entry : lastClassifyCounts.entrySet()) {
            String classifyType = entry.getKey();

            // Skip empty classify
            if (classifyType == null || classifyType.isEmpty()) {
                continue;
            }

            // Only include categories in the predefined list
            if (!CATEGORY_SET.contains(classifyType) && !"total".equals(classifyType)) {
                continue;
            }

            // Skip if already added from current period
            if (resultMap.containsKey(classifyType)) {
                continue;
            }

            Map<String, Object> statsMap = new HashMap<>(3);
            statsMap.put("classify", classifyType);
            statsMap.put("count", 0L);
            statsMap.put("countLast", entry.getValue());

            resultMap.put(classifyType, statsMap);
        }

        // Add the total entry
        Map<String, Object> totalMap = new HashMap<>(3);
        totalMap.put("classify", "total");
        totalMap.put("count", totalCount);
        totalMap.put("countLast", lastTotalCount);
        resultMap.put("total", totalMap);

        // Convert map to list
        List<Map<String, Object>> result = new ArrayList<>(resultMap.values());

        // Custom comparator that:
        // 1. Places "total" at the top
        // 2. Places "其他" at the bottom
        // 3. Sorts everything else by count in descending order
        result.sort((a, b) -> {
            String classifyA = (String) a.get("classify");
            String classifyB = (String) b.get("classify");

            // If one is "total", it comes first
            if ("total".equals(classifyA)) return -1;
            if ("total".equals(classifyB)) return 1;

            // If one is "其他", it comes last
            if ("其他".equals(classifyA)) return 1;
            if ("其他".equals(classifyB)) return -1;

            // Otherwise, sort by count in descending order
            Long countA = (Long) a.get("count");
            Long countB = (Long) b.get("count");
            return countB.compareTo(countA);
        });

        return result;
    }

    /**
     * Query classify statistics by shop and date range
     * Returns a list of maps with classify type and count, sorted by count in descending order
     * The 'total' entry participates in the ranking based on its count value
     *
     * @param shop shop common parameter containing shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @return list of maps with classify and count keys, sorted by count in descending order
     */
    @Override
    public List<Map<String, Object>> queryClassifyStatsByDateRange(ShopCommonParam shop, Date startDate, Date endDate) {
        // Get the raw data from the DAO for the date range
        List<ChatClassify> rawClassifyList = chatClassifyDao.queryClassifyByShopAndDateRange(shop, startDate, endDate);

        // Group by classify type and count occurrences
        Map<String, Long> classifyCounts = rawClassifyList.stream()
                .collect(Collectors.groupingBy(ChatClassify::getClassify, Collectors.counting()));

        // Calculate the total count
        long totalCount = classifyCounts.values().stream().mapToLong(Long::longValue).sum();

        // Create result list with capacity for all entries plus total
        List<Map<String, Object>> result = new ArrayList<>();

        // Convert the map entries to the result format
        classifyCounts.forEach((classifyType, count) -> {
            // Skip empty classify
            if (classifyType == null || classifyType.isEmpty()) {
                return;
            }

            // Only include categories in the predefined list
            if (!CATEGORY_SET.contains(classifyType) && !"total".equals(classifyType)) {
                return;
            }

            Map<String, Object> statsMap = new HashMap<>(3);
            statsMap.put("classify", classifyType);
            statsMap.put("count", count);
            statsMap.put("countLast", 0L);
            result.add(statsMap);
        });

        // Add the total entry to be included in sorting
        Map<String, Object> totalMap = new HashMap<>(3);
        totalMap.put("classify", "total");
        totalMap.put("count", totalCount);
        totalMap.put("countLast", 0L);
        result.add(totalMap);

        // Custom comparator that:
        // 1. Places "total" at the top
        // 2. Places "其他" at the bottom
        // 3. Sorts everything else by count in descending order
        result.sort((a, b) -> {
            String classifyA = (String) a.get("classify");
            String classifyB = (String) b.get("classify");

            // If one is "total", it comes first
            if ("total".equals(classifyA)) return -1;
            if ("total".equals(classifyB)) return 1;

            // If one is "其他", it comes last
            if ("其他".equals(classifyA)) return 1;
            if ("其他".equals(classifyB)) return -1;

            // Otherwise, sort by count in descending order
            Long countA = (Long) a.get("count");
            Long countB = (Long) b.get("count");
            return countB.compareTo(countA);
        });

        return result;
    }

    /**
     * Query classify statistics by shop and date
     * Returns a list of maps with only classify type and count, sorted by count in descending order
     *
     * @param shop shop common parameter containing shop ID
     * @param date date to query
     * @return list of maps with classify and count keys only, sorted by count in descending order
     * @deprecated Use queryClassifyStatsByDateRange instead
     */
    @Override
    public List<Map<String, Object>> queryClassifyStatsByDate(ShopCommonParam shop, Date date) {
        // Use the date range method with the same date for start and end
        return queryClassifyStatsByDateRange(shop, date, date);
    }

    /**
     * Query classify statistics by shop, date and specific classify type
     * Returns summary statistics for the specified classify type
     * including total count and tokens
     */
    @Override
    public List<ChatClassify> queryClassifyStatsByType(ShopCommonParam shop, Date date, String classify) {
        return chatClassifyDao.queryClassifyByShopAndClassify(shop, date, classify);
    }

    /**
     * Query low-rated evaluations and their corresponding chat classifications
     * Returns a list of maps containing evaluation details and chat classification data
     * for evaluations with eval_code less than 100 within the specified date range
     *
     * @param shop shop common parameter containing shop ID
     * @param startDate start date of the evaluation query range (inclusive)
     * @param endDate end date of the evaluation query range (inclusive)
     * @return list of maps with evaluation details and chat classification data
     */
    @Override
    public List<Map<String, Object>> queryLowRatedEvaluationsWithClassify(ShopCommonParam shop, Date startDate, Date endDate) {
        // Get the current date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        // Calculate the date 30 days ago for chat classify search
        calendar.add(Calendar.DAY_OF_MONTH, -30);
        Date thirtyDaysAgo = calendar.getTime();

        // Get low-rated evaluations
        List<CsServiceEvaluationDetail> lowRatedEvals = chatClassifyDao.queryLowRatedEvaluationsByDateRange(
                shop, DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate));
        logger.info("lowRatedEvals: {}", lowRatedEvals);
        // Extract SIDs from evaluations
        List<String> sids = lowRatedEvals.stream()
                .map(CsServiceEvaluationDetail::getSid)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // If no SIDs found, return empty list
        if (sids.isEmpty()) {
            return Collections.emptyList();
        }

        // Get chat classifications for these SIDs
        List<ChatClassify> chatClassifies = chatClassifyDao.queryClassifyBySidsAndDateRange(
                shop, thirtyDaysAgo, endDate, sids);

        // Create a map of SID to ChatClassify for quick lookup
        Map<String, ChatClassify> sidToClassifyMap = chatClassifies.stream()
                .collect(Collectors.toMap(
                        ChatClassify::getSid,
                        classify -> classify,
                        (existing, replacement) -> existing // Keep first entry in case of duplicates
                ));

        // Combine the data
        List<Map<String, Object>> result = new ArrayList<>();
        for (CsServiceEvaluationDetail eval : lowRatedEvals) {
            Map<String, Object> item = new HashMap<>();

            // Add chat classify details if available
            ChatClassify classify = sidToClassifyMap.get(eval.getSid());
            if (classify != null && classify.getClassifyExtra() != null) {
                // Add evaluation details
                item.put("id", eval.getId());
                item.put("shopId", eval.getShopId());
                item.put("csNick", eval.getCsNick());
                item.put("buyerNick", eval.getBuyerNick());
                item.put("evalTime", eval.getEvalTime());
                item.put("evalCode", eval.getEvalCode());
                item.put("sid", eval.getSid());
                item.put("classifyExtra", classify.getClassifyExtra());
                item.put("classify", classify.getClassify());
                result.add(item);
            }
        }

        return result;
    }


    @Override
    public Map<LocalDate, Map<Integer, Map<String,Integer>>> queryLowRatedEvaluationsWithClassifyV2(ShopCommonParam shop, Date startDate, Date endDate) {
        //客户事后越想越气，不能忍了，层皮烂谷子的事也要差评一下
        Date thirtyDaysAgo = cn.hutool.core.date.DateUtil.offsetDay(startDate,-30);
        List<CsServiceEvaluationDetail> lowRatedEvals = chatClassifyDao.queryLowRatedEvaluationsByDateRange(shop, DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate));
        List<String> sids = lowRatedEvals.stream()
                .map(CsServiceEvaluationDetail::getSid)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (sids.isEmpty()) {
            return Collections.emptyMap();
        }
        List<ChatClassify> chatClassifies = chatClassifyDao.queryClassifyBySidsAndDateRange(shop, thirtyDaysAgo, endDate, sids);

        //sid与ChatClassify的map映射
        Map<String, List<ChatClassify>> sidToClassifyMap = chatClassifies.stream().collect(Collectors.groupingBy(ChatClassify::getSid));

        // 数据组装 按照日期-评分-分类集合进行组装
        Map<LocalDate, Map<Integer, List<ChatClassify>>> result = new HashMap<>();
        for (CsServiceEvaluationDetail eval : lowRatedEvals) {
            LocalDate evalDate = cn.hutool.core.date.DateUtil.toLocalDateTime(eval.getEvalTime()).toLocalDate();
            Integer evalCode = eval.getEvalCode();

            result.putIfAbsent(evalDate, new HashMap<>());
            result.get(evalDate).putIfAbsent(evalCode, new ArrayList<>());

            if (sidToClassifyMap.containsKey(eval.getSid())) {
                result.get(evalDate).get(evalCode).addAll(sidToClassifyMap.get(eval.getSid()));
            }
        }

        //数据组装2  对最里面的分类的二级意图进行聚合并返回对应的会话数量
        Map<LocalDate, Map<Integer, Map<String,Integer>>> result2 = new HashMap<>();
        for (Map.Entry<LocalDate, Map<Integer, List<ChatClassify>>> entry : result.entrySet()) {
            LocalDate date = entry.getKey();
            result2.putIfAbsent(date, new HashMap<>());
            for (Map.Entry<Integer, List<ChatClassify>> evalEntry : entry.getValue().entrySet()) {
                Integer evalCode = evalEntry.getKey();
                result2.get(date).putIfAbsent(evalCode, new HashMap<>());
                List<ChatClassify> chatClassifiesForEval = evalEntry.getValue();
                Map<String, List<ChatClassify>> groupedByExtra = chatClassifiesForEval.stream()
                        .collect(Collectors.groupingBy(
                                classify1 -> classify1.getClassifyExtra() != null ? classify1.getClassifyExtra() : "未分类",
                                Collectors.toList()
                        ));
                for (Map.Entry<String, List<ChatClassify>> evalEntry2 : groupedByExtra.entrySet()) {
                    String classifyExtra = evalEntry2.getKey();
                    List<ChatClassify> classifyList = evalEntry2.getValue();
                    Set<String> uniqueSids = classifyList.stream()
                            .map(ChatClassify::getSid)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                    int sidCount = uniqueSids.size();
                    if (sidCount == 0) {
                        continue;
                    }
                    ChatClassify chatClassify = classifyList.get(0);
                    result2.get(date).get(evalCode).putIfAbsent(chatClassify.getClassify() + ":" + classifyExtra, sidCount);
                }
            }
        }
        return result2;
    }




    @Override
    public List<Map<String, Object>> queryClassifyByShopAndDateRangeAndClassify(ShopCommonParam shop, Date startDate, Date endDate, String classify) {
        logger.info("Querying chat classifications by shop: {}, date range: {} to {}, and classify: {}",
                shop, startDate, endDate, classify);

        // Call the DAO method to get the raw data
        List<ChatClassify> chatClassifies = chatClassifyDao.queryClassifyByShopAndDateRangeAndClassify(
                shop, startDate, endDate, classify);

        logger.info("Found {} chat classifications", chatClassifies.size());

        // Step 1: Group by classify_extra
        Map<String, List<ChatClassify>> groupedByExtra = chatClassifies.stream()
                .collect(Collectors.groupingBy(
                        classify1 -> classify1.getClassifyExtra() != null ? classify1.getClassifyExtra() : "未分类",
                        Collectors.toList()
                ));

        // Collect all unique SIDs
        Set<String> allUniqueSids = chatClassifies.stream()
                .map(ChatClassify::getSid)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Calculate the date 30 days after endDate for evaluation search
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDate);
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        Date thirtyDaysAfter = calendar.getTime();

        // Create a map to store evaluations by SID
        Map<String, List<CsServiceEvaluationDetail>> evaluationsBySid = new HashMap<>();

        // Batch process SIDs
        int batchSize = 2000;
        List<String> sidList = new ArrayList<>(allUniqueSids);
        for (int i = 0; i < sidList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, sidList.size());
            List<String> batchSids = sidList.subList(i, end);

            // Query evaluations for this batch
            List<CsServiceEvaluationDetail> batchEvaluations = chatClassifyDao.queryAllEvaluationsBySidsAndDateRange(
                    shop, startDate, thirtyDaysAfter, batchSids);

            // Group evaluations by SID
            for (CsServiceEvaluationDetail eval : batchEvaluations) {
                evaluationsBySid.computeIfAbsent(eval.getSid(), k -> new ArrayList<>()).add(eval);
            }
        }

        // Process each classify_extra group
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, List<ChatClassify>> entry : groupedByExtra.entrySet()) {
            String classifyExtra = entry.getKey();
            List<ChatClassify> classifyList = entry.getValue();

            // Get unique SIDs for this group
            Set<String> groupSids = classifyList.stream()
                    .map(ChatClassify::getSid)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            int sidCount = groupSids.size();
            if (sidCount == 0) {
                continue;
            }

            // Collect all evaluations for this group
            List<CsServiceEvaluationDetail> groupEvaluations = groupSids.stream()
                    .filter(evaluationsBySid::containsKey)
                    .flatMap(sid -> evaluationsBySid.get(sid).stream())
                    .collect(Collectors.toList());

            int evalCount = groupEvaluations.size();

            // Count dissatisfied evaluations
            long dissatisfiedCount = groupEvaluations.stream()
                    .filter(eval -> eval.getEvalCode() != null && eval.getEvalCode() < 100)
                    .count();

            // Calculate dissatisfied rate
            double dissatisfiedRate = evalCount > 0 ? (double) dissatisfiedCount / evalCount * 100 : 0;

            // Create result map
            Map<String, Object> statsMap = new HashMap<>();
            statsMap.put("classify", classify);
            statsMap.put("classifyExtra", classifyExtra);
            statsMap.put("sidCount", sidCount);
            statsMap.put("evalCount", evalCount);
            statsMap.put("dissatisfiedCount", dissatisfiedCount);
            statsMap.put("dissatisfiedRate", Math.round(dissatisfiedRate * 100) / 100.0); // Round to 2 decimal places

            result.add(statsMap);
        }
        // Sort by sidCount in descending order
        result.sort((a, b) -> Integer.compare((Integer) b.get("sidCount"), (Integer) a.get("sidCount")));

        return result;
    }

    /**
     * Query chat classifications by shop ID, classify, classifyExtra and date range
     * Returns a list of chat classifications matching the specified criteria
     *
     * @param shop shop common parameter containing shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param classify classification type
     * @param classifyExtra sub-classification type (optional, can be null)
     * @return list of chat classifications matching the criteria
     */
    @Override
    public List<ChatClassify> queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange(ShopCommonParam shop, Date startDate, Date endDate, String classify, String classifyExtra) {
        logger.info("Querying chat classifications by shop ID {}, date range {} to {}, classify {}, classifyExtra {}",
                shop.getShopId(), startDate, endDate, classify, classifyExtra);

        // Call the DAO method to get the raw data
        List<ChatClassify> result = chatClassifyDao.queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange(
                shop, startDate, endDate, classify, classifyExtra);

        logger.info("Found {} chat classifications matching the criteria", result.size());
        return result;
    }


    @Override
    public List<ChatClassify> queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndScore(ShopCommonParam shop, Date startDate, Date endDate, String classify, String classifyExtra,Integer score) {

        //1. 根据评分，日期，shopId找到sid
        List<CsServiceEvaluationDetail> lowRatedEvals = chatClassifyDao.queryLowRatedEvaluationsByDateRangeAndScore(shop, DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate),score);
        List<String> sidList = lowRatedEvals.stream().map(x -> x.getSid()).collect(Collectors.toList());
        //2. 根据sid, classify, classifyExtra ，date查询出符合条件的chatclassify并返回，查询classify得时间可以往后多加30天
        if(sidList.isEmpty()) {
            return Collections.emptyList();
        }
        endDate = cn.hutool.core.date.DateUtil.offsetDay(endDate,30);
        List<ChatClassify> result = chatClassifyDao.queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndSids(
                shop,DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate),classify, classifyExtra,sidList);

        return result;
    }

    /**
     * Query chatlogs by SID and date range
     * First searches for chat sessions matching the SID and date range,
     * then retrieves all chatlogs within the session time range
     *
     * @param shop shop common parameter containing shop ID
     * @param sid session ID to search for
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @return list of chatlogs for the specified session
     */
    @Override
    public List<CsChatlog> queryChatlogsBySidAndDateRange(ShopCommonParam shop, String sid, Date startDate, Date endDate) {
        logger.info("Querying chatlogs by shop ID {}, SID {}, date range {} to {}",
                shop.getShopId(), sid, startDate, endDate);

        // Step 1: Find the chat session by SID and date range
        List<CsChatSessionDO> sessions = chatClassifyDao.queryChatSessionByShopIdAndSidAndDateRange(shop, sid, startDate, endDate);

        if (sessions.isEmpty()) {
            logger.info("No chat sessions found for SID {} in date range {} to {}", sid, startDate, endDate);
            return Collections.emptyList();
        }

        logger.info("Found {} chat sessions for SID {}", sessions.size(), sid);

        // Step 2: For each session, get the chatlogs within the session time range
        List<CsChatlog> allChatlogs = new ArrayList<>();

        for (CsChatSessionDO session : sessions) {
            Date sessionStartTime = session.getSessionBeginTime();
            Date sessionEndTime = session.getSessionEndTime();

            logger.info("Querying chatlogs for session with begin time {} and end time {}",
                    sessionStartTime, sessionEndTime);

            // Get chatlogs for this session's time range
            List<CsChatlog> sessionChatlogs = chatClassifyDao.queryChatlogsByShopIdAndTimeRange(
                    shop, sessionStartTime, sessionEndTime);

            logger.info("Found {} chatlogs for session", sessionChatlogs.size());
            allChatlogs.addAll(sessionChatlogs);
        }

        // Sort chatlogs by time
        allChatlogs.sort(Comparator.comparing(CsChatlog::getTime));

        logger.info("Returning {} total chatlogs for SID {}", allChatlogs.size(), sid);
        return allChatlogs;
    }


    //近7天有XX条不满意评分，归因 XX条
    //近7天有XX条咨询问题已完成意图溯察
    public  Map<String, Object> queryInsightOverView(ShopCommonParam shop, Date startDate, Date endDate){


        List<CsServiceEvaluationDetail> lowRatedEvals = chatClassifyDao.queryLowRatedEvaluationsByDateRange(shop, DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate));
        Integer lowRatedEvalCount = lowRatedEvals.size();

        Map<LocalDate, Map<Integer, Map<String,Integer>>> results = queryLowRatedEvaluationsWithClassifyV2(shop, DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate));
        Map<Integer, Map<String, Integer>> resultMap = new HashMap<>();

        for (Map<Integer, Map<String, Integer>> dayMap : results.values()) {
            for (Map.Entry<Integer, Map<String, Integer>> entry : dayMap.entrySet()) {
                Integer score = entry.getKey();
                Map<String, Integer> attributions = entry.getValue();
                Map<String, Integer> aggregated = resultMap.computeIfAbsent(score, k -> new HashMap<>());
                // 累加归因 key 的值
                for (Map.Entry<String, Integer> attrEntry : attributions.entrySet()) {
                    String attribution = attrEntry.getKey();
                    Integer count = attrEntry.getValue();
                    aggregated.merge(attribution, count, Integer::sum);
                }
            }
        }
        Integer lowRatedEvalCountByClassify = resultMap.values().stream().mapToInt(Map::size).sum();

        //查询时间范围内有多少条会话id
        Integer consultCount  = chatClassifyDao.querySidByDateRange(shop,DateUtil.getStartTimeOfDate(startDate), DateUtil.getEndTimeOfDate(endDate));

        Map<String, Object> result = new HashMap<>();
        result.put("lowRatedEvalCount", lowRatedEvalCount);
        result.put("lowRatedEvalCountByClassify", lowRatedEvalCountByClassify);
        result.put("consultCount", consultCount);
        return result;

    }

}
