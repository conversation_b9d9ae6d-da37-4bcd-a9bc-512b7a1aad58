package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.ReceiveSessionNumHourlyDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/15 7:53 PM
 * @since 1.0.0
 */
public interface ReceiveSessionNumHourlyBusiness {
    List<ReceiveSessionNumHourlyDTO> searchAllDateShopGroup(
            Long shopId,
            Set<String> nicks,
            Date startDate,
            Date endDate,
            String groupBy,
            String schemaId,
            Set<Date> filterDates);
}
