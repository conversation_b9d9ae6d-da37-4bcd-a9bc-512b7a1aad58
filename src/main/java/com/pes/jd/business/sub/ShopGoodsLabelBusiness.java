package com.pes.jd.business.sub;

import com.pes.jd.model.DTO.ShopGoodsLabelDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO;
import com.pes.jd.model.Query.SkuQuery;

import java.util.List;

public interface ShopGoodsLabelBusiness {

    Long insertGoodsLabel(ShopGoodsLabelDTO record, SkuQuery skuQuery);

    int updateGoodLabel(ShopGoodsLabelDTO goodlabel, SkuQuery skuQuery);

    int deleteGoodsLabelAndSkuLabel(Long labelId, SkuQuery skuQuery);

    List<ShopGoodsSkuLabelDTO> searchGoodsSkuByWareid(Long shopId, String schemaId, List<Long> collect);

}
