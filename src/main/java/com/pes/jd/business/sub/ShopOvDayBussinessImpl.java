package com.pes.jd.business.sub;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.dao.sub.ShopOvDayDao;
import com.pes.jd.model.DTO.ShopOrderSaleAmountDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.ms.domain.Data.shopdata.ShopOvDay;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年08月08 15:39:39<br>
 */
@Service
public class ShopOvDayBussinessImpl implements  ShopOvDayBussiness {
    @Resource
    private ShopOvDayDao shopOvDayDao;
    @Override
    public List<ShopOvDay> selectShopSaleAmountByShopIdByDate(String schemdId, List<Long> shopIdLst, Date startDate, Date endDate) {
        return shopOvDayDao.selectShopSaleAmountByShopIdByDate(schemdId,shopIdLst,startDate,endDate);
    }

    @Override
    public List<ShopOvDay> selectMultiShopSaleAmount(List<CauseShop> causeShopLst,Date startDate,Date endDate) {
        List<ShopOvDay> shopSaleAmountResultLst= Lists.newArrayList();
        if(CollectionUtils.isEmpty(causeShopLst)){
            return shopSaleAmountResultLst;
        }
        Map<String, List<CauseShop>> schemaIdMap=causeShopLst.stream().collect(Collectors.groupingBy(CauseShop::getSchemaId));
        for (Map.Entry<String, List<CauseShop>> schemaIdEntry : schemaIdMap.entrySet()) {
            String schemaId=schemaIdEntry.getKey();
            List<CauseShop> shopLst=schemaIdEntry.getValue();
            if(CollectionUtils.isEmpty(shopLst)){
                continue;
            }

            List<Long> shopIdLst=shopLst.stream().map(CauseShop::getShopId).collect(Collectors.toList());
            List<ShopOvDay> shopSaleAmountLst=	selectShopSaleAmountByShopIdByDate(schemaId, shopIdLst,startDate,endDate);
            if(CollectionUtils.isEmpty(shopSaleAmountLst)){
                continue;
            }
            shopSaleAmountResultLst.addAll(shopSaleAmountLst);
        }
        return shopSaleAmountResultLst;
    }

    @Override
    public List<ShopOvDay> selectShopOrderSaleAmountByShopIdByDate(String schemdId, Set<Long> shopIdSet, Date date) {
        return shopOvDayDao.selectShopOrderSaleAmountByShopIdByDate(schemdId,shopIdSet,date);
    }

    @Override
    public List<ShopOrderSaleAmountDTO> selectMultiShopOrderSaleAmount(List<ShopUrge> causeShopLst, Date startDate, Date endDate) {
        List<ShopOrderSaleAmountDTO> shopSaleAmountResultLst= Lists.newArrayList();
        List<Date> dates= DateUtils.splitDate(startDate,endDate);
        if(CollectionUtils.isEmpty(dates)||CollectionUtils.isEmpty(causeShopLst)){
            return shopSaleAmountResultLst;
        }
        Map<String, List<ShopUrge>> schemaIdMap=causeShopLst.stream().collect(Collectors.groupingBy(ShopUrge::getSchemaId));
        for (Map.Entry<String, List<ShopUrge>> schemaIdEntry : schemaIdMap.entrySet()) {
            String schemaId=schemaIdEntry.getKey();
            List<ShopUrge> shopLst=schemaIdEntry.getValue();
            if(CollectionUtils.isEmpty(shopLst)){
                continue;
            }

            Set<Long> shopIdLst=shopLst.stream().map(ShopUrge::getShopId).collect(Collectors.toSet());
            List<ShopOrderSaleAmountDTO> shopSaleAmountLst=  collectShopOrderAmountLst(schemaId,shopIdLst,dates);
           if(CollectionUtils.isNotEmpty(shopSaleAmountLst)){
               shopSaleAmountResultLst.addAll(shopSaleAmountLst);
           }
        }
        return shopSaleAmountResultLst;
    }

    @Override
    public double selectShopSaleAmountByShopIdByDateNew(ShopCommonParam shopCommonParam, Date date) {
        ShopOvDay saleAmount = shopOvDayDao.selectShopSaleAmountByShopIdByDateNew(shopCommonParam, date);
        if(saleAmount != null && saleAmount.getSaleAmount() != null){
            return saleAmount.getSaleAmount();
        }else{
            Date yesterDay = DateUtils.getDateByPeriod(date, -1);
            saleAmount = shopOvDayDao.selectShopSaleAmountByShopIdByDateNew(shopCommonParam, yesterDay);
            if(saleAmount != null && saleAmount.getSaleAmount() != null){
                return saleAmount.getSaleAmount();
            }
        }
        return 0.0;
    }

    private List<ShopOrderSaleAmountDTO> collectShopOrderAmountLst(String schemaId,Set<Long> shopIdSet,List<Date> dates){
        List<ShopOrderSaleAmountDTO> shopSaleAmountResultLst= Lists.newArrayList();
        ShopOrderSaleAmountDTO sos=null;
        for (Date date : dates) {
            List<ShopOvDay> shopSaleAmountLst=	selectShopOrderSaleAmountByShopIdByDate(schemaId, shopIdSet,date);
            Map<Long, ShopOvDay> shopOrderAmountMap= Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(shopSaleAmountLst)){
                shopOrderAmountMap=shopSaleAmountLst.stream().collect(Collectors.toMap(ShopOvDay::getShopId,c->c));
            }
            for (Long shopId : shopIdSet) {
                Double shopOrderAmount=0.0;
                Double generalOrderAmount=0.0;
                Double reserveOrderAmount=0.0;
                Double presaleOrderAmount=0.0;
                sos=new ShopOrderSaleAmountDTO();
                sos.setShopId(shopId);
                sos.setDate(date);
                if(shopOrderAmountMap.get(shopId)!=null){
                    ShopOvDay sod=shopOrderAmountMap.get(shopId);
                    shopOrderAmount=sod.getSaleAmount();
                    reserveOrderAmount=sod.getSaleAmountPreordain();
                    presaleOrderAmount=sod.getSaleAmountPresale();
                    generalOrderAmount=sod.getSaleAmount()>0? sod.getSaleAmount()-sod.getSaleAmountPreordain()-sod.getSaleAmountPresale():0.0;
                }
                sos.setShopOrderAmount(shopOrderAmount);
                sos.setGeneralOrderAmount(generalOrderAmount);
                sos.setPresaleOrderAmount(presaleOrderAmount);
                sos.setReserveOrderAmount(reserveOrderAmount);
                shopSaleAmountResultLst.add(sos);
            }
        }
        return shopSaleAmountResultLst;
    }

}
