package com.pes.jd.business.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.business.sub.ChatAnalysisBusiness;
import com.pes.jd.dao.sub.CsChatlogDao;
import com.pes.jd.dao.sub.CsChatpeerDao;
import com.pes.jd.model.DTO.ChatLogDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.VO.ReceiveBuyerVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**  
 * 聊天分析 - 业务类
 * ClassName:ChatAnalysisBusinessImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午2:04:44 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class ChatAnalysisBusinessImpl implements ChatAnalysisBusiness {

	@Autowired
	private CsChatpeerDao  csChatpeerDao;
	@Autowired
	private CsChatlogDao csChatlogDao;

	@Override
	public List<String> searchReceiveChatpeerLst(ShopCommonParam shop, Date startDate, Date endDate,List<UserQuery> csLst,
			String buyerNickKeyword, String keyword) {
		if(StringUtils.isBlank(keyword)){
			return csChatpeerDao.selectReceiveChatpeerLst(shop,csLst,startDate, endDate, buyerNickKeyword);
		}else{
			return csChatpeerDao.selectCsBuyerChatpeersByParamFromChatlog(shop, csLst, buyerNickKeyword, startDate, endDate, keyword);
		}
	}
	
	@Override
	public List<ChatLogDTO> searchBuyerChatlogs(ShopCommonParam shop, List<String> csNick, String buyerNick, String keyWord,String sid,
			Date startDate, Date endDate){
		return csChatlogDao.selectBuyerChatlogs(shop, csNick, buyerNick, keyWord, sid,startDate, endDate);

	}

	@Override
	public List<String> searchReceiveNotEmptyChatLogByChatPeerLst(ShopCommonParam shop, Date startDate, Date endDate,
			String csNick, List<String> receiveBuyerLst) {
		if(CollectionUtils.isEmpty(receiveBuyerLst)){
			return Lists.newArrayList();
		}
		return csChatlogDao.searchReceiveNotEmptyChatLogByChatPeerLst(shop,startDate,endDate,csNick,receiveBuyerLst);
	}

	@Override
	public List<ReceiveBuyerVO> searchReceiveNotEmptyChatLogVOByChatPeerLst(ShopCommonParam shop, Date startDate, Date endDate,
																			String csNick, List<String> receiveBuyerLst) {
		if(CollectionUtils.isEmpty(receiveBuyerLst)){
			return Lists.newArrayList();
		}
		return csChatlogDao.searchReceiveNotEmptyChatLogVOByChatPeerLst(shop,startDate,endDate,csNick,receiveBuyerLst);
	}

	@Override
	public List<ChatLogDTO> searchCsChatlogs(ShopCommonParam shop, String csNick,
			List<String> buyerLst, Date startDate, Date endDate) {
		return csChatlogDao.searchCsChatlogs(shop, csNick, buyerLst, startDate, endDate);
	}

	
}
  
