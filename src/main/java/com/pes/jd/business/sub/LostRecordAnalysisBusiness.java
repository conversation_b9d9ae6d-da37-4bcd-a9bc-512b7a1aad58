package com.pes.jd.business.sub;

import com.pes.jd.model.JSON.EnquiryOrderLossVO;
import com.pes.jd.model.Param.LossOrderParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.EnquiryLostVO;
import com.pes.jd.model.VO.SilenceLostRecord;

import java.util.Date;
import java.util.List;

/**  
 * ClassName:LostRecordAnalysisBusiness <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月19日 下午1:37:17 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface LostRecordAnalysisBusiness {

	List<SilenceLostRecord> searchSilentOrderLostRecordLst(LossOrderParam lossOrderParam, Date startDate, Date endDate, OrderInfoLogUploadParam orderInfoLogUploadParam);

	EnquiryLostVO searchEnquiryLostRecordLst(LossOrderParam lossOrderParam, Date startDate, Date endDate,
                                             Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds);

	List<EnquiryOrderLossVO> searchEnquiryOrderLostRecordLst(LossOrderParam lossOrderParam, Date startDate,
                                                             Date endDate, OrderInfoLogUploadParam orderInfoLogUploadParam);

    EnquiryLostVO searchEnquiryLostRecordLstOfSpu(LossOrderParam lossOrderParam, Date startDate, Date endDate, Integer chatLimitNum, SortPageQuery sortPageQuery, Integer sessionDuration, String skuIds);

}
  
