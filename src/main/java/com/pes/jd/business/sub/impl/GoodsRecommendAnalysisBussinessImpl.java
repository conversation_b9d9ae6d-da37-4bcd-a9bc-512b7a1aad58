package com.pes.jd.business.sub.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.annotation.DBLog;
import com.pes.jd.annotation.OrderLog;
import com.pes.jd.business.sub.GoodsRecommendAnalysisBussiness;
import com.pes.jd.business.sub.ShopGoodsInfoBussiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.sub.CsRecommendGoodsDao;
import com.pes.jd.dao.sub.GoodsRecommendSummaryDao;
import com.pes.jd.dao.sub.OrderDetailDao;
import com.pes.jd.dao.sub.ShopGoodSkuDao;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.GoodsPurchaseEnum;
import com.pes.jd.model.Param.GoodsRecommedParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCategoryTree;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.CsRecommendGoodsDetailVO;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.model.VO.GoodsRecommendSummaryVO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.office.excel.ExportExcel;
import com.pes.jd.office.param.ExeclColumnParam;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Service
public class GoodsRecommendAnalysisBussinessImpl implements GoodsRecommendAnalysisBussiness {
    private static final Logger logger = LoggerFactory.getLogger(GoodsRecommendAnalysisBussinessImpl.class);

    @Autowired
    private GoodsRecommendSummaryDao goodsRecommendSummaryDao;

    @Autowired
    private CsRecommendGoodsDao csRecommendGoodsDao;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private ShopGoodSkuDao shopGoodSkuDao;

    @Autowired
    private ShopGoodsInfoBussiness shopGoodsInfoBussiness;
    /**
     * 推荐汇总
     */
    @Override
    public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(ShopCommonParam shop,
                                                                                                List<String> csNickLst, List<Long> skuLst, Date startDate, Date endDate) {
        List<GoodsRecommendSummaryDTO> result=Lists.newArrayList();
        List<GoodsRecommendSummaryDTO> summaryLst= goodsRecommendSummaryDao.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(shop, csNickLst, skuLst, startDate, endDate);
        GoodsRecommendSummaryDTO gr=null;
        if(CollectionUtils.isNotEmpty(summaryLst)){
            Map<String,List<GoodsRecommendSummaryDTO>> summaryMap=  summaryLst.stream().collect(Collectors.groupingBy(GoodsRecommendSummaryDTO::getCsNick));
            for (Entry<String, List<GoodsRecommendSummaryDTO>> entry : summaryMap.entrySet()) {
                String csNick=entry.getKey();
                List<GoodsRecommendSummaryDTO> values=   entry.getValue();
                if(CollectionUtils.isEmpty(values)){
                    continue;
                }
                gr=new GoodsRecommendSummaryDTO();
                 Integer recommendNum=0;
                 Integer purchasesBuyerNum=0;
                 Integer purchasesGoodsNum=0;
                 Double purchasesAmount=0.0;
                for (GoodsRecommendSummaryDTO summary : values) {
                    recommendNum+=summary.getRecommendNum();
                    purchasesAmount+=summary.getPurchasesAmount();
                    purchasesBuyerNum+=summary.getPurchasesBuyerNum();
                    purchasesGoodsNum+=summary.getPurchasesGoodsNum();
                }
                gr.setShopId(shop.getShopId());
                gr.setCsNick(csNick);
                gr.setPurchasesAmount(purchasesAmount);
                gr.setPurchasesBuyerNum(purchasesBuyerNum);
                gr.setPurchasesGoodsNum(purchasesGoodsNum);
                gr.setRecommendNum(recommendNum);
                result.add(gr);
            }
        }
        return result;
    }

    @Override
    public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2(ShopCommonParam shop,
                                                                                                  List<String> csNickLst, List<Long> skuLst, Long categoryId, Date startDate, Date endDate) throws IllegalAccessException {
        List<GoodsRecommendSummaryDTO> result = Lists.newArrayList();
        List<Long> categoryIds = new ArrayList<>();
        if (categoryId != null) {
            categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, categoryId);
        }
        List<GoodsRecommendSummaryDTO> summaryLst = goodsRecommendSummaryDao.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2(shop, csNickLst, skuLst, categoryIds, startDate, endDate);

        GoodsRecommendSummaryDTO gr = null;
        if (CollectionUtils.isNotEmpty(summaryLst)) {
            Map<String, List<GoodsRecommendSummaryDTO>> summaryMap = summaryLst.stream().collect(Collectors.groupingBy(GoodsRecommendSummaryDTO::getCsNick));
            for (Entry<String, List<GoodsRecommendSummaryDTO>> entry : summaryMap.entrySet()) {
                String csNick = entry.getKey();
                List<GoodsRecommendSummaryDTO> values = entry.getValue();
                if (CollectionUtils.isEmpty(values)) {
                    continue;
                }
                gr = new GoodsRecommendSummaryDTO();
                Integer recommendNum = 0;
                Integer purchasesBuyerNum = 0;
                Integer purchasesGoodsNum = 0;
                Double purchasesAmount = 0.0;
                for (GoodsRecommendSummaryDTO summary : values) {
                    recommendNum += summary.getRecommendNum();
                    purchasesAmount += summary.getPurchasesAmount();
                    purchasesBuyerNum += summary.getPurchasesBuyerNum();
                    purchasesGoodsNum += summary.getPurchasesGoodsNum();
                }
                gr.setShopId(shop.getShopId());
                gr.setCsNick(csNick);
                gr.setPurchasesAmount(purchasesAmount);
                gr.setPurchasesBuyerNum(purchasesBuyerNum);
                gr.setPurchasesGoodsNum(purchasesGoodsNum);
                gr.setRecommendNum(recommendNum);
                result.add(gr);
            }
        }
        return result;
    }

    /**
     * /**
     * 推荐分析
     */
    @Override
    public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryByDateByShopIdByCsNick(ShopCommonParam shop,
                                                                                            GoodsRecommedParam param) {
        List<GoodsRecommendSummaryDTO> result=Lists.newArrayList();
        List<GoodsRecommendSummaryDTO> summaryLst=goodsRecommendSummaryDao.selectGoodsRecommendSummaryByDateByShopIdByCsNick(shop, param);

        if(CollectionUtils.isNotEmpty(summaryLst)){
            List<ShopGoodsSkuDTO> goodsLst= shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shop,summaryLst.stream().map(GoodsRecommendSummaryDTO::getSkuId).collect(Collectors.toSet()));
            Map<Long,String> skuNameMap=Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(goodsLst)){
                skuNameMap= goodsLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId,ShopGoodsSkuDTO::getSkuName));
            }
            Map<Long,List<GoodsRecommendSummaryDTO>> skuIdMap=summaryLst.stream().collect(Collectors.groupingBy(GoodsRecommendSummaryDTO::getSkuId));
            for (Entry<Long, List<GoodsRecommendSummaryDTO>> entry : skuIdMap.entrySet()) {
                if(CollectionUtils.isEmpty(entry.getValue())){
                    continue;
                }
                List<GoodsRecommendSummaryDTO> recommendSummaryLst=entry.getValue();
                GoodsRecommendSummaryDTO summaryDTO=new GoodsRecommendSummaryDTO();
                summaryDTO.setSkuId(entry.getKey());
                summaryDTO.setSkuName(skuNameMap.get(entry.getKey()));
                 Integer recommendNum=0;
                 Integer purchasesBuyerNum=0;
                 Integer purchasesGoodsNum=0;
                 Double purchasesAmount=0.0;
                for (GoodsRecommendSummaryDTO rs : recommendSummaryLst) {
                    recommendNum+=rs.getRecommendNum();
                    purchasesBuyerNum+=rs.getPurchasesBuyerNum();
                    purchasesGoodsNum+=rs.getPurchasesGoodsNum();
                    purchasesAmount+=rs.getPurchasesAmount();
                }
                summaryDTO.setPurchasesAmount(purchasesAmount);
                summaryDTO.setPurchasesBuyerNum(purchasesBuyerNum);
                summaryDTO.setPurchasesGoodsNum(purchasesGoodsNum);
                summaryDTO.setRecommendNum(recommendNum);
                result.add(summaryDTO);
            }
            result.sort(Comparator.comparing(GoodsRecommendSummaryDTO::getSkuId));
        }
        return result;
    }

    /**
     * 推荐明细详情
     */
    @Override
    public DataAnalysisVO<CsRecommendGoodsDTO> selectCsRecommendGoodsDetail(ShopCommonParam shop, GoodsRecommedParam param, SortPageQuery query) {
    	DataAnalysisVO<CsRecommendGoodsDTO> dataAnalysisVO = new DataAnalysisVO<CsRecommendGoodsDTO>();
        List<CsRecommendGoodsDTO> csRecommendGoodsLst = Lists.newArrayList();
        Integer count = csRecommendGoodsDao.selectCsRecommendCount(shop, param);
        if (count > CommonConstants.FRONT_END_DIVIDE_COUNT) {
            query.setCurrentPage(query.getCurrentPage() * query.getSize());
            query.setSort(true);
            if (StringUtils.isNotBlank(query.getPropertity())) {
                if (!query.getPropertity().equals("date")) {
                	query.setField("date");
                }else{
                	query.setField(query.getPropertity());
                }
            }
            dataAnalysisVO.setCount(count);
            dataAnalysisVO.setPageFlag(false);
        } else {
            query.setSort(false);
            query.setCurrentPage(0L);
            query.setSize(0L);
            dataAnalysisVO.setCount(count);
            dataAnalysisVO.setPageFlag(true);
        }
			List<CsRecommendGoodsDTO> csRecommendLst = csRecommendGoodsDao.selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLst(shop, param, query);

        //当天咨询当天下单
        if (CollectionUtils.isNotEmpty(csRecommendLst)) {
            // 查询店铺skuId
            Set<Long> skuIds = csRecommendLst.stream().map(CsRecommendGoodsDTO::getSkuId).collect(Collectors.toSet());
            List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByDateByCustomerBySkuLst(shop,
                    param.getCustomer(), skuIds, param.getStartDate(), param.getEndDate());
            Map<String, List<OrderDetailDTO>> buyerOrderDetailMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderDetailLst)) {
                buyerOrderDetailMap = orderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getBuyerNick));
            }
            List<ShopGoodsSkuDTO> goodsSkuLst = shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shop, skuIds);
            Map<Long, String> skuMap = null;
            if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
                skuMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, g -> g.getSkuName()));
            }
            Map<String, List<CsRecommendGoodsDTO>> buyerMap = csRecommendLst.stream().collect(Collectors.groupingBy(CsRecommendGoodsDTO::getCustomer));
            for (Entry<String, List<CsRecommendGoodsDTO>> entry : buyerMap.entrySet()) {
                String buyerNick = entry.getKey();
                List<CsRecommendGoodsDTO> csBuyerRecommedLst = entry.getValue();
                if (CollectionUtils.isEmpty(csBuyerRecommedLst)) {
                    continue;
                }
                for (CsRecommendGoodsDTO csRecommend : csBuyerRecommedLst) {
                    Date date = DateUtils.getStartTimeOfDate(csRecommend.getDate());
                    if (skuMap != null) {
                        if (skuMap.get(csRecommend.getSkuId()) != null) {
                            csRecommend.setSkuName(skuMap.get(csRecommend.getSkuId()));
                        }
                    }
                    csRecommend.setPurchasesAmount(0.0);
                    csRecommend.setPurchasesGoodsNum(0);
                    List<OrderDetailDTO> buyerOrderLst = buyerOrderDetailMap.get(buyerNick);
                    if (csRecommend.getResult().equals(GoodsPurchaseEnum.DEAL.getType())) {
                        // 一天同一个买家买同一个商品下多单
                        if (CollectionUtils.isNotEmpty(buyerOrderLst)) {
                            Double purchasesAmount = 0.0;
                            Integer purchasesNum = 0;
                            for (OrderDetailDTO orderDetail : buyerOrderLst) {
                                Date createDate = DateUtils.getStartTimeOfDate(orderDetail.getCreated());
                                if (orderDetail.getItemSkuId().equals(String.valueOf(csRecommend.getSkuId()))
                                        && date.compareTo(createDate) == 0) {
                                    double jdPrice = orderDetail.getItemNum() * orderDetail.getItemPrice();
                                    double sellerRate = orderDetail.getTotalFee() == null ? 0
                                            : orderDetail.getTotalFee() > 0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
                                    double saleAmount = jdPrice - (orderDetail.getSellerDiscount() == null ? 0
                                            : sellerRate * orderDetail.getSellerDiscount());
                                    purchasesAmount += saleAmount;
                                    purchasesNum += orderDetail.getItemNum();
                                }
                            }
                            csRecommend.setPurchasesAmount(purchasesAmount);
                            csRecommend.setPurchasesGoodsNum(purchasesNum);
                        }
                    }
                }
                csRecommendGoodsLst.addAll(csBuyerRecommedLst);
            }
        }

        if (StringUtils.isBlank(query.getField())) {
            csRecommendGoodsLst.sort(Comparator.comparing(CsRecommendGoodsDTO::getDate));
        }

        dataAnalysisVO.setDataList(csRecommendGoodsLst);
        return dataAnalysisVO;
    }

    @Override
    public DataAnalysisVO<CsRecommendGoodsDTO> selectCsRecommendGoodsDetailV2(ShopCommonParam shop, GoodsRecommedParam param, SortPageQuery query) throws IllegalAccessException {
        DataAnalysisVO<CsRecommendGoodsDTO> dataAnalysisVO = new DataAnalysisVO<CsRecommendGoodsDTO>();
        List<CsRecommendGoodsDTO> csRecommendGoodsLst = Lists.newArrayList();
        if (param.getCategoryId() != null) {
            List<Long> categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shop, param.getCategoryId());
            param.setCatgoryLst(categoryIds);
        }
        Integer count = csRecommendGoodsDao.selectCsRecommendCountV2(shop, param);
        if (count > CommonConstants.FRONT_END_DIVIDE_COUNT) {
            query.setCurrentPage(query.getCurrentPage() * query.getSize());
            query.setSort(true);
            if (StringUtils.isNotBlank(query.getPropertity())) {
                if (!query.getPropertity().equals("date")) {
                    query.setField("date");
                } else {
                    query.setField(query.getPropertity());
                }
            }
            dataAnalysisVO.setCount(count);
            dataAnalysisVO.setPageFlag(false);
        } else {
            query.setSort(false);
            query.setCurrentPage(0L);
            query.setSize(0L);
            dataAnalysisVO.setCount(count);
            dataAnalysisVO.setPageFlag(true);
        }

        List<CsRecommendGoodsDTO> csRecommendLst = csRecommendGoodsDao.selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLstV2(shop, param, query);

        //当天咨询当天下单
        if (CollectionUtils.isNotEmpty(csRecommendLst)) {
            // 查询店铺skuId
            Set<Long> skuIds = csRecommendLst.stream().map(CsRecommendGoodsDTO::getSkuId).collect(Collectors.toSet());
            List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByDateByCustomerBySkuLst(shop,
                    param.getCustomer(), skuIds, param.getStartDate(), param.getEndDate());
            Map<String, List<OrderDetailDTO>> buyerOrderDetailMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderDetailLst)) {
                buyerOrderDetailMap = orderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getBuyerNick));
            }
            List<ShopGoodsSkuDTO> goodsSkuLst = shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shop, skuIds);
            Map<Long, String> skuMap = null;
            if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
                skuMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, g -> g.getSkuName()));
            }
            Map<String, List<CsRecommendGoodsDTO>> buyerMap = csRecommendLst.stream().collect(Collectors.groupingBy(CsRecommendGoodsDTO::getCustomer));
            for (Entry<String, List<CsRecommendGoodsDTO>> entry : buyerMap.entrySet()) {
                String buyerNick = entry.getKey();
                List<CsRecommendGoodsDTO> csBuyerRecommedLst = entry.getValue();
                if (CollectionUtils.isEmpty(csBuyerRecommedLst)) {
                    continue;
                }
                for (CsRecommendGoodsDTO csRecommend : csBuyerRecommedLst) {
                    Date date = DateUtils.getStartTimeOfDate(csRecommend.getDate());
                    if (skuMap != null) {
                        if (skuMap.get(csRecommend.getSkuId()) != null) {
                            csRecommend.setSkuName(skuMap.get(csRecommend.getSkuId()));
                        }
                    }
                    csRecommend.setPurchasesAmount(0.0);
                    csRecommend.setPurchasesGoodsNum(0);
                    List<OrderDetailDTO> buyerOrderLst = buyerOrderDetailMap.get(buyerNick);
                    if (csRecommend.getResult().equals(GoodsPurchaseEnum.DEAL.getType())) {
                        // 一天同一个买家买同一个商品下多单
                        if (CollectionUtils.isNotEmpty(buyerOrderLst)) {
                            Double purchasesAmount = 0.0;
                            Integer purchasesNum = 0;
                            for (OrderDetailDTO orderDetail : buyerOrderLst) {
                                Date createDate = DateUtils.getStartTimeOfDate(orderDetail.getCreated());
                                if (orderDetail.getItemSkuId().equals(String.valueOf(csRecommend.getSkuId()))
                                        && date.compareTo(createDate) == 0) {
                                    double jdPrice = orderDetail.getItemNum() * orderDetail.getItemPrice();
                                    double sellerRate = orderDetail.getTotalFee() == null ? 0
                                            : orderDetail.getTotalFee() > 0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
                                    double saleAmount = jdPrice - (orderDetail.getSellerDiscount() == null ? 0
                                            : sellerRate * orderDetail.getSellerDiscount());
                                    purchasesAmount += saleAmount;
                                    purchasesNum += orderDetail.getItemNum();
                                }
                            }
                            csRecommend.setPurchasesAmount(purchasesAmount);
                            csRecommend.setPurchasesGoodsNum(purchasesNum);
                        }
                    }
                }
                csRecommendGoodsLst.addAll(csBuyerRecommedLst);
            }
        }

        if (StringUtils.isBlank(query.getField())) {
            csRecommendGoodsLst.sort(Comparator.comparing(CsRecommendGoodsDTO::getDate));
        }

        dataAnalysisVO.setDataList(csRecommendGoodsLst);
        return dataAnalysisVO;
    }

    @Override
    public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickOfSpu(ShopCommonParam shop, List<String> csNickLst, List<Long> wareIds, Date startDate, Date endDate) {
        List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
        List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        return goodsRecommendSummaryDao.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(shop, csNickLst, skuLst, startDate, endDate);
    }

    @Override
    public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryByDateByShopIdByCsNickOfSpu(ShopCommonParam shop, GoodsRecommedParam param) {
        List<Long> wareIds = param.getSkuLst();
        if (CollectionUtils.isNotEmpty(wareIds)) {
            List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
            param.setSkuLst(shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList()));
        }
        List<GoodsRecommendSummaryDTO> result=Lists.newArrayList();
        List<GoodsRecommendSummaryDTO> summaryLst=goodsRecommendSummaryDao.selectGoodsRecommendSummaryByDateByShopIdByCsNick(shop, param);
        if(CollectionUtils.isNotEmpty(summaryLst)){
            List<ShopGoodsSkuDTO> goodsLst= shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shop,summaryLst.stream().map(GoodsRecommendSummaryDTO::getSkuId).collect(Collectors.toSet()));
            Map<Long,String> skuNameMap=Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(goodsLst)){
                skuNameMap= goodsLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId,ShopGoodsSkuDTO::getSkuName));
            }
            Map<Long,List<GoodsRecommendSummaryDTO>> skuIdMap=summaryLst.stream().collect(Collectors.groupingBy(GoodsRecommendSummaryDTO::getSkuId));
            for (Entry<Long, List<GoodsRecommendSummaryDTO>> entry : skuIdMap.entrySet()) {
                if(CollectionUtils.isEmpty(entry.getValue())){
                    continue;
                }
                List<GoodsRecommendSummaryDTO> recommendSummaryLst=entry.getValue();
                GoodsRecommendSummaryDTO summaryDTO=new GoodsRecommendSummaryDTO();
                summaryDTO.setSkuId(entry.getKey());
                summaryDTO.setSkuName(skuNameMap.get(entry.getKey()));
                Integer recommendNum=0;
                Integer purchasesBuyerNum=0;
                Integer purchasesGoodsNum=0;
                Double purchasesAmount=0.0;
                for (GoodsRecommendSummaryDTO rs : recommendSummaryLst) {
                    recommendNum+=rs.getRecommendNum();
                    purchasesBuyerNum+=rs.getPurchasesBuyerNum();
                    purchasesGoodsNum+=rs.getPurchasesGoodsNum();
                    purchasesAmount+=rs.getPurchasesAmount();
                }
                summaryDTO.setPurchasesAmount(purchasesAmount);
                summaryDTO.setPurchasesBuyerNum(purchasesBuyerNum);
                summaryDTO.setPurchasesGoodsNum(purchasesGoodsNum);
                summaryDTO.setRecommendNum(recommendNum);
                result.add(summaryDTO);
            }
            result.sort(Comparator.comparing(GoodsRecommendSummaryDTO::getSkuId));
        }
        return result;
    }

    /**
     * 推荐明细详情 of spu
     */
    @Override
    public DataAnalysisVO<CsRecommendGoodsDTO> selectCsRecommendGoodsDetailOfSpu(ShopCommonParam shop, GoodsRecommedParam param, SortPageQuery query) {
        List<Long> wareIds = param.getSkuLst();
        if (CollectionUtils.isNotEmpty(wareIds)) {
            List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shop, wareIds);
            List<Long> skuLst = shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
            param.setSkuLst(skuLst);
        }
        DataAnalysisVO<CsRecommendGoodsDTO> dataAnalysisVO = new DataAnalysisVO<>();
        List<CsRecommendGoodsDTO> csRecommendGoodsLst = Lists.newArrayList();
        Integer count = csRecommendGoodsDao.selectCsRecommendCount(shop, param);
        if (count > CommonConstants.FRONT_END_DIVIDE_COUNT) {
            query.setCurrentPage(query.getCurrentPage() * query.getSize());
            query.setSort(true);
            if (StringUtils.isNotBlank(query.getPropertity())) {
                if (!query.getPropertity().equals("date")) {
                    query.setField("date");
                } else {
                    query.setField(query.getPropertity());
                }
            }
            dataAnalysisVO.setCount(count);
            dataAnalysisVO.setPageFlag(false);
        } else {
            query.setSort(false);
            query.setCurrentPage(0L);
            query.setSize(0L);
            dataAnalysisVO.setCount(count);
            dataAnalysisVO.setPageFlag(true);
        }
        List<CsRecommendGoodsDTO> csRecommendLst = csRecommendGoodsDao.selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLst(shop, param, query);

        //当天咨询当天下单
        if (CollectionUtils.isNotEmpty(csRecommendLst)) {
            // 查询店铺skuId
            Set<Long> skuIds = csRecommendLst.stream().map(CsRecommendGoodsDTO::getSkuId).collect(Collectors.toSet());
            List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByDateByCustomerBySkuLst(shop,
                    param.getCustomer(), skuIds, param.getStartDate(), param.getEndDate());
            Map<String, List<OrderDetailDTO>> buyerOrderDetailMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(orderDetailLst)) {
                buyerOrderDetailMap = orderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getBuyerNick));
            }
            List<ShopGoodsSkuDTO> goodsSkuLst = shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shop, skuIds);
            Map<Long, String> skuMap = null;
            if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
                skuMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, ShopGoodsSkuDTO::getSkuName));
            }
            Map<String, List<CsRecommendGoodsDTO>> buyerMap = csRecommendLst.stream().collect(Collectors.groupingBy(CsRecommendGoodsDTO::getCustomer));
            for (Entry<String, List<CsRecommendGoodsDTO>> entry : buyerMap.entrySet()) {
                String buyerNick = entry.getKey();
                List<CsRecommendGoodsDTO> csBuyerRecommedLst = entry.getValue();
                if (CollectionUtils.isEmpty(csBuyerRecommedLst)) {
                    continue;
                }
                for (CsRecommendGoodsDTO csRecommend : csBuyerRecommedLst) {
                    Date date = DateUtils.getStartTimeOfDate(csRecommend.getDate());
                    if (skuMap != null) {
                        if (skuMap.get(csRecommend.getSkuId()) != null) {
                            csRecommend.setSkuName(skuMap.get(csRecommend.getSkuId()));
                        }
                    }
                    csRecommend.setPurchasesAmount(0.0);
                    csRecommend.setPurchasesGoodsNum(0);
                    List<OrderDetailDTO> buyerOrderLst = buyerOrderDetailMap.get(buyerNick);
                    if (csRecommend.getResult().equals(GoodsPurchaseEnum.DEAL.getType())) {
                        // 一天同一个买家买同一个商品下多单
                        if (CollectionUtils.isNotEmpty(buyerOrderLst)) {
                            Double purchasesAmount = 0.0;
                            Integer purchasesNum = 0;
                            for (OrderDetailDTO orderDetail : buyerOrderLst) {
                                Date createDate = DateUtils.getStartTimeOfDate(orderDetail.getCreated());
                                if (orderDetail.getItemSkuId().equals(String.valueOf(csRecommend.getSkuId()))
                                        && date.compareTo(createDate) == 0) {
                                    double jdPrice = orderDetail.getItemNum() * orderDetail.getItemPrice();
                                    double sellerRate = orderDetail.getTotalFee() == null ? 0
                                            : orderDetail.getTotalFee() > 0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
                                    double saleAmount = jdPrice - (orderDetail.getSellerDiscount() == null ? 0
                                            : sellerRate * orderDetail.getSellerDiscount());
                                    purchasesAmount += saleAmount;
                                    purchasesNum += orderDetail.getItemNum();
                                }
                            }
                            csRecommend.setPurchasesAmount(purchasesAmount);
                            csRecommend.setPurchasesGoodsNum(purchasesNum);
                        }
                    }
                }
                csRecommendGoodsLst.addAll(csBuyerRecommedLst);
            }
        }

        if (StringUtils.isBlank(query.getField())) {
            csRecommendGoodsLst.sort(Comparator.comparing(CsRecommendGoodsDTO::getDate));
        }

        dataAnalysisVO.setDataList(csRecommendGoodsLst);

        if (CollectionUtils.isNotEmpty(csRecommendGoodsLst)) {
            List<CsDTO> csLst = param.getCsLst();
            if (CollectionUtils.isNotEmpty(csLst)) {
                Map<String, String> simpleNameMap = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
                for (CsRecommendGoodsDTO recommd : csRecommendGoodsLst) {
                    recommd.setCsSimpleNick(simpleNameMap.get(recommd.getCsNick().toLowerCase()));
                }
            }
        }
        return dataAnalysisVO;
    }

    /**
     * 成交订单详情
     */
    @Override
    @OrderLog(resultType = List.class,paramType = OrderDetailDTO.class)
    @DBLog(value = "selectDealOrderDetailLst")
    public List<OrderDetailDTO> selectDealOrderDetailLst(ShopCommonParam shop, String customer, String skuId, Date startDate, OrderInfoLogUploadParam orderInfoLogUploadParamStr) {
        Set<Long> skuSet = Sets.newHashSet();
        skuSet.add(Long.valueOf(skuId));
//        Date endDate = DateUtil.getEndTimeOfDate(startDate);
        Date endDate = DateUtil.getDateByPeriod(startDate, 3);
        List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByDateByCustomerBySkuLst(shop, customer, skuSet, startDate, endDate);
        for (OrderDetailDTO orderDetail : orderDetailLst) {
        	//购买商品销售额
			double jdPrice = orderDetail.getItemNum() * (double) orderDetail.getItemPrice();
			double sellerRate = orderDetail.getTotalFee() == null ? 0 : orderDetail.getTotalFee() > 0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
			double saleAmount = jdPrice - (orderDetail.getSellerDiscount()==null ? 0 : sellerRate*orderDetail.getSellerDiscount());
			orderDetail.setItemPrice(saleAmount);
			//判断是否为货到付款类型
			if(orderDetail.getPayTime()==null){
				orderDetail.setPayTypeFlag(true);
			}
		}
        return orderDetailLst;
    }


    private Double DecimalFormatForDouble(Double value) {
        DecimalFormat df = new DecimalFormat("#.00");
        String str = df.format(value);
        return Double.valueOf(str);
    }

    /**
     * 推荐汇总导出
     *
     * @throws Exception
     */
    @Override
    public void exportGoodsRecommendSummary(OutputStream out, String jsonParam) throws Exception {
        logger.info("开始导出客服推荐汇总记录...");
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        Map<String, String> csSimpleMap = JSONObject.parseObject(jsonObject.getString("csSimpleNickMap"), new TypeReference<Map<String, String>>() {});
        List<Long> skuLst = (List<Long>) JSONArray.parseArray(jsonObject.getString("skuLst"), Long.class);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);

        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());

        /************确定维度 start ***************/
        List<Long> newSkuLst =getSkuIdsByDimension(jsonObject,shopCommonParam,skuLst);
        /************确定维度 end ***************/

        List<GoodsRecommendSummaryDTO> result=Lists.newArrayList();

        List<GoodsRecommendSummaryDTO> goodsRecommendSummaryDTOs = goodsRecommendSummaryDao.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(shopCommonParam, shopQuery.getCsNickLst(), newSkuLst, startDate, endDate);

        GoodsRecommendSummaryDTO gr=null;
        if(CollectionUtils.isNotEmpty(goodsRecommendSummaryDTOs)){
            Map<String,List<GoodsRecommendSummaryDTO>> summaryMap=  goodsRecommendSummaryDTOs.stream().collect(Collectors.groupingBy(GoodsRecommendSummaryDTO::getCsNick));
            for (Entry<String, List<GoodsRecommendSummaryDTO>> entry : summaryMap.entrySet()) {
                String csNick=entry.getKey();
                List<GoodsRecommendSummaryDTO> values=   entry.getValue();
                if(CollectionUtils.isEmpty(values)){
                    continue;
                }
                gr=new GoodsRecommendSummaryDTO();
                Integer recommendNum=0;
                Integer purchasesBuyerNum=0;
                Integer purchasesGoodsNum=0;
                Double purchasesAmount=0.0;
                for (GoodsRecommendSummaryDTO summary : values) {
                    recommendNum+=summary.getRecommendNum();
                    purchasesAmount+=summary.getPurchasesAmount();
                    purchasesBuyerNum+=summary.getPurchasesBuyerNum();
                    purchasesGoodsNum+=summary.getPurchasesGoodsNum();
                }
                gr.setShopId(shopCommonParam.getShopId());
                gr.setCsNick(csNick);
                gr.setPurchasesAmount(purchasesAmount);
                gr.setPurchasesBuyerNum(purchasesBuyerNum);
                gr.setPurchasesGoodsNum(purchasesGoodsNum);
                gr.setRecommendNum(recommendNum);
                result.add(gr);
            }
        }
        List<GoodsRecommendSummaryVO> voLst = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(result)) {
            for (GoodsRecommendSummaryDTO summary : result) {
                GoodsRecommendSummaryVO vo = new GoodsRecommendSummaryVO();
                vo.setCsNick(summary.getCsNick());
                vo.setCsSimpleNick(csSimpleMap.get(summary.getCsNick()));
                vo.setRecommendNum(summary.getRecommendNum());
                vo.setPurchasesAmount(summary.getPurchasesAmount());
                vo.setPurchasesBuyerNum(summary.getPurchasesBuyerNum());
                vo.setPurchasesGoodsNum(summary.getPurchasesGoodsNum());
                vo.setPurchasesSuccessPercent((summary.getRecommendNum() > 0 ? DecimalFormatForDouble(summary.getPurchasesBuyerNum() / Double.valueOf(summary.getRecommendNum()) * 100.0) : 0.0));
                voLst.add(vo);
            }
        }

        ExeclTableParam<GoodsRecommendSummaryVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();

        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("推荐客户数", "recommendNum"));
        columnParams.add(new ExeclColumnParam("购买人数", "purchasesBuyerNum"));
        columnParams.add(new ExeclColumnParam("推荐成功率(%)", "purchasesSuccessPercent"));
        columnParams.add(new ExeclColumnParam("购买件数", "purchasesGoodsNum"));
        columnParams.add(new ExeclColumnParam("购买金额(元)", "purchasesAmount", "%.2f"));

        String title = "客服推荐汇总";
        ExportExcel exportExcel = new ExportExcel();
        exportExcel.execlExport(title, tableParam, out);
        logger.info("导出客服推荐汇总记录结束 num:{}...", voLst.size());
    }


    /**
     * 推荐汇总导出
     *
     * @throws Exception
     */
    @Override
    public void exportGoodsRecommendSummaryV2(OutputStream out, String jsonParam) throws Exception {
        logger.info("开始导出客服推荐汇总记录...");
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        Map<String, String> csSimpleMap = JSONObject.parseObject(jsonObject.getString("csSimpleNickMap"), new TypeReference<Map<String, String>>() {
        });
        List<Long> skuLst = (List<Long>) JSONArray.parseArray(jsonObject.getString("skuLst"), Long.class);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        String categoryId = jsonObject.getString("categoryId");
        Long categoryIdtemp = null;
        if (StringUtils.isNotBlank(categoryId)) {
            categoryIdtemp = Long.valueOf(categoryId);
        }

        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());

        /************确定维度 start ***************/
        List<Long> newSkuLst = getSkuIdsByDimension(jsonObject, shopCommonParam, skuLst);
        /************确定维度 end ***************/

        List<GoodsRecommendSummaryDTO> result = Lists.newArrayList();

        List<Long> categoryIds = new ArrayList<>();
        if (categoryIdtemp != null) {
            categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shopCommonParam, categoryIdtemp);
        }
        List<GoodsRecommendSummaryDTO> goodsRecommendSummaryDTOs = goodsRecommendSummaryDao.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2(shopCommonParam, shopQuery.getCsNickLst(), newSkuLst, categoryIds, startDate, endDate);


        GoodsRecommendSummaryDTO gr = null;
        if (CollectionUtils.isNotEmpty(goodsRecommendSummaryDTOs)) {
            Map<String, List<GoodsRecommendSummaryDTO>> summaryMap = goodsRecommendSummaryDTOs.stream().collect(Collectors.groupingBy(GoodsRecommendSummaryDTO::getCsNick));
            for (Entry<String, List<GoodsRecommendSummaryDTO>> entry : summaryMap.entrySet()) {
                String csNick = entry.getKey();
                List<GoodsRecommendSummaryDTO> values = entry.getValue();
                if (CollectionUtils.isEmpty(values)) {
                    continue;
                }
                gr = new GoodsRecommendSummaryDTO();
                Integer recommendNum = 0;
                Integer purchasesBuyerNum = 0;
                Integer purchasesGoodsNum = 0;
                Double purchasesAmount = 0.0;
                for (GoodsRecommendSummaryDTO summary : values) {
                    recommendNum += summary.getRecommendNum();
                    purchasesAmount += summary.getPurchasesAmount();
                    purchasesBuyerNum += summary.getPurchasesBuyerNum();
                    purchasesGoodsNum += summary.getPurchasesGoodsNum();
                }
                gr.setShopId(shopCommonParam.getShopId());
                gr.setCsNick(csNick);
                gr.setPurchasesAmount(purchasesAmount);
                gr.setPurchasesBuyerNum(purchasesBuyerNum);
                gr.setPurchasesGoodsNum(purchasesGoodsNum);
                gr.setRecommendNum(recommendNum);
                result.add(gr);
            }
        }
        List<GoodsRecommendSummaryVO> voLst = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(result)) {
            for (GoodsRecommendSummaryDTO summary : result) {
                GoodsRecommendSummaryVO vo = new GoodsRecommendSummaryVO();
                vo.setCsNick(summary.getCsNick());
                vo.setCsSimpleNick(csSimpleMap.get(summary.getCsNick()));
                vo.setRecommendNum(summary.getRecommendNum());
                vo.setPurchasesAmount(summary.getPurchasesAmount());
                vo.setPurchasesBuyerNum(summary.getPurchasesBuyerNum());
                vo.setPurchasesGoodsNum(summary.getPurchasesGoodsNum());
                vo.setPurchasesSuccessPercent((summary.getRecommendNum() > 0 ? DecimalFormatForDouble(summary.getPurchasesBuyerNum() / Double.valueOf(summary.getRecommendNum()) * 100.0) : 0.0));
                voLst.add(vo);
            }
        }

        ExeclTableParam<GoodsRecommendSummaryVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();

        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("推荐客户数", "recommendNum"));
        columnParams.add(new ExeclColumnParam("购买人数", "purchasesBuyerNum"));
        columnParams.add(new ExeclColumnParam("推荐成功率(%)", "purchasesSuccessPercent"));
        columnParams.add(new ExeclColumnParam("购买件数", "purchasesGoodsNum"));
        columnParams.add(new ExeclColumnParam("购买金额(元)", "purchasesAmount", "%.2f"));

        String title = "客服推荐汇总";
        ExportExcel exportExcel = new ExportExcel();
        exportExcel.execlExport(title, tableParam, out);
        logger.info("导出客服推荐汇总记录结束 num:{}...", voLst.size());
    }




    /****根据维度查询对应的sku***/
    private List<Long> getSkuIdsByDimension(JSONObject jsonObject, ShopCommonParam shopCommonParam, List<Long> skuLst) {
        Byte dimension = jsonObject.getByte("dimension");
        //获取spu维度下所有的sku
        if (dimension != null && dimension == 2) {//spu维度
            List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shopCommonParam, skuLst);
            return shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList());
        }
        return skuLst;
    }


    /**
     * 推荐明细导出
     *
     * @throws Exception
     */
    @Override
    public void exportGoodsRecommendDetail(OutputStream out, String jsonParam) throws Exception {
        logger.info("开始导出客服推荐明细记录...");
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        String customer = jsonObject.getString("customer");
        String result = jsonObject.getString("result");
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);

        List<Long> skuLst = (List<Long>) JSONArray.parseArray(jsonObject.getString("skuLst"), Long.class);
        Map<String, String> csSimpleMap = JSONObject.parseObject(jsonObject.getString("csSimpleNickMap"), new TypeReference<Map<String, String>>() {
        });
        List<CsRecommendGoodsDetailVO> voLst = Lists.newArrayList();
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());
        GoodsRecommedParam param = new GoodsRecommedParam();
        param.setCsNickLst(shopQuery.getCsNickLst());
        param.setCustomer(customer);
        param.setResult(result);

        /************确定维度 start ***************/
        setParamDimension(jsonObject, shopCommonParam, skuLst, param);
        /************确定维度 end ***************/

        SortPageQuery sortPageQuery = new SortPageQuery();
        sortPageQuery.setSize(-1L);
        List<Date> dates=DateUtils.splitDate(startDate, endDate);
        for (Date date : dates) {
			Date sDate=DateUtils.getStartTimeOfDate(date);
			Date eDate=DateUtils.getEndTimeOfDate(date);
			param.setStartDate(sDate);
		    param.setEndDate(eDate);
        List<CsRecommendGoodsDTO> csRecommendLst = csRecommendGoodsDao.selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLst(shopCommonParam, param, sortPageQuery);
        if (CollectionUtils.isNotEmpty(csRecommendLst)) {
			// 查询店铺skuId
			Set<Long> skuIds = csRecommendLst.stream().map(CsRecommendGoodsDTO::getSkuId).collect(Collectors.toSet());
			List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByDateByCustomerBySkuLst(shopCommonParam,
					param.getCustomer(), skuIds, param.getStartDate(), param.getEndDate());
			Map<String, List<OrderDetailDTO>> buyerOrderDetailMap=Maps.newHashMap();
			if(CollectionUtils.isNotEmpty(orderDetailLst)){
				buyerOrderDetailMap= orderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getBuyerNick));
			}
			List<ShopGoodsSkuDTO> goodsSkuLst = shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shopCommonParam, skuIds);
			Map<Long, String> skuMap = null;
			if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
				skuMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, g -> g.getSkuName()));
			}
			Map<String, List<CsRecommendGoodsDTO>> buyerMap=	csRecommendLst.stream().collect(Collectors.groupingBy(CsRecommendGoodsDTO::getCustomer));
			for (Entry<String, List<CsRecommendGoodsDTO>> entry : buyerMap.entrySet()) {
				String buyerNick=entry.getKey();
				List<CsRecommendGoodsDTO> csBuyerRecommedLst=entry.getValue();
				if(CollectionUtils.isEmpty(csBuyerRecommedLst)){
					continue;
				}
				for (CsRecommendGoodsDTO csRecommend : csBuyerRecommedLst) {
				if (skuMap != null) {
					if (skuMap.get(csRecommend.getSkuId()) != null) {
						csRecommend.setSkuName(skuMap.get(csRecommend.getSkuId()));
					}
				}
				csRecommend.setPurchasesAmount(0.0);
				csRecommend.setPurchasesGoodsNum(0);
				List<OrderDetailDTO> buyerOrderLst=	buyerOrderDetailMap.get(buyerNick);
					if (csRecommend.getResult().equals(GoodsPurchaseEnum.DEAL.getType())) {
						// 一天同一个买家买同一个商品下多单
						if(CollectionUtils.isNotEmpty(buyerOrderLst)){
							Double purchasesAmount = 0.0;
							Integer purchasesNum = 0;
							for (OrderDetailDTO orderDetail : buyerOrderLst) {
								if (orderDetail.getItemSkuId().equals(String.valueOf(csRecommend.getSkuId()))) {
									double jdPrice = orderDetail.getItemNum() * orderDetail.getItemPrice();
									double sellerRate = orderDetail.getTotalFee() == null ? 0
											: orderDetail.getTotalFee() > 0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
									double saleAmount = jdPrice - (orderDetail.getSellerDiscount() == null ? 0
											: sellerRate * orderDetail.getSellerDiscount());
									purchasesAmount += saleAmount;
									purchasesNum += orderDetail.getItemNum();
								}
							}
							csRecommend.setPurchasesAmount(purchasesAmount);
							csRecommend.setPurchasesGoodsNum(purchasesNum);
						}
					}

                    CsRecommendGoodsDetailVO vo = new CsRecommendGoodsDetailVO();
		                vo.setDate(csRecommend.getDate());
		                vo.setCustomer(csRecommend.getCustomer());
		                vo.setCsNick(csRecommend.getCsNick());
		                vo.setCsSimpleNick(csSimpleMap.get(csRecommend.getCsNick()));
		                vo.setSkuId(csRecommend.getSkuId());
		                vo.setSkuName(csRecommend.getSkuName());
		                vo.setPurchasesAmount(csRecommend.getPurchasesAmount());
		                vo.setPurchasesGoodsNum(csRecommend.getPurchasesGoodsNum());
		                vo.setResult(csRecommend.getResult() == 1 ? GoodsPurchaseEnum.DEAL.getName() : GoodsPurchaseEnum.NODEAL.getName());
		                voLst.add(vo);
				}
			}
        }
        }
        if(CollectionUtils.isNotEmpty(voLst)){
        	voLst.sort(Comparator.comparing(CsRecommendGoodsDetailVO::getDate));
		}
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        ExeclTableParam<CsRecommendGoodsDetailVO> tableParam = new ExeclTableParam<CsRecommendGoodsDetailVO>();
        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("日期", "date", 1));
        columnParams.add(new ExeclColumnParam("顾客昵称", "customer"));
        columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
        columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("推荐结果", "result"));
        columnParams.add(new ExeclColumnParam("购买件数", "purchasesGoodsNum"));
        columnParams.add(new ExeclColumnParam("购买金额(元)", "purchasesAmount", "%.2f"));

        String title = "客服推荐明细";
        ExportExcel exort = new ExportExcel();
        exort.execlExport(title, tableParam, out);
        logger.info("导出客服推荐明细记录结束 num:{}...", voLst.size());
    }


    /**
     * 推荐明细导出
     *
     * @throws Exception
     */
    @Override
    public void exportGoodsRecommendDetailV2(OutputStream out, String jsonParam) throws Exception {
        logger.info("开始导出客服推荐明细记录...");
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));
        String customer = jsonObject.getString("customer");
        String result = jsonObject.getString("result");
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        String categoryId = jsonObject.getString("categoryId");
        Long categoryIdtemp = null;
        if (StringUtils.isNotBlank(categoryId)) {
            categoryIdtemp = Long.valueOf(categoryId);
        }

        List<Long> skuLst = (List<Long>) JSONArray.parseArray(jsonObject.getString("skuLst"), Long.class);
        Map<String, String> csSimpleMap = JSONObject.parseObject(jsonObject.getString("csSimpleNickMap"), new TypeReference<Map<String, String>>() {
        });
        List<CsRecommendGoodsDetailVO> voLst = Lists.newArrayList();
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());
        GoodsRecommedParam param = new GoodsRecommedParam();
        param.setCsNickLst(shopQuery.getCsNickLst());
        param.setCustomer(customer);
        param.setResult(result);
        List<Long> categoryIds = new ArrayList<>();
        if (categoryIdtemp != null) {
            categoryIds = shopGoodsInfoBussiness.selectShopAllSubCategory(shopCommonParam, categoryIdtemp);
            param.setCatgoryLst(categoryIds);
        }

        /************确定维度 start ***************/
        setParamDimension(jsonObject, shopCommonParam, skuLst, param);
        /************确定维度 end ***************/

        SortPageQuery sortPageQuery = new SortPageQuery();
        sortPageQuery.setSize(-1L);
        List<Date> dates = DateUtils.splitDate(startDate, endDate);
        for (Date date : dates) {
            Date sDate = DateUtils.getStartTimeOfDate(date);
            Date eDate = DateUtils.getEndTimeOfDate(date);
            param.setStartDate(sDate);
            param.setEndDate(eDate);
            List<CsRecommendGoodsDTO> csRecommendLst = csRecommendGoodsDao.selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLstV2(shopCommonParam, param, sortPageQuery);

            if (CollectionUtils.isNotEmpty(csRecommendLst)) {
                // 查询店铺skuId
                Set<Long> skuIds = csRecommendLst.stream().map(CsRecommendGoodsDTO::getSkuId).collect(Collectors.toSet());
                List<OrderDetailDTO> orderDetailLst = orderDetailDao.selectOrderGoodsSkuByDateByCustomerBySkuLst(shopCommonParam,
                        param.getCustomer(), skuIds, param.getStartDate(), param.getEndDate());
                Map<String, List<OrderDetailDTO>> buyerOrderDetailMap = Maps.newHashMap();
                if (CollectionUtils.isNotEmpty(orderDetailLst)) {
                    buyerOrderDetailMap = orderDetailLst.stream().collect(Collectors.groupingBy(OrderDetailDTO::getBuyerNick));
                }
                List<ShopGoodsSkuDTO> goodsSkuLst = shopGoodSkuDao.selectShopGoodsSkuByShopIdBySkuId(shopCommonParam, skuIds);
                Map<Long, String> skuMap = null;
                if (CollectionUtils.isNotEmpty(goodsSkuLst)) {
                    skuMap = goodsSkuLst.stream().collect(Collectors.toMap(ShopGoodsSkuDTO::getSkuId, g -> g.getSkuName()));
                }
                Map<String, List<CsRecommendGoodsDTO>> buyerMap = csRecommendLst.stream().collect(Collectors.groupingBy(CsRecommendGoodsDTO::getCustomer));
                for (Entry<String, List<CsRecommendGoodsDTO>> entry : buyerMap.entrySet()) {
                    String buyerNick = entry.getKey();
                    List<CsRecommendGoodsDTO> csBuyerRecommedLst = entry.getValue();
                    if (CollectionUtils.isEmpty(csBuyerRecommedLst)) {
                        continue;
                    }
                    for (CsRecommendGoodsDTO csRecommend : csBuyerRecommedLst) {
                        if (skuMap != null) {
                            if (skuMap.get(csRecommend.getSkuId()) != null) {
                                csRecommend.setSkuName(skuMap.get(csRecommend.getSkuId()));
                            }
                        }
                        csRecommend.setPurchasesAmount(0.0);
                        csRecommend.setPurchasesGoodsNum(0);
                        List<OrderDetailDTO> buyerOrderLst = buyerOrderDetailMap.get(buyerNick);
                        if (csRecommend.getResult().equals(GoodsPurchaseEnum.DEAL.getType())) {
                            // 一天同一个买家买同一个商品下多单
                            if (CollectionUtils.isNotEmpty(buyerOrderLst)) {
                                Double purchasesAmount = 0.0;
                                Integer purchasesNum = 0;
                                for (OrderDetailDTO orderDetail : buyerOrderLst) {
                                    if (orderDetail.getItemSkuId().equals(String.valueOf(csRecommend.getSkuId()))) {
                                        double jdPrice = orderDetail.getItemNum() * orderDetail.getItemPrice();
                                        double sellerRate = orderDetail.getTotalFee() == null ? 0
                                                : orderDetail.getTotalFee() > 0 ? jdPrice / orderDetail.getTotalFee() : 0.0;
                                        double saleAmount = jdPrice - (orderDetail.getSellerDiscount() == null ? 0
                                                : sellerRate * orderDetail.getSellerDiscount());
                                        purchasesAmount += saleAmount;
                                        purchasesNum += orderDetail.getItemNum();
                                    }
                                }
                                csRecommend.setPurchasesAmount(purchasesAmount);
                                csRecommend.setPurchasesGoodsNum(purchasesNum);
                            }
                        }

                        CsRecommendGoodsDetailVO vo = new CsRecommendGoodsDetailVO();
                        vo.setDate(csRecommend.getDate());
                        vo.setCustomer(csRecommend.getCustomer());
                        vo.setCsNick(csRecommend.getCsNick());
                        vo.setCsSimpleNick(csSimpleMap.get(csRecommend.getCsNick()));
                        vo.setSkuId(csRecommend.getSkuId());
                        vo.setSkuName(csRecommend.getSkuName());
                        vo.setPurchasesAmount(csRecommend.getPurchasesAmount());
                        vo.setPurchasesGoodsNum(csRecommend.getPurchasesGoodsNum());
                        vo.setResult(csRecommend.getResult() == 1 ? GoodsPurchaseEnum.DEAL.getName() : GoodsPurchaseEnum.NODEAL.getName());
                        voLst.add(vo);
                    }
                }
            }
        }
        if (CollectionUtils.isNotEmpty(voLst)) {
            voLst.sort(Comparator.comparing(CsRecommendGoodsDetailVO::getDate));
        }
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        ExeclTableParam<CsRecommendGoodsDetailVO> tableParam = new ExeclTableParam<CsRecommendGoodsDetailVO>();
        tableParam.setData(voLst);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("日期", "date", 1));
        columnParams.add(new ExeclColumnParam("顾客昵称", "customer"));
        columnParams.add(new ExeclColumnParam("商品名称", "skuName"));
        columnParams.add(new ExeclColumnParam("商品编号", "skuId"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("推荐结果", "result"));
        columnParams.add(new ExeclColumnParam("购买件数", "purchasesGoodsNum"));
        columnParams.add(new ExeclColumnParam("购买金额(元)", "purchasesAmount", "%.2f"));

        String title = "客服推荐明细";
        ExportExcel exort = new ExportExcel();
        exort.execlExport(title, tableParam, out);
        logger.info("导出客服推荐明细记录结束 num:{}...", voLst.size());
    }

    private void setParamDimension(JSONObject jsonObject, ShopCommonParam shopCommonParam, List<Long> skuLst, GoodsRecommedParam param) {
        Byte dimension = jsonObject.getByte("dimension");
        if (dimension != null && dimension == 2) {//spu维度
            if (CollectionUtils.isNotEmpty(skuLst)) {
                List<ShopGoodsSku> shopGoodsSkus = shopGoodSkuDao.selectShopGoodsSkuByShopIdByWareIds(shopCommonParam, skuLst);
                param.setSkuLst(shopGoodsSkus.stream().map(ShopGoodsSku::getSkuId).collect(toList()));
            }
        } else {
            param.setSkuLst(skuLst);
        }
    }
}
