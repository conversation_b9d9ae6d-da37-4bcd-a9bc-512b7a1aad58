package com.pes.jd.business.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.business.sub.ShopUseAnalysisBussiness;
import com.pes.jd.dao.sub.ShopUseAnalysisDao;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis;
import com.yiyitech.support.task.AsyncTask;
import com.yiyitech.support.task.AsyncTaskUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月10 11:47:47<br>
 */
@Service
public class ShopUseAnalysisBussinessImpl implements ShopUseAnalysisBussiness {
    @Resource
    private ShopUseAnalysisDao shopUseAnalysisDao;
    @Override
    public List<ShopUseAnalysis> selectShopUseAnalysisByShopIdSetByDate(List<CauseShop> causeShopLst, Date startDate, Date endDate) {
        List<ShopUseAnalysis> shopUserConversionLst= Lists.newArrayList();
        if(CollectionUtils.isEmpty(causeShopLst)){
            return shopUserConversionLst;
        }
        Map<String, List<CauseShop>> schemaIdMap=causeShopLst.stream().collect(Collectors.groupingBy(CauseShop::getSchemaId));

        List<AsyncTask> tasks = Lists.newArrayList();
        int taskOrder = 0;
        for (Map.Entry<String, List<CauseShop>> schemaIdEntry : schemaIdMap.entrySet()) {
            AsyncTask asyncTask = new AsyncTask(taskOrder) {
                @Override
                public Object run() {
                    String schemaId = schemaIdEntry.getKey();
                    List<CauseShop> shopLst = schemaIdEntry.getValue();
                    if(CollectionUtils.isNotEmpty(shopLst)){
                        Set<Long> shopIdSet=shopLst.stream().map(CauseShop::getShopId).collect(Collectors.toSet());
                        return shopUseAnalysisDao.selectShopUseAnalysisByShopIdSetByDate(schemaId,shopIdSet,startDate,endDate);
                    }

                    return null;
                }
            };
            tasks.add(asyncTask);
            taskOrder++;
//            String schemaId=schemaIdEntry.getKey();
//            List<CauseShop> shopLst=schemaIdEntry.getValue();
//            if(CollectionUtils.isEmpty(shopLst)){
//                continue;
//            }
//
//            Set<Long> shopIdSet=shopLst.stream().map(CauseShop::getShopId).collect(Collectors.toSet());
//            List<ShopUseAnalysis> converLst=shopUseAnalysisDao.selectShopUseAnalysisByShopIdSetByDate(schemaId,shopIdSet,startDate,endDate);
//            if(CollectionUtils.isEmpty(converLst)){
//                continue;
//            }
//            shopUserConversionLst.addAll(converLst);
        }
        Object[] objects = AsyncTaskUtil.runAll(tasks);
        Object[] var = objects;
        int var1 = var.length;
        for (int var2 = 0; var2 < var1; ++var2){
            List<ShopUseAnalysis> converLst = (List<ShopUseAnalysis>) var[var2];
            if(CollectionUtils.isNotEmpty(converLst)){
                shopUserConversionLst.addAll(converLst);
            }
        }
        return shopUserConversionLst;
    }

    @Override
    public List<Long> selectProblemShopIdLst(String dbName, String schemaId, Date startDate, Date endDate) {
        List<Long> shopIdLst = shopUseAnalysisDao.selectProblemShopIdLst(dbName, schemaId, startDate, endDate);
        return shopIdLst;
    }

}
