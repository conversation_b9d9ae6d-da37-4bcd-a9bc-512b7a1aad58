package com.pes.jd.business.sub.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.pes.jd.annotation.DBLog;
import com.pes.jd.annotation.OrderLog;
import com.pes.jd.business.sub.OrderIndexBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.constants.SubTable;
import com.pes.jd.dao.sub.CsOrderIndexDao;
import com.pes.jd.dao.sub.OrderDao;
import com.pes.jd.model.DO.CsOrderIndex;
import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.DTO.OrderDTO;
import com.pes.jd.model.Param.CommonLogUploadParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.CsOrderIndexVO;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.office.excel.ExportExcelBean;
import com.pes.jd.office.param.ExeclColumnParam;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/19 10:20 PM
 * @since 1.0.0
 */
@Service
public class OrderIndexBusinessImpl implements OrderIndexBusiness {
    @Autowired
    private CsOrderIndexDao csOrderIndexDao;

    @Autowired
    private OrderDao orderDao;

    @Override
    public List<CsOrderIndex> selectByNonPay(Set<String> nicks, String buyerNick, Date startDate, Date endDate,
                                             String shopId) {
        return csOrderIndexDao.selectByNonPay(CommonUtils.getTableName(AppContext.currentContext().getSchema(),
                SubTable.PES_CS_ORDER_INDEX.getName(), shopId), nicks, buyerNick, startDate, endDate);
    }

    @Override
    @OrderLog(resultType = DataAnalysisVO.class, paramType = CsOrderIndexDTO.class)
    @DBLog(value = "assistService")
    public DataAnalysisVO<CsOrderIndexDTO> selectCsOrderIndexByDateByCsNickByBuyerByOrderByType(ShopCommonParam shop,
                                                                                                Date startDate, Date endDate, List<String> csNickList, AssistServiceQuery assistServiceQuery,
                                                                                                SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam) {
        DataAnalysisVO<CsOrderIndexDTO> dataAnalysisVO = new DataAnalysisVO<>();
        Map<String, Integer> tableTotalRecordNumMap = csOrderIndexDao.selectCsOrderIndexByDateByCsNickByBuyerByOrderByTypeCount(shop, csNickList, startDate, endDate, assistServiceQuery);

        Integer count = tableTotalRecordNumMap.get("count");
        //如果数据大于2000,后端分页排序
        if (CommonConstants.FRONT_END_DIVIDE_COUNT < count) {
            sortPageQuery.setCurrentPage(sortPageQuery.getCurrentPage() * sortPageQuery.getSize());
            sortPageQuery.setSort(true);
            if (!Strings.isNullOrEmpty(sortPageQuery.getPropertity())) {
                sortPageQuery.setField("date".equals(sortPageQuery.getPropertity()) ? sortPageQuery.getPropertity() : "date");
                sortPageQuery.setSortDirection(sortPageQuery.getSortDirection());
            } else {
                sortPageQuery.setField("order_created");
                sortPageQuery.setSortDirection("ASC");
            }
            dataAnalysisVO.setPageFlag(false);
        } else {//前端分页排序
            sortPageQuery.setSort(false);
            sortPageQuery.setCurrentPage(0l);
            sortPageQuery.setSize(0l);
            dataAnalysisVO.setPageFlag(true);
        }
        List<CsOrderIndexDTO> csOrderIndexDTOs = csOrderIndexDao.selectCsOrderIndexByDateByCsNickByBuyerByOrderByType(shop, startDate, endDate, csNickList, assistServiceQuery, sortPageQuery, tableTotalRecordNumMap);
        Set<Long> orderIds = csOrderIndexDTOs.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toSet());
        ShopCommonParam param = new ShopCommonParam(shop.getShopId(), shop.getSchemaId(), shop.getDbName());
        List<OrderDTO> orderLst = orderDao.selectFieldsByOrderIds(param, orderIds, "out_stock_time,payment", startDate, endDate);

        for (CsOrderIndexDTO dto : csOrderIndexDTOs) {
            StringBuilder assistType = new StringBuilder();
            if (dto.getIsAssitOrderCreate()) {
                assistType.append("协助下单 ");
            }
            if (dto.getIsAssitOrderPay()) {
                assistType.append("协助付款 ");
            }
            if (dto.getIsAssitOrderInFollowup()) {
                assistType.append("协助跟进 ");
            }
            dto.setAssistType(assistType.toString());

            for (OrderDTO order : orderLst) {
                if (dto.getOrderId().equals(order.getOrderId()) && order.getOutStockTime() != null) {
                    dto.setOutStockTime(new Timestamp(order.getOutStockTime().getTime()));
                    break;
                }
            }
        }


        if (CommonConstants.FRONT_END_DIVIDE_COUNT >= count && Strings.isNullOrEmpty(sortPageQuery.getPropertity())) {
            csOrderIndexDTOs.sort(Comparator.comparing(CsOrderIndexDTO::getOrderCreated));
        }
        dataAnalysisVO.setDataList(csOrderIndexDTOs);
        dataAnalysisVO.setCount(count);
        return dataAnalysisVO;
    }

    /**
     * 协助服务分析导出
     */
    @Override
    public void exportAssistServiceAnalysis(OutputStream out, String jsonParam) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(jsonParam);
        ShopQuery shopQuery = JacksonUtils.json2pojo(jsonObject.getString("shopQuery"), ShopQuery.class);
        ShopCommonParam shopCommonParam = new ShopCommonParam(shopQuery.getShopId(), shopQuery.getSchemaId(), shopQuery.getDbName());
        Date startDate = DateUtil.getStartTimeOfDate(jsonObject.getDate("startDate"));
        Date endDate = DateUtil.getEndTimeOfDate(jsonObject.getDate("endDate"));

        String buyerNick = jsonObject.getString("buyerNick");
        Long orderId = jsonObject.getLong("orderId");
        Integer assistType = jsonObject.getInteger("assistType");

        AssistServiceQuery assistServiceQuery = new AssistServiceQuery();
        assistServiceQuery.setAssistType(assistType);
        assistServiceQuery.setCustomerId(buyerNick);
        assistServiceQuery.setOrderId(orderId);
        SortPageQuery sortPageQuery = new SortPageQuery();
        sortPageQuery.setSize(0l);
        Map<String, Integer> tableTotalRecordNumMap = csOrderIndexDao.selectCsOrderIndexByDateByCsNickByBuyerByOrderByTypeCount(shopCommonParam, shopQuery.getCsNickLst(), startDate, endDate, assistServiceQuery);
        Map<String, String> csSimpleMap = JSONObject.parseObject(jsonObject.getString("csSimpleNickMap"), new TypeReference<Map<String, String>>() {
        });

        List<CsOrderIndexDTO> csOrderIndexDTOs = csOrderIndexDao.selectCsOrderIndexByDateByCsNickByBuyerByOrderByType(shopCommonParam, startDate, endDate, shopQuery.getCsNickLst(), assistServiceQuery, sortPageQuery, tableTotalRecordNumMap);
        Set<Long> orderIds = csOrderIndexDTOs.stream().map(CsOrderIndexDTO::getOrderId).collect(Collectors.toSet());
        List<OrderDTO> orderLst = orderDao.selectFieldsByOrderIds(shopCommonParam, orderIds, "out_stock_time,payment", startDate, endDate);

        for (CsOrderIndexDTO dto : csOrderIndexDTOs) {
            StringBuilder sb = new StringBuilder();
            if (dto.getIsAssitOrderCreate()) {
                sb.append("协助下单 ");
            }
            if (dto.getIsAssitOrderPay()) {
                sb.append("协助付款 ");
            }
            if (dto.getIsAssitOrderInFollowup()) {
                sb.append("协助跟进 ");
            }
            dto.setAssistType(sb.toString());

            for (OrderDTO order : orderLst) {
                if (dto.getOrderId().equals(order.getOrderId()) && order.getOutStockTime() != null) {
                    dto.setOutStockTime(new Timestamp(order.getOutStockTime().getTime()));
                    break;
                }
            }
        }

        ExeclTableParam<CsOrderIndexVO> tableParam = new ExeclTableParam<>();
        List<ExeclColumnParam> columnParams = Lists.newArrayList();
        List<CsOrderIndexVO> voList = Lists.newArrayList();
        for (CsOrderIndexDTO csO : csOrderIndexDTOs) {
            CsOrderIndexVO vo = new CsOrderIndexVO();
            vo.setDate(csO.getDate());
            vo.setOrderId(csO.getOrderId());
            vo.setOrderPayDate(csO.getOrderPayDate());
            vo.setOutStockTime(csO.getOutStockTime());
            vo.setOrderPayment(csO.getOrderPayment());
            vo.setBuyerNick(csO.getBuyerNick());
            vo.setCsSimpleNick(csSimpleMap.get(csO.getCsNick()));
            vo.setAssistType(csO.getAssistType());
            vo.setOrderCreated(csO.getOrderCreated());
            voList.add(vo);
        }

        tableParam.setData(voList);
        tableParam.setColumnParams(columnParams);
        columnParams.add(new ExeclColumnParam("日期", "date", 1));
        columnParams.add(new ExeclColumnParam("订单编号", "orderId"));
        columnParams.add(new ExeclColumnParam("下单时间", "orderCreated", 2));
        columnParams.add(new ExeclColumnParam("出库时间", "outStockTime", 2));
        columnParams.add(new ExeclColumnParam("订单金额（元）", "orderPayment", "%.2f"));
        columnParams.add(new ExeclColumnParam("顾客昵称", "buyerNick"));
        columnParams.add(new ExeclColumnParam("客服昵称", "csSimpleNick"));
        columnParams.add(new ExeclColumnParam("协助类型", "assistType"));

        String title = "协助服务分析";
        //ExportExcel exportExcel = new ExportExcel();
        //交给spring处理方便aop
        ExportExcelBean exportExcel = SpringUtil.getBean(ExportExcelBean.class);
        //上传参数
        CommonLogUploadParam commonUpParam = JacksonUtils.json2pojo(jsonObject.getString("commonUpParam"), CommonLogUploadParam.class);
//        UploadDBOperationParam uploadParam = UploadDBOperationBusinessImpl.getParam(commonUpParam,null);
//        uploadParam.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_ASSISTSERVICE.getName());

        tableParam.setOrderInfoLogUploadParam(commonUpParam);
        exportExcel.execlExport(title, tableParam, out);

        //uploadDBOperationBusiness.upload(uploadParam);

    }


}
