package com.pes.jd.business.sub;

import com.pes.jd.model.DO.CsOrderIndex;
import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.DataAnalysisVO;

import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/19 10:20 PM
 * @since 1.0.0
 */
public interface OrderIndexBusiness {
    List<CsOrderIndex> selectByNonPay(
            Set<String> nicks,
            String buyerNick,
            Date startDate,
            Date endDate,
            String shopId
    );


    DataAnalysisVO<CsOrderIndexDTO> selectCsOrderIndexByDateByCsNickByBuyerByOrderByType(ShopCommonParam shop, Date startDate, Date endDate,
                                                                                         List<String> csNickList, AssistServiceQuery assistServiceQuery,
                                                                                         SortPageQuery sortPageQuery, OrderInfoLogUploadParam orderInfoLogUploadParam);

    void exportAssistServiceAnalysis(OutputStream out, String jsonParam) throws Exception;
}
