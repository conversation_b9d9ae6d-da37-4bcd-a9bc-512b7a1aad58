package com.pes.jd.business;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.reponse.ApiResponse;

public interface JobPriorityTaskBusiness {

    Object pullAndCalShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData,boolean isYd) throws Exception;

    Object calShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    Object initShopData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData)
            throws Exception;

    void pullShopLoginLogData(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    Object pullShopLoginLogDataForHand(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    //获取订单出库信息
    int pullOrderOutStockTime(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception;

    Object pullShopPvUvDataForHand(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    Object pullShopLoginLogDataByDelayHours(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

    //获取订单取消
	Object pullChatLog(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	Object calChatLog(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	//获取订单取消
    ApiResponse pullOrderCancel(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception;

    int pullOrderCreate(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception;

    int pullOrderPay(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception;

    int pullOrderFinish(JobShopDTO shop, Long orderId, String orderCreatedTime) throws Exception;

    //计算店铺中差评
    Object calOrderEvaluate(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
}
