package com.pes.jd.business;

import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;

public interface OrderHandleBussiness {

	//订单交易
	void pullShopOrderInfo(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
 	void pullShopIncrementOrder(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	void pushShopOrderFromJd(JobShopQuery jobShop, JobDateQuery jobDate, Integer type, boolean isDelData) throws Exception;



	//订单过滤
	void handleShopOrderFilter(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;

	//订单备注拉取
	void pullShopOrderRemark(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	//拉取未支付订单信息
	void pullShopNoPayOrderInfo(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData) throws Exception;
	
	//消息队列中的订单取消和订单出库信息更新订单和订单预售表
    void updateOrderAndPresaleOrder(JobShopQuery jobShop, JobDateQuery jobDate, boolean isDelData, boolean flag) throws Exception;
		
	
}
