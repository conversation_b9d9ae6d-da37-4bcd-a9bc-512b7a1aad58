package com.pes.jd.interceptor;

import com.pes.jd.model.Annotation.JobPriorityMethodAnnotation;
import com.pes.jd.task.JobTask;
import com.pes.jd.task.queue.JobQueuePriority;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * job优先级
 * <AUTHOR>
 *
 */
@Aspect
@Component
@Order(2)
public class JobPriorityInterceptor {
	
	@Resource
	@Qualifier("jobQueue")
	private PriorityBlockingQueue<JobTask> jobQueue;
	
	@Resource
	@Qualifier("jobQueuePriority")
	private JobQueuePriority priority;
	
	@Around(value="@annotation(priorityMethod)")
	public void invokeForSendfailShopMailTask(ProceedingJoinPoint proceed, JobPriorityMethodAnnotation priorityMethod){
		this.addJobTask(proceed, priority.getPriorityByLevel(priorityMethod.level()), priorityMethod.methodDesc());
	}

	private void addJobTask(ProceedingJoinPoint proceed, AtomicInteger levelPriority, String desc) {
		if(jobQueue.isEmpty()){
			priority.initValue();
		}
		jobQueue.put(new JobTask(proceed, levelPriority.decrementAndGet(), desc));
	}
}
