package com.pes.jd.interceptor;

import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.modifyVenderRemark.OperatorResult;
import com.pes.jd.annotation.DBLog;
import com.pes.jd.annotation.OrderLog;
import com.pes.jd.constants.AppConstants;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ExportTaskEnum;
import com.pes.jd.model.Enum.RequestUrlEnum;
import com.pes.jd.model.JSON.EnquiryOrderLossVO;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.RefundAnalysisParam;
import com.pes.jd.model.Param.UploadDBOperationParam;
import com.pes.jd.model.TO.OrderInfoLogTO;
import com.pes.jd.model.VO.*;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.DataBaseUtil;
import com.pes.jd.util.UploadLogUtil;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: aiJun
 * @Date: 2019-07-17 15:48
 * @Version 1.0
 */
@Aspect
@Component
public class LogAspect {
    private static Logger logger = LoggerFactory.getLogger(LogAspect.class);

    //定义切面
    @Pointcut("@annotation(com.pes.jd.annotation.OrderLog)")
    public void orderLogPointCut() {
    }

    //定义切面（数据库日志）
    @Pointcut("@annotation(com.pes.jd.annotation.DBLog)")
    public void DBLogPointCut() {
    }


    @AfterReturning(value = "DBLogPointCut()")
    public void uploadDbLog(JoinPoint joinPoint) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            //从切面中获取当前方法
            Method method = signature.getMethod();
            //得到了方法提取出他的注解
            DBLog annotation = method.getAnnotation(DBLog.class);
            //处理日志上传
            doUploadDBLog(joinPoint, annotation);

        } catch (Exception e) {
            logger.error("DB 操作日志处理出错 -->>>" + e.getMessage(), e);
        }

    }

    /**
     * 数据分析->成交分析
     * 数据分析->流失分析
     * 相关的订单查询上传
     *
     * @param joinPoint
     * @param result
     */
    @AfterReturning(value = "orderLogPointCut()", returning = "result")
    public void uploadOrderLog(JoinPoint joinPoint, Object result) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            //从切面中获取当前方法
            Method method = signature.getMethod();
            //得到了方法提取出他的注解
            OrderLog annotation = method.getAnnotation(OrderLog.class);

            //输出
//            System.err.println("paramType=>>" + action.paramType()+" resultType----->>>"+action.resultType());
            //---------------------上传订单日志
            //处理日志上传
            doUploadOrderLog(joinPoint, result, annotation);
//            System.out.println("return  --->>>>"+result);
        } catch (Exception e) {
            logger.error("order 操作日志处理出错 -->>>" + e.getMessage(), e);
        }

    }

    //定义切面  -- 导出订单的切面
    @Pointcut("@annotation(com.pes.jd.annotation.ExportOrderLog)")
    public void exportOrderLogPointCut() {
    }

    /**
     * 店铺销售分析
     * 客服销售分析
     * 静默销售分析
     * 客服商品销售明细
     * 静默商品销售明细
     * 客服退款分析
     * 静默退款分析
     * 协助服务分析
     * 预售订单分析
     * 询单下单未付款分析
     * 静默下单未付款分析
     * 以上导出的关于订单详情的上传
     *
     * @param joinPoint
     */
    @AfterReturning(value = "exportOrderLogPointCut()")
    public void uploadExportOrderLog(JoinPoint joinPoint) {
        try {
            //处理日志上传
            doExportUploadOrderLog(joinPoint);
//            System.out.println("return  --->>>>"+result);
        } catch (Exception e) {
            logger.error("export order 操作日志处理出错 -->>>" + e.getMessage(), e);
        }
    }

    /**
     * 执行导出订单日志的上传
     *
     * @param joinPoint
     */
    private void doExportUploadOrderLog(JoinPoint joinPoint) throws Exception {
        logger.info("导出order/sql-->AOP进入成功===>>>>joinPoint={}", joinPoint);
        String sheetName = null;
        ExeclTableParam tableParam = null;
        OutputStream out = null;
        //获取所有需要的参数
        for (Object arg : joinPoint.getArgs()) {
            if (arg instanceof String) {
                sheetName = (String) arg;
            } else if (arg instanceof ExeclTableParam) {
                tableParam = (ExeclTableParam) arg;
            } else if (arg instanceof OutputStream) {
                out = (OutputStream) arg;
            }
        }
        //封装参数并实现导出日志的上传
        doExportUploadOrderLogByParam(sheetName, tableParam, out);
    }

    private void doExportUploadOrderLogByParam(String sheetName, ExeclTableParam tableParam, OutputStream out) throws Exception {
        long s = System.currentTimeMillis();
        //日志上传必备的参数
        String uri = CommonConstants.URI + RequestUrlEnum.TASK_EXPORT_ADDEXPORTEXCELTASK;
        String deviceId = "";
        String jdId = "";
        String userId = "";
        Long timeStamp = System.currentTimeMillis();
        String userIp = "";
        String fileMd5 = "";
        long operation = 0;
        boolean isModify = false;
        List orderIdLst = new ArrayList();
        deviceId = tableParam.getOrderInfoLogUploadParam().getDeviceId();
        jdId = tableParam.getOrderInfoLogUploadParam().getJdId();
        userId = tableParam.getOrderInfoLogUploadParam().getUserId();
        userIp = tableParam.getOrderInfoLogUploadParam().getUserIp();
        //校验deviceId 如果为空就将方法return
        if (Objects.isNull(deviceId) || "".equals(deviceId)) {
            logger.warn("--->>> deviceId isEmpty  取消上传。");
            return;
        }

        //根据导出的内容做强转
        if (ExportTaskEnum.SHOP_SALE_ANALYSIS.getType().equals(sheetName)) {
            List<ShopSaleAnalysisVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(ShopSaleAnalysisVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.SHOP_SALE_ANALYSIS.getType());
        } else if (ExportTaskEnum.CS_SALE_ANALYSIS.getType().equals(sheetName)) {
            List<CsSaleAnalysisVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(CsSaleAnalysisVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.CS_SALE_ANALYSIS.getType());
        } else if ("静默销售记录".equals(sheetName)) {
            List<SilenceSaleAnalysisVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(SilenceSaleAnalysisVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.SILENCE_SALE_ANALYSIS.getType());
        } else if (ExportTaskEnum.CS_GOODS_SALE_DETAIL.getType().equals(sheetName)) {
            List<CsGoodsSaleIndexDetailVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }
            orderIdLst = orderLst.stream().map(CsGoodsSaleIndexDetailVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.CS_GOODS_SALE_DETAIL.getType());
        } else if (ExportTaskEnum.SILENT_GOODS_SALE_DETAIL.getType().equals(sheetName)) {
            List<SlientGoodsSaleIndexDetailDTO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(SlientGoodsSaleIndexDetailDTO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.SILENT_GOODS_SALE_DETAIL.getType());
        } else if (ExportTaskEnum.CS_REFUND_ANALYSIS.getType().equals(sheetName)) {
            List<RefundDataAnalysisDTO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }
            orderIdLst = orderLst.stream().map(RefundDataAnalysisDTO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.CS_REFUND_ANALYSIS.getType());
        } else if (ExportTaskEnum.SILENT_REFUND_ANALYSIS.getType().equals(sheetName)) {
            List<RefundDataAnalysisDTO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(RefundDataAnalysisDTO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.SILENT_REFUND_ANALYSIS.getType());
        } else if (ExportTaskEnum.ASSIST_SERVICE_ANALYSIS.getType().equals(sheetName)) {
            List<CsOrderIndexVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(CsOrderIndexVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.ASSIST_SERVICE_ANALYSIS.getType());
        } else if (ExportTaskEnum.PRESALE_ORDER_ANALYSIS.getType().equals(sheetName)) {
            List<OrderPresaleVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(OrderPresaleVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);
            logger.info("执行导出类型为:{}", ExportTaskEnum.PRESALE_ORDER_ANALYSIS.getType());
        }else if (ExportTaskEnum.RESERVE_ORDER_ANALYSIS.getType().equals(sheetName)) {
            List<OrderPresaleVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(OrderPresaleVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);
            logger.info("执行导出类型为:{}", ExportTaskEnum.RESERVE_ORDER_ANALYSIS.getType());
        } else if ("询单下单未付款流失记录".equals(sheetName)) {
            List<EnquiryOrderLossVO> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(EnquiryOrderLossVO::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);

            logger.info("执行导出类型为:{}", ExportTaskEnum.ENQUIRY_ORDER_LOST_ANALYSIS.getType());
        } else if ("静默下单未付款流失记录".equals(sheetName)) {
            List<SilenceLostRecord> orderLst = tableParam.getData();
            if (CollectionUtils.isEmpty(orderLst)) {
                logger.warn("订单数为空，不进行上传。");
                return;
            }

            orderIdLst = orderLst.stream().map(SilenceLostRecord::getOrderId).collect(Collectors.toList());
            operation = getOperation(isModify, orderIdLst, CommonConstants.BATCH_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER, CommonConstants.SINGLE_EXPORT_ORDER);
            logger.info("执行导出类型为:{}", ExportTaskEnum.SILENT_ORDER_LOST_ANALYSIS.getType());
        }

        fileMd5 = getFileMd5FromOutPutStream(out);
        logger.info("fileMd5=-->>>{}", fileMd5);

//        try( ByteArrayOutputStream baos = new ByteArrayOutputStream()){
//            baos.writeTo(out);
//            byte[] bytes = baos.toByteArray();
//            MessageDigest md5 = MessageDigest.getInstance("MD5");
//            md5.update(bytes);
//            BigInteger bi = new BigInteger(1, md5.digest());
//            fileMd5 = bi.toString(16);
//            logger.info("fileMd5={}", fileMd5);
//        }
        List<List<Object>> orderLsts = splitLst(orderIdLst, CommonConstants.ORDER_LOG_UPLOAD_MAX_NUM);
        if (CollectionUtils.isNotEmpty(orderLsts)) {
            for (List<Object> orderLstt : orderLsts) {
                //将订单进行上传
                String orderLstStr = getOrderStr(orderLstt);
                OrderInfoLogTO orderInfoLogTO = initBatchOrderInfoLogUploadParam(uri, deviceId, jdId, userId, userIp, timeStamp, operation, orderLstStr, fileMd5);
                UploadDBOperationParam DBLogTO = initDBOLogUploadParam(uri, deviceId, userId, userIp, timeStamp);
                try {
                    UploadLogUtil.uploadBatchLog(CommonConstants.UPLOAD_SQL, DBLogTO, "", timeStamp);
                    UploadLogUtil.uploadBatchLog(CommonConstants.UPLOAD_ORDER, orderInfoLogTO, "", timeStamp);
                    long e = System.currentTimeMillis();
                    logger.info("上传order/Sql订单日志耗时 -->{}ms", (e - s));
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }

    }

    private String getFileMd5FromOutPutStream(OutputStream out) {
        String fileMd5;
        ByteArrayInputStream byteArrayInputStream = outPutStream2InputStream(out);
        BigInteger bi = null;
        try {
            byte[] buffer = new byte[8192];
            int len = 0;
            MessageDigest md = MessageDigest.getInstance("MD5");
            while ((len = byteArrayInputStream.read(buffer)) != -1) {
                md.update(buffer, 0, len);
            }
            byteArrayInputStream.close();
            byte[] b = md.digest();
            bi = new BigInteger(1, b);
        } catch (NoSuchAlgorithmException e) {
            logger.error(e.getMessage(), e);
        } catch (IOException e) {
            logger.error(e.getMessage(), e);
        }
        fileMd5 = bi.toString(16);
        return fileMd5;
    }

    // outputStream转inputStream
    public ByteArrayInputStream outPutStream2InputStream(final OutputStream out) {
        ByteArrayOutputStream baos;
        baos = (ByteArrayOutputStream) out;
        final ByteArrayInputStream swapStream = new ByteArrayInputStream(baos.toByteArray());
        return swapStream;
    }

    private long getOperation(boolean isModify, List orderLst, long batchSearchOrder, long singleSearchOrder, long singleModifiedOrder) {
        long operation;
        if (orderLst.size() > 1) {
            operation = batchSearchOrder;
        } else {
            if (isModify) {
                operation = singleModifiedOrder;
            } else {
                operation = singleSearchOrder;
            }
        }
        return operation;
    }


    /**
     * 完成订单日志的上传
     *
     * @param joinPoint
     * @param result
     * @param action
     */
    private void doUploadOrderLog(JoinPoint joinPoint, Object result, OrderLog action) {
        logger.info("订单AOP进入成功===>>>>joinPoint={}", joinPoint);
        long s = System.currentTimeMillis();
        List<Object> orderIds = new ArrayList<>();
        //日志上传必备的参数
        String uri = "";
        String deviceId = "";
        String jdId = "";
        String userId = "";
        Long timeStamp = System.currentTimeMillis();
        String userIp = "";
        long operation;
        //是否是修改
        boolean isModify = false;

        /**获取上传必须的参数    start*/
        //遍历参数 --获取上传必须的参数封装
        for (Object arg : joinPoint.getArgs()) {
            if (arg instanceof OrderInfoLogUploadParam) {
                OrderInfoLogUploadParam orderInfoLogUploadParam = (OrderInfoLogUploadParam) arg;
                deviceId = orderInfoLogUploadParam.getDeviceId();
                jdId = orderInfoLogUploadParam.getJdId();
                userId = orderInfoLogUploadParam.getUserId();
                userIp = orderInfoLogUploadParam.getUserIp();
                timeStamp = orderInfoLogUploadParam.getTimeStamp();
            }
        }
        //校验deviceId 如果为空就将方法return
        if (Objects.isNull(deviceId) || "".equals(deviceId)) {
            logger.warn("---------------------->>>>>>>deviceId isEmpty 取消上传。");
            return;
        }

        /**获取上传必须的参数    end*/
        /***封装订单Id 和URI start***/
        if (Objects.equals(action.resultType(), List.class)) {  //结果返回的是集合
            List resultLst = (List) result;
            if (Objects.equals(action.paramType(), RefundDataAnalysisDTO.class)) {
                for (Object o : resultLst) {
                    RefundDataAnalysisDTO oRefundDataAnalysisDTO = (RefundDataAnalysisDTO) o;
                    orderIds.add(oRefundDataAnalysisDTO.getOrderId());
                }
                //获取方法的参数
                RefundAnalysisParam refundAnalysisParam = (RefundAnalysisParam) joinPoint.getArgs()[1];
                String refundUri = refundAnalysisParam.getUrl();
                if (refundUri.equals(RequestUrlEnum.DATA_ANALYSIS_CSREFUNDANALYSISLST.getName())) {
                    uri = CommonConstants.URI + refundUri;
                } else if (refundUri.equals(RequestUrlEnum.DATA_ANALYSIS_SILENTREFUNDANALYSISLST.getName())) {
                    uri = CommonConstants.URI + refundUri;
                }
                logger.info("数据分析-->成交分析->退款分析->客服退款分析or静默退款分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), EnquiryOrderLossVO.class)) {
                for (Object o : resultLst) {
                    EnquiryOrderLossVO oEnquiryOrderLossVO = (EnquiryOrderLossVO) o;
                    orderIds.add(oEnquiryOrderLossVO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ENQUIRYORDERLOSTRECORD;
                logger.info("数据分析-->流失分析->询单下单未付款分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), SilenceLostRecord.class)) {
                for (Object o : resultLst) {
                    SilenceLostRecord oSilenceLostRecord = (SilenceLostRecord) o;
                    orderIds.add(oSilenceLostRecord.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SILENCEORDERLOSTRECORD;
                logger.info("数据分析-->流失分析->静默下单未付款分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), OrderDetailDTO.class)) {
                for (Object o : resultLst) {
                    OrderDetailDTO oOrderDetailDTO = (OrderDetailDTO) o;
                    orderIds.add(oOrderDetailDTO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SELECTDEALORDERDETAILLST;
                logger.info("数据分析-->接待分析->成交订单详情-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), OrderDTO.class)) {
                for (Object o : resultLst) {
                    OrderDTO oOrderDTO = (OrderDTO) o;
                    orderIds.add(oOrderDTO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.CUSTOMER_SELECTSHOPORDERDETAILINFO;
                logger.info("插件查询订单处理成功 num={}", orderIds.size());
            }
        } else if (Objects.equals(action.resultType(), DataAnalysisVO.class)) {     //返回的是DataAnalysisVo类型
            System.out.println("DataAnalysisVO.class");
            DataAnalysisVO resultLst = (DataAnalysisVO) result;
            List dataList = resultLst.getDataList();
            if (Objects.equals(action.paramType(), CsGoodsSaleIndexDetailDTO.class)) {
                for (Object o : dataList) {
                    CsGoodsSaleIndexDetailDTO oCsGoodsSaleIndexDetailDTO = (CsGoodsSaleIndexDetailDTO) o;
                    orderIds.add(oCsGoodsSaleIndexDetailDTO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_CSGOODSSALEINDEXDETAIL;
                logger.info("数据分析-->成交分析->商品销售分析->客服商品销售明细-->订单处理成功 num={}", orderIds.size());

            } else if (Objects.equals(action.paramType(), ShopSaleAnalysisVO.class)) {
                for (Object o : dataList) {
                    ShopSaleAnalysisVO oShopSaleAnalysisVO = (ShopSaleAnalysisVO) o;
                    orderIds.add(oShopSaleAnalysisVO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SHOPSALEANALYSIS;
                logger.info("数据分析-->成交分析->销售分析->店铺销售分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), CsSaleAnalysisVO.class)) {
                for (Object o : dataList) {
                    CsSaleAnalysisVO oCsSaleAnalysisVO = (CsSaleAnalysisVO) o;
                    orderIds.add(oCsSaleAnalysisVO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_CSSALEANALYSIS;
                logger.info("数据分析-->成交分析->销售分析->客服销售分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), SilenceSaleAnalysisVO.class)) {
                for (Object o : dataList) {
                    SilenceSaleAnalysisVO oSilenceSaleAnalysisVO = (SilenceSaleAnalysisVO) o;
                    orderIds.add(oSilenceSaleAnalysisVO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SILENCESALEANALYSIS;
                logger.info("数据分析-->成交分析->销售分析->静默销售分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), SlientGoodsSaleIndexDetailDTO.class)) {
                for (Object o : dataList) {
                    SlientGoodsSaleIndexDetailDTO oSlientGoodsSaleIndexDetailDTO = (SlientGoodsSaleIndexDetailDTO) o;
                    orderIds.add(oSlientGoodsSaleIndexDetailDTO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SLIENTGOODSSALEINDEXDETAIL;
                logger.info("数据分析-->成交分析->商品销售分析->静默商品销售明细-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), CsOrderIndexDTO.class)) {
                for (Object o : dataList) {
                    CsOrderIndexDTO oCsOrderIndexDTO = (CsOrderIndexDTO) o;
                    orderIds.add(oCsOrderIndexDTO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ASSISTSERVICE;
                logger.info("数据分析-->成交分析->协助服务分析-->订单处理成功 num={}", orderIds.size());
            } else if (Objects.equals(action.paramType(), OrderPresaleVO.class)) {
                for (Object o : dataList) {
                    OrderPresaleVO oOrderPresaleVO = (OrderPresaleVO) o;
                    orderIds.add(oOrderPresaleVO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ORDERPRESALELST;
                logger.info("数据分析-->成交分析->预售订单分析-->订单处理成功 num={}", orderIds.size());
            }else if (Objects.equals(action.paramType(), OrderPreordainDTO.class)) {
                for (Object o : dataList) {
                    OrderPreordainDTO orderPreordainDTO = (OrderPreordainDTO) o;
                    orderIds.add(orderPreordainDTO.getOrderId());
                }
                uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ORDERPEDIANLST;
                logger.info("数据分析-->成交分析->预约订单分析-->订单处理成功 num={}", orderIds.size());
            }
            //Todo 这里需要对订单的修改做操作
        } else if (Objects.equals(action.paramType(), OrderInfoLogUploadParam.class)) {     //paramType=OrderInfoLogUploadParam 单独处理订单的修改
            //遍历全部参数获取订单ID
            isModify = true;
            Object orderId = null;
            for (Object arg : joinPoint.getArgs()) {//遍历所有参数获取订单ID
                if (arg instanceof Long) {
                    orderId = arg;
                }
            }
            orderIds.add(orderId);
            if (Objects.equals(action.resultType(), OperatorResult.class)) { //修改订单信息
                uri = CommonConstants.URI + RequestUrlEnum.CUSTOMER_UPDATEORDERREMARKINFO;
                logger.info("插件修改订单信息->处理成功 num={}", orderIds.size());


            } else if (Objects.equals(action.resultType(), void.class)) {
                uri = CommonConstants.URI + RequestUrlEnum.CUSTOMER_UPDATEADDRESS;
                logger.info("插件更新订单地址->处理成功 num={}", orderIds.size());
            }
        }
        /***封装订单Id 和URI end***/

        System.out.println(uri);
        if (CollectionUtils.isNotEmpty(orderIds)) {
            operation = getOperation(isModify, orderIds, CommonConstants.BATCH_SEARCH_ORDER, CommonConstants.SINGLE_SEARCH_ORDER, CommonConstants.SINGLE_MODIFIED_ORDER);

            //将订单按时间分割
            List<List<Object>> ordersLst = splitLst(orderIds, CommonConstants.ORDER_LOG_UPLOAD_MAX_NUM);

            if (CollectionUtils.isNotEmpty(ordersLst)) {
                for (List<Object> orderLst : ordersLst) {
                    //将订单进行上传
                    String orderLstStr = getOrderStr(orderLst);
                    OrderInfoLogTO orderInfoLogTO = initBatchOrderInfoLogUploadParam(uri, deviceId, jdId, userId, userIp, timeStamp, operation, orderLstStr, "NULL");
                    try {
                        UploadLogUtil.uploadBatchLog(CommonConstants.UPLOAD_ORDER, orderInfoLogTO, "", timeStamp);
                        long e = System.currentTimeMillis();
                        logger.info("上传订单日志耗时 -->{}ms", (e - s));
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    }
                }
            }
        }


    }


    private void doUploadDBLog(JoinPoint joinPoint, DBLog action) {
        long s = System.currentTimeMillis();
        //日志上传必备的参数
        String uri = "";
        String deviceId = "";
        String userId = "";
        Long timeStamp = System.currentTimeMillis();
        String userIp = "";

        /**获取上传必须的参数    start*/
        //遍历参数
        for (Object arg : joinPoint.getArgs()) {
            if (arg instanceof OrderInfoLogUploadParam) {
                OrderInfoLogUploadParam orderInfoLogUploadParam = (OrderInfoLogUploadParam) arg;
                deviceId = orderInfoLogUploadParam.getDeviceId();
                userId = orderInfoLogUploadParam.getUserId();
                userIp = orderInfoLogUploadParam.getUserIp();
                timeStamp = orderInfoLogUploadParam.getTimeStamp();
            }
        }
        /**获取上传必须的参数    end*/
        //校验deviceId 如果为空就将方法return
        if (Objects.isNull(deviceId) || "".equals(deviceId)) {
            logger.warn("--->> deviceId isEmpty 取消上传。");
            return;
        }

        /***封装URI start***/
        String type = action.value();
        if (type.equals("refundAnalysis")) {
            //获取方法的参数
            RefundAnalysisParam refundAnalysisParam = (RefundAnalysisParam) joinPoint.getArgs()[1];
            String refundUri = refundAnalysisParam.getUrl();
            if (refundUri.equals(RequestUrlEnum.DATA_ANALYSIS_CSREFUNDANALYSISLST.getName())) {
                uri = CommonConstants.URI + refundUri;
            } else if (refundUri.equals(RequestUrlEnum.DATA_ANALYSIS_SILENTREFUNDANALYSISLST.getName())) {
                uri = CommonConstants.URI + refundUri;
            }
            logger.info("数据库操作日志-->数据分析-->成交分析->退款分析->客服退款分析or静默退款分析");
        } else if (type.equals("enquiryOrderLost")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ENQUIRYORDERLOSTRECORD;
            logger.info("数据库操作日志-->数据分析-->流失分析->询单下单未付款分析");
        } else if (type.equals("slientOrderLost")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SILENCEORDERLOSTRECORD;
            logger.info("数据库操作日志-->数据分析-->流失分析->静默下单未付款分析");
        } else if (type.equals("csGoodsSaleIndexDetail")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_CSGOODSSALEINDEXDETAIL;
            logger.info("数据库操作日志-->数据分析-->成交分析->商品销售分析->客服商品销售明细");
        } else if (type.equals("shopSaleAnalysis")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SHOPSALEANALYSIS;
            logger.info("数据库操作日志-->数据分析-->成交分析->销售分析->店铺销售分析");
        } else if (type.equals("csSaleAnalysis")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_CSSALEANALYSIS;
            logger.info("数据库操作日志-->数据分析-->成交分析->销售分析->客服销售分析");
        } else if (type.equals("slientSaleAnalysis")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SILENCESALEANALYSIS;
            logger.info("数据库操作日志-->数据分析-->成交分析->销售分析->静默销售分析");
        } else if (type.equals("slientGoodsSaleDetail")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SLIENTGOODSSALEINDEXDETAIL;
            logger.info("数据库操作日志-->数据分析-->成交分析->商品销售分析->静默商品销售明细");
        } else if (type.equals("assistService")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ASSISTSERVICE;
            logger.info("数据库操作日志-->数据分析-->成交分析->协助服务分析");
        } else if (type.equals("OrderPresaleAnalysis")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ORDERPRESALELST;
            logger.info("数据库操作日志-->数据分析-->成交分析->预售订单分析");
        } else if (type.equals("selectDealOrderDetailLst")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_SELECTDEALORDERDETAILLST;
            logger.info("数据库操作日志-->数据分析-->接待分析->商品咨询/推荐明细->成交订单详情");
        } else if (type.equals("OrderDetailInfo")) {
            uri = CommonConstants.URI + RequestUrlEnum.CUSTOMER_SELECTSHOPORDERDETAILINFO;
            logger.info("数据库操作日志-->插件查询订单");
        } else if (type.equals("updateOrderRemarkInfo")) {
            uri = CommonConstants.URI + RequestUrlEnum.CUSTOMER_UPDATEORDERREMARKINFO;
            logger.info("数据库操作日志-->插件修改订单信息");
        } else if (type.equals("updateAddress")) {
            uri = CommonConstants.URI + RequestUrlEnum.CUSTOMER_UPDATEADDRESS;
            logger.info("数据库操作日志-->插件更新订单地址");
        }else if (type.equals("selectOrderPreOrdain")) {
            uri = CommonConstants.URI + RequestUrlEnum.DATA_ANALYSIS_ORDERPEDIANLST;
            logger.info("数据库操作日志-->数据分析-->成交分析->预约订单分析");
        }

        /***封装订单Id 和URI end***/

        //System.out.println(uri);
        //数据库操作日志上传
        UploadDBOperationParam DBOParamTO = initDBOLogUploadParam(uri, deviceId, userId, userIp, timeStamp);
        try {
            UploadLogUtil.uploadBatchLog(CommonConstants.UPLOAD_SQL, DBOParamTO, "", timeStamp);
            long e = System.currentTimeMillis();
            logger.info("上传数据库日志耗时 -->{}ms", (e - s));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 将集合按数量划分，应付防止单个集合数量过大溢出的场景
     *
     * @param orderIds             需要切分的集合
     * @param orderLogUploadMaxNum 单个集合最大的容量
     * @return
     */
    private List<List<Object>> splitLst(List orderIds, int orderLogUploadMaxNum) {
        List<List<Object>> ordersLst = new ArrayList<>();
        //超过接口上传的最大数要分割
        if (orderIds.size() > orderLogUploadMaxNum) {
            ordersLst = CollectionUtil.smallToLst(orderIds, orderLogUploadMaxNum);
        } else {
            ordersLst.add(orderIds);
        }
        return ordersLst;
    }

    private OrderInfoLogTO initBatchOrderInfoLogUploadParam(String uri, String deviceId, String jdId, String userId, String userIp, Long timeStamp, long operation, String orderLstStr, String fileMd5) {
        OrderInfoLogTO orderInfoLogTO = new OrderInfoLogTO();
        orderInfoLogTO.setUserIp(userIp);
        orderInfoLogTO.setAppName(AppConstants.SERVER_PIN);
        orderInfoLogTO.setJosAppKey(AppConstants.APP_KEY);
        orderInfoLogTO.setJdId(jdId);
        orderInfoLogTO.setDeviceId(deviceId);
        orderInfoLogTO.setUserId(userId);
        orderInfoLogTO.setFileMd5(fileMd5);
        orderInfoLogTO.setOrderIds(orderLstStr);
        orderInfoLogTO.setOperation(operation);
        orderInfoLogTO.setUrl(uri);
        orderInfoLogTO.setTimeStamp(timeStamp);
        logger.info("上传的userIP={},deviceId={}", userIp, deviceId);
        return orderInfoLogTO;
    }


    private UploadDBOperationParam initDBOLogUploadParam(String uri, String deviceId, String userId, String userIp, Long timeStamp) {
        UploadDBOperationParam DBOParamTO = new UploadDBOperationParam();
        DBOParamTO.setUserIp(userIp);
        DBOParamTO.setAppName(AppConstants.SERVER_PIN);
        DBOParamTO.setJosAppKey(AppConstants.APP_KEY);
        DBOParamTO.setDeviceId(deviceId);
        DBOParamTO.setUserId(userId);
        DBOParamTO.setUrl(uri);
        DBOParamTO.setTimeStamp(timeStamp);
        String dbUrl = DataBaseUtil.dbUrl;
        dbUrl = dbUrl.substring(dbUrl.indexOf('/' + 2), dbUrl.indexOf('?'));
        dbUrl = dbUrl.substring(0, dbUrl.indexOf('/'));
        String sql = SqlInterceptor.sql;
        //List<String> sqlLst = SqlInterceptor.sqlLst;
        logger.info("数据库执行sql:{}", sql);
        logger.info("数据库ip:{}", dbUrl);
        DBOParamTO.setDb(dbUrl);
        //DBOParamTO.setSql(String.join(",", sqlLst));
        DBOParamTO.setSql(sql);
        logger.info("上传的userIP={},deviceId={}", userIp, deviceId);
        return DBOParamTO;
    }

    private String getOrderStr(List<Object> orderLst) {
        StringBuilder orderLstBuilder = new StringBuilder();
        for (int i = 0; i < orderLst.size(); i++) {
            if (i == 0) {
                orderLstBuilder.append(orderLst.get(i));
            } else {
                orderLstBuilder.append("," + orderLst.get(i));
            }
        }
        return orderLstBuilder.toString();
    }
}