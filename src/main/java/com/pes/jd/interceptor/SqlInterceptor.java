package com.pes.jd.interceptor;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;

@Component
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
        //@Signature(type = Executor.class, method = "batch", args = { MappedStatement.class, Object.class })
})
public class SqlInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(SqlInterceptor.class);

    public static String sql;

    //public static List<String> sqlLst = new ArrayList<String>();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = null;
        if (invocation.getArgs().length > 1) {
            parameter = invocation.getArgs()[1];
        }

        BoundSql boundSql = mappedStatement.getBoundSql(parameter);
        Configuration configuration = mappedStatement.getConfiguration();
        Object returnVal = invocation.proceed();

        //获取sql语句d
        sql = getSql(configuration, boundSql);
        //sqlLst.add(sql);
        //log.info("Mybatis 拦截器获取SQL:{}",sql);
        return returnVal;
    }


    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties arg0) {
    }

    /**
     * 获取SQL
     *
     * @param configuration
     * @param boundSql
     * @return
     */
    private String getSql(Configuration configuration, BoundSql boundSql) {
        long s = System.currentTimeMillis();
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        if (parameterObject == null || parameterMappings.size() == 0) {
            return sql;
        }

        MetaObject metaObject = configuration.newMetaObject(parameterObject);
        StringBuilder builder = new StringBuilder();
        char[] sqlChars = sql.toCharArray();

        int index = 0;
        for (char c : sqlChars) {
            if (c == '?') {
                String property = parameterMappings.get(index).getProperty();
                if (metaObject.hasGetter(property)) {
                    builder.append(getParameterValue(metaObject.getValue(property)));
                } else if (boundSql.hasAdditionalParameter(property)) {
                    builder.append(getParameterValue(boundSql.getAdditionalParameter(property)));
                }
                index++;
            } else
                builder.append(c);
        }
        sql = builder.toString();
        long e = System.currentTimeMillis();
        log.info("---->getSql()执行时间 {} ms", (e - s));
        return sql;
    }

    private String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format(obj) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "";
            }
        }
        return value;
    }


    ///**
    // * 获取SQL
    // * @param configuration
    // * @param boundSql
    // * @return
    // */
    //private String getSql(Configuration configuration, BoundSql boundSql) {
    //    Object parameterObject = boundSql.getParameterObject();
    //    List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
    //    String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
    //    if (parameterObject == null || parameterMappings.size() == 0) {
    //        return sql;
    //    }
    //    TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
    //    if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
    //        sql = sql.replaceFirst("\\?", getParameterValue(parameterObject));
    //    } else {
    //        MetaObject metaObject = configuration.newMetaObject(parameterObject);
    //        for (ParameterMapping parameterMapping : parameterMappings) {
    //            String propertyName = parameterMapping.getProperty();
    //            if (metaObject.hasGetter(propertyName)) {
    //                Object obj = metaObject.getValue(propertyName);
    //                sql = sql.replaceFirst("\\?", getParameterValue(obj));
    //            } else if (boundSql.hasAdditionalParameter(propertyName)) {
    //                Object obj = boundSql.getAdditionalParameter(propertyName);
    //                sql = sql.replaceFirst("\\?", getParameterValue(obj));
    //            }
    //        }
    //    }
    //    return sql;
    //}
    //
    //private String getParameterValue(Object obj) {
    //    String value = null;
    //    if (obj instanceof String) {
    //        value = "'" + obj.toString() + "'";
    //    } else if (obj instanceof Date) {
    //        DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
    //        value = "'" + formatter.format(obj) + "'";
    //    } else {
    //        if (obj != null) {
    //            value = obj.toString();
    //        } else {
    //            value = "";
    //        }
    //    }
    //    return value;
    //}
}