package com.pes.jd.office.excel;

import com.google.common.collect.Sets;
import com.pes.jd.annotation.ExportOrderLog;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.RefundDataAnalysisDTO;
import com.pes.jd.model.Param.ExportExcelParam;
import com.pes.jd.model.VO.CsSaleAnalysisVO;
import com.pes.jd.model.VO.SilenceSaleAnalysisVO;
import com.pes.jd.office.param.ExeclColumnParam;
import com.pes.jd.office.param.ExeclTableParam;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.MD5Utils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

@SuppressWarnings("rawtypes")
@Component
public class ExportExcelBean {

    private static final Logger logger = LoggerFactory.getLogger(ExportExcelBean.class);

    /**
     * 导出功能
     *
     * @param sheetName
     * @param out
     */
    @ExportOrderLog
    public void execlExport(String sheetName, ExeclTableParam tableParam, OutputStream out) {
        //构造参数防止多线程问题
        ExportExcelParam exportExcelParam = new ExportExcelParam();

        Integer dataLimit = exportExcelParam.getDataLimit();
        List data = tableParam.getData();
        int sheetNum = data.size() / dataLimit + 1;
        int startIndex = 0, endIndex = dataLimit;

        for (int i = 0; i < sheetNum; i++) {
            if (endIndex > data.size()) {
                endIndex = data.size();
            }
            createSheet(sheetName + (i + 1), tableParam, startIndex, endIndex,exportExcelParam);
            startIndex += dataLimit;
            endIndex += dataLimit;
        }

        writeAndDispose(out,exportExcelParam);
    }


    /**
     * 分sheet导出
     * @param sheetName
     * @param baseParam
     * @param startIndex
     * @param endIndex
     * @param exportExcelParam
     */
    public void createSheet(String sheetName, ExeclTableParam baseParam, int startIndex, int endIndex, ExportExcelParam exportExcelParam) {
        // 生成一个表格
        Sheet sheet;
        synchronized (this) {
            sheet = exportExcelParam.getWorkbook().createSheet(sheetName);
        }

        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 15);

        // 声明一个画图的顶级管理器
        Drawing patriarch = sheet.createDrawingPatriarch();

        setExcelHeader(0, baseParam.getColumnParams(), sheet,exportExcelParam);

        setTableData(baseParam.getColumnParams(), baseParam.getData().subList(startIndex, endIndex), exportExcelParam.getWorkbook(), sheet, patriarch,exportExcelParam);
    }


    /**
     * 写入OutputStream，且关闭删除workbook临时文件
     * @param out
     * @param exportExcelParam
     *
     */
    public void writeAndDispose(OutputStream out, ExportExcelParam exportExcelParam) {
        try {
            exportExcelParam.getWorkbook().write(out);
        } catch (IOException e) {
            logger.error("workbook write : error{}", e);
        } finally {
            if (exportExcelParam.getWorkbook() != null) {
                exportExcelParam.getWorkbook().dispose();// 删除临时文件
            }
        }
    }

    private static final Set<String> deSensitiveFields = Sets.newHashSet("buyerNick", "customer");

    /**
     * @Description: 设置数据
     */
    public void setTableData(List<ExeclColumnParam> columnParams, Collection dataset, Workbook workbook, Sheet sheet, Drawing patriarch, ExportExcelParam exportExcelParam) {
        logger.info("----> 导出列表的长度 : " + dataset.size());
        // 遍历集合数据，产生数据行
        Iterator it = dataset.iterator();
        int index = 0;
        while (it.hasNext()) {
            index++;
            Row row = sheet.createRow(index);
            Object t = it.next();
            for (int i = 0; i < columnParams.size(); i++) {
                ExeclColumnParam columnParam = columnParams.get(i);
                Cell cell = row.createCell(i);
                cell.setCellStyle(exportExcelParam.getDataCellStyle().getCellStyle());
                try {
//                    Object value = PropertyUtils.getProperty(t, columnParam.getFieldName());
                    //脱敏start
                    Object value = PropertyUtils.getProperty(t, columnParam.getFieldName());
                    boolean deSensititive = false;
                    if(deSensitiveFields.contains(columnParam.getFieldName())){
                        deSensititive = true;
                    }
                    //脱敏end
                    if (value == null){
                        if (CommonConstants.ORDER_PAY_DATE_FIELD.equals(columnParam.getFieldName()) ||
                                CommonConstants.ORDER_PAY_TIME_FIELD.equals(columnParam.getFieldName()) &&
                                        (t instanceof RefundDataAnalysisDTO || (t instanceof SilenceSaleAnalysisVO)
                                                || (t instanceof CsSaleAnalysisVO))) {
                            cell.setCellValue(CommonConstants.CASH_ON_DELIVERY_TYPE);
                        }else {
                            cell.setCellValue("--");
                            continue;
                        }
                    }

                    // 判断值的类型后进行强制类型转换
                    if (value instanceof String) {
                        if("顾客mofangID".equals(columnParam.getHeaderName())) {
                            cell.setCellValue(MD5Utils.encode(value.toString().toLowerCase()));
                        }else if(deSensititive){
                            String org = value.toString();
                            String startStr = org.substring(0, 1);
                            String endStr = org.substring(org.length() - 1);
//                            String target = org.replaceAll("(?<=\\S{1})\\S(?=\\S{1})","*");
                            String target = startStr + "***" + endStr;
                            cell.setCellValue(target);
                        }else {
                            cell.setCellValue(value.toString());
                        }
                    } else if (value instanceof Number) {
                        if (value instanceof Long)
                            cell.setCellValue(value.toString());
                        else if (value instanceof Integer) {
                            cell.setCellValue((Integer) value);
                        } else if (value instanceof Double) {
                            if (columnParam.getFormat() == null) {
                                cell.setCellValue((Double) value);
                            } else {
                                //格式化字符不为空时，若为 0 导出-- 否则保留两位小数
                                double dv = (double) value;
                                CellStyle cellStyle = workbook.createCellStyle();
                                if (dv == 0 && columnParam.getFormat().equals("--")) {
                                    cellStyle.setAlignment(HSSFCellStyle.ALIGN_RIGHT);
                                    cell.setCellStyle(cellStyle);
                                    cell.setCellValue("--");
                                } else {
                                    cellStyle.setDataFormat(workbook.createDataFormat().getFormat("#,#0.00"));
                                    cell.setCellStyle(cellStyle);
                                    cell.setCellValue(dv);
                                }
                            }
                        }

                    } else if (value instanceof Date) {
                        String textValue = null;
                        // 判断columnParam的数据处理类是否为null，是的话使用默认的数据处理，否则使用columnParam的
                        if (columnParam.getType() == null || columnParam.getType().equals(2)) {//若type 为null 或 2 格式化年月日时分秒
                            textValue = DateFormatUtils.formatYMdHms((Date) value);
                        } else {
                            textValue = DateFormatUtils.formatYMd((Date) value);
                        }

                        // 如果textValue为null，跳过
                        if (textValue != null) {
                            cell.setCellValue(textValue);
                            // 自适应宽度
                            int columnWidth = sheet.getColumnWidth(i);
                            int newColumnWidth = textValue.getBytes().length * 1 * 256 + 256 * 2;
                            if (newColumnWidth > columnWidth) {
                                sheet.setColumnWidth(i, newColumnWidth);
                            }
                        }

                    }

                } catch (Exception e) {
                    logger.error("export excel setTableData : error{}", e);
                } finally {
                    // 清理资源
                }
            }
        }
    }

    public void setExcelHeader(Integer index, List<ExeclColumnParam> columnParams, Sheet sheet, ExportExcelParam exportExcelParam) {
        if (index == null || index < 0) {
            index = 0;
        }
        Row row = sheet.createRow(index);
        ExeclColumnParam columnParam;
        Cell cell;
        //add a new md5 column
        Optional<ExeclColumnParam> encryptColExist = columnParams.stream().filter(col -> deSensitiveFields.contains(col.getFieldName())).findFirst();
        if(encryptColExist.isPresent()) {
            ExeclColumnParam execlColumnOrg = encryptColExist.get();
            ExeclColumnParam execlColumnNew = new ExeclColumnParam("顾客mofangID", execlColumnOrg.getFieldName(), execlColumnOrg.getType(), execlColumnOrg.getFormat());
            columnParams.add(execlColumnNew);
        }
        for (int i = 0, columnNum = columnParams.size(); i < columnNum; i++) {
            columnParam = columnParams.get(i);
            cell = row.createCell(i);
            cell.setCellStyle(exportExcelParam.getTitleCellStyle().getCellStyle());
            cell.setCellValue(columnParam.getHeaderName());
            sheet.setColumnWidth(i, columnParam.getHeaderName().getBytes().length * 2 * 256);
        }
    }

}
