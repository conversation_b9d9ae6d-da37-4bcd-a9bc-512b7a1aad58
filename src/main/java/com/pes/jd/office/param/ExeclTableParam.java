package com.pes.jd.office.param;

import com.pes.jd.model.Param.CommonLogUploadParam;

import java.util.List;

public class ExeclTableParam<T> {

    private List<T> data;
    private List<ExeclColumnParam> columnParams;

    private CommonLogUploadParam orderInfoLogUploadParam;

    public ExeclTableParam() {
        super();
    }


    public ExeclTableParam(List<T> data, List<ExeclColumnParam> columnParams) {
        super();
        this.data = data;
        this.columnParams = columnParams;
    }

    public CommonLogUploadParam getOrderInfoLogUploadParam() {
        return orderInfoLogUploadParam;
    }

    public void setOrderInfoLogUploadParam(CommonLogUploadParam orderInfoLogUploadParam) {
        this.orderInfoLogUploadParam = orderInfoLogUploadParam;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public List<ExeclColumnParam> getColumnParams() {
        return columnParams;
    }

    public void setColumnParams(List<ExeclColumnParam> columnParams) {
        this.columnParams = columnParams;
    }


}
