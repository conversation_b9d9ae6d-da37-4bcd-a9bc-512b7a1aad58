package com.pes.jd.mq.jcq.consumer;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.Constants.JobPullRecordStatusConstants;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.dao.JobRecordDao;
import com.pes.jd.model.DO.JobPullRecordDO;
import com.pes.jd.model.DTO.JobPullRecordDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.task.executor.*;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.SpringUtil;
import com.yiyitech.support.jcq.AbstractConsumer;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/8/26
 * @Modified By:
 */
//@Component
public class TaskJobConsumer extends AbstractConsumer {

    private static final Logger LOGGER = LoggerFactory.getLogger(TaskJobConsumer.class);

    private ThreadPoolExecutor taskJobExecutor;

    private ExecutorService taskJobExecutorForPVUV;

    private ExecutorService taskJobExecutorForDutyLog;

    private ExecutorService taskJobExecutorForChatLog;

    private ExecutorService taskJobExecutorForGoodSku;

    private ExecutorService taskJobExecutorForPullAndRefundData;

    private ExecutorService taskJobExecutorForOrderEvaluate;

    private ExecutorService taskJobExecutorForShopUserCondition;

    private JobRecordDao jobRecordDao;

    private ShopManageBusiness shopManageBusiness;

    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 1;
        taskJobExecutor = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForPVUV = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForDutyLog = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForChatLog = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForGoodSku = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForPullAndRefundData = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new NamedThreadFactory("self-job-thread-", true));
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForOrderEvaluate = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForShopUserCondition = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    @Override
    public boolean handleMessages(List<String> list) {
        for (String message : list) {
            JSONObject param = JSONObject.parseObject(message);
            String type = (String) param.get("type");
            if(shopManageBusiness == null){
                shopManageBusiness = SpringUtil.getBean(ShopManageBusiness.class);
            }

            switch (TaskJobDispatchEnum.TYPE_MAP.get(type)){
                case TASK_JOB_PVUV:
                    System.out.println("PVUV处理"+message);
                    taskJobExecutorForPVUV.execute(new TaskJobPvUvExecutor(message));
                    break;
                case TASK_JOB_DUTYLOG:
                    System.out.println("店铺客服记录处理"+message);
                    taskJobExecutorForDutyLog.execute(new TaskLoginLogDataByHoursJobExecutor(message));
                    break;
                case TASK_JOB_GOODSKU:
                    System.out.println("goodSku单个拉取处理"+message);
                    taskJobExecutorForGoodSku.execute(new TaskJobGoodSkuExecutor(message));
                    break;
                case TASK_JOB_PULLCHATLOG:
                    System.out.println("JOB Chatlog拉取"+message);
                    taskJobExecutorForChatLog.execute(new TaskJobChatLogExecutor(message));
                    break;
                case TASK_JOB_CALCHATLOG:
                    System.out.println("JOB Chatlog计算"+message);
                    taskJobExecutorForChatLog.execute(new TaskJobChatLogExecutor(message));
                    break;
                case TASK_JOB_PULLANDCALREFUNDDATA:
                    System.out.println("JOB 退款的拉取和计算"+message);
                    taskJobExecutorForPullAndRefundData.execute(new TaskJobPullAndCalRefundExecutor(message));
                    break;
                case TASK_JOB_CAL_ORDER_EVALUATE:
                    System.out.println("JOB 计算店铺订单的中差评"+message);
                    taskJobExecutorForOrderEvaluate.execute(new TaskJobCalOrderEvaluateExecutor(message));
                    break;
                case TASK_JOB_PULL_SHOP_USER_CONDITION:
                    System.out.println("JOB 拉取店铺使用分析"+message);
                    taskJobExecutorForShopUserCondition.execute(new TaskShopUserConditionJobExecutor(message));
                    break;
                case TASK_JOB_CAL_SHOP_USER_CONDITION:
                    System.out.println("JOB 计算店铺使用分析"+message);
                    taskJobExecutorForShopUserCondition.execute(new TaskShopUserConditionJobExecutor(message));
                    break;
            }
        }
        return true;
    }

    private void handleJobPullRecord(JobShopDTO shop, String startDateStr, String endDateStr) {

        Date startDate;
        Date endDate;
        try {
            startDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDateStr));
            endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
            List<Date> dates = DateUtil.splitDate(startDate, endDate);
            if (CollectionUtils.isNotEmpty(dates)) {
                for (Date date : dates) {
                    handleJobPullRecord(shop, date);
                }
            }
        } catch (Exception e) {
            LOGGER.error("[{}] insert job pull record fail", shop.getTitle(), e);
        }
    }

    private void handleJobPullRecord(JobShopDTO shop, Date date) {
        if(jobRecordDao == null){
            jobRecordDao = SpringUtil.getBean(JobRecordDao.class);
        }
        JobPullRecordDTO existPullRecord = jobRecordDao.getJobPullRecordByShopIdAndDate(shop, date);
        if(existPullRecord != null && JobPullRecordStatusConstants.EXECUTING.equals(existPullRecord.getRunStatus())){
            LOGGER.info("[{}] 任务进行中，放弃此次重复新任务！", shop.getTitle());
            return;
        }
        JobPullRecordDO pullRecord = new JobPullRecordDO(shop.getShopId(), date, Boolean.FALSE, JobPullRecordStatusConstants.UNEXECUTED);
        pullRecord.init();//初始化值
        jobRecordDao.deleteJobPullRecordByShopIdAndDate(shop, date);
        jobRecordDao.insertPullJobRecord(shop, pullRecord, date);
    }

    @Override
    public String getName() {
        return "taskjob";
    }
}
