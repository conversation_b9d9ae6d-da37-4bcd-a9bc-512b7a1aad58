package com.pes.jd.mq.kafka.producer;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.data.api.ChatLogOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class KafkaProducer {

    private static final Logger logger = LoggerFactory.getLogger(KafkaProducer.class);

    private final KafkaTemplate<String, String> kafkaTemplate;

    public KafkaProducer(KafkaTemplate<String, String> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }

    public void sendMessage(String message, String topicName) {
        kafkaTemplate.send(topicName, message);
    }

    public void sendMessage(String message, String topicName, String key) {
        kafkaTemplate.send(topicName, key, message);
    }

    public void sendMessageLst(List<JSONObject> jsonLst, String topicName) {
        try {
            for(JSONObject json: jsonLst){
                kafkaTemplate.send(topicName, json.toJSONString());
            }
        }catch (Exception e){
            logger.error(e.getMessage());
        }
    }
}
