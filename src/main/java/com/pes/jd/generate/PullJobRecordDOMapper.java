package com.pes.jd.generate;

import com.pes.jd.generate.PullJobRecordDO;

public interface PullJobRecordDOMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PullJobRecordDO record);

    int insertSelective(PullJobRecordDO record);

    PullJobRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PullJobRecordDO record);

    int updateByPrimaryKey(PullJobRecordDO record);
}