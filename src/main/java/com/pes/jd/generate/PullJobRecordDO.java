package com.pes.jd.generate;

import java.util.Date;

public class PullJobRecordDO {
    private Long id;

    private Long shopId;

    private Date date;

    private Boolean result;

    private Boolean chatPeerFlag;

    private Boolean shopCategoryFlag;

    private Boolean shopSkuFlag;

    private Boolean shopGoodFlag;

    private Boolean shopDsrFlag;

    private Boolean noPayOrderFlag;

    private Boolean orderCreatedFlag;

    private Boolean orderModifyFlag;

    private Boolean orderPresaleFlag;

    private Boolean orderEvaluationFlag;

    private Boolean shopPvUvFlag;

    private Boolean orderRefundApplyFlag;

    private Boolean orderRefundCheckFlag;

    private Boolean ascOrderRefundApplyFlag;

    private Boolean ascOrderRefundCheckFlag;

    private Boolean leaveMsgFlag;

    private Boolean csSendEvalFlag;

    private Boolean updateCsEvalFlag;

    private Boolean csEvalFlag;

    private Boolean orderRemarkFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Boolean getChatPeerFlag() {
        return chatPeerFlag;
    }

    public void setChatPeerFlag(Boolean chatPeerFlag) {
        this.chatPeerFlag = chatPeerFlag;
    }

    public Boolean getShopCategoryFlag() {
        return shopCategoryFlag;
    }

    public void setShopCategoryFlag(Boolean shopCategoryFlag) {
        this.shopCategoryFlag = shopCategoryFlag;
    }

    public Boolean getShopSkuFlag() {
        return shopSkuFlag;
    }

    public void setShopSkuFlag(Boolean shopSkuFlag) {
        this.shopSkuFlag = shopSkuFlag;
    }

    public Boolean getShopGoodFlag() {
        return shopGoodFlag;
    }

    public void setShopGoodFlag(Boolean shopGoodFlag) {
        this.shopGoodFlag = shopGoodFlag;
    }

    public Boolean getShopDsrFlag() {
        return shopDsrFlag;
    }

    public void setShopDsrFlag(Boolean shopDsrFlag) {
        this.shopDsrFlag = shopDsrFlag;
    }

    public Boolean getNoPayOrderFlag() {
        return noPayOrderFlag;
    }

    public void setNoPayOrderFlag(Boolean noPayOrderFlag) {
        this.noPayOrderFlag = noPayOrderFlag;
    }

    public Boolean getOrderCreatedFlag() {
        return orderCreatedFlag;
    }

    public void setOrderCreatedFlag(Boolean orderCreatedFlag) {
        this.orderCreatedFlag = orderCreatedFlag;
    }

    public Boolean getOrderModifyFlag() {
        return orderModifyFlag;
    }

    public void setOrderModifyFlag(Boolean orderModifyFlag) {
        this.orderModifyFlag = orderModifyFlag;
    }

    public Boolean getOrderPresaleFlag() {
        return orderPresaleFlag;
    }

    public void setOrderPresaleFlag(Boolean orderPresaleFlag) {
        this.orderPresaleFlag = orderPresaleFlag;
    }

    public Boolean getOrderEvaluationFlag() {
        return orderEvaluationFlag;
    }

    public void setOrderEvaluationFlag(Boolean orderEvaluationFlag) {
        this.orderEvaluationFlag = orderEvaluationFlag;
    }

    public Boolean getShopPvUvFlag() {
        return shopPvUvFlag;
    }

    public void setShopPvUvFlag(Boolean shopPvUvFlag) {
        this.shopPvUvFlag = shopPvUvFlag;
    }

    public Boolean getOrderRefundApplyFlag() {
        return orderRefundApplyFlag;
    }

    public void setOrderRefundApplyFlag(Boolean orderRefundApplyFlag) {
        this.orderRefundApplyFlag = orderRefundApplyFlag;
    }

    public Boolean getOrderRefundCheckFlag() {
        return orderRefundCheckFlag;
    }

    public void setOrderRefundCheckFlag(Boolean orderRefundCheckFlag) {
        this.orderRefundCheckFlag = orderRefundCheckFlag;
    }

    public Boolean getAscOrderRefundApplyFlag() {
        return ascOrderRefundApplyFlag;
    }

    public void setAscOrderRefundApplyFlag(Boolean ascOrderRefundApplyFlag) {
        this.ascOrderRefundApplyFlag = ascOrderRefundApplyFlag;
    }

    public Boolean getAscOrderRefundCheckFlag() {
        return ascOrderRefundCheckFlag;
    }

    public void setAscOrderRefundCheckFlag(Boolean ascOrderRefundCheckFlag) {
        this.ascOrderRefundCheckFlag = ascOrderRefundCheckFlag;
    }

    public Boolean getLeaveMsgFlag() {
        return leaveMsgFlag;
    }

    public void setLeaveMsgFlag(Boolean leaveMsgFlag) {
        this.leaveMsgFlag = leaveMsgFlag;
    }

    public Boolean getCsSendEvalFlag() {
        return csSendEvalFlag;
    }

    public void setCsSendEvalFlag(Boolean csSendEvalFlag) {
        this.csSendEvalFlag = csSendEvalFlag;
    }

    public Boolean getUpdateCsEvalFlag() {
        return updateCsEvalFlag;
    }

    public void setUpdateCsEvalFlag(Boolean updateCsEvalFlag) {
        this.updateCsEvalFlag = updateCsEvalFlag;
    }

    public Boolean getCsEvalFlag() {
        return csEvalFlag;
    }

    public void setCsEvalFlag(Boolean csEvalFlag) {
        this.csEvalFlag = csEvalFlag;
    }

    public Boolean getOrderRemarkFlag() {
        return orderRemarkFlag;
    }

    public void setOrderRemarkFlag(Boolean orderRemarkFlag) {
        this.orderRemarkFlag = orderRemarkFlag;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }
}