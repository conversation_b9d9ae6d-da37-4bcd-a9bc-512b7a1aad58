package com.pes.jd.mapper;


import com.pes.jd.model.DO.JobPullApiRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface JobPullApiRecordMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByShopIdAndDate(@Param("shopId")Long shopId, @Param("date")Date date, @Param("type")byte type, @Param("tableName")String tableName);

    int insert(JobPullApiRecordDO record);

    int insertJobPullApiRecordDO(@Param("record")JobPullApiRecordDO record, @Param("tableName")String tableName);

    int insertSelective(JobPullApiRecordDO record);

    JobPullApiRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JobPullApiRecordDO record);

    int updateByPrimaryKey(JobPullApiRecordDO record);
}