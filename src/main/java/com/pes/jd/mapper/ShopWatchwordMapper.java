package com.pes.jd.mapper;

import com.pes.jd.model.DO.ShopWatchwordDO;
import com.pes.jd.model.DTO.ShopWatchwordDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShopWatchwordMapper {

    int insertShopWatchword(ShopWatchwordDO record);

    int batchInsertShopWatchword(@Param("tableName")String tableName,@Param("shopWatchwordLst") List<ShopWatchwordDO> shopWatchwordLst);
    
    int deleteShopWatchwordById(Integer id);
    
	int deleteByshopIdAndDate(
			@Param("shopId") Long shopId, 
			@Param("date") Date date, 
			@Param("tableName")String tableName);

    int deleteByshopIdAndDate(@Param("shopId") Long shopId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
    
    int updateShopWatchwordById(ShopWatchwordDO record);
    
    ShopWatchwordDO getShopWatchwordById(Integer id);

	List<ShopWatchwordDTO> selectShopWatchwordLstByShopAndDate(@Param("shopId")Long shopId,@Param("tableName")String tableName, @Param("date")Date date);


}