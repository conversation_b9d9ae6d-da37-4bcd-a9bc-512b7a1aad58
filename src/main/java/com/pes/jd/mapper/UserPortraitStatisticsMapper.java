package com.pes.jd.mapper;

import com.pes.jd.model.DO.UserPortraitStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface UserPortraitStatisticsMapper {


    /**
     * 插入用户画像统计数据
     * @param statistics 用户画像统计数据
     * @param tableName 表名
     * @return 插入行数
     */
    int insertUserPortraitStatistics(@Param("statistics") UserPortraitStatistics statistics,
                                     @Param("tableName") String tableName);

    /**
     * 删除用户画像统计数据
     * @param shopId 店铺ID
     * @param startDate 开始日期
     * @param tableName 表名
     * @return 删除行数
     */
    int deleteUserPortraitStatistics(@Param("shopId") Long shopId,
                                     @Param("startDate") Date startDate,
                                     @Param("tableName") String tableName);

}
