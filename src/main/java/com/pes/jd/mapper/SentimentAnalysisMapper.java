package com.pes.jd.mapper;

import com.pes.jd.model.DO.SentimentAnalysisDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SentimentAnalysisMapper {

	int batchInsertSentimentAnalysis(@Param("shopId") Long shopId,
									 @Param("sentimrntAnalysisList") List<SentimentAnalysisDO> sentimrntAnalysisList,
									 @Param("tableName") String tableName);

	int batchDeleteSentimentAnalysisByDate(	@Param("shopId") Long shopId,
											@Param("date") Date date,
                                         	@Param("tableName") String tableName);

}
  
