package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.InterfaceControlDO;

import java.util.List;

public interface InterfaceControlMapper {
    int deleteByPrimaryKey(Long id);

    int insert(InterfaceControlDO record);

    int insertSelective(InterfaceControlDO record);

    InterfaceControlDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(InterfaceControlDO record);

    int updateByPrimaryKey(InterfaceControlDO record);

    List<InterfaceControlDO> selectAll();
}