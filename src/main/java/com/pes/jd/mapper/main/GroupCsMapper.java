package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.GroupCs;
import com.pes.jd.model.DTO.GroupCsDTO;
import com.pes.jd.model.Param.GroupParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface GroupCsMapper {
	int insertGroupCs(GroupCs record);

	int batchInsertGroupcs(List<GroupCs> gcsLst);

	int deleteGroupCsByGroupId(Long groupId);

	int deleteGroupCsByGroupIdsAndCsId(@Param("groupIds") List<Long> groupIds, @Param("nick") String nick);

	int deleteGroupCsByNickByShopId(@Param("shopId") Long shopId, @Param("nick") String nick);

	int updateGroupCsBySelective(GroupCs record);

	int updateGroupCsByshopIdByNick(@Param("shopId") Long shopId, @Param("nick") String nick, @Param("groupId") Long groupId);

	GroupCsDTO	getGroupCsByGroupIdAndCsId(@Param("groupId") Long groupId, @Param("nick") String nick);

	List<GroupCsDTO> selectGroupCsByGroupId(@Param("groupId") Long groupId);

    List<Map<String,Object>> selectGroupCsByGroupIdMap(Map<String, Object> param);

    List<GroupCsDTO> selectGroupCsByShopId(@Param("shopId") Long shopId);

    List<GroupCsDTO> selectGroupCsByGroupParam(@Param("param") GroupParam groupParam);

    List<GroupCsDTO> selectGroupCsByCsNick(@Param("shopId") Long shopId, @Param("csNick") String csNick);

    int updateOldCsNickByNewCsNick(@Param("groupCsLst") List<GroupCsDTO> groupCsLst);

    int deleteGroupCsByCsNickLst(@Param("shopId") Long shopId, @Param("csNickLst") List<String> csNickLst);


}