package com.pes.jd.mapper.main;

import com.pes.jd.model.DTO.ShopGroupRequestDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopGroupRequestMapper {
	
	List<ShopGroupRequestDTO> selectShopGroupRequests(@Param("shopId") Long shopId,
                                                      @Param("shopGroupId") String shopGroupId);

	ShopGroupRequestDTO checkRequestExistOrNot(@Param("fromId") Long fromId, @Param("toId") Long toId);

	int InviteAgain(ShopGroupRequestDTO shopGroupRequest);

	int insertShopGroupRequest(ShopGroupRequestDTO shopGroupRequest);

	int updateShopGroupRequestByRequestId(ShopGroupRequestDTO shopGroupRequest);

	int updateShopGroupIdByFromShopIdByToShopId(@Param("fromId") Long shopId,
                                                @Param("shopIdList") List<String> shopIdList,
                                                @Param("shopGroupId") String shopGroupId);

	List<ShopGroupRequestDTO> selectJoinGroupList(@Param("shopId") Long shopId, @Param("status") String status);

	List<ShopGroupRequestDTO> selectShopAccreditByMainShop(@Param("shopId") Long shopId);

	ShopGroupRequestDTO selectShopGroupRequestsByRequestId(@Param("requsetId") String requestId);

}
