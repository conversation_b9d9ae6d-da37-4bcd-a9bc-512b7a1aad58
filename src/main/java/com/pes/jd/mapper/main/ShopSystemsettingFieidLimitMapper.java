package com.pes.jd.mapper.main;

import com.pes.jd.model.DTO.ShopSystemsettingFieidLimitDTO;
import org.apache.ibatis.annotations.Param;

public interface ShopSystemsettingFieidLimitMapper {
    int updateEnquiryAndOutStockValidDurationTimeByShopId(ShopSystemsettingFieidLimitDTO shopSysSettingFieid);

    ShopSystemsettingFieidLimitDTO selectByShopId(@Param("shopId") Long shopId);

    int insert(ShopSystemsettingFieidLimitDTO shopSysSettingFieid);
}
