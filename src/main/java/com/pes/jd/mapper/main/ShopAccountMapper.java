package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.ShopAccount;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShopAccountMapper {

    int insertShopAccount(ShopAccount record);

    int deleteShopAccountById(Long id);

    int updateShopAccountById(ShopAccount record);

    int updateSubUserRole(@Param("nick") String nick, @Param("type") String type);

    List<ShopAccountDTO> selectShopAccountOfShopStatus(@Param("shopId") Long shopId, @Param("nick") String nick, @Param("exactSearch") boolean exactSearch);

    ShopAccount getShopAccountById(Long id);

    ShopAccountDTO searchByNick(Long id);

    int batchInsertSubUsersOfShop(List<ShopAccount> subUsers);

    void deleteShopAccountByShopId(@Param("shopId") String shopId);

    int deleteShopAccountByNick(@Param("nick") String nick);

    List<ShopAccount> selectShopAccountOfShop(@Param("shopId") String shopId);

    List<ShopAccount> getShopAccountByNick(@Param("nick") String nick);

    List<ShopAccountDTO> selectShopAccountByShopIdByNickByStatus(@Param("shopId") Long shopId
            , @Param("nick") String nick, @Param("status") Integer status, @Param("flag") Integer flag, @Param("managerOverlay") Boolean managerOverlay);

    List<ShopAccountDTO> selectShopAccountByShopIdByNickByStatusForCsManager(@Param("shopId") Long shopId
            , @Param("nick") String nick, @Param("status") Integer status);

    ShopAccount getShopMainAccount(@Param("shopId") String shopId);

    ShopAccount getShopMainAccountByShopId(@Param("shopId") String shopId);

    int updateShopMainAccountNick(@Param("shopId") Long shopId, @Param("nick") String nick, @Param("userName") String userName, @Param("modified") Date modified);

    List<ShopAccountDTO> selectShopAccountByShopId(@Param("shopId") Long shopId);
}