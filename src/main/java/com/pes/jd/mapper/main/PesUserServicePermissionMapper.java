package com.pes.jd.mapper.main;


import com.pes.jd.model.DO.PesServicePermission;
import com.pes.jd.model.DO.PesUserServicePermission;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PesUserServicePermissionMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PesUserServicePermission record);

    int insertSelective(PesUserServicePermission record);

    PesUserServicePermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PesUserServicePermission record);

    int updateByPrimaryKey(PesUserServicePermission record);

    int insertByShopAccountsAndPermissions(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts,
                                           @Param("permissions") List<PesServicePermission> permissions);

    int batchInsertByShopAccountsAndPermissions(@Param("permissions") List<PesServicePermission> permissions);

    int deleteByShopAccounts(List<ShopAccountDTO> shopAccounts);

    List<PesUserServicePermission> selectByShopAccounts(List<ShopAccountDTO> shopAccounts);
    
    int deleteUserServicePermissionByCsNick(@Param("csNick") String csNick);
}