package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.ShopRemindWordGoodsDO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordGoodsDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ShopRemindWordGoodsMapper {
    int deleteShopRemindWordGoodsByWordId(@Param("wordId") Long wordId, @Param("type") Long type);


    int batchInsertShopRemindWordGoods(@Param("remindGoodsLst") List<ShopRemindWordGoodsDO> remindGoodsLst);

    List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByWordId(@Param("wordId") Long wordId);

    List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByShopId(@Param("shopId") Long shopId);

    List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByShopIdByRemindIds(@Param("shopId") Long shopId, @Param("remindIds") Set<Long> remindIds, @Param("type") Long type);

    int deleteRemindIdByShopIdAndType(@Param("shopId") Long shopId, @Param("goodIds") Set<Long> goodIds, @Param("remindId") Long remindId, @Param("type") Long type);

    List<ShopRemindWordGoodsDTO> selectRemindIdByShopIdAndType(@Param("shopId") Long shopId, @Param("goodIds") Set<Long> goodIds, @Param("remindId") Long remindId, @Param("type") Long type);

    List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByWordIdAndType(@Param("wordId") Long wordId, @Param("type") Long type);
}