package com.pes.jd.mapper.main;

import com.pes.jd.model.DTO.ShopUrgeDTO;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopUrgeMapper {

    List<ShopUrge> selectUrgeShopByShopIdAndType(@Param("shopId") Long shopId, @Param("type") Integer type);

    ShopUrgeDTO selectUrgeShopByShopId(@Param("shopId") Long shopId);
}