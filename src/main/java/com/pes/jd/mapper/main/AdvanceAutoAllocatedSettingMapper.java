package com.pes.jd.mapper.main;

import com.pes.jd.model.DTO.AdvanceAutoAllocatedSettingDTO;
import org.apache.ibatis.annotations.Param;

public interface AdvanceAutoAllocatedSettingMapper {

    AdvanceAutoAllocatedSettingDTO selectShopAutoAllocatedSettingByShopId(@Param("shopId") Long shopId);

    int insertShopAutoAllocatedSetting(AdvanceAutoAllocatedSettingDTO param);

    int updateShopAutoAllocatedSettingById(AdvanceAutoAllocatedSettingDTO param);

    int updateCloseAdvanceAutoAllocatedByShopId(@Param("shopId") Long shopId, @Param("autoAllocated") Boolean autoAllocated);
}