package com.pes.jd.mapper.main;

import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO;
import org.apache.ibatis.annotations.Param;

public interface ShopSettingBatchRemindReserveMapper {
    int insertSelective(ShopSettingBatchRemindReserveDTO record);

    int updateByShopId(ShopSettingBatchRemindReserveDTO record);

    ShopSettingBatchRemindReserveDTO selectShopSettingBatchRemindReserveByShopId(Long shopId);

    ShopSettingBatchRemindReserveDTO selecttShopSettingBatchRemindByShopId(@Param("shopId") Long shopId);

    void updateBatchRemindByShopIdAndIsRemind(@Param("shopId") Long shopId,
                                              @Param("isRemind") boolean isRemind);
}