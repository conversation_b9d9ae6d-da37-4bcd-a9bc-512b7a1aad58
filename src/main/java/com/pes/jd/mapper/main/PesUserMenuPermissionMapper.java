package com.pes.jd.mapper.main;


import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.model.DO.PesUserMenuPermission;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface PesUserMenuPermissionMapper {

    int deleteByPrimaryKey(Long id);
    int insert(PesUserMenuPermission record);

    int insertSelective(PesUserMenuPermission record);

    PesUserMenuPermission selectByPrimaryKey(Long id);

    PesUserMenuPermission selectByMenuId(@Param("id") Long id, @Param("nick") String nick);

    int selectCountByNick(@Param("nick") String nick);

    int updateByPrimaryKeySelective(PesUserMenuPermission record);

    int updateByPrimaryKey(PesUserMenuPermission record);

    int insertByShopAccountsAndMenus(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts,
                                     @Param("menus") List<PesMenuResource> menus);

    int deleteByShopAccounts(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts);

    List<PesUserMenuPermission> selectByShopAccounts(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts);

    List<PesUserMenuPermission> searchByNicks(@Param("nicks") Set<String> nicks);

    int deleteByMenuResourceId(Long id);

    void deleteByShopAccountsAndMenu(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts, @Param("menus") List<PesMenuResource> menus);

    int deleteMenuServicePermissionByCsNick(@Param("csNick") String csNick);

    int deleteByShopAccountsAndMenus(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts,
                                     @Param("menus") List<PesMenuResource> menus);
    int delete(@Param("record") PesUserMenuPermission record);

    void truncatePermission(@Param("shopAccounts") List<ShopAccountDTO> shopAccounts);
}