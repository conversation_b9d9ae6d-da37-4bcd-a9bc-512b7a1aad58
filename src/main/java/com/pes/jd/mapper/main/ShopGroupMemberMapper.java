package com.pes.jd.mapper.main;

import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopGroupMemberDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopGroupMemberMapper {
	
	List<ShopDTO> selectJoinedGroupShopList(@Param("shopId") Long shopId, @Param("shopGroupId") Long groupId,
                                            @Param("status1") String status1, @Param("status2") String status2);


	int updateShopToShopGroup(@Param("shopId") Long shopId, @Param("shopIdList") List<String> shopIdList, @Param("shopGroupId") String shopGroupId);

	int updateGroupShopStatus(ShopGroupMemberDTO existGroupShop);

	List<ShopDTO> selectShopGroupMemberShops(@Param("shopId") Long shopId, @Param("type") Integer type);

	List<ShopDTO> selectShopGroupMemberShopsStartMutualWatch(@Param("shopIdLst") List<Long> groupShopIdLst, @Param("type") Integer type);

	List<ShopDTO> selectShopGroupMemberShopsStartMutualWatchwithParent(@Param("shopId") Long shopId, @Param("type") Integer type);

	int insertShopGroupMember(ShopGroupMemberDTO shopGroupMemberDTO);

	int deleteShopGroupMember(@Param("shopId") Long shopId, @Param("shopGroupId") Long shopGroupId, @Param("memberShopId") Long memberShopId);

	ShopDTO getMainShopMemberShop(@Param("mainShopId") Long mainShopId, @Param("memberShopId") Long memberShopId);

	List<ShopDTO> getShopsCanBeSwitched(@Param("shopId") Long shopId);

	ShopGroupMemberDTO selectIsExistGroupShop(ShopGroupMemberDTO gm);

}
