package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.PesMenuResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PesMenuResourceMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByName(String name);

    int insert(PesMenuResource record);

    PesMenuResource selectByPrimaryKey(Long id);

    /**
     * 查询shopId为-1  与shopId 为自己家店铺的权限
     *
     * @param menuIds
     * @param shopId
     * @return
     */
    List<PesMenuResource> searchAll(@Param("list") List<Long> menuIds, @Param("shopId") Long shopId, @Param("type") Integer type);

    List<PesMenuResource> searchAllForSelf(@Param("list") List<Long> menuIds, @Param("shopId") Long shopId);



    int updatePermission(PesMenuResource pesMenuResource);

    int updateByPrimaryKey(PesMenuResource record);


    List<PesMenuResource> searchPerson();

    List<PesMenuResource> selectByName(@Param("name") String name);

    List<PesMenuResource> selectByNameTitle(@Param("name") String name, @Param("title") String title);

    Integer getMaxSort(Long parentId);

    long selectSystemJurisdicteIdByTitle(@Param("title") String menuTitle, @Param("shopId") long shopId);

    List<PesMenuResource> queryMenuByIds(@Param("list") List<Long> ids);
}