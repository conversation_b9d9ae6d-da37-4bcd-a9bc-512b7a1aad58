package com.pes.jd.mapper.main;


import com.pes.jd.model.DO.PesUserOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface PesUserOperationLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PesUserOperationLog record);

    int insertSelective(PesUserOperationLog record);

    PesUserOperationLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PesUserOperationLog record);

    int updateByPrimaryKey(PesUserOperationLog record);

    List<PesUserOperationLog> searchOperationLogByTypeTimeNick(
            @Param("shopId") String shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("nick") String nick,
            @Param("optType") String optType);

    int getUserOperationLogByNickAndShop(
            @Param("shopId") String shopId,
            @Param("nick") String nick,
            @Param("type") String optType
    );

    int getUserOperationLogByNickAndShopAndTime(
            @Param("shopId") String shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("type") String type,
            @Param("nick") String nick
    );
}