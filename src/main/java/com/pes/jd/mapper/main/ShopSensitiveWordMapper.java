package com.pes.jd.mapper.main;

import com.pes.jd.model.DTO.ShopSensitiveWordDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ShopSensitiveWordMapper {

    List<String> selectSensitiveWordByShopId(@Param("shopId") Long shopId);

    List<ShopSensitiveWordDTO> batchLikeByShopIdForLst(@Param("shopId") Long shopId,
                                                       @Param("sensitiveWord") String sensitiveWord);

    List<ShopSensitiveWordDTO> selectSensitiveWordByShopIdForLst(@Param("shopId") Long shopId);

    int selectExistenceSensitiveWordByShopId(@Param("shopId") Long shopId);

    ShopSensitiveWordDTO selectSensitiveWordNewestByShopId(@Param("shopId") Long shopId);

    int updateSensitiveWordByShopId(@Param("shopId") Long shopId,
                                    @Param("modifiedBy") Long modifiedBy,
                                    @Param("record") Map<String, String> record,
                                    @Param("date") Date date);

    int updateSensitiveWordDate(@Param("shopId") Long shopId,
                                @Param("field") String field,
                                @Param("date") Date date);

    int insertSensitiveWord(@Param("shopId") Long shopId,
                            @Param("modifiedBy") Long modifiedBy,
                            @Param("record") List<String> record,
                            @Param("date") Date date);

    int deleteSensitiveWordById(@Param("shopId") Long shopId,
                                @Param("record") List<String> record);
}
