package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.PesUserAnalysDO;
import com.pes.jd.model.DTO.PesUserAnalysDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**  
 * ClassName:CsManagerMapper <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月3日 上午10:46:03 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface PesUserAnalysMapper {

	PesUserAnalysDTO selectUserAnalysByDate(@Param("date") Date date);
	int insertUserAnalys(@Param("pesUserAnalysDO") PesUserAnalysDO pesUserAnalysDO);
	int updateUserAnalys(@Param("pesUserAnalysDTO") PesUserAnalysDTO pesUserAnalysDTO);

}
  
