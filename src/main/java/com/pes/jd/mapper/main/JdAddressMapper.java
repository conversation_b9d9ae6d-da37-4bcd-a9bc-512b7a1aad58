package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.GoodsFilter;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.DTO.GoodsFilterDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface JdAddressMapper {

	List<JdAddress> getAllJdAddressData();

	int batchInsert(@Param("list") List<JdAddress> addressList);

	int deleteAll();

	LocalDateTime getLatestCreateTime();
}