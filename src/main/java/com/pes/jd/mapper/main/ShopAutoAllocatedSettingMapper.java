package com.pes.jd.mapper.main;

import com.pes.jd.model.DO.ShopAutoAllocatedSettingDO;
import com.pes.jd.ms.domain.Data.master.ShopAutoAllocatedSettingDTO;
import org.apache.ibatis.annotations.Param;

public interface ShopAutoAllocatedSettingMapper {
    int deleteShopAutoAllocatedSetting(Long id);
    int insertShopAutoAllocatedSetting(ShopAutoAllocatedSettingDO record);
    ShopAutoAllocatedSettingDO selectShopAutoAllocatedSetting(Long id);
    int updateShopAutoAllocatedSettingById(ShopAutoAllocatedSettingDO record);
    ShopAutoAllocatedSettingDTO selectShopAutoAllocatedSettingByShopId(@Param("shopId") Long shopId);
    int updateCloseAutoAllocatedByShopId(@Param("shopId") Long shopId, @Param("isAutoAllocated") Boolean isAutoAllocated);
}