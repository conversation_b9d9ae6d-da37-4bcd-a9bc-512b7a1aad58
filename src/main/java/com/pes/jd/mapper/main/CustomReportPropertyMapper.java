package com.pes.jd.mapper.main;


import com.pes.jd.model.DO.CustomReportPropertyDO;
import com.pes.jd.model.DTO.CustomReportPropertyDTO;

import java.util.List;
import java.util.Set;

public interface CustomReportPropertyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CustomReportPropertyDO record);

    int insertBatch(List<CustomReportPropertyDTO> record);

    int insertSelective(CustomReportPropertyDO record);

    CustomReportPropertyDO selectByPrimaryKey(Long id);

    List<CustomReportPropertyDTO> searchAllByReportId(Set<Long> reportId);

    int updateByPrimaryKeySelective(CustomReportPropertyDO record);

    int updateByPrimaryKey(CustomReportPropertyDO record);

    int deleteByReportId(Long reportId);


}