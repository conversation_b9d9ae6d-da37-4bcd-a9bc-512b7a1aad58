package com.pes.jd.mapper;

import com.pes.jd.model.DO.SlientGoodsSaleIndexDetailDO;
import com.pes.jd.model.DTO.SlientGoodsSaleIndexDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SlientGoodsSaleIndexDetailMapper {
    int deleteSlientGoodsSaleIndexDetail(Long id);

    int insertSlientGoodsSaleIndexDetail(SlientGoodsSaleIndexDetailDO record);

    int insertSelective(SlientGoodsSaleIndexDetailDO record);

    SlientGoodsSaleIndexDetailDO selectSlientGoodsSaleIndexDetailById(Long id);

    int updateSlientGoodsSaleIndexDetail(SlientGoodsSaleIndexDetailDO record);

    int batchInsertSlientGoodsSaleIndexDetail(@Param("slienGoodsDetailLst") List<SlientGoodsSaleIndexDetailDO> slienGoodsDetailLst,@Param("tableName") String tableName);
    
    int deleteSlientGoodsSaleIndexDetailByShopIdByDate(@Param("shopId")Long shopId,@Param("startDate") Date startDate,@Param("endDate") Date endDate,@Param("tableName") String tableName);

    List<SlientGoodsSaleIndexDetailDTO>selectSlientGoodsSaleIndexByShopIdByDate(@Param("shopId")Long shopId,@Param("startDate") Date startDate,@Param("endDate") Date endDate,@Param("tableName") String tableName);
}