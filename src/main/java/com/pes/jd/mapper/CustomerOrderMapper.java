package com.pes.jd.mapper;


import com.pes.jd.model.DTO.*;
import org.apache.ibatis.annotations.Param;


public interface CustomerOrderMapper {
    int insertCustomerOrder(@Param("tableName") String tableName, @Param("order") OrderDTO order);
    
    int selectOrderById(@Param("oid") Long oid, @Param("tableName") String tableName);

    int deleteOrderByOrderId(@Param("oid") Long oid, @Param("tableName") String tableName);

	int insertOrderDetail(@Param("tableName")String tableName, @Param("orderDetail")OrderDetailInfoDTO orderDetailInfoDTO);

	int delOrderDetail(@Param("tableName")String tableName ,@Param("orderDetail") OrderDetailInfoDTO orderDetailInfoDTO);

}