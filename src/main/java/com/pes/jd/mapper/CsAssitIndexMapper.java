package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsAssitIndexDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface CsAssitIndexMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByShopDate(@Param("date") Date date,@Param("shopId") Long shopId,@Param("tableName") String tableName);

    int insert(@Param("record") CsAssitIndexDO record,@Param("tableName")String tableName);

    int insertSelective(CsAssitIndexDO record);

    CsAssitIndexDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CsAssitIndexDO record);

    int updateByPrimaryKey(CsAssitIndexDO record);
}