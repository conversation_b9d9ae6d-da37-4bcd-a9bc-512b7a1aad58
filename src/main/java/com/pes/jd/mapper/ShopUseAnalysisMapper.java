package com.pes.jd.mapper;

import com.pes.jd.model.DO.ShopUseAnalysisDO;
import com.pes.jd.ms.domain.Data.rtsub.ShopUseConversion;
import com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface ShopUseAnalysisMapper {
    int deleteShopUseAnalysisByshopIdByDate(@Param("shopId") Long shopId, @Param("date") Date date, @Param("tableName") String tableName);

    int insertShopUseAnalysis(@Param("tableName")  String tableName,@Param("record") ShopUseAnalysisDO record);


    ShopUseAnalysis selectShopUseAnalysisByShopIdByDate(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("tableName")  String tableName);

    int  updateShopUseShopSaleAmountAndScreenNum(@Param("shopId") Long shopId,
                                                 @Param("date") Date date,
                                                 @Param("num") Integer num,
                                                 @Param("shopSaleAmount") Double shopSaleAmount,
                                                 @Param("tableName")  String tableName);

   int updateShopUseShopUrgeByShopIdByDate( @Param("tableName")  String tableName,@Param("record") ShopUseAnalysisDO record);
}