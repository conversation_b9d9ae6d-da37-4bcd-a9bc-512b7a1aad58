package com.pes.jd.mapper;

import com.pes.jd.model.DTO.BuyerOrderDTO;
import com.pes.jd.model.DTO.OrderOutStockTimeDTO;
import com.pes.jd.model.DTO.PresaleOrderDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface PresaleOrderMapper {

    int batchDeleteShopPresaleOrderByDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    int batchInsertPresaleOrderByPresaleOrderList(
            @Param("shopId") Long shopId,
            @Param("presaleList") List<PresaleOrderDTO> presaleList,
            @Param("tableName") String tableName);

    int batchUpdateShopPresaleOrderByPresaleOrderList(
            @Param("shopId") Long shopId,
            @Param("presaleOrderLst") List<PresaleOrderDTO> presaleOrderLst,
            @Param("tableName") String tableName);

    int deleteShopPresaleOrderByOrderIdLst(@Param("tids") List<Long> tids, @Param("tableName") String tableName);


    List<Long> selectPresaleOrderByOrderLst(
            @Param("shopId") Long shopId,
            @Param("orderIdList") List<Long> orderIdList,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<PresaleOrderDTO> selectPresaleOrderByOrderIdAndOrderPayType(
            @Param("shopId") Long shopId,
            @Param("orderIdList") List<Long> orderIdList,
            @Param("orderPayType") int orderPayType,
            @Param("tableName") String tableName);


    List<BuyerOrderDTO> selectPresaleOrderInfoByOrderLst(
            @Param("orderIdLst") List<Long> orderIdList,
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName);

    List<PresaleOrderDTO> selectPresaleOrderInfoByOrderLstAll(
            @Param("orderIdLst") List<Long> orderIdList,
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName);

    List<BuyerOrderDTO> selectShopCreatedPresaleOrderLstByBuyersAndDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("filterOrderIds") List<Long> filterOrderIds ,
            @Param("tableName") String tableName);


    List<BuyerOrderDTO> selectShopBargainPaidOrderLstByBuyersAndDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("filterOrderIds") Set<Long> filterOrderIds,
            @Param("tableName") String tableName);


    List<BuyerOrderDTO> selectShopBargainPaidOrderLstByBuyersAndDateForCalEnquiry(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("tableName") String tableName);


    List<BuyerOrderDTO> selectShopBalancePaidOrderLstByBuyersAndDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("tableName") String tableName);


    int updateBatchPresaleOrderByOrderId(
            @Param("orderList") List<OrderOutStockTimeDTO> orderList,
            @Param("tableName") String tableName);

    List<BuyerOrderDTO> selectShopBalancePaidOrderLstByBuyersAndDateForPesBind(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("tableName") String tableName);

    List<Long> selectDownPaymentOrderIdByCreateTime(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<Long> selectPayEarnestMoneyPresaleOrderByOrderLst(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName,
            @Param("orderIdList") List<Long> orderIdList);

    List<Long> selectOrderIdByShopIdAndBalanceTime(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<PresaleOrderDTO> selectByTimeAndBuyerNicksForPresalePerformance(
            @Param("shopId") Long shopId,
            @Param("timeType") String timeType,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerSet") Set<String> buyerSet,
            @Param("orderIds") Set<Long> orderIds,
            @Param("skuIds") Set<Long> skuIds,
            @Param("tableName") String tableName);

    List<BuyerOrderDTO> selectFullPaymentOrderLstByBuyersAndDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("tableName") String tableName);

    List<PresaleOrderDTO> selectPresaleOrderByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderIdList") List<Long> orderIdList,
            @Param("tableName") String tableName);

    List<PresaleOrderDTO> selectShopOrderBlancePayTimeInAdjustDate(@Param("shopId") Long shopId,
                                                                   @Param("orderIdList") List<Long> orderIdList,
                                                                   @Param("tableName") String tableName,
                                                                   @Param("startDate") Date startDate,
                                                                   @Param("endDate") Date endDate);

    List<PresaleOrderDTO> selectOrderIdByShopIdAndPayBalanceTime(@Param("shopId") Long shopId,
                                                      @Param("startDate") Date startDate,
                                                      @Param("endDate") Date endDate,
                                                      @Param("tableName") String tableName);
    List<PresaleOrderDTO> selectOrderIdByShopIdAndPayBalanceTime2(@Param("shopId") Long shopId,
                                                                 @Param("startDate") Date startDate,
                                                                 @Param("endDate") Date endDate,
                                                                 @Param("tableName") String tableName);

    List<Long> selectOrderIdsUnbalance(
            @Param("shopId") Long shopId,
            @Param("startDate")Date startDate,
            @Param("endDate")Date endDate,
            @Param("tableName")String tableName);

    List<Long> selectOrderIdsByCreated(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<Long> selectShopBargainNoBalanceOdIds(
            @Param("shopId")Long shopId,
            @Param("startDate") Date adjustPaidStartDate,
            @Param("endDate") Date adjustPaidEndDate,
            @Param("tableName") String tableName);
}


