package com.pes.jd.mapper;

import com.pes.jd.model.DO.OrderDetailDO;
import com.pes.jd.model.DO.ShopGoodsReviewDO;
import com.pes.jd.model.DTO.OrderDetailDTO;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;


public interface OrderDetailMapper {
	
	int insertOrderCancelGoodsSku(@Param("tableName")String tableName, @Param("orderDetailList")List<OrderDetailDTO> orderDetailList);

    int insertOrderDetailByFile(@Param("filePath")String filePath, @Param("tableName")String tableName);

    int deleteOrdersDetailByOids(@Param("oids")List<Long> oids, @Param("tableName")String tableName);
    
    int deleteOrderCancelGoodsSkuByOid(@Param("oid")Long oid, @Param("tableName")String tableName);
    
    List<OrderDetailDO>selectOrderGoodsSkuByTableNameAndCreated(@Param("tableName")String tableName, @Param("shopId")Long shopId, @Param("startDate")Date startDate, @Param("endDate")Date endDate);
    
    List<OrderDetailDTO>selectOrderCreatedDetailByCreated(@Param("shopId")Long shopId ,@Param("tableName")String tableName,@Param("skuIdSet") Set<Long> skuIdSet,@Param("startDate")Date startDate, @Param("endDate")Date endDate);
    
    List<OrderDetailDTO>selectOrderPaidDetailByPayTime(@Param("shopId")Long shopId ,@Param("tableName")String tableName,@Param("startDate")Date startDate, @Param("endDate")Date endDate);
   
    List<OrderDetailDTO> selectOrderGoodsSkuByShopIdLstAndDate(@Param("shopId")Long shopId ,
    		@Param("tableNames")List<DateRangeParam> tableNames,
    		@Param("orderIdSet") Collection<Long> orderIdSet,
    		@Param("orderTableNames")List<DateRangeParam> orderTableNames,
			@Param("startDate") Date startDate,
			@Param("endDate") Date endDate);
    
    List<OrderDetailDTO> selectSlientOrderGoodsSkuByOrderIdLstAndDate(@Param("shopId")Long shopId, @Param("tableName")String tableName,@Param("orderIdLst") List<Long> orderIdLst,@Param("startDate")Date startDate, @Param("endDate")Date endDate);

	List<ShopGoodsReviewDO> selectOrderIdListBySkuidList(@Param("shopId")Long shopId, 
			@Param("skuIdSets")Set<Long> skuIdSets,
			@Param("tableName")String tableName);
	
	int updateOrderByOrderId(@Param("shopId")Long shopId,
			@Param("orderId") Long orderId,
			@Param("outStockTime") Date outStockTime,
			@Param("tableName")String tableName);
	
	List<OrderDetailDTO> selectShopOrderDetailForGoodsAnalysis(
			@Param("shopId")Long shopId,
			@Param("otTableNames") List<DateRangeParam> otTableNames);

	
	List<OrderDetailDTO> selectOrderGoodsSkuByShopIdLstAndCreateDate(@Param("shopId")Long shopId ,
    		@Param("tableName")String tableName,
    		@Param("orderIdSet")Set<Long> orderIdSet,
    		@Param("startDate")Date startDate,
    		@Param("endDate")Date endDate
    	);

    List<OrderDetailDTO> selectOrderGoodsSkuByShopIdLstAndDateNew(@Param("shopId") Long shopId,
																  @Param("tableNames") List<DateRangeParam> tableNames,
																  @Param("yesterDaySkuSet") Set<Long> yesterDaySkuSet,
																  @Param("orderTableNames") List<DateRangeParam> orderTableNames,
																  @Param("startDate")Date startDate,
																  @Param("endDate")Date endDate);

	List<OrderDetailDTO>  selectOrderGoodsSkuByOrderIdLst
			(@Param("shopId")Long shopId ,
			 @Param("tableNames")List<DateRangeParam> tableNames,
			 @Param("orderIdSet") Collection<Long> orderIdSet,
			 @Param("orderTableNames")List<DateRangeParam> orderTableNames);


	List<OrderDetailDTO> selectOrderGoodSkuByOrderIdLst(
			@Param("shopId") Long shopId,
			@Param("orderIdLst") List<Long> orderIdLst,
			@Param("tableName") String tableName);
}