package com.pes.jd.mapper;

import com.pes.jd.model.DO.ShopTeamTOrderPerformanceDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShopTeamTOrderPerformanceMapper {

    int insertShopTeamTOrderPerformances(@Param("shopId")Long shopId,
    		@Param("recordLst")List<ShopTeamTOrderPerformanceDO> recordLst,
    		@Param("tableName")String tableName);

    int deleteShopTeamTOrderPerformance(@Param("shopId")Long shopId,
    		@Param("date")Date date,
    		@Param("tableName")String tableName);

    int deleteShopTeamTOrderPerformances(@Param("shopId")Long shopId,
    		@Param("startDate")Date startDate,
    		@Param("endDate")Date endDate,
    		@Param("tableName")String tableName);

    int updateShopTeamTOrderPerformanceSelective(@Param("record")ShopTeamTOrderPerformanceDO record,
    		@Param("tableName")String tableName);

	int updateShopTeamSaleAndOutStackData(
			@Param("record") ShopTeamTOrderPerformanceDO record,
			@Param("tableName") String tableName);

	int updateShopTeamOutStackData(
			@Param("record") ShopTeamTOrderPerformanceDO record,
			@Param("tableName") String tableName);

    ShopTeamTOrderPerformanceDO selectShopTeamTOrderPerformanceForUpdate(
			@Param("shopId")Long shopId,
			@Param("date")Date date,
    		@Param("tableName")String tableName);

	ShopTeamTOrderPerformanceDO selectShopTeamTOrderPerformance(
			@Param("shopId")Long shopId,
			@Param("date")Date date,
			@Param("tableName")String tableName);
}