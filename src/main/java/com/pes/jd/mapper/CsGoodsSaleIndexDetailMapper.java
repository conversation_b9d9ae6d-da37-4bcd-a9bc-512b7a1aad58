package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsGoodsSaleIndexDetailDO;
import com.pes.jd.model.DTO.CsGoodsSaleIndexDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsGoodsSaleIndexDetailMapper {
    int deleteCsGoodsSaleIndexDetailById(Long id);

    int insertCsGoodsSaleIndexDetail(CsGoodsSaleIndexDetailDO record);

    CsGoodsSaleIndexDetailDO selectCsGoodsSaleIndexDetailById(Long id);

    int updateCsGoodsSaleIndexDetail(CsGoodsSaleIndexDetailDO record);

    int  batchInsertCsGoodsSaleIndexDetail(@Param("tableName") String tableName,@Param("csSaleDetailLst") List<CsGoodsSaleIndexDetailDO> csSaleDetailLst);

    int deleteCsGoodsSaleIndexDetailByShopIdByDate(@Param("shopId") Long shopId,@Param("startDate") Date startDate,@Param("endDate") Date endDate,@Param("tableName") String tableName);
    
    List<CsGoodsSaleIndexDetailDTO> selectCsGoodsSaleIndexDetailByCsNickByDate(@Param("shopId") Long shopId,
    		@Param("csNick") String csNick,
    		@Param("startDate") Date startDate,
    		@Param("endDate") Date endDate,
    		@Param("tableName") String tableName);
}