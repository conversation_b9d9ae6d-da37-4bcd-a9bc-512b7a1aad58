package com.pes.jd.mapper;

import com.pes.jd.model.DO.OrderFilter;
import com.pes.jd.model.DTO.OrderFilterDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderFilterMapper {
	int insertOrderFilter(OrderFilter record);

	int deleteOrderFilterById(Long id);

	int updateOrderFilterBySelective(OrderFilter record);

	OrderFilter getOrderFilterById(Long id);
	
    int persistOrderFilterByFile(@Param("filePath")String filePath, @Param("tableName") String tableName);
	
	List<Long> selectIdsByOrderFilterIds(@Param("oids")List<Long> oids, @Param("tableName")String tableName, @Param("shopId")Long shopId);
	
	int deleteOrdersFilterByIds(@Param("ids")List<Long> ids, @Param("tableName")String tableName, @Param("shopId")Long shopId);
	
	int persistOrderFilters(@Param("orders")List<OrderFilterDTO> orders, @Param("tableName")String tableName);
	
	List<OrderFilterDTO> queryPesOrdersByShopId(@Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("tableName")String tableName, @Param("shopId") Long shopId);

	List<Long> selectOrderFilterByOrderIds(@Param("oids")List<Long> oids, @Param("tableName")String tableName, @Param("shopId")Long shopId);
	
}