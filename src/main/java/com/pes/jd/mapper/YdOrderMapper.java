package com.pes.jd.mapper;

import com.pes.jd.model.DTO.YdOrderDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface YdOrderMapper {

    List<YdOrderDTO> selectOrderByOrderId(@Param("orderIds") List<Long> orderIds);

    List<Long> selectOrderIdByVendIdAndDate(@Param("venderId") Long venderId,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate,
                                        @Param("type") Integer type);


}