package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsPerformanceDO;
import com.pes.jd.model.DTO.CsPerformanceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsPerformanceMapper {
	
	int batchInsertCsPerformance(@Param("shopId")Long shopId, 
			@Param("date")Date date, @Param("csDayPerformanceLst")List<CsPerformanceDO> csDayPerformanceLst, 
			@Param("tableName")String tableName);

	int deleteCsPerformanceById(Long id);
	
	int deleteShopCsPerformanceByDate(@Param("shopId")Long shopId, @Param("date")Date date, 
			@Param("tableName")String tableName);
	
	int deleteShopCsPerformanceByDateRange(@Param("shopId")Long shopId, 
			@Param("startDate")Date startDate, @Param("endDate")Date endDate, 
			@Param("tableName")String tableName);

	int updateCsPerformanceById(CsPerformanceDO csPerformance);
	
	CsPerformanceDO getCsPerformanceById(Long id);

	List<CsPerformanceDTO> searchByDateShopCs(
			@Param("nicks") List<String> nicks,
			@Param("shopId") Long shopId,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("groupBy")String groupBy,
			@Param("tableName")String tableName
	);


}