package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsChatpeerDO;
import com.pes.jd.model.DTO.*;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface CsChatpeerMapper {

    int insertCsChatpeer(CsChatpeerDO record);

    int batchInsertChatpeer(@Param("tableName") String tableName, @Param("chatpeerLst") List<CsChatpeerDO> chatpeerLst);

    int deleteCsChatpeerById(Long id);

    int deleteChatpeerByShopIdAndDate(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("tableName") String tableName);

    int deleteChatpeerByShopIdAndDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    int updateChatpeerInfoForCommonChat(@Param("chatpeerLst") List<CommonCsChatpeerDTO> chatpeerLst,
                                        @Param("tableName") String tableName);

    int updateChatpeersCrossChat(@Param("shopId") Long shopId,
                                 @Param("csNick") String csNick,
                                 @Param("date") Date date,
                                 @Param("crossChatBuyerNickLst") List<String> crossChatBuyerNickLst,
                                 @Param("tableName") String tableName);

    int updateChatpeersCrossChatRecount(@Param("shopId") Long shopId,
                                 @Param("csNick") String csNick,
                                 @Param("date") Date date,
                                 @Param("crossChatBuyerNickLst") List<String> crossChatBuyerNickLst,
                                 @Param("tableName") String tableName);

    int updateShopForwardChatpeerInfoForCommonChat(@Param("chatpeerLst") List<CommonCsChatpeerDTO> chatpeerLst,
                                                   @Param("tableName") String tableName);

    int updateChatpeerInfoForCsOrderIndex(@Param("chatpeerLst") List<CsOrderIndexChatpeerDTO> chatpeers,
                                          @Param("tableName") String tableName);

    int updateChatpeerInfoForEnquiry(@Param("chatpeerLst") List<ReceivedChatpeerDTO> chatpeers,
                                     @Param("tableName") String tableName);


    int updateChatpeerInfoForFinalData(@Param("chatpeerLst") List<SimpleChatpeerDTO> chatpeers,
                                       @Param("tableName") String tableName);


    int updateChatpeerInfoForCsOrderBind(@Param("chatpeerLst") List<CsOrderBindChatpeerDTO> chatpeers,
                                         @Param("tableName") String tableName);

    CsChatpeerDO getCsChatpeerById(Long id);

    List<CommonCsChatpeerDTO> selectShopCsChatpeerLstByDate(@Param("shopId") Long shopId,
                                                            @Param("csNick") String csNick,
                                                            @Param("date") Date date,
                                                            @Param("tableName") String tableName);

    List<String> selectShopCsEnquiryBuyerNickLstByDate(@Param("shopId") Long shopId,
                                                       @Param("csNick") String csNick,
                                                       @Param("date") Date date,
                                                       @Param("tableName") String tableName);

    List<CsOrderIndexChatpeerDTO> selectReceiveChatpeersByShopAndDateForCsOrderIndex(@Param("shopId") Long shopId, @Param("csNick") String csNick,
                                                                                     @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

    List<CsOrderBindChatpeerDTO> selectShopChatpeerLstByDateForCsOrderBind(@Param("shopId") Long shopId,
                                                                           @Param("date") Date date,
                                                                           @Param("tableName") String tableName);

    List<EnquiryChatpeerDTO> selectShopCsFirstToConsultBuyerNickLst(@Param("shopId") Long shopId,
                                                                    @Param("csNick") String csNick,
                                                                    @Param("date") Date date,
                                                                    @Param("tableName") String tableName);

    List<ReceivedChatpeerDTO> selectShopCsChatpeerLstByDateForEnquiry(@Param("shopId") Long shopId,
                                                                      @Param("csNick") String csNick,
                                                                      @Param("date") Date date,
                                                                      @Param("tableName") String tableName);

    List<SimpleChatpeerDTO> selectShopCsOrderUnCreatedChatpeerLst(
            @Param("shopId") Long shopId,
            @Param("csNick") String csNick,
            @Param("date") Date date,
            @Param("tableName") String tableName);

    List<CsOrderBindChatpeerDTO> selectShopCsReceivedChatpeerLstByDateForCsOrderBind(@Param("shopId") Long shopId, @Param("csNick") String csNick,
                                                                                     @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

    List<CsOrderBindChatpeerDTO> selectConsultByDateAndCsNickAndBuyer(@Param("shopId") Long shopId, @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                                      @Param("buyerNicks") Collection<String> buyerNicks, @Param("tableName") String tableName);

    List<EnquiryChatpeerDTO> selectEnquiryChatpeerLst(@Param("shopId") Long shopId,
                                                      @Param("csNick") String csNick, @Param("date") Date date, @Param("tableName") String tableName);


    List<PesCalChatpeerDTO> selectShopCsChatpeerLstForCsPesCal(@Param("shopId") Long shopId,
                                                               @Param("csNick") String csNick, @Param("date") Date date, @Param("tableName") String tableName);

    List<String> selectShopCsForwordOutBuyerByDate(@Param("shopId") Long shopId,
                                                   @Param("csNick") String csNick,
                                                   @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                   @Param("tableName") String tableName);

    List<String> selectTargetDateReceiveBuyersByBuyerNickLst(@Param("shopId") Long shopId,
                                                             @Param("csNick") String csNick,
                                                             @Param("date") Date date,
                                                             @Param("tableName") String tableName);

    List<EnquiryChatpeerDTO> selectShopCsChatpeerLstByDateForEnquiryLoss(@Param("shopId") Long shopId,
                                                                         @Param("csLst") List<CsDTO> csLst,
                                                                         @Param("date") Date date,
                                                                         @Param("tableName") String tableName);

    int updateChatpeerInfoForCrossDayChat(@Param("chatpeerLst") List<CommonCsChatpeerDTO> chatpeerLst,
                                          @Param("tableName") String tableName);


    List<String> selectChatPeerLstByDateByCsNickForReceive(@Param("shopId") Long shopId,
                                                           @Param("csNick") String csNick,
                                                           @Param("date") Date date,
                                                           @Param("tableName") String tableName);

    int batchUpdateCsAfterSaleReceive(
            @Param("aftersaleReceiveLst") List<CsOrderIndexDTO> aftersaleReceiveLst,
            @Param("tableName") String tableName);

    Set<String> searchFillterBuyer(
            @Param("tableName") String tableName,
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("nick") String nick
    );

    int updateChatPeerInfoIsEnquiryStatusByIdLst(@Param("ids") List<Long> activeFollowUpEnquiryChatpeerIdLst,
                                                 @Param("tableName") String tableName);

    List<EnquiryChatpeerDTO> selectCustFirstReplyByCsByCustomerByDate(@Param("shopId") Long shopId,
                                                                      @Param("csNick") String csNick,
                                                                      @Param("enquiryChatpeerLst") List<EnquiryChatpeerDTO> enquiryChatpeerLst,
                                                                      @Param("date") Date date,
                                                                      @Param("tableName") String tableName);


    List<Long> selectChatPeerIdsByShopIdByDate(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("date") Date date);

    int deleteChatpeerByIds(
            @Param("tableName") String tableName,
            @Param("ids") List<Long> ids);

    List<CommonCsChatpeerDTO> selectChatPeerByShopIdByDate(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("date") Date date);

    List<CommonCsChatpeerDTO> selectByShopIdAndDateAndCsNicks(
            @Param("tableName") String tableName,
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("csNicks") List<String> csNicks
    );

    /**
     * 获取客服指定天数内为主动联系的聊天关系
     *
     * @param shopId
     * @param startDate
     * @param endDate
     * @param csNick
     * @param tableName
     * @return
     */
    List<SimpleChatpeerDTO> selectcsChatFirstChatPeerLstByDateByCsNick(@Param("shopId") Long shopId,
                                                                       @Param("startDate") Date startDate,
                                                                       @Param("endDate") Date endDate,
                                                                       @Param("csNick") String csNick,
                                                                       @Param("tableName") String tableName);

    /**
     * 查询过滤掉的聊天关系
     *
     * @param shopId
     * @param csNick
     * @param buyNick
     * @param date
     * @param tableName
     * @return
     */
    List<CommonCsChatpeerDTO> selectFilterChatPeer(@Param("shopId") Long shopId,
                                                   @Param("csNick") String csNick,
                                                   @Param("buyNick") String buyNick,
                                                   @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate,
                                                   @Param("tableName") String tableName);


    List<CsOrderBindChatpeerDTO> selectShopCsReceivedChatpeerLstByDateForCsOrderBindByCsNick(
            @Param("shopId") Long shopId, @Param("csLst") Set<String> csLst,
            @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);


    List<ReceivedChatpeerDTO> selectReceiveChatpeersByShopAndDateAndBuyerAndCustomer(
            @Param("shopId")Long shopId,
            @Param("date")Date date,
            @Param("csBuyerOrderIndexLst")List<CsOrderIndexDTO> csBuyerOrderIndexLst,
            @Param("tableName")String tableName);


    int batchUpdateAfterSale(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("afterSaleChatPeerLst") List<ReceivedChatpeerDTO> afterSaleChatPeerLst,
            @Param("tableName") String tableName);

    List<CommonCsChatpeerDTO> selectWatchwordFilterChatPeer(
            @Param("shopId") Long shopId,
            @Param("csNick")String csNick,
            @Param("buyerNick") String buyerNick,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<CommonCsChatpeerDTO> selectReceiveFilterChatPeerByDateAndCsNickAndBuyerLst(@Param("shopId")Long shopId,
                                                                                    @Param("csNick")String csNick,
                                                                                    @Param("buyerNickLst")List<String> buyerNickLst,
                                                                                    @Param("startDate")Date startDate,
                                                                                    @Param("endDate")Date endDate,
                                                                                    @Param("tableName")String tableName);


    List<CommonCsChatpeerDTO> selectBuyerNickByDate(@Param("shopId") Long shopId,
                                                    @Param("startDate")Date startDate,
                                                    @Param("endDate")Date endDate,
                                                    @Param("tableName")String tableName);
}
