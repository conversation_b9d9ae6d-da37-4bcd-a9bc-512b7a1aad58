package com.pes.jd.mapper;

import com.pes.jd.model.DO.UserPortraitAiReport;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 用户画像AI分析报告表 Mapper接口
 * <AUTHOR>
 */
public interface UserPortraitAiReportMapper {

    /**
     * 插入用户画像AI报告数据
     * @param report 用户画像AI报告数据
     * @param tableName 表名
     * @return 插入行数
     */
    int insertUserPortraitAiReport(@Param("report") UserPortraitAiReport report,
                                   @Param("tableName") String tableName);

    /**
     * 批量插入用户画像AI报告数据
     * @param reportList 用户画像AI报告数据列表
     * @param tableName 表名
     * @return 插入行数
     */
    int batchInsertUserPortraitAiReport(@Param("reportList") List<UserPortraitAiReport> reportList,
                                        @Param("tableName") String tableName);

    /**
     * 根据店铺ID和日期删除用户画像AI报告数据
     * @param shopId 店铺ID
     * @param reportDate 报告日期
     * @param tableName 表名
     * @return 删除行数
     */
    int deleteUserPortraitAiReportByShopIdAndDate(@Param("shopId") Long shopId,
                                                   @Param("reportDate") LocalDate reportDate,
                                                   @Param("tableName") String tableName);

    /**
     * 根据店铺ID和日期范围删除用户画像AI报告数据
     * @param shopId 店铺ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param tableName 表名
     * @return 删除行数
     */
    int deleteUserPortraitAiReportByShopIdAndDateRange(@Param("shopId") Long shopId,
                                                        @Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate,
                                                        @Param("tableName") String tableName);

    /**
     * 根据店铺ID删除所有用户画像AI报告数据
     * @param shopId 店铺ID
     * @param tableName 表名
     * @return 删除行数
     */
    int deleteAllUserPortraitAiReportByShopId(@Param("shopId") Long shopId,
                                              @Param("tableName") String tableName);

    /**
     * 根据主键ID查询用户画像AI报告数据
     * @param id 主键ID
     * @param tableName 表名
     * @return 用户画像AI报告数据
     */
    UserPortraitAiReport selectUserPortraitAiReportById(@Param("id") Long id,
                                                        @Param("tableName") String tableName);

    /**
     * 根据店铺ID和日期范围查询用户画像AI报告数据
     * @param shopId 店铺ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param tableName 表名
     * @return 用户画像AI报告数据列表
     */
    List<UserPortraitAiReport> selectUserPortraitAiReportByShopIdAndDateRange(@Param("shopId") Long shopId,
                                                                              @Param("startDate") LocalDate startDate,
                                                                              @Param("endDate") LocalDate endDate,
                                                                              @Param("tableName") String tableName);

    /**
     * 根据店铺ID、报告类型和图表类型查询用户画像AI报告数据
     * @param shopId 店铺ID
     * @param reportType 报告类型
     * @param chartType 图表类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param tableName 表名
     * @return 用户画像AI报告数据列表
     */
    List<UserPortraitAiReport> selectUserPortraitAiReportByConditions(@Param("shopId") Long shopId,
                                                                      @Param("reportType") Integer reportType,
                                                                      @Param("chartType") Integer chartType,
                                                                      @Param("startDate") LocalDate startDate,
                                                                      @Param("endDate") LocalDate endDate,
                                                                      @Param("tableName") String tableName);
}
