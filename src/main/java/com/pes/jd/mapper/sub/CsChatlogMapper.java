package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.ChatLogDTO;
import com.pes.jd.model.VO.ReceiveBuyerVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsChatlogMapper {
	
	List<ChatLogDTO> selectBuyerChatlogs(@Param("shopId") Long shopId,
                                         @Param("csNickLst") List<String> csNickLst,
                                         @Param("startDate") Date startDate,
                                         @Param("endDate") Date endDate,
                                         @Param("buyerNick") String buyerNick,
                                         @Param("keyWord") String keyWord,
                                         @Param("sid") String sid,
                                         @Param("tableName") String tableName);

	List<String> searchReceiveNotEmptyChatLogByChatPeerLst(@Param("shopId") Long shopId,
                                                           @Param("startDate") Date startDate,
                                                           @Param("endDate") Date endDate,
                                                           @Param("csNick") String csNick,
                                                           @Param("receiveBuyerLst") List<String> receiveBuyerLst,
                                                           @Param("tableName") String tableName);

	List<ReceiveBuyerVO> searchReceiveNotEmptyChatLogVOByChatPeerLst(@Param("shopId") Long shopId,
                                                                     @Param("startDate") Date startDate,
                                                                     @Param("endDate") Date endDate,
                                                                     @Param("csNick") String csNick,
                                                                     @Param("receiveBuyerLst") List<String> receiveBuyerLst,
                                                                     @Param("tableName") String tableName);

	List<ChatLogDTO> searchCsChatlogs(@Param("shopId") Long shopId,
                                      @Param("csNick") String csNick,
                                      @Param("startDate") Date startDate,
                                      @Param("endDate") Date endDate,
                                      @Param("buyerLst") List<String> buyerLst,
                                      @Param("tableName") String tableName);
}