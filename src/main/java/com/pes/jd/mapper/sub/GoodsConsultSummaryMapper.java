package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.GoodsConsultSummaryDTO;
import com.pes.jd.model.DTO.GoodsConsultSummaryV2DTO;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface GoodsConsultSummaryMapper {
    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTable") String goodsSkuTable,
            @Param("skuLst") List<Long> skuLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList) throws SQLException;

    List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTable") String goodsSkuTable,
            @Param("skuLst") List<Long> skuLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList) throws SQLException;


    List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTable") String goodsSkuTable,
            @Param("skuLst") List<Long> skuLst,
            @Param("categoryId") List<Long> categoryId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList) throws SQLException;


    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuId(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("skuId") Long skuId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList);

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV3(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("skuId") Long skuId,
            @Param("categoryId") List<Long> categoryId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList);
    List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV2(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("skuId") Long skuId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList);
    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIds(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("skuIds") List<Long> skuIds,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList);

    List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdsV2(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("skuIds") List<Long> skuIds,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList);

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(
            @Param("shopId") Long shopId,
            @Param("goodsConsultSummaryTables") List<DateRangeParam> goodsConsultSummaryTables,
            @Param("goodsSkuTable") String goodsSkuTable,
            @Param("skuLst") List<Long> skuLst,
            @Param("categoryId") List<Long> categoryId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList) throws SQLException;


}
