package com.pes.jd.mapper.sub;

import java.util.Date;
import java.util.List;

import com.pes.jd.model.entity.ChatClassify;
import com.pes.jd.model.DO.CsServiceEvaluationDetail;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DO.CsChatlog;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper for chat classification
 */
public interface ChatClassifyMapper {

    /**
     * Query classify by shop_id and date
     *
     * @param shopId shop ID
     * @param date date to query
     * @param tableName dynamic table name with suffix
     * @return list of chat classifications
     */
    List<ChatClassify> queryClassifyByShopAndDate(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("tableName") String tableName);

    /**
     * Query classify by shop_id and date range
     *
     * @param shopId shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param tableName dynamic table name with suffix
     * @return list of chat classifications
     */
    List<ChatClassify> queryClassifyByShopAndDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    /**
     * Query classify by shop_id, date and classify type
     *
     * @param shopId shop ID
     * @param date date to query
     * @param classify classification type
     * @param tableName dynamic table name with suffix
     * @return list of chat classifications
     */
    List<ChatClassify> queryClassifyByShopAndClassify(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("classify") String classify,
            @Param("tableName") String tableName);

    /**
     * Query classify by shop_id, date range and list of SIDs
     *
     * @param shopId shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param sids list of session IDs to filter by
     * @param tableName dynamic table name with suffix
     * @return list of chat classifications
     */
    List<ChatClassify> queryClassifyBySidsAndDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("sids") List<String> sids,
            @Param("tableName") String tableName);

    /**
     * Query low-rated evaluations by date range and shop ID
     *
     * @param shopId shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param tableName dynamic table name with suffix
     * @return list of evaluations with eval_code less than 100
     */
    List<CsServiceEvaluationDetail> queryLowRatedEvaluationsByDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<CsServiceEvaluationDetail> queryLowRatedEvaluationsByDateRangeAndScore(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName,
            @Param("score") Integer score);

    /**
     * Query classify by shop_id, date range and classify type
     *
     * @param shopId shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param classify classification type
     * @param tableName dynamic table name with suffix
     * @return list of chat classifications
     */
    List<ChatClassify> queryClassifyByShopAndDateRangeAndClassify(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("classify") String classify,
            @Param("tableName") String tableName);

    /**
     * Query all evaluations by shop_id, date range and list of SIDs
     *
     * @param shopId shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param sids list of session IDs to filter by
     * @param tableName dynamic table name with suffix
     * @return list of all evaluations matching the criteria
     */
    List<CsServiceEvaluationDetail> queryAllEvaluationsBySidsAndDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("sids") List<String> sids,
            @Param("tableName") String tableName);

    /**
     * Query classify by shop_id, classify, classifyExtra and date range
     *
     * @param shopId shop ID
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param classify classification type
     * @param classifyExtra sub-classification type (optional)
     * @param tableName dynamic table name with suffix
     * @return list of chat classifications matching the criteria
     */
    List<ChatClassify> queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("classify") String classify,
            @Param("classifyExtra") String classifyExtra,
            @Param("tableName") String tableName);


    List<ChatClassify> queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndSids(
            @Param("shopId") Long shopId,
            @Param("classify") String classify,
            @Param("classifyExtra") String classifyExtra,
            @Param("tableName") String tableName,
            @Param("sids") List<String> sids);

    /**
     * Query chat session by shop ID, SID, and date range
     *
     * @param shopId shop ID
     * @param sid session ID (optional)
     * @param startDate start date of the query range (inclusive)
     * @param endDate end date of the query range (inclusive)
     * @param tableName dynamic table name with suffix
     * @return list of chat sessions matching the criteria
     */
    List<CsChatSessionDO> queryChatSessionByShopIdAndSidAndDateRange(
            @Param("shopId") Long shopId,
            @Param("sid") String sid,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    /**
     * Query chatlogs by shop ID and time range
     *
     * @param shopId shop ID
     * @param startTime start time of the query range (inclusive)
     * @param endTime end time of the query range (inclusive)
     * @param tableName dynamic table name with suffix
     * @return list of chatlogs matching the criteria
     */
    List<CsChatlog> queryChatlogsByShopIdAndTimeRange(
            @Param("shopId") Long shopId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("tableName") String tableName);

    List<ChatClassify> querySessionIdByDateRange(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);
}
