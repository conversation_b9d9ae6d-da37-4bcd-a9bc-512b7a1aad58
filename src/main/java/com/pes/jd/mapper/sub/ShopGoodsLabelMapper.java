package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.ShopGoodsLabelDO;
import com.pes.jd.model.DTO.ShopGoodsLabelDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ShopGoodsLabelMapper {
    int deleteByPrimaryKey(@Param("id") Long id,
                           @Param("tableName") String tableName);

    int insert(@Param("entity") ShopGoodsLabelDO record, @Param("tableName") String tableName);

    int insertSelective(ShopGoodsLabelDO record);

    ShopGoodsLabelDO selectByPrimaryKey(@Param("entity") ShopGoodsLabelDO record, @Param("tableName") String tableName);

    List<Long> searchByShopId(@Param("shopId") Long shopId,
                              @Param("tableName") String tableName);

    int updateByPrimaryKey(@Param("record") ShopGoodsLabelDO record,
                           @Param("tableName") String tableName);

    ShopGoodsLabelDTO selectGoodsLabelById(@Param("id") Long id,
                                           @Param("tableName") String tableName);

    List<ShopGoodsSkuLabelDTO> searchByWareIds(@Param("ids") Set<Long> wareIds,
                                               @Param("tableName") String tableName,
                                               @Param("shopId") Long shopId);
}