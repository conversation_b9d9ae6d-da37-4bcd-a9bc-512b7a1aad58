package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.CustConsultGoodsDTO;
import com.pes.jd.model.DTO.CustConsultGoodsV2DTO;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


public interface CsConsultGoodsMapper {

    List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(
            @Param("shopId") Long shopId,
            @Param("consultDetailTables") List<DateRangeParam> consultDetailTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("result") Integer result,
            @Param("skuLst") List<Long> skuLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList,
            @Param("customer") String customer);
    List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(
            @Param("shopId") Long shopId,
            @Param("consultDetailTables") List<DateRangeParam> consultDetailTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("result") Integer result,
            @Param("skuLst") List<Long> skuLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList,
            @Param("customer") String customer);

    List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4(
            @Param("shopId") Long shopId,
            @Param("consultDetailTables") List<DateRangeParam> consultDetailTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("result") Integer result,
            @Param("skuLst") List<Long> skuLst,
            @Param("categoryId") List<Long> categoryId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList,
            @Param("customer") String customer);

    List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuByCategoryId(
            @Param("shopId") Long shopId,
            @Param("consultDetailTables") List<DateRangeParam> consultDetailTables,
            @Param("goodsSkuTables") String goodsSkuTables,
            @Param("result") Integer result,
            @Param("skuLst") List<Long> skuLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("csNickList") List<String> csNickList,
            @Param("customer") String customer,
            @Param("categoryId") List<Long> categoryId);

}
