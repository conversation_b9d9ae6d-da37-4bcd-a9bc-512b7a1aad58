package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.OrderFilter;
import com.pes.jd.model.DTO.OrderFilterDTO;
import com.pes.jd.model.Query.UserQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OrderFilterMapper {
	int insertOrderFilter(OrderFilter record);

	int deleteOrderFilterById(Long id);

	int updateOrderFilterBySelective(OrderFilter record);

	OrderFilter getOrderFilterById(Long id);
	
	int persistOrderFilterByFile(Map<String, Object> param);
	
	List<Long> selectIdsByOrderFilterIds(Map<String, Object> param);
	
	int deleteOrdersFilterByIds(Map<String, Object> param);
	
	int persistOrderFilters(Map<String, Object> param);

	List<OrderFilterDTO> selectByOrderId(@Param("filterTableName") String filterTableName, @Param("bindTableName") String bindTableName,
                                         @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("shopId") String shopId,
                                         @Param("nicks") List<UserQuery> nicks, @Param("buyerNick") String buyerNick, @Param("orderId") String orderId);

}