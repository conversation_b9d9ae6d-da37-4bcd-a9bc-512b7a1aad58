package com.pes.jd.mapper.sub;


import com.pes.jd.model.DO.CsPerformanceDO;
import com.pes.jd.model.DTO.CsPerformanceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface CsPerformanceMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CsPerformanceDO record);

    int insertSelective(CsPerformanceDO record);

    CsPerformanceDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CsPerformanceDO record);

    int updateByPrimaryKey(CsPerformanceDO record);

    List<CsPerformanceDTO> searchByDateShopCs(
            @Param("nicks") Set<String> nicks,
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName,
            @Param("filterDates") Set<Date> filterDates);

   List<CsPerformanceDTO>  selectCsPerformanceByCsNickByDateForRealTime(@Param("shopId") Long shopId, @Param("csNickLst") List<String> csNickLst,
                                                                        @Param("startDate") Date startDate,
                                                                        @Param("endDate") Date endDate,
                                                                        @Param("tableName") String tableName);
}