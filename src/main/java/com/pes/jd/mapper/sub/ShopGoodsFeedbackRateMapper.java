package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.ShopGoodsFeedbackRateDTO;
import com.pes.jd.model.Param.ShopGoodsRateParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShopGoodsFeedbackRateMapper {
    List<ShopGoodsFeedbackRateDTO> selectShopGoodsFeedbackByShopIdByDateByGoodsByType(
            @Param("shopId") Long shopId,
            @Param("param") ShopGoodsRateParam param,
            @Param("sortPageQuery") SortPageQuery sortPageQuery,
            @Param("tableNames") List<CommonUtils.DateRangeParam> tableNames);

Integer selectCountShopGoodsFeedbackByShopIdByDateByGoodsByType(
        @Param("shopId") Long shopId,
        @Param("startDate") Date startDate,
        @Param("endDate") Date endDate,
        @Param("param") ShopGoodsRateParam param,
        @Param("tableName") String tableName
);

}