package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.CsWarningDO;
import com.pes.jd.model.DTO.CsWarningDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsWarningMapper {

    int batchInsertCsWarning(@Param("shopId") Long shopId,
                             @Param("csWarningList") List<CsWarningDO> csWarningList,
                             @Param("tableName") String tableName);

    int batchDeleteCsWarningByDateByType(@Param("shopId") Long shopId,
                                         @Param("startDate") Date startDate,
                                         @Param("endDate") Date endDate,
                                         @Param("type") Byte type,
                                         @Param("tableName") String tableName);

    int batchDeleteCsWarningByDateByTypeAndCs(@Param("shopId") Long shopId,
                                              @Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate,
                                              @Param("type") Byte type,
                                              @Param("tableName") String tableName,
                                              @Param("csNick") String csNick);

    List<CsWarningDTO> selectCsWarnLstByDateAndCsLstAndWarnType(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("type") Byte type,
            @Param("tableName") String tableName,
            @Param("customer") String customer,
            @Param("csNickLst") List<String> csNickLst,
            @Param("keyword") String keyword);

//	List<CsWarningDO> selectCsSlowResponseWarningLst(@Param("shopId") Long shopId,
//                                                     @Param("csLst") List<CsDTO> csLst,
//                                                     @Param("startDate") Date startDate,
//                                                     @Param("endDate") Date endDate,
//                                                     @Param("type") int type,
//                                                     @Param("tableName") String tableName);
//
//	List<CsWarningDO> selectTotalWarnByShopIdAndDate(@Param("shopId") Long shopId,
//                                                     @Param("tableName") String tableName,
//                                                     @Param("startDate") Date startDate,
//                                                     @Param("endDate") Date endDate);
}
  
