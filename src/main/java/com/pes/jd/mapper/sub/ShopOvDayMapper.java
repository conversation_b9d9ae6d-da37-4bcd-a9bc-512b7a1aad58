package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.PesShopOvDay;
import com.pes.jd.model.DO.ShopOvDay;
import com.pes.jd.model.DTO.ShopOvDayDTO;
import com.pes.jd.model.DTO.ShopPerformanceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ShopOvDayMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PesShopOvDay record);

    int insertSelective(PesShopOvDay record);

    PesShopOvDay selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PesShopOvDay record);

    int updateByPrimaryKey(PesShopOvDay record);

    List<Map<String,Object>> selectByShopIdAndSchema();

    List<ShopOvDay> selectByShopId(@Param("tableName") String tableName, @Param("shopId") Long shopId
            , @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<Map<String,Object>> selectByShopIdReturnMap(@Param("tableName") String tableName, @Param("shopId") Long shopId
            , @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<ShopPerformanceDTO> selectCsPerformanceData(Map<String, Object> param);


    List<ShopOvDayDTO> selectByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopOvDayTableName") String shopOvDayTableName);


    List<com.pes.jd.ms.domain.Data.shopdata.ShopOvDay> selectShopSaleAmountByShopIdByDate(
            @Param("shopIdLst") List<Long> shopIdLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<com.pes.jd.ms.domain.Data.shopdata.ShopOvDay> selectShopOrderSaleAmountByShopIdByDate(
            @Param("shopIdSet") Set<Long> shopIdSet,
            @Param("date") Date date,
            @Param("tableName") String tableName);

    com.pes.jd.ms.domain.Data.shopdata.ShopOvDay selectShopSaleAmountByShopIdByDateNew(@Param("shopId") Long shopId,
                                                                                       @Param("date") Date date,
                                                                                       @Param("tableName") String shopOvDayTableName);

    ShopOvDayDTO selectSaleAmountAndBuyerNumByShopId(@Param("shopId") Long shopId,
                                                     @Param("date") Date date,
                                                     @Param("tableName") String tableName);
}