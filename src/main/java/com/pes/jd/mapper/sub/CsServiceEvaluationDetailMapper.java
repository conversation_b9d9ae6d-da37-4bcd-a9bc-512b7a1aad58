package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.CsServiceEvaluationDetail;
import com.pes.jd.model.DTO.CsServiceEvaluateDetailDTO;
import com.pes.jd.model.DTO.CsServiceStatisCountDTO;
import com.pes.jd.model.DTO.NickPerformanceDTO;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CsServiceEvaluationDetailMapper {

	CsServiceEvaluationDetail getCsServiceEvaluationById(Long id);

	int deleteCsServiceEvaluationById(Long id);

	List<CsServiceEvaluationDetail> selectCsServiceEvaluationByNick(Map<String, Object> param);

	List<CsServiceEvaluationDetail> selectShopQNDailyEvalLst(Map<String, Object> param);

	List<Map<String,Object>> selectShopQNDailyEvalLstReturnMap(Map<String, Object> param);

	List<Map<String,Object>> selectByDateAndShopNick(Map<String, Object> param);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalByEvaltimeCount(@Param("shopId") Long shopId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalSatisfactionCount(@Param("shopId") Long shopId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

	List<NickPerformanceDTO> selectPerformanceData(Map<String, Object> param);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalBySendTimeCount(@Param("shopId") Long shopId, @Param("csNickLst") List<String> csNickLst, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalByEvalTimeCount(@Param("shopId") Long shopId, @Param("csNickLst") List<String> csNickLst, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalSatisfactionCount(@Param("shopId") Long shopId, @Param("csNickLst") List<String> csNickLst, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("tableName") String tableName);

	List<CsServiceEvaluateDetailDTO> selectCsServiceEvalByCsNickByBuyerNickByEvalCode(

            @Param("shopId") Long shopId,
            @Param("csNickList") List<String> csNickLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerNick") String buyerNick,
            @Param("evalCode") Integer evalCode,
            @Param("evalTables") List<DateRangeParam> evalTables,
            @Param("sendEvalTables") List<DateRangeParam> sendEvalTables,
            @Param("chatSessionTables") List<DateRangeParam> chatSessionTables,
            @Param("sortPageQuery") SortPageQuery sortPageQuery
    );


	List<CsServiceEvaluateDetailDTO> selectCsServiceEvalReceiveChatSession(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

	Integer selectCsServiceEvalCount(
            @Param("shopId") Long shopId,
            @Param("csNickList") List<String> csNickLst,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerNick") String buyerNick,
            @Param("evalCode") Integer evalCode,
            @Param("evalTables") List<DateRangeParam> evalTables,
            @Param("sendEvalTables") List<DateRangeParam> sendEvalTables,
            @Param("chatSessionTables") List<DateRangeParam> chatSessionTables);
}