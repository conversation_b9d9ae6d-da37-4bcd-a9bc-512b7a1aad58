package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.ReserveActivityDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ReserveActivityMapper {


    List<ReserveActivityDTO> selectReserveActivityNow(@Param("tableName") String tableName,
                                                      @Param("nowDate") Date tableNameDate, @Param("shopId") Long shopId);

    List<ReserveActivityDTO> selectReserveActivity(@Param("tableName") String tableName, @Param("shopId") Long shopId, @Param("activityId") String activityId);

    List<ReserveActivityDTO> selectShopReservePresaleByActId(@Param("tableName") String tableName,
                                                             @Param("nowDate") Date now, @Param("shopId") Long shopId,
                                                             @Param("activityId") List<String> resIds);

    List<ReserveActivityDTO> selectShopReserveByShopId(@Param("tableName") String tableName, @Param("shopId") Long shopId);


    List<ReserveActivityDTO> selectShopReserveByShopIdByActiveityId(@Param("shopId") Long shopId, @Param("activityId") String activityId, @Param("tableName") String tableName);

    List<ReserveActivityDTO> selectReserveActivityByShop(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("skuId") List<Long> skuId,
            @Param("activityId") String activityId,
            @Param("type") String type,
            @Param("conditionType") Long conditionType,
            @Param("status") String status,
            @Param("tableName") String tableName
    );

    List<ReserveActivityDTO> selectReserveActivityByConditionType(
            @Param("shopId") Long shopId,
            @Param("conditionType") Long conditionType,
            @Param("activityId") String activityId,
            @Param("tableName") String tableName);

    List<ReserveActivityDTO> selectReserveActivtyByActivityId(
            @Param("shopId") Long shopId,
            @Param("activityId") String activityId,
            @Param("tableName") String tableName);

    ReserveActivityDTO selectReserveActivityByActivityIdAndSkuId(
            @Param("shopId") Long shopId,
            @Param("activityId") String activityId,
            @Param("skuId") Long skuId,
            @Param("tableName") String tableName);

    List<ReserveActivityDTO> selectReserveGoodsFromPra(@Param("shopId") Long shopId,
                                                       @Param("tableName") String tableName);

    List<String> getPresaleActivityIds(@Param("shopId") Long shopId,
                                       @Param("tableName") String tableName,
                                       @Param("startDate") Date startDate,
                                       @Param("endDate") Date endDate);
}