package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-01-28 17:05
 */

public interface ShopPerformanceMapper {
    List<ShopTeamPerformanceDTO> selectShopTeamPerByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopTeamPerTableName") String shopTeamPerTableName);

    List<ShopTeamOrderPerformanceDTO> selectShopTeamOrderPerByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopTeamOrderPerTableName") String shopTeamOrderPerTableName);

    List<ShopRefundDayDTO> selectShopRefundByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopRefunderDayTableName") String shopRefunderDayTableName);

    List<ShopOrderEvaluateDTO> selectShopOrderEvaluateByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopOrderEvaluateTableName") String shopOrderEvaluateTableName);

    List<ShopTeamSessionServiceIndexDTO> selectShopTeamSessionServiceIndexByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopTeamSessionServiceIndexTableName") String shopTeamSessionServiceIndexTableName);

    List<ShopTeamAssitIndexDTO> selectShopTeamAssitIndexByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("shopTeamAssitIndexTableName") String shopTeamAssitIndexTableName);

    List<CsRefundDayDTO> selectCsRefundDayLstByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("csRefundDayTableName") String csRefundDayTableName);

    List<ShopTeamLossRecordDTO> selectTeamLossRecordByShopIdAndDate(@Param("shopId") Long shopId, @Param("dateType") Integer dateType, @Param("dates") List<String> dates, @Param("tableName") String tableName);

    ShopTeamOrderPerformanceDTO selectShopSaleAmount(@Param("shopId") Long shopId, @Param("date") Date toDay, @Param("tableName") String shopTeamPerTableName);
}
