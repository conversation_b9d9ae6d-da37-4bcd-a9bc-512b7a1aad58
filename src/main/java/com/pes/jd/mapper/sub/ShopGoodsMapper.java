package com.pes.jd.mapper.sub;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pes.jd.model.DTO.ShopGoodNameDTO;
import com.pes.jd.model.DTO.ShopGoodsDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopGoodsMapper {

	List<ShopGoodsDTO> selectShopGoodsByShopId(@Param("tableName") String tableName,
                                               @Param("categoryTableName") String categoryTableName,
                                               @Param("categoryId") Long categoryId,
                                               @Param("name") String name,
                                               @Param("status") Integer status,
                                               @Param("shopId") Long shopId
    );

	List<ShopGoodNameDTO> selectShopGoodsByShopIdByWareIds(@Param("shopId") Long shopId,
                                                           @Param("wareIds") List<Long> wareIds,
                                                           @Param("tableName") String tableName);


	List<ShopGoodNameDTO> selectShopGoodsByCategoryIdBySkuNameByStatus(@Param("shopId") Long shopId,
                                                                       @Param("tableName") String tableName,
                                                                       @Param("categoryLst") List<Long> categoryLst,
                                                                       @Param("wareName") String wareName,
                                                                       @Param("status") String status,
                                                                       @Param("wareIdLst") List<Long> wareIdLst,
                                                                       @Param("pageNum") Integer pageNum,
                                                                       @Param("pageSize") Integer pageSize);

    IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(
            @Param("page") IPage<ShopGoodsSkuDTO> page,
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("joinTableName") String joinTableName,
            @Param("categoryLst") List<Long> categoryLst,
            @Param("skuName") String skuName,
            @Param("status") String status,
            @Param("excludeSkuIds") List<Long> excludeSkuIds,
            @Param("includeSkuIds") List<Long> includeSkuIds,
            @Param("topSku") List<Long> topSku,
            @Param("propertity") String propertity, @Param("sortDirection") String sortDirection);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(@Param("shopId") Long shopId,
                                                                             @Param("tableName") String tableName,
                                                                             @Param("categoryLst") List<Long> categoryLst,
                                                                             @Param("skuName") String skuName,
                                                                             @Param("status") String status,
                                                                             @Param("skuIdLst") List<Long> skuIdLst,
                                                                             @Param("pageNum") Integer pageNum,
                                                                             @Param("pageSize") Integer pageSize);

    int selectCountShopGoods(@Param("shopId") Long shopId,
                             @Param("tableName") String tableName,
                             @Param("categoryLst") List<Long> categoryLst,
                             @Param("skuName") String skuName,
                             @Param("status") String status,
                             @Param("skuIdLst") List<Long> skuIdLst);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByWareIdsLst(@Param("shopId") Long shopId,
                                                            @Param("tableName") String tableName,
                                                            @Param("wareIds") List<Long> wareIds);

}