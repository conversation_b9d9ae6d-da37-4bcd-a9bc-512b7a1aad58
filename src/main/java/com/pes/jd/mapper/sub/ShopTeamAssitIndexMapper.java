package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.ShopTeamAssitIndexDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface ShopTeamAssitIndexMapper {

    int deleteByPrimaryKey(Long id);

    int insert(@Param("record") ShopTeamAssitIndexDO record, @Param("tableName") String tableName);

    int insertSelective(ShopTeamAssitIndexDO record);

    ShopTeamAssitIndexDO selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ShopTeamAssitIndexDO record);

    int updateByPrimaryKey(ShopTeamAssitIndexDO record);

    int deleteByDateShopId(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("date") Date date);
    
}