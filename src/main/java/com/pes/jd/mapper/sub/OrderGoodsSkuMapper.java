package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.OrderGoodsSkuDO;
import com.pes.jd.model.DTO.OrderDetailDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface OrderGoodsSkuMapper {
    List<OrderGoodsSkuDO> selectByOrderIdsForRefundDataAnalysis(
            @Param("shopId") Long shopId,
            @Param("orderId") Set<Long> orderId,
            @Param("dateType") Integer dateType,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("skuIds") List<Long> skuIds,
            @Param("tableName") String tableName);

    List<OrderDetailDTO> selectOrderGoodsSkuByOrderIds(
            @Param("shopId") Long shopId,
            @Param("orderIds") Set<Long> orderId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName
    );
}
