package com.pes.jd.mapper.sub;


import com.pes.jd.model.DTO.ChatPeerDTO;
import com.pes.jd.model.DTO.CustomerReceiveDTO;
import com.pes.jd.model.Param.ReceiveParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;


public interface CsChatpeerMapper {
//    int deleteByPrimaryKey(Long id);
//
//    int insert(CsChatpeer record);
//
//    int insertSelective(CsChatpeer record);
//
//    CsChatpeer selectByPrimaryKey(Long id);
//
//    int updateByPrimaryKeySelective(CsChatpeer record);
//
//    int updateByPrimaryKey(CsChatpeer record);
    
    List<ChatPeerDTO> selectChatPeerByCsNickByBuyerByDateForReceiveFilter(@Param("shopId") Long shopId,
                                                                          @Param("csNickLst") List<String> csNickLst,
                                                                          @Param("tableNames") List<DateRangeParam> tableNams, @Param("buyerNick") String buyer);

    Integer selectChatPeersCountByCsNickByBuyerByDateForReceiveFilter(@Param("shopId") Long shopId,
                                                                      @Param("csNickLst") List<String> csNickLst,
                                                                      @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                                      @Param("tableName") String tableName, @Param("buyerNick") String buyer);

    List<String> selectReceiveChatpeer(@Param("shopId") Long shopId,
                                       @Param("csNickLst") List<UserQuery> csNickLst,
                                       @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                       @Param("tableName") String tableName, @Param("buyerNickKeyword") String buyerNickKeyword);


     List<String> selectCsBuyerChatpeersByParamFromChatlog(@Param("shopId") Long shopId,
                                                           @Param("csNickLst") List<UserQuery> csNickLst,
                                                           @Param("startDate") Date startDate, @Param("endDate") Date endDate,
                                                           @Param("tableName") String tableName, @Param("buyerNickKeyword") String buyerNickKeyword,
                                                           @Param("keyWord") String keyword);


	List<ChatPeerDTO> selectBuyerChatPeerLstByDate(@Param("shopId") Long shopId, @Param("adjustSDate") Date adjustSDate,
                                                   @Param("endDate") Date endDate, @Param("buyerNick") String buyerNick, @Param("tableName") String tableName);

	Set<String> selectBuyerNicksByDateAndBuyerNickForLostRecord(@Param("shopId") Long shopId, @Param("adjustSDate") Date adjustSDate,
                                                                @Param("endDate") Date endDate, @Param("buyerNick") String buyerNick, @Param("tableName") String tableName);

/*	List<ChatPeerDTO> selectNonOrdered(
			@Param("tableName") String tableName,
			@Param("shopId") Long shopId,
			@Param("buyerNick") String buyerNick,
			@Param("startDate") Date startDate,
			@Param("endDate") Date endDate,
			@Param("nicks") Set<String> nicks
	);*/

	int selectChatPeerCountByDateAndNickAndReceiveParam(@Param("shopId") Long shopId,
                                                        @Param("csNickLst") List<String> csNickLst,
                                                        @Param("param") ReceiveParam param,
                                                        @Param("cpTableNames") List<DateRangeParam> cpTableNames);


	List<CustomerReceiveDTO> selectChatPeerByDateAndNickAndReceiveParam(@Param("shopId") Long shopId,
                                                                        @Param("csNickLst") List<String> csNickLst,
                                                                        @Param("param") ReceiveParam param,
                                                                        @Param("cpTableNames") List<DateRangeParam> cpTableNames,
                                                                        @Param("sortPageQuery") SortPageQuery sortPageQuery);

	 List<CustomerReceiveDTO> selectChatPeerByCsNickByBuyerByDateForCustomerReceive(@Param("shopId") Long shopId,
                                                                                    @Param("csNickLst") List<String> csNickLst,
                                                                                    @Param("buyerNick") String buyerNick,
                                                                                    @Param("cpTableNames") List<DateRangeParam> cpTableNames,
                                                                                    @Param("sortPageQuery") SortPageQuery sortPageQuery);

	 int selectCountByCsNickByBuyerByDateForCustomerReceive(@Param("shopId") Long shopId,
                                                            @Param("csNickLst") List<String> csNickLst,
                                                            @Param("buyerNick") String buyerNick,
                                                            @Param("cpTableNames") List<DateRangeParam> cpTableNames);


	List<CustomerReceiveDTO> selectChatPeerByCsNickByBuyerLstByDate(@Param("shopId") Long shopId,
                                                                    @Param("buyerNickLst") List<String> buyerNickLst,
                                                                    @Param("csNick") String bcsNickuyerNick,
                                                                    @Param("startDate") Date startDate,
                                                                    @Param("endDate") Date endDate,
                                                                    @Param("tableName") String tableName);
}