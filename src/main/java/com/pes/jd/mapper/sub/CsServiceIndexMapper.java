package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.CsServiceIndexDTO;
import com.pes.jd.model.DTO.NickPerformanceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;


public interface CsServiceIndexMapper {

    int batchInsertCsServiceIndex(@Param("csServiceIndexLst") List<CsServiceIndexDTO> csServiceIndexLst,
                                  @Param("tableName") String tableName);

    int deleteShopCsServiceIndexByDate(@Param("shopId") Long shopId,
                                       @Param("date") Date date,
                                       @Param("tableName") String tableName);

    List<NickPerformanceDTO> selectByShopNickDate(Map<String, Object> param);

	List<Map<String,Object>> selectByShopNickDateList(Map<String, Object> param);

	CsServiceIndexDTO selectCsServiceIndexByCsNickByDateForRealTime(@Param("csNick") String csNick,
                                                                    @Param("startDate") Date startDate,
                                                                    @Param("endDate") Date endDate,
                                                                    @Param("tableName") String tableName);
} 