package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.LostRecordNote;
import com.pes.jd.model.DTO.LostRecordNoteDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface LostRecordNoteMapper {

	int insertLostRecordNoteForShop(@Param("tableName") String tableName, @Param("lostRecordNote") LostRecordNote lostRecordNote);

	int deleteLostRecordNoteById(Long id);

	int updateLostRecordNoteForShop(@Param("tableName") String tableName, @Param("lostRecordNote") LostRecordNoteDTO lostRecordNote);

	LostRecordNote getLostRecordNoteById(Long id);

	List<LostRecordNoteDTO> selectLostRecordNoteLst(@Param("shopId") Long shopId, @Param("date") Date date,
                                                    @Param("buyerNickLst") List<String> buyerNickLst, @Param("lostType") int lostType,
                                                    @Param("tableName") String tableName);

	LostRecordNoteDTO getShopBuyerLostRecordNoteByDate(@Param("shopId") Long shopId, @Param("date") Date date,
                                                       @Param("buyerNick") String buyerNick, @Param("orderId") String orderId, @Param("lostType") Integer lostType,
                                                       @Param("tableName") String tableName);


	List<LostRecordNoteDTO> selectByBuyerNickDate(String tableName, Date startDate, Date endDate, String buyerNick);

	List<LostRecordNoteDTO> selectByOrderId(@Param("tableName") String tableName, @Param("orderId") Long orderId);

	List<LostRecordNoteDTO> selectByOrderId(@Param("tableName") String tableName, @Param("orderIds") Set<String> orderIds);

	List<LostRecordNoteDTO> selectByBuyerNickDateShop(
            @Param("tableName") String tableName,
            @Param("buyerNick") String buyerNick,
            @Param("shopId") String shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate
    );

	List<LostRecordNoteDTO> selectLostRecordNoteLstByDateByBuyerNickList(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerNickList") List<String> buyerNickList,
            @Param("lostType") Integer lostType,
            @Param("tableName") String tableName);

	List<LostRecordNoteDTO> selectLostRecordNoteLstByOrderIdList(@Param("shopId") Long shopId,
                                                                 @Param("orderIds") List<Long> lossOrderIdList,
                                                                 @Param("tableName") String tableName);

}