package com.pes.jd.mapper.sub;


import com.pes.jd.model.DO.ReceiveSessionNumHourlyDO;
import com.pes.jd.model.DTO.ReceiveSessionNumHourlyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ReceiveSessionNumHourlyMapper {

    int deleteByPrimaryKey(Long id);

    int insert(ReceiveSessionNumHourlyDO record);

    int insertSelective(ReceiveSessionNumHourlyDO record);

    ReceiveSessionNumHourlyDO selectByPrimaryKey(Long id);

    List<ReceiveSessionNumHourlyDTO> searchAllDateShopGroup(
            @Param("shopId") Long shopId,
            @Param("nicks") Set<String> nicks,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("groupBy") String groupBy,
            @Param("tableName") String tableName,
            @Param("filterDates") Set<Date> filterDates);

    int updateByPrimaryKeySelective(ReceiveSessionNumHourlyDO record);

    int updateByPrimaryKey(ReceiveSessionNumHourlyDO record);

    List<ReceiveSessionNumHourlyDTO> selectShopReceiveSessionNumHourlyToMonth(
            @Param("shopId") Long shopId,
            @Param("dateType") Integer dateType,
            @Param("dates") List<String> dates,
            @Param("tableName") String tableName);
}