package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderPreordainMapper {

    int selectCountOrderPreordainAndCsOrderBindForOrderPreordainAnalysis(
            @Param("shopId") Long shopId,
            @Param("dateType") Integer dateType,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerNick") String buyerNick,
            @Param("orderId") Long orderId,
            @Param("tradeType") Integer tradeType,
            @Param("groupId") String groupId,
            @Param("csNickLst") List<String> csNickLst,
            @Param("orderPreordainTables") List<CommonUtils.DateRangeParam> orderPreordainTables,
            @Param("csOrderBindTables") List<CommonUtils.DateRangeParam> csOrderBindTables);

    List<OrderPreordainDTO> selectOrderPreordainAndCsOrderBindForOrderPreordainAnalysis(
            @Param("shopId") Long shopId,
            @Param("dateType") Integer dateType,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerNick") String buyerNick,
            @Param("orderId") Long orderId,
            @Param("tradeType") Integer tradeType,
            @Param("groupId") String groupId,
            @Param("csNickLst") List<String> csNickLst,
            @Param("orderPreordainTables") List<CommonUtils.DateRangeParam> orderPresaleTables,
            @Param("csOrderBindTables") List<CommonUtils.DateRangeParam> csOrderBindTables,
            @Param("sortPageQuery") SortPageQuery sortPageQuery
    );

}