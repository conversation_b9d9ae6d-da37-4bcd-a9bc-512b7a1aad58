package com.pes.jd.mapper.sub;


import com.pes.jd.model.DO.JobPullApiRecordDO;
import com.pes.jd.model.VO.JobPullApiRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface JobPullApiRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(JobPullApiRecordDO record);

    int insertSelective(JobPullApiRecordDO record);

    JobPullApiRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JobPullApiRecordDO record);

    int updateByPrimaryKey(JobPullApiRecordDO record);

    List<JobPullApiRecordVO> searchJobPullApiRecordRecord(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("date") Date date,
            @Param("type") Integer type);

    List<JobPullApiRecordVO> searchJobPullApiRecordRecordBySchemaId(
            @Param("tableName") String tableName,
            @Param("date") Date date,
            @Param("type") Integer type);
}