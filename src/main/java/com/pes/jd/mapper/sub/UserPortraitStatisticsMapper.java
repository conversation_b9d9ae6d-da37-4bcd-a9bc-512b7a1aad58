package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.UserPortraitStatistics;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface UserPortraitStatisticsMapper
{

    List<UserPortraitStatistics> getPortraitStatisticsByDate(@Param("shopId") Long shopId,
                                                             @Param("startTime") LocalDateTime startTime,
                                                             @Param("endTime") LocalDateTime endTime,
                                                             @Param("tableName") String tableName);


    /**
     * 根据店铺ID和日期范围查询地理分布数据
     */
    List<String> getRegionDistributionByShopAndDateRange(
            @Param("shopId") Long shopId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("tableName") String tableName
    );
}
