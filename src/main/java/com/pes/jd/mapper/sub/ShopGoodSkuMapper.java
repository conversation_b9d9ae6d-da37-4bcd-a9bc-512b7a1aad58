package com.pes.jd.mapper.sub;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pes.jd.model.DO.ShopGoodSkuDO;
import com.pes.jd.model.DTO.GoodsConsultSummaryDTO;
import com.pes.jd.model.DTO.ShopGoodNameDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuDTO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ShopGoodSkuMapper {
    int deleteShopGoodSkuById(Long id);

    int insertShopGoodSku(ShopGoodSkuDO record);

    ShopGoodSkuDO selectShopGoodSkuById(Long id);

    int updateShopGoodSku(ShopGoodSkuDO record);

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummary(Long shopId);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(@Param("shopId") Long shopId,
                                                                             @Param("tableName") String tableName,
                                                                             @Param("categoryLst") List<Long> categoryLst,
                                                                             @Param("skuName") String skuName,
                                                                             @Param("status") String status,
                                                                             @Param("skuIdLst") List<Long> skuIdLst,
                                                                             @Param("pageNum") Integer pageNum,
                                                                             @Param("pageSize") Integer pageSize);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByShopIdBySkuIdByWareId(@Param("shopId") Long shopId,
                                                                       @Param("tableName") String tableName,
                                                                       @Param("wareIdSet") Set<Long> wareIdSet,
                                                                       @Param("skuIdLst") List<Long> skuIdLst,
                                                                       @Param("status") String status);


    List<ShopGoodsSkuDTO> selectShopGoodsSkuByShopIdBySkuId(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("skuIds") Set<Long> skuIds);


    List<ShopGoodsSkuDTO> selectShopGoodsSkuByShopIdForLossOrder(@Param("shopId") Long shopId,
                                                                 @Param("tableName") String tableName);


    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstBySkuIdLst(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("skuIdLst") List<Long> skuIdLst);

    List<ShopGoodsSkuDTO> selectBySkuIdsForRefundDataAnalysis(
            @Param("shopId") Long shopId,
            @Param("skuIds") Set<Long> skuIds,
            @Param("tableName") String tableName);

    int selectCountShopGoods(@Param("shopId") Long shopId,
                             @Param("tableName") String tableName,
                             @Param("categoryLst") List<Long> categoryLst,
                             @Param("skuName") String skuName,
                             @Param("status") String status,
                             @Param("skuIdLst") List<Long> skuIdLst);


    //  商品营销


    IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods(
            @Param("page") IPage<ShopGoodsSkuDTO> page,
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("joinTableName") String joinTableName,
            @Param("categoryLst") List<Long> categoryLst,
            @Param("skuName") String skuName,
            @Param("status") String status,
//            @Param("excludeSkuIds") List<Long> excludeSkuIds,
            @Param("includeSkuIds") List<Long> includeSkuIds,
            @Param("topSku") List<Long> topSku,
            @Param("propertity") String propertity, @Param("sortDirection") String sortDirection);

    IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(
            @Param("page") IPage<ShopGoodsSkuDTO> page,
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("joinTableName") String joinTableName,
            @Param("categoryLst") List<Long> categoryLst,
            @Param("skuName") String skuName,
            @Param("status") String status,
            @Param("excludeSkuIds") List<Long> excludeSkuIds,
            @Param("includeSkuIds") List<Long> includeSkuIds,
            @Param("topSku") List<Long> topSku,
            @Param("propertity") String propertity, @Param("sortDirection") String sortDirection);

    int selectCountShopGoodsForGoods(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("joinTableName") String joinTableName,
            @Param("categoryLst") List<Long> categoryLst,
            @Param("skuName") String skuName,
            @Param("status") String status,
            @Param("excludeSkuIds") List<Long> excludeSkuIds,
            @Param("includeSkuIds") List<Long> includeSkuIds,
            @Param("topSku") List<Long> topSku
    );

    ShopGoodsSkuDTO queryShopGoodsInfoBySkuIdAndShopId(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("skuId") Long skuId);

    List<ShopGoodsSku> selectShopSkuByShopId(@Param("tableName") String tableName, @Param("shopId") Long shopId);
    Set<Long> selectShopSkuIdSetByShopId(@Param("tableName") String tableName, @Param("shopId") Long shopId);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuBySkuLst(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("skuLst") List<Long> skuLst);

    List<ShopGoodNameDTO> selectShopGoodsSkuNameBySkuIdLst(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("skuIds") List<Long> skuIds);


    List<Long> selectSkuIdsByWareId(@Param("shopId") Long shopId,
                                    @Param("wareId") Long wareId,
                                    @Param("tableName") String tableName);


    List<ShopGoodsSku> selectShopGoodsSkuByShopIdByWareIds(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("wareIds") List<Long> wareIds);


    List<ShopGoodsSku> selectShopGoodsSkuIdsByWareIdLst(@Param("shopId") Long shopId,
                                                        @Param("tableName") String tableName,
                                                        @Param("wareIds") List<Long> wareIds);

    List<ShopGoodsSku> selectShopSkuByShopIdAndSkuId(@Param("tableName") String tableName, @Param("shopId") Long shopId, @Param("skuLst") List<Long> skuList);

    List<ShopGoodsSkuDTO> selectShopSkuByShopIdAndskuName(@Param("tableName") String tableName, @Param("shopId") Long shopId, @Param("skuName") String skuName);

    List<ShopGoodsSku> selectShopSkuBySpu(@Param("shopId") Long shopId,
                                  @Param("goodsIdList") List<Long> goodsIdList,
                                  @Param("tableName") String tableName);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuIdLstByCategoryIdByStatus(@Param("shopId") Long shopId, @Param("tableName") String tableName, @Param("status") String status,@Param("categoryLst") List<Long> categoryLst);
}
