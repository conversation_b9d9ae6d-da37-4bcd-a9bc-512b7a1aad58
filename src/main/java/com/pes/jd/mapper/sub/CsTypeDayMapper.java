package com.pes.jd.mapper.sub;

import com.pes.jd.model.DO.CsTypeDayDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsTypeDayMapper {

    int deleteByPrimaryKey(Long id);

    int insert(CsTypeDayDO record);

    int insertSelective(CsTypeDayDO record);

    CsTypeDayDO selectByPrimaryKey(Long id);

    List<CsTypeDayDO> searchByShopDate(@Param("tableName") String tableName,
                                       @Param("shopId") Long shopId,
                                       @Param("startDate") Date startDate,
                                       @Param("endDate") Date endDate);

    int updateByPrimaryKeySelective(CsTypeDayDO record);

    int updateByPrimaryKey(CsTypeDayDO record);

    List<String> searchNoLockCsNickByShopDate(@Param("tableName") String tableName,
                                              @Param("shopId") Long shopId,
                                              @Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate);
}