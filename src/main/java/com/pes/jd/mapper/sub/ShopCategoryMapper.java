package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.ShopCategoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopCategoryMapper {
	List<ShopCategoryDTO> selectShopCategoryByShopId(@Param("tableName") String tableName, @Param("shopId") String shopId);

	List<ShopCategoryDTO> selectCategoryIdByShopIdByParentId(@Param("shopId") Long shopId, @Param("parentLst") List<Long> parentLst, @Param("tableName") String tableName);
}
