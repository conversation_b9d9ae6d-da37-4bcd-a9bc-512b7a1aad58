package com.pes.jd.mapper.sub;

import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.DTO.OrderSkuEvaluateDTO;
import com.pes.jd.model.Param.OrderSkuEvaluateParam;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OrderSkuEvaluateMapper {
	public List<OrderSkuEvaluateDTO> selectOrderSkuEvaluateByDateByCsNickByScore(
            @Param("shopId") Long shopId,
            @Param("param") OrderSkuEvaluateParam param,
//			@Param("endDate") Date endDate,
//			@Param("csNickList") List<String> csNickList,
//			@Param("evaluateType") Integer evaluateType,
            @Param("orderSkuEvalTables") List<DateRangeParam> orderSkuEvalTables,
            @Param("skuGoodsTables") String skuGoodsTables);


	public OrderSkuEvaluateDTO selectEvaluateInfoByOrderIdByBuyer(
			@Param("shopId") Long shopId,
			@Param("id") Integer id,
			@Param("orderId") Long orderId,
			@Param("orderSkuEvalTable") String orderSkuEvalTables,
			@Param("skuGoodsTable") String skuGoodsTable
	);


	public List<CsOrderIndexDTO> selectCsReceptionByBuyerByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderId") Long orderId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName
    );
}
