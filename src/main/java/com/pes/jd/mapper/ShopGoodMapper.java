package com.pes.jd.mapper;

import com.pes.jd.model.DTO.ShopGoodsDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopGoodMapper {
	int batchShopGood(@Param("tableName") String tableName, @Param("shopGoodLst") List<ShopGoodsDTO> shopGoodLst);

	int deleteByShopGoodsName(@Param("tableName") String tableName, @Param("shopId") Long shopId);

	int deleteByShopGoodsNameAndWareId(
			@Param("tableName")String tableName,
			@Param("shopId")Long shopId,
			@Param("wareId")String wareId);

	int batchUpdateShopGood(@Param("tableName") String tableName, @Param("shopGoodLst") List<ShopGoodsDTO> shopGoodLst);

    int deleteByShopIdAndShopGood(@Param("tableName") String tableName,
								   @Param("shopId")Long shopId,
								   @Param("shopGood") ShopGoodsDTO shopGood);

    int selectShopGoodNumByShopId(@Param("tableName") String tableName, @Param("shopId") Long shopId);

    int deleteByShopGoodsNameAndWareIds(@Param("tableName") String tableName,
                                        @Param("shopId") Long shopId,
                                        @Param("wareIds") List<Long> wareIds);
}
