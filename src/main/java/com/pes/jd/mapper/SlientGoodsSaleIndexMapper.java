package com.pes.jd.mapper;

import com.pes.jd.model.DO.SlientGoodsSaleIndexDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface SlientGoodsSaleIndexMapper {
    int deleteSlientGoodsSaleIndex(Long id);

    int insertSlientGoodsSaleIndex(SlientGoodsSaleIndexDO record);

    SlientGoodsSaleIndexDO selectSlientGoodsSaleIndexById(Long id);

    int updateSlientGoodsSaleIndex(SlientGoodsSaleIndexDO record);
    
    int batchInsertSlientGoodsSaleIndex(@Param("slienGoodsLst") List<SlientGoodsSaleIndexDO> slienGoodsLst,@Param("tableName") String tableName);
    
    int deleteSlientGoodsSaleIndexByShopIdByDate(@Param("shopId")Long shopId,@Param("startDate") Date startDate,@Param("endDate") Date endDate,@Param("tableName") String tableName);
}