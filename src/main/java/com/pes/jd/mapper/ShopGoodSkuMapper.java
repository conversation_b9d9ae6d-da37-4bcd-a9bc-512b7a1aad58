package com.pes.jd.mapper;


import com.pes.jd.model.DTO.ShopGoodSkuDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ShopGoodSkuMapper {
	int batchShopGoodsSku(@Param("tableName") String tableName, @Param("shopGoodsSkuLst") List<ShopGoodSkuDTO> shopGoodsSkuLst);

	int deleteByShopGoodsSkuName(@Param("tableName") String tableName, @Param("shopId") Long shopId);

	List<ShopGoodSkuDTO> selectShopGoodsSkuByShopId(@Param("shopId") Long shopId,@Param("tableName") String tableName);

	List<ShopGoodSkuDTO> selectShopGoodsSkuByShopIdAndSkuIdLst(
			@Param("shopId") Long shopId,
			@Param("skuIdLst") List<Long> skuIdLst,
			@Param("tableName") String tableName
	);
	List<ShopGoodSkuDTO> selectByShopIdAndSkuIds(
			@Param("shopId") Long shopId,
			@Param("skuIds") Set<Long> skuIds,
			@Param("tableName") String tableName
	);

	ShopGoodSkuDTO queryShopGoodsInfoBySkuIdAndShopId(@Param("shopId")Long shopId,@Param("tableName")String tableName,@Param("skuId")Long skuId);

    int deleteByShopGoodsSkuNameBySkuId(
			@Param("tableName")String tableName,
			@Param("shopId")Long shopId,
			@Param("skuId")String skuId);

	int deleteByShopIdAndSkuIdLst(
			@Param("tableName")String tableName,
			@Param("shopId")Long shopId,
			@Param("skuIdLst")List<Long> skuIdLst);

    long selectShopGoodsSkuNumByShopId(@Param("shopId") Long shopId,@Param("tableName") String tableName);

    int insertShopGoodSkuByFile(@Param("filePath") String filePath,@Param("tableName") String tableName);
	Set<Long> selectShopGoodsSkuIdByShopId(@Param("tableName") String tableName, @Param("shopId") Long shopId);

	Set<Long> selectShopGoodsSkuIdByShopIdBySkuIds(@Param("tableName") String tableName, @Param("shopId") Long shopId,@Param("skuIdSet") Set<Long> skuIdSet);

	List<Long> selectShopGoodsSkuIdByShopIdByStatus(@Param("tableName") String tableName, @Param("shopId") Long shopId, @Param("status") Integer status);

	List<ShopGoodSkuDTO> selectShopGoodsSkuByShopIdByStatus(@Param("tableName") String tableName, @Param("shopId") Long shopId, @Param("status") Integer status,@Param("currentPage") int currentPage,@Param("pageSize")int pageSize);

	Integer selectSkuCountByShopIdByStatus(@Param("tableName") String tableName, @Param("shopId") Long shopId, @Param("status") Integer status);
}