package com.pes.jd.mapper;

import com.pes.jd.model.DO.ReceiveSessionPressureDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ReceiveSessionPressureMapper {

    int deleteByPrimaryKey(Long id);

    int deleteByTimePoint(
            @Param("beginDate")Date beginDate,
            @Param("endDate") Date endDate,
            @Param("tableName")String tableName,
            @Param("shopId")Long shopId,
            @Param("nick")String nick
    );

    int insert(ReceiveSessionPressureDO record);

    int insertSelective(ReceiveSessionPressureDO record);

    ReceiveSessionPressureDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ReceiveSessionPressureDO record);

    int updateByPrimaryKey(ReceiveSessionPressureDO record);

    int insertBatch(@Param("list") List<ReceiveSessionPressureDO> record,@Param("tableName")String tableName);

}