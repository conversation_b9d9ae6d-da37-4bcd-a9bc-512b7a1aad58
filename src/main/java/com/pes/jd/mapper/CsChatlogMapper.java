package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsChatlogDO;
import com.pes.jd.model.DTO.ChatlogDTO;
import com.pes.jd.model.DTO.CsChatlogDTO;
import com.pes.jd.model.DTO.OrderChatlogInfoDTO;
import com.pes.jd.model.DTO.ReceiveQualityChatlogDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;


public interface CsChatlogMapper {

	CsChatlogDO getCsChatlogById(Long id);

	int deleteCsChatlogById(Long id);

	int deleteChatlogIdsByShopIdAndDate(@Param("shopId")Long shopId,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("tableName")String tableName);

	int insertCsChatlog(CsChatlogDO csChatlog);

	int insertChatlogByFile(@Param("filePath") String filePath,@Param("tableName") String tableName);

 	int batchInsertCsChatLog(@Param("chatLogList") List<CsChatlogDO> chatLogList,@Param("tableName") String chatlogTableName);

 	int updateCsChatlogById(CsChatlogDO csChatlog);

	List<ChatlogDTO> selectChatLogLstByCsAndBuyerAndDate(
			@Param("shopId")Long shopId,
			@Param("csNick")String csNick,
			@Param("buyerNick")String buyerNick,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("tableName")String tableName);

	List<ChatlogDTO> selectShopForwardChatLogLstByBuyerLstAndDate(
			@Param("shopId")Long shopId,
			@Param("buyerNickLst")List<String> buyerNickLst,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("tableName")String tableName);

	List<CsChatlogDTO> selectShopCsChatLogLstForConsultHandle(@Param("shopId")Long shopId,
			@Param("csNick")String csNick,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("tableName")String tableName);

	List<CsChatlogDTO> selectShopCsChatLogLstByBuyerNickLstForConsultHandle(
			@Param("shopId") Long shopId,
			@Param("csNick") String csNick,
			@Param("buyerNickLst") List<String> buyerNickLst,
			@Param("startDate") Date startDate,
			@Param("endDate") Date endDate,
			@Param("tableName") String tableName);

	List<ChatlogDTO> selectBuyerChatlogByCSNickAndDate(
			@Param("shopId") Long shopId,
			@Param("csNick") String csNick,
			@Param("startDate") Date startDate,
			@Param("endDate") Date endDate,
			@Param("tableName") String tableName,
			@Param("receiveBuyerLst") List<String> receiveBuyerLst);

	List<OrderChatlogInfoDTO> selectChatLogLstByCsAndBuyerAndDateForCsOrerIndex(@Param("shopId")Long shopId,
			@Param("csNick")String csNick, @Param("buyerNick")String buyerNick,  @Param("startDate")Date startDate,
			@Param("endDate")Date endDate, @Param("tableName")String tableName);

	List<OrderChatlogInfoDTO> selectChatLogLstByBuyerAndDateForCsOrerIndex(@Param("shopId")Long shopId,
		   @Param("buyerNick")String buyerNick,  @Param("startDate")Date startDate,
			@Param("endDate")Date endDate, @Param("tableName")String tableName);

	List<ReceiveQualityChatlogDTO> selectShopCsChatLogLst(@Param("shopId")Long shopId,
			@Param("csNick")String csNick, @Param("startDate")Date startDate, @Param("endDate")Date endDate,
			@Param("tableName")String tableName);

	List<CsChatlogDTO> selectShopCsChatLogLstByShopIdAndSid(@Param("shopId")Long shopId,@Param("sid")String sid,
											@Param("startDate")Date startDate, @Param("endDate")Date endDate,
														  @Param("tableName")String tableName);

	List<ChatlogDTO> searchAllByTime(@Param("shopId") Long shopId,
									 @Param("beginDate") Date beginDate, @Param("endDate") Date endDate,
									 @Param("tableName") String tableName, @Param("nick")String nick);

	List<ChatlogDTO> selectBuyerChatlogByShopIdAndDate(@Param("shopId") Long shopId,
			 @Param("startDate") Date beginDate, @Param("endDate") Date endDate,
			 @Param("tableName") String tableName);

	List<Long> selectChatLogIdsByShopIdByDate(
			@Param("shopId") Long shopId,
			@Param("tableName") String tableName,
			 @Param("startDate") Date startDate, @Param("endDate") Date endDate);

	List<ChatlogDTO> selectChatLogByShopIdByDate(
			@Param("shopId") Long shopId,
			@Param("tableName") String tableName,
			@Param("startDate") Date startDate, @Param("endDate") Date endDate);

	 int deleteChatLogByIds(
	    		@Param("tableName") String tableName,
	    		@Param("ids") List<Long> ids);
}
