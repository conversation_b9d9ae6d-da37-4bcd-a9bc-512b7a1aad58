package com.pes.jd.mapper;

import com.pes.jd.model.DO.ShopDayOverviewDO;
import com.pes.jd.model.DTO.ShopDayOverviewDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShopOvDayMapper {
	
	int batchInsertShopDayOverview(@Param("shopId")Long shopId, 
			@Param("dayOverviewLst")List<ShopDayOverviewDO> dayOverviewLst, @Param("tableName")String tableName);

	int deleteShopDayOverviewByDate(@Param("shopId")Long shopId, 
			@Param("date")Date date, @Param("tableName")String tableName);

	int deleteShopDayOverviewByDateRange(@Param("shopId")Long shopId, 
			@Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("tableName")String tableName);

	ShopDayOverviewDTO selectShopShopDayOverviewByShopIdAndDate(@Param("shopId") Long shopId, @Param("date") Date date,@Param("tableName") String tableName);

}