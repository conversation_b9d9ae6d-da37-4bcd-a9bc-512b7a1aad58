package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsDutyRecordDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsDutyRecordMapper {

	int insertBatch(@Param("shopId")Long shopId, 
			@Param("csDutyRecordDOList")List<CsDutyRecordDO> csDutyRecordDOList, 
			@Param("tableName")String tableName);

	int deleteCsDutyRecordByShopIdByDate(@Param("shopId")Long shopId, 
			@Param("date")Date date, 
			@Param("tableName")String tableName);

	List<CsDutyRecordDO> searchByShopDate(
			@Param("shopId") long shopId,
			@Param("beginDate") Date beginDate,
			@Param("endDate") Date endDate,
			@Param("tableName") String tableName
	);

}
