package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsWordnumDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsWordnumMapper {
	int insertCsWordnum(CsWordnumDO record);
	
	int batchInsertCsWordnum(@Param("csWordnumLst")List<CsWordnumDO> csWordnumLst, 
			@Param("tableName")String tableName);

	int deleteCsWordnumById(Long id);
	
	int deleteShopCsWordnumByDate(@Param("shopId")Long shopId, 
			@Param("startDate")Date startDate, 
			@Param("endDate")Date endDate, 
			@Param("tableName")String tableName);

	int updateCsWordnumBySelective(CsWordnumDO record);

	CsWordnumDO getCsWordnumById(Long id);

}