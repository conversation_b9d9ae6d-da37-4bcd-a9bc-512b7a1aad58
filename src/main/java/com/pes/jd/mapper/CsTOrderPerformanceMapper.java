package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsTOrderPerformanceDO;
import com.pes.jd.model.DTO.CsTOrderPerformanceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsTOrderPerformanceMapper {

    int insertCsTOrderPerformances(
    		@Param("shopId")Long shopId, 
    		@Param("csTOrderPerformanceLst")List<CsTOrderPerformanceDO> csTOrderPerformanceLst,
    		@Param("tableName")String tableName);

    int deleteCsTOrderPerformances(
    		@Param("shopId")Long shopId, 
    		@Param("date")Date date,
    		@Param("tableName")String tableName);

    int updateCsTOrderPerformanceBySelective(@Param("csTOrderPerformance")CsTOrderPerformanceDO csTOrderPerformance,
    		@Param("tableName")String tableName);

	int batchUpdateShopCsSaleAndOutStackData(
			@Param("csSaleDataLst") List<CsTOrderPerformanceDO> csSaleDataLst,
			@Param("tableName") String tableName);
	int batchUpdateShopCsOutStackData(
			@Param("csSaleDataLst") List<CsTOrderPerformanceDO> csSaleDataLst,
			@Param("tableName") String tableName);

    List<CsTOrderPerformanceDTO> selectCsTOrderPerformanceLst(
    		@Param("shopId")Long shopId,
    		@Param("date")Date date,
    		@Param("tableName")String tableName);

	List<CsTOrderPerformanceDO> selectCsTOrderPerformanceLstForUpdate(
			@Param("shopId")Long shopId,
			@Param("date")Date date,
			@Param("tableName")String tableName);

	CsTOrderPerformanceDO selectCsTOrderPerformanceByDateAndCsNick(
			@Param("shopId") Long shopId,
			@Param("date") Date date,
			@Param("tableName") String tableName,
			@Param("csNick") String csNick);


}