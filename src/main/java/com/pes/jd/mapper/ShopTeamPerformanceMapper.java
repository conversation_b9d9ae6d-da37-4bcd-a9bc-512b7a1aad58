package com.pes.jd.mapper;

import com.pes.jd.model.DO.ShopTeamPerformanceDO;
import com.pes.jd.model.DTO.ShopTeamPerformanceDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShopTeamPerformanceMapper {
	
	int insertShopTeamPerformances(@Param("recordLst")List<ShopTeamPerformanceDO> recordLst, @Param("tableName")String tableName);
	
    int deleteShopTeamPerformance(@Param("shopId")Long shopId, 
    		@Param("date")Date date, 
    		@Param("tableName")String tableName);

    int deleteShopTeamPerformances(@Param("shopId")Long shopId, 
    		@Param("startDate")Date startDate, 
    		@Param("endDate")Date endDate, 
    		@Param("tableName")String tableName);
    
//    int updateShopTeamPerformanceByShopIdSelective(ShopTeamPerformanceDO record);

    ShopTeamPerformanceDTO getShopTeamPerformanceById(@Param("shopId")Long shopId, @Param("id")Long id, 
    		@Param("tableName")String tableName);


}