package com.pes.jd.mapper;

import com.pes.jd.model.DTO.ShopTeamOvDayDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface ShopTeamOvDayMapper {

	int deleteShopTeamOvDayByShopIdByDate(@Param("shopId") Long shopId, 
			@Param("date") Date date,
			@Param("tableName") String tableName);

	int insertShopTeamOvDay(@Param("shopTeamOvDay") ShopTeamOvDayDTO shopTeamOvDay, 
			@Param("tableName") String tableName);

}
