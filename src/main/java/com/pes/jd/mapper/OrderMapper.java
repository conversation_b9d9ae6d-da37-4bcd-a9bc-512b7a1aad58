package com.pes.jd.mapper;


import com.pes.jd.model.DO.CsNickAndOrderIdDo;
import com.pes.jd.model.DO.Order;
import com.pes.jd.model.DTO.*;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface OrderMapper {
    int insertOrderCancel(
            @Param("tableName") String tableName, @Param("order") OrderDTO order);

    int deleteOrderCancelByOrderId(
            @Param("oid") Long oid, @Param("tableName") String tableName);

    int deleteOrderInvoiceInfo(
            @Param("oid") Long oid, @Param("shopId") Long shopId, @Param("tableName") String tabelName);

    int insertOrderInvoiceInfo(
            @Param("invoiceInfoDTO") InvoiceInfoDTO invoiceInfoDTO, @Param("tableName") String tableName);

    int deleteOrderByOrderId(
            @Param("oid") Long oid, @Param("tableName") String tableName);

    int deleteAddressByOrderIdAndShopId(
            @Param("oid") Long oid, @Param("tableName") String tableName, @Param("shopId") Long shopId);

    int deleteJcqMessageByOrderIdAndShopId(
            @Param("oid") Long oid, @Param("tableName") String tableName, @Param("shopID") Long shopId);

    int insertJcqMessage(
            @Param("tableName") String tableName, @Param("jcqMessage") JcqMessageDTO jcqMessage);

    int deleteOrderCustomerInfo(
            @Param("oid") Long oid, @Param("tableName") String tableName, @Param("shopId") Long shopId);

    int insertOrderCustomerInfo(
            @Param("tableName") String tableName, @Param("customerInfoDTO") CustomerInfoDTO customerInfoDTO);

    int insertAddress(
            @Param("tableName") String tableName, @Param("address") AddressDTO address);

    int updateTradeById(Order record);

    int updateOrderStatusByOrderIdAndDate(
            @Param("status") String status,
            @Param("tids") Set<Long> tids,
            @Param("tableName") String tableName);

    Order getTradeById(Long tradeId);

    int persistOrderByFile(
            @Param("filePath") String filePath, @Param("tableName") String tableName);

    int deleteOrdersByTids(
            @Param("tids") List<Long> tids, @Param("tableName") String tableName);

    List<Long> selectIdsByTradeIds(
            @Param("tids") List<Long> tids, @Param("tableName") String tableName);

    int persistOrders(
            @Param("orders") List<OrderDTO> orders, @Param("tableName") String tableName);

    List<BuyerOrderDTO> selectShopCreatedOrderLstByBuyersAndDateForFirstConsult(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate, @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst, @Param("tableName") String tableName);


    List<BuyerOrderDTO> selectShopCreatedOrderLstByBuyersAndDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("filterOrderIds") Set<Long> filterOrderIds,
            @Param("tableName") String tableName);


    List<BuyerOrderDTO> selectShopOrderLstByBuyersAndDateForAfterSale(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate, @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst, @Param("tableName") String tableName);

    List<BuyerOrderDTO> selectShopPaidOrderLstByBuyersAndDate(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("buyerLst") List<String> buyerLst,
            @Param("filterOrderIds") Set<Long> filterOrderIds,
            @Param("tableName") String tableName);


    List<CsSaleIndexPfDTO> selectCsTeamConfirmGoodsIndex(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("pesOrerIdLst") List<Long> pesOrerIdLst,
            @Param("tableName") String tableName);

    List<Long> selectShopCsConfirmGoodsOrderIdLst(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName);

    List<Long> selectShopOutStockOrderIdLstByOrderIdLst(
            @Param("shopId") Long shopId,
            @Param("orderIdLst") List<Long> orderIdLst,
            @Param("tableName") String tableName,
            @Param("endOutValid") Date endOutValid);

    List<BuyerOrderDTO> selectPresaleBalancePayOrderLstByOrderLst(
            @Param("orderIdLst") List<Long> orderIdLst,
            @Param("tableName") String tableName);


    List<OrderDTO> selectShopCsOrderByCsByDate(
            @Param("shopId") Long shopId,
            @Param("csLst") List<CsDTO> csLst,
            @Param("date") Date date,
            @Param("tableName") String tableName);

    List<SilentOrderLossDTO> selectShopSilentCreatedOrderNoChatLossByDate(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("pesOrder") String pesOrder,
            @Param("pesCsOrderIndexs") List<DateRangeParam> tableNamesOfMonth);

    List<SilentOrderLossDTO> selectBuyerNickWasTodayPlacedAndPaid(
            @Param("shopId") Long shopId,
            @Param("date") Date date,
            @Param("pesOrder") String pesOrder,
            @Param("buyerNickSet") Set<String> buyerNickSet);

    List<Long> selectShopPayOrderListByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderIdList") List<Long> lossEnquiryOrderIdList,
            @Param("tableName") String tableName);


    int updateOrderByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderId") Long orderId,
            @Param("outStockTime") Date outStockTime,
            @Param("status") String status,
            @Param("tableName") String tableName);

    List<OrderDTO> selectShopCsOutStockLossOrderByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderIdList") List<Long> orderIdList,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName,
            @Param("endOutValidDate") Date endOutValidDate);

    List<Long> selectShopOutStockOrderLstByOrderIdLst(
            @Param("shopId") Long shopId,
            @Param("orderIdLst") List<Long> orderIdLst,
            @Param("tableName") String tableName,
            @Param("endOutValidDate") Date endOutValidDate);

    List<OrderDTO> selectShopOrderRefundByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderRefundIdSet") Set<String> orderRefundIdSet,
            @Param("tableName") String tableName);


    int updateBatchOrderByOrderId(
            @Param("orderList") List<OrderOutStockTimeDTO> orderList,
            @Param("tableName") String tableName);

    List<OrderDTO> selectShopOrderByShopIdByPayTime(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);

    List<OrderDTO> selectShopOrderByShopIdByCreated(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("type") Integer type);

    Double selectTotalFeeByOrderId(
            @Param("shopId") Long shopId,
            @Param("orderId") Long orderId,
            @Param("tableNames") List<DateRangeParam> tableNames);

    Set<Long> selectShopCashOrderByShopIdByOrderIdByCreated(
            @Param("shopId") Long shopId,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName,
            @Param("type") Integer type,
            @Param("orderIds") Set<Long> orderIds);

    List<OrderDTO> selectCanceledOrderByOrderLst(
            @Param("shopId") Long shopId,
            @Param("startDate") Date beginDate,
            @Param("endDate") Date endDate,
            @Param("tableName") String tableName,
            @Param("orderIds") List<Long> orderIds);

    List<OrderDTO> selectPreordainOrderByCreatedAndBuyers(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("beginDate") Date beginDate,
            @Param("endDate") Date endDate,
            @Param("buyers") Set<String> buyers);

    List<CsNickAndOrderIdDo> selectBuyerNickAndOrderIdByOrderIds(@Param("shopId") Long shopId,
                                                                 @Param("orderIds") Set<Long> orderIds,
                                                                 @Param("tableName") String tableName,
                                                                 @Param("beginDate") Date beginDate,
                                                                 @Param("endDate") Date endDate);

    List<OrderDTO> selectOrderDTOByShopIdAndOrderId(@Param("shopId") Long shopId, @Param("orderIds") List<Long> orderIds, @Param("tableName") String tableName);

    List<OrderDTO> selectOrderByShopIdAndOrderIds(
            @Param("shopId") Long shopId,
            @Param("tableNames") List<String> tableNames,
            @Param("orderIds") Set<Long> orderIds);
    List<OrderDTO> selectOrderByShopIdAndOrderIdsAndDate(
            @Param("shopId") Long shopId,
            @Param("tableNames") List<String> tableNames,
            @Param("orderIds") Set<Long> orderIds,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate
            );
    List<OrderDTO> selectShopOrderByShopIdByCreatedNew(@Param("shopId") Long shopId,
                                                       @Param("tableName") String tableName,
                                                       @Param("beginDate") Date beginDate,
                                                       @Param("endDate") Date endDate);

    List<OrderDTO> selectShopPresaleOrderByShopIdByDate(@Param("shopId") Long shopId,
                                                        @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate,
                                                        @Param("tableName") String tableName);
    List<OrderDTO> selectParentOrderToPayCsSaleOrderLst(
            @Param("shopId")Long shopId,
            @Param("csNick")String csNick,
            @Param("startDate")Date startDate,
            @Param("endDate")Date endDate,
            @Param("tableName")String tableName
    );

    List<OrderDTO> selectParentOrderToPayCsSaleOrderLstByShopId(
            @Param("shopId")Long shopId,
            @Param("startDate")Date startDate,
            @Param("endDate")Date endDate,
            @Param("tableName")String tableName
    );

    List<OrderDTO> selectOrderDirectTradeIdByOrderId(@Param("shopId") Long shopId, @Param("orderIds") List<Long> orderIds, @Param("tableName") String tableName);

    List<Long> selectOrderDirectTradeId(@Param("shopId") Long shopId,@Param("orderType")Long orderType, @Param("startDate")Date startDate, @Param("endDate")Date endDate, @Param("tableName") String tableName);

    List<OrderDTO> selectShopOrderByShopIdByCreatedByOrderIds(
            @Param("shopId") Long shopId,
            @Param("tableName") String tableName,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("orderIdSet") Collection<Long> orderIdSet,
            @Param("type") Integer type);


    List<Long> selectShopOrderPayTimeInAdjustDate(@Param("shopId") Long shopId,
                                                  @Param("orderIdLst") List<Long> orderIdLst,
                                                  @Param("tableName") String tableName);

    List<OrderDTO> selectTradeCanceledOrderByOrderIdLst(@Param("shopId")Long shopId,
                                                        @Param("tableName")String tableName,
                                                        @Param("orderIdLst")List<Long> orderIdLst);

    List<OrderDTO> selectParentOrderByShopIdAndDateAndBuyerNick(@Param("shopId") Long shopId,
                                                                @Param("startDate") Date startDate1,
                                                                @Param("endDate") Date endDate,
                                                                @Param("tableName") String tableName);

    List<OrderDTO> selectParentOrderByShopIdAndDateAndBuyerNickTwo(@Param("shopId") Long shopId,
                                                                @Param("startDate") Date startDate,
                                                                @Param("endDate") Date endDate,
                                                                @Param("tableName") String tableName);

    List<OrderDTO> selectParentOrderToPayCsSaleOrderLstNew(@Param("shopId") Long shopId,
                                                           @Param("startDate") Date startDate,
                                                           @Param("endDate") Date endDate,
                                                           @Param("tableName") String tableName);

    List<OrderDTO> selectOrdersByDateScopeAndBuyerNicks(@Param("shopId") Long shopId,
                                                        @Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate,
                                                        @Param("tableName") String tableName,
                                                        @Param("buyers") Set<String> buyers);

    List<Long> selectParentOrderByOrderIds(@Param("shopId") Long shopId,
                                               @Param("orderIds") List<Long> orderIds,
                                               @Param("tableName") String tableName);
}