package com.pes.jd.mapper;

import com.pes.jd.model.DTO.ShopCategoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopCategoryMapper {

	int batchInsertShopCategory(@Param("tableName")String tableName, @Param("shopCategoryLst")List<ShopCategoryDTO> shopCategoryLst);

	int deleteByShopCategoryTableName(@Param("tableName")String tableName, @Param("shopId")Long shopId);

	List<Long> selectMissingCategory(@Param("shopCategoryTable") String shopCategoryTable, @Param("shopGoodSkuTable") String shopGoodSkuTable,@Param("shopId") Long shopId);


}
