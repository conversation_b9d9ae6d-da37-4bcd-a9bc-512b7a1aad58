package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsNoreplyDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CsNoreplyMapper {

	int insertCsNoreply(CsNoreplyDO csNoreply);

	int batchInsertCsNoreply(@Param("tableName")String tableName, 
			@Param("csNoreplyLst")List<CsNoreplyDO> csNoreplyLst);

	int deleteCsNoreplyById(Long id);
	
	int deleteCsNoreplyByDate(@Param("shopId")Long shopId, 
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate, 
			@Param("tableName")String tableName);


	int updateCsNoreplyById(CsNoreplyDO csNoreply);
	
	CsNoreplyDO getCsNoreplyById(Long id);
}