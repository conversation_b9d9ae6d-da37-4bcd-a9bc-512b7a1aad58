package com.pes.jd.mapper;

import com.pes.jd.model.DO.CsOrderIndexDO;
import com.pes.jd.model.DTO.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;


public interface CsOrderIndexMapper {
	
    int insertCsOrderIndex(CsOrderIndexDO record);
    
	int batchInsertCsOrderIndex(@Param("shopId")Long shopId, @Param("csOrderIndexLst")List<CsOrderIndexDO> csOrderIndexLst,
			@Param("tableName")String tableName);

	int deleteCsOrderIndexById(Long id);
	
	int deleteShopCsOrderIndexByDate(@Param("shopId")Long shopId, @Param("date")Date date, 
			@Param("tableName")String tableName);
	
	int deleteCsOrderIndexByShopAndDate(@Param("shopId")Long shopId, 
			@Param("startDate")Date startDate, @Param("endDate")Date endDate, 
			@Param("tableName")String tableName);
	
	int updateCsOrderIndexsForAssist(@Param("csOrderIndeLst") List<CsOrderIndexDTO> csOrderIndeLst,
			@Param("tableName")String tableName);

    CsOrderIndexDO getCsOrderIndexById(Long id);

	List<CsOrderIndexDTO> selectShopCsOrderIndexLstByDate(@Param("shopId")Long shopId, 
			@Param("date")Date date,
			@Param("tableName")String tableName);
	
	List<CsOrderIndexDTO> selectShopCsOrderIndexLstByCsAndDate(@Param("shopId")Long shopId, @Param("csNick")String csNick,
			@Param("date")Date date,
			@Param("tableName")String tableName);

	List<CsOrderIndexForSearchDTO> searchByDateShop(@Param("tableName")String tableName,@Param("shopId")Long shopId,@Param("startDate")Date startDate,
										   @Param("endDate")Date endDate);
	
	List<CsOrderIndexDTO> selectShopCsOrderIndexLstByCsAndDateAndBuyerLst(@Param("shopId")Long shopId, 
			@Param("date")Date date, 
			@Param("csNick")String csNick,
			@Param("buyerNickLst")List<String> buyerNickLst, 
			@Param("tableName")String tableName);
	
	List<CsOrderIndexDTO> selectShopCsOrderIndexLstByDateAndBuyerLst(@Param("shopId")Long shopId, 
			@Param("date")Date date, 
			@Param("buyerNickLst")List<String> buyerNickLst, 
			@Param("tableName")String tableName);

	List<CsOrderIndexDTO> selectShopCsOrderIndexLstByDateRangeAndBuyerLst(
			@Param("shopId")Long shopId,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("buyerNickLst")List<String> buyerNickLst,
			@Param("tableName")String tableName);


	List<CsOrderIndexDTO> selectShopCsOrderIndexLstForEnquiry(
			@Param("shopId")Long shopId,
			@Param("date")Date date,
			@Param("csNick")String csNick,
			@Param("tableName")String tableName);

	List<SimpleOrderIndexDTO> selectShopCsOrderIndexOrderIdLstByCsAndDateAndBuyerLst(
			@Param("shopId")Long shopId,
			@Param("csNick")String csNick,
			@Param("date")Date date,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("buyerNickLst")List<String> buyerNickLst,
			@Param("tableName")String tableName);


	List<CsOrderIndexDTO> selectShopAllCsOrderIndexLstByCsAndDate(@Param("shopId")Long shopId,
			@Param("date")Date date,
			@Param("tableName")String tableName);
	


	List<CsOrderIndexDTO> selectOrderIndexByOrderIdByCs(@Param("shopId")Long shopId,
			@Param("sellerNick")String sellerNick, 
			@Param("shopGoodsReviewLst")List<ShopGoodsReviewDTO> shopGoodsReviewLst,
			@Param("tableName")String tableName,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate
			);
	

	List<GoodsAnalysisOrderIndexDTO> selectShopCsOrderIndexLstByCsNickAndOrderIdLstAndDate(@Param("shopId")Long shopId, 
			@Param("csNick")String csNick,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("orderIdSet")Set<Long> orderIdSet,
			@Param("tableName")String tableName);

	List<SilentOrderLossDTO> selectShopSilentCreatedOrderHasChatLossByDate(@Param("shopId")Long shopId,
			@Param("startDate")Date startDate,
			@Param("endDate")Date endDate,
			@Param("tableName")String tableName);

	List<CsOrderBindInfoDTO> selectShopCsOrderIndexByOrderIdLstForLossRecord(@Param("shopId")Long shopId, 
			@Param("orderIdLst")List<Long> orderIdLst,
			@Param("tableName")String tableName);

	List<String> selectAfterSaleBuyer(@Param("shopId")Long shopId,
								@Param("date") Date date,
								@Param("tableName") String tableName,
								@Param("buyerNickSet") Set<String> buyerNickSet);

    List<CsOrderIndexDTO> selectBalancePayShopCsOrderIndexLstByDate(@Param("shopId")Long shopId,
																	@Param("date") Date date,
																	@Param("tableName") String tableName);

    List<CsOrderIndexDTO> selectPresaleOrderIndexByShopIdAndDate(@Param("shopId")Long shopId,
																 @Param("startDate")Date startDate,
																 @Param("endDate")Date endDate,
																 @Param("tableName")String tableName);
}