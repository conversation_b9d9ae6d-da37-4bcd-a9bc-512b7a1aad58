package com.pes.jd.rest;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.constants.APICodeConstants;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.yiyitech.support.rpc.AbstractRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import com.yiyitech.support.sentinel.CB;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/7/21
 * @Modified By:
 */
@Component
public class MessageRestTemplate extends AbstractRestTemplate {

    @Override
    public <T> RestResponseTypeRef<T> postRest(String serviceId, String uri, HttpEntity<Object> param, ParameterizedTypeReference<RestResponseTypeRef<T>> paramTypeRef) {
        boolean result = CB.protect(super.RTMESSAGE);
        // 熔断后可以进行降级处理，降级代码
        if (!result) return  RestResponseTypeRef.ofFail(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, ApiCodeEnum.CODE_ERROR_COMMON_DEMOTE.getMsg());
        try {
            // 受保护的资源代码
            return  this.restRequestRef(serviceId, uri, HttpMethod.POST ,param, paramTypeRef).getBody();
        } finally {
            // 熔断阀值统计，确保执行
            CB.release();
        }
    }

    @Override
    public <T> RestResponseTypeRef<T> getRest(String serviceId, String uri, ParameterizedTypeReference<RestResponseTypeRef<T>> paramTypeRef) {
        boolean result = CB.protect(this.RTMESSAGE);
        // 熔断后可以进行降级处理，降级代码
        if (!result) return  RestResponseTypeRef.ofFail(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, ApiCodeEnum.CODE_ERROR_COMMON_DEMOTE.getMsg());
        try {
            // 受保护的资源代码
            return  this.restRequestRef(serviceId, uri,HttpMethod.GET, RequestEntityBuilder.builder().toRequestEntity(), paramTypeRef).getBody();
        } finally {
            // 熔断阀值统计，确保执行
            CB.release();
        }
    }

    @Override
    public <T> RestResponseTypeRef<T> putRest(String serviceId, String uri, HttpEntity<Object> param, ParameterizedTypeReference<RestResponseTypeRef<T>> paramTypeRef) {
        boolean result = CB.protect(super.RTMESSAGE);
        // 熔断后可以进行降级处理，降级代码
        if (!result) return  RestResponseTypeRef.ofFail(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, ApiCodeEnum.CODE_ERROR_COMMON_DEMOTE.getMsg());
        try {
            // 受保护的资源代码
            return  this.restRequestRef(serviceId, uri, HttpMethod.PUT ,param, paramTypeRef).getBody();
        } finally {
            // 熔断阀值统计，确保执行
            CB.release();
        }
    }

    @Override
    public <T> RestResponseTypeRef<T> deleteRest(String serviceId, String uri, HttpEntity<Object> param, ParameterizedTypeReference<RestResponseTypeRef<T>> paramTypeRef) {
        boolean result = CB.protect(super.RTMESSAGE);
        // 熔断后可以进行降级处理，降级代码
        if (!result) return  RestResponseTypeRef.ofFail(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, ApiCodeEnum.CODE_ERROR_COMMON_DEMOTE.getMsg());
        try {
            // 受保护的资源代码
            return  this.restRequestRef(serviceId, uri, HttpMethod.DELETE ,param, paramTypeRef).getBody();
        } finally {
            // 熔断阀值统计，确保执行
            CB.release();
        }
    }

    @Deprecated
    public ApiResponse postRest(String serviceId, String uri, HttpEntity<Object> requestEntity){

        boolean result = CB.protect(super.RTMESSAGE);
        // 熔断后可以进行降级处理，降级代码
        if (!result) return  ApiResponse.of(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, ApiCodeEnum.CODE_ERROR_COMMON_DEMOTE.getMsg());
        try {
            // 受保护的资源代码
            final String url = RestOperator.getRequestUrl(serviceId, uri);
            ApiResponse response = this.getRestTemplate().postForObject(url, requestEntity, ApiResponse.class);
            return response;
        } finally {
            // 熔断阀值统计，确保执行
            CB.release();
        }
    }

}
