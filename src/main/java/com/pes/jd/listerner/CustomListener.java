package com.pes.jd.listerner;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * 
 * ClassName: CustomListener <br/>  
 * Function: 自定义listener <br/>  
 * Reason: TODO ADD REASON(可选). <br/>  
 * date: 2018年10月24日 上午10:46:54 <br/>  
 *  
 * <AUTHOR>  
 * @version   
 * @since JDK 1.8
 */
@WebListener
public class CustomListener implements ServletContextListener {

	@Override
	public void contextInitialized(ServletContextEvent sce) {
		System.out.println("CustomListener contextInitialized");
	}

	@Override
	public void contextDestroyed(ServletContextEvent sce) {
		System.out.println("contextDestroyed");
	}

}
