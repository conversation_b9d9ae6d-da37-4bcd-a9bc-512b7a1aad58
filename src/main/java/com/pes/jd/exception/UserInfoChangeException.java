package com.pes.jd.exception;
/**
 * 用户信息变更Exception
 * <AUTHOR>
 *
 */
public class UserInfoChangeException extends Exception{

	private static final long serialVersionUID = 3708757363015799289L;
	private String errorMsg;
	private String errorCode;
	public UserInfoChangeException() {
		super();
	}
	public UserInfoChangeException(String messsage) {
		super(messsage);
	}
	public String getErrorMsg() {
		return errorMsg;
	}
	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
}
