package com.pes.jd.exception;

public class CsIsEmptyException extends Exception{

	private String errCode;
	private String errMsg;
	private String subErrCode;
	private String subErrMsg;

	public CsIsEmptyException() {
		super();
	}

	public CsIsEmptyException(String errMsg) {
		super();
		this.errMsg = errMsg;
	}

	public CsIsEmptyException(String errCode, String errMsg) {
		super();
		this.errCode = errCode;
		this.errMsg = errMsg;
	}

	public String getErrCode() {
		return errCode;
	}

	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	public String getSubErrCode() {
		return subErrCode;
	}

	public void setSubErrCode(String subErrCode) {
		this.subErrCode = subErrCode;
	}

	public String getSubErrMsg() {
		return subErrMsg;
	}

	public void setSubErrMsg(String subErrMsg) {
		this.subErrMsg = subErrMsg;
	}


}
