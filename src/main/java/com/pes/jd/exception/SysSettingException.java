package com.pes.jd.exception;

public class SysSettingException extends Exception{
	
	private static final long serialVersionUID = -3195885913898385936L;
	private String errorMsg;
	private String errorCode;
	
	
	public SysSettingException() {
		super();
	}
	public SysSettingException(String messsage) {
		super(messsage);
	}
	public String getErrorMsg() {
		return errorMsg;
	}
	public void setErrorMsg(String errorMsg) {
		this.errorMsg = errorMsg;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}


}
