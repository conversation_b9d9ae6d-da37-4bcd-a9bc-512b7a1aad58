package com.pes.jd.controller;

import com.pes.jd.business.DataAnalysisBusiness;
import com.pes.jd.business.GoodsQueryOfSpuBusiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.OrderPreOrdainParam;
import com.pes.jd.model.Param.OrderPresaleParam;
import com.pes.jd.model.Param.RefundAnalysisParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.ms.constant.PesCommonConstant;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.SecurityMUtil;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 成交数据分析 - Rest接口
 */
@RestController
@RequestMapping("/data-analysis/deal/")
public class DealDataAnalysisController extends BaseController {

    @Autowired
    private ShopSysManagerBusiness shopSysManagerBusiness;

    @Autowired
    private DataAnalysisBusiness dataAnalysisBusiness;

    @Autowired
    private GoodsQueryOfSpuBusiness goodsQueryOfSpuBusiness;

    private Logger logger = LoggerFactory.getLogger(DealDataAnalysisController.class);

    /**
     * 客服退款分析
     *
     * @param dateType     时间类型， 1.按退款成功时间查询、2.按退款申请时间查询、3.按订单下单时间查询、4.按订单付款时间查询
     * @param startDateStr 开始时间
     * @param endDateStr   结束时间
     * @param shopId       店铺Id
     * @param groupId      客服组Id
     * @param csNick       客服昵称
     * @param customer     顾客Id
     * @param orderId      订单编号
     * @param refundId     退款编号
     * @param refundStatus 退款状态
     * @param outStatus    出库状态
     * @param reason       退款原因
     * @param orderType    订单类型
     * @param skus         商品skuId 多个用","隔开("1,2,3"
     * @return 包含 客服退款分析 列表的 ApiResponse
     */
    @RequestMapping(value = "selectCsRefundAnalysisLst", method = RequestMethod.POST)
    public ApiResponse selectCsRefundAnalysisLst(
            @RequestParam(name = "dateType") Integer dateType,
            @RequestParam(name = "startDateStr") String startDateStr,
            @RequestParam(name = "endDateStr") String endDateStr,
            @RequestParam(name = "shopId") String shopId,
            @RequestParam(name = "groupId", required = false) String groupId,
            @RequestParam(name = "csNick", required = false) String csNick,
            @RequestParam(name = "customer", required = false) String customer,
            @RequestParam(name = "orderId", required = false) Long orderId,
            @RequestParam(name = "refundId", required = false) Long refundId,
            @RequestParam(name = "refundStatus", required = false) String refundStatus,
            @RequestParam(name = "outStatus", required = false) String outStatus,
            @RequestParam(name = "reason", required = false) String reason,
            @RequestParam(name = "orderType", required = false) String orderType,
            @RequestParam(name = "skus", required = false) String skus,
            HttpServletRequest request,Byte dimension) throws LoginAuthException {

        ApiResponse apiResponse;
        UserShopQuery userShopQuery = this.getCustUserByParam(shopId);
        ShopDTO currentShop = this.getCurrentShop();
        ShopUserDTO currentUser = this.getCurrentUser();
        Object deviceId = this.getDeviceId();
        if (dimension == null) {
            dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
        }
        try {
            List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(userShopQuery, groupId, csNick);
            RefundAnalysisParam param = new RefundAnalysisParam();
            param.setDateType(dateType);
            param.setStartDate(DateUtil.getStartDateFromDateStr(startDateStr));
            param.setEndDate(DateUtil.getEndDateFromDateStr(endDateStr));
            param.setShopId(shopId);
            param.setCsNickList(csNickList);
            param.setCustomer(customer);
            param.setOrderId(orderId);
            param.setRefundId(refundId);
            param.setRefundStatus(refundStatus);
            param.setOutStatus(outStatus);
            param.setReason(reason);
            param.setOrderType(orderType);
            param.setDimension(dimension);
            param.setUrl("/data-analysis/deal/selectCsRefundAnalysisLst");
            if (StringUtils.isNotBlank(skus)) {
                Long[] skuIds = (Long[]) ConvertUtils.convert(skus.split(","), Long.class);
                param.setSkuIds(Arrays.asList(skuIds));
            }

            //封装上报订单日志需要的参数
            OrderInfoLogUploadParam orderInfoLogUploadParam = initOrderInfoLogUploadParam(request, currentShop, currentUser, deviceId);
            if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                apiResponse = dataAnalysisBusiness.selectCsRefundAnalysisLst(userShopQuery, param, orderInfoLogUploadParam);
            } else {
                apiResponse = goodsQueryOfSpuBusiness.selectCsRefundAnalysisLst(userShopQuery, param, orderInfoLogUploadParam);
            }
        } catch (Exception e) {
            logger.error("web selectChatSessionLstOnNonReply error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01);
        }
        return apiResponse;
    }

    /**
     * 静默退款分析
     *
     * @param dateType     时间类型， 1.按退款成功时间查询、2.按退款申请时间查询、3.按订单下单时间查询、4.按订单付款时间查询
     * @param startDateStr 开始时间
     * @param endDateStr   结束时间
     * @param shopId       店铺Id
     * @param customer     顾客Id
     * @param orderId      订单编号
     * @param refundId     退款编号
     * @param refundStatus 退款状态 0.全部 1.处理中 2.已关闭 3.已完成
     * @param outStatus    出库状态 0.全部 1.未出库 2.已出库
     * @param reason       退款原因
     * @param orderType    订单类型
     * @param skus         商品skuId 多个用","隔开("1,2,3"
     * @return 包含 客服退款分析 列表的 ApiResponse
     */
    @RequestMapping(value = "selectSilentRefundAnalysisLst", method = RequestMethod.POST)
    public ApiResponse selectSilentRefundAnalysisLst(
            @RequestParam("dateType") Integer dateType,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr,
            @RequestParam("shopId") String shopId,
            @RequestParam(name = "customer", required = false) String customer,
            @RequestParam(name = "orderId", required = false) Long orderId,
            @RequestParam(name = "refundId", required = false) Long refundId,
            @RequestParam(name = "refundStatus", required = false) String refundStatus,
            @RequestParam(name = "outStatus", required = false) String outStatus,
            @RequestParam(name = "reason", required = false) String reason,
            @RequestParam(name = "orderType", required = false) String orderType,
            @RequestParam(name = "skus", required = false) String skus,
            HttpServletRequest request,Byte dimension) throws LoginAuthException {

        ApiResponse apiResponse;
        UserShopQuery shop = this.getCustUserByParam(shopId);
        ShopUserDTO currentUser = this.getCurrentUser();
        ShopDTO currentShop = this.getCurrentShop();
        Object deviceId = this.getDeviceId();
        if (dimension == null) {
            dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
        }
        try {
            RefundAnalysisParam param = new RefundAnalysisParam();
            param.setDateType(dateType);
            param.setStartDate(DateUtil.getStartDateFromDateStr(startDateStr));
            param.setEndDate(DateUtil.getEndDateFromDateStr(endDateStr));
            param.setShopId(shopId);
            param.setCsNickList(null);
            param.setCustomer(customer);
            param.setOrderId(orderId);
            param.setRefundId(refundId);
            param.setRefundStatus(refundStatus);
            param.setOutStatus(outStatus);
            param.setReason(reason);
            param.setOrderType(orderType);
            param.setDimension(dimension);
            param.setUrl("/data-analysis/deal/selectSilentRefundAnalysisLst");
            if (StringUtils.isNotBlank(skus)) {
                Long[] skuIds = (Long[]) ConvertUtils.convert(skus.split(","), Long.class);
                param.setSkuIds(Arrays.asList(skuIds));
            }

            //封装上报订单日志需要的参数
            OrderInfoLogUploadParam orderInfoLogUploadParam = initOrderInfoLogUploadParam(request, currentShop, currentUser, deviceId);
            if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                apiResponse = dataAnalysisBusiness.selectCsRefundAnalysisLst(shop, param, orderInfoLogUploadParam);
            } else {
                apiResponse = goodsQueryOfSpuBusiness.selectCsRefundAnalysisLst(shop, param, orderInfoLogUploadParam);
            }
        } catch (Exception e) {
            logger.error("web selectSilentRefundAnalysisLst error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01);
        }
        return apiResponse;
    }

    /**
     * 查询预售订单分析列表
     *
     * @param dateType  时间类型，1.按付尾款时间 2.按付定金时间查询
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param buyerNick 顾客ID
     * @param orderId   订单编号
     * @param tradeType 交易类型 0.全部 1.成交 2付尾款流失
     * @return ApiResponse
     */
    @RequestMapping(value = "selectOrderPresaleLst", method = RequestMethod.POST)
    public ApiResponse selectOrderPresaleLst(
            @RequestParam(name = "dateType") Integer dateType,
            @RequestParam(name = "startDate") String startDate,
            @RequestParam(name = "endDate") String endDate,
            @RequestParam(name = "shopId") String shopId,
            @RequestParam(name = "groupId", required = false) String groupId,
            @RequestParam(name = "csNick", required = false) String csNick,
            @RequestParam(name = "buyerNick", required = false) String buyerNick,
            @RequestParam(name = "orderId", required = false) Long orderId,
            @RequestParam(name = "tradeType", required = false) Integer tradeType,
            @RequestParam(name = "propertity", required = false) String propertity,
            @RequestParam(name = "sortDirection", required = false) String sortDirection,
            @RequestParam(name = "currentPage", required = false) Integer currentPage,
            @RequestParam(name = "size", required = false) Integer size,
            HttpServletRequest request) throws LoginAuthException {

        ApiResponse apiResponse;
        //参数check
        if (StringUtils.isEmpty(shopId)) return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01);

        UserShopQuery userShopQuery = this.getCustUserByParam(shopId);
        ShopSystemsettingDTO shopSystemsetting = this.getShopSystemsetting();
        ShopDTO currentShop = this.getCurrentShop();
        ShopUserDTO currentUser = this.getCurrentUser();
        Object deviceId = this.getDeviceId();
        try {
            List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(userShopQuery, groupId, csNick);

            OrderPresaleParam orderPresaleParam = new OrderPresaleParam();
            orderPresaleParam.setDateType(dateType);
            orderPresaleParam.setStartDate(DateUtil.getStartDateFromDateStr(startDate));
            orderPresaleParam.setEndDate(DateUtil.getEndDateFromDateStr(endDate));
            orderPresaleParam.setGroupId(StringUtils.isBlank(groupId) && StringUtils.isNotBlank(csNick) ? "-1" : groupId);
            orderPresaleParam.setBuyerNick(buyerNick);
            orderPresaleParam.setOrderId(orderId);
            orderPresaleParam.setTradeType(tradeType);
            orderPresaleParam.setCsNickLst(csNickList);
            orderPresaleParam.setEnquiryValidDurationTime(shopSystemsetting.getEnquiryValidDurationTime());

            SortPageQuery sortPageQuery = new SortPageQuery();
            sortPageQuery.setPropertity(propertity);
            sortPageQuery.setSortDirection(sortDirection);
            sortPageQuery.setSize(size == null ? 0L : size.longValue());
            sortPageQuery.setCurrentPage((currentPage != null ? currentPage * sortPageQuery.getSize() : 0L));

            //封装上报订单日志需要的参数

            OrderInfoLogUploadParam orderInfoLogUploadParam = initOrderInfoLogUploadParam(request, currentShop, currentUser, deviceId);

            apiResponse = dataAnalysisBusiness.selectOrderPresaleLst(userShopQuery, orderPresaleParam, sortPageQuery, orderInfoLogUploadParam);
        } catch (Exception e) {
            logger.error("web selectOrderPresaleLst error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01);
        }

        return apiResponse;
    }

    private OrderInfoLogUploadParam initOrderInfoLogUploadParam(HttpServletRequest request, ShopDTO currentShop, ShopUserDTO currentUser, Object deviceId) {
        OrderInfoLogUploadParam orderInfoLogUploadParam = new OrderInfoLogUploadParam();
        orderInfoLogUploadParam.setJdId(currentShop.getTitle());
        orderInfoLogUploadParam.setDeviceId((String) deviceId);
        orderInfoLogUploadParam.setUserId(currentUser.getNick());
        orderInfoLogUploadParam.setUserIp(SecurityMUtil.getIpAddr(request));
        orderInfoLogUploadParam.setTimeStamp(System.currentTimeMillis());
        return orderInfoLogUploadParam;
    }


    /**
     * 查询预预约订单分析列表
     *
     * @param dateType  时间类型，1.按下单时间 2.按付尾款时间查询
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param buyerNick 顾客ID
     * @param orderId   订单编号
     * @param tradeType
     * @return ApiResponse
     */
    @RequestMapping(value = "selectOrderPredainLst", method = RequestMethod.POST)
    public ApiResponse selectOrderPredainLst(
            @RequestParam(name = "dateType") Integer dateType,
            @RequestParam(name = "startDate") String startDate,
            @RequestParam(name = "endDate") String endDate,
            @RequestParam(name = "shopId") String shopId,
            @RequestParam(name = "groupId", required = false) String groupId,
            @RequestParam(name = "csNick", required = false) String csNick,
            @RequestParam(name = "buyerNick", required = false) String buyerNick,
            @RequestParam(name = "orderId", required = false) Long orderId,
            @RequestParam(name = "tradeType", required = false) Integer tradeType,
            @RequestParam(name = "propertity", required = false) String propertity,
            @RequestParam(name = "sortDirection", required = false) String sortDirection,
            @RequestParam(name = "currentPage", required = false) Integer currentPage,
            @RequestParam(name = "size", required = false) Integer size,
            HttpServletRequest request) throws LoginAuthException {

        //参数check
        if (StringUtils.isEmpty(shopId)) return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01);

        UserShopQuery userShopQuery = this.getCustUserByParam(shopId);
        ShopSystemsettingDTO shopSystemsetting = this.getShopSystemsetting();
        ShopDTO currentShop = this.getCurrentShop();
        ShopUserDTO currentUser = this.getCurrentUser();
        Object deviceId = this.getDeviceId();
        try {
            List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(userShopQuery, groupId, csNick);

            OrderPreOrdainParam orderPreOrdainParam = new OrderPreOrdainParam();
            orderPreOrdainParam.setDateType(dateType);
            orderPreOrdainParam.setStartDate(DateUtil.getStartDateFromDateStr(startDate));
            orderPreOrdainParam.setEndDate(DateUtil.getEndDateFromDateStr(endDate));
            orderPreOrdainParam.setGroupId(StringUtils.isBlank(groupId) && StringUtils.isNotBlank(csNick) ? "-1" : groupId);
            orderPreOrdainParam.setBuyerNick(buyerNick);
            orderPreOrdainParam.setOrderId(orderId);
            orderPreOrdainParam.setTradeType(tradeType);
            orderPreOrdainParam.setCsNickLst(csNickList);
            orderPreOrdainParam.setEnquiryValidDurationTime(shopSystemsetting.getEnquiryValidDurationTime());

            SortPageQuery sortPageQuery = new SortPageQuery();
            sortPageQuery.setPropertity(propertity);
            sortPageQuery.setSortDirection(sortDirection);
            sortPageQuery.setSize(size == null ? 0L : size.longValue());
            sortPageQuery.setCurrentPage((currentPage != null ? currentPage * sortPageQuery.getSize() : 0L));

            //封装上报订单日志需要的参数

            OrderInfoLogUploadParam orderInfoLogUploadParam = initOrderInfoLogUploadParam(request, currentShop, currentUser, deviceId);

            DataAnalysisVO<OrderPreordainDTO> daVo = dataAnalysisBusiness.selectOrderPredianLst(userShopQuery, orderPreOrdainParam, sortPageQuery, orderInfoLogUploadParam);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,daVo);
        } catch (Exception e) {
            logger.error("web selectOrderPredainLst error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_09_01);
        }
    }
}
