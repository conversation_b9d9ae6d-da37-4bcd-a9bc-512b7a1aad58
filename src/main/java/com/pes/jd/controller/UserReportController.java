package com.pes.jd.controller;

import com.google.common.collect.ImmutableMap;
import com.pes.jd.business.main.UserReportPropertyBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/20 10:21 AM
 * @since 1.0.0
 */
@RequestMapping("/user/report")
@RestController
public class UserReportController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserReportController.class);
    @Autowired
    private UserReportPropertyBusiness reportPropertyBusiness;
    /**
     * @param type 1:店铺绩效  2: 客服绩效
     * @return
     */
    @RequestMapping("/get")
    public Object get(Integer type){

        try {
            return ApiResponse.of(
                    ApiCodeEnum.CODE_SUCCESS_1001,
                    ImmutableMap.<String,Object>builder()
                    .put(ApiResponse.RESULT_SINGLE_NAME,reportPropertyBusiness
                            .getUserReportPropertyByUserReturnList(type))
                    .build()
            );
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_01);
        }
    }



}
