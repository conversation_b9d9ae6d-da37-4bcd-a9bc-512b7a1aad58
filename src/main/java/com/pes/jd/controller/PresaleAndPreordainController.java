/**
 * Project Name:jd-pes
 * File Name:ShopPerformanceController.java
 * Package Name:com.pes.jd.controller
 * Date:2018年10月25日下午3:09:57
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.
 */

package com.pes.jd.controller;


import com.pes.jd.business.sub.PresaleAndPreordainPerformanceBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
 * ClassName:ShopPerformanceController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 下午3:09:57 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/performance/presaleAndPreordain/")
public class PresaleAndPreordainController extends BaseController {

    private final static Logger LOGGER = LoggerFactory.getLogger(PresaleAndPreordainController.class);

    @Autowired
    private PresaleAndPreordainPerformanceBusiness presaleAndPreordainPerformanceBusiness;


    @RequestMapping(value = "/selectShopPresale", method = RequestMethod.POST)
    public Object selectShopPresale(
            @RequestParam("shop") String shop,
            @RequestParam("startDate") String startDateStr,
            @RequestParam("endDate") String endDateStr,
            @RequestParam(name = "skuIdOrName", required = false) String skuIdOrName,
            @RequestParam(name = "enquiryValidDurationTime") Integer enquiryValidDurationTime
    ) {

        try {
            Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
            ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);
            return RestApiResponse2.of(true, ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(),
                    presaleAndPreordainPerformanceBusiness.selectShopPresale(shopQuery, startDate, endDate, skuIdOrName, enquiryValidDurationTime));
        } catch (Exception e) {
            return RestApiResponse2.of(false, ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getCode(), ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getMsg());
        }
    }

    @RequestMapping(value = "/selectShopPresaleDetails", method = RequestMethod.POST)
    public Object selectShopPresaleDetails(
            @RequestParam("shop") String shop,
            @RequestParam(name = "activityId", required = false) String activityId,
            @RequestParam("skuId") Long skuId,
            @RequestParam(name = "startDate", required = false) String startDateStr,
            @RequestParam(name = "endDate", required = false) String endDateStr,
            @RequestParam("type") Integer type) {

        try {
            Date startDate = StringUtils.isEmpty(startDateStr) ? null : DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = StringUtils.isEmpty(endDateStr) ? null : DateUtil.getEndDateFromDateStr(endDateStr);
            ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);
            return RestApiResponse2.of(true, ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(),
                    presaleAndPreordainPerformanceBusiness.selectShopPresaleDetails(shopQuery, activityId, skuId, startDate, endDate, type));
        } catch (Exception e) {
            return RestApiResponse2.of(false, ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getCode(), ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getMsg());

        }
    }


    @RequestMapping(value = "/selectCsPresale", method = RequestMethod.POST)
    public Object selectCsPresale(
            @RequestParam("shop") String shop,
            @RequestParam("startDate") String startDateStr,
            @RequestParam("endDate") String endDateStr,
            @RequestParam("csNickMap") String csNickMap,
            @RequestParam(name = "skuIdOrName", required = false) String skuIdOrName,
            @RequestParam(name = "enquiryValidDurationTime") Integer enquiryValidDurationTime
    ) {

        try {
            Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
            ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);
            Map<String, String> csNicks = JacksonUtils.json2map(csNickMap, String.class);
            return RestApiResponse2.of(true, ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(),
                    presaleAndPreordainPerformanceBusiness.selectCsPresale(shopQuery, startDate, endDate, skuIdOrName, csNicks, enquiryValidDurationTime));
        } catch (Exception e) {
            return RestApiResponse2.of(false, ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getCode(), ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getMsg());
        }
    }

    @RequestMapping(value = "/selectCsPresaleDetails", method = RequestMethod.POST)
    public Object selectCsPresaleDetails(
            @RequestParam("shop") String shop,
            @RequestParam(name = "activityId", required = false) String activityId,
            @RequestParam("skuId") Long skuId,
            @RequestParam(name = "startDate", required = false) String startDateStr,
            @RequestParam(name = "endDate", required = false) String endDateStr,
            @RequestParam("csNick") String csNick) {

        try {
            Date startDate = StringUtils.isEmpty(startDateStr) ? null : DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = StringUtils.isEmpty(endDateStr) ? null : DateUtil.getEndDateFromDateStr(endDateStr);
            ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);
            return RestApiResponse2.of(true, ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(),
                    presaleAndPreordainPerformanceBusiness.selectCsPresaleDetails(shopQuery, activityId, skuId, startDate, endDate, csNick));
        } catch (Exception e) {
            return RestApiResponse2.of(false, ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getCode(), ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT.getMsg());
        }
    }


}

