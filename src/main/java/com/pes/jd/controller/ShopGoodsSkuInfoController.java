package com.pes.jd.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pes.jd.business.sub.*;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.framework.FormUrlencoded;
import com.pes.jd.model.DO.ShopRecommendSkuDO;
import com.pes.jd.model.DTO.ShopGoodsLabelDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SkuQuery;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
@RequestMapping("/shop_sku")
@RestController
public class ShopGoodsSkuInfoController {

    @Autowired
    private ShopGoodsSkuLabelBusiness shopGoodsSkuLabelBusiness;

    @Autowired
    private ShopGoodsInfoBussiness shopGoodsInfoBussiness;

    @Autowired
    private ShopRecommendSkuBusiness shopRecommendSkuBusiness;

    @Autowired
    private ShopGoodsSkuAssociativeBusiness shopGoodsSkuAssociativeBusiness;

    @Autowired
    private ShopGoodsLabelBusiness shopGoodsLabelBusiness;

    private static final Logger logger = LoggerFactory.getLogger(ShopGoodsSkuInfoController.class);

    @RequestMapping("/search")
    public Object searchShopGoodsSku(String categoryId,
                                     Long level,
                                     String skuName,
                                     String status,
                                     Integer pageSize,
                                     Integer pageNum,
                                     Long ownSkuId,
                                     String propertity,
                                     String sortDirection,
                                     String associativeStatus,
                                     @FormUrlencoded SkuQuery skuQuery,
                                     Integer addStatus,
                                     Byte dimension) {
        try {
            ShopCommonParam param = new ShopCommonParam();
            param.setDbName(skuQuery.getDbName());
            param.setSchemaId(skuQuery.getSchemaId());
            param.setShopId(skuQuery.getShopId());
            //获取结果集
            Map<String, Object> res = getResultMap(
                    param, categoryId, level, skuName, status,
                    Lists.newArrayList(), pageSize, pageNum, ownSkuId, propertity, sortDirection,
                    associativeStatus, addStatus, dimension
            );
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, res);
        } catch (Exception e) {
            logger.error("get shop sku info error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01);
        }
    }

    private Map<String, Object> getResultMap(ShopCommonParam param, String categoryId, Long level, String skuName, String status, List<Long> skuIdLst, Integer pageSize, Integer pageNum, Long ownSkuId, String propertity, String sortDirection, String associativeStatus, Integer addStatus, Byte dimension) {

        if(dimension==null){
            dimension = CommonConstants.DIMENSION_SKU_OF_BYTE;
        }
        Map<String, Object> res;
        if (null == addStatus && CommonConstants.DIMENSION_SKU_OF_BYTE.equals(dimension)) {//添加状态为全部_开闭原则  //未使用状态查找并且是sku维度
            logger.info("aj_sku维度 不带添加状态查询");
            res = shopGoodsInfoBussiness
                    .selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods(
                            param, categoryId, level, skuName, status,
                            skuIdLst, pageSize, pageNum, ownSkuId, propertity, sortDirection,
                            associativeStatus
                    );
        } else {
            logger.info("aj_spu维度 查询 || 添加状态参数");
            res = shopGoodsInfoBussiness
                    .selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(
                            param, categoryId, level, skuName, status,
                            skuIdLst, pageSize, pageNum, ownSkuId, propertity, sortDirection,
                            associativeStatus, addStatus, dimension
                    );
        }
        res.put("dimension",dimension);
        return res;
    }

    @RequestMapping("/delete_sku_label")
    public Object deleteShopSkuLabel(@FormUrlencoded SkuQuery skuQuery,Long labelId){
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopGoodsSkuLabelBusiness.deleteSkuLabel(labelId,skuQuery));
        } catch (Exception e) {
            logger.error("delete sku label error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_02);
        }
    }

    @RequestMapping("/delete_good_label")
    public Object deleteShopGoodLabel(@FormUrlencoded SkuQuery skuQuery,Long labelId) {
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopGoodsLabelBusiness.deleteGoodsLabelAndSkuLabel(labelId, skuQuery));
        } catch (Exception e) {
            logger.error("delete good label error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_02);
        }
    }

    @RequestMapping("/update_sku_label")
    public Object updateSkuLabel(@FormUrlencoded SkuQuery skuQuery,
                                 @FormUrlencoded ShopGoodsSkuLabelDTO label){
        try {
            Assert.notNull(label.getId(),"update label must has id");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopGoodsSkuLabelBusiness.updateSkuLabel(label,skuQuery));
        } catch (Exception e) {
            logger.error("update sku label error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_03);
        }
    }

    @RequestMapping("/update_good_label")
    public Object updateGoodLabel(@FormUrlencoded SkuQuery skuQuery,
                                 @FormUrlencoded ShopGoodsSkuLabelDTO label) {
        try {
            Assert.notNull(label.getId(), "update label must has id");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopGoodsLabelBusiness.updateGoodLabel(skuLabelConvertGoodlabel(label), skuQuery));
        } catch (Exception e) {
            logger.error("update good label error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_03);
        }
    }

    @RequestMapping("/insert_sku_label")
    public Object insertSkuLabel(@FormUrlencoded SkuQuery skuQuery,
                                 @FormUrlencoded ShopGoodsSkuLabelDTO label){
        try {
            label.setId(null);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    ImmutableMap.of("id",shopGoodsSkuLabelBusiness.insertSkuLabel(label,skuQuery)
                            ,"desc","如果为0说明没添加成功"));
        } catch (Exception e) {
            logger.error("insert sku label error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_04);
        }
    }

    @RequestMapping("/insert_good_label")
    public Object insertGoodLabel(@FormUrlencoded SkuQuery skuQuery,
                                 @FormUrlencoded ShopGoodsSkuLabelDTO label){
        try {
            label.setId(null);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    ImmutableMap.of("id",shopGoodsLabelBusiness.insertGoodsLabel(skuLabelConvertGoodlabel(label),skuQuery)
                            ,"desc","如果为0说明没添加成功"));
        } catch (Exception e) {
            logger.error("insert good label error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_04);
        }
    }
    //对象转换 从sku维度转换为spu维度
    private ShopGoodsLabelDTO skuLabelConvertGoodlabel(ShopGoodsSkuLabelDTO label) {
        ShopGoodsLabelDTO shopGoodsLabelDTO = new ShopGoodsLabelDTO();
        shopGoodsLabelDTO.setId(label.getId());
        shopGoodsLabelDTO.setShopId(label.getShopId());
        shopGoodsLabelDTO.setWareId(label.getSkuId());
        shopGoodsLabelDTO.setLabel(label.getLabel());
        return shopGoodsLabelDTO;
    }


    @RequestMapping("/insert_recommend")
    public Object insertRecommend(@FormUrlencoded SkuQuery skuQuery,
                                  @FormUrlencoded ShopRecommendSkuDO recommend){
        try {
            recommend.setId(null);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopRecommendSkuBusiness.insert(recommend,skuQuery));
        } catch (Exception e) {
            logger.error("insert recommend error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_05);
        }
    }

    @RequestMapping("/delete_recommend")
    public Object deleteRecommend(@FormUrlencoded SkuQuery skuQuery,
                                  Long skuId){
        try {
            Assert.notNull(skuId,"skuId must be non null");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopRecommendSkuBusiness.deleteBySku(skuId,skuQuery));
        } catch (Exception e) {
            logger.error("delete recommend error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_06);
        }
    }

    @RequestMapping("/setting_associative")
    public Object settingAssociative(@FormUrlencoded SkuQuery skuQuery,
                                     Long skuId, Long[] addedSkuIds){
        try {
            Assert.notNull(skuId,"SKU Id must be non null");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    shopGoodsSkuAssociativeBusiness.settingAssociative(skuId, Arrays.asList(addedSkuIds),skuQuery));
        } catch (Exception e) {
            logger.error("delete recommend error",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_07);
        }
    }

}
