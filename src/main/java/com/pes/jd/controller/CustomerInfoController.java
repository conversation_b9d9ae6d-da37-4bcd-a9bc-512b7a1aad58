package com.pes.jd.controller;

import com.pes.jd.business.sub.CustomerInfBusiness;
import com.pes.jd.model.DTO.CustomerInfoDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 2019-05-29 18:40
 */
@RestController
@RequestMapping("/customer")
public class CustomerInfoController {
    private static final Logger logger = LoggerFactory.getLogger(CustomerInfoController.class);

    @Autowired
    private CustomerInfBusiness customerInfBusiness;


    public CustomerInfoController() throws ParseException {
    }


    @RequestMapping("/getCustomerInfo")
    public ApiResponse getCustomerInfo(@RequestParam("shop") String shopStr,
                                       @RequestParam("customer") String customer) {
        ShopQuery shop = new ShopQuery();
        CustomerInfoDTO customerInfoDTO = null;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopQuery.class);
            customerInfoDTO = customerInfBusiness.selectCustomerInfoByShopIdAndOrderId(shop, customer);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, customerInfoDTO);
        } catch (Exception e) {
            logger.error("CustomerInfoController.getCustomerInfo error:{}", e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01, customerInfoDTO);
        }

    }

    @RequestMapping("/updateCustomerInfo")
    public ApiResponse updateCustomerInfo(@RequestParam("shop") String shopStr,
                                          @RequestParam("evaluate") String evaluate,
                                          @RequestParam("customerLabel") String customerLabel,
                                          @RequestParam("customer") String customer)   {
        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        ApiResponse apiResponse=new ApiResponse();
        ShopQuery shopQuery=new ShopQuery();
        try {
        	shopQuery = JacksonUtils.json2pojo(shopStr, ShopQuery.class);
        	customerInfoDTO.setCustomer(customer);
        	customerInfoDTO.setEvaluate(evaluate);
        	customerInfoDTO.setCustomerLabel(customerLabel);
        	customerInfoDTO.setUpdateTime(new Date());
            customerInfBusiness.updateCustomerInfo(shopQuery,customer,customerInfoDTO);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        }catch (Exception e){
            logger.error("CustomerInfoController.updateCustomerInfo error:{}"+e.getMessage(),e);
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_CJ_01_01.getMsg());
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_CJ_01_01.getCode());
        }
        return apiResponse;
    }


}
