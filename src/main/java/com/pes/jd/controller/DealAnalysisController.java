package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.DealAnalysisBussiness;
import com.pes.jd.framework.FormUrlencoded;
import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.DTO.RefundDataAnalysisDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.*;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据分析->成交分析
 */

@RequestMapping("/data/analysis")
@RestController
public class DealAnalysisController {
    private static final Logger logger = LoggerFactory.getLogger(DealAnalysisController.class);

    @Autowired
    private DealAnalysisBussiness dealAnalysisBussiness;

    /**
     * 店铺销售分析
     */
    @RequestMapping("/shopSaleAnalysis")
    public Object shopSaleAnalysis(@FormUrlencoded ShopCommonParam shopCommonParam, @FormUrlencoded ShopSaleParam shopSaleParam, @FormUrlencoded SortPageQuery sortPageQuery,
    		@FormUrlencoded OrderInfoLogUploadParam orderInfoLogUploadParamStr) {
        DataAnalysisVO<ShopSaleAnalysisVO> shopSaleAnalysis;
        try {
            shopSaleAnalysis = dealAnalysisBussiness.searchShopSaleAnalysisLstByPage(shopCommonParam, shopSaleParam, sortPageQuery,orderInfoLogUploadParamStr);
            //上传参数
//            UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParamStr);
//   		 	param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_SHOPSALEANALYSIS.getName());
//            uploadDBOperationBusiness.upload(param);
        } catch (Exception e) {
            logger.error("serach shopSaleAnalysis:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_15);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, shopSaleAnalysis);
    }


    /**
     * 客服销售分析
     */
    @RequestMapping("/csSaleAnalysis")
    public Object csSaleAnalysis(@FormUrlencoded ShopCommonParam shopCommonParam, @FormUrlencoded CsSaleParam csSaleParam, @FormUrlencoded SortPageQuery sortPageQuery,
    		@FormUrlencoded OrderInfoLogUploadParam orderInfoLogUploadParamStr) {
        DataAnalysisVO<CsSaleAnalysisVO> csSaleAnalysis;
        try {
            csSaleAnalysis = dealAnalysisBussiness.searchCsSaleAnalysisLstByPage(shopCommonParam, csSaleParam, sortPageQuery,orderInfoLogUploadParamStr);
            //上传参数
//            UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParamStr);
//   		 	param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_CSSALEANALYSIS.getName());
//            uploadDBOperationBusiness.upload(param);

        } catch (Exception e) {
            logger.error("serach csSaleAnalysis:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_16);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, csSaleAnalysis);
    }

    /**
     * 静默销售分析
     */
    @RequestMapping("/silenceSaleAnalysis")
    public Object silenceSaleAnalysis(@FormUrlencoded ShopCommonParam shopCommonParam, @FormUrlencoded SilenceSaleParam silenceSaleParam, @FormUrlencoded SortPageQuery sortPageQuery,
    		@FormUrlencoded OrderInfoLogUploadParam orderInfoLogUploadParamStr) {
        DataAnalysisVO<SilenceSaleAnalysisVO> dataAnalysisVO;
        try {
           dataAnalysisVO = dealAnalysisBussiness.searchSilenceSaleAnalysisLstByPage(shopCommonParam, silenceSaleParam, sortPageQuery,orderInfoLogUploadParamStr);

           //上传参数
//           UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParamStr);
//  		   param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_SILENCESALEANALYSIS.getName());
//           uploadDBOperationBusiness.upload(param);

        } catch (Exception e) {
            logger.error("serach silenceSaleAnalysis:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_17);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, dataAnalysisVO);
    }

    /**
     * 退款分析(含客服退款分析、静默退款分析
     */
    @RequestMapping("/selectRefundAnalysisLst")
    public ApiResponse selectRefundAnalysisLst(@RequestParam("shop") String shopStr, @RequestParam("param") String paramStr, @RequestParam("orderInfoLogUploadParamStr") String orderInfoLogUploadParamStr) {
        Map<String, Object> result = Maps.newHashMap();
        ShopQuery shop;
        RefundAnalysisParam param = null;
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopQuery.class);
            param = JacksonUtils.json2pojo(paramStr, RefundAnalysisParam.class);
            param = JacksonUtils.json2pojo(paramStr, RefundAnalysisParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
            List<RefundDataAnalysisDTO> refundLst = dealAnalysisBussiness.selectRefundAnalysisLst(shop, param,orderInfoLogUploadParam);
            result.put("refundLst", refundLst);

        } catch (Exception e) {
            result.put("result", new ArrayList<RefundDataAnalysisDTO>(0));
            logger.error("sub selectRefundAnalysisLst error:{}", e.getMessage(), e);
            if (param != null && CollectionUtils.isNotEmpty(param.getCsNickList()))
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01, result);
            else
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_02, result);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
    }

    /**
     * 退款分析(含客服退款分析、静默退款分析  SPU维度
     */
    @RequestMapping("/selectRefundAnalysisLstOfSpu")
    public ApiResponse selectRefundAnalysisLstOfSpu(@RequestParam("shop") String shopStr, @RequestParam("param") String paramStr, @RequestParam("orderInfoLogUploadParamStr") String orderInfoLogUploadParamStr) {
        Map<String, Object> result = Maps.newHashMap();
        ShopQuery shop;
        RefundAnalysisParam param = null;
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopQuery.class);
            param = JacksonUtils.json2pojo(paramStr, RefundAnalysisParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
//            List<RefundDataAnalysisDTO> refundLst1 = dealAnalysisBussiness.selectRefundAnalysisLst(shop, param,orderInfoLogUploadParam);
            List<RefundDataAnalysisDTO> refundLst = dealAnalysisBussiness.selectRefundAnalysisLstOfSpu(shop, param,orderInfoLogUploadParam);
            result.put("refundLst", refundLst);
        } catch (Exception e) {
            result.put("result", new ArrayList<RefundDataAnalysisDTO>(0));
            logger.error("sub selectRefundAnalysisLst error:{}", e.getMessage(), e);
            if (param != null && CollectionUtils.isNotEmpty(param.getCsNickList()))
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01, result);
            else
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_02, result);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
    }

    /**
     * 预售订单分析
     */
    @RequestMapping("/selectOrderPresaleLst")
    public ApiResponse selectOrderPresaleLst(
            @FormUrlencoded ShopCommonParam shopCommonParam,
            @FormUrlencoded OrderPresaleParam orderPresaleParam,
            @FormUrlencoded SortPageQuery sortPageQuery,
            @FormUrlencoded OrderInfoLogUploadParam orderInfoLogUploadParamStr) {
        DataAnalysisVO<OrderPresaleVO> dataAnalysisVO;
        try {

            dataAnalysisVO = dealAnalysisBussiness.selectOrderPresaleLst(shopCommonParam, orderPresaleParam, sortPageQuery,orderInfoLogUploadParamStr);
            if (dataAnalysisVO == null) dataAnalysisVO = new DataAnalysisVO<>();

            //上传参数
//            UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParamStr);
//   		    param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_ORDERPRESALELST.getName());
//            uploadDBOperationBusiness.upload(param);

        } catch (Exception e) {
            logger.error("serach selectOrderPresaleLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_16);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, dataAnalysisVO);
    }

    /**
     * 预售未付款
     */
    @RequestMapping("/selectOrderPresaleUnbalance")
    public ApiResponse selectOrderPresaleUnbalance(@FormUrlencoded ShopCommonParam shopCommonParam, @FormUrlencoded OrderPresaleUnbalanceParam presaleUnbalanceParam) {
        List<OrderPresaleUnbalanceVO> vos;
        try {
            vos = dealAnalysisBussiness.selectOrderPresaleUnbalance(shopCommonParam, presaleUnbalanceParam);
        } catch (Exception e) {
            logger.error("serach selectOrderPresaleUnbalance:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_16);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, vos);
    }


    /**
     * 预售订单分析
     */
    @RequestMapping("/selectOrderPredainLst")
    public Object selectOrderPresaleLst(
            @FormUrlencoded ShopCommonParam shopCommonParam,
            @FormUrlencoded OrderPreOrdainParam orderPreOrdainParam,
            @FormUrlencoded SortPageQuery sortPageQuery,
            @FormUrlencoded OrderInfoLogUploadParam orderInfoLogUploadParamStr) {
        DataAnalysisVO<OrderPreordainDTO> dataAnalysisVO;
        try {

            dataAnalysisVO = dealAnalysisBussiness.selectOrderPreOrdainLst(shopCommonParam, orderPreOrdainParam, sortPageQuery,orderInfoLogUploadParamStr);
            if (dataAnalysisVO == null) dataAnalysisVO = new DataAnalysisVO<>();

            return RestResponseTypeRef.ofSuccess(dataAnalysisVO);
        } catch (Exception e) {
            logger.error("serach selectOrderPredainLst:{}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }


}
