package com.pes.jd.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.sub.ShopGoodsBusiness;
import com.pes.jd.business.sub.ShopGoodsInfoBussiness;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCategoryTree;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.ShopGoodsResult;
import com.pes.jd.model.VO.GoodsGroupSkuVO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.ms.domain.Result.shopdata.ShopGoodsSkuResult;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/shop/goodinfo")
public class ShopGoodsInfoController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(ShopGoodsInfoController.class);

	@Autowired
	private ShopGoodsInfoBussiness shopGoodsInfoBussiness;

	@Autowired
	private ShopGoodsBusiness shopGoodsBusiness;


	@RequestMapping("/selectCategoryLst")
	public Object selectCategoryLst(@RequestParam("shopStr") String shopStr) {
		Map<String, Object> data = new HashMap<String, Object>();
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopCategoryTree> categoryLst = shopGoodsInfoBussiness.selectShopCategoryAndGoodsByShopId(shop);
			data.put("categoryLst", categoryLst);
		} catch (Exception e) {
			data.put("categoryLst", new ArrayList<ShopCategoryDTO>(0));
			logger.error("selectCategoryLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_05);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}

	@RequestMapping("/selectCategoryLstV2")
	public Object selectCategoryLstV2(@RequestParam("shopStr") String shopStr) {
		Map<String, Object> data = new HashMap<String, Object>();
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopCategoryTree> categoryLst = shopGoodsInfoBussiness.selectShopCategoryAndGoodsByShopIdV2(shop);
			data.put("categoryLst", categoryLst);
		} catch (Exception e) {
			data.put("categoryLst", new ArrayList<ShopCategoryDTO>(0));
			logger.error("selectCategoryLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_05);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}


	@RequestMapping("/selectGoodsinfoLst")
	public Object selectGoodsinfoLst(@RequestParam(name="shopId")String shopId,
			@RequestParam(name="categoryId")Long categoryId,
			@RequestParam(name="keyWord")String keyWord,
			@RequestParam(name="status")Integer status) {
		Map<String, Object> data = new HashMap<String, Object>();

		//UserShopQuery shop = getUserShopParam(shopId, null, null);
		ShopDTO shop = new ShopDTO();
		shop.setDb("DB_01");
		shop.setShopId(Long.valueOf("773035"));
		shop.setSchemaId("pes_jd_sub1");
		try {
			List<ShopGoodsDTO> goodInfoLst = shopGoodsInfoBussiness.selectShopGoodsByShopIdAndCategoryIdAndNameAndStatus(shop, categoryId, keyWord, status);
			data.put("goodInfoLst", goodInfoLst);
		} catch (Exception e) {
			logger.error("selectGoodsinfoLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_06);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}


	@RequestMapping("/selectGoodsSkuIdLst")
	public ApiResponse selectGoodsSkuIdLst(@RequestParam(name="shop")String shopStr,
										 @RequestParam(name="categoryId",required=false)String categoryId,
										 @RequestParam(name="status",required=false)String status,
										 @RequestParam(name="level",required=false)Long level){
		Map<String, Object> data = new HashMap<>();
		ShopCommonParam shop;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			data = shopGoodsInfoBussiness.selectShopGoodsSkuIdLstByCategoryIdByStatus(shop, categoryId, status,level);
		} catch (Exception e) {
			data.put("goodsSkuIdLst", new ArrayList<ShopGoodsSkuDTO>(0));
			logger.error("sub selectGoodsSkuIdLst:{}",e.getMessage(),e);

			StringWriter stringWriter = new StringWriter();
			PrintWriter printWriter = new PrintWriter(stringWriter);
			e.printStackTrace(printWriter);
			String stackTraceString = stringWriter.toString();
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01,stackTraceString);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}


	@RequestMapping("/selectGoodsSkuLst")
	public ApiResponse selectGoodsSkuLst(@RequestParam(name="shop")String shopStr,
			@RequestParam(name="categoryId",required=false)String categoryId,
			@RequestParam(name="level",required=false)Long level,
			@RequestParam(name="skuName",required=false)String skuName,
			@RequestParam(name="status",required=false)String status,
			@RequestParam(name="skuIds",required=false) String skuIds,
			@RequestParam(name="pageSize")Integer pageSize,
			@RequestParam(name="pageNum")Integer pageNum){
		Map<String, Object> data = new HashMap<>();
		ShopCommonParam shop;
		List<Long> skuIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			if(StringUtils.isNoneBlank(skuIds)){
				skuIdLst=JSON.parseArray(skuIds,Long.class);
			}
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			data = shopGoodsInfoBussiness.selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(shop, categoryId,level, skuName, status,skuIdLst,pageSize,pageNum);
		} catch (Exception e) {
			data.put("goodsSkuLst", new ArrayList<ShopGoodsSkuDTO>(0));
			logger.error("sub selectGoodsSkuLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}

	@RequestMapping("/selectGoodsSkuLstOfSpu")
	public ApiResponse selectGoodsSkuLstOfSpu(@RequestParam(name="shop")String shopStr,
			@RequestParam(name="categoryId",required=false)String categoryId,
			@RequestParam(name="level",required=false)Long level,
			@RequestParam(name="skuName",required=false)String skuName,
			@RequestParam(name="status",required=false)String status,
			@RequestParam(name="skuIds",required=false) String skuIds,
			@RequestParam(name="pageSize")Integer pageSize,
			@RequestParam(name="pageNum")Integer pageNum){
		Map<String, Object> data = new HashMap<>();
		ShopCommonParam shop;
		List<Long> skuIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			if(StringUtils.isNoneBlank(skuIds)){
				skuIdLst=JSON.parseArray(skuIds,Long.class);
			}
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			data = shopGoodsBusiness.selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(shop, categoryId,level, skuName, status,skuIdLst,pageSize,pageNum);
		} catch (Exception e) {
			data.put("goodsSkuLst", new ArrayList<ShopGoodsSkuDTO>(0));
			logger.error("sub selectGoodsSkuLstOfSpu:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}

	@RequestMapping("/selectGoodsSkuLstBySkuIdLst")
	public ApiResponse selectGoodsSkuLstBySkuIdLst(@RequestParam(name="shop")String shopStr,
			@RequestParam(name="skuIdLst")String skuIdLstStr){
		Map<String, Object> data = new HashMap<>();
		ShopCommonParam shop;
		List<Long> skuIdLst;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			skuIdLst=JSON.parseArray(skuIdLstStr,Long.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodsSkuDTO> selectedGoodsSkuLst = shopGoodsInfoBussiness.selectShopGoodsSkuLstBySkuIdLst(shop, skuIdLst);
			data.put("selectedGoodsSkuLst", selectedGoodsSkuLst);
		} catch (Exception e) {
			data.put("selectedGoodsSkuLst", new ArrayList<ShopGoodsSkuDTO>(0));
			logger.error("sub selectGoodsSkuLstBySkuIdLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}

	@RequestMapping("/selectGoodsSkuLstBySkuIdLstOfSpu")
	public ApiResponse selectGoodsSkuLstBySkuIdLstOfSpu(@RequestParam(name="shop")String shopStr,
			@RequestParam(name="wareIdLSt")String wareIdLSt){
		Map<String, Object> data = new HashMap<>();
		ShopCommonParam shop;
		List<Long> skuIdLst;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			skuIdLst=JSON.parseArray(wareIdLSt,Long.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
//			List<ShopGoodsSkuDTO> selectedGoodsSkuLst = shopGoodsInfoBussiness.selectShopGoodsSkuLstBySkuIdLst(shop, skuIdLst);
			List<ShopGoodsSkuDTO> selectedGoodsSkuLst = shopGoodsBusiness.selectShopGoodsSkuLstByWareIdsLst(shop, skuIdLst);
			data.put("selectedGoodsSkuLst", selectedGoodsSkuLst);
		} catch (Exception e) {
			data.put("selectedGoodsSkuLst", new ArrayList<ShopGoodsSkuDTO>(0));
			logger.error("sub selectGoodsSkuLstBySkuIdLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}



	@RequestMapping("/selectGoodsSkuLstByShopLstBySkuIdLst")
	public ApiResponse selectGoodsSkuLstByShopLstBySkuIdLst(@RequestParam(name="shopLst")String shopLstStr,
			@RequestParam(name="goodsGroupLst")String goodsGroupLstStr){
		Map<String, Object> data = new HashMap<>();
		List<ShopCommonParam> shopLst;
		List<GoodsGroupSkuVO> goodsGroupSkuLst;
		try {
			shopLst = JacksonUtils.json2list(shopLstStr, ShopCommonParam.class);
			goodsGroupSkuLst=JacksonUtils.json2list(goodsGroupLstStr, GoodsGroupSkuVO.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
            List<ShopGoodsSkuDTO> selectedGoodsSkuLst=new ArrayList<>();
            Map<Byte, List<GoodsGroupSkuVO>> collect = goodsGroupSkuLst.stream().collect(Collectors.groupingBy(GoodsGroupSkuVO::getDimension));
                List<GoodsGroupSkuVO> skuDim = collect.get(Byte.valueOf("1"));//sku维度
                List<GoodsGroupSkuVO> spuDim = collect.get(Byte.valueOf("2"));//spu维度
            if(CollectionUtils.isNotEmpty(skuDim)){
                List<ShopGoodsSkuDTO> skuLst = shopGoodsInfoBussiness.selectGoodsSkuLstByShopLstBySkuIdLst(shopLst, skuDim);
                if(CollectionUtils.isNotEmpty(skuLst)){
                    selectedGoodsSkuLst.addAll(skuLst);
                }
            }
            if(CollectionUtils.isNotEmpty(spuDim)){
                List<ShopGoodsSkuDTO> spuLst = shopGoodsInfoBussiness.selectGoodsSkuLstByShopLstBySkuIdLstOfSpu(shopLst, spuDim);
                if(CollectionUtils.isNotEmpty(spuLst)){
                    selectedGoodsSkuLst.addAll(spuLst);
                }
            }
			data.put("selectedGoodsSkuLst", selectedGoodsSkuLst);
		} catch (Exception e) {
			data.put("selectedGoodsSkuLst", new ArrayList<ShopGoodsSkuDTO>(0));
			logger.error("sub selectGoodsSkuLstByShopLstBySkuIdLst:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SP_01_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}


	@RequestMapping("/selectShopSkuByShopId")
	public Object selectShopSkuByShopId(@RequestParam(name="shop")String shopStr){
		ShopGoodsSkuResult su=null;
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodsSku> skuLst=shopGoodsInfoBussiness.selectShopSkuByShopId(shop);
			su=new ShopGoodsSkuResult(skuLst);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(su));
		} catch (Exception e) {
			logger.error("sub selectShopSkuByShopId:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SP_01_01,RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/selectShopGoodsByCategoryIdBySkuNameByStatus")
	public Object selectShopGoodsByCategoryIdBySkuNameByStatus(@RequestParam(name="shop")String shopStr,
															   @RequestParam(name="categoryId",required=false)String categoryId,
															   @RequestParam(name="level",required=false)Long level,
															   @RequestParam(name="wareName",required=false)String wareName,
															   @RequestParam(name="status",required=false)String status,
															   @RequestParam(name="wareIds",required=false) String wareIds,
															   @RequestParam(name="pageSize")Integer pageSize,
															   @RequestParam(name="pageNum")Integer pageNum){
		ShopCommonParam shop;
		List<Long> wareIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			if(StringUtils.isNoneBlank(wareIds)){
				wareIdLst=JSON.parseArray(wareIds,Long.class);
			}
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodNameDTO>  goodsLst=shopGoodsInfoBussiness.selectShopGoodsByCategoryIdBySkuNameByStatus(shop,categoryId,level,wareName,status,wareIdLst,pageSize,pageNum);
			return RestResponseTypeRef.ofSuccess(goodsLst);
		} catch (Exception e) {
			logger.error("sub selectShopGoodsByCategoryIdBySkuNameByStatus:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectShopGoodsByShopIdByWareIds")
	public Object selectShopGoodsByShopIdByWareIds(@RequestParam(name="shop")String shopStr,
															   @RequestParam(name="wareIds",required=false) String wareIds
															  ){
		ShopCommonParam shop=null;
		List<Long> wareIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			if(StringUtils.isNoneBlank(wareIds)){
				wareIdLst=JSON.parseArray(wareIds,Long.class);
			}
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodNameDTO>  goodsLst=shopGoodsInfoBussiness.selectShopGoodsByShopIdByWareIds(shop,wareIdLst);
			return RestResponseTypeRef.ofSuccess(goodsLst);
		} catch (Exception e) {
			logger.error("sub selectShopGoodsByShopIdByWareIds:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectShopGoodsName")
	public Object selectShopGoodsName(@RequestParam(name="shop")String shopStr,
									  @RequestParam(name="wareIds",required=false) String wareIds,
									  @RequestParam(name="skuIds",required=false) String skuIds
	){
		ShopCommonParam shop=null;
		List<Long> wareIdLst=null;
		List<Long> skuIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			if(StringUtils.isNoneBlank(wareIds)){
				wareIdLst=JSON.parseArray(wareIds,Long.class);
			}
			if(StringUtils.isNoneBlank(skuIds)){
				skuIdLst=JSON.parseArray(skuIds,Long.class);
			}
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			ShopGoodsResult result=shopGoodsInfoBussiness.selectShopGoodsName(shop,wareIdLst,skuIdLst);
			return RestResponseTypeRef.ofSuccess(result);
		} catch (Exception e) {
			logger.error("sub selectShopGoodsName:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectShopGoodsSkuByShopIdByWareIds")
	public Object selectShopGoodsSkuByShopIdByWareIds(@RequestParam(name="shop")String shopStr,
												   @RequestParam(name="wareIds",required=false) String wareIds
	){
		ShopCommonParam shop;
		List<Long> wareIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			if(StringUtils.isNoneBlank(wareIds)){
				wareIdLst=JSON.parseArray(wareIds,Long.class);
			}
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodsSku>  goodsLst=shopGoodsInfoBussiness.selectShopGoodsSkuByShopIdByWareIds(shop,wareIdLst);
			return RestResponseTypeRef.ofSuccess(goodsLst);
		} catch (Exception e) {
			logger.error("sub selectShopGoodsSkuByShopIdByWareIds:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectGoodsSkuByShopIdBySkuIdLst")
	public Object selectGoodsSkuByShopIdBySkuIdLst(@RequestParam(name="shop")String shopStr,
												   @RequestParam(name="skuIdLst")String skuIdLstStr){
		ShopCommonParam shop=null;
		List<Long> skuIdLst=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			skuIdLst=JSON.parseArray(skuIdLstStr,Long.class);
		} catch (Exception e1) {
			logger.error("shopJson parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodsSkuDTO> goodsSkuLst = shopGoodsInfoBussiness.selectShopGoodsSkuLstBySkuIdLst(shop, skuIdLst);
			return RestResponseTypeRef.ofSuccess(goodsSkuLst);
		} catch (Exception e) {
			logger.error("sub selectGoodsSkuByShopIdBySkuIdLst:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectShopSkuByShopIdAndSkuId")
	public Object selectShopSkuByShopIdAndSkuId(@RequestParam(name="shop")String shopStr,
			@RequestParam(name="skuIdList")String skuIdList
			){
		ShopGoodsSkuResult su=null;
		ShopCommonParam shop=null;
		List<Long> skuList = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			skuList= JSONObject.toJavaObject(JSONObject.parseArray(skuIdList), ArrayList.class);
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ShopGoodsSku> skuLst=shopGoodsInfoBussiness.selectShopSkuByShopIdAndSkuId(shop,skuList);


//			List<ShopGoodsSkuDTO> selectedGoodsSkuLst = shopGoodsInfoBussiness.selectShopGoodsSkuLstBySkuIdLst(shop, skuList);
//
//			List<ShopGoodsSku> skuLst= new ArrayList<ShopGoodsSku>();
//
//			for(ShopGoodsSkuDTO shopGoodsSkuDTO:selectedGoodsSkuLst) {
//				ShopGoodsSku shopGoodsSku = JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(shopGoodsSkuDTO)), ShopGoodsSku.class);
//				skuLst.add(shopGoodsSku);
//			}
//

			su=new ShopGoodsSkuResult(skuLst);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(su));
		} catch (Exception e) {
			logger.error("sub selectShopSkuByShopId:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SP_01_01,RestApiResponse2.of(false));
		}
	}


}
