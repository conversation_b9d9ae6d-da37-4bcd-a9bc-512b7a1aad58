package com.pes.jd.controller;

import com.pes.jd.business.sub.UserPortraitStatisticsBusiness;
import com.pes.jd.model.DTO.UserPortraitStatisticsDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.RegionDistributionResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.Map;

import static cn.hutool.core.date.DateUtil.parseDate;

@RestController
@RequestMapping("/userPortraitStatistics/")
public class UserPortraitStatisticsController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(UserPortraitStatisticsController.class);

    @Autowired
    private UserPortraitStatisticsBusiness userPortraitStatisticsBusiness;


    @PostMapping("queryPortraitStatisticsByDate")
    public ApiResponse queryPortraitStatisticsByDate(@RequestBody Map<String, Object> requestMap) {
        try {

            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            UserPortraitStatisticsDTO userPortraitStatisticsDTO =  userPortraitStatisticsBusiness.getPortraitStatisticsByDate(shop, startDate, endDate);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, userPortraitStatisticsDTO);
        } catch (Exception e) {
            logger.error("Error querying chat classification statistics by shop and date range", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }


    @PostMapping("queryRegionDistributionByDate")
    public ApiResponse queryRegionDistributionByDate(@RequestBody Map<String, Object> requestMap) {
        try {
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            RegionDistributionResult regionDistributionByDate = userPortraitStatisticsBusiness.getRegionDistributionByDate(shop, startDate, endDate);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, regionDistributionByDate);
        } catch (Exception e) {
            logger.error("Error querying chat classification statistics by shop and date range", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }


}
