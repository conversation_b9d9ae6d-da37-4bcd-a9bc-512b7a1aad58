package com.pes.jd.controller;

import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.PopSubRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/userPortraitStatistics/")
public class UserPortraitStatisticsController extends  BaseController{

    private static final Logger logger = LoggerFactory.getLogger(UserPortraitStatisticsController.class);

    @Autowired
    private PopSubRestTemplate popSubRestTemplate;

    @PostMapping("queryPortraitStatisticsByDate")
    public ApiResponse queryStatsByDateRange(@RequestParam String startDate, @RequestParam String endDate) {

        try {
            ShopDTO shop = this.getCurrentShop();
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("shopId", shop.getShopId());
            requestMap.put("schemaId", shop.getSchemaId());
            requestMap.put("dbName", shop.getDb());
            requestMap.put("startDate", startDate);
            requestMap.put("endDate", endDate);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);

            String serviceId = RestOperator.getMSServiceId(shop.getDb(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            RestResponseTypeRef<Object> response = popSubRestTemplate.postRest(
                    serviceId,
                    "/userPortraitStatistics/queryPortraitStatisticsByDate",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if (response != null && ApiCodeEnum.CODE_SUCCESS_1001.getCode().equals(response.getRpCode())) {
                Object data = response.getData();
                return ApiResponse.ofSuccess(data);
            } else {
                return ApiResponse.of(response.getRpCode(), response.getRpMsg());
            }
        } catch (Exception e) {
            logger.error("Error proxying low-rated evaluations with classify", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }



    }


    @PostMapping("queryRegionDistributionByDate")
    public ApiResponse queryRegionDistributionByDate(@RequestParam String startDate, @RequestParam String endDate) {
        try {
            ShopDTO shop = this.getCurrentShop();
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("shopId", shop.getShopId());
            requestMap.put("schemaId", shop.getSchemaId());
            requestMap.put("dbName", shop.getDb());
            requestMap.put("startDate", startDate);
            requestMap.put("endDate", endDate);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> param = new HttpEntity<>(requestMap, headers);

            String serviceId = RestOperator.getMSServiceId(shop.getDb(), ApplicationServiceNameEnum.PROVIDER_SUB.getName());
            RestResponseTypeRef<Object> response = popSubRestTemplate.postRest(
                    serviceId,
                    "/userPortraitStatistics/queryRegionDistributionByDate",
                    param,
                    new ParameterizedTypeReference<RestResponseTypeRef<Object>>() {});

            if (response != null && ApiCodeEnum.CODE_SUCCESS_1001.getCode().equals(response.getRpCode())) {
                Object data = response.getData();
                return ApiResponse.ofSuccess(data);
            } else {
                return ApiResponse.of(response.getRpCode(), response.getRpMsg());
            }
        } catch (Exception e) {
            logger.error("Error proxying low-rated evaluations with classify", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }

}
