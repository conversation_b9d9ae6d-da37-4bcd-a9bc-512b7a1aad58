package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.pes.jd.business.main.MarketingActivityBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.MarketingActivityDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 13:38 2019/10/29
 * @Description:
 */
@RestController
@RequestMapping("/marketingActivity/")
public class MarketingActivityController extends BaseController {

    @Autowired
    private MarketingActivityBusiness marketingActivityBusiness;

    @RequestMapping("insert")
    public Object insert(String activityName, String activityContent,
                         String startDate, String endDate,
                         String version, String orderType,
                         @RequestParam(name = "shopType", required = false) Integer shopType,
                         @RequestParam(name = "url", required = false) String url) {
        try {
            if (null == shopType) {
                shopType = Integer.valueOf(CommonConstants.SHOP_TYPE_POP_STR);
            }
            Date sDate;
            Date eDate;
            try {
                sDate = DateUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");
                eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_SF_03_20, RestApiResponse2.of(false));
            }

            marketingActivityBusiness.insert(activityName, activityContent, sDate,
                    eDate, version, orderType, shopType, url);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("delete")
    public Object delete(Long id) {
        try {
            marketingActivityBusiness.deleteByPrimaryKey(id);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("update")
    public Object update(Long id, String activityName, String activityContent,
                         String startDate, String endDate,
                         String version, String orderType, String url) {
        try {
            Date sDate;
            Date eDate;
            try {
                sDate = DateUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");
                eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return RestResponseTypeRef.ofFail();
            }
            marketingActivityBusiness.update(id, activityName, activityContent, sDate,
                    eDate, version, orderType, url);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("updateEnable")
    public Object updateEnable(Long id, Boolean enableSwitch) {
        try {
            marketingActivityBusiness.updateEnable(id, enableSwitch);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("selectEnableActivity")
    public Object selectEnableActivity(@RequestParam(name = "shopType", required = false) Integer shopType) {
        try {
            if (null == shopType) {
                shopType = Integer.valueOf(CommonConstants.SHOP_TYPE_POP_STR);
            }
            List<MarketingActivityDTO> marketingActivityDTOS = marketingActivityBusiness.selectEnableActivity(shopType);
            return RestResponseTypeRef.ofSuccess(marketingActivityDTOS);
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("selectActivityByActivityNameAndDate")
    public Object selectActivityByActivityNameAndDate(@RequestParam(name = "startDate", required = true) String startDate,
                                                      @RequestParam(name = "endDate", required = true) String endDate,
                                                      @RequestParam(name = "activityName", required = true) String activityName,
                                                      @RequestParam(name = "shopType", required = false) Integer shopType) {
        try {
            Date sDate;
            Date eDate;
            try {
                if (null == shopType) {
                    shopType = Integer.valueOf(CommonConstants.SHOP_TYPE_POP_STR);
                }
                sDate = DateUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");
                eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_SF_03_20, RestApiResponse2.of(false));
            }
            activityName = StrUtil.isNotBlank(activityName) ? activityName.trim() : activityName;
            List<MarketingActivityDTO> marketingActivityDTOS = marketingActivityBusiness.selectActivityByActivityNameAndDate(sDate, eDate, activityName, shopType);
            return RestResponseTypeRef.ofSuccess(marketingActivityDTOS);
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }
}
