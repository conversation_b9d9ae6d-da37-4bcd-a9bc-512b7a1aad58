package com.pes.jd.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pes.jd.business.SgDiagnosticAnalysisBusiness;
import com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO;
import com.pes.jd.model.DO.ShopSmsSettingDO;
import com.pes.jd.model.DTO.JoyiShopRemindSettingDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Response.RestResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 诊断分析控制器
 */
@RestController
@RequestMapping("/diagnosticAnalysis")
public class SgDiagnosticAnalysisController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(SgDiagnosticAnalysisController.class);

    @Resource
    private SgDiagnosticAnalysisBusiness sgDiagnosticAnalysisBusiness;

    /**
     * 同步系统设置
     * @param shopIds
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/synSetting")
    public Object synSetting(@RequestParam("shopIds") String shopIds,
                             @RequestParam(value = "startDate",required = false)String startDate,
                             @RequestParam(value = "endDate",required = false)String endDate) {
        try {
            logger.info("into synSetting shopIds={},startDate={},endDate={}", shopIds, startDate, endDate);

            Assert.notNull(shopIds);
//            if (CollectionUtils.isNotEmpty(Arrays.asList(shopIds.split(",")))) {
//                shopIdLst.addAll(Arrays.asList(shopIds.split(",")));
//            }
//       校验时间区间不能大于一天
//            checkDateScope(startDate, endDate);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }

        try {
            Object result = sgDiagnosticAnalysisBusiness.synSetting(shopIds, startDate, endDate);
            return RestResponseTypeRef.ofSuccess(result);
        } catch (Exception e) {
            logger.error("synSetting get fail {}",e.getMessage(),e);
            return RestResponseTypeRef.ofFail();
        }
    }
    /**
     * 同步`话术
     * @param shopIds
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/synWord")
    public Object synWord(@RequestParam("shopIds") String shopIds,
                             @RequestParam(value = "startDate",required = false)String startDate,
                             @RequestParam(value = "endDate",required = false)String endDate) {
        try {
            logger.info("into synWord shopIds={},startDate={},endDate={}", shopIds, startDate, endDate);

            Assert.notNull(shopIds);
//            if (CollectionUtils.isNotEmpty(Arrays.asList(shopIds.split(",")))) {
//                shopIdLst.addAll(Arrays.asList(shopIds.split(",")));
//            }
//       校验时间区间不能大于一天
//            checkDateScope(startDate, endDate);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }

        try {
            return RestResponseTypeRef.ofSuccess(sgDiagnosticAnalysisBusiness.synWord(shopIds, startDate,endDate));
        } catch (Exception e) {
            logger.error("synWord get fail {}",e.getMessage(),e);
            return RestResponseTypeRef.ofFail();
        }
    }


    /**
     * 同步系统设置
     * @param shopIds
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/synSetting2")
    public Object synSetting2(@RequestParam("shopIds") String shopIds,
                             @RequestParam(value = "startDate",required = false)String startDate,
                             @RequestParam(value = "endDate",required = false)String endDate) {
        try {
            logger.info("into synSetting shopIds={},startDate={},endDate={}", shopIds, startDate, endDate);

            Assert.notNull(shopIds);
//            if (CollectionUtils.isNotEmpty(Arrays.asList(shopIds.split(",")))) {
//                shopIdLst.addAll(Arrays.asList(shopIds.split(",")));
//            }
//       校验时间区间不能大于一天
//            checkDateScope(startDate, endDate);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }

        try {
            Object result = sgDiagnosticAnalysisBusiness.synSetting2(shopIds, startDate, endDate);
            return RestResponseTypeRef.ofSuccess(result);
        } catch (Exception e) {
            logger.error("synSetting get fail {}",e.getMessage(),e);
            return RestResponseTypeRef.ofFail();
        }
    }
    /**
     * 同步`话术
     * @param shopIds
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping(value = "/synWord2")
    public Object synWord2(@RequestParam("shopIds") String shopIds,
                             @RequestParam(value = "startDate",required = false)String startDate,
                             @RequestParam(value = "endDate",required = false)String endDate) {
        try {
            logger.info("into synWord shopIds={},startDate={},endDate={}", shopIds, startDate, endDate);

            Assert.notNull(shopIds);
//            if (CollectionUtils.isNotEmpty(Arrays.asList(shopIds.split(",")))) {
//                shopIdLst.addAll(Arrays.asList(shopIds.split(",")));
//            }
//       校验时间区间不能大于一天
//            checkDateScope(startDate, endDate);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }

        try {
            return RestResponseTypeRef.ofSuccess(sgDiagnosticAnalysisBusiness.synWord2(shopIds, startDate,endDate));
        } catch (Exception e) {
            logger.error("synWord get fail {}",e.getMessage(),e);
            return RestResponseTypeRef.ofFail();
        }
    }

    private void checkDateScope(@RequestParam("startDate") Date startDate, @RequestParam("endDate") Date endDate) {
        long betweenDay = DateUtil.between(startDate, endDate, DateUnit.DAY);
        if (betweenDay > 1) {
            throw new RuntimeException("时间跨度不要大于一天");
        }
    }

    public static void main(String[] args) {

//        testResult();
//        testSetting();
        testsetting1();  //查看设置

//        JoyiShopRemindSettingDTO joyiShopRemindSettingDTO = JSONObject.parseObject("{\"data\":{\"batchRemindCnoSettingLst\":[{\"cnoTime\":0,\"cnoWordId\":\"\",\"created\":1583049002856,\"id\":0,\"isRemind\":false,\"modified\":1583049002856,\"remindEndDot\":0,\"remindStartDot\":0,\"shopId\":0}],\"batchRemindSettingLst\":[{\"balanceRemindDot\":0,\"balanceRemindWordCount\":\"\",\"balanceRemindWordId\":\"\",\"bargainRemindDot\":0,\"bargainRemindWordCount\":\"\",\"bargainRemindWordId\":\"\",\"created\":1583049002841,\"historyDataFlag\":false,\"id\":0,\"isRemind\":false,\"remindEndDot\":0,\"remindStartDot\":0,\"remindWordLst\":[],\"shopId\":0,\"silentCsNick\":\"\",\"silentFlag\":0,\"silentGroupId\":0,\"silentSpareCsNick\":\"\",\"silentSpareGroupId\":0,\"unpcTime\":0,\"unpcWordCount\":\"\",\"unpcWordId\":\"\"}],\"smsSettingLst\":[{\"created\":1583049002866,\"id\":0,\"isRemind\":false,\"modified\":1583049002866,\"remindEndDot\":0,\"remindStartDot\":0,\"remindTime\":0,\"shopId\":0,\"slinetWordId\":0,\"unpcWordId\":0}]},\"rpCode\":\"\",\"rpMsg\":\"\",\"success\":true}\n", JoyiShopRemindSettingDTO.class);
//        System.out.println(joyiShopRemindSettingDTO);
//        String str="{\"data\":{\"batchRemindCnoSettingLst\":[{\"cnoTime\":0,\"cnoWordId\":\"\",\"created\":1583049002856,\"id\":0,\"isRemind\":false,\"modified\":1583049002856,\"remindEndDot\":0,\"remindStartDot\":0,\"shopId\":0}],\"batchRemindSettingLst\":[{\"balanceRemindDot\":0,\"balanceRemindWordCount\":\"\",\"balanceRemindWordId\":\"\",\"bargainRemindDot\":0,\"bargainRemindWordCount\":\"\",\"bargainRemindWordId\":\"\",\"created\":1583049002841,\"historyDataFlag\":false,\"id\":0,\"isRemind\":false,\"remindEndDot\":0,\"remindStartDot\":0,\"remindWordLst\":[],\"shopId\":0,\"silentCsNick\":\"\",\"silentFlag\":0,\"silentGroupId\":0,\"silentSpareCsNick\":\"\",\"silentSpareGroupId\":0,\"unpcTime\":0,\"unpcWordCount\":\"\",\"unpcWordId\":\"\"}],\"smsSettingLst\":[{\"created\":1583049002866,\"id\":0,\"isRemind\":false,\"modified\":1583049002866,\"remindEndDot\":0,\"remindStartDot\":0,\"remindTime\":0,\"shopId\":0,\"slinetWordId\":0,\"unpcWordId\":0}]},\"rpCode\":\"\",\"rpMsg\":\"\",\"success\":true}\n";
////JSONObject.parseja(str,RestResponseTypeRef.class)
////        getobject(str);
//
//        RestResponseTypeRef restResponseTypeRef = JSONObject.parseObject(str, RestResponseTypeRef.class);
//        if(restResponseTypeRef!=null&&restResponseTypeRef.getSuccess() ){
//            JoyiShopRemindSettingDTO joyiShopRemindSettingDTO = JSONObject.parseObject(JSONObject.toJSONString(restResponseTypeRef.getData()), JoyiShopRemindSettingDTO.class);
//            System.out.println(joyiShopRemindSettingDTO);
//        }

//        Object o = getobjectT(str, JoyiShopRemindSettingDTO.class);

//        parseJoyiRsult(str)

//        JoyiShopRemindSettingDTO joyiShopRemindSettingDTO = JSONObject.parseObject(JSONObject.toJSONString(data), JoyiShopRemindSettingDTO.class);
//        System.out.println(joyiShopRemindSettingDTO);
//        JoyiShopRemindSettingDTO data = (JoyiShopRemindSettingDTO)restResponseTypeRef.getData();
//        System.out.println(data);

//        Object parse = JSONObject.parse(str);
//        JSONObject.toJavaObject(JSONObject.toJSON(str), JoyiShopRemindSettingDTO.class);
//        System.out.println(parse);
//        Object parse = JSONObject.parse("{\"data\":{\"batchRemindCnoSettingLst\":[{\"cnoTime\":0,\"cnoWordId\":\"\",\"created\":1583049002856,\"id\":0,\"isRemind\":false,\"modified\":1583049002856,\"remindEndDot\":0,\"remindStartDot\":0,\"shopId\":0}],\"batchRemindSettingLst\":[{\"balanceRemindDot\":0,\"balanceRemindWordCount\":\"\",\"balanceRemindWordId\":\"\",\"bargainRemindDot\":0,\"bargainRemindWordCount\":\"\",\"bargainRemindWordId\":\"\",\"created\":1583049002841,\"historyDataFlag\":false,\"id\":0,\"isRemind\":false,\"remindEndDot\":0,\"remindStartDot\":0,\"remindWordLst\":[],\"shopId\":0,\"silentCsNick\":\"\",\"silentFlag\":0,\"silentGroupId\":0,\"silentSpareCsNick\":\"\",\"silentSpareGroupId\":0,\"unpcTime\":0,\"unpcWordCount\":\"\",\"unpcWordId\":\"\"}],\"smsSettingLst\":[{\"created\":1583049002866,\"id\":0,\"isRemind\":false,\"modified\":1583049002866,\"remindEndDot\":0,\"remindStartDot\":0,\"remindTime\":0,\"shopId\":0,\"slinetWordId\":0,\"unpcWordId\":0}]},\"rpCode\":\"\",\"rpMsg\":\"\",\"success\":true}\n");
//        System.out.println(parse);
    }

    private static void testsetting1() {
        String str="{\n" +
                "  \"success\": true,\n" +
                "  \"rpCode\": \"\",\n" +
                "  \"rpMsg\": \"\",\n" +
                "  \"data\": {\n" +
                "    \"batchRemindSettingLst\": [\n" +
                "      {\n" +
                "        \"id\": 12,\n" +
                "        \"shopId\": 81477,\n" +
                "        \"isRemind\": false,\n" +
                "        \"silentCsNick\": \"vgame武极-人参果\",\n" +
                "        \"unpcTime\": 15,\n" +
                "        \"unpcWordId\": \"1045\",\n" +
                "        \"bargainRemindDot\": 5,\n" +
                "        \"bargainRemindWordId\": \"0\",\n" +
                "        \"balanceRemindDot\": 30,\n" +
                "        \"balanceRemindWordId\": \"0\",\n" +
                "        \"remindStartDot\": 480,\n" +
                "        \"remindEndDot\": 1260,\n" +
                "        \"unpcWordCount\": null,\n" +
                "        \"bargainRemindWordCount\": null,\n" +
                "        \"balanceRemindWordCount\": null,\n" +
                "        \"silentFlag\": 2,\n" +
                "        \"silentGroupId\": 0,\n" +
                "        \"silentSpareGroupId\": 0,\n" +
                "        \"silentSpareCsNick\": \"\",\n" +
                "        \"remindWordLst\": null,\n" +
                "        \"historyDataFlag\": null,\n" +
                "        \"created\": null,\n" +
                "        \"modified\": null\n" +
                "      }\n" +
                "    ],\n" +
                "    \"batchRemindCnoSettingLst\": [\n" +
                "      {\n" +
                "        \"id\": 306,\n" +
                "        \"shopId\": 81477,\n" +
                "        \"isRemind\": true,\n" +
                "        \"cnoTime\": 30,\n" +
                "        \"cnoWordId\": \"8\",\n" +
                "        \"remindStartDot\": 540,\n" +
                "        \"remindEndDot\": 1260,\n" +
                "        \"created\": 1574068604000,\n" +
                "        \"modified\": 1582881948000\n" +
                "      }\n" +
                "    ],\n" +
                "    \"smsSettingLst\": [\n" +
                "      {\n" +
                "        \"id\": 8,\n" +
                "        \"shopId\": 81477,\n" +
                "        \"isRemind\": true,\n" +
                "        \"remindTime\": 5,\n" +
                "        \"unpcWordId\": 99,\n" +
                "        \"slinetWordId\": 114,\n" +
                "        \"remindStartDot\": 540,\n" +
                "        \"remindEndDot\": 1260,\n" +
                "        \"created\": 1582874381000,\n" +
                "        \"modified\": 1583115923000\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        RestResponseTypeRef restResponseTypeRef = JSONObject.parseObject(str, RestResponseTypeRef.class);
        if (restResponseTypeRef != null && restResponseTypeRef.getSuccess()) {
            JoyiShopRemindSettingDTO joyiShopRemindSettingDTO = JSONObject.parseObject(JSONObject.toJSONString(restResponseTypeRef.getData()), JoyiShopRemindSettingDTO.class);
            System.out.println(joyiShopRemindSettingDTO );
        }
    }

    private static void testSetting() {
            String str ="{\n" +
                    "    \"success\":true,\n" +
                    "    \"rpCode\": \"1001\",\n" +
                    "    \"rpMsg\": \"查询成功\",\n" +
                    "    \n" +
                    "    \"token\": \"\",\n" +
                    "\n" +
                    "\"data\": {\n" +
                    "        \"result\": [\n" +
                    "            {\n" +
                    "               \"shopId\": \"625260\",\n" +
                    "        \"date\": 1581955200000,\n" +
                    "        \"allTaskNum\": 0,\n" +
                    "        \"allotValidTaskNum\": 4,\n" +
                    "        \"executeTaskNum\": 0,\n" +
                    "        \"ddExecuteTaskNum\": 0,\n" +
                    "        \"ddValidTaskNum\": 4,\n" +
                    "        \"smsSuccessTaskNum\": 0,\n" +
                    "        \"smsSendTaskNum\": 0,\n" +
                    "        \"oneRemindClinchAmount\": 0.0,\n" +
                    "        \"autoRemindClinchAmount\": 0.0,\n" +
                    "        \"allUrgeClinchAmount\": 0.0,\n" +
                    "        \"taskType\": \"1\",\n" +
                    "        \"orderType\": \"1\",\n" +
                    "        \"unExecuteTaskNum\": 4,\n" +
                    "        \"unAllotTaskNum\": 0\n" +
                    "            }\n" +
                    "        ]\n" +
                    "    }\n" +
                    "}";
            Object parse = JSONObject.parse(str);
        RestResponse2 restResponse2 = JSONObject.parseObject(str, RestResponse2.class);
        Object result = restResponse2.getData().get("result");
        List<ShopUseAnalysisDetailDTO> shopUseAnalysisDetailDTOS = JSONObject.parseArray(JSONObject.toJSONString(result), ShopUseAnalysisDetailDTO.class);


        System.out.println(parse);
        }

//    private static T  getobjectT(String str, Class<T> clazz) {
//        RestResponseTypeRef restResponseTypeRef = JSONObject.parseObject(str, RestResponseTypeRef.class);
//        if (restResponseTypeRef != null && restResponseTypeRef.getSuccess()) {
//            Object o = JSONObject.parseObject(JSONObject.toJSONString(restResponseTypeRef.getData()), clazz);
//
//            return JSONObject.parseObject(JSONObject.toJSONString(restResponseTypeRef.getData()), clazz);
//        } else {
//            return null;
//        }
//
//
//    }

    private JoyiShopRemindSettingDTO  getobject(String str) {
        RestResponseTypeRef restResponseTypeRef = JSONObject.parseObject(str, RestResponseTypeRef.class);

        JoyiShopRemindSettingDTO joyiShopRemindSettingDTO = JSONObject.parseObject(JSONObject.toJSONString(restResponseTypeRef.getData()), JoyiShopRemindSettingDTO.class);
        return joyiShopRemindSettingDTO;
    }

    public static JoyiShopRemindSettingDTO parseJoyiRsult( String str ,T t) {
        RestResponseTypeRef result = JSONObject.parseObject(str, RestResponseTypeRef.class);

        if (result.getSuccess()) {

            return JSONObject.parseObject(JSONObject.toJSONString(result.getData()), JoyiShopRemindSettingDTO.class);
        } else {
            return null;
        }

    }


    private static void testResult() {
        JoyiShopRemindSettingDTO joyiShopRemindSettingDTO = new JoyiShopRemindSettingDTO();
        ShopSettingBatchRemindDTO shopSettingBatchRemindDTO = new ShopSettingBatchRemindDTO();
        ArrayList<ShopSettingBatchRemindDTO> batchRemindSettingLst = Lists.newArrayList();
        batchRemindSettingLst.add(shopSettingBatchRemindDTO);
        shopSettingBatchRemindDTO.setCreated(new Date());
        shopSettingBatchRemindDTO.setId(0L);
        shopSettingBatchRemindDTO.setShopId(0L);
        shopSettingBatchRemindDTO.setIsRemind(false);
        shopSettingBatchRemindDTO.setSilentCsNick("");
        shopSettingBatchRemindDTO.setUnpcTime(0);
        shopSettingBatchRemindDTO.setBargainRemindDot(0);
        shopSettingBatchRemindDTO.setBalanceRemindDot(0);
        shopSettingBatchRemindDTO.setRemindStartDot(0);
        shopSettingBatchRemindDTO.setRemindEndDot(0);
        shopSettingBatchRemindDTO.setUnpcWordCount("");
        shopSettingBatchRemindDTO.setBargainRemindWordCount("");
        shopSettingBatchRemindDTO.setBalanceRemindWordCount("");
        shopSettingBatchRemindDTO.setSilentFlag(0);
        shopSettingBatchRemindDTO.setSilentGroupId(0L);
        shopSettingBatchRemindDTO.setSilentSpareGroupId(0L);
        shopSettingBatchRemindDTO.setSilentSpareCsNick("");
        shopSettingBatchRemindDTO.setUnpcWordId("");
        shopSettingBatchRemindDTO.setBargainRemindWordId("");
        shopSettingBatchRemindDTO.setBalanceRemindWordId("");
        shopSettingBatchRemindDTO.setRemindWordLst(Lists.newArrayList());
        shopSettingBatchRemindDTO.setHistoryDataFlag(false);

        joyiShopRemindSettingDTO.setBatchRemindSettingLst(batchRemindSettingLst);

        ArrayList<ShopSettingBatchRemindCnoDO> batchRemindCnoSettingLst = Lists.newArrayList();
        ShopSettingBatchRemindCnoDO shopSettingBatchRemindCnoDO = new ShopSettingBatchRemindCnoDO();
        shopSettingBatchRemindCnoDO.setId(0L);
        shopSettingBatchRemindCnoDO.setShopId(0L);
        shopSettingBatchRemindCnoDO.setIsRemind(false);
        shopSettingBatchRemindCnoDO.setCnoTime(0);
        shopSettingBatchRemindCnoDO.setCnoWordId("");
        shopSettingBatchRemindCnoDO.setRemindStartDot(0);
        shopSettingBatchRemindCnoDO.setRemindEndDot(0);
        shopSettingBatchRemindCnoDO.setCreated(new Date());
        shopSettingBatchRemindCnoDO.setModified(new Date());
        batchRemindCnoSettingLst.add(shopSettingBatchRemindCnoDO);

        joyiShopRemindSettingDTO.setBatchRemindCnoSettingLst(batchRemindCnoSettingLst);
        ArrayList<ShopSmsSettingDO> smsSettingLst = Lists.newArrayList();
        ShopSmsSettingDO shopSmsSettingDO = new ShopSmsSettingDO();
        shopSmsSettingDO.setId(0L);
        shopSmsSettingDO.setShopId(0L);
        shopSmsSettingDO.setIsRemind(false);
        shopSmsSettingDO.setRemindTime(0);
        shopSmsSettingDO.setUnpcWordId(0L);
        shopSmsSettingDO.setSlinetWordId(0L);
        shopSmsSettingDO.setRemindStartDot(0);
        shopSmsSettingDO.setRemindEndDot(0);
        shopSmsSettingDO.setCreated(new Date());
        shopSmsSettingDO.setModified(new Date());
        smsSettingLst.add(shopSmsSettingDO);
        joyiShopRemindSettingDTO.setSmsSettingLst(smsSettingLst);

        System.out.println(JSONObject.toJSONString(RestResponseTypeRef.ofSuccess(joyiShopRemindSettingDTO)));
    }

//    public static void main(String[] args) throws UnsupportedEncodingException {
////		String state="eyJqb3NfcGFyYW1ldGVycyI6eyJ1c2VyX25hbWUiOiJhdXjlrpjml5ct5Y 254K554K5IiwiZW5kX2RhdGUiOjE1NzM0MzcyMDUwMDAsIml0ZW1fY29kZSI6IkZXX0dPT0RTLTkwODYyMi0yIiwiYXJ0aWNsZV9udW0iOjEsInZlcnNpb25fbm8iOjIsImFwcF9rZXkiOiIzMjFDRUFCMDAxRjU5RkREQTY3REMzODhDMDU3NTk1OCJ9fQ==";
////		//System.out.println(new String(Base64.getDecoder().decode(state), "UTF-8"));
////
////		Pattern p = Pattern.compile("\\s");
////        Matcher m = p.matcher(state);
////        if(m.find()){
////        	 String strNoBlank = m.replaceAll("+");
////             System.out.println(strNoBlank);
////             System.out.println(new String(Base64.getDecoder().decode(strNoBlank), "UTF-8"));
////        }
//        try {
//            System.out.println(ApiClientUtil.getSessionKeyByRefreshToken("381b7a0b8cbb4d6fadcd50f333aeba2chmgi"));
//        } catch (HttpReqException | JacksonParseException e) {
//            e.printStackTrace();
//        }
//
//
//    }

}
