package com.pes.jd.controller;

import com.pes.jd.business.main.AddressLibraryBusiness;
import com.pes.jd.data.converter.JingDongAddressLibraryConverter;
import com.pes.jd.model.DO.AddressLiabryDO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2019-06-01 22:17
 */
@RestController
@RequestMapping(value = "/address")
public class JingDongAddressLibraryController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(JingDongAddressLibraryController.class);
    @Autowired
    private JingDongAddressLibraryConverter jingDongAddressLibraryConverter;
    @Autowired
    private AddressLibraryBusiness addressLibraryBusiness;
    @RequestMapping("/updateJingDongAddressLiabry")
    public ApiResponse InsertJingDongAddressLibray(@RequestParam("shopId") Long shopId) {
        ShopDTO shopDTO = getSelectedShop(shopId);
        ApiResponse apiResponse = new ApiResponse();
        try {
            jingDongAddressLibraryConverter.getFullAddressAreaInfo( shopDTO.getSessionKey());
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
        } catch (Exception e) {
            logger.error("JingDongAddressLibraryController.InsertJingDongAddressLibray error:{}"+e.getMessage(),e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
        }
        return apiResponse;
    }
    
    @RequestMapping("/GetAddressLiabryInfo")
    public ApiResponse GetAddressLiabryInfo() {
        ApiResponse apiResponse = new ApiResponse();
        try {
        	Map<String,Object> resMap = new HashMap<>();
        	resMap.put("list", addressLibraryBusiness.getfullAddressList());
        	apiResponse.setData(resMap);   
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
        } catch (Exception e) {
        	logger.error("JingDongAddressLibraryController.GetAddressLiabryInfo error:{}"+e.getMessage(),e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
        }
        return apiResponse;
    }
    
    @RequestMapping("/getAddressByParam")
    public ApiResponse getAddressByParam(@RequestParam("level")Integer level,
    		@RequestParam(name="cityId",required=false) String cityId,
    		@RequestParam(name="provinceId",required=false) String provinceId,
    		@RequestParam(name="countryId",required=false) String countryId
    		) {
        ApiResponse apiResponse = new ApiResponse();
        try {
        	Map<String,Object> resMap = new HashMap<>();
        	AddressLiabryDO addressLiabryDO= new AddressLiabryDO();
        	addressLiabryDO.setLevel(new Byte(level.intValue()+""));
        	addressLiabryDO.setCityId(cityId);
        	addressLiabryDO.setProvinceId(provinceId);
        	addressLiabryDO.setCountryId(countryId);
        	
        	resMap.put("list", addressLibraryBusiness.getAddressByParam(addressLiabryDO));
        	apiResponse.setData(resMap);   
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
        } catch (Exception e) {
        	logger.error("JingDongAddressLibraryController.getAddressByParam error:{}"+e.getMessage(),e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
        }
        return apiResponse;
    }
    
    
}
