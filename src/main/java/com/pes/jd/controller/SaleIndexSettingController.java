package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.SaleIndexSettingBusiness;
import com.pes.jd.model.DO.SaleIndexSettingDO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.SaleIndexSettingParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/sale/target/")
public class SaleIndexSettingController {
	@Autowired
	private SaleIndexSettingBusiness saleIndexSettingBusiness;
	private final Logger logger= LoggerFactory.getLogger(SaleIndexSettingController.class);
	@RequestMapping("insertSaleIndexSetting")
	public ApiResponse insertSaleIndexSetting(
			@RequestParam("shopId") String shopIdStr,
			@RequestParam("param") String paramStr
			) {
		SaleIndexSettingParam param = null;
		logger.info(this.getClass().getName(),"insertSaleIndexSetting()","shopIdStr"+shopIdStr,"paramStr"+paramStr);
		int num = 0;
		try {
			param = JacksonUtils.json2pojo(paramStr, SaleIndexSettingParam.class);
			if(StringUtils.isBlank(shopIdStr)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_06);
			}
			param.setShopId(Long.valueOf(shopIdStr));
		    num =  saleIndexSettingBusiness.insertSaleIndexSetting(param);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		if(num>0){
			ApiResponse saleIndexSettingInfo = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, new HashMap<>());
			logger.info(this.getClass().getName()+"insertSaleIndexSetting()"+"saleIndexSettingInfo"+saleIndexSettingInfo);
			return saleIndexSettingInfo;
		}else {
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CS_01_01);

		}
	}
	
	
	@RequestMapping("selectSaleIndexSetting")
	public ApiResponse selectSaleIndexSetting(
			@RequestParam("shopId") String shopIdStr,
			@RequestParam("date") String dateStr){
		Map<String, Object> data = Maps.newHashMap();
		try {
			Long shopId = Long.valueOf(shopIdStr);
			Date date = DateUtil.yyyyMMddFormat(new Date(Long.parseLong(dateStr)));
			SaleIndexSettingDO saleDO = saleIndexSettingBusiness.selectSaleIndexSettingByShop(shopId, date);
			data.put("saleIndex", saleDO);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			e.printStackTrace();
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_01);
		}
	}
}
