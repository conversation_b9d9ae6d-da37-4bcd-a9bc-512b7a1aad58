package com.pes.jd.controller;

import com.google.common.base.Strings;
import com.pes.jd.business.*;
import com.pes.jd.dao.JobRecordDao;
import com.pes.jd.data.api.OrderNotPayOperator;
import com.pes.jd.data.api.SinglePopOrderOperator;
import com.pes.jd.model.BO.PerformanceRuleValidDateBO;
import com.pes.jd.model.DO.JobPullRecordDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.TO.OrderDataNotPayTO;
import com.pes.jd.model.TO.SinglePopOrderTO;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/hand/")
public class HandController {
    private static final Logger logger = LoggerFactory.getLogger(HandController.class);
    @Autowired
    private ShopManageBusiness shopManageBusiness;
    @Autowired
    private JobPriorityTaskBusiness jobTaskBusiness;
    @Autowired
    private CsChatHandleBussiness csChatHandleBussiness;
    @Autowired
    private OrderHandleBussiness orderHandleBussiness;
    @Autowired
    private ShopCategoryAndGoodsBussiness shopCategoryAndGoodsBussiness;
    @Autowired
    private OrderRefundHandleBussiness orderRefundHandleBussiness;
    @Autowired
    private SinglePopOrderOperator singlePopOrderOperator;
    @Autowired
    private ShopPvUvDayHandleBussiness shopPvUvDayHandleBussiness;
    @Autowired
    private JobRecordDao jobRecordDao;
    @Autowired
    private OrderNotPayOperator orderNotPayOperator;
    @Autowired
    private ShopUserAnalysisHandBussiness shopUserAnalysisHandBussiness;
    @Autowired
    private SentimentAnalysisBusiness sentimentAnalysisBusiness;
    @Autowired
    private ReserveActivityBussiness reserveActivityBusiness;
    @Autowired
    private ShopGoodsRateHandBussiness shopGoodsRateHandBussiness;
    @Autowired
    private PresaleAndPreordainPerformanceHandleBusiness presaleAndPreordainPerformanceHandleBusiness;
    @Autowired
    private PerformanceRuleBusiness performanceRuleBusiness;
    @Autowired
    private CsTypeDayBusiness csTypeDayBusiness;
    /**
     * 店铺数据拉取与计算
     * http://localhost:9400/hand/pullAndCalShopData?shopId=81477&startDate=2019-06-20&endDate=2019-06-20
     *
     * @param shopId
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping("pullAndCalShopData")
    public Object pullAndCalShopData(
            @RequestParam(value = "shopId") String shopId,
            @RequestParam(value = "startDate") String startDate,
            @RequestParam(value = "endDate") String endDate,
            @RequestParam(value = "isYdStr", required = false) String isYdStr) {

        Map<String, Object> retMap = new HashMap<>();

        boolean isYd;
        if (Strings.isNullOrEmpty(isYdStr)) {
            isYd = false;
        }else {
            isYd = Boolean.valueOf(isYdStr);
        }

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
//        JobShopQuery jobShop = shopManageBusiness.getLocalJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取

        boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(date);
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                if (i > 0) {
                    jobDate.setNeedCalFinalData(Boolean.FALSE);
                }
                jobTaskBusiness.pullAndCalShopData(jobShop, jobDate, isDelData,isYd);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * 店铺数据计算
     * http://localhost:9400/hand/calShopData?shopId=612277&startDate=2020-06-04&endDate=2020-06-04
     *
     * @param shopId
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping("calShopData")
    @ResponseBody
    public Object calShopData(String shopId, String startDate, String endDate) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        //     JobShopQuery jobShop = shopManageBusiness.getLocalJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);

        boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(date);
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
//                if (i > 0) {
//                    jobDate.setNeedCalFinalData(Boolean.FALSE);
//                }
                jobTaskBusiness.calShopData(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    /**
     * pullShopLonginlog:(手动拉取登陆记录). <br/>
     *
     * @param startDate
     * @param endDate
     * @param shopId
     * @return
     * @throws IOException
     */
    @RequestMapping("pullShopLonginlog")
    @ResponseBody
    public Object pullShopLonginlog(String shopId, String startDate, String endDate, Integer dataType) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取

        boolean isDelData = true;

        try {
            for (Date date : dates) {
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                jobTaskBusiness.pullShopLoginLogData(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * http://localhost:9400/hand/pullChatPeersAndChatlogs?shopId=625260&startDate=2019-07-29&endDate=2019-07-29
     * 拉去聊天记录，聊天关系，聊天会话
     * pes_cs_chatlog_2025_04   pes_cs_chatpeer_2025_04  pes_cs_chat_session_2025_04
     */
    @RequestMapping("pullChatPeersAndChatlogs")
    @ResponseBody
    public Object pullChatPeersAndChatlogs(String shopId, String startDate, String endDate) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }
        List<Date> dates = DateUtil.splitDate(sDate, eDate);

        if (dates.isEmpty()) {
            retMap.put("msg", "dates is empty");
            return retMap;
        }
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取

        boolean isDelData = true;
        try {
            for (Date date : dates) {
                System.out.println("***********pull date*************:" + date);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                csChatHandleBussiness.pullChatPeersAndChatlogs(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    @RequestMapping("pullShopCategory")
    public Object pullShopCategory(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopCategory(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺类目失败";
        }
        return "拉取店铺类目成功";
    }


    @RequestMapping("pullShopSku")
    public Object pullShopSku(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopSku(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺sku失败";
        }
        return "拉取店铺sku成功";
    }


    @RequestMapping("pullShopGood")
    public Object pullShopGood(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopGood(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺商品失败";
        }
        return "拉取店铺商品成功";
    }


    @RequestMapping("pullOrderRefundData")
    public Object pullOrderRefundData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                orderRefundHandleBussiness.pullOrderRefundApplyData(jobShop, jobDate, true);

                orderRefundHandleBussiness.pullOrderRefundCheckData(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺退款数据失败";
        }
        return "拉取店铺退款数据成功";
    }

    /**
     * @Description:(作用)
     * @param:@param shopId
     * @param:@param startDate
     * @param:@param endDate
     * @param:@return
     * @return:Object
     * @author:Lsp
     * @date:2018年11月8日
     * @version:V1.8
     */
    @RequestMapping("pullShopOrderData")
    public Object
    pullShopOrderData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                // 交易订单
                orderHandleBussiness.pullShopOrderInfo(jobShop, jobDate, true);

                // 增量订单
                orderHandleBussiness.pullShopIncrementOrder(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺订单失败";
        }
        return "拉取店铺订单成功";
    }


    @RequestMapping("pullShopOrderByOrderId")
    public Object pullShopOrderByOrderId(String shopId, Long orderId) {

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());
        String result = null;
        try {
            SinglePopOrderTO orderDto = singlePopOrderOperator.getSinglePopOrder(orderId, jobShop.getShop().getSessionKey());
            result = orderDto.getSinglePopOrderResult().getMsg();
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取单个订单失败";
        }
        return result;
    }


    /**
     * pullShopPvUv:(手动拉取pvuv). <br/>
     */
    @RequestMapping("pullShopPvUv")
    public Object pullShopPvUv(String shopId, String startDate, String endDate) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        ObjectUtils.isEmpty(sDate);

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取

        boolean isDelData = true;
        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(date);
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopPvUvDayHandleBussiness.pullShopPvUvDayInfo(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }
    @RequestMapping("pullShopNoPayOrderData")
    public Object pullShopNoPayOrderData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                // 交易订单
                orderHandleBussiness.pullShopNoPayOrderInfo(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺订单失败";
        }
        return "拉取店铺订单成功";
    }

    @Autowired
    private ShopPopHandBusiness shopPopHandBusiness;

    @Deprecated
    @RequestMapping("pullShopPop")
    public Object pullShopPop(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                // 交易订单
                shopPopHandBusiness.pullShopPop(jobShop, jobDate, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取失败";
        }
        return "拉取成功";
    }


    @RequestMapping("pullShopNoPayByOrderId")
    public Object pullShopNoPayByOrderId(String shopId, Long orderId) {

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);//手动拉取
        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());
        String result = null;
        try {
            OrderDataNotPayTO orderDto = orderNotPayOperator.getOrderNotPay(orderId, jobShop.getShop().getSessionKey());
            result = orderDto.getPopOrderNotPayOrderByIdResponse().getMsg();
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取单个未付款订单失败";
        }
        return result;
    }

    //插入基础数据记录
    private void handlePullJobRecord(JobPullRecordDO jobPullRecord, JobShopDTO jobShop, Date date) {
        int m = jobRecordDao.deleteJobPullRecordByShopIdAndDate(jobShop, date);
        logger.info("{},{} jobrecord delete num {}", jobShop.getTitle(), jobShop.getDb(), m);
        int n = jobRecordDao.insertPullJobRecord(jobShop, jobPullRecord, date);
        logger.info("{},{} jobrecord insert num ", jobShop.getTitle(), jobShop.getDb(), n);
    }

    // http://localhost:9400/hand/handShopUserAnalysis?shopId=625260&startDate=2019-11-21&endDate=2019-11-21
    @RequestMapping("calShopUserAnalysis")
    public Object calShopUserAnalysis(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopDTO jobShop = shopManageBusiness.getShopSplitByShopId(Long.valueOf(shopId));

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                //店铺使用分析
                shopUserAnalysisHandBussiness.calShopUserAnalysis(jobShop, jobDate);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "处理失败";
        }
        return "处理成功";
    }

    @RequestMapping("handShopUserAnalysis")
    public Object handShopUserAnalysis(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopDTO jobShop = shopManageBusiness.getShopSplitByShopId(Long.valueOf(shopId));
        System.out.println("店铺id" + jobShop.getShopId() + "----" + "店铺名称" + jobShop.getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                //店铺使用分析
                shopUserAnalysisHandBussiness.handShopUserAnalysis(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "处理失败";
        }
        return "处理成功";
    }

    /**
     * 手动生成情感分析初始数据
     * http://localhost:9400/hand/generateSentimentAnalysis?shopId=81477&startDate=2019-11-12&endDate=2019-11-12
     *
     * @param shopId
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping("generateSentimentAnalysis")
    @ResponseBody
    public Object generateSentimentAnalysis(String shopId, String startDate, String endDate) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        //     JobShopQuery jobShop = shopManageBusiness.getLocalJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);

        boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);
                JobDateQuery jobDate = new JobDateQuery(date);

                sentimentAnalysisBusiness.handleSentimentAnalysis(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    @RequestMapping("pullShopActivity")
    public Object pullShopActivity(String shopId) throws Exception {
        JobDateQuery jobDate = new JobDateQuery(new Date());
        jobDate.setStartDate(DateUtil.getStartTimeOfDate(new Date()));
        jobDate.setEndDate(DateUtil.getEndTimeOfDate(new Date()));
        try {
            JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
            /*
             * 店铺商品SKU (in use) - 少鹏
             */
            long s4 = System.currentTimeMillis();
            shopCategoryAndGoodsBussiness.pullShopSku(jobShop, jobDate, true);
            long e4 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f4-time={}ms - method[pullShopSku-api]", (e4 - s4));


            //预售活动拉取
            long s23 = System.currentTimeMillis();
            shopCategoryAndGoodsBussiness.pullShopPresaleSku(jobShop);
            long e23 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f4.1-time={}ms - method[pullShopPresaleSku-api]", (e23 - s23));

            //预约活动拉取
            long s24 = System.currentTimeMillis();
            reserveActivityBusiness.pullReserveActivity(jobShop, true);
            long e24 = System.currentTimeMillis();
            logger.info("pop-job-invoke-api-" + shopId + "-f4.2-time={}ms - method[pullReserveActivity-api]", (e24 - s24));

        } catch (Exception e) {
            e.printStackTrace();
            return "拉取活动失败";
        }

        return "拉取活动成功";
    }

    @RequestMapping("pullShopGoodsRate")
    public Object pullShopGoodsRate(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopGoodsRateHandBussiness.pullShopGoodsRate(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "处理失败";
        }
        return "处理成功";
    }



    /**
     * 店铺数据计算
     * http://localhost:9400/hand/calShopData?shopId=81477&startDate=2019-06-24&endDate=2019-06-24
     *
     * @param shopId
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping("calPresaleAndPreordainPerformance")
    @ResponseBody
    public Object calPresaleAndPreordainPerformance(String shopId, String startDate, String endDate, String type) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        //     JobShopQuery jobShop = shopManageBusiness.getLocalJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);

        boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(date);
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                jobShop.setHand(Boolean.FALSE);
                PerformanceRuleValidDateBO validDate = performanceRuleBusiness.getPerformanceRuleValidDate(jobShop, jobDate);
                jobDate.setValidDate(validDate);

                jobDate.getCommonDates().add(jobDate.getDate());
                jobDate.getDates().addAll(DateUtil.splitDate(validDate.getEnquiry2PayValidDate(), validDate.getDate()));

                /*
                 * 客服每日类型(售前 | 售后 - 温辉
                 */
                csTypeDayBusiness.handleCsTypeDay(jobShop, jobDate, isDelData);

                if (type.equals("0")) {
                    presaleAndPreordainPerformanceHandleBusiness.handlePerformanceForPresale(jobShop, jobDate, isDelData);
                } else {
                    presaleAndPreordainPerformanceHandleBusiness.handlePerformanceForPreordain(jobShop, jobDate, isDelData);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    @RequestMapping("pushShopOrderFromJd")
    public Object pushShopOrderFromJd(String shopId, Long venderId, Integer type) {
        //JobDateQuery jobDate, Integer type, boolean isDelData
        JobShopQuery jobShop = new JobShopQuery();
        JobShopDTO shop = new JobShopDTO();

        shop.setShopId(Long.valueOf(shopId));
        shop.setVenderId(venderId);
        shop.setSchemaId("pes_jd_sub_01");
        shop.setDb("db_01");
        shop.setRtDb("db_01");
        shop.setRtSchemaId("pes_jd_rtsub_01");
        jobShop.setShop(shop);

        JobDateQuery jobDate = new JobDateQuery();
        jobDate.setStartDate(DateFormatUtils.getStartTimeOfDate(DateUtils.parseYMd("2020-05-17")));
        jobDate.setEndDate(DateFormatUtils.getEndTimeOfDate(DateUtils.parseYMd("2020-05-17")));
        try {
            long s1 = System.currentTimeMillis();
            orderHandleBussiness.pushShopOrderFromJd(jobShop, jobDate, type, true);

            long e1= System.currentTimeMillis();
            System.err.println("============> 毫秒 "+ (e1-s1));
        } catch (Exception e) {
            e.printStackTrace();
            return "pushShopOrderFromJd error";
        }
        return "success";
    }

}
