package com.pes.jd.controller;

import com.pes.jd.business.main.UserReportPropertyBusiness;
import com.pes.jd.framework.FormUrlencoded;
import com.pes.jd.model.DO.UserReportProperty;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/7 9:58 AM
 * @since 1.0.0
 */
@RestController
@RequestMapping("/user/report")
public class UserReportPropertyController {
    private final static Logger LOGGER = LoggerFactory.getLogger(UserReportPropertyController.class);
    @Autowired
    private UserReportPropertyBusiness userReportPropertyBusiness;

    @RequestMapping("insertorupdate")
    public Object insertOrUpdate(@FormUrlencoded UserReportProperty u){
        try {
            userReportPropertyBusiness.insertOrUpdate(u);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        }catch (Exception e){
            LOGGER.error(" update or insert user report property error ! ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_UP_01_01);
        }
    }

}
