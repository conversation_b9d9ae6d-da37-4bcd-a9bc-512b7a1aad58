package com.pes.jd.controller;


import com.alibaba.fastjson.JSONObject;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderInfoFBP;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderSearchInfo;
import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.getRemarkByCreateTime.OrderRemark;
import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.modifyVenderRemark.OperatorResult;
import com.jd.open.api.sdk.response.order.VenderRemark;
import com.pes.jd.business.sub.OrderCustomerBusiness;
import com.pes.jd.data.api.OrderReMarkOperator;
import com.pes.jd.data.api.ShippingAddressOperator;
import com.pes.jd.data.convert.OrderDataConverter;
import com.pes.jd.data.convert.OrderReMarkConverter;
import com.pes.jd.model.BO.OrderAddressInvoiceBO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.CustomerOrderStatusEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopBaseDataParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.TO.OrderResultTO;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2019-05-23 14:02
 */
@RestController
@RequestMapping(value = "/customer")
public class ShopCustomerInfoController {
    @Autowired
    private OrderCustomerBusiness orderCustomerBusiness;
    @Autowired
    private OrderDataConverter orderDataConverter;


    private static final Logger logger = LoggerFactory.getLogger(ShopCustomerInfoController.class);

//    Date creatTime=new Date(1559005815000L);

    @Autowired
    private ShippingAddressOperator shippingAddressOperator;


    @Autowired
    private OrderReMarkConverter converter;

    @Autowired
    private OrderReMarkOperator orderReMarkOperator;


    @RequestMapping(value = "/order/getOrderGoodsSku")
    @ResponseBody
    public ApiResponse getOrderGoodsSku(
            @RequestParam("shop") String shopStr,
            @RequestParam("orderId") Long orderId,
            @RequestParam("orderTime") Long orderTime
    ) {
        List<GoodsDTO> list = new ArrayList<GoodsDTO>();
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            list = orderCustomerBusiness.getOrderGoodsSku(shop, orderId, orderTime);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, list);
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderList error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, list);
        }

    }


    @RequestMapping(value = "/order/getOrderList")
    @ResponseBody
    public ApiResponse getOrderList(
            @RequestParam("shop") String shopStr,
            @RequestParam("buyerNick") String buyerNick,
            @RequestParam("endTime") Long endTime,
            @RequestParam("querySortPageQuery") String sortPageQueryStr
    ) {
        List<OrderDTO> list = new ArrayList<OrderDTO>();
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            SortPageQuery sortPageQuery = JSONObject.toJavaObject(JSONObject.parseObject(sortPageQueryStr), SortPageQuery.class);
            list = orderCustomerBusiness.getOrderList(shop, buyerNick, new Date(endTime), sortPageQuery);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, list);
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderList error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, list);
        }

    }


    @RequestMapping(value = "/order/getOrderCount")
    @ResponseBody
    public ApiResponse getOrderCount(
            @RequestParam("shop") String shopStr,
            @RequestParam("buyerNick") String buyerNick,
            @RequestParam("endTime") Long endTime
    ) {
        int count = 0;
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            count = orderCustomerBusiness.selectOrderCount(shop, buyerNick, endTime);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, count);
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderCount error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, count);
        }

    }


    @RequestMapping(value = "/order/getInoviceAndAddress")
    @ResponseBody
    public ApiResponse getInoviceAndAddress(
            @RequestParam("shop") String shopStr,
            @RequestParam("orderIds") String orderIds,
            @RequestParam("colType") Integer colType
    ) {
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            JSONObject resObject = new JSONObject();
            ArrayList<Object> list = JSONObject.toJavaObject(JSONObject.parseArray(orderIds), ArrayList.class);
            for (Object obj : list) {
                JSONObject jobj = JSONObject.parseObject(JSONObject.toJSONString(obj));
                Long orderId = jobj.getLong("orderId");
                Long orderTime = jobj.getLong("orderTime");
                OrderAddressInvoiceBO orderAddressInvoiceBO = orderCustomerBusiness.getInoviceAndAddress(shop, Long.valueOf(orderId), colType, new Date(orderTime));
                resObject.put(orderId + "", orderAddressInvoiceBO);
            }

            JSONObject jObject = new JSONObject();
            jObject.put("result", resObject);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, jObject);

        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getInoviceAndAddress error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01);
        }

    }


    @RequestMapping(value = "/order/getRefund")
    @ResponseBody
    public ApiResponse getRefund(
            @RequestParam("shop") String shopStr,
            @RequestParam("orderIds") String orderIds

    ) {
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            JSONObject resobj = new JSONObject();
            ArrayList<Object> list = JSONObject.toJavaObject(JSONObject.parseArray(orderIds), ArrayList.class);
            for (Object obj : list) {
                JSONObject jobj = JSONObject.parseObject(JSONObject.toJSONString(obj));
                Long orderId = jobj.getLong("orderId");
                Long orderTime = jobj.getLong("orderTime");

                try {
                    resobj.put(orderId + "", orderCustomerBusiness.getRefund(shop, Long.valueOf(orderId), new Date(orderTime)));
                } catch (Exception e) {
                    resobj.put(orderId + "", 0);
                }
            }
//            map.put("result", map);
            JSONObject obj = new JSONObject();
            obj.put("result", resobj);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, obj);

        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getRefund error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01);
        }

    }


    @RequestMapping(value = "/order/updateOrderRemarkInfo")
    @ResponseBody
    public ApiResponse updateOrderRemarkInfo(
            @RequestParam("shop") String shopStr,
            @RequestParam("orderTime") Long orderTime,
            @RequestParam("orderId") Long orderId,
            @RequestParam("flag") Integer flag,
            @RequestParam("remark") String remark,
            @RequestParam("orderInfoLogUploadParamStr") String orderInfoLogUploadParamStr
    ) {
        ShopCommonParam shop = null;
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
            OrderRemark orderRemark = new OrderRemark();
            orderRemark.setOrderId(orderId);
            orderRemark.setFlag(flag);
            orderRemark.setRemark(remark);
            ApiResponse apiResponse = new ApiResponse();
            OperatorResult operatorResult = orderReMarkOperator.updateOrderRemark(orderRemark, orderId, shop.getSessionKey(), orderInfoLogUploadParam);

            if (operatorResult.getSuccess()) {
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());

//        	  更新本地数据库
                orderCustomerBusiness.updateOrderRemarkInfo(shop, orderTime, orderRemark, orderInfoLogUploadParam);

            } else {
                apiResponse.setRpMsg("当前订单不支持修改");
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_CJ_01_01.getCode());
            }
            return apiResponse;

        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.updateOrderRemarkInfo error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01);
        }

    }


    @RequestMapping(value = "/order/getOrderRemark")
    @ResponseBody
    public ApiResponse getOrderDetail(
            @RequestParam("shop") String shopStr,
            @RequestParam("orderTime") Long orderTime,
            @RequestParam("orderIds") Long orderIds
    ) {
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            List<OrderDTO> orderList = orderCustomerBusiness.selectOrderById(shop, orderIds, new Date(orderTime));
            if (null == orderList || orderList.size() == 0) {
                logger.error("未找到相关订单！！");
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, new ArrayList<>());
            }
            OrderDTO orderDTO = orderList.get(0);

//            List<OrderRemarkDTO> list= orderCustomerBusiness.getOrderRemark(shop, Long.valueOf(orderIds),new Date(orderTime));
//                 if(list!=null && list.size()>0) {
//                	 OrderRemarkDTO orderRemarkDTO =list.get(0);
//
//            		int status = 0;
//            		if(orderDTO.getStatus()!=null) {
//            			status = CustomerOrderStatusEnum.getType(orderDTO.getStatus());
//            		}
//
//
//            		if( status ==2 || status==3 || status==4|| status==5||  status==6) {
////            			付款完成
//            			orderRemarkDTO.setRemarkFlag(1);
//            		}else {
//            			orderRemarkDTO.setRemarkFlag(0);
//            		}
//
//                	 return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,orderRemarkDTO);
//                 }else {
            //实时获取
            OrderRemarkDTO orderRemarkDTO = new OrderRemarkDTO();


            int status = 0;
            if (orderDTO.getStatus() != null) {
                status = CustomerOrderStatusEnum.getType(orderDTO.getStatus());
            }


            if (status == 2 || status == 3 || status == 4 || status == 5 || status == 6) {
//            			付款完成
                orderRemarkDTO.setRemarkFlag(1);
            } else {
                orderRemarkDTO.setRemarkFlag(0);
            }


            VenderRemark venderRemark = converter.pullOrderConvert(shop.getSessionKey(), Long.valueOf(orderIds));
            if (venderRemark != null) {
                orderRemarkDTO.setRemark(venderRemark.getRemark());
                orderRemarkDTO.setFlag(Integer.valueOf(venderRemark.getFlag() + ""));
                orderRemarkDTO.setCreated(new Date());
                orderRemarkDTO.setModified(new Date());
                orderRemarkDTO.setOrderId(Long.valueOf(orderIds));
                orderRemarkDTO.setShopId(shop.getShopId());

                OrderRemark orderRemark = new OrderRemark();
                orderRemark.setOrderId(Long.valueOf(orderIds));
                orderRemark.setFlag(Integer.valueOf(venderRemark.getFlag() + ""));
                orderRemark.setRemark(venderRemark.getRemark());
                orderCustomerBusiness.modifyRemark(shop, new Date(orderTime), orderRemark);

            } else {
                orderRemarkDTO.setRemark("");
                orderRemarkDTO.setFlag(new Integer("0"));
            }
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderRemarkDTO);
//            }


        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderRemark error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01);
        }

    }


    @RequestMapping(value = "/order/getOrderAddress")
    @ResponseBody
    public ApiResponse getOrderAddFlag(
            @RequestParam("shop") String shopStr,
            @RequestParam("orderTime") Long orderTime,
            @RequestParam("orderIds") Long orderIds,
            @RequestParam("colType") Integer colType
    ) {
        ShopCommonParam shop = null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            List<OrderDTO> orderList = orderCustomerBusiness.selectOrderById(shop, orderIds, new Date(orderTime));

            if (null == orderList && orderList.size() == 0) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01);
            }

            OrderConsignDTO orderConsignDTO = new OrderConsignDTO();
            OrderDTO orderDTO = orderList.get(0);
            int status = 0;
            if (orderDTO.getStatus() != null) {
                status = CustomerOrderStatusEnum.getType(orderDTO.getStatus());
            }
            if (status != 2) {
                orderConsignDTO.setModifyAddressFlag(0);
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderConsignDTO);
            }


            orderConsignDTO = orderCustomerBusiness.getOrderAddFlag(shop, Long.valueOf(orderIds), new Date(orderTime), orderIds, colType);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderConsignDTO);


        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderRemark error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01);
        }

    }


    @RequestMapping(value = "/order/getOrderDetailInfo")
    @ResponseBody
    public ApiResponse getOrderDetail(
            @RequestParam("shop") String shopStr,
            @RequestParam("buyer_nick") String buyer_nick,
            @RequestParam("colType") Integer colType,
            @RequestParam("sortPageQuery") String sortPageQueryStr,
            @RequestParam("orderInfoLogUploadParamStr") String orderInfoLogUploadParamStr
    ) {
        List<OrderInfoDTO> orderInfoDTOList = null;
        ShopCommonParam shop = null;
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            logger.info("查询插件订单列表获取数据开始");
            Long s1 = System.currentTimeMillis();

//        	 Date creatTime = new Date(CustomerOrderEnum.orderCreateTime.getCreateTime());
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
            SortPageQuery sortPageQuery = JSONObject.toJavaObject(JSONObject.parseObject(sortPageQueryStr), SortPageQuery.class);
            Map<String, Object> map = orderCustomerBusiness.selectOrderDeatilByShopIdAndBuyerNick(shop, buyer_nick, new Date(), colType, sortPageQuery, orderInfoLogUploadParam);
            Long s2 = System.currentTimeMillis();
            logger.info("查询插件订单列表获取数据时间  {}", s2 - s1);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, map);
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderDetail error:{}", e.getMessage(), e);
            ApiResponse apiResponse = new ApiResponse();
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_CJ_01_01.getCode());
            return apiResponse;
        }

    }


    @RequestMapping(value = "/order/updateAddress")
    public ApiResponse updateAddress(@RequestParam("orderId") Long orderId,
                                     @RequestParam("shop") String shopStr,
                                     @RequestParam("buyer_nick") String buyer_nick,
                                     @RequestParam("orderTime") Long orderTime,
                                     @RequestParam("shippingAddressDTO") String shippingAddressDTO,
                                     @RequestParam("orderInfoLogUploadParamStr") String orderInfoLogUploadParamStr
    ) {
        ApiResponse apiResponse = new ApiResponse();
        ShopCommonParam shop = new ShopCommonParam();
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            ShippingAddressDTO shippingAddressDTOparam = JSONObject.toJavaObject(JSONObject.parseObject(shippingAddressDTO), ShippingAddressDTO.class);
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
            Boolean flag = shippingAddressOperator.modifyOrderAddr(shippingAddressDTOparam, shop.getSessionKey());
            if (flag) {
                orderCustomerBusiness.updateAddress(shop, orderId, buyer_nick, shippingAddressDTOparam, new Date(orderTime), orderInfoLogUploadParam);
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
            } else {
                apiResponse.setRpCode("当前订单不支持修改");
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_CJ_01_01.getMsg());
            }
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController========updateAddress=========", e.getMessage(), e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_CJ_01_01.getCode());
            apiResponse.setRpMsg("当前订单不支持修改");

        }
        return apiResponse;
    }

    @RequestMapping(value = "/order/getVenderRemark")
    public ApiResponse getVenderRemark(@RequestParam("orderId") Long orderId,
                                       @RequestParam("orderTime") Long orderTime,
                                       @RequestParam("shop") String shopStr
    ) {
        ApiResponse apiResponse = new ApiResponse();
        ShopBaseDataParam shop = new ShopBaseDataParam();
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopBaseDataParam.class);
            OrderRemarkDTO orderRemarkDTO = new OrderRemarkDTO();
            OrderResultTO orderInfo = orderDataConverter.getOrderInfo(shop, orderId);
            if (null == orderInfo) {
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderRemarkDTO);
            }

            int status = 0;
            if (shop.getColType() == 0) {
                OrderSearchInfo popOrder = orderInfo.getPopOrder();
                if (null == popOrder) {
                    return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderRemarkDTO);
                }

                status = CustomerOrderStatusEnum.getOrderState(popOrder.getOrderState());
            } else {
                OrderInfoFBP fbpOrder = orderInfo.getFbpOrder();
                if (null == fbpOrder) {
                    return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderRemarkDTO);
                }

                status = CustomerOrderStatusEnum.getOrderState(fbpOrder.getOrderState());
            }

            if (status == 2 || status == 3 || status == 4 || status == 5 || status == 6) {
                // 已付款
                orderRemarkDTO.setRemarkFlag(1);
            } else {
                orderRemarkDTO.setRemarkFlag(0);
            }

            VenderRemark venderRemark = converter.pullOrderConvert(shop.getSessionKey(), orderId);
            if (venderRemark != null) {
                orderRemarkDTO.setRemark(venderRemark.getRemark());
                orderRemarkDTO.setFlag(new Integer(venderRemark.getFlag() + ""));
            } else {
                orderRemarkDTO.setRemark("");
                orderRemarkDTO.setFlag(new Integer("0"));
            }

            orderRemarkDTO.setOrderId(orderId);
            orderRemarkDTO.setShopId(shop.getShopId());
            orderRemarkDTO.setCreated(new Date());
            orderRemarkDTO.setModified(new Date());

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, orderRemarkDTO);
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController========getVenderRemark=========", e.getMessage(), e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_CJ_01_01.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_CJ_01_01.getMsg());
        }
        return apiResponse;
    }


}
