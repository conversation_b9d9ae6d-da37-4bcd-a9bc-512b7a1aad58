package com.pes.jd.controller;


import com.pes.jd.business.ShopCustomerBusiness;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.model.DTO.OrderDTO;
import com.pes.jd.model.DTO.ShippingAddressDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.SecurityMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2019-05-21 15:06
 */
@RestController
@RequestMapping(value = "/customer")
public class ShopCustomerInfoController extends BaseController {
    @Resource
    private ShopCustomerBusiness shopCustomerBusiness;
    private static final Logger logger = LoggerFactory.getLogger(ShopCustomerInfoController.class);
    @RequestMapping(value = "/getInoviceAndAddress")
    public ApiResponse getInoviceAndAddress(@RequestParam(value = "shopId")Long shopId,
                                            @RequestParam(value = "orderIds")String orderIds
    ) {
        ApiResponse apiResponse = new ApiResponse();
        try {
            ShopQuery shop = this.getSelectShop(shopId+"");
            apiResponse = shopCustomerBusiness.getInoviceAndAddress(shop,orderIds);
        } catch (Exception e) {
            logger.error("CustomerInfoController.getInoviceAndAddress error {}" + e.getMessage(),e);
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
        }
        return apiResponse;
    }

    @RequestMapping(value = "/getRefund")
    public ApiResponse getRefund(@RequestParam(value = "shopId")Long shopId,
                                 @RequestParam("orderIds") String orderIds

    ) {
        ApiResponse apiResponse = new ApiResponse();
        try {
            ShopQuery shop = this.getSelectShop(shopId+"");
            apiResponse = shopCustomerBusiness.getRefund(shop,orderIds);
        } catch (Exception e) {
            logger.error("CustomerInfoController.getInoviceAndAddress error {}" + e.getMessage(),e);
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
        }
        return apiResponse;
    }

    @RequestMapping(value = "/getOrderRemark")
    public ApiResponse getOrderRemark(@RequestParam(value = "shopId")Long shopId,
                                      @RequestParam("orderIds") String orderIds,
             @RequestParam("orderTime") Long orderTime

    ) {
        ApiResponse apiResponse = new ApiResponse();
        try {
            ShopQuery shop = this.getSelectShop(shopId+"");
            apiResponse = shopCustomerBusiness.getVenderRemark(shop,Long.parseLong(orderIds),orderTime);
        } catch (Exception e) {
            logger.error("CustomerInfoController.getInoviceAndAddress error {}" + e.getMessage(),e);
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
        }
        return apiResponse;
    }

    @RequestMapping(value = "/selectShopOrderDetailInfo")
    public ApiResponse selectShopOrderDetailInfo(@RequestParam(name ="shopId") String shopId ,
                                                @RequestParam(name="buyer_nick") String buyerNick,
                                                @RequestParam(name = "currentPage") Integer currentPage,
                                                @RequestParam(name = "size") Integer size,
                                                 String csNick,
                                                 String deviceId,
                                                 HttpServletRequest request
                                           ) {
        ApiResponse apiRespone;
        OrderInfoLogUploadParam orderInfoLogUploadParam = null;
        try {
            try {
                ShopDTO currentShop = getCurrentShop();
                if(currentShop.getShopId() != Long.parseLong(shopId)){
                    return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
                }
            }catch (LoginAuthException e){
                logger.error("selectShopOrderDetailInfo login auth error: {}", e.getMessage());
            }
            ShopQuery shop = this.getSelectShop(shopId);

                SortPageQuery sortPageQuery = new SortPageQuery();
              sortPageQuery.setSize(size.longValue());
              sortPageQuery.setCurrentPage( currentPage * sortPageQuery.getSize());
            //封装上报订单日志需要的参数
            try {
                orderInfoLogUploadParam = this.initOrderInfoLogUploadParam(request,shop,csNick,deviceId);
            }catch (Exception e){
                logger.error("initOrderInfoLogUploadParam error",e);
            }
            apiRespone = shopCustomerBusiness.selectCustomerOrderInfo(shop, buyerNick,sortPageQuery,orderInfoLogUploadParam);
            return apiRespone;
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.selectShopOrderDetailInfo error" +e);
            Map <String,Object> map = new HashMap<>();
            map.put("orderList", new ArrayList<OrderDTO>() );
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
        }
    }


    @RequestMapping(value = "/getOrderAddress")
    public ApiResponse getOrderAddress(@RequestParam(name ="shopId") String shopId ,
                                                @RequestParam(name="orderTime") String orderTime,
                                                @RequestParam(name = "orderIds") Long orderIds
                                           ) {
        ApiResponse apiRespone = null;
        try {
            ShopQuery shop = this.getSelectShop(shopId);

            apiRespone = shopCustomerBusiness.getOrderAddress(shop, orderTime,orderIds);
            return apiRespone;
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.selectShopOrderDetailInfo error:{}" + e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001, apiRespone);
        }
    }


//    @RequestMapping(value = "/getVenderRemark")
//    public ApiResponse selectShopOrderDetailInfo(@RequestParam(name ="shopId") String shopId ,
//                                               @RequestParam(name="orderId") Long orderId,
//                                               @RequestParam(name="orderTime") Long orderTime
//                                           ) {
//        ApiResponse apiRespone = null;
//        try {
//        	ShopQuery shop = this.getSelectShop(shopId.toString());
//            apiRespone = shopCustomerBusiness.getVenderRemark(shop, orderId,orderTime);
//            return apiRespone;
//        } catch (Exception e) {
//            logger.error("ShopCustomerInfoController.selectShopOrderDetailInfo error:{}" + e.getMessage(),e);
//            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001, apiRespone);
//        }
//    }


    @RequestMapping(value = "/updateOrderRemarkInfo")
    public ApiResponse updateOrderRemarkInfo(@RequestParam("shopId") String shopId,
                                             @RequestParam("orderId")Long orderId,
                                             @RequestParam("flag")Integer flag,
                                             @RequestParam("remark")String remark,
                                             @RequestParam("orderTime") Long orderTime,
                                             String csNick,
                                             String deviceId,
                                             HttpServletRequest request){
        ApiResponse apiResponse = new ApiResponse();
        OrderInfoLogUploadParam orderInfoLogUploadParam = null;
        try {
            ShopQuery shop = this.getSelectShop(shopId);
//            OrderRemark orderRemark = new OrderRemark();
//            orderRemark.setOrderId(orderId);
//            orderRemark.setFlag(flag);
//            orderRemark.setRemark(remark);
            //封装上报订单日志需要的参数
            try {
                orderInfoLogUploadParam = this.initOrderInfoLogUploadParam(request,shop,csNick,deviceId);
            }catch (Exception e){
                logger.error(e.getMessage(),e);
            }
            return shopCustomerBusiness.updateOrderRemark(shop, orderId, flag, remark, orderTime, orderInfoLogUploadParam);
//            OperatorResult operatorResult= orderReMarkOperator.updateOrderRemark(orderRemark, orderId, shop.getSessionKey());
//            if(operatorResult.getSuccess()) {
//            	 apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
//                 apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
//            }else {
//            	 apiResponse.setRpMsg("修改订单备注失败");
//            	 apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
//            }
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.updateOrderRemarkInfo error:{}" + e.getMessage(),e);
            apiResponse.setRpMsg("修改订单备注失败");
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
            return apiResponse;
        }


    }

    @RequestMapping(value = "/updateAddress")
    public ApiResponse updateAddressInfo(@RequestParam("shopId") String shopId,
                                         @RequestParam("buyer_nick") String buyerNick,
                                         @RequestParam("orderId") Long orderId,
                                         @RequestParam("customerName")String customerName,
                                         @RequestParam(name="customerPhone",required=false)String customerPhone,
                                         @RequestParam(name="provinceId",required = false)String provinceId,
                                         @RequestParam(name="cityId",required = false)String cityId,
                                         @RequestParam(name="countyId",required = false)String countyId,
                                         @RequestParam(name="townId",required = false)String townId,
                                         @RequestParam("detailAddr")String detailAddr,
                                         @RequestParam("customerMobile")String customerMobile,
                                         @RequestParam(name="orderTime") Long orderTime,
                                         String csNick,
                                         String deviceId,
                                         HttpServletRequest request
//                                         ,
//                                         @RequestParam("csNick") String  csNick
                                         ){

        ApiResponse apiResponse = new ApiResponse();
        OrderInfoLogUploadParam orderInfoLogUploadParam = null;
        try {
            ShopQuery shop = this.getSelectShop(shopId);
            ShippingAddressDTO shippingAddressDTO = new ShippingAddressDTO();
            shippingAddressDTO.setCityId(cityId);
            shippingAddressDTO.setCountyId(countyId);
            shippingAddressDTO.setCustomerName(customerName);
            shippingAddressDTO.setCustomerPhone(customerPhone);
            shippingAddressDTO.setDetailAddr(detailAddr);
            shippingAddressDTO.setOrderId(orderId);
            shippingAddressDTO.setProvinceId(provinceId);
            shippingAddressDTO.setTownId(townId);
            shippingAddressDTO.setCustomerMobile(customerMobile);
//        	shippingAddressDTO.setCsNick(csNick);

//        	测试 暂时去掉修改地址
            //封装上报订单日志需要的参数
            try {
                orderInfoLogUploadParam = this.initOrderInfoLogUploadParam(request,shop,csNick,deviceId);
            }catch (Exception e){
                logger.error(e.getMessage(),e);
            }
            apiResponse = shopCustomerBusiness.updateAddressInfo(shop, orderId, buyerNick,shippingAddressDTO,orderTime,orderInfoLogUploadParam);
//            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
//            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.updateAddressInfo error:{}" + e.getMessage(),e);
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
        }

        return apiResponse;

    }

    @RequestMapping(value = "/selectOrderGoodsInfo")
    public ApiResponse selectOrderGoodsInfo(@RequestParam("shopId")String shopId,
                                            @RequestParam("buyer_nick")String buyerNick,
                                            @RequestParam("orderId")Long orderId){
        ApiResponse apiResponse=new ApiResponse();
        try{
            ShopQuery shop = this.getSelectShop(shopId);
            shopCustomerBusiness.selectOrderGoodsInfo(shop,orderId,buyerNick);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        }catch (Exception e){
            logger.error("ShopCustomerInfoController==================selectOrderGoodsInfo" + e.getMessage());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_1001.getMsg());
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_1001.getCode());
        }
        return apiResponse;
    }
    private OrderInfoLogUploadParam initOrderInfoLogUploadParam(HttpServletRequest request, ShopQuery currentShop, String csNick, Object deviceId) {
        String ipAddr = SecurityMUtil.getIpAddr(request);
        logger.info("csNick={},deviceId={},Ip={}",csNick,deviceId,ipAddr);
        OrderInfoLogUploadParam orderInfoLogUploadParam = new OrderInfoLogUploadParam();
        orderInfoLogUploadParam.setJdId(currentShop.getTitle());
        orderInfoLogUploadParam.setDeviceId((String) deviceId);
        orderInfoLogUploadParam.setUserId(csNick);
        orderInfoLogUploadParam.setUserIp(ipAddr);
        orderInfoLogUploadParam.setTimeStamp(System.currentTimeMillis());
        return orderInfoLogUploadParam;
    }
}
