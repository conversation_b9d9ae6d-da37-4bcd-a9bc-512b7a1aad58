package com.pes.jd.controller;

import com.google.common.collect.Lists;
import com.pes.jd.business.main.DeptBusiness;
import com.pes.jd.business.main.DeptShopBusiness;
import com.pes.jd.business.main.DeptShopBussiness;
import com.pes.jd.model.DO.DeptShopDO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DeptShopVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RequestMapping("/dept/shop")
@RestController
public class DeptShopController extends BaseController{
	private final Logger logger=LoggerFactory.getLogger(DeptShopController.class);
	@Resource
	private DeptShopBussiness deptShopBussiness;
	
	@Autowired
	private DeptShopBusiness deptShopBusiness;

	@Autowired
    private DeptBusiness deptBusines;
	
	@Autowired
	private DeptBusiness deptBusiness;
	
	@RequestMapping("/selectDeptShopInfoByDeptId")
	public Object selectDeptShopInfoByDeptId(
			@RequestParam("deptId") Long deptId,
			@RequestParam("shopName") String shopName,
			@RequestParam(required = false,name=  "id") String id,
			@RequestParam(required = false,name=  "type") String type,
			@RequestParam(required = false,name= "shopType") String shopType){
		try {
			if(null==shopType) {
				shopType = "0";
			}
			 List<Long>  shpoIdsList;
			if("0".equals(shopType)) {
				shpoIdsList = deptBusiness.getShopIdByDeptIdAndType(id, deptId+"", type);
			}else {
				shpoIdsList = deptBusiness.getSelfShopIdByDeptId(deptId.intValue());
			}
			 
			 if(shpoIdsList==null || shpoIdsList.size()==0) {
					return apiResponse(ApiCodeEnum.CODE_ERROR_DP_01_01, RestApiResponse2.of(false));
			 }
			 
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(deptShopBussiness.selectDeptShopInfoByDeptId(shopName,deptId,shpoIdsList)));
		} catch (Exception e) {
			logger.info("selectDeptShopInfoByDeptId error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_DP_01_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/selectShopLst")
	public ApiResponse selectAllDeptShopByDeptId(
			@RequestParam("deptId") Integer deptId) {
			Map<String, Object> data = new HashMap<>();
			List<Long> shopLst = Lists.newArrayList();
		try {
			List<DeptShopDO> shopDOLst =  deptShopBusiness.selectAllByDeptId(deptId);
			for(DeptShopDO shop : shopDOLst){
				shopLst.add(shop.getShopId());
			}
			data.put("shopLst", shopLst);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
	}

	@RequestMapping("/selectShopIdsByDeptId")
	public ApiResponse selectShopIdsByDeptId(@RequestParam("id") String id,@RequestParam("deptId") String deptId,@RequestParam("type") String type){
		Map<String, Object> data = new HashMap<>();
		try {
			List<Long> shopIds=deptBusines.getShopIdByDeptIdAndType(id, deptId, type);
			data.put("shopIds", shopIds);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);

	}

	@RequestMapping("/selectShopByDeptId")
	public Object selectShopByDeptId(@RequestParam("id") String id,@RequestParam("deptId") String deptId
			,@RequestParam("type") String type,@RequestParam("shopParam") String shopParam){
		try {
			List<DeptShopVO> shops=deptBusines.getShopByDeptIdAndType(id, deptId, type, shopParam);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shops));
		} catch (Exception e) {
			e.printStackTrace();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(false));
		}
	}




}
