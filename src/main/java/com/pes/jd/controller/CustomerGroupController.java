package com.pes.jd.controller;

import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.CustomerGroupEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 插件管理 - 批量提醒
 */
@RestController
@RequestMapping("/customerGroup")
public class CustomerGroupController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CustomerGroupController.class);

    /**
     * 获取所有的标签v1
     * @return
     */
    @RequestMapping("/getAllTags")
    public ApiResponse getAllTags(){
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, CustomerGroupEnum.getCustomerGroupList());
    }

    /**
     * 根据remind_task_id和shopId获取任务的tagId
     * @return
     */
    @RequestMapping("/getTagsByRemindId")
    public ApiResponse getTagsByRemindId(){

        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
    }
}
