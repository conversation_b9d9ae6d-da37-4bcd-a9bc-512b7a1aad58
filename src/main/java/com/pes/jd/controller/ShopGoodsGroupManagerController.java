package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.ShopGoodsGroupManagerBussiness;
import com.pes.jd.model.DTO.GoodsGroupDTO;
import com.pes.jd.model.DTO.GoodsGroupSkuDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.constant.PesCommonConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/goods/group")
public class ShopGoodsGroupManagerController {
	private final Logger logger=LoggerFactory.getLogger(ShopGoodsGroupManagerController.class);
	@Autowired
	private ShopGoodsGroupManagerBussiness shopGoodsGroupManagerBussiness;
	/**
	 * @Description:（商品组列表查询） 
	*
	 */
	@RequestMapping("/selectGoodsGroup")
	public ApiResponse selectGoodsGroup(@RequestParam("shopId") Long shopId){
		ApiResponse apiResponse;
		Map<String, Object> result=Maps.newHashMap();
		try {
			List<GoodsGroupDTO>	goodsGroupLst = shopGoodsGroupManagerBussiness.selectGoodsGroupLst(shopId);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			result.put("goodsGroupLst", goodsGroupLst);
		} catch (Exception e) {
			logger.error("master selectGoodsGroup error:{}",e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_SPZ_01_02);
			result.put("goodsGroupLst", new ArrayList<GoodsGroupDTO>(0));
		}
		apiResponse.setData(result);
		return apiResponse;
	}
	/**
	 * @Description:（商品组已有的商品查询根据groupId） 
	*
	 */
	@RequestMapping("/selectSelectedGoodsSkuLstByGroupId")
	public ApiResponse selectSelectedGoodsSkuLstByGroupId(@RequestParam("groupId") Long groupId){
		ApiResponse apiResponse;
		Map<String, Object> result=Maps.newHashMap();
		try {
			List<GoodsGroupSkuDTO> selectedGroupSkuLst=	shopGoodsGroupManagerBussiness.selectSelectedSkuLstByGroupId(groupId);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			result.put("selectedGroupSkuLst", selectedGroupSkuLst);
		} catch (Exception e) {
			logger.error("master selectSelectedGoodsSkuLst error:{}",e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_SPZ_01_03);
			result.put("selectedGroupSkuLst", new ArrayList<GoodsGroupSkuDTO>(0));
		}
		apiResponse.setData(result);
		return apiResponse;
	}
	/**
	 * @Description:（商品组已有的商品查询根据shopId） 
	*
	 */
	@RequestMapping("/selectSelectedGoodsSkuLstByShopId")
	public ApiResponse selectSelectedGoodsSkuLstByShopId(@RequestParam("shopId") Long shopId){
		ApiResponse apiResponse;
		Map<String, Object> result=Maps.newHashMap();
		try {
			List<GoodsGroupSkuDTO> selectedGroupSkuLst=	shopGoodsGroupManagerBussiness.selectSelectedSkuLstByShopId(shopId);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			result.put("selectedGroupSkuLst", selectedGroupSkuLst);
		} catch (Exception e) {
			logger.error("master selectSelectedGoodsSkuLst error:{}",e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_SPZ_01_03);
			result.put("selectedGroupSkuLst", new ArrayList<GoodsGroupSkuDTO>(0));
		}
		apiResponse.setData(result);
		return apiResponse;
	}
	/**
	 * 商品组增加/修改
	 * 
	 */
	@RequestMapping("/saveOrUpdateGoodsGroup")
	public ApiResponse saveOrUpdateGoodsGroup(@RequestParam("shopId") Long shopId,
			@RequestParam(name="goodsGroupId",required=false) String goodsGroupId,
			@RequestParam(name="goodsGroupName") String goodsGroupName,
			@RequestParam(name="skuIds") String skuIds,
			@RequestParam("userId") Long userId,
                                              Byte dimension){
		ApiResponse apiResponse;
		try {
            if (dimension == null) {
                dimension = PesCommonConstant.DIMENSION_SKU_BYTE;
            }
            apiResponse = shopGoodsGroupManagerBussiness.saveOrUpdateGoodsGroup(shopId, goodsGroupId, goodsGroupName, skuIds, userId, dimension);
        } catch (Exception e) {
			logger.error("master saveOrUpdateGoodsGroup error:{}",e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_SPZ_01_01);
		}
		return apiResponse;
	}
	/**
	 * @Description:（商品组删除） 
	*
	 */
	@RequestMapping("/deleteGoodsGroup")
	public ApiResponse deleteGoodsGroup(@RequestParam("goodsGroupId") Long goodsGroupId){
		ApiResponse apiResponse;
		try {
			shopGoodsGroupManagerBussiness.deleteGoodsGroup(goodsGroupId);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			logger.error("master deleteGoodsGroup error:{}",e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_SPZ_01_02);
		}
		return apiResponse;
	}
	
	
}
