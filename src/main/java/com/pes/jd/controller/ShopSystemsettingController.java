package com.pes.jd.controller;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.main.*;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DO.ShopAccount;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.RealTimePerformanceSettingsParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.ShopSensitiveWordVO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName:ShopSystemsettingController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月25日 下午3:11:28 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@RestController
@RequestMapping("/shop/sysetting")
public class ShopSystemsettingController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(ShopSystemsettingController.class);
	@Resource
	private ShopSystemsettingBusiness shopSystemsettingBusiness;
	@Resource
	private PerformanceSettingBussiness performanceSettingBussiness;
	@Resource
	private ShopSensitiveWordBusiness shopSensitiveWordBusiness;
	@Resource
	private ShopBusiness shopBusiness;
	/**
	 * updateSubUserType:(添加/解除 店铺管理员). <br/>
	 *
	 * @param nick
	 * @param type
	 * @return
	 * @since JDK 1.8
	 */
	@RequestMapping("updateSubUserType")
	public ApiResponse updateSubUserType(@RequestParam(name = "nick") String nick,
			@RequestParam(name = "type") String type) {
		try {
			int update = 0;
			if ( CommonConstants.SUB_USER_TYPE_MANAGE.equals(type) ) {
				update = shopSystemsettingBusiness.updateSubUserRole(nick, CommonConstants.SUB_USER_TYPE_NO);
			} else if (CommonConstants.SUB_USER_TYPE_NO.equals(type) ){
				update = shopSystemsettingBusiness.updateSubUserRole(nick, CommonConstants.SUB_USER_TYPE_MANAGE);
			} else {
				throw new RuntimeException(String.format(" type 传参错误  ： %s    合理的值应该为 N 或 M  ",type));
			}
			if(update>0){
				return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			}else{
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_04_07);
			}
		} catch (Exception e) {
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_04_08,e.getMessage());
		}
	}

	/**
	 * updateWarnSetting:(修改告警设置)
	 */
	@RequestMapping("updateWarnSetting")
	public ApiResponse updateWarnSetting(@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "openWarn") String openWarnStr,
			@RequestParam(name = "warnAcceptCs") String warnAcceptCs) {
		if(StringUtils.isAnyBlank(shopId, openWarnStr)){
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_02);
		}
		if(StringUtils.length(warnAcceptCs) > 2000){
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_19);
		}
		try {
			Boolean openWarn = JacksonUtils.json2pojo(openWarnStr, Boolean.class);
			int updateNum = performanceSettingBussiness.updateWarnSetting(Long.parseLong(shopId),openWarn,warnAcceptCs);
			if (updateNum > 0) {
				return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_05);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_05);
	}

	/**
	 * selectShopAccountForShop:(查询店铺子账号). <br/>
	 *
	 * <AUTHOR>
	 * @param shopId
	 * @return
	 * @since JDK 1.8
	 */
	@RequestMapping(value = "/selectShopAccountForShop")
	public ApiResponse selectShopAccountForShop(@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "includeMainAccount") String includeMainAccount) {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {
			List<ShopAccount> shopAccountList = shopSystemsettingBusiness.selectShopAccountForShop(shopId,includeMainAccount);
			//查询 type 0-pop 1-自营
			List<ShopAccount> shopAccountList2 =new ArrayList<>();
			Integer type =0;
			ShopDTO shopDTO = shopBusiness.selectShopByShopId(shopId !=null ? Long.parseLong(shopId) : 0L);
			if(null != shopDTO){
				type = shopDTO.getType();
			}
			Map<String, List<ShopAccount>> collect = shopAccountList.stream().collect(Collectors.groupingBy(ShopAccount::getNick));
			if(1 == type){
				for (String s : collect.keySet()) {
					List<ShopAccount> shopAccounts = collect.get(s);
					if(shopAccounts.size() > 1){
						for (ShopAccount shopAccount : shopAccounts) {
							if(null == shopAccount.getSource()){
								shopAccount.setSource(0);
							}
							if(0 == shopAccount.getSource()){
								shopAccountList2.add(shopAccount);
							}
						}
					} else {
						shopAccountList2.addAll(shopAccounts);
					}
				}
			} else {
				shopAccountList2.addAll(shopAccountList);
			}
			data.put("retLsit", shopAccountList2);
			apiResponse.setData(data);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_09.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_09.getMsg());
			logger.error("selectShopAccountForShop：" + e.getMessage(), e);
		}
		return apiResponse;
	}

	/**
	 *
	 * @Description:(添加商品过滤设置)
	 * @param:@param goodList
	 * @param:@param shopId
	 * @param:@return
	 * @return:ApiResponse
	 * @author:Lsp
	 * @date:2018年11月6日
	 * @version:V1.8
	 */
	@RequestMapping("addGoodsFilter")
	@ResponseBody
	public ApiResponse addGoodsFilter(@RequestParam(name = "goodList") String goodList,
			@RequestParam(name = "shopId") String shopId) {
		Map<String, Object> resultMap = new HashMap<>();
		List<GoodsFilterDTO> pesGoodsFilter = null;
		ApiResponse apiResponse = new ApiResponse();
		try {
			pesGoodsFilter = JacksonUtils.json2list(goodList, GoodsFilterDTO.class);
		} catch (Exception e1) {
			logger.error(e1.getMessage(), e1);
		}

		if (pesGoodsFilter == null) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_51.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_51.getMsg());
			return apiResponse;
		}

		List<String> goodUrlList = new ArrayList<>();
		try {
			for (GoodsFilterDTO pr : pesGoodsFilter) {
				goodUrlList.add(pr.getGoodsUrl());
				pr.setShopId(Long.valueOf(shopId));
			}
			List<GoodsFilterDTO> goodsExisted = performanceSettingBussiness.getExistedPesGoods(Long.valueOf(shopId),
					goodUrlList);
			if (goodsExisted == null || goodsExisted.isEmpty()) {
				performanceSettingBussiness.addPesGoods(pesGoodsFilter);
				List<GoodsFilterDTO> goodsList = performanceSettingBussiness.getPesGoodsByShopId(Long.valueOf(shopId));
				resultMap.put("goods", goodsList);
				apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
				apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
				apiResponse.setData(resultMap);

			} else {
				apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_52.getCode());
				apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_52.getMsg());
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_51.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_51.getMsg());
		}
		apiResponse.setData(resultMap);
		return apiResponse;
	}

	/**
	 *
	 * @Description:(添加商品过滤设置)
	 * @param:@param goodList
	 * @param:@param shopId
	 * @param:@return
	 * @return:ApiResponse
	 * @author:Lsp
	 * @date:2018年11月6日
	 * @version:V1.8
     * spu维度下所有的sku全部添加
	 */
	@RequestMapping("addGoodsFilterOfSpu")
	@ResponseBody
	public ApiResponse addGoodsFilterOfSpu(@RequestParam(name = "goodList") String goodList,
			@RequestParam(name = "shopId") String shopId,
                                           String shopGoodsSku) {
        Map<String, Object> resultMap = new HashMap<>();
        List<GoodsFilterDTO> pesGoodsFilter = null;
        List<ShopGoodsSku> shopGoodsSkus = null;
        ApiResponse apiResponse = new ApiResponse();
        try {
            pesGoodsFilter = JacksonUtils.json2list(goodList, GoodsFilterDTO.class);
            shopGoodsSkus = JacksonUtils.json2list(shopGoodsSku, ShopGoodsSku.class);
        } catch (Exception e1) {
            logger.error(e1.getMessage(), e1);
        }
        if (pesGoodsFilter == null || shopGoodsSkus == null) {
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_51.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_51.getMsg());
            return apiResponse;
        }
        List<String> goodUrlList = new ArrayList<>();
        try {
            GoodsFilterDTO firstGoodFilter = pesGoodsFilter.get(0);
            List<GoodsFilterDTO> newGoodsFilter = new ArrayList<>();

            for (ShopGoodsSku goodsSkus : shopGoodsSkus) {
                GoodsFilterDTO goodsFilterDTO = new GoodsFilterDTO();
                goodsFilterDTO.setShopId(Long.valueOf(shopId));
                goodsFilterDTO.setNumIid(goodsSkus.getSkuId());
                goodsFilterDTO.setCreated(firstGoodFilter.getCreated());
                goodsFilterDTO.setName(goodsSkus.getSkuName());
                goodsFilterDTO.setEndTime(firstGoodFilter.getEndTime());
                goodsFilterDTO.setWareId(goodsSkus.getWareId());
                goodUrlList.add(goodsFilterDTO.getGoodsUrl());
                newGoodsFilter.add(goodsFilterDTO);
            }
            List<GoodsFilterDTO> allGoodFilter = performanceSettingBussiness.getPesGoodsByShopId(Long.valueOf(shopId));
            List<GoodsFilterDTO> goodsExisted = performanceSettingBussiness.getExistedPesGoods(Long.valueOf(shopId),
                    goodUrlList);
            if (goodsExisted == null || goodsExisted.isEmpty()) {
                if (CollectionUtils.isNotEmpty(allGoodFilter)) {
                    Set<Long> skuSet = allGoodFilter.stream().map(GoodsFilterDTO::getNumIid).collect(Collectors.toSet());
                    //已经存在的sku维度不添加
                    newGoodsFilter.removeIf(ele -> skuSet.contains(ele.getNumIid()));
                }
                performanceSettingBussiness.addPesGoods(newGoodsFilter);
                List<GoodsFilterDTO> goodsList = performanceSettingBussiness.getPesGoodsByShopId(Long.valueOf(shopId));
                resultMap.put("goods", goodsList);
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
                apiResponse.setData(resultMap);
            } else {
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_52.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_52.getMsg());
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_51.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_51.getMsg());
        }
        apiResponse.setData(resultMap);
        return apiResponse;
    }

	/**
	 *
	 * @Description:(删除商品过滤数据)
	 * @param:@param shopId
	 * @param:@param numIid
	 * @param:@return
	 * @return:ApiResponse
	 * @author:Lsp
	 * @date:2018年11月6日
	 * @version:V1.8
	 */
	@RequestMapping("deleteGoodsFilter")
	@ResponseBody
	public ApiResponse deleteGoodsFilter(@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "numIid") String numIid) {
		Map<String, Object> resultMap = new HashMap<>();
		ApiResponse apiResponse = new ApiResponse();
		try {
			List<String> goodsIdList = new ArrayList<>();
			String str[] = numIid.split(",");
			for (String string : str) {
				goodsIdList.add(string);
			}
			int row = performanceSettingBussiness.deletePesGoods(Long.valueOf(shopId), goodsIdList);
			List<GoodsFilterDTO> goodsList = performanceSettingBussiness.getPesGoodsByShopId(Long.valueOf(shopId));
			resultMap.put("goods", goodsList);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
			apiResponse.setData(resultMap);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			;
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_53.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_53.getMsg());
		}
		return apiResponse;
	}

	/**
	 *
	 * @Description:(作用)
	 * @param:@param ars
	 *                   自动回复过滤开关
	 * @param:@param arm
	 *                   自动回复过滤标识
	 * @param:@param sellAfter
	 *                   售后时间
	 * @param:@param mwt
	 *                   最大等待时间
	 * @param:@param std
	 *                   业务分隔时间点
	 * @param:@param jixiaoTime
	 *                   接待时间
	 * @param:@return
	 * @return:ApiResponse
	 * @author:Lsp
	 * @date:2018年11月6日
	 * @version:V1.8
	 */
	@RequestMapping("basicPerformanceSettings")
	@ResponseBody
	public ApiResponse basicPerformanceSettings(@RequestParam(name="shopId")String shopId,
			@RequestParam(name="shopSystemsettingDTO") String shopSystemsettingDTO) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopSystemsettingDTO sso = JacksonUtils.json2pojo(shopSystemsettingDTO, ShopSystemsettingDTO.class);
			performanceSettingBussiness.updateShopSystemsettingByShopId(sso);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_54.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_54.getMsg());
		}
		return apiResponse;
	}


	@RequestMapping("rtPerformanceSettings")
	@ResponseBody
	public ApiResponse rtPerformanceSettings(@RequestParam(name="shopId")String shopId,
			@RequestParam(name="shopSystemsettingDTO") String shopSystemsettingDTO) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopSystemsettingDTO sso = JacksonUtils.json2pojo(shopSystemsettingDTO, ShopSystemsettingDTO.class);
			performanceSettingBussiness.updateShopSystemsettingByShopId(sso);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_04_61);
		}
		return apiResponse;
	}



	@RequestMapping("performanceDecisionSettings")
	@ResponseBody
	public ApiResponse performanceDecisionSettings(@RequestParam(name="shopId")String shopId,
												   @RequestParam(name="shopSystemsettingDTO") String shopSystemsettingDTO) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopSystemsettingDTO sso = JacksonUtils.json2pojo(shopSystemsettingDTO, ShopSystemsettingDTO.class);
			performanceSettingBussiness.updateShopSystemsettingByShopId(sso);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_55.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_55.getMsg());
		}
		return apiResponse;
	}

    /**
     * 询单和出库有效时长修改限制
     * @param shopId
     * @return
     */
    @RequestMapping("queryIsModifyEnquiryAndOutStockValidTime")
    @ResponseBody
    public ApiResponse queryIsModifyEnquiryAndOutStockValidTime(@RequestParam(name="shopId") String shopId) {
        ApiResponse apiResponse = new ApiResponse();
        try {
            Map<String, Object> map = new HashMap<>();
            ShopSystemsettingFieidLimitDTO settingFieId = performanceSettingBussiness.queryIsModifyEnquiryAndOutStockValidTime(Long.parseLong(shopId));
            map.put("isModifyEnquiryAndOutStockValidTime", settingFieId);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());

            apiResponse.setData(map);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_59.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_59.getMsg());
        }
        return apiResponse;
    }

	/**
	 *
	 * @Description:(接待设置)
	 * @param:@param cws
	 *                   暗语过滤开关
	 * @param:@param cw
	 *                   暗语过滤词汇
	 * @param:@param cscf
	 *                   客服单口相声过滤开关
	 * @param:@param scn
	 *                   客服单口相声过滤客服回复句数
	 * @param:@return
	 * @return:ApiResponse
	 * @author:Lsp
	 * @date:2018年11月7日
	 * @version:V1.8
	 */
	@RequestMapping("receptionSettings")
	@ResponseBody
	public ApiResponse receptionSettings(
			@RequestParam(name = "shopSystemsettingDTO") String shopSystemsettingDTO) {
		ApiResponse apiResponse = new ApiResponse();
		try {
		    ShopSystemsettingDTO sso = JacksonUtils.json2pojo(shopSystemsettingDTO, ShopSystemsettingDTO.class);
			performanceSettingBussiness.updateShopSystemsettingByShopId(sso);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_56.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_56.getMsg());
		}
		return apiResponse;
	}

	/**
	 * 添加买家过滤的昵称
	 *
	 * @param
	 * @return
	 */
	@RequestMapping("addFliterBuynick")
	@ResponseBody
	public ApiResponse addFliterBuynick(@RequestParam(name = "nick") String nick,
			@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "currentUser") String currentUser) {
		Map<String, Object> data = Maps.newHashMap();
		BuyerNickFilterDTO buyernick;
		List<String> buyerNickLst = null;
		List<BuyerNickFilterDTO> buyerNickFilterLst = Lists.newArrayList();
		ApiResponse apiResponse = new ApiResponse();
		try {
			List<String> buyerNickListAlready = performanceSettingBussiness
					.selectFilterBuyerNickLstByShopId(Long.valueOf(shopId));
			if(!Strings.isNullOrEmpty(nick)){
				buyerNickLst = Arrays.asList(nick.split(","));
			}
			for(String buyer : buyerNickListAlready){
				if(buyerNickLst.contains(buyer)){
					data.put("buyer", buyer);
					apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_GL_01_01.getCode());
					apiResponse.setRpMsg("顾客"+buyer+ApiCodeEnum.CODE_ERROR_GL_01_01.getMsg());
					return apiResponse;
				}
			}
			for(String buyerNick1 : buyerNickLst){
				buyernick = new BuyerNickFilterDTO();
				buyernick.setBuyerNick(buyerNick1);
				buyernick.setCreated(new Date());
				buyernick.setShopId(Long.valueOf(shopId));
				buyernick.setCreatedBy(currentUser);
				buyerNickFilterLst.add(buyernick);
			}

			performanceSettingBussiness.insertBuyernickFilter(buyerNickFilterLst);
			data.put("buyerNickLst", buyerNickLst);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
			apiResponse.setData(data);
		} catch (Exception e) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_57.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_57.getMsg());
			logger.error(e.getMessage(), e);
		}
		return apiResponse;
	}

	/**
	 * 删除买家过滤的昵称
	 *
	 * @param
	 * @return
	 */
	@RequestMapping("deleteFliterBuynick")
	@ResponseBody
	public ApiResponse deleteFliterBuynick(@RequestParam(name = "nick") String nick,
			@RequestParam(name = "shopId") String shopId) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			performanceSettingBussiness.deleteByNickAndShopId(nick, Long.valueOf(shopId));
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_04_58.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_04_58.getMsg());
			logger.error(e.getMessage(), e);
		}
		return apiResponse;
	}

	@RequestMapping("querySystemSetForQN")
	@ResponseBody
	public ApiResponse getSystemSettingForQN(@RequestParam(name = "shopId") String shopId) {
		Map<String, Object> resultMap = new HashMap<>();
		ApiResponse apiResponse;
		try {
			ShopSystemsettingDTO shopSystemsettingDTO = performanceSettingBussiness.getShopSystemsettingByShopId(Long.valueOf(shopId));
			AssemblySysData(resultMap, shopSystemsettingDTO);
			List<GoodsFilterDTO> goodsList = performanceSettingBussiness.getPesGoodsByShopId(Long.valueOf(shopId));
			List<String> buyerNickList = performanceSettingBussiness
					.selectFilterBuyerNickLstByShopId(Long.valueOf(shopId));
			resultMap.put("goodsList", goodsList);
			resultMap.put("buyerNickList", buyerNickList);
			resultMap.put("shopSystemsettingDTO",shopSystemsettingDTO);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001.getCode(),
					ApiCodeEnum.CODE_SUCCESS_1001.getMsg(), resultMap);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_04_59.getCode(),
					ApiCodeEnum.CODE_ERROR_XS_04_59.getMsg());
			logger.error(e.getMessage(), e);
		}

		return apiResponse;
	}


	@RequestMapping("batchLikeByShopIdForLst")
	@ResponseBody
	public Object batchLikeByShopIdForLst(@RequestParam(name = "shopId") String shopId,
										  @RequestParam(name = "sensitiveWord") String sensitiveWord) {
		try {
			Map<String,ShopSensitiveWordVO> wordLst= Maps.newHashMap();
			wordLst.put("wordLst", shopSensitiveWordBusiness.batchLikeByShopIdForLst(Long.valueOf(shopId), sensitiveWord));
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(wordLst));
		} catch (Exception e) {
			logger.error("master -> /shop/sysetting/batchLikeByShopIdForLst error:{}", e.getMessage(), e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_03, new RestApiResponse2<>(false));
		}
	}

	@RequestMapping("selectSensitiveWordByShopIdForLst")
	@ResponseBody
	public Object selectSensitiveWordByShopIdForLst(
			@RequestParam(name = "userId") Long userId,
			@RequestParam(name = "shopId") Long shopId) {

		try {
			Map<String,ShopSensitiveWordVO> wordLst= Maps.newHashMap();
			wordLst.put("wordLst",shopSensitiveWordBusiness.selectSensitiveWordByShopIdForLst(shopId,userId));
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(wordLst));
		} catch (Exception e) {
			logger.error("master -> /shop/sysetting/selectSensitiveWordByShopIdForLst error:{}", e.getMessage(), e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_03, new RestApiResponse2<>(false));
		}

	}


	@RequestMapping("insertSensitiveWordByshopId")
	@ResponseBody
	public Object insertSensitiveWordByshopId(
			@RequestParam(name = "userId") Long userId,
			@RequestParam(name = "shopId") Long shopId,
			@RequestParam(name = "record") String record) {
		try {
			String record1 = record.replaceAll("，", ",");
			List<String> sw = Arrays.stream(record1.split(",")).distinct().collect(Collectors.toList());
			if(sw.isEmpty()){
				return apiResponse(ApiCodeEnum.CODE_ERROR_1001, new RestApiResponse2<>(false));
			}
			Map<String, String> map = shopSensitiveWordBusiness.insertSensitiveWordByshopId(shopId, userId, sw);
			if ("false".equals(map.get("result")))
					return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));

			if("true".equals(map.get("result")))
				return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of(Arrays.asList(map)));

			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002,RestApiResponse2.of(Arrays.asList(map)));

		} catch (Exception e) {
			logger.error("master -> /shop/sysetting/insrtSensitiveWordByshopId error:{}", e.getMessage(), e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));
		}

	}


	@RequestMapping("updateOrDeleteSensitiveWordByshopIdAndModifiedIdOrWord")
	@ResponseBody
	public Object updateOrDeleteSensitiveWordByshopIdAndModifiedIdOrWord(
			@RequestParam(name = "userId") String userId,
			@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "updateRecordOdl", required = false) String updateRecordOdl,
			@RequestParam(name = "updateRecordNew", required = false) String updateRecordNew,
			@RequestParam(name = "deleteRecord", required = false) String deleteRecord) {
		try {
			List<String> wordOdl =new ArrayList<>(Arrays.stream(updateRecordOdl.split(",")).collect(Collectors.toSet()));
			List<String> wordNew =new  ArrayList(Arrays.stream(updateRecordNew.split(",")).collect(Collectors.toSet()));
			//修改的敏感词
			Map<String,String> updateRecordMap = Maps.newHashMap();
			for (int i = 0; i <wordNew.size() ; i++) {
				updateRecordMap.put(wordOdl.get(i),wordNew.get(i));
			}

			//删除敏感词
			List<String> deleteRecordLst = Arrays.asList(deleteRecord.split(","));
			int num = shopSensitiveWordBusiness.updateOrDeleteSensitiveWordByshopIdAndModifiedIdOrWord(Long.valueOf(shopId), Long.valueOf(userId), updateRecordMap,deleteRecordLst);
			//修改失败
			if (Objects.equals(-2,num)) {
				return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_05, new RestApiResponse2<>(false));
			}
			// 删除失败
			if (Objects.equals(-3,num)) {
				return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_04, new RestApiResponse2<>(false));
			}

			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, new RestApiResponse2<>(true));

		} catch (Exception e) {
			logger.error("master -> /shop/sysetting/updateSensitiveWordByshopId error:{}", e.getMessage(), e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));
		}

	}

	@RequestMapping("updateSensitiveWordByshopId")
	@ResponseBody
	public Object updateSensitiveWordByshopId(
			@RequestParam(name = "userId") String userId,
			@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "recordOdl") String recordOdl,
			@RequestParam(name = "recordNew") String recordNew) {
		try {
			List<String> wordOdl =new ArrayList<>(Arrays.stream(recordOdl.split(",")).collect(Collectors.toSet()));
			List<String> wordNew =new  ArrayList(Arrays.stream(recordNew.split(",")).collect(Collectors.toSet()));
			Map<String,String> map=new HashMap<>();
			for (int i = 0; i <wordNew.size() ; i++) {
				map.put(wordOdl.get(i),wordNew.get(i));
			}
			int num = shopSensitiveWordBusiness.updateSensitiveWordByshopId(Long.valueOf(shopId), Long.valueOf(userId), map);
			if (num < 0) {
				return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));
			}
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, new RestApiResponse2<>(true));

		} catch (Exception e) {
			logger.error("master -> /shop/sysetting/updateSensitiveWordByshopId error:{}", e.getMessage(), e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));
		}

	}

	@RequestMapping("deleteSensitiveWordById")
	@ResponseBody
	public Object deleteSensitiveWordById(
			@RequestParam(name = "userId") String userId,
			@RequestParam(name = "shopId") Long shopId,
			@RequestParam(name = "record") String record) {
		List<String> sw = Arrays.asList(record.split(","));
		if (sw.isEmpty()) {
			return apiResponse(ApiCodeEnum.CODE_ERROR_1001, new RestApiResponse2<>(false));
		}
		try {
			int num = shopSensitiveWordBusiness.deleteSensitiveWordById(Long.valueOf(userId),shopId, sw);
			if (num < 0) {
				return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));
			}
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, new RestApiResponse2<>(true));
		} catch (Exception e) {
			logger.error("master -> /shop/sysetting/deleteSensitiveWordById error:{}", e.getMessage(), e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_07_01, new RestApiResponse2<>(false));
		}
	}

	/**
	 * 组装系统设置数据
	 * @throws IllegalAccessException 
	 * @throws InvocationTargetException 
	 * @throws IllegalArgumentException 
	 * @throws SecurityException 
	 * @throws NoSuchMethodException 
	 */
	private Map<String, Object> AssemblySysData(Map<String, Object> resultMap,
												ShopSystemsettingDTO shopSystemsettingDTO) throws IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException {
		resultMap.put("ars",
					  shopSystemsettingDTO.getAutoReplySwitch() != null && shopSystemsettingDTO.getAutoReplySwitch() ?
							  "Y" : "N");
		resultMap.put("arm", shopSystemsettingDTO.getAutoReplyMark());
		resultMap.put("sellAfter", shopSystemsettingDTO.getSellAfter());
		resultMap.put("mwt", shopSystemsettingDTO.getMaxWaitTime());
		resultMap.put("std", shopSystemsettingDTO.getSchedulingTimeDot());
		resultMap.put("jra", shopSystemsettingDTO.getJudgeRuleAscription().toString());
		resultMap.put("jr", shopSystemsettingDTO.getJudgeRule().toString());
		resultMap.put("sas",
					  shopSystemsettingDTO.getSilentAllSwitch() != null && shopSystemsettingDTO.getSilentAllSwitch() ?
							  "Y" : "N");
		resultMap.put("saft", shopSystemsettingDTO.getSilentAllFollowUpTime());
		resultMap.put("bso",
					  shopSystemsettingDTO.getIsBindSilentAllOrder() != null && shopSystemsettingDTO.getIsBindSilentAllOrder() ? "Y" : "N");
		resultMap.put("mrn", shopSystemsettingDTO.getSigchatMinReplyNum());
		resultMap.put("afus",
					  shopSystemsettingDTO.getActiveFollowUpSwitch() != null && shopSystemsettingDTO.getActiveFollowUpSwitch() ? "Y" : "N");
		resultMap.put("sus",
					  shopSystemsettingDTO.getSilentUrgepaySwitch() != null && shopSystemsettingDTO.getSilentUrgepaySwitch() ? "Y" : "N");
		resultMap.put("sut", shopSystemsettingDTO.getSilentUrgepayTime());
		resultMap.put("cws",
					  shopSystemsettingDTO.getCsWatchwordSwitch() != null && shopSystemsettingDTO.getCsWatchwordSwitch() ? "Y" : "N");
		resultMap.put("cw", shopSystemsettingDTO.getCsWatchword());
		resultMap.put("cscf",
					  shopSystemsettingDTO.getCsSingleChatFilter() != null && shopSystemsettingDTO.getCsSingleChatFilter() ? "Y" : "N");
		resultMap.put("scn", shopSystemsettingDTO.getCustSingleChatNum());
		resultMap.put("saf",
					  shopSystemsettingDTO.getAftersellAcountFilter() != null && shopSystemsettingDTO.getAftersellAcountFilter() ? "Y" : "N");
		resultMap.put("srt", shopSystemsettingDTO.getSlowResponseTime());
		resultMap.put("srn", shopSystemsettingDTO.getSlowResponseTimesNum());
		resultMap.put("qrt", shopSystemsettingDTO.getQuickResponseTime());
		resultMap.put("lrt", shopSystemsettingDTO.getLongReceptionTime());
		resultMap.put("crs",
					  shopSystemsettingDTO.getCsRecommendSwitch() != null && shopSystemsettingDTO.getCsRecommendSwitch() ? "Y" : "N");
		resultMap.put("crt", shopSystemsettingDTO.getCsRecommendMark());
		resultMap.put("dru", shopSystemsettingDTO.getDutyRecordExportUnit());
		resultMap.put("evt", shopSystemsettingDTO.getEnquiryValidDurationTime());
		resultMap.put("ost", shopSystemsettingDTO.getOutStockValidDurationTime());
		resultMap.put("cals",
					  shopSystemsettingDTO.getCsToCsutFirstLostSwitch() != null && shopSystemsettingDTO.getCsToCsutFirstLostSwitch() ? "Y" : "N");
		resultMap.put("brd", shopSystemsettingDTO.getCustFirstReplyDay());
		resultMap.put("cdls",
					  shopSystemsettingDTO.getEnquiryLossSwitch() != null && shopSystemsettingDTO.getEnquiryLossSwitch() ? "Y" : "N");
		resultMap.put("bs",
					  shopSystemsettingDTO.getOrderFlagSwitch() != null && shopSystemsettingDTO.getOrderFlagSwitch() ?
							  "Y" : "N");
		resultMap.put("bf", shopSystemsettingDTO.getOrderFlag());
		resultMap.put("fnc",
					  shopSystemsettingDTO.getNonChatFilteSwitch() != null && shopSystemsettingDTO.getNonChatFilteSwitch() ? "Y" : "N");
		resultMap.put("cof",
					  shopSystemsettingDTO.getCsOfflineFilteCustMsgSwitch() != null && shopSystemsettingDTO.getCsOfflineFilteCustMsgSwitch() ? "Y" : "N");
		resultMap.put("cfs",
					  shopSystemsettingDTO.getCsForwardSwitch() != null && shopSystemsettingDTO.getCsForwardSwitch() ?
							  "Y" : "N");
		resultMap.put("cfn", shopSystemsettingDTO.getCsForwardNum());
		resultMap.put("mrs",
					  shopSystemsettingDTO.getMainAccountAutoReplySwitch() != null && shopSystemsettingDTO.getMainAccountAutoReplySwitch() ? "Y" : "N");
		resultMap.put("mrc", shopSystemsettingDTO.getMainAccountAutoReplyContent());
		resultMap.put("bss",
					  shopSystemsettingDTO.getCustSigchatSwitch() != null && shopSystemsettingDTO.getCustSigchatSwitch() ? "Y" : "N");
		resultMap.put("jcf",
					  shopSystemsettingDTO.getPlatformCsFilteSwitch() != null && shopSystemsettingDTO.getPlatformCsFilteSwitch() ? "Y" : "N");
		resultMap.put("cwsn", shopSystemsettingDTO.getCsWatchwordSendTimesNum());
		resultMap.put("openWarn", shopSystemsettingDTO.getOpenWarn());
		resultMap.put("warnCs", shopSystemsettingDTO.getWarnAcceptCs());
		resultMap.put("cfas",shopSystemsettingDTO.getCsForwardAftersellSwitch() !=null?shopSystemsettingDTO.getCsForwardAftersellSwitch():false);
		resultMap.put("pcps",
					  shopSystemsettingDTO.getPoolConvertedNoPaySwitch() != null && shopSystemsettingDTO.getPoolConvertedNoPaySwitch() ? "Y" : "N");
		resultMap.put("rgms",
					  shopSystemsettingDTO.getRtpfRidCsSwitch() != null && shopSystemsettingDTO.getRtpfRidCsSwitch() ?
							  "Y" : "N");
		resultMap.put("rgmv", shopSystemsettingDTO.getRtpfRidCsRate() != null ?
				shopSystemsettingDTO.getRtpfRidCsRate() : 0);
		resultMap.put("tcus",
					  shopSystemsettingDTO.getTaskCountUrgePaySwitch() != null && shopSystemsettingDTO.getTaskCountUrgePaySwitch() ? "Y" : "N");
		resultMap.put("drcs",
					  shopSystemsettingDTO.getDutyRidCsSwitch() != null && shopSystemsettingDTO.getDutyRidCsSwitch() ?
							  "Y" : "N");
		return resultMap;
	}
	/**
	 * 实时绩效设置
	 */
	@RequestMapping("realTimePerformanceSettings")
	@ResponseBody
	public ApiResponse realTimePerformanceSettings(
			@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "param") String paramStr) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			RealTimePerformanceSettingsParam param = JacksonUtils.json2pojo(paramStr, RealTimePerformanceSettingsParam.class);
			shopSystemsettingBusiness.updateRtpfSettings(Long.valueOf(shopId), param);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_RT_01_01.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_RT_01_01.getMsg());
			logger.error(e.getMessage(), e);
		}
		return apiResponse;
	}
	
	/**
	 * 实时绩效设置查询
	 * @param shopId
	 * @return
	 */
	@RequestMapping("selectRealTimePerformanceSettings")
	@ResponseBody
	public ApiResponse selectRealTimePerformanceSettings(
			@RequestParam(name = "shopId") String shopId) {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {
			ShopSystemsettingDTO shopSystemsettingDTO = shopSystemsettingBusiness.selectRealTimePerformanceSettings(Long.valueOf(shopId));
			data.put("shopSystemsettingDTO", shopSystemsettingDTO);
			apiResponse.setData(data);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		} catch (Exception e) {
			apiResponse.setData(data);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_RT_01_02.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_RT_01_02.getMsg());
			logger.error(e.getMessage(), e);
		}
		return apiResponse;
	}

}
