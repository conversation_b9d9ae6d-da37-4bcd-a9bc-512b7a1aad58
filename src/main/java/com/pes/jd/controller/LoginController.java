package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.LoginBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.data.converter.ShopInfoDateConverter;
import com.pes.jd.exception.ApiInvokException;
import com.pes.jd.model.BO.LoginResultBO;
import com.pes.jd.model.DO.ShopInfoDO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.LoginUserParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.ms.utils.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/login")
public class LoginController extends BaseController {
	@Autowired
	private LoginBusiness loginBusiness;
	@Autowired
	private ShopInfoDateConverter shopInfoDateConverter;

	private final Logger logger = LoggerFactory.getLogger(LoginController.class);
	
	@RequestMapping("/commonLogin")
	public ApiResponse login(
			@RequestParam(name = "userParam") String userParamJson,
			@RequestParam(name = "shopInfo") String shopInfoJson,
			@RequestParam(name = "switchFlag") String switchFlag,
			@RequestParam(name = "flag") Integer flag) {
		ApiResponse apiResponse=new ApiResponse();
		LoginResultBO loginResult;
		ShopInfoDO shopInfo;
		Map<String, Object> result=Maps.newHashMap();
		LoginUserParam userParam;
		try {
			shopInfo = JacksonUtils.json2pojo(shopInfoJson, ShopInfoDO.class);
			userParam = JacksonUtils.json2pojo(userParamJson, LoginUserParam.class);
		} catch (Exception e1) {
			logger.error("commonLogin parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
		}
        long s1=System.currentTimeMillis();
		try {

			loginResult=loginBusiness.commonLogin(userParam, switchFlag, shopInfo,flag);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
			result.put("retCode", loginResult.getRetCode());
			result.put("msg", loginResult.getErrMsg());
			result.put("sessionMap", loginResult.getSessionMap());
		} catch (Exception e) {
			result.put("retCode", 1);
			result.put("sessionMap",result);
			result.put("msg","登录失败");
			logger.error(e.getMessage(),e);
		}
		logger.info("pin:{} master commonLogin time:{} ms",userParam.getPin(),System.currentTimeMillis()-s1);
		apiResponse.setData(result);
		return	apiResponse;
	}

	@RequestMapping("/csmLogin")
	public RestResponseTypeRef csmLogin(
			@RequestParam(name="userParam")String userParamJson) {
		LoginResultBO loginResult;
		Map<String, Object> result=Maps.newHashMap();
		LoginUserParam userParam;
		try {
			userParam = JacksonUtils.json2pojo(userParamJson, LoginUserParam.class);
		} catch (Exception e1) {
			logger.error("csmLogin parse error:{}",e1.getMessage(),e1);
			return RestResponseTypeRef.ofFail();
		}
		try {

			loginResult=loginBusiness.csmLogin(userParam);

			result.put("retCode", loginResult.getRetCode());
			result.put("msg", loginResult.getErrMsg());
			result.put("sessionMap", loginResult.getSessionMap());

			return RestResponseTypeRef.ofSuccess(result);
		} catch (Exception e) {
			result.put("retCode", 1);
			result.put("sessionMap",result);
			result.put("msg","登录失败");
			logger.error(e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/sgLogin")
	public RestResponseTypeRef sgLogin(
			@RequestParam(name="userParam")String userParamJson) {
		LoginResultBO loginResult=null;
		Map<String, Object> result=Maps.newHashMap();
		LoginUserParam userParam=null;
		try {
			userParam = JacksonUtils.json2pojo(userParamJson, LoginUserParam.class);
		} catch (Exception e1) {
			logger.error("sgLogin parse error:{}",e1.getMessage(),e1);
			return RestResponseTypeRef.ofFail();
		}
		try {

			loginResult=loginBusiness.sgLogin(userParam);

			result.put("retCode", loginResult.getRetCode());
			result.put("msg", loginResult.getErrMsg());
			result.put("sessionMap", loginResult.getSessionMap());

			return RestResponseTypeRef.ofSuccess(result);
		} catch (Exception e) {
			result.put("retCode", 1);
			result.put("sessionMap",result);
			result.put("msg","登录失败");
			logger.error(e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}
	
	@RequestMapping("/getShopInfo")
	public ApiResponse getShopInfo(@RequestParam(name="userNick") String userNick,
			@RequestParam(name="sessionKey") String sessionKey){
		ApiResponse apiResponse=null;
		Map<String, Object> result=Maps.newHashMap();
		ShopInfoDO shopInfoDO = null;
		try {
			shopInfoDO = shopInfoDateConverter.getShopInfo(userNick,sessionKey);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			result.put("shopInfoDO", shopInfoDO);
		} catch (ApiInvokException e) {
			logger.error(e.getErrMsg(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		}
		
		apiResponse.setData(result);
		return	apiResponse;
	}
	@RequestMapping("/loginSuccessInitDate")
	public ApiResponse loginSuccessInitDate(@RequestParam(name="shopId")Long shopId,
			@RequestParam(name="userId")Long userId,
			@RequestParam(name="csNick")String csNick){
		ApiResponse apiResponse=null;
		try {
			
			loginBusiness.loginSuccessInitDate(shopId, userId, csNick);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		} 
		return apiResponse;
	}
	
	@RequestMapping("/getMainShopMemberShop")
	public ApiResponse getMainShopMemberShop(@RequestParam(name="mainShopId")Long mainShopId,
			@RequestParam(name="memberShopId")Long memberShopId){
		ApiResponse apiResponse=null;
		Map<String, Object> result=Maps.newHashMap();
		try {
			ShopDTO selectShop=	loginBusiness.getMainShopMemberShop(mainShopId, memberShopId);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			result.put("selectShop", selectShop);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		} 
		apiResponse.setData(result);
		return apiResponse;
	}
	@RequestMapping("/getUserInfoByUserNick")
	public ApiResponse getUserInfoByUserNick(@RequestParam(name="userNick")String userNick){
		ApiResponse apiResponse=null;
		Map<String, Object> result=Maps.newHashMap();
		try {
			ShopUserDTO user=	loginBusiness.getUserInfoByUserNick(userNick);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			result.put("user", user);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		} 
		apiResponse.setData(result);
		return apiResponse;
	}
	@RequestMapping("/test")
	public ApiResponse login() {
		ApiResponse apiResponse=null;
		LoginResultBO loginResult=null;
		ShopInfoDO shopInfo=new ShopInfoDO();
		shopInfo.setColType(0);
		shopInfo.setShopId(81477L);
		shopInfo.setVenderId(84607L);
		shopInfo.setShopName("武极电脑DIY旗舰店");
		Map<String, Object> result=Maps.newHashMap();
		try {
			LoginUserParam param=new LoginUserParam();
			param.setEndDate(DateUtils.parseYMdHms("2020-02-19 16:15:35"));
			param.setPin("vgame武极-人参果");
			param.setSessionKey("382b8ea0cd2f47a1aa7ee4c5fa4f1f07lytb");
			param.setItemCode("FW_GOODS-908622-4");
			param.setUid("5436996014");
			param.setUserNick("vgame武极-人参果");
			loginResult=loginBusiness.commonLogin(param , "web", shopInfo, CommonConstants.webFlag);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
			result.put("retCode", loginResult.getRetCode());
			result.put("msg", loginResult.getErrMsg());
			result.put("sessionMap", loginResult.getSessionMap());
		} catch (Exception e) {
			result.put("retCode", 1);
			result.put("sessionMap",result);
			result.put("msg","登录失败");
			logger.error(e.getMessage(),e);
		}
		apiResponse.setData(result);
		return	apiResponse;
	}

	@RequestMapping("/mobileLogin")
	public Object mobileLogin(Long shopId,String pin){
		long s1=System.currentTimeMillis();
		LoginResultBO loginResult = null;
		try {
			loginResult= loginBusiness.mobileLogin(shopId,pin);
			logger.info("master mobileLogin pin:{},haoshi time :{}ms",pin ,System.currentTimeMillis()-s1);
			return RestResponseTypeRef.ofSuccess(loginResult);
		} catch (Exception e) {
			loginResult=new LoginResultBO(1);
			logger.error("mobileLogin error msg:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFailWithNextStep(ApiCodeEnum.CODE_SUCCESS_1002.getCode(),ApiCodeEnum.CODE_SUCCESS_1002.getMsg(),loginResult);
		}
	}
}
