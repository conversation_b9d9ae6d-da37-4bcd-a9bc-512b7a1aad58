package com.pes.jd.controller;


import com.alibaba.fastjson.JSONObject;
import com.pes.jd.data.convert.OrderShipOperatorConverter;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/order/ship")
public class OrderShipController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(OrderShipController.class);
	
	
	 @Autowired
	 private OrderShipOperatorConverter orderShipOperatorConverter;
	
	@RequestMapping("/getOrderShipInfo")
	public Object selectCategoryLst(@RequestParam("shopStr") String shopStr,@RequestParam("orderId") String orderId) {
		try {
			ShopCommonParam shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
			//orderShipOperatorConverter.getOrderShip(orderId, shop.getSessionKey());
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, null);
		} catch (Exception e) {
			logger.error("CustomerInfoController.selectCustomerInfo  error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
         }
		
	}
	
	
}
