package com.pes.jd.controller;

import com.pes.jd.business.OrderShipBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;

/**
 * <AUTHOR>
 * @version 2019-06-02 18:42
 */
@RestController
@RequestMapping(value = "/customer")
public class OrderShipController extends  BaseController{
    private static final Logger logger = LoggerFactory.getLogger(OrderShipController.class);


    @Autowired
    private OrderShipBusiness orderShipBusiness;
    
    @RequestMapping(value = "/getOrderShipInfo")
    public ApiResponse getOrderShipInfo(@RequestParam("shopId")String shopId,
                                         @RequestParam("orderId")String orderId) throws ParseException {
        try{
        	ShopQuery shop = this.getSelectShop(shopId);
        	ApiResponse  apiResponse  = orderShipBusiness.getOrderShip(shop,orderId);
        	apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
        	return apiResponse;
        }catch (Exception e){
            logger.error("OrderShipController.getOrderShipInfo error :{}"+e.getMessage(),e);
           return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
    }


}
