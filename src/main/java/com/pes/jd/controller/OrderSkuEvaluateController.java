package com.pes.jd.controller;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.OrderSkuEvaluateBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.DTO.OrderSkuEvaluateDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.OrderSkuEvaluateParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.OrderSkuEvalVO;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/cs/evaluate/")
public class OrderSkuEvaluateController extends BaseController {
	
	private Logger logger = LoggerFactory.getLogger(OrderSkuEvaluateController.class);
	
	@Autowired
	private OrderSkuEvaluateBusiness orderSkuEvaluateBusiness;
	
	@RequestMapping("selectOrderSkuEvaluate")
	public ApiResponse selectOrderSkuEvaluate(
			@RequestParam("shop") String shopStr,
			@RequestParam("param") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		OrderSkuEvaluateParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, OrderSkuEvaluateParam.class);
		} catch (Exception e) {
			logger.error("json转换错误",e.getMessage(),e);
		}
		try {
			List<OrderSkuEvaluateDTO> skuEvaluateList = orderSkuEvaluateBusiness.selectOrderSkuEvaluateByDateByCsNickByScore(shop,param);
			data.put("skuEvaluateList", skuEvaluateList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e1) {
			logger.error(e1.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_FW_01_04);
		}
		
		
	}
	
	
	@RequestMapping("selectOrderSkuEvaluateInfo")
	public ApiResponse selectOrderSkuEvaluateInfo(
			@RequestParam("shop") String shopStr,
			@RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr,
			@RequestParam("id") String idStr,
			@RequestParam("orderId") String orderIdStr) throws ParseException {
		Map<String, Object> data = Maps.newHashMap();
		Integer id = null;
		Long orderId = null;
		if(!Strings.isNullOrEmpty(idStr)){
			id = Integer.parseInt(idStr);
		}
		if(!Strings.isNullOrEmpty(orderIdStr)){
			orderId = Long.valueOf(orderIdStr);
		}

		Date startDate = null;
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		if (StringUtils.isBlank(startDateStr)) {
			startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(endDate, CommonConstants.DAY_MED_BAD_ORDER));
		} else {
			startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		}

		ShopCommonParam shop = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e) {
			logger.error("json转换错误{}",e.getMessage(),e);
		}
		try {
			OrderSkuEvalVO<CsOrderIndexDTO> skuEvaluate = orderSkuEvaluateBusiness.selectEvaluateInfoByOrderIdByBuyer(startDate,endDate,shop,id,orderId);
			data.put("orderSkuEvalVO", skuEvaluate);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e1) {
			logger.error(e1.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_FW_01_05,data);
		}
		
	}
	

}
