package com.pes.jd.controller;


import com.pes.jd.business.TaskRtJobBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.SendMsgResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@SuppressWarnings("Duplicates")
@RestController
@RequestMapping("/task/message/rt/")
public class TaskRtMessageController {
    private static final Logger logger = LoggerFactory.getLogger(TaskRtMessageController.class);

    @Resource
    private TaskRtJobBusiness taskRtJobBusiness;

    /**
     * 计算店铺数据
     *
     * @param shopId(shopId为空,则调用所有店铺)
     * @param handleType
     * @return
     */
    @RequestMapping(value = "invokeTaskRtJob")
    public Object invokeTaskRtJob(
            @RequestParam(required = false) Long shopId,
            @RequestParam(required = false) String handleType,
            @RequestParam(required = false) String rtDb,
            @RequestParam(required = false) String rtSchemaId) {

        try {
            SendMsgResult sendMsgResult = taskRtJobBusiness.invokeTaskRtJob(shopId, handleType, rtDb, rtSchemaId);
            return ApiResponse.ofSuccess("result", sendMsgResult);
        } catch (Exception e) {
            logger.error("invoke task rtjob error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
        }
    }

}
