package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.SlientGoodsSaleIndexBusiness;
import com.pes.jd.business.sub.SlientGoodsSaleIndexDetailBusiness;
import com.pes.jd.model.DTO.SlientGoodsSaleIndexDTO;
import com.pes.jd.model.DTO.SlientGoodsSaleIndexDetailDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/slientGoodsSale/")
public class SlientGoodsSaleController extends BaseController{
	
	private Logger logger = LoggerFactory.getLogger(SlientGoodsSaleController.class);
	
	@Autowired
	private SlientGoodsSaleIndexBusiness goodsSaleIndexBusiness;
	
	@Autowired
	private SlientGoodsSaleIndexDetailBusiness goodsSaleIndexDetailBusiness;
	
	@RequestMapping("selectGoodsSaleIndexDetail")
	public ApiResponse selectGoodsSaleIndexDetail(
			@RequestParam("shop") String shopStr, 
			@RequestParam("param") String paramStr,
			@RequestParam(value = "orderInfoLogUploadParamStr",required = false) String orderInfoLogUploadParamStr,
		    @RequestParam("sortPageQuery") String sortPageQueryStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		SortPageQuery sortPageQuery = null;
		OrderInfoLogUploadParam orderInfoLogUploadParam = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
			orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
		} catch (Exception e1){
			logger.error("json error",e1.getMessage(),e1);
		}
		try {
            DataAnalysisVO<SlientGoodsSaleIndexDetailDTO> silentGoodsSaleDetailVO =
                    goodsSaleIndexDetailBusiness.selectGoodsSaleIndexByDateBySku(shop, param.getStartDate(), param.getEndDate(), param.getSkuLst(), param.getOrderId(), sortPageQuery, orderInfoLogUploadParam);
            data.put("silentGoodsSaleDetailVO", silentGoodsSaleDetailVO);

			//上传参数
//            UploadDBOperationParam uploadParam = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParam);
//            uploadParam.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_SLIENTGOODSSALEINDEXDETAIL.getName());
//            uploadDBOperationBusiness.upload(uploadParam);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("slient goods sale detail error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_02);
		}
	}

    @RequestMapping("selectGoodsSaleIndexDetailOfSpu")
    public ApiResponse selectGoodsSaleIndexDetailOfSpu(
            @RequestParam("shop") String shopStr,
            @RequestParam("param") String paramStr,
            @RequestParam(value = "orderInfoLogUploadParamStr",required = false) String orderInfoLogUploadParamStr,
            @RequestParam("sortPageQuery") String sortPageQueryStr) {
        ShopCommonParam shop = null;
        GoodsConsultParam param = null;
        SortPageQuery sortPageQuery = null;
        OrderInfoLogUploadParam orderInfoLogUploadParam = null;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
            sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
        } catch (Exception e1){
            logger.error("json error【{}】",e1.getMessage(),e1);
        }
        try {
            Map<String, Object> data = Maps.newHashMap();
            DataAnalysisVO<SlientGoodsSaleIndexDetailDTO> silentGoodsSaleDetailVO =
                    goodsSaleIndexDetailBusiness.selectGoodsSaleIndexByDateBySkuOfSpu(shop, param.getStartDate(), param.getEndDate(), param.getSkuLst(), param.getOrderId(), sortPageQuery, orderInfoLogUploadParam);
            data.put("silentGoodsSaleDetailVO", silentGoodsSaleDetailVO);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
        } catch (Exception e) {
            logger.error("slient goods sale detail error=[{}]",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_02);
        }
    }
	
	@RequestMapping("selectGoodsSaleIndex")
	public ApiResponse selectGoodsSaleIndex(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
			List<SlientGoodsSaleIndexDTO> goodsSaleIndexList = goodsSaleIndexBusiness.selectGoodsSaleIndexByDateBySku(shop, param.getStartDate(), param.getEndDate(), param.getSkuLst());
			data.put("goodsSaleIndexList",goodsSaleIndexList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("slient goods sale summary error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_04);
		}
	}
	@RequestMapping("selectGoodsSaleIndexOfSpu")
	public ApiResponse selectGoodsSaleIndexOfSpu(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
//			List<SlientGoodsSaleIndexDTO> goodsSaleIndexList1 = goodsSaleIndexBusiness.selectGoodsSaleIndexByDateBySku(shop, param.getStartDate(), param.getEndDate(), param.getSkuLst());
			List<SlientGoodsSaleIndexDTO> goodsSaleIndexList = goodsSaleIndexBusiness.selectGoodsSaleIndexByDateBySkuOfSpu(shop, param.getStartDate(), param.getEndDate(), param.getSkuLst());
			data.put("goodsSaleIndexList",goodsSaleIndexList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("slient goods sale summary OfSpu error={}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_04);
		}
	}


}
