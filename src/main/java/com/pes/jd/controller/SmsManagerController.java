package com.pes.jd.controller;

import com.pes.jd.business.main.SmsManagerBusiness;
import com.pes.jd.model.DTO.ShopSmsWordDTO;
import com.pes.jd.model.DTO.SmsOrderDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.SmsServiceVO;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/sms/*")
public class SmsManagerController extends BaseController {
    private Logger logger = LoggerFactory.getLogger(SmsManagerController.class);
    @Resource
    private SmsManagerBusiness smsManagerBusiness;
    // 根据nick,orderId,payWay模糊查询短信充值记录
    @RequestMapping("queryRechargeRecord")
    @ResponseBody
    public ApiResponse queryRechargeRecord(@RequestParam(value = "startDate") String startDateStr,
                                           @RequestParam(value = "endDate") String endDateStr,
                                           @RequestParam(value = "nick", required = false) String nick,
                                           @RequestParam(value = "orderId", required = false) String orderId,
                                           @RequestParam(value = "payWay") Integer payWay,
                                           @RequestParam(value = "orderStatus") Integer orderStatus) {
        ApiResponse apiResponse = new ApiResponse();
        HashMap<String, Object> retMap = new HashMap<>();
        Date startDate;
        Date endDate;
        try {
            Date startDateTemp = DateUtils.parseYMd(startDateStr);
            startDate = DateUtils.getStartTimeOfDate(startDateTemp);
            Date endDateTemp = DateUtils.parseYMd(endDateStr);
            endDate = DateUtils.getEndTimeOfDate(endDateTemp);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_1001.getMsg(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
        try {
            List<SmsOrderDTO> shops = smsManagerBusiness.queryRechargeRecord(startDate, endDate, nick, orderId, payWay, orderStatus);
            retMap.put("rechargeRecordList", shops);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
            apiResponse.setData(retMap);
            return apiResponse;
        } catch (Exception e) {
            logger.error("查询短信充值记录error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_01);
        }
    }

    @RequestMapping("recharge")
    @ResponseBody
    public ApiResponse recharge(@RequestParam(value = "nick") String nick,
                                @RequestParam(value = "shopId") Long shopId,
                                @RequestParam(value = "number") Integer number,
                                @RequestParam(value = "orderFee") Double orderFee) {
        try {
            Boolean result = smsManagerBusiness.recharge(nick, shopId, number, orderFee);
            if (result) {
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
            }
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_05);
        } catch (Exception e) {
            logger.error("短信充值error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_02);
        }
    }

    @RequestMapping("queryShopSmsCount")
    @ResponseBody
    public ApiResponse queryShopSmsRemainingCount(@RequestParam(value = "shopId") Long shopId) {
        try {
            Map<String, Object> data = smsManagerBusiness.queryShopSmsRemainingCount(shopId);

            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
            of.setData(data);

            return of;
        } catch (Exception e) {
            logger.error("店铺短信余量查询 error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_06);
        }
    }

    @RequestMapping("querySmsBalanceCount")
    @ResponseBody
    public ApiResponse querySmsBalanceCount() {
        try {
            Map<String, Object> data = smsManagerBusiness.querySmsBalanceCount();
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
            of.setData(data);
            return of;
        } catch (Exception e) {
            logger.error("短信余量查询 error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_06);
        }
    }

    // 根据nick 模糊查询短信话术
    @RequestMapping("selectSmsWordByNick")
    @ResponseBody
    public ApiResponse selectSmsWordByNick(@RequestParam(value = "nick", required = false) String nick) {
        ApiResponse apiResponse = new ApiResponse();
        HashMap<String, Object> retMap = new HashMap<>();
        try {
            List<ShopSmsWordDTO> shops = smsManagerBusiness.selectSmsWordByNick(nick);
            retMap.put("smsWordList", shops);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
            apiResponse.setData(retMap);
            return apiResponse;
        } catch (Exception e) {
            logger.error("查询短信话术 error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_03);
        }
    }

    // 查询短信话术未审核数量
    @RequestMapping("queryNotAiditSmsWordCount")
    @ResponseBody
    public ApiResponse queryNotAiditSmsWordCount() {
        ApiResponse apiResponse = new ApiResponse();
        HashMap<String, Object> retMap = new HashMap<>();
        try {
            int count = smsManagerBusiness.queryNotAiditSmsWordCount();
            retMap.put("count", count);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
            apiResponse.setData(retMap);
            return apiResponse;
        } catch (Exception e) {
            logger.error("查询短信话术未审核数量 error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_03);
        }
    }

    @RequestMapping("auditSmsWord")
    @ResponseBody
    public ApiResponse auditSmsWord(@RequestParam(value = "id") Long id,
                                    @RequestParam(value = "auditRemark") String auditRemark,
                                    @RequestParam(value = "auditStutas") Integer auditStutas) {
        try {
            smsManagerBusiness.auditSmsWord(id, auditRemark, auditStutas);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            logger.error("审核短信话术error", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_04);
        }
    }

    /**
     * 根据店铺id查询所有的用户的短信余量和
     *
     * @param shopId
     * @return
     */
    @RequestMapping("queryShopSmsCountAndPrice")
    @ResponseBody
    public ApiResponse queryShopSmsCountAndPrice(@RequestParam(value = "shopId") Long shopId) {
        try {

            Assert.notNull(shopId, ApiCodeEnum.CODE_ERROR_1001.getMsg());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
        try {
            Map<String, Object> data = new HashMap<>();
            SmsServiceVO countAndPrice = smsManagerBusiness.queryShopSmsCountAndPrice(shopId);
            List<SmsOrderDTO> smsOrderLst = smsManagerBusiness.selectSmsOrderByShopId(shopId);
            //字段汇总
            SmsOrderDTO smsOrderSummary = this.summaryField(smsOrderLst);
            data.put("countAndPrice", countAndPrice);
            data.put("smsOrderLst", smsOrderLst);
            data.put("smsOrderSummary", smsOrderSummary);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
        } catch (Exception e) {
            logger.error("<<短信服务>>套餐查询error {}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SR_01_06);
        }
    }

    /*
    字段汇总
     */
    private SmsOrderDTO summaryField(List<SmsOrderDTO> smsOrderLst) {
        SmsOrderDTO smsOrderDTO = new SmsOrderDTO();
        if (CollectionUtils.isEmpty(smsOrderLst)) {
            return smsOrderDTO;
        }
        smsOrderDTO.setNumber(smsOrderLst.stream().mapToInt(SmsOrderDTO::getNumber).sum());
        smsOrderDTO.setOrderFee(smsOrderLst.stream().mapToDouble(SmsOrderDTO::getOrderFee).sum());
        return smsOrderDTO;
    }

}
