package com.pes.jd.controller;

import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.business.SmsManagerBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@RequestMapping("/sms/*")
public class SmsManagerController extends BaseController{
	
	private static final Logger logger = LoggerFactory.getLogger(SmsManagerController.class);
	
	@Autowired
	private SmsManagerBusiness smsManagerBusiness;

	@Autowired
	private ShopSysManagerBusiness shopSysManagerBusiness;
	

	@RequestMapping(value = "selectRechargeRecord")
	@ResponseBody
	public ApiResponse selectRechargeRecord(@RequestParam(value = "startDate") String startDate,
										@RequestParam(value = "endDate") String endDate,
										@RequestParam(value = "nick",required = false) String nick,
										@RequestParam(value = "orderId",required = false) String orderId,
										@RequestParam(value = "payWay") Integer payWay,
										@RequestParam(value = "orderStatus") Integer orderStatus){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.queryRechargeRecord(startDate, endDate, nick, orderId, payWay, orderStatus);
		} catch (Exception e) {
			logger.error("select sms Recharge Record : error", e.getMessage());
		}
		return apiResponse;
	}

	@RequestMapping(value = "checkShopInfo")
	@ResponseBody
	public ApiResponse checkShopInfo(@RequestParam(value = "nick") String nick,
								@RequestParam(value = "shopId") String shopId){
		try {
			ShopQuery shopInfo = shopSysManagerBusiness.getShopInfo(Long.valueOf(shopId));
			if(shopInfo == null){
				ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1005);
				of.setSuccess(Boolean.FALSE);
				return of;
			}
			if(!shopInfo.getTitle().equals(nick)){
				ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1004);
				of.setSuccess(Boolean.FALSE);
				return of;
			}
		} catch (Exception e) {
			logger.error("checkShopInfo : error", e.getMessage());

			ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
			of.setSuccess(Boolean.FALSE);
			return of;
		}
		ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1004);
		of.setSuccess(Boolean.TRUE);
		return of;
	}

	@RequestMapping(value = "queryShopSmsCount")
	@ResponseBody
	public ApiResponse queryShopSmsCount(@RequestParam(value = "shopId") Long shopId){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.queryShopSmsCount(shopId);
		} catch (Exception e) {
			logger.error("checkShopInfo : error", e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1004);
		}
		return apiResponse;
	}

	@RequestMapping(value = "querySmsBalanceCount")
	@ResponseBody
	public ApiResponse querySmsBalanceCount(){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.querySmsBalanceCount();
		} catch (Exception e) {
			logger.error("checkShopInfo : error", e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1004);
		}
		return apiResponse;
	}

	@RequestMapping(value = "recharge")
	@ResponseBody
	public ApiResponse recharge(@RequestParam(value = "nick") String nick,
							   	@RequestParam(value = "shopId") Long shopId,
							   	@RequestParam(value = "number") Integer number,
								@RequestParam(value = "orderFee") Double orderFee){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.recharge(nick, shopId, number, orderFee);
		} catch (Exception e) {
			logger.error("sms recharge : error", e.getMessage());
		}
		return apiResponse;
	}

	@RequestMapping(value = "selectSmsWordByNick")
	@ResponseBody
	public ApiResponse selectSmsWordByNick(@RequestParam(value = "nick",required = false) String nick){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.selectSmsWordByNick(nick);
		} catch (Exception e) {
			logger.error("select Sms Word : error", e.getMessage());
		}
		return apiResponse;
	}

	@RequestMapping(value = "queryNotAiditSmsWordCount")
	@ResponseBody
	public ApiResponse queryNotAiditSmsWordCount(){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.queryNotAiditSmsWordCount();
		} catch (Exception e) {
			logger.error("select not audit Sms Word count : error", e.getMessage());
		}
		return apiResponse;
	}

	@RequestMapping(value = "auditSmsWord")
	@ResponseBody
	public ApiResponse auditSmsWord(@RequestParam(value = "id") Long id,
									 @RequestParam(value = "auditRemark") String auditRemark,
									 @RequestParam(value = "auditStutas") Integer auditStutas){
		ApiResponse apiResponse = new ApiResponse();
		try {
			apiResponse = smsManagerBusiness.auditSmsWord(id,auditRemark,auditStutas);
		} catch (Exception e) {
			logger.error("audit Sms Word : error", e.getMessage());
		}
		return apiResponse;
	}

	@RequestMapping(value = "searchMsgSendInfo")
	@ResponseBody
	public ApiResponse searchMsgSendInfoByShopIdAndTimeAndBuyNickAndStatus(@RequestParam(value = "shopId") String shopId,
																		   @RequestParam(value = "startDate") String startDate,
																		   @RequestParam(value = "endDate") String endDate,
																		   @RequestParam(value = "buyerNick",required = false) String buyerNick,
																		   @RequestParam(value = "status") Integer status){
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopQuery shop = shopSysManagerBusiness.getShopInfo(Long.valueOf(shopId));
			apiResponse = smsManagerBusiness.searchMsgSendInfoByShopIdAndTimeAndBuyNickAndStatus(shop,startDate,endDate,buyerNick,status);
		} catch (Exception e) {
			logger.error("search Send Msg Info : error", e.getMessage());
		}
		return apiResponse;
	}

	@RequestMapping(value = "queryShopSmsCountAndPrice")
	@ResponseBody
	public ApiResponse queryShopSmsCountAndPrice(@RequestParam(value = "shopId") Long shopId) {
		try {
            if (logger.isDebugEnabled()) {
                logger.info("shopId={}",shopId);
            }
			Assert.notNull(shopId, ApiCodeEnum.CODE_ERROR_1001.getMsg());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
		}
		try {
			return smsManagerBusiness.queryShopSmsCountAndPrice(shopId);
		} catch (Exception e) {
			logger.error("queryShopSmsCountAndPrice : error", e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1004);
		}
	}
}
  
