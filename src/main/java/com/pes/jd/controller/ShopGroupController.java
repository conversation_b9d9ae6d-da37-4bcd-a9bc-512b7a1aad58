package com.pes.jd.controller;

import com.pes.jd.business.main.ShopGroupBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.ShopGroupDTO;
import com.pes.jd.model.DTO.ShopGroupRequestDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/shopGroup/*")
public class ShopGroupController extends BaseController {
	private Logger logger = LoggerFactory.getLogger(ShopGroupController.class);
	@Autowired
	private ShopGroupBusiness shopGroupBusiness;
	/**
	 * 多店铺开关：是否显示多店铺
	 *
	 */
	@RequestMapping("showShops")
	public ApiResponse showShops(@RequestParam(name = "currentUser") String currentUser) {
		ApiResponse apiResponse;
		try {
			ShopUserDTO currentShopUser = JacksonUtils.json2pojo(currentUser, ShopUserDTO.class);
			if(currentShopUser == null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_29);
			}
			int updateNum = shopGroupBusiness.updateShowMultiShopStatus(currentShopUser.getShopId(),currentShopUser.getUserId(),currentShopUser.getSellerId());
			if(updateNum>0){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_28);
			}
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_28);
			logger.error("showShops : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}
	
	/**
	 * 多店铺：查询当前店铺下的店铺组信息
	 *
	 */
	@RequestMapping("selectShopGroupsOfOwner")
	public ApiResponse selectShopGroupsOfOwner(@RequestParam(name = "mainShopId") String mainShopId) {
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try {
			if(StringUtils.isNotBlank(mainShopId)){
				List<ShopGroupDTO> retShopGroupList = shopGroupBusiness.selectShopGroupsByOwnerShopId(Long.parseLong(mainShopId));
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
				retMap.put("retShopGroupList", retShopGroupList);
				apiResponse.setData(retMap);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_01);
			logger.error("selectShopGroupsOfOwner : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：创建店铺组
	 *
	 */
	@RequestMapping("createNewShopGroup")
	public ApiResponse createNewShopGroup(@RequestParam(name = "mainShopId") String mainShopId,
			@RequestParam(name = "shopGroupName") String shopGroupName) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isNotBlank(mainShopId)){
				apiResponse = shopGroupBusiness.addShopGroupByGroupNameAndOwner(Long.parseLong(mainShopId), shopGroupName);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_03);
			logger.error("createNewShopGroup : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：修改店铺组名称
	 *
	 */
	@RequestMapping("updateShopGroupName")
	public ApiResponse updateShopGroupName(@RequestParam(name = "mainShopId") String mainShopId,
			@RequestParam(name = "shopGroupId") String shopGroupId,
			@RequestParam(name = "shopGroupName") String shopGroupName) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isNotBlank(mainShopId)){
				apiResponse = shopGroupBusiness.updateShopGroupNameByShopGroupId(Long.parseLong(mainShopId), shopGroupId,shopGroupName);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_05);
			logger.error("updateShopGroupName : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：组内成员互看开关
	 *
	 */
	@RequestMapping("updateShopGroupMutualWatch")
	public ApiResponse updateShopGroupMutualWatch(@RequestParam(name = "shopGroupId") String shopGroupId,
										   @RequestParam(name = "mutualWatch") Boolean mutualWatch) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isNotBlank(shopGroupId) && mutualWatch!=null){
				apiResponse = shopGroupBusiness.updateShopGroupMutualWatch(shopGroupId,mutualWatch);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_31);
			logger.error("updateShopGroupName : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：删除店铺组
	 *
	 */
	@RequestMapping("deleteShopGroup")
	public ApiResponse deleteShopGroup(@RequestParam(name = "mainShopId") String mainShopId,
			@RequestParam(name = "shopGroupId") String shopGroupId) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isBlank(mainShopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
			// 查询是否存在为授权店铺或者已邀请，未授权的店铺邀请记录
			int isExistShopType = shopGroupBusiness.selectShopGroupRequestsByGroupId(Long.parseLong(mainShopId), shopGroupId);
			if (isExistShopType == 1) {
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_10);
			} else if (isExistShopType == 2) {
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_11);

			} else {
				// 空店铺组，执行删除操作
				Boolean deleteStatus = shopGroupBusiness.deleteShopGroupByShopGroupId(shopGroupId);
				if (deleteStatus) {
					apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
				} else {
					apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_08);
				}
			}
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_09);
			logger.error("deleteShopGroup : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：邀请店铺
	 *
	 */
	@RequestMapping("sendInviteRequestForShopGroup")
	public ApiResponse sendInviteRequestForShopGroup(
			@RequestParam(name = "mainShopId") String mainShopId,
			@RequestParam(name = "inviteShopInfo") String inviteShopInfo,
			@RequestParam(name = "shopGroupId") String shopGroupId) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isBlank(mainShopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
			apiResponse = shopGroupBusiness.addShopGroupRequest(Long.parseLong(mainShopId), inviteShopInfo, shopGroupId);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_15);
			logger.error("sendInviteRequestForShopGroup : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：移动店铺
	 *
	 */
	@RequestMapping("moveShopToShopGroup")
	public ApiResponse moveShopToShopGroup(@RequestParam(name = "mainShopId") String mainShopId,
			@RequestParam(name = "moveShopIds") String moveShopIds,
			@RequestParam(name = "shopGroupId") String shopGroupId) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isBlank(mainShopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
			apiResponse = shopGroupBusiness.moveShopToShopGroup(Long.parseLong(mainShopId), moveShopIds, shopGroupId);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_17);
			logger.error("moveShopToShopGroup : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}
	
	/**
	 * 多店铺：同步
	 *
	 *	同步后的绩效重算未做 没有接口
	 */
	@RequestMapping("shopGroupSystemSettingSynchronization")
	public ApiResponse shopGroupSystemSettingSynchronization(
			@RequestParam(name = "fromShopId") String fromShopId,
			@RequestParam(name = "toShopIds") String toShopIds,
			@RequestParam(name = "syncType") String syncType) {
		ApiResponse apiResponse;
		try {
			apiResponse = shopGroupBusiness.shopGroupSystemSettingSynchronization(fromShopId, toShopIds, syncType);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_19);
			logger.error("shopGroupSystemSettingSynchronization : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：查询店铺组详情
	 *
	 */
	@RequestMapping("selectJoinedMyGroupShopList")
	public ApiResponse selectJoinedMyGroupShopList(@RequestParam(name = "mainShopId") String mainShopId,
			@RequestParam(name = "shopGroupId") String shopGroupId) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isBlank(mainShopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_21);
			}
			apiResponse = shopGroupBusiness.selectJoinedMyGroupShopList(Long.parseLong(mainShopId), shopGroupId);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_21);
			logger.error("selectJoinedMyGroupShopList : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：查询我加入的店铺组
	 *
	 */
	@RequestMapping("selectJoinGroupList")
	public ApiResponse selectJoinGroupList(@RequestParam(name = "mainShopId") String mainShopId) {
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try {
			if(StringUtils.isBlank(mainShopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
			List<ShopGroupRequestDTO> retShopGroupRequestList = shopGroupBusiness.selectJoinGroupList(Long.parseLong(mainShopId),
					CommonConstants.SHOP_GROUP_REQUEST_STATUS_INVITE_ACCEPTED);
			if (retShopGroupRequestList == null) {
				retShopGroupRequestList = new ArrayList<>(0);
			}
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shopGroupList", retShopGroupRequestList);
			apiResponse.setData(retMap);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_23);
			logger.error("selectJoinGroupList : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：查询店铺授权列表
	 *
	 */
	@RequestMapping("selectShopAccredit")
	public ApiResponse selectShopAccredit(@RequestParam(name = "mainShopId") String mainShopId) {
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try {
			if(StringUtils.isBlank(mainShopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_30);
			}
			List<ShopGroupRequestDTO> retShopAccreditList = shopGroupBusiness.selectShopAccreditByMainShop(Long.parseLong(mainShopId));
			if (retShopAccreditList == null) {
				retShopAccreditList = new ArrayList<>(0);
			}
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shopAccreditList", retShopAccreditList);
			apiResponse.setData(retMap);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_24);
			logger.error("selectShopAccredit : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}

	/**
	 * 多店铺：店铺授权报表操作（接受，拒绝等）
	 *
	 */
	@RequestMapping("updateAuthorizeShopStatus")
	public ApiResponse updateAuthorizeShopStatus(@RequestParam(name = "status") Integer status,
			@RequestParam(name = "requestId") String requestId,
			@RequestParam(name = "mainShopId",required = false) Long mainShopId) {
		ApiResponse apiResponse;
		try {
            apiResponse = shopGroupBusiness.updateAuthorizeShopStatus(status, requestId, mainShopId);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_06_25);
			logger.error("updateAuthorizeShopStatus : ", e);
			e.printStackTrace();
		}
		return apiResponse;
	}
}
