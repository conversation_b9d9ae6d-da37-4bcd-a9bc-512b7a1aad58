package com.pes.jd.controller;

import com.google.common.base.Strings;
import com.pes.jd.business.AssistServiceAnalysisBusiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.SecurityMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/data-analysis/assistService")
public class AssistServiceAnalysisController extends BaseController {

	private static final Logger logger = LoggerFactory.getLogger(ReceiveDataAnalysisController.class);

	@Autowired
	private AssistServiceAnalysisBusiness assistServiceAnalysisBusiness;

	@Autowired
	private ShopSysManagerBusiness shopSysManagerBusiness;

	/**
	 * 协助服务分析
	 */
	@RequestMapping(value = "/selectCsOrderIndex", method = RequestMethod.POST)
	public ApiResponse selectCsOrderIndex(String shopId, String csNick, String groupId, String buyerNick,
			@RequestParam("assistType") String assistTypeStr, 
			@RequestParam("orderId") String orderIdStr, String startDate, String endDate, String sortDirection,
			String propertity, Long currentPage, Long size,
										  HttpServletRequest request) throws Exception {
		ApiResponse apiResponse = null;
		Long orderId = null;
		Integer assistType = null;
		if (!Strings.isNullOrEmpty(assistTypeStr)) {
			assistType = Integer.parseInt(assistTypeStr);
		}
		if (!Strings.isNullOrEmpty(orderIdStr)) {
			orderId = Long.valueOf(orderIdStr);
		}
		UserShopQuery userShopQuery = this.getCustUserByParam(shopId);
		ShopDTO currentShop = this.getCurrentShop();
		ShopUserDTO currentUser = this.getCurrentUser();
		Object deviceId = this.getDeviceId();

		List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(userShopQuery, groupId,
				csNick);
		String csNicks = String.join(",", csNickList);
		AssistServiceQuery assistServiceQuery = new AssistServiceQuery();
		assistServiceQuery.setAssistType(assistType);
		assistServiceQuery.setCustomerId(buyerNick);
		assistServiceQuery.setOrderId(orderId);
		SortPageQuery sortPageQuery = new SortPageQuery();
		sortPageQuery.setPropertity(propertity);
		sortPageQuery.setSortDirection(sortDirection);
		sortPageQuery.setCurrentPage(currentPage);
		sortPageQuery.setSize(size);
		try {
			//封装上报订单日志需要的参数
			OrderInfoLogUploadParam orderInfoLogUploadParam = initOrderInfoLogUploadParam(request, currentShop, currentUser, deviceId);
			apiResponse = assistServiceAnalysisBusiness.searchCsOrderIndex(userShopQuery, csNicks,
					startDate, endDate, assistServiceQuery, sortPageQuery, orderInfoLogUploadParam);
		} catch (Exception e) {
			logger.error("cs assist service error", e.getMessage(),e);
		}
		return apiResponse;
	}

	private OrderInfoLogUploadParam initOrderInfoLogUploadParam(HttpServletRequest request, ShopDTO currentShop, ShopUserDTO currentUser, Object deviceId) {
		OrderInfoLogUploadParam orderInfoLogUploadParam = new OrderInfoLogUploadParam();
		orderInfoLogUploadParam.setJdId(currentShop.getTitle());
		orderInfoLogUploadParam.setDeviceId((String) deviceId);
		orderInfoLogUploadParam.setUserId(currentUser.getNick());
		orderInfoLogUploadParam.setUserIp(SecurityMUtil.getIpAddr(request));
		orderInfoLogUploadParam.setTimeStamp(System.currentTimeMillis());
		return orderInfoLogUploadParam;
	}

}
