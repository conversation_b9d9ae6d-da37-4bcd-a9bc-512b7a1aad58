package com.pes.jd.controller;

import org.springframework.boot.autoconfigure.web.ErrorController;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

//@Controller
public class ErrorPageController implements ErrorController {


    @Override
    public String getErrorPath() {
        return null;
    }

    private static final String ERROR_PATH = "/error";

    @RequestMapping(value=ERROR_PATH,produces = {MediaType.TEXT_HTML_VALUE})
    public String handleError(HttpServletRequest request){
        //获取statusCode:401,404,500
        Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
        System.out.println("current request statusCode:"+statusCode);
        if(statusCode == 404){
            return "error/404";
        }else{
            return "error/500";
        }
    }

    @GetMapping("/to500")
    public String to500(HttpServletRequest request){
        throw  new RuntimeException();
    }
}
