  
package com.pes.jd.controller;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.business.main.CsBusiness;
import com.pes.jd.business.main.CsManageBusiness;
import com.pes.jd.business.main.SaleIndexSettingBusiness;
import com.pes.jd.model.DO.Cs;
import com.pes.jd.model.DO.SaleIndexSettingDO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.ShopAccountDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 客服设置，查询
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */

@RestController
@RequestMapping("/cs/manage")
public class CsManageController extends BaseController{
	private final Logger log=LoggerFactory.getLogger(CsManageController.class);
	@Autowired
	private CsManageBusiness csManageBusiness;

	@Autowired
	private SaleIndexSettingBusiness saleIndexSettingBusiness;


	@Autowired
	private CsBusiness csBusiness;


	/**
	 * 客服信息查询 
	 */
	@RequestMapping("/selectShopCs")
	public ApiResponse selectShopCs(@RequestParam(name="shopId") String shopId,
			@RequestParam(name="groupId")String groupId,
			@RequestParam(name="nick")String nick,
			@RequestParam(name="operateType")String operateType,
			@RequestParam(name="type")String typeStr) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		List<CsDTO> csLst;
		Integer type = null;
		try {
			if(!Strings.isNullOrEmpty(typeStr)){
				type = Integer.valueOf(typeStr);
			}
			csLst=	csManageBusiness.selectShopCs(shopId,groupId,nick,operateType,type);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			csLst= new ArrayList<>(0);
			log.error("selectShopCs:{}" , e.getMessage(), e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		}
		 map.put("csLst", csLst);
		apiResponse.setData(map);
		return apiResponse;
	}
	
	/**
	 * 查询店铺子账号来设置售前客服
	 */
	@RequestMapping(value = "selectShopSubUserForCsSetting")
	public ApiResponse selectShopSubUserForCsSetting(@RequestParam(name="shopId")String shopId,
			@RequestParam(name="nick")String nick) {
		ApiResponse apiResponse;
		Map<String, Object> resultMap = new HashMap<>();
		try {
			// 查询设置的客服
			List<CsDTO> css = csManageBusiness.selectCsByShopId(Long.valueOf(shopId));
			boolean havePreCss = css != null && css.size() > 0 ? true : false;
			List<ShopAccountDTO> retSubUsers = null;
			// 查询店铺下的所有客服
			List<ShopAccountDTO> shopAccounts = csManageBusiness.selectShopAccountByShopIdByNickByStatusForCsManager(Long.valueOf(shopId), nick, null);
			if (havePreCss) {
				if (CollectionUtils.isNotEmpty(shopAccounts)) {
					retSubUsers = new ArrayList<>(shopAccounts.size());
					Set<String> csNickSet = new HashSet<>(css.size());
					for (CsDTO cs : css) {
						csNickSet.add(cs.getNick().toLowerCase());
					}
					for (ShopAccountDTO subUser : shopAccounts) {
						if (!csNickSet.contains(subUser.getNick().toLowerCase())) {
							retSubUsers.add(subUser);
						}
					}
				}
			} else {
				retSubUsers = shopAccounts;
			}
			if (CollectionUtils.isEmpty(retSubUsers)) {
				retSubUsers = new ArrayList<>(0);
			}else{
				Set<String> csNicksSet=Sets.newHashSet();
				retSubUsers=retSubUsers.stream().filter(// 过滤去重,避免存在重复的客服
			               v -> {
			                   boolean flag = !csNicksSet.contains(v.getNick());
			                   csNicksSet.add(v.getNick());
			                   return flag;
			               }
			       ).collect(Collectors.toList());
			}
			resultMap.put("subUsers", retSubUsers);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			log.error("getShopSubUserForCsSetting :{}" , e.getMessage(), e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
			resultMap.put("subUsers", new ArrayList<ShopAccountDTO>(0));
		}
		apiResponse.setData(resultMap);
		return apiResponse;
	}
	/**
	 *单个客服设置
	 * 
	 * @param shopId
	 * @param groupId
	 * @param nickArrStr
	 *            店铺没有全部选项
	 * @return ApiResponse
	 *        
	 */
	@RequestMapping(value = "updateShopCustomerServices")
	public ApiResponse updateShopCustomerServices(@RequestParam() String nickArrStr,
			@RequestParam() String groupId,
			@RequestParam() Long shopId,
			@RequestParam() String type,
			@RequestParam(name="subUserNum") String subUserNum) {
		ApiResponse apiResponse;
		Map<String, Object> result=Maps.newHashMap();
		try {
			List<CsDTO> css = csManageBusiness.selectCsByShopIdByTypeByCsStatus(shopId,null,1);
			List<String> nickLst=Arrays.asList(nickArrStr.split(","));
			Integer tempNum=css.size()+nickLst.size();
			Integer subNum=Integer.valueOf(subUserNum);
			if(subNum<tempNum){
				 return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_03,subUserNum);
			}
			result = csManageBusiness.saveShopCs(shopId,groupId, nickArrStr,type);
			result.put("remainCount", subNum-tempNum);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_05);
			log.error("updateShopCustomerServices:{}" , e.getMessage(), e);
		}
		apiResponse.setData(result);
		return apiResponse;
	}
	/**
	 * 批量客服管理
	 * @param operateType 1:批量添加 2批量锁定  
	 *
	 */
	@RequestMapping(value = "batchUpdateShopCs")
	public ApiResponse batchUpdateShopCs(@RequestParam(name="nickArrStr") String nickArrStr,
			@RequestParam(name="groupId") String groupId,
			@RequestParam(name="shopId") Long shopId,
			@RequestParam(required=false,name="type") String type,
			@RequestParam(name="operateType") Integer operateType,
			@RequestParam(name="subUserNum") String subUserNum,
			@RequestParam(required=false,name="selfOptType") String selfOptType
			) {
		ApiResponse apiResponse;
		try {
			if(StringUtils.isBlank(selfOptType)) {
				selfOptType = "0";
			}
			if(operateType==1){
				List<CsDTO> css = csManageBusiness.selectCsByShopIdByTypeByCsStatus(shopId,null,1);
				List<String> nickLst=Arrays.asList(nickArrStr.split(","));
				int tempNum=css.size()+nickLst.size();
				if(Integer.parseInt(subUserNum)<tempNum){
					 return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_03,subUserNum);
				}
			}
			apiResponse=csManageBusiness.batchUpdateShopCs(shopId,groupId, nickArrStr, type,operateType,selfOptType);
		} catch (Exception e) {
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_05);
			log.error("batch UpdateShopCs:{}" , e.getMessage(), e);
		}
		return apiResponse;
	}
	
	/**
	 * 编辑客服信息
	 */
	@RequestMapping(value = "updateCs")
	public ApiResponse updateCs(@RequestParam(name="csNick")String csNick,
			@RequestParam(name="simpleNick") String simpleNick,
			@RequestParam(name="type") Integer type ,
			@RequestParam(name="shopId") Long shopId,
			@RequestParam(name="groupId") String  groupId) {
		int count=csManageBusiness.selectSampleSimpleNickBySimpleName(shopId,simpleNick,csNick);
		if(count>0){
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_18);
		}
		try {
			csManageBusiness.updateCs(shopId,groupId,csNick, simpleNick,type);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			log.error("update Cs:{}" , e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_09);
		}
	}
	
	
	/**
	 * 锁定或解锁客服
	 */
	@RequestMapping(value = "lockCs")
	public ApiResponse lockCs(@RequestParam(name="shopId")Long shopId,
			@RequestParam(name="csNickStr")String csNickStr,
			@RequestParam(name="subuserNum")String subuserNum,
			@RequestParam(name="operateType") String operateType) {
		try {
			if("2".equals(operateType)){
				List<CsDTO> css = csManageBusiness.selectCsByShopIdByTypeByCsStatus(shopId,null,1);
				List<String> nickLst=Arrays.asList(csNickStr.split(","));
				int tempNum=css.size()+nickLst.size();
				if(Integer.parseInt(subuserNum)<tempNum){
					 return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_03,subuserNum);
				}
			}
			csManageBusiness.lockCs(shopId,csNickStr, operateType);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			log.error("lock or unLock Cs:{}" ,e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_13);
		}
	}
	

	
	@RequestMapping(value = "/refreshShopSubUsers")
	public ApiResponse refreshShopSubUsers(
			@RequestParam(name="shopId") Long shopId,
			@RequestParam(name="sessionKey") String sessionKey,
			@RequestParam(name="sellerId") String sellerId,
			@RequestParam(name="sellerShowNick") String sellerShowNick,
			@RequestParam(name="sellerNick") String sellerNick) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			long t1 = System.currentTimeMillis();
			csManageBusiness.saveShopAccountInfoOfShop(sessionKey, sellerId, sellerNick,sellerShowNick, shopId);
			long t2 = System.currentTimeMillis();
			log.info("refreshAccountTime is :{}",t2-t1);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1003.getCode());
			apiResponse.setRpMsg(String.format(ApiCodeEnum.CODE_SUCCESS_1003.getMsg(),"子账号刷新成功"));
		} catch (Exception e) {
			log.error("refreshShopSubUsers error information：{}" , e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_01_12.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_01_12.getMsg());
		}
		return apiResponse;
	}
	


	//自营的这个接口未来可以用到,但是现在换成洞察的jdskd.报错先注释掉,后续再说
	@RequestMapping(value = "/refreshShopSubUsersSelf")
	public ApiResponse refreshShopSubUsersSelf(
			@RequestParam(name="shopId") Long shopId,
			@RequestParam(name="sessionKey") String sessionKey,
			@RequestParam(name="sellerId") String sellerId,
			@RequestParam(name="sellerShowNick") String sellerShowNick,
			@RequestParam(name="sellerNick") String sellerNick) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			csManageBusiness.saveShopAccountInfoOfShopSelf(sessionKey, sellerId, sellerNick,sellerShowNick, shopId);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1003.getCode());
			apiResponse.setRpMsg(String.format(ApiCodeEnum.CODE_SUCCESS_1003.getMsg(),"子账号刷新成功"));
		} catch (Exception e) {
			log.error("refreshShopSubUsers error information：{}" , e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_01_12.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_01_12.getMsg());
		}
		return apiResponse;
	}
	
	
	/**
	 * 获取店铺下的咚咚信息
	*@param type
	 */
	@RequestMapping(value = "/selectShopCswwSimpleNames" ,method=RequestMethod.POST)
	public ApiResponse selectShopCswwSimpleNames(@RequestParam(name="shopLst") String  shopLstJson,
			@RequestParam(required=false,name="type")String type) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		List<CsDTO> csLst;
		List<ShopCommonParam> shopLst;
		Integer csType=null;
		if(StringUtils.isNotBlank(type)){
			csType=Integer.valueOf(type);
		}
		try {
			shopLst = JacksonUtils.json2list(shopLstJson, ShopCommonParam.class);
		} catch (Exception e1) {
			log.error("parse shopLst error ：{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
		}
		try {
				 csLst=	csManageBusiness.selectShopCswwSimpleNames(shopLst, csType);
			 apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			csLst=Lists.newArrayList();
			 apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
			log.error("selectShopCswwSimpleNames error information：{}" , e.getMessage(), e);
		}
		map.put("csLst", csLst);
		apiResponse.setData(map);
		return apiResponse;
	}
	
	@RequestMapping(value = "/selectCsNickByShopIdByGroupIdByCsNick",method=RequestMethod.POST)
	public ApiResponse selectCsNickByShopIdByGroupIdByCsNick(@RequestParam(name="shopId")String shopId ,
			@RequestParam(name="groupId")String groupId,
			@RequestParam(name="csNick") String csNick) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		try {
			List<UserQuery> nickLst=csBusiness.getNicks(shopId, groupId, csNick);
			map.put("csNickLst", nickLst);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			map.put("csNickLst", new ArrayList<String>(0));
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01,map);
		}
		return apiResponse;
	}

	@RequestMapping(value = "/selectCsNickByShopIdByGroupIdByCsNickNew",method=RequestMethod.POST)
	public ApiResponse selectCsNickByShopIdByGroupIdByCsNickNew(@RequestParam(name="shopId")String shopId ,
															 @RequestParam(name="groupId")String groupId,
															 @RequestParam(name="csNick") String csNick) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		try {
			List<UserQuery> nickLst=csBusiness.getNicksNew(shopId, groupId, csNick);
			map.put("csNickLst", nickLst);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			map.put("csNickLst", new ArrayList<String>(0));
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01,map);
		}
		return apiResponse;
	}
	
	@RequestMapping(value = "/selectMultiCsNickByShopIdByGroupIdByCsNick",method=RequestMethod.POST)
	public ApiResponse selectMultiCsNickByShopIdByGroupIdByCsNick(@RequestParam(name="shopLst")String shopLstJson ,
			@RequestParam(name="groupId")String groupId,
			@RequestParam(name="csNick") String csNick) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		List<ShopCommonParam> shopLst;
		try {
			shopLst = JacksonUtils.json2list(shopLstJson, ShopCommonParam.class);
		} catch (Exception e1) {
			log.error("parse shopLst error ：{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
		}
		try {
			List<UserQuery> csNickAllLst=Lists.newArrayList();
			for (ShopCommonParam shopQuery : shopLst) {
				List<UserQuery> nickLst=csBusiness.getNicks(String.valueOf(shopQuery.getShopId()), groupId, csNick);
				csNickAllLst.addAll(nickLst);
			}
			List<String> csNickLst=Lists.newArrayList();
			if(CollectionUtils.isNotEmpty(csNickAllLst)){
				for (UserQuery userQuery : csNickAllLst) {
					csNickLst.add(userQuery.getNick());
				}
				map.put("csNickLst", csNickLst);
			}
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			map.put("csNickLst", new ArrayList<String>(0));
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01,map);
		}
		return apiResponse;
	}
	
	@RequestMapping("/selectCsByShopIdByType")
	public  ApiResponse selectCsByShopIdByType(
			@Param("shopId")Long shopId, 
			@Param("type")Integer type){
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		try {
			List<CsDTO> csLst=	csManageBusiness.selectCsByShopIdByTypeByCsStatus(shopId, type,null);
			map.put("csLst", csLst);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			map.put("csLst", new ArrayList<CsDTO>(0));
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01,map);
		}
		return apiResponse;
	}
	
	@RequestMapping("/selectCsCountByShopIdByType")
	public  ApiResponse selectCsCountByShopIdByType(
			@Param("shopId")Long shopId, 
			@Param("type")Integer type){
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		try {
			int count=	csManageBusiness.selectCsCountByShopIdByType(shopId, type);
			map.put("count", count);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			log.error(e.getMessage(),e);
			map.put("count", 0);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01,map);
		}
		return apiResponse;
	}

	/**
	 * 查询看板客服信息
	 * @param shopId
	 * @return
	 */
	@RequestMapping("/selectBoardCsInfo")
	public  ApiResponse selectBoardCsInfo(@Param("shopId")String shopId) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		try {
			if(StringUtils.isBlank(shopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_02.getCode(),ApiCodeEnum.CODE_ERROR_XS_01_02.getMsg());
			}
			Date nowDate=new Date();
			Date date = DateUtil.yyyyMMddFormat(nowDate);
			SaleIndexSettingDO saleDO = saleIndexSettingBusiness.selectSaleIndexSettingByShop(Long.valueOf(shopId), date);
			map.put("saleIndex", saleDO);
			List<UserQuery> boardNicks = csBusiness.getBoardNicks(shopId);
			map.put("boardNicks",boardNicks);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			log.error(this.getClass().getName(),"selectBoardCsInfo()-error:[]",e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01.getCode(),ApiCodeEnum.CODE_ERROR_XS_01_01.getMsg());
		}
		return apiResponse;
	}

	/**
	 *
	 * @param shopId
	 * @param groupId
	 * @param csNicks
	 * @return
	 */
	@RequestMapping("/batchUpdateCsofGroup")
	public Object batchUpdateCsofGroup(
			@RequestParam("shopId") Long shopId,
			@RequestParam("groupId") String groupId,
			@RequestParam("csNicks") String csNicks){

		try {
			csManageBusiness.batchUpdateCsofGroup(shopId,groupId,csNicks);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002,RestApiResponse2.of());
		} catch (Exception e) {
			log.error("master batchUpdateCsofGroup error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_17,RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/selectCsCountByShopIdByStatus")
	public Object  selectCsCountByShopIdByCsStatus(@RequestParam("shopId") Long shopId,
												   @RequestParam("status") Integer status){
		Integer count;
		try {
			count = csManageBusiness.selectCsCountByShopIdByCsStatus(shopId,status);
			return RestResponseTypeRef.ofSuccess(count);
		} catch (Exception e) {
			return RestResponseTypeRef.ofFail();
		}

	}
	
	
	/**
	 * 查询店铺对应的客服数据
	 */
	@RequestMapping("/selectShopCsList")
	public Object selectShopCsLists(@RequestParam(name="shopId") Long shopId,
			@RequestParam(name="csNicks",required=false)String csNicks
		) {
		List<Cs> csLst;
		try {
			csLst = csManageBusiness.selectShopCsLists(shopId, csNicks);
		} catch (Exception e) {
			log.error("selectShopCsList:{}", e.getMessage(), e);
			return RestResponseTypeRef.ofFail();
		}
		return RestResponseTypeRef.ofSuccess(csLst);
	}
	
	
}
  
