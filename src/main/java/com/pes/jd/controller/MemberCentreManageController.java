package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.pes.jd.business.main.MemberCentreBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.NewMemberCentreDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * @Author: yuanxun
 * @Date: 16:30 2019/11/6
 * @Description:
 */
@RestController
@RequestMapping("/memberCentre")
public class MemberCentreManageController {
    private static final Logger logger = LoggerFactory.getLogger(MemberCentreManageController.class);

    @Autowired
    private MemberCentreBusiness memberCentreBusiness;

    private static final String format = "yyyy-MM-dd HH:mm:ss";

    @RequestMapping("/insert")
    public RestResponseTypeRef insert(@RequestParam("shopId") Long shopId,
                                      @RequestParam("shopTitle") String shopTitle,
                                      @RequestParam("userNick") String userNick,
                                      @RequestParam("contact") String contact,
                                      @RequestParam("phoneNumber") String phoneNumber,
                                      @RequestParam("position") Integer position,
                                      @RequestParam(value = "qq", required = false) String qq,
                                      @RequestParam(value = "email", required = false) String email) {
        try {
            Assert.notNull(shopId, "shopId not null");
            Assert.notNull(shopTitle, "shopTitle not null");
            Assert.notNull(userNick, "userNick not null");
            Assert.notNull(position, "position not null");
            Assert.notNull(phoneNumber, "phoneNumber not null");
            Assert.notNull(contact, "contact not null");

            memberCentreBusiness.insert(shopId, shopTitle, userNick, contact, phoneNumber, position, qq, email);

            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            logger.error("master member insert error:" + e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/update")
    public RestResponseTypeRef update(@RequestParam("id") Long id,
                                      @RequestParam("position") Integer position,
                                      @RequestParam(value = "phoneNumber", required = false) String phoneNumber,
                                      @RequestParam("contact") String contact,
                                      @RequestParam(value = "qq", required = false) String qq,
                                      @RequestParam(value = "qqEmail", required = false) String qqEmail) {
        try {
            Assert.notNull(id, "id not null");
            Assert.notNull(position, "position not null");
            Assert.notNull(contact, "contact not null");

            memberCentreBusiness.update(contact, phoneNumber, position, id, qq, qqEmail);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            logger.error("master member update error: " + e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/getMemberInfoByShopId")
    public RestResponseTypeRef getMemberInfoByShopId(@RequestParam("shopId") Long shopId,
                                                     @RequestParam("nick") String nick) {
        try {
            Assert.notNull(shopId, "shopId not null");

            return RestResponseTypeRef.ofSuccess(memberCentreBusiness.getMemberInfoByShopId(shopId, nick));
        } catch (Exception e) {
            logger.error("master getMemberInfoByShopId error:" + e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/selectMemberInfoBySubDateAndShopAndPosition")
    public RestResponseTypeRef selectMemberInfoBySubDateAndShopAndPosition(@RequestParam("startDate") String startDate,
                                                                   @RequestParam("endDate") String endDate,
                                                                   @RequestParam("nick") String nick,
                                                                    @RequestParam("position") Integer position,
                                                                   @RequestParam("status") String status,
                                                                   @RequestParam(value = "shopType",required = false) Integer shopType) {

        try {
        	if(shopType==null) {
        		shopType = Integer.valueOf(CommonConstants.SHOP_TYPE_POP_STR);
        	}
        	
            Date sDate = DateUtil.parse(startDate, format);
            Date eDate = DateUtil.parse(endDate, format);

            return RestResponseTypeRef.ofSuccess(memberCentreBusiness.selectMemberInfoBySubDateAndShopAndPosition(sDate, eDate, nick, position, status,shopType));
        } catch (Exception e) {
            logger.error("master selectMemberInfo error:" + e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/insertForInitialization")
    public Object insertForInitialization(@RequestParam("shopId") String shopId,
                                          @RequestParam("title") String title,
                                          @RequestParam("csNick") String csNick,
                                          @RequestParam("phone") String phone,
                                          @RequestParam(value = "qq", required = false) String qq,
                                          @RequestParam(value = "email", required = false) String email){
        try{
            Long shop = Long.valueOf(shopId);
            memberCentreBusiness.insertForInitialization(shop, title, csNick, phone, qq, email);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        }catch (Exception e){
            logger.error("初始化时添加会员信息出错 shopId :{}, error :{}", shopId, e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }

    }

    @RequestMapping("/judgePopUpOrNot")
    public Object listMemberInfosByShopIdCsNick(@RequestParam("shopId") String shopId,
                                                @RequestParam("userPin") String nick,
                                                @RequestParam("shopName") String shopName){
        try{
            boolean flag = memberCentreBusiness.judgePopUpOrNot(Long.valueOf(shopId), nick, shopName);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, flag);
        }catch (Exception e){
            logger.error("弹窗判断时, 查询该客服的会员信息时报错 shopId :{}, nick :{}, error :{}", shopId, nick, e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
    }
}
  
