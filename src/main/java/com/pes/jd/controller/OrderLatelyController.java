package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.sub.OrderLatelyBusiness;
import com.pes.jd.model.DTO.OrderLatelyDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 *	插件提供近三天的订单数据
 */
@RestController
@RequestMapping("/OrderLately")
public class OrderLatelyController {
	
    private static final Logger logger = LoggerFactory.getLogger(OrderLatelyController.class);

    @Autowired
    private OrderLatelyBusiness orderLatelyBusiness;

    
    @RequestMapping(value = "/getOrderLatelyDate")
    @ResponseBody
    public ApiResponse getOrderLatelyDate(
            @RequestParam("shop") String shopStr,
            @RequestParam("buyerNick") String buyerNick
            ) {
    	 List<OrderLatelyDTO> list = new ArrayList<OrderLatelyDTO>();
        ShopCommonParam shop=null;
        try {
        	logger.info("getOrderLatelyDate begin");
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            list = orderLatelyBusiness.getOrderLatelyDate(shop.getShopId(), buyerNick,shop.getSchemaId());
            logger.info("getOrderLatelyDate result {}",JSONObject.toJSONString(list));
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,list );
        } catch (Exception e) {
            logger.error("OrderLatelyController.getOrderLatelyDate error:{}", e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01, list);
        }
        

    }
    
    
    



}
