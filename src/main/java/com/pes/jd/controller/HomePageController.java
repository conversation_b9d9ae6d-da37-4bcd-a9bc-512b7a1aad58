package com.pes.jd.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.pes.jd.business.main.HomePageBusiness;
import com.pes.jd.business.main.PesMenuResourceBusiness;
import com.pes.jd.framework.FormUrlencoded;
import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.model.DTO.CustomReportDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.PesMenuResourceVo;
import com.pes.jd.util.BaseUtils;
import com.yiyitech.support.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/26 2:57 PM
 * @since 1.0.0
 * <p>
 * 首页
 */
@RestController
@RequestMapping("/home")
public class HomePageController {
    private final static Logger LOGGER = LoggerFactory.getLogger(HomePageController.class);
    @Autowired
    private HomePageBusiness homePageBusiness;
    @Autowired
    private PesMenuResourceBusiness pesMenuResourceBusiness;
    /**
     * 自定义列信息
     */
    @RequestMapping("custom/report/base-info")
    public Object searchCustomReportBaseInfo(String name, Long shopId) {
        try {
            name = BaseUtils.purify(name);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, homePageBusiness.searchAllWithCategory(name, shopId));
        } catch (Exception e) {
            LOGGER.error(" searchCustomReportBaseInfo error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_01);
        }
    }

    /**
     * 获取自定义报表信息
     */
    @RequestMapping("custom/report/info")
    public Object searchCustomReportInfo(Integer type,
                                         Long shopId) {
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, homePageBusiness.searchAllByShopId(type, shopId));
        } catch (Exception e) {
            LOGGER.error(" searchCustomReportInfo error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_01);
        }
    }

    /**
     * 增加修改报表
     *
     * @param report 实体
     * @param type   1增加  2修改
     */
    @PostMapping("custom/report/insert-or-update")
    public Object insertOrUpdateCustomReportInfo(@RequestBody CustomReportDTO report, Integer type, String nick) {
        try {
            final Integer add = 1;
            final Integer update = 2;
            if (Objects.equals(type, add)) {
                Assert.notNull(report.getShopId(), " shop id must be non null ");
            } else if (Objects.equals(type, update)) {
                Assert.notNull(report.getId(), " report id must be non null ");
            } else {
                throw new IllegalArgumentException(" type must be 1 or 2 ");
            }

            int status = homePageBusiness.insertOrUpdateCustomReport(report, type, nick);

            if (status == -2)//该报表名称已存在，请重新填写
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_02);
            else
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" searchCustomReportInfo error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_01);
        }
    }

    @RequestMapping("custom/report/delete")
    public Object deleteCustomReport(@RequestParam Long id) {
        try {
            homePageBusiness.deleteCustomReport(id);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" delete custom report error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_01);
        }
    }

    @RequestMapping("custom/report/insertField")
    public Object insertCustomReport(@RequestParam String fieldsStr) {
        try {
            String reportName = "店铺总览";
            homePageBusiness.insertCustomReport(fieldsStr, reportName);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" insert field error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CR_01_01);
        }
    }

    /**
     * 运维接口
     *
     * @return 所有的系统菜单
     */
    @RequestMapping("sys/get_menu")
    public Object getMenu(String[] property) {
        try {
            Set<String> container = new HashSet<>(Arrays.asList(property == null ? new String[0] : property));
            return JSON.toJSONString(ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    pesMenuResourceBusiness.searchAll(null, false)),
                    (PropertyFilter) (Object object, String name, Object value) ->
                            !(object instanceof PesMenuResourceVo) ||
                                    CollectionUtils.isEmpty(container) ||
                                    container.contains(name) ||
                                    name.equals("subMenuLst"));
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR";
        }
    }

    /**
     * 运维接口
     * 添加菜单
     *
     * @return flag
     */
    @RequestMapping("sys/add_menu")
    public Object addMenu(@RequestBody PesMenuResource menu, @FormUrlencoded PesMenuResource menuBack, String valid) {
        try {
            Assert.notNull(valid, "not null");
            Assert.isTrue(Objects.equals(valid, LocalDate.now().format(DateTimeFormatter.ISO_DATE)), "not valid");
            if (menu == null) menu = menuBack;
            Assert.notNull(menu, "not null");
            menu.setId(null);
            menu.setShopId(-1L);
            Integer maxSort = pesMenuResourceBusiness.getMaxSort(Long.valueOf(menu.getParentId()));
            menu.setSort(maxSort == null ? 0 : maxSort + 1);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    pesMenuResourceBusiness.safeInsertMenuResource(menu));
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR";
        }
    }

    /**
     * 运维接口
     * 修改菜单
     *
     * @return flag
     */
    @RequestMapping("sys/update_menu")
    public Object updateMenu(@RequestBody PesMenuResource menu, @FormUrlencoded PesMenuResource menuBack, String valid) {
        try {
            if (menu == null) menu = menuBack;
            Assert.notNull(menu, "not null");
            Assert.isTrue(Objects.equals(valid, LocalDate.now().format(DateTimeFormatter.ISO_DATE)), "not valid");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002,
                    pesMenuResourceBusiness.updateByPrimaryKey(menu));
        } catch (Exception e) {
            e.printStackTrace();
            return "ERROR";
        }
    }

    /**
     * 店铺首页展示的菜单
     *
     * @param shopId 店铺Id
     * @return Object
     */
    @RequestMapping("/page/showMenu")
    public Object queryMenuByShopId(@RequestParam("shopId") String shopId) {
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, homePageBusiness.queryMenuByShopId(shopId));
        } catch (Exception e) {
            LOGGER.error(" queryMenuByShopId error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SY_01_01);
        }
    }

    @RequestMapping("/page/showMenuSimple")
    public Object queryMenuByShopIdOfSimple(@RequestParam("shopId") String shopId) {
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, homePageBusiness.queryMenuByShopIdOfSimple(shopId));
        } catch (Exception e) {
            LOGGER.error(" queryMenuByShopId error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SY_01_01);
        }
    }

    /**
     * 首页菜单更新
     *
     * @param shopId 店铺Id
     * @param ids    菜单Id
     * @return Object
     */
    @RequestMapping("/page/updateMenu")
    public Object updateMenuByIds(@RequestParam("shopId") String shopId, @RequestParam("ids") String ids) {
        if (StringUtils.isBlank(shopId) || StringUtils.isBlank(ids)) {
            throw new BusinessException("1003", "请至少选择一个菜单！！");
        }

        try {
            homePageBusiness.updateMenuByIds(shopId, ids);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" update field error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }

    @RequestMapping("/page/updateMenuSimple")
    public Object updateMenuByIdsOfSimple(@RequestParam("shopId") String shopId, @RequestParam("ids") String ids) {
        if (StringUtils.isBlank(shopId) || StringUtils.isBlank(ids)) {
            throw new BusinessException("1003", "请至少选择一个菜单！！");
        }

        try {
            homePageBusiness.updateMenuByIdsOfSimple(shopId, ids);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" update field error  ", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }

}
