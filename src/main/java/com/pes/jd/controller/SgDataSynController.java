package com.pes.jd.controller;

import cn.hutool.core.lang.Assert;
import com.pes.jd.business.main.ShopRemindSettingHistoryBusiness;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 导购同步接口控制器 - 批量提醒
 */
@RestController
@RequestMapping("/sgSyn/batch_remind")
public class SgDataSynController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ShopBatchRemindController.class);
    @Resource
    private ShopRemindSettingHistoryBusiness shopRemindSettingHistoryBusiness;

    @RequestMapping(value = "selectRemindSettingHistory")
    public Object selectRemindSettingHistory(
            @RequestParam("shopIdStr") String shopIdStr,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Assert.notNull(shopIdStr);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }
        try {
            logger.info("selectRemindSettingHistory shopIdStr={} startDate={} endDate={}", shopIdStr,
                    startDate,
                    endDate);
            return RestResponseTypeRef.ofSuccess(shopRemindSettingHistoryBusiness.selectRemindSettingHistory(shopIdStr, startDate, endDate));
        } catch (Exception e) {
            logger.error("selectRemindSettingHistory error shopId={}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping(value = "selectRemindWordHistory")
    public Object selectRemindWordHistory(
            @RequestParam("shopIdStr") String shopIdStr,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Assert.notNull(shopIdStr);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }
        try {
            logger.info("selectRemindWordHistory shopIdStr={} startDate={} endDate={}", shopIdStr,
                    startDate,
                    endDate);
            return RestResponseTypeRef.ofSuccess(shopRemindSettingHistoryBusiness.selectRemindWordHistory(shopIdStr, startDate, endDate));
        } catch (Exception e) {
            logger.error("selectRemindWordHistory error shopId={}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping(value = "selectRemindSettingHistory2")
    public Object selectRemindSettingHistor2y(
            @RequestParam("shopIdStr") String shopIdStr,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Assert.notNull(shopIdStr);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }
        try {
            logger.info("selectRemindSettingHistory shopIdStr={} startDate={} endDate={}", shopIdStr,
                    startDate,
                    endDate);
            return RestResponseTypeRef.ofSuccess(shopRemindSettingHistoryBusiness.selectRemindSettingHistory2(shopIdStr, startDate, endDate));
        } catch (Exception e) {
            logger.error("selectRemindSettingHistory error shopId={}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping(value = "selectRemindWordHistory2")
    public Object selectRemindWordHistory2(
            @RequestParam("shopIdStr") String shopIdStr,
            @RequestParam(value = "startDate", required = false) String startDate,
            @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            Assert.notNull(shopIdStr);
        } catch (Exception e) {
            RestResponseTypeRef.ofFail();
        }
        try {
            logger.info("selectRemindWordHistory shopIdStr={} startDate={} endDate={}", shopIdStr,
                    startDate,
                    endDate);
            return RestResponseTypeRef.ofSuccess(shopRemindSettingHistoryBusiness.selectRemindWordHistory2(shopIdStr, startDate, endDate));
        } catch (Exception e) {
            logger.error("selectRemindWordHistory error shopId={}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }
}
