package com.pes.jd.controller;

import com.pes.jd.business.main.PesUserAnalysBusiness;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.ms.domain.Result.task.dispatching.MessageSendResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/1/7
 * @Modified By:
 */
@RestController
@RequestMapping("pesUserAnalys")
public class PesUserAnalysController {

    private static final  Logger logger = LoggerFactory.getLogger(PesUserAnalysController.class);
    @Autowired
    private PesUserAnalysBusiness pesUserAnalysBusiness;
    @RequestMapping("saveUserVisitData")
    public Object saveUserVisitData(String status,String routeId){

        try {
            MessageSendResult messageSendResult = new MessageSendResult();
            messageSendResult.setMessageId("");
            int result = pesUserAnalysBusiness.saveUserVisitData(status, routeId);
            switch (result){
                case -1:
                    messageSendResult.setSendResult(Boolean.FALSE);
                    break;
                default:
                    messageSendResult.setSendResult(Boolean.TRUE);
                    break;
            }

            return RestResponseTypeRef.ofSuccess(messageSendResult);
        } catch (Exception e) {
            logger.error("根据用户传统计次数,保存用户在咚咚管家点击次数:{}",e.getMessage(),e);
            return RestResponseTypeRef.ofFail();
        }
    }
}
