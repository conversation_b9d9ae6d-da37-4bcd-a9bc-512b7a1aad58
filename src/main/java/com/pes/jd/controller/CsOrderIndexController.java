package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.OrderIndexBusiness;
import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.AssistServiceQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/cs/order/")
public class CsOrderIndexController {

	private final Logger logger = LoggerFactory.getLogger(CsOrderIndexController.class);
	
	/**
	 * 协助服务分析
	 */
	@Autowired
	private OrderIndexBusiness orderIndexBusiness;

	@RequestMapping("selectCsOrderIndex")
	public ApiResponse selectCsOrderIndex(@RequestParam("shop") String shopStr,
			@RequestParam("csNicks") String csNicks,
			@RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr,
			@RequestParam("sortPageQuery") String sortPageQueryStr,
			@RequestParam("assistServiceQuery") String assistServiceQueryStr,
			@RequestParam(value = "orderInfoLogUploadParamStr" ,required = false) String orderInfoLogUploadParamStr
			) throws Exception {
		Map<String, Object> data = Maps.newHashMap();
		
		List<String> csNickList = Arrays.asList(csNicks.split(","));
		Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		ShopCommonParam shop = null;
		SortPageQuery sortPageQuery = null;
		AssistServiceQuery assistServiceQuery = null;
		OrderInfoLogUploadParam orderInfoLogUploadParam = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
			assistServiceQuery = JacksonUtils.json2pojo(assistServiceQueryStr, AssistServiceQuery.class);
			orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
		} catch (Exception e) {
			logger.error("json error", e.getMessage(),e);
		}
		try {
			DataAnalysisVO<CsOrderIndexDTO> csAssistServiceAnalysisVO = orderIndexBusiness.selectCsOrderIndexByDateByCsNickByBuyerByOrderByType(shop, startDate, endDate, csNickList, assistServiceQuery, sortPageQuery,orderInfoLogUploadParam);
			data.put("csAssistServiceAnalysisVO", csAssistServiceAnalysisVO);
			//上传参数
//			UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParam);
//  		    param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_ASSISTSERVICE.getName());
//            uploadDBOperationBusiness.upload(param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
		} catch (Exception e1) {
			logger.error("cs assist service analysis error", e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_FW_01_03);
		}
	}
}
