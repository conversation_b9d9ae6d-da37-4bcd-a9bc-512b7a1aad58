package com.pes.jd.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.business.main.ShopBatchRemindBusiness;
import com.pes.jd.business.main.ShopBusiness;
import com.pes.jd.dao.main.ShopRemindWordDao;
import com.pes.jd.data.converter.ApiCheckConvert;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.JacksonUtils;
import com.pes.jd.util.KeyWordUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 插件管理 - 批量提醒
 */
@RestController
@RequestMapping("/shop/batch_remind")
public class ShopBatchRemindController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ShopBatchRemindController.class);
    @Resource
    private ShopBatchRemindBusiness shopBatchRemindBusiness;
    @Resource
    private ShopBusiness shopBusiness;
    @Resource
    private ApiCheckConvert apiCheckConvert;
    @Resource
    private ShopRemindWordDao shopRemindWordDao;
    @RequestMapping("updateShopBatchRemindSetting")
    @ResponseBody
    public Object updateShopBatchRemindSetting(ShopSettingBatchRemindDTO dto) {
        if (dto == null || dto.getShopId() == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            int update = shopBatchRemindBusiness.updateShopBatchRemindSetting(dto);
            switch (update) {
                case -1:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_03, RestApiResponse2.of(false));
                case 0:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_01, RestApiResponse2.of(false));
                default:
                    return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            }
        } catch (Exception e) {
            logger.error("web -> /shop/batch_remind/updateShopBatchRemindSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_02, RestApiResponse2.of(false));
        }
    }


    @RequestMapping("updateShopPresaleBatchRemindSetting")
    @ResponseBody
    public Object updateShopPresaleBatchRemindSetting(String shopSettingBatchRemindPresaleStr) {

        ShopSettingBatchRemindPresaleDTO dto = null;
        try {
            dto = JacksonUtils.json2pojo(shopSettingBatchRemindPresaleStr, ShopSettingBatchRemindPresaleDTO.class);
        } catch (Exception e) {
            logger.info("master parse json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }

        if (dto == null || dto.getShopId() == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            int update = shopBatchRemindBusiness.updateShopPresaleBatchRemindSetting(dto);
            switch (update) {
                case -1:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_03, RestApiResponse2.of(false));
                case 0:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_01, RestApiResponse2.of(false));
                default:
                    return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            }
        } catch (Exception e) {
            logger.error("web -> /shop/batch_remind/updateShopPresaleBatchRemindSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_02, RestApiResponse2.of(false));
        }
    }


    @RequestMapping("updateShopReserveBatchRemindSetting")
    @ResponseBody
    public Object updateShopReserveBatchRemindSetting(String shopSettingBatchRemindReserveStr) {

        ShopSettingBatchRemindReserveDTO dto = null;
        try {
            dto = JacksonUtils.json2pojo(shopSettingBatchRemindReserveStr, ShopSettingBatchRemindReserveDTO.class);
        } catch (Exception e) {
            logger.info("master parse json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }

        if (dto == null || dto.getShopId() == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            int update = shopBatchRemindBusiness.updateShopReserveBatchRemindSetting(dto);
            switch (update) {
                case -1:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_03, RestApiResponse2.of(false));
                case 0:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_01, RestApiResponse2.of(false));
                default:
                    return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            }
        } catch (Exception e) {
            logger.error("web -> /shop/batch_remind/updateShopReserveBatchRemindSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_02, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("selectShopBatchRemindSetting")
    @ResponseBody
    public Object selectShopBatchRemindSetting(Long shopId) {
        if (shopId == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            ShopSettingBatchRemindDTO setting = shopBatchRemindBusiness.selectShopBatchRemindSetting(shopId);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(setting));
        } catch (Exception e) {
            logger.error("web -> /shop/batch_remind/selectShopBatchRemindSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_02, RestApiResponse2.of(false));

        }
    }

    @RequestMapping("selectShopPresaleBatchRemindSetting")
    @ResponseBody
    public Object selectShopPresaleBatchRemindSetting(Long shopId) {
        if (shopId == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            ShopSettingBatchRemindPresaleDTO setting = shopBatchRemindBusiness.selectShopPresaleBatchRemindSetting(shopId);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(setting));
        } catch (Exception e) {
            logger.error("web -> /shop/batch_remind/selectShopPresaleBatchRemindSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_02, RestApiResponse2.of(false));

        }
    }

    @RequestMapping("selectShopReserveBatchRemindSetting")
    @ResponseBody
    public Object selectShopReserveBatchRemindSetting(Long shopId) {
        if (shopId == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            ShopSettingBatchRemindReserveDTO setting = shopBatchRemindBusiness.selectShopReserveBatchRemindSetting(shopId);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(setting));
        } catch (Exception e) {
            logger.error("web -> /shop/batch_remind/selectShopPresaleBatchRemindSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_04_02, RestApiResponse2.of(false));

        }
    }


    @RequestMapping("selfAddShopRemindWord")
    @ResponseBody
    public Object selfAddShopRemindWord(ShopRemindWordDTO dto) {
        if (dto == null || dto.getShopId() == null || StringUtils.isBlank(dto.getName()) || dto.getType() == null || StringUtils.isBlank(dto.getContent()) || dto.getIsSendGoodsUrl() == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        Map<String,Object> result= Maps.newHashMap();
        RestApiResponse2<Object> resp=validCheckKeyWord(dto.getShopId(),dto.getContent(),result);
        if(resp!=null){
            return resp;
        }
        try {
            switch (shopBatchRemindBusiness.addSelfShopRemindWord(dto)) {
                case -3:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_14, RestApiResponse2.of(false));
                case -2:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_13, RestApiResponse2.of(false));
                case 0:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_02, RestApiResponse2.of(false,result));
                default:
                    return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of(true,result));
            }
        } catch (Exception e) {
            logger.error("master -> /shop/batch_remind/addShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_03, RestApiResponse2.of(false));
        }
    }



    @RequestMapping("addShopRemindWord")
    @ResponseBody
    public Object addShopRemindWord(@RequestParam(name="dtoStr") String dtoStr) {

        ShopRemindWordDTO dto;

        try {
            dto = JSONObject.toJavaObject(JSONObject.parseObject(dtoStr), ShopRemindWordDTO.class);

        }catch(Exception e){
            logger.error("master -> /shop/batch_remind/addShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        }

        Map<String,Object> result= Maps.newHashMap();
        dto.setGoodsType(0);
        dto.setUsing(false);

        if(StringUtils.isNotBlank(dto.getContent())){
            //查看该客服在新表中是否有数据，没有将原表的数据已客服维度保存，有就不操作
            shopBatchRemindBusiness.checkShopCsRemindWordDb(dto);
            List<ShopRemindWordDTO> shopRemindWordList = shopBatchRemindBusiness.selectShopBatchRemindSettingWord(dto.getShopId(), dto.getName(),dto.getCsNick());
            if(CollectionUtils.isNotEmpty(shopRemindWordList)){
                return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_16, RestApiResponse2.of(false));
            }
            RestApiResponse2<Object> resp=validCheckKeyWord(dto.getShopId(),dto.getContent(),result);
            if(resp!=null){
                return resp;
            }
        }
        try {
           return shopBatchRemindBusiness.addShopRemindWord(dto);
        } catch (Exception e) {
            logger.error("master -> /shop/batch_remind/addShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_03, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("addTopTime")
    @ResponseBody
    public Object addTopTime(@RequestParam("shopId") Long shopId,
                             @RequestParam("id") Long id) {
        try {
            return shopBatchRemindBusiness.addTopTime(shopId, id);
        } catch (Exception e) {
            logger.error("master -> /shop/batch_remind/addShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_17, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("deleteShopRemindWord")
    @ResponseBody
    public Object deleteShopRemindWord(Long id,String csNick,Long shopId) {
        if (id == null) return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            //查看该客服在新表中是否有数据，没有将原表的数据已客服维度保存，有就不操作
            ShopRemindWordDTO dto = new ShopRemindWordDTO();
            dto.setShopId(shopId);
            dto.setId(id);
            dto.setCsNick(csNick);
            shopBatchRemindBusiness.checkShopCsRemindWordDb(dto);
            int del = shopBatchRemindBusiness.deleteShopRemindWord(id,csNick);
            switch (del) {
                case -1:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_10, RestApiResponse2.of(false));
                case 0:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_04, RestApiResponse2.of(false));
                default:
                    return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            }
        } catch (Exception e) {
            logger.error("master -> /shop/batch_remind/deleteShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_05, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("updateShopRemindWord")
    @ResponseBody
    public Object updateShopRemindWord(@RequestParam(name="dtoStr") String dtoStr) {

        ShopRemindWordDTO dto;

        try {
            dto = JSONObject.toJavaObject(JSONObject.parseObject(dtoStr), ShopRemindWordDTO.class);

        }catch(Exception e){
            logger.error("master -> /shop/batch_remind/updateShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        }

        try{
            List<ShopRemindWordDTO> shopRemindWordList = shopBatchRemindBusiness.selectShopBatchRemindSettingWord(dto.getShopId(), dto.getName(),dto.getCsNick());
            if(CollectionUtils.isNotEmpty(shopRemindWordList)){
                if(!shopRemindWordList.get(0).getId().equals(dto.getId())){
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_16, RestApiResponse2.of(false));
                }
            }
        }catch (Exception e){
            logger.error("master -> /shop/batch_remind/updateShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        }

        if (dto == null || dto.getId() == null || dto.getShopId() == null
                || !ObjectUtils.anyNotNull(dto.getName(), dto.getType(), dto.getIsSendGoodsUrl(), dto.getIsDefault()))
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        Map<String,Object> result= Maps.newHashMap();
        if(StringUtils.isNotBlank(dto.getContent())){
            RestApiResponse2<Object> resp=validCheckKeyWord(dto.getShopId(),dto.getContent(),result);
            if(resp!=null){
                return resp;
            }
        }
        try {
            //查看该客服在新表中是否有数据，没有将原表的数据已客服维度保存，有就不操作
            shopBatchRemindBusiness.checkShopCsRemindWordDb(dto);
            int stnum = shopBatchRemindBusiness.updateShopRemindWord(dto);
            switch (stnum) {
                case 0:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_06, RestApiResponse2.of(false, result));
                case -2:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_13, RestApiResponse2.of(false,result));
                case -3:
                    return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_14, RestApiResponse2.of(false));
                default:
                    return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            }


        } catch (Exception e) {
            logger.error("master -> /shop/batch_remind/updateShopRemindWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_07, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("selectShopRemindWordLst")
    @ResponseBody
    public Object selectShopRemindWordLst(ShopRemindWordDTO dto) {
        if (dto == null || dto.getShopId() == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            //查看该客服在新表中是否有数据，没有将原表的数据已客服维度保存，有就不操作
            shopBatchRemindBusiness.checkShopCsRemindWordDb(dto);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopBatchRemindBusiness.selectShopRemindWordLstForPlugin(dto)));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("master -> /shop/batch_remind/selectShopRemindWordLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_09, RestApiResponse2.of(false));
        }
    }


    @RequestMapping("addDefaultRemindWordLst")
    @ResponseBody
    public Object addDefaultRemindWordLst(Long shopId) {
        if (shopId == null)
            return apiResponse(ApiCodeEnum.CODE_ERROR_1001, RestApiResponse2.of(false));
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopBatchRemindBusiness.addDefaultRemindWordLst(shopId)));
        } catch (Exception e) {
            logger.error("master -> /shop/batch_remind/addDefaultRemindWordLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_09, RestApiResponse2.of(false));
        }
    }


    private RestApiResponse2<Object> validCheckKeyWord(Long shopId,String content,Map<String,Object> result){

        Set<String> checkWordSet= Sets.newHashSet();
        result.put("checkWordSet",checkWordSet);
        result.put("isContainWord",false);
        result.put("isContainDDWord",false);
        //敏感词
        Set<String>  keyWordSet=shopBusiness.getKeyWord(shopId);
        if(CollectionUtils.isNotEmpty(keyWordSet)){
            Set<String> validWordSet=  KeyWordUtil.validKeyWords(keyWordSet,content);
            if(CollectionUtils.isNotEmpty(validWordSet)){
                checkWordSet.addAll(validWordSet);
                result.put("isContainWord",true);
                return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_08, RestApiResponse2.of(false,result));
            }
        }
        boolean keyWordFlag;
        try {
            keyWordFlag = apiCheckConvert.isRiskSensitiveWordCheck(content);
            if(keyWordFlag){
                result.put("isContainDDWord",true);
                return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_12, RestApiResponse2.of(false,result));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return apiResponse(ApiCodeEnum.CODE_ERROR_CG_05_08, RestApiResponse2.of(false,result));
        }
        return null;
    }

    @RequestMapping("/selectShopRemindWordGoodsByWordId")
    public Object selectShopRemindWordGoodsByWordId(@RequestParam("wordId") Long wordId){
        try {
            List<Long> goodsIdLst=  shopBatchRemindBusiness.selectShopRemindWordGoodsByWordId(wordId);
            return RestResponseTypeRef.ofSuccess(goodsIdLst);
        } catch (Exception e) {
            logger.error("master -> selectShopRemindWordGoodsByWordId error:{}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }


    @RequestMapping("/selectShopRemindWordGoodsByShopId")
    public Object selectShopRemindWordGoodsByShopId(@RequestParam("shopId") Long shopId){
        try {
            List<Long> goodsIdLst=  shopBatchRemindBusiness.selectShopRemindWordGoodsByShopId(shopId);
            return RestResponseTypeRef.ofSuccess(goodsIdLst);
        } catch (Exception e) {
            logger.error("master -> selectShopRemindWordGoodsByShopId error:{}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/selectShopRemindWordLstByShopIdAndOrderType")
    public Object selectShopRemindWordLstByShopIdAndOrderType(@RequestParam("shopId") Long shopId,
                                                              @RequestParam(value = "orderType",required = false) String orderType) {
        Long type = null;
        if (StrUtil.isNotEmpty(orderType)) {
            type = Long.valueOf(orderType);
        }
        try {
            List<ShopRemindWordDTO> shopRemindWordList = shopBatchRemindBusiness.selectShopRemindWordLstByShopIdAndOrderType(shopId, type);
            return RestResponseTypeRef.ofSuccess(shopRemindWordList);
        } catch (Exception e) {
            logger.error("shopId :{},orderType :{}查询店铺话术失败", shopId, orderType);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/selectShopBatchRemindSettings")
    public Object selectShopBatchRemindSettings(@RequestParam("shop") String shop){
        if(StringUtils.isBlank(shop)) return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);

        ApiResponse apiResponse = new ApiResponse();
        try{
            Long shopId = Long.valueOf(shop);
            boolean isRemind = shopBatchRemindBusiness.selecttShopBatchRemindSetting(shopId);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, isRemind);
        }catch (Exception e){
            logger.error("selectShopBatchRemindSettings error e :{}", e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
    }

    @RequestMapping("/insertDefaultRemindWord")
    public Object insertDefaultRemindWord(@RequestParam("shopId") String shopId){
        try{
            Long shop = Long.valueOf(shopId);
            int flag = shopBatchRemindBusiness.insertDefaultRemindWord(shop);
            if(flag > 1){
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, flag);
            }else{
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
            }
        }catch (Exception e){
            logger.error("店铺初始化时添加话术出错 shopId :{}, error :{}", shopId, e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
    }
}
