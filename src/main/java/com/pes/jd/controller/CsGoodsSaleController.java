package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.CsGoodsSaleBusiness;
import com.pes.jd.model.DTO.CsGoodsSaleIndexDTO;
import com.pes.jd.model.DTO.CsGoodsSaleIndexDetailDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/cs/goodsSale/")
public class CsGoodsSaleController extends BaseController{

	private Logger logger = LoggerFactory.getLogger(CsGoodsSaleController.class);

	@Autowired
	private CsGoodsSaleBusiness csGoodsSaleBusiness;

	//客服商品销售汇总
	@RequestMapping("selectCsGoodsSaleIndex")
	public ApiResponse selectCsGoodsSaleIndex(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
			List<CsGoodsSaleIndexDTO> csGoodsSaleIndexList = csGoodsSaleBusiness.selectCsGoodsSaleIndexCountByCsNickBySku(shop, param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst());
			data.put("csGoodsSaleIndexList", csGoodsSaleIndexList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("cs goods sale summary error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01);
		}

	}

	@RequestMapping("selectCsGoodsSaleIndexV2")
	public ApiResponse selectCsGoodsSaleIndexV2(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error(e1.getMessage());
		}
		try {
			List<CsGoodsSaleIndexDTO> csGoodsSaleIndexList = csGoodsSaleBusiness.selectCsGoodsSaleIndexCountByCsNickBySkuV2(shop, param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst(), param.getCategoryId());
			data.put("csGoodsSaleIndexList", csGoodsSaleIndexList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
		} catch (Exception e) {
			logger.error("cs goods sale summary error{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01);
		}

	}




	//客服商品销售汇总  OfSpu
	@RequestMapping("selectCsGoodsSaleIndexOfSpu")
	public ApiResponse selectCsGoodsSaleIndexOfSpu(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
//			List<CsGoodsSaleIndexDTO> csGoodsSaleIndexList1 = csGoodsSaleBusiness.selectCsGoodsSaleIndexCountByCsNickBySku(shop, param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst());
			List<CsGoodsSaleIndexDTO> csGoodsSaleIndexList = csGoodsSaleBusiness.selectCsGoodsSaleIndexCountByCsNickBySkuOfSpu(shop,param.getCsLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst());
			data.put("csGoodsSaleIndexList", csGoodsSaleIndexList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("cs goods sale summary OfSpu error {}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01);
		}

	}


	//客服销售分析
	@RequestMapping("selectCsGoodsSaleIndexAnalysis")
	public ApiResponse selectCsGoodsSaleIndexAnalysis(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
			List<CsGoodsSaleIndexDTO> csGoodsSaleAnalysis = csGoodsSaleBusiness.selectCsGoodsSaleIndexByCsNickByDateBySku(shop, param.getStartDate(), param.getEndDate(), param.getSkuId(),param.getCsNick());
			data.put("csGoodsSaleAnalysis", csGoodsSaleAnalysis);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("cs goods sale analysis error",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_05);
		}

	}


	//客服商品销售明细
	@RequestMapping("selectCsGoodsSaleIndexDetail")
	public ApiResponse selectCsGoodsSaleIndexDetail(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr,
			@RequestParam("enquiryValidDurationTime") Integer enquiryValidDurationTime,
			@RequestParam("sortPageQuery") String sortPageQueryStr,
			@RequestParam(value = "orderInfoLogUploadParamStr", required = false) String orderInfoLogUploadParamStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		SortPageQuery sortPageQuery = null;
		OrderInfoLogUploadParam orderInfoLogUploadParam = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
			//orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
			DataAnalysisVO<CsGoodsSaleIndexDetailDTO> csGoodsSaleDetailVO = csGoodsSaleBusiness.selectCsGoodsSaleIndexDetailByCsNickByDateBySku(shop, param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst(), param.getOrderId(), sortPageQuery, enquiryValidDurationTime,1);
			data.put("csGoodsSaleDetailVO", csGoodsSaleDetailVO);

			//上传参数
//            UploadDBOperationParam uploadParam = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParam);
//            uploadParam.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_CSGOODSSALEINDEXDETAIL.getName());
//            uploadDBOperationBusiness.upload(uploadParam);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("cs goods sale detail error={}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_03);
		}
	}

	//客服商品销售明细
	@RequestMapping("selectCsGoodsSaleIndexDetailV2")
	public ApiResponse selectCsGoodsSaleIndexDetailV2(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr,
			@RequestParam("enquiryValidDurationTime") Integer enquiryValidDurationTime,
			@RequestParam("sortPageQuery") String sortPageQueryStr,
			@RequestParam(value = "orderInfoLogUploadParamStr", required = false) String orderInfoLogUploadParamStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		SortPageQuery sortPageQuery = null;
		OrderInfoLogUploadParam orderInfoLogUploadParam = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
			//orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
		} catch (Exception e1) {
			logger.error(e1.getMessage());
		}
		try {
			DataAnalysisVO<CsGoodsSaleIndexDetailDTO> csGoodsSaleDetailVO = csGoodsSaleBusiness.selectCsGoodsSaleIndexDetailByCsNickByDateBySkuV2(shop, param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst(), param.getCategoryId(), param.getOrderId(), sortPageQuery, enquiryValidDurationTime, 1);

			data.put("csGoodsSaleDetailVO", csGoodsSaleDetailVO);

			//上传参数
//            UploadDBOperationParam uploadParam = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParam);
//            uploadParam.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_CSGOODSSALEINDEXDETAIL.getName());
//            uploadDBOperationBusiness.upload(uploadParam);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
		} catch (Exception e) {
			logger.error("cs goods sale detail error={}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_03);
		}
	}



	//客服商品销售明细  OfSpu
	@RequestMapping("selectCsGoodsSaleIndexDetailOfSpu")
	public ApiResponse selectCsGoodsSaleIndexDetailOfSpu(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr,
			@RequestParam("enquiryValidDurationTime") Integer enquiryValidDurationTime,
			@RequestParam("sortPageQuery") String sortPageQueryStr,
			@RequestParam(value = "orderInfoLogUploadParamStr", required = false) String orderInfoLogUploadParamStr) {
		Map<String, Object> data = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		SortPageQuery sortPageQuery = null;
		OrderInfoLogUploadParam orderInfoLogUploadParam = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
			orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
		} catch (Exception e1){
			logger.error(e1.getMessage());
		}
		try {
//            DataAnalysisVO<CsGoodsSaleIndexDetailDTO> csGoodsSaleDetailVO = csGoodsSaleBusiness.selectCsGoodsSaleIndexDetailByCsNickByDateBySku(shop, param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst(), param.getOrderId(), sortPageQuery, orderInfoLogUploadParam);
			DataAnalysisVO<CsGoodsSaleIndexDetailDTO> csGoodsSaleDetailVO = csGoodsSaleBusiness.selectCsGoodsSaleIndexDetailByCsNickByDateBySkuOfSpu(shop, param.getCsLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getSkuLst(), param.getOrderId(), sortPageQuery, enquiryValidDurationTime, orderInfoLogUploadParam);
			data.put("csGoodsSaleDetailVO", csGoodsSaleDetailVO);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error("cs goods sale detail error={}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_03);
		}
	}
}
