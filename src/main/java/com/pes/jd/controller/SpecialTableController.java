package com.pes.jd.controller;

import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/18 6:42 PM
 * @since 1.0.0
 */
@RestController
@RequestMapping("/special/table/")
public class SpecialTableController {


    @RequestMapping("pressure")
    public ApiResponse getPressureTableData(){
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
    }

}
