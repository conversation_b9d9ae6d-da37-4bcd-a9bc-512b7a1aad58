package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.main.MarketingActivityV2Business;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.MarketingActivityDTO;
import com.pes.jd.model.DTO.MarketingActivityV2DTO;
import com.pes.jd.model.DTO.MarketingActivityV2RespDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yuanxun
 * @Date: 13:38 2019/10/29
 * @Description:
 */
@RestController
@RequestMapping("/marketingActivity/v2")
public class MarketingActivityV2Controller extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(MarketingActivityV2Controller.class);
    @Autowired
    private MarketingActivityV2Business marketingActivityV2Business;

    @GetMapping("/getEffectDate")
    public Object getEffectDates() {
        List<Map<String, Object>> effectDates = Arrays.stream(MarketingActivityV2DTO.EffectDate.values())
                .map(effectDate -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", effectDate.getValue());
                    map.put("description", effectDate.getDescription());
                    return map;
                })
                .collect(Collectors.toList());
        return RestResponseTypeRef.ofSuccess(effectDates);
    }

    @GetMapping("/getFrequency")
    public Object getFrequencies() {
        List<Map<String, Object>> frequencies = Arrays.stream(MarketingActivityV2DTO.Frequency.values())
                .map(frequency -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", frequency.getValue());
                    map.put("description", frequency.getDescription());
                    return map;
                })
                .collect(Collectors.toList());
        return RestResponseTypeRef.ofSuccess(frequencies);
    }

    @GetMapping("/getOrderType")
    public Object getOrderType() {
        List<Map<String, Object>> orderTypes = Arrays.stream(MarketingActivityV2DTO.OrderType.values())
                .map(orderType -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", orderType.getValue());
                    map.put("description", orderType.getDescription());
                    return map;
                })
                .collect(Collectors.toList());
        return RestResponseTypeRef.ofSuccess(orderTypes);
    }

    @GetMapping("/getAppVersion")
    public Object getAppVersion() {
        List<Map<String, Object>> appVersions = Arrays.stream(MarketingActivityV2DTO.AppVersion.values())
                .map(appVersion -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("value", appVersion.getValue());
                    map.put("description", appVersion.getDesc());
                    return map;
                })
                .collect(Collectors.toList());
        return RestResponseTypeRef.ofSuccess(appVersions);
    }
    @PostMapping(value = "/insert", consumes = "multipart/form-data")
    public Object insert(@ModelAttribute @Valid MarketingActivityV2DTO dto) {
        try {
            logger.info("===>insert: {}", JSONObject.toJSONString(dto));
            marketingActivityV2Business.insert(dto);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/delete")
    public Object delete(Long id) {
        try {
            marketingActivityV2Business.deleteByPrimaryKey(id);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @PostMapping(value = "/update", consumes = "multipart/form-data")
    public Object update(@ModelAttribute MarketingActivityV2DTO dto) {
        try {
            marketingActivityV2Business.update(dto);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            logger.info(e.getMessage());
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/updateEnable")
    public Object updateEnable(Long id, Boolean enableSwitch) {
        try {
            marketingActivityV2Business.updateEnable(id, enableSwitch);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/selectEnableActivity")
    public Object selectEnableActivity(@RequestParam(name = "shopType") Integer shopType,
                                       @RequestParam(name = "shopId") Long shopId,
                                       @RequestParam(name = "csNick") String csNick,
                                       @RequestParam(name = "currentTime") String currentTime) {
        try {

            if (null == shopType) {
                return RestResponseTypeRef.ofFail();
            }
            // Define the formatter matching the input string
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localDateTime = LocalDateTime.parse(currentTime, formatter);
            List<MarketingActivityV2RespDTO> marketingActivityDTOS = marketingActivityV2Business.selectEnableActivity(shopId, shopType, localDateTime, csNick);
            return RestResponseTypeRef.ofSuccess(marketingActivityDTOS);
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/selectActivityByActivityNameAndDate")
    public Object selectActivityByActivityNameAndDate(@RequestParam(name = "startDate", required = true) String startDate,
                                                      @RequestParam(name = "endDate", required = true) String endDate,
                                                      @RequestParam(name = "activityName", required = true) String activityName,
                                                      @RequestParam(name = "shopType", required = false) Integer shopType) {
        try {
            Date sDate;
            Date eDate;
            try {
                if (null == shopType) {
                    shopType = Integer.valueOf(CommonConstants.SHOP_TYPE_POP_STR);
                }
                sDate = DateUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");
                eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_SF_03_20, RestApiResponse2.of(false));
            }
            activityName = StrUtil.isNotBlank(activityName) ? activityName.trim() : activityName;
            List<MarketingActivityV2RespDTO> marketingActivityDTOS = marketingActivityV2Business.selectActivityByActivityNameAndDate(sDate, eDate, activityName, shopType);
            return RestResponseTypeRef.ofSuccess(marketingActivityDTOS);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return RestResponseTypeRef.ofFail();
        }
    }
}
