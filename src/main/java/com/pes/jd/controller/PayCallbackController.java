package com.pes.jd.controller;

import com.pes.jd.business.PayCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * @Author: aiJun
 * @Date: 2019-09-21 14:56
 * 支付成功回调控制器，不做权限校验可由外网直接访问
 * @Version 1.0
 */
@Slf4j
@Controller
@RequestMapping("/pay/callback/**")
public class PayCallbackController {

    @Autowired
    private PayCallbackService payCallbackService;

    /**
     * 微信支付的回调
     * @param request
     */
    @RequestMapping(value = "wxpay/notify")
    @ResponseBody
    public Object wxpayNotify(HttpServletRequest request) {
        String notityXml = "";
        Object respXml = "";
        log.info("WxPay支付回调方法进入--->{}",request.toString());
        try (BufferedReader br = new BufferedReader(new InputStreamReader(request.getInputStream()))) {
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            br.close();
            //sb为微信返回的xml
            notityXml = sb.toString();
            log.info("接收到的报文：{}", notityXml);
        } catch (IOException e) {
            log.error("接收到的xml报文获取失败->>>{}", e.getMessage(), e);
        }
        try {
            respXml = payCallbackService.wxpayNotify(notityXml);
            log.info("即将返回给微信的respXml=【{}】", respXml);
        } catch (Exception e) {
            log.error("本系统rest发送错误返回错误{}", e.getMessage(), e);
        }
        return respXml;
    }
}
