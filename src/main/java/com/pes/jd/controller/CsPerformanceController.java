/**
 * Project Name:jd-pes
 * File Name:CsPerformanceController.java
 * Package Name:com.pes.jd.controller
 * Date:2018年10月25日下午3:02:34
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.
 */

package com.pes.jd.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.SerializeFilter;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.pes.jd.business.sub.CsPerformanceBusiness;
import com.pes.jd.business.sub.CsPerformancePreordainBusiness;
import com.pes.jd.framework.StopWatch;
import com.pes.jd.framework.ThreadLocalHelper;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.PerformanceParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.CsPerformanceVo;
import com.pes.jd.model.VO.CsPerformanceVoAvg;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.alibaba.fastjson.JSON.DEFAULT_GENERATE_FEATURE;
import static com.pes.jd.model.Enum.ApiCodeEnum.*;
import static com.pes.jd.util.DateUtils.TIME_FORMATTER;

/**
 * 客服绩效
 * ClassName:CsPerformanceController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 下午3:02:34 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("/cs/performance")
public class CsPerformanceController extends BaseController {

    private final static Logger LOGGER = LoggerFactory.getLogger(CsPerformanceController.class);

    @Autowired
    private CsPerformanceBusiness csPerformanceBusiness;

    @Autowired
    private CsPerformancePreordainBusiness csPerformancePreordainBusiness;

    @PostMapping("select")
    public Object getCsperformance(
            @RequestBody(required = false) PerformanceParam param,
            Date startDate, Date endDate, Long shopId, Integer flag, String schemaId
    ) {
        try {
            final StopWatch stopWatch = ThreadLocalHelper.STOP_WATCH.get();
            stopWatch.start("cs-performance");
            LOGGER.debug(" --- 客服绩效 --- 进入方法 -> ");
            String format = flag == 0 ? "yyyy-MM" : "yyyy-MM-dd";
            final Map<?, ?> result = csPerformanceBusiness.searchClientServicePerformance(
                    param, startDate, endDate, shopId, flag, schemaId);
            final ApiResponse of = ApiResponse.of(CODE_SUCCESS_1001, (Map<String, Object>) result);
            LOGGER.debug(" --- 客服绩效 --- 方法结束 -> 耗时：{}毫秒 ", stopWatch.stop());
            final Set<String> propertiesSet = new HashSet<>(param.getProperty());
            propertiesSet.add("csNick");
            propertiesSet.add("date");
            propertiesSet.add("simpleName");
            propertiesSet.add("shopId");
            propertiesSet.add("notFinalData");
            ThreadLocalHelper.STOP_WATCH.remove();
            return JSON.toJSONString(of, SerializeConfig.globalInstance, new SerializeFilter[]{
                    (ValueFilter) (object, name, value) -> {
                        if (value instanceof LocalTime) {
                            return TIME_FORMATTER.format((TemporalAccessor) value);
                        }
                        return value;
                    }
                    ,
                    (PropertyFilter) (object, name, value) -> {
                        if (((object instanceof CsPerformanceVo) || (object instanceof CsPerformanceVoAvg))
                                && !propertiesSet.contains(name)) {
                            return false;
                        }
                        return true;
                    }
            }, format, DEFAULT_GENERATE_FEATURE);
        } catch (Exception e) {
            LOGGER.error("获取客服绩效失败 error :{}", e.getMessage(), e);
            return ApiResponse.of(CODE_ERROR_CUSTOM_REPORT);
        }
    }


    @PostMapping("/selectCsAndShopSaleAmount")
    public ApiResponse selectCsAndShopSaleAmount(
            @RequestParam("shopId") String shopId, @RequestParam("schema") String schema) {
        try {
            Date startDate = DateUtils.getBeginDayOfMonth();
            Date endDate = new Date();
            Map<?, ?> map = csPerformanceBusiness.selectCsAndShopSaleAmount(shopId, startDate, endDate, schema);
            return ApiResponse.of(CODE_SUCCESS_1001, map);
        } catch (Exception e) {
            LOGGER.error("获取店铺客服月汇总失败", e);
            return ApiResponse.of(CODE_ERROR_SY_01_01);
        }
    }


    /**
     * @param shopstr     店铺
     * @param sku         商品id 或者  商品名称
     * @param csNickMapStr
     * @param activityId  活动Id
     * @return
     */
    @RequestMapping(value = "/selectCsPerformancePreordain", method = RequestMethod.POST)
    public ApiResponse selectCsPerformancePreordain(
            @RequestParam(value = "shop", required = true) String shopstr,
            @RequestParam(value = "csNickMap", required = true) String csNickMapStr,
            @RequestParam(value = "activityId", required = true) String activityId,
            @RequestParam(value = "sku", required = false) String sku) {
        try {
            ShopCommonParam shopCommonParam = JacksonUtils.json2pojo(shopstr, ShopCommonParam.class);
            Map<String,String> csNickMap = JSON.parseObject(csNickMapStr, Map.class);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, csPerformancePreordainBusiness.selectCsPerformancePreordain(shopCommonParam, csNickMap, activityId, sku));
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.error("selectCsPerformancePreordain is errr {}", e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_02);
        }
    }


    /**
     * 查询客服预约绩效SKU明细
     *
     * @param shopstr    店铺的
     * @param csNickMapStr
     * @param activityId 活动Id
     * @param skuId
     * @return
     */
    @RequestMapping(value = "/selectCsPerformancePreordainSkuDetailed", method = RequestMethod.POST)
    public ApiResponse selectCsPerformancePreordainSkuDetailed(
            @RequestParam(value = "shop", required = true) String shopstr,
            @RequestParam(value = "csNickMap", required = true) String csNickMapStr,
            @RequestParam(value = "activityId", required = true) String activityId,
            @RequestParam(value = "skuId", required = false) String skuId,
            @RequestParam(value = "skuName", required = false) String skuName) {
        try {
            ShopCommonParam shopCommonParam = JacksonUtils.json2pojo(shopstr, ShopCommonParam.class);
            Map<String,String> csNickMap = JSON.parseObject(csNickMapStr, Map.class);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, csPerformancePreordainBusiness.selectCsPerformancePreordainSkuDetailed(shopCommonParam, csNickMap, activityId, Strings.isBlank(skuId) ? null : Long.valueOf(skuId) , skuName));
        } catch (Exception e) {
            LOGGER.error("selectCsPerformancePreordainSkuDetailed is errr {}", e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_02);
        }
    }


}
  
