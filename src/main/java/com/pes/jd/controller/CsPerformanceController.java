package com.pes.jd.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.*;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.framework.FormUrlencoded;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.CsLoginlogParam;
import com.pes.jd.model.Param.PerformanceParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.net.ColumnType;
import com.pes.jd.net.CsNet;
import com.pes.jd.net.CsPerformanceNet;
import com.pes.jd.net.UserReportPropertyNet;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 客服绩效 - Rest接口
 * @<NAME_EMAIL>
 * @date 2018/12/5 8:58 AM
 * @since 1.0.0
 */
@SuppressWarnings({"SingleElementAnnotation", "Duplicates"})
@RestController
@RequestMapping("/performance/cs")
public class CsPerformanceController extends BaseController{

    private final static Logger LOGGER = LoggerFactory.getLogger(CsPerformanceController.class);

    @Autowired
    private CsNet csNet;

    @Autowired
    private CsPerformanceNet csPerformanceNet;

    @Autowired
    private UserReportPropertyNet userReportPropertyNet;

    @Autowired
	private CsPerformanceBusiness csPerformanceBusiness;
    
    @Autowired
	private ShopSysManagerBusiness shopSysManagerBusiness;

    @Autowired
	private CsShopSystemSettingsBusiness csShopSystemSettingsBusiness;
    
    
    @Autowired
    private CsSpecialPerformanceBusiness csSpecialPerformanceBusiness;

    @Autowired
    private CsPerformancePreordainBusiness csPerformancePreordainBusiness;

	@Autowired
    private ReserveActivityBusiness reserveActivityBusiness;

    @RequestMapping("select")
    public Object select(String shopId, String startDate
            , String endDate, String property, String groupId, String nick, Integer queryType){
        try {
            /**
             *  1 根据shopId groupId nick获取指定 client services -> master
             *  2 根据property修改当前用户的自定义列
             *  3 访问cs-performance业务 -> sub
             */
            List<UserQuery> users = csNet.getCssRpc(shopId, groupId, nick);
            if (CollectionUtils.isEmpty(users)){
                LOGGER.error(" Select Client Service Info is Empty !!!! ");
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_03_01);
            }
            userReportPropertyNet.insertOrUpdate(property, ColumnType.CS_PERFORMANCE.getValue());
            return csPerformanceNet.getCsPerformanceRpc(startDate,endDate,property,queryType,users);
        } catch (Exception e) {
            LOGGER.error(" get client service info error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_03_01,e.getMessage());
        }
    }

    @RequestMapping("detail")
    public Object doDetails(String shopId, String startDate
            , String endDate, String nick, String queryType){
        return csPerformanceNet.getCsDetails(startDate, endDate, nick, queryType,shopId);
    }

	/**  
	 * queryCsDutyRecordCollect:(登录记录总览查询). <br/>  
	 *
	 * @param shopId
	 * @param groupId
	 * @param csNick
	 * @param filterTime	过滤时间段（单位：天）
	 * @param startDate
	 * @param endDate
	 */
	@RequestMapping(value = "/queryCsDutyRecordCollect", method = RequestMethod.POST)
	public ApiResponse queryCsDutyRecordCollect(@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "groupId", required = false) String groupId,
			@RequestParam(name = "csNick", required = false) String csNick,
			@RequestParam(name = "filterTime", required = false) String filterTime,
			@RequestParam(name = "startDate") String startDate,
			@RequestParam(name = "endDate") String endDate) throws LoginAuthException {
		ApiResponse apiResponse;
		UserShopQuery shopQuery = this.getUserShopByParam(shopId,groupId,csNick);
		try {
			List<UserQuery> userQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, groupId, csNick);
			ShopSystemsettingDTO shopSystemsetting = null;
			ApiResponse systemSettingForQN = shopSysManagerBusiness.getSystemSettingForQN(shopQuery.getSelectedShop());
			Map<String, Object> data = systemSettingForQN.getData();
			shopSystemsetting = JacksonUtils.json2pojo(JacksonUtils.obj2json(data.get("shopSystemsettingDTO")), ShopSystemsettingDTO.class);
			CsLoginlogParam csLoginlogParam = new CsLoginlogParam(userQueryLst, shopSystemsetting.getDutyRecordExportUnit(),shopSystemsetting.getSchedulingTimeDot(), null, filterTime);
			apiResponse = csSpecialPerformanceBusiness.queryCsDutyRecordCollect(shopQuery,csLoginlogParam,shopSystemsetting,startDate,endDate);
		} catch (Exception e) {
			LOGGER.error("web - queryCsDutyRecordCollect"+e.getMessage(), e);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_03);
		}
		return apiResponse;
	}

	/**  
	 * queryCsDutyRecordDetail:(登录记录详情查询). <br/>  
	 *
	 * @param shopId
	 * @param groupId
	 * @param csNick
	 * @param dateType		0:月份 ,1:日期
	 * @param filterTime	过滤时间段（单位：天）
	 * @param startDate
	 * @param endDate
	 */
	@RequestMapping(value = "/queryCsDutyRecordDetail", method = RequestMethod.POST)
	public ApiResponse queryCsDutyRecordDetail(@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "groupId") String groupId,
			@RequestParam(name = "csNick") String csNick,
			@RequestParam(name = "dateType") String dateType,
			@RequestParam(name = "filterTime", required = false) String filterTime,
			@RequestParam(name = "startDate") String startDate,
			@RequestParam(name = "endDate") String endDate) throws LoginAuthException {
		ApiResponse apiResponse;
		UserShopQuery shopQuery = this.getUserShopByParam(shopId,groupId,csNick);
		try {
			List<UserQuery> userQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, groupId, csNick);
			ShopSystemsettingDTO shopSystemsetting;
			ApiResponse systemSettingForQN = shopSysManagerBusiness.getSystemSettingForQN(shopQuery.getSelectedShop());
			Map<String, Object> data = systemSettingForQN.getData();
			shopSystemsetting = JacksonUtils.json2pojo(JacksonUtils.obj2json(data.get("shopSystemsettingDTO")), ShopSystemsettingDTO.class);
			CsLoginlogParam csLoginlogParam = new CsLoginlogParam(userQueryLst, shopSystemsetting.getDutyRecordExportUnit(), shopSystemsetting.getSchedulingTimeDot(), dateType, filterTime);

			LOGGER.info("/queryCsDutyRecordDetail--->shopId:"+shopId+",groupId:"+groupId+",csNick"+csNick);
			LOGGER.info("/queryCsDutyRecordDetail--->shopQuery:"+shopQuery);
			LOGGER.info("/queryCsDutyRecordDetail--->userQueryLst:"+userQueryLst);
			LOGGER.info("/queryCsDutyRecordDetail--->shopSystemsetting:"+shopSystemsetting);
			LOGGER.info("/queryCsDutyRecordDetail--->csLoginlogParam:"+csLoginlogParam);
			apiResponse = csSpecialPerformanceBusiness.queryCsDutyRecordDetail(shopQuery,csLoginlogParam,shopSystemsetting,startDate,endDate);
		} catch (Exception e) {
			LOGGER.error("web - queryCsDutyRecordDetail:"+e.getMessage(), e);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_06);
		}
		return apiResponse;
	}
	
	/**  
	 * queryCsLoginOperateDetail:(登录分析上下线详情，针对单个客服，单天查询). <br/>  
	 *
	 * @param shopId
	 * @param groupId
	 * @param csNick
	 * @param startDate
	 * @param endDate
	 * @return
	 * @since JDK 1.8  
	 */
	@RequestMapping(value = "/queryCsLoginOperateDetail", method = RequestMethod.POST)
	public ApiResponse queryCsLoginOperateDetail(@RequestParam(name = "shopId") String shopId,
			@RequestParam(name = "groupId") String groupId,
			@RequestParam(name = "csNick") String csNick,
			@RequestParam(name = "startDate") String startDate,
			@RequestParam(name = "endDate") String endDate) throws LoginAuthException {
		ApiResponse apiResponse;
		UserShopQuery shopQuery = this.getUserShopByParam(shopId,groupId,csNick);
        ShopSystemsettingDTO shopSystemsetting = this.getShopSystemsetting();
        try {
            List<UserQuery> userQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, groupId, csNick);
			apiResponse = csSpecialPerformanceBusiness.queryCsLoginOperateDetail(shopQuery,userQueryLst,shopSystemsetting,startDate,endDate);
			LOGGER.info("return informace code:{}", apiResponse.getRpCode());
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_07);
		}
		return apiResponse;
	}

	private final static Map<String,Object> NO_NICK = new HashMap<>();

	static {
		NO_NICK.put("result",new Object());
		NO_NICK.put("avg",new Object());
		NO_NICK.put("count",new Object());
		NO_NICK.put("setting",new Object());
	}


	@RequestMapping("get_csperformance")
	public Object queryCsPerformance(@FormUrlencoded List<String> property,/*字段*/
									 @FormUrlencoded List<PerformanceParam.FilterTimeBean> filterTime,/*过滤时间*/
									 @FormUrlencoded List<PerformanceParam.Filter>filter ,/*过滤字段，扣除退款。。。*/
									 Date startDate, Date endDate,String csGroup,String csNick,Boolean csContrast,
									 Long shopId, Integer flag,boolean preDefine/*是否查询预定义报表*/) throws LoginAuthException {
		try {
//			MasterServiceShopQuery shop = this.getMasterServiceShopByParam(shopId+"");
//			ApiResponse	apiResponse = shopSysManagerBusiness.getSystemSettingForQN(shop.getSelectedShop());
//			if (!(Objects.equals(apiResponse.getRpCode(), APICodeConstants.CODE_SUCCESS_1001)
//					|| Objects.equals(apiResponse.getRpCode(), APICodeConstants.CODE_SUCCESS_1002))){
//				throw new RuntimeException("获取店铺系统设置失败");
//			}
			ShopDTO currentShop = getCurrentShop();
			if(!getMemberShopIds(currentShop.getShopId()).contains(shopId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
			}
			//******************************修改 start***************************************//
            if (csContrast == null) {
                csContrast = Boolean.FALSE;
            }
			UserShopQuery shopQuery=new UserShopQuery();
            List<UserQuery> csNicks = Lists.newArrayList();
            List<UserQuery> nickTemp;
            ApiResponse apiResponse = csShopSystemSettingsBusiness.selectShopCsAndSystemSettings(shopId, csGroup, csNick, true);
			if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				Object obj = apiResponse.getData().get("csNickLst");
				if (obj != null) {
					nickTemp = JacksonUtils.objTolist(obj, UserQuery.class);
					csNicks.addAll(nickTemp);
				}

			} else {
				LOGGER.error("get csNickLst error:{}", apiResponse.getRpMsg());
			}

			initUserShopQuery(shopQuery,apiResponse,shopId+"");
			//******************************修改   end***************************************//

			String sysSetting = JSON.toJSONString(apiResponse.getData().get("shopSystemsettingDTO"));

			PerformanceParam param = new PerformanceParam();
			param.setFilter(filter);
			param.setSysSetting(sysSetting);
			param.setFilterTime(filterTime);
			param.setProperty(property);
			param.setPreDefine(preDefine);
            if (csContrast) {//是客服对比
                param.setNickQuery(false);
            } else {
                param.setNickQuery(csNick != null && csNick.length() > 0);
            }
//			final UserShopQuery shopQuery = this.getCustUserByParam(String.valueOf(shopId));
//			final List<UserQuery> csNicks = shopSysManagerBusiness
//					.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, csGroup, csNick);
			// 如果没有客服
			if (CollectionUtils.isEmpty(csNicks)){
				return JSON.toJSON(ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,NO_NICK));
			}
			Map<String,String> saleBefore = new HashMap<>();
			Map<String,String> saleAfter = new HashMap<>();
			if (org.apache.commons.collections.CollectionUtils.isNotEmpty(csNicks)) {
				for (UserQuery nick : csNicks) {
					/*售前客服*/
					saleBefore.put(nick.getNick(),nick.getSimpleName());
					if (Objects.equals(nick.getType(),2)){
						/*售后客服*/
						saleAfter.put(nick.getNick(),nick.getSimpleName());
					}
				}
			}
			param.setNicks(saleBefore);
            param.setAfterSaleNicks(saleAfter);
			return csPerformanceBusiness.queryCsPerformance(shopQuery,param,startDate,endDate,flag);
		} catch (LoginAuthException ee) {
			throw ee;
		} catch (Exception e) {
			LOGGER.error(" get_csperformance error  ", e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_01);
		}

	}

	private void initUserShopQuery(UserShopQuery custUser, ApiResponse apiResponse, String shopIdStr) throws LoginAuthException {
		LOGGER.info("initUserShopQuery shopIdStr={}",shopIdStr);
		if (apiResponse.getData().get("shop") != null) {
			Map<String, Object> map = apiResponse.getData();
			Object obj = map.get("shop");
			custUser.setSelectedShop(!StringUtils.isBlank(shopIdStr));
			custUser.setCurrentShop(revertShop(this.getCurrentShop(), this.getMainUser()));
			custUser.setMainShop(revertShop(this.getMainShop(), this.getMainUser()));
			if (custUser.isSelectedShop()) {
				custUser.setSelectedShop(getSelectedShop(custUser.getMainShop(), Long.valueOf(shopIdStr)));
			}
			final ShopDTO shop = JSON.parseObject(JSON.toJSONString(obj), ShopDTO.class);

			ShopQuery shopQuery = new ShopQuery();
			shopQuery.setDbName(shop.getDb());
			shopQuery.setSchemaId(shop.getSchemaId());
			shopQuery.setShopId(shop.getShopId());
			shopQuery.setRtSchemaId(shop.getRtSchemaId());
			shopQuery.setRtDbName(shop.getRtDb());
			shopQuery.setTitle(shop.getTitle());
			shopQuery.setLastGetDateTime(shop.getPreviousGetDataTime());
			shopQuery.setSubuserNum(shop.getSubuserNum());
			shopQuery.setSessionKey(shop.getSessionKey());
			custUser.setSelectedShop(shopQuery);
		}
	}

	@RequestMapping("pressure")
	public ApiResponse pressureReport(Long shopId,
									  Date startDate,
									  String csNick,
									  String csGroup,
									  Date endDate,
									  String queryFlag) throws LoginAuthException {

		final UserShopQuery shopQuery = this.getCustUserByParam(shopId == null ? null : String.valueOf(shopId));
		try {
			Assert.notNull(shopId,"shopId not be null");
			final List<UserQuery> csNicks = shopSysManagerBusiness
					.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, csGroup, csNick);
			Set<String> nicks = new HashSet<>();
			if (!CollectionUtils.isEmpty(csNicks)){
				for (UserQuery user : csNicks) {
					nicks.add(user.getNick());
				}
			}
			return csPerformanceBusiness.queryCsPerformancePressure(
					shopQuery,nicks,startDate,endDate,queryFlag,csNick!=null&&!csNick.isEmpty(),csNicks.isEmpty()
			);

		}catch (Exception e){
			LOGGER.error(" pressure error  ",e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_01);
		}

	}




	/**
	 * 查询店铺预约活动
	 *
	 * @param shopId        店铺的
	 * @param sku           商品名称 或者 商品skuId
	 * @param activityId    预约组id
	 * @param type          预约类型：  (1)全部  (2)不预约不可购买 (3)不预约可购买   默认全部 (1)      数据库字段解释   1：预约购买资格（仅预约的用户才可以进行购买）； 3：预约享优惠（为预约的用户绑定令牌，预约用户才可享受令牌价）； 4：预约消息提醒（预约的用户在抢购时可推送消息提醒）5：预约抽签（仅中签用户可购买）
	 * @param startDate     预约起时间
	 * @param endDate       预约止时间
	 * @param conditionType 预约是否有效 : (1)全部   (2)无效/结束  (3)有效   默认全部 (1)
	 * @param status        预约状态: (1)全部  (2)预约进行中 (3)预约结束抢购未开始  (4)抢购进行中  (5)抢购结束
	 * @return 根据条件 返回 店铺 一段时间内的 预约 商品
	 */
	@RequestMapping(value = "/selectReserveActivityByShop", method = RequestMethod.POST)
	public ApiResponse selectReserveActivty(
			@RequestParam(value = "shopId") String shopId,
			@RequestParam(value = "startDate") String startDate,
			@RequestParam(value = "endDate") String endDate,
			@RequestParam(value = "sku", required = false) String sku,
			@RequestParam(value = "activityId", required = false) String activityId,
			@RequestParam(value = "type", required = false) String type,
			@RequestParam(value = "conditionType", required = false) String conditionType,
			@RequestParam(value = "status", required = false) String status) {
		try {
			final ShopQuery shopQuery = getSelectShop(shopId);
			return reserveActivityBusiness.selectReserveActivityByShop(shopQuery, startDate, endDate, sku, activityId, type, conditionType, status);
		} catch (Exception e) {
			LOGGER.error("web selectReserveActivityByShop is errr {}", e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_01);
		}
	}




	/**
	 *
	 * @param shopId 店铺
	 * @param sku 商品id 或者  商品名称
	 * @param csNick
	 * @param activityId  活动Id
	 * @param   csGroup   分组Id
	 * @return
	 */
	@RequestMapping(value = "/selectCsPerformancePreordain",method = RequestMethod.POST)
	public Object selectCsPerformancePreordain(
			@RequestParam(value = "shopId") String shopId,
			@RequestParam(value = "csGroup",required = false)String csGroup,
			@RequestParam(value = "csNick",required = false)String csNick,
			@RequestParam(value = "activityId") String activityId,
			@RequestParam(value = "sku",required = false) String sku){
		try {
			final UserShopQuery shopQuery = this.getCustUserByParam(shopId == null ? null : shopId);

			Map map = Maps.newHashMap();
			final List<UserQuery> csNicks = shopSysManagerBusiness
					.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, csGroup, csNick);
			csNicks.forEach(a ->{
				if (Objects.equals(a.getCsStatus(),1)) {
					map.put(a.getNick(),a.getSimpleName());
				}
			});
			if(CollectionUtil.isEmpty(map))
				return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,new ArrayList<>());
			return csPerformancePreordainBusiness.selectCsPerformancePreordain(shopQuery.getSelectedShop(),map,activityId,sku);
		}catch (Exception e){
			LOGGER.error("web selectCsPerformancePreordain is errr {}",e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_02);
		}
	}



	/**
	 * 查询客服预约绩效SKU明细
	 * @param shopId 店铺的
	 * @param csNick
	 * @param activityId    活动Id
	 * @param sku
	 * @return
	 */
	@RequestMapping(value = "/selectCsPerformancePreordainSkuDetailed",method = RequestMethod.POST)
	public ApiResponse selectCsPerformancePreordainSkuDetailed(
			@RequestParam(value = "shopId") String shopId,
			@RequestParam(value = "csNick") String csNick,
			@RequestParam(value = "activityId") String activityId,
			@RequestParam(value = "sku") String sku){

		try {
			final UserShopQuery shopQuery = this.getCustUserByParam(shopId == null ? null : shopId);

			String skuName = null;
			String skuId = null;
			try {
				Long.valueOf(sku);
				skuId =sku;
			}catch (NumberFormatException e){
				skuName = sku;
			}
            Map map = Maps.newHashMap();
			final List<UserQuery> csNicks = shopSysManagerBusiness
					.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, "", csNick);
			csNicks.forEach(a ->{
				if (Objects.equals(a.getCsStatus(),1)) {
					map.put(a.getNick(),a.getSimpleName());
				}
			});
            if(CollectionUtil.isEmpty(map))
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_KB_00_00,new ArrayList<>());
			return csPerformancePreordainBusiness.selectCsPerformancePreordainSkuDetailed(shopQuery.getSelectedShop(),map,activityId,skuId,skuName);		}catch (Exception e){
			LOGGER.error("web selectCsPerformancePreordainSkuDetailed is errr {}",e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_02);
		}
	}


}
