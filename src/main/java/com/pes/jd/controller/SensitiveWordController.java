package com.pes.jd.controller;

import com.pes.jd.dao.main.KeywordSensitiveDao;
import com.pes.jd.dao.main.ShopSensitiveWordDao;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 敏感词相关的控制器
 */
@Controller
@RequestMapping("/sensitiveWord/*")
public class SensitiveWordController extends BaseController {
	private final Logger logger = LoggerFactory.getLogger(SensitiveWordController.class);
	@Autowired
	private KeywordSensitiveDao keywordSensitiveDao;
	@Autowired
	private ShopSensitiveWordDao shopSensitiveWordDao;
	@RequestMapping("getWordLst")
	@ResponseBody
	public ApiResponse getWordLst(@RequestParam(name = "shopId") Long shopId) {
		logger.info("shopId = " + shopId);
		Map<String, Object> data = new HashMap<>();
		//校验参数
		try {
			if (Objects.isNull(shopId)) {
				throw new IllegalArgumentException("shopId 不能为空！");
			}
		} catch (Exception e) {
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
		}

		try {
			//查询系统默认敏感词校验
			List<String> sysWordLst = keywordSensitiveDao.selectKeyWordByScope(1);
			//自定义的敏感词库
			List<String> customWordLst = shopSensitiveWordDao.selectSensitiveWordByShopId(shopId);
			data.put("sysWordLst", sysWordLst);
			data.put("customWordLst", customWordLst);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_MGC_01_01, data);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}
}
