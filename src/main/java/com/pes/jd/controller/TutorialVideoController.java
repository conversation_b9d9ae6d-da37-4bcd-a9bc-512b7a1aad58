package com.pes.jd.controller;

import com.pes.jd.business.main.TutorialVideoBusiness;
import com.pes.jd.model.DTO.TutorialVideoDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 15:47 2019/10/14
 * @Description:
 */
@RestController
@RequestMapping("/tutorialVideo/*")
public class TutorialVideoController extends BaseController {

    @Autowired
    private TutorialVideoBusiness tutorialVideoBusiness;

    @RequestMapping("select")
    public Object select(String videoName) {
        try {
            List<TutorialVideoDTO> tutorialVideoDTOS = tutorialVideoBusiness.selectVideoByVideoName(videoName);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(tutorialVideoDTOS));
        } catch (Exception e) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_01, RestApiResponse2.of());
        }
    }
}
