package com.pes.jd.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.business.main.ShopBusiness;
import com.pes.jd.business.main.SmartFollowUpBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DO.ShopBatchRemindTaskDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopBatchRemindTaskDTO;
import com.pes.jd.model.DTO.ShopBatchRemindTaskDTO2;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopBatchRemindTaskParam;
import com.pes.jd.model.Param.TaskParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.ShopBatchRemindTaskVO;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.pes.jd.constants.CommonConstants.DD_SEND;
import static com.pes.jd.constants.CommonConstants.SMS_SEND;

/**
 * 待转化池催付任务设置
 */
@RestController
@RequestMapping("/shopBatchRemindTask")
public class ShopBatchRemindTaskController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(ShopBatchRemindTaskController.class);
    @Resource
    private SmartFollowUpBusiness smartFollowUpBusiness;
    @Resource
    private ShopBusiness shopBusiness;

    private static final Set<Integer> SEND_TYPE_SET = Sets.newHashSet(DD_SEND, SMS_SEND);

    /**
     * 任务列表查询所有
     * 1 普通咨询未下单
     * 2 现货下单未付款
     * 3 预售咨询未下单
     * 4 预售下单未付定金
     * 5 预售付定金未付尾款
     * 6 预约商品咨询未预约
     * 7 预约商品未下单
     * 8 预约商品下单未付款
     */
    @RequestMapping("selectTaskLst")
    public Object select(String param) {
        try {
            TaskParam param1 = JacksonUtils.json2pojo(param, TaskParam.class);

            List<ShopBatchRemindTaskDTO> Lst = smartFollowUpBusiness.queryShopBatchRemindTaskLst(param1);

            Map<String, Object> data = new HashMap<>();
            List<ShopBatchRemindTaskVO> list = new ArrayList<>();
            List<Long> toUpdateIdList = Lists.newArrayList();
            List<Long> toUpdateIdList2 = Lists.newArrayList();
            int size = 0;
            int firstPrompt = 0;
            //VO封装
            if (CollUtil.isNotEmpty(Lst)) {
                for (ShopBatchRemindTaskDTO dto : Lst) {
                    if(dto.getSendType() == 0){
                        if(param1.getClickPosition().equals("1"))
                            firstPrompt = dto.getFirstPrompt();
                        continue;
                    }
                    ShopBatchRemindTaskVO vo = new ShopBatchRemindTaskVO();
                    BeanUtil.copyProperties(dto, vo);
                    if(vo.isProblem())
                        size++;
                    //咚咚区分指定或通用商品
                    if(SEND_TYPE_SET.contains(param1.getSendType())){
//                    if (DD_SEND.equals(param1.getSendType())) {
//                    通用商品 1 指定2
                        if (StrUtil.isEmpty(dto.getSkuId())) {
                            vo.setScope(1);
                        } else {
                            vo.setScope(2);
                            if(vo.getDimension() == 1){
                                vo.setSkuIdLst(Arrays.stream(dto.getSkuId().split(",")).map(Long::valueOf).collect(Collectors.toList()));
                            } else if (vo.getDimension() == 2 && StrUtil.isNotEmpty(dto.getWareId())) {
                                vo.setSkuIdLst(Arrays.stream(dto.getSkuId().split(",")).map(Long::valueOf).collect(Collectors.toList()));
                                // if (StringUtils.isNotBlank(dto.getWareId())) {
                                vo.setSpuIdList(Arrays.stream(dto.getWareId().split(",")).map(Long::valueOf).collect(Collectors.toList()));
                                // }
                            } else {
                                continue;
                            }
                        }

                    }else{
                        vo.setScope(1);
                    }

                    //TODO:任务状态存入
                    //维护任务状态	暂停0 进行中1 未进行2 已过期3
                    if (Objects.nonNull(vo.getTaskStartTime()) && new Date().before(vo.getTaskStartTime()) && dto.getIsPermanent() == 0 && dto.getSendType() == 1) {
                        toUpdateIdList2.add(vo.getId());
                        vo.setIsRemind(CommonConstants.SMS_TASK_NOT_STARTED);
                    }else if (Objects.nonNull(vo.getTaskEndTime()) && new Date().after(vo.getTaskEndTime()) && dto.getIsPermanent() == 0) {
                        toUpdateIdList.add(vo.getId());
                        vo.setIsRemind(CommonConstants.SMS_TASK_EXPIRED);
                    }
                    //审核中与未通过状态的任务不展示异常提示
                    if(vo.getIsRemind() == CommonConstants.SMS_TASK_IN_REVIEW || vo.getIsRemind() == CommonConstants.SMS_TASK_NOT_PASS ){
                        vo.setPrompt(false);
                    }
                    list.add(vo);
                }
                smartFollowUpBusiness.updateShopBatchRemindTaskByIds(param1.getShopId().toString(), toUpdateIdList, CommonConstants.SMS_TASK_EXPIRED);
                //与featurev29冲突，暂时注释更新逻辑
                smartFollowUpBusiness.updateShopBatchRemindTaskByIds(param1.getShopId().toString(), toUpdateIdList2, CommonConstants.SMS_TASK_NOT_STARTED);
                if(firstPrompt == 2)
                    smartFollowUpBusiness.updateTaskToDisplayed(param1.getShopId(), 1);
            }
            ShopBatchRemindTaskDTO2 shopBatchRemindTask2 = new ShopBatchRemindTaskDTO2();
            shopBatchRemindTask2.setSize(size);
            shopBatchRemindTask2.setShopBatchRemindTaskList(list);
            shopBatchRemindTask2.setFirstPrompt(firstPrompt);
            data.put("data", shopBatchRemindTask2);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

    @RequestMapping("selectByFlag")
    public Object selectById(@RequestParam(value = "shopId") String shopId,
                             @RequestParam(value = "flag") Integer flag) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, smartFollowUpBusiness.queryShopBatchRemindTaskByFlag(shopId, flag));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }



    @RequestMapping("selectById")
    public Object selectById(@RequestParam(value = "shopId") String shopId,
                             @RequestParam(value = "id") String id) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, smartFollowUpBusiness.queryShopBatchRemindTaskById(shopId, id));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

    @RequestMapping("selectByIds")
    public Object selectByIds(@RequestParam(value = "shopId") String shopId,
                             @RequestParam(value = "id") String id) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, smartFollowUpBusiness.queryShopBatchRemindTaskByIds(shopId, id));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }
    @RequestMapping("selectTaskLstByddWord")
    public Object selectTaskLstByddWord(@RequestParam(value = "shopId") String shopId,
                             @RequestParam(value = "word") String word) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, smartFollowUpBusiness.queryShopBatchRemindTaskByDdWord(shopId, word));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }



    @RequestMapping("deleteById")
    public Object deleteById(@RequestParam(value = "shopId") String shopId,
                             @RequestParam(value = "id") String id,
                             @RequestParam(value = "sendType") String sendType,
                             @RequestParam(value = "consultWordId") String consultWordId,
                             @RequestParam(value = "silenceWordId") String silenceWordId) {
        try {
            smartFollowUpBusiness.deleteById(shopId, id, sendType, consultWordId, silenceWordId);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

//    //新增任务设置
//    @RequestMapping("insert")
//    public Object insert(@RequestBody() ShopBatchRemindTaskDO shopBatchRemindTaskDO) {
//        try {
//            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, smartFollowUpBusiness.insert(shopBatchRemindTaskDO));
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
//        }
//    }
//
//    //默认展示构造
//    @RequestMapping("selectDefaultTask")
//    public Object selectDefaultTask(String shopId, String sendType, String taskType) {
//        try {
//            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, smartFollowUpBusiness.selectDefaultTask(Long.valueOf(shopId), Integer.valueOf(sendType), Integer.valueOf(taskType)));
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
//        }
//    }

    //0;暂停 1:恢复
    @RequestMapping("updateIsRemind")
    public Object updateIsRemind(
            @RequestParam(value = "shopId") String shopId,
            @RequestParam(value = "id") String id,
            @RequestParam(value = "isRemind") String isRemind) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, smartFollowUpBusiness.updateIsRemind(shopId, id, isRemind));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

    //新建任务
    @RequestMapping("insertOrUpdateTask")
    public Object insertOrUpdateTask(
            @RequestParam(value = "task") String task) {
        try {
            ShopBatchRemindTaskParam taskParam = JacksonUtils.json2pojo(task, ShopBatchRemindTaskParam.class);
            int i = 0;
            if (taskParam.getId() == null) {// 插入有同名的不准插入
                i += smartFollowUpBusiness.getShopBatchRemindTask(taskParam.getName().trim(), taskParam.getShopId());
            }
            ShopBatchRemindTaskDO taskDO = new ShopBatchRemindTaskDO();
            BeanUtil.copyProperties(taskParam, taskDO);
            if (StrUtil.isNotEmpty(taskParam.getCouponStartTime())) taskDO.setCouponStartTime(DateFormatUtils.parseYMd(taskParam.getCouponStartTime()));
            if (StrUtil.isNotEmpty(taskParam.getCouponEndTime())) taskDO.setCouponEndTime(DateFormatUtils.parseYMd(taskParam.getCouponEndTime()));
            if (StrUtil.isNotEmpty(taskParam.getTaskStartTime())) taskDO.setTaskStartTime(DateUtils.getStartTimeOfDate(DateUtils.parseYMd(taskParam.getTaskStartTime())));
            if (StrUtil.isNotEmpty(taskParam.getTaskEndTime())) taskDO.setTaskEndTime(DateUtils.getEndTimeOfDate(DateUtils.parseYMd(taskParam.getTaskEndTime())));
            if(StringUtils.isNotBlank(taskParam.getConsultWordId())) taskDO.getSmsWordIdList().add(Long.valueOf(taskParam.getConsultWordId()));
            if(StringUtils.isNotBlank(taskParam.getSilenceWordId())) taskDO.getSmsWordIdList().add(Long.valueOf(taskParam.getSilenceWordId()));

            //同名的不给存
            if (i > 0) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_GD_01_01);
            }
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, smartFollowUpBusiness.insertOrUpdateTask(taskDO));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

    //快速开启
    @RequestMapping("quickOpening")
    public Object quickOpening(
            @RequestParam(value = "shopId") String shopId,
            @RequestParam(value = "sendType") String sendType,
            @RequestParam(value = "open") String open) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, smartFollowUpBusiness.quickOpening(shopId, sendType, Boolean.valueOf(open)));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

    //是否需要一键开启
    @RequestMapping("ifQuickOpening")
    public Object ifQuickOpening(
            @RequestParam(value = "shopId") String shopId,
            @RequestParam(value = "sendType") String sendType) {
        try {
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, smartFollowUpBusiness.ifQuickOpening(shopId, sendType));
            of.setSuccess(Boolean.TRUE);
            return of;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
            of.setSuccess(Boolean.FALSE);
            return of;
        }
    }

    @RequestMapping("/selectShopBatchRemindTaskState")
    public Object selectShopRemindTaskState(@Param("shopId") String shopId,
                                            @Param("type") String type){
        try{
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, smartFollowUpBusiness.selectShopRemindTaskState(shopId, type));
            of.setSuccess(true);
            return of;
        }catch (Exception e){
            logger.error("selectShopRemindTaskState error shopId :{}, error :{}", shopId, e.getMessage(), e);
            ApiResponse of = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
            of.setSuccess(false);
            return of;
        }
    }

    @RequestMapping("/readNotice")
    public void readNotice(@RequestParam("shopId") String shopId){
        try {
            smartFollowUpBusiness.readNotice(Long.valueOf(shopId));
        }catch (Exception e){
            logger.error("readNotice error shopId :{}, error :{}", shopId, e.getMessage(), e);
        }
    }

    @RequestMapping("/clickPrompt")
    public void clickPrompt(@RequestParam("id") String id){
        try {
            smartFollowUpBusiness.clickPrompt(Long.valueOf(id));
        }catch (Exception e){
            logger.error("clickPrompt error shopId :{}, error :{}", id, e.getMessage(), e);
        }
    }

    @RequestMapping("/updateNoticeAndPromptById")
    public void updateNoticeAndPromptById(@RequestParam("shopId") String shopId,
                                          @RequestParam("needToUpdateTaskIdList") String needToUpdateTaskIdListString){
        try {
            List<Long> needToUpdateTaskIdList = JSONObject.parseArray(needToUpdateTaskIdListString, Long.class);
            int firstPrompt = smartFollowUpBusiness.selectDefaultTask(Long.valueOf(shopId));
            if(firstPrompt == 0){
                smartFollowUpBusiness.updateTaskToDisplayed(Long.valueOf(shopId), 2);
            }
            smartFollowUpBusiness.updateNoticeAndPromptById(Long.valueOf(shopId), needToUpdateTaskIdList, 1);
        }catch (Exception e){
            logger.error("updateNoticeAndPromptById error shopId :{}, error :{}", shopId, e.getMessage(), e);
        }
    }
}
