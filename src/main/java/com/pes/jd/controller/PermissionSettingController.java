package com.pes.jd.controller;

import com.pes.jd.business.main.HomePageBusiness;
import com.pes.jd.business.main.PermissionSettingBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.PermissionSettingControlVo;
import com.pes.jd.util.GrowAssert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/17 4:43 PM
 * @since 1.0.0
 */
@SuppressWarnings("ALL")
@RestController
@RequestMapping("/setting/permission")
public class PermissionSettingController {
    private final static Logger LOGGER = LoggerFactory.getLogger(PermissionSettingController.class);
    @Autowired
    private PermissionSettingBusiness permissionSettingBusiness;
    @Autowired
    private HomePageBusiness homePageBusiness;
    /**
     *  验证访问码
     */
    @RequestMapping("visit_code/valid")
    public Object validVisitCode(String visitCode,Long shopId){
        try {
            Assert.notNull(visitCode," 访问码不能为空 ");
            Assert.notNull(shopId," 店铺不能为空 ");
            final boolean res = permissionSettingBusiness.validateCode(visitCode, shopId);
            if (res){
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1003,"登录成功",res);
            }
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"密码不正确，请重新输入",res);
        } catch (Exception e) {
            LOGGER.error(" valid visit code error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_PM_01_03);
        }
    }

    /**
     *  设置访问吗
     */
    @RequestMapping("visit_code/find")
    public Object findVisitCode(Long shopId,String code){
        try {
            Assert.notNull(shopId," 店铺不能为空 ");
            permissionSettingBusiness.findCode(shopId,code);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" find visit code error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_PM_01_02);
        }
    }

    /**
     *  获取shopId的email
     *   2个作用：  第一个判断该店铺是否有访问码（如果清除访问码的话会将   访问邮箱和访问密码清除掉）
     *             第二个给前端显示访问邮箱
     */
    @RequestMapping("visit_code/get_email")
    public Object findVisitEmail(Long shopId){
        try {
            Assert.notNull(shopId," 店铺不能为空 ");
            Object result = permissionSettingBusiness.findEmail(shopId);
            Map<String,Object> res = new HashMap<>();
            res.put("result",result);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,res);
        } catch (Exception e) {
            LOGGER.error(" find visit email error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"获取Email失败");
        }
    }

    /**
     *
     * @param flag  1 设置访问码  2 清除访问码
     */
    @RequestMapping("visit_code/setting")
    public Object settingVisitCode(String visitCode,String visitEmail,Integer flag,Long shopId){
        try {
            Assert.notNull(flag," flag 不能为空 ");
            Assert.notNull(shopId," 店铺不能为空 ");
            final Integer setting = 1;
            if (Objects.equals(setting,flag)){
                Assert.notNull(visitCode," 访问码不能为空 ");
                Assert.notNull(visitEmail," 访问码找回邮箱不能为空 ");
            }
            permissionSettingBusiness.settingCode(visitCode, visitEmail, flag, shopId);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(" setting visit code error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,flag == 1 ? "设置访问码失败" : "清除访问码失败");
        }
    }

    /**
     * 获取权限的基本信息
     * @param shopId
     * @return
     */
    @RequestMapping("search_tree")
    public Object searchBaseTreeInfo(Long shopId,String nick,Long userId){
        try {
            Assert.notNull(shopId," 店铺不能为空 ");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,
                    permissionSettingBusiness.searchBaseInfo(shopId,nick,userId));
        }catch (Exception e){
            LOGGER.error(" search base info error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"查询权限设置基本信息失败");
        }
    }
    /**
     *  获取子账户权限集合
     * @param shopId
     * @return
     */
    @RequestMapping("search_shop_account_info")
    public Object searchShopAccountInfo(Long shopId,String nick,Integer flag,Long userId){
        try {
            Assert.notNull(shopId," 店铺不能为空 ");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,permissionSettingBusiness
                    .searchShopAccountPermissionsByShopId(shopId,nick,flag,userId));
        }catch (Exception e){
            LOGGER.error(" search shop account info error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"查询账号权限失败");
        }
    }


    /**
     *  增加 修改 删除权限
     * @param type 0 增加 1 修改 2 删除
     * @param permissionSettingControlVo 实体
     * @return
     */
    @RequestMapping("insert_update_delete")
    public Object insertOrUpdatePermission(Integer type, @RequestBody PermissionSettingControlVo permissionSettingControlVo,Long shopId){
        final Integer insert = 0;
        final Integer update = 1;
        final Integer delete = 2;
        final boolean insertDo = Objects.equals(type, insert);
        final boolean updateDo = Objects.equals(type, update);
        final boolean deleteDo = Objects.equals(type, delete);
        try {
            Assert.notNull(type," 执行类型不能为空 ");
            GrowAssert.between(type,insert,delete," type parameter error ");
            if (insertDo){
                // insert
                permissionSettingBusiness.addShopAccountPermission(
                        permissionSettingControlVo.getShopAccounts(),
                        permissionSettingControlVo.getPermissions(),
                        permissionSettingBusiness.processSubMenuResources(permissionSettingControlVo.getMenuResources(), shopId)
                );
            }else {

                if (updateDo){
                    // update
                    permissionSettingBusiness.updateShopAccountPermission(
                            permissionSettingControlVo.getShopAccounts(),
                            permissionSettingControlVo.getPermissions(),
                            permissionSettingBusiness.processSubMenuResources(permissionSettingControlVo.getMenuResources(),shopId)
                    );
                }else {

                    if (deleteDo){
                        // delete
                        permissionSettingBusiness.deleteShopAccountPermission(
                                permissionSettingControlVo.getShopAccounts()
                        );
                    }
                }
            }
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            LOGGER.error(getMessage(type),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,insertDo?"增加客服权限失败":(updateDo?"修改客服权限失败":(deleteDo?"删除客服权限失败":"UNDO")));
        }

    }

    @RequestMapping("/admin")
    public Object admin(String nick){
        homePageBusiness.intiAdmin(nick);
        return "OK";
    }

    @RequestMapping("/user")
    public Object user(String nick){
        homePageBusiness.initUserPermission(nick);
        return "OK";
    }

    private String getMessage(Integer type) {
        if (Objects.equals(type,0)){
            return " insert permission error ";
        }else if (Objects.equals(type,1)){
            return " update permission error ";
        }else if (Objects.equals(type,2)){
            return " delete permission error ";
        }
        return null;
    }


}
