/**
 * Project Name:jd-pes
 * File Name:UserManageController.java
 * Package Name:com.pes.jd.controller
 * Date:2018年10月25日下午3:12:09
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.
 */

package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.UserBusiness;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * ClassName:UserManageController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 下午3:12:09 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/user/manage")
public class UserManageController {
    private static final Logger logger = LoggerFactory.getLogger(UserManageController.class);
    @Autowired
    private UserBusiness userBusiness;
    @RequestMapping("/selectUser")
    public ApiResponse selectUser(@RequestParam("shopId") String shopId) {
        List<ShopUserDTO> userLst;
        try {
            userLst = userBusiness.selectUserByshopId(shopId);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, userLst);
        } catch (Exception e) {
            logger.error("master selectUserByShopId error:" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01, new ArrayList<ShopUserDTO>(0));
        }
    }

    @RequestMapping("/selectShopCreateTime")
    public ApiResponse selectShopCreateTime(@RequestParam("shopId") Long shopId, @RequestParam("date") Integer date) {
        Date createTime;
        Map<String, Object> map = Maps.newHashMap();
        try {
            createTime = userBusiness.calculateShopCreateTimeByDate(shopId, date);
            map.put("create", createTime);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, map);
        } catch (Exception e) {
            logger.error("master  selectShopCreateTime error: " + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01, map);
        }
    }

    @RequestMapping("/getUserInterfaceType")
    public Object getUserInterfaceType(@RequestParam("nick") String nick) {
        try {
            return RestResponseTypeRef.ofSuccess(userBusiness.getUserInterfaceTypeByNick(nick));
        } catch (Exception e) {
            logger.error("master getUserInterface error:" + e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("/saveUserInterfaceType")
    public ApiResponse saveUserInterfaceType(@RequestParam("nick") String nick,
                                             @RequestParam("interfaceType") Integer interfaceType) {

        try {
            userBusiness.saveUserInterfaceType(nick, interfaceType);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        } catch (Exception e) {
            logger.error("master getUserInterface error:" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }

    @RequestMapping("/selectUserByShopId")
    public Object selectUserByShopId(@RequestParam("shopId") String shopId) {
        try {
            return RestResponseTypeRef.ofSuccess(userBusiness.selectUserByshopId(shopId));
        } catch (Exception e) {
            logger.error("master selectUserByShopId error:" + e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }


    @RequestMapping("/getSessionkey")
    public Object getSessionkey(Long shopId, String nick , Integer mainAccount){
        try {
            Assert.notNull(shopId,"店铺ID不能为空");
            return RestResponseTypeRef.ofSuccess(userBusiness.getSessionkey(shopId,nick,mainAccount));
        } catch (Exception e) {
            logger.error(" getSessionkey  error ",e);
            return RestResponseTypeRef.ofFail();
        }
    }


    @RequestMapping("/queryMainAccountList")
    public Object queryMainAccountList(@RequestParam("shopId") Long shopId){
        Map<String, Object> data = new HashMap<>();
        ApiResponse apiResponse = new ApiResponse();
        try {
            List<ShopUserDTO> res =  userBusiness.queryMainAccountList(shopId);
            data.put("mainAccountList", res);
            apiResponse.setData(data);
            apiResponse.setSuccess(Boolean.TRUE);

            return RestResponseTypeRef.ofSuccess(data);
        }catch (Exception e) {
            apiResponse.setSuccess(Boolean.FALSE);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_06_13.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_06_13.getMsg());
            logger.error("get jobShop：" + e.getMessage(), e);
        }
        return apiResponse;
    }



}
  
