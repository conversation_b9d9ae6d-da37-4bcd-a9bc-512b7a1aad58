package com.pes.jd.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.business.*;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.util.CommonDateUtils;
import com.pes.jd.util.DateUtil;
import com.yiyitech.support.jcq.SendResult;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.pes.jd.business.ShopManageBusiness;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/insightJob")
public class xxlJobController {

    private final Logger logger = LoggerFactory.getLogger(xxlJobController.class);

    @Autowired
    private ShopManageBusiness shopManageBusiness;

    @Autowired
    private ShopUserBussiness shopUserBussiness;

    @Autowired
    private TaskMessageBusiness taskMessageBusiness;


    private static List<Long> taskTestShopIds = Arrays.asList(29859L, 57108L, 962409L, 125189L, 625260L, 27099L, 94368L, 1000092118L, 1000002989L, 1000000866L, 1000085423L, 1000008567L, 1000081781L);
    private static final Set<Long> BIG_SHOPS = Sets.newHashSet(1000006804L,1000013402L,1000004064L,1000004123L,1000000127L,1000282702L,1000001132L,1000001683L,1000096602L,1000015268L,1000004259L);



    @RequestMapping("/test/run")
    public void demoTest() throws Exception {
        // 业务逻辑
        for (int i = 0; i < 5; i++) {
            System.out.println("beat at: " + i);
        }
        System.out.println("触发完毕!");
    }




    @RequestMapping("/dailyJob/run")
    public void DailyJobTask(@RequestParam(required = false) String shopId,@RequestParam(required = false) String startDate,@RequestParam(required = false) String endDate ) throws Exception {
        System.out.println("每日job触发成功!");
        Boolean popIsYd = true;
        try {
            logger.info("==> daily job start...");
            Date yestoday = CommonDateUtils.getDateByPeriod(new Date(), -1);

            String startDateStr = CommonDateUtils.formatYMd(yestoday);
            String endDateStr = CommonDateUtils.formatYMdHms(CommonDateUtils.getEndTimeOfDate(yestoday));
            if(StringUtils.isNotBlank(startDateStr) && StringUtils.isNotBlank(endDateStr)){
                startDateStr = startDateStr.trim();
                endDateStr = endDateStr.trim();
            }
            List<JobShopDTO> shops = shopManageBusiness.getActiveShopLst();
            List<ShopUserDTO> shos = Optional.ofNullable(shopUserBussiness.queryMainAccountList(null)).orElse(Lists.newArrayList());
            Map<String, Date> map = Maps.newConcurrentMap();
            if (CollectionUtil.isNotEmpty(shos)) {
                map = shos.stream().collect(Collectors.toMap(ShopUserDTO::getShopId, ShopUserDTO::getCreated, (oldv, newv) -> newv));
            }
            //大店铺放最后跑
            List<JobShopDTO> bigShops = new ArrayList<>();
            for (JobShopDTO shop : shops) {
                if(StringUtils.isNotBlank(shopId)   &&  !shop.getShopId().toString().equals(shopId) ){
                    continue;
                }
                if (BIG_SHOPS.contains(shop.getShopId())) {
                    bigShops.add(shop);
                    continue;
                }
                SendResult sendResult;
                try {
                    Date date = map.get(String.valueOf(shop.getShopId()));
                    boolean isYd = false;
                    if (Objects.nonNull(date)) {
                        isYd = DateUtil.isSameDate(yestoday, date);//新用户
                    }
                    sendResult = taskMessageBusiness.sendTaskJobMessageOfRedisFromParams(shop.getShopId() + "", startDateStr, endDateStr,
                            TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType(), !isYd, popIsYd,shop);

                } catch (Exception e) {
                    logger.error("=====>>>>>taskJobSendMessage  error {}<<<<<<=========", e);
                    sendResult = taskMessageBusiness.sendTaskJobMessageOfRedis(shop.getShopId() + "", startDateStr, endDateStr,
                            TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType(), false,shop);
                    continue;
                }
                logger.info("daily job msg send result:" + sendResult.getMessageId());
            }
            //大店铺
            if (!CollectionUtils.isEmpty(bigShops)) {
                for (JobShopDTO shop : bigShops) {
                    SendResult sendResult;
                    try {
                        Date date = map.get(String.valueOf(shop.getShopId()));
                        boolean isYd = false;
                        if (Objects.nonNull(date)) {
                            isYd = DateUtil.isSameDate(yestoday, date);//新用户
                        }
                        sendResult = taskMessageBusiness.sendTaskJobMessageOfRedisFromParams(shop.getShopId() + "", startDateStr, endDateStr,
                                TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType(), !isYd, popIsYd, shop);

                    } catch (Exception e) {
                        logger.error("=====>>>>>taskJobSendMessage  error {}<<<<<<=========", e);
                        sendResult = taskMessageBusiness.sendTaskJobMessageOfRedis(shop.getShopId() + "", startDateStr, endDateStr,
                                TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType(), false, shop);
                        continue;
                    }
                    logger.info("daily job msg send result:bigShop:" + sendResult.getMessageId());
                }
            }
            logger.info("==> daily job start ok...");
        }
        catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }


}
