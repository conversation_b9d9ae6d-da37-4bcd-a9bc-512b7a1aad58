package com.pes.jd.controller;

import com.pes.jd.business.sub.JobRecordBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.JobRecordVO;
import com.pes.jd.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 错误店铺处理
 * @Author: aiJun
 * @Date: 2019-05-15 21:43
 * @Version 1.0
 */
@RequestMapping("/errorShop/")
@RestController
public class ErrorShopController {
    private static Logger logger = LoggerFactory.getLogger(ErrorShopController.class);

    @Resource
    private JobRecordBusiness jobRecordBusiness;


    @RequestMapping("searchJobRecordShopIds")
    private Object searchJobRecordShopIds(
            @RequestParam(value = "SchemaId") String schemaId,
            @RequestParam(value = "type") String type,
            @RequestParam(value = "dateStr") String dateStr){
        Date date;
        final ApiResponse restApiResponse;

        try {
            date = DateUtils.parseYMd(dateStr);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS);
        }


        try {
            final List<Long> shopIds = jobRecordBusiness.searchErrorShopIdByType(schemaId, type, date);
            restApiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,shopIds);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_QUERY_ERRORSHOP);
        }
        //获取这段时间所有失败的所有店铺
        return restApiResponse;
    }

    @RequestMapping("searchJobRecordShop")
    private Object searchJobRecordShop(
            @RequestParam(value = "SchemaId") String schemaId,
            @RequestParam(value = "type") String type,
            @RequestParam(value = "dateStr") String dateStr,
            @RequestParam(value = "handleFailShopJob", defaultValue = "false", required = false) Boolean handleFailShopJob){
        Date date;
        try {
            date = DateUtils.parseYMd(dateStr);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS);
        }

        try {
            List<JobRecordVO> jobRecordVOS = jobRecordBusiness.searchJobRecordShopByType(schemaId, type, date, handleFailShopJob);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,jobRecordVOS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_QUERY_ERRORSHOP);
        }
    }

    @RequestMapping("searchJobRecordShopByShopIds")
    private Object searchJobRecordShopByShopIds(
            @RequestParam(value = "SchemaId") String schemaId,
            @RequestParam(value = "type") String type,
            @RequestParam(value = "dateStr") String dateStr,
            @RequestParam(value = "shopIds") String shopIds
            ){
        Date date;
        List<Long> shopIdLst;

        try {
            date = DateUtils.parseYMd(dateStr);
            if (StringUtils.isEmpty(shopIds)){
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS);
            }
            String substring = shopIds.substring(1, shopIds.length()-1);
            shopIdLst = Arrays.stream(substring.split(",")).map(Long::valueOf).collect(Collectors.toList());
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS);
        }
        try {
            List<JobRecordVO> jobRecordVOS = jobRecordBusiness.searchJobRecordShopByTypeAndShopIdLst(schemaId, type, date,shopIdLst);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,jobRecordVOS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_QUERY_ERRORSHOP);
        }
    }


    @RequestMapping("getJobPullOrCalShopNum")
    private Object getJobPullOrCalShopNum(
            @RequestParam(value = "SchemaId") String schemaId,
            @RequestParam(value = "type") String type,
            @RequestParam(value = "startDateStr") String startDateStr,
            @RequestParam(value = "endDateStr") String endDateStr){
        Date startDate;
        Date endDate;
        try {
            startDate = DateUtils.parseYMd(startDateStr);
            endDate = DateUtils.parseYMd(endDateStr);
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS);
        }

        try {
            long ss = System.currentTimeMillis();
            int totalNum = jobRecordBusiness.getJobPullOrCalShopNum(schemaId, type, startDate, endDate);
            long ee = System.currentTimeMillis();
            logger.info("查询type={}的数量为：{},耗时：{}ms",type,totalNum,(ee-ss));
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,totalNum);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_QUERY_ERRORSHOP);
        }
    }


}
