package com.pes.jd.controller;


import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.sub.OrderCustomerBusiness;
import com.pes.jd.data.api.OrderReMarkOperator;
import com.pes.jd.data.api.ShippingAddressOperator;
import com.pes.jd.data.convert.OrderReMarkConverter;
import com.pes.jd.model.DTO.OrderDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping(value = "/customerHistory")
public class ShopCustomerInfoHistoryController {
    @Autowired
    private OrderCustomerBusiness orderCustomerBusiness;


    private static final Logger logger = LoggerFactory.getLogger(ShopCustomerInfoHistoryController.class);

//    Date creatTime=new Date(1559005815000L);

    @Autowired
    private ShippingAddressOperator shippingAddressOperator;
    
    
    @Autowired
    private OrderReMarkConverter converter;
    
    @Autowired
    private OrderReMarkOperator orderReMarkOperator;
    







    @RequestMapping(value = "/order/getOrderList")
    @ResponseBody
    public ApiResponse getOrderList(
            @RequestParam("shop") String shopStr,
            @RequestParam("buyerNick") String buyerNick,
            @RequestParam("endTime") Long endTime,
            @RequestParam("querySortPageQuery") String sortPageQueryStr
            ) {
        List<OrderDTO> list = new ArrayList<OrderDTO>();
        ShopCommonParam shop=null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            SortPageQuery sortPageQuery = JSONObject.toJavaObject(JSONObject.parseObject(sortPageQueryStr), SortPageQuery.class);
             list =orderCustomerBusiness.getOrderList(shop, buyerNick,new Date(endTime),sortPageQuery);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,list );
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderList error:{}", e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, list);
        }

    }



    @RequestMapping(value = "/order/getOrderCount")
    @ResponseBody
    public ApiResponse getOrderCount(
            @RequestParam("shop") String shopStr,
            @RequestParam("buyerNick") String buyerNick,
            @RequestParam("endTime") Long endTime
            ) {
    	 int count = 0;
        ShopCommonParam shop=null;
        try {
            shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
            count=orderCustomerBusiness.selectOrderCount(shop, buyerNick,endTime);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, count);
        } catch (Exception e) {
            logger.error("ShopCustomerInfoController.getOrderCount error:{}", e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, count);
        }

    }






}
