package com.pes.jd.controller;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.business.GoodsQueryOfSpuBusiness;
import com.pes.jd.business.ReceiveDataAnalysisBusiness;
import com.pes.jd.business.ShopGoodsManagerBussiness;
import com.pes.jd.business.ShopSysManagerBusiness;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.framework.DateFormat;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.GoodsRecommedParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.constant.PesCommonConstant;
import com.pes.jd.ms.utils.DateUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import com.pes.jd.util.SecurityMUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 接待数据分析 - Rest接口
 */
@RestController
@RequestMapping("/data-analysis/receive/")
public class ReceiveDataAnalysisController extends BaseController {

	private static final Logger logger=LoggerFactory.getLogger(ReceiveDataAnalysisController.class);
	@Autowired
	private ReceiveDataAnalysisBusiness receiveDataAnalysisBusiness;
	@Autowired
	private ShopSysManagerBusiness shopSysManagerBusiness;
	@Autowired
	private GoodsQueryOfSpuBusiness goodsQueryOfSpuBusiness;
	@Autowired
	private ShopGoodsManagerBussiness shopGoodsManagerBussiness;

	/**
	 * 商品咨询汇总
	 */
	@RequestMapping(value = "selectGoodsConsultSummary",method = RequestMethod.POST)
	public ApiResponse selectGoodsConsultSummary(@RequestParam("shopId")String shopId,
												 @RequestParam("skuStr") String skuIdStr,
												 @RequestParam("startDate") String startDateStr,
												 @RequestParam("endDate") String endDateStr,
												 @RequestParam("csNick")String csNick,
												 @RequestParam("groupId")String groupIdStr,Byte dimension) throws Exception{//dimension==1 SKU维度
		ApiResponse apiResponse;
		Date startTime = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endTime = DateUtil.getEndDateFromDateStr(endDateStr);
		UserShopQuery shop=this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		if(!Strings.isNullOrEmpty(skuIdStr)){
			skuList=Arrays.stream(skuIdStr.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = null;
		try {
			csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);

		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setStartDate(startTime);
		param.setEndDate(endTime);
		param.setSkuLst(skuList);
		param.setGroupId(groupIdStr);
		param.setDimension(dimension);
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				apiResponse = receiveDataAnalysisBusiness.selectGoodsConsultSummary(shop, param);
			} else {
				apiResponse = goodsQueryOfSpuBusiness.selectGoodsConsultSummary(shop, param);
			}
			return apiResponse;
		} catch (Exception e) {
			logger.error("goods consult summary error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_03);
		}

	}

	/**
	 * 查询商品咨询汇总数据
	 * @param shopId
	 * @param skuIdStr
	 * @param startDateStr
	 * @param endDateStr
	 * @param csNick
	 * @param categoryId
	 * @param groupIdStr
	 * @param dimension
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "selectGoodsConsultSummaryV3", method = RequestMethod.POST)
	public ApiResponse selectGoodsConsultSummaryV4(@RequestParam("shopId")String shopId,
												   @RequestParam("skuStr") String skuIdStr,
												   @RequestParam("startDate") String startDateStr,
												   @RequestParam("endDate") String endDateStr,
												   @RequestParam("csNick")String csNick,
												   @RequestParam(value = "categoryId",required = false) String categoryId,
												   @RequestParam("groupId")String groupIdStr,Byte dimension) throws Exception{//dimension==1 SKU维度
		ApiResponse apiResponse;
		Date startTime = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endTime = DateUtil.getEndDateFromDateStr(endDateStr);
		UserShopQuery shop=this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		List<ShopCategoryTreeDTO> shopCategoryTree= new ArrayList<>();
		if(StringUtils.isNotBlank(categoryId)){
			ApiResponse categoryIdsApiResponse = shopSysManagerBusiness.selectCategoryLstV2(shop.getCurrentShop());
			if (categoryIdsApiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				Object categoryObj = categoryIdsApiResponse.getData().get("categoryLst");
				if (categoryObj != null) {
					shopCategoryTree = JacksonUtils.objTolist(categoryObj, ShopCategoryTreeDTO.class);
				}
			}
		}
		if(!Strings.isNullOrEmpty(skuIdStr)){
			skuList=Arrays.stream(skuIdStr.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = null;
		try {
			csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);

		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setStartDate(startTime);
		param.setEndDate(endTime);
		param.setSkuLst(skuList);
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		param.setGroupId(groupIdStr);
		param.setDimension(dimension);
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				apiResponse = receiveDataAnalysisBusiness.selectGoodsConsultSummaryV3(shop, param);
				if(StringUtils.isNotBlank(categoryId)){
					if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
						List<GoodsConsultSummaryDTO> consultSummaryDTOs = JacksonUtils.objTolist(apiResponse.getData().get("goodsConsultList"), GoodsConsultSummaryDTO.class);
						apiResponse = receiveDataAnalysisBusiness.processCategoryData(shopCategoryTree, Long.valueOf(categoryId), consultSummaryDTOs, shopId);
					}
				}
			} else {
				apiResponse = goodsQueryOfSpuBusiness.selectGoodsConsultSummary(shop, param);
			}
			return apiResponse;
		} catch (Exception e) {
			e.printStackTrace();
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_03);
		}

	}


	/**
	 * 商品咨询汇总 v2
	 */
	@RequestMapping(value = "selectGoodsConsultSummaryV2",method = RequestMethod.POST)
	public ApiResponse selectGoodsConsultSummaryV2(@RequestParam("shopId")String shopId,
												 @RequestParam("skuStr") String skuIdStr,
												 @RequestParam("startDate") String startDateStr,
												 @RequestParam("endDate") String endDateStr,
												 @RequestParam("csNick")String csNick,
												   @RequestParam(value = "categoryId", required = false) String categoryId,
												 @RequestParam("groupId")String groupIdStr,Byte dimension) throws Exception{//dimension==1 SKU维度
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		if(!Strings.isNullOrEmpty(skuIdStr)){
			skuList = Arrays.stream(skuIdStr.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = null;
		try {
			csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		dimension = dimension == null ? PesCommonConstant.DIMENSION_SKU_BYTE : dimension;
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setStartDate(DateUtil.getStartDateFromDateStr(startDateStr));
		param.setEndDate(DateUtil.getEndDateFromDateStr(endDateStr));
		param.setSkuLst(skuList);
		param.setGroupId(groupIdStr);
		param.setDimension(dimension);//初始化sku维度
		try {
			if (Objects.equals(dimension, PesCommonConstant.DIMENSION_SKU_BYTE)) {
				return receiveDataAnalysisBusiness.selectGoodsConsultSummaryV2(shop, param);
			} else {
				return goodsQueryOfSpuBusiness.selectGoodsConsultSummaryV2(shop, param);
			}
		} catch (Exception e) {
			logger.error("goods consult summary error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_03);
		}
	}


	/**
	 * 新商品咨询汇总报表 增加了categoryId筛选
	 */
	@RequestMapping(value = "selectGoodsConsultSummaryV5", method = RequestMethod.POST)
	public ApiResponse selectGoodsConsultSummaryV5(@RequestParam("shopId") String shopId,
												   @RequestParam("skuStr") String skuIdStr,
												   @RequestParam(value = "categoryId", required = false) String categoryId,
												   @RequestParam("startDate") String startDateStr,
												   @RequestParam("endDate") String endDateStr,
												   @RequestParam("csNick") String csNick,
												   @RequestParam("groupId") String groupIdStr, Byte dimension) throws Exception {//dimension==1 SKU维度
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		if (!Strings.isNullOrEmpty(skuIdStr)) {
			skuList = Arrays.stream(skuIdStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = null;
		List<ShopCategoryTreeDTO> shopCategoryTree = new ArrayList<>();
		if (StringUtils.isNotBlank(categoryId)) {
			ApiResponse categoryIdsApiResponse = shopSysManagerBusiness.selectCategoryLstV2(shop.getCurrentShop());
			if (categoryIdsApiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				Object categoryObj = categoryIdsApiResponse.getData().get("categoryLst");
				if (categoryObj != null) {
					shopCategoryTree = JacksonUtils.objTolist(categoryObj, ShopCategoryTreeDTO.class);
				}
			}
		}
		try {
			csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}


		dimension = dimension == null ? PesCommonConstant.DIMENSION_SKU_BYTE : dimension;
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setStartDate(DateUtil.getStartDateFromDateStr(startDateStr));
		param.setEndDate(DateUtil.getEndDateFromDateStr(endDateStr));
		param.setSkuLst(skuList);
		param.setGroupId(groupIdStr);
		param.setDimension(dimension);//初始化sku维度
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		try {
			if (Objects.equals(dimension, PesCommonConstant.DIMENSION_SKU_BYTE)) {
				ApiResponse apiResponse = receiveDataAnalysisBusiness.selectGoodsConsultSummaryV4(shop, param);
				if (StringUtils.isNotBlank(categoryId)) {
					if (apiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
						List<GoodsConsultSummaryV2DTO> consultSummaryV2DTOs = JacksonUtils.objTolist(apiResponse.getData().get("goodsConsultList"), GoodsConsultSummaryV2DTO.class);
						apiResponse = receiveDataAnalysisBusiness.processCategoryDataV2(shopCategoryTree, Long.valueOf(categoryId), consultSummaryV2DTOs);
					}
				}
				return apiResponse;
			} else {
				return goodsQueryOfSpuBusiness.selectGoodsConsultSummaryV2(shop, param);
			}
		} catch (Exception e) {
			logger.error("goods consult summary error{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_03);
		}
	}


    /**
     * 客服咨询分析
     */
    @RequestMapping(value = "selectCsConsultAnalysis",method = RequestMethod.POST)
    public ApiResponse selectCsConsultAnalysis(@RequestParam("shopId")String shopId,
    		@RequestParam("skuStr") String skuIdStr,
    		@RequestParam("startDate") String startDateStr,
    		@RequestParam("endDate") String endDateStr,
    		@RequestParam("csNick")String csNick,
			@RequestParam("groupId")String groupIdStr,Byte dimension
    		) throws Exception{
    	Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
    	Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
    	Long skuId = null;
        if (dimension == null) {
            dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
        }
    	if(!Strings.isNullOrEmpty(skuIdStr)){
    		skuId = Long.valueOf(skuIdStr);
    	}
    	UserShopQuery shop=this.getCustUserByParam(shopId);
    	List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);

    	GoodsConsultParam param = new GoodsConsultParam();
    	param.setStartDate(startDate);
    	param.setEndDate(endDate);
    	param.setSkuId(skuId);
    	param.setCsNickLst(csNickList);
        param.setDimension(dimension);
    	try {
            if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                return receiveDataAnalysisBusiness.selectCsConsultAnalysis(shop, param);
            } else {
                return goodsQueryOfSpuBusiness.selectCsConsultAnalysis(shop, param);
            }
        } catch (Exception e) {
           logger.error("select cs consult analysis error{}",e.getMessage(),e);
           return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_06);
        }
    }


	/**
	 * 客服咨询分析
	 */
	@RequestMapping(value = "selectCsConsultAnalysisV3", method = RequestMethod.POST)
	public ApiResponse selectCsConsultAnalysisV3(@RequestParam("shopId") String shopId,
												 @RequestParam("skuStr") String skuIdStr,
												 @RequestParam(value = "categoryId", required = false) String categoryId,
												 @RequestParam("startDate") String startDateStr,
												 @RequestParam("endDate") String endDateStr,
												 @RequestParam("csNick") String csNick,
												 @RequestParam("groupId") String groupIdStr, Byte dimension
	) throws Exception {
		Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		Long skuId = null;
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		if (!Strings.isNullOrEmpty(skuIdStr)) {
			skuId = Long.valueOf(skuIdStr);
		}
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);

		GoodsConsultParam param = new GoodsConsultParam();
		param.setStartDate(startDate);
		param.setEndDate(endDate);
		param.setSkuId(skuId);
		param.setCsNickLst(csNickList);
		param.setDimension(dimension);
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				return receiveDataAnalysisBusiness.selectCsConsultAnalysisV3(shop, param);
			} else {
				return goodsQueryOfSpuBusiness.selectCsConsultAnalysis(shop, param);
			}
		} catch (Exception e) {
			logger.error("select cs consult analysis error{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_06);
		}
	}



	/**
	 * 客服咨询分析 v2
	 */
	@RequestMapping(value = "selectCsConsultAnalysisV2",method = RequestMethod.POST)
	public ApiResponse selectCsConsultAnalysisV2(@RequestParam("shopId")String shopId,
											   @RequestParam("skuStr") String skuIdStr,
											   @RequestParam("startDate") String startDateStr,
											   @RequestParam("endDate") String endDateStr,
											   @RequestParam("csNick")String csNick,
											   @RequestParam("groupId")String groupIdStr,Byte dimension) throws Exception{
		Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		Long skuId = null;
		//初始化sku维度
		dimension = dimension == null ? PesCommonConstant.DIMENSION_SKU_BYTE : dimension;

		if(!Strings.isNullOrEmpty(skuIdStr)){
			skuId = Long.valueOf(skuIdStr);
		}
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
		GoodsConsultParam param = new GoodsConsultParam();
		param.setStartDate(startDate);
		param.setEndDate(endDate);
		param.setSkuId(skuId);
		param.setCsNickLst(csNickList);
		param.setDimension(dimension);
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				return receiveDataAnalysisBusiness.selectCsConsultAnalysisV2(shop, param);
			} else {
				return goodsQueryOfSpuBusiness.selectCsConsultAnalysisV2(shop, param);
			}
		} catch (Exception e) {
			logger.error("select cs consult analysis error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_06);
		}
	}

    /**
     * 商品咨询明细
     */
    @RequestMapping(value = "selectGoodsConsultDetail",method = RequestMethod.POST)
    public ApiResponse selectCustConsultGoods(@RequestParam("shopId")String shopId,
    		@RequestParam("result")String resultStr,
    		@RequestParam("skuStr") String skuStr,
    		@RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr,
			@RequestParam("csNick")String csNick,
			@RequestParam("groupId")String groupIdStr,
			@RequestParam("customer")String customer,Byte dimension) throws Exception{
    	Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
    	Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
    	Integer result = null;
        if (dimension == null) {
            dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
        }
    	if(!Strings.isNullOrEmpty(resultStr)){
    		result = Integer.parseInt(resultStr);
    	}
		UserShopQuery shop=this.getCustUserByParam(shopId);
    	List<Long> skuList = null;
    	if(!Strings.isNullOrEmpty(skuStr)){
    		skuList=Arrays.stream(skuStr.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    	}
    	List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
    	GoodsConsultParam param = new GoodsConsultParam();
    	param.setCsNickLst(csNickList);
    	param.setCustomer(customer);
    	param.setEndDate(endDate);
    	param.setResult(result);
    	param.setSkuLst(skuList);
    	param.setStartDate(startDate);
    	param.setDimension(dimension);
    	try {
            if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                return receiveDataAnalysisBusiness.selectGoodsConsultDetail(shop, param);
            } else {
                return goodsQueryOfSpuBusiness.selectGoodsConsultDetail(shop, param);
            }
        } catch (Exception e) {
           logger.error("select goods consult detail{} error",e.getMessage(),e);
           return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_04);
        }
    }



	/**
	 * 商品咨询明细有分类功能
	 */
	@RequestMapping(value = "selectGoodsConsultDetailV3",method = RequestMethod.POST)
	public ApiResponse selectCustConsultGoodsV3(@RequestParam("shopId")String shopId,
											  @RequestParam("result")String resultStr,
											  @RequestParam("skuStr") String skuStr,
											  @RequestParam("startDate") String startDateStr,
											  @RequestParam("endDate") String endDateStr,
											  @RequestParam("csNick")String csNick,
											  @RequestParam("groupId")String groupIdStr,
											  @RequestParam(value = "categoryId",required = false) String categoryId,
											  @RequestParam("customer")String customer,Byte dimension) throws Exception{
		Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		Integer result = null;
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		if(!Strings.isNullOrEmpty(resultStr)){
			result = Integer.parseInt(resultStr);
		}
		UserShopQuery shop=this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		if(!Strings.isNullOrEmpty(skuStr)){
			skuList=Arrays.stream(skuStr.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setCustomer(customer);
		param.setEndDate(endDate);
		param.setResult(result);
		param.setSkuLst(skuList);
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		param.setStartDate(startDate);
		param.setDimension(dimension);
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				ApiResponse apiResponse = receiveDataAnalysisBusiness.selectGoodsConsultDetailV3(shop, param);
				return apiResponse;
			} else {
				return goodsQueryOfSpuBusiness.selectGoodsConsultDetail(shop, param);
			}
		} catch (Exception e) {
			logger.error("select goods consult detail{} error",e.getMessage(),e);
			e.printStackTrace();
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_04);
		}
	}



	/**
	 * 商品咨询明细
	 */
	@RequestMapping(value = "selectGoodsConsultDetailV2",method = RequestMethod.POST)
	public ApiResponse selectGoodsConsultDetailV2(@RequestParam("shopId")String shopId,
											  @RequestParam("result")String resultStr,
											  @RequestParam("skuStr") String skuStr,
											  @RequestParam("startDate") String startDateStr,
											  @RequestParam("endDate") String endDateStr,
											  @RequestParam("csNick")String csNick,
											  @RequestParam("groupId")String groupIdStr,
											  @RequestParam("customer")String customer,Byte dimension) throws Exception{
		Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		Integer result = null;
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		if(!Strings.isNullOrEmpty(resultStr)){
			result = Integer.parseInt(resultStr);
		}
		UserShopQuery shop=this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		if(!Strings.isNullOrEmpty(skuStr)){
			skuList=Arrays.stream(skuStr.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setCustomer(customer);
		param.setEndDate(endDate);
		param.setResult(result);
		param.setSkuLst(skuList);
		param.setStartDate(startDate);
		param.setDimension(dimension);
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				return receiveDataAnalysisBusiness.selectGoodsConsultDetailV2(shop, param);
			} else {
				return goodsQueryOfSpuBusiness.selectGoodsConsultDetailV2(shop, param);
			}
		} catch (Exception e) {
			logger.error("select goods consult detail{} error",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_04);
		}
	}


	/**
	 * 商品咨询明细
	 */
	@RequestMapping(value = "selectGoodsConsultDetailV4", method = RequestMethod.POST)
	public ApiResponse selectGoodsConsultDetailV4(@RequestParam("shopId") String shopId,
												  @RequestParam("result") String resultStr,
												  @RequestParam("skuStr") String skuStr,
												  @RequestParam("startDate") String startDateStr,
												  @RequestParam("endDate") String endDateStr,
												  @RequestParam("csNick") String csNick,
												  @RequestParam(value = "categoryId", required = false) String categoryId,
												  @RequestParam("groupId") String groupIdStr,
												  @RequestParam("customer") String customer, Byte dimension) throws Exception {
		Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
		Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
		Integer result = null;
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		if (!Strings.isNullOrEmpty(resultStr)) {
			result = Integer.parseInt(resultStr);
		}
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<Long> skuList = null;
		if (!Strings.isNullOrEmpty(skuStr)) {
			skuList = Arrays.stream(skuStr.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupIdStr, csNick);
		GoodsConsultParam param = new GoodsConsultParam();
		param.setCsNickLst(csNickList);
		param.setCustomer(customer);
		param.setEndDate(endDate);
		param.setResult(result);
		param.setSkuLst(skuList);
		param.setStartDate(startDate);
		param.setDimension(dimension);
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				return receiveDataAnalysisBusiness.selectGoodsConsultDetailV4(shop, param);
			} else {
				return goodsQueryOfSpuBusiness.selectGoodsConsultDetailV2(shop, param);
			}
		} catch (Exception e) {
			logger.error("select goods consult detail{} error", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_04);
		}
	}


	//--------------------------------商品推荐分析start-----------------------------------------------
    /**
     * @Description:（商品推荐汇总）
    *
     */
    @RequestMapping(value="selectGoodsRecommendSummary",method=RequestMethod.POST)
    public ApiResponse selectGoodsRecommendSummary(@RequestParam("shopId") String shopId,
    		@RequestParam("groupId") String  groupId,
    		@RequestParam("csNick") String csNick,@RequestParam("skuIds") String skuIds,
    		@DateFormat Date startDate,@DateFormat Date endDate,Byte dimension) throws LoginAuthException {
			List<Long> skuLst=null;
            if (dimension == null) {
                dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
            }
			Date startTime = DateUtil.getStartTimeOfDate(startDate);
			Date endTime = DateUtil.getEndTimeOfDate(endDate);
			UserShopQuery shop=this.getCustUserByParam(shopId);
			List<String> csNickLst=null;
			try {
				csNickLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupId, csNick);
			} catch (Exception e1) {
				logger.error("get csNickList error{}",e1.getMessage(),e1);
			}
			if(StringUtils.isNotBlank(skuIds)){
	    		skuLst=Arrays.stream(skuIds.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
	    	}
			Map<String, Object> result=Maps.newHashMap();
			GoodsRecommedParam param=new GoodsRecommedParam();
			param.setCsNickLst(csNickLst);
			param.setSkuLst(skuLst);
			param.setStartDate(startTime);
			param.setEndDate(endTime);
            param.setDimension(dimension);
	    	try {
                List<GoodsRecommendSummaryDTO> goodsRecommendSummaryLst;
                if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                    goodsRecommendSummaryLst = receiveDataAnalysisBusiness.selectGoodsRecommendSummary(shop, param);
                } else {
                    goodsRecommendSummaryLst = goodsQueryOfSpuBusiness.selectGoodsRecommendSummary(shop, param);
                }
                result.put("goodsRecommendSummaryLst", goodsRecommendSummaryLst);
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
            } catch (Exception e) {
				logger.error("web selectGoodsRecommendSummary error:{}",e.getMessage(),e);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01,result);
			}
    }


	/**
	 * @Description:（商品推荐汇总）
	 */
	@RequestMapping(value = "selectGoodsRecommendSummaryV2", method = RequestMethod.POST)
	public ApiResponse selectGoodsRecommendSummaryV2(@RequestParam("shopId") String shopId,
													 @RequestParam("groupId") String groupId,
													 @RequestParam("csNick") String csNick, @RequestParam("skuIds") String skuIds,
													 @RequestParam(value = "categoryId", required = false) String categoryId,
													 @DateFormat Date startDate, @DateFormat Date endDate, Byte dimension) throws Exception {
		List<Long> skuLst = null;
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		Date startTime = DateUtil.getStartTimeOfDate(startDate);
		Date endTime = DateUtil.getEndTimeOfDate(endDate);
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<String> csNickLst = null;
		List<ShopCategoryTreeDTO> shopCategoryTree = new ArrayList<>();
		if (StringUtils.isNotBlank(categoryId)) {
			ApiResponse categoryIdsApiResponse = shopSysManagerBusiness.selectCategoryLstV2(shop.getCurrentShop());
			if (categoryIdsApiResponse.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
				Object categoryObj = categoryIdsApiResponse.getData().get("categoryLst");
				if (categoryObj != null) {
					shopCategoryTree = JacksonUtils.objTolist(categoryObj, ShopCategoryTreeDTO.class);
				}
			}
		}
		try {
			csNickLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupId, csNick);
		} catch (Exception e1) {
			logger.error("get csNickList error{}", e1.getMessage(), e1);
		}
		if (StringUtils.isNotBlank(skuIds)) {
			skuLst = Arrays.stream(skuIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		Map<String, Object> result = Maps.newHashMap();
		GoodsRecommedParam param = new GoodsRecommedParam();
		param.setCsNickLst(csNickLst);
		param.setSkuLst(skuLst);
		param.setStartDate(startTime);
		param.setEndDate(endTime);
		param.setDimension(dimension);
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		try {
			List<GoodsRecommendSummaryDTO> goodsRecommendSummaryLst;
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				goodsRecommendSummaryLst = receiveDataAnalysisBusiness.selectGoodsRecommendSummaryV2(shop, param, shopCategoryTree);
			} else {
				goodsRecommendSummaryLst = goodsQueryOfSpuBusiness.selectGoodsRecommendSummary(shop, param);
			}
			result.put("goodsRecommendSummaryLst", goodsRecommendSummaryLst);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
		} catch (Exception e) {
			logger.error("web selectGoodsRecommendSummary error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_01, result);
		}
	}


    /**
     * 商品推荐分析
     */
    @RequestMapping(value="selectGoodsRecommendAnalysis",method=RequestMethod.POST)
    public ApiResponse selectGoodsRecommendAnalysis(@RequestParam("shopId") String shopId,
    		@RequestParam("csNick") String csNick,
    		@DateFormat Date startDate,
    		@DateFormat Date endDate,Byte dimension,String skuIds) throws LoginAuthException {
		ApiResponse apiResponse = null;
		UserShopQuery shop = this.getCustUserByParam(shopId);
		if (StringUtils.isEmpty(csNick) || StringUtils.isEmpty(shopId)) {
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_02);
		}
		Date startTime = DateUtil.getStartTimeOfDate(startDate);
		Date endTime = DateUtil.getEndTimeOfDate(endDate);
		List<String> csNickLst = Lists.newArrayList();
		csNickLst.add(csNick);
		GoodsRecommedParam param = new GoodsRecommedParam();
		param.setCsNickLst(csNickLst);
		param.setStartDate(startTime);
		param.setEndDate(endTime);
		List<Long> skuLst=null;
		if(StringUtils.isNotBlank(skuIds)){
			skuLst=Arrays.stream(skuIds.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		param.setSkuLst(skuLst);
        if (dimension == null) dimension = PesCommonConstant.DIMENSION_SKU_BYTE;
        param.setDimension(dimension);
        try {
            if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                apiResponse = receiveDataAnalysisBusiness.selectGoodsRecommendAnalysis(shop, param);
            } else {
                apiResponse = goodsQueryOfSpuBusiness.selectGoodsRecommendAnalysis(shop, param);
            }
        } catch (Exception e) {
			logger.error("web selectGoodsRecommendAnalysis error:{}", e.getMessage(), e);
		}
		return apiResponse;
	}


    /**
     * @Description:（商品推荐明细）
    *
     */
    @RequestMapping(value="selectGoodsRecommendDetail",method=RequestMethod.POST)
    public ApiResponse selectGoodsRecommendDetail(@RequestParam("shopId") String shopId,
    		@RequestParam("groupId") String  groupId,
    		@RequestParam("csNick") String csNick,
    		@RequestParam("skuIds") String skuIds,
    		@DateFormat Date startDate,
    		@DateFormat Date endDate,
    		@RequestParam("customer") String customer,
    		@RequestParam("result") String result,
    		String propertity,String sortDirection,
    		Long currentPage,Long size ,Byte dimension) throws LoginAuthException {
			List<Long> skuLst=null;
			ApiResponse apiResponse=null;
            if (dimension == null) {
                dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
            }
			Date startTime = DateUtil.getStartTimeOfDate(startDate);
			Date endTime = DateUtil.getEndTimeOfDate(endDate);
			UserShopQuery shop=this.getCustUserByParam(shopId);
			List<String> csNickLst=null;
			try {
				csNickLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupId, csNick);
			} catch (Exception e1) {
				logger.error("get csNickLst error:{}",e1.getMessage(),e1);
			}
			if(StringUtils.isNotBlank(skuIds)){
	    		skuLst=Arrays.stream(skuIds.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
	    	}
			GoodsRecommedParam param=new GoodsRecommedParam();
			param.setCsNickLst(csNickLst);
			param.setSkuLst(skuLst);
			param.setStartDate(startTime);
			param.setEndDate(endTime);
			param.setResult(result);
			param.setCustomer(customer.trim());
			param.setDimension(dimension);
			SortPageQuery sortPageQuery = new SortPageQuery();
			sortPageQuery.setCurrentPage(currentPage);
			sortPageQuery.setPropertity(propertity);
			sortPageQuery.setSize(size);
			sortPageQuery.setSortDirection(sortDirection);
	    	try {
                if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
                    apiResponse = receiveDataAnalysisBusiness.selectGoodsRecommendDetail(shop, param, sortPageQuery);
                } else {
                    apiResponse = goodsQueryOfSpuBusiness.selectGoodsRecommendDetail(shop, param, sortPageQuery);
                }
            } catch (Exception e) {
				logger.error("web selectGoodsRecommendDetail error:{}",e.getMessage(),e);
			}
	    	return apiResponse;
	}


	@RequestMapping(value = "selectGoodsRecommendDetailV2", method = RequestMethod.POST)
	public ApiResponse selectGoodsRecommendDetailV2(@RequestParam("shopId") String shopId,
													@RequestParam("groupId") String groupId,
													@RequestParam("csNick") String csNick,
													@RequestParam("skuIds") String skuIds,
													@RequestParam(value = "categoryId", required = false) String categoryId,
													@DateFormat Date startDate,
													@DateFormat Date endDate,
													@RequestParam("customer") String customer,
													@RequestParam("result") String result,
													String propertity, String sortDirection,
													Long currentPage, Long size, Byte dimension) throws LoginAuthException {
		List<Long> skuLst = null;
		ApiResponse apiResponse = null;
		if (dimension == null) {
			dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
		}
		Date startTime = DateUtil.getStartTimeOfDate(startDate);
		Date endTime = DateUtil.getEndTimeOfDate(endDate);
		UserShopQuery shop = this.getCustUserByParam(shopId);
		List<String> csNickLst = null;
		try {
			csNickLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupId, csNick);
		} catch (Exception e1) {
			logger.error("get csNickLst error:{}", e1.getMessage(), e1);
		}
		if (StringUtils.isNotBlank(skuIds)) {
			skuLst = Arrays.stream(skuIds.split(",")).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		}
		GoodsRecommedParam param = new GoodsRecommedParam();
		param.setCsNickLst(csNickLst);
		param.setSkuLst(skuLst);
		param.setStartDate(startTime);
		param.setEndDate(endTime);
		param.setResult(result);
		param.setCustomer(customer.trim());
		param.setDimension(dimension);
		if (StringUtils.isNotBlank(categoryId)) {
			param.setCategoryId(Long.valueOf(categoryId));
		}
		SortPageQuery sortPageQuery = new SortPageQuery();
		sortPageQuery.setCurrentPage(currentPage);
		sortPageQuery.setPropertity(propertity);
		sortPageQuery.setSize(size);
		sortPageQuery.setSortDirection(sortDirection);
		try {
			if (dimension.equals(PesCommonConstant.DIMENSION_SKU_BYTE)) {
				apiResponse = receiveDataAnalysisBusiness.selectGoodsRecommendDetailV2(shop, param, sortPageQuery);
			} else {
				apiResponse = goodsQueryOfSpuBusiness.selectGoodsRecommendDetail(shop, param, sortPageQuery);
			}
		} catch (Exception e) {
			logger.error("web selectGoodsRecommendDetail error:{}", e.getMessage(), e);
		}
		return apiResponse;
	}



    /**
     * @Description:（成交订单详情）
    *
     */
    @RequestMapping(value="selectDealOrderDetailLst",method=RequestMethod.POST)
    public ApiResponse selectDealOrderDetailLst(@RequestParam("shopId") String shopId,
    		@RequestParam("customer") String customer,
    		@RequestParam("skuId") String skuId,
    		@RequestParam("startDate") String startDate,
    		@RequestParam("endDate") String endDate,
			HttpServletRequest request) throws LoginAuthException {
			ApiResponse apiResponse=null;
			if(StringUtils.isEmpty(customer)||StringUtils.isEmpty(skuId)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_08_02);
			}
			Date startTime = DateUtil.getStartTimeOfDate(DateUtils.parseYMdHms(startDate));
			Date endTime = DateUtil.getEndTimeOfDate(DateUtils.parseYMdHms(endDate));
			UserShopQuery shop=this.getCustUserByParam(shopId);
			GoodsRecommedParam param=new GoodsRecommedParam();
            ShopDTO currentShop = this.getCurrentShop();
            ShopUserDTO currentUser = this.getCurrentUser();
            Object deviceId = this.getDeviceId();
			param.setStartDate(startTime);
			param.setEndDate(endTime);
			param.setCustomer(customer.trim());
			param.setSkuId(skuId);
	    	try {
				//封装上报订单日志需要的参数
                OrderInfoLogUploadParam orderInfoLogUploadParam = initOrderInfoLogUploadParam(request, currentShop, currentUser, deviceId);
	    		apiResponse=receiveDataAnalysisBusiness.selectDealOrderDetailLst(shop, param,orderInfoLogUploadParam);
			} catch (Exception e) {
				logger.error("web selectDealOrderDetailLst error:{}",e.getMessage(),e);
			}
	    	return apiResponse;
    }


    private OrderInfoLogUploadParam initOrderInfoLogUploadParam(HttpServletRequest request, ShopDTO currentShop, ShopUserDTO currentUser, Object deviceId) {
        OrderInfoLogUploadParam orderInfoLogUploadParam = new OrderInfoLogUploadParam();
        orderInfoLogUploadParam.setJdId(currentShop.getTitle());
        orderInfoLogUploadParam.setDeviceId((String) deviceId);
        orderInfoLogUploadParam.setUserId(currentUser.getNick());
        orderInfoLogUploadParam.setUserIp(SecurityMUtil.getIpAddr(request));
        orderInfoLogUploadParam.setTimeStamp(System.currentTimeMillis());
        return orderInfoLogUploadParam;
    }
   //-------------------------------------商品推荐分析end----------------------------------------------------
}
