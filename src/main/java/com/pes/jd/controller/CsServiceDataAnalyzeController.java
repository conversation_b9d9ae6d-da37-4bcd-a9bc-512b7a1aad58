/**
 * @Auther: wenhui
 * @Date: 2019-01-08 16:25
 * @Description:
 */
package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.CsServiceDataAnalyzeBusiness;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.CsReceiveUnSendEvalParam;
import com.pes.jd.model.Param.CsServiceEvalParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 服务分析Controller
 */
@RestController
@RequestMapping("/service/")
public class CsServiceDataAnalyzeController extends BaseController {

    //初始化logger
    private static final Logger logger = LoggerFactory.getLogger(CsServiceDataAnalyzeController.class);

    @Autowired
    private CsServiceDataAnalyzeBusiness csServiceDataAnalyzeBusiness;

    /**
     * 获取聊天关系(会话维度)记录
     *
     * @param shopStr       店铺json
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param csNickListStr 客服昵称list
     * @param customer      顾客ID
     * @param type          =1 未回复 | =2 慢响应 | = 3 长接待
     * @return ApiResponse
     */
    @RequestMapping(value = "selectChatSessionLst", method = RequestMethod.POST)
    public ApiResponse selectChatSessionLst(@RequestParam("shop") String shopStr,
                                            @RequestParam("startDate") String startDate,
                                            @RequestParam("endDate") String endDate,
                                            @RequestParam("csNickListStr") String csNickListStr,
                                            @RequestParam(value = "customer", required = false) String customer,
                                            @RequestParam("type") String type) {
        //声明存储集
        Map<String, Object> result = new HashMap<>();
        try {
            //反序列化成ShopQuery对象
            ShopQuery shop = JacksonUtils.json2pojo(shopStr, ShopQuery.class);
            Date start = DateUtil.getStartDateFromDateStr(startDate);
            Date end = DateUtil.getEndDateFromDateStr(endDate);
            //将格式"a,b,c"转化为list
            List<String> csNickList = Arrays.asList(csNickListStr.split(","));
            //查询会话关系集
            List<CsChatSessionDTO> chatSessions = csServiceDataAnalyzeBusiness.selectChatSessionLst(shop, start, end, csNickList, customer, type);
            result.put("chatSessionLst", chatSessions);
        } catch (Exception e) {
            result.put("result", new ArrayList<CsChatSessionDTO>(0));
            logger.error("sub selectChatSessionLst error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_06_01, result);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
    }


    /**
     * 满意率分析
     */

    @RequestMapping("selectCsServiceEvaluate")
    public ApiResponse selectCsServiceEvaluate(
            @RequestParam("shop") String shopStr,
            @RequestParam("param") String paramStr,
            @RequestParam("sortPageQuery") String sortPageQueryStr) {
        Map<String, Object> data = Maps.newHashMap();
        ShopCommonParam shop = null;
        CsServiceEvalParam param = null;
        SortPageQuery sortPageQuery = null;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            param = JacksonUtils.json2pojo(paramStr, CsServiceEvalParam.class);
            sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
        } catch (Exception e) {
            logger.error("json error", e.getMessage(), e);
        }
        try {
            DataAnalysisVO<CsServiceEvaluateDetailDTO> satisRateVO = csServiceDataAnalyzeBusiness.selectCsServiceEval(shop, param, sortPageQuery);
            data.put("satisRateVO", satisRateVO);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_FW_01_01);
        }
    }

    /**
     * 接待未邀评分析
     */
    @RequestMapping("selectCsReceiveUnSendEval")
    public ApiResponse selectCsReceiveUnSendEval(
            @RequestParam("shop") String shopStr,
            @RequestParam("param") String paramStr,
            @RequestParam("sortPageQuery") String sortPageQueryStr) {
        Map<String, Object> data = Maps.newHashMap();
        ShopCommonParam shop = null;
        CsReceiveUnSendEvalParam param = null;
        SortPageQuery sortPageQuery = null;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            param = JacksonUtils.json2pojo(paramStr, CsReceiveUnSendEvalParam.class);
            sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
        } catch (Exception e) {
            logger.error("json error", e.getMessage(), e);
        }
        try {
            DataAnalysisVO<ReceiveUnSendDTO> receiveUnSendEvalVO = csServiceDataAnalyzeBusiness.selectReceiveUnSendEval(shop, param, sortPageQuery);
            data.put("receiveUnSendEvalVO", receiveUnSendEvalVO);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
        } catch (Exception e) {
            logger.error(e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_FW_01_02);
        }
    }

    /**
     * 留言分析
     */
    @RequestMapping(value = "selectLeaveMessage", method = RequestMethod.POST)
    public ApiResponse selectLeaveMessage(@RequestParam("shop") String shopStr,
                                          @RequestParam("startDateStr") String startDateStr,
                                          @RequestParam("endDateStr") String endDateStr,
                                          @RequestParam(value = "csNickListStr", required = false) String csNickListStr,
                                          @RequestParam(value = "customer", required = false) String customer,
                                          @RequestParam("selectType") Integer selectType) {

        Map<String, Object> leaveMessageLst = new HashMap<>();
        //格式化时间
        try {
            List<String> csNickList = StringUtils.isNotBlank(csNickListStr) ? Arrays.asList(csNickListStr.split(",")) : null;
            Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);

            ShopQuery shop = JacksonUtils.json2pojo(shopStr, ShopQuery.class);

            //查询会话关系集
            leaveMessageLst = csServiceDataAnalyzeBusiness.selectLeaveMessage(shop, startDate, endDate, csNickList, customer, selectType);
        } catch (Exception e) {
            logger.error("sub selectLeaveMessage error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_06_01, leaveMessageLst);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, leaveMessageLst);

    }

    /**
     * 获取店铺客户或客服违规记录
     *
     * @param shopStr       店铺json
     * @param startDate     开始日期
     * @param endDate       结束日期
     * @param csNickListStr 客服昵称list
     * @param customer      顾客ID
     * @param type          =2-客服违规     |       3-顾客辱骂'
     * @return ApiResponse
     */
    @RequestMapping(value = "selectCsWarn", method = RequestMethod.POST)
    public ApiResponse selectCsWarn(@RequestParam("shop") String shopStr,
                                    @RequestParam("startDate") String startDate,
                                    @RequestParam("endDate") String endDate,
                                    @RequestParam("csNickListStr") String csNickListStr,
                                    @RequestParam(value = "customer", required = false) String customer,
                                    @RequestParam(value = "keyword", required = false) String keyword,
                                    @RequestParam(value = "csLst", required = false) String csLstStr,
                                    @RequestParam("type") Byte type) {
        //声明存储集
        Map<String, Object> result = new HashMap<>();
        try {
            //反序列化成ShopQuery对象
            ShopQuery shop = JacksonUtils.json2pojo(shopStr, ShopQuery.class);
            List<CsDTO> csLst = JacksonUtils.json2list(csLstStr, CsDTO.class);
            Date start = DateUtil.getStartDateFromDateStr(startDate);
            Date end = DateUtil.getEndDateFromDateStr(endDate);
            //将格式"a,b,c"转化为list
            List<String> csNickList = Arrays.asList(csNickListStr.split(","));
            //查询记录
            List<CsWarningDTO> csWarningLst = csServiceDataAnalyzeBusiness.selectCsWarnLst(shop, csLst, start, end, csNickList, customer, type, keyword);
            result.put("csWarningLst", csWarningLst);
        } catch (Exception e) {
            result.put("result", new ArrayList<CsWarningDTO>(0));
            logger.error("sub selectChatSessionLst error:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_06_01, result);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
    }
}
