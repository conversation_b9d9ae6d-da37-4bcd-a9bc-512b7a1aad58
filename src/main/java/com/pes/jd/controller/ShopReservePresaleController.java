package com.pes.jd.controller;

import com.alibaba.fastjson.JSON;
import com.pes.jd.business.sub.ShopReservePresaleBusiness;
import com.pes.jd.model.DTO.ReserveActivityDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.CommonParam;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/shop/reservePresale")
public class ShopReservePresaleController extends BaseController{
	private static final Logger logger = LoggerFactory.getLogger(ShopReservePresaleController.class);
	
	@Autowired
	private ShopReservePresaleBusiness shopReservePresaleBusiness;

	

	@RequestMapping("/selectShopReservePresale")
	public Object selectShopReservePresale(@RequestParam(name="shop")String shopStr
			){
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopReservePresaleBusiness.selectShopReservePresale(shop)));
		} catch (Exception e) {
			logger.error("sub selectShopSkuByShopId:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SP_01_01,RestApiResponse2.of(false));
		}
	}
	
	
	@RequestMapping("/selectShopReservePresaleByActId")
	public Object selectShopReservePresaleByActId(@RequestParam(name="shop")String shopStr,
			@RequestParam(name ="resIds") String resIdsStr,
			@RequestParam(name ="preIds") String preIdsStr
			)
			{
		ShopCommonParam shop=null;
		List<String> resIds = null;
		List<String> preIds = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			resIds=JSON.parseArray(resIdsStr,String.class);
			preIds= JSON.parseArray(preIdsStr,String.class);
			
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopReservePresaleBusiness.selectShopReservePresaleByActId(shop,resIds,preIds)));
		} catch (Exception e) {
			logger.error("sub selectShopReservePresaleByActId:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SP_01_01,RestApiResponse2.of(false));
		}
	}


	@RequestMapping("/selectShopReserveAndPresale")
	public Object selectShopReserveAndPresale(@RequestParam(name="shop")String shopStr
	){
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			return RestResponseTypeRef.ofSuccess(shopReservePresaleBusiness.selectShopReserveAndPresale(shop));
		} catch (Exception e) {
			logger.error("sub selectShopReserveAndPresale:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectShopReserveAndPresaleByActivityId")
	public Object selectShopReserveAndPresaleByActiveid(
			@RequestParam(name="shop")String shopStr,
			@RequestParam(name="activityId")String activityId
	){
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			return RestResponseTypeRef.ofSuccess(shopReservePresaleBusiness.selectShopReserveAndPresaleByActiveid(shop,activityId));
		} catch (Exception e) {
			logger.error("sub selectShopReserveAndPresaleByActiveid:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectReserveGoodsFromPra")
	public Object selectReserveGoodsFromPra(@RequestParam(name="shop")String shopStr){
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		}catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<ReserveActivityDTO> reserveActivityGoods = shopReservePresaleBusiness.selectReserveGoodsFromPra(shop);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(reserveActivityGoods));
		} catch (Exception e) {
			logger.error("sub selectShopReserveGoods:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("/selectShopReserve")
	public Object getResDataByShop(@RequestParam(name="shop")String shopStr
	){
		ShopCommonParam shop=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopReservePresaleBusiness.selectShopReserve(shop)));
		} catch (Exception e) {
			logger.error("sub selectShopSkuByShopId:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SP_01_01,RestApiResponse2.of(false));
		}
	}


	@RequestMapping("/presaleActivityIds")
	public Object getPresaleActivityIds(@RequestParam(name="shop")String shopStr,
										@RequestParam(name="param") String paramStr){
		ShopCommonParam shop=null;
		CustConversionParam param =null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, CustConversionParam.class);
		} catch (Exception e1) {
			logger.error("shopJson,paramSet  parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}

		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopReservePresaleBusiness.getPresaleActivityIds(shop, param)));
		} catch (Exception e) {
			logger.error("sub selectShopSkuByShopId:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_CJ_01_01,RestApiResponse2.of(false));
		}
	}
}
