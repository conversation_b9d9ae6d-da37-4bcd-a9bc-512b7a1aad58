package com.pes.jd.controller;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.pes.jd.business.*;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.CustConversionTwoParam;
import com.pes.jd.model.Param.ShopBatchRemindTaskParam;
import com.pes.jd.model.Param.TaskParam;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.ShopGoodsResult;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import com.pes.jd.util.CommonDateUtils;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能跟单控制器
 */
@RestController
@RequestMapping("/smartFollowUp/")
public class SmartFollowUpController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SmartFollowUpController.class);

    @Resource
    private SmartFollowUpBusiness smartFollowUpBusiness;
    @Resource
    private ShopGoodsManagerBussiness shopGoodsManagerBussiness;
    @Resource
    private RealtimeShopPerformanceSummaryBusiness realtimeShopPerformanceSummaryBusiness;
    @Resource
    private ShopSysManagerBusiness shopSysManagerBusiness;
    @Resource
    private ShopBatchRemindBusiness shopBatchRemindBusiness;

    //查询全部任务列表
    @RequestMapping(value = "selectTaskLst")
    public ApiResponse selectTaskLst(TaskParam param) {
        try {
            Assert.notNull(param.getShopId(), " 店铺不能为空 ");
            ShopDTO currentShop = getCurrentShop();
            if(currentShop.getShopId() != param.getShopId().longValue()){
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
            }
            UserShopQuery shopQuery = this.getCustUserByParam(param.getShopId() + "");
            return smartFollowUpBusiness.selectTaskLst(shopQuery.getSelectedShop(), param);
        } catch (Exception e) {
            logger.error("select selectTaskLst: error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }



    //根据状态查询
    @RequestMapping(value = "selectTaskLstByFlag")
    public ApiResponse selectTaskLstByFlag(@RequestParam(value = "shopId") String shopId,
                                           @RequestParam(value = "flag") Integer flag) {
        try {
            Assert.notNull(shopId, " 店铺不能为空 ");
           Assert.notNull(flag,"状态不能为空");
            return smartFollowUpBusiness.selectTaskByFlag(shopId, flag);
        } catch (Exception e) {
            logger.error("select selectTaskLst: error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }






    //查询单个任务列表详情
    @RequestMapping(value = "selectById")
    public ApiResponse selectById(@RequestParam(value = "shopId") String shopId,
                                  @RequestParam(value = "id") String id) {
        try {
            Assert.notNull(shopId, " 店铺不能为空 ");
            Assert.notNull(id, " id不能为空 ");
            return smartFollowUpBusiness.selectById(shopId, id);
        } catch (Exception e) {
            logger.error("select selectById: error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }

    //删除任务
    @RequestMapping(value = "deleteById")
    public ApiResponse deleteById(@RequestParam(value = "shopId") String shopId,
                                  @RequestParam(value = "id") String id,
                                  @RequestParam(value = "sendType") String sendType,
                                  @RequestParam(value = "consultWordId") String consultWordId,
                                  @RequestParam(value = "silenceWordId") String silenceWordId) {
        try {
            Assert.notNull(shopId, " 店铺不能为空 ");
            Assert.notNull(id, " id不能为空 ");
            return smartFollowUpBusiness.deleteById(shopId, id, sendType, consultWordId, silenceWordId);
        } catch (Exception e) {
            logger.error("deleteById : error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        }
    }

    //isRemind  1-恢复 0-暂停
    //任务的暂停和恢复
    @RequestMapping(value = "updateIsRemind")
    public ApiResponse updateIsRemind(
            @RequestParam(value = "shopId") String shopId,
            @RequestParam(value = "id") String id,
            @RequestParam(value = "isRemind") String isRemind) {
        try {
            Assert.notNull(shopId, " 店铺不能为空 ");
            Assert.notNull(id, " id不能为空 ");
            return smartFollowUpBusiness.updateIsRemind(shopId, id, isRemind);
        } catch (Exception e) {
            logger.error("updateIsRemind : error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        }
    }

    //新建任务
    @RequestMapping(value = "insertOrUpdateTask")
    public ApiResponse insertOrUpdateTask(ShopBatchRemindTaskParam param) {
        try {
            Assert.notNull(param.getShopId(), " 店铺不能为空 ");
            UserShopQuery shopQuery = this.getCustUserByParam(String.valueOf(param.getShopId()));
            logger.info(param.toString());
            return smartFollowUpBusiness.insertOrUpdateTask(shopQuery.getSelectedShop(), param);
        } catch (Exception e) {
            logger.error("insertTask : error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        }
    }

    //快速开启-->发送类型 1 咚咚 2 短信
    @RequestMapping(value = "quickOpening")
    public ApiResponse quickOpening(String shopId, String sendType, String open) {
        try {
            logger.info("shopId={},sendType={},open={}", shopId, sendType, open);
            Assert.notNull(shopId, " 店铺不能为空 ");
            Assert.notNull(sendType, " 发送类型不能为空 ");
            Assert.notNull(open, " 开启或关闭方式不能为空 ");
            return smartFollowUpBusiness.quickOpening(shopId, sendType, open);
        } catch (Exception e) {
            logger.error("insertTask : error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        }
    }

    //是否一键开启
    @RequestMapping(value = "ifQuickOpening")
    public ApiResponse ifQuickOpening(@RequestParam(value = "shopId")Long shopId, @RequestParam(value = "sendType")String sendType) {
        try {
            Assert.notNull(shopId, " 店铺不能为空 ");
            Assert.notNull(sendType, " 发送类型不能为空 ");
            return smartFollowUpBusiness.ifQuickOpening(String.valueOf(shopId), sendType);
        } catch (Exception e) {
            logger.error("insertTask : error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
        }
    }

    // 查询sku列表
    @RequestMapping("/selectShopWordGoodsSkuLst")
    public ApiResponse selectShopWordGoodsSkuLst(TaskParam taskParam) {
        Map<String, Object> result;
        try {
            MasterServiceShopQuery shop = this.getMasterServiceShopByParam(taskParam.getShopId() + "");
            result = shopGoodsManagerBussiness.selectShopWordGoodsSkuLstNew(shop, taskParam);
        } catch (Exception e) {
            result = new HashMap<>(0);
            logger.error("SmartFollowUpController selectShopWordGoodsSkuLst error:{}" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
    }


//remind_type 字段为1 send_remind_type 是空 的就是手动跟单
//remind_type 为2 send_remind_type为1 3的是咚咚跟单
//remind_type 为2 send_remind_type为2 3的是短信跟单

    /**
     * @Description:（数据看板趋势图）
     */
    @RequestMapping(value = "/serachDataTrendChart")
    public ApiResponse serachCsConversionTaskTotal(TaskParam taskParam) {
        List<String> skuList = null;
        ApiResponse apiResponse = null;
        try {
            Date startTime = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(taskParam.getStartDate()));
            Date endTime = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(taskParam.getEndDate()));
            UserShopQuery shopQuery = this.getCustUserByParam(taskParam.getShopId() + "");
            CustConversionParam param = new CustConversionParam();
            param.setStartDate(startTime);
            param.setEndDate(endTime);
            param.setSendType(taskParam.getSendType() + "");
            param.setRemindTypeAndSendTypeByFollowUpTypeAndSmsFollowUp(taskParam.getFollowUpType(),"");
            param.setSkuIdList(skuList);
            apiResponse = realtimeShopPerformanceSummaryBusiness.serachCsConversionTaskTotalNew(shopQuery, param);
        } catch (Exception e) {
            logger.info("smartFollowUp serachCsConversionTaskTotal error" + e.getMessage(), e);
        }
        return apiResponse;
    }


    /**
     * 咚咚跟单数据看板
     *
     * @param taskParam
     * @return
     */
    @RequestMapping(value = "/serachAllocatedConvertedLst")
    public ApiResponse serachAllocatedConvertedLst(TaskParam taskParam) {
        List<String> skuList = null;
        if (!Strings.isNullOrEmpty(taskParam.getSkuIds()))
            skuList = Arrays.asList(taskParam.getSkuIds().split(","));
        ApiResponse apiResponse = null;
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNoneBlank(taskParam.getStartDate()) && StringUtils.isNoneBlank(taskParam.getEndDate())) {
            startTime = DateUtil.getStartTimeOfDate(CommonDateUtils.parseYMd(taskParam.getStartDate()));
            endTime = DateUtil.getEndTimeOfDate(CommonDateUtils.parseYMd(taskParam.getEndDate()));
        }
        try {
            UserShopQuery shopQuery = this.getCustUserByParam(taskParam.getShopId() + "");
            CustConversionTwoParam param = new CustConversionTwoParam();
            if (StringUtils.isBlank(taskParam.getGroupId()) && StringUtils.isBlank(taskParam.getCsNick())) {
            } else {
                List<UserQuery> userQuery = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, taskParam.getGroupId(), taskParam.getCsNick());
                List<String> csNickLst = userQuery.stream().map(UserQuery::getNick).collect(Collectors.toList());
                param.setCsNickLst(csNickLst);
            }
            param.setRemindTypeAndSendTypeByFollowUpTypeAndSmsFollowUp(taskParam.getFollowUpType(), taskParam.getOtherFollowUp());
            if (StringUtils.isNotBlank(taskParam.getBuyerNick())) {
                taskParam.setBuyerNick(taskParam.getBuyerNick().trim());
            }
            if (CommonConstants.DIMENSION_SPU_INTEGET.equals(taskParam.getDimension()) && CollUtil.isNotEmpty(skuList)) {
                ShopGoodsResult result = shopBatchRemindBusiness.selectShopGoodsName(shopQuery.getSelectedShop(), new ArrayList<>(), skuList.stream().map(Long::valueOf).collect(Collectors.toList()));
                List<ShopGoodsSku> wareSkuIdLst = result.getWareSkuIdLst();
                if (CollUtil.isNotEmpty(wareSkuIdLst)) {
                    param.setSkuIdList(wareSkuIdLst.stream().map(ele -> ele.getSkuId() + "").collect(Collectors.toList()));
                }
            } else {
                param.setSkuIdList(skuList);
            }



            //是否按处理的结果查询
            if (taskParam.getConversionResult() != null) {
                if(StringUtils.isBlank(taskParam.getConversionResult())){
                    param.setConversionResult(taskParam.getConversionResult());
                }else{
                    try {
                        Integer.parseInt(taskParam.getConversionResult());
                    }catch (Exception e){
                        throw new Exception(e.getMessage());
                    }
                    param.setConversionResult(taskParam.getConversionResult());
                }
            }



            param.setBuyerNick(taskParam.getBuyerNick());
            param.setStartDate(startTime);
            param.setEndDate(endTime);
            param.setWordId(taskParam.getWord());
            if(null != taskParam.getType()){
                param.setType(String.valueOf(taskParam.getType()));
            }else{
                param.setType("");
            }
            param.setOrderType(taskParam.getOrderType());
            try {
                param.setOrderId(StringUtils.isNotBlank(taskParam.getOrderId()) ? taskParam.getOrderId() : null);
            } catch (Exception ee) {
                apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
                Map<String, Object> result = new LinkedHashMap<>();
                result.put("csConversionLst", Collections.EMPTY_LIST);
                apiResponse.setData(result);
                return apiResponse;
            }
            apiResponse = realtimeShopPerformanceSummaryBusiness.serachAllocatedConvertedLstNew(shopQuery, param, taskParam.getFollowUpType());
        } catch (Exception e) {
            logger.info("smartFollowUp serachAllocatedConvertedLst error :{}", e.getMessage(), e);
        }
        return apiResponse;
    }

    /*
     * 记录商家已经点击过任务列表上的通知
     */
    @RequestMapping("/readNotice")
    public void readNotice(@RequestParam("shopId") String shopId){
        smartFollowUpBusiness.readNotice(shopId);
    }

    /*
     * 点击任务列表中任务的“？”
     */
    @RequestMapping("/clickPrompt")
    public void clickPrompt(@RequestParam("id") String id){
        smartFollowUpBusiness.clickPrompt(id);
    }

}

