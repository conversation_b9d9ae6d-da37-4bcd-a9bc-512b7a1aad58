package com.pes.jd.controller;

import cn.hutool.core.collection.CollUtil;
import com.pes.jd.business.main.GroupBusiness;
import com.pes.jd.model.DTO.GroupCsDTO;
import com.pes.jd.model.DTO.GroupDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GroupParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.MultiShopGroupVO;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**  
 * 客服组设置、 查询
 * ClassName:GroupController <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午3:08:07 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@RestController
@RequestMapping("/cs/group")
public class GroupController extends BaseController {
	private final Logger log=LoggerFactory.getLogger(GroupController.class);
	@Autowired
	private GroupBusiness groupBusiness;
	
	@RequestMapping("/queryShopCsGroups")
	public ApiResponse queryShopCsGroups(@RequestParam(name="shopLst") String shopLstJson,
			@RequestParam(name="groupName")String groupName) {
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		List<GroupDTO> groupList;
		List<ShopCommonParam> shopLst;
		try {
			shopLst=JacksonUtils.json2list(shopLstJson, ShopCommonParam.class);
		} catch (Exception e) {
			log.error("queryShopCsGroups parse shopLst error：{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
			
		}
		try {
			groupList=groupBusiness.queryShopCsGroups(shopLst, groupName);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			groupList=new ArrayList<GroupDTO>(0);
			log.error("queryShopCsGroups:" + e.getMessage(), e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		}
		retMap.put("groups", groupList);
		apiResponse.setData(retMap);
		return apiResponse;
	}

	/**
	 * 创建或者更新客服分组
	 * 
	 * @param groupId
	 * @param groupName
	 * @return
	 */
	@RequestMapping("/createOrUpdateShopCsGroup")
	public ApiResponse createOrUpdateShopCsGroup(@RequestParam(required=false,name="groupId")String groupId,
			@RequestParam(name="groupName") String groupName,
			@RequestParam(name="shopId")String shopId) {
		ApiResponse apiResponse;
		try {
			if (StringUtils.isBlank(groupName)) {
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_02);
			} else {
				if (groupName.length() > 20) {
					return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_03);
				}
			}
			GroupDTO group = groupBusiness.getCsGroupByGroupName(Long.valueOf(shopId), groupName, groupId);
			if (group!=null) {
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_06,groupName);
			}
			if (StringUtils.isBlank(groupId)) {// 创建
				groupBusiness.createShopCsGroup(groupName, shopId, false);
			} else {
				groupBusiness.updateShopGroup(Long.valueOf(groupId), groupName, shopId);
			}
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			log.error("createOrUpdateShopCsGroup error information:{}" , e.getMessage(), e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_04);
		}
		return apiResponse;
	}
	
	@RequestMapping("/deleteCsGroup")
	public ApiResponse deleteCsGroup(@RequestParam(name="groupId")Long groupId) {
		try {
			return groupBusiness.deleteCsGroup(groupId);
		} catch (Exception e) {
			log.error("deleteCsGroup error information:{}" , e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_05);
		}
	}
	@RequestMapping("/selectShopGroups")
	public ApiResponse selectShopGroups(@RequestParam(name="shopLst") String shopLstJson) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		List<ShopQuery> shopLst;
		try {
			shopLst=JacksonUtils.json2list(shopLstJson, ShopQuery.class);
		} catch (Exception e) {
			log.error("queryShopCsGroups parse shopLst error：{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
		}
		try {
			List<MultiShopGroupVO> multiShopInfo = groupBusiness.queryMultiShopGroupByCurrentShopId(shopLst);
			map.put("multiShopInfo", multiShopInfo);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			apiResponse.setData(map);
		} catch (Exception e) {
			log.error("getShopGroups error informatin:{}" , e.getMessage());
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
		}
		
		return apiResponse;
	}
	@RequestMapping("/selectMultiShopGroupByShopId")
	public ApiResponse selectMultiShopGroupByShopId(@RequestParam(name="shop") String shopStr) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		ShopCommonParam shop;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
		} catch (Exception e) {
			log.error("selectMultiShopGroupByShopId parse shop error：{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
		}
		try {
			List<MultiShopGroupVO> multiShopInfo = groupBusiness.queryMultiShopGroupByShopId(shop);
			map.put("multiShopInfo", multiShopInfo);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			apiResponse.setData(map);
		} catch (Exception e) {
			log.error("selectMultiShopGroupByShopId error informatin:{}", e.getMessage());
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
		}
		return apiResponse;
	}

	@RequestMapping("/selectShopGroupMemberShops")
	public ApiResponse selectShopGroupMemberShops(@RequestParam("mainShopId") String mainShopId) {
		ApiResponse apiResponse;
		Map<String, Object> result = new HashMap<>();
		try {
			List<ShopDTO> multiShopLst = groupBusiness.selectShopGroupMemberShops(Long.valueOf(mainShopId));
			if(CollUtil.isNotEmpty(multiShopLst)){
				for (ShopDTO shopDTO : multiShopLst) {
				log.info("id={}",shopDTO.getId());
				}
			}
			result.put("multiShopLst", multiShopLst);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			apiResponse.setData(result);
		} catch (Exception e) {
			log.error("selectShopGroupMemberShops error informatin:{}" , e.getMessage());
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
		}
		
		return apiResponse;
	}

	@RequestMapping("/selectGroupCsByGroupParam")
	public ApiResponse selectGroupCsByGroupParam(@RequestParam("param") String paramStr){
		ApiResponse apiResponse;
		Map<String, Object> result = new HashMap<>();;
		GroupParam param;
		try {
			param=JacksonUtils.json2pojo(paramStr, GroupParam.class);
		} catch (Exception e) {
			log.error("selectGroupCsByGroupParam parse shopLst error：{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_15);
		}
		try {
			List<GroupCsDTO> groupCsLst = groupBusiness.selectGroupCsByGroupParam(param);
			result.put("groupCsLst", groupCsLst);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			apiResponse.setData(result);
		} catch (Exception e) {
			log.error("selectGroupCsByGroupParam error informatin:{}" , e.getMessage());
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
		}
		return apiResponse;
	}

	@RequestMapping("/selectGroupIdByShopId")
	public ApiResponse selectGroupIdByShopId(@RequestParam("shopId") String shopId){
		try{
			Long shop = Long.valueOf(shopId);
			Long groupId = groupBusiness.selectGroupIdByShopId(shop);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, groupId);
		}catch (Exception e){
			log.error("获取店铺默认客服分组出错 shoId :{}, error :{}", shopId, e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
		}
	}
}
  
