package com.pes.jd.controller;

import com.pes.jd.business.main.ShopAutoAllocatedSettingBussiness;
import com.pes.jd.model.DTO.AdvanceAutoAllocatedSettingDTO;
import com.pes.jd.model.DTO.AppointmentAutoAllocatedSettingDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopAutoAllocatedSettingParam;
import com.pes.jd.ms.domain.Data.master.ShopAutoAllocatedSettingDTO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年07月30 15:50:50<br>
 */
@RequestMapping("/setting/auto/allocated")
@RestController
public class ShopAutoAllocatedSettingController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ShopAutoAllocatedSettingController.class);
    @Autowired
    private ShopAutoAllocatedSettingBussiness shopAutoAllocatedSettingBussiness;
    @RequestMapping("/saveOrUpdateShopAutoAllocatedSetting")
    public Object saveOrUpdateShopAutoAllocatedSetting(@RequestParam("recordStr") String recordStr){
        ShopAutoAllocatedSettingParam record= null;
        try {
            record = JacksonUtils.json2pojo(recordStr, ShopAutoAllocatedSettingParam.class);
        } catch (Exception e) {
            logger.info("master parse json error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08,RestApiResponse2.of(false));
        }
        try {
            int num= shopAutoAllocatedSettingBussiness.saveOrUpdateShopAutoAllocatedSetting(record);
            if(num==1){
                return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002,RestApiResponse2.of());
            }else{
                return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_01,RestApiResponse2.of(false));
            }
        } catch (Exception e) {
            logger.error("master saveOrUpdateShopAutoAllocatedSetting error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_01,RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopAutoAllocatedSettingByShopId")
    public Object selectShopAutoAllocatedSettingByShopId( @RequestParam("shopId") Long shopId){

        try {
            ShopAutoAllocatedSettingDTO record= shopAutoAllocatedSettingBussiness.selectShopAutoAllocatedSettingByShopId(shopId);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001,RestApiResponse2.of(record));
        } catch (Exception e) {
            logger.error("master selectShopAutoAllocatedSettingByShopId error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_02,RestApiResponse2.of(false));
        }

    }


    @RequestMapping("/selectAppointmentAutoAllocatedSettingByShopId")
    public Object selectAppointmentAutoAllocatedSettingByShopId( @RequestParam("shopId") Long shopId){

        try {
        	AppointmentAutoAllocatedSettingDTO record= shopAutoAllocatedSettingBussiness.selectAppointmentAutoAllocatedSettingByShopId(shopId);
            return RestResponseTypeRef.ofSuccess(record);
        } catch (Exception e) {
            logger.error("master selectShopAutoAllocatedSettingByShopId error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_02,RestApiResponse2.of(false));
        }

    }


    @RequestMapping("/selectAdvanceAutoAllocatedSetting")
    public Object selectAdvanceAutoAllocatedSetting( @RequestParam("shopId") Long shopId){

        try {
        	AdvanceAutoAllocatedSettingDTO record= shopAutoAllocatedSettingBussiness.selectAdvanceAutoAllocatedSetting(shopId);
        	 return RestResponseTypeRef.ofSuccess(record);
        } catch (Exception e) {
            logger.error("master selectShopAutoAllocatedSettingByShopId error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_02,RestApiResponse2.of(false));
        }

    }


    @RequestMapping("/saveOrUpdateAdvanceAutoAllocatedSetting")
    public Object saveOrUpdateAdviceAutoAllocatedSetting(@RequestParam("recordStr") String recordStr){
    	AdvanceAutoAllocatedSettingDTO record;
        try {
            record = JacksonUtils.json2pojo(recordStr, AdvanceAutoAllocatedSettingDTO.class);
        } catch (Exception e) {
            logger.info("master parse json error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08,RestApiResponse2.of(false));
        }

        try {
            int num= shopAutoAllocatedSettingBussiness.saveOrUpdateAdvanceAutoAllocatedSetting(record);
            if(num==1){
                return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002,RestApiResponse2.of());
            }else{
                return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_01,RestApiResponse2.of(false));
            }
        } catch (Exception e) {
            logger.error("master saveOrUpdateShopAutoAllocatedSetting error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_01,RestApiResponse2.of(false));
        }

    }


    @RequestMapping("/saveOrUpdateAppointmentAutoAllocatedSetting")
    public Object saveOrUpdateAppointmentAutoAllocatedSetting(@RequestParam("recordStr") String recordStr){
    	AppointmentAutoAllocatedSettingDTO record= null;
        try {
            record = JacksonUtils.json2pojo(recordStr, AppointmentAutoAllocatedSettingDTO.class);
        } catch (Exception e) {
            logger.info("master parse json error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08,RestApiResponse2.of(false));
        }

        try {
            int num= shopAutoAllocatedSettingBussiness.saveOrUpdateAppointmentAutoAllocatedSetting(record);
            if(num==1){
                return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002,RestApiResponse2.of());
            }else{
                return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_01,RestApiResponse2.of(false));
            }
        } catch (Exception e) {
            logger.error("master saveOrUpdateShopAutoAllocatedSetting error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_AAS_01_01,RestApiResponse2.of(false));
        }

    }


}
