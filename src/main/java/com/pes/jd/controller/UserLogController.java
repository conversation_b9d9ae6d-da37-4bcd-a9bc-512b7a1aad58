package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.ImmutableMap;
import com.pes.jd.business.main.PesUserLoginLogBusiness;
import com.pes.jd.business.main.UserLogBusiness;
import com.pes.jd.framework.DateFormat;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/12 3:20 PM
 * @since 1.0.0
 */
@RestController
@RequestMapping("/user/log")
public class UserLogController extends BaseController{

    private static final Logger LOGGER = LoggerFactory.getLogger(UserLogController.class);

    @Autowired
    private UserLogBusiness userLogBusiness;

    @Autowired
    private PesUserLoginLogBusiness pesUserLoginLogBusiness;
    /**
     *  用户登录日志查询
     */
    @RequestMapping("login/select")
    public Object selectLoginLog(Long shopId,
                                 @DateFormat Date startDate,
                                 @DateFormat Date endDate,
                                 String nick){
        try {
            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(startDate,"开始时间不能为空");
            Assert.notNull(endDate,"结束时间不能为空");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,ImmutableMap.builder()
                    .put(ApiResponse.RESULT_COLLECTION_NAME,userLogBusiness.
                            searchLoginCountLogByTimeNick(shopId, startDate, endDate, nick)).build());
        } catch (Exception e) {
            LOGGER.error(" select login log error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_UL_01_01);
        }
    }

    /**
     *  用户登录日志详情
     */
    @RequestMapping("login/detail")
    public Object selectLoginLogDetails(Long shopId,
                                        @DateFormat Date startDate,
                                        @DateFormat Date endDate,
                                 String nick){
        try {
            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(startDate,"开始时间不能为空");
            Assert.notNull(endDate,"结束时间不能为空");
            Assert.notNull(nick,"nick不能为空");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,ImmutableMap.builder()
                    .put(ApiResponse.RESULT_COLLECTION_NAME,userLogBusiness.
                            searchLoginLogByTimeNick(shopId, startDate, endDate, nick)).build());
        } catch (Exception e) {
            LOGGER.error(" select login log detail error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"用户日志详情查询失败");
        }
    }

    /**
     *  用户操作日志查询
     */
    @RequestMapping("operation/select")
    public Object selectOperationLog(Long shopId,
                                 @DateFormat Date startDate, @DateFormat Date endDate,
                                 String optType,
                                 String nick){

        try {
            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(startDate,"开始时间不能为空");
            Assert.notNull(endDate,"结束时间不能为空");
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,ImmutableMap.builder()
                    .put(ApiResponse.RESULT_COLLECTION_NAME,userLogBusiness.
                            searchOperationLogByTypeTimeNick(shopId, startDate, endDate, nick,optType)).build());
        } catch (Exception e) {
            LOGGER.error(" select operation log error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"操作日志查询失败");
        }
    }

    /**
     *  用户操作日志插入
     */
    @RequestMapping("operation/insert")
    public Object insertOperationLog(Long userId,String nick,String optContent,Long shopId,
                                     String optType){
        try {
            Assert.notNull(shopId,"shopId不能为空");
            Assert.notNull(userId,"userId不能为空");
            Assert.notNull(optContent,"optContent不能为空");
            Assert.notNull(nick,"nick不能为空");
            Assert.notNull(optType,"optType不能为空");
            userLogBusiness.insertOperationLog(userId, nick, DateUtils.now(), shopId, optType, optContent);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002.getCode(),(String) null);
        } catch (Exception e) {
            LOGGER.error(" insert operation log error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_01,"操作日志插入失败");
        }
    }

    @RequestMapping("/selectCsLoginDetailForShopUserAnalysis")
	public Object selectCsLoginDetailForShopUserAnalysis(@RequestParam("param")String paramStr)	{
		UserAnalysisParam param=null;
		try {
			param=JacksonUtils.json2pojo(paramStr, UserAnalysisParam.class);
		} catch (Exception e) {
			LOGGER.error(e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(pesUserLoginLogBusiness.selectCsLoginDetailForShopUserAnalysis(param)));
		} catch (Exception e) {
			LOGGER.error("master selectCsLoginDetailForShopUserAnalysis error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_DL_01_12, RestApiResponse2.of(false));
		}
	}

    /**
     *  用户操作日志查询
     */
    @RequestMapping("/getUserOperationLogByNickAndShop")
    public Object getUserOperationLogByNickAndShop(Long shopId,
                                 String nick, String type){
        try {
            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(nick,"用户昵称不能为空");
            Assert.notNull(type,"操作类型不能为空");

            return RestResponseTypeRef.ofSuccess(userLogBusiness.getUserOperationLogByNickAndShop(shopId, nick, type));
        } catch (Exception e) {
            LOGGER.error(" getUserLoginCountByNickAndShop error ",e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("getUserOperationLogByNickAndShopAndTime")
    public Object getUserOperationLogByNickAndShopAndTime(Long shopId,
                                 @DateFormat Date startDate,
                                 @DateFormat Date endDate,
                                 String type,
                                 String nick){
        try {
            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(startDate,"开始时间不能为空");
            Assert.notNull(endDate,"结束时间不能为空");
            Assert.notNull(type,"操作类型不能为空");
            Assert.notNull(nick,"用户昵称不能为空");

            return RestResponseTypeRef.ofSuccess(userLogBusiness.getUserOperationLogByNickAndShopAndTime(shopId, startDate, endDate, type, nick));
        } catch (Exception e) {
            LOGGER.error(" select login log error ",e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("getUserOperationLog")
    public Object getUserOperationLog(Long shopId, String startDate, String endDate, String type, String nick) {
        try {
            Date sDate = DateUtil.parse(startDate,"yyyy-MM-dd HH:mm:ss");
            Date eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");

            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(type,"操作类型不能为空");
            Assert.notNull(nick,"用户昵称不能为空");

            return RestResponseTypeRef.ofSuccess(userLogBusiness.getUserOperationLogByNickAndShopAndTime(shopId, sDate, eDate, type, nick));
        } catch (Exception e) {
            LOGGER.error(" select login log error ",e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("getUserLoginCountByNickAndShopAndTime")
    public Object getUserLoginCountByNickAndShopAndTime(Long shopId, String startDate, String endDate, String nick) {
        try {
            Date sDate = DateUtil.parse(startDate,"yyyy-MM-dd HH:mm:ss");
            Date eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");

            Assert.notNull(shopId,"店铺ID不能为空");
            Assert.notNull(nick,"用户昵称不能为空");

            return RestResponseTypeRef.ofSuccess(userLogBusiness.getUserLoginCountByNickAndShopAndTime(shopId, sDate, eDate, nick));
        } catch (Exception e) {
            LOGGER.error(" select login log error ",e);
            return RestResponseTypeRef.ofFail();
        }
    }
}
