package com.pes.jd.controller;

import com.pes.jd.business.sub.ReceiveSessionPressureBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/29 11:25 AM
 * @since 1.0.0
 */
@RequestMapping("/special/report")
@RestController
public class SpecialReportController {

    private final static Logger logger = LoggerFactory.getLogger(SpecialReportController.class);

    @Autowired
    private ReceiveSessionPressureBusiness receiveSessionPressureBusiness;

    @RequestMapping("/pressure")
    public Object pressureReport(
            Long shopId,
            Date startDate,
            Date endDate,
            @RequestBody(required = false) Set<String> nicks,
            String schemaId,
            String queryFlag,
            Boolean singleNick,
            Boolean empty
    ){
        try {
            return ApiResponse.of(
                    ApiCodeEnum.CODE_SUCCESS_1001,
                    receiveSessionPressureBusiness.searchByDateShopNicks(
                            shopId, startDate, endDate, nicks, schemaId, queryFlag,singleNick,empty)
            );
        } catch (Exception e) {
            logger.error("search press info error ",e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT);
        }
    }

}
