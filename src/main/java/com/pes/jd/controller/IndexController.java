
package com.pes.jd.controller;

import com.pes.jd.business.main.IndexBussiness;
import com.pes.jd.model.BO.ShortCutMenuPermissionTreeBo;
import com.pes.jd.model.DO.Resource;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
* @Title: IndexController.java
* @Package:com.pes.jd.controller
* @Description:(作用)
* @author:Lsp
* @date:2018年12月3日
* @version:V1.8
 */
@Controller
@RequestMapping("/index")
public class IndexController extends BaseController {

	private final static Logger LOGGER = LoggerFactory.getLogger(IndexController.class);
	@Autowired
	private IndexBussiness indexBussiness;

	@RequestMapping("/")
	public String index(Model m) {
		m.addAttribute("name", "后台传值");
		return "welcome";
	}

	@RequestMapping(value = "/getIndexTree")
	public @ResponseBody ApiResponse getIndexTree() {
		Map<String, Object> returnMap = new HashMap<>();
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopUserDTO shopUser = new ShopUserDTO();
			shopUser.setUserId(Long.valueOf("3499638716"));
			List<ShortCutMenuPermissionTreeBo> pgPermissionTreeList = indexBussiness.makePermissionTree(shopUser);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
			returnMap.put("pgPermissionTreeList", pgPermissionTreeList);
			apiResponse.setData(returnMap);
			return apiResponse;
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_IX_01_01.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_IX_01_01.getMsg());
			return apiResponse;
		}
	}

	@RequestMapping(value = "/getCurrentUserIndexResource")
	public @ResponseBody ApiResponse getCurrentUserIndexResource() {
		Map<String, Object> returnMap = new HashMap<>();
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopUserDTO shopUser = getCurrentUser();
			List<Resource> userRoleResourceList = indexBussiness.seachCurrentUserIndexResource(shopUser);
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
			returnMap.put("userRoleResourceList", userRoleResourceList);
			apiResponse.setData(returnMap);
			return apiResponse;
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_IX_01_02.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_IX_01_02.getMsg());
			return apiResponse;
		}
	}

	@RequestMapping("savePesIndexUserResource")
	@ResponseBody
	public ApiResponse savePesIndexUserResource(@RequestParam(name = "resourceId") String resourceId) {
		ApiResponse apiResponse = new ApiResponse();
		try {
			ShopUserDTO shopUser = getCurrentUser();
			indexBussiness.insertPesIndexUserResource(resourceId, shopUser.getUserId() + "");
			apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1002.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1002.getMsg());
		} catch (Exception e) {
			LOGGER.error(e.getMessage(), e);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_IX_01_03.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_IX_01_03.getMsg());
		}
		return apiResponse;
	}
}
