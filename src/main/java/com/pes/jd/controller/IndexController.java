package com.pes.jd.controller;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.rest.UsermgrRestTemplate;
import com.yiyitech.support.rpc.RestOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/index")
public class IndexController {

	private static final Logger logger = LoggerFactory.getLogger(IndexController.class);

	@Autowired
	private UsermgrRestTemplate usermgrRestTemplate;

	@RequestMapping(value = "/getIndexTree", method = RequestMethod.GET)
	public Object getIndexTree() {

		//String url = "http://jd-pes-master-provider/index/getIndexTree";
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		ApiResponse res = null;
		String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
		try {
			res = usermgrRestTemplate.postRest(serviceId, "/index/getIndexTree", RequestEntityBuilder.builder().toRequestEntity());
			System.err.println("/index/getIndexTree" + res);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return res;
	}

	@RequestMapping(value = "/getCurrentUserIndexResource", method = RequestMethod.GET)
	public Object getCurrentUserIndexResource() {

		//String url = "http://jd-pes-master-provider/index/getCurrentUserIndexResource";
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		ApiResponse res = null;
		try {
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			res = usermgrRestTemplate.postRest(serviceId, "/index/getCurrentUserIndexResource", RequestEntityBuilder.builder().toRequestEntity());
			System.err.println("/index/getCurrentUserIndexResource" + res);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return res;
	}

	@RequestMapping(value = "/savePesIndexUserResource", method = RequestMethod.GET)
	public Object savePesIndexUserResource(@RequestParam(name = "resourceId") String resourceId) {

		//String url = "http://jd-pes-master-provider/index/savePesIndexUserResource";
		//HttpHeaders headers = new HttpHeaders();
		//headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		HttpEntity<Object> body = RequestEntityBuilder.builder().put("resourceId", resourceId)
				.toRequestEntity();
		ApiResponse res = null;
		try {
			String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_MASTER.getName());
			res = usermgrRestTemplate.postRest(serviceId, "/index/savePesIndexUserResource",body);

			System.err.println("/index/savePesIndexUserResource" + res);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return res;
	}
	
	
	/*@RequestMapping(value = "/getIndexCsData", method = RequestMethod.GET)
	public Object getIndexCsData(@RequestParam(name = "shopId")String shopId) {

		String url = "http://jd-pes-sub-provider/index/getIndexCsData";

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<String, Object>();
		paramMap.add("shopId", shopId);
		ApiResponse res = null;
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(paramMap);

		try {
			res = popSubRestTemplate.postRest(url, request, ApiResponse.class);
			System.err.println("/index/getIndexCsData" + res);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return res;
	}
	*/
	
	/*@RequestMapping(value = "/getIndexShopData", method = RequestMethod.GET)
	public Object getIndexShopData(@RequestParam(name = "shopId")String shopId) {

		String url = "http://jd-pes-sub-provider/index/getIndexShopData";

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
		MultiValueMap<String, Object> paramMap = new LinkedMultiValueMap<String, Object>();
		paramMap.add("shopId", shopId);
		ApiResponse res = null;
		HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<MultiValueMap<String, Object>>(paramMap);

		try {
			res = restTemplate.postForObject(url, request, ApiResponse.class);
			System.err.println("/index/getIndexShopData" + res);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return res;
	}*/
}
