package com.pes.jd.controller;

import com.pes.jd.business.main.CsShopSystemSettingsBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Author: aiJun
 * @Date: 2019-05-17 16:58
 * @Version 1.0
 */
@RestController
@RequestMapping("/shop/manage")
public class CsShopSystemSettingsController {
    private static final Logger logger = LoggerFactory.getLogger(CsShopSystemSettingsController.class);
    @Autowired
    private CsShopSystemSettingsBusiness csShopSystemSettingsBusiness;
    @RequestMapping(value = "selectShopCsAndSystemSettings" ,method = RequestMethod.POST)
    public ApiResponse selectShopCsAndSystemSettings(
            @RequestParam(name="shopId")String shopId ,
            @RequestParam(name="groupId")String groupId,
            @RequestParam(name="csNick")String csNick,
            @RequestParam(name="needSystemSettings") boolean needSystemSettings) {
        logger.info("/shop/manage/selectShopCsAndSystemSettings shopId = {}，request = {}，request = {}，request = {}，",shopId,
                groupId,csNick,needSystemSettings);

        ApiResponse apiResponse;
        try {
            Map<String,Object> shopCsSyssetting= csShopSystemSettingsBusiness.selectShopCsAndSystemSettings(shopId,groupId,csNick,needSystemSettings);
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,shopCsSyssetting);
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
        return apiResponse;
    }


}
