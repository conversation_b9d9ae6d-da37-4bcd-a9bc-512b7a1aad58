package com.pes.jd.controller;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月16 11:17:17<br>
 */

import com.pes.jd.business.main.ShopSmsSettingBussiness;
import com.pes.jd.business.main.SmsManagerBusiness;
import com.pes.jd.model.DO.ShopSmsSettingDO;
import com.pes.jd.model.DO.ShopSmsWordDO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.ShopSmsSetting;
import com.pes.jd.ms.domain.Data.master.ShopSmsWord;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/shop/sms/setting")
public class ShopSmsSettingCotroller extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(ShopSmsSettingCotroller.class);
    @Resource
    private ShopSmsSettingBussiness shopSmsSettingBussiness;
    @Resource
    private SmsManagerBusiness smsManagerBusiness;

    @RequestMapping("/saveOrUpdateShopSmsWord")
    public Object saveOrUpdateShopSmsWord(@RequestParam("recordStr") String recordStr) {
        ShopSmsWordDO record;
        try {
            record = JacksonUtils.json2pojo(recordStr, ShopSmsWordDO.class);
        } catch (Exception e) {
            logger.info("master parse ShopSmsWordDO json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }

        try {
//            boolean status = shopSmsSettingBussiness.saveOrUpdateShopSmsWord(record);
            Long result = shopSmsSettingBussiness.saveOrUpdateShopSmsWordAndRtId(record);
            if (null == result){
                return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_01, RestApiResponse2.of(false));
            }
//            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("id", result);
            dataMap.put("type", record.getType());
//            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002.getCode(), ApiCodeEnum.CODE_SUCCESS_1002.getMsg(), dataMap);
        } catch (Exception e) {
            logger.error("master saveOrUpdateShopSmsWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_01, RestApiResponse2.of(false));
        }

    }


    @RequestMapping("/deleteShopSmsWord")
    public Object deleteShopSmsWord(@RequestParam("id") Long id) {

        try {
            shopSmsSettingBussiness.deleteShopSmsWordById(id);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            logger.error("master deleteShopSmsWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_02, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsWordByShopId")
    public Object selectShopSmsWordByShopId(@RequestParam("shopId") Long shopId,
                                            @RequestParam("type") String type) {

        try {
            List<ShopSmsWord> smsLst = shopSmsSettingBussiness.selectShopSmsWordByShopId(shopId, type);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(smsLst));
        } catch (Exception e) {
            logger.error("master selectShopSmsWordByShopId error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_03, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsWordByIdByType")
    public Object selectShopSmsWordByIdByType(@RequestParam("id") Long id, @RequestParam("type") String type) {
        try {
            List<ShopSmsWord> smsLst = shopSmsSettingBussiness.selectShopSmsWordByIdByType(id, type);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(smsLst));
        } catch (Exception e) {
            logger.error("master selectShopSmsWordByIdByType error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_03, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsWordById")
    public Object selectShopSmsWordById(@RequestParam("id") Long id) {

        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSmsSettingBussiness.selectShopSmsWordById(id)));
        } catch (Exception e) {
            logger.error("master selectShopSmsWordById error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_03, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/saveOrUpdateShopSmsSetting")
    public Object saveOrUpdateShopSmsSetting(@RequestParam("recordStr") String recordStr) {
        ShopSmsSettingDO record;
        try {
            record = JacksonUtils.json2pojo(recordStr, ShopSmsSettingDO.class);
        } catch (Exception e) {
            logger.info("master parse ShopSmsSettingDO json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }
        try {
            shopSmsSettingBussiness.saveOrUpdateShopSmsSetting(record);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            logger.error("master saveOrUpdateShopSmsSetting error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_04, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsSettingByShopId")
    public Object selectShopSmsSettingByShopId(@RequestParam("shopId") Long shopId) {

        try {
            ShopSmsSetting record = shopSmsSettingBussiness.selectShopSmsSettingByShopId(shopId);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(record));
        } catch (Exception e) {
            logger.error("master selectShopSmsSettingByShopId error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_05, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/deleteShopSmsBacklistById")
    public Object deleteShopSmsBacklistById(@RequestParam("id") Long id) {

        try {
            shopSmsSettingBussiness.deleteShopSmsBacklistById(id);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            logger.error("master deleteShopSmsWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_06, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/batchInsertShopSmsBacklist")
    public Object batchInsertShopSmsBacklist(
            @RequestParam("recordStr") String recordStr,
            @RequestParam("shopId") Long shopId,
            @RequestParam("userId") Long userId) {
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of(shopSmsSettingBussiness.batchInsertShopSmsBacklist(shopId, userId, recordStr)));
        } catch (Exception e) {
            logger.error("master deleteShopSmsWord error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_07, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsBacklistByShopIdByCsNickByBuyerNick")
    public Object selectShopSmsBacklistByShopIdByCsNickByBuyerNick(
            @RequestParam("shopId") Long shopId,
            @RequestParam("buyerNick") String buyerNick,
            @RequestParam("startDate") String startDateStr,
            @RequestParam("endDate") String endDateStr) {
        Date startDate;
        Date endDate;
        try {
            startDate = DateUtils.parseYMdHms(startDateStr);
            endDate = DateUtils.parseYMdHms(endDateStr);
        } catch (Exception e) {
            logger.info("master parse csNickLst json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }

        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSmsSettingBussiness.selectShopSmsBacklistByShopIdByCsNickByBuyerNick(shopId, buyerNick, startDate, endDate)));
        } catch (Exception e) {
            logger.error("master selectShopSmsBacklistByShopIdByCsNickByBuyerNick error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_08, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsWordByShopIdByAuditstatus")
    public Object selectShopSmsWordByShopIdByAuditstatus(
            @RequestParam("shopId") Long shopId,
            @RequestParam("statusOne") Integer statusOne,
            @RequestParam("statusTwo") Integer statusTwo) {
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSmsSettingBussiness.selectShopSmsWordByShopIdByAuditstatus(shopId, statusOne, statusTwo)));
        } catch (Exception e) {
            logger.error("master selectShopSmsWordByShopIdByAuditstatus error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_03, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSmsWordByShopIdByMap")
    public Object selectShopSmsWordByShopIdByMap(
            @RequestParam("dataMap") HashMap<String, Object> dataMap) {
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSmsSettingBussiness.selectShopSmsWordByShopIdByMap(dataMap)));
        } catch (Exception e) {
            logger.error("master selectShopSmsWordByShopIdByMap error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_03, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectSmsSuccessOrderCountByShopId")
    public Object selectSmsSuccessOrderCountByShopId(
            @RequestParam("shopId") Long shopId) {
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSmsSettingBussiness.selectSmsSuccessOrderCountByShopId(shopId)));
        } catch (Exception e) {
            logger.error("master selectSmsSuccessOrderCountByShopId error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SR_01_01, RestApiResponse2.of(false));
        }

    }


    /* ******V15.dipatching 接口迁移 start***********/

    /**
     * 更新短信使用数量
     *
     * @param shopId       店铺ID
     * @param successCount 数量
     * @return Object
     */
    @RequestMapping(value = "/updateSmsUseNumber")
    public Object updateSmsUseNumber(String shopId, Integer successCount) {
        logger.info("updateSmsUseNumber req come in shop:" + shopId);
        ApiResponse apiResponse = new ApiResponse();
        try {
            smsManagerBusiness.updateSmsUseNumber(Long.valueOf(shopId), successCount);
            apiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            apiResponse.setSuccess(Boolean.FALSE);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_COMMON_01_02.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_COMMON_01_02.getMsg());
            logger.error("updateSmsUseNumber ：" + e.getMessage(), e);
        }
        return apiResponse;
    }

    /**
     * 更新短信使用数量通过失败数
     * (使用数-失败数)
     *
     * @param shopId    店铺ID
     * @param failCount 数量
     * @return Object
     */
    @RequestMapping(value = "/updateSmsUseNumberByFailCount")
    public Object updateSmsUseNumberByFailCount(String shopId, Integer failCount) {
        logger.info("updateSmsUseNumberByFailCount req come in shop:" + shopId);
        ApiResponse apiResponse = new ApiResponse();
        try {
            smsManagerBusiness.updateSmsUseNumberByFailCount(Long.valueOf(shopId), failCount);
            apiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            apiResponse.setSuccess(Boolean.FALSE);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_COMMON_01_02.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_COMMON_01_02.getMsg());
            logger.error("updateSmsUseNumberByFailCount ：" + e.getMessage(), e);
        }
        return apiResponse;
    }
    /* ******V15.dipatching 接口迁移   end***********/

}
