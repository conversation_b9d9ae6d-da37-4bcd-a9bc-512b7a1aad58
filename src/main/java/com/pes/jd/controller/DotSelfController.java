package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.main.MenuDotSelfBusiness;
import com.pes.jd.model.DTO.MenuDotDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.DotParam;
import com.pes.jd.model.Response.ApiResponse;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/dot/self/")
public class DotSelfController {
    private static final Logger logger = LoggerFactory.getLogger(DotSelfController.class);
    private ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());
    @Autowired
    private MenuDotSelfBusiness menuDotSelfBusiness;
    /**
     * 保存打点信息
     * @param dotParam
     * @return
     */
    @RequestMapping(value = "dotInfoSaveForSelf")
    @ResponseBody
    public ApiResponse dotInfoSave(@RequestParam(name = "dotParam") String dotParam) {
        try {
            DotParam param = JSONObject.parseObject(dotParam, DotParam.class);
            MenuDotDTO menuDotDTO = new MenuDotDTO();
            BeanUtils.copyProperties(param,menuDotDTO);
            CompletableFuture.runAsync(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    menuDotSelfBusiness.inserMenuDot(menuDotDTO);
                }
            },executor);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
        } catch (Exception e) {
            logger.error("菜单打点error : {}", e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);

        }
    }

    /**
     * 事件分析查询
     * @param startTime
     * @param endTime
     * @return
     */
    @RequestMapping(value = "getDotInfoList")
    @ResponseBody
    public ApiResponse getDotInfoList(@RequestParam(name = "startTime") String startTime,
                                      @RequestParam(name = "endTime") String endTime,
                                      @RequestParam("pageName") String pageName) {
        try {
            Date sDate = JSONObject.parseObject(startTime, Date.class);
            Date eDate = JSONObject.parseObject(endTime, Date.class);
            return menuDotSelfBusiness.listMenuDotByDate(sDate,eDate,pageName);
        } catch (Exception e) {
            logger.error("事件分析查询error : {}", e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);

        }
    }

    /**
     * 事件分析详情
     * @param shopName
     * @param nick
     * @param pageVisitName
     * @return
     */
    @RequestMapping(value = "getDotInfoDetails")
    @ResponseBody
    public ApiResponse getDotInfoDetails(@RequestParam(name = "shopName", required = false) String shopName,
                                         @RequestParam(name = "nick", required = false) String nick,
                                         @RequestParam(name = "pageVisitName") String pageVisitName,
                                         @RequestParam(name = "sDate") String sDate,
                                         @RequestParam(name = "eDate") String eDate) {
        ApiResponse apiResponse;
        try {
            Date startDate = JSONObject.parseObject(sDate, Date.class);
            Date endDate = JSONObject.parseObject(eDate, Date.class);
            return menuDotSelfBusiness.getDotInfoDetails(shopName,nick,pageVisitName,startDate,endDate);
        } catch (Exception e) {
            logger.error("事件分析查询error : {}", e.getMessage());
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
        return apiResponse;
    }

    /**
     * 使用习惯信息查询
     * @param startTime
     * @param endTime
     * @return
     */
    @RequestMapping(value = "getUserInfo")
    @ResponseBody
    public ApiResponse getUserInfo(@RequestParam(name = "startTime") String startTime,
                                   @RequestParam(name = "endTime") String endTime) {
        ApiResponse apiResponse;
        try {
            Date sDate = JSONObject.parseObject(startTime, Date.class);
            Date eDate = JSONObject.parseObject(endTime, Date.class);
            return menuDotSelfBusiness.getUserInfo(sDate,eDate);
        } catch (Exception e) {
            logger.error("使用习惯查询error : {}", e.getMessage());
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
        return apiResponse;
    }

}
