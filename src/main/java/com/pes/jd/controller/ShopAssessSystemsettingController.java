  
package com.pes.jd.controller;

import com.pes.jd.business.main.ShopAssessSystemsettingBusiness;
import com.pes.jd.model.DTO.ShopAssessSystemsettingDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 平台考核指标设置
 */
@RestController
@RequestMapping("/shopAssessSystemsetting")
public class ShopAssessSystemsettingController extends BaseController{
	private final Logger log=LoggerFactory.getLogger(ShopAssessSystemsettingController.class);
	@Resource
	private ShopAssessSystemsettingBusiness shopAssessSystemsettingBusiness;
	/**
	 * 平台考核信息查询
	 */
	@RequestMapping("/select")
	public ApiResponse select(@RequestParam(name="shopId") Long shopId) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		ShopAssessSystemsettingDTO resultDO = null;
		try {
			resultDO = shopAssessSystemsettingBusiness.selectByShopId(shopId);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			log.error("shopAssessSystemsetting.select:{}", e.getMessage(), e);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		}
		map.put("assessSetting", resultDO);
		apiResponse.setData(map);
		return apiResponse;
	}
	/**
	 * 平台考核信息更新
	 */
	@RequestMapping("/updateOrInsert")
	public ApiResponse updateOrInsert(@RequestParam(name="shopId") Long shopId,
									  @RequestParam(name="id",required = false) Long id,
									  @RequestParam(name="satisfiedRateIndex") double satisfiedRateIndex,
									  @RequestParam(name="avgRespIndex") double avgRespIndex) {
		ApiResponse apiResponse;
		Map<String, Object> map = new HashMap<>();
		try {
			shopAssessSystemsettingBusiness.updateOrInsert(id, shopId, satisfiedRateIndex, avgRespIndex);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			log.error("shopAssessSystemsetting.updateOrInsert:{}", e.getMessage(), e);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
		}
		apiResponse.setData(map);
		return apiResponse;
	}

}
  
