package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.pes.jd.business.main.DeptBusiness;
import com.pes.jd.model.DTO.DeptFilerDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.DeptInfoParam;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/3 14:12
 */
@Controller
@RequestMapping("/dashboard/")
public class DashboardController extends BaseController {
    private final Logger logger = LoggerFactory.getLogger(DashboardController.class);
    @Autowired
    private DeptBusiness deptBusiness;
    /**
     * 看板登陆接口
     * @param username
     * @param password
     * @return
     */
    @RequestMapping("login")
    @ResponseBody
    public ApiResponse login(@RequestParam(name = "username") String username,
                             @RequestParam(name = "password") String password) {

        ApiResponse apiResponse = new ApiResponse();
        try {
            logger.info("username {},password{}",username,password);
            Map<String,Object> map = Maps.newHashMap();
            // 验证密码后，设置为管理员权限
            if (password == null || password.trim().length() == 0) {
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_01.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_01.getMsg());
                return apiResponse;
            }
            if (!deptBusiness.loginValiadte(username, password,map)) {
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_15.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_15.getMsg());
                return apiResponse;
            }
            apiResponse.setData(map);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
//            logger.error("apiResponse={}"+ JSON.toJSONString(apiResponse));
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_15);

        }
        return apiResponse;
    }


    /**
     * 部门关系信息上传接口
     * @param deptInfos
     */
    @RequestMapping("uploadExcelInfo")
    @ResponseBody
    public void uploadExcelInfo(@RequestParam(name = "deptInfos") String deptInfos, @RequestParam(name = "filerName") String filerId) {
        try {
            List<DeptInfoParam> deptInfoParams = JSONObject.parseArray(deptInfos, DeptInfoParam.class);
            deptBusiness.uploadExcelInfo(deptInfoParams,filerId);
        } catch (Exception e) {
            logger.error("Dashboard uploadExcelInfo error：{}",e.getMessage(),e);
        }
    }

    /**
     * 获取下拉关系列表
     * @param id
     * @param deptId
     * @param type
     * @return
     */
    @RequestMapping("getDeptRelation")
    @ResponseBody
    public ApiResponse getDeptRelation(@RequestParam(name = "id") String id,
                                       @RequestParam(name = "deptId") String deptId, @RequestParam(name = "type") String type) {
        ApiResponse apiResponse = new ApiResponse();
        try {
            // 验证密码后，设置为管理员权限
            if (!validateGetDeptRelation(id,deptId,type)) {
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_01_02.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_01_02.getMsg());
                return apiResponse;
            }
            Map<String, Object> deptRelation = deptBusiness.getDeptRelation(id, deptId, type);
            apiResponse.setData(deptRelation);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_02_01);

        }
        return apiResponse;
    }

   public boolean validateGetDeptRelation(String id,String deptId,String type){
       if(org.apache.commons.lang3.StringUtils.isBlank(id)){
           logger.info("id is null");
           return false;
       }
       if(org.apache.commons.lang3.StringUtils.isBlank(deptId)){
           logger.info("deptId is null");
           return false;
       }
       if(org.apache.commons.lang3.StringUtils.isBlank(type)){
           logger.info("type is null");
           return false;
       }
       return true;
   }

    @RequestMapping("selfLogin")
    @ResponseBody
    public ApiResponse selfLogin(@RequestParam(name = "username") String username,
                                 @RequestParam(name = "password") String password) {

        ApiResponse apiResponse = new ApiResponse();
        try {
            logger.info("username {},password{}",username,password);
            Map<String,Object> map = Maps.newHashMap();
            // 验证密码后，设置为管理员权限
            if (password == null || password.trim().length() == 0) {
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_01.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_01.getMsg());
                return apiResponse;
            }
            if (!deptBusiness.selfLogin(username, password,map)) {
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_15.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_15.getMsg());
                return apiResponse;
            }
            apiResponse.setData(map);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_15);

        }
        return apiResponse;
    }

    @RequestMapping("test")
    @ResponseBody
    public List<Long> test(@RequestParam(name = "deptId") String deptId) {
        try {
            List<Long> selfShopIdByDeptId = deptBusiness.getSelfShopIdByDeptId(Integer.valueOf(deptId));
            return selfShopIdByDeptId;
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);

        }
        return null;
    }



    /**
     * 获取上传Excel文件信息
     * @return
     */
    @RequestMapping("getUploadExcelInfo")
    @ResponseBody
    public ApiResponse getUploadExcelInfo() {
        ApiResponse apiResponse = new ApiResponse();
        try {
            Map<String,Object> map = Maps.newHashMap();
            List<DeptFilerDTO> deptFilerDTOS = deptBusiness.listDeptFiler();
            map.put("filerInfo",deptFilerDTOS);
            apiResponse.setData(map);
            apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_02_01);

        }
        return apiResponse;
    }

    /**
     *新增文件名接口
     * @return
     */
    @RequestMapping("uploadFilerInfo")
    @ResponseBody
    public ApiResponse uploadFilerInfo(@RequestParam(name = "filerName") String filerName) {
        ApiResponse apiResponse = new ApiResponse();
        try {
            boolean uploadFilerFlag = deptBusiness.uploadFilerInfo(filerName);
            if(uploadFilerFlag){
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
            } else {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_KB_01_01);
            }
        } catch (Exception e) {
            logger.error("Dashboard login error：{}",e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);

        }
        return apiResponse;
    }

}
