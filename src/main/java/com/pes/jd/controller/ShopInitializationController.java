package com.pes.jd.controller;

import com.pes.jd.business.main.ShopInitializationBusiness;
import com.pes.jd.model.DO.GuidanceRecordDO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 店铺初始化
 */
@RestController
@RequestMapping("/init/")
public class ShopInitializationController {

    private final Logger logger = LoggerFactory.getLogger(ShopInitializationController.class);

    @Resource
    private ShopInitializationBusiness shopInitializationBusiness;

    @RequestMapping("/insertEntryGuidance")
    public Object entryGuidance(@RequestParam("shopId") String shopId){
        try {
            Long shop = Long.valueOf(shopId);
            shopInitializationBusiness.insertEntryGuidance(shop);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        }catch (Exception e){
            logger.error("进入新手引导失败 shopId :{}, error :{}",shopId, e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
    }

    @RequestMapping("/updateEntryGuidance")
    public Object updateEntryGuidance(@RequestParam("shopId") String shopId){
        try {
            Long shop = Long.valueOf(shopId);
            shopInitializationBusiness.updateEntryGuidance(shop);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        }catch (Exception e){
            logger.error("新手引导更新状态出错 shopId :{}, error :{}", shopId, e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
    }

    @RequestMapping("/getGuidanceRecordByShopId")
    public Object getGuidanceRecordByShopId(@RequestParam("shopId") String shopId){
        try {
            Long shop = Long.valueOf(shopId);
            GuidanceRecordDO guidanceRecordDO = shopInitializationBusiness.getGuidanceRecordByShopId(shop);
            if(guidanceRecordDO == null){
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, false);
            }
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, guidanceRecordDO.isFlag());
        }catch (Exception e){
            logger.error("查询店铺引导记录时出错 shopId :{}, error :{}", shopId, e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
        }
    }
}
