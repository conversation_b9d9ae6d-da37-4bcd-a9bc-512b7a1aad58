package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.FunctionWhiteBusiness;
import com.pes.jd.business.main.ShopWipFunctionModuleBusiness;
import com.pes.jd.model.DTO.ShopWipFunctionModuleDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.FunctionModuleWhiteVO;
import com.pes.jd.model.VO.ShopVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: ms-pes-jd
 * @description: 白名单控制
 * @author: ALan
 * @create: 2019-05-15 16:25
 */
@RestController
@RequestMapping("/sys/functionWhite")
public class FunctionWhiteController extends BaseController {
    private final static Logger logger = LoggerFactory.getLogger(FunctionWhiteController.class);
    @Autowired
    private FunctionWhiteBusiness functionWhiteBusiness;
    @Autowired
    private ShopWipFunctionModuleBusiness shopWipFunctionModuleBusiness;
    /*
    *@Description:   查询店铺有那些白名单功能
    *@Param:  店铺 id
    *@return:
    *@Author: ALan
    *@date: 2019/5/19
    */
    @RequestMapping("searchFunctionWhiteLstByShopId")
    public Object searchFunctionWhiteLstByShopId(
            @RequestParam(name = "shopId") String shopId) {
        if (null == shopId || "".equals(shopId)) {
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
        try {
            Map<String,List<FunctionModuleWhiteVO>> whiteLst=Maps.newHashMap();
            whiteLst.put("whiteLst",functionWhiteBusiness.searchFunctionWhiteLstByShopId(Long.parseLong(shopId)));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(whiteLst));
        } catch (Exception e) {
            logger.error("master -> /sys/functionWhite/searchFunctionWhiteLstByShopId error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_02, new RestApiResponse2<>(false));
        }
    }

    /*
    *@Description: 查询 添加 那些 白名单的店
    *@Param:
    *@return:
    *@Author:
    *@date: 2019/5/19
    */
    @RequestMapping("searchFunctionWhiteForLst")
    public Object searchFunctionWhiteForLst(@RequestParam(name = "type")Integer type) {
         try{
            Map<String,List<FunctionModuleWhiteVO>> whiteLst= Maps.newHashMap();
            whiteLst.put("whiteLst",functionWhiteBusiness.searchFunctionWhiteForLst(type));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(whiteLst));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("master -> /sys/functionWhite/searchFunctionWhiteForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_02, new RestApiResponse2<>(false));
        }

    }

    /*
    *@Description: 查询所有添加过白名单的店
    *@Param:
    *@return:
    *@Author:
    *@date: 2019/5/19
    */
    @RequestMapping("searchFunctionWhiteAddShopForLst")
    public Object searchFunctionWhiteAddShopForLst(@RequestParam(name = "type")Integer type) {
        try {
            Map<String,List<FunctionModuleWhiteVO>> whiteLst= Maps.newHashMap();
            whiteLst.put("whiteLst",functionWhiteBusiness.searchFunctionWhiteAddShopForLst(type));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(whiteLst));
        } catch (Exception e) {
            //异常处理
            e.printStackTrace();
            logger.error("master -> /sys/functionWhite/searchFunctionWhiteAddShopForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_03, new RestApiResponse2<>(false));
        }

    }

   /*
   *@Description:  查询所有功能的
   *@Param:
   *@return:
   *@Author: ALan
   *@date: 2019/5/19
   */
    @RequestMapping("batchAllWipFunction")
    public Object batchAllWipFunction() {
        try {
            Map<String,List<ShopWipFunctionModuleDTO>> functionLst= Maps.newHashMap();
            functionLst.put("functionLst",shopWipFunctionModuleBusiness.batchAllWipFunction());
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(functionLst));
        } catch (Exception e) {
            //异常处理
            e.printStackTrace();
            logger.error("master -> /sys/functionWhite/batchAllWipFunction error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_03, new RestApiResponse2<>(false));
        }


    }

    /*
    *@Description: 
    *@Param: 
    *@return: 
    *@Author: ALan
    *@date: 2019/5/19
    */
    @RequestMapping("selectAllNotWhiteShopForLst")
    public Object selectAllNotWhiteShopForLst(
            @RequestParam(name = "type") String type) {
        try{
            Map<String,List<ShopVO>> shopLst= Maps.newHashMap();
            shopLst.put("shopLst",functionWhiteBusiness.selectAllNotWhiteShopForLst(type));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopLst));
        }catch(Exception e){
            e.printStackTrace();
            logger.error("master -> /sys/functionWhite/searchShopForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_03, new RestApiResponse2<>(false));
        }

    }

    /*
     *@Description: 添加白名单功能
     *@Param: arrShopId 多个店铺的Id   functionId 功能Id
     *@return:
     *@Author: ALan
     *@date: 2019/5/19
     */
    @RequestMapping("insertFunctionModuleWhiteForLst")
    public Object insertFunctionModuleWhiteForLst(
            @RequestParam(name = "arrShopId") String arrShopId,
            @RequestParam(name = "functionIds") String functionIds
    ) {

        try {
            String[] list = arrShopId.split(",");
            List<Long> shopIdLst = new ArrayList<>();

            for (String num : list) {
                shopIdLst.add(Long.valueOf(num));
            }
            int insetNum=0;
            List<Long> funcIds=null;
            if ("".equals(functionIds)||null==functionIds) {
                insetNum = functionWhiteBusiness.insertFunctionModuleWhiteForLst(shopIdLst, null);
            }else {
                funcIds = Arrays.stream(functionIds.split(",")).map(Long::parseLong).collect(Collectors.toList());

            }

            if (shopIdLst == null || shopIdLst.size() == 0) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_02, new RestApiResponse2<>(false));
            }
            if(insetNum==0)
                insetNum = functionWhiteBusiness.insertFunctionModuleWhiteForLst(shopIdLst, funcIds);
            if (insetNum < 0) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_02, new RestApiResponse2<>(false));
            }
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, new RestApiResponse2<>(true));
        } catch (Exception e) {
            //异常处理
            e.printStackTrace();
            logger.error("insertFunctionModuleWhiteForLst : ", e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_02, new RestApiResponse2<>(false));
        }
    }

    /*
    *@Description:
    *@Param: 白名单ID
    *@return:
    *@Author: ALan
    *@date: 2019/5/19
    */
    @RequestMapping("batchDeleteFunctionModuleWhite")
    public Object batchDeleteFunctionModuleWhite(
            @RequestParam(name = "fmwIds") String fmwIds) {
        List<Long> fmwIdLst = new ArrayList<>();
        String[] list = fmwIds.split(",");
        for (String num : list) {
            fmwIdLst.add(Long.parseLong(num));
        }
        if (fmwIdLst == null || fmwIdLst.size() == 0) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_02, new RestApiResponse2<>(false));
        }
        try {
            int insetNum = functionWhiteBusiness.batchDeleteFunctionModuleWhite(fmwIdLst);
            if (insetNum < 0) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_01, new RestApiResponse2<>(true));
            }
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, new RestApiResponse2<>(true));
        } catch (Exception e) {
            //异常处理
            e.printStackTrace();
            logger.error("batchDeleteFunctionModuleWhite : ", e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_08_02, new RestApiResponse2<>(false));
        }

    }


}
