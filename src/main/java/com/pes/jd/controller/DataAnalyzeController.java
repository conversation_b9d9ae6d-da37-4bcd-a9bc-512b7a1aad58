package com.pes.jd.controller;

import com.google.common.collect.ImmutableMap;
import com.pes.jd.business.sub.DataAnalyzeBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.LostType;
import com.pes.jd.model.Enum.OrderStatus;
import com.pes.jd.model.Enum.PositionCode;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.AppContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 *  data analyze  数据分析
 *
 * @<NAME_EMAIL>
 * @date 2018/11/14 4:46 PM
 * @since 1.0.0
 */
@RequestMapping("/data/analyze")
@RestController
public class DataAnalyzeController {

    private final static Logger LOGGER = LoggerFactory.getLogger(DataAnalyzeController.class);

    @Autowired
    private DataAnalyzeBusiness dataAnalyzeBusiness;

    @RequestMapping("/shopfilter")
    public Object selectShopFilter(String startDate,String endDate,
                                   String buyerNick,String orderId){
        AppContext appContext = AppContext.currentContext();
        String shopId = appContext.getCurrentShopId();
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, ImmutableMap.<String,Object>builder()
                    .put(ApiResponse.RESULT_SINGLE_NAME,dataAnalyzeBusiness.doSelectOrderFilter(
                            startDate,endDate,shopId,buyerNick,orderId
                    )).build());
        }catch (Exception e){
            if (Objects.equals(e.getMessage(), PositionCode.USER_EMPTY.getDetail())){
                return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,ApiResponse.EMPTY_RESULT);
            }
            LOGGER.error(e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01,e.getMessage());
        }

    }

    @RequestMapping("/lost")
    public Object selectLost(String startDate,String endDate,
                                   String buyerNick,Integer orderType,Integer lostType){
        AppContext appContext = AppContext.currentContext();
        String shopId = appContext.getCurrentShopId();
        try {
            return ApiResponse.of(
                    ApiCodeEnum.CODE_SUCCESS_1001,
                    ImmutableMap.<String,Object>builder()
                            .put(ApiResponse.RESULT_COLLECTION_NAME,dataAnalyzeBusiness
                                    .doSelectReceiveData(startDate,endDate,shopId,
                                            buyerNick,OrderStatus.get(orderType), LostType.get(lostType)))
                            .build()
            );
        }catch (Exception e){
            LOGGER.error(e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }
    }

}
