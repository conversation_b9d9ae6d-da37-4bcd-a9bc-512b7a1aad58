package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.pes.jd.business.*;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.constants.PesConstants;
import com.pes.jd.data.api.ShopBatchRemindOperator;
import com.pes.jd.data.api.UserSendCouponOperator;
import com.pes.jd.exception.LoginAuthException;
import com.pes.jd.framework.DateFormat;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Enum.ConversionResultEnum;
import com.pes.jd.model.Enum.ConversionStatusEnum;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.MasterServiceShopQuery;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.ChatLogVO;
import com.pes.jd.model.VO.CsNickLstForRealTimeVO;
import com.pes.jd.model.VO.MultiShopGroupVO;
import com.pes.jd.ms.constant.PesCommonConstant;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Result.rtsub.CsAllocateResult;
import com.pes.jd.ms.utils.DateUtils;
import com.pes.jd.util.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Integer.parseInt;

/**
 * 实时绩效
 */
@RestController
@RequestMapping("/realtime/performance")
public class RealtimePerformanceController extends BaseController{

	private static final Logger logger = LoggerFactory.getLogger(RealtimePerformanceController.class);

	@Resource
	private RealtimeShopPerformanceSummaryBusiness realtimeShopPerformanceSummaryBusiness;
	@Resource
	private ShopSysManagerBusiness shopSysManagerBusiness;
	@Resource
	private CsPerformanceInTimeIndexBusiness csPerformanceInTimeIndexBusiness;

	@Resource
	private DataAnalysisBusiness dataAnalysisBusiness;

	@Resource
	private RealTimeBoardBusiness realTimeBoardBusiness;

	@Resource
	private CsTeamIntimePerformanceBusiness csTeamIntimePerformanceBusiness;

	@Resource
	private SaleIndexSettingDataBusiness saleIndexSettingDataBusiness;

	@Resource
	private UserSendCouponOperator userSendCouponOperator;

	@Resource
	private ShopBatchRemindOperator shopBatchRemindOperator;
	/**
	 * 查询我的绩效（当天）
	 */
	@RequestMapping(value="/selectCurrentUserTodayRealtimePerformance",method=RequestMethod.POST)
	public ApiResponse selectCurrentUserTodayRealtimePerformance(@RequestParam("groupId") String groupId,
			@RequestParam(value="csNick",required=false) String csNick) throws LoginAuthException {
		long s1=System.currentTimeMillis();
		ApiResponse apiResponse = null;
		ShopDTO shop = this.getCurrentShop();
		UserShopQuery shopQuery = this.getCustUserByParam(String.valueOf(shop.getShopId()));
		ShopUserDTO currentUser = this.getCurrentUser();
		try {
			if (shop == null) {
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}

			List<UserQuery> csNickQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery,groupId, "");
			Set<String> preCsNickSet=csNickQueryLst.stream().filter(u->u.getType()==1).filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
			Set<String> csNickSet=csNickQueryLst.stream().filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
			String userNick="";
			if(StringUtils.isNoneBlank(csNick)){
				userNick=csNick;
			}else{
				userNick= currentUser.getNick();
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			apiResponse = realtimeShopPerformanceSummaryBusiness.selectCurrentUserRealtimePerformance(shopQuery, userNick,
					preCsNickSet,csNickSet, startDate, nowDate);
		} catch (Exception e) {
			logger.error("web selectCurrentUserTodayRealtimePerformance:{}",e.getMessage(),e);
		}
		logger.info("web controller 我的绩效耗时：{}ms",System.currentTimeMillis()-s1);
		return apiResponse;
	}



	/**
	 * 查询我的绩效（昨天）
	 */
	@RequestMapping("/selectCurrentUserYesterDayRealtimePerformance")
	public ApiResponse selectCurrentUserYesterDayRealtimePerformance(
			@RequestParam("groupId")String groupId,
			@RequestParam(value="csNick",required=false) String csNick) throws LoginAuthException {
		ApiResponse apiResponse=null;
		ShopDTO shop = this.getCurrentShop();
		if (shop == null) {
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
		}
		UserShopQuery shopQuery = this.getCustUserByParam(String.valueOf(shop.getShopId()));
		ShopUserDTO currentUser = this.getCurrentUser();
		try {
			List<UserQuery> csNickQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery,groupId, "");
			Set<String> preCsNickSet=csNickQueryLst.stream().filter(u->u.getType()==1).filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
			Set<String> csNickSet=csNickQueryLst.stream().filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
			String userNick="";
			if(StringUtils.isNoneBlank(csNick)){
				userNick=csNick;
			}else{
				userNick= currentUser.getNick();
			}
			Date nowDate=new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			apiResponse=realtimeShopPerformanceSummaryBusiness.selectCurrentUserRealtimePerformance(shopQuery, userNick, preCsNickSet,csNickSet, startDate, endDate);
		} catch (Exception e) {
			logger.error("web selectCurrentUserYesterDayRealtimePerformance:{}",e.getMessage(),e);
		}
		return apiResponse;
	}

	@RequestMapping("/selectGroupCsByCsNickForPlugin")
	public ApiResponse selectGroupCsByCsNickForPlugin(@RequestParam("csNick") String csNick,
			@RequestParam("shopId") String shopId){
		ApiResponse apiResponse;
		try {
			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(csNick)){
				logger.info("shopId:{}，csNick：{}",shopId,csNick);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shop = this.getSelectShop(shopId+"");
			if(shop==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
			}
			MasterServiceShopQuery shopQuery=new MasterServiceShopQuery();
			shopQuery.setSelectedShop(true);
			shopQuery.setSelectedShop(shop);
			GroupParam param=new GroupParam();
			param.setShopId(Long.valueOf(shopId));
			param.setCsNick(csNick);
			apiResponse=shopSysManagerBusiness.selectGroupCsByGroupParamForPlugin(shopQuery, param);
		} catch (Exception e) {
			logger.error("selectGroupCsByCsNickForPlugin error informatin:{}" , e.getMessage(),e);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
		}
		return apiResponse;

	}


	/**
	 * 插件-查询我的绩效（当天）
	 */
	@RequestMapping(value="/selectCurrentUserTodayRealtimePerformanceForPlugin",method=RequestMethod.POST)
	public ApiResponse selectCurrentUserTodayRealtimePerformanceForPlugin(@RequestParam("groupId") String groupId,
			@RequestParam("csNick") String csNick,
			@RequestParam("shopId") String shopId){
		ApiResponse apiResponse=null;
		try {
			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(csNick)){
				logger.info("shopId:{}，csNick：{}",shopId,csNick);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shop = this.getSelectShop(shopId);
			if(shop==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
			}
			UserShopQuery shopQuery=new UserShopQuery();
			shopQuery.setSelectedShop(true);
			shopQuery.setSelectedShop(shop);
			List<UserQuery> csNickQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery,groupId, "");
			Set<String> preCsNickSet=csNickQueryLst.stream().filter(u->u.getType()==1).filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
			Set<String> csNickSet=csNickQueryLst.stream().filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			apiResponse = realtimeShopPerformanceSummaryBusiness.selectCurrentUserRealtimePerformance(shopQuery, csNick,
					preCsNickSet,csNickSet, startDate, nowDate);

		} catch (Exception e) {
			logger.error("web selectCurrentUserTodayRealtimePerformance :{}",e.getMessage(),e);
		}
		return apiResponse;
	}



	/**
	 * 插件-查询我的绩效（昨天）
	 */
	@RequestMapping("/selectCurrentUserYesterDayRealtimePerformanceForPlugin")
	public ApiResponse selectCurrentUserYesterDayRealtimePerformanceForPlugin(@RequestParam("csNick") String csNick,
			@RequestParam("shopId") String shopId,@RequestParam("groupId")String groupId){
		ApiResponse apiResponse=null;
		try {
				if(StringUtils.isBlank(shopId)||StringUtils.isBlank(csNick)){
					logger.info("shopId:{}，csNick：{}",shopId,csNick);
					return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
				}
				ShopQuery shop = this.getSelectShop(shopId);
				if(shop==null){
					return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
				}
				UserShopQuery shopQuery=new UserShopQuery();
				shopQuery.setSelectedShop(true);
				shopQuery.setSelectedShop(shop);
				List<UserQuery> csNickQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery,groupId, "");
				Set<String> preCsNickSet=csNickQueryLst.stream().filter(u->u.getType()==1).filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
				Set<String> csNickSet=csNickQueryLst.stream().filter(u->u.getCsStatus()==1).map(u->u.getNick()).collect(Collectors.toSet());
				Date nowDate=new Date();
				Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
				Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
				apiResponse=realtimeShopPerformanceSummaryBusiness.selectCurrentUserRealtimePerformance(shopQuery, csNick, preCsNickSet,csNickSet, startDate, endDate);

		} catch (Exception e) {
			logger.error("web selectCurrentUserYesterDayRealtimePerformanceForPlugin:{}",e.getMessage(),e);
		}
		return apiResponse;
	}

	/**
	 * 查询店铺绩效（今天）
	 */
	@RequestMapping("/selectShopTodayRealtimePerformance")
	public ApiResponse selectShopTodayRealtimePerformance(@RequestParam("shopId")String shopId) throws LoginAuthException {
		ApiResponse apiResponse=null;
		UserShopQuery shopQuery=this.getCustUserByParam(shopId);
		try {
				Date nowDate=new Date();
				Date startDate = DateUtil.getStartTimeOfDate(nowDate);
				apiResponse=realtimeShopPerformanceSummaryBusiness.selectShopRealtimePerformance(shopQuery, startDate, nowDate);
		} catch (Exception e) {
			logger.error("web selectShopTodayRealtimePerformance:{}",e.getMessage(),e);
		}
		return apiResponse;
	}


	/**
	 * 查询店铺绩效（昨天）
	 */
	@RequestMapping("/selectShopYesterDayRealtimePerformance")
	public ApiResponse selectShopYesterDayRealtimePerformance(@RequestParam("shopId")String shopId) throws LoginAuthException {
		ApiResponse apiResponse = null;
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		try {
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			apiResponse = realtimeShopPerformanceSummaryBusiness.selectShopRealtimePerformance(shopQuery, startDate,
					endDate);
		} catch (Exception e) {
			logger.error("web selectShopYesterDayRealtimePerformance：{}",e.getMessage(), e);

		}
		return apiResponse;
	}



	/**
	 * 客服绩效（客服对比今日）
	 * @throws Exception
	 */
	@RequestMapping(value = "/selectCsPerformanceCompareToday",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceCompareToday(
			@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId) throws LoginAuthException {
		Map<String, Object> data = Maps.newHashMap();
		UserShopQuery shop = this.getCustUserByParam(shopId);
		try {
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(shop, groupId, "");
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO csNick : csNickByTypeList){
					csNickList.add(csNick.getCsNick());
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(nowDate);
			param.setCsNickByTypeLst(csNickByTypeList);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceInTime(shop.getSelectedShop(),param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_01,data);
		}

	}

	/**
	 * 客服绩效（客服对比昨日）
	 * @throws Exception
	 */
	@RequestMapping(value = "/selectCsPerformanceCompareYesterday",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceCompareYesterday(
			@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId) throws LoginAuthException {
		Map<String, Object> data = Maps.newHashMap();
		UserShopQuery shop = this.getCustUserByParam(shopId);
		try {
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(shop, groupId, "");
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO csNick : csNickByTypeList){
				csNickList.add(csNick.getCsNick());
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setCsNickByTypeLst(csNickByTypeList);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceInTime(shop.getSelectedShop(),param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_01,data);
		}

	}

	/**
	 * 插件获取通用的店铺，客服组，客服下拉框
	*
	 */
	@RequestMapping(value="/selectShopGroupsForPlugin",method=RequestMethod.POST)
	public ApiResponse selectMultiShopGroupsForPlugin(
			@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick) {
		ApiResponse apiResponse = null;
		Map<String, Object> result=Maps.newHashMap();
		List<MultiShopGroupVO> multiShopGroupLst=null;
		try {
			ShopQuery shop = this.getSelectShop(shopId);
			MasterServiceShopQuery shopQuery=new MasterServiceShopQuery();
			shopQuery.setMainShop(shop);
			multiShopGroupLst=shopSysManagerBusiness.selectMultiShopGroups(shopQuery.getMainShop());
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			multiShopGroupLst=new ArrayList<MultiShopGroupVO>(0);
			apiResponse=ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
			logger.error("web queryShopCsGroups：{}", e.getMessage(), e);
		}
		result.put("multiShopLst", multiShopGroupLst);
		apiResponse.setData(result);
		return apiResponse;
	}



	/**
	 * 插件-客服绩效（客服对比今日）
	 * @throws Exception
	 */
	@RequestMapping(value = "/selectCsPerformanceCompareTodayForPlugin",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceCompareTodayForPlugin(
			@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId) throws Exception {
		Map<String, Object> data = Maps.newHashMap();
		try {
			if(StringUtils.isBlank(shopId)){
				logger.info("shopId:{}，csNick：{}",shopId);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shop = this.getSelectShop(shopId);
			if(shop==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
			}
			UserShopQuery userShopQuery = new UserShopQuery();
			userShopQuery.setSelectedShop(true);
			userShopQuery.setSelectedShop(shop);
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(userShopQuery, groupId, "");
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO csNick : csNickByTypeList){
				csNickList.add(csNick.getCsNick());
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(nowDate);
			param.setCsNickByTypeLst(csNickByTypeList);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceInTime(userShopQuery.getSelectedShop(),param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_01,data);
		}

	}

	/**
	 * 插件-客服绩效（客服对比昨日）
	 * @throws Exception
	 */
	@RequestMapping(value = "/selectCsPerformanceCompareYesterdayForPlugin",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceCompareYesterdayForPlugin(
			@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId) throws Exception {
		Map<String, Object> data = Maps.newHashMap();
		try {
			if(StringUtils.isBlank(shopId)){
				logger.info("shopId:{}，csNick：{}",shopId);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shop = this.getSelectShop(shopId);
			if(shop==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
			}
			UserShopQuery userShopQuery = new UserShopQuery();
			userShopQuery.setSelectedShop(true);
			userShopQuery.setSelectedShop(shop);
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(userShopQuery, groupId, "");
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO csNick : csNickByTypeList){
				csNickList.add(csNick.getCsNick());
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));

			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setCsNickByTypeLst(csNickByTypeList);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceInTime(userShopQuery.getSelectedShop(),param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_01,data);
		}

	}

	/**
	 * 客服绩效（分时绩效今日）
	 */
	@RequestMapping(value = "selectCsPerformanceHourlyToday",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceHourlyToday(
			@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId,
			@RequestParam("hourDotStr") String hourDotStr) throws LoginAuthException {
		Map<String, Object> data;
		UserShopQuery shop = this.getCustUserByParam(shopId);
		try {
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(shop, groupId, "");
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO csNick : csNickByTypeList){
				csNickList.add(csNick.getCsNick());
			}
			List<Integer> hourDotList = ArraysUtil.stringToInt(hourDotStr);
			Integer startHour = hourDotList.get(0);
			Integer endHour = hourDotList.get(hourDotList.size()-1);
			Date startDate = DateUtil.getStartDateHour(new Date(),startHour);
			Date endDate = DateUtil.getEndDateHour(new Date(),endHour);
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setHourDotList(hourDotList);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceTimeSharing(shop.getSelectedShop(), param);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_02);
		}
	}

	/**
	 * 客服绩效（分时绩效昨日）
	 */
	@RequestMapping(value = "selectCsPerformanceHourlyYesterday",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceHourlyYesterday(
			@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId,
			@RequestParam("hourDotStr") String hourDotStr) throws LoginAuthException {
		Map<String, Object> data;
		UserShopQuery shop = this.getCustUserByParam(shopId);
		try {
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(shop, groupId, "");
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO csNick : csNickByTypeList){
				csNickList.add(csNick.getCsNick());
			}
			List<Integer> hourDotList = ArraysUtil.stringToInt(hourDotStr);
			Date nowDate = new Date();
			Integer startHour = hourDotList.get(0);
			Integer endHour = hourDotList.get(hourDotList.size()-1);
			Date startDate = DateUtil.getStartDateHour(DateUtil.getDateByPeriod(nowDate, -1), startHour);
			Date endDate = DateUtil.getEndDateHour(DateUtil.getDateByPeriod(nowDate, -1), endHour);
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setHourDotList(hourDotList);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceTimeSharing(shop.getSelectedShop(), param);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_02);
		}
	}

	/**
	 * 实时监控-不良接待
	 */
	@RequestMapping(value = "/selectBadReceiveMnoitor")
	public ApiResponse selectBadReceiveMnoitor(@RequestParam("shopId") String shopId,
			@RequestParam("warningType") String warningType) throws LoginAuthException {
		ApiResponse apiResponse;
		UserShopQuery userShopByParam = this.getUserShopByParam(shopId, null, null);
        ShopSystemsettingDTO shopSystemsetting = this.getShopSystemsetting();
        try {
            List<UserQuery> userQueryLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(userShopByParam, null,null);
            ApiResponse systemSettingForQN = shopSysManagerBusiness.getSystemSettingForQN(userShopByParam.getSelectedShop());
            ShopSystemsettingDTO sys = null;
            if(null != systemSettingForQN){
                if (systemSettingForQN.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())) {
                    Map<String, Object> sessionMap = systemSettingForQN.getData();
                    if(null != sessionMap && null != sessionMap.get("shopSystemsettingDTO")){
                        String jsonStr = JSONObject.toJSONString(sessionMap.get("shopSystemsettingDTO"));
                        sys= JSONObject.parseObject(jsonStr).toJavaObject(new ShopSystemsettingDTO().getClass());
                    }
                }
            } else {
                sys = shopSystemsetting;
			}
//			//默认显示今日和昨日的，按告警时间倒序排序
			Date today = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(today, -1));
			Date endDate = DateUtil.getEndTimeOfDate(today);
			apiResponse = realtimeShopPerformanceSummaryBusiness.selectBadReceiveMnoitor(userShopByParam.getSelectedShop(), userQueryLst, startDate, endDate, warningType,sys);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_01);
		}
		return apiResponse;
	}

	/**
	 * 实时监控-全局监控+坐席监控
	 */
	@RequestMapping("/selectShopMnoitor")
	public ApiResponse selectShopMnoitor(@RequestParam("shopId") String shopId,
			@RequestParam("groupId") String groupId,
			@RequestParam("keyWord") String keyWord,
			//模糊匹配符合的客服
			@RequestParam("csStatus") String csStatus) throws LoginAuthException {
		//csStatus		客服状态：1-全部客服，2-非离线客服
		ApiResponse apiResponse = null;
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		try {
			long s = System.currentTimeMillis();
			//查询店铺所有客服，全局监控需要
			List<UserQuery> userQueryList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQueryNew(shopQuery, groupId, keyWord);
			if (CollectionUtils.isEmpty(userQueryList)) {
				Map<String, Object> map = new HashMap<>();
				map.put("shopMnoitorVO", null);
				map.put("csMnoitorList", Lists.newArrayList());
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
				apiResponse.setData(map);
				return apiResponse;
			}

			ShopMnoitorParam shopMnoitorParam = new ShopMnoitorParam(groupId,keyWord,csStatus,userQueryList);
			apiResponse = realtimeShopPerformanceSummaryBusiness.selectShopMnoitor(shopQuery.getSelectedShop(), shopMnoitorParam);
			long e = System.currentTimeMillis();
			logger.info("实时监控-全局监控+坐席监控接口耗时：{} ms",e-s);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_03);
		}
		return apiResponse;
	}

	/**
	 * 实时看板-全局监控明细
	 */
	@RequestMapping("/selectGlobalMnoitor")
	public ApiResponse selectGlobalMnoitor(@RequestParam("deptId") String deptId,
			String queryParam,
            @RequestParam("seletDetail") Integer seletDetail,
			String currentPage,
			String size,
			@RequestParam("id") String id,
			@RequestParam("type") String type) {
		ApiResponse apiResponse;
		Map<String, Object> data = new HashMap<>();


		if(StringUtils.isBlank(deptId)){
			logger.error("deptId:{} is Empty",deptId);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
		}
		try {
			long s = System.currentTimeMillis();
//			//实时看板-全局监控：查询部门下的所有店铺
//			List<ShopBoardMnoitorParam> shopBoardMnoitorParams = realTimeBoardBusiness.ShopBoardMnoitorParam(deptId, queryParam,currentPage,size,data,id,type);
			//实时看板-全局监控：查询所有涉及到的rt-db
			List<String> rtDbLst = realTimeBoardBusiness.selectRtDbLst(deptId, queryParam,currentPage,size,data,id,type);
            long e = System.currentTimeMillis();
            logger.info("实时看板-查看所有店铺耗时：{} ms",e-s);


			s = System.currentTimeMillis();
//			realTimeBoardBusiness.selectGlobalMnoitor(shopBoardMnoitorParams, seletDetail,data);
            GlobalMnoitorParam globalMnoitorParam = new GlobalMnoitorParam(rtDbLst, seletDetail, deptId, queryParam, currentPage, size, data, id, type, new Date());
            realTimeBoardBusiness.selectGlobalMnoitorByRtdb(globalMnoitorParam);
			e = System.currentTimeMillis();
			logger.info("实时看板-全局监控耗时：{} ms",e-s);
            apiResponse=ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001.getCode(), ApiCodeEnum.CODE_SUCCESS_1001.getMsg(),globalMnoitorParam.getResultMap());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_06);
		}
		return apiResponse;
	}

	/**
	 * 告警调度接口
	 */
	@RequestMapping("/warnDispatcher")
	public ApiResponse warnDispatcher(@RequestParam("shopId") String shopId,
			@RequestParam("warnType") String warnType,//告警类型：1：全局监控-解卦，2：全局监控-挂起
			@RequestParam("csNick") String csNick) throws LoginAuthException {
		ApiResponse apiResponse;
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		try {
			//需要获取店铺主账号 -> mainShop -> sellerNick
			apiResponse = realtimeShopPerformanceSummaryBusiness.warnDispatcher(shopQuery,warnType,csNick);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_03);
		}
		return apiResponse;
	}

	/**
	 * 修改告警设置
	 */
	@RequestMapping("/updateWarnSetting")
	public ApiResponse updateWarnSetting(@RequestParam("shopId") String shopId,
			@RequestParam("openWarn") Boolean openWarn,
			@RequestParam("warnAcceptCs") String warnAcceptCs) throws LoginAuthException {
		ApiResponse apiResponse;
		MasterServiceShopQuery shop = this.getMasterServiceShopByParam(shopId);
		try {
			apiResponse = realtimeShopPerformanceSummaryBusiness.updateWarnSetting(shop.getSelectedShop(),openWarn,warnAcceptCs);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_06_05);
		}
		return apiResponse;
	}

	/**
	 * 绩效明细(今日)
	 * @throws Exception
	 */
	@RequestMapping(value = "selectCsPerformanceDetailToday",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceDetailToday(
			@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam("groupId") String groupId,
			@RequestParam("buyerNick") String buyerNick,
			@RequestParam(required = false,name = "dataIndex") String dataIndex,
			@RequestParam("keyWord") String keyWord) throws LoginAuthException {
		Map<String, Object> data;
		UserShopQuery shop = this.getCustUserByParam(shopId);
		try {
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(shop, groupId, csNick);
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO type : csNickByTypeList){
				csNickList.add(type.getCsNick());
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(nowDate);
			param.setBuyerNick(buyerNick);
			param.setDataIndex(dataIndex);
			param.setKeyWord(keyWord);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceDetail(shop.getSelectedShop(), param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_03);
		}
	}

	/**
	 * 绩效明细（昨日）
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "selectCsPerformanceDetailYesterday",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceDetailYesterday(
			@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam("groupId") String groupId,
			@RequestParam("buyerNick") String buyerNick,
			@RequestParam(required = false,name = "dataIndex") String dataIndex,
			@RequestParam("keyWord") String keyWord) throws LoginAuthException {
		Map<String, Object> data;
		UserShopQuery shop = this.getCustUserByParam(shopId);
		try {
			List<CsNickLstForRealTimeVO> csNickByTypeList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForHourlyPerformance(shop, groupId, csNick);
			List<String> csNickList = Lists.newArrayList();
			for(CsNickLstForRealTimeVO type : csNickByTypeList){
				csNickList.add(type.getCsNick());
			}
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setBuyerNick(buyerNick);
			param.setDataIndex(dataIndex);
			param.setKeyWord(keyWord);
			data = csPerformanceInTimeIndexBusiness.selectCsPerformanceDetail(shop.getSelectedShop(), param);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_03);
		}
	}


	/**
	 * 插件-绩效明细(今日)
	 */
	@RequestMapping(value = "selectCsPerformanceDetailTodayForPlugin",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceDetailTodayForPlugin(
			@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam("groupId") String groupId,
			@RequestParam("buyerNick") String buyerNick,
			@RequestParam("dataIndex") String dataIndex) {
		try {
			if(StringUtils.isBlank(shopId)){
				logger.info("shopId:{}，csNick：{}",shopId);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery = this.getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
			}
			UserShopQuery shop = new UserShopQuery();
			shop.setSelectedShop(true);
			shop.setSelectedShop(shopQuery);
			List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupId, csNick);
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(nowDate);
			param.setBuyerNick(buyerNick);
			param.setDataIndex(dataIndex);

			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,csPerformanceInTimeIndexBusiness.selectCsPerformanceDetail(shop.getSelectedShop(), param));
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_03);
		}
	}


	/**
	 * 插件-绩效明细(昨日)
	 */
	@RequestMapping(value = "selectCsPerformanceDetailYesterdayForPlugin",method=RequestMethod.POST)
	public ApiResponse selectCsPerformanceDetailYesterdayForPlugin(
			@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam("groupId") String groupId,
			@RequestParam("buyerNick") String buyerNick,
			@RequestParam("dataIndex") String dataIndex) {
		try {
			if(StringUtils.isBlank(shopId)){
				logger.info("shopId:{}，csNick：{}",shopId);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery = this.getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_04);
			}
			UserShopQuery shop = new UserShopQuery();
			shop.setSelectedShop(true);
			shop.setSelectedShop(shopQuery);
			List<String> csNickList = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSub(shop, groupId, csNick);
			Date nowDate = new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			CsPerformanceInTimeParam param = new CsPerformanceInTimeParam();
			param.setCsNickList(csNickList);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setBuyerNick(buyerNick);
			param.setDataIndex(dataIndex);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,csPerformanceInTimeIndexBusiness.selectCsPerformanceDetail(shop.getSelectedShop(), param));
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_07_03);
		}
	}



	/**
	 * 插件-绩效明细关联会话
	 */

	@RequestMapping(value = "/chatlogLstForPlugin", method = RequestMethod.POST)
    public ApiResponse searchChatlogLstForPlugin(
            @RequestParam(name = "shopId") String shopId,
            @RequestParam(name = "csNick") String csNick,
            @RequestParam(name = "buyerNick") String buyerNick,
            @RequestParam(name = "startDate") String startDate,
            @RequestParam(required = false, name = "realTimeFlag") String realTimeFlag,
            @RequestParam(name = "endDate") String endDate,
            @RequestParam(required = false, name = "sid")  String sid){
        ApiResponse apiResponse = null;
        Date sDate;
        Date eDate;
        if (StringUtils.isBlank(csNick) || StringUtils.isBlank(shopId) || StringUtils.isBlank(endDate)
                || StringUtils.isBlank(buyerNick) || StringUtils.isBlank(startDate)) {
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SE_01_04);
            Map<String, Object> map = Maps.newHashMap();
            List<ChatLogVO> chatLogMsgLst = Lists.newArrayList();
            map.put("chatLogMsgLst", chatLogMsgLst);
            apiResponse.setData(map);
            return apiResponse;
        }
        Boolean DbFlag = true;
        if(StringUtils.isNotBlank(realTimeFlag) && "1".equals(realTimeFlag)){
        	//实时绩效调用关联会话接口
        	DbFlag = false;
        }
        if (sid == null) {
          sid = "";//sid为null,会服务降级
      }
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
            //查询间隔不能超过十天
            if (DateFormatUtils.getDateByPeriod(sDate, 10).getTime() < eDate.getTime()) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SE_01_03);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
        	ShopQuery shop = this.getSelectShop(shopId);
        	boolean isPlugin=true;
            UserShopQuery shopQuery = new UserShopQuery();
            shopQuery.setSelectedShop(true);
            shopQuery.setSelectedShop(shop);
            List<UserQuery> csLst = shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, null, csNick);
            apiResponse = dataAnalysisBusiness.searchChatlogLst(shopQuery, startDate, endDate, csLst.get(0), buyerNick,sid, null, DbFlag,isPlugin, null, false, false, null);
            logger.info("return informace code:{}", apiResponse.getRpCode());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return apiResponse;
    }


	/**
	 * 待转化池
	 * @param shopId
	 * @param csNick
	 * @param groupId
	 * @param type 1:咨询未下单|2:下单未付款
	 * @param skuIds 商品sku
	 * @param buyerNick 顾客id
	 * @param chatRoundNum 聊天回合数:
	 * 						-1->不限制
	 * 						 1->1-4
	 * 						 5->5-8
	 *						 8->大于8
	 * @param sessionDurationTime 会话时长:
	 *						-1->不限制
	 * 						 0->1分钟以内
	 * 						 1->1-2
	 *						 2->2-4
	 *					     4->4-6
	 *						 6->6-10
	 *						 10->大于10
	 * @param orderId 订单编号
	 * @return
	 */
	@RequestMapping(value="/serachTodayNeededAllocatedCsConversionList",method=RequestMethod.POST)
	public 	ApiResponse serachNeededAllocatedCsConversionList(@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam("groupId") String groupId,
			@RequestParam("type")String type,
			@RequestParam(name = "skuIds",required = false) String skuIds,
			@RequestParam(name = "buyerNick",required = false) String buyerNick,
			@RequestParam(name = "chatRoundNum",required = false) String chatRoundNum,
			@RequestParam(name = "sessionDurationTime",required = false) String sessionDurationTime,
			@RequestParam(name = "orderId",required = false) String orderId,Byte dimension,
			@RequestParam("wareType")Integer wareType,
			@RequestParam("taskType")String taskType) throws LoginAuthException {
	long s1=System.currentTimeMillis();
		ApiResponse apiResponse=null;
		UserShopQuery shopQuery=this.getCustUserByParam(shopId);
        if (dimension == null) {
            dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
        }
		try {
			List<UserQuery>	 userQuery=	shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, groupId, csNick);
			List<String> csNickLst=	userQuery.stream().filter(u->u.getType().equals(1)).map(UserQuery::getNick).collect(Collectors.toList());
			Date nowDate=new Date();
			Date startDate = DateUtil.getStartTimeOfDate(nowDate);
			CustConversionParam param=new CustConversionParam();
			param.setType(type);
			param.setStartDate(startDate);
			param.setEndDate(nowDate);
			param.setSelectedAll(true);
            param.setDimension(dimension);
            param.setTaskType(taskType);
			if(StringUtils.isNotBlank(groupId)||StringUtils.isNotBlank(csNick)){
				param.setCsNickLst(csNickLst);
				param.setSelectedAll(false);
			}
			param.setBuyerNick(buyerNick);
			param.setChatRoundNum(StringUtils.isNotBlank(chatRoundNum)? parseInt(chatRoundNum):0);
			//param.setOrderId(StringUtils.isNotBlank(orderId)?Long.valueOf(orderId):null);
			param.setOrderIdPool(orderId);
			param.setSessionDurationTime(StringUtils.isNotBlank(sessionDurationTime)? parseInt(sessionDurationTime):0);
			List<Long> skuLst = null;
			if(StringUtils.isNotBlank(skuIds)){
				skuLst=Arrays.stream(skuIds.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
	    	}
			param.setSkuLst(skuLst);
			param.setDimension(dimension);
			param.setWareType(wareType);
			apiResponse= realtimeShopPerformanceSummaryBusiness.serachNeededAllocatedCsConversionList(shopQuery, param);
		} catch (Exception e) {
			logger.error("web serachTodayNeededAllocatedCsConversionList"+e.getMessage(),e);
		}
		logger.info("web  today controller 查询带转化池列表耗时：{}ms",System.currentTimeMillis()-s1);
		return apiResponse;
	}
	@RequestMapping(value="/serachYesterdayNeededAllocatedCsConversionList",method=RequestMethod.POST)
	public 	ApiResponse serachYesterdayNeededAllocatedCsConversionList(@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam("groupId") String groupId,
			@RequestParam("type")String type,
			@RequestParam(name = "skuIds",required = false) String skuIds,
			@RequestParam(name = "buyerNick",required = false) String buyerNick,
			@RequestParam(name = "chatRoundNum",required = false) String chatRoundNum,
			@RequestParam(name = "sessionDurationTime",required = false) String sessionDurationTime,
			@RequestParam(name = "orderId",required = false) String orderId,Byte dimension,
			@RequestParam("wareType")Integer wareType) throws LoginAuthException {
		ApiResponse apiResponse=null;
		long s1=System.currentTimeMillis();
		UserShopQuery shopQuery=this.getCustUserByParam(shopId);
		try {
            if (dimension == null) {
                dimension = PesCommonConstant.DIMENSION_SKU_BYTE;//初始化sku维度
            }
			List<UserQuery>	 userQuery=	shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, groupId, csNick);
			List<String> csNickLst=	userQuery.stream().filter(u->u.getType().equals(1)).map(UserQuery::getNick).collect(Collectors.toList());
			Date nowDate=new Date();
			Date startDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			Date endDate = DateUtil.getEndTimeOfDate(DateUtil.getDateByPeriod(nowDate, -1));
			CustConversionParam param=new CustConversionParam();
			param.setType(type);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setSelectedAll(true);
            param.setDimension(dimension);
			if(StringUtils.isNotBlank(groupId)||StringUtils.isNotBlank(csNick)){
				param.setCsNickLst(csNickLst);
				param.setSelectedAll(false);
			}
			param.setBuyerNick(buyerNick);
			param.setChatRoundNum(StringUtils.isNotBlank(chatRoundNum)?Integer.valueOf(chatRoundNum):0);

			param.setOrderIdPool(orderId);
			param.setSessionDurationTime(StringUtils.isNotBlank(sessionDurationTime)?Integer.valueOf(sessionDurationTime):0);
			List<Long> skuLst = null;
			if(StringUtils.isNotBlank(skuIds)){
				skuLst=Arrays.stream(skuIds.split(",")) .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
	    	}
			param.setWareType(wareType);
			param.setSkuLst(skuLst);
			apiResponse= realtimeShopPerformanceSummaryBusiness.serachNeededAllocatedCsConversionList(shopQuery, param);

		} catch (Exception e) {
			logger.error(" web serachYesterdayNeededAllocatedCsConversionList :{}",e.getMessage(),e);
		}
		logger.info("web  yesterr controller 查询带转化池列表耗时：{}ms",System.currentTimeMillis()-s1);
		return apiResponse;
	}

	/**
	 * 保存分配的待转化数据
	 *
	 * @param csConversions 待分配的客服转化列表(批量分配所选)
	 * @return
	 */
	@RequestMapping(value = "/saveAllocatedCsConversionLst", method = RequestMethod.POST)
	public Object saveAllocatedCsConversionLst(@RequestParam("shopId") String shopId,
											   @RequestParam("csConversions") String csConversions,
											   @RequestParam(value = "flag", required = false) Integer flag) throws LoginAuthException {
		RestApiResponse2<CsAllocateResult> resp = null;
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		ShopUserDTO user = this.getCurrentUser();
		try {
			resp = realtimeShopPerformanceSummaryBusiness.saveAllocatedCsConversionLst(shopQuery, user, csConversions, flag);
		} catch (Exception e) {
			logger.error("web saveAllocatedCsConversionLst :{}", e.getMessage(), e);
		}
		return resp;
	}

	/**
	 * 保存分配的待转化数据
	 *
	 * @param String type 待分配的客服转化列表(一件分配绩效客服)
	 * @return
	 */
	@RequestMapping(value = "/oneStepSaveAllocatedCsConversionLst", method = RequestMethod.POST)
	public Object oneStepSaveAllocatedCsConversionLst(@RequestParam("shopId") String shopId,
													  @RequestParam("type") String type,
													  @RequestParam(value = "dateFlag") Integer dateFlag,
													  @RequestParam("wareType") Integer wareType,
													  @RequestParam("taskType") String taskType) throws LoginAuthException {
		RestApiResponse2<CsAllocateResult> resp = null;
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		ShopUserDTO user = this.getCurrentUser();
		try {
			Date startDate = null;
			Date endDate = null;
			//今日
			if (dateFlag == 1) {
				startDate = DateUtil.getStartTimeOfDate(new Date());
				endDate = new Date();
			} else {
				startDate = DateUtils.getStartTimeOfDate(DateUtils.getDateByPeriod(new Date(), -1));
				endDate = DateUtil.getEndTimeOfDate(startDate);
			}
			CustConversionParam param = new CustConversionParam();
			param.setType(type);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setWareType(wareType);
			param.setTaskType(taskType);
			resp = realtimeShopPerformanceSummaryBusiness.oneStepSaveAllocatedCsConversionLst(shopQuery, user, param);
		} catch (Exception e) {
			logger.error("web oneStepSaveAllocatedCsConversionLst :{}", e.getMessage(), e);
		}
		return resp;
	}

	/**
	 * 保存分配的待转化数据
	 *
	 * @param String type
	 * @param String allocateCsStr待分配的客服转化列表(批量分配代分配的客服)
	 * @return
	 */
	@RequestMapping(value = "/batchSaveAllocatedCsConversionLst", method = RequestMethod.POST)
	public Object batchSaveAllocatedCsConversionLst(@RequestParam("shopId") String shopId,
													@RequestParam("type") String type,
													@RequestParam("allocateCsStr") String allocateCsStr,
													@RequestParam(value = "dateFlag") Integer dateFlag,
													@RequestParam("wareType") Integer wareType,
													@RequestParam("taskType") String taskType) throws LoginAuthException {
		RestApiResponse2<CsAllocateResult> resp = null;
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		ShopUserDTO user = this.getCurrentUser();
		try {
			List<allocateCsConversionParam> allocateCsLst = JacksonUtils.json2list(allocateCsStr, allocateCsConversionParam.class);
			Set<String> csNickSet = Sets.newHashSet();
			StringBuffer sb = new StringBuffer();
			boolean existFlag = false;
			for (allocateCsConversionParam allocateCs : allocateCsLst) {
				if (csNickSet.contains(allocateCs.getAllocatedCsNick())) {
					sb.append(allocateCs.getAllocatedCsNick() + ",");
					existFlag = true;
				}
				csNickSet.add(allocateCs.getAllocatedCsNick());
			}
			if (existFlag) {
				String csNicks = sb.substring(0, sb.toString().length() - 1);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_03_03, csNicks);

			}

			Date startDate;
			Date endDate;
			//今日
			if (dateFlag == 1) {
				startDate = DateUtil.getStartTimeOfDate(new Date());
				endDate = new Date();
			} else {
				startDate = DateUtils.getStartTimeOfDate(DateUtils.getDateByPeriod(new Date(), -1));
				endDate = DateUtil.getEndTimeOfDate(startDate);
			}
			CustConversionParam param = new CustConversionParam();
			param.setAllocateCsStr(allocateCsStr);
			param.setType(type);
			param.setStartDate(startDate);
			param.setEndDate(endDate);
			param.setWareType(wareType);
			param.setTaskType(taskType);
			resp = realtimeShopPerformanceSummaryBusiness.batchSaveAllocatedCsConversionLst(shopQuery, user, param);
		} catch (Exception e) {
			logger.error("web batchSaveAllocatedCsConversionLst :{}", e.getMessage(), e);
		}
		return resp;
	}

	/**
	 * 一键追单话术查询
	 */
	@Deprecated
	@RequestMapping(value="/getToolsSettingByShopId")
	public ApiResponse getToolsSettingByShopId(@RequestParam("shopId") String shopId) throws LoginAuthException {
		ApiResponse apiResponse=null;
		UserShopQuery shopQuery=this.getCustUserByParam(shopId);
		try {
			apiResponse=realtimeShopPerformanceSummaryBusiness.getToolsSettingByShopId(shopQuery);
		} catch (Exception e) {
			logger.info("web getToolsSettingByShopId error" + e.getMessage());
		}
		return apiResponse;
	}

	/**
	 * 一键追单话术设置
	 */
	@Deprecated
	@RequestMapping(value="/setShopToolsSetting")
	public ApiResponse setShopToolsSettingd(@RequestParam("shopId") String shopId,
			@RequestParam("xiadanWord") String xiadanWord,
			@RequestParam("payWord") String payWord,

			@RequestParam("sendGoodsUrl") Boolean sendGoodsUrl) throws LoginAuthException {
		ApiResponse apiResponse=null;
		UserShopQuery shopQuery=this.getCustUserByParam(shopId);
		try {
			logger.info("商品链接勾选"+sendGoodsUrl);
			apiResponse=realtimeShopPerformanceSummaryBusiness.setShopToolsSetting(shopQuery, xiadanWord, payWord,sendGoodsUrl);
		} catch (Exception e) {
			logger.info("web setShopToolsSettingd error :{}" , e.getMessage(),e);
		}
		return apiResponse;
	}

	/**
	 * 任务管理列表
	 * @param shopId
	 * @param groupId
	 * @param csNick
	 * @param type 任务分类 全部"" 任务类型orderType为 1 普通，type：1：咨询未下单 2，下单未付款 3静默下单未付款
	 *             						orderType为 2 预约,type：1：咨询未下单 2，下单未付款
	 *             						orderType为 3 预售，type：1：咨询未下单 2，下单未付定金 3 付定金未付尾款
	 * @param buyerNick
	 * @param orderType 任务类型 1：普通，2：预约 ，3：预售
	 * @param remindType
	 * @param conversionStatus
	 * @param conversionResult
	 * @param startDate
	 * @param endDate
	 * @param taskTotalflag
	 * @param orderId
	 * @param smsSendStatus
	 * @param sendType
	 * @return
	 * @throws LoginAuthException
	 */
	@RequestMapping(value="/serachAllocatedConvertedLst")
	public ApiResponse serachAllocatedConvertedLst(@RequestParam("shopId") String shopId,
			@RequestParam(value="groupId",required=false) String groupId,
			@RequestParam(name="csNick",required=false) String csNick,
			@RequestParam(name="type",required=false) String type,
			@RequestParam(name="buyerNick",required=false) String buyerNick,
			@RequestParam(name="orderType",required=false) String orderType,
			@RequestParam(name="remindType",required=false) String remindType,
			@RequestParam(name="conversionStatus",required=false) String conversionStatus,
			@RequestParam(name="conversionResult",required=false) String conversionResult,
			@RequestParam(name="startDate",required=false) String startDate,
			@RequestParam(name="endDate",required=false) String endDate,
			@RequestParam(name="taskTotalflag",required=false) Integer taskTotalflag,
             @RequestParam(name="orderId",required=false) String orderId,
             @RequestParam(name="smsSendStatus",required=false) String smsSendStatus,
			@RequestParam(name="sendType",required=false) String sendType,
			@RequestParam(name = "wordId",required = false) String wordId,
			@RequestParam(name = "skuIds",required = false) String skuIds
			) throws LoginAuthException {
		List<String> skuList = null;
		if(!Strings.isNullOrEmpty(skuIds)){
			skuList = Arrays.asList(skuIds.split(","));
		}
		ApiResponse apiResponse=null;
		Date startTime=null;
		Date endTime =null;
		if(StringUtils.isNoneBlank(startDate)&&StringUtils.isNoneBlank(endDate)){
			startTime = DateUtil.getStartTimeOfDate(CommonDateUtils.parseYMd(startDate));
			endTime= DateUtil.getEndTimeOfDate(CommonDateUtils.parseYMd(endDate));
		}
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		try {
			CustConversionParam param=new CustConversionParam();
			if(StringUtils.isBlank(groupId)&&StringUtils.isBlank(csNick)){

			}else{
				List<UserQuery>	 userQuery=	shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, groupId, csNick);
				List<String> csNickLst=userQuery.stream().map(UserQuery::getNick).collect(Collectors.toList());
				if(CollectionUtils.isEmpty(csNickLst)){
					apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
					LinkedHashMap<String, Object> result = new LinkedHashMap<>();
					result.put("csConversionLst", Collections.EMPTY_LIST);
					apiResponse.setData(result);
					return apiResponse;
				}
				param.setCsNickLst(csNickLst);
			}
			param.setSendType(sendType);
			if (StringUtils.isNotBlank(conversionResult)) {
				try {
					Integer.parseInt(conversionResult);
				}catch (Exception e){
					throw new Exception(e.getMessage());
				}
				param.setConversionResult(conversionResult);
			}else {
				param.setConversionResult("");
			}

			param.setConversionStatus(conversionStatus);
			param.setType(type);
			if(StringUtils.isNotBlank(buyerNick)){
				buyerNick=buyerNick.trim();
			}
			param.setBuyerNick(buyerNick);
			param.setOrderType(orderType);
			param.setRemindType(remindType);
			param.setStartDate(startTime);
			param.setEndDate(endTime);
			param.setTaskTotalflag(taskTotalflag);
			param.setSendType(sendType);
			param.setSmsSendStatus(smsSendStatus);
			param.setWordId(wordId);
			param.setSkuIdList(skuList);
			try{
				param.setOrderId(StringUtils.isNotBlank(orderId)?Long.valueOf(orderId):null);
			}catch (Exception ee){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
				Map<String, Object> result = new LinkedHashMap<>();
				result.put("csConversionLst", Collections.EMPTY_LIST);
				apiResponse.setData(result);
				return apiResponse;
			}

			apiResponse=realtimeShopPerformanceSummaryBusiness.serachAllocatedConvertedLst(shopQuery, param);

		} catch (Exception e) {
			logger.info("web serachAllocatedConvertedLst error :{}" , e.getMessage(),e);
		}
		return apiResponse;
	}


	/**
	 * @Description:（任务统计）
	*
	 */
	@RequestMapping(value="/serachCsConversionTaskTotal")
	public ApiResponse serachCsConversionTaskTotal(@RequestParam("shopId") String shopId,
			@RequestParam(name="groupId",required=false) String groupId,
			@RequestParam(name="csNick",required=false) String csNick,
			@DateFormat Date startDate,
			@DateFormat Date endDate,
			@RequestParam(name="type",required=false) String type,
			@RequestParam(name="orderType",required=false) String orderType,
			@RequestParam(name="remindType",required=false) String remindType,
			@RequestParam(name = "wordId",required = false) String wordId,
			@RequestParam(name = "skuIds",required = false) String skuIds

			) throws LoginAuthException {
		List<String> skuList = null;
		if(!Strings.isNullOrEmpty(skuIds)){
			skuList = Arrays.asList(skuIds.split(","));
		}
		ApiResponse apiResponse=null;
		Date startTime = DateUtil.getStartTimeOfDate(startDate);
		Date endTime = DateUtil.getEndTimeOfDate(endDate);
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		try {
			CustConversionParam param=new CustConversionParam();
			param.setStartDate(startTime);
			param.setEndDate(endTime);
			param.setConversionResult("");
			param.setTaskTotalflag(2);
			param.setType(type);
			param.setRemindType(remindType);
			param.setOrderType(orderType);
			param.setWordId(wordId);
			param.setSkuIdList(skuList);
			apiResponse=realtimeShopPerformanceSummaryBusiness.serachCsConversionTaskTotal(shopQuery, param);
		} catch (Exception e) {
			logger.info("web serachCsConversionTaskTotal error" + e.getMessage(),e);
		}
		return apiResponse;
	}
	/**
	 * 一键提醒(插件查询已分配的任务)
	 */
	@RequestMapping(value="/serachAllocateCsConversionForOneWarn")
	public ApiResponse serachAllocateCsConversionForOneWarn(@RequestParam("csNick") String csNick,
			@RequestParam("shopId") String shopId,
			@RequestParam("urgeLoss") Boolean urgeLoss){
		ApiResponse apiResponse=null;
		try {
			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(csNick)){
				logger.info("shopId:{}，csNick：{}",shopId,csNick);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery=	getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}
			List<String> csNickLst=Lists.newArrayList();
			csNickLst.add(csNick);
			CustConversionParam param=new CustConversionParam();
			param.setCsNickLst(csNickLst);
			param.setStartDate(DateUtils.getDateByPeriod(new Date(), -PesConstants.TASK_CONVERSION_DAY));
			param.setEndDate(new Date());
			//param.setType("1,2,3");
			param.setConversionResult(ConversionResultEnum.ING.getType());
			param.setUrgeLoss(urgeLoss);
			apiResponse=	realtimeShopPerformanceSummaryBusiness.serachAllocateCsConversionForOneWarn(shopQuery, param);
		} catch (Exception e) {
			logger.error("web serachAllocateCsConversionForOneWarn " + e.getMessage(), e);
		}
		return apiResponse;
	}

	/**
	 * (插件)一键提醒(设置)
	 */
	@RequestMapping(value="/setOneWarnCsConversion")
	public ApiResponse setOneWarnCsConversion(@RequestParam("conversionId") Long conversionId,
											@RequestParam("type") String type,
											@RequestParam("orderId") Long orderId,
											@RequestParam("buyerNick") String buyerNick,
											@RequestParam("operateType") String operateType,
											@RequestParam("csNick") String csNick,
											@RequestParam("shopId") String shopId,
											@RequestParam("orderType") String orderType,
											@RequestParam("activityId") String activityId,
											@RequestParam("wordId") String wordId){
		ApiResponse apiResponse=null;
		try {
			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(csNick)){
				logger.info("shopId:{}，csNick：{}",shopId,csNick);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery=	getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}
			List<String> csNickLst=Lists.newArrayList();
			csNickLst.add(csNick);
			CustConversionParam param=new CustConversionParam();
			param.setStartDate(DateUtils.getStartTimeOfDate(new Date()));
			param.setEndDate(new Date());
			param.setCsNickLst(csNickLst);
			param.setType(type);
			param.setBuyerNick(buyerNick);
			param.setOrderId(orderId);
			param.setConversionId(conversionId);
			param.setActivityId(activityId);
			param.setOrderType(orderType);
			param.setWordId(wordId);
			apiResponse = realtimeShopPerformanceSummaryBusiness.setOneWarnCsConversion(shopQuery, param, operateType);
		} catch (Exception e) {
			logger.error(" web setOneWarnCsConversion :{}" , e.getMessage(), e);
		}
		return apiResponse;
	}

	@RequestMapping("/selectShopRemindWordLstForPlugin")
	public ApiResponse selectShopRemindWordLstForPlugin(
			@RequestParam("shopId") String shopId,
			@RequestParam("csNick") String csNick,
			@RequestParam(value = "origin",required = false) String origin){
		Map<Integer, List<ShopRemindWordDTO>> result;
		try {
			if(StringUtils.isBlank(shopId)){
				logger.info("shopId:{}，",shopId);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery=	getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}
			result=	realtimeShopPerformanceSummaryBusiness.selectShopRemindWordLst(shopQuery,origin,csNick);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
		} catch (Exception e) {
			result= new HashMap<>();
			logger.error(" web setOneWarnCsConversion" + e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_06,result);
		}
	}

	/**
	 * 任务管理修改
	 */
	@RequestMapping(value="/updateAllocatedCsNickById")
	public ApiResponse updateAllocatedCsNickById(@RequestParam("conversionId") Long conversionId,
			@RequestParam("shopId") String shopId,
			@RequestParam("allocatedCsNick") String allocateNick
		) throws LoginAuthException {
		ApiResponse apiResponse=null;
		UserShopQuery shopQuery = this.getCustUserByParam(String.valueOf(shopId));
		try {
			CustConversionParam param=new CustConversionParam();
			param.setAllocateCsStr(allocateNick);
			param.setConversionId(conversionId);
			apiResponse=	realtimeShopPerformanceSummaryBusiness.updateAllocateCsNickById(shopQuery.getSelectedShop(),param);
		} catch (Exception e) {
			logger.error(" web updateAllocatedCsNickById :{}" , e.getMessage(), e);
		}
		return apiResponse;
	}

	/**
	 * 批量修改分配客服
	 * @param allocateCsStr
	 * @param shopId
	 * @return
	 */
	@RequestMapping(value="/batchUpdateAllocatedCsNick")
	public ApiResponse batchUpdateAllocatedCsNick(
			@RequestParam("allocateCsStr") String allocateCsStr,
			@RequestParam("shopId") String shopId
		) throws LoginAuthException {
		ApiResponse apiResponse=null;
		UserShopQuery shopQuery = this.getCustUserByParam(String.valueOf(shopId));
		try {
			CustConversionParam param=new CustConversionParam();
			param.setAllocateCsStr(allocateCsStr);
			apiResponse = realtimeShopPerformanceSummaryBusiness.batchUpdateAllocateCsNick(shopQuery.getSelectedShop(),param);
		} catch (Exception e) {
			logger.error(" web updateAllocatedCsNickById :{}" , e.getMessage(), e);
		}
		return apiResponse;
	}


	/**
	 *实时看板
	 *
	 * 1 查询客户实时榜（以每个客服为单位）
	 * 2 今日绩效指标/较昨日绩效指标
	 * 3 今日销售额
	 * 4 本月目标/已完成以及完成比率
	 */
	@RequestMapping("/selectRealtimePerformanceBord")
	public ApiResponse selectRealtimePerformanceBord(
			@RequestParam("shopId")String shopId) {
		ApiResponse apiResponse;
		UserShopQuery shopQuery=new UserShopQuery();
		shopQuery.setSelectedShop(this.getSelectShop(shopId));
		try {
			if(null == shopQuery.getSelectedShop()){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_03.getCode(),ApiCodeEnum.CODE_ERROR_SF_04_03.getMsg());
			}
			apiResponse=csPerformanceInTimeIndexBusiness.selectRealTimePerformanceBordInfo(shopQuery);
		} catch (Exception e) {
			logger.error("selectRealtimePerformanceBord error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_05_01.getCode(),ApiCodeEnum.CODE_ERROR_SF_05_01.getMsg());
		}
		return apiResponse;
	}

	/**
	 * 趋势图
	 */
	@RequestMapping("/selectCsTeamIntimePerformance")
	public ApiResponse selectCsTeamIntimePerformance(
			@RequestParam("shopId")String shopId,
			@RequestParam("date") String date) {
		ApiResponse apiResponse = null;
		try {
			apiResponse = csTeamIntimePerformanceBusiness.searchCsTeamIntimePerformance(this.getSelectShop(shopId), date);
		} catch (Exception e) {
			logger.error("趋势图apiResponse{}",e.getMessage(), e);
		}
		logger.info("趋势图apiResponse{}", apiResponse);
		return apiResponse;
	}


	/**
	 * 实时看板-看板设置
	 */
	@RequestMapping("/insertSaleIndexSetting")
	public ApiResponse insertSaleIndexSetting(
			@RequestParam("shopId") String shopId,
			@RequestParam("shopSaleTarget") String shopSaleTarget,
			@RequestParam("csSaleTarget") String csSaleTarget) throws LoginAuthException {
		ApiResponse apiResponse;
		UserShopQuery shopQuery=this.getCustUserByParam(shopId);
		try {
			SaleIndexSettingParam param = new SaleIndexSettingParam();
			param.setCsSaleTarget(Double.valueOf(csSaleTarget));
			param.setShopSaleTarget(Double.valueOf(shopSaleTarget));
			apiResponse = saleIndexSettingDataBusiness.insertSaleIndexSetting(shopQuery.getSelectedShop(), param);
			return apiResponse;
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_05_01.getCode(),ApiCodeEnum.CODE_ERROR_SF_05_01.getMsg());
		}
	}


	@RequestMapping("/getShopInfoByShopNameForPlugin")
	public ApiResponse getShopInfoByShopNameForPlugin(@RequestParam("shopName") String shopName){
		ApiResponse apiResponse=null;
		try {
				apiResponse=shopSysManagerBusiness.getShopInfoByShopNameForPlugin(shopName);
		} catch (Exception e) {
			logger.error("getShopInfoByShopNameForPlugin error:{}",e.getMessage(),e);
		}
		return apiResponse;
	}


	/**
	 * 查询在线客服
	 * @return
	 */
	@RequestMapping("/selectLoginCsForRtPool")
	public ApiResponse selectLoginCsForRtPool(@RequestParam("shopId") String shopId) throws LoginAuthException {
		UserShopQuery shopQuery = this.getCustUserByParam(shopId);
		ShopUserDTO userDTO = this.getCurrentUser();
		try {
			long s = System.currentTimeMillis();
			List<UserQuery>	userQuery =	shopSysManagerBusiness.selectCsNickByShopIdByGroupIdByCsNickForSubQuery(shopQuery, "", "");
			if(CollectionUtils.isNotEmpty(userQuery)){
				userQuery = userQuery.stream().filter(c->c.getCsStatus()==1).collect(Collectors.toList());
			}
			Map<String, Object> map = realtimeShopPerformanceSummaryBusiness.selectLoginCs(shopQuery,userQuery, userDTO);
			long e = System.currentTimeMillis();
			logger.info("查询在线客服耗时：{}",e-s);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,map);
		} catch (Exception e) {
			logger.error("get shop login cs error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CS_01_01);
		}
	}


	@RequestMapping("/selectShopPoolTaskCount")
	public Object selectShopPoolTaskCount(@RequestParam("shopId") String shopId,@RequestParam("csNick") String csNick){
		try {
			if(StringUtils.isBlank(shopId)){
				logger.info("shopId:{}，",shopId);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery=	getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}

			CustConversionParam param=new CustConversionParam();
			List<String> csNickLst=Lists.newArrayList();
			csNickLst.add(csNick);
			param.setCsNickLst(csNickLst);
			param.setStartDate(DateUtils.getDateByPeriod(new Date(),-2));
			param.setEndDate(new Date());
			param.setType("1,2,3");
			param.setConversionStatus(ConversionStatusEnum.UN_EXECUTE.getType());
			param.setConversionResult(ConversionResultEnum.ING.getType());

			Map<String,Object> result= realtimeShopPerformanceSummaryBusiness.selectShopPoolTaskCount(shopQuery,param,csNick);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);
		} catch (Exception e) {
			logger.error(" web selectShopPoolTaskCount error:{}" , e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1003);
		}
	}


	@RequestMapping(value="/sendCouponForPlugin")
	public ApiResponse sendCouponForPlugin(@RequestParam("couponId") String couponId,
											  @RequestParam("shopId") String shopId,
											 @RequestParam("buyerNick") String buyerNick,
										   @RequestParam("csNick") String csNick){
//		try {
//			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(buyerNick)||StringUtils.isBlank(couponId)){
//				logger.info("shopId:{}，buyerNick：{} ,couponId:{}",shopId,buyerNick,couponId);
//				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
//			}
//			ShopQuery shopQuery=	getSelectShop(shopId);
//			if(shopQuery==null){
//				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
//			}
//			boolean sendFlag = userSendCouponOperator.isSendCouponSuccess(shopQuery.getSessionKey(),buyerNick,Long.valueOf(couponId),shopQuery.getShopId());
//			logger.info(" web shopId:{}date :{},buyerNick:{},sendFlag:{}",shopId,new Date(),buyerNick,sendFlag);
//			return  ApiResponse.of(ApiCodeEnum.CODE_ERROR_SC_01_02,sendFlag);
//		} catch (Exception e) {
//			logger.error(" web  buyerNick:{} sendCouponForPlugin :{}" ,buyerNick, e.getMessage(), e);
//			return  ApiResponse.of(ApiCodeEnum.CODE_ERROR_SC_01_01);
//		}
		try{
			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(buyerNick)||StringUtils.isBlank(csNick)){
				logger.info("shopId:{}，csNick：{} ,buyerNick:{}",shopId,csNick,buyerNick);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery=	getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}
			boolean sendFlag = shopBatchRemindOperator.sendCoupon(shopQuery.getVenderId(), shopQuery.getSessionKey(), csNick,buyerNick,couponId);
			logger.info(" web shopId:{}date :{},buyerNick:{},sendWordFlag:{}",shopId,new Date(),buyerNick,sendFlag);
			return  ApiResponse.of(ApiCodeEnum.CODE_ERROR_SC_01_02,sendFlag);
		}catch (Exception e){
			logger.error(" web  buyerNick:{} sendCouponForPlugin :{}" ,buyerNick, e.getMessage(), e);
			return  ApiResponse.of(ApiCodeEnum.CODE_ERROR_SC_01_01);
		}
	}

	@RequestMapping(value="/sendRemindWordForPlugin")
	public ApiResponse sendRemindWordForPlugin(
										   @RequestParam("shopId") String shopId,
										   @RequestParam("buyerNick") String buyerNick,
										   @RequestParam("csNick") String csNick,
										   @RequestParam("word") String word,
										   @RequestParam("orderId") Long orderId,
										   @RequestParam("skuId") String skuId,
										   @RequestParam("isUrl") boolean isUrl,
										   @RequestParam("isConsultNotOrder") boolean isConsultNotOrder){
		try {
			if(StringUtils.isBlank(shopId)||StringUtils.isBlank(buyerNick)||StringUtils.isBlank(csNick)||StringUtils.isBlank(word)){
				logger.info("shopId:{}，csNick：{} ,buyerNick:{},word:{}",shopId,csNick,buyerNick,word);
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_05);
			}
			ShopQuery shopQuery=	getSelectShop(shopId);
			if(shopQuery==null){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_RT_01_03);
			}
			String content = isSendWordUrl(word, skuId, isUrl, isConsultNotOrder);
			boolean sendFlag = shopBatchRemindOperator.batchRemindMessagePush(shopQuery,csNick,buyerNick,content,orderId);
			logger.info(" web shopId:{}date :{},buyerNick:{},orderId:{},word:{},sendWordFlag:{}",shopId,new Date(),buyerNick,orderId,word,sendFlag);
			return  ApiResponse.of(ApiCodeEnum.CODE_ERROR_SC_01_02,sendFlag);
		} catch (Exception e) {
			logger.error(" web  buyerNick:{} sendRemindWordForPlugin :{}" ,buyerNick, e.getMessage(), e);
			return  ApiResponse.of(ApiCodeEnum.CODE_ERROR_SC_01_03);
		}
	}

	private String isSendWordUrl(String word, String skuId, boolean isUrl, boolean isConsultNotOrder) {
		if(isConsultNotOrder && isUrl && skuId != null && !skuId.equals("") && !skuId.equals("null")){
			word += String.format(CommonConstants.SKUID_URL,skuId);
		}

		return word;
	}

}
