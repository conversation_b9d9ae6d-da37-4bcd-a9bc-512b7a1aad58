  
package com.pes.jd.controller;

import com.alibaba.fastjson.JSON;
import com.pes.jd.business.sub.RealtimePerformanceBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**  
 * ClassName:RealtimePerformanceController <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午3:08:55 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@RestController
@RequestMapping("/realtime/performance")
public class RealtimePerformanceController {

	private static final Logger logger = LoggerFactory.getLogger(RealtimePerformanceController.class);
	@Autowired
	private RealtimePerformanceBusiness realtimePerformanceBusiness;
	/**
	 * 查询我的绩效
	 */
	@RequestMapping("/selectCurrentUserRealtimePerformance")
	public ApiResponse selectCurrentUserRealtimePerformance(@RequestParam("shop") ShopQuery shop,
			@RequestParam("csNick") String csNick,
			@RequestParam("csNickLst") String csNickLstStr,
			@RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr){
		Map<String, Object> result=null;
		try {
			List<String>csNickLst=	JSON.parseArray(csNickLstStr, String.class);
			Date startDate=DateUtils.parseYMdHms(startDateStr);
			Date endDate=DateUtils.parseYMdHms(endDateStr);
			result=	realtimePerformanceBusiness.selectCurrentUserRealtimePerformance(shop,csNick,csNickLst,startDate,endDate);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_01_02,result);
		}
	}
}
  
