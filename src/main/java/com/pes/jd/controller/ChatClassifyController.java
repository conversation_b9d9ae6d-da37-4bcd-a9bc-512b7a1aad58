package com.pes.jd.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.pes.jd.business.sub.ChatClassifyBusiness;
import com.pes.jd.model.DO.CsChatlog;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.entity.ChatClassify;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.*;

/**
 * Controller for chat classification statistics
 */
@RestController
@RequestMapping("/chatClassify/")
public class ChatClassifyController extends BaseController {

    private Logger logger = LoggerFactory.getLogger(ChatClassifyController.class);

    @Autowired
    private ChatClassifyBusiness chatClassifyBusiness;

    /**
     * Query chat classification statistics by shop and date range
     * Returns a simple JSON list with classify type and count only, sorted by count in descending order
     *
     * @return API response with a list of classify types and their counts, sorted by count in descending order
     */
    @PostMapping("queryStatsByDateRange")
    public ApiResponse queryStatsByDateRange(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            // Parse dates for current period
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            logger.info("Created ShopCommonParam: {}", shop);

            List<Map<String, Object>> classifyStats;

            // Check if lastStartDate and lastEndDate are provided
            if (requestMap.containsKey("lastStartDate") && requestMap.containsKey("lastEndDate")) {
                // Parse dates for previous period
                Date lastStartDate = parseDate(requestMap.get("lastStartDate").toString());
                Date lastEndDate = parseDate(requestMap.get("lastEndDate").toString());

                // Call the business method with both date ranges
                classifyStats = chatClassifyBusiness.queryClassifyStatsByDateRange(
                        shop, startDate, endDate, lastStartDate, lastEndDate);
            } else {
                // Call the business method with only the current date range
                classifyStats = chatClassifyBusiness.queryClassifyStatsByDateRange(
                        shop, startDate, endDate);
            }

            // Return the list directly without wrapping it in another object
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", classifyStats));
        } catch (Exception e) {
            logger.error("Error querying chat classification statistics by shop and date range", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }

    /**
     * Query chat classification statistics by shop and date
     * Returns a simple JSON list with classify type and count only, sorted by count in descending order
     *
     * @return API response with a list of classify types and their counts, sorted by count in descending order
     * @deprecated Use queryStatsByDateRange instead
     */
    @PostMapping("queryStatsByDate")
    public ApiResponse queryStatsByDate(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            // Parse date
            Date date = parseDate(requestMap.get("date").toString());

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            logger.info("Created ShopCommonParam: {}", shop);

            List<Map<String, Object>> classifyStats = chatClassifyBusiness.queryClassifyStatsByDate(
                    shop, date);

            // Return the list directly without wrapping it in another object
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, classifyStats);
        } catch (Exception e) {
            logger.error("Error querying chat classification statistics by shop and date", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }

    /**
     * Query chat classification statistics by shop, date and classify type
     * Returns summary statistics for the specified classify type
     *
     * @return API response with chat classification statistics
     */
    @PostMapping("queryStatsByType")
    public ApiResponse queryStatsByType(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            // Parse date and classify
            Date date = parseDate(requestMap.get("date").toString());
            String classify = requestMap.get("classify").toString();

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            logger.info("Created ShopCommonParam: {}", shop);

            List<ChatClassify> classifyList = chatClassifyBusiness.queryClassifyStatsByType(
                    shop, date, classify);

            Map<String, Object> data = Maps.newHashMap();
            data.put("classifyStats", classifyList);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
        } catch (Exception e) {
            logger.error("Error querying chat classification statistics by shop, date and classify", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }

    /**
     * Legacy endpoint for backward compatibility
     * @deprecated Use queryStatsByDate instead
     */
    @PostMapping("queryByShopAndDate")
    public ApiResponse queryByShopAndDate(@RequestBody Map<String, Object> requestMap) {
        return queryStatsByDate(requestMap);
    }

    /**
     * Legacy endpoint for backward compatibility
     * @deprecated Use queryStatsByType instead
     */
    @PostMapping("queryByShopAndClassify")
    public ApiResponse queryByShopAndClassify(@RequestBody Map<String, Object> requestMap) {
        return queryStatsByType(requestMap);
    }

    /**
     * Query low-rated evaluations and their corresponding chat classifications
     *
     * @param requestMap Map containing shopId, schemaId, dbName, startDate, endDate
     * @return API response with a list of evaluation details and chat classification data
     */
    @PostMapping("queryLowRatedEvaluationsWithClassify")
    public ApiResponse queryLowRatedEvaluationsWithClassify(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            // Parse dates
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            logger.info("Created ShopCommonParam: {}", shop);

            // Call the business method
            List<Map<String, Object>> results = chatClassifyBusiness.queryLowRatedEvaluationsWithClassify(
                    shop, startDate, endDate);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", results));
        } catch (Exception e) {
            logger.error("Error querying low-rated evaluations with classify", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS, e.getMessage());
        }
    }

    @PostMapping("queryLowRatedEvaluationsWithClassifyV2")
    public ApiResponse queryLowRatedEvaluationsWithClassifyV2(@RequestBody Map<String, Object> requestMap) {
        try {

            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");

            Map<LocalDate, Map<Integer, Map<String,Integer>>> results = chatClassifyBusiness.queryLowRatedEvaluationsWithClassifyV2(shop, startDate, endDate);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", results));
        } catch (Exception e) {
            logger.error("Error querying low-rated evaluations with classifyV2", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS, e.getMessage());
        }
    }

    @PostMapping("queryLowRatedEvaluationsAttributionOverview")
    public ApiResponse queryLowRatedEvaluationsAttributionOverview(@RequestBody Map<String, Object> requestMap) {
        try {

            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");

            Map<LocalDate, Map<Integer, Map<String,Integer>>> results = chatClassifyBusiness.queryLowRatedEvaluationsWithClassifyV2(shop, startDate, endDate);
            Map<Integer, Map<String, Integer>> resultMap = new HashMap<>();

            for (Map<Integer, Map<String, Integer>> dayMap : results.values()) {
                for (Map.Entry<Integer, Map<String, Integer>> entry : dayMap.entrySet()) {
                    Integer score = entry.getKey();
                    Map<String, Integer> attributions = entry.getValue();
                    Map<String, Integer> aggregated = resultMap.computeIfAbsent(score, k -> new HashMap<>());
                    // 累加归因 key 的值
                    for (Map.Entry<String, Integer> attrEntry : attributions.entrySet()) {
                        String attribution = attrEntry.getKey();
                        Integer count = attrEntry.getValue();
                        aggregated.merge(attribution, count, Integer::sum);
                    }
                }
            }
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,Collections.singletonMap("list", resultMap));
        } catch (Exception e) {
            logger.error("Error querying low-rated evaluations with classifyV2", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS, e.getMessage());
        }
    }

    /**
     * 获取客服洞察首页的数据
     * @param requestMap
     * @return
     */
    @PostMapping("queryInsightOverView")
    public ApiResponse queryInsightOverView(@RequestBody Map<String, Object> requestMap) {
        try {

            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();

            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            //查询近7天的数据
            Integer intervalDay = 7;
            Date endDate = new Date();
            Date startDate = cn.hutool.core.date.DateUtil.offsetDay(endDate, -intervalDay);
            Map<String ,Object> res = chatClassifyBusiness.queryInsightOverView(shop, startDate, endDate);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,res);
        } catch (Exception e) {
            logger.error("Error querying low-rated evaluations with classifyV2", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_PARAMS, e.getMessage());
        }
    }


    /**
     * Query chat classifications statistics by shop ID, date range, and classify type
     * Returns statistical data grouped by classify_extra, including:
     * - classify: the classification type
     * - classifyExtra: the sub-classification type
     * - sidCount: count of unique SIDs for each classify_extra
     * - evalCount: count of evaluations for these SIDs in the next 30 days
     * - dissatisfiedCount: count of evaluations with evalCode < 100
     * - dissatisfiedRate: percentage of dissatisfied evaluations (dissatisfiedCount/evalCount)
     *
     * @param requestMap Map containing shopId, schemaId, dbName, startDate, endDate, classify
     * @return API response with statistical data for each classify_extra
     */
    @PostMapping("queryByShopAndDateRangeAndClassify")
    public ApiResponse queryByShopAndDateRangeAndClassify(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();
            String classify = requestMap.get("classify").toString();

            // Parse dates
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            logger.info("Created ShopCommonParam: {}", shop);

            // Call the business method
            List<Map<String, Object>> results = chatClassifyBusiness.queryClassifyByShopAndDateRangeAndClassify(
                    shop, startDate, endDate, classify);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", results));
        } catch (Exception e) {
            logger.error("Error querying chat classifications by shop, date range, and classify", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }

    /**
     * Helper method to parse date strings
     *
     * @param dateStr date string in yyyy-MM-dd format
     * @return parsed Date object
     * @throws Exception if parsing fails
     */
    private Date parseDate(String dateStr) throws Exception {
        try {
            return new java.text.SimpleDateFormat("yyyy-MM-dd").parse(dateStr);
        } catch (Exception e) {
            throw new Exception("Invalid date format: " + dateStr + ". Expected format: yyyy-MM-dd");
        }
    }

    /**
     * Query chat classifications by shop ID, classify, classifyExtra and date range
     * Returns a list of chat classifications matching the specified criteria
     *
     * @param requestMap Map containing shopId, schemaId, dbName, startDate, endDate, classify, classifyExtra
     * @return API response with a list of chat classifications matching the criteria
     */
    @PostMapping("queryByShopAndClassifyAndClassifyExtraAndDateRange")
    public ApiResponse queryByShopAndClassifyAndClassifyExtraAndDateRange(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();
            String classify = requestMap.get("classify").toString();

            // classifyExtra is optional
            String classifyExtra = null;
            if (requestMap.containsKey("classifyExtra") && requestMap.get("classifyExtra") != null) {
                classifyExtra = requestMap.get("classifyExtra").toString();
            }

            // Parse dates
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            logger.info("Created ShopCommonParam: {}", shop);

            // Call the business method
            List<ChatClassify> results = chatClassifyBusiness.queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange(
                    shop, startDate, endDate, classify, classifyExtra);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", results));
        } catch (Exception e) {
            logger.error("Error querying chat classifications by shop, classify, classifyExtra, and date range", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT, e.getMessage());
        }
    }

    @PostMapping("queryByShopAndClassifyAndClassifyExtraAndDateRangeAndScore")
    public ApiResponse queryByShopAndClassifyAndClassifyExtraAndDateRangeAndScore(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();
            String classify = requestMap.get("classify").toString();

            // classifyExtra is optional
            String classifyExtra = null;
            if (requestMap.containsKey("classifyExtra") && requestMap.get("classifyExtra") != null) {
                classifyExtra = requestMap.get("classifyExtra").toString();
            }
            Integer evalCode = Integer.parseInt(requestMap.get("evalCode").toString());

            // Parse dates
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            DateTime dateTime = DateUtil.beginOfDay(startDate);
            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            logger.info("Created ShopCommonParam: {}", shop);

            // Call the business method
            List<ChatClassify> results = chatClassifyBusiness.queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndScore(
                    shop, startDate, endDate, classify, classifyExtra,evalCode);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", results));
        } catch (Exception e) {
            logger.error("Error querying chat classifications by shop, classify, classifyExtra, and date range", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT, e.getMessage());
        }
    }

    /**
     * Query chatlogs by SID and date range
     * First searches for chat sessions matching the SID and date range,
     * then retrieves all chatlogs within the session time range
     *
     * @param requestMap Map containing shopId, schemaId, dbName, sid, startDate, endDate
     * @return API response with a list of chatlogs for the specified session
     */
    @PostMapping("queryChatlogsBySidAndDateRange")
    public ApiResponse queryChatlogsBySidAndDateRange(@RequestBody Map<String, Object> requestMap) {
        try {
            logger.info("Received request: {}", requestMap);

            // Extract fields from the request map
            Long shopId = Long.valueOf(requestMap.get("shopId").toString());
            String schemaId = requestMap.get("schemaId").toString();
            String dbName = requestMap.get("dbName").toString();
            String sid = requestMap.get("sid").toString();

            // Parse dates
            Date startDate = parseDate(requestMap.get("startDate").toString());
            Date endDate = parseDate(requestMap.get("endDate").toString());

            // Create ShopCommonParam
            ShopCommonParam shop = new ShopCommonParam(shopId, schemaId, dbName);
            //ShopCommonParam shop = new ShopCommonParam(95513l, "insight_01", "db_01");
            logger.info("Created ShopCommonParam: {}", shop);

            // Call the business method
            List<CsChatlog> chatlogs = chatClassifyBusiness.queryChatlogsBySidAndDateRange(
                    shop, sid, startDate, endDate);

            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, Collections.singletonMap("list", chatlogs));
        } catch (Exception e) {
            logger.error("Error querying chatlogs by SID and date range", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT);
        }
    }
}
