package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.GoodsConsultBusiness;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GoodsConsultParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.AnalysisSummaryVO;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2019年2月25日 下午5:40:10
 * @since
 */

@RestController
@RequestMapping("/cs/consult/goods/")
public class CsGoodsConsultSummaryController extends BaseController {

	private static final Logger logger = LoggerFactory.getLogger(CsGoodsConsultSummaryController.class);

	@Autowired
	private GoodsConsultBusiness consultBusiness;

	/**
	 * 商品咨询汇总
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectGoodsConsultSummary")
	public ApiResponse selectGoodsConsultSummary(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
			List<GoodsConsultSummaryDTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(shop, param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
			retMap.put("goodsConsultSummaryList", consultSummaryList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
		} catch (Exception e) {
			logger.error("select goods consult summary error",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_18);
		}
	}

	/**
	 * 商品咨询汇总  添加了categoryId筛选
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectGoodsConsultSummaryV3")
	public ApiResponse selectGoodsConsultSummaryV3(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
			List<GoodsConsultSummaryDTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(shop, param.getSkuLst(), param.getCategoryId(),param.getStartDate(), param.getEndDate(), param.getCsNickLst());
			retMap.put("goodsConsultSummaryList", consultSummaryList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
		} catch (Exception e) {
			logger.error("select goods consult summary error",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_18);
		}
	}


	/**
	 * 商品咨询汇总 v2
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectGoodsConsultSummaryV2")
	public ApiResponse selectGoodsConsultSummaryV2(@RequestParam("shop") String shopStr, @RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		try {
			ShopCommonParam shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			GoodsConsultParam param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
			List<GoodsConsultSummaryV2DTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(shop, param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
			retMap.put("list", consultSummaryList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
		} catch (Exception e) {
			logger.error("selectGoodsConsultSummaryV2 error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_18);
		}
	}

	/**
	 * 新商品咨询汇总 v2   加了category筛选
	 *
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectGoodsConsultSummaryV4")
	public ApiResponse selectGoodsConsultSummaryV4(@RequestParam("shop") String shopStr, @RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		try {
			ShopCommonParam shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			GoodsConsultParam param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
			List<GoodsConsultSummaryV2DTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(shop, param.getSkuLst(), param.getCategoryId(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());

			retMap.put("list", consultSummaryList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
		} catch (Exception e) {
			logger.error("selectGoodsConsultSummaryV2 error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_18);
		}
	}

	/**
	 * 商品咨询汇总 spu维度
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectGoodsConsultSummaryOfSpu")
	public ApiResponse selectGoodsConsultSummaryOfSpu(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
            List<GoodsConsultSummaryDTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickOfSpu(shop, param.getGroupId(), param.getCsLst(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, handlerViews(consultSummaryList));
        } catch (Exception e) {
			logger.error("select goods consult summary of Spu error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_18);
		}
	}

	/**
	 * 商品咨询汇总 spu维度
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectGoodsConsultSummaryOfSpuV2")
	public ApiResponse selectGoodsConsultSummaryOfSpuV2(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
			List<GoodsConsultSummaryV2DTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickOfSpuV2(shop, param.getGroupId(), param.getCsLst(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, handlerViewsV2(consultSummaryList));
		} catch (Exception e) {
			logger.error("select goods consult summary of Spu error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_18);
		}
	}

    private Map<String, Object> handlerViews(List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        HashMap<String, Object> data = Maps.newHashMap();
        GoodsConsultSummaryAvgDTO avg = new GoodsConsultSummaryAvgDTO();
        GoodsConsultSummaryDTO count = new GoodsConsultSummaryDTO();
        calAvg(avg, consultSummaryDTOs);
        calCount(count, consultSummaryDTOs);
        data.put("avg", avg);
        data.put("count", count);
        data.put("goodsConsultList", consultSummaryDTOs);
        return data;
    }

	private Map<String, Object> handlerViewsV2(List<GoodsConsultSummaryV2DTO> consultSummaryDTOs) {
		HashMap<String, Object> data = Maps.newHashMap();
		GoodsConsultSummaryAvgV2DTO avg = new GoodsConsultSummaryAvgV2DTO();
		GoodsConsultSummaryV2DTO count = new GoodsConsultSummaryV2DTO();
		calAvgV2(avg, consultSummaryDTOs);
		calCountV2(count, consultSummaryDTOs);
		data.put("avg", avg);
		data.put("count", count);
		data.put("goodsConsultList", consultSummaryDTOs);
		return data;
	}

    //    均值
    private void calAvg(GoodsConsultSummaryAvgDTO avg, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        double num = consultSummaryDTOs.size() * 1.0;
        //咨询人数
        int consultNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
        avg.setConsultNum(consultNum / num);//咨询人数
        avg.setPurchasesBuyerNum(buyerNum / num);//购买人数
        avg.setPurchasesGoodsNum(consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum() / num);//购买件数
        avg.setBuyPercent(consultNum > 0 ? ((buyerNum * 1.0 / consultNum) * 100.0) : 0.0);//购买占比
        avg.setPurchasesAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum() / num);//购买金额
    }

	//    均值
	private void calAvgV2(GoodsConsultSummaryAvgV2DTO avg, List<GoodsConsultSummaryV2DTO> consultSummaryDTOs) {
		if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
		double num = consultSummaryDTOs.size() * 1.0;
		//咨询人数
		int receiveNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getReceiveNum).sum();
		int enquiryNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getEnquiryNum).sum();
		//购买人数
		int payNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayNum).sum();
		avg.setReceiveNum(receiveNum / num);//咨询人数
		avg.setEnquiryNum(enquiryNum / num);//咨询人数
		avg.setPayNum(payNum / num);//购买人数
		avg.setPayGoodsNum(consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayGoodsNum).sum() / num);//购买件数
		avg.setBuyPercent(enquiryNum > 0 ? ((payNum * 1.0 / enquiryNum) * 100.0) : 0.0);//购买占比
		avg.setPayAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryV2DTO::getPayAmount).sum() / num);//购买金额
	}

    //    汇总 不需要展示率
    private void calCount(GoodsConsultSummaryDTO count, List<GoodsConsultSummaryDTO> consultSummaryDTOs) {
        if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
        //咨询人数
        int consultNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getConsultNum).sum();
        //购买人数
        int buyerNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesBuyerNum).sum();
        count.setConsultNum(consultNum);//咨询人数
        count.setPurchasesBuyerNum(buyerNum);//购买人数
        count.setPurchasesGoodsNum(consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryDTO::getPurchasesGoodsNum).sum());//购买件数
        count.setPurchasesAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryDTO::getPurchasesAmount).sum());//购买金额
    }

	//    汇总 不需要展示率
	private void calCountV2(GoodsConsultSummaryV2DTO count, List<GoodsConsultSummaryV2DTO> consultSummaryDTOs) {
		if (CollectionUtils.isEmpty(consultSummaryDTOs)) return;
		//咨询人数
		int receiveNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getReceiveNum).sum();
		int enquiryNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getEnquiryNum).sum();
		//购买人数
		int payNum = consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayNum).sum();
		count.setReceiveNum(receiveNum);//咨询人数
		count.setEnquiryNum(enquiryNum);//咨询人数
		count.setPayNum(payNum);//购买人数
		count.setPayGoodsNum(consultSummaryDTOs.stream().mapToInt(GoodsConsultSummaryV2DTO::getPayGoodsNum).sum());//购买件数
		count.setPayAmount(consultSummaryDTOs.stream().mapToDouble(GoodsConsultSummaryV2DTO::getPayAmount).sum());//购买金额
	}
	/**
	 * 客服咨询分析
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectCsConsultAnalysis")
	public ApiResponse selectCsConsultAnalysis(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error",e1.getMessage(),e1);
		}
		try {
			List<GoodsConsultSummaryDTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryByShopIdByDateByskuId(shop, param.getSkuId(),param.getStartDate(),param.getEndDate(),param.getCsNickLst());
			retMap.put("goodsConsultSummaryList", consultSummaryList);
		} catch (Exception e) {
			logger.error("select cs analysis error",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
	}


	/**
	 * 客服咨询分析
	 *
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectCsConsultAnalysisV3")
	public ApiResponse selectCsConsultAnalysisV3(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error", e1.getMessage(), e1);
		}
		try {
			List<GoodsConsultSummaryDTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryByShopIdByDateByskuIdV3(shop, param.getSkuId(), param.getCategoryId(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
			retMap.put("goodsConsultSummaryList", consultSummaryList);
		} catch (Exception e) {
			logger.error("select cs analysis error", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
	}



	/**
	 * 客服咨询分析
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectCsConsultAnalysisV2")
	public ApiResponse selectCsConsultAnalysisV2(@RequestParam("shop") String shopStr, @RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error",e1.getMessage(),e1);
		}
		try {
			List<GoodsConsultSummaryV2DTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryByShopIdByDateByskuIdV2(shop, param.getSkuId(),param.getStartDate(),param.getEndDate(),param.getCsNickLst());
			retMap.put("goodsConsultSummaryList", consultSummaryList);
		} catch (Exception e) {
			logger.error("select cs analysis error",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
	}

	/**
	 * 客服咨询分析OfSpu
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectCsConsultAnalysisOfSpu")
	public ApiResponse selectCsConsultAnalysisOfSpu(
			@RequestParam("shop") String shopStr,
			@RequestParam("paramStr") String paramStr) {
        Map<String, Object> retMap = new HashMap<>();
        ShopCommonParam shop = null;
        GoodsConsultParam param = null;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
        } catch (Exception e1) {
            logger.error("json error OfSpu：{}", e1.getMessage(), e1);
        }
        try {
            List<GoodsConsultSummaryDTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryByShopIdByDateByskuIdOfSpu(shop, param.getSkuId(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
            retMap = handlerVOData(consultSummaryList, param);//处理汇总的数据
        } catch (Exception e) {
            logger.error("select cs analysis OfSpu error{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
    }

	/**
	 * 客服咨询分析OfSpu
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping("selectCsConsultAnalysisOfSpuV2")
	public ApiResponse selectCsConsultAnalysisOfSpuV2(@RequestParam("shop") String shopStr, @RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = new HashMap<>();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error OfSpu：{}", e1.getMessage(), e1);
		}
		try {
			List<GoodsConsultSummaryV2DTO> consultSummaryList = consultBusiness.selectGoodsConsultSummaryByShopIdByDateByskuIdOfSpuV2(shop, param.getSkuId(), param.getStartDate(), param.getEndDate(), param.getCsNickLst());
			retMap = handlerVODataV2(consultSummaryList, param);//处理汇总的数据
		} catch (Exception e) {
			logger.error("select cs analysis OfSpu error{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
	}

    private Map<String, Object> handlerVOData(List<GoodsConsultSummaryDTO> consultSummaryList, GoodsConsultParam param) {
        Map<String, Object> retMap = new HashMap<>();
        AnalysisSummaryVO<GoodsConsultSummaryDTO> analysisSummaryVO = new AnalysisSummaryVO<>();
        if (CollectionUtils.isEmpty(consultSummaryList)) {
            analysisSummaryVO.setSumData(new GoodsConsultSummaryDTO());//汇总数据
            analysisSummaryVO.setDataList(consultSummaryList);
            retMap.put("analysisSummaryVO", analysisSummaryVO);
        }

        List<CsDTO> csLst = param.getCsLst();
        if (CollectionUtils.isNotEmpty(csLst)) {
            //汇总数据
            GoodsConsultSummaryDTO sumData = new GoodsConsultSummaryDTO();
            Integer consultSum = 0;
            Integer buyerSum = 0;
            Double buyerPerSum = 0.0;
            Integer goodsSum = 0;
            Double amountSum = 0.0;
            //客服昵称
            Map<String, String> simpleNameMap = csLst.stream()
                    .collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
            for (GoodsConsultSummaryDTO consult : consultSummaryList) {
                consult.setCsSimpleNick(simpleNameMap.get(consult.getCsNick()));
                consultSum += consult.getConsultNum();
                buyerSum += consult.getPurchasesBuyerNum();
                goodsSum += consult.getPurchasesGoodsNum();
                amountSum += consult.getPurchasesAmount();
                buyerPerSum += consult.getBuyPercent();
            }
            sumData.setConsultNum(consultSum);
            sumData.setPurchasesAmount(amountSum);
            sumData.setPurchasesBuyerNum(buyerSum);
            sumData.setPurchasesGoodsNum(goodsSum);
            sumData.setBuyPercent(buyerPerSum);
            analysisSummaryVO.setSumData(sumData);//汇总数据
        }
//        retMap.put("goodsConsultSummaryList", consultSummaryList);
        analysisSummaryVO.setDataList(consultSummaryList);
        retMap.put("analysisSummaryVO", analysisSummaryVO);
        return retMap;
    }

	private Map<String, Object> handlerVODataV2(List<GoodsConsultSummaryV2DTO> consultSummaryList, GoodsConsultParam param) {
		Map<String, Object> retMap = new HashMap<>();
		AnalysisSummaryVO<GoodsConsultSummaryV2DTO> analysisSummaryVO = new AnalysisSummaryVO<>();
		if (CollectionUtils.isEmpty(consultSummaryList)) {
			analysisSummaryVO.setSumData(new GoodsConsultSummaryV2DTO());//汇总数据
			analysisSummaryVO.setDataList(consultSummaryList);
			retMap.put("analysisSummaryVO", analysisSummaryVO);
		}

		List<CsDTO> csLst = param.getCsLst();
		if (CollectionUtils.isNotEmpty(csLst)) {
			//汇总数据
			GoodsConsultSummaryV2DTO sumData = new GoodsConsultSummaryV2DTO();
			Integer receiveSum = 0;
			Integer enquirySum = 0;
			Integer paySum = 0;
			Double buyerPerSum = 0.0;
			Integer goodsSum = 0;
			Double amountSum = 0.0;
			//客服昵称
			Map<String, String> simpleNameMap = csLst.stream().collect(Collectors.toMap(CsDTO::getNick, CsDTO::getCsSimpleNick));
			for (GoodsConsultSummaryV2DTO consult : consultSummaryList) {
				consult.setCsSimpleNick(simpleNameMap.get(consult.getCsNick()));
				receiveSum += consult.getReceiveNum();
				enquirySum += consult.getEnquiryNum();
				paySum += consult.getPayNum();
				goodsSum += consult.getPayGoodsNum();
				amountSum += consult.getPayAmount();
				buyerPerSum += consult.getBuyPercent();
			}
			sumData.setReceiveNum(receiveSum);
			sumData.setEnquiryNum(enquirySum);
			sumData.setPayAmount(amountSum);
			sumData.setPayNum(paySum);
			sumData.setPayGoodsNum(goodsSum);
			sumData.setBuyPercent(buyerPerSum);
			analysisSummaryVO.setSumData(sumData);//汇总数据
		}
//        retMap.put("goodsConsultSummaryList", consultSummaryList);
		analysisSummaryVO.setDataList(consultSummaryList);
		retMap.put("analysisSummaryVO", analysisSummaryVO);
		return retMap;
	}


    /**
	 * 商品咨询明细
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping(value = "selectGoodsConsultDetail")
	public ApiResponse selectGoodsConsultDetail(
			@RequestParam("shop")String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
			List<CustConsultGoodsDTO> custConsultGoodsList = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(shop, param.getResult(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
			retMap.put("custConsultGoodsList", custConsultGoodsList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,retMap);
		} catch (Exception e) {
			logger.error("select cs goods consult detail error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
	}

	/**
	 * 商品咨询明细
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping(value = "selectGoodsConsultDetailV3")
	public ApiResponse selectGoodsConsultDetailV3(
			@RequestParam("shop")String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
			List<CustConsultGoodsDTO> custConsultGoodsList = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV3(shop, param.getResult(), param.getSkuLst(),param.getCategoryId(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
			retMap.put("custConsultGoodsList", custConsultGoodsList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,retMap);
		} catch (Exception e) {
			logger.error("select cs goods consult detail error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
	}


	/**
	 * 商品咨询明细
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping(value = "selectGoodsConsultDetailV2")
	public ApiResponse selectGoodsConsultDetailV2(@RequestParam("shop")String shopStr, @RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}",e1.getMessage(),e1);
		}
		try {
			List<CustConsultGoodsV2DTO> custConsultGoodsList = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(shop, param.getResult(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
			retMap.put("custConsultGoodsList", custConsultGoodsList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,retMap);
		} catch (Exception e) {
			logger.error("select cs goods consult detail error{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
	}


	/**
	 * 商品咨询明细  添加了categoryId筛选
	 *
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping(value = "selectGoodsConsultDetailV4")
	public ApiResponse selectGoodsConsultDetailV4(@RequestParam("shop") String shopStr, @RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json error{}", e1.getMessage(), e1);
		}
		try {
			List<CustConsultGoodsV2DTO> custConsultGoodsList = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4(shop, param.getResult(), param.getSkuLst(), param.getCategoryId(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());

			retMap.put("custConsultGoodsList", custConsultGoodsList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
		} catch (Exception e) {
			logger.error("select cs goods consult detail error{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
	}



	 /**
	 * 商品咨询明细   OfSpu
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping(value = "selectGoodsConsultDetailOfSpu")
	public ApiResponse selectGoodsConsultDetailOfSpu(
			@RequestParam("shop")String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json OfSpu error{}",e1.getMessage(),e1);
		}
        try {
//            List<CustConsultGoodsDTO> custConsultGoodsList1 = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(shop, param.getResult(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
            List<CustConsultGoodsDTO> custConsultGoodsList = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuOfSpu(shop,param.getCsLst(), param.getResult(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
            retMap.put("custConsultGoodsList", custConsultGoodsList);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
        } catch (Exception e) {
            logger.error("select cs goods consult detail OfSpu error{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }
	}

	/**
	 * 商品咨询明细   OfSpu
	 * @param shopStr
	 * @param paramStr
	 * @return
	 */
	@RequestMapping(value = "selectGoodsConsultDetailOfSpuV2")
	public ApiResponse selectGoodsConsultDetailOfSpuV2(
			@RequestParam("shop")String shopStr,
			@RequestParam("paramStr") String paramStr) {
		Map<String, Object> retMap = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsConsultParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsConsultParam.class);
		} catch (Exception e1) {
			logger.error("json OfSpu error{}",e1.getMessage(),e1);
		}
		try {
//            List<CustConsultGoodsDTO> custConsultGoodsList1 = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(shop, param.getResult(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
			List<CustConsultGoodsV2DTO> custConsultGoodsList = consultBusiness.selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuOfSpuV2(shop,param.getCsLst(), param.getResult(), param.getSkuLst(), param.getStartDate(), param.getEndDate(), param.getCsNickLst(), param.getCustomer());
			retMap.put("custConsultGoodsList", custConsultGoodsList);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, retMap);
		} catch (Exception e) {
			logger.error("select cs goods consult detail OfSpu error{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
		}
	}



}
