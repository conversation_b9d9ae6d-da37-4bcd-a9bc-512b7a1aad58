package com.pes.jd.controller;

import com.pes.jd.business.main.ShopRemindBlackListBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RequestMapping("/blacklist")
@RestController
public class ShopRemindBlackListController extends BaseController{
    private final Logger LOGGER = LoggerFactory.getLogger(ShopRemindBlackListController.class);

    @Autowired
    private ShopRemindBlackListBusiness shopRemindBlackListBusiness;

    @RequestMapping("/batchInsert")
    public Object batchInsert(@RequestParam("shopId") Long shopId,
                              @RequestParam("buyerNicks") String buyerNicks,
                              @RequestParam("type") String type,
                              @RequestParam("userId") String userId) {
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopRemindBlackListBusiness.batchInsert(shopId, buyerNicks, type, userId)));
        } catch (Exception e) {
            LOGGER.error("batchInsert shopId :{}, error :{}",shopId, e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_07, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopRemindBlackList")
    public Object selectShopRemindBlackList(@RequestParam("shopId") Long shopId,
                                            @RequestParam("buyerNick") String buyerNick,
                                            @RequestParam("startDate") String startDateStr,
                                            @RequestParam("endDate") String endDateStr){
        Date startDate;
        Date endDate;
        try {
            startDate = DateUtils.parseYMdHms(startDateStr);
            endDate = DateUtils.parseYMdHms(endDateStr);
        } catch (Exception e) {
            LOGGER.info("master parse csNickLst json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }

        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopRemindBlackListBusiness.selectShopRemindBlackList(shopId, buyerNick, startDate, endDate)));
        } catch (Exception e) {
            LOGGER.error("selectShopRemindBlackList shopId :{}, error:{}", shopId, e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_08, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("/selectManualMerchandisingBlacklistList")
    public Object selectManualMerchandisingBlacklistList(@RequestParam("shopId") String shopId){
        try {
            List<String> strings = shopRemindBlackListBusiness.selectManualMerchandisingBlacklistList(Long.valueOf(shopId));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopRemindBlackListBusiness.selectManualMerchandisingBlacklistList(Long.valueOf(shopId))));
        } catch (Exception e) {
            LOGGER.error("selectManualMerchandisingBlacklistList shopId :{}, error:{}", shopId, e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_08, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("/deleteShopRemindBlackListById")
    public Object deleteShopRemindBlackListById(@RequestParam("id") Long id){
        try {
            shopRemindBlackListBusiness.deleteShopRemindBlackListById(id);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            LOGGER.error("deleteShopRemindBlackListById id :{}, error:{}",id, e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SW_01_06, RestApiResponse2.of(false));
        }
    }
}
