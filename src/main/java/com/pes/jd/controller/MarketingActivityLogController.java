package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.main.MarketingActivityLogBusiness;
import com.pes.jd.model.DTO.MarketingActivityLogDTO;
import com.pes.jd.model.DTO.MarketingActivityLogSummaryDTO;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/marketingActivityLog")
public class MarketingActivityLogController extends BaseController {

    private final Logger logger = LoggerFactory.getLogger(MarketingActivityLogController.class);
    @Autowired
    private MarketingActivityLogBusiness marketingActivityLogBusiness;

    @PostMapping(value = "/insert", consumes = "multipart/form-data")
    public Object insert(@ModelAttribute MarketingActivityLogDTO dto) {
        try {
            logger.info("===>insert: {}", JSONObject.toJSONString(dto));
            marketingActivityLogBusiness.insert(dto);
            return RestResponseTypeRef.ofSuccess();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

    @PostMapping(value = "/summary")
    public Object summary(@RequestParam(name = "startDate")String startDate,
                          @RequestParam(name = "endDate")String endDate
    ) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            Map<String, Object> summary = marketingActivityLogBusiness.summary(start, end);
            return RestResponseTypeRef.ofSuccess(summary);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }
}
