package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.pes.jd.business.main.OnlineNoticeBusiness;
import com.pes.jd.model.DTO.OnlineNoticeDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 15:39 2019/10/14
 * @Description:
 */
@RestController
@RequestMapping("/onlineNotice/")
public class OnlineNoticeController extends BaseController {

    @Autowired
    private OnlineNoticeBusiness onlineNoticeBusiness;

    @RequestMapping("insert")
    public Object insert(String onlineVersion, String onlineContent) {
        try {
            onlineNoticeBusiness.insert(onlineVersion, onlineContent);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_01, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("delete")
    public Object delete(Long id) {
        try {
            onlineNoticeBusiness.deleteByPrimaryKey(id);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_1002, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("update")
    public Object update(Long id, String onlineVersion, String noticeContent) {
        try {
            onlineNoticeBusiness.update(id, onlineVersion, noticeContent);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_1002, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("updateEnable")
    public Object updateEnable(Long id, Boolean enableSwitch) {
        try {
            onlineNoticeBusiness.updateEnable(id, enableSwitch);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());
        } catch (Exception e) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_1002, RestApiResponse2.of(false));
        }
    }

    @RequestMapping("selectEnableNotice")
    public Object selectEnableNotice() {
        try {
            return RestResponseTypeRef.ofSuccess(onlineNoticeBusiness.selectEnableNotice());
        } catch (Exception e) {
            return RestResponseTypeRef.ofFail();
        }
    }

    @RequestMapping("selectNoticeByVersionAndDate")
    public Object selectNoticeByVersionAndDate(@RequestParam(name = "startDate", required = true) String startDate,
                                               @RequestParam(name = "endDate", required = true) String endDate,
                                               @RequestParam(name = "version", required = true) String version) {
        try {
            Date sDate;
            Date eDate;
            try {
                sDate = DateUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss");
                eDate = DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_SF_03_20, RestApiResponse2.of(false));
            }
            version = StrUtil.isNotBlank(version) ? version.trim() : version;

            List<OnlineNoticeDTO> onlineNoticeDTOS = onlineNoticeBusiness.selectNoticeByVersionAndDate(sDate, eDate, version);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(onlineNoticeDTOS));
        } catch (Exception e) {
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_01, RestApiResponse2.of(false));
        }
    }
}
