package com.pes.jd.controller;

import com.pes.jd.business.sub.LoginlogAnalysisBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cs/duty/")
public class CsDutyLogController extends BaseController{
	private Logger logger = LoggerFactory.getLogger(CsDutyLogController.class);
	@Autowired
	private LoginlogAnalysisBusiness loginlogAnalysisBusiness;
	
	@RequestMapping("/selectCsLoginLogForShopUserAnalysis")
	public Object selectShopSubScribeDetailForUseAnalysis(@RequestParam("schemaId")String schemaId,@RequestParam("param")String paramStr)	{
		UserAnalysisParam param=null;
		try {
			param=JacksonUtils.json2pojo(paramStr, UserAnalysisParam.class);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(loginlogAnalysisBusiness.selectCsLoginLogForShopUserAnalysis(schemaId, param)));
		} catch (Exception e) {
			logger.error("master selectCsLoginLogForShopUserAnalysis error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SF_04_05, RestApiResponse2.of(false));
		}
	}
}
