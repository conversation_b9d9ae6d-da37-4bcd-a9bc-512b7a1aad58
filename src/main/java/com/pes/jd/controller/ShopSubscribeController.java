package com.pes.jd.controller;

import com.pes.jd.business.main.ShopSubscribeBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.ShopAuthExpiredVo;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RequestMapping("/shop/Subscribe")
@RestController
public class ShopSubscribeController extends BaseController {

	private static final Logger logger = LoggerFactory.getLogger(ShopSubscribeController.class);
	@Resource
	private ShopSubscribeBusiness shopSubscribeBusiness;
	
	 @RequestMapping("/selectShopSubScribeForUseAnalysis")
		public Object selectShopSubScribeForUseAnalysis(@RequestParam("param")String paramStr,
														@RequestParam("type")String type)	{
			UserAnalysisParam param;
			try {
				param=JacksonUtils.json2pojo(paramStr, UserAnalysisParam.class);
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
				return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
			}
			try {
				return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSubscribeBusiness.selectShopSubScribeForUseAnalysis(param,type)));
			} catch (Exception e) {
				logger.error("master selectShopSubScribeForUseAnalysis error:{}",e.getMessage(),e);
				return apiResponse(ApiCodeEnum.CODE_ERROR_SD_01_01, RestApiResponse2.of(false));
			}
		}
	 
	 @RequestMapping("/selectShopSubScribeDetailForUseAnalysis")
		public Object selectShopSubScribeDetailForUseAnalysis(@RequestParam("param")String paramStr)	{
			UserAnalysisParam param;
			try {
				param=JacksonUtils.json2pojo(paramStr, UserAnalysisParam.class);
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
				return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
			}
			try {
				return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSubscribeBusiness.selectShopSubScribeDetailForUseAnalysis(param)));
			} catch (Exception e) {
				logger.error("master selectShopSubScribeDetailForUseAnalysis error:{}",e.getMessage(),e);
				return apiResponse(ApiCodeEnum.CODE_ERROR_SD_01_01, RestApiResponse2.of(false));
			}
		}

	@RequestMapping("/selectShopAuthExpiredLst")
	public ApiResponse selectShopAuthExpiredLst(@RequestParam("startDate") String startDateStr,
												@RequestParam("endDate") String endDateStr,
												@RequestParam("nick") String nick,
												@RequestParam("shopType") Integer shopType) {
		Date startDate;
		Date endDate;
		try {
			Date startDateTemp = DateUtils.parseYMd(startDateStr);
			startDate = DateUtils.getStartTimeOfDate(startDateTemp);
			Date endDateTemp = DateUtils.parseYMd(endDateStr);
			endDate = DateUtils.getEndTimeOfDate(endDateTemp);
		} catch (Exception e) {
			logger.error(ApiCodeEnum.CODE_ERROR_1001.getMsg(), e);
			e.printStackTrace();
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
		}
		try {
			List<ShopAuthExpiredVo> shopAuthExpiredLst = shopSubscribeBusiness.selectShopAuthExpiredLst(startDate, endDate, nick != null ? nick.trim() : nick, shopType);
			Map<String, Object> result = new LinkedHashMap<>();
			result.put("shopAuthExpiredLst", shopAuthExpiredLst);
			ApiResponse apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			apiResponse.setData(result);
			return apiResponse;
		} catch (Exception e) {
			logger.error("master getUserInterface error:" + e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_01);
		}
	}


	@RequestMapping("/getShopLoginSubscribe")
	public Object getShopLoginSubscribe(
			@RequestParam("shopId")Long shopId,
			@RequestParam("startDate") String startDateStr,
			@RequestParam("endDate") String endDateStr)	{
		Date startDate;
		Date endDate;
		try {
			startDate= DateUtils.parseYMdHms(startDateStr);
			endDate=DateUtils.parseYMdHms(endDateStr);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSubscribeBusiness.getShopLoginSubscribe(shopId,startDate,endDate)));
		} catch (Exception e) {
			logger.error("master getShopLoginSubscribe error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SD_01_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/selectShopSubscribe")
	public Object selectShopSubscribe(
			@RequestParam("shopId")Long shopId)	{

		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopSubscribeBusiness.selectShopSubscribe(shopId)));
		} catch (Exception e) {
			logger.error("master selectShopSubscribe error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_SD_01_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/selectShopSubscribeByShopId")
	public Object selectShopSubscribeByShopId(
			@RequestParam("shopId")Long shopId)	{

		try {
			return RestResponseTypeRef.ofSuccess(shopSubscribeBusiness.selectShopSubscribe(shopId));
		} catch (Exception e) {
			logger.error("master selectShopSubscribe by shopId error:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}
}
