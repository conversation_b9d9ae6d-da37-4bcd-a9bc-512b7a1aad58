package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.pes.jd.business.sub.SentimentAnalysisBusiness;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/sentimentAnalysis/*")
public class SentimentAnalysisController {
	private static final Logger logger = LoggerFactory.getLogger(SentimentAnalysisController.class);

	@Autowired
	private SentimentAnalysisBusiness sentimentAnalysisBusiness;

	private static final String format = "yyyy-MM-dd HH:mm:ss";

	@RequestMapping("auditSentimentAnalysis")
	public RestResponseTypeRef auditSentimentAnalysis(@RequestParam("schemaId") String schemaId,
													  @RequestParam("id")Long id,
													  @RequestParam("sentimentType")Integer sentimentType){
		try{

			return RestResponseTypeRef.ofSuccess(sentimentAnalysisBusiness.auditSentimentAnalysis(schemaId, id,sentimentType));
		} catch (Exception e){
			logger.error("sub selectSentimentAnalysisByShopAndDate error:" + e.getMessage(), e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("batchAuditSentimentAnalysis")
	public RestResponseTypeRef batchAuditSentimentAnalysis(@RequestParam("schemaIds") String schemaIds,
																	@RequestParam("ids")String ids,
																	@RequestParam("sentimentType")Integer sentimentType){
		try{

			List<String> schemaIdLst = Arrays.asList(schemaIds.split(","));
			return RestResponseTypeRef.ofSuccess(sentimentAnalysisBusiness.batchAuditSentimentAnalysis(schemaIdLst, ids, sentimentType));
		} catch (Exception e){
			logger.error("sub selectSentimentAnalysisByShopAndDate error:" + e.getMessage(), e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("selectSentimentAnalysisByShopAndDate")
	public RestResponseTypeRef selectSentimentAnalysisByShopAndDate(@RequestParam("schemaIds") String schemaIds,
													   @RequestParam("nick")String nick,
													   @RequestParam("warningType")Integer warningType,
													   @RequestParam("keyword")String keyword,
													   @RequestParam("status")Integer status,
													   @RequestParam("startDate")String startDate,
													   @RequestParam("endDate")String endDate){
		try{
			Date sDate = DateUtil.parse(startDate, format);
			Date eDate = DateUtil.parse(endDate, format);

			if(StrUtil.isEmpty(schemaIds))
				return RestResponseTypeRef.ofSuccess(Lists.newArrayListWithCapacity(0));

			List<String> schemaIdLst = Arrays.asList(schemaIds.split(","));
			return RestResponseTypeRef.ofSuccess(sentimentAnalysisBusiness.selectSentimentAnalysisByShopAndDate(schemaIdLst, nick, warningType, keyword, status, sDate, eDate));
		} catch (Exception e){
			logger.error("sub selectSentimentAnalysisByShopAndDate error:" + e.getMessage(), e);
			return RestResponseTypeRef.ofFail();
		}
	}
}
