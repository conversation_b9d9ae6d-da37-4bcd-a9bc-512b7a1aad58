package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.pes.jd.business.SentimentAnalysisBusiness;
import com.pes.jd.business.SentimentAnalysisModelBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * @Author: yuanxun
 * @Date: 15:29 2019/11/13
 * @Description:
 */
@RestController
@RequestMapping("/sentimentAnalysis")
public class SentimentAnalysisController {
    private static final Logger logger = LoggerFactory.getLogger(SentimentAnalysisController.class);

    @Autowired
    private SentimentAnalysisBusiness sentimentAnalysisBusiness;

    @Autowired
    private SentimentAnalysisModelBusiness sentimentAnalysisModelBusiness;

    private static final String format = "yyyy-MM-dd";

    @RequestMapping("/audit")
    public Object audit(@RequestParam("id") Long id,
                        @RequestParam("shopId") Long shopId,
                        @RequestParam("content") String content,
                        @RequestParam("sentimentType") Integer sentimentType) {
        ApiResponse apiResponse;
        try {
            Assert.notNull(id, "id not null");
            Assert.notNull(shopId, "shopId not null");
            Assert.notNull(content, "content not null");
            Assert.notNull(sentimentType, "sentimentType not null");

            apiResponse = sentimentAnalysisBusiness.auditSentimentAnalysis(id, shopId, sentimentType, content);
        } catch (Exception e) {
            logger.error("web SentimentAnalysis audit error:" + e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
            apiResponse.setSuccess(Boolean.FALSE);
        }
        return apiResponse;
    }
    @RequestMapping("/batchAudit")
    public Object audit(@RequestParam("param") String param, @RequestParam("sentimentType") Integer sentimentType) {
        ApiResponse apiResponse;
        try {
            Assert.notNull(param, "param not null");
            Assert.notNull(sentimentType, "sentimentType not null");

            apiResponse = sentimentAnalysisBusiness.batchAuditSentimentAnalysis(param, sentimentType);
        } catch (Exception e) {
            logger.error("web SentimentAnalysis audit error:" + e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
            apiResponse.setSuccess(Boolean.FALSE);
        }
        return apiResponse;
    }


    @RequestMapping("/selectSentimentAnalysis")
    public Object selectSentimentAnalysis(@RequestParam("nick")String nick,
                                          @RequestParam("warningType")Integer warningType,
                                          @RequestParam("keyword")String keyword,
                                          @RequestParam("status")Integer status,
                                          @RequestParam("startDate")String startDate,
                                          @RequestParam("endDate")String endDate) {
        ApiResponse apiResponse;
        try {
            Date sDate = DateUtil.parse(startDate, format);
            Date eDate = DateUtil.parse(endDate, format);

            apiResponse = sentimentAnalysisBusiness.selectSentimentAnalysis(nick, warningType, keyword, status, sDate, eDate);
        } catch (Exception e) {
            logger.error("web selectSentimentAnalysis error:" + e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
            apiResponse.setSuccess(Boolean.FALSE);
        }
        return apiResponse;
    }


    @RequestMapping("/trainSentimentAnalysisModel")
    public Object trainSentimentAnalysis() {
        ApiResponse apiResponse;
        try {
            sentimentAnalysisModelBusiness.train();
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
            apiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            logger.error("web trainSentimentAnalysisModel error:" + e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_1003);
            apiResponse.setSuccess(Boolean.FALSE);
        }
        return apiResponse;
    }
}
  
