package com.pes.jd.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 2019-06-02 20:34
 */
@RestController
@RequestMapping(value = "/customer")
public class ShopOrderInvoiceInfoController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ShopOrderInvoiceInfoController.class);

//    @Autowired
//    private CustomerInfBusiness customerInfBusiness;
//
//    Date startDate=new Date();
//
//    @RequestMapping(value = "/selectInvoiceInfo")
//    public ApiResponse getInvoiceInfo(@RequestParam("shop")String  shopStr,
//                                      @RequestParam("orderId")Long orderId) throws ParseException {
//        ShopCommonParam shop = new ShopCommonParam();
//        InvoiceInfoDTO  invoiceInfoDTO=new InvoiceInfoDTO();
//        try{
////            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
//        	  shop = JSONObject.toJavaObject(JSONObject.parseObject(shopStr), ShopCommonParam.class);
//            invoiceInfoDTO=customerInfBusiness.selectInvoiceInfo(shop,orderId,startDate);
//            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, invoiceInfoDTO);
//        }catch (Exception e){
//            logger.error("ShopOrderInvoiceInfoController==========" + e.getMessage());
//            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CJ_01_01, invoiceInfoDTO);
//        }
//
//    }



}
