package com.pes.jd.controller;

import com.pes.jd.business.main.PayManagerService;
import com.pes.jd.framework.FormUrlencoded;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.PayParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.utils.RandomNumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: aiJun
 * @Date: 2019-09-21 0:18
 * @Version 1.0
 * @desc 支付管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/payManager")
public class PayManagerController extends BaseController {
    @Autowired
    private PayManagerService payManagerService;
    @RequestMapping(value = "wxpay/buySmsService", method = RequestMethod.POST)
    public ApiResponse buySmsService(
            @FormUrlencoded() PayParam payParam) {
        try {
            if (log.isDebugEnabled()) {
                log.info("payParam={}", payParam);
            }
            Assert.notNull(payParam, "充值的必要参数不能为空。");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
        try {
            payParam.setOrderId(RandomNumUtil.getOrderNo());
            return payManagerService.buySmsService(payParam);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SMS_01_01);
        }
    }

    @RequestMapping(value = "wxpay/checkOrderStatus", method = RequestMethod.POST)
    public ApiResponse checkOrderStatus(
            @RequestParam("orderId") String orderId) {
        try {
            if (log.isDebugEnabled()) {
                log.info("orderId={}", orderId);
            }
            Assert.notNull(orderId, "需要校验的订单号不能为空。");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
        try {
            return payManagerService.checkOrderStatus(orderId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SMS_01_01);
        }
    }

    @RequestMapping(value = "wxpay/callback", method = RequestMethod.POST)
    public Object wxpayCallback(
            @RequestParam("notityXml") String notityXml) {
        try {
                log.info("notityXml={}", notityXml);
            Assert.notNull(notityXml, "微信回调必要参数不能为空。");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1001);
        }
        try {
            return payManagerService.handlerWxOrderPayResult(notityXml);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_ZF_01_02);
        }
    }

}
