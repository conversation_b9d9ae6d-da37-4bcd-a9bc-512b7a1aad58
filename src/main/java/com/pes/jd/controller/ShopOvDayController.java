package com.pes.jd.controller;

import com.pes.jd.business.sub.ShopOvDayBussiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.master.ShopUrge;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/shop/ov/")
public class ShopOvDayController extends BaseController {
	private Logger logger = LoggerFactory.getLogger(ShopOvDayController.class);
	@Autowired
	private ShopOvDayBussiness shopOvDayBussiness;
	
	@RequestMapping("/slectMultiShopSaleAmount")
	public Object slectMultiShopSaleAmount(
			@RequestParam("causeShopLst")String causeShopLstStr,
			@RequestParam("startDate")String startDateStr,
			@RequestParam("endDate")String endDateStr)	{
		List<CauseShop> causeShopLst=null;
		Date startDate=null;
		Date endDate=null;
		try {
			causeShopLst=JacksonUtils.json2list(causeShopLstStr,CauseShop.class);
			startDate= DateUtils.parseYMdHms(startDateStr);
			endDate=DateUtils.parseYMdHms(endDateStr);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopOvDayBussiness.selectMultiShopSaleAmount(causeShopLst, startDate,endDate)));
		} catch (Exception e) {
			logger.error("sub slectMultiShopSaleAmount error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("selecShopOrderSaleAmountLst")
	public Object selectSgShopUseConvertLst(
			@RequestParam("startDate")String startDateStr,
			@RequestParam("endDate")String endDateStr,
			@RequestParam("urgeShopLst")String urgeShopLstStr){
		Date startDate=null;
		Date endDate=null;
		List<ShopUrge> shopUrgeLst=null;
		try {
			shopUrgeLst=JacksonUtils.json2list(urgeShopLstStr, ShopUrge.class);
			startDate=DateUtils.parseYMdHms(startDateStr);
			endDate=DateUtils.parseYMdHms(endDateStr);
		} catch (Exception e) {
			logger.error("sub selecShopOrderSaleAmountLst json error:{} ",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
		try {
			return RestResponseTypeRef.ofSuccess(shopOvDayBussiness.selectMultiShopOrderSaleAmount(shopUrgeLst,startDate,endDate));
		} catch (Exception e) {
			logger.error("sub selecShopOrderSaleAmountLst error:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}
}
