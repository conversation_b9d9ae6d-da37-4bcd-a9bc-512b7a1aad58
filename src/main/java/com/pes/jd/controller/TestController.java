package com.pes.jd.controller;

import cn.hutool.core.date.DateUtil;
import com.pes.jd.Constants.AppConstants;
import com.pes.jd.business.ChatClassfiyBusiness;
import com.pes.jd.business.CsChatlogBusiness;
import com.pes.jd.config.PersistentRoutingDataSource;
import com.pes.jd.data.api.OrgLevelAndLeaderInfoAPI;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.redis.ListeningJobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/test")
public class TestController {
	private final Logger logger = LoggerFactory.getLogger(TestController.class);

	@Resource
	private Environment env;

	@Autowired
	private ListeningJobHandler listeningJobHandler;

	@Autowired
	private ChatClassfiyBusiness chatClassfiyBusiness;

	@Autowired
	private CsChatlogBusiness csChatlogBusiness;


	@RequestMapping("/t1")
	public String test(String msg) throws Exception{
		logger.info("msg:{}",msg);

		System.out.println(env.getProperty("profile"));
		System.out.println(env.getProperty("app.appKey"));
		System.out.println(env.getProperty("app.appSecret"));
		System.out.println(env.getProperty("app.redirectUrl"));
		System.out.println(env.getProperty("app.tokenOauthUrl"));

		System.out.println(AppConstants.APP_KEY);
		System.out.println(AppConstants.APP_SECRET);
		System.out.println(AppConstants.REDIRECT_URL);
		System.out.println(AppConstants.TOKEN_OAUTH_URL);
		return msg;
	}
	http://insight.yiyitech.com/web-report/login/main?code=afWMxX&state=eyJqb3NfcGFyYW1ldGVycyI6eyJlbmRfZGF0ZSI6MTc4MTY2NTQ2NzAwMCwidmVyc2lvbl9ubyI6MSwiaXRlbV9jb2RlIjoiRldfR09PRFMtMTg5OTAxMC0xIiwiYXBwX2tleSI6IjU4QTg4M0FEOEZERjEzMDlEODJFQzc4OTg2MEY4QTM4IiwidXNlcl9uYW1lIjoi5LiK5rW35piT6JqBIiwiYXJ0aWNsZV9udW0iOjF9fQ==&fwState=eyJqb3NfcGFyYW1ldGVycyI6eyJhcHBfa2V5IjoiNThBODgzQUQ4RkRGMTMwOUQ4MkVDNzg5ODYwRjhBMzgiLCJ1c2VyX25hbWUiOiLkuIrmtbfmmJPomoEiLCJwdXJjaGFzZV9saXN0IjpbeyJlbmRfZGF0ZSI6MTc4MTY2NTQ2NzAwMCwidmVyc2lvbl9ubyI6MSwiaXRlbV9jb2RlIjoiRldfR09PRFMtMTg5OTAxMC0xIiwiYXJ0aWNsZV9udW0iOjF9XX19

	@RequestMapping("/getOrgLevelAndLeaderInfoByUserName")
	public String getOrgLevelAndLeaderInfoByUserName(String userName) {
		logger.info("userName:{}",userName);

		return OrgLevelAndLeaderInfoAPI.getOrgLevelAndLeaderInfoByUserName(userName);
	}


	/**
	 * 测试数据源切换
	 */
	@RequestMapping("/testDataSource")
	public String testDataSource(String shopId,String startTime,String endTime,String schemaId,String db1) throws Exception {
		//listeningJobHandler.testChangeDataSource(Integer.parseInt(shopId));


//		String startTime  ="2025-06-18";
//		String endTime  ="2025-06-20";
		JobDateQuery jobDate = new JobDateQuery();

		jobDate.setStartDate(DateUtil.parse(startTime));
		jobDate.setEndDate(DateUtil.parse(endTime));
        JobShopQuery jobShop = new JobShopQuery();
		JobShopDTO shop = new JobShopDTO();
		//shop.setSchemaId("insight_01");
		shop.setSchemaId(schemaId);

		//shop.setDb("db_02");
		shop.setDb(db1);
		shop.setShopId(Long.valueOf(shopId));
		jobShop.setShop(shop);
		String db = jobShop.getShop().getDb();
		if(db.contains("1")){
			PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
		}
		if(db.contains("2")){
			PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
		}
       // chatClassfiyBusiness.handleClassfiyAnalysis(jobShop, jobDate, true);
		csChatlogBusiness.findSidOfCsServiceEvaluation(jobShop,jobDate);
		return "success";
	}




}
