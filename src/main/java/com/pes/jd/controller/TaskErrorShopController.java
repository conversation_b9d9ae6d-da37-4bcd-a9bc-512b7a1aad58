package com.pes.jd.controller;


import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.LoginBussiness;
import com.pes.jd.business.PullAndCalErrorShopBusiness;
import com.pes.jd.business.TaskJobBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ErrorShopParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.SendMsgResult;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.util.CommonDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@SuppressWarnings("Duplicates")
@RestController
@RequestMapping("/task/errorShopMessage/")
public class TaskErrorShopController {
    private static final Logger logger = LoggerFactory.getLogger(TaskErrorShopController.class);

    @Autowired
    private TaskJobBusiness taskJobBusiness;
    @Autowired
    private LoginBussiness loginBussiness;

    @Autowired
    private PullAndCalErrorShopBusiness pullAndCalErrorShopBusiness;

    /**
     * 拉取和计算错误店铺
     *
     * @param shopId
     * @param startDateStr
     * @param endDateStr
     * @param type
     * @return
     */
    @RequestMapping(value = "pullAndCalErrorShopData")
    public Object pullAndCalShopData(
            Long shopId,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr,
            @RequestParam("type") String type) {//pull.拉取 cal.计算

        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_JCQ_SEND_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_DATE_PARSE);
        }

        if (shopId != null) {   //-----------------单个错误店铺处理---
            switch (type) {
                case CommonConstants.SHOP_DATA_PULL:
                    try {
                        ApiResponse resultResp = handleSingleShopPull(shopId, startDateStr, endDateStr);
                        resultResp.setSuccess(true);
                        return resultResp;
                    } catch (Exception e) {
                        e.printStackTrace();
                        ApiResponse resultResp = ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
                        resultResp.setSuccess(false);
                        return resultResp;
                    }
                case CommonConstants.SHOP_DATA_CAL:
                    try {
                        ApiResponse resultResp = handleSingleShopCal(shopId, startDateStr, endDateStr);
                        resultResp.setSuccess(true);
                        return resultResp;
                    } catch (Exception e) {
                        e.printStackTrace();
                        ApiResponse resultResp = ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
                        resultResp.setSuccess(false);
                        return resultResp;
                    }
            }
        } else {    //-----------------所有错误店铺处理---

            switch (type) {
                case CommonConstants.SHOP_DATA_PULL:
                    try {
                        ApiResponse resultResp = handleAllShopPull(startDateStr, endDateStr);
                        resultResp.setSuccess(true);
                        return resultResp;
                    } catch (Exception e) {
                        e.printStackTrace();
                        ApiResponse resultResp = ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
                        resultResp.setSuccess(false);
                        return resultResp;
                    }

                case CommonConstants.SHOP_DATA_CAL:
                    try {

                        ApiResponse resultResp = handleAllShopCal(startDateStr, endDateStr);
                        resultResp.setSuccess(true);
                        return resultResp;
                    } catch (Exception e) {
                        e.printStackTrace();
                         ApiResponse resultResp = ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
                        resultResp.setSuccess(false);
                        return resultResp;
                    }
                    default:
                        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_DATE_PARSE);
            }
        }

        return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);

    }

    private ApiResponse handleAllShopCal(String startDateStr, String endDateStr) {
        SendMsgResult sendMsgResult = taskJobBusiness.calAllErrorShopData(startDateStr, endDateStr);
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, sendMsgResult.getMessageId());
    }

    private ApiResponse handleAllShopPull(String startDateStr, String endDateStr) {
        SendMsgResult sendMsgResult = taskJobBusiness.pullAndCalAllErrorShopData(startDateStr, endDateStr);
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, sendMsgResult.getMessageId());
    }

    private ApiResponse handleSingleShopCal(Long shopId, String startDateStr, String endDateStr) {
        SendMsgResult sendMsgResult = taskJobBusiness.calShopData(shopId, startDateStr, endDateStr);
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, sendMsgResult.getMessageId());
    }

    private ApiResponse handleSingleShopPull(Long shopId, String startDateStr, String endDateStr) {
        SendMsgResult sendMsgResult = taskJobBusiness.pullAndCalShopData(shopId, startDateStr, endDateStr);
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002, sendMsgResult.getMessageId());

    }

    /**
     * 获取所有的失败店铺
     * @param nick
     * @param startDateStr
     * @param endDateStr
     * @param type
     * @return
     */
    @RequestMapping(value = "getErrorShopDetail")
    public Object getErrorShopDetail(
            String nick,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr,
            @RequestParam("type") String type) {//pull.拉取 cal.计算
        ApiResponse apiResponse = new ApiResponse();
        TaskJobDispatchEnum taskJobDispatchEnum = null;
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
            if(CommonConstants.SHOP_DATA_CAL.equals(type)){
                logger.info("每日job获取所有的失败店铺 类型为{}", CommonConstants.SHOP_DATA_CAL);
                taskJobDispatchEnum=TaskJobDispatchEnum.SHOP_DATA_CAL;
            } else {//不是计算就是拉取
                taskJobDispatchEnum=TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL;
                logger.info("每日job获取所有的失败店铺 类型为{}", CommonConstants.SHOP_DATA_PULL);
            }
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SF_02_01.getMsg(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }

        try {
            ErrorShopParam errorShopParam = new ErrorShopParam(startDateStr, endDateStr, type, nick, startDate, endDate,taskJobDispatchEnum);
            //获取所有的错误店铺
            long s = System.currentTimeMillis();
            apiResponse= pullAndCalErrorShopBusiness.getErrorShopDetail(errorShopParam);
            long e = System.currentTimeMillis();
            logger.info("每日job查询所有[{}]失败店铺，耗时{}ms",taskJobDispatchEnum.getType(),(e-s));
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SF_02_01.getMsg(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
            apiResponse.setSuccess(false);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }


        return apiResponse;


    }


    /**
     * 获取实时绩效所有的失败店铺
     * @param nick
     * @param pullStatus
     * @return
     */
    @RequestMapping(value = "getRtErrorShopDetail")
    public Object getRtErrorShopDetail(
            @RequestParam("nick")String nick,
            @RequestParam("pullStatus") String pullStatus) {
        ApiResponse apiResponse;
        try {
            ErrorShopParam errorShopParam = new ErrorShopParam();
            errorShopParam.setNick(nick);
            errorShopParam.setPullStatus(pullStatus);
            //获取所有的错误店铺
            apiResponse= pullAndCalErrorShopBusiness.getRtErrorShopDetail(errorShopParam);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SF_02_01.getMsg(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
            apiResponse.setSuccess(false);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }
        return apiResponse;


    }

    /**
     * 更新正在执行中的店铺为失败
     * @param shopId
     * @return
     */
    @RequestMapping(value = "updateRtErrorShopStatus")
    public ApiResponse updateRtErrorShopStatus(@RequestParam(value = "shopId",required = true)String shopId) {
        ApiResponse apiResponse;
        try {
            if(StringUtils.isBlank(shopId)){
                apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_02);
                apiResponse.setSuccess(false);
                return apiResponse;
            }
            //获取店铺db信息
            ShopDTO shopDTO =new ShopDTO();
            shopDTO.setShopId(Long.valueOf(shopId));
            ApiResponse shopInfo = loginBussiness.getShopInfo(shopDTO);
            ShopDTO shoInfoResult = null;
            if(shopInfo.getRpCode().equals(ApiCodeEnum.CODE_SUCCESS_1001.getCode())){
                String s = JSONObject.toJSONString(shopInfo.getData().get("shop"));
                shoInfoResult= JSONObject.parseObject(s).toJavaObject(new ShopDTO().getClass());
            }
            if(null == shoInfoResult){
                logger.info(ApiCodeEnum.CODE_ERROR_DL_01_09.getMsg());
                apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_DL_01_09);
                apiResponse.setSuccess(false);
                return apiResponse;
            }
            ErrorShopParam errorShopParam = new ErrorShopParam();
            errorShopParam.setShopId(shopId);
            errorShopParam.setRtDb(shoInfoResult.getRtDb());
            errorShopParam.setRtSchemaId(shoInfoResult.getRtSchemaId());
            //更新error店铺状态
            return pullAndCalErrorShopBusiness.updateRtErrorShopStatus(errorShopParam);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SF_02_01.getMsg(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
            apiResponse.setSuccess(false);
            return apiResponse;
        }
    }



    @RequestMapping(value = "searchPvuvRecord")
    public Object searchPvuvRecord(@RequestParam("nick") String nick,
                                        @RequestParam("startDateStr") String startDateStr,
                                        @RequestParam("endDateStr") String endDateStr) {
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE);
        }

        ApiResponse resultResp;
        try {
            //Assert.notNull(nick, " nick must be non null ");
            resultResp = pullAndCalErrorShopBusiness.searchPvuvRecord(nick, startDateStr, endDateStr);
            return resultResp;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_PVUV_RECORD);
        }
    }

    @RequestMapping(value = "searchDutyRecord")
    public Object searchDutyRecord(@RequestParam("nick") String nick,
                                   @RequestParam("startDateStr") String startDateStr,
                                   @RequestParam("endDateStr") String endDateStr) {
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE);
        }

        ApiResponse resultResp;
        try {
            //Assert.notNull(nick, " nick must be non null ");
            resultResp = pullAndCalErrorShopBusiness.searchDutyRecord(nick, startDateStr, endDateStr);
            return resultResp;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_DUTY_RECORD);
        }
    }

    /**
     * pullDutylog
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    @RequestMapping(value = "pullErrorDutylog")
    public Object pullErrorDutylog(
            @RequestParam(required = true) String startDateStr,
            @RequestParam(required = true) String endDateStr) {

        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_JCQ_SEND_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_DATE_PARSE);
        }
        try {
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, pullAndCalErrorShopBusiness.pullErrorDutylog(startDateStr, endDateStr));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JCQ_SEND_TASK_JOB);
        }

    }

    @RequestMapping(value = "searchOdHistoryRecord")
    public Object searchOdHistoryRecord(@RequestParam("nick") String nick,
                                   @RequestParam("startDateStr") String startDateStr,
                                   @RequestParam("endDateStr") String endDateStr) {
        try {
            return pullAndCalErrorShopBusiness.searchOrderHistoryRecord(nick, startDateStr, endDateStr);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_1002);
        }
    }
}
