package com.pes.jd.controller;

import com.pes.jd.business.main.ShopUrgeBussiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年08月09 11:25:25<br>
 */
@RequestMapping("/shop/urge")
@RestController
public class ShopUrgeController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ShopSubscribeController.class);
   @Resource
    private ShopUrgeBussiness shopUrgeBussiness;

    @RequestMapping("/selectUrgeShopByShopId")
    public Object selectUrgeShopByShopId(@RequestParam(value="shopId",required = false)Long shopId,@RequestParam(value="type",required = true)Integer type)	{

        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopUrgeBussiness.selectUrgeShopByShopId(shopId,type)));
        } catch (Exception e) {
            logger.error("master selectUrgeShopByShopId error:{}",e.getMessage(),e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_SD_01_01, RestApiResponse2.of(false));
        }
    }

}
