package com.pes.jd.controller;

import com.pes.jd.business.ShopSubscribeBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.SubscribeParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/shop/subscribe")
public class ShopSubController {
	
	private Logger logger = LoggerFactory.getLogger(ShopSubController.class);
		
	@Autowired
	private ShopSubscribeBusiness shopSubscribeBusiness;
	
	/**
	 * 查询订购明细
	 * @param deptId
	 * @param shopParam
	 * @param startTime
	 * @param endTime
	 * @param status
	 * @param version
	 * @param cycle
	 * @param popName 运营经理
	 * @return
	 */
	@RequestMapping("/selectShopSubscribeInfo")
	public Object selectShopSubscribeInfo(
			@RequestParam("startTime") String startTime,
			@RequestParam("shopParam") String shopParam,
			@RequestParam("status") String status,
			@RequestParam("version") String version,
			@RequestParam("cycle") String cycle,
			@RequestParam("popName") String popName,
			@RequestParam(name = "currentPage",required=false) String currentPage,
			@RequestParam(name = "size",required=false) String size){
		Map<String, Object> result=null;
		try {
			SubscribeParam param = new SubscribeParam();

			param.setStartTime(DateUtil.getStartDateFromDateStr(startTime));
			param.setEndTime(DateUtil.getEndDateFromDateStr(startTime));
			param.setShopName(shopParam);
			param.setStatus(org.apache.commons.lang3.StringUtils.isNotEmpty(status)?Integer.valueOf(status):null);
			param.setVersion(version);
			param.setOrderCycle(org.apache.commons.lang3.StringUtils.isNotEmpty(cycle)?Integer.valueOf(cycle):null);
			param.setPopName(popName);
			return shopSubscribeBusiness.selectShopSubscribeInfo(param);
					
		} catch (Exception e) {
			logger.error("web select shop subscribe info error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SD_01_01,result);
		}
	}
	
	
	/**
	 * 店铺订购分析
	 * @param startTime
	 * @param endTime
	 * @param popName 运营经理
	 * @return
	 */
	@RequestMapping("/selectShopSubscribe")
	public Object selectShopSubscribe(
			@RequestParam("startTime") String startTime,
			@RequestParam("endTime") String endTime,
			@RequestParam("popName") String popName){
		Map<String, Object> result=new HashMap<>();
		try {
			SubscribeParam param = new SubscribeParam();

			param.setStartTime(DateUtil.getStartDateFromDateStr(startTime));
			param.setEndTime(DateUtil.getEndDateFromDateStr(endTime));
			param.setPopName(popName);
			return shopSubscribeBusiness.selectShopSubscribe(param);
		} catch (Exception e) {
			logger.error("web select shop subscribe error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SD_01_01,result);
		}
	}
}
