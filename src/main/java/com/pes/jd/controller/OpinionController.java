package com.pes.jd.controller;

import com.pes.jd.business.main.OpinionRecordBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.OpinionParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.JacksonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 顾客意见反馈类
 * <AUTHOR>
 * @date 2019年5月23日 上午10:29:15
 * @since
 */
@RestController
@RequestMapping("/opinion/")
public class OpinionController {
	
	@Autowired
	private OpinionRecordBusiness opinionRecordBusiness;
	
	@RequestMapping("insertOpinionRecord")
	public ApiResponse insertOpinionRecord(@RequestParam("param") String paramStr) {
		ApiResponse apiResponse = null;
		OpinionParam param = null;
		try {
			param = JacksonUtils.json2pojo(paramStr, OpinionParam.class);
			int num = opinionRecordBusiness.insertOpinionRecord(param);
			if(num>0){
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
			}
			else {
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_YF_01_01);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return apiResponse;
	}
}
