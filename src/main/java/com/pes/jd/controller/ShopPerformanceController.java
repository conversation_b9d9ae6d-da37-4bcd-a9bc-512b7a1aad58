/**
 * Project Name:jd-pes
 * File Name:ShopPerformanceController.java
 * Package Name:com.pes.jd.controller
 * Date:2018年10月25日下午3:09:57
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.
 */

package com.pes.jd.controller;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pes.jd.business.sub.*;
import com.pes.jd.model.DTO.ShopTeamOrderPerformanceConciseDTO;
import com.pes.jd.model.DTO.ShopTeamOrderPerformanceDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.JSON.FilterTimeJSON;
import com.pes.jd.model.Param.PerformanceParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * ClassName:ShopPerformanceController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 下午3:09:57 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@RestController
@RequestMapping("/shop/performance/")
public class ShopPerformanceController extends BaseController {

    private final static Logger LOGGER = LoggerFactory.getLogger(ShopPerformanceController.class);

    @Autowired
    private ShopPerformanceBusiness shopPerformanceBusiness;

    @Autowired
    private ReceiveSessionPressureBusiness receiveSessionPressureBusiness;

    @Autowired
    private ReserveActivityBusiness reserveActivityBusiness;

    @Autowired
    private ShopPerformancePreordainBusiness shopPerformancePreordainBusiness;

    @Autowired
    private ShopOvDayBussinessImpl shopOvDayBussiness;
    /**
     * 店铺绩效，自定义报表查询
     *
     * @param shop         店铺 jsonStr
     * @param dateType     时间维度类型，1：日期维度，2：月份维度
     * @param startDateStr 开始时间
     * @param endDateStr   结束时间
     * @param filterTime   过滤部分时间段 jsonStr
     * @param property     自定义报表字段 jsonStr
     * @return resp
     */
    @RequestMapping(value = "/selectShopPerformance", method = RequestMethod.POST)
    public ApiResponse selectShopPerformance(
            @RequestParam("shop") String shop,
            @RequestParam(value = "dateType", required = false) Integer dateType,
            @RequestParam(value = "startDate") String startDateStr,
            @RequestParam(value = "endDate") String endDateStr,
            @RequestParam(value = "filterTime", required = false) String filterTime,
            @RequestParam(value = "property", required = false) String property,
            @RequestParam(value = "filter", required = false) String filter,
            @RequestParam(value = "enquiryValidDurationTime") String enquiryValidDurationTime,
            @RequestParam(value = "outStockValidDurationTime") String outStockValidDurationTime) {

        Map<String, Object> data = new HashMap<>();
        try {
            if (StringUtils.isNotBlank(property)) {
                Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
                Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);

                ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);
                String[] propertys;
                if (property.contains("[") || property.contains("\"")) {
                    List<String> listll = new ObjectMapper().readValue(property, new TypeReference<List<String>>() {
                    });
                    propertys = new String[listll.size()];
                    listll.toArray(propertys);
                } else {
                    propertys = property.split(",");
                }
                //参数字段去重
                propertys = new HashSet<>(Arrays.asList(propertys)).toArray(new String[]{});


                List<FilterTimeJSON> filterTimes = StringUtils.isEmpty(filterTime) ? new ArrayList<>() : new ObjectMapper().readValue(filterTime, new TypeReference<List<FilterTimeJSON>>() {
                });
                List<PerformanceParam.Filter> filters = StringUtils.isEmpty(filter) ? new ArrayList<>() : new ObjectMapper().readValue(filter, new TypeReference<List<PerformanceParam.Filter>>() {
                });

                Map<String, Object> shopPerformance = shopPerformanceBusiness.selectShopPerformance(shopQuery, dateType, startDate, endDate, filterTimes, propertys, filters, Integer.parseInt(enquiryValidDurationTime), Integer.parseInt(outStockValidDurationTime));
                if (shopPerformance != null) {
                    data.put("result", shopPerformance);
                    if (shopPerformance.containsKey("count")) {
                        data.put("count", shopPerformance.remove("count"));
                    }
                    if (shopPerformance.containsKey("avg")) {
                        data.put("avg", shopPerformance.remove("avg"));
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("selectShopPerformance:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_04);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
    }

    /**
     * 店铺绩效，特殊报表分时接待量查询
     *
     * @param shop         店铺 jsonStr
     * @param dateType     时间维度类型，1：日期维度，2：月份维度
     * @param startDateStr 开始时间
     * @param endDateStr   结束时间
     * @param filterTime   过滤部分时间段 jsonStr
     * @return resp
     */
    @RequestMapping(value = "/selectShopReceiveSessionNumHourly", method = RequestMethod.POST)
    public ApiResponse selectShopReceiveSessionNumHourly(
            @RequestParam("shop") String shop,
            @RequestParam("dateType") Integer dateType,
            @RequestParam("startDate") String startDateStr,
            @RequestParam("endDate") String endDateStr,
            @RequestParam("filterTime") String filterTime) {

        Map<String, Object> data = new HashMap<>();
        try {
            Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
            ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);

            List<FilterTimeJSON> filterTimes = new ArrayList<>();
            if (StringUtils.isNotBlank(filterTime)) {
                filterTimes = JacksonUtils.json2list(filterTime, FilterTimeJSON.class);
            }
            Map<String, Object> receiveSessionNumHourlyList = shopPerformanceBusiness.selectShopReceiveSessionNumHourly(shopQuery, dateType, startDate, endDate, filterTimes);
            data.put("result", receiveSessionNumHourlyList);
            if (receiveSessionNumHourlyList.containsKey("count")) {
                data.put("count", receiveSessionNumHourlyList.remove("count"));
            }
            if (receiveSessionNumHourlyList.containsKey("avg")) {
                data.put("avg", receiveSessionNumHourlyList.remove("avg"));
            }
        } catch (Exception e) {
            LOGGER.error("selectShopReceiveSessionNumHourly:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_03_04);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
    }

    /**
     * 店铺绩效，特殊报表接待压力查询
     *
     * @param shop         店铺 jsonStr
     * @param startDateStr 开始时间
     * @param endDateStr   结束时间
     * @param queryFlag    汇总”COUNT” 平均”AVG”
     * @return resp
     */
    @RequestMapping(value = "/selectShopReceiveSessionPressure", method = RequestMethod.POST)
    public ApiResponse selectShopReceiveSessionPressure(
            @RequestParam("shop") String shop,
            @RequestParam("startDate") String startDateStr,
            @RequestParam("endDate") String endDateStr,
            @RequestParam("queryFlag") String queryFlag) {

        try {
            Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
            ShopQuery shopQuery = JacksonUtils.json2pojo(shop, ShopQuery.class);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,
                    receiveSessionPressureBusiness.searchByDateShopNicks(shopQuery.getShopId(), startDate, endDate, null, shopQuery.getSchemaId(), queryFlag, true,
                            false)
            );
        } catch (Exception e) {
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CUSTOM_REPORT);
        }
    }


    /**
     *
     * @param shopstr 店铺
     * @param type  1(店铺) 2(客服) 3(静默)
     * @param sku 商品id 或者  商品名称
     * @param activityId  活动Id
     * @return
     */
    @RequestMapping(value = "/selectPerformancePreordain",method = RequestMethod.POST)
    public Object selectShopPerformancePreordain(
            @RequestParam(value = "shop",required = true) String shopstr,
            @RequestParam(value = "type",required = true) String type,
            @RequestParam(value = "activityId",required = true) String activityId,
            @RequestParam(value = "sku",required = false) String sku){
        try {
            ShopCommonParam shopCommonParam = JacksonUtils.json2pojo(shopstr, ShopCommonParam.class);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,shopPerformancePreordainBusiness.selectPerformancePreordain(shopCommonParam,type,activityId,sku));
        }catch (Exception e){
            LOGGER.error("sub selectShopPerformancePreordain is errr {}",e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_02);
        }
    }


    /**
     * 查询店铺预约活动
     * @param shopstr 店铺的
     * @param sku  商品名称 或者 商品skuId
     * @param activityId 预约组id
     * @param type  预约类型：  (1)全部  (2)不预约不可购买 (3)不预约可购买   默认全部 (1)      数据库字段解释   1：预约购买资格（仅预约的用户才可以进行购买）； 3：预约享优惠（为预约的用户绑定令牌，预约用户才可享受令牌价）； 4：预约消息提醒（预约的用户在抢购时可推送消息提醒）5：预约抽签（仅中签用户可购买）
     * @param startDateStr  预约起时间
     * @param endDateStr   预约止时间
     * @param conditionType   预约是否有效 : (1)全部   (2)无效/结束  (3)有效   默认全部 (1)
     * @param status  预约状态: (1)全部  (2)预约进行中 (3)预约结束抢购未开始  (4)抢购进行中  (5)抢购结束
     * @return 根据条件 返回 店铺 一段时间内的 预约 商品
     */
    @RequestMapping(value = "/selectReserveActivityByShop",method = RequestMethod.POST)
    public ApiResponse selectReserveActivityByShop(
            @RequestParam(value = "shop",required = true) String shopstr,
            @RequestParam(value = "startDate",required = true) String startDateStr,
            @RequestParam(value = "endDate" ,required = true) String endDateStr,
            @RequestParam(value = "sku",required = false) String sku,
            @RequestParam(value = "activityId",required = false) String activityId,
            @RequestParam(value = "type",required = false) String type,
            @RequestParam(value = "conditionType",required = false) String conditionType,
            @RequestParam(value = "status",required = false) String status){

        try {
            Date startDate = DateUtil.getStartDateFromDateStr(startDateStr);
            Date endDate = DateUtil.getEndDateFromDateStr(endDateStr);
            ShopCommonParam shopCommonParam = JacksonUtils.json2pojo(shopstr, ShopCommonParam.class);
           return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,reserveActivityBusiness.selectReserveActivityByShop(shopCommonParam,startDate,endDate,sku,
                   Strings.isBlank(activityId) ? null : activityId,
                    Strings.isBlank(type) ? null : Long.valueOf(type),
                    Strings.isBlank(conditionType) ? null : Long.valueOf(conditionType),
                    status));
        }catch (Exception e){
            LOGGER.error("sub selectReserveActivityByShop is error {}",e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_01);
        }
    }


    /**
     * 查询店铺预约绩效SKU明细
     * @param shopstr 店铺的
     * @param type   1(店铺) 2(客服) 3(静默)
     * @param activityId    活动Id
     * @param skuId
     * @return
     */
    @RequestMapping(value = "/selectShopPerformancePreordainSkuDetailed",method = RequestMethod.POST)
    public ApiResponse selectShopPerformancePreordainSkuDetailed(
            @RequestParam(value = "shop",required = true) String shopstr,
            @RequestParam(value = "type",required = true) String type,
            @RequestParam(value = "activityId",required = true) String activityId,
            @RequestParam(value = "skuId",required = false) String skuId,
            @RequestParam(value = "skuName",required = false) String skuName){
        try {
            ShopCommonParam shopCommonParam = JacksonUtils.json2pojo(shopstr, ShopCommonParam.class);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,shopPerformancePreordainBusiness.selectShopPerformancePreordainSkuDetailed(shopCommonParam,type,activityId,Strings.isBlank(skuId) ? null : Long.valueOf(skuId) ,skuName));
        }catch (Exception e){
            LOGGER.error("sub selectShopPerformancePreordainSkuDetailed is error {}",e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_02);
        }
    }

    @RequestMapping(value = "/selectShopSaleAmount")
    public Object selectShopSaleAmount(@RequestParam("shop") String shop,
                                       @RequestParam("date") String date){
        try{
            ShopCommonParam shopCommonParam = JSONObject.parseObject(shop, ShopCommonParam.class);
            Date toDay = DateUtils.parseYMd(date);
            ShopTeamOrderPerformanceConciseDTO shopTeamOrderPerformanceConcise = shopPerformanceBusiness.selectShopSaleAmount(shopCommonParam, toDay);
            return RestResponseTypeRef.ofSuccess(shopTeamOrderPerformanceConcise);
        }catch (Exception e){
            LOGGER.error("selectShopSaleAmount error e :{}",e.getMessage());
            return RestResponseTypeRef.ofFail();
        }
    }
}
