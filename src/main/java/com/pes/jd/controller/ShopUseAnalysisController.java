package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.ShopUseAnalysisBussiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.DateUtils;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/shop/use/analysis")
public class ShopUseAnalysisController extends BaseController {
	private Logger logger = LoggerFactory.getLogger(ShopUseAnalysisController.class);
	@Autowired
	private ShopUseAnalysisBussiness shopUseAnalysisBussiness;
	
	@RequestMapping("/selectShopUseAnalysisByShopIdSetByDate")
	public Object selectShopUseAnalysisByShopIdSetByDate(
			@RequestParam("causeShopLst")String causeShopLstStr,
			@RequestParam("startDate")String startDateStr,
			@RequestParam("endDate")String endDateStr)	{
		List<CauseShop> causeShopLst;
		Date startDate;
		Date endDate;
		try {
			causeShopLst=JacksonUtils.json2list(causeShopLstStr,CauseShop.class);
			startDate= DateUtils.parseYMdHms(startDateStr);
			endDate=DateUtils.parseYMdHms(endDateStr);
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
		}
		try {
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopUseAnalysisBussiness.selectShopUseAnalysisByShopIdSetByDate(causeShopLst, startDate,endDate)));
		} catch (Exception e) {
			logger.error("sub selectShopUseAnalysisByShopIdSetByDate error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/selectProblemShopIdLst")
	public Object selectProblemShopIdLst(@RequestParam("dbName") String dbName,
										 @RequestParam("schemaId") String schemaId,
										 @RequestParam("startDate") String startDateStr,
										 @RequestParam("endDate") String endDateStr){
		Date startDate = DateUtils.parseYMd(startDateStr);
		Date endDate = DateUtils.parseYMd(endDateStr);

		ApiResponse apiResponse = new ApiResponse();
		try{
			Map<String, Object> map = Maps.newHashMap();
			List<Long> shopIdLst = shopUseAnalysisBussiness.selectProblemShopIdLst(dbName, schemaId, startDate, endDate);
			map.put("result",shopIdLst);
			apiResponse.setData(map);
			return apiResponse;
		}catch(Exception e){
			logger.info("sub selectProblemShopIdLst error:{}",e.getMessage(),e);
			return apiResponse;
		}

	}
}
