package com.pes.jd.controller;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.pes.jd.business.sub.ShopDataAnalysisBussiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.CustomerReceiveDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.JSON.EnquiryOrderLossVO;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.*;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.*;

/**
 * 数据分析
 * ClassName:ReceiveAnalysisController <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 下午3:09:17 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */

@RestController
@RequestMapping("/data/analysis")
public class ShopDataAnalysisController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ShopDataAnalysisController.class);

    @Autowired
    private ShopDataAnalysisBussiness shopDataAnalysisBussiness;

    /**
     * queryCsLoginlogCollect:(登录记录总览查询). <br/>
     *
     * @param startDate
     * @param endDate
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("queryCsDutyRecordCollect")
    public ApiResponse queryCsDutyRecordCollect(@RequestParam(name = "shop") String shopStr,
                                                @RequestParam(name = "csLoginlogParam") String csLoginlogParamStr,
                                                @RequestParam(name = "startDate") String startDate,
                                                @RequestParam(name = "endDate") String endDate,
                                                @RequestParam(name = "dutyRidCsSwitch") Boolean dutyRidCsSwitch) {
        logger.info("/data/analysis/queryCsDutyRecordCollect 方法-------------------开始执行-------------");
        ApiResponse apiResponse = new ApiResponse();

        CsLoginlogParam csLoginlogParam;
        ShopCommonParam shop;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            csLoginlogParam = JacksonUtils.json2pojo(csLoginlogParamStr, CsLoginlogParam.class);
        } catch (Exception e) {
            logger.error("登录分析总览-json参数解析异常", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_01);
        }

        Map<String, Object> result;
        // 时间参数处理
        Date startTime;
        Date endTime;
        try {
            startTime = DateUtil.getStartTimeOfDate(DateFormatUtils.getDateFormatOfYMd().parse(startDate));
            endTime = DateUtil.getEndTimeOfDate(DateFormatUtils.getDateFormatOfYMd().parse(endDate));
        } catch (ParseException e1) {
            logger.error("登录分析总览-时间解析异常", e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_08);
        }
        try {
            result = shopDataAnalysisBussiness.queryCsDutyRecordSituation(shop, csLoginlogParam, startTime, endTime, dutyRidCsSwitch);
            if (result == null) {
                // 查询异常，返回错误提示
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_02);
            } else {
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
                apiResponse.setData(result);
            }
        } catch (Exception e) {
            logger.error("登录分析总览-代码执行异常", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_03);
        }
        logger.info("/data/analysis/queryCsDutyRecordCollect 方法-------------------执行结束-------------");
        return apiResponse;
    }

    /**
     * queryCsLoginlogDetail:(登录记录详情查询). <br/>
     *
     * @param startDate
     * @param endDate
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("queryCsDutyRecordDetail")
    public ApiResponse queryCsLoginlogDetail(@RequestParam(name = "shop") String shopStr,
                                             @RequestParam(name = "csLoginlogParam") String csLoginlogParamStr,
                                             @RequestParam(name = "startDate") String startDate,
                                             @RequestParam(name = "endDate") String endDate) {
        logger.info("/data/analysis/queryCsDutyRecordDetail 方法-------------------开始执行-------------");
        logger.info("csLoginlogParamStr:::" + csLoginlogParamStr);
        ApiResponse apiResponse = new ApiResponse();
        Map<String, Object> result;

        CsLoginlogParam csLoginlogParam;
        ShopCommonParam shop;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            csLoginlogParam = JacksonUtils.json2pojo(csLoginlogParamStr, CsLoginlogParam.class);
        } catch (Exception e) {
            logger.error("登录分析总览-json参数解析异常", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_01);
        }

        // 时间参数处理
        Date startTime;
        Date endTime;
        try {
            startTime = DateUtil.getStartTimeOfDate(DateFormatUtils.getDateFormatOfYMd().parse(startDate));
            endTime = DateUtil.getEndTimeOfDate(DateFormatUtils.getDateFormatOfYMd().parse(endDate));
        } catch (ParseException e1) {
            logger.error("登录分析详情-时间解析异常", e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_09);
        }
        try {
            result = shopDataAnalysisBussiness.queryCsDutyRecordSituationDetail(shop, csLoginlogParam, startTime, endTime);
            if (result == null) {
                // 查询异常，返回错误提示
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_SF_04_05.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_SF_04_05.getMsg());
            } else {
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
                apiResponse.setData(result);
            }
        } catch (Exception e) {
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_SF_04_06.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_SF_04_06.getMsg());
            logger.error("登录分析详情-代码执行异常", e);
        }
        logger.info("/data/analysis/queryCsDutyRecordDetail 方法-------------------执行结束-------------");
        return apiResponse;
    }

    /**
     * queryCsLoginlogDetail:(登录分析上下线详情，针对单个客服，单天查询). <br/>
     * 2019年1月3日 下午5:29:57
     *
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("queryCsLoginOperateDetail")
    public ApiResponse queryCsLoginOperateDetail(@RequestParam(name = "shop") String shopStr,
                                                 @RequestParam(name = "userQuery") String userQueryStr,
                                                 @RequestParam(name = "delayTime") String delayTimeStr,
                                                 @RequestParam(name = "startDate") String startDate,
                                                 @RequestParam(name = "endDate") String endDate) {
        ApiResponse apiResponse = new ApiResponse();
        Map<String, Object> result;
        ShopCommonParam shop;
        UserQuery userQuery;
        Long delayTime;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            userQuery = JacksonUtils.json2pojo(userQueryStr, UserQuery.class);
            delayTime = JacksonUtils.json2pojo(delayTimeStr, Long.class);
        } catch (Exception e) {
            logger.error("queryCsLoginOperateDetail josn error:", e.getMessage());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_10);
        }
        // 时间参数处理
        Date startTime;
        Date endTime;
        try {
            startTime = DateUtil.getStartTimeOfDate(DateFormatUtils.getDateFormatOfYMd().parse(startDate));
            endTime = DateUtil.getEndTimeOfDate(DateFormatUtils.getDateFormatOfYMd().parse(endDate));
        } catch (ParseException e1) {
            logger.error("登录分析上下线详情-时间解析异常", e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_04_11);
        }
        try {
            logger.info("登录分析上下线详情使用业务天分隔时间为：{}", delayTime);
            result = shopDataAnalysisBussiness.queryCsLoginOperateDetail(shop, startTime, endTime, userQuery,
                    delayTime);
            if (result == null) {
                // 查询异常，返回错误提示
                apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_SF_04_12.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_SF_04_12.getMsg());
            } else {
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
                apiResponse.setData(result);
            }
        } catch (Exception e) {
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_SF_04_13.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_SF_04_13.getMsg());
            logger.error("登录分析上下线详情-代码执行异常", e);
        }
        return apiResponse;
    }

    /**
     * 接待过滤分析<br/>  人数维度
     */
    @RequestMapping("/reciveFilterRecord")
    public ApiResponse reciveFilterRecord(@RequestParam(name = "shop") String shopStr,
                                          @RequestParam(name = "param") String receiveFilterParamStr) {
        ApiResponse apiResponse;
        Map<String, Object> result = new HashMap<>();
        ReceiveFilterParam receiveParam;

        ShopCommonParam shop;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            receiveParam = JacksonUtils.json2pojo(receiveFilterParamStr, ReceiveFilterParam.class);
        } catch (Exception e1) {
            logger.error("json parse error:{}", e1.getMessage(), e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
        }
        List<ReceiveFilterRecordVO> receiveFilterRecordVOs;
        try {
            receiveFilterRecordVOs = shopDataAnalysisBussiness.searchShopReciveFilterRecordLst(shop, receiveParam);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        } catch (Exception e) {
            receiveFilterRecordVOs = new ArrayList<>();
            logger.error("serach reciveRecord error:{}", e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_01);
        }
        result.put("receiveFilterLst", receiveFilterRecordVOs);
        apiResponse.setData(result);
        return apiResponse;
    }


    /**
     * 接待过滤分析   按会话维度
     * @param shopStr
     * @param receiveFilterParamStr
     * @return
     */
    @RequestMapping("/reciveFilterRecordSession")
    public ApiResponse reciveFilterRecordSession(@RequestParam(name = "shop") String shopStr,
                                                 @RequestParam(name = "param") String receiveFilterParamStr) {


        ApiResponse apiResponse;
        Map<String, Object> result = new HashMap<>();
        ReceiveFilterParam receiveParam;

        ShopCommonParam shop;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            receiveParam = JacksonUtils.json2pojo(receiveFilterParamStr, ReceiveFilterParam.class);
        } catch (Exception e1) {
            logger.error("json parse error:{}", e1.getMessage(), e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
        }
        List<ReceiveFilterRecordSessionVO> receiveFilterRecordVOs;
        try {
            receiveFilterRecordVOs = shopDataAnalysisBussiness.searchShopReciveFilterRecordLstSession(shop, receiveParam);

            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        } catch (Exception e) {
            receiveFilterRecordVOs = new ArrayList<>();
            logger.error("serach reciveRecord error:{}", e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_01);
        }
        result.put("receiveFilterLst", receiveFilterRecordVOs);
        apiResponse.setData(result);
        return apiResponse;
    }








    /**
     * 顾客接待分析<br/> 人数维度
     *
     * @return
     */
    @RequestMapping("/customerReceiveRecord")
    public ApiResponse customerReceiveRecord(@RequestParam(name = "shop") String shopStr,
                                             @RequestParam(name = "param") String paramStr,
                                             @RequestParam("sortPageQuery") String sortPageQueryStr) {
        ApiResponse apiResponse;
        Map<String, Object> result = new HashMap<>();
        CustomerReceiveParam receiveParam;
        ShopCommonParam shop;
        DataAnalysisVO<CustomerReceiveDTO> custReceiveLst;
        SortPageQuery sortQuery;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            receiveParam = JacksonUtils.json2pojo(paramStr, CustomerReceiveParam.class);
            sortQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
        } catch (Exception e1) {
            logger.error("json parse error:{}", e1.getMessage(), e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
        }
        try {
            custReceiveLst = shopDataAnalysisBussiness.searchCustomerReciveRecordLst(shop, receiveParam, sortQuery);

            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        } catch (Exception e) {
            custReceiveLst = new DataAnalysisVO<>();
            logger.error("serach customerReceiveRecord error:{}", e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_06);
        }
        result.put("dataAnalysisVO", custReceiveLst);
        apiResponse.setData(result);
        return apiResponse;
    }

    /**
     * 顾客接待分析<br/> 会话维度
     *
     * @return
     */
    @RequestMapping("/customerReceiveRecordForChatSession")
    public ApiResponse customerReceiveRecordForChatSession(@RequestParam(name = "shop") String shopStr,
                                                           @RequestParam(name = "param") String paramStr,
                                                           @RequestParam(name = "sortQuery") String sortQueryStr) {
        ApiResponse apiResponse;
        Map<String, Object> result = new HashMap<>();
        CustomerReceiveParam receiveParam;
        ShopCommonParam shop;
        SortPageQuery sortQuery;
        DataAnalysisVO<CsCustChatSessionVO> dataAnalysisVO;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            receiveParam = JacksonUtils.json2pojo(paramStr, CustomerReceiveParam.class);
            sortQuery = JacksonUtils.json2pojo(sortQueryStr, SortPageQuery.class);
        } catch (Exception e1) {
            logger.error("json parse error:{}", e1.getMessage(), e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
        }
        try {
            dataAnalysisVO = shopDataAnalysisBussiness.serachCustomerReceiveRecordForChatSession(shop, receiveParam, sortQuery);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        } catch (Exception e) {
            dataAnalysisVO = new DataAnalysisVO<>();
            logger.error("serach customerReceiveRecordForChatSession error:{}", e.getMessage(), e);
            apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_06);
        }
        result.put("dataAnalysisVO", dataAnalysisVO);
        apiResponse.setData(result);
        return apiResponse;
    }

    /**
     * 查询聊天对象
     * searchChatpeerLst:(查询聊天对象). <br/>
     *
     * @param buyerNick
     * @param keyWord
     * @return ApiResponse
     * <AUTHOR>
     * @since JDK 1.8
     */
    @RequestMapping("/searchChatpeerLst")
    @ResponseBody
    public ApiResponse searchChatpeerLst(@RequestParam(name = "shop") String shopStr,
                                         @RequestParam(name = "csLst") String csLstStr,
                                         @RequestParam(name = "buyerNick") String buyerNick,
                                         @RequestParam(name = "startDate") String startDateStr,
                                         @RequestParam(name = "endDate") String endDateStr,
                                         @RequestParam(name = "keyWord") String keyWord) {
        ApiResponse result;
        Date startDate;
        Date endDate;
        ShopCommonParam shop;
        List<UserQuery> csLst;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            csLst = JacksonUtils.json2list(csLstStr, UserQuery.class);
            startDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDateStr));
            endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
        } catch (Exception e) {
            logger.error("date formate error：{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
        }
        List<ReceiveBuyerVO> receiveBuyerLst;
        try {
            receiveBuyerLst = shopDataAnalysisBussiness.searchChatpeerLst(shop, startDate, endDate, csLst, buyerNick, keyWord);
        } catch (Exception e) {
            logger.error("search ChatpeerLst error：{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_03);
        }
        Map<String, Object> data = new HashMap<>(1);
        data.put("chatpeerLst", receiveBuyerLst);
        result = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        result.setData(data);
        return result;
    }


    /**
     * 查询聊天内容
     * searchChatlogLst:(查询聊天内容). <br/>
     *
     * @param shopStr
     * @param buyerNick
     * @param startDateStr
     * @param endDateStr
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("/chatlogLst")
    public ApiResponse searchChatlogLst(
            @RequestParam(name = "shop") String shopStr,
            @RequestParam(name = "userQuery") String userQueryStr,
            @RequestParam(name = "buyerNick") String buyerNick,
            @RequestParam(name = "sid") String sid,
            @RequestParam(name = "startDate") String startDateStr,
            @RequestParam(name = "endDate") String endDateStr,
            @RequestParam(name = "isSlowResp") boolean isSlowResp,
            @RequestParam(required = false, name = "shopSystemsetting") String shopSystemsettingStr) {
        ApiResponse result;
        Date startDate;
        Date endDate;
        ShopCommonParam shop;
        UserQuery userQuery;
        ShopSystemsettingDTO shopSystemsetting;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            userQuery = JacksonUtils.json2pojo(userQueryStr, UserQuery.class);
            shopSystemsetting = JacksonUtils.json2pojo(shopSystemsettingStr, ShopSystemsettingDTO.class);
            startDate = DateFormatUtils.parseYMdHms(startDateStr);
            endDate = DateFormatUtils.parseYMdHms(endDateStr);
            if (StringUtils.isNotBlank(sid)) {
                startDate = DateUtil.getStartTimeOfDate(startDate);
                endDate = DateUtil.getEndTimeOfDate(endDate);
            }
        } catch (Exception e) {
            logger.error(" date format :{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_02);
        }
        List<NewChatLogVO> chatLogMsgLst;
        try {
            chatLogMsgLst = shopDataAnalysisBussiness.searchChatlogLst(shop, startDate, endDate, sid, userQuery, buyerNick, shopSystemsetting, isSlowResp);
        } catch (Exception e) {
            logger.error(" serach chatLogLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_04);
        }
        Map<String, Object> data = new HashMap<>(1);
        data.put("chatLogMsgLst", chatLogMsgLst);
        result = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        result.setData(data);
        return result;
    }

    /**
     * 查询聊天内容-数据分析，敏感量分析
     * searchChatlogLst:(查询聊天内容). <br/>
     *
     * @param shopStr
     * @param buyerNick
     * @param startDateStr
     * @param endDateStr
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("/chatlogLstForWarning")
    public ApiResponse searchChatlogLstForWarning(
            @RequestParam(name = "shop") String shopStr,
            @RequestParam(name = "userQuery") String userQueryStr,
            @RequestParam(name = "buyerNick") String buyerNick,
            @RequestParam(name = "sid") String sid,
            @RequestParam(name = "startDate") String startDateStr,
            @RequestParam(name = "endDate") String endDateStr,
            @RequestParam(required = false, name = "keyWordLst") String keyWordLstStr,
            @RequestParam(required = false, name = "direction") Integer direction//direction  0:客服->买家 1：买家->客服
    ) {
        ApiResponse result;
        Date startDate;
        Date endDate;
        ShopCommonParam shop;
        UserQuery userQuery;
        List<String> keyWordLst;
//        ShopSystemsettingDTO shopSystemsetting;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            userQuery = JacksonUtils.json2pojo(userQueryStr, UserQuery.class);
//            shopSystemsetting = JacksonUtils.json2pojo(shopSystemsettingStr, ShopSystemsettingDTO.class);
            startDate = DateFormatUtils.parseYMd(startDateStr);
            endDate = DateFormatUtils.parseYMd(endDateStr);
            if (StringUtils.isNotBlank(sid)) {
                startDate = DateUtil.getStartTimeOfDate(startDate);
                endDate = DateUtil.getEndTimeOfDate(endDate);
            }
            keyWordLst = Arrays.asList(keyWordLstStr.split(","));
        } catch (Exception e) {
            logger.error(" date format :{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_02);
        }
        Map<String, Object> resultMap;
        try {
            resultMap = shopDataAnalysisBussiness.searchChatlogLst(shop, startDate, endDate, sid, userQuery, buyerNick, keyWordLst, direction);
        } catch (Exception e) {
            logger.error(" serach chatLogLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_04);
        }
        Map<String, Object> data = new HashMap<>(1);
        data.put("chatLogMsgLst", resultMap.get("newChatLogVOLst"));
        data.put("keywordLst", resultMap.get("keywordLst"));
        result = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        result.setData(data);
        return result;
    }


    /**
     * 统计详情关联会话
     *
     * @param shopStr
     * @param userQueryStr
     * @param buyerNick
     * @param sid
     * @param startDateStr
     * @param endDateStr
     * @return
     */
    @RequestMapping("/searchChatlogLstForConver")
    public ApiResponse searchChatlogLstForConver(
            @RequestParam(name = "shop") String shopStr,
            @RequestParam(name = "userQuery") String userQueryStr,
            @RequestParam(name = "buyerNick") String buyerNick,
            @RequestParam(name = "sid") String sid,
            @RequestParam(name = "startDate") String startDateStr,
            @RequestParam(name = "endDate") String endDateStr) {

        ApiResponse result;
        Date startDate;
        Date endDate;
        ShopCommonParam shop;
        List<UserQuery> userQueries;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            startDate = DateFormatUtils.parseYMdHms(startDateStr);
            endDate = DateFormatUtils.parseYMdHms(endDateStr);
            userQueries = JacksonUtils.json2list(userQueryStr, UserQuery.class);
            if (StringUtils.isNotBlank(sid)) {
                startDate = DateUtil.getStartTimeOfDate(startDate);
                endDate = DateUtil.getEndTimeOfDate(endDate);
            }
        } catch (Exception e) {
            logger.error(" date format :{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_02);
        }

        List<NewChatLogVO> chatLogMsgLst = null;
        try {
            if (CollectionUtils.isNotEmpty(userQueries) && userQueries.size() > 1) {//多个客服
                chatLogMsgLst = shopDataAnalysisBussiness.searchChatlogLstForConver(shop, startDate, endDate, sid, userQueries, buyerNick);
            }
        } catch (Exception e) {
            logger.error(" serach chatLogLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_04);
        }
        Map<String, Object> data = new HashMap<>(1);
        data.put("chatLogMsgLst", chatLogMsgLst);
        result = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        result.setData(data);
        return result;
    }

    /**
     * 查询聊天内容ForPlugin
     * searchChatlogLst:(查询聊天内容). <br/>
     *
     * @param shopStr
     * @param buyerNick
     * @param startDateStr
     * @param endDateStr
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("/chatlogLstForPlugin")
    public ApiResponse chatlogLstForPlugin(
            @RequestParam(name = "shop") String shopStr,
            @RequestParam(name = "userQuery") String userQueryStr,
            @RequestParam(name = "buyerNick") String buyerNick,
            @RequestParam(name = "sid") String sid,
            @RequestParam(name = "startDate") String startDateStr,
            @RequestParam(name = "endDate") String endDateStr) {
        ApiResponse result;
        Date startDate;
        Date endDate;
        ShopCommonParam shop;
        UserQuery userQuery;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            userQuery = JacksonUtils.json2pojo(userQueryStr, UserQuery.class);
            startDate = DateFormatUtils.parseYMdHms(startDateStr);
            endDate = DateFormatUtils.parseYMdHms(endDateStr);
            if (StringUtils.isNotBlank(sid)) {
                startDate = DateUtil.getStartTimeOfDate(startDate);
                endDate = DateUtil.getEndTimeOfDate(endDate);
            }
        } catch (Exception e) {
            logger.error(" date format :{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_02);
        }

        List<ChatLogVO> chatLogMsgLst;
        try {
            chatLogMsgLst = shopDataAnalysisBussiness.searchChatlogLstForPlugin(shop, startDate, endDate, sid, userQuery, buyerNick);
        } catch (Exception e) {
            logger.error(" serach chatLogLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_04);
        }
        Map<String, Object> data = new HashMap<>(1);
        data.put("chatLogMsgLst", chatLogMsgLst);
        result = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
        result.setData(data);
        return result;
    }


    @RequestMapping("/tradeRecord")
    @ResponseBody
    public Object searchTradeRecordLst(@RequestParam(name = "shopId") String shopId,
                                       @RequestParam(name = "groupId") String groupId,
                                       @RequestParam(name = "csNick") String csNick,
                                       @RequestParam(name = "buyerNick") String buyerNick,
                                       @RequestParam(name = "startDate") String startDate,
                                       @RequestParam(name = "endDate") String endDate,
                                       @RequestParam(name = "sellerType") String sellerType,
                                       int start,
                                       int length) {
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            logger.error(" date format :{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }

        UserShopQuery shop = getUserShopParam(shopId, groupId, csNick);
        Map<String, Object> map;
        try {
            map = shopDataAnalysisBussiness.searchTradeRecordLst(shop, sDate, eDate, buyerNick, sellerType, start, length, groupId, csNick);
        } catch (Exception e) {
            logger.error(" serach dealAnalysisVoLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, map);
    }

    @RequestMapping("/searchEnquiryLostRecord")
    public ApiResponse searchEnquiryLostRecord(@RequestParam(name = "lossOrderParam") String lossOrderParamStr,
                                               @RequestParam(name = "startDate") String startDate,
                                               @RequestParam(name = "endDate") String endDate,
                                               @RequestParam(name = "chatLimitNum") Integer chatLimitNum,
                                               @RequestParam(name = "sortPageQueryStr") String sortPageQueryStr,
                                               @RequestParam(name = "sessionDuration", required = false) Integer sessionDuration,
                                               @RequestParam(name = "skuIds", required = false) String skuIds) {
        ApiResponse result = new ApiResponse();
        SortPageQuery sortPageQuery;
        LossOrderParam lossOrderParam;
        try {
            sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
            lossOrderParam = JacksonUtils.json2pojo(lossOrderParamStr, LossOrderParam.class);
        } catch (Exception e) {
            logger.error("searchEnquiryLostRecord json2pojo error:{}", e.getMessage(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
            //查询间隔不能超过31天
            if (DateFormatUtils.getDateByPeriod(sDate, CommonConstants.DAY_ENQUIRY_LOST).getTime() < eDate.getTime()) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_21);
            }
        } catch (ParseException e) {
            logger.error("searchEnquiryLostRecord date format :{}", e.getMessage(), e);
            // 日期格式不正确
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }
        EnquiryLostVO enquiryLostVO;
        try {
            enquiryLostVO = shopDataAnalysisBussiness.searchEnquiryLostRecordLst(lossOrderParam, sDate, eDate, chatLimitNum, sortPageQuery, sessionDuration, skuIds);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            // 流失记录查询失败！
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_25);
        }
        Map<String, Object> resultMap = new HashMap<>(1);
        resultMap.put("enquiryLostVO", enquiryLostVO);
        result.setData(resultMap);
        result.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
        result.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        return result;
    }

    @RequestMapping("/searchEnquiryLostRecordOfSpu")
    public ApiResponse searchEnquiryLostRecordOfSpu(@RequestParam(name = "lossOrderParam") String lossOrderParamStr,
                                                    @RequestParam(name = "startDate") String startDate,
                                                    @RequestParam(name = "endDate") String endDate,
                                                    @RequestParam(name = "chatLimitNum") Integer chatLimitNum,
                                                    @RequestParam(name = "sortPageQueryStr") String sortPageQueryStr,
                                                    @RequestParam(name = "sessionDuration", required = false) Integer sessionDuration,
                                                    @RequestParam(name = "skuIds", required = false) String skuIds) {
        ApiResponse result = new ApiResponse();
        SortPageQuery sortPageQuery;
        LossOrderParam lossOrderParam;
        try {
            sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
            lossOrderParam = JacksonUtils.json2pojo(lossOrderParamStr, LossOrderParam.class);
        } catch (Exception e) {
            logger.error("searchEnquiryLostRecord json2pojo  OfSpu error:{}", e.getMessage(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
            //查询间隔不能超过31天
            if (DateFormatUtils.getDateByPeriod(sDate, CommonConstants.DAY_ENQUIRY_LOST).getTime() < eDate.getTime()) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_21);
            }
        } catch (ParseException e) {
            logger.error("searchEnquiryLostRecord date format OfSpu :{}", e.getMessage(), e);
            // 日期格式不正确
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }
        EnquiryLostVO enquiryLostVO;
        try {
//            enquiryLostVO = shopDataAnalysisBussiness.searchEnquiryLostRecordLst(lossOrderParam, sDate, eDate, chatLimitNum, sortPageQuery, sessionDuration, skuIds);
            enquiryLostVO = shopDataAnalysisBussiness.searchEnquiryLostRecordLstOfSpu(lossOrderParam, sDate, eDate, chatLimitNum, sortPageQuery, sessionDuration, skuIds);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            // 流失记录查询失败！
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_25);
        }
        Map<String, Object> resultMap = new HashMap<>(1);
        resultMap.put("enquiryLostVO", enquiryLostVO);
        result.setData(resultMap);
        result.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
        result.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        return result;
    }

    @RequestMapping("/searchEnquiryOrderLostRecord")
    public ApiResponse searchEnquiryOrderLostRecord(@RequestParam(name = "lossOrderParam") String lossOrderParamStr,
                                                    @RequestParam(name = "startDate") String startDate,
                                                    @RequestParam(name = "endDate") String endDate,
                                                    @RequestParam(name = "orderInfoLogUploadParamStr", required = false) String orderInfoLogUploadParamStr) {
        ApiResponse result = new ApiResponse();
        Date sDate;
        Date eDate;
        LossOrderParam lossOrderParam;
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            lossOrderParam = JacksonUtils.json2pojo(lossOrderParamStr, LossOrderParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (Exception e) {
            logger.error("searchEnquiryLostRecord date format :{}", e.getMessage(), e);
            // 日期格式不正确
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }

        //查询间隔不能超过31天
        if (DateFormatUtils.getDateByPeriod(sDate, CommonConstants.DAY_ENQUIRY_LOST).getTime() < eDate.getTime()) {
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_21);
        }
        List<EnquiryOrderLossVO> enquiryOrderLossList;
        try {
            enquiryOrderLossList = shopDataAnalysisBussiness.searchEnquiryOrderLostRecordLst(lossOrderParam, sDate, eDate, orderInfoLogUploadParam);
            //上传参数
//            UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParam);
//   		 	param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_ENQUIRYORDERLOSTRECORD.getName());
//            uploadDBOperationBusiness.upload(param);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            // 流失记录查询失败！
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_26);
        }
        Map<String, Object> resultMap = new HashMap<>(1);
        resultMap.put("retList", enquiryOrderLossList);
        result.setData(resultMap);
        result.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
        result.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        return result;
    }

    /**
     * 静默的流失记录 silenceLostRecord:(静默下单未付款流失记录). <br/>
     *
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("/silenceOrderLostRecord")
    public ApiResponse searchSilenceOrderLostRecordLst(@RequestParam(name = "lossOrderParam") String lossOrderParamStr,
                                                       @RequestParam(name = "startDate") String startDate,
                                                       @RequestParam(name = "endDate") String endDate,
                                                       String orderInfoLogUploadParamStr) {
        ApiResponse result = new ApiResponse();
        Date sDate;
        Date eDate;
        LossOrderParam lossOrderParam;
        OrderInfoLogUploadParam orderInfoLogUploadParam;
        try {
            lossOrderParam = JacksonUtils.json2pojo(lossOrderParamStr, LossOrderParam.class);
            orderInfoLogUploadParam = JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (Exception e) {
            logger.error(" date format :{}", e.getMessage(), e);
            // 日期格式不正确
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }
        if (DateUtil.compareTimeThanOneYear(sDate)) {
            // 时间选择超过范围，请缩小时间间隔
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_21);
        }
        List<SilenceLostRecord> silentLostRecLst;
        try {
            silentLostRecLst = shopDataAnalysisBussiness.searchSilentOrderLostRecordLst(lossOrderParam, sDate, eDate, orderInfoLogUploadParam);
            //上传参数
//            UploadDBOperationParam param = UploadDBOperationBusinessImpl.getParam(null,orderInfoLogUploadParam);
//   		 	param.setUrl(CommonConstants.URI+RequestUrlEnum.DATA_ANALYSIS_SILENCEORDERLOSTRECORD.getName());
//            uploadDBOperationBusiness.upload(param);

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            // 流失记录查询失败！
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_22);
        }
        Map<String, Object> resultMap = new HashMap<>(1);
        resultMap.put("retList", silentLostRecLst);
        result.setData(resultMap);
        result.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
        result.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        return result;
    }

    /**
     * saveOrUpdateLostRecordNote:(添加/修改 流失备注). <br/>
     *
     * @param buyerNick
     * @param dateStr
     * @param orderId
     * @param note
     * @param lostType
     * @return
     * @since JDK 1.8
     */
    @RequestMapping("saveOrUpdateLostRecordNote")
    public Object saveOrUpdateLostRecordNote(@RequestParam(name = "shop") String shopStr,
                                             @RequestParam(name = "buyerNick") String buyerNick,
                                             @RequestParam(name = "dateStr") String dateStr,
                                             @RequestParam(name = "orderId", required = false) String orderId,
                                             @RequestParam(name = "noteId", required = false) String noteIdStr,
                                             @RequestParam(name = "note", required = false) String note,
                                             @RequestParam(name = "lostType") Integer lostType) {
        ApiResponse result = new ApiResponse();
        Date date;
        ShopCommonParam shop;
        Long noteId = null;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            if (StringUtils.isNotBlank(noteIdStr)) {
                noteId = JacksonUtils.json2pojo(noteIdStr, Long.class);
            }
            date = DateFormatUtils.parseYMd(dateStr);
        } catch (Exception e1) {
            logger.error(" date format :{}", e1.getMessage(), e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_20);
        }
        try {
            Long currentNoteId = shopDataAnalysisBussiness.saveOrUpdateLostRecordNote(shop, buyerNick, date,
                    noteId, note, lostType, orderId);
            if (currentNoteId == null) {
                return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_24);
            }
            Map<String, Object> data = new HashMap<>();
            data.put("noteId", currentNoteId);
            result.setData(data);
            result.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
            result.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_03_23);
        }
        return result;
    }

    @RequestMapping("/tradeFilter")
    @ResponseBody
    public Object searchTradeFilterLst(@RequestParam(name = "shopId") String shopId,
                                       @RequestParam(name = "groupId") String groupId,
                                       @RequestParam(name = "csNick") String csNick,
                                       @RequestParam(name = "buyerNick") String buyerNick,
                                       @RequestParam(name = "startDate") String startDate,
                                       @RequestParam(name = "endDate") String endDate,
                                       @RequestParam(name = "filterType") String filterType) {
        Date sDate;
        Date eDate;
        Map<String, Object> data = new HashMap<>();
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            logger.error(" date format :{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }

        UserShopQuery shop = getUserShopParam(shopId, groupId, csNick);
        try {
            List<DealAnalysisVo> dealAnalysisVoLst = shopDataAnalysisBussiness.searchTradeFilterLst(shop, sDate, eDate, buyerNick, true);
            data.put("dealAnalysisVoLst", dealAnalysisVoLst);
        } catch (Exception e) {
            logger.error("serach tradeFilterLst:{}", e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_02_01);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
    }


    @RequestMapping("/searchShopGoodsFeedbackRateLst")
    public Object searchShopGoodsFeedbackRateLst(
            @RequestParam(name = "shop") String shopStr,
            @RequestParam(name = "param") String paramStr,
            @RequestParam("sortPageQuery") String sortPageQueryStr) {

        ShopCommonParam shop;
        ShopGoodsRateParam param;
        SortPageQuery sortPageQuery;
        try {
            shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
            sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
            param = JacksonUtils.json2pojo(paramStr, ShopGoodsRateParam.class);
        } catch (Exception e1) {
            logger.error("json parse error:{}", e1.getMessage(), e1);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
        }
        try {
            return RestResponseTypeRef.ofSuccess(shopDataAnalysisBussiness.searchShopGoodsFeedbackRateLst(shop, param, sortPageQuery));
        } catch (Exception e) {
            logger.error("serach searchShopGoodsFeedbackRateLst error:{}", e.getMessage(), e);
            return RestResponseTypeRef.ofFail();
        }
    }

}
  
