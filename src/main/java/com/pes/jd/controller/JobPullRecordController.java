package com.pes.jd.controller;

import com.pes.jd.business.sub.JobRecordBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.JobPullRecordVO;
import com.pes.jd.ms.domain.Response.RestApiResponse;
import com.pes.jd.util.CommonDateUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: yuanxun
 * @Date: 2019-07-30 14:33
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/jobRecord/")
public class JobPullRecordController {
    private static final Logger logger = LoggerFactory.getLogger(JobPullRecordController.class);

    @Autowired
    private JobRecordBusiness jobRecordBusiness;

    @RequestMapping(value = "cleanJobPullRecord")
    public Object cleanJobPullRecord(
            @RequestParam("shopIds") String shopIds,
            @RequestParam("schemaId") String schemaId,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr) {
        logger.info("cleanJobPullRecord all store information according to db and schemaId .... shopId={},schemaId={}", shopIds, schemaId);
        RestApiResponse<Integer> restApiResponse = new RestApiResponse<>();
        //检验参数
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
            Assert.notNull(shopIds, " shopIds must be non null ");
            Assert.notNull(schemaId, " schemaId must be non null ");
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE);
        }

        List<Long> ids = Arrays.stream(shopIds.split(",")).map(s -> Long.valueOf(s)).collect(Collectors.toList());
        //判断shopIds 是否为空
        if (CollectionUtils.isEmpty(ids)) {
            logger.error(ApiCodeEnum.CODE_ERROR_CLEAN_JOB_PULL_RECORD.getMsg());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CLEAN_JOB_PULL_RECORD);
        }

        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if (dates.isEmpty()) {
            logger.error(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE.getMsg());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE);
        }

        try {

            Integer count = jobRecordBusiness.cleanJobPullRecord(ids, schemaId, dates);
            restApiResponse.setResult(count);
            restApiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            logger.error("cleanJobPullRecord  error:{}", e.getMessage(), e);
            restApiResponse.setSuccess(Boolean.FALSE);
            return restApiResponse;
        }
        return restApiResponse;
    }

    @RequestMapping(value = "searchJobPullRecord")
    public Object searchJobPullRecord(
            @RequestParam("shopIds") String shopIds,
            @RequestParam("schemaId") String schemaId,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr,
            @RequestParam("status") Integer status) {
        logger.info(" searchJobPullRecord all store information according to db and schemaId .... shopIds={}", shopIds);
        RestApiResponse<List<JobPullRecordVO>> restApiResponse = new RestApiResponse<>();
        //检验参数
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
            Assert.notNull(shopIds, " shopId must be non null ");
            Assert.notNull(schemaId, " schemaId must be non null ");
            Assert.notNull(status, " status must be non null ");
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE);
        }

        List<Long> ids = Arrays.stream(shopIds.split(",")).map(s -> Long.valueOf(s)).collect(Collectors.toList());
        //判断shopIds 是否为空
        if (CollectionUtils.isEmpty(ids)) {
            logger.error(ApiCodeEnum.CODE_ERROR_SEARCH_JOB_PULL_RECORD.getMsg());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_JOB_PULL_RECORD);
        }

        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if (dates.isEmpty()) {
            logger.error(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE.getMsg());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE);
        }

        try {
            List<JobPullRecordVO> jobRecordVOLst = jobRecordBusiness.searchJobPullRecord(ids, schemaId, dates, status);
            restApiResponse.setResult(jobRecordVOLst);
            restApiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            logger.error("searchJobPullRecord  error:{}", e.getMessage(), e);
            restApiResponse.setSuccess(Boolean.FALSE);
            return restApiResponse;
        }
        return restApiResponse;
    }

}
