package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.LoginBusiness;
import com.pes.jd.business.main.ShopBusiness;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DTO.ShopDBRelationDTO;
import com.pes.jd.model.DTO.ShopUserDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.JdSystemPageParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.AdminShopVO;
import com.pes.jd.model.VO.ShopDBRelationVO;
import com.pes.jd.model.VO.ShopVO;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import com.pes.jd.business.main.ShopDBRelationBusiness;

import javax.security.auth.login.LoginException;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/admin/*")
public class AdminController extends BaseController {

	private Logger logger = LoggerFactory.getLogger(AdminController.class);

	@Autowired
	private LoginBusiness loginBusiness;

	@Autowired
	private ShopBusiness shopBusiness;

	@Autowired
	private ShopDBRelationBusiness ShopDBRelationBusiness;

	@RequestMapping
	public String loginPage() {
		return "/dist/index";
	}


	@RequestMapping("testgit")
	@ResponseBody
	public ApiResponse testgit() {
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
	}


	@RequestMapping("login")
	@ResponseBody
	public ApiResponse login(HttpServletRequest request,
			@RequestParam(name = "username") String username,
			@RequestParam(name = "password") String password) {
		logger.info("request = " + request);
		ApiResponse apiResponse = new ApiResponse();
		Map<String,Object> map = Maps.newHashMap();
		apiResponse.setData(map);
		// 验证密码后，设置为管理员权限
		if (password == null || password.trim().length() == 0) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_01.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_01.getMsg());
			return apiResponse;
		}
		Long status = loginBusiness.loginValiadte(username, password);
		if (status<1) {
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_02.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_02.getMsg());
			return apiResponse;
		}
		apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
		apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
		map.put("status",status);
		return apiResponse;
	}

	// 根据nick或title模糊查询店铺
	@RequestMapping("queryShopByAdmin")
	@ResponseBody
	public ApiResponse getShopInfoByShopTitleOrUserNick(@RequestParam(value = "nick") String nick,@RequestParam(value = "type") String type) {
		ApiResponse apiResponse = new ApiResponse();
		HashMap<String, Object> retMap = new HashMap<>();
		try {
			if(StringUtils.isBlank(type)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_JZ_02_01);
			}
			if (StringUtils.isBlank(nick)) {
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_07);
			} else {
				List<AdminShopVO> shops = shopBusiness.selectShopByNickOrTitle(nick,type);
				retMap.put("retList", shops);
				apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
				apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
				apiResponse.setData(retMap);
				return apiResponse;
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("后台登录error", e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_04);
		}
	}

    @RequestMapping("queryAllShop")
    @ResponseBody
    public ApiResponse queryAllShop() {
        ApiResponse apiResponse = new ApiResponse();
        HashMap<String, Object> retMap = new HashMap<String, Object>();
        try {
                List<ShopVO> shops = shopBusiness.searchAllShop();
                retMap.put("retList", shops);
                apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
                apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
                apiResponse.setData(retMap);
                return apiResponse;
        } catch (Exception e) {
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_04);
        }
    }

	@RequestMapping("/sysLoginEntrance")
	@ResponseBody
	public ApiResponse sysLoginEntrance(HttpServletRequest request,@RequestParam(value = "shopId") String shopId) throws Exception {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, String> result = new HashMap<>(1);
		Map<String, Object> sessionMap = new HashMap<>();
		apiResponse.setData(sessionMap);
		try {
			System.out.println("request-ShopId:" + shopId);
			Map<String, String> loginResult = loginBusiness.adminLoginEntrance(shopId, sessionMap);
			if (CommonConstants.YES.equals(loginResult.get("result_code"))) {
				ShopUserDTO user = this.getCurrentUser();
				if (user != null) {
					System.out.println("request2-Username :" + user.getNick());
				}

				System.out.println("request-Login success");

				@SuppressWarnings("unchecked")
				List<String> userRoles = (List<String>) sessionMap.get("userRoles");
				boolean isAdminRole = false;
				if(CollectionUtils.isNotEmpty(userRoles)){
					for (String role : userRoles) {
						if (CommonConstants.ROLE_SYSTEM_ADMIN_NAME.equals(role)) {
							isAdminRole = true;
							break;
						}
					}
				}
				if (isAdminRole) {
					apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_06.getCode());
					apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_06.getMsg());
					return apiResponse;
				} else {
					apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
					apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
					return apiResponse;
				}
			} else {
				System.out.println("request-Login fail");
				result.put("errorMsg", loginResult.get("error_msg"));
			}
		} catch (LoginException e) {
			e.printStackTrace();
		} catch (Exception e) {
			e.printStackTrace();
		}
		System.out.println("request-Login fail");
		apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_HD_01_05.getCode());
		apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_HD_01_05.getMsg());
		return apiResponse;
	}


	// 根据nick或title模糊查询店铺
	@RequestMapping("queryJdSystemPage")
	@ResponseBody
	public ApiResponse queryJdSystemPage(String jdSystemPageParam) {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> retMap = new HashMap<>();
		try {
			Assert.notNull(jdSystemPageParam,"jdSystemPageParam is null");
		}catch (Exception e){
			logger.error("queryJdSystemPage 参数为空"+e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_01_02);
		}
		try {
			JdSystemPageParam jdSystemPageParam1 = JacksonUtils.json2pojo(jdSystemPageParam, JdSystemPageParam.class);
			if (null==jdSystemPageParam) {
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_07);
			} else {
				List<ShopVO> shops = shopBusiness.selectShopByDateByNickOrTitle(jdSystemPageParam1);
				retMap.put("retList", shops);
				apiResponse.setRpCode(ApiCodeEnum.CODE_SUCCESS_1001.getCode());
				apiResponse.setRpMsg(ApiCodeEnum.CODE_SUCCESS_1001.getMsg());
				apiResponse.setData(retMap);
				return apiResponse;
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("jd系统页后台登陆error,"+e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_14);
		}
	}


	@RequestMapping("queryEveryServer")
	@ResponseBody
	public ApiResponse queryEveryServer() {
		try {
			logger.info("开始->获取DBRelation");
			ShopDBRelationVO shopDBRelationVO = new ShopDBRelationVO();
			List<ShopDBRelationDTO> shopDBRelationDTOS = ShopDBRelationBusiness.searchShopDBNameAndSchemaIdByType(CommonConstants.JOB_TYPE_DAILY);
			if(CollectionUtils.isEmpty(shopDBRelationDTOS)){
				return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YW_01_02,shopDBRelationVO);
			}
			List<String> dbNameLst = shopDBRelationDTOS.stream().map(ShopDBRelationDTO::getDbName).sorted().collect(Collectors.toList());
			List<String> schemaIdLst = shopDBRelationDTOS.stream().map(ShopDBRelationDTO::getSchemaId).sorted().collect(Collectors.toList());
			shopDBRelationVO.setDbNames(dbNameLst);
			shopDBRelationVO.setSchemaIds(schemaIdLst);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, shopDBRelationVO);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("dbRealation 获取失败 --->error", e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_HD_01_07);
		}
	}
}
