package com.pes.jd.controller;

import com.pes.jd.business.main.ShopSettingBatchRemindCnoBussiness;
import com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.ms.domain.Data.master.ShopSettingBatchRemindCno;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月18 10:50:50<br>
 */
@RequestMapping("/setting/batch/remind/cno")
@RestController
public class ShopSettingBatchRemindConController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(ShopSettingBatchRemindConController.class);
    @Resource
    private ShopSettingBatchRemindCnoBussiness shopSettingBatchRemindCnoBussiness;

    @RequestMapping("/saveOrUpdateShopSettingBatchRemindCno")
    public Object saveOrUpdateShopSettingBatchRemindCno(@RequestParam("recordStr") String recordStr) {
        ShopSettingBatchRemindCnoDO record;
        try {
            record = JacksonUtils.json2pojo(recordStr, ShopSettingBatchRemindCnoDO.class);
        } catch (Exception e) {
            logger.info("master parse json error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_01_08, RestApiResponse2.of(false));
        }
        try {
            shopSettingBatchRemindCnoBussiness.saveOrUpdateShopSettingBatchRemindCno(record);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, RestApiResponse2.of());

        } catch (Exception e) {
            logger.error("master saveOrUpdateShopSettingBatchRemindCno error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CBR_01_01, RestApiResponse2.of(false));
        }

    }

    @RequestMapping("/selectShopSettingBatchRemindCnoByShopId")
    public Object selectShopSettingBatchRemindCnoByShopId(@RequestParam("shopId") Long shopId) {
        try {
            ShopSettingBatchRemindCno record = shopSettingBatchRemindCnoBussiness.selectShopSettingBatchRemindCnoByShopId(shopId);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(record));
        } catch (Exception e) {
            logger.error("master selectShopSettingBatchRemindCnoByShopId error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_CBR_01_02, RestApiResponse2.of(false));
        }

    }

}
