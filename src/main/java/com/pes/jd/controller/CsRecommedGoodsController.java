package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.sub.GoodsRecommendAnalysisBussiness;
import com.pes.jd.model.DTO.CsRecommendGoodsDTO;
import com.pes.jd.model.DTO.GoodsRecommendSummaryDTO;
import com.pes.jd.model.DTO.OrderDetailDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.GoodsRecommedParam;
import com.pes.jd.model.Param.OrderInfoLogUploadParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DataAnalysisVO;
import com.pes.jd.util.JacksonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("cs/recommed/goods")
public class CsRecommedGoodsController {

	private static final Logger logger = LoggerFactory.getLogger(CsRecommedGoodsController.class);
	@Autowired
	private GoodsRecommendAnalysisBussiness goodsRecommendAnalysisBussiness;

	/**
	 * 商品推荐汇总
	 *
	 */

	@RequestMapping("/selectGoodsRecommendSummary")
	public ApiResponse selectGoodsRecommendSummary(@RequestParam("shop") String shopStr,@RequestParam("param") String paramStr){
		Map<String, Object> result=Maps.newHashMap();
		ShopCommonParam shop=null;
		GoodsRecommedParam param=null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param=JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
		} catch (Exception e1) {

			logger.error("json parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
		List<GoodsRecommendSummaryDTO> goodsRecommendSummaryLst=	goodsRecommendAnalysisBussiness.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(shop, param.getCsNickLst(), param.getSkuLst(), param.getStartDate(), param.getEndDate());
		result.put("goodsRecommendSummaryLst", goodsRecommendSummaryLst);
		} catch (Exception e) {
			result.put("goodsRecommendSummaryLst", new ArrayList<GoodsRecommendSummaryDTO>(0));
			logger.error("sub selectGoodsRecommendSummary error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01,result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);

	}

	@RequestMapping("/selectGoodsRecommendSummaryV2")
	public ApiResponse selectGoodsRecommendSummaryV2(@RequestParam("shop") String shopStr, @RequestParam("param") String paramStr) {
		Map<String, Object> result = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsRecommedParam param = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
		} catch (Exception e1) {

			logger.error("json parse error:{}", e1.getMessage(), e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			List<GoodsRecommendSummaryDTO> goodsRecommendSummaryLst = goodsRecommendAnalysisBussiness.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2(shop, param.getCsNickLst(), param.getSkuLst(), param.getCategoryId(), param.getStartDate(), param.getEndDate());

			result.put("goodsRecommendSummaryLst", goodsRecommendSummaryLst);
		} catch (Exception e) {
			result.put("goodsRecommendSummaryLst", new ArrayList<GoodsRecommendSummaryDTO>(0));
			logger.error("sub selectGoodsRecommendSummary error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01, result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);

	}

	/**
	 * 商品推荐汇总——OfSpu
	 *
	 */

	@RequestMapping("/selectGoodsRecommendSummaryOfSpu")
	public ApiResponse selectGoodsRecommendSummaryOfSpu(@RequestParam("shop") String shopStr,@RequestParam("param") String paramStr){
		Map<String, Object> result=Maps.newHashMap();
		ShopCommonParam shop;
		GoodsRecommedParam param;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param=JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
		} catch (Exception e1) {

			logger.error("json parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
            List<GoodsRecommendSummaryDTO> goodsRecommendSummaryLst = goodsRecommendAnalysisBussiness.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickOfSpu(shop, param.getCsNickLst(), param.getSkuLst(), param.getStartDate(), param.getEndDate());
            result.put("goodsRecommendSummaryLst", goodsRecommendSummaryLst);
        } catch (Exception e) {
			result.put("goodsRecommendSummaryLst", new ArrayList<GoodsRecommendSummaryDTO>(0));
			logger.error("sub selectGoodsRecommendSummary OfSpu error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_01,result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);

	}

	/**
	 * 商品推荐分析
	 */
	@RequestMapping("/selectGoodsRecommendAnalysis")
	public ApiResponse selectGoodsRecommendAnalysis(@RequestParam("shop") String shopStr,@RequestParam("param") String paramStr){
		Map<String, Object> result=Maps.newHashMap();
		ShopCommonParam shop;
		GoodsRecommedParam param;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param=JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
		} catch (Exception e1) {
			logger.error("json parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
		List<GoodsRecommendSummaryDTO> goodsRecommendAnalysLst=	goodsRecommendAnalysisBussiness.selectGoodsRecommendSummaryByDateByShopIdByCsNick(shop, param);
		result.put("goodsRecommendAnalysLst", goodsRecommendAnalysLst);
		} catch (Exception e) {
			result.put("goodsRecommendAnalysLst", new ArrayList<GoodsRecommendSummaryDTO>(0));
			logger.error("sub selectGoodsRecommendSummary error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_02,result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);

	}

	/**
	 * 商品推荐分析 OfSpu
	 */
	@RequestMapping("/selectGoodsRecommendAnalysisOfSpu")
	public ApiResponse selectGoodsRecommendAnalysisOfSpu(@RequestParam("shop") String shopStr,@RequestParam("param") String paramStr){
		Map<String, Object> result=Maps.newHashMap();
		ShopCommonParam shop;
		GoodsRecommedParam param;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param=JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
		} catch (Exception e1) {
			logger.error("json parse OfSpu error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
		List<GoodsRecommendSummaryDTO> goodsRecommendAnalysLst=	goodsRecommendAnalysisBussiness.selectGoodsRecommendSummaryByDateByShopIdByCsNickOfSpu(shop, param);
		result.put("goodsRecommendAnalysLst", goodsRecommendAnalysLst);
		} catch (Exception e) {
			result.put("goodsRecommendAnalysLst", new ArrayList<GoodsRecommendSummaryDTO>(0));
			logger.error("sub selectGoodsRecommendSummary OfSpu error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_02,result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);

	}
	/**
	 * 商品明细
	 */
	@RequestMapping("/selectGoodsRecommendDetail")
	public ApiResponse selectGoodsRecommendDetail(@RequestParam("shop") String shopStr,@RequestParam("param") String paramStr,
			@RequestParam("sortPageQuery") String sortPageQueryStr){
		Map<String, Object> result = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsRecommedParam param = null;
		SortPageQuery sortPageQuery = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr ,SortPageQuery.class);
		} catch (Exception e1) {
			logger.error("json parse error:{}", e1.getMessage(), e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
			DataAnalysisVO<CsRecommendGoodsDTO> csRecommendDetailVO= goodsRecommendAnalysisBussiness.selectCsRecommendGoodsDetail(shop, param,sortPageQuery);
			result.put("csRecommendDetailVO", csRecommendDetailVO);
		} catch (Exception e) {
			result.put("csRecommendDetailVO", new ArrayList<CsRecommendGoodsDTO>(0));
			logger.error("sub: select cs goods recommend detail error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_03, result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);
	}

	@RequestMapping("/selectGoodsRecommendDetailV2")
	public ApiResponse selectGoodsRecommendDetailV2(@RequestParam("shop") String shopStr, @RequestParam("param") String paramStr,
													@RequestParam("sortPageQuery") String sortPageQueryStr) {
		Map<String, Object> result = Maps.newHashMap();
		ShopCommonParam shop = null;
		GoodsRecommedParam param = null;
		SortPageQuery sortPageQuery = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr, SortPageQuery.class);
		} catch (Exception e1) {
			logger.error("json parse error:{}", e1.getMessage(), e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {

			DataAnalysisVO<CsRecommendGoodsDTO> csRecommendDetailVO = goodsRecommendAnalysisBussiness.selectCsRecommendGoodsDetailV2(shop, param, sortPageQuery);
			result.put("csRecommendDetailVO", csRecommendDetailVO);
		} catch (Exception e) {
			result.put("csRecommendDetailVO", new ArrayList<CsRecommendGoodsDTO>(0));
			logger.error("sub: select cs goods recommend detail error:{}", e.getMessage(), e);
			e.printStackTrace();
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_03, result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, result);
	}

	/**
	 * 商品明细
	 */
	@RequestMapping("/selectGoodsRecommendDetailOfSpu")
	public ApiResponse selectGoodsRecommendDetailOfSpu(@RequestParam("shop") String shopStr,@RequestParam("param") String paramStr,
			@RequestParam("sortPageQuery") String sortPageQueryStr){
		Map<String, Object> result = Maps.newHashMap();
		ShopCommonParam shop;
		GoodsRecommedParam param;
		SortPageQuery sortPageQuery;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param = JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
			sortPageQuery = JacksonUtils.json2pojo(sortPageQueryStr ,SortPageQuery.class);
		} catch (Exception e1) {
			logger.error("json parse OfSpu error:{}", e1.getMessage(), e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
//			DataAnalysisVO<CsRecommendGoodsDTO> csRecommendDetailVO1= goodsRecommendAnalysisBussiness.selectCsRecommendGoodsDetail(shop, param,sortPageQuery);
			DataAnalysisVO<CsRecommendGoodsDTO> csRecommendDetailVO= goodsRecommendAnalysisBussiness.selectCsRecommendGoodsDetailOfSpu(shop, param,sortPageQuery);
			result.put("csRecommendDetailVO", csRecommendDetailVO);
		} catch (Exception e) {
			result.put("csRecommendDetailVO", new ArrayList<CsRecommendGoodsDTO>(0));
			logger.error("sub: select cs goods recommend detail OfSpu error:{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_03, result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);
	}


	/**
	 * 成交订单详情
	 */
	@RequestMapping("/selectDealOrderDetailLst")
	public ApiResponse selectDealOrderDetailLst(
			@RequestParam("shop") String shopStr,
			@RequestParam("param") String paramStr,
			@RequestParam("orderInfoLogUploadParam") String orderInfoLogUploadParamStr){
		Map<String, Object> result=Maps.newHashMap();
		ShopCommonParam shop=null;
		GoodsRecommedParam param=null;
		OrderInfoLogUploadParam orderInfoLogUploadParam = null;
		try {
			shop = JacksonUtils.json2pojo(shopStr, ShopCommonParam.class);
			param=JacksonUtils.json2pojo(paramStr, GoodsRecommedParam.class);
			orderInfoLogUploadParam=JacksonUtils.json2pojo(orderInfoLogUploadParamStr, OrderInfoLogUploadParam.class);
		} catch (Exception e1) {
			logger.error("json parse error:{}",e1.getMessage(),e1);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_01_05);
		}
		try {
		List<OrderDetailDTO> dealOrderDetailLst=	goodsRecommendAnalysisBussiness.selectDealOrderDetailLst(shop, param.getCustomer(), param.getSkuId(), param.getStartDate(),orderInfoLogUploadParam);
		result.put("dealOrderDetailLst", dealOrderDetailLst);
		} catch (Exception e) {
			result.put("dealOrderDetailLst", new ArrayList<OrderDetailDTO>(0));
			logger.error("sub selectDealOrderDetailLst error:{}",e.getMessage(),e);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SF_07_04,result);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,result);
	}
}
