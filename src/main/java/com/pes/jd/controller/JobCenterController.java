package com.pes.jd.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.pes.jd.business.*;
import com.pes.jd.config.PersistentRoutingDataSource;
import com.pes.jd.data.converter.OrderNotPayConverter;
import com.pes.jd.model.DO.ShopDayOverviewDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.AfterOrderParam;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.model.reponse.ApiResponse;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;

@SuppressWarnings("Duplicates")
@RestController
@RequestMapping("/handle/")
public class JobCenterController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(JobCenterController.class);

    @Resource
    private ShopManageBusiness shopManageBusiness;

    @Resource
    private JobPriorityTaskBusiness jobPriorityTaskBusiness;

    @Resource
    private DatabaseBusiness databaseBusiness;

    @Resource
    private OrderHandleBussiness orderHandleBussiness;

    @Resource
    private ShopCategoryAndGoodsBussiness shopCategoryAndGoodsBussiness;

    @Resource
    private OrderRefundHandleBussiness orderRefundHandleBussiness;

    @Resource
    private OrderBussiness orderBussiness;

    @Resource
    private OrderNotPayConverter orderNotPayConverter;

    @Resource
    private ShopOverviewBusiness shopOverviewBusiness;

    @Resource
    private CsPerformanceHandleBusiness csPerformanceHandleBusiness;

    @Resource
    private UserPortraitBusiness userPortraitBusiness;

    @Resource
    private JdAddressBusiness jdAddressBusiness;

    @Resource
    private UserPortraitAiReportBusiness userPortraitAiReportBusiness;

    /**
     * 拉取和计算店铺数据
     *
     * @param req
     * @return
     */
    @RequestMapping("pullAndCalShopData")
    public Object pullAndCalShopData(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }

        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDate = (String) jo.get("startDate");
        String endDate = (String) jo.get("endDate");
        String isYdStr = (String) jo.get("isYd");

        boolean isYd;
        if (Strings.isNullOrEmpty(isYdStr)) {
            isYd = false;
        }else {
            isYd = Boolean.valueOf(isYdStr);
        }
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);

        final Boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));

                jobPriorityTaskBusiness.pullAndCalShopData(jobShop, jobDate, isDelData,isYd);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", e.getMessage());
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * 计算店铺数据
     *
     * @param req
     * @return
     */
    @RequestMapping("calShopData")
    @ResponseBody
    public Object calShopData(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDate = (String) jo.get("startDate");
        String endDate = (String) jo.get("endDate");

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            retMap.put("msg", "时间格式化错误");
            return retMap;
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return retMap;
        }
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);

        boolean isDelData = true;
        try {
            for (int i = 0, size = dates.size(); i < size; i++) {
                Date date = dates.get(i);

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));

                jobPriorityTaskBusiness.calShopData(jobShop, jobDate, isDelData);
            }
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    /**
     * 店铺数据初始化
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "initShopData")
    @ResponseBody
    public Object initShopData(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.TRUE);// 手动拉取

        JobShopDTO shop = jobShop.getShop();
        if (shop == null) {
            logger.info("查询店铺为空");
            retMap.put("msg", "未查询到店铺");
            return retMap;
        }

        int initDataFlag = shop.getInitDataFlag();
        if (initDataFlag == 0) {
            boolean isDelData = false;

            try {
                Date yestoday = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(new Date(), -1));
                JobDateQuery jobDate = new JobDateQuery(yestoday);
                jobDate.setStartDate(yestoday);
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(yestoday));
                jobPriorityTaskBusiness.initShopData(jobShop, jobDate, isDelData);

            } catch (Exception e) {
                e.printStackTrace();
                logger.error(e.getMessage());
            }

        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * pullShopLoginLogData:(登陆记录拉取). <br/>
     *
     * @param
     * @return Object
     */
    @RequestMapping(value = "pullShopLoginLogData", method = RequestMethod.POST)
    @ResponseBody
    public Object pullShopLoginLogData(HttpServletRequest req) {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDate = (String) jo.get("startDate");
        String endDate = (String) jo.get("endDate");
        String dataTypeStr = (String) jo.get("dataType");

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        Date sDate = null;
        Date eDate = null;
        Integer dataType = null;
        try {
            if (StringUtils.isNoneBlank(dataTypeStr)) {
                dataType = Integer.parseInt(dataTypeStr);
            }
            sDate = DateUtil.getStartDateFromDateStr(startDate);
            eDate = DateUtil.getEndDateFromDateStr(endDate);
        } catch (ParseException e1) {
            e1.printStackTrace();
        }
        int shopIndex = 1;
        boolean isDelData = true;
        String type = "";
        try {

            JobDateQuery jobDate = new JobDateQuery(sDate);
            jobDate.setStartDate(sDate);
            jobDate.setEndDate(DateUtil.getEndTimeOfDate(eDate));

            jobPriorityTaskBusiness.pullShopLoginLogData(jobShop, jobDate, isDelData);
        } catch (Exception e) {
            e.printStackTrace();
            retMap.put("msg", "error");
            return retMap;
        }
        retMap.put("retCode", 0);
        return retMap;
    }

    /**
     * createShopAllTable:(每个店铺初始化一套表). <br/>
     *
     * @param
     * @param
     * @param
     * @return
     * @throws IOException
     * <AUTHOR>
     */
    @RequestMapping("createShopAllTable")
    @ResponseBody
    public Object createShopAllTable(HttpServletRequest req) {

        Map<String, Object> retMap = new HashMap<>();

        retMap.put("retCode", 1L);

        String body;
        try {
            body = getReqBodyStr(req);
        } catch (IOException e) {
            e.printStackTrace();
            retMap.put("msg", "get_req_body_fail");
            return retMap;
        }
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        try {
            databaseBusiness.initCreateShopAllTable(jobShop);
        } catch (Exception e) {
            e.printStackTrace();
        }
        retMap.put("retCode", 0);
        return retMap;
    }


    /**
     * @Description:(作用)
     * @param:@param shopId
     * @param:@param startDate
     * @param:@param endDate
     * @param:@return
     * @return:Object
     * @author:Lsp
     * @date:2018年11月8日
     * @version:V1.8
     */
    @RequestMapping("pullShopOrderData")
    public Object pullShopOrderData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                // 交易订单
                orderHandleBussiness.pullShopOrderInfo(jobShop, jobDate, true);

				// 增量订单
				orderHandleBussiness.pullShopIncrementOrder(jobShop, jobDate, true);
			}
		}catch (Exception e) {
			e.printStackTrace();
			return "拉取店铺订单失败";
		}
		return "拉取店铺订单成功";
	}


	/**
	 *
	 * @Description:(拉取订单的出库信息)
	 * @param:@param req
	 * @param:@return
	 * @return:Object
	 * @author:Lsp
	 * @date:2019年1月18日
	 * @version:V1.8
	 */
	@RequestMapping(value = "pullOrderOutStockTime")
	@ResponseBody
	public ApiResponse pullOrderOutStockTime(Long shopId,Integer colType,String sessionKey,String sellerNick,String rtSchemaId,String schemaId,Integer venderId, Long orderId, String orderCreatedTime) {
//		logger.info("task-dispatching pullOrderOutStockTime request start");
//		logger.info("venderId:{},orderId:{},orderCreatedTime:{}",venderId, orderId, orderCreatedTime);
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
//		logger.info("sessionKey:{}", shop.getSessionKey());
        try {
            jobPriorityTaskBusiness.pullOrderOutStockTime(shop, orderId, orderCreatedTime);
        } catch (Exception e) {
            logger.error("拉取订单的出库信息 error" + e.getMessage(), e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_OS_01_01);
        }
        return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
    }


    @RequestMapping("pullShopCategory")
    public Object pullShopCategory(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopCategory(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺类目失败";
        }
        return "拉取店铺类目成功";
    }


    @RequestMapping("pullShopSku")
    public Object pullShopSku(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopSku(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺sku失败";
        }
        return "拉取店铺sku成功";
    }


    @RequestMapping("pullShopGood")
    public Object pullShopGood(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取
        String db = jobShop.getShop().getDb();
        if(db.contains("1")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
        }
        if(db.contains("2")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
        }

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopGood(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺商品失败";
        }
        return "拉取店铺商品成功";
    }




    @RequestMapping("pullOrderRefundData")
    public Object pullOrderRefundData(String shopId, String startDate, String endDate) {

        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            for (Date date : dates) {

                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                orderRefundHandleBussiness.pullOrderRefundApplyData(jobShop, jobDate, true);
                orderRefundHandleBussiness.pullOrderRefundCheckData(jobShop, jobDate, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺退款数据失败";
        }
        return "拉取店铺退款数据成功";
    }

    @RequestMapping(value = "pullOrderCancel")
    @ResponseBody
    public ApiResponse pullOrderCancel(Long shopId, Integer colType, String sessionKey, String sellerNick, String schemaId, Integer venderId, Long orderId, String orderCreatedTime) {
//		logger.info("pullOrderCancel orderId:{}", orderId);
		JobShopDTO shop = new JobShopDTO();
		shop.setShopId(shopId);
		shop.setColType(colType);
		shop.setSessionKey(sessionKey);
		shop.setSellerNick(sellerNick);
		shop.setSchemaId(schemaId);
//		logger.info("sessionKey:{}", shop.getSessionKey());
        try {
			return jobPriorityTaskBusiness.pullOrderCancel(shop, orderId, orderCreatedTime);
        } catch (Exception e) {
			return getReturnInfo(false, "no search order", "-1");
        }
    }

    @RequestMapping(value = "pullOrderCreated")
    @ResponseBody
    public ApiResponse pullOrderCreate(Long shopId, Integer colType, String sessionKey, String sellerNick, String schemaId, Integer venderId, Long orderId, String orderCreatedTime) {
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
//        logger.info("pullOrderCreate=========JobShopDTO================{}",shop);
        try {
            int count = jobPriorityTaskBusiness.pullOrderCreate(shop, orderId, orderCreatedTime);
            if(count==0) {
            	  return getReturnInfo(false, "search order", "0");
            }
        } catch (Exception e) {
            logger.error("pullOrderCreate{}",e.getMessage());
            return getReturnInfo(false, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }

    @RequestMapping(value = "pullOrderPay")
    @ResponseBody
    public ApiResponse pullOrderPay(Long shopId,Integer colType,String sessionKey,String sellerNick,String schemaId,Integer venderId,Long orderId, String orderCreatedTime) {
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
        try {
        	 int count = jobPriorityTaskBusiness.pullOrderPay(shop, orderId, orderCreatedTime);
        	  if(count==0) {
            	  return getReturnInfo(false, "search order", "0");
            }

        } catch (Exception e) {
            logger.error("pullOrderPay{}",e.getMessage());
            return getReturnInfo(false, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }

    @RequestMapping(value = "pullOrderFinish")
    @ResponseBody
    public ApiResponse pullOrderFinish(Long shopId, Integer colType,String sessionKey,String sellerNick,String schemaId,Integer venderId, Long orderId, String orderCreatedTime) {
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);
        try {
        	 int count =jobPriorityTaskBusiness.pullOrderFinish(shop, orderId, orderCreatedTime);
            if(count==0) {
          	  return getReturnInfo(false, "search order", "0");
          }
        } catch (Exception e) {
            logger.error("pullOrderFinish{}",e.getMessage());
            return getReturnInfo(false, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }
    @RequestMapping(value = "pullOrderNotPay")
    @ResponseBody
    public  ApiResponse pullOrderNotPay(Long shopId, Integer colType, String sessionKey, String sellerNick, String schemaId, Integer venderId, Long orderId, String orderCreatedTime){
        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(shopId);
        shop.setColType(colType);
        shop.setSessionKey(sessionKey);
        shop.setSellerNick(sellerNick);
        shop.setSchemaId(schemaId);

        try {
            orderNotPayConverter.getOrderNotPay(shop,orderId);
        }catch (Exception e) {
            logger.error("pullOrderNotPay{}",e.getMessage());
            return getReturnInfo(true, "search order", "0");
        }
        return getReturnInfo(true, "search order", "0");
    }


    @RequestMapping(value = "/getAfterSaleOrder")
    public Object getAfterSaleOrder(@RequestParam(required = true) String afterOrderParam) {
//        System.out.println("getAfterSaleOrder come in");
        long l = System.currentTimeMillis();

        ApiResponse apiResponse = new ApiResponse();
        Map<String, Object> data = new HashMap<String, Object>();
        try {
            AfterOrderParam afterOrderParams = JacksonUtils.json2pojo(afterOrderParam, AfterOrderParam.class);
//            logger.info("getAfterOrderFromJob schemaId{}:", afterOrderParams.getSchemaId());
//            logger.info("getAfterOrderFromJob shopId{}:", afterOrderParams.getShopId());
            List<String> afterSaleBuyerLst = orderBussiness.selectShopOrderLstByBuyersAndDateForAfterSale(afterOrderParams.getShopId(),
                                                                                                             afterOrderParams.getSchemaId(),
                                                                                                             afterOrderParams.getBuyerLst(),
                                                                                                             afterOrderParams.getStartDate(),
                                                                                                             afterOrderParams.getEndDate());

            data.put("afterSaleBuyerLst", afterSaleBuyerLst);
            apiResponse.setData(data);
            apiResponse.setSuccess(Boolean.TRUE);
//            logger.info("getAfterSaleOrder shopId:{} sunccess", afterOrderParams.getShopId());
        } catch (Exception e) {
            apiResponse.setSuccess(Boolean.FALSE);
            apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_SF_06_01.getCode());
            apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_SF_06_01.getMsg());
            logger.error("getAfterSaleOrder error", e);
        }
//        System.out.println("getAfterSaleOrder consume end time :"+(System.currentTimeMillis() - l));
        return apiResponse;
    }


    private ApiResponse getReturnInfo(boolean successFlag, String msg, String code) {
		ApiResponse apiResponse = new ApiResponse();
		apiResponse.setSuccess(successFlag);
		apiResponse.setRpMsg(msg);
		apiResponse.setRpCode(code);
		return apiResponse;
	}


    @RequestMapping("pullShopCategoryV2")
    public Object pullShopCategoryV2(String shopId, String startDate, String endDate) {
        Date sDate;
        Date eDate;
        try {
            sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            eDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDate));
        } catch (ParseException e) {
            e.printStackTrace();
            return "时间格式化错误";
        }

        List<Date> dates = DateUtil.splitDate(sDate, eDate);
        if (dates.isEmpty()) {
            return "时间为空";
        }
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        jobShop.setShopIndex(1);
        jobShop.setHand(Boolean.FALSE);// 非手动拉取

        System.out.println("店铺id" + jobShop.getShop().getShopId() + "----" + "店铺名称" + jobShop.getShop().getSellerNick() + "----" + "店铺sessionkey:" + jobShop.getShop().getSessionKey());

        try {
            Date date = dates.get(0);
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                shopCategoryAndGoodsBussiness.pullShopCategoryV2(jobShop, jobDate, true);

        } catch (Exception e) {
            e.printStackTrace();
            return "拉取店铺类目V2失败";
        }
        return "拉取店铺类目V2成功";
    }


    @RequestMapping("getShopOvDay")
    public String getShopOvDay(String shopId, String startDate) throws Exception {
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);;
        Date sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
        ShopDayOverviewDO dayOverview = shopOverviewBusiness.getShopDayOverview(jobShop, sDate);
        return "ok";
    }

    @RequestMapping("getCsPerformance")
    public String getCsPerformance(String shopId, String startDate) throws Exception {
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);;
        Date sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
        JobDateQuery jobDate = new JobDateQuery();
        //【今日19号-算昨日：计算18号数据，下单-付款：计算17号数据，下单-出库：计算14号数据】
         Set<Date> dates = new LinkedHashSet<>();
         dates.add(cn.hutool.core.date.DateUtil.offsetDay(sDate, -5));
         dates.add(sDate);
        jobDate.setShopCsSaleAndOutStackDataSet(dates);

//        PerformanceRuleValidDateBO validDate = jobDate.getValidDate();
//        if (jobDate.getNeedCalFinalData()) {
//            jobDate.getCsPerformanceForToOrderCalTypeDateSet().add(new CalTypeDateBO(validDate.getToOrderedThenOutstackValidDate(), DataTypeEnum.TYPE_OUT_STACK));
//            jobDate.getCsPerformanceForToOrderCalTypeDateSet().add(new CalTypeDateBO(validDate.getToOrderedThenPayValidDate(), DataTypeEnum.TYPE_PAID));
//        }
//        jobDate.getCsPerformanceForToOrderCalTypeDateSet().add(new CalTypeDateBO(validDate.getDate(), DataTypeEnum.TYPE_ORDERED));

      //  csPerformanceHandleBusiness.handleShopCsPerformanceForToOrder(jobShop, jobDate, false);
        csPerformanceHandleBusiness.handleShopCsSaleAndOutStackData(jobShop, jobDate, false);
        return "ok";
    }

    //curl -X GET "http://localhost:9400/handle/handleUserPortrait?shopId=744873&sessionKey=db95ee586a62458b826d2cc48788adfdnwvl&startDate=2025-06-25&endDate=2025-06-25&schemaId=insight_01&dbName=db_02"

    @GetMapping("handleUserPortrait")
    public String handleUserPortrait(@RequestParam String shopId,
                                     @RequestParam String startDate,
                                     @RequestParam String endDate,
                                     @RequestParam String sessionKey,
                                      @RequestParam String schemaId,
                                      @RequestParam String dbName
                                    ) throws Exception {

        JobDateQuery jobDate = new JobDateQuery();
        Date sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
        Date eDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(endDate));
        jobDate.setStartDate(sDate);
        jobDate.setEndDate(eDate);
        JobShopQuery jobShop = new JobShopQuery();
        JobShopDTO shop = new JobShopDTO();
        shop.setSchemaId(schemaId);
        shop.setDb(dbName);
        shop.setShopId(Long.valueOf(shopId));
        shop.setSessionKey(sessionKey);
        jobShop.setShop(shop);
        String db = jobShop.getShop().getDb();

        if(db.contains("1")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
        }
        if(db.contains("2")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
        }
        userPortraitBusiness.handleUserPortraitStatistics(jobShop,jobDate,true);
        return "success";
    }


    @GetMapping("handleJdAdress")
    public String handleJdAdress(@RequestParam String shopId,@RequestParam String sessionKey,@RequestParam String schemaId, @RequestParam String dbName) throws Exception {

        JobShopQuery jobShop = new JobShopQuery();
        JobShopDTO shop = new JobShopDTO();
        shop.setSchemaId(schemaId);
        shop.setDb(dbName);
        shop.setShopId(Long.valueOf(shopId));
        shop.setSessionKey(sessionKey);
        jobShop.setShop(shop);
        String db = jobShop.getShop().getDb();
        if(db.contains("1")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
        }
        if(db.contains("2")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
        }
        jdAddressBusiness.handleJdAddress(jobShop,true);
        return "success";
    }


    @GetMapping("handleShopGood")
    public String handleShopGood(@RequestParam String shopId,
                                 @RequestParam String startDate,
                                 @RequestParam String endDate) throws Exception {

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        JobDateQuery jobDate = new JobDateQuery();
        Date sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
        Date eDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(endDate));
        jobDate.setStartDate(sDate);
        jobDate.setEndDate(eDate);

        String db = jobShop.getShop().getDb();
        if(db.contains("1")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
        }
        if(db.contains("2")){
            PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
        }
        long s5 = System.currentTimeMillis();
        shopCategoryAndGoodsBussiness.pullShopGood(jobShop, jobDate, true);
        long e5 = System.currentTimeMillis();
        logger.info("pop-job-invoke-api-" + shopId + "-f5-time={}ms - method[pullShopGood-api]", (e5 - s5));
        return "success";
    }


    /**
     * 手动触发用户画像AI分析报告生成
     */
    @PostMapping("generateAiReport")
    public ApiResponse generateAiReport(@RequestParam Long shopId,
                                        @RequestParam String startDate,
                                        @RequestParam String endDate,
                                        @RequestParam(defaultValue = "false") boolean isDelData) {
        try {
            JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
            JobDateQuery jobDate = new JobDateQuery();
            Date sDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDate));
            Date eDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(endDate));
            jobDate.setStartDate(sDate);
            jobDate.setEndDate(eDate);
            String db = jobShop.getShop().getDb();
            if(db.contains("1")){
                PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
            }
            if(db.contains("2")){
                PersistentRoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
            }
            userPortraitAiReportBusiness.handlePortraitAiAnalysis(jobShop, jobDate, isDelData);
            return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, "AI报告生成成功");
        } catch (Exception e) {
            logger.error("生成AI报告失败", e);
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_COMMON_DEFAULT, "生成AI报告失败: " + e.getMessage());
        }
    }



}
