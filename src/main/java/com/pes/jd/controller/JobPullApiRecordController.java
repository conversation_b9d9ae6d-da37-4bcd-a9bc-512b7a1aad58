package com.pes.jd.controller;

import com.pes.jd.business.sub.JobPullApiRecordBussiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.JobPullApiRecordVO;
import com.pes.jd.ms.domain.Response.RestApiResponse;
import com.pes.jd.util.CommonDateUtils;
import com.pes.jd.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 2019-07-30 14:33
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/jobPullApiRecord/")
public class JobPullApiRecordController {
    private static final Logger logger = LoggerFactory.getLogger(JobPullApiRecordController.class);

    @Autowired
    private JobPullApiRecordBussiness jobPullApiRecordBussiness;

    @RequestMapping(value = "searchJobPullApiRecordRecord")
    public Object searchJobPullApiRecordRecord(
            @RequestParam("shopId") Long shopId,
            @RequestParam("schemaId") String schemaId,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr,
            @RequestParam("type") Integer type) {
        logger.info(" searchJobPullApiRecordRecord all store information according  .... type={}", type);
        RestApiResponse<List<JobPullApiRecordVO>> restApiResponse = new RestApiResponse<>();
        //检验参数
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
            Assert.notNull(shopId, " shopId must be non null ");
            Assert.notNull(schemaId, " schemaId must be non null ");
            Assert.notNull(type, " type must be non null ");
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE);
        }

        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if (dates.isEmpty()) {
            logger.error(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE.getMsg());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE);
        }

        try {
            List<JobPullApiRecordVO> jobRecordVOLst = jobPullApiRecordBussiness.searchJobPullApiRecordRecord(shopId, schemaId, dates, type);
            restApiResponse.setResult(jobRecordVOLst);
            restApiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            logger.error("searchJobPullApiRecordRecord  error:{}", e.getMessage(), e);
            restApiResponse.setSuccess(Boolean.FALSE);
            return restApiResponse;
        }
        return restApiResponse;
    }

    @RequestMapping(value = "searchJobPullApiRecordRecordBySchemaId")
    public Object searchJobPullApiRecordRecordBySchemaId(
            @RequestParam("schemaId") String schemaId,
            @RequestParam("startDateStr") String startDateStr,
            @RequestParam("endDateStr") String endDateStr,
            @RequestParam("type") Integer type) {
        logger.info(" searchJobPullApiRecordRecord all store information according  .... type={}", type);
        RestApiResponse<List<JobPullApiRecordVO>> restApiResponse = new RestApiResponse<>();
        //检验参数
        Date startDate;
        Date endDate;
        try {
            startDate = CommonDateUtils.parseYMd(startDateStr);
            endDate = CommonDateUtils.parseYMd(endDateStr);
            Assert.notNull(schemaId, " schemaId must be non null ");
            Assert.notNull(type, " type must be non null ");
        } catch (Exception e) {
            logger.error(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE.getMsg(), e);
            e.printStackTrace();
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SEARCH_RECORD_DATE_PARSE);
        }

        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if (dates.isEmpty()) {
            logger.error(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE.getMsg());
            return ApiResponse.of(ApiCodeEnum.CODE_ERROR_CLEAN_RECORD_DATE_PARSE);
        }

        try {
            List<JobPullApiRecordVO> jobRecordVOLst = jobPullApiRecordBussiness.searchJobPullApiRecordRecordBySchemaId(schemaId, dates, type);
            restApiResponse.setResult(jobRecordVOLst);
            restApiResponse.setSuccess(Boolean.TRUE);
        } catch (Exception e) {
            logger.error("searchJobPullApiRecordRecordBySchemaId  error:{}", e.getMessage(), e);
            restApiResponse.setSuccess(Boolean.FALSE);
            return restApiResponse;
        }
        return restApiResponse;
    }
}
