package com.pes.jd.controller;

import com.pes.jd.business.main.JobBusiness;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/22 4:35 PM
 * @since 1.0.0
 */
@RestController
@RequestMapping("/job")
public class JobLogController {
    private static final Logger LOGGER = LoggerFactory.getLogger(JobLogController.class);
    @Autowired
    private JobBusiness jobBusiness;

    @RequestMapping("log")
    public Object log(Long shopId, String tDate, Integer status, Integer type, String shopConsumeDate) {
        try {
            return ApiResponse.of(
                    ApiCodeEnum.CODE_SUCCESS_1002, jobBusiness.logJob(shopId,
                            DateUtils.parseYMd(tDate), status, type, DateUtils.parseYMdHms(shopConsumeDate))
            );
        } catch (Exception e) {
            LOGGER.error("插入job结果失败", e);
            return ApiResponse.of(
                    ApiCodeEnum.CODE_ERROR_JOB_01_01, e.getMessage()
            );
        }
    }
}
