package com.pes.jd.controller;

import com.google.common.collect.Lists;
import com.pes.jd.business.main.DeptBusiness;
import com.pes.jd.business.main.DeptShopBusiness;
import com.pes.jd.business.main.DeptShopBussiness;
import com.pes.jd.business.main.JdAddressBusiness;
import com.pes.jd.model.DO.DeptShopDO;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DeptShopVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RequestMapping("/jdAddress")
@RestController
public class JdAddressController extends BaseController{
	private final Logger logger=LoggerFactory.getLogger(JdAddressController.class);

	@Resource
	private JdAddressBusiness jdAddressBusiness;


	@RequestMapping("/getAll")
	public RestApiResponse2 getAllJdAddressData(){
		ApiResponse apiResponse = new ApiResponse();
		try {
		List<JdAddress> jdAddressList = jdAddressBusiness.getAllJdAddressData();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(jdAddressList));
		} catch (Exception e) {
			logger.info("getAllJdAddressData error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/batchInsert")
	public RestApiResponse2 batchInsert(@RequestBody List<JdAddress> addressList){
		ApiResponse apiResponse = new ApiResponse();
		try {
			int insertCount = jdAddressBusiness.batchInsert(addressList);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(insertCount));
		} catch (Exception e) {
			logger.info("batchInsert error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/deleteAll")
	public RestApiResponse2 deleteAll(){
		ApiResponse apiResponse = new ApiResponse();
		try {
			int deleteCount = jdAddressBusiness.deleteAll();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(deleteCount));
		} catch (Exception e) {
			logger.info("deleteAll error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/getLatestCreateTime")
	public RestApiResponse2 getLatestCreateTime(){
		ApiResponse apiResponse = new ApiResponse();
		try {
			LocalDateTime latestCreateTime = jdAddressBusiness.getLatestCreateTime();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(latestCreateTime));
		} catch (Exception e) {
			logger.info("getLatestCreateTime error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}


}
