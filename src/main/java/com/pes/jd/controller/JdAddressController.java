package com.pes.jd.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.pes.jd.business.main.JdAddressBusiness;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;


@RequestMapping("/jdAddress")
@RestController
public class JdAddressController extends BaseController{
	private final Logger logger=LoggerFactory.getLogger(JdAddressController.class);

	@Resource
	private JdAddressBusiness jdAddressBusiness;


	@RequestMapping("/getAll")
	public RestApiResponse2 getAllJdAddressData(){
		ApiResponse apiResponse = new ApiResponse();
		try {
		List<JdAddress> jdAddressList = jdAddressBusiness.getAllJdAddressData();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(jdAddressList));
		} catch (Exception e) {
			logger.info("getAllJdAddressData error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/batchInsert")
	public RestApiResponse2 batchInsert(@RequestBody List<JdAddress> addressList){
		try {
			int insertCount = jdAddressBusiness.batchInsert(addressList);
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(insertCount));
		} catch (Exception e) {
			logger.info("batchInsert error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/deleteAll")
	public RestApiResponse2 deleteAll(){
		try {
			int deleteCount = jdAddressBusiness.deleteAll();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(deleteCount));
		} catch (Exception e) {
			logger.info("deleteAll error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	@RequestMapping("/getLatestCreateTime")
	public RestApiResponse2 getLatestCreateTime(){
		try {
			LocalDateTime latestCreateTime = jdAddressBusiness.getLatestCreateTime();
			if(latestCreateTime == null ){
				DateTime dateTime = DateUtil.offsetDay(new Date(), -10000);
				LocalDateTime localDateTime = DateUtil.toLocalDateTime(dateTime);
				// 转换为字符串格式返回
				String dateTimeStr = localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
				return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(dateTimeStr));
			}
			String dateTimeStr = latestCreateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(dateTimeStr));
		} catch (Exception e) {
			logger.info("getLatestCreateTime error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}


}
