package com.pes.jd.controller;

import com.google.common.collect.Lists;
import com.pes.jd.business.main.DeptBusiness;
import com.pes.jd.business.main.DeptShopBusiness;
import com.pes.jd.business.main.DeptShopBussiness;
import com.pes.jd.business.main.JdAddressBusiness;
import com.pes.jd.model.DO.DeptShopDO;
import com.pes.jd.model.DO.JdAddress;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.VO.DeptShopVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.checkerframework.checker.units.qual.A;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RequestMapping("/jdAddress")
@RestController
public class JdAddressController extends BaseController{
	private final Logger logger=LoggerFactory.getLogger(JdAddressController.class);

	@Resource
	private JdAddressBusiness jdAddressBusiness;


	@RequestMapping("/getAll")
	public RestApiResponse2 getAllJdAddressData(){
		ApiResponse apiResponse = new ApiResponse();
		try {
		List<JdAddress> jdAddressList = jdAddressBusiness.getAllJdAddressData();
			return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(jdAddressList));
		} catch (Exception e) {
			logger.info("getAllJdAddressData error:{}",e.getMessage(),e);
			return apiResponse(ApiCodeEnum.CODE_ERROR_JZ_02_01, RestApiResponse2.of(false));
		}
	}

	//batchInsert

	//deleteAll

	//getLatestCreateTime


}
