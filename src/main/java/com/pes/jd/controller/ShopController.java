package com.pes.jd.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.pes.jd.business.main.*;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.model.DO.ShopAccount;
import com.pes.jd.model.DO.ShopDetail;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.Param.ShopBoardMnoitorParam;
import com.pes.jd.model.Param.SubscribeParam;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Result.JobShopResult;
import com.pes.jd.ms.domain.Data.master.BatchRemindSetting;
import com.pes.jd.ms.domain.Response.RestResponseTypeRef;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**  
 * ClassName:ShopController <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午3:09:38 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@RestController
@RequestMapping("/shop/")
public class ShopController extends BaseController {
	private static final Logger logger = LoggerFactory.getLogger(ShopController.class);
	private static final String ACTIVE = "active";
	@Resource
	private UserBusiness userBusiness;
	@Resource
	private ShopBusiness shopBusiness;
	@Resource
	private DeptShopBusiness deptShopBusiness;
	@Resource
	private CsManageBusiness csManageBusiness;
	@Resource
	private ShopSubscribeBusiness shopSubscribeBusiness;
	@Resource
	private ShopPopBusiness shopPopBusiness;
	@Resource
	private ShopAccountBussiness shopAccountBussiness;
	@Resource
	private RedisCache redisCache;
	@Resource
	private ShopBatchRemindTaskBusiness shopBatchRemindTaskBusiness;

	/**
	 * getShopDetail:(查询店铺信息). <br/>
	 * Date:2018年11月7日下午1:27:52
	 *
	 * <AUTHOR>
	 * @param shopId
	 * @return
	 * @since JDK 1.8
	 */
	@RequestMapping("getShopDetail")
	public ApiResponse getShopDetail(@RequestParam(name="shopId")String shopId){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try{
			ShopDetail shopDetail = null;
			if(shopId!=null&&!"".equals(shopId)){
				shopDetail = shopBusiness.getShopDetailByShopId(Long.parseLong(shopId));
			}
			if(shopDetail!=null){
				retMap.put("shopDetail", shopDetail);
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_01);
			}
		}catch(Exception e){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			e.printStackTrace();
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	/**
	 * updateShopDetail:(修改店铺信息). <br/>
	 * Date:2018年11月7日下午1:40:33
	 *
	 * <AUTHOR>
	 * @param shopId
	 * @param firstName
	 * @param phone
	 * @param email
	 * @param contactQq
	 * @return
	 * @since JDK 1.8
	 */
	@RequestMapping("updateShopDetail")
	public ApiResponse updateShopDetail(@RequestParam(name="shopId")String shopId, @RequestParam("firstName")String firstName, @RequestParam("phone")String phone, @RequestParam("email")String email, @RequestParam("contactQq")String contactQq){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try{
			ShopDetail shopDetail = new ShopDetail();
			shopDetail.setShopId(Long.parseLong(shopId));
			shopDetail.setFirstName(firstName);
			shopDetail.setPhone(phone);
			shopDetail.setEmail(email);
			shopDetail.setContactQq(contactQq);
			int updateNum = shopBusiness.updateShopDetailByShopId(shopDetail);
			if(updateNum > 0){
				ShopDetail retShopDetail = shopBusiness.getShopDetailByShopId(Long.parseLong(shopId));
				retMap.put("shopDetail", retShopDetail);
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			}else{
				apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_03);
			}
		}catch(Exception e){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_04);
			e.printStackTrace();
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}



	@RequestMapping("visitcode/setting")
	public Object setVisitCodeForCurrentShop(String visitCode,String visitEmail,Integer flag){
		try {
			VisitFlag of = VisitFlag.of(flag);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1002);
		} catch (Exception e) {
			logger.error(e.getMessage());
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_VC_01_01);
		}
	}

	private enum VisitFlag {
		OPEN(1, "开启"), CLOSE(0, "关闭");
		private Integer seq;
		private String desc;

		VisitFlag(Integer seq, String desc) {
			this.seq = seq;
			this.desc = desc;
		}

		public Integer getSeq() {
			return seq;
		}

		public String getDesc() {
			return desc;
		}

		public final static Map<Integer,VisitFlag> INTEGER_VISIT_FLAG_MAP = Arrays.stream(VisitFlag.values()).collect(Collectors.toMap(k->k.getSeq(), v->v));

		public static VisitFlag of(Integer code){
			VisitFlag visitFlag = INTEGER_VISIT_FLAG_MAP.get(code);
			Assert.notNull(visitFlag," 访问码状态获取失败 ");
			return visitFlag;
		}

	}

	/**
	 * @Description:（获取店铺信息）
	 *
	 */

	@RequestMapping("getShopInfo")
	public ApiResponse getShopInfo(@RequestParam(name="shopId")String shopId){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try{
			ShopDTO shop = shopBusiness.selectShopByShopId(Long.valueOf(shopId));
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shop", shop);
		}catch(Exception e){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("get shop error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	/**
	 * 获取shop 通过venderId
	 * @param venderId
	 * @return
	 */
	@RequestMapping("getShopInfoForVenderId")
	public ApiResponse getShopInfoForVenderId(@RequestParam(name="venderId")String venderId){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try{
			ShopMsgDataDTO shop = shopBusiness.getShopInfoForVenderId(Long.valueOf(venderId));
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put(venderId, shop);
		}catch(Exception e){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("get shop error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	@RequestMapping("getRealTimeShopByShopId")
	public ApiResponse getRealTimeShopByShopId(@RequestParam(name="shopId")String shopId){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try{
			RealtimeShopDTO shop = shopBusiness.getRealTimeShopByShopId(Long.valueOf(shopId));
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shop", shop);
		}catch(Exception e){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_02_01);
			logger.error("get getRealTimeShopByShopId error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	@RequestMapping("getShopInfoForPlugin")
	public ApiResponse getShopInfoForPlugin(@RequestParam(name="shopName")String shopName){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		try{
			ShopDTO shop = shopBusiness.selectShopByShopNameorShopId(shopName);

			List<CsDTO> csLst =	csManageBusiness.selectCsByShopIdByTypeByCsStatus(shop.getShopId(), 1, null);

			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("csLst", csLst);
		}catch(Exception e){
			retMap.put("csLst", new ArrayList<>(0));
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("get getShopInfoForPlugin error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	@RequestMapping("getShopInfoByDeptId")
	public ApiResponse getShopInfoByDeptId(
			@RequestParam(name="deptId")String deptId,
			String queryParam,
			Integer currentPage,
			Integer size,
			@RequestParam(required = false, name="id")String id,
			@RequestParam(required = false,name="type")String type,
			@RequestParam(name="shopType",required=false) String shopType
	){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		if(null== shopType) {
			shopType = "0";
		}

		try{
			List<ShopBoardMnoitorParam> shopBoardMnoitorParams = deptShopBusiness.selectShopByDeptId(deptId,queryParam,currentPage,size,retMap,id,type,shopType);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shopBoardMnoitorParams", shopBoardMnoitorParams);
		}catch(Exception e){
			retMap.put("shopBoardMnoitorParams", new ArrayList<>(0));
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("getShopInfoByDeptId error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	@RequestMapping("getShopInfoByParam")
	public ApiResponse getShopInfoByParam(
			@RequestParam(name="deptId")String deptId,
			String queryParam,
			Integer currentPage,
			Integer size,
			@RequestParam(name="id")String id,
			@RequestParam(name="rtDb")String rtDb,
			@RequestParam(name="type")String type){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();


		try{
//			List<ShopBoardMnoitorParam> shopBoardMnoitorParams = deptShopBusiness.selectShopByDeptId(deptId,queryParam,currentPage,size,retMap,id,type);
			List<ShopBoardMnoitorParam> shopBoardMnoitorParams = deptShopBusiness.selectShopByDeptIdAndRtDb(deptId,queryParam,currentPage,size,retMap,id,type,rtDb);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shopBoardMnoitorParams", shopBoardMnoitorParams);
		}catch(Exception e){
			retMap.put("shopBoardMnoitorParams", new ArrayList<>(0));
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("getShopInfoByDeptId error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	@RequestMapping("getRtDbLstByDeptId")
	public ApiResponse getRtDbLstByDeptId(
			@RequestParam(name="deptId")String deptId,
			String queryParam,
			Integer currentPage,
			Integer size,
			@RequestParam(name="id")String id,
			@RequestParam(name="type")String type) {
		ApiResponse apiResponse;
		Map<String, Object> resultMap = null;
		try {
			resultMap = deptShopBusiness.selectRtDbLstAndTotalNum(deptId, queryParam, currentPage, size, id, type);
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
		} catch (Exception e) {
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("getRtDbLstByDeptId error:{}", e.getMessage(), e);
		}
		apiResponse.setData(resultMap);
		return apiResponse;
	}






	/**
	 * 查询多店铺下的 rtDb,rtDb,schemaId For 实时看板
	 * @param
	 */
	@RequestMapping("selectShopDBLstForRtBoard")
	public ApiResponse selectShopDBLstForRtBoard(
			@RequestParam(name="shopIdLst") String shopIdLstStr,
			@RequestParam(name="shopName",required=false) String shopParam){
		ApiResponse apiResponse;
		Map<String, Object> retMap = new HashMap<>();
		List<Long> shopIds = Lists.newArrayList();
		try{
			List<Long> shopIdLst = JSON.parseArray(shopIdLstStr,Long.class);
			Long shopId = null;
			String shopName = null;
			if(org.apache.commons.lang3.StringUtils.isNotEmpty(shopParam)){
				if(org.apache.commons.lang3.StringUtils.isNumeric(shopParam)){
					shopId = Long.valueOf(shopParam);
				}else{
					shopName = shopParam;
				}
			}
			List<ShopDTO> shopDTOS = shopBusiness.selectShopByShopNameorShopId(shopIdLst,shopName,shopId);
			for(ShopDTO shopDTO: shopDTOS){
				if(shopDTO!=null){
					shopIds.add(shopDTO.getShopId());
				}
			}
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001);
			retMap.put("shopLst", shopDTOS);
			retMap.put("shopIds", shopIds);
		}catch(Exception e){
			apiResponse = ApiResponse.of(ApiCodeEnum.CODE_ERROR_XS_05_02);
			logger.error("get shop error:{}",e.getMessage(),e);
		}
		apiResponse.setData(retMap);
		return apiResponse;
	}

	/**
	 * 查询订购明细
	 * @return
	 */
	@RequestMapping("/selectShopSubscribeInfo")
	public ApiResponse selectShopSubscribeInfo(@RequestParam("param") String paramStr) {
		Map<String, Object> data = new HashMap<>();
		try {
			SubscribeParam param = JSON.parseObject(paramStr, SubscribeParam.class);
			List<ShopSubScribeDTO> subscribeDTOs = shopSubscribeBusiness.selectShopSubscribeByShopIdLstByDate(param);
			logger.info("店铺订购信息"+subscribeDTOs.size()+"条");
			data.put("subLst", subscribeDTOs);
		} catch (Exception e) {
			logger.error("get shop subscribe error:{}",e.getMessage(),e);
			data.put("subLst", new ArrayList<>(0));
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SD_01_01,data);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);

	}

	/**
	 * 查询实时看板订购明细
	 * @return
	 */
	@RequestMapping("/selectShopSubscribeInfoForRt")
	public ApiResponse selectShopSubscribeInfoForRt(@RequestParam("param") String paramStr) {
		Map<String, Object> data = new HashMap<>();
		try {
			SubscribeParam param = JSON.parseObject(paramStr, SubscribeParam.class);
			data.put("subLst", shopSubscribeBusiness.selectShopSubscribeByShopIdLstByDateForRt(param));
		} catch (Exception e) {
			logger.error("get shop subscribe error:{}",e.getMessage(),e);
			data.put("subLst", new ArrayList<>(0));
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SD_01_01,data);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001,data);

	}


	/**
	 * 查询运营经理管理的店铺
	 * @return
	 */
	@RequestMapping("/selectShopPop")
	public ApiResponse selectShopPop(
			@RequestParam(name = "popName",required = false) String popName,
			@RequestParam(name = "shopName",required = false) String shopNameStr,
			@RequestParam(name = "shopType",required = false) Integer shopType) {
		Map<String, Object> data = new HashMap<>();
		try {
			Long shopId = null;
			String shopName = null;
			if (org.apache.commons.lang3.StringUtils.isNotEmpty(shopNameStr)) {
				if (org.apache.commons.lang3.StringUtils.isNumeric(shopNameStr)) {
					shopId = Long.valueOf(shopNameStr);
				} else {
					shopName = shopNameStr;
				}
			}
			List<ShopPopDTO> popShops = shopPopBusiness.selectAllPopShopByPopName(popName, shopId, shopName, shopType);
			data.put("popShops", popShops);
		} catch (Exception e) {
			logger.error("get shop pop error:{}", e.getMessage(), e);
			data.put("popShops", new ArrayList<>(0));
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_YY_01_01, data);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);

	}


	/**
	 * 获取店铺的敏感词
	 * @param shopId
	 * @return
	 */
	@RequestMapping(value = "getKeyWord")
	public Object getKeyWord(Long shopId) {
		logger.info("getKeyWord req come in shop:【{}】", shopId);
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {
			//查询系统默认敏感词校验
			//自定义的敏感词库
			data.put("keyWord", shopBusiness.getKeyWord(shopId));
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_MGC_01_01.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_MGC_01_01.getMsg());
			logger.error("get KeyWord--->：" + e.getMessage(), e);
		}
		return apiResponse;
	}


	/**
	 * 根据商铺ID查询商铺订购信息
	 * @return
	 */
	@RequestMapping("/getShopSubscribeInfoByShopId")
	public Object getShopSubscribeInfoByShopId(Long shopId) {
		try {
			return RestResponseTypeRef.ofSuccess(shopSubscribeBusiness.getShopSubscribeInfoByShopId(shopId));
		} catch (Exception e) {
			logger.error("get shop subscribe error:{}", e.getMessage(), e);
		}
		return RestResponseTypeRef.ofFail();

	}

	/**
	 * 根据用户nick查询用户角色
	 * @return
	 */
	@RequestMapping("user/selectUserRole")
	public ApiResponse selectUserRole(String userNick) {
		Map<String, Object> data = new HashMap<>();
		try {
			ShopAccount shopAccountByNick = shopAccountBussiness.getShopAccountByNick(userNick);
			Object role = shopAccountByNick != null ? shopAccountByNick.getRole() : CommonConstants.SUB_USER_TYPE_NO;
			logger.info("userNick =>{} Role is [{}]", userNick, role);
			data.put("role", role);
		} catch (Exception e) {
			logger.error("get shop user Role is error:{}", e.getMessage(), e);
			data.put("role", CommonConstants.SUB_USER_TYPE_NO);
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SD_01_01, data);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);

	}


	/**
	 * 根据店铺id集合,查询店铺
	 * @param shopIdLstStr
	 * @return
	 */
	@RequestMapping("/selectShopByIds")
	public ApiResponse selectShopByIds(@RequestParam("shopIdLst") String shopIdLstStr,@RequestParam("shopParam") String shopParam) {
		Map<String, Object> data = new HashMap<>();
		try {
			List<Long> shopIdLst = JSON.parseArray(shopIdLstStr, Long.class);
			List<ShopDTO> dtos = shopBusiness.selectShopByIds(shopIdLst, shopParam);
			data.put("subLst", dtos);
		} catch (Exception e) {
			logger.error("根据店铺id集合,查询店铺:{}", e.getMessage(), e);
			data.put("subLst", new ArrayList<>(0));
			return ApiResponse.of(ApiCodeEnum.CODE_ERROR_SD_01_01, data);
		}
		return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, data);
	}

	/**
	 * 根据店铺名称 模糊查询其所在的db和schemaID
	 * @param shopTitle
	 * @return
	 */
	@RequestMapping("/selectShopDbAndSchemaIdByShopTitle")
	public Object selectShopDbAndSchemaIdByShopTitle(
			@RequestParam("title") String shopTitle,
			@RequestParam("type") Integer type) {
		try {
			return RestResponseTypeRef.ofSuccess(shopBusiness.selectShopDbAndSchemaIdByShopTitle(shopTitle, type));
		} catch (Exception e) {
			logger.error("根据店铺名称 模糊查询其所在的db和schemaID 错误:{}", e.getMessage(), e);
			return RestResponseTypeRef.ofFail();
		}
	}

	/**
	 * 根据店铺id集合,查询店铺所在db
	 * @param shopIdLstStr
	 * @return
	 */
	@RequestMapping("/selectDbByShopIds")
	public Object selectDbByShopIds(@RequestParam("shopIdLst") String shopIdLstStr){
		Map<String, Object> data = new HashMap<>();
		try {
			List<Long> shopIdLst = JSON.parseArray(shopIdLstStr,Long.class);
			return RestResponseTypeRef.ofSuccess(shopBusiness.selectDbByShopIds(shopIdLst));
		} catch (Exception e) {
			logger.error("根据店铺id集合,查询店铺所在db:{}",e.getMessage(),e);
			data.put("subLst", new ArrayList<>(0));
			return RestResponseTypeRef.ofFail();
		}

	}
	@RequestMapping("/selectUrgeShopByShopIdLstByType")
	public Object selectUrgeShopByShopIdSetByType(
			@RequestParam("shopIdLst") String shopIdLstStr,
			@RequestParam("type") Integer type){
		try {
			List<Long> shopIdLst = JSON.parseArray(shopIdLstStr,Long.class);
			return RestResponseTypeRef.ofSuccess(shopBusiness.selectUrgeShopByShopIdLstByType(shopIdLst,type));
		} catch (Exception e) {
			logger.error("selectUrgeShopByShopIdLstByType error:{}",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}

	}

	/**
	 * 导购一期 -- 查询店铺状态信息
	 * @param shopType
	 * @return
	 */
	@RequestMapping("/queryShopInfoLst")
	public Object queryShopInfoLst(@RequestParam("shopType") Integer shopType){
		try {
			return RestResponseTypeRef.ofSuccess(shopBusiness.queryShopInfoLst(shopType));
		} catch (Exception e) {
			logger.error("导购一期 -- 查询店铺状态信息 错误！",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	@RequestMapping("updateInitFlag")
	public void updateInitFlag(@RequestParam("shopId") Long shopId){
		ShopDTO shopDTO = shopBusiness.selectShopByShopId(shopId);
		if(shopDTO != null){
			shopBusiness.updateInitFlag(shopId);
		}
	}
	@RequestMapping("/queryShopInfoList")
	public Object queryShopInfoList(@RequestParam("shopType") Integer shopType){
		try {
			return RestResponseTypeRef.ofSuccess(shopBusiness.queryShopInfoList(shopType));
		} catch (Exception e) {
			logger.error("查询店铺状态信息 错误！",e.getMessage(),e);
			return RestResponseTypeRef.ofFail();
		}
	}

	/* ******V15.dipatching 接口迁移 start***********/
	/**
	 * 拉取离线店铺数据
	 * @param shopId 店铺ID
	 * @return Object
	 */
	@RequestMapping(value = "getJobShop")
	public Object getJobShop(String shopId) {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		boolean isActive;
		try {
			JobShopResult jobShop;
			int start = 0;
			int end = 300;
			int randomNum = ThreadLocalRandom.current().nextInt(start, end + 1);
			JobShopDTO shop = shopBusiness.getJobShopInfoById(Long.valueOf(shopId));
			try {
				isActive = ACTIVE.equals(shop.getStatus());//未过期的店铺才加缓存
				//先从缓存中取，取不到到数据库拿 过期时间当天晚上
				String shopKey = CommonConstants.JOB_SHOP + shopId;
				String cslstKey = CommonConstants.JOB_CSLST + shopId;
				String subuserKey = CommonConstants.JOB_SUBUSER + shopId;
				String batchremindsettingKey = CommonConstants.JOB_BATCHREMINDSETTING + shopId;
				String syswordlstKey = CommonConstants.JOB_SYSWORDLST;
				String jobShopUserKey = CommonConstants.JOB_SHOP_USER + shopId;
				String jobShopRemindtask = CommonConstants.JOB_SHOP_REMINDTASK + shopId;
				String jobShopStr;
				String cslstStr;
				String subuserStr;
				String batchremindsettingStr;
				String syswordlstStr;
				String jobShopUserStr;
				String jobShopRemindtaskStr;
				try {
					jobShopStr = redisCache.get(shopKey, 0);
					cslstStr = redisCache.get(cslstKey, 0);
					subuserStr = redisCache.get(subuserKey, 0);
					batchremindsettingStr = redisCache.get(batchremindsettingKey, 0);
					syswordlstStr = redisCache.get(syswordlstKey, 0);
					jobShopUserStr = redisCache.get(jobShopUserKey, 0);
					jobShopRemindtaskStr = redisCache.get(jobShopRemindtask, 0);
				} catch (Exception ignore) {
					logger.error(ignore.getMessage(), ignore);
					jobShopStr = "";
					cslstStr = "";
					subuserStr = "";
					batchremindsettingStr = "";
					syswordlstStr = "";
					jobShopUserStr = "";
					jobShopRemindtaskStr = "";
				}
				if (StringUtils.isEmpty(jobShopStr)) {
					logger.info("getJobShop req come in shop:{} 在数据库中取数据 店铺相关信息 涉及表={pes_shop,pes_shop_systemsetting,pes_buyernick_filter,pes_goods_filter,pes_shop_setting_batch_remind,pes_shop_remind_word,pes_shop_remind_word_goods,pes_shop_auto_allocated_setting,pes_urge_shop,pes_shop_sms_setting,pes_shop_sms_word,pes_shop_remind_blacklist,pes_shop_sms_service,pes_shop_setting_batch_remind_cno,pes_shop_auto_appointment_allocated_setting,pes_shop_sensitive_word,pes_shop_auto_advance_allocated_setting}", shopId);
					jobShop = shopBusiness.getJobShop(Long.valueOf(shopId));
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(shopKey, JSONObject.toJSONString(jobShop), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
						logger.info("店铺相关信息    缓存的过期时间为：{}", DateFormatUtils.formatYMdHms(new Date(System.currentTimeMillis() + randomNum * 1000)));
					}
				} else {
					logger.info("getJobShop req come in shop:{} 在缓存中取数据 店铺相关信息 涉及表={pes_shop,pes_shop_systemsetting,pes_buyernick_filter,pes_goods_filter,pes_shop_setting_batch_remind,pes_shop_remind_word,pes_shop_remind_word_goods,pes_shop_auto_allocated_setting,pes_urge_shop,pes_shop_sms_setting,pes_shop_sms_word,pes_shop_sms_backlist,pes_shop_sms_service,pes_shop_setting_batch_remind_cno,pes_shop_auto_appointment_allocated_setting,pes_shop_sensitive_word,pes_shop_auto_advance_allocated_setting}", shopId);
					jobShop = JSONObject.parseObject(jobShopStr, new TypeReference<JobShopResult>() {
					});
				}
//                获取需要的字段
				List<CsDTO> csLst;
				List<ShopAccountDTO> shopSubUserLst;
				BatchRemindSetting batchRemindSetting;
				List<String> sysWordLst;
				if (StringUtils.isEmpty(cslstStr)) {
					csLst = shopBusiness.getCsLst(Long.valueOf(shopId));
					logger.info("getJobShop req come in shop:{} 在数据库中取数据 客服信息 涉及表={pes_cs}", shopId);
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(cslstKey, JSONObject.toJSONString(csLst), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
						logger.info("客服信息    缓存的过期时间为：{}", DateFormatUtils.formatYMdHms(new Date(System.currentTimeMillis() + randomNum * 1000)));
					}
				} else {
					logger.info("getJobShop req come in shop:{} 在缓存中取数据 客服信息 涉及表={pes_cs}", shopId);
					csLst = JSONObject.parseArray(cslstStr, CsDTO.class);
				}
				jobShop.setCsLst(csLst);
				if (StringUtils.isEmpty(subuserStr)) {
					shopSubUserLst = shopBusiness.getShopSubUserLst(Long.valueOf(shopId));
					logger.info("getJobShop req come in shop:{} 在数据库中取数据 店铺子账户 涉及表={pes_shop_account}", shopId);
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(subuserKey, JSONObject.toJSONString(shopSubUserLst), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
						logger.info("店铺子账户    缓存的过期时间为：{}", DateFormatUtils.formatYMdHms(new Date(endTimeOfDate.getTime() + randomNum * 1000)));
					}
				} else {
					logger.info("getJobShop req come in shop:{} 在缓存中取数据 店铺子账户 涉及表={pes_shop_account}", shopId);
					shopSubUserLst = JSONObject.parseArray(subuserStr, ShopAccountDTO.class);
				}
				jobShop.setShopSubUserLst(shopSubUserLst);
				if (StringUtils.isEmpty(batchremindsettingStr)) {
					batchRemindSetting = shopBusiness.getBatchRemindSetting(Long.valueOf(shopId), jobShop.getShop());
					logger.info("getJobShop req come in shop:{} 在数据库中取数据 催付设置 涉及表={pes_shop_setting_batch_remind,pes_shop_remind_word,pes_shop_remind_word_goods,pes_shop_setting_batch_remind_presale,pes_shop_setting_batch_remind_reserve,pes_shop_setting_batch_remind_cno}", shopId);
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(batchremindsettingKey, JSONObject.toJSONString(batchRemindSetting), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
						logger.info("催付设置    缓存的过期时间为：{}", DateFormatUtils.formatYMdHms(new Date(endTimeOfDate.getTime() + randomNum * 1000)));
					}
				} else {
					logger.info("getJobShop req come in shop:{} 在缓存中取数据 催付设置 涉及表={pes_shop_setting_batch_remind,pes_shop_remind_word,pes_shop_remind_word_goods,pes_shop_setting_batch_remind_presale,pes_shop_setting_batch_remind_reserve,pes_shop_setting_batch_remind_cno}", shopId);
					batchRemindSetting = JSONObject.parseObject(batchremindsettingStr, new TypeReference<BatchRemindSetting>() {
					});
				}
				jobShop.setBatchRemindSetting(batchRemindSetting);
				if (StringUtils.isEmpty(syswordlstStr)) {
					sysWordLst = shopBusiness.getSysWordLst();
					logger.info("getJobShop req come in shop:{}  在数据库中取数据 sysWordLst 敏感词 涉及表={pes_keyword_sensitive}", shopId);
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(syswordlstKey, JSONObject.toJSONString(sysWordLst), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
					}
				} else {
					logger.info("getJobShop req come in shop:{}  在缓存中取数据 sysWordLst 敏感词 涉及表={pes_keyword_sensitive}", shopId);
					sysWordLst = JSONObject.parseArray(syswordlstStr, String.class);
				}
				jobShop.setSysWordLst(sysWordLst);

				List<ShopUserDTO> shopUser;
				if (StringUtils.isEmpty(jobShopUserStr)) {
					shopUser = userBusiness.selectUserByshopId(shopId);
					logger.info("getJobShop req come in shop:{}  在数据库中取数据 shopUser 用户信息 涉及表={pes_user}", shopId);
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(jobShopUserKey, JSONObject.toJSONString(shopUser), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
					}
				} else {
					logger.info("getJobShop req come in shop:{}  在缓存中取数据 shopUser 用户信息 涉及表={pes_user}", shopId);
					shopUser = JSONObject.parseArray(jobShopUserStr, ShopUserDTO.class);
				}
				jobShop.setShopUser(shopUser);
				List<ShopBatchRemindTaskDTO> shopBatchRemindTaskLst;
				if (StringUtils.isEmpty(jobShopRemindtaskStr)) {
					shopBatchRemindTaskLst = shopBatchRemindTaskBusiness.queryLstByShopId(Long.valueOf(shopId));
					//bug2781 定时的任务更改状态
					Date thisDate = new Date();
					shopBatchRemindTaskLst.stream().filter(t ->
									null != t.getIsPermanent()
									&& t.getIsPermanent() == 0
									&& null != t.getIsRemind()
									&& t.getIsRemind() == 2
							).forEach(t -> {
						//判断时间范围
						Date taskStartTime = t.getTaskStartTime();
						Date taskEndTime = t.getTaskEndTime();
						if(thisDate.compareTo(taskStartTime) > 0
								&& thisDate.compareTo(taskEndTime) < 0){
							t.setIsRemind(1);
							shopBatchRemindTaskBusiness.updateIsRemind(t.getShopId(), t.getId(), 1);
						}
					});
					logger.info("getJobShop req come in shop:{}  在数据库中取数据 pes_shop_batch_remind_task 用户信息 涉及表={pes_shop_batch_remind_task}", shopId);
					if (isActive) {
						Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
						redisCache.set(jobShopRemindtask, JSONObject.toJSONString(shopBatchRemindTaskLst), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
					}
				} else {
					logger.info("getJobShop req come in shop:{}  在缓存中取数据 pes_shop_batch_remind_task 用户信息 涉及表={pes_shop_batch_remind_task}", shopId);
					shopBatchRemindTaskLst = JSONObject.parseArray(jobShopRemindtaskStr, ShopBatchRemindTaskDTO.class);
				}
				List<ShopBatchRemindTaskDTO> useTask = Lists.newArrayList();
				if (CollUtil.isNotEmpty(shopBatchRemindTaskLst)) {
					for (ShopBatchRemindTaskDTO dto : shopBatchRemindTaskLst) {
						if(dto.getSendType() == 0){
							jobShop.setCreatedTask(true);
							continue;
						}
						useTask.add(dto);
					}
				} else {
					jobShop.setCreatedTask(false);
				}
				jobShop.setShopBatchRemindTaskList(useTask);
			} catch (Exception e) {
				logger.error("===>dispatcher getShop error,shopId:{} ", shopId, e);
				apiResponse.setSuccess(Boolean.FALSE);
				apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_06_13.getCode());
				apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_06_13.getMsg());
				return apiResponse;
			}
			data.put("smsNum", jobShop.getSmsNum());
			data.put("sysWordLst", jobShop.getSysWordLst());
			data.put("customWordLst", jobShop.getCustomWordLst());
			data.put("interfaceControlLst", jobShop.getInterfaceControlLst());
			data.put("shop", shop);
			data.put("csLst", jobShop.getCsLst());
			data.put("shopSystemsetting", jobShop.getShopSystemsetting());
			data.put("shopSubUserLst", jobShop.getShopSubUserLst());
			data.put("buyerFilterLst", jobShop.getBuyerFilterLst());
			data.put("goodFilterLst", jobShop.getGoodFilterLst());
			data.put("shopSettingBatchRemind", jobShop.getShopSettingBatchRemind());
			data.put("shopAutoAllocatedSetting", jobShop.getShopAutoAllocatedSetting());
			data.put("shopUrge", jobShop.getShopUrge());
			data.put("shopSmsSetting", jobShop.getShopSmsSetting());
			data.put("shopSmsWordLst", jobShop.getShopSmsWordLst());
			data.put("shopRemindBlacklistList", jobShop.getShopRemindBlacklistList());
			data.put("shopSettingBatchRemindCno", jobShop.getShopSettingBatchRemindCno());
			data.put("batchRemindSetting", jobShop.getBatchRemindSetting());
			data.put("shopAutoAdvanceAllocatedSetting", jobShop.getShopAutoAdvanceAllocatedSetting());
			data.put("shopAutoAppointmentAllocatedSetting", jobShop.getShopAutoAppointmentAllocatedSetting());
			data.put("shopUser", jobShop.getShopUser());
			data.put("shopBatchRemindTaskList", jobShop.getShopBatchRemindTaskList());
			data.put("isCreatedTask", jobShop.isCreatedTask());
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_06_13.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_06_13.getMsg());
			logger.error("===>dispatcher getJobShop info error：{},shopId:{} ", e.getMessage(), shopId, e);
		}
		return apiResponse;
	}

	/**
	 * 拉取离线店铺数据
	 *
	 * @return Object
	 */
	@RequestMapping(value = "getAllActiveJobShop")
	public Object getAllActiveJobShop(@RequestParam(value = "shopType", required = false) String shopType) {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		Integer shopTyepInteger = new Integer("0");
		try {
			if (StringUtils.isNotBlank(shopType) || NumberUtils.isNumber(shopType)) {
				shopTyepInteger = new Integer(shopType);
			}
			List<JobShopResult> allActiveJobShop = shopBusiness.getAllActiveJobShop(shopTyepInteger);
			data.put("allActiveJobShop", allActiveJobShop);
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_06_13.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_06_13.getMsg());
			logger.error("get jobShop：" + e.getMessage(), e);
		}
		return apiResponse;
	}


	@RequestMapping(value = "selectActiveShopLst")
	public Object selectActiveShopLst() {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {

			List<JobShopDTO> allActiveJobShop = shopBusiness.selectActiveShopLst();
			data.put("activeShopLst", allActiveJobShop);
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_06_13.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_06_13.getMsg());
			logger.error("get jobShop：" + e.getMessage(), e);
		}
		return apiResponse;
	}


	@RequestMapping(value = "getShopSplitKeyInfo")
	public Object getShopSplitKeyInfo(@RequestParam("shopId") Long shopId) {

		ShopSplitKeyDTO key = shopBusiness.getShopSplitKeyInfo(Long.valueOf(shopId));
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {

			List<JobShopDTO> allActiveJobShop = shopBusiness.selectActiveShopLst();
			data.put("shopSplitKeyDTO", key);
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_06_13.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_06_13.getMsg());
			logger.error("get jobShop：" + e.getMessage(), e);
		}
		return apiResponse;
	}


	/**
	 * 拉取离线店铺数据
	 *
	 * @return Object
	 */
	@RequestMapping(value = "selectShopCsNickLst")
	public Object selectShopCsNickLst(
			@RequestParam("shopId") Long shopId,
			@RequestParam("groupId") String groupId) {


		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {
			List<String> csNickLst = shopBusiness.selectShopCsNickLst(shopId, groupId);
			data.put("csNickLst", csNickLst);
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_01_01.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_01_01.getMsg());
			logger.error("selectShopCsNickLst：{}", e.getMessage(), e);
		}
		return apiResponse;
	}

	@RequestMapping(value = "/getShopSplitByShopId")
	public Object getShopSplitByShopId(
			@RequestParam("shopId") Long shopId) {
		ApiResponse apiResponse = new ApiResponse();
		Map<String, Object> data = new HashMap<>();
		try {
			JobShopResult jobShop;
			int start = 0;
			int end = 300;
			int randomNum = ThreadLocalRandom.current().nextInt(start, end + 1);
			String shopKey = CommonConstants.JOB_SHOP + shopId;
			String jobShopStr;
			try {
				jobShopStr = redisCache.get(shopKey, 0);
			} catch (Exception ignore) {
				logger.error(ignore.getMessage(), ignore);
				jobShopStr = "";
			}
			if (StringUtils.isEmpty(jobShopStr)) {
				logger.info("getShopSplitByShopId req come in shop:{} 在数据库中取数据 店铺相关信息 涉及表={pes_shop,pes_shop_systemsetting,pes_buyernick_filter,pes_goods_filter,pes_shop_setting_batch_remind,pes_shop_remind_word,pes_shop_remind_word_goods,pes_shop_auto_allocated_setting,pes_urge_shop,pes_shop_sms_setting,pes_shop_sms_word,pes_shop_sms_backlist,pes_shop_sms_service,pes_shop_setting_batch_remind_cno,pes_shop_auto_appointment_allocated_setting,pes_shop_sensitive_word,pes_shop_auto_advance_allocated_setting}", shopId);
				jobShop = shopBusiness.getJobShop(shopId);
				Date endTimeOfDate = DateFormatUtils.getEndTimeOfDate(new Date());
				redisCache.set(shopKey, JSONObject.toJSONString(jobShop), ((endTimeOfDate.getTime() - System.currentTimeMillis()) / 1000L) + randomNum, 0);
				logger.info("getShopSplitByShopId 店铺相关信息    缓存的过期时间为：{}", DateFormatUtils.formatYMdHms(new Date(System.currentTimeMillis() + randomNum * 1000)));
			} else {
				logger.info("getShopSplitByShopId req come in shop:{} 在缓存中取数据 店铺相关信息 涉及表={pes_shop,pes_shop_systemsetting,pes_buyernick_filter,pes_goods_filter,pes_shop_setting_batch_remind,pes_shop_remind_word,pes_shop_remind_word_goods,pes_shop_auto_allocated_setting,pes_urge_shop,pes_shop_sms_setting,pes_shop_sms_word,pes_shop_sms_backlist,pes_shop_sms_service,pes_shop_setting_batch_remind_cno,pes_shop_auto_appointment_allocated_setting,pes_shop_sensitive_word,pes_shop_auto_advance_allocated_setting}", shopId);
				jobShop = JSONObject.parseObject(jobShopStr, new TypeReference<JobShopResult>() {
				});
			}
			data.put("shop", jobShop.getShop());
			apiResponse.setData(data);
			apiResponse.setSuccess(Boolean.TRUE);
		} catch (Exception e) {
			apiResponse.setSuccess(Boolean.FALSE);
			apiResponse.setRpCode(ApiCodeEnum.CODE_ERROR_XS_01_01.getCode());
			apiResponse.setRpMsg(ApiCodeEnum.CODE_ERROR_XS_01_01.getMsg());
			logger.error("getShopSplitByShopId：{}", e.getMessage(), e);
		}
		return apiResponse;
	}
	/* ******V15.dipatching 接口迁移   end***********/

	@RequestMapping("/provideShopInfo")
	public Object provideShopInfo(@RequestParam("shopIds") String shopIds){
		try{
			if(StringUtils.isBlank(shopIds))
				return null;
			List<Long> finalShopIdList = Lists.newArrayList();
			Arrays.asList(shopIds.split(",")).forEach(shopId -> finalShopIdList.add(Long.valueOf(shopId)));
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, shopBusiness.listShopInfoByShopIds(finalShopIdList));
		}catch (Exception e){
			logger.info("provideShopInfo error :{}", e.getMessage(), e);
			return ApiResponse.of(ApiCodeEnum.CODE_SUCCESS_1001, new ArrayList<>());
		}
	}

}
  
