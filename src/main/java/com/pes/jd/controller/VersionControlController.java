package com.pes.jd.controller;

import com.google.common.collect.Maps;
import com.pes.jd.business.main.PesAppVersionMenuBusiness;
import com.pes.jd.business.main.SysetingAppVersionBusiness;
import com.pes.jd.business.main.UserVersionControlBusiness;
import com.pes.jd.model.DTO.SysettingAppVersionDTO;
import com.pes.jd.model.Enum.ApiCodeEnum;
import com.pes.jd.model.VO.PesAppVersionMenuVO;
import com.pes.jd.ms.domain.Response.RestApiResponse2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: ms-pes-jd
 * @description:
 * @author: ALan
 * @create: 2019-05-19
 */
@RequestMapping("/sys/versionControl")
@RestController
public class VersionControlController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(VersionControlController.class);
    @Autowired
    public PesAppVersionMenuBusiness pesAppVersionMenuBusiness;

    @Autowired
    private SysetingAppVersionBusiness sysetingAppVersionBusiness;

    @Autowired
    private UserVersionControlBusiness userVersionControlBusiness;

    @RequestMapping("searchMenuForLst")
    public Object searchMenuForLst(@RequestParam(name = "versionIdType") Long versionIdType,Integer type) {
        Assert.notNull(versionIdType, "versionIdType not be null!");
        try {
            Map<String,List<PesAppVersionMenuVO>> menuLst= Maps.newHashMap();

            if(type == 0){
                type = null;
            }
            menuLst.put("menuLst",pesAppVersionMenuBusiness.selectAppVersionMenuByVersionId(versionIdType,type));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(menuLst));
        } catch (Exception e) {
            logger.error(" searchMenuForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_03, new RestApiResponse2<>(false));
        }
    }

    @RequestMapping("batchAppVersionForLst")
    public Object batchAppVersionForLst() {
        try {
            Map<String,List<SysettingAppVersionDTO>> versionLst= Maps.newHashMap();
            versionLst.put("versionLst",sysetingAppVersionBusiness.batchAppVersionForLst());
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(versionLst));
        } catch (Exception e) {
            logger.error(" searchMenuForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_03, new RestApiResponse2<>(false));
        }
    }

    @RequestMapping("selectFunctionPointByShopIdForLst")
    public Object selectFunctionPointByShopIdForLst(@RequestParam(name = "shopId")Long shopId) {
        try {
            Map<String,List<String>> versionLst= Maps.newHashMap();
            versionLst.put("functionPoint",pesAppVersionMenuBusiness.selectFunctionPointByShopIdForLst(shopId));
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(versionLst));
        } catch (Exception e) {
            logger.error(" searchMenuForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_03, new RestApiResponse2<>(false));
        }
    }

    @RequestMapping("searchFunctionByfunctionNameAndByShopId")
    public Object selectFunctionPointByShopIdForLst(@RequestParam(name = "funName")  String funName,
                                                    @RequestParam(name = "shopIds")String shopIds) {
        try {
            List<Long> shopIdLst;
            shopIdLst = Arrays.stream(shopIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
            shopIdLst = pesAppVersionMenuBusiness.searchFunctionByfunctionNameAndByShopId(funName, shopIdLst);
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001, RestApiResponse2.of(shopIdLst));
        } catch (Exception e) {
            logger.error(" searchMenuForLst error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_03, new RestApiResponse2<>(false));
        }
    }

    @RequestMapping("isShopMeunNameDisplayByShopId")
    public Object isShopMeunNameDisplayByShopId(@RequestParam(name = "shopId")  String shopId,
                                             @RequestParam(name = "meunName")  String meunName){
        try {
            return apiResponse(ApiCodeEnum.CODE_SUCCESS_1001,
                               new RestApiResponse2<>(userVersionControlBusiness.isShopMeunNameDisplayByShopId(Long.valueOf(shopId), meunName)));
        } catch (Exception e) {
            logger.error(" isShopMeunNameDisplayByShopId error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_03, new RestApiResponse2<>(false));
        }
    }



    @RequestMapping("batchUpdateVersionControl")
    public Object batchUpdateVersionControl(@RequestParam(name = "ids") String ids,
                                            @RequestParam(name="versionIdType")Long versionIdType) {
        RestApiResponse2<String> response = new RestApiResponse2<>(true);
        List<Long> vLst=null;
        try {
            if (!"".equals(ids)) {
                List<String> vids = Arrays.asList(ids.split(","));
            if (vids == null) {
                return apiResponse(ApiCodeEnum.CODE_ERROR_1001, new RestApiResponse2<>(false));
            }
            vLst = vids.stream().map(Long::parseLong).collect(Collectors.toList());

            }
            switch (pesAppVersionMenuBusiness.batchUpdateVersionControl(vLst, versionIdType)) {
                case -1:
                    apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_01, response);
                    break;
                default:
                    apiResponse(ApiCodeEnum.CODE_SUCCESS_1002, response);
            }
            return response;
        } catch (Exception e) {
            logger.error(" batchUpdateVersionControl error:{}", e.getMessage(), e);
            return apiResponse(ApiCodeEnum.CODE_ERROR_XS_09_02, response);
        }
    }




}
