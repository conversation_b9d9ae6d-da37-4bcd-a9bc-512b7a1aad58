
package com.pes.jd.util;

import org.apache.http.*;
import org.apache.commons.collections.MapUtils;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpResponseException;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @desc: 新版，推荐使用.
 * @date: 2018-3-16
 * <AUTHOR>
 */
public class HttpClientUtils {  
	
	private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);
    private static final String HTTP = "http";
    private static final String HTTPS = "https";
    private static SSLConnectionSocketFactory sslsf = null;
    private static PoolingHttpClientConnectionManager cm = null;
    private static SSLContextBuilder builder = null;
    private static RequestConfig requestConfig = null;
    
    static {
        try {
            builder = new SSLContextBuilder();
            // 全部信任 不做身份鉴定
            builder.loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                    return true;
                }
            });
            
            requestConfig = RequestConfig.custom()
            .setSocketTimeout(5000) 
            .setConnectTimeout(5000)
            .setConnectionRequestTimeout(5000)
            .build();
            
            sslsf = new SSLConnectionSocketFactory(builder.build(), new String[]{"SSLv2Hello", "SSLv3", "TLSv1", "TLSv1.2"}, null, NoopHostnameVerifier.INSTANCE);
            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register(HTTP, new PlainConnectionSocketFactory())
                    .register(HTTPS, sslsf)
                    .build();
            cm = new PoolingHttpClientConnectionManager(registry);
            cm.setMaxTotal(100);//max connection
        } catch (Exception e) {
        	logger.error(e.getMessage(),e);
        }
    }
    
    public static CloseableHttpClient getHttpClient() throws Exception {
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(sslsf)
                .setConnectionManager(cm)
                .setConnectionManagerShared(true)
                .build();
        return httpClient;
    }
    
    public static String readHttpResponse(HttpResponse httpResponse) throws ParseException, ClientProtocolException, IOException {
        StringBuilder builder = new StringBuilder();
        // 获取响应消息实体
        HttpEntity entity = httpResponse.getEntity();
        
        if (entity == null) {
            throw new ClientProtocolException("Response contains no content");
        }
        // 响应状态
        builder.append("status:" + httpResponse.getStatusLine());
        builder.append("headers:");
        HeaderIterator iterator = httpResponse.headerIterator();
        while (iterator.hasNext()) {
            builder.append("\t" + iterator.next());
        }
        return  EntityUtils.toString(entity) ;
    }
    
    /**
     * httpClient post请求
     * @param url 请求url
     * @param header 头部信息
     * @param param 请求参数 form提交适用  ）
     * application/x-www-form-urlencoded (格式:key1=value1&key2=value2) 
     * @param entity 请求实体 json/xml提交适用
     * @return 可能为空 需要处理
     * @throws Exception
     *
     */
    public static String post(String  url, Map<String, String> header, Map<String, String> param, String entity, final boolean debug) throws Exception {
        CloseableHttpClient httpClient = null;
        try {
            httpClient = getHttpClient();
            HttpPost httpPost = new HttpPost(url);
           logger.info("Executing request " + httpPost.getRequestLine());
            httpPost.setConfig(requestConfig);  
            // 设置头信息
            if (MapUtils.isNotEmpty(header)) {
                for (Map.Entry<String, String> entry : header.entrySet()) {
                    httpPost.addHeader(entry.getKey(), entry.getValue());
                }
            }
            // 设置请求参数
            if (MapUtils.isNotEmpty(param)) {
                List<NameValuePair> formParams = new ArrayList<NameValuePair>();
                for (Map.Entry<String, String> entry : param.entrySet()) {
                    //给参数赋值
                	formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                UrlEncodedFormEntity urlEncodedFormEntity = new UrlEncodedFormEntity(formParams, Consts.UTF_8);
                httpPost.setEntity(urlEncodedFormEntity);
            }
            // 设置实体 优先级高
            if (entity != null) {
            	//设置请求体 
                StringEntity stringEntity = new StringEntity(entity, ContentType.APPLICATION_JSON.getCharset());  
                stringEntity.setContentType(ContentType.APPLICATION_JSON.getMimeType());
                httpPost.setEntity(stringEntity);
            }
            
            // Create a custom response handler
            ResponseHandler<String> responseHandler = new ResponseHandler<String>() {

                @Override
                public String handleResponse(
                        final HttpResponse response) throws ClientProtocolException, IOException {
                    StatusLine statusLine = response.getStatusLine();
                    int status = statusLine.getStatusCode();
                    HttpEntity entity = null;
                    String result = null;
                    if (status >= 200 && status < 300) {
                    	if(debug){
                    		result = readHttpResponse(response);
                    		logger.info(result);
                    		return result;
                    	}
                    	 entity = response.getEntity();
                    	 if (entity == null) {
                             throw new ClientProtocolException("Response contains no content" + status);
                         }
                    	 result = EntityUtils.toString(entity) ;
                    } else if (status >= 300) {
                        throw new HttpResponseException(status, statusLine.getReasonPhrase());
                    } 
                	return result;
                }
            };
            String result = httpClient.execute(httpPost, responseHandler);
            logger.info(result);
            return result;
        } catch (Exception e) {
        	logger.error(e.getMessage(),e);
        	throw e;
        } finally {
            if (httpClient != null) {
                httpClient.close();
            }
        }
    }
    
}
