package com.pes.jd.util;

import com.google.common.io.Closer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StreamUtils;
import java.io.InputStream;
import java.nio.charset.Charset;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/13 2:44 PM
 * @since 1.0.0
 */
@Configuration
public class EmailUtils {
    private static Logger LOGGER = LoggerFactory.getLogger(EmailUtils.class);
    {
        try(
                Closer closer = Closer.create()
        ) {
            InputStream resourceAsStream =  closer.register(Thread.currentThread().
                    getContextClassLoader().getResourceAsStream("emailContent.txt"));
            emailContent = StreamUtils.copyToString(resourceAsStream, Charset.defaultCharset());
        }catch (Exception e){
            LOGGER.error("get email content txt error",e);
        }
    }

    public static String emailContent;

}
