package com.pes.jd.util;

import org.apache.commons.beanutils.MethodUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.NumberFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/3 5:10 PM
 * @since 1.0.0
 */
public abstract class BaseUtils {

    public static final String EMPTY = "";


    private static final ThreadLocal<NumberFormat> NUMBER_FORMAT_THREAD_LOCAL =
            ThreadLocal.withInitial(NumberFormat::getPercentInstance);

    public static String percent(Number d,int radix){
        NumberFormat format = NUMBER_FORMAT_THREAD_LOCAL.get();
        format.setMaximumFractionDigits(radix);
        return format.format(d);
    }
    public static String percent(Number d){
        if (d == null){
            return EMPTY;
        }
       return percent(d,2);
    }

    public static Double getNonNull(Double doubleValue){
        return doubleValue == null ? 0.0 : doubleValue;
    }

    public static Integer getNonNull(Integer doubleValue){
        return doubleValue == null ? 0 : doubleValue;
    }

    public static void setFlagStr(Number number,Object bean,String methodName){
        try {
            setFlagString(number, bean, methodName);
        } catch (Exception e) {

        }
    }

    public static void setFlagString(boolean condition,Object bean,String[] methodName,String value){
        if (condition) {
            for (String name : methodName) {
                setFlagString(bean, name, value);
            }
        }
    }

    public static void setFlagString(boolean condition,Object bean,String methodName,String value){
        if (condition){
            setFlagString(bean, methodName, value);
        }
    }

    private static void setFlagString(Object bean, String methodName, String value) {
        Method accessibleMethod = MethodUtils.getAccessibleMethod(bean.getClass(), methodName, String.class);
        try {
            accessibleMethod.invoke(bean,value);
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
    }

    private static void setFlagString(Number number,Object bean,String methodName) throws Exception{
        Method accessibleMethod = MethodUtils.getAccessibleMethod(bean.getClass(), methodName, String.class);
        String flag = "--";
        if (Objects.isNull(number)){
            accessibleMethod.invoke(bean,flag);
        }
        flag = "0";
        if (number instanceof Double){
            Double d = (Double)number;
            if (d == 0){
                accessibleMethod.invoke(bean,flag);
            }
        } else if (number instanceof Integer){
            Integer d = (Integer)number;
            if (d == 0){
                accessibleMethod.invoke(bean,flag);
            }
        } else {
            throw new NotSupportedException();
        }
    }

    public static boolean getNonNull(Boolean booleanValue) {
        if (booleanValue == null) {
            return false;
        } else {
            return booleanValue;
        }
    }

    private static class NotSupportedException extends RuntimeException{}

    public static class BaseMap<K,V> extends HashMap<K,V> {
        public Double getDouble(K key){
            V v = get(key);
            if (Objects.isNull(v)){
                return 0.0;
            }
            return (Double) v;
        }
        public Date getDate(K key){
            V v = get(key);
            return (Date)v;
        }
        public Integer getInteger(K key){
            V v = get(key);
            return (Integer) v;
        }

        public V getNonNull(K key){
            return Objects.requireNonNull(get(key)," The Map Value Must be not null ");
        }

        public String getNonNullString(K key){
            return (String) getNonNull(key);
        }

    }

}
