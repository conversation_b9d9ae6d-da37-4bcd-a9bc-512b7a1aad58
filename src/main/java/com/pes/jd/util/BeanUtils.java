package com.pes.jd.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/15 11:23 AM
 * @since 1.0.0
 */
@SuppressWarnings("Duplicates")
public abstract class BeanUtils extends org.springframework.beans.BeanUtils {

    private final static Logger LOGGER = LoggerFactory.getLogger(BeanUtils.class);

    /**
     * 如果加数与被加数有一个是负数 那么返回负数
     */
    public static final ThreeFunction COUNT_PROCESS = (a, b, c) -> {
        if (a == null || b == null) return c;

        if (!Objects.equals(a.getClass(), b.getClass())) {
            return c;
        }
        if (a instanceof Integer) {
            if ((Integer) a < 0) {
                return a;
            }
            if ((Integer) b < 0) {
                return b;
            }
        }
        if (a instanceof Double) {
            if ((Double) a < 0) {
                return a;
            }
            if ((Double) b < 0) {
                return b;
            }
        }
        if (a instanceof Long) {
            if ((Long) a < 0) {
                return a;
            }
            if ((Long) b < 0) {
                return b;
            }
        }
        return c;
    };

    // 延迟数据
    private static final Integer DELAY = -1;
    // 显示 '--'
    private static final Integer DILIMITER = -10;

    public static final ThreeFunction COUNT_PROCESS_SPEC = (a, b, c) -> {
        if (a == null || b == null) return c;

        if (!Objects.equals(a.getClass(), b.getClass())) {
            return c;
        }
        if (a instanceof Integer) {
            final Integer a1 = (Integer) a;
            final Integer b1 = (Integer) b;
            if (a1 == DELAY || b1 == DELAY) {
                return DELAY;
            }
            if (a1 == DILIMITER) {
                return b1;
            }
            if (b1 == DILIMITER) {
                return a1;
            }
        }
        if (a instanceof Double) {
            final Double a1 = (Double) a;
            final Double b1 = (Double) b;
            if (a1 == (double) DELAY || b1 == (double) DELAY) {
                return (double) DELAY;
            }
            if (a1 == (double) DILIMITER) {
                return b1;
            }
            if (b1 == (double) DILIMITER) {
                return a1;
            }
        }
        if (a instanceof Long) {
            final Long a1 = (Long) a;
            final Long b1 = (Long) b;
            if (a1 == (long) DELAY || b1 == (long) DELAY) {
                return (double) DELAY;
            }
            if (a1 == (long) DILIMITER) {
                return b1;
            }
            if (b1 == (long) DILIMITER) {
                return a1;
            }
        }
        return c;
    };

    public static void copyProperties(Object source, Object target) throws BeansException {
        if (source == null || target == null) {
            return;
        }
        copyProperties(source, target, null, true, null, (String[]) null);
    }

    public static void copyProperties(Object source, Object target, String... properties) throws BeansException {
        if (source == null || target == null) {
            return;
        }
        copyProperties(source, target, null, true, null, properties);
    }

    public static void copyPropertiesForContainProperties(Object source, Object target, String defaultVal, String... properties) throws BeansException {
        if (source == null || target == null) {
            return;
        }
        copyProperties(source, target, null, false, defaultVal, properties);
    }

    public static void setDefalutValue(Object source, String defaultVal, Set<String> ignore) {
        PropertyDescriptor[] targetPds = getPropertyDescriptors(source.getClass());
        Set<String> ignoreOrNotIgnoreList = ignore == null ? new HashSet<>() : ignore;
        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            if (writeMethod != null && !ignoreOrNotIgnoreList.contains(targetPd.getName())) {
                ReflectionUtils.makeAccessible(writeMethod);
                final Class<?> parameterType = writeMethod.getParameterTypes()[0];
                Object val = null;
                if (parameterType == Integer.class) {
                    val = Integer.valueOf(defaultVal);
                } else if (parameterType == Double.class) {
                    val = Double.valueOf(defaultVal);
                } else if (parameterType == Long.class) {
                    val = Long.valueOf(defaultVal);
                } else {
                    continue;
                }
                try {
                    writeMethod.invoke(source, val);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    private static void copyProperties(Object source, Object target, Class<?> editable, boolean ignore, String notAllowCopyDefaultValue, String... ignoreOrAllowProperties)
            throws BeansException {

        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");

        Class<?> actualEditable = target.getClass();
        if (editable != null) {
            if (!editable.isInstance(target)) {
                throw new IllegalArgumentException("Target class [" + target.getClass().getName() +
                        "] not assignable to Editable class [" + editable.getName() + "]");
            }
            actualEditable = editable;
        }
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);

        Set<String> ignoreOrNotIgnoreList = (ignoreOrAllowProperties != null ? new HashSet<>(Arrays.asList(ignoreOrAllowProperties)) : null);

        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            final boolean writeMethodNotnull = writeMethod != null;
            Class<?> parameterType1 = null;
            if (writeMethodNotnull) {
                parameterType1 = writeMethod.getParameterTypes()[0];
                if (!ignore) {
                    try {
                        ReflectionUtils.makeAccessible(writeMethod);
                        if (parameterType1 == Integer.class) {
                            writeMethod.invoke(target, Integer.valueOf(notAllowCopyDefaultValue));
                        } else if (parameterType1 == Double.class) {
                            writeMethod.invoke(target, Double.valueOf(notAllowCopyDefaultValue));
                        } else if (parameterType1 == Long.class) {
                            writeMethod.invoke(target, Long.valueOf(notAllowCopyDefaultValue));
                        }
                    } catch (Exception e) {
                        throw new FatalBeanException(e.getMessage(), e);
                    }

                }
            }
            if (writeMethodNotnull && (
                    (!ignore && ignoreOrNotIgnoreList.contains(targetPd.getName())) ||
                            (ignore && (ignoreOrNotIgnoreList == null || !ignoreOrNotIgnoreList.contains(targetPd.getName())))
            )) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null) {
                    Method readMethod = sourcePd.getReadMethod();
                    final Class<?> parameterType = parameterType1;
                    final Class<?> returnType = readMethod.getReturnType();
                    final boolean number = ClassUtils.isAssignable(Number.class, parameterType) &&
                            ClassUtils.isAssignable(Number.class, returnType);
                    final boolean assignable = ClassUtils.isAssignable(parameterType, returnType);
                    if (readMethod != null && (assignable || number)) {
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(source);
                            if (value == null) {
                                continue;
                            }
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            if (!assignable) {
                                if (number) {
                                    final Number nu = (Number) value;
                                    if (parameterType.equals(Double.class)) {
                                        value = nu.doubleValue();
                                    } else if (parameterType.equals(Integer.class)) {
                                        value = nu.intValue();
                                    } else if (parameterType.equals(Long.class)) {
                                        value = nu.longValue();
                                    } else {
                                        throw new RuntimeException("无法支持的类型---" + nu);
                                    }
                                } else {
                                    throw new RuntimeException("类型不通，无法copy");
                                }
                            }
                            writeMethod.invoke(target, value);
                        } catch (Throwable ex) {
                            throw new FatalBeanException(
                                    "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
                        }
                    }
                }
            }
        }
    }

    public static <T> void countPropertyVal(Map<String, Integer> propertyCount, Object source, Object target, Predicate<T> condition
            , String... ignoreProperties) {
        countPropertyVal(source, target, null, propertyCount, condition, ignoreProperties);
    }

    public static void countPropertyVal(Object source, Object target, String... ignoreProperties)
            throws BeansException {
        countPropertyVal(source, target, null, null, null, ignoreProperties);

    }

    @FunctionalInterface
    public interface ThreeFunction<A, B, C, T> {
        T apply(A a, B b, C c);
    }

    public static <T> void countPropertyVal(
            Object source, Object target,
            ThreeFunction<Object, Object, Object, Object> handlerValue,
            Map<String, Integer> propertyCount/*统计字段相加了多少次(有效次数)*/,
            Predicate<T> conditionPropertyCount, String... ignoreProperties)
            throws BeansException {

        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");

        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        List<String> ignoreList = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : null);

        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            Method targetReadMethod = targetPd.getReadMethod();
            final String name = targetPd.getName();
//            LOGGER.info("name: "+name);
            if (writeMethod != null && targetReadMethod != null && (ignoreList == null || !ignoreList.contains(name))) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), name);
                if (sourcePd != null) {
                    Method readMethod = sourcePd.getReadMethod();
                    Class<?> returnType = readMethod.getReturnType();
                    if (!Number.class.isAssignableFrom(returnType)) {
                        continue;
                    }
                    if (readMethod != null &&
                            ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], returnType)) {
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(source);
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            Object value1 = targetReadMethod.invoke(target);
                            Object count = null;
                            if (!"shopSaleAmount".equals(name) && !"csSaleAmount".equals(name) && !"silenceSaleAmount".equals(name)
                                    && !"saleAmount".equals(name) && !"saleGoodsNum".equals(name)) {
                                count = count(value, value1);
                                if (handlerValue != null) {
                                    count = handlerValue.apply(value, value1, count);
                                }
                            } else {
//                                LOGGER.info("countPropertyVal ***** shopSaleAmount,csSaleAmount,silenceSaleAmountte 特殊处理");
//                            	LOGGER.info("name:"+name+" | value:"+value+" | value1:"+value1);
                                //Todo:-10要修改
                                if ("saleAmount".equals(name) && 123456780.0 == (double) value
                                ) {
                                    value = (double) 0.0;
                                }

                                if ("saleGoodsNum".equals(name) && 123456780 == (int) value
                                    // ||"csSaleGoodsNum".equals(name) && 123456780== (int) value
                                ) {
                                    value = 0;
                                }


                                count = countForAmount(value, value1);
//                            	LOGGER.info("countForAmount："+count);
                            }
                            if (count == null) {
                                continue;
                            }
                            if (propertyCount != null) {
                                Integer countP = propertyCount.get(name);
                                if (countP == null) {
                                    countP = 0;
                                }
                                if (conditionPropertyCount == null || conditionPropertyCount.test((T) source))
                                    propertyCount.put(name, ++countP);
                            }
                            writeMethod.invoke(target, count);
                        } catch (Throwable ex) {
                            throw new FatalBeanException(
                                    "Could not add property '" + name + "' from source to target", ex);
                        }
                    }
                }
            }
        }
    }

    public static void avgPropertyVal(Object source, Object target,
                                      int dividend, Map<String, Integer> propertyCount,
                                      Set<String> exclude, String... ignoreProperties)
            throws BeansException {
        dividend = dividend == 0 ? 1 : dividend;
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");

        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        List<String> ignoreList = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : null);

        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            final String name = targetPd.getName();
            if (writeMethod != null && (ignoreList == null || !ignoreList.contains(name))) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), name);
                if (sourcePd != null) {
                    Method readMethod = sourcePd.getReadMethod();
                    if (readMethod != null && (
                            Number.class.isAssignableFrom(readMethod.getReturnType())
                    )) {
                        int devi = dividend;
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(source);
                            if (value == null) {
                                continue;
                            }
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }


                            //处理均值时排除售后客服的数量
                            if (propertyCount != null && (exclude == null || !exclude.contains(name))) {
                                if (!"shopSaleAmount".equals(name) && !"csSaleAmount".equals(name) && !"silenceSaleAmount".equals(name)
                                        && !"saleAmount".equals(name) && !"saleGoodsNum".equals(name) && !"csSaleGoodsNum".equals(name)) {
                                    devi = propertyCount.getOrDefault(name, 1);
                                } else {
                                    devi = dividend;
                                }
                            }


                            Object o = null;
                            if (!"shopSaleAmount".equals(name) && !"csSaleAmount".equals(name) && !"silenceSaleAmount".equals(name)
                                    && !"saleAmount".equals(name) && !"saleGoodsNum".equals(name) && !"csSaleGoodsNum".equals(name)) {
                                o = avgVal(value, devi, writeMethod.getParameterTypes()[0]);
                            } else {
//                                LOGGER.info("avgPropertyVal |||| shopSaleAmount,csSaleAmount,silenceSaleAmountte 特殊处理");
//                            	LOGGER.info("value:"+value+" | devi:"+devi);
                                o = avgValForAmount(value, devi, writeMethod.getParameterTypes()[0]);
//                            	LOGGER.info("avgValForAmount："+o);
                            }
                            writeMethod.invoke(target, o);
                        } catch (Throwable ex) {
                            throw new FatalBeanException(
                                    "Could not copy property '" + name + "' from source to target", ex);
                        }
                    }
                }
            }
        }

    }





    public static void avgPropertyValNew(Object source, Object target,
                                      int dividend,int divs, Map<String, Integer> propertyCount,
                                      Set<String> exclude, String... ignoreProperties)
            throws BeansException {
        dividend = dividend == 0 ? 1 : dividend;
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");

        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        List<String> ignoreList = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : null);

        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            final String name = targetPd.getName();
            if (writeMethod != null && (ignoreList == null || !ignoreList.contains(name))) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), name);
                if (sourcePd != null) {
                    Method readMethod = sourcePd.getReadMethod();
                    if (readMethod != null && (
                            Number.class.isAssignableFrom(readMethod.getReturnType())
                    )) {
                        int devi = dividend;
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(source);
                            if (value == null) {
                                continue;
                            }
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }


                            //处理均值时排除售后客服的数量
                            if (propertyCount != null && (exclude == null || !exclude.contains(name))) {
                                if (!"shopSaleAmount".equals(name) && !"csSaleAmount".equals(name) && !"silenceSaleAmount".equals(name)
                                        && !"saleAmount".equals(name) && !"saleGoodsNum".equals(name) && !"csSaleGoodsNum".equals(name)) {
                                    devi = propertyCount.getOrDefault(name, 1);
                                } else {
                                    devi = divs;
                                }
                            }


                            Object o = null;
                            if (!"shopSaleAmount".equals(name) && !"csSaleAmount".equals(name) && !"silenceSaleAmount".equals(name)
                                    && !"saleAmount".equals(name) && !"saleGoodsNum".equals(name) && !"csSaleGoodsNum".equals(name)) {
                                o = avgVal(value, devi, writeMethod.getParameterTypes()[0]);
                            } else {

                                o = avgValForAmount(value, devi, writeMethod.getParameterTypes()[0]);

                            }
                            writeMethod.invoke(target, o);
                        } catch (Throwable ex) {
                            throw new FatalBeanException(
                                    "Could not copy property '" + name + "' from source to target", ex);
                        }
                    }
                }
            }
        }

    }




    private static Object avgVal(Object value, int dividend, Class targetCla) {
        if (value instanceof Integer) {
            final Integer val = (Integer) value;
            if (targetCla.equals(Double.class)) {
                if (val < 0) {
                    return (double) val;
                }
                return val * 1.0 / dividend;
            }

            return dividend != 0 ? val / dividend : 0;
        }
        if (value instanceof Double) {

            final Double val = (Double) value;
            if (val < 0) {
                return val;
            }
            return val / dividend;
        }
        if (value instanceof Long) {
            final Long val = (Long) value;

            if (targetCla.equals(Double.class)) {
                if (val < 0) {
                    return (double) val;
                }
                return val * 1.0 / dividend;
            }
            if (val < 0) {
                return val;
            }
            return val / dividend;
        }
        throw new RuntimeException();
    }

    private static Object avgValForAmount(Object value, int dividend, Class targetCla) {
        if (value instanceof Integer) {
            final Integer val = (Integer) value;
            if (targetCla.equals(Double.class)) {

                return val * 1.0 / dividend;
            }

            return dividend != 0 ? val / dividend : 0;
        }
        if (value instanceof Double) {

            final Double val = (Double) value;

            return val / dividend;
        }
        if (value instanceof Long) {
            final Long val = (Long) value;

            if (targetCla.equals(Double.class)) {

                return val * 1.0 / dividend;
            }

            return val / dividend;
        }
        throw new RuntimeException();
    }

    private static Object count(Object value, Object value1) {
        if (value == null) {
            return value1;
        }
        if (value1 == null) {
            return value;
        }
        Class<?> aClass = value.getClass();
        Class<?> bClass = value1.getClass();
        if (aClass != bClass) {
            throw new RuntimeException(" the two arg type must is same ");
        }
        if (value instanceof Integer) {
            final Integer v1 = (Integer) value;
            final Integer v2 = (Integer) value1;
            if (v1 < 0 || v2 < 0 || v1 == 123456780 || v2 == 123456780) {

                return null;
            }
            return v1 + v2;
        }
        if (value instanceof Double) {
            final Double v1 = (Double) value;
            final Double v2 = (Double) value1;
            if (v1 < 0 || v2 < 0 || v1 == (double) 123456780 || v2 == (double) 123456780) {
                return null;
            }
            return v1 + v2;
        }
        if (value instanceof Long) {
            final Long v1 = (Long) value;
            final Long v2 = (Long) value1;
            if (v1 < 0 || v2 < 0 || v1 == (long) 123456780 || v2 == (long) 123456780) {
                return null;
            }
            return v1 + v2;
        }
        throw new NotSupportException();
    }

    private static Object countForAmount(Object value, Object value1) {
        if (value == null) {
            return value1;
        }
        if (value1 == null) {
            return value;
        }
        Class<?> aClass = value.getClass();
        Class<?> bClass = value1.getClass();
        if (aClass != bClass) {
            throw new RuntimeException(" the two arg type must is same ");
        }
        if (value instanceof Integer) {
            final Integer v1 = (Integer) value;
            final Integer v2 = (Integer) value1;
//            if (v1 < 0 || v2 < 0) {
//                return null;
//            }
            return v1 + v2;
        }
        if (value instanceof Double) {
            final Double v1 = (Double) value;
            final Double v2 = (Double) value1;
//            if (v1 < 0 || v2 < 0) {
//                return null;
//            }
            return v1 + v2;
        }
        if (value instanceof Long) {
            final Long v1 = (Long) value;
            final Long v2 = (Long) value1;
//            if (v1 < 0 || v2 < 0) {
//                return null;
//            }
            return v1 + v2;
        }
        throw new NotSupportException();
    }

    private static class NotSupportException extends RuntimeException {
    }

}
