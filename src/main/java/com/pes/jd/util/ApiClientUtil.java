package com.pes.jd.util;

import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.pes.jd.constants.AppConstants;
import com.pes.jd.exception.HttpReqException;
import com.pes.jd.exception.JacksonParseException;
import com.pes.jd.model.AO.JDSessionKey;
import com.pes.jd.ms.constant.JdClientConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.*;

public class ApiClientUtil {
	private static Logger log = LoggerFactory.getLogger(ApiClientUtil.class);

	public static JdClient getClient(String accessToken) {
		return new DefaultJdClient(AppConstants.SERVER_URL, accessToken, AppConstants.APP_KEY,
				AppConstants.APP_SECRET, JdClientConstant.CONNECT_TIMEOUT, JdClientConstant.READ_TIMEOUT);
	}

	public static JdClient getClientPreDev(String accessToken) {
		return new DefaultJdClient(AppConstants.SERVER_URL_DEV, accessToken, AppConstants.APP_KEY,
				AppConstants.APP_SECRET, JdClientConstant.CONNECT_TIMEOUT, JdClientConstant.READ_TIMEOUT);
	}

	public static JdClient getClient(String accessToken, String serverUrl) {
		return new DefaultJdClient(serverUrl, accessToken, AppConstants.APP_KEY,
				AppConstants.APP_SECRET, JdClientConstant.CONNECT_TIMEOUT, JdClientConstant.READ_TIMEOUT);
	}


	public static JDSessionKey getSessionKeyByCode(String code) throws HttpReqException, JacksonParseException {
		Map<String, String> props = new HashMap<String, String>();
		props.put("grant_type", "authorization_code");
		props.put("code", code);
		props.put("client_id", AppConstants.APP_KEY);
		props.put("client_secret", AppConstants.APP_SECRET);
		props.put("redirect_uri", AppConstants.REDIRECT_URL);
		String authParamJson;
		JDSessionKey authParam = new JDSessionKey();

		try {
			authParamJson = HttpClientUtils.post(AppConstants.TOKEN_OAUTH_URL, null, props, null, false);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new HttpReqException("京东授权失败，请售后再试！");

		}
		log.info("--get authParamJson:" + authParamJson);
		try {
			authParam = JacksonUtils.json2pojo(authParamJson, JDSessionKey.class);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			throw new JacksonParseException("授权用户访问失败，请咨询系统管理员");
		}
		return (JDSessionKey) authParam;
	}

	public static JDSessionKey getSessionKeyByRefreshToken(String refreshToken) throws HttpReqException, JacksonParseException {
		Map<String, String> props = new HashMap<String, String>();
		props.put("grant_type", "refresh_token");
		props.put("refresh_token", refreshToken);
		props.put("client_id", AppConstants.APP_KEY);
		props.put("client_secret", AppConstants.APP_SECRET);
		String authParamJson;
		JDSessionKey authParam;
		try {
			authParamJson = HttpClientUtils.post(AppConstants.TOKEN_OAUTH_URL, null, props, null, false);
		} catch (Exception e) {
			e.printStackTrace();
			throw new HttpReqException("京东刷新SessionKey失败，请售后再试！");

		}
		System.out.println("京东刷新SessionKey--authParamJson:" + authParamJson);
		try {
			authParam = JacksonUtils.json2pojo(authParamJson, JDSessionKey.class);
		} catch (Exception e) {
			e.printStackTrace();
			throw new JacksonParseException("京东刷新SessionKey访问失败，请咨询系统管理员");
		}
		return (JDSessionKey) authParam;
	}

	/**
	 * @throws UnsupportedEncodingException
	 */


	public static boolean pluginValidation(HttpServletRequest request) throws UnsupportedEncodingException {
		TreeMap<String, String> tree = new TreeMap<String, String>();

		Enumeration<String> enums = request.getParameterNames();

		String param = "";

		while (enums.hasMoreElements()) {

			String ele = enums.nextElement();

			tree.put(ele, request.getParameter(ele));

		}
		Set<String> key = tree.keySet();

		Iterator<String> it = key.iterator();

		while (it.hasNext()) {

			String v = it.next();

			// System.out.println(" "+v+"
			// decode:"+java.net.URLDecoder.decode(request.getParameter(v),"utf-8"));

			if (!"sign".equals(v)) {

				param += v + java.net.URLDecoder.decode(request.getParameter(v), "utf-8");

			}

		}

		param = AppConstants.APP_SECRET + param + AppConstants.APP_SECRET;

		String sign = request.getParameter("sign");

		String sign2 = MD5Utils.encode(param).toUpperCase();

		System.out.println("sign:" + sign);

		System.out.println("sign2:" + sign2);

		/*
		 * if(sign2.equalsIgnoreCase(sign)){
		 *
		 * return true;
		 *
		 * }
		 */
		return true;
		// return false;
	}

}
