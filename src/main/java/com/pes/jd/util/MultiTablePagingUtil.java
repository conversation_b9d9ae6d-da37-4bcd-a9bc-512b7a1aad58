package com.pes.jd.util;

import java.util.Map;

public class MultiTablePagingUtil {


    private int totalRecordNum;//已经获取到的数据总和

    private int tableRecordNum;//遍历过的表 在数据库的数据总和

    private int tableStartIndex;//当前表获取数据的开始下标
    private int tableRecordLength;//当前表需要获取数据的记录数

    private int start;//页面传过来的 开始记录的下标
    private int length;//页面传过来的 每页记录数
    private boolean isPosition = false;

    private Map<String, Integer> tableTotalRecordNumMap; //key:表名 value:该表count

    public MultiTablePagingUtil() {
        super();
    }

    public MultiTablePagingUtil(int start, int length) {
        super();
        this.start = start;
        this.length = length;
        init();
    }

    public MultiTablePagingUtil(int start, int length, Map<String, Integer> tableTotalRecordNumMap) {
        super();
        this.start = start;
        this.length = length;
        this.tableTotalRecordNumMap = tableTotalRecordNumMap;
        init();
    }

    /**
     * 初始化值
     */
    public void init() {
        this.totalRecordNum = 0;
        this.tableRecordNum = 0;
        this.tableRecordLength = this.length;
    }

    /**
     * 增加 上一次查询过的表记录数 再 get 已经拿到的总数
     *
     * @param recordNum 上一次查询记录数
     * @return
     */
    public int addAndGetTotalRecordNum(int recordNum) {
        this.totalRecordNum += recordNum;
        return this.totalRecordNum;
    }

    /**
     * 是否可以接着查询
     *
     * @return
     */
    public boolean hasNext() {
        if (this.totalRecordNum >= this.length) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 获取 对当前表 处理
     *
     * @param tableName 当前表名
     * @return 0 数据满 return | 1 查询当前表数据，继续拿数据 | 2 跳过该表
     */
    public int nextTable(String tableName) {
        /*
         * 优化性能，当index大于 前几个表的数据总和时，跳过之前表的查询逻辑
         */
        Integer tableTotalNum = tableTotalRecordNumMap.get(tableName);  //获取当前表的总数据量
        if (this.start < this.tableRecordNum + tableTotalNum) {    //开始坐标 是否小于（遍历过的表数据量 + 当前表数据量）
            if(this.isPosition){
            	this.tableStartIndex = 0;
            }else{
            	this.tableStartIndex = this.start - this.tableRecordNum;
            	this.isPosition = true;
            }
            boolean isNext = this.totalRecordNum < this.length;
            if (isNext) { //是接着查询
                this.tableRecordLength -= this.totalRecordNum;  //计算在该表中查询记录的长度
                this.tableRecordNum += tableTotalNum;
                return 1;
            } else {
                return 0;
            }
        }
        this.tableRecordNum += tableTotalNum;
        return 2;
    }

    /**
     * 设置当前表
     *
     * @param tableTotalRecordNumMap Map<表名，表数据量>
     */
    public void setTableTotalRecordNumMap(Map<String, Integer> tableTotalRecordNumMap) {
        this.tableTotalRecordNumMap = tableTotalRecordNumMap;
    }

    /**
     * 获取当前表 开始位置
     *
     * @return
     */
    public int getTableStartIndex() {
        return tableStartIndex;
    }

    /**
     * 获取当前表 查询长度
     *
     * @return
     */
    public int getTableRecordLength() {
        return tableRecordLength;
    }

}
