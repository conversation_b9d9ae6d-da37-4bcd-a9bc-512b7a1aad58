package com.pes.jd.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DateUtil {

	public static final long dayTime = 86400000;
	
	private final static Logger LOGGER = LoggerFactory.getLogger(DateUtil.class);
	/**
	 * 将日期Date格式化为yyyy-MM-dd (eg:2015-11-11)
	 * 
	 * @param Date
	 * @return Date
	 */
	public static Date yyyyMMddFormat(Date date) throws ParseException{
		return DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(date));
	}
    /**
     * 获取传入时间字符串的开始时间："2015-11-11"
     * @param dateStr(时间字符串)
     * @return Date
     */
	public static Date getStartDateFromDateStr(String dateStr) throws ParseException{
	    
	    Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateFormatUtils.parseYMd(dateStr));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
	}
	/**
     * 获取传入时间字符串的结束时间："2015-11-11"
     * @param dateStr(时间字符串)
     * @return Date
     */
    public static Date getEndDateFromDateStr(String dateStr) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateFormatUtils.parseYMd(dateStr));
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.SECOND, -1);
        return calendar.getTime();
    }
    /**
     * 得到指定时间在增减相应的天数后的结束时间
     * @Title: getEndDateFromDateStrAndPeriod 
     * @Description: TODO(这里用一句话描述这个方法的作用) 
     * @param @param dateStr
     * @param @param period
     * @param @return
     * @param @throws ParseException    设定文件 
     * @return Date    返回类型 
     * @throws
     */
    public static Date getEndDateFromDateStrAndPeriod(String dateStr,int period) throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateFormatUtils.parseYMd(dateStr));
        calendar.add(Calendar.DAY_OF_MONTH, period);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.SECOND, -1);
        return calendar.getTime();
    }
    
	public static boolean isSameDate(Date date1, Date date2) {
		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);

		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);

		boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
		boolean isSameMonth = isSameYear && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
		boolean isSameDate = isSameMonth && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
		return isSameDate;
	}
    
    public static Date getEndDateFromDateAndPeriod(Date date,int period){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, period+1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.SECOND, -1);
        return calendar.getTime();
    }
	/**
	 *在当前的时间上加上或者减去指定的天数
	 * 
	 * @param targetDate
	 *            目标时间
	 * @param period
	 *           周期(-1 当前的时间减去一天；1当前的时间加一天)
	 * @return
	 */
	public static Date getDateByPeriod(Date targetDate, int period) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(targetDate);
		calendar.add(Calendar.DAY_OF_MONTH, period);
		return calendar.getTime();
	}

	/**
	 * 获取传入时间的开始时间：2015-11-11 00:00:00
	 * 
	 * @param date
	 * @return
	 */
	public static Date getStartTimeOfDate(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
	}

	/**
	 * 获取传入时间的结束时间：2015-11-11 23:59:59
	 * 
	 * @param date
	 * @return
	 */
	public static Date getEndTimeOfDate(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.add(Calendar.SECOND, -1);
		return calendar.getTime();
	}

	public static ArrayList<Date> splitDate(Date startDate, Date endDate)  {
		ArrayList<Date> dateList = new ArrayList<Date>(0);

		startDate = DateUtil.getStartTimeOfDate(startDate);
		endDate = DateUtil.getStartTimeOfDate(endDate);
		long startDateTime = startDate.getTime();
		long endDateTime = endDate.getTime();
		if (startDateTime != endDateTime) {
			dateList.add(startDate);
			startDateTime = startDateTime + dayTime;
			while (startDateTime < endDateTime-dayTime/2) {
				dateList.add(new Date(startDateTime));
				startDateTime = startDateTime + dayTime;
			}
			dateList.add(endDate);
		} else {
			dateList.add(startDate);
		}
		return dateList;
	}

	/**
	 * 算出开始时间和结束时间之间的时间间隔（2015-11-05 00:00:00 =>2015-11-05 23:59:59）
	 * @param startDate
	 * @param endDate
	 * @param interval
	 * @return
	 */
	public static ArrayList<Date> splitOneDayDatePeriod(Date startDate, Date endDate)  {
		ArrayList<Date> dateList = new ArrayList<Date>();
		int interval = 24*60*60-1;
		long startDateTime = startDate.getTime();
		long endDateTime = endDate.getTime();

		if (startDateTime != endDateTime) {
			dateList.add(startDate);
			startDateTime = startDateTime + interval*1000;
			while (startDateTime + interval*1000 < endDateTime) {
				dateList.add(new Date(startDateTime));
				startDateTime+=1000;
				dateList.add(new Date(startDateTime));
				startDateTime = startDateTime + interval*1000;
			}
			dateList.add(endDate);
		} else {
			dateList.add(startDate);
			dateList.add(endDate);
		}
		return dateList;
	}
	/**
	 * 线程不安全的
	 * 算出开始时间和结束时间之间的时间List (long interval为指定的间隔,单位：秒)
	 * @param startDate
	 * @param endDate
	 * @param interval
	 * @return List<Date> dates
	 */
	public static List<Date> splitDateToList(Date startDate, Date endDate,long interval)  {
		if(interval <= 1) {
			System.err.println("间隔必须大于1！");
			return new ArrayList<Date>();
		}
		ArrayList<Date> dateList = new ArrayList<Date>();
		interval -= 1;
		
		long startDateTime = startDate.getTime();
		long endDateTime = endDate.getTime();

		if (startDateTime != endDateTime) {
			dateList.add(startDate);
			startDateTime = startDateTime + interval*1000;
			while (startDateTime+interval*1000 < endDateTime) {
				dateList.add(new Date(startDateTime));
				startDateTime+=1000;
				dateList.add(new Date(startDateTime));
				startDateTime = startDateTime + interval*1000;
			}
			dateList.add(endDate);
		} else {
			dateList.add(startDate);
			dateList.add(endDate);
		}
		return dateList;
	}
	/**
	 * 线程安全的
	 * 算出开始时间和结束时间之间的时间List (long interval为指定的间隔,单位：秒)
	 * @param startDate
	 * @param endDate
	 * @param interval
	 * @return List<Date> dates
	 */
	public static List<Date> splitDateToListWithSync(Date startDate, Date endDate,long interval)  {
		if(interval <= 1) {
			System.err.println("间隔必须大于1！");
			return new ArrayList<Date>();
		}
		ArrayList<Date> dateList = new ArrayList<Date>();
		interval -= 1;
		
		long startDateTime = startDate.getTime();
		long endDateTime = endDate.getTime();

		if (startDateTime != endDateTime) {
			dateList.add(startDate);
			startDateTime = startDateTime + interval*1000;
			while (startDateTime+interval*1000 < endDateTime) {
				dateList.add(new Date(startDateTime));
				startDateTime+=1000;
				dateList.add(new Date(startDateTime));
				startDateTime = startDateTime + interval*1000;
			}
			dateList.add(endDate);
		} else {
			dateList.add(startDate);
			dateList.add(endDate);
		}
		return dateList;
	}
	
	/**
	 * 根据开始时间和结束时间算出之间的日期List
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static ArrayList<Date> splitPeriod2List(Date startDate, Date endDate)  {
		ArrayList<Date> dateList = new ArrayList<Date>(2);
		
		long startDateTime = startDate.getTime();
		long endDateTime = endDate.getTime();
		try {
			if (startDateTime != endDateTime) {
				dateList.add(yyyyMMddFormat(startDate));
				startDateTime = startDateTime + 24*60*60*1000;
				while (startDateTime < endDateTime) {
						dateList.add(yyyyMMddFormat(new Date(startDateTime)));
					startDateTime = startDateTime + 24*60*60*1000;
				}
			} else {
				dateList.add(yyyyMMddFormat(startDate));
			}
		} catch (ParseException e) {
			LOGGER.error(e.getMessage(),e);
		}
		return dateList;
	}

	// �õ����Ի�ȡ��ݵ�������ʱ��
	public static Date getMaxEndDate(Date date) throws ParseException {
		Date nowDate = new Date();
		nowDate = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(new Date(nowDate.getTime()
				- dayTime)));

		if ((nowDate.getTime()) < date.getTime()) {
			date = nowDate;
		} else {
			date = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(date));
		}

		return date;
	}

	/**
	 * yyyy-MM-dd
	 * @param date
	 * @return Date
	 * @throws ParseException
	 */
	public static Date getSQLDate(Date date) throws ParseException {
		date = DateFormatUtils.parseYMd(DateFormatUtils.formatYMd(date));
		return date;
	}

	/**
	 * yyyy-MM-dd
	 * @param date String
	 * @return
	 * @throws ParseException 
	 */
	public static String date2String(Date date) throws ParseException {
		return DateFormatUtils.formatYMd(date);
	}

	// 根据传入的毫秒获取日期
	public static String getTime(long time) {

		int tim = (int) (time / 1000);
		int hour = tim / 3600;
		int temp = tim % 3600;
		int mm = temp / 60;
		temp = temp % 60;
		int ss = temp;
		String result="" ;
		
		if(hour!=0){
			result=result+hour+"h ";
		}
		 if(mm!=0){
			result=result+mm+"m ";
		}
		 if(ss!=0){
			result=result+ss+"s ";
		}
		return result;
	}
	
/*	//��AM��PM��β��ʱ��ת��ΪDate����
	public static Date  AM2Date(String amdate) throws ParseException{
		int endH=0;
		int endM=0;
		
		String endWidth[]=amdate.split(" ");
		if(endWidth[1].equals("AM")){
			String time[]=endWidth[0].split(":");
			endH=Integer.parseInt(time[0]);
			endM=Integer.parseInt(time[1]);
		}
		else{
			String time[]=endWidth[0].split(":");
			endH=(Integer.parseInt(time[0])+12)%24;
			endM=Integer.parseInt(time[1]);	
		}
		String result="2014-09-09_";
		if(endH<10){
			result=result+"0"+endH+"-";
			
		}
		else{
			result=result+endH+"-";
		}
		
		if(endM<10){
			result=result+"0"+endM+"-00";
		}
		else{
			result=result+endM+"-00";
		}
		return sdf.parse(result);
	}*/

	//��Dateת��ΪAM���͵��ַ�ת
	@SuppressWarnings("deprecation")
	public static String date2Am(Date date){
		
		int hour=date.getHours();
		int minut=date.getMinutes();
		String type;
		if(hour>12){
			hour=hour%12;
			type="PM";
		}else{
			type="AM";
		}
		
		return  hour+":"+minut+" "+type;
	}
	
	public static Date getFirstDayOfWeekOfYear(int year, int weekNum){
		Calendar c = new GregorianCalendar();
		c.setFirstDayOfWeek(Calendar.MONDAY);
		c.set(Calendar.YEAR, year);
		c.set(Calendar.WEEK_OF_YEAR, weekNum);
		c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
		return c.getTime();
	}
	
	public static Date getLastDayOfWeekOfYear(int year, int weekNum){
		Calendar c = new GregorianCalendar();
		c.setFirstDayOfWeek(Calendar.MONDAY);
		c.set(Calendar.YEAR, year);
		c.set(Calendar.WEEK_OF_YEAR, weekNum);
		c.set(Calendar.DAY_OF_WEEK, c.getFirstDayOfWeek());
		c.roll(Calendar.DAY_OF_WEEK, 6);
		return c.getTime();
	}
	public static void main(String[] args) throws ParseException {
		List<Date> dateLst=splitDate(DateUtil.getStartDateFromDateStr("2019-08-26 00:00:00"), DateUtil.getEndDateFromDateStr("2019-08-27 23:59:59"));
		for (Date date : dateLst) {
			System.out.println(date);
		}
	}
	
	public static List<Date> splitDateByHour(Date startDate, Date endDate,long interval)  {
		if(interval <= 1) {
			System.err.println("间隔必须大于1！");
			return new ArrayList<Date>();
		}
		ArrayList<Date> dateList = new ArrayList<Date>();
		
		long startDateTime = startDate.getTime();
		long endDateTime = endDate.getTime();

		if (startDateTime != endDateTime) {
			dateList.add(startDate);
			startDateTime = startDateTime + interval*1000;
			while (startDateTime < endDateTime) {
				dateList.add(new Date(startDateTime));
				startDateTime = startDateTime + interval*1000;
			}
			dateList.add(endDate);
		} else {
			dateList.add(startDate);
			dateList.add(endDate);
		}
		return dateList;
	}
	
	public static int date2Date(Date maxDate,Date minDate){
		return (int) ((maxDate.getTime()-minDate.getTime())/86400000);
	}
	
    /** 
     * 根据年 月 获取对应的月份 天数 
     * */  
    public static int getDaysByYearMonth(int year, int month) {  
          
        Calendar a = Calendar.getInstance();  
        a.set(Calendar.YEAR, year);  
        a.set(Calendar.MONTH, month - 1);  
        a.set(Calendar.DATE, 1);  
        a.roll(Calendar.DATE, -1);  
        int maxDate = a.get(Calendar.DATE);  
        return maxDate;  
    } 
    
    public static Date getMiddleDateTime(Date startDate, Date endDate){
    	Date middleDate = null;
    	middleDate = new Date((endDate.getTime() - startDate.getTime()) / 2 + startDate.getTime());
    	return middleDate;
    }
   
	/**
	 * 当日期间隔大于指定的间隔的时候，修改开始查询时间
	 * @param startDate
	 * @param num
	 * @return Date
	 */
	public static Date setStartTime(Date startDate,int num){
		
		Date newDate =  new Date();
		long days = (newDate.getTime()-startDate.getTime())/86400000;
		if(days>num){
			startDate =  DateUtil.getDateByPeriod(newDate, 0-num);
		}
		
		return startDate;
	}
	public static Date getEndDateOfMonth(String dateStr) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
		Calendar a = Calendar.getInstance();
		try {
			Date fromDate = sdf.parse(dateStr);
			a.setTime(fromDate);
			a.add(Calendar.MONTH, 1);
			a.set(Calendar.DAY_OF_MONTH, 1);
			a.set(Calendar.HOUR_OF_DAY, 0);
			a.set(Calendar.MINUTE, 0);
			a.set(Calendar.SECOND, 0);
			a.add(Calendar.SECOND, -1);
		} catch (ParseException e) {
			LOGGER.error(e.getMessage(),e);
		}
		
		return a.getTime();
	}
	
	public static Date getStartDateOfMonth(String dateStr) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
		Calendar a = Calendar.getInstance();
		try {
			Date fromDate = sdf.parse(dateStr);
			a.setTime(fromDate);
			a.set(Calendar.DAY_OF_MONTH, 1);
			a.set(Calendar.HOUR_OF_DAY, 0);
			a.set(Calendar.MINUTE, 0);
			a.set(Calendar.SECOND, 0);
		} catch (ParseException e) {
			LOGGER.error(e.getMessage(),e);
		}
		
		return a.getTime();
	}
	
	/**
     * 功能：获取上月的开始时间
     * @return
     */
	public static Date getLastMonthStart() {// 当月开始时间
	    Calendar currentDate=Calendar.getInstance();
	    currentDate.add(Calendar.MONTH, -1);
	    currentDate.set(Calendar.DAY_OF_MONTH,1);
        return (Date)currentDate.getTime();

    }
    
    /**
     * 功能：获取上月的结束时间
     * @return
     */
	public static Date getLastMonthEnd() {
		  Calendar currentDate=Calendar.getInstance();
		  currentDate.set(Calendar.DAY_OF_MONTH, 0);
	      return (Date)currentDate.getTime();
    }
	/**
     * 功能：比较传入时间是否是一年以内
     * @return
     */ 
	public static boolean compareTimeThanOneYear(Date startDate) {
		Date OldYear = getOldYear();
		if (OldYear.getTime() < startDate.getTime()){
			return false;
		} else {
			return true;
		}

	}
	
	/**
     * 功能：获取一年前的的时间
     * @return
     */
	public static Date getOldYear() {
		Calendar calendar = Calendar.getInstance();
		Date date = new Date(System.currentTimeMillis());
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, -1);
		date = calendar.getTime();
		return date;
	}
	/**
	 * 功能：比较传入时间是否三月前：去除本月的三月前(三月前的本月第一天是可查的)
	 * @return
	 */ 
	public static boolean compareTimeThanThreeMonths(Date startDate) {
		
		Calendar calendar = Calendar.getInstance();
		Date date = new Date(System.currentTimeMillis());//获取当前时间
		calendar.setTime(date);
		calendar.add(Calendar.MONDAY, -3);//三月前
		calendar.set(Calendar.DAY_OF_MONTH, 0);//三月前的当月第一天的前一天（四月前的最后一天）
		date = calendar.getTime();
		//System.out.println(date);
		
		if (date.getTime() < startDate.getTime()){
			return false;
		} else {
			return true;
		}
		
	}
	
	/**
	 * 功能：比较传入时间与当天开始时间的差值：用于比较当天时间大小,后者大返回true 		--最早上线时间
	 * @return
	 */ 
	public static boolean compareTimeOfDay(Date firstOnLineDate,Date twoOnLineDate) {
		if((firstOnLineDate.getTime()- DateUtil.getStartTimeOfDate(firstOnLineDate).getTime())<(twoOnLineDate.getTime()- DateUtil.getStartTimeOfDate(twoOnLineDate).getTime())){
			return true;
		}else{
			return false;
		}
	}
	/**
	 * 功能：比较两个时间       目的：得出 最晚下线时间
	 * @return
	 */ 
	public static boolean compareTimeToLastOffLine(Date firstOnLineDate,Date twoOnLineDate) {
	//21600000		6小时		86400000 
		Long sixFirst = firstOnLineDate.getTime()- DateUtil.getStartTimeOfDate(firstOnLineDate).getTime();
		Long sixTwo = twoOnLineDate.getTime()- DateUtil.getStartTimeOfDate(twoOnLineDate).getTime();
		if(sixFirst <21600000){
			//小于六点，加一天
			sixFirst += dayTime;
		}
		if(sixTwo<21600000){
			sixTwo += dayTime;
		}
		if(sixFirst<sixTwo){
			return true;
		}else{
			return false;
		}
	}
	
  //当前时间 YYYY-MM-DD 23：59：59往后推迟  period 小时数
    
    public static Date getEndDateFromDateAndPeriodByHour(Date date,int period){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, period+1);  
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.add(Calendar.SECOND, -1);
        return calendar.getTime();
    }
    
    
    
    //通过传入的hour得到时间段的开始时间
    public static Date getStartDateHour(Date date,Integer startHour){
    	Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, startHour);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTime();
    }
    
    
    //通过传入的hour得到时间段的结束时间
    public static Date getEndDateHour(Date date,Integer endHour){
    	Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, endHour);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);
		return calendar.getTime();
    }
    
}
