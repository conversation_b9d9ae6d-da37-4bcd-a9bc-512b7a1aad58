package com.pes.jd.util;

import com.google.common.collect.Sets;

import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import java.util.Set;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2019/8/29
 * @Modified By:
 */
public class MobileDeviceUtils {

    public static boolean isMobileDevice(HttpServletRequest request) {
        String requestHeader = request.getHeader("user-agent");
        String[] deviceArray = new String[]{"android", "iphone", "ios", "windows phone"};
        if (StringUtils.isBlank(requestHeader)) {
            return false;
        }
        requestHeader=requestHeader .toLowerCase();
        for (String s : deviceArray) {
            if(requestHeader.contains(s)){
                return true;
            }
        }
        return false;
    }
    public static boolean isPcDevice(HttpServletRequest request) {
        String requestHeader = request.getHeader("user-agent");
        if (StringUtils.isBlank(requestHeader)) {
            return false;
        }
        Set<String> deviceSet= Sets.newHashSet("dd_pc");
        requestHeader=requestHeader .toLowerCase();
        for (String s : deviceSet) {
            if(requestHeader.contains(s)){
                return true;
            }
        }
        return false;
    }
}
