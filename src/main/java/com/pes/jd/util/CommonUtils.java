package com.pes.jd.util;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.model.Enum.QueryType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.env.PropertiesPropertySourceLoader;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.Assert;
import org.springframework.util.StreamUtils;
import org.springframework.util.ReflectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @<NAME_EMAIL>
 * @since 1.0.0
 * @version 0.0.1
 */
public abstract class CommonUtils {



    public static final Boolean TEST = Boolean.FALSE;

    private final static Logger LOGGER = LoggerFactory.getLogger(CommonUtils.class);
    private final static int STR_CAPACITY = 20;
    private final static String PLACEHOLDER = "_";
    private final static Map<String,Object> EMPTY_SET = new HashMap<>(0);
    private final static String METHOD_PLACEHOLDER = "get";
    private final static Map<Class<?>,Method[]> CLASS_GETMETHOD_MAP = new ConcurrentHashMap<>(16);
    private final static Map<MethodConvert,MethodDependency> METHOD_DUMP = Maps.newConcurrentMap();
    private final static int EX_CAPACITY = 1000;
    public final static Splitter COMMA_SPLITTER =  Splitter.on(",").omitEmptyStrings().trimResults();

    public final static Double DOUBLE_ZERO = Double.valueOf(0.0d);

    private final static String CUSTOM_COLUNMS = "custom";
    private final static String SHOP = "shop";
    private final static String NICK = "nick";
    public static final List<String> CUSTOM_COLUNMS_SHOP;
    public static final List<String> CUSTOM_COLUNMS_NICK;
    static {
        try {
            PropertySource<?> load = new PropertiesPropertySourceLoader()
                    .load(CUSTOM_COLUNMS, new ClassPathResource("json/custom.properties"), null);
            CUSTOM_COLUNMS_NICK = COMMA_SPLITTER.splitToList((String)load.getProperty(SHOP));
            CUSTOM_COLUNMS_SHOP = COMMA_SPLITTER.splitToList((String)load.getProperty(NICK));
            LOGGER.info("======================= init custom columns ===================");
        } catch (IOException e) {
            LOGGER.error(e.getMessage(),e);
            throw new IllegalStateException("init source faild");
        }
    }

    public static <T> T getNonNullObj(T t,Class<T> cla){
        if (Objects.isNull(t)){
            try {
                return cla.newInstance();
            } catch (InstantiationException e) {
                LOGGER.error(e.getMessage(),e);
            } catch (IllegalAccessException e) {
                LOGGER.error(e.getMessage(),e);
            }
        }
        return t;
    }

    /**
     *  对象转换
     * @param data
     * @param out
     * @param <I>
     * @param <O>
     * @return
     */
    public static <I,O> List<O> convertList(List<I> data,Class<O> out) {
        if (CollectionUtils.isEmpty(data))return Collections.emptyList();
        List<O> result = Lists.newArrayListWithCapacity(data.size());
        for (I datum : data) {
            try {
                result.add(convertSingle(datum,out));
            } catch (Exception e) {
                LOGGER.error(e.getMessage(),e);
            }
        }
        return result;
    }

    public static <I,O> O convertSingle(I in,Class<O> cla) throws InvocationTargetException, IllegalAccessException, InstantiationException {
        O o = cla.newInstance();
        MethodDependency methodDependency = getMethodDependency(in.getClass(), cla);
        List<Method> smethods = methodDependency.getSmethods();
        List<Method> tmethods = methodDependency.getTmethods();
        for (int i = 0; i < smethods.size(); i++) {
            tmethods.get(i).invoke(o,smethods.get(i).invoke(in));
        }
        return o;
    }

    private static MethodDependency getMethodDependency(Class<?> source,Class<?> target) {
        MethodDependency dependency = METHOD_DUMP.get(MethodConvert.of(source,target));
        if (Objects.nonNull(dependency)){
            return dependency;
        }
        Method[] smethods = source.getDeclaredMethods();
        Method[] tmethods = target.getDeclaredMethods();
        Assert.state(ArrayUtils.isNotEmpty(smethods)&&ArrayUtils.isNotEmpty(tmethods),"转换失败找不到方法");
        List<Method> smethod = Lists.newArrayList();
        List<Method> tmethod = Lists.newArrayList();
        for (int i = 0; i < tmethods.length; i++) {
            if (tmethods[i].getName().startsWith("set")) {
                for (int j = 0; j < smethods.length; j++) {
                    if (smethods[j].getName().startsWith("get") &&
                            Objects.equals(smethods[j].getName().substring(3), tmethods[i].getName().substring(3))) {
                        smethod.add(makeAccessible(smethods[j]));
                        tmethod.add(makeAccessible(tmethods[i]));
                        break;
                    }
                }
            }
        }
        return put(MethodConvert.of(source,target), MethodDependency.newBuilder().source(source).target(target).smethods(smethod).tmethods(tmethod).build());
    }

    private static Method makeAccessible(Method method){
        ReflectionUtils.makeAccessible(method);
        return method;
    }

    private static MethodDependency put(MethodConvert sou, MethodDependency t){
        METHOD_DUMP.put(sou,t);
        return t;
    }

    public static String getTableName(String schema,String table){
        return new StringBuilder(STR_CAPACITY)
                .append(schema).append(".")
                .append(table).toString();
    }

    public static Map<String,Object> getObjectFiledValue(Object... objects) throws InvocationTargetException, IllegalAccessException {
        if (CollectionUtils.isEmpty(Arrays.asList(objects)))return EMPTY_SET;
        Map<String,Object> result = new HashMap<>(16);
        for (Object object : objects) {
            Method[] methods = getGetMethods(object.getClass());
            for (Method method : methods) {
                result.put(method.getName().substring(METHOD_PLACEHOLDER.length()).toLowerCase(),
                        method.invoke(object));
            }
        }
        return result;
    }

    public static String getExMessage(Exception ex){
        return getExMessage(ex,Integer.MAX_VALUE);
    }

    public static String getExMessage(Exception ex,int spliter) {
        return Joiner.on("\n\t")
                .appendTo(ofStringBuilder(EX_CAPACITY)
                .append("Exception Type : { ")
                .append(ex.getClass().toString())
                .append(" } ")
                .append(" cause :  ")
                .append(ex.getMessage()).append("\n"), getStackTrace(ex,spliter))
                .toString();
    }

    private static StringBuilder ofStringBuilder(){
        return new StringBuilder();
    }
    private static StringBuilder ofStringBuilder(int capacity){
        return new StringBuilder(capacity);
    }

    private static StackTraceElement[] getStackTrace(Exception ex,int spliter) {
        if (ex.getStackTrace().length<=spliter){
            return ex.getStackTrace();
        }
        return Arrays.copyOfRange(ex.getStackTrace(),0,spliter);
    }

    private static Method[] getGetMethods(Class<?> aClass) {
        Method[] method = CLASS_GETMETHOD_MAP.get(aClass);
        if (Objects.nonNull(method)){ return method;}
        Method[] declaredMethods = aClass.getDeclaredMethods();
        return put(aClass,Arrays.stream(declaredMethods)
                .filter(e -> e.getName().startsWith(METHOD_PLACEHOLDER))
                .map(MethodUtils::getAccessibleMethod).toArray(Method[]::new));
    }

    private static Method[] put(Class<?> cla,Method[] methods){
        CLASS_GETMETHOD_MAP.put(cla,methods);
        return methods;
    }

    public static List<Map<String, Object>>  mergeTables(
            List<List<Map<String, Object>>> dataList, List<Date> subDate,
            QueryType type, List<Columns> columns, Object... mergeColumn) {
            return handlerResidual(mergeTables(dataList,mergeColumn),subDate,columns,type);
    }

    private static List<Map<String, Object>> handlerResidual(
            List<Map<String, Object>> mergeTables, List<Date> subDate, List<Columns> columns, QueryType type) {
        if (Objects.equals(type, QueryType.NICK)) {
            nickFillOrCreate(mergeTables, columns);
            return mergeTables;
        }
        // 日期维度
        if (Objects.equals(type, QueryType.DATE)) {
            dateFillOrCreate(mergeTables, columns,subDate);
            mergeTables.sort((x,y)->{
                long datex = ((Date) x.get("date")).getTime();
                long datey = ((Date) y.get("date")).getTime();
                if (datex>datey){
                    return -1;
                }
                if (datex == datey){
                    return 0;
                }
                return 1;
            });
            return mergeTables;
        }
        throw new IllegalStateException("找不到合适维度计算");
    }

    private static void dateFillOrCreate(List<Map<String, Object>> mergeTables,
                                         List<Columns> columns, List<Date> subDate) {
        System.out.println();
        for (int i = 0; i < mergeTables.size(); i++) {
            fillMap(mergeTables.get(i),columns);
            subDate.remove(mergeTables.get(i).get("date"));
        }
        for (int i = 0; i < subDate.size(); i++) {
            mergeTables.add(createMap(subDate.get(i),columns));
        }
    }

    private static void nickFillOrCreate(List<Map<String, Object>> mergeTables, List<Columns> columns) {
        for (int i = 0; i < mergeTables.size(); i++) {
            Map<String, Object> tmp = mergeTables.get(i);
            if (tmp.size()<columns.size()){
                fillMap(tmp,columns);
            }
        }
    }

    private static void fillMap(Map<String,Object> map,List<Columns> cl){
        cl.forEach(e->{
            if (Objects.isNull(map.get(e.getField()))){
                map.put(e.getField(),getValue(e));
            }
        });
    }

    private static Map<String, Object> createMap(Date date,List<Columns> cl){
        Map<String, Object> collect = cl.stream().collect(Collectors.toMap(Columns::getField, CommonUtils::getValue));
        collect.put("date",date);
        return collect;
    }

    private static Object getValue(Columns e) {
        if (Objects.equals(e.getType(),"double")){
            return DOUBLE_ZERO;
        }
        if (Objects.equals(e.getType(),"integer")){
            return Integer.valueOf(0);
        }
        if (Objects.equals(e.getType(),"bigdecimal")){
            return BigDecimal.ZERO;
        }
        throw new IllegalStateException("找不到匹配的类型，赋值失败");
    }

    private static Integer getMapSize(int size) {
        int i = Integer.bitCount(size) - 1;
        return (size>>>i)<<i+1;
    }

    /**
     * 合并多个 ${@code List<Map> }
     * @param dataList 需要合并 ${@code List<Map> } 的集合
     * @param mergeColumn 合并相同的条件
     * @return 最终合并的数据集
     */
    public static List<Map<String, Object>> mergeTables(
            List<List<Map<String, Object>>> dataList,Object... mergeColumn) {
        if (dataList.size()==0)return new ArrayList<>();
        if (dataList.size()==1)return dataList.get(0);
        Assert.notEmpty(mergeColumn,"合并条件不能为空");
        final List<List<Map<String, Object>>> biggestList = getBiggestList(dataList);
        List<Map<String, Object>> sizer = biggestList.get(biggestList.size()-1);
        List<Map<String, Object>> result = new ArrayList<>(sizer.size());
        sizer.forEach(e->{
            List<Map<String,Object>> maps = new ArrayList<>();
            maps.add(e);
            for (int i = 0; i < biggestList.size()-1; i++) {
                List<Map<String, Object>> tmpMap = requireSizeOne(biggestList.get(i).stream()
                        .filter(k -> mergeTableCondition(k,e,mergeColumn))
                        .collect(Collectors.toList()), "匹配数据个数大于1");
                if (tmpMap.size()==0)continue;
                maps.add(tmpMap.get(0));
            }
            result.add(maps.stream().map(Map::entrySet).flatMap(Set::stream)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,(x,y)->x)));
        });
        return result;
    }

    public static <T> T requireSizeOne(T t,String msg){
        if (!(t instanceof List)){
            throw new IllegalArgumentException(" args must be List ");
        }
        if (((List)(t)).size()>1){
            throw new IllegalStateException(msg);
        }
        return t;
    }

    private static boolean mergeTableCondition(Map<String,Object> elementArg1,Map<String,Object> elementArg2,Object[] mergeColumn){
        for (int i = 0; i < mergeColumn.length; i++) {
            if (!Objects.equals(elementArg2.get(mergeColumn[i]),elementArg1.get(mergeColumn[i]))){
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private static List<List<Map<String,Object>>> getBiggestList(List<List<Map<String,Object>>> lists){
        return lists.stream().sorted((x, y) -> {
            if (x.size() > y.size()) return 1;
            if (x.size() == y.size()) return 0;
            return -1;
        }).collect(Collectors.toList());
    }

    public static <T> List<T> readResource(String path, Class<T> cla){
        try (
                InputStream resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(path)
                ){
            List<T> ts = JSON.parseArray(StreamUtils.copyToString(resourceAsStream, Charset.defaultCharset()), cla);
            LOGGER.info("=============加载资源文件{"+path+"}============");
            return ts;
        } catch (IOException e) {
            LOGGER.error(getExMessage(e));
            throw new IllegalStateException("无法加载资源文件{"+path+"}");
        }
    }


    public static List<Map<String, Object>> handlerConverge(List<Map<String, Object>> collectData) {
        List<Map<String, Object>> result = Lists.newArrayListWithCapacity(1);
        Map<String,Object> avg = Maps.newHashMapWithExpectedSize(64);
        //处理聚合数据
        Map<String,Object> biggestColumns = getBiggestMap(collectData);
        biggestColumns.keySet().forEach(e->{
            Object nothing = biggestColumns.get(e);
            deduceAndSet(collectData, avg, e, nothing);
        });
        result.add(avg);
        return result;
    }

    private static void deduceAndSet(List<Map<String, Object>> collectData, Map<String, Object> avg, String e, Object nothing) {
        if (nothing instanceof Integer){
            avg.put(e,collectData.stream().map(c->c.getOrDefault(e,0))
                    .collect(Collectors.averagingInt(Integer.class::cast)));
        }
        if (nothing instanceof BigDecimal){
            avg.put(e,collectData.stream().map(c->c.getOrDefault(e,BigDecimal.ZERO))
                    .collect(Collectors.averagingInt(v->BigDecimal.class.cast(v).intValue())));
        }
        if (nothing instanceof Double){
            avg.put(e,collectData.stream().map(c->c.getOrDefault(e,DOUBLE_ZERO))
                    .collect(Collectors.averagingDouble(Double.class::cast)));
        }
    }

    private static Map<String, Object> getBiggestMap(List<Map<String, Object>> collectData) {
        if(org.springframework.util.CollectionUtils.isEmpty(collectData))return Collections.emptyMap();
        Map<String,Object> biggest = collectData.get(0);
        for (int i = 1; i < collectData.size(); i++) {
            if (collectData.get(i).size()>biggest.size())
                biggest = collectData.get(i);
        }
        return biggest;
    }

    private static class MethodConvert{

        public static MethodConvert of(Class source,Class target){
            return new MethodConvert(source,target);
        }

        private Class source;
        private Class target;

        public MethodConvert(Class source, Class target) {
            this.source = source;
            this.target = target;
        }

        private MethodConvert(Builder builder) {
            setSource(builder.source);
            setTarget(builder.target);
        }

        public static Builder newBuilder() {
            return new Builder();
        }

        public static Builder newBuilder(MethodConvert copy) {
            Builder builder = new Builder();
            builder.source = copy.getSource();
            builder.target = copy.getTarget();
            return builder;
        }

        public Class getSource() {
            return source;
        }

        public void setSource(Class source) {
            this.source = source;
        }

        public Class getTarget() {
            return target;
        }

        public void setTarget(Class target) {
            this.target = target;
        }


        public static final class Builder {
            private Class source;
            private Class target;

            private Builder() {
            }

            public Builder source(Class source) {
                this.source = source;
                return this;
            }

            public Builder target(Class target) {
                this.target = target;
                return this;
            }

            public MethodConvert build() {
                return new MethodConvert(this);
            }
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MethodConvert that = (MethodConvert) o;
            return Objects.equals(source, that.source) &&
                    Objects.equals(target, that.target);
        }

        @Override
        public int hashCode() {
            return Objects.hash(source, target);
        }
    }

    private static class MethodDependency{
        private Class source;
        private Class target;
        private List<Method> smethods;
        private List<Method> tmethods;

        private MethodDependency(Builder builder) {
            setSource(builder.source);
            setTarget(builder.target);
            setSmethods(builder.smethods);
            setTmethods(builder.tmethods);
        }

        public static Builder newBuilder() {
            return new Builder();
        }

        public static Builder newBuilder(MethodDependency copy) {
            Builder builder = new Builder();
            builder.source = copy.getSource();
            builder.target = copy.getTarget();
            builder.smethods = copy.getSmethods();
            builder.tmethods = copy.getTmethods();
            return builder;
        }


        public Class getSource() {
            return source;
        }

        public void setSource(Class source) {
            this.source = source;
        }

        public Class getTarget() {
            return target;
        }

        public void setTarget(Class target) {
            this.target = target;
        }

        public List<Method> getSmethods() {
            return smethods;
        }

        public void setSmethods(List<Method> smethods) {
            this.smethods = smethods;
        }

        public List<Method> getTmethods() {
            return tmethods;
        }

        public void setTmethods(List<Method> tmethods) {
            this.tmethods = tmethods;
        }

        public static final class Builder {
            private Class source;
            private Class target;
            private List<Method> smethods;
            private List<Method> tmethods;

            private Builder() {
            }

            public Builder source(Class source) {
                this.source = source;
                return this;
            }

            public Builder target(Class target) {
                this.target = target;
                return this;
            }

            public Builder smethods(List<Method> smethods) {
                this.smethods = smethods;
                return this;
            }

            public Builder tmethods(List<Method> tmethods) {
                this.tmethods = tmethods;
                return this;
            }

            public MethodDependency build() {
                return new MethodDependency(this);
            }
        }
    }


    public static String getOrDefault(double condition,double defaultVar){
        if (condition == 0){
            return "--";
        }
        return String.format("%.2f", defaultVar);
    }

    public static String getOrDefaultPecent(double condition,double defaultVar){
        if (condition == 0){
            return "--";
        }
        return addPercentSign(defaultVar)+"%";
    }

    public static String addPercentSign(double d) {
        double v = d * 100;
        int k = (int) (d * 100);
        if (k == v) {
            return k + "";
        }
        return String.format("%.2f", v);
    }

}
