package com.pes.jd.util;

import com.pes.jd.model.DTO.ShopUserDTO;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 *  程序上下文
 *
 *  一定记得在后置过滤器中release掉
 *
 * <AUTHOR> <EMAIL>
 * @date 2018/11/9 10:50 AM
 * @since 1.0.0
 */
public class AppContext extends ConcurrentHashMap<String,Object> {

    protected static Class<AppContext> contextClass = AppContext.class;

    private final static ThreadLocal<? extends AppContext> THREAD_LOCAL = ThreadLocal.withInitial(() -> {
        try {
            return contextClass.newInstance();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    });

    public static AppContext currentContext(){
        return THREAD_LOCAL.get();
    }

    public String getSchema(){
        return (String) get("schema");
    }

    public Boolean getNickPerFormanceNullFlag(){
        return (Boolean) getOrDefault("nick_performance_flag",Boolean.FALSE);
    }
    public void setNickPerFormanceNullFlag(Boolean flag){
        put("nick_performance_flag",flag);
    }


    public boolean getMybatisMapFlag(){
        return (Boolean) (Objects.equals(get("mybatis_flag"),null)?Boolean.FALSE:Boolean.TRUE);
    }

    public void setUser(ShopUserDTO shopUserDTO){
        put("shop_user_dto",shopUserDTO);
    }

    public ShopUserDTO getUser(){
        ShopUserDTO dto = (ShopUserDTO) get("shop_user_dto");
        Assert.notNull(dto,"当前用户为空无法进行业务处理");
        return dto;
    }

    public void setSchema(String schema){
        put("schema",schema);
    }

    public void setMybatisMapFlag(boolean flag){
        put("mybatis_flag",flag);
    }

    public void release(){
        THREAD_LOCAL.remove();
    }

}
