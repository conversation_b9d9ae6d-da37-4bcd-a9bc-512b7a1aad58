package com.pes.jd.util;
import org.springframework.util.Assert;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> <EMAIL>
 * @since 1.0.0
 */
public abstract class CommonDateUtils {

    public static final ZoneId SYS_ZONEID;

    private static final String ERR_FORMAT = "参数date不能为空，格式化失败";

    private static final DateTimeFormatter FORMATTER_yyyyMMddHHmmss;

    static {
        SYS_ZONEID = ZoneId.systemDefault();
        FORMATTER_yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    }

    private static ZonedDateTime getZonedDateTime(Date date) throws DateTimeParseException {
        return date.toInstant().atZone(ZoneId.systemDefault());
    }

    /**
     * {@code date}是否在{@code balance}-{@code time}之后(包括相等)
     */
    public static boolean afterBalanceTime(Date date,Date balance,long time,TimeUnit timeUnit){
        balance = (Date) balance.clone();
        balance.setTime(balance.getTime()-timeUnit.toMillis(time));
        return date.after(balance) || date.getTime() == balance.getTime();
    }

    /**
     *  Date->String( rule: yyyy-MM-dd )
     * @param date
     * @return  example { 2018-10-25 }
     */
    public static String formatYMd(Date date) throws DateTimeParseException{
        return getZonedDateTime(Objects.requireNonNull(date,ERR_FORMAT)).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    public static Date now(){
        return new Date();
    }

    /**
     * Date->String   format rule{ yyyy-MM-dd HH:mm:ss }
     * @param date
     * @return
     */
    public static String formatYMdHms(Date date) throws DateTimeParseException{
        return getZonedDateTime(Objects.requireNonNull(date,ERR_FORMAT)).format(FORMATTER_yyyyMMddHHmmss);
    }

    /**
     * String->Date   format rule{ yyyy-MM-dd }
     * @param textDate
     * @return
     */
    public static Date parseYMd(String textDate) throws DateTimeParseException{
        Assert.isTrue(textDate.length()==10," The format string must match { yyyy-MM-dd } ");
        return Date.from(LocalDate.parse(
                textDate,DateTimeFormatter.ISO_LOCAL_DATE).atStartOfDay(SYS_ZONEID).toInstant());
    }

    /**
     * String->textDate   format rule{ HH-mm-ss }
     * @param textDate
     * @return
     */
    public static Date parseYMDHms(String textDate) throws DateTimeParseException, ParseException{
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Assert.isTrue(textDate.length()==8," The format string must match { yyyy-MM-dd HH:mm:ss } ");
        return f.parse(formatYMd(now())+" "+textDate);
    }

    public static LocalDateTime parseLocalDateTime(String textDate) throws DateTimeParseException{
        Assert.notNull(textDate,"textDate 不可以为空");
        boolean ten = textDate.length() == 10;
        boolean nineteen = textDate.length() == 19;
        Assert.isTrue((ten||nineteen)," The format string must match { yyyy-MM-dd } or { yyyy-MM-dd hh:mm:ss} ");
        if (ten) {
            return LocalDateTime.parse(textDate + " 00:00:00", FORMATTER_yyyyMMddHHmmss);
        }
        if (nineteen){
            return LocalDateTime.parse(textDate,FORMATTER_yyyyMMddHHmmss);
        }
        return null;
    }

    public static LocalDate parseLocalDate(String textDate) throws DateTimeParseException{
        Assert.isTrue(textDate!=null&&textDate.length()==10," The format string must match { yyyy-MM-dd } ");
        return LocalDate.parse(textDate);
    }

    public static LocalTime parseLocalTime(String textDate) throws DateTimeParseException{
        Assert.isTrue(textDate!=null&&textDate.length()==8," The format string must match { hh:mm:ss } ");
        return LocalTime.parse(textDate);
    }

    /**
     * String->Date   format rule{ yyyy-MM-dd }
     * @param textDate
     * @return
     */
    public static Date parseYMd(String textDate,DateTimeFormatter dateTimeFormatter) throws DateTimeParseException{
        Assert.isTrue(textDate.length()==10," The format string must match { yyyy-MM-dd } ");
        return Date.from(LocalDate.parse(
                textDate,dateTimeFormatter).atStartOfDay(SYS_ZONEID).toInstant());
    }

    /**
     * String->Date   format rule{ yyyy-MM-dd HH:mm:ss }
     * @param textDate
     * @return
     */
    public static Date parseYMdHms(String textDate) throws DateTimeParseException{
        Assert.isTrue(textDate.length()==19," The format string must match { yyyy-MM-dd HH:mm:ss } ");
        return Date.from(LocalDateTime.parse(
                textDate,FORMATTER_yyyyMMddHHmmss).atZone(SYS_ZONEID).toInstant());
    }

    /**
     *  add or lower days
     *  增加或者减少对应日期的天数，如果{@param period}为负数，那么就减少日期
     * @param date
     * @param period 增加或减少的天数
     * @return updated date 修改后的日期
     */
    public static Date getDateByPeriod(Date date, int period) throws  DateTimeParseException{
        return Date.from(LocalDateTime.ofInstant(
                Objects.requireNonNull(date,ERR_FORMAT).toInstant(),SYS_ZONEID)
                .plusDays(period).atZone(SYS_ZONEID).toInstant());
    }

    /**
     *  获取指定日期的开始时间 2018-02-02 12:12:12 -> 2018-02-02 00:00:00
     *  get begin datetime of the specify date
     * @param date specify date
     * @return updated date
     */
    public static Date getStartTimeOfDate(Date date) throws DateTimeParseException {
        return Date.from(LocalDateTime.ofInstant(
                Objects.requireNonNull(date,ERR_FORMAT).toInstant(),
                SYS_ZONEID).toLocalDate().atStartOfDay(SYS_ZONEID).toInstant());
    }

    /**
     *  获取指定日期的结束时间 2018-02-02 12:12:12 -> 2018-02-02 23:59:59
     *  get end datetime of the specify date
     * @param date
     * @return updated date
     */
    public static Date getEndTimeOfDate(Date date) throws DateTimeParseException {
        return Date.from(LocalDateTime.ofInstant(
                Objects.requireNonNull(date,ERR_FORMAT).toInstant(),SYS_ZONEID)
                .plusDays(1L).toLocalDate().atStartOfDay().minusSeconds(1L).atZone(SYS_ZONEID).toInstant());
    }

    /**
     *  获取指定日期一年前的时间  2018-02-02 12:12:12 -> 2017-02-02 12:12:12
     *  get pre year datetime of the specify date
     * @param date
     * @return updated date
     */
    public static Date getPreYearDate(Date date) throws DateTimeParseException {
        return Date.from(LocalDateTime.ofInstant(
                Objects.requireNonNull(date,ERR_FORMAT).toInstant(),
                SYS_ZONEID).minusYears(1L).atZone(SYS_ZONEID).toInstant());
    }

    public static List<LocalDate> splitForLocalDate(Date begin, Date end){
        LocalDateTime timeBegin = LocalDateTime.ofInstant(begin.toInstant(),SYS_ZONEID);
        LocalDateTime timeEnd = LocalDateTime.ofInstant(end.toInstant(),SYS_ZONEID);
        List<LocalDate> result = new ArrayList<>(20);
        LocalDate endLocalDate = timeEnd.toLocalDate();
        LocalDate beginLocalDate = timeBegin.toLocalDate();
        for (LocalDate i = endLocalDate; i.toEpochDay() >= beginLocalDate.toEpochDay(); i = i.minusDays(1L)) {
            result.add(i);
        }
        return result;
    }


    public static long split(Date begin,Date end){
        LocalDateTime localDateTimeBegin = LocalDateTime.ofInstant(begin.toInstant(),SYS_ZONEID);
        LocalDateTime localDateTimeEnd = LocalDateTime.ofInstant(end.toInstant(),SYS_ZONEID);
        LocalDate localDateBegin = localDateTimeBegin.toLocalDate();
        LocalDate localDateEnd = localDateTimeEnd.toLocalDate();
        long l = localDateBegin.toEpochDay();
        long k = localDateEnd.toEpochDay();
        return k-l;
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     * 
     * @param nowTime 当前时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     * <AUTHOR>
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

}
