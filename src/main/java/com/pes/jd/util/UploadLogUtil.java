package com.pes.jd.util;

import com.jd.open.api.sdk.JdClient;
import com.pes.jd.task.executor.UploadLogExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;

/**
 * @Author: aiJun
 * @Date: 2019-07-18 10:15
 * @Version 1.0
 */
public class UploadLogUtil {
    private static Logger logger = LoggerFactory.getLogger(UploadLogUtil.class);

    /**
     *
     * @param method            批量上传的日志记录名字：login, order， sendorder，sql
     * @param model             --传入封装参数的实体
     * @param sessionKey           --店铺sessionKey
     * @param timestamp             时间 System.currentTimeMillis(); long类型
     * @throws Exception
     */
    public static void uploadBatchLog(String method, Object model, String sessionKey, Object timestamp){
        ExecutorService uploadLogExecutorService = SpringUtil.getBean("uploadLogExecutorService", ExecutorService.class);
        uploadLogExecutorService.execute(new UploadLogExecutor(method,model,sessionKey,timestamp));
    }

    public static JdClient getClient(String sessionKey) {
        return ApiClientUtil.getClient(sessionKey);
    }

//    public static void main(String[] args) throws Exception{
//        OrderInfoLogTO orderInfoLogTO = new OrderInfoLogTO();
//        Date date = new Date();
//        orderInfoLogTO.setUserIp("***************.111");
//        orderInfoLogTO.setAppName("http://joyi.yiyitech.com");
//        orderInfoLogTO.setJosAppKey("321CEAB001F59FDDA67DC388C0575958");
//        orderInfoLogTO.setJdId("81477");
//        orderInfoLogTO.setDeviceId("16-4F-8A-DD-C5-84");
//        orderInfoLogTO.setUserId("");
//        orderInfoLogTO.setFileMd5("");
//        orderInfoLogTO.setOrderIds("9994816269223");
//        orderInfoLogTO.setOperation(1L);
//        orderInfoLogTO.setUrl("http://joyi.yiyitech.com/#/data-analysis/transaction-analysis/goods-sale-analysis/custom-goods-detail");
//        orderInfoLogTO.setTimeStamp(date.getTime());
//        uploadBatchLog("order",orderInfoLogTO,"", date);
//
//    }


//    public static Object getOrderBean() {
//        OrderInfoLogTO orderInfoLogTO = new OrderInfoLogTO();
//        Date date = new Date();
//        orderInfoLogTO.setUserIp("***************.111");
//        orderInfoLogTO.setAppName("http://joyi.yiyitech.com");
//        orderInfoLogTO.setJosAppKey("321CEAB001F59FDDA67DC388C0575958");
//        orderInfoLogTO.setJdId("81477");
//        orderInfoLogTO.setDeviceId("16-4F-8A-DD-C5-84");
//        orderInfoLogTO.setUserId("");
//        orderInfoLogTO.setFileMd5("");
//        orderInfoLogTO.setOrderIds("9994816269223");
//        orderInfoLogTO.setOperation(1L);
//        orderInfoLogTO.setUrl("http://joyi.yiyitech.com/#/data-analysis/transaction-analysis/goods-sale-analysis/custom-goods-detail");
//        orderInfoLogTO.setTimeStamp(date.getTime());
//        return orderInfoLogTO;
//    }

}
