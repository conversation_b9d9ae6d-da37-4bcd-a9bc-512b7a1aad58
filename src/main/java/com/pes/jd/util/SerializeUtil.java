package com.pes.jd.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

public class SerializeUtil {
	private final static Logger LOGGER = LoggerFactory.getLogger(SerializeUtil.class);
	
	public synchronized static byte[] serialize(Object object) {
		if(object==null){
			return null;
		}
		ObjectOutputStream oos = null;
		ByteArrayOutputStream baos = null;
		try {
			// 序列化
			baos = new ByteArrayOutputStream();
			oos = new ObjectOutputStream(baos);
			oos.writeObject(object);
			byte[] bytes = baos.toByteArray();
			return bytes;
		} catch (Exception e) {
			LOGGER.error(e.getMessage(),e);
		}finally{
			if(oos!=null){
				try {
					oos.close();
				} catch (IOException e) {
					LOGGER.error(e.getMessage(),e);
				}
			}
			if(baos!=null){
				try {
					baos.close();
				} catch (IOException e) {
					LOGGER.error(e.getMessage(),e);
				}
			}
		}
		return null;
	}

	public synchronized static Object unserialize(byte[] bytes) {
		if(bytes==null){
			return null;
		}
		ByteArrayInputStream bais = null;
		ObjectInputStream ois = null;
		try {
			// 反序列化
			bais = new ByteArrayInputStream(bytes);
			ois = new ObjectInputStream(bais);
			return ois.readObject();
		} catch (Exception e) {
			LOGGER.error(e.getMessage(),e);
		} finally{
			if(ois!=null){
				try {
					ois.close();
				} catch (IOException e) {
					LOGGER.error(e.getMessage(),e);
				}
			}
			if(bais!=null){
				try {
					bais.close();
				} catch (IOException e) {
					LOGGER.error(e.getMessage(),e);
				}
			}
		}
		return null;
	}
}
