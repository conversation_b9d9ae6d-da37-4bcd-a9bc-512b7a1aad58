package com.pes.jd.util;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.pes.jd.model.Query.JobDateQuery;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author: aiJun
 * @Date: 2021/7/21 0021 14:48
 * @desc： 业务时间工具类
 * @Version 1.0
 */
public final class ServiceDateUtil {

    /**
     * 往前重拉订单数据涉及的时间修改
     *
     * @param jobDate
     * @return
     */
    public static List<JobDateQuery> getNewJobDate(JobDateQuery jobDate) {
        List<JobDateQuery> jobDateList = Lists.newArrayList();
        if (jobDate.getNeedCalFinalData()) {
            for (int i = 2; i > 0; i--) {
                JobDateQuery newJobDate = new JobDateQuery();
                BeanUtil.copyProperties(jobDate, newJobDate, "date", "startDate", "endDate");
                newJobDate.setStartDate(DateUtils.getDateByPeriod(jobDate.getStartDate(), -i));
                newJobDate.setEndDate(DateUtils.getDateByPeriod(jobDate.getEndDate(), -i));
                newJobDate.setDate(DateUtils.parseYMd(DateUtils.formatYMd(newJobDate.getStartDate())));
                jobDateList.add(newJobDate);
            }
        }
        jobDateList.add(jobDate);
        return jobDateList;
    }

    /**
     * 计算新的重算店铺指标的时间
     *
     * @param jobDate
     * @param enquiryValidDurationTime
     * @param dates
     * @return
     */
    public static List<Date> calNewOrderIndexDate(JobDateQuery jobDate, Integer enquiryValidDurationTime, List<Date> dates) {
        if (enquiryValidDurationTime > 3) {
            return dates;
        }
        List<Date> newCalDate = new ArrayList<>();
        for (int i = 3; i >= 0; i--) {
            newCalDate.add(DateUtil.getDateByPeriod(jobDate.getDate(), -i));
        }
        return newCalDate;
    }

}
