package com.pes.jd.constants;

public final class PesConstants {


    /**
     * 落实下单
     */
    public static final int ORDER_BIND_TYPE_ORDER = 1;
    /**
     * 落实付款
     */
    public static final int ORDER_BIND_TYPE_PAY = 2;
    /**
     * 全静默算绩效
     */
    public static final int ORDER_BIND_TYPE_SILENTALL = 3;

    /**
     * 按聊天时间判定归属
     */
    public static final int JUDGE_RULE_ASCRIPTION_TYPE_CHAT = 1;
    /**
     * 按回合数判定归属
     */
    public static final int JUDGE_RULE_ASCRIPTION_TYPE_ROUND = 2;

    /**
     * 下单判定
     */
    public static final int JUDGE_RULE_ORDER = 1;
    /**
     * 下单优先判定
     */
    public static final int JUDGE_RULE_ORDER_FIRST = 2;

    /**
     * 付款判定
     */
    public static final int JUDGE_RULE_PAY = 3;
    /**
     * 默认有效的询单时长为2天
     */
    public static final Integer ENQUIRY_VALID_DURATION_TIME = 2;


    public static final Integer GOODS_CONSULT_ORDER_DAY_NUM = 1;


    /**
     * 下单到付款差1天
     */
    public static final Integer ORDER_TO_PAY_DAY = 1;

    public static final Integer ORDER_TO_PAY_DAY_COMPANYTURN = 7;

    /**
     * 根据预售订单付尾款时间询单时长为90
     */
    public static final Integer ORDER_PRESALE_PAY_BALANCE_DATE = 45;

    /**
     * 按退款申请时间询单时长
     */
    public static final Integer ORDER_REFUND_CREATED_DATE = 60;
    /**
     * 按退款完成时间询单时长
     */
    public static final Integer ORDER_REFUND_MODIFIED_DATE = 75;

    /**
     * 按退款完成时间询单时长-新版180天
     */
    public static final Integer ORDER_REFUND_MODIFIED_DATE_NEW = 180;
    public static final Integer ORDER_REFUND_MODIFIED_DATE_NEW_V2 = 200;
    public static final Integer ORDER_REFUND_MODIFIED_DATE_NEW_V3 = 365;



    /**
     * 按退款完成时间询单时长-新版90天
     */
    public static final Integer ORDER_REFUND_MODIFIED_DATE_NINE = 91;

    /**
     * 按退款完成时间询单时长-一年
     */
    public static final Integer ORDER_REFUND_MODIFIED_DATE_NEW_YEAR = 365;
    /**
     * 按退款成功时间 推 退款申请时间
     */
    public static final Integer ORDER_REFUND_MODIFIED_TO_CREATE = 19;

    /**
     * 最大有效的询单时长 15天
     */
    public static final int ENQUIRY_VALID_DURATION_TIME_MAX = 15;

    /**
     * 根据预约订单付尾款时间询单时长为48
     */
    public static final Integer ORDER_ADVANCE_PAY_BALANCE_DATE = 48;

    public final static Integer validPresaleOrderBalancePayDays = 90; //默认预售跨度90天(下单-付尾款)

}
