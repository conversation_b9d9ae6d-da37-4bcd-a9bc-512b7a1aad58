
package com.pes.jd.constants;

/**
 * ClassName:CommonConstants <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月24日 下午1:43:53 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
public final class CommonConstants {

    private CommonConstants() {
    }


    public static final Integer ROLE_USER = 2; // 子账号（包括服务商版和普通版）
    public static final Integer ROLE_COMPANY_ADMIN = 1; // 普通版本主账号
    public static final Integer ROLE_SYSTEM_ADMIN = 0; // 系统主账号
    public static final Integer ROLE_SERVICE_COMPANY_ADMIN = 3; // 服务商版主账号
    public static final Integer ROLE_REAL_TIME_COMPANY_ADMIN = 4;// 实时获取数据的店铺主账号角色
    public static final Integer ROLE_REAL_TIME_USER = 5; // 实时获取数据的店铺子账号角色

    public static final String ROLE_USER_NAME = "ROLE_USER"; // 子账号（包括服务商版和普通版）
    public static final String ROLE_COMPANY_ADMIN_NAME = "ROLE_COMPANY_ADMIN"; // 普通版本主账号
    public static final String ROLE_SYSTEM_ADMIN_NAME = "ROLE_SYSTEM_ADMIN"; // 系统主账号
    public static final String ROLE_SERVICE_COMPANY_ADMIN_NAME = "ROLE_SERVICE_COMPANY_ADMIN"; // 服务商版主账号
    public static final String ROLE_REAL_TIME_COMPANY_ADMIN_NAME = "ROLE_REAL_TIME_COMPANY_ADMIN";// 实时获取数据的店铺主账号角色
    public static final String ROLE_REAL_TIME_USER_NAME = "ROLE_REAL_TIME_USER"; // 实时获取数据的店铺子账号角色

    public static final String ARTICLE_CODE = "FW_GOODS-547810";
    public static final String ITEM_CODE_FOR_SHOP = "FW_GOODS-1967605-1";
    public static final String ITEM_CODE_FOR_SUPPLIER = "FW_GOODS-1967605-v2";
    // 限制子旺旺个数的版本
    // 专业版限制三个子旺旺
    public static final String ITEM_CODE_FOR_SHOP_3SUBUSER = "FW_GOODS-1967605-v3";

    // 预留两个位置
    public static final String ITEM_CODE_FOR_SHOP_XSUBUSER = "FW_GOODS-1967605-v4";
    public static final String ITEM_CODE_FOR_SHOP_X1SUBUSER = "FW_GOODS-1967605-v5 ";

    // 实时版
    public static final String ITEM_CODE_FOR_REAL_SHOP = "FW_GOODS-1967605-v6";
    public static final String ITEM_CODE_FOR_SUPPLIER_NEW = "FW_GOODS-1967605-v7 ";

    public static final String API_INVOKEPOTENTIAL_MSG = "您的问题可能由于以下原因引起：与淘宝服务器连接异常(由于部分数据需要从淘宝服务器获取，与淘宝服务器连接失败，可能导致系统故障，您可以尝试重新登录。";
    public static final String USER_STATUS_ACTIVE = "active";
    public static final String USER_STATUS_EXPIRED = "expired";

    public static final int RECALLAPI_TIMES = 5;
    public static final int CHATLOG_DATA_INSERT_NUM = 1000;
    public static final int TRADE_DATA_INSERT_NUM = 1000;
    public static final int CHATPEER_DATA_INSERT_NUM = 400;
    public static final int DATA_INITIALIZE_DEFAULT_DAYS = -16;
    public static final int BIG_DATA_INSERT_NUM = 1000;

    public static final int CUSTOMER_SERVICE_TYPE_PRE = 1;
    public static final int CUSTOMER_SERVICE_TYPE_POST = 2;

    public static final int SUB_USER_STATUS_NORMAL = 1;// 子账号当前状态 1正常
    public static final int SUB_USER_STATUS_DELETED = -1;// 子账号当前状态 -1删除
    public static final int SUB_USER_STATUS_FROZEN = 3;// 子账号当前状态 3冻结

    public static final Integer MAX_SUBUSER_NUM_OLD = 10000;

    // public static Integer MAX_SUBUSER_NUM_2 = 2;
    public static final Integer MAX_SUBUSER_NUM_3 = 3;

    /**
     * 店铺是否初始化
     */
    public static final Integer SHOP_INIT_DATA_YES = 1;
    public static final Integer SHOP_INIT_DATA_NO = 0;


    public static final int DEFAULT_EFFECTIVE_PAY_DAYS = 4;
    public static final int DEFAULT_EFFECTIVE_ORDER_DAYS = 1;
    /**
     * 落实下单
     */
    public static final String CSTRADE_TYPE_ORDER_OWNER = "落实下单";
    /**
     * 落实付款
     */
    public static final String CSTRADE_TYPE_PAY_OWNER = "落实付款";
    /**
     * assist_pay(协助催付款)
     */
    public static final String CSTRADE_TYPE_ASSIST_PAY = "assist_pay";// 协助催付款
    /**
     * ower_order_pay(落实订单并催付款)
     */
    public static final String CSTRADE_TYPE_ORDER_OWER_PAY = "ower_order_pay";// 落实订单并催付款
    /**
     * silent_order_pay(静默订单催付款)
     */
    public static final String CSTRADE_TYPE_SILENT_ORDER_PAY = "silent_order_pay";// 静默订单落实付款
    public static final String LOAD_DATA_INFILE_SEPARATOR = "``MYPES`";
    public static final String LOAD_DATA_INFILE_LINE_END = "`MYPES`\n";

    public static final int MAX_THREAD_NUM = 80;

    public static final int MIN_THREAD_CHATPEER_NUM = 80;

    public static final int SHARE_SHOP_MAX_THREAD_DEAL_NUM = 50;

    public static final int MIN_SHARE_SHOP_NUM = 20;

    public static final int GET_SHARE_SHOP_TIME = 10000;

    public static final int GET_REAL_TIME_TRADE_TIME = 10000;

    public static final String SHOP_GROUP_REQUEST_STATUS_NEW_INVITE_CREATED = "已发送邀请,等待对方接受";
    public static final String SHOP_GROUP_REQUEST_STATUS_INVITE_ACCEPTED = "对方已接受邀请";
    public static final String SHOP_GROUP_REQUEST_STATUS_INVITE_REJECTED = "对方拒绝接受邀请";

    public static final String SHOP_GROUP_REQUEST_STATUS_NEW_APPLY_CREATED = "已发送申请,等待对方核实";
    public static final String SHOP_GROUP_REQUEST_STATUS_APPLY_ACCEPTED = "对方已接受申请";
    public static final String SHOP_GROUP_REQUEST_STATUS_APPLY_REJECTED = "对方拒绝,申请失败";

    public static final double NARROW_RATE = 0.3d; // 图片缩小比例,原图片的0.1倍长和宽同时缩小

    // 错误信息
    public static final String INVAID_LOGIN_SOURCE = "无效的登录来源";
    public static final String RESULT_SUCCESS = "success";

    public static final String REAL_TIME_GET_DATA_EXCEPTION = "realTimeGetDataFailur";

    public static final String YES = "Y";
    public static final String NO = "N";
    public static final String ING = "I";

    public static final long ADMIN_SHOP_ID = 67202484;

    public static final String DEFAULT = "0000-00-00 00:00:00";

    public static final String WANG_PERMISSION = "150,151,152";
    public static final String SUB_USER_TYPE_MANAGE = "M";// 店铺管理员
    public static final String SUB_USER_TYPE_NO = "N";// 普通子账号
    // 暂定为90天
    public static final long MAX_CHATLOG_TIME = 7776000000l;
    // 交易记录的最长存储时间
    public static final long MAX_TRADE_TIME = 3024000000l;

    public static final String THIRD_PARTY_LOGIN = "/thirdPartyLogin";

    public static final String SPLIT_DATE = "2015-07-03";

    public static final String SPLIT_REFUND_DATE = "2018-01-09"; // 分表日期,1月9号数据存新表

    public static final String REFUND_APPLY = "A";

    public static final String REFUND_SUCCESS = "S";

    public static final String CNTAOBAO = "cntaobao";
    public static final int SHOP_FETCH_DATA_SUCCESS = 1;
    // 店铺初始化数据完成
    public static final int SHOP_INIT_DATA_FINISHED = 1;
    // 店铺初始化数据未完成,未初始化过
    public static final int SHOP_INIT_DATA_UNFINISHED = 0;
    // 店铺数据初始化中
    public static final int SHOP_INIT_DATA_ING = 2;
    // 退款状态
    public static final String FEFUND_SUCCESS = "SUCCESS";
    public static final String FEFUND_SELLER_AGREE = "WAIT_SELLER_AGREE";

    public static final String CS_TRADE_SPLIT_DATE = "2016-05-05";
    public static final String SQL_SEPARATOR = ",";
    public static final String SPLIT_SCHEMA_DATETIME = "2016-06-15";
    public static final String SPLIT_LOGINLOG_DATETIME = "2017-03-01";
    public static final String SPLIT_SCHEMA_YYYYMM = "2016-06";
    public static final String SPLIT_SCHEMA_YYYY_MM = "2016_06";
    public static final String SCHEMA_CUSTOMERSERVICE_PES = "pes_jd";
    public static final String SCHEMA_CUSTOMERSERVICE_PES1 = "customerservice_pes_sub1";
    public static final String SCHEMA_CUSTOMERSERVICE_PES2 = "customerservice_pes_sub2";
    public static final String SCHEMA_CUSTOMERSERVICE_PES3 = "customerservice_pes_sub3";
    public static final String SCHEMA_CUSTOMERSERVICE_PES4 = "customerservice_pes_sub4";
    public static final String SCHEMA_CUSTOMERSERVICE_PES5 = "customerservice_pes_sub5";
    public static final String PES_TRADE = ".pes_trade";
    public static final String ORIGIN = "origin";
    public static final String SCHEMA = "schema";
    public static final String SCHEMAID = "schemaId";
    public static final String WM_ADMIN_PASSWORD = "wm_jixiao";
    public static final String PES_ORDER_FILTER = ".pes_order_filter";
    public static final String PES_TRADE_DETAIL = ".pes_order_detail";

    public static final String TRADE_TYPE_STEP = "step";
    /*
     * 定金和尾款都付
     */
    public static final String STEP_TRADE_STATUS_FRONT_PAID_FINAL_PAID = "FRONT_PAID_FINAL_PAID";
    /*
     * 定金已付尾款未付
     */
    public static final String STEP_TRADE_STATUS_FRONT_PAID_FINAL_NOPAID = "FRONT_PAID_FINAL_NOPAID";
    /*
     * 定金未付尾款未付
     */
    public static final String STEP_TRADE_STATUS_FRONT_NOPAID_FINAL_NOPAID = "FRONT_NOPAID_FINAL_NOPAID";
    /*
     * 付款以前，卖家或买家主动关闭交易
     */
    public static final String TRADE_STATUS_TRADE_CLOSED_BY_TAOBAO = "TRADE_CLOSED_BY_TAOBAO";
    /*
     * 付款以后用户退款成功，交易自动关闭
     */
    public static final String TRADE_STATUS_TRADE_CLOSED = "TRADE_CLOSED";

    public static final String TRADE_TYPE = "step";
    public static final String FROM_QIANNIU = "qianniupc";
    public static final String QIANNIUPC_SLOT_QIANNIU = "qianniu";
    public static final String QIANNIUPC_SLOT_WW = "wangwang";
    public static final String CLENT_WEB = "web";
    public static final String CLENT_QN = "qn";
    public static final String CLENT_SWITCH = "switch";
    public static final int FETCH_DATA_ING = 1;
    public static final int FETCH_DATA_OVER = 0;

    // 订单往前天数
    public static final int PERIOD = -2;
    public static final int THREEPERIOD = -3;
    public static final int FOURPERIOD = -4;

    public static final int DAY_1 = 1;
    public static final int DAY_2 = 2;
    public static final int DAY_3 = 3;

    public static final int PES_DAY_ONE = 1;
    public static final int PES_DAY_THREE = 3;

    // 等待买家付款时间（有三天改为两天的时间节点）
    public static final String SPLITPAYDATE = "2018-02-07";

    // 预售订单往前查询天数
    public static final int DAY_NUMBER = 30;
    /**
     * 咨询未下单
     */
    public static final String ENQUIRY_NOT_XIADAN = "enquiry_not_xiadan";
    /**
     * 下单未付款
     */
    public static final String XIADAN_NOT_PAY = "xiadan_not_pay";
    /**
     * 已付款
     */
    public static final String XIADAN_AND_PAY = "xiadan_and_pay";
    public static final int STATUS_NO_OPERATION = 0;// 未操作
    public static final int STATUS_OPERATED = 1;// 已操作
    public static final int STATUS_IGNORE = 2;// 忽略
    public static final int STATUS_AUTO_OPT = 3;// 自己下单或者付款的待转化，则系统更新为自行下单或者付款

    public static final int STATUS_1 = 1;// 状态1
    public static final int STATUS_0 = 0;// 状态0
    public static final int BATCH_DELETE_LIMIT_NUM = 8000;
    public static final int BATCH_DELETE_LIMIT_NUM_400 = 400;
    public static final int BATCH_DELETE_LIMIT_NUM_200 = 200;
    public static final int SPLIT_GET_CHATPEER = 20; // 获取聊天对象20个客服一组

    /**
     * sso
     */
    public static final String TICKET_NAME = "jxTaobao-jxticket";
    public static final String SESSION_PREFIX = "jxsessions:";// 用户session
    public static final String OPT_TYPE_ADD = "add";
    public static final String OPT_TYPE_DELETE = "delete";
    public static final String OPT_TYPE_UPDATE = "update";
    public static final String OPT_TYPE_QUERY = "query";
    public static final String OPT_TYPE_MODIFIED = "modified";
    public static final String TYPE_CS = "cs";

    /**
     * 客服绩效查询字段
     */

    public static final String QUERYFIELD = "退款金额,退款率,客服平均评分,平均退款时长";
    public static final String REFUNDFEE = "退款金额";
    public static final String REFUNDRATE = "退款率";
    public static final String AVGEVALCODE = "客服平均评分";
    public static final String AVGINTERVALTIME = "平均退款时长";

    /**
     * 导出报表的类型
     */
    public static final String RECEPTIONRECORD = "receptionrecord"; // 接待记录
    public static final String CHENGJIAORECORD = "chengjiaorecord"; // 成交记录
    public static final String LOSSOFRECEPTION = "lossofreception"; // 接待流失
    public static final String SILENTLOSS = "silentloss"; // 静默流失
    public static final String REALTIMEJOB = "realtimejob"; // 静默流失

    /**
     * 数据分析报表类型
     */
    public static final String CUSTOMER_RECEPTION_ANALYSIS = "顾客接待分析";
    public static final String RECEPTION_FILTER_ANALYSIS = "接待过滤分析";

    public static final String GOODS_CONSULT_SUMMARY = "商品咨询汇总";
    public static final String GOODS_CONSULT_DETAIL = "商品咨询明细";

    public static final String GOODS_RECOMMEND_SUMMARY = "商品推荐汇总";
    public static final String GOODS_RECOMMEND_DETAIL = "商品推荐明细";

    public static final String SHOP_SALE_ANALYSIS = "店铺销售分析";
    public static final String CS_SALE_ANALYSIS = "客服销售分析";
    public static final String SILENCE_SALE_ANALYSIS = "静默销售分析";

    public static final String CS_GOODS_SALE_SUMMARY = "客服商品销售汇总";
    public static final String CS_GOODS_SALE_DETAIL = "客服商品销售明细";
    public static final String SILENT_GOODS_SALE_SUMMARY = "静默商品销售汇总";
    public static final String SILENT_GOODS_SALE_DETAIL = "静默商品销售明细";

    public static final String CS_REFUND_ANALYSIS = "客服退款分析";
    public static final String SILENT_REFUND_ANALYSIS = "静默退款分析";

    public static final String ASSIST_SERVICE_ANALYSIS = "协助服务分析";
    public static final String PRESALE_ORDER_ANALYSIS = "预售订单分析";

    public static final String ENQUIRY_LOST_ANALYSIS = "询单流失分析";
    public static final String ENQUIRY_ORDER_LOST_ANALYSIS = "询单下单未付款分析";
    public static final String SILENT_ORDER_LOST_ANALYSIS = "静默下单未付款分析";

    public static final String NON_REPLY_ANALYSIS = "未回复分析";
    public static final String LEAVE_MESSAGE_ANALYSIS = "留言分析";
    public static final String SLOW_RESP_ANALYSIS = "慢响应分析";
    public static final String LONG_RECEIVE_ANALYSIS = "长接待分析";
    public static final String CS_CHAT_LOG = "聊天记录导出";

    public static final String SATIS_ANALYSIS = "满意率分析";
    public static final String RECEPTION_NOT_SENDEVAL_ANALYSIS = "接待未邀评分析";
    public static final String DISLIKES_ANALYSIS = "中差评分析";
    public static final String RESERVE_ORDER_ANALYSIS = "预约订单分析";
    public static final String GOODS_RATE_ANALYSIS = "商品好评率";

    /**
     * 导出中心报表的类型
     */
    public static final Integer RECEPTIONRECORDTYPE = 1; // 接待记录
    public static final Integer CHENGJIAOTYPE = 2; // 成交记录
    public static final Integer LOSSOFRECEPTIONTYPE = 3; // 接待流失
    public static final Integer SILENTLOSSTYPE = 4; // 静默流失


    public static final Number PES_INDEX_NOT_EXIST_DEFAULT_VALUE = -1005;

    public static final Integer PLT_TAOBAO_TYPE = 1;

    public static final double CS_SCORE = 5;

    public static final int CHAT_PEER_STATUS_0 = 0;
    public static final int CHAT_PEER_STATUS_1 = 1;
    public static final int CHAT_PEER_STATUS_2 = 2;

    public static final int API_NUM_TOTAL_LEVEL_1 = 25000;
    public static final int API_NUM_TOTAL_LEVEL_2 = 115000;

    public static final int IP_TYPE_SUB = 3;

    public static final int FORWARD_NON = 0;
    public static final int FORWARD_IN = 1;
    public static final int FORWARD_OUT = 2;

    public static final int FAIL_SHOP_TYPE_4_JOB = 1;// 4点钟job
    public static final int FAIL_SHOP_TYPE_LOGINLOG = 2;// 考勤job

    public static final int LOGINLOG_TYPE_1 = 1;// 登录记录type:上线
    public static final int LOGINLOG_TYPE_2 = 2;// 登录记录type:下线

    public static final int TESTUSETIME = 15;

    public static final Integer EFFECT_STATUS=1;//有效的
    public static final Integer VALID_STATUS=2;//失效的session
    public static final String RT_SUB_SCHEMA="pes_jd_sub1_rt";
    public static final String SHOPPERFORMANCE = "shopSaleAmount,shopSilentSaleAmount,shopPaidTradeNum,shopSaleBuyerNum,shopConsignNum,shopPerCusPrice,shopCreatedTradeAmount,shopConfirmGoodsAmount,consultNum,receiveNum,enquiryNum,finalPaidNum,perCusPrice,saleAmount,csSaleRate,saleOrderNum,saleBuyerNum,saleGoodsNum,avgResponseTime,avgWaitTime,noReplyNum,recoveryRate,qaRate,enquiryToOrdered,orderedToPaid,enquiryToPaid,dsrItemRating,dsrServiceRating,dsrDeliveryRating";


	public static final String DD_PC="DD_PC";//区分咚咚插件
	
	public static final Integer webFlag=1;//web
	public static final Integer pluginFlag=0;//插件
    public static final Integer mobileFlag=2;//插件

    //----------实时看板全局监控是否查看明细
    public static final Integer REALTIME_BOARD_SELET_DETAIL=1;//查看明细


    //对店铺是拉取还是计算操作进行区分
    public static final String SHOP_DATA_PULL = "pull";
    public static final String SHOP_DATA_CAL = "cal";

    //对实时数据拉取
    public static final String RT_SHOP_DATA_SELECT = "select";
    public static final String RT_SHOP_DATA_PULL = "pull";


	public static final String SKUID_URL="http://item.jd.com/%s.html?utm_source=kong&utm_medium=cpc&utm_campaign=t_1001464481_kfmf";

	public static final String version_all_zhanghao="不限账号";

	public static final String version_3_zhanghao="3个账号";

	public static final String version_all_kefu="不限客服";

	public static final String version_3_kefu="3个客服";

    public static final String version_3_kefu_jichu="3个客服基础版";

    public static final String version_all_kefu_jichu="不限客服基础版";

    /**
     * 管理员类型
     */
    public static final String TYPE_0 = "0";

    /**
     * 一级部门类型
     */
    public static final String TYPE_1 = "1";

    /**
     * 二级部门类型
     */
    public static final String TYPE_2 = "2";

    /**
     *三级部门类型
     */
    public static final String TYPE_3 = "3";

    /**
     *四级部门类型
     */
    public static final String TYPE_4 = "4";

    /**
     * 自营类型
     */
    public static final Integer SELF_TYPE = 1;

    /**
     * 看板模板的值
     */
    public static final String FILER_VALUE = "0";

    /**
     * pop类型
     */
    public static final Integer POP_TYPE = 0;


    /**
     * 自营类型code
     */
    public static final String SELF_TYPE_CODE = "FW_GOODS-908622-5";

    /**
     * 警告类型
     */
    public static final Byte WARNING_CS_VIOLATION = 2; //2-客服违规
    public static final Byte WARNING_BUYER_RAIL = 3; //3-顾客辱骂

    /**
     * 商品营销管理
     */
    //维度
    public static final String DIMENSION_SKU = "SKU";
    public static final String DIMENSION_SPU = "SPU";

    /**
     * 打点类型 0：不需要数据入库
     */
    public static final String DOT_TYPE_0 = "0";

    /**
     * 打点类型 1：需要数据入库
     */
    public static final String DOT_TYPE_1 = "1";

    public static final Integer COMMON_1 = 1;

    public static Integer DD_SEND = 2;
    public static Integer SMS_SEND = 3;
    public static Integer DD_SEND_TYPE = 1;
    public static Integer SMS_SEND_TYPE = 2;
//    通用商品 1 指定2
    public static final Integer SCOPE_COMMON = 1;
    public static final Integer SCOPE_SPECIFY = 2;
    public static final Integer DIMENSION_SKU_INTEGET = 1;
    public static final Integer DIMENSION_SPU_INTEGET = 2;

    public static final String SMART_FOLLOWUP_ONLINE_DATE = "2021-03-02 23:59:59";
    public static final String SMART_FOLLOWUP_TYPE_DDHAND = "1";
    public static final String SMART_FOLLOWUP_TYPE_DDAUTO = "2";
    public static final String SMART_FOLLOWUP_TYPE_SMS = "3";

    //店铺类型 1:自营 2.pop
    public static final int SHOP_TYPE_SELF = 1;
}
  
