package com.pes.jd.dao;

import com.pes.jd.model.DO.UserPortraitAiReport;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户画像AI分析报告表 DAO接口
 * <AUTHOR>
 */
public interface UserPortraitAiReportDao {

    /**
     * 插入用户画像AI报告数据
     * @param report 用户画像AI报告数据
     * @param schemaId 数据库schema
     * @return 插入行数
     */
    int insertUserPortraitAiReport(UserPortraitAiReport report, String schemaId);

    /**
     * 批量插入用户画像AI报告数据
     * @param reportList 用户画像AI报告数据列表
     * @param schemaId 数据库schema
     * @return 插入行数
     */
    int batchInsertUserPortraitAiReport(List<UserPortraitAiReport> reportList, String schemaId);

    /**
     * 根据店铺ID和日期删除用户画像AI报告数据
     * @param shopId 店铺ID
     * @param reportDate 报告日期
     * @param schemaId 数据库schema
     * @return 删除行数
     */
    int deleteUserPortraitAiReportByShopIdAndDate(Long shopId, LocalDate reportDate, String schemaId);

    /**
     * 根据店铺ID和日期范围删除用户画像AI报告数据
     * @param shopId 店铺ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param schemaId 数据库schema
     * @return 删除行数
     */
    int deleteUserPortraitAiReportByShopIdAndDateRange(Long shopId, LocalDate startDate, LocalDate endDate, String schemaId);

    /**
     * 根据店铺ID删除所有用户画像AI报告数据
     * @param shopId 店铺ID
     * @param schemaId 数据库schema
     * @return 删除行数
     */
    int deleteAllUserPortraitAiReportByShopId(Long shopId, String schemaId);

    /**
     * 根据主键ID查询用户画像AI报告数据
     * @param id 主键ID
     * @param schemaId 数据库schema
     * @return 用户画像AI报告数据
     */
    UserPortraitAiReport selectUserPortraitAiReportById(Long id, String schemaId);

    /**
     * 根据店铺ID和日期范围查询用户画像AI报告数据
     * @param shopId 店铺ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param schemaId 数据库schema
     * @return 用户画像AI报告数据列表
     */
    List<UserPortraitAiReport> selectUserPortraitAiReportByShopIdAndDateRange(Long shopId, LocalDate startDate, LocalDate endDate, String schemaId);

    /**
     * 根据店铺ID、报告类型和图表类型查询用户画像AI报告数据
     * @param shopId 店铺ID
     * @param reportType 报告类型
     * @param chartType 图表类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param schemaId 数据库schema
     * @return 用户画像AI报告数据列表
     */
    List<UserPortraitAiReport> selectUserPortraitAiReportByConditions(Long shopId, Integer reportType, Integer chartType, LocalDate startDate, LocalDate endDate, String schemaId);
}
