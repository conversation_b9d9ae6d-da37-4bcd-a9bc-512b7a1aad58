package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.CsGoodsSaleIndexDTO;
import com.pes.jd.model.Param.ShopCommonParam;

import java.util.Date;
import java.util.List;

public interface CsGoodsSaleIndexDao {

	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexCountByCsNickBySku(ShopCommonParam shop, Date startDate,
                                                                              Date endDate, List<String> csNickList, List<Long> skuLst);

	public List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexByCsNickByDateBySku(ShopCommonParam shop, Date startDate,
                                                                               Date endDate, Long skuId, String csNick);

	List<CsGoodsSaleIndexDTO> selectCsGoodsSaleIndexCountByCsNickBySkuV2(ShopCommonParam shop, Date startDate, Date endDate, List<String> csNickList, List<Long> skuLst, List<Long> categoryIds);
}
