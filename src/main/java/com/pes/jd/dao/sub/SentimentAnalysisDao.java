package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.SentimentAnalysisDTO;

import java.util.Date;
import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 10:42 2019/11/13
 * @Description:
 */
public interface SentimentAnalysisDao {

	List<SentimentAnalysisDTO> selectSentimentAnalysisByShopAndDate(List<String> schemaIdLst, String nick,
                                                                    Integer warningType,
                                                                    String keyword,
                                                                    Integer status,
                                                                    Date startDate,
                                                                    Date endDate);


	int auditSentimentAnalysis(String schemaId, Long id, Integer sentimentType);

	int batchAuditSentimentAnalysis(List<String> schemaIds, String ids, Integer sentimentType);
}
  
