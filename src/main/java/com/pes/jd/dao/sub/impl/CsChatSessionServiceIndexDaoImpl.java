package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsChatSessionServiceIndexDao;
import com.pes.jd.mapper.sub.CsChatSessionServiceIndexMapper;
import com.pes.jd.model.DTO.CsChatSessionServiceIndexDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/14 3:37 PM
 * @since 1.0.0
 */
@Repository
public class CsChatSessionServiceIndexDaoImpl implements CsChatSessionServiceIndexDao {

    @Autowired
    private CsChatSessionServiceIndexMapper csChatSessionServiceIndexMapper;

    @Override
    public List<CsChatSessionServiceIndexDTO> searchByDateShopCs(
            Set<String> nicks, Long shopId, Date startDate, Date endDate, String queryType, String schemaId, Set<Date> filterDates) {
        return CommonUtils.tablesMerge(
                startDate,endDate,schemaId,TableEnum.PES_CS_CHAT_SESSION_SERVICE_INDEX.getName(),
                (query)->csChatSessionServiceIndexMapper.searchByDateShopCs(
                        nicks,shopId,query.getBeginDate(),query.getEndDate(),queryType,query.getTableName(),filterDates
                ), CommonUtils.MergeType.YEAR
        );
    }

    @Override
    public List<CsChatSessionServiceIndexDTO> selectCsChatSessionServiceIndexByCsNickByDateForRealTime(ShopQuery shop,
                                                                                                       List<String> csNickLst, Date startDate, Date endDate) {

        String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION_SERVICE_INDEX.getName());
        return csChatSessionServiceIndexMapper.selectCsChatSessionServiceIndexByCsNickByDateForRealTime(shop.getShopId(),csNickLst, startDate, endDate, tableName);
    }
}
