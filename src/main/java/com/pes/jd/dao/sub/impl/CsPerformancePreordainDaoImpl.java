package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsPerformancePreordainDao;
import com.pes.jd.mapper.sub.CsPerformancePreordainMapper;
import com.pes.jd.model.DTO.CsPerformancePreordainDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/3/12
 * @Modified By:
 */
@Repository
public class CsPerformancePreordainDaoImpl implements CsPerformancePreordainDao {

    @Autowired
    private CsPerformancePreordainMapper csPerformancePreordainMapper;

    @Override
    public List<CsPerformancePreordainDTO> selectCsPerformancePreordainDaily(ShopCommonParam shop,List<String> csNick,Date startDate, Date endDate, String sku,String activityId){
        String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_PERFORMANCE_PREORDAIN.getName());
        return csPerformancePreordainMapper.selectCsPerformancePreordainDaily(shop.getShopId(),csNick,startDate,endDate,sku, activityId,tableName);
    }

    @Override
    public List<CsPerformancePreordainDTO> selectPerformancePreordain(ShopCommonParam shop, List<Long> skuIds, Date startDate, Date endDate,String sku,String activityId) {
        String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_PERFORMANCE_PREORDAIN.getName());
        return csPerformancePreordainMapper.selectPerformancePreordain(shop.getShopId(),skuIds,startDate,endDate,sku, activityId,tableName);
    }

    @Override
    public List<Long> selectPerformancePreordainSkuId(ShopCommonParam shop, List<Long> skuIds, Date startDate, Date endDate,String sku,String activityId) {
        return csPerformancePreordainMapper.selectPerformancePreordainSkuId(shop.getShopId(),skuIds,startDate,endDate,sku, activityId);
    }

    @Override
    public List<CsPerformancePreordainDTO> selectPerformancePreordainDetailed(ShopCommonParam shop, Date startDate, Date endDate,String csNick, Long skuId,String skuName,String activityId) {
        String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_PERFORMANCE_PREORDAIN.getName());
        return csPerformancePreordainMapper.selectPerformancePreordainDetailed(shop.getShopId(),startDate,endDate,csNick,skuId,skuName, activityId, tableName);
    }

    @Override
    public List<CsPerformancePreordainDTO> selectCsPerformancePreordain(ShopCommonParam shop, List<String> csNick, Date startDate, Date endDate, String sku,String activityId) {
        return csPerformancePreordainMapper.selectCsPerformancePreordain(shop.getShopId(),csNick,startDate,endDate,sku, activityId);
    }
}
