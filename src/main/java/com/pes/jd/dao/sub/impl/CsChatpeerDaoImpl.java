  
package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.sub.CsChatpeerDao;
import com.pes.jd.mapper.sub.CsChatpeerMapper;
import com.pes.jd.model.DO.CsChatpeer;
import com.pes.jd.model.DTO.ChatPeerDTO;
import com.pes.jd.model.DTO.CustomerReceiveDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.CustomerReceiveParam;
import com.pes.jd.model.Param.ReceiveParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**  
 * ClassName:CsChatpeerDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月12日 下午3:57:47 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository
public class CsChatpeerDaoImpl implements CsChatpeerDao {

	
	@Autowired
	private CsChatpeerMapper csChatpeerMapper;
	@Override
	public int deleteCsChatpeerById(Long id) {

		// TODO Auto-generated method stub  
		return 0;
	}

	@Override
	public int insertCsChatpeer(CsChatpeer record) {

		// TODO Auto-generated method stub  
		return 0;
	}

	@Override
	public int updateCsChatpeerById(CsChatpeer record) {

		// TODO Auto-generated method stub  
		return 0;
	}

	@Override
	public CsChatpeer getCsChatpeerById(Long id) {

		// TODO Auto-generated method stub  
		return null;
	}

	@Override
	public List<ChatPeerDTO> selectChatPeerByCsNickByBuyerByDateForReceiveFilter(ShopCommonParam shop,List<String> csNickLst, Date startDate, Date endDate,
			String buyer) {
		if (CollectionUtils.isEmpty(csNickLst)) {
			return new ArrayList<ChatPeerDTO>(0);
		}
		List<DateRangeParam> tableNams = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),TableEnum.PES_CS_CHATPEER.getName());
		List<ChatPeerDTO> chatPeerLst = csChatpeerMapper.selectChatPeerByCsNickByBuyerByDateForReceiveFilter(
				shop.getShopId(), csNickLst, tableNams, buyer);
		return chatPeerLst;
	}
	@Override
	public Integer	selectChatPeersCountByCsNickByBuyerByDateForReceiveFilter(ShopCommonParam shop,List<String> csNickLst, Date startDate, Date endDate,
			String buyer){
		if (CollectionUtils.isEmpty(csNickLst)) {
			return 0;
		}
		Integer countTotal=0;
		List<DateRangeParam> tableNams = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),TableEnum.PES_CS_CHATPEER.getName());
		for (DateRangeParam dateRangeParam : tableNams) {
			Integer count=csChatpeerMapper.selectChatPeersCountByCsNickByBuyerByDateForReceiveFilter(shop.getShopId(), csNickLst, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), dateRangeParam.getTableName(), buyer);;
			if(count==null){
				count=0;
			}
			countTotal+=count;
		}
		return countTotal;
	}
	@Override
	public List<String> selectReceiveChatpeerLst(ShopCommonParam shop, List<UserQuery> csNickLst, Date startDate, Date endDate,
			String buyerNickKeyword) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return new ArrayList<String>(0);
		}
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		List<String> retChatPeerLst = Lists.newArrayList();
		for (DateRangeParam drp : tableNames) {
			List<String> queryChatPeerLst = csChatpeerMapper.selectReceiveChatpeer(shop.getShopId(),csNickLst, drp.getBeginDate(), drp.getEndDate(), drp.getTableName(), buyerNickKeyword);
			if(CollectionUtils.isNotEmpty(queryChatPeerLst)){
				retChatPeerLst.addAll(queryChatPeerLst);
			}
		}
		return retChatPeerLst;
	}
	
	@Override
	public List<String> selectCsBuyerChatpeersByParamFromChatlog(ShopCommonParam shop, List<UserQuery> csNickLst, String buyerNickKeyword,
			Date startDate, Date endDate, String keyword) {

		if(CollectionUtils.isEmpty(csNickLst)){
			return new ArrayList<String>(0);
		}
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATLOG.getName());
		List<String> retChatPeerLst = Lists.newArrayList();
		for (DateRangeParam drp : tableNames) {
			List<String> queryChatPeerLst = csChatpeerMapper.selectCsBuyerChatpeersByParamFromChatlog(shop.getShopId(),csNickLst, drp.getBeginDate(), drp.getEndDate(), drp.getTableName(), buyerNickKeyword, keyword);
			if(CollectionUtils.isNotEmpty(queryChatPeerLst)){
				retChatPeerLst.addAll(queryChatPeerLst);
			}
		}
		return retChatPeerLst;
	}
	@Override
	public List<ChatPeerDTO> selectBuyerChatPeerLstByDate(ShopDTO shop, Date adjustSDate, Date endDate, String buyerNick) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName(), String.valueOf(shop.getShopId()));
		return csChatpeerMapper.selectBuyerChatPeerLstByDate(shop.getShopId(),adjustSDate,endDate,buyerNick,tableName);
	}

	@Override
	public Set<String> selectBuyerNicksByDateAndBuyerNickForLostRecord(ShopDTO shop, Date adjustSDate, Date endDate,
			String buyerNick) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName(), String.valueOf(shop.getShopId()));
		return csChatpeerMapper.selectBuyerNicksByDateAndBuyerNickForLostRecord(shop.getShopId(),adjustSDate,endDate,buyerNick,tableName);
	}

	@Override
	public List<ChatPeerDTO> selectNonOrdered(String tableName, String shopId, String buyerNick, Date startDate, Date endDate,Set<String> nicks) {
//		return csChatpeerMapper.selectNonOrdered(tableName, Long.parseLong(shopId), buyerNick, startDate, endDate,nicks);
		return null;
	}

	@Override
	public List<CustomerReceiveDTO> selectChatPeerByDateAndNickAndReceiveParam(ShopCommonParam shop, List<String> csNickLst,
			ReceiveParam param, Date startDate, Date endDate,SortPageQuery sortPageQuery) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return new ArrayList<CustomerReceiveDTO>(0);
		}
		List<DateRangeParam> cpTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		List<CustomerReceiveDTO> peerlst=	csChatpeerMapper.selectChatPeerByDateAndNickAndReceiveParam(shop.getShopId(), csNickLst, param,cpTableNames,sortPageQuery);
		return peerlst;
	}

	@Override
	public Integer selectChatPeerCountByDateAndNickAndReceiveParam(ShopCommonParam shop, List<String> csNickLst,
			ReceiveParam param, Date startDate, Date endDate) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return 0;
		}
		List<DateRangeParam> cpTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		return	csChatpeerMapper.selectChatPeerCountByDateAndNickAndReceiveParam(shop.getShopId(), csNickLst, param, cpTableNames);
	}
	@Override
	public List<CustomerReceiveDTO> selectChatPeerByCsNickByBuyerByDateForCustomerReceiveAll(ShopCommonParam shop, CustomerReceiveParam param,SortPageQuery sortPageQuery) {
		if(CollectionUtils.isEmpty(param.getCsNickLst())){
			return new ArrayList<CustomerReceiveDTO>(0);
		}
		List<DateRangeParam> tableNames=CommonUtils.getTableNames(param.getStartDate(), param.getEndDate(), shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		return csChatpeerMapper.selectChatPeerByCsNickByBuyerByDateForCustomerReceive(shop.getShopId(),param.getCsNickLst(), param.getBuyerNick(),tableNames,sortPageQuery);
	}

	@Override
	public int selectCountByCsNickByBuyerByDateForCustomerReceiveAll(ShopCommonParam shop, CustomerReceiveParam param) {
		if(CollectionUtils.isEmpty(param.getCsNickLst())){
			return 0;
		}
		List<DateRangeParam> tableNames=CommonUtils.getTableNames(param.getStartDate(), param.getEndDate(), shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		return csChatpeerMapper.selectCountByCsNickByBuyerByDateForCustomerReceive(shop.getShopId(),param.getCsNickLst(), param.getBuyerNick(),tableNames);
	}
	@Override
	public List<CustomerReceiveDTO> selectChatPeerByCsNickByBuyerLstByDate(ShopCommonParam shop,List<String> buyerNickLst, String csNick,Date startDate,Date endDate) {
		if(CollectionUtils.isEmpty(buyerNickLst)){
			return new ArrayList<CustomerReceiveDTO>(0);
		}
		List<CustomerReceiveDTO> result=Lists.newArrayList();
		List<DateRangeParam> tableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		for (DateRangeParam tableName : tableNames) {
			List<CustomerReceiveDTO> list=	csChatpeerMapper.selectChatPeerByCsNickByBuyerLstByDate(shop.getShopId(),buyerNickLst,csNick,tableName.getBeginDate(),tableName.getEndDate(),tableName.getTableName());
			if(CollectionUtils.isNotEmpty(list)){
				result.addAll(list);
			}
		}
		return  result;
	}

}
  
