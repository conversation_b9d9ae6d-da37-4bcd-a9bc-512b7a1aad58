/**  
 * Project Name:jd-pes  
 * File Name:TradeEvaluateDaoImpl.java  
 * Package Name:com.pes.jd.dao.impl  
 * Date:2018年10月24日下午7:26:59  
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.OrderEvaluateDao;
import com.pes.jd.mapper.sub.OrderEvaluateMapper;
import com.pes.jd.model.DO.OrderEvaluate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**  
 * ClassName:OrderEvaluateDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月24日 下午7:26:59 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository
public class OrderEvaluateDaoImpl implements OrderEvaluateDao {

	@Autowired
	private OrderEvaluateMapper orderEvaluateMapper;

	@Override
	public int deleteTradeEvaluateById(OrderEvaluate key) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int insertTradeEvaluate(OrderEvaluate record) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public int updateTradeEvaluateById(OrderEvaluate record) {
		// TODO Auto-generated method stub
		return 0;
	}

	@Override
	public OrderEvaluate getTradeEvaluateById(OrderEvaluate key) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<Map<String, Object>> selectByDateShopTypeNick(Map<String, Object> param) {
		return orderEvaluateMapper.selectDateShopNick(param);
	}


}
  
