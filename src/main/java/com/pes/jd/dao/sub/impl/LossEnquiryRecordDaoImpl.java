package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.LossEnquiryRecordDao;
import com.pes.jd.mapper.sub.LossEnquiryRecordMapper;
import com.pes.jd.model.DTO.CustomerReceiveDTO;
import com.pes.jd.model.DTO.LossEnquiryRecordDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ReceiveParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class LossEnquiryRecordDaoImpl implements LossEnquiryRecordDao {

	
	@Autowired
	private LossEnquiryRecordMapper lossEnquiryRecordMapper;

	@Override
	public List<CustomerReceiveDTO> selectEnquiryLossByDateAndCsNickForCustReceiveEnquiryLoss(ShopCommonParam shop,
			List<String> csNickLst, Date startDate, Date endDate,ReceiveParam receiveParam,SortPageQuery sortPageQuery) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return new ArrayList<CustomerReceiveDTO>(0);
		}
		List<DateRangeParam> elTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_LOSS_ENQUIRY_RECORD.getName());
		List<DateRangeParam> cpTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		
		List<CustomerReceiveDTO> crLst=lossEnquiryRecordMapper.selectEnquiryLossByDateAndCsNickForCustReceiveEnquiryLoss(shop.getShopId(), csNickLst,receiveParam,elTableNames,cpTableNames,sortPageQuery);
		return crLst;
	}

	@Override
	public Integer selectEnquiryLossCountByDateAndCsNickForCustReceiveEnquiryLoss(ShopCommonParam shop,
			List<String> csNickLst, Date startDate, Date endDate,ReceiveParam receiveParam) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return 0;
		}
		List<DateRangeParam> elTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_LOSS_ENQUIRY_RECORD.getName());
		List<DateRangeParam> cpTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHATPEER.getName());
		
		Integer count=lossEnquiryRecordMapper.selectEnquiryLossCountByDateAndCsNickForCustReceiveEnquiryLoss(shop.getShopId(), csNickLst,receiveParam,elTableNames,cpTableNames);
		return count;
	}

	@Override
	public List<LossEnquiryRecordDTO> selectEnquiryLossByDateAndCsNickForCustReceiveAll(ShopCommonParam shop,
			List<String> csNickLst, String buyerNick, Date startDate, Date endDate) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return new ArrayList<LossEnquiryRecordDTO>(0);
		}
		List<DateRangeParam> elTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_LOSS_ENQUIRY_RECORD.getName());
		return lossEnquiryRecordMapper.selectEnquiryLossByDateAndCsNickForCustReceiveAll(shop.getShopId(), csNickLst, buyerNick, elTableNames);
	}
	@Override
	public List<CustomerReceiveDTO> selectEnquiryLossByDateAndCsNickForEnquiryLoss(ShopCommonParam shop,List<String> csNickLst, Date startDate, Date endDate,ReceiveParam receiveParam,SortPageQuery sortPageQuery) {
		if(CollectionUtils.isEmpty(csNickLst)){
			return new ArrayList<CustomerReceiveDTO>(0);
		}
		List<DateRangeParam> elTableNames=CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_LOSS_ENQUIRY_RECORD.getName());

		List<CustomerReceiveDTO> crLst=lossEnquiryRecordMapper.selectEnquiryLossByDateAndCsNickForEnquiryLoss(shop.getShopId(), csNickLst,receiveParam,elTableNames,sortPageQuery);
		return crLst;
	}

}
