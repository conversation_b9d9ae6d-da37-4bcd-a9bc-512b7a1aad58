package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.PresaleActivityDao;
import com.pes.jd.mapper.sub.PresaleActivityMapper;
import com.pes.jd.model.DTO.PresaleActivityDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class PresaleActivityDaoImpl implements PresaleActivityDao {

    @Resource
    private PresaleActivityMapper presaleActivityMapper;

    @Override
    public List<PresaleActivityDTO> selectPresaleActivity(ShopCommonParam shop, Date date, String activityId) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), date, TableEnum.PES_PRESALE_ACTIVITY.getName());
        return presaleActivityMapper.selectPresaleActivity(tableName, shop.getShopId(), activityId);

    }

    @Override
    public List<PresaleActivityDTO> selectPresaleActivityNow(ShopCommonParam shop) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_PRESALE_ACTIVITY.getName());
        return presaleActivityMapper.selectPresaleActivityNow(tableName, new Date(), shop.getShopId());

    }

    @Override
    public List<PresaleActivityDTO> selectShopReservePresaleByActId(ShopCommonParam shop, List<String> preIds) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_PRESALE_ACTIVITY.getName());
        return presaleActivityMapper.selectShopReservePresaleByActId(tableName, shop.getShopId(), preIds);
    }

    @Override
    public List<PresaleActivityDTO> selectShopPresaleActivityByShopId(ShopCommonParam shop) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_PRESALE_ACTIVITY.getName());
        return presaleActivityMapper.selectShopPresaleActivityByShopId(tableName, shop.getShopId());

    }


    @Override
    public List<PresaleActivityDTO> selectByShopIdAndDateForPresalePerformance(ShopQuery shop, Date start, Date end) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_PRESALE_ACTIVITY.getName());
        return presaleActivityMapper.selectByShopIdAndDateForPresalePerformance(tableName, shop.getShopId(), start, end);
    }
    @Override
    public List<PresaleActivityDTO> selectByShopIdAndSkuId(ShopQuery shop, Long skuId) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_PRESALE_ACTIVITY.getName());
        return presaleActivityMapper.selectByShopIdAndSkuId(tableName, shop.getShopId(), skuId);
    }

	@Override
	public List<PresaleActivityDTO> selectShopPresaleActivityByShopIdByActiveityId(ShopCommonParam shop, String activityId) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_PRESALE_ACTIVITY.getName());
		return presaleActivityMapper.selectShopPresaleActivityByShopIdByActiveityId(shop.getShopId(),activityId,tableName);
	}


}
