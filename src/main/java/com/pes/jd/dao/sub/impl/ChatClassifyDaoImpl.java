package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.constants.SubTable;
import com.pes.jd.dao.sub.ChatClassifyDao;
import com.pes.jd.mapper.sub.ChatClassifyMapper;
import com.pes.jd.model.DO.CsServiceEvaluationDetail;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DO.CsChatlog;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.entity.ChatClassify;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of ChatClassifyDao
 */
@Repository("chatClassifyDao")
public class ChatClassifyDaoImpl implements ChatClassifyDao {

    @Autowired
    private ChatClassifyMapper chatClassifyMapper;

    @Override
    public List<ChatClassify> queryClassifyByShopAndDate(ShopCommonParam shop, Date date) {
        // Create a date range for the same day (date to date)
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(date, date, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyByShopAndDate(
                    shop.getShopId(), date, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<ChatClassify> queryClassifyByShopAndDateRange(ShopCommonParam shop, Date startDate, Date endDate) {
        // Create a date range from startDate to endDate
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            // For each table in the date range, query for records between beginDate and endDate
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyByShopAndDateRange(
                    shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<ChatClassify> queryClassifyByShopAndClassify(ShopCommonParam shop, Date date, String classify) {
        // Create a date range for the same day (date to date)
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(date, date, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyByShopAndClassify(
                    shop.getShopId(), date, classify, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<ChatClassify> queryClassifyBySidsAndDateRange(ShopCommonParam shop, Date startDate, Date endDate, List<String> sids) {
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), SubTable.PES_CHAT_CLASSIFY.getName());
        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyBySidsAndDateRange(shop.getShopId(), startDate, endDate, sids, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<CsServiceEvaluationDetail> queryLowRatedEvaluationsByDateRange(ShopCommonParam shop, Date startDate, Date endDate) {
        // Create a date range from startDate to endDate
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), SubTable.PES_CS_SERVICE_EVALUATION_DETAIL.getName());

        List<CsServiceEvaluationDetail> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<CsServiceEvaluationDetail> queryResult = chatClassifyMapper.queryLowRatedEvaluationsByDateRange(
                    shop.getShopId(), startDate, endDate, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<CsServiceEvaluationDetail> queryLowRatedEvaluationsByDateRangeAndScore(ShopCommonParam shop, Date startDate, Date endDate,Integer score) {

        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), SubTable.PES_CS_SERVICE_EVALUATION_DETAIL.getName());

        List<CsServiceEvaluationDetail> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<CsServiceEvaluationDetail> queryResult = chatClassifyMapper.queryLowRatedEvaluationsByDateRangeAndScore(
                    shop.getShopId(), startDate, endDate, drp.getTableName(), score);

            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }


    @Override
    public List<ChatClassify> queryClassifyByShopAndDateRangeAndClassify(ShopCommonParam shop, Date startDate, Date endDate, String classify) {
        // Create a date range from startDate to endDate
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            // For each table in the date range, query using the original startDate and endDate
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyByShopAndDateRangeAndClassify(
                    shop.getShopId(), startDate, endDate, classify, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<CsServiceEvaluationDetail> queryAllEvaluationsBySidsAndDateRange(ShopCommonParam shop, Date startDate, Date endDate, List<String> sids) {
        // If the SIDs list is empty, return an empty result
        if (CollectionUtils.isEmpty(sids)) {
            return Lists.newArrayList();
        }

        // Create a date range from startDate to endDate
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                SubTable.PES_CS_SERVICE_EVALUATION_DETAIL.getName());

        List<CsServiceEvaluationDetail> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            // For each table in the date range, query using the original startDate and endDate
            List<CsServiceEvaluationDetail> queryResult = chatClassifyMapper.queryAllEvaluationsBySidsAndDateRange(
                    shop.getShopId(), startDate, endDate, sids, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<ChatClassify> queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange(ShopCommonParam shop, Date startDate, Date endDate, String classify, String classifyExtra) {
        // Create a date range from startDate to endDate
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            // For each table in the date range, query using the original startDate and endDate
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange(
                    shop.getShopId(), startDate, endDate, classify, classifyExtra, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<ChatClassify> queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndSids(ShopCommonParam shop, Date startDate, Date endDate, String classify, String classifyExtra,List<String> sids) {
        // Create a date range from startDate to endDate
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<ChatClassify> queryResult = chatClassifyMapper.queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndSids(
                    shop.getShopId(), classify, classifyExtra, drp.getTableName(),sids);
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<CsChatSessionDO> queryChatSessionByShopIdAndSidAndDateRange(ShopCommonParam shop, String sid, Date startDate, Date endDate) {
        // Create a date range from startDate to endDate
        // Using PES_CS_SERVICE_EVALUATION as a substitute for chat session table since it's not defined in SubTable
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                "pes_cs_chat_session");

        List<CsChatSessionDO> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            // For each table in the date range, query using the original startDate and endDate
            List<CsChatSessionDO> queryResult = chatClassifyMapper.queryChatSessionByShopIdAndSidAndDateRange(
                    shop.getShopId(), sid, startDate, endDate, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }

    @Override
    public List<CsChatlog> queryChatlogsByShopIdAndTimeRange(ShopCommonParam shop, Date startTime, Date endTime) {
        // Create a date range from startTime to endTime
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startTime, endTime, shop.getSchemaId(),
                SubTable.PES_CS_CHATLOG.getName());

        List<CsChatlog> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            // For each table in the date range, query using the original startTime and endTime
            List<CsChatlog> queryResult = chatClassifyMapper.queryChatlogsByShopIdAndTimeRange(
                    shop.getShopId(), startTime, endTime, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }

        return result;
    }



    @Override
     public Integer  querySidByDateRange(ShopCommonParam shop, Date startDate, Date endDate){
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(),
                SubTable.PES_CHAT_CLASSIFY.getName());

        List<ChatClassify> result = Lists.newArrayList();
        for (DateRangeParam drp : tableNames) {
            List<ChatClassify> queryResult = chatClassifyMapper.querySessionIdByDateRange(shop.getShopId(), drp.getTableName(),startDate,endDate);
            if (CollectionUtils.isNotEmpty(queryResult)) {
                result.addAll(queryResult);
            }
        }
        int size = result.stream().map(ChatClassify::getSid).distinct().collect(Collectors.toList()).size();
        return size;
    }

}
