package com.pes.jd.dao.sub;


import com.pes.jd.model.DO.JobPullApiRecordDO;
import com.pes.jd.model.VO.JobPullApiRecordVO;

import java.util.Date;
import java.util.List;

public interface JobPullApiRecordDao {
    int deleteByPrimaryKey(Long id);

    int insert(JobPullApiRecordDO record);

    int insertSelective(JobPullApiRecordDO record);

    JobPullApiRecordDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JobPullApiRecordDO record);

    int updateByPrimaryKey(JobPullApiRecordDO record);

    List<JobPullApiRecordVO> searchJobPullApiRecordRecord(Long shopId, String schemaId, Date dates, Integer type);

    List<JobPullApiRecordVO> searchJobPullApiRecordRecordBySchemaId(String schemaId, Date dates, Integer type);
}