package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.CsTorderPerformanceDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/31 3:09 PM
 * @since 1.0.0
 */
public interface CsTorderPerformanceDao {

    List<CsTorderPerformanceDTO> searchByDateShopNicks(
            Set<String> nicks,
            Long shopId,
            Date startDate,
            Date endDate,
            String schema,
            String queryType,
            Set<Date> filterDates);

    Double selectCsSaleAmount(Long shopId, Date startDate, Date endDate, String schema);

}
