package com.pes.jd.dao.sub;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.pes.jd.model.DTO.GoodsConsultSummaryDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.DTO.ShopGoodNameDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuDTO;
import com.pes.jd.model.Param.LossOrderParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SkuQuery;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;

import java.util.List;
import java.util.Set;


public interface ShopGoodSkuDao {

    List<GoodsConsultSummaryDTO> selectGoodsConsultSummary(Long ShopId);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstBySkuIdLst(ShopCommonParam shop, List<Long> skuIdLst);

    List<ShopGoodsSkuDTO> selectBySkuIdsForRefundDataAnalysis(ShopQuery shop, Set<Long> skuIds);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus(ShopCommonParam shop,
                                                                             List<Long> categoryLst, String skuName, String status, List<Long> skuIdLst, Integer pageSize,
                                                                             Integer pageNum);


    List<ShopGoodsSkuDTO> selectShopGoodsSkuLstByShopIdBySkuIdByWareId(ShopCommonParam shop, Set<Long> wareIdSet, List<Long> skuIdLst, String status);

    IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods(
            IPage<ShopGoodsSkuDTO> goodsSkuLstPage,
            ShopCommonParam shop,
            List<Long> categoryLst,
            String skuName,
            String status,
            List<Long> excludeSkuIds,
            List<Long> includeSkuIds,
            List<Long> topSku, String propertity, String sortDirection);

    IPage<ShopGoodsSkuDTO> selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(
            IPage<ShopGoodsSkuDTO> goodsSkuLstPage, ShopCommonParam shop,
            List<Long> threeLevelParentLst, String skuName, String status,
            List<Long> excludeSkuIds, List<Long> includeSkuIds,
            List<Long> topSku, String propertity,
            String sortDirection, Integer addStatus, Byte dimension);

    int selectCountShopGoods(ShopCommonParam shop, List<Long> categoryLst, String skuName, String status,
                             List<Long> skuIdLst);

    int selectCountShopGoodsForGoods(ShopCommonParam shop, List<Long> threeLevelParentLst, String skuName, String status, List<Long> excludeSkuIds, List<Long> includeSku, List<Long> topSku);


	List<ShopGoodsSkuDTO> selectShopGoodsSkuByShopIdBySkuId(ShopCommonParam shop, Set<Long> skuIds);

	ShopGoodsSkuDTO queryShopGoodsInfoBySkuIdAndShopId(ShopCommonParam shop, Long skuId);

    List<ShopGoodsSku> selectShopSkuByShopId(ShopCommonParam param);

    Set<Long> selectShopSkuIdSetByShopId(ShopCommonParam param);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuBySkuLst(ShopCommonParam param, List<Long> skuLst);

	List<ShopGoodsSkuDTO> selectShopGoodsSkuByShopIdForLossOrder(LossOrderParam shop);


    List<ShopGoodNameDTO> selectShopGoodsSkuNameBySkuIdLst(ShopCommonParam shop, List<Long> skuIds);

    IPage<ShopGoodsSkuDTO> selectShopGoodsLstByCategoryIdBySkuNameByStatusByAddStatusForGoods(IPage<ShopGoodsSkuDTO> goodsSkuLstPage, ShopCommonParam shop, List<Long> threeLevelParentLst, String skuName, String status, List<Long> excludeSkuIds, List<Long> includeSku, List<Long> topSku, String propertity, String sortDirection, Integer addStatus, Byte dimension);


    List<Long> selectSkuIdsByWareId(SkuQuery skuQuery, Long wareId);

    List<ShopGoodsSku> selectShopGoodsSkuByShopIdByWareIds(ShopCommonParam shop, List<Long> wareIds);

    List<ShopGoodsSku> selectShopGoodsSkuIdsByWareIdLst(ShopCommonParam shop, List<Long> wareIds);

    List<ShopGoodsSku> selectShopSkuByShopIdAndSkuId(ShopCommonParam shop, List<Long> skuList);

    List<ShopGoodsSkuDTO> selectShopSkuByShopIdAndskuName(ShopCommonParam shop, String skuName);

    List<ShopGoodsSku> selectShopSkuBySpu(ShopDTO shopDTO, List<Long> goodsIdList);

    List<ShopGoodsSkuDTO> selectShopGoodsSkuIdLstByCategoryIdByStatus(ShopCommonParam shop, List<Long> threeLevelParentLst, String status);
}
