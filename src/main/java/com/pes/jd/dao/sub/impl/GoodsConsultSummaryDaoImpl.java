package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.GoodsConsultSummaryDao;
import com.pes.jd.mapper.sub.GoodsConsultSummaryMapper;
import com.pes.jd.model.DTO.GoodsConsultSummaryDTO;
import com.pes.jd.model.DTO.GoodsConsultSummaryV2DTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

@Repository
public class GoodsConsultSummaryDaoImpl implements GoodsConsultSummaryDao {

	@Autowired
	private GoodsConsultSummaryMapper goodsConsultSummaryMapper;
	@Override
	public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(ShopCommonParam shop,
    		List<Long> skuLst,Date startDate,Date endDate,List<String> csNickList) throws SQLException {

		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());
		String goodsSkuTable = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());

		return goodsConsultSummaryMapper.selectGoodsConsultSummaryCountByDateBySkuIdByCsNick(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTable, skuLst, startDate, endDate, csNickList);
	}

	@Override
	public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(ShopCommonParam shop,
																							List<Long> skuLst,List<Long> categoryLst,Date startDate,Date endDate,List<String> csNickList) throws SQLException {

		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());
		String goodsSkuTable = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());

		return goodsConsultSummaryMapper.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTable, skuLst, categoryLst,startDate, endDate, csNickList);
	}

	@Override
	public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(ShopCommonParam shop,
																								List<Long> skuLst, Date startDate, Date endDate, List<String> csNickList) throws SQLException {

		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY_V2.getName());
		String goodsSkuTable = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());

		return goodsConsultSummaryMapper.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTable, skuLst, startDate, endDate, csNickList);
	}

	@Override
	public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(ShopCommonParam shop,
																								List<Long> skuLst, List<Long> categoryIds, Date startDate, Date endDate, List<String> csNickList) throws SQLException {

		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY_V2.getName());
		String goodsSkuTable = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsConsultSummaryMapper.selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTable, skuLst, categoryIds, startDate, endDate, csNickList);

	}

	@Override
	public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuId(ShopCommonParam shop, Long skuId,
			Date startDate, Date endDate,List<String> csNickLst) throws SQLException {
		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());
		String goodsSkuTables = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsConsultSummaryMapper.selectGoodsConsultSummaryByShopIdByDateByskuId(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTables, skuId, startDate, endDate,csNickLst);
	}

	@Override
	public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV3(ShopCommonParam shop, Long skuId, List<Long> categoryIds,
																						 Date startDate, Date endDate, List<String> csNickLst) throws SQLException {
		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());
		String goodsSkuTables = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsConsultSummaryMapper.selectGoodsConsultSummaryByShopIdByDateByskuIdV3(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTables, skuId, categoryIds, startDate, endDate, csNickLst);
	}

	@Override
	public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdV2(ShopCommonParam shop, Long skuId,
																					   Date startDate, Date endDate,List<String> csNickLst) throws SQLException {
		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY_V2.getName());
		String goodsSkuTables = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsConsultSummaryMapper.selectGoodsConsultSummaryByShopIdByDateByskuIdV2(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTables, skuId, startDate, endDate,csNickLst);
	}

    @Override
    public List<GoodsConsultSummaryDTO> selectGoodsConsultSummaryByShopIdByDateByskuIds(ShopCommonParam shop, List<Long> skuIds, Date startDate, Date endDate, List<String> csNickLst) {
        List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());
        String goodsSkuTables = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
        return goodsConsultSummaryMapper.selectGoodsConsultSummaryByShopIdByDateByskuIds(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTables, skuIds, startDate, endDate,csNickLst);
    }

	@Override
	public List<GoodsConsultSummaryV2DTO> selectGoodsConsultSummaryByShopIdByDateByskuIdsV2(ShopCommonParam shop, List<Long> skuIds, Date startDate, Date endDate, List<String> csNickLst) {
		List<DateRangeParam> goodsConsultSummaryTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY_V2.getName());
		String goodsSkuTables = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsConsultSummaryMapper.selectGoodsConsultSummaryByShopIdByDateByskuIdsV2(shop.getShopId(), goodsConsultSummaryTables, goodsSkuTables, skuIds, startDate, endDate,csNickLst);
	}

}
