package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.SlientGoodsSaleIndexDetailDao;
import com.pes.jd.mapper.sub.SlientGoodsSaleIndexDetailMapper;
import com.pes.jd.model.DTO.SlientGoodsSaleIndexDetailDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

@Repository
public class SlientGoodsSaleIndexDetailDaoImpl implements SlientGoodsSaleIndexDetailDao {

	@Autowired
	private SlientGoodsSaleIndexDetailMapper goodsSaleIndexDetailMapper;

	@Override
	public List<SlientGoodsSaleIndexDetailDTO> selectGoodsSaleIndexDetailBySkuByDate(ShopCommonParam shop, Date startDate,
                                                                                     Date endDate, List<Long> skuLst, SortPageQuery sortPageQuery, String orderId) throws SQLException {
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_SLIENT_GOODS_SALE_INDEX_DETAIL.getName());
		String tableName2 = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsSaleIndexDetailMapper.selectGoodsSaleIndexDetailBySkuByDate(shop.getShopId(), startDate, endDate, skuLst, tableNames, tableName2, sortPageQuery, orderId);
	}

	@Override
	public Integer selectGoodsSaleIndexDetailCount(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst, String orderId) {
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_SLIENT_GOODS_SALE_INDEX_DETAIL.getName());
		String tableName2 = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());

        return goodsSaleIndexDetailMapper.selectGoodsSaleIndexDetailCount(shop.getShopId(), startDate, endDate, skuLst, tableNames, tableName2, orderId);
	}

	@Override
	public List<SlientGoodsSaleIndexDetailDTO> selectGoodsSaleIndexDetailByShopIdBySkuByDate(ShopCommonParam shop, Date startDate, Date endDate,
																							 List<Long> skuLst, SortPageQuery sortPageQuery, String orderId, List<Long> bargainOrderIds) {
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_SLIENT_GOODS_SALE_INDEX_DETAIL.getName());
		return goodsSaleIndexDetailMapper.selectGoodsSaleIndexDetailByShopIdBySkuByDate(shop.getShopId(), startDate, endDate, skuLst, tableNames, sortPageQuery, orderId, bargainOrderIds);
	}


}
