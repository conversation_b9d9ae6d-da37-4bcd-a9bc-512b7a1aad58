package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.dao.sub.CsServiceEvaluationDetailDao;
import com.pes.jd.mapper.sub.CsServiceEvaluationDetailMapper;
import com.pes.jd.model.DO.CsServiceEvaluationDetail;
import com.pes.jd.model.DTO.CsServiceEvaluateDetailDTO;
import com.pes.jd.model.DTO.CsServiceStatisCountDTO;
import com.pes.jd.model.DTO.NickPerformanceDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**  
 * ClassName:CsServiceEvaluationDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 上午9:24:18 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository("csServiceEvaluationDetailDao")
public class CsServiceEvaluationDetailDaoImpl implements CsServiceEvaluationDetailDao{

	@Autowired
	private CsServiceEvaluationDetailMapper csServiceEvaluationDetailMapper;
	
	@Override
	public CsServiceEvaluationDetail getCsServiceEvaluationById(Long id) {
		return csServiceEvaluationDetailMapper.getCsServiceEvaluationById(id);
	}

	@Override
	public int deleteCsServiceEvaluationById(Long id) {
		return csServiceEvaluationDetailMapper.deleteCsServiceEvaluationById(id);
	}

	@Override
	public List<CsServiceEvaluationDetail> selectCsServiceEvaluationByNick(Map<String, Object> param) {
		return csServiceEvaluationDetailMapper.selectCsServiceEvaluationByNick(param);
	}

	@Override
	public List<Map<String, Object>> selectShopQNDailyEvalLst(String tableName, Long shopId, Date startDate, Date endDate) {
		Map<String,Object> param = Maps.newHashMapWithExpectedSize(8);
		param.put("tableName",tableName);
		param.put("shopId",shopId);
		param.put("startDate",startDate);
		param.put("endDate",endDate);
		return csServiceEvaluationDetailMapper.selectShopQNDailyEvalLstReturnMap(param);
	}

	@Override

	public List<Map<String, Object>> selectByDateAndShopNick(Map<String, Object> param) {
		return csServiceEvaluationDetailMapper.selectByDateAndShopNick(param);
	}

	@Override
	public List<CsServiceStatisCountDTO> selectCsNickServiceEvalByEvaltimeCount(Long shopId, Date startDate, Date endDate, String schemaId) {
		String tableName=CommonUtils.getTableName(schemaId, TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName(), String.valueOf(shopId));
		return csServiceEvaluationDetailMapper.selectCsNickServiceEvalByEvaltimeCount(shopId, startDate, endDate, tableName);
	}

	@Override
	public List<CsServiceStatisCountDTO> selectCsNickServiceEvalSatisfactionCount(Long shopId,Date startDate, Date endDate, String schemaId) {
		String tableName=CommonUtils.getTableName(schemaId, TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName(), String.valueOf(shopId)); 
		return csServiceEvaluationDetailMapper.selectCsNickServiceEvalSatisfactionCount(shopId,startDate, endDate, tableName);
	}

	@Override
	public List<NickPerformanceDTO> selectPerformanceData(Map<String, Object> param) {
		return csServiceEvaluationDetailMapper.selectPerformanceData(param);
	}

	@Override
	public List<CsServiceStatisCountDTO> selectCsNickServiceEvalBySendTimeCount(ShopQuery shop,List<String> csNickLst, Date startDate, Date endDate) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName()); 
		
		return csServiceEvaluationDetailMapper.selectCsNickServiceEvalBySendTimeCount(shop.getShopId(),csNickLst, startDate, endDate, tableName);
	}

	@Override
	public List<CsServiceStatisCountDTO> selectCsNickServiceEvalByEvalTimeCount(ShopQuery shop, List<String> csNickLst, Date startDate, Date endDate) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName()); 
		return csServiceEvaluationDetailMapper.selectCsNickServiceEvalByEvalTimeCount(shop.getShopId(), csNickLst, startDate, endDate, tableName);
	}

	@Override
	public List<CsServiceStatisCountDTO> selectCsNickServiceEvalSatisfactionCount(ShopQuery shop, List<String> csNickLst, Date startDate, Date endDate) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName()); 
		return csServiceEvaluationDetailMapper.selectCsNickServiceEvalSatisfactionCount(shop.getShopId(), csNickLst, startDate, endDate, tableName);
	}

	@Override
	public List<CsServiceEvaluateDetailDTO> selectCsServiceEvalByCsNickByBuyerNickByEvalCode(ShopCommonParam shop,
			List<String> csNickList, Date startDate, Date endDate, String buyerNick, Integer evalCode,SortPageQuery sortPageQuery) {
		List<DateRangeParam> sendEvalTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_CS_SERVICE_SEND_EVAL.getName());
		List<DateRangeParam> evalTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName());
		List<DateRangeParam> chatSessionTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName());
		return csServiceEvaluationDetailMapper.selectCsServiceEvalByCsNickByBuyerNickByEvalCode(shop.getShopId(), csNickList, startDate, endDate, buyerNick, evalCode, evalTables,sendEvalTables,chatSessionTables,sortPageQuery);
	}

	@Override
	public Integer selectCsServiceEvalCount(ShopCommonParam shop, List<String> csNickList, Date startDate, Date endDate,
			String buyerNick, Integer evalCode) {
		List<DateRangeParam> sendEvalTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_CS_SERVICE_SEND_EVAL.getName());
		List<DateRangeParam> evalTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName());
		List<DateRangeParam> chatSessionTables = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName());

		return csServiceEvaluationDetailMapper.selectCsServiceEvalCount(shop.getShopId(), csNickList, startDate, endDate, buyerNick, evalCode, evalTables, sendEvalTables,chatSessionTables);
	}

	@Override
	public List<CsServiceEvaluateDetailDTO> selectCsServiceEvalReceiveChatSession(ShopQuery shop, Date startDate,
			Date endDate) {
		List<CsServiceEvaluateDetailDTO> csServiceEvalLst=Lists.newArrayList();
		 List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName());
	        for (CommonUtils.DateRangeParam data : tableNames) {
	            List<CsServiceEvaluateDetailDTO> dtos = csServiceEvaluationDetailMapper.selectCsServiceEvalReceiveChatSession(shop.getShopId(), data.getBeginDate(), data.getEndDate(), TableEnum.PES_CS_CHATPEER.getName());
	            if (CollectionUtils.isNotEmpty(dtos)) {
	            	csServiceEvalLst.addAll(dtos);
	            }
	        }
		return csServiceEvalLst;
	}	


}
  
