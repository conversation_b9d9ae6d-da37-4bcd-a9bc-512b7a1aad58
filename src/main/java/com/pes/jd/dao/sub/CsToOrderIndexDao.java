package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.CsToOrderIndexDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/2/2 10:24 AM
 * @since 1.0.0
 */
public interface CsToOrderIndexDao {
    List<CsToOrderIndexDTO> searchByDateShopNicks(
            Long shopId,
            Date startDate,
            Date endDate,
            Set<String> nicks,
            String queryType,
            String schema,
            Set<Date> filterDates);
}
