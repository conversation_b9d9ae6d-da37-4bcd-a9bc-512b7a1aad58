
package com.pes.jd.dao.sub;

import com.pes.jd.model.DO.GoodsFilter;
import com.pes.jd.model.DO.PesShopOvDay;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface PesShopOvDayDao {
	int insertPesShopOvDay(PesShopOvDay record);

	int deletePesShopOvDayById(Long id);

	int updatePesShopOvDayBySelective(GoodsFilter record);

	PesShopOvDay getPesShopOvDayById(Long id);

	Double selectShopSaleAmount(@Param("shopId") Long shopId, @Param("startDate") Date startDate, @Param("endDate") Date endDate, String schema);
}
