package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.ShopTeamOvDayDao;
import com.pes.jd.mapper.sub.ShopTeamOvDayMapper;
import com.pes.jd.model.DTO.ShopTeamOvDayDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ShopTeamOvDayDaoImpl implements ShopTeamOvDayDao {

    @Autowired
    private ShopTeamOvDayMapper shopTeamOvDayMapper;

    @Override
    public List<ShopTeamOvDayDTO> selectByShopIdAndDateForShopPerformance(ShopQuery shop, Integer dateType, List<String> dates) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_OV_DAY.getName());
        return shopTeamOvDayMapper.selectByShopIdAndDateForShopPerformance(shop.getShopId(), dateType, dates, tableName);
    }
}
