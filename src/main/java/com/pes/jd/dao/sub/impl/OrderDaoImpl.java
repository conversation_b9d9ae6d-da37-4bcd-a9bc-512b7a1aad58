
package com.pes.jd.dao.sub.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.pes.jd.constants.PesConstants;
import com.pes.jd.dao.sub.OrderDao;
import com.pes.jd.mapper.sub.OrderMapper;
import com.pes.jd.model.DO.Order;
import com.pes.jd.model.DTO.AddressDTO;
import com.pes.jd.model.DTO.GoodsDTO;
import com.pes.jd.model.DTO.OrderDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.*;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.model.Query.UserShopQuery;
import com.pes.jd.model.VO.DealAnalysisVo;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.MultiTablePagingUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName:TradeDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月24日 下午7:27:51 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */

@Repository("orderDao")
public class OrderDaoImpl implements OrderDao {

    @Autowired
    private OrderMapper orderMapper;


    @Override
    public int insertTrade(Order record) {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public int deleteTradeById(Long tradeId) {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public int updateTradeById(Order record) {
        // TODO Auto-generated method stub
        return 0;
    }

    @Override
    public Order getTradeById(Long tradeId) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public int persistOrderByFile(String filePath, String tableName) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("filePath", filePath);
        param.put("tableName", tableName);
        return orderMapper.persistOrderByFile(param);
    }

    @Override
    public int deleteOrderByTids(List<Long> tids, String tableName) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("tids", tids);
        param.put("tableName", tableName);
        return orderMapper.deleteOrdersByTids(param);
    }

    @Override
    public List<Long> selectIdsByTradeIds(List<Long> tids, String tableName) {
        Map<String, Object> param = new HashMap<String, Object>();
        param.put("tids", tids);
        param.put("tableName", tableName);
        return orderMapper.selectIdsByTradeIds(param);
    }

    @Override
    public int persistOrders(List<OrderDTO> orders, String tableName) {
        if (orders == null || orders.size() == 0) {
            return 0;
        }
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("orders", orders);
        paramMap.put("tableName", tableName);
        return orderMapper.persistOrders(paramMap);
    }


    @Override
    public void updateAddress( ShopCommonParam shop, Long orderId,String buyer_Nick) {
    	String tableName =  CommonUtils.getTableName(shop.getSchemaId(), "pes_cs_update_addr");
        orderMapper.updateAddress(shop.getShopId(),orderId,buyer_Nick,tableName);
    }

    @Override
    public List<DealAnalysisVo> getSilenceSaleTradeByDateAndBuyNickAndShopId(UserShopQuery shop, Date startDate, Date endDate,
                                                                             String buyerNick, int start, int length) {
        Map<String, Object> map = new HashMap<String, Object>();
        String orderTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_ORDER.getName(), shop.getShop().getShopId() + "");
        String csOrderBindTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName(), shop.getShop().getShopId() + "");
        map.put("shopId", shop.getShop().getShopId());
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("buyerNick", buyerNick);
        map.put("orderTableName", orderTableName);
        map.put("csOrderBindTableName", csOrderBindTableName);
        map.put("start", start);
        map.put("length", length);
        return orderMapper.getSilenceSaleTradeByDateAndBuyNickAndShopId(map);
    }

    @Override
    public List<OrderDTO> selectOrderInfoByOrderIds(ShopCommonParam shopCommonParam, Collection<Long> orderIds, Date startDate, Date endDate) {
        List<OrderDTO> orderLst = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (DateRangeParam dateRangeParam : tableNames) {
            List<OrderDTO> dtos = orderMapper.selectOrderInfoByOrderIds(shopCommonParam.getShopId(), orderIds, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(dtos)) {
                orderLst.addAll(dtos);
            }
        }
        return orderLst;
    }


    @Override
    public List<OrderDTO> selectFieldsByOrderIds(ShopCommonParam shopCommonParam, Collection<Long> orderIds, String fields, Date startDate, Date endDate) {
        List<OrderDTO> orderLst = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (DateRangeParam dateRangeParam : tableNames) {
            List<OrderDTO> dtos = orderMapper.selectFieldsByOrderIds(shopCommonParam.getShopId(), orderIds, fields, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(dtos)) {
                orderLst.addAll(dtos);
            }
        }
        return orderLst;
    }

    @Override
    public List<OrderDTO> selectOrderDeatilByShopIdAndBuyerNick(UserShopQuery shop, String buyerNick) {
        List<OrderDTO> orderDLst = Lists.newArrayList();

        return orderDLst;

    }

    @Override
    public List<DealAnalysisVo> selectShopDealTradesByPage(UserShopQuery shop, Date startDate, Date endDate,
                                                           String buyerNick, int start, int length) {
        Map<String, Object> map = new HashMap<String, Object>();
        String orderTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_ORDER.getName(), shop.getShop().getShopId() + "");
        String csOrderBindTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName(), shop.getShop().getShopId() + "");
        Set<String> nicks = shop.getCsNickInfo().stream().map(UserQuery::getNick).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nicks)) {
            return new ArrayList<DealAnalysisVo>();
        }
        map.put("nicks", nicks);
        map.put("shopId", shop.getShop().getShopId());
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("buyerNick", buyerNick);
        map.put("orderTableName", orderTableName);
        map.put("csOrderBindTableName", csOrderBindTableName);
        map.put("start", start);
        map.put("length", length);
        return orderMapper.selectShopDealTradesByPage(map);
    }

    @Override
    public int getSilenceSaleTradeCount(UserShopQuery shop, Date startDate, Date endDate, String buyerNick) {
        Map<String, Object> map = new HashMap<String, Object>();
        String orderTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_ORDER.getName(), shop.getShop().getShopId() + "");
        String csOrderBindTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName(), shop.getShop().getShopId() + "");
        Set<String> nicks = shop.getCsNickInfo().stream().map(UserQuery::getNick).collect(Collectors.toSet());
        map.put("nicks", nicks);
        map.put("shopId", shop.getShop().getShopId());
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("buyerNick", buyerNick);
        map.put("orderTableName", orderTableName);
        map.put("csOrderBindTableName", csOrderBindTableName);
        return orderMapper.getSilenceSaleTradeCount(map);
    }

    @Override
    public int getShopDealTradesCount(UserShopQuery shop, Date startDate, Date endDate, String buyerNick) {
        Map<String, Object> map = new HashMap<String, Object>();
        String orderTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_ORDER.getName(), shop.getShop().getShopId() + "");
        String csOrderBindTableName = CommonUtils.getTableName(shop.getShop().getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName(), shop.getShop().getShopId() + "");
        Set<String> nicks = shop.getCsNickInfo().stream().map(UserQuery::getNick).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(nicks)) {
            return 0;
        }
        map.put("nicks", nicks);
        map.put("shopId", shop.getShop().getShopId());
        map.put("startDate", startDate);
        map.put("endDate", endDate);
        map.put("buyerNick", buyerNick);
        map.put("orderTableName", orderTableName);
        map.put("csOrderBindTableName", csOrderBindTableName);
        return orderMapper.getShopDealTradesCount(map);
    }


    @Override
    public List<OrderDTO> selectOrderStatusByOrderIdList(LossOrderParam lossOrderParam, Date startDate, Date endDate, List<Long> lossOrderIdList) {
        List<OrderDTO> retOrderLst = Lists.newArrayList();
        if (CollectionUtils.isEmpty(lossOrderIdList)) {
            return retOrderLst;
        }
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, lossOrderParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (DateRangeParam drp : tableNames) {
            List<OrderDTO> queryOrderLst = orderMapper.selectOrderStatusByOrderIdList(lossOrderParam.getShopId(), lossOrderIdList, drp.getTableName());
            if (CollectionUtils.isNotEmpty(queryOrderLst)) {
                retOrderLst.addAll(queryOrderLst);
            }
        }
        return retOrderLst;
    }

    @Override
    public List<OrderDTO> selectByOrderIdsAndOutStatusForRefundDataAnalysis(ShopQuery shop, RefundAnalysisParam param) {

        Date beginDate = param.getStartDate();
        Date endDate = param.getEndDate();

        Date queryStart = param.getStartDate();
        Date queryEnd = param.getEndDate();

        switch (param.getDateType()) {
            //按退款成功时间
            case 1:
                beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -PesConstants.ORDER_REFUND_MODIFIED_DATE_NEW);
                queryStart = beginDate;
                break;
            //按退款申请时间
            case 2:
                beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -PesConstants.ORDER_REFUND_MODIFIED_DATE_NINE);
                queryStart = beginDate;
                break;
            //按下单时间
            case 3:
                break;
            //按付款时间
            case 4:
                beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -1);
                break;
        }
        List<List<Long>> orderIdList = Lists.partition(new ArrayList<>(param.getOrderIds()), 600);

        List<OrderDTO> orderLst = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(beginDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            for (List<Long> orderIds : orderIdList) {
                List<OrderDTO> dtos = orderMapper.selectByOrderIdsAndOutStatusForRefundDataAnalysis(shop.getShopId(), orderIds, param.getDateType(), queryStart, queryEnd, param.getOutStatus(), dateRangeParam.getTableName());
                if (CollectionUtils.isNotEmpty(dtos)) {
                    orderLst.addAll(dtos);
                }
            }
        }
        return orderLst;
    }

    @Override
    public List<OrderDTO> selectByOrderIdsAndOutStatusForRefundDataAnalysis(ShopQuery shop, RefundAnalysisParam param, boolean csRefund) {

        Date beginDate = param.getStartDate();
        Date endDate = param.getEndDate();

        Date queryStart = param.getStartDate();
        Date queryEnd = param.getEndDate();

        switch (param.getDateType()) {
            //按退款成功时间
            case 1:
                if(!csRefund){//静默
                    beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -PesConstants.ORDER_REFUND_MODIFIED_DATE_NEW_V3);
                    queryStart = beginDate;
                }else{
                    beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -PesConstants.ORDER_REFUND_MODIFIED_DATE_NEW);
                    queryStart = beginDate;
                }
                break;
            //按退款申请时间
            case 2:
                beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -PesConstants.ORDER_REFUND_MODIFIED_DATE_NINE);
                queryStart = beginDate;
                break;
            //按下单时间
            case 3:
                break;
            //按付款时间
            case 4:
                beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -1);
                break;
        }
        List<List<Long>> orderIdList = Lists.partition(new ArrayList<>(param.getOrderIds()), 600);

        List<OrderDTO> orderLst = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(beginDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            for (List<Long> orderIds : orderIdList) {
                List<OrderDTO> dtos = orderMapper.selectByOrderIdsAndOutStatusForRefundDataAnalysis(shop.getShopId(), orderIds, param.getDateType(), queryStart, queryEnd, param.getOutStatus(), dateRangeParam.getTableName());
                if (CollectionUtils.isNotEmpty(dtos)) {
                    orderLst.addAll(dtos);
                }
            }
        }
        return orderLst;
    }

    @Override
    public Map<String, Integer> selectCountByShopIdAndDateAndOrderIdAndBuyerNick(ShopCommonParam shopCommonParam, Date startDate, Date endDate, Long orderId, String buyerNick, Integer orderType, List<Long> parentOrderId) {

        Map<String, Integer> tableTotalRecordNumMap = new HashMap<>();
        int count = 0;
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            int tableCount = orderMapper.selectCountByShopIdAndDateAndOrderIdAndBuyerNick(shopCommonParam.getShopId(), orderType, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, orderId, dateRangeParam.getTableName(), parentOrderId);
            //以当前 表名 作为key， 当前表count 作为value
            tableTotalRecordNumMap.put(dateRangeParam.getTableName(), tableCount);
            //统计已查询的表的 总count
            count += tableCount;
        }
        //将总 count添加到 map
        tableTotalRecordNumMap.put("count", count);
        return tableTotalRecordNumMap;
    }

    @Override
    public List<OrderDTO> selectPageByShopIdAndDateAndOrderIdAndBuyerNick(ShopCommonParam shopCommonParam, Date startDate, Date endDate, Long orderId, String buyerNick, Integer orderType, SortPageQuery query, Map<String, Integer> tableTotalRecordNumMap, List<Long> parentOrderId) {
        //根据开始时间、结束时间 获取 月份表集合
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());

        List<OrderDTO> orderLst = new ArrayList<>();
        //size > 0 ? 分页 : 不分表页
        if (query.getSize() > 0) {
            MultiTablePagingUtil paging = new MultiTablePagingUtil(query.getCurrentPage().intValue(), query.getSize().intValue(), tableTotalRecordNumMap);
            //遍历 各月份表
            for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
                // 获取 对当前表处理状态
                // 0 数据满 return | 1 查询当前表数据，继续拿数据 | 2 跳过该表
                int flag = paging.nextTable(dateRangeParam.getTableName());
                if (flag == 1) {
                    //查询当前月分表
                    List<OrderDTO> orders = orderMapper.selectPageByShopIdAndDateAndOrderIdAndBuyerNick(shopCommonParam.getShopId(), orderType, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, orderId, dateRangeParam.getTableName(), paging.getTableStartIndex(), paging.getTableRecordLength(),parentOrderId);
                    //计算已查询count
                    paging.addAndGetTotalRecordNum(CollectionUtils.isEmpty(orders) ? 0 : orders.size());
                    //添加该月份结果集 到 总结果集
                    if (CollectionUtils.isNotEmpty(orders)) {
                        orderLst.addAll(orders);
                    }
                }
                //判断 是否接着查，不用则 return 结果集
                if (!paging.hasNext()) {
                    return orderLst;
                }
            }
        } else {
            //遍历 各月份表
            for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
                List<OrderDTO> orders = orderMapper.selectPageByShopIdAndDateAndOrderIdAndBuyerNick(shopCommonParam.getShopId(), orderType, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, orderId, dateRangeParam.getTableName(), null, null,parentOrderId);
                if (CollectionUtils.isNotEmpty(orders))
                    orderLst.addAll(orders);
            }
        }
        return orderLst;
    }

    @Override
    public List<Long> selectOrderIdsForSilenceSaleAnalysis(ShopCommonParam shopCommonParam, SilenceSaleParam silenceSaleParam, SortPageQuery sortPageQuery,List<Long> orderIds) {
        List<CommonUtils.DateRangeParam> orderTableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(silenceSaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY_COMPANYTURN), silenceSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<CommonUtils.DateRangeParam> bindTableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(silenceSaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY - silenceSaleParam.getEnquiryValidDurationTime()), silenceSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName());
        List<CommonUtils.DateRangeParam> indexTableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(silenceSaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY - silenceSaleParam.getEnquiryValidDurationTime()), silenceSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
        return orderMapper.selectOrderIdsForSilenceSaleAnalysis(shopCommonParam.getShopId(), silenceSaleParam.getStartDate(), silenceSaleParam.getEndDate(), silenceSaleParam.getOrderId(), silenceSaleParam.getBuyerNick(), silenceSaleParam.getOrderType(),orderIds ,sortPageQuery, orderTableNames, bindTableNames, indexTableNames);
    }


    @Override
    public List<AddressDTO> selectAddressInfoByOrderIdAndShopId(ShopCommonParam shop, String tableName, Collection orderId, Date startDate, Date endDate) {
        List<DateRangeParam> dateRangeParams = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), tableName);
        List<AddressDTO> addressDTOS = Lists.newArrayList();
        for (DateRangeParam dateRangeParam : dateRangeParams) {
            List<AddressDTO> addressDTOList = orderMapper.selectAddressInfoByOrderIdAndShopId(dateRangeParam.getTableName(), orderId, shop.getShopId());
            addressDTOS.addAll(addressDTOList);
        }
        return addressDTOS;
    }


    @Override
    public List<GoodsDTO> selectGoodsDetailInfoBySkuId(Collection skuId) {
        List<GoodsDTO> goodsDTOS = Lists.newArrayList();
        goodsDTOS = orderMapper.selectGoodsDetailInfoBySkuId(skuId);
        return goodsDTOS;

    }

    @Override
    public List<GoodsDTO> selectGoodsDetailInfoByOrderId(ShopCommonParam shop, String tableName, Collection orderId, Date startDate, Date endDate) {
        List<GoodsDTO> goodsDTOS = Lists.newArrayList();
        List<DateRangeParam> dateRangeParams = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), tableName);
        for (DateRangeParam dateRangeParam : dateRangeParams) {
            List<GoodsDTO> goodsDTOList = orderMapper.selectGoodSInfoByOrderId(orderId, dateRangeParam.getTableName());
            goodsDTOS.addAll(goodsDTOList);
        }
        return goodsDTOS;
    }


    @Override
    public int selectSilenceOrderCountForSilenceSaleAnalysis(ShopCommonParam shopCommonParam, SilenceSaleParam silenceSaleParam,List<Long> orderIds) {
        List<CommonUtils.DateRangeParam> orderTableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(silenceSaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY_COMPANYTURN), silenceSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<CommonUtils.DateRangeParam> bindTableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(silenceSaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY - silenceSaleParam.getEnquiryValidDurationTime()), silenceSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName());
        List<CommonUtils.DateRangeParam> indexTableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(silenceSaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY - silenceSaleParam.getEnquiryValidDurationTime()), silenceSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
        return orderMapper.selectSilenceOrderCountForSilenceSaleAnalysis(shopCommonParam.getShopId(), silenceSaleParam.getStartDate(), silenceSaleParam.getEndDate(), silenceSaleParam.getOrderId(), silenceSaleParam.getBuyerNick(), silenceSaleParam.getOrderType(),orderIds ,orderTableNames, bindTableNames, indexTableNames);
    }


    @Override
    public List<OrderDTO> selectOrderByShopIdByDateByOrderLst(ShopCommonParam shopCommonParam, Set<Long> orderIds, Date startDate, Date endDate) {
        List<OrderDTO> orderLst = Lists.newArrayList();
        if(CollectionUtils.isEmpty(orderIds)){
            return orderLst;
        }
        List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (DateRangeParam dateRangeParam : tableNames) {
            List<OrderDTO> dtos = orderMapper.selectOrderByShopIdByDateByOrderLst(shopCommonParam.getShopId(),dateRangeParam.getBeginDate(),dateRangeParam.getEndDate(), orderIds, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(dtos)) {
                orderLst.addAll(dtos);
            }
        }
        return orderLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderByShopIdAndDateAndBuyerNick(ShopCommonParam shopCommonParam, Date startDate, Date endDate, String buyerNick, Integer orderType) {
        List<OrderDTO> parentOrderLst = Lists.newArrayList();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            List<OrderDTO> parentOrder = orderMapper.selectParentOrderByShopIdAndDateAndBuyerNick(shopCommonParam.getShopId(), orderType, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, dateRangeParam.getTableName());
            if (CollUtil.isEmpty(parentOrder)) continue;
            parentOrderLst.addAll(parentOrder);
        }
        return parentOrderLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderForCsSaleAnalysis(ShopCommonParam shopCommonParam, CsSaleParam csSaleParam) {
        List<OrderDTO> parentOrderLst = Lists.newArrayList();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(csSaleParam.getStartDate(), -csSaleParam.getEnquiryValidDurationTime() + 1 - PesConstants.validPresaleOrderBalancePayDays), csSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            List<OrderDTO> parentOrder = orderMapper.selectParentOrderForCsSaleAnalysis(shopCommonParam.getShopId(), csSaleParam.getStartDate(), csSaleParam.getEndDate(), csSaleParam.getCsNickLst(), csSaleParam.getBuyerNick(), dateRangeParam.getTableName());
            if (CollUtil.isEmpty(parentOrder)) continue;
            parentOrderLst.addAll(parentOrder);
        }
        return parentOrderLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderForCsSaleAnalysis2(ShopCommonParam shopCommonParam, CsSaleParam csSaleParam) {
        List<OrderDTO> parentOrderLst = Lists.newArrayList();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(DateUtil.getDateByPeriod(csSaleParam.getStartDate(), -csSaleParam.getEnquiryValidDurationTime() + 1 - PesConstants.validPresaleOrderBalancePayDays), csSaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            List<OrderDTO> parentOrder = orderMapper.selectParentOrderForCsSaleAnalysis2(shopCommonParam.getShopId(), csSaleParam.getStartDate(), csSaleParam.getEndDate(), csSaleParam.getCsNickLst(), csSaleParam.getBuyerNick(), dateRangeParam.getTableName());
            if (CollUtil.isEmpty(parentOrder)) continue;
            parentOrderLst.addAll(parentOrder);
        }
        return parentOrderLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderForPresaleOrderByShopIdAndDateAndBuyerNick(ShopCommonParam shopCommonParam, Date startDate, Date startDate1, Date endDate, String buyerNick, Integer orderType) {
        List<OrderDTO> parentOrderLst = Lists.newArrayList();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            List<OrderDTO> parentOrder = orderMapper.selectParentOrderByShopIdAndDateAndBuyerNick1(shopCommonParam.getShopId(), orderType, startDate1, endDate, buyerNick, dateRangeParam.getTableName());
            if (CollUtil.isEmpty(parentOrder)) continue;
            parentOrderLst.addAll(parentOrder);
        }
        return parentOrderLst;
    }
}

