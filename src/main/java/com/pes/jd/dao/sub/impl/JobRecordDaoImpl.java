package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.JobRecordDao;
import com.pes.jd.mapper.sub.JobRecordMapper;
import com.pes.jd.model.VO.JobPullRecordVO;
import com.pes.jd.model.VO.JobRecordVO;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class JobRecordDaoImpl implements JobRecordDao {
	
	@Resource
	private JobRecordMapper jobRecordMapper;

	@Override
	public List<Long> searchJobPullRecordByDateAndTableName(String SchemaId,Date date) {
		String tableName = CommonUtils.getTableNameOfYear(SchemaId, date, "pes_job_pull_record");
		return jobRecordMapper.searchJobPullRecordByDateAndTableName(date,tableName);
	}

	@Override
	public List<Long> searchJobCalRecordByDateAndTableName(String SchemaId,Date date) {
		String tableName = CommonUtils.getTableNameOfYear(SchemaId, date, "pes_job_cal_record");
		return jobRecordMapper.searchJobCalRecordByDateAndTableName(date,tableName);
	}

	//获取店铺失败的相关时间和参数
	@Override
	public List<JobRecordVO> searchJobRecordByDateAndTableNameAndType(String schemaId, Date date, String type, Boolean handleFailShopJob) {
		List<JobRecordVO> jobRecordVOS = new ArrayList<>();
		String tableName;
		if(TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType().equals(type)){
			tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_job_pull_record");
			return jobRecordMapper.searchJobRecordByDateAndTableName(date, tableName,handleFailShopJob);
		}else if(TaskJobDispatchEnum.SHOP_DATA_CAL.getType().equals(type)){
			tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_job_cal_record");
			return jobRecordMapper.searchJobRecordByDateAndTableName(date, tableName, false);
		}

		return jobRecordVOS;
	}

	@Override
	public List<JobRecordVO> searchJobRecordShopByTypeAndShopIdLst(String schemaId, Date date, String type, List<Long> shopIdLst) {
		List<JobRecordVO> jobRecordVOS = new ArrayList<>();
		String tableName;
		if(TaskJobDispatchEnum.SHOP_DATA_PULL_AND_CAL.getType().equals(type)){
			tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_job_pull_record");
			return jobRecordMapper.searchJobRecordShopByTypeAndShopIdLst(date, tableName,shopIdLst);
		}else if(TaskJobDispatchEnum.SHOP_DATA_CAL.getType().equals(type)){
			tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_job_cal_record");
			return jobRecordMapper.searchJobRecordShopByTypeAndShopIdLst(date, tableName,shopIdLst);
		}

		return jobRecordVOS;
	}

	@Override
	public int getJobPullShopNum(String schemaId, Date startDate, Date endDate) {
		int totalNum = 0;
		List<CommonUtils.DateRangeParam> pes_job_pull_record = CommonUtils.getTableNamesOfYear(startDate, endDate, schemaId, "pes_job_pull_record");
		for (CommonUtils.DateRangeParam dateRangeParam : pes_job_pull_record) {
			totalNum += jobRecordMapper.getJobPullShopNum(startDate,endDate, dateRangeParam.getTableName());
		}
		return totalNum;
	}

	@Override
	public int getJobCalShopNum(String schemaId, Date startDate, Date endDate) {
		int totalNum = 0;
		List<CommonUtils.DateRangeParam> pes_job_pull_record = CommonUtils.getTableNamesOfYear(startDate, endDate, schemaId, "pes_job_cal_record");
		for (CommonUtils.DateRangeParam dateRangeParam : pes_job_pull_record) {
			totalNum += jobRecordMapper.getJobCalShopNum(startDate,endDate, dateRangeParam.getTableName());
		}
		return totalNum;
	}

	@Override
	public Integer cleanJobPullRecord(List<Long> shopIds, String schemaId, Date date) {
		String tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_job_pull_record");
		return jobRecordMapper.cleanJobPullRecord(shopIds, tableName, date);
	}

	@Override
	public List<JobPullRecordVO> searchJobPullRecord(List<Long> shopIds, String schemaId, Date date, Integer status) {
		String tableName = CommonUtils.getTableNameOfYear(schemaId, date, "pes_job_pull_record");
		return jobRecordMapper.searchJobPullRecord(shopIds, tableName, date, status);

	}
}
