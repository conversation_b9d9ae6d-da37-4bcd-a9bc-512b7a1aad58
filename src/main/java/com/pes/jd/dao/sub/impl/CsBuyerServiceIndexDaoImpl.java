package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsBuyerServiceIndexDao;
import com.pes.jd.mapper.sub.CsBuyerServiceIndexMapper;
import com.pes.jd.model.DO.CsBuyerServiceIndexDO;
import com.pes.jd.model.DTO.CsBuyerServiceIndexDTO;
import com.pes.jd.model.DTO.NickPerformanceDTO;
import com.pes.jd.model.DTO.ReceiveUnSendDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public class CsBuyerServiceIndexDaoImpl implements CsBuyerServiceIndexDao {

	@Autowired
	private CsBuyerServiceIndexMapper csBuyerServiceIndexMapper;

	@Override
	public int batchInsertCsBuyerServiceIndex(ShopDTO shop, List<CsBuyerServiceIndexDO> csServiceIndexLst) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_BUYER_SERVICE_INDEX.getName(),
				shop.getShopId() + "");
		return csBuyerServiceIndexMapper.batchInsertCsBuyerServiceIndex(csServiceIndexLst, tableName);
	}

	@Override
	public int deleteShopCsBuyerServiceIndexByDate(ShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_BUYER_SERVICE_INDEX.getName(),
				shop.getShopId() + "");
		return csBuyerServiceIndexMapper.deleteShopCsBuyerServiceIndexByDate(shop.getShopId(), date, tableName);
	}

	@Override
	public List<CsBuyerServiceIndexDTO> selectCsBuyerServiceByShopIdByDate(Long shopId, Date startDate, Date endDate,
			String schemaId) {
		String tableName = CommonUtils.getTableName(schemaId, TableEnum.PES_CS_BUYER_SERVICE_INDEX.getName(),
				shopId + "");
		return csBuyerServiceIndexMapper.selectCsBuyerServiceByShopIdByDate(shopId, startDate, endDate, tableName);
	}

	@Override
	public List<NickPerformanceDTO> selectPerformanceData(Map<String, Object> param) {
		return csBuyerServiceIndexMapper.selectPerformanceData(param);
	}

	@Override
	public List<Map<String, Object>> selectDetail(Map<String, Object> param) {
		return csBuyerServiceIndexMapper.selectDetail(param);
	}

	@Override
	public List<ReceiveUnSendDTO> selectReceiveUnSendEvalByDateByCsNickByBuyer(ShopCommonParam shop,
			List<String> csNickLst, Date startDate, Date endDate, String buyerNick, String buyerEvalInit,
			SortPageQuery sortPageQuery) {
		
		List<DateRangeParam> csBuyerServiceTables = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName());
		List<DateRangeParam> evalDetailTables = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName());
		List<DateRangeParam> sendEvalTables =  CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_SERVICE_SEND_EVAL.getName());
		return csBuyerServiceIndexMapper.selectReceiveUnSendEvalByDateByCsNickByBuyer(shop.getShopId(),startDate, endDate, csNickLst, buyerNick, buyerEvalInit,sortPageQuery, csBuyerServiceTables, evalDetailTables,sendEvalTables);
	}

	@Override
	public Integer selectReceiveUnSendEvalCount(ShopCommonParam shop, List<String> csNickLst, Date startDate, Date endDate,
			String buyerNick, String buyerEvalInit) {
		List<DateRangeParam> csBuyerServiceTables = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName());
		List<DateRangeParam> evalDetailTables = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_SERVICE_EVALUATION_DETAIL.getName());
		List<DateRangeParam> sendEvalTables =  CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_SERVICE_SEND_EVAL.getName());
		return csBuyerServiceIndexMapper.selectReceiveUnSendEvalCount(shop.getShopId(),startDate, endDate, csNickLst, buyerNick,
				buyerEvalInit, csBuyerServiceTables, evalDetailTables,sendEvalTables);
	}

}
