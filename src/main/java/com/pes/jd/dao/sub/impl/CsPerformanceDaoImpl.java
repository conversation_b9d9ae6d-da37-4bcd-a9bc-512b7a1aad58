package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsPerformanceDao;
import com.pes.jd.mapper.sub.CsPerformanceMapper;
import com.pes.jd.model.DO.CsPerformanceDO;
import com.pes.jd.model.DTO.CsPerformanceDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/3 3:50 PM
 * @since 1.0.0
 */
@Repository
public class CsPerformanceDaoImpl implements CsPerformanceDao {

    @Autowired
    private CsPerformanceMapper mapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return 0;
    }

    @Override
    public int insert(CsPerformanceDO record) {
        return 0;
    }

    @Override
    public int insertSelective(CsPerformanceDO record) {
        return 0;
    }

    @Override
    public CsPerformanceDO selectByPrimaryKey(Long id) {
        return null;
    }

    @Override
    public int updateByPrimaryKeySelective(CsPerformanceDO record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(CsPerformanceDO record) {
        return 0;
    }

    @Override
    public List<CsPerformanceDTO> searchByDateShopCs(Set<String> nicks, Long shopId, Date startDate, Date endDate, String groupBy, String schemaId, Set<Date> filterDates) {
        return CommonUtils.tablesMerge(
                startDate, endDate, schemaId, TableEnum.PES_CS_PERFORMANCE.getName(), (query) ->
                        mapper.searchByDateShopCs(nicks, shopId, query.getBeginDate(), query.getEndDate(), query.getTableName(),filterDates)
                , CommonUtils.MergeType.YEAR
        );

    }


    @Override
	public List<CsPerformanceDTO> selectCsPerformanceByCsNickByDateForRealTime(ShopQuery shop, List<String> csNickLst, Date startDate,
                                                                               Date endDate) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_PERFORMANCE.getName());
		return mapper.selectCsPerformanceByCsNickByDateForRealTime(shop.getShopId(),csNickLst, startDate, endDate, tableName);
	}
}
