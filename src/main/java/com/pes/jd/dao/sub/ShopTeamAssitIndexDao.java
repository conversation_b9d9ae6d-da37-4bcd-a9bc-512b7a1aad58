package com.pes.jd.dao.sub;

import com.pes.jd.model.DO.ShopTeamAssitIndexDO;

import java.util.Date;

public interface ShopTeamAssitIndexDao {

    int deleteByPrimaryKey(Long id);

    int insert(ShopTeamAssitIndexDO record, String schema);

    int insertSelective(ShopTeamAssitIndexDO record);

    ShopTeamAssitIndexDO selectByPrimaryKey(Long id);


    int updateByPrimaryKeySelective(ShopTeamAssitIndexDO record);

    int updateByPrimaryKey(ShopTeamAssitIndexDO record);

    int deleteByDateShopId(Long shopId, String schema, Date date);
}
