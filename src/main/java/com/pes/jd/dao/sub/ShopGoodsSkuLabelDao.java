package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO;
import com.pes.jd.model.Query.SkuQuery;

import java.util.List;

public interface ShopGoodsSkuLabelDao {

    List<ShopGoodsSkuLabelDTO> searchGoodsSkuBySkuIds(
            Long shopId, String schemaId, List<Long> skuIds
    );

    int deleteSkuLabel(Long id, SkuQuery skuQuery);

    int updateSkuLabel(ShopGoodsSkuLabelDTO record, SkuQuery skuQuery);

    Long insertSkuLabel(ShopGoodsSkuLabelDTO record, SkuQuery skuQuery);

    List<Long> searchGoodsSkuIds(Long shopId, String skuLabelTableName);

    Long insertSkuLabelLst(SkuQuery skuQuery, List<ShopGoodsSkuLabelDTO> gkLst);

    int updateSkuLabelByShopIdAndSkuIdsAndLabel(SkuQuery skuQuery, List<Long> skuIds, String label, String label1);

    int deleteSKuLabelBySkuIdsAndLabel(SkuQuery skuQuery, List<Long> skuIds, String shopGoodsLabelDTO);

    List<Long> searchGoodsLabelBySkuIds(SkuQuery skuQuery, List<Long> skuIds, String label);
}
