package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.OrderFilterDao;
import com.pes.jd.mapper.sub.OrderFilterMapper;
import com.pes.jd.model.DO.OrderFilter;
import com.pes.jd.model.DTO.OrderFilterDTO;
import com.pes.jd.model.Query.UserQuery;
import com.pes.jd.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName:OrderFilterDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:58:17 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository("orderFilterDao")
public class OrderFilterDaoImpl implements OrderFilterDao {

	@Autowired
	private OrderFilterMapper orderFilterMapper;

	@Override
	public int deleteOrderFilterById(Long id) {
		return orderFilterMapper.deleteOrderFilterById(id);
	}

	@Override
	public int insertOrderFilter(OrderFilter record) {
		return orderFilterMapper.insertOrderFilter(record);
	}

	@Override
	public OrderFilter getOrderFilterById(Long id) {
		return orderFilterMapper.getOrderFilterById(id);
	}

	@Override
	public int updateOrderFilterBySelective(OrderFilter record) {
		return orderFilterMapper.updateOrderFilterBySelective(record);
	}

	@Override
	public int persistOrderFilterByFile(String filePath, String tableName) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("filePath", filePath);
		map.put("tableName", tableName);
		return orderFilterMapper.persistOrderFilterByFile(map);
	}

	@Override
	public List<Long> selectIdsByOrderFilterIds(List<Long> oids, String tableName) {
		if(oids == null || oids.isEmpty())
			return null;
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("oids", oids);
		map.put("tableName", tableName);
		List<Long> ids= orderFilterMapper.selectIdsByOrderFilterIds(map);
		
		if(ids.size() != 0){
			return ids;
		}else{
			return null;
		}
	}

	@Override
	public int deleteOrdersFilterByIds(List<Long> ids, String tableName) {
		if(ids == null || ids.isEmpty())
			return 0;
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("ids", ids);
		map.put("tableName", tableName);
		return orderFilterMapper.deleteOrdersFilterByIds(map);
	}

	@Override
	public int persistOrderFilters(List<OrderFilterDTO> orders, String tableName) {
		if(orders==null||orders.size()==0){
			return 0;
		}
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("orders", orders);
		paramMap.put("tableName",tableName);
		return orderFilterMapper.persistOrderFilters(paramMap);
	}

	@Override
	public List<OrderFilterDTO> selectByOrderId(
			String filterTableName, String bindTableName, Date startDate,
			Date endDate, String shopId, List<UserQuery> nicks, String buyerNick, String orderId) {
		endDate = DateUtils.getEndTimeOfDate(endDate);
		return orderFilterMapper.selectByOrderId(filterTableName,bindTableName,
				startDate,endDate,shopId,nicks,buyerNick,orderId);
	}

}
