package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsTorderPerformanceDao;
import com.pes.jd.mapper.sub.CsTorderPerformanceMapper;
import com.pes.jd.model.DTO.CsTorderPerformanceDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/31 3:10 PM
 * @since 1.0.0
 */
@Repository
public class CsTorderPerformanceDaoImpl implements CsTorderPerformanceDao {


    @Autowired
    private CsTorderPerformanceMapper mapper;

    @Override
    public List<CsTorderPerformanceDTO> searchByDateShopNicks(
            Set<String> nicks, Long shopId, Date startDate, Date endDate, String schema, String queryType, Set<Date> filterDates) {
        return CommonUtils.tablesMerge(
                startDate, endDate, schema, TableEnum.PES_CS_TORDER_PERFORMANCE.getName(), (query) ->
                        mapper.searchByDateShopNicks(nicks, shopId, query.getBeginDate(), query.getEndDate(), query.getTableName(),filterDates),
                CommonUtils.MergeType.YEAR
        );

    }

    @Override
    public Double selectCsSaleAmount(Long shopId, Date startDate, Date endDate, String schema) {
        String tableName = CommonUtils.getTableNameOfYear(schema, endDate, TableEnum.PES_CS_TORDER_PERFORMANCE.getName());
        Double csSaleAmountForMonth = mapper.selectCsSaleAmount(shopId, startDate, endDate, tableName);
        return csSaleAmountForMonth;
    }



//


}
