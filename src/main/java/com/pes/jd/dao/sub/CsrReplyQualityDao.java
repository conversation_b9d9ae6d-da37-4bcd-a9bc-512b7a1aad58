package com.pes.jd.dao.sub;

import com.pes.jd.model.DO.CsrReplyQuality;

import java.util.List;
import java.util.Map;

public interface CsrReplyQualityDao {
	 CsrReplyQuality getCsrReplyQualityById(Long id);
	
	 int deleteCsrReplyQualityById(Long id);
	
	 int insertCsrReplyQuality(CsrReplyQuality csrReplyQuality);
	
	 int updateCsrReplyQualityById(CsrReplyQuality csrReplyQuality);

	List<CsrReplyQuality> selectCsrReplyQualityByCsNick(Map<String, Object> param);

	List<Map<String,Object>> selectCsrReplyQualityByShopIdNickSchema(Map<String, Object> param);
}

  
