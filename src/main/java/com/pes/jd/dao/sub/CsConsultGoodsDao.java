package com.pes.jd.dao.sub;


import com.pes.jd.model.DTO.CustConsultGoodsDTO;
import com.pes.jd.model.DTO.CustConsultGoodsV2DTO;
import com.pes.jd.model.Param.ShopCommonParam;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface CsConsultGoodsDao {
    List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku(ShopCommonParam shop, Integer result, List<Long> skuLst,
                                                                                          Date startDate, Date endDate, List<String> csNickList, String customer) throws SQLException;
    List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2(ShopCommonParam shop, Integer result, List<Long> skuLst,
                                                                                              Date startDate, Date endDate, List<String> csNickList, String customer) throws SQLException;

    List<CustConsultGoodsDTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV3(ShopCommonParam shop, Integer result, List<Long> skuLst, List<Long> categoryIds, Date startDate, Date endDate, List<String> csNickList, String customer);

    List<CustConsultGoodsV2DTO> selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4(ShopCommonParam shop, Integer result, List<Long> skuLst, List<Long> categoryIds, Date startDate, Date endDate, List<String> csNickList, String customer) throws SQLException;
}
