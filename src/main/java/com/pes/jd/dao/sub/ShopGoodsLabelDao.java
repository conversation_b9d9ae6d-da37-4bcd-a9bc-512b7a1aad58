package com.pes.jd.dao.sub;

import com.pes.jd.model.DO.ShopGoodsLabelDO;
import com.pes.jd.model.DTO.ShopGoodsLabelDTO;
import com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO;
import com.pes.jd.model.Query.SkuQuery;

import java.util.HashSet;
import java.util.List;

public interface ShopGoodsLabelDao {
    int insertSelective(ShopGoodsLabelDO record);

    List<Long> searchGoodsSkuIds(Long shopId, String labelTableName);

    int updateGoodLabel(ShopGoodsLabelDO record, SkuQuery skuQuery);

    Long insertGoodLabel(ShopGoodsLabelDO record, SkuQuery skuQuery);

    int deleteGoodsLabelById(Long id, SkuQuery skuQuery);

    ShopGoodsLabelDTO selectGoodsLabelById(Long id, SkuQuery skuQuery);

    List<ShopGoodsSkuLabelDTO> searchByWareIds(HashSet<Long> longs, String tableName, Long shopId);
}