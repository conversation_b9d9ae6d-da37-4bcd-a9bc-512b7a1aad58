package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.sub.GoodsRecommendSummaryDao;
import com.pes.jd.mapper.sub.GoodsRecommendSummaryMapper;
import com.pes.jd.model.DO.GoodsRecommendSummaryDO;
import com.pes.jd.model.DTO.GoodsRecommendSummaryDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.GoodsRecommedParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class GoodsRecommendSummaryDaoImpl implements GoodsRecommendSummaryDao {

	@Autowired
	private GoodsRecommendSummaryMapper goodsRecommendSummaryMapper;

	@Override
	public int deleteGoodsRecommendSummaryById(Long id) {
		return goodsRecommendSummaryMapper.deleteGoodsRecommendSummaryById(id);
	}

	@Override
	public int insertGoodsRecommendSummary(GoodsRecommendSummaryDO record) {
		return goodsRecommendSummaryMapper.insertGoodsRecommendSummary(record);
	}

	@Override
	public GoodsRecommendSummaryDO selectGoodsRecommendSummaryById(Long id) {
		return goodsRecommendSummaryMapper.selectGoodsRecommendSummaryById(id);
	}

	@Override
	public int updateGoodsRecommendSummary(GoodsRecommendSummaryDO record) {
		return goodsRecommendSummaryMapper.updateGoodsRecommendSummary(record);
	}

	@Override
	public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(ShopCommonParam shop,
			List<String> csNickLst, List<Long> skuLst, Date startDate, Date endDate) {
		if (CollectionUtils.isEmpty(csNickLst)) {
			return new ArrayList<GoodsRecommendSummaryDTO>(0);
		}
		List<GoodsRecommendSummaryDTO> goodsRecommendLst=Lists.newArrayList();
		List<DateRangeParam> tableNames=	CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_RECOMMEND_SUMMARY.getName());
		for (DateRangeParam dateRangeParam : tableNames) {
			List<GoodsRecommendSummaryDTO> list = goodsRecommendSummaryMapper.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick(shop.getShopId(), csNickLst, skuLst,dateRangeParam.getTableName(), dateRangeParam.getBeginDate(), dateRangeParam.getEndDate());
			if (CollectionUtils.isNotEmpty(list)) {
				goodsRecommendLst.addAll(list);
			}
		}
		return goodsRecommendLst;
	}

	@Override
	public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2(ShopCommonParam shop,
																								  List<String> csNickLst, List<Long> skuLst, List<Long> categoryIds, Date startDate, Date endDate) {
		if (CollectionUtils.isEmpty(csNickLst)) {
			return new ArrayList<GoodsRecommendSummaryDTO>(0);
		}
		List<GoodsRecommendSummaryDTO> goodsRecommendLst = Lists.newArrayList();
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_RECOMMEND_SUMMARY.getName());
		for (DateRangeParam dateRangeParam : tableNames) {
			String goodsSkuTable = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
			List<GoodsRecommendSummaryDTO> list = goodsRecommendSummaryMapper.selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2(shop.getShopId(), csNickLst, skuLst, categoryIds, dateRangeParam.getTableName(), goodsSkuTable, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate());

			if (CollectionUtils.isNotEmpty(list)) {
				goodsRecommendLst.addAll(list);
			}
		}
		return goodsRecommendLst;
	}

	@Override
	public List<GoodsRecommendSummaryDTO> selectGoodsRecommendSummaryByDateByShopIdByCsNick(ShopCommonParam shop,
																							GoodsRecommedParam param) {
		if (CollectionUtils.isEmpty(param.getCsNickLst())) {
			return new ArrayList<GoodsRecommendSummaryDTO>(0);
		}
		List<DateRangeParam> tableNames=	CommonUtils.getTableNames(param.getStartDate(), param.getEndDate(), shop.getSchemaId(),TableEnum.PES_GOODS_RECOMMEND_SUMMARY.getName());
		return goodsRecommendSummaryMapper.selectGoodsRecommendSummaryByDateByShopIdByCsNick(shop.getShopId(), param,tableNames);
	}

}
