package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsToOrderIndexDao;
import com.pes.jd.mapper.sub.CsToOrderIndexMapper;
import com.pes.jd.model.DTO.CsToOrderIndexDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/2/2 10:24 AM
 * @since 1.0.0
 */
@Repository
public class CsToOrderIndexDaoImpl implements CsToOrderIndexDao {

    @Autowired
    private CsToOrderIndexMapper mapper;

    @Override
    public List<CsToOrderIndexDTO> searchByDateShopNicks(
            Long shopId, Date startDate, Date endDate, Set<String> nicks, String queryType, String schema, Set<Date> filterDates) {
        return CommonUtils.tablesMerge(
                startDate,endDate,schema, TableEnum.PES_CS_TO_ORDER_INDEX.getName(),(query)->
                        mapper.searchByDateShopNicks(
                                shopId,query.getBeginDate(),query.getEndDate(),nicks,query.getTableName(),filterDates
                        ),
                CommonUtils.MergeType.YEAR
        );
    }
}
