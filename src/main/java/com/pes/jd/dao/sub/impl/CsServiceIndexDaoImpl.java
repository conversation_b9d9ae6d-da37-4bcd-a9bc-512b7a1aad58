package com.pes.jd.dao.sub.impl;

import com.google.common.collect.ImmutableMap;
import com.pes.jd.dao.sub.CsServiceIndexDao;
import com.pes.jd.mapper.sub.CsServiceIndexMapper;
import com.pes.jd.model.DTO.CsServiceIndexDTO;
import com.pes.jd.model.DTO.NickPerformanceDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public class CsServiceIndexDaoImpl implements CsServiceIndexDao {
	
	@Autowired
	private CsServiceIndexMapper csServiceIndexMapper;

	@Override
	public int batchInsertCsServiceIndex(ShopDTO shop, List<CsServiceIndexDTO> csServiceIndexLst) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_SERVICE_INDEX.getName(), shop.getShopId()+"");
		return csServiceIndexMapper.batchInsertCsServiceIndex(csServiceIndexLst, tableName);
	}

	@Override
	public int deleteShopCsServiceIndexByDate(ShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_SERVICE_INDEX.getName(), shop.getShopId()+"");
		return csServiceIndexMapper.deleteShopCsServiceIndexByDate(shop.getShopId(), date, tableName);
	}

	@Override
	public List<NickPerformanceDTO> selectByShopNickDate(Map<String, Object> param) {
		return csServiceIndexMapper.selectByShopNickDate(param);
	}

	@Override
	public List<Map<String, Object>> selectByShopNickDateList(
			Date startDate, Date endDate, String shopId, String tableName, String nick) {
		return csServiceIndexMapper.selectByShopNickDateList(ImmutableMap
				.<String,Object>builderWithExpectedSize(8)
				.put("startDate",startDate)
				.put("endDate",endDate)
				.put("shopId",shopId)
				.put("tableName",tableName)
				.put("nick",nick)
				.build());
	}

	@Override
	public CsServiceIndexDTO selectCsServiceIndexByCsNickByDateForRealTime(ShopQuery shop, String csNick,
			Date startDate, Date endDate) {
		String tableName=CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_CS_SERVICE_INDEX.getName());
		
		return csServiceIndexMapper.selectCsServiceIndexByCsNickByDateForRealTime(csNick, startDate, endDate, tableName);
	}

}
