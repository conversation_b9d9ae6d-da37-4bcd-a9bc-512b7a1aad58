package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsWordnumDao;
import com.pes.jd.mapper.sub.CsWordnumMapper;
import com.pes.jd.model.DO.CsWordnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * ClassName:CsWordnumDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:05:20 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository("csWordnumDao")
public class CsWordnumDaoImpl implements CsWordnumDao {

	@Autowired
	private CsWordnumMapper csWordnumMapper;

	@Override
	public int deleteCsWordnumById(Long id) {
		return csWordnumMapper.deleteCsWordnumById(id);
	}

	@Override
	public int insertCsWordnum(CsWordnum record) {
		return csWordnumMapper.insertCsWordnum(record);
	}

	@Override
	public CsWordnum getCsWordnumById(Long id) {
		return csWordnumMapper.getCsWordnumById(id);
	}

	@Override
	public List<CsWordnum> selectCsWordnumByNick(Map<String, Object> param) {
		return csWordnumMapper.selectCsWordnumByNick(param);
	}

	@Override
	public List<Map<String, Object>> selectCsWordnumShopIdAndNick(Map<String, Object> param) {
		return csWordnumMapper.selectCsWordnumShopIdAndNick(param);
	}

	@Override
	public int updateCsWordnumBySelective(CsWordnum record) {
		return csWordnumMapper.updateCsWordnumBySelective(record);
	}

}
