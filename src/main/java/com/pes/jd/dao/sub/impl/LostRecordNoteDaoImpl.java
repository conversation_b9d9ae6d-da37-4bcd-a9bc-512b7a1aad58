package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.sub.LostRecordNoteDao;
import com.pes.jd.mapper.sub.LostRecordNoteMapper;
import com.pes.jd.model.DO.LostRecordNote;
import com.pes.jd.model.DTO.LostRecordNoteDTO;
import com.pes.jd.model.DTO.ShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.LossOrderParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * ClassName:LostRecordNoteDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:41:09 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository("lostRecordNoteDao")
public class LostRecordNoteDaoImpl implements LostRecordNoteDao {

	@Autowired
	private LostRecordNoteMapper lostRecordNoteMapper;

	@Override
	public int insertLostRecordNoteForShop(ShopCommonParam shop, Date date, LostRecordNote lostRecordNote) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), date, TableEnum.PES_LOST_RECORD_NOTE.getName());
		return lostRecordNoteMapper.insertLostRecordNoteForShop(tableName,lostRecordNote);
	}
	
	@Override
	public int deleteLostRecordNoteById(Long id) {
		return lostRecordNoteMapper.deleteLostRecordNoteById(id);
	}
	
	@Override
	public int updateLostRecordNoteForShop(ShopCommonParam shop, Date date, LostRecordNoteDTO lostRecordNote) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), date, TableEnum.PES_LOST_RECORD_NOTE.getName());
		return lostRecordNoteMapper.updateLostRecordNoteForShop(tableName,lostRecordNote);
	}
	@Override
	public LostRecordNote getLostRecordNoteById(Long id) {
		return lostRecordNoteMapper.getLostRecordNoteById(id);
	}

	@Override
	public List<LostRecordNoteDTO> selectLostRecordNoteLst(ShopDTO shop, Date date, List<String> buyerNickLst, int lostType) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), date,TableEnum.PES_LOST_RECORD_NOTE.getName());
		return lostRecordNoteMapper.selectLostRecordNoteLst(shop.getShopId(),date,buyerNickLst,lostType,tableName);
	}

	@Override
	public LostRecordNoteDTO getShopBuyerLostRecordNoteByDate(ShopCommonParam shop, Date date, String buyerNick, String orderId,
			Integer lostType) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), date,TableEnum.PES_LOST_RECORD_NOTE.getName());
		return lostRecordNoteMapper.getShopBuyerLostRecordNoteByDate(shop.getShopId(),date,buyerNick,orderId,lostType,tableName);
	}

	@Override
	public List<LostRecordNoteDTO> selectByOrderId(String tableName, Long orderId) {
		return lostRecordNoteMapper.selectByOrderId(tableName,orderId);
	}

	@Override
	public List<LostRecordNoteDTO> selectByOrderId(String tableName, Set<String> orderIds) {
		return lostRecordNoteMapper.selectByOrderId(tableName,orderIds);
	}

	@Override
	public List<LostRecordNoteDTO> selectByBuyerNickDateShop(String tableName,
															 String buyerNick, String shopId, Date startDate,Date endDate) {
		return lostRecordNoteMapper.selectByBuyerNickDateShop(tableName, buyerNick, shopId, startDate,endDate);
	}

	@Override
	public List<LostRecordNoteDTO> selectLostRecordNoteLstByDateByBuyerNickList(Long shopId,String schemaId, Date startDate, Date endDate,List<String> buyerNickList, Integer noteEnquiryLost) {
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, schemaId, TableEnum.PES_LOST_RECORD_NOTE.getName());
		List<LostRecordNoteDTO> retLostRecordNoteLst = Lists.newArrayList();
		for (DateRangeParam drp : tableNames) {
			List<LostRecordNoteDTO> queryLostRecordNoteLst = lostRecordNoteMapper.selectLostRecordNoteLstByDateByBuyerNickList(shopId,drp.getBeginDate(),drp.getEndDate(),buyerNickList,noteEnquiryLost,drp.getTableName());
			if(CollectionUtils.isNotEmpty(queryLostRecordNoteLst)){
				retLostRecordNoteLst.addAll(queryLostRecordNoteLst);
			}
		}
		return retLostRecordNoteLst;
	}

	@Override
	public List<LostRecordNoteDTO> selectLostRecordNoteLstByOrderIdList(LossOrderParam lossOrderParam, Date startDate, Date endDate, List<Long> lossOrderIdList) {
		List<LostRecordNoteDTO> retLostRecordNoteLst = Lists.newArrayList();
		if (CollectionUtils.isEmpty(lossOrderIdList)) {
			return retLostRecordNoteLst;
		}
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, lossOrderParam.getSchemaId(), TableEnum.PES_LOST_RECORD_NOTE.getName());
		for (DateRangeParam drp : tableNames) {
			List<LostRecordNoteDTO> queryLostRecordNoteLst = lostRecordNoteMapper.selectLostRecordNoteLstByOrderIdList(lossOrderParam.getShopId(),lossOrderIdList,drp.getTableName());
			if(CollectionUtils.isNotEmpty(queryLostRecordNoteLst)){
				retLostRecordNoteLst.addAll(queryLostRecordNoteLst);
			}
		}
		return retLostRecordNoteLst;
	}

}
