package com.pes.jd.dao.sub;

import com.pes.jd.model.DO.CsServiceEvaluationDetail;
import com.pes.jd.model.DTO.CsServiceEvaluateDetailDTO;
import com.pes.jd.model.DTO.CsServiceStatisCountDTO;
import com.pes.jd.model.DTO.NickPerformanceDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CsServiceEvaluationDetailDao {
	public CsServiceEvaluationDetail getCsServiceEvaluationById(Long id);
	
	public int deleteCsServiceEvaluationById(Long id);

	List<CsServiceEvaluationDetail> selectCsServiceEvaluationByNick(Map<String, Object> param);

	List<Map<String, Object>> selectShopQNDailyEvalLst(String tableName, Long shopId, Date startDate, Date endDate);


	List<Map<String,Object>> selectByDateAndShopNick(Map<String, Object> param);


	List<CsServiceStatisCountDTO> selectCsNickServiceEvalByEvaltimeCount(Long shopId, Date startDate, Date endDate, String schemaId);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalSatisfactionCount(Long shopId, Date startDate, Date endDate, String schemaId);

	List<NickPerformanceDTO> selectPerformanceData(Map<String, Object> param);


	List<CsServiceStatisCountDTO> selectCsNickServiceEvalBySendTimeCount(ShopQuery shop, List<String> csNickLst, Date startDate, Date endDate);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalByEvalTimeCount(ShopQuery shop, List<String> csNickLst, Date startDate, Date endDate);

	List<CsServiceStatisCountDTO> selectCsNickServiceEvalSatisfactionCount(ShopQuery shop, List<String> csNickLst, Date startDate, Date endDate);

	List<CsServiceEvaluateDetailDTO> selectCsServiceEvalByCsNickByBuyerNickByEvalCode(ShopCommonParam shop, List<String> csNickList, Date startDate, Date endDate, String buyerNick, Integer evalCode, SortPageQuery sortPageQuery);

	List<CsServiceEvaluateDetailDTO> selectCsServiceEvalReceiveChatSession(ShopQuery shop, Date startDate, Date endDate);

	Integer selectCsServiceEvalCount(ShopCommonParam shop, List<String> csNickList, Date startDate, Date endDate, String buyerNick, Integer evalCode);
}

