package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.ReserveActivityDao;
import com.pes.jd.mapper.sub.ReserveActivityMapper;
import com.pes.jd.model.DTO.ReserveActivityDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.CommonParam;
import com.pes.jd.model.Param.CustConversionParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
public class ReserveActivityDaoImpl implements ReserveActivityDao {

    @Resource
    private ReserveActivityMapper reserveActivityMapper;

	@Override
	public List<ReserveActivityDTO> selectReserveActivity() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ReserveActivityDTO> selectPresaleActivityNow(ShopCommonParam shop) {
		Date now = new Date();
//		Date date = DateUtil.getDateByPeriod(now, -40);
//		Date date = new Date(CommonConstants.ACTIVITY_TIME);
		List<ReserveActivityDTO> resList = new ArrayList<ReserveActivityDTO>();
//		List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(date,now, shop.getSchemaId(),TableEnum.PES_RESERVE_ACTIVITY.getName());
//		//遍历 各月份表
//		for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
//			List<ReserveActivityDTO> itemList  = reserveActivityMapper.selectReserveActivityNow(dateRangeParam.getTableName(),
//					now,shop.getShopId());
//			if(CollectionUtils.isNotEmpty(itemList)) {
//				resList.addAll(itemList);
//			}
//			
//		}
		String tableName= CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_RESERVE_ACTIVITY.getName());
		resList = reserveActivityMapper.selectReserveActivityNow(tableName,
				now,shop.getShopId());
		
		return resList;
		
	}

	@Override
	public List<ReserveActivityDTO> selectShopReservePresaleByActId(ShopCommonParam shop, List<String> resIds) {
		Date now = new Date();
//		Date date = DateUtil.getDateByPeriod(now, -40);
//		Date date = new Date(CommonConstants.ACTIVITY_TIME);
		List<ReserveActivityDTO> resList = new ArrayList<ReserveActivityDTO>();
//		List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(date,now, shop.getSchemaId(),TableEnum.PES_RESERVE_ACTIVITY.getName());
//		//遍历 各月份表
//		for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
//			List<ReserveActivityDTO> resActList = reserveActivityMapper.selectShopReservePresaleByActId(dateRangeParam.getTableName(),
//					now,shop.getShopId(),resIds);
//					if(CollectionUtils.isNotEmpty(resActList)) {
//						resList.addAll(resList);
//					}
//		}

		String tableName= CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_RESERVE_ACTIVITY.getName());
		resList = reserveActivityMapper.selectShopReservePresaleByActId(tableName,
				now,shop.getShopId(),resIds);
		
		return resList;
	}

	@Override
	public List<ReserveActivityDTO> selectShopPresaleActivityByShopId(ShopCommonParam shop) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectShopReserveByShopId(tableName,shop.getShopId());
	}

	@Override
	public List<ReserveActivityDTO> selectShopReserveByShopIdByActiveityId(ShopCommonParam shop, String activityId) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectShopReserveByShopIdByActiveityId(shop.getShopId(),activityId,tableName);
	}


	@Override
	public List<ReserveActivityDTO> selectReserveActivityByShop(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuId, String activityId, String type, Long conditionType, String status) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectReserveActivityByShop(shop.getShopId(),startDate,endDate,skuId,activityId,type,conditionType,status,tableName);
	}

	@Override
	public List<ReserveActivityDTO> selectReserveActivityByConditionType(ShopCommonParam shop, Long conditionType, String activityId) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectReserveActivityByConditionType(shop.getShopId(),conditionType,activityId,tableName);
	}

	@Override
	public List<ReserveActivityDTO> selectReserveActivityByActivityId(ShopCommonParam shop, String activityId) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectReserveActivtyByActivityId(shop.getShopId(),activityId,tableName);
	}

	@Override
	public ReserveActivityDTO selectReserveActivityByActivityIdAndSkuId(ShopCommonParam shop, String activityId, Long skuId) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectReserveActivityByActivityIdAndSkuId(shop.getShopId(),activityId,skuId,tableName);
	}

	@Override
	public List<ReserveActivityDTO> selectReserveGoodsFromPra(ShopCommonParam shop) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_RESERVE_ACTIVITY.getName());
		return reserveActivityMapper.selectReserveGoodsFromPra(shop.getShopId(),tableName);
	}

	@Override
	public List<String> getPresaleActivityIds(ShopCommonParam shop, CustConversionParam param) {
		String tableName= CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_PRESALE_ACTIVITY.getName());
		return reserveActivityMapper.getPresaleActivityIds(shop.getShopId(),tableName, param.getStartDate(), param.getEndDate());
	}

}
