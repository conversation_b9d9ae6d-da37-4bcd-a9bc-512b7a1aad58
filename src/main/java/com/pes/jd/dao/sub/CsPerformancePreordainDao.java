package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.CsPerformancePreordainDTO;
import com.pes.jd.model.Param.ShopCommonParam;

import java.util.Date;
import java.util.List;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/3/12
 * @Modified By:
 */
public interface CsPerformancePreordainDao {



    List<CsPerformancePreordainDTO> selectCsPerformancePreordainDaily(ShopCommonParam shop, List<String> csNick, Date startDate, Date endDate, String sku, String activityId);

    List<CsPerformancePreordainDTO> selectPerformancePreordain(ShopCommonParam shop, List<Long> skuIds, Date startDate, Date endDate, String sku, String activityId);

    List<Long> selectPerformancePreordainSkuId(ShopCommonParam shop, List<Long> skuIds, Date startDate, Date endDate, String sku, String activityId);

    List<CsPerformancePreordainDTO> selectPerformancePreordainDetailed(ShopCommonParam shop, Date startDate, Date endDate, String csNick, Long skuId, String skuName, String activityId);

    List<CsPerformancePreordainDTO> selectCsPerformancePreordain(ShopCommonParam shop, List<String> csNick, Date startDate, Date endDate, String sku, String activityId);
}
