package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.OrderPreordainDTO;
import com.pes.jd.model.Param.OrderPreOrdainParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;

import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年03月06 14:19:19<br>
 */
public interface OrderPreordainDao {
    int selectCountOrderPreordainAndCsOrderBindForOrderPreordainAnalysis(ShopCommonParam shopCommonParam, OrderPreOrdainParam orderPreOrdainParam);

    List<OrderPreordainDTO> selectOrderPreordainAndCsOrderBindForOrderPreordainAnalysis(ShopCommonParam shopCommonParam, OrderPreOrdainParam orderPreOrdainParam, SortPageQuery sortPageQuery);
}
