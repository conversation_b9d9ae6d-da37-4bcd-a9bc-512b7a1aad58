package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsServiceSendEvalDao;
import com.pes.jd.mapper.sub.CsServiceSendEvalMapper;
import com.pes.jd.model.DO.CsServiceSendEvalDO;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class CsServiceSendEvalDaoImpl implements CsServiceSendEvalDao {

    @Autowired
    private CsServiceSendEvalMapper mapper;

    @Override
    public List<CsServiceSendEvalDO> searchAllDateShopGroup(
            Long shopId, Set<String> nicks, Date startDate, Date endDate, String queryType, String schema, Set<Date> filterDates) {

        return CommonUtils.tablesMerge(startDate,endDate,schema,"pes_cs_service_send_eval",(x)->
                mapper.searchAllDateShopGroup(
                        shopId,nicks,x.getBeginDate(),x.getEndDate(),queryType,x.getTableName(),filterDates
                ), CommonUtils.MergeType.MONTH);
    }
}
