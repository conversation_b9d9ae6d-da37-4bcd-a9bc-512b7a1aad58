package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.PesShopDsrDao;
import com.pes.jd.mapper.sub.PesShopDsrMapper;
import com.pes.jd.model.DTO.ShopDsrDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/11/7 4:25 PM
 * @since 1.0.0
 */
@Repository
public class PesShopDsrDaoImpl implements PesShopDsrDao {

    @Autowired
    private PesShopDsrMapper pesShopDsrMapper;

    @Override
    public List<ShopDsrDTO> selectByShopIdAndDate(ShopQuery shop, Integer dateType, List<String> dates) {
        String tableNames = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_DSR.getName());
        return pesShopDsrMapper.selectByShopIdAndDate(shop.getShopId(), dateType, dates, tableNames);
    }
}
