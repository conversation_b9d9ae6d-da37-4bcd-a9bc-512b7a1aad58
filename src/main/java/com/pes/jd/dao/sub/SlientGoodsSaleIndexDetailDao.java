package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.SlientGoodsSaleIndexDetailDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.SortPageQuery;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

public interface SlientGoodsSaleIndexDetailDao {
	List<SlientGoodsSaleIndexDetailDTO> selectGoodsSaleIndexDetailBySkuByDate(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst, SortPageQuery sortPageQuery, String orderId) throws SQLException;
	
	Integer selectGoodsSaleIndexDetailCount(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst, String orderId);

	List<SlientGoodsSaleIndexDetailDTO> selectGoodsSaleIndexDetailByShopIdBySkuByDate(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst, SortPageQuery sortPageQuery, String orderId, List<Long> bargainOrderIds);
}
