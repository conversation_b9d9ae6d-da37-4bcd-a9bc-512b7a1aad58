package com.pes.jd.dao.sub;

import com.pes.jd.model.DO.ShopPerformancePresaleDO;
import com.pes.jd.model.Query.ShopQuery;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ShopPerformancePresaleDao {


    List<ShopPerformancePresaleDO> selectByActivityIdAndSku(ShopQuery shop, Set<String> activityIds, Long skuId, String skuName);

    List<ShopPerformancePresaleDO>  selectByActivityIdAndSkuAndDate(ShopQuery shop, String activityId, Long skuId, Date startDate, Date endDate);
}
