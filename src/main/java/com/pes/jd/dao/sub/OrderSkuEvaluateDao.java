package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.CsOrderIndexDTO;
import com.pes.jd.model.DTO.OrderSkuEvaluateDTO;
import com.pes.jd.model.Param.OrderSkuEvaluateParam;
import com.pes.jd.model.Param.ShopCommonParam;

import java.util.Date;
import java.util.List;

public interface OrderSkuEvaluateDao {
	public List<OrderSkuEvaluateDTO> selectOrderSkuEvaluateByDateByCsNickByScore(
            ShopCommonParam shop, OrderSkuEvaluateParam param);

	public OrderSkuEvaluateDTO selectEvaluateInfoByOrderIdByBuyer(Date startDate, Date endDate, ShopCommonParam shop, Integer id, Long orderId);

	public List<CsOrderIndexDTO> selectCsReceptionByBuyerByOrderId(Date startDate, Date endDate, ShopCommonParam shop, Long orderId);
}
