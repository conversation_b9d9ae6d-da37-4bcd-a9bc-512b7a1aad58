package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.constants.PesConstants;
import com.pes.jd.dao.sub.OrderRefundDao;
import com.pes.jd.mapper.sub.OrderRefundMapper;
import com.pes.jd.model.DTO.OrderRefundDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.RefundAnalysisParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 退款分析Dao实现
 *
 * <AUTHOR>
 * @create 2019-01-16 09:22
 */
@Repository
public class OrderRefundDaoImpl implements OrderRefundDao {


    @Autowired
    private OrderRefundMapper orderRefundMapper;


    @Override
    public List<OrderRefundDTO> selectByRefundIdAndOrderIdMoreForRefundDataAnalysis(ShopQuery shop, RefundAnalysisParam param) {
        Date beginDate = param.getStartDate();
        Date endDate = param.getEndDate();
        Date queryStart = param.getStartDate();
        Date queryEnd = param.getEndDate();
        switch (param.getDateType()) {
            case 1:
                beginDate = DateUtil.getDateByPeriod(param.getStartDate(), -PesConstants.ORDER_REFUND_MODIFIED_DATE_NINE);
                endDate = param.getEndDate();
                break;
            case 2:
                break;
            case 3:
            case 4:
                long days = DateUtil.getStartToEndForDays(param.getStartDate(), new Date());
                if (days <= PesConstants.ORDER_REFUND_MODIFIED_DATE) {
                    endDate = new Date();
                    queryEnd = endDate;
                } else {
                    endDate = DateUtil.getDateByPeriod(param.getStartDate(), PesConstants.ORDER_REFUND_MODIFIED_DATE);
                    queryEnd = endDate;
                }
                break;
        }
        List<OrderRefundDTO> orderRefundLst = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(beginDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER_REFUND.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<OrderRefundDTO> refunds = orderRefundMapper.selectByRefundIdAndOrderIdMoreForRefundDataAnalysis(
                    shop.getShopId(), param.getRefundId(), param.getOrderId(), param.getCustomer(), param.getDateType(),
                    queryStart, queryEnd, param.getReason(), param.getRefundStatus(), param.getSkuIds(), dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(refunds)) {
                orderRefundLst.addAll(refunds);
            }
        }
        return orderRefundLst;
    }


    @Override
    public List<Long> selectShopIdAndOrderIdsAndDate(ShopCommonParam shopCommonParam, Set<Long> orderIds, Date startDate, Date endDate) {
        List<Long> orderRefundLst = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_REFUND.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<Long> refunds = orderRefundMapper.selectShopIdAndOrderIdsAndDate(shopCommonParam.getShopId(), orderIds, startDate, endDate, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(refunds)) {
                orderRefundLst.addAll(refunds);
            }
        }
        return orderRefundLst;
    }


}
