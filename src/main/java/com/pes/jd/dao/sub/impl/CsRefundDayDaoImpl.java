package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.CsRefundDayDao;
import com.pes.jd.mapper.sub.CsRefundDayMapper;
import com.pes.jd.model.DTO.CsRefundDayDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @<NAME_EMAIL>
 * @date 2019/1/14 1:53 PM
 * @since 1.0.0
 */
@Repository
public class CsRefundDayDaoImpl implements CsRefundDayDao {

    @Autowired
    private CsRefundDayMapper csRefundDayMapper;

    @Override
    public List<CsRefundDayDTO> searchAllDateShopGroup(Long shopId, Set<String> nicks, Date startDate, Date endDate, String groupBy, String schemaId, Set<Date> filterDates) {
        return CommonUtils.tablesMerge(
                startDate,endDate,schemaId,TableEnum.PES_CS_REFUND_DAY.getName(),(query)->
                        csRefundDayMapper.searchAllDateShopGroup(
                                shopId,nicks,query.getBeginDate(),query.getEndDate(),groupBy,query.getTableName(),filterDates
                        ), CommonUtils.MergeType.YEAR
        );
    }
}
