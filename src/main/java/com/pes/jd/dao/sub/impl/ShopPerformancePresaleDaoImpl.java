package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.ShopPerformancePresaleDao;
import com.pes.jd.mapper.sub.ShopPerformancePresaleMapper;
import com.pes.jd.model.DO.ShopPerformancePresaleDO;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class ShopPerformancePresaleDaoImpl implements ShopPerformancePresaleDao {
    @Resource
    private ShopPerformancePresaleMapper shopPerformancePresaleMapper;

    @Override
    public List<ShopPerformancePresaleDO> selectByActivityIdAndSku(ShopQuery shop, Set<String> activityIds, Long skuId, String skuName) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_performance_presale");
        return shopPerformancePresaleMapper.selectByActivityIdAndSku(shop.getShopId(), activityIds, skuId, skuName, tableName);
    }
    @Override
    public List<ShopPerformancePresaleDO>  selectByActivityIdAndSkuAndDate(ShopQuery shop, String activityId, Long skuId, Date startDate, Date endDate){
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_performance_presale");
        return shopPerformancePresaleMapper.selectByActivityIdAndSkuAndDate(shop.getShopId(), activityId, skuId, startDate, endDate, tableName);
    }
}
