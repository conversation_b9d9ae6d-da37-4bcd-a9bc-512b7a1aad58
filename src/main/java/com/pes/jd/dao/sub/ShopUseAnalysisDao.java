package com.pes.jd.dao.sub;

import com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月10 11:41:41<br>
 */
public interface ShopUseAnalysisDao {

    List<ShopUseAnalysis> selectShopUseAnalysisByShopIdSetByDate(String schemaId, Set<Long> shopIdSet, Date startDate, Date endDate);

    List<Long> selectProblemShopIdLst(String dbName, String schemaId, Date startDate, Date endDate);
}
