package com.pes.jd.dao.sub.impl;

import com.google.common.collect.Lists;
import com.pes.jd.business.sub.impl.PresaleDateUtil;
import com.pes.jd.constants.PesConstants;
import com.pes.jd.dao.sub.OrderPresaleDao;
import com.pes.jd.mapper.sub.OrderPresaleMapper;
import com.pes.jd.model.DTO.CsOrderBindDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.OrderPresaleParam;
import com.pes.jd.model.Param.OrderPresaleUnbalanceParam;
import com.pes.jd.model.Param.RefundAnalysisParam;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.model.Query.ShopQuery;
import com.pes.jd.model.Query.SortPageQuery;
import com.pes.jd.model.VO.OrderPresaleUnbalanceVO;
import com.pes.jd.model.VO.OrderPresaleVO;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.MultiTablePagingUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * <AUTHOR>
 * @create 2019-01-18 11:02
 */
@Repository
public class OrderPresaleDaoImpl implements OrderPresaleDao {

    @Autowired
    private OrderPresaleMapper orderPresaleMapper;

    /**
     * 获取 当前条件下预售订单 各月份表中count 和 总count
     */
    @Override
    public Map<String, Integer> selectCountForOrderPresaleAnalysis(ShopCommonParam shopCommonParam, OrderPresaleParam orderPresaleParam) {
        Map<String, Integer> tableTotalRecordNumMap = new HashMap<>();
        int count = 0;
        Date beginDate = orderPresaleParam.getStartDate();
        switch (orderPresaleParam.getDateType()) {
            case 1:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE);
                break;
            case 2:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY);
                break;
        }
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(beginDate, orderPresaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            int tableCount = orderPresaleMapper.selectCountForOrderPresaleAnalysis(shopCommonParam.getShopId(), orderPresaleParam.getDateType(), orderPresaleParam.getStartDate(), orderPresaleParam.getEndDate(), orderPresaleParam.getBuyerNick(), orderPresaleParam.getOrderId(), orderPresaleParam.getTradeType(), dateRangeParam.getTableName());
            //以当前 表名 作为key， 当前表count 作为value
            tableTotalRecordNumMap.put(dateRangeParam.getTableName(), tableCount);
            //统计已查询的表的 总count
            count += tableCount;
        }
        //将总 count添加到 map
        tableTotalRecordNumMap.put("count", count);
        return tableTotalRecordNumMap;
    }

    @Override
    public List<OrderPresaleVO> selectForOrderPresaleAnalysis(ShopCommonParam shopCommonParam, OrderPresaleParam orderPresaleParam, SortPageQuery sortPageQuery, Map<String, Integer> tableTotalRecordNumMap) {
        Date beginDate = orderPresaleParam.getStartDate();
        switch (orderPresaleParam.getDateType()) {
            case 1:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE);
                break;
            case 2:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY);
                break;
        }
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(beginDate, orderPresaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        List<OrderPresaleVO> orderPresaleLst = new ArrayList<>();
        if (sortPageQuery != null && sortPageQuery.getSize() > 0 && tableTotalRecordNumMap != null) {
            MultiTablePagingUtil paging = new MultiTablePagingUtil(sortPageQuery.getCurrentPage().intValue(), sortPageQuery.getSize().intValue(), tableTotalRecordNumMap);
            //遍历 各月份表
            for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
                // 获取 对当前表处理状态
                // 0 数据满 return | 1 查询当前表数据，继续拿数据 | 2 跳过该表
                int flag = paging.nextTable(dateRangeParam.getTableName());
                if (flag == 1) {
                    //查询当前月分表
                    List<OrderPresaleVO> vos = orderPresaleMapper.selectForOrderPresaleAnalysis(shopCommonParam.getShopId(), orderPresaleParam.getDateType(), orderPresaleParam.getStartDate(), orderPresaleParam.getEndDate(), orderPresaleParam.getBuyerNick(), orderPresaleParam.getOrderId(), orderPresaleParam.getTradeType(), dateRangeParam.getTableName(), paging.getTableStartIndex(), paging.getTableRecordLength());
                    //计算已查询count
                    paging.addAndGetTotalRecordNum(CollectionUtils.isEmpty(vos) ? 0 : vos.size());
                    //添加该月份结果集 到 总结果集
                    if (CollectionUtils.isNotEmpty(vos)) {
                        orderPresaleLst.addAll(vos);
                    }
                }
                //判断 是否接着查，不用则 return 结果集
                if (!paging.hasNext()) {
                    return orderPresaleLst;
                }
            }
        } else {
            for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
                List<OrderPresaleVO> vos = orderPresaleMapper.selectForOrderPresaleAnalysis(shopCommonParam.getShopId(), orderPresaleParam.getDateType(), orderPresaleParam.getStartDate(), orderPresaleParam.getEndDate(), orderPresaleParam.getBuyerNick(), orderPresaleParam.getOrderId(), orderPresaleParam.getTradeType(), dateRangeParam.getTableName(), null, null);
                if (CollectionUtils.isNotEmpty(vos)) {
                    orderPresaleLst.addAll(vos);
                }
            }
        }
        return orderPresaleLst;
    }

    @Override
    public List<Long> selectOrderIdByShopIdAndOrderIdsAndDate(ShopCommonParam shopCommonParam, Collection<Long> orderIds, Date startDate, Date endDate) {
        List<Long> longList = new ArrayList<>();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<Long> list = orderPresaleMapper.selectOrderIdByShopIdAndOrderIdsAndDate(shopCommonParam.getShopId(), orderIds, startDate, endDate, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(list)) {
                longList.addAll(list);
            }
        }
        return longList;
    }

    @Override
    public List<CsOrderBindDTO> selectCsNickByOrderIdsForPresaleAnalysis(ShopCommonParam shopCommonParam, OrderPresaleParam orderPresaleParam, Set<Long> orderIds) {
        Date beginDate = orderPresaleParam.getStartDate();
        Date endDate = orderPresaleParam.getEndDate();
        long days=0L;
        switch (orderPresaleParam.getDateType()) {
            case 1:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE);
                break;
            case 2:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -PesConstants.ORDER_TO_PAY_DAY - orderPresaleParam.getEnquiryValidDurationTime());
                 days = DateUtil.getStartToEndForDays(beginDate, new Date());
                if (days <= PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE)
                    endDate = new Date();
                else
                    endDate = DateUtil.getDateByPeriod(beginDate, PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE);

                break;
            case 3:
                beginDate = DateUtil.getDateByPeriod(orderPresaleParam.getStartDate(), -orderPresaleParam.getEnquiryValidDurationTime());
                 days = DateUtil.getStartToEndForDays(beginDate, new Date());
                if (days <= PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE)
                    endDate = new Date();
                else
                    endDate = DateUtil.getDateByPeriod(beginDate, PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE);
                break;
        }
        List<CsOrderBindDTO> csOrderBindLst = new ArrayList<>();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(beginDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<CsOrderBindDTO> dtos = orderPresaleMapper.selectCsNickByOrderIdsForPresaleAnalysis(shopCommonParam.getShopId(), orderIds, dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), orderPresaleParam.getCsNickLst(), dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(dtos)) {
                csOrderBindLst.addAll(dtos);
            }
        }
        return csOrderBindLst;
    }

    @Override
    public int selectCountOrderPresaleAndCsOrderBindForOrderPresaleAnalysis(ShopCommonParam shopCommonParam, OrderPresaleParam orderPresaleParam,List<Long> filterOrderIds) {
        PresaleDateUtil presaleDateUtil = new PresaleDateUtil(orderPresaleParam).invoke();
        Date beginDate = presaleDateUtil.getBeginDate();
        Date bindBeginDate = presaleDateUtil.getBindBeginDate();
        Date bindEndDate = presaleDateUtil.getBindEndDate();
        List<CommonUtils.DateRangeParam> orderPresaleTables = CommonUtils.getTableNames(beginDate, orderPresaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        List<CommonUtils.DateRangeParam> csOrderBindTables = CommonUtils.getTableNames(bindBeginDate, bindEndDate, shopCommonParam.getSchemaId(), TableEnum.PES_CS_ORDER_BIND.getName());
        return orderPresaleMapper.selectCountOrderPresaleAndCsOrderBindForOrderPresaleAnalysis(shopCommonParam.getShopId(), orderPresaleParam.getDateType(), orderPresaleParam.getStartDate(), orderPresaleParam.getEndDate(), orderPresaleParam.getBuyerNick(), orderPresaleParam.getOrderId(), orderPresaleParam.getTradeType(), orderPresaleParam.getGroupId(), orderPresaleParam.getCsNickLst(),filterOrderIds, orderPresaleTables, csOrderBindTables);
    }

    @Override
    public List<OrderPresaleVO> selectOrderPresaleForOrderPresaleAnalysis(ShopCommonParam shopCommonParam, OrderPresaleParam orderPresaleParam, SortPageQuery sortPageQuery,List<Long> filterOrderIds) {
        PresaleDateUtil presaleDateUtil = new PresaleDateUtil(orderPresaleParam).invoke();
        Date beginDate = presaleDateUtil.getBeginDate();
        List<CommonUtils.DateRangeParam> orderPresaleTables = CommonUtils.getTableNames(beginDate, orderPresaleParam.getEndDate(), shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        return orderPresaleMapper.selectOrderPresaleForOrderPresaleAnalysis(shopCommonParam.getShopId(), orderPresaleParam.getDateType(),
                orderPresaleParam.getStartDate(), orderPresaleParam.getEndDate(), orderPresaleParam.getBuyerNick(), orderPresaleParam.getOrderId(),
                orderPresaleParam.getTradeType(), orderPresaleParam.getGroupId(), orderPresaleParam.getCsNickLst(),filterOrderIds, orderPresaleTables, sortPageQuery);
    }

    @Override
    public List<OrderPresaleUnbalanceVO> selectOrderPresaleUnbalance(ShopCommonParam shopCommonParam, OrderPresaleUnbalanceParam presaleUnbalanceParam) {
        List<OrderPresaleUnbalanceVO> voList = Lists.newArrayList();
        Date end = new Date();
        Date start = DateUtil.getDateByPeriod(end, -PesConstants.ORDER_PRESALE_PAY_BALANCE_DATE);
        List<CommonUtils.DateRangeParam> orderPresaleTables = CommonUtils.getTableNames(start, end, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());

        for (CommonUtils.DateRangeParam dateRangeParam : orderPresaleTables) {
            List<OrderPresaleUnbalanceVO> vos = orderPresaleMapper.selectOrderPresaleUnbalance(shopCommonParam.getShopId(), presaleUnbalanceParam.getSkuId(), presaleUnbalanceParam.getBalanceEndTime(), dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(vos))
                voList.addAll(vos);
        }
        return voList;
    }

    @Override
    public List<OrderPresaleVO> selectPageByShopIdAndDateAndOrderIdAndBuyerNick(ShopCommonParam shopCommonParam,
                                                                                Date startDate, Date endDate,
                                                                                Long orderId, String buyerNick,
                                                                                SortPageQuery query, Map<String,
            Integer> tableTotalRecordNumMap,List<Long> filterOrderIds) {
        //根据开始时间、结束时间 获取 月份表集合
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());

        List<OrderPresaleVO> orderLst = Lists.newArrayList();
        //size > 0 ? 分页 : 不分表页
        if (query.getSize() > 0) {
            MultiTablePagingUtil paging = new MultiTablePagingUtil(query.getCurrentPage().intValue(), query.getSize().intValue(), tableTotalRecordNumMap);
            //遍历 各月份表
            for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
                // 获取 对当前表处理状态
                // 0 数据满 return | 1 查询当前表数据，继续拿数据 | 2 跳过该表
                int flag = paging.nextTable(dateRangeParam.getTableName());
                if (flag == 1) {
                    //查询当前月分表
                    List<OrderPresaleVO> orders = orderPresaleMapper.selectPageByShopIdAndDateAndOrderIdAndBuyerNick(shopCommonParam.getShopId(), dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, orderId,filterOrderIds, dateRangeParam.getTableName(), paging.getTableStartIndex(), paging.getTableRecordLength());
                    //计算已查询count
                    paging.addAndGetTotalRecordNum(CollectionUtils.isEmpty(orders) ? 0 : orders.size());
                    //添加该月份结果集 到 总结果集
                    if (CollectionUtils.isNotEmpty(orders)) {
                        orderLst.addAll(orders);
                    }
                }
                //判断 是否接着查，不用则 return 结果集
                if (!paging.hasNext()) {
                    return orderLst;
                }
            }
        } else {
            //遍历 各月份表
            for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
                List<OrderPresaleVO> orders = orderPresaleMapper.selectPageByShopIdAndDateAndOrderIdAndBuyerNick(shopCommonParam.getShopId(), dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, orderId, filterOrderIds,dateRangeParam.getTableName(), null, null);
                if (CollectionUtils.isNotEmpty(orders))
                    orderLst.addAll(orders);
            }
        }
        return orderLst;
    }

    @Override
    public Map<String, Integer> selectCountByShopIdAndDateAndOrderIdAndBuyerNick(ShopCommonParam shopCommonParam, Date startDate, Date endDate, Long orderId, String buyerNick,List<Long> filterOrderIds) {
        Map<String, Integer> tableTotalRecordNumMap = new HashMap<>();
        int count = 0;
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            //查询 当前月份表 中 符合条件的 count
            int tableCount = orderPresaleMapper.selectCountByShopIdAndDateAndOrderIdAndBuyerNick(shopCommonParam.getShopId(), dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), buyerNick, orderId,filterOrderIds,dateRangeParam.getTableName());
            //以当前 表名 作为key， 当前表count 作为value
            tableTotalRecordNumMap.put(dateRangeParam.getTableName(), tableCount);
            //统计已查询的表的 总count
            count += tableCount;
        }
        //将总 count添加到 map
        tableTotalRecordNumMap.put("count", count);
        return tableTotalRecordNumMap;
    }



    @Override
    public List<OrderPresaleVO> selectOrderInfoByOrderIds(ShopCommonParam shopCommonParam, Collection<Long> orderIds, Date startDate, Date endDate) {
        List<OrderPresaleVO> orderLst = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<OrderPresaleVO> dtos = orderPresaleMapper.selectOrderInfoByOrderIds(shopCommonParam.getShopId(), orderIds, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(dtos)) {
                orderLst.addAll(dtos);
            }
        }
        return orderLst;
    }

    @Override
    public List<Long> selectOrderIdsUnbalance(ShopCommonParam shopCommonParam, Date startDate, Date endDate) {
        List<Long> orderIds = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<Long> dtos = orderPresaleMapper.selectOrderIdsUnbalance(shopCommonParam.getShopId(), dateRangeParam.getTableName(), startDate, endDate);
            if (CollectionUtils.isNotEmpty(dtos)) {
                orderIds.addAll(dtos);
            }
        }
        return orderIds;
    }

    @Override
    public List<OrderPresaleVO> selectFullPayOrderInfoByOrderIds(ShopCommonParam shopCommonParam, Set<Long> orderIds, Date startDate, Date endDate) {
        List<OrderPresaleVO> orderLst = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNames(startDate, endDate, shopCommonParam.getSchemaId(), TableEnum.PES_ORDER_PRESALE.getName());
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<OrderPresaleVO> dtos = orderPresaleMapper.selectFullPayOrderInfoByOrderIds(shopCommonParam.getShopId(), orderIds, dateRangeParam.getTableName());
            if (CollectionUtils.isNotEmpty(dtos)) {
                orderLst.addAll(dtos);
            }
        }
        return orderLst;
    }

    @Override
    public List<Long> selectOrderIdsByCreated(ShopQuery shop, RefundAnalysisParam param, Date presaleStartTime) {
        List<Long> result = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(presaleStartTime, param.getEndDate(), shop.getSchemaId(), "pes_order_presale");
        for (CommonUtils.DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            Optional<List<Long>> optional = Optional.ofNullable(orderPresaleMapper.selectOrderIdsByCreated(shop.getShopId(), presaleStartTime, param.getEndDate(), tableName));
            optional.ifPresent(result::addAll);
        }
        return result;
    }

}
