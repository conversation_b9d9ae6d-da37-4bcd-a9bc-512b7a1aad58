package com.pes.jd.dao.sub.impl;

import com.pes.jd.dao.sub.SlientGoodsSaleIndexDao;
import com.pes.jd.mapper.sub.SlientGoodsSaleIndexMapper;
import com.pes.jd.model.DTO.SlientGoodsSaleIndexDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class SlientGoodsSaleIndexDaoImpl implements SlientGoodsSaleIndexDao {

	@Autowired
	private SlientGoodsSaleIndexMapper goodsSaleIndexMapper;

	@Override
	public List<SlientGoodsSaleIndexDTO> selectGoodsSaleIndexByDateBySku(ShopCommonParam shop, Date startDate, Date endDate, List<Long> skuLst){
		List<DateRangeParam> tableNames = CommonUtils.getTableNames(startDate,endDate,shop.getSchemaId(), TableEnum.PES_SLIENT_GOODS_SALE_INDEX.getName());
		String tableName2 = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS_SKU.getName());
		return goodsSaleIndexMapper.selectGoodsSaleIndexByDateBySku(shop.getShopId(), startDate, endDate, skuLst, tableNames, tableName2);
	}
	

}
