package com.pes.jd.dao.sub;

import com.pes.jd.model.DTO.CsChatSessionServiceIndexDTO;
import com.pes.jd.model.Query.ShopQuery;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/14 3:36 PM
 * @since 1.0.0
 */
public interface CsChatSessionServiceIndexDao {

    List<CsChatSessionServiceIndexDTO> searchByDateShopCs(
            Set<String> nicks,
            Long shopId,
            Date startDate,
            Date endDate,
            String queryType,
            String schemaId,
            Set<Date> filterDates);

    List<CsChatSessionServiceIndexDTO> selectCsChatSessionServiceIndexByCsNickByDateForRealTime(ShopQuery shop, List<String> csNickLst, Date startDate, Date endDate);

}
