package com.pes.jd.dao;

import com.pes.jd.model.DO.Cs;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CsDao {
	public Cs getCsById(Long id);

	public int deleteCsById(Long id);

	public int insertCs(Cs cs);

	public int updateCsById(Cs cs);

	List<CsDTO> selectCsLstByShop(@Param("shop")JobShopDTO shop,
			@Param("csType")Integer csType);

	List<CsDTO> selectCsLstByShopIdAndType(@Param("shopId")Long shopId, @Param("csType")Integer csType);



}

