package com.pes.jd.dao;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodsDTO;

import java.util.List;

public interface ShopGoodDao {
	int batchShopGood(JobShopDTO shop, List<ShopGoodsDTO> shopGoodLst);

	int deleteByShopGoodsName(JobShopDTO shop);

//	int deleteByShopGoodsNameAndWareId(JobShopDTO shop, GoodskuParam goodskuParamPojo);

	int batchUpdateShopGood(JobShopDTO shop, List<ShopGoodsDTO> shopGoodLst);

    int deleteByShopIdAndWareIdLst(JobShopDTO shop, List<ShopGoodsDTO> wareIdLst);

    int selectShopGoodNumByShopId(JobShopDTO jobShop);

    int deleteByShopGoodsNameAndWareIds(JobShopDTO shop, List<Long> wareIdLst);
}
