package com.pes.jd.dao;

import java.util.Date;
import java.util.List;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.OrderCancelDTO;
import com.pes.jd.model.DTO.OrderCancelGoodsSkuDTO;
import com.pes.jd.model.Query.JobDateQuery;

public interface OrderCancelDao {
	
	List<OrderCancelDTO> selectOrderCancelByShopIdAndDate(JobShopDTO shop, Date startDate, Date endDate);
	
	List<OrderCancelGoodsSkuDTO> selectOrderCancelGoodsSkuByShopIdAndDate(JobShopDTO shop, Date startDate, Date endDate);

}
