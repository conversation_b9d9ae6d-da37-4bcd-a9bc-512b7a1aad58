package com.pes.jd.dao;

import com.pes.jd.model.DO.CsRecommendGoodsDO;
import com.pes.jd.model.DTO.CsPurchaseGoodsResultDTO;
import com.pes.jd.model.DTO.CsRecommendGoodsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.csRecommentGoodsSummaryDTO;

import java.util.Date;
import java.util.List;

public interface CsRecommendGoodsDao {
    int deleteCsRecommendGoodsById(Long id);

    int insertCsRecommendGoods(CsRecommendGoodsDO record);

    CsRecommendGoodsDO selectCsRecommendGoodsById(Long id);

    int  batchUpdateCsRecommendGoods(JobShopDTO shop, Date date,List<CsPurchaseGoodsResultDTO> resultLst);
    
    int  batchInsertCsRecommendGoods(JobShopDTO shop, Date date, List<CsRecommendGoodsDO> csRecommendGoodsLst);

    int deleteTargetDateShopCsRecommendGoods(JobShopDTO shop, Date date);
    
    int deleteCsRecommendGoodsByShopIdAndDate(JobShopDTO shop,Date startDate,Date endDate);

    List<csRecommentGoodsSummaryDTO> selectCsRecommentNumByShopIdAndCsNickAndDate(JobShopDTO shop,String csNick,Date startDate,Date endDate);
    
    List<csRecommentGoodsSummaryDTO> selectPurchasesBuyerNumByShopIdAndCsNickAndDate(JobShopDTO shop,String csNick, Date startDate,Date endDate);
    
    List<CsRecommendGoodsDTO> selectCsRecommentGoodsPurchaseResultByCsNickAndDate(JobShopDTO shop,String csNick,Date startDate,Date endDate);

	List<CsPurchaseGoodsResultDTO> selectCsRecommentByshopIdByDate(JobShopDTO shop, Date startDate, Date endDate);
}