package com.pes.jd.dao;

import com.pes.jd.model.DO.ShopGoodsReviewDO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodsReviewDTO;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;

public interface ShopGoodsReviewDao {

	int insertShopGoodsReviewList(JobShopDTO shop, Date date, List<ShopGoodsReviewDO> goodsReviewDOLst);

	int deleteShopGoodsReviewByDateByShopId(JobShopDTO shop, Date date);

	List<ShopGoodsReviewDTO> selectShopCsReviewByOrderId(JobShopDTO shop, Date date, List<ShopGoodsReviewDTO> list);

	List<ShopGoodsReviewDTO> selectShopCsOrderByCsBySendTime(JobShopDTO shop, List<CsDTO> csLst, Date date);

    List<ShopGoodsReviewDO> selectShopCsOrderBySendTime(JobShopQuery jobShop, Date date);
}
