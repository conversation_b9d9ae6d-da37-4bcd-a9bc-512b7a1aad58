package com.pes.jd.dao;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.LossOrderRecordDTO;
import com.pes.jd.model.Query.ValidDateRangeQuery;

import java.util.Date;
import java.util.List;

public interface LossOrderRecordDao {

	int insertBatchShopLossOrderEnquiryLoss(JobShopDTO shop, List<LossOrderRecordDTO> lossOrderEnquiryDOList, Date date);

	int deleteShopLossOrderByCratedDateByType(JobShopDTO shop, ValidDateRangeQuery drq, Integer lossTypeOutStock);

	List<Long> selectLossEnquiryOrderIdByDate(JobShopDTO shop, Date date, Integer status, Integer lossType);

	List<LossOrderRecordDTO> selectLossEnquiryOrderByDate(JobShopDTO shop, Date date, Integer status, ValidDateRangeQuery drq);

	List<LossOrderRecordDTO> selectOrderPaymentLossByDateForCalculatePaymentLoss(JobShopDTO shop, Date startTimeOfDate, Date endTimeOfDate);

}
  
