package com.pes.jd.dao;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodSkuDTO;

import java.util.List;
import java.util.Set;

public interface ShopGoodsSkuDao {
    int batchShopGoodsSku(JobShopDTO shop, List<ShopGoodSkuDTO> shopGoodsSkuLst);

    int deleteByShopGoodsSkuName(JobShopDTO shop);

//	int deleteByShopGoodsSkuNameBySkuId(JobShopDTO shop, GoodskuParam goodskuParamPojo);

    int deleteByShopIdAndSkuIdLst(JobShopDTO shop, List<Long> skuIdLst);

    List<ShopGoodSkuDTO> selectShopGoodsSkuByShopId(JobShopDTO shop);

    List<ShopGoodSkuDTO> selectShopGoodsSkuByShopIdAndSkuIdLst(JobShopDTO shop, List<Long> skuIdLst);

    List<ShopGoodSkuDTO> selectByShopIdAndSkuIds(JobShopDTO shop, Set<Long> skuIds);

    ShopGoodSkuDTO queryShopGoodsInfoBySkuIdAndShopId(JobShopDTO shopDTO, Long skuId);

    long selectShopGoodsSkuNumByShopId(JobShopDTO shop);

    int insertShopGoodSkuByFile(JobShopDTO shop, String absolutePath);

    Set<Long> selectShopGoodsSkuIdByShopId(JobShopDTO shop);

    Set<Long> selectShopGoodsSkuIdByShopIdBySkuIds(JobShopDTO shop, Set<Long> skuIdSet);

    List<Long> selectShopGoodsSkuIdByShopIdByStatus(JobShopDTO shop, Integer status);

    List<ShopGoodSkuDTO> selectShopGoodsSkuByShopIdByStatus(JobShopDTO shop, Integer status,int currentPage,int pageSize);

    Integer selectSkuCountByShopIdByStatus(JobShopDTO shop, Integer status);
}
