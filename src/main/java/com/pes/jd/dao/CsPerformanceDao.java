package com.pes.jd.dao;

import com.pes.jd.model.DO.CsPerformanceDO;
import com.pes.jd.model.DTO.CsPerformanceDTO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;

public interface CsPerformanceDao {
	
	 int insertCsPerformance(JobShopDTO shop, Date date, CsPerformanceDO csDayPerformance);
	
	 int insertTargetDateCsPerformances(JobShopDTO shop, Date date, List<CsPerformanceDO> csDayPerformanceLst);

	 int deleteCsPerformanceById(Long id);
	
	int deleteShopCsPerformanceByDate(JobShopDTO shop, Date date);
	
	int deleteShopCsPerformanceByDateRange(JobShopDTO shop, Date startDate, Date endDate);

	 int updateCsPerformanceById(CsPerformanceDO csPerformance);

	
	 CsPerformanceDO getCsPerformanceById(Long id);

	List<CsPerformanceDTO> searchByDateShopCs(
			List<String> nicks,
			Long shopId,
			Date startDate,
			Date endDate,
			String groupBy,
			String tableName
	);



}
  
