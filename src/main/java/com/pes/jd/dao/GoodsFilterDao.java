package com.pes.jd.dao;

import com.pes.jd.model.DO.GoodsFilter;
import com.pes.jd.model.DTO.GoodsFilterDTO;

import java.util.List;

public interface GoodsFilterDao {
	int insertGoodsFilter(GoodsFilter record);

	int deleteGoodsFilterById(Long id);

	int updateGoodsFilterBySelective(GoodsFilter record);

	GoodsFilter getGoodsFilterById(Long id);
	
	List<GoodsFilterDTO> queryPesGoodsFilterByShopId(Long shopId);

}