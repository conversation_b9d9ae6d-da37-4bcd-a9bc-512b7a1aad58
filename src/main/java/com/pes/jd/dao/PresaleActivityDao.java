package com.pes.jd.dao;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.PresaleActivityDTO;

import java.util.Date;
import java.util.List;

public interface PresaleActivityDao {

    int deleteByShopId(JobShopDTO shop, Date date);

    int deleteByActivityIdAndSkuId(JobShopDTO shop, Date date, List<PresaleActivityDTO> presaleActivityLst);

    int updateByActivityIdAndSkuId(JobShopDTO shop, Date date, List<PresaleActivityDTO> presaleActivityLst);

    int updateByActivityIdAndStatus(JobShopDTO shop, Integer status, Date date);

    int batchInsert(JobShopDTO shop, Date date, List<PresaleActivityDTO> presaleActivityLst);

    List<PresaleActivityDTO> selectByShopIdAndDateForPresalePerformance(JobShopDTO shop, Date date);

    PresaleActivityDTO selectActivityPeriodByShopIdAndDateForPresalePerformance(JobShopDTO shop, Date date);

    Integer selectCountByShopIdAndDate(JobShopDTO shopDTO, Date startDate, Date endDate);
}
