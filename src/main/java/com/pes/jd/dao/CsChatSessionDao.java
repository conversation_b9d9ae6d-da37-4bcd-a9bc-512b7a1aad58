package com.pes.jd.dao;

import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DTO.CrossChatSessionDTO;
import com.pes.jd.model.DTO.CsChatSessionDTO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/21 10:12 AM
 * @since 1.0.0
 */
public interface CsChatSessionDao {

    int updateSetForwordFlag(Set<String> sid, Integer flag, String schema, Date date);


    int updateByPrimaryKeySelective(String schemaId, List<CsChatSessionDTO> record);


    List<CsChatSessionDTO> searchAllByTime(Date beginDate, Date endDate, Long shopId, String schemaId, String nick);

    int insertBatchCsChatSession(JobShopDTO shop, Date date, List<CsChatSessionDO> pesCsChatSessionLst);

    int deleteChatSessionByShopIdAndDate(JobShopDTO shop, Date date);

    CsChatSessionDO getCsChatSessionBySidByDate(JobShopDTO shop, String sid, Date leaveMsgTime);


    List<Integer> searchForWordInOutAndDirect(
            String schema,
            Long shopId,
            String nick,
            Date date,
            Integer sessionType
    );

    List<String> selectReceiveCsChatSessionSidByDate(JobShopDTO shop, List<CsDTO> csLst, Date date);

    List<CsChatSessionDTO> selectReceiveCsChatSessionLstByDate(JobShopDTO shop, List<CsDTO> csLst, Date date, Date newDate, Date endDate);

    List<CrossChatSessionDTO> selectCrossChatCsSessionByShopIdForEndTimeEqual(JobShopDTO shop, Date date);

    List<CrossChatSessionDTO> selectCrossChatCsSessionByShopIdByDate(JobShopDTO shop, Date yesterday, Date targetDate);


    List<Long> selectChatSessionIdsByShopIdByDate(JobShopDTO shop, Date date);

    int deleteChatSessionByIds(JobShopDTO shop, Date date, List<Long> ids);

    List<CrossChatSessionDTO> selectChatCsSessionByShopIdByDate(JobShopDTO shop, Date date);

    List<String> searchForwordOutSidByTime(Date startTimeOfDate, Date endTimeOfDate, Long shopId, String schemaId, String csNick);

    List<String> searchSidByTimeForwordType(Date startTimeOfDate, Date endTimeOfDate, Long shopId, String schemaId, String csNick, Integer forwordType);

    List<CsChatSessionDO> selectByShopIdAndDateAndSkuIds(JobShopDTO shop, Date start, Date end, Set<Long> skuIds);

    List<CsChatSessionDTO> selectEvaluationBySids(JobShopDTO shop, List<String> sids, Date startDate, Date endDate);

    List<CsChatSessionDTO> selectShopCsChatSessionLstByBuyerNickLstForConsultHandle(JobShopDTO shop, String csNick, List<String> buyerNickList, Date startDate,
                                                                                    Date endDate);

    CsChatSessionDO findLatestSessions(JobShopDTO shop, String csNick, String buyerNick, Date startTimeOfDate, Date evalTime);
}
