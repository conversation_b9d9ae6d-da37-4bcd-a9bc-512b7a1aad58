package com.pes.jd.dao.main;

import com.pes.jd.model.DO.ShopAccount;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopAccountDao {

    List<ShopAccountDTO> selectShopAccountOfShopStatus(@Param("shopId") Long shopId, String nick, boolean exactSearch);

    int batchInsertSubUsersOfShop(List<ShopAccount> subUsers);

    void  deleteShopAccountByShopId(@Param("shopId") String shopId);

    int deleteShopAccountByNick(String nick);

    int updateSubUserRole(String nick, String type);
    
    List<ShopAccount>  selectShopAccountOfShop(String shopId);
    
    List<ShopAccount>  getShopAccountByNick(@Param("nick") String nick);
    
    List<ShopAccountDTO>  selectShopAccountByShopIdByNickByStatus(Long shopId
            , String nick, Integer status, Integer flag, Boolean managerOverlay);

	ShopAccount getShopMainAccount(@Param("shopId") String shopId);

    ShopAccount getShopMainAccountByShopId(String shopId);

    List<ShopAccountDTO>  selectShopAccountByShopIdByNickByStatusForCsManager(Long shopId
            , String nick, Integer status);

	 int updateShopMainAccountNick(Long shopId, String nick, String userName);

    List<ShopAccountDTO> selectShopAccountByShopId(Long shopId);
}