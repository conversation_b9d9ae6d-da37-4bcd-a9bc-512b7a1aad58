package com.pes.jd.dao.main;

import com.pes.jd.model.DO.AddressLiabryDO;
import com.pes.jd.model.DTO.AddressAreaCityDTO;
import com.pes.jd.model.DTO.AddressAreaCountryDTO;
import com.pes.jd.model.DTO.AddressAreaProviceDTO;
import com.pes.jd.model.DTO.FullAddressAreaDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2019-06-01 22:37
 */
public interface AddressLiabryDao {

    void insertAddressLiabry(FullAddressAreaDTO fullAddressAreaDTO);
    
    List<FullAddressAreaDTO> getfullAddressList(AddressLiabryDO addressLiabryDO);
    
    void batchInsertAddressLiabry(List<FullAddressAreaDTO> list);

	List<AddressAreaProviceDTO> getProviceList(AddressLiabryDO addressLiabryDO);

	List<AddressAreaCityDTO> getCityList(AddressLiabryDO addressLiabryDO);
	
	List<FullAddressAreaDTO> getAddressByParam(AddressLiabryDO addressLiabryDO);
	
	List<AddressAreaCountryDTO> getCountryList(AddressLiabryDO addressLiabryDO);
    
}
