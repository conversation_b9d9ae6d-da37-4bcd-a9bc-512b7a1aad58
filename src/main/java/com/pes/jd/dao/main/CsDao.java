package com.pes.jd.dao.main;

import com.pes.jd.model.DO.Cs;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.ms.domain.Data.master.CsSimple;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CsDao {
	public Cs getCsById(Long id);

	List<Cs> searchCsByNicks(List<String> nicks);
	
	public int deleteCsByNick(String nick);

	int batchInsertCs(List<Cs> csLst);
	
	public int insertCs(Cs cs);
	
	public int updateCsById(Cs cs);
	
	List<CsDTO> selectCsByShopId(Long shopId);

	int updateCsByCsNickByshopId(Long shopId, String nick, String simpleName, Integer type);

	List<CsDTO> selectCsByShopIdByNickByType(Long shopId, String nick, Integer type);

	List<Map<String,Object>> selectCsByShopIdAndCsid(Map<String, Object> param);

	List<Map<String,Object>> selectCsByShopIdMap(Map<String, Object> param);

	List<CsDTO> selectByNick(String nick);

	List<CsDTO> selectByGroup(String groupId);

	List<CsDTO> selectByShop(String shopId);

	List<Cs> selectCsByNickLst(List<String> nickLst);

	int updateCsOfStatusByNickLst(List<Cs> csLst);

	List<String> searchCsNickByShopId(Long shopId);

	List<CsDTO> searchCsByshopIdLstAndType(List<ShopCommonParam> shopLst, Integer type);

	List<CsDTO> selectCsByShopIdByTypeByCsStatus(Long shopId, Integer type, Integer csStatus);


	int selectCsCountByShopIdByType(Long shopId, Integer type);

	int updateCsNickByOldNick(Long shopId, String newCsNick, String oldCsNick);

	List<CsSimple> selectCsByShopIdSetByTypeByCsStatus(Set<Long> shopIdSet, Integer type, Integer csStatus);

	List<CsDTO> selectBoardNicksByShopId(String shopId);

	int selectSampleSimpleNickBySimpleName(Long shopId, String simpleName, String csNick);

	public List<CsDTO> selectByShopIds(Set<Long> useShopIdsList);

	List<CsDTO> selectCsByShopIdAndCsNick(Long shopId, String csNick);

	int selectCsCountByShopIdByCsStatus(Long shopId, Integer status);

	public List<Cs> selectShopCsLists(Long shopId, String csNicks);

    List<CsDTO> selectByShopAndGroupIdAndNick(String shopId, String groupId, String nick);

    List<CsDTO> selectCsLstByShopIdAndType(Long shopId, int csType);

	List<String> selectCsNickByGroupId(Long valueOf);

	List<String> selectCsNickByShopId(Long shopId);

	String getOneCsByShopId(Long shop);
}
  
