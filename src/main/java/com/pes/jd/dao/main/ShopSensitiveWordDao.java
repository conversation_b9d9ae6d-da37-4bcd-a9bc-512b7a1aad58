package com.pes.jd.dao.main;

import com.pes.jd.model.DTO.ShopSensitiveWordDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ShopSensitiveWordDao {

    List<String> selectSensitiveWordByShopId(Long shopId);

    List<ShopSensitiveWordDTO> batchLikeByShopIdForLst(Long shopId, String sensitiveWord);

    List<ShopSensitiveWordDTO> selectSensitiveWordByShopIdForLst(Long shopId);

    ShopSensitiveWordDTO selectSensitiveWordNewestByShopId(Long shopId);

    int selectExistenceSensitiveWordByShopId(Long shopId);

    int updateSensitiveWordByShopId(Long shopId, Long modifiedBy, Map<String, String> record, Date date);

    int updateSensitiveWordDate(Long shopId, String field, Date date);

    int insertSensitiveWord(Long shopId, Long modifiedBy, List<String> record);

    int deleteSensitiveWordById(Long shopId, List<String> record);
}
