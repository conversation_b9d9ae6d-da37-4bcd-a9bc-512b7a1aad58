package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.AppointmentAutoAllocatedSettingDao;
import com.pes.jd.mapper.main.AppointmentAutoAllocatedSettingMapper;
import com.pes.jd.model.DTO.AppointmentAutoAllocatedSettingDTO;
import com.yiyitech.support.redis.RedisCache;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

@Repository
public class AppointmentAutoAllocatedSettingDaoImpl implements AppointmentAutoAllocatedSettingDao {
    @Resource
    private RedisCache redisCache;
    private Integer redisDBNum = 2;
    @Resource
    private AppointmentAutoAllocatedSettingMapper  appointmentAutoAllocatedSettingMapper;
    @Override
    public AppointmentAutoAllocatedSettingDTO selectShopAutoAllocatedSettingByShopId(Long shopId) {
        return appointmentAutoAllocatedSettingMapper.selectShopAutoAllocatedSettingByShopId(shopId);
    }


    @Override
    public int insertShopAutoAllocatedSetting(AppointmentAutoAllocatedSettingDTO record) {
        int i = appointmentAutoAllocatedSettingMapper.insertShopAutoAllocatedSetting(record);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }


    @Override
    public int updateShopAutoAllocatedSettingById(AppointmentAutoAllocatedSettingDTO record) {
        int i = appointmentAutoAllocatedSettingMapper.updateShopAutoAllocatedSettingById(record);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public int updateCloseAppointmentAutoAllocatedByShopId(Long shopId, Boolean autoAllocated) {
        int i = appointmentAutoAllocatedSettingMapper.updateCloseAppointmentAutoAllocatedByShopId(shopId, autoAllocated);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }

}
