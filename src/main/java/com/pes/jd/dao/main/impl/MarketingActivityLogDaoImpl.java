package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.MarketingActivityLogDao;
import com.pes.jd.mapper.main.MarketingActivityLogMapper;
import com.pes.jd.model.DO.MarketingActivityLog;
import com.pes.jd.model.DTO.MarketingActivityLogSummaryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Repository
public class MarketingActivityLogDaoImpl implements MarketingActivityLogDao {
    @Autowired
    private MarketingActivityLogMapper marketingActivityLogMapper;
    @Override
    public List<MarketingActivityLog> queryByShopAndCsNick(Long shopId, String csNick, LocalDateTime popStartTime, Long activityId, Integer optType) {
        return marketingActivityLogMapper.queryByShopAndCsNickAndDate(shopId, csNick, popStartTime, activityId, optType);
    }

    @Override
    public int save(MarketingActivityLog marketingActivityLog) {
        return marketingActivityLogMapper.insert(marketingActivityLog);
    }

    @Override
    public List<MarketingActivityLogSummaryDTO> queryGroupByShop(LocalDate startDate, LocalDate endDate) {
        return marketingActivityLogMapper.queryGroupByShop(startDate, endDate);
    }

    @Override
    public List<MarketingActivityLog> queryListByOptTypeAndDate(Integer optType, LocalDate date) {
        return marketingActivityLogMapper.queryListByOptTypeAndDate(optType, date);
    }
}
  
