package com.pes.jd.dao.main;

import com.pes.jd.model.DO.PesUserOperationLog;

import java.util.Date;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/12 2:56 PM
 * @since 1.0.0
 */
public interface PesUserOperationLogDao {

    int insert(PesUserOperationLog record);

    List<PesUserOperationLog> searchOperationLogByTypeTimeNick(
            String shopId,
            Date startDate,
            Date endDate,
            String nick,
            String optType);

    int getUserOperationLogByNickAndShop(String shopId, String nick, String optType);

    int getUserOperationLogByNickAndShopAndTime(
            String shopId,
            Date startDate,
            Date endDate,
            String type,
            String nick
    );

}
