package com.pes.jd.dao.main.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopRemindWordGoodsDao;
import com.pes.jd.mapper.main.ShopRemindWordGoodsMapper;
import com.pes.jd.model.DO.ShopRemindWordGoodsDO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordGoodsDTO;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年11月05 11:12:12<br>
 */
@Repository
public class ShopRemindWordGoodsDaoImpl implements ShopRemindWordGoodsDao {
    @Resource
    private RedisCache redisCache;
    @Resource
    private ShopRemindWordGoodsMapper shopRemindWordGoodsMapper;
    private Integer redisDBNum = 2;

    @Override
    public int deleteShopRemindWordGoodsByWordId(Long wordId,Long type) {
        List<ShopRemindWordGoodsDTO> result = shopRemindWordGoodsMapper.selectShopRemindWordGoodsByWordIdAndType(wordId, type);
        int i = shopRemindWordGoodsMapper.deleteShopRemindWordGoodsByWordId(wordId, type);
        if (CollUtil.isNotEmpty(result)) {
            redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + result.get(0).getShopId(), 0, redisDBNum);
            redisCache.expire(CommonConstants.JOB_SHOP + result.get(0).getShopId(), 0, redisDBNum);
        }
        return i;
    }

    @Override
    public int batchInsertShopRemindWordGoods(List<ShopRemindWordGoodsDO> remindGoodsLst) {
        if (CollectionUtils.isEmpty(remindGoodsLst)) {
            return 0;
        }
        int i = shopRemindWordGoodsMapper.batchInsertShopRemindWordGoods(remindGoodsLst);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + remindGoodsLst.get(0).getShopId(), 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + remindGoodsLst.get(0).getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByWordId(Long wordId) {
        return shopRemindWordGoodsMapper.selectShopRemindWordGoodsByWordId(wordId);
    }

    @Override
    public List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByShopId(Long shopId) {
        return shopRemindWordGoodsMapper.selectShopRemindWordGoodsByShopId(shopId);
    }

    @Override
    public List<ShopRemindWordGoodsDTO> selectShopRemindWordGoodsByShopIdByRemindIds(Long shopId, Set<Long> remindIds,Long type) {
        return shopRemindWordGoodsMapper.selectShopRemindWordGoodsByShopIdByRemindIds(shopId,remindIds,type);
    }

    @Override
    public int deleteRemindIdByShopIdAndType(Long shopId, Set<Long> goodIds,Long remindId,Long type) {
        if (CollectionUtil.isEmpty(goodIds)) return 0;
        int i = shopRemindWordGoodsMapper.deleteRemindIdByShopIdAndType(shopId, goodIds, remindId, type);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + shopId, 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }

    @Override
    public List<ShopRemindWordGoodsDTO> selectRemindIdByShopIdAndType(Long shopId, Set<Long> goodIds, Long remindId, Long type) {
        if (CollectionUtil.isEmpty(goodIds)) return Lists.newArrayListWithCapacity(0);
        return shopRemindWordGoodsMapper.selectRemindIdByShopIdAndType(shopId,goodIds,remindId,type);
    }
}
