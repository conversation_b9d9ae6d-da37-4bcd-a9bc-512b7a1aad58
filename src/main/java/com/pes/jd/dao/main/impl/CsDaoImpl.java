package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.CsDao;
import com.pes.jd.mapper.main.CsMapper;
import com.pes.jd.model.DO.Cs;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.Param.ShopCommonParam;
import com.pes.jd.ms.domain.Data.master.CsSimple;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import org.springframework.beans.factory.annotation.Autowired;
import java.util.*;

/**
 * ClassName:CsDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月25日 上午9:24:18 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@Repository("csDao")
public class CsDaoImpl implements CsDao {
    @Autowired
    private CsMapper csMapper;
    @Autowired
    private RedisCache redisCache;

    private Integer redisDBNum = 2;
    @Override
    public Cs getCsById(Long id) {
        return csMapper.getCsById(id);
    }

    @Override
    public List<Cs> searchCsByNicks(List<String> nicks) {
        return csMapper.searchCsByNicks(nicks);
    }

    @Override
    public int deleteCsByNick(String nick) {
        return csMapper.deleteCsByNick(nick);
    }

    @Override
    public int insertCs(Cs cs) {
		int i = csMapper.insertCs(cs);
		redisCache.expire(CommonConstants.JOB_CSLST + cs.getShopId(), 0, redisDBNum);
		return i;
	}

    @Override
    public int updateCsById(Cs cs) {
		int i = csMapper.updateCsById(cs);
		redisCache.expire(CommonConstants.JOB_CSLST + cs.getShopId(), 0, redisDBNum);
		return i;
    }

    @Override
    public List<CsDTO> selectCsByShopId(Long shopId) {

        return csMapper.selectCsByShopId(shopId);
    }

    @Override
    public int batchInsertCs(List<Cs> csLst) {
		if (CollectionUtils.isEmpty(csLst)) {
			return 0;
		}
		int i = csMapper.batchInsertCs(csLst);
		redisCache.expire(CommonConstants.JOB_CSLST + csLst.get(0).getShopId(), 0, redisDBNum);
		return i;
	}

    @Override
    public int updateCsOfStatusByNickLst(List<Cs> csLst) {
        if (CollectionUtils.isEmpty(csLst)) {
            return 0;
        }
		int i = csMapper.updateCsOfStatusByNickLst(csLst);
		redisCache.expire(CommonConstants.JOB_CSLST + csLst.get(0).getShopId(), 0, redisDBNum);
		return i;
    }

    @Override
    public List<CsDTO> selectCsByShopIdByNickByType(Long shopId, String nick, Integer type) {

        return csMapper.selectCsByShopIdByNickByType(shopId, nick, type);
    }

    @Override
    public List<Map<String, Object>> selectCsByShopIdAndCsid(Map<String, Object> param) {
        return csMapper.selectCsByShopIdAndCsid(param);
    }

    @Override
    public List<Map<String, Object>> selectCsByShopIdMap(Map<String, Object> param) {
        return csMapper.selectCsByShopIdMap(param);
    }

    @Override
//	@Cacheable(cacheNames ="common",key = "'usrmgr-shop-'+#nick")
    public List<CsDTO> selectByNick(String nick) {
        return csMapper.selectByNick(nick);
    }

    @Override
//	@Cacheable(cacheNames ="common",key = "'usrmgr-shop-'+#groupId")
    public List<CsDTO> selectByGroup(String groupId) {
        return csMapper.selectByGroup(groupId);
    }

    @Override
//	@Cacheable(cacheNames ="common",key = "'usrmgr-shop-'+#shopId")
    public List<CsDTO> selectByShop(String shopId) {
        return csMapper.selectByShop(shopId);
    }

    @Override
    public int updateCsByCsNickByshopId(Long shopId, String nick, String simpleName, Integer type) {
		int i = csMapper.updateCsByCsNickByshopId(shopId, nick, simpleName, type);
		redisCache.expire(CommonConstants.JOB_CSLST + shopId, 0, redisDBNum);
		return i;
    }

    @Override
    public List<Cs> selectCsByNickLst(List<String> nickLst) {
        if (CollectionUtils.isEmpty(nickLst)) {
            return new ArrayList<>(0);
        }
        return csMapper.selectCsByNickLst(nickLst);
    }

    @Override
    public List<String> searchCsNickByShopId(Long shopId) {
        return csMapper.searchCsNickByShopId(shopId);
    }

    @Override
    public List<CsDTO> searchCsByshopIdLstAndType(List<ShopCommonParam> shopLst, Integer type) {
        if (CollectionUtils.isEmpty(shopLst)) {
            return new ArrayList<>(0);
        }
        return csMapper.searchCsByshopIdLstAndType(shopLst, type);
    }

    @Override
    public List<CsDTO> selectCsByShopIdByTypeByCsStatus(Long shopId, Integer type, Integer csStatus) {
        return csMapper.selectCsByShopIdByTypeByCsStatus(shopId, type, csStatus);
    }

    @Override
    public int updateCsNickByOldNick(Long shopId, String newCsNick, String oldCsNick) {

		int i = csMapper.updateCsNickByOldNick(shopId, newCsNick, oldCsNick);
		redisCache.expire(CommonConstants.JOB_CSLST + shopId, 0, redisDBNum);
		return i;
    }

    @Override
    public List<CsDTO> selectBoardNicksByShopId(String shopId) {

        return csMapper.selectBoardNicksByShopId(shopId);
    }


    @Override
    public int selectCsCountByShopIdByType(Long shopId, Integer type) {
        return csMapper.selectCsCountByShopIdByType(shopId, type);
    }

    @Override
    public List<CsSimple> selectCsByShopIdSetByTypeByCsStatus(Set<Long> shopIdSet, Integer type, Integer csStatus) {
        return csMapper.selectCsByShopIdSetByTypeByCsStatus(shopIdSet, type, csStatus);
    }

    @Override
    public int selectSampleSimpleNickBySimpleName(Long shopId, String simpleName, String csNick) {
        return csMapper.selectSampleSimpleNickBySimpleName(shopId, simpleName, csNick);
    }

    @Override
    public List<CsDTO> selectByShopIds(Set<Long> useShopIdsList) {
        return csMapper.selectByShopIds(useShopIdsList);
    }

    @Override
    public List<CsDTO> selectCsByShopIdAndCsNick(Long shopId, String csNick) {
        return csMapper.selectCsByShopIdAndCsNick(shopId, csNick);
    }

    @Override
    public int selectCsCountByShopIdByCsStatus(Long shopId, Integer status) {
        return csMapper.selectCsCountByShopIdByCsStatus(shopId, status);
    }

    @Override
    public List<Cs> selectShopCsLists(Long shopId, String csNicks) {
        List<String> list = null;
        if (StringUtils.isNotBlank(csNicks)) {
            list = Arrays.asList(csNicks.split(","));
        }
        return csMapper.selectShopCsLists(shopId, list);
    }

    @Override
    public List<CsDTO> selectByShopAndGroupIdAndNick(String shopId, String groupId, String nick) {
        return csMapper.selectByShopAndGroupIdAndNick(shopId, groupId, nick);
    }

    @Override
    public List<CsDTO> selectCsLstByShopIdAndType(Long shopId, int csType) {
        return csMapper.selectCsLstByShopIdAndType(shopId,csType);
    }

    @Override
    public List<String> selectCsNickByGroupId(Long groupId) {
        return csMapper.selectCsNickByGroupId(groupId);
    }

    @Override
    public List<String> selectCsNickByShopId(Long shopId) {
        return csMapper.selectCsNickByShopId(shopId);
    }

    @Override
    public String getOneCsByShopId(Long shop) {
        return csMapper.getOneCsByShopId(shop);
    }
}
  
