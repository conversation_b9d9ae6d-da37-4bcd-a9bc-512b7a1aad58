package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.ShopAssessSystemsettingDao;
import com.pes.jd.mapper.main.ShopAssessSystemsettingMapper;
import com.pes.jd.model.DO.ShopAssessSystemsettingDO;
import com.pes.jd.model.DTO.ShopAssessSystemsettingDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: aiJun
 * @Date: 2020/11/25 13:22
 * @Version 1.0
 */
@Component
public class ShopAssessSystemsettingDaoImpl implements ShopAssessSystemsettingDao {
    @Resource
    private ShopAssessSystemsettingMapper shopAssessSystemsettingMapper;
    @Override
    public int deleteByPrimaryKey(Long id) {
        return shopAssessSystemsettingMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ShopAssessSystemsettingDO record) {
        return shopAssessSystemsettingMapper.insert(record);
    }

    @Override
    public int insertSelective(ShopAssessSystemsettingDO record) {
        return shopAssessSystemsettingMapper.insertSelective(record);
    }

    @Override
    public ShopAssessSystemsettingDTO selectByPrimaryKey(Long id) {
        return shopAssessSystemsettingMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopAssessSystemsettingDO record) {
        return shopAssessSystemsettingMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ShopAssessSystemsettingDO record) {
        return shopAssessSystemsettingMapper.updateByPrimaryKey(record);
    }

    @Override
    public ShopAssessSystemsettingDTO selectByShopId(Long shopId) {
        return shopAssessSystemsettingMapper.selectByShopId(shopId);
    }
}
