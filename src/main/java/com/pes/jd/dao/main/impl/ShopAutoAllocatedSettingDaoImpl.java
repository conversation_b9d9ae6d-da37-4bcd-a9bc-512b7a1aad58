package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopAutoAllocatedSettingDao;
import com.pes.jd.mapper.main.ShopAutoAllocatedSettingMapper;
import com.pes.jd.model.DO.ShopAutoAllocatedSettingDO;
import com.pes.jd.ms.domain.Data.master.ShopAutoAllocatedSettingDTO;
import com.yiyitech.support.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年07月30 15:39:39<br>
 */
@Repository
public class ShopAutoAllocatedSettingDaoImpl implements ShopAutoAllocatedSettingDao {
    @Resource
    private RedisCache redisCache;

    private Integer redisDBNum = 2;
    @Autowired
    private ShopAutoAllocatedSettingMapper  shopAutoAllocatedSettingMapper;
    @Override
    public int insertShopAutoAllocatedSetting(ShopAutoAllocatedSettingDO record) {
        int i = shopAutoAllocatedSettingMapper.insertShopAutoAllocatedSetting(record);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public int updateShopAutoAllocatedSettingById(ShopAutoAllocatedSettingDO record) {
        int i = shopAutoAllocatedSettingMapper.updateShopAutoAllocatedSettingById(record);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public ShopAutoAllocatedSettingDTO selectShopAutoAllocatedSettingByShopId(Long shopId) {
        return shopAutoAllocatedSettingMapper.selectShopAutoAllocatedSettingByShopId(shopId);
    }

    @Override
    public int updateCloseAutoAllocatedByShopId(Long shopId, Boolean isAutoAllocated) {

        int i = shopAutoAllocatedSettingMapper.updateCloseAutoAllocatedByShopId(shopId, isAutoAllocated);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }
}
