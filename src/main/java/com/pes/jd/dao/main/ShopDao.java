package com.pes.jd.dao.main;

import com.pes.jd.model.BO.ShopDbAndSchemeIdBO;
import com.pes.jd.model.DO.Shop;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Param.JdSystemPageParam;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.model.VO.AdminShopVO;
import com.pes.jd.model.VO.ShopVO;
import com.pes.jd.ms.domain.Data.master.CauseShop;
import com.pes.jd.ms.domain.Data.master.ShopUrge;

import java.util.Date;
import java.util.List;

public interface ShopDao {

    int insertShop(Shop record);

    int updateShopById(Shop record);

    ShopDTO selectShopByShopId(Long shopId);
    ShopDTO getShopInfoForVenderId(Long venderId);
    ShopDTO selectShopByShopIdFromRedis(Long shopId);

    ShopDTO selectShopByUserNick(String userNick);

    List<AdminShopVO> selectShopByNickOrTitle(String nick, String type);

    List<ShopVO> selectAllNotWhiteShopForLst(String type);

    List<ShopVO> searchAllShop();

    int updateShopCreateTableFlagByShopId(Long shopId);

    ShopDTO getShopBySellerNickOrTitle(String inviteShopInfo);

    List<ShopDTO> selectShopAuthExpiredBySellerNickOrTitleAndTime(Date startDate, Date endDate, String inviteShopInfo, Integer shopType);

    int deleteShopByShopId(Long shopId);

	ShopDTO selectShopByShopNameorShopId(String shopName);

	List<ShopDTO> selectShopByShopNameorShopId(List<Long> shopIdLst, String shopName, Long shopId1);

    List<ShopVO> selectShopByDateByNickOrTitle(JdSystemPageParam jdSystemPageParam);

	List<CauseShop> selectShopInfoForShopUserAnalysis(UserAnalysisParam param, String type);


    List<ShopVO> selectNoDateShopByDateByNickOrTitle(JdSystemPageParam jdSystemPageParam);

    /**
     * 根据id集合查询店铺dto
     * @return
     */
    List<ShopDTO> selectShopByIds(List<Long> shopIdLst, String shopParam);

	List<BoardMnoitorParamDTO> getShopListByIds(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size);

	int selectShopNumsByDeptIdAndTitle(List<Long> shopIds, String queryParam);

    List<ShopDTO> searchAllActiveShopIdsByType(String shopName, String type, String shopStatus);

    List<String> getRtDbLstByIds(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size);

    List<BoardMnoitorParamDTO> getShopListByIdsAndRtdb(List<Long> shopIdLst, String queryParam, Integer startIndex, Integer size, String rtDb);

    List<ShopDbAndSchemeIdBO> selectShopDbAndSchemaIdByShopTitle(String shopTitle, Integer type);

    List<ShopDbAndSchemeIdBO> selectDbByShopIds(List<Long> shopIdLst);

    Integer selectCountByStatus();

    List<ShopDTO> queryShopInfoLst(Integer shopType);

    List<ShopUrge> selectUrgeShopByShopIdLstByType(List<Long> shopIdLst, Integer type);

    List<ShopDTO> queryShopAllActive();

    Integer selectSelfShopCountByStatus();


    void updateInitFlag(Long shopId);

    List<Shop> queryShopInfoList(Integer shopType);

    JobShopDTO getJobShopInfoById(Long shopId);

    List<JobShopDTO> getAllActiveJobShop(Integer shopType);

    PullSubscribeDTO getAllActiveJobShopByShopId(Long shopId);

    JobShopDTO getShopSplitByShopId(Long shopId);

    ShopSplitKeyDTO getShopSplitKeyInfo(Long shopId);

    int getTypeByShopId(String shopId);

    ShopDTO selectShopBySessionKey(String sessionKey);

    List<ShopDTO> listShopInfoByShopIds(List<Long> shopIdList);
}
