package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.SmsServiceDao;
import com.pes.jd.mapper.main.SmsServiceMapper;
import com.pes.jd.model.DO.SmsServiceDO;
import com.pes.jd.model.DTO.SmsServiceDTO;
import com.yiyitech.support.redis.RedisCache;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @Anthor: yuanxun
 * @Date: 18:08 2019/9/17
 * @Description:
 */
@Repository
public class SmsServiceDaoImpl implements SmsServiceDao {
	@Resource
	private RedisCache redisCache;

	private Integer redisDBNum = 2;
	@Resource
	private SmsServiceMapper smsServiceMapper;
	@Override
	public int insert(SmsServiceDO record) {
		int insert = smsServiceMapper.insert(record);
		if(record!=null){
			redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
		}
		return insert;
	}

	@Override
	public SmsServiceDTO getByShopId(Long shopId) {
		return smsServiceMapper.getByShopId(shopId);
	}

	@Override
	public SmsServiceDTO getSum() {
		return smsServiceMapper.getSum();
	}

	@Override
	public int updateByPrimaryKey(SmsServiceDO record) {
		int i = smsServiceMapper.updateByPrimaryKey(record);
		redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
		return i;
	}

	@Override
	public int updateSmsNumByShopId(Integer number, Long shopId) {
		int i = smsServiceMapper.updateSmsNumByShopId(number, shopId);
		redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
		return i;
	}

    @Override
    public int updateSmsUseNumber(Long shopId, Integer successCount){
		int i = smsServiceMapper.updateSmsUseNumber(shopId, successCount);
		redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
		return i;
	}

	@Override
	public int updateSmsUseNumberByFailCount(Long shopId, Integer failCount) {
		int i = smsServiceMapper.updateSmsUseNumberByFailCount(shopId, failCount);
		redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
		return i;
	}
}
  
