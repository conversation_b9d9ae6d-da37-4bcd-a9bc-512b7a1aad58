package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.CsManagerDao;
import com.pes.jd.mapper.main.CsManagerMapper;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.GroupCsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**  
 * ClassName:CsManagerDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月3日 上午10:47:07 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository
public class CsManagerDaoImpl implements CsManagerDao {
	@Autowired
	private CsManagerMapper csManagerMapper;
	@Override
	public	List<CsDTO>	selectGroupCsByShopIdByGroupIdByNick(String shopId, String groupId,String nick,String operateType,Integer type) {
		return csManagerMapper.selectGroupCsByShopIdByGroupIdByNick(shopId, groupId,nick,operateType,type);
	}

	@Override
	public List<GroupCsDTO> selectGroupDataList(Long shopId) {
		return csManagerMapper.selectGroupDataList(shopId);
	}
	
	@Override
	
	public	List<GroupCsDTO> selectShopGroupCsByShopIdByGroupIdByNick(String shopId,
			String groupId,String nick){
		return csManagerMapper.selectShopGroupCsByShopIdByGroupIdByNick(shopId, groupId, nick);
	}
}
  
