package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.SmsOrderDao;
import com.pes.jd.mapper.main.SmsOrderMapper;
import com.pes.jd.model.DO.SmsOrderDO;
import com.pes.jd.model.DTO.SmsOrderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @Anthor: yuanxun
 * @Date: 11:21 2019/9/17
 * @Description:
 */
@Repository
public class SmsOrderDaoImpl implements SmsOrderDao {

	@Autowired
	private SmsOrderMapper smsOrderMapper;

	@Override
	public List<SmsOrderDTO> queryRechargeRecord(Date startDate, Date endDate, String nick, String orderId, Integer payWay, Integer orderStatus) {
		return smsOrderMapper.queryRechargeRecord(startDate, endDate, nick, orderId, payWay, orderStatus);
	}

	@Override
	public int recharge(SmsOrderDO record) {
		return smsOrderMapper.recharge(record);
	}

	@Override
	public int updateDateAndStatusByOrderId(String orderId, Date date, int status) {
		return smsOrderMapper.updateDateAndStatusByOrderId(orderId, date, status);
	}

    @Override
    public List<SmsOrderDTO> selectSmsOrderByShopId(Long shopId) {
        return smsOrderMapper.selectSmsOrderByShopId(shopId);
    }

	@Override
	public SmsOrderDTO selectSmsOrderByOrderId(String orderId) {
		return smsOrderMapper.selectSmsOrderByOrderId(orderId);
	}

	@Override
	public Byte selectOrderStatusBYOrderId(String orderId) {
		return smsOrderMapper.selectOrderStatusBYOrderId(orderId);
	}

    @Override
    public int selectSmsSuccessOrderCountByShopId(Long shopId) {

		return smsOrderMapper.selectSmsSuccessOrderCountByShopId(shopId);
    }

}
  
