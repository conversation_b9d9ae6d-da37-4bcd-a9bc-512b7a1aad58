package com.pes.jd.dao.main;

import com.pes.jd.model.DO.SmsOrderDO;
import com.pes.jd.model.DTO.SmsOrderDTO;

import java.util.Date;
import java.util.List;

public interface SmsOrderDao {

    List<SmsOrderDTO> queryRechargeRecord(Date startDate, Date endDate, String nick, String orderId, Integer payWay, Integer orderStatus);

    int recharge(SmsOrderDO record);

    int updateDateAndStatusByOrderId(String orderId, Date date, int status);

    List<SmsOrderDTO> selectSmsOrderByShopId(Long shopId);
    SmsOrderDTO selectSmsOrderByOrderId(String orderid);

    Byte selectOrderStatusBYOrderId(String orderId);

    int selectSmsSuccessOrderCountByShopId(Long shopId);

}