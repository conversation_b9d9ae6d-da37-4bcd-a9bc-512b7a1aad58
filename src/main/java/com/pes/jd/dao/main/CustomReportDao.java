package com.pes.jd.dao.main;


import com.pes.jd.model.DO.CustomReportDO;
import com.pes.jd.model.DTO.CustomReportDTO;

import java.util.List;

public interface CustomReportDao {

    int deleteByPrimaryKey(Long id);

    /**
     *  检查该店铺是否含有预定义报表
     * @param shopId
     * @param name
     * @return
     */
    int checkPredefine(Long shopId, String name);

    Long insert(CustomReportDO record);

    int insertSelective(CustomReportDO record);

    CustomReportDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CustomReportDO record);

    int updateByPrimaryKey(CustomReportDO record);

    List<CustomReportDTO> searchByType(Integer type, Long shopId);

    int deleteByPrimaryKeyWithProperty(Long id);

    List<Long> searchReportIdByReportName(String reportName);

    List<CustomReportDTO> selectByShopAndName(Long shopId, String name);
}