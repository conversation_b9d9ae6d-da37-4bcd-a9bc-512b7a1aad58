package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopSensitiveWordDao;
import com.pes.jd.mapper.main.ShopSensitiveWordMapper;
import com.pes.jd.model.DTO.ShopSensitiveWordDTO;
import com.yiyitech.support.redis.RedisCache;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public class ShopSensitiveWordDaoImpl implements ShopSensitiveWordDao {
    @Resource
    private RedisCache redisCache;

    private Integer redisDBNum = 2;
    @Resource
    private ShopSensitiveWordMapper shopSensitiveWordMapper;
    @Override
    public List<String> selectSensitiveWordByShopId(Long shopId) {
        return shopSensitiveWordMapper.selectSensitiveWordByShopId(shopId);
    }

    @Override
    public List<ShopSensitiveWordDTO> batchLikeByShopIdForLst(Long shopId, String sensitiveWord) {
        return shopSensitiveWordMapper.batchLikeByShopIdForLst(shopId,sensitiveWord);
    }

    @Override
    public List<ShopSensitiveWordDTO> selectSensitiveWordByShopIdForLst(Long shopId) {
        return shopSensitiveWordMapper.selectSensitiveWordByShopIdForLst(shopId);
    }

    @Override
    public ShopSensitiveWordDTO selectSensitiveWordNewestByShopId(Long shopId) {
        return shopSensitiveWordMapper.selectSensitiveWordNewestByShopId(shopId);
    }

    @Override
    public int selectExistenceSensitiveWordByShopId(Long shopId) {
        return shopSensitiveWordMapper.selectExistenceSensitiveWordByShopId(shopId);
    }

    @Override
    public int updateSensitiveWordByShopId(Long shopId, Long modifiedBy, Map<String,String> record,Date date) {
        int i = shopSensitiveWordMapper.updateSensitiveWordByShopId(shopId, modifiedBy, record, date);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }

    @Override
    public int updateSensitiveWordDate(Long shopId,String field,Date date) {
        int i = shopSensitiveWordMapper.updateSensitiveWordDate(shopId, field, date);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }

    @Override
    public int insertSensitiveWord(Long shopId, Long modifiedBy, List<String> record) {
        int i = shopSensitiveWordMapper.insertSensitiveWord(shopId, modifiedBy, record, new Date());
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }

    @Override
    public int deleteSensitiveWordById(Long shopId, List<String> record) {
        int i = shopSensitiveWordMapper.deleteSensitiveWordById(shopId, record);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }


}
