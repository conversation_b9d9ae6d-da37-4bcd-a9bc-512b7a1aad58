package com.pes.jd.dao.main.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopSmsBacklistDao;
import com.pes.jd.mapper.main.ShopSmsBacklistMapper;
import com.pes.jd.model.DO.ShopSmsBacklistDO;
import com.pes.jd.ms.domain.Data.master.ShopSmsBacklist;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月16 14:35:35<br>
 */
@Repository
public class ShopSmsBacklistDaoImpl implements ShopSmsBacklistDao {
    @Resource
    private RedisCache redisCache;

    private Integer redisDBNum = 2;
    @Resource
    private ShopSmsBacklistMapper shopSmsBacklistMapper;
    @Override
    public int deleteShopSmsBacklistById(Long id) {
        List<ShopSmsBacklist> result = shopSmsBacklistMapper.selectShopSmsBacklistById(id);
        int i = shopSmsBacklistMapper.deleteShopSmsBacklistById(id);
        if(CollUtil.isNotEmpty(result)){
            redisCache.expire(CommonConstants.JOB_SHOP + result.get(0).getShopId(), 0, redisDBNum);
        }
        return i;
    }

    @Override
    public int batchInsertShopSmsBacklist(List<ShopSmsBacklistDO> smsBackLst) {
        if(CollectionUtils.isEmpty(smsBackLst)){
            return 0;
        }
        int i = shopSmsBacklistMapper.batchInsertShopSmsBacklist(smsBackLst);
        redisCache.expire(CommonConstants.JOB_SHOP + smsBackLst.get(0).getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public List<ShopSmsBacklist> selectShopSmsBacklistByShopIdByCsNickByBuyerNick(Long shopId, String buyerNick, Date startDate, Date endDate) {
        return shopSmsBacklistMapper.selectShopSmsBacklistByShopIdByCsNickByBuyerNick(shopId,buyerNick,startDate,endDate);
    }

    @Override
    public int updateShopSmsBacklist(ShopSmsBacklistDO record) {
        int i = shopSmsBacklistMapper.updateShopSmsBacklist(record);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public List<ShopSmsBacklist> selectShopSmsBacklistByShopIdBuyerNickSet(Long shopId, Set<String> buyerNickSet) {
       if(CollectionUtils.isEmpty(buyerNickSet)){
           return Lists.newArrayList();
       }
        return shopSmsBacklistMapper.selectShopSmsBacklistByShopIdBuyerNickSet(shopId,buyerNickSet);
    }

    @Override
    public List<ShopSmsBacklist> selectShopSmsBackLstByShopId(Long shopId) {
        return shopSmsBacklistMapper.selectShopSmsBackLstByShopId(shopId);
    }
}
