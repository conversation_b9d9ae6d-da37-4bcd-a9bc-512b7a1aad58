package com.pes.jd.dao.main;

import com.pes.jd.model.DTO.ShopSubScribeDTO;
import com.pes.jd.model.Param.SubscribeParam;
import com.pes.jd.model.Param.UserAnalysisParam;
import com.pes.jd.ms.domain.Data.master.ShopSubScribe;

import java.time.LocalDateTime;
import java.util.List;

public interface ShopSubscribeDao {
	List<ShopSubScribeDTO> selectShopSubscribeByShopIdLstByDate(SubscribeParam param);
	
	List<ShopSubScribeDTO> selectShopSubscribeByShopIdLstByDateForRt(SubscribeParam param);
	
	List<ShopSubScribe> selectShopSubScribeForUseAnalysis(UserAnalysisParam param);

	List<ShopSubScribe> selectShopValidSubScribeForUseAnalysis(UserAnalysisParam param);

	List<ShopSubScribe> selectShopSubScribeByShopId(Long shopId);

	List<ShopSubScribe> selectValidShopSubScribeByShopIdOrderByStart(Long shopId, List<String> versions);

	List<ShopSubScribe> selectShopSubscribeByDateScope(LocalDateTime start, LocalDateTime end, List<Long> ShopIds);

	List<ShopSubScribeDTO> getShopSubscribeInfoByShopId(Long shopId);
}
