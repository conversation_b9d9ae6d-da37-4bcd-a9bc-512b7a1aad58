package com.pes.jd.dao.main;

import com.pes.jd.model.DO.ShopSmsWordDO;
import com.pes.jd.model.DTO.ShopSmsWordDTO;
import com.pes.jd.ms.domain.Data.master.ShopSmsWord;

import java.util.HashMap;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月16 10:09:09<br>
 */
public interface ShopSmsWordDao {
    int deleteShopSmsWordById(Long id);

    int insertShopSmsWord(ShopSmsWordDO record);

    Long insertShopSmsWordAndRtId(ShopSmsWordDO record);

    ShopSmsWord selectShopSmsWordById(Long id);

    int updateShopSmsWord(ShopSmsWordDO record);

    List<ShopSmsWord> selectShopSmsWordByShopId(Long shopId, String type);

    List<ShopSmsWord> selectShopSmsWordByIdByType(Long id, String type);

    int updateShopWordUsingByIds(List<Long> wordIdLst, Boolean using);

    List<ShopSmsWord> selectShopSmsWordByShopIdByAuditstatus(Long shopId, Integer statusOne, Integer statusTwo);

    List<ShopSmsWord> selectShopSmsWordByShopIdByMap(HashMap<String, Object> dataMap);

    List<ShopSmsWordDTO> selectSmsWordByNick(String nick);

    int queryNotAiditSmsWordCount();

    int auditSmsWord(ShopSmsWordDO record);

      int selectSmsWordCountByShopIdByName(Long shopId, String name, Long id);

    List<ShopSmsWordDTO> selectWordByShopIds(List<String> shopIds);

    void updateToNoUsingByShopId(Long shopId);

    String queryShopSmsWordTemplateIdById(Integer id);

    ShopSmsWordDTO queryShopSmsWordById(Long id);

    List<ShopSmsWordDTO> searchByIds(List<Long> ids);
}
