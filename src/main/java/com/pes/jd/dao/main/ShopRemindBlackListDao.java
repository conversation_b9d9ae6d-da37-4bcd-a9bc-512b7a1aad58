package com.pes.jd.dao.main;

import com.pes.jd.model.DO.ShopRemindBlackListDO;
import com.pes.jd.model.DTO.ShopRemindBlackListDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ShopRemindBlackListDao {
    void batchInsert(List<ShopRemindBlackListDO> result);

    List<ShopRemindBlackListDTO> selectShopRemindBlackList(Long shopId, String buyerNick, Date startDate, Date endDate);

    void deleteShopRemindBlackListById(Long id);

    List<ShopRemindBlackListDTO> selectShopRemindBlacklistByShopIdAndBuyerNickSet(Long shopId, Set<String> buyerNickSet);

    List<ShopRemindBlackListDTO> selectShopRemindBlacklistByShopId(Long shopId);

    List<String> selectManualMerchandisingBlacklistList(Long shopId);
}
