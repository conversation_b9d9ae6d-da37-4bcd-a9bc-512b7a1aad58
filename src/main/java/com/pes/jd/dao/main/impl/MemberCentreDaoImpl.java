
package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.MemberCentreDao;
import com.pes.jd.mapper.main.MemberCentreMapper;
import com.pes.jd.model.DO.MemberCentreDO;
import com.pes.jd.model.DTO.MemberCentreDTO;
import com.pes.jd.model.DTO.NewMemberCentreDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @Author: yuanxun
 * @Date: 16:03 2019/11/6
 * @Description:
 */
@Repository
public class MemberCentreDaoImpl implements MemberCentreDao {
    @Autowired
    private MemberCentreMapper memberCentreMapper;

    @Override
    public int insert(MemberCentreDO record) {
        return memberCentreMapper.insert(record);
    }

    @Override
	public int update(String contact, String phoneNumber, Integer position, Long id, String qq, String qqEmail, Date ejectTime) {
		return memberCentreMapper.update(contact, phoneNumber, position, id, qq, qqEmail, ejectTime);
	}

	@Override
	public List<MemberCentreDTO> getMemberInfoByShopId(Long shopId, String nick) {
		return memberCentreMapper.getMemberInfoByShopId(shopId, nick);
	}

	@Override
	public List<MemberCentreDTO> selectMemberInfoBySubDateAndShopAndPosition(Date startDate, Date endDate,
																 String nick,  Integer position, String status,Integer shopType) {
		
		return memberCentreMapper.selectMemberInfoBySubDateAndShopAndPosition(startDate, endDate,
																nick, position, status,shopType);
	}

	@Override
	public void insertForInitialization(Long shop, String title, String csNick, String phone, String qq, String email, Date date) {
    	memberCentreMapper.insertForInitialization(shop, title, csNick, phone, qq, email, date);
	}

	@Override
	public List<NewMemberCentreDTO> listMemberInfosByShopIdCsNick(Long shopId, String nick) {
		return memberCentreMapper.listMemberInfosByShopIdCsNick(shopId, nick);
	}

	@Override
	public int updateEjectTimeByNick(Long shopId, String nick, Date date) {
		return memberCentreMapper.updateEjectTimeByNick(shopId, nick, date);
	}

	@Override
	public int insertMemberCentre(MemberCentreDO memberCentre) {
		return memberCentreMapper.insertMemberCentre(memberCentre);
	}

}
  
