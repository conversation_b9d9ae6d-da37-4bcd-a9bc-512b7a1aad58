package com.pes.jd.dao.main;

import com.pes.jd.model.DTO.ShopPopDTO;

import java.util.List;

public interface ShopPopDao {
	List<ShopPopDTO> selectAllPopShopByPopName(String popName, Long shopId, String shopName, Integer shopType);
	List<Long> selectShopIdByErpId(List<String> erpIds);

    List<Long> selectShopIdByErpIdAndRtDb(List<String> erpIds, String rtDb);

	List<ShopPopDTO> selectAllPopShopByShopType(String popName, Long shopId, String shopName, Integer shopType);
}
