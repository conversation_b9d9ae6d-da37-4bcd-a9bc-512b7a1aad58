package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.UserManageDao;
import com.pes.jd.mapper.main.UserManageMapper;
import com.pes.jd.model.DO.UserResource;
import com.pes.jd.model.DTO.UserRoleResourceDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class UserManageDaoImpl implements UserManageDao {

	@Autowired
	private UserManageMapper userManageMapper;

	@Override
	public int batchInsertUserResource(List<UserResource> userResoureLst) {
		if (CollectionUtils.isEmpty(userResoureLst)) {
			return 0;
		}
		return	userManageMapper.batchInsertUserResource(userResoureLst);
	}

	@Override
	public List<String>  getUserRoleByUserId(Long userId) {
		return userManageMapper.getUserRoleByUserId(userId);
	}
	
	@Override
	public	List<UserRoleResourceDTO> selectRoleResourcesByRoleName(String roleName ){
		return userManageMapper.selectRoleResourcesByRoleName(roleName);
	}
	
	@Override
	public List<UserRoleResourceDTO> selectUserResourceByNick(String nick){
		Map<String, Object> map= new HashMap<>();
		map.put("nick", nick);
		return userManageMapper.selectUserResourceByNick(map);
	}
}
  
