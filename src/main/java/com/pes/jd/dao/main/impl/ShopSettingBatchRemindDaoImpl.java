package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopSettingBatchRemindDao;
import com.pes.jd.mapper.main.ShopSettingBatchRemindMapper;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Repository
public class ShopSettingBatchRemindDaoImpl implements ShopSettingBatchRemindDao {
    @Resource
    private RedisCache redisCache;

    private Integer redisDBNum = 2;
    @Resource
    private ShopSettingBatchRemindMapper shopSettingBatchRemindMapper;
    @Override
    public int insert(ShopSettingBatchRemindDTO dto) {
        int insert = shopSettingBatchRemindMapper.insert(dto);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + dto.getShopId(), 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + dto.getShopId(), 0, redisDBNum);
        return insert;
    }

    @Override
    public int updateByShopId(ShopSettingBatchRemindDTO dto) {
        int i = shopSettingBatchRemindMapper.updateByShopId(dto);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + dto.getShopId(), 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + dto.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public int updateIsremindByShopId(Long shopId, boolean isRemind) {
        int i = shopSettingBatchRemindMapper.updateIsremindByShopId(shopId, isRemind);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + shopId, 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        return i;
    }

    @Override
    public int updateCloseRemindByShopIds(Collection<Long> shopIds){
        if (CollectionUtils.isEmpty(shopIds)) return 0;
        int i = shopSettingBatchRemindMapper.updateCloseRemindByShopIds(shopIds);
        shopIds.forEach(shopId->{
            redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + shopId, 0, redisDBNum);
            redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
        });
        return i;
    }


    @Override
    public ShopSettingBatchRemindDTO selectByshopId(Long shopId) {
        return shopSettingBatchRemindMapper.selectByshopId(shopId);
    }

    @Override
    public List<ShopSettingBatchRemindDTO> selectSettingByShopIds(List<String> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return new ArrayList<>();
        }
        return shopSettingBatchRemindMapper.selectSettingByShopIds(shopIds);

    }

    @Override
    public ShopSettingBatchRemindDTO selectByShopId(Long shopId) {
        return shopSettingBatchRemindMapper.selectByShopId(shopId);
    }

    @Override
    public ShopSettingBatchRemindDTO selectShopSettingBatchRemindByShopId(Long shopId) {
        return shopSettingBatchRemindMapper.selectShopSettingBatchRemindByShopId(shopId);
    }
}
