package com.pes.jd.dao.main;

import com.pes.jd.model.DO.ShopSmsBacklistDO;
import com.pes.jd.model.DTO.ShopSmsWordDTO;
import com.pes.jd.ms.domain.Data.master.ShopSmsBacklist;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月16 14:35:35<br>
 */
public interface ShopSmsBacklistDao {
    int deleteShopSmsBacklistById(Long id);

    int batchInsertShopSmsBacklist(List<ShopSmsBacklistDO> smsBackLst);

    List<ShopSmsBacklist> selectShopSmsBacklistByShopIdByCsNickByBuyerNick(
            Long shopId,
            String buyerNick,
            Date startDate, Date endDate);
    int updateShopSmsBacklist(ShopSmsBacklistDO record);

    List<ShopSmsBacklist> selectShopSmsBacklistByShopIdBuyerNickSet(Long shopId, Set<String> buyerNickSet);

    List<ShopSmsBacklist> selectShopSmsBackLstByShopId(Long shopId);

}
