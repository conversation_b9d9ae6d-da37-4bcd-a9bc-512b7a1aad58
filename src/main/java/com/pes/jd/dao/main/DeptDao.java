package com.pes.jd.dao.main;

import com.pes.jd.model.DO.*;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.VO.DeptShopVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/3 14:55
 */
public interface DeptDao {
    DeptUserDTO getDeptByUserName(String userName)throws Exception;

    /**
     * 通过一级部门名称获取一级部门信息
     * @param deptName
     * @return
     * @throws Exception
     */
    List<Dept01DTO> listDept01ByDeptName(String deptName)throws Exception;

    List<Dept02DTO> listDept02ByDeptName(String deptName)throws Exception;

    List<Dept03DTO> listDept03ByDeptName(String deptName)throws Exception;

    List<Dept04DTO> listDept04ByDeptName(String erpId)throws Exception;

    Integer insertDept01(Dept01DO dept01DO)throws Exception;

    Integer insertDept02(Dept02DO dept02DO)throws Exception;

    Integer insertDept03(Dept03DO dept03DO)throws Exception;

    Integer insertDept04(Dept04DO dept04DO)throws Exception;

    Integer insertDeptUser(DeptUserDO deptUserDO)throws Exception;

    List<Dept01DTO> listDept01ByParentId(Long parentId)throws Exception;

    List<Dept02DTO> listDept02ByParentId(Long parentId)throws Exception;

    List<Dept03DTO> listDept03ByParentId(Long parentId)throws Exception;

    List<Dept04DTO> listDept04ByParentId(Long parentId)throws Exception;

    List<Dept02DTO> listDept02ByDeptId(List<Long> parentIds)throws Exception;

    List<Dept03DTO> listDept03ByDeptId(List<Long> parentIds)throws Exception;

    List<Dept04DTO> listDept04ByDeptId(List<Long> parentIds)throws Exception;

    Dept01DTO getDept01ByDeptId(Long deptId)throws Exception;

    Dept02DTO getDept02ByDeptId(Long deptId)throws Exception;

    Dept03DTO getDept03ByDeptId(Long deptId)throws Exception;

    Dept04DTO getDept04ByDeptId(Long deptId)throws Exception;

    DeptUserDTO getDeptUserByDeptId(Long deptId)throws Exception;

    Integer insertDeptFiler(DeptFilerDO deptFilerDO)throws Exception;

    List<Dept00DTO> listDept00ByDeptName(String deptName)throws Exception;

    Integer insertDept00(Dept00DO dept00DO)throws Exception;

    Dept00DTO getDept00ByDeptId(Long deptId)throws Exception;

    Integer updateDeptFiler(DeptFilerDO deptFilerDO)throws Exception;
    DeptFilerDTO getDeptFilerByFilerName(String filerName)throws Exception;
    List<DeptFilerDTO> listDeptFiler()throws Exception;


    DeptDTO getPesDeptByUserName(String userName)throws Exception;


    List<DeptShopVO> selectShopByDeptIdAndType(Long deptId, String type, String shopParam)throws Exception;



    Integer deleteDeptUserByFilerId(Long filerId)throws Exception;
    Integer deleteDept00ByFilerId(Long filerId)throws Exception;
    Integer deleteDept01ByFilerId(Long filerId)throws Exception;
    Integer deleteDept02ByFilerId(Long filerId)throws Exception;
    Integer deleteDept03ByFilerId(Long filerId)throws Exception;
    Integer deleteDept04ByFilerId(Long filerId)throws Exception;
}
