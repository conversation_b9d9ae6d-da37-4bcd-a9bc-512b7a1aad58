package com.pes.jd.dao.main;

import com.pes.jd.model.DTO.AppointmentAutoAllocatedSettingDTO;

public interface AppointmentAutoAllocatedSettingDao {

    AppointmentAutoAllocatedSettingDTO selectShopAutoAllocatedSettingByShopId(Long shopId);

    int insertShopAutoAllocatedSetting(AppointmentAutoAllocatedSettingDTO record);

    int updateShopAutoAllocatedSettingById(AppointmentAutoAllocatedSettingDTO record);

    int updateCloseAppointmentAutoAllocatedByShopId(Long shopId, Boolean autoAllocated);
}