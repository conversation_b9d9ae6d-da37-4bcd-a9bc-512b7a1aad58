package com.pes.jd.dao.main;

import com.pes.jd.model.DTO.ShopGroupRequestDTO;

import java.util.List;

public interface ShopGroupRequestDao {
	
	List<ShopGroupRequestDTO> selectShopGroupRequestsByGroupId(Long mainShopId, String shopGroupId);

	ShopGroupRequestDTO checkRequestExistOrNot(Long mainShopId, Long toId);

	int InviteAgain(ShopGroupRequestDTO shopGroupRequest);

	int insertShopGroupRequest(ShopGroupRequestDTO shopGroupRequest);
	
	int updateShopGroupRequestByRequestId(ShopGroupRequestDTO shopGroupRequest);
	
	int updateShopGroupIdByFromShopIdByToShopId(Long mainShopId, List<String> shopIdList, String shopGroupId);

	List<ShopGroupRequestDTO> selectJoinGroupList(Long mainShopId, String status);

	List<ShopGroupRequestDTO> selectShopAccreditByMainShop(Long mainShopId);

	ShopGroupRequestDTO selectShopGroupRequestsByRequestId(String requestId);

}
