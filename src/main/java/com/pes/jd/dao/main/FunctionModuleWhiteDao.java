package com.pes.jd.dao.main;

import com.pes.jd.model.DO.FunctionModuleWhiteDO;
import com.pes.jd.model.DTO.FunctionModuleWhiteDTO;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @program: ms-pes-jd
 * @description: 白名单
 * @author: ALan
 * @create: 2019-05-15 15:42
 */
public interface FunctionModuleWhiteDao {

    List<FunctionModuleWhiteDTO> selectFunctionWhiteShopIdForLst(Long ShopId);

    List<FunctionModuleWhiteDTO> selectFunctionWhiteForLst(Integer type);

    List<FunctionModuleWhiteDTO> searchFunctionWhiteAddShopForLst(Integer type);

    Long searchFunctionWhiteDisplayByShopId(Long shopId);

    List<Long> searchFunctionWhiteIdByShopId(Long shopId);

    int insertFunctionModuleWhiteForLst(List<FunctionModuleWhiteDO> lsit);

    int batchDeleteFunctionModuleWhite(List<Long> fmwIds);

    List<FunctionModuleWhiteDTO> selectFunctionWhiteByIds(Collection<Long> ids);

    List<String>searchFunctionByIdForLst(List<Long> ids);

    List<Long>searchFunctionByShopIdAndFunctionId(Long shopId, List<Long> ids);

    int updateFunctionDateByShopId(Long shopId, Date date);

    List<Long> searchFunctionDisplayShopIds(List<Long> shopIds);


}
