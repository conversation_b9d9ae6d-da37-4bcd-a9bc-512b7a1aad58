package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.SaleIndexSettingDao;
import com.pes.jd.mapper.main.SaleIndexSettingMapper;
import com.pes.jd.model.DO.SaleIndexSettingDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;

@Repository
public class SaleIndexSettingDaoImpl implements SaleIndexSettingDao {

	@Autowired
	private SaleIndexSettingMapper saleIndexSettingMapper;
	
	@Override
	public int insertSaleIndexSetting(SaleIndexSettingDO saleIndexSettingDO) {
		
		return saleIndexSettingMapper.insertSaleIndexSetting(saleIndexSettingDO);
	}

	@Override
	public SaleIndexSettingDO selectSaleIndexSettingByShop(Long shopId,Date date) {
		return saleIndexSettingMapper.selectSaleIndexSettingByShop(shopId, date);
	}

}
