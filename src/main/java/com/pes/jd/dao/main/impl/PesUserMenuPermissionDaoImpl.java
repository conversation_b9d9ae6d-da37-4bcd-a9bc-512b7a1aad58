package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.PesUserMenuPermissionDao;
import com.pes.jd.mapper.main.PesUserMenuPermissionMapper;
import com.pes.jd.model.DO.PesMenuResource;
import com.pes.jd.model.DO.PesUserMenuPermission;
import com.pes.jd.model.DTO.ShopAccountDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * @<NAME_EMAIL>
 * @date 2018/12/17 2:04 PM
 * @since 1.0.0
 */
@Repository
public class PesUserMenuPermissionDaoImpl implements PesUserMenuPermissionDao {
    @Autowired
    private PesUserMenuPermissionMapper menuPermissionMapper;
    @Override
    public int insertByShopAccountsAndMenus(List<ShopAccountDTO> shopAccounts, List<PesMenuResource> menus) {
        if (CollectionUtils.isEmpty(shopAccounts) || CollectionUtils.isEmpty(menus)){
            return 0;
        }
               menuPermissionMapper.deleteByShopAccountsAndMenus(shopAccounts,menus);
        return menuPermissionMapper.insertByShopAccountsAndMenus(shopAccounts, menus);
    }

    @Override
    public List<PesUserMenuPermission> searchByNicks(Set<String> nicks) {
        return menuPermissionMapper.searchByNicks(nicks);
    }

    @Override
    public int deleteByShopAccounts(List<ShopAccountDTO> shopAccounts) {
        if (CollectionUtils.isEmpty(shopAccounts)){
            return 0;
        }
        return menuPermissionMapper.deleteByShopAccounts(shopAccounts);
    }

    @Override
    public PesUserMenuPermission selectByMenuId(Long menuId,String nick) {
        return menuPermissionMapper.selectByMenuId(menuId,nick);
    }

    @Override
    public int selectCountByNick(String nick) {
        return menuPermissionMapper.selectCountByNick(nick);
    }

    @Override
    public List<PesUserMenuPermission> selectByShopAccounts(List<ShopAccountDTO> shopAccounts) {
        return menuPermissionMapper.selectByShopAccounts(shopAccounts);
    }

    @Override
    public int insert(PesUserMenuPermission pesUserMenuPermission) {
               menuPermissionMapper.delete(pesUserMenuPermission);
        return menuPermissionMapper.insert(pesUserMenuPermission);
    }

    @Override
    public int deleteByMenuResourceId(Long id) {
        return menuPermissionMapper.deleteByMenuResourceId(id);
    }

    @Override
    public void deleteByShopAccountsAndMenu(List<ShopAccountDTO> shopAccounts, List<PesMenuResource> menus) {
         menuPermissionMapper.deleteByShopAccountsAndMenu(shopAccounts,menus);
    }
    
    @Override
    public  int deleteMenuServicePermissionByCsNick(String csNick){
    	
    	return menuPermissionMapper.deleteMenuServicePermissionByCsNick(csNick);
    }

    @Override
    public void truncatePermission(List<ShopAccountDTO> shopAccounts) {

    }
}
