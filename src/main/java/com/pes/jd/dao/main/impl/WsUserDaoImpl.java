package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.WsUserDao;
import com.pes.jd.mapper.main.WsUserMapper;
import com.pes.jd.model.DO.WsUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**  
 * ClassName:WsUserDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月24日 下午7:17:16 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository
public class WsUserDaoImpl implements WsUserDao {
	
	@Autowired
	private WsUserMapper wsUserMapper;

	@Override
	public int insertWsUser(WsUser record) {

		return wsUserMapper.insertWsUser(record);
	}

	@Override
	public int deleteWsUserById(Long id) {

		return wsUserMapper.deleteWsUserById(id);
	}

	@Override
	public int updateWsUserPasswordById(WsUser record) {

		return wsUserMapper.updateWsUserPasswordById(record);
	}

	@Override
	public WsUser selectWsUserById(Long id) {

		return wsUserMapper.selectWsUserById(id);
	}

	@Override
	public WsUser getWsUserByUserName(String userName) {
		return wsUserMapper.getWsUserByUserName(userName);
	}

}
  
