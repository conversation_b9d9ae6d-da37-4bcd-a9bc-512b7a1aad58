package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.OptRecordDao;
import com.pes.jd.mapper.main.OptRecordMapper;
import com.pes.jd.model.DO.OptRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * ClassName:OptRecordDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:52:50 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository("optRecordDao")
public class OptRecordDaoImpl implements OptRecordDao {

	@Autowired
	private OptRecordMapper optRecordMapper;

	@Override
	public int deleteOptRecordById(Long id) {
		return optRecordMapper.deleteOptRecordById(id);
	}

	@Override
	public int insertOptRecord(OptRecord record) {
		return optRecordMapper.insertOptRecord(record);
	}

	@Override
	public OptRecord getOptRecordById(Long id) {
		return optRecordMapper.getOptRecordById(id);
	}

	@Override
	public int updateOptRecordBySelective(OptRecord record) {
		return optRecordMapper.updateOptRecordBySelective(record);
	}

	@Override
	public int batchInsertOptRecord(List<OptRecord> optLst) {
		  
		return optRecordMapper.batchInsertOptRecord(optLst);
	}

}
