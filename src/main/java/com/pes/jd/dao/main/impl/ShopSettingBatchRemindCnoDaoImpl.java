package com.pes.jd.dao.main.impl;

import com.pes.jd.constants.CommonConstants;
import com.pes.jd.dao.main.ShopSettingBatchRemindCnoDao;
import com.pes.jd.mapper.main.ShopSettingBatchRemindCnoMapper;
import com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO;
import com.pes.jd.ms.domain.Data.master.ShopSettingBatchRemindCno;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月18 10:32:32<br>
 */
@Repository
public class ShopSettingBatchRemindCnoDaoImpl implements ShopSettingBatchRemindCnoDao {

    @Autowired
    private RedisCache redisCache;

    private Integer redisDBNum = 2;

    @Autowired
    private ShopSettingBatchRemindCnoMapper shopSettingBatchRemindCnoMapper;
    @Override
    public int insertShopSettingBatchRemindCno(ShopSettingBatchRemindCnoDO record) {
        int i = shopSettingBatchRemindCnoMapper.insertShopSettingBatchRemindCno(record);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + record.getShopId(), 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public int updateShopSettingBatchRemindCno(ShopSettingBatchRemindCnoDO record) {
        int i = shopSettingBatchRemindCnoMapper.updateShopSettingBatchRemindCno(record);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + record.getShopId(), 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + record.getShopId(), 0, redisDBNum);
        return i;
    }

    @Override
    public ShopSettingBatchRemindCno selectShopSettingBatchRemindCnoByshopId(Long shopId) {
        return shopSettingBatchRemindCnoMapper.selectShopSettingBatchRemindCnoByshopId(shopId);
    }

    @Override
    public List<ShopSettingBatchRemindCnoDO> selectSettingByShopIds(List<String> shopIds) {
        if(CollectionUtils.isEmpty(shopIds)){
            return new ArrayList<>();
        }
        return shopSettingBatchRemindCnoMapper.selectSettingByShopIds(shopIds);
    }

    @Override
    public ShopSettingBatchRemindCno selectShopSettingBatchRemindCnoByShopId(Long shopId) {
        return shopSettingBatchRemindCnoMapper.selectShopSettingBatchRemindCnoByShopId(shopId);
    }

    @Override
    public ShopSettingBatchRemindCno selecttShopSettingBatchRemindCnoByShopId(Long shopId) {
        return shopSettingBatchRemindCnoMapper.selecttShopSettingBatchRemindCnoByShopId(shopId);
    }

    @Override
    public void updateShopSettingBatchRemindCnoByShopIdAndIsRemind(Long shopId, boolean isRemind) {
        shopSettingBatchRemindCnoMapper.updateShopSettingBatchRemindCnoByShopIdAndIsRemind(shopId, isRemind);
        redisCache.expire(CommonConstants.JOB_BATCHREMINDSETTING + shopId, 0, redisDBNum);
        redisCache.expire(CommonConstants.JOB_SHOP + shopId, 0, redisDBNum);
    }
}
