package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.RoleDao;
import com.pes.jd.mapper.main.RoleMapper;
import com.pes.jd.model.DO.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * ClassName:RoleDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午6:23:56 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository("roleDao")
public class RoleDaoImpl implements RoleDao {

	@Autowired
	private RoleMapper roleMapper;

	@Override
	public int deleteRoleById(Long id) {
		return roleMapper.deleteRoleById(id);
	}

	@Override
	public int insertRole(Role record) {
		return roleMapper.insertRole(record);
	}

	@Override
	public Role getRoleById(Long id) {
		return roleMapper.getRoleById(id);
	}

	@Override
	public int updateRoleBySelective(Role record) {
		return roleMapper.updateRoleBySelective(record);
	}

}
