package com.pes.jd.dao.main.impl;


import com.pes.jd.dao.main.ShopAutoAppointmentAllocatedSettingDao;
import com.pes.jd.mapper.main.ShopAutoAppointmentAllocatedSettingMapper;
import com.pes.jd.model.DTO.ShopAutoAppointmentAllocatedSettingDTO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年03月24 15:57:57<br>
 */@Repository
public class ShopAutoAppointmentAllocatedSettingDaoImpl implements ShopAutoAppointmentAllocatedSettingDao {

     @Resource
     private ShopAutoAppointmentAllocatedSettingMapper shopAutoAppointmentAllocatedSettingMapper;
     @Override
    public ShopAutoAppointmentAllocatedSettingDTO selectShopAutoAppointmentAllocatedSettingByShopId(Long shopId) {
        return shopAutoAppointmentAllocatedSettingMapper.selectShopAutoAppointmentAllocatedSettingByShopId(shopId);
    }
}
