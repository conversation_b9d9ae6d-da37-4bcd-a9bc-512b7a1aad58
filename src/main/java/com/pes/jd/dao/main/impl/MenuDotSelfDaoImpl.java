package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.MenuDotSelfDao;
import com.pes.jd.mapper.main.MenuDotSelfMapper;
import com.pes.jd.model.DTO.MenuDotDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class MenuDotSelfDaoImpl implements MenuDotSelfDao {

    @Autowired
    private MenuDotSelfMapper menuDotSelfMapper;
    @Override
    public Integer inserMenuDot(MenuDotDTO menuDotDTO) throws Exception {
        return menuDotSelfMapper.inserMenuDot(menuDotDTO);
    }

    @Override
    public List<MenuDotDTO> listMenuDotByDate(Date sDate, Date eDate, String pageName) throws Exception {
        return menuDotSelfMapper.listMenuDotByDate(sDate,eDate,pageName);
    }

    @Override
    public List<MenuDotDTO> listMenuDotByShopNameAndNick(String shopName, String nick, String pageVisitName,Date startDate,Date endDate) throws Exception {
        return menuDotSelfMapper.listMenuDotByShopNameAndNick(shopName,nick,pageVisitName,startDate,endDate);
    }

    @Override
    public List<MenuDotDTO> listMenuDotByShopId(Long shopId) throws Exception {
        return menuDotSelfMapper.listMenuDotByShopId(shopId);
    }

    @Override
    public List<MenuDotDTO> listAllMenuDot() throws Exception {
        return menuDotSelfMapper.listAllMenuDot();
    }

    @Override
    public Integer deleteMenuDotByDate(Date deleteDate) throws Exception {
        return menuDotSelfMapper.deleteMenuDotByDate(deleteDate);
    }
}
