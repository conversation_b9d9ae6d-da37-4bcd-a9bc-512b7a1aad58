  
package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.UserReportPropertyDao;
import com.pes.jd.mapper.main.UserReportPropertyMapper;
import com.pes.jd.model.DO.UserReportProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**  
 * ClassName:UserReportPropertyDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月24日 下午7:25:34 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class UserReportPropertyDaoImpl implements UserReportPropertyDao {

	@Autowired
	private UserReportPropertyMapper reportPropertyMapper;

	@Override
	public int insertUserReportProperty(UserReportProperty record) {
	   return	reportPropertyMapper.insertUserReportProperty(record);
	}
	@Override
	public UserReportProperty getUserReportPropertyByUser(String user,Integer type) {
		return reportPropertyMapper.getUserReportPropertyByUser(user,type);
	}

	@Override
	public int updateUserReportPropertyByUserIdAndType(UserReportProperty record) {
		return reportPropertyMapper.updateUserReportPropertyByUserIdAndType(record);
	}

}
  
