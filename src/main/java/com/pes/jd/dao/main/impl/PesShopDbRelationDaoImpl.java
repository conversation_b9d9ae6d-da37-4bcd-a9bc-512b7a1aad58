package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.PesShopDbRelationDao;
import com.pes.jd.mapper.main.PesShopDbRelationMapper;
import com.pes.jd.model.DTO.SchemaInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * ClassName:SchemaDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午6:39:03 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@Repository("pesShopDbRelationDao")
public class PesShopDbRelationDaoImpl implements PesShopDbRelationDao {

    @Autowired
    private PesShopDbRelationMapper schemaMapper;

    @Override
    public SchemaInfoDTO getSchemaInfoAndDbName(Integer shopType) {
        return schemaMapper.getSchemaInfoAndDbName(shopType);
    }

    @Override
    public SchemaInfoDTO getSchemaInfoAndDbNameForRt(Integer shopType) {
        return schemaMapper.getSchemaInfoAndDbNameForRt(shopType);
    }
}
