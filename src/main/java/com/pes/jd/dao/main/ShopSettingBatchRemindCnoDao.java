package com.pes.jd.dao.main;

import com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO;
import com.pes.jd.ms.domain.Data.master.ShopSettingBatchRemindCno;

import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月18 10:31:31<br>
 */
public interface ShopSettingBatchRemindCnoDao {
    int insertShopSettingBatchRemindCno(ShopSettingBatchRemindCnoDO record);

    int updateShopSettingBatchRemindCno(ShopSettingBatchRemindCnoDO record);

    ShopSettingBatchRemindCno selectShopSettingBatchRemindCnoByshopId(Long shopId);

    List<ShopSettingBatchRemindCnoDO> selectSettingByShopIds(List<String> shopIds);

    ShopSettingBatchRemindCno selectShopSettingBatchRemindCnoByShopId(Long shopId);

    ShopSettingBatchRemindCno selecttShopSettingBatchRemindCnoByShopId(Long shopId);

    void updateShopSettingBatchRemindCnoByShopIdAndIsRemind(Long shopId, boolean isRemind);
}
