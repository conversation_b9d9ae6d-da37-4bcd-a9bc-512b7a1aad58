package com.pes.jd.dao.main;


import com.pes.jd.model.DO.MarketingActivityDO;
import com.pes.jd.model.DTO.MarketingActivityDTO;

import java.util.Date;
import java.util.List;

public interface MarketingActivityDao {

    int insert(MarketingActivityDO record);

    int deleteByPrimaryKey(Long id);

    int updateByPrimaryKey(MarketingActivityDO record);

    MarketingActivityDTO getByPrimaryKey(Long id);

    List<MarketingActivityDTO> selectEnableActivity(Integer shopType);

    List<MarketingActivityDTO> selectActivityByActivityNameAndDate(Date startDate, Date endDate, String version, Integer shopType);
}