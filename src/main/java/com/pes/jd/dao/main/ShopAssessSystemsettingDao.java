package com.pes.jd.dao.main;

import com.pes.jd.model.DO.ShopAssessSystemsettingDO;
import com.pes.jd.model.DTO.ShopAssessSystemsettingDTO;

/**
 * @Author: aiJun
 * @Date: 2020/11/25 13:21
 * @Version 1.0
 */
public interface ShopAssessSystemsettingDao {
    int deleteByPrimaryKey(Long id);

    int insert(ShopAssessSystemsettingDO record);

    int insertSelective(ShopAssessSystemsettingDO record);

    ShopAssessSystemsettingDTO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ShopAssessSystemsettingDO record);

    int updateByPrimaryKey(ShopAssessSystemsettingDO record);

    ShopAssessSystemsettingDTO selectByShopId(Long shopId);

}
