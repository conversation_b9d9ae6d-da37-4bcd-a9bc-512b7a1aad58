  
package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.ShopJobRecordDao;
import com.pes.jd.mapper.main.ShopJobRecordMapper;
import com.pes.jd.model.DO.ShopJobRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**  
 * ClassName:ShopJobRecordDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月24日 下午7:30:14 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class ShopJobRecordDaoImpl implements ShopJobRecordDao {

	@Autowired
	private  ShopJobRecordMapper shopJobRecordMapper;

	@Override
	public int insertShopJobRecord(ShopJobRecord record) {
		return shopJobRecordMapper.insertShopJobRecord(record);
	}

	@Override
	public int updateShopJobRecordById(ShopJobRecord record) {
		return shopJobRecordMapper.updateShopJobRecordById(record);
	}

	@Override
	public ShopJobRecord getShopJobRecordById(Long id) {
		return shopJobRecordMapper.getShopJobRecordById(id);
	}

	@Override
	public ShopJobRecord getByDateAndShop(ShopJobRecord record) {
		return shopJobRecordMapper.getByDateAndShop(record);
	}

}
  
