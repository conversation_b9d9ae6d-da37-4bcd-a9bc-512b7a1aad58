package com.pes.jd.dao.main;

import com.pes.jd.model.DO.GuidanceRecordDO;

import java.util.Date;

public interface ShopInitializationDao {
    void insertEntryGuidance(Long shop, Date date, Date created, Date modified, boolean flag);

    GuidanceRecordDO selectEntryGuidanceByShopId(Long shop);

    void updateEntryGuidance(Long shop, Date created);

    GuidanceRecordDO getGuidanceRecordByShopId(Long shop);
}
