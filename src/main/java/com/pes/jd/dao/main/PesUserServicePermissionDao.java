package com.pes.jd.dao.main;

import com.pes.jd.model.DO.PesServicePermission;
import com.pes.jd.model.DO.PesUserServicePermission;
import com.pes.jd.model.DTO.ShopAccountDTO;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/17 11:22 AM
 * @since 1.0.0
 */
public interface PesUserServicePermissionDao {

    int insertByShopAccountsAndPermissions(List<ShopAccountDTO> shopAccounts, List<PesServicePermission> permissions);
    int batchInsertByShopAccountsAndPermissions(List<ShopAccountDTO> shopAccounts, List<PesServicePermission> permissions);

    int deleteByShopAccounts(List<ShopAccountDTO> shopAccounts);

    List<PesUserServicePermission> selectByShopAccounts(List<ShopAccountDTO> shopAccounts);

	int deleteUserServicePermissionByCsNick(String csNick);
}
