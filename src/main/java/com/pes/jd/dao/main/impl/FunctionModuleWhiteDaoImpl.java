package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.FunctionModuleWhiteDao;
import com.pes.jd.mapper.main.FunctionModuleWhiteMapper;
import com.pes.jd.model.DO.FunctionModuleWhiteDO;
import com.pes.jd.model.DTO.FunctionModuleWhiteDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * @program: ms-pes-jd
 * @description: 白名单功能接口实现类
 * @author: ALan
 * @create: 2019-05-15 15:45
 */
@Repository
public class FunctionModuleWhiteDaoImpl implements FunctionModuleWhiteDao {

    @Autowired
    private FunctionModuleWhiteMapper functionModuleWhiteMapper;

    @Override
    public List<FunctionModuleWhiteDTO> selectFunctionWhiteShopIdForLst(Long ShopId) {

        return functionModuleWhiteMapper.selectFunctionWhiteShopIdForLst(ShopId);
    }

    @Override
    public List<FunctionModuleWhiteDTO> selectFunctionWhiteForLst(Integer type) {
        return functionModuleWhiteMapper.selectFunctionWhiteForLst(type);
    }

    @Override
    public List<FunctionModuleWhiteDTO> searchFunctionWhiteAddShopForLst(Integer type) {
        return functionModuleWhiteMapper.searchFunctionWhiteAddShopForLst(type);
    }

    @Override
    public Long searchFunctionWhiteDisplayByShopId(Long shopId) {

        return functionModuleWhiteMapper.searchFunctionWhiteDisplayByShopId(shopId);
    }

    @Override
    public List<Long> searchFunctionWhiteIdByShopId(Long shopId) {
        return functionModuleWhiteMapper.searchFunctionWhiteIdByShopId(shopId);
    }

    @Override
    public int insertFunctionModuleWhiteForLst(List<FunctionModuleWhiteDO> list) {
        return functionModuleWhiteMapper.insertFunctionModuleWhiteForLst(list);
    }

    @Override
    public int batchDeleteFunctionModuleWhite(List<Long> fmwIds) {
        return functionModuleWhiteMapper.batchDeleteFunctionModuleWhite(fmwIds);
    }

    @Override
    public List<FunctionModuleWhiteDTO> selectFunctionWhiteByIds(Collection<Long> ids) {
        return functionModuleWhiteMapper.selectFunctionWhiteByIds(ids);
    }

    @Override
    public List<String> searchFunctionByIdForLst(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) return new ArrayList<>(0);
        return functionModuleWhiteMapper.searchFunctionByIdForLst(ids);
    }

    @Override
    public List<Long> searchFunctionByShopIdAndFunctionId(Long shopId, List<Long> ids) {
        return functionModuleWhiteMapper.searchFunctionByShopIdAndFunctionId(shopId, ids);
    }

    @Override
    public int updateFunctionDateByShopId(Long shopId, Date date) {
        return functionModuleWhiteMapper.updateFunctionDateByShopId(shopId, date);
    }

    @Override
    public List<Long> searchFunctionDisplayShopIds(List<Long> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) return new ArrayList<>(0);
        return functionModuleWhiteMapper.searchFunctionDisplayShopIds(shopIds);
    }


}
