package com.pes.jd.dao.main;


import com.pes.jd.model.DO.MarketingActivityV2;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public interface MarketingActivityV2Dao {

    int insert(MarketingActivityV2 record);

    int deleteByPrimaryKey(Long id);

    int updateByPrimaryKey(MarketingActivityV2 record);

    MarketingActivityV2 getByPrimaryKey(Long id);

    List<MarketingActivityV2> selectEnableActivity(Integer shopType, LocalDateTime currentTime);

    List<MarketingActivityV2> selectActivityByActivityNameAndDate(Date startDate, Date endDate, String version, Integer shopType);
}