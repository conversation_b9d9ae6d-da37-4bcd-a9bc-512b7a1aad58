package com.pes.jd.dao.main.impl;

import com.pes.jd.dao.main.ShopSettingBatchRemindHistoryDao;
import com.pes.jd.model.DO.ShopSettingBatchRemindHistoryDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class ShopSettingBatchRemindHistoryDaoImpl implements ShopSettingBatchRemindHistoryDao {

    @Autowired
    private com.pes.jd.mapper.main.ShopSettingBatchRemindHistoryMapper ShopSettingBatchRemindHistoryMapper;
    @Override
    public int insert(ShopSettingBatchRemindHistoryDO record) {
        return ShopSettingBatchRemindHistoryMapper.insert(record);
    }

}