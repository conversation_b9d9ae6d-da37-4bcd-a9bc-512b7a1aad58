package com.pes.jd.dao;

import com.pes.jd.model.DO.Shop;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.List;

public interface ShopDao {

    int insertShop(Shop record);
    
    int deleteShopById(Long shopId);

    int updateShopById(Shop record);
    
    Shop selectShopByShopId(Long shopId);

	JobShopDTO getJobShopInfoById(Long shopId);
	
	JobShopDTO getJobShopInfoByVenderId(Long venderId);

	List<JobShopDTO> getActiveJobShopInfoByDelayTime(Integer hours);

	List<JobShopDTO> getAllActiveJobShopInfo();

}