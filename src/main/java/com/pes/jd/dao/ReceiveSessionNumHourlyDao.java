package com.pes.jd.dao;

import com.pes.jd.model.DO.ReceiveSessionNumHourlyDO;

import java.util.Date;

public interface ReceiveSessionNumHourlyDao {
    int deleteByTimePoint(
            Date date,
            String schemaId,
            Long shopId,
            String nick
    );

    int deleteByPrimaryKey(Long id);

    int insert(ReceiveSessionNumHourlyDO record,String schema);

    int insertSelective(ReceiveSessionNumHourlyDO record);

    ReceiveSessionNumHourlyDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ReceiveSessionNumHourlyDO record);

    int updateByPrimaryKey(ReceiveSessionNumHourlyDO record);
}
