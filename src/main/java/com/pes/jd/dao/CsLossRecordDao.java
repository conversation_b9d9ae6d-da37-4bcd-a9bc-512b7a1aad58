package com.pes.jd.dao;

import com.pes.jd.model.DTO.CsLossRecordDTO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;

/**  
 * ClassName:CsLossRecordDao <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface CsLossRecordDao {

	int insertBatchShopCsLossRecord(JobShopDTO shop, List<CsLossRecordDTO> csLossList, Date date);

	int deleteShopCsLossRecordByDate(JobShopDTO shop, Date startDate, Date endDate, int lossType);
	
}
  
