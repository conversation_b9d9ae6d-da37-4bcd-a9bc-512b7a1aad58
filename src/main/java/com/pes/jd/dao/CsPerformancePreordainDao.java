package com.pes.jd.dao;

import com.pes.jd.model.DO.CsPerformancePreordainDO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface CsPerformancePreordainDao {


    int batchInsertCsPerformancePreordain(JobShopDTO shop, Set<CsPerformancePreordainDO> performances);

    int deleteByShopIdAndDateAndActivityId(JobShopDTO shop, Date startDate, Date endDate, String activityId);

}
