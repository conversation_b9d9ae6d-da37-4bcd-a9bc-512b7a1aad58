package com.pes.jd.dao;

import com.pes.jd.model.DTO.CsSendEvalDTO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;

public interface CsSendEvalDao {

	int batchInsertCsSendEvals(JobShopDTO jobShop, Date date, List<CsSendEvalDTO> csSendEvalList);

	int deleteCsSendEvalsByShopIdByDate(JobShopDTO jobShop, Date date, Date startDate);

	List<CsSendEvalDTO> selectCsSendEvalByDate(JobShopDTO jobShop, List<String> sids, Date date, Date startDate);

}
  
