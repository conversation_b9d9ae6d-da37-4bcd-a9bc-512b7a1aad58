package com.pes.jd.dao;

import com.pes.jd.model.DO.ChatClassifyDO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;


public interface ChatClassifyDao {

	int insertChatClassify(JobShopDTO shop, Date date, ChatClassifyDO chatClassify);

	List<ChatClassifyDO> findByShopIdAndClassify(JobShopDTO shop, Date date, String category);

	void updateById(JobShopDTO shop,ChatClassifyDO entry, Date date);

	void delChatClassify(JobShopDTO shop, Date date);
}

