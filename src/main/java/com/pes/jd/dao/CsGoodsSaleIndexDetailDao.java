package com.pes.jd.dao;

import com.pes.jd.model.DO.CsGoodsSaleIndexDetailDO;
import com.pes.jd.model.DTO.CsGoodsSaleIndexDetailDTO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;

public interface CsGoodsSaleIndexDetailDao {
    int deleteCsGoodsSaleIndexDetailById(Long id);

    int insertCsGoodsSaleIndexDetail(CsGoodsSaleIndexDetailDO record);

    CsGoodsSaleIndexDetailDO selectCsGoodsSaleIndexDetailById(Long id);

    int updateCsGoodsSaleIndexDetail(CsGoodsSaleIndexDetailDO record);


    int deleteCsGoodsSaleIndexDetailByShopIdByDate(JobShopDTO shop,Date startDate,Date endDate);

	List<CsGoodsSaleIndexDetailDTO> selectCsGoodsSaleIndexDetailByCsNickByDate(JobShopDTO shop, String csNick,
			Date startDate, Date endDate);

	int batchInsertCsGoodsSaleIndexDetail(JobShopDTO shop, Date date, List<CsGoodsSaleIndexDetailDO> csSaleDetailLst);
}