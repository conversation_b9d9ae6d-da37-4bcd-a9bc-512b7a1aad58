  
package com.pes.jd.dao;

import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Query.ValidDateRangeQuery;

import java.util.Date;
import java.util.List;

/**  
 * ClassName:ShopOverviewDao <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午1:34:29 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public interface
ShopOverviewDao {
	
	List<CreatedOrderDTO> selectShopCreatedOredrLst(JobShopDTO shop, Date date,
			ValidDateRangeQuery validDateRange);
	
//	List<ShopDayOverviewDTO> selectShopOredredDataOverview(JobShopDTO shop, Date date,
//			ValidDateRangeQuery validDateRange);

	
	List<SaleOrderDTO> selectShopPaidSaleOrderLst(JobShopDTO shop, Date date,
			ValidDateRangeQuery validDateRange);
	
//	List<ShopDayOverviewDTO> selectShopPaidDataOverview(JobShopDTO shop, Date date,
//			ValidDateRangeQuery validDateRange);
	
	List<ConfirmGoodsOrderDTO> selectShopConfirmGoodsOrderLst(JobShopDTO shop, Date date,
			ValidDateRangeQuery validDateRange);
	
//	List<ShopDayOverviewDTO> selectShopConfirmGoodsDataOverview(JobShopDTO shop, Date date,
//			ValidDateRangeQuery validDateRange);

	List<SaleOrderDTO> selectShopSaleOrderLst(JobShopDTO shop, Date date,
											  ValidDateRangeQuery validDateRange);

	List<OutStockOrderDTO> selectShopOutStockOrderLst(JobShopDTO shop, Date date,
													  ValidDateRangeQuery validDateRange);

    List<SaleOrderDTO> selectPaymentByOrderIds(JobShopDTO shop, List<Long> orderIds, Date period, Date endDate);

//	List<ShopDayOverviewDTO> selectShopOutStockDataOverview(JobShopDTO shop, Date date,
//			ValidDateRangeQuery validDateRange);


}
  
