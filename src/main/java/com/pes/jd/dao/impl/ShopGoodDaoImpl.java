package com.pes.jd.dao.impl;

import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.ShopGoodDao;
import com.pes.jd.mapper.ShopGoodMapper;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodsDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
@Repository
public class ShopGoodDaoImpl implements ShopGoodDao{

	@Resource
	private ShopGoodMapper shopGoodMapper;
	
	@Override
	public int batchShopGood(JobShopDTO shop, List<ShopGoodsDTO> shopGoodLst) {
		int num = 0;
		 if(CollectionUtils.isEmpty(shopGoodLst)) {
			 return num;
		 }
		List<List<ShopGoodsDTO>> shopGoodGroups = CollectionUtil.smallToLst(shopGoodLst, CommonConstants.BATCH_INSERT_LIMIT_NUM);
		String tableName = CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_SHOP_GOODS.getName());
		for (List<ShopGoodsDTO> shopGoodlist : shopGoodGroups) {
			num += shopGoodMapper.batchShopGood(tableName, shopGoodlist);
		}
		return num;
	}

	@Override
	public int deleteByShopGoodsName(JobShopDTO shop) {
		 String tableName = CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_SHOP_GOODS.getName());
		return shopGoodMapper.deleteByShopGoodsName(tableName, shop.getShopId());
	}

//	@Override
//	public int deleteByShopGoodsNameAndWareId(JobShopDTO shop, GoodskuParam goodskuParamPojo) {
//		String tableName = CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_SHOP_GOODS.getName());
//		return shopGoodMapper.deleteByShopGoodsNameAndWareId(tableName, shop.getShopId(),goodskuParamPojo.getGoodskuParam());
//	}


	@Override
	public int batchUpdateShopGood(JobShopDTO shop, List<ShopGoodsDTO> shopGoodLst) {
		if(CollectionUtils.isEmpty(shopGoodLst)) {
			 return 0;
		 }
 		String tableName = CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_SHOP_GOODS.getName());
		return shopGoodMapper.batchUpdateShopGood(tableName, shopGoodLst);
	}

	@Override
	public int deleteByShopIdAndWareIdLst(JobShopDTO shop, List<ShopGoodsDTO> shopGoodLst) {
		int i=0;
		if(CollectionUtils.isEmpty(shopGoodLst)) {
			return 0;
		}
		String tableName = CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_SHOP_GOODS.getName());
		for (ShopGoodsDTO shopGoodsDTO : shopGoodLst) {

			i+=shopGoodMapper.deleteByShopIdAndShopGood(tableName,shop.getShopId(), shopGoodsDTO);
		}
		return i;
	}

    @Override
    public int selectShopGoodNumByShopId(JobShopDTO shop) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(),TableEnum.PES_SHOP_GOODS.getName());
        return shopGoodMapper.selectShopGoodNumByShopId(tableName,shop.getShopId());
    }

    @Override
    public int deleteByShopGoodsNameAndWareIds(JobShopDTO shop, List<Long> wareIds) {
        int num = 0;
        if (CollectionUtils.isEmpty(wareIds)) {
            return num;
        }
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_GOODS.getName());
        List<List<Long>> wareIdLst = CollectionUtil.smallToLst(wareIds, CommonConstants.BATCH_DELETE_LIMIT_NUM_800);
        for (List<Long> ware : wareIdLst) {
            num += shopGoodMapper.deleteByShopGoodsNameAndWareIds(tableName, shop.getShopId(), ware);
        }
        return num;
    }

}
