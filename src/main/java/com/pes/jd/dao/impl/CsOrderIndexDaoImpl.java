package com.pes.jd.dao.impl;

import com.google.common.collect.Lists;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.CsOrderIndexDao;
import com.pes.jd.mapper.CsOrderIndexMapper;
import com.pes.jd.model.DO.CsOrderIndexDO;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;


@Repository
public class CsOrderIndexDaoImpl implements CsOrderIndexDao {

	@Resource
	private CsOrderIndexMapper csOrderIndexMapper;

	@Override
	public List<CsOrderIndexForSearchDTO> searchByDateShop(String schema, Long shopId, Date startDate, Date endDate) {
		return CommonUtils.tablesMerge(
				startDate,endDate,schema,TableEnum.PES_CS_ORDER_INDEX.getName(),(query)->
						csOrderIndexMapper.searchByDateShop(
								query.getTableName(),shopId,query.getBeginDate(),query.getEndDate()
						), CommonUtils.MergeType.MONTH
		);
	}



	

	@Override
	public int batchInsertCsOrderIndex(JobShopDTO shop, Date date, List<CsOrderIndexDO> csOrderIndexLst) {
		if(CollectionUtils.isEmpty(csOrderIndexLst)){
			return 0;
		}
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());

		List<List<CsOrderIndexDO>> lst = CollectionUtil.smallToLst(csOrderIndexLst, CommonConstants.BATCH_INSERT_LIMIT_NUM);
		int rows = 0;
		for (List<CsOrderIndexDO> subLst : lst) {
			rows += csOrderIndexMapper.batchInsertCsOrderIndex(shop.getShopId(), subLst, tableName);
		}
		return rows;
	}

	@Override
	public int deleteShopCsOrderIndexByDate(JobShopDTO shop, Date date) {
	
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.deleteShopCsOrderIndexByDate(shop.getShopId(), date, tableName);
	}
	
	@Override
	public int deleteCsOrderIndexByShopAndDate(JobShopDTO shop, Date startDate, Date endDate) {

		int rows = 0;
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
		for (DateRangeParam drp : tableNames) {
			rows += csOrderIndexMapper.deleteCsOrderIndexByShopAndDate(shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), drp.getTableName());
		}
		return rows;
	}
	
	@Override
	public int updateCsOrderIndexsForAssist(JobShopDTO shop, Date date, List<CsOrderIndexDTO> csOrderIndeLst) {
		if(CollectionUtils.isEmpty(csOrderIndeLst)){
			return 0;
		}
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.updateCsOrderIndexsForAssist(csOrderIndeLst, tableName);
	}

	@Override
	public List<CsOrderIndexDTO> selectShopCsOrderIndexLstByCsAndDate(JobShopDTO shop, String csNick,  Date date) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopCsOrderIndexLstByCsAndDate(shop.getShopId(), csNick, date, tableName);
	}
	
	@Override
	public List<CsOrderIndexDTO> selectShopCsOrderIndexLstByCsAndDateAndBuyerLst(JobShopDTO shop, 
			Date date, String csNick, List<String> buyerNickLst) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopCsOrderIndexLstByCsAndDateAndBuyerLst(shop.getShopId(), date, csNick, buyerNickLst, tableName);
	}

	@Override
	public List<CsOrderIndexDTO> selectShopCsOrderIndexLstByDateAndBuyerLst(JobShopDTO shop,
																			Date date, List<String> buyerNickLst) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopCsOrderIndexLstByDateAndBuyerLst(shop.getShopId(), date, buyerNickLst, tableName);
	}


	@Override
	public List<CsOrderIndexDTO> selectShopCsOrderIndexLstByDateRangeAndBuyerLst(JobShopDTO shop,
					Date startDate, Date endDate, List<String> buyerNickLst) {
		List<DateRangeParam> dps = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName(), false);

		List<CsOrderIndexDTO> result = Lists.newArrayList();
		for (DateRangeParam dp : dps) {
			List<CsOrderIndexDTO> lst = csOrderIndexMapper.selectShopCsOrderIndexLstByDateRangeAndBuyerLst(shop.getShopId(), dp.getBeginDate(), dp.getEndDate(), buyerNickLst, dp.getTableName());
			result.addAll(lst);
		}
		return result;
	}

	@Override
	public List<CsOrderIndexDTO> selectShopCsOrderIndexLstForEnquiry(JobShopDTO shop, Date date, String csNick) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopCsOrderIndexLstForEnquiry(shop.getShopId(), date, csNick, tableName);
	}

	@Override
	public List<SimpleOrderIndexDTO> selectShopCsOrderIndexOrderIdLstByCsAndDateAndBuyerLst(
			JobShopDTO shop, Date date,
			Date startDate,
			Date endDate,
			String csNick,
			List<String> buyerNickLst) {

		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopCsOrderIndexOrderIdLstByCsAndDateAndBuyerLst(shop.getShopId(), csNick, date, startDate, endDate, buyerNickLst, tableName);
	}

	@Override
	public List<CsOrderIndexDTO> selectShopAllCsOrderIndexLstByCsAndDate(JobShopDTO shop,  Date date) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopAllCsOrderIndexLstByCsAndDate(shop.getShopId(), date, tableName);
	}

	@Override
	public List<CsOrderIndexDTO> selectShopCsOrderIndexLstByDate(JobShopDTO shop, Date date) {
		
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectShopCsOrderIndexLstByDate(shop.getShopId(), date, tableName);

	}

	@Override
	public List<CsOrderIndexDTO> selectOrderIndexByOrderIdByCs(JobShopDTO shop, Date finalQueryStartDate, Date endDate, String sellerNick, List<ShopGoodsReviewDTO> retCsOrderLst) {
		if(CollectionUtils.isEmpty(retCsOrderLst)){
			return new ArrayList<>();
		}
		List<DateRangeParam> drpLst = CommonUtils.getTableNamesOfMonth(finalQueryStartDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
		List<CsOrderIndexDTO> csOrderIndexLst = Lists.newArrayList();
		List<CsOrderIndexDTO> retLst;
		for (int i = drpLst.size()-1; i >= 0; i--) {
			retLst = csOrderIndexMapper.selectOrderIndexByOrderIdByCs(shop.getShopId(), sellerNick, retCsOrderLst,drpLst.get(i).getTableName(), drpLst.get(i).getBeginDate(),drpLst.get(i).getEndDate());
			if(CollectionUtils.isNotEmpty(retLst)){
				csOrderIndexLst.addAll(retLst);
				if(csOrderIndexLst.size() == retCsOrderLst.size()){
					return csOrderIndexLst;
				}
			}
		}
		return csOrderIndexLst;
	}
	@Override
	public List<GoodsAnalysisOrderIndexDTO> selectShopCsOrderIndexLstByCsNickAndOrderIdLstAndDate(JobShopDTO shop, Date startDate, Date endDate,
			Set<Long> orderIdSet,String csNick) {
		if(CollectionUtils.isEmpty(orderIdSet)){
			return new ArrayList<GoodsAnalysisOrderIndexDTO>(0);
		}
		List<DateRangeParam> tableNames=	CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
		List<GoodsAnalysisOrderIndexDTO> goodsAnalyLst=Lists.newArrayList();
		for (DateRangeParam param : tableNames) {
			List<GoodsAnalysisOrderIndexDTO> list=csOrderIndexMapper.selectShopCsOrderIndexLstByCsNickAndOrderIdLstAndDate(shop.getShopId(),csNick,param.getBeginDate(),param.getEndDate(),orderIdSet,param.getTableName());
			if(CollectionUtils.isNotEmpty(list)){
				goodsAnalyLst.addAll(list);
			}
		}
		return goodsAnalyLst ;
	}

	@Override
	public List<String> selectAfterSaleBuyer(JobShopDTO shop, ValidDateRangeQuery drq, Set<String> buyerNickSet) {
		String pesOrder = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), drq.getStartDate(), TableEnum.PES_CS_ORDER_INDEX.getName());
		List<String> afterSaleBuyerNick = csOrderIndexMapper.selectAfterSaleBuyer(shop.getShopId(), drq.getStartDate(), pesOrder, buyerNickSet);
		if(CollectionUtils.isEmpty(afterSaleBuyerNick)){
			return new ArrayList<>();
		}
		return afterSaleBuyerNick;
	}

    @Override
    public List<CsOrderIndexDTO> selectBalancePayShopCsOrderIndexLstByDate(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_ORDER_INDEX.getName());
		return csOrderIndexMapper.selectBalancePayShopCsOrderIndexLstByDate(shop.getShopId(), date, tableName);
	}

    @Override
    public List<CsOrderIndexDTO> selectPresaleOrderIndexByShopIdAndDate(JobShopDTO shop, Date startDate, Date endDate) {
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
		List<CsOrderIndexDTO> list = new ArrayList<>();
		for (DateRangeParam tableName : tableNames) {
			List<CsOrderIndexDTO> csOrderIndex = csOrderIndexMapper.selectPresaleOrderIndexByShopIdAndDate(shop.getShopId(), startDate, endDate, tableName.getTableName());
			list.addAll(csOrderIndex);
		}
		return list;
	}


    @Override
	public List<SilentOrderLossDTO> selectShopSilentCreatedOrderHasChatLossByDate(JobShopDTO shop,
			ValidDateRangeQuery drq) {
		List<DateRangeParam> tableNamesOfMonth = CommonUtils.getTableNamesOfMonth(drq.getAdjustEnquiryStartDate(), drq.getEndDate(),shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
		List<SilentOrderLossDTO> retSilentOrderLossLst = Lists.newArrayList();
		for (DateRangeParam drp : tableNamesOfMonth) {
			List<SilentOrderLossDTO> querySilentOrderLossLst = csOrderIndexMapper.selectShopSilentCreatedOrderHasChatLossByDate(shop.getShopId(),drq.getStartDate(), drq.getEndDate(),drp.getTableName());
			if(CollectionUtils.isNotEmpty(querySilentOrderLossLst)){
				retSilentOrderLossLst.addAll(querySilentOrderLossLst);
			}
		}
		return retSilentOrderLossLst;
	}


	@Override
	public List<CsOrderBindInfoDTO> selectShopCsOrderIndexByOrderIdLstForLossRecord(JobShopDTO shop,
			List<Long> orderIdLst, ValidDateRangeQuery drq) {
		if(CollectionUtils.isEmpty(orderIdLst)){
			return null;
		}
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(drq.getAdjustEnquiryStartDate(), drq.getEndDate(), shop.getSchemaId(), TableEnum.PES_CS_ORDER_INDEX.getName());
		List<CsOrderBindInfoDTO> retLst = Lists.newArrayList();
		for (DateRangeParam drp : tableNames) {
			List<CsOrderBindInfoDTO> csOrderBindList = csOrderIndexMapper
					.selectShopCsOrderIndexByOrderIdLstForLossRecord(shop.getShopId(), orderIdLst, drp.getTableName());
			if (CollectionUtils.isNotEmpty(csOrderBindList)) {
				retLst.addAll(csOrderBindList);
			}
		}
		return retLst;
	}

}
