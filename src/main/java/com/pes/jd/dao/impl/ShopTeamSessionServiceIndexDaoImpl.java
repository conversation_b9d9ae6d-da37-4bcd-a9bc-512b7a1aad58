package com.pes.jd.dao.impl;

import com.pes.jd.dao.ShopTeamSessionServiceIndexDao;
import com.pes.jd.mapper.ShopTeamSessionServiceIndexMapper;
import com.pes.jd.model.DO.ShopTeamSessionServiceIndexDO;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class ShopTeamSessionServiceIndexDaoImpl implements ShopTeamSessionServiceIndexDao {

    @Resource
    private ShopTeamSessionServiceIndexMapper mapper;

    @Override
    public int insert(ShopTeamSessionServiceIndexDO record, String schema) {
        String tableName = CommonUtils.getTableName(schema,"pes_shop_team_session_service_index");
        return mapper.insert(record,tableName);
    }

    @Override
    public int deleteDate(Long shopId, String schema, Date date) {
        String tableName = CommonUtils.getTableName(schema,"pes_shop_team_session_service_index");
        return mapper.deleteDate(shopId,tableName,date);
    }
}
