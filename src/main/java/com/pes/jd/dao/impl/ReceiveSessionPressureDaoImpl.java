package com.pes.jd.dao.impl;

import com.pes.jd.dao.ReceiveSessionPressureDao;
import com.pes.jd.mapper.ReceiveSessionPressureMapper;
import com.pes.jd.model.DO.ReceiveSessionPressureDO;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class ReceiveSessionPressureDaoImpl implements ReceiveSessionPressureDao {

    @Resource
    private ReceiveSessionPressureMapper mapper;

    @Override
    public int insertBatch(List<ReceiveSessionPressureDO> record, Date date, String schemaId) {
        final String tableName = CommonUtils.getTableNameOfMonth(schemaId, date, "pes_receive_session_pressure");
        return mapper.insertBatch(record,tableName);
    }

    @Override
    public int deleteByTimePoint(Date beginDate, Date endDate, String schemaId,Long shopId,String nick){
        final String tableName = CommonUtils.getTableNameOfMonth(schemaId, beginDate, "pes_receive_session_pressure");
        return mapper.deleteByTimePoint(beginDate,endDate,tableName,shopId,nick);
    }
}
