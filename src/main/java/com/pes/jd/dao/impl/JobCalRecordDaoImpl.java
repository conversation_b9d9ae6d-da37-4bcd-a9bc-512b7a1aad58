package com.pes.jd.dao.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.pes.jd.dao.JobCalRecordDao;
import com.pes.jd.mapper.JobCalRecordMapper;
import com.pes.jd.model.DTO.JobCalRecordDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.util.CommonUtils;

@Repository
public class JobCalRecordDaoImpl implements JobCalRecordDao{
	
	@Resource
	private JobCalRecordMapper jobCalRecordMapper;

	@Override
	public int deleteJobCalRecordByShopIdAndDate(JobShopDTO jobShop, Date date) {
		String tableName = CommonUtils.getTableNameOfYear(jobShop.getSchemaId(), date, "pes_job_cal_record");
		return jobCalRecordMapper.deleteJobCalRecordByShopIdAndDate(tableName, jobShop.getShopId(), date);
	}

	@Override
	public int insertJobCalRecord(JobShopDTO jobShop, JobCalRecordDTO record, Date date) {
		String tableName = CommonUtils.getTableNameOfYear(jobShop.getSchemaId(), date, "pes_job_cal_record");
		return jobCalRecordMapper.insertJobCalRecord(tableName, record);
	}

}
