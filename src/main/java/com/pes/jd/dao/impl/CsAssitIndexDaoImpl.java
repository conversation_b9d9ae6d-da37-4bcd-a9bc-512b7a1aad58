package com.pes.jd.dao.impl;

import com.pes.jd.dao.CsAssitIndexDao;
import com.pes.jd.mapper.CsAssitIndexMapper;
import com.pes.jd.model.DO.CsAssitIndexDO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

@Repository
public class CsAssitIndexDaoImpl implements CsAssitIndexDao {

    @Resource
    private CsAssitIndexMapper csAssitIndexMapper;

    @Override
    public int deleteByShopDate(Date date, Long shopId,String schema) {
        String tableName = CommonUtils.getTableNameOfYear(schema,date, TableEnum.PES_CS_ASSIT_INDEX.getName());
        return csAssitIndexMapper.deleteByShopDate(date,shopId,tableName);
    }

    @Override
    public int insert(CsAssitIndexDO record,String schemaId) {
        String tableName = CommonUtils.getTableNameOfYear(schemaId,record.getDate(), TableEnum.PES_CS_ASSIT_INDEX.getName());
        return csAssitIndexMapper.insert(record,tableName);
    }
}
