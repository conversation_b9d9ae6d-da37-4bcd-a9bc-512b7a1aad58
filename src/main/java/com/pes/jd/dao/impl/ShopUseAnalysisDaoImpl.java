package com.pes.jd.dao.impl;

import com.pes.jd.dao.ShopUseAnalysisDao;
import com.pes.jd.mapper.ShopUseAnalysisMapper;
import com.pes.jd.model.DO.ShopUseAnalysisDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.ms.domain.Data.rtsub.ShopUseConversion;
import com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年09月09 15:49:49<br>
 */
@Repository
public class ShopUseAnalysisDaoImpl implements ShopUseAnalysisDao {
    @Resource
    private ShopUseAnalysisMapper shopUseAnalysisMapper;
    @Override
    public int deleteShopUseAnalysisByshopIdByDate(JobShopDTO shop, Date date) {
        String tableName= CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_SHOP_USE_ANALYSIS.getName());
        return shopUseAnalysisMapper.deleteShopUseAnalysisByshopIdByDate(shop.getShopId(),date,tableName);
    }

    @Override
    public int insertShopUseAnalysis(JobShopDTO shop,Date date, ShopUseAnalysisDO record) {
        String tableName= CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_SHOP_USE_ANALYSIS.getName());
        return shopUseAnalysisMapper.insertShopUseAnalysis(tableName,record);
    }

    @Override
    public ShopUseAnalysis selectShopUseAnalysisByShopIdByDate(JobShopDTO shop, Date date) {
        String tableName= CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_SHOP_USE_ANALYSIS.getName());
        return shopUseAnalysisMapper.selectShopUseAnalysisByShopIdByDate(shop.getShopId(),date,tableName);
    }

    @Override
    public int updateShopUseShopSaleAmountAndScreenNum(JobShopDTO shop, Date date, Integer num, Double shopSaleAmount) {
        String tableName= CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_SHOP_USE_ANALYSIS.getName());
        return shopUseAnalysisMapper.updateShopUseShopSaleAmountAndScreenNum(shop.getShopId(),date,num,shopSaleAmount,tableName);
    }

    @Override
    public int updateShopUseShopUrgeByShopIdByDate(JobShopDTO shop,Date date, ShopUseAnalysisDO record) {
        String tableName= CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_SHOP_USE_ANALYSIS.getName());
        return shopUseAnalysisMapper.updateShopUseShopUrgeByShopIdByDate(tableName,record);
    }
}
