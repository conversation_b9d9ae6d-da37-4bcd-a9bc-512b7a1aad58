package com.pes.jd.dao.impl;

import com.pes.jd.dao.WsUserDao;
import com.pes.jd.mapper.WsUserMapper;
import com.pes.jd.model.DO.WsUser;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**  
 * ClassName:WsUserDaoImpl <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月24日 下午7:17:16 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Repository
public class WsUserDaoImpl implements WsUserDao {
	
	@Resource
	private WsUserMapper wsUserMapper;

	@Override
	public int insertWsUser(WsUser record) {

		return wsUserMapper.insertWsUser(record);
	}

	@Override
	public int deleteWsUserById(Long id) {

		return wsUserMapper.deleteWsUserById(id);
	}

	@Override
	public int updateWsUserPasswordById(WsUser record) {

		return wsUserMapper.updateWsUserPasswordById(record);
	}

	@Override
	public WsUser selectWsUserById(Long id) {

		return wsUserMapper.selectWsUserById(id);
	}

}
  
