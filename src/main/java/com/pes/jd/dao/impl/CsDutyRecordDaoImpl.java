package com.pes.jd.dao.impl;

import com.pes.jd.dao.CsDutyRecordDao;
import com.pes.jd.mapper.CsDutyRecordMapper;
import com.pes.jd.model.DO.CsDutyRecordDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class CsDutyRecordDaoImpl implements CsDutyRecordDao {
	
	@Resource
	private CsDutyRecordMapper csDutyRecordDaoMapper;

	@Override
	public int insertBatch(JobShopDTO shop, Date date, List<CsDutyRecordDO> csDutyRecordDOList) {
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(),date, TableEnum.PES_CS_DUTY_RECORD.getName());
		return csDutyRecordDaoMapper.insertBatch(shop.getShopId(),csDutyRecordDOList,tableName);
	}

	@Override
	public int deleteCsDutyRecordByShopIdByDate(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), date, TableEnum.PES_CS_DUTY_RECORD.getName());
		return csDutyRecordDaoMapper.deleteCsDutyRecordByShopIdByDate(shop.getShopId(),date,tableName);
	}

	@Override
	public List<CsDutyRecordDO> searchByShopDate(long shopId, Date beginDate, Date endDate,String schema) {
		return CommonUtils.tablesMerge(beginDate, endDate, schema, TableEnum.PES_CS_DUTY_RECORD.getName(), (x) ->
		csDutyRecordDaoMapper.searchByShopDate(shopId, x.getBeginDate(), x.getEndDate(), x.getTableName()),
				CommonUtils.MergeType.YEAR);
	}

}
