package com.pes.jd.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.pes.jd.dao.YdOrderDao;
import com.pes.jd.mapper.YdOrderMapper;
import com.pes.jd.model.DTO.YdOrderDTO;
import com.yiyitech.support.datasource.RoutingDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class YdOrderDaoImpl implements YdOrderDao {

    @Autowired
    private YdOrderMapper ydOrderMapper;

    @Override
    public List<YdOrderDTO> selectOrderByOrderId(List<Long> orderIds) {
        try {
            if (CollectionUtil.isEmpty(orderIds)) return Lists.newArrayListWithCapacity(0);
            RoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
            return ydOrderMapper.selectOrderByOrderId(orderIds);
        }finally {
            RoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
        }
    }

    @Override
    public List<Long> selectOrderIdByVendIdAndDate(Long venderId, Date startDate, Date endDate, Integer type) {
        try {
            RoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db2");
            return ydOrderMapper.selectOrderIdByVendIdAndDate(venderId, startDate, endDate, type);
        }finally {
            RoutingDataSource.DataSourceKeyHolder.setDataSourceKey("db1");
        }
    }
}
