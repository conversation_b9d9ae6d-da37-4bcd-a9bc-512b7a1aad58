package com.pes.jd.dao.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.ShopTeamTOrderPerformanceDao;
import com.pes.jd.mapper.ShopTeamTOrderPerformanceMapper;
import com.pes.jd.model.DO.ShopTeamTOrderPerformanceDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class ShopTeamTOrderPerformanceDaoImpl implements ShopTeamTOrderPerformanceDao {
	
	@Resource
	private ShopTeamTOrderPerformanceMapper shopTeamTOrderPerformanceMapper;
	
	@Override
	public int insertShopTeamTOrderPerformance(JobShopDTO shop, ShopTeamTOrderPerformanceDO record) {
		
		List<ShopTeamTOrderPerformanceDO> recordLst = Lists.newArrayListWithExpectedSize(1);
		recordLst.add(record);
		return this.insertShopTeamTOrderPerformances(shop, recordLst);
	}

	@Override
	public int insertShopTeamTOrderPerformances(JobShopDTO shop, List<ShopTeamTOrderPerformanceDO> recordLst) {
		
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.insertShopTeamTOrderPerformances(shop.getShopId(), recordLst, tableName);
	}

	@Override
	public int deleteShopTeamTOrderPerformance(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.deleteShopTeamTOrderPerformance(shop.getShopId(), date, tableName);
	}

	@Override
	public int updateShopTeamTOrderPerformanceSelective(JobShopDTO shop, ShopTeamTOrderPerformanceDO record) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.updateShopTeamTOrderPerformanceSelective(record, tableName);
	}


	@Override
	public int updateShopTeamSaleAndOutStackData(JobShopDTO shop, ShopTeamTOrderPerformanceDO record) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.updateShopTeamSaleAndOutStackData(record, tableName);
	}

	@Override
	public int updateShopTeamOutStackData(JobShopDTO shop, ShopTeamTOrderPerformanceDO record) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.updateShopTeamOutStackData(record, tableName);
	}

	@Override
	public ShopTeamTOrderPerformanceDO selectShopTeamTOrderPerformanceForUpdate(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.selectShopTeamTOrderPerformanceForUpdate(shop.getShopId(), date, tableName);
	}

    @Override
    public ShopTeamTOrderPerformanceDO selectShopTeamTOrderPerformance(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), TableEnum.PES_SHOP_TEAM_TORDER_PERFORMANCE.getName());
		return shopTeamTOrderPerformanceMapper.selectShopTeamTOrderPerformance(shop.getShopId(), date, tableName);
	}

}
