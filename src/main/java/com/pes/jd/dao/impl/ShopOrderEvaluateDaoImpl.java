package com.pes.jd.dao.impl;

import com.pes.jd.dao.ShopOrderEvaluateDao;
import com.pes.jd.mapper.ShopOrderEvaluateMapper;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopOrderEvaluateDTO;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

@Repository
public class ShopOrderEvaluateDaoImpl implements ShopOrderEvaluateDao {

	@Resource
	private ShopOrderEvaluateMapper shopOrderEvaluateMapper;

	@Override
	public int deleteShopOrderEvaluateByDateByShopId(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_order_evaluate");
		return shopOrderEvaluateMapper.deleteShopOrderEvaluateByDateByShopId(shop.getShopId(), date, tableName);
	}

	@Override
	public int insertShopOrderEvaluate(JobShopDTO shop, Date date, ShopOrderEvaluateDTO shopOrderEvaluate) {
		if (shopOrderEvaluate == null) {
			return 0;
		}
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_order_evaluate");
		return shopOrderEvaluateMapper.insertShopOrderEvaluate(shopOrderEvaluate, tableName);
	}

}
