package com.pes.jd.dao.impl;

import com.pes.jd.dao.SchemaDao;
import com.pes.jd.mapper.SchemaMapper;
import com.pes.jd.model.DO.Schema;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * ClassName:SchemaDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午6:39:03 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository
public class SchemaDaoImpl implements SchemaDao {

	@Resource
	private SchemaMapper schemaMapper;

	@Override
	public int deleteSchemaById(Integer id) {
		return schemaMapper.deleteSchemaById(id);
	}

	@Override
	public int insertSchema(Schema record) {
		return schemaMapper.insertSchema(record);
	}

	@Override
	public Schema getSchemaById(Integer id) {
		return schemaMapper.getSchemaById(id);
	}

	@Override
	public int updateSchemaBySelective(Schema record) {
		return schemaMapper.updateSchemaBySelective(record);
	}

}
