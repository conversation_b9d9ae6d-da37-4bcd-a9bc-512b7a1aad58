package com.pes.jd.dao.impl;

import com.pes.jd.dao.ShopTeamAssitIndexDao;
import com.pes.jd.mapper.ShopTeamAssitIndexMapper;
import com.pes.jd.model.DO.ShopTeamAssitIndexDO;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

@Repository
public class ShopTeamAssitIndexDaoImpl implements ShopTeamAssitIndexDao {

    @Resource
    private ShopTeamAssitIndexMapper shopTeamAssitIndexMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return 0;
    }

    @Override
    public int insert(ShopTeamAssitIndexDO record,String schema) {
        String tableName = CommonUtils.getTableName(schema,"pes_shop_team_assit_index");
        return shopTeamAssitIndexMapper.insert(record,tableName);
    }

    @Override
    public int insertSelective(ShopTeamAssitIndexDO record) {
        return 0;
    }

    @Override
    public ShopTeamAssitIndexDO selectByPrimaryKey(Long id) {
        return null;
    }

    @Override
    public int updateByPrimaryKeySelective(ShopTeamAssitIndexDO record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(ShopTeamAssitIndexDO record) {
        return 0;
    }

    @Override
    public int deleteByDateShopId(Long shopId, String schema, Date date) {
        String tableName = CommonUtils.getTableName(schema,"pes_shop_team_assit_index");
        return shopTeamAssitIndexMapper.deleteByDateShopId(shopId,tableName,date);
    }
}
