

package com.pes.jd.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.OrderDao;
import com.pes.jd.mapper.OrderMapper;
import com.pes.jd.model.DO.CsNickAndOrderIdDo;
import com.pes.jd.model.DTO.*;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Param.OrderStatusEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.*;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * ClassName:TradeDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月24日 下午7:27:51 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@Repository
public class OrderDaoImpl implements OrderDao {

    @Resource
    private OrderMapper orderMapper;

    @Override
    public int persistOrderByFile(JobShopDTO shop, String filePath, Date jobDate) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate, TableEnum.PES_ORDER.getName());
        return orderMapper.persistOrderByFile(filePath, tableName);
    }

    @Override
    public int deleteOrderByOrderIdLst(JobShopDTO shop, List<Long> orderIdLst, Date jobDate) {
        if (CollectionUtils.isEmpty(orderIdLst)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate, TableEnum.PES_ORDER.getName());
        return orderMapper.deleteOrdersByTids(orderIdLst, tableName);
    }

    @Override
    public int updateOrderStatusByOrderIdAndDate(JobShopDTO shop, Set<Long> orderIdLst, Date date) {
        if (CollectionUtils.isEmpty(orderIdLst)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_ORDER.getName());
        return orderMapper.updateOrderStatusByOrderIdAndDate("TRADE_CANCELED", orderIdLst, tableName);
    }

    @Override
    public int persistOrders(List<OrderDTO> orders, String tableName) {
        if (CollectionUtils.isEmpty(orders)) {
            return 0;
        }
        return orderMapper.persistOrders(orders, tableName);
    }

    @Override
    public List<BuyerOrderDTO> selectShopCreatedOrderLstByBuyersAndDateForFirstConsult(JobShopDTO shop, Date startDate, Date endDate, List<String> buyerLst) {
        if (CollectionUtils.isEmpty(buyerLst)) {
            return null;
        }
        List<BuyerOrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            List<BuyerOrderDTO> lst = orderMapper.selectShopCreatedOrderLstByBuyersAndDateForFirstConsult(shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), buyerLst, drp.getTableName());
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<BuyerOrderDTO> selectShopCreatedOrderLstByBuyersAndDate(JobShopDTO shop, List<String> buyerLst, ValidDateRangeQuery validDateRange,Set<Long> filterOrderIdSet) {
        if (CollectionUtils.isEmpty(buyerLst)) {
            return null;
        }
        List<BuyerOrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDateRange.getStartDate(), validDateRange.getEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            List<BuyerOrderDTO> lst = orderMapper.selectShopCreatedOrderLstByBuyersAndDate(shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), buyerLst,filterOrderIdSet, drp.getTableName());
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<BuyerOrderDTO> selectShopOrderLstByBuyersAndDateForAfterSale(JobShopDTO shop, List<String> buyerLst, ValidDateRangeQuery validDateRange) {
        if (CollectionUtils.isEmpty(buyerLst)) {
            return null;
        }
        List<BuyerOrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDateRange.getStartDate(), validDateRange.getEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            List<BuyerOrderDTO> lst = orderMapper.selectShopOrderLstByBuyersAndDateForAfterSale(shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), buyerLst, drp.getTableName());
            result.addAll(lst);
        }
        return result;
    }
    /**
     * 查询之宗买家付款或者付尾款的订单
     *
     * @param shop
     * @param buyerLst
     * @param validDateRange
     * @return
     */
    @Override
    public List<BuyerOrderDTO> selectShopPaidOrderLstByBuyersAndDate(JobShopDTO shop, List<String> buyerLst, ValidDateRangeQuery validDateRange,Set<Long> filterOrderIdSet) {
        if (CollectionUtils.isEmpty(buyerLst)) {
            return null;
        }
        List<BuyerOrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDateRange.getAdjustStartDate(), validDateRange.getAdjustEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            List<BuyerOrderDTO> lst = orderMapper.selectShopPaidOrderLstByBuyersAndDate(shop.getShopId(), validDateRange.getStartDate(), validDateRange.getEndDate(), buyerLst,filterOrderIdSet, drp.getTableName());
            result.addAll(lst);
        }
        return result;
    }
    @Override
    public Optional<Set<Long>> selectShopOutStockOrderIdLstByOrderIdLst(JobShopDTO shop, List<Long> orderIdLst, ValidDateRangeQuery validDateRange, Date endOutValid) {
        if (CollectionUtils.isEmpty(orderIdLst)) {
            return Optional.empty();
        }
        Set<Long> allOrderIdSet = Sets.newHashSet();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDateRange.getAdjustOutStockStartDate(), validDateRange.getAdjustOutStockEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<Long> lst = orderMapper.selectShopOutStockOrderIdLstByOrderIdLst(shop.getShopId(), orderIdLst, tableName, endOutValid);
            allOrderIdSet.addAll(lst);
        }
        return Optional.of(allOrderIdSet);
    }

    @Override
    public List<SilentOrderLossDTO> selectShopSilentCreatedOrderNoChatLossByDate(JobShopDTO shop, ValidDateRangeQuery drq) {
        String pesOrder = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), drq.getStartDate(), "pes_order");
        List<DateRangeParam> tableNamesOfMonth = CommonUtils.getTableNamesOfMonth(drq.getAdjustEnquiryStartDate(), drq.getEndDate(), shop.getSchemaId(), "pes_cs_order_index");
        List<SilentOrderLossDTO> silentOrderLossLst = orderMapper.selectShopSilentCreatedOrderNoChatLossByDate(shop.getShopId(), drq.getStartDate(), pesOrder, tableNamesOfMonth);
        return silentOrderLossLst;
    }

    @Override
    public List<String> selectBuyerNickWasTodayPlacedAndPaid(JobShopDTO shop, ValidDateRangeQuery drq, Set buyerNickSet) {
        String pesOrder = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), drq.getStartDate(), "pes_order");
        return orderMapper.selectBuyerNickWasTodayPlacedAndPaid(shop.getShopId(), drq.getStartDate(), pesOrder, buyerNickSet);
    }

    @Override
    public int updateOrderByOrderId(JobShopDTO shop, Long orderId, Date outStockTime, String orderCreatedTime, String status) throws ParseException {
        //outStockValidTime  出库往前推的时间点
        //Date outStockValidTime = DateUtils.getDateByPeriod(DateUtils.parseYMd(orderCreatedTime), 1);
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), DateFormatUtils.parseYMd(orderCreatedTime), "pes_order");
        return orderMapper.updateOrderByOrderId(shop.getShopId(), orderId, outStockTime, status, tableName);
    }

    @Override
    public List<OrderDTO> selectShopCsOutStockLossOrderByOrderId(JobShopDTO shop, List<Long> orderIdList,
                                                                 ValidDateRangeQuery drq, Date endOutValidDate) {
        List<OrderDTO> retList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(orderIdList)) {
            return retList;
        }
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(drq.getStartDate(), drq.getEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam param : tableNames) {
            List<OrderDTO> orderList = orderMapper.selectShopCsOutStockLossOrderByOrderId(shop.getShopId(), orderIdList, param.getBeginDate(), param.getEndDate(), param.getTableName(), endOutValidDate);
            if (CollectionUtils.isNotEmpty(orderList)) {
                retList.addAll(orderList);
            }
        }
        return retList;
    }

    @Override
    public Set<Long> selectShopOutStockOrderLstByOrderIdSet(JobShopDTO shop, Date startDate, Date endDate, List<Long> orderIdLst, Date endOutValid) {
        Date adjustendDate = DateUtil.getDateByPeriod(endDate, 30);
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, adjustendDate, shop.getSchemaId(), "pes_order");
        Set<Long> result = Sets.newHashSet();
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<Long> lst = orderMapper.selectShopOutStockOrderLstByOrderIdLst(shop.getShopId(), orderIdLst, tableName, endOutValid);
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<OrderDTO> selectShopOrderRefundByOrderId(JobShopDTO shop, Date date, Set<String> orderRefundIdSet) {
        List<OrderDTO> totalOrderList = new ArrayList<OrderDTO>();
        if (CollectionUtils.isEmpty(orderRefundIdSet)) {
            return totalOrderList;
        }
        Date startDate = DateUtil.getDateByPeriod(date, CommonConstants.ORDER_REFUND_DELAY_DAYS);
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, date, shop.getSchemaId(), "pes_order");
        for (DateRangeParam dateRangeParam : tableNames) {
            List<OrderDTO> orderList = orderMapper.selectShopOrderRefundByOrderId(shop.getShopId(), orderRefundIdSet, dateRangeParam.getTableName());
            totalOrderList.addAll(orderList);
        }
        return totalOrderList;
    }
    @Override
    public int insertOrderCancel(JobShopDTO shop, Date date, OrderDTO order) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, "pes_order_cancel");
        return orderMapper.insertOrderCancel(tableName, order);
    }

    @Override
    public int deleteOrderCanecelByOrderId(JobShopDTO shop, Long orderId, Date jobDate) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate, "pes_order_cancel");
        return orderMapper.deleteOrderCancelByOrderId(orderId, tableName);
    }

    @Override
    public List<OrderDTO> selectShopOrderByShopIdByCreated(JobShopDTO shop, Date startDate, Date endDate, Integer type) {
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<OrderDTO> list = Lists.newArrayList();
        for (DateRangeParam table : tableNames) {
            List<OrderDTO> orderLst = orderMapper.selectShopOrderByShopIdByCreated(shop.getShopId(), table.getTableName(), table.getBeginDate(), table.getEndDate(), type);
            if (CollectionUtils.isNotEmpty(orderLst)) {
                list.addAll(orderLst);
            }
        }
        return list;
    }

    @Override
    public List<OrderDTO> selectShopOrderByShopIdByPayTime(JobShopDTO shop, Date preDates, Date startDate, Date endDate) {
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(preDates, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<OrderDTO> list = Lists.newArrayList();
        for (DateRangeParam param : tableNames) {
            List<OrderDTO> orderLst = orderMapper.selectShopOrderByShopIdByPayTime(shop.getShopId(), param.getTableName(), startDate, endDate);
            if (CollectionUtils.isNotEmpty(orderLst)) {
                list.addAll(orderLst);
            }

        }
        return list;
    }

    @Override
    public Set<Long> selectShopCashOrderByShopIdByOrderIdByCreated(JobShopDTO shop, Date startDate, Date endDate,
                                                                   Integer type, Set<Long> orderIds) {
        Set<Long> result = Sets.newHashSet();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (DateRangeParam param : tableNames) {
            Set<Long> orderIdSet = orderMapper.selectShopCashOrderByShopIdByOrderIdByCreated(shop.getShopId(), param.getBeginDate(), param.getEndDate(), param.getTableName(), type, orderIds);
            if (CollectionUtils.isNotEmpty(orderIdSet)) {
                result.addAll(orderIdSet);
            }
        }
        return result;
    }

    @Override
    public List<OrderDTO> selectCanceledOrderByOrderLst(JobShopDTO shop, ValidDateRangeQuery drq, List<Long> orderIdLst) {

        List orderLst = new ArrayList<>();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(drq.getStartDate(), drq.getEndDate(), shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        for (DateRangeParam param : tableNames) {
            List<OrderDTO> orderList = orderMapper.selectCanceledOrderByOrderLst(shop.getShopId(), param.getBeginDate(), param.getEndDate(), param.getTableName(), orderIdLst);
            if (CollectionUtils.isNotEmpty(orderList)) {
                orderLst.addAll(orderList);
            }
        }
        return orderLst;
    }
    /**
     * 过滤出状态为取消的订单
     *
     * @param orderLst
     * @return
     */
    @Override
    public List<OrderDTO> filterCancelOrder(List<OrderDTO> orderLst) {
        List<OrderDTO> cancelOrderLst = new ArrayList<>();
        if (orderLst == null || orderLst.size() == 0) return new ArrayList<>();
        Date now = new Date();
        Date yesterday = DateUtils.getDateByPeriod(now, -1);//昨天此刻
        Date sevendays = DateUtils.getDateByPeriod(now, -10);//10天前此刻
        for (OrderDTO order : orderLst) {
            //取消类型的订单
            if (isCancelOrder(order)) {
                cancelOrderLst.add(order);
                continue;
            }
            if (order == null || order.getCreated() == null || order.getPayType() == null ||
                    order.getOrderType() == null || order.getOrderType() == 1 // 订单类型 为空 或 预售订单
                    || StringUtils.isEmpty(order.getStatus())
            ) {
                continue;
            }

            if ((4 == order.getPayType() && order.getCreated().getTime() < yesterday.getTime())
                    || (5 == order.getPayType() && order.getCreated().getTime() < sevendays.getTime())) {
                order.setStatus(OrderStatusEnum.TRADE_CANCELED.getStatus());
                cancelOrderLst.add(order);
            }
        }
        return cancelOrderLst;
    }

    private boolean isCancelOrder(OrderDTO order) {
        return (order != null && StringUtils.isNotEmpty(order.getStatus())) && Objects.equals(order.getStatus(), OrderStatusEnum.TRADE_CANCELED.getStatus());
    }

    @Override
    public List<OrderDTO> selectPreordainOrderByCreatedAndBuyers(JobShopDTO shop, Date start, Date end, Set<String> buyers) {
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(start, end, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<OrderDTO> list = Lists.newArrayList();
        for (DateRangeParam param : tableNames) {
            List<OrderDTO> orderLst = orderMapper.selectPreordainOrderByCreatedAndBuyers(shop.getShopId(), param.getTableName(), param.getBeginDate(), param.getEndDate(), buyers);
            if (CollectionUtils.isNotEmpty(orderLst)) {
                list.addAll(orderLst);
            }
        }
        return list;
    }

    @Override
    public List<CsNickAndOrderIdDo> selectBuyerNickAndOrderIdByOrderIds(Long shopId, String schemaId, Date finalQueryStartDate, Date endDate, Set<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        List<DateRangeParam> drpLst = CommonUtils.getTableNamesOfMonth(finalQueryStartDate, endDate, schemaId, "pes_order");
        List<CsNickAndOrderIdDo> csNickAndOrderIdDos = Lists.newArrayList();
        List<CsNickAndOrderIdDo> retLst = null;
        for (int i = drpLst.size() - 1; i >= 0; i--) {
            retLst = orderMapper.selectBuyerNickAndOrderIdByOrderIds(shopId, orderIds, drpLst.get(i).getTableName(), drpLst.get(i).getBeginDate(), drpLst.get(i).getEndDate());
            if (CollectionUtils.isNotEmpty(retLst)) {
                csNickAndOrderIdDos.addAll(retLst);
                if (csNickAndOrderIdDos.size() == orderIds.size()) {
                    return csNickAndOrderIdDos;
                }
            }
        }
        return csNickAndOrderIdDos;
    }

    @Override
    public List<OrderDTO> selectOrderByShopIdAndOrderIds(Set<Long> orderIds, JobShopDTO shop,Date startDate,Date endDate) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        List<DateRangeParam> dateRangeParams = CommonUtils.getTableNamesOfMonth(startDate,endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<String> tableNames = dateRangeParams.stream().map(DateRangeParam::getTableName).collect(Collectors.toList());
        return orderMapper.selectOrderByShopIdAndOrderIds(shop.getShopId(), tableNames, orderIds);
//        return orderMapper.selectOrderByShopIdAndOrderIdsAndDate(shop.getShopId(), tableNames, orderIds, startDate, endDate);
    }

    @Override
    public List<OrderDTO> selectOrderByShopIdAndOrderIdsAndDate(Set<Long> orderIds, JobShopDTO shop,Date startDate,Date endDate) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        List<DateRangeParam> dateRangeParams = CommonUtils.getTableNamesOfMonth(startDate,endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<String> tableNames = dateRangeParams.stream().map(DateRangeParam::getTableName).collect(Collectors.toList());
        return orderMapper.selectOrderByShopIdAndOrderIdsAndDate(shop.getShopId(), tableNames, orderIds, startDate, endDate);
    }

    @Override
    public List<OrderDTO> selectParentOrderToPayCsSaleOrderLstByShop(JobShopDTO shop, ValidDateRangeQuery validDateRange) {
        List<OrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDateRange.getAdjustStartDate(), validDateRange.getAdjustEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<OrderDTO> lst = orderMapper.selectParentOrderToPayCsSaleOrderLstByShopId(shop.getShopId(), validDateRange.getStartDate(), validDateRange.getEndDate(), tableName);
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<OrderDTO> selectOrderDirectTradeIdByOrderId(List<Long> orderIds, JobShopDTO shop, Date start, Date end) {
        if(CollUtil.isEmpty(orderIds)){
            return new ArrayList<>(0);
        }
        List<OrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(start,end, shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<OrderDTO> lst = orderMapper.selectOrderDirectTradeIdByOrderId(shop.getShopId(),orderIds,tableName);
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<Long> selectOrderDirectTradeId(JobShopDTO shop,Long orderType, Date startDate, Date endDate) {

        List<Long> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate,endDate, shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<Long> lst = orderMapper.selectOrderDirectTradeId(shop.getShopId(),orderType,startDate,endDate,tableName);
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<OrderDTO> selectShopOrderByShopIdByCreatedByOrderIds(JobShopDTO shop, Date startDate, Date endDate, Collection<Long> orderIdSet, int type) {
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        List<OrderDTO> list = Lists.newArrayList();
        for (DateRangeParam table : tableNames) {
            List<OrderDTO> orderLst = orderMapper.selectShopOrderByShopIdByCreatedByOrderIds(shop.getShopId(), table.getTableName(), table.getBeginDate(), table.getEndDate(), orderIdSet, type);
            if (CollectionUtils.isNotEmpty(orderLst)) {
                list.addAll(orderLst);
            }
        }
        return list;
    }

    @Override
    public List<OrderDTO> selectTradeCanceledOrderByOrderIdLst(JobShopDTO shop, Date date, List<Long> orderIdLst) {
        if (CollUtil.isEmpty(orderIdLst)) {
            return new ArrayList<>();
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_ORDER.getName());
        List<OrderDTO> resultLst = Lists.newArrayList();
        for (List<Long> tOrderIdLst : CollectionUtil.smallToLst(orderIdLst, CommonConstants.BATCH_SELECT_LIMIT_NUM)) {
            List<OrderDTO> tOrderLst = orderMapper.selectTradeCanceledOrderByOrderIdLst(shop.getShopId(), tableName, tOrderIdLst);
            if (CollUtil.isNotEmpty(tOrderLst)) {
                resultLst.addAll(tOrderLst);
            }
        }
        return resultLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderForPresaleOrderByShopIdAndDateAndBuyerNick(JobShopDTO shop, Date startDate, Date startDate1, Date endDate) {
        List<OrderDTO> parentOrderLst = Lists.newArrayList();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<OrderDTO> parentOrder = orderMapper.selectParentOrderByShopIdAndDateAndBuyerNick(shop.getShopId(), startDate1, endDate, dateRangeParam.getTableName());
            if (CollUtil.isEmpty(parentOrder)) continue;
            parentOrderLst.addAll(parentOrder);
        }
        return parentOrderLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderForPresaleOrderByShopIdAndDateAndBuyerNickTwo(JobShopDTO shop, Date startDate, Date endDate) {
        List<OrderDTO> parentOrderLst = Lists.newArrayList();
        //根据开始时间，结束时间获取 各月份表
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_ORDER.getName());
        //遍历表集合
        for (CommonUtils.DateRangeParam dateRangeParam : tableNames) {
            List<OrderDTO> parentOrder = orderMapper.selectParentOrderByShopIdAndDateAndBuyerNickTwo(shop.getShopId(), startDate, endDate, dateRangeParam.getTableName());
            if (CollUtil.isEmpty(parentOrder)) continue;
            parentOrderLst.addAll(parentOrder);
        }
        return parentOrderLst;
    }

    @Override
    public List<OrderDTO> selectParentOrderToPayCsSaleOrderLst(JobShopDTO shop, String csNick, ValidDateRangeQuery validDateRange) {
        List<OrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDateRange.getAdjustStartDate(), validDateRange.getAdjustEndDate(), shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<OrderDTO> lst = orderMapper.selectParentOrderToPayCsSaleOrderLst(shop.getShopId(), csNick,
                    validDateRange.getStartDate(), validDateRange.getEndDate(), tableName);
            result.addAll(lst);
        }
        return result;
    }
    @Override
    public List<OrderDTO> selectParentOrderToPayCsSaleOrderLstNew(JobShopDTO shop, Date startDate, Date endDate) {
        List<OrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<OrderDTO> lst = orderMapper.selectParentOrderToPayCsSaleOrderLstNew(shop.getShopId(), startDate, endDate, tableName);
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<OrderDTO> selectOrdersByDateScopeAndBuyerNicks(JobShopDTO shop, Date startDate, Date endDate, Set<String> buyers) {
        List<OrderDTO> result = Lists.newArrayList();
        List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), "pes_order");
        for (DateRangeParam drp : tableNames) {
            String tableName = drp.getTableName();
            List<OrderDTO> lst = orderMapper.selectOrdersByDateScopeAndBuyerNicks(shop.getShopId(), startDate, endDate, tableName, buyers);
            result.addAll(lst);
        }
        return result;
    }

    @Override
    public List<Long> selectParentOrderByOrderIds(JobShopDTO shop, List<Long> orderIds, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_ORDER.getName());
        return orderMapper.selectParentOrderByOrderIds(shop.getShopId(), orderIds, tableName);
    }
}
  
