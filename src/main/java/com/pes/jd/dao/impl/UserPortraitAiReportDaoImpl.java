package com.pes.jd.dao.impl;

import com.pes.jd.dao.UserPortraitAiReportDao;
import com.pes.jd.mapper.UserPortraitAiReportMapper;
import com.pes.jd.model.DO.UserPortraitAiReport;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 用户画像AI分析报告表 DAO实现类
 * <AUTHOR>
 */
@Repository
public class UserPortraitAiReportDaoImpl implements UserPortraitAiReportDao {

    @Autowired
    private UserPortraitAiReportMapper userPortraitAiReportMapper;

    @Override
    public int insertUserPortraitAiReport(UserPortraitAiReport report, String schemaId) {
        // 使用reportDate字段转换为Date来生成年份分表
        Date reportDate = Date.from(report.getReportDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        String tableName = CommonUtils.getTableNameOfYear(schemaId, reportDate, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName());
        return userPortraitAiReportMapper.insertUserPortraitAiReport(report, tableName);
    }

    @Override
    public int batchInsertUserPortraitAiReport(List<UserPortraitAiReport> reportList, String schemaId) {
        if (reportList == null || reportList.isEmpty()) {
            return 0;
        }
        
        // 使用第一个报告的日期来确定表名（假设批量插入的数据都是同一年的）
        Date reportDate = Date.from(reportList.get(0).getReportDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        String tableName = CommonUtils.getTableNameOfYear(schemaId, reportDate, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName());
        return userPortraitAiReportMapper.batchInsertUserPortraitAiReport(reportList, tableName);
    }

    @Override
    public int deleteUserPortraitAiReportByShopIdAndDate(Long shopId, LocalDate reportDate, String schemaId) {
        Date date = Date.from(reportDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        String tableName = CommonUtils.getTableNameOfYear(schemaId, date, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName());
        return userPortraitAiReportMapper.deleteUserPortraitAiReportByShopIdAndDate(shopId, reportDate, tableName);
    }

    @Override
    public int deleteUserPortraitAiReportByShopIdAndDateRange(Long shopId, LocalDate startDate, LocalDate endDate, String schemaId) {
        // 对于跨年的日期范围，需要使用tablesMerge来处理多个年份表
        Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        List<Integer> results = CommonUtils.tablesMerge(
                start, end, schemaId, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName(),
                (query) -> Collections.singletonList(userPortraitAiReportMapper.deleteUserPortraitAiReportByShopIdAndDateRange(
                        shopId, startDate, endDate, query.getTableName())),
                CommonUtils.MergeType.YEAR
        );
        return 0;

    }

    @Override
    public int deleteAllUserPortraitAiReportByShopId(Long shopId, String schemaId) {
        // 这里需要根据业务需求决定是删除所有年份表的数据，还是只删除当前年份的数据
        // 为了安全起见，这里只删除当前年份的数据
        Date currentDate = new Date();
        String tableName = CommonUtils.getTableNameOfYear(schemaId, currentDate, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName());
        return userPortraitAiReportMapper.deleteAllUserPortraitAiReportByShopId(shopId, tableName);
    }

    @Override
    public UserPortraitAiReport selectUserPortraitAiReportById(Long id, String schemaId) {
        // 由于不知道具体的年份，这里需要根据业务需求来确定查询策略
        // 可以考虑查询当前年份，或者提供额外的年份参数
        Date currentDate = new Date();
        String tableName = CommonUtils.getTableNameOfYear(schemaId, currentDate, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName());
        return userPortraitAiReportMapper.selectUserPortraitAiReportById(id, tableName);
    }

    @Override
    public List<UserPortraitAiReport> selectUserPortraitAiReportByShopIdAndDateRange(Long shopId, LocalDate startDate, LocalDate endDate, String schemaId) {
        Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        // 使用年份分表合并查询
        return CommonUtils.tablesMerge(
                start, end, schemaId, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName(),
                (query) -> userPortraitAiReportMapper.selectUserPortraitAiReportByShopIdAndDateRange(
                        shopId, startDate, endDate, query.getTableName()),
                CommonUtils.MergeType.YEAR
        );
    }

    @Override
    public List<UserPortraitAiReport> selectUserPortraitAiReportByConditions(Long shopId, Integer reportType, Integer chartType, LocalDate startDate, LocalDate endDate, String schemaId) {
        Date start = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        // 使用年份分表合并查询
        return CommonUtils.tablesMerge(
                start, end, schemaId, TableEnum.PES_USER_PORTRAIT_AI_REPORT.getName(),
                (query) -> userPortraitAiReportMapper.selectUserPortraitAiReportByConditions(
                        shopId, reportType, chartType, startDate, endDate, query.getTableName()),
                CommonUtils.MergeType.YEAR
        );
    }
}
