package com.pes.jd.dao.impl;

import com.pes.jd.dao.LossOrderRecordDao;
import com.pes.jd.mapper.LossOrderRecordMapper;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.LossOrderRecordDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class LossOrderRecordDaoImpl implements LossOrderRecordDao {

	@Resource
	private LossOrderRecordMapper LossOrderRecordMapper;
	@Override
	public int insertBatchShopLossOrderEnquiryLoss(JobShopDTO shop, List<LossOrderRecordDTO> lossOrderEnquiryList, Date date) {
		if(CollectionUtils.isEmpty(lossOrderEnquiryList)){
			return 0;
		}
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_LOSS_ORDER_RECORD.getName());
		return LossOrderRecordMapper.insertBatchShopLossOrderEnquiryLoss(shop.getShopId(),lossOrderEnquiryList,tableName);
	}
	@Override
	public int deleteShopLossOrderByCratedDateByType(JobShopDTO shop, ValidDateRangeQuery drq,Integer lostType) {
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(drq.getStartDate(), drq.getEndDate(), shop.getSchemaId(), TableEnum.PES_LOSS_ORDER_RECORD.getName());
		int delRows = 0;
		for (DateRangeParam drp : tableNames) {
			delRows += LossOrderRecordMapper.deleteShopLossOrderByCratedDateByType(shop.getShopId(),lostType,drp.getBeginDate(),drp.getEndDate(),drp.getTableName());
		}
		return delRows;
	}
	@Override
	public List<Long> selectLossEnquiryOrderIdByDate(JobShopDTO shop, Date date, Integer status,Integer lostType) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_LOSS_ORDER_RECORD.getName());
		return LossOrderRecordMapper.selectLossEnquiryOrderIdByDate(shop.getShopId(),DateUtil.getStartTimeOfDate(date),DateUtil.getEndTimeOfDate(date),status,lostType,tableName);
	}
	@Override
	public List<LossOrderRecordDTO> selectLossEnquiryOrderByDate(JobShopDTO shop, Date date, Integer lostType, ValidDateRangeQuery drq) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_LOSS_ORDER_RECORD.getName());
		return LossOrderRecordMapper.selectLossEnquiryOrderByDate(shop.getShopId(),drq.getStartDate(),drq.getEndDate(),lostType,tableName);
	}
	@Override
	public List<LossOrderRecordDTO> selectOrderPaymentLossByDateForCalculatePaymentLoss(JobShopDTO shop,
			Date startDate, Date endDate) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), startDate, TableEnum.PES_LOSS_ORDER_RECORD.getName());
		return LossOrderRecordMapper.selectOrderPaymentLossByDateForCalculatePaymentLoss(shop.getShopId(),startDate,endDate,tableName);
	}

}
  
