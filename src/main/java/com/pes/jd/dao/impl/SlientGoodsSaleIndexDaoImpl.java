package com.pes.jd.dao.impl;

import com.pes.jd.dao.SlientGoodsSaleIndexDao;
import com.pes.jd.mapper.SlientGoodsSaleIndexMapper;
import com.pes.jd.model.DO.SlientGoodsSaleIndexDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@Repository
public class SlientGoodsSaleIndexDaoImpl implements SlientGoodsSaleIndexDao {

	@Resource
	private SlientGoodsSaleIndexMapper slientGoodsSaleIndexMapper;
	@Override 
	public int deleteSlientGoodsSaleIndex(Long id) {
		return slientGoodsSaleIndexMapper.deleteSlientGoodsSaleIndex(id);
	}

	@Override
	public int insertSlientGoodsSaleIndex(SlientGoodsSaleIndexDO record) {
		return slientGoodsSaleIndexMapper.insertSlientGoodsSaleIndex(record);
	}

	@Override
	public SlientGoodsSaleIndexDO selectSlientGoodsSaleIndexById(Long id) {
		return slientGoodsSaleIndexMapper.selectSlientGoodsSaleIndexById(id);
	}

	@Override
	public int updateSlientGoodsSaleIndex(SlientGoodsSaleIndexDO record) {
		return slientGoodsSaleIndexMapper.updateSlientGoodsSaleIndex(record);
	}

	@Override
	public int batchInsertSlientGoodsSaleIndex(JobShopDTO shop,Date date, List<SlientGoodsSaleIndexDO> slienGoodsLst) {
		if(CollectionUtils.isEmpty(slienGoodsLst)){
			return 0;
		}
		String tableName=CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date,TableEnum.PES_SLIENT_GOODS_SALE_INDEX.getName());
		return slientGoodsSaleIndexMapper.batchInsertSlientGoodsSaleIndex(slienGoodsLst, tableName);
	}

	@Override
	public int deleteSlientGoodsSaleIndexByShopIdByDate(JobShopDTO shop,Date startDate, Date endDate) {
		int row=0;
		List<DateRangeParam> tableNames=	CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_SLIENT_GOODS_SALE_INDEX.getName());
		for (DateRangeParam dateRangeParam : tableNames) {
			row+=slientGoodsSaleIndexMapper.deleteSlientGoodsSaleIndexByShopIdByDate(shop.getShopId(), dateRangeParam.getBeginDate(), dateRangeParam.getEndDate(), dateRangeParam.getTableName());
		}
		return row;
	}
	
}
