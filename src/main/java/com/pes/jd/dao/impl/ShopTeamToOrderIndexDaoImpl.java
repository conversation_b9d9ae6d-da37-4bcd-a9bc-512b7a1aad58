package com.pes.jd.dao.impl;

import com.pes.jd.dao.ShopTeamToOrderIndexDao;
import com.pes.jd.mapper.ShopTeamToOrderIndexMapper;
import com.pes.jd.model.DO.ShopTeamToOrderIndexDO;
import com.pes.jd.util.CommonUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @<NAME_EMAIL>
 * @date 2019/2/3 11:12 AM
 * @since 1.0.0
 */
@Repository
public class ShopTeamToOrderIndexDaoImpl implements ShopTeamToOrderIndexDao {

    @Resource
    private ShopTeamToOrderIndexMapper mapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return 0;
    }

    @Override
    public int insert(ShopTeamToOrderIndexDO record, String schemaId) {
        String tableName = CommonUtils.getTableName(schemaId,"pes_shop_team_to_order_index");
        return mapper.insert(record,tableName);
    }

    @Override
    public int insertSelective(ShopTeamToOrderIndexDO record) {
        return 0;
    }

    @Override
    public int deleteByShopIdDate(Long shopId, String schemaId, Date date) {
        String tableName = CommonUtils.getTableName(schemaId,"pes_shop_team_to_order_index");
        return mapper.deleteByShopIdDate(shopId,tableName,date);
    }

    @Override
    public ShopTeamToOrderIndexDO selectByPrimaryKey(Long id) {
        return null;
    }

    @Override
    public int updateByPrimaryKeySelective(ShopTeamToOrderIndexDO record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(ShopTeamToOrderIndexDO record) {
        return 0;
    }
}
