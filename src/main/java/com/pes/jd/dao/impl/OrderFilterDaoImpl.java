package com.pes.jd.dao.impl;

import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.OrderFilterDao;
import com.pes.jd.mapper.OrderFilterMapper;
import com.pes.jd.model.DO.OrderFilter;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.OrderFilterDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ClassName:OrderFilterDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:58:17 <br/>
 *
 * <AUTHOR>
 * @see
 * @since JDK 1.8
 */
@Repository
public class OrderFilterDaoImpl implements OrderFilterDao {

    @Resource
    private OrderFilterMapper orderFilterMapper;

    @Override
    public int deleteOrderFilterById(Long id) {
        return orderFilterMapper.deleteOrderFilterById(id);
    }

    @Override
    public int insertOrderFilter(OrderFilter record) {
        return orderFilterMapper.insertOrderFilter(record);
    }

    @Override
    public OrderFilter getOrderFilterById(Long id) {
        return orderFilterMapper.getOrderFilterById(id);
    }

    @Override
    public int updateOrderFilterBySelective(OrderFilter record) {
        return orderFilterMapper.updateOrderFilterBySelective(record);
    }

    @Override
    public int persistOrderFilterByFile(String filePath, JobShopDTO shop, JobDateQuery jobDate) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate.getDate(), TableEnum.PES_ORDER_FILTER.getName());
        return orderFilterMapper.persistOrderFilterByFile(filePath, tableName);
    }

    @Override
    public List<Long> selectIdsByOrderFilterIds(List<Long> oids, JobShopDTO shop, JobDateQuery jobDate) {
        if (CollectionUtils.isEmpty(oids)) {
            return null;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate.getDate(), TableEnum.PES_ORDER_FILTER.getName());
        List<Long> ids = orderFilterMapper.selectIdsByOrderFilterIds(oids, tableName, shop.getShopId());

        if (ids.size() != 0) {
            return ids;
        } else {
            return null;
        }
    }

    @Override
    public int deleteOrdersFilterByIds(List<Long> ids, JobShopDTO shop, JobDateQuery jobDate) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate.getDate(), TableEnum.PES_ORDER_FILTER.getName());
        return orderFilterMapper.deleteOrdersFilterByIds(ids, tableName, shop.getShopId());
    }

    @Override
    public int persistOrderFilters(List<OrderFilterDTO> orders, JobShopDTO shop, JobDateQuery jobDate) {
        if (CollectionUtils.isEmpty(orders)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), jobDate.getDate(), TableEnum.PES_ORDER_FILTER.getName());
        return orderFilterMapper.persistOrderFilters(orders, tableName);
    }

    @Override
    public List<OrderFilterDTO> queryPesOrdersByShopId(JobShopDTO shop, Date sDate, Date eDate) {
        List<OrderFilterDTO> orderFilters = new ArrayList<>();
        Date adjustDate = DateUtil.getDateByPeriod(sDate, CommonConstants.ORDER_FILTER_DELAY_DAYS);
        List<DateRangeParam> orderRefundTableName = CommonUtils.getTableNamesOfMonth(adjustDate, eDate, shop.getSchemaId(), TableEnum.PES_ORDER_FILTER.getName());
        for (DateRangeParam dateRangeParam : orderRefundTableName) {
            List<OrderFilterDTO> orderFilter = orderFilterMapper.queryPesOrdersByShopId(adjustDate, dateRangeParam.getEndDate(), dateRangeParam.getTableName(), shop.getShopId());
            if (CollectionUtils.isNotEmpty(orderFilter)) {
                orderFilters.addAll(orderFilter);
            }
        }
        return orderFilters;
    }

    @Override
    public List<Long> selectOrderFilterByOrderIds(List<Long> oids, JobShopDTO shop, Date date) {
        if (CollectionUtils.isEmpty(oids)) {
            return new ArrayList<>();
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_ORDER_FILTER.getName());
        return orderFilterMapper.selectOrderFilterByOrderIds(oids, tableName, shop.getShopId());
    }

}
