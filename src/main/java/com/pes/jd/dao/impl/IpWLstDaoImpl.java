
package com.pes.jd.dao.impl;

import com.pes.jd.dao.IpWLstDao;
import com.pes.jd.mapper.IpWLstMapper;
import com.pes.jd.model.DO.IpWLst;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * ClassName:IpWLstDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午5:34:56 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository
public class IpWLstDaoImpl implements IpWLstDao {

	@Resource
	private IpWLstMapper ipWLstMapper;

	@Override
	public int deleteIpWLstById(Integer id) {
		return ipWLstMapper.deleteIpWLstById(id);
	}

	@Override
	public int insertIpWLst(IpWLst record) {
		return ipWLstMapper.insertIpWLst(record);
	}

	@Override
	public IpWLst getIpWLstById(Integer id) {
		return ipWLstMapper.getIpWLstById(id);
	}

	@Override
	public int updateIpWLstBySelective(IpWLst record) {
		return ipWLstMapper.updateIpWLstBySelective(record);
	}

}
