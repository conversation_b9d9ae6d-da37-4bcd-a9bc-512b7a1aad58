package com.pes.jd.dao.impl;

import com.pes.jd.dao.CsRefundDayDao;
import com.pes.jd.mapper.CsRefundDayMapper;
import com.pes.jd.model.DTO.CsRefundDayDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class CsRefundDayDaoImpl implements CsRefundDayDao{

	@Resource
	private CsRefundDayMapper csRefundDayMapper;
	
	@Override
	public int batchInsertCsRefundDay(List<CsRefundDayDTO> csRefundDayLst, JobShopDTO shop, Date date) {
       if(CollectionUtils.isEmpty(csRefundDayLst)) {
    	   return 0;
       }
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), date, TableEnum.PES_CS_REFUND_DAY.getName());
	  return csRefundDayMapper.batchInsertCsRefundDay(csRefundDayLst, tableName);
	}

	@Override
	public int deltCsRefundDayByDateAndShopId(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableNameOfYear(shop.getSchemaId(), date, TableEnum.PES_CS_REFUND_DAY.getName());
		return csRefundDayMapper.deleteCsRefundDayByDateAndShopId(shop.getShopId(), tableName, date);
	}

}
