package com.pes.jd.dao.impl;

import com.pes.jd.dao.GoodsConsultSummaryDao;
import com.pes.jd.mapper.GoodsConsultSummaryMapper;
import com.pes.jd.model.DO.GoodsConsultSummaryDO;
import com.pes.jd.model.DO.GoodsConsultSummaryDOV2;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@Repository
public class GoodsConsultSummaryDaoImpl implements GoodsConsultSummaryDao {

	@Resource
	private GoodsConsultSummaryMapper goodsConsultSummaryMapper;
	@Override
	public int deleteGoodsConsultSummaryById(Long id) {
		return goodsConsultSummaryMapper.deleteGoodsConsultSummaryById(id);
	}

	@Override
	public int insertGoodsConsultSummary(GoodsConsultSummaryDO record) {
		return goodsConsultSummaryMapper.insertGoodsConsultSummary(record);
	}

	@Override
	public GoodsConsultSummaryDO selectGoodsConsultSummaryById(Long id) {
		return goodsConsultSummaryMapper.selectGoodsConsultSummaryById(id);
	}

	@Override
	public int updateGoodsConsultSummaryById(GoodsConsultSummaryDO record) {
		return goodsConsultSummaryMapper.updateGoodsConsultSummaryById(record);
	}

	@Override
	public int batchInsertGoodsConsultSummary(JobShopDTO shop,Date date, List<GoodsConsultSummaryDO> csRecommendGoodsLst) {
		if (CollectionUtils.isEmpty(csRecommendGoodsLst)) {
			return 0;
		}
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());

		return goodsConsultSummaryMapper.batchInsertGoodsConsultSummary(tableName, csRecommendGoodsLst);
	}

	@Override
	public int batchInsertGoodsConsultSummaryV2(JobShopDTO shop,Date date, List<GoodsConsultSummaryDOV2> csRecommendGoodsLst) {
		if (CollectionUtils.isEmpty(csRecommendGoodsLst)) {
			return 0;
		}
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_GOODS_CONSULT_SUMMARY_V2.getName());
		return goodsConsultSummaryMapper.batchInsertGoodsConsultSummaryV2(tableName, csRecommendGoodsLst);
	}

	@Override
	public int deleteGoodsConsultSummaryByShopIdAndByDate(JobShopDTO shop, Date startDate, Date endDate) {
		
		List<DateRangeParam> tableNames=CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY.getName());
		int rows = 0;
		for(DateRangeParam tableName:tableNames){
			rows+=goodsConsultSummaryMapper.deleteGoodsConsultSummaryByShopIdAndByDate(shop.getShopId(), tableName.getTableName(), tableName.getBeginDate(), tableName.getBeginDate());
		}
		return rows;
	}
	@Override
	public int deleteGoodsConsultSummaryByShopIdAndByDateV2(JobShopDTO shop, Date startDate, Date endDate) {

		List<DateRangeParam> tableNames=CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_CONSULT_SUMMARY_V2.getName());
		int rows = 0;
		for(DateRangeParam tableName:tableNames){
			rows += goodsConsultSummaryMapper.deleteGoodsConsultSummaryByShopIdAndByDateV2(shop.getShopId(), tableName.getTableName(), tableName.getBeginDate(), tableName.getBeginDate());
		}
		return rows;
	}
}
