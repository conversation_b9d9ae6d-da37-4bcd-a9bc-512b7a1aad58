package com.pes.jd.dao.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.CsChatSessionDao;
import com.pes.jd.mapper.CsChatSessionMapper;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DTO.CrossChatSessionDTO;
import com.pes.jd.model.DTO.CsChatSessionDTO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.CommonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

import static com.pes.jd.Constants.CommonConstants.BATCH_UPDATE_LIMIT_NUM;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/21 10:13 AM
 * @since 1.0.0
 */
@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
@Repository
public class CsChatSessionDaoImpl implements CsChatSessionDao {

    @Resource
    private CsChatSessionMapper mapper;

    @Override
    public int updateSetForwordFlag(Set<String> sid, Integer flag, String schema, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(schema, date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.updateSetForwordFlag(sid, flag, tableName);
    }

    @Override
    public int updateByPrimaryKeySelective(String schemaId, List<CsChatSessionDTO> record) {
        if (org.springframework.util.CollectionUtils.isEmpty(record)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(schemaId, record.get(0).getDate(), TableEnum.PES_CS_CHAT_SESSION.getName());
        List<List<CsChatSessionDTO>> lists = CollectionUtil.smallToLst(record, BATCH_UPDATE_LIMIT_NUM);
        for (List<CsChatSessionDTO> list : lists) {
            if (CollectionUtils.isNotEmpty(list)) {
                mapper.updateByPrimaryKeySelective(list, tableName);
            }

        }
        return record.size();
    }

    @Override
    public List<CsChatSessionDTO> searchAllByTime(Date beginDate, Date endDate, Long shopId, String schemaId, String nick) {

        return CommonUtils.tablesMerge(beginDate, endDate, schemaId, TableEnum.PES_CS_CHAT_SESSION.getName(), (x) ->
                mapper.searchAllByTime(x.getTableName(), x.getBeginDate(), x.getEndDate(), shopId, nick), CommonUtils.MergeType.MONTH);
    }

    @Override
    public int insertBatchCsChatSession(JobShopDTO shop, Date date, List<CsChatSessionDO> pesCsChatSessionLst) {
        if (CollectionUtils.isEmpty(pesCsChatSessionLst)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());

        return mapper.insertBatchCsChatSession(pesCsChatSessionLst, tableName);

    }

    @Override
    public int deleteChatSessionByShopIdAndDate(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.deleteChatSessionByShopIdAndDate(shop.getShopId(), date, tableName);
    }

    @Override
    public CsChatSessionDO getCsChatSessionBySidByDate(JobShopDTO shop, String sid, Date leaveMsgTime) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), leaveMsgTime, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.getCsChatSessionBySidByDate(shop.getShopId(), sid, tableName);
    }

    @Override
    public List<Integer> searchForWordInOutAndDirect(String schema, Long shopId, String nick, Date date, Integer sessionType) {
        String tableName = CommonUtils.getTableNameOfMonth(schema, date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.searchForWordInOutAndDirect(tableName, shopId, nick, date, sessionType);
    }

    @Override
    public List<String> selectReceiveCsChatSessionSidByDate(JobShopDTO shop, List<CsDTO> csLst, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.selectReceiveCsChatSessionSidByDate(tableName, shop.getShopId(), csLst, date);
    }

    @Override
    public List<CsChatSessionDTO> selectReceiveCsChatSessionLstByDate(JobShopDTO shop, List<CsDTO> csLst, Date date, Date newDate, Date endDate) {
        List<CsChatSessionDTO> csChatSessionDTOS = Lists.newArrayList();
        List<CommonUtils.DateRangeParam>  tableNames = CommonUtils.getTableNamesOfMonth(newDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName(), true);
        for (CommonUtils.DateRangeParam tableName : tableNames) {
            String name = tableName.getTableName();
            List<CsChatSessionDTO> list = mapper.selectReceiveCsChatSessionLstByDate(name, shop.getShopId(), csLst, newDate, endDate);
            if (CollectionUtils.isNotEmpty(list)) {
                csChatSessionDTOS.addAll(list);
            }
        }
        return csChatSessionDTOS;
    }

    @Override
    public List<CrossChatSessionDTO> selectCrossChatCsSessionByShopIdForEndTimeEqual(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.selectCrossChatCsSessionByShopIdForEndTimeEqual(shop.getShopId(), date, tableName);
    }

    @Override
    public List<CrossChatSessionDTO> selectCrossChatCsSessionByShopIdByDate(JobShopDTO shop, Date yesterday, Date targetDate) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), yesterday, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.selectCrossChatCsSessionByShopIdByDate(shop.getShopId(), yesterday, targetDate, tableName);

    }

    @Override
    public List<Long> selectChatSessionIdsByShopIdByDate(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.selectChatSessionIdsByShopIdByDate(shop.getShopId(), tableName, date);
    }

    @Override
    public int deleteChatSessionByIds(JobShopDTO shop, Date date, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.deleteChatSessionByIds(tableName, ids);
    }

    @Override
    public List<CrossChatSessionDTO> selectChatCsSessionByShopIdByDate(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_CS_CHAT_SESSION.getName());
        return mapper.selectChatCsSessionByShopIdByDate(shop.getShopId(), date, tableName);
    }

    @Override
    public List<String> searchForwordOutSidByTime(Date beginDate, Date endDate, Long shopId, String schemaId, String nick) {
        return CommonUtils.tablesMerge(beginDate, endDate, schemaId, TableEnum.PES_CS_CHAT_SESSION.getName(), (x) ->
                mapper.searchForwordOutSidByTime(x.getTableName(), x.getBeginDate(), x.getEndDate(), shopId, nick), CommonUtils.MergeType.MONTH);
    }

    @Override
    public List<String> searchSidByTimeForwordType(Date beginDate, Date endDate, Long shopId, String schemaId, String nick, Integer forwordType) {
        return CommonUtils.tablesMerge(beginDate, endDate, schemaId, TableEnum.PES_CS_CHAT_SESSION.getName(), (x) ->
                mapper.searchSidByTimeForwordType(x.getTableName(), x.getBeginDate(), x.getEndDate(), shopId, nick, forwordType), CommonUtils.MergeType.MONTH);
    }

    @Override
    public List<CsChatSessionDO> selectByShopIdAndDateAndSkuIds(JobShopDTO shop, Date start, Date end, Set<Long> skuIds) {
        List<CsChatSessionDO> sessions = Lists.newArrayList();
        if (shop == null || start == null || end == null || CollectionUtils.isEmpty(skuIds)) return sessions;

        List<CommonUtils.DateRangeParam> tables = CommonUtils.getTableNamesOfMonth(
                start, end, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName());
        for (CommonUtils.DateRangeParam table : tables) {
            List<CsChatSessionDO> dtos = mapper.selectByShopIdAndDateAndSkuIds(table.getTableName(),
                    shop.getShopId(), table.getBeginDate(), table.getEndDate(), skuIds);
            if (CollectionUtils.isNotEmpty(dtos)) sessions.addAll(dtos);
        }
        return sessions;
    }

    @Override
    public List<CsChatSessionDTO> selectEvaluationBySids(JobShopDTO shop, List<String> sids, Date startDate, Date endDate) {
        List<CsChatSessionDTO> csChatSessionDTOS = Lists.newArrayList();
        List<CommonUtils.DateRangeParam>  tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName(), true);
        for (CommonUtils.DateRangeParam tableName : tableNames) {
            String name = tableName.getTableName();
            List<CsChatSessionDTO> list = mapper.selectEvaluationBySids(name, shop.getShopId(), sids);
            if (CollectionUtils.isNotEmpty(list)) {
                csChatSessionDTOS.addAll(list);
            }
        }
        return csChatSessionDTOS;
    }

    @Override
    public List<CsChatSessionDTO> selectShopCsChatSessionLstByBuyerNickLstForConsultHandle(JobShopDTO shop, String csNick, List<String> buyerNickList, Date startDate,
                                                                                           Date endDate) {

        if(CollectionUtils.isEmpty(buyerNickList)){
            return Lists.newArrayList();
        }

        List<CsChatSessionDTO> result = Lists.newArrayList();
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName(),false);

        List<List<String>> buyerLst = CollectionUtil.smallToLst(buyerNickList, 600);
        for (CommonUtils.DateRangeParam drp : tableNames) {
            for (List<String> subLst : buyerLst) {
                List<CsChatSessionDTO> lst = mapper.selectShopCsChatSessionLstByBuyerNickLstForConsultHandle(shop.getShopId(), csNick, subLst, drp.getBeginDate(), drp.getEndDate(), drp.getTableName());
                result.addAll(lst);
            }
        }
        return result;
    }


    /**
     *
     * @param shop
     * @param csNick
     * @param buyerNick
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public CsChatSessionDO findLatestSessions(JobShopDTO shop, String csNick, String buyerNick, Date startDate, Date endDate) {

        List<CsChatSessionDO> result = Lists.newArrayList();
        if(StringUtils.isEmpty(csNick) || StringUtils.isEmpty(buyerNick)){
            return null;
        }
        List<CommonUtils.DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_CS_CHAT_SESSION.getName(),false);
        for (CommonUtils.DateRangeParam drp : tableNames) {
            CsChatSessionDO lst = mapper.findLatestSessions(shop.getShopId(), csNick,buyerNick, startDate,endDate,drp.getTableName());
            if(lst != null){
                result.add(lst);
            }
        }
        //找出result中sessionEndTime最大的那个
        CsChatSessionDO latestSession = result.stream()
                .filter(Objects::nonNull)
                .max(Comparator.comparing(CsChatSessionDO::getSessionEndTime))
                .orElse(null);



        return latestSession;
    }
}
