package com.pes.jd.dao.impl;

import com.pes.jd.dao.UserPortraitStatisticsDao;
import com.pes.jd.mapper.UserPortraitStatisticsMapper;
import com.pes.jd.model.DO.UserPortraitStatistics;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.ZoneId;
import java.util.Date;

@Repository
public class UserPortraitStatisticsDaoImpl implements UserPortraitStatisticsDao {

    @Autowired
    private UserPortraitStatisticsMapper userPortraitStatisticsMapper;


    @Override
    public int insertUserPortraitStatistics(UserPortraitStatistics statistics, String schemaId){
        // 使用statisticsDate字段转换为Date来生成年份分表
        Date statisticsDate = Date.from(statistics.getStatisticsDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        String tableName = CommonUtils.getTableNameOfYear(schemaId, statisticsDate, TableEnum.PES_USER_PORTRAIT_STATISTICS.getName());
        return userPortraitStatisticsMapper.insertUserPortraitStatistics(statistics, tableName);
    }

    @Override
    public int deleteUserPortraitStatistics(Long shopId, Date startDate, String schemaId) {
        String tableName = CommonUtils.getTableNameOfYear(schemaId, startDate, TableEnum.PES_USER_PORTRAIT_STATISTICS.getName());
        return userPortraitStatisticsMapper.deleteUserPortraitStatistics(shopId, startDate, tableName);
    }
}
