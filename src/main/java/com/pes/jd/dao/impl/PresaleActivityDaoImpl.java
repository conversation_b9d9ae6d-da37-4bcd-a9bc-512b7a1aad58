package com.pes.jd.dao.impl;

import com.pes.jd.dao.PresaleActivityDao;
import com.pes.jd.mapper.PresaleActivityMapper;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.PresaleActivityDTO;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class PresaleActivityDaoImpl implements PresaleActivityDao {

    @Resource
    private PresaleActivityMapper presaleActivityMapper;

    @Override
    public int deleteByShopId(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.deleteByShopId(tableName, shop.getShopId());
    }

    @Override
    public int deleteByActivityIdAndSkuId(JobShopDTO shop, Date date, List<PresaleActivityDTO> presaleActivityLst) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.deleteByActivityIdAndSkuId(tableName, presaleActivityLst);
    }

    @Override
    public int updateByActivityIdAndSkuId(JobShopDTO shop, Date date, List<PresaleActivityDTO> presaleActivityLst) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.updateByActivityIdAndSkuId(tableName, presaleActivityLst);
    }

    @Override
    public int updateByActivityIdAndStatus(JobShopDTO shop, Integer status, Date date) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(),"pes_presale_activity");
        return presaleActivityMapper.updateByActivityIdAndStatus(tableName, shop.getShopId(), status, date);
    }

    @Override
    public int batchInsert(JobShopDTO shop, Date date, List<PresaleActivityDTO> presaleActivityLst) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.batchInsert(tableName, presaleActivityLst);
    }

    @Override
    public List<PresaleActivityDTO> selectByShopIdAndDateForPresalePerformance(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.selectByShopIdAndDateForPresalePerformance(tableName, shop.getShopId(), date, DateUtil.getEndTimeOfDate(date));
    }
    @Override
    public PresaleActivityDTO selectActivityPeriodByShopIdAndDateForPresalePerformance(JobShopDTO shop, Date date) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.selectActivityPeriodByShopIdAndDateForPresalePerformance(tableName, shop.getShopId(), date, DateUtil.getEndTimeOfDate(date));
    }

    @Override
    public Integer selectCountByShopIdAndDate(JobShopDTO shop, Date startDate, Date endDate) {
        String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_presale_activity");
        return presaleActivityMapper.selectCountByShopIdAndDate(tableName, shop.getShopId(), startDate, endDate);
    }
}
