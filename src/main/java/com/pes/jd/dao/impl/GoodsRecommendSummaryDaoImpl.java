package com.pes.jd.dao.impl;

import com.pes.jd.dao.GoodsRecommendSummaryDao;
import com.pes.jd.mapper.GoodsRecommendSummaryMapper;
import com.pes.jd.model.DO.GoodsRecommendSummaryDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
@Repository
public class GoodsRecommendSummaryDaoImpl implements GoodsRecommendSummaryDao {

	@Resource
	private GoodsRecommendSummaryMapper goodsRecommendSummaryMapper;
	
	@Override
	public int deleteGoodsRecommendSummaryById(Long id) {
		return goodsRecommendSummaryMapper.deleteGoodsRecommendSummaryById(id);
	}

	@Override
	public int insertGoodsRecommendSummary(GoodsRecommendSummaryDO record) {
		return goodsRecommendSummaryMapper.insertGoodsRecommendSummary(record);
	}

	@Override
	public GoodsRecommendSummaryDO selectGoodsRecommendSummaryById(Long id) {
		return goodsRecommendSummaryMapper.selectGoodsRecommendSummaryById(id);
	}

	@Override
	public int updateGoodsRecommendSummary(GoodsRecommendSummaryDO record) {
		return goodsRecommendSummaryMapper.updateGoodsRecommendSummary(record);
	}

	@Override
	public int batchInsertGoodsRecommendSummary(JobShopDTO shop,Date date,
			List<GoodsRecommendSummaryDO> goodsRecommendSummaryLst) {
		if(CollectionUtils.isEmpty(goodsRecommendSummaryLst)){
			return 0;
		}
		String tableName=CommonUtils.getTableNameOfMonth(shop.getSchemaId(),date, TableEnum.PES_GOODS_RECOMMEND_SUMMARY.getName());
		
		return goodsRecommendSummaryMapper.batchInsertGoodsRecommendSummary(tableName, goodsRecommendSummaryLst);
	}

	@Override
	public int deleteGoodsRecommendSummaryByShopIdAndByDate(JobShopDTO shop, Date startDate, Date endDate) {
		
		List<DateRangeParam> tableNames=CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), TableEnum.PES_GOODS_RECOMMEND_SUMMARY.getName());
		int rows = 0;
		for(DateRangeParam tableName:tableNames){
			rows+=goodsRecommendSummaryMapper.deleteGoodsRecommendSummaryByShopIdAndByDate(shop.getShopId(), tableName.getTableName(), tableName.getBeginDate(), tableName.getEndDate());
		}
		return rows;
	}

}
