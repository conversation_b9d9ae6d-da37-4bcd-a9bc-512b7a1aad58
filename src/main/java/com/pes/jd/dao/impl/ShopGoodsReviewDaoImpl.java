package com.pes.jd.dao.impl;

import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.ShopGoodsReviewDao;
import com.pes.jd.mapper.ShopGoodsReviewMapper;
import com.pes.jd.model.DO.ShopGoodsReviewDO;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopGoodsReviewDTO;
import com.pes.jd.model.Enum.TableEnum;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class ShopGoodsReviewDaoImpl implements ShopGoodsReviewDao {

	@Resource
	private ShopGoodsReviewMapper shopGoodsReviewMapper;
	
	@Override
	public int insertShopGoodsReviewList(JobShopDTO shop, Date date, List<ShopGoodsReviewDO> goodsReviewDOLst) {
		if(CollectionUtils.isEmpty(goodsReviewDOLst)){
			return 0;
		}
		List<List<ShopGoodsReviewDO>> smallToLst = CollectionUtil.smallToLst(goodsReviewDOLst, CommonConstants.BATCH_INSERT_LIMIT_NUM);
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date,TableEnum.PES_ORDER_SKU_EVALUATE.getName());
		int insertNum = 0;
		for (List<ShopGoodsReviewDO> list : smallToLst) {
			insertNum += shopGoodsReviewMapper.insertShopGoodsReviewList(list, tableName);
		}
		return insertNum;
	}

	@Override
	public int deleteShopGoodsReviewByDateByShopId(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, TableEnum.PES_ORDER_SKU_EVALUATE.getName());
		return shopGoodsReviewMapper.deleteShopGoodsReviewByDateByShopId(shop.getShopId(), DateUtil.getStartTimeOfDate(date), DateUtil.getEndTimeOfDate(date),
				tableName);
	}

	@Override
	public List<ShopGoodsReviewDTO> selectShopCsReviewByOrderId(JobShopDTO shop, Date date, List<ShopGoodsReviewDTO> csGoodsReviewLst) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, "pes_order_sku_evaluate");
		return shopGoodsReviewMapper.selectShopCsReviewByOrderId(shop.getShopId(),csGoodsReviewLst,tableName);
	}

	@Override
	public List<ShopGoodsReviewDTO> selectShopCsOrderByCsBySendTime(JobShopDTO shop, List<CsDTO> csLst, Date date) {
		String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), date, "pes_order_sku_evaluate");
		return shopGoodsReviewMapper.selectShopCsOrderByCsBySendTime(shop.getShopId(),DateUtil.getStartTimeOfDate(date),DateUtil.getEndTimeOfDate(date),csLst,tableName);
	}

	@Override
	public List<ShopGoodsReviewDO> selectShopCsOrderBySendTime(JobShopQuery jobShop, Date date) {
		String tableName = CommonUtils.getTableNameOfMonth(jobShop.getShop().getSchemaId(), date, "pes_order_sku_evaluate");
		return shopGoodsReviewMapper.selectShopCsOrderBySendTime(jobShop.getShop().getShopId(),DateUtil.getStartTimeOfDate(date),DateUtil.getEndTimeOfDate(date),tableName);
	}

}
