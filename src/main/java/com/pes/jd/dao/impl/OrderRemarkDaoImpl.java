package com.pes.jd.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.pes.jd.dao.OrderRemarkDao;
import com.pes.jd.mapper.OrderRemarkMapper;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.OrderRemarkDTO;
import com.pes.jd.model.Query.ValidDateRangeQuery;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Repository
public class OrderRemarkDaoImpl implements OrderRemarkDao {

	@Resource
	private OrderRemarkMapper orderRemarkMapper;
	
	@Override
	public int batchInsertOrderRemark(JobShopDTO shop, Date date, List<OrderRemarkDTO> orderRemarkList) {
		if (CollectionUtils.isEmpty(orderRemarkList)) {
			return 0;
		}
		int batchInsertOrderRemark = 0;
		long s = System.currentTimeMillis();
		Map<String, List<OrderRemarkDTO>> orderRemarkMap = Maps.newHashMap();
		for (OrderRemarkDTO orderRemark : orderRemarkList) {
			Date created = orderRemark.getCreated();
			String tableName = CommonUtils.getTableNameOfMonth(shop.getSchemaId(), created, "pes_order_remark");
			if (orderRemarkMap.containsKey(tableName)) {
				orderRemarkMap.get(tableName).add(orderRemark);
			} else {
				List<OrderRemarkDTO> orLst = Lists.newArrayList();
				orLst.add(orderRemark);
				orderRemarkMap.put(tableName, orLst);
			}
		}
		long e = System.currentTimeMillis();
		System.out.println("订单备注分组耗时：{"+(e-s)+"}");
		s = System.currentTimeMillis();
		for (Entry<String, List<OrderRemarkDTO>> orderRemarkEntry : orderRemarkMap.entrySet()) {
			String tableName = orderRemarkEntry.getKey();
			List<OrderRemarkDTO> remarkList = orderRemarkEntry.getValue();
			batchInsertOrderRemark += orderRemarkMapper.batchInsertOrderRemark(shop.getShopId(), remarkList, tableName);
		}
		e = System.currentTimeMillis();
		System.out.println("订单备注插入到数据库耗时：{"+(e-s)+"}");
		return batchInsertOrderRemark;
	}

	@Override
	public int batchDeleteOrderRemarkByShopByDate(JobShopDTO shop, Date startDate, Date endDate) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_order_remark");
		return orderRemarkMapper.batchDeleteOrderRemarkByShopByDate(shop.getShopId(),startDate,endDate,tableName);
	}

	@Override
	public List<Long> selectOrderRemarkByOrderIdList(JobShopDTO shop, List<Long> orderIdList, Date startDate,
			Date endDate) {
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), "pes_order_remark");
		List<Long> retOrderIds = Lists.newArrayList();
		for (DateRangeParam tableNameParam : tableNames) {
			List<Long> orderIds = orderRemarkMapper.selectOrderRemarkByOrderIdList(shop.getShopId(),orderIdList,tableNameParam.getBeginDate(),tableNameParam.getEndDate(),tableNameParam.getTableName());
			if(CollectionUtils.isNotEmpty(orderIds)){
				retOrderIds.addAll(orderIds);
			}
		}
		return retOrderIds;
	}

	@Override
	public int updateOrderRemarkByObjList(JobShopDTO shop, List<OrderRemarkDTO> repeatOrderRemarkList, Date startDate,
			Date endDate) {
		if(CollectionUtils.isEmpty(repeatOrderRemarkList)){
			return 0;
		}
		int num = 0;
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), "pes_order_remark");
		for (DateRangeParam tableNameParam : tableNames) {
			num += orderRemarkMapper.updateOrderRemarkByObjList(shop.getShopId(),repeatOrderRemarkList,tableNameParam.getBeginDate(),tableNameParam.getEndDate(),tableNameParam.getTableName());
		}
		return num;
	}

	@Override
	public List<OrderRemarkDTO> selectOrderRemarkInfoByOrderIdList(JobShopDTO shop, List<Long> orderIdList,
			ValidDateRangeQuery validDate) {
		if(CollectionUtil.isEmpty(orderIdList)){
			return new ArrayList<>(0);
		}
		List<OrderRemarkDTO> totalOrderRemarkList = new ArrayList<>(orderIdList.size());
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(validDate.getAdjustOrderReMarkStartDate(), validDate.getAdjustOrderReMarkEndDate(), shop.getSchemaId(), "pes_order_remark");
		for (DateRangeParam tableNameParam : tableNames) {
			List<OrderRemarkDTO> orderRemarkList = orderRemarkMapper.selectOrderRemarkInfoByOrderIdList(shop.getShopId(), orderIdList,tableNameParam.getTableName());
			if(CollectionUtils.isNotEmpty(orderRemarkList)) {
				totalOrderRemarkList.addAll(orderRemarkList);	
			}
		  }
		return totalOrderRemarkList;
	}

}
  
