package com.pes.jd.dao.impl;

import com.pes.jd.dao.RoleDao;
import com.pes.jd.mapper.RoleMapper;
import com.pes.jd.model.DO.Role;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * ClassName:RoleDaoImpl <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 下午6:23:56 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
@Repository
public class RoleDaoImpl implements RoleDao {

	@Resource
	private RoleMapper roleMapper;

	@Override
	public int deleteRoleById(Long id) {
		return roleMapper.deleteRoleById(id);
	}

	@Override
	public int insertRole(Role record) {
		return roleMapper.insertRole(record);
	}

	@Override
	public Role getRoleById(Long id) {
		return roleMapper.getRoleById(id);
	}

	@Override
	public int updateRoleBySelective(Role record) {
		return roleMapper.updateRoleBySelective(record);
	}

}
