package com.pes.jd.dao.impl;

import com.google.common.collect.Lists;
import com.pes.jd.dao.ShopTeamPerformanceDao;
import com.pes.jd.mapper.ShopTeamPerformanceMapper;
import com.pes.jd.model.DO.ShopTeamPerformanceDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopTeamPerformanceDTO;
import com.pes.jd.util.CommonUtils;
import com.pes.jd.util.CommonUtils.DateRangeParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Repository
public class ShopTeamPerformanceDaoImpl implements ShopTeamPerformanceDao {

	@Resource
	private ShopTeamPerformanceMapper shopTeamPerformanceMapper;

	@Override
	public int insertShopTeamPerformance(JobShopDTO shop, Date date, ShopTeamPerformanceDO record) {
		List<ShopTeamPerformanceDO> recordLst = Lists.newArrayListWithExpectedSize(1);
		recordLst.add(record);
		return this.insertShopTeamPerformances(shop, date, recordLst);
	}
	
	@Override
	public int insertShopTeamPerformances(JobShopDTO shop, Date date, List<ShopTeamPerformanceDO> recordLst) {
		if(CollectionUtils.isEmpty(recordLst)){
			return 0;
		}
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_team_performance");
		return shopTeamPerformanceMapper.insertShopTeamPerformances(recordLst, tableName);
	}
	
	@Override
	public int deleteShopTeamPerformance(JobShopDTO shop, Date date) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_team_performance");
		return shopTeamPerformanceMapper.deleteShopTeamPerformance(shop.getShopId(), date, tableName);
	}
	
	@Override
	public int deleteShopTeamPerformances(JobShopDTO shop, Date startDate, Date endDate) {
		
		int rows = 0;
		List<DateRangeParam> tableNames = CommonUtils.getTableNamesOfMonth(startDate, endDate, shop.getSchemaId(), "pes_shop_team_performance");
		for (DateRangeParam drp : tableNames) {
			String tableName = drp.getTableName();
			rows += shopTeamPerformanceMapper.deleteShopTeamPerformances(shop.getShopId(), drp.getBeginDate(), drp.getEndDate(), tableName);
		}
		return rows;
	}

	@Override
	public ShopTeamPerformanceDTO getShopTeamPerformanceById(JobShopDTO shop, Long id) {
		String tableName = CommonUtils.getTableName(shop.getSchemaId(), "pes_shop_team_performance");
		return shopTeamPerformanceMapper.getShopTeamPerformanceById(shop.getShopId(), id, tableName);
	}


}
