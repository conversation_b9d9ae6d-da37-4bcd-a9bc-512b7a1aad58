package com.pes.jd.dao;

import com.pes.jd.model.DO.CsrReplyQualityDO;
import com.pes.jd.model.DTO.JobShopDTO;

import java.util.Date;
import java.util.List;

public interface CsReplyQualityDao {
	
	public int insertCsrReplyQuality(CsrReplyQualityDO csrReplyQuality);
	
	public int batchInsertCsrReplyQuality(JobShopDTO shop, List<CsrReplyQualityDO> csReplyQualityLst);
	
	public int deleteCsrReplyQualityById(Long id);
	
	public int deleteCsrReplyQualityByDate(JobShopDTO shop, Date startDate, Date endDate);
	
	public CsrReplyQualityDO getCsrReplyQualityById(Long id);
	
	public int updateCsrReplyQualityById(CsrReplyQualityDO csrReplyQuality);



}
  
