package com.pes.jd.dao;

import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopCategoryDTO;

import java.util.List;

public interface ShopCategoryDao {

	int batchInsertShopCategory(JobShopDTO shop,
			List<ShopCategoryDTO> shopCategoryLst);

	int deleteByShopCategoryTableName(JobShopDTO shop);

	int batchInsertShopCategoryV2(JobShopDTO shop, List<ShopCategoryDTO> shopCategoryLst);

	int deleteByShopCategoryTableNameV2(JobShopDTO shop);

	List<Long> selectMissingCategory(JobShopDTO shop);
}
