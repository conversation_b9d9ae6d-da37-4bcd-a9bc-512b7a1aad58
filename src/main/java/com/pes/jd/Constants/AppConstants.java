package com.pes.jd.Constants;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
public class AppConstants {

	public static String APP_KEY;
	public static String APP_SECRET;
	public static String REDIRECT_URL;
	public static String TOKEN_OAUTH_URL;

	public static String SERVER_ITEM_CODE;
	public static String SERVER_PIN;
	public static String SERVER_URL;
	public static String SERVER_URL_DEV;

	public static String SERVER_URL_LOG;

	@Value("${app.serverUrlLog}")
	public void setServerUrlLog(String serverUrlLog) {
		SERVER_URL_LOG = serverUrlLog;
	}

	@Value("${app.appKey}")
	public void setAppKey(String appKey) {
		AppConstants.APP_KEY = appKey;
	}

	@Value("${app.appSecret}")
	public void setAppSecret(String appSecret) {
		AppConstants.APP_SECRET = appSecret;
	}

	@Value("${app.redirectUrl}")
	public void setRedirectUrl(String redirectUrl) {
		AppConstants.REDIRECT_URL = redirectUrl;
	}

	@Value("${app.tokenOauthUrl}")
	public void setTokenOauthUrl(String tokenOauthUrl) {
		AppConstants.TOKEN_OAUTH_URL = tokenOauthUrl;
	}

	@Value("${app.serverItemCode}")
	public void setServerItemCode(String serverItemCode) {
		SERVER_ITEM_CODE = serverItemCode;
	}

	@Value("${app.serverPin}")
	public void setServerPin(String serverPin) {
		SERVER_PIN = serverPin;
	}

	@Value("${app.serverUrl}")
	public void setServerUrl(String serverUrl) {
		SERVER_URL = serverUrl;
	}

	@Value("${app.serverUrlDev}")
	public void setServerUrlDev(String serverUrlDev) {
		SERVER_URL_DEV = serverUrlDev;
	}
}
