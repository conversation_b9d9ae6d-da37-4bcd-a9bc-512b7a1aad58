package com.pes.jd.Constants;

/**
 * @Author: aiJun
 * @Date: 2020-11-20 13:04
 * @Version 1.0
 */
public enum GoodFilterEnum {

    FILTER_ORDERIDS_OF_FINAL("filterOrderIdsOfFinal","需要过滤的订单，订单中所有sku过滤，该订单才会被过滤"),
    NOT_FILTER_ORDER_DETAIL("notFilterOrderDetail","需要过滤的订单，订单中所有sku过滤，该订单才会被过滤");
    private String key;
    private String desc;

    GoodFilterEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
