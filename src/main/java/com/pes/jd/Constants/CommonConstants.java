
package com.pes.jd.Constants;
/**
 * ClassName:CommonConstants <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:   TODO ADD REASON. <br/>
 * Date:     2018年10月24日 下午1:43:53 <br/>
 * <AUTHOR>
 * @version
 * @since    JDK 1.8
 * @see
 */
public final class CommonConstants {

	private CommonConstants() {}
	public static final int RECALLAPI_TIMES = 5;
	public static final int ORDER_MESSAGE_RECALLAPI_TIMES = 5;
	public static final int ORDER_MESSAGE_RECALLAPI_TIMES_OUT = 2;
	public static final String LOAD_DATA_INFILE_SEPARATOR = "``MYPES`";
	public static final String LOAD_DATA_INFILE_LINE_END = "`MYPES`\n";
	public static final int MIN_THREAD_CHATPEER_NUM =300;
	public static final int BATCH_INSERT_LIMIT_NUM = 600;
	public static final int BATCH_UPDATE_LIMIT_NUM = 600;
	public static final int JOB_CS_NUM = 1; //跑job每次循环的客服数，0或复数 为一次跑完所有客服
	public static final int JOB_CS_NUM_TEN = 10;
	public static final String DEFAULT = "0000-00-00 00:00:00";
	public static final String SUB_USER_TYPE_MANAGE = "M";// 店铺管理员
	public static final String SUB_USER_TYPE_NO = "N";// 普通子账号
	public static final String SPLIT_SCHEMA_DATETIME = "2016-06-15";
	public static final String SPLIT_SCHEMA_YYYY_MM = "2016_06";
	public static final String ORIGIN = "origin";
	public static final String SCHEMA = "schema";
	// 评价订单往前查询天数
	public static final int DAY_EVAL_ORDER = -180;
	public static final int BATCH_DELETE_LIMIT_NUM = 8000;
	public static final int BATCH_DELETE_LIMIT_NUM_800 = 800;
	public static final int FORWARD_NON = 0;
	public static final int FORWARD_IN = 1;
	public static final int FORWARD_OUT = 2;
	public static final int EVALUATION_SCORE_BAD_1 = 1;//差评
	public static final int EVALUATION_SCORE_NEUTRAL_2 = 2;//中评
	public static final int EVALUATION_SCORE_NEUTRAL_3 = 3;//中评
	public static final int EVALUATION_SCORE_GOOD_4 = 4;//好评
	public static final int EVALUATION_SCORE_GOOD_5 = 5;//好评
	public static final int CS_EVALUATION_VERY_DISSATISFIED = 0;//非常不满意
	public static final int CS_EVALUATION_DISSATISFIED = 25;//不满意
	public static final int CS_EVALUATION_GENERAL = 50;//一般
	public static final int CS_EVALUATION_SATISFIED = 75;//满意
	public static final int CS_EVALUATION_VERY_SATISFIED = 100;//非常满意
	public static final String ORDER_EVALUATION_TYPE_PRE = "售前";
	public static final String ORDER_EVALUATION_TYPE_BET = "售中";
	public static final String ORDER_EVALUATION_TYPE_AFTER = "售后";
	/**
	 * 下单流失类型
	 */
	public static final int LOSS_TYPE_ENQUIRY_ORDER = 1;//1:询单付款流失
	public static final int LOSS_TYPE_SILENT_ORDER = 2;//2：静默付款流失
	public static final int LOSS_TYPE_OUT_STOCK = 3;//3：出库流失
	public static final int LOSS_TYPE_PAY_MENT = 4;//4：催付流失

	/**
	 * 店铺&&客服流失统计类型
	 */
	public static final int LOSS_CS_ENQUIRY = 1;//1:询单流失
	public static final int LOSS_CS_ENQUIRY_ORDER = 3;//3：询单付款流失
	public static final int LOSS_CS_OUT_STOCK = 4;//4：出库流失
	public static final int ORDER_REFUND_DELAY_DAYS = -90;//退款推迟查询天数
	public static final int ORDER_REFUND_DELAY_DAYS_NEW = -180;//退款推迟查询天数
	public static final int ASC_ORDER_REFUND_DELAY_DAYS = -17;//售后退款补充数据的天数
	public static final int ORDER_REFUND_PUT_OFF_DAYS = -30;//售后退款补充数据的天数
	public static final int ORDER_FILTER_DELAY_DAYS = -45;//订单过滤表推得天数
	/**
	 * 流失备注类型
	 */
	public static final int NOTE_ENQUIRY_LOST = 1; // 询单流失
	public static final int NOTE_SILENT_LOST = 2; // 静默下单未付款流失
	public static final int NOTE_ENQUIRY_ORDER_LOSS = 3; // 静默下单未付款流失
	public static final int CUSTOMER_INITIATIVE_CHAT = 1; //客服非主动聊天
	public static final int CS_INITIATIVE_CHAT = 2; //客服主动聊天
	public static final Boolean YES_PES_ORDER = true; // 是绩效客服
	public static final Boolean NO_PES_ORDER = false; // 不是绩效客服
	//平台类型
	public static final String TP_TYPE = "pc,m,app,wx,sq,zgb,xpc";
	public static final String JDIMAGEURL = "http://img10.360buyimg.com/n0/";
	public static final int JOB_API_DATE_TYPE = 2; //实时绩效拉取传参，2：只能拉取到订购后的数据（拉取快），3：能拉取到所有数据（拉取慢）
	public static final int CS_TYPE_SALE_PRE = 1;//售前
	public static final int CS_TYPE_SALE_AFTER = 2;//售后
	public static final int CS_TYPE_SALE_ALL = 3;//所有
	public static final int CS_STATUS_NOT_LOCK = 1;//未锁定
	public static final int CS_STATUS_LOCK = 2;//锁定
	public static final int CS_STATUS_ALL = 3;//所有
	//----拉取sku相关
	public static final int PULL_SKUS_CREATED = 1;//通过创建时间拉取skus
	public static final int PULL_SKUS_MODIFIED = 2;//通过修改时间拉取skus
	//----拉取商品相关
	//商品状态,多个值属于[或]操作
	// 1:从未上架
	// 2:自主下架
	// 4:系统下架
	// 8:上架
	// 513:从未上架待审
	// 514:自主下架待审
	// 516:系统下架待审
	// 520:上架待审核
	// 1028:系统下架审核失败
	public static final int PULL_GOOD_NEVERON = 1;
	public static final int PULL_GOOD_INDEPENDENTFROMTHESHELVES = 2;
	public static final int PULL_GOOD_SYSTEMFROMTHESHELVES= 4;
	public static final int PULL_GOOD_SHELVES = 8;
	public static final int PULL_GOOD_ITSNEVERBEENONTHESHELVES= 513;
	public static final int PULL_GOOD_VOLUNTARYREMOVALPENDINGTRIAL= 514;
	public static final int PULL_GOOD_THESYSTEMISDOWNFORREVIEW = 516;
	public static final int PULL_GOOD_SHELFPENDINGAPPROVAL = 520;
	public static final int PULL_GOOD_SYSTEMTAKEDOWNAUDITFAILED = 1028;
	//----拉取sku相关
	public static final int PULL_GOOD_CREATED = 1;//通过创建时间拉取good
	public static final int PULL_GOOD_MODIFIED = 2;//修改时间
	public static final int PULL_GOOD_ONLINE_TIME = 3;//上架时间
	public static final int PULL_GOOD_OFFLINE_TIME = 4;//下架时间
    //jd拉取的最大跳过2万
	public static final int PULL_SKU_LIMIT_NUM = 20000;
	//jd拉取的最大跳过2万
	public static final int PULL_GOODS_LIMIT_NUM = 100000;
	public static final int SPLIT_LIMIT_NUM = 50;
	public static final int BATCH_INSERT_CHATLOG_LIMIT_NUM = 12000;
	//`forward_flag` tinyint(2) DEFAULT '0' COMMENT '转入：1,转出：2 ，非转发：0,转接:3 售前转发给售后客服:4',
	public static final int PRESALE_FORWORD2_AFTERSALECS = 4;
	//转接类型 0：直接接待\n               1：转入接待\n               2转出接待
	public static final int CHAT_SESSION_FORWARD_TYPE_INTO = 1;
	public static final int CHAT_SESSION_FORWARD_TYPE_OUT = 2;
	/**
	 * 会话类型 1：在线会话 2：留言
	 */
	public static final Integer CHAT_SESSION_TYPE_ONLINE = 1;
	public static final Integer CHAT_SESSION_TYPE_LEAVEMESSAGE = 2;
	/**
	 * 警告类型
	 */
	public static final Byte WARNING_CS_VIOLATION = 2; //2-客服违规
	public static final Byte WARNING_BUYER_RAIL = 3; //3-顾客辱骂
	/**
	 * 情感分析状态
	 */
	public static final Byte SENTIMENT_UNPROCESSED_STATUS = 1; //状态：1：待处理
	public static final Byte SENTIMENT_PROCESSED_STATUS = 2; //状态：2：已处理
	/**
	 * 情感分析类型
	 */
	public static final Byte SENTIMENT_DEFAULT_TYPE = 0; //类型：0：默认
	public static final Byte SENTIMENT_POSITIVE_TYPE = 1; //类型：1：正面
	public static final Byte SENTIMENT_NEGATIVE_TYPE = 2; //类型：2：负面
	//差分订单大小
	public static final int ORDER_DATA_SIZE = 400;
	public static final int BATCH_SELECT_LIMIT_NUM = 10000;
	public static final int BATCH_THREAD_LIMIT_NUM = 5000;
//分隔符
	public static final String DELIMITER = "&&&";
	//处理预售拆单限制数
	public static final int BATCH_HANDLE_PRESALE_ORDER_LIMIT_NUM_500 = 500;

	//计算出库金额时默认往前推14天
	public static final int OUT_STOCK_DAY = 14;

	public static final int JUDGE_ASSIT_ORDER_CREATE = 1;

	public static final int JUDGE_ASSIT_ORDER_PAY = 2;

	public static final int REFUND_PULL_CAL_DATE = 2;
	//买家说话
	public static final Integer BUYER_SAY =1;

	//店铺类型 1:自营 2.pop
	public static final int SHOP_TYPE_SELF = 1;
}
