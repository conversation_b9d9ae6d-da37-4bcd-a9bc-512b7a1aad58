/**
 * Project Name:customerservicePES File Name:ApiRpCodeConstants.java Package
 * Name:com.customerservicePES.constants Date:2018年7月19日上午11:25:05 Copyright (c)
 * 2018, <EMAIL> All Rights Reserved.
 */

package com.pes.jd.Constants;

/**
 * 前两个字符是 模块的拼音首字符 例如SY - 首页，
 * 接着后面两个字符是子模块 00,01
 * 最后连个字符是具体的错误码 递增
 * ClassName:ApiRpCodeConstants <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年7月19日 上午11:25:05 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 * @see
 */
public final class APICodeConstants {


    //成功code，比如报表查询
    public static final String CODE_SUCCESS_1001 = "1001";
    //成功code，需要后续操作，例如成功后，弹窗
    public static final String CODE_SUCCESS_1002 = "1002";
    //用于扩展....
    public static final String CODE_SUCCESS_1003 = "1003";


    //-------------------  Error common   ---------------------//
    public static final String CODE_ERROR_COMMON_DEFAULT = "Error-0000";
    public static final String CODE_ERROR_COMMON_01_01 = "Error-0101";
    public static final String CODE_ERROR_COMMON_DEMOTE = "Error-0102";
    public static final String CODE_ERROR_1001 = "E1001";
    //-------------------  系统设置   ---------------------//

    //咚咚设置 - 模块
    public static final String CODE_ERROR_XS_01_01 = "XS0101";
    //咚咚组设置 - 模块
    public static final String CODE_ERROR_XS_02_01 = "XS0201";
    //绩效设置 - 模块
    public static final String CODE_ERROR_XS_03_01 = "XS0301";
    //权限设置 - 模块
    public static final String CODE_ERROR_XS_04_01 = "XS0401";
    //店铺设置  - 模块
    public static final String CODE_ERROR_XS_05_01 = "XS0401";

    //-------------------  首页&初始化   ---------------------//

    //店铺 - 模块
    public static final String CODE_ERROR_SY_01_01 = "SY0101";
    //客服咚咚 - 模块
    public static final String CODE_ERROR_SY_02_01 = "SY0101";


    //-------------------  绩效中心    ---------------------//

    //实时绩效 - 模块
    public static final String CODE_ERROR_JZ_01_01 = "JZ0101";
    //店铺绩效 - 模块
    public static final String CODE_ERROR_JZ_02_01 = "JZ0201";
    //店铺绩效 - 模块
    public static final String CODE_ERROR_JZ_02_02 = "JZ0202";

    //客服绩效  - 模块
    public static final String CODE_ERROR_JZ_03_01 = "JZ0301";

    //-------------------  数据分析    ---------------------//

    //接待分析 - 模块
    public static final String CODE_ERROR_SF_01_01 = "SF0101";
    //成交分析 - 模块
    public static final String CODE_ERROR_SF_02_01 = "SF0201";
    //流失分析  - 模块
    public static final String CODE_ERROR_SF_03_01 = "SF0301";
    //登陆分析 - 模块
    public static final String CODE_ERROR_SF_04_01 = "SF0401";
    //商品过滤 - 模块
    public static final String CODE_ERROR_SF_05_01 = "SF0501";
    //聊天记录 - 模块
    public static final String CODE_ERROR_SF_06_01 = "SF0601";
    
    
    //出库时间
    public static final String CODE_ERROR_OS_01_01 = "OS0101";


}
