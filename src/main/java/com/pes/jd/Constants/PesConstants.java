
package com.pes.jd.Constants;

public final class PesConstants {
	
	
	/**
	 * 无法确定类型的绑定(处理询单人数=落实下单+询单流失：场景①跨天聊天的不算询单也不该算绑定)
	 */
	public static int ORDER_BIND_TYPE_DEFAULT = 0;
	/**
	 * 落实下单
	 */
	public static int ORDER_BIND_TYPE_ORDER = 1;
	/**
	 * 落实付款
	 */
	public static int ORDER_BIND_TYPE_PAY = 2;


	/**
	 * 全静默算绩效
	 */
	public static int ORDER_BIND_TYPE_SILENTALL = 3;

	/**
	 * 落实付尾款
	 */
	public static int ORDER_BIND_TYPE_BALANCE_PAY = 4;
	
	/**
	 * 按聊天时间判定归属
	 */
	public static int JUDGE_RULE_ASCRIPTION_TYPE_CHAT = 1;
	/**
	 *按回合数判定归属
	 */
	public static int JUDGE_RULE_ASCRIPTION_TYPE_ROUND = 2;
	
	/**
	 * 下单判定
	 */
	public static int JUDGE_RULE_ORDER = 1;
	/**
	 * 下单优先判定
	 */
	public static int JUDGE_RULE_ORDER_FIRST = 2;
	
	/**
	 * 付款判定
	 */
	public static int JUDGE_RULE_PAY = 3;
	/**
	 * 默认有效的询单时长为2天
	 */
	public static final Integer ENQUIRY_VALID_DURATION_TIME_DEFAULT = 2;
	
	/**
	 * 商品推荐/咨询付款延迟一天
	 */
	public static final Integer GOODS_PAY = 1;
	
	/**
	 * 跨天聊天一天
	 */
	public static final Integer CROSS_CHAT_DAY = 1;
	
	/**
	 * 货到付款
	 */
	public static final Integer ORDER_TO_PAY_DAY_CASHPAY = 7;
	
	/**
	 * 预售期
	 */
	public static final Integer ORDER_PRESALE_PAY_BALANCE_DATE = 90;


	/**
	 * 按退款完成时间询单时长-一年
	 */
	public static final Integer ORDER_REFUND_MODIFIED_DATE_NEW_YEAR = 365;

	/**
	 * 定金期
	 */
	public static final Integer ORDER_PRESALE_PAY_BARGAIN_DATE = 45;

	/**
	 * 按退款完成时间询单时长-新版90天
	 */
	public static final Integer ORDER_REFUND_MODIFIED_DATE_NINE = 90;
}
