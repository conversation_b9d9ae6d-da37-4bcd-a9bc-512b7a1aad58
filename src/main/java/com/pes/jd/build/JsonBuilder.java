package com.pes.jd.build;

import com.alibaba.fastjson.JSONObject;

public final class JsonBuilder {

	/**
     * 不能通过new初始化
     */
    private JsonBuilder(){}

    public static Builder builder(){
        return new Builder();
    }

    public static class Builder{
    	JSONObject json = new JSONObject();
        public Builder put(String key, Object value){
            this.json.put(key, value);
            return this;
        }

        public String toJsonString(){
        	return json.toJSONString();
        }
    }

}
