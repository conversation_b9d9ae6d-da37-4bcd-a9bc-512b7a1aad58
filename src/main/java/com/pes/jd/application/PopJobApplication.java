package com.pes.jd.application;

import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.pes.jd.Constants.AppConstants;
import com.pes.jd.util.SpringUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, MybatisPlusAutoConfiguration.class})
@EnableScheduling
@EnableAspectJAutoProxy
@ServletComponentScan //spring能够扫描到我们自己编写的servlet和filter。
@MapperScan({"com.pes.jd.mapper"})  //Mapper 接口路径
@ComponentScan(basePackages = {"com.pes.jd.mq.jcq.consumer", "com.pes.jd.business",
        "com.pes.jd.dao", "com.pes.jd.config", "com.pes.jd.rest",
        "com.pes.jd.data.converter","com.pes.jd.data.api", "com.pes.jd.controller",
        "com.pes.jd.redis", "com.pes.jd.mq.kafka.producer"})
@Import(value = {SpringUtil.class, AppConstants.class})
//@ImportResource(locations = { "classpath:druid-bean.xml" })
public class PopJobApplication {
	public static void main(String[] args) {
      SpringApplication.run(PopJobApplication.class, args);
    }


}
