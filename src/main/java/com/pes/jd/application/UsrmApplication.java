package com.pes.jd.application;


import com.pes.jd.constants.AppConstants;
import com.pes.jd.model.DO.PesUserServicePermission;
import org.apache.commons.beanutils.PropertyUtilsBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableScheduling
@ServletComponentScan //spring能够扫描到我们自己编写的servlet和filter。
@MapperScan({"com.pes.jd.mapper"})  //Mapper 接口路径
@ComponentScan(basePackages = {"com.pes.jd.business", "com.pes.jd.dao","com.pes.jd.sdk.wxpay",
        "com.pes.jd.config","com.pes.jd.data", "com.pes.jd.controller","com.pes.jd.framework",
        "com.pes.jd.constants", "com.pes.jd.rest", "com.pes.jd.sdk.jdydsms"})
@Import(value = {AppConstants.class})
public class UsrmApplication {

    public static void main(String[] args) {
        new PropertyUtilsBean().getPropertyDescriptors(PesUserServicePermission.class);
        SpringApplication app = new SpringApplication(UsrmApplication.class);
        ConfigurableApplicationContext context = app.run(args);

        ConfigurableEnvironment environment = context.getEnvironment();
        String version = environment.getProperty("app.version");
        System.out.println("==========>app.version:{}" + version);
        // fail-fast 项目启动检测mybatis 配置是否含有错误  后面开发完成可以去掉
        context.getBean(SqlSessionFactory.class).getConfiguration().getMappedStatementNames();
    }

}
