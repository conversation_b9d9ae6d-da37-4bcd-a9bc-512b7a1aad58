package com.pes.jd.application;

import com.pes.jd.util.SpringUtil;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.session.defaults.DefaultSqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.RestController;

import java.util.TimeZone;


@RestController
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableScheduling
@ServletComponentScan //spring能够扫描到我们自己编写的servlet和filter。
@MapperScan({"com.pes.jd.mapper"})  //Mapper 接口路径
@ComponentScan(basePackages = {"com.pes.jd.mq.jcq.consumer", "com.pes.jd.business",
        "com.pes.jd.dao", "com.pes.jd.config", "com.pes.jd.rest",
        "com.pes.jd.data", "com.pes.jd.controller", "com.pes.jd.framework", "com.pes.jd.oss",
"com.pes.jd.office.excel", "com.pes.jd.constants", "com.pes.jd.redis"})
@Import(value = {SpringUtil.class})
public class PopSubApplication {


	public static void main(String[] args) {
        SpringApplication app = new SpringApplication(PopSubApplication.class);
        final ConfigurableApplicationContext context = app.run(args);
        SqlSessionFactory bean = context.getBean(SqlSessionFactory.class);
        DefaultSqlSessionFactory bean1 = (DefaultSqlSessionFactory) bean;
        bean1.getConfiguration().getMappedStatements();
    }


    /**
     *  解决jackson时区问题，由于jackson解析date类型时将时区设置为UTC
     * @return
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jacksonObjectMapperCustomization() {
        return jacksonObjectMapperBuilder ->
                jacksonObjectMapperBuilder.timeZone(TimeZone.getDefault());
    }

}
