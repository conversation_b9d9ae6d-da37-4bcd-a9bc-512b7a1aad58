package com.pes.jd.task.executor;

import com.pes.jd.builder.RequestEntityBuilder;
import com.pes.jd.constants.APICodeConstants;
import com.pes.jd.model.DTO.ExportRecordDTO;
import com.pes.jd.model.Enum.ExportTaskEnum;
import com.pes.jd.model.Response.ApiResponse;
import com.pes.jd.model.Enum.ApplicationServiceNameEnum;
import com.pes.jd.oss.OssUploadUtils;
import com.pes.jd.rest.DispatchingRestTemplate;
import com.pes.jd.util.JacksonUtils;
import com.pes.jd.util.SpringUtil;
import com.yiyitech.support.rpc.RestOperator;
import com.yiyitech.support.rpc.RestResponseTypeRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;

public class ExeclExportExecutor implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ExeclExportExecutor.class);

    private String msg;

    public ExeclExportExecutor(final String msg) {
        this.msg = msg;
    }

    @Override
    public void run() {
        System.out.println("msg: " + msg);
        logger.info("msg: " + msg);
        ExportRecordDTO exportRecord;
        try {
            //解析参数
            exportRecord = JacksonUtils.json2pojo(msg, ExportRecordDTO.class);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("消息解释失败{}", msg, e);
            return;
        }

        ExportTaskEnum exportTaskEnum = ExportTaskEnum.getExportTaskEnum(exportRecord.getType());

        if (exportTaskEnum == null) {
            logger.error("报表类型不匹配，type={}", exportRecord.getType());
            updateExportRecord(exportRecord.getId(), 3, null);
            return;
        }

        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            //excel文件名
            String excelName = exportRecord.getName() + ".xlsx";
            //匹配导出报表 类与方法
            Class<?> bussinessClass = Class.forName(exportTaskEnum.getClassName());
            Method bussinessMethod = bussinessClass.getMethod(exportTaskEnum.getMethod(), OutputStream.class, String.class);
            Object bean = SpringUtil.getBean(bussinessClass);
            bussinessMethod.invoke(bean, baos, exportRecord.getCondition());

            //上传导出的excel 至 oss
            uploadToOss(baos.toByteArray(), exportRecord.getShopId() + "/" + excelName);
            //更新导出记录
            updateExportRecord(exportRecord.getId(), 2, exportRecord.getShopId() + "/" + excelName);

        } catch (Exception e) {
            logger.error("======导出excel--->消费异常 error : {}===", e.getMessage(), e);
            updateExportRecord(exportRecord.getId(), 3, "");
        }
        logger.info("---->消费成功 msg : {}===", msg);
    }

    /**
     * 上传到文件存储
     */
    private String uploadToOss(byte[] bytes, String ossName) {
        long start = System.currentTimeMillis();
        logger.info("导出excel文件 oss上传开始");
        String excelUrl = OssUploadUtils.ossUploadBytes(bytes, ossName);
        logger.info("导出excel文件 oss上传结束: " + (System.currentTimeMillis() - start) + "ms");
        logger.info("导出excel文件 在oss url ----> : " + excelUrl);
        return excelUrl;
    }

    /**
     * 更新导出记录
     */
    private void updateExportRecord(Long id, Integer status, String excelUrl) {
        for (int i = 0; i < 10; i++) {
            DispatchingRestTemplate dispatchingRestTemplate = SpringUtil.getBean(DispatchingRestTemplate.class);
            HttpEntity<Object> body = RequestEntityBuilder.builder()
                    .put("id", id)
                    .put("status", status)
                    .put("url", excelUrl)
                    .toRequestEntity();

            try {
                String serviceId = RestOperator.getRemoteServiceId(ApplicationServiceNameEnum.PROVIDER_TASK_SCHEDULING.getName());
                ApiResponse apiResponse = dispatchingRestTemplate.postRest(serviceId,"/task/export/updateExportUrl", body);
                if (apiResponse.getRpCode().equals(APICodeConstants.CODE_SUCCESS_1002)) break;
                Thread.sleep(10);
            } catch (Exception e) {
                logger.error("/task/export/updateExportUrl error:{}", e.getMessage(), e);
            }
        }
    }


}
