package com.pes.jd.task.executor;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.JobPriorityTaskBusiness;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Date;
import java.util.List;


public class TaskLoginLogDataByHoursJobExecutor implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(TaskLoginLogDataByHoursJobExecutor.class);

    private String msg;

    public TaskLoginLogDataByHoursJobExecutor(final String msg) {
        this.msg = msg;
    }

    @Override
    public void run() {

        logger.info("======开始消费-值班记录 {}===",msg);

        logger.info("msg: " + msg);
        try {
        	pullLoginLogDataByHours(msg);
        }catch (Throwable e){
            e.printStackTrace();
        }

        logger.info("=======消费结束-值班记录:{}",msg);
    }


    /**
     * 
     * @param body
     * @return
     */
    public void pullLoginLogDataByHours(String body) {


    	ShopManageBusiness shopManageBusiness = SpringUtil.getBean(ShopManageBusiness.class);
        JobPriorityTaskBusiness jobPriorityTaskBusiness = SpringUtil.getBean(JobPriorityTaskBusiness.class);

        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDateStr = (String) jo.get("startDateStr");
        String endDateStr = (String) jo.get("endDateStr");

        Date startDate;
        Date endDate;
        try {
            startDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDateStr));
            endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
        } catch (ParseException e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            return;
        }


        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if(dates.isEmpty()){
            return;
        }
        System.out.println("=============》 dates:"+dates);
        System.out.println("=============》 shopId:"+shopId);

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        System.out.println("duty log => job shop="+jobShop);
        int shopIndex = 1;
        boolean isDelData = true;
        for (Date date : dates) {
            try {
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                jobPriorityTaskBusiness.pullShopLoginLogDataByDelayHours(jobShop, jobDate, isDelData);
            }catch (Throwable e) {
                e.printStackTrace();
            }

        }

    }


}
