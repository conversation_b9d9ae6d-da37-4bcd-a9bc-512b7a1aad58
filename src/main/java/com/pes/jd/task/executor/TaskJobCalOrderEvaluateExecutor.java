package com.pes.jd.task.executor;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.JobPriorityTaskBusiness;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * @Author: aiJun
 * @Date: 2019-07-11 12:54
 * @Version 1.0
 */
public class TaskJobCalOrderEvaluateExecutor implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(TaskJobCalOrderEvaluateExecutor.class);
    private String msg;

    public TaskJobCalOrderEvaluateExecutor(String msg) {
        this.msg = msg;
    }

    @Override
    public void run() {
        logger.info("======开始消费-cal_order_evaluate {}===",msg);

        System.out.println("msg: " + msg);
        try {
            handleCalOrderEvaluateJob(msg);
        }catch (Throwable e){
            e.printStackTrace();
        }

        logger.info("=======消费结束-cal_order_evaluate:{}",msg);
    }

    private void handleCalOrderEvaluateJob(String body) {
        ShopManageBusiness shopManageBusiness = SpringUtil.getBean(ShopManageBusiness.class);
        JobPriorityTaskBusiness jobPriorityTaskBusiness = SpringUtil.getBean(JobPriorityTaskBusiness.class);

        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);

        String shopId = (String) jo.get("shopId");
        String startDateStr = (String) jo.get("startDateStr");
        String endDateStr = (String) jo.get("endDateStr");
        String type = (String) jo.get("type");

        Date startDate;
        Date endDate;
        try {
            startDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDateStr));
            endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
        } catch (ParseException e) {
            e.printStackTrace();
            return;
        }

        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if (dates.isEmpty()) {
            return;
        }
        System.out.println("=============》 dates:" + dates);

        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);

        boolean isDelData = true;

        /*********************************************************/
        System.out.println("evaluate => cal_order_evaluate shop=" + jobShop);
        int shopIndex = 1;
        for (Date date : dates) {
            try {
                JobDateQuery jobDate = new JobDateQuery(date);
                jobDate.setStartDate(DateUtil.getStartTimeOfDate(date));
                jobDate.setEndDate(DateUtil.getEndTimeOfDate(date));
                jobPriorityTaskBusiness.calOrderEvaluate(jobShop, jobDate, isDelData);
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }
        /*********************************************************/
    }
}
