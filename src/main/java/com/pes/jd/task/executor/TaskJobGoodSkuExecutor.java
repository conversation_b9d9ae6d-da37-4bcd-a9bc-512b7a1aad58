package com.pes.jd.task.executor;

import com.alibaba.fastjson.JSONObject;
import com.pes.jd.business.ShopGoodSkuHandleBussiness;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.model.Param.GoodskuParam;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.Date;
import java.util.List;


public class TaskJobGoodSkuExecutor implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(TaskJobGoodSkuExecutor.class);

    private String msg;

    public TaskJobGoodSkuExecutor(final String msg) {
        this.msg = msg;
    }

    @Override
    public void run() {

        logger.info("======开始消费-goodSku记录 {}===",msg);

        System.out.println("msg: " + msg);
        try {
        	pullGoodSkuByGoodskuParam(msg);
        }catch (Throwable e){
            e.printStackTrace();
        }

        logger.info("=======消费结束-goodSku记录:{}",msg);
    }

    private void pullGoodSkuByGoodskuParam(String body) {
        ShopManageBusiness shopManageBusiness = SpringUtil.getBean(ShopManageBusiness.class);
        ShopGoodSkuHandleBussiness ShopGoodSkuHandleBussiness = SpringUtil.getBean(ShopGoodSkuHandleBussiness.class);
        JSONObject jo = JSONObject.parseObject(body);
        System.out.println(jo);
        String shopId = (String) jo.get("shopId");
        String startDateStr = (String) jo.get("startDateStr");
        String endDateStr = (String) jo.get("endDateStr");
        String type = (String) jo.get("type");

        Date startDate;
        Date endDate;
        try {
            startDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDateStr));
            endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
        } catch (ParseException e) {
            e.printStackTrace();
            return;
        }

        List<Date> dates = DateUtil.splitDate(startDate, endDate);
        if (dates.isEmpty()) {
            return;
        }
        System.out.println("=============》 dates:" + dates);
        System.out.println("=============》 shopId:" + shopId);
        JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
        logger.info("pull good sku  => job shopTitle={} start", jobShop.getShop().getTitle());
        boolean isDelData = true;
        try {
            GoodskuParam goodskuParamPojo;
            for (Date date : dates) {
                goodskuParamPojo = new GoodskuParam(Long.valueOf(shopId), DateFormatUtils.formatYMd(date), DateFormatUtils.formatYMd(date), type);
                ShopGoodSkuHandleBussiness.pullGoodSkus(jobShop, goodskuParamPojo, isDelData);
            }
            logger.info("pull good sku  => job shopTitle={} end", jobShop.getShop().getTitle());
        } catch (Exception e) {
            logger.error("pull good sku  => job shopTitle={} fail", jobShop.getShop().getTitle());
            logger.error(e.getMessage(), e);
        }
    }

}
