package com.pes.jd.task.executor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.ReserveActivityTO;
import com.pes.jd.ms.domain.Data.job.ReserveInfoVO;
import com.pes.jd.util.HttpClientUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;

public class FetchReserveActivityExecutor implements Callable<ReserveActivityTO> {
    private static final Logger logger = LoggerFactory.getLogger(FetchReserveActivityExecutor.class);
    private List<Long> skuIdLst;
    private String appSecret;

    private static final String API_URL = "https://api.jd.com/routerjson";

    public FetchReserveActivityExecutor() {
        super();
    }

    public FetchReserveActivityExecutor(List<Long> skuIdLst, String appSecret) {
        super();
        this.skuIdLst = skuIdLst;
        this.appSecret = appSecret;
    }

    @Override
    public ReserveActivityTO call() throws Exception {
        return fetchReserveActivitySkuId(skuIdLst, appSecret);
    }

    private ReserveActivityTO fetchReserveActivitySkuId(List<Long> skuIdLst, String appSecret) throws Exception {
        int num = 0;
        int retryNum = 0;
        List<ReserveInfoVO> reserveInfoVOList = Lists.newArrayList();
        JSONObject businessParams = new JSONObject();
        businessParams.put("bu_Id", "301");
        businessParams.put("sku", JSONObject.toJSONString(skuIdLst));
        Map<String, String> params = Maps.newHashMap();
//        map.put("360buy_param_json", JSONObject.toJSONString(params));
        params.put("360buy_param_json", JSONObject.toJSONString(businessParams));
        params.put("method", "jingdong.pop.presell.PresellInfoService.getPresellInfo");
        params.put("app_key", "321CEAB001F59FDDA67DC388C0575958");
        params.put("v", "2.0");
        params.put("format", "json");
        params.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        // Calculate signature
        logger.info("======>request json: {}", params);
        String signature = calculateSignature(params, appSecret);
        params.put("sign", signature);
        for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
            try {
                num++;
                retryNum++;
                Gson returnReponse = new Gson();
                String response = makeApiCall(params);
                if (StringUtils.isNotBlank(response)) {
                    logger.info("======>returnReponse.response:{}", response);
                    JsonObject obj = returnReponse.fromJson(response, JsonObject.class);
                    //System.out.println("obj:" + obj);
                    JsonElement reponse = obj.get("jingdong_pop_presell_PresellInfoService_getPresellInfo_responce");
                    //System.out.println("reponse:" + reponse);
                    if (reponse != null) {
                        JsonElement code = reponse.getAsJsonObject().get("code");
                        logger.info("======>fetchReserveActivitySkuId:response:{}", JSONObject.toJSONString(response));
                        //System.out.println("code:" + code);
                        if (code != null && "0".equals(code.getAsString())) {
                            JsonElement result = reponse.getAsJsonObject().get("response");
                            //System.out.println("result:" + result);
                            if(null == result || result.isJsonNull())break;
                            JsonElement data = result.getAsJsonObject().get("data");
                            //System.out.println("data:" + data);

                            for (Long skuId : skuIdLst) {
                                JsonElement skuIdInfo = data.getAsJsonObject().get(skuId.toString());
                                //System.out.println("skuIdInfo:" + skuIdInfo);

                                if (skuIdInfo != null) {
                                    ReserveInfoVO reserveInfoBean = new Gson().fromJson(skuIdInfo, ReserveInfoVO.class);
                                    //System.out.println("reserveInfoBean:" + reserveInfoBean);
                                    reserveInfoVOList.add(reserveInfoBean);
                                }
                            }
                            break;
                        }

                    } else {
                        if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                            logger.error("getShopReserveActivity error");
                            GainShopDataFailException dataFailException = new GainShopDataFailException("获取预约活动失败");
                            throw dataFailException;
                        } else {
                            Thread.sleep(1000);
                        }
                    }

                }

            } catch (GainShopDataFailException e) {
                throw e;
            } catch (Exception e) {
                if (!(e.getCause() instanceof SocketTimeoutException)) {
                    throw e;
                }
                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                    logger.error(e.getMessage(), e);
                    throw e;
                }
            }
        }
        return new ReserveActivityTO(reserveInfoVOList, num, retryNum - num);
    }

//    public static void main(String[] args) throws Exception {
//        String str = "67478900560,67478900560,67478900560";
//
//        List<Long> skuIdLst = new ArrayList<>();
//        skuIdLst.add(67478900560L);
//        skuIdLst.add(67478900560L);
//        skuIdLst.add(67478900560L);
//        String skuIdStr = JSONObject.toJSONString(skuIdLst);
//        //StringUtils.strip(skuIdStr,"[]")
//        System.out.println("skuIdStr:" + skuIdStr);
//        JSONObject params = new JSONObject();
//        params.put("bu_Id", "301");
//        params.put("sku", skuIdStr);
//        Map<String, String> map = Maps.newHashMap();
//        map.put("360buy_param_json", JSONObject.toJSONString(params));
//        Gson returnReponse = new Gson();
//        String post = HttpClientUtils.post("https://api.jd.com/routerjson?v=2.0&method=jingdong.pop" +
//                                                   ".presell.PresellInfoService" +
//                                                   ".getPresellInfo&app_key" +
//                                                   "=321CEAB001F59FDDA67DC388C0575958&access_token=",
//                                           null, map, null, false);
//        if (StringUtils.isNotBlank(post)) {
//            JsonObject obj = returnReponse.fromJson(post, JsonObject.class);
//            System.out.println("obj:" + obj);
//
//            JsonElement reponse = obj.get(
//                    "jingdong_pop_presell_PresellInfoService_getPresellInfo_responce");
//            System.out.println("reponse:" + reponse);
//            if (reponse != null) {
//                JsonElement code = reponse.getAsJsonObject().get("code");
//                System.out.println("code:" + code);
//                if (code != null && "0".equals(code.getAsString())) {
//                    JsonElement result = reponse.getAsJsonObject().get("response");
//                    System.out.println("result:" + result);
//
//                    JsonElement data = result.getAsJsonObject().get("data");
//                    System.out.println("data:" + data);
//
//                    for (Long skuId : skuIdLst) {
//                        JsonElement skuIdInfo = data.getAsJsonObject().get(skuId.toString());
//                        System.out.println("skuId:" + skuId.toString() + "-----" + "skuIdInfo:" + skuIdInfo);
//
//                        /*if(skuIdInfo != null){
//                            ReserveInfoVO reserveInfoBean = new Gson().fromJson(skuIdInfo, ReserveInfoVO.class);
//                            //System.out.println("reserveInfoBean:" + reserveInfoBean);
//                            reserveInfoVOList.add(reserveInfoBean);
//                        }*/
//                    }
//
//                }
//            }
//        }
//    }

    private String calculateSignature(Map<String, String> params, String appSecret) {
        // Sort parameters alphabetically using TreeMap
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // Concatenate parameter names and values
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            signStr.append(entry.getKey()).append(entry.getValue());
        }

        // Add appSecret to both ends
        String finalStr = appSecret + signStr.toString() + appSecret;

        // Calculate MD5 and convert to uppercase
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(finalStr.getBytes(StandardCharsets.UTF_8));
            StringBuilder result = new StringBuilder();
            for (byte b : bytes) {
                result.append(String.format("%02X", b));
            }
            return result.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }

    private String makeApiCall(Map<String, String> params) throws Exception {
        // Build URL with properly encoded parameters
        StringBuilder urlBuilder = new StringBuilder(API_URL).append("?");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            urlBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                    .append("&");
        }

        // Remove trailing "&" and make the HTTP call
        String url = urlBuilder.substring(0, urlBuilder.length() - 1);
        return HttpClientUtils.post(url, null, null, null, false);
    }
}