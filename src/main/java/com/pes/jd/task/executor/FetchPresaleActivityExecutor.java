package com.pes.jd.task.executor;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.PresaleActivityTO;
import com.pes.jd.ms.domain.Data.job.PresaleInfoVO;
import com.pes.jd.util.HttpClientUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;

public class FetchPresaleActivityExecutor implements Callable<PresaleActivityTO> {

    private static final Logger logger = LoggerFactory.getLogger(FetchPresaleActivityExecutor.class);
    private List<Long> skuIdLst;
    private String secret;

    private static final String API_URL = "https://api.jd.com/routerjson";

    public FetchPresaleActivityExecutor() {
        super();
    }

    public FetchPresaleActivityExecutor(List<Long> skuIdLst, String secret) {
        super();
        this.skuIdLst = skuIdLst;
        this.secret = secret;
    }

    @Override
    public PresaleActivityTO call() throws Exception {
        return fetchPresaleActivitySkuId(skuIdLst, secret);
    }

    private PresaleActivityTO fetchPresaleActivitySkuId(List<Long> skuIdLst, String secret) throws Exception {
        int num = 0;
        int retryNum = 0;
        List<PresaleInfoVO> presaleInfoVOList = Lists.newArrayList();
//        JSONObject params = new JSONObject();
//        params.put("skuid", JSONObject.toJSONString(skuIdLst));
//        Map<String, String> map = Maps.newHashMap();
//        map.put("360buy_param_json", JSONObject.toJSONString(params));
        JSONObject businessParams = new JSONObject();
        businessParams.put("skuid", JSONObject.toJSONString(skuIdLst));

        Map<String, String> params = new HashMap<>();
        params.put("360buy_param_json", JSONObject.toJSONString(businessParams));
        params.put("method", "jingdong.com.jd.presale.client.PresaleInfoExportServiceForJOS.getValidPresaleInfoBySkusForJOS");
        params.put("app_key", "321CEAB001F59FDDA67DC388C0575958");
        params.put("v", "2.0");
        params.put("format", "json");
        params.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        // Calculate signature
        String signature = calculateSignature(params, secret);
        params.put("sign", signature);
        for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
            try {
                num++;
                retryNum++;
                Gson returnReponse = new Gson();
//                String post = HttpClientUtils.post("https://api.jd.com/routerjson?v=2.0&method=jingdong.com.jd" +
//                                                           ".presale.client.PresaleInfoExportServiceForJOS" +
//                                                           ".getValidPresaleInfoBySkusForJOS&app_key" +
//                                                           "=321CEAB001F59FDDA67DC388C0575958&access_token=", null,
//                                                   map, null, false);
                String response = makeApiCall(params);
                logger.info("======>returnReponse.response:{}", response);
                if (StringUtils.isNotBlank(response)) {
                    JsonObject obj = returnReponse.fromJson(response, JsonObject.class);
                    //System.out.println("obj:" + obj);

                    JsonElement reponse = obj.get(
                            "jingdong_com_jd_presale_client_PresaleInfoExportServiceForJOS_getValidPresaleInfoBySkusForJOS_responce");
                    //System.out.println("reponse:" + reponse);
                    if (reponse != null) {
                        logger.info("======>fetchPresaleActivitySkuId:response:{}", JSONObject.toJSONString(response));
                        JsonElement code = reponse.getAsJsonObject().get("code");

                        if (code != null && "0".equals(code.getAsString())) {
                            JsonElement result = reponse.getAsJsonObject().get("result");
                            //System.out.println("result:" + result);
                            if(result.isJsonNull())break;
                            for (Long skuId : skuIdLst) {
                                JsonElement skuIdInfo = result.getAsJsonObject().get(String.valueOf(skuId));
                                //System.out.println("skuIdInfo:" + skuIdInfo);

                                PresaleInfoVO presaleInfoBean = new Gson().fromJson(skuIdInfo, PresaleInfoVO.class);
                                //System.out.println("presaleInfoBean:" + presaleInfoBean);
                                presaleInfoVOList.add(presaleInfoBean);
                            }
                            break;
                        }

                    } else {
                        if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                            logger.error("getShopPresaleActivity error");
                            GainShopDataFailException dataFailException = new GainShopDataFailException("获取预售活动失败");
                            throw dataFailException;
                        } else {
                            Thread.sleep(1000);
                        }
                    }
                }

            } catch (GainShopDataFailException e) {
                throw e;
            } catch (Exception e) {
                if (!(e.getCause() instanceof SocketTimeoutException)) {
                    throw e;
                }
                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                    logger.error(e.getMessage(), e);
                    throw e;
                }
            }
        }
        return new PresaleActivityTO(presaleInfoVOList, num, retryNum - num);
    }

//    public static void main(String[] args) throws Exception {
//        String str = "***********,***********,***********";
//
//        List<Long> skuIdLst = new ArrayList<>();
//        skuIdLst.add(67958633319L);
//        skuIdLst.add(***********L);
//        skuIdLst.add(68014706219L);
//        String skuIdStr = JSONObject.toJSONString(skuIdLst);
//        //StringUtils.strip(skuIdStr,"[]")
//        System.out.println("skuIdStr:" + skuIdStr);
////        JSONObject params = new JSONObject();
//        JSONObject businessParams = new JSONObject();
//        businessParams.put("skuid", JSONObject.toJSONString(skuIdLst));
//
////        params.put("skuid", JSONObject.toJSONString(skuIdLst));
//        Map<String, String> map = Maps.newHashMap();
////        map.put("360buy_param_json", JSONObject.toJSONString(params));
////        calculateSignature(map, "2b5f6d0cbde1419c9a64e8ce694f22e9");
//        Map<String, String> params = new HashMap<>();
//        params.put("360buy_param_json", JSONObject.toJSONString(businessParams));
//        params.put("method", "jingdong.com.jd.presale.client.PresaleInfoExportServiceForJOS.getValidPresaleInfoBySkusForJOS");
//        params.put("app_key", "321CEAB001F59FDDA67DC388C0575958");
//        params.put("v", "2.0");
//        params.put("format", "json");
//        params.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//        // Calculate signature
////        String signature = calculateSignature(params, "2b5f6d0cbde1419c9a64e8ce694f22e9");
////        params.put("sign", signature);
////        String returnReponse = makeApiCall(params);
////        System.out.println("======>:" + returnReponse);
//        // Implement retry logic
////        for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
////            try {
////                // Make API call with all parameters properly encoded
////                String response = makeApiCall(params);
////
////                if (StringUtils.isNotBlank(response)) {
////                    JsonObject responseObj = new Gson().fromJson(response, JsonObject.class);
////
////                    // Get the specific response field based on the method name
////                    String responseKey = "jingdong_com_jd_presale_client_PresaleInfoExportServiceForJOS_getValidPresaleInfoBySkusForJOS_responce";
////                    JsonElement apiResponse = responseObj.get(responseKey);
////
////                    if (apiResponse != null) {
////                        JsonElement code = apiResponse.getAsJsonObject().get("code");
////
////                        if (code != null && "0".equals(code.getAsString())) {
////                            JsonElement result = apiResponse.getAsJsonObject().get("result");
////
////                            // Process each SKU's presale information
////                            for (Long skuId : skuIdLst) {
////                                JsonElement skuIdInfo = result.getAsJsonObject().get(String.valueOf(skuId));
////                                if (skuIdInfo != null) {
////                                    PresaleInfoVO presaleInfoBean = new Gson().fromJson(skuIdInfo, PresaleInfoVO.class);
//////                                    presaleInfoVOList.add(presaleInfoBean);
////                                }
////                            }
//////                            return presaleInfoVOList;
////                        }
////                    }
////                }
////
////                // If we reach here, there was an error. Wait before retry
////                if (recallApiTimes < CommonConstants.RECALLAPI_TIMES - 1) {
////                    Thread.sleep(1000);
////                }
////
////            } catch (Exception e) {
////                logger.error("Error calling JD API", e);
////                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
////                    throw new GainShopDataFailException("获取预售活动失败: " + e.getMessage());
////                }
////            }
////        }
//
////        throw new GainShopDataFailException("获取预售活动失败: 重试次数已用完");
////        String post = HttpClientUtils.post("https://api.jd.com/routerjson?v=2.0&method=jingdong.com.jd" +
////                                                   ".presale.client.PresaleInfoExportServiceForJOS" +
////                                                   ".getValidPresaleInfoBySkusForJOS&app_key" +
////                                                   "=321CEAB001F59FDDA67DC388C0575958&access_token=", null,
////                                           map, null, false);
////        if (StringUtils.isNotBlank(post)) {
////            JsonObject obj = returnReponse.fromJson(post, JsonObject.class);
////            //System.out.println("obj:" + obj);
////
////            JsonElement reponse = obj.get(
////                    "jingdong_com_jd_presale_client_PresaleInfoExportServiceForJOS_getValidPresaleInfoBySkusForJOS_responce");
////            //System.out.println("reponse:" + reponse);
////            System.out.println("======>" + reponse.toString());
////            if (reponse != null) {
////                JsonElement code = reponse.getAsJsonObject().get("code");
////
////                if (code != null && "0".equals(code.getAsString())) {
////                    JsonElement result = reponse.getAsJsonObject().get("result");
////                    //System.out.println("result:" + result);
////
////                    for (Long skuId : skuIdLst) {
////                        JsonElement skuIdInfo = result.getAsJsonObject().get(String.valueOf(skuId));
////                        System.out.println("skuIdInfo:" + skuIdInfo);
////
////                        PresaleInfoVO presaleInfoBean = new Gson().fromJson(skuIdInfo, PresaleInfoVO.class);
////                        //.out.println("presaleInfoBean:" + presaleInfoBean);
////                    }
////
////                }
////            }
////        }
//    }

    private String calculateSignature(Map<String, String> params, String appSecret) {
        // Sort parameters alphabetically using TreeMap
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // Concatenate parameter names and values
        StringBuilder signStr = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            signStr.append(entry.getKey()).append(entry.getValue());
        }

        // Add appSecret to both ends
        String finalStr = appSecret + signStr.toString() + appSecret;

        // Calculate MD5 and convert to uppercase
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] bytes = md.digest(finalStr.getBytes(StandardCharsets.UTF_8));
            StringBuilder result = new StringBuilder();
            for (byte b : bytes) {
                result.append(String.format("%02X", b));
            }
            return result.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }

    private String makeApiCall(Map<String, String> params) throws Exception {
        // Build URL with properly encoded parameters
        StringBuilder urlBuilder = new StringBuilder(API_URL).append("?");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            urlBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                    .append("&");
        }

        // Remove trailing "&" and make the HTTP call
        String url = urlBuilder.substring(0, urlBuilder.length() - 1);
        return HttpClientUtils.post(url, null, null, null, false);
    }
}