package com.pes.jd.task.executor;

import com.alibaba.fastjson.JSONObject;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceCommonProvider.response.view.OrderAfsAndRefund;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceQueryProvider.response.view.ServiceBill;
import com.jd.open.api.sdk.request.afsservice.AfsserviceRefundinfoGetRequest;
import com.jd.open.api.sdk.request.shangjiashouhou.AscQueryViewRequest;
import com.jd.open.api.sdk.request.shangjiashouhou.AscServiceAndRefundViewRequest;
import com.jd.open.api.sdk.response.afsservice.AfsserviceRefundinfoGetResponse;
import com.jd.open.api.sdk.response.shangjiashouhou.AscQueryViewResponse;
import com.jd.open.api.sdk.response.shangjiashouhou.AscServiceAndRefundViewResponse;
import com.pes.jd.Constants.AppConstants;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.Enum.OrderRefundEnum;
import com.pes.jd.model.TO.AscServiceOrderRefundTO;
import com.pes.jd.util.ApiClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.Callable;

public class FetchAscServiceOrderRefundDataExecutor implements Callable<AscServiceOrderRefundTO> {
	private static final Logger logger = LoggerFactory.getLogger(FetchAscServiceOrderRefundDataExecutor.class);

	private Date sDate;
	private Date eDate;
	private String sessionKey;
	private int fetchType;
	private Long venderId;

	public FetchAscServiceOrderRefundDataExecutor() {
		super();
	}

	// 拉取退款时间的fetchType类型 1根据退款申请时间拉取 2根据退款审核时间拉取
	public FetchAscServiceOrderRefundDataExecutor(Date sDate, Date eDate, String sessionKey, int fetchType) {
		super();
		this.sDate = sDate;
		this.eDate = eDate;
		this.sessionKey = sessionKey;
		this.fetchType = fetchType;
	}

	// 拉取退款时间的fetchType类型 1根据退款申请时间拉取 2根据退款审核时间拉取
	public FetchAscServiceOrderRefundDataExecutor(Date sDate, Date eDate, String sessionKey, int fetchType, Long venderId) {
		super();
		this.sDate = sDate;
		this.eDate = eDate;
		this.sessionKey = sessionKey;
		this.fetchType = fetchType;
		this.venderId = venderId;
	}

	@Override
	public AscServiceOrderRefundTO call() throws Exception {
		AscServiceOrderRefundTO model;
		if (fetchType == OrderRefundEnum.FETCH_TYPE_ASC_SERVICE_APPLY_TIME.getType()) {
			model = getApplyTimeAscServiceOrderRefundData(sDate, eDate, sessionKey);
		} else {
			model = getCheckTimeAscServiceOrderRefundData(sDate, eDate, sessionKey);
		}
		return model;
	}

	private AscServiceOrderRefundTO getApplyTimeAscServiceOrderRefundData(Date sDate, Date eDate, String sessionKey) throws Exception {
		JdClient client = ApiClientUtil.getClient(sessionKey);
		AscServiceAndRefundViewRequest req = new AscServiceAndRefundViewRequest();
		int pageSize = 50;
		req.setPageSize(pageSize);
		List<OrderAfsAndRefund> allRefund = new ArrayList<OrderAfsAndRefund>();
		AscServiceAndRefundViewResponse  response = null;
		List<OrderAfsAndRefund> resultLst = null;
		int num = 0;
		int retryNum = 0;

		boolean hasNextPage = false;
		int page = 0;
		do {
			page++;
			num++;
			req.setPageNumber(page);
			//req.setStatus("3,5");
			req.setApplyTimeBegin(sDate);
			req.setApplyTimeEnd(eDate);
			for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
				try {
					retryNum++;
					response = client.execute(req);
					if (response.getCode() != null && "0".equals(response.getCode())) {
						resultLst = response.getPageResult().getData();
						break;
					} else {
						if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
							String errorCode = response.getCode();
							String errorMsg = response.getMsg();
							if(logger.isDebugEnabled()){
								logger.debug(" AscService refound order error_code:{}, error_msg:{}", errorCode, errorMsg);
							}
							GainShopDataFailException callApiException = new GainShopDataFailException("订单获取失败");
							callApiException.setErrorMsg(errorMsg);
							callApiException.setErrorCode(errorCode);
							throw callApiException;
						} else {
							Thread.sleep(3000);
						}
					}
				} catch (GainShopDataFailException e) {
					throw e;
				} catch (Exception e) {
					if (!(e.getCause() instanceof SocketTimeoutException)) {
						throw e;
					}
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						if(logger.isDebugEnabled()){
							logger.debug("fetch AscService refund orderData error{}", e);
						}
						throw e;
					}
				}

			}

			if (resultLst != null && resultLst.size() > 0) {
				allRefund.addAll(resultLst);
				if (resultLst.size() < pageSize) {
					hasNextPage = false;
				} else {
					hasNextPage = true;
				}
//				logger.info("refund AscServiceorder list size{}", resultLst.size());
			} else {
				hasNextPage = false;
//				logger.error("fetch refund AscServiceorder error need check");
			}
		} while (hasNextPage);
		if(!CollectionUtils.isEmpty(allRefund)){
			refundDataLaundering(allRefund);
		}
		return new AscServiceOrderRefundTO(allRefund, num, retryNum - num);
	}

	private AscServiceOrderRefundTO getCheckTimeAscServiceOrderRefundData(Date sDate, Date eDate, String sessionKey) throws Exception {
		//JdClient client = ApiClientUtil.getClient(sessionKey);

		JdClient client = ApiClientUtil.getClient(sessionKey);
		AscServiceAndRefundViewRequest req = new AscServiceAndRefundViewRequest();
		int pageSize = 50;
		req.setPageSize(pageSize);
		List<OrderAfsAndRefund> allRefund = new ArrayList<OrderAfsAndRefund>();
		List<OrderAfsAndRefund> resultLst = null;
		int num = 0;
		int retryNum = 0;

		boolean hasNextPage = false;
		int page = 0;
		do {
			page++;
			num++;
			req.setPageNumber(page);
			//req.setStatus("3,5");
			req.setApproveTimeBegin(sDate);
			req.setApproveTimeEnd(eDate);
			AscServiceAndRefundViewResponse response = null;
			for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
				try {
					retryNum++;
					response = client.execute(req);
					if (response.getCode() != null && "0".equals(response.getCode())) {
						resultLst = response.getPageResult().getData();
						break;
					} else {
						if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
							String errorCode = response.getCode();
							String errorMsg = response.getMsg();
							logger.error("AscService refound order error_code:{}, error_msg:{}", errorCode, errorMsg);
							GainShopDataFailException callApiException = new GainShopDataFailException("订单获取失败");
							callApiException.setErrorMsg(errorMsg);
							callApiException.setErrorCode(errorCode);
							throw callApiException;
						} else {
							Thread.sleep(3000);
						}
					}
				} catch (GainShopDataFailException e) {
					throw e;
				} catch (Exception e) {
					if (!(e.getCause() instanceof SocketTimeoutException)) {
						throw e;
					}
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						logger.warn("fetch AscService refund orderData error{}", e);
						throw e;
					}
				}

			}

			if (resultLst != null && resultLst.size() > 0) {
				allRefund.addAll(resultLst);
				if (resultLst.size() < pageSize) {
					hasNextPage = false;
				} else {
					hasNextPage = true;
				}
//				logger.info("AscService refund order list size{}", resultLst.size());
			} else {
				hasNextPage = false;
//				logger.error("fetch AscService refund order error need check");
			}
		} while (hasNextPage);
		//fixme data laundering
		if(!CollectionUtils.isEmpty(allRefund)){
			refundDataLaundering(allRefund);
		}
		return new AscServiceOrderRefundTO(allRefund, num, retryNum - num);
	}

	/**
	 *
	 * @param refundList
	 */
	private void refundDataLaundering(List<OrderAfsAndRefund> refundList){
		refundList.removeIf(t -> {
			try {
				if(this.queryView(t, venderId)){
					this.refundInfo(t);
					return Boolean.FALSE;
				}else{
					return Boolean.TRUE;//remove
				}
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("======>jingdong.asc.query.view调用失败, venderId:{}, serviceId:{}", venderId, t.getSameOrderServiceBill().getServiceId());
			}
			return Boolean.TRUE;//remove
		});
//		for(OrderAfsAndRefund thisRefund: refundList){
//			try {
//				Boolean isSuccess = this.queryView(thisRefund, venderId);
//				if(isSuccess){
//					isSuccess = this.refundInfo(thisRefund);
//				}
//			}catch (Exception e){
//				e.printStackTrace();
//				logger.info("======>jingdong.asc.query.view调用失败, venderId:{}, serviceId:{}", venderId, thisRefund.getSameOrderServiceBill().getServiceId());
//			}
//			Thread.sleep(100);
//		}
	}
	//获取的退款中的serviceId 传入jingdong.asc.query.view ( 查看服务单明细信息 )，清洗脏数据
	private Boolean queryView(OrderAfsAndRefund orderAfsAndRefund, Long venderId) throws Exception {
		JdClient client = new DefaultJdClient(AppConstants.SERVER_URL, sessionKey, AppConstants.APP_KEY, AppConstants.APP_SECRET);
		AscQueryViewRequest request = new AscQueryViewRequest();
		request.setServiceId(orderAfsAndRefund.getSameOrderServiceBill().getServiceId());
		request.setOperateNick(orderAfsAndRefund.getSameOrderServiceBill().getApproveName());
		request.setOperatePin(orderAfsAndRefund.getSameOrderServiceBill().getApproveName());
		request.setOrderId(orderAfsAndRefund.getSameOrderServiceBill().getOrderId());
		request.setBuId(venderId + "");
		AscQueryViewResponse response;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				response = client.execute(request);
				if (response.getCode() != null && "0".equals(response.getCode())) {
					ServiceBill serviceBill = response.getResult().getData();
					if(null != serviceBill
							&& null != serviceBill.getProcessResult()
							&& serviceBill.getProcessResult() == 40){
						//handle status
						Integer status = StatusReflect.codeStatusMap.get(serviceBill.getServiceStatus());
						if(null != status){
							orderAfsAndRefund.setStatus(status);
							if(status == 2 || status == 14){
								orderAfsAndRefund.setCompleteTime(null);
							}
						}
						return Boolean.TRUE;
					}
					break;
				}
				Thread.sleep(3000);
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
			}
		}
		return Boolean.FALSE;
	}
	//退款金额通过jingdong.afsservice.refundinfo.get获取
	private void refundInfo(OrderAfsAndRefund orderAfsAndRefund) throws Exception{
		JdClient client = new DefaultJdClient(AppConstants.SERVER_URL, sessionKey, AppConstants.APP_KEY, AppConstants.APP_SECRET);
		AfsserviceRefundinfoGetRequest request = new AfsserviceRefundinfoGetRequest();
		request.setAfsServiceId(orderAfsAndRefund.getSameOrderServiceBill().getServiceId());
		AfsserviceRefundinfoGetResponse response;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				response = client.execute(request);
				if (response.getCode() != null && "0".equals(response.getCode())) {
					JSONObject jsonObject = JSONObject.parseObject(response.getMsg());
					String amount = jsonObject.getJSONObject("jingdong_afsservice_refundinfo_get_responce")
							.getJSONObject("publicResultObject4")
							.getJSONObject("afsRefundInfoOut")
							.getJSONObject("afsRefundOut")
							.getString("suggestAmount");
					if(null != amount) {
						orderAfsAndRefund.setRefoundAmount(new BigDecimal(amount));
					}
					break;
				}
				Thread.sleep(3000);
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
			}
		}
	}

	/**
	 * serviceStatus:
	 *         待审核：10001      //映射到我们status(2, "待审核")  completeTime（完成时间）置空
	 *         待客户反馈：10002  //映射到我们status(2, "待审核") completeTime（完成时间）置空
	 *         客户已反馈：10012  //映射到我们status(2, "待审核") completeTime（完成时间）置空
	 *         待收货：10005      //映射到我们status(2, "待审核") completeTime（完成时间）置空
	 *         已收货，待处理 ：10007   //映射到我们status(2, "待审核") completeTime（完成时间）置空
	 *         取消：10011        //映射到我们status(14, "退款失败") completeTime（完成时间）置空
	 *         审核关闭：10004    //映射到我们status(14, "退款失败") completeTime（完成时间）置空
	 *         待用户确认：10009  //映射到我们status(13, "退款成功")
	 *         完成：10010    //映射到我们status(13, "退款成功")
	 */
	private enum StatusReflect{
		code_1(10001, 2),
		code_2(10002, 2),
		code_3(10012, 2),
		code_4(10005, 2),
		code_5(10007, 2),
		code_6(10011, 14),
		code_7(10004, 14),
		code_8(10009, 13),
		code_9(10010, 13),
		;
		private final Integer code;
		private final Integer status;

		private static final Map<Integer, Integer> codeStatusMap = new HashMap<>();

		static {
			StatusReflect[] values = StatusReflect.values();
			for(StatusReflect thisEnum: values){
				codeStatusMap.put(thisEnum.code, thisEnum.status);
			}
		}

		StatusReflect(Integer code, Integer status) {
			this.code = code;
			this.status = status;
		}
	}
}
