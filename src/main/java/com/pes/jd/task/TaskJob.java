/**
 * Project Name:z-spring-boot
 * File Name:TaskJob.java
 * Package Name:com.pes.jd.task
 * Date:2018年6月26日下午3:14:17
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.
 */

package com.pes.jd.task;

import com.pes.jd.dao.main.MenuDotDao;
import com.pes.jd.dao.main.MenuDotSelfDao;
import com.pes.jd.util.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 定时任务Job
 * ClassName:TaskJob <br/>
 * Date:     2018年6月26日 下午3:14:17 <br/>
 *
 * <AUTHOR>
 */
@Component
public class TaskJob {

    //
    //@Scheduled(cron="0 5 * * * *")
    //public void test(){
    //	System.out.println("task:"+new Date());
    //}

    @Resource
    private MenuDotDao menuDotDao;

    @Resource
    private MenuDotSelfDao menuDotSelfDao;

    /**
     * 删除打点 定时任务
     * <p>
     * 每天凌晨1点0分1秒跑一次
     */
    @Scheduled(cron = "1 0 1 * * ?")
    public void delMenuDotTask() {
        //删除15天以前的 打点数据
        try {
            menuDotDao.deleteMenuDotByDate(DateUtils.getDateByPeriod(
            		DateUtils.getStartTimeOfDate(new Date()), -365));
            menuDotSelfDao.deleteMenuDotByDate(DateUtils.getDateByPeriod(
                    DateUtils.getStartTimeOfDate(new Date()), -365));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}

