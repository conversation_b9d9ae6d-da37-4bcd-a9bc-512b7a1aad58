package com.pes.jd.task;

import org.springframework.stereotype.Component;

/**  
 * ClassName:ScheduledSessionKey <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月31日 上午10:02:01 <br/>  
 * <AUTHOR> 
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Component
public class ScheduledSessionKey {

//	@Autowired
//	private ShopDao shopDao;
//	 private final Logger logger = LoggerFactory.getLogger(ScheduledSessionKey.class);
    //1小时刷新一次
	// @Scheduled(cron="0 0 * * * ?")
//    public void reFreshSessionKey(){
//    	
//    	try {
//    		logger.info(new Date()+"开始刷新sessionKey··············");
//    		JDSessionKey jdSession=	ApiClientUtil.getSessionKeyByRefreshToken("230dae82-5127-4821-8988-9f9471b34ee1");
//			shopDao.updateShopBySessionkey("230dae82-5127-4821-8988-9f9471b34ee1", jdSession.getAccess_token());
//			logger.info(new Date()+"刷新sessionKey成功··············");
//    	} catch (HttpReqException | JacksonParseException e) {
//			e.printStackTrace();  
//			
//		}
// 
//    }

}
  
