package com.pes.jd.config;


import com.alibaba.druid.pool.DruidDataSource;
import com.yiyitech.support.datasource.DataSourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@ConditionalOnProperty(name = "yiyitech.datasource.enabled", havingValue = "true")
public class JobDataSourceConfig {

    private static final Logger LOGGER = LoggerFactory.getLogger(JobDataSourceConfig.class);

    @Autowired
    private Environment environment;

    /**
     * 创建持久化路由数据源，替换原有的RoutingDataSource
     * 使用@Primary注解确保这个DataSource被优先使用
     */
    @Bean(name = "dataSource")
    @Primary
    public DataSource routingDataSource() throws SQLException {
        LOGGER.info("创建持久化路由数据源...");

        // 借用原有的数据源解析逻辑
        Map<String, DataSource> druidDataSourceMap = createDruidDataSources();

        // 创建持久化路由数据源
        PersistentRoutingDataSource routingDataSource = new PersistentRoutingDataSource();

        // 转换为路由数据源需要的格式
        Map<Object, Object> targetDataSources = new HashMap<>();
        for (Map.Entry<String, DataSource> entry : druidDataSourceMap.entrySet()) {
            targetDataSources.put(entry.getKey(), entry.getValue());
            LOGGER.info("注册数据源: {}", entry.getKey());
        }

        routingDataSource.setTargetDataSources(targetDataSources);

        // 设置默认数据源（取第一个作为默认）
        if (!druidDataSourceMap.isEmpty()) {
            String firstKey = druidDataSourceMap.keySet().iterator().next();
            routingDataSource.setDefaultTargetDataSource(druidDataSourceMap.get(firstKey));
            LOGGER.info("设置默认数据源: {}", firstKey);
        }

        // 初始化数据源
        routingDataSource.afterPropertiesSet();

        LOGGER.info("持久化路由数据源创建完成，包含 {} 个数据源", druidDataSourceMap.size());
        return routingDataSource;
    }

    /**
     * 借用原有的DataSourceConfig逻辑创建Druid数据源
     */
    private Map<String, DataSource> createDruidDataSources() throws SQLException {
        LOGGER.info("解析数据源配置，创建Druid数据源...");

        Map<String, DataSource> dsMap = new HashMap<>();
        List<DataSourceConfig> dataSourceConfigList = DataSourceConfig.getDataSourceConfigList(environment);

        for (DataSourceConfig dsConfig : dataSourceConfigList) {
            String dsName = dsConfig.getDsName();
            LOGGER.info("创建数据源: {}", dsName);

            DruidDataSource ds = new DruidDataSource();
            ds.setUsername(dsConfig.getUsername());
            ds.setPassword(dsConfig.getPassword());
            ds.setDriverClassName(dsConfig.getDrivername());
            ds.setUrl(dsConfig.getJdbcurl());
            ds.setInitialSize(dsConfig.getMinpoolSize());
            ds.setMinIdle(dsConfig.getMinpoolSize());
            ds.setMaxActive(dsConfig.getMaxpoolSize());
            ds.setMaxWait(60000L);
            ds.setTestWhileIdle(true);
            ds.setTimeBetweenEvictionRunsMillis(60000L);
            ds.setMinEvictableIdleTimeMillis(300000L);
            ds.setValidationQuery("SELECT 1 FROM DUAL");
            ds.setPoolPreparedStatements(true);
            ds.setMaxPoolPreparedStatementPerConnectionSize(20);
            ds.setMaxOpenPreparedStatements(20);

            // 初始化Druid数据源
            ds.init();

            dsMap.put(dsName, ds);
            LOGGER.info("数据源 {} 创建成功", dsName);
        }

        return dsMap;
    }
}


