package com.pes.jd.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

//@Configuration
public class TaskExecutorConfig {

    private final Logger logger = LoggerFactory.getLogger(TaskExecutorConfig.class);

    @Bean(name = "taskExecutor")
    public ExecutorService taskExecutor() {


        final int corePoolSize = 5;
        final int maximumPoolSize = 20;

        ExecutorService executorService = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
        logger.info("=======>init ExecutorService for taskExecutor ok !");
        return executorService;
    }
    /**
     * 报表导出队列
     */
/*    @Bean(name="execlExportQueue")
    public LinkedBlockingQueue<String> execlExportQueue(){
        LinkedBlockingQueue<String> queue = new LinkedBlockingQueue<String>();
        return queue;
    }*/

}
