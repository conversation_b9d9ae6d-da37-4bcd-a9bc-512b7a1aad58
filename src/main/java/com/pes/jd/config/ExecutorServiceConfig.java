package com.pes.jd.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Author: aiJun
 * @Date: 2019-07-18 15:40
 * @Version 1.0
 */
@Configuration
public class ExecutorServiceConfig {
    private static Logger logger = LoggerFactory.getLogger(ExecutorServiceConfig.class);

    @Bean(name = "uploadLogExecutorService")
    public ExecutorService uploadLogExecutorService() {
        final int corePoolSize = 2;
        final int maximumPoolSize = 2;
        ExecutorService executorService = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
        logger.info("======》 init executorService for UploadLog ok ! 《=====");
        return executorService;
    }
}
