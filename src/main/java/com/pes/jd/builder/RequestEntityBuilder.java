package com.pes.jd.builder;

import org.springframework.http.HttpEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

public class RequestEntityBuilder {
	
	private RequestEntityBuilder(){}
    
    public static Builder builder(){
        return new Builder();
    }
    
    public static class Builder{
    	
    	MultiValueMap<String,Object> build = new LinkedMultiValueMap<>();
        public Builder put(String key, Object value){
            this.build.add(key, value);
            return this;





        }
        public HttpEntity<Object> toRequestEntity(){
        	return new HttpEntity<Object>(build);
        }
    }
	

}
