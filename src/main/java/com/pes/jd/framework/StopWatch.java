package com.pes.jd.framework;

import java.util.ArrayList;
import java.util.List;

public class StopWatch {

    private String taskName;

    private long startTime;

    private class TaskInfo{

        private String taskName;

        private long startTime;

        private long endTime;

        TaskInfo(String taskName, long startTime, long endTime) {
            this.taskName = taskName;
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }

    private final List<TaskInfo> taskArray = new ArrayList<>();

    public void start(String taskName){
        this.taskName = taskName;
        startTime = System.currentTimeMillis();
    }

    /**
     *  经过了多久
     * @return
     */
    public long watch(){
        return System.currentTimeMillis() - startTime;
    }

    public long stop(){
        final long watch = watch();
        taskArray.add(new TaskInfo(this.taskName,this.startTime,System.currentTimeMillis()));
        this.taskName = null;
        this.startTime = 0L;
        return watch;
    }

}
