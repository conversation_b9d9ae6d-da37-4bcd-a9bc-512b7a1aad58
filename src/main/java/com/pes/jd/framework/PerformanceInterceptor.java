/*
 * Copyright (c) 2011-2020, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * https://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.pes.jd.framework;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.sql.Statement;
import java.util.*;

/**
 * 性能分析拦截器，用于输出每条 SQL 语句及其执行时间
 *
 * <AUTHOR> nieqiurong TaoYu
 * @since 2016-07-07
 */
@Profile("dev")
//@Component
@Intercepts({
    @Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class}),
    @Signature(type = StatementHandler.class, method = "update", args = {Statement.class}),
    @Signature(type = StatementHandler.class, method = "batch", args = {Statement.class})
})
public class PerformanceInterceptor implements Interceptor {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceInterceptor.class);
    private static final String DruidPooledPreparedStatement = "com.alibaba.druid.pool.DruidPooledPreparedStatement";
    private static final String T4CPreparedStatement = "oracle.jdbc.driver.T4CPreparedStatement";
    private static final String OraclePreparedStatementWrapper = "oracle.jdbc.driver.OraclePreparedStatementWrapper";
    /**
     * SQL 执行最大时长，超过自动停止运行，有助于发现问题。
     */
    private long maxTime = 30000;
    /**
     * SQL 是否格式化
     */
    private boolean format = true;
    /**
     * 是否写入日志文件
     * <p>true 写入日志文件，不阻断程序执行！</p>
     * <p>超过设定的最大执行时长异常提示！</p>
     */
    private boolean writeInLog = true;

    public PerformanceInterceptor() {
        System.out.println();
    }

    public long getMaxTime() {
        return maxTime;
    }

    public PerformanceInterceptor setMaxTime(long maxTime) {
        this.maxTime = maxTime;
        return this;
    }

    public boolean isFormat() {
        return format;
    }

    public PerformanceInterceptor setFormat(boolean format) {
        this.format = format;
        return this;
    }

    public boolean isWriteInLog() {
        return writeInLog;
    }

    public PerformanceInterceptor setWriteInLog(boolean writeInLog) {
        this.writeInLog = writeInLog;
        return this;
    }

    private Method oracleGetOriginalSqlMethod;
    private Method druidGetSQLMethod;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Statement statement;
        Object firstArg = invocation.getArgs()[0];
        if (Proxy.isProxyClass(firstArg.getClass())) {
            statement = (Statement) SystemMetaObject.forObject(firstArg).getValue("h.statement");
        } else {
            statement = (Statement) firstArg;
        }
        MetaObject stmtMetaObj = SystemMetaObject.forObject(statement);
        try {
            statement = (Statement) stmtMetaObj.getValue("stmt.statement");
        } catch (Exception e) {
            // do nothing
        }
        if (stmtMetaObj.hasGetter("delegate")) {
            //Hikari
            try {
                statement = (Statement) stmtMetaObj.getValue("delegate");
            } catch (Exception ignored) {

            }
        }

        String originalSql = null;
        String stmtClassName = statement.getClass().getName();
        if (DruidPooledPreparedStatement.equals(stmtClassName)) {
            try {
                if (druidGetSQLMethod == null) {
                    Class<?> clazz = Class.forName(DruidPooledPreparedStatement);
                    druidGetSQLMethod = clazz.getMethod("getSql");
                }
                Object stmtSql = druidGetSQLMethod.invoke(statement);
                if (stmtSql instanceof String) {
                    originalSql = (String) stmtSql;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (T4CPreparedStatement.equals(stmtClassName)
            || OraclePreparedStatementWrapper.equals(stmtClassName)) {
            try {
                if (oracleGetOriginalSqlMethod != null) {
                    Object stmtSql = oracleGetOriginalSqlMethod.invoke(statement);
                    if (stmtSql instanceof String) {
                        originalSql = (String) stmtSql;
                    }
                } else {
                    Class<?> clazz = Class.forName(stmtClassName);
                    oracleGetOriginalSqlMethod = getMethodRegular(clazz, "getOriginalSql");
                    if (oracleGetOriginalSqlMethod != null) {
                        //OraclePreparedStatementWrapper is not a public class, need set this.
                        oracleGetOriginalSqlMethod.setAccessible(true);
                        if (null != oracleGetOriginalSqlMethod) {
                            Object stmtSql = oracleGetOriginalSqlMethod.invoke(statement);
                            if (stmtSql instanceof String) {
                                originalSql = (String) stmtSql;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                //ignore
            }
        }
        if (originalSql == null) {
            originalSql = statement.toString();
        }
        originalSql = originalSql.replaceAll("[\\s]+", " ");
        int index = indexOfSqlStart(originalSql);
        if (index > 0) {
            originalSql = originalSql.substring(index);
        }

        // 计算执行 SQL 耗时
        long start = System.currentTimeMillis();

        long timing = System.currentTimeMillis() - start;

        // 格式化 SQL 打印执行结果
        Object target = realTarget(invocation.getTarget());
        if (Proxy.isProxyClass(target.getClass())) {
            MetaObject metaObject = SystemMetaObject.forObject(target);
            return realTarget(metaObject.getValue("h.target"));
        }
        MetaObject metaObject = SystemMetaObject.forObject(target);
        MappedStatement ms = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        StringBuilder formatSql = new StringBuilder()
                .append("\n")
            .append(" ms - ID：").append(ms.getId())
            .append("\n").append("Execute SQL：")
            .append(sqlFormat(originalSql, format)).append(StringPool.NEWLINE);
        if (this.isWriteInLog()) {
            if (this.getMaxTime() >= 1 && timing > this.getMaxTime()) {
                logger.error(formatSql.toString());
            } else {
                logger.info(formatSql.toString());
            }
        } else {
            System.err.println(formatSql.toString());
            Assert.isTrue(!(this.getMaxTime() >= 1 && timing > this.getMaxTime()),
                " The SQL execution time is too large, please optimize ! ");
        }
        Object result = invocation.proceed();
        return result;
    }

    private final static SqlFormatter SQL_FORMATTER = new SqlFormatter();

    public  String sqlFormat(String boundSql, boolean format) {
        if (format) {
            try {
                return SQL_FORMATTER.format(boundSql);
            } catch (Exception ignored) {
            }
        }
        return boundSql;
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        }
        return target;
    }


    @Override
    public void setProperties(Properties prop) {
        String maxTime = prop.getProperty("maxTime");
        String format = prop.getProperty("format");
        if (StringUtils.isNotEmpty(maxTime)) {
            this.maxTime = Long.parseLong(maxTime);
        }
        if (StringUtils.isNotEmpty(format)) {
            this.format = Boolean.valueOf(format);
        }
    }

    public Object realTarget(Object target) {
        if (Proxy.isProxyClass(target.getClass())) {
            MetaObject metaObject = SystemMetaObject.forObject(target);
            return realTarget(metaObject.getValue("h.target"));
        }
        return target;
    }

    /**
     * 获取此方法名的具体 Method
     *
     * @param clazz      class 对象
     * @param methodName 方法名
     * @return 方法
     */
    public Method getMethodRegular(Class<?> clazz, String methodName) {
        if (Object.class.equals(clazz)) {
            return null;
        }
        for (Method method : clazz.getDeclaredMethods()) {
            if (method.getName().equals(methodName)) {
                return method;
            }
        }
        return getMethodRegular(clazz.getSuperclass(), methodName);
    }

    /**
     * 获取sql语句开头部分
     *
     * @param sql ignore
     * @return ignore
     */
    private int indexOfSqlStart(String sql) {
        String upperCaseSql = sql.toUpperCase();
        Set<Integer> set = new HashSet<>();
        set.add(upperCaseSql.indexOf("SELECT "));
        set.add(upperCaseSql.indexOf("UPDATE "));
        set.add(upperCaseSql.indexOf("INSERT "));
        set.add(upperCaseSql.indexOf("DELETE "));
        set.remove(-1);
        if (CollectionUtils.isEmpty(set)) {
            return -1;
        }
        List<Integer> list = new ArrayList<>(set);
        list.sort(Comparator.naturalOrder());
        return list.get(0);
    }

    static class SqlFormatter {

        public static final String WHITESPACE = " \n\r\f\t";
        private static final Set<String> BEGIN_CLAUSES = new HashSet<>();
        private static final Set<String> END_CLAUSES = new HashSet<>();
        private static final Set<String> LOGICAL = new HashSet<>();
        private static final Set<String> QUANTIFIERS = new HashSet<>();
        private static final Set<String> DML = new HashSet<>();
        private static final Set<String> MISC = new HashSet<>();
        private static final String INDENT_STRING = "    ";
        private static final String INITIAL = "\n    ";

        static {
            BEGIN_CLAUSES.add("left");
            BEGIN_CLAUSES.add("right");
            BEGIN_CLAUSES.add("inner");
            BEGIN_CLAUSES.add("outer");
            BEGIN_CLAUSES.add("group");
            BEGIN_CLAUSES.add("order");

            END_CLAUSES.add("where");
            END_CLAUSES.add("set");
            END_CLAUSES.add("having");
            END_CLAUSES.add("join");
            END_CLAUSES.add("from");
            END_CLAUSES.add("by");
            END_CLAUSES.add("into");
            END_CLAUSES.add("union");

            LOGICAL.add("and");
            LOGICAL.add("or");
            LOGICAL.add("when");
            LOGICAL.add("else");
            LOGICAL.add("end");

            QUANTIFIERS.add("in");
            QUANTIFIERS.add("all");
            QUANTIFIERS.add("exists");
            QUANTIFIERS.add("some");
            QUANTIFIERS.add("any");

            DML.add("insert");
            DML.add("update");
            DML.add("delete");

            MISC.add("select");
            MISC.add("on");
        }

        public String format(String source) {
            return new FormatProcess(source).perform();
        }

        private static class FormatProcess {

            boolean beginLine = true;
            boolean afterBeginBeforeEnd;
            boolean afterByOrSetOrFromOrSelect;
            boolean afterValues;
            boolean afterOn;
            boolean afterBetween;
            boolean afterInsert;
            int inFunction;
            int parensSinceSelect;
            private LinkedList<Integer> parenCounts = new LinkedList<>();
            private LinkedList<Boolean> afterByOrFromOrSelects = new LinkedList<>();

            int indent = 1;

            StringBuilder result = new StringBuilder();
            StringTokenizer tokens;
            String lastToken;
            String token;
            String lcToken;

            public FormatProcess(String sql) {
                tokens = new StringTokenizer(
                        sql,
                        "()+*/-=<>'`\"[]," + WHITESPACE,
                        true
                );
            }

            public String perform() {

                result.append(INITIAL);

                while (tokens.hasMoreTokens()) {
                    token = tokens.nextToken();
                    lcToken = token.toLowerCase(Locale.ROOT);

                    if ("'".equals(token)) {
                        String t = com.baomidou.mybatisplus.core.toolkit.StringUtils.EMPTY;
                        do {
                            try {
                                t = tokens.nextToken();
                            } catch (Exception ignored) {
                            }
                            token += t;
                        }
                        // cannot handle single quotes
                        while (!"'".equals(t) && tokens.hasMoreTokens());
                    } else if (StringPool.QUOTE.equals(token)) {
                        String t;
                        do {
                            t = tokens.nextToken();
                            token += t;
                        }
                        while (!StringPool.QUOTE.equals(t));
                    }

                    if (afterByOrSetOrFromOrSelect && StringPool.COMMA.equals(token)) {
                        commaAfterByOrFromOrSelect();
                    } else if (afterOn && StringPool.COMMA.equals(token)) {
                        commaAfterOn();
                    } else if (StringPool.LEFT_BRACKET.equals(token)) {
                        openParen();
                    } else if (StringPool.RIGHT_BRACKET.equals(token)) {
                        closeParen();
                    } else if (BEGIN_CLAUSES.contains(lcToken)) {
                        beginNewClause();
                    } else if (END_CLAUSES.contains(lcToken)) {
                        endNewClause();
                    } else if ("select".equals(lcToken)) {
                        select();
                    } else if (DML.contains(lcToken)) {
                        updateOrInsertOrDelete();
                    } else if ("values".equals(lcToken)) {
                        values();
                    } else if ("on".equals(lcToken)) {
                        on();
                    } else if (afterBetween && "and".equals(lcToken)) {
                        misc();
                        afterBetween = false;
                    } else if (LOGICAL.contains(lcToken)) {
                        logical();
                    } else if (isWhitespace(token)) {
                        white();
                    } else {
                        misc();
                    }

                    if (!isWhitespace(token)) {
                        lastToken = lcToken;
                    }

                }
                return result.toString();
            }

            private void commaAfterOn() {
                out();
                indent--;
                newline();
                afterOn = false;
                afterByOrSetOrFromOrSelect = true;
            }

            private void commaAfterByOrFromOrSelect() {
                out();
                newline();
            }

            private void logical() {
                if ("end".equals(lcToken)) {
                    indent--;
                }
                newline();
                out();
                beginLine = false;
            }

            private void on() {
                indent++;
                afterOn = true;
                newline();
                out();
                beginLine = false;
            }

            private void misc() {
                out();
                if ("between".equals(lcToken)) {
                    afterBetween = true;
                }
                if (afterInsert) {
                    newline();
                    afterInsert = false;
                } else {
                    beginLine = false;
                    if ("case".equals(lcToken)) {
                        indent++;
                    }
                }
            }

            private void white() {
                if (!beginLine) {
                    result.append(StringPool.SPACE);
                }
            }

            private void updateOrInsertOrDelete() {
                out();
                indent++;
                beginLine = false;
                if ("update".equals(lcToken)) {
                    newline();
                }
                if ("insert".equals(lcToken)) {
                    afterInsert = true;
                }
            }

            private void select() {
                out();
                indent++;
                newline();
                parenCounts.addLast(parensSinceSelect);
                afterByOrFromOrSelects.addLast(afterByOrSetOrFromOrSelect);
                parensSinceSelect = 0;
                afterByOrSetOrFromOrSelect = true;
            }

            private void out() {
                result.append(token);
            }

            private void endNewClause() {
                if (!afterBeginBeforeEnd) {
                    indent--;
                    if (afterOn) {
                        indent--;
                        afterOn = false;
                    }
                    newline();
                }
                out();
                if (!"union".equals(lcToken)) {
                    indent++;
                }
                newline();
                afterBeginBeforeEnd = false;
                afterByOrSetOrFromOrSelect = "by".equals(lcToken)
                        || "set".equals(lcToken)
                        || "from".equals(lcToken);
            }

            private void beginNewClause() {
                if (!afterBeginBeforeEnd) {
                    if (afterOn) {
                        indent--;
                        afterOn = false;
                    }
                    indent--;
                    newline();
                }
                out();
                beginLine = false;
                afterBeginBeforeEnd = true;
            }

            private void values() {
                indent--;
                newline();
                out();
                indent++;
                newline();
                afterValues = true;
            }

            private void closeParen() {
                parensSinceSelect--;
                if (parensSinceSelect < 0) {
                    indent--;
                    parensSinceSelect = parenCounts.removeLast();
                    afterByOrSetOrFromOrSelect = afterByOrFromOrSelects.removeLast();
                }
                if (inFunction > 0) {
                    inFunction--;
                    out();
                } else {
                    if (!afterByOrSetOrFromOrSelect) {
                        indent--;
                        newline();
                    }
                    out();
                }
                beginLine = false;
            }

            private void openParen() {
                if (isFunctionName(lastToken) || inFunction > 0) {
                    inFunction++;
                }
                beginLine = false;
                if (inFunction > 0) {
                    out();
                } else {
                    out();
                    if (!afterByOrSetOrFromOrSelect) {
                        indent++;
                        newline();
                        beginLine = true;
                    }
                }
                parensSinceSelect++;
            }

            private static boolean isFunctionName(String tok) {
                final char begin = tok.charAt(0);
                final boolean isIdentifier = Character.isJavaIdentifierStart(begin) || '"' == begin;
                return isIdentifier &&
                        !LOGICAL.contains(tok) &&
                        !END_CLAUSES.contains(tok) &&
                        !QUANTIFIERS.contains(tok) &&
                        !DML.contains(tok) &&
                        !MISC.contains(tok);
            }

            private static boolean isWhitespace(String token) {
                return WHITESPACE.contains(token);
            }

            private void newline() {
                result.append(StringPool.NEWLINE);
                for (int i = 0; i < indent; i++) {
                    result.append(INDENT_STRING);
                }
                beginLine = true;
            }
        }

    }
    interface StringPool {

        String AMPERSAND = "&";
        String AND = "and";
        String AT = "@";
        String ASTERISK = "*";
        String STAR = ASTERISK;
        String BACK_SLASH = "\\";
        String COLON = ":";
        String COMMA = ",";
        String DASH = "-";
        String DOLLAR = "$";
        String DOT = ".";
        String DOTDOT = "..";
        String DOT_CLASS = ".class";
        String DOT_JAVA = ".java";
        String DOT_XML = ".xml";
        String EMPTY = "";
        String EQUALS = "=";
        String FALSE = "false";
        String SLASH = "/";
        String HASH = "#";
        String HAT = "^";
        String LEFT_BRACE = "{";
        String LEFT_BRACKET = "(";
        String LEFT_CHEV = "<";
        String NEWLINE = "\n";
        String N = "n";
        String NO = "no";
        String NULL = "null";
        String OFF = "off";
        String ON = "on";
        String PERCENT = "%";
        String PIPE = "|";
        String PLUS = "+";
        String QUESTION_MARK = "?";
        String EXCLAMATION_MARK = "!";
        String QUOTE = "\"";
        String RETURN = "\r";
        String TAB = "\t";
        String RIGHT_BRACE = "}";
        String RIGHT_BRACKET = ")";
        String RIGHT_CHEV = ">";
        String SEMICOLON = ";";
        String SINGLE_QUOTE = "'";
        String BACKTICK = "`";
        String SPACE = " ";
        String TILDA = "~";
        String LEFT_SQ_BRACKET = "[";
        String RIGHT_SQ_BRACKET = "]";
        String TRUE = "true";
        String UNDERSCORE = "_";
        String UTF_8 = "UTF-8";
        String US_ASCII = "US-ASCII";
        String ISO_8859_1 = "ISO-8859-1";
        String Y = "y";
        String YES = "yes";
        String ONE = "1";
        String ZERO = "0";
        String DOLLAR_LEFT_BRACE = "${";
        String HASH_LEFT_BRACE = "#{";
        String CRLF = "\r\n";

        String HTML_NBSP = "&nbsp;";
        String HTML_AMP = "&amp";
        String HTML_QUOTE = "&quot;";
        String HTML_LT = "&lt;";
        String HTML_GT = "&gt;";

        // ---------------------------------------------------------------- array

        String[] EMPTY_ARRAY = new String[0];

        byte[] BYTES_NEW_LINE = com.baomidou.mybatisplus.core.toolkit.StringPool.NEWLINE.getBytes();
    }
}
