package com.pes.jd.framework;


import com.alibaba.fastjson.JSON;
import com.pes.jd.util.CommonDateUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.annotation.PostConstruct;
import java.lang.annotation.Annotation;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/20 2:24 PM
 * @since 1.0.0
 *
 *  自定义框架的一些内容
 *
 */
@SuppressWarnings("ALL")
@Configuration
public class WebConfigBean {

    private final RequestMappingHandlerAdapter requestMappingHandlerAdapter;

    public WebConfigBean(RequestMappingHandlerAdapter requestMappingHandlerAdapter) {
        this.requestMappingHandlerAdapter = requestMappingHandlerAdapter;
    }

    @PostConstruct
    public void init(){
        List<HandlerMethodArgumentResolver> argumentResolvers = requestMappingHandlerAdapter.getArgumentResolvers();
        argumentResolvers = new ArrayList<>(argumentResolvers);
        argumentResolvers.add(0,new FormatterDateArgumentResolver());
        argumentResolvers.add(0,new JsonUrlencodedHandlerMethodArgumentResolver());
        requestMappingHandlerAdapter.setArgumentResolvers(argumentResolvers);

    }

    private static class FormatterDateArgumentResolver implements HandlerMethodArgumentResolver{

        @Override
        public boolean supportsParameter(MethodParameter parameter) {
            Class<?> parameterType = parameter.getParameterType();
            boolean isDate = parameterType.equals(Date.class)
                    || parameterType.equals(LocalDateTime.class)
                    || parameterType.equals(LocalDate.class)
                    || parameterType.equals(LocalTime.class);
            return isDate;
        }

        @SuppressWarnings("Duplicates")
        @Override
        public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                      NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
            String value = webRequest.getParameter(parameter.getParameterName());
            Annotation[] parameterAnnotations = parameter.getParameterAnnotations();
            Class<?> parameterType = parameter.getParameterType();
            for (Annotation parameterAnnotation : parameterAnnotations) {
                if (parameterAnnotation instanceof DateFormat){
                    DateFormat annotation = (DateFormat) parameterAnnotation;
                    DateFormat.FormatPattern anVal = annotation.value();
                    DateTimeFormatter formatter = anVal.getFormatter();
                    if (parameterType.equals(Date.class)){
                        return doParseDate(value, formatter,annotation.required());
                    }
                    // ignore com.pes.jd.framework.DateFormat.FormatPattern
                    if (parameterType.equals(LocalDateTime.class)){
                        return doParseLocalDateTime(value, annotation.required());
                    }
                    if (parameterType.equals(LocalDate.class)){
                        return doParseLocalDate(value,annotation.required());
                    }
                    if (parameterType.equals(LocalTime.class)){
                        return doParseLocalTime(value,annotation.required());
                    }
                }
            }
            if (parameterType.equals(Date.class)) {
                return doParseDate(value,false);
            }
            if (parameterType.equals(LocalDateTime.class)){
                return doParseLocalDateTime(value,false);
            }
            if (parameterType.equals(LocalDate.class)){
                return doParseLocalDate(value,false);
            }
            if (parameterType.equals(LocalTime.class)){
                return doParseLocalTime(value,false);
            }
            return null;
        }

        private Object doParseDate(String value, DateTimeFormatter formatter,boolean required) {
            try {
                return CommonDateUtils.parseYMd(value,formatter);
            }catch (Exception e){
                if (required){
                    throw e;
                }
                return null;
            }
        }

        private Object doParseDate(String value,boolean required) {
            try {
                if (value.length()==10)
                return CommonDateUtils.parseYMd(value);
                return CommonDateUtils.parseYMdHms(value);
            }catch (Exception e){
                if (required){
                    throw e;
                }
                return null;
            }
        }
        private Object doParseLocalDateTime(String value,boolean required) {
            try {
                return CommonDateUtils.parseLocalDateTime(value);
            }catch (Exception e){
                if (required){
                    throw e;
                }
                return null;
            }
        }
        private Object doParseLocalDate(String value,boolean required) {
            try {
                return CommonDateUtils.parseLocalDate(value);
            }catch (Exception e){
                if (required){
                    throw e;
                }
                return null;
            }
        }

        private Object doParseLocalTime(String value,boolean required) {
            try {
                return CommonDateUtils.parseLocalTime(value);
            }catch (Exception e){
                if (required){
                    throw e;
                }
                return null;
            }
        }

        @Override
        public String toString() {
            return "FormatterDateArgumentResolver{}";
        }
    }

    private static class JsonUrlencodedHandlerMethodArgumentResolver extends AbstractNamedValueMethodArgumentResolver {

        private Set<Class> primitive = new HashSet<>(
                Arrays.asList(String.class,Integer.class,Float.class,Double.class)
        );

        private Set<String> pickOut = new HashSet<>(
                Arrays.asList("[]","{}","")
        );

        @Override
        public boolean supportsParameter(MethodParameter parameter) {
            FormUrlencoded urlEncodeBody = parameter.getParameterAnnotation(FormUrlencoded.class);
            if (Objects.nonNull(urlEncodeBody)){
                return true;
            }
            return false;
        }


        @Override
        protected NamedValueInfo createNamedValueInfo(MethodParameter parameter) {
            return new NamedValueInfo(parameter.getParameterName(),true,null);
        }

        protected boolean pickOut(String value){
            if (value == null){
                return true;
            }
            return pickOut.contains(value);
        }

        @Override
        protected void handleMissingValue(String name, MethodParameter parameter, NativeWebRequest request) throws Exception {
            // ignore
        }

        @Override
        protected Object resolveName(String name, MethodParameter parameter, NativeWebRequest request) throws Exception {
            String value = request.getParameter(name);
            if (pickOut(value)){
                return null;
            }
            Class<?> type = parameter.getParameterType();
            Type genericParameterType = parameter.getGenericParameterType();
            if (List.class.isAssignableFrom(type)){
                if (genericParameterType instanceof ParameterizedType){
                    try {
                        final Type[] actualTypeArguments = ((ParameterizedType) genericParameterType).getActualTypeArguments();
                        if (actualTypeArguments.length == 1){
                            final Type actualTypeArgument = actualTypeArguments[0];
                            if (primitive.contains(actualTypeArgument) && actualTypeArgument instanceof Class){
                                final Class actualCla = (Class) actualTypeArgument;
                                return JSON.parseArray(value,actualCla);
                            }
                        }
                        if (actualTypeArguments.length==1){
                            return JSON.parseArray(value,(Class)actualTypeArguments[0]);
                        }
                        return JSON.parseArray(value, actualTypeArguments);
                    } catch (Exception e) {
                        throw new ParseException(String.format(
                                "json content \"%s\" convert to %s error \n The method is : %s",value,type,parameter.getMethod()
                        ));
                    }
                }
            }
            try {
                return JSON.parseObject(value, type);
            } catch (Exception e) {
                throw new ParseException(String.format(
                        "json content \n %s \n convert to %s error \n The method is : %s",value,type,parameter.getMethod()
                ));
            }
        }

    }

    private static class ParseException extends RuntimeException{
        public ParseException(String msg) {
            super(msg);
        }
    };

}
