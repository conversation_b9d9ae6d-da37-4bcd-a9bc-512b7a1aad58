package com.pes.jd.framework;

import java.lang.annotation.*;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/20 2:10 PM
 * @since 1.0.0
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DateFormat {

    FormatPattern value() default FormatPattern.YMD ;

    enum FormatPattern {
        /**
         *  yyyy-MM-dd
         */
        YMD(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
        /**
         * yyyy-MM-dd hh:mm:ss
         */
        YMDHMS(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss"));

        private DateTimeFormatter formatter;

        FormatPattern(DateTimeFormatter formatter) {
            this.formatter = formatter;
        }

        public DateTimeFormatter getFormatter() {
            return formatter;
        }
    }

}
