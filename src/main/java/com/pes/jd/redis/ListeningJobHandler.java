package com.pes.jd.redis;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pes.jd.Constants.JobPullRecordStatusConstants;
import com.pes.jd.business.ShopManageBusiness;
import com.pes.jd.config.JdYunDingConfig;
import com.pes.jd.dao.JobRecordDao;
import com.pes.jd.model.DO.JobCalRecordDO;
import com.pes.jd.model.DO.JobPullRecordDO;
import com.pes.jd.model.DO.Shop;
import com.pes.jd.model.DTO.JobCalRecordDTO;
import com.pes.jd.model.DTO.JobPullRecordDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.task.executor.TaskJobExecutor;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.SpringUtil;
import com.yiyitech.support.datasource.RoutingDataSource;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: aiJun
 * @Date: 2021/1/13 0013 10:52
 * @desc 项目启动后执行 从redis中拉取需要消费的店铺
 * @Version 1.0
 */
@Component
@RefreshScope
public class ListeningJobHandler implements ApplicationRunner {
    private static Logger logger = LoggerFactory.getLogger(ListeningJobHandler.class);
    @Resource
    private RedisCache redisCache;
    private JdYunDingConfig jdYunDingConfig;
    @Value("#{'${yiyitech.redis.pull.key}'.split(',')}")
    private List<String> jobKeys;
    @Value("#{'${yiyitech.redis.pull.key.handle}'.split(',')}")
    private List<String> jobKeyHandles;
    private ShopManageBusiness shopManageBusiness;

    @Resource
    private JobRecordDao jobRecordDao;

    private Integer redisDBNum = 2;
    @Override
    public void run(ApplicationArguments applicationArguments) {
        new Thread(() -> {
            while (true) {
                try {
                    //先将全部店铺状态置为未执行
                    initShopRunStatus();
                } catch (Exception e) {
                    logger.error("店铺状态置为未执行失败" + e.getMessage(), e);
                }
            }
        }).start();
        new Thread(() -> {
            while (true) {
                try {
                    for (String handleKey : jobKeyHandles) {
                        String handleJobKey = handleKey + "-jobHandle";
                        String message = redisCache.lpop(handleJobKey, redisDBNum);
                        if (StringUtils.isNotEmpty(message)) {
                            JSONObject param = JSONUtil.parseObj(message);
                            String shopId = (String) param.get("shopId");
                            String type = (String) param.get("type");
                            logger.info("redis收到消息{},类型:{},参数:{}",shopId,type,param);
                            if (shopManageBusiness == null) {
                                shopManageBusiness = SpringUtil.getBean(ShopManageBusiness.class);
                            }
                            long s = System.currentTimeMillis();
                            switch (TaskJobDispatchEnum.TYPE_MAP.get(type)) {
                                case SHOP_DATA_PULL_AND_CAL:
                                    logger.info("店铺数据（拉取 && 计算）" + message);
                                    //线程池队列
                                    TaskJobExecutor executorPullAndCal = new TaskJobExecutor(message);
                                    executorPullAndCal.run();
                                    break;
                                case SHOP_DATA_CAL:
                                    logger.info("店铺数据（计算）" + message);
                                    TaskJobExecutor executorCal = new TaskJobExecutor(message);
                                    executorCal.run();
                                    break;
                                case SHOP_DATA_INIT:
                                    System.out.println("店铺初始化（拉取 && 计算）" + message);
                                    TaskJobExecutor taskJobExecutor = new TaskJobExecutor(message);
                                    taskJobExecutor.run();
                                    break;

                            }
                            long e = System.currentTimeMillis();
                            logger.info("当前店铺执行完成  --> run shopId={} 耗时={}s", shopId, (e - s) / 1000);
                        } else {
                            logger.debug("暂无消息消费 key={}", handleJobKey);
                            TimeUnit.SECONDS.sleep(3);
                        }
                }
                } catch (Exception e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }).start();
    }

    private void initShopRunStatus() throws Exception {
        for (String jobKey : jobKeys) {
            String message = redisCache.lpop(jobKey, redisDBNum);
            if (StrUtil.isNotEmpty(message)) {
                if (message.startsWith("\"") && message.endsWith("\"")) {
                    // 去除首尾的引号，并处理内部的转义字符
                    message = message.substring(1, message.length() - 1);
                    message = message.replace("\\\"", "\"")
                            .replace("\\\\", "\\");
                }
                JSONObject param = JSONUtil.parseObj(message);
                String shopId = (String) param.get("shopId");
                String startDateStr = (String) param.get("startDateStr");
                String endDateStr = (String) param.get("endDateStr");
                logger.info("redis收到消息{},参数:{}",param);
                shopManageBusiness = SpringUtil.getBean(ShopManageBusiness.class);
                JobShopQuery jobShop = shopManageBusiness.getJobShop(Long.valueOf(shopId), 1);
                //记录任务，状态：未执行
                if (jobShop == null) {
                    logger.info("jobshop == null");
                }
                handleJobPullRecord(jobShop.getShop(), startDateStr, endDateStr);

                String handleJobKey = jobKey+"_handle" + "-jobHandle";
                redisCache.rpush(handleJobKey, redisDBNum, message);
            } else {
                TimeUnit.SECONDS.sleep(3);
                logger.debug("暂无消息消费 key={}", jobKey);
            }
        }
    }

    private void handleJobPullRecord(JobShopDTO shop, String startDateStr, String endDateStr) {

        Date startDate;
        Date endDate;
        try {
            startDate = DateUtil.getStartTimeOfDate(DateFormatUtils.parseYMd(startDateStr));
            endDate = DateUtil.getEndTimeOfDate(DateFormatUtils.parseYMd(endDateStr));
            List<Date> dates = DateUtil.splitDate(startDate, endDate);
            if (CollectionUtils.isNotEmpty(dates)) {
                for (Date date : dates) {
                    handleJobPullRecord(shop, date);
                    handleJobCalRecord(shop, date);
                }
            }
        } catch (Exception e) {
            logger.error("[{}] insert job pull record fail", shop.getTitle(), e);
        }
    }


    private void handleJobPullRecord(JobShopDTO shop, Date date) {
        if(jobRecordDao == null){
            jobRecordDao = SpringUtil.getBean(JobRecordDao.class);
        }
        JobPullRecordDTO existPullRecord = jobRecordDao.getJobPullRecordByShopIdAndDate(shop, date);
        if(existPullRecord != null && JobPullRecordStatusConstants.EXECUTING.equals(existPullRecord.getRunStatus())){
            logger.info("[{}] 任务进行中，放弃此次重复新任务！", shop.getTitle());
            return;
        }
        JobPullRecordDO pullRecord = new JobPullRecordDO(shop.getShopId(), date, Boolean.FALSE, JobPullRecordStatusConstants.UNEXECUTED);
        pullRecord.init();//初始化值
        jobRecordDao.deleteJobPullRecordByShopIdAndDate(shop, date);
        jobRecordDao.insertPullJobRecord(shop, pullRecord, date);
    }

    //feature_v28 初始化的时候补一条数据
    private void handleJobCalRecord(JobShopDTO shop, Date date){
        JobCalRecordDTO existCalRecord = jobRecordDao.getJobCalRecordByShopIdAndDate(shop, date);
        if(existCalRecord != null){
            logger.info("[{}], cal，任务进行中，放弃此次重复新任务！", shop.getTitle());
            return;
        }
        JobCalRecordDO calRecord = new JobCalRecordDO(shop.getShopId(), date, new Date());
        calRecord.setResult(Boolean.FALSE);
        calRecord.setMsg("每日Job预生成");
        jobRecordDao.deleteJobCalRecordByShopIdAndDate(shop, date);
        jobRecordDao.insertJobCalRecord(shop, calRecord, date);
    }


    private boolean isYd(Long shopId, Boolean isYdStr) {
        if (jdYunDingConfig == null) jdYunDingConfig = SpringUtil.getBean(JdYunDingConfig.class);
        if (Objects.isNull(isYdStr) || Objects.isNull(shopId))
            return false;
        else return jdYunDingConfig.isOnOrOff() && isYdStr;
    }
}
