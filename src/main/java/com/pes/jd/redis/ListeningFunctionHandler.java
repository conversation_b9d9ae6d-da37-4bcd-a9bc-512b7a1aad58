package com.pes.jd.redis;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.ms.constant.enumConstant.TaskJobDispatchEnum;
import com.pes.jd.task.executor.*;
import com.yiyitech.support.redis.RedisCache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@RefreshScope
public class ListeningFunctionHandler implements ApplicationRunner {
    private static Logger logger = LoggerFactory.getLogger(ListeningFunctionHandler.class);

    @Resource
    private RedisCache redisCache;
    @Value("${yiyitech.redis.function.key}")
    private String key;

    private Integer redisDBNum = 2;

    private ExecutorService taskJobExecutorForPVUV;

    private ExecutorService taskJobExecutorForDutyLog;

    private ExecutorService taskJobExecutorForChatLog;

    private ExecutorService taskJobExecutorForGoodSku;

    private ExecutorService taskJobExecutorForPullAndRefundData;

    private ExecutorService taskJobExecutorForOrderEvaluate;

    private ExecutorService taskJobExecutorForShopUserCondition;

    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForPVUV = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForDutyLog = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForChatLog = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForGoodSku = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForPullAndRefundData = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new NamedThreadFactory("self-job-thread-", true));
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForOrderEvaluate = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }
    {
        final int corePoolSize = 1;
        final int maximumPoolSize = 2;
        taskJobExecutorForShopUserCondition = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L,
                TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    }

    @Override
    public void run(ApplicationArguments applicationArguments) throws Exception {
        logger.info("处理定时任务功能代码启动成功");
        new Thread(() -> {
            logger.info("线程开始");
            while (true) {
                try{
                    String message = redisCache.lpop(key, redisDBNum);
                    if(StringUtils.isNotBlank(message)){
                        JSONObject msgParam = JSON.parseObject(message);
                        String type = msgParam.getString("type");

                        switch (TaskJobDispatchEnum.TYPE_MAP.get(type)) {
                            case TASK_JOB_PVUV:
                                taskJobExecutorForPVUV.execute(new TaskJobPvUvExecutor(message));
                                break;
                            case TASK_JOB_DUTYLOG:
                                taskJobExecutorForDutyLog.execute(new TaskLoginLogDataByHoursJobExecutor(message));
                                break;
                            case TASK_JOB_GOODSKU:
                                taskJobExecutorForGoodSku.execute(new TaskJobGoodSkuExecutor(message));
                                break;
                            case TASK_JOB_PULLCHATLOG:
                                taskJobExecutorForChatLog.execute(new TaskJobChatLogExecutor(message));
                                break;
                            case TASK_JOB_CALCHATLOG:
                                taskJobExecutorForChatLog.execute(new TaskJobChatLogExecutor(message));
                                break;
                            case TASK_JOB_PULLANDCALREFUNDDATA:
                                taskJobExecutorForPullAndRefundData.execute(new TaskJobPullAndCalRefundExecutor(message));
                                break;
                            case TASK_JOB_CAL_ORDER_EVALUATE:
                                taskJobExecutorForOrderEvaluate.execute(new TaskJobCalOrderEvaluateExecutor(message));
                                break;
                            case TASK_JOB_PULL_SHOP_USER_CONDITION:
                                taskJobExecutorForShopUserCondition.execute(new TaskShopUserConditionJobExecutor(message));
                                break;
                            case TASK_JOB_CAL_SHOP_USER_CONDITION:
                                taskJobExecutorForShopUserCondition.execute(new TaskShopUserConditionJobExecutor(message));
                                break;
                        }
                    }else{
                        TimeUnit.SECONDS.sleep(10);
                    }
                }catch (Exception e){
                    logger.error("ListeningFunctionHandler error :{}", e.getMessage(), e);
                }
            }
        }).start();
    }
}
