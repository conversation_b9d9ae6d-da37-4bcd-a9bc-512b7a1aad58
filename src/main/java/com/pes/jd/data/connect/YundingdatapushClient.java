package com.pes.jd.data.connect;

import com.jdcloud.sdk.auth.CredentialsProvider;
import com.jdcloud.sdk.auth.StaticCredentialsProvider;
import com.jdcloud.sdk.http.HttpRequestConfig;
import com.jdcloud.sdk.http.Protocol;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/4/28
 * @Modified By:
 */
@Component
public class YundingdatapushClient extends AbstrctYundingdatapushClient {

    @Autowired
    private YundingdatapushConfig yundingdatapushConfig;

    @Override
    public com.jdcloud.sdk.service.yundingdatapush.client.YundingdatapushClient getYundingdatapushClient() {
        CredentialsProvider credentialsProvider = new StaticCredentialsProvider(
                yundingdatapushConfig.getAccessKeyId(), yundingdatapushConfig.getSecretAccessKey());
        com.jdcloud.sdk.service.yundingdatapush.client.YundingdatapushClient client = com.jdcloud.sdk.service.yundingdatapush.client.YundingdatapushClient.builder()
                .credentialsProvider(credentialsProvider)
                .httpRequestConfig(new HttpRequestConfig.Builder().protocol(Protocol.HTTPS).build()) //默认为HTTPS
                .build();
        return client;
    }
}
