package com.pes.jd.data.converter;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.im.OpenApiService.response.get.WaiterPresence;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.CsLoginlogDao;
import com.pes.jd.data.api.LoginlogOperator;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.CsLoginlogDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Enum.DateEnum;
import com.pes.jd.model.TO.CsLoginlogResultTO;
import com.pes.jd.util.CollectionUtil;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**  
 * ClassName:LoginlogDateConverter <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月31日 上午9:51:42 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Service
public class LoginlogDataConverter {
	private static final Logger logger = LoggerFactory.getLogger(LoginlogDataConverter.class);
	
	@Resource
	private LoginlogOperator loginlogOperator;
	
	@Resource
	private CsLoginlogDao csLonginlogDao;
	
	public void loginlogDateConverter(JobShopDTO shop, List<CsDTO> shopCsLst, Date date, Integer delayTime, boolean isDelData) throws Exception{
		Long shopId = shop.getShopId();
		if(shopCsLst.isEmpty()){
			//店铺账号查询为空
			logger.error("~~~~~~~~~~~~~~~~~shopId:{},shopCsLst isEntry,loginlog pull fail",shopId);
			return;
		}
		//-----------------------
		Date startDate = DateUtil.getStartTimeOfDate(date);
		Date endDate = DateUtil.getEndTimeOfDate(date);
		logger.info("进入登陆记录数据转换类：店铺{},登陆记录数据拉取,转换开始",shopId);

		/**************判断拉取秒级接口还是分钟级接口 并且拉取start*****************/
		CsLoginlogResultTO loginLogResult = doPullLoginLog(shop, delayTime, shopCsLst,
				startDate, endDate);
		/**************判断拉取秒级接口还是分钟级接口 并且拉取end*****************/
		if(logger.isDebugEnabled()){
			logger.debug("~~~~~~~~~~~~~~~~~店铺{},登陆记录数据拉取结束", shopId);
		}
		List<WaiterPresence> loginLogRet = loginLogResult.getLoginLogRet();
		List<CsLoginlogDTO> loginLst = Lists.newArrayList();//同一个月的数据
		List<CsLoginlogDTO> nextMonthLoginLst = Lists.newArrayList();//下个月的数据
		
		//删除历史数据的时候需要延迟时间段
		startDate = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(startDate.getTime() + delayTime * 60 * 60 * 1000));
		endDate = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(endDate.getTime() + delayTime * 60 * 60 * 1000));
		Boolean isSameMonth = DateUtil.getTimeSameMonth(startDate, endDate);
		if (CollectionUtils.isNotEmpty(loginLogRet)) {
			if(logger.isDebugEnabled()){
				logger.debug("~~~~~~~~~~~~~~~~~店铺{},登陆记录数据转换开始", shopId);
			}

			for (WaiterPresence passLog : loginLogRet) {
				CsLoginlogDTO loginlogDO = new CsLoginlogDTO();
				loginlogDO.setShopId(shopId);
				loginlogDO.setCsNick(passLog.getWaiter());
				loginlogDO.setChangeTime(passLog.getChangeTime());
				loginlogDO.setType(passLog.getType());
				if (isSameMonth || DateUtil.getTimeSameMonth(passLog.getChangeTime(), startDate)) {
					loginLst.add(loginlogDO);
				} else {
					nextMonthLoginLst.add(loginlogDO);
				}
			}
		}
		if (CollectionUtils.isEmpty(loginLst) && CollectionUtils.isEmpty(nextMonthLoginLst)) {
			if(logger.isDebugEnabled()){
				logger.debug("~~~~~~~~~~~~~~~~~店铺{},登陆记录转换数据为空", shopId);
			}

		} else {
			// 转换的数据插入数据库
			if (isDelData) {
				int deleteNum = csLonginlogDao.deleteCsLoginlogByDateByShopId(shop, startDate, endDate);
				if(logger.isDebugEnabled()){
					logger.debug("~~~~~~~~~~~~~~~~~店铺{}指定更新时间内删除的登陆记录条数为：{}", shopId, deleteNum);
				}

			}
			List<List<CsLoginlogDTO>> allLoginlogLst = CollectionUtil.smallToLst(loginLst, CommonConstants.BATCH_INSERT_LIMIT_NUM);
			int insertNum = 0;
			for (List<CsLoginlogDTO> loginlogLst : allLoginlogLst) {
				insertNum += csLonginlogDao.insertCsLoginlogList(shop, startDate, loginlogLst);
			}
			if (CollectionUtils.isNotEmpty(nextMonthLoginLst)) {
				insertNum += csLonginlogDao.insertCsLoginlogList(shop, endDate, nextMonthLoginLst);
			}
			if(logger.isDebugEnabled()){
				logger.debug("~~~~~~~~~~~~~~~~~店铺{}插入的登陆记录条数为：{},登陆记录数据转换结束", shopId, insertNum);
			}

		}
	}

	private CsLoginlogResultTO doPullLoginLog(JobShopDTO shop, Integer delayTime, List<CsDTO> shopCsLst, Date startDate, Date endDate) throws Exception {
		//秒级接口有数据之前调用分钟级接口
		if (startDate.after(DateFormatUtils.parseYMd(DateEnum.DATE_PULL_SECONDS.getDateStr()))) {
			//调秒级
			return loginlogOperator.pullLoginLogForSEcondsApi(shop, delayTime, shopCsLst,
					startDate, endDate);
		} else {
			//分钟级
			return loginlogOperator.pullLoginLog(shop, delayTime, shopCsLst,
					startDate, endDate);
		}


	}

}
  
