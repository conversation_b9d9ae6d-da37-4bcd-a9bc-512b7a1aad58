package com.pes.jd.data.converter;

import com.jd.open.api.sdk.domain.evaluation.PopCommentJsfService.response.getVenderCommentsForJos.PopCommentJosVo;
import com.jd.security.tde.MKData;
import com.jd.security.tde.ServiceKeyInfo;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.OrderDao;
import com.pes.jd.dao.ShopGoodsReviewDao;
import com.pes.jd.data.api.ShopGoodsReviewOperator;
import com.pes.jd.model.BO.ShopGoodsReviewBO;
import com.pes.jd.model.DO.CsNickAndOrderIdDo;
import com.pes.jd.model.DO.ShopGoodsReviewDO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobShopQuery;
import com.pes.jd.util.DateUtil;
import com.pes.jd.util.JdApiEncryptionUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ShopGoodsReviewConverter {
    private static final Logger logger = LoggerFactory.getLogger(ShopGoodsReviewConverter.class);

    @Resource
    private ShopGoodsReviewOperator shopGoodsReviewOperator;

    @Resource
    private ShopGoodsReviewDao shopGoodsReviewDao;

    @Resource
    private OrderDao orderDao;

    public void persistShopGoodsReview(JobShopQuery jobShop, Date date)
            throws Exception {
        JobShopDTO shop = jobShop.getShop();
        Long shopId = shop.getShopId();
        String sessionKey = shop.getSessionKey();
        String schemaId = shop.getSchemaId();
        Date startDate = DateUtil.getStartTimeOfDate(date);
        Date endDate = DateUtil.getEndTimeOfDate(date);
        //根据当前时间（评价时间）往前推180天
        Date finalQueryStartDate = DateUtil.getStartTimeOfDate(DateUtil.getDateByPeriod(date, CommonConstants.DAY_EVAL_ORDER));

        // 数据转换
        long ss = System.currentTimeMillis();
        ShopGoodsReviewBO pullShopGoodsReview = shopGoodsReviewOperator.pullShopGoodsReview(shop, startDate,
                endDate);
        long ee = System.currentTimeMillis();
        if (logger.isDebugEnabled()) {
            logger.debug("拉取中差评-Draw a poor rating耗时：{}ms", (ee - ss));
        }

        List<PopCommentJosVo> shopGoodsReviewLst = pullShopGoodsReview.getShopGoodsReviewLst();
        List<ShopGoodsReviewDO> convertGoodsReviewLst = new ArrayList<ShopGoodsReviewDO>();
//		List<ShopGoodsReviewDO> insertGoodsReviewDOLst = new ArrayList<ShopGoodsReviewDO>();
        if (CollectionUtils.isNotEmpty(shopGoodsReviewLst)) {
            ShopGoodsReviewDO goodsReviewDO = null;
            for (PopCommentJosVo popCommentJosVo : shopGoodsReviewLst) {
                if (popCommentJosVo == null) {
//					logger.debug("数据转换层：评价接口返回数据存在null值，continue");
                    continue;
                }
                String skuId = popCommentJosVo.getSkuid();
                goodsReviewDO = new ShopGoodsReviewDO();
                goodsReviewDO.setSkuId(Long.parseLong(skuId == null ? "0" : skuId));
                goodsReviewDO.setShopId(shopId);
                goodsReviewDO.setScore(popCommentJosVo.getScore());
                goodsReviewDO.setBuyerNick(popCommentJosVo.getNickName());
                goodsReviewDO.setSendTime(popCommentJosVo.getCreationTime());
                goodsReviewDO.setIsReply(popCommentJosVo.getIsVenderReply());
                goodsReviewDO.setContent(popCommentJosVo.getContent());
                goodsReviewDO.setStatus(popCommentJosVo.getStatus());
                String encryptOrderId = popCommentJosVo.getEncryptOrderId();
                if (StringUtils.isNotBlank(encryptOrderId)) {
                    ServiceKeyInfo josMasterKey = JdApiEncryptionUtils.getJosMasterKey(sessionKey);
                    if (null != josMasterKey) {
                        for (MKData mkData : josMasterKey.getKeys()) {
                            //解密订单Id
                            String consigneeTelp = JdApiEncryptionUtils.jdDecoder(mkData.getId(),
                                    mkData.getKey_string(), encryptOrderId);
                            if (StringUtils.isNotBlank(consigneeTelp)) {
                                Long orderId = Long.valueOf(consigneeTelp);
                                goodsReviewDO.setOrderId(orderId);
                                break;
                            }
                        }
                    }
                }

                convertGoodsReviewLst.add(goodsReviewDO);
            }

            Set<Long> orderIds = convertGoodsReviewLst.stream().map(ShopGoodsReviewDO::getOrderId).collect(Collectors.toSet());
            List<CsNickAndOrderIdDo> csNickAndOrderIdDos = orderDao.selectBuyerNickAndOrderIdByOrderIds(shopId, schemaId,finalQueryStartDate,endDate,orderIds);
            if (CollectionUtils.isNotEmpty(convertGoodsReviewLst) && CollectionUtils.isNotEmpty(csNickAndOrderIdDos)) {
                /*for (ShopGoodsReviewDO shopGoodsReviewDO : convertGoodsReviewLst) {
                    for (CsNickAndOrderIdDo csNickAndOrderIdDo : csNickAndOrderIdDos) {
                        if(csNickAndOrderIdDo.getOrderId().equals(shopGoodsReviewDO.getOrderId())){
                            shopGoodsReviewDO.setBuyerNick(csNickAndOrderIdDo.getBuyerNick());
                        }
                    }
                }*/

                List<ShopGoodsReviewDO> list = convertGoodsReviewLst.stream().map(convertGoodsReview -> csNickAndOrderIdDos.stream()
                        .filter(csNickAndOrderIdDo -> convertGoodsReview.getOrderId().equals(csNickAndOrderIdDo.getOrderId()))
                        .findFirst()
                        .map(csNickAndOrderIdDo -> {
                            convertGoodsReview.setBuyerNick(csNickAndOrderIdDo.getBuyerNick());
                            return convertGoodsReview;
                        }).orElse(null))
                        .collect(Collectors.toList());
            }


            //中差评基础数据入库
            if (CollectionUtils.isEmpty(convertGoodsReviewLst)) {
                if (logger.isDebugEnabled()) {
                    logger.debug("数据转换层：~~~~~~~~店铺{},评价接口可转换数据为空", shopId);
                }
            } else {
                // 转换的数据插入数据库
                int deleteNum = shopGoodsReviewDao.deleteShopGoodsReviewByDateByShopId(shop, date);
                if (logger.isDebugEnabled()) {
                    logger.debug("~~~~~~~~~~~~~~~~~店铺{}指定更新时间内删除的评价(中差评)记录条数为：{}", shopId, deleteNum);
                }
                int insertNum = shopGoodsReviewDao.insertShopGoodsReviewList(shop, date, convertGoodsReviewLst);
                if (logger.isDebugEnabled()) {
                    logger.debug("~~~~~~~~~~~~~~~~~店铺{}插入的登陆记录条数为：{},评价(中差评)记录数据转换结束", shopId, insertNum);
                }
            }

//			//------------------------------计算店铺维度中差评数量------------------------------
//			int goodEvaluateNum = 0;
//			int neutralEvaluateNum = 0;
//			int badEvaluateNum = 0;
//			for (ShopGoodsReviewDO shopGoodsReview : convertGoodsReviewLst) {
//				switch (shopGoodsReview.getScore()) {
//				case CommonConstants.EVALUATION_SCORE_GOOD_5:
//				case CommonConstants.EVALUATION_SCORE_GOOD_4:
//					goodEvaluateNum++;
//					break;
//				case CommonConstants.EVALUATION_SCORE_NEUTRAL_3:
//				case CommonConstants.EVALUATION_SCORE_NEUTRAL_2:
//					neutralEvaluateNum++;
//					break;
//				case CommonConstants.EVALUATION_SCORE_BAD_1:
//					badEvaluateNum++;
//					break;
//				default:
////					logger.debug("未定义的中差评分值 score = {}", shopGoodsReview.getScore());
//					break;
//				}
//			}
//			ShopOrderEvaluateDTO shopOrderEvaluate = new ShopOrderEvaluateDTO(shopId, date, goodEvaluateNum, neutralEvaluateNum, badEvaluateNum);
//
//			if (isDelData) {
//				int delNum = ShopOrderEvaluateDao.deleteShopOrderEvaluateByDateByShopId(shop,date);
//			}
//			int addNum = ShopOrderEvaluateDao.insertShopOrderEvaluate(shop,date,shopOrderEvaluate);
//			//------------------------------计算店铺维度中差评数量------------------------------

//			//------------------------------中差评匹配订单入库，未匹配到订单的评价直接入库------------------------------
//			Date queryOrderStartDate = DateUtil.getDateByPeriod(startDate, CommonConstants.DAY_EVAL_ORDER);
//
//			//查询中差评绑定订单
//			List<ShopGoodsReviewDO> selectOrdergoodsReviewList = orderDetailDao.selectOrderIdListBySkuidList(shop,queryOrderStartDate,endDate,convertGoodsReviewLst);
//			List<ShopGoodsReviewDO> ordergoodsReviewList = Optional.ofNullable(selectOrdergoodsReviewList).orElse(Lists.newArrayList());
//			Set<Long> csOrderIdSet = ordergoodsReviewList
//					.stream()
//					.map(ShopGoodsReviewDO::getOrderId)
//					.collect(Collectors.toSet());
//			List<Long> orderIdLst = Lists.newArrayList(csOrderIdSet);
//			//根据订单号查询接待客服
//			List<ShopGoodsReviewDO> shopGoodsCsLstByOrderId = csOrderBindDao.selectShopCsOrderBindCsNickByOrderIdLst(shop,queryOrderStartDate,endDate,orderIdLst);
//
//			List<ShopGoodsReviewDO> orderBindGoodsReviewLst = Lists.newArrayList();
//			//将中差评对应到接待客服
//			if(CollectionUtils.isEmpty(shopGoodsCsLstByOrderId)){
////				logger.debug("date={},根据订单评价查询到订单{}条,但并未查询到绑定客服",date,ordergoodsReviewList.size());
//				orderBindGoodsReviewLst.addAll(ordergoodsReviewList);
//			}else{
//				for (ShopGoodsReviewDO orderBindGoods : ordergoodsReviewList) {
//					for (int i = 0; i < shopGoodsCsLstByOrderId.size(); i++) {
//						ShopGoodsReviewDO csOrderbind = shopGoodsCsLstByOrderId.get(i);
//						if(csOrderbind.getOrderId().equals(orderBindGoods.getOrderId())){
//							orderBindGoods.setCsNick(csOrderbind.getCsNick());
//							orderBindGoodsReviewLst.add(orderBindGoods);
//							break;
//						}
//						if( i == shopGoodsCsLstByOrderId.size() - 1){
//							//未匹配到绑定客服，静默下单评价：客服为空
//							orderBindGoodsReviewLst.add(orderBindGoods);
//						}
//					}
//				}
//			}
//
//			if (CollectionUtils.isNotEmpty(orderBindGoodsReviewLst)) {
//				for (ShopGoodsReviewDO shopGoods : convertGoodsReviewLst) {
//					Boolean isQueryOrder = false;
//					for (ShopGoodsReviewDO orderGoods : orderBindGoodsReviewLst) {
//						if (shopGoods.getSkuId().equals(orderGoods.getSkuId())
//								&& shopGoods.getBuyerNick().equalsIgnoreCase(orderGoods.getBuyerNick())) {
//							isQueryOrder = true;
//							shopGoods.setOrderCreated(orderGoods.getOrderCreated());
//							shopGoods.setOrderPayTime(orderGoods.getOrderPayTime());
//							shopGoods.setOrderId(orderGoods.getOrderId());
//							shopGoods.setCsNick(orderGoods.getCsNick());
//							insertGoodsReviewDOLst.add(shopGoods);
//							break;
//						}
//					}
//					if(!isQueryOrder){
//						insertGoodsReviewDOLst.add(shopGoods);
//					}
//				}
//			}else{
//				insertGoodsReviewDOLst.addAll(convertGoodsReviewLst);
//			}
//		}
//		if (CollectionUtils.isEmpty(insertGoodsReviewDOLst)) {
////			logger.debug("数据转换层：~~~~~~~~店铺{},评价接口可转换数据为空", shopId);
//		} else {
//			// 转换的数据插入数据库
//			int deleteNum = shopGoodsReviewDao.deleteShopGoodsReviewByDateByShopId(shop, date);
////			logger.debug("~~~~~~~~~~~~~~~~~店铺{}指定更新时间内删除的评价(中差评)记录条数为：{}", shopId, deleteNum);
//			int insertNum = shopGoodsReviewDao.insertShopGoodsReviewList(shop, date, insertGoodsReviewDOLst);
////			logger.debug("~~~~~~~~~~~~~~~~~店铺{}插入的登陆记录条数为：{},评价(中差评)记录数据转换结束", shopId, insertNum);
        }
//		//------------------------------中差评匹配订单入库，未匹配到订单的评价直接入库------------------------------
    }
}
