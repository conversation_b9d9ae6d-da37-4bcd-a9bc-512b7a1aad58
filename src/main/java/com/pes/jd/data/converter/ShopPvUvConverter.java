package com.pes.jd.data.converter;

import com.jd.open.api.sdk.response.udp.MarketBdpOLShopSumQueryResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.dao.ShopPvUvDayDao;
import com.pes.jd.data.api.ShopPvUvOperator;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.ShopPvUvDayDTO;
import com.pes.jd.model.TO.ShopPvUvTO;
import com.pes.jd.util.DateFormatUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
public class ShopPvUvConverter {

	@Resource
	private ShopPvUvOperator shopPvUvOperator;
	
	@Resource
	private ShopPvUvDayDao shopPvUvDao;

	public void getShopPvUvDay(JobShopDTO shop, Date date, boolean isDelData)
			throws Exception {
		Long totalPv = 0L, totalUv = 0L;
		List<ShopPvUvDayDTO> shopPvUvDTOLst = new ArrayList<>();
		String[] tpTypeLst = CommonConstants.TP_TYPE.split(",");
		for (String tp : tpTypeLst) {
			ShopPvUvTO model = shopPvUvOperator.getShopPvUv(shop.getSessionKey(), tp, DateFormatUtils.format_Ymd(date));
			ShopPvUvDayDTO shopPvUvDayDTO;
			MarketBdpOLShopSumQueryResponse reponse = model.getMarketBdpOLShopSumQueryResponse();
	 		if(StringUtils.isNotBlank(reponse.getTp())) {
	 			shopPvUvDayDTO = new ShopPvUvDayDTO();
	 			shopPvUvDayDTO.setPlatformDesc(reponse.getPlatformDesc());
	 			shopPvUvDayDTO.setAvgRt(reponse.getAvgRt().doubleValue());
	 			shopPvUvDayDTO.setAvgRtNewuser(reponse.getAvgRtNewuser());
	 			shopPvUvDayDTO.setAvgRtOlduser(reponse.getAvgRtOlduser());
	 			shopPvUvDayDTO.setPv(reponse.getPv());
	 			totalPv += reponse.getPv();
	 			shopPvUvDayDTO.setUv(reponse.getUv()); 
	 			totalUv += reponse.getUv();
	 			shopPvUvDayDTO.setLandingTimes(reponse.getLandingTimes()); 
	 			shopPvUvDayDTO.setQuitTimes(reponse.getQuitTimes()); 
	 			shopPvUvDayDTO.setItemPv(reponse.getItemPv()); 
	 			shopPvUvDayDTO.setItemUv(reponse.getItemUv());  
	 			shopPvUvDayDTO.setHomepagePv(reponse.getHomepagePv());  
	 			shopPvUvDayDTO.setHomepageUv(reponse.getHomepageUv());  
	 			shopPvUvDayDTO.setAddToCartSkunum(reponse.getAddToCartSkunum());  
	 			shopPvUvDayDTO.setAddToCartSkutypenum(reponse.getAddToCartSkutypenum()); 
	 			shopPvUvDayDTO.setAddToCartUsers(reponse.getAddToCartUsers()); 
	 			shopPvUvDayDTO.setOrdNumDeal(reponse.getOrdNumDeal());  
	 			shopPvUvDayDTO.setSaleQttyDeal(reponse.getSaleQttyDeal()); 
	 			shopPvUvDayDTO.setBeforePrefrAmountDeal(reponse.getBeforePrefrAmountDeal()); 
	 			shopPvUvDayDTO.setAfterPrefrAmountDeal(reponse.getAfterPrefrAmountDeal()); 
	 			shopPvUvDayDTO.setOrdUserNumDeal(reponse.getOrdUserNumDeal()); 
	 			shopPvUvDayDTO.setPvOldUser(reponse.getPvOlduser()); 
	 			shopPvUvDayDTO.setUvOldUser(reponse.getUvOlduser()); 
	 			shopPvUvDayDTO.setVisitsOldUser(reponse.getVisitsOlduser()); 
	 			shopPvUvDayDTO.setPvNewuser(reponse.getPvNewuser()); 
	 			shopPvUvDayDTO.setUvNewuser(reponse.getUvNewuser());  
	 			shopPvUvDayDTO.setVisitsNewuser(reponse.getVisitsNewuser()); 
	 			shopPvUvDayDTO.setTp(reponse.getTp());  
	 			shopPvUvDayDTO.setVenderId(Long.valueOf(reponse.getVenderId())); 
	 			shopPvUvDayDTO.setVenderName(reponse.getVenderName()); 
	 			shopPvUvDayDTO.setShopId(Long.valueOf(reponse.getShopId()));  
	 			shopPvUvDayDTO.setShopName(reponse.getShopName()); 
	 			shopPvUvDayDTO.setVisits(reponse.getVisits()); 
	 			shopPvUvDayDTO.setBounceTimes(reponse.getBounceTimes()); 
	 			shopPvUvDayDTO.setDt(DateFormatUtils.parseYMd(reponse.getDt())); ;
	 			shopPvUvDTOLst.add(shopPvUvDayDTO);
			}
		}
		if(CollectionUtils.isNotEmpty(shopPvUvDTOLst)) {
			for (ShopPvUvDayDTO pvUvDay : shopPvUvDTOLst) {
				pvUvDay.setTotalPv(totalPv);
				pvUvDay.setTotalUv(totalUv);
			}
			if(isDelData) {
				shopPvUvDao.deleteShopPvUvDay(shop, DateFormatUtils.parse_Ymd(DateFormatUtils.format_Ymd(date)));
			}
			shopPvUvDao.batchInsertShopPvUvDay(shop, shopPvUvDTOLst);
		}
		
	}
}
