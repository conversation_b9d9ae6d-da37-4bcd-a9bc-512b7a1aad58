package com.pes.jd.data.converter;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.getRemarkByModifyTime.OrderRemark;
import com.jd.open.api.sdk.response.order.VenderRemark;
import com.pes.jd.dao.OrderRemarkDao;
import com.pes.jd.data.api.OrderReMarkOperator;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.DTO.OrderRemarkDTO;
import com.pes.jd.model.TO.OrderRemarkTO;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class OrderReMarkConverter {
	private static final Logger logger = LoggerFactory.getLogger(OrderReMarkConverter.class);
	
	@Resource
	private OrderReMarkOperator orderReMarkOperator;
	
	@Resource
	private OrderRemarkDao orderRemarkDao;
	
	public void pullPresaleOrderConverter(JobShopDTO shop, Date date,boolean isDelData) throws Exception{
		Date startDate = DateUtil.getStartTimeOfDate(date);
		Date endDate = DateUtil.getEndTimeOfDate(date);
		long s = System.currentTimeMillis();
		OrderRemarkTO orderRemarkTO = orderReMarkOperator.getOrderRemark(shop, startDate, endDate);
		long e = System.currentTimeMillis();
		if(logger.isDebugEnabled()){

			logger.debug("拉取订单备注-Pull order notes  耗时{}ms",(e-s));
		}
		List<OrderRemarkDTO> newOrderRemarkList = Lists.newArrayList();
		List<OrderRemarkDTO> repeatOrderRemarkList = Lists.newArrayList();
		if(orderRemarkTO != null){
			List<OrderRemark> remarkList = orderRemarkTO.getRemarkList();
			if(CollectionUtils.isNotEmpty(remarkList)){
				if(logger.isDebugEnabled()){

					logger.debug("订单备注拉取到的条数为：{}",remarkList.size());
				}
				if (isDelData) {
					int deleteNum = orderRemarkDao.batchDeleteOrderRemarkByShopByDate(shop,startDate,endDate);
					if(logger.isDebugEnabled()){

						logger.debug("订单备注删除的条数为：{}",deleteNum);
					}
//					logger.debug("pullPresaleOrderConverter delete num = {}",deleteNum);
				}
				List<Long> orderIdList = Lists.newArrayList();
				Date queryStartDate = startDate;
				Date queryEndDate = endDate;
				for (OrderRemark orderRemark : remarkList) {
					if(queryStartDate.after(orderRemark.getCreated())){
						queryStartDate = orderRemark.getCreated();
					}
					if(queryEndDate.before(orderRemark.getCreated())){
						queryEndDate = orderRemark.getCreated();
					}
					orderIdList.add(orderRemark.getOrderId());
				}
				//根据订单ID查询备注表中已存在的订单备注，添加时间防止跨月
				s=System.currentTimeMillis();
				List<Long> repeatOrderIdList = orderRemarkDao.selectOrderRemarkByOrderIdList(shop,orderIdList,queryStartDate,queryEndDate);
				e=System.currentTimeMillis();
				if(logger.isDebugEnabled()){

					logger.debug("订单备注db查询到的条数为：{},耗时：{}ms",repeatOrderIdList.size(),(e-s));
				}
				OrderRemarkDTO OrderRemarkDTO;
				for (OrderRemark orderRemark : remarkList) {
					OrderRemarkDTO  = new OrderRemarkDTO();
					OrderRemarkDTO.setShopId(shop.getShopId());
					OrderRemarkDTO.setOrderId(orderRemark.getOrderId());
					OrderRemarkDTO.setCreated(orderRemark.getCreated());
					OrderRemarkDTO.setModified(orderRemark.getModified());
					OrderRemarkDTO.setRemark(orderRemark.getRemark());
					OrderRemarkDTO.setFlag(orderRemark.getFlag());
					if(repeatOrderIdList.contains(orderRemark.getOrderId())){
						repeatOrderRemarkList.add(OrderRemarkDTO);
					}else{
						newOrderRemarkList.add(OrderRemarkDTO);
					}
				}
				//修改已存在的订单备注，插旗
				if(logger.isDebugEnabled()){

					logger.debug("订单备注开始更新...数量：{}",repeatOrderRemarkList.size());
				}
				s = System.currentTimeMillis();
				int updateNum = orderRemarkDao.updateOrderRemarkByObjList(shop,repeatOrderRemarkList,queryStartDate,queryEndDate);
				e = System.currentTimeMillis();
				if(logger.isDebugEnabled()){

					logger.debug("订单备注结束更新...耗时{}ms,条数{}条",(e-s),repeatOrderRemarkList.size());
				}
//				logger.debug("pullPresaleOrderConverter update num = {}",updateNum);
				//插入新的订单备注，插旗
				if(logger.isDebugEnabled()){

					logger.debug("订单备注开始插入...");
				}
				s = System.currentTimeMillis();
				int insertNum = orderRemarkDao.batchInsertOrderRemark(shop, date,newOrderRemarkList);
				e = System.currentTimeMillis();
				if(logger.isDebugEnabled()){

					logger.debug("订单备注结束插入...耗时{}ms,条数{}条",(e-s),insertNum);
				}
//				logger.debug("pullPresaleOrderConverter insert num = {}",insertNum);
			}else{
//				logger.debug("pullPresaleOrderConverter presaleOrderVOList isEmpty");
			}
		}else{
//			logger.debug("pull PresaleOrderConverter orderRemarkTO is null");
		}
	}

    public VenderRemark pullOrderConvert(JobShopDTO jobShopDTO, Long orderId) {
        VenderRemark venderRemark = null;
        try {
            OrderRemarkTO orderRemarkTO = orderReMarkOperator.getOrderRemark(orderId, jobShopDTO.getSessionKey());
            venderRemark = orderRemarkTO.getOrderVenderRemarkQueryByOrderIdResponse().getVenderRemarkQueryResult().getVenderRemark();
        } catch (Exception e) {
            logger.error("pullOrderConvert==================调用异常" + e.getMessage());
        }
        return venderRemark;
    }
	
}
  
