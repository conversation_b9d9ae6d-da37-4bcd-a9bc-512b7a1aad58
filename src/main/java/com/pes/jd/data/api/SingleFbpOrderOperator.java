package com.pes.jd.data.api;

import com.jd.open.api.sdk.request.order.PopOrderFbpGetRequest;
import com.jd.open.api.sdk.response.order.PopOrderFbpGetResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.model.TO.SingleFbpOrderTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;

@Service
public class SingleFbpOrderOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(SingleFbpOrderOperator.class);

	/**
	 * 根据jcq进行查询出订单补上出库信息
	 *
	 * @throws Exception
	 */
	public SingleFbpOrderTO getSingleFbpOrder(Long orderId, String sessionKey) throws Exception {
		PopOrderFbpGetRequest req = new PopOrderFbpGetRequest();
		req.setOptionalFields(

				"orderId,venderId,orderPayment,orderType,payType,freightPrice,orderState,orderStartTime,orderEndTime,itemInfoList,paymentConfirmTime,modified,pin,parentOrderId,orderTotalPrice,balanceUsed,consigneeInfo,orderRemark,invoiceInfo,invoiceCode ");

		req.setOrderId(orderId);
		req.setColType(1);
		req.setOrderState(
				"DengDaiDaYin,DengDaiChuKu,DengDaiDaBao,DengDaiFaHuo,ZiTiTuZhong,ShangMenTiHuo,ZiTiTuiHuo,DengDaiQueRenShouHuo,PeiSongTuiHuo,HuoDaoFuKuanQueRen,WanCheng,DengDaiFenQiFuKuan,ServiceFinished,SuoDing,DengDaiTuiKuan,orderSign");
		PopOrderFbpGetResponse response;
		int num = 0;
		int retryNum = 0;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.ORDER_MESSAGE_RECALLAPI_TIMES_OUT; recallApiTimes++) {
			try {
				response = getClient(sessionKey).execute(req);
				retryNum++;
				if(response != null) {
					if("0".equals(response.getCode())) {
						if(response.getSearchfbporderbyidResult() != null) {
							if(response.getSearchfbporderbyidResult().getOrderInfo() != null) {
								return new SingleFbpOrderTO(response, num, retryNum);
							}
						}
					}else {
						if("19".equals(response.getCode())) {
							return new SingleFbpOrderTO(new PopOrderFbpGetResponse(), num, retryNum);
						}
					}
					Thread.sleep(200);
					continue;
				}
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
				if (recallApiTimes >= CommonConstants.ORDER_MESSAGE_RECALLAPI_TIMES - 1) {
					logger.error(e.getMessage(), e);
					throw e;
				}
			}
		}
		return new SingleFbpOrderTO(new PopOrderFbpGetResponse(), num, retryNum);
	}
}
