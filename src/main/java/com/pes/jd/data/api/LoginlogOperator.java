package com.pes.jd.data.api;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.domain.im.OpenApiService.response.get.WaiterPresence;
import com.jd.open.api.sdk.domain.im.OpenApiService.response.get.WaiterPresencePage;
import com.jd.open.api.sdk.request.im.ImPopWaiterpresenceOpenapiGetRequest;
import com.jd.open.api.sdk.request.im.ImPopWaiterpresencesecondOpenapiGetRequest;
import com.jd.open.api.sdk.response.im.ImPopWaiterpresenceOpenapiGetResponse;
import com.jd.open.api.sdk.response.im.ImPopWaiterpresencesecondOpenapiGetResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.CsLoginlogResultTO;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * * 1:接口名-   jingdong.im.pop.waiterpresencesecond.openapi.get
 *      * 描述-      获取客服操作状态(秒级)
 *      * 接口地址-  https://open.jd.com/home/<USER>/doc/api?apiCateId=53&apiId=3410&apiName=jingdong.im.pop.waiterpresencesecond.openapi.get
 * * * 2:接口名-   jingdong.im.pop.waiterpresence.openapi.get
 *      * 描述-      获取客服的操作状态
 *      * 接口地址-  https://open.jd.com/home/<USER>/doc/api?apiCateId=53&apiId=3402&apiName=jingdong.im.pop.waiterpresence.openapi.get
 */
@Service
public class LoginlogOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(LoginlogOperator.class);
	/**  
	 * pullLoginLog:(查询店铺账号登陆记录). <br/>  
	 *
	 * @param shop
	 * @param delayTime		业务天延迟时间
	 * @param shopCsLst		客服s
	 * @param startDate
	 * @param endDate
	 * @return
	 * @throws Exception
	 * @since JDK 1.8  
	 */
	public CsLoginlogResultTO pullLoginLog(JobShopDTO shop, Integer delayTime, List<CsDTO> shopCsLst, Date startDate,Date endDate)
			throws Exception {
		ImPopWaiterpresenceOpenapiGetRequest req = new ImPopWaiterpresenceOpenapiGetRequest();
		String sessionKey = shop.getSessionKey();
		
		List<Date> dateList = DateUtil.splitDate(startDate, endDate);
		if (CollectionUtils.isEmpty(dateList)) {
			return new CsLoginlogResultTO(Lists.newArrayList(), 0, 0);
		}

		//一次跑的客服人数太多容易报错
		List<String> csStringList = splitCustomerServiceList(shopCsLst, CommonConstants.JOB_CS_NUM);
		List<WaiterPresence> retCsLoginLog = null;
		CsLoginlogResultTO csLoginlogResultTO = new CsLoginlogResultTO(retCsLoginLog, 0, 0);
		Integer pageSize = 50;// 每页最多50条记录
		req.setPageSize(pageSize);
		req.setDataType(CommonConstants.JOB_API_DATE_TYPE);
		for (String shopCsLstStr : csStringList) {
			req.setWaiter(shopCsLstStr);
			for (int i = 0; i < dateList.size(); i = i + 1) {
				long delayStartTime = dateList.get(i).getTime() + delayTime * 60 * 60 * 1000;// 如果业务天为3，开始时间推迟到凌晨3点
				long delayEndTime = DateUtil.getEndTimeOfDate(dateList.get(i)).getTime() + delayTime * 60 * 60 * 1000;// 如果业务天为3，结束时间推迟到第二天凌晨2:59:59点
				Date startTimeTemp = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(delayStartTime));
				Date endTimeTemp = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(delayEndTime));
				//防止跨月
				boolean isSameMonth = DateUtil.getTimeSameMonth(startTimeTemp, endTimeTemp);
				if(isSameMonth){
					req.setStartTime(startTimeTemp);
					req.setEndTime(endTimeTemp);
					pullLoginLogData(req, sessionKey, csLoginlogResultTO);
				}else {
					//分两个月单独调用
					{
						req.setStartTime(startTimeTemp);
						req.setEndTime(DateFormatUtils.getEndTimeOfDate(startTimeTemp));
						pullLoginLogData(req, sessionKey, csLoginlogResultTO);
					}
					{
						req.setStartTime(DateFormatUtils.getStartTimeOfDate(endTimeTemp));
						req.setEndTime(endTimeTemp);
						pullLoginLogData(req, sessionKey, csLoginlogResultTO);
					}
				}
			}
		}
		return csLoginlogResultTO;
	}
	
	private void pullLoginLogData(ImPopWaiterpresenceOpenapiGetRequest req, String sessionKey, CsLoginlogResultTO csLoginlogResultTO) throws Exception{
		int page = 0;
		int num = csLoginlogResultTO.getNum();
		int retryNum = csLoginlogResultTO.getRetryNum() + num;
		List<WaiterPresence> retPassLog = csLoginlogResultTO.getLoginLogRet();
		while (true) {// 分页获取登陆记录数据
			num++;
			page++;
			req.setPage(page);
//			String server_url = "https://api.jd.com/routerjson";
			ImPopWaiterpresenceOpenapiGetResponse loginLogResponse = null;
			int recallApiTimes = 0;
			for (; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
				retryNum++;
				try {
					loginLogResponse = getClient(sessionKey).execute(req);
					if (loginLogResponse == null || loginLogResponse.getCode() == null) {
						logger.info("loginLogResponse = null");
						GainShopDataFailException ae = new GainShopDataFailException("咚咚登陆记录获取失败");
						throw ae;
					} else {
						// 正常返回
						if ("0".equals(loginLogResponse.getCode())) {
							WaiterPresencePage waiterPresencePage = loginLogResponse.getWaiterPresencePage();
							if (retPassLog == null) {
								retPassLog = waiterPresencePage.getWaiterPresenceList();
							} else {
								retPassLog.addAll(waiterPresencePage.getWaiterPresenceList());
							}
							break;
						}
						logger.info("ErrorCode = {},ErrorMsg = {}", loginLogResponse.getCode(),
								loginLogResponse.getMsg());
						if ("2".equals(loginLogResponse.getCode()) // code=2：1天内访问接口
																	// 超过500次！
								|| "19".equals(loginLogResponse.getCode()) // code=19:token已过期或者不存在，请重新授权
								|| "61".equals(loginLogResponse.getCode()) // code=61:日期格式不正常，例：2018-09-35
								|| "67".equals(loginLogResponse.getCode())) { // code=67:参数异常：客服账号列表为空，时间超过限制
							GainShopDataFailException ae = new GainShopDataFailException("咚咚登陆记录获取失败");
							ae.setErrorCode(loginLogResponse.getCode());
							ae.setErrorMsg(loginLogResponse.getMsg());
							throw ae;
						}
					}
				} catch (Exception e) {
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						loginLogResponse.getMsg();
						GainShopDataFailException ae = new GainShopDataFailException("咚咚登陆记录获取失败");
						ae.setErrorCode("-");
						ae.setErrorMsg("通过京东接口获取店铺账号登陆记录信息失败");
						throw ae;
					}
					Thread.sleep(500L); // 睡眠半秒 TODO
				}
			}
			if (loginLogResponse == null 
					|| loginLogResponse.getWaiterPresencePage() == null
					|| loginLogResponse.getWaiterPresencePage().getWaiterPresenceList() == null) {
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					GainShopDataFailException e = new GainShopDataFailException("咚咚登陆记录获取失败");
					throw e;
				}
				break;
			} else {
				if (loginLogResponse.getWaiterPresencePage().getWaiterPresenceList().size() == 0) {
					logger.info("拉取完，当前page:{},拉取数量={}", page, loginLogResponse.getWaiterPresencePage().getWaiterPresenceList().size());
					break;
				} else {
					logger.info("继续拉取下一页，当前page:{}", page);
				}
			}
		}
		csLoginlogResultTO.setLoginLogRet(retPassLog);
		csLoginlogResultTO.setNum(num);
		csLoginlogResultTO.setRetryNum(retryNum - num);
	}

	public CsLoginlogResultTO pullLoginLogForSEcondsApi(JobShopDTO shop, Integer delayTime, List<CsDTO> shopCsLst, Date startDate, Date endDate) throws Exception {
		ImPopWaiterpresencesecondOpenapiGetRequest req = new ImPopWaiterpresencesecondOpenapiGetRequest();
		String sessionKey = shop.getSessionKey();

		List<Date> dateList = DateUtil.splitDate(startDate, endDate);
		if (CollectionUtils.isEmpty(dateList)) {
			return new CsLoginlogResultTO(Lists.newArrayList(), 0, 0);
		}

		//一次跑的客服人数太多容易报错
		List<String> csStringList = splitCustomerServiceList(shopCsLst, CommonConstants.JOB_CS_NUM);
		List<WaiterPresence> retCsLoginLog = null;
		CsLoginlogResultTO csLoginlogResultTO = new CsLoginlogResultTO(retCsLoginLog, 0, 0);
		Integer pageSize = 50;// 每页最多50条记录
		req.setPageSize(pageSize);
		req.setDataType(CommonConstants.JOB_API_DATE_TYPE);
		for (String shopCsLstStr : csStringList) {
			req.setWaiter(shopCsLstStr);
			for (int i = 0; i < dateList.size(); i = i + 1) {
				long delayStartTime = dateList.get(i).getTime() + delayTime * 60 * 60 * 1000;// 如果业务天为3，开始时间推迟到凌晨3点
				long delayEndTime = DateUtil.getEndTimeOfDate(dateList.get(i)).getTime() + delayTime * 60 * 60 * 1000;// 如果业务天为3，结束时间推迟到第二天凌晨2:59:59点
				Date startTimeTemp = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(delayStartTime));
				Date endTimeTemp = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(delayEndTime));
				//防止跨月
				boolean isSameMonth = DateUtil.getTimeSameMonth(startTimeTemp, endTimeTemp);
				if(isSameMonth){
					req.setStartTime(startTimeTemp);
					req.setEndTime(endTimeTemp);
					pullLoginLogDataForSEcondsApi(req, sessionKey, pageSize, csLoginlogResultTO);
				}else {
					//分两个月单独调用
					{
						req.setStartTime(startTimeTemp);
						req.setEndTime(DateFormatUtils.getEndTimeOfDate(startTimeTemp));
						pullLoginLogDataForSEcondsApi(req, sessionKey, pageSize, csLoginlogResultTO);
					}
					{
						req.setStartTime(DateFormatUtils.getStartTimeOfDate(endTimeTemp));
						req.setEndTime(endTimeTemp);
						pullLoginLogDataForSEcondsApi(req, sessionKey, pageSize, csLoginlogResultTO);
					}
				}
			}
		}
		return csLoginlogResultTO;
	}

	private void pullLoginLogDataForSEcondsApi(ImPopWaiterpresencesecondOpenapiGetRequest req, String sessionKey, Integer pageSize, CsLoginlogResultTO csLoginlogResultTO) throws Exception {
		int page = 0;
		int num = csLoginlogResultTO.getNum();
		int retryNum = csLoginlogResultTO.getRetryNum() + num;
		List<WaiterPresence> retPassLog = csLoginlogResultTO.getLoginLogRet();
		while (true) {// 分页获取登陆记录数据
			num++;
			page++;
			req.setPage(page);
			ImPopWaiterpresencesecondOpenapiGetResponse loginLogResponse = null;
			int recallApiTimes = 0;
			for (; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
				retryNum++;
				try {
					loginLogResponse = getClient(sessionKey).execute(req);
//					loginLogResponse = getClient(sessionKey,"https://api.jd.com/routerjson").execute(req);
					if (loginLogResponse == null || loginLogResponse.getCode() == null) {
						logger.info("loginLogResponse = null");
						GainShopDataFailException ae = new GainShopDataFailException("咚咚登陆记录获取失败");
						throw ae;
					} else {
						// 正常返回
						if ("0".equals(loginLogResponse.getCode())) {
							WaiterPresencePage waiterPresencePage = loginLogResponse.getWaiterPresencePage();
							if (retPassLog == null) {
								retPassLog = waiterPresencePage.getWaiterPresenceList();
							} else {
								retPassLog.addAll(waiterPresencePage.getWaiterPresenceList());
							}
							break;
						}
						logger.info("ErrorCode = {},ErrorMsg = {}", loginLogResponse.getCode(),
								loginLogResponse.getMsg());
						if ("2".equals(loginLogResponse.getCode()) // code=2：1天内访问接口
								// 超过500次！
								|| "19".equals(loginLogResponse.getCode()) // code=19:token已过期或者不存在，请重新授权
								|| "61".equals(loginLogResponse.getCode()) // code=61:日期格式不正常，例：2018-09-35
								|| "67".equals(loginLogResponse.getCode())) { // code=67:参数异常：客服账号列表为空，时间超过限制
							GainShopDataFailException ae = new GainShopDataFailException("咚咚登陆记录获取失败");
							ae.setErrorCode(loginLogResponse.getCode());
							ae.setErrorMsg(loginLogResponse.getMsg());
							throw ae;
						}
					}
				} catch (Exception e) {
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						loginLogResponse.getMsg();
						GainShopDataFailException ae = new GainShopDataFailException("咚咚登陆记录获取失败");
						ae.setErrorCode("-");
						ae.setErrorMsg("通过京东接口获取店铺账号登陆记录信息失败");
						throw ae;
					}
					Thread.sleep(500L); // 睡眠半秒 TODO
				}
			}
			if (loginLogResponse == null
					|| loginLogResponse.getWaiterPresencePage() == null
					|| loginLogResponse.getWaiterPresencePage().getWaiterPresenceList() == null) {
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					GainShopDataFailException e = new GainShopDataFailException("咚咚登陆记录获取失败");
					throw e;
				}
				break;
			} else {
				if (loginLogResponse.getWaiterPresencePage().getWaiterPresenceList().size() == 0) {
					logger.info("拉取完，当前page:{},拉取数量={}", page, loginLogResponse.getWaiterPresencePage().getWaiterPresenceList().size());
					break;
				} else {
					logger.info("继续拉取下一页，当前page:{}", page);
				}
			}
		}
		csLoginlogResultTO.setLoginLogRet(retPassLog);
		csLoginlogResultTO.setNum(num);
		csLoginlogResultTO.setRetryNum(retryNum - num);
	}
}
