package com.pes.jd.data.api;

import cn.hutool.core.collection.CollUtil;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceCommonProvider.response.view.OrderAfsAndRefund;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.AscServiceOrderRefundTO;
import com.pes.jd.task.executor.FetchAscServiceOrderRefundDataExecutor;
import com.pes.jd.util.DateFormatUtils;
import com.yiyitech.support.task.AsyncTask;
import com.yiyitech.support.task.AsyncTaskUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class AscServiceOrderRefundOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(AscServiceOrderRefundOperator.class);

	public AscServiceOrderRefundTO getAscServiceOrderRefundData(JobShopDTO shop, Date startDate, Date endDate, int fetchType) throws Exception {
		String sessionKey = shop.getSessionKey();
		List<OrderAfsAndRefund> allRefund = new ArrayList<>();
		AtomicInteger num = new AtomicInteger(0);
		AtomicInteger retryNum = new AtomicInteger(0);
		long orderRefundStartTime = System.currentTimeMillis();
		List<Date> threadDates = DateFormatUtils.splitDateToList(startDate, endDate, 6 * 60 * 60L);
		List<AsyncTask> taskList = new ArrayList<>();
		int task = 0;
		for (int t = 0, threadDateSize = threadDates.size(); t < threadDateSize; t += 2) {
			if(logger.isDebugEnabled()){
				logger.debug("AscServiceOrderRefund start date: " + threadDates.get(t) + " AscServiceOrderRefund end date : " + threadDates.get(t + 1));
			}
			int finalT = t;
			AsyncTask asyncTask = new AsyncTask(task) {
				@Override
				public Object run() {
					FetchAscServiceOrderRefundDataExecutor cdi = new FetchAscServiceOrderRefundDataExecutor(threadDates.get(finalT), threadDates.get(finalT + 1), sessionKey, fetchType, shop.getVenderId());
					try {
						return cdi.call();
					} catch (Exception e) {
						logger.error(e.getMessage(), e);
						return null;
					}
				}
			};
			taskList.add(asyncTask);
			task++;
		}
		if (CollUtil.isNotEmpty(taskList)) {
			Object[] objects = AsyncTaskUtil.runAll(taskList);
			Arrays.stream(objects).filter(Objects::nonNull).forEach(callBack -> {
				AscServiceOrderRefundTO result = (AscServiceOrderRefundTO) callBack;
				if (result != null) {
					allRefund.addAll(result.getRefunds());
					num.addAndGet(result.getNum());
					retryNum.addAndGet(result.getRetryNum());
				}
			});
		}
		long orderRefundEndTime = System.currentTimeMillis();
		if(logger.isDebugEnabled()){
			logger.debug("AscServiceOrderRefund use ----------time{}", (orderRefundEndTime - orderRefundStartTime));
		}
		return new AscServiceOrderRefundTO(allRefund, num.get(), retryNum.get());
	}

}
