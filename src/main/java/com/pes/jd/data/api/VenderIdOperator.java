package com.pes.jd.data.api;

//import com.jd.open.api.sdk.request.user.VenderErpGetOperatorByVenderIdRequest;
//import com.jd.open.api.sdk.response.user.VenderErpGetOperatorByVenderIdResponse;

/**
 * <AUTHOR>
 * @version 2019-06-10 10:58
 */
public class VenderIdOperator extends BaseOperator {





//    public  VenderErpGetOperatorByVenderIdResponse getErpVender(){
//        VenderErpGetOperatorByVenderIdRequest request=new VenderErpGetOperatorByVenderIdRequest();
//        String date="2019-06-01 15:00:00";
//        request.setTimestamp(date);
//        String sessionKey="444c8e06354448d78b32463022f47a040yzh";
//        VenderErpGetOperatorByVenderIdResponse response=new VenderErpGetOperatorByVenderIdResponse();
//        try{
//           response=getClient(sessionKey).execute(request);
//           System.out.println(response.toString());
//        }catch (Exception e){
//
//        }
//
//        return response;
//    }


//    public static  void  main(String[] args){
//        VenderIdOperator venderIdOperator=new VenderIdOperator();
//        VenderErpGetOperatorByVenderIdResponse venderIdResponse=venderIdOperator.getErpVender();
//        System.out.println(venderIdResponse);
//
//    }


}
