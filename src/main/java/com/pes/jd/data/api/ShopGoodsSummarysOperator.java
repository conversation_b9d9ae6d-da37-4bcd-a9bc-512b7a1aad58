package com.pes.jd.data.api;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.evaluation.PopCommentJsfService.response.getCommentSummarys.CommentSummaryUgcVo;
import com.jd.open.api.sdk.request.evaluation.PopGetCommentSummarysRequest;
import com.jd.open.api.sdk.response.evaluation.PopGetCommentSummarysResponse;
import com.pes.jd.Constants.AppConstants;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.ShopGoodsSummarysTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.BindException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.List;


/**
 * 接口：jingdong.pop.getCommentSummarys
 * 描述：实时商品好评率
 * 地址：https://open.jd.com/home/<USER>/doc/api?apiCateId=102&apiId=5225&apiName=jingdong.pop.getCommentSummarys
 */
@Component
public class ShopGoodsSummarysOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(ShopGoodsSummarysOperator.class);

	public ShopGoodsSummarysTO getGoodsSummarys(JobShopDTO shop, Integer type, List<String> goodsIdLst)
			throws Exception {
		long s=System.currentTimeMillis();
		List<CommentSummaryUgcVo> goodsSummaryLst = Lists.newArrayList();
		JdClient client=getClient(shop.getSessionKey(), AppConstants.SERVER_URL);
		PopGetCommentSummarysRequest req = new PopGetCommentSummarysRequest();
		PopGetCommentSummarysResponse response = null;
		if(type==1){
			req.setAggr(0);
		}else{
			req.setAggr(1);
		}
		req.setObjectIds(String.join(",",goodsIdLst));
			for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
				try {
					response = client.execute(req);
					if (response != null&&"0".equals(response.getCode())&& CollectionUtils.isNotEmpty(response.getSummaries())) {

						goodsSummaryLst.addAll(response.getSummaries());
						 break;
					} else {
						if(response != null&&"32".equals(response.getResultCode())){
							Thread.sleep(5000);//睡眠5s
						}
						if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
							GainShopDataFailException dataFailException = new GainShopDataFailException(response==null?"获取商品好评率为空":response.getMsg());
							dataFailException.setErrorMsg(response==null?" response is null":response.getMsg());
							dataFailException.setErrorCode(response==null?" response is null":response.getCode());
							throw dataFailException;
						}
					}
				} catch (SocketTimeoutException e) {
					if (recallApiTimes >= CommonConstants.RECALLAPI_TIMES - 1) {
						logger.error(" getGoodsSummarys error:{}",e.getMessage(),e);
						throw e;
					}
					Thread.sleep(800);//睡眠1s
				} catch (ConnectException e) {
					if (recallApiTimes >= CommonConstants.RECALLAPI_TIMES - 1) {
						logger.error(" getGoodsSummarys error:{}",e.getMessage(),e);
						throw e;
					}
					Thread.sleep(30000);
				} catch (IllegalStateException e) {
					if (recallApiTimes >= CommonConstants.RECALLAPI_TIMES - 1) {
						logger.error(" getGoodsSummarys error:{}",e.getMessage(),e);
						throw e;
					}

					Thread.sleep(3000);//睡眠3s
				}
				catch (BindException e) {
					if (recallApiTimes >= CommonConstants.RECALLAPI_TIMES - 1) {
						logger.error(" getGoodsSummarys error:{}",e.getMessage(),e);
						throw e;
					}

					Thread.sleep(20000);
				} catch (Exception e) {
					if (recallApiTimes >= CommonConstants.RECALLAPI_TIMES - 1) {
						logger.error(" getGoodsSummarys error:{}",e.getMessage(),e);
						throw e;
					}
					Thread.sleep(100);//睡眠3s
				}
			}
		long e=System.currentTimeMillis();
	//	logger.info("pull shopId：{}  getGoodsSummarys size:{}  time:{}ms",shop.getShopId(),goodsSummaryLst.size(),e-s);
		return new ShopGoodsSummarysTO(goodsSummaryLst);
	}
}
