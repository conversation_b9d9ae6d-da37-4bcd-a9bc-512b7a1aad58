package com.pes.jd.data.api;

import cn.hutool.core.collection.CollUtil;
import com.jd.open.api.sdk.domain.order.OrderNotPayService.response.notPayOrderInfo.OrderDataNotPayInfo;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.NoPayOrderGetModelTO;
import com.pes.jd.task.executor.FetchNoPayOrderDataExecutor;
import com.pes.jd.util.DateFormatUtils;
import com.yiyitech.support.task.AsyncTask;
import com.yiyitech.support.task.AsyncTaskUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Component
public class NoPayOrderOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(NoPayOrderOperator.class);
	// ********TODO1**********
	// 多线程交易使用
	public NoPayOrderGetModelTO pullOrderDataByThread(JobShopDTO shop, Date startDate, Date endDate) {
		String sessionKey = shop.getSessionKey();
		AtomicInteger num = new AtomicInteger(0);
		AtomicInteger retryNum = new AtomicInteger(0);
		List<OrderDataNotPayInfo> orders = new ArrayList<>();
		long interval = 24 * 60 * 60L;// 初始化一天间隔
		List<Date> dates = DateFormatUtils.splitDateToList(DateFormatUtils.getStartTimeOfDate(startDate),
				DateFormatUtils.getEndTimeOfDate(endDate), interval);
		for (int i = 0, size = dates.size(); i < size; i += 2) {
			List<Date> threadDates = DateFormatUtils.splitDateToList(dates.get(i), dates.get(i + 1), 60 * 60L);
			List<AsyncTask> taskList = new ArrayList<>();
			int task = 0;
			for (int t = 0, threadDateSize = threadDates.size(); t < threadDateSize; t += 2) {
				int finalT = t;
				AsyncTask asyncTask = new AsyncTask(task) {
					@Override
					public Object run() {
						FetchNoPayOrderDataExecutor cdi = new FetchNoPayOrderDataExecutor(threadDates.get(finalT), threadDates.get(finalT + 1), sessionKey, shop.getTitle());
						try {
							return cdi.call();
						} catch (Exception e) {
							logger.error(e.getMessage(), e);
							return null;
						}
					}
				};
				taskList.add(asyncTask);
				task++;
			}
			if (CollUtil.isNotEmpty(taskList)) {
				Object[] objects = AsyncTaskUtil.runAll(taskList);
				Arrays.stream(objects).filter(Objects::nonNull).forEach(callBack -> {
					NoPayOrderGetModelTO result = (NoPayOrderGetModelTO) callBack;
					if (result != null) {
						orders.addAll(result.getNoPayOrders());
						num.addAndGet(result.getNum());
						retryNum.addAndGet(result.getRetryNum());
					}
				});
			}
		}
		return new NoPayOrderGetModelTO(orders, num.get(), retryNum.get());
	}

}