package com.pes.jd.data.api;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.market.OrderServiceProvider.response.listwithpage.OrderVO;
import com.jd.open.api.sdk.domain.market.OrderServiceProvider.response.listwithpage.PageResult;
import com.jd.open.api.sdk.request.market.PopFwOrderListwithpageRequest;
import com.jd.open.api.sdk.response.market.PopFwOrderListwithpageResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.exception.TokenOverdueException;
import com.pes.jd.model.TO.ShopSubscribeTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.util.List;

@Service
public class ShopSubscribeOperator extends BaseOperator{
	
	private static final Logger logger = LoggerFactory.getLogger(ShopSubscribeOperator.class);
	
	public ShopSubscribeTO getUserSubscribe(String sessionKey) throws Exception {
		PopFwOrderListwithpageRequest  req = new PopFwOrderListwithpageRequest();
		int pageSize=20;
		PopFwOrderListwithpageResponse  response = null;
		List<OrderVO> subOrderLst=Lists.newArrayList();
//		JdClient client = new DefaultJdClient("https://api.jd.com/routerjson", "ff51c256a5b5497fb8dbf6f76f45b87bntzh", "321CEAB001F59FDDA67DC388C0575958",
//				"2b5f6d0cbde1419c9a64e8ce694f22e9");
		JdClient client = getClient(sessionKey);
		boolean hasNext;
		req.setPageSize(pageSize);
		req.setFwsPin("魔方2019");
		req.setServiceCode("FW_GOODS-908622");
		int page = 0;
		do {
			page++;
			req.setCurrentPage(page);
			for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
				try {
					response = client.execute(req);
					if (response != null && "0".equals(response.getCode())) {
						break;
					}else if(response != null && "19".equals(response.getCode())){
						throw new TokenOverdueException("token已过期或者不存在");
					} else {
						if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
							if(logger.isDebugEnabled()){
								logger.info("订购关系获取失败::  response.getMsg=" + response.getMsg() + " ;errorCode="
										+ response.getCode());
							}
							GainShopDataFailException dataFailException = new GainShopDataFailException("订购关系获取失败");
							dataFailException.setErrorMsg(response.getMsg());
							dataFailException.setErrorCode(response.getCode());
							throw dataFailException;
						} else {
							Thread.sleep(200);
						}
					}
				} catch (GainShopDataFailException e) {
					throw e;
				} catch (Exception e) {
					if (!(e.getCause() instanceof SocketTimeoutException)) {
						throw e;
					}
					if (e.getCause() instanceof TokenOverdueException) {
						throw e;
					}
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						e.printStackTrace();
						throw e;
					}
					logger.error(e.getMessage(),e);
				}
			}
			PageResult result=	response.getReturnType();
				if(result!=null){
					List<OrderVO> orderLst = result.getOrderList();
					if(CollectionUtils.isNotEmpty(orderLst)){
						subOrderLst.addAll(orderLst);
						if(orderLst.size()<pageSize){
							if(logger.isDebugEnabled()){
								logger.debug("已经拉完，当前page:{}", page);
							}
							hasNext = false;
						} else {
							hasNext = true;
						}
					}else {
						hasNext = false;
					}
			} else {
				hasNext = false;
			}
		} while (hasNext);
		if(logger.isDebugEnabled()){
			logger.debug("invoke JD api get subOrder num:{}" , subOrderLst.size());
		}

		
		return new ShopSubscribeTO(subOrderLst);
	}
	
//	public static void main(String[] args) throws Exception {
//		
//		ShopSubscribeOperator shopSubscribeOperator = new ShopSubscribeOperator();
//		shopSubscribeOperator.getUserSubscribe("2d3767658de14b6f832c4ba0dfaac774y3nj");
//		
//		JdClient client=new DefaultJdClient("https://api.jd.com/routerjson", "2d3767658de14b6f832c4ba0dfaac774y3nj", "321CEAB001F59FDDA67DC388C0575958",
//				"2b5f6d0cbde1419c9a64e8ce694f22e9"); 
//		PopFwOrderListwithpageRequest request=new PopFwOrderListwithpageRequest();
//		request.setFwsPin("魔方2019");
//		request.setServiceCode("FW_GOODS-908622");
//		request.setPageSize(1);
//		request.setFwsPin("fwsPin");
//		request.setCurrentPage(1);
//		PopFwOrderListwithpageResponse response=client.execute(request);
//	}
}
