package com.pes.jd.data.api;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.jingzhun.JosQueryFacade.request.query.ApiQueryCacheableRequest;
import com.jd.open.api.sdk.domain.jingzhun.JosQueryFacade.request.query.JosSysParams;
import com.jd.open.api.sdk.request.jingzhun.MktUserLabelQueryRequest;
import com.jd.open.api.sdk.response.jingzhun.MktUserLabelQueryResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DO.UserPortraitLabel;
import com.pes.jd.util.BeanUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;



/**
 * 接口：jingdong.mkt.user.label.query
 * 描述：精准营销用户画像查询
 * 地址：https://open.jd.com/v2/#/doc/api?apiCateId=359&apiId=10153&apiName=jingdong.mkt.user.label.query
 */
@Component
public class UserPortraitOperator extends BaseOperator {

	private static final Logger logger = LoggerFactory.getLogger(UserPortraitOperator.class);

	/**
	 * 查询用户标签数据
	 *
	 * @param sessionKey 店铺sessionToken
	 * @param userPin 用户PIN
	 * @param openIdBuyer 买家开放ID
	 * @param xidBuyer 买家XID
	 * @param requestId 请求ID（可选，如果为空则自动生成）
	 * @return 用户标签查询结果
	 * @throws Exception 查询异常
	 */
	public UserPortraitLabel getUserLabelData(String sessionKey, String userPin,
											  String openIdBuyer, String xidBuyer,
											  String requestId) throws Exception {

		long startTime = System.currentTimeMillis();
		int retryNum = 0;

		// 参数校验
		if (StringUtils.isBlank(sessionKey)) {
			throw new IllegalArgumentException("sessionKey不能为空");
		}

		if (StringUtils.isBlank(userPin) && StringUtils.isBlank(openIdBuyer) && StringUtils.isBlank(xidBuyer)) {
			throw new IllegalArgumentException("userPin、openIdBuyer、xidBuyer至少需要提供一个");
		}

		// 如果requestId为空，自动生成一个
		if (StringUtils.isBlank(requestId)) {
			requestId = UUID.randomUUID().toString().replace("-", "");
		}

		JdClient client = getClient(sessionKey);
		MktUserLabelQueryRequest request = new MktUserLabelQueryRequest();
		MktUserLabelQueryResponse response = null;

		// 构建请求参数
		JosSysParams systemParam = new JosSysParams();
		request.setSystemParam(systemParam);
		ApiQueryCacheableRequest requestParam = new ApiQueryCacheableRequest();
		Map<String, Object> stringSubs = new HashMap<>();
		stringSubs.put("userPin", userPin);
		stringSubs.put("open_id_buyer", openIdBuyer);
		stringSubs.put("xid_buyer", xidBuyer);
		requestParam.setRequestId(requestId);
		requestParam.setStringSubs(stringSubs);
		request.setRequestParam(requestParam);

		// 执行API调用，带重试机制
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				retryNum++;
				logger.debug("开始查询用户标签，userPin: {}, openIdBuyer: {}, xidBuyer: {}, requestId: {}, 第{}次尝试",
						userPin, openIdBuyer, xidBuyer, requestId, recallApiTimes + 1);

				response = client.execute(request);
				if (response != null) {
					// 检查响应状态码
					if (StringUtils.isNotBlank(response.getCode())) {
						if ("0".equals(response.getCode())) {
							// 成功获取数据
							logger.debug("用户标签查询成功，userPin: {}, requestId: {}", userPin, requestId);
							break;
						} else {
							// API返回错误码
							String errorMsg = String.format("查询用户标签失败，错误码: %s, 错误信息: %s,用户pin: %s",
									response.getCode(), response.getZhDesc(),userPin);
							logger.error(errorMsg);

							GainShopDataFailException dataFailException = new GainShopDataFailException("获取用户标签数据失败");
							dataFailException.setErrorMsg(response.getZhDesc());
							dataFailException.setErrorCode(response.getCode());
							throw dataFailException;
						}
					} else {
						// 响应码为空，可能是网络问题，继续重试
						logger.warn("用户标签查询响应码为空，第{}次重试", recallApiTimes + 1);
						if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
							throw new GainShopDataFailException("用户标签查询响应码为空，重试次数已达上限");
						}
					}
				} else {
					// 响应为空
					logger.warn("用户标签查询响应为空，第{}次重试", recallApiTimes + 1);
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						GainShopDataFailException dataFailException = new GainShopDataFailException("用户标签查询失败");
						dataFailException.setErrorMsg("第" + (recallApiTimes + 1) + "次查询用户标签返回的response为空");
						dataFailException.setErrorCode("USER_LABEL_RESPONSE_EMPTY");
						logger.error("********查询用户标签response为空 ********************");
						throw dataFailException;
					}
				}

			} catch (GainShopDataFailException e) {
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					logger.error("userPin: {}, 查询用户标签失败，第{}次尝试，响应码: {}, 错误信息: {}",
							userPin, recallApiTimes + 1,
							response != null ? response.getCode() : "null",
							response != null ? response.getZhDesc() : "response为空");
					throw e;
				}
				// 等待一段时间后重试
				try {
					Thread.sleep(1000 * (recallApiTimes + 1)); // 递增等待时间
				} catch (InterruptedException ie) {
					Thread.currentThread().interrupt();
					throw new RuntimeException("线程被中断", ie);
				}
			} catch (Exception e) {
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					logger.error("查询用户标签发生未知异常: " + e.getMessage(), e);
					throw e;
				}
				// 等待一段时间后重试
				try {
					Thread.sleep(1000 * (recallApiTimes + 1));
				} catch (InterruptedException ie) {
					Thread.currentThread().interrupt();
					throw new RuntimeException("线程被中断", ie);
				}
			}
		}

		long endTime = System.currentTimeMillis();
		logger.debug("用户标签查询完成，userPin: {}, requestId: {}, 耗时: {}ms, 重试次数: {}",
				userPin, requestId, endTime - startTime, retryNum - 1);

		if (response != null && logger.isDebugEnabled()) {
			logger.debug("用户标签查询响应: {}",
					JSONObject.toJSONString(response, SerializerFeature.PrettyFormat));
		}
		if(response != null) {
			try {
				JSON parse = JSONUtil.parse(response.getMsg());
				Object byPath = parse.getByPath("jingdong_mkt_user_label_query_responce.returnType.result[0]");
				String jsonStr = JSONUtil.toJsonStr(byPath);
				if(jsonStr.equals("{}")) {
					logger.warn("这是一个没有数据的用户{}",userPin);
				}
				com.jd.open.api.sdk.domain.jingzhun.JosQueryFacade.response.query.Map sdkMap = parse.getByPath("jingdong_mkt_user_label_query_responce.returnType.result[0]", com.jd.open.api.sdk.domain.jingzhun.JosQueryFacade.response.query.Map.class);
				ObjectMapper objectMapper = new ObjectMapper();
				Map<String, Object> map = objectMapper.convertValue(sdkMap, Map.class);
				UserPortraitLabel userPortraitLabel = objectMapper.convertValue(map, UserPortraitLabel.class);
				userPortraitLabel.setUserPin(userPin);
				return userPortraitLabel;
			} catch (Exception e) {
				logger.error("使用Jackson转换用户画像标签数据时发生错误: {}", e.getMessage(), e);
				throw new RuntimeException("用户画像标签数据转换失败", e);
			}
		}
		else{
			return null;
		}


	}



}
