package com.pes.jd.data.api;

import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.user.UserRelatedRpcService.response.getUserInfoByOpenId.OAuthUserInfo;
//import com.jd.open.api.sdk.request.user.NicknamebyuidQueryRequest;
import com.jd.open.api.sdk.request.user.UserGetUserInfoByOpenIdRequest;
//import com.jd.open.api.sdk.response.user.NicknamebyuidQueryResponse;
import com.jd.open.api.sdk.response.user.UserGetUserInfoByOpenIdResponse;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.exception.ApiInvokException;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.UserInfoResultTO;
import com.pes.jd.util.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.SocketTimeoutException;
import java.text.ParseException;
import java.util.Date;

/**
 * ClassName:ShopOperator <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 上午10:18:37 <br/>
 * 
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@Component
public class UserInfoOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(UserInfoOperator.class);
	
	public UserInfoResultTO getUserInfoByPin(String openId,String sessionKey) throws Exception {
		UserGetUserInfoByOpenIdRequest   req = new UserGetUserInfoByOpenIdRequest  ();
		req.setOpenId(openId);
		try {
			req.setTimestamp(DateFormatUtils.formatYMdHms(new Date()));
		} catch (ParseException e1) {
			logger.error(e1.getMessage(),e1);
		}
		UserGetUserInfoByOpenIdResponse   response = null;
		UserInfoResultTO resultTO=new UserInfoResultTO();
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				response = getClient(sessionKey).execute(req);
				if (response != null && response.getCode() != null) {
					String code = response.getCode();
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						logger.info("many time get shopInfo failure！！！");
					}
					if(!code.equals("0")) {
						ApiInvokException ae = new ApiInvokException(response.getCode(), response.getZhDesc());
						throw ae;
					}
				}
				if (response == null) {
					ApiInvokException ae = new ApiInvokException("-", "获取京东用户response为空");
					throw ae;
				}
				OAuthUserInfo user=	response.getGetuserinfobyappidandopenidResult().getData();
				resultTO.setUserInfo(user);
				break;
			} catch (ApiInvokException e) {
				throw e;
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					e.printStackTrace();
					throw e;
				}
			}
		}
		return resultTO;
	}
	
	
	
//	public  String getUserInfoBySessionKeyByUid(String uid,String sessionKey) throws Exception {
//		NicknamebyuidQueryRequest req = new NicknamebyuidQueryRequest();
//		String nickName="";
//		req.setUid(uid);
//		JdClient client=getClient(sessionKey);
//		NicknamebyuidQueryResponse   response = null;
//		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
//			try {
//				response = client.execute(req);
//				if (response != null && response.getCode() != null) {
//					String code = response.getCode();
//					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//						logger.info("many time get userNick failure！！！");
//					}
//					if(!code.equals("0")) {
//						GainShopDataFailException ae = new GainShopDataFailException();
//						ae.setErrorMsg(response.getZhDesc());
//						ae.setErrorCode(response.getCode());
//						throw ae;
//					}
//				}
//				if (response == null) {
//					GainShopDataFailException ae = new GainShopDataFailException();
//					ae.setErrorMsg("获取用户昵称信息为空");
//					ae.setErrorCode("---");
//					throw ae;
//				}
//				 nickName=	response.getNickName();
//				logger.info("get nickName:{}",nickName);
//				break;
//			} catch (GainShopDataFailException e) {
//				throw e;
//			} catch (Exception e) {
//				if (!(e.getCause() instanceof SocketTimeoutException)) {
//					throw e;
//				}
//				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//					throw e;
//				}
//			}
//		}
//		return nickName;
//	}
	
}
