package com.pes.jd.data.api;

import com.jd.open.api.sdk.request.udp.MarketBdpOLShopSumQueryRequest;
import com.jd.open.api.sdk.response.udp.MarketBdpOLShopSumQueryResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.ShopPvUvTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
/**
 * * 1:接口名-   jingdong.market.bdp.OLShopSum.query
 *      * 描述-      店铺分平台流量
 *      * 接口地址-  https://open.jd.com/home/<USER>/doc/api?apiCateId=48&apiId=2736&apiName=jingdong.market.bdp.OLShopSum.query
 */
@Service
public class ShopPvUvOperator extends BaseOperator {

	private static final Logger logger = LoggerFactory.getLogger(ShopPvUvOperator.class);
	/**
	 * 获取店铺的pv uv
	 * @throws Exception 
	 */
	public ShopPvUvTO getShopPvUv(String sessionKey, String tp, String date) throws Exception {
		MarketBdpOLShopSumQueryRequest req = new MarketBdpOLShopSumQueryRequest();
		logger.info("-------------------PvUvStart------------------------");
		logger.info("pvuvsessionKey:{}pvuvTp:{}pvuvDate:{}",sessionKey, tp, date);
		System.out.println(date);
		req.setTp(tp);
		req.setDt(date);
		req.setField("platformDesc,avgRt,avgRtNewuser,avgRtOlduser,pv"
				+ ",uv,landingTimes ,quitTimes,itemPv,itemUv,"
				+ "homepagePv,homepageUv,addToCartSkunu,addToCartSkutypenum,"
				+ "addToCartUsers,ordNumDeal,saleQttyDeal,beforePrefrAmountDeal,"
				+ "afterPrefrAmountDeal,ordUserNumDeal,pvOlduser,uvOlduser,visitsOlduser,pvNewuser,"
				+ "uvNewuser,visitsNewuser,tp,venderId,venderName,shopId,shopName,visits,bounceTimes,dt");
		MarketBdpOLShopSumQueryResponse response = null;
		int num = 0;
		int retryNum = 0;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				response = getClient(sessionKey).execute(req);
				//logger.info("获取店铺的pv uv:response.getMsg{}, errorCode{}", response.getMsg(), response.getCode());
				retryNum++;
				if (response != null && "0".equals(response.getCode())) {
					break;
				} else {
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						logger.error("fetchShopPvUv error_code:{}, error_msg:{}" , response.getCode(), response.getMsg());
						GainShopDataFailException dataFailException = new GainShopDataFailException("店铺PvUv获取失败");
						dataFailException.setErrorMsg(response.getMsg());
						dataFailException.setErrorCode(response.getCode());
						throw dataFailException;
					} else {
						Thread.sleep(1000);
						continue;
					}
				}

			} catch (GainShopDataFailException e) {
				throw e;
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					logger.error(e.getMessage(), e);
					throw e;
				}
			}
		}
		return new ShopPvUvTO(response, num, retryNum) ;
	}
}
