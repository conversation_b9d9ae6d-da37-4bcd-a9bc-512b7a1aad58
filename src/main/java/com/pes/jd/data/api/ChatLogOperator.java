package com.pes.jd.data.api;

import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.im.ApiService.response.get.ChatLog;
import com.jd.open.api.sdk.domain.im.ApiService.response.get.ChatLogPage;
//import com.jd.open.api.sdk.domain.im.OpenApiService.response.get.ChatLog;
//import com.jd.open.api.sdk.domain.im.OpenApiService.response.get.ChatLogPage;
import com.jd.open.api.sdk.request.im.ImPopChatlogGetRequest;
//import com.jd.open.api.sdk.request.im.ImPopChatlogOpenapiGetRequest;
import com.jd.open.api.sdk.response.im.ImPopChatlogGetResponse;
//import com.jd.open.api.sdk.response.im.ImPopChatlogOpenapiGetResponse;
import com.pes.jd.Constants.AppConstants;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.ChatLogResultTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 接口：jingdong.im.pop.chatlog.openapi.get
 * 描述：获取聊天记录
 * 地址：http://open.jd.com/home/<USER>/doc/api?apiCateId=53&apiId=3297&apiName=jingdong.im.pop.chatlog.openapi.get
 */
@Component
public class ChatLogOperator extends BaseOperator {
    private static final Logger logger = LoggerFactory.getLogger(ChatLogOperator.class);

    /**
     * @param startDate 查询聊天对象的开始时间点 格式： yyyy-mm-dd
     * @param endDate   查询聊天对象的结束时间点 格式： yyyy-mm-dd
     * @param waiter    咚咚账号
     * @param customer  买家账号
     * @return ChatLogResultTO 返回封装成本地对象
     * @throws Exception
     * <AUTHOR> 查找客服聊天记录
     */
    public ChatLogResultTO getChatLogs(String sessionKey, Date startDate, Date endDate, String waiter, String customer) throws Exception {
        int num = 1;
        int retryNum = 0;
        int pageSize = 50;//每页最大数为500
        int page = 0;
        ImPopChatlogGetResponse response = null;
        List<ChatLog> chatLogLst = null;
        JdClient client = getClientWrapper(sessionKey,AppConstants.SERVER_URL);
        List<ChatLog> chatLogAllLst = new ArrayList<ChatLog>();
        ChatLogResultTO chatLogModel;
        ImPopChatlogGetRequest req = new ImPopChatlogGetRequest();
        req.setCustomer(customer);
        req.setWaiter(waiter);
        req.setStartTime(startDate);
        req.setEndTime(endDate);
        req.setPageSize(pageSize);
        boolean hasNext = false;
        do {
            page++;
            num++;
            req.setPage(page);
            for (int i = 0; i < CommonConstants.RECALLAPI_TIMES; i++) {
                try {
                    response = client.execute(req);
                    retryNum++;
                    if (response != null) {
                        if (response.getCode().equals("0")) {

                            break;
                        } else {
                            GainShopDataFailException dataFailException = new GainShopDataFailException("聊天记录获取失败");
                            dataFailException.setErrorMsg(response.getMsg());
                            dataFailException.setErrorCode(response.getCode());
                            throw dataFailException;
                        }
                    } else {
                        if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                            GainShopDataFailException dataFailException = new GainShopDataFailException();
                            dataFailException.setErrorMsg("第" + i + "次拉去chatLog返回的response为空");
                            dataFailException.setErrorCode("response is empty");
                            logger.error("chatLog response is empty");
                            throw dataFailException;
                        }
                    }
                } catch (GainShopDataFailException e) {
                    if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                        if(response.getCode().equals("65") && response.getZhDesc().startsWith("平台连接后端服务超时")){
                            return new ChatLogResultTO(chatLogAllLst, num, retryNum - num);
                        }
                        logger.error("waiter：{}和customer：{},pull chatLog date:{}  ,第几{} times response code:{}------------msg:{}", waiter, customer, startDate, i + 1, response.getCode(), response.getZhDesc());
                        throw e;
                    }
                } catch (Exception e) {
                    if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                        logger.error(e.getMessage(), e);
                        throw e;
                    }
                }
            }
            ChatLogPage result = response.getChatLogPage();
            if (result != null) {
                chatLogLst = result.getChatLogList();
                if (CollectionUtils.isNotEmpty(chatLogLst)) {
                    chatLogAllLst.addAll(chatLogLst);
                    if (chatLogLst.size() < pageSize) {
                        hasNext = false;
                    }else hasNext = page < 10;
                } else {
                    hasNext = false;
                }
            } else {
                hasNext = false;
            }
        } while (hasNext);
        chatLogModel = new ChatLogResultTO(chatLogAllLst, num, retryNum - num);
        //	logger.info("csNick:{},buyerNick:{} pull chatLog size:{}",waiter,customer,chatLogAllLst.size());
        return chatLogModel;
    }

    public ChatLogResultTO getChatLogs(String sessionKey, Date startDate, Date endDate, String waiter) throws Exception {
        int num = 1;
        int retryNum = 0;
        int pageSize = 500;//每页最大数为500
        int page = 0;
        ImPopChatlogGetResponse response = null;
        List<ChatLog> chatLogLst = null;
        JdClient client = getClient(sessionKey,AppConstants.SERVER_URL);
        List<ChatLog> chatLogAllLst = new ArrayList<>();
        ChatLogResultTO chatLogModel;
        ImPopChatlogGetRequest req = new ImPopChatlogGetRequest();
     //   req.setCustomer(customer);
        req.setWaiter(waiter);
        req.setStartTime(startDate);
        req.setEndTime(endDate);
        req.setPageSize(pageSize);
//        req.setDataType(2);
//        req.setTimeNo("100");
        boolean hasNext = false;
        do {
            page++;
            logger.info("======>waiter: {}, page:{}", waiter, page);
            num++;
            req.setPage(page);
            for (int i = 0; i < CommonConstants.RECALLAPI_TIMES; i++) {
                try {
                    response = client.execute(req);
                    retryNum++;
                    if (response != null) {
                        if (response.getCode().equals("0")) {

                            break;
                        } else {
                            GainShopDataFailException dataFailException = new GainShopDataFailException("聊天记录获取失败");
                            dataFailException.setErrorMsg(response.getMsg());
                            dataFailException.setErrorCode(response.getCode());
                            throw dataFailException;
                        }
                    } else {
                        if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                            GainShopDataFailException dataFailException = new GainShopDataFailException();
                            dataFailException.setErrorMsg("第" + i + "次拉取chatLog返回的response为空");
                            dataFailException.setErrorCode("response is empty");
                            logger.error("chatLog response is empty");
                            throw dataFailException;
                        }
                    }
                } catch (GainShopDataFailException e) {
                    if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                        assert response != null;
                        if(response.getCode().equals("65") && response.getZhDesc().startsWith("平台连接后端服务超时")){
                            return new ChatLogResultTO(chatLogAllLst, num, retryNum - num);
                        }
                        logger.error("======> getChatLogs waiter：{}, date:{} , retry_times: {} times,  response_code:{}, msg:{}", waiter, startDate, i + 1, response.getCode(), response.getZhDesc());
                        throw e;
                    }
                } catch (Exception e) {
                    if (i == CommonConstants.RECALLAPI_TIMES - 1) {
                        logger.error(e.getMessage(), e);
                        throw e;
                    }
                }
            }
            assert response != null;
            ChatLogPage result = response.getChatLogPage();
            if (result != null) {
                chatLogLst = result.getChatLogList();
                if (CollectionUtils.isNotEmpty(chatLogLst)) {
                    chatLogAllLst.addAll(chatLogLst);
                    if(page >= 25){
                        hasNext = false;
                    }else if (chatLogLst.size() >= pageSize) {
                        hasNext = true;
                    }
                } else {
                    hasNext = false;
                }
            } else {
                hasNext = false;
            }
        } while (hasNext);
        chatLogModel = new ChatLogResultTO(chatLogAllLst, num, retryNum - num);
        return chatLogModel;
    }
}
