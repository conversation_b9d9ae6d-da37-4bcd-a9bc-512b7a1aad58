package com.pes.jd.data.api;

import com.jd.open.api.sdk.request.order.PopOrderGetRequest;
import com.jd.open.api.sdk.response.order.PopOrderGetResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.model.TO.SinglePopOrderTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;

@Service
public class SinglePopOrderForMessageOperator extends BaseOperator {

	private static final Logger logger = LoggerFactory.getLogger(SinglePopOrderForMessageOperator.class);

	/**
	 * 根据jcq进行查询出订单补上出库信息
	 * 
	 * @throws Exception
	 */
	public SinglePopOrderTO getSinglePopOrder(Long orderId, String sessionKey) throws Exception {
		PopOrderGetRequest req = new PopOrderGetRequest();
		req.setOptionalFields(
				"orderId,venderId,orderPayment,orderType,payType,orderSellerPrice,freightPrice,orderState,orderStartTime,orderEndTime,itemInfoList,paymentConfirmTime,modified,pin,parentOrderId,orderTotalPrice,balanceUsed,sellerDiscount,couponDetailList,consigneeInfo,orderSign");
		req.setOrderId(orderId);
		PopOrderGetResponse response;
		int num = 0;
		int retryNum = 0;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.ORDER_MESSAGE_RECALLAPI_TIMES; recallApiTimes++) {
			try {
				response = getClient(sessionKey).execute(req);
				logger.debug("消息队列获取订单信息:response.getMsg{}, errorCode{}", response.getMsg(), response.getCode());
				retryNum++;
				if(response != null) {
					if("0".equals(response.getCode())) {
						if(response.getOrderDetailInfo() != null) {
							return new SinglePopOrderTO(response, num, retryNum);
						}
					}else {
						if("19".equals(response.getCode())) {
							return new SinglePopOrderTO(new PopOrderGetResponse(), num, retryNum);
						}
					}
					Thread.sleep(200);
					continue;
				}
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
				if (recallApiTimes >= CommonConstants.ORDER_MESSAGE_RECALLAPI_TIMES - 1) {
					logger.error(e.getMessage(), e);
					throw e;
				}
			}
		}
		return new SinglePopOrderTO(new PopOrderGetResponse(), num, retryNum);
	}

}
