package com.pes.jd.data.api;

import com.jd.open.api.sdk.request.order.PopOrderNotPayOrderByIdRequest;
import com.jd.open.api.sdk.response.order.PopOrderNotPayOrderByIdResponse;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.OrderDataNotPayTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;

/**
 * <AUTHOR>
 * @version 2019-05-15 18:15
 */
@Service
public class OrderNotPayOperator extends BaseOperator {
    private static final Logger logger = LoggerFactory.getLogger(OrderNotPayOperator.class);

    public OrderDataNotPayTO getOrderNotPay(Long orderId, String sessionKey) throws Exception {
        PopOrderNotPayOrderByIdRequest req = new PopOrderNotPayOrderByIdRequest();
        req.setOrderId(orderId);
        int num = 0;
        int retryNum = 0;
        PopOrderNotPayOrderByIdResponse response = null;
//        for (int recallApiTimes = 0; recallApiTimes <CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
        	for (int recallApiTimes = 0; recallApiTimes <1; recallApiTimes++) {
            try {

                response = getClient(sessionKey).execute(req);
//                logger.info("获取未付款订单信息:response.getMsg{}, errorCode{}", response.getMsg(), response.getCode());
                retryNum++;
                if (response != null && response.getCode().equals("0") && null!=response.getOrderDataNotPayInfo()) {
                    break;
                } else {
                    if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                        logger.info("fetchShopOrder error_code:{}, error_msg:{}", response.getCode(), response.getMsg());
                        GainShopDataFailException dataFailException = new GainShopDataFailException("未付款订单获取失败");
                        dataFailException.setErrorMsg(response.getMsg());
                        dataFailException.setErrorCode(response.getCode());
                        throw dataFailException;
                    } else {
//                        Thread.sleep(1000);
                        continue;
                    }
                }

            } catch (GainShopDataFailException e) {
                throw e;
            } catch (Exception e) {
                if (!(e.getCause() instanceof SocketTimeoutException)) {
                    throw e;
                }
                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                    logger.error(e.getMessage(), e);
                    throw e;
                }
            }
        }


        return new OrderDataNotPayTO(response, num, retryNum);
    }


}
