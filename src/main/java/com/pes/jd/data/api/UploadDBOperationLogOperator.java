package com.pes.jd.data.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class UploadDBOperationLogOperator {

	private Logger logger = LoggerFactory.getLogger(getClass());
	
//	public boolean uploadDBOperation(UploadDBOperationParam param) {
//		JdClient client=new DefaultJdClient("https://api-log.jd.com/routerjson","","321CEAB001F59FDDA67DC388C0575958","2b5f6d0cbde1419c9a64e8ce694f22e9"); 
//		IsvUploadDBOperationLogRequest request=new IsvUploadDBOperationLogRequest();
//		request.setUserIp("***********"); 
//		request.setAppName("日志应用");
//		request.setJosAppKey("444c8e06354448d78b32463022f47a040yzh");
//		request.setDeviceId("123df232dds");
//		request.setUserId("1111");
//		request.setUrl("//jd.com/getorder");
//		request.setDb("order");
//		request.setSql("select * from order where id=1");
//		request.setUserIp(param.getUserIp()); 
//		request.setAppName(param.getAppName());
//		request.setJosAppKey(param.getJosAppKey());
//		request.setDeviceId(param.getDeviceId());
//		request.setUserId(param.getUserId());
//		request.setUrl(param.getUrl());
//		request.setDb(param.getDb());
//		request.setSql(param.getSql());
//		request.setTimeStamp(param.getTimeStamp());
//		IsvUploadDBOperationLogResponse response= null;
//		boolean f = false;
//		try {
//			response=client.execute(request);
//			if(response.getC()==0){
//				logger.info("数据库操作日志上传成功！",response.getMsg());
//				System.out.println("数据库操作日志上传成功！"+response.getMsg());
//				f = true;
//			}else{
//				System.out.println("数据库操作日志上传失败！"+response.getMsg());
//				f = false;
//			}
//		} catch (Exception e) {
//			System.out.println("数据库操作日志上传失败！"+response.getMsg());
//			e.printStackTrace();
//			f = false;
//		}
//		return f;

//	}

}
