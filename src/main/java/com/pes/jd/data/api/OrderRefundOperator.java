package com.pes.jd.data.api;

import cn.hutool.core.collection.CollUtil;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryPageList.RefundApplyVo;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.OrderRefundTO;
import com.pes.jd.task.executor.FetchOrderRefundDataExecutor;
import com.pes.jd.util.DateFormatUtils;
import com.yiyitech.support.task.AsyncTask;
import com.yiyitech.support.task.AsyncTaskUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class OrderRefundOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(OrderRefundOperator.class);

	public OrderRefundTO getOrderRefundData(JobShopDTO shop, Date startDate, Date endDate, int fetchType) {
		String sessionKey = shop.getSessionKey();
		List<RefundApplyVo> allRefund = new ArrayList<>();
		AtomicInteger num = new AtomicInteger(0);
		AtomicInteger retryNum = new AtomicInteger(0);
		//long orderRefundStartTime = System.currentTimeMillis();
		List<Date> threadDates = DateFormatUtils.splitDateToList(startDate, endDate, 60 * 60L);
		List<AsyncTask> taskList = new ArrayList<>();
		int task = 0;
		for (int t = 0, threadDateSize = threadDates.size(); t < threadDateSize; t += 2) {
			int finalT = t;
			AsyncTask asyncTask = new AsyncTask(task) {
				@Override
				public Object run() {
					FetchOrderRefundDataExecutor cdi = new FetchOrderRefundDataExecutor(threadDates.get(finalT), threadDates.get(finalT + 1), sessionKey, fetchType);
					try {
						return cdi.call();
					} catch (Exception e) {
						logger.error(e.getMessage(), e);
						return null;
					}
				}
			};
			taskList.add(asyncTask);
			task++;
		}
		if (CollUtil.isNotEmpty(taskList)) {
			Object[] objects = AsyncTaskUtil.runAll(taskList);
			Arrays.stream(objects).filter(Objects::nonNull).forEach(callBack -> {
				OrderRefundTO future = (OrderRefundTO) callBack;
				allRefund.addAll(future.getRefunds());
				num.addAndGet(future.getNum());
				retryNum.addAndGet(future.getRetryNum());
			});
		}
		//long orderRefundEndTime = System.currentTimeMillis();
		//logger.info("Order Refund use ----------time{}", (orderRefundEndTime - orderRefundStartTime));
		return new OrderRefundTO(allRefund, num.get(), retryNum.get());
	}

}
