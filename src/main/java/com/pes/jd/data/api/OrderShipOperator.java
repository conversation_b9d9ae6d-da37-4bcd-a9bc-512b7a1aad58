package com.pes.jd.data.api;

//import com.jd.open.api.sdk.request.etms.PopJmOrderShipServiceGetOrderShipRequest;
//import com.jd.open.api.sdk.response.etms.PopJmOrderShipServiceGetOrderShipResponse;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 2019-06-02 18:03
 */
@Service
public class OrderShipOperator extends BaseOperator {

    private static Logger logger = LoggerFactory.getLogger(OrderShipOperator.class);


//    public PopJmOrderShipServiceGetOrderShipResponse getOrderShipInfo(String orderId, String sessionkey) {
//        PopJmOrderShipServiceGetOrderShipRequest request = new PopJmOrderShipServiceGetOrderShipRequest();
//        request.setOrderId(orderId);
//        int num = 0;
//        int retryNum = 0;
//        PopJmOrderShipServiceGetOrderShipResponse response = null;
//        for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
//            try {
//                response = getClient(sessionkey).execute(request);
//                logger.info("消息队列获取订单信息:response.getMsg{}, errorCode{}", response.getMsg(), response.getCode());
//                System.out.println("消息队列获取订单信息:response.getMsg{}=======" + response.getMsg() + "errorCode{}======" + response.getCode());
//                retryNum++;
//                if (response != null && response.getCode().equals("0")) {
//                    break;
//                } else {
//                    if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//                        logger.error("fetchShopOrder error_code:{}, error_msg:{}", response.getCode(), response.getMsg());
//                        System.out.println("fetchShopOrder error_code{}=====" + response.getCode() + "error_msg:{}" + response.getMsg());
//                        GainShopDataFailException dataFailException = new GainShopDataFailException("获取物流信息失败");
//                        dataFailException.setErrorMsg(response.getMsg());
//                        dataFailException.setErrorCode(response.getCode());
//                        throw dataFailException;
//                    } else {
//                        Thread.sleep(1000);
//                        continue;
//                    }
//                }
//            } catch (Exception e) {
//
//            }
//        }
//        return response;
//    }

}
