package com.pes.jd.data.api;

import com.google.common.collect.Lists;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.order.PresaleOrderExportServiceForJOS.response.getPresaleOrderByPage.PresaleOrderVO;
import com.jd.open.api.sdk.domain.order.PresaleOrderExportServiceForJOS.response.getPresaleOrderByPage.ServiceResponse;
import com.jd.open.api.sdk.request.order.PresaleOrderUpdateOrderGetPresaleOrderByPageRequest;
import com.jd.open.api.sdk.response.order.PresaleOrderUpdateOrderGetPresaleOrderByPageResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.PresaleOrderTO;
import com.pes.jd.util.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * * 1:接口名-   jingdong.presale.order.updateOrder.getPresaleOrderByPage
 *      * 描述-     预售订单详情分页查询（支持多种查询条件）
 *      * 接口地址-  https://open.jd.com/home/<USER>/doc/api?apiCateId=55&apiId=2869&apiName=jingdong.presale.order.updateOrder.getPresaleOrderByPage
 */
@Service
public class PresaleOrderOperator extends BaseOperator {
    private static final Logger logger = LoggerFactory.getLogger(PresaleOrderOperator.class);

    /**
     * 成交:2,3,23,33
     * 待付定金/全款: 0,12,31
     * 待付尾款: 1,13,22
     * 付定金流失: 11,121,990,992
     * 付尾款流失: 21,131,221,991,993,994
     * 付全款流失: 311,995
     * 其它: 1000,1001,2000
     *
     * @param shop
     * @param
     * @param startDate
     * @param endDate
     * @throws Exception
     */
    public PresaleOrderTO pullPresaleOrder(JobShopDTO shop, Date startDate, Date endDate)
            throws Exception {

        JdClient client = getClient(shop.getSessionKey());

        PresaleOrderUpdateOrderGetPresaleOrderByPageRequest req = new PresaleOrderUpdateOrderGetPresaleOrderByPageRequest();

        int num = 0;
        int retryNum = 0;

        PresaleOrderUpdateOrderGetPresaleOrderByPageResponse presaleOrderResponse = null;
        List<PresaleOrderVO> presaleOrderLst = Lists.newArrayList();

        int page = 0;
        int beginIndex = 0;
        int endIndex = 0;
        int pageSize = 50;// 每页暂时查询50条记录


   /*     if (endDate.after(new Date())) {
            // 结束时间在当前时间之后：当天
//            logger.info("不支持当天的数据拉取：continue ; daye = {}", date);
            return new PresaleOrderTO(presaleOrderLst, 0, 0);
        }*/
//        logger.info("店铺:{},{}拉取数据开始", shop.getShopId(), date);

        req.setOrderStatusItem("0,1,2,3,11,12,13,21,22,23,31,33,121,131,221,311,990,991,992,993,994,995,1000,1001,2000");
        req.setStartTime(startDate);
        req.setEndTime(endDate);
        boolean hasNext = false;
        do {
            num++;
            page++;
            req.setBeginIndex(beginIndex);
            req.setEndIndex(pageSize);
            hasNext = false;
            for (int i = 0; i < CommonConstants.RECALLAPI_TIMES; i++) {
                try {
                    presaleOrderResponse = client.execute(req);
                    if (presaleOrderResponse == null
                            || presaleOrderResponse.getCode() == null) {
                        //错误
                        GainShopDataFailException dataFailException = new GainShopDataFailException("预售订单获取失败");
                        throw dataFailException;
                    } else {
                        //正常返回
                        if ("0".equals(presaleOrderResponse.getCode())) {
                            break;
                        } else {
                            GainShopDataFailException dataFailException = new GainShopDataFailException("预售订单获取失败");
                            dataFailException.setErrorMsg(presaleOrderResponse.getMsg());
                            dataFailException.setErrorCode(presaleOrderResponse.getCode());
                            throw dataFailException;
                        }
                    }
                } catch (Exception e) {
                    if (i >= CommonConstants.RECALLAPI_TIMES - 1) {
                        logger.error(e.getMessage(), e);
                        GainShopDataFailException ae = new GainShopDataFailException("预售订单获取失败");
                        ae.setErrorCode("-");
                        ae.setErrorMsg("error - 通过京东接口获取店铺预售订单失败");
                        throw ae;
                    }
                }
            }
            if (presaleOrderResponse != null) {
                ServiceResponse returnType = presaleOrderResponse.getReturnType();
                if (returnType != null) {
                    List<PresaleOrderVO> data = returnType.getData();
                    for (PresaleOrderVO vo : data) {
//                        if (StringUtils.isNotBlank(vo.getProductName()))
//                            logger.info("----> 预售订单ID: " + vo.getOrderId() + "\tskuID: " + vo.getSkuID() + "\t商品名" + vo.getProductName());
                    }
                    if (CollectionUtils.isNotEmpty(data)) {
                        presaleOrderLst.addAll(data);

                        if (data.size() < pageSize) {
//                            logger.info("拉取完，当前page:{},拉取数量={}", page, data.size());
                            return new PresaleOrderTO(presaleOrderLst, num, retryNum - num);
                        } else {
                            beginIndex += pageSize;
                            hasNext = true;
//                            logger.info("继续拉取下一页，当前page:{}", page);
                        }
                    } else {
                        hasNext = false;
                    }
                } else {
                    hasNext = false;
                }
            } else {
                hasNext = false;
            }
        } while (hasNext);
        return new PresaleOrderTO(presaleOrderLst, num, retryNum - num);
    }

    public static void main(String[] args) throws Exception {
        PresaleOrderOperator o = new PresaleOrderOperator();

        JobShopDTO shop = new JobShopDTO();
        shop.setShopId(732842L);
        shop.setSessionKey("ccb9d51cf8ed49a4b8e808af2e59f6cfwq1m");
        Date date = DateUtils.parseYMd("2019-04-28");
        Date startDate = DateUtils.getStartTimeOfDate(date);
        Date endDate = DateUtils.getEndTimeOfDate(date);
        PresaleOrderTO presaleOrderTO = o.pullPresaleOrder(shop,startDate, endDate);
        presaleOrderTO.getPresaleOrderVOList().forEach(ele -> System.out.println(ele.getOrderId()));

    }
}
