package com.pes.jd.data.api;

import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.getRemarkByModifyTime.OrderRemark;
import com.jd.open.api.sdk.domain.order.OrderRemarkService.response.getRemarkByModifyTime.OrderRemarkResult;
import com.jd.open.api.sdk.request.order.OrderVenderRemarkQueryByOrderIdRequest;
import com.jd.open.api.sdk.request.order.PopOrderGetRemarkByModifyTimeRequest;
import com.jd.open.api.sdk.response.order.OrderVenderRemarkQueryByOrderIdResponse;
import com.jd.open.api.sdk.response.order.PopOrderGetRemarkByModifyTimeResponse;
import com.pes.jd.Constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.TO.OrderRemarkTO;
import com.pes.jd.util.DateFormatUtils;
import com.pes.jd.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * * 1:接口名-   jingdong.pop.order.getRemarkByModifyTime
 *      * 描述-      根据修改时间段获取订单备注
 *      * 接口地址-  https://open.jd.com/home/<USER>/doc/api?apiCateId=55&apiId=743&apiName=jingdong.pop.order.getRemarkByModifyTime
 */
@Service
public class OrderReMarkOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(OrderReMarkOperator.class);
	
	public OrderRemarkTO getOrderRemark(JobShopDTO shop,Date startDate,Date endDate) throws Exception{
		PopOrderGetRemarkByModifyTimeRequest req = new PopOrderGetRemarkByModifyTimeRequest();
		int num = 0;
		int retryNum = 0;
		List<Date> dateList = DateUtil.splitDate(startDate, endDate);
		if (CollectionUtils.isEmpty(dateList)) {
//			logger.info("日期输入有误！");
			return new OrderRemarkTO(new ArrayList<>(), num, retryNum);
		}
		int pageSize = 100;
		PopOrderGetRemarkByModifyTimeResponse response = null;
		List<OrderRemark> remarkList = null;
		
		for (int i = 0; i < dateList.size(); i++) {
			Date queryStartDate = dateList.get(i);
			Date queryEndDate = DateUtil.getEndTimeOfDate(dateList.get(i));
//			logger.info("店铺:{},{}拉取数据开始", shopId, dateList.get(i));
			if (DateUtil.getEndTimeOfDate(dateList.get(i)).after(new Date())) {
				// 结束时间在当前时间之后：当天
//				logger.info("不支持当天的数据拉取：continue ; daye = {}", dateList.get(i));
				continue;
			}
			req.setPage(1);// （只返回第一页的数据，请使用时间滚动的方式获取数据；pageSize=100)
			req.setSortTime(1);// 1 正序 -1 倒序
			while (queryEndDate.getTime() > queryStartDate.getTime()) {// 分时段获取登陆记录数据
				num++;
				req.setStartTime(DateFormatUtils.formatYMdHms(queryStartDate));
				req.setEndTime(DateFormatUtils.formatYMdHms(queryEndDate));
				int recallApiTimes = 0;
				for (; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
					retryNum++;
					try {
						response = getClient(shop.getSessionKey()).execute(req);
						if (response == null || response.getCode() == null) {
//							logger.info("loginLogResponse = null");
							GainShopDataFailException ae = new GainShopDataFailException("订单备注拉取失败");
							throw ae;
						} else {
							// 正常返回
							if ("0".equals(response.getCode())) {
								OrderRemarkResult orderRemarkResult = response.getGetremarkbymodifytimeResult();
								if (orderRemarkResult != null) {
									List<OrderRemark> retRemarkList = orderRemarkResult.getRemarkList();
									if (CollectionUtils.isNotEmpty(retRemarkList)) {
										if (remarkList == null) {
											remarkList = retRemarkList;
										} else {
											remarkList.addAll(retRemarkList);
										}
									} else {
//										 logger.info("店铺【{}】 - {} pull data orderRemarkList isEmpty",shop.getTitle(),dateList.get(i));
									}
								} else {
//									 logger.info("店铺【{}】 - {} pull data returnType is null",shop.getTitle(),dateList.get(i));
								}
								break;
							}else{
								GainShopDataFailException ae = new
								GainShopDataFailException("订单备注拉取失败");
								ae.setErrorCode(response.getCode());
								ae.setErrorMsg(response.getMsg());
								throw ae;
							}
						}
					} catch (Exception e) {
						if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
							GainShopDataFailException ae = new GainShopDataFailException("订单备注拉取失败");
							ae.setErrorCode("-");
							ae.setErrorMsg("error - 通过京东接口获取订单备注失败");
							throw ae;
						}
						Thread.sleep(500L); // 睡眠半秒 TODO
					}
				}
				if (response == null || response.getGetremarkbymodifytimeResult() == null
						|| response.getGetremarkbymodifytimeResult().getRemarkList() == null) {
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						GainShopDataFailException e = new GainShopDataFailException("订单备注拉取失败");
						throw e;
					}
//					logger.info("店铺:{},{}拉取数据为空,退出循环", shopId, dateList.get(i));
					break;
				} else {
					if (response.getGetremarkbymodifytimeResult().getRemarkList().size() < pageSize) {
//						logger.info("当天拉取完，当前日期:{},拉取数量={}", startDate, remarkList.size());
						break;
					} else {
						//经验证：返回的集合是按照修改时间顺序返回
						Date indexDate = remarkList.get(remarkList.size()-1).getModified();//中间截至时间点
//						logger.info("当前时间段拉取完，继续拉取下一时间段，当前时间段:{}-{}", queryStartDate,indexDate);
						//开始查询时间：上次时间段结束时间+1秒
						queryStartDate = DateFormatUtils.parseYMdHms(DateUtil.sdf.format(indexDate.getTime()+1000));
					}
				}
			}
//			logger.info("店铺【{}】,{}拉取数据结束", shopId, dateList.get(i));
        }
        return new OrderRemarkTO(remarkList, num, retryNum - num);

    }


    public OrderRemarkTO getOrderRemark(Long orderId, String sessionKey) throws Exception {
        OrderVenderRemarkQueryByOrderIdRequest req = new OrderVenderRemarkQueryByOrderIdRequest();
        int num = 0;
        int retryNum = 0;
        req.setOrderId(orderId);
        OrderVenderRemarkQueryByOrderIdResponse response = null;
        for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
            try {
                response = getClient(sessionKey).execute(req);
                logger.info("消息队列获取订单信息:response.getMsg{}, errorCode{}", response.getMsg(), response.getCode());
                System.out.println("消息队列获取订单信息:response.getMsg{}=======" + response.getMsg() + "errorCode{}======" + response.getCode());
                retryNum++;
                if (response != null &&
                        response.getCode().equals("0") &&
                        response.getVenderRemarkQueryResult() != null) {
                    break;
                } else {
                    if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                        logger.error("fetchShopOrder error_code:{}, error_msg:{}", response.getCode(), response.getMsg());
                        System.out.println("fetchShopOrder error_code{}=====" + response.getCode() + "error_msg:{}" + response.getMsg());

                        GainShopDataFailException dataFailException = new GainShopDataFailException("订单备注获取失败");
                        dataFailException.setErrorMsg(response.getMsg());
                        dataFailException.setErrorCode(response.getCode());
                        throw dataFailException;
                    } else {
                        Thread.sleep(1000);
                        continue;
                    }
                }

            } catch (GainShopDataFailException e) {
                throw e;
            } catch (Exception e) {
                if (!(e.getCause() instanceof SocketTimeoutException)) {
                    throw e;
                }
                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
                    logger.error(e.getMessage(), e);
                    throw e;
                }
            }
        }
        return new OrderRemarkTO(response, num, retryNum);
    }




}
  
