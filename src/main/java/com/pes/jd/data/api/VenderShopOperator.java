package com.pes.jd.data.api;

import com.jd.open.api.sdk.domain.seller.VenderBasicSafService.response.getBasicVenderInfoByVenderId.VenderBasicResult;
import com.jd.open.api.sdk.request.seller.VenderInfoQueryByPinRequest;
import com.jd.open.api.sdk.request.seller.VenderVbinfoGetBasicVenderInfoByVenderIdRequest;
import com.jd.open.api.sdk.response.seller.VenderInfoQueryByPinResponse;
import com.jd.open.api.sdk.response.seller.VenderInfoResult;
import com.jd.open.api.sdk.response.seller.VenderVbinfoGetBasicVenderInfoByVenderIdResponse;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.exception.ApiInvokException;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.TO.ShopInfoResultTO;
import com.pes.jd.util.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.text.ParseException;
import java.util.Date;

/**
 * ClassName:ShopOperator <br/>
 * Function: TODO ADD FUNCTION. <br/>
 * Reason: TODO ADD REASON. <br/>
 * Date: 2018年10月24日 上午10:18:37 <br/>
 *
 * <AUTHOR>
 * @version
 * @since JDK 1.8
 */
@Component
public class VenderShopOperator extends BaseOperator {
	private static final Logger logger = LoggerFactory.getLogger(VenderShopOperator.class);

	public ShopInfoResultTO getShopInfoByPin(String sellerNick,String sessionKey) throws Exception {
		VenderInfoQueryByPinRequest req = new VenderInfoQueryByPinRequest();
		req.setExtJsonParam(sellerNick);
		VenderInfoQueryByPinResponse shopGetResponse = null;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				shopGetResponse = getClient(sessionKey).execute(req);
				if (shopGetResponse != null
						&& StringUtils.isNotBlank(shopGetResponse.getCode())
						&&"0".equals(shopGetResponse.getCode())) {
					VenderInfoResult shopInfoResult=shopGetResponse.getVenderInfoResult();
					if(shopInfoResult!=null){
						return new ShopInfoResultTO(shopInfoResult);
					}
				}else  {
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						GainShopDataFailException dataFailException = new GainShopDataFailException("获取京东店铺信息失败");
						dataFailException.setErrorMsg(shopGetResponse==null?"获取京东店铺response为空":shopGetResponse.getZhDesc());
						dataFailException.setErrorCode(shopGetResponse==null?" ":shopGetResponse.getCode());
						throw dataFailException;
					}
				}

			} catch (SocketTimeoutException | ConnectException e) {
				if (recallApiTimes >= CommonConstants.RECALLAPI_TIMES - 1) {
					throw e;
				}
			}
		}
	return null;
	}

	public ShopInfoResultTO getSelfOrPopShopInfo(String sessionKey) throws ApiInvokException {
		VenderVbinfoGetBasicVenderInfoByVenderIdRequest req = new VenderVbinfoGetBasicVenderInfoByVenderIdRequest();
		try {
			req.setSource(1);
			req.setTimestamp(DateFormatUtils.formatYMdHms(new Date()));
		} catch (ParseException e1) {
			logger.error(e1.getMessage(),e1);
		}
		VenderVbinfoGetBasicVenderInfoByVenderIdResponse shopGetResponse = null;
		ShopInfoResultTO shopInfoModel=new ShopInfoResultTO();
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				shopGetResponse = getClient(sessionKey).execute(req);
				if (shopGetResponse != null && shopGetResponse.getCode() != null) {
					String code = shopGetResponse.getCode();
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						logger.info("many time get shopInfo failure！！！");
					}
					if(!code.equals("0")) {
						ApiInvokException ae = new ApiInvokException(shopGetResponse.getCode(), shopGetResponse.getZhDesc());
						throw ae;
					}
				}
				if (shopGetResponse == null) {
					ApiInvokException ae = new ApiInvokException("-", "获取京东店铺response为空");
					throw ae;
				}
				VenderBasicResult shopInfoResult=shopGetResponse.getGetbasicvenderinfobyvenderidResult();
				shopInfoModel=new ShopInfoResultTO(shopInfoResult);
				break;
			} catch (Exception e) {
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					ApiInvokException ae = new ApiInvokException("popvc.errorcode.vender.error", "获取京东店铺信息失败");
					ae.setSubErrCode("-");
					ae.setSubErrMsg("通过京东接口获取店铺信息失败");
					throw ae;
				}
			}
		}

		return shopInfoModel;
	}


	public void checkShopExpireBySessionKey(String sessionKey) throws Exception {

		VenderInfoQueryByPinRequest req = new VenderInfoQueryByPinRequest();
		VenderInfoQueryByPinResponse shopGetResponse = null;
		try {
			shopGetResponse = getClient(sessionKey).execute(req);
			if (shopGetResponse != null && StringUtils.isNotBlank(shopGetResponse.getCode())) {
				String code = shopGetResponse.getCode();
				//logger.info("~~~~~~~~~~~~~getShopInfoByPin return information:"+shopGetResponse.getMsg());
				if("0".equals(code)) {
					VenderInfoResult shopInfoResult=shopGetResponse.getVenderInfoResult();
					if(shopInfoResult!=null){
						return ;
					}

				}else{
					GainShopDataFailException dataFailException = new GainShopDataFailException("店铺信息获取失败");
					dataFailException.setErrorMsg(shopGetResponse.getMsg());
					dataFailException.setErrorCode(shopGetResponse.getCode());
					throw dataFailException;
				}
			}
			if (shopGetResponse == null) {
				GainShopDataFailException dataFailException = new GainShopDataFailException("获取京东店铺response为空");
				dataFailException.setErrorMsg("获取京东店铺response为空");
				dataFailException.setErrorCode("response is empty");
				throw dataFailException;
			}
		} catch (GainShopDataFailException e) {
			//logger.error("checkShopExpireBySessionKey error:{}",e.getMessage(),e);
			throw e;
		} catch (Exception e) {
			//logger.error("checkShopExpireBySessionKey error:{}",e.getMessage(),e);
			if (!(e.getCause() instanceof SocketTimeoutException)) {
				throw e;
			}
		}
	}

}

