package com.pes.jd.data.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.request.im.ComJdDdOpenGwApiDataServiceRequest;
//import com.jd.open.api.sdk.request.messagePush.ComJdDdOpenGwApiGrantServiceRequest;
//import com.jd.open.api.sdk.request.seller.VenderVbinfoFindVenderCodeListByVenderIdRequest;
import com.jd.open.api.sdk.response.im.ComJdDdOpenGwApiDataServiceResponse;
//import com.jd.open.api.sdk.response.messagePush.ComJdDdOpenGwApiGrantServiceResponse;
//import com.jd.open.api.sdk.response.seller.VenderVbinfoFindVenderCodeListByVenderIdResponse;
import com.pes.jd.constants.CommonConstants;
import com.pes.jd.exception.GainShopDataFailException;
import com.pes.jd.model.DO.ShopAccount;
import com.pes.jd.model.TO.SubUserGetTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.net.SocketTimeoutException;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service("selfCsOperator")
public class SelfCsOperator extends BaseOperator {
	private static  Logger logger = LoggerFactory.getLogger(SelfCsOperator.class);


//public SubUserGetTO getVenderSubUsers(Long venderId, Long shopId, String sessionKey) throws Exception{

//	VenderChildAccountQueryRequest req = new VenderChildAccountQueryRequest();
//	int num = 1;
//	int retryNum = 0;
//	int pageSize=20;
//	VenderChildAccountQueryResponse response = null;
//	Set<ShopAccount> custSubUsers = Sets.newHashSet();
//	List<VenderAccountContent> shopAccount=Lists.newArrayList();
//	JdClient clinet = getClient(sessionKey);
//	boolean hasNext = false;
//	req.setSize(pageSize);
//	int page = 0;
//	do {
//		page++;
//		num++;
//		req.setPage(page);
//		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
//			try {
//				response = clinet.execute(req);
//				retryNum++;
//				if (response != null && response.getCode().equals("0")) {
//					break;
//				} else {
//					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//						logger.info("子账号获取失败::  response.getMsg=" + response.getMsg() + " ;errorCode="
//								+ response.getCode());
//						GainShopDataFailException dataFailException = new GainShopDataFailException(response.getMsg());
//						dataFailException.setErrorMsg(response.getMsg());
//						dataFailException.setErrorCode(response.getCode());
//						throw dataFailException;
//					} else {
//						Thread.sleep(2000);
//					}
//				}
//			} catch (GainShopDataFailException e) {
//				logger.error(e.getMessage(),e);
//				throw e;
//			} catch (Exception e) {
//				if (!(e.getCause() instanceof SocketTimeoutException)) {
//					throw e;
//				}
//				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//					e.printStackTrace();
//					throw e;
//				}
//				logger.error(e.getMessage(),e);
//			}
//		}
//		
//		VenderAccountResult account=	response.getChildAccountResult();
//			if(account!=null){
//				//logger.info("account num:{}",account.getTotalCount());
//				List<VenderAccountContent> subUsers = account.getChildAccountS();
//				if(CollectionUtils.isNotEmpty(subUsers)){
//					shopAccount.addAll(subUsers);
//					if(shopAccount.size()>=account.getTotalCount()){
//						logger.info("已经拉完，当前page:{}", page);
//						hasNext = false;
//					} else {
//					//	logger.info("继续拉取下一页，当前page:{},当前拉取子账号num:{},当前拉去总数 size：{} totalCount num:{}", page,subUsers.size(),shopAccount.size(),account.getTotalCount());
//						hasNext = true;
//					}
//				}else {
//					hasNext = false;
//				}
//		} else {
//			hasNext = false;
//		}
//	} while (hasNext);
//	for (VenderAccountContent av : shopAccount) {
//		//logger.info("accountId:{},accountName：{},userName：{}",av.getAccountId(),av.getAccountName(),av.getUserName());
//		ShopAccount cstUser = new ShopAccount();
//		if(StringUtils.isNotBlank(av.getAccountName())){
//			cstUser.setNick(av.getAccountName().toLowerCase());
//		}
//		cstUser.setRole(CommonConstants.SUB_USER_TYPE_NO);
//		cstUser.setIsAccount(false);
//		cstUser.setShopId(shopId);
//		cstUser.setSellerId(venderId);
//		cstUser.setStatus(av.getStatus());// 员工状态.启用(1）,停用(0)
//		cstUser.setUserName(av.getUserName());
//		cstUser.setCreated(new Date());
//		custSubUsers.add(cstUser);
//	}
//	
//	logger.info("invoke JD api get subuser num:{}" , custSubUsers.size());
//	List<ShopAccount> shopSubUserLst=Lists.newArrayList();
//	if(CollectionUtils.isNotEmpty(custSubUsers)){
//		Set<String> csNicksSet=Sets.newHashSet();
//		shopSubUserLst=custSubUsers.stream().filter(// 过滤去重,避免存在重复的客服
//	               v -> {
//	                   boolean flag = !csNicksSet.contains(v.getNick());
//	                   csNicksSet.add(v.getNick());
//	                   return flag;
//	               }
//	       ).collect(Collectors.toList());
//	}
//	logger.info("remove resubuser get subuser num:{}" , shopSubUserLst.size());
//	return new SubUserGetTO(shopSubUserLst, num, retryNum - num);
//}



//public List<String> getCodeList(String sessionKey) throws Exception{
//	VenderVbinfoFindVenderCodeListByVenderIdRequest request=new VenderVbinfoFindVenderCodeListByVenderIdRequest();
//	JdClient clinet = getClient(sessionKey);
//    VenderVbinfoFindVenderCodeListByVenderIdResponse response= null;
//    for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
//        try {
//            response = clinet.execute(request);
//            if (response != null && response.getCode().equals("0") && null!=response.getVenderCodeList()) {
//                break;
//            } else {
//                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//                    logger.info("getCodeList error_code:{}, error_msg:{}", response.getCode(), response.getMsg());
//                    GainShopDataFailException dataFailException = new GainShopDataFailException("自营拉取供应商编码失败");
//                    dataFailException.setErrorMsg(response.getMsg());
//                    dataFailException.setErrorCode(response.getCode());
//                    throw dataFailException;
//                } else {
//                    Thread.sleep(200);
//                    continue;
//                }
//            }
//        } catch (GainShopDataFailException e) {
//            throw e;
//        } catch (Exception e) {
//            if (!(e.getCause() instanceof SocketTimeoutException)) {
//                throw e;
//            }
//            if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//                logger.error(e.getMessage(), e);
//                throw e;
//            }
//        }
//    }
//	return response.getVenderCodeList();
//}





//public String  getToken(String sessionKey) throws Exception {
//	ComJdDdOpenGwApiGrantServiceRequest request=new ComJdDdOpenGwApiGrantServiceRequest();
//	request.setAspid("110.300.0000001.02");
//	request.setSecret("64d162384e30bc7d0843c14222fc4a2d");
//	request.setVersion("4.0");
//
//	JdClient clinet =getClient(sessionKey);
//	ComJdDdOpenGwApiGrantServiceResponse response =null;
//
//    for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
//        try {
//            response = clinet.execute(request);
//            if (response != null && response.getCode().equals("0") && null!=response.getAccessSignatureResult()&&null!=
//            		response.getAccessSignatureResult().getAccessToken()) {
//                break;
//            } else {
//                if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//                    logger.info("!=null error_code:{}, error_msg:{}", response.getCode(), response.getMsg());
//                    GainShopDataFailException dataFailException = new GainShopDataFailException("自营拉取供应商获取token失败");
//                    dataFailException.setErrorMsg(response.getMsg());
//                    dataFailException.setErrorCode(response.getCode());
//                    throw dataFailException;
//                } else {
//                    Thread.sleep(200);
//                    continue;
//                }
//            }
//        } catch (GainShopDataFailException e) {
//        	logger.info("自营客获取子账户失败",e);
////            throw e;
//            return null;
//        } catch (Exception e) {
//            if (!(e.getCause() instanceof SocketTimeoutException)) {
////                throw e;
//                logger.info("自营客获取子账户失败",e);
//                return null;
//            }
//            if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
//                logger.error(e.getMessage(), e);
//                return null;
//            }
//        }
//    }
//	return response.getAccessSignatureResult().getAccessToken();
//
//}


public SubUserGetTO getVenderSubUsersSelf(Long venderId, Long shopId, String sessionKey,String token,String mallMark ) throws Exception{

	ComJdDdOpenGwApiDataServiceRequest req = new ComJdDdOpenGwApiDataServiceRequest();
	req.setAccessid(System.currentTimeMillis() + "");
	req.setAspid("110.300.0000001.02");
	req.setAccessToken(token);
	req.setVersion("4.0");
	req.setBizTimestamp(System.currentTimeMillis());
	req.setAppid("jd.mayijixiao");
	req.setReqType("getWaiterListByMallMark");
	
	Set<ShopAccount> custSubUsers = Sets.newHashSet();
	
	int pageSize = 500;
	
	JSONObject paramObject = new JSONObject();
//	req.setJsonParam("{'mallMark':'wrzg','page':'1','pageSize':'500'}"); 
	paramObject.put("pageSize", pageSize+"");
	paramObject.put("mallMark", mallMark);
	
	int num = 1;
	int retryNum = 0;
	
	
	ComJdDdOpenGwApiDataServiceResponse response = null;
	JdClient clinet = getClient(sessionKey);
	boolean hasNext = false;
	
	int page = 0;
	do {
		page++;
		num++;
		for (int recallApiTimes = 0; recallApiTimes < CommonConstants.RECALLAPI_TIMES; recallApiTimes++) {
			try {
				paramObject.put("page", page+"");
				
				
				req.setJsonParam(JSONObject.toJSONString(paramObject));
				response = clinet.execute(req);
				
				retryNum++;
				if (response != null && response.getCode().equals("0")) {
					break;
				} else {
					if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
						logger.info("自营根据子供应商编码获取店铺客服失败::  response.getMsg=" + response.getMsg() + " ;errorCode="
								+ response.getCode());
						GainShopDataFailException dataFailException = new GainShopDataFailException(response.getMsg());
						dataFailException.setErrorMsg(response.getMsg());
						dataFailException.setErrorCode(response.getCode());
						throw dataFailException;
					} else {
						Thread.sleep(200);
					}
				}
			} catch (GainShopDataFailException e) {
				logger.error(e.getMessage(),e);
				throw e;
			} catch (Exception e) {
				if (!(e.getCause() instanceof SocketTimeoutException)) {
					throw e;
				}
				if (recallApiTimes == CommonConstants.RECALLAPI_TIMES - 1) {
					e.printStackTrace();
					throw e;
				}
				logger.error(e.getMessage(),e);
			}
		}
		
		String jsonData = response.getReturnType().getJsonData();
		
		if(StringUtils.isNotBlank(jsonData)) {
			JSONObject jobj = JSONObject.parseObject(jsonData);
			JSONArray jarr = jobj.getJSONArray("waiterList");
			int totalPage = 1;
			if(null!=jobj.getInteger("page")) {
				totalPage=jobj.getInteger("page").intValue();
			}
			
			splicingObj(custSubUsers,jarr,shopId,venderId);
			
			if(null==jarr || jarr.size()==0 || page >= totalPage) {
				hasNext = false;
			}else {
				hasNext = true;
			}
		}else {
			hasNext = false;
		}
		
	} while (hasNext);
	
	logger.info("invoke JD api get subuserdrlg num:{}" , custSubUsers.size());
	List<ShopAccount> shopSubUserLst=Lists.newArrayList();
	if(CollectionUtils.isNotEmpty(custSubUsers)){
		Set<String> csNicksSet=Sets.newHashSet();
		shopSubUserLst=custSubUsers.stream().filter(// 过滤去重,避免存在重复的客服
	               v -> {
	                   boolean flag = !csNicksSet.contains(v.getNick());
	                   csNicksSet.add(v.getNick());
	                   return flag;
	               }
	       ).collect(Collectors.toList());
	}
	logger.info("remove resubuser get subuser num:{}" , shopSubUserLst.size());
	return new SubUserGetTO(shopSubUserLst, num, retryNum - num);
}


private void splicingObj(Set<ShopAccount> custSubUsers,JSONArray jarr, Long shopId, Long venderId) {
	
	if(null==jarr) {
		return ;
	}
	
	for(Object obj :jarr) {
		JSONObject jObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
		if(StringUtils.isNotBlank(jObject.getString("pin"))) {
			ShopAccount shopAccount = new ShopAccount();
			shopAccount.setRole(CommonConstants.SUB_USER_TYPE_NO);
			shopAccount.setIsAccount(false);
			shopAccount.setShopId(shopId);
			shopAccount.setSellerId(venderId);
			shopAccount.setStatus(CommonConstants.STATUS_1);// 员工状态.启用(1）,停用(0)
			shopAccount.setUserName(jObject.getString("pin"));
			shopAccount.setNick(jObject.getString("pin"));
			shopAccount.setCreated(new Date());
			shopAccount.setSource(1);
			custSubUsers.add(shopAccount);
		}
	}
}


}
