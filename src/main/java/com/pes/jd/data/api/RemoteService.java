package com.pes.jd.data.api;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.pes.jd.business.KnowledgVolcengine;
import com.pes.jd.config.RedisOperator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class RemoteService {

    @javax.annotation.Resource
    private RedisOperator redisOperator;

    private static final Logger logger = LoggerFactory.getLogger(RemoteService.class);
    private static String systemPromptTemplate;
    private static String chatContext;

    private static final List<String> CATEGORY_LIST = Arrays.asList(
            "尺码推荐", "商品信息咨询", "物流方式", "换货问题", "问候类", "商品推荐",
            "商品价格咨询", "催退款", "下单问题", "订单咨询", "退货规则咨询", "催发货", "售后咨询",
            "优惠咨询", "商品品质问题", "运费险问题", "退款流程咨询", "退差价", "赠品问题",
            "快递问题", "补贴", "国补", "优惠咨询", "补差价", "转人工", "以旧换新", "安装方式", "返现", "其他"
    );

    static {
        try {
            systemPromptTemplate = StreamUtils.copyToString(
                    new ClassPathResource("system_prompt2.txt").getInputStream(),
                    StandardCharsets.UTF_8
            );
            chatContext = StreamUtils.copyToString(
                    new ClassPathResource("chatContext.txt").getInputStream(),
                    StandardCharsets.UTF_8
            );
        } catch (IOException e) {
            logger.error("Failed to load system_prompt.txt", e);
        }
    }

    private static String apiKey = "20bddd13-d761-4b03-b3f3-56fa1248d4fd";
    private static String endPointId = "ep-20250528112022-ksds9";
    private static String contextIdKey = "ai-cache-context-id";
    private final Object lock = new Object();


    @Autowired
    private KnowledgVolcengine knowledgVolcengine;


    public String callChatCompletions(String categoryList, String chatText) {
        String url = "https://ark.cn-beijing.volces.com/api/v3/chat/completions";
        HashMap<String, String> formatMap = new HashMap<>();
        formatMap.put("category_list",categoryList);
        formatMap.put("chat_text",chatText);
        String systemPrompt = StrUtil.format(systemPromptTemplate, formatMap);

        JSONObject requestBody = JSONUtil.createObj()
                .set("model", "doubao-1-5-pro-32k-250115")
                .set("max_tokens",10000)
                .set("temperature",0.7)
                .set("messages", JSONUtil.createArray()
                        .put(JSONUtil.createObj()
                                .set("role", "system")
                                .set("content", systemPrompt))
                        .put(JSONUtil.createObj()
                                .set("role", "user")
                                .set("content", "请对聊天记录进行分类")));

        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + apiKey)
                .body(requestBody.toString())
                .execute();

        if (response.isOk()) {
           return response.body();
        }
        return null;
    }

    /**
     * 前缀缓存对话
     */
    public  String callContextChatCompletions(String categoryList, String chatText) {

        String contextId = "";
        String url = "https://ark.cn-beijing.volces.com/api/v3/context/chat/completions";
        HashMap<String, String> formatMap = new HashMap<>();
        formatMap.put("category_list",categoryList);
        formatMap.put("chat_text",chatText);
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        String userMessage = gson.toJson(formatMap);
        userMessage = "下面分别是预定义标签与聊天记录 :\n"+userMessage+"\n请严格按照格式要求输出分类结果";

        contextId =(String) redisOperator.get(contextIdKey);
        if(StrUtil.isBlank(contextId)){
            synchronized (lock) {
                // 再次检查，防止其他线程已经创建
                contextId = (String) redisOperator.get(contextIdKey);
                if (StrUtil.isBlank(contextId)) {
                    logger.warn("前缀缓存id已经过期");
                    contextId = contextCreate();
                }
            }
        }
        JSONObject requestBody = JSONUtil.createObj()
                .set("model", endPointId)
                .set("max_tokens",1024)
                .set("context_id", contextId)
                .set("messages", JSONUtil.createArray()
                        .put(JSONUtil.createObj()
                                .set("role", "user")
                                .set("content", userMessage)));
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + apiKey)
                .body(requestBody.toString())
                .execute();

        if (response.isOk()) {
//            String aiResponse = response.body();
//            com.alibaba.fastjson.JSONObject jsonResponse = com.alibaba.fastjson.JSONObject.parseObject(aiResponse);
//            String responseText = jsonResponse.getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
//            System.out.println(responseText);
            return response.body();
        }
        logger.error("这是不ok的,ai返回回来的结果:{}",response);
        return "hahaAi查询错误";
    }

    public static void main(String[] args) {
        String categoryList = String.join(",", CATEGORY_LIST);
        String chatText = chatContext;
//        String contextId = contextCreate();
//        System.out.println(contextId);
//        String s = callContextChatCompletions(categoryList, chatText, contextId);
     //   String s = callContextChatCompletions(categoryList, chatText);

    }



    /**
     * 创建前缀缓存
     */
    public String contextCreate() {
        String url = "https://ark.cn-beijing.volces.com/api/v3/context/create";
        String apiKey = "29a21cd4-a29b-4aab-8e18-685ddb646239";
        String systemPrompt = systemPromptTemplate;

        JSONObject requestBody = JSONUtil.createObj()
                .set("model", endPointId)
                .set("max_tokens",4096)
                .set("temperature",0.7)
                .set("ttl",3600)
                .set("mode","common_prefix")
                .set("messages", JSONUtil.createArray()
                        .put(JSONUtil.createObj()
                                .set("role", "system")
                                .set("content", systemPrompt)));

        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .header("Authorization", "Bearer " + apiKey)
                .body(requestBody.toString())
                .execute();
        if (response.isOk()) {
            String contextId = JSONUtil.parseObj(response.body()).getStr("id");
            redisOperator.setForTimeMIN(contextIdKey,contextId,55);
            logger.info("已设置好前缀缓存Id");
            return contextId;
        }
        logger.error("这是不ok的,创建前缀缓存返回的结果:{}",response);
        return "haha前缀错误";
    }








    /**
     * Search for a document in the knowledge base by collection name and document name
     *
     * @param collectionName Name of the knowledge base collection
     * @param docName Name of the document to search for
     * @return Map containing the search results
     */
    public Map<String, Object> searchDocByName(String collectionName, String docName, String key, String value) {
        String docId = knowledgVolcengine.findDocIdByName(collectionName, docName, null);
        if(StrUtil.isBlank(docId)){
            logger.error("Document '{doc_name}' not found in collection ",docName);
            return null;
        }
        String query = new JSONObject().put("key", key).put("value", value).toString();
        Map<String, Object> searchResult = knowledgVolcengine.searchKnowledge(collectionName, query, docId, 0.7, 1, null);

        if ((int)searchResult.getOrDefault("code", 0) != 0 || !searchResult.containsKey("data")) {
            logger.warn("Search failed for document '{}'", docName);
            Map<String, Object> response = new HashMap<>();
            response.put("results", new ArrayList<>());
            response.put("message", "Search failed");
            return response;
        }

        Map<String, Object> data = (Map<String, Object>) searchResult.get("data");
        List<Map<String, Object>> resultList = (List<Map<String, Object>>) data.getOrDefault("result_list", new ArrayList<>());

        // 过滤结果
        List<Map<String, Object>> filteredResults = new ArrayList<>();

        for (Map<String, Object> result : resultList) {
            double score = ((Number) result.getOrDefault("score", 0)).doubleValue();
            filteredResults.add(result);
        }

        logger.debug("Found {} results with score > 0.5", filteredResults.size());

        // 创建响应
        Map<String, Object> response = new HashMap<>();
        response.put("doc_id", docId);
        response.put("results", filteredResults);
        response.put("message", "success");
        response.put("total_results", filteredResults.size());
        return response;
    }


    private static JSONObject buildRequestBody(String projectName,String collectName,String query) {
        // 使用JSONUtil创建JSON对象，链式调用更简洁
        return JSONUtil.createObj()
                .set("project", projectName)
                .set("name", collectName)
                .set("query", query)
                .set("limit", 10)
                .set("pre_processing", JSONUtil.createObj()
                        .set("need_instruction", true)
                        .set("return_token_usage", true)
                        .set("messages", JSONUtil.createArray()
                                .put(JSONUtil.createObj()
                                        .set("role", "system")
                                        .set("content", ""))
                                .put(JSONUtil.createObj()
                                        .set("role", "user"))))
                .set("dense_weight", 1)
                .set("post_processing", JSONUtil.createObj()
                        .set("get_attachment_link", true)
                        .set("rerank_only_chunk", false)
                        .set("rerank_switch", false));
    }




    /**
     * 根据集合名称和文档名称获取文档ID
     *
     * @param collectionName 集合名称
     * @param docName 文档名称
     * @return 文档ID，如果未找到则返回null
     */
    private String listDocs(String collectionName, String docName) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("collection_name", collectionName);
            params.put("doc_name", docName);

            // 发送请求获取文档列表
            String url = "http://your-api-host/api/knowledge/list_docs"; // 替换为实际的API地址
            String response = HttpUtil.post(url, JSONUtil.toJsonStr(params));

            // 解析响应
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            if (jsonResponse.getInt("code", -1) != 0) {
                logger.error("Failed to list docs: {}", jsonResponse.getStr("message"));
                return null;
            }

            JSONObject data = jsonResponse.getJSONObject("data");
            if (data == null || !data.containsKey("doc_list")) {
                return null;
            }

            JSONArray docList = data.getJSONArray("doc_list");
            if (docList.isEmpty()) {
                return null;
            }

            // 找到匹配文档名称的文档ID
            for (int i = 0; i < docList.size(); i++) {
                JSONObject doc = docList.getJSONObject(i);
                if (docName.equals(doc.getStr("doc_name"))) {
                    return doc.getStr("doc_id");
                }
            }

            return null;
        } catch (Exception e) {
            logger.error("Error listing docs: {}", e.getMessage(), e);
            return null;
        }
    }


    /**
     * Add a point to a document in the knowledge base
     *
     * @param collectionName Name of the knowledge base collection
     * @param docId ID of the document to add a point to
     * @param key Key for the structured data point
     * @param value Value for the structured data point
     * @param projectName Optional project name (defaults to "default" if null)
     * @return Map containing the API response
     * @throws IllegalArgumentException if required parameters are missing
     * @throws RuntimeException if API call fails
     */
    public Map<String, Object> addPointToDoc(String collectionName, String docId, String key, String value, String projectName) {

        Map<String, Object> stringObjectMap = knowledgVolcengine.addPoint(collectionName, docId, key, value, projectName);
        return stringObjectMap;
    }



}
