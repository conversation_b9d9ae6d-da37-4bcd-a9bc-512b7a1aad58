package com.pes.jd.data.api;

import com.jd.open.api.sdk.JdClient;
import com.pes.jd.util.ApiClientUtil;
import com.pes.jd.util.DateUtil;
import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.map.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class BaseOperator {

	public JdClient getClient(String sessionKey) {
		return ApiClientUtil.getClient(sessionKey);
	}
	// 当日期间隔大于指定的间隔的时候，修改开始查询时间
	public Date setStartTime(Date startDate, int num) {

		Date newDate = new Date();
		long days = (newDate.getTime() - startDate.getTime()) / 86400000;
		if (days > num) {
			startDate = DateUtil.getDateByPeriod(newDate, 0 - num);
		}

		return startDate;
	}

	protected static String map2Json(Map<String, Object> map) {
		String json = "";
		try {
			ObjectMapper mapper = new ObjectMapper();
			json = mapper.writeValueAsString(map);
		} catch (JsonParseException e) {
			e.printStackTrace();
		} catch (JsonMappingException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return json;
	}

	// 将日期按小于等于7天进行划分成多个时间段,奇数下标为结束时间，偶数下标为开始时间
	public static List<Date> splitDate(Date startDate, Date endDate) {

		if (startDate == null || endDate == null || endDate.getTime() < startDate.getTime()) {
			return null;
		}

		List<Date> dateList = new ArrayList<Date>(0);

		Date start = startDate;
		Date end = DateUtil.getDateByPeriod(start, 6);
		end = DateUtil.getEndTimeOfDate(end);
		while (end.getTime() < endDate.getTime()) {
			dateList.add(start);
			dateList.add(end);
			start = DateUtil.getDateByPeriod(end, 1);
			start = DateUtil.getStartTimeOfDate(start);
			end = DateUtil.getDateByPeriod(start, 6);
			end = DateUtil.getEndTimeOfDate(end);
		}

		if (start.getTime() < endDate.getTime()) {
			dateList.add(start);
			dateList.add(endDate);
		}
		return dateList;
	}

}
