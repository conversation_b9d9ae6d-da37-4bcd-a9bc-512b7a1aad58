package com.pes.jd.data.convert;

import com.jd.open.api.sdk.response.order.VenderRemark;
import com.pes.jd.data.api.OrderReMarkOperator;
import com.pes.jd.model.TO.OrderRemarkTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OrderReMarkConverter {
    private static final Logger logger = LoggerFactory.getLogger(OrderReMarkConverter.class);

    @Resource
    private OrderReMarkOperator orderReMarkOperator;




    public VenderRemark pullOrderConvert(String sessionkey, Long orderId) {
        VenderRemark venderRemark = null;
        try {
            OrderRemarkTO orderRemarkTO = orderReMarkOperator.getOrderRemark(orderId, sessionkey);
            venderRemark = orderRemarkTO.getOrderVenderRemarkQueryByOrderIdResponse().getVenderRemarkQueryResult().getVenderRemark();
        } catch (Exception e) {
            logger.error("pullOrderConvert==================调用异常" + e.getMessage());
        }
        return venderRemark;
    }






}
  
