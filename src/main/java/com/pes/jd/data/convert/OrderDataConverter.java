package com.pes.jd.data.convert;

import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderInfoFBP;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.get.OrderSearchInfo;
import com.pes.jd.data.api.OrderOperator;
import com.pes.jd.model.Param.ShopBaseDataParam;
import com.pes.jd.model.TO.OrderResultTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
public class OrderDataConverter {
    @Resource
    private OrderOperator orderOperator;

    public OrderResultTO getOrderInfo(ShopBaseDataParam shopCommomDTO, Long orderId) throws Exception {
        OrderResultTO resultTo = new OrderResultTO();
        AtomicBoolean encryptFlag = new AtomicBoolean(true);
        if (shopCommomDTO.getColType() == 0) {
            OrderSearchInfo order = orderOperator.getPopOrderInfo(shopCommomDTO, orderId, encryptFlag);
            if (order != null) {
                resultTo.setPopOrder(order);
                resultTo.setEncryptFlag(encryptFlag.get());
            }
        } else {
            OrderInfoFBP order = orderOperator.getFbpOrderInfo(shopCommomDTO, orderId, encryptFlag);
            if (order != null) {
                resultTo.setFbpOrder(order);
                resultTo.setEncryptFlag(encryptFlag.get());
            }
        }

        return resultTo;
    }


}
