package com.pes.jd.manage;

import com.pes.jd.model.DTO.CsChatlogDTO;
import com.pes.jd.model.Query.JobShopQuery;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface CacheManage {


    public void putAllShopCsChatLogLst(JobShopQuery jobShop, String csNick, Date startDate, Date endDate, List<CsChatlogDTO> chatlogLst);

    public Map<String, List<CsChatlogDTO>> searchShopCsChatLogLst(JobShopQuery jobShop, String csNick, Date startDate, Date endDate);

    public List<CsChatlogDTO> searchShopCsAndCustomerChatLogLst(JobShopQuer<PERSON> jobShop, String csNick, String customer, Date startDate, Date endDate);

}
