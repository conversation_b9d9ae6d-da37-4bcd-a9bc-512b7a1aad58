package com.pes.jd;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**  
 * ClassName:StartupRunnerOrder2 <br/>  
 * Function: 启动SpringBoot会执行 @Order标识执行顺序 值越小越先执行 <br/>  
 * Date:     2018年10月17日 下午3:50:36 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@Component
@Order(0)
public class StartupRunnerOrder2 implements CommandLineRunner {
	private Logger logger = LoggerFactory.getLogger(StartupRunnerOrder2.class);

	@Override
	public void run(String... args) throws Exception {
		logger.info(">>服务启动执行，执行加载数据等操作0<<");
	}

}
