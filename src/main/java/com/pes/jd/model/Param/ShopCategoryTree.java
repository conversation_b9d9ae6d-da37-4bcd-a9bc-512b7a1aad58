package com.pes.jd.model.Param;


import java.util.List;

public class ShopCategoryTree {

	private Long categoryId;
	
	private Long parentId;
	
	private String name;

	private Long level;
	private List<ShopCategoryTree> childrens;

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public List<ShopCategoryTree> getChildrens() {
		return childrens;
	}

	public void setChildrens(List<ShopCategoryTree> childrens) {
		this.childrens = childrens;
	}

	public Long getLevel() {
		return level;
	}

	public void setLevel(Long level) {
		this.level = level;
	}

}
