package com.pes.jd.model.Param;

import java.util.List;

public class CustomReportParam {


    /**
     * id : 1
     * shopId : 12315
     * name : 新的报表
     * type : 1
     * desc :
     * customReportPropertyLst : [{"propertyId":1,"filterFlag":1,"filter":[{"id":1,"property":"refund_amount","isFilter":true},{"id":2,"property":"post_fee","isFilter":true}]},{"propertyId":2,"filterFlag":0}]
     */

    private Integer id;
    private Long shopId;
    private String name;
    private Integer type;
    private String desc;
    private Integer status;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    private List<CustomReportPropertyLstBean> customReportPropertyLst;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public List<CustomReportPropertyLstBean> getCustomReportPropertyLst() {
        return customReportPropertyLst;
    }

    public void setCustomReportPropertyLst(List<CustomReportPropertyLstBean> customReportPropertyLst) {
        this.customReportPropertyLst = customReportPropertyLst;
    }

    public static class CustomReportPropertyLstBean {
        /**
         * propertyId : 1
         * filterFlag : 1
         * filter : [{"id":1,"property":"refund_amount","isFilter":true},{"id":2,"property":"post_fee","isFilter":true}]
         */

        private Integer propertyId;
        private Integer filterFlag;
        private List<FilterBean> filter;

        public Integer getPropertyId() {
            return propertyId;
        }

        public void setPropertyId(Integer propertyId) {
            this.propertyId = propertyId;
        }

        public Integer getFilterFlag() {
            return filterFlag;
        }

        public void setFilterFlag(Integer filterFlag) {
            this.filterFlag = filterFlag;
        }

        public List<FilterBean> getFilter() {
            return filter;
        }

        public void setFilter(List<FilterBean> filter) {
            this.filter = filter;
        }

        public static class FilterBean {
            /**
             * id : 1
             * property : refund_amount
             * isFilter : true
             */

            private Integer id;
            private String property;
            private Boolean isFilter;

            public Integer getId() {
                return id;
            }

            public void setId(Integer id) {
                this.id = id;
            }

            public String getProperty() {
                return property;
            }

            public void setProperty(String property) {
                this.property = property;
            }

            public Boolean isIsFilter() {
                return isFilter;
            }

            public void setIsFilter(Boolean isFilter) {
                this.isFilter = isFilter;
            }
        }
    }
}
