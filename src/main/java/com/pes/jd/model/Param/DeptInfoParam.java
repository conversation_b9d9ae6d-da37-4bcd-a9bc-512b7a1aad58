package com.pes.jd.model.Param;

import java.io.Serializable;

public class DeptInfoParam implements Serializable{

    /**
     * 类型（0：管理员，1：一级部门，2：二级部门，3：三级部门，4：四级部门）
     */
    private String type;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 事业群名称
     */
    private String deptName00;

    /**
     * 一级表名称
     */
    private String deptName01;

    /**
     * 二级表名称
     */
    private String deptName02;

    /**
     * 三级表名称
     */
    private String deptName03;

    /**
     * 运营经理账号
     */
    private String erpId;

    /**
     * 运营经理名称
     */
    private String erpName;

    public String getDeptName00() {
        return deptName00;
    }

    public void setDeptName00(String deptName00) {
        this.deptName00 = deptName00;
    }

    public String getErpName() {
        return erpName;
    }

    public void setErpName(String erpName) {
        this.erpName = erpName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDeptName01() {
        return deptName01;
    }

    public void setDeptName01(String deptName01) {
        this.deptName01 = deptName01;
    }

    public String getDeptName02() {
        return deptName02;
    }

    public void setDeptName02(String deptName02) {
        this.deptName02 = deptName02;
    }

    public String getDeptName03() {
        return deptName03;
    }

    public void setDeptName03(String deptName03) {
        this.deptName03 = deptName03;
    }

    public String getErpId() {
        return erpId;
    }

    public void setErpId(String erpId) {
        this.erpId = erpId;
    }

    @Override
    public String toString() {
        return "DeptInfoParam{" +
                "type='" + type + '\'' +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", deptName01='" + deptName01 + '\'' +
                ", deptName02='" + deptName02 + '\'' +
                ", deptName03='" + deptName03 + '\'' +
                ", erpId='" + erpId + '\'' +
                '}';
    }
}
