package com.pes.jd.model.Param;

import com.pes.jd.model.DTO.CsDTO;

import java.util.Date;
import java.util.List;

public class GoodsConsultParam {

	private List<String> csNickLst;
	private List<Long> skuLst;
	private Date startDate;
	private Date endDate;
	private Integer result;
	private String customer;
	private Long skuId;
	private Long categoryId;
	private String csNick;

	private String groupId;
	private String orderId;
    private Byte dimension;
    private List<CsDTO> csLst;

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public Integer getResult() {
		return result;
	}
	public void setResult(Integer result) {
		this.result = result;
	}
	public String getCustomer() {
		return customer;
	}
	public void setCustomer(String customer) {
		this.customer = customer;
	}
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public List<String> getCsNickLst() {
		return csNickLst;
	}
	public void setCsNickLst(List<String> csNickLst) {
		this.csNickLst = csNickLst;
	}
	public List<Long> getSkuLst() {
		return skuLst;
	}
	public void setSkuLst(List<Long> skuLst) {
		this.skuLst = skuLst;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public List<CsDTO> getCsLst() {
        return csLst;
    }

    public Byte getDimension() {
        return dimension;
    }

    public void setDimension(Byte dimension) {
        this.dimension = dimension;
    }

    public void setCsLst(List<CsDTO> csLst) {
        this.csLst = csLst;
    }
}
