package com.pes.jd.model.Param;

import java.util.Date;
import java.util.List;

public class AfterOrderParam {
	
	private Date startDate;
	
	private Date endDate;
	
	private Long shopId;
	
	private String schemaId; 
	
	private List<String> buyerLst;
	
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getSchemaId() {
		return schemaId;
	}
	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}
	public List<String> getBuyerLst() {
		return buyerLst;
	}
	public void setBuyerLst(List<String> buyerLst) {
		this.buyerLst = buyerLst;
	}
}
