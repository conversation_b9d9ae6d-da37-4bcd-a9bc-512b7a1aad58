package com.pes.jd.model.Param;

import com.pes.jd.model.Query.UserQuery;

import java.util.List;

/**  
 * ClassName:Demo <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月24日 下午3:43:39 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class LossOrderParam {
	
	private Long shopId;
	private String schemaId;
	private String dbName;
	
	private List<UserQuery> csNickInfo; 
	private String buyerNick; 
	private String orderId; 
	private String orderType;
	private String receiveStatus;
    private Byte dimension;
	public LossOrderParam(){};
	
	public LossOrderParam(List<UserQuery> csNickInfo, String buyerNick, String orderId, String orderType, String receiveStatus){
		this.csNickInfo = csNickInfo;
		this.buyerNick = buyerNick;
		this.orderId = orderId;
		this.orderType = orderType;
		this.receiveStatus = receiveStatus;
	};

	public List<UserQuery> getCsNickInfo() {
		return csNickInfo;
	}
	public void setCsNickInfo(List<UserQuery> csNickInfo) {
		this.csNickInfo = csNickInfo;
	}
	public String getBuyerNick() {
		return buyerNick;
	}
	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getReceiveStatus() {
		return receiveStatus;
	}
	public void setReceiveStatus(String receiveStatus) {
		this.receiveStatus = receiveStatus;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getSchemaId() {
		return schemaId;
	}

	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

    public Byte getDimension() {
        return dimension;
    }

    public void setDimension(Byte dimension) {
        this.dimension = dimension;
    }
}
  
