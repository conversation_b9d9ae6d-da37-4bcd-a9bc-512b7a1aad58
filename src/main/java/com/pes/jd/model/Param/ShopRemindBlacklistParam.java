package com.pes.jd.model.Param;

import lombok.Data;

@Data
public class ShopRemindBlacklistParam {
    private String buyerNick;
    private String telephone;

    @Override
    public boolean equals(Object obj) {
        if (obj == null)
            return false;
        if (this == obj)
            return true;
        ShopRemindBlacklistParam other = (ShopRemindBlacklistParam) obj;
        if (buyerNick == null) {
            if (other.buyerNick != null)
                return false;
        } else if (!buyerNick.equals(other.buyerNick))
            return false;
        if (telephone == null) {
            if (other.telephone != null)
                return false;
        } else if (!telephone.equals(other.telephone))
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return buyerNick.hashCode() * telephone.hashCode();
    }
}
