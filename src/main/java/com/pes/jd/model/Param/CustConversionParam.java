package com.pes.jd.model.Param;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pes.jd.model.DTO.CustomerReserveDTO;
import com.pes.jd.model.DTO.OrderLatelyDTO;
import com.pes.jd.model.DTO.ShopReservePresaleDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown  =true)
public class CustConversionParam extends CommonParam{

	private String type;

	private String conversionStatus;
	
	private String conversionResult;
	
	private String allocateCsStr;
	
	private Long orderId;
	
	private Long conversionId;
	
	private String orderType;
	
	private String remindType;
	
	private Integer taskTotalflag;
	
	private Boolean isRemind;
	
	//二期优化新增参数
	private Set<String> buyerNickSet;
	
	private List<Long> skuLst;
	
	private Integer chatRoundNum;
	
	private Integer sessionDurationTime;
	//开启自动分配
	private Boolean allocateIsRemind;
	
	private String orderIdPool;

	private Boolean isSelectedAll;

	private Integer colType;

	private Boolean urgeLoss;
//remind_type 为2 send_remind_type为1 3的是咚咚跟单
//remind_type 为2 send_remind_type为2 3的是短信跟单
	private String sendType;

	private String smsSendStatus;

	private Boolean isCnoRemind;

    private List<OrderLatelyDTO> orderLst;

    private Byte dimension;//五期优化新增参数

	private String activityId;

	private Integer wareType;

	private Boolean reserveAutoRemind;

	private Boolean reserveBatchRemind;
	private Boolean reserveCnoRemind;

	private Boolean presaleAutoRemind;
	private Boolean presaleBatchRemind;

	private Boolean presaleCnoRemind;
	private Boolean presaleBarginRemind;

	private ShopReservePresaleDTO active;

	private List<CustomerReserveDTO> customerReserveLst;

	private Boolean reserveRemind;

	private Map<String,Object> resMap;

	private String wordId;

	private List<String> skuIdList;

	/**
	 *remind_type 字段为1 send_remind_type 是空 的就是手动跟单
	 *remind_type 为2 send_remind_type为1 3的是咚咚跟单
	 *remind_type 为2 send_remind_type为2 3的是短信跟单
	 * @param followUpType 1手动跟单 2咚咚智能跟单 3短信跟单
	 * @param otherFollowUp 其他跟单做条件  0 没有  1 有
	 */
	public void setRemindTypeAndSendTypeByFollowUpTypeAndSmsFollowUp(String followUpType,String otherFollowUp) {
		switch (followUpType) {
			case "1"://手动跟单
				remindType = "1";
				sendType = null;
				break;
			case "2"://咚咚智能跟单
				if (StrUtil.isEmpty(otherFollowUp)) {
					remindType = "2";
					sendType = "1,3";
				} else {
					if ("0".equals(otherFollowUp)) {//没有短信跟单
						remindType = "2";
						sendType = "1";
					} else if ("1".equals(otherFollowUp)) {//有短信跟单
						remindType = "2";
						sendType = "3";
					}
				}
				break;
			case "3"://短信跟单
				if (StrUtil.isEmpty(otherFollowUp)) {
					remindType = "2";
					sendType = "2,3";
				} else {
					if ("0".equals(otherFollowUp)) {//没有咚咚跟单
						remindType = "2";
						sendType = "2";
					} else if ("1".equals(otherFollowUp)) {//有咚咚跟单
						remindType = "2";
						sendType = "3";
					}
				}
				break;
		}
	}

	private String taskType;

	public Map<String, Object> getResMap() {
		return resMap;
	}

	public void setResMap(Map<String, Object> resMap) {
		this.resMap = resMap;
	}
	public String getType() {
		return type;
	}

	public void setType(String type) {
		if(StringUtils.isNotBlank(type)){
			if(type.contains(",")){
				if(Arrays.stream(type.split(",")).anyMatch(t -> !StringUtils.isNumeric(t))){
					throw new RuntimeException("参数异常...");
				}
			}else{
				if(!StringUtils.isNumeric(type)){
					throw new RuntimeException("参数异常...");
				}
			}
		}
		this.type = type;
	}

	public String getConversionStatus() {
		return conversionStatus;
	}

	public void setConversionStatus(String conversionStatus) {
		this.conversionStatus = conversionStatus;
	}

	public String getConversionResult() {
		return conversionResult;
	}

	public void setConversionResult(String conversionResult) {
		this.conversionResult = conversionResult;
	}

	public String getAllocateCsStr() {
		return allocateCsStr;
	}

	public void setAllocateCsStr(String allocateCsStr) {
		this.allocateCsStr = allocateCsStr;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getConversionId() {
		return conversionId;
	}

	public void setConversionId(Long conversionId) {
		this.conversionId = conversionId;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getRemindType() {
		return remindType;
	}

	public void setRemindType(String remindType) {
		this.remindType = remindType;
	}

	public Integer getTaskTotalflag() {
		return taskTotalflag;
	}

	public void setTaskTotalflag(Integer taskTotalflag) {
		this.taskTotalflag = taskTotalflag;
	}

	public Boolean getRemind() {
		return isRemind;
	}

	public void setRemind(Boolean remind) {
		isRemind = remind;
	}

	public Set<String> getBuyerNickSet() {
		return buyerNickSet;
	}

	public void setBuyerNickSet(Set<String> buyerNickSet) {
		this.buyerNickSet = buyerNickSet;
	}

	public List<Long> getSkuLst() {
		return skuLst;
	}

	public void setSkuLst(List<Long> skuLst) {
		this.skuLst = skuLst;
	}

	public Integer getChatRoundNum() {
		return chatRoundNum;
	}

	public void setChatRoundNum(Integer chatRoundNum) {
		this.chatRoundNum = chatRoundNum;
	}

	public Integer getSessionDurationTime() {
		return sessionDurationTime;
	}

	public void setSessionDurationTime(Integer sessionDurationTime) {
		this.sessionDurationTime = sessionDurationTime;
	}

	public Boolean getAllocateIsRemind() {
		return allocateIsRemind;
	}

	public void setAllocateIsRemind(Boolean allocateIsRemind) {
		this.allocateIsRemind = allocateIsRemind;
	}

	public String getOrderIdPool() {
		return orderIdPool;
	}

	public void setOrderIdPool(String orderIdPool) {
		this.orderIdPool = orderIdPool;
	}

	public Boolean getSelectedAll() {
		return isSelectedAll;
	}

	public void setSelectedAll(Boolean selectedAll) {
		isSelectedAll = selectedAll;
	}

	public Integer getColType() {
		return colType;
	}

	public void setColType(Integer colType) {
		this.colType = colType;
	}

	public Boolean getUrgeLoss() {
		return urgeLoss;
	}

	public void setUrgeLoss(Boolean urgeLoss) {
		this.urgeLoss = urgeLoss;
	}

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		if(StringUtils.isNotBlank(sendType)){
			if(sendType.contains(",")){
				if(Arrays.stream(sendType.split(",")).anyMatch(t -> !StringUtils.isNumeric(t))){
					throw new RuntimeException("参数异常...");
				}
			}else{
				if(!StringUtils.isNumeric(sendType)){
					throw new RuntimeException("参数异常...");
				}
			}
		}
		this.sendType = sendType;
	}

	public String getSmsSendStatus() {
		return smsSendStatus;
	}

	public void setSmsSendStatus(String smsSendStatus) {
		this.smsSendStatus = smsSendStatus;
	}

	public Boolean getCnoRemind() {
		return isCnoRemind;
	}

	public void setCnoRemind(Boolean cnoRemind) {
		isCnoRemind = cnoRemind;
	}

	public List<OrderLatelyDTO> getOrderLst() {
		return orderLst;
	}

	public void setOrderLst(List<OrderLatelyDTO> orderLst) {
		this.orderLst = orderLst;
	}

	public Byte getDimension() {
		return dimension;
	}

	public void setDimension(Byte dimension) {
		this.dimension = dimension;
	}

	public String getActivityId() {
		return activityId;
	}

	public void setActivityId(String activityId) {
		this.activityId = activityId;
	}

	public Integer getWareType() {
		return wareType;
	}

	public void setWareType(Integer wareType) {
		this.wareType = wareType;
	}

	public Boolean getReserveAutoRemind() {
		return reserveAutoRemind;
	}

	public void setReserveAutoRemind(Boolean reserveAutoRemind) {
		this.reserveAutoRemind = reserveAutoRemind;
	}

	public Boolean getReserveBatchRemind() {
		return reserveBatchRemind;
	}

	public void setReserveBatchRemind(Boolean reserveBatchRemind) {
		this.reserveBatchRemind = reserveBatchRemind;
	}

	public Boolean getReserveCnoRemind() {
		return reserveCnoRemind;
	}

	public void setReserveCnoRemind(Boolean reserveCnoRemind) {
		this.reserveCnoRemind = reserveCnoRemind;
	}

	public Boolean getPresaleAutoRemind() {
		return presaleAutoRemind;
	}

	public void setPresaleAutoRemind(Boolean presaleAutoRemind) {
		this.presaleAutoRemind = presaleAutoRemind;
	}

	public Boolean getPresaleBatchRemind() {
		return presaleBatchRemind;
	}

	public void setPresaleBatchRemind(Boolean presaleBatchRemind) {
		this.presaleBatchRemind = presaleBatchRemind;
	}

	public Boolean getPresaleCnoRemind() {
		return presaleCnoRemind;
	}

	public void setPresaleCnoRemind(Boolean presaleCnoRemind) {
		this.presaleCnoRemind = presaleCnoRemind;
	}

	public Boolean getPresaleBarginRemind() {
		return presaleBarginRemind;
	}

	public void setPresaleBarginRemind(Boolean presaleBarginRemind) {
		this.presaleBarginRemind = presaleBarginRemind;
	}

	public ShopReservePresaleDTO getActive() {
		return active;
	}

	public void setActive(ShopReservePresaleDTO active) {
		this.active = active;
	}

	public Boolean getReserveRemind() {
		return reserveRemind;
	}

	public void setReserveRemind(Boolean reserveRemind) {
		this.reserveRemind = reserveRemind;
	}

	public List<CustomerReserveDTO> getCustomerReserveLst() {
		return customerReserveLst;
	}

	public void setCustomerReserveLst(List<CustomerReserveDTO> customerReserveLst) {
		this.customerReserveLst = customerReserveLst;
	}

	public String getWordId() {
		return wordId;
	}

	public void setWordId(String wordId) {
		this.wordId = wordId;
	}

	public List<String> getSkuIdList() {
		return skuIdList;
	}

	public void setSkuIdList(List<String> skuIdList) {
		this.skuIdList = skuIdList;
	}

	public String getTaskType() {
		return taskType;
	}

	public void setTaskType(String taskType) {
		this.taskType = taskType;
	}
}
