package com.pes.jd.model.Param;

import com.pes.jd.office.excel.style.AbstractCellStyle;
import com.pes.jd.office.excel.style.DefaultDataCellStyle;
import com.pes.jd.office.excel.style.DefaultTitleCellStyle;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

/**
 * @Author: aiJun
 * @Date: 2019-07-24 13:01
 * @Version 1.0
 */
public class ExportExcelParam {
    private SXSSFWorkbook workbook;
    private AbstractCellStyle titleCellStyle;// 标题行样式
    private AbstractCellStyle dataCellStyle;// 数据行样式

    private Integer dataLimit = 500000;

    public ExportExcelParam() {
        // 声明一个工作薄
        this(new SXSSFWorkbook(5000));
    }

    public ExportExcelParam(SXSSFWorkbook workbook) {
        this(workbook, new DefaultTitleCellStyle(workbook), new DefaultDataCellStyle(workbook));
    }

    public ExportExcelParam(SXSSFWorkbook workbook, AbstractCellStyle titleCellStyle, AbstractCellStyle dataCellStyle) {
        this.workbook = workbook;
        this.titleCellStyle = titleCellStyle;
        this.dataCellStyle = dataCellStyle;
        this.workbook.setCompressTempFiles(true);
    }

    public SXSSFWorkbook getWorkbook() {
        return workbook;
    }

    public void setWorkbook(SXSSFWorkbook workbook) {
        this.workbook = workbook;
    }

    public AbstractCellStyle getTitleCellStyle() {
        return titleCellStyle;
    }

    public void setTitleCellStyle(AbstractCellStyle titleCellStyle) {
        this.titleCellStyle = titleCellStyle;
    }

    public AbstractCellStyle getDataCellStyle() {
        return dataCellStyle;
    }

    public void setDataCellStyle(AbstractCellStyle dataCellStyle) {
        this.dataCellStyle = dataCellStyle;
    }

    public Integer getDataLimit() {
        return dataLimit;
    }

    public void setDataLimit(Integer dataLimit) {
        this.dataLimit = dataLimit;
    }
}
