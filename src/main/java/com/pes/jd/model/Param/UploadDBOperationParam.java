package com.pes.jd.model.Param;

import com.alibaba.fastjson.annotation.JSONField;

public class UploadDBOperationParam {
	
	@JSONField(name = "user_ip")
	private String userIp;
	
	@J<PERSON><PERSON>ield(name = "app_name")
	private String appName;
	
	@J<PERSON><PERSON>ield(name = "josAppKey")
	private String josAppKey;
	
	@JSO<PERSON>ield(name = "device_id")
	private String deviceId;
	
	@JSONField(name = "user_id")
	private String userId;
	
	@JSONField(name = "url")
	private String url;
	
	@JSONField(name = "db")
	private String db;
	
	@JSONField(name = "sql")
	private String sql;
	
	@J<PERSON>NField(name = "timestamp")
	private long timeStamp;

	public String getUserIp() {
		return userIp;
	}

	public void setUserIp(String userIp) {
		this.userIp = userIp;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getJosAppKey() {
		return josAppKey;
	}

	public void setJosAppKey(String josAppKey) {
		this.josAppKey = josAppKey;
	}

	public String getDeviceId() {
		return deviceId;
	}

	public void setDeviceId(String deviceId) {
		this.deviceId = deviceId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDb() {
		return db;
	}

	public void setDb(String db) {
		this.db = db;
	}

	public String getSql() {
		return sql;
	}

	public void setSql(String sql) {
		this.sql = sql;
	}

	public long getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(long timeStamp) {
		this.timeStamp = timeStamp;
	}


	@Override
	public String toString() {
		return "UploadDBOperationParam{" +
				"userIp='" + userIp + '\'' +
				", appName='" + appName + '\'' +
				", josAppKey='" + josAppKey + '\'' +
				", deviceId='" + deviceId + '\'' +
				", userId='" + userId + '\'' +
				", url='" + url + '\'' +
				", db='" + db + '\'' +
				", sql='" + sql + '\'' +
				", timeStamp=" + timeStamp +
				'}';
	}
}
