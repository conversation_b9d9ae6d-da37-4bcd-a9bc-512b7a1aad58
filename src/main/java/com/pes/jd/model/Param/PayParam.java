package com.pes.jd.model.Param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: aiJun
 * @Date: 2019-09-21 0:55
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PayParam {
    private Long shopId;
    private String shopTitle;
    private String operator;
    private Integer number;
    private Double orderFe;
    private String ip;
    private String domain;
}
