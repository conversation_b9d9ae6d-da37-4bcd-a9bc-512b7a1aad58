package com.pes.jd.model.Param;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * 
 * <AUTHOR>
 *	微服务之间传shop信息
 */
@JsonIgnoreProperties(ignoreUnknown=true)
public class ShopCommonParam {
	private Long shopId;
	
	private String schemaId;

	private String dbName;

	private String sessionKey;
	
	public ShopCommonParam(Long shopId, String schemaId, String dbName) {
		super();
		this.shopId = shopId;
		this.schemaId = schemaId;
		this.dbName = dbName;
	}

	public ShopCommonParam() {
		super();
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getSchemaId() {
		return schemaId;
	}

	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

	public String getSessionKey() {
		return sessionKey;
	}

	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey;
	}
}
