package com.pes.jd.model.Param;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: aiJun
 * @Date: 2019-06-16 10:58
 * @Version 1.0
 */
public class JdSystemPageParam implements Serializable {

    private static final long serialVersionUID = 2234048108516498349L;
    private String nick;
    private String status;
    private Date startDate;
    private Date endDate;
    private boolean ignoreDate;
    private String operateManager;

    /**
     * 店铺类型:0-pop ,1-自营
     */
    private Integer type;

    public JdSystemPageParam() {
    }

    public JdSystemPageParam(String nick, String status, Date startDate, Date endDate,boolean ignoreDate) {
        this.nick = nick;
        this.status = status;
        this.startDate = startDate;
        this.endDate = endDate;
        this.ignoreDate=ignoreDate;
    }

    public JdSystemPageParam(String nick, String status, Date startDate, Date endDate,boolean ignoreDate,Integer type) {
        this.nick = nick;
        this.status = status;
        this.startDate = startDate;
        this.endDate = endDate;
        this.ignoreDate=ignoreDate;
        this.type = type;
    }

    public JdSystemPageParam(String nick, String status, Date startDate, Date endDate,boolean ignoreDate,Integer type,String operateManager) {
        this.nick = nick;
        this.status = status;
        this.startDate = startDate;
        this.endDate = endDate;
        this.ignoreDate=ignoreDate;
        this.type = type;
        this.operateManager = operateManager;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getStartDateStr() {
        return startDate;
    }

    public void setStartDateStr(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDateStr() {
        return endDate;
    }

    public void setEndDateStr(Date endDate) {
        this.endDate = endDate;
    }

    public boolean isIgnoreDate() {
        return ignoreDate;
    }

    public void setIgnoreDate(boolean ignoreDate) {
        this.ignoreDate = ignoreDate;
    }

    public String getOperateManager() {
        return operateManager;
    }

    public void setOperateManager(String operateManager) {
        this.operateManager = operateManager;
    }
}
