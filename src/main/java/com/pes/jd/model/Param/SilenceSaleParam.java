package com.pes.jd.model.Param;

import java.util.Date;

public class SilenceSaleParam {
    private Date startDate;
    private Date endDate;
    private Long orderId;
    private String buyerNick;
    private Integer orderType;
    private Integer enquiryValidDurationTime;//询单有效时长

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getEnquiryValidDurationTime() {
        return enquiryValidDurationTime;
    }

    public void setEnquiryValidDurationTime(Integer enquiryValidDurationTime) {
        this.enquiryValidDurationTime = enquiryValidDurationTime;
    }

    @Override
    public String toString() {
        return "SilenceSaleParam{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", orderId=" + orderId +
                ", buyerNick='" + buyerNick + '\'' +
                ", orderType=" + orderType +
                ", enquiryValidDurationTime=" + enquiryValidDurationTime +
                '}';
    }
}
