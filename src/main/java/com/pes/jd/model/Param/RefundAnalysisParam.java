package com.pes.jd.model.Param;

import com.pes.jd.model.DTO.CsDTO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 退款分析接口参数
 *
 * <AUTHOR>
 * @create 2019-01-15 16:33
 */

public class RefundAnalysisParam {


    private Integer dateType;
    private Date startDate;
    private Date endDate;
    private String shopId;
    private List<String> csNickList;
    private String customer;
    private Long orderId;
    private Set<Long> orderIds;
    private Long refundId;
    private String refundStatus;
    private String outStatus;
    private String reason;
    private String orderType;
    private List<Long> skuIds;
    private String url;
    private Byte dimension;
    private List<CsDTO> csLst;
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public Integer getDateType() {
        return dateType;
    }

    public void setDateType(Integer dateType) {
        this.dateType = dateType;
    }


    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public List<String> getCsNickList() {
        return csNickList;
    }

    public void setCsNickList(List<String> csNickList) {
        this.csNickList = csNickList;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getRefundId() {
        return refundId;
    }

    public Set<Long> getOrderIds() {
        return orderIds;
    }

    public void setOrderIds(Set<Long> orderIds) {
        this.orderIds = orderIds;
    }

    public void setRefundId(Long refundId) {
        this.refundId = refundId;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getOutStatus() {
        return outStatus;
    }

    public void setOutStatus(String outStatus) {
        this.outStatus = outStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public List<Long> getSkuIds() {
        return skuIds;
    }

    public void setSkuIds(List<Long> skuIds) {
        this.skuIds = skuIds;
    }

    public Byte getDimension() {
        return dimension;
    }

    public void setDimension(Byte dimension) {
        this.dimension = dimension;
    }

    public List<CsDTO> getCsLst() {
        return csLst;
    }

    public void setCsLst(List<CsDTO> csLst) {
        this.csLst = csLst;
    }
}
