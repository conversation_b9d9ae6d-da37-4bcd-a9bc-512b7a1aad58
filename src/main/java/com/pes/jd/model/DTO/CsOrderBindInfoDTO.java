package com.pes.jd.model.DTO;

import java.util.Date;

public class CsOrderBindInfoDTO {
	
	private Long shopId;

	private Date date;

	private String csNick;

	private String buyerNick;

	private Long orderId;

	private Date orderCreated;

	private Date orderPayDate;

	private Double orderPayment;

	private Integer orderGoodsNum;

	private Double orderPostFee;

	private Integer type;
	
	private Date acFirstReplyDate;
	
	private Date acFirstChatDate;
	
	private Date firstChatDate;
	
	private Date lastChatDate;
	
	private Boolean isGoodsFilter;

	private Integer payType;

	private Boolean presale;

	private Date orderValidPayTime;
    public CsOrderBindInfoDTO() {
		super();
	}

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }


    public Long getOrderId() {
		return orderId;
	}


	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}


	public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick == null ? null : buyerNick.trim();
    }


	public Date getOrderCreated() {
		return orderCreated;
	}

	public void setOrderCreated(Date orderCreated) {
		this.orderCreated = orderCreated;
	}

	public Date getOrderPayDate() {
		return orderPayDate;
	}

	public void setOrderPayDate(Date orderPayDate) {
		this.orderPayDate = orderPayDate;
	}

	public Double getOrderPayment() {
		return orderPayment;
	}

	public void setOrderPayment(Double orderPayment) {
		this.orderPayment = orderPayment;
	}

	public Integer getOrderGoodsNum() {
		return orderGoodsNum;
	}

	public void setOrderGoodsNum(Integer orderGoodsNum) {
		this.orderGoodsNum = orderGoodsNum;
	}

	public Double getOrderPostFee() {
		return orderPostFee;
	}

	public void setOrderPostFee(Double orderPostFee) {
		this.orderPostFee = orderPostFee;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Date getFirstChatDate() {
		return firstChatDate;
	}

	public void setFirstChatDate(Date firstChatDate) {
		this.firstChatDate = firstChatDate;
	}

	public Date getLastChatDate() {
		return lastChatDate;
	}

	public void setLastChatDate(Date lastChatDate) {
		this.lastChatDate = lastChatDate;
	}

	public Date getAcFirstReplyDate() {
		return acFirstReplyDate;
	}

	public void setAcFirstReplyDate(Date acFirstReplyDate) {
		this.acFirstReplyDate = acFirstReplyDate;
	}

	public Date getAcFirstChatDate() {
		return acFirstChatDate;
	}

	public void setAcFirstChatDate(Date acFirstChatDate) {
		this.acFirstChatDate = acFirstChatDate;
	}

	public Boolean getIsGoodsFilter() {
		return isGoodsFilter;
	}


	public void setIsGoodsFilter(Boolean isGoodsFilter) {
		this.isGoodsFilter = isGoodsFilter;
	}

	public Boolean getGoodsFilter() {
		return isGoodsFilter;
	}

	public void setGoodsFilter(Boolean goodsFilter) {
		isGoodsFilter = goodsFilter;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Boolean getPresale() {
		return presale;
	}

	public void setPresale(Boolean presale) {
		this.presale = presale;
	}

	public Date getOrderValidPayTime() {
		return orderValidPayTime;
	}

	public void setOrderValidPayTime(Date orderValidPayTime) {
		this.orderValidPayTime = orderValidPayTime;
	}
}
