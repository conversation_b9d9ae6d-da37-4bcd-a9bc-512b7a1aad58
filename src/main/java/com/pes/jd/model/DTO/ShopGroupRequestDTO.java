package com.pes.jd.model.DTO;

import java.util.Date;

public class ShopGroupRequestDTO {
	
	private Long requestId;
	private Long fromId;
	private Long toId;
	private Long shopGroupId;
	private String status;
	private Date requestDate;
	private Date acceptInviteDate;
	
	private String fromShopSellerNick;
	private String fromShopTitle;
	private String toShopTitle;
	private Integer showStatus;//前端显示的授权状态
	private String inviteType;//邀请标志：1：主动邀请 2：被邀请
	
	public String getInviteType() {
		return inviteType;
	}
	public void setInviteType(String inviteType) {
		this.inviteType = inviteType;
	}
	public Integer getShowStatus() {
		return showStatus;
	}
	public void setShowStatus(Integer showStatus) {
		this.showStatus = showStatus;
	}
	public Long getRequestId() {
		return requestId;
	}
	public void setRequestId(Long requestId) {
		this.requestId = requestId;
	}
	public Long getFromId() {
		return fromId;
	}
	public void setFromId(Long fromId) {
		this.fromId = fromId;
	}
	public Long getToId() {
		return toId;
	}
	public void setToId(Long toId) {
		this.toId = toId;
	}
	public Long getShopGroupId() {
		return shopGroupId;
	}
	public void setShopGroupId(Long shopGroupId) {
		this.shopGroupId = shopGroupId;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Date getRequestDate() {
		return requestDate;
	}
	public void setRequestDate(Date requestDate) {
		this.requestDate = requestDate;
	}
	public Date getAcceptInviteDate() {
		return acceptInviteDate;
	}
	public void setAcceptInviteDate(Date acceptInviteDate) {
		this.acceptInviteDate = acceptInviteDate;
	}
	public String getFromShopSellerNick() {
		return fromShopSellerNick;
	}
	public void setFromShopSellerNick(String fromShopSellerNick) {
		this.fromShopSellerNick = fromShopSellerNick;
	}
	public String getFromShopTitle() {
		return fromShopTitle;
	}
	public void setFromShopTitle(String fromShopTitle) {
		this.fromShopTitle = fromShopTitle;
	}
	public String getToShopTitle() {
		return toShopTitle;
	}
	public void setToShopTitle(String toShopTitle) {
		this.toShopTitle = toShopTitle;
	}
}
