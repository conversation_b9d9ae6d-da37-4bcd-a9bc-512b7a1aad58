package com.pes.jd.model.DTO;

import lombok.Data;

import java.time.LocalDate;

@Data
public class MarketingActivityLogSummaryDTO {

    private String date;
    private Integer shopCt;//点击量（去重）
    private Integer typeOneCt;//1、弹出，
    private Integer typeTwoCt;//2、跳转、
    private Integer typeThreeCt;//3、不再提醒
    private Integer typeOneShopCt;//1、弹出，店铺去重
    private Integer typeTwoShopCt;//2、跳转，店铺去重
    private Integer typeThreeShopCt;//3、不再提醒，店铺去重

    private Integer subscribeCt;//订购数（点击量转化）
    private Integer subscribeAmount;//订购金额
}