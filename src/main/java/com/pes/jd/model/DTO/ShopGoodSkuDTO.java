package com.pes.jd.model.DTO;

import java.util.Objects;

public class ShopGoodSkuDTO {
    private Long id;

    private String skuName;

    private String wareName;

    private Double price;

    private Long wareId;

    private Long skuId;

    private Integer status;

    private Integer stockNum;

    private Long categoryId;

    private String imageUrl;

    private Long shopId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    public String getWareName() {
        return wareName;
    }

    public void setWareName(String wareName) {
        this.wareName = wareName == null ? null : wareName.trim();
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Long getWareId() {
        return wareId;
    }

    public void setWareId(Long wareId) {
        this.wareId = wareId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl == null ? null : imageUrl.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ShopGoodSkuDTO)) return false;
        ShopGoodSkuDTO that = (ShopGoodSkuDTO) o;
        return Objects.equals(wareId, that.wareId) &&
                Objects.equals(skuId, that.skuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(wareId, skuId);
    }

    @Override
    public String toString() {
        return "ShopGoodSkuDTO{" +
                "id=" + id +
                ", skuName='" + skuName + '\'' +
                ", wareName='" + wareName + '\'' +
                ", price=" + price +
                ", wareId=" + wareId +
                ", skuId=" + skuId +
                ", status=" + status +
                ", stockNum=" + stockNum +
                ", categoryId=" + categoryId +
                ", imageUrl='" + imageUrl + '\'' +
                ", shopId=" + shopId +
                '}';
    }
}