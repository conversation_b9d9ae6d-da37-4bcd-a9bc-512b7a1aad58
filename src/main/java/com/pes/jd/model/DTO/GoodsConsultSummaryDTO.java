package com.pes.jd.model.DTO;

import java.util.Date;

public class GoodsConsultSummaryDTO {
    private Long id;

    private Long shopId;

    private Date date;

    private Long skuId;

    private Integer consultNum;

    private Integer purchasesBuyerNum;

    private Integer purchasesGoodsNum;

	public GoodsConsultSummaryDTO() {
		super();
	}

	public GoodsConsultSummaryDTO(Integer consultNum, Integer purchasesBuyerNum, Integer purchasesGoodsNum) {
		super();
		this.consultNum = consultNum;
		this.purchasesBuyerNum = purchasesBuyerNum;
		this.purchasesGoodsNum = purchasesGoodsNum;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId ;
    }

    public Integer getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(Integer consultNum) {
        this.consultNum = consultNum;
    }

    public Integer getPurchasesBuyerNum() {
        return purchasesBuyerNum;
    }

    public void setPurchasesBuyerNum(Integer purchasesBuyerNum) {
        this.purchasesBuyerNum = purchasesBuyerNum;
    }

    public Integer getPurchasesGoodsNum() {
        return purchasesGoodsNum;
    }

    public void setPurchasesGoodsNum(Integer purchasesGoodsNum) {
        this.purchasesGoodsNum = purchasesGoodsNum;
    }
}