package com.pes.jd.model.DTO;

import java.util.Date;

public class ReceivedChatpeerDTO {
    private Long id;

    private Long shopId;

    private String csNick;

    private String buyerNick;

    private Date date;

    private Byte chatFlag;

    private Byte forwardFlag;

    private Boolean isWatchwordBuyer;

    private Boolean isFilteredBuyer;

    private Boolean isCsSingleChatFilter;

    private Boolean isConsult;

    private Boolean isReceive;

    private Boolean isEnquiry;

    private Boolean isCsConsultFirst;

    private Integer csChatFirstFlag;

    private Boolean isPes;

    private Boolean isTeamPes;

    private Boolean isNextDayPes;

    private Boolean isAssist;

    private Boolean isAfterSale;
    
    private Boolean isOrderCreated;
    
    private Integer buyerChatNum;
    
    private Integer chatNum;
    
    private Date firstChatDate;

    private Boolean crossChat;

    private Boolean crossChatFilter;

    private Boolean csActiveChatFail;

    private Boolean csActiveUrgepayFail;

    private Boolean crossChatFail;

    public Integer getChatNum() {
		return chatNum;
	}

	public void setChatNum(Integer chatNum) {
		this.chatNum = chatNum;
	}

	public Date getFirstChatDate() {
		return firstChatDate;
	}

	public void setFirstChatDate(Date firstChatDate) {
		this.firstChatDate = firstChatDate;
	}

	public Date getLastChatDate() {
		return lastChatDate;
	}

	public void setLastChatDate(Date lastChatDate) {
		this.lastChatDate = lastChatDate;
	}

	private Date lastChatDate;

    public ReceivedChatpeerDTO() {
		super();
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick == null ? null : buyerNick.trim();
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Byte getChatFlag() {
        return chatFlag;
    }

    public void setChatFlag(Byte chatFlag) {
        this.chatFlag = chatFlag;
    }

    public Byte getForwardFlag() {
        return forwardFlag;
    }

    public void setForwardFlag(Byte forwardFlag) {
        this.forwardFlag = forwardFlag;
    }

    public Boolean getIsWatchwordBuyer() {
        return isWatchwordBuyer;
    }

    public void setIsWatchwordBuyer(Boolean isWatchwordBuyer) {
        this.isWatchwordBuyer = isWatchwordBuyer;
    }

    public Boolean getIsFilteredBuyer() {
        return isFilteredBuyer;
    }

    public void setIsFilteredBuyer(Boolean isFilteredBuyer) {
        this.isFilteredBuyer = isFilteredBuyer;
    }

    public Boolean getIsCsSingleChatFilter() {
        return isCsSingleChatFilter;
    }

    public void setIsCsSingleChatFilter(Boolean isCsSingleChatFilter) {
        this.isCsSingleChatFilter = isCsSingleChatFilter;
    }

    public Boolean getIsConsult() {
        return isConsult;
    }

    public void setIsConsult(Boolean isConsult) {
        this.isConsult = isConsult;
    }

    public Boolean getIsReceive() {
        return isReceive;
    }

    public void setIsReceive(Boolean isReceive) {
        this.isReceive = isReceive;
    }

    public Boolean getIsEnquiry() {
        return isEnquiry;
    }

    public void setIsEnquiry(Boolean isEnquiry) {
        this.isEnquiry = isEnquiry;
    }

    public Boolean getIsCsConsultFirst() {
        return isCsConsultFirst;
    }

    public void setIsCsConsultFirst(Boolean isCsConsultFirst) {
        this.isCsConsultFirst = isCsConsultFirst;
    }

    public Integer getCsChatFirstFlag() {
        return csChatFirstFlag;
    }

    public void setCsChatFirstFlag(Integer csChatFirstFlag) {
        this.csChatFirstFlag = csChatFirstFlag;
    }

    public Boolean getCsActiveChatFail() {
        return csActiveChatFail;
    }

    public void setCsActiveChatFail(Boolean csActiveChatFail) {
        this.csActiveChatFail = csActiveChatFail;
    }

    public Boolean getCsActiveUrgepayFail() {
        return csActiveUrgepayFail;
    }

    public void setCsActiveUrgepayFail(Boolean csActiveUrgepayFail) {
        this.csActiveUrgepayFail = csActiveUrgepayFail;
    }

    public Boolean getIsPes() {
        return isPes;
    }

    public void setIsPes(Boolean isPes) {
        this.isPes = isPes;
    }

    public Boolean getIsTeamPes() {
        return isTeamPes;
    }

    public void setIsTeamPes(Boolean isTeamPes) {
        this.isTeamPes = isTeamPes;
    }

    public Boolean getIsNextDayPes() {
        return isNextDayPes;
    }

    public void setIsNextDayPes(Boolean isNextDayPes) {
        this.isNextDayPes = isNextDayPes;
    }

    public Boolean getIsAssist() {
        return isAssist;
    }

    public void setIsAssist(Boolean isAssist) {
        this.isAssist = isAssist;
    }

    public Boolean getIsAfterSale() {
        return isAfterSale;
    }

    public void setIsAfterSale(Boolean isAfterSale) {
        this.isAfterSale = isAfterSale;
    }

	public Boolean getIsOrderCreated() {
		return isOrderCreated;
	}

	public void setIsOrderCreated(Boolean isOrderCreated) {
		this.isOrderCreated = isOrderCreated;
	}

	public Integer getBuyerChatNum() {
		return buyerChatNum;
	}

	public void setBuyerChatNum(Integer buyerChatNum) {
		this.buyerChatNum = buyerChatNum;
	}

    public Boolean getCrossChat() {
        return crossChat;
    }

    public void setCrossChat(Boolean crossChat) {
        this.crossChat = crossChat;
    }

    public Boolean getCrossChatFilter() {
        return crossChatFilter;
    }

    public void setCrossChatFilter(Boolean crossChatFilter) {
        this.crossChatFilter = crossChatFilter;
    }

    public Boolean getWatchwordBuyer() {
        return isWatchwordBuyer;
    }

    public void setWatchwordBuyer(Boolean watchwordBuyer) {
        isWatchwordBuyer = watchwordBuyer;
    }

    public Boolean getFilteredBuyer() {
        return isFilteredBuyer;
    }

    public void setFilteredBuyer(Boolean filteredBuyer) {
        isFilteredBuyer = filteredBuyer;
    }

    public Boolean getCsSingleChatFilter() {
        return isCsSingleChatFilter;
    }

    public void setCsSingleChatFilter(Boolean csSingleChatFilter) {
        isCsSingleChatFilter = csSingleChatFilter;
    }

    public Boolean getConsult() {
        return isConsult;
    }

    public void setConsult(Boolean consult) {
        isConsult = consult;
    }

    public Boolean getReceive() {
        return isReceive;
    }

    public void setReceive(Boolean receive) {
        isReceive = receive;
    }

    public Boolean getEnquiry() {
        return isEnquiry;
    }

    public void setEnquiry(Boolean enquiry) {
        isEnquiry = enquiry;
    }

    public Boolean getCsConsultFirst() {
        return isCsConsultFirst;
    }

    public void setCsConsultFirst(Boolean csConsultFirst) {
        isCsConsultFirst = csConsultFirst;
    }

    public Boolean getPes() {
        return isPes;
    }

    public void setPes(Boolean pes) {
        isPes = pes;
    }

    public Boolean getTeamPes() {
        return isTeamPes;
    }

    public void setTeamPes(Boolean teamPes) {
        isTeamPes = teamPes;
    }

    public Boolean getNextDayPes() {
        return isNextDayPes;
    }

    public void setNextDayPes(Boolean nextDayPes) {
        isNextDayPes = nextDayPes;
    }

    public Boolean getAssist() {
        return isAssist;
    }

    public void setAssist(Boolean assist) {
        isAssist = assist;
    }

    public Boolean getAfterSale() {
        return isAfterSale;
    }

    public void setAfterSale(Boolean afterSale) {
        isAfterSale = afterSale;
    }

    public Boolean getOrderCreated() {
        return isOrderCreated;
    }

    public void setOrderCreated(Boolean orderCreated) {
        isOrderCreated = orderCreated;
    }

    public Boolean getCrossChatFail() {
        return crossChatFail;
    }

    public void setCrossChatFail(Boolean crossChatFail) {
        this.crossChatFail = crossChatFail;
    }
}
