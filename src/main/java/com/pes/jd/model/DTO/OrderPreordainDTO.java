package com.pes.jd.model.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2020年03月05 17:48:48<br>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderPreordainDTO {
	private Date date;//日期
	private Long orderId;//订单编号
	private String buyerNick;//顾客id
	private Long skuId;//商品编号
	private String skuName;//商品名称
	private Date created;//下单时间
	private Date payTime;//付尾款时间
	private Double payment;
	private Date outStockTime;//出库时间
	private String fulfilOrderCs;//落实下单
	private String fulfilPayCs;//落实付尾款
	private String pesBelongCs;//绩效归属
	private String orderStatus;//订单状态
	private Long shopId;
	private String csNick;//客服昵称
	private Integer type;//1：落实下单 2：落实付款 3：全静默绩效绑定
	private String fulfilOrderCsSimpleNick;//落实下单
	private String fulfilPayCsSimpleNick;//落实付尾款
	private String pesBelongCsSimpleNick;//绩效归属
	private Integer goodsNum;
	private String consigneeTelp;
	private String consignee;
	private Integer payType;
}
