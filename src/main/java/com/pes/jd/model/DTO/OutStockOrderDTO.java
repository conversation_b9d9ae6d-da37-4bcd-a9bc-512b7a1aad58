package com.pes.jd.model.DTO;

public class OutStockOrderDTO{

	private String buyerNick;

	private Long orderId;

	private Double payment;//订单应付金额

	private Double orderBargainPayment;//付定金

	private Double orderBalancePayment;//付尾款

	private Integer num;

	private Boolean preSale = Boolean.FALSE;

	public OutStockOrderDTO() {
		super();
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Double getPayment() {
		return payment;
	}

	public void setPayment(Double payment) {
		this.payment = payment;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public Double getOrderBalancePayment() {
		return orderBalancePayment;
	}

	public void setOrderBalancePayment(Double orderBalancePayment) {
		this.orderBalancePayment = orderBalancePayment;
	}

	public Boolean getPreSale() {
		return preSale;
	}

	public void setPreSale(Boolean preSale) {
		this.preSale = preSale;
	}

	public Double getOrderBargainPayment() {
		return orderBargainPayment;
	}

	public void setOrderBargainPayment(Double orderBargainPayment) {
		this.orderBargainPayment = orderBargainPayment;
	}
}
