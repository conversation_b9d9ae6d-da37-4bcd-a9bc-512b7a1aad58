package com.pes.jd.model.DTO;

import java.util.Date;

public class CsServiceIndexDTO {
    private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private Double avgWaitTimeFirst;

    private Double avgWaitTime;

    private Long sessionTime;

    private Integer nonReplyNum;

    private Integer csWordNum;

    private Integer csReplyNum;

    private Integer buyerChatNum;

    private Double qaRate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Double getAvgWaitTimeFirst() {
        return avgWaitTimeFirst;
    }

    public void setAvgWaitTimeFirst(Double avgWaitTimeFirst) {
        this.avgWaitTimeFirst = avgWaitTimeFirst;
    }

    public Double getAvgWaitTime() {
        return avgWaitTime;
    }

    public void setAvgWaitTime(Double avgWaitTime) {
        this.avgWaitTime = avgWaitTime;
    }

    public Long getSessionTime() {
        return sessionTime;
    }

    public void setSessionTime(Long sessionTime) {
        this.sessionTime = sessionTime;
    }

    public Integer getNonReplyNum() {
        return nonReplyNum;
    }

    public void setNonReplyNum(Integer nonReplyNum) {
        this.nonReplyNum = nonReplyNum;
    }

    public Integer getCsWordNum() {
        return csWordNum;
    }

    public void setCsWordNum(Integer csWordNum) {
        this.csWordNum = csWordNum;
    }

    public Integer getCsReplyNum() {
        return csReplyNum;
    }

    public void setCsReplyNum(Integer csReplyNum) {
        this.csReplyNum = csReplyNum;
    }

    public Integer getBuyerChatNum() {
        return buyerChatNum;
    }

    public void setBuyerChatNum(Integer buyerChatNum) {
        this.buyerChatNum = buyerChatNum;
    }

    public Double getQaRate() {
        return qaRate;
    }

    public void setQaRate(Double qaRate) {
        this.qaRate = qaRate;
    }
}