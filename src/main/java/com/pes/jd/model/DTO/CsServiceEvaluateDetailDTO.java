package com.pes.jd.model.DTO;

import java.sql.Timestamp;
import java.util.Date;

public class CsServiceEvaluateDetailDTO {
	
	private Long shopId;
	
	private String csNick;
	
	private String buyerNick;
	
	private Date sendTime;
	
	private Date evalTime;
	
	private String evalCode;

	private String csSimpleNick;
	
	private Timestamp beginDateTime;//接待开始时间
	
	private Timestamp endDateTime; 
	
	private String sid;
	
	
	public Timestamp getBeginDateTime() {
		return beginDateTime;
	}

	public void setBeginDateTime(Timestamp beginDateTime) {
		this.beginDateTime = beginDateTime;
	}

	public Timestamp getEndDateTime() {
		return endDateTime;
	}

	public void setEndDateTime(Timestamp endDateTime) {
		this.endDateTime = endDateTime;
	}

	public String getSid() {
		return sid;
	}

	public void setSid(String sid) {
		this.sid = sid;
	}

	public String getCsSimpleNick() {
		return csSimpleNick;
	}

	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	public Date getEvalTime() {
		return evalTime;
	}

	public void setEvalTime(Date evalTime) {
		this.evalTime = evalTime;
	}

	public String getEvalCode() {
		return evalCode;
	}

	public void setEvalCode(String evalCode) {
		this.evalCode = evalCode;
	}
	
	
}
