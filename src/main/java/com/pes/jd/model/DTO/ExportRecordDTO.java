package com.pes.jd.model.DTO;

import java.util.Date;

public class ExportRecordDTO {
    private Long id;

    private String optUser;

    private String optUserShopId;

    private String shopId;

    private String type;

    private Date time;

    private String condition;

    private String conditionDocument;

    private String name;

    private String latitude;

    private Integer status;

    private String url;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOptUser() {
        return optUser;
    }

    public void setOptUser(String optUser) {
        this.optUser = optUser;
    }

    public String getOptUserShopId() {
        return optUserShopId;
    }

    public void setOptUserShopId(String optUserShopId) {
        this.optUserShopId = optUserShopId;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getConditionDocument() {
        return conditionDocument;
    }

    public void setConditionDocument(String conditionDocument) {
        this.conditionDocument = conditionDocument;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    @Override
    public String toString() {
        return "ExportRecordDTO{" +
                "id=" + id +
                ", optUser='" + optUser + '\'' +
                ", optUserShopId='" + optUserShopId + '\'' +
                ", shopId='" + shopId + '\'' +
                ", type='" + type + '\'' +
                ", time=" + time +
                ", condition='" + condition + '\'' +
                ", conditionDocument='" + conditionDocument + '\'' +
                ", name='" + name + '\'' +
                ", latitude='" + latitude + '\'' +
                ", status=" + status +
                ", url='" + url + '\'' +
                '}';
    }
}