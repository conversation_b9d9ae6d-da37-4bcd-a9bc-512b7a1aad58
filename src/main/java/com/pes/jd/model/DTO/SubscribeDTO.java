package com.pes.jd.model.DTO;

import java.util.Date;

public class SubscribeDTO {
	
	private Long shopId;
	
	private Date startDate;//订购日期
	
	private Date endDate;//到期日期
	
	private Integer status;//订购状态
	
	private String version;//订购版本
	
	private Integer orderCycle;//订购周期
	
	private String shopName;

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Integer getOrderCycle() {
		return orderCycle;
	}

	public void setOrderCycle(Integer orderCycle) {
		this.orderCycle = orderCycle;
	}
}
