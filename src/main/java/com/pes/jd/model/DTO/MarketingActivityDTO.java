package com.pes.jd.model.DTO;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MarketingActivityDTO implements Serializable {
    private Long id;

    private String activityName;

    private String activityContent;

    private Date startDate;

    private Date endDate;

    private Date createDate;

    private String version;

    private String orderType;

    private Boolean enableSwitch;

    private String url;


}