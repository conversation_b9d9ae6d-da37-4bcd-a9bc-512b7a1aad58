package com.pes.jd.model.DTO;

import java.util.Date;

public class CsConfirmGoodsIndexDTO {

    private Long shopId;

    private Date date;

    private String csNick;

    private Integer conformGoodsNum;

    private Integer conformGoodsOrderBuyerNum;

    private Integer conformGoodsOrderNum;

    private Double conformGoodsOrderAmount;

	public CsConfirmGoodsIndexDTO() {
		super();
	}

	public CsConfirmGoodsIndexDTO(Long shopId, Date date, String csNick) {
		super();
		this.shopId = shopId;
		this.date = date;
		this.csNick = csNick;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Integer getConformGoodsNum() {
		return conformGoodsNum;
	}

	public void setConformGoodsNum(Integer conformGoodsNum) {
		this.conformGoodsNum = conformGoodsNum;
	}

	public Integer getConformGoodsOrderBuyerNum() {
		return conformGoodsOrderBuyerNum;
	}

	public void setConformGoodsOrderBuyerNum(Integer conformGoodsOrderBuyerNum) {
		this.conformGoodsOrderBuyerNum = conformGoodsOrderBuyerNum;
	}

	public Integer getConformGoodsOrderNum() {
		return conformGoodsOrderNum;
	}

	public void setConformGoodsOrderNum(Integer conformGoodsOrderNum) {
		this.conformGoodsOrderNum = conformGoodsOrderNum;
	}

	public Double getConformGoodsOrderAmount() {
		return conformGoodsOrderAmount;
	}

	public void setConformGoodsOrderAmount(Double conformGoodsOrderAmount) {
		this.conformGoodsOrderAmount = conformGoodsOrderAmount;
	}

	

   
}