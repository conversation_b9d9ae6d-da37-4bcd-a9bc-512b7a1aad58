package com.pes.jd.model.DTO;

import java.util.Date;

/**  
 * ClassName:LostRecordNoteDTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月19日 下午3:04:53 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class LostRecordNoteDTO {

	private Long id;
    private Long shopId;
    private Date date;//流失日期
    private String orderId;//订单id
    private String buyerNick;//买家旺旺id
    private String lostType;//流失类型
    private String note;//备注
    
    public LostRecordNoteDTO() {
		super();
	}

	public LostRecordNoteDTO(Long shopId) {
		super();
		this.shopId = shopId;
	}

	public Long getShopId() {
		return shopId;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getLostType() {
		return lostType;
	}

	public void setLostType(String lostType) {
		this.lostType = lostType;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}


	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
    
}
  
