package com.pes.jd.model.DTO;

import java.util.Date;

public class CsServiceEvaluationDTO {
	
	private Long id;

	private Long shopId;

	private String csNick;

	private Date date;
	
	private Integer verySatisfiedNum; //非常满意
	private Integer satisfiedNum; //满意
	private Integer generalNum; //一般
	private Integer dissatisfiedNum; //不满意
	private Integer veryDissatisfiedNum; //非常不满意
	
	private Integer evalReplyNum;//评价量
	private Integer evalSendNum;//邀评量

	public CsServiceEvaluationDTO() {
	}

	public CsServiceEvaluationDTO(Long shopId, String csNick, Date date) {
		super();
		this.shopId = shopId;
		this.csNick = csNick;
		this.date = date;
	}

	// 初始化
	public void init() {
		this.verySatisfiedNum = 0;
		this.satisfiedNum = 0;
		this.generalNum = 0;
		this.dissatisfiedNum = 0;
		this.veryDissatisfiedNum = 0;
		this.evalReplyNum = 0;
		this.evalSendNum = 0;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public Integer getEvalReplyNum() {
		return evalReplyNum;
	}
	public void setEvalReplyNum(Integer evalReplyNum) {
		this.evalReplyNum = evalReplyNum;
	}
	public Integer getEvalSendNum() {
		return evalSendNum;
	}
	public void setEvalSendNum(Integer evalSendNum) {
		this.evalSendNum = evalSendNum;
	}
	public Integer getVerySatisfiedNum() {
		return verySatisfiedNum;
	}
	public void setVerySatisfiedNum(Integer verySatisfiedNum) {
		this.verySatisfiedNum = verySatisfiedNum;
	}
	public Integer getSatisfiedNum() {
		return satisfiedNum;
	}
	public void setSatisfiedNum(Integer satisfiedNum) {
		this.satisfiedNum = satisfiedNum;
	}
	public Integer getGeneralNum() {
		return generalNum;
	}
	public void setGeneralNum(Integer generalNum) {
		this.generalNum = generalNum;
	}
	public Integer getDissatisfiedNum() {
		return dissatisfiedNum;
	}
	public void setDissatisfiedNum(Integer dissatisfiedNum) {
		this.dissatisfiedNum = dissatisfiedNum;
	}
	public Integer getVeryDissatisfiedNum() {
		return veryDissatisfiedNum;
	}
	public void setVeryDissatisfiedNum(Integer veryDissatisfiedNum) {
		this.veryDissatisfiedNum = veryDissatisfiedNum;
	}
}
