package com.pes.jd.model.DTO;

public class JobShopDTO {
	
    private Long id;

    private Long shopId;

    private String sellerNick;

    private String title;

    private String sessionKey;

    private String status;

    private Integer initDataFlag;

    private String schemaId;

    private String db;
    
    private int colType;
    
    private Long venderId;
    
    private String rtSchemaId;
    
    private String rtDb;
    
    private Integer type;

    private String optionSessionKey;

    //签名id
    private String signId;

    private String businessCategory;

    public String getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(String businessCategory) {
        this.businessCategory = businessCategory;
    }

    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }

    public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public JobShopDTO() {
		super();
	}

	public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick == null ? null : sellerNick.trim();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey == null ? null : sessionKey.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getSchemaId() {
        return schemaId;
    }

    public void setSchemaId(String schemaId) {
        this.schemaId = schemaId == null ? null : schemaId.trim();
    }

    public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db == null ? null : db.trim();
    }

	public int getColType() {
		return colType;
	}

	public void setColType(int colType) {
		this.colType = colType;
	}

	public Long getVenderId() {
		return venderId;
	}

	public void setVenderId(Long venderId) {
		this.venderId = venderId;
	}
	
    public String getRtSchemaId() {
		return rtSchemaId;
	}

	public void setRtSchemaId(String rtSchemaId) {
		this.rtSchemaId = rtSchemaId;
	}

	public String getRtDb() {
		return rtDb;
	}

	public void setRtDb(String rtDb) {
		this.rtDb = rtDb;
	}

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getInitDataFlag() {
        return initDataFlag;
    }

    public void setInitDataFlag(Integer initDataFlag) {
        this.initDataFlag = initDataFlag;
    }

    public String getOptionSessionKey() {
        return optionSessionKey;
    }

    public void setOptionSessionKey(String optionSessionKey) {
        this.optionSessionKey = optionSessionKey;
    }

    @Override
    public String toString() {
        return "JobShopDTO{" +
                "shopId=" + shopId +
                ", title='" + title + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", status='" + status + '\'' +
                ", schemaId='" + schemaId + '\'' +
                ", db='" + db + '\'' +
                ", colType=" + colType +
                ", rtSchemaId='" + rtSchemaId + '\'' +
                ", rtDb='" + rtDb + '\'' +
                ", type=" + type +
                ", optionSessionKey='" + optionSessionKey + '\'' +
                '}';
    }
}
