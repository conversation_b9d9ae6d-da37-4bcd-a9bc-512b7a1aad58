package com.pes.jd.model.DTO;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

public class JobShopDTO {
	

    private Long shopId;

    private Long userId;

    private String sellerNick;

    private String title;

    private String sessionKey;

    private String status;

    private Integer subuserNum;

    private Boolean realtimeSwitch;

    private Integer fetchFlag;

    private Integer initDataFlag;

    private Date previousGetDataTime;

    private Date preFetchRealtime;

    private Long lastConsumedTime;

    private String schemaId;

    private String db;

    private int colType;

    private int type;
    
    private Long venderId;

    private String rtDb;
    private String rtSchemaId;

    private  String optionSessionKey;

    private String 

    Map<String, HashSet<String>> parentOdIds = new HashMap<>();

    Map<String, Boolean> presaleOdFilter = new HashMap<>();


    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public JobShopDTO() {
		super();
	}

    public Map<String, Boolean> getPresaleOdFilter() {
        return presaleOdFilter;
    }

    public void setPresaleOdFilter(Map<String, Boolean> presaleOdFilter) {
        this.presaleOdFilter = presaleOdFilter;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick == null ? null : sellerNick.trim();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey == null ? null : sessionKey.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Integer getSubuserNum() {
        return subuserNum;
    }

    public void setSubuserNum(Integer subuserNum) {
        this.subuserNum = subuserNum;
    }

    public Boolean getRealtimeSwitch() {
        return realtimeSwitch;
    }

    public void setRealtimeSwitch(Boolean realtimeSwitch) {
        this.realtimeSwitch = realtimeSwitch;
    }

    public Integer getFetchFlag() {
        return fetchFlag;
    }

    public void setFetchFlag(Integer fetchFlag) {
        this.fetchFlag = fetchFlag;
    }

    public Integer getInitDataFlag() {
        return initDataFlag;
    }

    public void setInitDataFlag(Integer initDataFlag) {
        this.initDataFlag = initDataFlag;
    }

    public Date getPreviousGetDataTime() {
        return previousGetDataTime;
    }

    public void setPreviousGetDataTime(Date previousGetDataTime) {
        this.previousGetDataTime = previousGetDataTime;
    }

    public Date getPreFetchRealtime() {
        return preFetchRealtime;
    }

    public void setPreFetchRealtime(Date preFetchRealtime) {
        this.preFetchRealtime = preFetchRealtime;
    }

    public Long getLastConsumedTime() {
        return lastConsumedTime;
    }

    public void setLastConsumedTime(Long lastConsumedTime) {
        this.lastConsumedTime = lastConsumedTime;
    }

    public String getSchemaId() {
        return schemaId;
    }

    public void setSchemaId(String schemaId) {
        this.schemaId = schemaId == null ? null : schemaId.trim();
    }

    public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db == null ? null : db.trim();
    }

	public int getColType() {
		return colType;
	}

	public void setColType(int colType) {
		this.colType = colType;
	}

	public Long getVenderId() {
		return venderId;
	}

	public void setVenderId(Long venderId) {
		this.venderId = venderId;
	}

    public String getRtDb() {
        return rtDb;
    }

    public void setRtDb(String rtDb) {
        this.rtDb = rtDb;
    }

    public String getRtSchemaId() {
        return rtSchemaId;
    }

    public void setRtSchemaId(String rtSchemaId) {
        this.rtSchemaId = rtSchemaId;
    }

    public String getOptionSessionKey() {
        return optionSessionKey;
    }

    public void setOptionSessionKey(String optionSessionKey) {
        this.optionSessionKey = optionSessionKey;
    }

    public Map<String, HashSet<String>> getParentOdIds() {
        return parentOdIds;
    }

    public void setParentOdIds(Map<String, HashSet<String>> parentOdIds) {
        this.parentOdIds = parentOdIds;
    }

    @Override
    public String toString() {
        return "JobShopDTO{" +
                "shopId=" + shopId +
                ", userId=" + userId +
                ", sellerNick='" + sellerNick + '\'' +
                ", title='" + title + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", status='" + status + '\'' +
                ", subuserNum=" + subuserNum +
                ", realtimeSwitch=" + realtimeSwitch +
                ", fetchFlag=" + fetchFlag +
                ", initDataFlag=" + initDataFlag +
                ", previousGetDataTime=" + previousGetDataTime +
                ", preFetchRealtime=" + preFetchRealtime +
                ", lastConsumedTime=" + lastConsumedTime +
                ", schemaId='" + schemaId + '\'' +
                ", db='" + db + '\'' +
                ", colType=" + colType +
                ", venderId=" + venderId +
                '}';
    }
}
