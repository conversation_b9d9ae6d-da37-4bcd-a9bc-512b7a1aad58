package com.pes.jd.model.DTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2019-01-29 15:38
 */

public class ShopDsrDTO {

    private Long id;

    private Long shopId;

    private Date date;

    private String dateStr;

    private Double itemScore;

    private Double serviceScore;

    private Double deliveryScore;

    private Double afterSaleScore;

    private Double disputeScore;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Double getItemScore() {
        return itemScore;
    }

    public void setItemScore(Double itemScore) {
        this.itemScore = itemScore;
    }

    public Double getServiceScore() {
        return serviceScore;
    }

    public void setServiceScore(Double serviceScore) {
        this.serviceScore = serviceScore;
    }

    public Double getDeliveryScore() {
        return deliveryScore;
    }

    public void setDeliveryScore(Double deliveryScore) {
        this.deliveryScore = deliveryScore;
    }

    public Double getAfterSaleScore() {
        return afterSaleScore;
    }

    public void setAfterSaleScore(Double afterSaleScore) {
        this.afterSaleScore = afterSaleScore;
    }

    public Double getDisputeScore() {
        return disputeScore;
    }

    public void setDisputeScore(Double disputeScore) {
        this.disputeScore = disputeScore;
    }

    @Override
    public String toString() {
        return "ShopDsrDTO{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", date=" + date +
                ", dateStr='" + dateStr + '\'' +
                ", itemScore=" + itemScore +
                ", serviceScore=" + serviceScore +
                ", deliveryScore=" + deliveryScore +
                ", afterSaleScore=" + afterSaleScore +
                ", disputeScore=" + disputeScore +
                '}';
    }
}
