package com.pes.jd.model.DTO;



/**
 * <AUTHOR>
 * @version 2019-05-27 16:29
 */
public class GoosDetailInfoDTO{
    private String goodsName;
    private Double price;
    private String status;
    private Long wareId;
    private Long skuId;
    private String imageUrl;
    private Integer num;
    private String pcUrl;
    private String phoneUrl;
    
    
    
	public String getPhoneUrl() {
		return phoneUrl;
	}
	public void setPhoneUrl(String phoneUrl) {
		this.phoneUrl = phoneUrl;
	}

	
	
	public String getPcUrl() {
		return pcUrl;
	}
	public void setPcUrl(String pcUrl) {
		this.pcUrl = pcUrl;
	}
	public String getGoodsName() {
		return goodsName;
	}
	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Long getWareId() {
		return wareId;
	}
	public void setWareId(Long wareId) {
		this.wareId = wareId;
	}
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public Integer getNum() {
		return num;
	}
	public void setNum(Integer num) {
		this.num = num;
	}
    
    
    
    
}
