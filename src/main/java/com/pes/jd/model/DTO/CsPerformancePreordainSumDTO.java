package com.pes.jd.model.DTO;

import lombok.Data;

import java.util.Date;

/**
 * @Author:acer
 * @Dcscription: 客服绩效-预约商品
 * @Date: Created in 2020/3/12
 * @Modified By:
 */
@Data
public class CsPerformancePreordainSumDTO {
    private Long id;
    private Long shopId;
    private Date date;
    private String csNick;
    private Long skuId;   //预约商品sku编号
    private String skuName; //预约商品sku名称
    private int enquiryOrderedBuyerNum; //下单人数
    private int enquiryOrderedSkuNum;  //下单件数
    private int enquiryOrderedPaidBuyerNum; //付款人数
    private int enquiryOrderedPaidSkuNum;  //付款件数
    private double enquiryOrderedPaidAmount; //付款金额
    private int consultBuyerNum;  //咨询人数：针对本商品咨询本客服的顾客数
    private int enquiryBuyerNum;  //询单  人数：咨询人数中，本客服接待的询单顾客数
    private String  enquiryBuyerConversion;//、询单👉下单转化：下单人数/询单人数
    private String  enquiryPaidConversion; // 询单👉付款转化：付款人数/询单人数
}
