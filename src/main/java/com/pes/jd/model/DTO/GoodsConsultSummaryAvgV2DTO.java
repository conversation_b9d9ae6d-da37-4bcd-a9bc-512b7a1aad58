package com.pes.jd.model.DTO;

import lombok.Data;

import java.util.Date;

@Data
public class GoodsConsultSummaryAvgV2DTO {
	
	private Long skuId;

	private String skuName;
	
	private String csNick;
	
	private Long shopId;

	private double receiveNum;

	private double enquiryNum;

	private double payNum;

	private double payGoodsNum;

	private double payAmount;

	private Double buyPercent;
	
	private Date date;
	
	private ShopGoodsSkuDTO goodsDTO;
	
	private Long categoryId;
	
	private String imageUrl;
	
	private String groupId;
	
	private Integer type;

}
