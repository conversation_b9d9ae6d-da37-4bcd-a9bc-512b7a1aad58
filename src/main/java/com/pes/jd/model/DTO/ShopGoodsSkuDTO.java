package com.pes.jd.model.DTO;

import java.util.List;
import java.util.StringJoiner;

public class ShopGoodsSkuDTO {
    private Long id;

    private String skuName;

    private String wareName;

    private Double price;

    private Long wareId;

    private Long skuId;

    private Integer status;

    private Integer stockNum;

    private Long categoryId;

    private String imageUrl;

    private Long shopId;

    private String pcUrl;

    private String phoneUrl;

    /**
     *  是否主推
     */
    private Boolean recommend;

    private String articleNumber;

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public String getPhoneUrl() {
        return phoneUrl;
    }

    public void setPhoneUrl(String phoneUrl) {
        this.phoneUrl = phoneUrl;
    }

    private List<ShopGoodsSkuDTO> associative;

    public List<ShopGoodsSkuDTO> getAssociative() {
        return associative;
    }

    public void setAssociative(List<ShopGoodsSkuDTO> associative) {
        this.associative = associative;
    }

    public Boolean getRecommend() {
        return recommend;
    }

    public void setRecommend(Boolean recommend) {
        this.recommend = recommend;
    }

    private List<ShopGoodsSkuLabelDTO> shopGoodsSkuLabelLst;

    public List<ShopGoodsSkuLabelDTO> getShopGoodsSkuLabelLst() {
        return shopGoodsSkuLabelLst;
    }

    public void setShopGoodsSkuLabelLst(List<ShopGoodsSkuLabelDTO> shopGoodsSkuLabelLst) {
        this.shopGoodsSkuLabelLst = shopGoodsSkuLabelLst;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    public String getWareName() {
        return wareName;
    }

    public void setWareName(String wareName) {
        this.wareName = wareName == null ? null : wareName.trim();
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Long getWareId() {
        return wareId;
    }

    public void setWareId(Long wareId) {
        this.wareId = wareId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl == null ? null : imageUrl.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getArticleNumber() {
        return articleNumber;
    }

    public void setArticleNumber(String articleNumber) {
        this.articleNumber = articleNumber;
    }

    public ShopGoodsSkuDTO(){
    	
    }
    
    public ShopGoodsSkuDTO(Long skuId){
    	this.skuId =skuId;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", ShopGoodsSkuDTO.class.getSimpleName() + "[", "]")
                .add("id=" + id)
                .add("skuName='" + skuName + "'")
                .add("wareName='" + wareName + "'")
                .add("price=" + price)
                .add("wareId=" + wareId)
                .add("skuId=" + skuId)
                .add("status=" + status)
                .add("stockNum=" + stockNum)
                .add("categoryId=" + categoryId)
                .add("imageUrl='" + imageUrl + "'")
                .add("shopId=" + shopId)
                .add("recommend=" + recommend)
                .add("associative=" + associative)
                .add("shopGoodsSkuLabelLst=" + shopGoodsSkuLabelLst)
                .toString();
    }
}