package com.pes.jd.model.DTO;

import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class CsPerformanceDTO {

    public static CsPerformanceDTO getInstance() {
        CsPerformanceDTO re = new CsPerformanceDTO();
        re.setId(0L);
        re.setShopId(0L);
        re.setDate(null);
        re.setCsNick(StringUtils.EMPTY);
        re.setDirectReceiveNum(0);
        re.setForwardInNum(0);
        re.setForwardOutNum(0);
        re.setConsultNum(0);
        re.setReceiveNum(0);
        re.setEnquiryNum(0);
        re.setOrderedNumToday(0);
        re.setOrderedGoodsNumToday(0);
        re.setOrderedAmountToday(0d);
        re.setOrderedNumFinal(0);
        re.setOrderedGoodsNumFinal(0);
        re.setOrderedAmountFinal(0d);
        re.setPaidNumToday(0);
        re.setPaidAmountToday(0d);
        re.setPaidGoodsNumToday(0);
        re.setPaidNumTodayNext(0);
        re.setPaidNumFinal(0);
        re.setPaidGoodsNumFinal(0);
        re.setPaidAmountFinal(0d);
        re.setReceiveRate(0d);
        re.setConversionRate(0d);
        re.setOutStockOrderBuyerNumFinal(0);
        re.setOutStockOrderNumFinal(0);
        re.setOutStockOrderGoodsNumFinal(0);
        re.setOutStockOrderAmountFinal(0);
        return re;
    }

    private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private Integer directReceiveNum;

    private Integer forwardInNum;

    private Integer forwardOutNum;

    private Integer consultNum;

    private Integer receiveNum;

    private Integer enquiryNum;

    private Integer orderedNumToday;

    private Integer orderedGoodsNumToday;

    private Double orderedAmountToday;

    private Integer orderedNumFinal;

    private Integer orderedGoodsNumFinal;

    private Double orderedAmountFinal;

    private Integer paidNumToday;

    private Double paidAmountToday;

    private Integer paidGoodsNumToday;

    private Integer paidNumTodayNext;

    private Integer paidNumFinal;

    private Integer paidGoodsNumFinal;

    private Double paidAmountFinal;

    private Double receiveRate;

    private Double conversionRate;

    private Integer outStockOrderBuyerNumFinal;

    private Integer outStockOrderNumFinal;

    private Integer outStockOrderGoodsNumFinal;

    private Integer outStockOrderAmountFinal;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick;
    }

    public Integer getDirectReceiveNum() {
        return directReceiveNum;
    }

    public void setDirectReceiveNum(Integer directReceiveNum) {
        this.directReceiveNum = directReceiveNum;
    }

    public Integer getForwardInNum() {
        return forwardInNum;
    }

    public void setForwardInNum(Integer forwardInNum) {
        this.forwardInNum = forwardInNum;
    }

    public Integer getForwardOutNum() {
        return forwardOutNum;
    }

    public void setForwardOutNum(Integer forwardOutNum) {
        this.forwardOutNum = forwardOutNum;
    }

    public Integer getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(Integer consultNum) {
        this.consultNum = consultNum;
    }

    public Integer getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Integer receiveNum) {
        this.receiveNum = receiveNum;
    }

    public Integer getEnquiryNum() {
        return enquiryNum;
    }

    public void setEnquiryNum(Integer enquiryNum) {
        this.enquiryNum = enquiryNum;
    }

    public Integer getOrderedNumToday() {
        return orderedNumToday;
    }

    public void setOrderedNumToday(Integer orderedNumToday) {
        this.orderedNumToday = orderedNumToday;
    }

    public Integer getOrderedGoodsNumToday() {
        return orderedGoodsNumToday;
    }

    public void setOrderedGoodsNumToday(Integer orderedGoodsNumToday) {
        this.orderedGoodsNumToday = orderedGoodsNumToday;
    }

    public Double getOrderedAmountToday() {
        return orderedAmountToday;
    }

    public void setOrderedAmountToday(Double orderedAmountToday) {
        this.orderedAmountToday = orderedAmountToday;
    }

    public Integer getOrderedNumFinal() {
        return orderedNumFinal;
    }

    public void setOrderedNumFinal(Integer orderedNumFinal) {
        this.orderedNumFinal = orderedNumFinal;
    }

    public Integer getOrderedGoodsNumFinal() {
        return orderedGoodsNumFinal;
    }

    public void setOrderedGoodsNumFinal(Integer orderedGoodsNumFinal) {
        this.orderedGoodsNumFinal = orderedGoodsNumFinal;
    }

    public Double getOrderedAmountFinal() {
        return orderedAmountFinal;
    }

    public void setOrderedAmountFinal(Double orderedAmountFinal) {
        this.orderedAmountFinal = orderedAmountFinal;
    }

    public Integer getPaidNumToday() {
        return paidNumToday;
    }

    public void setPaidNumToday(Integer paidNumToday) {
        this.paidNumToday = paidNumToday;
    }

    public Double getPaidAmountToday() {
        return paidAmountToday;
    }

    public void setPaidAmountToday(Double paidAmountToday) {
        this.paidAmountToday = paidAmountToday;
    }

    public Integer getPaidGoodsNumToday() {
        return paidGoodsNumToday;
    }

    public void setPaidGoodsNumToday(Integer paidGoodsNumToday) {
        this.paidGoodsNumToday = paidGoodsNumToday;
    }

    public Integer getPaidNumTodayNext() {
        return paidNumTodayNext;
    }

    public void setPaidNumTodayNext(Integer paidNumTodayNext) {
        this.paidNumTodayNext = paidNumTodayNext;
    }

    public Integer getPaidNumFinal() {
        return paidNumFinal;
    }

    public void setPaidNumFinal(Integer paidNumFinal) {
        this.paidNumFinal = paidNumFinal;
    }

    public Integer getPaidGoodsNumFinal() {
        return paidGoodsNumFinal;
    }

    public void setPaidGoodsNumFinal(Integer paidGoodsNumFinal) {
        this.paidGoodsNumFinal = paidGoodsNumFinal;
    }

    public Double getPaidAmountFinal() {
        return paidAmountFinal;
    }

    public void setPaidAmountFinal(Double paidAmountFinal) {
        this.paidAmountFinal = paidAmountFinal;
    }

    public Double getReceiveRate() {
        return receiveRate;
    }

    public void setReceiveRate(Double receiveRate) {
        this.receiveRate = receiveRate;
    }

    public Double getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(Double conversionRate) {
        this.conversionRate = conversionRate;
    }

    public Integer getOutStockOrderBuyerNumFinal() {
        return outStockOrderBuyerNumFinal;
    }

    public void setOutStockOrderBuyerNumFinal(Integer outStockOrderBuyerNumFinal) {
        this.outStockOrderBuyerNumFinal = outStockOrderBuyerNumFinal;
    }

    public Integer getOutStockOrderNumFinal() {
        return outStockOrderNumFinal;
    }

    public void setOutStockOrderNumFinal(Integer outStockOrderNumFinal) {
        this.outStockOrderNumFinal = outStockOrderNumFinal;
    }

    public Integer getOutStockOrderGoodsNumFinal() {
        return outStockOrderGoodsNumFinal;
    }

    public void setOutStockOrderGoodsNumFinal(Integer outStockOrderGoodsNumFinal) {
        this.outStockOrderGoodsNumFinal = outStockOrderGoodsNumFinal;
    }

    public Integer getOutStockOrderAmountFinal() {
        return outStockOrderAmountFinal;
    }

    public void setOutStockOrderAmountFinal(Integer outStockOrderAmountFinal) {
        this.outStockOrderAmountFinal = outStockOrderAmountFinal;
    }
}