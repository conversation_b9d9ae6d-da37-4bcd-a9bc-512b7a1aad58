package com.pes.jd.model.DTO;

import java.util.Date;

public class ReceiveSessionNumHourlyDTO {
    private Long id;

    private Long shopId;

    private String csNick;

    private Date date;

    private String dateStr;

    private Integer hour0;

    private Integer hour1;

    private Integer hour2;

    private Integer hour3;

    private Integer hour4;

    private Integer hour5;

    private Integer hour6;

    private Integer hour7;

    private Integer hour8;

    private Integer hour9;

    private Integer hour10;

    private Integer hour11;

    private Integer hour12;

    private Integer hour13;

    private Integer hour14;

    private Integer hour15;

    private Integer hour16;

    private Integer hour17;

    private Integer hour18;

    private Integer hour19;

    private Integer hour20;

    private Integer hour21;

    private Integer hour22;

    private Integer hour23;

    private Integer allDay;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Integer getHour0() {
        return hour0;
    }

    public void setHour0(Integer hour0) {
        this.hour0 = hour0;
    }

    public Integer getHour1() {
        return hour1;
    }

    public void setHour1(Integer hour1) {
        this.hour1 = hour1;
    }

    public Integer getHour2() {
        return hour2;
    }

    public void setHour2(Integer hour2) {
        this.hour2 = hour2;
    }

    public Integer getHour3() {
        return hour3;
    }

    public void setHour3(Integer hour3) {
        this.hour3 = hour3;
    }

    public Integer getHour4() {
        return hour4;
    }

    public void setHour4(Integer hour4) {
        this.hour4 = hour4;
    }

    public Integer getHour5() {
        return hour5;
    }

    public void setHour5(Integer hour5) {
        this.hour5 = hour5;
    }

    public Integer getHour6() {
        return hour6;
    }

    public void setHour6(Integer hour6) {
        this.hour6 = hour6;
    }

    public Integer getHour7() {
        return hour7;
    }

    public void setHour7(Integer hour7) {
        this.hour7 = hour7;
    }

    public Integer getHour8() {
        return hour8;
    }

    public void setHour8(Integer hour8) {
        this.hour8 = hour8;
    }

    public Integer getHour9() {
        return hour9;
    }

    public void setHour9(Integer hour9) {
        this.hour9 = hour9;
    }

    public Integer getHour10() {
        return hour10;
    }

    public void setHour10(Integer hour10) {
        this.hour10 = hour10;
    }

    public Integer getHour11() {
        return hour11;
    }

    public void setHour11(Integer hour11) {
        this.hour11 = hour11;
    }

    public Integer getHour12() {
        return hour12;
    }

    public void setHour12(Integer hour12) {
        this.hour12 = hour12;
    }

    public Integer getHour13() {
        return hour13;
    }

    public void setHour13(Integer hour13) {
        this.hour13 = hour13;
    }

    public Integer getHour14() {
        return hour14;
    }

    public void setHour14(Integer hour14) {
        this.hour14 = hour14;
    }

    public Integer getHour15() {
        return hour15;
    }

    public void setHour15(Integer hour15) {
        this.hour15 = hour15;
    }

    public Integer getHour16() {
        return hour16;
    }

    public void setHour16(Integer hour16) {
        this.hour16 = hour16;
    }

    public Integer getHour17() {
        return hour17;
    }

    public void setHour17(Integer hour17) {
        this.hour17 = hour17;
    }

    public Integer getHour18() {
        return hour18;
    }

    public void setHour18(Integer hour18) {
        this.hour18 = hour18;
    }

    public Integer getHour19() {
        return hour19;
    }

    public void setHour19(Integer hour19) {
        this.hour19 = hour19;
    }

    public Integer getHour20() {
        return hour20;
    }

    public void setHour20(Integer hour20) {
        this.hour20 = hour20;
    }

    public Integer getHour21() {
        return hour21;
    }

    public void setHour21(Integer hour21) {
        this.hour21 = hour21;
    }

    public Integer getHour22() {
        return hour22;
    }

    public void setHour22(Integer hour22) {
        this.hour22 = hour22;
    }

    public Integer getHour23() {
        return hour23;
    }

    public void setHour23(Integer hour23) {
        this.hour23 = hour23;
    }

    public Integer getAllDay() {
        return allDay;
    }

    public void setAllDay(Integer allDay) {
        this.allDay = allDay;
    }
}