package com.pes.jd.model.DTO;

import java.io.Serializable;
import java.util.Date;

public class CustConsultGoodsV2DTO implements Serializable{
	private static final long serialVersionUID = -3332775171591089896L;

	private Long id;

    private Long shopId;

    private Date date;

    private Long skuId;

    private String customer;

    private String csNick;

    private String result;
    
    private String skuName;
    
    private String csSimpleNick;
    
    private Long orderId;

    private String isEnquiry;
    
    

    public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getCsSimpleNick() {
		return csSimpleNick;
	}

	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}

	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer == null ? null : customer.trim();
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getIsEnquiry() {
        return isEnquiry;
    }

    public void setIsEnquiry(String isEnquiry) {
        this.isEnquiry = isEnquiry;
    }
}