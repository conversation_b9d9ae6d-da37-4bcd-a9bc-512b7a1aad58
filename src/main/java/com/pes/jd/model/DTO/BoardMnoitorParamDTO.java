package com.pes.jd.model.DTO;

import java.io.Serializable;

/**
 * @Author: aiJun
 * @Date: 2019-06-03 21:15
 * @Version 1.0
 */
public class BoardMnoitorParamDTO implements Serializable {


    private static final long serialVersionUID = 6023063856320285269L;
    private Long shopId;
    private String title;
    private String sessionKey;
    private String schemaId;
    private String db;
    private String rtSchemaId;
    private String rtDb;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey;
    }

    public String getSchemaId() {
        return schemaId;
    }

    public void setSchemaId(String schemaId) {
        this.schemaId = schemaId;
    }

    public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db;
    }

    public String getRtSchemaId() {
        return rtSchemaId;
    }

    public void setRtSchemaId(String rtSchemaId) {
        this.rtSchemaId = rtSchemaId;
    }

    public String getRtDb() {
        return rtDb;
    }

    public void setRtDb(String rtDb) {
        this.rtDb = rtDb;
    }

    @Override
    public String toString() {
        return "BoardMnoitorParamDTO{" +
                "shopId=" + shopId +
                ", title='" + title + '\'' +
                ", sessionKey='" + sessionKey + '\'' +
                ", schemaId='" + schemaId + '\'' +
                ", db='" + db + '\'' +
                ", rtSchemaId='" + rtSchemaId + '\'' +
                ", rtDb='" + rtDb + '\'' +
                '}';
    }
}
