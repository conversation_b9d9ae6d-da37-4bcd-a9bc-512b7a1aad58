package com.pes.jd.model.DTO;

import java.util.Date;

public class CsToOrderIndexDTO {
    private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private Integer toOrderedPaidNum;

    private Integer toOrderedPaidGoodsNum;

    private Integer toOrderedPaidOrderNum;

    private Double toOrderedPaidAmount;

    private Integer toOrderedNotPaidNum;

    private Integer toOrderedNotPaidGoodsNum;

    private Integer toOrderedNotPaidOrderNum;

    private Double toOrderedNotPaidAmount;

    private Integer toPaidNum;

    private Integer toPaidGoodsNum;

    private Integer toPaidOrderNum;

    private Double toPaidAmount;

    private Integer silentOrderToPaidNum;

    private Integer silentOrderToPaidGoodsNum;

    private Integer silentOrderToPaidOrderNum;

    private Double silentOrderToPaidAmount;

    private Integer silentToFolowupNum;

    private Integer silentToFolowupGoodsNum;

    private Integer silentToFolowupOrderNum;

    private Double silentToFolowupAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Integer getToOrderedPaidNum() {
        return toOrderedPaidNum;
    }

    public void setToOrderedPaidNum(Integer toOrderedPaidNum) {
        this.toOrderedPaidNum = toOrderedPaidNum;
    }

    public Integer getToOrderedPaidGoodsNum() {
        return toOrderedPaidGoodsNum;
    }

    public void setToOrderedPaidGoodsNum(Integer toOrderedPaidGoodsNum) {
        this.toOrderedPaidGoodsNum = toOrderedPaidGoodsNum;
    }

    public Integer getToOrderedPaidOrderNum() {
        return toOrderedPaidOrderNum;
    }

    public void setToOrderedPaidOrderNum(Integer toOrderedPaidOrderNum) {
        this.toOrderedPaidOrderNum = toOrderedPaidOrderNum;
    }

    public Double getToOrderedPaidAmount() {
        return toOrderedPaidAmount;
    }

    public void setToOrderedPaidAmount(Double toOrderedPaidAmount) {
        this.toOrderedPaidAmount = toOrderedPaidAmount;
    }

    public Integer getToOrderedNotPaidNum() {
        return toOrderedNotPaidNum;
    }

    public void setToOrderedNotPaidNum(Integer toOrderedNotPaidNum) {
        this.toOrderedNotPaidNum = toOrderedNotPaidNum;
    }

    public Integer getToOrderedNotPaidGoodsNum() {
        return toOrderedNotPaidGoodsNum;
    }

    public void setToOrderedNotPaidGoodsNum(Integer toOrderedNotPaidGoodsNum) {
        this.toOrderedNotPaidGoodsNum = toOrderedNotPaidGoodsNum;
    }

    public Integer getToOrderedNotPaidOrderNum() {
        return toOrderedNotPaidOrderNum;
    }

    public void setToOrderedNotPaidOrderNum(Integer toOrderedNotPaidOrderNum) {
        this.toOrderedNotPaidOrderNum = toOrderedNotPaidOrderNum;
    }

    public Double getToOrderedNotPaidAmount() {
        return toOrderedNotPaidAmount;
    }

    public void setToOrderedNotPaidAmount(Double toOrderedNotPaidAmount) {
        this.toOrderedNotPaidAmount = toOrderedNotPaidAmount;
    }

    public Integer getToPaidNum() {
        return toPaidNum;
    }

    public void setToPaidNum(Integer toPaidNum) {
        this.toPaidNum = toPaidNum;
    }

    public Integer getToPaidGoodsNum() {
        return toPaidGoodsNum;
    }

    public void setToPaidGoodsNum(Integer toPaidGoodsNum) {
        this.toPaidGoodsNum = toPaidGoodsNum;
    }

    public Integer getToPaidOrderNum() {
        return toPaidOrderNum;
    }

    public void setToPaidOrderNum(Integer toPaidOrderNum) {
        this.toPaidOrderNum = toPaidOrderNum;
    }

    public Double getToPaidAmount() {
        return toPaidAmount;
    }

    public void setToPaidAmount(Double toPaidAmount) {
        this.toPaidAmount = toPaidAmount;
    }

    public Integer getSilentOrderToPaidNum() {
        return silentOrderToPaidNum;
    }

    public void setSilentOrderToPaidNum(Integer silentOrderToPaidNum) {
        this.silentOrderToPaidNum = silentOrderToPaidNum;
    }

    public Integer getSilentOrderToPaidGoodsNum() {
        return silentOrderToPaidGoodsNum;
    }

    public void setSilentOrderToPaidGoodsNum(Integer silentOrderToPaidGoodsNum) {
        this.silentOrderToPaidGoodsNum = silentOrderToPaidGoodsNum;
    }

    public Integer getSilentOrderToPaidOrderNum() {
        return silentOrderToPaidOrderNum;
    }

    public void setSilentOrderToPaidOrderNum(Integer silentOrderToPaidOrderNum) {
        this.silentOrderToPaidOrderNum = silentOrderToPaidOrderNum;
    }

    public Double getSilentOrderToPaidAmount() {
        return silentOrderToPaidAmount;
    }

    public void setSilentOrderToPaidAmount(Double silentOrderToPaidAmount) {
        this.silentOrderToPaidAmount = silentOrderToPaidAmount;
    }

    public Integer getSilentToFolowupNum() {
        return silentToFolowupNum;
    }

    public void setSilentToFolowupNum(Integer silentToFolowupNum) {
        this.silentToFolowupNum = silentToFolowupNum;
    }

    public Integer getSilentToFolowupGoodsNum() {
        return silentToFolowupGoodsNum;
    }

    public void setSilentToFolowupGoodsNum(Integer silentToFolowupGoodsNum) {
        this.silentToFolowupGoodsNum = silentToFolowupGoodsNum;
    }

    public Integer getSilentToFolowupOrderNum() {
        return silentToFolowupOrderNum;
    }

    public void setSilentToFolowupOrderNum(Integer silentToFolowupOrderNum) {
        this.silentToFolowupOrderNum = silentToFolowupOrderNum;
    }

    public Double getSilentToFolowupAmount() {
        return silentToFolowupAmount;
    }

    public void setSilentToFolowupAmount(Double silentToFolowupAmount) {
        this.silentToFolowupAmount = silentToFolowupAmount;
    }
}