package com.pes.jd.model.DTO;

import lombok.Data;

import java.util.Date;

/**
 * @Author:acer
 * @Dcscription: 店铺绩效-预约商品
 * @Date: Created in 2020/3/12
 * @Modified By:
 */
@Data
public class ShopPerformancePreordainSumDTO {
    private Long id;
    private Long shopId;
    private Date date;
    private Long skuId;   //预约商品sku编号
    private String skuName; //预约商品sku名称
    private int orderedBuyerNum; //下单人数
    private int orderedSkuNum;  //下单件数
    private int orderedPaidBuyerNum; //付款人数
    private int orderedPaidSkuNum;  //付款件数
    private double orderedPaidAmount; //付款金额
    private String paidConversion; //付款转化
    private int consultBuyerNum;  //咨询人数：针对本商品咨询本客服的顾客数
    private int enquiryBuyerNum;  //询单  人数：咨询人数中，本客服接待的询单顾客数
    private String enquiryBuyerConversion;//、询单👉下单转化：下单人数/询单人数
    private String  enquiryPaidConversion; // 询单👉付款转化：付款人数/询单人数
    private int enquiryOrderedBuyerNum; //询单 下单人数
    private int enquiryOrderedSkuNum;  //询单 下单件数
    private int enquiryOrderedPaidBuyerNum; //询单 付款人数
    private int enquiryOrderedPaidSkuNum;  //询单 付款件数
    private double enquiryOrderedPaidAmount; //询单 付款金额

}
