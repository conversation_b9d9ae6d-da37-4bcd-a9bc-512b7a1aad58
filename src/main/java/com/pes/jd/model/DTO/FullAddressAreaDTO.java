package com.pes.jd.model.DTO;

import java.util.List;

public class FullAddressAreaDTO  {
    private String provinceId;

    private String provinceName;

    private String cityId;

    private String cityName;

    private String countryId;

    private String countryName;

    private String townId;

    private String townName;

    private String phoneCode;

    private String dataVersion;
    
    private Integer level;
    
    private List<FullAddressAreaDTO> cityList;

    
    
    
	public Integer getLevel() {
		return level;
	}


	public void setLevel(Integer level) {
		this.level = level;
	}


	public String getProvinceId() {
		return provinceId;
	}


	public void setProvinceId(String provinceId) {
		this.provinceId = provinceId;
	}


	public String getProvinceName() {
		return provinceName;
	}


	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}


	public String getCityId() {
		return cityId;
	}


	public void setCityId(String cityId) {
		this.cityId = cityId;
	}


	public String getCityName() {
		return cityName;
	}


	public void setCityName(String cityName) {
		this.cityName = cityName;
	}


	public String getCountryId() {
		return countryId;
	}


	public void setCountryId(String countryId) {
		this.countryId = countryId;
	}


	public String getCountryName() {
		return countryName;
	}


	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}


	public String getTownId() {
		return townId;
	}


	public void setTownId(String townId) {
		this.townId = townId;
	}


	public String getTownName() {
		return townName;
	}


	public void setTownName(String townName) {
		this.townName = townName;
	}


	public String getPhoneCode() {
		return phoneCode;
	}


	public void setPhoneCode(String phoneCode) {
		this.phoneCode = phoneCode;
	}


	public String getDataVersion() {
		return dataVersion;
	}


	public void setDataVersion(String dataVersion) {
		this.dataVersion = dataVersion;
	}


	public List<FullAddressAreaDTO> getCityList() {
		return cityList;
	}


	public void setCityList(List<FullAddressAreaDTO> cityList) {
		this.cityList = cityList;
	}
    
    
    
    
}
