package com.pes.jd.model.DTO;

import java.util.Date;

public class ShopOrderEvaluateDTO {

    private Long id;

    private Long shopId;

    private Date date;

    private String dateStr;

    private Integer goodEvaluateNum;

    private Integer neutralEvaluateNum;

    private Integer badEvaluateNum;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public Integer getGoodEvaluateNum() {
        return goodEvaluateNum;
    }

    public void setGoodEvaluateNum(Integer goodEvaluateNum) {
        this.goodEvaluateNum = goodEvaluateNum;
    }

    public Integer getNeutralEvaluateNum() {
        return neutralEvaluateNum;
    }

    public void setNeutralEvaluateNum(Integer neutralEvaluateNum) {
        this.neutralEvaluateNum = neutralEvaluateNum;
    }

    public Integer getBadEvaluateNum() {
        return badEvaluateNum;
    }

    public void setBadEvaluateNum(Integer badEvaluateNum) {
        this.badEvaluateNum = badEvaluateNum;
    }

    @Override
    public String toString() {
        return "ShopOrderEvaluateDTO{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", date=" + date +
                ", dateStr='" + dateStr + '\'' +
                ", goodEvaluateNum=" + goodEvaluateNum +
                ", neutralEvaluateNum=" + neutralEvaluateNum +
                ", badEvaluateNum=" + badEvaluateNum +
                '}';
    }
}
