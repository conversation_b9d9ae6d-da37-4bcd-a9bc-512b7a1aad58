package com.pes.jd.model.DTO;

import java.util.Date;

public class ShopRefundDayDTO {
    private Long id;

    private Long shopId;

    private Date date;

    private Integer applyRefundNum;

    private Integer applyRefundProductNum;

    private Integer applyRefundBuyerNum;

    private Double applyRefundAmount;

    private Integer completedRefundNum;

    private Integer completedRefundGoodsNum;

    private Integer completedRefundBuyerNum;

    private Double completedRefundAmount;

    private Long totalRefundDuration;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getApplyRefundNum() {
        return applyRefundNum;
    }

    public void setApplyRefundNum(Integer applyRefundNum) {
        this.applyRefundNum = applyRefundNum;
    }

    public Integer getApplyRefundProductNum() {
        return applyRefundProductNum;
    }

    public void setApplyRefundProductNum(Integer applyRefundProductNum) {
        this.applyRefundProductNum = applyRefundProductNum;
    }

    public Integer getApplyRefundBuyerNum() {
        return applyRefundBuyerNum;
    }

    public void setApplyRefundBuyerNum(Integer applyRefundBuyerNum) {
        this.applyRefundBuyerNum = applyRefundBuyerNum;
    }

    public Double getApplyRefundAmount() {
        return applyRefundAmount;
    }

    public void setApplyRefundAmount(Double applyRefundAmount) {
        this.applyRefundAmount = applyRefundAmount;
    }

    public Integer getCompletedRefundNum() {
        return completedRefundNum;
    }

    public void setCompletedRefundNum(Integer completedRefundNum) {
        this.completedRefundNum = completedRefundNum;
    }

    public Integer getCompletedRefundGoodsNum() {
        return completedRefundGoodsNum;
    }

    public void setCompletedRefundGoodsNum(Integer completedRefundGoodsNum) {
        this.completedRefundGoodsNum = completedRefundGoodsNum;
    }

    public Integer getCompletedRefundBuyerNum() {
        return completedRefundBuyerNum;
    }

    public void setCompletedRefundBuyerNum(Integer completedRefundBuyerNum) {
        this.completedRefundBuyerNum = completedRefundBuyerNum;
    }

    public Double getCompletedRefundAmount() {
        return completedRefundAmount;
    }

    public void setCompletedRefundAmount(Double completedRefundAmount) {
        this.completedRefundAmount = completedRefundAmount;
    }

    public Long getTotalRefundDuration() {
        return totalRefundDuration;
    }

    public void setTotalRefundDuration(Long totalRefundDuration) {
        this.totalRefundDuration = totalRefundDuration;
    }
}