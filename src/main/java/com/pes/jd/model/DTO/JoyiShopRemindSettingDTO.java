package com.pes.jd.model.DTO;

import com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO;
import com.pes.jd.model.DO.ShopSmsSettingDO;
import com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: JoyiShopRemindSettingDTO
 * @Author: aiJun
 * @Description: TODO
 * @date: 2020-03-01  14:35
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class JoyiShopRemindSettingDTO implements Serializable {
    private List<ShopSettingBatchRemindDTO> batchRemindSettingLst;
    private List<ShopSettingBatchRemindCnoDO> batchRemindCnoSettingLst;
    private List<ShopSmsSettingDO> smsSettingLst;
}
