package com.pes.jd.model.DTO;

import java.util.Date;

/**  
 * ClassName:ChatPeerDTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月12日 下午3:15:22 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ChatPeerDTO {
	private Long id;

    private Long shopId;

    private String csNick;

    private String buyerNick;

    private Date date;

    private Integer chatFlag;

    private Integer forwardFlag;

    private Integer forwardFilteFlag;

    private Boolean isWatchwordBuyer;

    private Boolean isFilteredBuyer;

    private Boolean isCsSingleChatFilter;

    private Boolean isCustSingleChatFilter;

    private Boolean isConsult;

    private Boolean isReceive;

    private Boolean isEnquiry;

    private Boolean isCsConsultFirst;

    private Boolean isPes;

    private Boolean isTeamPes;

    private Boolean isNextDayPes;

    private Boolean isAssist;

    private Boolean isAfterSale=false;

    private Boolean isOrderCreated;

    private Boolean isCsOfflineMsgFilter;

    private Boolean isMaAutoReplyFilter;

    private Integer chatNum;

    private Integer buyerChatNum;

    private Date firstChatDate;

    private Date lastChatDate;
    
    private Boolean crossChat;
    
    private Boolean isCrossChatFilter;

    private Boolean isCustLeaveMessageFilter;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick == null ? null : buyerNick.trim();
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

  

    public Integer getChatFlag() {
		return chatFlag;
	}

	public void setChatFlag(Integer chatFlag) {
		this.chatFlag = chatFlag;
	}

	public Integer getForwardFlag() {
		return forwardFlag;
	}

	public void setForwardFlag(Integer forwardFlag) {
		this.forwardFlag = forwardFlag;
	}

	public Integer getForwardFilteFlag() {
		return forwardFilteFlag;
	}

	public void setForwardFilteFlag(Integer forwardFilteFlag) {
		this.forwardFilteFlag = forwardFilteFlag;
	}

	public Boolean getIsWatchwordBuyer() {
        return isWatchwordBuyer;
    }

    public void setIsWatchwordBuyer(Boolean isWatchwordBuyer) {
        this.isWatchwordBuyer = isWatchwordBuyer;
    }

    public Boolean getIsFilteredBuyer() {
        return isFilteredBuyer;
    }

    public void setIsFilteredBuyer(Boolean isFilteredBuyer) {
        this.isFilteredBuyer = isFilteredBuyer;
    }

    public Boolean getIsCsSingleChatFilter() {
        return isCsSingleChatFilter;
    }

    public void setIsCsSingleChatFilter(Boolean isCsSingleChatFilter) {
        this.isCsSingleChatFilter = isCsSingleChatFilter;
    }

    public Boolean getIsCustSingleChatFilter() {
        return isCustSingleChatFilter;
    }

    public void setIsCustSingleChatFilter(Boolean isCustSingleChatFilter) {
        this.isCustSingleChatFilter = isCustSingleChatFilter;
    }

    public Boolean getIsConsult() {
        return isConsult;
    }

    public void setIsConsult(Boolean isConsult) {
        this.isConsult = isConsult;
    }

    public Boolean getIsReceive() {
        return isReceive;
    }

    public void setIsReceive(Boolean isReceive) {
        this.isReceive = isReceive;
    }

    public Boolean getIsEnquiry() {
        return isEnquiry;
    }

    public void setIsEnquiry(Boolean isEnquiry) {
        this.isEnquiry = isEnquiry;
    }

    public Boolean getIsCsConsultFirst() {
        return isCsConsultFirst;
    }

    public void setIsCsConsultFirst(Boolean isCsConsultFirst) {
        this.isCsConsultFirst = isCsConsultFirst;
    }

    public Boolean getIsPes() {
        return isPes;
    }

    public void setIsPes(Boolean isPes) {
        this.isPes = isPes;
    }

    public Boolean getIsTeamPes() {
        return isTeamPes;
    }

    public void setIsTeamPes(Boolean isTeamPes) {
        this.isTeamPes = isTeamPes;
    }

    public Boolean getIsNextDayPes() {
        return isNextDayPes;
    }

    public void setIsNextDayPes(Boolean isNextDayPes) {
        this.isNextDayPes = isNextDayPes;
    }

    public Boolean getIsAssist() {
        return isAssist;
    }

    public void setIsAssist(Boolean isAssist) {
        this.isAssist = isAssist;
    }

    public Boolean getIsAfterSale() {
        return isAfterSale;
    }

    public void setIsAfterSale(Boolean isAfterSale) {
        this.isAfterSale = isAfterSale;
    }

    public Boolean getIsOrderCreated() {
        return isOrderCreated;
    }

    public void setIsOrderCreated(Boolean isOrderCreated) {
        this.isOrderCreated = isOrderCreated;
    }

    public Boolean getIsCsOfflineMsgFilter() {
        return isCsOfflineMsgFilter;
    }

    public void setIsCsOfflineMsgFilter(Boolean isCsOfflineMsgFilter) {
        this.isCsOfflineMsgFilter = isCsOfflineMsgFilter;
    }

    public Boolean getIsMaAutoReplyFilter() {
        return isMaAutoReplyFilter;
    }

    public void setIsMaAutoReplyFilter(Boolean isMaAutoReplyFilter) {
        this.isMaAutoReplyFilter = isMaAutoReplyFilter;
    }

    public Integer getChatNum() {
        return chatNum;
    }

    public void setChatNum(Integer chatNum) {
        this.chatNum = chatNum;
    }

    public Integer getBuyerChatNum() {
        return buyerChatNum;
    }

    public void setBuyerChatNum(Integer buyerChatNum) {
        this.buyerChatNum = buyerChatNum;
    }

    public Date getFirstChatDate() {
        return firstChatDate;
    }

    public void setFirstChatDate(Date firstChatDate) {
        this.firstChatDate = firstChatDate;
    }

    public Date getLastChatDate() {
        return lastChatDate;
    }

    public void setLastChatDate(Date lastChatDate) {
        this.lastChatDate = lastChatDate;
    }

	

	public Boolean getCrossChat() {
		return crossChat;
	}

	public void setCrossChat(Boolean crossChat) {
		this.crossChat = crossChat;
	}

	public Boolean getIsCrossChatFilter() {
		return isCrossChatFilter;
	}

	public void setIsCrossChatFilter(Boolean isCrossChatFilter) {
		this.isCrossChatFilter = isCrossChatFilter;
	}

    public Boolean getCustLeaveMessageFilter() {
        return isCustLeaveMessageFilter;
    }

    public void setCustLeaveMessageFilter(Boolean custLeaveMessageFilter) {
        isCustLeaveMessageFilter = custLeaveMessageFilter;
    }
}
  
