package com.pes.jd.model.DTO;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

import java.sql.Timestamp;
import java.util.Date;

public class CsOrderIndexDTO {
	private Long id;
	
	private Long shopId;
	
	private Date date;
	
	private String csNick;
	
	private String buyerNick;
	
	private String receType;//接待类型
	
	
	
	public String getReceType() {
		return receType;
	}

	public void setReceType(String receType) {
		this.receType = receType;
	}

	@JSONField(serializeUsing=ToStringSerializer.class)
	private Long orderId;
	
	private String assistType;
	
	private Timestamp orderPayDate;
	
	private Timestamp orderCreated;
	
	public Timestamp getOrderCreated() {
		return orderCreated;
	}

	public void setOrderCreated(Timestamp orderCreated) {
		this.orderCreated = orderCreated;
	}

	private Double orderPayment;
	
	private String csSimpleNick;
	
	private Date outStockTime;
	 
	private Timestamp firstChatDate;

	public Timestamp getFirstChatDate() {
		return firstChatDate;
	}

	public void setFirstChatDate(Timestamp firstChatDate) {
		this.firstChatDate = firstChatDate;
	}

	public Date getOutStockTime() {
		return outStockTime;
	}

	public void setOutStockTime(Date outStockTime) {
		this.outStockTime = outStockTime;
	}

	public String getCsSimpleNick() {
		return csSimpleNick;
	}

	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}


	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getAssistType() {
		return assistType;
	}

	public void setAssistType(String assistType) {
		this.assistType = assistType;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public Timestamp getOrderPayDate() {
		return orderPayDate;
	}

	public void setOrderPayDate(Timestamp orderPayDate) {
		this.orderPayDate = orderPayDate;
	}

	public Double getOrderPayment() {
		return orderPayment;
	}

	public void setOrderPayment(Double orderPayment) {
		this.orderPayment = orderPayment;
	}

	
	
	
	
	
}
