package com.pes.jd.model.DTO;


/**
 * <AUTHOR>
 * @version 2019-06-02 18:27
 */

public class OrderShipDetailDTO  {

    private Integer lastitemflag;

    private Long processDate;
    
    private String processInfo;
    
    private String shipId;
    
    private Integer thirdId;
    
    private String traceDate;

	public Integer getLastitemflag() {
		return lastitemflag;
	}

	public void setLastitemflag(Integer lastitemflag) {
		this.lastitemflag = lastitemflag;
	}

	public Long getProcessDate() {
		return processDate;
	}

	public void setProcessDate(Long processDate) {
		this.processDate = processDate;
	}

	public String getProcessInfo() {
		return processInfo;
	}

	public void setProcessInfo(String processInfo) {
		this.processInfo = processInfo;
	}

	public String getShipId() {
		return shipId;
	}

	public void setShipId(String shipId) {
		this.shipId = shipId;
	}

	public Integer getThirdId() {
		return thirdId;
	}

	public void setThirdId(Integer thirdId) {
		this.thirdId = thirdId;
	}

	public String getTraceDate() {
		return traceDate;
	}

	public void setTraceDate(String traceDate) {
		this.traceDate = traceDate;
	}
    
    
    
}
