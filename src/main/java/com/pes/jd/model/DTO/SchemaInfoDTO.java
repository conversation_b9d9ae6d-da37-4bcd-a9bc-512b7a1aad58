  
package com.pes.jd.model.DTO;

import java.io.Serializable;

/**  
 * ClassName:SchemaInfoDTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月26日 下午5:02:16 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class SchemaInfoDTO implements Serializable{
	private static final long serialVersionUID = 6432516414780267721L;
	private String schemaId;
	private String dbName;
	private int shopNum;
	public String getSchemaId() {
		return schemaId;
	}
	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}
	public int getShopNum() {
		return shopNum;
	}
	public void setShopNum(int shopNum) {
		this.shopNum = shopNum;
	}
	public String getDbName() {
		return dbName;
	}
	public void setDbName(String dbName) {
		this.dbName = dbName;
	}
	
}
  
