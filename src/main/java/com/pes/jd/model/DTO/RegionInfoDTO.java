package com.pes.jd.model.DTO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class RegionInfoDTO {

    /**
     * 区域ID
     */
    private Long areaId;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 层级（1-省，2-市，3-区县）
     */
    private Integer level;

    /**
     * 人数统计
     */
    private Integer userCount;

    /**
     * 子区域列表
     */
    private List<RegionInfoDTO> children;
}
