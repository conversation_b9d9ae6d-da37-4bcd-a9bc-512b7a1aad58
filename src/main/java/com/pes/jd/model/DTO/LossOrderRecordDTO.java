package com.pes.jd.model.DTO;

import java.util.Date;

/**  
 * ClassName:LossOrderRecordDTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class LossOrderRecordDTO {
	
	private Long id;
	private Long shopId;
	private String csNick;
	private String customer;
	private Long orderId;
	private Date orderCreated;
	private Double orderPayment;
	private Integer orderGoodsNum;
	private Boolean isChatAfterOrdered;
	private Date date;
	private Integer status;
	private Date startDateTime;
	private Date endDateTime;
	private Integer type;
	private Integer chatType;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public String getCustomer() {
		return customer;
	}
	public void setCustomer(String customer) {
		this.customer = customer;
	}
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Date getOrderCreated() {
		return orderCreated;
	}
	public void setOrderCreated(Date orderCreated) {
		this.orderCreated = orderCreated;
	}
	public Double getOrderPayment() {
		return orderPayment;
	}
	public void setOrderPayment(Double orderPayment) {
		this.orderPayment = orderPayment;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public Date getStartDateTime() {
		return startDateTime;
	}
	public void setStartDateTime(Date startDateTime) {
		this.startDateTime = startDateTime;
	}
	public Date getEndDateTime() {
		return endDateTime;
	}
	public void setEndDateTime(Date endDateTime) {
		this.endDateTime = endDateTime;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public Integer getOrderGoodsNum() {
		return orderGoodsNum;
	}
	public void setOrderGoodsNum(Integer orderGoodsNum) {
		this.orderGoodsNum = orderGoodsNum;
	}
	public Boolean getIsChatAfterOrdered() {
		return isChatAfterOrdered;
	}
	public void setIsChatAfterOrdered(Boolean isChatAfterOrdered) {
		this.isChatAfterOrdered = isChatAfterOrdered;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Integer getChatType() {
		return chatType;
	}
	public void setChatType(Integer chatType) {
		this.chatType = chatType;
	}
	
}
  
