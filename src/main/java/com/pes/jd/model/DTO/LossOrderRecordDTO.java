package com.pes.jd.model.DTO;

import java.util.Date;

public class LossOrderRecordDTO {
	
	private Long id;
	private Long shopId;
	private String csNick;
	private String customer;
	private Long orderId;
	private Date orderCreated;
	private Double orderPayment;
	private Integer orderGoodsNum;
	private Boolean isChatAfterOrdered;
	private Date date;
	private Date startDateTime;
	private Date endDateTime;
	private Integer orderChatType;//1:非主动聊天，2：主动聊天
	private Integer type;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public String getCustomer() {
		return customer;
	}
	public void setCustomer(String customer) {
		this.customer = customer;
	}
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Date getOrderCreated() {
		return orderCreated;
	}
	public void setOrderCreated(Date orderCreated) {
		this.orderCreated = orderCreated;
	}
	public Double getOrderPayment() {
		return orderPayment;
	}
	public void setOrderPayment(Double orderPayment) {
		this.orderPayment = orderPayment;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public Date getStartDateTime() {
		return startDateTime;
	}
	public void setStartDateTime(Date startDateTime) {
		this.startDateTime = startDateTime;
	}
	public Date getEndDateTime() {
		return endDateTime;
	}
	public void setEndDateTime(Date endDateTime) {
		this.endDateTime = endDateTime;
	}
	public Integer getOrderGoodsNum() {
		return orderGoodsNum;
	}
	public void setOrderGoodsNum(Integer orderGoodsNum) {
		this.orderGoodsNum = orderGoodsNum;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	public Boolean getIsChatAfterOrdered() {
		return isChatAfterOrdered;
	}
	public void setIsChatAfterOrdered(Boolean isChatAfterOrdered) {
		this.isChatAfterOrdered = isChatAfterOrdered;
	}
	public Integer getOrderChatType() {
		return orderChatType;
	}
	public void setOrderChatType(Integer orderChatType) {
		this.orderChatType = orderChatType;
	}
}
  
