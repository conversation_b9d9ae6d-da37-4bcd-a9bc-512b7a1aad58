package com.pes.jd.model.DTO;

import java.util.Date;

public class CsLossRecordDTO {
    private Long id;

    private String csNick;

    private Long shopId;

    private Date date;

    private Integer orderNum;

    private Integer customerNum;

    private Integer orderGoodsNum;

    private Double orderSaleAmount;

    private Byte type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getCustomerNum() {
        return customerNum;
    }

    public void setCustomerNum(Integer customerNum) {
        this.customerNum = customerNum;
    }

    public Integer getOrderGoodsNum() {
        return orderGoodsNum;
    }

    public void setOrderGoodsNum(Integer orderGoodsNum) {
        this.orderGoodsNum = orderGoodsNum;
    }

    public Double getOrderSaleAmount() {
        return orderSaleAmount;
    }

    public void setOrderSaleAmount(Double orderSaleAmount) {
        this.orderSaleAmount = orderSaleAmount;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "CsLossRecordDTO{" +
                "id=" + id +
                ", csNick='" + csNick + '\'' +
                ", shopId=" + shopId +
                ", date=" + date +
                ", orderNum=" + orderNum +
                ", customerNum=" + customerNum +
                ", orderGoodsNum=" + orderGoodsNum +
                ", orderSaleAmount=" + orderSaleAmount +
                ", type=" + type +
                '}';
    }
}