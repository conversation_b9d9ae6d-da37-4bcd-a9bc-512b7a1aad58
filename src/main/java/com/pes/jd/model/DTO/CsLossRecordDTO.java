package com.pes.jd.model.DTO;

import java.util.Date;

public class CsLossRecordDTO {
	
	private Long id; 
	private String csNick; 
	private Long shopId; 
	private Date date; 
	private Integer orderNum;//流失订单数
	private Integer customerNum; //流失买家数
	private Integer orderGoodsNum; //流失件数
	private Double orderSaleAmount;//流失金额
	private Integer type; //0：无 1：询单流失 2：付款流失 3：出库流失
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public Integer getOrderNum() {
		return orderNum;
	}
	public void setOrderNum(Integer orderNum) {
		this.orderNum = orderNum;
	}
	public Integer getCustomerNum() {
		return customerNum;
	}
	public void setCustomerNum(Integer customerNum) {
		this.customerNum = customerNum;
	}
	public Integer getOrderGoodsNum() {
		return orderGoodsNum;
	}
	public void setOrderGoodsNum(Integer orderGoodsNum) {
		this.orderGoodsNum = orderGoodsNum;
	}
	public Double getOrderSaleAmount() {
		return orderSaleAmount;
	}
	public void setOrderSaleAmount(Double orderSaleAmount) {
		this.orderSaleAmount = orderSaleAmount;
	}
	public Integer getType() {
		return type;
	}
	public void setType(Integer type) {
		this.type = type;
	}

}
  
