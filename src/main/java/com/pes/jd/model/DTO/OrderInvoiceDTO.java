package com.pes.jd.model.DTO;

import java.util.Date;

public class OrderInvoiceDTO {
    private Long id;

    private Long orderId;

    private Byte invoiceType;

    private String title;

    private String contentId;

    private String consigneeEmail;

    private String consigneePhone;

    private String invoiceCode;

    private String vatNo;

    private String vatAddressRegistered;

    private String vatPhoneRegistered;

    private String vatDepositBank;

    private String vatBankAccount;

    private String vatUserAddress;

    private String vatUserName;

    private String vatUserPhone;

    private Date createdTime;

    private Date modifiedTime;
    
    private String invoiceInfo;

    private String invoiceContentId;
    
    
    

    public String getInvoiceInfo() {
		return invoiceInfo;
	}

	public void setInvoiceInfo(String invoiceInfo) {
		this.invoiceInfo = invoiceInfo;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Byte getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Byte invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId == null ? null : contentId.trim();
    }

    public String getConsigneeEmail() {
        return consigneeEmail;
    }

    public void setConsigneeEmail(String consigneeEmail) {
        this.consigneeEmail = consigneeEmail == null ? null : consigneeEmail.trim();
    }

    public String getConsigneePhone() {
        return consigneePhone;
    }

    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone == null ? null : consigneePhone.trim();
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode == null ? null : invoiceCode.trim();
    }

    public String getVatNo() {
        return vatNo;
    }

    public void setVatNo(String vatNo) {
        this.vatNo = vatNo == null ? null : vatNo.trim();
    }

    public String getVatAddressRegistered() {
        return vatAddressRegistered;
    }

    public void setVatAddressRegistered(String vatAddressRegistered) {
        this.vatAddressRegistered = vatAddressRegistered == null ? null : vatAddressRegistered.trim();
    }

    public String getVatPhoneRegistered() {
        return vatPhoneRegistered;
    }

    public void setVatPhoneRegistered(String vatPhoneRegistered) {
        this.vatPhoneRegistered = vatPhoneRegistered == null ? null : vatPhoneRegistered.trim();
    }

    public String getVatDepositBank() {
        return vatDepositBank;
    }

    public void setVatDepositBank(String vatDepositBank) {
        this.vatDepositBank = vatDepositBank == null ? null : vatDepositBank.trim();
    }

    public String getVatBankAccount() {
        return vatBankAccount;
    }

    public void setVatBankAccount(String vatBankAccount) {
        this.vatBankAccount = vatBankAccount == null ? null : vatBankAccount.trim();
    }

    public String getVatUserAddress() {
        return vatUserAddress;
    }

    public void setVatUserAddress(String vatUserAddress) {
        this.vatUserAddress = vatUserAddress == null ? null : vatUserAddress.trim();
    }

    public String getVatUserName() {
        return vatUserName;
    }

    public void setVatUserName(String vatUserName) {
        this.vatUserName = vatUserName == null ? null : vatUserName.trim();
    }

    public String getVatUserPhone() {
        return vatUserPhone;
    }

    public void setVatUserPhone(String vatUserPhone) {
        this.vatUserPhone = vatUserPhone == null ? null : vatUserPhone.trim();
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(Date modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    public String getInvoiceContentId() {
        return invoiceContentId;
    }

    public void setInvoiceContentId(String invoiceContentId) {
        this.invoiceContentId = invoiceContentId;
    }
}