package com.pes.jd.model.DTO;

import lombok.Data;

import java.util.Date;

@Data
public class ShopAutoAdvanceAllocatedSettingDTO {
    private Long id;

    private Long shopId;

    private Date created;

    private Date modify;

    private Integer status;

    private Boolean isAutoAllocated;

    private Integer cnoNotOrderFlag;

    private Long cnoNotOrderGroupId;

    private String cnoNotOrderCsNick;

    private String cnoNotOrderSpareCsNick;

    private Long cnoNotOrderSpareGroupId;

    private Integer cnoNotPayEarnestFlag;

    private Long cnoNotPayEarnestGroupId;

    private String cnoNotPayEarnestCsNick;

    private String cnoNotPayEarnestSpareCsNick;

    private Long cnoNotPayEarnestSpareGroupId;

    private Integer snpNotPayEarnestFlag;

    private Long snpNotPayEarnestGroupId;

    private String snpNotPayEarnestCsNick;

    private String snpNotPayEarnestSpareCsNick;

    private Long snpNotPayEarnestSpareGroupId;

    private Integer cnoNotPayTailFlag;

    private Long cnoNotPayTailGroupId;

    private String cnoNotPayTailCsNick;

    private String cnoNotPayTailSpareCsNick;

    private Long cnoNotPayTailSpareGroupId;

    private Integer snpNotPayTailFlag;

    private Long snpNotPayTailGroupId;

    private String snpNotPayTailCsNick;

    private String snpNotPayTailSpareCsNick;

    private Long snpNotPayTailSpareGroupId;


}