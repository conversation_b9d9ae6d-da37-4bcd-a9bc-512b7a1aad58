package com.pes.jd.model.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pes.jd.model.DO.ShopInfoDO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**  
 * ClassName:ShopBO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月26日 下午4:40:02 <br/>  
 * <AUTHOR> 
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShopDTO implements Serializable {

	/**
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).
	 * 
	 * @since JDK 1.8
	 */
	private static final long serialVersionUID = 1L;
	private Long id;

	private Long shopId;

	private Long userId;

	private String sellerNick;

	private String title;

	private String sessionKey;

	private String status;

	private Integer subuserNum;

	private Boolean realtimeSwitch;

	private Integer fetchFlag;

	private Integer initDataFlag;

	private Date previousGetDataTime;

	private Date preFetchRealtime;
	
	private Date subscribeDeadLine;

	private Long lastConsumedTime;
	
	private String shopGroupMemberCs;

	private String schemaId;

	private String db;

	private Long venderId;
	private Integer createTableFlag;// 是否创建表
	
	private Integer isAccount = 1;// 是否是主账号 0是，1否
	
	private Long defaultGroupId; // 默认分组ID
	
	private List<GroupDTO> groupLst;
	
	private Integer colType;
	private String rtSchemaId;
	private String rtDb;
	private Integer type; //0:pop 1:自营

	private Date authDeadLine;//授权过期时间

	private String refreskSessionKey;

	private String optionSessionKey;

	private Boolean initHisOdFlag = false;
	private String hisOdStartDateStr;
	private String hisOdEndDateStr;
	private String hisSubDeadLine;
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public ShopDTO() {
		  
		super();  
		// TODO Auto-generated constructor stub  
		
	}

	public ShopDTO(ShopInfoDO shop) {
		this.shopId = shop.getShopId();
		this.title = shop.getShopName();
		this.userId=shop.getVenderId();
	}

	public ShopDTO(Long shopId, String schemaId) {
		super();
		this.shopId = shopId;
		this.schemaId = schemaId;
	}


	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getSellerNick() {
		return sellerNick;
	}

	public void setSellerNick(String sellerNick) {
		this.sellerNick = sellerNick;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getSessionKey() {
		return sessionKey;
	}

	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getSubuserNum() {
		return subuserNum;
	}

	public void setSubuserNum(Integer subuserNum) {
		this.subuserNum = subuserNum;
	}

	public Boolean getRealtimeSwitch() {
		return realtimeSwitch;
	}

	public void setRealtimeSwitch(Boolean realtimeSwitch) {
		this.realtimeSwitch = realtimeSwitch;
	}

	public Integer getFetchFlag() {
		return fetchFlag;
	}

	public void setFetchFlag(Integer fetchFlag) {
		this.fetchFlag = fetchFlag;
	}

	public Integer getInitDataFlag() {
		return initDataFlag;
	}

	public void setInitDataFlag(Integer initDataFlag) {
		this.initDataFlag = initDataFlag;
	}

	public Date getPreviousGetDataTime() {
		return previousGetDataTime;
	}

	public void setPreviousGetDataTime(Date previousGetDataTime) {
		this.previousGetDataTime = previousGetDataTime;
	}

	public Date getPreFetchRealtime() {
		return preFetchRealtime;
	}

	public void setPreFetchRealtime(Date preFetchRealtime) {
		this.preFetchRealtime = preFetchRealtime;
	}

	public Long getLastConsumedTime() {
		return lastConsumedTime;
	}

	public void setLastConsumedTime(Long lastConsumedTime) {
		this.lastConsumedTime = lastConsumedTime;
	}

	

	public String getSchemaId() {
		return schemaId;
	}

	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}

	public String getDb() {
		return db;
	}

	public void setDb(String db) {
		this.db = db;
	}

	public Integer getCreateTableFlag() {
		return createTableFlag;
	}

	public void setCreateTableFlag(Integer createTableFlag) {
		this.createTableFlag = createTableFlag;
	}

	public Integer getIsAccount() {
		return isAccount;
	}

	public void setIsAccount(Integer isAccount) {
		this.isAccount = isAccount;
	}

	public Long getDefaultGroupId() {
		return defaultGroupId;
	}

	public void setDefaultGroupId(Long defaultGroupId) {
		this.defaultGroupId = defaultGroupId;
	}

	public List<GroupDTO> getGroupLst() {
		return groupLst;
	}

	public void setGroupLst(List<GroupDTO> groupLst) {
		this.groupLst = groupLst;
	}

	public Integer getColType() {
		return colType;
	}

	public void setColType(Integer colType) {
		this.colType = colType;
	}

	public Long getVenderId() {
		return venderId;
	}

	public void setVenderId(Long venderId) {
		this.venderId = venderId;
	}

	public String getShopGroupMemberCs() {
		return shopGroupMemberCs;
	}

	public void setShopGroupMemberCs(String shopGroupMemberCs) {
		this.shopGroupMemberCs = shopGroupMemberCs;
	}

	public Date getSubscribeDeadLine() {
		return subscribeDeadLine;
	}

	public void setSubscribeDeadLine(Date subscribeDeadLine) {
		this.subscribeDeadLine = subscribeDeadLine;
	}

	public String getRtSchemaId() {
		return rtSchemaId;
	}

	public void setRtSchemaId(String rtSchemaId) {
		this.rtSchemaId = rtSchemaId;
	}

	public String getRtDb() {
		return rtDb;
	}

	public void setRtDb(String rtDb) {
		this.rtDb = rtDb;
	}

	public Date getAuthDeadLine() {
		return authDeadLine;
	}

	public void setAuthDeadLine(Date authDeadLine) {
		this.authDeadLine = authDeadLine;
	}

	public String getRefreskSessionKey() {
		return refreskSessionKey;
	}

	public void setRefreskSessionKey(String refreskSessionKey) {
		this.refreskSessionKey = refreskSessionKey;
	}

	public String getOptionSessionKey() {
		return optionSessionKey;
	}

	public void setOptionSessionKey(String optionSessionKey) {
		this.optionSessionKey = optionSessionKey;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Boolean getInitHisOdFlag() {
		return initHisOdFlag;
	}

	public void setInitHisOdFlag(Boolean initHisOdFlag) {
		this.initHisOdFlag = initHisOdFlag;
	}

	public String getHisOdStartDateStr() {
		return hisOdStartDateStr;
	}

	public void setHisOdStartDateStr(String hisOdStartDateStr) {
		this.hisOdStartDateStr = hisOdStartDateStr;
	}

	public String getHisOdEndDateStr() {
		return hisOdEndDateStr;
	}

	public void setHisOdEndDateStr(String hisOdEndDateStr) {
		this.hisOdEndDateStr = hisOdEndDateStr;
	}

	public String getHisSubDeadLine() {
		return hisSubDeadLine;
	}

	public void setHisSubDeadLine(String hisSubDeadLine) {
		this.hisSubDeadLine = hisSubDeadLine;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (!(o instanceof ShopDTO)) return false;
		ShopDTO shopDTO = (ShopDTO) o;
		return Objects.equals(shopId, shopDTO.shopId) &&
				Objects.equals(userId, shopDTO.userId) &&
				Objects.equals(sellerNick, shopDTO.sellerNick) &&
				Objects.equals(title, shopDTO.title) &&
				Objects.equals(sessionKey, shopDTO.sessionKey) &&
				Objects.equals(status, shopDTO.status) &&
				Objects.equals(subuserNum, shopDTO.subuserNum) &&
				Objects.equals(realtimeSwitch, shopDTO.realtimeSwitch) &&
				Objects.equals(fetchFlag, shopDTO.fetchFlag) &&
				Objects.equals(initDataFlag, shopDTO.initDataFlag) &&
				Objects.equals(previousGetDataTime, shopDTO.previousGetDataTime) &&
				Objects.equals(preFetchRealtime, shopDTO.preFetchRealtime) &&
				Objects.equals(subscribeDeadLine, shopDTO.subscribeDeadLine) &&
				Objects.equals(lastConsumedTime, shopDTO.lastConsumedTime) &&
				Objects.equals(shopGroupMemberCs, shopDTO.shopGroupMemberCs) &&
				Objects.equals(schemaId, shopDTO.schemaId) &&
				Objects.equals(db, shopDTO.db) &&
				Objects.equals(venderId, shopDTO.venderId) &&
				Objects.equals(createTableFlag, shopDTO.createTableFlag) &&
				Objects.equals(isAccount, shopDTO.isAccount) &&
				Objects.equals(defaultGroupId, shopDTO.defaultGroupId) &&
				Objects.equals(groupLst, shopDTO.groupLst) &&
				Objects.equals(colType, shopDTO.colType) &&
				Objects.equals(rtSchemaId, shopDTO.rtSchemaId) &&
				Objects.equals(rtDb, shopDTO.rtDb);
	}

	@Override
	public int hashCode() {
		return Objects.hash(shopId, userId, sellerNick, title, sessionKey, status, subuserNum, realtimeSwitch, fetchFlag, initDataFlag, previousGetDataTime, preFetchRealtime, subscribeDeadLine, lastConsumedTime, shopGroupMemberCs, schemaId, db, venderId, createTableFlag, isAccount, defaultGroupId, groupLst, colType, rtSchemaId, rtDb);
	}

	@Override
	public String toString() {
		return "shopId=" + shopId + "sellerNick=" + sellerNick + "title=" + title + "sessionKey=" + sessionKey + "status=" + status
				+ "schemaId=" + schemaId;
	}

}
  
