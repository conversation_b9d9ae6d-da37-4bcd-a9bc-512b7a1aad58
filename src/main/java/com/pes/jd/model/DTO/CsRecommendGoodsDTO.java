package com.pes.jd.model.DTO;

import java.io.Serializable;
import java.util.Date;

public class CsRecommendGoodsDTO implements Serializable{
	private static final long serialVersionUID = -8871034129324670850L;

	private Long id;

    private Long shopId;

    private Date date;

    private Long skuId;

    private String customer;

    private String skuName;
    
    private String csNick;

    private Integer result;

    private Double purchasesAmount;
    
    private Integer purchasesGoodsNum;
    
    private String csSimpleNick;
    
	public CsRecommendGoodsDTO() {
		super();
	}

	
	public CsRecommendGoodsDTO(Double purchasesAmount, Integer purchasesGoodsNum) {
		super();
		this.purchasesAmount = purchasesAmount;
		this.purchasesGoodsNum = purchasesGoodsNum;
	}


	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer == null ? null : customer.trim();
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	public Double getPurchasesAmount() {
		return purchasesAmount;
	}

	public void setPurchasesAmount(Double purchasesAmount) {
		this.purchasesAmount = purchasesAmount;
	}

	public Integer getPurchasesGoodsNum() {
		return purchasesGoodsNum;
	}

	public void setPurchasesGoodsNum(Integer purchasesGoodsNum) {
		this.purchasesGoodsNum = purchasesGoodsNum;
	}


	public String getCsSimpleNick() {
		return csSimpleNick;
	}


	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}
    
	
}