package com.pes.jd.model.DTO;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MarketingActivityV2RespDTO {
    private Long id;
    @NotBlank(message = "活动名称不能为空")
    private String activityName; // 活动名称
    @NotNull(message = "活动内容不能为空")
    private String activityContent; // 活动内容
    @NotNull(message = "开始时间不能为空")
    private String startDate; // 开始时间
    @NotNull(message = "结束时间不能为空")
    private String endDate; // 结束时间
    @NotNull(message = "订购类型不能为空")
    private Integer orderType; // 订购类型 0：全部 1：试用 2：订购 3：续费
    @NotNull(message = "到期时间不能为空")
    private Integer effectDate; // 到期时间:1天/3天/7天/15天/30天/60天/180天/180天以上
    @NotNull(message = "弹出频率不能为空")
    private Integer frequency; // 打开即弹/1天/3天/7天/15天/30天
    @NotNull(message = "订购版本不能为空")
    private String version; // 订购版本
    @NotNull(message = "跳转链接不能为空")
    private String url; // 相关活动的url
    @NotNull(message = "启用开关不能为空")
    private Boolean enableSwitch; // 启用开关
    private Integer activitySort;
    private String created;

}