package com.pes.jd.model.DTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import java.util.Map;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPortraitStatisticsDTO {

    /**
     * 年龄分布数据
     */
    private List<PieChartData> ageDistribution;

    /**
     * 性别分布数据
     */
    private List<PieChartData> genderDistribution;

    /**
     * 婚姻状况分布数据
     */
    private List<PieChartData> marriageDistribution;

    /**
     * 职业分布数据
     */
    private List<BarChartData> professionDistribution;

    /**
     * 用户群体类型分布数据
     */
    private List<BarChartData> userGroupDistribution;

    /**
     * 孩子数量分布数据
     */
    private List<PieChartData> childrenDistribution;

    /**
     * 地区分布数据
     */
    private Map<String, Object> regionDistribution;

    /**
     * 大促预售购买敏感人群占比
     */
    private List<PieChartData> presaleDistribution;

    /**
     * 平台促销敏感人群占比
     */
    private List<PieChartData> promotionSensitivityDistribution;

    /**
     * 大促高消费金额人群
     */
    private List<PieChartData> highConsumptionDistribution;

    /**
     * 新品偏好人群占比
     */
    private List<PieChartData> newProductPreferenceDistribution;

    /**
     * 饼图数据结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PieChartData {
        private String name;
        private Integer value;
        private Double percentage;
    }

    /**
     * 柱状图数据结构
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BarChartData {
        private String name;
        private Integer value;
        private Double percentage;
    }
}
