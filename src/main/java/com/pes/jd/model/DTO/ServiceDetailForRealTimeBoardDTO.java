package com.pes.jd.model.DTO;

public class ServiceDetailForRealTimeBoardDTO {
	
	private long shopId;
	
	private String shopName;//店铺名称

	private int receiveSessionNum;//接待量
	
	private int nonReplySessionNum;//未回复量
	
	private double avgResTimeFirst;//首次平均响应
	
	private double avgResTime;//平均响应
	
	private int avgRespInQuickTime;//平均响应时间在快速应答时间中的会话数
	
	private double replyRate;//回复率
	
	private double satisRate;//满意率
	
	private double quickResRate;//快速应答率
	
	private int satisNum;
	
	private int evalNum;

	private int nonReplySessionNumSize = 0;
	private int avgResTimeSize = 0;
	private int avgResTimeFirstSize = 0;

    private int consultNum;// 咨询量

    private int receiveNum;// 接待量

    private double avgSessionDurationTime;// 平均会话时长(m)

    //留言接待量
    private int leaveMsgReceiveSessionNum; //平均响应时间在快速应答时间中的会话数

    private int forwardOutSessionNum;//转出

    private int leaveMsgSessionNum;//留言分配
	
	public int getSatisNum() {
		return satisNum;
	}

	public void setSatisNum(int satisNum) {
		this.satisNum = satisNum;
	}

	public int getEvalNum() {
		return evalNum;
	}

	public void setEvalNum(int evalNum) {
		this.evalNum = evalNum;
	}
	
	public long getShopId() {
		return shopId;
	}

	public void setShopId(long shopId) {
		this.shopId = shopId;
	}

	public String getShopName() {
		return shopName;
	}

	public void setShopName(String shopName) {
		this.shopName = shopName;
	}

	public int getReceiveSessionNum() {
		return receiveSessionNum;
	}

	public void setReceiveSessionNum(int receiveSessionNum) {
		this.receiveSessionNum = receiveSessionNum;
	}

	public int getNonReplySessionNum() {
		return nonReplySessionNum;
	}

	public void setNonReplySessionNum(int nonReplySessionNum) {
		this.nonReplySessionNum = nonReplySessionNum;
	}

	public double getAvgResTimeFirst() {
		return avgResTimeFirst;
	}

	public void setAvgResTimeFirst(double avgResTimeFirst) {
		this.avgResTimeFirst = avgResTimeFirst;
	}

	public double getAvgResTime() {
		return avgResTime;
	}

	public void setAvgResTime(double avgResTime) {
		this.avgResTime = avgResTime;
	}

	public int getAvgRespInQuickTime() {
		return avgRespInQuickTime;
	}

	public void setAvgRespInQuickTime(int avgRespInQuickTime) {
		this.avgRespInQuickTime = avgRespInQuickTime;
	}

	public double getReplyRate() {
		return replyRate;
	}

	public void setReplyRate(double replyRate) {
		this.replyRate = replyRate;
	}

	public double getSatisRate() {
		return satisRate;
	}

	public void setSatisRate(double satisRate) {
		this.satisRate = satisRate;
	}

	public double getQuickResRate() {
		return quickResRate;
	}

	public void setQuickResRate(double quickResRate) {
		this.quickResRate = quickResRate;
	}

	public int getNonReplySessionNumSize() {
		return nonReplySessionNumSize;
	}

	public void setNonReplySessionNumSize(int nonReplySessionNumSize) {
		this.nonReplySessionNumSize = nonReplySessionNumSize;
	}

	public int getAvgResTimeSize() {
		return avgResTimeSize;
	}

	public void setAvgResTimeSize(int avgResTimeSize) {
		this.avgResTimeSize = avgResTimeSize;
	}

	public int getAvgResTimeFirstSize() {
		return avgResTimeFirstSize;
	}

	public void setAvgResTimeFirstSize(int avgResTimeFirstSize) {
		this.avgResTimeFirstSize = avgResTimeFirstSize;
	}

    public int getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(int consultNum) {
        this.consultNum = consultNum;
    }

    public int getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(int receiveNum) {
        this.receiveNum = receiveNum;
    }

    public double getAvgSessionDurationTime() {
        return avgSessionDurationTime;
    }

    public void setAvgSessionDurationTime(double avgSessionDurationTime) {
        this.avgSessionDurationTime = avgSessionDurationTime;
    }

    public int getLeaveMsgReceiveSessionNum() {
        return leaveMsgReceiveSessionNum;
    }

    public void setLeaveMsgReceiveSessionNum(int leaveMsgReceiveSessionNum) {
        this.leaveMsgReceiveSessionNum = leaveMsgReceiveSessionNum;
    }

    public int getForwardOutSessionNum() {
        return forwardOutSessionNum;
    }

    public void setForwardOutSessionNum(int forwardOutSessionNum) {
        this.forwardOutSessionNum = forwardOutSessionNum;
    }

    public int getLeaveMsgSessionNum() {
        return leaveMsgSessionNum;
    }

    public void setLeaveMsgSessionNum(int leaveMsgSessionNum) {
        this.leaveMsgSessionNum = leaveMsgSessionNum;
    }

    @Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + (int) (shopId ^ (shopId >>> 32));
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ServiceDetailForRealTimeBoardDTO other = (ServiceDetailForRealTimeBoardDTO) obj;
		if (shopId != other.shopId)
			return false;
		return true;
	}

}
