package com.pes.jd.model.DTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pes.jd.model.DO.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown=true)
public class ShopUserDTO implements Serializable{

	public static ShopUserDTO of(String nick){
		ShopUserDTO dto = new ShopUserDTO();
		try {
			dto.setNick(nick);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return dto;
	}

	private Logger log = LoggerFactory.getLogger(ShopUserDTO.class);
	private static final long serialVersionUID = -4075665008375912941L;
	private Long userId;
	private Long sellerId;//映射pes_user表中的主键user_id，没有业务含义，是京东账号的ID
	private String nick;
	private String showNick;
	private String sessionKey;
	private String roles;
	private String type;
	private Date created;
	private Date subscribeDeadLine;
	private String status;
	private String shopId;
	private Integer lockFlag;
	private String itemCode;// 店铺订购的版本
	private Long level;// 店铺信用等级
	private Boolean multiShopSwitch; // 是否显示多店铺筛选项
	private Boolean mainAccount;
	private Boolean vistorCheck;// 访问码是否开启
	private String openId;
	private Integer interfaceType;//1：简洁版  2：完整版
	private Date authDeadLine;//授权过期时间
	private String refreshSessionKey;
	public ShopUserDTO() {
	}

	public ShopUserDTO(String nick) {
		super();
		this.nick = nick;
	}

	public ShopUserDTO(User user, String sessionKey) {
		this.setUserId(user.getUserId());
		try {
			this.setNick(user.getNick());
		} catch (Exception e) {
			log.error("error information:" + e.getMessage(), e);
		}
		this.setSessionKey(sessionKey);
	}


	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getNick() {
		return nick;
	}

	public void setNick(String nick) throws UnsupportedEncodingException {
		this.nick = URLDecoder.decode(nick, "UTF-8");
	}

	public String getSessionKey() {
		return sessionKey;
	}

	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey;
	}

	public String getRoles() {
		return roles;
	}

	public void setRoles(String roles) {
		this.roles = roles;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Date getSubscribeDeadLine() {
		return subscribeDeadLine;
	}

	public void setSubscribeDeadLine(Date subscribeDeadLine) {
		this.subscribeDeadLine = subscribeDeadLine;
	}

	public String getStatus() {
		return status;
	}

	public String getShopId() {
		return shopId;
	}

	public void setShopId(String shopId) {
		this.shopId = shopId;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode;
	}


	public Long getLevel() {
		return level;
	}

	public void setLevel(Long level) {
		this.level = level;
	}

    public Integer getLockFlag() {
        return lockFlag;
    }

    public void setLockFlag(Integer lockFlag) {
        this.lockFlag = lockFlag;
    }

    public Boolean getMultiShopSwitch() {
		return multiShopSwitch;
	}

	public void setMultiShopSwitch(Boolean multiShopSwitch) {
		this.multiShopSwitch = multiShopSwitch;
	}

	public Boolean getMainAccount() {
		return mainAccount;
	}

	public void setMainAccount(Boolean mainAccount) {
		this.mainAccount = mainAccount;
	}
	public Long getSellerId() {
		return sellerId;
	}

	public void setSellerId(Long sellerId) {
		this.sellerId = sellerId;
	}

	public Boolean getVistorCheck() {
		return vistorCheck;
	}

	public void setVistorCheck(Boolean vistorCheck) {
		this.vistorCheck = vistorCheck;
	}

	public Integer getInterfaceType() {
		return interfaceType;
	}

	public void setInterfaceType(Integer interfaceType) {
		this.interfaceType = interfaceType;
	}

	public Date getAuthDeadLine() {
		return authDeadLine;
	}

	public void setAuthDeadLine(Date authDeadLine) {
		this.authDeadLine = authDeadLine;
	}

	public String getRefreshSessionKey() {
		return refreshSessionKey;
	}

	public void setRefreshSessionKey(String refreshSessionKey) {
		this.refreshSessionKey = refreshSessionKey;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((userId == null) ? 0 : userId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ShopUserDTO other = (ShopUserDTO) obj;
		if (userId == null) {
			if (other.userId != null)
				return false;
		} else if (!userId.equals(other.userId))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "ShopUserDTO{" +
				"userId=" + userId +
				", nick='" + nick + '\'' +
				", sessionKey='" + sessionKey + '\'' +
				", roles='" + roles + '\'' +
				", type='" + type + '\'' +
				", created=" + created +
				", subscribeDeadLine=" + subscribeDeadLine +
				", status='" + status + '\'' +
				", shopId='" + shopId + '\'' +
				", lockFlag=" + lockFlag +
				", itemCode='" + itemCode + '\'' +
				", level=" + level +
				", multiShopSwitch=" + multiShopSwitch +
				", mainAccount=" + mainAccount +
				'}';
	}

	public String getShowNick() {
		return showNick;
	}

	public void setShowNick(String showNick) {
		this.showNick = showNick;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}
}
  
