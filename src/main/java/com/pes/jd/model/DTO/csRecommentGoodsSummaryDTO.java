package com.pes.jd.model.DTO;

import java.util.Date;

public class csRecommentGoodsSummaryDTO {

	private Long id;

	private Long shopId;
	
	private Date date;
	
	private String csNick;
	
	private Long skuId;
	
	private Integer recommendNum;

    private Integer purchasesBuyerNum;

    private Integer purchasesGoodsNum;

    private Integer purchasesAmount;

	public Long getShopId() {
		return shopId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}


	public Integer getRecommendNum() {
		return recommendNum;
	}

	public void setRecommendNum(Integer recommendNum) {
		this.recommendNum = recommendNum;
	}

	public Integer getPurchasesBuyerNum() {
		return purchasesBuyerNum;
	}

	public void setPurchasesBuyerNum(Integer purchasesBuyerNum) {
		this.purchasesBuyerNum = purchasesBuyerNum;
	}

	public Integer getPurchasesGoodsNum() {
		return purchasesGoodsNum;
	}

	public void setPurchasesGoodsNum(Integer purchasesGoodsNum) {
		this.purchasesGoodsNum = purchasesGoodsNum;
	}

	public Integer getPurchasesAmount() {
		return purchasesAmount;
	}

	public void setPurchasesAmount(Integer purchasesAmount) {
		this.purchasesAmount = purchasesAmount;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	} 
    
    

}
