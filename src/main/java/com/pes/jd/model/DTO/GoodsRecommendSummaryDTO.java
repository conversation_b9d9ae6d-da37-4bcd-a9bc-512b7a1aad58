package com.pes.jd.model.DTO;

import java.util.Date;

public class GoodsRecommendSummaryDTO {

	private Long id;
	
	private Long shopId;
	
	private Date date;
	
	private Long skuId;
	
	private String csNick;
	
	private String skuName;
	
	private Integer recommendNum;

    private Integer purchasesBuyerNum;

    private Integer purchasesGoodsNum;

    private Double purchasesAmount;

    public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
    
	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public Integer getRecommendNum() {
		return recommendNum;
	}

	public void setRecommendNum(Integer recommendNum) {
		this.recommendNum = recommendNum;
	}

	public Integer getPurchasesBuyerNum() {
		return purchasesBuyerNum;
	}

	public void setPurchasesBuyerNum(Integer purchasesBuyerNum) {
		this.purchasesBuyerNum = purchasesBuyerNum;
	}

	public Integer getPurchasesGoodsNum() {
		return purchasesGoodsNum;
	}

	public void setPurchasesGoodsNum(Integer purchasesGoodsNum) {
		this.purchasesGoodsNum = purchasesGoodsNum;
	}

	

	public Double getPurchasesAmount() {
		return purchasesAmount;
	}

	public void setPurchasesAmount(Double purchasesAmount) {
		this.purchasesAmount = purchasesAmount;
	}

	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	} 
    
    

}
