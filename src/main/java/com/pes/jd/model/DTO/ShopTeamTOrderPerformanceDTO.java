package com.pes.jd.model.DTO;

import java.util.Date;

public class ShopTeamTOrderPerformanceDTO {
    private Long id;

    private Long shopId;

    private Date date;

    private Integer toOrderedNum;

    private Integer toOrderedOrderNum;

    private Integer toOrderedGoodsNum;

    private Double toOrderedAmount;

    private Integer toOrderedPaidNumToday;

    private Integer toOrderedPaidOrderNumToday;

    private Integer toOrderedPaidGoodsToday;

    private Double toOrderedPaidAmountToday;

    private Integer toOrderedPaidNumFinal;

    private Integer toOrderedPaidOrderNumFinal;

    private Integer toOrderedPaidGoodsFinal;

    private Double toOrderedPaidAmountFinal;

    private Integer toOrderedOutStockNum;

    private Double toOrderedOutStockAmount;

    private Integer toOrderedOutStockGoodsNum;

    private Integer toOrderedOutStockOrderNum;

    private Double saleAmount;

    private Integer saleOrderNum;

    private Integer saleGoodsNum;

    private Integer saleBuyerNum;

    private Integer saleSkuNum;

    private Double postFee;

    private Integer outStockNum;

    private Double outStockAmount;

    private Integer outStockGoodsNum;

    private Integer outStockOrderNum;

    private Integer cfmGoodsOrderNum;

    private Double cfmGoodsAmount;

    private Integer cfmGoodsNum;

    private Integer cfmGoodsBuyerNum;

    public ShopTeamTOrderPerformanceDTO() {
		super();
	}

	public ShopTeamTOrderPerformanceDTO(Long shopId, Date date) {
		super();
		this.shopId = shopId;
		this.date = date;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getToOrderedNum() {
        return toOrderedNum;
    }

    public void setToOrderedNum(Integer toOrderedNum) {
        this.toOrderedNum = toOrderedNum;
    }

    public Integer getToOrderedOrderNum() {
        return toOrderedOrderNum;
    }

    public void setToOrderedOrderNum(Integer toOrderedOrderNum) {
        this.toOrderedOrderNum = toOrderedOrderNum;
    }

    public Integer getToOrderedGoodsNum() {
        return toOrderedGoodsNum;
    }

    public void setToOrderedGoodsNum(Integer toOrderedGoodsNum) {
        this.toOrderedGoodsNum = toOrderedGoodsNum;
    }

    public Double getToOrderedAmount() {
        return toOrderedAmount;
    }

    public void setToOrderedAmount(Double toOrderedAmount) {
        this.toOrderedAmount = toOrderedAmount;
    }

    public Integer getToOrderedPaidNumToday() {
        return toOrderedPaidNumToday;
    }

    public void setToOrderedPaidNumToday(Integer toOrderedPaidNumToday) {
        this.toOrderedPaidNumToday = toOrderedPaidNumToday;
    }

    public Integer getToOrderedPaidOrderNumToday() {
        return toOrderedPaidOrderNumToday;
    }

    public void setToOrderedPaidOrderNumToday(Integer toOrderedPaidOrderNumToday) {
        this.toOrderedPaidOrderNumToday = toOrderedPaidOrderNumToday;
    }

    public Integer getToOrderedPaidGoodsToday() {
        return toOrderedPaidGoodsToday;
    }

    public void setToOrderedPaidGoodsToday(Integer toOrderedPaidGoodsToday) {
        this.toOrderedPaidGoodsToday = toOrderedPaidGoodsToday;
    }

    public Double getToOrderedPaidAmountToday() {
        return toOrderedPaidAmountToday;
    }

    public void setToOrderedPaidAmountToday(Double toOrderedPaidAmountToday) {
        this.toOrderedPaidAmountToday = toOrderedPaidAmountToday;
    }

    public Integer getToOrderedPaidNumFinal() {
        return toOrderedPaidNumFinal;
    }

    public void setToOrderedPaidNumFinal(Integer toOrderedPaidNumFinal) {
        this.toOrderedPaidNumFinal = toOrderedPaidNumFinal;
    }

    public Integer getToOrderedPaidOrderNumFinal() {
        return toOrderedPaidOrderNumFinal;
    }

    public void setToOrderedPaidOrderNumFinal(Integer toOrderedPaidOrderNumFinal) {
        this.toOrderedPaidOrderNumFinal = toOrderedPaidOrderNumFinal;
    }

    public Integer getToOrderedPaidGoodsFinal() {
        return toOrderedPaidGoodsFinal;
    }

    public void setToOrderedPaidGoodsFinal(Integer toOrderedPaidGoodsFinal) {
        this.toOrderedPaidGoodsFinal = toOrderedPaidGoodsFinal;
    }

    public Double getToOrderedPaidAmountFinal() {
        return toOrderedPaidAmountFinal;
    }

    public void setToOrderedPaidAmountFinal(Double toOrderedPaidAmountFinal) {
        this.toOrderedPaidAmountFinal = toOrderedPaidAmountFinal;
    }

    public Integer getToOrderedOutStockNum() {
        return toOrderedOutStockNum;
    }

    public void setToOrderedOutStockNum(Integer toOrderedOutStockNum) {
        this.toOrderedOutStockNum = toOrderedOutStockNum;
    }

    public Double getToOrderedOutStockAmount() {
        return toOrderedOutStockAmount;
    }

    public void setToOrderedOutStockAmount(Double toOrderedOutStockAmount) {
        this.toOrderedOutStockAmount = toOrderedOutStockAmount;
    }

    public Integer getToOrderedOutStockGoodsNum() {
        return toOrderedOutStockGoodsNum;
    }

    public void setToOrderedOutStockGoodsNum(Integer toOrderedOutStockGoodsNum) {
        this.toOrderedOutStockGoodsNum = toOrderedOutStockGoodsNum;
    }

    public Integer getToOrderedOutStockOrderNum() {
        return toOrderedOutStockOrderNum;
    }

    public void setToOrderedOutStockOrderNum(Integer toOrderedOutStockOrderNum) {
        this.toOrderedOutStockOrderNum = toOrderedOutStockOrderNum;
    }

    public Double getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(Double saleAmount) {
        this.saleAmount = saleAmount;
    }

    public Integer getSaleOrderNum() {
        return saleOrderNum;
    }

    public void setSaleOrderNum(Integer saleOrderNum) {
        this.saleOrderNum = saleOrderNum;
    }

    public Integer getSaleGoodsNum() {
        return saleGoodsNum;
    }

    public void setSaleGoodsNum(Integer saleGoodsNum) {
        this.saleGoodsNum = saleGoodsNum;
    }

    public Integer getSaleBuyerNum() {
        return saleBuyerNum;
    }

    public void setSaleBuyerNum(Integer saleBuyerNum) {
        this.saleBuyerNum = saleBuyerNum;
    }

    public Integer getSaleSkuNum() {
        return saleSkuNum;
    }

    public void setSaleSkuNum(Integer saleSkuNum) {
        this.saleSkuNum = saleSkuNum;
    }

    public Double getPostFee() {
        return postFee;
    }

    public void setPostFee(Double postFee) {
        this.postFee = postFee;
    }

    public Integer getOutStockNum() {
        return outStockNum;
    }

    public void setOutStockNum(Integer outStockNum) {
        this.outStockNum = outStockNum;
    }

    public Double getOutStockAmount() {
        return outStockAmount;
    }

    public void setOutStockAmount(Double outStockAmount) {
        this.outStockAmount = outStockAmount;
    }

    public Integer getOutStockGoodsNum() {
        return outStockGoodsNum;
    }

    public void setOutStockGoodsNum(Integer outStockGoodsNum) {
        this.outStockGoodsNum = outStockGoodsNum;
    }

    public Integer getOutStockOrderNum() {
        return outStockOrderNum;
    }

    public void setOutStockOrderNum(Integer outStockOrderNum) {
        this.outStockOrderNum = outStockOrderNum;
    }

    public Integer getCfmGoodsOrderNum() {
        return cfmGoodsOrderNum;
    }

    public void setCfmGoodsOrderNum(Integer cfmGoodsOrderNum) {
        this.cfmGoodsOrderNum = cfmGoodsOrderNum;
    }

    public Double getCfmGoodsAmount() {
        return cfmGoodsAmount;
    }

    public void setCfmGoodsAmount(Double cfmGoodsAmount) {
        this.cfmGoodsAmount = cfmGoodsAmount;
    }

    public Integer getCfmGoodsNum() {
        return cfmGoodsNum;
    }

    public void setCfmGoodsNum(Integer cfmGoodsNum) {
        this.cfmGoodsNum = cfmGoodsNum;
    }

    public Integer getCfmGoodsBuyerNum() {
        return cfmGoodsBuyerNum;
    }

    public void setCfmGoodsBuyerNum(Integer cfmGoodsBuyerNum) {
        this.cfmGoodsBuyerNum = cfmGoodsBuyerNum;
    }
}