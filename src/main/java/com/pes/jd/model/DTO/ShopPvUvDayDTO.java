package com.pes.jd.model.DTO;

import java.util.Date;

public class ShopPvUvDayDTO {

    private Long id;

    private Long shopId;

    private Date dt;

    private String date;

    private String shopName;

    private Long venderId;

    private String venderName;

    private String platformDesc;

    private Double avgRt;

    private Double avgRtNewuser;

    private Double avgRtOlduser;

    private Long pv;

    private Long uv;

    private Long landingTimes;

    private Long quitTimes;

    private Long itemPv;

    private Long itemUv;

    private Long homepagePv;

    private Long homepageUv;

    private Long addToCartSkunum;

    private Long addToCartSkutypenum;

    private Long addToCartUsers;

    private Long ordNumDeal;

    private Long saleQttyDeal;

    private Double beforePrefrAmountDeal;

    private Double afterPrefrAmountDeal;

    private Long ordUserNumDeal;

    private Long pvOldUser;

    private Long uvOldUser;

    private Long visitsOldUser;

    private Long pvNewuser;

    private Long uvNewuser;

    private Long visitsNewuser;

    private String tp;

    private Long visits;

    private Long bounceTimes;

    private Long totalPv;

    private Long totalUv;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDt() {
        return dt;
    }

    public void setDt(Date dt) {
        this.dt = dt;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName == null ? null : shopName.trim();
    }

    public Long getVenderId() {
        return venderId;
    }

    public void setVenderId(Long venderId) {
        this.venderId = venderId;
    }

    public String getVenderName() {
        return venderName;
    }

    public void setVenderName(String venderName) {
        this.venderName = venderName == null ? null : venderName.trim();
    }

    public String getPlatformDesc() {
        return platformDesc;
    }

    public void setPlatformDesc(String platformDesc) {
        this.platformDesc = platformDesc == null ? null : platformDesc.trim();
    }

    public Double getAvgRt() {
        return avgRt;
    }

    public void setAvgRt(Double avgRt) {
        this.avgRt = avgRt;
    }

    public Double getAvgRtNewuser() {
        return avgRtNewuser;
    }

    public void setAvgRtNewuser(Double avgRtNewuser) {
        this.avgRtNewuser = avgRtNewuser;
    }

    public Double getAvgRtOlduser() {
        return avgRtOlduser;
    }

    public void setAvgRtOlduser(Double avgRtOlduser) {
        this.avgRtOlduser = avgRtOlduser;
    }

    public Long getPv() {
        return pv;
    }

    public void setPv(Long pv) {
        this.pv = pv;
    }

    public Long getUv() {
        return uv;
    }

    public void setUv(Long uv) {
        this.uv = uv;
    }

    public Long getLandingTimes() {
        return landingTimes;
    }

    public void setLandingTimes(Long landingTimes) {
        this.landingTimes = landingTimes;
    }

    public Long getQuitTimes() {
        return quitTimes;
    }

    public void setQuitTimes(Long quitTimes) {
        this.quitTimes = quitTimes;
    }

    public Long getItemPv() {
        return itemPv;
    }

    public void setItemPv(Long itemPv) {
        this.itemPv = itemPv;
    }

    public Long getItemUv() {
        return itemUv;
    }

    public void setItemUv(Long itemUv) {
        this.itemUv = itemUv;
    }

    public Long getHomepagePv() {
        return homepagePv;
    }

    public void setHomepagePv(Long homepagePv) {
        this.homepagePv = homepagePv;
    }

    public Long getHomepageUv() {
        return homepageUv;
    }

    public void setHomepageUv(Long homepageUv) {
        this.homepageUv = homepageUv;
    }

    public Long getAddToCartSkunum() {
        return addToCartSkunum;
    }

    public void setAddToCartSkunum(Long addToCartSkunum) {
        this.addToCartSkunum = addToCartSkunum;
    }

    public Long getAddToCartSkutypenum() {
        return addToCartSkutypenum;
    }

    public void setAddToCartSkutypenum(Long addToCartSkutypenum) {
        this.addToCartSkutypenum = addToCartSkutypenum;
    }

    public Long getAddToCartUsers() {
        return addToCartUsers;
    }

    public void setAddToCartUsers(Long addToCartUsers) {
        this.addToCartUsers = addToCartUsers;
    }

    public Long getOrdNumDeal() {
        return ordNumDeal;
    }

    public void setOrdNumDeal(Long ordNumDeal) {
        this.ordNumDeal = ordNumDeal;
    }

    public Long getSaleQttyDeal() {
        return saleQttyDeal;
    }

    public void setSaleQttyDeal(Long saleQttyDeal) {
        this.saleQttyDeal = saleQttyDeal;
    }

    public Double getBeforePrefrAmountDeal() {
        return beforePrefrAmountDeal;
    }

    public void setBeforePrefrAmountDeal(Double beforePrefrAmountDeal) {
        this.beforePrefrAmountDeal = beforePrefrAmountDeal;
    }

    public Double getAfterPrefrAmountDeal() {
        return afterPrefrAmountDeal;
    }

    public void setAfterPrefrAmountDeal(Double afterPrefrAmountDeal) {
        this.afterPrefrAmountDeal = afterPrefrAmountDeal;
    }

    public Long getOrdUserNumDeal() {
        return ordUserNumDeal;
    }

    public void setOrdUserNumDeal(Long ordUserNumDeal) {
        this.ordUserNumDeal = ordUserNumDeal;
    }

    public Long getPvOldUser() {
        return pvOldUser;
    }

    public void setPvOldUser(Long pvOldUser) {
        this.pvOldUser = pvOldUser;
    }

    public Long getUvOldUser() {
        return uvOldUser;
    }

    public void setUvOldUser(Long uvOldUser) {
        this.uvOldUser = uvOldUser;
    }

    public Long getVisitsOldUser() {
        return visitsOldUser;
    }

    public void setVisitsOldUser(Long visitsOldUser) {
        this.visitsOldUser = visitsOldUser;
    }

    public Long getPvNewuser() {
        return pvNewuser;
    }

    public void setPvNewuser(Long pvNewuser) {
        this.pvNewuser = pvNewuser;
    }

    public Long getUvNewuser() {
        return uvNewuser;
    }

    public void setUvNewuser(Long uvNewuser) {
        this.uvNewuser = uvNewuser;
    }

    public Long getVisitsNewuser() {
        return visitsNewuser;
    }

    public void setVisitsNewuser(Long visitsNewuser) {
        this.visitsNewuser = visitsNewuser;
    }

    public String getTp() {
        return tp;
    }

    public void setTp(String tp) {
        this.tp = tp == null ? null : tp.trim();
    }

    public Long getVisits() {
        return visits;
    }

    public void setVisits(Long visits) {
        this.visits = visits;
    }

    public Long getBounceTimes() {
        return bounceTimes;
    }

    public void setBounceTimes(Long bounceTimes) {
        this.bounceTimes = bounceTimes;
    }


    public Long getTotalPv() {
        return totalPv;
    }

    public void setTotalPv(Long totalPv) {
        this.totalPv = totalPv;
    }

    public Long getTotalUv() {
        return totalUv;
    }

    public void setTotalUv(Long totalUv) {
        this.totalUv = totalUv;
    }

    @Override
    public String toString() {
        return "ShopPvUvDayDTO{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", dt=" + dt +
                ", date='" + date + '\'' +
                ", shopName='" + shopName + '\'' +
                ", venderId=" + venderId +
                ", venderName='" + venderName + '\'' +
                ", platformDesc='" + platformDesc + '\'' +
                ", avgRt=" + avgRt +
                ", avgRtNewuser=" + avgRtNewuser +
                ", avgRtOlduser=" + avgRtOlduser +
                ", pv=" + pv +
                ", uv=" + uv +
                ", landingTimes=" + landingTimes +
                ", quitTimes=" + quitTimes +
                ", itemPv=" + itemPv +
                ", itemUv=" + itemUv +
                ", homepagePv=" + homepagePv +
                ", homepageUv=" + homepageUv +
                ", addToCartSkunum=" + addToCartSkunum +
                ", addToCartSkutypenum=" + addToCartSkutypenum +
                ", addToCartUsers=" + addToCartUsers +
                ", ordNumDeal=" + ordNumDeal +
                ", saleQttyDeal=" + saleQttyDeal +
                ", beforePrefrAmountDeal=" + beforePrefrAmountDeal +
                ", afterPrefrAmountDeal=" + afterPrefrAmountDeal +
                ", ordUserNumDeal=" + ordUserNumDeal +
                ", pvOldUser=" + pvOldUser +
                ", uvOldUser=" + uvOldUser +
                ", visitsOldUser=" + visitsOldUser +
                ", pvNewuser=" + pvNewuser +
                ", uvNewuser=" + uvNewuser +
                ", visitsNewuser=" + visitsNewuser +
                ", tp='" + tp + '\'' +
                ", visits=" + visits +
                ", bounceTimes=" + bounceTimes +
                ", totalPv=" + totalPv +
                ", totalUv=" + totalUv +
                '}';
    }
}
