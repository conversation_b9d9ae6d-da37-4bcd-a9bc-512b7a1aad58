package com.pes.jd.model.VO;

import com.pes.jd.model.DTO.ReserveActivityDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author:acer
 * @Dcscription:
 * @Date: Created in 2020/3/13
 * @Modified By:
 */
@Data
public class ReserveActivityVO implements Serializable {

    private Long id;
    private String activityId; //预约活动id
    private List<ReserveActivityDTO> raDTO;
    private Long shopId;
    private Integer type;  //预约活动类型
    private Integer plusType;  //plus优先购类型
    private Integer status;  //预约状态。1:预约未开始; 2:预约中; 3:预约结束; 4:抢购中; 5:抢购结束

    private Date startTime;
    private Date endTime;
    private Date panicbuyingStime; //抢购开始时间
    private Date panicbuyingEtime; //抢购结束时间

    private Long conditionType;  //活动状态 1正在进行  0过期

}
