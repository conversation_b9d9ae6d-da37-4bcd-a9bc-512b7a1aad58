package com.pes.jd.model.VO;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年08月09 16:42:42<br>
 */
public class ShopClosureConversionAvgVO {
    private Long shopId;
    private String shopName;
    private double allocatedNum;
    private double urgeSuccessNum;
    private double shopSaleAmount;
    private double urgeAmount;
    private double urgeAmountRate;
    private double closureNum;
    private double closureSuccessNum;
    private double closureAmount;
    private double closureAmountRate;
    private double notUrgeAmount;
    private double notClosureAmount;
    private double enquiryOrderUrgeAmount;
    private double enquiryOrderUrgeAmountRate;
    private double notEnquiryOrderUrgeAmount;
    private double enquiryOrderClosureAmount;
    private double enquiryOrderClosureAmountRate;
    private double notEnquiryOrderClosureAmount;
    private double urgeExecuteNum;
    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public double getAllocatedNum() {
        return allocatedNum;
    }

    public void setAllocatedNum(double allocatedNum) {
        this.allocatedNum = allocatedNum;
    }

    public double getUrgeSuccessNum() {
        return urgeSuccessNum;
    }

    public void setUrgeSuccessNum(double urgeSuccessNum) {
        this.urgeSuccessNum = urgeSuccessNum;
    }

    public double getShopSaleAmount() {
        return shopSaleAmount;
    }

    public void setShopSaleAmount(double shopSaleAmount) {
        this.shopSaleAmount = shopSaleAmount;
    }

    public double getUrgeAmount() {
        return urgeAmount;
    }

    public void setUrgeAmount(double urgeAmount) {
        this.urgeAmount = urgeAmount;
    }

    public double getUrgeAmountRate() {
        return urgeAmountRate;
    }

    public void setUrgeAmountRate(double urgeAmountRate) {
        this.urgeAmountRate = urgeAmountRate;
    }

    public double getClosureNum() {
        return closureNum;
    }

    public void setClosureNum(double closureNum) {
        this.closureNum = closureNum;
    }

    public double getClosureSuccessNum() {
        return closureSuccessNum;
    }

    public void setClosureSuccessNum(double closureSuccessNum) {
        this.closureSuccessNum = closureSuccessNum;
    }

    public double getClosureAmount() {
        return closureAmount;
    }

    public void setClosureAmount(double closureAmount) {
        this.closureAmount = closureAmount;
    }

    public double getClosureAmountRate() {
        return closureAmountRate;
    }

    public void setClosureAmountRate(double closureAmountRate) {
        this.closureAmountRate = closureAmountRate;
    }

    public double getNotUrgeAmount() {
        return notUrgeAmount;
    }

    public void setNotUrgeAmount(double notUrgeAmount) {
        this.notUrgeAmount = notUrgeAmount;
    }

    public double getEnquiryOrderUrgeAmount() {
        return enquiryOrderUrgeAmount;
    }

    public void setEnquiryOrderUrgeAmount(double enquiryOrderUrgeAmount) {
        this.enquiryOrderUrgeAmount = enquiryOrderUrgeAmount;
    }

    public double getEnquiryOrderUrgeAmountRate() {
        return enquiryOrderUrgeAmountRate;
    }

    public void setEnquiryOrderUrgeAmountRate(double enquiryOrderUrgeAmountRate) {
        this.enquiryOrderUrgeAmountRate = enquiryOrderUrgeAmountRate;
    }

    public double getNotEnquiryOrderUrgeAmount() {
        return notEnquiryOrderUrgeAmount;
    }

    public void setNotEnquiryOrderUrgeAmount(double notEnquiryOrderUrgeAmount) {
        this.notEnquiryOrderUrgeAmount = notEnquiryOrderUrgeAmount;
    }

    public double getEnquiryOrderClosureAmount() {
        return enquiryOrderClosureAmount;
    }

    public void setEnquiryOrderClosureAmount(double enquiryOrderClosureAmount) {
        this.enquiryOrderClosureAmount = enquiryOrderClosureAmount;
    }

    public double getEnquiryOrderClosureAmountRate() {
        return enquiryOrderClosureAmountRate;
    }

    public void setEnquiryOrderClosureAmountRate(double enquiryOrderClosureAmountRate) {
        this.enquiryOrderClosureAmountRate = enquiryOrderClosureAmountRate;
    }

    public double getNotEnquiryOrderClosureAmount() {
        return notEnquiryOrderClosureAmount;
    }

    public void setNotEnquiryOrderClosureAmount(double notEnquiryOrderClosureAmount) {
        this.notEnquiryOrderClosureAmount = notEnquiryOrderClosureAmount;
    }

    public double getNotClosureAmount() {
        return notClosureAmount;
    }

    public void setNotClosureAmount(double notClosureAmount) {
        this.notClosureAmount = notClosureAmount;
    }

    public double getUrgeExecuteNum() {
        return urgeExecuteNum;
    }

    public void setUrgeExecuteNum(double urgeExecuteNum) {
        this.urgeExecuteNum = urgeExecuteNum;
    }
}
