package com.pes.jd.model.VO;

import java.util.List;


public class GoodsGroupVO {
	private Long shopId;
	private String goodsGroupId;
	private String goodsGroupName;
	private int goodsNum;
	private List<GoodsGroupSkuVO> shopGoodsSkuLst;
    private Byte dimension;//维度
	
	/**
	 * @param shopId
	 * @param goodsGroupId
	 * @param goodsGroupName
	 * @param goodsNum
	 * @param shopGoodsSkuLst
	 */
	public GoodsGroupVO(Long shopId, String goodsGroupId, String goodsGroupName, int goodsNum,
			List<GoodsGroupSkuVO> shopGoodsSkuLst) {
		super();
		this.shopId = shopId;
		this.goodsGroupId = goodsGroupId;
		this.goodsGroupName = goodsGroupName;
		this.goodsNum = goodsNum;
		this.shopGoodsSkuLst = shopGoodsSkuLst;
	}
	public GoodsGroupVO() {
		super();
	}
	public String getGoodsGroupId() {
		return goodsGroupId;
	}
	public void setGoodsGroupId(String goodsGroupId) {
		this.goodsGroupId = goodsGroupId;
	}
	public String getGoodsGroupName() {
		return goodsGroupName;
	}
	public void setGoodsGroupName(String goodsGroupName) {
		this.goodsGroupName = goodsGroupName;
	}
	public int getGoodsNum() {
		return goodsNum;
	}
	public void setGoodsNum(int goodsNum) {
		this.goodsNum = goodsNum;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public List<GoodsGroupSkuVO> getShopGoodsSkuLst() {
		return shopGoodsSkuLst;
	}
	public void setShopGoodsSkuLst(List<GoodsGroupSkuVO> shopGoodsSkuLst) {
		this.shopGoodsSkuLst = shopGoodsSkuLst;
	}

    public Byte getDimension() {
        return dimension;
    }

    public void setDimension(Byte dimension) {
        this.dimension = dimension;
    }
}
