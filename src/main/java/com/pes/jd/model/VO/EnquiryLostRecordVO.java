package com.pes.jd.model.VO;

import java.util.Date;
import java.util.List;

public class EnquiryLostRecordVO {

    private Long shopId;
    private Date date;
    private Date startDate;
    private Date endDate;
    private String buyerNick;
    private String csNick;
    private String csSimpleNick;
    private String note;
    private Long noteId;
    private Integer chatNum;//对话句数
    private List<String> goodsLst;//商品名
    private Long consumeTime;
    private String goodStr;
    private String consumeTimeStr;


    public EnquiryLostRecordVO() {
        super();
    }

    public EnquiryLostRecordVO(Long shopId) {
        super();
        this.shopId = shopId;
    }

    public Long getConsumeTime() {
        return consumeTime;
    }

    public void setConsumeTime(Long consumeTime) {
        this.consumeTime = consumeTime;
    }

    public Integer getChatNum() {
        return chatNum;
    }

    public void setChatNum(Integer chatNum) {
        this.chatNum = chatNum;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Long getNoteId() {
        return noteId;
    }

    public void setNoteId(Long noteId) {
        this.noteId = noteId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCsSimpleNick() {
        return csSimpleNick;
    }

    public void setCsSimpleNick(String csSimpleNick) {
        this.csSimpleNick = csSimpleNick;
    }

    public List<String> getGoodsLst() {
        return goodsLst;
    }

    public void setGoodsLst(List<String> goodsLst) {
        this.goodsLst = goodsLst;
    }

    public String getGoodStr() {
        return goodStr;
    }

    public void setGoodStr(String goodStk) {
        this.goodStr = goodStk;
    }

    public String getConsumeTimeStr() {
        return consumeTimeStr;
    }

    public void setConsumeTimeStr(String consumeTimeStr) {
        this.consumeTimeStr = consumeTimeStr;
    }
}
  
