package com.pes.jd.model.VO;

/**
 * @Author: aiJun
 * @Date: 2019-06-02 13:45
 * @Version 1.0
 */
public class GlobalMnoitorVOAVG {

    private double slowResponseNum;
    private double csViolationNum;
    private double customerRailNum;

    private String shopId;

    private String title;

    private double receiveNum;//接待客服人数：当前有会话未关闭的客服总数

    private double shopLoginNum;//在线人数

    private double shopSuspendNum;//挂起人数

    private double onlineConsultSessionNum;// 在线咨询量：在线咨询类型的咨询总量

    private double receiveingSessionNum;// 当前咨询量：当前会话未关闭的咨询量




    public double getSlowResponseNum() {
        return slowResponseNum;
    }

    public void setSlowResponseNum(double slowResponseNum) {
        this.slowResponseNum = slowResponseNum;
    }

    public double getCsViolationNum() {
        return csViolationNum;
    }

    public void setCsViolationNum(double csViolationNum) {
        this.csViolationNum = csViolationNum;
    }

    public double getCustomerRailNum() {
        return customerRailNum;
    }

    public void setCustomerRailNum(double customerRailNum) {
        this.customerRailNum = customerRailNum;
    }

    public String getShopId() {
        return shopId;
    }

    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public double getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(double receiveNum) {
        this.receiveNum = receiveNum;
    }

    public double getShopLoginNum() {
        return shopLoginNum;
    }

    public void setShopLoginNum(double shopLoginNum) {
        this.shopLoginNum = shopLoginNum;
    }

    public double getShopSuspendNum() {
        return shopSuspendNum;
    }

    public void setShopSuspendNum(double shopSuspendNum) {
        this.shopSuspendNum = shopSuspendNum;
    }

    public double getOnlineConsultSessionNum() {
        return onlineConsultSessionNum;
    }

    public void setOnlineConsultSessionNum(double onlineConsultSessionNum) {
        this.onlineConsultSessionNum = onlineConsultSessionNum;
    }

    public double getReceiveingSessionNum() {
        return receiveingSessionNum;
    }

    public void setReceiveingSessionNum(double receiveingSessionNum) {
        this.receiveingSessionNum = receiveingSessionNum;
    }
}
