package com.pes.jd.model.VO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/3/4/16:27
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReceiveFilterRecordSessionVO {
    private String sid;

    private Long shopId;

    private Date date;

    private String csNick;

    private String customer;

    private Date beginDatetime;

    private Date endDatetime;

    private Date sessionBeginDatetime;

    private Date sessionEndDatetime;

    private Integer sessionType;

    private Double sessionDurationTime;

    private Double avgRespTimeFirst;

    private Double avgRespTime;

    private Integer csChatNum;

    private Integer custChatNum;

    private Integer receiveStartType;

    private Integer forwardType;

    private Double satisRate;

    private String csSimpleNick;

    private String groupName;

    private Integer evalCode;

    private Boolean isAssign;

    private Boolean leaveMsgFilter;

    private Boolean forwardOut;//是转出接待

    public Boolean getForwardOut() {
        return forwardOut;
    }

    public void setForwardOut(Boolean forwardOut) {
        this.forwardOut = forwardOut;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public Date getBeginDatetime() {
        return beginDatetime;
    }

    public void setBeginDatetime(Date beginDatetime) {
        this.beginDatetime = beginDatetime;
    }

    public Date getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(Date endDatetime) {
        this.endDatetime = endDatetime;
    }

    public Integer getSessionType() {
        return sessionType;
    }

    public void setSessionType(Integer sessionType) {
        this.sessionType = sessionType;
    }

    public Double getSessionDurationTime() {
        return sessionDurationTime;
    }

    public void setSessionDurationTime(Double sessionDurationTime) {
        this.sessionDurationTime = sessionDurationTime;
    }

    public Double getAvgRespTimeFirst() {
        return avgRespTimeFirst;
    }

    public void setAvgRespTimeFirst(Double avgRespTimeFirst) {
        this.avgRespTimeFirst = avgRespTimeFirst;
    }

    public Double getAvgRespTime() {
        return avgRespTime;
    }

    public void setAvgRespTime(Double avgRespTime) {
        this.avgRespTime = avgRespTime;
    }

    public Integer getCsChatNum() {
        return csChatNum;
    }

    public void setCsChatNum(Integer csChatNum) {
        this.csChatNum = csChatNum;
    }

    public Integer getCustChatNum() {
        return custChatNum;
    }

    public void setCustChatNum(Integer custChatNum) {
        this.custChatNum = custChatNum;
    }

    public Integer getReceiveStartType() {
        return receiveStartType;
    }

    public void setReceiveStartType(Integer receiveStartType) {
        this.receiveStartType = receiveStartType;
    }

    public Integer getForwardType() {
        return forwardType;
    }

    public void setForwardType(Integer forwardType) {
        this.forwardType = forwardType;
    }


    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getCsSimpleNick() {
        return csSimpleNick;
    }

    public void setCsSimpleNick(String csSimpleNick) {
        this.csSimpleNick = csSimpleNick;
    }


    public Double getSatisRate() {
        return satisRate;
    }

    public void setSatisRate(Double satisRate) {
        this.satisRate = satisRate;
    }

    public Integer getEvalCode() {
        return evalCode;
    }

    public void setEvalCode(Integer evalCode) {
        this.evalCode = evalCode;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public Date getSessionBeginDatetime() {
        return sessionBeginDatetime;
    }

    public void setSessionBeginDatetime(Date sessionBeginDatetime) {
        this.sessionBeginDatetime = sessionBeginDatetime;
    }

    public Date getSessionEndDatetime() {
        return sessionEndDatetime;
    }

    public void setSessionEndDatetime(Date sessionEndDatetime) {
        this.sessionEndDatetime = sessionEndDatetime;
    }

    public Boolean getAssign() {
        return isAssign;
    }

    public void setAssign(Boolean assign) {
        isAssign = assign;
    }

    public Boolean getLeaveMsgFilter() {
        return leaveMsgFilter;
    }

    public void setLeaveMsgFilter(Boolean leaveMsgFilter) {
        this.leaveMsgFilter = leaveMsgFilter;
    }
}
