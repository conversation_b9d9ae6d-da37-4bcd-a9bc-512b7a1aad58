package com.pes.jd.model.VO;

import java.io.Serializable;
import java.util.Date;

public class CsSaleAnalysisVO implements Serializable{

	private Long shopId;

	private Long orderId;

	private String buyerNick;

	private String orderStatus;

	private Date orderCreated;

	private Date orderPayTime;

	private Date outStockTime;
	
	private Double payMoney;
	
	private String csNick;

	private Date firstChatDate;

	private Date lastChatDate;
	
	private Boolean payTypeFlag;//判断是不是货到付款标识
	
	private Integer payType;

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Boolean getPayTypeFlag() {
		return payTypeFlag;
	}

	public void setPayTypeFlag(Boolean payTypeFlag) {
		this.payTypeFlag = payTypeFlag;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	public Date getOrderCreated() {
		return orderCreated;
	}

	public void setOrderCreated(Date orderCreated) {
		this.orderCreated = orderCreated;
	}

	public Date getOrderPayTime() {
		return orderPayTime;
	}

	public void setOrderPayTime(Date orderPayTime) {
		this.orderPayTime = orderPayTime;
	}

	public Date getOutStockTime() {
		return outStockTime;
	}

	public void setOutStockTime(Date outStockTime) {
		this.outStockTime = outStockTime;
	}

	public Double getPayMoney() {
		return payMoney;
	}

	public void setPayMoney(Double payMoney) {
		this.payMoney = payMoney;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Date getFirstChatDate() {
		return firstChatDate;
	}

	public void setFirstChatDate(Date firstChatDate) {
		this.firstChatDate = firstChatDate;
	}

	public Date getLastChatDate() {
		return lastChatDate;
	}

	public void setLastChatDate(Date lastChatDate) {
		this.lastChatDate = lastChatDate;
	}

}
