package com.pes.jd.model.VO;

import java.util.Date;

public class CsDutyRecordExportVO {
	
	private Long id;
	private Long shopId;
	private Long groupId;
	private String csNick;
	private String csSimpleNick;
	private Date date;
	private Long delayTime;//登录记录延迟时间：用于多个店铺汇总使用(多个店铺并且业务天延迟时间点不同)
	
	private Date firstOnlineDateTime;//最早登录时间
	private Date lastOfflineDateTime;//最晚登出时间
	private Date avgFirstOnlineDateTime;//平均最早登录时间
	private Date avgLastOfflineDateTime;//平均最晚登出时间
	private Double loginTimesNum;//总登录次数
	private Long loginDurationTime;//登录时长
	private Long rceiveDurationTime;//接待时长
	private Long hangupDurationTime;//挂起时长
	
	//总览
	private Double workDay;//工作天数
	private Double avgLoginTimesNum;//日均登录次数
	private Long avgLoginDurationTime;//日均登录时长
	private Long avgHangupDurationTime;//日均挂起时长
	private Long avgRceiveDurationTime;//日均接待时长
	
	//详情
	private Double rceiveTimeRate;//接待时长占比=接待时长/登录时长
	private Long offlineDurationTime;//上班期间离线时长
	
	public CsDutyRecordExportVO(Long shopId, Long groupId, String csNick, String csSimpleNick) {
		super();
		this.shopId = shopId;
		this.groupId = groupId;
		this.csNick = csNick;
		this.csSimpleNick = csSimpleNick;
		this.loginTimesNum = 0.0;
		this.loginDurationTime = 0L;
		this.rceiveDurationTime = 0L;
		this.hangupDurationTime = 0L;
		this.workDay = 0.0;
		this.avgLoginTimesNum = 0d;
		this.avgLoginDurationTime = 0L;
		this.avgHangupDurationTime = 0L;
		this.avgRceiveDurationTime = 0L;
		this.rceiveTimeRate = 0.0;
		this.offlineDurationTime = 0L;
	}
	public CsDutyRecordExportVO() {
		super();  
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public Double getWorkDay() {
		return workDay==null?0.0:workDay;
	}
	public void setWorkDay(Double workDay) {
		this.workDay = workDay;
	}
	public Date getFirstOnlineDateTime() {
		return firstOnlineDateTime;
	}
	public void setFirstOnlineDateTime(Date firstOnlineDateTime) {
		this.firstOnlineDateTime = firstOnlineDateTime;
	}
	public Date getLastOfflineDateTime() {
		return lastOfflineDateTime;
	}
	public void setLastOfflineDateTime(Date lastOfflineDateTime) {
		this.lastOfflineDateTime = lastOfflineDateTime;
	}
	public Double getLoginTimesNum() {
		return loginTimesNum;
	}
	public void setLoginTimesNum(Double loginTimesNum) {
		this.loginTimesNum = loginTimesNum;
	}
	public Double getAvgLoginTimesNum() {
		return avgLoginTimesNum;
	}
	public void setAvgLoginTimesNum(Double avgLoginTimesNum) {
		this.avgLoginTimesNum = avgLoginTimesNum;
	}
	public Long getLoginDurationTime() {
		return loginDurationTime;
	}
	public void setLoginDurationTime(Long loginDurationTime) {
		this.loginDurationTime = loginDurationTime;
	}
	public Long getAvgLoginDurationTime() {
		return avgLoginDurationTime;
	}
	public void setAvgLoginDurationTime(Long avgLoginDurationTime) {
		this.avgLoginDurationTime = avgLoginDurationTime;
	}
	public Long getRceiveDurationTime() {
		return rceiveDurationTime;
	}
	public void setRceiveDurationTime(Long rceiveDurationTime) {
		this.rceiveDurationTime = rceiveDurationTime;
	}
	public Long getAvgRceiveDurationTime() {
		return avgRceiveDurationTime;
	}
	public void setAvgRceiveDurationTime(Long avgRceiveDurationTime) {
		this.avgRceiveDurationTime = avgRceiveDurationTime;
	}
	public Long getHangupDurationTime() {
		return hangupDurationTime;
	}
	public void setHangupDurationTime(Long hangupDurationTime) {
		this.hangupDurationTime = hangupDurationTime;
	}
	public Long getAvgHangupDurationTime() {
		return avgHangupDurationTime;
	}
	public void setAvgHangupDurationTime(Long avgHangupDurationTime) {
		this.avgHangupDurationTime = avgHangupDurationTime;
	}
	public Double getRceiveTimeRate() {
		return rceiveTimeRate;
	}
	public void setRceiveTimeRate(Double rceiveTimeRate) {
		this.rceiveTimeRate = rceiveTimeRate;
	}
	public Long getOfflineDurationTime() {
		return offlineDurationTime;
	}
	public void setOfflineDurationTime(Long offlineDurationTime) {
		this.offlineDurationTime = offlineDurationTime;
	}
	public Date getAvgFirstOnlineDateTime() {
		return avgFirstOnlineDateTime;
	}
	public void setAvgFirstOnlineDateTime(Date avgFirstOnlineDateTime) {
		this.avgFirstOnlineDateTime = avgFirstOnlineDateTime;
	}
	public Date getAvgLastOfflineDateTime() {
		return avgLastOfflineDateTime;
	}
	public void setAvgLastOfflineDateTime(Date avgLastOfflineDateTime) {
		this.avgLastOfflineDateTime = avgLastOfflineDateTime;
	}
	public Long getGroupId() {
		return groupId;
	}
	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}
	public String getCsSimpleNick() {
		return csSimpleNick;
	}
	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}
	public Long getDelayTime() {
		return delayTime;
	}
	public void setDelayTime(Long delayTime) {
		this.delayTime = delayTime;
	}
	
}
  
