package com.pes.jd.model.VO;

import lombok.Data;

import java.util.Date;

@Data
public class ShopUseAnalysisVO {

	private Long shopId;
	private double shopSaleAmount;
	private String causeGroupName;
	private String causeDeptName;
	private String secondDeptName;
	private String shopName;
	private String operateManagerErp;
	private String operateManagerName;
	private int subscribeNum;
	private int validSubscribeDay;
	private Date lastSubscribeTime;
	private Integer lastSubscribeStatus;
	private String lastShopUserStatus;
	private int loginDayNum;
	private double  loginDayRate;
	private int oneStepUserUrgeDay;
	private double oneStepUserUrgeRate;
	private double oneStepUrgeAmount;
	private double oneStepNotUrgeAmount;
	private double oneStepNotUrgeAmountForTotal;
	private double oneStepUrgeAmountRate;
	private double oneStepExecuteSuccessRate;
	private int batchUserUrgeDay;
	private double batchUserUrgeRate;
	private double batchUrgeAmount;
	private double batchNotUrgeAmount;
	private double batchNotUrgeAmountForTotal;
	private double batchUrgeAmountRate;
	private double batchExecuteSuccessRate;
	private double sumUrgeAmount;
	private double sumUrgeAmountRate;
	private double sumExecuteSuccessRate;
	private int oneStepAllocateNum;
	private int batchAllocateNum;
	private int oneStepExecutetNum;
	private int batchExecuteNum;
	private int screenValidSubscribeDay;
	private double sumNotUrgeAmount;
	private double sumNotUrgeAmountForTotal;
	private int sumAllocatedNum;
	private int sumExecuteNum;

	private int loginUseDayNum;
	private double loginUseDayRate;


	private int smsUseNum;
	private double smsUseNumRate;
	private double batchUrgeOrderAmount;
	private double smsUrgeAmount;
	private int smsSendSuccessNum;
	private int smsSendValidNum;
	private double auotUrgeAmount;
	private double auotNotUrgeAmountForTotal;
	private double auotNotUrgeAmountForAvg;
	private double smsSendValidRate;
	private double auotUrgeAmountRate;

	private int reserveBatchUserUrgeDay;
	private double reserveBatchUserUrgeRate;
	private int reserveBatchAllocateNum;
	private int reserveBatchExecuteNum;
	private double reserveBatchExecuteSuccessRate;
	private double reserveBatchUrgeAmount;
	private double reserveBatchNotUrgeAmount;
	private double reserveBatchNotUrgeAmountForTotal;
	private double reserveBatchUrgeAmountRate;
	private double reserveBatchUrgeOrderAmount;


	private int presaleBatchUserUrgeDay;
	private double presaleBatchUserUrgeRate;
	private int presaleBatchAllocateNum;
	private int presaleBatchExecuteNum;
	private double presaleBatchExecuteSuccessRate;
	private double presaleBatchUrgeAmount;
	private double presaleBatchNotUrgeAmount;
	private double presaleBatchNotUrgeAmountForTotal;
	private double presaleBatchUrgeAmountRate;
	private double presaleBatchUrgeOrderAmount;

	private double reserveAuotUrgeAmount;
	private double reserveAuotNotUrgeAmountForTotal;
	private double reserveAuotNotUrgeAmount;
	private double reserveAuotUrgeAmountRate;

	private double presaleAuotUrgeAmount;
	private double presaleAuotNotUrgeAmountForTotal;
	private double presaleAuotNotUrgeAmount;
	private double presaleAuotUrgeAmountRate;

	private double reserveShopSaleAmount;
	private double presaleShopSaleAmount;

	private double reserveSumUrgeAmount;
	private double reserveSumUrgeAmountRate;
	private double reserveSumNotUrgeAmount;
	private double reserveSumNotUrgeAmountForTotal;

	private double presaleSumUrgeAmount;
	private double presaleSumUrgeAmountRate;
	private double presaleSumNotUrgeAmount;
	private double presaleSumNotUrgeAmountForTotal;

	private int reserveSumAllocatedNum;
	private int reserveSumExecuteNum;
	private double reserveSumExecuteSuccessRate;

	private int presaleSumAllocatedNum;
	private int presaleSumExecuteNum;
	private double presaleSumExecuteSuccessRate;

	private double shopSumSaleAmount;

}
