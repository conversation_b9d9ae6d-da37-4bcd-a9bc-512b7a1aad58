package com.pes.jd.model.VO;

import java.util.Date;

public class ChatLogExportVO {
	private Date chatTime;//聊天时间
	
	private String sender;//发送人
	
	private String content;//发送内容

	public ChatLogExportVO() {
	}

	public ChatLogExportVO(String sender, String content , Date chatTime) {
		super();
		this.chatTime = chatTime;
		this.sender = sender;
		this.content = content;
	}

	public Date getChatTime() {
		return chatTime;
	}

	public void setChatTime(Date chatTime) {
		this.chatTime = chatTime;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
}
  
