package com.pes.jd.model.VO;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

import java.util.Date;

public class CsGoodsSaleIndexDetailVO {
private Long id;
	
	private Long shopId;
	
	private Date date;
	
	private Long skuId;
	
	private String skuName;
	
	private String customer;
	
	private String csNick;
	
	private Integer saleGoodsNum;
	
	private Double saleAmount;
	
	private Date outStockTime;
	
	private String csSimpleNick;

	public String getCsSimpleNick() {
		return csSimpleNick;
	}

	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}
	
	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	

	public Date getOutStockTime() {
		return outStockTime;
	}

	public void setOutStockTime(Date outStockTime) {
		this.outStockTime = outStockTime;
	}



	@JSONField(serializeUsing=ToStringSerializer.class)
	private Long orderId;
	
	

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	
	public String getCustomer() {
		return customer;
	}

	public void setCustomer(String customer) {
		this.customer = customer;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Integer getSaleGoodsNum() {
		return saleGoodsNum;
	}

	public void setSaleGoodsNum(Integer saleGoodsNum) {
		this.saleGoodsNum = saleGoodsNum;
	}

	public Double getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(Double saleAmount) {
		this.saleAmount = saleAmount;
	}
	
	
}
