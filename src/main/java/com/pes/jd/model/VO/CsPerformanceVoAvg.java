package com.pes.jd.model.VO;

import com.pes.jd.model.Annotation.Property;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/30 9:27 AM
 * @since 1.0.0
 */
public class CsPerformanceVoAvg{


    public static CsPerformanceVoAvg getFilledAvg() {
        CsPerformanceVoAvg avo = new CsPerformanceVoAvg();
        avo.setChatRoundNum(0);
        avo.setRespTimeFirstCount(0.0D);
        avo.setRespTimeCount(0.0D);
        avo.setSessionDurationTimeCount(0.0D);
        avo.setSaleAmountOrigin(0.0D);
        avo.setAvgRespInQuickTime(0.0D);
        avo.setSessionNum(0.0D);
        avo.setConsultNum(0.0D);
        avo.setReceiveNum(0.0D);
        avo.setEnquiryNum(0.0D);
        avo.setAvgRespTimeFirst(0.0D);
        avo.setAvgRespTime(0.0D);
        avo.setSaleAmount(0.0D);
        avo.setSaleGoodsNum(0.0D);
        avo.setSaleBuyerNum(0.0D);
        avo.setSaleOrderNum(0.0D);
        avo.setSaleAmountPercent(0.0D);
        avo.setQueryToFinalPaid(0.0D);
        avo.setSaleGuestAvgAmount(0.0D);
        avo.setOutStockAmount(0.0D);
        avo.setOutStockGoodsNum(0.0D);
        avo.setOutStockNum(0.0D);
        avo.setOutStockOrderNum(0.0D);
        avo.setPersonalOutStockAmountPercent(0.0D);
        avo.setOutStockGuestAvgAmount(0.0D);
        avo.setCompletedRefundAmount(0.0D);
        avo.setCompletedRefundProductNum(0.0D);
        avo.setPostFee(0.0D);
        avo.setEnquiryLossNum(0.0D);
        avo.setOrderedNumToday(0.0D);
        avo.setOrderedAmountToday(0.0D);
        avo.setOrderedNumFinal(0.0D);
        avo.setOrderedAmountFinal(0.0D);
        avo.setToOrderedNum(0.0D);
        avo.setToOrderedGoodsNum(0.0D);
        avo.setToOrderedAmount(0.0D);
        avo.setToOrderedOrderNum(0.0D);
        avo.setPaidLossNum(0.0D);
        avo.setPaidLossGoodsNum(0.0D);
        avo.setPaidLossMoney(0.0D);
        avo.setToOrderedPaidNumToday(0.0D);
        avo.setToOrderedPaidGoodsToday(0.0D);
        avo.setToOrderedPaidAmountToday(0.0D);
        avo.setToOrderedPaidNumFinal(0.0D);
        avo.setToOrderedPaidGoodsFinal(0.0D);
        avo.setToOrderedPaidAmountFinal(0.0D);
        avo.setToOrderedOutStockNum(0.0D);
        avo.setToOrderedOutStockGoodsNum(0.0D);
        avo.setToOrderedOutStockAmount(0.0D);
        avo.setToOrderedOutStockOrderNum(0.0D);
        avo.setOutStackLossNum(0.0D);
        avo.setOutStackLossGoodsNum(0.0D);
        avo.setOutStackLossMoney(0.0D);
        avo.setOutStackLossOrderNum(0.0D);
        avo.setToOrderedPaidGoodsNum(0.0D);
        avo.setToOrderedPaidOrderNum(0.0D);
        avo.setToOrderedPaidNum(0.0D);
        avo.setToOrderedPaidAmount(0.0D);
        avo.setToOrderedNotPaidGoodsNum(0.0D);
        avo.setToOrderedNotPaidOrderNum(0.0D);
        avo.setToOrderedNotPaidNum(0.0D);
        avo.setToOrderedNotPaidAmount(0.0D);
        avo.setToPaidGoodsNum(0.0D);
        avo.setToPaidOrderNum(0.0D);
        avo.setToPaidNum(0.0D);
        avo.setToPaidAmount(0.0D);
        avo.setSilentOrderToPaidGoodsNum(0.0D);
        avo.setSilentOrderToPaidOrderNum(0.0D);
        avo.setSilentOrderToPaidNum(0.0D);
        avo.setSilentOrderToPaidAmount(0.0D);
        avo.setSilentToFolowupGoodsNum(0.0D);
        avo.setSilentToFolowupOrderNum(0.0D);
        avo.setSilentToFolowupNum(0.0D);
        avo.setSilentToFolowupAmount(0.0D);
        avo.setAssitOrderCreateNum(0.0D);
        avo.setAssitOrderCreateAmount(0.0D);
        avo.setAssitOrderPayNum(0.0D);
        avo.setAssitOrderPayAmount(0.0D);
        avo.setAssitOrderFollowupNum(0.0D);
        avo.setAssitOrderFollowupAmount(0.0D);
        avo.setOutStockOrderBuyerNumFinal(0.0D);
        avo.setQueryToOutStock(0.0D);
        avo.setPaidNumTodayNext(0.0D);
        avo.setPaidNumFinal(0.0D);
        avo.setQueryToTomorrow(0.0D);
        avo.setOrderedToPaid(0.0D);
        avo.setOrderedToPaidFinal(0.0D);
        avo.setQueryToOrderedToday(0.0D);
        avo.setQueryToFinalOrdered(0.0D);
        avo.setConsultSessionNum(0.0D);
        avo.setReceiveSessionNum(0.0D);
        avo.setLoginDurationTime(0.0D);
        avo.setRceiveDurationTime(0.0D);
        avo.setDirectReceiveSessionNum(0.0D);
        avo.setForwardInSessionNum(0.0D);
        avo.setForwardOutSessionNum(0.0D);
        avo.setCustConsultSessionNum(0.0D);
        avo.setCsToCustSessionNum(0.0D);
        avo.setChatNum(0.0D);
        avo.setCustChatNum(0.0D);
        avo.setCsChatNum(0.0D);
        avo.setAnswerRatio(0.0D);
        avo.setCsWordNum(0.0D);
        avo.setAvgCsMsgSessionNum(0.0D);
        avo.setMaxReceiveSessionNum(0.0D);
        avo.setNonReplySessionNum(0.0D);
        avo.setResponseRate(0.0D);
        avo.setRapidAnswerRate(0.0D);
        avo.setLeaveMsgSessionNum(0.0D);
        avo.setLeaveMsgReceiveSessionNum(0.0D);
        avo.setLeaveMsgResponseRate(0.0D);
        avo.setAvgSessionDurationTime(0.0D);
        avo.setSlowRespSessionNum(0.0D);
        avo.setLongRespSessionNum(0.0D);
        avo.setSaleGuestAvgGoods(0.0D);
        avo.setSaleGoodsAvgAmount(0.0D);
        avo.setOutStockGuestItemNum(0.0D);
        avo.setOutStockItemAvgAmount(0.0D);
        avo.setVerySatisfiedNum(0.0D);
        avo.setSatisfiedNum(0.0D);
        avo.setGeneralNum(0.0D);
        avo.setDissatisfiedNum(0.0D);
        avo.setVeryDissatisfiedNum(0.0D);
        avo.setEvalReplyNum(0.0D);
        avo.setSatisfactionRate(0.0D);
        avo.setEvaluationRate(0.0D);
        avo.setInviteRate(0.0D);
        avo.setEvalSendNum(0.0D);
        avo.setHour0(0.0D);
        avo.setHour1(0.0D);
        avo.setHour2(0.0D);
        avo.setHour3(0.0D);
        avo.setHour4(0.0D);
        avo.setHour5(0.0D);
        avo.setHour6(0.0D);
        avo.setHour7(0.0D);
        avo.setHour8(0.0D);
        avo.setHour9(0.0D);
        avo.setHour10(0.0D);
        avo.setHour11(0.0D);
        avo.setHour12(0.0D);
        avo.setHour13(0.0D);
        avo.setHour14(0.0D);
        avo.setHour15(0.0D);
        avo.setHour16(0.0D);
        avo.setHour17(0.0D);
        avo.setHour18(0.0D);
        avo.setHour19(0.0D);
        avo.setHour20(0.0D);
        avo.setHour21(0.0D);
        avo.setHour22(0.0D);
        avo.setHour23(0.0D);
        avo.setAllDay(0.0D);
        avo.setGoodEvaluateNumPreSale(0.0D);
        avo.setNeutralEvaluateNumPreSale(0.0D);
        avo.setBadEvaluateNumPreSale(0.0D);
        avo.setGoodEvaluateNumBetSale(0.0D);
        avo.setNeutralEvaluateNumBetSale(0.0D);
        avo.setBadEvaluateNumBetSale(0.0D);
        avo.setGoodEvaluateNumAfterSale(0.0D);
        avo.setNeutralEvaluateNumAfterSale(0.0D);
        avo.setBadEvaluateNumAfterSale(0.0D);
        avo.setGoodEvaluateNumTotal(0.0D);
        avo.setNeutralEvaluateNumTotal(0.0D);
        avo.setBadEvaluateNumTotal(0.0D);
        avo.setApplyRefundNum(0.0D);
        avo.setApplyRefundProductNum(0.0D);
        avo.setApplyRefundBuyerNum(0.0D);
        avo.setApplyRefundAmount(0.0D);
        avo.setCompletedRefundNum(0.0D);
        avo.setCompletedRefundBuyerNum(0.0D);
        avo.setTransactionsNum(0.0D);
        avo.setRefundProductNum(0.0D);
        avo.setRefundPercent(0.0D);
        avo.setTeamSaleAmount(0.0D);
        avo.setOrderItemAvgAmount(0.0D);
        avo.setOrderedGoodsNumToday(0.0D);
        avo.setOrderedGuestAvgPrice(0.0D);
        avo.setOrderedGuestAvgAmount(0.0D);
        avo.setPaidAmountFinal(0.0D);
        avo.setPaidNumToday(0.0D);
        avo.setPaidGoodsNumToday(0.0D);
        avo.setSaleOrderSkuNum(0.0D);
        avo.setMedalNum(0.0D);
        avo.setWorkDay(0.0D);
        avo.setAvgFirstOnlineDateTime(null);
        avo.setAvgLastOfflineDateTime(null);
        avo.setFirstOnlineDateTime(null);
        avo.setLastOfflineDateTime(null);
        avo.setLoginTimesNum(0.0D);
        avo.setAvgLoginTimesNum(0.0D);
        avo.setAvgLoginDurationTime(0.0D);
        avo.setAvgRceiveDurationTime(0.0D);
        avo.setHangupDurationTime(0.0D);
        avo.setAvgHangupDurationTime(0.0D);
        avo.setRceiveTimeRate(0.0D);
        avo.setOfflineDurationTime(0.0D);
        avo.setChatRoundNumNoLeave(0L);
        avo.setEmptyChatNum(0);
        return avo;

    }
    @Property(value = "去除留言接待的聊天回合数", direct = true)
    private Long chatRoundNumNoLeave;

    private List<String> notFinalData;

    private  Double avgRespInQuickTime;

    private  Double sessionNum;

    @Property(value = "咨询人数", direct = true)
    private  Double consultNum;

    @Property(value = "接待人数", direct = true)
    private  Double receiveNum;

    @Property(value = "询单人数", direct = true)
    private  Double enquiryNum;

    @Property(value = "首次平均响应(s)")
    private  Double avgRespTimeFirst;

    @Property(value = "平均响应(s)")
    private  Double avgRespTime;

    @Property(value = "销售额、是否扣除退款、是否扣除邮费", direct = true)
    private  Double saleAmount;

    @Property(value = "销售量、是否扣除退款件数")
    private  Double saleGoodsNum;

    @Property(value = "销售人数", direct = true)
    private  Double saleBuyerNum;

    @Property(value = "销售订单数", direct = true)
    private  Double saleOrderNum;

    @Property(value = "个人销售额占比")
    private  Double saleAmountPercent;

    @Property(value = "询单→付款转化率")
    private  Double queryToFinalPaid;

    @Property(value = "销售客单价（元/件）")
    private  Double saleGuestAvgAmount;

    @Property(value = "出库金额", direct = true)
    private  Double outStockAmount;

    @Property(value = "出库件数", direct = true)
    private  Double outStockGoodsNum;

    @Property(value = "出库人数", direct = true)
    private  Double outStockNum;

    @Property(value = "出库订单数", direct = true)
    private  Double outStockOrderNum;

    @Property(value = "个人出库金额占比")
    private  Double personalOutStockAmountPercent;

    @Property(value = "出库客单价（元/人）")
    private  Double outStockGuestAvgAmount;

    @Property(value = "完成退款金额", direct = true)
    private  Double completedRefundAmount;

    @Property(value = "完成退款件数", direct = true)
    private  Double completedRefundProductNum;

    @Property(value = "邮费总额", direct = true)
    private  Double postFee;

    @Property(value = "询单流失人数")
    private  Double enquiryLossNum;

    @Property(value = "询单→当日下单人数", direct = true)
    private  Double orderedNumToday;

    @Property(value = "询单→当日下单金额", direct = true)
    private  Double orderedAmountToday;

    @Property(value = "询单→最终下单人数", direct = true)
    private  Double orderedNumFinal;

    @Property(value = "询单→最终下单金额", direct = true)
    private  Double orderedAmountFinal;

    @Property(value = "客服落实下单人数", direct = true)
    private  Double toOrderedNum;

    @Property(value = "客服落实下单件数", direct = true)
    private  Double toOrderedGoodsNum;

    @Property(value = "客服落实下单金额", direct = true)
    private  Double toOrderedAmount;

    @Property(value = "客服落实下单订单数", direct = true)
    private  Double toOrderedOrderNum;

    @Property(value = "客服落实下单未付款人数")
    private  Double paidLossNum;

    @Property(value = "客服落实下单未付款件数")
    private  Double paidLossGoodsNum;

    @Property(value = "客服落实下单未付款金额")
    private  Double paidLossMoney;

    @Property(value = "下单→当日付款人数", direct = true)
    private  Double toOrderedPaidNumToday;

    @Property(value = "下单→当日付款件数", direct = true)
    private  Double toOrderedPaidGoodsToday;

    @Property(value = "下单→当日付款金额", direct = true)
    private  Double toOrderedPaidAmountToday;

    @Property(value = "下单→最终付款人数", direct = true)
    private  Double toOrderedPaidNumFinal;

    @Property(value = "下单→最终付款件数", direct = true)
    private  Double toOrderedPaidGoodsFinal;

    @Property(value = "下单→最终付款金额", direct = true)
    private  Double toOrderedPaidAmountFinal;

    @Property(value = "下单→出库人数")
    private  Double toOrderedOutStockNum;

    @Property(value = "下单→出库件数")
    private  Double toOrderedOutStockGoodsNum;

    @Property(value = "下单→出库金额")
    private  Double toOrderedOutStockAmount;

    @Property(value = "下单→出库订单数")
    private  Double toOrderedOutStockOrderNum;

    @Property(value = "下单→出库流失人数")
    private  Double outStackLossNum;

    @Property(value = "下单→出库流失件数")
    private  Double outStackLossGoodsNum;

    @Property(value = "下单→出库流失金额")
    private  Double outStackLossMoney;

    @Property(value = "下单→出库流失订单数")
    private  Double outStackLossOrderNum;

    @Property(value = "本人落实下单付款-件数")
    private  Double toOrderedPaidGoodsNum;

    @Property(value = "本人落实下单付款-订单数")
    private  Double toOrderedPaidOrderNum;

    @Property(value = "本人落实下单付款-人数")
    private  Double toOrderedPaidNum;

    @Property(value = "本人落实下单付款-金额")
    private  Double toOrderedPaidAmount;

    @Property(value = "本人落实下单他人落实付款-件数")
    private  Double toOrderedNotPaidGoodsNum;

    @Property(value = "本人落实下单他人落实付款-订单数")
    private  Double toOrderedNotPaidOrderNum;

    @Property(value = "本人落实下单他人落实付款-人数 ")
    private  Double toOrderedNotPaidNum;

    @Property(value = "本人落实下单他人落实付款-金额 ")
    private  Double toOrderedNotPaidAmount;

    @Property(value = "他人落实下单本人落实付款-件数 ")
    private  Double toPaidGoodsNum;

    @Property(value = "他人落实下单本人落实付款-订单数 ")
    private  Double toPaidOrderNum;

    @Property(value = "他人落实下单本人落实付款-人数")
    private  Double toPaidNum;

    @Property(value = "他人落实下单本人落实付款-金额")
    private  Double toPaidAmount;

    @Property(value = "静默下单本人落实付款-件数 ")
    private  Double silentOrderToPaidGoodsNum;

    @Property(value = "静默下单本人落实付款-订单数 ")
    private  Double silentOrderToPaidOrderNum;

    @Property(value = "静默下单本人落实付款-人数")
    private  Double silentOrderToPaidNum;

    @Property(value = "静默下单本人落实付款-金额")
    private  Double silentOrderToPaidAmount;

    @Property(value = "全静默订单本人跟进-件数 ")
    private  Double silentToFolowupGoodsNum;

    @Property(value = "全静默订单本人跟进-订单数 ")
    private  Double silentToFolowupOrderNum;

    @Property(value = "全静默订单本人跟进-人数")
    private  Double silentToFolowupNum;

    @Property(value = "全静默订单本人跟进-金额")
    private  Double silentToFolowupAmount;

    @Property(value = "协助下单人数")
    private  Double assitOrderCreateNum;

    @Property(value = "协助下单金额")
    private  Double assitOrderCreateAmount;

    @Property(value = "协助付款人数")
    private  Double assitOrderPayNum;

    @Property(value = "协助付款金额")
    private  Double assitOrderPayAmount;

    @Property(value = "协助跟进人数")
    private  Double assitOrderFollowupNum;

    @Property(value = "协助跟进金额")
    private  Double assitOrderFollowupAmount;

    @Property(value = "最终出库人数", direct = true)
    private  Double outStockOrderBuyerNumFinal;

    @Property(value = "询单→出库转化率")
    private  Double queryToOutStock;

    @Property(value = "询单→次日付款人数", direct = true)
    private  Double paidNumTodayNext;

    @Property(value = "询单→最终付款人数", direct = true)
    private  Double paidNumFinal;

    @Property(value = "询单→次日付款转化率")
    private  Double queryToTomorrow;

    @Property(value = "下单→当日付款转化率")
    private  Double orderedToPaid;

    @Property(value = "下单→付款转化率")
    private  Double orderedToPaidFinal;

    @Property(value = "询单→当日下单转化率")
    private  Double queryToOrderedToday;

    @Property(value = "询单→下单转化率")
    private  Double queryToFinalOrdered;

    @Property(value = "咨询量", direct = true)
    private  Double consultSessionNum;

    @Property(value = "接待量", direct = true)
    private  Double receiveSessionNum;

    @Property(value = "登录时长")
    private  Double loginDurationTime;

    @Property(value = "接待时长")
    private  Double rceiveDurationTime;

    @Property(value = "直接接待量", direct = true)
    private  Double directReceiveSessionNum;

    @Property(value = "转入量", direct = true)
    private  Double forwardInSessionNum;

    @Property(value = "转出量", direct = true)
    private  Double forwardOutSessionNum;

    @Property(value = "顾客发起量", direct = true)
    private  Double custConsultSessionNum;

    @Property(value = "客服主动跟进量", direct = true)
    private  Double csToCustSessionNum;

    @Property(value = "总消息数", direct = true)
    private  Double chatNum;

    @Property(value = "回合数", direct = true)
    private  Integer chatRoundNum;

    @Property(value = "顾客消息数", direct = true)
    private  Double custChatNum;

    @Property(value = "客服消息数", direct = true)
    private  Double csChatNum;

    @Property(value = "答问比")
    private  Double answerRatio;

    @Property(value = "客服字数", direct = true)
    private  Double csWordNum;

    @Property(value = "平均回复消息数")
    private  Double avgCsMsgSessionNum;

    @Property(value = "最大同时接待量", direct = true)
    private  Double maxReceiveSessionNum;

    @Property(value = "未回复量", direct = true)
    private  Double nonReplySessionNum;

    @Property(value = "回复率")
    private  Double responseRate;

    @Property(value = "快速应答率", direct = true)
    private  Double rapidAnswerRate;

    @Property(value = "留言分配量")
    private  Double leaveMsgSessionNum;

    @Property(value = "留言接待量")
    private  Double leaveMsgReceiveSessionNum;

    @Property(value = "留言响应率")
    private  Double leaveMsgResponseRate;

    @Property(value = "平均会话时长")
    private  Double avgSessionDurationTime;

    @Property(value = "慢响应量")
    private  Double slowRespSessionNum;

    @Property(value = "长接待量")
    private  Double longRespSessionNum;

    @Property(value = "销售客件数（件/人）")
    private  Double saleGuestAvgGoods;

    @Property(value = "销售件均价（元/件）")
    private  Double saleGoodsAvgAmount;

    @Property(value = "出库客件数（件/人）")
    private  Double outStockGuestItemNum;

    @Property(value = "出库件均价（元/件）")
    private  Double outStockItemAvgAmount;

    @Property(value = "满意度-非常满意", direct = true)
    private  Double verySatisfiedNum;

    @Property(value = "满意度-满意", direct = true)
    private  Double satisfiedNum;

    @Property(value = "满意度-一般", direct = true)
    private  Double generalNum;

    @Property(value = "满意度-不满意", direct = true)
    private  Double dissatisfiedNum;

    @Property(value = "满意度-非常不满意", direct = true)
    private  Double veryDissatisfiedNum;

    @Property(value = "评价量")
    private  Double evalReplyNum;

    @Property(value = "满意率")
    private  Double satisfactionRate;

    @Property(value = "评价率", direct = true)
    private  Double evaluationRate;

    @Property(value = "邀评率", direct = true)
    private  Double inviteRate;

    @Property(value = "邀评量", direct = true)
    private  Double evalSendNum;

    private  Double hour0;

    private  Double hour1;

    private  Double hour2;

    private  Double hour3;

    private  Double hour4;

    private  Double hour5;

    private  Double hour6;

    private  Double hour7;

    private  Double hour8;

    private  Double hour9;

    private  Double hour10;

    private  Double hour11;

    private  Double hour12;

    private  Double hour13;

    private  Double hour14;

    private  Double hour15;

    private  Double hour16;

    private  Double hour17;

    private  Double hour18;

    private  Double hour19;

    private  Double hour20;

    private  Double hour21;

    private  Double hour22;

    private  Double hour23;

    @Property(value = "全天分时接待", direct = true)
    private  Double allDay;

    @Property(value = "售前好评数", direct = true)
    private  Double goodEvaluateNumPreSale;

    @Property(value = "售前中评数", direct = true)
    private  Double neutralEvaluateNumPreSale;

    @Property(value = "售前差评数", direct = true)
    private  Double badEvaluateNumPreSale;

    @Property(value = "售中好评数", direct = true)
    private  Double goodEvaluateNumBetSale;

    @Property(value = "售中中评数", direct = true)
    private  Double neutralEvaluateNumBetSale;

    @Property(value = "售中差评数", direct = true)
    private  Double badEvaluateNumBetSale;

    @Property(value = "售后好评数", direct = true)
    private  Double goodEvaluateNumAfterSale;

    @Property(value = "售后中评数", direct = true)
    private  Double neutralEvaluateNumAfterSale;

    @Property(value = "售后差评数", direct = true)
    private  Double badEvaluateNumAfterSale;

    @Property(value = "好评总数", direct = true)
    private  Double goodEvaluateNumTotal;

    @Property(value = "中评总数", direct = true)
    private  Double neutralEvaluateNumTotal;

    @Property(value = "差评总数", direct = true)
    private  Double badEvaluateNumTotal;

    @Property(value = "申请退款笔数", direct = true)
    private  Double applyRefundNum;

    @Property(value = "申请退款件数", direct = true)
    private  Double applyRefundProductNum;

    @Property(value = "申请退款人数", direct = true)
    private  Double applyRefundBuyerNum;

    @Property(value = "申请退款金额", direct = true)
    private  Double applyRefundAmount;

    @Property(value = "完成退款笔数", direct = true)
    private  Double completedRefundNum;

    @Property(value = "完成退款人数", direct = true)
    private  Double completedRefundBuyerNum;

    @Property(value = "成交件数", direct = true)
    private  Double transactionsNum;

    @Property(value = "退款件数", direct = true)
    private  Double refundProductNum;

    @Property(value = "退款率")
    private  Double refundPercent;

    @Property(value = "团队销售额")
    private  Double teamSaleAmount;

    @Property(value = "下单件均价（元/件）")
    private  Double orderItemAvgAmount;

    @Property(value = "询单→当日的下单件数", direct = true)
    private  Double orderedGoodsNumToday;

    @Property(value = "下单客单价（元/人）")
    private  Double orderedGuestAvgPrice;

    @Property(value = "下单客件数（件/人）")
    private  Double orderedGuestAvgAmount;

    @Property(value = "询单→最终付款金额", direct = true)
    private  Double paidAmountFinal;

    @Property(value = "当日询单 当天落实下单 并且 付了款的人数", direct = true)
    private  Double paidNumToday;

    @Property(value = "下单当日付款的件数", direct = true)
    private  Double paidGoodsNumToday;

    @Property(value = "成交笔数", direct = true)
    private  Double saleOrderSkuNum;

    @Property(value = "勋章评价", direct = true)
    private  Double medalNum;

    @Property(value = "上班天数")
    private  Double workDay;

    @Property(value = "平均上线时间")
    private  LocalTime avgFirstOnlineDateTime;

    @Property(value = "平均下线时间")
    private  LocalTime avgLastOfflineDateTime;

    @Property(value = "最早上线时间")
    private  LocalTime firstOnlineDateTime;

    @Property(value = "最晚下线时间")
    private  LocalTime lastOfflineDateTime;

    @Property(value = "登录次数")
    private  Double loginTimesNum;

    @Property(value = "日均登录次数")
    private  Double avgLoginTimesNum;

    @Property(value = "日均登录时长")
    private  Double avgLoginDurationTime;

    @Property(value = "日均接待时长")
    private  Double avgRceiveDurationTime;

    @Property(value = "挂起时长")
    private  Double hangupDurationTime;

    @Property(value = "日均挂起时长")
    private  Double avgHangupDurationTime;

    @Property(value = "接待时长占比")
    private  Double rceiveTimeRate;

    @Property(value = "上班期间离线时长")
    private  Double offlineDurationTime;

    private Double saleAmountOrigin;

    private Double respTimeFirstCount;

    private Double respTimeCount;

    private Double sessionDurationTimeCount;

    @Property(value = "空聊天数", direct = true)
    private Integer emptyChatNum;//空聊天数


    public Integer getEmptyChatNum() {
        return emptyChatNum;
    }

    public void setEmptyChatNum(Integer emptyChatNum) {
        this.emptyChatNum = emptyChatNum;
    }

    public List<String> getNotFinalData() {
        return notFinalData;
    }

    public void setNotFinalData(List<String> notFinalData) {
        this.notFinalData = notFinalData;
    }

    public Double getRespTimeFirstCount() {
        return respTimeFirstCount;
    }

    public void setRespTimeFirstCount(Double respTimeFirstCount) {
        this.respTimeFirstCount = respTimeFirstCount;
    }

    public Double getRespTimeCount() {
        return respTimeCount;
    }

    public void setRespTimeCount(Double respTimeCount) {
        this.respTimeCount = respTimeCount;
    }

    public Double getSessionDurationTimeCount() {
        return sessionDurationTimeCount;
    }

    public void setSessionDurationTimeCount(Double sessionDurationTimeCount) {
        this.sessionDurationTimeCount = sessionDurationTimeCount;
    }

    public Double getSaleAmountOrigin() {
        return saleAmountOrigin;
    }

    public void setSaleAmountOrigin(Double saleAmountOrigin) {
        this.saleAmountOrigin = saleAmountOrigin;
    }


    public Double getAvgRespInQuickTime() {
        return avgRespInQuickTime;
    }

    public void setAvgRespInQuickTime(Double avgRespInQuickTime) {
        this.avgRespInQuickTime = avgRespInQuickTime;
    }

    public Double getSessionNum() {
        return sessionNum;
    }

    public void setSessionNum(Double sessionNum) {
        this.sessionNum = sessionNum;
    }

    public Double getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(Double consultNum) {
        this.consultNum = consultNum;
    }

    public Double getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Double receiveNum) {
        this.receiveNum = receiveNum;
    }

    public Double getEnquiryNum() {
        return enquiryNum;
    }

    public void setEnquiryNum(Double enquiryNum) {
        this.enquiryNum = enquiryNum;
    }

    public Double getAvgRespTimeFirst() {
        return avgRespTimeFirst;
    }

    public void setAvgRespTimeFirst(Double avgRespTimeFirst) {
        this.avgRespTimeFirst = avgRespTimeFirst;
    }

    public Double getAvgRespTime() {
        return avgRespTime;
    }

    public void setAvgRespTime(Double avgRespTime) {
        this.avgRespTime = avgRespTime;
    }

    public Double getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(Double saleAmount) {
        this.saleAmount = saleAmount;
    }

    public Double getSaleGoodsNum() {
        return saleGoodsNum;
    }

    public void setSaleGoodsNum(Double saleGoodsNum) {
        this.saleGoodsNum = saleGoodsNum;
    }

    public Double getSaleBuyerNum() {
        return saleBuyerNum;
    }

    public void setSaleBuyerNum(Double saleBuyerNum) {
        this.saleBuyerNum = saleBuyerNum;
    }

    public Double getSaleOrderNum() {
        return saleOrderNum;
    }

    public void setSaleOrderNum(Double saleOrderNum) {
        this.saleOrderNum = saleOrderNum;
    }

    public Double getSaleAmountPercent() {
        return saleAmountPercent;
    }

    public void setSaleAmountPercent(Double saleAmountPercent) {
        this.saleAmountPercent = saleAmountPercent;
    }

    public Double getQueryToFinalPaid() {
        return queryToFinalPaid;
    }

    public void setQueryToFinalPaid(Double queryToFinalPaid) {
        this.queryToFinalPaid = queryToFinalPaid;
    }

    public Double getSaleGuestAvgAmount() {
        return saleGuestAvgAmount;
    }

    public void setSaleGuestAvgAmount(Double saleGuestAvgAmount) {
        this.saleGuestAvgAmount = saleGuestAvgAmount;
    }

    public Double getOutStockAmount() {
        return outStockAmount;
    }

    public void setOutStockAmount(Double outStockAmount) {
        this.outStockAmount = outStockAmount;
    }

    public Double getOutStockGoodsNum() {
        return outStockGoodsNum;
    }

    public void setOutStockGoodsNum(Double outStockGoodsNum) {
        this.outStockGoodsNum = outStockGoodsNum;
    }

    public Double getOutStockNum() {
        return outStockNum;
    }

    public void setOutStockNum(Double outStockNum) {
        this.outStockNum = outStockNum;
    }

    public Double getOutStockOrderNum() {
        return outStockOrderNum;
    }

    public void setOutStockOrderNum(Double outStockOrderNum) {
        this.outStockOrderNum = outStockOrderNum;
    }

    public Double getPersonalOutStockAmountPercent() {
        return personalOutStockAmountPercent;
    }

    public void setPersonalOutStockAmountPercent(Double personalOutStockAmountPercent) {
        this.personalOutStockAmountPercent = personalOutStockAmountPercent;
    }

    public Double getOutStockGuestAvgAmount() {
        return outStockGuestAvgAmount;
    }

    public void setOutStockGuestAvgAmount(Double outStockGuestAvgAmount) {
        this.outStockGuestAvgAmount = outStockGuestAvgAmount;
    }

    public Double getCompletedRefundAmount() {
        return completedRefundAmount;
    }

    public void setCompletedRefundAmount(Double completedRefundAmount) {
        this.completedRefundAmount = completedRefundAmount;
    }

    public Double getCompletedRefundProductNum() {
        return completedRefundProductNum;
    }

    public void setCompletedRefundProductNum(Double completedRefundProductNum) {
        this.completedRefundProductNum = completedRefundProductNum;
    }

    public Double getPostFee() {
        return postFee;
    }

    public void setPostFee(Double postFee) {
        this.postFee = postFee;
    }

    public Double getEnquiryLossNum() {
        return enquiryLossNum;
    }

    public void setEnquiryLossNum(Double enquiryLossNum) {
        this.enquiryLossNum = enquiryLossNum;
    }

    public Double getOrderedNumToday() {
        return orderedNumToday;
    }

    public void setOrderedNumToday(Double orderedNumToday) {
        this.orderedNumToday = orderedNumToday;
    }

    public Double getOrderedAmountToday() {
        return orderedAmountToday;
    }

    public void setOrderedAmountToday(Double orderedAmountToday) {
        this.orderedAmountToday = orderedAmountToday;
    }

    public Double getOrderedNumFinal() {
        return orderedNumFinal;
    }

    public void setOrderedNumFinal(Double orderedNumFinal) {
        this.orderedNumFinal = orderedNumFinal;
    }

    public Double getOrderedAmountFinal() {
        return orderedAmountFinal;
    }

    public void setOrderedAmountFinal(Double orderedAmountFinal) {
        this.orderedAmountFinal = orderedAmountFinal;
    }

    public Double getToOrderedNum() {
        return toOrderedNum;
    }

    public void setToOrderedNum(Double toOrderedNum) {
        this.toOrderedNum = toOrderedNum;
    }

    public Double getToOrderedGoodsNum() {
        return toOrderedGoodsNum;
    }

    public void setToOrderedGoodsNum(Double toOrderedGoodsNum) {
        this.toOrderedGoodsNum = toOrderedGoodsNum;
    }

    public Double getToOrderedAmount() {
        return toOrderedAmount;
    }

    public void setToOrderedAmount(Double toOrderedAmount) {
        this.toOrderedAmount = toOrderedAmount;
    }

    public Double getToOrderedOrderNum() {
        return toOrderedOrderNum;
    }

    public void setToOrderedOrderNum(Double toOrderedOrderNum) {
        this.toOrderedOrderNum = toOrderedOrderNum;
    }

    public Double getPaidLossNum() {
        return paidLossNum;
    }

    public void setPaidLossNum(Double paidLossNum) {
        this.paidLossNum = paidLossNum;
    }

    public Double getPaidLossGoodsNum() {
        return paidLossGoodsNum;
    }

    public void setPaidLossGoodsNum(Double paidLossGoodsNum) {
        this.paidLossGoodsNum = paidLossGoodsNum;
    }

    public Double getPaidLossMoney() {
        return paidLossMoney;
    }

    public void setPaidLossMoney(Double paidLossMoney) {
        this.paidLossMoney = paidLossMoney;
    }

    public Double getToOrderedPaidNumToday() {
        return toOrderedPaidNumToday;
    }

    public void setToOrderedPaidNumToday(Double toOrderedPaidNumToday) {
        this.toOrderedPaidNumToday = toOrderedPaidNumToday;
    }

    public Double getToOrderedPaidGoodsToday() {
        return toOrderedPaidGoodsToday;
    }

    public void setToOrderedPaidGoodsToday(Double toOrderedPaidGoodsToday) {
        this.toOrderedPaidGoodsToday = toOrderedPaidGoodsToday;
    }

    public Double getToOrderedPaidAmountToday() {
        return toOrderedPaidAmountToday;
    }

    public void setToOrderedPaidAmountToday(Double toOrderedPaidAmountToday) {
        this.toOrderedPaidAmountToday = toOrderedPaidAmountToday;
    }

    public Double getToOrderedPaidNumFinal() {
        return toOrderedPaidNumFinal;
    }

    public void setToOrderedPaidNumFinal(Double toOrderedPaidNumFinal) {
        this.toOrderedPaidNumFinal = toOrderedPaidNumFinal;
    }

    public Double getToOrderedPaidGoodsFinal() {
        return toOrderedPaidGoodsFinal;
    }

    public void setToOrderedPaidGoodsFinal(Double toOrderedPaidGoodsFinal) {
        this.toOrderedPaidGoodsFinal = toOrderedPaidGoodsFinal;
    }

    public Double getToOrderedPaidAmountFinal() {
        return toOrderedPaidAmountFinal;
    }

    public void setToOrderedPaidAmountFinal(Double toOrderedPaidAmountFinal) {
        this.toOrderedPaidAmountFinal = toOrderedPaidAmountFinal;
    }

    public Double getToOrderedOutStockNum() {
        return toOrderedOutStockNum;
    }

    public void setToOrderedOutStockNum(Double toOrderedOutStockNum) {
        this.toOrderedOutStockNum = toOrderedOutStockNum;
    }

    public Double getToOrderedOutStockGoodsNum() {
        return toOrderedOutStockGoodsNum;
    }

    public void setToOrderedOutStockGoodsNum(Double toOrderedOutStockGoodsNum) {
        this.toOrderedOutStockGoodsNum = toOrderedOutStockGoodsNum;
    }

    public Double getToOrderedOutStockAmount() {
        return toOrderedOutStockAmount;
    }

    public void setToOrderedOutStockAmount(Double toOrderedOutStockAmount) {
        this.toOrderedOutStockAmount = toOrderedOutStockAmount;
    }

    public Double getToOrderedOutStockOrderNum() {
        return toOrderedOutStockOrderNum;
    }

    public void setToOrderedOutStockOrderNum(Double toOrderedOutStockOrderNum) {
        this.toOrderedOutStockOrderNum = toOrderedOutStockOrderNum;
    }

    public Double getOutStackLossNum() {
        return outStackLossNum;
    }

    public void setOutStackLossNum(Double outStackLossNum) {
        this.outStackLossNum = outStackLossNum;
    }

    public Double getOutStackLossGoodsNum() {
        return outStackLossGoodsNum;
    }

    public void setOutStackLossGoodsNum(Double outStackLossGoodsNum) {
        this.outStackLossGoodsNum = outStackLossGoodsNum;
    }

    public Double getOutStackLossMoney() {
        return outStackLossMoney;
    }

    public void setOutStackLossMoney(Double outStackLossMoney) {
        this.outStackLossMoney = outStackLossMoney;
    }

    public Double getOutStackLossOrderNum() {
        return outStackLossOrderNum;
    }

    public void setOutStackLossOrderNum(Double outStackLossOrderNum) {
        this.outStackLossOrderNum = outStackLossOrderNum;
    }

    public Double getToOrderedPaidGoodsNum() {
        return toOrderedPaidGoodsNum;
    }

    public void setToOrderedPaidGoodsNum(Double toOrderedPaidGoodsNum) {
        this.toOrderedPaidGoodsNum = toOrderedPaidGoodsNum;
    }

    public Double getToOrderedPaidOrderNum() {
        return toOrderedPaidOrderNum;
    }

    public void setToOrderedPaidOrderNum(Double toOrderedPaidOrderNum) {
        this.toOrderedPaidOrderNum = toOrderedPaidOrderNum;
    }

    public Double getToOrderedPaidNum() {
        return toOrderedPaidNum;
    }

    public void setToOrderedPaidNum(Double toOrderedPaidNum) {
        this.toOrderedPaidNum = toOrderedPaidNum;
    }

    public Double getToOrderedPaidAmount() {
        return toOrderedPaidAmount;
    }

    public void setToOrderedPaidAmount(Double toOrderedPaidAmount) {
        this.toOrderedPaidAmount = toOrderedPaidAmount;
    }

    public Double getToOrderedNotPaidGoodsNum() {
        return toOrderedNotPaidGoodsNum;
    }

    public void setToOrderedNotPaidGoodsNum(Double toOrderedNotPaidGoodsNum) {
        this.toOrderedNotPaidGoodsNum = toOrderedNotPaidGoodsNum;
    }

    public Double getToOrderedNotPaidOrderNum() {
        return toOrderedNotPaidOrderNum;
    }

    public void setToOrderedNotPaidOrderNum(Double toOrderedNotPaidOrderNum) {
        this.toOrderedNotPaidOrderNum = toOrderedNotPaidOrderNum;
    }

    public Double getToOrderedNotPaidNum() {
        return toOrderedNotPaidNum;
    }

    public void setToOrderedNotPaidNum(Double toOrderedNotPaidNum) {
        this.toOrderedNotPaidNum = toOrderedNotPaidNum;
    }

    public Double getToOrderedNotPaidAmount() {
        return toOrderedNotPaidAmount;
    }

    public void setToOrderedNotPaidAmount(Double toOrderedNotPaidAmount) {
        this.toOrderedNotPaidAmount = toOrderedNotPaidAmount;
    }

    public Double getToPaidGoodsNum() {
        return toPaidGoodsNum;
    }

    public void setToPaidGoodsNum(Double toPaidGoodsNum) {
        this.toPaidGoodsNum = toPaidGoodsNum;
    }

    public Double getToPaidOrderNum() {
        return toPaidOrderNum;
    }

    public void setToPaidOrderNum(Double toPaidOrderNum) {
        this.toPaidOrderNum = toPaidOrderNum;
    }

    public Double getToPaidNum() {
        return toPaidNum;
    }

    public void setToPaidNum(Double toPaidNum) {
        this.toPaidNum = toPaidNum;
    }

    public Double getToPaidAmount() {
        return toPaidAmount;
    }

    public void setToPaidAmount(Double toPaidAmount) {
        this.toPaidAmount = toPaidAmount;
    }

    public Double getSilentOrderToPaidGoodsNum() {
        return silentOrderToPaidGoodsNum;
    }

    public void setSilentOrderToPaidGoodsNum(Double silentOrderToPaidGoodsNum) {
        this.silentOrderToPaidGoodsNum = silentOrderToPaidGoodsNum;
    }

    public Double getSilentOrderToPaidOrderNum() {
        return silentOrderToPaidOrderNum;
    }

    public void setSilentOrderToPaidOrderNum(Double silentOrderToPaidOrderNum) {
        this.silentOrderToPaidOrderNum = silentOrderToPaidOrderNum;
    }

    public Double getSilentOrderToPaidNum() {
        return silentOrderToPaidNum;
    }

    public void setSilentOrderToPaidNum(Double silentOrderToPaidNum) {
        this.silentOrderToPaidNum = silentOrderToPaidNum;
    }

    public Double getSilentOrderToPaidAmount() {
        return silentOrderToPaidAmount;
    }

    public void setSilentOrderToPaidAmount(Double silentOrderToPaidAmount) {
        this.silentOrderToPaidAmount = silentOrderToPaidAmount;
    }

    public Double getSilentToFolowupGoodsNum() {
        return silentToFolowupGoodsNum;
    }

    public void setSilentToFolowupGoodsNum(Double silentToFolowupGoodsNum) {
        this.silentToFolowupGoodsNum = silentToFolowupGoodsNum;
    }

    public Double getSilentToFolowupOrderNum() {
        return silentToFolowupOrderNum;
    }

    public void setSilentToFolowupOrderNum(Double silentToFolowupOrderNum) {
        this.silentToFolowupOrderNum = silentToFolowupOrderNum;
    }

    public Double getSilentToFolowupNum() {
        return silentToFolowupNum;
    }

    public void setSilentToFolowupNum(Double silentToFolowupNum) {
        this.silentToFolowupNum = silentToFolowupNum;
    }

    public Double getSilentToFolowupAmount() {
        return silentToFolowupAmount;
    }

    public void setSilentToFolowupAmount(Double silentToFolowupAmount) {
        this.silentToFolowupAmount = silentToFolowupAmount;
    }

    public Double getAssitOrderCreateNum() {
        return assitOrderCreateNum;
    }

    public void setAssitOrderCreateNum(Double assitOrderCreateNum) {
        this.assitOrderCreateNum = assitOrderCreateNum;
    }

    public Double getAssitOrderCreateAmount() {
        return assitOrderCreateAmount;
    }

    public void setAssitOrderCreateAmount(Double assitOrderCreateAmount) {
        this.assitOrderCreateAmount = assitOrderCreateAmount;
    }

    public Double getAssitOrderPayNum() {
        return assitOrderPayNum;
    }

    public void setAssitOrderPayNum(Double assitOrderPayNum) {
        this.assitOrderPayNum = assitOrderPayNum;
    }

    public Double getAssitOrderPayAmount() {
        return assitOrderPayAmount;
    }

    public void setAssitOrderPayAmount(Double assitOrderPayAmount) {
        this.assitOrderPayAmount = assitOrderPayAmount;
    }

    public Double getAssitOrderFollowupNum() {
        return assitOrderFollowupNum;
    }

    public void setAssitOrderFollowupNum(Double assitOrderFollowupNum) {
        this.assitOrderFollowupNum = assitOrderFollowupNum;
    }

    public Double getAssitOrderFollowupAmount() {
        return assitOrderFollowupAmount;
    }

    public void setAssitOrderFollowupAmount(Double assitOrderFollowupAmount) {
        this.assitOrderFollowupAmount = assitOrderFollowupAmount;
    }

    public Double getOutStockOrderBuyerNumFinal() {
        return outStockOrderBuyerNumFinal;
    }

    public void setOutStockOrderBuyerNumFinal(Double outStockOrderBuyerNumFinal) {
        this.outStockOrderBuyerNumFinal = outStockOrderBuyerNumFinal;
    }

    public Double getQueryToOutStock() {
        return queryToOutStock;
    }

    public void setQueryToOutStock(Double queryToOutStock) {
        this.queryToOutStock = queryToOutStock;
    }

    public Double getPaidNumTodayNext() {
        return paidNumTodayNext;
    }

    public void setPaidNumTodayNext(Double paidNumTodayNext) {
        this.paidNumTodayNext = paidNumTodayNext;
    }

    public Double getPaidNumFinal() {
        return paidNumFinal;
    }

    public void setPaidNumFinal(Double paidNumFinal) {
        this.paidNumFinal = paidNumFinal;
    }

    public Double getQueryToTomorrow() {
        return queryToTomorrow;
    }

    public void setQueryToTomorrow(Double queryToTomorrow) {
        this.queryToTomorrow = queryToTomorrow;
    }

    public Double getOrderedToPaid() {
        return orderedToPaid;
    }

    public void setOrderedToPaid(Double orderedToPaid) {
        this.orderedToPaid = orderedToPaid;
    }

    public Double getOrderedToPaidFinal() {
        return orderedToPaidFinal;
    }

    public void setOrderedToPaidFinal(Double orderedToPaidFinal) {
        this.orderedToPaidFinal = orderedToPaidFinal;
    }

    public Double getQueryToOrderedToday() {
        return queryToOrderedToday;
    }

    public void setQueryToOrderedToday(Double queryToOrderedToday) {
        this.queryToOrderedToday = queryToOrderedToday;
    }

    public Double getQueryToFinalOrdered() {
        return queryToFinalOrdered;
    }

    public void setQueryToFinalOrdered(Double queryToFinalOrdered) {
        this.queryToFinalOrdered = queryToFinalOrdered;
    }

    public Double getConsultSessionNum() {
        return consultSessionNum;
    }

    public void setConsultSessionNum(Double consultSessionNum) {
        this.consultSessionNum = consultSessionNum;
    }

    public Double getReceiveSessionNum() {
        return receiveSessionNum;
    }

    public void setReceiveSessionNum(Double receiveSessionNum) {
        this.receiveSessionNum = receiveSessionNum;
    }

    public Double getLoginDurationTime() {
        return loginDurationTime;
    }

    public void setLoginDurationTime(Double loginDurationTime) {
        this.loginDurationTime = loginDurationTime;
    }

    public Double getRceiveDurationTime() {
        return rceiveDurationTime;
    }

    public void setRceiveDurationTime(Double rceiveDurationTime) {
        this.rceiveDurationTime = rceiveDurationTime;
    }

    public Double getDirectReceiveSessionNum() {
        return directReceiveSessionNum;
    }

    public void setDirectReceiveSessionNum(Double directReceiveSessionNum) {
        this.directReceiveSessionNum = directReceiveSessionNum;
    }

    public Double getForwardInSessionNum() {
        return forwardInSessionNum;
    }

    public void setForwardInSessionNum(Double forwardInSessionNum) {
        this.forwardInSessionNum = forwardInSessionNum;
    }

    public Double getForwardOutSessionNum() {
        return forwardOutSessionNum;
    }

    public void setForwardOutSessionNum(Double forwardOutSessionNum) {
        this.forwardOutSessionNum = forwardOutSessionNum;
    }

    public Double getCustConsultSessionNum() {
        return custConsultSessionNum;
    }

    public void setCustConsultSessionNum(Double custConsultSessionNum) {
        this.custConsultSessionNum = custConsultSessionNum;
    }

    public Double getCsToCustSessionNum() {
        return csToCustSessionNum;
    }

    public void setCsToCustSessionNum(Double csToCustSessionNum) {
        this.csToCustSessionNum = csToCustSessionNum;
    }

    public Double getChatNum() {
        return chatNum;
    }

    public void setChatNum(Double chatNum) {
        this.chatNum = chatNum;
    }

    public Integer getChatRoundNum() {
        return chatRoundNum;
    }

    public void setChatRoundNum(Integer chatRoundNum) {
        this.chatRoundNum = chatRoundNum;
    }

    public Double getCustChatNum() {
        return custChatNum;
    }

    public void setCustChatNum(Double custChatNum) {
        this.custChatNum = custChatNum;
    }

    public Double getCsChatNum() {
        return csChatNum;
    }

    public void setCsChatNum(Double csChatNum) {
        this.csChatNum = csChatNum;
    }

    public Double getAnswerRatio() {
        return answerRatio;
    }

    public void setAnswerRatio(Double answerRatio) {
        this.answerRatio = answerRatio;
    }

    public Double getCsWordNum() {
        return csWordNum;
    }

    public void setCsWordNum(Double csWordNum) {
        this.csWordNum = csWordNum;
    }

    public Double getAvgCsMsgSessionNum() {
        return avgCsMsgSessionNum;
    }

    public void setAvgCsMsgSessionNum(Double avgCsMsgSessionNum) {
        this.avgCsMsgSessionNum = avgCsMsgSessionNum;
    }

    public Double getMaxReceiveSessionNum() {
        return maxReceiveSessionNum;
    }

    public void setMaxReceiveSessionNum(Double maxReceiveSessionNum) {
        this.maxReceiveSessionNum = maxReceiveSessionNum;
    }

    public Double getNonReplySessionNum() {
        return nonReplySessionNum;
    }

    public void setNonReplySessionNum(Double nonReplySessionNum) {
        this.nonReplySessionNum = nonReplySessionNum;
    }

    public Double getResponseRate() {
        return responseRate;
    }

    public void setResponseRate(Double responseRate) {
        this.responseRate = responseRate;
    }

    public Double getRapidAnswerRate() {
        return rapidAnswerRate;
    }

    public void setRapidAnswerRate(Double rapidAnswerRate) {
        this.rapidAnswerRate = rapidAnswerRate;
    }

    public Double getLeaveMsgSessionNum() {
        return leaveMsgSessionNum;
    }

    public void setLeaveMsgSessionNum(Double leaveMsgSessionNum) {
        this.leaveMsgSessionNum = leaveMsgSessionNum;
    }

    public Double getLeaveMsgReceiveSessionNum() {
        return leaveMsgReceiveSessionNum;
    }

    public void setLeaveMsgReceiveSessionNum(Double leaveMsgReceiveSessionNum) {
        this.leaveMsgReceiveSessionNum = leaveMsgReceiveSessionNum;
    }

    public Double getLeaveMsgResponseRate() {
        return leaveMsgResponseRate;
    }

    public void setLeaveMsgResponseRate(Double leaveMsgResponseRate) {
        this.leaveMsgResponseRate = leaveMsgResponseRate;
    }

    public Double getAvgSessionDurationTime() {
        return avgSessionDurationTime;
    }

    public void setAvgSessionDurationTime(Double avgSessionDurationTime) {
        this.avgSessionDurationTime = avgSessionDurationTime;
    }

    public Double getSlowRespSessionNum() {
        return slowRespSessionNum;
    }

    public void setSlowRespSessionNum(Double slowRespSessionNum) {
        this.slowRespSessionNum = slowRespSessionNum;
    }

    public Double getLongRespSessionNum() {
        return longRespSessionNum;
    }

    public void setLongRespSessionNum(Double longRespSessionNum) {
        this.longRespSessionNum = longRespSessionNum;
    }

    public Double getSaleGuestAvgGoods() {
        return saleGuestAvgGoods;
    }

    public void setSaleGuestAvgGoods(Double saleGuestAvgGoods) {
        this.saleGuestAvgGoods = saleGuestAvgGoods;
    }

    public Double getSaleGoodsAvgAmount() {
        return saleGoodsAvgAmount;
    }

    public void setSaleGoodsAvgAmount(Double saleGoodsAvgAmount) {
        this.saleGoodsAvgAmount = saleGoodsAvgAmount;
    }

    public Double getOutStockGuestItemNum() {
        return outStockGuestItemNum;
    }

    public void setOutStockGuestItemNum(Double outStockGuestItemNum) {
        this.outStockGuestItemNum = outStockGuestItemNum;
    }

    public Double getOutStockItemAvgAmount() {
        return outStockItemAvgAmount;
    }

    public void setOutStockItemAvgAmount(Double outStockItemAvgAmount) {
        this.outStockItemAvgAmount = outStockItemAvgAmount;
    }

    public Double getVerySatisfiedNum() {
        return verySatisfiedNum;
    }

    public void setVerySatisfiedNum(Double verySatisfiedNum) {
        this.verySatisfiedNum = verySatisfiedNum;
    }

    public Double getSatisfiedNum() {
        return satisfiedNum;
    }

    public void setSatisfiedNum(Double satisfiedNum) {
        this.satisfiedNum = satisfiedNum;
    }

    public Double getGeneralNum() {
        return generalNum;
    }

    public void setGeneralNum(Double generalNum) {
        this.generalNum = generalNum;
    }

    public Double getDissatisfiedNum() {
        return dissatisfiedNum;
    }

    public void setDissatisfiedNum(Double dissatisfiedNum) {
        this.dissatisfiedNum = dissatisfiedNum;
    }

    public Double getVeryDissatisfiedNum() {
        return veryDissatisfiedNum;
    }

    public void setVeryDissatisfiedNum(Double veryDissatisfiedNum) {
        this.veryDissatisfiedNum = veryDissatisfiedNum;
    }

    public Double getEvalReplyNum() {
        return evalReplyNum;
    }

    public void setEvalReplyNum(Double evalReplyNum) {
        this.evalReplyNum = evalReplyNum;
    }

    public Double getSatisfactionRate() {
        return satisfactionRate;
    }

    public void setSatisfactionRate(Double satisfactionRate) {
        this.satisfactionRate = satisfactionRate;
    }

    public Double getEvaluationRate() {
        return evaluationRate;
    }

    public void setEvaluationRate(Double evaluationRate) {
        this.evaluationRate = evaluationRate;
    }

    public Double getInviteRate() {
        return inviteRate;
    }

    public void setInviteRate(Double inviteRate) {
        this.inviteRate = inviteRate;
    }

    public Double getEvalSendNum() {
        return evalSendNum;
    }

    public void setEvalSendNum(Double evalSendNum) {
        this.evalSendNum = evalSendNum;
    }

    public Double getHour0() {
        return hour0;
    }

    public void setHour0(Double hour0) {
        this.hour0 = hour0;
    }

    public Double getHour1() {
        return hour1;
    }

    public void setHour1(Double hour1) {
        this.hour1 = hour1;
    }

    public Double getHour2() {
        return hour2;
    }

    public void setHour2(Double hour2) {
        this.hour2 = hour2;
    }

    public Double getHour3() {
        return hour3;
    }

    public void setHour3(Double hour3) {
        this.hour3 = hour3;
    }

    public Double getHour4() {
        return hour4;
    }

    public void setHour4(Double hour4) {
        this.hour4 = hour4;
    }

    public Double getHour5() {
        return hour5;
    }

    public void setHour5(Double hour5) {
        this.hour5 = hour5;
    }

    public Double getHour6() {
        return hour6;
    }

    public void setHour6(Double hour6) {
        this.hour6 = hour6;
    }

    public Double getHour7() {
        return hour7;
    }

    public void setHour7(Double hour7) {
        this.hour7 = hour7;
    }

    public Double getHour8() {
        return hour8;
    }

    public void setHour8(Double hour8) {
        this.hour8 = hour8;
    }

    public Double getHour9() {
        return hour9;
    }

    public void setHour9(Double hour9) {
        this.hour9 = hour9;
    }

    public Double getHour10() {
        return hour10;
    }

    public void setHour10(Double hour10) {
        this.hour10 = hour10;
    }

    public Double getHour11() {
        return hour11;
    }

    public void setHour11(Double hour11) {
        this.hour11 = hour11;
    }

    public Double getHour12() {
        return hour12;
    }

    public void setHour12(Double hour12) {
        this.hour12 = hour12;
    }

    public Double getHour13() {
        return hour13;
    }

    public void setHour13(Double hour13) {
        this.hour13 = hour13;
    }

    public Double getHour14() {
        return hour14;
    }

    public void setHour14(Double hour14) {
        this.hour14 = hour14;
    }

    public Double getHour15() {
        return hour15;
    }

    public void setHour15(Double hour15) {
        this.hour15 = hour15;
    }

    public Double getHour16() {
        return hour16;
    }

    public void setHour16(Double hour16) {
        this.hour16 = hour16;
    }

    public Double getHour17() {
        return hour17;
    }

    public void setHour17(Double hour17) {
        this.hour17 = hour17;
    }

    public Double getHour18() {
        return hour18;
    }

    public void setHour18(Double hour18) {
        this.hour18 = hour18;
    }

    public Double getHour19() {
        return hour19;
    }

    public void setHour19(Double hour19) {
        this.hour19 = hour19;
    }

    public Double getHour20() {
        return hour20;
    }

    public void setHour20(Double hour20) {
        this.hour20 = hour20;
    }

    public Double getHour21() {
        return hour21;
    }

    public void setHour21(Double hour21) {
        this.hour21 = hour21;
    }

    public Double getHour22() {
        return hour22;
    }

    public void setHour22(Double hour22) {
        this.hour22 = hour22;
    }

    public Double getHour23() {
        return hour23;
    }

    public void setHour23(Double hour23) {
        this.hour23 = hour23;
    }

    public Double getAllDay() {
        return allDay;
    }

    public void setAllDay(Double allDay) {
        this.allDay = allDay;
    }

    public Double getGoodEvaluateNumPreSale() {
        return goodEvaluateNumPreSale;
    }

    public void setGoodEvaluateNumPreSale(Double goodEvaluateNumPreSale) {
        this.goodEvaluateNumPreSale = goodEvaluateNumPreSale;
    }

    public Double getNeutralEvaluateNumPreSale() {
        return neutralEvaluateNumPreSale;
    }

    public void setNeutralEvaluateNumPreSale(Double neutralEvaluateNumPreSale) {
        this.neutralEvaluateNumPreSale = neutralEvaluateNumPreSale;
    }

    public Double getBadEvaluateNumPreSale() {
        return badEvaluateNumPreSale;
    }

    public void setBadEvaluateNumPreSale(Double badEvaluateNumPreSale) {
        this.badEvaluateNumPreSale = badEvaluateNumPreSale;
    }

    public Double getGoodEvaluateNumBetSale() {
        return goodEvaluateNumBetSale;
    }

    public void setGoodEvaluateNumBetSale(Double goodEvaluateNumBetSale) {
        this.goodEvaluateNumBetSale = goodEvaluateNumBetSale;
    }

    public Double getNeutralEvaluateNumBetSale() {
        return neutralEvaluateNumBetSale;
    }

    public void setNeutralEvaluateNumBetSale(Double neutralEvaluateNumBetSale) {
        this.neutralEvaluateNumBetSale = neutralEvaluateNumBetSale;
    }

    public Double getBadEvaluateNumBetSale() {
        return badEvaluateNumBetSale;
    }

    public void setBadEvaluateNumBetSale(Double badEvaluateNumBetSale) {
        this.badEvaluateNumBetSale = badEvaluateNumBetSale;
    }

    public Double getGoodEvaluateNumAfterSale() {
        return goodEvaluateNumAfterSale;
    }

    public void setGoodEvaluateNumAfterSale(Double goodEvaluateNumAfterSale) {
        this.goodEvaluateNumAfterSale = goodEvaluateNumAfterSale;
    }

    public Double getNeutralEvaluateNumAfterSale() {
        return neutralEvaluateNumAfterSale;
    }

    public void setNeutralEvaluateNumAfterSale(Double neutralEvaluateNumAfterSale) {
        this.neutralEvaluateNumAfterSale = neutralEvaluateNumAfterSale;
    }

    public Double getBadEvaluateNumAfterSale() {
        return badEvaluateNumAfterSale;
    }

    public void setBadEvaluateNumAfterSale(Double badEvaluateNumAfterSale) {
        this.badEvaluateNumAfterSale = badEvaluateNumAfterSale;
    }

    public Double getGoodEvaluateNumTotal() {
        return goodEvaluateNumTotal;
    }

    public void setGoodEvaluateNumTotal(Double goodEvaluateNumTotal) {
        this.goodEvaluateNumTotal = goodEvaluateNumTotal;
    }

    public Double getNeutralEvaluateNumTotal() {
        return neutralEvaluateNumTotal;
    }

    public void setNeutralEvaluateNumTotal(Double neutralEvaluateNumTotal) {
        this.neutralEvaluateNumTotal = neutralEvaluateNumTotal;
    }

    public Double getBadEvaluateNumTotal() {
        return badEvaluateNumTotal;
    }

    public void setBadEvaluateNumTotal(Double badEvaluateNumTotal) {
        this.badEvaluateNumTotal = badEvaluateNumTotal;
    }

    public Double getApplyRefundNum() {
        return applyRefundNum;
    }

    public void setApplyRefundNum(Double applyRefundNum) {
        this.applyRefundNum = applyRefundNum;
    }

    public Double getApplyRefundProductNum() {
        return applyRefundProductNum;
    }

    public void setApplyRefundProductNum(Double applyRefundProductNum) {
        this.applyRefundProductNum = applyRefundProductNum;
    }

    public Double getApplyRefundBuyerNum() {
        return applyRefundBuyerNum;
    }

    public void setApplyRefundBuyerNum(Double applyRefundBuyerNum) {
        this.applyRefundBuyerNum = applyRefundBuyerNum;
    }

    public Double getApplyRefundAmount() {
        return applyRefundAmount;
    }

    public void setApplyRefundAmount(Double applyRefundAmount) {
        this.applyRefundAmount = applyRefundAmount;
    }

    public Double getCompletedRefundNum() {
        return completedRefundNum;
    }

    public void setCompletedRefundNum(Double completedRefundNum) {
        this.completedRefundNum = completedRefundNum;
    }

    public Double getCompletedRefundBuyerNum() {
        return completedRefundBuyerNum;
    }

    public void setCompletedRefundBuyerNum(Double completedRefundBuyerNum) {
        this.completedRefundBuyerNum = completedRefundBuyerNum;
    }

    public Double getTransactionsNum() {
        return transactionsNum;
    }

    public void setTransactionsNum(Double transactionsNum) {
        this.transactionsNum = transactionsNum;
    }

    public Double getRefundProductNum() {
        return refundProductNum;
    }

    public void setRefundProductNum(Double refundProductNum) {
        this.refundProductNum = refundProductNum;
    }

    public Double getRefundPercent() {
        return refundPercent;
    }

    public void setRefundPercent(Double refundPercent) {
        this.refundPercent = refundPercent;
    }

    public Double getTeamSaleAmount() {
        return teamSaleAmount;
    }

    public void setTeamSaleAmount(Double teamSaleAmount) {
        this.teamSaleAmount = teamSaleAmount;
    }

    public Double getOrderItemAvgAmount() {
        return orderItemAvgAmount;
    }

    public void setOrderItemAvgAmount(Double orderItemAvgAmount) {
        this.orderItemAvgAmount = orderItemAvgAmount;
    }

    public Double getOrderedGoodsNumToday() {
        return orderedGoodsNumToday;
    }

    public void setOrderedGoodsNumToday(Double orderedGoodsNumToday) {
        this.orderedGoodsNumToday = orderedGoodsNumToday;
    }

    public Double getOrderedGuestAvgPrice() {
        return orderedGuestAvgPrice;
    }

    public void setOrderedGuestAvgPrice(Double orderedGuestAvgPrice) {
        this.orderedGuestAvgPrice = orderedGuestAvgPrice;
    }

    public Double getOrderedGuestAvgAmount() {
        return orderedGuestAvgAmount;
    }

    public void setOrderedGuestAvgAmount(Double orderedGuestAvgAmount) {
        this.orderedGuestAvgAmount = orderedGuestAvgAmount;
    }

    public Double getPaidAmountFinal() {
        return paidAmountFinal;
    }

    public void setPaidAmountFinal(Double paidAmountFinal) {
        this.paidAmountFinal = paidAmountFinal;
    }

    public Double getPaidNumToday() {
        return paidNumToday;
    }

    public void setPaidNumToday(Double paidNumToday) {
        this.paidNumToday = paidNumToday;
    }

    public Double getPaidGoodsNumToday() {
        return paidGoodsNumToday;
    }

    public void setPaidGoodsNumToday(Double paidGoodsNumToday) {
        this.paidGoodsNumToday = paidGoodsNumToday;
    }

    public Double getSaleOrderSkuNum() {
        return saleOrderSkuNum;
    }

    public void setSaleOrderSkuNum(Double saleOrderSkuNum) {
        this.saleOrderSkuNum = saleOrderSkuNum;
    }

    public Double getMedalNum() {
        return medalNum;
    }

    public void setMedalNum(Double medalNum) {
        this.medalNum = medalNum;
    }

    public Double getWorkDay() {
        return workDay;
    }

    public void setWorkDay(Double workDay) {
        this.workDay = workDay;
    }

    public LocalTime getAvgFirstOnlineDateTime() {
        return avgFirstOnlineDateTime;
    }

    public void setAvgFirstOnlineDateTime(LocalTime avgFirstOnlineDateTime) {
        this.avgFirstOnlineDateTime = avgFirstOnlineDateTime;
    }

    public LocalTime getAvgLastOfflineDateTime() {
        return avgLastOfflineDateTime;
    }

    public void setAvgLastOfflineDateTime(LocalTime avgLastOfflineDateTime) {
        this.avgLastOfflineDateTime = avgLastOfflineDateTime;
    }

    public LocalTime getFirstOnlineDateTime() {
        return firstOnlineDateTime;
    }

    public void setFirstOnlineDateTime(LocalTime firstOnlineDateTime) {
        this.firstOnlineDateTime = firstOnlineDateTime;
    }

    public LocalTime getLastOfflineDateTime() {
        return lastOfflineDateTime;
    }

    public void setLastOfflineDateTime(LocalTime lastOfflineDateTime) {
        this.lastOfflineDateTime = lastOfflineDateTime;
    }

    public Double getLoginTimesNum() {
        return loginTimesNum;
    }

    public void setLoginTimesNum(Double loginTimesNum) {
        this.loginTimesNum = loginTimesNum;
    }

    public Double getAvgLoginTimesNum() {
        return avgLoginTimesNum;
    }

    public void setAvgLoginTimesNum(Double avgLoginTimesNum) {
        this.avgLoginTimesNum = avgLoginTimesNum;
    }

    public Double getAvgLoginDurationTime() {
        return avgLoginDurationTime;
    }

    public void setAvgLoginDurationTime(Double avgLoginDurationTime) {
        this.avgLoginDurationTime = avgLoginDurationTime;
    }

    public Double getAvgRceiveDurationTime() {
        return avgRceiveDurationTime;
    }

    public void setAvgRceiveDurationTime(Double avgRceiveDurationTime) {
        this.avgRceiveDurationTime = avgRceiveDurationTime;
    }

    public Double getHangupDurationTime() {
        return hangupDurationTime;
    }

    public void setHangupDurationTime(Double hangupDurationTime) {
        this.hangupDurationTime = hangupDurationTime;
    }

    public Double getAvgHangupDurationTime() {
        return avgHangupDurationTime;
    }

    public void setAvgHangupDurationTime(Double avgHangupDurationTime) {
        this.avgHangupDurationTime = avgHangupDurationTime;
    }

    public Double getRceiveTimeRate() {
        return rceiveTimeRate;
    }

    public void setRceiveTimeRate(Double rceiveTimeRate) {
        this.rceiveTimeRate = rceiveTimeRate;
    }

    public Double getOfflineDurationTime() {
        return offlineDurationTime;
    }

    public void setOfflineDurationTime(Double offlineDurationTime) {
        this.offlineDurationTime = offlineDurationTime;
    }

    public Long getChatRoundNumNoLeave() {
        return chatRoundNumNoLeave;
    }

    public void setChatRoundNumNoLeave(Long chatRoundNumNoLeave) {
        this.chatRoundNumNoLeave = chatRoundNumNoLeave;
    }
}
