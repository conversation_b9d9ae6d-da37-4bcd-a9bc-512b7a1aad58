package com.pes.jd.model.VO;

/**
 * <AUTHOR> 🏀 huqingfeng
 * @date 2019-06-14
 */
public class ShopTeamHourIndexVO {
    // 下单人数
    private Integer orderedBuyerNum;

    //下单订单数
    private Integer orderedNum;

    //付款订单数
    private Integer saleOrderNum;

    // 付款人数
    private Integer saleBuyerNum;

    // 询单->付款转化率
    private Double enquiryToPay;

    private Double saleAmount;

    private Integer shopNum;

    // 较昨日

    private String orderedBuyerNumComparison;

    private String orderedNumComparison;

    private String saleOrderNumComparison;

    private String saleBuyerNumComparison;

    private String enquiryToPayComparison;

    public Integer getShopNum() {
        return shopNum;
    }

    public void setShopNum(Integer shopNum) {
        this.shopNum = shopNum;
    }

    public Double getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(Double saleAmount) {
        this.saleAmount = saleAmount;
    }

    public Integer getOrderedBuyerNum() {
        return orderedBuyerNum;
    }

    public void setOrderedBuyerNum(Integer orderedBuyerNum) {
        this.orderedBuyerNum = orderedBuyerNum;
    }

    public Integer getOrderedNum() {
        return orderedNum;
    }

    public void setOrderedNum(Integer orderedNum) {
        this.orderedNum = orderedNum;
    }

    public Integer getSaleOrderNum() {
        return saleOrderNum;
    }

    public void setSaleOrderNum(Integer saleOrderNum) {
        this.saleOrderNum = saleOrderNum;
    }

    public Integer getSaleBuyerNum() {
        return saleBuyerNum;
    }

    public void setSaleBuyerNum(Integer saleBuyerNum) {
        this.saleBuyerNum = saleBuyerNum;
    }

    public Double getEnquiryToPay() {
        return enquiryToPay;
    }

    public void setEnquiryToPay(Double enquiryToPay) {
        this.enquiryToPay = enquiryToPay;
    }

    public String getOrderedBuyerNumComparison() {
        return orderedBuyerNumComparison;
    }

    public void setOrderedBuyerNumComparison(String orderedBuyerNumComparison) {
        this.orderedBuyerNumComparison = orderedBuyerNumComparison;
    }

    public String getOrderedNumComparison() {
        return orderedNumComparison;
    }

    public void setOrderedNumComparison(String orderedNumComparison) {
        this.orderedNumComparison = orderedNumComparison;
    }

    public String getSaleOrderNumComparison() {
        return saleOrderNumComparison;
    }

    public void setSaleOrderNumComparison(String saleOrderNumComparison) {
        this.saleOrderNumComparison = saleOrderNumComparison;
    }

    public String getSaleBuyerNumComparison() {
        return saleBuyerNumComparison;
    }

    public void setSaleBuyerNumComparison(String saleBuyerNumComparison) {
        this.saleBuyerNumComparison = saleBuyerNumComparison;
    }

    public String getEnquiryToPayComparison() {
        return enquiryToPayComparison;
    }

    public void setEnquiryToPayComparison(String enquiryToPayComparison) {
        this.enquiryToPayComparison = enquiryToPayComparison;
    }

    public static ShopTeamHourIndexVO getInstance(){
        ShopTeamHourIndexVO shopTeamHourIndexVO = new ShopTeamHourIndexVO();
        shopTeamHourIndexVO.setShopNum(0);
        shopTeamHourIndexVO.setSaleAmount(0.0D);
        shopTeamHourIndexVO.setOrderedBuyerNum(0);
        shopTeamHourIndexVO.setOrderedNum(0);
        shopTeamHourIndexVO.setSaleOrderNum(0);
        shopTeamHourIndexVO.setSaleBuyerNum(0);
        shopTeamHourIndexVO.setEnquiryToPay(0.0D);
        shopTeamHourIndexVO.setOrderedBuyerNumComparison("--");
        shopTeamHourIndexVO.setOrderedNumComparison("--");
        shopTeamHourIndexVO.setSaleOrderNumComparison("--");
        shopTeamHourIndexVO.setSaleBuyerNumComparison("--");
        shopTeamHourIndexVO.setEnquiryToPayComparison("--");
        return shopTeamHourIndexVO;

    }
}
