package com.pes.jd.model.VO;

import lombok.Data;

import java.util.Date;

/**
 * @Author: yuanxun
 * @Date: 18:34 2019/10/10
 * @Description:
 */
@Data
public class AdminShopVO {
	private Long shopId;
	private String sellerNick;
	private String title;
	private String sessionKey;
	private String status;
	private String db;
	private String schemaId;
	private Integer judgeRule;//判定规则    \n1：下单判定\n2：下单优先判定\n3：付款判定
	private Date orderDate;
	private String operateManagerName;
	private String operateManagerErp;
	private String businessGroup;//事业群
	private String businessUnit;//事业部
	private String secondaryDepartment;//二级部门


	private Integer type; //0:pop 1:自营

	//拓展参数
	private Long venderId;
	private Integer subuserNum;//版本的子帐号上限
	private Boolean initDataFlag;//店铺是否完成初始化
	private Date previousGetDataTime;//上次实时绩效拉取时间
	private Integer colType;//店铺类型
	private String rtSchemaId;
	private String rtDb;
	private Date subscribeDeadLine;//订购结束时间

	private String itemCode;//版本号
	private Integer interfaceType;//简洁、完整界面
	private Date authDeadLine;//授权过期时间
	private String ip;//ip地址

//	private Boolean auto_reply_switch;//绩效是否过滤自动回复
//	private String auto_reply_mark;//自动回复开头表示
//	private Boolean aftersell_acount_filter;//售后账号不参与过滤
//	private Integer sell_after;//售后时间
//	private Integer max_wait_time;//最长等待时间(分钟)',
//	private Integer slow_response_time;//慢响应时间（秒）',
//	private Integer slow_response_times_num;//慢响应次数',
//	private Integer quick_response_time;//快速应答时间(秒)',
//	private Integer long_reception_time;//长接待时间（分钟）',
//	private Integer scheduling_time_dot;//业务天分隔时间点 默认值3点',
//	private Boolean cs_recommend_switch;//客服推荐关键字匹配 开关',
//	private String cs_recommend_mark;//客服推荐关键字\n开启本功能后，客服发送商品链接时需要以关键字开头，才会判断为客服推荐。',
//	private String duty_record_export_unit;//值班记录导出时长单位设置 (分钟/秒)',
//	private Boolean value_service_sale_amout_switch;//增值服务订单统计设置\n销售额统计开关',
//	private Boolean value_service_goods_num_switch;//增值服务订单统计设置\n商品件数统计',
//	private Integer judge_rule_ascription;//1:聊天时间判定归属\n2:回合数判定归属',
//	private Integer enquiry_valid_duration_time;//询单有效时长(n天)n:2-15',
//	private Integer out_stock_valid_duration_time;//出库有效时长(天)',

	@Override
	public String toString() {
		return "ShopVO [shopId=" + shopId + ", sellerNick=" + sellerNick + ", title=" + title + ", sessionKey="
				+ sessionKey + ", status=" + status + ", db=" + db + ", schemaId=" + schemaId + ", judgeRule="
				+ judgeRule + "]";
	}
}
  
