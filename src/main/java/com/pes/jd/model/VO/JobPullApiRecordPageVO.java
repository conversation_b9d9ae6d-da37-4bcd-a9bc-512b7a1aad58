package com.pes.jd.model.VO;

import com.pes.jd.util.CommonDateUtils;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: yuanxun
 * @Date: 2019-08-01 14:30
 * @Version 1.0
 */
@Data
public class JobPullApiRecordPageVO implements Serializable,Comparable<JobPullApiRecordPageVO> {

    private Long shopId;
    private String shopTitle;
    private String shopsStatus;
    private String dbName;
    private String schemaId;
    private String sessionKey;
    private Integer result;
    private String modified;//操作时间
    private String date;
    private String msg;//失败原因
    private String sellerNick;

    @Override
    public int compareTo(JobPullApiRecordPageVO o) {
        Long time = CommonDateUtils.parseYMd(this.getDate()).getTime() - CommonDateUtils.parseYMd(o.getDate()).getTime();
        return time.intValue();
    }
}
