package com.pes.jd.model.VO;

import com.pes.jd.model.Annotation.Property;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Date;

/**
 * <AUTHOR> <EMAIL>
 * @date 2019/1/3 3:42 PM
 * @since 1.0.0
 */
public class CsPerformanceVo {

    @Property(value = "店铺ID", direct = true)
    private Long shopId;

    @Property(value = "客服Nick", direct = true)
    private String csNick;

    @Property(value = "日期", direct = true)
    private Date date;

    @Property(value = "客服销售额", direct = true)
    private Double saleAmount;

    @Property(value = "个人销售占比")
    private Double saleAmountPercent;

    @Property(value = "个人销售占比 -> 显示数据(String)")
    private String saleAmountPercentStr;

    @Property(value = "销售量", direct = true)
    private Integer saleGoodsNum;

    @Property(value = "销售人数", direct = true)
    private Integer saleBuyerNum;

    @Property(value = "销售订单数", direct = true)
    private Integer saleOrderNum;

    @Property(value = "询单人数", direct = true)
    private Integer enquiryNum;

    @Property(value = "询单人数 显示数据")
    private String enquiryNumStr;

    @Property(value = "邮费总额", direct = true)
    private Double postFee;

    @Property(value = "销售客单价（元/件） 本客服客单价=本客服销售额/本客服销售人数")
    private Double saleGuestAvgAmount;

    @Property(value = "销售客单价 显示数据 -> String")
    private String saleGuestAvgAmountStr;

    @Property(value = "销售客件数（件/人）")
    private Integer saleGuestAvgGoods;

    @Property(value = "销售客件数 显示数据 -> String")
    private String saleGuestAvgGoodsStr;

    @Property(value = "销售件均价（元/件）")
    private Double saleGoodsAvgAmount;

    @Property(value = "销售件均价（元/件） 显示数据")
    private String saleGoodsAvgAmountStr;

    @Property(value = "出库客单价（元/人）")
    private Double outStockGuestAvgAmount;

    @Property(value = "出库客单价（元/人） 显示数据")
    private String outStockGuestAvgAmountStr;

    @Property(value = "出库的人数", direct = true)
    private Integer outStockNum;

    @Property(value = "出库的金额", direct = true)
    private Double outStockAmount;

    @Property(value = "出库的件数", direct = true)
    private Integer outStockGoodsNum;

    @Property(value = "出库订单数", direct = true)
    private Integer outStockOrderNum;

    @Property(value = "出库客件数（件/人）")
    private Integer outStockGuestItemNum;

    @Property(value = "出库客件数（件/人）显示数据")
    private Integer outStockGuestItemNumStr;

    @Property(value = "出库件均价（元/件）")
    private Double outStockItemAvgAmount;

    @Property(value = "出库件均价（元/件）显示数据")
    private String outStockItemAvgAmountStr;

    @Property(value = "下单件均价")
    private Double orderItemAvgAmount;

    @Property(value = "下单件均价显示数据")
    private String orderItemAvgAmountStr;

    @Property(value = "询单→当日下单人数",direct = true)
    private Integer orderedNumToday;

    @Property(value = "询单→当日下单金额",direct = true)
    private Double orderedAmountToday;

    @Property(value = "询单→当日的下单件数",direct = true)
    private Integer orderedGoodsNumToday;

    @Property(value = "下单客单价（元/人）")
    private Double orderedGuestAvgPrice;

    @Property(value = "下单客单价（元/人） 显示数据")
    private String orderedGuestAvgPriceStr;

    @Property(value = "下单客件数（件/人）")
    private Double orderedGuestAvgAmount;

    @Property(value = "下单客件数（件/人）显示数据")
    private String orderedGuestAvgAmountStr;

    @Property(value = "询单→次日付款转化率")
    private Double queryToTomorrow;

    @Property(value = "询单→次日付款转化率 显示数据")
    private String queryToTomorrowStr;

    @Property(value = "当日咨询，当日下单，当日或次日付款的人数 (询单→次日付款人数)",direct = true)
    private Integer paidNumTodayNext;

    @Property(value = "当日咨询，当日下单，当日或次日付款的人数 (询单→次日付款人数)   显示数据")
    private String paidNumTodayNextStr;

    @Property(value = "当日询单 最终落实下单，并且最终付了款的金额 (询单→最终付款人数)",direct = true)
    private Double paidAmountFinal;

    @Property(value = "当日询单 最终落实下单，并且最终付了款的人数（当日或者次日）",direct = true)
    private Integer paidNumFinal;

    @Property(value = "询单→最终付款转化率")
    private Double queryToFinalPaid;

    @Property(value = "询单→最终付款转化率 显示数据")
    private String queryToFinalPaidStr;

    @Property(value = "询单→当日下单转化率")
    private Double queryToOrderedToday;

    @Property(value = "询单→当日下单转化率 显示数据")
    private String queryToOrderedTodayStr;

    @Property(value = "当日询单 最终落实下单的人数 (今日/明日)",direct = true)
    private Integer orderedNumFinal;

    @Property(value = "下单→当日付款转化率")
    private Double orderedToPaid;

    @Property(value = "下单→当日付款转化率 显示数据")
    private String orderedToPaidStr;

    @Property(value = "下单→最终付款转化率")
    private Double orderedToPaidFinal;

    @Property(value = "下单→最终付款转化率 显示数据")
    private String orderedToPaidFinalStr;

    @Property(value = "当日询单 当天落实下单 并且 付了款的人数",direct = true)
    private Integer paidNumToday;

    @Property(value = "询单→最终下单转化率")
    private Double queryToFinalOrdered;

    @Property(value = "询单→最终下单转化率 显示数据")
    private String queryToFinalOrderedStr;

    @Property(value = "询单→出库转化率")
    private Double queryToOutStock;

    @Property(value = "询单→出库转化率 显示数据")
    private String queryToOutStockStr;

    @Property(value = "最终下单人数 显示数据")
    private String orderedNumFinalStr;

    @Property(value = "当日询单 最终落实下单的金额",direct = true)
    private Double orderedAmountFinal;

    @Property(value = "当日询单 最终落实下单的金额 显示数据")
    private String orderedAmountFinalStr;

    @Property(value = "下单当日付款的件数",direct = true)
    private Integer paidGoodsNumToday;

    @Property(value = "咨询人数",direct = true)
    private Integer consultNum;

    @Property(value = "接待人数",direct = true)
    private Integer receiveNum;

    @Property(value = "个人出库金额占比")
    private Double personalOutStockAmountPercent;

    @Property(value = "个人出库金额占比 显示数据")
    private String personalOutStockAmountPercentStr;

    @Property(value = "售前好评数",direct = true)
    private Integer goodEvaluateNumPreSale;

    @Property(value = "售前中评数",direct = true)
    private Integer neutralEvaluateNumPreSale;

    @Property(value = "售前差评数",direct = true)
    private Integer badEvaluateNumPreSale;

    @Property(value = "售中好评数",direct = true)
    private Integer goodEvaluateNumBetSale;

    @Property(value = "售中中评数",direct = true)
    private Integer neutralEvaluateNumBetSale;

    @Property(value = "售中差评数",direct = true)
    private Integer badEvaluateNumBetSale;

    @Property(value = "售后好评数",direct = true)
    private Integer goodEvaluateNumAfterSale;

    @Property(value = "售后中评数",direct = true)
    private Integer neutralEvaluateNumAfterSale;

    @Property(value = "售后差评数",direct = true)
    private Integer badEvaluateNumAfterSale;

    @Property(value = "好评总数",direct = true)
    private Integer goodEvaluateNumTotal;

    @Property(value = "中评总数",direct = true)
    private Integer neutralEvaluateNumTotal;

    @Property(value = "差评总数",direct = true)
    private Integer badEvaluateNumTotal;

    @Property(value = "非常满意",direct = true)
    private Integer verySatisfiedNum;

    @Property(value = "满意",direct = true)
    private Integer satisfiedNum;

    @Property(value = "一般",direct = true)
    private Integer generalNum;

    @Property(value = "不满意",direct = true)
    private Integer dissatisfiedNum;

    @Property(value = "非常不满意",direct = true)
    private Integer veryDissatisfiedNum;

    @Property(value = "申请退款笔数",direct = true)
    private Integer applyRefundNum;

    @Property(value = "申请退款件数",direct = true)
    private Integer applyRefundProductNum;

    @Property(value = "申请退款人数",direct = true)
    private Integer applyRefundBuyerNum;

    @Property(value = "申请退款金额",direct = true)
    private Double applyRefundAmount;

    @Property(value = "完成退款笔数",direct = true)
    private Integer completedRefundNum;

    @Property(value = "完成退款件数",direct = true)
    private Integer completedRefundProductNum;

    @Property(value = "完成退款人数",direct = true)
    private Integer completedRefundBuyerNum;

    @Property(value = "完成退款金额",direct = true)
    private Double completedRefundAmount;

    @Property(value = "成交笔数",direct = true)
    private Integer saleOrderSkuNum;

    @Property(value = "退款率")
    private Double refundPercent;

    @Property(value = "退款率 显示数据")
    private String refundPercentStr;

    @Property(value = "询单流失人数")
    private Integer enquiryLossNum;

    @Property(value = "询单流失人数 显示数据")
    private String enquiryLossNumStr;

    @Property(value = "付款流失人数")
    private Integer paidLossNum;

    @Property(value = "付款流失人数")
    private String paidLossNumStr;

    @Property(value = "下单维度：促成下单并当日付款的，付款人数",direct = true)
    private Integer toOrderedPaidNumToday;

    @Property(value = "下单维度：促成下单并当日付款的，付款件数",direct = true)
    private Integer toOrderedPaidGoodsToday;

    @Property(value = "下单维度：促成下单并当日付款的，付款金额",direct = true)
    private Double toOrderedPaidAmountToday;

    @Property(value = "最终付款人数",direct = true)
    private Integer toOrderedPaidNumFinal;

    @Property(value = "最终付款人数 显示数据",direct = true)
    private String toOrderedPaidNumFinalStr;

    @Property(value = "最终付款件数",direct = true)
    private Integer toOrderedPaidGoodsFinal;

    @Property(value = "最终付款件数 显示数据",direct = true)
    private String toOrderedPaidGoodsFinalStr;

    @Property(value = "最终付款金额",direct = true)
    private Double toOrderedPaidAmountFinal;

    @Property(value = "最终付款金额 显示数据" ,direct = true)
    private String toOrderedPaidAmountFinalStr;

    @Property(value = "客服落实下单人数",direct = true)
    private Integer toOrderedNum;

    @Property(value = "客服落实下单件数",direct = true)
    private Integer toOrderedGoodsNum;

    @Property(value = "客服落实下单金额",direct = true)
    private Double toOrderedAmount;

    @Property(value = "客服落实下单订单数",direct = true)
    private Integer toOrderedOrderNum;

    public Integer getToOrderedNum() {
        return toOrderedNum;
    }

    public void setToOrderedNum(Integer toOrderedNum) {
        this.toOrderedNum = toOrderedNum;
    }

    public Integer getToOrderedGoodsNum() {
        return toOrderedGoodsNum;
    }

    public void setToOrderedGoodsNum(Integer toOrderedGoodsNum) {
        this.toOrderedGoodsNum = toOrderedGoodsNum;
    }

    public Double getToOrderedAmount() {
        return toOrderedAmount;
    }

    public void setToOrderedAmount(Double toOrderedAmount) {
        this.toOrderedAmount = toOrderedAmount;
    }

    public Integer getToOrderedOrderNum() {
        return toOrderedOrderNum;
    }

    public void setToOrderedOrderNum(Integer toOrderedOrderNum) {
        this.toOrderedOrderNum = toOrderedOrderNum;
    }

    public String getToOrderedPaidNumFinalStr() {
        return toOrderedPaidNumFinalStr;
    }

    public void setToOrderedPaidNumFinalStr(String toOrderedPaidNumFinalStr) {
        this.toOrderedPaidNumFinalStr = toOrderedPaidNumFinalStr;
    }

    public String getToOrderedPaidGoodsFinalStr() {
        return toOrderedPaidGoodsFinalStr;
    }

    public void setToOrderedPaidGoodsFinalStr(String toOrderedPaidGoodsFinalStr) {
        this.toOrderedPaidGoodsFinalStr = toOrderedPaidGoodsFinalStr;
    }

    public String getToOrderedPaidAmountFinalStr() {
        return toOrderedPaidAmountFinalStr;
    }

    public void setToOrderedPaidAmountFinalStr(String toOrderedPaidAmountFinalStr) {
        this.toOrderedPaidAmountFinalStr = toOrderedPaidAmountFinalStr;
    }

    public Integer getToOrderedPaidNumFinal() {
        return toOrderedPaidNumFinal;
    }

    public void setToOrderedPaidNumFinal(Integer toOrderedPaidNumFinal) {
        this.toOrderedPaidNumFinal = toOrderedPaidNumFinal;
    }

    public Integer getToOrderedPaidGoodsFinal() {
        return toOrderedPaidGoodsFinal;
    }

    public void setToOrderedPaidGoodsFinal(Integer toOrderedPaidGoodsFinal) {
        this.toOrderedPaidGoodsFinal = toOrderedPaidGoodsFinal;
    }

    public Double getToOrderedPaidAmountFinal() {
        return toOrderedPaidAmountFinal;
    }

    public void setToOrderedPaidAmountFinal(Double toOrderedPaidAmountFinal) {
        this.toOrderedPaidAmountFinal = toOrderedPaidAmountFinal;
    }

    public Integer getToOrderedPaidNumToday() {
        return toOrderedPaidNumToday;
    }

    public void setToOrderedPaidNumToday(Integer toOrderedPaidNumToday) {
        this.toOrderedPaidNumToday = toOrderedPaidNumToday;
    }

    public Integer getToOrderedPaidGoodsToday() {
        return toOrderedPaidGoodsToday;
    }

    public void setToOrderedPaidGoodsToday(Integer toOrderedPaidGoodsToday) {
        this.toOrderedPaidGoodsToday = toOrderedPaidGoodsToday;
    }

    public Double getToOrderedPaidAmountToday() {
        return toOrderedPaidAmountToday;
    }

    public void setToOrderedPaidAmountToday(Double toOrderedPaidAmountToday) {
        this.toOrderedPaidAmountToday = toOrderedPaidAmountToday;
    }

    public String getEnquiryLossNumStr() {
        return enquiryLossNumStr;
    }

    public void setEnquiryLossNumStr(String enquiryLossNumStr) {
        this.enquiryLossNumStr = enquiryLossNumStr;
    }

    public String getPaidLossNumStr() {
        return paidLossNumStr;
    }

    public void setPaidLossNumStr(String paidLossNumStr) {
        this.paidLossNumStr = paidLossNumStr;
    }

    public Integer getEnquiryLossNum() {
        return enquiryLossNum;
    }

    public void setEnquiryLossNum(Integer enquiryLossNum) {
        this.enquiryLossNum = enquiryLossNum;
    }

    public Integer getPaidLossNum() {
        return paidLossNum;
    }

    public void setPaidLossNum(Integer paidLossNum) {
        this.paidLossNum = paidLossNum;
    }

    public Double getRefundPercent() {
        return refundPercent;
    }

    public void setRefundPercent(Double refundPercent) {
        this.refundPercent = refundPercent;
    }

    public String getRefundPercentStr() {
        return refundPercentStr;
    }

    public void setRefundPercentStr(String refundPercentStr) {
        this.refundPercentStr = refundPercentStr;
    }

    public Integer getSaleOrderSkuNum() {
        return saleOrderSkuNum;
    }

    public void setSaleOrderSkuNum(Integer saleOrderSkuNum) {
        this.saleOrderSkuNum = saleOrderSkuNum;
    }

    public Integer getApplyRefundNum() {
        return applyRefundNum;
    }

    public void setApplyRefundNum(Integer applyRefundNum) {
        this.applyRefundNum = applyRefundNum;
    }

    public Integer getApplyRefundProductNum() {
        return applyRefundProductNum;
    }

    public void setApplyRefundProductNum(Integer applyRefundProductNum) {
        this.applyRefundProductNum = applyRefundProductNum;
    }

    public Integer getApplyRefundBuyerNum() {
        return applyRefundBuyerNum;
    }

    public void setApplyRefundBuyerNum(Integer applyRefundBuyerNum) {
        this.applyRefundBuyerNum = applyRefundBuyerNum;
    }

    public Double getApplyRefundAmount() {
        return applyRefundAmount;
    }

    public void setApplyRefundAmount(Double applyRefundAmount) {
        this.applyRefundAmount = applyRefundAmount;
    }

    public Integer getCompletedRefundNum() {
        return completedRefundNum;
    }

    public void setCompletedRefundNum(Integer completedRefundNum) {
        this.completedRefundNum = completedRefundNum;
    }

    public Integer getCompletedRefundProductNum() {
        return completedRefundProductNum;
    }

    public void setCompletedRefundProductNum(Integer completedRefundProductNum) {
        this.completedRefundProductNum = completedRefundProductNum;
    }

    public Integer getCompletedRefundBuyerNum() {
        return completedRefundBuyerNum;
    }

    public void setCompletedRefundBuyerNum(Integer completedRefundBuyerNum) {
        this.completedRefundBuyerNum = completedRefundBuyerNum;
    }

    public Double getCompletedRefundAmount() {
        return completedRefundAmount;
    }

    public void setCompletedRefundAmount(Double completedRefundAmount) {
        this.completedRefundAmount = completedRefundAmount;
    }

    public Integer getVerySatisfiedNum() {
        return verySatisfiedNum;
    }

    public void setVerySatisfiedNum(Integer verySatisfiedNum) {
        this.verySatisfiedNum = verySatisfiedNum;
    }

    public Integer getSatisfiedNum() {
        return satisfiedNum;
    }

    public void setSatisfiedNum(Integer satisfiedNum) {
        this.satisfiedNum = satisfiedNum;
    }

    public Integer getGeneralNum() {
        return generalNum;
    }

    public void setGeneralNum(Integer generalNum) {
        this.generalNum = generalNum;
    }

    public Integer getDissatisfiedNum() {
        return dissatisfiedNum;
    }

    public void setDissatisfiedNum(Integer dissatisfiedNum) {
        this.dissatisfiedNum = dissatisfiedNum;
    }

    public Integer getVeryDissatisfiedNum() {
        return veryDissatisfiedNum;
    }

    public void setVeryDissatisfiedNum(Integer veryDissatisfiedNum) {
        this.veryDissatisfiedNum = veryDissatisfiedNum;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getGoodEvaluateNumTotal() {
        return goodEvaluateNumTotal;
    }

    public void setGoodEvaluateNumTotal(Integer goodEvaluateNumTotal) {
        this.goodEvaluateNumTotal = goodEvaluateNumTotal;
    }

    public Integer getNeutralEvaluateNumTotal() {
        return neutralEvaluateNumTotal;
    }

    public void setNeutralEvaluateNumTotal(Integer neutralEvaluateNumTotal) {
        this.neutralEvaluateNumTotal = neutralEvaluateNumTotal;
    }

    public Integer getBadEvaluateNumTotal() {
        return badEvaluateNumTotal;
    }

    public void setBadEvaluateNumTotal(Integer badEvaluateNumTotal) {
        this.badEvaluateNumTotal = badEvaluateNumTotal;
    }

    public Integer getGoodEvaluateNumPreSale() {
        return goodEvaluateNumPreSale;
    }

    public void setGoodEvaluateNumPreSale(Integer goodEvaluateNumPreSale) {
        this.goodEvaluateNumPreSale = goodEvaluateNumPreSale;
    }

    public Integer getNeutralEvaluateNumPreSale() {
        return neutralEvaluateNumPreSale;
    }

    public void setNeutralEvaluateNumPreSale(Integer neutralEvaluateNumPreSale) {
        this.neutralEvaluateNumPreSale = neutralEvaluateNumPreSale;
    }

    public Integer getBadEvaluateNumPreSale() {
        return badEvaluateNumPreSale;
    }

    public void setBadEvaluateNumPreSale(Integer badEvaluateNumPreSale) {
        this.badEvaluateNumPreSale = badEvaluateNumPreSale;
    }

    public Integer getGoodEvaluateNumBetSale() {
        return goodEvaluateNumBetSale;
    }

    public void setGoodEvaluateNumBetSale(Integer goodEvaluateNumBetSale) {
        this.goodEvaluateNumBetSale = goodEvaluateNumBetSale;
    }

    public Integer getNeutralEvaluateNumBetSale() {
        return neutralEvaluateNumBetSale;
    }

    public void setNeutralEvaluateNumBetSale(Integer neutralEvaluateNumBetSale) {
        this.neutralEvaluateNumBetSale = neutralEvaluateNumBetSale;
    }

    public Integer getBadEvaluateNumBetSale() {
        return badEvaluateNumBetSale;
    }

    public void setBadEvaluateNumBetSale(Integer badEvaluateNumBetSale) {
        this.badEvaluateNumBetSale = badEvaluateNumBetSale;
    }

    public Integer getGoodEvaluateNumAfterSale() {
        return goodEvaluateNumAfterSale;
    }

    public void setGoodEvaluateNumAfterSale(Integer goodEvaluateNumAfterSale) {
        this.goodEvaluateNumAfterSale = goodEvaluateNumAfterSale;
    }

    public Integer getNeutralEvaluateNumAfterSale() {
        return neutralEvaluateNumAfterSale;
    }

    public void setNeutralEvaluateNumAfterSale(Integer neutralEvaluateNumAfterSale) {
        this.neutralEvaluateNumAfterSale = neutralEvaluateNumAfterSale;
    }

    public Integer getBadEvaluateNumAfterSale() {
        return badEvaluateNumAfterSale;
    }

    public void setBadEvaluateNumAfterSale(Integer badEvaluateNumAfterSale) {
        this.badEvaluateNumAfterSale = badEvaluateNumAfterSale;
    }

    public String getPersonalOutStockAmountPercentStr() {
        return personalOutStockAmountPercentStr;
    }

    public void setPersonalOutStockAmountPercentStr(String personalOutStockAmountPercentStr) {
        this.personalOutStockAmountPercentStr = personalOutStockAmountPercentStr;
    }

    public Double getPersonalOutStockAmountPercent() {
        return personalOutStockAmountPercent;
    }

    public void setPersonalOutStockAmountPercent(Double personalOutStockAmountPercent) {
        this.personalOutStockAmountPercent = personalOutStockAmountPercent;
    }

    public Integer getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Integer receiveNum) {
        this.receiveNum = receiveNum;
    }

    public Integer getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(Integer consultNum) {
        this.consultNum = consultNum;
    }

    public String getPaidNumTodayNextStr() {
        return paidNumTodayNextStr;
    }

    public void setPaidNumTodayNextStr(String paidNumTodayNextStr) {
        this.paidNumTodayNextStr = paidNumTodayNextStr;
    }

    public Integer getPaidGoodsNumToday() {
        return paidGoodsNumToday;
    }

    public void setPaidGoodsNumToday(Integer paidGoodsNumToday) {
        this.paidGoodsNumToday = paidGoodsNumToday;
    }

    public String getOrderedAmountFinalStr() {
        return orderedAmountFinalStr;
    }

    public void setOrderedAmountFinalStr(String orderedAmountFinalStr) {
        this.orderedAmountFinalStr = orderedAmountFinalStr;
    }

    public Double getOrderedAmountFinal() {
        return orderedAmountFinal;
    }

    public void setOrderedAmountFinal(Double orderedAmountFinal) {
        this.orderedAmountFinal = orderedAmountFinal;
    }

    public String getOrderedNumFinalStr() {
        return orderedNumFinalStr;
    }

    public void setOrderedNumFinalStr(String orderedNumFinalStr) {
        this.orderedNumFinalStr = orderedNumFinalStr;
    }

    public Double getQueryToOutStock() {
        return queryToOutStock;
    }

    public void setQueryToOutStock(Double queryToOutStock) {
        this.queryToOutStock = queryToOutStock;
    }

    public String getQueryToOutStockStr() {
        return queryToOutStockStr;
    }

    public void setQueryToOutStockStr(String queryToOutStockStr) {
        this.queryToOutStockStr = queryToOutStockStr;
    }

    public Double getQueryToFinalOrdered() {
        return queryToFinalOrdered;
    }

    public void setQueryToFinalOrdered(Double queryToFinalOrdered) {
        this.queryToFinalOrdered = queryToFinalOrdered;
    }

    public String getQueryToFinalOrderedStr() {
        return queryToFinalOrderedStr;
    }

    public void setQueryToFinalOrderedStr(String queryToFinalOrderedStr) {
        this.queryToFinalOrderedStr = queryToFinalOrderedStr;
    }

    public String getOrderedToPaidStr() {
        return orderedToPaidStr;
    }

    public void setOrderedToPaidStr(String orderedToPaidStr) {
        this.orderedToPaidStr = orderedToPaidStr;
    }

    public Double getOrderedToPaidFinal() {
        return orderedToPaidFinal;
    }

    public void setOrderedToPaidFinal(Double orderedToPaidFinal) {
        this.orderedToPaidFinal = orderedToPaidFinal;
    }

    public String getOrderedToPaidFinalStr() {
        return orderedToPaidFinalStr;
    }

    public void setOrderedToPaidFinalStr(String orderedToPaidFinalStr) {
        this.orderedToPaidFinalStr = orderedToPaidFinalStr;
    }

    public Integer getPaidNumToday() {
        return paidNumToday;
    }

    public void setPaidNumToday(Integer paidNumToday) {
        this.paidNumToday = paidNumToday;
    }

    public Double getOrderedToPaid() {
        return orderedToPaid;
    }

    public void setOrderedToPaid(Double orderedToPaid) {
        this.orderedToPaid = orderedToPaid;
    }

    public Integer getOrderedNumFinal() {
        return orderedNumFinal;
    }

    public void setOrderedNumFinal(Integer orderedNumFinal) {
        this.orderedNumFinal = orderedNumFinal;
    }

    public String getQueryToOrderedTodayStr() {
        return queryToOrderedTodayStr;
    }

    public void setQueryToOrderedTodayStr(String queryToOrderedTodayStr) {
        this.queryToOrderedTodayStr = queryToOrderedTodayStr;
    }

    public Double getQueryToOrderedToday() {
        return queryToOrderedToday;
    }

    public void setQueryToOrderedToday(Double queryToOrderedToday) {
        this.queryToOrderedToday = queryToOrderedToday;
    }

    public String getQueryToFinalPaidStr() {
        return queryToFinalPaidStr;
    }

    public void setQueryToFinalPaidStr(String queryToFinalPaidStr) {
        this.queryToFinalPaidStr = queryToFinalPaidStr;
    }

    public Double getQueryToFinalPaid() {
        return queryToFinalPaid;
    }

    public void setQueryToFinalPaid(Double queryToFinalPaid) {
        this.queryToFinalPaid = queryToFinalPaid;
    }

    public Integer getPaidNumFinal() {
        return paidNumFinal;
    }

    public void setPaidNumFinal(Integer paidNumFinal) {
        this.paidNumFinal = paidNumFinal;
    }

    public Double getPaidAmountFinal() {
        return paidAmountFinal;
    }

    public void setPaidAmountFinal(Double paidAmountFinal) {
        this.paidAmountFinal = paidAmountFinal;
    }

    public Integer getPaidNumTodayNext() {
        return paidNumTodayNext;
    }

    public void setPaidNumTodayNext(Integer paidNumTodayNext) {
        this.paidNumTodayNext = paidNumTodayNext;
    }

    public String getQueryToTomorrowStr() {
        return queryToTomorrowStr;
    }



    public void setQueryToTomorrowStr(String queryToTomorrowStr) {
        this.queryToTomorrowStr = queryToTomorrowStr;
    }

    public Double getQueryToTomorrow() {
        return queryToTomorrow;
    }

    public void setQueryToTomorrow(Double queryToTomorrow) {
        this.queryToTomorrow = queryToTomorrow;
    }

    public Double getOrderedGuestAvgAmount() {
        return orderedGuestAvgAmount;
    }

    public void setOrderedGuestAvgAmount(Double orderedGuestAvgAmount) {
        this.orderedGuestAvgAmount = orderedGuestAvgAmount;
    }

    public String getOrderedGuestAvgAmountStr() {
        return orderedGuestAvgAmountStr;
    }

    public void setOrderedGuestAvgAmountStr(String orderedGuestAvgAmountStr) {
        this.orderedGuestAvgAmountStr = orderedGuestAvgAmountStr;
    }

    public String getOrderedGuestAvgPriceStr() {
        return orderedGuestAvgPriceStr;
    }

    public void setOrderedGuestAvgPriceStr(String orderedGuestAvgPriceStr) {
        this.orderedGuestAvgPriceStr = orderedGuestAvgPriceStr;
    }

    public Double getOrderedGuestAvgPrice() {
        return orderedGuestAvgPrice;
    }

    public void setOrderedGuestAvgPrice(Double orderedGuestAvgPrice) {
        this.orderedGuestAvgPrice = orderedGuestAvgPrice;
    }

    public Integer getOrderedGoodsNumToday() {
        return orderedGoodsNumToday;
    }

    public void setOrderedGoodsNumToday(Integer orderedGoodsNumToday) {
        this.orderedGoodsNumToday = orderedGoodsNumToday;
    }

    public Double getOrderedAmountToday() {
        return orderedAmountToday;
    }

    public void setOrderedAmountToday(Double orderedAmountToday) {
        this.orderedAmountToday = orderedAmountToday;
    }

    public Integer getOrderedNumToday() {
        return orderedNumToday;
    }




    public void setOrderedNumToday(Integer orderedNumToday) {
        this.orderedNumToday = orderedNumToday;
    }

    public String getOrderItemAvgAmountStr() {
        return orderItemAvgAmountStr;
    }

    public void setOrderItemAvgAmountStr(String orderItemAvgAmountStr) {
        this.orderItemAvgAmountStr = orderItemAvgAmountStr;
    }

    public Double getOrderItemAvgAmount() {
        return orderItemAvgAmount;
    }

    public void setOrderItemAvgAmount(Double orderItemAvgAmount) {
        this.orderItemAvgAmount = orderItemAvgAmount;
    }

    public Double getOutStockItemAvgAmount() {
        return outStockItemAvgAmount;
    }

    public void setOutStockItemAvgAmount(Double outStockItemAvgAmount) {
        this.outStockItemAvgAmount = outStockItemAvgAmount;
    }

    public String getOutStockItemAvgAmountStr() {
        return outStockItemAvgAmountStr;
    }

    public void setOutStockItemAvgAmountStr(String outStockItemAvgAmountStr) {
        this.outStockItemAvgAmountStr = outStockItemAvgAmountStr;
    }

    public Integer getOutStockGuestItemNumStr() {
        return outStockGuestItemNumStr;
    }

    public void setOutStockGuestItemNumStr(Integer outStockGuestItemNumStr) {
        this.outStockGuestItemNumStr = outStockGuestItemNumStr;
    }

    public Integer getOutStockGuestItemNum() {
        return outStockGuestItemNum;
    }

    public void setOutStockGuestItemNum(Integer outStockGuestItemNum) {
        this.outStockGuestItemNum = outStockGuestItemNum;
    }

    public Double getOutStockAmount() {
        return outStockAmount;
    }

    public void setOutStockAmount(Double outStockAmount) {
        this.outStockAmount = outStockAmount;
    }

    public Integer getOutStockGoodsNum() {
        return outStockGoodsNum;
    }

    public void setOutStockGoodsNum(Integer outStockGoodsNum) {
        this.outStockGoodsNum = outStockGoodsNum;
    }

    public Integer getOutStockOrderNum() {
        return outStockOrderNum;
    }

    public void setOutStockOrderNum(Integer outStockOrderNum) {
        this.outStockOrderNum = outStockOrderNum;
    }

    public Integer getOutStockNum() {
        return outStockNum;
    }

    public void setOutStockNum(Integer outStockNum) {
        this.outStockNum = outStockNum;
    }

    public Double getOutStockGuestAvgAmount() {
        return outStockGuestAvgAmount;
    }

    public void setOutStockGuestAvgAmount(Double outStockGuestAvgAmount) {
        this.outStockGuestAvgAmount = outStockGuestAvgAmount;
    }

    public String getOutStockGuestAvgAmountStr() {
        return outStockGuestAvgAmountStr;
    }

    public void setOutStockGuestAvgAmountStr(String outStockGuestAvgAmountStr) {
        this.outStockGuestAvgAmountStr = outStockGuestAvgAmountStr;
    }

    public String getSaleGoodsAvgAmountStr() {
        return saleGoodsAvgAmountStr;
    }

    public void setSaleGoodsAvgAmountStr(String saleGoodsAvgAmountStr) {
        this.saleGoodsAvgAmountStr = saleGoodsAvgAmountStr;
    }

    public Double getSaleGoodsAvgAmount() {
        return saleGoodsAvgAmount;
    }

    public void setSaleGoodsAvgAmount(Double saleGoodsAvgAmount) {
        this.saleGoodsAvgAmount = saleGoodsAvgAmount;
    }

    public String getSaleGuestAvgGoodsStr() {
        return saleGuestAvgGoodsStr;
    }

    public void setSaleGuestAvgGoodsStr(String saleGuestAvgGoodsStr) {
        this.saleGuestAvgGoodsStr = saleGuestAvgGoodsStr;
    }

    public Integer getSaleGuestAvgGoods() {
        return saleGuestAvgGoods;
    }

    public void setSaleGuestAvgGoods(Integer saleGuestAvgGoods) {
        this.saleGuestAvgGoods = saleGuestAvgGoods;
    }

    public String getSaleGuestAvgAmountStr() {
        return saleGuestAvgAmountStr;
    }

    public void setSaleGuestAvgAmountStr(String saleGuestAvgAmountStr) {
        this.saleGuestAvgAmountStr = saleGuestAvgAmountStr;
    }

    public Double getSaleGuestAvgAmount() {
        return saleGuestAvgAmount;
    }

    public void setSaleGuestAvgAmount(Double saleGuestAvgAmount) {
        this.saleGuestAvgAmount = saleGuestAvgAmount;
    }

    public String getEnquiryNumStr() {
        return enquiryNumStr;
    }

    public void setEnquiryNumStr(String enquiryNumStr) {
        this.enquiryNumStr = enquiryNumStr;
    }

    public Integer getEnquiryNum() {
        return enquiryNum;
    }

    public void setEnquiryNum(Integer enquiryNum) {
        this.enquiryNum = enquiryNum;
    }

    public Double getPostFee() {
        return postFee;
    }

    public void setPostFee(Double postFee) {
        this.postFee = postFee;
    }

    public Integer getSaleOrderNum() {
        return saleOrderNum;
    }

    public void setSaleOrderNum(Integer saleOrderNum) {
        this.saleOrderNum = saleOrderNum;
    }

    public Integer getSaleBuyerNum() {
        return saleBuyerNum;
    }

    public void setSaleBuyerNum(Integer saleBuyerNum) {
        this.saleBuyerNum = saleBuyerNum;
    }

    public Integer getSaleGoodsNum() {
        return saleGoodsNum;
    }

    public void setSaleGoodsNum(Integer saleGoodsNum) {
        this.saleGoodsNum = saleGoodsNum;
    }

    public String getSaleAmountPercentStr() {
        return saleAmountPercentStr;
    }

    public void setSaleAmountPercentStr(String saleAmountPercentStr) {
        this.saleAmountPercentStr = saleAmountPercentStr;
    }

    public Double getSaleAmountPercent() {
        return saleAmountPercent;
    }

    public void setSaleAmountPercent(Double saleAmountPercent) {
        this.saleAmountPercent = saleAmountPercent;
    }

    public Double getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(Double saleAmount) {
        this.saleAmount = saleAmount;
    }


    public void printDoc(){
        Field[] declaredFields = CsPerformanceVo.class.getDeclaredFields();
        final String template = "|%s|%s|%s|";
        StringBuilder sb = new StringBuilder(1000);
        for (Field declaredField : declaredFields) {
            ReflectionUtils.makeAccessible(declaredField);
            sb.append(String.format(template,declaredField.getName()
                    ,declaredField.getType().getSimpleName(),declaredField.getAnnotation(Property.class).value()));
            sb.append("\n");
        }
        System.out.println(sb.toString());
    }

}
