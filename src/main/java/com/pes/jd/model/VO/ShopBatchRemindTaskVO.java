package com.pes.jd.model.VO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * (PesShopBatchRemindTask)实体类
 *
 * <AUTHOR>
 * @since 2021-02-23 10:11:15
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ShopBatchRemindTaskVO {
    private Long id;
    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 是否开启 0 暂停 1 开启
     */
    private Integer isRemind;
    /**
     * 是否永久生效 1 永久 0 限时
     */
    private Integer isPermanent;
    /**
     * 1 普通咨询未下单
     * 2 现货下单未付款
     * 3 预售咨询未下单
     * 4 预售下单未付定金
     * 5 预售付定金未付尾款
     * 6 预约商品咨询未预约
     * 7 预约商品未下单
     * 8 预约商品下单未付款
     */
    private Integer taskType;

    /**
     * 不是永久生效的任务 ：任务开始时间
     */
    private Date taskStartTime;
    /**
     * 不是永久生效的任务 ：任务结束时间
     */
    private Date taskEndTime;
    /**
     * 提醒话术
     */
    private String remindWord;
    /**
     * 通用商品 1 指定2
     */
    private Integer scope;
    /**
     * skuId集合
     */
    private List<Long> skuIdLst;
    /**
     * sku的名称集合
     */
    private List<String> skuName;

    /**
     * 维度 1sku 2 spu
     */
    private Integer dimension;

    /**
     * 短信下的咨询类型话术
     */
    private String consultWord;
    /**
     * 短信下的静默类型话术
     */
    private String silenceWord;

    private List<Long> spuIdList;

    /**
     * 判断问题任务的"？"有没有被点击过
     */
    private boolean prompt;
    /**
     * 判断该条问题展示在任务列表的标识有没有被点击过
     */
    private boolean problem;
}