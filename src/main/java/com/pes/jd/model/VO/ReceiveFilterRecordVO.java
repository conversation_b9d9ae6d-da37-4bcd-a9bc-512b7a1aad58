package com.pes.jd.model.VO;

import java.io.Serializable;
import java.util.Date;

/**  
 * ClassName:ReciveRecordVO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月12日 上午11:36:37 <br/>  `
 * 
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ReceiveFilterRecordVO implements Serializable{

	/**  
	 * serialVersionUID:TODO(用一句话描述这个变量表示什么).  
	 * @since JDK 1.8  
	 */
	private static final long serialVersionUID = -341265559031458705L;

	private Long shopId;
	private Date date;// 接待日期
	private String csNick;// 咚咚
	private String csSimpleNick;//咚咚简称
	private String buyerNick;// 买家旺旺
	private String reciveFilterType;// 接待过滤状态
    private Date recEarliestDate;//最早接待时间
    private Date recLatestDate;//最晚接待时间
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public String getCsNick() {
		return csNick;
	}
	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}
	public String getBuyerNick() {
		return buyerNick;
	}
	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}
	public Date getRecEarliestDate() {
		return recEarliestDate;
	}
	public void setRecEarliestDate(Date recEarliestDate) {
		this.recEarliestDate = recEarliestDate;
	}
	public Date getRecLatestDate() {
		return recLatestDate;
	}
	public void setRecLatestDate(Date recLatestDate) {
		this.recLatestDate = recLatestDate;
	}
	
	public String getCsSimpleNick() {
		return csSimpleNick;
	}
	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}
	public String getReciveFilterType() {
		return reciveFilterType;
	}
	public void setReciveFilterType(String reciveFilterType) {
		this.reciveFilterType = reciveFilterType;
	}
	
	
	
}
  
