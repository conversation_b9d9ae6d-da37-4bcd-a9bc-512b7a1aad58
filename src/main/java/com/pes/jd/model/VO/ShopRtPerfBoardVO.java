package com.pes.jd.model.VO;

public class ShopRtPerfBoardVO {
	
	private double saleAmount;//销售额
	
	private int saleOrderNum;//付款订单数
	
    private int orderedBuyerNum;//下单人数
    
    private int orderedNum;//下单数
    
    private int saleBuyerNum;//付款人数
    
    private double enquireToPayRate;//询单付款转化率
    
    //和昨日对比
    private double orderedBuyerNumYesRate = 0.0;
    
    private double saleOrderNumYesRate = 0.0;

    private double orderedNumYesRate = 0.0;

    private double saleBuyerNumYesRate = 0.0;

    private double enquireToPayRateYesRate = 0.0;

    
    
	
	private int nonReplySessionNum;//未回复量
	
	private double avgResTimeFirst;//首次平均响应
	
	private double avgResTime;//平均响应
	
	private double replyRate;//回复率
	
	private double satisRate;//满意率
	
	private double quickResRate;//快速应答率

	private int subscribeCount;//店铺订购总数

	public double getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(double saleAmount) {
		this.saleAmount = saleAmount;
	}

	public int getSaleOrderNum() {
		return saleOrderNum;
	}

	public void setSaleOrderNum(int saleOrderNum) {
		this.saleOrderNum = saleOrderNum;
	}

	public int getOrderedBuyerNum() {
		return orderedBuyerNum;
	}

	public void setOrderedBuyerNum(int orderedBuyerNum) {
		this.orderedBuyerNum = orderedBuyerNum;
	}

	public int getOrderedNum() {
		return orderedNum;
	}

	public void setOrderedNum(int orderedNum) {
		this.orderedNum = orderedNum;
	}

	public int getSaleBuyerNum() {
		return saleBuyerNum;
	}

	public void setSaleBuyerNum(int saleBuyerNum) {
		this.saleBuyerNum = saleBuyerNum;
	}

	public double getEnquireToPayRate() {
		return enquireToPayRate;
	}

	public void setEnquireToPayRate(double enquireToPayRate) {
		this.enquireToPayRate = enquireToPayRate;
	}

	public double getOrderedBuyerNumYesRate() {
		return orderedBuyerNumYesRate;
	}

	public void setOrderedBuyerNumYesRate(double orderedBuyerNumYesRate) {
		this.orderedBuyerNumYesRate = orderedBuyerNumYesRate;
	}

	public double getSaleOrderNumYesRate() {
		return saleOrderNumYesRate;
	}

	public void setSaleOrderNumYesRate(double saleOrderNumYesRate) {
		this.saleOrderNumYesRate = saleOrderNumYesRate;
	}

	public double getOrderedNumYesRate() {
		return orderedNumYesRate;
	}

	public void setOrderedNumYesRate(double orderedNumYesRate) {
		this.orderedNumYesRate = orderedNumYesRate;
	}

	public double getSaleBuyerNumYesRate() {
		return saleBuyerNumYesRate;
	}

	public void setSaleBuyerNumYesRate(double saleBuyerNumYesRate) {
		this.saleBuyerNumYesRate = saleBuyerNumYesRate;
	}

	public double getEnquireToPayRateYesRate() {
		return enquireToPayRateYesRate;
	}

	public void setEnquireToPayRateYesRate(double enquireToPayRateYesRate) {
		this.enquireToPayRateYesRate = enquireToPayRateYesRate;
	}

	public int getNonReplySessionNum() {
		return nonReplySessionNum;
	}

	public void setNonReplySessionNum(int nonReplySessionNum) {
		this.nonReplySessionNum = nonReplySessionNum;
	}

	public double getAvgResTimeFirst() {
		return avgResTimeFirst;
	}

	public void setAvgResTimeFirst(double avgResTimeFirst) {
		this.avgResTimeFirst = avgResTimeFirst;
	}

	public double getAvgResTime() {
		return avgResTime;
	}

	public void setAvgResTime(double avgResTime) {
		this.avgResTime = avgResTime;
	}

	public double getReplyRate() {
		return replyRate;
	}

	public void setReplyRate(double replyRate) {
		this.replyRate = replyRate;
	}

	public double getSatisRate() {
		return satisRate;
	}

	public void setSatisRate(double satisRate) {
		this.satisRate = satisRate;
	}

	public double getQuickResRate() {
		return quickResRate;
	}

	public void setQuickResRate(double quickResRate) {
		this.quickResRate = quickResRate;
	}

	public int getSubscribeCount() {
		return subscribeCount;
	}

	public void setSubscribeCount(int subscribeCount) {
		this.subscribeCount = subscribeCount;
	}
	
 
    
}
