package com.pes.jd.model.VO;

import com.pes.jd.model.DO.PesServicePermission;
import com.pes.jd.model.DTO.ShopAccountDTO;

import java.util.List;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/12/17 2:35 PM
 * @since 1.0.0
 */
public class PermissionSettingListVo {
    /**
     * 子账号信息
     */
    private ShopAccountDTO shopAccountDTO;

    /**
     *  数据权限
     */
    private List<PesServicePermission> dataPermissionLst;

    /**
     *  功能权限
     */
    private List<PesServicePermission> functionPermissionLst;

    /**
     *  菜单树
     */
    private List<PesMenuResourceVo> menuResourceLst;

    public ShopAccountDTO getShopAccountDTO() {
        return shopAccountDTO;
    }

    public void setShopAccountDTO(ShopAccountDTO shopAccountDTO) {
        this.shopAccountDTO = shopAccountDTO;
    }

    public List<PesServicePermission> getDataPermissionLst() {
        return dataPermissionLst;
    }

    public void setDataPermissionLst(List<PesServicePermission> dataPermissionLst) {
        this.dataPermissionLst = dataPermissionLst;
    }

    public List<PesServicePermission> getFunctionPermissionLst() {
        return functionPermissionLst;
    }

    public void setFunctionPermissionLst(List<PesServicePermission> functionPermissionLst) {
        this.functionPermissionLst = functionPermissionLst;
    }

    public List<PesMenuResourceVo> getMenuResourceLst() {
        return menuResourceLst;
    }

    public void setMenuResourceLst(List<PesMenuResourceVo> menuResourceLst) {
        this.menuResourceLst = menuResourceLst;
    }
}
