package com.pes.jd.model.VO;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

import java.util.Date;

public class CsOrderIndexDTO {
	private Long id;
	
	private Long shopId;
	
	private Date date;
	
	private String csNick;
	
	private String buyNick;
	
	@JSONField(serializeUsing=ToStringSerializer.class)
	private Long orderId;
	
	private Integer assistType;
	
	private Date payDate;
	
	private Double payMent;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public String getBuyNick() {
		return buyNick;
	}

	public void setBuyNick(String buyNick) {
		this.buyNick = buyNick;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Integer getAssistType() {
		return assistType;
	}

	public void setAssistType(Integer assistType) {
		this.assistType = assistType;
	}

	public Date getPayDate() {
		return payDate;
	}

	public void setPayDate(Date payDate) {
		this.payDate = payDate;
	}

	public Double getPayMent() {
		return payMent;
	}

	public void setPayMent(Double payMent) {
		this.payMent = payMent;
	}
	
	
	
	
	
}
