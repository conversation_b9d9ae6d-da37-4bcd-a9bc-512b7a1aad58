package com.pes.jd.model.VO;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class PresalePerformanceVO {

    private Long shopId;
    private String activityId;
    private Date date;//日期
    private String csNick;     // 客服id
    private String csSimpleNick;    // 客服昵称
    private String skuName;     // 商品名称
    private Long skuId;       // 商品编号
    private Date bargainStartTime; //定金开始时间 用以排序
    private String bargainTime; // 定金期
    private String balanceTime; // 尾款期
    private Integer consultBuyerNum; // 咨询人数
    private Integer enquiryBuyerNum; // 询单人数
    private Integer orderedBuyerNum; // 下单人数
    private Integer orderedSkuNum;   // 下单件数
    private Integer orderedBargainBuyerNum;  // 付定金人数
    private Integer orderedBargainSkuNum;    // 付定金件数
    private Double orderedBargainAmount;    //付定金金额
    private Integer orderedBalanceBuyerNum;  //付尾款人数
    private Integer orderedBalanceSkuNum;    //付尾款件数
    private Double orderedBalanceAmount;    //付尾款金额
    private Double consultToOrderdPercent;  //询单👉下单转化
    private Double consultToBargainPercent; //询单👉付定金转化
    private Double consultToBalancePercent; //询单👉付尾款转化
    private Integer toOrderedBargainBuyerNum;    //落实付定金人数
    private Integer toOrderedBalanceBuyerNum;    //落实付尾款人数
    private Double toBargainToBalancePercent;   //落实定金👉尾款转化
    private Double orderedToBargainPercent; //下单👉付定金转化
    private Double orderedToBalancePercent; //下单👉付尾款转化
    private Double bargainToBalancePercent; //定金👉尾款转化

    private Set<String> notFinalData; //非最终字段标记


    public PresalePerformanceVO() {
        consultBuyerNum = 0;
        enquiryBuyerNum = 0;
        orderedBuyerNum = 0;
        orderedSkuNum = 0;
        orderedBargainBuyerNum = 0;
        orderedBargainSkuNum = 0;
        orderedBargainAmount = 0.0;
        orderedBalanceBuyerNum = 0;
        orderedBalanceSkuNum = 0;
        orderedBalanceAmount = 0.0;
        consultToOrderdPercent = 0.0;
        consultToBargainPercent = 0.0;
        consultToBalancePercent = 0.0;
        toOrderedBargainBuyerNum = 0;
        toOrderedBalanceBuyerNum = 0;
        toBargainToBalancePercent = 0.0;
        orderedToBargainPercent = 0.0;
        orderedToBalancePercent = 0.0;
        bargainToBalancePercent = 0.0;
        notFinalData = new HashSet<>();
    }
}
