package com.pes.jd.model.VO;

import java.util.Date;

public class RealTimeCsPerformanceCompareVO {
	private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private int consultNum;//咨询量

    private int receiveNum;//接待量

    private int enquiryNum;//询单人数

    private double saleAmount;//销售额

    private int saleGoodsNum;//销售量

    private int saleBuyerNum;//销售人数
    
    private int orderNumToday;//下单人数
    
    //本客服销售客件数=本客服促成销售量/本客服促成销售人数
    private double saleGoodsNumByBuyerNum;
    
    //本客服销售客单价=本客服促成销售额/本客服促成销售人数
    private double saleAmountByBuyerNum;
    
    //下单未付款人数=下单人数-下单已付款人数
    private int orderedUnpayNum;
    
    //询单未下单人数=询单人数-询单已下单人数
    private int enquiryUnOrderedNum;
    
    //询单-下单转化率=当日下单人数/询单人数
    private double enquiryToOrderRate;
    
    //询单-付款转化率=当日付款人数/询单人数
    private double enquiryToPayRate;
    
    //下单-付款转化率=当日付款人数/下单人数
    private double orderToPayRate;

    //未回复量
    private int unReplyNum;
    
    //首次平均响应
    private double avgWaitTimeFirst;
    
    //平均响应
    private double avgWaitTime;
    
    //平均接待时长
    private double avgReceiveSsessionTime;
    
   //回复率=（接待量-未回复量）/接待量
    private double replyRate;
    
    //邀评率=邀评量/接待量
    private double sendEvaluatRate;
    
    //评价率=评价量/接待量
    private double evaluateRate;
    
    //满意率=（非常满意+满意量）
    private double satisRate;
    
    private int evaluateNum;//评价量
    
    private int sendEvalNum;//邀评量
    
    private int paidNumToday;//付款人数
    
    private String csSimpleNick;
    
    private int type;//判断客服售前售后

	private Boolean filterFlag;//分组均值计算过滤标识
	
	private double quickResRate;

	public double getQuickResRate() {
		return quickResRate;
	}

	public void setQuickResRate(double quickResRate) {
		this.quickResRate = quickResRate;
	}

	public Boolean getFilterFlag() {
		return filterFlag;
	}

	public void setFilterFlag(Boolean filterFlag) {
		this.filterFlag = filterFlag;
	}

    public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getCsSimpleNick() {
		return csSimpleNick;
	}

	public void setCsSimpleNick(String csSimpleNick) {
		this.csSimpleNick = csSimpleNick;
	}
    
    public Integer getPaidNumToday() {
		return paidNumToday;
	}

	public void setPaidNumToday(Integer paidNumToday) {
		this.paidNumToday = paidNumToday;
	}

    
    public Integer getSendEvalNum() {
		return sendEvalNum;
	}

	public void setSendEvalNum(Integer sendEvalNum) {
		this.sendEvalNum = sendEvalNum;
	}

	

	public Integer getEvaluateNum() {
		return evaluateNum;
	}

	public void setEvaluateNum(Integer evaluateNum) {
		this.evaluateNum = evaluateNum;
	}

	public Double getAvgReceiveSsessionTime() {
		return avgReceiveSsessionTime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Integer getConsultNum() {
		return consultNum;
	}

	public void setConsultNum(Integer consultNum) {
		this.consultNum = consultNum;
	}

	public Integer getReceiveNum() {
		return receiveNum;
	}

	public void setReceiveNum(Integer receiveNum) {
		this.receiveNum = receiveNum;
	}

	public Integer getEnquiryNum() {
		return enquiryNum;
	}

	public void setEnquiryNum(Integer enquiryNum) {
		this.enquiryNum = enquiryNum;
	}

	public Double getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(Double saleAmount) {
		this.saleAmount = saleAmount;
	}

	public Integer getSaleGoodsNum() {
		return saleGoodsNum;
	}

	public void setSaleGoodsNum(Integer saleGoodsNum) {
		this.saleGoodsNum = saleGoodsNum;
	}

	public Integer getSaleBuyerNum() {
		return saleBuyerNum;
	}

	public void setSaleBuyerNum(Integer saleBuyerNum) {
		this.saleBuyerNum = saleBuyerNum;
	}

	public Double getSaleGoodsNumByBuyerNum() {
		return saleGoodsNumByBuyerNum;
	}

	public void setSaleGoodsNumByBuyerNum(Double saleGoodsNumByBuyerNum) {
		this.saleGoodsNumByBuyerNum = saleGoodsNumByBuyerNum;
	}

	public Double getSaleAmountByBuyerNum() {
		return saleAmountByBuyerNum;
	}

	public void setSaleAmountByBuyerNum(Double saleAmountByBuyerNum) {
		this.saleAmountByBuyerNum = saleAmountByBuyerNum;
	}

	public Integer getOrderedUnpayNum() {
		return orderedUnpayNum;
	}

	public void setOrderedUnpayNum(Integer orderedUnpayNum) {
		this.orderedUnpayNum = orderedUnpayNum;
	}

	public Integer getEnquiryUnOrderedNum() {
		return enquiryUnOrderedNum;
	}

	public void setEnquiryUnOrderedNum(Integer enquiryUnOrderedNum) {
		this.enquiryUnOrderedNum = enquiryUnOrderedNum;
	}

	public Double getEnquiryToOrderRate() {
		return enquiryToOrderRate;
	}

	public void setEnquiryToOrderRate(Double enquiryToOrderRate) {
		this.enquiryToOrderRate = enquiryToOrderRate;
	}

	public Double getEnquiryToPayRate() {
		return enquiryToPayRate;
	}

	public void setEnquiryToPayRate(Double enquiryToPayRate) {
		this.enquiryToPayRate = enquiryToPayRate;
	}

	public Double getOrderToPayRate() {
		return orderToPayRate;
	}

	public void setOrderToPayRate(Double orderToPayRate) {
		this.orderToPayRate = orderToPayRate;
	}

	public Integer getUnReplyNum() {
		return unReplyNum;
	}

	public void setUnReplyNum(Integer unReplyNum) {
		this.unReplyNum = unReplyNum;
	}

	public Double getAvgWaitTimeFirst() {
		return avgWaitTimeFirst;
	}

	public void setAvgWaitTimeFirst(Double avgWaitTimeFirst) {
		this.avgWaitTimeFirst = avgWaitTimeFirst;
	}

	public Double getAvgWaitTime() {
		return avgWaitTime;
	}

	public void setAvgWaitTime(Double avgWaitTime) {
		this.avgWaitTime = avgWaitTime;
	}

	public void setAvgReceiveSsessionTime(Double avgReceiveSsessionTime) {
		this.avgReceiveSsessionTime = avgReceiveSsessionTime;
	}
	
	public Double getReplyRate() {
		return replyRate;
	}

	public void setReplyRate(Double replyRate) {
		this.replyRate = replyRate;
	}

	public Double getSendEvaluatRate() {
		return sendEvaluatRate;
	}

	public void setSendEvaluatRate(Double sendEvaluatRate) {
		this.sendEvaluatRate = sendEvaluatRate;
	}

	public Double getEvaluateRate() {
		return evaluateRate;
	}

	public void setEvaluateRate(Double evaluateRate) {
		this.evaluateRate = evaluateRate;
	}

	public Double getSatisRate() {
		return satisRate;
	}

	public void setSatisRate(Double satisRate) {
		this.satisRate = satisRate;
	}

	public Integer getOrderNumToday() {
		return orderNumToday;
	}

	public void setOrderNumToday(Integer orderNumToday) {
		this.orderNumToday = orderNumToday;
	}

}