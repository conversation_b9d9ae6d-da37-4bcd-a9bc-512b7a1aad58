package com.pes.jd.model.VO;  
/**  
 * ClassName:ShopVO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月7日 下午4:26:08 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ShopVO {
	private Long shopId;
	private String sellerNick;
	private String title;
	private String sessionKey;
	private String status;
	private String db;
	private String schemaId;
	private Integer judgeRule;//判定规则
	
	public Integer getJudgeRule() {
		return judgeRule;
	}
	public void setJudgeRule(Integer judgeRule) {
		this.judgeRule = judgeRule;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getSellerNick() {
		return sellerNick;
	}
	public void setSellerNick(String sellerNick) {
		this.sellerNick = sellerNick;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getSessionKey() {
		return sessionKey;
	}
	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getDb() {
		return db;
	}
	public void setDb(String db) {
		this.db = db;
	}
	public String getSchemaId() {
		return schemaId;
	}
	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}
	@Override
	public String toString() {
		return "ShopVO [shopId=" + shopId + ", sellerNick=" + sellerNick + ", title=" + title + ", sessionKey="
				+ sessionKey + ", status=" + status + ", db=" + db + ", schemaId=" + schemaId + ", judgeRule="
				+ judgeRule + "]";
	}
}
  
