package com.pes.jd.model.VO;

import java.io.Serializable;

public class DotVO implements Serializable {

    /**
     *	页面访问次数
     */
   private Double pageVisitTime;

    /**
     * 次均停留时间
     */
   private Double averageStopTime;

    /**
     * 店铺访问数
     */
   private Double shopVisitTime;

    /**
     * 访问用户数
     */
   private Double nickVisitTime;

    /**
     * 页面访问名称
     */
   private String pageVisitName;

    /**
     * 店铺访问数占比
     */
   private Double shopVisitTimeProportion;

    /**
     * 访问用户数占比
     */
   private Double nickVisitTimeProportion;

    /**
     * 人均使用时间
     */
   private Double perCapitaUserTime;

    /**
     * 页面访问次数占比
     */
   private Double pageVisitTimeProportion;

    /**
     * 次均停留时间占比
     */
   private Double averageStopTimeProportion;

    /**
     * 客服昵称
     */
   private String nick;

    /**
     * 店铺名称
     */
   private String shopName;

    /**
     * 访问时长
     */
   private Double visitDuration;

   public DotVO(){

   }

    public DotVO(Double pageVisitTime, Double averageStopTime, Double shopVisitTime, Double nickVisitTime, String pageVisitName, Double shopVisitTimeProportion, Double nickVisitTimeProportion, Double perCapitaUserTime, Double pageVisitTimeProportion, Double averageStopTimeProportion, String nick, String shopName, Double visitDuration) {
        this.pageVisitTime = pageVisitTime;
        this.averageStopTime = averageStopTime;
        this.shopVisitTime = shopVisitTime;
        this.nickVisitTime = nickVisitTime;
        this.pageVisitName = pageVisitName;
        this.shopVisitTimeProportion = shopVisitTimeProportion;
        this.nickVisitTimeProportion = nickVisitTimeProportion;
        this.perCapitaUserTime = perCapitaUserTime;
        this.pageVisitTimeProportion = pageVisitTimeProportion;
        this.averageStopTimeProportion = averageStopTimeProportion;
        this.nick = nick;
        this.shopName = shopName;
        this.visitDuration = visitDuration;
    }

    public Double getPageVisitTime() {
        return pageVisitTime;
    }

    public void setPageVisitTime(Double pageVisitTime) {
        this.pageVisitTime = pageVisitTime;
    }

    public Double getAverageStopTime() {
        return averageStopTime;
    }

    public void setAverageStopTime(Double averageStopTime) {
        this.averageStopTime = averageStopTime;
    }

    public Double getShopVisitTime() {
        return shopVisitTime;
    }

    public void setShopVisitTime(Double shopVisitTime) {
        this.shopVisitTime = shopVisitTime;
    }

    public Double getNickVisitTime() {
        return nickVisitTime;
    }

    public void setNickVisitTime(Double nickVisitTime) {
        this.nickVisitTime = nickVisitTime;
    }

    public String getPageVisitName() {
        return pageVisitName;
    }

    public void setPageVisitName(String pageVisitName) {
        this.pageVisitName = pageVisitName;
    }

    public Double getShopVisitTimeProportion() {
        return shopVisitTimeProportion;
    }

    public void setShopVisitTimeProportion(Double shopVisitTimeProportion) {
        this.shopVisitTimeProportion = shopVisitTimeProportion;
    }

    public Double getNickVisitTimeProportion() {
        return nickVisitTimeProportion;
    }

    public void setNickVisitTimeProportion(Double nickVisitTimeProportion) {
        this.nickVisitTimeProportion = nickVisitTimeProportion;
    }

    public Double getPerCapitaUserTime() {
        return perCapitaUserTime;
    }

    public void setPerCapitaUserTime(Double perCapitaUserTime) {
        this.perCapitaUserTime = perCapitaUserTime;
    }

    public Double getPageVisitTimeProportion() {
        return pageVisitTimeProportion;
    }

    public void setPageVisitTimeProportion(Double pageVisitTimeProportion) {
        this.pageVisitTimeProportion = pageVisitTimeProportion;
    }

    public Double getAverageStopTimeProportion() {
        return averageStopTimeProportion;
    }

    public void setAverageStopTimeProportion(Double averageStopTimeProportion) {
        this.averageStopTimeProportion = averageStopTimeProportion;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Double getVisitDuration() {
        return visitDuration;
    }

    public void setVisitDuration(Double visitDuration) {
        this.visitDuration = visitDuration;
    }
}
