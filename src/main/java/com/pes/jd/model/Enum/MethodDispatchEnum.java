package com.pes.jd.model.Enum;

public enum MethodDispatchEnum {
	
	/**
	 * 初始化绩效
	 */
	INIT_PES("init_pes",1,"handle/initShopPes"),
	
	/**
	 * 实时绩效计算
	 */
	REALTIME_PES("realtime_pes",2,"handle/realtimePesCal"),
	/**
	 * 每日绩效计算-4点Job
	 */
	DAILY_PES("daily_pes",3,"handle/dailySchemaShopPesCal"),
	/**
	 * 手动拉取绩效数据
	 */
	PULL_PES("pull_pes",4,"handle/handleTaskJob"),
	/**
	 * 手动重算店铺绩效
	 */
	PULL_CAL("pull_cal",5,"handle/calCsPerformance"),
	/**
	 * 批量重算店铺绩效
	 */
	PULL_BATCH_CAL("pull_batch_cal",6,"handle/batchCalShopPf"),
	/**
	 * dsr,更新订购信息，API统计，fetch_flag
	 */
	COMMON_TASK("common_task",7,"handle/commonTask"),
	/**
	 * 手动-指定时间处理失败店铺
	 */
	PULL_PES_FAIL("pull_pes_fail",8,"handle/handleFalseShopPes"),
	/**
	 * 根据指定的nicks 和 时间 拉取店铺数据
	 */
	PULL_PES_NICKS("pull_pes_nicks",9,"handle/handleShopsPesByNicks"),
	/**
	 * 报表导出
	 */
	REPORT_EXPORT("report_export",10,"handle/reportExport"),
	/**
	 * 创建表
	 */
	TABLE_CREATE("table_create",11,"handle/createTable"),
	
	/**
	 * 错误店铺统计发送邮件提醒
	 */
	FAIL_SHOP_MAIL("fail_shop_mail",12,"handle/failShopMail");
	
   
	// 成员变量  
    private String type;
    private int tag;
    private String method;
    // 构造方法  
    private MethodDispatchEnum(String type, int tag, String method) {  
        this.type = type; 
        this.tag = tag;
        this.method = method;
    }  
    public static int getTag(String type) {  
        for (MethodDispatchEnum s : MethodDispatchEnum.values()) {  
            if (type.equals(s.getType())) {  
                return s.getTag();
            } 
        }
        return 0; 
    }
    
    public static String getType(int tag) {  
        for (MethodDispatchEnum s : MethodDispatchEnum.values()) {  
            if (s.getTag() == tag) {  
                return s.type;
            } 
        }
        return null; 
    }
    
    public static String getMethod(int tag) {  
        for (MethodDispatchEnum s : MethodDispatchEnum.values()) {  
            if (s.getTag() == tag) {  
                return s.method;
            } 
        }
        return null; 
    }
    
    public static MethodDispatchEnum getMethodDispatch(String type) {  
        for (MethodDispatchEnum s : MethodDispatchEnum.values()) {  
        	 if (type.equals(s.getType())) {  
                 return s;
             } 
        }
        return null; 
    }
    
	public String getType() {
		return type;
	}
	public int getTag() {
		return tag;
	}
	public String getMethod() {
		return method;
	}


	
	
}
