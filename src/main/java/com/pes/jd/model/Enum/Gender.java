package com.pes.jd.model.Enum;

public enum Gender {
    UNKNOW(2, "未知"),
    MAN(1, "男"),
    FEMALE(0, "女");

    private final int code;
    private final String description;

    Gender(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() { return code; }
    public String getDescription() { return description; }

    public static Gender fromCode(int code) {
        for (Gender sex : values()) {
            if (sex.code == code) return sex;
        }
        throw new IllegalArgumentException("Unknown sex code: " + code);
    }
}
