package com.pes.jd.model.Enum;

/**
 * <AUTHOR> <EMAIL>
 * @since 1.0.0
 */
public enum TableEnum {

	PES_CS_SERVICE_INDEX("pes_cs_service_index","买家维度平均响应时间,会话时长"),
    PES_CS_CHATLOG("pes_cs_chatlog","聊天记录"),
    PES_CS_CHATPEER("pes_cs_chatpeer","聊天关系"),
    PES_CS_CONVERSION("pes_cs_conversion","客户转化"),
    PES_CS_LOGINLOG("pes_cs_loginlog","客服登录"),
    PES_CS_ORDER_BIND("pes_cs_order_bind","客服订单绑定表"),
    PES_CS_ORDER_EVALUATION("pes_cs_order_evaluation","客服落实的交易中差评"),
    PES_CS_ORDER_INDEX("pes_cs_order_index","客服账号相关的订单指标"),
    PES_CS_PERFORMANCE("pes_cs_performance","客服绩效表"),
    PES_CS_REPLY_QUALITY("pes_cs_reply_quality","平均响应时间"),
    PES_CS_SERVICE_EVALUATION("pes_cs_service_evaluation","每日客服服务评价汇总"),
    PES_LOST_RECORD_NOTE("pes_lost_record_note","流失记录备注表"),
    PES_ORDER("pes_order","订单交易表"),
    PES_ORDER_DETAIL("pes_order_detail","订单详情表"),
    PES_ORDER_EVALUATE("pes_order_evaluate","交易评价表"),
    PES_ORDER_FILTER("pes_order_filter","订单过滤表"),
    PES_SHOP_DSR("pes_shop_dsr","店铺评分表"),
    PES_SHOP_OV_DAY("pes_shop_ov_day","店铺每日绩效"),
	PES_CS_BUYER_SERVICE_INDEX("pes_cs_buyer_service_index","客服，买家服务表"),
	PES_USER_PORTRAIT_AI_REPORT("pes_user_portrait_ai_report","用户画像AI分析报告表");
    private String name;
    private String details;

    TableEnum(String name, String desc) {
        this.name = name;
        this.details = desc;
    }

    public String getName() {
        return name;
    }
    
    public static TableEnum getEnumByName(String name){
    	for (TableEnum tableEnum : TableEnum.values()) {
			if(tableEnum.name.equals(name)){
				return tableEnum;
			}
		}
    	return null;
    }

    public String getDetails() {
        return details;
    }

    @Override
    public String toString() {
        return getName();
    }

}
