package com.pes.jd.model.Enum;

/**
 * <AUTHOR> <EMAIL>
 * @since 1.0.0
 */
public enum TableEnum {

	PES_CS_BUYER_SERVICE_INDEX("pes_cs_buyer_service_index","买家维度平均响应时间,会话时长"),
	PES_CS_SERVICE_INDEX("pes_cs_service_index","客服维度平均响应时间,会话时长"),
    PES_CS_CHATLOG("pes_cs_chatlog","聊天记录"),
    PES_CHAT_CLASSIFY("pes_chat_classify","聊天分类"),
    PES_CS_CHATPEER("pes_cs_chatpeer","聊天关系"),
    PES_CS_CONVERSION("pes_cs_conversion","客户转化"),
    PES_CS_LEAVE_MESSAGE("pes_cs_leave_message", "客服留言消息表"),
    PES_CS_LOGINLOG("pes_cs_loginlog","客服登录"),
    PES_CS_NOREPLY("pes_cs_noreply","客服未回复信息"),
    PES_CS_ORDER_BIND("pes_cs_order_bind","客服订单绑定表"),
    PES_CS_ORDER_EVALUATE("pes_cs_order_evaluate","客服落实的交易中差评"),
    PES_CS_ORDER_INDEX("pes_cs_order_index","客服账号相关的订单指标"),
    PES_CS_PERFORMANCE("pes_cs_performance","客服绩效表"),
    PES_SHOP_TEAM_TORDER_PERFORMANCE("pes_shop_team_torder_performance","店铺团队促成订单表"),
    PES_CS_REPLY_QUALITY("pes_cs_reply_quality","平均响应时间"),
    PES_CS_SERVICE_EVALUATION_DETAIL("pes_cs_service_evaluation_detail","每日客服服务评价汇总"),
    PES_CS_WORDNUM("pes_cs_wordnum","客服聊天次数"),
    PES_CS_TYPE_DAY("pes_cs_type_day", "客服每日状态"),
    PES_CS_TORDER_PERFORMANCE("pes_cs_torder_performance", "下单维度绩效"),
    PES_CS_LOSS_RECORD("pes_cs_loss_record","流失表"),
    PES_ORDER_CUSTOMER_INFO("pes_order_customer_info", "订单收货人信息表"),
    PES_LOSS_ENQUIRY_RECORD("pes_loss_enquiry_record", "询单流失"),
    PES_LOSS_ORDER_RECORD("pes_loss_order_record", "下单流失"),
    PES_CS_TO_ORDER_INDEX("pes_cs_to_order_index", "客服落实表"),
    PES_CS_SERVICE_SEND_EVAL("pes_cs_service_send_eval", "邀评表"),
    PES_CS_SERVICE_EVALUATION("pes_cs_service_evaluation","每日客服服务评价汇总"),
    PES_SHOP_TEAM_LOSS_RECORD("pes_shop_team_loss_record","团队流失表"),
    PES_LOST_RECORD_NOTE("pes_lost_record_note","流失记录备注表"),
    PES_CS_PERFORMANCE_PRESALE("pes_cs_performance_presale","预售客服绩效表"),
    PES_ORDER("pes_order","订单交易表"),
    PES_CS_ASSIT_INDEX("pes_cs_assit_index", "客服 协助表"),
    PES_CS_CHAT_SESSION_SERVICE_INDEX("pes_cs_chat_session_service_index", "客服聊天会话服务分析指标"),
    PES_CS_DUTY_RECORD("pes_cs_duty_record", "每日值班记录统计"),
    PES_CS_PERFORMANCE_PREORDAIN("pes_cs_performance_preordain","客服绩效-预约商品表"),
    PES_ORDER_SKU_EVALUATE("pes_order_sku_evaluate","交易评价表"),
    PES_ORDER_FILTER("pes_order_filter","订单过滤表"),
    PES_SHOP_OV_DAY("pes_shop_ov_day","店铺每日绩效"),
    PES_ORDER_GOODS_SKU("pes_order_goods_sku","订单SKU"),
	PES_SHOP_CATEGORY("pes_shop_category","店铺类目"),
    PES_SHOP_CATEGORY_V2("pes_shop_category_v2","店铺类目2"),
	PES_SHOP_GOODS("pes_shop_goods","店铺商品"),
	PES_CS_RECOMMEND_GOODS("pes_cs_recommend_goods","客服商品推荐"),
	PES_CUST_CONSULT_GOODS("pes_cust_consult_goods","客户咨询商品"),
	PES_CUST_CONSULT_GOODS_V2("pes_cust_consult_goods_new","客户咨询商品"),
	PES_GOODS_CONSULT_SUMMARY("pes_goods_consult_summary","客户咨询商品汇总"),
	PES_GOODS_CONSULT_SUMMARY_V2("pes_goods_consult_summary_new","客户咨询商品汇总"),
	PES_GOODS_RECOMMEND_SUMMARY("pes_goods_recommend_summary","客服商品推荐汇总"),
	PES_SHOP_REFUND_DAY("pes_shop_refund_day","店铺维度退款"),
	PES_CS_REFUND_DAY("pes_cs_refund_day","客服维度退款"),
	PES_ORDER_REFUND("pes_order_refund","订单退款"),
	PES_CS_GOODS_SALE_INDEX("pes_cs_goods_sale_index","客服商品销售指标明细"),
	PES_CS_GOODS_SALE_INDEX_DETAIL("pes_cs_goods_sale_index_detail","客服商品销售汇总指标"),
	PES_SLIENT_GOODS_SALE_INDEX("pes_slient_goods_sale_index","静默商品销售指标明细"),
	PES_SLIENT_GOODS_SALE_INDEX_DETAIL("pes_slient_goods_sale_index_detail","静默商品销售汇总指标"),
	PES_SHOP_GOODS_SKU("pes_shop_goods_sku","店铺商品sku"),
	PES_SHOP_WATCHWORD("pes_shop_watchword","暗语表"),
	PES_CS_CHAT_SESSION("pes_cs_chat_session","聊天会话表"),
	PES_SHOP_PV_UV_DAY("pes_shop_pv_uv_day","店铺每日pv uv"),
	PES_PULL_JOB_RECORD("pes_pull_job_record","JOB拉取记录"),
    PES_CS_CUST_RECOMMEND_CONSULT_SKU("pes_cs_cust_recommend_consult_sku","买家商品咨询/推荐表"),
    PES_SHOP_USE_ANALYSIS("pes_shop_use_analysis","店铺使用分析"),
    PES_CS_WARNING("pes_cs_warning","店铺客服客户告警表"),
    PES_SENTIMENT_ANALYSIS("pes_sentiment_analysis_info","情感分析表"),
    PES_SHOP_GOODS_FEEDBACK_RATE("pes_shop_goods_feedback_rate","商品好评率"),
    PES_SHOP_USER_ANALYSIS_ERROR("pes_shop_use_analysis_error","拉取店铺使用分析错误店铺表"),
    PES_USER_PORTRAIT_STATISTICS("pes_user_portrait_statistics","用户画像统计表"),
    PES_USER_PORTRAIT_AI_REPORT("pes_user_portrait_ai_report","用户画像AI分析报告表");





    private String name;
    private String details;

    TableEnum(String name, String desc) {
        this.name = name;
        this.details = desc;
    }

    public String getName() {
        return name;
    }

    public static TableEnum getEnumByName(String name){
    	for (TableEnum tableEnum : TableEnum.values()) {
			if(tableEnum.name.equals(name)){
				return tableEnum;
			}
		}
    	return null;
    }

    public String getDetails() {
        return details;
    }

    @Override
    public String toString() {
        return getName();
    }

}
