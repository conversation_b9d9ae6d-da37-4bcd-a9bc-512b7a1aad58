package com.pes.jd.model.Enum;

/**
 * 
* @Title: CrossDayEnum.java 跨天聊天
* @Package:com.pes.jd.model.Enum
* @Description:(作用)
* @author:Lsp
* @date:2019年3月13日
* @version:V1.8
 */
public enum CrossDayEnum {

	NOMAL(0,""),SUCCESS(1,"成功"),FAIL(2,"失败");
	private Integer type;
	private String name;
	private CrossDayEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public Integer getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	public static String getNameByType(String type) {
		for (CrossDayEnum ele : CrossDayEnum.values()) {
			if(ele.type.equals(type)){
				return ele.name;
			}
		}
		return "";
	}
}
