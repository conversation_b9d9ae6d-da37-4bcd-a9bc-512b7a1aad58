package com.pes.jd.model.Enum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 2019-06-05 14:05
 */
public enum CustomerOrderStatusEnum {

    DengDaiFenQiFuKuan("PLACE_ORDER","已下单","DengDaiFenQiFuKuan","等待分期付款",1),
    TRADE_CREATED("PLACE_ORDER","已下单","TRADE_CREATED","未付款",1),
    NO_PAY("NO_PAY","已下单","NO_PAY","未付款",1),
    NOT_PAY("NOT_PAY","已下单","NOT_PAY","未付款",1),
    WAIT_SELLER_STOCK_OUT("PAY_MENT","已付款","WAIT_SELLER_STOCK_OUT","等待出库",2),
    Deng<PERSON>ai<PERSON>aYin("PAY_MENT","已付款","DengDaiDaYin","等待打印",2),
    DengDaiChuKu("PAY_MENT","已付款","DengDaiChuKu","等待出库",2),
    DengDaiDaBao("PAY_MENT","已付款","DengDaiDaBao","等待打包",2),
    TRADE_PAYMENT("PAY_MENT","已付款","TRADE_PAYMENT","已付款",2),
    WAIT_SEND_CODE("PAY_MENT","已出库","WAIT_SEND_CODE","等待发货",2),

    WAIT_GOODS_RECEIVE_CONFIRM("OutStock","已出库","WAIT_GOODS_RECEIVE_CONFIRM","等待确认收货",3),
    WAIT_SELLER_DELIVERY("OutStock","已出库","WAIT_SELLER_DELIVERY","等待发货",3),
    DENGDAIFAHUO("OutStock","已出库","DengDaiFaHuo","等待发货",3),
    DengDaiQueRenShouHuo("OutStock","已出库","DengDaiQueRenShouHuo","等待确认收货",3),
    

    FINISHED_L("FINISHED","已完成","FINISHED_L","完成",4),
    WanCheng("FINISHED","已完成","WanCheng","完成",4),
    ServiceFinished("FINISHED","已完成","ServiceFinished","收款确认(服务完 ) ",4),
    HuoDaoFuKuanQueRen("FINISHED","已完成","HuoDaoFuKuanQueRen","货到付款确认 ",4),

    ZiTiTuZhong("Return_GOODS","退货","ZiTiTuZhong","自提途中 ",5),
    ShangMenTiHuo("Return_GOODS","退货","ShangMenTiHuo","上门提货  ",5),
    ZiTiTuiHuo("Return_GOODS","退货","ZiTiTuiHuo","自提退货 ",5),
    PeiSongTuiHuo("Return_GOODS","退货","PeiSongTuiHuo","货到付款退货 ",5),
    DengDaiTuiKuan("Return_GOODS","退货","DengDaiTuiKuan","等待退款 ",5),
    
    SuoDing("CANCEL","取消","SuoDing","取消",6),
    DengDaiKeHuHuiFu("CANCEL","取消","DengDaiKeHuHuiFu","取消",6),
    TRADE_CANCELED("CANCEL","取消","TRADE_CANCELED","取消",6),
    PAUSE("CANCEL","取消","PAUSE","暂停 ",6),
    LOCKED("CANCEL","取消","LOCKED","已锁定 ",6),
    POP_ORDER_PAUSE("CANCEL","取消","POP_ORDER_PAUSE","业务暂停， ",6),
	PARENT_TRADE_CANCELED("CANCEL","取消","PARENT_TRADE_CANCELED","业务暂停， ",6),

    UN_KNOWN("UN_KNOWN","未知","UN_KNOWN","未知， ",7);
	
	

    private String name;

    private String subName;

    private String describe;

    private String subdescribe;

    private Integer type;

    CustomerOrderStatusEnum(String name,String describe,String subName,String subdescribe,Integer type){
        this.name=name;
        this.subName=subName;
        this.describe=describe;
        this.type=type;
        this.describe=describe;
        this.subdescribe=subdescribe;
    }
    public Integer getType() {
        return type;
    }

    public String getName(){
        return name;
    }

    public String getSubName(){
        return subName;
    }

    public String getDescribe(){
        return describe;
    }

    public String getSubdescribe(){
        return subdescribe;
    }

    public static  Integer getType(String values){
        Integer type=new Integer(0);
        for(CustomerOrderStatusEnum enumItem: CustomerOrderStatusEnum.values()){
            if(enumItem.getSubName().equalsIgnoreCase(values)){
                type=enumItem.type;
                break;
            }

        }
        return type;
    }

    public static Integer getOrderState(String describe){
        Map map = new HashMap();
        map.put("DengDaiFenQiFuKuan",DengDaiFenQiFuKuan.getType());
        map.put("TRADE_CREATED",TRADE_CREATED.getType());
        map.put("NO_PAY",NO_PAY.getType());
        map.put("NOT_PAY",NOT_PAY.getType());
        map.put("WAIT_SELLER_STOCK_OUT",WAIT_SELLER_STOCK_OUT.getType());
        map.put("DengDaiDaYin",DengDaiDaYin.getType());
        map.put("DengDaiChuKu",DengDaiChuKu.getType());
        map.put("DengDaiDaBao",DengDaiDaBao.getType());
        map.put("TRADE_PAYMENT",TRADE_PAYMENT.getType());
        map.put("WAIT_SEND_CODE",WAIT_SEND_CODE.getType());
        map.put("WAIT_GOODS_RECEIVE_CONFIRM",WAIT_GOODS_RECEIVE_CONFIRM.getType());
        map.put("WAIT_SELLER_DELIVERY",WAIT_SELLER_DELIVERY.getType());
        map.put("DENGDAIFAHUO",DENGDAIFAHUO.getType());
        map.put("DengDaiQueRenShouHuo",DengDaiQueRenShouHuo.getType());
        map.put("FINISHED_L",FINISHED_L.getType());
        map.put("WanCheng",WanCheng.getType());
        map.put("ServiceFinished",ServiceFinished.getType());
        map.put("HuoDaoFuKuanQueRen",HuoDaoFuKuanQueRen.getType());
        map.put("ZiTiTuZhong",ZiTiTuZhong.getType());
        map.put("ShangMenTiHuo",ShangMenTiHuo.getType());
        map.put("ZiTiTuiHuo",ZiTiTuiHuo.getType());
        map.put("PeiSongTuiHuo",PeiSongTuiHuo.getType());
        map.put("DengDaiTuiKuan",DengDaiTuiKuan.getType());
        map.put("SuoDing",SuoDing.getType());
        map.put("DengDaiKeHuHuiFu",DengDaiKeHuHuiFu.getType());
        map.put("TRADE_CANCELED",TRADE_CANCELED.getType());
        map.put("PAUSE",PAUSE.getType());
        map.put("LOCKED",LOCKED.getType());
        map.put("POP_ORDER_PAUSE",POP_ORDER_PAUSE.getType());
        map.put("PARENT_TRADE_CANCELED",PARENT_TRADE_CANCELED.getType());
        map.put("UN_KNOWN",UN_KNOWN.getType());
        if(null != describe || !"".equals(describe)){
            return (Integer) map.get(describe);
        }
        return 0;
    }

    public static String getSubNameByDescribe(String describe){
        Map map = new HashMap();
        map.put("DengDaiFenQiFuKuan",DengDaiFenQiFuKuan.getDescribe());
        map.put("TRADE_CREATED",TRADE_CREATED.getDescribe());
        map.put("NO_PAY",NO_PAY.getDescribe());
        map.put("NOT_PAY",NOT_PAY.getDescribe());
        map.put("WAIT_SELLER_STOCK_OUT",WAIT_SELLER_STOCK_OUT.getDescribe());
        map.put("DengDaiDaYin",DengDaiDaYin.getDescribe());
        map.put("DengDaiChuKu",DengDaiChuKu.getDescribe());
        map.put("DengDaiDaBao",DengDaiDaBao.getDescribe());
        map.put("TRADE_PAYMENT",TRADE_PAYMENT.getDescribe());
        map.put("WAIT_SEND_CODE",WAIT_SEND_CODE.getDescribe());
        map.put("WAIT_GOODS_RECEIVE_CONFIRM",WAIT_GOODS_RECEIVE_CONFIRM.getDescribe());
        map.put("WAIT_SELLER_DELIVERY",WAIT_SELLER_DELIVERY.getDescribe());
        map.put("DENGDAIFAHUO",DENGDAIFAHUO.getDescribe());
        map.put("DengDaiQueRenShouHuo",DengDaiQueRenShouHuo.getDescribe());
        map.put("FINISHED_L",FINISHED_L.getDescribe());
        map.put("WanCheng",WanCheng.getDescribe());
        map.put("ServiceFinished",ServiceFinished.getDescribe());
        map.put("HuoDaoFuKuanQueRen",HuoDaoFuKuanQueRen.getDescribe());
        map.put("ZiTiTuZhong",ZiTiTuZhong.getDescribe());
        map.put("ShangMenTiHuo",ShangMenTiHuo.getDescribe());
        map.put("ZiTiTuiHuo",ZiTiTuiHuo.getDescribe());
        map.put("PeiSongTuiHuo",PeiSongTuiHuo.getDescribe());
        map.put("DengDaiTuiKuan",DengDaiTuiKuan.getDescribe());
        map.put("SuoDing",SuoDing.getDescribe());
        map.put("DengDaiKeHuHuiFu",DengDaiKeHuHuiFu.getDescribe());
        map.put("TRADE_CANCELED",TRADE_CANCELED.getDescribe());
        map.put("PAUSE",PAUSE.getDescribe());
        map.put("LOCKED",LOCKED.getDescribe());
        map.put("POP_ORDER_PAUSE",POP_ORDER_PAUSE.getDescribe());
        map.put("PARENT_TRADE_CANCELED",PARENT_TRADE_CANCELED.getDescribe());
        map.put("UN_KNOWN",UN_KNOWN.getDescribe());
        if(null != describe || !"".equals(describe)){
            return (String) map.get(describe);
        }
        return describe;
    }

}
