package com.pes.jd.model.Enum;


/**
 * 京东用户标签
 */
public enum Profession {
    FINANCE("a", "金融从业者"),
    MEDICAL("b", "医务人员"),
    EMPLOYEE("d", "公司职员"),
    WORKER("e", "工人"),
    TEACHER("f", "教职工"),
    FARMER("g", "农民"),
    STUDENT("h", "学生"),
    INDIVIDUAL("i", "个体或服务业"),
    URBAN_OTHER("m", "城市其他职业"),
    RURAL_OTHER("n", "农村其他职业");

    private final String code;
    private final String description;

    Profession(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() { return code; }
    public String getDescription() { return description; }

    public static Profession fromCode(String code) {
        for (Profession profession : values()) {
            if (profession.code.equals(code)) return profession;
        }
        throw new IllegalArgumentException("Unknown profession code: " + code);
    }
}
