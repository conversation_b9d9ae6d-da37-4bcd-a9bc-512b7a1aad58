package com.pes.jd.model.Enum;

public enum RefundTypeEnum {

    PRESALE(1,"售前退款"),AFTERSALE(2,"售后退款");
	private Integer type;
	private String name;
	private RefundTypeEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public Integer getType() {
		return type;
	}
	
	public String getName() {
		return name;
	}

	public static String getNameByType(String type) {
		for (RefundTypeEnum ele : RefundTypeEnum.values()) {
			if(ele.type.equals(type)){
				return ele.name;
			}
		}
		return "";
	}
}
