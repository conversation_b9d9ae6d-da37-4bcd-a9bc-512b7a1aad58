package com.pes.jd.model.Enum;

import com.pes.jd.constants.APICodeConstants;

/**
 * 前两个字符是 模块的拼音首字符 例如SY - 首页，
 * 接着后面两个字符是子模块 00,01
 * 最后连个字符是具体的错误码 递增
 * ClassName: ApiResponseCodeEnum <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason: TODO ADD REASON(可选). <br/>  
 * date: 2018年10月24日 下午3:07:02 <br/>  
 *  
 * <AUTHOR>  
 * @version   
 * @since JDK 1.8
 */
public enum ApiCodeEnum {

	CODE_SUCCESS_1001(APICodeConstants.CODE_SUCCESS_1001, "查询成功"),
	CODE_SUCCESS_1002(APICodeConstants.CODE_SUCCESS_1002, "操作成功"),
	CODE_SUCCESS_1003(APICodeConstants.CODE_SUCCESS_1003, "%s"),
	CODE_ERROR_COMMON_DEFAULT(APICodeConstants.CODE_ERROR_COMMON_DEFAULT, "服务降级了..."),
	CODE_ERROR_COMMON_DEMOTE(APICodeConstants.CODE_ERROR_COMMON_DEMOTE, "common rest 服务降级...."),

	CODE_ERROR_COMMON_PARAMS(APICodeConstants.CODE_ERROR_COMMON_PARAMS, "参数异常..."),

	//	------------------------------系统设置-------------------------------------//
	// 咚咚设置 - 模块
	CODE_ERROR_XS_01_01(APICodeConstants.CODE_ERROR_XS_01_01, "查询异常....."),
	CODE_ERROR_XS_01_02(APICodeConstants.CODE_ERROR_XS_01_02, "参数为空"),
	CODE_ERROR_XS_01_03(APICodeConstants.CODE_ERROR_XS_01_03, "您设置的售前客服数超过了订购版本的限制！最多可以设置%d个售前咚咚号。"),
	CODE_ERROR_XS_01_04(APICodeConstants.CODE_ERROR_XS_01_04, "试用用户最多只能设置20个售前咚咚号。"),
	CODE_ERROR_XS_01_05(APICodeConstants.CODE_ERROR_XS_01_05, "设置咚咚失败。"),
	CODE_ERROR_XS_01_06(APICodeConstants.CODE_ERROR_XS_01_06, "移动咚咚失败。"),
	CODE_ERROR_XS_01_07(APICodeConstants.CODE_ERROR_XS_01_07, "咚咚只能在单店铺中移动。"),
	CODE_ERROR_XS_01_08(APICodeConstants.CODE_ERROR_XS_01_08, "解析json格式失败!。"),
	CODE_ERROR_XS_01_09(APICodeConstants.CODE_ERROR_XS_01_09, "修改咚咚昵称失败。"),
	CODE_ERROR_XS_01_10(APICodeConstants.CODE_ERROR_XS_01_10, "删除咚咚失败。"),
	CODE_ERROR_XS_01_12(APICodeConstants.CODE_ERROR_XS_01_12, "子账号刷新失败，请稍后再试!"),


	// 咚咚组设置 - 模块请填写分组的名称!
	CODE_ERROR_XS_02_01(APICodeConstants.CODE_ERROR_XS_02_01, "查询异常....."),
	CODE_ERROR_XS_02_02(APICodeConstants.CODE_ERROR_XS_02_02, "请填写分组的名称!"),
	CODE_ERROR_XS_02_03(APICodeConstants.CODE_ERROR_XS_02_03, "咚咚组名字数超过20!"),
	CODE_ERROR_XS_02_04(APICodeConstants.CODE_ERROR_XS_02_04, "设置咚咚组失败!"),
	CODE_ERROR_XS_02_05(APICodeConstants.CODE_ERROR_XS_02_05, "删除咚咚组失败!"),
	CODE_ERROR_XS_02_06(APICodeConstants.CODE_ERROR_XS_02_06, "该分组名称已经存在!"),
	//绩效设置 - 模块
	CODE_ERROR_XS_03_01(APICodeConstants.CODE_ERROR_XS_03_01, "查询异常....."),
	CODE_ERROR_XS_03_02(APICodeConstants.CODE_ERROR_XS_03_02, "查看详情失败....."),
	CODE_ERROR_XS_03_03(APICodeConstants.CODE_ERROR_XS_03_03, "获取客服绩效失败....."),
	CODE_ERROR_XS_03_04(APICodeConstants.CODE_ERROR_XS_03_04, "获取店铺绩效失败....."),
	CODE_ERROR_XS_03_05(APICodeConstants.CODE_ERROR_XS_03_05, "获取店铺商品类目失败....."),
	CODE_ERROR_XS_03_06(APICodeConstants.CODE_ERROR_XS_03_06, "获取店铺商品失败....."),


	//权限设置 - 模块

	CODE_ERROR_XS_04_01(APICodeConstants.CODE_ERROR_XS_04_01, "查询咚咚权限缺少参数"),
	CODE_ERROR_XS_04_02(APICodeConstants.CODE_ERROR_XS_04_02, "查询店铺系统权限设置失败"),
	CODE_ERROR_XS_04_03(APICodeConstants.CODE_ERROR_XS_04_03, "一键解除所有授权异常"),
	CODE_ERROR_XS_04_04(APICodeConstants.CODE_ERROR_XS_04_04, "一键解除所有授权失败"),
	CODE_ERROR_XS_04_05(APICodeConstants.CODE_ERROR_XS_04_05, "修改咚咚权限失败"),
	CODE_ERROR_XS_04_06(APICodeConstants.CODE_ERROR_XS_04_06, "修改咚咚权限异常"),
	CODE_ERROR_XS_04_07(APICodeConstants.CODE_ERROR_XS_04_07, "管理员设置失败"),
	CODE_ERROR_XS_04_08(APICodeConstants.CODE_ERROR_XS_04_08, "管理员设置异常"),
	CODE_ERROR_XS_04_09(APICodeConstants.CODE_ERROR_XS_04_09, "查询店铺子账号异常"),
	CODE_ERROR_XS_04_10(APICodeConstants.CODE_ERROR_XS_04_10, "查询异常....."),
	CODE_ERROR_XS_04_11(APICodeConstants.CODE_ERROR_XS_04_11, "查询异常....."),
	CODE_ERROR_XS_04_12(APICodeConstants.CODE_ERROR_XS_04_12, "查询异常....."),
	CODE_ERROR_XS_04_13(APICodeConstants.CODE_ERROR_XS_04_13, "查询异常....."),
	CODE_ERROR_XS_04_14(APICodeConstants.CODE_ERROR_XS_04_14, "查询异常....."),
	CODE_ERROR_XS_04_15(APICodeConstants.CODE_ERROR_XS_04_15, "查询异常....."),
	CODE_ERROR_XS_04_16(APICodeConstants.CODE_ERROR_XS_04_16, "查询异常....."),

	CODE_ERROR_XS_04_51(APICodeConstants.CODE_ERROR_XS_04_51, "添加商品过滤失败"),
	CODE_ERROR_XS_04_52(APICodeConstants.CODE_ERROR_XS_04_52, "商品信息已存在"),
	CODE_ERROR_XS_04_53(APICodeConstants.CODE_ERROR_XS_04_53, "删除商品过滤失败"),
	CODE_ERROR_XS_04_54(APICodeConstants.CODE_ERROR_XS_04_54, "绩效基本设置失败"),
	CODE_ERROR_XS_04_55(APICodeConstants.CODE_ERROR_XS_04_55, "绩效判定设置失败"),
	CODE_ERROR_XS_04_56(APICodeConstants.CODE_ERROR_XS_04_56, "接待设置失败"),
	CODE_ERROR_XS_04_57(APICodeConstants.CODE_ERROR_XS_04_57, "买家过滤添加失败"),
	CODE_ERROR_XS_04_58(APICodeConstants.CODE_ERROR_XS_04_58, "买家过滤删除失败"),
	CODE_ERROR_XS_04_59(APICodeConstants.CODE_ERROR_XS_04_59, "查询异常....."),
	CODE_ERROR_XS_04_60(APICodeConstants.CODE_ERROR_XS_04_60, "查询异常....."),

	//店铺设置 - 模块
	CODE_ERROR_XS_05_01(APICodeConstants.CODE_ERROR_XS_05_01, "未查询到店铺信息,请点击修改,完善店铺信息....."),
	CODE_ERROR_XS_05_02(APICodeConstants.CODE_ERROR_XS_05_02, "查询店铺信息异常....."),
	CODE_ERROR_XS_05_03(APICodeConstants.CODE_ERROR_XS_05_03, "修改店铺信息失败....."),
	CODE_ERROR_XS_05_04(APICodeConstants.CODE_ERROR_XS_05_04, "修改店铺信息异常....."),


	//-------------------  数据分析    ---------------------//
	//接待分析
	CODE_ERROR_SF_01_01(APICodeConstants.CODE_ERROR_SF_01_01, "接待过滤查询失败！"),
	CODE_ERROR_SF_01_02(APICodeConstants.CODE_ERROR_SF_01_02, "日期格式不正确"),
	CODE_ERROR_SF_01_03(APICodeConstants.CODE_ERROR_SF_01_03, "查询聊天对象失败"),
	CODE_ERROR_SF_01_04(APICodeConstants.CODE_ERROR_SF_01_04, "查询聊天内容失败"),
	CODE_ERROR_SF_01_05(APICodeConstants.CODE_ERROR_SF_01_05, "参数解析异常"),
	CODE_ERROR_SF_01_06(APICodeConstants.CODE_ERROR_SF_01_06, "顾客接待分析查询失败！"),

	CODE_ERROR_SF_02_01(APICodeConstants.CODE_ERROR_SF_02_01, "查询异常....."),
	CODE_ERROR_SF_02_02(APICodeConstants.CODE_ERROR_SF_02_02, "成交分析查询异常....."),
	CODE_ERROR_SF_02_03(APICodeConstants.CODE_ERROR_SF_02_03, "成交订单详情查询异常....."),
	CODE_ERROR_SF_02_15(APICodeConstants.CODE_ERROR_SF_02_15, "店铺销售分析查询异常....."),
	CODE_ERROR_SF_02_16(APICodeConstants.CODE_ERROR_SF_02_16, "客服销售分析查询异常....."),
	CODE_ERROR_SF_02_17(APICodeConstants.CODE_ERROR_SF_02_17, "静默销售分析查询异常....."),
	CODE_ERROR_SF_02_18(APICodeConstants.CODE_ERROR_SF_02_18, "商品咨询分析查询异常....."),


	//流失分析
	CODE_ERROR_SF_03_20(APICodeConstants.CODE_ERROR_SF_03_20, "参数传递异常"),
	CODE_ERROR_SF_03_21(APICodeConstants.CODE_ERROR_SF_03_21, "时间选择超过范围，请缩小时间间隔"),
	CODE_ERROR_SF_03_22(APICodeConstants.CODE_ERROR_SF_03_22, "静默流失记录查询失败！"),
	CODE_ERROR_SF_03_23(APICodeConstants.CODE_ERROR_SF_03_23, "saveOrUpdateLostRecordNote执行异常"),
	CODE_ERROR_SF_03_24(APICodeConstants.CODE_ERROR_SF_03_24, "保存备注失败"),
	CODE_ERROR_SF_03_25(APICodeConstants.CODE_ERROR_SF_03_25, "询单流失查询失败"),
	CODE_ERROR_SF_03_26(APICodeConstants.CODE_ERROR_SF_03_26, "询单未下单流失查询失败"),
	
	
	//登录分析
	CODE_ERROR_SF_04_01(APICodeConstants.CODE_ERROR_SF_04_01, "登录分析总览查询参数传递异常"),
	CODE_ERROR_SF_04_02(APICodeConstants.CODE_ERROR_SF_04_02, "登录分析总览查询失败....."),
	CODE_ERROR_SF_04_03(APICodeConstants.CODE_ERROR_SF_04_03, "登录分析总览查询异常....."),
	CODE_ERROR_SF_04_04(APICodeConstants.CODE_ERROR_SF_04_04, "登录分析详情查询参数传递异常"),
	CODE_ERROR_SF_04_05(APICodeConstants.CODE_ERROR_SF_04_05, "登录分析详情查询失败....."),
	CODE_ERROR_SF_04_06(APICodeConstants.CODE_ERROR_SF_04_06, "登录分析详情查询异常....."),
	CODE_ERROR_SF_04_07(APICodeConstants.CODE_ERROR_SF_04_07, "该咚咚组为空，请重新选择查询"),
	CODE_ERROR_SF_04_08(APICodeConstants.CODE_ERROR_SF_04_08, "登录分析总览时间解析异常"),
	CODE_ERROR_SF_04_09(APICodeConstants.CODE_ERROR_SF_04_09, "登录分析详情时间解析异常"),
	CODE_ERROR_SF_04_10(APICodeConstants.CODE_ERROR_SF_04_10, "登录分析上下线详情查询参数传递异常"),
	CODE_ERROR_SF_04_11(APICodeConstants.CODE_ERROR_SF_04_11, "登录分析上下线详情时间解析异常"),
	CODE_ERROR_SF_04_12(APICodeConstants.CODE_ERROR_SF_04_12, "登录分析上下线详情查询失败....."),
	CODE_ERROR_SF_04_13(APICodeConstants.CODE_ERROR_SF_04_13, "登录分析上下线详情查询异常....."),
	
	CODE_ERROR_SF_05_01(APICodeConstants.CODE_ERROR_SF_05_01, "查询异常....."),

	CODE_ERROR_SF_06_01(APICodeConstants.CODE_ERROR_SF_06_01, "查询异常....."),

	//商品推荐分析
	CODE_ERROR_SF_07_01(APICodeConstants.CODE_ERROR_SF_07_01, "商品推荐汇总查询失败"),
	CODE_ERROR_SF_07_02(APICodeConstants.CODE_ERROR_SF_07_02, "商品推荐分析查询失败"),
	CODE_ERROR_SF_07_03(APICodeConstants.CODE_ERROR_SF_07_03, "商品推荐明细查询失败"),
	CODE_ERROR_SF_07_04(APICodeConstants.CODE_ERROR_SF_07_04, "成交订单详情查询失败"),
	CODE_ERROR_SF_07_05(APICodeConstants.CODE_ERROR_SF_07_05, "商品推荐汇总导出失败"),
	//退款分析
	CODE_ERROR_SF_09_01(APICodeConstants.CODE_ERROR_SF_09_01, "客服退款分析查询失败"),
	CODE_ERROR_SF_09_02(APICodeConstants.CODE_ERROR_SF_09_02, "静默退款分析查询失败"),


	//成交分析
	CODE_ERROR_SF_08_01(APICodeConstants.CODE_ERROR_SF_02_18, "客服商品销售汇总异常....."),
	CODE_ERROR_SF_08_02(APICodeConstants.CODE_ERROR_SF_02_19, "静默商品销售明细异常....."),
	CODE_ERROR_SF_08_03(APICodeConstants.CODE_ERROR_SF_02_20, "客服商品销售明细异常....."),
	CODE_ERROR_SF_08_04(APICodeConstants.CODE_ERROR_SF_02_21, "静默商品销售汇总异常....."),
	CODE_ERROR_SF_08_05(APICodeConstants.CODE_ERROR_SF_02_22, "客服销售分析异常....."),

	//服务分析
	CODE_ERROR_FW_01_01(APICodeConstants.CODE_ERROR_FW_01_02, "满意率查询失败"),

	CODE_ERROR_FW_01_02(APICodeConstants.CODE_ERROR_FW_01_03, "接待未邀评查询失败"),

	CODE_ERROR_FW_01_03(APICodeConstants.CODE_ERROR_FW_01_04, "协助服务分析异常"),

	CODE_ERROR_FW_01_04(APICodeConstants.CODE_ERROR_FW_01_05, "中差评分析异常"),
	CODE_ERROR_FW_01_05(APICodeConstants.CODE_ERROR_FW_01_06, "追责异常"),

	//-------------------  绩效中心    ---------------------//
    //	实时绩效
	CODE_ERROR_JZ_01_01(APICodeConstants.CODE_ERROR_JZ_01_01, "实时绩效拉取失败"),
	CODE_ERROR_JZ_01_02(APICodeConstants.CODE_ERROR_JZ_01_02, "获取我的绩效失败"),
    //店铺
	CODE_ERROR_JZ_02_01(APICodeConstants.CODE_ERROR_JZ_02_01, "查询异常....."),

	CODE_ERROR_JZ_02_02(APICodeConstants.CODE_ERROR_JZ_02_02, "店铺数据总览查询失败"),

    //客服咚咚绩效
	CODE_ERROR_JZ_03_01(APICodeConstants.CODE_ERROR_JZ_03_01, "查询异常....."),
	//首页index

	CODE_ERROR_IX_01_01(APICodeConstants.CODE_ERROR_IX_01_01, "首页快捷菜单列表查询失败"),
	CODE_ERROR_IX_01_02(APICodeConstants.CODE_ERROR_IX_01_02, "登录用户首页快捷菜单查询失败"),
	CODE_ERROR_IX_01_03(APICodeConstants.CODE_ERROR_IX_01_03, "保存首页快捷菜单失败"),
	//-------------------  首页&初始化   ---------------------//
	CODE_ERROR_SY_01_01(APICodeConstants.CODE_ERROR_SY_01_01, "查询异常....."),

	CODE_ERROR_SY_02_01(APICodeConstants.CODE_ERROR_SY_02_01, "查询异常....."),

	//-------------------  发送mq消息   ---------------------//
	CODE_ERROR_MQ_01_01(APICodeConstants.CODE_ERROR_MQ_01_01, " mq信息发送失败 "),
	
	//-------------------  后台登录   ---------------------//
	CODE_ERROR_HD_01_01(APICodeConstants.CODE_ERROR_HD_01_01, "密码不能为空"),
	CODE_ERROR_HD_01_02(APICodeConstants.CODE_ERROR_HD_01_02, "密码验证失败"),
	CODE_ERROR_HD_01_03(APICodeConstants.CODE_ERROR_HD_01_03, "test_ok"),//测试账号登录
	CODE_ERROR_HD_01_04(APICodeConstants.CODE_ERROR_HD_01_04, "模糊查询店铺信息异常"),
	CODE_ERROR_HD_01_05(APICodeConstants.CODE_ERROR_HD_01_05, "管理员登录当前店铺失败"),
	CODE_ERROR_HD_01_06(APICodeConstants.CODE_ERROR_HD_01_06, "加载当前店铺资源失败"),
	CODE_ERROR_HD_01_07(APICodeConstants.CODE_ERROR_HD_01_07, "请输入查询条件"),
	CODE_ERROR_HD_01_13(APICodeConstants.CODE_ERROR_HD_01_13, "......"),

    //----------------------插件管理----------------------------
    CODE_ERROR_CJ_01_01(APICodeConstants.CODE_ERROR_CJ_01_01,"查询异常......."),

	// job日志
	CODE_ERROR_JOB_01_01(APICodeConstants.CODE_ERROR_JOB_01_01, "job日志插入失败"),

	// 手动拉取数据-重算数据-初始话用户数据
	CODE_ERROR_JOB_TASK_01_01(APICodeConstants.CODE_ERROR_JOB_TASK_01_01, "(手动拉取数据-重算数据-初始话用户数据) -> 失败"),

	//商品
	CODE_ERROR_SP_01_01(APICodeConstants.CODE_ERROR_SP_01_01, "获取店铺SKU商品失败....."),
	CODE_ERROR_SP_01_02(APICodeConstants.CODE_ERROR_SP_01_01, "删除商品知识库失败"),
	CODE_ERROR_SP_01_03(APICodeConstants.CODE_ERROR_SP_01_01, "更新商品知识库失败"),
	CODE_ERROR_SP_01_04(APICodeConstants.CODE_ERROR_SP_01_01, "插入商品知识库失败"),

    CODE_ERROR_SP_01_05(APICodeConstants.CODE_ERROR_SP_01_01, "设置主推失败"),
    CODE_ERROR_SP_01_06(APICodeConstants.CODE_ERROR_SP_01_01, "取消主推失败"),
    CODE_ERROR_SP_01_07(APICodeConstants.CODE_ERROR_SP_01_01, "设置关联商品失败"),

	// 自定义报表拉取失败
	CODE_ERROR_CUSTOM_REPORT(APICodeConstants.CODE_ERROR_CUSTOM_REPORT,"获取自定义报表数据失败"),

	//错误店铺查询失败
	CODE_ERROR_QUERY_ERRORSHOP(APICodeConstants.CODE_ERROR_QUERY_ERRORSHOP,"错误店铺查询失败"),

	CODE_ERROR_CLEAN_JOB_PULL_RECORD(APICodeConstants.CODE_ERROR_CLEAN_JOB_PULL_RECORD, "清除任务消息失败！"),
	CODE_ERROR_CLEAN_RECORD_DATE_PARSE(APICodeConstants.CODE_ERROR_CLEAN_RECORD_DATE_PARSE, "时间解析错误！"),
	CODE_ERROR_SEARCH_JOB_PULL_RECORD(APICodeConstants.CODE_ERROR_SEARCH_JOB_PULL_RECORD, "查询任务消息失败！"),
	CODE_ERROR_SEARCH_RECORD_DATE_PARSE(APICodeConstants.CODE_ERROR_SEARCH_RECORD_DATE_PARSE, "时间解析错误！"),
	CODE_ERROR_YY_01_01(APICodeConstants.CODE_ERROR_YY_01_01, "预约活动店铺绩效查询失败！"),
	CODE_ERROR_YY_01_02(APICodeConstants.CODE_ERROR_YY_01_02, "预约活动汇总查询失败！")
	;

	private String code;//错误码
	private String msg;//错误信息

	private ApiCodeEnum(String code, String msg) {
		this.code = code;
		this.msg = msg;
	}

	public String getCode() {
		return code;
	}

	public String getMsg() {
		return msg;
	}

}
