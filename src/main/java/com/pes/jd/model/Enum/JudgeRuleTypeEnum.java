package com.pes.jd.model.Enum;

import com.pes.jd.Constants.PesConstants;

public enum JudgeRuleTypeEnum {
	ORDER_BIND_TYPE_ORDER(PesConstants.ORDER_BIND_TYPE_ORDER,"落实下单"),
	ORDER_BIND_TYPE_PAY(PesConstants.ORDER_BIND_TYPE_PAY, "落实付款"),
	ORDER_BIND_TYPE_SILENTALL(PesConstants.ORDER_BIND_TYPE_SILENTALL, "全静默跟进"),
    ORDER_BIND_TYPE_BALANCE_PAY(PesConstants.ORDER_BIND_TYPE_BALANCE_PAY, "落实付尾款"),
    ORDER_BIND_TYPE_DEFAULT(PesConstants.ORDER_BIND_TYPE_DEFAULT, "无法确定类型的绑定(处理询单人数=落实下单+询单流失：场景①跨天聊天的不算询单也不该算绑定)");

    // 成员变量
    private int type;
    private String desc;

    // 构造方法  
    private JudgeRuleTypeEnum(int type,String desc) {
		this.type = type;
		this.desc = desc;
	}
    
    public static int getType(int type) {  
        for (JudgeRuleTypeEnum s : JudgeRuleTypeEnum.values()) {  
            if (s.getType() == type) {  
                return s.getType();
            } 
        }
        return 0; 
    }
    


	public static String getDesc(int type) {  
        for (JudgeRuleTypeEnum s : JudgeRuleTypeEnum.values()) {  
            if (s.getType() == type) {  
                return s.getDesc();
            } 
        }
        return null; 
    }

	public int getType() {
		return type;
	}

	public String getDesc() {
		return desc;
	}
    


	
	
}
