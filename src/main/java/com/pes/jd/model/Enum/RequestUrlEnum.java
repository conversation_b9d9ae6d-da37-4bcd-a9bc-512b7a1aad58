package com.pes.jd.model.Enum;

/**
 * @Author: aiJun
 * @Date: 2019-07-17 20:18
 * @Version 1.0
 */
public enum RequestUrlEnum {
    //describe
    DATA_ANALYSIS_SHOPSALEANALYSIS("/data/analysis/shopSaleAnalysis", "数据分析-->成交分析->销售分析->店铺销售分析"),
    DATA_ANALYSIS_CSSALEANALYSIS("/data/analysis/csSaleAnalysis", "数据分析-->成交分析->销售分析->客服销售分析"),
    DATA_ANALYSIS_SILENCESALEANALYSIS("/data/analysis/silenceSaleAnalysis", "数据分析-->成交分析->销售分析->静默销售分析"),
    DATA_ANALYSIS_CSGOODSSALEINDEXDETAIL("/data-analysis/goodsSale/selectCsGoodsSaleIndexDetail", "数据分析-->成交分析->商品销售分析->客服商品销售明细"),
    DATA_ANALYSIS_SLIENTGOODSSALEINDEXDETAIL("/data-analysis/goodsSale/selectSlientGoodsSaleIndexDetail", "数据分析-->成交分析->商品销售分析->静默商品销售明细"),

    DATA_ANALYSIS_CSREFUNDANALYSISLST("/data-analysis/deal/selectCsRefundAnalysisLst", "数据分析-->成交分析->退款分析->客服退款分析"),
    DATA_ANALYSIS_SILENTREFUNDANALYSISLST("/data-analysis/deal/selectSilentRefundAnalysisLst", "数据分析-->成交分析->退款分析->静默退款分析"),
    DATA_ANALYSIS_ASSISTSERVICE("/data-analysis/assistService/selectCsOrderIndex", "数据分析-->成交分析->协助服务分析"),
    DATA_ANALYSIS_ORDERPRESALELST("/data-analysis/deal/selectOrderPresaleLst", "数据分析-->成交分析->预售订单分析"),
    DATA_ANALYSIS_ENQUIRYORDERLOSTRECORD("/data/analysis/searchEnquiryOrderLostRecord", "数据分析-->流失分析->询单下单未付款分析"),
    
    DATA_ANALYSIS_SILENCEORDERLOSTRECORD("/data/analysis/silenceOrderLostRecord", "数据分析-->流失分析->静默下单未付款分析"),
    DATA_ANALYSIS_SELECTDEALORDERDETAILLST("/data-analysis/receive/selectDealOrderDetailLst", "数据分析-->接待分析->成交订单详情"),
    CUSTOMER_SELECTSHOPORDERDETAILINFO("/customer/selectShopOrderDetailInfo", "插件查询订单"),
    CUSTOMER_UPDATEORDERREMARKINFO("/customer/updateOrderRemarkInfo", "插件修改订单信息"),
    CUSTOMER_UPDATEADDRESS("/customer/updateAddress", "插件更新订单地址"),
    TASK_EXPORT_ADDEXPORTEXCELTASK("/task/export/addExportExcelTask", "订单日志的导出"),
    DATA_ANALYSIS_ORDERPEDIANLST("/data-analysis/selectOrderPredainLst", "数据分析-->成交分析->预约订单分析");

    private String name;
    private String describe;

    RequestUrlEnum(String name, String describe) {
        this.name = name;
        this.describe = describe;
    }

    public String getName() {
        return name;
    }

    public static com.pes.jd.model.Enum.RequestUrlEnum getEnumByName(String name) {
        for (com.pes.jd.model.Enum.RequestUrlEnum tableEnum : com.pes.jd.model.Enum.RequestUrlEnum.values()) {
            if (tableEnum.name.equals(name)) {
                return tableEnum;
            }
        }
        return null;
    }

    public String getDescribe() {
        return describe;
    }

    @Override
    public String toString() {
        return getName();
    }

}
