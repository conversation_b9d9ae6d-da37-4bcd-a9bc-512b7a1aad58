package com.pes.jd.model.Enum;

public enum RequestUrlEnum {
    //describe
    SHOP_SYSETTING_ADDGOODSFILTER("/shop/sysetting/addGoodsFilter", "商品过滤添加"),
    SHOP_SYSETTING_ADDGOODSFILTEROFSPU("/shop/sysetting/addGoodsFilterOfSpu", "商品过滤添加"),
    SHOP_SYSETTING_DELETEGOODSFILTER("/shop/sysetting/deleteGoodsFilter", "删除商品过滤"),
    SHOP_SYSETTING_BASICPERFORMANCESETTINGS("/shop/sysetting/basicPerformanceSettings", "绩效设置基本设置"),
    SHOP_SYSETTING_PERFORMANCEDECISIONSETTINGS("/shop/sysetting/performanceDecisionSettings", "绩效设置绩效判定设置"),
    SHOP_SYSETTING_RECEPTIONSETTINGS("/shop/sysetting/receptionSettings", "绩效设置接待设置"),
    SHOP_SYSETTING_RTPERFORMANCESETTINGS("/shop/sysetting/rtPerformanceSettings", "实时绩效设置接待设置"),
    SHOP_SYSETTING_ADDFLITERBUYNICK("/shop/sysetting/addFliterBuynick", "买家过滤添加"),
    SHOP_SYSETTING_DELETEFLITERBUYNICK("/shop/sysetting/deleteFliterBuynick", "买家删除过滤"),
    SHOP_SYSETTING_QUERYSYSTEMSETFORQN("/shop/sysetting/querySystemSetForQN", "获取绩效设置所有基础信息"),
    SHOP_SYSETTING_QUERYISMODIFYENQUIRYANDOUTSTOCKVALIDTIME("/shop/sysetting/queryIsModifyEnquiryAndOutStockValidTime", "获取询单和出库有效时长修改限制信息"),
    SHOP_GOODINFO_SELECTCATEGORYLST("/shop/goodinfo/selectCategoryLst", "店铺商品下拉列表"),
    SHOP_GOODINFO_SELECTCATEGORYLSTV2("/shop/goodinfo/selectCategoryLstV2", "店铺商品下拉列表V2"),
    GOODINFO_SELECTGOODSINFOLST("/goodinfo/selectGoodsinfoLst", "获取商品详情"),
    DATA_ANALYSIS_SHOPSALEANALYSIS("/data/analysis/shopSaleAnalysis", "店铺销售分析"),
    DATA_ANALYSIS_EXPORTSHOPSALEANALYSIS("/data/analysis/exportShopSaleAnalysis", "店铺销售分析导出"),
    DATA_ANALYSIS_CSSALEANALYSIS("/data/analysis/csSaleAnalysis", "客服销售分析"),
    DATA_ANALYSIS_EXPORTCSSALEANALYSIS("/data/analysis/exportCsSaleAnalysis", "客服销售分析导出"),
    DATA_ANALYSIS_SILENCESALEANALYSIS("/data/analysis/silenceSaleAnalysis", "静默销售分析"),
    DATA_ANALYSIS_EXPORTSILENCESALEANALYSIS("/data/analysis/exportSilenceSaleAnalysis", "静默销售分析"),
    DATA_ANALYSIS_SELECTORDERPRESALELST("/data/analysis/selectOrderPresaleLst", "预售订单分析"),
    DATA_ANALYSIS_SELECTREFUNDANALYSISLST("/data/analysis/selectRefundAnalysisLst", "查询退款分析"),
    DATA_ANALYSIS_SELECTREFUNDANALYSISLSTOFSPU("/data/analysis/selectRefundAnalysisLstOfSpu", "查询退款分析SPU维度"),
    DATA_ANALYSIS_EXPORTENQUIRYLOSTRECORD("/data/analysis/exportEnquiryLostRecord", "询单流失分析"),
    DATA_ANALYSIS_EXPORTENQUIRYORDERLOSTRECORD("/data/analysis/exportEnquiryOrderLostRecord", "询单下单未付款分析"),
    DATA_ANALYSIS_EXPORTSILENTORDERLOSTRECORD("/data/analysis/exportSilentOrderLostRecord", "静默下单未付款分析"),
    DATA_ANALYSIS_EXPORTCHATLOGLSTQUERY("/data/analysis/exportChatlogLstQuery", "聊天记录"),
    DATA_ANALYSIS_SELECTLEAVEMESSAGE("/service/selectLeaveMessage", "留言分析"),
    TASK_EXPORT_ADDEXPORTEXCELTASK("/task/export/addExportExcelTask", "导出任务"),
    TASK_EXPORT_DELETEEXPORTRECORDBYID("/task/export/deleteExportRecordById", "导出任务"),
    TASK_EXPORT_SELECTBYSHOPIDEXPORTLST("/task/export/selectByShopIdExportLst", "导出列表查询"),
    TASK_EXPORT_CHECKEXPORTFILEFOROSS("/task/export/checkExportFileForOss", "导出列表查询"),
    DATA_ANALYSIS_SELECTORDERPREDIAN("/data/analysis/selectOrderPredainLst", "预约订单分析");
    private String name;
    private String describe;

    RequestUrlEnum(String name, String describe) {
        this.name = name;
        this.describe = describe;
    }

    public String getName() {
        return name;
    }

    public static RequestUrlEnum getEnumByName(String name) {
        for (RequestUrlEnum tableEnum : RequestUrlEnum.values()) {
            if (tableEnum.name.equals(name)) {
                return tableEnum;
            }
        }
        return null;
    }

    public String getDescribe() {
        return describe;
    }

    @Override
    public String toString() {
        return getName();
    }

}
