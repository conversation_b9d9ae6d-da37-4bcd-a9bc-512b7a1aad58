package com.pes.jd.model.BO;

import java.io.Serializable;

public class DataPreparedStatus implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8023771830319981204L;
	private String shopId;
	private String dataType;
	private String prepareStatus;
	private long effectedRowsNum;
	private String shopTitle;
	private int apiNum;
	private int apiRetryNum;
	
	public DataPreparedStatus() {
		super();
	}
	
	public DataPreparedStatus(long effectedRowsNum, int apiNum, int apiRetryNum) {
		super();
		this.effectedRowsNum = effectedRowsNum;
		this.apiNum = apiNum;
		this.apiRetryNum = apiRetryNum;
	}

	public DataPreparedStatus(int apiNum, int apiRetryNum) {
		super();
		this.apiNum = apiNum;
		this.apiRetryNum = apiRetryNum;
	}

	public String getShopId() {
		return shopId;
	}
	public void setShopId(String shopId) {
		this.shopId = shopId;
	}
	public String getDataType() {
		return dataType;
	}
	public void setDataType(String dataType) {
		this.dataType = dataType;
	}
	public String getPrepareStatus() {
		return prepareStatus;
	}
	public void setPrepareStatus(String prepareStatus) {
		this.prepareStatus = prepareStatus;
	}
	public long getEffectedRowsNum() {
		return effectedRowsNum;
	}
	public void setEffectedRowsNum(long effectedRowsNum) {
		this.effectedRowsNum = effectedRowsNum;
	}
	public String getShopTitle() {
		return shopTitle;
	}
	public void setShopTitle(String shopTitle) {
		this.shopTitle = shopTitle;
	}
	public int getApiNum() {
		return apiNum;
	}
	public void setApiNum(int apiNum) {
		this.apiNum = apiNum;
	}
	public int getApiRetryNum() {
		return apiRetryNum;
	}
	public void setApiRetryNum(int apiRetryNum) {
		this.apiRetryNum = apiRetryNum;
	}

	@Override
	public String toString() {
		return "DataPreparedStatus [shopId=" + shopId + ", dataType=" + dataType + ", prepareStatus=" + prepareStatus + ", effectedRowsNum="
				+ effectedRowsNum + ", shopTitle=" + shopTitle + ", apiNum=" + apiNum + ", apiRetryNum=" + apiRetryNum + "]";
	}
	
	
}
