package com.pes.jd.model.BO;

import java.io.Serializable;
import java.util.Date;

/**  
 * ClassName:ForwardChatLogBO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年11月1日 下午4:52:46 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ForwardChatLogBO implements Serializable{

	private static final long serialVersionUID = 479826279636664887L;
	private long shopId;
	private String forwarder;
	private String buyerNick;
	private Date lastChatTime;
	private int direction;
	private long type;
	private String targetCsNick;
	private int targetCsType;
	
	public ForwardChatLogBO() {
		super();
	}
	
	public ForwardChatLogBO(long shopId, String forwarder, String buyerNick) {
		super();
		this.shopId = shopId;
		this.forwarder = forwarder;
		this.buyerNick = buyerNick;
	}

	public long getShopId() {
		return shopId;
	}
	public void setShopId(long shopId) {
		this.shopId = shopId;
	}
	public String getForwarder() {
		return forwarder;
	}
	public void setForwarder(String forwarder) {
		this.forwarder = forwarder;
	}
	
	public Date getLastChatTime() {
		return lastChatTime;
	}
	public void setLastChatTime(Date lastChatTime) {
		this.lastChatTime = lastChatTime;
	}
	public int getDirection() {
		return direction;
	}
	public void setDirection(int direction) {
		this.direction = direction;
	}
	public long getType() {
		return type;
	}
	public void setType(long type) {
		this.type = type;
	}
	
	public int getTargetCsType() {
		return targetCsType;
	}
	public void setTargetCsType(int targetCsType) {
		this.targetCsType = targetCsType;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public String getTargetCsNick() {
		return targetCsNick;
	}

	public void setTargetCsNick(String targetCsNick) {
		this.targetCsNick = targetCsNick;
	}

	


}
  
