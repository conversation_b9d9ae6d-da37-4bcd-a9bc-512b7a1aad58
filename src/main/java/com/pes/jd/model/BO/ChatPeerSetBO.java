package com.pes.jd.model.BO;

import com.pes.jd.model.AO.ApiNumAO;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DO.CsChatpeerDO;

import java.util.List;
import java.util.Set;

/**  
 * ClassName:ChatPeerBO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月31日 下午5:20:56 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ChatPeerSetBO extends ApiNumAO {

	private Set<CsChatpeerDO> chatPeerSet;

	private List<CsChatSessionDO> csChatSessionLst;
	
	public ChatPeerSetBO() {
		super();  
	}
	public Set<CsChatpeerDO> getChatPeerSet() {
		return chatPeerSet;
	}
	public void setChatPeerSet(Set<CsChatpeerDO> chatPeerSet) {
		this.chatPeerSet = chatPeerSet;
	}
	public List<CsChatSessionDO> getCsChatSessionLst() {
		return csChatSessionLst;
	}
	public void setCsChatSessionLst(List<CsChatSessionDO> csChatSessionLst) {
		this.csChatSessionLst = csChatSessionLst;
	}

}
  
