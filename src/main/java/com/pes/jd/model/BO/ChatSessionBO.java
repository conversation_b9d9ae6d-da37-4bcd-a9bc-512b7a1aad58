package com.pes.jd.model.BO;

import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DO.CsChatSessionServiceIndexDO;
import com.pes.jd.model.DO.CsServiceSendEvalDO;
import com.pes.jd.model.DTO.ChatlogDTO;
import com.pes.jd.model.DTO.CsChatSessionDTO;
import com.pes.jd.model.DTO.CsLeaveMsgDTO;
import com.pes.jd.model.DTO.CsServiceEvaluationDetailDTO;

import java.util.*;


/**
 *  每个客服一天的bo对象
 */
public class ChatSessionBO {

    // chatSessionServiceIndex 一个店铺一天的
    private  List<CsChatSessionServiceIndexDO> shopChatSessionServiceIndexs = new ArrayList<>(40);

    // 所有的chatSession
    private List<CsChatSessionDTO> cschatSessions = new ArrayList<>();

    // 店铺留言咨询
    private  int shopLeaveMsgAdvisorySessionNum = 0;

    // 转出的chatSession
    private List<CsChatSessionDTO> forWordOut = new ArrayList<>();

    // 店铺一天的 chatSession
    private List<CsChatSessionDTO> shopChatSession = new ArrayList<>();

    // 店铺一天的 chatLog
    private List<ChatlogDTO> shopChatLog = new ArrayList<>();


    private SessionBO sessionBO;

    public SessionBO getSessionBO() {
        return sessionBO == null ? sessionBO = new SessionBO() : sessionBO;
    }

    public void setSessionBO(SessionBO sessionBO) {
        this.sessionBO = sessionBO;
    }

    /**
     *  每个客服每天
     */
    public static class SessionBO{
        private Set<String> filterNick;
        private Map<CsChatSessionDTO, List<ChatlogDTO>> chatSessions;
        private  List<CsLeaveMsgDTO> csLeaveMsgs;
        private  List<CsServiceEvaluationDetailDTO> csServiceEvaluation;
        private List<CsServiceSendEvalDO> csServiceSendEval;
        List<CsChatSessionDTO> forWordInSet;

        public Set<String> getFilterNick() {
            return filterNick;
        }

        public void setFilterNick(Set<String> filterNick) {
            this.filterNick = filterNick;
        }

        public Map<CsChatSessionDTO, List<ChatlogDTO>> getChatSessions() {
            return chatSessions;
        }

        public void setChatSessions(Map<CsChatSessionDTO, List<ChatlogDTO>> chatSessions) {
            this.chatSessions = chatSessions;
        }

        public List<CsLeaveMsgDTO> getCsLeaveMsgs() {
            return csLeaveMsgs;
        }

        public void setCsLeaveMsgs(List<CsLeaveMsgDTO> csLeaveMsgs) {
            this.csLeaveMsgs = csLeaveMsgs;
        }

        public List<CsServiceEvaluationDetailDTO> getCsServiceEvaluation() {
            return csServiceEvaluation;
        }

        public void setCsServiceEvaluation(List<CsServiceEvaluationDetailDTO> csServiceEvaluation) {
            this.csServiceEvaluation = csServiceEvaluation;
        }

        public List<CsServiceSendEvalDO> getCsServiceSendEval() {
            return csServiceSendEval;
        }

        public void setCsServiceSendEval(List<CsServiceSendEvalDO> csServiceSendEval) {
            this.csServiceSendEval = csServiceSendEval;
        }

        public List<CsChatSessionDTO> getForWordInSet() {
            return forWordInSet;
        }

        public void setForWordInSet(List<CsChatSessionDTO> forWordInSet) {
            this.forWordInSet = forWordInSet;
        }
    }

    public List<CsChatSessionServiceIndexDO> getShopChatSessionServiceIndexs() {
        return shopChatSessionServiceIndexs;
    }

    public void setShopChatSessionServiceIndexs(List<CsChatSessionServiceIndexDO> shopChatSessionServiceIndexs) {
        this.shopChatSessionServiceIndexs = shopChatSessionServiceIndexs;
    }

    public List<CsChatSessionDTO> getShopChatSession() {
        return shopChatSession;
    }

    public void setShopChatSession(List<CsChatSessionDTO> shopChatSession) {
        this.shopChatSession = shopChatSession;
    }

    public List<ChatlogDTO> getShopChatLog() {
        return shopChatLog;
    }

    public void setShopChatLog(List<ChatlogDTO> shopChatLog) {
        this.shopChatLog = shopChatLog;
    }

    public List<CsChatSessionDTO> getCschatSessions() {
        return cschatSessions;
    }

    public void setCschatSessions(List<CsChatSessionDTO> cschatSessions) {
        this.cschatSessions = cschatSessions;
    }

    public int getShopLeaveMsgAdvisorySessionNum() {
        return shopLeaveMsgAdvisorySessionNum;
    }

    public void setShopLeaveMsgAdvisorySessionNum(int shopLeaveMsgAdvisorySessionNum) {
        this.shopLeaveMsgAdvisorySessionNum = shopLeaveMsgAdvisorySessionNum;
    }

    public List<CsChatSessionDTO> getForWordOut() {
        return forWordOut;
    }

    public void setForWordOut(List<CsChatSessionDTO> forWordOut) {
        this.forWordOut = forWordOut;
    }
}
