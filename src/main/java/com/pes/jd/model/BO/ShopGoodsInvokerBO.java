package com.pes.jd.model.BO;

import com.pes.jd.data.api.ShopCategoryAndGoodAndSkuOperator;
import com.pes.jd.model.DTO.JobShopDTO;
import com.pes.jd.model.Query.JobDateQuery;
import com.pes.jd.util.SpringUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * @Author: aiJun
 * @Date: 2019-06-20 15:11
 * @Version 1.0
 */
public class ShopGoodsInvokerBO implements Callable<Map<String, Object>> {
    private JobShopDTO jobShop;
    private Date sDate;
    private Date eDate;
    private ShopCategoryAndGoodAndSkuOperator shopCategoryAndGoodAndSkuOperator;

    public ShopGoodsInvokerBO(JobShopDTO jobShop, Date sDate, Date eDate) {
        shopCategoryAndGoodAndSkuOperator = SpringUtil.getBean(ShopCategoryAndGoodAndSkuOperator.class);
        this.jobShop = jobShop;
        this.sDate = sDate;
        this.eDate = eDate;
    }

    @Override
    public Map<String, Object> call() throws Exception {
        Map<String, Object> result = new HashMap<>();
        JobDateQuery jobDate = new JobDateQuery();
        jobDate.setStartDate(sDate);
        jobDate.setEndDate(eDate);
        result.put("shopGoods", shopCategoryAndGoodAndSkuOperator.getShopGoodOfEveryday(jobShop, jobDate));
        return result;
    }
}
