package com.pes.jd.model.BO;

import com.google.common.collect.Sets;
import lombok.Data;

import java.util.Date;
import java.util.Objects;
import java.util.Set;

@Data
public class CsPerformancePresaleBO {


    private String activityId;
    private Long skuId;
    private String csNick;
    private Date date;

    private String skuName;

    private Set<String> consultBuyerSet; //咨询买家集合
    private Set<String> enquiryBuyerSet; //询单买家集合

    private Set<String> enquiryOrderedBuyerSet; //咨询维度 - 下单买家集合
    private Integer enquiryOrderedSkuNum;
    private Set<String> enquiryOrderedBargainBuyerSet; //咨询维度 - 付定金买家集合
    private Integer enquiryOrderedBargainSkuNum;
    private Double enquiryOrderedBargainAmount;
    private Set<String> enquiryOrderedBalanceBuyerSet; //咨询维度 - 付尾款买家集合
    private Integer enquiryOrderedBalanceSkuNum;
    private Double enquiryOrderedBalanceAmount;
    private Set<String> toOrderedBargainBuyerSet; //落实付定金买家集合
    private Set<String> toOrderedBalanceBuyerSet; //落实付尾款买家集合

    private Set<String> orderedBuyerSet; //下单维度 - 下单买家集合
    private Integer orderedSkuNum;
    private Set<String> orderedBargainBuyerSet; //下询维度 - 付定金买家集合
    private Integer orderedBargainSkuNum;
    private Double orderedBargainAmount;
    private Set<String> orderedBalanceBuyerSet; //下询维度 - 付尾款买家集合
    private Integer orderedBalanceSkuNum;
    private Double orderedBalanceAmount;

    public CsPerformancePresaleBO(String activityId, Long skuId, String csNick, Date date) {
        this.activityId = activityId;
        this.skuId = skuId;
        this.csNick = csNick;
        this.date = date;

        consultBuyerSet = Sets.newHashSet();
        enquiryBuyerSet = Sets.newHashSet();
        enquiryOrderedBuyerSet = Sets.newHashSet();
        enquiryOrderedSkuNum = 0;
        enquiryOrderedBargainBuyerSet = Sets.newHashSet();
        enquiryOrderedBargainSkuNum = 0;
        enquiryOrderedBargainAmount = 0.0;
        enquiryOrderedBalanceBuyerSet = Sets.newHashSet();
        enquiryOrderedBalanceSkuNum = 0;
        enquiryOrderedBalanceAmount = 0.0;
        toOrderedBargainBuyerSet = Sets.newHashSet();
        toOrderedBalanceBuyerSet = Sets.newHashSet();

        orderedBuyerSet = Sets.newHashSet();
        orderedSkuNum = 0;
        orderedBargainBuyerSet = Sets.newHashSet();
        orderedBargainSkuNum = 0;
        orderedBargainAmount = 0.0;
        orderedBalanceBuyerSet = Sets.newHashSet();
        orderedBalanceSkuNum = 0;
        orderedBalanceAmount = 0.0;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CsPerformancePresaleBO that = (CsPerformancePresaleBO) o;
        return activityId.equals(that.activityId) &&
                skuId.equals(that.skuId) &&
                csNick.equals(that.csNick) &&
                date.equals(that.date);
    }

    @Override
    public int hashCode() {
        return Objects.hash(activityId, skuId, csNick, date);
    }
}
