package com.pes.jd.model.BO;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.pes.jd.model.Annotation.Property;

import java.util.List;

/**
 * 店铺绩效平均值BO
 *
 * <AUTHOR>
 * @create 2019-01-23 10:46
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShopPerformanceAvg {

    /*-------------------销售数据----------------------*/
    @Property(value = "店铺销售额、是否扣除退款、是否扣除邮费", direct = true)
    private Double shopSaleAmount;//店铺销售额

    @Property(value = "店铺销售量、是否扣除退款件数", direct = true)
    private Double shopSaleGoodsNum;//店铺销售量

    @Property(value = "店铺销售人数", direct = true)
    private Double shopSaleBuyerNum;//店铺销售人数

    @Property(value = "店铺销售订单数", direct = true)
    private Double shopSaleOrderNum;//店铺销售订单数

    @Property(value = "客服销售额、是否扣除退款、是否扣除邮费", direct = true)
    private Double csSaleAmount;//客服销售额

    @Property(value = "客服销售量、是否扣除退款件数", direct = true)
    private Double csSaleGoodsNum;//客服销售量

    @Property(value = "客服销售人数", direct = true)
    private Double csSaleBuyerNum;//客服销售人数

    @Property(value = "客服销售订单数", direct = true)
    private Double csSaleOrderNum;//客服销售订单数

    @Property(value = "客服销售额占比", direct = true)
    private Double csSaleAmountPercent;//客服销售额占比

    @Property(value = "客服成交件数", direct = true)
    private Double csTransactionsNum;

    //@Property(value = "客服成交笔数", direct = true)
    //private Double csSaleOrderSkuNum;//客服成交笔数

    @Property(value = "静默销售额、是否扣除退款、是否扣除邮费", direct = true)
    private Double silenceSaleAmount;//静默销售额

    @Property(value = "静默销售量、是否扣除退款件数", direct = true)
    private Double silenceSaleGoodsNum;//静默销售量

    @Property(value = "静默销售人数", direct = true)
    private Double silenceSaleBuyerNum;//静默销售人数

    @Property(value = "静默销售订单数", direct = true)
    private Double silenceSaleOrderNum;//静默销售订单数

    @Property(value = "静默销售额占比", direct = true)
    private Double silenceSaleAmountPercent;//静默销售额占比


    /*-------------------顾客数据----------------------*/


    @Property(value = "浏览量(PV)", direct = true)
    private Double pvNum;//浏览量(PV)
    @Property(value = "访客数(UV)", direct = true)
    private Double uvNum;//访客数(UV)
    @Property(value = "咨询人数", direct = true)
    private Double consultNum;//咨询人数
    @Property(value = "咨询量", direct = true)
    private Double consultSessionNum;//咨询量
    @Property(value = "接待人数", direct = true)
    private Double receiveNum;//接待人数
    @Property(value = "接待量", direct = true)
    private Double receiveSessionNum;//接待量
    @Property(value = "询单人数", direct = true)
    private Double enquiryNum;//询单人数
    @Property(value = "咨询率", direct = true)
    private Double consultPercent;//咨询率

    /*-------------------客单价----------------------*/


    @Property(value = "店铺销售客单价（元/人）", direct = true)
    private Double shopSaleGuestAvgAmount;//店铺销售客单价（元/人）

    @Property(value = "店铺销售客件数（件/人）", direct = true)
    private Double shopSaleGuestAvgGoods;//店铺销售客件数（件/人）

    @Property(value = "店铺销售件均价（元/件）", direct = true)
    private Double shopSaleGoodsAvgAmount;//店铺销售件均价（元/件）

    @Property(value = "店铺出库客单价（元/件）", direct = true)
    private Double shopOutStockGuestAvgAmount;//店铺出库客单价（元/件）

    @Property(value = "店铺出库客件数（元/人）", direct = true)
    private Double shopOutStockGuestItemNum;//店铺出库客件数（件/人）

    @Property(value = "店铺出库件均价（件/人）", direct = true)
    private Double shopOutStockItemAvgAmount;//店铺出库件均价（元/件）

    @Property(value = "客服销售客单价（元/人）", direct = true)
    private Double csSaleGuestAvgAmount;//客服销售客单价（元/人）

    @Property(value = "客服销售客件数（件/人）", direct = true)
    private Double csSaleGuestAvgGoods;//客服销售客件数（件/人）

    @Property(value = "客服销售件均价（元/件）", direct = true)
    private Double csSaleGoodsAvgAmount;//客服销售件均价（元/件）

    @Property(value = "客服出库客单价（元/人）", direct = true)
    private Double csOutStockGuestAvgAmount;//客服出库客单价（元/件）

    @Property(value = "客服出库客件数（件/人）", direct = true)
    private Double csOutStockGuestItemNum;//客服出库客件数（件/人）

    @Property(value = "客服出库件均价（元/件）", direct = true)
    private Double csOutStockItemAvgAmount;//客服出库件均价（元/件）

    @Property(value = "静默销售客单价（元/人）", direct = true)
    private Double silenceSaleGuestAvgAmount;//静默销售客单价（元/人）

    @Property(value = "静默销售客件数（件/人）", direct = true)
    private Double silenceSaleGuestAvgGoods;//静默销售客件数（件/人）

    @Property(value = "静默销售件均价（元/件）", direct = true)
    private Double silenceSaleGoodsAvgAmount;//静默销售件均价（元/件）

    @Property(value = "静默出库客单价（元/人）", direct = true)
    private Double silenceOutStockGuestAvgAmount;//静默出库客单价（元/件）

    @Property(value = "静默出库客件数（件/人）", direct = true)
    private Double silenceOutStockGuestItemNum;//静默出库客件数（件/人）

    @Property(value = "静默出库件均价（元/件）", direct = true)
    private Double silenceOutStockItemAvgAmount;//静默出库件均价（元/件）


    /*-------------------转化率----------------------*/

    @Property(value = "全店成交转化率", direct = true)
    private Double shopDealPercent;//全店成交转化率

    @Property(value = "全店出库转化率", direct = true)
    private Double shopOutStockPercent;//全店出库转化率

    @Property(value = "静默转化率", direct = true)
    private Double silenceDealPercent;//静默转化率


    @Property(value = "询单→次日付款转化率", direct = true)
    private Double queryToTomorrow;//询单→次日付款转化率

    @Property(value = "询单→最终付款转化率")
    private Double queryToFinalPaid;

    @Property(value = "询单→当日下单转化率")
    private Double queryToOrderedToday;

    @Property(value = "询单→最终下单转化率")
    private Double queryToFinalOrdered;

    @Property(value = "下单→当日付款转化率")
    private Double orderedToPaid;

    @Property(value = "下单→最终付款转化率")
    private Double orderedToPaidFinal;

    @Property(value = "询单→出库转化率")
    private Double queryToOutStock;


    /*-------------------询单→下单----------------------*/

    @Property(value = "询单→当日下单人数", direct = true)
    private Double orderedNumToday;

    private Double orderedGoodsNumToday;

    @Property(value = "询单→当日下单金额", direct = true)
    private Double orderedAmountToday;

    @Property(value = "询单→最终下单金额", direct = true)
    private Double orderedAmountFinal;

    @Property(value = "询单→最终下单人数", direct = true)
    private Double orderedNumFinal;

    @Property(value = "下单件均价（元/件）")
    private Double orderItemAvgAmount;

    @Property(value = "下单客单价（元/人）")
    private Double orderedGuestAvgPrice;

    @Property(value = "下单客件数（件/人）")
    private Double orderedGuestAvgAmount;

    /*-------------------下单→付款----------------------*/

    @Property(value = "下单→当日付款人数", direct = true)
    private Double orderedPaidNumToday;

    @Property(value = "下单→当日付款件数", direct = true)
    private Double orderedPaidGoodsToday;

    @Property(value = "下单→当日付款金额", direct = true)
    private Double orderedPaidAmountToday;

    @Property(value = "下单→当日付款订单数", direct = true)
    private Double orderedPaidOrdersToday;

    @Property(value = "下单→最终付款人数", direct = true)
    private Double orderedPaidNumFinal;

    @Property(value = "下单→最终付款件数", direct = true)
    private Double orderedPaidGoodsFinal;

    @Property(value = "下单→最终付款金额", direct = true)
    private Double orderedPaidAmountFinal;

    @Property(value = "下单→最终付款订单数", direct = true)
    private Double orderedPaidOrdersFinal;


    /*-------------------下单→出库----------------------*/

    @Property(value = "下单→出库人数", direct = true)
    private Double orderedOutStockNum;

    @Property(value = "下单→出库件数", direct = true)
    private Double orderedOutStockGoods;

    @Property(value = "下单→出库金额", direct = true)
    private Double orderedOutStockAmount;

    @Property(value = "下单→出库订单数", direct = true)
    private Double orderedOutStockOrders;

    /*-------------------询单→付款----------------------*/
    @Property(value = "询单→次日付款人数", direct = true)
    private Double paidNumTodayNext;

    @Property(value = "询单→最终付款人数", direct = true)
    private Double paidNumFinal;

    /*-------------------客服落实下单----------------------*/

    @Property(value = "客服落实下单人数", direct = true)
    private Double toOrderedNum;

    @Property(value = "客服落实下单件数", direct = true)
    private Double toOrderedGoodsNum;

    @Property(value = "客服落实下单金额", direct = true)
    private Double toOrderedAmount;

    @Property(value = "客服落实下单订单数", direct = true)
    private Double toOrderedOrderNum;


    /*-------------------工作量----------------------*/
    @Property(value = "值班客服数", direct = true)
    private Double dutyCsNum;

    @Property(value = "登录时长")
    private Double loginDurationTime;

    @Property(value = "接待时长")
    private Double rceiveDurationTime;

    @Property(value = "总消息数", direct = true)
    private Double chatNum;

    @Property(value = "顾客消息数", direct = true)
    private Double custChatNum;

    @Property(value = "客服消息数", direct = true)
    private Double csChatNum;

    @Property(value = "平均回复消息数")
    private Double avgReplyMsg;

    @Property(value = "答问比")
    private Double answerRatio;

    @Property(value = "客服字数", direct = true)
    private Double csWordNum;

    @Property(value = "最大同时接待量", direct = true)
    private Double maxReceiveSessionNum;

    @Property(value = "未回复量", direct = true)
    private Double nonReplySessionNum;

    @Property(value = "回复率")
    private Double responseRate;


    @Property(value = "慢响应量")
    private Double slowRespSessionNum;

    @Property(value = "长接待量")
    private Double longRespSessionNum;

    @Property(value = "快速应答率")
    private Double quickResponseRate;

    @Property(value = "首次平均响应（s）")
    private Double avgRespTimeFirst;

    @Property(value = "平均响应（s）")
    private Double avgRespTime;

    @Property(value = "平均会话时长（m）")
    private Double avgSessionDurationTime;

    @Property(value = "留言咨询量")
    private Double advisoryMessageNum;

    @Property(value = "留言分配量")
    private Double leaveMsgSessionNum;

    @Property(value = "留言接待量")
    private Double leaveMsgReceiveSessionNum;

    @Property(value = "留言回复率")
    private Double leaveMsgReplyRate;

    @Property(value = "留言响应率")
    private Double leaveMsgResponseRate;

    @Property(value = "回合数", direct = true)
    private Double chatRoundNum;
    private Double chatRoundNumNoLeave;//去除留言接待的聊天回合数


    @Property(value = "客服团队转入量" , direct = true)
    private Integer forwardInSessionNum;

    @Property(value = "客服团队转出量" , direct = true)
    private Integer forwardOutSessionNum;

    @Property(value = "空聊天数", direct = true)
    private Integer emptyChatNum;//空聊天数
    /*-------------------协助服务----------------------*/
    @Property("协助下单人数")
    private Double aidOrderNum;

    @Property("协助下单金额")
    private Double aidOrderAmount;

    @Property("协助跟进人数")
    private Double aidFollowNum;

    @Property("协助跟进金额")
    private Double aidFollowAmount;

    @Property("协助付款人数")
    private Double aidPayNum;

    @Property("协助付款金额")
    private Double aidPayAmount;
    /*-------------------出库数据----------------------*/

    @Property(value = "店铺出库金额", direct = true)
    private Double shopOutStockAmount;//店铺出库的金额

    @Property(value = "店铺出库件数", direct = true)
    private Double shopOutStockGoodsNum;//店铺出库件数

    @Property(value = "店铺出库人数", direct = true)
    private Double shopOutStockNum;//店铺出库的人数

    @Property(value = "店铺出库订单数", direct = true)
    private Double shopOutStockOrderNum;//店铺出库订单数

    @Property(value = "客服出库金额", direct = true)
    private Double csOutStockAmount;//客服出库的金额

    @Property(value = "客服出库件数", direct = true)
    private Double csOutStockGoodsNum;//客服出库件数

    @Property(value = "客服出库人数", direct = true)
    private Double csOutStockNum;//客服出库的人数

    @Property(value = "客服出库订单数", direct = true)
    private Double csOutStockOrderNum;//客服出库订单数

    @Property(value = "客服出库金额占比", direct = true)
    private Double csOutStockAmountPercent;//客服出库金额占比

    @Property(value = "静默出库金额", direct = true)
    private Double silenceOutStockAmount;//静默出库的金额

    @Property(value = "静默出库件数", direct = true)
    private Double silenceOutStockGoodsNum;//静默出库件数

    @Property(value = "静默出库人数", direct = true)
    private Double silenceOutStockNum;//静默出库的人数

    @Property(value = "静默出库订单数", direct = true)
    private Double silenceOutStockOrderNum;//静默出库订单数

    @Property(value = "静默出库金额占比", direct = true)
    private Double silenceOutStockAmountPercent;//静默出库金额占比


    @Property(value = "客服最终出库人数", direct = true)
    private Double csOutStockBuyerNumFinal;


    /*-------------------中差评----------------------*/

    @Property(value = "中评数", direct = true)
    private Double shopNeutralEvaluateNum;

    @Property(value = "差评数", direct = true)
    private Double shopBadEvaluateNum;

    @Property(value = "中差评总数", direct = true)
    private Double shopNeutralBadEvaluateNumTotal;


    @Property(value = "客服中评数", direct = true)
    private Double csNeutralEvaluateNum;

    @Property(value = "客服差评数", direct = true)
    private Double csBadEvaluateNum;

    @Property(value = "客服中差评总数", direct = true)
    private Double csNeutralBadEvaluateNumTotal;


    @Property(value = "静默中评数", direct = true)
    private Double silenceNeutralEvaluateNum;

    @Property(value = "静默差评数", direct = true)
    private Double silenceBadEvaluateNum;

    @Property(value = "静默中差评总数", direct = true)
    private Double silenceNeutralBadEvaluateNumTotal;


    /*-------------------满意率----------------------*/

    @Property(value = "邀评量", direct = true)
    private Double inviteEvaluateNum;

    @Property(value = "评价量")
    private Double evaluateNum;

    @Property(value = "满意度-非常满意", direct = true)
    private Double verySatisfiedNum;

    @Property(value = "满意度-满意", direct = true)
    private Double satisfiedNum;

    @Property(value = "满意度-一般", direct = true)
    private Double generalNum;

    @Property(value = "满意度-不满意", direct = true)
    private Double dissatisfiedNum;

    @Property(value = "满意度-非常不满意", direct = true)
    private Double veryDissatisfiedNum;

    @Property(value = "邀评率", direct = true)
    private Double inviteEvaluateRate;

    @Property(value = "评价率")
    private Double evaluateRate;

    @Property(value = "满意率")
    private Double satisfactionRate;

    /*-------------------退款数据----------------------*/

    @Property(value = "店铺退款笔数")
    private Double shopRefundNum;
    @Property(value = "店铺退款人数")
    private Double shopRefundBuyerNum;
    @Property(value = "店铺退款件数")
    private Double shopRefundProductNum;
    @Property(value = "店铺退款金额", direct = true)
    private Double shopRefundAmount;

    @Property(value = "客服退款笔数")
    private Double csRefundNum;
    @Property(value = "客服退款人数")
    private Double csRefundBuyerNum;
    @Property(value = "客服退款件数")
    private Double csRefundProductNum;
    @Property(value = "客服退款金额", direct = true)
    private Double csRefundAmount;
    @Property(value = "客服退款率")
    private Double csRefundPercent;

    @Property(value = "静默退款笔数")
    private Double silenceRefundNum;
    @Property(value = "静默退款人数")
    private Double silenceRefundBuyerNum;
    @Property(value = "静默退款件数")
    private Double silenceRefundProductNum;
    @Property(value = "静默退款金额", direct = true)
    private Double silenceRefundAmount;

    /*-------------------流失数据----------------------*/

    @Property(value = "询单流失人数")
    private Double enquiryLossNum;

    @Property(value = "客服落实下单未付款人数")
    private Double csPracticalOrderedUnpaidPeople;
    @Property(value = "客服落实下单未付款件数")
    private Double csPracticalOrderedUnpaidItemNum;
    @Property(value = "客服落实下单未付款金额")
    private Double csPracticalOrderedUnpaidAmount;
    @Property(value = "客服落实下单未付款订单数")
    private Double csPracticalOrderedUnpaidOrderedNum;

    @Property(value = "客服落实下单未出库人数")
    private Double csPracticalOrderedUnoutStockPeople;
    @Property(value = "客服落实下单未出库件数")
    private Double csPracticalOrderedUnoutStockItemNum;
    @Property(value = "客服落实下单未出库金额")
    private Double csPracticalOrderedUnoutStockAmount;
    @Property(value = "客服落实下单未出库订单数")
    private Double csPracticalOrderedUnoutStockOrderedNum;


    /*-------------------其他----------------------*/
    @Property(value = "店铺邮费 ", direct = true)
    private Double shopPostFee;
    @Property(value = "静默邮费", direct = true)
    private Double silencePostFee;
    @Property(value = "客服邮费", direct = true)
    private Double csPostFee;

    @Property(value = "商品评价DSR", direct = true)
    private Double productEvaluationDSR;
    @Property(value = "服务态度DSR", direct = true)
    private Double serviceAttitudeDSR;
    @Property(value = "物流速度DSR", direct = true)
    private Double logisticsSpeedDSR;

    @Property(value = "售后服务评分", direct = true)
    private Double afterSaleScore;
    @Property(value = "交易纠纷评分", direct = true)
    private Double disputeScore;


    @Property(value = "展示非最终数据的字段", direct = false)
    private List<String> notFinalData;//展示非最终数据的字段

    public List<String> getNotFinalData() {
        return notFinalData;
    }

    public void setNotFinalData(List<String> notFinalData) {
        this.notFinalData = notFinalData;
    }
    public Double getAfterSaleScore() {
        return afterSaleScore;
    }

    public void setAfterSaleScore(Double afterSaleScore) {
        this.afterSaleScore = afterSaleScore;
    }

    public Double getDisputeScore() {
        return disputeScore;
    }

    public void setDisputeScore(Double disputeScore) {
        this.disputeScore = disputeScore;
    }

    public Double getChatRoundNum() {
        return chatRoundNum;
    }

    public void setChatRoundNum(Double chatRoundNum) {
        this.chatRoundNum = chatRoundNum;
    }

    public Double getChatRoundNumNoLeave() {
        return chatRoundNumNoLeave;
    }

    public void setChatRoundNumNoLeave(Double chatRoundNumNoLeave) {
        this.chatRoundNumNoLeave = chatRoundNumNoLeave;
    }

    public Double getShopSaleAmount() {
        return shopSaleAmount;
    }

    public void setShopSaleAmount(Double shopSaleAmount) {
        this.shopSaleAmount = shopSaleAmount;
    }

    public Double getShopSaleGoodsNum() {
        return shopSaleGoodsNum;
    }

    public void setShopSaleGoodsNum(Double shopSaleGoodsNum) {
        this.shopSaleGoodsNum = shopSaleGoodsNum;
    }

    public Double getShopSaleBuyerNum() {
        return shopSaleBuyerNum;
    }

    public void setShopSaleBuyerNum(Double shopSaleBuyerNum) {
        this.shopSaleBuyerNum = shopSaleBuyerNum;
    }

    public Double getShopSaleOrderNum() {
        return shopSaleOrderNum;
    }

    public void setShopSaleOrderNum(Double shopSaleOrderNum) {
        this.shopSaleOrderNum = shopSaleOrderNum;
    }

    public Double getCsSaleAmount() {
        return csSaleAmount;
    }

    public void setCsSaleAmount(Double csSaleAmount) {
        this.csSaleAmount = csSaleAmount;
    }

    public Double getCsSaleGoodsNum() {
        return csSaleGoodsNum;
    }

    public void setCsSaleGoodsNum(Double csSaleGoodsNum) {
        this.csSaleGoodsNum = csSaleGoodsNum;
    }

    public Double getCsSaleBuyerNum() {
        return csSaleBuyerNum;
    }

    public void setCsSaleBuyerNum(Double csSaleBuyerNum) {
        this.csSaleBuyerNum = csSaleBuyerNum;
    }

    public Double getCsSaleOrderNum() {
        return csSaleOrderNum;
    }

    public void setCsSaleOrderNum(Double csSaleOrderNum) {
        this.csSaleOrderNum = csSaleOrderNum;
    }

    public Double getCsSaleAmountPercent() {
        return csSaleAmountPercent;
    }

    public void setCsSaleAmountPercent(Double csSaleAmountPercent) {
        this.csSaleAmountPercent = csSaleAmountPercent;
    }

    public Double getCsTransactionsNum() {
        return csTransactionsNum;
    }

    public void setCsTransactionsNum(Double csTransactionsNum) {
        this.csTransactionsNum = csTransactionsNum;
    }

    public Double getSilenceSaleAmount() {
        return silenceSaleAmount;
    }

    public void setSilenceSaleAmount(Double silenceSaleAmount) {
        this.silenceSaleAmount = silenceSaleAmount;
    }

    public Double getSilenceSaleGoodsNum() {
        return silenceSaleGoodsNum;
    }

    public void setSilenceSaleGoodsNum(Double silenceSaleGoodsNum) {
        this.silenceSaleGoodsNum = silenceSaleGoodsNum;
    }

    public Double getSilenceSaleBuyerNum() {
        return silenceSaleBuyerNum;
    }

    public void setSilenceSaleBuyerNum(Double silenceSaleBuyerNum) {
        this.silenceSaleBuyerNum = silenceSaleBuyerNum;
    }

    public Double getSilenceSaleOrderNum() {
        return silenceSaleOrderNum;
    }

    public void setSilenceSaleOrderNum(Double silenceSaleOrderNum) {
        this.silenceSaleOrderNum = silenceSaleOrderNum;
    }

    public Double getSilenceSaleAmountPercent() {
        return silenceSaleAmountPercent;
    }

    public void setSilenceSaleAmountPercent(Double silenceSaleAmountPercent) {
        this.silenceSaleAmountPercent = silenceSaleAmountPercent;
    }

    public Double getPvNum() {
        return pvNum;
    }

    public void setPvNum(Double pvNum) {
        this.pvNum = pvNum;
    }

    public Double getUvNum() {
        return uvNum;
    }

    public void setUvNum(Double uvNum) {
        this.uvNum = uvNum;
    }

    public Double getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(Double consultNum) {
        this.consultNum = consultNum;
    }

    public Double getConsultSessionNum() {
        return consultSessionNum;
    }

    public void setConsultSessionNum(Double consultSessionNum) {
        this.consultSessionNum = consultSessionNum;
    }

    public Double getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Double receiveNum) {
        this.receiveNum = receiveNum;
    }

    public Double getReceiveSessionNum() {
        return receiveSessionNum;
    }

    public void setReceiveSessionNum(Double receiveSessionNum) {
        this.receiveSessionNum = receiveSessionNum;
    }

    public Double getEnquiryNum() {
        return enquiryNum;
    }

    public void setEnquiryNum(Double enquiryNum) {
        this.enquiryNum = enquiryNum;
    }

    public Double getConsultPercent() {
        return consultPercent;
    }

    public void setConsultPercent(Double consultPercent) {
        this.consultPercent = consultPercent;
    }

    public Double getShopSaleGuestAvgAmount() {
        return shopSaleGuestAvgAmount;
    }

    public void setShopSaleGuestAvgAmount(Double shopSaleGuestAvgAmount) {
        this.shopSaleGuestAvgAmount = shopSaleGuestAvgAmount;
    }

    public Double getShopSaleGuestAvgGoods() {
        return shopSaleGuestAvgGoods;
    }

    public void setShopSaleGuestAvgGoods(Double shopSaleGuestAvgGoods) {
        this.shopSaleGuestAvgGoods = shopSaleGuestAvgGoods;
    }

    public Double getShopSaleGoodsAvgAmount() {
        return shopSaleGoodsAvgAmount;
    }

    public void setShopSaleGoodsAvgAmount(Double shopSaleGoodsAvgAmount) {
        this.shopSaleGoodsAvgAmount = shopSaleGoodsAvgAmount;
    }

    public Double getShopOutStockGuestAvgAmount() {
        return shopOutStockGuestAvgAmount;
    }

    public void setShopOutStockGuestAvgAmount(Double shopOutStockGuestAvgAmount) {
        this.shopOutStockGuestAvgAmount = shopOutStockGuestAvgAmount;
    }

    public Double getShopOutStockGuestItemNum() {
        return shopOutStockGuestItemNum;
    }

    public void setShopOutStockGuestItemNum(Double shopOutStockGuestItemNum) {
        this.shopOutStockGuestItemNum = shopOutStockGuestItemNum;
    }

    public Double getShopOutStockItemAvgAmount() {
        return shopOutStockItemAvgAmount;
    }

    public void setShopOutStockItemAvgAmount(Double shopOutStockItemAvgAmount) {
        this.shopOutStockItemAvgAmount = shopOutStockItemAvgAmount;
    }

    public Double getCsSaleGuestAvgAmount() {
        return csSaleGuestAvgAmount;
    }

    public void setCsSaleGuestAvgAmount(Double csSaleGuestAvgAmount) {
        this.csSaleGuestAvgAmount = csSaleGuestAvgAmount;
    }

    public Double getCsSaleGuestAvgGoods() {
        return csSaleGuestAvgGoods;
    }

    public void setCsSaleGuestAvgGoods(Double csSaleGuestAvgGoods) {
        this.csSaleGuestAvgGoods = csSaleGuestAvgGoods;
    }

    public Double getCsSaleGoodsAvgAmount() {
        return csSaleGoodsAvgAmount;
    }

    public void setCsSaleGoodsAvgAmount(Double csSaleGoodsAvgAmount) {
        this.csSaleGoodsAvgAmount = csSaleGoodsAvgAmount;
    }

    public Double getCsOutStockGuestAvgAmount() {
        return csOutStockGuestAvgAmount;
    }

    public void setCsOutStockGuestAvgAmount(Double csOutStockGuestAvgAmount) {
        this.csOutStockGuestAvgAmount = csOutStockGuestAvgAmount;
    }

    public Double getCsOutStockGuestItemNum() {
        return csOutStockGuestItemNum;
    }

    public void setCsOutStockGuestItemNum(Double csOutStockGuestItemNum) {
        this.csOutStockGuestItemNum = csOutStockGuestItemNum;
    }

    public Double getCsOutStockItemAvgAmount() {
        return csOutStockItemAvgAmount;
    }

    public void setCsOutStockItemAvgAmount(Double csOutStockItemAvgAmount) {
        this.csOutStockItemAvgAmount = csOutStockItemAvgAmount;
    }

    public Double getSilenceSaleGuestAvgAmount() {
        return silenceSaleGuestAvgAmount;
    }

    public void setSilenceSaleGuestAvgAmount(Double silenceSaleGuestAvgAmount) {
        this.silenceSaleGuestAvgAmount = silenceSaleGuestAvgAmount;
    }

    public Double getSilenceSaleGuestAvgGoods() {
        return silenceSaleGuestAvgGoods;
    }

    public void setSilenceSaleGuestAvgGoods(Double silenceSaleGuestAvgGoods) {
        this.silenceSaleGuestAvgGoods = silenceSaleGuestAvgGoods;
    }

    public Double getSilenceSaleGoodsAvgAmount() {
        return silenceSaleGoodsAvgAmount;
    }

    public void setSilenceSaleGoodsAvgAmount(Double silenceSaleGoodsAvgAmount) {
        this.silenceSaleGoodsAvgAmount = silenceSaleGoodsAvgAmount;
    }

    public Double getSilenceOutStockGuestAvgAmount() {
        return silenceOutStockGuestAvgAmount;
    }

    public void setSilenceOutStockGuestAvgAmount(Double silenceOutStockGuestAvgAmount) {
        this.silenceOutStockGuestAvgAmount = silenceOutStockGuestAvgAmount;
    }

    public Double getSilenceOutStockGuestItemNum() {
        return silenceOutStockGuestItemNum;
    }

    public void setSilenceOutStockGuestItemNum(Double silenceOutStockGuestItemNum) {
        this.silenceOutStockGuestItemNum = silenceOutStockGuestItemNum;
    }

    public Double getSilenceOutStockItemAvgAmount() {
        return silenceOutStockItemAvgAmount;
    }

    public void setSilenceOutStockItemAvgAmount(Double silenceOutStockItemAvgAmount) {
        this.silenceOutStockItemAvgAmount = silenceOutStockItemAvgAmount;
    }

    public Double getShopDealPercent() {
        return shopDealPercent;
    }

    public void setShopDealPercent(Double shopDealPercent) {
        this.shopDealPercent = shopDealPercent;
    }

    public Double getShopOutStockPercent() {
        return shopOutStockPercent;
    }

    public void setShopOutStockPercent(Double shopOutStockPercent) {
        this.shopOutStockPercent = shopOutStockPercent;
    }

    public Double getSilenceDealPercent() {
        return silenceDealPercent;
    }

    public void setSilenceDealPercent(Double silenceDealPercent) {
        this.silenceDealPercent = silenceDealPercent;
    }

    public Double getQueryToTomorrow() {
        return queryToTomorrow;
    }

    public void setQueryToTomorrow(Double queryToTomorrow) {
        this.queryToTomorrow = queryToTomorrow;
    }

    public Double getQueryToFinalPaid() {
        return queryToFinalPaid;
    }

    public void setQueryToFinalPaid(Double queryToFinalPaid) {
        this.queryToFinalPaid = queryToFinalPaid;
    }

    public Double getQueryToOrderedToday() {
        return queryToOrderedToday;
    }

    public void setQueryToOrderedToday(Double queryToOrderedToday) {
        this.queryToOrderedToday = queryToOrderedToday;
    }

    public Double getQueryToFinalOrdered() {
        return queryToFinalOrdered;
    }

    public void setQueryToFinalOrdered(Double queryToFinalOrdered) {
        this.queryToFinalOrdered = queryToFinalOrdered;
    }

    public Double getOrderedToPaid() {
        return orderedToPaid;
    }

    public void setOrderedToPaid(Double orderedToPaid) {
        this.orderedToPaid = orderedToPaid;
    }

    public Double getOrderedToPaidFinal() {
        return orderedToPaidFinal;
    }

    public void setOrderedToPaidFinal(Double orderedToPaidFinal) {
        this.orderedToPaidFinal = orderedToPaidFinal;
    }

    public Double getQueryToOutStock() {
        return queryToOutStock;
    }

    public void setQueryToOutStock(Double queryToOutStock) {
        this.queryToOutStock = queryToOutStock;
    }

    public Double getOrderedNumToday() {
        return orderedNumToday;
    }

    public void setOrderedNumToday(Double orderedNumToday) {
        this.orderedNumToday = orderedNumToday;
    }

    public Double getOrderedGoodsNumToday() {
        return orderedGoodsNumToday;
    }

    public void setOrderedGoodsNumToday(Double orderedGoodsNumToday) {
        this.orderedGoodsNumToday = orderedGoodsNumToday;
    }

    public Double getOrderedAmountToday() {
        return orderedAmountToday;
    }

    public void setOrderedAmountToday(Double orderedAmountToday) {
        this.orderedAmountToday = orderedAmountToday;
    }

    public Double getOrderedAmountFinal() {
        return orderedAmountFinal;
    }

    public void setOrderedAmountFinal(Double orderedAmountFinal) {
        this.orderedAmountFinal = orderedAmountFinal;
    }

    public Double getOrderedNumFinal() {
        return orderedNumFinal;
    }

    public void setOrderedNumFinal(Double orderedNumFinal) {
        this.orderedNumFinal = orderedNumFinal;
    }

    public Double getOrderItemAvgAmount() {
        return orderItemAvgAmount;
    }

    public void setOrderItemAvgAmount(Double orderItemAvgAmount) {
        this.orderItemAvgAmount = orderItemAvgAmount;
    }

    public Double getOrderedGuestAvgPrice() {
        return orderedGuestAvgPrice;
    }

    public void setOrderedGuestAvgPrice(Double orderedGuestAvgPrice) {
        this.orderedGuestAvgPrice = orderedGuestAvgPrice;
    }

    public Double getOrderedGuestAvgAmount() {
        return orderedGuestAvgAmount;
    }

    public void setOrderedGuestAvgAmount(Double orderedGuestAvgAmount) {
        this.orderedGuestAvgAmount = orderedGuestAvgAmount;
    }

    public Double getOrderedPaidNumToday() {
        return orderedPaidNumToday;
    }

    public void setOrderedPaidNumToday(Double orderedPaidNumToday) {
        this.orderedPaidNumToday = orderedPaidNumToday;
    }

    public Double getOrderedPaidGoodsToday() {
        return orderedPaidGoodsToday;
    }

    public void setOrderedPaidGoodsToday(Double orderedPaidGoodsToday) {
        this.orderedPaidGoodsToday = orderedPaidGoodsToday;
    }

    public Double getOrderedPaidAmountToday() {
        return orderedPaidAmountToday;
    }

    public void setOrderedPaidAmountToday(Double orderedPaidAmountToday) {
        this.orderedPaidAmountToday = orderedPaidAmountToday;
    }

    public Double getOrderedPaidOrdersToday() {
        return orderedPaidOrdersToday;
    }

    public void setOrderedPaidOrdersToday(Double orderedPaidOrdersToday) {
        this.orderedPaidOrdersToday = orderedPaidOrdersToday;
    }

    public Double getOrderedPaidNumFinal() {
        return orderedPaidNumFinal;
    }

    public void setOrderedPaidNumFinal(Double orderedPaidNumFinal) {
        this.orderedPaidNumFinal = orderedPaidNumFinal;
    }

    public Double getOrderedPaidGoodsFinal() {
        return orderedPaidGoodsFinal;
    }

    public void setOrderedPaidGoodsFinal(Double orderedPaidGoodsFinal) {
        this.orderedPaidGoodsFinal = orderedPaidGoodsFinal;
    }

    public Double getOrderedPaidAmountFinal() {
        return orderedPaidAmountFinal;
    }

    public void setOrderedPaidAmountFinal(Double orderedPaidAmountFinal) {
        this.orderedPaidAmountFinal = orderedPaidAmountFinal;
    }

    public Double getOrderedPaidOrdersFinal() {
        return orderedPaidOrdersFinal;
    }

    public void setOrderedPaidOrdersFinal(Double orderedPaidOrdersFinal) {
        this.orderedPaidOrdersFinal = orderedPaidOrdersFinal;
    }

    public Double getOrderedOutStockNum() {
        return orderedOutStockNum;
    }

    public void setOrderedOutStockNum(Double orderedOutStockNum) {
        this.orderedOutStockNum = orderedOutStockNum;
    }

    public Double getOrderedOutStockGoods() {
        return orderedOutStockGoods;
    }

    public void setOrderedOutStockGoods(Double orderedOutStockGoods) {
        this.orderedOutStockGoods = orderedOutStockGoods;
    }

    public Double getOrderedOutStockAmount() {
        return orderedOutStockAmount;
    }

    public void setOrderedOutStockAmount(Double orderedOutStockAmount) {
        this.orderedOutStockAmount = orderedOutStockAmount;
    }

    public Double getOrderedOutStockOrders() {
        return orderedOutStockOrders;
    }

    public void setOrderedOutStockOrders(Double orderedOutStockOrders) {
        this.orderedOutStockOrders = orderedOutStockOrders;
    }

    public Double getPaidNumTodayNext() {
        return paidNumTodayNext;
    }

    public void setPaidNumTodayNext(Double paidNumTodayNext) {
        this.paidNumTodayNext = paidNumTodayNext;
    }

    public Double getPaidNumFinal() {
        return paidNumFinal;
    }

    public void setPaidNumFinal(Double paidNumFinal) {
        this.paidNumFinal = paidNumFinal;
    }

    public Double getToOrderedNum() {
        return toOrderedNum;
    }

    public void setToOrderedNum(Double toOrderedNum) {
        this.toOrderedNum = toOrderedNum;
    }

    public Double getToOrderedGoodsNum() {
        return toOrderedGoodsNum;
    }

    public void setToOrderedGoodsNum(Double toOrderedGoodsNum) {
        this.toOrderedGoodsNum = toOrderedGoodsNum;
    }

    public Double getToOrderedAmount() {
        return toOrderedAmount;
    }

    public void setToOrderedAmount(Double toOrderedAmount) {
        this.toOrderedAmount = toOrderedAmount;
    }

    public Double getToOrderedOrderNum() {
        return toOrderedOrderNum;
    }

    public void setToOrderedOrderNum(Double toOrderedOrderNum) {
        this.toOrderedOrderNum = toOrderedOrderNum;
    }

    public Double getDutyCsNum() {
        return dutyCsNum;
    }

    public void setDutyCsNum(Double dutyCsNum) {
        this.dutyCsNum = dutyCsNum;
    }

    public Double getLoginDurationTime() {
        return loginDurationTime;
    }

    public void setLoginDurationTime(Double loginDurationTime) {
        this.loginDurationTime = loginDurationTime;
    }

    public Double getRceiveDurationTime() {
        return rceiveDurationTime;
    }

    public void setRceiveDurationTime(Double rceiveDurationTime) {
        this.rceiveDurationTime = rceiveDurationTime;
    }

    public Double getChatNum() {
        return chatNum;
    }

    public void setChatNum(Double chatNum) {
        this.chatNum = chatNum;
    }

    public Double getCustChatNum() {
        return custChatNum;
    }

    public void setCustChatNum(Double custChatNum) {
        this.custChatNum = custChatNum;
    }

    public Double getCsChatNum() {
        return csChatNum;
    }

    public void setCsChatNum(Double csChatNum) {
        this.csChatNum = csChatNum;
    }

    public Double getAvgReplyMsg() {
        return avgReplyMsg;
    }

    public void setAvgReplyMsg(Double avgReplyMsg) {
        this.avgReplyMsg = avgReplyMsg;
    }

    public Double getAnswerRatio() {
        return answerRatio;
    }

    public void setAnswerRatio(Double answerRatio) {
        this.answerRatio = answerRatio;
    }

    public Double getCsWordNum() {
        return csWordNum;
    }

    public void setCsWordNum(Double csWordNum) {
        this.csWordNum = csWordNum;
    }

    public Double getMaxReceiveSessionNum() {
        return maxReceiveSessionNum;
    }

    public void setMaxReceiveSessionNum(Double maxReceiveSessionNum) {
        this.maxReceiveSessionNum = maxReceiveSessionNum;
    }

    public Double getNonReplySessionNum() {
        return nonReplySessionNum;
    }

    public void setNonReplySessionNum(Double nonReplySessionNum) {
        this.nonReplySessionNum = nonReplySessionNum;
    }

    public Double getResponseRate() {
        return responseRate;
    }

    public void setResponseRate(Double responseRate) {
        this.responseRate = responseRate;
    }

    public Double getSlowRespSessionNum() {
        return slowRespSessionNum;
    }

    public void setSlowRespSessionNum(Double slowRespSessionNum) {
        this.slowRespSessionNum = slowRespSessionNum;
    }

    public Double getLongRespSessionNum() {
        return longRespSessionNum;
    }

    public void setLongRespSessionNum(Double longRespSessionNum) {
        this.longRespSessionNum = longRespSessionNum;
    }

    public Double getQuickResponseRate() {
        return quickResponseRate;
    }

    public void setQuickResponseRate(Double quickResponseRate) {
        this.quickResponseRate = quickResponseRate;
    }

    public Double getAvgRespTimeFirst() {
        return avgRespTimeFirst;
    }

    public void setAvgRespTimeFirst(Double avgRespTimeFirst) {
        this.avgRespTimeFirst = avgRespTimeFirst;
    }

    public Double getAvgRespTime() {
        return avgRespTime;
    }

    public void setAvgRespTime(Double avgRespTime) {
        this.avgRespTime = avgRespTime;
    }

    public Double getAvgSessionDurationTime() {
        return avgSessionDurationTime;
    }

    public void setAvgSessionDurationTime(Double avgSessionDurationTime) {
        this.avgSessionDurationTime = avgSessionDurationTime;
    }

    public Double getAdvisoryMessageNum() {
        return advisoryMessageNum;
    }

    public void setAdvisoryMessageNum(Double advisoryMessageNum) {
        this.advisoryMessageNum = advisoryMessageNum;
    }

    public Double getLeaveMsgSessionNum() {
        return leaveMsgSessionNum;
    }

    public void setLeaveMsgSessionNum(Double leaveMsgSessionNum) {
        this.leaveMsgSessionNum = leaveMsgSessionNum;
    }

    public Double getLeaveMsgReceiveSessionNum() {
        return leaveMsgReceiveSessionNum;
    }

    public void setLeaveMsgReceiveSessionNum(Double leaveMsgReceiveSessionNum) {
        this.leaveMsgReceiveSessionNum = leaveMsgReceiveSessionNum;
    }

    public Double getLeaveMsgReplyRate() {
        return leaveMsgReplyRate;
    }

    public void setLeaveMsgReplyRate(Double leaveMsgReplyRate) {
        this.leaveMsgReplyRate = leaveMsgReplyRate;
    }

    public Double getLeaveMsgResponseRate() {
        return leaveMsgResponseRate;
    }

    public void setLeaveMsgResponseRate(Double leaveMsgResponseRate) {
        this.leaveMsgResponseRate = leaveMsgResponseRate;
    }

    public Double getAidOrderNum() {
        return aidOrderNum;
    }

    public void setAidOrderNum(Double aidOrderNum) {
        this.aidOrderNum = aidOrderNum;
    }

    public Double getAidOrderAmount() {
        return aidOrderAmount;
    }

    public void setAidOrderAmount(Double aidOrderAmount) {
        this.aidOrderAmount = aidOrderAmount;
    }

    public Double getAidFollowNum() {
        return aidFollowNum;
    }

    public void setAidFollowNum(Double aidFollowNum) {
        this.aidFollowNum = aidFollowNum;
    }

    public Double getAidFollowAmount() {
        return aidFollowAmount;
    }

    public void setAidFollowAmount(Double aidFollowAmount) {
        this.aidFollowAmount = aidFollowAmount;
    }

    public Double getAidPayNum() {
        return aidPayNum;
    }

    public void setAidPayNum(Double aidPayNum) {
        this.aidPayNum = aidPayNum;
    }

    public Double getAidPayAmount() {
        return aidPayAmount;
    }

    public void setAidPayAmount(Double aidPayAmount) {
        this.aidPayAmount = aidPayAmount;
    }

    public Double getShopOutStockAmount() {
        return shopOutStockAmount;
    }

    public void setShopOutStockAmount(Double shopOutStockAmount) {
        this.shopOutStockAmount = shopOutStockAmount;
    }

    public Double getShopOutStockGoodsNum() {
        return shopOutStockGoodsNum;
    }

    public void setShopOutStockGoodsNum(Double shopOutStockGoodsNum) {
        this.shopOutStockGoodsNum = shopOutStockGoodsNum;
    }

    public Double getShopOutStockNum() {
        return shopOutStockNum;
    }

    public void setShopOutStockNum(Double shopOutStockNum) {
        this.shopOutStockNum = shopOutStockNum;
    }

    public Double getShopOutStockOrderNum() {
        return shopOutStockOrderNum;
    }

    public void setShopOutStockOrderNum(Double shopOutStockOrderNum) {
        this.shopOutStockOrderNum = shopOutStockOrderNum;
    }

    public Double getCsOutStockAmount() {
        return csOutStockAmount;
    }

    public void setCsOutStockAmount(Double csOutStockAmount) {
        this.csOutStockAmount = csOutStockAmount;
    }

    public Double getCsOutStockGoodsNum() {
        return csOutStockGoodsNum;
    }

    public void setCsOutStockGoodsNum(Double csOutStockGoodsNum) {
        this.csOutStockGoodsNum = csOutStockGoodsNum;
    }

    public Double getCsOutStockNum() {
        return csOutStockNum;
    }

    public void setCsOutStockNum(Double csOutStockNum) {
        this.csOutStockNum = csOutStockNum;
    }

    public Double getCsOutStockOrderNum() {
        return csOutStockOrderNum;
    }

    public void setCsOutStockOrderNum(Double csOutStockOrderNum) {
        this.csOutStockOrderNum = csOutStockOrderNum;
    }

    public Double getCsOutStockAmountPercent() {
        return csOutStockAmountPercent;
    }

    public void setCsOutStockAmountPercent(Double csOutStockAmountPercent) {
        this.csOutStockAmountPercent = csOutStockAmountPercent;
    }

    public Double getSilenceOutStockAmount() {
        return silenceOutStockAmount;
    }

    public void setSilenceOutStockAmount(Double silenceOutStockAmount) {
        this.silenceOutStockAmount = silenceOutStockAmount;
    }

    public Double getSilenceOutStockGoodsNum() {
        return silenceOutStockGoodsNum;
    }

    public void setSilenceOutStockGoodsNum(Double silenceOutStockGoodsNum) {
        this.silenceOutStockGoodsNum = silenceOutStockGoodsNum;
    }

    public Double getSilenceOutStockNum() {
        return silenceOutStockNum;
    }

    public void setSilenceOutStockNum(Double silenceOutStockNum) {
        this.silenceOutStockNum = silenceOutStockNum;
    }

    public Double getSilenceOutStockOrderNum() {
        return silenceOutStockOrderNum;
    }

    public void setSilenceOutStockOrderNum(Double silenceOutStockOrderNum) {
        this.silenceOutStockOrderNum = silenceOutStockOrderNum;
    }

    public Double getSilenceOutStockAmountPercent() {
        return silenceOutStockAmountPercent;
    }

    public void setSilenceOutStockAmountPercent(Double silenceOutStockAmountPercent) {
        this.silenceOutStockAmountPercent = silenceOutStockAmountPercent;
    }

    public Double getCsOutStockBuyerNumFinal() {
        return csOutStockBuyerNumFinal;
    }

    public void setCsOutStockBuyerNumFinal(Double csOutStockBuyerNumFinal) {
        this.csOutStockBuyerNumFinal = csOutStockBuyerNumFinal;
    }

    public Double getShopNeutralEvaluateNum() {
        return shopNeutralEvaluateNum;
    }

    public void setShopNeutralEvaluateNum(Double shopNeutralEvaluateNum) {
        this.shopNeutralEvaluateNum = shopNeutralEvaluateNum;
    }

    public Double getShopBadEvaluateNum() {
        return shopBadEvaluateNum;
    }

    public void setShopBadEvaluateNum(Double shopBadEvaluateNum) {
        this.shopBadEvaluateNum = shopBadEvaluateNum;
    }

    public Double getShopNeutralBadEvaluateNumTotal() {
        return shopNeutralBadEvaluateNumTotal;
    }

    public void setShopNeutralBadEvaluateNumTotal(Double shopNeutralBadEvaluateNumTotal) {
        this.shopNeutralBadEvaluateNumTotal = shopNeutralBadEvaluateNumTotal;
    }

    public Double getCsNeutralEvaluateNum() {
        return csNeutralEvaluateNum;
    }

    public void setCsNeutralEvaluateNum(Double csNeutralEvaluateNum) {
        this.csNeutralEvaluateNum = csNeutralEvaluateNum;
    }

    public Double getCsBadEvaluateNum() {
        return csBadEvaluateNum;
    }

    public void setCsBadEvaluateNum(Double csBadEvaluateNum) {
        this.csBadEvaluateNum = csBadEvaluateNum;
    }

    public Double getCsNeutralBadEvaluateNumTotal() {
        return csNeutralBadEvaluateNumTotal;
    }

    public void setCsNeutralBadEvaluateNumTotal(Double csNeutralBadEvaluateNumTotal) {
        this.csNeutralBadEvaluateNumTotal = csNeutralBadEvaluateNumTotal;
    }

    public Double getSilenceNeutralEvaluateNum() {
        return silenceNeutralEvaluateNum;
    }

    public void setSilenceNeutralEvaluateNum(Double silenceNeutralEvaluateNum) {
        this.silenceNeutralEvaluateNum = silenceNeutralEvaluateNum;
    }

    public Double getSilenceBadEvaluateNum() {
        return silenceBadEvaluateNum;
    }

    public void setSilenceBadEvaluateNum(Double silenceBadEvaluateNum) {
        this.silenceBadEvaluateNum = silenceBadEvaluateNum;
    }

    public Double getSilenceNeutralBadEvaluateNumTotal() {
        return silenceNeutralBadEvaluateNumTotal;
    }

    public void setSilenceNeutralBadEvaluateNumTotal(Double silenceNeutralBadEvaluateNumTotal) {
        this.silenceNeutralBadEvaluateNumTotal = silenceNeutralBadEvaluateNumTotal;
    }

    public Double getInviteEvaluateNum() {
        return inviteEvaluateNum;
    }

    public void setInviteEvaluateNum(Double inviteEvaluateNum) {
        this.inviteEvaluateNum = inviteEvaluateNum;
    }

    public Double getEvaluateNum() {
        return evaluateNum;
    }

    public void setEvaluateNum(Double evaluateNum) {
        this.evaluateNum = evaluateNum;
    }

    public Double getVerySatisfiedNum() {
        return verySatisfiedNum;
    }

    public void setVerySatisfiedNum(Double verySatisfiedNum) {
        this.verySatisfiedNum = verySatisfiedNum;
    }

    public Double getSatisfiedNum() {
        return satisfiedNum;
    }

    public void setSatisfiedNum(Double satisfiedNum) {
        this.satisfiedNum = satisfiedNum;
    }

    public Double getGeneralNum() {
        return generalNum;
    }

    public void setGeneralNum(Double generalNum) {
        this.generalNum = generalNum;
    }

    public Double getDissatisfiedNum() {
        return dissatisfiedNum;
    }

    public void setDissatisfiedNum(Double dissatisfiedNum) {
        this.dissatisfiedNum = dissatisfiedNum;
    }

    public Double getVeryDissatisfiedNum() {
        return veryDissatisfiedNum;
    }

    public void setVeryDissatisfiedNum(Double veryDissatisfiedNum) {
        this.veryDissatisfiedNum = veryDissatisfiedNum;
    }

    public Double getInviteEvaluateRate() {
        return inviteEvaluateRate;
    }

    public void setInviteEvaluateRate(Double inviteEvaluateRate) {
        this.inviteEvaluateRate = inviteEvaluateRate;
    }

    public Double getEvaluateRate() {
        return evaluateRate;
    }

    public void setEvaluateRate(Double evaluateRate) {
        this.evaluateRate = evaluateRate;
    }

    public Double getSatisfactionRate() {
        return satisfactionRate;
    }

    public void setSatisfactionRate(Double satisfactionRate) {
        this.satisfactionRate = satisfactionRate;
    }

    public Double getShopRefundNum() {
        return shopRefundNum;
    }

    public void setShopRefundNum(Double shopRefundNum) {
        this.shopRefundNum = shopRefundNum;
    }

    public Double getShopRefundBuyerNum() {
        return shopRefundBuyerNum;
    }

    public void setShopRefundBuyerNum(Double shopRefundBuyerNum) {
        this.shopRefundBuyerNum = shopRefundBuyerNum;
    }

    public Double getShopRefundProductNum() {
        return shopRefundProductNum;
    }

    public void setShopRefundProductNum(Double shopRefundProductNum) {
        this.shopRefundProductNum = shopRefundProductNum;
    }

    public Double getShopRefundAmount() {
        return shopRefundAmount;
    }

    public void setShopRefundAmount(Double shopRefundAmount) {
        this.shopRefundAmount = shopRefundAmount;
    }

    public Double getCsRefundNum() {
        return csRefundNum;
    }

    public void setCsRefundNum(Double csRefundNum) {
        this.csRefundNum = csRefundNum;
    }

    public Double getCsRefundBuyerNum() {
        return csRefundBuyerNum;
    }

    public void setCsRefundBuyerNum(Double csRefundBuyerNum) {
        this.csRefundBuyerNum = csRefundBuyerNum;
    }

    public Double getCsRefundProductNum() {
        return csRefundProductNum;
    }

    public void setCsRefundProductNum(Double csRefundProductNum) {
        this.csRefundProductNum = csRefundProductNum;
    }

    public Double getCsRefundAmount() {
        return csRefundAmount;
    }

    public void setCsRefundAmount(Double csRefundAmount) {
        this.csRefundAmount = csRefundAmount;
    }

    public Double getCsRefundPercent() {
        return csRefundPercent;
    }

    public void setCsRefundPercent(Double csRefundPercent) {
        this.csRefundPercent = csRefundPercent;
    }

    public Double getSilenceRefundNum() {
        return silenceRefundNum;
    }

    public void setSilenceRefundNum(Double silenceRefundNum) {
        this.silenceRefundNum = silenceRefundNum;
    }

    public Double getSilenceRefundBuyerNum() {
        return silenceRefundBuyerNum;
    }

    public void setSilenceRefundBuyerNum(Double silenceRefundBuyerNum) {
        this.silenceRefundBuyerNum = silenceRefundBuyerNum;
    }

    public Double getSilenceRefundProductNum() {
        return silenceRefundProductNum;
    }

    public void setSilenceRefundProductNum(Double silenceRefundProductNum) {
        this.silenceRefundProductNum = silenceRefundProductNum;
    }

    public Double getSilenceRefundAmount() {
        return silenceRefundAmount;
    }

    public void setSilenceRefundAmount(Double silenceRefundAmount) {
        this.silenceRefundAmount = silenceRefundAmount;
    }

    public Double getEnquiryLossNum() {
        return enquiryLossNum;
    }

    public void setEnquiryLossNum(Double enquiryLossNum) {
        this.enquiryLossNum = enquiryLossNum;
    }

    public Double getCsPracticalOrderedUnpaidPeople() {
        return csPracticalOrderedUnpaidPeople;
    }

    public void setCsPracticalOrderedUnpaidPeople(Double csPracticalOrderedUnpaidPeople) {
        this.csPracticalOrderedUnpaidPeople = csPracticalOrderedUnpaidPeople;
    }

    public Double getCsPracticalOrderedUnpaidItemNum() {
        return csPracticalOrderedUnpaidItemNum;
    }

    public void setCsPracticalOrderedUnpaidItemNum(Double csPracticalOrderedUnpaidItemNum) {
        this.csPracticalOrderedUnpaidItemNum = csPracticalOrderedUnpaidItemNum;
    }

    public Double getCsPracticalOrderedUnpaidAmount() {
        return csPracticalOrderedUnpaidAmount;
    }

    public void setCsPracticalOrderedUnpaidAmount(Double csPracticalOrderedUnpaidAmount) {
        this.csPracticalOrderedUnpaidAmount = csPracticalOrderedUnpaidAmount;
    }

    public Double getCsPracticalOrderedUnpaidOrderedNum() {
        return csPracticalOrderedUnpaidOrderedNum;
    }

    public void setCsPracticalOrderedUnpaidOrderedNum(Double csPracticalOrderedUnpaidOrderedNum) {
        this.csPracticalOrderedUnpaidOrderedNum = csPracticalOrderedUnpaidOrderedNum;
    }

    public Double getCsPracticalOrderedUnoutStockPeople() {
        return csPracticalOrderedUnoutStockPeople;
    }

    public void setCsPracticalOrderedUnoutStockPeople(Double csPracticalOrderedUnoutStockPeople) {
        this.csPracticalOrderedUnoutStockPeople = csPracticalOrderedUnoutStockPeople;
    }

    public Double getCsPracticalOrderedUnoutStockItemNum() {
        return csPracticalOrderedUnoutStockItemNum;
    }

    public void setCsPracticalOrderedUnoutStockItemNum(Double csPracticalOrderedUnoutStockItemNum) {
        this.csPracticalOrderedUnoutStockItemNum = csPracticalOrderedUnoutStockItemNum;
    }

    public Double getCsPracticalOrderedUnoutStockAmount() {
        return csPracticalOrderedUnoutStockAmount;
    }

    public void setCsPracticalOrderedUnoutStockAmount(Double csPracticalOrderedUnoutStockAmount) {
        this.csPracticalOrderedUnoutStockAmount = csPracticalOrderedUnoutStockAmount;
    }

    public Double getCsPracticalOrderedUnoutStockOrderedNum() {
        return csPracticalOrderedUnoutStockOrderedNum;
    }

    public void setCsPracticalOrderedUnoutStockOrderedNum(Double csPracticalOrderedUnoutStockOrderedNum) {
        this.csPracticalOrderedUnoutStockOrderedNum = csPracticalOrderedUnoutStockOrderedNum;
    }

    public Double getShopPostFee() {
        return shopPostFee;
    }

    public void setShopPostFee(Double shopPostFee) {
        this.shopPostFee = shopPostFee;
    }

    public Double getSilencePostFee() {
        return silencePostFee;
    }

    public void setSilencePostFee(Double silencePostFee) {
        this.silencePostFee = silencePostFee;
    }

    public Double getCsPostFee() {
        return csPostFee;
    }

    public void setCsPostFee(Double csPostFee) {
        this.csPostFee = csPostFee;
    }

    public Double getProductEvaluationDSR() {
        return productEvaluationDSR;
    }

    public void setProductEvaluationDSR(Double productEvaluationDSR) {
        this.productEvaluationDSR = productEvaluationDSR;
    }

    public Double getServiceAttitudeDSR() {
        return serviceAttitudeDSR;
    }

    public void setServiceAttitudeDSR(Double serviceAttitudeDSR) {
        this.serviceAttitudeDSR = serviceAttitudeDSR;
    }

    public Double getLogisticsSpeedDSR() {
        return logisticsSpeedDSR;
    }

    public void setLogisticsSpeedDSR(Double logisticsSpeedDSR) {
        this.logisticsSpeedDSR = logisticsSpeedDSR;
    }

    public Integer getForwardInSessionNum() {
        return forwardInSessionNum;
    }

    public void setForwardInSessionNum(Integer forwardInSessionNum) {
        this.forwardInSessionNum = forwardInSessionNum;
    }

    public Integer getForwardOutSessionNum() {
        return forwardOutSessionNum;
    }

    public void setForwardOutSessionNum(Integer forwardOutSessionNum) {
        this.forwardOutSessionNum = forwardOutSessionNum;
    }
}
