package com.pes.jd.model.BO;

import com.pes.jd.model.DO.CsOrderBindDO;

public class CsOrderBindJudgeBO {

    private CsOrderBindDO csOrderBind;

    private Boolean csActiveFollowIn;

    private Integer silentFlag;

    private Integer urgepayFlag;
    //    全静默算绩效关闭的情况下顾客在有效时间内主动找客服聊天（且有效时间内有客服有回复），算客服销售额
    private Boolean isPesOrder = Boolean.FALSE;

    //全款支付
    private Boolean isAllPay = Boolean.FALSE;
    //插旗订单
    private Boolean isOrderFlag = Boolean.FALSE;

    private Integer bindPayType;
    public CsOrderBindJudgeBO() {
    }

    public CsOrderBindJudgeBO(CsOrderBindDO csOrderBind) {
        this.csOrderBind = csOrderBind;
    }

    public CsOrderBindJudgeBO(CsOrderBindDO csOrderBind, Boolean csActiveFollowIn) {
        this.csOrderBind = csOrderBind;
        this.csActiveFollowIn = csActiveFollowIn;
    }

    public CsOrderBindDO getCsOrderBind() {
        return csOrderBind;
    }

    public void setCsOrderBind(CsOrderBindDO csOrderBind) {
        this.csOrderBind = csOrderBind;
    }

    public Boolean getCsActiveFollowIn() {
        return csActiveFollowIn;
    }

    public void setCsActiveFollowIn(Boolean csActiveFollowIn) {
        this.csActiveFollowIn = csActiveFollowIn;
    }

    public Integer getSilentFlag() {
        return silentFlag;
    }

    public void setSilentFlag(Integer silentFlag) {
        this.silentFlag = silentFlag;
    }

    public Integer getUrgepayFlag() {
        return urgepayFlag;
    }

    public void setUrgepayFlag(Integer urgepayFlag) {
        this.urgepayFlag = urgepayFlag;
    }

    public Boolean getAllPay() {
        return isAllPay;
    }

    public void setAllPay(Boolean allPay) {
        isAllPay = allPay;
    }

    public Boolean getOrderFlag() {
        return isOrderFlag;
    }

    public void setOrderFlag(Boolean orderFlag) {
        isOrderFlag = orderFlag;
    }

    public Boolean getPesOrder() {
        return isPesOrder;
    }

    public void setPesOrder(Boolean pesOrder) {
        isPesOrder = pesOrder;
    }

    public Integer getBindPayType() {
        return bindPayType;
    }

    public void setBindPayType(Integer bindPayType) {
        this.bindPayType = bindPayType;
    }
}
