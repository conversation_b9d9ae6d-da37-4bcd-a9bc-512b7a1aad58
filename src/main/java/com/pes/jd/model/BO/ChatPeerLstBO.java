package com.pes.jd.model.BO;

import com.pes.jd.model.AO.ApiNumAO;
import com.pes.jd.model.DO.CsChatSessionDO;
import com.pes.jd.model.DO.CsChatpeerDO;

import java.util.List;

/**  
 * ClassName:ChatPeerBO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月31日 下午5:20:56 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ChatPeerLstBO extends ApiNumAO {

	private List<CsChatpeerDO> chatPeerLst;

	private List<CsChatSessionDO> csChatSessionLst; 
	public ChatPeerLstBO() {
		super();  
	}
	
	/**
	 * @param chatPeerLst
	 * @param csChatSessionLst
	 */
	public ChatPeerLstBO(List<CsChatpeerDO> chatPeerLst, List<CsChatSessionDO> csChatSessionLst,int num, int retryNum) {
		super(num, retryNum);  
		this.chatPeerLst = chatPeerLst;
		this.csChatSessionLst = csChatSessionLst;
	}

	public ChatPeerLstBO(List<CsChatpeerDO> chatPeerLst,int num, int retryNum) {
		super(num, retryNum);  
		this.chatPeerLst=chatPeerLst;  
	}
	public List<CsChatpeerDO> getChatPeerLst() {
		return chatPeerLst;
	}
	public void setChatPeerLst(List<CsChatpeerDO> chatPeerLst) {
		this.chatPeerLst = chatPeerLst;
	}
	public List<CsChatSessionDO> getCsChatSessionLst() {
		return csChatSessionLst;
	}
	public void setCsChatSessionLst(List<CsChatSessionDO> csChatSessionLst) {
		this.csChatSessionLst = csChatSessionLst;
	}

	

	
	
}
  
