package com.pes.jd.model.BO;

import java.util.Map;

public class LoginResultBO {
	private int retCode;
	private String msg;
	private String errCode;
	private String errMsg;
	private Map<String, Object> sessionMap;
	
	
	public LoginResultBO() {
		super();
	}
	public LoginResultBO(int retCode) {
		super();
		this.retCode = retCode;
	}
	public int getRetCode() {
		return retCode;
	}
	public void setRetCode(int retCode) {
		this.retCode = retCode;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public String getErrCode() {
		return errCode;
	}
	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}
	public String getErrMsg() {
		return errMsg;
	}
	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
	public Map<String, Object> getSessionMap() {
		return sessionMap;
	}
	public void setSessionMap(Map<String, Object> sessionMap) {
		this.sessionMap = sessionMap;
	}
	
}
  
