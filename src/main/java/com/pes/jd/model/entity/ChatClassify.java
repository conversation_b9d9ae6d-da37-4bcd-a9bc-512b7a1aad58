package com.pes.jd.model.entity;

import java.util.Date;

/**
 * Chat classification entity
 */
public class ChatClassify {
    private Long id;
    private Long shopId;
    private Date date;
    private String sid;
    private Long chatlogId;
    private String classify;
    private String classifyExtra;
    private String buyerContent;
    private String csContent;
    private Long skuId;
    private Integer totalTokens;
    private Date created;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    public Long getChatlogId() {
        return chatlogId;
    }

    public void setChatlogId(Long chatlogId) {
        this.chatlogId = chatlogId;
    }

    public String getClassify() {
        return classify;
    }

    public void setClassify(String classify) {
        this.classify = classify;
    }

    public String getClassifyExtra() {
        return classifyExtra;
    }

    public void setClassifyExtra(String classifyExtra) {
        this.classifyExtra = classifyExtra;
    }

    public String getBuyerContent() {
        return buyerContent;
    }

    public void setBuyerContent(String buyerContent) {
        this.buyerContent = buyerContent;
    }

    public String getCsContent() {
        return csContent;
    }

    public void setCsContent(String csContent) {
        this.csContent = csContent;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public Integer getTotalTokens() {
        return totalTokens;
    }

    public void setTotalTokens(Integer totalTokens) {
        this.totalTokens = totalTokens;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }
} 