package com.pes.jd.model.DO;

import com.google.common.collect.Sets;

import java.util.Date;
import java.util.Set;

public class ShopTeamPerformanceDataBO {
	
    private Long shopId;

    private Date date;

    private Set<String> directReceiveNumSet = Sets.newHashSet();

    private Set<String> forwardInNumSet = Sets.newHashSet();

    private Set<String> forwardOutNumSet = Sets.newHashSet();

    private Set<String> consultNumSet = Sets.newHashSet();

    private Set<String> receiveNumSet = Sets.newHashSet();

    private Set<String> enquiryNumSet = Sets.newHashSet();

    private Set<String> orderedNumTodaySet = Sets.newHashSet();

    private Integer orderedGoodsNumToday = 0;

    private Double orderedAmountToday = 0D;

    private Set<String> orderedNumFinalSet = Sets.newHashSet();

    private Integer orderedGoodsNumFinal = 0;

    private Double orderedAmountFinal  = 0D;

    private Set<String> paidNumTodaySet = Sets.newHashSet();

    private Double paidAmountToday = 0D;

    private Integer paidGoodsNumToday = 0;

    private Set<String> paidNumTodayNextSet = Sets.newHashSet();

    private Set<String> paidNumFinalSet = Sets.newHashSet();

    private Integer paidGoodsNumFinal = 0;

    private Double paidAmountFinal = 0D;

    private Set<String> outStockOrderBuyerNumFinalSet = Sets.newHashSet();

    private Integer outStockOrderNumFinal = 0;

    private Integer outStockOrderGoodsNumFinal = 0;

    private Double outStockOrderAmountFinal = 0D;

    public ShopTeamPerformanceDataBO() {
		super();
	}

	public ShopTeamPerformanceDataBO(Long shopId, Date date) {
		super();
		this.shopId = shopId;
		this.date = date;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Set<String> getDirectReceiveNumSet() {
		return directReceiveNumSet;
	}

	public void setDirectReceiveNumSet(Set<String> directReceiveNumSet) {
		this.directReceiveNumSet = directReceiveNumSet;
	}

	public Set<String> getForwardInNumSet() {
		return forwardInNumSet;
	}

	public void setForwardInNumSet(Set<String> forwardInNumSet) {
		this.forwardInNumSet = forwardInNumSet;
	}

	public Set<String> getForwardOutNumSet() {
		return forwardOutNumSet;
	}

	public void setForwardOutNumSet(Set<String> forwardOutNumSet) {
		this.forwardOutNumSet = forwardOutNumSet;
	}

	public Set<String> getConsultNumSet() {
		return consultNumSet;
	}

	public void setConsultNumSet(Set<String> consultNumSet) {
		this.consultNumSet = consultNumSet;
	}

	public Set<String> getReceiveNumSet() {
		return receiveNumSet;
	}

	public void setReceiveNumSet(Set<String> receiveNumSet) {
		this.receiveNumSet = receiveNumSet;
	}

	public Set<String> getEnquiryNumSet() {
		return enquiryNumSet;
	}

	public void setEnquiryNumSet(Set<String> enquiryNumSet) {
		this.enquiryNumSet = enquiryNumSet;
	}

	public Set<String> getOrderedNumTodaySet() {
		return orderedNumTodaySet;
	}

	public void setOrderedNumTodaySet(Set<String> orderedNumTodaySet) {
		this.orderedNumTodaySet = orderedNumTodaySet;
	}


	public Integer getOrderedGoodsNumToday() {
		return orderedGoodsNumToday;
	}

	public void setOrderedGoodsNumToday(Integer orderedGoodsNumToday) {
		this.orderedGoodsNumToday = orderedGoodsNumToday;
	}

	public Double getOrderedAmountToday() {
		return orderedAmountToday;
	}

	public void setOrderedAmountToday(Double orderedAmountToday) {
		this.orderedAmountToday = orderedAmountToday;
	}

	public Set<String> getOrderedNumFinalSet() {
		return orderedNumFinalSet;
	}

	public void setOrderedNumFinalSet(Set<String> orderedNumFinalSet) {
		this.orderedNumFinalSet = orderedNumFinalSet;
	}

	public Integer getOrderedGoodsNumFinal() {
		return orderedGoodsNumFinal;
	}

	public void setOrderedGoodsNumFinal(Integer orderedGoodsNumFinal) {
		this.orderedGoodsNumFinal = orderedGoodsNumFinal;
	}

	public Double getOrderedAmountFinal() {
		return orderedAmountFinal;
	}

	public void setOrderedAmountFinal(Double orderedAmountFinal) {
		this.orderedAmountFinal = orderedAmountFinal;
	}

	public Set<String> getPaidNumTodaySet() {
		return paidNumTodaySet;
	}

	public void setPaidNumTodaySet(Set<String> paidNumTodaySet) {
		this.paidNumTodaySet = paidNumTodaySet;
	}

	public Double getPaidAmountToday() {
		return paidAmountToday;
	}

	public void setPaidAmountToday(Double paidAmountToday) {
		this.paidAmountToday = paidAmountToday;
	}

	public Integer getPaidGoodsNumToday() {
		return paidGoodsNumToday;
	}

	public void setPaidGoodsNumToday(Integer paidGoodsNumToday) {
		this.paidGoodsNumToday = paidGoodsNumToday;
	}

	public Set<String> getPaidNumTodayNextSet() {
		return paidNumTodayNextSet;
	}

	public void setPaidNumTodayNextSet(Set<String> paidNumTodayNextSet) {
		this.paidNumTodayNextSet = paidNumTodayNextSet;
	}

	public Set<String> getPaidNumFinalSet() {
		return paidNumFinalSet;
	}

	public void setPaidNumFinalSet(Set<String> paidNumFinalSet) {
		this.paidNumFinalSet = paidNumFinalSet;
	}

	public Integer getPaidGoodsNumFinal() {
		return paidGoodsNumFinal;
	}

	public void setPaidGoodsNumFinal(Integer paidGoodsNumFinal) {
		this.paidGoodsNumFinal = paidGoodsNumFinal;
	}

	public Double getPaidAmountFinal() {
		return paidAmountFinal;
	}

	public void setPaidAmountFinal(Double paidAmountFinal) {
		this.paidAmountFinal = paidAmountFinal;
	}

	public Set<String> getOutStockOrderBuyerNumFinalSet() {
		return outStockOrderBuyerNumFinalSet;
	}

	public void setOutStockOrderBuyerNumFinalSet(Set<String> outStockOrderBuyerNumFinalSet) {
		this.outStockOrderBuyerNumFinalSet = outStockOrderBuyerNumFinalSet;
	}

	public Integer getOutStockOrderNumFinal() {
		return outStockOrderNumFinal;
	}

	public void setOutStockOrderNumFinal(Integer outStockOrderNumFinal) {
		this.outStockOrderNumFinal = outStockOrderNumFinal;
	}

	public Integer getOutStockOrderGoodsNumFinal() {
		return outStockOrderGoodsNumFinal;
	}

	public void setOutStockOrderGoodsNumFinal(Integer outStockOrderGoodsNumFinal) {
		this.outStockOrderGoodsNumFinal = outStockOrderGoodsNumFinal;
	}

	public Double getOutStockOrderAmountFinal() {
		return outStockOrderAmountFinal;
	}

	public void setOutStockOrderAmountFinal(Double outStockOrderAmountFinal) {
		this.outStockOrderAmountFinal = outStockOrderAmountFinal;
	}

}