package com.pes.jd.model.DO;


import java.util.Date;

public class ShopTeamSessionServiceIndexDO {

    private Long id;

    private Long shopId;

    private Date date;

    private Integer consultSessionNum;

    private Integer receiveSessionNum;

    private Double receiveSessionDurationTime;

    private Integer directReceiveSessionNum;

    private Integer forwardInSessionNum;

    private Integer forwardOutSessionNum;

    private Integer custConsultSessionNum;

    private Integer csToCustSessionNum;

    private Integer chatNum;

    private Integer custChatNum;

    private Integer csChatNum;

    private Integer csWordNum;

    private Double avgCsMsgSessionNum;

    private Integer maxReceiveSessionNum;

    private Integer nonReplySessionNum;

    private Integer leaveMsgSessionNum;

    private Integer leaveMsgReceiveSessionNum;

    private Integer slowRespSessionNum;

    private Integer longRespSessionNum;

    private Double avgRespTimeFirst;

    private Double avgRespTime;

    private Double avgSessionDurationTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getConsultSessionNum() {
        return consultSessionNum;
    }

    public void setConsultSessionNum(Integer consultSessionNum) {
        this.consultSessionNum = consultSessionNum;
    }

    public Integer getReceiveSessionNum() {
        return receiveSessionNum;
    }

    public void setReceiveSessionNum(Integer receiveSessionNum) {
        this.receiveSessionNum = receiveSessionNum;
    }

    public Double getReceiveSessionDurationTime() {
        return receiveSessionDurationTime;
    }

    public void setReceiveSessionDurationTime(Double receiveSessionDurationTime) {
        this.receiveSessionDurationTime = receiveSessionDurationTime;
    }

    public Integer getDirectReceiveSessionNum() {
        return directReceiveSessionNum;
    }

    public void setDirectReceiveSessionNum(Integer directReceiveSessionNum) {
        this.directReceiveSessionNum = directReceiveSessionNum;
    }

    public Integer getForwardInSessionNum() {
        return forwardInSessionNum;
    }

    public void setForwardInSessionNum(Integer forwardInSessionNum) {
        this.forwardInSessionNum = forwardInSessionNum;
    }

    public Integer getForwardOutSessionNum() {
        return forwardOutSessionNum;
    }

    public void setForwardOutSessionNum(Integer forwardOutSessionNum) {
        this.forwardOutSessionNum = forwardOutSessionNum;
    }

    public Integer getCustConsultSessionNum() {
        return custConsultSessionNum;
    }

    public void setCustConsultSessionNum(Integer custConsultSessionNum) {
        this.custConsultSessionNum = custConsultSessionNum;
    }

    public Integer getCsToCustSessionNum() {
        return csToCustSessionNum;
    }

    public void setCsToCustSessionNum(Integer csToCustSessionNum) {
        this.csToCustSessionNum = csToCustSessionNum;
    }

    public Integer getChatNum() {
        return chatNum;
    }

    public void setChatNum(Integer chatNum) {
        this.chatNum = chatNum;
    }

    public Integer getCustChatNum() {
        return custChatNum;
    }

    public void setCustChatNum(Integer custChatNum) {
        this.custChatNum = custChatNum;
    }

    public Integer getCsChatNum() {
        return csChatNum;
    }

    public void setCsChatNum(Integer csChatNum) {
        this.csChatNum = csChatNum;
    }

    public Integer getCsWordNum() {
        return csWordNum;
    }

    public void setCsWordNum(Integer csWordNum) {
        this.csWordNum = csWordNum;
    }

    public Double getAvgCsMsgSessionNum() {
        return avgCsMsgSessionNum;
    }

    public void setAvgCsMsgSessionNum(Double avgCsMsgSessionNum) {
        this.avgCsMsgSessionNum = avgCsMsgSessionNum;
    }

    public Integer getMaxReceiveSessionNum() {
        return maxReceiveSessionNum;
    }

    public void setMaxReceiveSessionNum(Integer maxReceiveSessionNum) {
        this.maxReceiveSessionNum = maxReceiveSessionNum;
    }

    public Integer getNonReplySessionNum() {
        return nonReplySessionNum;
    }

    public void setNonReplySessionNum(Integer nonReplySessionNum) {
        this.nonReplySessionNum = nonReplySessionNum;
    }

    public Integer getLeaveMsgSessionNum() {
        return leaveMsgSessionNum;
    }

    public void setLeaveMsgSessionNum(Integer leaveMsgSessionNum) {
        this.leaveMsgSessionNum = leaveMsgSessionNum;
    }

    public Integer getLeaveMsgReceiveSessionNum() {
        return leaveMsgReceiveSessionNum;
    }

    public void setLeaveMsgReceiveSessionNum(Integer leaveMsgReceiveSessionNum) {
        this.leaveMsgReceiveSessionNum = leaveMsgReceiveSessionNum;
    }

    public Integer getSlowRespSessionNum() {
        return slowRespSessionNum;
    }

    public void setSlowRespSessionNum(Integer slowRespSessionNum) {
        this.slowRespSessionNum = slowRespSessionNum;
    }

    public Integer getLongRespSessionNum() {
        return longRespSessionNum;
    }

    public void setLongRespSessionNum(Integer longRespSessionNum) {
        this.longRespSessionNum = longRespSessionNum;
    }

    public Double getAvgRespTimeFirst() {
        return avgRespTimeFirst;
    }

    public void setAvgRespTimeFirst(Double avgRespTimeFirst) {
        this.avgRespTimeFirst = avgRespTimeFirst;
    }

    public Double getAvgRespTime() {
        return avgRespTime;
    }

    public void setAvgRespTime(Double avgRespTime) {
        this.avgRespTime = avgRespTime;
    }

    public Double getAvgSessionDurationTime() {
        return avgSessionDurationTime;
    }

    public void setAvgSessionDurationTime(Double avgSessionDurationTime) {
        this.avgSessionDurationTime = avgSessionDurationTime;
    }
}
