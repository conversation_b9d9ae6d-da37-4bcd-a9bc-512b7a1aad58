package com.pes.jd.model.DO;

import com.google.common.collect.Sets;

import java.util.Date;
import java.util.Set;

public class ShopTeamTOrderPerformanceDataBO {

    private Long shopId;

    private Date date;

    private Set<String> toOrderedNumSet = Sets.newHashSet();

    private Integer toOrderedOrderNum = 0;

    private Integer toOrderedGoodsNum = 0;

    private Double toOrderedAmount = 0D;

    private Set<String> toOrderedPaidNumTodaySet =  Sets.newHashSet();

    private Integer toOrderedPaidOrderNumToday = 0;

    private Integer toOrderedPaidGoodsToday = 0;

    private Double toOrderedPaidAmountToday = 0D;

    private Set<String> toOrderedPaidNumFinalSet = Sets.newHashSet();

    private Integer toOrderedPaidOrderNumFinal = 0;

    private Integer toOrderedPaidGoodsFinal = 0;

    private Double toOrderedPaidAmountFinal = 0D;

    private Set<String> toOrderedOutStockNumSet =  Sets.newHashSet();

    private Double toOrderedOutStockAmount = 0D;

    private Integer toOrderedOutStockGoodsNum = 0;

    private Integer toOrderedOutStockOrderNum = 0;

    private Double saleAmount = 0D;

    private Integer saleOrderNum = 0;

    private Integer saleGoodsNum = 0;

    private Set<String> saleBuyerNumSet =  Sets.newHashSet();

    private Integer saleSkuNum = 0;

    private Double postFee = 0D;

    private Set<String> outStockNumSet =  Sets.newHashSet();

    private Double outStockAmount = 0D;

    private Integer outStockGoodsNum = 0;

    private Integer outStockOrderNum = 0;

    private Integer cfmGoodsOrderNum = 0;

    private Double cfmGoodsAmount = 0D;

    private Integer cfmGoodsNum = 0;

    private Set<String> cfmGoodsBuyerNumSet =  Sets.newHashSet();

    public ShopTeamTOrderPerformanceDataBO() {
		super();
	}

	public ShopTeamTOrderPerformanceDataBO(Long shopId, Date date) {
		super();
		this.shopId = shopId;
		this.date = date;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Set<String> getToOrderedNumSet() {
		return toOrderedNumSet;
	}

	public void setToOrderedNumSet(Set<String> toOrderedNumSet) {
		this.toOrderedNumSet = toOrderedNumSet;
	}

	public Integer getToOrderedOrderNum() {
		return toOrderedOrderNum;
	}

	public void setToOrderedOrderNum(Integer toOrderedOrderNum) {
		this.toOrderedOrderNum = toOrderedOrderNum;
	}

	public Integer getToOrderedGoodsNum() {
		return toOrderedGoodsNum;
	}

	public void setToOrderedGoodsNum(Integer toOrderedGoodsNum) {
		this.toOrderedGoodsNum = toOrderedGoodsNum;
	}

	public Double getToOrderedAmount() {
		return toOrderedAmount;
	}

	public void setToOrderedAmount(Double toOrderedAmount) {
		this.toOrderedAmount = toOrderedAmount;
	}

	public Set<String> getToOrderedPaidNumTodaySet() {
		return toOrderedPaidNumTodaySet;
	}

	public void setToOrderedPaidNumTodaySet(Set<String> toOrderedPaidNumTodaySet) {
		this.toOrderedPaidNumTodaySet = toOrderedPaidNumTodaySet;
	}

	public Integer getToOrderedPaidOrderNumToday() {
		return toOrderedPaidOrderNumToday;
	}

	public void setToOrderedPaidOrderNumToday(Integer toOrderedPaidOrderNumToday) {
		this.toOrderedPaidOrderNumToday = toOrderedPaidOrderNumToday;
	}

	public Integer getToOrderedPaidGoodsToday() {
		return toOrderedPaidGoodsToday;
	}

	public void setToOrderedPaidGoodsToday(Integer toOrderedPaidGoodsToday) {
		this.toOrderedPaidGoodsToday = toOrderedPaidGoodsToday;
	}

	public Double getToOrderedPaidAmountToday() {
		return toOrderedPaidAmountToday;
	}

	public void setToOrderedPaidAmountToday(Double toOrderedPaidAmountToday) {
		this.toOrderedPaidAmountToday = toOrderedPaidAmountToday;
	}

	public Set<String> getToOrderedPaidNumFinalSet() {
		return toOrderedPaidNumFinalSet;
	}

	public void setToOrderedPaidNumFinalSet(Set<String> toOrderedPaidNumFinalSet) {
		this.toOrderedPaidNumFinalSet = toOrderedPaidNumFinalSet;
	}

	public Integer getToOrderedPaidOrderNumFinal() {
		return toOrderedPaidOrderNumFinal;
	}

	public void setToOrderedPaidOrderNumFinal(Integer toOrderedPaidOrderNumFinal) {
		this.toOrderedPaidOrderNumFinal = toOrderedPaidOrderNumFinal;
	}

	public Integer getToOrderedPaidGoodsFinal() {
		return toOrderedPaidGoodsFinal;
	}

	public void setToOrderedPaidGoodsFinal(Integer toOrderedPaidGoodsFinal) {
		this.toOrderedPaidGoodsFinal = toOrderedPaidGoodsFinal;
	}

	public Double getToOrderedPaidAmountFinal() {
		return toOrderedPaidAmountFinal;
	}

	public void setToOrderedPaidAmountFinal(Double toOrderedPaidAmountFinal) {
		this.toOrderedPaidAmountFinal = toOrderedPaidAmountFinal;
	}

	public Set<String> getToOrderedOutStockNumSet() {
		return toOrderedOutStockNumSet;
	}

	public void setToOrderedOutStockNumSet(Set<String> toOrderedOutStockNumSet) {
		this.toOrderedOutStockNumSet = toOrderedOutStockNumSet;
	}

	public Double getToOrderedOutStockAmount() {
		return toOrderedOutStockAmount;
	}

	public void setToOrderedOutStockAmount(Double toOrderedOutStockAmount) {
		this.toOrderedOutStockAmount = toOrderedOutStockAmount;
	}

	public Integer getToOrderedOutStockGoodsNum() {
		return toOrderedOutStockGoodsNum;
	}

	public void setToOrderedOutStockGoodsNum(Integer toOrderedOutStockGoodsNum) {
		this.toOrderedOutStockGoodsNum = toOrderedOutStockGoodsNum;
	}

	public Integer getToOrderedOutStockOrderNum() {
		return toOrderedOutStockOrderNum;
	}

	public void setToOrderedOutStockOrderNum(Integer toOrderedOutStockOrderNum) {
		this.toOrderedOutStockOrderNum = toOrderedOutStockOrderNum;
	}

	public Double getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(Double saleAmount) {
		this.saleAmount = saleAmount;
	}

	public Integer getSaleOrderNum() {
		return saleOrderNum;
	}

	public void setSaleOrderNum(Integer saleOrderNum) {
		this.saleOrderNum = saleOrderNum;
	}

	public Integer getSaleGoodsNum() {
		return saleGoodsNum;
	}

	public void setSaleGoodsNum(Integer saleGoodsNum) {
		this.saleGoodsNum = saleGoodsNum;
	}

	public Set<String> getSaleBuyerNumSet() {
		return saleBuyerNumSet;
	}

	public void setSaleBuyerNumSet(Set<String> saleBuyerNumSet) {
		this.saleBuyerNumSet = saleBuyerNumSet;
	}

	public Integer getSaleSkuNum() {
		return saleSkuNum;
	}

	public void setSaleSkuNum(Integer saleSkuNum) {
		this.saleSkuNum = saleSkuNum;
	}

	public Double getPostFee() {
		return postFee;
	}

	public void setPostFee(Double postFee) {
		this.postFee = postFee;
	}

	public Set<String> getOutStockNumSet() {
		return outStockNumSet;
	}

	public void setOutStockNumSet(Set<String> outStockNumSet) {
		this.outStockNumSet = outStockNumSet;
	}

	public Double getOutStockAmount() {
		return outStockAmount;
	}

	public void setOutStockAmount(Double outStockAmount) {
		this.outStockAmount = outStockAmount;
	}

	public Integer getOutStockGoodsNum() {
		return outStockGoodsNum;
	}

	public void setOutStockGoodsNum(Integer outStockGoodsNum) {
		this.outStockGoodsNum = outStockGoodsNum;
	}

	public Integer getOutStockOrderNum() {
		return outStockOrderNum;
	}

	public void setOutStockOrderNum(Integer outStockOrderNum) {
		this.outStockOrderNum = outStockOrderNum;
	}

	public Integer getCfmGoodsOrderNum() {
		return cfmGoodsOrderNum;
	}

	public void setCfmGoodsOrderNum(Integer cfmGoodsOrderNum) {
		this.cfmGoodsOrderNum = cfmGoodsOrderNum;
	}

	public Double getCfmGoodsAmount() {
		return cfmGoodsAmount;
	}

	public void setCfmGoodsAmount(Double cfmGoodsAmount) {
		this.cfmGoodsAmount = cfmGoodsAmount;
	}

	public Integer getCfmGoodsNum() {
		return cfmGoodsNum;
	}

	public void setCfmGoodsNum(Integer cfmGoodsNum) {
		this.cfmGoodsNum = cfmGoodsNum;
	}

	public Set<String> getCfmGoodsBuyerNumSet() {
		return cfmGoodsBuyerNumSet;
	}

	public void setCfmGoodsBuyerNumSet(Set<String> cfmGoodsBuyerNumSet) {
		this.cfmGoodsBuyerNumSet = cfmGoodsBuyerNumSet;
	}


}