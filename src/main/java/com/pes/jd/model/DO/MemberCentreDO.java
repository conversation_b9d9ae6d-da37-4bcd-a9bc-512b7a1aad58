package com.pes.jd.model.DO;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class MemberCentreDO {

	private Long id;

	private Long shopId;

	private String shopTitle;

	private String userNick;

	private String contact;
	
	private String phoneNumber;

	private Integer position;

	private Date created;

	private String qq;

	private String email;

	private Date ejectTime;

}