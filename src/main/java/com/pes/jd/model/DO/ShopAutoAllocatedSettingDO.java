package com.pes.jd.model.DO;


import java.util.Date;

public class ShopAutoAllocatedSettingDO {

    private Long id;

    private Long shopId;

    private Date created;

    private Date modify;

    private Integer status;

    private Boolean isAutoAllocated;

    private Integer cnoFlag;

    private Long cnoGroupId;

    private String cnoCsNick;

    private Integer onpFlag;

    private Long onpGroupId;

    private String onpCsNick;

    private Integer snpFlag;

    private Long snpGroupId;

    private String snpCsNick;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModify() {
        return modify;
    }

    public void setModify(Date modify) {
        this.modify = modify;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getIsAutoAllocated() {
        return isAutoAllocated;
    }

    public void setIsAutoAllocated(Boolean isAutoAllocated) {
        this.isAutoAllocated = isAutoAllocated;
    }

    public Integer getCnoFlag() {
        return cnoFlag;
    }

    public void setCnoFlag(Integer cnoFlag) {
        this.cnoFlag = cnoFlag;
    }

    public Long getCnoGroupId() {
        return cnoGroupId;
    }

    public void setCnoGroupId(Long cnoGroupId) {
        this.cnoGroupId = cnoGroupId;
    }

    public String getCnoCsNick() {
        return cnoCsNick;
    }

    public void setCnoCsNick(String cnoCsNick) {
        this.cnoCsNick = cnoCsNick == null ? null : cnoCsNick.trim();
    }

    public Integer getOnpFlag() {
        return onpFlag;
    }

    public void setOnpFlag(Integer onpFlag) {
        this.onpFlag = onpFlag;
    }

    public Long getOnpGroupId() {
        return onpGroupId;
    }

    public void setOnpGroupId(Long onpGroupId) {
        this.onpGroupId = onpGroupId;
    }

    public String getOnpCsNick() {
        return onpCsNick;
    }

    public void setOnpCsNick(String onpCsNick) {
        this.onpCsNick = onpCsNick == null ? null : onpCsNick.trim();
    }

    public Integer getSnpFlag() {
        return snpFlag;
    }

    public void setSnpFlag(Integer snpFlag) {
        this.snpFlag = snpFlag;
    }

    public Long getSnpGroupId() {
        return snpGroupId;
    }

    public void setSnpGroupId(Long snpGroupId) {
        this.snpGroupId = snpGroupId;
    }

    public String getSnpCsNick() {
        return snpCsNick;
    }

    public void setSnpCsNick(String snpCsNick) {
        this.snpCsNick = snpCsNick == null ? null : snpCsNick.trim();
    }
}