package com.pes.jd.model.DO;

import java.util.Date;

public class JobCalRecordDO {
    private Long id;

    private Long shopId;

    private Date date;

    private Boolean result;

    private Boolean commonChatFlag;

    private Boolean receiveQualityFlag;

    private Boolean csOrderIndexFlag;

    private Boolean csOrderBindFlag;

    private Boolean enquiryChatFlag;

    private Boolean finalChatDataFlag;

    private Boolean enquiryLossFlag;

    private Boolean csPerformanceFlag;

    private Boolean csTorderPerformance;

    private Boolean shopDayOverviewFlag;

    private Boolean assitIndexFlag;

    private Boolean orderFilteFlag;

    private Boolean orderLossFlag;

    private Boolean outstockLossFlag;

    private Boolean teamLossFlag;

    private Boolean csOrderEvalFlag;

    private Boolean csOrderBindIndexFlag;

    private Boolean csGoodsHandleFlag;

    private Boolean csGoodsSumFlag;

    private Boolean csSilentGoodsSumFlag;

    private Boolean csSlientSaleFlag;

    private Boolean teamDayRefundFlag;

    private Boolean shopDayRefundFlag;

    private Date modified;//操作时间

    private String msg;//失败原因

    private Long consumeTime;//花费的时间:ms

    public JobCalRecordDO(Long shopId, Date date, Date modified) {
        this.shopId = shopId;
        this.date = date;
        this.modified = modified;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public JobCalRecordDO() {
    }

    public JobCalRecordDO(Long shopId, Date date) {
        this.shopId = shopId;
        this.date = date;
    }

    public Long getConsumeTime() {
        return consumeTime;
    }

    public void setConsumeTime(Long consumeTime) {
        this.consumeTime = consumeTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public Boolean getCommonChatFlag() {
        return commonChatFlag;
    }

    public void setCommonChatFlag(Boolean commonChatFlag) {
        this.commonChatFlag = commonChatFlag;
    }

    public Boolean getReceiveQualityFlag() {
        return receiveQualityFlag;
    }

    public void setReceiveQualityFlag(Boolean receiveQualityFlag) {
        this.receiveQualityFlag = receiveQualityFlag;
    }

    public Boolean getCsOrderIndexFlag() {
        return csOrderIndexFlag;
    }

    public void setCsOrderIndexFlag(Boolean csOrderIndexFlag) {
        this.csOrderIndexFlag = csOrderIndexFlag;
    }

    public Boolean getCsOrderBindFlag() {
        return csOrderBindFlag;
    }

    public void setCsOrderBindFlag(Boolean csOrderBindFlag) {
        this.csOrderBindFlag = csOrderBindFlag;
    }

    public Boolean getEnquiryChatFlag() {
        return enquiryChatFlag;
    }

    public void setEnquiryChatFlag(Boolean enquiryChatFlag) {
        this.enquiryChatFlag = enquiryChatFlag;
    }

    public Boolean getFinalChatDataFlag() {
        return finalChatDataFlag;
    }

    public void setFinalChatDataFlag(Boolean finalChatDataFlag) {
        this.finalChatDataFlag = finalChatDataFlag;
    }

    public Boolean getEnquiryLossFlag() {
        return enquiryLossFlag;
    }

    public void setEnquiryLossFlag(Boolean enquiryLossFlag) {
        this.enquiryLossFlag = enquiryLossFlag;
    }

    public Boolean getCsPerformanceFlag() {
        return csPerformanceFlag;
    }

    public void setCsPerformanceFlag(Boolean csPerformanceFlag) {
        this.csPerformanceFlag = csPerformanceFlag;
    }

    public Boolean getCsTorderPerformance() {
        return csTorderPerformance;
    }

    public void setCsTorderPerformance(Boolean csTorderPerformance) {
        this.csTorderPerformance = csTorderPerformance;
    }

    public Boolean getShopDayOverviewFlag() {
        return shopDayOverviewFlag;
    }

    public void setShopDayOverviewFlag(Boolean shopDayOverviewFlag) {
        this.shopDayOverviewFlag = shopDayOverviewFlag;
    }

    public Boolean getAssitIndexFlag() {
        return assitIndexFlag;
    }

    public void setAssitIndexFlag(Boolean assitIndexFlag) {
        this.assitIndexFlag = assitIndexFlag;
    }

    public Boolean getOrderFilteFlag() {
        return orderFilteFlag;
    }

    public void setOrderFilteFlag(Boolean orderFilteFlag) {
        this.orderFilteFlag = orderFilteFlag;
    }

    public Boolean getCsSlientSaleFlag() {
        return csSlientSaleFlag;
    }

    public void setCsSlientSaleFlag(Boolean csSlientSaleFlag) {
        this.csSlientSaleFlag = csSlientSaleFlag;
    }

    public Boolean getOrderLossFlag() {
        return orderLossFlag;
    }

    public void setOrderLossFlag(Boolean orderLossFlag) {
        this.orderLossFlag = orderLossFlag;
    }

    public Boolean getOutstockLossFlag() {
        return outstockLossFlag;
    }

    public void setOutstockLossFlag(Boolean outstockLossFlag) {
        this.outstockLossFlag = outstockLossFlag;
    }

    public Boolean getTeamLossFlag() {
        return teamLossFlag;
    }

    public void setTeamLossFlag(Boolean teamLossFlag) {
        this.teamLossFlag = teamLossFlag;
    }

    public Boolean getCsOrderEvalFlag() {
        return csOrderEvalFlag;
    }

    public void setCsOrderEvalFlag(Boolean csOrderEvalFlag) {
        this.csOrderEvalFlag = csOrderEvalFlag;
    }

    public Boolean getCsOrderBindIndexFlag() {
        return csOrderBindIndexFlag;
    }

    public void setCsOrderBindIndexFlag(Boolean csOrderBindIndexFlag) {
        this.csOrderBindIndexFlag = csOrderBindIndexFlag;
    }

    public Boolean getCsGoodsHandleFlag() {
        return csGoodsHandleFlag;
    }

    public void setCsGoodsHandleFlag(Boolean csGoodsHandleFlag) {
        this.csGoodsHandleFlag = csGoodsHandleFlag;
    }

    public Boolean getCsGoodsSumFlag() {
        return csGoodsSumFlag;
    }

    public void setCsGoodsSumFlag(Boolean csGoodsSumFlag) {
        this.csGoodsSumFlag = csGoodsSumFlag;
    }

    public Boolean getCsSilentGoodsSumFlag() {
        return csSilentGoodsSumFlag;
    }

    public void setCsSilentGoodsSumFlag(Boolean csSilentGoodsSumFlag) {
        this.csSilentGoodsSumFlag = csSilentGoodsSumFlag;
    }

    public Boolean getTeamDayRefundFlag() {
        return teamDayRefundFlag;
    }

    public void setTeamDayRefundFlag(Boolean teamDayRefundFlag) {
        this.teamDayRefundFlag = teamDayRefundFlag;
    }

    public Boolean getShopDayRefundFlag() {
        return shopDayRefundFlag;
    }

    public void setShopDayRefundFlag(Boolean shopDayRefundFlag) {
        this.shopDayRefundFlag = shopDayRefundFlag;
    }
}