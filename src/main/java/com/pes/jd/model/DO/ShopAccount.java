package com.pes.jd.model.DO;

import com.google.common.collect.Sets;

import java.util.Date;
import java.util.Set;


public class ShopAccount {
    private Long id;

    private Long sellerId;

    private String nick;

    private Long shopId;

    private String role;

    private Integer status;

    private String userName;
    
    private Boolean isAccount;
    
    private Date created;
    
    private Integer source;
    
    
    
    
    public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	private Date modified;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick == null ? null : nick.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role == null ? null : role.trim();
    }

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Boolean getIsAccount() {
		return isAccount;
	}

	public void setIsAccount(Boolean isAccount) {
		this.isAccount = isAccount;
	}
	
	
	
	 public Date getModified() {
		return modified;
	}

	public void setModified(Date modified) {
		this.modified = modified;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	@Override
	    public boolean equals(Object obj) {
	        if (obj == null)
	            return false;
	        if (this == obj)
	            return true;
	        if (obj instanceof ShopAccount) {
	            ShopAccount vo = (ShopAccount) obj;
	            // 比较每个属性的值 一致时才返回true
	            if (vo.nick.equals(this.nick) 
	            		&& vo.userName.equals(this.userName))
	                return true;
	        }
	        return false;
	    }
	    @Override
	    public int hashCode() {
	        return nick.hashCode() * userName.hashCode();
	    }
	@Override
	public String toString() {
		return "ShopAccount [id=" + id + ", sellerId=" + sellerId + ", nick=" + nick + ", shopId=" + shopId + ", role=" + role + ", status=" + status
				+ "]";
	}
  
	public static void main(String[] args) {
		ShopAccount sa=new ShopAccount();
		sa.setNick("s1");
		sa.setUserName("us1");
		sa.setStatus(1);
		ShopAccount sa2=new ShopAccount();
		sa2.setNick("s1");
		sa2.setUserName("us1");
		sa.setStatus(2);
		ShopAccount sa4=new ShopAccount();
		sa4.setNick("s2");
		sa4.setUserName("us1");
		sa.setStatus(2);
		Set<ShopAccount> sets=Sets.newHashSet();
		sets.add(sa);
		sets.add(sa2);
		sets.add(sa4);
		System.out.println(sets.size());
	}
}