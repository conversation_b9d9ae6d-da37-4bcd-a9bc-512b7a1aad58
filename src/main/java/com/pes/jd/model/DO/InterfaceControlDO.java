package com.pes.jd.model.DO;

public class InterfaceControlDO {
    private Long id;

    private Byte infType;

    private Boolean switchFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getInfType() {
        return infType;
    }

    public void setInfType(Byte infType) {
        this.infType = infType;
    }

    public Boolean getSwitchFlag() {
        return switchFlag;
    }

    public void setSwitchFlag(Boolean switchFlag) {
        this.switchFlag = switchFlag;
    }
}