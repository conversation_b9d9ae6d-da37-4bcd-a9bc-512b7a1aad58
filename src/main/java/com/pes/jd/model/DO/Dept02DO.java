package com.pes.jd.model.DO;

public class Dept02DO {

    /**
     * 二级部门ID
     */
    private Long deptId;

    /**
     * 二级部门名
     */
    private String deptName;

    /**
     * 类型(2)
     */
    private String type;

    /**
     *上级部门ID
     */
    private Long parentId;

    /**
     * 文件id
     */
    private Long filerId;

    public Long getFilerId() {
        return filerId;
    }

    public void setFilerId(Long filerId) {
        this.filerId = filerId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Override
    public String toString() {
        return "Dept02DO{" +
                "deptId=" + deptId +
                ", deptName='" + deptName + '\'' +
                ", type='" + type + '\'' +
                ", parentId=" + parentId +
                '}';
    }
}
