package com.pes.jd.model.DO;

public class CustomReportPropertyDO {
    private Long id;

    private Long propertyId;

    private Long customReportId;

    private Byte filterFlag;

    private String filterJson;

    private Byte status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Long getCustomReportId() {
        return customReportId;
    }

    public void setCustomReportId(Long customReportId) {
        this.customReportId = customReportId;
    }

    public Byte getFilterFlag() {
        return filterFlag;
    }

    public void setFilterFlag(Byte filterFlag) {
        this.filterFlag = filterFlag;
    }

    public String getFilterJson() {
        return filterJson;
    }

    public void setFilterJson(String filterJson) {
        this.filterJson = filterJson == null ? null : filterJson.trim();
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }
}