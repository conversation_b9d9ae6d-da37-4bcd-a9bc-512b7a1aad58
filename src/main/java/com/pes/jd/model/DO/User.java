package com.pes.jd.model.DO;

import java.io.Serializable;
import java.util.Date;

public class User implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1598614265970009046L;

	private Long id;

	private Long userId;

	private Long shopId;

	private String nick;

	private String showNick;
	
	private String sessionKey;

	private String type;

	private Date created;

	private Date subscribeDeadLine;

	private String itemCode;

	private String status;

	private Integer lockFlag;

	private Long level;

	private Date readMaessageTime;

	private Boolean multiShopSwitch;
	
	private Boolean mainAccount;

	private String openId;

	private Integer interfaceType;//1：简洁版  2：完整版
	private Date authDeadLine;//授权过期时间
	private String ip;

	private String refreshSessionKey;
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getNick() {
		return nick;
	}

	public void setNick(String nick) {
		this.nick = nick;
	}

	public String getSessionKey() {
		return sessionKey;
	}

	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey ;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type ;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public Date getSubscribeDeadLine() {
		return subscribeDeadLine;
	}

	public void setSubscribeDeadLine(Date subscribeDeadLine) {
		this.subscribeDeadLine = subscribeDeadLine;
	}

	public String getItemCode() {
		return itemCode;
	}

	public void setItemCode(String itemCode) {
		this.itemCode = itemCode ;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status == null ? null : status.trim();
	}

	public Integer getLockFlag() {
		return lockFlag;
	}

	public void setLockFlag(Integer lockFlag) {
		this.lockFlag = lockFlag;
	}

	public Long getLevel() {
		return level;
	}

	public void setLevel(Long level) {
		this.level = level;
	}

	public Date getReadMaessageTime() {
		return readMaessageTime;
	}

	public void setReadMaessageTime(Date readMaessageTime) {
		this.readMaessageTime = readMaessageTime;
	}


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Boolean getMultiShopSwitch() {
		return multiShopSwitch;
	}

	public void setMultiShopSwitch(Boolean multiShopSwitch) {
		this.multiShopSwitch = multiShopSwitch;
	}

	public Boolean getMainAccount() {
		return mainAccount;
	}

	public void setMainAccount(Boolean mainAccount) {
		this.mainAccount = mainAccount;
	}

	public String getShowNick() {
		return showNick;
	}

	public void setShowNick(String showNick) {
		this.showNick = showNick;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public Date getAuthDeadLine() {
		return authDeadLine;
	}

	public void setAuthDeadLine(Date authDeadLine) {
		this.authDeadLine = authDeadLine;
	}

	public Integer getInterfaceType() {
		return interfaceType;
	}

	public void setInterfaceType(Integer interfaceType) {
		this.interfaceType = interfaceType;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getRefreshSessionKey() {
		return refreshSessionKey;
	}

	public void setRefreshSessionKey(String refreshSessionKey) {
		this.refreshSessionKey = refreshSessionKey;
	}
}