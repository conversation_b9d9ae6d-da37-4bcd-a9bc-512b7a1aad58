package com.pes.jd.model.DO;

import lombok.Data;
import java.util.Date;

@Data
public class GoodsConsultSummaryDOV2 {

    private Long id;

    private Long shopId;

    private Date date;

    private Long skuId;

    private String csNick;

    private Integer receiveNum;

    private Integer enquiryNum;

    private Integer payNum;

    private Integer payGoodsNum;

    private Double payAmount;

    public GoodsConsultSummaryDOV2(Integer receiveNum, Integer enquiryNum, Integer payNum, Integer payGoodsNum, Double payAmount) {
        this.receiveNum = receiveNum;
        this.enquiryNum = enquiryNum;
        this.payNum = payNum;
        this.payGoodsNum = payGoodsNum;
        this.payAmount = payAmount;
    }
}