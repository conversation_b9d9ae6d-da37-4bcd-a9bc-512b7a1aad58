package com.pes.jd.model.DO;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class ShopPerformancePreordainDO {

    private Long id;
    private Long shopId;
    private Date date;
    private Long skuId;
    private String skuName;
    private String activityId;

    private Integer orderedBuyerNum;
    private Integer orderedSkuNum;
    private Integer orderedPaidBuyerNum;
    private Integer orderedPaidSkuNum;
    private Double orderedPaidAmount;


    public ShopPerformancePreordainDO(Long shopId, Date date, Long skuId, String activityId) {
        this.shopId = shopId;
        this.date = date;
        this.skuId = skuId;
        this.activityId = activityId;


        orderedBuyerNum = 0;
        orderedSkuNum = 0;
        orderedPaidBuyerNum = 0;
        orderedPaidSkuNum = 0;
        orderedPaidAmount = 0.0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ShopPerformancePreordainDO that = (ShopPerformancePreordainDO) o;
        return shopId.equals(that.shopId) &&
                date.equals(that.date) &&
                skuId.equals(that.skuId) &&
                activityId.equals(that.activityId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(shopId, date, skuId, activityId);
    }
}