package com.pes.jd.model.DO;

import java.util.Date;

public class OrderDetailDO {
	private Long id;

	private Long orderId;

	private String itemSkuId;

	private Double itemPrice;

	private Integer itemNum;

	private String itemSkuName;

	private Date created;

	private String buyerNick;

	private Long wareId;

	private Date payTime;

	private Date outStockTime;
	
	private Double sellerDiscount;
	
	private Double totalFee;
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getItemSkuId() {
		return itemSkuId;
	}

	public void setItemSkuId(String itemSkuId) {
		this.itemSkuId = itemSkuId;
	}

	public Double getItemPrice() {
		return itemPrice;
	}

	public void setItemPrice(Double itemPrice) {
		this.itemPrice = itemPrice;
	}

	public Integer getItemNum() {
		return itemNum;
	}

	public void setItemNum(Integer itemNum) {
		this.itemNum = itemNum;
	}

	public String getItemSkuName() {
		return itemSkuName;
	}

	public void setItemSkuName(String itemSkuName) {
		this.itemSkuName = itemSkuName == null ? null : itemSkuName.trim();
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}

	public Date getPayTime() {
		return payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public Long getWareId() {
		return wareId;
	}

	public void setWareId(Long wareId) {
		this.wareId = wareId;

	}

	public Date getOutStockTime() {
		return outStockTime;
	}

	public void setOutStockTime(Date outStockTime) {
		this.outStockTime = outStockTime;
	}

	public Double getSellerDiscount() {
		return sellerDiscount;
	}

	public void setSellerDiscount(Double sellerDiscount) {
		this.sellerDiscount = sellerDiscount;
	}

	public Double getTotalFee() {
		return totalFee;
	}

	public void setTotalFee(Double totalFee) {
		this.totalFee = totalFee;
	}

	
}