package com.pes.jd.model.DO;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

import com.google.common.collect.Sets;
import com.pes.jd.util.DateUtils;


public class CsChatpeerDO implements Serializable{
	private static final long serialVersionUID = -8354606205798392238L;

	private Long id;

	private Long shopId;

	private String csNick;

	private String buyerNick;

	private Date date;

	private Integer chatFlag;

	private Integer forwardFlag;
	
	private Integer forwardFilteFlag;

	private Boolean isWatchwordBuyer;

	private Boolean isFilteredBuyer;

	private Boolean isCsSingleChatFilter;

	private Boolean isConsult;

	private Boolean isReceive;

	private Boolean isEnquiry;

	private Boolean isCsConsultFirst;

	private Boolean isPes;

	private Boolean isTeamPes;

	private Boolean isNextDayPes;

	private Boolean isAssist;

	private Boolean isAfterSale;

	private Boolean isOrderCreated;
	
	private Boolean isCrossChatFilter;
	
	private Integer buyerChatNum;
	
	private Date firstChatDate;
	
	private Date lastChatDate;
	
	public CsChatpeerDO() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick == null ? null : csNick.trim();
	}

	public String getBuyerNick() {
		return buyerNick;
	}

	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick == null ? null : buyerNick.trim();
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Integer getChatFlag() {
		return chatFlag;
	}

	public void setChatFlag(Integer chatFlag) {
		this.chatFlag = chatFlag;
	}

	public Integer getForwardFlag() {
		return forwardFlag;
	}

	public void setForwardFlag(Integer forwardFlag) {
		this.forwardFlag = forwardFlag;
	}

	public Integer getForwardFilteFlag() {
		return forwardFilteFlag;
	}

	public void setForwardFilteFlag(Integer forwardFilteFlag) {
		this.forwardFilteFlag = forwardFilteFlag;
	}

	public Boolean getIsWatchwordBuyer() {
		return isWatchwordBuyer;
	}

	public void setIsWatchwordBuyer(Boolean isWatchwordBuyer) {
		this.isWatchwordBuyer = isWatchwordBuyer;
	}

	public Boolean getIsFilteredBuyer() {
		return isFilteredBuyer;
	}

	public void setIsFilteredBuyer(Boolean isFilteredBuyer) {
		this.isFilteredBuyer = isFilteredBuyer;
	}

	public Boolean getIsCsSingleChatFilter() {
		return isCsSingleChatFilter;
	}

	public void setIsCsSingleChatFilter(Boolean isCsSingleChatFilter) {
		this.isCsSingleChatFilter = isCsSingleChatFilter;
	}

	public Boolean getIsConsult() {
		return isConsult;
	}

	public void setIsConsult(Boolean isConsult) {
		this.isConsult = isConsult;
	}

	public Boolean getIsReceive() {
		return isReceive;
	}

	public void setIsReceive(Boolean isReceive) {
		this.isReceive = isReceive;
	}

	public Boolean getIsEnquiry() {
		return isEnquiry;
	}

	public void setIsEnquiry(Boolean isEnquiry) {
		this.isEnquiry = isEnquiry;
	}

	public Boolean getIsCsConsultFirst() {
		return isCsConsultFirst;
	}

	public void setIsCsConsultFirst(Boolean isCsConsultFirst) {
		this.isCsConsultFirst = isCsConsultFirst;
	}

	public Boolean getIsPes() {
		return isPes;
	}

	public void setIsPes(Boolean isPes) {
		this.isPes = isPes;
	}

	public Boolean getIsTeamPes() {
		return isTeamPes;
	}

	public void setIsTeamPes(Boolean isTeamPes) {
		this.isTeamPes = isTeamPes;
	}

	public Boolean getIsNextDayPes() {
		return isNextDayPes;
	}

	public void setIsNextDayPes(Boolean isNextDayPes) {
		this.isNextDayPes = isNextDayPes;
	}

	public Boolean getIsAssist() {
		return isAssist;
	}

	public void setIsAssist(Boolean isAssist) {
		this.isAssist = isAssist;
	}

	public Boolean getIsAfterSale() {
		return isAfterSale;
	}

	public void setIsAfterSale(Boolean isAfterSale) {
		this.isAfterSale = isAfterSale;
	}

	public Boolean getIsOrderCreated() {
		return isOrderCreated;
	}

	public void setIsOrderCreated(Boolean isOrderCreated) {
		this.isOrderCreated = isOrderCreated;
	}

	public Integer getBuyerChatNum() {
		return buyerChatNum;
	}

	public void setBuyerChatNum(Integer buyerChatNum) {
		this.buyerChatNum = buyerChatNum;
	}

	public Date getFirstChatDate() {
		return firstChatDate;
	}

	public void setFirstChatDate(Date firstChatDate) {
		this.firstChatDate = firstChatDate;
	}

	public Date getLastChatDate() {
		return lastChatDate;
	}

	public void setLastChatDate(Date lastChatDate) {
		this.lastChatDate = lastChatDate;
	}

	public Boolean getIsCrossChatFilter() {
		return isCrossChatFilter;
	}

	public void setIsCrossChatFilter(Boolean isCrossChatFilter) {
		this.isCrossChatFilter = isCrossChatFilter;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((buyerNick == null) ? 0 : buyerNick.hashCode());
		result = prime * result + ((csNick == null) ? 0 : csNick.hashCode());
		result = prime * result + ((date == null) ? 0 : date.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		CsChatpeerDO other = (CsChatpeerDO) obj;
		if (buyerNick == null) {
			if (other.buyerNick != null)
				return false;
		} else if (!buyerNick.equals(other.buyerNick))
			return false;
		if (csNick == null) {
			if (other.csNick != null)
				return false;
		} else if (!csNick.equals(other.csNick))
			return false;
		if (date == null) {
			if (other.date != null)
				return false;
		} else if (!date.equals(other.date))
			return false;
		return true;
	}

	/**
	 * @param shopId
	 * @param csNick
	 */
	public CsChatpeerDO(Long shopId, String csNick) {
		super();
		this.shopId = shopId;
		this.csNick = csNick;
	}
	
	@Override
	public String toString() {
		return "CsChatpeerDO [shopId=" + shopId + ", csNick=" + csNick + ", buyerNick=" + buyerNick + ", date=" + date
				+ ", isCrossChatFilter=" + isCrossChatFilter + "]";
	}


	
}