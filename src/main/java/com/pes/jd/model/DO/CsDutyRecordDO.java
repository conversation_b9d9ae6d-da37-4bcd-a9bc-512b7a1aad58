package com.pes.jd.model.DO;

import java.util.Date;

public class CsDutyRecordDO {

    private Long id;
    private Long shopId;
    private String csNick;
    private Date date;
    private Date firstOnlineDateTime;
    private Date lastOfflineDateTime;
    private Integer loginTimesNum;
    private Long loginDurationTime;
    private Long rceiveDurationTime;
    private Long hangupDurationTime;
    private Long offlineDurationTime;
    private Integer suspendNum;//挂起次数

    public CsDutyRecordDO() {
        super();
    }

    public CsDutyRecordDO(Long shopId, String csNick, Date date) {
        this.shopId = shopId;
        this.csNick = csNick;
        this.date = date;
        this.loginTimesNum = 0;
        this.loginDurationTime = 0L;
        this.rceiveDurationTime = 0L;
        this.hangupDurationTime = 0L;
        this.offlineDurationTime = 0L;
        this.suspendNum = 0;
    }


    public CsDutyRecordDO(Long shopId, String csNick, Date date, Date firstOnlineDateTime, Date lastOfflineDateTime, Integer loginTimesNum, Long loginDurationTime, Long rceiveDurationTime, Long hangupDurationTime, Long offlineDurationTime, Integer suspendNum) {
        this.shopId = shopId;
        this.csNick = csNick;
        this.date = date;
        this.firstOnlineDateTime = firstOnlineDateTime;
        this.lastOfflineDateTime = lastOfflineDateTime;
        this.loginTimesNum = loginTimesNum;
        this.loginDurationTime = loginDurationTime;
        this.rceiveDurationTime = rceiveDurationTime;
        this.hangupDurationTime = hangupDurationTime;
        this.offlineDurationTime = offlineDurationTime;
        this.suspendNum = suspendNum;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getFirstOnlineDateTime() {
        return firstOnlineDateTime;
    }

    public void setFirstOnlineDateTime(Date firstOnlineDateTime) {
        this.firstOnlineDateTime = firstOnlineDateTime;
    }

    public Date getLastOfflineDateTime() {
        return lastOfflineDateTime;
    }

    public void setLastOfflineDateTime(Date lastOfflineDateTime) {
        this.lastOfflineDateTime = lastOfflineDateTime;
    }

    public Integer getLoginTimesNum() {
        return loginTimesNum;
    }

    public void setLoginTimesNum(Integer loginTimesNum) {
        this.loginTimesNum = loginTimesNum;
    }

    public Long getLoginDurationTime() {
        return loginDurationTime;
    }

    public void setLoginDurationTime(Long loginDurationTime) {
        this.loginDurationTime = loginDurationTime;
    }

    public Long getRceiveDurationTime() {
        return rceiveDurationTime;
    }

    public void setRceiveDurationTime(Long rceiveDurationTime) {
        this.rceiveDurationTime = rceiveDurationTime;
    }

    public Long getHangupDurationTime() {
        return hangupDurationTime;
    }

    public void setHangupDurationTime(Long hangupDurationTime) {
        this.hangupDurationTime = hangupDurationTime;
    }

    public Long getOfflineDurationTime() {
        return offlineDurationTime;
    }

    public void setOfflineDurationTime(Long offlineDurationTime) {
        this.offlineDurationTime = offlineDurationTime;
    }

    public Integer getSuspendNum() {
        return suspendNum;
    }

    public void setSuspendNum(Integer suspendNum) {
        this.suspendNum = suspendNum;
    }
}
