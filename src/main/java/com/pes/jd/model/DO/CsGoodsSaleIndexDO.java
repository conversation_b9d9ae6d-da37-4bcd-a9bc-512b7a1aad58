package com.pes.jd.model.DO;

import java.util.Date;

public class CsGoodsSaleIndexDO {
    private Long id;

    private Long shopId;

    private Date date;

    private Long skuId;

    private Integer purchaseBuyerNum;

    private String csNick;

    private Integer saleGoodsNum;

    private Double saleAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }


    public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	public Integer getPurchaseBuyerNum() {
        return purchaseBuyerNum;
    }

    public void setPurchaseBuyerNum(Integer purchaseBuyerNum) {
        this.purchaseBuyerNum = purchaseBuyerNum;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Integer getSaleGoodsNum() {
        return saleGoodsNum;
    }

    public void setSaleGoodsNum(Integer saleGoodsNum) {
        this.saleGoodsNum = saleGoodsNum;
    }

    public Double getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(Double saleAmount) {
        this.saleAmount = saleAmount;
    }
}