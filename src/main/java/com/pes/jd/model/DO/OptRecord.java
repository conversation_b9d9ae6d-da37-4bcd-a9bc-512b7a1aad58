package com.pes.jd.model.DO;

import java.util.Date;

public class OptRecord {
    private Long id;

    private Long shopId;

    private String optNick;

    private String optName;

    private String reqUri;

    private Date optTime;

    private String type;

    private String optType;

    private String content;

    
    public OptRecord() {
		super();  
	}

	public OptRecord(Long shopId, String optName) {
		super();
		this.shopId = shopId;
		this.optName = optName;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getOptNick() {
        return optNick;
    }

    public void setOptNick(String optNick) {
        this.optNick = optNick == null ? null : optNick.trim();
    }

    public String getOptName() {
        return optName;
    }

    public void setOptName(String optName) {
        this.optName = optName == null ? null : optName.trim();
    }

    public String getReqUri() {
        return reqUri;
    }

    public void setReqUri(String reqUri) {
        this.reqUri = reqUri == null ? null : reqUri.trim();
    }

    public Date getOptTime() {
        return optTime;
    }

    public void setOptTime(Date optTime) {
        this.optTime = optTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getOptType() {
        return optType;
    }

    public void setOptType(String optType) {
        this.optType = optType == null ? null : optType.trim();
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
}