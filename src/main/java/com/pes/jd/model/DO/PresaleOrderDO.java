package com.pes.jd.model.DO;

import java.util.Date;

public class PresaleOrderDO {
	private Long id;
	private Long shopId;
	private String buyerNick;
	private Long presaleId;
	private Long skuId;
	private String skuName;
	private Integer goodsNum;
	private Long orderId;
	private Double yushouPrice;
	private Double freight;//运费
	private Integer orderPayType;
	private Integer orderType;
	private Integer orderStatus;
	private Date createTime;
	private Date updateTime;
	private Double payBargainReal;
	private Double payBargainPlan;
	private Date bargainTime;
	private Double payBalanceReal;
	private Double payBalancePlan;
	private Date balanceTime;
	private Date balanceStartTime;
	private Date balanceEndTime;
	private Integer yn;
	private Date orderTime;
	private Date balanceEndTimePlan;
	private Long companyId;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public String getBuyerNick() {
		return buyerNick;
	}
	public void setBuyerNick(String buyerNick) {
		this.buyerNick = buyerNick;
	}
	public Long getPresaleId() {
		return presaleId;
	}
	public void setPresaleId(Long presaleId) {
		this.presaleId = presaleId;
	}
	public Long getSkuId() {
		return skuId;
	}
	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}
	public String getSkuName() {
		return skuName;
	}
	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}
	public Integer getGoodsNum() {
		return goodsNum;
	}
	public void setGoodsNum(Integer goodsNum) {
		this.goodsNum = goodsNum;
	}
	public Long getOrderId() {
		return orderId;
	}
	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}
	public Double getYushouPrice() {
		return yushouPrice;
	}
	public void setYushouPrice(Double yushouPrice) {
		this.yushouPrice = yushouPrice;
	}
	public Double getFreight() {
		return freight;
	}
	public void setFreight(Double freight) {
		this.freight = freight;
	}
	public Integer getOrderPayType() {
		return orderPayType;
	}
	public void setOrderPayType(Integer orderPayType) {
		this.orderPayType = orderPayType;
	}
	public Integer getOrderType() {
		return orderType;
	}
	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}
	public Integer getOrderStatus() {
		return orderStatus;
	}
	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Double getPayBargainReal() {
		return payBargainReal;
	}
	public void setPayBargainReal(Double payBargainReal) {
		this.payBargainReal = payBargainReal;
	}
	public Double getPayBargainPlan() {
		return payBargainPlan;
	}
	public void setPayBargainPlan(Double payBargainPlan) {
		this.payBargainPlan = payBargainPlan;
	}
	public Date getBargainTime() {
		return bargainTime;
	}
	public void setBargainTime(Date bargainTime) {
		this.bargainTime = bargainTime;
	}
	public Double getPayBalanceReal() {
		return payBalanceReal;
	}
	public void setPayBalanceReal(Double payBalanceReal) {
		this.payBalanceReal = payBalanceReal;
	}
	public Double getPayBalancePlan() {
		return payBalancePlan;
	}
	public void setPayBalancePlan(Double payBalancePlan) {
		this.payBalancePlan = payBalancePlan;
	}
	public Date getBalanceTime() {
		return balanceTime;
	}
	public void setBalanceTime(Date balanceTime) {
		this.balanceTime = balanceTime;
	}
	public Date getBalanceStartTime() {
		return balanceStartTime;
	}
	public void setBalanceStartTime(Date balanceStartTime) {
		this.balanceStartTime = balanceStartTime;
	}
	public Date getBalanceEndTime() {
		return balanceEndTime;
	}
	public void setBalanceEndTime(Date balanceEndTime) {
		this.balanceEndTime = balanceEndTime;
	}
	public Integer getYn() {
		return yn;
	}
	public void setYn(Integer yn) {
		this.yn = yn;
	}
	public Date getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}
	public Date getBalanceEndTimePlan() {
		return balanceEndTimePlan;
	}
	public void setBalanceEndTimePlan(Date balanceEndTimePlan) {
		this.balanceEndTimePlan = balanceEndTimePlan;
	}
	public Long getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Long companyId) {
		this.companyId = companyId;
	}
}
  
