package com.pes.jd.model.DO;

public class UserVersionControlDO {
    private Long id;

    private Long appVersionId;

    private Long pesUserId;

    private Byte status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Long getAppVersionId() {
        return appVersionId;
    }

    public void setAppVersionId(Long appVersionId) {
        this.appVersionId = appVersionId;
    }

    public Long getPesUserId() {
        return pesUserId;
    }

    public void setPesUserId(Long pesUserId) {
        this.pesUserId = pesUserId;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }
}