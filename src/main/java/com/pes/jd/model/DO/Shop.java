package com.pes.jd.model.DO;

import java.util.Date;

public class Shop {
    private Long id;

    private Long shopId;

    private Long userId;

    private String sellerNick;

    private String title;

    private String sessionKey;

    private String status;

    private Integer subuserNum;

    private Boolean realtimeSwitch;

    private Integer fetchFlag;

    private Integer initDataFlag;

    private Date previousGetDataTime;

    private Date preFetchRealtime;

    private Long lastConsumedTime;

    private String schemaId;

    private String db;
    
    private Integer createTableFlag;//是否创建表
    private Long venderId;

    private Integer colType;
    private String rtSchemaId;
    private String rtDb;
    private Date subscribeDeadLine;
    private Integer type; //0:pop 1:自营

    private String refreshSessionKey;

    private String optionSessionKey;
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick == null ? null : sellerNick.trim();
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getSessionKey() {
        return sessionKey;
    }

    public void setSessionKey(String sessionKey) {
        this.sessionKey = sessionKey == null ? null : sessionKey.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }


	public Integer getSubuserNum() {
		return subuserNum;
	}

	public void setSubuserNum(Integer subuserNum) {
		this.subuserNum = subuserNum;
	}

	public Boolean getRealtimeSwitch() {
        return realtimeSwitch;
    }

    public void setRealtimeSwitch(Boolean realtimeSwitch) {
        this.realtimeSwitch = realtimeSwitch;
    }

    public Integer getFetchFlag() {
        return fetchFlag;
    }

    public void setFetchFlag(Integer fetchFlag) {
        this.fetchFlag = fetchFlag;
    }

    public Integer getInitDataFlag() {
        return initDataFlag;
    }

    public void setInitDataFlag(Integer initDataFlag) {
        this.initDataFlag = initDataFlag;
    }

    public Date getPreviousGetDataTime() {
        return previousGetDataTime;
    }

    public void setPreviousGetDataTime(Date previousGetDataTime) {
        this.previousGetDataTime = previousGetDataTime;
    }

    public Date getPreFetchRealtime() {
        return preFetchRealtime;
    }

    public void setPreFetchRealtime(Date preFetchRealtime) {
        this.preFetchRealtime = preFetchRealtime;
    }

    public Long getLastConsumedTime() {
        return lastConsumedTime;
    }

    public void setLastConsumedTime(Long lastConsumedTime) {
        this.lastConsumedTime = lastConsumedTime;
    }

   

    public String getSchemaId() {
		return schemaId;
	}

	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}

	public String getDb() {
        return db;
    }

    public void setDb(String db) {
        this.db = db == null ? null : db.trim();
    }

	public Integer getCreateTableFlag() {
		return createTableFlag;
	}

	public void setCreateTableFlag(Integer createTableFlag) {
		this.createTableFlag = createTableFlag;
	}

	public Integer getColType() {
		return colType;
	}

	public void setColType(Integer colType) {
		this.colType = colType;
	}

	public Long getVenderId() {
		return venderId;
	}

	public void setVenderId(Long venderId) {
		this.venderId = venderId;
	}

	public String getRtSchemaId() {
		return rtSchemaId;
	}

	public void setRtSchemaId(String rtSchemaId) {
		this.rtSchemaId = rtSchemaId;
	}

	public String getRtDb() {
		return rtDb;
	}

	public void setRtDb(String rtDb) {
		this.rtDb = rtDb;
	}

    public Date getSubscribeDeadLine() {
        return subscribeDeadLine;
    }

    public void setSubscribeDeadLine(Date subscribeDeadLine) {
        this.subscribeDeadLine = subscribeDeadLine;
    }

    public String getRefreshSessionKey() {
        return refreshSessionKey;
    }

    public void setRefreshSessionKey(String refreshSessionKey) {
        this.refreshSessionKey = refreshSessionKey;
    }

    public String getOptionSessionKey() {
        return optionSessionKey;
    }

    public void setOptionSessionKey(String optionSessionKey) {
        this.optionSessionKey = optionSessionKey;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}