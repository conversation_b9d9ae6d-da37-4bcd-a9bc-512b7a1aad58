package com.pes.jd.model.DO;

import java.util.Date;

public class CsAssitIndexDO {
    private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private Integer assitOrderCreateNum;

    private Double assitOrderCreateAmount;

    private Integer assitOrderPayNum;

    private Double assitOrderPayAmount;

    private Integer assitOrderFollowupNum;

    private Double assitOrderFollowupAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public Integer getAssitOrderCreateNum() {
        return assitOrderCreateNum;
    }

    public void setAssitOrderCreateNum(Integer assitOrderCreateNum) {
        this.assitOrderCreateNum = assitOrderCreateNum;
    }

    public Double getAssitOrderCreateAmount() {
        return assitOrderCreateAmount;
    }

    public void setAssitOrderCreateAmount(Double assitOrderCreateAmount) {
        this.assitOrderCreateAmount = assitOrderCreateAmount;
    }

    public Integer getAssitOrderPayNum() {
        return assitOrderPayNum;
    }

    public void setAssitOrderPayNum(Integer assitOrderPayNum) {
        this.assitOrderPayNum = assitOrderPayNum;
    }

    public Double getAssitOrderPayAmount() {
        return assitOrderPayAmount;
    }

    public void setAssitOrderPayAmount(Double assitOrderPayAmount) {
        this.assitOrderPayAmount = assitOrderPayAmount;
    }

    public Integer getAssitOrderFollowupNum() {
        return assitOrderFollowupNum;
    }

    public void setAssitOrderFollowupNum(Integer assitOrderFollowupNum) {
        this.assitOrderFollowupNum = assitOrderFollowupNum;
    }

    public Double getAssitOrderFollowupAmount() {
        return assitOrderFollowupAmount;
    }

    public void setAssitOrderFollowupAmount(Double assitOrderFollowupAmount) {
        this.assitOrderFollowupAmount = assitOrderFollowupAmount;
    }
}