package com.pes.jd.model.DO;

import java.util.Objects;

public class PesUserMenuPermission {
    private Long id;

    private Long userId;

    private Long menuResourceId;

    private String csNick;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMenuResourceId() {
        return menuResourceId;
    }

    public void setMenuResourceId(Long menuResourceId) {
        this.menuResourceId = menuResourceId;
    }

    public String getCsNick() {
        return csNick;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PesUserMenuPermission that = (PesUserMenuPermission) o;
        return Objects.equals(menuResourceId, that.menuResourceId) &&
                Objects.equals(csNick, that.csNick);
    }

    @Override
    public int hashCode() {
        return Objects.hash(menuResourceId, csNick);
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();


    }
}