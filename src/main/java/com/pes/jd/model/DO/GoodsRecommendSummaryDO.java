package com.pes.jd.model.DO;

import java.util.Date;

public class GoodsRecommendSummaryDO {
    private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private Long skuId;
    
    private Integer recommendNum;

    private Integer purchasesBuyerNum;

    private Integer purchasesGoodsNum;

    private Double purchasesAmount;
    
    
	public GoodsRecommendSummaryDO() {
		super();
	}


    /**
	 * @param recommendNum
	 * @param purchasesBuyerNum
	 */
	public GoodsRecommendSummaryDO(Integer recommendNum, Integer purchasesBuyerNum) {
		super();
		this.recommendNum = recommendNum;
		this.purchasesBuyerNum = purchasesBuyerNum;
	}


	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
		return csNick;
	}

	public void setCsNick(String csNick) {
		this.csNick = csNick;
	}

	public Integer getRecommendNum() {
        return recommendNum;
    }

    public void setRecommendNum(Integer recommendNum) {
        this.recommendNum = recommendNum;
    }

    public Integer getPurchasesBuyerNum() {
        return purchasesBuyerNum;
    }

    public void setPurchasesBuyerNum(Integer purchasesBuyerNum) {
        this.purchasesBuyerNum = purchasesBuyerNum;
    }

    public Integer getPurchasesGoodsNum() {
        return purchasesGoodsNum;
    }

    public void setPurchasesGoodsNum(Integer purchasesGoodsNum) {
        this.purchasesGoodsNum = purchasesGoodsNum;
    }

    public Double getPurchasesAmount() {
        return purchasesAmount;
    }

    public void setPurchasesAmount(Double purchasesAmount) {
        this.purchasesAmount = purchasesAmount;
    }

	public Long getSkuId() {
		return skuId;
	}

	public void setSkuId(Long skuId) {
		this.skuId = skuId;
	}

	
    
}