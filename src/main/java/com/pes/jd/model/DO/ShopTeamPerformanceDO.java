package com.pes.jd.model.DO;

import java.util.Date;

public class ShopTeamPerformanceDO {
    private Long id;

    private Long shopId;

    private Date date;

    private Integer directReceiveNum;

    private Integer forwardInNum;

    private Integer forwardOutNum;

    private Integer consultNum;

    private Integer receiveNum;

    private Integer enquiryNum;

    private Integer orderedNumToday;

    private Integer orderedGoodsNumToday;

    private Double orderedAmountToday;

    private Integer orderedNumFinal;

    private Integer orderedGoodsNumFinal;

    private Double orderedAmountFinal;

    private Integer paidNumToday;

    private Double paidAmountToday;

    private Integer paidGoodsNumToday;

    private Integer paidNumTodayNext;

    private Integer paidNumFinal;

    private Integer paidGoodsNumFinal;

    private Double paidAmountFinal;

    private Integer outStockOrderBuyerNumFinal;

    private Integer outStockOrderNumFinal;

    private Integer outStockOrderGoodsNumFinal;

    private Double outStockOrderAmountFinal;

    public ShopTeamPerformanceDO() {
		super();
	}
    
	public ShopTeamPerformanceDO(Long shopId, Date date) {
		super();
		this.shopId = shopId;
		this.date = date;
	}

	public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getDirectReceiveNum() {
        return directReceiveNum;
    }

    public void setDirectReceiveNum(Integer directReceiveNum) {
        this.directReceiveNum = directReceiveNum;
    }

    public Integer getForwardInNum() {
        return forwardInNum;
    }

    public void setForwardInNum(Integer forwardInNum) {
        this.forwardInNum = forwardInNum;
    }

    public Integer getForwardOutNum() {
        return forwardOutNum;
    }

    public void setForwardOutNum(Integer forwardOutNum) {
        this.forwardOutNum = forwardOutNum;
    }

    public Integer getConsultNum() {
        return consultNum;
    }

    public void setConsultNum(Integer consultNum) {
        this.consultNum = consultNum;
    }

    public Integer getReceiveNum() {
        return receiveNum;
    }

    public void setReceiveNum(Integer receiveNum) {
        this.receiveNum = receiveNum;
    }

    public Integer getEnquiryNum() {
        return enquiryNum;
    }

    public void setEnquiryNum(Integer enquiryNum) {
        this.enquiryNum = enquiryNum;
    }

    public Integer getOrderedNumToday() {
        return orderedNumToday;
    }

    public void setOrderedNumToday(Integer orderedNumToday) {
        this.orderedNumToday = orderedNumToday;
    }

    public Integer getOrderedGoodsNumToday() {
        return orderedGoodsNumToday;
    }

    public void setOrderedGoodsNumToday(Integer orderedGoodsNumToday) {
        this.orderedGoodsNumToday = orderedGoodsNumToday;
    }

    public Double getOrderedAmountToday() {
        return orderedAmountToday;
    }

    public void setOrderedAmountToday(Double orderedAmountToday) {
        this.orderedAmountToday = orderedAmountToday;
    }

    public Integer getOrderedNumFinal() {
        return orderedNumFinal;
    }

    public void setOrderedNumFinal(Integer orderedNumFinal) {
        this.orderedNumFinal = orderedNumFinal;
    }

    public Integer getOrderedGoodsNumFinal() {
        return orderedGoodsNumFinal;
    }

    public void setOrderedGoodsNumFinal(Integer orderedGoodsNumFinal) {
        this.orderedGoodsNumFinal = orderedGoodsNumFinal;
    }

    public Double getOrderedAmountFinal() {
        return orderedAmountFinal;
    }

    public void setOrderedAmountFinal(Double orderedAmountFinal) {
        this.orderedAmountFinal = orderedAmountFinal;
    }

    public Integer getPaidNumToday() {
        return paidNumToday;
    }

    public void setPaidNumToday(Integer paidNumToday) {
        this.paidNumToday = paidNumToday;
    }

    public Double getPaidAmountToday() {
        return paidAmountToday;
    }

    public void setPaidAmountToday(Double paidAmountToday) {
        this.paidAmountToday = paidAmountToday;
    }

    public Integer getPaidGoodsNumToday() {
        return paidGoodsNumToday;
    }

    public void setPaidGoodsNumToday(Integer paidGoodsNumToday) {
        this.paidGoodsNumToday = paidGoodsNumToday;
    }

    public Integer getPaidNumTodayNext() {
        return paidNumTodayNext;
    }

    public void setPaidNumTodayNext(Integer paidNumTodayNext) {
        this.paidNumTodayNext = paidNumTodayNext;
    }

    public Integer getPaidNumFinal() {
        return paidNumFinal;
    }

    public void setPaidNumFinal(Integer paidNumFinal) {
        this.paidNumFinal = paidNumFinal;
    }

    public Integer getPaidGoodsNumFinal() {
        return paidGoodsNumFinal;
    }

    public void setPaidGoodsNumFinal(Integer paidGoodsNumFinal) {
        this.paidGoodsNumFinal = paidGoodsNumFinal;
    }

    public Double getPaidAmountFinal() {
        return paidAmountFinal;
    }

    public void setPaidAmountFinal(Double paidAmountFinal) {
        this.paidAmountFinal = paidAmountFinal;
    }

    public Integer getOutStockOrderBuyerNumFinal() {
        return outStockOrderBuyerNumFinal;
    }

    public void setOutStockOrderBuyerNumFinal(Integer outStockOrderBuyerNumFinal) {
        this.outStockOrderBuyerNumFinal = outStockOrderBuyerNumFinal;
    }

    public Integer getOutStockOrderNumFinal() {
        return outStockOrderNumFinal;
    }

    public void setOutStockOrderNumFinal(Integer outStockOrderNumFinal) {
        this.outStockOrderNumFinal = outStockOrderNumFinal;
    }

    public Integer getOutStockOrderGoodsNumFinal() {
        return outStockOrderGoodsNumFinal;
    }

    public void setOutStockOrderGoodsNumFinal(Integer outStockOrderGoodsNumFinal) {
        this.outStockOrderGoodsNumFinal = outStockOrderGoodsNumFinal;
    }

    public Double getOutStockOrderAmountFinal() {
        return outStockOrderAmountFinal;
    }

    public void setOutStockOrderAmountFinal(Double outStockOrderAmountFinal) {
        this.outStockOrderAmountFinal = outStockOrderAmountFinal;
    }
}