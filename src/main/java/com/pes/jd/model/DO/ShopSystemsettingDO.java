package com.pes.jd.model.DO;

public class ShopSystemsettingDO {
	private Long id;

	private Long shopId;

	private Boolean autoReplySwitch;

	private String autoReplyMark;

	private Boolean aftersellAcountFilter;

	private Integer sellAfter;

	private Integer maxWaitTime;

	private Integer slowResponseTime;

	private Integer slowResponseTimesNum;

	private Integer quickResponseTime;

	private Integer longReceptionTime;

	private Integer schedulingTimeDot;

	private Boolean csRecommendSwitch;

	private String csRecommendMark;

	private String dutyRecordExportUnit;

	private Boolean valueServiceSaleAmoutSwitch;

	private Boolean valueServiceGoodsNumSwitch;

	private Integer judgeRuleAscription;

	private Integer judgeRule;

	private Integer enquiryValidDurationTime;

	private Integer outStockValidDurationTime;

	private Boolean silentAllSwitch;

	private Integer silentAllFollowUpTime;

	private Boolean isBindSilentAllOrder;

	private Boolean activeFollowUpSwitch;

	private Integer custFirstReplyDay;

	private Boolean silentUrgepaySwitch;

	private Integer silentUrgepayTime;

	private Boolean orderFlagSwitch;

	private Long orderFlag;

	private Boolean enquiryLossSwitch;

	private Integer minReplyNum;

	private Boolean teamChatFilteSwitch;

	private Boolean sysCsChatFilteSwitch;

	private Boolean nonChatFilteSwitch;

	private Boolean csOfflineFilteCustMsgSwitch;

	private Boolean csForwardSwitch;

	private Integer csForwardNum;

	private String jixiaoTime;

	private Boolean csWatchwordSwitch;

	private String csWatchword;

	private Integer csWatchwordSendTimesNum;

	private Boolean mainAccountAutoReplySwitch;

	private String mainAccountAutoReplyContent;

	private Boolean csToCsutFirstLostSwitch;

	private Boolean custSigchatSwitch;

	private Integer sigchatMinReplyNum;

	private Boolean csSingleChatFilter;

	private Boolean platformCsFilteSwitch;

	private Integer custSingleChatNum;

	private Boolean custChatWordSwitch;

	private String custWatchword;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public Boolean getAutoReplySwitch() {
		return autoReplySwitch;
	}

	public void setAutoReplySwitch(Boolean autoReplySwitch) {
		this.autoReplySwitch = autoReplySwitch;
	}

	public String getAutoReplyMark() {
		return autoReplyMark;
	}

	public void setAutoReplyMark(String autoReplyMark) {
		this.autoReplyMark = autoReplyMark == null ? null : autoReplyMark.trim();
	}

	public Boolean getAftersellAcountFilter() {
		return aftersellAcountFilter;
	}

	public void setAftersellAcountFilter(Boolean aftersellAcountFilter) {
		this.aftersellAcountFilter = aftersellAcountFilter;
	}

	public Integer getSellAfter() {
		return sellAfter;
	}

	public void setSellAfter(Integer sellAfter) {
		this.sellAfter = sellAfter;
	}

	public Integer getMaxWaitTime() {
		return maxWaitTime;
	}

	public void setMaxWaitTime(Integer maxWaitTime) {
		this.maxWaitTime = maxWaitTime;
	}

	public Integer getSlowResponseTime() {
		return slowResponseTime;
	}

	public void setSlowResponseTime(Integer slowResponseTime) {
		this.slowResponseTime = slowResponseTime;
	}

	public Integer getSlowResponseTimesNum() {
		return slowResponseTimesNum;
	}

	public void setSlowResponseTimesNum(Integer slowResponseTimesNum) {
		this.slowResponseTimesNum = slowResponseTimesNum;
	}

	public Integer getQuickResponseTime() {
		return quickResponseTime;
	}

	public void setQuickResponseTime(Integer quickResponseTime) {
		this.quickResponseTime = quickResponseTime;
	}

	public Integer getLongReceptionTime() {
		return longReceptionTime;
	}

	public void setLongReceptionTime(Integer longReceptionTime) {
		this.longReceptionTime = longReceptionTime;
	}

	public Integer getSchedulingTimeDot() {
		return schedulingTimeDot;
	}

	public void setSchedulingTimeDot(Integer schedulingTimeDot) {
		this.schedulingTimeDot = schedulingTimeDot;
	}

	public Boolean getCsRecommendSwitch() {
		return csRecommendSwitch;
	}

	public void setCsRecommendSwitch(Boolean csRecommendSwitch) {
		this.csRecommendSwitch = csRecommendSwitch;
	}

	public String getCsRecommendMark() {
		return csRecommendMark;
	}

	public void setCsRecommendMark(String csRecommendMark) {
		this.csRecommendMark = csRecommendMark == null ? null : csRecommendMark.trim();
	}

	public String getDutyRecordExportUnit() {
		return dutyRecordExportUnit;
	}

	public void setDutyRecordExportUnit(String dutyRecordExportUnit) {
		this.dutyRecordExportUnit = dutyRecordExportUnit == null ? null : dutyRecordExportUnit.trim();
	}

	public Boolean getValueServiceSaleAmoutSwitch() {
		return valueServiceSaleAmoutSwitch;
	}

	public void setValueServiceSaleAmoutSwitch(Boolean valueServiceSaleAmoutSwitch) {
		this.valueServiceSaleAmoutSwitch = valueServiceSaleAmoutSwitch;
	}

	public Boolean getValueServiceGoodsNumSwitch() {
		return valueServiceGoodsNumSwitch;
	}

	public void setValueServiceGoodsNumSwitch(Boolean valueServiceGoodsNumSwitch) {
		this.valueServiceGoodsNumSwitch = valueServiceGoodsNumSwitch;
	}

	public Integer getJudgeRuleAscription() {
		return judgeRuleAscription;
	}

	public void setJudgeRuleAscription(Integer judgeRuleAscription) {
		this.judgeRuleAscription = judgeRuleAscription;
	}

	public Integer getJudgeRule() {
		return judgeRule;
	}

	public void setJudgeRule(Integer judgeRule) {
		this.judgeRule = judgeRule;
	}

	public Integer getEnquiryValidDurationTime() {
		return enquiryValidDurationTime;
	}

	public void setEnquiryValidDurationTime(Integer enquiryValidDurationTime) {
		this.enquiryValidDurationTime = enquiryValidDurationTime;
	}

	public Integer getOutStockValidDurationTime() {
		return outStockValidDurationTime;
	}

	public void setOutStockValidDurationTime(Integer outStockValidDurationTime) {
		this.outStockValidDurationTime = outStockValidDurationTime;
	}

	public Boolean getSilentAllSwitch() {
		return silentAllSwitch;
	}

	public void setSilentAllSwitch(Boolean silentAllSwitch) {
		this.silentAllSwitch = silentAllSwitch;
	}

	public Integer getSilentAllFollowUpTime() {
		return silentAllFollowUpTime;
	}

	public void setSilentAllFollowUpTime(Integer silentAllFollowUpTime) {
		this.silentAllFollowUpTime = silentAllFollowUpTime;
	}

	public Boolean getIsBindSilentAllOrder() {
		return isBindSilentAllOrder;
	}

	public void setIsBindSilentAllOrder(Boolean isBindSilentAllOrder) {
		this.isBindSilentAllOrder = isBindSilentAllOrder;
	}

	public Boolean getActiveFollowUpSwitch() {
		return activeFollowUpSwitch;
	}

	public void setActiveFollowUpSwitch(Boolean activeFollowUpSwitch) {
		this.activeFollowUpSwitch = activeFollowUpSwitch;
	}

	public Integer getCustFirstReplyDay() {
		return custFirstReplyDay;
	}

	public void setCustFirstReplyDay(Integer custFirstReplyDay) {
		this.custFirstReplyDay = custFirstReplyDay;
	}

	public Boolean getSilentUrgepaySwitch() {
		return silentUrgepaySwitch;
	}

	public void setSilentUrgepaySwitch(Boolean silentUrgepaySwitch) {
		this.silentUrgepaySwitch = silentUrgepaySwitch;
	}

	public Integer getSilentUrgepayTime() {
		return silentUrgepayTime;
	}

	public void setSilentUrgepayTime(Integer silentUrgepayTime) {
		this.silentUrgepayTime = silentUrgepayTime;
	}

	public Boolean getOrderFlagSwitch() {
		return orderFlagSwitch;
	}

	public void setOrderFlagSwitch(Boolean orderFlagSwitch) {
		this.orderFlagSwitch = orderFlagSwitch;
	}

	public Long getOrderFlag() {
		return orderFlag;
	}

	public void setOrderFlag(Long orderFlag) {
		this.orderFlag = orderFlag;
	}

	public Boolean getEnquiryLossSwitch() {
		return enquiryLossSwitch;
	}

	public void setEnquiryLossSwitch(Boolean enquiryLossSwitch) {
		this.enquiryLossSwitch = enquiryLossSwitch;
	}

	public Integer getMinReplyNum() {
		return minReplyNum;
	}

	public void setMinReplyNum(Integer minReplyNum) {
		this.minReplyNum = minReplyNum;
	}

	public Boolean getTeamChatFilteSwitch() {
		return teamChatFilteSwitch;
	}

	public void setTeamChatFilteSwitch(Boolean teamChatFilteSwitch) {
		this.teamChatFilteSwitch = teamChatFilteSwitch;
	}

	public Boolean getSysCsChatFilteSwitch() {
		return sysCsChatFilteSwitch;
	}

	public void setSysCsChatFilteSwitch(Boolean sysCsChatFilteSwitch) {
		this.sysCsChatFilteSwitch = sysCsChatFilteSwitch;
	}

	public Boolean getNonChatFilteSwitch() {
		return nonChatFilteSwitch;
	}

	public void setNonChatFilteSwitch(Boolean nonChatFilteSwitch) {
		this.nonChatFilteSwitch = nonChatFilteSwitch;
	}

	public Boolean getCsOfflineFilteCustMsgSwitch() {
		return csOfflineFilteCustMsgSwitch;
	}

	public void setCsOfflineFilteCustMsgSwitch(Boolean csOfflineFilteCustMsgSwitch) {
		this.csOfflineFilteCustMsgSwitch = csOfflineFilteCustMsgSwitch;
	}

	public Boolean getCsForwardSwitch() {
		return csForwardSwitch;
	}

	public void setCsForwardSwitch(Boolean csForwardSwitch) {
		this.csForwardSwitch = csForwardSwitch;
	}

	public Integer getCsForwardNum() {
		return csForwardNum;
	}

	public void setCsForwardNum(Integer csForwardNum) {
		this.csForwardNum = csForwardNum;
	}

	public String getJixiaoTime() {
		return jixiaoTime;
	}

	public void setJixiaoTime(String jixiaoTime) {
		this.jixiaoTime = jixiaoTime == null ? null : jixiaoTime.trim();
	}

	public Boolean getCsWatchwordSwitch() {
		return csWatchwordSwitch;
	}

	public void setCsWatchwordSwitch(Boolean csWatchwordSwitch) {
		this.csWatchwordSwitch = csWatchwordSwitch;
	}

	public String getCsWatchword() {
		return csWatchword;
	}

	public void setCsWatchword(String csWatchword) {
		this.csWatchword = csWatchword == null ? null : csWatchword.trim();
	}

	public Integer getCsWatchwordSendTimesNum() {
		return csWatchwordSendTimesNum;
	}

	public void setCsWatchwordSendTimesNum(Integer csWatchwordSendTimesNum) {
		this.csWatchwordSendTimesNum = csWatchwordSendTimesNum;
	}

	public Boolean getMainAccountAutoReplySwitch() {
		return mainAccountAutoReplySwitch;
	}

	public void setMainAccountAutoReplySwitch(Boolean mainAccountAutoReplySwitch) {
		this.mainAccountAutoReplySwitch = mainAccountAutoReplySwitch;
	}

	public String getMainAccountAutoReplyContent() {
		return mainAccountAutoReplyContent;
	}

	public void setMainAccountAutoReplyContent(String mainAccountAutoReplyContent) {
		this.mainAccountAutoReplyContent = mainAccountAutoReplyContent == null ? null
				: mainAccountAutoReplyContent.trim();
	}

	public Boolean getCsToCsutFirstLostSwitch() {
		return csToCsutFirstLostSwitch;
	}

	public void setCsToCsutFirstLostSwitch(Boolean csToCsutFirstLostSwitch) {
		this.csToCsutFirstLostSwitch = csToCsutFirstLostSwitch;
	}

	public Boolean getCustSigchatSwitch() {
		return custSigchatSwitch;
	}

	public void setCustSigchatSwitch(Boolean custSigchatSwitch) {
		this.custSigchatSwitch = custSigchatSwitch;
	}

	public Integer getSigchatMinReplyNum() {
		return sigchatMinReplyNum;
	}

	public void setSigchatMinReplyNum(Integer sigchatMinReplyNum) {
		this.sigchatMinReplyNum = sigchatMinReplyNum;
	}

	public Boolean getCsSingleChatFilter() {
		return csSingleChatFilter;
	}

	public void setCsSingleChatFilter(Boolean csSingleChatFilter) {
		this.csSingleChatFilter = csSingleChatFilter;
	}

	public Boolean getPlatformCsFilteSwitch() {
		return platformCsFilteSwitch;
	}

	public void setPlatformCsFilteSwitch(Boolean platformCsFilteSwitch) {
		this.platformCsFilteSwitch = platformCsFilteSwitch;
	}

	public Integer getCustSingleChatNum() {
		return custSingleChatNum;
	}

	public void setCustSingleChatNum(Integer custSingleChatNum) {
		this.custSingleChatNum = custSingleChatNum;
	}

	public Boolean getCustChatWordSwitch() {
		return custChatWordSwitch;
	}

	public void setCustChatWordSwitch(Boolean custChatWordSwitch) {
		this.custChatWordSwitch = custChatWordSwitch;
	}

	public String getCustWatchword() {
		return custWatchword;
	}

	public void setCustWatchword(String custWatchword) {
		this.custWatchword = custWatchword == null ? null : custWatchword.trim();
	}
}