package com.pes.jd.model.DO;

import java.util.Date;

public class CsOrderBindDO {
    private Long id;

    private Long shopId;

    private Date date;

    private String csNick;

    private String buyerNick;

    private Long orderId;

    private Date orderCreated;

    private Date orderPayDate;

    private Double orderPayment;

    private Integer orderGoodsNum;

    private Double orderPostFee;

    private Boolean type;

    private Boolean isPesOrder;

    private Boolean silentFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getCsNick() {
        return csNick;
    }

    public void setCsNick(String csNick) {
        this.csNick = csNick == null ? null : csNick.trim();
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick == null ? null : buyerNick.trim();
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Date getOrderCreated() {
        return orderCreated;
    }

    public void setOrderCreated(Date orderCreated) {
        this.orderCreated = orderCreated;
    }

    public Date getOrderPayDate() {
        return orderPayDate;
    }

    public void setOrderPayDate(Date orderPayDate) {
        this.orderPayDate = orderPayDate;
    }

    public Double getOrderPayment() {
        return orderPayment;
    }

    public void setOrderPayment(Double orderPayment) {
        this.orderPayment = orderPayment;
    }

    public Integer getOrderGoodsNum() {
        return orderGoodsNum;
    }

    public void setOrderGoodsNum(Integer orderGoodsNum) {
        this.orderGoodsNum = orderGoodsNum;
    }

    public Double getOrderPostFee() {
        return orderPostFee;
    }

    public void setOrderPostFee(Double orderPostFee) {
        this.orderPostFee = orderPostFee;
    }

    public Boolean getType() {
        return type;
    }

    public void setType(Boolean type) {
        this.type = type;
    }

    public Boolean getIsPesOrder() {
        return isPesOrder;
    }

    public void setIsPesOrder(Boolean isPesOrder) {
        this.isPesOrder = isPesOrder;
    }

    public Boolean getSilentFlag() {
        return silentFlag;
    }

    public void setSilentFlag(Boolean silentFlag) {
        this.silentFlag = silentFlag;
    }
}