package com.pes.jd.model.DO;

import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class CsPerformancePreordainDO {

    private Long id;
    private Long shopId;
    private Date date;
    private String csNick;
    private Long skuId;
    private String skuName;
    private String activityId;
    private Integer consultBuyerNum;
    private Integer enquiryBuyerNum;
    private Integer enquiryOrderedBuyerNum;
    private Integer enquiryOrderedSkuNum;
    private Integer enquiryOrderedPaidBuyerNum;
    private Integer enquiryOrderedPaidSkuNum;
    private Double enquiryOrderedPaidAmount;

    private Integer orderedBuyerNum;
    private Integer orderedSkuNum;
    private Integer orderedPaidBuyerNum;
    private Integer orderedPaidSkuNum;
    private Double orderedPaidAmount;


    public CsPerformancePreordainDO(Long shopId, Date date, String csNick, Long skuId, String activityId) {
        this.shopId = shopId;
        this.date = date;
        this.csNick = csNick;
        this.skuId = skuId;
        this.activityId = activityId;
        consultBuyerNum = 0;
        enquiryBuyerNum = 0;
        enquiryOrderedBuyerNum = 0;
        enquiryOrderedSkuNum = 0;
        enquiryOrderedPaidBuyerNum = 0;
        enquiryOrderedPaidSkuNum = 0;
        enquiryOrderedPaidAmount = 0.0;
        orderedBuyerNum = 0;
        orderedSkuNum = 0;
        orderedPaidBuyerNum = 0;
        orderedPaidSkuNum = 0;
        orderedPaidAmount = 0.0;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CsPerformancePreordainDO that = (CsPerformancePreordainDO) o;
        return shopId.equals(that.shopId) &&
                date.equals(that.date) &&
                csNick.equals(that.csNick) &&
                skuId.equals(that.skuId) &&
                activityId.equals(that.activityId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(shopId, date, csNick, skuId, activityId);
    }
}
