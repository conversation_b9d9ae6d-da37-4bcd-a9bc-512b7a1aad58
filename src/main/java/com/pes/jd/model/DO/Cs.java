package com.pes.jd.model.DO;

import java.io.Serializable;
import java.util.Date;

public class Cs implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = -4469955460870528385L;

	private String nick;

    private Long shopId;

    private Integer type;

    private String simpleName;

    private Integer csStatus;
    
    private Date modifiedDate;
    
    private Date lockTime;
    
    private Integer source;
    
    
    
    public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public Cs() {
		super();
	}

	public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick == null ? null : nick.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName ;
    }

	public Integer getCsStatus() {
		return csStatus;
	}

	public void setCsStatus(Integer csStatus) {
		this.csStatus = csStatus;
	}

	public Date getModifiedDate() {
		return modifiedDate;
	}

	public void setModifiedDate(Date modifiedDate) {
		this.modifiedDate = modifiedDate;
	}

	public Date getLockTime() {
		return lockTime;
	}

	public void setLockTime(Date lockTime) {
		this.lockTime = lockTime;
	}
    
}