package com.pes.jd.model.DO;

import java.io.Serializable;

public class Cs implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = -4469955460870528385L;

	private String nick;

    private Long shopId;

    private Integer type;

    private String simpleName;

    
    public Cs() {
		super();
	}

	public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick == null ? null : nick.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName == null ? null : simpleName.trim();
    }
}