package com.pes.jd.model.DO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户画像AI分析报告表
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPortraitAiReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 报告生成日期
     */
    private LocalDate reportDate;

    /**
     * 报告类型：1-日报，2-周报，3-月报
     */
    private Integer reportType;

    /**
     * 图表类型：1-年龄分布，2-性别分布，3-婚姻状况，4-职业分布，5-用户群体类型，6-孩子数量，7-所在地区信息，8-大促预售购买敏感人群占比，9-平台促销敏感人群占比，10-大促高消费金额人群，11-新品偏好人群占比
     */
    private Integer chartType;

    /**
     * AI生成的完整报告内容
     */
    private String reportContent;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 报告类型枚举
     */
    public enum ReportType {
        DAILY(1, "日报"),
        WEEKLY(2, "周报"),
        MONTHLY(3, "月报");

        private final Integer code;
        private final String desc;

        ReportType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ReportType getByCode(Integer code) {
            for (ReportType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 图表类型枚举
     */
    public enum ChartType {
        AGE_DISTRIBUTION(1, "年龄分布"),
        GENDER_DISTRIBUTION(2, "性别分布"),
        MARRIAGE_STATUS(3, "婚姻状况"),
        PROFESSION_DISTRIBUTION(4, "职业分布"),
        USER_GROUP_TYPE(5, "用户群体类型"),
        CHILDREN_COUNT(6, "孩子数量"),
        REGION_INFO(7, "所在地区信息"),
        PRESALE_SENSITIVE(8, "大促预售购买敏感人群占比"),
        PROMOTION_SENSITIVE(9, "平台促销敏感人群占比"),
        HIGH_CONSUMPTION(10, "大促高消费金额人群"),
        NEW_PRODUCT_PREFERENCE(11, "新品偏好人群占比");

        private final Integer code;
        private final String desc;

        ChartType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ChartType getByCode(Integer code) {
            for (ChartType type : values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }
}
