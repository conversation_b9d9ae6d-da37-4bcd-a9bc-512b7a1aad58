package com.pes.jd.model.DO;

import lombok.Data;

import java.util.Date;

@Data
public class OrderPreordainDO {
    private Long id;

    private Long orderId;

    private Long tradeId;

    private Long shopId;

    private String buyerNick;

    private Double payment;

    private Date date;

    private Date created;

    private Date payTime;

    private Date modified;

    private Date outStockTime;

    private String orderStatus;

    private Integer payType;

    private Double sellerDiscount;

    private Long skuId;

    private String skuName;

    private Integer skuNum;

    private Double skuPrice;


}