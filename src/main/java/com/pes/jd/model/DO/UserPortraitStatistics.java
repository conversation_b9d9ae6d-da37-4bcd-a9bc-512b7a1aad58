package com.pes.jd.model.DO;

import com.alibaba.excel.util.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPortraitStatistics {


    private Long id;


    private Long shopId;


    private LocalDate statisticsDate;

    // 年龄分布（13个年龄段）
    private Integer age0To15 = 0;

    private Integer age16To20 = 0;


    private Integer age21To25 = 0;


    private Integer age26To30 = 0;


    private Integer age31To35 = 0;


    private Integer age36To40 = 0;


    private Integer age41To45 = 0;


    private Integer age46To50 = 0;


    private Integer age51To55 = 0;


    private Integer age56To60 = 0;


    private Integer age61To65 = 0;


    private Integer age66To70 = 0;


    private Integer ageOver71 = 0;

    // 性别分布

    private Integer genderMale = 0;


    private Integer genderFemale = 0;

    // 婚姻状况

    private Integer marriageSingle = 0;


    private Integer marriageMarried = 0;

    // 职业分布（10种职业）

    private Integer professionFinance = 0;


    private Integer professionMedical = 0;


    private Integer professionEmployee = 0;


    private Integer professionWorker = 0;


    private Integer professionTeacher = 0;


    private Integer professionFarmer = 0;


    private Integer professionStudent = 0;


    private Integer professionIndividual = 0;


    private Integer professionUrbanOther = 0;


    private Integer professionRuralOther = 0;

    // 孩子数量

    private Integer childrenUnknown = 0;


    private Integer childrenOne = 0;


    private Integer childrenTwo = 0;


    private Integer childrenThree = 0;


    private Integer childrenFour = 0;

    // 平台价格敏感人群

    private Integer priceSensitivityLuxury = 0;


    private Integer priceSensitivityHighCollar = 0;


    private Integer priceSensitivityHighQuality = 0;


    private Integer priceSensitivityUrbanCollar = 0;


    private Integer priceSensitivityMass = 0;


    private Integer priceSensitivityLowPower = 0;


    private Integer priceSensitivityInactive = 0;

    // 大促购买行为

    private Integer promotionStable = 0;


    private Integer promotionRising = 0;


    private Integer promotionDeclining = 0;


    private Integer promotionHesitant = 0;


    private Integer promotionLowValue = 0;

    // 大促预售购买人群

    private Integer presaleYes = 0;


    private Integer presaleNo = 0;

    // 平台促销敏感人群

    private Integer promotionNotSensitive = 0;


    private Integer promotionLightlySensitive = 0;


    private Integer promotionModeratelySensitive = 0;


    private Integer promotionHighlySensitive = 0;


    private Integer promotionVerySensitive = 0;


    private Integer promotionSensitivityUnknown = 0;

    // 大促高消费金额人群

    private Integer highConsumptionYes = 0;


    private Integer highConsumptionNo = 0;

    // 新品偏好

    private Integer newProductPotentialYes = 0;


    private Integer newProductPotentialNo = 0;


    private Integer newProductMediumYes = 0;


    private Integer newProductMediumNo = 0;


    private Integer newProductSevereYes = 0;

    private Integer newProductSevereNo = 0;

    // 用户好评率分层

    private Integer commentHigh = 0;


    private Integer commentMedium = 0;


    private Integer commentLow = 0;


    private Integer commentUnknown = 0;

    // 地区分布JSON
    private String regionDistribution;

    // 客单价统计
    private BigDecimal avgCustomerUnitPrice;


    private Integer highUnitPriceUsers = 0;


    private Integer totalUsers = 0;


    private LocalDateTime createdTime;


    private LocalDateTime updatedTime;


    /**
     * 学生一族数量
     */
    private Integer studentGroup = 0;

    /**
     * 银发一族数量
     */
    private Integer elderlyGroup = 0;

    /**
     * 都市家庭数量
     */
    private Integer urbanFamilyGroup = 0;

    /**
     * 小镇家庭数量
     */
    private Integer townFamilyGroup = 0;

    /**
     * 都市Z世代数量
     */
    private Integer urbanGenZGroup = 0;

    /**
     * 都市中产数量
     */
    private Integer urbanMiddleClassGroup = 0;

    /**
     * 都市蓝领数量
     */
    private Integer urbanBlueCollarGroup = 0;

    /**
     * 小镇中年数量
     */
    private Integer townMiddleAgedGroup = 0;

    /**
     * 小镇青年数量
     */
    private Integer townYouthGroup = 0;

    /**
     * 小镇中产数量
     */
    private Integer townMiddleClassGroup = 0;




    /**
     * 合并用户画像统计数据列表
     * 将多个统计数据的数值字段进行累加
     *
     * @param statisticsList 统计数据列表
     * @return 合并后的统计数据
     */
    public static UserPortraitStatistics mergeStatistics(List<UserPortraitStatistics> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return new UserPortraitStatistics();
        }

        UserPortraitStatistics merged = new UserPortraitStatistics();

        // 设置非累加字段（取第一个记录的值）
        UserPortraitStatistics first = statisticsList.get(0);
        merged.setShopId(first.getShopId());
        merged.setStatisticsDate(first.getStatisticsDate());
        merged.setCreatedTime(first.getCreatedTime());
        merged.setUpdatedTime(first.getUpdatedTime());

        // 累加所有数值字段
        for (UserPortraitStatistics stats : statisticsList) {
            if (stats == null) {
                continue;
            }

            // 年龄分布累加
            merged.setAge0To15(safeAdd(merged.getAge0To15(), stats.getAge0To15()));
            merged.setAge16To20(safeAdd(merged.getAge16To20(), stats.getAge16To20()));
            merged.setAge21To25(safeAdd(merged.getAge21To25(), stats.getAge21To25()));
            merged.setAge26To30(safeAdd(merged.getAge26To30(), stats.getAge26To30()));
            merged.setAge31To35(safeAdd(merged.getAge31To35(), stats.getAge31To35()));
            merged.setAge36To40(safeAdd(merged.getAge36To40(), stats.getAge36To40()));
            merged.setAge41To45(safeAdd(merged.getAge41To45(), stats.getAge41To45()));
            merged.setAge46To50(safeAdd(merged.getAge46To50(), stats.getAge46To50()));
            merged.setAge51To55(safeAdd(merged.getAge51To55(), stats.getAge51To55()));
            merged.setAge56To60(safeAdd(merged.getAge56To60(), stats.getAge56To60()));
            merged.setAge61To65(safeAdd(merged.getAge61To65(), stats.getAge61To65()));
            merged.setAge66To70(safeAdd(merged.getAge66To70(), stats.getAge66To70()));
            merged.setAgeOver71(safeAdd(merged.getAgeOver71(), stats.getAgeOver71()));

            // 性别分布累加
            merged.setGenderMale(safeAdd(merged.getGenderMale(), stats.getGenderMale()));
            merged.setGenderFemale(safeAdd(merged.getGenderFemale(), stats.getGenderFemale()));

            // 婚姻状况累加
            merged.setMarriageSingle(safeAdd(merged.getMarriageSingle(), stats.getMarriageSingle()));
            merged.setMarriageMarried(safeAdd(merged.getMarriageMarried(), stats.getMarriageMarried()));

            // 职业分布累加
            merged.setProfessionFinance(safeAdd(merged.getProfessionFinance(), stats.getProfessionFinance()));
            merged.setProfessionMedical(safeAdd(merged.getProfessionMedical(), stats.getProfessionMedical()));
            merged.setProfessionEmployee(safeAdd(merged.getProfessionEmployee(), stats.getProfessionEmployee()));
            merged.setProfessionWorker(safeAdd(merged.getProfessionWorker(), stats.getProfessionWorker()));
            merged.setProfessionTeacher(safeAdd(merged.getProfessionTeacher(), stats.getProfessionTeacher()));
            merged.setProfessionFarmer(safeAdd(merged.getProfessionFarmer(), stats.getProfessionFarmer()));
            merged.setProfessionStudent(safeAdd(merged.getProfessionStudent(), stats.getProfessionStudent()));
            merged.setProfessionIndividual(safeAdd(merged.getProfessionIndividual(), stats.getProfessionIndividual()));
            merged.setProfessionUrbanOther(safeAdd(merged.getProfessionUrbanOther(), stats.getProfessionUrbanOther()));
            merged.setProfessionRuralOther(safeAdd(merged.getProfessionRuralOther(), stats.getProfessionRuralOther()));

            // 孩子数量累加
            merged.setChildrenUnknown(safeAdd(merged.getChildrenUnknown(), stats.getChildrenUnknown()));
            merged.setChildrenOne(safeAdd(merged.getChildrenOne(), stats.getChildrenOne()));
            merged.setChildrenTwo(safeAdd(merged.getChildrenTwo(), stats.getChildrenTwo()));
            merged.setChildrenThree(safeAdd(merged.getChildrenThree(), stats.getChildrenThree()));
            merged.setChildrenFour(safeAdd(merged.getChildrenFour(), stats.getChildrenFour()));

            // 价格敏感人群累加
            merged.setPriceSensitivityLuxury(safeAdd(merged.getPriceSensitivityLuxury(), stats.getPriceSensitivityLuxury()));
            merged.setPriceSensitivityHighCollar(safeAdd(merged.getPriceSensitivityHighCollar(), stats.getPriceSensitivityHighCollar()));
            merged.setPriceSensitivityHighQuality(safeAdd(merged.getPriceSensitivityHighQuality(), stats.getPriceSensitivityHighQuality()));
            merged.setPriceSensitivityUrbanCollar(safeAdd(merged.getPriceSensitivityUrbanCollar(), stats.getPriceSensitivityUrbanCollar()));
            merged.setPriceSensitivityMass(safeAdd(merged.getPriceSensitivityMass(), stats.getPriceSensitivityMass()));
            merged.setPriceSensitivityLowPower(safeAdd(merged.getPriceSensitivityLowPower(), stats.getPriceSensitivityLowPower()));
            merged.setPriceSensitivityInactive(safeAdd(merged.getPriceSensitivityInactive(), stats.getPriceSensitivityInactive()));

            // 大促购买行为累加
            merged.setPromotionStable(safeAdd(merged.getPromotionStable(), stats.getPromotionStable()));
            merged.setPromotionRising(safeAdd(merged.getPromotionRising(), stats.getPromotionRising()));
            merged.setPromotionDeclining(safeAdd(merged.getPromotionDeclining(), stats.getPromotionDeclining()));
            merged.setPromotionHesitant(safeAdd(merged.getPromotionHesitant(), stats.getPromotionHesitant()));
            merged.setPromotionLowValue(safeAdd(merged.getPromotionLowValue(), stats.getPromotionLowValue()));

            // 大促预售购买人群累加
            merged.setPresaleYes(safeAdd(merged.getPresaleYes(), stats.getPresaleYes()));
            merged.setPresaleNo(safeAdd(merged.getPresaleNo(), stats.getPresaleNo()));

            // 促销敏感人群累加
            merged.setPromotionNotSensitive(safeAdd(merged.getPromotionNotSensitive(), stats.getPromotionNotSensitive()));
            merged.setPromotionLightlySensitive(safeAdd(merged.getPromotionLightlySensitive(), stats.getPromotionLightlySensitive()));
            merged.setPromotionModeratelySensitive(safeAdd(merged.getPromotionModeratelySensitive(), stats.getPromotionModeratelySensitive()));
            merged.setPromotionHighlySensitive(safeAdd(merged.getPromotionHighlySensitive(), stats.getPromotionHighlySensitive()));
            merged.setPromotionVerySensitive(safeAdd(merged.getPromotionVerySensitive(), stats.getPromotionVerySensitive()));
            merged.setPromotionSensitivityUnknown(safeAdd(merged.getPromotionSensitivityUnknown(), stats.getPromotionSensitivityUnknown()));

            // 大促高消费金额人群累加
            merged.setHighConsumptionYes(safeAdd(merged.getHighConsumptionYes(), stats.getHighConsumptionYes()));
            merged.setHighConsumptionNo(safeAdd(merged.getHighConsumptionNo(), stats.getHighConsumptionNo()));

            // 新品偏好累加
            merged.setNewProductPotentialYes(safeAdd(merged.getNewProductPotentialYes(), stats.getNewProductPotentialYes()));
            merged.setNewProductPotentialNo(safeAdd(merged.getNewProductPotentialNo(), stats.getNewProductPotentialNo()));
            merged.setNewProductMediumYes(safeAdd(merged.getNewProductMediumYes(), stats.getNewProductMediumYes()));
            merged.setNewProductMediumNo(safeAdd(merged.getNewProductMediumNo(), stats.getNewProductMediumNo()));
            merged.setNewProductSevereYes(safeAdd(merged.getNewProductSevereYes(), stats.getNewProductSevereYes()));
            merged.setNewProductSevereNo(safeAdd(merged.getNewProductSevereNo(), stats.getNewProductSevereNo()));

            // 用户好评率分层累加
            merged.setCommentHigh(safeAdd(merged.getCommentHigh(), stats.getCommentHigh()));
            merged.setCommentMedium(safeAdd(merged.getCommentMedium(), stats.getCommentMedium()));
            merged.setCommentLow(safeAdd(merged.getCommentLow(), stats.getCommentLow()));
            merged.setCommentUnknown(safeAdd(merged.getCommentUnknown(), stats.getCommentUnknown()));

            // 高客单价用户数累加
            merged.setHighUnitPriceUsers(safeAdd(merged.getHighUnitPriceUsers(), stats.getHighUnitPriceUsers()));

            // 总用户数累加
            merged.setTotalUsers(safeAdd(merged.getTotalUsers(), stats.getTotalUsers()));

            // 人群分类累加
            merged.setStudentGroup(safeAdd(merged.getStudentGroup(), stats.getStudentGroup()));
            merged.setElderlyGroup(safeAdd(merged.getElderlyGroup(), stats.getElderlyGroup()));
            merged.setUrbanFamilyGroup(safeAdd(merged.getUrbanFamilyGroup(), stats.getUrbanFamilyGroup()));
            merged.setTownFamilyGroup(safeAdd(merged.getTownFamilyGroup(), stats.getTownFamilyGroup()));
            merged.setUrbanGenZGroup(safeAdd(merged.getUrbanGenZGroup(), stats.getUrbanGenZGroup()));
            merged.setUrbanMiddleClassGroup(safeAdd(merged.getUrbanMiddleClassGroup(), stats.getUrbanMiddleClassGroup()));
            merged.setUrbanBlueCollarGroup(safeAdd(merged.getUrbanBlueCollarGroup(), stats.getUrbanBlueCollarGroup()));
            merged.setTownMiddleAgedGroup(safeAdd(merged.getTownMiddleAgedGroup(), stats.getTownMiddleAgedGroup()));
            merged.setTownYouthGroup(safeAdd(merged.getTownYouthGroup(), stats.getTownYouthGroup()));
            merged.setTownMiddleClassGroup(safeAdd(merged.getTownMiddleClassGroup(), stats.getTownMiddleClassGroup()));
        }
        return merged;
    }

    /**
     * 安全的整数相加，处理null值
     */
    private static Integer safeAdd(Integer a, Integer b) {
        if (a == null && b == null) {
            return 0;
        }
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a + b;
    }


}
