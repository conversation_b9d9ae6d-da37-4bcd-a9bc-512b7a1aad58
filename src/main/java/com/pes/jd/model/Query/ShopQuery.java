package com.pes.jd.model.Query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pes.jd.model.DTO.CsDTO;

import java.util.Date;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown=true)
public class ShopQuery  {

	private Long shopId;
	
	private String schemaId;

	private String dbName;
	
	private String rtSchemaId;
	
	private String rtDbName;
	
	private Long sellerId;

	private String sellerNick;
	
	private String sellerShowNick;

	private String title;
	
	private String sessionKey;

	private List<String> csNickLst;// 客服昵称集合

	private Date lastGetDateTime;

	private Integer colType;

	private Integer subuserNum;

	private String optionSessionKey;

	private List<CsDTO> csLst;

	private Long venderId;

	public ShopQuery() {
		super();
	}

	public Long getSellerId() {
		return sellerId;
	}

	public void setSellerId(Long sellerId) {
		this.sellerId = sellerId;
	}

	public String getSellerNick() {
		return sellerNick;
	}

	public void setSellerNick(String sellerNick) {
		this.sellerNick = sellerNick;
	}

	public List<String> getCsNickLst() {
		return csNickLst;
	}

	public void setCsNickLst(List<String> csNickLst) {
		this.csNickLst = csNickLst;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Date getLastGetDateTime() {
		return lastGetDateTime;
	}

	public void setLastGetDateTime(Date lastGetDateTime) {
		this.lastGetDateTime = lastGetDateTime;
	}

	public Integer getColType() {
		return colType;
	}

	public void setColType(Integer colType) {
		this.colType = colType;
	}

	public Integer getSubuserNum() {
		return subuserNum;
	}

	public void setSubuserNum(Integer subuserNum) {
		this.subuserNum = subuserNum;
	}

	public String getSessionKey() {
		return sessionKey;
	}

	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey;
	}

	public Long getShopId() {
		return shopId;
	}

	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}

	public String getSchemaId() {
		return schemaId;
	}

	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}

	public String getDbName() {
		return dbName;
	}

	public void setDbName(String dbName) {
		this.dbName = dbName;
	}

	public String getRtSchemaId() {
		return rtSchemaId;
	}

	public void setRtSchemaId(String rtSchemaId) {
		this.rtSchemaId = rtSchemaId;
	}

	public String getRtDbName() {
		return rtDbName;
	}

	public void setRtDbName(String rtDbName) {
		this.rtDbName = rtDbName;
	}

	public String getSellerShowNick() {
		return sellerShowNick;
	}

	public void setSellerShowNick(String sellerShowNick) {
		this.sellerShowNick = sellerShowNick;
	}

	public String getOptionSessionKey() {
		return optionSessionKey;
	}

	public void setOptionSessionKey(String optionSessionKey) {
		this.optionSessionKey = optionSessionKey;
	}

	public List<CsDTO> getCsLst() {
		return csLst;
	}

	public void setCsLst(List<CsDTO> csLst) {
		this.csLst = csLst;
	}

	public Long getVenderId() {
		return venderId;
	}

	public void setVenderId(Long venderId) {
		this.venderId = venderId;
	}

	@Override
	public String toString() {
		return "ShopQuery{" +
				"shopId=" + shopId +
				", schemaId='" + schemaId + '\'' +
				", dbName='" + dbName + '\'' +
				", rtSchemaId='" + rtSchemaId + '\'' +
				", rtDbName='" + rtDbName + '\'' +
				", sellerId=" + sellerId +
				", sellerNick='" + sellerNick + '\'' +
				", title='" + title + '\'' +
				", sessionKey='" + sessionKey + '\'' +
				", csNickLst=" + csNickLst +
				", lastGetDateTime=" + lastGetDateTime +
				", colType=" + colType +
				", subuserNum=" + subuserNum +
				'}';
	}
}
  
