package com.pes.jd.model.Query;

import java.util.Date;

public class ValidDateRangeQuery {

	public final static Integer validOrderPayDays = 1;

	public final static Integer validPresaleOrderBalancePayDays = 90; //默认预售跨度90天(下单-付尾款)

	private Date startDate; //查询目标数据的开始时间
	
	private Date endDate; //查询目标数据的结束时间

	private Date adjustStartDate; //分表查询-调整的开始时间 - 通用

	private Date adjustEndDate; //分表查询-调整的结束时间 - 通用

	private Date adjustPresaleStartDate; //分表查询-调整的预售相关的开始时间

	private Date adjustPresaleEndDate; //分表查询-调整的预售相关的结束时间
	
	private Date adjustEnquiryStartDate; //分表查询-询单维度调整的开始时间
	
	private Date adjustEnquiryEndDate; //分表查询-询单维度调整的结束时间
	
	private Date adjustPaidStartDate; //分表查询-付款维度调整的开始时间
	
	private Date adjustPaidEndDate; //分表查询-付款维度调整的结束时间
	
	private Date adjustOutStockStartDate; //分表查询-出库维度调整的开始时间
	
	private Date adjustOutStockEndDate; //分表查询-出库维度调整的结束时间
	
	private Date adjustConfirmGoodsStartDate; //分表查询-收货维度调整的开始时间
	
	private Date adjustConfirmGoodsEndDate; //分表查询-收货维度调整的结束时间
	
	private Date adjustOrderReMarkStartDate; //分表查询-订单备注开始时间
	
	private Date adjustOrderReMarkEndDate; //分表查询-订单备注结束时间

	private Integer enquiryValidDurationTime;//询单有效时长(n天)n:2-15

	private Integer sellAfter;//售后时间
	public ValidDateRangeQuery() {
		super();
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getAdjustStartDate() {
		return adjustStartDate;
	}

	public void setAdjustStartDate(Date adjustStartDate) {
		this.adjustStartDate = adjustStartDate;
	}

	public Date getAdjustEndDate() {
		return adjustEndDate;
	}

	public void setAdjustEndDate(Date adjustEndDate) {
		this.adjustEndDate = adjustEndDate;
	}


	public Date getAdjustPresaleStartDate() {
		return adjustPresaleStartDate;
	}

	public void setAdjustPresaleStartDate(Date adjustPresaleStartDate) {
		this.adjustPresaleStartDate = adjustPresaleStartDate;
	}

	public Date getAdjustPresaleEndDate() {
		return adjustPresaleEndDate;
	}

	public void setAdjustPresaleEndDate(Date adjustPresaleEndDate) {
		this.adjustPresaleEndDate = adjustPresaleEndDate;
	}

	public Date getAdjustEnquiryStartDate() {
		return adjustEnquiryStartDate;
	}

	public void setAdjustEnquiryStartDate(Date adjustEnquiryStartDate) {
		this.adjustEnquiryStartDate = adjustEnquiryStartDate;
	}

	public Date getAdjustEnquiryEndDate() {
		return adjustEnquiryEndDate;
	}

	public void setAdjustEnquiryEndDate(Date adjustEnquiryEndDate) {
		this.adjustEnquiryEndDate = adjustEnquiryEndDate;
	}

	public Date getAdjustOutStockStartDate() {
		return adjustOutStockStartDate;
	}

	public void setAdjustOutStockStartDate(Date adjustOutStockStartDate) {
		this.adjustOutStockStartDate = adjustOutStockStartDate;
	}

	public Date getAdjustOutStockEndDate() {
		return adjustOutStockEndDate;
	}

	public void setAdjustOutStockEndDate(Date adjustOutStockEndDate) {
		this.adjustOutStockEndDate = adjustOutStockEndDate;
	}

	public Date getAdjustConfirmGoodsStartDate() {
		return adjustConfirmGoodsStartDate;
	}

	public void setAdjustConfirmGoodsStartDate(Date adjustConfirmGoodsStartDate) {
		this.adjustConfirmGoodsStartDate = adjustConfirmGoodsStartDate;
	}

	public Date getAdjustConfirmGoodsEndDate() {
		return adjustConfirmGoodsEndDate;
	}

	public void setAdjustConfirmGoodsEndDate(Date adjustConfirmGoodsEndDate) {
		this.adjustConfirmGoodsEndDate = adjustConfirmGoodsEndDate;
	}

	public Date getAdjustPaidStartDate() {
		return adjustPaidStartDate;
	}

	public void setAdjustPaidStartDate(Date adjustPaidStartDate) {
		this.adjustPaidStartDate = adjustPaidStartDate;
	}

	public Date getAdjustPaidEndDate() {
		return adjustPaidEndDate;
	}

	public void setAdjustPaidEndDate(Date adjustPaidEndDate) {
		this.adjustPaidEndDate = adjustPaidEndDate;
	}

	public Date getAdjustOrderReMarkStartDate() {
		return adjustOrderReMarkStartDate;
	}

	public void setAdjustOrderReMarkStartDate(Date adjustOrderReMarkStartDate) {
		this.adjustOrderReMarkStartDate = adjustOrderReMarkStartDate;
	}

	public Date getAdjustOrderReMarkEndDate() {
		return adjustOrderReMarkEndDate;
	}

	public void setAdjustOrderReMarkEndDate(Date adjustOrderReMarkEndDate) {
		this.adjustOrderReMarkEndDate = adjustOrderReMarkEndDate;
	}

	public Integer getEnquiryValidDurationTime() {
		return enquiryValidDurationTime;
	}

	public void setEnquiryValidDurationTime(Integer enquiryValidDurationTime) {
		this.enquiryValidDurationTime = enquiryValidDurationTime;
	}

	public Integer getSellAfter() {
		return sellAfter;
	}

	public void setSellAfter(Integer sellAfter) {
		this.sellAfter = sellAfter;
	}
}
