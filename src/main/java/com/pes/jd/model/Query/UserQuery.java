package com.pes.jd.model.Query;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> <EMAIL>
 * @date 2018/11/15 9:21 AM
 * @since 1.0.0
 */
public class UserQuery {
	private String nick;
    private Long shopId;
    private Long groupId;
    private String groupName;
    private Integer type;
    private String simpleName;
    private Integer csStatus;
    private Date lockTime;
    public Date getLockTime() {
		return lockTime;
	}

	public void setLockTime(Date lockTime) {
		this.lockTime = lockTime;
	}

	public Long getGroupId() {
		return groupId;
	}

	public void setGroupId(Long groupId) {
		this.groupId = groupId;
	}

	public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

	public Integer getCsStatus() {
		return csStatus;
	}

	public void setCsStatus(Integer csStatus) {
		this.csStatus = csStatus;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserQuery)) return false;
        UserQuery userQuery = (UserQuery) o;
        return Objects.equals(nick, userQuery.nick) &&
                Objects.equals(shopId, userQuery.shopId) &&
                Objects.equals(groupId, userQuery.groupId) &&
                Objects.equals(groupName, userQuery.groupName) &&
                Objects.equals(type, userQuery.type) &&
                Objects.equals(simpleName, userQuery.simpleName) &&
                Objects.equals(csStatus, userQuery.csStatus) &&
                Objects.equals(lockTime, userQuery.lockTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nick, shopId, groupId, groupName, type, simpleName, csStatus, lockTime);
    }
}
