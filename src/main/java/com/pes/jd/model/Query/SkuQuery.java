package com.pes.jd.model.Query;

/**
 * <AUTHOR>
 * @date 2019-05-15
 */
public class SkuQuery {

    private Long shopId;
    private String schemaId;
    private String dbName;

    private SkuQuery(Builder builder) {
        setShopId(builder.shopId);
        setSchemaId(builder.schemaId);
        setDbName(builder.dbName);
    }

    public static Builder newBuilder() {
        return new Builder();
    }


    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getSchemaId() {
        return schemaId;
    }

    public void setSchemaId(String schemaId) {
        this.schemaId = schemaId;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public static final class Builder {
        private Long shopId;
        private String schemaId;
        private String dbName;

        private Builder() {
        }

        public Builder shopId(Long val) {
            shopId = val;
            return this;
        }

        public Builder schemaId(String val) {
            schemaId = val;
            return this;
        }

        public Builder dbName(String val) {
            dbName = val;
            return this;
        }

        public SkuQuery build() {
            return new SkuQuery(this);
        }
    }
}
