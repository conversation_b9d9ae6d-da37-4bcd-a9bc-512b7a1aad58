package com.pes.jd.model.Query;

public class SortPageQuery {

	private Boolean sort = false;//默认不排序

	private String propertity;//排序属性

	private String field;//排序字段

	private String sortDirection;//排序方向

	private Long currentPage = 0L;//当前页

	private Long size = 10L;//每页显示数量

	public String getPropertity() {
		return propertity;
	}

	public void setPropertity(String propertity) {
		this.propertity = propertity;
	}

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}

	public Boolean getSort() {
		return sort;
	}

	public void setSort(Boolean sort) {
		this.sort = sort;
	}

	public String getSortDirection() {
		return sortDirection;
	}

	public void setSortDirection(String sortDirection) {
		this.sortDirection = sortDirection;
	}

	public Long getCurrentPage() {
		return currentPage;
	}

	public void setCurrentPage(Long currentPage) {
		this.currentPage = currentPage;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

}
