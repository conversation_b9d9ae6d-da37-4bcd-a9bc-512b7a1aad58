package com.pes.jd.model.Query;

import com.pes.jd.model.DTO.CsDTO;
import com.pes.jd.model.DTO.ShopSystemsettingDTO;

import java.util.Date;
import java.util.List;

public class ShopInfoQuery {
	
	private Long shopId;
	private Long userId;
	private String sellerNick;
	private String title;
	private String sessionKey;
	private int lastConsumedTime;
	private String status;
	private Date previousGetDataTime;
	private Date subscribeDeadLine;
	private Date previousFetchRealtime;//上次拉取实时数据的时间
	private Integer subUserNum;
	private String realTimeType;
	private String nick;
	private Integer fetchFlag;
	private Integer initDataFlag;
	private String schemaId;
	private List<CsDTO> csLst;
	private ShopSystemsettingDTO shopSystemsettingDTO;
	
	private String db;
	
	public ShopInfoQuery() {
		super();
	}
	public Long getShopId() {
		return shopId;
	}
	public void setShopId(Long shopId) {
		this.shopId = shopId;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getSellerNick() {
		return sellerNick;
	}
	public void setSellerNick(String sellerNick) {
		this.sellerNick = sellerNick;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getSessionKey() {
		return sessionKey;
	}
	public void setSessionKey(String sessionKey) {
		this.sessionKey = sessionKey;
	}
	public int getLastConsumedTime() {
		return lastConsumedTime;
	}
	public void setLastConsumedTime(int lastConsumedTime) {
		this.lastConsumedTime = lastConsumedTime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Date getPreviousGetDataTime() {
		return previousGetDataTime;
	}
	public void setPreviousGetDataTime(Date previousGetDataTime) {
		this.previousGetDataTime = previousGetDataTime;
	}
	public Date getSubscribeDeadLine() {
		return subscribeDeadLine;
	}
	public void setSubscribeDeadLine(Date subscribeDeadLine) {
		this.subscribeDeadLine = subscribeDeadLine;
	}
	public Date getPreviousFetchRealtime() {
		return previousFetchRealtime;
	}
	public void setPreviousFetchRealtime(Date previousFetchRealtime) {
		this.previousFetchRealtime = previousFetchRealtime;
	}
	public Integer getSubUserNum() {
		return subUserNum;
	}
	public void setSubUserNum(Integer subUserNum) {
		this.subUserNum = subUserNum;
	}
	public String getRealTimeType() {
		return realTimeType;
	}
	public void setRealTimeType(String realTimeType) {
		this.realTimeType = realTimeType;
	}
	public String getNick() {
		return nick;
	}
	public void setNick(String nick) {
		this.nick = nick;
	}
	public Integer getFetchFlag() {
		return fetchFlag;
	}
	public void setFetchFlag(Integer fetchFlag) {
		this.fetchFlag = fetchFlag;
	}
	public Integer getInitDataFlag() {
		return initDataFlag;
	}
	public void setInitDataFlag(Integer initDataFlag) {
		this.initDataFlag = initDataFlag;
	}
	public String getSchemaId() {
		return schemaId;
	}
	public void setSchemaId(String schemaId) {
		this.schemaId = schemaId;
	}
	public List<CsDTO> getCsLst() {
		return csLst;
	}
	public void setCsLst(List<CsDTO> csLst) {
		this.csLst = csLst;
	}
	public String getDb() {
		return db;
	}
	public void setDb(String db) {
		this.db = db;
	}
	public ShopSystemsettingDTO getShopSystemsettingDTO() {
		return shopSystemsettingDTO;
	}
	public void setShopSystemsettingDTO(ShopSystemsettingDTO shopSystemsettingDTO) {
		this.shopSystemsettingDTO = shopSystemsettingDTO;
	}
}
