package com.pes.jd.model.Query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown=true)
public class SgShopQuery {

	private Long shopId;

	private Integer shopStatus;

	private Integer enquiryValidDurationTime;


	@Override
	public String toString() {
		return "ShopQuery{" +
				"shopId=" + shopId +
				", shopStatus='" + shopStatus + '\'' +
				", enquiryValidDurationTime='" + enquiryValidDurationTime + '\'' +
				'}';
	}
}
  
