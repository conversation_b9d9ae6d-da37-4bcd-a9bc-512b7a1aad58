package com.pes.jd.model.Response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.ImmutableMap;
import com.pes.jd.model.Enum.ApiCodeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**  
 * ClassName:ReportApiResponse <br/>  
 * Function: 前端调接口响应 <br/>
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年7月19日 上午9:51:23 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8 
 * @see        
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
public class ReportApiResponse {

	/**
	 * RPC调用接口处理结果
	 */
	private Boolean success;
	/**
	 * 接口调用状态码
	 */
	private String rpCode;
	/**
	 * 接口调用状态码
	 */
	private String rpMsg;
	/**
	 * 接口返回值
	 */
	private Map<String, Object> data;
	/**
	 * 请求的授权码
	 */
	private String token;


	/**
	 * 请求成功，无后续提示操作
	 */
	public static ReportApiResponse ofSuccess() {
		return of(Boolean.TRUE);
	}

	/**
	 * 请求成功，回传结果集，无后续提示操作
	 * @param key
	 * @param value
	 * @return
	 */
	public static ReportApiResponse ofSuccess(String key, Object value) {
		return of(Boolean.TRUE, "", "", ImmutableMap.<String, Object>builder().put(key, value).build());
	}

	/**
	 * 请求成功，回传结果集，无后续提示操作
	 * @param data
	 */
	public static ReportApiResponse ofSuccess(Map<String, Object> data) {
		return of(Boolean.TRUE, "", "", data);
	}

	/**
	 * 请求成功,回传结果集和操作提示
	 * @param rpCode
	 * @param rpMsg
	 * @param data
	 * @return
	 */
	public static ReportApiResponse ofSuccessWithNextStep(String rpCode, String rpMsg, Map<String, Object> data) {
		return of(Boolean.TRUE, rpCode, rpMsg, data);
	}

	/**
	 * 请求成功,回传结果集和操作提示
	 * @param apiCodeEnum
	 * @param data
	 * @return
	 */
	public static ReportApiResponse ofSuccessWithNextStep(ApiCodeEnum apiCodeEnum, Map<String, Object> data) {
		return of(Boolean.TRUE, apiCodeEnum.getCode(), apiCodeEnum.getMsg(), data);
	}

	/**
	 *  请求失败，回传错误码，错误信息
	 */
	public static ReportApiResponse ofFail(ApiCodeEnum apiCodeEnum) {
		return of(Boolean.FALSE, apiCodeEnum.getCode(), apiCodeEnum.getMsg(), null);
	}

	/**
	 * 请求失败，回传错误码，错误信息
	 * @param rpCode
	 * @param rpMsg
	 */
	public static ReportApiResponse ofFail(String rpCode, String rpMsg) {
		return of(Boolean.FALSE, rpCode, rpMsg, null);
	}

	/**
	 * 请求失败，回传错误码，错误信息
	 * @param rpCode
	 * @param rpMsg
	 * @param data
	 * @return
	 */
	public static ReportApiResponse ofFailWithNextStep(String rpCode, String rpMsg, Map<String, Object> data) {
		return of(Boolean.FALSE, rpCode, rpMsg, data);
	}

	/**
	 * 请求失败，回传错误码，错误信息
	 * @param apiCodeEnum
	 * @param data
	 * @return
	 */
	public static ReportApiResponse ofFailWithNextStep(ApiCodeEnum apiCodeEnum, Map<String, Object> data) {
		return of(Boolean.FALSE, apiCodeEnum.getCode(), apiCodeEnum.getMsg(), data);
	}

	private static ReportApiResponse of(Boolean success) {
		return of(success, "", "", null);
	}

	private static ReportApiResponse of(Boolean success, String rpCode, String rpMsg, Map<String, Object> data) {
		return ReportApiResponse.builder()
				.success(success)
				.rpCode(rpCode)
				.rpMsg(rpMsg)
				.data(data)
				.build();
	}
}
  
