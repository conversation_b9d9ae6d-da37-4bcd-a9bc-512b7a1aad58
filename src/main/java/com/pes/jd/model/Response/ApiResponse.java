/**  
 * Project Name:customerservicePES  
 * File Name:JxResponse.java  
 * Package Name:com.customerservicePES.model.response  
 * Date:2018年7月19日上午9:51:23  
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.pes.jd.model.Response;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.pes.jd.model.Enum.ApiCodeEnum;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**  
 * ClassName:ApiResponse <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年7月19日 上午9:51:23 <br/>  
 * <AUTHOR>
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8 
 * @see        
 */
@SuppressWarnings("Duplicates")
public class ApiResponse {

	public enum Type{
		SINGLE,COLLECTION
	}

	public static final ApiResponse of(String rpCode, String rpMsg, Map<String,Object> data){
		return new ApiResponse.Builder().rpCode(rpCode).rpMsg(rpMsg).data(data).build();
	}

	public static final ApiResponse of(String rpCode, String rpMsg){
		return of(rpCode, rpMsg,null);
	}

	public static final ApiResponse of(String rpCode, Integer rpMsg){
		return of(rpCode, rpMsg+"",null);
	}

	public static final ApiResponse of(ApiCodeEnum apiCodeEnum){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg());
	}

	public static final ApiResponse of(ApiCodeEnum apiCodeEnum, Object message){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg(),ImmutableMap.<String,Object>builder()
				.put(ApiResponse.RESULT_SINGLE_NAME,message)
				.build());
	}

	public static final ApiResponse of(ApiCodeEnum apiCodeEnum, Map<String,Object> data){
		return of(apiCodeEnum.getCode(),apiCodeEnum.getMsg(),data);
	}

	public static final ApiResponse of(ApiCodeEnum apiCodeEnum, Object data, Type type){
		Map<String,Object> result = Maps.newHashMap();
		if (Objects.equals(type, Type.SINGLE)){
			result.put(RESULT_SINGLE_NAME,data);
		}else {
			throw new IllegalArgumentException(" error args ");
		}
		return of(apiCodeEnum,result);
	}

	public static final String RESULT_SINGLE_NAME = "result";
	public static final String RESULT_COLLECTION_NAME = "collectData";
	public static final String RESULT_AVG_NAME = "converge";

	public static final Map<String,Object> EMPTY_RESULT = ImmutableMap.
			<String,Object>builder().put(RESULT_SINGLE_NAME, Collections.EMPTY_LIST).build();
	
	/**
	 * 接口调用状态码
	 */
	private String rpCode;
	/**
	 * 接口调用状态码
	 */
	private String rpMsg;
	/**
	 * 接口返回值
	 */
	private Map<String, Object> data;

	public ApiResponse() {
	}

	private ApiResponse(Builder builder) {
		setRpCode(builder.rpCode);
		setRpMsg(builder.rpMsg);
		setData(builder.data);
	}

	public static Builder newBuilder() {
		return new Builder();
	}

	public static Builder newBuilder(ApiResponse copy) {
		Builder builder = new Builder();
		builder.rpCode = copy.getRpCode();
		builder.rpMsg = copy.getRpMsg();
		builder.data = copy.getData();
		return builder;
	}


	public String getRpCode() {
		return rpCode;
	}
	public void setRpCode(String rpCode) {
		this.rpCode = rpCode;
	}
	public String getRpMsg() {
		return rpMsg;
	}
	public void setRpMsg(String rpMsg) {
		this.rpMsg = rpMsg;
	}
	public Map<String, Object> getData() {
		return data;
	}
	public void setData(Map<String, Object> data) {
		this.data = data;
	}


	public static final class Builder {
		private String rpCode;
		private String rpMsg;
		private Map<String, Object> data;

		private Builder() {
		}

		public Builder rpCode(String rpCode) {
			this.rpCode = rpCode;
			return this;
		}

		public Builder rpMsg(String rpMsg) {
			this.rpMsg = rpMsg;
			return this;
		}

		public Builder data(Map<String, Object> data) {
			this.data = data;
			return this;
		}

		public ApiResponse build() {
			return new ApiResponse(this);
		}
	}
}
  
