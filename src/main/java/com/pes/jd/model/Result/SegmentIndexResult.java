package com.pes.jd.model.Result;

import com.pes.jd.model.DTO.ShopTeamHourIndexDTO2;

import java.util.Collection;
import java.util.List;

public class SegmentIndexResult {

    private Boolean detailIndex = Boolean.FALSE;

    private List<ShopTeamHourIndexDTO2> todayTotalIndex;

    private Collection<ShopTeamHourIndexDTO2> todayIndexDetailLst;

    private Collection<ShopTeamHourIndexDTO2> designationTotalIndex;

    public Boolean getDetailIndex() {
        return detailIndex;
    }

    public void setDetailIndex(Boolean detailIndex) {
        this.detailIndex = detailIndex;
    }

    public List<ShopTeamHourIndexDTO2> getTodayTotalIndex() {
        return todayTotalIndex;
    }

    public void setTodayTotalIndex(List<ShopTeamHourIndexDTO2> todayTotalIndex) {
        this.todayTotalIndex = todayTotalIndex;
    }

    public Collection<ShopTeamHourIndexDTO2> getTodayIndexDetailLst() {
        return todayIndexDetailLst;
    }

    public void setTodayIndexDetailLst(Collection<ShopTeamHourIndexDTO2> todayIndexDetailLst) {
        this.todayIndexDetailLst = todayIndexDetailLst;
    }

    public Collection<ShopTeamHourIndexDTO2> getDesignationTotalIndex() {
        return designationTotalIndex;
    }

    public void setDesignationTotalIndex(Collection<ShopTeamHourIndexDTO2> designationTotalIndex) {
        this.designationTotalIndex = designationTotalIndex;
    }
}
