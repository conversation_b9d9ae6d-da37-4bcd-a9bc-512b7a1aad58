package com.pes.jd.model.Result;

import com.pes.jd.model.DTO.RegionInfoDTO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.List;

@Data
@Accessors(chain = true)
public class RegionDistributionResult {

    /**
     * 省级分布数据
     */
    private List<RegionInfoDTO> provinces;

    /**
     * 扁平化的所有区域数据（包含省市区）
     */
    private List<RegionInfoDTO> allRegions;
}
