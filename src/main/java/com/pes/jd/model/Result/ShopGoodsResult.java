package com.pes.jd.model.Result;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pes.jd.model.DTO.ShopGoodNameDTO;
import com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku;
import lombok.Data;

import java.util.List;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @date 2019年11月06 14:28:28<br>
 */
@JsonIgnoreProperties(ignoreUnknown =true)
@Data
public class ShopGoodsResult {

    private List<ShopGoodNameDTO> skuLst;
    private List<ShopGoodNameDTO> spuLst;
    private List<ShopGoodsSku> wareSkuIdLst;
}
