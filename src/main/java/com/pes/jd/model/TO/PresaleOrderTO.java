package com.pes.jd.model.TO;

import com.jd.open.api.sdk.domain.order.PresaleOrderExportServiceForJOS.response.getPresaleOrderByPage.PresaleOrderVO;

import java.util.List;

public class PresaleOrderTO extends JdApiNumTO {
	
	private List<PresaleOrderVO> presaleOrderVOList;
	
	public PresaleOrderTO(){
		super();
	};
	
	public PresaleOrderTO(List<PresaleOrderVO> presaleOrderVOList , int num , int retryNum){
		super(num, retryNum); 
		this.presaleOrderVOList = presaleOrderVOList;
	}
	
	public List<PresaleOrderVO> getPresaleOrderVOList() {
		return presaleOrderVOList;
	}
	public void setPresaleOrderVOList(List<PresaleOrderVO> presaleOrderVOList) {
		this.presaleOrderVOList = presaleOrderVOList;
	};
	
}
  
