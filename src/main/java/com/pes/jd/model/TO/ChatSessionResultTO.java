/**  
 * Project Name:pes-jd-sub  
 * File Name:ChatPeerResultTO.java  
 * Package Name:com.pes.jd.model.TO  
 * Date:2018年10月31日下午3:52:11  
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.pes.jd.model.TO;  
/**  
 * ClassName:ChatPeerResultTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月31日 下午3:52:11 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */

import com.jd.open.api.sdk.response.im.ChatSession;
import com.pes.jd.model.AO.ApiNumAO;

import java.util.List;

public class ChatSessionResultTO extends ApiNumAO{

	private List<ChatSession> chatPeerLst;

	public ChatSessionResultTO() {
		super();  
	}

	public ChatSessionResultTO(List<ChatSession> chatPeerLst,int num, int retryNum) {
		super(num, retryNum);
		this.chatPeerLst = chatPeerLst;
	}

	public ChatSessionResultTO(List<ChatSession> chatPeerLst) {
		super();
		this.chatPeerLst = chatPeerLst;
	}

	public List<ChatSession> getChatPeerLst() {
		return chatPeerLst;
	}

	public void setChatPeerLst(List<ChatSession> chatPeerLst) {
		this.chatPeerLst = chatPeerLst;
	}
	
	
}
  
