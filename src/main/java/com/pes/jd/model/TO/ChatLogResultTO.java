/**  
 * Project Name:jd_web  
 * File Name:ShopAccuntResultTO.java  
 * Package Name:com.pes.jd.model.TO  
 * Date:2018年10月25日下午2:28:01  
 * Copyright (c) 2018, <EMAIL> All Rights Reserved.  
 *  
*/  
  
package com.pes.jd.model.TO;


//import com.jd.open.api.sdk.domain.im.OpenApiService.response.get.ChatLog;

import com.jd.open.api.sdk.domain.im.ApiService.response.get.ChatLog;

import java.util.List;

/**  
 * ClassName:ShopAccuntResultTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午2:28:01 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class ChatLogResultTO {
	
	private List<ChatLog> chatLogLst;
	private int num = 0;
	private int retryNum = 0;
	
	public ChatLogResultTO() {
		super();  
	}
	
	public ChatLogResultTO(List<ChatLog> chatLogLst, int num, int retryNum) {
		super();
		this.chatLogLst = chatLogLst;
		this.num = num;
		this.retryNum = retryNum;
	}

	public List<ChatLog> getChatLogLst() {
		return chatLogLst;
	}
	public void setChatLogLst(List<ChatLog> chatLogLst) {
		this.chatLogLst = chatLogLst;
	}
	public int getNum() {
		return num;
	}
	public void setNum(int num) {
		this.num = num;
	}
	public int getRetryNum() {
		return retryNum;
	}
	public void setRetryNum(int retryNum) {
		this.retryNum = retryNum;
	}
	
	

}
  
