package com.pes.jd.model.TO;

import com.pes.jd.model.DTO.SubscribeOrderDTO;

import java.util.List;


/**  
 * ClassName:SubscribeInfoResultTo <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午6:01:26 <br/>  
 * <AUTHOR>  
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class SubscribeInfoResultTO{

	private List<SubscribeOrderDTO> subOrderLst;

	public SubscribeInfoResultTO() {
		super();  
	}

	public List<SubscribeOrderDTO> getSubOrderLst() {
		return subOrderLst;
	}

	public void setSubOrderLst(List<SubscribeOrderDTO> subOrderLst) {
		this.subOrderLst = subOrderLst;
	}

	public SubscribeInfoResultTO(List<SubscribeOrderDTO> subOrderLst) {
		super();
		this.subOrderLst = subOrderLst;
	}

	
	
	
}
  
