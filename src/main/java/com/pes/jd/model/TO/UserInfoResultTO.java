package com.pes.jd.model.TO;

import com.jd.open.api.sdk.domain.user.UserRelatedRpcService.response.getUserInfoByOpenId.OAuthUserInfo;

/**  
 * ClassName:ShopInfoResultTO <br/>  
 * Function: TODO ADD FUNCTION. <br/>  
 * Reason:   TODO ADD REASON. <br/>  
 * Date:     2018年10月25日 下午5:49:37 <br/>  
 * <AUTHOR>
 * @version    
 * @since    JDK 1.8  
 * @see        
 */
public class UserInfoResultTO {

	private OAuthUserInfo userInfo;

	
	public UserInfoResultTO() {
		super();  
	}


	public OAuthUserInfo getUserInfo() {
		return userInfo;
	}


	public void setUserInfo(OAuthUserInfo userInfo) {
		this.userInfo = userInfo;
	}


	/**
	 * @param userInfo
	 */
	public UserInfoResultTO(OAuthUserInfo userInfo) {
		super();
		this.userInfo = userInfo;
	}

	
	
}
  
