package com.pes.jd.model.TO;

import com.jd.open.api.sdk.response.order.PopOrderFbpGetResponse;

public class SingleFbpOrderTO extends JdApiNumTO{

	private PopOrderFbpGetResponse singleFbpOrderResult;

	public SingleFbpOrderTO() {
		super();
	}

	public SingleFbpOrderTO(PopOrderFbpGetResponse singleFbpOrderResult, int num, int retryNum) {
		super(num, retryNum);
		this.singleFbpOrderResult = singleFbpOrderResult;
	}

	public PopOrderFbpGetResponse getSingleFbpOrderResult() {
		return singleFbpOrderResult;
	}

	public void setSingleFbpOrderResult(PopOrderFbpGetResponse singleFbpOrderResult) {
		this.singleFbpOrderResult = singleFbpOrderResult;
	}

}
