package com.pes.jd.model.TO;

import com.jd.open.api.sdk.response.seller.VenderCategoryGetValidCategoryResultByVenderIdResponse;

public class ShopCategoryTO extends JdApiNumTO{
	
private VenderCategoryGetValidCategoryResultByVenderIdResponse venderCategoryGetValidCategoryResultByVenderIdResponse ;
	
	public ShopCategoryTO() {
		super();
	}
	
	public ShopCategoryTO(VenderCategoryGetValidCategoryResultByVenderIdResponse venderCategoryGetValidCategoryResultByVenderIdResponse, int num, int retryNum) {
		super(num, retryNum);
		this.venderCategoryGetValidCategoryResultByVenderIdResponse = venderCategoryGetValidCategoryResultByVenderIdResponse;
	}

	public VenderCategoryGetValidCategoryResultByVenderIdResponse getVenderCategoryGetValidCategoryResultByVenderIdResponse() {
		return venderCategoryGetValidCategoryResultByVenderIdResponse;
	}

	public void setVenderCategoryGetValidCategoryResultByVenderIdResponse(
			VenderCategoryGetValidCategoryResultByVenderIdResponse venderCategoryGetValidCategoryResultByVenderIdResponse) {
		this.venderCategoryGetValidCategoryResultByVenderIdResponse = venderCategoryGetValidCategoryResultByVenderIdResponse;
	}

}
