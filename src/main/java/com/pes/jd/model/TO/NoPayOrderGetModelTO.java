package com.pes.jd.model.TO;

import com.jd.open.api.sdk.domain.order.OrderNotPayService.response.notPayOrderInfo.OrderDataNotPayInfo;

import java.util.List;

public class NoPayOrderGetModelTO extends JdApiNumTO {

	List<OrderDataNotPayInfo> noPayOrders;

	public NoPayOrderGetModelTO() {
		super();
	}

	public NoPayOrderGetModelTO(List<OrderDataNotPayInfo> noPayOrders, int num, int retryNum) {
		super(num, retryNum);
		this.noPayOrders = noPayOrders;
	}

	public List<OrderDataNotPayInfo> getNoPayOrders() {
		return noPayOrders;
	}

	public void setNoPayOrders(List<OrderDataNotPayInfo> noPayOrders) {
		this.noPayOrders = noPayOrders;
	}

}
