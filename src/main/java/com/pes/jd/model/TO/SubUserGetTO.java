package com.pes.jd.model.TO;

import com.pes.jd.model.DTO.ShopAccountDTO;

import java.util.List;

public class SubUserGetTO extends JdApiNumTO  {

	private List<ShopAccountDTO> custSubUsers;

	public SubUserGetTO() {
		super();
	}

	public SubUserGetTO(List<ShopAccountDTO> custSubUsers, int num, int retryNum) {
		super(num, retryNum);
		this.custSubUsers = custSubUsers;
	}
	
	public List<ShopAccountDTO> getCustSubUsers() {
		return custSubUsers;
	}

	public void setCustSubUsers(List<ShopAccountDTO> custSubUsers) {
		this.custSubUsers = custSubUsers;
	}
	
}
