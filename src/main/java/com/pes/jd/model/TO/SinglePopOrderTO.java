package com.pes.jd.model.TO;

import com.jd.open.api.sdk.response.order.PopOrderGetResponse;

public class SinglePopOrderTO extends JdApiNumTO {

	private PopOrderGetResponse singlePopOrderResult;


	public SinglePopOrderTO() {
		super();
	}

	public SinglePopOrderTO(PopOrderGetResponse singlePopOrderResult, int num, int retryNum) {
		super(num, retryNum);
		this.singlePopOrderResult = singlePopOrderResult;
	}

	public PopOrderGetResponse getSinglePopOrderResult() {
		return singlePopOrderResult;
	}

	public void setSinglePopOrderResult(PopOrderGetResponse singlePopOrderResult) {
		this.singlePopOrderResult = singlePopOrderResult;
	}
	
}
