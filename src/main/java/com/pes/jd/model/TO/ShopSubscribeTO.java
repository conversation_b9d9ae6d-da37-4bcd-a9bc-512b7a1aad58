package com.pes.jd.model.TO;

import com.jd.open.api.sdk.domain.market.OrderServiceProvider.response.listwithpage.OrderVO;

import java.util.List;

public class ShopSubscribeTO {
	
	private List<OrderVO> subOrderLst;

	public ShopSubscribeTO() {
		super();  
	}

	public List<OrderVO> getSubOrderLst() {
		return subOrderLst;
	}

	public void setSubOrderLst(List<OrderVO> subOrderLst) {
		this.subOrderLst = subOrderLst;
	}

	public ShopSubscribeTO(List<OrderVO> subOrderLst) {
		super();
		this.subOrderLst = subOrderLst;
	}
}
