package com.pes.jd.model.TO;

import com.jd.open.api.sdk.domain.ware.WareReadService.response.searchWare4Valid.Ware;

import java.util.ArrayList;
import java.util.List;

public class ShopGoodTO extends JdApiNumTO {
	
	private List<Ware> wareLst;

	public ShopGoodTO() {
		super();
	}
	
	public ShopGoodTO( List<Ware> wareLst, int num, int retryNum) {
		super(num, retryNum);
		this.wareLst = wareLst;
	}

	public List<Ware> getWareLst() {
		return wareLst;
	}

	public void setWareLst(List<Ware> wareLst) {
		this.wareLst = wareLst;
	}

	public ShopGoodTO addAll(ShopGoodTO shopGoodSkuTO){
		if(this.wareLst==null){
			this.wareLst= new ArrayList<>();
		}
		if (shopGoodSkuTO != null && shopGoodSkuTO.getWareLst()!=null) {
			this.wareLst.addAll(shopGoodSkuTO.getWareLst());
		}
		return this;
	}

	public ShopGoodTO addAllGoodLst(List<Ware> wareLst){
		if(this.wareLst==null){
			this.wareLst= new ArrayList<>();
		}
		if (wareLst != null) {
			this.wareLst.addAll(wareLst);
		}
		return this;
	}

}
