package com.pes.jd.model.TO;

import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.search.OrderSearchInfo;

import java.util.HashSet;
import java.util.List;

public class OrderGetModelTO extends JdApiNumTO {

   private List<OrderSearchInfo> orders;

   private HashSet<String> parentOdIds;
	
	public OrderGetModelTO() {
		super();
	}

	public OrderGetModelTO(List<OrderSearchInfo> orders, int num, int retryNum) {
		super(num, retryNum);
		this.orders = orders;
	}

	public OrderGetModelTO(List<OrderSearchInfo> orders, int num, int retryNum, HashSet<String> parentOdIds) {
		super(num, retryNum);
		this.orders = orders;
		this.parentOdIds = parentOdIds;
	}

	public List<OrderSearchInfo> getOrders() {
		return orders;
	}

	public void setOrders(List<OrderSearchInfo> orders) {
		this.orders = orders;
	}

	public HashSet<String> getParentOdIds() {
		return parentOdIds;
	}

	public void setParentOdIds(HashSet<String> parentOdIds) {
		this.parentOdIds = parentOdIds;
	}
}
