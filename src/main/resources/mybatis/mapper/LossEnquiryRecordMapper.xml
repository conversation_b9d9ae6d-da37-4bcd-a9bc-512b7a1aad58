<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.LossEnquiryRecordMapper" >
  
	<resultMap  id="LossEnquiryRecordDTO" type="com.pes.jd.model.DTO.LossEnquiryRecordDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="chat_num" property="chatNum" jdbcType="INTEGER" />
		<result column="chat_type" property="chatType" jdbcType="INTEGER" />
		<result column="start_datetime" property="startDateTime" jdbcType="TIMESTAMP" />
		<result column="end_datetime" property="endDateTime" jdbcType="TIMESTAMP" />
	</resultMap>
		
	<insert id="insertBatchShopLossRecord" parameterType="map">
		INSERT INTO ${tableName} (shop_id,cs_nick,date,customer,chat_num,chat_type,start_datetime,end_datetime,consume_time,sku_ids)
		VALUES
		<foreach collection="lossRecordDOList" item="lossRecord" separator="," >
			(#{lossRecord.shopId},#{lossRecord.csNick},#{lossRecord.date},#{lossRecord.customer},
			#{lossRecord.chatNum},#{lossRecord.chatType},#{lossRecord.startDateTime},#{lossRecord.endDateTime},#{lossRecord.consumeTime},#{lossRecord.skuIds})
		</foreach>
	</insert>
		
	<delete id="deleteShopLossRecordByDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date BETWEEN #{startDate} and #{endDate}
		<if test="chatType != null">
			AND chat_type = #{chatType}
		</if>
	</delete>
	
	<delete id="deleteShopLossRecordByDateAndBuyerLst" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date = #{date}
		AND cs_nick = #{csNick}
		AND customer IN
		<foreach collection="buyerLst" index="index" item="buyer"
			open="(" separator="," close=")">
			#{buyer}
		</foreach>
	</delete>
		
	<select id="selectEnquiryLostRecordLstByDate" parameterType="map" resultMap="LossEnquiryRecordDTO">
		SELECT cs_nick,date,customer
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date BETWEEN #{startDate} AND #{endDate}
	</select>
</mapper>