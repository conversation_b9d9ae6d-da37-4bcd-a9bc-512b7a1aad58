<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopTeamPerformanceMapper">

  <resultMap id="ShopTeamPerformanceDTO" type="com.pes.jd.model.DTO.ShopTeamPerformanceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="direct_receive_num" jdbcType="INTEGER" property="directReceiveNum" />
    <result column="forward_in_num" jdbcType="INTEGER" property="forwardInNum" />
    <result column="forward_out_num" jdbcType="INTEGER" property="forwardOutNum" />
    <result column="consult_num" jdbcType="INTEGER" property="consultNum" />
    <result column="receive_num" jdbcType="INTEGER" property="receiveNum" />
    <result column="enquiry_num" jdbcType="INTEGER" property="enquiryNum" />
    <result column="ordered_num_today" jdbcType="INTEGER" property="orderedNumToday" />
    <result column="ordered_goods_num_today" jdbcType="INTEGER" property="orderedGoodsNumToday" />
    <result column="ordered_amount_today" jdbcType="DOUBLE" property="orderedAmountToday" />
    <result column="ordered_num_final" jdbcType="INTEGER" property="orderedNumFinal" />
    <result column="ordered_goods_num_final" jdbcType="INTEGER" property="orderedGoodsNumFinal" />
    <result column="ordered_amount_final" jdbcType="DOUBLE" property="orderedAmountFinal" />
    <result column="paid_num_today" jdbcType="INTEGER" property="paidNumToday" />
    <result column="paid_amount_today" jdbcType="DOUBLE" property="paidAmountToday" />
    <result column="paid_goods_num_today" jdbcType="INTEGER" property="paidGoodsNumToday" />
    <result column="paid_num_today_next" jdbcType="INTEGER" property="paidNumTodayNext" />
    <result column="paid_num_final" jdbcType="INTEGER" property="paidNumFinal" />
    <result column="paid_goods_num_final" jdbcType="INTEGER" property="paidGoodsNumFinal" />
    <result column="paid_amount_final" jdbcType="DOUBLE" property="paidAmountFinal" />
    <result column="out_stock_order_buyer_num_final" jdbcType="INTEGER" property="outStockOrderBuyerNumFinal" />
    <result column="out_stock_order_num_final" jdbcType="INTEGER" property="outStockOrderNumFinal" />
    <result column="out_stock_order_goods_num_final" jdbcType="INTEGER" property="outStockOrderGoodsNumFinal" />
    <result column="out_stock_order_amount_final" jdbcType="INTEGER" property="outStockOrderAmountFinal" />
  </resultMap>
  
  <insert id="insertShopTeamPerformances" parameterType="map">
    INSERT INTO ${tableName} 
    (
    	shop_id, date, 
      	direct_receive_num, 
      	forward_in_num, 
      	forward_out_num, 
      	consult_num, 
      	receive_num, 
      	enquiry_num, 
      	ordered_num_today, 
      	ordered_goods_num_today, 
      	ordered_amount_today, 
      	ordered_num_final, 
      	ordered_goods_num_final, 
      	ordered_amount_final, 
      	paid_num_today, 
      	paid_amount_today, 
      	paid_goods_num_today, 
      	paid_num_today_next, 
      	paid_num_final, 
      	paid_goods_num_final, 
      	paid_amount_final, 
      	out_stock_order_buyer_num_final, 
      	out_stock_order_num_final, 
      	out_stock_order_goods_num_final, 
      	out_stock_order_amount_final
     )
    VALUES 
    <foreach item="itm" collection="recordLst" open="" close="" separator=",">
    (
      #{itm.shopId,jdbcType=BIGINT}, 
      #{itm.date,jdbcType=DATE}, 
      #{itm.directReceiveNum,jdbcType=INTEGER}, 
      #{itm.forwardInNum,jdbcType=INTEGER}, 
      #{itm.forwardOutNum,jdbcType=INTEGER}, 
      #{itm.consultNum,jdbcType=INTEGER}, 
      #{itm.receiveNum,jdbcType=INTEGER}, 
      #{itm.enquiryNum,jdbcType=INTEGER}, 
      #{itm.orderedNumToday,jdbcType=INTEGER}, 
      #{itm.orderedGoodsNumToday,jdbcType=INTEGER}, 
      #{itm.orderedAmountToday,jdbcType=DOUBLE}, 
      #{itm.orderedNumFinal,jdbcType=INTEGER}, 
      #{itm.orderedGoodsNumFinal,jdbcType=INTEGER}, 
      #{itm.orderedAmountFinal,jdbcType=DOUBLE}, 
      #{itm.paidNumToday,jdbcType=INTEGER}, 
      #{itm.paidAmountToday,jdbcType=DOUBLE}, 
      #{itm.paidGoodsNumToday,jdbcType=INTEGER}, 
      #{itm.paidNumTodayNext,jdbcType=INTEGER}, 
      #{itm.paidNumFinal,jdbcType=INTEGER}, 
      #{itm.paidGoodsNumFinal,jdbcType=INTEGER}, 
      #{itm.paidAmountFinal,jdbcType=DOUBLE}, 
      #{itm.outStockOrderBuyerNumFinal,jdbcType=INTEGER}, 
      #{itm.outStockOrderNumFinal,jdbcType=INTEGER}, 
      #{itm.outStockOrderGoodsNumFinal,jdbcType=INTEGER}, 
      #{itm.outStockOrderAmountFinal,jdbcType=INTEGER}
     )
    </foreach>
  </insert>
  
  <delete id="deleteShopTeamPerformance">
  	DELETE FROM ${tableName}
  	WHERE 
  		shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>
  
  <delete id="deleteShopTeamPerformances">
  	DELETE FROM ${tableName}
  	WHERE 
  		shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
  
<!--   <update id="updateShopTeamPerformanceByShopIdSelective" parameterType="map">
    UPDATE ${tableName}
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.date != null">
        date = #{record.date,jdbcType=DATE},
      </if>
      <if test="record.directReceiveNum != null">
        direct_receive_num = #{record.directReceiveNum,jdbcType=INTEGER},
      </if>
      <if test="record.forwardInNum != null">
        forward_in_num = #{record.forwardInNum,jdbcType=INTEGER},
      </if>
      <if test="record.forwardOutNum != null">
        forward_out_num = #{record.forwardOutNum,jdbcType=INTEGER},
      </if>
      <if test="record.consultNum != null">
        consult_num = #{record.consultNum,jdbcType=INTEGER},
      </if>
      <if test="record.receiveNum != null">
        receive_num = #{record.receiveNum,jdbcType=INTEGER},
      </if>
      <if test="record.enquiryNum != null">
        enquiry_num = #{record.enquiryNum,jdbcType=INTEGER},
      </if>
      <if test="record.orderedNumToday != null">
        ordered_num_today = #{record.orderedNumToday,jdbcType=INTEGER},
      </if>
      <if test="record.orderedGoodsNumToday != null">
        ordered_goods_num_today = #{record.orderedGoodsNumToday,jdbcType=INTEGER},
      </if>
      <if test="record.orderedAmountToday != null">
        ordered_amount_today = #{record.orderedAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="record.orderedNumFinal != null">
        ordered_num_final = #{record.orderedNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.orderedGoodsNumFinal != null">
        ordered_goods_num_final = #{record.orderedGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.orderedAmountFinal != null">
        ordered_amount_final = #{record.orderedAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="record.paidNumToday != null">
        paid_num_today = #{record.paidNumToday,jdbcType=INTEGER},
      </if>
      <if test="record.paidAmountToday != null">
        paid_amount_today = #{record.paidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="record.paidGoodsNumToday != null">
        paid_goods_num_today = #{record.paidGoodsNumToday,jdbcType=INTEGER},
      </if>
      <if test="record.paidNumTodayNext != null">
        paid_num_today_next = #{record.paidNumTodayNext,jdbcType=INTEGER},
      </if>
      <if test="record.paidNumFinal != null">
        paid_num_final = #{record.paidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.paidGoodsNumFinal != null">
        paid_goods_num_final = #{record.paidGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.paidAmountFinal != null">
        paid_amount_final = #{record.paidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="record.outStockOrderBuyerNumFinal != null">
        out_stock_order_buyer_num_final = #{record.outStockOrderBuyerNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.outStockOrderNumFinal != null">
        out_stock_order_num_final = #{record.outStockOrderNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.outStockOrderGoodsNumFinal != null">
        out_stock_order_goods_num_final = #{record.outStockOrderGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.outStockOrderAmountFinal != null">
        out_stock_order_amount_final = #{record.outStockOrderAmountFinal,jdbcType=INTEGER},
      </if>
    </set>
    WHERE 
    	id = #{record.id}
  </update> -->
  
  <select id="getShopTeamPerformanceById" resultMap="ShopTeamPerformanceDTO">
    SELECT *
    FROM ${tableName}
    WHERE 
    	id = #{id}
  </select>
  
</mapper>