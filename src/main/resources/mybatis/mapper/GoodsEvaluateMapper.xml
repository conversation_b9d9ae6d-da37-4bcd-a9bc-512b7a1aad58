<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopGoodsReviewMapper">

	<resultMap id="ShopGoodsReviewDO" type="com.pes.jd.model.DO.ShopGoodsReviewDO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="sku_id" property="skuId" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="order_id" property="orderId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
		<result column="content" property="content" jdbcType="LONGVARCHAR" />
		<result column="order_created" property="orderCreated" jdbcType="TIMESTAMP" />
		<result column="order_pay_time" property="orderPayTime" jdbcType="TIMESTAMP" />
		<result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
		<result column="is_reply" property="isReply" jdbcType="BIT" />
		<result column="score" property="score" jdbcType="INTEGER" />
		<result column="status" property="status" jdbcType="INTEGER" />
	</resultMap>

	<resultMap id="ShopGoodsReviewDTO" type="com.pes.jd.model.DTO.ShopGoodsReviewDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="sku_id" property="skuId" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="order_id" property="orderId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
		<result column="content" property="content" jdbcType="LONGVARCHAR" />
		<result column="order_created" property="orderCreated" jdbcType="TIMESTAMP" />
		<result column="order_pay_time" property="orderPayTime" jdbcType="TIMESTAMP" />
		<result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
		<result column="is_reply" property="isReply" jdbcType="BIT" />
		<result column="score" property="score" jdbcType="INTEGER" />
		<result column="status" property="status" jdbcType="INTEGER" />
	</resultMap>

	<insert id="insertShopGoodsReviewList" parameterType="map">
		INSERT INTO ${tableName}
		(sku_id,buyer_nick,shop_id,cs_nick,order_id,order_created,order_pay_time,score,send_time,content,is_reply,status)
		VALUES
		<foreach collection="goodsReviewDOLst" item="review" separator=",">
			(#{review.skuId},#{review.buyerNick},#{review.shopId},#{review.csNick},#{review.orderId},#{review.orderCreated},#{review.orderPayTime},#{review.score},#{review.sendTime},#{review.content},#{review.isReply},#{review.status})
		</foreach>
	</insert>

	<delete id="deleteShopGoodsReviewByDateByShopId" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId,jdbcType=BIGINT}
		AND send_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND
		#{endDate,jdbcType=TIMESTAMP}
	</delete>

	<select id="selectShopCsReviewByOrderId" parameterType="map" resultMap="ShopGoodsReviewDTO">
		SELECT *
		FROM ${tableName}
		WHERE order_id IN
		<foreach collection="csGoodsReviewLst" item="csGoodsReview" open="("
			close=")" separator=",">
			#{csGoodsReview.orderId}
		</foreach>
		AND shop_id = #{shopId}
	</select>

	<select id="selectShopCsOrderByCsBySendTime" parameterType="map" resultMap="ShopGoodsReviewDTO">
		SELECT *
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND cs_nick IN
		<foreach collection="csLst" item="csDTO" open="("
			close=")" separator=",">
			#{csDTO.nick}
		</foreach>
		AND send_time BETWEEN #{startDate} AND #{endDate}
	</select>

	<select id="selectShopCsOrderBySendTime" parameterType="map" resultMap="ShopGoodsReviewDO">
		SELECT sku_id,buyer_nick,shop_id,cs_nick,order_id,order_created,order_pay_time,score,send_time,content,is_reply,status
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND send_time BETWEEN #{startDate} AND #{endDate}
	</select>

</mapper>