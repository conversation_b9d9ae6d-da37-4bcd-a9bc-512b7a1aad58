<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopCategoryDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="BIGINT" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="level" property="level" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, name, category_id, parent_id, level, status
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from pes_shop_category_773035
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByShopCategoryTableName" parameterType="map" >
    delete from ${tableName}
    where shop_id = #{shopId,jdbcType=BIGINT}
  </delete>
    <insert id="batchInsertShopCategory" parameterType="map">
	INSERT INTO ${tableName}(shop_id, name,
      category_id, parent_id, level,
      status)
	VALUES
	<foreach collection="shopCategoryLst" item="itm" separator="," >
	   	(
	   	  #{itm.shopId,jdbcType=BIGINT},
	      #{itm.name,jdbcType=VARCHAR},
	      #{itm.categoryId,jdbcType=BIGINT},
	      #{itm.parentId,jdbcType=BIGINT},
	      #{itm.level,jdbcType=BIGINT},
	      #{itm.status,jdbcType=TINYINT}
      	)
      </foreach>
	</insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopCategoryDO" >
    insert into pes_shop_category_773035
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="categoryId != null" >
        category_id,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="level != null" >
        level,
      </if>
      <if test="status != null" >
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null" >
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null" >
        #{level,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopCategoryDO" >
    update pes_shop_category_773035
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="level != null" >
        level = #{level,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ShopCategoryDO" >
    update pes_shop_category_773035
    set shop_id = #{shopId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=BIGINT},
      parent_id = #{parentId,jdbcType=BIGINT},
      level = #{level,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectMissingCategory" resultType="java.lang.Long">
    SELECT
      DISTINCT g.category_id
    FROM
      ${shopGoodSkuTable} g
        LEFT JOIN
      ${shopCategoryTable} c
      ON
        c.shop_id = g.shop_id
          AND c.category_id = g.category_id
    WHERE
      g.shop_id = #{shopId}
      AND c.category_id IS NULL
    ORDER BY g.category_id DESC
  </select>

</mapper>
