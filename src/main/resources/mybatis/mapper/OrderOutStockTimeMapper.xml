<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderOutStockTimeMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderOutStockTimeDTO" >
    <id column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="order_type" property="orderType" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    order_id, created, out_stock_time, shop_id, order_type
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pes_order_out_stock_time
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  
  <select id="selectOrderOutStockTimeByShopId" resultMap="BaseResultMap" parameterType="map" >
   	SELECT
	 <include refid="Base_Column_List" />
	FROM ${tableName}
	WHERE
		shop_id = #{shopId,jdbcType=BIGINT}
		and order_id in
		<foreach collection="orderIdSet" index="index" item="oid" open="("
			separator="," close=")">
			#{oid}
		</foreach>
  </select>
  
    <select id="selectOrderOutStockTimeByShopIdAndDate" resultMap="BaseResultMap" parameterType="map" >
   	SELECT
	 <include refid="Base_Column_List" />
	FROM ${tableName}
	WHERE
		shop_id = #{shopId,jdbcType=BIGINT}
		and out_stock_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
   </select>
  <delete id="deleteByOrderId" parameterType="map" >
    delete from ${tableName}
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <insert id="insertOrderOutStockTime" parameterType="map" >
    insert into ${tableName} (order_id, created, out_stock_time, 
      shop_id, order_type, status)
    values (#{orderOutStockTimeDTO.orderId,jdbcType=BIGINT}, #{orderOutStockTimeDTO.created,jdbcType=TIMESTAMP}, #{orderOutStockTimeDTO.outStockTime,jdbcType=TIMESTAMP}, 
      #{orderOutStockTimeDTO.shopId,jdbcType=BIGINT}, #{orderOutStockTimeDTO.orderType,jdbcType=INTEGER}, #{orderOutStockTimeDTO.status,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DTO.OrderOutStockTimeDTO" >
    insert into pes_order_out_stock_time
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="created != null" >
        created,
      </if>
      <if test="outStockTime != null" >
        out_stock_time,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="created != null" >
        #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="outStockTime != null" >
        #{outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DTO.OrderOutStockTimeDTO" >
    update pes_order_out_stock_time
    <set >
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="outStockTime != null" >
        out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DTO.OrderOutStockTimeDTO" >
    update pes_order_out_stock_time
    set created = #{created,jdbcType=TIMESTAMP},
      out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
      shop_id = #{shopId,jdbcType=BIGINT}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
</mapper>