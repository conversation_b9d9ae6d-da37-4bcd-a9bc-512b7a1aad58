<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.CsServiceSendEvalMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsServiceSendEvalDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="customer" jdbcType="VARCHAR" property="customer" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="eval_time" jdbcType="TIMESTAMP" property="evalTime" />
    <result column="eval_code" jdbcType="INTEGER" property="evalCode" />
    <result column="sid" jdbcType="VARCHAR" property="sid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, cs_nick, customer, send_time, eval_time, eval_code, sid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_cs_service_send_eval
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchByDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from ${tableName}
    where send_time between #{startDate} and #{endDate} and shop_id = #{shopId} and cs_nick = #{nick}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_service_send_eval
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CsServiceSendEvalDO">
    insert into pes_cs_service_send_eval (id, shop_id, cs_nick, 
      customer, send_time, eval_time, 
      eval_code, sid)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, 
      #{customer,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{evalTime,jdbcType=TIMESTAMP}, 
      #{evalCode,jdbcType=INTEGER}, #{sid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsServiceSendEvalDO">
    insert into pes_cs_service_send_eval
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="customer != null">
        customer,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="evalTime != null">
        eval_time,
      </if>
      <if test="evalCode != null">
        eval_code,
      </if>
      <if test="sid != null">
        sid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="customer != null">
        #{customer,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evalTime != null">
        #{evalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evalCode != null">
        #{evalCode,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsServiceSendEvalDO">
    update pes_cs_service_send_eval
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="customer != null">
        customer = #{customer,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evalTime != null">
        eval_time = #{evalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evalCode != null">
        eval_code = #{evalCode,jdbcType=INTEGER},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsServiceSendEvalDO">
    UPDATE pes_cs_service_send_eval
    SET shop_id = #{shopId,jdbcType=BIGINT},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      customer = #{customer,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      eval_time = #{evalTime,jdbcType=TIMESTAMP},
      eval_code = #{evalCode,jdbcType=INTEGER},
      sid = #{sid,jdbcType=VARCHAR}
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>