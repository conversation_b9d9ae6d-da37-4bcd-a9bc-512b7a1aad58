<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsPerformancePreordainMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsPerformancePreordainDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="consult_buyer_num" jdbcType="INTEGER" property="consultBuyerNum"/>
        <result column="enquiry_buyer_num" jdbcType="INTEGER" property="enquiryBuyerNum"/>
        <result column="enquiry_ordered_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBuyerNum"/>
        <result column="enquiry_ordered_sku_num" jdbcType="INTEGER" property="enquiryOrderedSkuNum"/>
        <result column="enquiry_ordered_paid_buyer_num" jdbcType="INTEGER" property="enquiryOrderedPaidBuyerNum"/>
        <result column="enquiry_ordered_paid_sku_num" jdbcType="INTEGER" property="enquiryOrderedPaidSkuNum"/>
        <result column="enquiry_ordered_paid_amount" jdbcType="DOUBLE" property="enquiryOrderedPaidAmount"/>
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum"/>
        <result column="ordered_sku_num" jdbcType="INTEGER" property="orderedSkuNum"/>
        <result column="ordered_paid_buyer_num" jdbcType="INTEGER" property="orderedPaidBuyerNum"/>
        <result column="ordered_paid_sku_num" jdbcType="INTEGER" property="orderedPaidSkuNum"/>
        <result column="ordered_paid_amount" jdbcType="DOUBLE" property="orderedPaidAmount"/>
    </resultMap>

    <sql id="base_field">
  	id,shop_id,`date`,cs_nick,sku_id,sku_name,activity_id,consult_buyer_num,enquiry_buyer_num,
  	enquiry_ordered_buyer_num,
    enquiry_ordered_sku_num,
    enquiry_ordered_paid_buyer_num,
    enquiry_ordered_paid_sku_num,
    enquiry_ordered_paid_amount,
  	ordered_buyer_num,
  	ordered_sku_num,
  	ordered_paid_buyer_num,
  	ordered_paid_sku_num,
  	ordered_paid_amount
  </sql>

    <insert id="batchInsertCsPerformancePreordain" parameterType="com.pes.jd.model.DO.CsPerformancePreordainDO">
        INSERT INTO ${tableName}
        (
        shop_id,`date`,cs_nick,sku_id,sku_name,activity_id,consult_buyer_num,enquiry_buyer_num,
        enquiry_ordered_buyer_num,
        enquiry_ordered_sku_num,
        enquiry_ordered_paid_buyer_num,
        enquiry_ordered_paid_sku_num,
        enquiry_ordered_paid_amount,
        ordered_buyer_num,
        ordered_sku_num,
        ordered_paid_buyer_num,
        ordered_paid_sku_num,
        ordered_paid_amount
        )
        VALUES
        <foreach collection="performances" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.csNick,jdbcType=VARCHAR},
            #{itm.skuId,jdbcType=INTEGER},
            #{itm.skuName,jdbcType=VARCHAR},
            #{itm.activityId,jdbcType=VARCHAR},
            #{itm.consultBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedSkuNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedPaidBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedPaidSkuNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedPaidAmount,jdbcType=DOUBLE},
            #{itm.orderedBuyerNum,jdbcType=INTEGER},
            #{itm.orderedSkuNum,jdbcType=INTEGER},
            #{itm.orderedPaidBuyerNum,jdbcType=INTEGER},
            #{itm.orderedPaidSkuNum,jdbcType=INTEGER},
            #{itm.orderedPaidAmount,jdbcType=DOUBLE}
            )
        </foreach>

    </insert>

    <delete id="deleteByShopIdAndDateAndActivityId">
        DELETE FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND `date` BETWEEN #{startDate} AND #{endDate}
        AND activity_id = #{activityId,jdbcType=VARCHAR}
    </delete>
</mapper>