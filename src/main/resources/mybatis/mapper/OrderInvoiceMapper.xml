<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.OrderInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderInvoiceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content_id" jdbcType="VARCHAR" property="contentId" />
    <result column="consignee_email" jdbcType="VARCHAR" property="consigneeEmail" />
    <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="vat_no" jdbcType="VARCHAR" property="vatNo" />
    <result column="vat_address_registered" jdbcType="VARCHAR" property="vatAddressRegistered" />
    <result column="vat_phone_registered" jdbcType="VARCHAR" property="vatPhoneRegistered" />
    <result column="vat_deposit_bank" jdbcType="VARCHAR" property="vatDepositBank" />
    <result column="vat_bank_account" jdbcType="VARCHAR" property="vatBankAccount" />
    <result column="vat_user_address" jdbcType="VARCHAR" property="vatUserAddress" />
    <result column="vat_user_name" jdbcType="VARCHAR" property="vatUserName" />
    <result column="vat_user_phone" jdbcType="VARCHAR" property="vatUserPhone" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
    <result column="invoice_info" jdbcType="VARCHAR" property="invoiceInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, invoice_type, title, content_id, consignee_email, consignee_phone, 
    invoice_code, vat_no, vat_address_registered, vat_phone_registered, vat_deposit_bank, 
    vat_bank_account, vat_user_address, vat_user_name, vat_user_phone, created_time, 
    modified_time,invoice_info
  </sql>
	
	<insert id="insertOrderInvoice"  parameterType="map">
    insert into ${tableName}   
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderInvoice.orderId != null">
        order_id,
      </if>
      <if test="orderInvoice.invoiceType != null">
        invoice_type,
      </if>
      <if test="orderInvoice.title != null">
        title,
      </if>
      <if test="orderInvoice.contentId != null">
        content_id,
      </if>
      <if test="orderInvoice.consigneeEmail != null">
        consignee_email,
      </if>
      <if test="orderInvoice.consigneePhone != null">
        consignee_phone,
      </if>
      <if test="orderInvoice.invoiceCode != null">
        invoice_code,
      </if>
      <if test="orderInvoice.vatNo != null">
        vat_no,
      </if>
      <if test="orderInvoice.vatAddressRegistered != null">
        vat_address_registered,
      </if>
      <if test="orderInvoice.vatPhoneRegistered != null">
        vat_phone_registered,
      </if>
      <if test="orderInvoice.vatDepositBank != null">
        vat_deposit_bank,
      </if>
      <if test="orderInvoice.vatBankAccount != null">
        vat_bank_account,
      </if>
      <if test="orderInvoice.vatUserAddress != null">
        vat_user_address,
      </if>
      <if test="orderInvoice.vatUserName != null">
        vat_user_name,
      </if>
      <if test="orderInvoice.vatUserPhone != null">
        vat_user_phone,
      </if>
      <if test="orderInvoice.createdTime != null">
        created_time,
      </if>
      <if test="orderInvoice.modifiedTime != null">
        modified_time,
      </if>
       <if test="orderInvoice.invoiceInfo != null">
        invoice_info,
      </if>
      
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderInvoice.orderId != null">
        #{orderInvoice.orderId,jdbcType=BIGINT},
      </if>
      <if test="orderInvoice.invoiceType != null">
        #{orderInvoice.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="orderInvoice.title != null">
        #{orderInvoice.title,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.contentId != null">
        #{orderInvoice.contentId,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.consigneeEmail != null">
        #{orderInvoice.consigneeEmail,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.consigneePhone != null">
        #{orderInvoice.consigneePhone,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.invoiceCode != null">
        #{orderInvoice.invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatNo != null">
        #{orderInvoice.vatNo,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatAddressRegistered != null">
        #{orderInvoice.vatAddressRegistered,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatPhoneRegistered != null">
        #{orderInvoice.vatPhoneRegistered,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatDepositBank != null">
        #{orderInvoice.vatDepositBank,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatBankAccount != null">
        #{orderInvoice.vatBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatUserAddress != null">
        #{orderInvoice.vatUserAddress,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatUserName != null">
        #{orderInvoice.vatUserName,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.vatUserPhone != null">
        #{orderInvoice.vatUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoice.createdTime != null">
        #{orderInvoice.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderInvoice.modifiedTime != null">
        #{orderInvoice.modifiedTime,jdbcType=TIMESTAMP},
      </if>
        <if test="orderInvoice.invoiceInfo != null">
        #{orderInvoice.invoiceInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  
   <delete id="deleteOrderByOrderId" parameterType="map">
        DELETE FROM ${tableName}
        where
		order_id = #{oid,jdbcType=BIGINT}
    </delete>
  
</mapper>