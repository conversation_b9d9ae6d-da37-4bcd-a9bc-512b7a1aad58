<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsWarningMapper" >

  <resultMap id="CsWarningDO" type="com.pes.jd.model.DO.CsWarningDO" >
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="shop_id" jdbcType="BIGINT" property="shopId" />
      <result column="date" jdbcType="DATE" property="date" />
      <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
      <result column="customer" jdbcType="VARCHAR" property="customer" />
      <result column="sid" jdbcType="VARCHAR" property="sid" />
      <result column="session_begin_time" jdbcType="TIMESTAMP" property="sessionBeginTime" />
      <result column="session_end_time" jdbcType="TIMESTAMP" property="sessionEndTime" />
      <result column="warning_type" jdbcType="TINYINT" property="warningType" />
      <result column="keyword" jdbcType="VARCHAR" property="keyword" />
  </resultMap>

    <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, customer, sid, session_begin_time, session_end_time,
    warning_type, keyword
    </sql>

  <insert id="batchInsertCsWarning" parameterType="map" >
    INSERT INTO ${tableName} (shop_id, date,
      cs_nick, customer, sid,
      session_begin_time, session_end_time, warning_type,
      keyword)
    VALUES 
    <foreach collection="csWarningList" item="csWarning" separator=",">
	    (#{shopId,jdbcType=BIGINT}, #{csWarning.date,jdbcType=DATE},
        #{csWarning.csNick,jdbcType=VARCHAR}, #{csWarning.customer,jdbcType=VARCHAR},
        #{csWarning.sid,jdbcType=VARCHAR},#{csWarning.sessionBeginTime,jdbcType=TIMESTAMP},
        #{csWarning.sessionEndTime,jdbcType=TIMESTAMP}, #{csWarning.warningType,jdbcType=TINYINT},
        #{csWarning.keyword,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <delete id="batchDeleteCsWarningByDateByType" parameterType="map" >
    DELETE FROM ${tableName} 
    WHERE date BETWEEN #{startDate} AND #{endDate}
    <if test="type != null and type != 0">
    	AND warning_type = #{type}
    </if>
    AND shop_id = #{shopId,jdbcType=BIGINT}
  </delete>
    <delete id="batchDeleteCsWarningByDateByTypeAndCs">
        DELETE FROM ${tableName}
<!--        WHERE date BETWEEN #{startDate} AND #{endDate}-->
        WHERE date = #{startDate}
        <if test="type != null and type != 0">
            AND warning_type = #{type}
        </if>
        AND shop_id = #{shopId,jdbcType=BIGINT}
        AND cs_nick=#{csNick,jdbcType=VARCHAR}
    </delete>

    <!--  <select id="selectCsSlowResponseWarningLst" parameterType="map" resultMap="CsWarningDO">-->
<!--    SELECT * FROM ${tableName} -->
<!--    WHERE shop_id = #{shopId,jdbcType=BIGINT}-->
<!--	AND cs_nick IN -->
<!--	<foreach collection="csLst" item="cs" open="(" close=")" separator=",">-->
<!--		#{cs.nick}-->
<!--	</foreach>-->
<!--    AND warning_time BETWEEN #{startDate} AND #{endDate}-->
<!--    <if test="type != null and type != 0">-->
<!--    	AND warning_type = #{type}-->
<!--    </if>-->
<!--  </select>-->

<!--    <select id="selectTotalWarnByShopIdAndDate" parameterType="map" resultMap="CsWarningDO">-->
<!--    SELECT * FROM ${tableName}-->
<!--    WHERE shop_id = #{shopId,jdbcType=BIGINT}-->
<!--    AND warning_time BETWEEN #{startDate} AND #{endDate}-->
<!--  </select>-->
  
</mapper>