<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsOrderIndexAndBindMapper" >

  <resultMap id="CsOrderIndexDTO" type="com.pes.jd.model.DTO.CsOrderIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="banner_flag" jdbcType="TINYINT" property="bannerFlag" />
    <result column="order_created" jdbcType="TIMESTAMP" property="orderCreated" />
    <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate" />
    <result column="order_payment" jdbcType="DOUBLE" property="orderPayment" />
    <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum" />
    <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee" />
    <result column="is_goods_filte" jdbcType="BIT" property="isGoodsFilte" />
    <result column="is_mrn_filter" jdbcType="BIT" property="isMrnFilter" />
    <result column="bc_chat_num" jdbcType="INTEGER" property="bcChatNum" />
    <result column="bc_reply_num" jdbcType="INTEGER" property="bcReplyNum" />
    <result column="bc_chat_round_num" jdbcType="INTEGER" property="bcChatRoundNum" />
    <result column="bc_first_reply_date" jdbcType="TIMESTAMP" property="bcFirstReplyDate" />
    <result column="bc_last_reply_date" jdbcType="TIMESTAMP" property="bcLastReplyDate" />
    <result column="bc_first_chat_date" jdbcType="TIMESTAMP" property="bcFirstChatDate" />
    <result column="bc_last_chat_date" jdbcType="TIMESTAMP" property="bcLastChatDate" />
    <result column="bp_reply_num" jdbcType="INTEGER" property="bpReplyNum" />
    <result column="bp_chat_round_num" jdbcType="INTEGER" property="bpChatRoundNum" />
    <result column="bp_first_reply_date" jdbcType="TIMESTAMP" property="bpFirstReplyDate" />
    <result column="bp_last_chat_date" jdbcType="TIMESTAMP" property="bpLastChatDate" />
    <result column="bp_last_reply_date" jdbcType="TIMESTAMP" property="bpLastReplyDate" />
    <result column="bp_chat_num" jdbcType="INTEGER" property="bpChatNum" />
    <result column="ac_first_reply_date" jdbcType="TIMESTAMP" property="acFirstReplyDate" />
    <result column="ac_first_chat_date" jdbcType="TIMESTAMP" property="acFirstChatDate" />
    <result column="ap_first_chat_date" jdbcType="TIMESTAMP" property="apFirstChatDate" />
    <result column="ap_first_reply_date" jdbcType="TIMESTAMP" property="apFirstReplyDate" />
    <result column="last_reply_date" jdbcType="TIMESTAMP" property="lastReplyDate" />
    <result column="first_reply_date" jdbcType="TIMESTAMP" property="firstReplyDate" />
    <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
    <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate" />
   </resultMap>


	<select id="selectShopCsOrderIndexLstByCsAndDateAndBuyerLst" resultMap="CsOrderIndexDTO">
		SELECT t1.*
		FROM ${csOrderIndexTableName} t1
		WHERE
			t1.cs_nick = #{csNick,jdbcType=VARCHAR}
		AND t1.date = #{date,jdbcType=DATE}
		AND t1.buyer_nick IN
		<foreach collection="buyerNickLst" item="buyerNick" open="(" close=")"  separator=",">
		 	#{buyerNick,jdbcType=VARCHAR}
		</foreach>
		AND NOT EXISTS (SELECT 1 FROM ${csOrderBindTableName} t2 WHERE t2.order_id = t1.order_id)
		AND t1.shop_id = #{shopId,jdbcType=BIGINT}
	</select>

</mapper>