<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsDutyRecordMapper">

    <resultMap id="CsDutyRecordDO" type="com.pes.jd.model.DO.CsDutyRecordDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="first_online_datetime" property="firstOnlineDateTime" jdbcType="TIMESTAMP"/>
        <result column="last_offline_datetime" property="lastOfflineDateTime" jdbcType="TIMESTAMP"/>
        <result column="login_times_num" property="loginTimesNum" jdbcType="INTEGER"/>
        <result column="login_duration_time" property="loginDurationTime" jdbcType="BIGINT"/>
        <result column="rceive_duration_time" property="rceiveDurationTime" jdbcType="BIGINT"/>
        <result column="hangup_duration_time" property="hangupDurationTime" jdbcType="BIGINT"/>
        <result column="offline_duration_time" property="offlineDurationTime" jdbcType="BIGINT"/>
        <result column="suspend_num" property="suspendNum" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, shop_id, cs_nick, date, first_online_datetime, last_offline_datetime, login_times_num,
    login_duration_time, rceive_duration_time, hangup_duration_time, offline_duration_time,suspend_num
  </sql>

    <insert id="insertBatch" parameterType="map">
        INSERT INTO ${tableName} (shop_id,cs_nick,date,first_online_datetime,last_offline_datetime,
        login_times_num,login_duration_time,rceive_duration_time,hangup_duration_time,offline_duration_time,suspend_num)
        VALUES
        <foreach collection="csDutyRecordDOList" item="csDutyRecord" separator=",">
            (#{csDutyRecord.shopId,jdbcType=BIGINT},#{csDutyRecord.csNick,jdbcType=VARCHAR},
            #{csDutyRecord.date,jdbcType=DATE},
            #{csDutyRecord.firstOnlineDateTime,jdbcType=TIMESTAMP},#{csDutyRecord.lastOfflineDateTime,jdbcType=TIMESTAMP},#{csDutyRecord.loginTimesNum,jdbcType=INTEGER},
            #{csDutyRecord.loginDurationTime,jdbcType=BIGINT},#{csDutyRecord.rceiveDurationTime,jdbcType=BIGINT},#{csDutyRecord.hangupDurationTime,jdbcType=BIGINT},#{csDutyRecord.offlineDurationTime,jdbcType=BIGINT},
            #{csDutyRecord.suspendNum,jdbcType=INTEGER})
        </foreach>
    </insert>

    <delete id="deleteCsDutyRecordByShopIdByDate" parameterType="map">
    DELETE FROM ${tableName}
    WHERE shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>
    <select id="searchByShopDate" resultMap="CsDutyRecordDO">

        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where shop_id = #{shopId}
        and date between #{beginDate} and #{endDate}
    </select>

</mapper>