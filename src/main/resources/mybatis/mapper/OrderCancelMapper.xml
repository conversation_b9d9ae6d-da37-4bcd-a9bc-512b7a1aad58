<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderCancelMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderCancelDTO" >
    <id column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="trade_id" property="tradeId" jdbcType="BIGINT" />
    <result column="seller_nick" property="sellerNick" jdbcType="VARCHAR" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="payment" property="payment" jdbcType="DOUBLE" />
    <result column="post_fee" property="postFee" jdbcType="DOUBLE" />
    <result column="consign_time" property="consignTime" jdbcType="TIMESTAMP" />
    <result column="num" property="num" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="total_fee" property="totalFee" jdbcType="DOUBLE" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="seller_flag" property="sellerFlag" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="step_trade_status" property="stepTradeStatus" jdbcType="VARCHAR" />
    <result column="step_paid_fee" property="stepPaidFee" jdbcType="DOUBLE" />
    <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
    <result column="pay_type" property="payType" jdbcType="TINYINT" />
    <result column="seller_discount" property="sellerDiscount" jdbcType="DOUBLE" />
    <result column="order_type" property="orderType" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    order_id, trade_id, seller_nick, date, shop_id, payment, post_fee, consign_time, 
    num, status, total_fee, created, pay_time, modified, end_time, buyer_nick, seller_flag, 
    type, step_trade_status, step_paid_fee, out_stock_time, pay_type, seller_discount, 
    order_type
  </sql>
  <select id="selectOrderCancelByShopIdAndDate" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from ${tableName}
    where 
    shop_id = #{shopId,jdbcType=BIGINT}
    AND
    created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pes_order_cancel_2019_04
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DTO.OrderCancelDTO" >
    insert into pes_order_cancel_2019_04 (order_id, trade_id, seller_nick, 
      date, shop_id, payment, post_fee, 
      consign_time, num, status, 
      total_fee, created, pay_time, 
      modified, end_time, buyer_nick, 
      seller_flag, type, step_trade_status, 
      step_paid_fee, out_stock_time, pay_type, 
      seller_discount, order_type)
    values (#{orderId,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT}, #{sellerNick,jdbcType=VARCHAR}, 
      #{date,jdbcType=DATE}, #{shopId,jdbcType=BIGINT}, #{payment,jdbcType=DOUBLE}, #{postFee,jdbcType=DOUBLE}, 
      #{consignTime,jdbcType=TIMESTAMP}, #{num,jdbcType=INTEGER}, #{status,jdbcType=VARCHAR}, 
      #{totalFee,jdbcType=DOUBLE}, #{created,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP}, 
      #{modified,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{buyerNick,jdbcType=VARCHAR}, 
      #{sellerFlag,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{stepTradeStatus,jdbcType=VARCHAR}, 
      #{stepPaidFee,jdbcType=DOUBLE}, #{outStockTime,jdbcType=TIMESTAMP}, #{payType,jdbcType=TINYINT}, 
      #{sellerDiscount,jdbcType=DOUBLE}, #{orderType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DTO.OrderCancelDTO" >
    insert into pes_order_cancel_2019_04
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="tradeId != null" >
        trade_id,
      </if>
      <if test="sellerNick != null" >
        seller_nick,
      </if>
      <if test="date != null" >
        date,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="payment != null" >
        payment,
      </if>
      <if test="postFee != null" >
        post_fee,
      </if>
      <if test="consignTime != null" >
        consign_time,
      </if>
      <if test="num != null" >
        num,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="totalFee != null" >
        total_fee,
      </if>
      <if test="created != null" >
        created,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="modified != null" >
        modified,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="buyerNick != null" >
        buyer_nick,
      </if>
      <if test="sellerFlag != null" >
        seller_flag,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="stepTradeStatus != null" >
        step_trade_status,
      </if>
      <if test="stepPaidFee != null" >
        step_paid_fee,
      </if>
      <if test="outStockTime != null" >
        out_stock_time,
      </if>
      <if test="payType != null" >
        pay_type,
      </if>
      <if test="sellerDiscount != null" >
        seller_discount,
      </if>
      <if test="orderType != null" >
        order_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null" >
        #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="sellerNick != null" >
        #{sellerNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null" >
        #{date,jdbcType=DATE},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="payment != null" >
        #{payment,jdbcType=DOUBLE},
      </if>
      <if test="postFee != null" >
        #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="consignTime != null" >
        #{consignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="totalFee != null" >
        #{totalFee,jdbcType=DOUBLE},
      </if>
      <if test="created != null" >
        #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyerNick != null" >
        #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="sellerFlag != null" >
        #{sellerFlag,jdbcType=BIGINT},
      </if>
      <if test="type != null" >
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="stepTradeStatus != null" >
        #{stepTradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="stepPaidFee != null" >
        #{stepPaidFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockTime != null" >
        #{outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null" >
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="sellerDiscount != null" >
        #{sellerDiscount,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DTO.OrderCancelDTO" >
    update pes_order_cancel_2019_04
    <set >
      <if test="tradeId != null" >
        trade_id = #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="sellerNick != null" >
        seller_nick = #{sellerNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="payment != null" >
        payment = #{payment,jdbcType=DOUBLE},
      </if>
      <if test="postFee != null" >
        post_fee = #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="consignTime != null" >
        consign_time = #{consignTime,jdbcType=TIMESTAMP},
      </if>
      <if test="num != null" >
        num = #{num,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="totalFee != null" >
        total_fee = #{totalFee,jdbcType=DOUBLE},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyerNick != null" >
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="sellerFlag != null" >
        seller_flag = #{sellerFlag,jdbcType=BIGINT},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="stepTradeStatus != null" >
        step_trade_status = #{stepTradeStatus,jdbcType=VARCHAR},
      </if>
      <if test="stepPaidFee != null" >
        step_paid_fee = #{stepPaidFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockTime != null" >
        out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payType != null" >
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="sellerDiscount != null" >
        seller_discount = #{sellerDiscount,jdbcType=DOUBLE},
      </if>
      <if test="orderType != null" >
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DTO.OrderCancelDTO" >
    update pes_order_cancel_2019_04
    set trade_id = #{tradeId,jdbcType=BIGINT},
      seller_nick = #{sellerNick,jdbcType=VARCHAR},
      date = #{date,jdbcType=DATE},
      shop_id = #{shopId,jdbcType=BIGINT},
      payment = #{payment,jdbcType=DOUBLE},
      post_fee = #{postFee,jdbcType=DOUBLE},
      consign_time = #{consignTime,jdbcType=TIMESTAMP},
      num = #{num,jdbcType=INTEGER},
      status = #{status,jdbcType=VARCHAR},
      total_fee = #{totalFee,jdbcType=DOUBLE},
      created = #{created,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      modified = #{modified,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      seller_flag = #{sellerFlag,jdbcType=BIGINT},
      type = #{type,jdbcType=VARCHAR},
      step_trade_status = #{stepTradeStatus,jdbcType=VARCHAR},
      step_paid_fee = #{stepPaidFee,jdbcType=DOUBLE},
      out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
      pay_type = #{payType,jdbcType=TINYINT},
      seller_discount = #{sellerDiscount,jdbcType=DOUBLE},
      order_type = #{orderType,jdbcType=TINYINT}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
</mapper>