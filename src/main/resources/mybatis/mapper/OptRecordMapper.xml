<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.OptRecordMapper">
  <resultMap id="OptRecordDO" type="com.pes.jd.model.DO.OptRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="opt_nick" jdbcType="VARCHAR" property="optNick" />
    <result column="opt_name" jdbcType="VARCHAR" property="optName" />
    <result column="req_uri" jdbcType="VARCHAR" property="reqUri" />
    <result column="opt_time" jdbcType="TIMESTAMP" property="optTime" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="opt_type" jdbcType="VARCHAR" property="optType" />
     <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  
  <sql id="base_field">
    id, shop_id, opt_nick, opt_name, req_uri, opt_time, type, opt_type
  </sql>
 
   <insert id="insertOptRecord" parameterType="com.pes.jd.model.DO.OptRecord">
    insert into pes_opt_record 
    (		shop_id, opt_nick, 
	      	opt_name, req_uri, opt_time, 
	      	type, opt_type, content
	 )
    VALUES 
    (
		  #{shopId,jdbcType=BIGINT}, #{optNick,jdbcType=VARCHAR}, 
	      #{optName,jdbcType=VARCHAR}, #{reqUri,jdbcType=VARCHAR}, #{optTime,jdbcType=TIMESTAMP}, 
	      #{type,jdbcType=VARCHAR}, #{optType,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  
  <delete id="deleteOptRecordById" parameterType="java.lang.Long">
    DELETE FROM pes_opt_record
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
 
  <update id="updateOptRecordBySelective" parameterType="com.pes.jd.model.DO.OptRecord">
    UPDATE pes_opt_record
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="optNick != null">
        opt_nick = #{optNick,jdbcType=VARCHAR},
      </if>
      <if test="optName != null">
        opt_name = #{optName,jdbcType=VARCHAR},
      </if>
      <if test="reqUri != null">
        req_uri = #{reqUri,jdbcType=VARCHAR},
      </if>
      <if test="optTime != null">
        opt_time = #{optTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        opt_type = #{optType,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
<!--  <select id="getOptRecordById" parameterType="java.lang.Long" resultMap="OptRecordDO">
    SELECT 
   		 <include refid="base_field" />
    FROM pes_opt_record
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select> -->
</mapper>