<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.LossOrderRecordMapper" >
  
	<resultMap  id="LossOrderRecordDTO" type="com.pes.jd.model.DTO.LossOrderRecordDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="order_id" property="orderId" jdbcType="BIGINT" />
		<result column="order_payment" property="orderPayment" jdbcType="DOUBLE" />
		<result column="order_created" property="orderCreated" jdbcType="TIMESTAMP" />
		<result column="order_goods_num" property="orderGoodsNum" jdbcType="INTEGER" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="start_datetime" property="startDateTime" jdbcType="TIMESTAMP" />
		<result column="end_datetime" property="endDateTime" jdbcType="TIMESTAMP" />
		<result column="is_chat_after_ordered" property="isChatAfterOrdered" jdbcType="BIT" />
		<result column="order_chat_type" property="orderChatType" jdbcType="INTEGER" />
		<result column="type" property="type" jdbcType="INTEGER" />
	</resultMap>
		
	<insert id="insertBatchShopLossOrderEnquiryLoss" parameterType="map">
		INSERT INTO ${tableName} (shop_id,cs_nick,date,customer,order_created,order_id,order_payment,order_goods_num, 
			start_datetime,end_datetime,is_chat_after_ordered,order_chat_type,type) 
		VALUES
		<foreach collection="lossOrderRecordDOList" item="lossRecord" separator="," >
			(#{lossRecord.shopId},#{lossRecord.csNick},#{lossRecord.date},#{lossRecord.customer},#{lossRecord.orderCreated},#{lossRecord.orderId},
			#{lossRecord.orderPayment},#{lossRecord.orderGoodsNum},#{lossRecord.startDateTime},#{lossRecord.endDateTime},
			#{lossRecord.isChatAfterOrdered},#{lossRecord.orderChatType},#{lossRecord.type})
		</foreach>
	</insert>
		
	<delete id="deleteShopLossOrderByCratedDateByType" parameterType="map">
		DELETE FROM ${tableName}
		WHERE order_created BETWEEN #{startDate} and #{endDate}
		AND type = #{lostType}
		AND shop_id = #{shopId}
	</delete>
		
	<select id="selectLossEnquiryOrderIdByDate" parameterType="map" resultType="java.lang.Long">
		SELECT order_id 
		FROM ${tableName}
		WHERE order_created BETWEEN #{startDate} and #{endDate}
		AND type = #{lostType}
		AND shop_id = #{shopId}
	</select>
		
	<select id="selectLossEnquiryOrderByDate" parameterType="map" resultMap="LossOrderRecordDTO">
		SELECT order_id,cs_nick,customer,order_payment,order_goods_num
		FROM ${tableName}
		WHERE order_created BETWEEN #{startDate} AND #{endDate}
		AND type = #{lostType}
		AND shop_id = #{shopId}
	</select>
		
	<select id="selectOrderPaymentLossByDateForCalculatePaymentLoss" parameterType="map" resultMap="LossOrderRecordDTO">
		SELECT *
		FROM ${tableName}
		WHERE order_created BETWEEN #{startDate} AND #{endDate}
		AND is_chat_after_ordered = 1	<!-- 下单后有聊天 -->
		AND order_chat_type = 2 <!-- 客服主动聊天 -->
		AND type = 2 <!-- 静默下单流失 -->
		AND shop_id = #{shopId}
	</select>
</mapper>