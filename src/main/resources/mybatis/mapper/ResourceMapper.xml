<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ResourceMapper">

  <resultMap id="ResourceDO" type="com.pes.jd.model.DO.Resource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="controller" jdbcType="VARCHAR" property="controller" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort" jdbcType="VARCHAR" property="sort" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
  </resultMap>
  <sql id="base_field">
    id, url, note, controller, title, name, sort, parent_id, type, is_default
  </sql>
  
   <insert id="insertResource" parameterType="com.pes.jd.model.DO.Resource">
    INSERT INTO pes_resource (url, note,  controller, title, name,  sort, parent_id, type, is_default)
    VALUES 
    ( 
   	  #{url,jdbcType=VARCHAR}, #{note,jdbcType=VARCHAR}, 
      #{controller,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{sort,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=BIT}
     )
  </insert>
  
  <delete id="deleteResourceById" parameterType="java.lang.Long">
    DELETE FROM pes_resource
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
 
  <update id="updateResourceBySelective" parameterType="com.pes.jd.model.DO.Resource">
    UPDATE pes_resource
    <set>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="note != null">
        note = #{note,jdbcType=VARCHAR},
      </if>
      <if test="controller != null">
        controller = #{controller,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
<!--   
   <select id="getResourceById" parameterType="java.lang.Long" resultMap="ResourceDO">
    SELECT 
    	<include refid="base_field" />
    FROM pes_resource
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select> -->
</mapper>