<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopUseAnalysisMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopUseAnalysisDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="screen_valid_subscribe_num" property="screenValidSubscribeNum" jdbcType="INTEGER" />
    <result column="login_num" property="loginNum" jdbcType="INTEGER" />
    <result column="login_use_num" property="loginUseNum" jdbcType="INTEGER" />
    <result column="one_step_user_num" property="oneStepUserNum" jdbcType="INTEGER" />
    <result column="one_step_execute_num" property="oneStepExecuteNum" jdbcType="INTEGER" />
    <result column="one_step_allocated_num" property="oneStepAllocatedNum" jdbcType="INTEGER" />
    <result column="one_step_urge_amount" property="oneStepUrgeAmount" jdbcType="DOUBLE" />
    <result column="batch_remind_user_num" property="batchRemindUserNum" jdbcType="INTEGER" />
    <result column="batch_remind_execute_num" property="batchRemindExecuteNum" jdbcType="INTEGER" />
    <result column="batch_remind_allocated_num" property="batchRemindAllocatedNum" jdbcType="INTEGER" />
    <result column="batch_remind_urge_amount" property="batchRemindUrgeAmount" jdbcType="DOUBLE" />
    <result column="shop_sale_amount" property="shopSaleAmount" jdbcType="DOUBLE" />
  </resultMap>
    <resultMap id="ShopUseConversion" type="com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis" >
        <result column="shop_id" property="shopId" jdbcType="BIGINT" />
        <result column="date" property="date" jdbcType="DATE" />
        <result column="screen_valid_subscribe_num" property="screenValidSubscribeNum" jdbcType="INTEGER" />
        <result column="login_num" property="loginNum" jdbcType="INTEGER" />
        <result column="login_use_num" property="loginUseNum" jdbcType="INTEGER" />
        <result column="one_step_user_num" property="oneStepUserNum" jdbcType="INTEGER" />
        <result column="one_step_execute_num" property="oneStepExecuteNum" jdbcType="INTEGER" />
        <result column="one_step_allocated_num" property="oneStepAllocatedNum" jdbcType="INTEGER" />
        <result column="one_step_urge_amount" property="oneStepUrgeAmount" jdbcType="DOUBLE" />
        <result column="batch_remind_user_num" property="batchRemindUserNum" jdbcType="INTEGER" />
        <result column="batch_remind_execute_num" property="batchRemindExecuteNum" jdbcType="INTEGER" />
        <result column="batch_remind_allocated_num" property="batchRemindAllocatedNum" jdbcType="INTEGER" />
        <result column="batch_remind_urge_amount" property="batchRemindUrgeAmount" jdbcType="DOUBLE" />
        <result column="shop_sale_amount" property="shopSaleAmount" jdbcType="DOUBLE" />
    </resultMap>
  <sql id="base_filed" >
    id, shop_id, date, screen_valid_subscribe_num, login_num, login_use_num, one_step_user_num, 
    one_step_execute_num, one_step_allocated_num, one_step_urge_amount, batch_remind_user_num,
    batch_remind_execute_num, batch_remind_allocated_num, batch_remind_urge_amount, shop_sale_amount
  </sql>
  <delete id="deleteShopUseAnalysisByshopIdByDate" parameterType="java.lang.Long" >
    delete from ${tableName}
    where shop_id = #{shopId,jdbcType=BIGINT}
    and date=#{date}
  </delete>
  <insert id="insertShopUseAnalysis" parameterType="com.pes.jd.model.DO.ShopUseAnalysisDO" >
    insert into ${tableName} (
        shop_id,
        date,
      screen_valid_subscribe_num,
      login_num, login_use_num,
      one_step_user_num,
      one_step_execute_num,
      one_step_allocated_num,
      one_step_urge_amount,
      batch_remind_user_num,
      batch_remind_execute_num,
      batch_remind_allocated_num,
      batch_remind_urge_amount,
      shop_sale_amount,
      sms_use_num,
    sms_send_success_num,
      sms_send_valid_num,
      sms_urge_amount,
      auto_urge_amount,
      batch_urge_order_amount,
      all_allocated_num,
      all_execute_num,
      reserve_batch_remind_execute_num,
      reserve_batch_remind_user_num,
      reserve_batch_remind_allocated_num,
      reserve_batch_remind_urge_amount,
      presale_batch_remind_execute_num,
      presale_batch_remind_user_num,
      presale_batch_remind_allocated_num,
      presale_batch_remind_urge_amount,
      reserve_batch_urge_order_amount,
      presale_batch_urge_order_amount,
      reserve_auto_urge_amount,
      presale_auto_urge_amount,
      reserve_shop_sale_amount,
      presale_shop_sale_amount,
      reserve_all_allocated_num,
      presale_all_allocated_num,
      reserve_all_execute_num,
      presale_all_execute_num,
      sum_urge_amount,
      reserve_sum_urge_amount,
      presale_sum_urge_amount,
      reserve_one_step_urge_amount,
      presale_one_step_urge_amount
      )
    values (
        #{record.shopId,jdbcType=BIGINT},
        #{record.date,jdbcType=DATE},
        #{record.screenValidSubscribeNum,jdbcType=INTEGER},
        #{record.loginNum,jdbcType=INTEGER},
        #{record.loginUseNum,jdbcType=INTEGER},
        #{record.oneStepUserNum,jdbcType=INTEGER},
        #{record.oneStepExecuteNum,jdbcType=INTEGER},
        #{record.oneStepAllocatedNum,jdbcType=INTEGER},
       #{record.oneStepUrgeAmount,jdbcType=DOUBLE},
       #{record.batchRemindUserNum,jdbcType=INTEGER},
       #{record.batchRemindExecuteNum,jdbcType=INTEGER},
       #{record.batchRemindAllocatedNum,jdbcType=INTEGER},
       #{record.batchRemindUrgeAmount,jdbcType=DOUBLE},
       #{record.shopSaleAmount,jdbcType=DOUBLE},
       #{record.smsUseNum,jdbcType=INTEGER},
       #{record.smsSendSuccessNum,jdbcType=INTEGER},
       #{record.smsSendValidNum,jdbcType=INTEGER},
       #{record.smsUrgeAmount,jdbcType=DOUBLE},
       #{record.autoUrgeAmount,jdbcType=DOUBLE},
       #{record.batchUrgeOrderAmount,jdbcType=DOUBLE},
       #{record.allAllocateNum,jdbcType=INTEGER},
        #{record.allExecutedNum,jdbcType=INTEGER},

        #{record.reserveBatchRemindExecuteNum,jdbcType=INTEGER},
        #{record.reserveBatchRemindUserNum,jdbcType=INTEGER},
        #{record.reserveBatchRemindAllocatedNum,jdbcType=INTEGER},
       #{record.reserveBatchRemindUrgeAmount,jdbcType=DOUBLE},
       #{record.presaleBatchRemindExecuteNum,jdbcType=INTEGER},
       #{record.presaleBatchRemindUserNum,jdbcType=INTEGER},
       #{record.presaleBatchRemindAllocatedNum,jdbcType=INTEGER},
       #{record.presaleBatchRemindUrgeAmount,jdbcType=DOUBLE},
       #{record.reserveBatchUrgeOrderAmount,jdbcType=DOUBLE},
       #{record.presaleBatchUrgeOrderAmount,jdbcType=DOUBLE},
       #{record.reserveAuotUrgeAmount,jdbcType=DOUBLE},
       #{record.presaleAuotUrgeAmount,jdbcType=DOUBLE},
       #{record.reserveShopSaleAmount,jdbcType=DOUBLE},
       #{record.presaleShopSaleAmount,jdbcType=DOUBLE},
        #{record.reserveAllAllocateNum,jdbcType=INTEGER},
       #{record.presaleAllAllocateNum,jdbcType=INTEGER},
       #{record.reserveAllExecutedNum,jdbcType=INTEGER},
       #{record.presalAllExecutedNum,jdbcType=INTEGER},
       #{record.sumUrgeAmount,jdbcType=DOUBLE},
       #{record.reserveSumUrgeAmount,jdbcType=DOUBLE},
       #{record.presaleSumUrgeAmount,jdbcType=DOUBLE},
        #{record.reserveOneStepUrgeAmount,jdbcType=DOUBLE},
         #{record.presaleOneStepUrgeAmount,jdbcType=DOUBLE}

       )
  </insert>

    <select id="selectShopUseAnalysisByShopIdByDate" resultMap="ShopUseConversion">
        select
        shop_id,
        date,
        screen_valid_subscribe_num,
        shop_sale_amount
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and date=#{date}
        </where>
    </select>

    <update id="updateShopUseShopSaleAmountAndScreenNum">
        update ${tableName}
        set screen_valid_subscribe_num=#{num},shop_sale_amount=#{shopSaleAmount}
        <where>
            shop_id=#{shopId}
            and date=#{date}
        </where>
    </update>

    <update id="updateShopUseShopUrgeByShopIdByDate" parameterType="com.pes.jd.model.DO.ShopUseAnalysisDO" >
        update ${tableName}
        <set >
        <if test="record.oneStepUserNum != null" >
            one_step_user_num = #{record.oneStepUserNum,jdbcType=INTEGER},
        </if>
        <if test="record.oneStepExecuteNum != null" >
            one_step_execute_num = #{record.oneStepExecuteNum,jdbcType=INTEGER},
        </if>
        <if test="record.oneStepAllocatedNum != null" >
            one_step_allocated_num = #{record.oneStepAllocatedNum,jdbcType=INTEGER},
        </if>
        <if test="record.oneStepUrgeAmount != null" >
            one_step_urge_amount = #{record.oneStepUrgeAmount,jdbcType=DOUBLE},
        </if>
        <if test="record.batchRemindUserNum != null" >
            batch_remind_user_num = #{record.batchRemindUserNum,jdbcType=INTEGER},
        </if>
        <if test="record.batchRemindExecuteNum != null" >
            batch_remind_execute_num = #{record.batchRemindExecuteNum,jdbcType=INTEGER},
        </if>
        <if test="record.batchRemindAllocatedNum != null" >
            batch_remind_allocated_num = #{record.batchRemindAllocatedNum,jdbcType=INTEGER},
        </if>
        <if test="record.batchRemindUrgeAmount != null" >
            batch_remind_urge_amount = #{record.batchRemindUrgeAmount,jdbcType=DOUBLE},
        </if>

        <if test="record.smsUseNum != null" >
            sms_use_num = #{record.smsUseNum,jdbcType=INTEGER},
        </if>
        <if test="record.smsSendSuccessNum != null" >
            sms_send_success_num = #{record.smsSendSuccessNum,jdbcType=INTEGER},
        </if>
        <if test="record.smsSendValidNum != null" >
            sms_send_valid_num = #{record.smsSendValidNum,jdbcType=INTEGER},
        </if>
        <if test="record.smsUrgeAmount != null" >
            sms_urge_amount = #{record.smsUrgeAmount,jdbcType=DOUBLE},
        </if>
        <if test="record.autoUrgeAmount != null" >
            auto_urge_amount = #{record.autoUrgeAmount,jdbcType=DOUBLE},
        </if>
        <if test="record.batchUrgeOrderAmount != null" >
            batch_urge_order_amount = #{record.batchUrgeOrderAmount,jdbcType=DOUBLE},
        </if>
         <if test="record.allAllocateNum != null" >
             all_allocated_num = #{record.allAllocateNum,jdbcType=INTEGER},
         </if>
         <if test="record.allExecutedNum != null" >
             all_execute_num = #{record.allExecutedNum,jdbcType=INTEGER},
         </if>
            <if test="record.screenValidSubscribeNum != null" >
                screen_valid_subscribe_num = #{record.screenValidSubscribeNum,jdbcType=INTEGER},
            </if>
            <if test="record.shopSaleAmount != null" >
                shop_sale_amount = #{record.shopSaleAmount,jdbcType=DOUBLE},
            </if>

            <if test="record.reserveBatchRemindExecuteNum != null" >
                reserve_batch_remind_execute_num = #{record.reserveBatchRemindExecuteNum,jdbcType=INTEGER},
            </if>
            <if test="record.reserveBatchRemindUserNum != null" >
                reserve_batch_remind_user_num = #{record.reserveBatchRemindUserNum,jdbcType=INTEGER},
            </if>
            <if test="record.reserveBatchRemindAllocatedNum != null" >
                reserve_batch_remind_allocated_num = #{record.reserveBatchRemindAllocatedNum,jdbcType=INTEGER},
            </if>
            <if test="record.reserveBatchRemindUrgeAmount != null" >
                reserve_batch_remind_urge_amount = #{record.reserveBatchRemindUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.presaleBatchRemindExecuteNum != null" >
                presale_batch_remind_execute_num = #{record.presaleBatchRemindExecuteNum,jdbcType=INTEGER},
            </if>
            <if test="record.presaleBatchRemindUserNum != null" >
                presale_batch_remind_user_num = #{record.presaleBatchRemindUserNum,jdbcType=INTEGER},
            </if>
            <if test="record.presaleBatchRemindAllocatedNum != null" >
                presale_batch_remind_allocated_num = #{record.presaleBatchRemindAllocatedNum,jdbcType=INTEGER},
            </if>
            <if test="record.presaleBatchRemindUrgeAmount != null" >
                presale_batch_remind_urge_amount = #{record.presaleBatchRemindUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.reserveBatchUrgeOrderAmount != null" >
                reserve_batch_urge_order_amount = #{record.reserveBatchUrgeOrderAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.presaleBatchUrgeOrderAmount != null" >
                presale_batch_urge_order_amount = #{record.presaleBatchUrgeOrderAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.reserveAuotUrgeAmount != null" >
                reserve_auto_urge_amount = #{record.reserveAuotUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.presaleAuotUrgeAmount != null" >
                presale_auto_urge_amount = #{record.presaleAuotUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.reserveShopSaleAmount != null" >
                reserve_shop_sale_amount = #{record.reserveShopSaleAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.presaleShopSaleAmount != null" >
                presale_shop_sale_amount = #{record.presaleShopSaleAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.reserveAllAllocateNum != null" >
                reserve_all_allocated_num = #{record.reserveAllAllocateNum,jdbcType=INTEGER},
            </if>
            <if test="record.presaleAllAllocateNum != null" >
                presale_all_allocated_num = #{record.presaleAllAllocateNum,jdbcType=INTEGER},
            </if>
            <if test="record.reserveAllExecutedNum != null" >
                reserve_all_execute_num = #{record.reserveAllExecutedNum,jdbcType=INTEGER},
            </if>
            <if test="record.presalAllExecutedNum != null" >
                presale_all_execute_num = #{record.presalAllExecutedNum,jdbcType=INTEGER},
            </if>
            <if test="record.sumUrgeAmount != null" >
                sum_urge_amount = #{record.sumUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.reserveSumUrgeAmount != null" >
                reserve_sum_urge_amount = #{record.reserveSumUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.presaleSumUrgeAmount != null" >
                presale_sum_urge_amount = #{record.presaleSumUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.reserveOneStepUrgeAmount != null" >
                reserve_one_step_urge_amount = #{record.reserveOneStepUrgeAmount,jdbcType=DOUBLE},
            </if>
            <if test="record.presaleOneStepUrgeAmount != null" >
                presale_one_step_urge_amount = #{record.presaleOneStepUrgeAmount,jdbcType=DOUBLE}
            </if>

    </set>
       <where>
           shop_id = #{record.shopId}
           AND date = #{record.date}
       </where>

    </update>
</mapper>