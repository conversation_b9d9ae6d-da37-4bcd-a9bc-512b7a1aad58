<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.MarketingActivityLogMapper">


    <insert id="insert" parameterType="com.pes.jd.model.DO.MarketingActivityLog">
        INSERT INTO pes_marketing_activity_log (
            activity_id,
            activity_name,
            `date`,
            shop_id,
            cs_nick,
            opt_type,
            created
        ) VALUES (
             #{activityId, jdbcType=BIGINT},
             #{activityName, jdbcType=VARCHAR},
             #{date, jdbcType=DATE},
             #{shopId, jdbcType=BIGINT},
             #{csNick, jdbcType=VARCHAR},
             #{optType, jdbcType=INTEGER},
             #{created, jdbcType=TIMESTAMP}
         )
    </insert>

    <select id="queryByShopAndCsNickAndDate" resultType="com.pes.jd.model.DO.MarketingActivityLog">
        SELECT  activity_id,
                activity_name,
                date,
                shop_id,
                cs_nick,
                opt_type,
                created
        FROM pes_marketing_activity_log
        WHERE shop_id = #{shopId, jdbcType=BIGINT}
          AND cs_nick = #{csNick, jdbcType=VARCHAR}
          AND activity_id = #{activityId, jdbcType=BIGINT}
          AND opt_type = #{optType, jdbcType=INTEGER}
          AND created >= #{popStartTime, jdbcType=TIMESTAMP}
    </select>

    <select id="queryGroupByShop" resultType="com.pes.jd.model.DTO.MarketingActivityLogSummaryDTO">
        SELECT
            date,
            COUNT(DISTINCT shop_id) AS shopCt,
            SUM(CASE WHEN opt_type = 1 THEN 1 ELSE 0 END) AS typeOneCt,
            SUM(CASE WHEN opt_type = 2 THEN 1 ELSE 0 END) AS typeTwoCt,
            SUM(CASE WHEN opt_type = 3 THEN 1 ELSE 0 END) AS typeThreeCt,
            COUNT(DISTINCT CASE WHEN opt_type = 1 THEN CONCAT(opt_type, '_', shop_id) ELSE NULL END) AS typeOneShopCt,
            COUNT(DISTINCT CASE WHEN opt_type = 2 THEN CONCAT(opt_type, '_', shop_id) ELSE NULL END) AS typeTwoShopCt,
            COUNT(DISTINCT CASE WHEN opt_type = 3 THEN CONCAT(opt_type, '_', shop_id) ELSE NULL END) AS typeThreeShopCt
        FROM pes_marketing_activity_log
        GROUP BY date
        ORDER BY date
    </select>

    <select id="queryListByOptTypeAndDate" resultType="com.pes.jd.model.DO.MarketingActivityLog">
        SELECT  activity_id,
                activity_name,
                date,
                shop_id,
                cs_nick,
                opt_type,
                created
        FROM pes_marketing_activity_log
        WHERE
            date = #{date, jdbcType=DATE}
        AND opt_type = #{optType, jdbcType=INTEGER}
    </select>
</mapper>