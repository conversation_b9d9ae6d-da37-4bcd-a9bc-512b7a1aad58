<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSmsBacklistMapper" >
  <resultMap id="ShopSmsBacklistDO" type="com.pes.jd.model.DO.ShopSmsBacklistDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="operator" property="operator" jdbcType="BIGINT" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ShopSmsBacklist" type="com.pes.jd.ms.domain.Data.master.ShopSmsBacklist" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="operator" property="operator" jdbcType="BIGINT" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, operator, buyer_nick, created
  </sql>
  <select id="selectShopSmsBacklistByShopIdByCsNickByBuyerNick" resultMap="ShopSmsBacklist" >
    select 
    <include refid="base_field" />
    from pes_shop_sms_backlist
    <where>
      shop_id=#{shopId}
      and created between #{startDate} and #{endDate}
      <if test="buyerNick!=null and buyerNick!=''">
        and (buyer_nick =#{buyerNick} or telephone  like CONCAT(CONCAT('%',#{buyerNick}),'%') )
      </if>
    </where>
  </select>
  <delete id="deleteShopSmsBacklistById" parameterType="java.lang.Long" >
    delete from pes_shop_sms_backlist
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="batchInsertShopSmsBacklist"  >
    insert into pes_shop_sms_backlist
        (   shop_id,
            operator,
            buyer_nick,
            created
        )

    VALUES
      <foreach collection="smsBackLst" item="back" separator=",">
          (
              #{back.shopId,jdbcType=BIGINT},
              #{back.operator,jdbcType=BIGINT},
              #{back.buyerNick,jdbcType=VARCHAR},
              #{back.created,jdbcType=TIMESTAMP}
         )
      </foreach>

  </insert>

  <update id="updateShopSmsBacklist" parameterType="com.pes.jd.model.DO.ShopSmsBacklistDO" >
    update pes_shop_sms_backlist
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="operator != null" >
        operator = #{operator,jdbcType=BIGINT},
      </if>
      <if test="buyerNick != null" >
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectShopSmsBacklistByShopIdBuyerNickSet" resultMap="ShopSmsBacklist" >
    select
    shop_id,buyer_nick,telephone
    from pes_shop_sms_backlist
    <where>
      shop_id=#{shopId}
      and buyer_nick in
     <foreach collection="buyerNickSet" item="buyerNick" close=")" open="(" separator=",">
       #{buyerNick}
     </foreach>
    </where>
  </select>

  <select id="selectShopSmsBacklistById" resultMap="ShopSmsBacklist">
    select
    shop_id,buyer_nick,telephone
     from pes_shop_sms_backlist
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectShopSmsBackLstByShopId" resultMap="ShopSmsBacklist">
    select
    <include refid="base_field"/>
    from pes_shop_sms_backlist
    <where>
      shop_id=#{shopId}
    </where>
  </select>
</mapper>