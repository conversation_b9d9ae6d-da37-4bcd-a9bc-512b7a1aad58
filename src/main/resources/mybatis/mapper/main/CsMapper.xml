<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.CsMapper">
   <resultMap id="CsDO" type="com.pes.jd.model.DO.Cs">
    <id column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="simple_name" jdbcType="VARCHAR" property="simpleName" />
    <result column="cs_status" jdbcType="INTEGER" property="csStatus" />
    <result column="lock_time" jdbcType="TIMESTAMP" property="lockTime" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
  </resultMap>
  <resultMap id="CsDTO" type="com.pes.jd.model.DTO.CsDTO">
      <id column="nick" jdbcType="VARCHAR" property="nick"/>
      <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
      <result column="type" jdbcType="INTEGER" property="type"/>
      <result column="simple_name" jdbcType="VARCHAR" property="csSimpleNick"/>
      <result column="group_id" jdbcType="BIGINT" property="groupId"/>
      <result column="status" jdbcType="INTEGER" property="status"/>
      <result column="cs_status" jdbcType="INTEGER" property="csStatus"/>
      <result column="lock_time" jdbcType="TIMESTAMP" property="lockTime"/>
      <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate"/>
      <result column="source" property="source" jdbcType="TINYINT"/>
  </resultMap>
  <resultMap id="CsNumDTO" type="com.pes.jd.model.DTO.CsNumDTO">
    <result column="shop_id"  property="shopId" />
    <result column="type"  property="type" />
    <result column="num"  property="num" />
  </resultMap>
  <sql id="base_field">
    nick, shop_id, `type`, simple_name,cs_status,lock_time,modified_date,`source`
  </sql>
  <select id="getCsById" parameterType="java.lang.String" resultMap="CsDO">
    SELECT 
    <include refid="base_field" />
    FROM pes_cs
    WHERE nick = #{nick,jdbcType=VARCHAR}
  </select>

  <insert id="insertCs" parameterType="com.pes.jd.model.DO.Cs">
    INSERT INTO pes_cs (nick, shop_id, type, 
      simple_name,cs_status,modified_date)
    VALUES (#{nick,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, 
      #{simpleName,jdbcType=VARCHAR}, #{csStatus,jdbcType=INTEGER}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>

  <insert id="batchInsertCs" parameterType="list">
    INSERT INTO pes_cs (nick, shop_id, type,
      simple_name,cs_status,modified_date,source)
    VALUES
    <foreach collection="list" item="item" separator=",">
    (#{item.nick,jdbcType=VARCHAR}, #{item.shopId,jdbcType=BIGINT},
    #{item.type,jdbcType=INTEGER}, #{item.simpleName,jdbcType=VARCHAR},
    #{item.csStatus,jdbcType=INTEGER}, #{item.modifiedDate,jdbcType=TIMESTAMP},#{item.source,jdbcType=INTEGER})
     </foreach>
  </insert>

   <delete id="deleteCsByNick" parameterType="java.lang.String">
    DELETE FROM pes_cs
    WHERE nick = #{nick,jdbcType=VARCHAR}
  </delete>

  <update id="updateCsById" parameterType="com.pes.jd.model.DO.Cs">
    UPDATE pes_cs
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="simpleName != null">
        simple_name = #{simpleName,jdbcType=VARCHAR},
      </if>
      <if test="csStatus != null">
        cs_status = #{csStatus,jdbcType=INTEGER},
      </if>
      <if test="modifiedDate != null">
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    WHERE nick = #{nick,jdbcType=VARCHAR}
  </update>
  <update id="updateCsByCsNickByshopId" parameterType="map">
		UPDATE pes_cs
		SET
			simple_name =#{simpleName,jdbcType=VARCHAR},
			type=#{type,jdbcType=INTEGER}
		 WHERE
		 nick = #{nick,jdbcType=VARCHAR}
		 and shop_id=#{shopId,jdbcType=BIGINT}
	</update>
	<update id="updateCsOfStatusByNickLst" parameterType="map">
		<foreach collection="csLst"  item="itm" open=""  close="" separator=";">
				update pes_cs 
				<set>
					lock_time=#{itm.lockTime},
				 	cs_status=#{itm.csStatus},
					modified_date=#{itm.modifiedDate},
					<if test="itm.source!=null">
						source = #{itm.source}
					</if>
				</set>
				where nick =#{itm.nick} 
				and shop_id =#{itm.shopId}
		</foreach>
	</update>
  <select id="selectCsByShopId" parameterType="java.lang.Long" resultMap="CsDTO">
  		select
  		<include refid="base_field"></include>
  		from pes_cs where shop_id =#{shopId,jdbcType=BIGINT} 
  </select>
  
  <select id="selectCsByShopIdByTypeByCsStatus"  resultMap="CsDTO">
  		select
  		<include refid="base_field"></include>
  		from pes_cs 
  		<where>
  			<if test="shopId!=null">
  				 shop_id =#{shopId,jdbcType=BIGINT} 
  			</if>
  			<if test="type!=null">
  				and type=#{type}
  			</if>
  			<if test="csStatus!=null">
  				and cs_status=#{csStatus}
  			</if>
  		</where>
  		
  </select>

  <select id="selectShopCsNumByShopId" parameterType="long" resultMap="CsNumDTO">
		SELECT type,count(nick) as num
		FROM pes_cs s
		WHERE
			s.shop_id = #{shopId,jdbcType=BIGINT}
		GROUP BY s.type
	</select>

	 <select id="selectCsByShopIdByNickByType" parameterType="map" resultMap="CsDTO">
  		select
  		<include refid="base_field"></include>
  		from pes_cs
		<where>
			<if test="shopId!=null">
				shop_id =#{shopId,jdbcType=BIGINT}
			</if>
			<if test="nick!=null and nick!=''">
				and	 nick like concat('%',#{nick},'%')
			</if>
			<if test="type!=null">
				and	 type=#{type,jdbcType=INTEGER}
			</if>
		</where>

  </select>

    <!-- 根据nick 查询 BEGIN -->
    <select id="searchCsByNicks" resultMap="CsDO">
        select
        <include refid="base_field"/>
        from pes_cs
        where nick IN 
        <foreach collection="list" open="(" close=")" separator="," item="nick">
            #{nick}
        </foreach>
    </select>
    
    <!-- 根据nick 查询 END -->

  <!-- for client performance -->
  <select id="selectCsByShopIdAndCsid" resultType="java.util.Map" parameterType="java.util.Map">
    SELECT css.nick,css.shop_id shopId
	FROM pes_cs css
	WHERE
		css.shop_id = #{shopId}
	AND	css.nick = #{nick}
  </select>

  <select id="selectCsByShopIdMap" parameterType="java.util.Map" resultType="java.util.Map">
    select
    nick, shop_id shopId
    from pes_cs where shop_id =#{shopId,jdbcType=BIGINT}
  </select>
    
    <select id="selectByNick" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type,cs.cs_status,cs.lock_time, cs.simple_name, gcs.group_id,g.group_name groupName
        FROM pes_cs cs
        LEFT JOIN pes_group_cs gcs
        ON cs.nick = gcs.nick
        LEFT JOIN pes_group g
        ON gcs.group_id = g.group_id
        WHERE cs.nick = #{nick}
        
    </select>

    <select id="selectByShop" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type, cs.cs_status,cs.lock_time,cs.simple_name, gcs.group_id,g.group_name groupName
        FROM pes_cs cs
        LEFT JOIN pes_group_cs gcs
        ON cs.nick = gcs.nick
        LEFT JOIN pes_group g
        ON gcs.group_id = g.group_id
        WHERE cs.shop_id = #{shopId}
         	 
    </select>
    
    
     <select id="selectByShopIds" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type, cs.cs_status,cs.lock_time,cs.simple_name, gcs.group_id,g.group_name groupName
        FROM pes_cs cs
        LEFT JOIN pes_group_cs gcs
        ON cs.nick = gcs.nick
        LEFT JOIN pes_group g
        ON gcs.group_id = g.group_id  
<!--         WHERE cs.shop_id = #{shopId} -->
		WHERE  
		cs.shop_id in 
		<foreach collection="shopIds" item="shopId" open="(" close=")" separator=",">
			#{shopId}
		</foreach>
      	 
    </select>
    


    <select id="selectByGroup" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type, cs.cs_status,cs.lock_time,cs.simple_name, gcs.group_id,g.group_name groupName
        FROM pes_cs cs
        LEFT JOIN pes_group_cs gcs
        ON cs.nick = gcs.nick
        LEFT JOIN pes_group g
        ON gcs.group_id = g.group_id
        WHERE gcs.group_id = #{groupId}
       
    </select>
    
	<select id="selectCsByNickLst" resultMap="CsDO" parameterType="map">
	    SELECT <include refid="base_field" /> FROM pes_cs cs
	    WHERE cs.nick in
	        <foreach collection="nickLst" item="itm" open="(" close=")" separator=",">
	        	#{itm}
	        </foreach>
	   </select>
	   
    <select id="searchCsNickByShopId" resultType="String" parameterType="map">
        select nick
        from pes_cs
        where shop_id =#{shopId} 
        AND type=1
    </select>
    
    <select id="searchCsByshopIdLstAndType" resultMap="CsDTO" parameterType="map">
        select
        <include refid="base_field"/>
        from pes_cs
        where shop_id IN 
        <foreach collection="shopLst" open="(" close=")" separator="," item="itm">
            #{itm.shopId}
        </foreach>
        <if test="type!=null">
         AND type=#{type}
        </if>
        
    </select>
    
    <update id="updateCsNickByOldNick"  parameterType="map">
    	update pes_cs set nick=#{newCsNick} where nick=#{oldCsNick} and shop_id=#{shopId}
    </update>
    
   <select id="selectCsCountByShopIdByType" parameterType="map" resultType="int">
   		select COUNT(nick) FROM pes_cs 
		<where>
			<if test="shopId!=null">
				shop_id =#{shopId}
			</if>
			<if test="type!=null">
				and	 type=#{type}
			</if>
		</where>
   </select>

    <select id="selectBoardNicksByShopId" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type, cs.cs_status,cs.lock_time,cs.simple_name
        FROM  pes_jd.pes_cs cs
        <where>
            <if test="shopId!=null">
                cs.shop_id =#{shopId}
            </if>
            and cs.cs_status=1
        </where>
    </select>

   <select id="selectCsByShopIdSetByTypeByCsStatus"  resultType="com.pes.jd.ms.domain.Data.master.CsSimple">
  		select
  			 shop_id shopId, nick nick , simple_name  csSimpleNick
  		from pes_cs
  		<where>
  			shop_id IN
	        <foreach collection="shopIdSet" open="(" close=")" separator="," item="shopId">
	            #{shopId}
	        </foreach>
  			<if test="type!=null">
  				and type=#{type}
  			</if>
  			<if test="csStatus!=null">
  				and cs_status=#{csStatus}
  			</if>
  		</where>

  </select>
    <select id="selectSampleSimpleNickBySimpleName" resultType="int">
            select count(nick)
            from  pes_cs
            where  shop_id=#{shopId}
            and simple_name=#{simpleName}
            and nick!=#{csNick}
    </select>
    <select id="selectCsByShopIdAndCsNick" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type,cs.cs_status,cs.lock_time, cs.simple_name, gcs.group_id,g.group_name groupName
        FROM pes_cs cs
        LEFT JOIN pes_group_cs gcs
        ON cs.nick = gcs.nick
        LEFT JOIN pes_group g
        ON gcs.group_id = g.group_id
        WHERE
        cs.shop_id=#{shopId}
        AND
        cs.nick = #{csNick}
    </select>

    <select id="selectCsCountByShopIdByCsStatus"  resultType="int">
        select
        count(nick)
        from pes_cs
        where
        shop_id =#{shopId,jdbcType=BIGINT}
        <if test="status!=null">
            and cs_status=#{status}
        </if>


    </select>


	<select id="selectShopCsLists" resultMap="CsDO">
		SELECT
		<include refid="base_field" />
		FROM pes_cs
		WHERE shop_id = #{shopId}
		<if test="csNicks!=null">
		and nick in 
			<foreach collection="csNicks" open="(" close=")"
				separator="," item="itm">
				#{itm}
			</foreach>
		</if>
	</select>

    <select id="selectByShopAndGroupIdAndNick" resultMap="CsDTO">
        SELECT cs.nick, cs.shop_id, cs.type,cs.cs_status,cs.lock_time, cs.simple_name, gcs.group_id,g.group_name groupName
        FROM pes_cs cs
        LEFT JOIN pes_group_cs gcs ON cs.nick = gcs.nick
        LEFT JOIN pes_group g ON gcs.group_id = g.group_id
        WHERE cs.shop_id = #{shopId} AND cs.cs_status = 1
        <if test="groupId !=null and groupId != ''">
            AND gcs.group_id = #{groupId}
        </if>
        <if test="nick != null and nick != ''">
            AND cs.simple_name LIKE CONCAT(CONCAT('%',#{nick}),'%')
        </if>

    </select>

   <select id="selectCsLstByShopIdAndType" resultMap="CsDTO">
       SELECT
       <include refid="base_field"></include>
       FROM pes_cs
       WHERE
       shop_id = #{shopId,jdbcType=BIGINT}
       <!--   AND type = #{csType,jdbcType=TINYINT} -->
   </select>

   <select id="selectCsNickByGroupId" resultType="java.lang.String">
       SELECT cs.nick
       FROM pes_cs cs
                    INNER JOIN pes_group_cs gcs
               ON cs.nick = gcs.nick
                    INNER JOIN pes_group g
               ON gcs.group_id = g.group_id
       WHERE gcs.group_id = #{groupId}
   </select>

   <select id="selectCsNickByShopId" resultType="java.lang.String">
       SELECT nick
       FROM pes_cs
       where shop_id=#{shopId}
   </select>

    <select id="getOneCsByShopId" resultType="java.lang.String">
        select nick
        from pes_cs
        where shop_id = #{shopId}
        limit 0,1
    </select>
</mapper>