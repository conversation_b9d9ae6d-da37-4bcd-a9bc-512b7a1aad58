<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopDetailMapper">

	<resultMap id="ShopDetailDO" type="com.pes.jd.model.DO.ShopDetail">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="first_name" jdbcType="VARCHAR" property="firstName" />
		<result column="phone" jdbcType="VARCHAR" property="phone" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="contact_qq" jdbcType="VARCHAR" property="contactQq" />
		<result column="visit_code" jdbcType="VARCHAR" property="visitCode" />
		<result column="visit_email" jdbcType="VARCHAR" property="visitEmail" />
	</resultMap>

	<sql id="base_field">
		shop_id, first_name, phone, email, contact_qq, visit_code, visit_email
	</sql>

	<insert id="insertShopDetail" parameterType="com.pes.jd.model.DO.ShopDetail">
		insert into pes_shop_detail (shop_id, first_name, phone,
      email, contact_qq, visit_code,
      visit_email)
    values (#{shopId,jdbcType=BIGINT}, #{firstName,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR},
      #{email,jdbcType=VARCHAR}, #{contactQq,jdbcType=VARCHAR}, #{visitCode,jdbcType=VARCHAR},
      #{visitEmail,jdbcType=VARCHAR})
	</insert>
	
	<delete id="deleteShopDetailByShopId" parameterType="java.lang.Long">
		DELETE FROM pes_shop_detail
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</delete>

	<update id="updateShopDetailByShopId" parameterType="com.pes.jd.model.DO.ShopDetail">
		update pes_shop_detail
		<set>
			<if test="firstName != null">
				first_name = #{firstName,jdbcType=VARCHAR},
			</if>
			<if test="phone != null">
				phone = #{phone,jdbcType=VARCHAR},
			</if>
			<if test="email != null">
				email = #{email,jdbcType=VARCHAR},
			</if>
			<if test="contactQq != null">
				contact_qq = #{contactQq,jdbcType=VARCHAR},
			</if>
			<if test="visitCode != null">
				visit_code = #{visitCode,jdbcType=VARCHAR},
			</if>
			<if test="visitEmail != null">
				visit_email = #{visitEmail,jdbcType=VARCHAR},
			</if>
		</set>
		where shop_id = #{shopId,jdbcType=BIGINT}
	</update>

	<update id="clearVisitCode" parameterType="com.pes.jd.model.DO.ShopDetail">
		update pes_shop_detail
		set
				visit_code = null ,
				visit_email = null
		where shop_id = #{shopId,jdbcType=BIGINT}
	</update>
	
	<select id="getShopDetailByShopId" parameterType="java.lang.Long" resultMap="ShopDetailDO">
		SELECT
			sd.shop_id, s.title , sd.first_name, sd.phone, sd.email, sd.contact_qq,sd.visit_code ,sd.visit_email
		FROM 
			pes_shop_detail sd
		INNER JOIN pes_shop s
		ON s.shop_id = sd.shop_id
		WHERE 
			s.shop_id = #{shopId,jdbcType=BIGINT}
	</select>
</mapper>