<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.UserManageMapper">

<!-- 用户管理SQL(用户，用户角色，用户资源连表业务) -->

<resultMap type="com.pes.jd.model.DTO.UserRoleResourceDTO" id="UserRoleResourceDTO">
		<result property="url" column="url" />
</resultMap>
<insert id="batchInsertUserResource" parameterType="java.util.List">
		insert into pes_user_resource(nick,resource_id)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.nick},#{item.resourceId})
		</foreach>
</insert>

<select id="getUserRoleByUserId" parameterType="java.lang.Long" resultType="java.lang.String">
		SELECT r.role_name FROM
		pes_role r, pes_user u, pes_user_role ur
		WHERE
		r.id = ur.role_id
		AND
		ur.user_id = u.user_id
		AND
		u.user_id = #{userId}
	</select>
	
	
	<select id="selectRoleResourcesByRoleName" parameterType="java.lang.String" resultMap="UserRoleResourceDTO">
		SELECT rs.id, rs.url url
		FROM pes_role r, pes_resource rs, pes_role_resource rrs
		WHERE
			r.id = rrs.role_id
		AND
			rrs.resource_id = rs.id
		AND
			r.role_name = #{roleName}
	</select>
	
	<select id="selectUserResourceByNick" parameterType="java.util.Map" resultMap="UserRoleResourceDTO">
		SELECT
	    e.id id,e.url url
        FROM
	    pes_user_resource s
        LEFT JOIN pes_resource e ON s.resource_id = e.id
        WHERE
	    nick = #{nick,jdbcType=VARCHAR}
	</select>
</mapper>