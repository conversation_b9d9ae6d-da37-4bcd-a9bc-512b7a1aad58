<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.DeptShopMapper" >
  <resultMap id="DeptShopDTO" type="com.pes.jd.ms.domain.Data.master.DeptShop" >
    <result column="dept_id" property="deptId" jdbcType="INTEGER" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
	<result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
	<result column="title" jdbcType="VARCHAR" property="shopName" />
  </resultMap>

    <resultMap id="ShopDO" type="com.pes.jd.model.DTO.BoardMnoitorParamDTO">
        <id column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="title" jdbcType="VARCHAR" property="title" />
        <result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
        <result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
        <result column="db" jdbcType="VARCHAR" property="db" />
        <result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
        <result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
    </resultMap>


  <sql id="base_field" >
    id, dept_id, shop_id
  </sql>
  <select id="selectDeptShop" resultMap="DeptShopDTO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_dept_shop
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteDeptShopById" parameterType="java.lang.Long" >
    delete from pes_dept_shop
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertDeptShop" parameterType="com.pes.jd.model.DO.DeptShopDO" >
    insert into pes_dept_shop (id, dept_id, shop_id
      )
    values (#{id,jdbcType=BIGINT}, #{deptId,jdbcType=INTEGER}, #{shopId,jdbcType=BIGINT}
      )
  </insert>
  <update id="updateDeptShop" parameterType="com.pes.jd.model.DO.DeptShopDO" >
    update pes_dept_shop
    <set >
      <if test="deptId != null" >
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectDeptShopInfoByDeptId" resultMap="DeptShopDTO">
  	SELECT 
<!-- 	#  	pds.dept_id, -->
	  	ps.shop_id,
	  	ps.title,
	  	ps.rt_schema_id,
	  	ps.rt_db
	  	FROM
	    pes_shop ps
	  	WHERE   ps.status='active'
	  	<if test="shopIds!=null and shopIds.size>0">
				and  shop_id in
				<foreach collection="shopIds" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
		</if>
	  	<if test="shopName!=null and shopName!='' ">
	  	and ps.title like CONCAT(CONCAT('%', #{shopName}), '%'  )
	  	</if>
	  
  </select>

    <select id="selectShopByDeptIdAndTitle" resultMap="ShopDO">
        SELECT  s.shop_id,s.title,s.session_key,s.schema_id,s.db,s.rt_schema_id,s.rt_db
        FROM pes_shop s inner join pes_dept_shop dp
        ON s.shop_id=dp.shop_id
        WHERE dp.dept_id=#{deptId} AND s.status='active'
        <if test="queryParam!=null and queryParam!=''">
            AND( s.title LIKE concat(concat('%',#{queryParam}),'%')
            OR s.shop_id LIKE concat(concat('%',#{queryParam}),'%'))
        </if>
        <if test="size!=null and size!=0">
           limit #{currentPage},#{size}
        </if>
    </select>

    <select id="selectShopNumsByDeptIdAndTitle" resultType="java.lang.Integer">
        SELECT  count(*)
        FROM pes_shop s , pes_dept_shop dp
        WHERE s.shop_id=dp.shop_id
        AND dp.dept_id=#{deptId} AND s.status='active'
        <if test="queryParam!=null and queryParam!=''">
            AND( s.title LIKE concat(concat('%',#{queryParam}),'%')
            OR s.shop_id LIKE concat(concat('%',#{queryParam}),'%'))
        </if>
    </select>



    <select id="selectShopByDeptIdAndShopId" resultMap="ShopDO">
        SELECT  s.shop_id,s.title,s.session_key,s.schema_id,s.db,s.rt_schema_id,s.rt_db
        FROM
            pes_shop s
        inner join
            pes_dept_shop dp
        ON s.shop_id=dp.shop_id
        WHERE dp.dept_id=#{deptId}
       AND s.shop_id=#{queryParam}
    </select>
  
<select id="selectAllByDeptId" resultType="com.pes.jd.model.DO.DeptShopDO">
    select * from pes_dept_shop
    where dept_id = #{deptId}
  </select>
  
</mapper> 

