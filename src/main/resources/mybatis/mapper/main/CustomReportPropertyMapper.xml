<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.CustomReportPropertyMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CustomReportPropertyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="property_id" jdbcType="BIGINT" property="propertyId" />
    <result column="custom_report_id" jdbcType="BIGINT" property="customReportId" />
    <result column="filter_flag" jdbcType="TINYINT" property="filterFlag" />
    <result column="filter_json" jdbcType="VARCHAR" property="filterJson" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <resultMap id="CustomReportPropertyDTO" type="com.pes.jd.model.DTO.CustomReportPropertyDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="property_id" jdbcType="BIGINT" property="propertyId" />
    <result column="custom_report_id" jdbcType="BIGINT" property="customReportId" />
    <result column="filter_flag" jdbcType="TINYINT" property="filterFlag" />
    <result column="filter_json" jdbcType="VARCHAR" property="filterJson" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, property_id, custom_report_id, filter_flag, filter_json, status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_custom_report_property
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchAllByReportId" resultMap="CustomReportPropertyDTO">
    select
    <include refid="Base_Column_List" />
    from pes_custom_report_property
    <if test="collection !=null and collection.size() > 0">
      where custom_report_id IN 
      <foreach collection="collection" separator="," item="reportId" open="(" close=")">
        #{reportId}
      </foreach>
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_custom_report_property
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByReportId" parameterType="java.lang.Long">
    delete from pes_custom_report_property
    where custom_report_id = #{reportId}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CustomReportPropertyDO">
    insert into pes_custom_report_property (id, property_id, custom_report_id, 
      filter_flag, filter_json, status
      )
    values (#{id,jdbcType=BIGINT}, #{propertyId,jdbcType=BIGINT}, #{customReportId,jdbcType=BIGINT}, 
      #{filterFlag,jdbcType=TINYINT}, #{filterJson,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CustomReportPropertyDO">
    insert into pes_custom_report_property
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="propertyId != null">
        property_id,
      </if>
      <if test="customReportId != null">
        custom_report_id,
      </if>
      <if test="filterFlag != null">
        filter_flag,
      </if>
      <if test="filterJson != null">
        filter_json,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="propertyId != null">
        #{propertyId,jdbcType=BIGINT},
      </if>
      <if test="customReportId != null">
        #{customReportId,jdbcType=BIGINT},
      </if>
      <if test="filterFlag != null">
        #{filterFlag,jdbcType=TINYINT},
      </if>
      <if test="filterJson != null">
        #{filterJson,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into pes_custom_report_property (id, property_id, custom_report_id,
      filter_flag, filter_json, status
      ) values
    <foreach collection="list" separator="," close=";" item="item">
      (null, #{item.propertyId,jdbcType=BIGINT}, #{item.customReportId,jdbcType=BIGINT},
      #{item.filterFlag,jdbcType=TINYINT}, #{item.filterJson,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}
      )
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CustomReportPropertyDO">
    update pes_custom_report_property
    <set>
      <if test="propertyId != null">
        property_id = #{propertyId,jdbcType=BIGINT},
      </if>
      <if test="customReportId != null">
        custom_report_id = #{customReportId,jdbcType=BIGINT},
      </if>
      <if test="filterFlag != null">
        filter_flag = #{filterFlag,jdbcType=TINYINT},
      </if>
      <if test="filterJson != null">
        filter_json = #{filterJson,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CustomReportPropertyDO">
    update pes_custom_report_property
    set property_id = #{propertyId,jdbcType=BIGINT},
      custom_report_id = #{customReportId,jdbcType=BIGINT},
      filter_flag = #{filterFlag,jdbcType=TINYINT},
      filter_json = #{filterJson,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>