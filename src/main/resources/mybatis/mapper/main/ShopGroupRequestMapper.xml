<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopGroupRequestMapper">
	
	<resultMap type="com.pes.jd.model.DTO.ShopGroupRequestDTO" id="ShopGroupRequestDTO">
		<id column="request_id" jdbcType="BIGINT" property="requestId"/>
	    <result column="from_id" jdbcType="BIGINT" property="fromId" />
	    <result column="to_id" jdbcType="BIGINT" property="toId" />
	    <result column="shop_group_id" jdbcType="BIGINT" property="shopGroupId" />
	    <result column="status" jdbcType="VARCHAR" property="status" />
	    <result column="request_date" jdbcType="TIMESTAMP" property="requestDate" />
	    <result column="accept_invite_date" jdbcType="TIMESTAMP" property="acceptInviteDate" />
	</resultMap>
	
	<insert id="insertShopGroupRequest" parameterType="com.pes.jd.model.DTO.ShopGroupRequestDTO">
		INSERT INTO pes_shop_group_request (from_id,to_id,shop_group_id,status,request_date) 
		VALUES (#{fromId},#{toId},#{shopGroupId},#{status},#{requestDate});
	</insert>
	
	<update id="InviteAgain" parameterType="com.pes.jd.model.DTO.ShopGroupRequestDTO">
		UPDATE pes_shop_group_request
			SET status = #{status},
			request_date = #{requestDate},
			accept_invite_date = #{acceptInviteDate},
			shop_group_id = #{shopGroupId}
		<where>
			<choose>
				<when test="requestId != null">
					request_id = #{requestId}
				</when>
				<otherwise>
					from_id = #{fromId}
					AND to_id = #{toId}
				</otherwise>
			</choose>
		</where>
		
	</update>
	
	<update id="updateShopGroupRequestByRequestId" parameterType="com.pes.jd.model.DTO.ShopGroupRequestDTO">
		UPDATE pes_shop_group_request
		SET
		status = #{status},
		request_date = #{requestDate},
		accept_invite_date = #{acceptInviteDate}
		WHERE request_id = #{requestId}
	</update>
	
	<update id="updateShopGroupIdByFromShopIdByToShopId" parameterType="map">
		<foreach collection="shopIdList" item="toId" open="" close="" separator=";">
			UPDATE pes_shop_group_request
			SET shop_group_id = #{shopGroupId}
			WHERE from_id = #{fromId}
			AND to_id = #{toId}
		</foreach>
	</update>
	
	<select id="selectShopGroupRequests" parameterType="map" resultMap="ShopGroupRequestDTO">
		SELECT *
		FROM pes_shop_group_request 
		WHERE shop_group_id = #{shopGroupId}
		AND from_id = #{shopId}
	</select>
	
	<select id="checkRequestExistOrNot" parameterType="map" resultMap="ShopGroupRequestDTO">
		SELECT *
		FROM pes_shop_group_request 
		WHERE from_id = #{fromId}
		AND to_id = #{toId}
	</select>
	
	<select id="selectJoinGroupList" parameterType="map" resultMap="ShopGroupRequestDTO">
		SELECT sgr.*,s.seller_nick fromShopSellerNick,s.title fromShopTitle
		FROM pes_shop_group_request sgr
		INNER JOIN pes_shop s
		ON sgr.from_id = s.shop_id
		WHERE sgr.to_id = #{shopId}
		AND sgr.status = #{status}
	</select>
	
	<select id="selectShopAccreditByMainShop" parameterType="java.lang.Long" resultMap="ShopGroupRequestDTO">
		SELECT *
		FROM pes_shop_group_request
		WHERE (from_id = #{shopId}
		OR to_id = #{shopId})
	</select>
	
	<select id="selectShopGroupRequestsByRequestId" parameterType="java.lang.String" resultMap="ShopGroupRequestDTO">
		SELECT *
		FROM pes_shop_group_request
		WHERE request_id = #{requsetId}
	</select>
</mapper>