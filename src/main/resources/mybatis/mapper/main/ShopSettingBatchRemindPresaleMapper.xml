<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSettingBatchRemindPresaleMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="is_unpo_remind" property="isUnpoRemind" jdbcType="BIT" />
    <result column="unpo_remind_dot" property="unpoRemindDot" jdbcType="INTEGER" />
    <result column="unpo_remind_word_id" property="unpoRemindWordId" jdbcType="VARCHAR" />
    <result column="is_bargain_remind" property="isBargainRemind" jdbcType="BIT" />
    <result column="bargain_remind_dot" property="bargainRemindDot" jdbcType="INTEGER" />
    <result column="bargain_remind_word_id" property="bargainRemindWordId" jdbcType="VARCHAR" />
    <result column="bargain_silent_cs_nick" property="bargainSilentCsNick" jdbcType="VARCHAR" />
    <result column="bargain_silent_group_id" property="bargainSilentGroupId" jdbcType="BIGINT" />
    <result column="bargain_silent_flag" property="bargainSilentFlag" jdbcType="TINYINT" />
    <result column="bargain_silent_word_id" property="bargainSilentWordId" jdbcType="VARCHAR" />
    <result column="bargain_silent_spare_cs_nick" property="bargainSilentSpareCsNick" jdbcType="VARCHAR" />
    <result column="bargain_silent_spare_group_id" property="bargainSilentSpareGroupId" jdbcType="BIGINT" />
    <result column="is_balance_remind" property="isBalanceRemind" jdbcType="BIT" />
    <result column="balance_remind_dot" property="balanceRemindDot" jdbcType="INTEGER" />
    <result column="balance_remind_word_id" property="balanceRemindWordId" jdbcType="VARCHAR" />
    <result column="balance_silent_cs_nick" property="balanceSilentCsNick" jdbcType="VARCHAR" />
    <result column="balance_silent_group_id" property="balanceSilentGroupId" jdbcType="BIGINT" />
    <result column="balance_silent_flag" property="balanceSilentFlag" jdbcType="TINYINT" />
    <result column="balance_silent_word_id" property="balanceSilentWordId" jdbcType="VARCHAR" />
    <result column="balance_silent_spare_cs_nick" property="balanceSilentSpareCsNick" jdbcType="VARCHAR" />
    <result column="balance_silent_spare_group_id" property="balanceSilentSpareGroupId" jdbcType="BIGINT" />
    <result column="is_sec_balance_silent_remind" property="isSecBalanceSilentRemind" jdbcType="BIT" />
    <result column="sec_balance_silent_remind_dot" property="secBalanceSilentRemindDot" jdbcType="INTEGER" />
    <result column="sec_balance_silent_remind_word_id" property="secBalanceSilentRemindWordId" jdbcType="VARCHAR" />
    <result column="is_sec_balance_remind" property="isSecBalanceRemind" jdbcType="BIT" />
    <result column="sec_balance_remind_dot" property="secBalanceRemindDot" jdbcType="INTEGER" />
    <result column="sec_balance_remind_word_id" property="secBalanceRemindWordId" jdbcType="VARCHAR" />
    <result column="remind_start_dot" property="remindStartDot" jdbcType="INTEGER" />
    <result column="remind_end_dot" property="remindEndDot" jdbcType="INTEGER" />
    <result column="is_send_profit_point" property="isSendProfitPoint" jdbcType="BIT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, is_unpo_remind, unpo_remind_dot, unpo_remind_word_id, is_bargain_remind,
    bargain_remind_dot, bargain_remind_word_id, bargain_silent_cs_nick, bargain_silent_group_id,
    bargain_silent_flag, bargain_silent_word_id, bargain_silent_spare_cs_nick, bargain_silent_spare_group_id,
    is_balance_remind, balance_remind_dot, balance_remind_word_id, balance_silent_cs_nick,
    balance_silent_group_id, balance_silent_flag, balance_silent_word_id, balance_silent_spare_cs_nick,
    balance_silent_spare_group_id, is_sec_balance_silent_remind, sec_balance_silent_remind_dot,
    sec_balance_silent_remind_word_id, is_sec_balance_remind, sec_balance_remind_dot,
    sec_balance_remind_word_id, remind_start_dot, remind_end_dot, is_send_profit_point,
    created, modified
  </sql>
  <select id="selectShopSettingBatchRemindPresaleByShopId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from pes_shop_setting_batch_remind_presale
    where shop_id = #{shopId,jdbcType=BIGINT}
  </select>

  <insert id="insert" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO" >
    insert into pes_shop_setting_batch_remind_presale (id, shop_id, is_unpo_remind, 
      unpo_remind_dot, unpo_remind_word_id, is_bargain_remind, 
      bargain_remind_dot, bargain_remind_word_id, 
      bargain_silent_cs_nick, bargain_silent_group_id, 
      bargain_silent_flag, bargain_silent_word_id, 
      bargain_silent_spare_cs_nick, bargain_silent_spare_group_id, 
      is_balance_remind, balance_remind_dot, balance_remind_word_id, 
      balance_silent_cs_nick, balance_silent_group_id, 
      balance_silent_flag, balance_silent_word_id, 
      balance_silent_spare_cs_nick, balance_silent_spare_group_id, 
      is_sec_balance_silent_remind, sec_balance_silent_remind_dot, 
      sec_balance_silent_remind_word_id, is_sec_balance_remind, 
      sec_balance_remind_dot, sec_balance_remind_word_id, 
      remind_start_dot, remind_end_dot, is_send_profit_point, 
      created, modified)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{isUnpoRemind,jdbcType=BIT}, 
      #{unpoRemindDot,jdbcType=INTEGER}, #{unpoRemindWordId,jdbcType=VARCHAR}, #{isBargainRemind,jdbcType=BIT}, 
      #{bargainRemindDot,jdbcType=INTEGER}, #{bargainRemindWordId,jdbcType=VARCHAR}, 
      #{bargainSilentCsNick,jdbcType=VARCHAR}, #{bargainSilentGroupId,jdbcType=BIGINT}, 
      #{bargainSilentFlag,jdbcType=TINYINT}, #{bargainSilentWordId,jdbcType=VARCHAR}, 
      #{bargainSilentSpareCsNick,jdbcType=VARCHAR}, #{bargainSilentSpareGroupId,jdbcType=BIGINT}, 
      #{isBalanceRemind,jdbcType=BIT}, #{balanceRemindDot,jdbcType=INTEGER}, #{balanceRemindWordId,jdbcType=VARCHAR}, 
      #{balanceSilentCsNick,jdbcType=VARCHAR}, #{balanceSilentGroupId,jdbcType=BIGINT}, 
      #{balanceSilentFlag,jdbcType=TINYINT}, #{balanceSilentWordId,jdbcType=VARCHAR}, 
      #{balanceSilentSpareCsNick,jdbcType=VARCHAR}, #{balanceSilentSpareGroupId,jdbcType=BIGINT}, 
      #{isSecBalanceSilentRemind,jdbcType=BIT}, #{secBalanceSilentRemindDot,jdbcType=INTEGER}, 
      #{secBalanceSilentRemindWordId,jdbcType=VARCHAR}, #{isSecBalanceRemind,jdbcType=BIT}, 
      #{secBalanceRemindDot,jdbcType=INTEGER}, #{secBalanceRemindWordId,jdbcType=VARCHAR}, 
      #{remindStartDot,jdbcType=INTEGER}, #{remindEndDot,jdbcType=INTEGER}, #{isSendProfitPoint,jdbcType=BIT}, 
      #{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO" >
    insert into pes_shop_setting_batch_remind_presale
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="isUnpoRemind != null" >
        is_unpo_remind,
      </if>
      <if test="unpoRemindDot != null" >
        unpo_remind_dot,
      </if>
      <if test="unpoRemindWordId != null" >
        unpo_remind_word_id,
      </if>
      <if test="isBargainRemind != null" >
        is_bargain_remind,
      </if>
      <if test="bargainRemindDot != null" >
        bargain_remind_dot,
      </if>
      <if test="bargainRemindWordId != null" >
        bargain_remind_word_id,
      </if>
      <if test="bargainSilentCsNick != null" >
        bargain_silent_cs_nick,
      </if>
      <if test="bargainSilentGroupId != null" >
        bargain_silent_group_id,
      </if>
      <if test="bargainSilentFlag != null" >
        bargain_silent_flag,
      </if>
      <if test="bargainSilentWordId != null" >
        bargain_silent_word_id,
      </if>
      <if test="bargainSilentSpareCsNick != null" >
        bargain_silent_spare_cs_nick,
      </if>
      <if test="bargainSilentSpareGroupId != null" >
        bargain_silent_spare_group_id,
      </if>
      <if test="isBalanceRemind != null" >
        is_balance_remind,
      </if>
      <if test="balanceRemindDot != null" >
        balance_remind_dot,
      </if>
      <if test="balanceRemindWordId != null" >
        balance_remind_word_id,
      </if>
      <if test="balanceSilentCsNick != null" >
        balance_silent_cs_nick,
      </if>
      <if test="balanceSilentGroupId != null" >
        balance_silent_group_id,
      </if>
      <if test="balanceSilentFlag != null" >
        balance_silent_flag,
      </if>
      <if test="balanceSilentWordId != null" >
        balance_silent_word_id,
      </if>
      <if test="balanceSilentSpareCsNick != null" >
        balance_silent_spare_cs_nick,
      </if>
      <if test="balanceSilentSpareGroupId != null" >
        balance_silent_spare_group_id,
      </if>
      <if test="isSecBalanceSilentRemind != null" >
        is_sec_balance_silent_remind,
      </if>
      <if test="secBalanceSilentRemindDot != null" >
        sec_balance_silent_remind_dot,
      </if>
      <if test="secBalanceSilentRemindWordId != null" >
        sec_balance_silent_remind_word_id,
      </if>
      <if test="isSecBalanceRemind != null" >
        is_sec_balance_remind,
      </if>
      <if test="secBalanceRemindDot != null" >
        sec_balance_remind_dot,
      </if>
      <if test="secBalanceRemindWordId != null" >
        sec_balance_remind_word_id,
      </if>
      <if test="remindStartDot != null" >
        remind_start_dot,
      </if>
      <if test="remindEndDot != null" >
        remind_end_dot,
      </if>
      <if test="isSendProfitPoint != null" >
        is_send_profit_point,
      </if>
      <if test="created != null" >
        created,
      </if>
      <if test="modified != null" >
        modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isUnpoRemind != null" >
        #{isUnpoRemind,jdbcType=BIT},
      </if>
      <if test="unpoRemindDot != null" >
        #{unpoRemindDot,jdbcType=INTEGER},
      </if>
      <if test="unpoRemindWordId != null" >
        #{unpoRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isBargainRemind != null" >
        #{isBargainRemind,jdbcType=BIT},
      </if>
      <if test="bargainRemindDot != null" >
        #{bargainRemindDot,jdbcType=INTEGER},
      </if>
      <if test="bargainRemindWordId != null" >
        #{bargainRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="bargainSilentCsNick != null" >
        #{bargainSilentCsNick,jdbcType=VARCHAR},
      </if>
      <if test="bargainSilentGroupId != null" >
        #{bargainSilentGroupId,jdbcType=BIGINT},
      </if>
      <if test="bargainSilentFlag != null" >
        #{bargainSilentFlag,jdbcType=TINYINT},
      </if>
      <if test="bargainSilentWordId != null" >
        #{bargainSilentWordId,jdbcType=VARCHAR},
      </if>
      <if test="bargainSilentSpareCsNick != null" >
        #{bargainSilentSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="bargainSilentSpareGroupId != null" >
        #{bargainSilentSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isBalanceRemind != null" >
        #{isBalanceRemind,jdbcType=BIT},
      </if>
      <if test="balanceRemindDot != null" >
        #{balanceRemindDot,jdbcType=INTEGER},
      </if>
      <if test="balanceRemindWordId != null" >
        #{balanceRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="balanceSilentCsNick != null" >
        #{balanceSilentCsNick,jdbcType=VARCHAR},
      </if>
      <if test="balanceSilentGroupId != null" >
        #{balanceSilentGroupId,jdbcType=BIGINT},
      </if>
      <if test="balanceSilentFlag != null" >
        #{balanceSilentFlag,jdbcType=TINYINT},
      </if>
      <if test="balanceSilentWordId != null" >
        #{balanceSilentWordId,jdbcType=VARCHAR},
      </if>
      <if test="balanceSilentSpareCsNick != null" >
        #{balanceSilentSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="balanceSilentSpareGroupId != null" >
        #{balanceSilentSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isSecBalanceSilentRemind != null" >
        #{isSecBalanceSilentRemind,jdbcType=BIT},
      </if>
      <if test="secBalanceSilentRemindDot != null" >
        #{secBalanceSilentRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secBalanceSilentRemindWordId != null" >
        #{secBalanceSilentRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecBalanceRemind != null" >
        #{isSecBalanceRemind,jdbcType=BIT},
      </if>
      <if test="secBalanceRemindDot != null" >
        #{secBalanceRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secBalanceRemindWordId != null" >
        #{secBalanceRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="remindStartDot != null" >
        #{remindStartDot,jdbcType=INTEGER},
      </if>
      <if test="remindEndDot != null" >
        #{remindEndDot,jdbcType=INTEGER},
      </if>
      <if test="isSendProfitPoint != null" >
        #{isSendProfitPoint,jdbcType=BIT},
      </if>
      <if test="created != null" >
        #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        #{modified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByShopId" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindPresaleDTO" >
    update pes_shop_setting_batch_remind_presale
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isUnpoRemind != null" >
        is_unpo_remind = #{isUnpoRemind,jdbcType=BIT},
      </if>
      <if test="unpoRemindDot != null" >
        unpo_remind_dot = #{unpoRemindDot,jdbcType=INTEGER},
      </if>
        unpo_remind_word_id = #{unpoRemindWordId,jdbcType=VARCHAR},
      <if test="isBargainRemind != null" >
        is_bargain_remind = #{isBargainRemind,jdbcType=BIT},
      </if>
      <if test="bargainRemindDot != null" >
        bargain_remind_dot = #{bargainRemindDot,jdbcType=INTEGER},
      </if>
        bargain_remind_word_id = #{bargainRemindWordId,jdbcType=VARCHAR},
      <if test="bargainSilentCsNick != null" >
        bargain_silent_cs_nick = #{bargainSilentCsNick,jdbcType=VARCHAR},
      </if>
      <if test="bargainSilentGroupId != null" >
        bargain_silent_group_id = #{bargainSilentGroupId,jdbcType=BIGINT},
      </if>
      <if test="bargainSilentFlag != null" >
        bargain_silent_flag = #{bargainSilentFlag,jdbcType=TINYINT},
      </if>
        bargain_silent_word_id = #{bargainSilentWordId,jdbcType=VARCHAR},
      <if test="bargainSilentSpareCsNick != null" >
        bargain_silent_spare_cs_nick = #{bargainSilentSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="bargainSilentSpareGroupId != null" >
        bargain_silent_spare_group_id = #{bargainSilentSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isBalanceRemind != null" >
        is_balance_remind = #{isBalanceRemind,jdbcType=BIT},
      </if>
      <if test="balanceRemindDot != null" >
        balance_remind_dot = #{balanceRemindDot,jdbcType=INTEGER},
      </if>
        balance_remind_word_id = #{balanceRemindWordId,jdbcType=VARCHAR},
      <if test="balanceSilentCsNick != null" >
        balance_silent_cs_nick = #{balanceSilentCsNick,jdbcType=VARCHAR},
      </if>
      <if test="balanceSilentGroupId != null" >
        balance_silent_group_id = #{balanceSilentGroupId,jdbcType=BIGINT},
      </if>
      <if test="balanceSilentFlag != null" >
        balance_silent_flag = #{balanceSilentFlag,jdbcType=TINYINT},
      </if>
        balance_silent_word_id = #{balanceSilentWordId,jdbcType=VARCHAR},
      <if test="balanceSilentSpareCsNick != null" >
        balance_silent_spare_cs_nick = #{balanceSilentSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="balanceSilentSpareGroupId != null" >
        balance_silent_spare_group_id = #{balanceSilentSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isSecBalanceSilentRemind != null" >
        is_sec_balance_silent_remind = #{isSecBalanceSilentRemind,jdbcType=BIT},
      </if>
      <if test="secBalanceSilentRemindDot != null" >
        sec_balance_silent_remind_dot = #{secBalanceSilentRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secBalanceSilentRemindWordId != null" >
        sec_balance_silent_remind_word_id = #{secBalanceSilentRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecBalanceRemind != null" >
        is_sec_balance_remind = #{isSecBalanceRemind,jdbcType=BIT},
      </if>
      <if test="secBalanceRemindDot != null" >
        sec_balance_remind_dot = #{secBalanceRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secBalanceRemindWordId != null" >
        sec_balance_remind_word_id = #{secBalanceRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="remindStartDot != null" >
        remind_start_dot = #{remindStartDot,jdbcType=INTEGER},
      </if>
      <if test="remindEndDot != null" >
        remind_end_dot = #{remindEndDot,jdbcType=INTEGER},
      </if>
      <if test="isSendProfitPoint != null" >
        is_send_profit_point = #{isSendProfitPoint,jdbcType=BIT},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where shop_id = #{shopId,jdbcType=BIGINT}
  </update>

  <select id="selecttShopSettingBatchRemindPresaleByShopId" resultMap="BaseResultMap">
    SELECT
    is_unpo_remind, is_bargain_remind, is_balance_remind
    FROM pes_shop_setting_batch_remind_presale
    WHERE shop_id = #{shopId}
  </select>

  <update id="updateBatchRemindByShopIdAndIsRemind">
    update pes_shop_setting_batch_remind_presale
    set is_unpo_remind    = #{isRemind},
        is_bargain_remind =  #{isRemind},
        is_balance_remind =  #{isRemind}
    where shop_id = #{shopId}
  </update>
</mapper>