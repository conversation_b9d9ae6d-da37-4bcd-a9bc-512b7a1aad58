<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesShopDbRelationMapper">
  <resultMap id="PesShopDbRelationDO" type="com.pes.jd.model.DO.PesShopDbRelation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="db_name" jdbcType="VARCHAR" property="dbName" />
    <result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
     <result column="type" jdbcType="INTEGER" property="type" />
    <result column="error_shop_flag" jdbcType="INTEGER" property="errorShopFlag" />
    <result column="shop_flag" jdbcType="INTEGER" property="shopFlag" />
    <result column="instance_url" jdbcType="VARCHAR" property="instanceUrl" />
  </resultMap>
 
  <select id="getSchemaInfoAndDbName"  parameterType="java.lang.Integer" resultType="com.pes.jd.model.DTO.SchemaInfoDTO">
		SELECT 
		    sc.db_name dbName,
		    sc.schema_id schemaId,
		    COUNT(sc.schema_id) shopNum
		FROM
		    pes_shop_db_relation sc
		        LEFT JOIN
		    (
			    SELECT 
			        db, schema_id
			    FROM
			        pes_shop
			    WHERE
			        status = 'active'
		    ) s 
		        ON sc.schema_id = s.schema_id
		        AND s.db = sc.db_name
		WHERE
		    sc.type = 1
		AND
			sc.shop_type = #{shopType,jdbcType=INTEGER}
		GROUP BY schemaId , dbName
		ORDER BY shopNum ASC
		LIMIT 1
	</select>
	<select id="getSchemaInfoAndDbNameForRt"  parameterType="java.lang.Integer" resultType="com.pes.jd.model.DTO.SchemaInfoDTO">
		SELECT 
		    sc.db_name dbName,
		    sc.schema_id schemaId,
		    COUNT(sc.schema_id) shopNum
		FROM
		    pes_shop_db_relation sc
	   LEFT JOIN
		    (
			    SELECT 
			        rt_db, rt_schema_id
			    FROM
			        pes_shop
			    WHERE
			        status = 'active'
		     ) s 
		        ON sc.schema_id = s.rt_schema_id
		        AND s.rt_db = sc.db_name
		WHERE
		    sc.type = 2
		AND
		    sc.shop_type = #{shopType,jdbcType=INTEGER}
		GROUP BY schemaId , dbName
		ORDER BY shopNum ASC
		LIMIT 1
	</select>
	
</mapper>