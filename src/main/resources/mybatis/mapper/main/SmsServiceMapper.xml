<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.SmsServiceMapper">
  <resultMap id="SmsServiceDTO" type="com.pes.jd.model.DTO.SmsServiceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="number" jdbcType="BIGINT" property="number" />
    <result column="use_number" jdbcType="BIGINT" property="useNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, number, use_number
  </sql>
  <select id="getByShopId" parameterType="java.lang.Long" resultMap="SmsServiceDTO">
    select
    <include refid="Base_Column_List" />
    from pes_shop_sms_service
    where shop_id = #{ShopId,jdbcType=BIGINT}
  </select>
  <select id="getSum" resultMap="SmsServiceDTO">
    select
     sum(number) number, sum(use_number) use_number
    from pes_shop_sms_service
  </select>
  <insert id="insert" parameterType="com.pes.jd.model.DO.SmsServiceDO">
    insert into pes_shop_sms_service (id, shop_id, number, use_number
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{number,jdbcType=BIGINT}, #{useNumber,jdbcType=BIGINT}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.SmsServiceDO">
    update pes_shop_sms_service
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="useNumber != null">
        use_number = #{useNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateSmsNumByShopId" parameterType="map">
    update
        pes_shop_sms_service
    set number=number+${number}
    where shop_id =#{shopId}
  </update>

  <update id="updateSmsUseNumber" >
    update pes_shop_sms_service
    set
            use_number = use_number + #{successCount}
    where shop_id = #{shopId,jdbcType=BIGINT}
  </update>

  <update id="updateSmsUseNumberByFailCount" >
    update pes_shop_sms_service
    set
            use_number = use_number - #{failCount}
    where shop_id = #{shopId,jdbcType=BIGINT}
  </update>
</mapper>