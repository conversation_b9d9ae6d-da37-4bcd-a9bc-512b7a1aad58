<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.UserReportPropertyMapper">

	<resultMap id="UserReportPropertyDO" type="com.pes.jd.model.DO.UserReportProperty">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="user_id" jdbcType="VARCHAR" property="userId" />
		<result column="property" jdbcType="VARCHAR" property="property" />
		<result column="type" jdbcType="BIT" property="type" />
	</resultMap>

	<sql id="base_field">
		user_id, property, type
	</sql>

	<insert id="insertUserReportProperty" parameterType="com.pes.jd.model.DO.UserReportProperty">
		INSERT INTO pes_user_report_property (id, user_id, property,type)
		VALUES 
		(
			#{id,jdbcType=BIGINT}, #{userId,jdbcType=VARCHAR},#{property,jdbcType=VARCHAR},#{type,jdbcType=BIT}
		)
	</insert>

	<delete id="deleteUserReportPropertyById" parameterType="java.lang.Long">
		DELETE FROM pes_user_report_property
		WHERE 
			id = #{id,jdbcType=BIGINT}
	</delete>

	<update id="updateUserReportPropertyByUserIdAndType" parameterType="com.pes.jd.model.DO.UserReportProperty">
		UPDATE pes_user_report_property
		<set>
			<if test="userId != null">
				user_id = #{userId,jdbcType=VARCHAR},
			</if>
			<if test="property != null">
				property = #{property,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=BIT},
			</if>
		</set>
		WHERE 
			user_id = #{userId}
		AND
			type = #{type}
	</update>

	<select id="getUserReportPropertyById" parameterType="java.lang.Long" resultMap="UserReportPropertyDO">
		SELECT
			<include refid="base_field" />
		FROM pes_user_report_property
		WHERE
			id = #{id,jdbcType=BIGINT}
	</select>

	<select id="getUserReportPropertyByUser" resultMap="UserReportPropertyDO">
		SELECT
		<include refid="base_field" />
		FROM pes_user_report_property
		WHERE
		user_id = #{user}
		AND type = #{type}
	</select>

</mapper>