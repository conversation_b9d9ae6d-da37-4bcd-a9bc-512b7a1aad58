<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.GoodsGroupMapper" >
  <resultMap id="GoodsGroupDO" type="com.pes.jd.model.DO.GoodsGroupDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
    <result column="modifiedby" property="modifiedby" jdbcType="BIGINT" />
    <result column="createby" property="createby" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
      <result column="dimension" jdbcType="TINYINT" property="dimension" />
  </resultMap>
  
  <resultMap id="GoodsGroupDTO" type="com.pes.jd.model.DTO.GoodsGroupDTO" >
    <id column="id" property="goodsGroupId" jdbcType="BIGINT" />
    <result column="name" property="goodsGroupName" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
    <result column="modifiedby" property="modifiedby" jdbcType="BIGINT" />
    <result column="createby" property="createby" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
      <result column="dimension" jdbcType="TINYINT" property="dimension" />
  </resultMap>
  <sql id="base_field" >
    id, name, shop_id, modified, modifiedby, createby, created,dimension
  </sql>
 
  <delete id="deleteGoodsGroupById" parameterType="java.lang.Long" >
    delete from pes_goods_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertGoodsGroup" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.pes.jd.model.DO.GoodsGroupDO" >
    insert into pes_goods_group (id, name, shop_id, 
      modified, modifiedby, createby, 
      created,dimension)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, 
      #{modified,jdbcType=TIMESTAMP}, #{modifiedby,jdbcType=BIGINT}, #{createby,jdbcType=BIGINT}, 
      #{created,jdbcType=TIMESTAMP},#{dimension,jdbcType=TINYINT})
  </insert>
 
  <update id="updateGoodsGroup" parameterType="com.pes.jd.model.DO.GoodsGroupDO" >
    update pes_goods_group
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedby != null" >
        modifiedby = #{modifiedby,jdbcType=BIGINT},
      </if>
      <if test="createby != null" >
        createby = #{createby,jdbcType=BIGINT},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="dimension != null" >
        dimension = #{dimension}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectGoodsGroupById" resultMap="GoodsGroupDO" parameterType="java.lang.Long">
  	SELECT 
    <include refid="base_field" />
    FROM pes_goods_group  WHERE  id=#{id,jdbcType=BIGINT}
  </select>
  
   <select id="selectGoodsGroupByShopId" resultMap="GoodsGroupDTO" parameterType="map">
    select 
    <include refid="base_field" />
    from pes_goods_group
    <where>
    	shop_id=#{shopId,jdbcType=BIGINT}
    </where>
  </select>
  
   <select id="selectGoodsGroupByName" resultMap="GoodsGroupDTO" parameterType="map">
    select 
    <include refid="base_field" />
    from pes_goods_group
    <where>
    <if test="groupId!=null and groupId!='' ">
    	id !=#{groupId}
    </if>
    	AND name=#{groupName}	
    	AND shop_id=#{shopId}
    </where>
  </select>
</mapper>