<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.AddressLiabryMapper">
	<resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.FullAddressAreaDTO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="phone_code" jdbcType="VARCHAR" property="phoneCode" />
    <result column="country_name" jdbcType="VARCHAR" property="countryName" />
    <result column="country_id" jdbcType="VARCHAR" property="countryId" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="town_name" jdbcType="VARCHAR" property="townName" />
    <result column="town_id" jdbcType="VARCHAR" property="townId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="data_version" jdbcType="VARCHAR" property="dataVersion" />
    <result column="level" jdbcType="TINYINT" property="level" />
  	</resultMap>


    
    <insert id="insertAddressLiabry" parameterType="map">
        insert into pes_address
        (provinceId,provinceName,cityId,cityName,countryId,countryName,townId,townName,phoneCode,dataVersion)
        values
        (#{fullAddressAreaDTO.provinceId},
        #{fullAddressAreaDTO.provinceName},
        #{fullAddressAreaDTO.cityId},
        #{fullAddressAreaDTO.cityName},
        #{fullAddressAreaDTO.countryId},
        #{fullAddressAreaDTO.countryName},
        #{fullAddressAreaDTO.townId},
        #{fullAddressAreaDTO.townName},
        #{fullAddressAreaDTO.phoneCode},
        #{fullAddressAreaDTO.dataVersion})

    </insert>
    
    
    <insert id="batchInsertAddressLiabry"  parameterType="java.util.List">
    INSERT INTO pes_address (phone_code,country_name,country_id,province_id,province_name,town_name,town_id,city_name,city_id,data_version,
    level
      )
   	 VALUES
    <foreach collection="list" item="fullAddressAreaDTO" separator=",">
     (
    	#{fullAddressAreaDTO.phoneCode},
        #{fullAddressAreaDTO.countryName},
        #{fullAddressAreaDTO.countryId},
        #{fullAddressAreaDTO.provinceId},
        #{fullAddressAreaDTO.provinceName},
        #{fullAddressAreaDTO.townName},
        #{fullAddressAreaDTO.townId},
        #{fullAddressAreaDTO.cityName},
        #{fullAddressAreaDTO.cityId},
        #{fullAddressAreaDTO.dataVersion},
         #{fullAddressAreaDTO.level}
      )
    </foreach>
    </insert>
    
    
   <select id="getfullAddressList" parameterType="com.pes.jd.model.DO.AddressLiabryDO" resultMap="BaseResultMap">
    select 
    country_name,
    country_id,
    province_id,
    province_name,
    city_name,
    city_id 
    from pes_address
    where level = #{level,jdbcType=TINYINT}
  </select>
    
    
   <select id="getProviceList" parameterType="com.pes.jd.model.DO.AddressLiabryDO" resultType="com.pes.jd.model.DTO.AddressAreaProviceDTO">
    select 
    province_id,
    province_name
    from pes_address  
    where level = #{level,jdbcType=TINYINT}
  </select>
  
  
    <select id="getCityList" parameterType="com.pes.jd.model.DO.AddressLiabryDO" resultType="com.pes.jd.model.DTO.AddressAreaCityDTO">
    select 
    province_id,
    province_name,
    city_name,
    city_id 
    from pes_address 
    where level = #{level,jdbcType=TINYINT}
  </select>
  
     <select id="getCountryList" parameterType="com.pes.jd.model.DO.AddressLiabryDO" resultType="com.pes.jd.model.DTO.AddressAreaCountryDTO">
    select 
    city_name,
    city_id, 
    country_name,
    country_id 
    from pes_address 
    where level = #{level,jdbcType=TINYINT}
  </select>
  
    
     <select id="getAddressByParam" parameterType="com.pes.jd.model.DO.AddressLiabryDO" resultMap="BaseResultMap">
    select 
    country_name,
    country_id,
    province_id,
    province_name,
    city_name,
    city_id
    from pes_address
    where level = #{level,jdbcType=TINYINT} 
    <if test="cityId != null  and cityId !=''">
        and city_id = #{cityId}
     </if>
   <if test="provinceId != null  and provinceId !=''">
        and province_id = #{provinceId}
     </if>
   <if test="countryId != null  and countryId !=''">
        and country_id = #{countryId}
     </if>
    
  </select>
    
    
    
</mapper>