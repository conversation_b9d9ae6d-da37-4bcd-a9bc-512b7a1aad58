<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopSensitiveWordMapper">
    <resultMap id="ShopSensitiveWordDTO" type="com.pes.jd.model.DTO.ShopSensitiveWordDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="sensitive_word" jdbcType="VARCHAR" property="sw"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
        <result column="modified_by" jdbcType="BIGINT" property="modifiedBy"/>
        <result column="modified" jdbcType="TIMESTAMP" property="modified"/>
        <result column="modifiedName" jdbcType="VARCHAR" property="modifiedName"/>
    </resultMap>
    <sql id="base_field">
        id, shop_id, sensitive_word, created, modified_by, modified
    </sql>

    <select id="selectSensitiveWordByShopId" resultType="String">
        SELECT
	        sensitive_word
        FROM
	       pes_shop_sensitive_word
	    WHERE
	        shop_id=0
        UNION ALL
        SELECT
            sensitive_word
        FROM
            pes_shop_sensitive_word
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
  </select>


    <select id="batchLikeByShopIdForLst" resultMap="ShopSensitiveWordDTO">
        SELECT
         shop_id AS shop_id, sensitive_word, created AS created, modified_by, "admin" AS modifiedName, modified
        FROM
        pes_shop_sensitive_word
        WHERE
        shop_id=0
        AND sensitive_word LIKE CONCAT('%',#{sensitiveWord},'%')
		UNION ALL
        SELECT
        pssw.shop_id AS shop_id, sensitive_word, pssw.created AS created, modified_by,pu.show_nick AS modifiedName , modified
        FROM
        pes_shop_sensitive_word AS pssw LEFT JOIN pes_user  AS pu
        ON pssw.modified_by=pu.id
        WHERE
        pssw.shop_id=#{shopId}
        AND
        pssw.sensitive_word LIKE CONCAT('%',#{sensitiveWord},'%')
    </select>

    <select id="selectExistenceSensitiveWordByShopId" resultType="int">
        SELECT count(1) from pes_shop_sensitive_word WHERE  shop_id=#{shopId}
    </select>

    <select id="selectSensitiveWordByShopIdForLst" parameterType="java.lang.Long" resultMap="ShopSensitiveWordDTO">
        SELECT
         shop_id AS shop_id, sensitive_word, created AS created, modified_by, "admin" AS modifiedName, modified
        FROM
        pes_shop_sensitive_word
        WHERE
        shop_id=0
		UNION ALL
        SELECT
         pssw.shop_id AS shop_id, sensitive_word, pssw.created AS created, modified_by,pu.show_nick AS modifiedName , modified
        FROM
        pes_shop_sensitive_word AS pssw LEFT JOIN pes_user  AS pu
        ON pssw.modified_by=pu.id
        WHERE
        pssw.shop_id=#{shopId}
    </select>


    <select id="selectSensitiveWordNewestByShopId" parameterType="long" resultMap="ShopSensitiveWordDTO">
         SELECT
         pssw.shop_id AS shop_id, sensitive_word, pssw.created AS created, modified_by,pu.show_nick AS modifiedName , modified
        FROM
        pes_shop_sensitive_word AS pssw LEFT JOIN pes_user  AS pu
        ON pssw.modified_by=pu.id
        WHERE
        pssw.shop_id=#{shopId}
        ORDER BY pssw.created DESC, pssw.modified DESC
        LIMIT 1
    </select>

    <insert id="insertSensitiveWord">
        INSERT INTO pes_shop_sensitive_word (id, shop_id, sensitive_word,
        created,modified_by,modified)
        VALUES
        <foreach collection="record" item="lte" separator=",">
            (null,#{shopId},#{lte},#{date},#{modifiedBy},#{date})
        </foreach>
    </insert>

    <update id="updateSensitiveWordByShopId">
        <foreach collection="record" item="newValue" index="odlValue" open="" close="" separator=";">
            UPDATE pes_shop_sensitive_word
            <set>
                sensitive_word=#{newValue},
                modified_By=#{modifiedBy},
                modified=#{date}
            </set>
            WHERE shop_id = #{shopId}
            AND sensitive_word=#{odlValue}
        </foreach>
    </update>

    <update id="updateSensitiveWordDate">
        UPDATE pes_shop_sensitive_word
            <set>
                ${field}= #{date}
            </set>
        WHERE shop_id = #{shopId}
    </update>


    <delete id="deleteSensitiveWordById">
        <foreach collection="record" item="tem" index="" open="" close="" separator=";">
        DELETE FROM pes_shop_sensitive_word
        WHERE shop_id=#{shopId}
        AND
        sensitive_word=#{tem}
        </foreach>
    </delete>

</mapper>