<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.OpinionRecordMapper">
  <resultMap id="OpinionRecordDO" type="com.pes.jd.model.DO.OpinionRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="phone" jdbcType="BIGINT" property="phone" />
    <result column="qq" jdbcType="BIGINT" property="qq" />
    <result column="create_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
  </resultMap>
  
  <sql id="base_field">
    id, content, phone, qq, create_by, created
  </sql>
 
   <insert id="insertOpinionRecord" parameterType="com.pes.jd.model.DO.OpinionRecordDO">
    insert into pes_opinion_record 
    (		content, phone, 
	      	qq, create_by, created
	 )
    VALUES 
    (
		  #{record.content}, #{record.phone}, 
	      #{record.qq}, #{record.createBy}, #{record.created}
      )
  </insert>
 
</mapper>