<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopBatchRemindTaskMapper">

    <resultMap type="com.pes.jd.model.DTO.ShopBatchRemindTaskDTO" id="ShopBatchRemindTaskMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="shopId" column="shop_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="isRemind" column="is_remind" jdbcType="INTEGER"/>
        <result property="sendType" column="send_type" jdbcType="INTEGER"/>
        <result property="taskType" column="task_type" jdbcType="INTEGER"/>
        <result property="isPermanent" column="is_permanent" jdbcType="INTEGER"/>
        <result property="taskStartTime" column="task_start_time" jdbcType="TIMESTAMP"/>
        <result property="taskEndTime" column="task_end_time" jdbcType="TIMESTAMP"/>
        <result property="remindTime" column="remind_time" jdbcType="INTEGER"/>
        <result property="remindStartTime" column="remind_start_time" jdbcType="INTEGER"/>
        <result property="remindEndTime" column="remind_end_time" jdbcType="INTEGER"/>
        <result property="csType" column="cs_type" jdbcType="INTEGER"/>
        <result property="csGroup" column="cs_group" jdbcType="INTEGER"/>
        <result property="csNick" column="cs_nick" jdbcType="VARCHAR"/>
        <result property="remindWord" column="remind_word" jdbcType="VARCHAR"/>
        <result property="skuId" column="sku_id" jdbcType="VARCHAR"/>
        <result property="wareId" column="ware_id" jdbcType="VARCHAR"/>
        <result property="dimension" column="dimension" jdbcType="INTEGER"/>
        <result property="isSendProductLinks" column="is_send_product_links" jdbcType="INTEGER"/>
        <result property="consultWord" column="consult_word" jdbcType="VARCHAR"/>
        <result property="silenceWord" column="silence_word" jdbcType="VARCHAR"/>
        <result property="consultWord" column="consult_word" jdbcType="VARCHAR"/>
        <result property="consultWordId" column="consult_word_id" jdbcType="INTEGER"/>
        <result property="silenceWordId" column="silence_word_id" jdbcType="INTEGER"/>
        <result property="consultTemplateId" column="consult_template_id" jdbcType="VARCHAR"/>
        <result property="silenceTemplateId" column="silence_template_id" jdbcType="VARCHAR"/>
        <result property="couponLink" column="coupon_link" jdbcType="VARCHAR"/>
        <result property="couponStartTime" column="coupon_start_time" jdbcType="TIMESTAMP"/>
        <result property="couponEndTime" column="coupon_end_time" jdbcType="TIMESTAMP"/>
        <result property="created" column="created" jdbcType="TIMESTAMP"/>
        <result property="modified" column="modified" jdbcType="TIMESTAMP"/>
        <result property="prompt" column="is_prompt" jdbcType="BIT"/>
        <result property="problem" column="is_problem" jdbcType="BIT"/>
        <result property="firstPrompt" column="is_first_prompt" jdbcType="INTEGER"/>
    </resultMap>

    <!--查询单个-->
    <select id="queryById" resultMap="ShopBatchRemindTaskMap">
        select id,
               shop_id,
               name,
               is_remind,
               send_type,
               task_type,
               is_permanent,
               task_start_time,
               task_end_time,
               remind_time,
               remind_start_time,
               remind_end_time,
               cs_type,
               cs_group,
               cs_nick,
               remind_word,
               dimension,
               sku_id,
               ware_id,
               is_send_product_links,
               consult_word,
               silence_word,
               consult_word_id,
               silence_word_id,
               coupon_link,
               coupon_start_time,
               coupon_end_time,
               created,
               modified,
               is_prompt
        from pes_shop_batch_remind_task
        where id = #{id} and shop_id=#{shopId}
    </select>

    <!--查询指定行数据-->
    <select id="queryAllByLimit" resultMap="ShopBatchRemindTaskMap">
        select id,
               shop_id,
               name,
               is_remind,
               send_type,
               task_type,
               is_permanent,
               task_start_time,
               task_end_time,
               remind_time,
               remind_start_time,
               remind_end_time,
               cs_type,
               cs_group,
               cs_nick,
               remind_word,
                dimension,
               sku_id,
               ware_id,
               is_send_product_links,
               consult_word,
               silence_word,
               coupon_link,
               coupon_start_time,
               coupon_end_time,
               created,
               modified
        from pes_shop_batch_remind_task limit #{offset}, #{limit}
    </select>

    <!--通过实体作为筛选条件查询-->
    <select id="queryAll" resultMap="ShopBatchRemindTaskMap">
        select
        id, shop_id, name, is_remind, send_type, task_type, is_permanent, task_start_time, task_end_time, remind_time,
        remind_start_time, remind_end_time, cs_type, cs_group, cs_nick, remind_word,  dimension,
        sku_id,
        ware_id, is_send_product_links,
        consult_word, silence_word, coupon_link, coupon_start_time, coupon_end_time, created, modified
        from pes_shop_batch_remind_task
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="shopId != null">
                and shop_id = #{shopId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="isRemind != null">
                and is_remind = #{isRemind}
            </if>
            <if test="sendType != null">
                and send_type = #{sendType}
            </if>
            <if test="taskType != null">
                and task_type = #{taskType}
            </if>
            <if test="isPermanent != null">
                and is_permanent = #{isPermanent}
            </if>
            <if test="taskStartTime != null">
                and task_start_time = #{taskStartTime}
            </if>
            <if test="taskEndTime != null">
                and task_end_time = #{taskEndTime}
            </if>
            <if test="remindTime != null">
                and remind_time = #{remindTime}
            </if>
            <if test="remindStartTime != null">
                and remind_start_time = #{remindStartTime}
            </if>
            <if test="remindEndTime != null">
                and remind_end_time = #{remindEndTime}
            </if>
            <if test="csType != null">
                and cs_type = #{csType}
            </if>
            <if test="csGroup != null">
                and cs_group = #{csGroup}
            </if>
            <if test="csNick != null and csNick != ''">
                and cs_nick = #{csNick}
            </if>
            <if test="remindWord != null and remindWord != ''">
                and remind_word = #{remindWord}
            </if>
            <if test="skuId != null and skuId != ''">
                and sku_id = #{skuId}
            </if>
            <if test="isSendProductLinks != null">
                and is_send_product_links = #{isSendProductLinks}
            </if>
            <if test="consultWord != null and consultWord != ''">
                and consult_word = #{consultWord}
            </if>
            <if test="silenceWord != null and silenceWord != ''">
                and silence_word = #{silenceWord}
            </if>
            <if test="couponLink != null and couponLink != ''">
                and coupon_link = #{couponLink}
            </if>
            <if test="couponStartTime != null">
                and coupon_start_time = #{couponStartTime}
            </if>
            <if test="couponEndTime != null">
                and coupon_end_time = #{couponEndTime}
            </if>
            <if test="created != null">
                and created = #{created}
            </if>
            <if test="modified != null">
                and modified = #{modified}
            </if>
        </where>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into pes_shop_batch_remind_task(shop_id, name, is_remind, send_type, task_type, is_permanent,
                                                      task_start_time, task_end_time, remind_time, remind_start_time,
                                                      remind_end_time, cs_type, cs_group, cs_nick, remind_word,  dimension,
               sku_id,
               ware_id,
                                                      is_send_product_links, consult_word, silence_word, coupon_link,
                                                      coupon_start_time, coupon_end_time, created, modified)
        values (#{shopId}, #{name}, #{isRemind}, #{sendType}, #{taskType}, #{isPermanent}, #{taskStartTime},
                #{taskEndTime}, #{remindTime}, #{remindStartTime}, #{remindEndTime}, #{csType}, #{csGroup}, #{csNick},
                #{remindWord}, #{dimension},  #{skuId},  #{wareId}, #{isSendProductLinks}, #{consultWord}, #{silenceWord}, #{couponLink},
                #{couponStartTime}, #{couponEndTime}, #{created}, #{modified})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pes_shop_batch_remind_task(shop_id, name, is_remind, send_type, task_type, is_permanent,
        task_start_time, task_end_time, remind_time, remind_start_time, remind_end_time, cs_type, cs_group, cs_nick,
        remind_word, dimension,
        sku_id,
        ware_id, is_send_product_links, consult_word, silence_word, consult_word_id, silence_word_id, coupon_link, coupon_start_time,
        coupon_end_time, created, modified,consult_template_id,silence_template_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.shopId}, #{entity.name}, #{entity.isRemind}, #{entity.sendType}, #{entity.taskType},
            #{entity.isPermanent}, #{entity.taskStartTime}, #{entity.taskEndTime}, #{entity.remindTime},
            #{entity.remindStartTime}, #{entity.remindEndTime}, #{entity.csType}, #{entity.csGroup}, #{entity.csNick},
            #{entity.remindWord}, #{entity.dimension}, #{entity.skuId}, #{entity.wareId}, #{entity.isSendProductLinks}, #{entity.consultWord},
            #{entity.silenceWord}, #{entity.consultWordId}, #{entity.silenceWordId}, #{entity.couponLink}, #{entity.couponStartTime}, #{entity.couponEndTime},
            #{entity.created}, #{entity.modified}, #{entity.consultTemplateId}, #{entity.silenceTemplateId})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into pes_shop_batch_remind_task(shop_id, name, is_remind, send_type, task_type, is_permanent,
        task_start_time, task_end_time, remind_time, remind_start_time, remind_end_time, cs_type, cs_group, cs_nick,
        remind_word, dimension,
        sku_id,
        ware_id, is_send_product_links, consult_word, silence_word, coupon_link, coupon_start_time,
        coupon_end_time, created, modified)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.shopId}, #{entity.name}, #{entity.isRemind}, #{entity.sendType}, #{entity.taskType},
            #{entity.isPermanent}, #{entity.taskStartTime}, #{entity.taskEndTime}, #{entity.remindTime},
            #{entity.remindStartTime}, #{entity.remindEndTime}, #{entity.csType}, #{entity.csGroup}, #{entity.csNick},
            #{entity.remindWord}, #{entity.dimension}, #{entity.skuId}, #{entity.wareId}, #{entity.isSendProductLinks}, #{entity.consultWord},
            #{entity.silenceWord}, #{entity.couponLink}, #{entity.couponStartTime}, #{entity.couponEndTime},
            #{entity.created}, #{entity.modified})
        </foreach>
        on duplicate key update
        shop_id = values(shop_id) , name = values(name) , is_remind = values(is_remind) , send_type = values(send_type)
        , task_type = values(task_type) , is_permanent = values(is_permanent) , task_start_time =
        values(task_start_time) , task_end_time = values(task_end_time) , remind_time = values(remind_time) ,
        remind_start_time = values(remind_start_time) , remind_end_time = values(remind_end_time) , cs_type =
        values(cs_type) , cs_group = values(cs_group) , cs_nick = values(cs_nick) , remind_word = values(remind_word) ,
        dimension = values(dimension) ,sku_id = values(sku_id) ,ware_id = values(ware_id) , is_send_product_links = values(is_send_product_links) , consult_word =
        values(consult_word) , silence_word = values(silence_word) , coupon_link = values(coupon_link) ,
        coupon_start_time = values(coupon_start_time) , coupon_end_time = values(coupon_end_time) , created =
        values(created) , modified = values(modified)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update pes_shop_batch_remind_task
        <set>
            <if test="shopId != null">
                shop_id = #{shopId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="isRemind != null">
                is_remind = #{isRemind},
            </if>
            <if test="sendType != null">
                send_type = #{sendType},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="isPermanent != null">
                is_permanent = #{isPermanent},
            </if>
            <if test="taskStartTime != null">
                task_start_time = #{taskStartTime},
            </if>
            <if test="taskEndTime != null">
                task_end_time = #{taskEndTime},
            </if>
            <if test="remindTime != null">
                remind_time = #{remindTime},
            </if>
            <if test="remindStartTime != null">
                remind_start_time = #{remindStartTime},
            </if>
            <if test="remindEndTime != null">
                remind_end_time = #{remindEndTime},
            </if>
            <if test="csType != null">
                cs_type = #{csType},
            </if>
            <if test="csGroup != null">
                cs_group = #{csGroup},
            </if>
            <if test="csNick != null and csNick != ''">
                cs_nick = #{csNick},
            </if>
            <if test="remindWord != null and remindWord != ''">
                remind_word = #{remindWord},
            </if>
            <if test="dimension != null">
                dimension = #{dimension},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId},
            </if>
            <if test="wareId != null">
                ware_id = #{wareId},
            </if>
            <if test="isSendProductLinks != null">
                is_send_product_links = #{isSendProductLinks},
            </if>
            <if test="consultWord != null and consultWord != ''">
                consult_word = #{consultWord},
            </if>
            <if test="silenceWord != null and silenceWord != ''">
                silence_word = #{silenceWord},
            </if>
            <if test="consultWordId != null and consultWordId != ''">
                consult_word_id = #{consultWordId},
            </if>
            <if test="silenceWordId != null and silenceWordId != ''">
                silence_word_id = #{silenceWordId},
            </if>
            coupon_link = #{couponLink},

            coupon_start_time = #{couponStartTime},

            coupon_end_time = #{couponEndTime},
            <if test="created != null">
                created = #{created},
            </if>
            <if test="modified != null">
                modified = #{modified},
            </if>
            <if test="consultTemplateId != null and consultTemplateId != ''">
                consult_template_id = #{consultTemplateId},
            </if>
            <if test="silenceTemplateId != null and silenceTemplateId != ''">
                silence_template_id = #{silenceTemplateId}
            </if>
        </set>
        where id = #{id} and shop_id=#{shopId}
    </update>

    <update id="updateShopBatchRemindTaskByIds">
        update pes_shop_batch_remind_task
        set is_remind = #{isRemind}
        where
        id in
        <foreach collection="toUpdateIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateShopBatchRemindTaskByIdsByIsremind">
        update pes_shop_batch_remind_task
        set is_remind = #{isRemind}
        where is_remind = #{targetIsRemind} and
        id in
        <foreach collection="toUpdateIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from pes_shop_batch_remind_task
        where id = #{id} and shop_id=#{shopId}
    </delete>

    <select id="queryLstByShopId" resultMap="ShopBatchRemindTaskMap">
                select
                id,
               shop_id,
               name,
               is_remind,
               send_type,
               task_type,
               is_permanent,
               task_start_time,
               task_end_time,
               remind_time,
               remind_start_time,
               remind_end_time,
               cs_type,
               cs_group,
               cs_nick,
               remind_word,
               dimension,
               sku_id,
               ware_id,
               is_send_product_links,
               consult_word,
               silence_word,
               consult_template_id,
               silence_template_id,
               coupon_link,
               coupon_start_time,
               coupon_end_time,
               created,
               modified
                from pes_shop_batch_remind_task
                where shop_id = #{shopId}
    </select>

    <select id="queryLstByShopIdAndSendType" resultMap="ShopBatchRemindTaskMap">
        select
        id,
        shop_id,
        name,
        is_remind,
        send_type,
        task_type,
        is_permanent,
        task_start_time,
        task_end_time,
        remind_time,
        remind_start_time,
        remind_end_time,
        cs_type,
        cs_group,
        cs_nick,
        remind_word,
        dimension,
        sku_id,
        ware_id,
        is_send_product_links,
        consult_word,
        silence_word,
        consult_word_id,
        silence_word_id,
        coupon_link,
        coupon_start_time,
        coupon_end_time,
        created,
        modified,
        is_prompt,
        is_problem,
        is_first_prompt
        from pes_shop_batch_remind_task
        where shop_id = #{param.shopId}
        <if test="param.name != null and param.name != ''">
            and name like CONCAT('%',#{param.name},'%')
        </if>

          <if test="param.isRemind !=null and param.isRemind !=''">
              and is_remind=#{param.isRemind}
          </if>

        <if test="param.remindWord != null and param.remindWord != '' and param.sendType != null and param.sendType != '' and param.sendType ==2">
            and ( consult_word like CONCAT('%',#{param.remindWord},'%')
            or silence_word like CONCAT('%',#{param.remindWord},'%') )
        </if>

        <if test="param.remindWord != null and param.remindWord != '' and param.sendType != null and param.sendType != '' and param.sendType !=2">
            and remind_word like CONCAT('%',#{param.remindWord},'%')
        </if>
        <if test="param.taskType != null and param.taskType != ''">
            and task_type = #{param.taskType}
        </if>
        <if test="param.sendType != null and param.sendType != '' and param.sendType == 1">
            and (send_type = #{param.sendType} or send_type = 0)
        </if>
        <if test="param.sendType != null and param.sendType != '' and param.sendType == 2">
            and send_type = #{param.sendType}
        </if>
        <if test="param.startDate != null and param.startDate != ''">
            and (
            ( #{param.startDate} <![CDATA[ <= ]]> task_start_time
            and #{param.endDate} <![CDATA[ >= ]]> task_start_time)
            or ( #{param.startDate} <![CDATA[ <= ]]> task_end_time
            and #{param.endDate} <![CDATA[ >= ]]> task_end_time)
            or ( #{param.startDate} <![CDATA[ >= ]]> task_start_time
            and #{param.endDate} <![CDATA[ <= ]]> task_end_time)
            or is_permanent=1
            )
        </if>
        <if test="param.skuIdList != null and param.skuIdList.size() > 0 and param.dimension==1">
            and sku_id in
            <foreach collection="param.skuIdList" item="sku" index="index" open="(" close=")" separator=",">
                #{sku}
            </foreach>
        </if>
        <if test="param.skuIdList != null and param.skuIdList.size() > 0 and param.dimension==2">
            and ware_id in
            <foreach collection="param.skuIdList" item="wareId" index="index" open="(" close=")" separator=",">
                #{wareId}
            </foreach>
        </if>
        order by created desc
    </select>

    <update id="updateIsRemind">
        update pes_shop_batch_remind_task
        <set>
            is_remind = #{isRemind}
        </set>
        where id = #{id} and shop_id=#{shopId}
    </update>

    <update id="updateIsRemindByShopId">
        update pes_shop_batch_remind_task
        <set>
            is_remind = #{isRemind}
        </set>
        where shop_id=#{shopId} and send_type=#{sendType}
    </update>

    <update id="updateIsRemindByParam">
        update pes_shop_batch_remind_task
        <set>
            is_remind = #{isRemind}
        </set>
        where shop_id=#{shopId} and send_type=#{sendType} and is_remind=#{targatStatus}
    </update>

    <select id="selectTaskNumByShopIdAndSendTypeAndIsRemind" resultType="int">
        select count(*)
        from pes_shop_batch_remind_task
        where  shop_id=#{shopId} and send_type=#{sendType} and is_remind = #{isRemind}
    </select>

    <select id="getShopBatchRemindTask" resultType="int">
       select count(*)
        from pes_shop_batch_remind_task
        where  shop_id=#{shopId} and name =#{name}
    </select>

    <select id="queryShopBatchRemindTaskByDdWord" resultMap="ShopBatchRemindTaskMap">
        select *
        from pes_shop_batch_remind_task
        where  shop_id=#{shopId} and remind_word like CONCAT('%',#{word},'%')
    </select>

    <select id="selectTaskByShopIdsAndTaskType" resultMap="ShopBatchRemindTaskMap">
        select * from pes_shop_batch_remind_task
        where shop_id in
        <foreach collection="shopIds" item="shopId" close=")" open="(" separator=",">
            #{shopId}
        </foreach>
        and task_type=#{taskType}
    </select>

    <select id="queryShopBatchRemindTaskByIds" resultMap="ShopBatchRemindTaskMap">
        select * from pes_shop_batch_remind_task
        where id in (${ids})
    </select>

    <select id="selectShopRemindTaskReserve" resultType="java.lang.Integer">
        select count(1)
        from pes_shop_batch_remind_task
        where
        shop_id = #{shopId}
        and send_type = 0
    </select>

    <select id="selectShopRemindTaskListByType" resultMap="ShopBatchRemindTaskMap">
        select * from pes_shop_batch_remind_task
        where
        shop_id = #{shopId}
        and task_type = #{type}
    </select>

    <select id="selectTaskNumByShopIdAndSendTypeAndTaskTypeAndIsRemind" resultType="int">
        select count(*)
        from pes_shop_batch_remind_task
        where  shop_id=#{shopId} and send_type=#{sendType} and task_type=#{taskType} and is_remind = #{isRemind}
    </select>

    <select id="getDefaultShopBatchRemindTask" resultType="java.lang.Integer">
        select count(1)
        from pes_shop_batch_remind_task
        where shop_id = #{shopId}
        and send_type = 0
    </select>

    <select id="selectShopBatchRemindTaskListByTypes" resultMap="ShopBatchRemindTaskMap">
        select * from pes_shop_batch_remind_task
        where
        shop_id = #{shopId}
        and task_type in
        <foreach collection="taskTypeList" item="type" open="(" close=")" separator=",">
            #{type}
        </foreach>
        and is_remind = 1
        and send_type = 1
    </select>

    <select id="selectSmsConsultWordTaskNum" resultType="java.lang.Integer">
        select count(1) from pes_shop_batch_remind_task
        where
        shop_id = #{shopId}
        and send_type = #{sendType}
        and consult_word_id = #{consultWordId}
    </select>

    <select id="selectSmsSilenceWordTaskNum" resultType="java.lang.Integer">
        select count(1) from pes_shop_batch_remind_task
        where
        shop_id = #{shopId}
        and send_type = #{sendType}
        and silence_word_id = #{silenceWordId}
    </select>
    <select id="selectDefaultTask" resultType="java.lang.Integer">
        select is_first_prompt
        from pes_shop_batch_remind_task
        where shop_id = #{shopId}
          and send_type = 0
    </select>

    <update id="updateBatchRemindByShopIdAndIsRemind">
        update pes_shop_batch_remind_task
        set is_remind = #{isRemind}
        where shop_id = #{shopId}
        and send_type =1
    </update>

    <update id="readNotice">
        update pes_shop_batch_remind_task
        set is_problem = 0
        where shop_id = #{shopId}
    </update>

    <update id="clickPrompt">
        update pes_shop_batch_remind_task
        set is_prompt = 0
        where id = #{id}
    </update>

    <update id="updateNoticeAndPromptById">
        update pes_shop_batch_remind_task
        set is_prompt = #{status},
        is_problem = #{status}
        where
        id in
        <foreach collection="needToUpdateTaskIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateTaskToDisplayed">
        update pes_shop_batch_remind_task
        set is_first_prompt = #{firstPrompt}
        where shop_id = #{shopId}
          and send_type = 0;
    </update>

    <select id="searchByWordId" resultMap="ShopBatchRemindTaskMap">
        select * from pes_shop_batch_remind_task
        where consult_word_id = #{wordId}
        or silence_word_id=#{wordId}
    </select>

    <update id="updateRemindById">
        update pes_shop_batch_remind_task
        set is_remind = #{isRemind}
        where id =  #{id}
    </update>
</mapper>