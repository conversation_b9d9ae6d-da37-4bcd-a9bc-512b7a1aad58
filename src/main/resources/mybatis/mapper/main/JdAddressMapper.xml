<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.JdAddressMapper">


  <select id="getAllJdAddressData" resultType="com.pes.jd.model.DO.JdAddress">

    SELECT id, area_id, area_name, parent_id, level
    FROM pes_jd_address
    ORDER BY level, area_id

  </select>

  <!-- 获取最新的创建时间 -->
  <select id="getLatestCreateTime" resultType="java.time.LocalDateTime">
    SELECT MAX(create_time)
    FROM pes_jd_address
  </select>

  <!-- 批量插入 -->
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO pes_jd_address (area_id, area_name, parent_id, level, create_time)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.areaId}, #{item.areaName}, #{item.parentId}, #{item.level}, #{item.createTime})
    </foreach>
  </insert>

  <!-- 删除所有数据 -->
  <delete id="deleteAll">
    DELETE FROM pes_jd_address
  </delete>

  <!-- 统计总数据量 -->
  <select id="countAll" resultType="java.lang.Long">
    SELECT COUNT(*) FROM pes_jd_address
  </select>
</mapper>