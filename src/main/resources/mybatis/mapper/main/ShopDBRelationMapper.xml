<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopDBRelationMapper">

  <resultMap id="ShopDBRelationDTO" type="com.pes.jd.model.DTO.ShopDBRelationDTO">
    <result column="db_name" jdbcType="VARCHAR" property="dbName" />
    <result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
    <result column="error_shop_flag" jdbcType="INTEGER" property="errorShopFlag" />
    <result column="shop_flag" jdbcType="INTEGER" property="shopFlag" />
    <result column="instance_url" jdbcType="VARCHAR" property="instanceUrl" />
  </resultMap>

  <select id="getShopDBRelationByDbName" parameterType="map" resultMap="ShopDBRelationDTO">
    SELECT instance_url
    FROM pes_shop_db_relation
   	WHERE
   		db_name = #{dbName}
   	AND schema_id = #{schemaId}
   	LIMIT 1
  </select>

    <select id="searchShopDBRelationByType" parameterType="map" resultMap="ShopDBRelationDTO">
    SELECT db_name,schema_id,error_shop_flag,shop_flag,instance_url
    FROM pes_shop_db_relation
   	WHERE
   	    type=#{type}
  </select>

    <select id="searchShopDBNameAndSchemaIdByType" parameterType="map" resultMap="ShopDBRelationDTO">
    SELECT db_name,schema_id
    FROM pes_shop_db_relation
   	WHERE
   	    type=#{type}
  </select>

    <select id="searchShopDBRelationByDbNameAndSchemaIdAndType" parameterType="map" resultMap="ShopDBRelationDTO">
    SELECT db_name,schema_id,error_shop_flag,shop_flag,instance_url
    FROM pes_shop_db_relation
   	WHERE
   	    type=#{type}
   	    and db_name=#{dbName}
   	    and schema_id=#{schemaId}
  </select>
 
</mapper>