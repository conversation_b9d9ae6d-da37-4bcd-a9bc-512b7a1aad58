<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesUserMenuPermissionMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesUserMenuPermission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="menu_resource_id" jdbcType="BIGINT" property="menuResourceId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, menu_resource_id, cs_nick
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_user_menu_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByShopAccounts" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_user_menu_permission
    <if test="shopAccounts!=null and shopAccounts.size()>0">
      where cs_nick in
      <foreach collection="shopAccounts" item="account" open="(" close=")" separator=",">
        #{account.nick}
      </foreach>
    </if>
  </select>
    <select id="searchByNicks" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from pes_user_menu_permission
        <if test="nicks != null and nicks.size() != 0">
            where cs_nick in
            <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
                #{nick}
            </foreach>
        </if>
    </select>
  <select id="selectByMenuId" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from pes_user_menu_permission
    where menu_resource_id =  #{id} and cs_nick = #{nick}

  </select>
  <select id="selectCountByNick" resultType="java.lang.Integer">
    select count(1) from pes_user_menu_permission where cs_nick = #{nick}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_user_menu_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByShopAccounts">
    delete from pes_user_menu_permission
    where cs_nick in
    <foreach collection="shopAccounts" item="account" open="(" close=")" separator=",">
      #{account.nick}
    </foreach>
  </delete>
  <delete id="deleteByMenuResourceId">
    delete from pes_user_menu_permission where menu_resource_id = #{id}
  </delete>
  <delete id="deleteByShopAccountsAndMenu">


    <foreach collection="shopAccounts" item="shopAccount">
      <foreach collection="menus" item="menu">
      delete from pes_user_menu_permission where menu_resource_id = #{menu.id} and cs_nick = #{shopAccount.nick};
      </foreach>
    </foreach>

  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.PesUserMenuPermission">
    insert into pes_user_menu_permission (id, user_id, menu_resource_id,
      cs_nick)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{menuResourceId,jdbcType=BIGINT},
      #{csNick,jdbcType=VARCHAR})
  </insert>
  <insert id="insertByShopAccountsAndMenus">
    <foreach collection="shopAccounts" item="shopAccount">
      <foreach collection="menus" item="menu">
        insert into pes_user_menu_permission (id, user_id, menu_resource_id,
        cs_nick)
        values (null, null , #{menu.id,jdbcType=BIGINT},
        #{shopAccount.nick,jdbcType=VARCHAR});
      </foreach>
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.PesUserMenuPermission">
    insert into pes_user_menu_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="menuResourceId != null">
        menu_resource_id,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="menuResourceId != null">
        #{menuResourceId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.PesUserMenuPermission">
    update pes_user_menu_permission
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="menuResourceId != null">
        menu_resource_id = #{menuResourceId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.PesUserMenuPermission">
    update pes_user_menu_permission
    set user_id = #{userId,jdbcType=BIGINT},
      menu_resource_id = #{menuResourceId,jdbcType=BIGINT},
      cs_nick = #{csNick,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteMenuServicePermissionByCsNick" parameterType="map">
  	delete from pes_user_menu_permission where cs_nick=#{csNick}  
  </delete>
  <delete id="truncatePermission">
    delete from pes_user_menu_permission where cs_nick in
    <foreach collection="shopAccounts" item="acc" separator="," close=")" open="(">
      #{acc.nick}
    </foreach>
    delete from pes_user_service_permission where cs_nick in
    <foreach collection="shopAccounts" item="acc" separator="," close=")" open="(">
      #{acc.nick}
    </foreach>
  </delete>
    <delete id="deleteByShopAccountsAndMenus">
        <foreach collection="shopAccounts" item="shopAccount" separator=";" open="" close=";">
            delete from pes_user_menu_permission where menu_resource_id in
            <foreach collection="menus" item="menu" separator="," close=")" open="(">
                #{menu.id,jdbcType=BIGINT}
            </foreach>
            and cs_nick= #{shopAccount.nick,jdbcType=VARCHAR}
        </foreach>
    </delete>
  <delete id="delete">
     delete from  pes_user_menu_permission where menu_resource_id =#{record.menuResourceId,jdbcType=BIGINT}
        and cs_nick= #{record.csNick,jdbcType=VARCHAR}
  </delete>
</mapper>