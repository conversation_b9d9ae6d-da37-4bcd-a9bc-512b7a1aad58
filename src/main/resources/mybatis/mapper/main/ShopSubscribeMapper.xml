<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSubscribeMapper" >
  <resultMap id="ShopSubScribeDTO" type="com.pes.jd.model.DTO.ShopSubScribeDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="order_cycle" property="orderCycle" jdbcType="INTEGER" />
    <result column="shopName" property="shopName" jdbcType="VARCHAR" />
  </resultMap>
  
   <resultMap id="ShopSubScribe" type="com.pes.jd.ms.domain.Data.master.ShopSubScribe" >
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="order_cycle" property="orderCycle" jdbcType="INTEGER" />
    <result column="title" property="shopName" jdbcType="VARCHAR" />
    <result column="buyer" property="buyer" jdbcType="VARCHAR" />
    <result column="nick_name" property="nickName" jdbcType="VARCHAR" />
    <result column="item_code" property="itemCode" jdbcType="VARCHAR" />
    <result column="service_name" property="serviceName" jdbcType="VARCHAR" />
    <result column="order_status" property="orderStatus" jdbcType="INTEGER" />
     <result column="shop_status" property="shopStatus" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="base_filed" >
    id, shop_id, start_time, end_time, status, version, order_cycle,buyer,nick_name,service_name,order_status
  </sql>
 
 <!-- 运营报表订购明细 -->
 <select id="selectShopSubscribeByShopIdLstByDate" resultMap="ShopSubScribeDTO">
 	SELECT 
 		shop_id, 
 		start_time, 
 		end_time, 
 		status,
 		version, 
 		order_cycle
 	FROM pes_shop_subcribe 
 	<where>
 		<if test="shopIdLst!=null and shopIdLst.size>0">
 			shop_id in
 			<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
 				#{shopId}
 			</foreach>
 		</if>
 		<if test="version=='FW_GOODS-908622-1'">
		<!--不限账号 -->
			and item_code = 'FW_GOODS-908622-1'
 		</if>
 		<if test="version=='FW_GOODS-908622-2'">
		<!--3个账号-->
			and item_code = 'FW_GOODS-908622-2'
 		</if>
		<if test="version=='FW_GOODS-908622-3'">
			<!--3个账号-->
			and item_code = 'FW_GOODS-908622-3'
		</if>
		<if test="version=='FW_GOODS-908622-4'">
			<!--3个账号-->
			and item_code = 'FW_GOODS-908622-4'
		</if>
		<if test="version=='FW_GOODS-908622-5'">
			<!--自营不限账号-->
			and item_code = 'FW_GOODS-908622-5'
		</if>
 		<if test="status!=null and status!=''">
 			and status = #{status}
 		</if>
 		<if test="orderCycle!=null and orderCycle!=''">
 			and order_cycle = #{orderCycle}
 		</if>
 		and order_status = 04
 		and start_time between #{startDate} and #{endDate}
 	</where>
 </select>
 	
 	<!-- 实时看板订购明细（取时间最新的一条数据） -->
 <select id="selectShopSubscribeByShopIdLstByDateForRt" resultMap="ShopSubScribeDTO">
 	<!--SELECT
 		shop_id, 
 		MAX(start_time) start_time , 
 		end_time, 
 		status,
 		version, 
 		order_cycle
 	FROM pes_shop_subcribe
 	<where>
 		<if test="shopIdLst!=null and shopIdLst.size>0">
 			shop_id in
 			<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
 				#{shopId}
 			</foreach>
 		</if>
 		
 		and order_status = 04
 		and start_time between #{startDate} and #{endDate}
 	</where>
 	 group by shop_id
 	 having 1=1
 	 	<if test="status!=null and status!=''">
 			and status = #{status}
 		</if>
 		<if test="orderCycle!=null and orderCycle!=''">
 			and order_cycle = #{orderCycle}
 		</if> -->

SELECT
	 pss.shop_id shop_id,
	 MAX(pss.start_time)  start_time,
	 pss.end_time end_time,
	 pss.status status,
	 pss.version version,
	 pss.order_cycle order_cycle

	 FROM pes_shop_subcribe pss inner join pes_shop  ps ON  pss.shop_id=ps.shop_id
	 <where>
		 <if test="shopIdLst!=null and shopIdLst.size>0">
			 pss.shop_id in
			 <foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
				 #{shopId}
			 </foreach>
		 </if>

		 and ps.status='active'
		 and pss.start_time between #{startDate} and #{endDate}
		 and (ps.title LIKE CONCAT('%', #{shopName},'%') or pss.shop_id LIKE CONCAT('%', #{shopName},'%'))
	 </where>
	 group by pss.shop_id
	 having 1=1
	 <if test="status!=null and status!=''">
		 and pss.status = #{status}
	 </if>
	 <if test="orderCycle!=null and orderCycle!=''">
		 and pss.order_cycle = #{orderCycle}
	 </if>
 </select>
 	
 	<!-- 订购详情 -->
 	<select id="selectShopSubScribeForUseAnalysis" resultMap="ShopSubScribe" >
 		SELECT 
	 		pss.shop_id, 
	 		pss.start_time, 
	 		pss.end_time, 
	 		pss.status, 
	 		pss.version, 
	 		pss.order_cycle,
	 		pss.buyer,
	 		pss.nick_name,
	 		pss.service_name,
	 		pss.order_status,
	 		pss.item_code
	 		
	 	FROM 
	 		pes_shop_subcribe pss
	 		<where>
	 			pss.shop_id in
	 			<foreach collection="param.shopIdSet" item="shopId" open="(" close=")" separator=",">
	 				#{shopId}
	 			</foreach>
	 		</where>
	 		order by start_time desc
 	</select>
 	
 	<select id="selectShopValidSubScribeForUseAnalysis" resultMap="ShopSubScribe" >
 		SELECT 
	 		pss.shop_id, 
	 		pss.start_time, 
	 		pss.end_time, 
	 		pss.status, 
	 		pss.version, 
	 		pss.order_cycle,
	 		pss.buyer,
	 		pss.nick_name,
	 		pss.service_name,
	 		pss.order_status,
	 		pss.item_code
	 		
	 	FROM 
	 		pes_shop_subcribe pss
	 		<where>
	 			pss.shop_id in
	 			<foreach collection="param.shopIdSet" item="shopId" open="(" close=")" separator=",">
	 				#{shopId}
	 			</foreach>
	 			AND order_status=4
	 		</where>
	 		order by start_time desc
 	</select>

	<select id="selectShopSubScribeByShopId" resultMap="ShopSubScribe" >
		SELECT
		pss.shop_id,
		pss.start_time,
		pss.end_time,
		pss.status,
		pss.version,
		pss.order_cycle,
		pss.buyer,
		pss.nick_name,
		pss.service_name,
		pss.order_status,
		pss.item_code

		FROM
		pes_shop_subcribe pss
		<where>
			pss.shop_id =#{shopId}
		</where>

	</select>


	<!-- 根据商铺ID查询商铺订购信息 -->
	<select id="getShopSubscribeInfoByShopId" resultMap="ShopSubScribeDTO">
		SELECT
		shop_id,
		start_time,
		end_time,
		status,
		version,
		order_cycle
		FROM pes_shop_subcribe
		WHERE shop_id = #{shopId}
		AND order_status = 04
		ORDER BY start_time DESC
	</select>

	<select id="selectValidShopSubScribeByShopIdOrderByStart" resultType="com.pes.jd.ms.domain.Data.master.ShopSubScribe">
		SELECT
		pss.shop_id,
		pss.start_time,
		pss.end_time,
		pss.status,
		pss.version,
		pss.order_cycle,
		pss.buyer,
		pss.nick_name,
		pss.service_name,
		pss.order_status,
		pss.item_code
		FROM
		pes_shop_subcribe pss
		where pss.shop_id =#{shopId} and order_status=4
		and pss.item_code in
		<foreach collection="versions" item="item" open="(" separator="," close=")">
			#{item, jdbcType=VARCHAR}
		</foreach>
		order by start_time;

	</select>

	<select id="selectShopSubscribeByDateScope" resultType="com.pes.jd.ms.domain.Data.master.ShopSubScribe">
		SELECT
			shop_id,
			start_time,
			end_time,
			status,
			version,
			item_code,
			order_cycle
		FROM pes_shop_subcribe
		WHERE
		  start_time between #{startTime, jdbcType=TIMESTAMP} and #{endTime, jdbcType=TIMESTAMP}
		  AND order_status = 4
		  AND shop_id in
		<foreach collection="shopIds" item="item" open="(" separator="," close=")">
			#{item, jdbcType=BIGINT}
		</foreach>
		ORDER BY start_time DESC
	</select>
</mapper>