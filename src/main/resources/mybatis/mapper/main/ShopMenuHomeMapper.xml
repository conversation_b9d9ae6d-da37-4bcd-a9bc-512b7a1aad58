<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopMenuHomeMapper">

    <select id="queryMenuByShopId" resultType="com.pes.jd.model.DTO.ShopMenuHomeDTO">
      SELECT shop_id, menu_id, `name`, title, `type`
      FROM pes_shop_menu_home
      WHERE shop_id = #{shopId}
      AND `type` = #{type}
      ORDER BY create_time ASC
    </select>

    <insert id="insertShopMenuHome">
        INSERT INTO pes_shop_menu_home (shop_id, menu_id, `name`, title,
        create_time, `type`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.shopId,jdbcType=BIGINT}, #{item.menuId,jdbcType=BIGINT},
            #{item.name,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.type,jdbcType=INTEGER})
        </foreach>
    </insert>

    <delete id="deleteShopMenuHome">
        DELETE FROM pes_shop_menu_home WHERE shop_id = #{shopId} AND `type` = #{type}
    </delete>
</mapper>