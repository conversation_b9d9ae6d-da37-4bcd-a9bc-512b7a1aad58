<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.SaleIndexSettingMapper">
  <resultMap id="SaleIndexSettingDO" type="com.pes.jd.model.DO.SaleIndexSettingDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
     <result column="shop_sale_target" jdbcType="DOUBLE" property="shopSaleTarget" />
    <result column="cs_sale_target" jdbcType="DOUBLE" property="csSaleTarget" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="created_by" jdbcType="VARCHAR" property="createBy" />
  </resultMap>
 
 <sql id="base_field">
 	id,shop_id,date,shop_sale_target,cs_sale_target,created,created_by
 </sql>
 
 	<insert id="insertSaleIndexSetting" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.pes.jd.model.DO.SaleIndexSettingDO">
 		insert into pes_sale_index_setting(id,shop_id,date,shop_sale_target,cs_sale_target,created,created_by)
 		values(#{id},#{shopId},
 		#{date},#{shopSaleTarget},
 		#{csSaleTarget},#{created},
 		#{createBy})
 	</insert>
 
 
  <select id="selectSaleIndexSettingByShop"  resultMap="SaleIndexSettingDO">
		SELECT 
		    <include refid="base_field"/>
		FROM pes_jd.pes_sale_index_setting
		<where>
			created = 
			(select MAX(created) from pes_jd.pes_sale_index_setting where shop_id = #{shopId})
			limit 1;
		</where>  
	</select>
</mapper>