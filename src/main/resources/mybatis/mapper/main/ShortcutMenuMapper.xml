<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShortcutMenuMapper">

  <resultMap id="ShortcutMenuDO" type="com.pes.jd.model.DO.ShortcutMenu">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
  </resultMap>
  
  <sql id="base_field">
    	user_id, resource_id
  </sql>
  
  <select id="getShortcutMenuById" parameterType="java.lang.Long" resultMap="ShortcutMenuDO">
    SELECT 
    <include refid="base_field" />
    FROM pes_shortcut_menu
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteShortcutMenuByUserId" parameterType="string">
    DELETE FROM pes_shortcut_menu
    where user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  
  <insert id="insertShortcutMenu" parameterType="com.pes.jd.model.DO.ShortcutMenu">
    INSERT INTO pes_shortcut_menu (id, user_id, resource_id)
    VALUES 
    (
    	#{id,jdbcType=BIGINT}, #{userId,jdbcType=VARCHAR}, #{resourceId,jdbcType=BIGINT}
    )
  </insert>
  
  <update id="updateShortcutMenuById" parameterType="com.pes.jd.model.DO.ShortcutMenu">
    UPDATE pes_shortcut_menu
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
    </set>
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </update>
</mapper>