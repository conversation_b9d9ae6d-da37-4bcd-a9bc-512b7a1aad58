<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopGroupMemberMapper">
	
	<resultMap type="com.pes.jd.model.DO.ShopGroupMemberDO" id="ShopGroupMemberDO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId"/>
		<id column="shop_group_id" jdbcType="BIGINT" property="shopGroupId"/>
		<id column="member_shop_id" jdbcType="BIGINT" property="memberShopId"/>
	    <result column="is_delete" jdbcType="BIT" property="isDelete" />
	</resultMap>
	
	<insert id="insertShopGroupMember" parameterType="com.pes.jd.model.DTO.ShopGroupMemberDTO">
		INSERT INTO pes_shop_group_member (shop_id,shop_group_id,member_shop_id)
		VALUES (#{shopId},#{shopGroupId},#{memberShopId});
	</insert>
	
	
	<update id="deleteShopGroupMember" parameterType="map">
		UPDATE pes_shop_group_member
		SET is_delete = 1
		WHERE shop_id = #{shopId}
		AND shop_group_id = #{shopGroupId}
		AND member_shop_id = #{memberShopId}
	</update>
	
	<select id="selectJoinedGroupShopList" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDTO">
		SELECT s.shop_id shopId,s.seller_nick sellerNick,s.title title
		FROM pes_shop_group_member m
		INNER JOIN pes_shop_group_request psr
			ON m.shop_id = psr.from_id
			AND m.member_shop_id = psr.to_id
		LEFT JOIN  pes_shop s
			ON m.member_shop_id = s.shop_id
		WHERE m.shop_id = #{shopId}
			AND m.shop_group_id = #{shopGroupId}
			AND m.is_delete = 0
			AND (psr.status = #{status1} OR psr.status = #{status2} )
	</select>

	<update id="updateShopToShopGroup" parameterType="map">
		UPDATE pes_shop_group_member
		SET shop_group_id = #{shopGroupId}
		WHERE
		shop_id = #{shopId}
		AND member_shop_id IN
		<foreach collection="shopIdList" item="shopId" open="(" separator="," close=")">
			#{shopId}
		</foreach>
		AND is_delete = 0
	</update>

	<update id="updateGroupShopStatus" parameterType="com.pes.jd.model.DTO.ShopGroupMemberDTO">
		UPDATE pes_shop_group_member
		SET is_delete = 0
		WHERE
		shop_id = #{shopId}
		AND shop_group_id = #{shopGroupId}
		AND member_shop_id = #{memberShopId}
	</update>
	<!-- 查询自己组下的所有成员店铺 -->
	<select id="selectShopGroupMemberShops" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDTO">
	(SELECT
	s.id id,
	s.shop_id shopId,
	s.schema_id schemaId ,
	s.db db,
	s.rt_schema_id rtSchemaId ,
	s.rt_db rtDb,
	s.title title,
	s.subuser_num subuserNum,
	s.pre_fetch_realtime preFetchRealtime,
	s.session_key sessionKey,
	s.col_type colType,
	s.option_session_key optionSessionKey
	FROM pes_shop_group_member m
	INNER JOIN pes_shop s
	ON m.member_shop_id = s.shop_id
	WHERE
		m.shop_id = #{shopId}
	AND s.type= #{type}
	AND is_delete = 0
	GROUP BY m.member_shop_id
	ORDER BY s.title)
</select>
	<!-- 查询自己所属店铺组创建店铺 -->
	<select id="selectShopGroupMemberShopsStartMutualWatchwithParent" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDTO">
	SELECT
	DISTINCT
	s.id id,
	s.shop_id shopId,
	s.schema_id schemaId ,
	s.db db,
	s.rt_schema_id rtSchemaId ,
	s.rt_db rtDb,
	s.title title,
	s.subuser_num subuserNum,
	s.pre_fetch_realtime preFetchRealtime,
	s.session_key sessionKey,
	s.col_type colType,
	s.option_session_key optionSessionKey
	FROM
	pes_shop s ,pes_shop_group g,pes_shop_group_member m
	WHERE
	s.shop_id =g.shop_id AND g.group_id=m.shop_group_id
	AND m.member_shop_id=#{shopId} AND g.mutual_watch=1 AND m.is_delete=0
	AND s.type=#{type}

	</select>

	<!-- 查询所属店铺组的成员店铺 -->
	<select id="selectShopGroupMemberShopsStartMutualWatch" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDTO">
	SELECT
	s.id id,
	s.shop_id shopId,
	s.schema_id schemaId ,
	s.db db,
	s.rt_schema_id rtSchemaId ,
	s.rt_db rtDb,
	s.title title,
	s.subuser_num subuserNum,
	s.pre_fetch_realtime preFetchRealtime,
	s.session_key sessionKey,
	s.col_type colType,
	s.option_session_key optionSessionKey
	FROM pes_shop_group_member m,
		pes_shop s ,pes_shop_group g
	WHERE
		m.member_shop_id = s.shop_id
	AND g.group_id=m.shop_group_id
	AND s.type=#{type}

 	AND m.shop_id in
 	<foreach collection="shopIdLst" open="(" close=")" item="shopId" separator=",">
		#{shopId}
	</foreach>
	AND is_delete = 0 AND g.mutual_watch=1
	-- GROUP BY m.member_shop_id
	-- ORDER BY s.title
	</select>



	<select id="getMainShopMemberShop" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDTO">
	SELECT
	s.shop_id shopId,
	s.user_id userId,
	s.seller_nick sellerNick,
	s.title title,
	s.session_key sessionKey,
	s.status status,
	s.subuser_num subuserNum,
	s.schema_id schemaId,
	s.col_type colType,
	s.vender_id venderId,
	s.previous_get_data_time previousGetDataTime,
	s.pre_fetch_realtime preFetchRealtime,
	s.fetch_flag fetchFlag,
	s.init_data_flag initDataFlag
	FROM pes_shop_group_member m
	LEFT JOIN pes_shop s
	ON m.member_shop_id = s.shop_id
	WHERE
	m.member_shop_id= #{memberShopId}
	AND m.shop_id = #{mainShopId}
	AND m.is_delete = 0
	LIMIT 1
	</select>

	<select id="getShopsCanBeSwitched" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDTO">
		(SELECT
		s.shop_id shopId,
		s.option_session_key optionSessionKey,
		s.user_id userId,
		s.seller_nick sellerNick,
		s.title title,
		s.session_key sessionKey,
		s.status status,
		s.subuser_num subuserNum,
		s.schema_id schemaId,
		s.col_type colType,
		s.vender_id venderId,
		s.previous_get_data_time previousGetDataTime,
		s.pre_fetch_realtime preFetchRealtime,
		s.fetch_flag fetchFlag,
		s.init_data_flag initDataFlag
		FROM pes_shop_group_member m
		INNER JOIN pes_shop s
		ON m.member_shop_id = s.shop_id
		WHERE
		m.shop_id = #{shopId}
		AND is_delete = 0
		GROUP BY m.member_shop_id
		ORDER BY s.title)
		UNION
		<!-- 查询所有他加入的多店铺分组并开启了组内成员可见的 -->
		(SELECT
		s.shop_id shopId,
		s.option_session_key optionSessionKey,
		s.user_id userId,
		s.seller_nick sellerNick,
		s.title title,
		s.session_key sessionKey,
		s.status status,
		s.subuser_num subuserNum,
		s.schema_id schemaId,
		s.col_type colType,
		s.vender_id venderId,
		s.previous_get_data_time previousGetDataTime,
		s.pre_fetch_realtime preFetchRealtime,
		s.fetch_flag fetchFlag,
		s.init_data_flag initDataFlag
		FROM pes_shop_group_member m,
		pes_shop s ,pes_shop_group g,
		( SELECT tp.shop_group_id FROM pes_shop_group_member tp	WHERE tp.member_shop_id=#{shopId}) tpt
		WHERE
		m.member_shop_id = s.shop_id AND g.group_id=m.shop_group_id
		AND g.group_id in ( tpt.shop_group_id )
		AND is_delete = 0 AND g.mutual_watch=1
		GROUP BY m.member_shop_id
		ORDER BY s.title)
		UNION
		<!-- 查询开启组内互看的店铺 -->
		(SELECT
		DISTINCT s.shop_id shopId,
		s.option_session_key optionSessionKey,
		s.user_id userId,
		s.seller_nick sellerNick,
		s.title title,
		s.session_key sessionKey,
		s.status status,
		s.subuser_num subuserNum,
		s.schema_id schemaId,
		s.col_type colType,
		s.vender_id venderId,
		s.previous_get_data_time previousGetDataTime,
		s.pre_fetch_realtime preFetchRealtime,
		s.fetch_flag fetchFlag,
		s.init_data_flag initDataFlag
		FROM
		pes_shop s ,pes_shop_group g,pes_shop_group_member m,( SELECT tp.shop_id FROM pes_shop_group_member tp	WHERE tp.member_shop_id=#{shopId}) tpt
		WHERE
		s.shop_id =g.shop_id
		AND is_delete = 0 AND g.mutual_watch=1
		AND s.shop_id in (tpt.shop_id))
	</select>
	
	<select id="selectIsExistGroupShop" parameterType="com.pes.jd.model.DTO.ShopGroupMemberDTO" resultType="com.pes.jd.model.DTO.ShopGroupMemberDTO">
		SELECT * 
		FROM pes_shop_group_member
		WHERE 
		shop_id = #{shopId}
		AND shop_group_id = #{shopGroupId}
		AND member_shop_id = #{memberShopId}
	</select>
</mapper>