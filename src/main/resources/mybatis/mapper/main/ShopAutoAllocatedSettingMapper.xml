<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopAutoAllocatedSettingMapper" >
  <resultMap id="ShopAutoAllocatedSettingDO" type="com.pes.jd.model.DO.ShopAutoAllocatedSettingDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modify" property="modify" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="is_auto_allocated" property="isAutoAllocated" jdbcType="BIT" />
    <result column="cno_flag" property="cnoFlag" jdbcType="INTEGER" />
    <result column="cno_group_id" property="cnoGroupId" jdbcType="BIGINT" />
    <result column="cno_cs_nick" property="cnoCsNick" jdbcType="VARCHAR" />
    <result column="onp_flag" property="onpFlag" jdbcType="INTEGER" />
    <result column="onp_group_id" property="onpGroupId" jdbcType="BIGINT" />
    <result column="onp_cs_nick" property="onpCsNick" jdbcType="VARCHAR" />
    <result column="snp_flag" property="snpFlag" jdbcType="INTEGER" />
    <result column="snp_group_id" property="snpGroupId" jdbcType="BIGINT" />
    <result column="snp_cs_nick" property="snpCsNick" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ShopAutoAllocatedSettingDTO" type="com.pes.jd.ms.domain.Data.master.ShopAutoAllocatedSettingDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modify" property="modify" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="is_auto_allocated" property="isAutoAllocated" jdbcType="BIT" />
    <result column="cno_flag" property="cnoFlag" jdbcType="INTEGER" />
    <result column="cno_group_id" property="cnoGroupId" jdbcType="BIGINT" />
    <result column="cno_cs_nick" property="cnoCsNick" jdbcType="VARCHAR" />
    <result column="onp_flag" property="onpFlag" jdbcType="INTEGER" />
    <result column="onp_group_id" property="onpGroupId" jdbcType="BIGINT" />
    <result column="onp_cs_nick" property="onpCsNick" jdbcType="VARCHAR" />
    <result column="snp_flag" property="snpFlag" jdbcType="INTEGER" />
    <result column="snp_group_id" property="snpGroupId" jdbcType="BIGINT" />
    <result column="snp_cs_nick" property="snpCsNick" jdbcType="VARCHAR" />
    <result column="cno_spare_group_id" property="cnoSpareGroupId" jdbcType="BIGINT" />
    <result column="cno_spare_cs_nick" property="cnoSpareCsNick" jdbcType="VARCHAR" />
    <result column="onp_spare_group_id" property="onpSpareGroupId" jdbcType="BIGINT" />
    <result column="onp_spare_cs_nick" property="onpSpareCsNick" jdbcType="VARCHAR" />
    <result column="snp_spare_group_id" property="snpSpareGroupId" jdbcType="BIGINT" />
    <result column="snp_spare_cs_nick" property="snpSpareCsNick" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="base_filed" >
    id, shop_id, created, modify, status, is_auto_allocated, cno_flag, cno_group_id, 
    cno_cs_nick, onp_flag, onp_group_id, onp_cs_nick, snp_flag, snp_group_id, snp_cs_nick,
    cno_spare_cs_nick,cno_spare_group_id,onp_spare_cs_nick,onp_spare_group_id,snp_spare_cs_nick,
    snp_spare_group_id
  </sql>
  <select id="selectShopAutoAllocatedSetting" resultMap="ShopAutoAllocatedSettingDO" parameterType="java.lang.Long" >
  select
  <include refid="base_filed" />
  from pes_shop_auto_allocated_setting
  where id = #{id,jdbcType=BIGINT}
</select>
  <select id="selectShopAutoAllocatedSettingByShopId" resultMap="ShopAutoAllocatedSettingDTO" >
    select
    <include refid="base_filed" />
    from pes_shop_auto_allocated_setting
    where shop_id=#{shopId}
  </select>
  <delete id="deleteShopAutoAllocatedSetting" parameterType="java.lang.Long" >
    delete from pes_shop_auto_allocated_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertShopAutoAllocatedSetting" parameterType="com.pes.jd.model.DO.ShopAutoAllocatedSettingDO" >
    insert into pes_shop_auto_allocated_setting (shop_id, created,
      modify, status, is_auto_allocated, 
      cno_flag, cno_group_id, cno_cs_nick, 
      onp_flag, onp_group_id, onp_cs_nick, 
      snp_flag, snp_group_id, snp_cs_nick,
      cno_spare_cs_nick,cno_spare_group_id,
      onp_spare_cs_nick,onp_spare_group_id,
      snp_spare_cs_nick,snp_spare_group_id
      )
    values ( #{shopId,jdbcType=BIGINT}, #{created,jdbcType=TIMESTAMP},
      #{modify,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{isAutoAllocated,jdbcType=BIT},
      #{cnoFlag,jdbcType=INTEGER}, #{cnoGroupId,jdbcType=BIGINT}, #{cnoCsNick,jdbcType=VARCHAR},
      #{onpFlag,jdbcType=INTEGER}, #{onpGroupId,jdbcType=BIGINT}, #{onpCsNick,jdbcType=VARCHAR},
      #{snpFlag,jdbcType=INTEGER}, #{snpGroupId,jdbcType=BIGINT}, #{snpCsNick,jdbcType=VARCHAR},
      #{cnoSpareCsNick,jdbcType=VARCHAR},#{cnoSpareGroupId,jdbcType=BIGINT},
      #{onpSpareCsNick,jdbcType=VARCHAR},#{onpSpareGroupId,jdbcType=BIGINT},
      #{snpSpareCsNick,jdbcType=VARCHAR},#{snpSpareGroupId,jdbcType=BIGINT}
      )
  </insert>
  <update id="updateShopAutoAllocatedSettingById" parameterType="com.pes.jd.model.DO.ShopAutoAllocatedSettingDO" >
    update pes_shop_auto_allocated_setting
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modify != null" >
        modify = #{modify,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="isAutoAllocated != null" >
        is_auto_allocated = #{isAutoAllocated,jdbcType=BIT},
      </if>

        cno_flag = #{cnoFlag,jdbcType=INTEGER},

        cno_group_id = #{cnoGroupId,jdbcType=BIGINT},

        cno_cs_nick = #{cnoCsNick,jdbcType=VARCHAR},

        onp_flag = #{onpFlag,jdbcType=INTEGER},

        onp_group_id = #{onpGroupId,jdbcType=BIGINT},

        onp_cs_nick = #{onpCsNick,jdbcType=VARCHAR},

        snp_flag = #{snpFlag,jdbcType=INTEGER},

        snp_group_id = #{snpGroupId,jdbcType=BIGINT},

        snp_cs_nick = #{snpCsNick,jdbcType=VARCHAR},

        cno_spare_cs_nick = #{cnoSpareCsNick,jdbcType=VARCHAR},

        cno_spare_group_id = #{cnoSpareGroupId,jdbcType=BIGINT},

        onp_spare_cs_nick = #{onpSpareCsNick,jdbcType=VARCHAR},

        onp_spare_group_id = #{onpSpareGroupId,jdbcType=BIGINT},

        snp_spare_cs_nick = #{snpSpareCsNick,jdbcType=VARCHAR},

        snp_spare_group_id = #{snpSpareGroupId,jdbcType=BIGINT}

    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateCloseAutoAllocatedByShopId" >
    update pes_shop_auto_allocated_setting set  is_auto_allocated =#{isAutoAllocated} where shop_id=#{shopId}

  </update>
</mapper>