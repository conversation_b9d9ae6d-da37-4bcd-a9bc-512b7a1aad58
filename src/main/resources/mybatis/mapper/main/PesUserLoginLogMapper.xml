<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesUserLoginLogMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesUserLoginLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
  </resultMap>
  <resultMap id="PesUserLoginLogVo" type="com.pes.jd.model.VO.PesUserLoginLogVo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="loginCount" jdbcType="INTEGER" property="loginCount" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
  </resultMap>
    <resultMap id="UserLoginLogDTO" type="com.pes.jd.model.DTO.UserLoginLogDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, nick, shop_id, login_time
  </sql>
  <!-- 查询日志 ( 根据时间段和客服昵称 ) BEGIN -->
  <select id="searchLoginLogByTimeNick" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from  pes_user_login_log
    <where>
      login_time between #{startDate} and #{endDate}
      <if test=" nick != null and nick != '' ">
        and nick =#{nick}
      </if>
      and shop_id = #{shopId}
    </where>
  </select>
  <!-- 查询日志 ( 根据时间段和客服昵称 ) END -->
  <!-- 查询日志登录次数 ( 根据时间段和客服昵称 ) BEGIN -->
  <select id="searchLoginCountLogByTimeNick" resultMap="PesUserLoginLogVo">
    select
    <include refid="Base_Column_List" />,count(1) loginCount
    from  pes_user_login_log
    <where>
      login_time between #{startDate} and #{endDate}
      <if test=" nick != null and nick != '' ">
        and nick LIKE concat('%',#{nick},'%')
      </if>
      and shop_id = #{shopId}
    </where>
    group  by nick
  </select>

  <!-- 查询日志登录次数 ( 根据时间段和客服昵称 ) END -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_user_login_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_user_login_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.PesUserLoginLog">
    insert into pes_user_login_log (id, user_id, nick, 
      shop_id, login_time)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR}, 
      #{shopId,jdbcType=BIGINT}, #{loginTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.PesUserLoginLog">
    insert into pes_user_login_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="nick != null">
        nick,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="loginTime != null">
        login_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="nick != null">
        #{nick,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="loginTime != null">
        #{loginTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.PesUserLoginLog">
    update pes_user_login_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="nick != null">
        nick = #{nick,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="loginTime != null">
        login_time = #{loginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.PesUserLoginLog">
    update pes_user_login_log
    set user_id = #{userId,jdbcType=BIGINT},
      nick = #{nick,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      login_time = #{loginTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectShopLoginForUserAnalysis" resultType="com.pes.jd.ms.domain.Data.master.ShopLogin">
  	SELECT 
  	shop_id,
  	date_format(login_time,'%Y-%m-%d') date,
  	count(DISTINCT shop_id) num 
  	FROM pes_user_login_log
  	<where>
  			 shop_id in
	  	<foreach collection="param.shopIdSet" item="shopId" open="(" close=")" separator=",">
	  		#{shopId}
	  	</foreach>
	  	AND login_time BETWEEN #{param.startDate} AND #{param.endDate}
	  	group by shop_id,date
  	</where>
  </select>
  
  <select id="selectCsLoginDetailForShopUserAnalysis" resultMap="UserLoginLogDTO">
  	SELECT 
  	id, nick,  
  	date_format(login_time,'%Y-%m-%d') date,
  	shop_id
  	FROM pes_user_login_log
  	<where>
  		shop_id in
  		<foreach collection="shopIdSet" item="shopId" open="(" close=")" separator=",">
  			#{shopId}
  		</foreach>
  			
  		    AND login_time BETWEEN #{startDate} AND #{endDate}
     
  	</where>
  </select>


  <select id="selectShopLoginInfoByShopId" resultType="com.pes.jd.ms.domain.Data.job.ShopLoginInfo">
    SELECT
    shop_id,
    date_format(login_time,'%Y-%m-%d') date,
    count(DISTINCT shop_id) num
    FROM pes_user_login_log
    <where>
      shop_id =#{shopId}
      AND login_time BETWEEN #{startDate} AND #{endDate}
    </where>
  </select>

  <!-- 查询登录日志 ( 根据时间段和客服昵称 ) BEGIN -->
  <select id="getUserLoginCountByNickAndShopAndTime" resultType="int">
    select
    count(1)
    from  pes_user_login_log
    <where>
      login_time between #{startDate} and #{endDate}
      <if test=" nick != null and nick != '' ">
        and nick = #{nick}
      </if>
      and shop_id = #{shopId}
    </where>
  </select>
  <!-- 查询日志 ( 根据时间段和客服昵称 ) END -->

  <select id="listAllLoginCount" resultType="java.util.HashMap">
    SELECT
        COUNT(*) AS loginCount,
        shop_id shopId
    FROM
        pes_user_login_log
    GROUP BY
        shop_id
  </select>
</mapper>