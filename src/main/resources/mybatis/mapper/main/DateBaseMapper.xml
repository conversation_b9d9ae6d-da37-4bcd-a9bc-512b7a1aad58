<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.DateBaseMapper" >
 <update id="createPesCsBuyerServiceIndex" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`date` date NOT NULL COMMENT '等待时长（统计）日期',
	`cs_nick` varchar(45) NOT NULL COMMENT '客服昵称',
	`buyer_nick` varchar(45) NOT NULL COMMENT '客户昵称',
	`avg_wait_time_first` double(20,3) DEFAULT '0.000' COMMENT '首次等待时长（秒）',
	`avg_wait_time` double(20,3) DEFAULT '0.000' COMMENT '平均等待时长（秒）',
	`session_time` bigint(20) DEFAULT '0' COMMENT '会话时长（秒）',
	`cs_word_num` int(11) DEFAULT NULL COMMENT '客服字数',
	`cs_reply_num` int(11) DEFAULT '0' COMMENT '客服回复数',
	`buyer_chat_num` int(11) DEFAULT '0' COMMENT '买家咨询数',
	`is_non_reply` bit(1) DEFAULT b'0' COMMENT '是都是未回复',
	`first_receive_date` datetime DEFAULT NULL COMMENT '客服 接待的开始时间',
	`last_receive_date` datetime DEFAULT NULL COMMENT '客服接待的结束时间',
	PRIMARY KEY (`id`),
	KEY `idx_buyer_cs_date` (`buyer_nick`,`cs_nick`,`date`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
	COMMENT='买家服务指标'
    </update>
  <update id="createPesCsChatlogTable" parameterType="string">
  	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`cs_nick` varchar(45) NOT NULL COMMENT '卖家id - 客服',
	`buyer` varchar(45) NOT NULL COMMENT '买家id',
	`time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '聊天时间点',
	`content` varchar(5000) DEFAULT '',
	`direction` tinyint(1) DEFAULT '-1' COMMENT '表示消息方向 0:from_id->to_id
	1:to_id->from_id',
	`length` int(11) DEFAULT '0',
	`type` tinyint(1) DEFAULT '0' COMMENT
	'消息通道。区分消息是顾客还是客服发送。\n11:客户发送的普通消息\n12:客户接收到的转接消息\n13:客户发送的留言消息\n14:app-sdk客户普通消息
	- 在线咨询\n15:app-sdk客户普通消息 - 离线咨询\n16:app-sdk客户普通消息 -
	语音消息\n21:客服发送的普通消息\n22:客服发送的自动回复\n23:客服快捷回复\n24:发给发起转接客服的消息\n25:发给被转接客服的消息\n26:客服发送的留言消息\n27:
	客服的欢迎语\n28:客服发送的离线消息\n29:客服之间对话\n31:客服邀评信息 \n',
	PRIMARY KEY (`id`),
	KEY `idx_cs_buyer` (`cs_nick`,`buyer`) USING BTREE,
	KEY `idx_time` (`time`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8
	ROW_FORMAT=COMPACT COMMENT='聊天记录'
    </update>
    
	<update id="createPesCsChatpeerTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`cs_nick` varchar(45) NOT NULL COMMENT '聊天用户ID',
	`buyer_nick` varchar(45) NOT NULL COMMENT '聊天对象用户nick',
	`date` date NOT NULL COMMENT '聊天日期',
	`chat_flag` tinyint(2) DEFAULT '0' COMMENT '0:正常咨询 1:空聊天 2:团队内部过滤 3: 绩效系统客服过滤 ',
	`forward_flag` tinyint(2) DEFAULT '0' COMMENT '转入：1,\n转出：2 ，\n非转发：0',
	`is_watchword_buyer` bit(1) DEFAULT b'0' COMMENT '暗语过滤',
	`is_filtered_buyer` bit(1) DEFAULT b'0' COMMENT '过滤的买家',
	`is_cs_single_chat_filter` bit(1) DEFAULT b'0' COMMENT '客服单口相声过滤 ',
	`is_consult` bit(1) DEFAULT b'0' COMMENT '是否是 咨询客户',
	`is_receive` bit(1) DEFAULT b'0' COMMENT '是否是 接待的客户',
	`is_enquiry` bit(1) DEFAULT b'0' COMMENT '是否是 询单的客户',
	`is_cs_consult_first` bit(1) DEFAULT b'0' COMMENT '客服主动找客户聊天',
	`is_pes` bit(1) DEFAULT b'0' COMMENT '绩效买家',
	`is_team_pes` bit(1) DEFAULT b'0',
	`is_next_day_pes` bit(1) DEFAULT b'0' COMMENT '第二天的绩效',
	`is_assist` bit(1) DEFAULT b'0' COMMENT '协助服务',
	`is_after_sale` bit(1) DEFAULT b'0' COMMENT '售后咨询客户',
	`is_order_created` bit(1) DEFAULT b'0' COMMENT '买家有下单',
	`buyer_chat_num` int(11) DEFAULT '0',
	`first_chat_date` datetime DEFAULT NULL,
	`last_chat_date` datetime DEFAULT NULL COMMENT '最晚接待时间',
	PRIMARY KEY (`id`),
	KEY `idx_buyer_cs_date` (`buyer_nick`,`cs_nick`,`date`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8
	ROW_FORMAT=COMPACT COMMENT='聊天关系'
    </update>
    
    <update id="createPesCsConversionTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`cs_nick` varchar(45) DEFAULT NULL,
	`buyer_nick` varchar(45) NOT NULL,
	`order_id` bigint(20) DEFAULT NULL,
	`allocated_cs_nick` varchar(45) DEFAULT NULL,
	`created` datetime DEFAULT NULL,
	`modified` datetime DEFAULT NULL,
	`type` varchar(50) DEFAULT NULL,
	`status` tinyint(1) DEFAULT '0',
	PRIMARY KEY (`id`),
	KEY `idx_order` (`order_id`) USING BTREE,
	KEY `index_buyer_created` (`buyer_nick`,`created`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
	COMMENT='客户转化'
	</update>
	
    <update id="createPesCsLoginlogTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`cs_nick` varchar(45) NOT NULL COMMENT '店铺账号',
	`login_time` datetime DEFAULT NULL COMMENT '登陆时间',
	`logout_time` datetime DEFAULT NULL COMMENT '退出时间',
	`shop_id` bigint(20) NOT NULL COMMENT '店铺id',
	`ip` varchar(45) DEFAULT NULL COMMENT '登陆ip',
	`login_sid` varchar(45) DEFAULT NULL COMMENT '登陆sid',
	PRIMARY KEY (`id`),
	KEY `idx_cs` (`cs_nick`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8
	ROW_FORMAT=COMPACT COMMENT='客服登录日志'
	</update>
    <update id="createPesCsOrderBindTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`date` date NOT NULL,
	`cs_nick` varchar(45) NOT NULL,
	`buyer_nick` varchar(45) NOT NULL DEFAULT '',
	`order_id` bigint(20) NOT NULL,
	`order_created` datetime NOT NULL COMMENT '订单下单日期',
	`order_pay_date` datetime DEFAULT NULL COMMENT '订单付款金额',
	`order_payment` double(20,3) DEFAULT NULL COMMENT '订单付款金额',
	`order_goods_num` int(11) DEFAULT '0',
	`order_post_fee` double(20,3) DEFAULT NULL COMMENT '邮费',
	`type` tinyint(1) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`),
	KEY `index_cs_date` (`cs_nick`,`date`) USING BTREE,
	KEY `idx_order` (`order_id`) USING BTREE,
	KEY `idx_buyer` (`buyer_nick`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8
	ROW_FORMAT=COMPACT COMMENT='客服订单绑定表'
	</update>

    <update id="createPesCsOrderEvaluationTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL DEFAULT '0',
	`date` date NOT NULL DEFAULT '0000-00-00' COMMENT '评价产生的日期',
	`cs_nick` varchar(45) NOT NULL COMMENT '客服人员ID',
	`good_rating_num` int(11) NOT NULL DEFAULT '0' COMMENT '好评数',
	`bad_rating_num` int(11) NOT NULL DEFAULT '0' COMMENT '差评数',
	`neutral_rating_num` int(11) NOT NULL DEFAULT '0' COMMENT '中差评',
	PRIMARY KEY (`id`),
	KEY `idx_cs_date` (`cs_nick`,`date`) USING BTREE
	) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
	COMMENT='客服落实的交易的中差评'
	</update>
	
    <update id="createPesCsOrderIndexTable" parameterType="string">
    CREATE TABLE IF NOT EXISTS ${tableName}(
	 `id` bigint(20) NOT NULL AUTO_INCREMENT,
   `shop_id` bigint(20) NOT NULL,
   `date` date NOT NULL,
   `cs_nick` varchar(45) NOT NULL,
   `buyer_nick` varchar(45) NOT NULL DEFAULT '',
   `order_id` bigint(20) NOT NULL,
   `banner_flag` tinyint(2) NOT NULL DEFAULT '0' COMMENT '插旗',
   `order_created` datetime DEFAULT NULL,
   `order_pay_date` datetime DEFAULT NULL,
   `order_payment` double(20,3) DEFAULT '0.000' COMMENT '订单付款金额',
   `order_goods_num` int(11) DEFAULT '0',
   `order_post_fee` double(20,3) DEFAULT NULL COMMENT '邮费',
   `is_goods_filte` bit(1) DEFAULT b'0' COMMENT '商品过滤',
   `is_mrn_filter` bit(1) DEFAULT b'0' COMMENT '最小回复数过滤',
   `bc_chat_num` int(11) DEFAULT '0' COMMENT '下单前 买家咨询的句数',
   `bc_reply_num` int(11) DEFAULT '0' COMMENT '下单前 回复数',
   `bc_chat_round_num` int(11) DEFAULT '0' COMMENT '下单前 聊天回合数',
   `bc_first_reply_date` datetime DEFAULT NULL COMMENT '下单前 首次 客服接待时间',
   `bc_last_reply_date` datetime DEFAULT NULL COMMENT '下单前  客服 最后待时间',
   `bc_first_chat_date` datetime DEFAULT NULL COMMENT '下单前 买家 首次咨询时间',
   `bc_last_chat_date` datetime DEFAULT NULL COMMENT '下单前 买家 最后咨询时间',
   `bp_reply_num` int(11) DEFAULT '0' COMMENT '付款前 回复数',
   `bp_chat_round_num` int(11) DEFAULT '0' COMMENT '付款前 聊天回合数',
   `bp_first_reply_date` datetime DEFAULT NULL COMMENT '付款前  客服 首次接待时间',
   `bp_last_chat_date` datetime DEFAULT NULL COMMENT '付款前  买家 最后咨询时间',
   `bp_last_reply_date` datetime DEFAULT NULL COMMENT '付款前  客服 最后回复时间',
   `bp_chat_num` int(11) DEFAULT '0' COMMENT '付款前 买家咨询的句数',
   `ac_first_reply_date` datetime DEFAULT NULL COMMENT '下单后 客服 首次待时间',
   `ac_first_chat_date` datetime DEFAULT NULL COMMENT '下单后 买家 首次咨询时间',
   `ap_first_chat_date` datetime DEFAULT NULL COMMENT '付款前 买家 首次咨询时间',
   `ap_first_reply_date` datetime DEFAULT NULL COMMENT '付款后 客服 首次回复时间',
   `last_reply_date` datetime DEFAULT NULL,
   `first_reply_date` datetime DEFAULT NULL,
   `first_chat_date` datetime DEFAULT NULL,
   `last_chat_date` datetime DEFAULT NULL,
   PRIMARY KEY (`id`),
   KEY `idx_cs_date` (`cs_nick`,`date`),
   KEY `idx_order` (`order_id`) USING BTREE
 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客服账号 相关的 订单的指标'
	</update>
	
    <update id="createPesCsPerformanceTable" parameterType="string">
		CREATE TABLE IF NOT EXISTS ${tableName}(
		   `id` bigint(20) NOT NULL AUTO_INCREMENT,
		   `shop_id` bigint(20) NOT NULL,
		   `date` date NOT NULL,
		   `cs_nick` varchar(45) NOT NULL COMMENT '客服昵称',
		   `direct_receive_num` int(11) DEFAULT '0' COMMENT '直接接待人数',
		   `forward_in_num` int(11) DEFAULT '0' COMMENT '转入人数',
		   `forward_out_num` int(11) DEFAULT '0' COMMENT '转出人数',
		   `consult_num` int(11) DEFAULT '0' COMMENT '咨询人数',
		   `receive_num` int(11) DEFAULT '0' COMMENT '接待人数',
		   `enquiry_num` int(11) DEFAULT '0' COMMENT '询单人数',
		   `ordered_num_today` int(11) DEFAULT '0' COMMENT '当日询单 当日下单人数',
		   `paid_num_today` int(11) DEFAULT '0' COMMENT '当日询单 当天落实下单 并且 付了款的人数',
		   `ordered_amount_today` double(20,5) DEFAULT '0.00000' COMMENT '当日询单 当日落实下单的金额',
		   `paid_amount_today` double(20,5) DEFAULT '0.00000' COMMENT '当日询单 当日落实下单 并且当天付了款的金额',
		   `ordered_num_final` int(11) DEFAULT '0' COMMENT '当日询单 最终落实下单的人数',
		   `ordered_amount_final` double(20,5) DEFAULT '0.00000' COMMENT '当日询单 最终落实下单的金额',
		   `ordered_goods_num_final` int(11) DEFAULT '0' COMMENT '咨询下单的订单的商品数',
		   `paid_num_final` int(11) DEFAULT '0' COMMENT '当日询单 最终落实下单，并且最终付了款的人数',
		   `paid_amount_final` double(20,5) DEFAULT '0.00000' COMMENT '当日询单 最终落实下单，并且最终付了款的金额',
		   `paid_goods_num_final` int(11) DEFAULT '0',
		   `receive_rate` double(5,3) DEFAULT '0.000' COMMENT '接待占比',
		   `conversion_rate` double(5,3) DEFAULT '0.000' COMMENT '客服转化率',
		   `sale_amount` double(20,3) DEFAULT '0.000' COMMENT '客服销售额',
		   `sale_order_num` int(11) DEFAULT '0' COMMENT '销售单数',
		   `sale_goods_num` int(11) DEFAULT '0' COMMENT '销售件数',
		   `sale_buyer_num` int(11) DEFAULT '0' COMMENT '销售人数',
		   `cfm_goods_o_num` int(11) DEFAULT '0' COMMENT '确认收货订单数',
		   `cfm_goods_amount` double(20,3) DEFAULT '0.000' COMMENT '确认收货金额',
		   `post_fee` double(20,3) DEFAULT '0.000' COMMENT '邮费',
		   PRIMARY KEY (`id`),
		   KEY `IDX_CS_DA_SH` (`cs_nick`,`date`) USING BTREE
		 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客服绩效表'
	</update>
	
    <update id="createPesCsReplyQualityTable" parameterType="string">
		CREATE TABLE IF NOT EXISTS ${tableName}(
		  `id` bigint(20) NOT NULL AUTO_INCREMENT,
		   `shop_id` bigint(20) NOT NULL,
		   `date` date NOT NULL COMMENT '等待时长（统计）日期',
		   `cs_nick` varchar(45) NOT NULL COMMENT '客服人员ID',
		   `avg_wait_time_first` double(8,2) DEFAULT '0.00' COMMENT '平均等待时间长度（秒）',
		   `avg_wait_time` double(8,2) DEFAULT '0.00',
		   `session_time` bigint(20) DEFAULT NULL COMMENT '会话时长(秒)',
		   PRIMARY KEY (`id`),
		   KEY `fk_cs_avgwaitingtime_idx` (`cs_nick`) USING BTREE,
		   KEY `IDX_CS_ID_WAITING_DATE` (`cs_nick`,`date`) USING BTREE,
		   KEY `IDX_SHOP_ID_WAITING_DATE` (`shop_id`,`date`) USING BTREE
		 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='平均响应时间'
	</update>

    <update id="createPesCsServiceEvaluationTable" parameterType="string">
		CREATE TABLE IF NOT EXISTS ${tableName}(
		  `id` bigint(20) NOT NULL AUTO_INCREMENT,
		   `shop_id` bigint(20) NOT NULL,
		   `cs_nick` varchar(45) NOT NULL COMMENT '客服id',
		   `buyer_nick` varchar(45) NOT NULL COMMENT '买家id',
		   `send_time` datetime DEFAULT NULL COMMENT '发送时间',
		   `eval_time` datetime DEFAULT NULL COMMENT '评价时间',
		   `eval_code` int(11) DEFAULT NULL COMMENT '评价等级，非常满意100，满意75，不满意50，非常不满意25',
		   PRIMARY KEY (`id`),
		   KEY `idx_cs_evaltime` (`cs_nick`,`eval_time`) USING BTREE
		 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='每日 客服 服务评价 汇总表'
	</update>
	<update id="createPesCsServiceIndexTable" parameterType="string">
		CREATE TABLE IF NOT EXISTS ${tableName}(
		 `id` bigint(20) NOT NULL AUTO_INCREMENT,
	   `shop_id` bigint(20) NOT NULL,
	   `date` date NOT NULL COMMENT '等待时长（统计）日期',
	   `cs_nick` varchar(45) NOT NULL COMMENT '客服人员ID',
	   `avg_wait_time_first` double(20,3) DEFAULT '0.000' COMMENT '平均等待时间长度（秒）',
	   `avg_wait_time` double(20,3) DEFAULT '0.000',
	   `session_time` bigint(20) DEFAULT NULL COMMENT '会话时长(秒)',
	   `non_reply_num` int(11) DEFAULT '0' COMMENT '客服未回复数',
	   `cs_word_num` bigint(20) DEFAULT '0' COMMENT '客服字数',
	   `cs_reply_num` int(11) DEFAULT '0' COMMENT '客服聊天记录数',
	   `buyer_chat_num` int(11) DEFAULT '0' COMMENT '买家聊天记录数',
	   `qa_rate` double(20,3) DEFAULT '0.000' COMMENT '答问比',
	   PRIMARY KEY (`id`),
	   KEY `idx_cs_date` (`cs_nick`,`date`) USING BTREE
	 ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='客服服务指标'
	</update>


    <update id="createPesLostRecordNoteTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL DEFAULT '0',
	`buyer_nick` varchar(45) NOT NULL COMMENT '买家旺旺id',
	`order_id` bigint(20) DEFAULT NULL COMMENT '订单id',
	`date` date NOT NULL COMMENT '流失日期',
	`note` varchar(500) DEFAULT NULL COMMENT '备注',
	`lost_type` bit(1) DEFAULT b'0' COMMENT '是否为静默流失 0:接待流失 1:静默流失',
	PRIMARY KEY (`id`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8
	ROW_FORMAT=COMPACT COMMENT='流失记录备注'
			
	</update>
	
    <update id="createPesOrderTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`order_id` bigint(255) NOT NULL,
	`trade_id` bigint(255) unsigned DEFAULT NULL COMMENT '交易编号 (父订单的交易编号)',
	`seller_nick` varchar(255) NOT NULL,
	`date` date NOT NULL,
	`shop_id` bigint(20) NOT NULL,
	`payment` double DEFAULT NULL COMMENT '用户实付金额',
	`post_fee` double DEFAULT '0' COMMENT '邮费',
	`consign_time` datetime DEFAULT NULL COMMENT '发货时间',
	`num` int(11) DEFAULT NULL COMMENT '数量',
	`status` varchar(100) DEFAULT NULL COMMENT '交易状态',
	`total_fee` double DEFAULT '0' COMMENT '商品金额（商品价格乘以数量的总金额）',
	`created` datetime DEFAULT NULL COMMENT '创建时间',
	`pay_time` datetime DEFAULT NULL COMMENT '付款时间',
	`modified` datetime DEFAULT NULL COMMENT '交易修改时间',
	`end_time` datetime DEFAULT NULL COMMENT '交易结束时间',
	`buyer_nick` varchar(45) DEFAULT NULL COMMENT '买家昵称',
	`seller_flag` bigint(20) DEFAULT '0' COMMENT
	'卖家备注旗帜（与淘宝网上订单的卖家备注旗帜对应，只有卖家才能查看该字段）红、黄、绿、蓝、紫 分别对应 1、2、3、4、5',
	`type` varchar(50) DEFAULT '' COMMENT '交易类型列表',
	`step_trade_status` varchar(50) CHARACTER SET utf32 DEFAULT '',
	`step_paid_fee` double DEFAULT '0',
	PRIMARY KEY (`order_id`) USING BTREE,
	KEY `idx_buyer_paytime` (`buyer_nick`,`pay_time`),
	KEY `idx_buyer_created` (`buyer_nick`,`created`),
	KEY `idx_endtime` (`buyer_nick`,`end_time`),
	KEY `idx_consign_time` (`consign_time`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='订单交易表'
	</update>

	<update id="createPesOrderDetailTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`order_id` bigint(255) NOT NULL,
	`item_sku_id` varchar(45) NOT NULL COMMENT '京东内部商品ID（极端情况下不保证返回，建议从商品接口获取） ',
	`item_price` double(20,3) NOT NULL COMMENT 'SKU的京东价 ',
	`item_num` int(11) NOT NULL COMMENT '数量 ',
	`item_sku_name` varchar(45) NOT NULL COMMENT '商品的名称+SKU规格',
	`buyer_nick` varchar(255) DEFAULT NULL,
	`created` datetime DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='订单细节表'
	</update>
    <update id="createPesOrderEvaluateTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL,
	`order_id` bigint(20) unsigned NOT NULL DEFAULT '0',
	`trade_id` bigint(20) NOT NULL DEFAULT '0',
	`buyer_nick` varchar(45) NOT NULL,
	`result` varchar(8) NOT NULL,
	`created` datetime NOT NULL,
	`shop_id` bigint(20) unsigned NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`,`trade_id`),
	KEY `idx_order` (`trade_id`) USING BTREE,
	KEY `idx_created` (`created`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='订单评价表'
    </update>
    	
    <update id="createPesOrderFilterTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL COMMENT '店铺id',
	`date` date NOT NULL,
	`buyer_nick` varchar(45) NOT NULL,
	`order_id` bigint(20) NOT NULL COMMENT '订单ID',
	`trade_id` bigint(20) NOT NULL COMMENT '交易id',
	`sku_id` bigint(20) NOT NULL COMMENT '商品数字编号',
	`created` datetime NOT NULL COMMENT '订单创建时间',
	`type` varchar(45) DEFAULT '' COMMENT '订单过滤类型',
	`num` int(11) DEFAULT '0' COMMENT '商品数量',
	`title` varchar(200) NOT NULL,
	`price` double(20,3) NOT NULL DEFAULT '0.000',
	PRIMARY KEY (`id`),
	KEY `idx_order` (`order_id`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
	COMMENT='订单过滤表'
    </update>

    <update id="createPesShopDayOverviewTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName}(
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`date` date NOT NULL,
	`sale_amount` double(20,3) DEFAULT '0.000' COMMENT '店铺销售额',
	`sale_order_num` int(11) DEFAULT '0' COMMENT '付款订单数',
	`sale_buyer_num` int(11) DEFAULT '0' COMMENT '付款买家数',
	`consign_num` int(11) DEFAULT '0' COMMENT '发货单数',
	`ordered_num` int(11) DEFAULT '0' COMMENT '当天下单数',
	`ordered_amount` double(20,3) DEFAULT '0.000' COMMENT '当天下单金额',
	`cfm_goods_o_amount` double(20,3) DEFAULT '0.000' COMMENT '确认收货金额',
	`cfm_goodst_o_num` int(11) DEFAULT '0' COMMENT '确认收货订单数',
	`team_receive_num` int(11) DEFAULT '0' COMMENT '团队接待人数',
	`team_enquiry_num` int(11) DEFAULT '0' COMMENT '客服团队询单人数',
	PRIMARY KEY (`id`),
	KEY `idx_date` (`date`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
	COMMENT='店铺每日绩效'
    </update>
    
   <update id="createPesShopDsrTable" parameterType="string">
	CREATE TABLE IF NOT EXISTS ${tableName} (
	`id` bigint(20) NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL,
	`date` date NOT NULL,
	`item_score` double(20,5) DEFAULT '0.00000' COMMENT '商品评分',
	`service_score` double(20,5) DEFAULT '0.00000' COMMENT '服务态度评分',
	`delivery_score` double(20,5) DEFAULT '0.00000' COMMENT '物流速度评分',
	PRIMARY KEY (`id`),
	KEY `IDX_SHOP_ID_DATE` (`shop_id`,`date`),
	KEY `index_shop_id` (`shop_id`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT
	COMMENT='店铺评分表'
</update>
</mapper>