<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ItemCodeSubuserMapper">

	<resultMap id="ItemCodeSubuserDO" type="com.pes.jd.model.DO.ItemCodeSubuserDO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="item_code" jdbcType="VARCHAR" property="itemCode" />
		<result column="subuser_num" jdbcType="VARCHAR" property="subuserNum" />
	</resultMap>
	<resultMap id="ItemCodeSubuserDTO" type="com.pes.jd.model.DTO.ItemCodeSubuserDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="item_code" jdbcType="VARCHAR" property="itemCode" />
		<result column="subuser_num" jdbcType="VARCHAR" property="subuserNum" />
		<result column="app_version_id" jdbcType="INTEGER" property="appVersionId" />
	</resultMap>

	<select id="selectItemCodeSubuserByItemCode" resultMap="ItemCodeSubuserDTO">
		select item_code,subuser_num,app_version_id from  pes_itemcode_subuser
		where item_code=#{itemCode}
	</select>

</mapper>