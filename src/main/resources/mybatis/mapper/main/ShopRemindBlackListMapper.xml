<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopRemindBlackListMapper" >
  <resultMap id="ShopRemindBlackListDO" type="com.pes.jd.model.DO.ShopRemindBlackListDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="operator" property="operator" jdbcType="BIGINT" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="base_field" >
    id, shop_id, operator, buyer_nick, `type`, created
  </sql>

  <insert id="batchInsert">
    insert into pes_shop_remind_blacklist
    (
    shop_id,
    operator,
    buyer_nick,
    `type`,
    created
    )

    VALUES
    <foreach collection="result" item="model" separator=",">
      (
      #{model.shopId,jdbcType=BIGINT},
      #{model.operator,jdbcType=BIGINT},
      #{model.buyerNick,jdbcType=VARCHAR},
      #{model.type,jdbcType=INTEGER},
      #{model.created,jdbcType=TIMESTAMP}
      )
    </foreach>
  </insert>
  <delete id="deleteShopRemindBlackListById">
    delete from pes_shop_remind_blacklist
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="selectShopRemindBlackList" resultType="com.pes.jd.model.DTO.ShopRemindBlackListDTO">
    select
    <include refid="base_field"/>
    from pes_shop_remind_blacklist
    <where>
      shop_id=#{shopId}
      and created between #{startDate} and #{endDate}
      <if test="buyerNick!=null and buyerNick!=''">
        and (buyer_nick =#{buyerNick} or telephone like CONCAT(CONCAT('%',#{buyerNick}),'%') )
      </if>
    </where>
  </select>

  <select id="selectShopRemindBlacklistById" resultType="com.pes.jd.model.DTO.ShopRemindBlackListDTO">
    select shop_id,
           buyer_nick,
           telephone
    from pes_shop_remind_blacklist
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectShopRemindBlacklistByShopIdAndBuyerNickSet" resultType="com.pes.jd.model.DTO.ShopRemindBlackListDTO">
    select
    shop_id,buyer_nick,telephone
    from pes_shop_remind_blacklist
    <where>
      shop_id=#{shopId}
      and buyer_nick in
      <foreach collection="buyerNickSet" item="buyerNick" close=")" open="(" separator=",">
        #{buyerNick}
      </foreach>
    </where>
  </select>
  <select id="selectShopRemindBlacklistByShopId" resultType="com.pes.jd.model.DTO.ShopRemindBlackListDTO">
    select
    <include refid="base_field"/>
    from pes_shop_remind_blacklist
    where
    shop_id=#{shopId}
  </select>

  <select id="selectManualMerchandisingBlacklistList" resultType="java.lang.String">
    select buyer_nick
    from pes_shop_remind_blacklist
    where shop_id = #{shopId}
      and `type` in (2, 4, 6, 7)
  </select>

</mapper>