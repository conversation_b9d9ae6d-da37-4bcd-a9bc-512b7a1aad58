<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.UserResourceMapper">

	<resultMap id="UserResourceDO" type="com.pes.jd.model.DO.UserResource">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="nick" jdbcType="VARCHAR" property="nick" />
		<result column="resource_id" jdbcType="BIGINT" property="resourceId" />
	</resultMap>
	
	<resultMap id="ShopAccountResourceVO" type="com.pes.jd.model.VO.ShopAccountResourceVO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="nick" jdbcType="VARCHAR" property="nick" />
	</resultMap>
	
	<insert id="insertBatch" parameterType="java.util.List">
		INSERT INTO pes_user_resource(nick,resource_id)
		VALUES
		<foreach collection="userResourceList" item="item" index="index" separator=",">
			(#{item.nick},#{item.resourceId})
		</foreach>
	</insert>
	
	<select id="queryShopAccountResourceByNicks" parameterType="java.util.Map" resultMap="ShopAccountResourceVO">
		SELECT ur.nick, GROUP_CONCAT(r.name) as resourceName
		FROM pes_user_resource ur
		LEFT JOIN pes_resource r
		ON ur.resource_id = r.id
		WHERE ur.nick IN
		<foreach collection="nicks" index="index" item="item" open="("
			separator="," close=")"> #{item}  </foreach>
		AND r.name IN
		<foreach collection="resourceNameList" index="index" item="resourceName" open="("
			separator="," close=")"> #{resourceName}  </foreach>
		GROUP BY ur.nick
	</select>
	
	<select id="selectResourceIdByResourceNames" parameterType="java.util.List" resultType="java.lang.String">
		SELECT id
		FROM pes_resource
		WHERE name IN
		<foreach collection="resourceNameList" index="index" item="item" open="(" 
			separator="," close=")"> #{item} </foreach>
	</select>
	
	<delete id="deleteShopAccountResourceByNicks" parameterType="java.util.Map">
		DELETE FROM pes_user_resource
		WHERE nick IN
		<foreach collection="nicks" item="nick" index="index" open="("
			close=")" separator=",">
			#{nick}
		</foreach>
		AND resource_id IN
		<foreach collection="resourceIdList" item="resourceId" index="index" open="("
			close=")" separator=",">
			#{resourceId}
		</foreach>
	</delete>
	<select id="selectMenuResource" parameterType="string" resultType="com.pes.jd.model.DO.Resource">
	SELECT e.id, e.url, e.note, e.name, e.parent_id as parentId, e.title  FROM
	pes_role_resource r
	INNER JOIN pes_resource e
	on e.id = r.resource_id and r.role_id = #{roleId}
	where
	e.type = 'menu_1' or e.type = 'menu_2' ORDER BY sort;
	</select>
	
	<select id="selectCurrentUserShortCutMenuByUserId" parameterType="string" resultType="com.pes.jd.model.DO.Resource">
	SELECT e.id, e.url, e.name, e.parent_id as parentId, e.title FROM
	pes_shortcut_menu r
	LEFT JOIN
	pes_resource e
	ON e.id = r.resource_id
	WHERE
	e.type = 'menu_1' or e.type = 'menu_2'
	and
	r.user_id = #{userId}
	</select>
	
	<select id="selectBaseShortCutMenuResource" resultType="com.pes.jd.model.DO.Resource">
	SELECT
	e.id,
	e.url,
	e.note,
	e.name,
	e.parent_id AS parentId
	FROM
	customerservice_pes.pes_resource e
	WHERE
	approve = 1
	AND e.type = 'menu_2';
	</select>
</mapper>