<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesUserAnalysMapper">

    <resultMap id="resultMap" type="com.pes.jd.model.DTO.PesUserAnalysDTO">
        <result property="id" column="id"/>
        <result property="popCpNum" column="pop_cp_num"/>
        <result property="selfCpNum" column="self_cp_num"/>
        <result property="notCpNum" column="not_cp_num"/>
        <result property="popRtMonitoringNum" column="pop_rt_monitoring_num"/>
        <result property="selfRtMonitoringNum" column="self_rt_monitoring_num"/>
        <result property="notRtMonitoringNum" column="not_rt_monitoring_num"/>
        <result property="popAnalysReportNum" column="pop_analys_report_num"/>
        <result property="selfAnalysReportNum" column="self_analys_report_num"/>
        <result property="notAnalysReportNum" column="not_analys_report_num"/>
        <result property="popAboutUsNum" column="pop_about_us_num"/>
        <result property="selfAboutUsNum" column="self_about_us_num"/>
        <result property="notAboutUsNum" column="not_about_us_num"/>
    </resultMap>

    <insert id="insertUserAnalys">
        INSERT INTO pes_user_analys(`date`,pop_cp_num,self_cp_num,not_cp_num,
        pop_rt_monitoring_num,self_rt_monitoring_num,not_rt_monitoring_num,
        pop_analys_report_num,self_analys_report_num,not_analys_report_num,
        pop_about_us_num,self_about_us_num,not_about_us_num)
        VALUES(#{pesUserAnalysDO.date},#{pesUserAnalysDO.popCpNum},#{pesUserAnalysDO.selfCpNum},#{pesUserAnalysDO.notCpNum},
        #{pesUserAnalysDO.popRtMonitoringNum},#{pesUserAnalysDO.selfRtMonitoringNum},#{pesUserAnalysDO.notRtMonitoringNum},
        #{pesUserAnalysDO.popAnalysReportNum},#{pesUserAnalysDO.selfAnalysReportNum},#{pesUserAnalysDO.notAnalysReportNum},
        #{pesUserAnalysDO.popAboutUsNum},#{pesUserAnalysDO.selfAboutUsNum},#{pesUserAnalysDO.notAboutUsNum})
    </insert>


    <update id="updateUserAnalys">
        UPDATE pes_user_analys
        <set>
            <if test="pesUserAnalysDTO.popCpNum > 0 ">
                pop_cp_num = pop_cp_num + #{pesUserAnalysDTO.popCpNum} ,
            </if>
            <if test="pesUserAnalysDTO.selfCpNum > 0 ">
                self_cp_num = self_cp_num + #{pesUserAnalysDTO.selfCpNum} ,
            </if>
            <if test="pesUserAnalysDTO.notCpNum > 0 ">
                not_cp_num = not_cp_num + #{pesUserAnalysDTO.notCpNum} ,
            </if>

            <if test="pesUserAnalysDTO.popRtMonitoringNum > 0 ">
                pop_rt_monitoring_num = pop_rt_monitoring_num + #{pesUserAnalysDTO.popRtMonitoringNum} ,
            </if>
            <if test="pesUserAnalysDTO.selfRtMonitoringNum > 0 ">
                self_rt_monitoring_num = self_rt_monitoring_num + #{pesUserAnalysDTO.selfRtMonitoringNum} ,
            </if>
            <if test="pesUserAnalysDTO.notRtMonitoringNum > 0 ">
                not_rt_monitoring_num = not_rt_monitoring_num + #{pesUserAnalysDTO.notRtMonitoringNum} ,
            </if>

            <if test="pesUserAnalysDTO.popAnalysReportNum > 0 ">
                pop_analys_report_num = pop_analys_report_num + #{pesUserAnalysDTO.popAnalysReportNum} ,
            </if>
            <if test="pesUserAnalysDTO.selfAnalysReportNum > 0 ">
                self_analys_report_num = self_analys_report_num + #{pesUserAnalysDTO.selfAnalysReportNum} ,
            </if>
            <if test="pesUserAnalysDTO.notAnalysReportNum > 0 ">
                not_analys_report_num = not_analys_report_num + #{pesUserAnalysDTO.notAnalysReportNum} ,
            </if>

            <if test="pesUserAnalysDTO.popAboutUsNum > 0 ">
                pop_about_us_num = pop_about_us_num + #{pesUserAnalysDTO.popAboutUsNum} ,
            </if>
            <if test="pesUserAnalysDTO.selfAboutUsNum > 0 ">
                self_about_us_num = self_about_us_num + #{pesUserAnalysDTO.selfAboutUsNum} ,
            </if>
            <if test="pesUserAnalysDTO.notAboutUsNum > 0 ">
                not_about_us_num = not_about_us_num + #{pesUserAnalysDTO.notAboutUsNum} ,
            </if>
        </set>
        <where>
            `date` = #{pesUserAnalysDTO.date}
        </where>
    </update>


    <select id="selectUserAnalysByDate" resultMap="resultMap">
        SELECT pop_cp_num,self_cp_num,not_cp_num,
        pop_rt_monitoring_num,self_rt_monitoring_num,not_rt_monitoring_num,
        pop_analys_report_num,self_analys_report_num,not_analys_report_num,
        pop_about_us_num,self_about_us_num,not_about_us_num
        FROM pes_user_analys where `date` = #{date}
    </select>
</mapper>