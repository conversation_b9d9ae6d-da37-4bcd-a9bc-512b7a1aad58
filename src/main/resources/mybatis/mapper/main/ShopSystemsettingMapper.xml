<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopSystemsettingMapper">

	<resultMap id="ShopSystemsettingDTO" type="com.pes.jd.model.DTO.ShopSystemsettingDTO">
	<id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="auto_reply_switch" property="autoReplySwitch" jdbcType="BIT" />
    <result column="auto_reply_mark" property="autoReplyMark" jdbcType="VARCHAR" />
    <result column="aftersell_acount_filter" property="aftersellAcountFilter" jdbcType="BIT" />
    <result column="sell_after" property="sellAfter" jdbcType="INTEGER" />
    <result column="max_wait_time" property="maxWaitTime" jdbcType="INTEGER" />
    <result column="slow_response_time" property="slowResponseTime" jdbcType="INTEGER" />
    <result column="slow_response_times_num" property="slowResponseTimesNum" jdbcType="INTEGER" />
    <result column="quick_response_time" property="quickResponseTime" jdbcType="INTEGER" />
    <result column="long_reception_time" property="longReceptionTime" jdbcType="INTEGER" />
    <result column="scheduling_time_dot" property="schedulingTimeDot" jdbcType="INTEGER" />
    <result column="cs_recommend_switch" property="csRecommendSwitch" jdbcType="BIT" />
    <result column="cs_recommend_mark" property="csRecommendMark" jdbcType="VARCHAR" />
    <result column="duty_record_export_unit" property="dutyRecordExportUnit" jdbcType="VARCHAR" />
    <result column="value_service_sale_amout_switch" property="valueServiceSaleAmoutSwitch" jdbcType="BIT" />
    <result column="value_service_goods_num_switch" property="valueServiceGoodsNumSwitch" jdbcType="BIT" />
    <result column="judge_rule_ascription" property="judgeRuleAscription" jdbcType="INTEGER" />
    <result column="judge_rule" property="judgeRule" jdbcType="INTEGER" />
    <result column="enquiry_valid_duration_time" property="enquiryValidDurationTime" jdbcType="INTEGER" />
    <result column="out_stock_valid_duration_time" property="outStockValidDurationTime" jdbcType="INTEGER" />
    <result column="silent_all_switch" property="silentAllSwitch" jdbcType="BIT" />
    <result column="silent_all_follow_up_time" property="silentAllFollowUpTime" jdbcType="INTEGER" />
    <result column="is_bind_silent_all_order" property="isBindSilentAllOrder" jdbcType="BIT" />
    <result column="active_follow_up_switch" property="activeFollowUpSwitch" jdbcType="BIT" />
    <result column="cust_first_reply_day" property="custFirstReplyDay" jdbcType="INTEGER" />
    <result column="silent_urgepay_switch" property="silentUrgepaySwitch" jdbcType="BIT" />
    <result column="silent_urgepay_time" property="silentUrgepayTime" jdbcType="INTEGER" />
    <result column="order_flag_switch" property="orderFlagSwitch" jdbcType="BIT" />
    <result column="order_flag" property="orderFlag" jdbcType="INTEGER" />
    <result column="enquiry_loss_switch" property="enquiryLossSwitch" jdbcType="BIT" />
    <result column="cs_to_cust_loss_valid_time" property="csToCustLossValidTime" jdbcType="INTEGER" />
    <result column="min_reply_num" property="minReplyNum" jdbcType="INTEGER" />
    <result column="team_chat_filte_switch" property="teamChatFilteSwitch" jdbcType="BIT" />
    <result column="sys_cs_chat_filte_switch" property="sysCsChatFilteSwitch" jdbcType="BIT" />
    <result column="non_chat_filte_switch" property="nonChatFilteSwitch" jdbcType="BIT" />
    <result column="cs_offline_filte_cust_msg_switch" property="csOfflineFilteCustMsgSwitch" jdbcType="BIT" />
    <result column="cs_forward_switch" property="csForwardSwitch" jdbcType="BIT" />
    <result column="cs_forward_num" property="csForwardNum" jdbcType="INTEGER" />
    <result column="jixiao_time" property="jixiaoTime" jdbcType="VARCHAR" />
    <result column="cs_watchword_switch" property="csWatchwordSwitch" jdbcType="BIT" />
    <result column="cs_watchword" property="csWatchword" jdbcType="VARCHAR" />
    <result column="cs_watchword_send_times_num" property="csWatchwordSendTimesNum" jdbcType="INTEGER" />
    <result column="main_account_auto_reply_switch" property="mainAccountAutoReplySwitch" jdbcType="BIT" />
    <result column="main_account_auto_reply_content" property="mainAccountAutoReplyContent" jdbcType="VARCHAR" />
    <result column="cs_to_csut_first_lost_switch" property="csToCsutFirstLostSwitch" jdbcType="BIT" />
    <result column="cust_sigchat_switch" property="custSigchatSwitch" jdbcType="BIT" />
    <result column="sigchat_min_reply_num" property="sigchatMinReplyNum" jdbcType="INTEGER" />
    <result column="cs_single_chat_filter" property="csSingleChatFilter" jdbcType="BIT" />
    <result column="platform_cs_filte_switch" property="platformCsFilteSwitch" jdbcType="BIT" />
    <result column="cust_single_chat_num" property="custSingleChatNum" jdbcType="INTEGER" />
    <result column="cust_chat_word_switch" property="custChatWordSwitch" jdbcType="BIT" />
    <result column="cust_watchword" property="custWatchword" jdbcType="VARCHAR" />
    <result column="open_warn" property="openWarn" jdbcType="BIT" />
    <result column="warn_accept_cs" property="warnAcceptCs" jdbcType="VARCHAR" />
    <result column="rtpf_cs_login_switch" property="rtpfCsLoginSwitch" jdbcType="BIT" />
    <result column="duty_rid_cs_switch" property="dutyRidCsSwitch" jdbcType="BIT" />
    <result column="rtpf_rid_cs_switch" property="rtpfRidCsSwitch" jdbcType="BIT" />
    <result column="rtpf_rid_cs_rate" property="rtpfRidCsRate" jdbcType="INTEGER" />
    <result column="pool_converted_no_pay_switch" property="poolConvertedNoPaySwitch" jdbcType="BIT" />
    <result column="task_count_urge_pay_switch" property="taskCountUrgePaySwitch" jdbcType="BIT" />
    <result column="rt_group_mean_switch" property="rtGroupMeanSwitch" jdbcType="BIT" />
    <result column="rt_group_mean_value" property="rtGroupMeanValue" jdbcType="INTEGER" />
    <result column="pool_rid_pay_buyer_switch" property="poolRidPayBuyerSwitch" jdbcType="BIT" />
    <result column="pool_urge_after_pay_switch" property="poolUrgeAfterPaySwitch" jdbcType="BIT" />
        <result column="urge_loss_follow_up_time" property="urgeLossFollowUpTime" jdbcType="INTEGER" />
        <result column="leave_message_switch" property="leaveMessageSwitch" jdbcType="BIT" />
	</resultMap>
	
	<sql id="base_field">
	id, shop_id, auto_reply_switch, auto_reply_mark, aftersell_acount_filter, sell_after, 
    max_wait_time, slow_response_time, slow_response_times_num, quick_response_time, 
    long_reception_time, scheduling_time_dot, cs_recommend_switch, cs_recommend_mark, 
    duty_record_export_unit, value_service_sale_amout_switch, value_service_goods_num_switch, 
    judge_rule_ascription, judge_rule, enquiry_valid_duration_time, out_stock_valid_duration_time, 
    silent_all_switch, silent_all_follow_up_time, is_bind_silent_all_order, active_follow_up_switch,
    cust_first_reply_day, silent_urgepay_switch, silent_urgepay_time, order_flag_switch,
    order_flag, enquiry_loss_switch, cs_to_cust_loss_valid_time, min_reply_num, team_chat_filte_switch, 
    sys_cs_chat_filte_switch, non_chat_filte_switch, cs_offline_filte_cust_msg_switch, 
    cs_forward_switch, cs_forward_num, jixiao_time, cs_watchword_switch, cs_watchword, 
    cs_watchword_send_times_num, main_account_auto_reply_switch, main_account_auto_reply_content, 
    cs_to_csut_first_lost_switch, cust_sigchat_switch, sigchat_min_reply_num, cs_single_chat_filter, 
    platform_cs_filte_switch, cust_single_chat_num, cust_chat_word_switch, cust_watchword,open_warn,warn_accept_cs,
    duty_rid_cs_switch,rtpf_rid_cs_switch,rtpf_rid_cs_rate,pool_converted_no_pay_switch,task_count_urge_pay_switch,rt_group_mean_switch,rt_group_mean_value,
    rtpf_cs_login_switch,duty_rid_cs_switch,rtpf_rid_cs_switch,rtpf_rid_cs_rate,cs_forward_aftersell_switch,
    urge_loss_follow_up_time,pool_rid_pay_buyer_switch,pool_urge_after_pay_switch,leave_message_switch
    </sql>
	
	<insert id="insertShopSystemsetting" parameterType="com.pes.jd.model.DO.ShopSystemsettingDO">
		 insert into pes_shop_systemsetting (shop_id, auto_reply_switch, 
      auto_reply_mark, aftersell_acount_filter, sell_after, 
      max_wait_time, slow_response_time, slow_response_times_num, 
      quick_response_time, long_reception_time, scheduling_time_dot, 
      cs_recommend_switch, cs_recommend_mark, duty_record_export_unit, 
      value_service_sale_amout_switch, value_service_goods_num_switch, 
      judge_rule_ascription, judge_rule, enquiry_valid_duration_time, 
      out_stock_valid_duration_time, silent_all_switch, 
      silent_all_follow_up_time, is_bind_silent_all_order, 
      active_follow_up_switch, cust_first_reply_day, silent_urgepay_switch, 
      silent_urgepay_time, order_flag_switch, order_flag, 
      enquiry_loss_switch, cs_to_cust_loss_valid_time, min_reply_num, 
      team_chat_filte_switch, sys_cs_chat_filte_switch, non_chat_filte_switch, 
      cs_offline_filte_cust_msg_switch, cs_forward_switch, cs_forward_num, 
      jixiao_time, cs_watchword_switch, cs_watchword, 
      cs_watchword_send_times_num, main_account_auto_reply_switch, 
      main_account_auto_reply_content, cs_to_csut_first_lost_switch, 
      cust_sigchat_switch, sigchat_min_reply_num, cs_single_chat_filter, 
      platform_cs_filte_switch, cust_single_chat_num, cust_chat_word_switch, 
      cust_watchword,open_warn,warn_accept_cs,rtpf_rid_cs_rate,
      duty_rid_cs_switch,
      rtpf_rid_cs_switch,
      pool_rid_pay_buyer_switch,
      pool_urge_after_pay_switch,
      rtpf_cs_login_switch,
      pool_converted_no_pay_switch,
      task_count_urge_pay_switch,
      rt_group_mean_switch,
      rt_group_mean_value,
      cs_forward_aftersell_switch,
      urge_loss_follow_up_time
      )
    values (#{shopId,jdbcType=BIGINT}, #{autoReplySwitch,jdbcType=BIT}, 
      #{autoReplyMark,jdbcType=VARCHAR}, #{aftersellAcountFilter,jdbcType=BIT}, #{sellAfter,jdbcType=INTEGER}, 
      #{maxWaitTime,jdbcType=INTEGER}, #{slowResponseTime,jdbcType=INTEGER}, #{slowResponseTimesNum,jdbcType=INTEGER}, 
      #{quickResponseTime,jdbcType=INTEGER}, #{longReceptionTime,jdbcType=INTEGER}, #{schedulingTimeDot,jdbcType=INTEGER}, 
      #{csRecommendSwitch,jdbcType=BIT}, #{csRecommendMark,jdbcType=VARCHAR}, #{dutyRecordExportUnit,jdbcType=VARCHAR}, 
      #{valueServiceSaleAmoutSwitch,jdbcType=BIT}, #{valueServiceGoodsNumSwitch,jdbcType=BIT}, 
      #{judgeRuleAscription,jdbcType=INTEGER}, #{judgeRule,jdbcType=INTEGER}, #{enquiryValidDurationTime,jdbcType=INTEGER}, 
      #{outStockValidDurationTime,jdbcType=INTEGER}, #{silentAllSwitch,jdbcType=BIT}, 
      #{silentAllFollowUpTime,jdbcType=INTEGER}, #{isBindSilentAllOrder,jdbcType=BIT}, 
      #{activeFollowUpSwitch,jdbcType=BIT}, #{custFirstReplyDay,jdbcType=INTEGER}, #{silentUrgepaySwitch,jdbcType=BIT}, 
      #{silentUrgepayTime,jdbcType=INTEGER}, #{orderFlagSwitch,jdbcType=BIT}, #{orderFlag,jdbcType=INTEGER}, 
      #{enquiryLossSwitch,jdbcType=BIT}, #{csToCustLossValidTime,jdbcType=INTEGER}, #{minReplyNum,jdbcType=INTEGER}, 
      #{teamChatFilteSwitch,jdbcType=BIT}, #{sysCsChatFilteSwitch,jdbcType=BIT}, #{nonChatFilteSwitch,jdbcType=BIT}, 
      #{csOfflineFilteCustMsgSwitch,jdbcType=BIT}, #{csForwardSwitch,jdbcType=BIT}, #{csForwardNum,jdbcType=INTEGER}, 
      #{jixiaoTime,jdbcType=VARCHAR}, #{csWatchwordSwitch,jdbcType=BIT}, #{csWatchword,jdbcType=VARCHAR}, 
      #{csWatchwordSendTimesNum,jdbcType=INTEGER}, #{mainAccountAutoReplySwitch,jdbcType=BIT}, 
      #{mainAccountAutoReplyContent,jdbcType=VARCHAR}, #{csToCsutFirstLostSwitch,jdbcType=BIT}, 
      #{custSigchatSwitch,jdbcType=BIT}, #{sigchatMinReplyNum,jdbcType=INTEGER}, #{csSingleChatFilter,jdbcType=BIT}, 
      #{platformCsFilteSwitch,jdbcType=BIT}, #{custSingleChatNum,jdbcType=INTEGER}, #{custChatWordSwitch,jdbcType=BIT}, 
      #{custWatchword,jdbcType=VARCHAR},#{openWarn,jdbcType=BIT},#{warnAcceptCs,jdbcType=VARCHAR},#{rtpfRidCsRate,jdbcType=INTEGER},
       #{dutyRidCsSwitch,jdbcType=BIT},
      #{rtpfRidCsSwitch,jdbcType=BIT},
      #{poolUrgeAfterPaySwitch,jdbcType=BIT},
      #{poolRidPayBuyerSwitch,jdbcType=BIT},
      #{rtpfCsLoginSwitch,jdbcType=BIT},
      #{poolConvertedNoPaySwitch,jdbcType=BIT},
      #{taskCountUrgePaySwitch,jdbcType=BIT},
     #{rtGroupMeanSwitch,jdbcType=BIT},
      #{rtGroupMeanValue,jdbcType=INTEGER},
      #{csForwardAftersellSwitch,jdbcType=BIT},
       #{urgeLossFollowUpTime,jdbcType=INTEGER}
      )
  </insert>
	
	<delete id="deleteShopSystemsettingByShopId" parameterType="java.lang.Long">
		DELETE FROM pes_shop_systemsetting
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</delete>
	
	<update id="updateShopSystemsettingByShopId" parameterType="com.pes.jd.model.DTO.ShopSystemsettingDTO">
 		update pes_shop_systemsetting
     <set>
	  <if test="autoReplySwitch != null" >
        auto_reply_switch = #{autoReplySwitch,jdbcType=BIT},
      </if>
      <if test="autoReplyMark != null" >
        auto_reply_mark = #{autoReplyMark,jdbcType=VARCHAR},
      </if>
      <if test="aftersellAcountFilter != null" >
        aftersell_acount_filter = #{aftersellAcountFilter,jdbcType=BIT},
      </if>
      <if test="sellAfter != null" >
        sell_after = #{sellAfter,jdbcType=INTEGER},
      </if>
      <if test="maxWaitTime != null" >
        max_wait_time = #{maxWaitTime,jdbcType=INTEGER},
      </if>
      <if test="slowResponseTime != null" >
        slow_response_time = #{slowResponseTime,jdbcType=INTEGER},
      </if>
      <if test="slowResponseTimesNum != null" >
        slow_response_times_num = #{slowResponseTimesNum,jdbcType=INTEGER},
      </if>
      <if test="quickResponseTime != null" >
        quick_response_time = #{quickResponseTime,jdbcType=INTEGER},
      </if>
      <if test="longReceptionTime != null" >
        long_reception_time = #{longReceptionTime,jdbcType=INTEGER},
      </if>
      <if test="schedulingTimeDot != null" >
        scheduling_time_dot = #{schedulingTimeDot,jdbcType=INTEGER},
      </if>
      <if test="csRecommendSwitch != null" >
        cs_recommend_switch = #{csRecommendSwitch,jdbcType=BIT},
      </if>
      <if test="csRecommendMark != null" >
        cs_recommend_mark = #{csRecommendMark,jdbcType=VARCHAR},
      </if>
      <if test="dutyRecordExportUnit != null" >
        duty_record_export_unit = #{dutyRecordExportUnit,jdbcType=VARCHAR},
      </if>
      <if test="valueServiceSaleAmoutSwitch != null" >
        value_service_sale_amout_switch = #{valueServiceSaleAmoutSwitch,jdbcType=BIT},
      </if>
      <if test="valueServiceGoodsNumSwitch != null" >
        value_service_goods_num_switch = #{valueServiceGoodsNumSwitch,jdbcType=BIT},
      </if>
      <if test="judgeRuleAscription != null" >
        judge_rule_ascription = #{judgeRuleAscription,jdbcType=INTEGER},
      </if>
      <if test="judgeRule != null" >
        judge_rule = #{judgeRule,jdbcType=INTEGER},
      </if>
      <if test="enquiryValidDurationTime != null" >
        enquiry_valid_duration_time = #{enquiryValidDurationTime,jdbcType=INTEGER},
      </if>
      <if test="outStockValidDurationTime != null" >
        out_stock_valid_duration_time = #{outStockValidDurationTime,jdbcType=INTEGER},
      </if>
      <if test="silentAllSwitch != null" >
        silent_all_switch = #{silentAllSwitch,jdbcType=BIT},
      </if>
      <if test="silentAllFollowUpTime != null" >
        silent_all_follow_up_time = #{silentAllFollowUpTime,jdbcType=INTEGER},
      </if>
      <if test="isBindSilentAllOrder != null" >
        is_bind_silent_all_order = #{isBindSilentAllOrder,jdbcType=BIT},
      </if>
      <if test="activeFollowUpSwitch != null" >
        active_follow_up_switch = #{activeFollowUpSwitch,jdbcType=BIT},
      </if>
      <if test="custFirstReplyDay != null" >
        cust_first_reply_day = #{custFirstReplyDay,jdbcType=INTEGER},
      </if>
      <if test="silentUrgepaySwitch != null" >
        silent_urgepay_switch = #{silentUrgepaySwitch,jdbcType=BIT},
      </if>
      <if test="silentUrgepayTime != null" >
        silent_urgepay_time = #{silentUrgepayTime,jdbcType=INTEGER},
      </if>
      <if test="orderFlagSwitch != null" >
        order_flag_switch = #{orderFlagSwitch,jdbcType=BIT},
      </if>
      <if test="orderFlag != null" >
        order_flag = #{orderFlag,jdbcType=BIGINT},
      </if>
      <if test="enquiryLossSwitch != null" >
        enquiry_loss_switch = #{enquiryLossSwitch,jdbcType=BIT},
      </if>
      <if test="minReplyNum != null" >
        min_reply_num = #{minReplyNum,jdbcType=INTEGER},
      </if>
      <if test="teamChatFilteSwitch != null" >
        team_chat_filte_switch = #{teamChatFilteSwitch,jdbcType=BIT},
      </if>
      <if test="sysCsChatFilteSwitch != null" >
        sys_cs_chat_filte_switch = #{sysCsChatFilteSwitch,jdbcType=BIT},
      </if>
      <if test="nonChatFilteSwitch != null" >
        non_chat_filte_switch = #{nonChatFilteSwitch,jdbcType=BIT},
      </if>
      <if test="csOfflineFilteCustMsgSwitch != null" >
        cs_offline_filte_cust_msg_switch = #{csOfflineFilteCustMsgSwitch,jdbcType=BIT},
      </if>
      <if test="csForwardSwitch != null" >
        cs_forward_switch = #{csForwardSwitch,jdbcType=BIT},
      </if>
      <if test="csForwardNum != null" >
        cs_forward_num = #{csForwardNum,jdbcType=INTEGER},
      </if>
      <if test="jixiaoTime != null" >
        jixiao_time = #{jixiaoTime,jdbcType=VARCHAR},
      </if>
      <if test="csWatchwordSwitch != null" >
        cs_watchword_switch = #{csWatchwordSwitch,jdbcType=BIT},
      </if>
      <if test="csWatchword != null" >
        cs_watchword = #{csWatchword,jdbcType=VARCHAR},
      </if>
      <if test="csWatchwordSendTimesNum != null" >
        cs_watchword_send_times_num = #{csWatchwordSendTimesNum,jdbcType=INTEGER},
      </if>
      <if test="mainAccountAutoReplySwitch != null" >
        main_account_auto_reply_switch = #{mainAccountAutoReplySwitch,jdbcType=BIT},
      </if>
      <if test="mainAccountAutoReplyContent != null" >
        main_account_auto_reply_content = #{mainAccountAutoReplyContent,jdbcType=VARCHAR},
      </if>
      <if test="csToCsutFirstLostSwitch != null" >
        cs_to_csut_first_lost_switch = #{csToCsutFirstLostSwitch,jdbcType=BIT},
      </if>
      <if test="custSigchatSwitch != null" >
        cust_sigchat_switch = #{custSigchatSwitch,jdbcType=BIT},
      </if>
      <if test="sigchatMinReplyNum != null" >
        sigchat_min_reply_num = #{sigchatMinReplyNum,jdbcType=INTEGER},
      </if>
      <if test="csSingleChatFilter != null" >
        cs_single_chat_filter = #{csSingleChatFilter,jdbcType=BIT},
      </if>
      <if test="platformCsFilteSwitch != null" >
        platform_cs_filte_switch = #{platformCsFilteSwitch,jdbcType=BIT},
      </if>
      <if test="custSingleChatNum != null" >
        cust_single_chat_num = #{custSingleChatNum,jdbcType=INTEGER},
      </if>
      <if test="custChatWordSwitch != null" >
        cust_chat_word_switch = #{custChatWordSwitch,jdbcType=BIT},
      </if>
      <if test="custWatchword != null" >
        cust_watchword = #{custWatchword,jdbcType=VARCHAR},
      </if>
      <if test="rtpfCsLoginSwitch != null" >
        rtpf_cs_login_switch = #{rtpfCsLoginSwitch,jdbcType=BIT},
      </if>
      <if test="dutyRidCsSwitch != null" >
        duty_rid_cs_switch = #{dutyRidCsSwitch,jdbcType=BIT},
      </if>
      <if test="rtpfRidCsSwitch != null" >
        rtpf_rid_cs_switch = #{rtpfRidCsSwitch,jdbcType=BIT},
      </if>
      <if test="rtpfRidCsRate != null" >
        rtpf_rid_cs_rate = #{rtpfRidCsRate,jdbcType=INTEGER},
      </if>
      <if test="csForwardAftersellSwitch != null" >
          cs_forward_aftersell_switch = #{csForwardAftersellSwitch,jdbcType=BIT},
      </if>
      <if test="poolConvertedNoPaySwitch != null" >
        pool_converted_no_pay_switch = #{poolConvertedNoPaySwitch,jdbcType=BIT},
      </if>
      <if test="taskCountUrgePaySwitch != null" >
        task_count_urge_pay_switch = #{taskCountUrgePaySwitch,jdbcType=BIT},
      </if>
         <if test="poolRidPayBuyerSwitch != null">
             pool_rid_pay_buyer_switch = #{poolRidPayBuyerSwitch,jdbcType=BIT},
         </if>
         <if test="poolUrgeAfterPaySwitch != null">
             pool_urge_after_pay_switch = #{poolUrgeAfterPaySwitch,jdbcType=BIT},
         </if>
         <if test="urgeLossFollowUpTime != null" >
             urge_loss_follow_up_time = #{urgeLossFollowUpTime,jdbcType=INTEGER},
         </if>
         <if test="leaveMessageSwitch != null">
             leave_message_switch = #{leaveMessageSwitch,jdbcType=BIT},
         </if>
    </set>
    	where shop_id = #{shopId,jdbcType=BIGINT}
	</update>
	
	<select id="getShopSystemsettingByShopId" parameterType="java.lang.Long"
		resultMap="ShopSystemsettingDTO">
		SELECT
			<include refid="base_field" />
		FROM pes_shop_systemsetting
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</select>
	
	<update id="updateWarnSetting" parameterType="map" >
	update pes_shop_systemsetting
	<set>
	  <if test="openWarn != null" >
        open_warn = #{openWarn,jdbcType=BIT},
      </if>
      <if test="warnAcceptCs != ''" >
        warn_accept_cs = #{warnAcceptCs,jdbcType=VARCHAR},
      </if>
      </set>
    	WHERE shop_id = #{shopId,jdbcType=BIGINT}
	</update>

	<update id="updateRtpfSettings" parameterType="map">
		update pes_shop_systemsetting
		<set>
			<if test="param.rtpfRidCsSwitch != null">
				rtpf_rid_cs_switch = #{param.rtpfRidCsSwitch,jdbcType=BIT},
			</if>
			<if test="param.rtpfRidCsRate != null">
				rtpf_rid_cs_rate = #{param.rtpfRidCsRate,jdbcType=INTEGER},
			</if>
			<if test="param.poolRidPayBuyerSwitch != null">
				pool_rid_pay_buyer_switch = #{param.poolRidPayBuyerSwitch,jdbcType=BIT},
			</if>
			<if test="param.poolUrgeAfterPaySwitch != null">
				pool_urge_after_pay_switch = #{param.poolUrgeAfterPaySwitch,jdbcType=BIT},
			</if>
            <if test="param.urgeLossFollowUpTime != null" >
                urge_loss_follow_up_time = #{param.urgeLossFollowUpTime,jdbcType=INTEGER}
            </if>
		</set>
		WHERE shop_id = #{shopId,jdbcType=BIGINT}
	</update>

	<select id="selectRealTimePerformanceSettings" resultMap="ShopSystemsettingDTO">
		SELECT
			shop_id,
			rtpf_rid_cs_switch,
			rtpf_rid_cs_rate,
			pool_rid_pay_buyer_switch,
			pool_urge_after_pay_switch,
            urge_loss_follow_up_time
		FROM pes_shop_systemsetting
		<where>
			shop_id = #{shopId}
		</where>
	</select>

	<select id="getShopSystemsetting" resultMap="ShopSystemsettingDTO">
        SELECT
                *
        FROM pes_shop_systemsetting
        WHERE
                shop_id = #{shop.shopId,jdbcType=BIGINT}
    </select>
</mapper>