<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.MarketingActivityMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.MarketingActivityDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="activity_content" jdbcType="VARCHAR" property="activityContent"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="enable_switch" jdbcType="BIT" property="enableSwitch"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, activity_name, activity_content, start_date, end_date, create_date, version, order_type, enable_switch,url
  </sql>

    <insert id="insert" parameterType="com.pes.jd.model.DO.MarketingActivityDO">
        insert into pes_marketing_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="activityName != null">
                activity_name,
            </if>
            <if test="activityContent != null">
                activity_content,
            </if>
            <if test="startDate != null">
                start_date,
            </if>
            <if test="endDate != null">
                end_date,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="orderType != null">
                order_type,
            </if>
            <if test="enableSwitch != null">
                enable_switch,
            </if>
             <if test="type != null">
                type,
            </if>
            <if test="url != null">
                url
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityName != null">
                #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="activityContent != null">
                #{activityContent,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                #{startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                #{endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=TINYINT},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=TINYINT},
            </if>
            <if test="enableSwitch != null">
                #{enableSwitch,jdbcType=BIT},
            </if>
			<if test="type != null">
               #{type,jdbcType=BIT},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_marketing_activity
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.MarketingActivityDO">
        update pes_marketing_activity
        <set>
            <if test="activityName != null">
                activity_name = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="activityContent != null">
                activity_content = #{activityContent,jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                start_date = #{startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                end_date = #{endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=TINYINT},
            </if>
            <if test="orderType != null">
                order_type = #{orderType,jdbcType=TINYINT},
            </if>
            <if test="enableSwitch != null">
                enable_switch = #{enableSwitch,jdbcType=BIT},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_marketing_activity
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectEnableActivity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_marketing_activity
        where enable_switch = 1  
        and type =  #{shopType}   
        order by create_date desc
    </select>

    <select id="selectActivityByActivityNameAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_marketing_activity
        where create_date between #{startDate} and #{endDate} 
        and type =  #{shopType}  
        <if test="activityName != null and activityName != ''">
            and activity_name like CONCAT('%', #{activityName},'%')
        </if>
        order by create_date desc
    </select>
</mapper>