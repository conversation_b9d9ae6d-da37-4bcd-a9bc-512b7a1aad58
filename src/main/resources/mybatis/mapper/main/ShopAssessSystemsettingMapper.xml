<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopAssessSystemsettingMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.ShopAssessSystemsettingDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="satisfied_rate_index" jdbcType="DOUBLE" property="satisfiedRateIndex"/>
        <result column="avg_resp_index" jdbcType="DOUBLE" property="avgRespIndex"/>
    </resultMap>
    <sql id="Base_Column_List">
            id, shop_id, satisfied_rate_index, avg_resp_index
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_shop_assess_systemsetting
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">

            delete from pes_shop_assess_systemsetting
            where id = #{id,jdbcType=BIGINT}

    </delete>
    <insert id="insert" parameterType="com.pes.jd.model.DO.ShopAssessSystemsettingDO">

            insert into pes_shop_assess_systemsetting (id, shop_id, satisfied_rate_index,
              avg_resp_index)
            values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{satisfiedRateIndex,jdbcType=DOUBLE},
              #{avgRespIndex,jdbcType=DOUBLE})

    </insert>
    <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopAssessSystemsettingDO">
        insert into pes_shop_assess_systemsetting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="satisfiedRateIndex != null">
                satisfied_rate_index,
            </if>
            <if test="avgRespIndex != null">
                avg_resp_index,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="satisfiedRateIndex != null">
                #{satisfiedRateIndex,jdbcType=DOUBLE},
            </if>
            <if test="avgRespIndex != null">
                #{avgRespIndex,jdbcType=DOUBLE},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopAssessSystemsettingDO">
        update pes_shop_assess_systemsetting
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="satisfiedRateIndex != null">
                satisfied_rate_index = #{satisfiedRateIndex,jdbcType=DOUBLE},
            </if>
            <if test="avgRespIndex != null">
                avg_resp_index = #{avgRespIndex,jdbcType=DOUBLE},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DTO.ShopAssessSystemsettingDTO">
    update pes_shop_assess_systemsetting
    set shop_id = #{shopId,jdbcType=BIGINT},
      satisfied_rate_index = #{satisfiedRateIndex,jdbcType=DOUBLE},
      avg_resp_index = #{avgRespIndex,jdbcType=DOUBLE}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="selectByShopId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        pes_shop_assess_systemsetting
        where
        shop_id=#{shopId,jdbcType=BIGINT}
        limit 0,1
    </select>
</mapper>