<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopSettingBatchRemindMapper">
    <resultMap id="ShopSettingBatchRemindDTO" type="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="is_remind" jdbcType="BIT" property="isRemind"/>
        <result column="silent_cs_nick" jdbcType="VARCHAR" property="silentCsNick"/>
        <result column="unpc_time" jdbcType="INTEGER" property="unpcTime"/>
        <result column="unpc_word_id" jdbcType="VARCHAR" property="unpcWordId"/>
        <result column="bargain_remind_dot" jdbcType="INTEGER" property="bargainRemindDot"/>
        <result column="bargain_remind_word_id" jdbcType="VARCHAR" property="bargainRemindWordId"/>
        <result column="balance_remind_dot" jdbcType="INTEGER" property="balanceRemindDot"/>
        <result column="balance_remind_word_id" jdbcType="VARCHAR" property="balanceRemindWordId"/>
        <result column="remind_start_dot" jdbcType="INTEGER" property="remindStartDot"/>
        <result column="remind_end_dot" jdbcType="INTEGER" property="remindEndDot"/>
        <result column="silent_flag" jdbcType="INTEGER" property="silentFlag"/>
        <result column="silent_group_id" jdbcType="BIGINT" property="silentGroupId"/>
        <result column="silent_spare_cs_nick" jdbcType="VARCHAR" property="silentSpareCsNick"/>
        <result column="silent_spare_group_id" jdbcType="BIGINT" property="silentSpareGroupId"/>
        <result column="created" jdbcType="TIMESTAMP" property="created" />
    </resultMap>
    <sql id="base_field">
        id, shop_id, is_remind, silent_cs_nick, unpc_time, unpc_word_id, bargain_remind_dot,
        bargain_remind_word_id, balance_remind_dot, balance_remind_word_id, remind_start_dot,
        remind_end_dot,silent_group_id,silent_flag,silent_spare_cs_nick,silent_spare_group_id
    </sql>
    <insert id="insert" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO">
        INSERT INTO pes_shop_setting_batch_remind (shop_id, is_remind,
            silent_cs_nick, unpc_time, unpc_word_id,
            bargain_remind_dot, bargain_remind_word_id, balance_remind_dot,
            balance_remind_word_id, remind_start_dot, remind_end_dot,
            silent_group_id,silent_flag,silent_spare_cs_nick,silent_spare_group_id,created,modified
        )
        VALUES (#{shopId,jdbcType=BIGINT}, #{isRemind,jdbcType=BIT},
            #{silentCsNick,jdbcType=VARCHAR}, #{unpcTime,jdbcType=INTEGER}, #{unpcWordId,jdbcType=VARCHAR},
            #{bargainRemindDot,jdbcType=INTEGER}, #{bargainRemindWordId,jdbcType=VARCHAR}, #{balanceRemindDot,jdbcType=INTEGER},
            #{balanceRemindWordId,jdbcType=VARCHAR}, #{remindStartDot,jdbcType=INTEGER}, #{remindEndDot,jdbcType=INTEGER},
            #{silentGroupId,jdbcType=BIGINT},#{silentFlag,jdbcType=INTEGER}, #{silentSpareCsNick,jdbcType=VARCHAR},#{silentSpareGroupId,jdbcType=BIGINT}
        ,#{created,jdbcType=TIMESTAMP},#{modified,jdbcType=TIMESTAMP}
        )
  </insert>
    <update id="updateByShopId" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindDTO">
        UPDATE pes_shop_setting_batch_remind
        <set>
            <if test="isRemind != null">
                is_remind = #{isRemind,jdbcType=BIT},
            </if>
            <if test="silentCsNick != null">
                silent_cs_nick = #{silentCsNick,jdbcType=VARCHAR},
            </if>
            <if test="unpcTime != null">
                unpc_time = #{unpcTime,jdbcType=INTEGER},
            </if>
            unpc_word_id = #{unpcWordId,jdbcType=VARCHAR},
            <if test="bargainRemindDot != null">
                bargain_remind_dot = #{bargainRemindDot,jdbcType=INTEGER},
            </if>
            <if test="bargainRemindWordId != null and bargainRemindWordId!=''">
                bargain_remind_word_id = #{bargainRemindWordId,jdbcType=VARCHAR},
            </if>
            <if test="balanceRemindDot != null">
                balance_remind_dot = #{balanceRemindDot,jdbcType=INTEGER},
            </if>
            <if test="balanceRemindWordId != null and balanceRemindWordId!=''">
                balance_remind_word_id = #{balanceRemindWordId,jdbcType=VARCHAR},
            </if>
            <if test="remindStartDot != null">
                remind_start_dot = #{remindStartDot,jdbcType=INTEGER},
            </if>
            <if test="remindEndDot != null">
                remind_end_dot = #{remindEndDot,jdbcType=INTEGER},
            </if>
            <if test="silentFlag != null">
                silent_flag = #{silentFlag,jdbcType=INTEGER},
            </if>
            <if test="silentGroupId != null">
                silent_group_id = #{silentGroupId,jdbcType=BIGINT},
            </if>
            <if test="silentSpareGroupId != null">
                silent_spare_group_id = #{silentSpareGroupId,jdbcType=BIGINT},
            </if>
            <if test="silentSpareCsNick != null">
                silent_spare_cs_nick = #{silentSpareCsNick,jdbcType=VARCHAR},
            </if>
            <if test="modified != null">
                modified = #{modified,jdbcType=TIMESTAMP}
            </if>
        </set>
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
    </update>

    <update id="updateCloseRemindByShopIds" parameterType="long">
        UPDATE pes_shop_setting_batch_remind SET is_remind = 0 WHERE shop_id IN
        <foreach collection="shopIds" item="shopId" open="(" close=")" separator=",">
            #{shopId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateEndDotFor618">
        UPDATE
            pes_shop_setting_batch_remind
        SET
            remind_end_dot = #{endDot,jdbcType=INTEGER}
        WHERE
            remind_end_dot = #{whereEnd,jdbcType=INTEGER}
    </update>
    <update id="updateEndDotForDeflult">
        UPDATE
            pes_shop_setting_batch_remind
        SET
            remind_end_dot = #{endDot,jdbcType=INTEGER}
        WHERE
            remind_end_dot > #{whereEnd,jdbcType=INTEGER}
    </update>
    <update id="updateIsremindByShopId">
        UPDATE
            pes_shop_setting_batch_remind
        SET
            is_remind=#{isRemind}
        WHERE
            shop_id=#{shopId}
    </update>

    <select id="selectByshopId" parameterType="java.lang.Long" resultMap="ShopSettingBatchRemindDTO">
        SELECT
        <include refid="base_field"/>
        FROM pes_shop_setting_batch_remind
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
    </select>
    <select id="selectSettingByShopIds" resultMap="ShopSettingBatchRemindDTO">
        select * from pes_shop_setting_batch_remind where shop_id in
        <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
            #{shopId}
        </foreach>
    </select>

    <select id="selectByShopId" parameterType="java.lang.Long" resultMap="ShopSettingBatchRemindDTO">
        SELECT
        <include refid="base_field"/>
        FROM pes_shop_setting_batch_remind
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
    </select>

    <select id="selectShopSettingBatchRemindByShopId" resultMap="ShopSettingBatchRemindDTO">
        SELECT
        is_remind
        FROM pes_shop_setting_batch_remind
        WHERE shop_id = #{shopId}
    </select>
</mapper>