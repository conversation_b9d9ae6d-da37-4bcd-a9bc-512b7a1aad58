<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopUrgeMapper" >
  <resultMap id="ShopUrge" type="com.pes.jd.ms.domain.Data.master.ShopUrge" >
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="title" property="shopName" jdbcType="VARCHAR" />
    <result column="db" property="db" jdbcType="VARCHAR" />
    <result column="schema_id" property="schemaId" jdbcType="VARCHAR" />
    <result column="rt_db" property="rtDb" jdbcType="VARCHAR" />
    <result column="rt_schema_id" property="rtSchemaId" jdbcType="VARCHAR" />


  </resultMap>
  <sql id="base_flied" >
    id, shop_id, closure_switch
  </sql>
  <select id="selectUrgeShopByShopIdAndType" resultMap="ShopUrge"  >
    select 
      ps.shop_id,
      ps.title,
      ps.schema_id,
      ps.db,
      ps.rt_schema_id,
      ps.rt_db
    from pes_urge_shop pus
    inner join pes_shop ps
    on ps.shop_id=pus.shop_id
    <where>
      pus.closure_switch=1
      <if test="shopId!=null">
        AND  pus.shop_id=#{shopId}
      </if>
      <if test="type!=null">
        AND  ps.type=#{type}
      </if>
      
    </where>
  </select>

  <select id="selectUrgeShopByShopId" resultType="com.pes.jd.model.DTO.ShopUrgeDTO">
    select
    shop_id, closure_switch
    from pes_urge_shop

    <where>
      shop_id=#{shopId}
      and closure_switch=1
    </where>
  </select>
</mapper>