<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopPopMapper">

	<select id="selectAllPopShopByPopName" resultType="com.pes.jd.model.DTO.ShopPopDTO">
		SELECT 
			pop.shop_id, pop.pop_name,s.title shopName,pop.erp_id
		FROM pes_shop_pop pop 
		LEFT JOIN pes_shop s
		ON pop.shop_id = s.shop_id
		<where>
			<if test="popName!=null and popName!=''">
				pop.pop_name like concat('%',#{popName},'%')  
			</if>
			
			<if test="shopId!=null and shopId!=''">
				pop.shop_id like concat('%',#{shopId},'%')
			</if>
			<if test="shopName!=null and shopName!=''">
				s.title like concat('%',#{shopName},'%') 
			</if>
			and pop.status = 1
			and s.type = #{shopType}
		</where>
	</select>


	<select id="selectAllPopShopByShopType" resultType="com.pes.jd.model.DTO.ShopPopDTO">
		SELECT
		s.shop_id, s.title shopName
		FROM  pes_shop s
		<where>
			<if test="shopId!=null and shopId!=''">
				s.shop_id like concat('%',#{shopId},'%')
			</if>
			<if test="shopName!=null and shopName!=''">
				s.title like concat('%',#{shopName},'%')
			</if>
			and s.type = #{shopType}
		</where>
	</select>



	<select id="selectShopIdByErpId" resultType="Long">
		SELECT shop_id
		  FROM pes_shop_pop pop
		 WHERE erp_id in
		<foreach collection="erpIds" item="erpId" open="(" close=")" separator=",">
			#{erpId}
		</foreach>

	</select>
	<select id="selectShopIdByErpIdAndRtDb" resultType="java.lang.Long">
		SELECT pop.shop_id
		FROM pes_shop_pop pop,pes_shop shop
		WHERE pop.shop_id=shop.shop_id
			AND pop.erp_id in
		<foreach collection="erpIds" item="erpId" open="(" close=")" separator=",">
			#{erpId}
		</foreach>
			AND shop.rt_db=#{rtDb}
	</select>
</mapper>