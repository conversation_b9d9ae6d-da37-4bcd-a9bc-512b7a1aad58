<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.CustomReportMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CustomReportDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>
    <resultMap id="CustomReportDTO" type="com.pes.jd.model.DTO.CustomReportDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, shop_id, name, type, `desc`,`status`
  </sql>
    <select id="searchByType" resultMap="CustomReportDTO" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from pes_custom_report
        where status >0
        <if test="type != null">
            and type = #{type}
        </if>
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_custom_report
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_custom_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByPrimaryKeyWithProperty" parameterType="java.lang.Long">
    delete from pes_custom_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.pes.jd.model.DO.CustomReportDO" useGeneratedKeys="true" keyProperty="id">
    insert into pes_custom_report (id, shop_id, name, 
      type, `desc`, `status`)
    values (null, #{shopId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR},
      #{type,jdbcType=TINYINT}, #{desc,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT})
  </insert>
    <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CustomReportDO">
        insert into pes_custom_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="desc != null">
                desc,
            </if>
            <if test="status != null">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
            <if test="desc != null">
                #{desc,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CustomReportDO">
        update pes_custom_report
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="desc != null">
                desc = #{desc,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CustomReportDO">
    update pes_custom_report
    set shop_id = #{shopId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      `desc` = #{desc,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="checkPredefine" resultType="int">
    select count(1) from pes_custom_report pr,pes_custom_report_property pc
    where shop_id = #{shopId} and pr.status = 2 and name = #{name} and pc.custom_report_id = pr.id
  </select>
    <delete id="deleteByNameStatus">
    DELETE pcrp, pcr FROM
    pes_custom_report_property pcrp
    INNER JOIN
    pes_custom_report pcr
    ON pcrp.custom_report_id = pcr.id
    AND pcr.shop_id = #{shopId}
    AND pcr.status = #{status}
    AND pcr.name = #{name};
  </delete>

    <select id="searchReportIdByReportName" resultType="java.lang.Long">
   SELECT id FROM `pes_custom_report` WHERE name=#{reportName}
  </select>

    <select id="selectByShopAndName" resultMap="CustomReportDTO">
   SELECT id FROM `pes_custom_report` WHERE shop_id = #{shopId,jdbcType=BIGINT} AND name = #{name,jdbcType=VARCHAR}
  </select>

</mapper>