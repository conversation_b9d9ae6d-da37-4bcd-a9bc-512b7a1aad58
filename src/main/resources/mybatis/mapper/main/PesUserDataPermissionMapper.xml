<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesUserServicePermissionMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesUserServicePermission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="service_permission_id" jdbcType="BIGINT" property="dataPermissionId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, service_permission_id, cs_nick
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_user_service_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByShopAccounts" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from pes_user_service_permission
        <if test="list!=null and list.size()>0">
          where cs_nick in
          <foreach collection="list" item="account" open="(" close=")" separator=",">
            #{account.nick}
          </foreach>
        </if>
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_user_service_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByShopAccounts">
    delete from pes_user_service_permission
    where cs_nick in 
    <foreach collection="list" item="account" open="(" close=")" separator=",">
      #{account.nick}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.PesUserServicePermission">
    insert into pes_user_service_permission (id, user_id, service_permission_id,
      cs_nick)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{dataPermissionId,jdbcType=BIGINT},
      #{csNick,jdbcType=VARCHAR})
  </insert>
  <insert id="insertByShopAccountsAndPermissions">
    <foreach collection="shopAccounts" item="shopAccount">
      <foreach collection="permissions" item="permission">
        insert into pes_user_service_permission (id, user_id, service_permission_id,
        cs_nick)
        values (null, null , #{permission.id,jdbcType=BIGINT},
        #{shopAccount.nick,jdbcType=VARCHAR});
      </foreach>
    </foreach>
  </insert>

  <insert id="batchInsertByShopAccountsAndPermissions">
    insert into pes_user_service_permission
        (id, user_id, service_permission_id, cs_nick)
    VALUES
    <foreach collection="permissions" item="itm" separator=",">
      (
       null,
       null,
      #{itm.id,jdbcType=BIGINT},
      #{itm.name,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.PesUserServicePermission">
    insert into pes_user_service_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="dataPermissionId != null">
        service_permission_id,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="dataPermissionId != null">
        #{dataPermissionId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.PesUserServicePermission">
    update pes_user_service_permission
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="dataPermissionId != null">
        service_permission_id = #{dataPermissionId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.PesUserServicePermission">
    update pes_user_service_permission
    set user_id = #{userId,jdbcType=BIGINT},
      service_permission_id = #{dataPermissionId,jdbcType=BIGINT},
      cs_nick = #{csNick,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <delete id="deleteUserServicePermissionByCsNick" parameterType="map">
  	delete  from pes_user_service_permission where cs_nick=#{csNick}
  </delete>
</mapper>