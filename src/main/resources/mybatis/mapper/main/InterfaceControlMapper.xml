<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.InterfaceControlMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.InterfaceControlDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="inf_type" jdbcType="TINYINT" property="infType" />
    <result column="switch_flag" jdbcType="BIT" property="switchFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, inf_type, switch_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_interface_control_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_interface_control_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.InterfaceControlDO">
    insert into pes_interface_control_info (id, inf_type, switch_flag
      )
    values (#{id,jdbcType=BIGINT}, #{infType,jdbcType=TINYINT}, #{switchFlag,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.InterfaceControlDO">
    insert into pes_interface_control_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="infType != null">
        inf_type,
      </if>
      <if test="switchFlag != null">
        switch_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="infType != null">
        #{infType,jdbcType=TINYINT},
      </if>
      <if test="switchFlag != null">
        #{switchFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.InterfaceControlDO">
    update pes_interface_control_info
    <set>
      <if test="infType != null">
        inf_type = #{infType,jdbcType=TINYINT},
      </if>
      <if test="switchFlag != null">
        switch_flag = #{switchFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.InterfaceControlDO">
    update pes_interface_control_info
    set inf_type = #{infType,jdbcType=TINYINT},
      switch_flag = #{switchFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_interface_control_info
  </select>
</mapper>