<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopSystemsettingFieidLimitMapper">

    <resultMap id="ShopSystemsettingFieidLimitDTO" type="com.pes.jd.model.DTO.ShopSystemsettingFieidLimitDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="enquiry_valid_duration_time" property="enquiryValidDurationTime" jdbcType="INTEGER"/>
        <result column="out_stock_valid_duration_time" property="outStockValidDurationTime" jdbcType="INTEGER"/>
        <result column="modify_enquiry_out_stock" property="modifyEnquiryOutStock" jdbcType="DATE"/>
    </resultMap>

    <sql id="base_field">
    id, shop_id, enquiry_valid_duration_time, out_stock_valid_duration_time, modify_enquiry_out_stock
  </sql>
    <insert id="insert" parameterType="com.pes.jd.model.DTO.ShopSystemsettingFieidLimitDTO">
    insert into pes_shop_systemsetting_fieid_limit (shop_id, enquiry_valid_duration_time,
      out_stock_valid_duration_time, modify_enquiry_out_stock
      )
    values (#{shopId,jdbcType=BIGINT}, #{enquiryValidDurationTime,jdbcType=INTEGER},
      #{outStockValidDurationTime,jdbcType=INTEGER}, #{modifyEnquiryOutStock,jdbcType=DATE}
      )
  </insert>

    <update id="updateEnquiryAndOutStockValidDurationTimeByShopId" parameterType="com.pes.jd.model.DTO.ShopSystemsettingFieidLimitDTO">
        update pes_shop_systemsetting_fieid_limit
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="enquiryValidDurationTime != null">
                enquiry_valid_duration_time = #{enquiryValidDurationTime,jdbcType=INTEGER},
            </if>
            <if test="outStockValidDurationTime != null">
                out_stock_valid_duration_time = #{outStockValidDurationTime,jdbcType=INTEGER},
            </if>
            <if test="modifyEnquiryOutStock != null">
                modify_enquiry_out_stock = #{modifyEnquiryOutStock,jdbcType=DATE},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} AND shop_id = #{shopId,jdbcType=BIGINT}
    </update>


    <select id="selectByShopId" resultType="com.pes.jd.model.DTO.ShopSystemsettingFieidLimitDTO">
        select
        <include refid="base_field"/>
        from pes_shop_systemsetting_fieid_limit
        where shop_id = #{shopId,jdbcType=BIGINT}
    </select>
</mapper>