<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.RealtimeShopMapper" >
  <resultMap id="RealtimeShopDTO" type="com.pes.jd.model.DTO.RealtimeShopDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="previous_start_time" property="previousStartTime" jdbcType="TIMESTAMP" />
    <result column="previous_end_time" property="previousEndTime" jdbcType="TIMESTAMP" />
    <result column="last_consumed_time" property="lastConsumedTime" jdbcType="BIGINT" />
    <result column="get_data_success_time" property="getDataSuccessTime" jdbcType="TIMESTAMP" />
    <result column="pull_status" property="pullStatus" jdbcType="INTEGER" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, previous_start_time, previous_end_time, last_consumed_time, get_data_success_time, 
    pull_status
  </sql>
  
  <select id="getRealTimeShopByShopId" resultMap="RealtimeShopDTO">
  	select 
	  	shop_id, 
	  	previous_start_time, 
	  	previous_end_time, 
	  	last_consumed_time, 
	  	get_data_success_time, 
	    pull_status
	 from pes_realtime_shop
	 where shop_id=#{shopId}
  </select>
</mapper>