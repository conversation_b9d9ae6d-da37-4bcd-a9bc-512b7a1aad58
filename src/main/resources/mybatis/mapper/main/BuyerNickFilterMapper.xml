<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.BuyerNickFilterMapper">

  <resultMap id="BuyerNickFilterDTO" type="com.pes.jd.model.DTO.BuyerNickFilterDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created" jdbcType="DATE" property="created" />
  </resultMap>
  
  <sql id="base_field">
    shop_id,buyer_nick
  </sql>
  
  <insert id="insertBuyernickFilter" parameterType="com.pes.jd.model.DTO.BuyerNickFilterDTO">
    INSERT INTO pes_buyernick_filter (id, shop_id, buyer_nick, 
      created_by, created)
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{buyerNick,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{created,jdbcType=DATE})
  </insert>
  
  <insert id="insertBatchBuyernickFilter" parameterType="map">
    INSERT INTO pes_buyernick_filter (shop_id, buyer_nick, 
      created_by, created)
    VALUES 
    <foreach collection="filterBuyerNickLst" item="filter" separator=",">
    (#{filter.shopId,jdbcType=BIGINT}, #{filter.buyerNick,jdbcType=VARCHAR}, 
      #{filter.createdBy,jdbcType=VARCHAR}, #{filter.created,jdbcType=DATE})
    </foreach>
  </insert>
 
  <delete id="deleteBuyernickFilterByShopId" parameterType="java.lang.Long">
    DELETE FROM pes_buyernick_filter
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
  </delete>
  
  <update id="updateBuyernickFilterById" parameterType="com.pes.jd.model.DTO.BuyerNickFilterDTO">
    UPDATE pes_buyernick_filter
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="buyerNick != null">
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=DATE},
      </if>
    </set>
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectBuyernickFilterById" parameterType="java.lang.Long" resultMap="BuyerNickFilterDTO">
    SELECT 
   		<include refid="base_field" />
    FROM pes_buyernick_filter
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectFilterBuyerNickLstByShopId" parameterType="java.lang.Long" resultType="string">
    SELECT buyer_nick
    FROM pes_buyernick_filter
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
  </select>
  
   <delete id="deleteByNickAndShopId" parameterType="java.util.Map" >
    delete from pes_buyernick_filter
    where buyer_nick = #{buyerNick,jdbcType=VARCHAR} and shop_id = #{shopId,jdbcType=BIGINT}
  </delete>
</mapper>