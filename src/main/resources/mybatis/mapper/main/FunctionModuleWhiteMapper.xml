<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.FunctionModuleWhiteMapper">
    <resultMap id="FunctionModuleWhiteDO" type="com.pes.jd.model.DO.FunctionModuleWhiteDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="function_id" jdbcType="BIGINT" property="functionId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
    </resultMap>

    <resultMap id="FunctionModuleWhiteDTO" type="com.pes.jd.model.DTO.FunctionModuleWhiteDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="title" jdbcType="VARCHAR" property="shopTitle"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
        <result column="function" jdbcType="VARCHAR" property="function"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>


    <sql id="base_field">
    id, function_id, shop_id, created
  </sql>

    <select id="selectFunctionWhiteShopIdForLst" parameterType="java.lang.Long" resultMap="FunctionModuleWhiteDTO">
    SELECT
    fmw.id as id ,fmw.created AS created, s.title AS title ,swfm.`desc` AS `desc` ,
     swfm.function AS function, swfm.status AS status ,s.shop_id AS shop_id
    FROM
    pes_function_module_white AS fmw
    LEFT JOIN
    pes_shop_wip_function_module AS swfm ON fmw.function_id=swfm.id
    LEFT JOIN
    pes_shop AS s ON fmw.shop_id=s.shop_id
    WHERE
    fmw.shop_id=#{shopId}
    ORDER BY
    fmw.created DESC , s.title DESC
  </select>

    <select id="selectFunctionWhiteForLst" resultMap="FunctionModuleWhiteDTO">
    SELECT
    fmw.id AS id ,fmw.created AS created , s.title AS title ,swfm.`desc` AS `desc` ,s.shop_id AS shop_id
    FROM
    pes_function_module_white AS fmw
    LEFT JOIN
    pes_shop_wip_function_module AS swfm ON fmw.function_id=swfm.id
    LEFT JOIN
    pes_shop AS s ON fmw.shop_id=s.shop_id
    WHERE
    s.type=#{type}
    ORDER BY
    fmw.created DESC , s.title DESC
  </select>

    <select id="searchFunctionWhiteAddShopForLst" resultMap="FunctionModuleWhiteDTO">
        SELECT
            fmw.id AS id ,fmw.shop_id AS shop_id,title AS title
        FROM
            pes_function_module_white AS fmw
        LEFT JOIN
            pes_shop AS ps ON ps.shop_id=fmw.shop_id
        WHERE
             ps.type=#{type}
            group by ps.shop_id
           <!-- AND ps.`status`='active'-->
  </select>


    <select id="searchFunctionByIdForLst" parameterType="list" resultType="string">
        SELECT function FROM pes_shop_wip_function_module WHERE id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>

    </select>


    <select id="searchFunctionByShopIdAndFunctionId" resultType="long">

        SELECT id FROM pes_function_module_white
        WHERE shop_id=#{shopId}
        AND function_id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

    <select id="searchFunctionWhiteDisplayByShopId" resultType="java.lang.Long">
       SELECT COUNT(1) FROM pes_function_module_white
        WHERE
        shop_id=#{shopId}
    </select>

    <select id="searchFunctionWhiteIdByShopId" parameterType="java.lang.Long" resultType="java.lang.Long">
        SELECT function_Id FROM pes_function_module_white
        WHERE
        shop_id=#{shopId}
    </select>

    <update id="updateFunctionDateByShopId">
        UPDATE pes_function_module_white SET created=#{date}
        WHERE shop_id=#{shopId}
    </update>


    <delete id="batchDeleteFunctionModuleWhite" parameterType="java.util.List">
        delete from pes_function_module_white
        WHERE id
        IN
        <foreach collection="list" item="id" index="no" open="("
                 separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insertFunctionModuleWhiteForLst" parameterType="java.util.List">
           INSERT INTO pes_function_module_white(id, function_id, shop_id, created)
           VALUES
           <foreach collection="list" item="itm" separator=",">
               (#{itm.id},#{itm.functionId},#{itm.shopId},#{itm.created})
           </foreach>
    </insert>

    <select id="selectFunctionWhiteByIds" resultMap="FunctionModuleWhiteDTO">
        SELECT
        fmw.shop_id as shop_id ,swfm.`desc` AS `desc` ,swfm.function AS function, swfm.status AS status
        FROM
        pes_function_module_white AS fmw
        LEFT JOIN
        pes_shop_wip_function_module AS swfm ON fmw.function_id=swfm.id
        WHERE
        fmw.id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="searchFunctionDisplayShopIds" resultType="long">
        select shop_id FROM pes_function_module_white  WHERE  shop_id IN
        <foreach collection="shopIds" item="id" open="(" close=")" separator=",">
          #{id}
        </foreach>

    </select>


</mapper>