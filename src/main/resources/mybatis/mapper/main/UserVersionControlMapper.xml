<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.UserVersionControlMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.UserVersionControlDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_version_id" jdbcType="BIGINT" property="appVersionId" />
    <result column="pes_user_id" jdbcType="BIGINT" property="pesUserId" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>

  <insert id="insertUserVersionControl" parameterType="com.pes.jd.model.DO.UserVersionControlDO">
    INSERT INTO
    pes_user_version_control(id,app_version_id,pes_user_id,`status`)
    VALUES(#{record.id},#{record.appVersionId},#{record.pesUserId},#{record.status})
  </insert>


  <select id="selctUserAppVersionByUserId" resultType="java.lang.Long">
    SELECT app_version_id
     FROM
     pes_user_version_control
     WHERE pes_user_id=#{userId}
  </select>




</mapper>