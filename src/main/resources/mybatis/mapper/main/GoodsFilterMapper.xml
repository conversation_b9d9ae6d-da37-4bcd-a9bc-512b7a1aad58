<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.GoodsFilterMapper">
	<resultMap id="GoodsFilterDO"
		type="com.pes.jd.model.DO.GoodsFilter">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="num_iid" jdbcType="BIGINT" property="numIid" />
		<result column="goods_url" jdbcType="VARCHAR" property="goodsUrl" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="type" jdbcType="VARCHAR" property="type" />
		<result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
	</resultMap>

	<sql id="base_field">
		id, shop_id, num_iid, goods_url, created, name, type
	</sql>

	<insert id="insertGoodsFilter"
		parameterType="com.pes.jd.model.DO.GoodsFilter">
		INSERT INTO pes_goods_filter (shop_id, num_iid, goods_url, created, name,
		type)
		VALUES
		(
		#{shopId,jdbcType=BIGINT}, #{numIid,jdbcType=BIGINT},
		#{goodsUrl,jdbcType=VARCHAR}, #{created,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR},
		#{type,jdbcType=VARCHAR}
		)
	</insert>

	<delete id="deleteGoodsFilterById"
		parameterType="java.lang.Long">
		DELETE FROM pes_goods_filter
		WHERE
		id = #{id,jdbcType=BIGINT}
	</delete>

	<update id="updateGoodsFilterBySelective"
		parameterType="com.pes.jd.model.DO.GoodsFilter">
		UPDATE pes_goods_filter
		<set>
			<if test="shopId != null">
				shop_id = #{shopId,jdbcType=BIGINT},
			</if>
			<if test="numIid != null">
				num_iid = #{numIid,jdbcType=BIGINT},
			</if>
			<if test="goodsUrl != null">
				goods_url = #{goodsUrl,jdbcType=VARCHAR},
			</if>
			<if test="created != null">
				created = #{created,jdbcType=TIMESTAMP},
			</if>
			<if test="name != null">
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT}
	</update>

	
     <insert id="insertPesGoodsFilter" parameterType="com.pes.jd.model.DTO.GoodsFilterDTO">
		insert into
		pes_goods_filter(shop_id,num_iid,goods_url,created,name,end_time, ware_id)
		values
		<foreach collection="list" item="itm" separator=","
			index="index">
			(#{itm.shopId},#{itm.numIid},#{itm.goodsUrl},#{itm.created},#{itm.name},#{itm.endTime},#{itm.wareId})
		</foreach>
	</insert>

	<!-- 查询goods -->
	<select id="queryPesGoodsFilterByShopId" parameterType="long"
		resultType="com.pes.jd.model.DTO.GoodsFilterDTO">
		SELECT pgf.id as id, pgf.shop_id as shopId, pgf.num_iid as
		numIid, pgf.goods_url as goodsUrl,
		pgf.type as type, pgf.created as created, pgf.name as name,
		pgf.article_number as articleNumber, pgf.end_time as endTime,
		pgf.ware_id as wareId
		FROM
		pes_goods_filter pgf
		WHERE
		shop_id = #{shopId}
		ORDER BY
		created desc
	</select>

	<!-- 查询已存在的goods -->
	<select id="queryExistedPesGoodsFilter"
		parameterType="java.util.Map"
		resultType="com.pes.jd.model.DTO.GoodsFilterDTO">
		SELECT pgf.id as id, pgf.shop_id as shopId, pgf.num_iid as numIid,
		pgf.goods_url as goodsUrl ,
		pgf.type as type, pgf.created as created, pgf.name as name,
		pgf.article_number as articleNumber
		FROM
		pes_goods_filter pgf
		WHERE
		shop_id = #{shopId}
		AND
		goods_url in
		<foreach item="item" collection="goodsUrl" open="("
			separator="," close=")">
			#{item}
		</foreach>
	</select>


	<!-- 删除goods -->
	<delete id="deletePesGoodsFilter" parameterType="java.util.Map">
		DELETE FROM
		pes_goods_filter
		WHERE
		shop_id = #{shopId}
		AND
		num_iid in	
		<foreach item="item" collection="goodsIdList" open="("
			separator="," close=")">
			#{item}
		</foreach>
	</delete>


	<!-- 查询已存在的goods通过shopId,numiid -->
	<select id="getGoodsFilterByShopIdAndNumIid" parameterType="map"
		resultType="com.pes.jd.model.DTO.GoodsFilterDTO">
		SELECT pgf.id as id, pgf.shop_id as shopId, pgf.num_iid as
		numIid, pgf.goods_url as goodsUrl,
		pgf.type as type, pgf.created as created, pgf.name as name,
		pgf.article_number as articleNumber
		FROM
		pes_goods_filter pgf
		WHERE
		shop_id = #{shopId}
		AND num_iid = #{numIid}
	</select>

	<select id="selectPesGoodsByShopId" resultType="com.pes.jd.model.DTO.GoodsFilterDTO">
		SELECT pgf.id as id, pgf.shop_id as shopId, pgf.num_iid as
						 numIid, pgf.goods_url as goodsUrl,
			   pgf.type as type, pgf.created as created, pgf.name as name,
			   pgf.article_number as articleNumber, pgf.end_time as endTime,
			   pgf.ware_id as wareId
		FROM
				pes_goods_filter pgf
		WHERE
				shop_id = #{shopId}
		ORDER BY
				created desc
    </select>
</mapper>