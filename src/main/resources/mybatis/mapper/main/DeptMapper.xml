<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.DeptMapper">

    <resultMap id="DeptDTO" type="com.pes.jd.model.DTO.DeptUserDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    </resultMap>

    <resultMap id="pesDeptDTO" type="com.pes.jd.model.DTO.DeptDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="password" jdbcType="VARCHAR" property="password" />
    </resultMap>

    <resultMap id="Dept01DTO" type="com.pes.jd.model.DTO.Dept01DTO">
        <id column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    </resultMap>

    <resultMap id="Dept02DTO" type="com.pes.jd.model.DTO.Dept02DTO">
        <id column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    </resultMap>

    <resultMap id="Dept03DTO" type="com.pes.jd.model.DTO.Dept03DTO">
        <id column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    </resultMap>

    <resultMap id="Dept04DTO" type="com.pes.jd.model.DTO.Dept04DTO">
        <id column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="erp_id" jdbcType="VARCHAR" property="erpId" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    </resultMap>

    <resultMap id="Dept00DTO" type="com.pes.jd.model.DTO.Dept00DTO">
        <id column="dept_id" jdbcType="BIGINT" property="deptId" />
        <result column="type" jdbcType="VARCHAR" property="type" />
        <result column="parent_id" jdbcType="BIGINT" property="parentId" />
        <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    </resultMap>

    <resultMap id="DeptFilerDTO" type="com.pes.jd.model.DTO.DeptFilerDTO">
        <id column="filer_id" jdbcType="BIGINT" property="filerId" />
        <result column="filer_name" jdbcType="VARCHAR" property="filerName" />
        <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
        <result column="status" jdbcType="VARCHAR" property="status" />
    </resultMap>

    <resultMap id="DeptShopVO" type="com.pes.jd.model.VO.DeptShopVO">
        <id column="shop_id" jdbcType="BIGINT" property="shopId" />
        <id column="title" jdbcType="VARCHAR" property="shopName" />
        <result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
        <result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
    </resultMap>

    <select id="getDeptByUserName" parameterType="java.lang.String" resultMap="DeptDTO">
        SELECT id,type,username,password,dept_id
        FROM pes_dept_user
        WHERE
        username = #{userName}
        LIMIT 1
    </select>

    <select id="listDept01ByDeptName" parameterType="java.lang.String" resultMap="Dept01DTO">
        SELECT dept_id,dept_name,type,parent_id
          FROM  pes_dept_01
         WHERE  dept_name = #{deptName}
    </select>

    <select id="listDept02ByDeptName" parameterType="java.lang.String" resultMap="Dept02DTO">
        SELECT dept_id,dept_name,type,parent_id
        FROM  pes_dept_02
        WHERE  dept_name = #{deptName}
    </select>

    <select id="listDept03ByDeptName" parameterType="java.lang.String" resultMap="Dept03DTO">
        SELECT dept_id,dept_name,type,parent_id
        FROM  pes_dept_03
        WHERE  dept_name = #{deptName}
    </select>

    <select id="listDept04ByDeptName" parameterType="java.lang.String" resultMap="Dept04DTO">
        SELECT dept_id,erp_id,type,parent_id
        FROM  pes_dept_04
        WHERE  erp_id = #{erpId}
    </select>

    <select id="listDept01ByParentId" parameterType="java.lang.Long" resultMap="Dept01DTO">
        SELECT dept_id,dept_name,type,parent_id
        FROM  pes_dept_01
        WHERE  parent_id = #{deptId}
    </select>

    <select id="listDept02ByParentId" parameterType="java.lang.Long" resultMap="Dept02DTO">
        SELECT dept_id,dept_name,type,parent_id
        FROM  pes_dept_02
        WHERE  parent_id = #{deptId}
    </select>

    <select id="listDept03ByParentId" parameterType="java.lang.Long" resultMap="Dept03DTO">
        SELECT dept_id,dept_name,type,parent_id
        FROM  pes_dept_03
        WHERE  parent_id = #{deptId}
    </select>

    <select id="listDept04ByParentId" parameterType="java.lang.Long" resultMap="Dept04DTO">
        SELECT dept_id,erp_id,type,parent_id,dept_name
        FROM  pes_dept_04
        WHERE  parent_id = #{deptId}
    </select>

    <insert id="insertDept01" parameterType="com.pes.jd.model.DO.Dept01DO" >
        insert into pes_dept_01 (dept_name,type,parent_id,filer_id
        )
        values ( #{deptName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},#{filerId,jdbcType=BIGINT}
        )
    </insert>

    <insert id="insertDept02" parameterType="com.pes.jd.model.DO.Dept02DO" >
        insert into pes_dept_02 (dept_name,type,parent_id,filer_id
        )
        values ( #{deptName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},#{filerId,jdbcType=BIGINT}
        )
    </insert>

    <insert id="insertDept03" parameterType="com.pes.jd.model.DO.Dept03DO" >
        insert into pes_dept_03 (dept_name,type,parent_id,filer_id
        )
        values ( #{deptName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},#{filerId,jdbcType=BIGINT}
        )
    </insert>

    <insert id="insertDept04" parameterType="com.pes.jd.model.DO.Dept04DO" >
        insert into pes_dept_04 (erp_id,type,parent_id,dept_name,filer_id
        )
        values ( #{erpId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},#{deptName,jdbcType=VARCHAR},#{filerId,jdbcType=BIGINT}
        )
    </insert>

    <insert id="insertDeptUser" parameterType="com.pes.jd.model.DO.DeptUserDO" >
        insert into pes_dept_user (username,password,type,dept_id,filer_id
        )
        values ( #{username,jdbcType=VARCHAR},
                  #{password,jdbcType=VARCHAR},#{type,jdbcType=VARCHAR},#{deptId,jdbcType=BIGINT},#{filerId,jdbcType=BIGINT}
        )
    </insert>

    <select id="listDept02ByDeptId"  resultMap="Dept02DTO">
        SELECT DISTINCT dept_id
        FROM  pes_dept_02
        WHERE  parent_id in
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </select>

    <select id="listDept03ByDeptId"  resultMap="Dept03DTO">
        SELECT DISTINCT dept_id
        FROM  pes_dept_03
        WHERE  parent_id in
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </select>

    <select id="listDept04ByDeptId"  resultMap="Dept04DTO">
        SELECT DISTINCT erp_id
        FROM  pes_dept_04
        WHERE  parent_id in
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </select>

    <select id="getDept01ByDeptId"  resultMap="Dept01DTO">
      SELECT  dept_id,dept_name,type,parent_id
        FROM  pes_dept_01
       WHERE  dept_id = #{deptId}
        limit 1
    </select>

    <select id="getDept02ByDeptId"  resultMap="Dept02DTO">
        SELECT  dept_id,dept_name,type,parent_id
        FROM  pes_dept_02
        WHERE  dept_id = #{deptId}
        limit 1
    </select>

    <select id="getDept03ByDeptId"  resultMap="Dept03DTO">
        SELECT  dept_id,dept_name,type,parent_id
        FROM  pes_dept_03
        WHERE  dept_id = #{deptId}
        limit 1
    </select>

    <select id="getDept04ByDeptId"  resultMap="Dept04DTO">
        SELECT  dept_id,erp_id,type,parent_id,dept_name
        FROM  pes_dept_04
        WHERE  dept_id = #{deptId}
        limit 1
    </select>

    <select id="getDeptUserByDeptId"  resultMap="DeptDTO">
        SELECT  id,dept_id,type,username,password
        FROM  pes_dept_user
        WHERE  dept_id = #{deptId}
        limit 1
    </select>
    <insert id="insertDeptFiler" parameterType="com.pes.jd.model.DO.DeptFilerDO" >

        insert into pes_dept_filer (filer_name,upload_time,status
        )
        values ( #{filerName,jdbcType=VARCHAR}, #{uploadTime,jdbcType=TIMESTAMP},#{status,jdbcType=VARCHAR}
        )
    </insert>


    <select id="listDept00ByDeptName" parameterType="java.lang.String" resultMap="Dept00DTO">
        SELECT dept_id,type,parent_id,dept_name
        FROM  pes_dept_00
        WHERE  dept_name = #{deptName}
    </select>

    <insert id="insertDept00" parameterType="com.pes.jd.model.DO.Dept00DO" >
        insert into pes_dept_00 (dept_name,type,parent_id,filer_id
        )
        values ( #{deptName,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT},#{filerId,jdbcType=BIGINT}
        )
    </insert>

    <select id="getDept00ByDeptId"  resultMap="Dept00DTO">
        SELECT  dept_id,dept_name,type,parent_id
        FROM  pes_dept_00
        WHERE  dept_id = #{deptId}
        limit 1
    </select>

    <delete id="deleteDept00ByParentId">
        DELETE
          FROM  pes_dept_00
         WHERE
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </delete>

    <delete id="deleteDept01ByParentId" >
        DELETE
        FROM  pes_dept_01
        WHERE
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </delete>

    <delete id="deleteDept02ByParentId">
        DELETE
        FROM  pes_dept_02
        WHERE
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </delete>

    <delete id="deleteDept03ByParentId"  >
        DELETE
        FROM  pes_dept_03
        WHERE
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </delete>

    <delete id="deleteDept04ByParentId" >
        DELETE
        FROM  pes_dept_04
        WHERE
        <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
            #{parentId}
        </foreach>
    </delete>

    <select id="getDeptFilerByFilerName" resultMap="DeptFilerDTO">
        SELECT  filer_id,filer_name,upload_time,status
        FROM  pes_dept_filer
        WHERE  filer_name = #{filerName}
        limit 1
    </select>

    <select id="getDeptFilerByFilerId" resultMap="DeptFilerDTO">
        SELECT  filer_id,filer_name,upload_time,status
        FROM  pes_dept_filer
        WHERE  filer_id = #{filerId}
        limit 1
    </select>

    <update id="updateDeptFiler" parameterType="com.pes.jd.model.DO.DeptFilerDO">
        update pes_dept_filer
        <set>
            <if test="filerName != null and filerName!=''">
                filer_name = #{filerName,jdbcType=VARCHAR},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status!=''">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <!--<if test="status != null and status!=''">-->
                <!--dept_00_id = #{dept00Id,jdbcType=BIGINT}-->
            <!--</if>-->
        </set>
        WHERE
        filer_id = #{filerId,jdbcType=BIGINT}
    </update>
    <select id="listDeptFiler" resultMap="DeptFilerDTO">
        SELECT  filer_id,filer_name,upload_time,status
        FROM  pes_dept_filer
    </select>

    <select id="getPesDeptByUserName" parameterType="java.lang.String" resultMap="pesDeptDTO">
        SELECT id,name,username,password
        FROM pes_dept
        WHERE
        username = #{userName}
        LIMIT 1
    </select>

    <select id="selectShopByDeptIdAndType" resultMap="DeptShopVO">
        SELECT p.shop_id,p.rt_db,p.rt_schema_id,p.title
            FROM
            <if test="type == 0 ">
                pes_dept_01 d1,pes_dept_02 d2,pes_dept_03 d3,pes_dept_04 d4,pes_shop_pop pop,pes_shop p
            </if>
            <if test="type == 1 ">
                pes_dept_02 d2,pes_dept_03 d3,pes_dept_04 d4,pes_shop_pop pop,pes_shop p
            </if>
            <if test="type == 2 ">
                pes_dept_03 d3,pes_dept_04 d4,pes_shop_pop pop,pes_shop p
            </if>
            <if test="type == 3 ">
                pes_dept_04 d4,pes_shop_pop pop,pes_shop p
            </if>
            <if test="type == 4 ">
                pes_dept_04 d4,pes_shop_pop pop,pes_shop p
            </if>
        WHERE
            p.status = 'active' and

            <if test="shopParam != null ">
                p.title LIKE CONCAT('%', #{shopParam},'%') and
            </if>

            <if test="type == 0 ">
                d1.dept_id=d2.parent_id and d2.dept_id=d3.parent_id
                and d3.dept_id=d4.parent_id and d4.erp_id = pop.erp_id
                and pop.shop_id = p.shop_id  and d1.parent_id = #{deptId}
            </if>
            <if test="type == 1 ">
                d2.dept_id=d3.parent_id
                and d3.dept_id=d4.parent_id and d4.erp_id = pop.erp_id
                and pop.shop_id = p.shop_id  and d2.parent_id = #{deptId}
            </if>
            <if test="type == 2 ">
                d3.dept_id=d4.parent_id and d4.erp_id = pop.erp_id
                and pop.shop_id = p.shop_id  and d3.parent_id = #{deptId}
            </if>
            <if test="type == 3 ">
                d4.erp_id = pop.erp_id
                and pop.shop_id = p.shop_id  and d4.parent_id = #{deptId}
            </if>
            <if test="type == 4 ">
                d4.erp_id = pop.erp_id
                and pop.shop_id = p.shop_id  and d4.dept_id = #{deptId}
            </if>
    </select>

    <!--<delete id="deleteDeptUserByDeptIdAndType" parameterType="com.pes.jd.model.DO.DeptUserDO" >-->
        <!--delete-->
        <!--FROM pes_dept_user-->
        <!--WHERE-->
        <!--<if test="deptId != null">-->
            <!--dept_id = #{deptId}-->
        <!--</if>-->
        <!--<if test="type != null and type!=''">-->
            <!--and-->
            <!--type = #{type}-->
        <!--</if>-->
    <!--</delete>-->

    <delete id="deleteDeptUserByFilerId" >
            delete
            FROM pes_dept_user
            WHERE
            <if test="filerId != null">
                filer_id = #{filerId}
            </if>
    </delete>

    <delete id="deleteDept00ByFilerId" >
        delete
        FROM pes_dept_00
        WHERE
        <if test="filerId != null">
            filer_id = #{filerId}
        </if>
    </delete>

    <delete id="deleteDept01ByFilerId" >
        delete
        FROM pes_dept_01
        WHERE
        <if test="filerId != null">
            filer_id = #{filerId}
        </if>
    </delete>

    <delete id="deleteDept02ByFilerId"  >
        delete
        FROM pes_dept_02
        WHERE
        <if test="filerId != null">
            filer_id = #{filerId}
        </if>
    </delete>

    <delete id="deleteDept03ByFilerId"  >
        delete
        FROM pes_dept_03
        WHERE
        <if test="filerId != null">
            filer_id = #{filerId}
        </if>
    </delete>

    <delete id="deleteDept04ByFilerId"  >
        delete
        FROM pes_dept_04
        WHERE
        <if test="filerId != null">
            filer_id = #{filerId}
        </if>
    </delete>

</mapper>