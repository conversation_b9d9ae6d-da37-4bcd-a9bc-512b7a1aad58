<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopRemindWordGoodsMapper" >
    <resultMap id="ShopRemindWordGoodsDO" type="com.pes.jd.model.DO.ShopRemindWordGoodsDO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="shop_id" property="shopId" jdbcType="BIGINT" />
        <result column="remind_word_id" property="remindWordId" jdbcType="BIGINT" />
        <result column="goods_id" property="goodsId" jdbcType="BIGINT" />
    </resultMap>
    <resultMap id="ShopRemindWordGoodsDTO" type="com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordGoodsDTO" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="shop_id" property="shopId" jdbcType="BIGINT" />
        <result column="remind_word_id" property="remindWordId" jdbcType="BIGINT" />
        <result column="goods_id" property="goodsId" jdbcType="BIGINT" />
        <result column="type" property="type" jdbcType="TINYINT"/>
    </resultMap>
    <sql id="base_field" >
    id, shop_id, remind_word_id, goods_id,`type`
  </sql>
    <select id="selectShopRemindWordGoodsByWordId" resultMap="ShopRemindWordGoodsDTO"  >
        select
        <include refid="base_field" />
        from pes_shop_remind_word_goods
        where remind_word_id = #{wordId,jdbcType=BIGINT}
    </select>
    <delete id="deleteShopRemindWordGoodsByWordId" parameterType="java.lang.Long" >
    delete from pes_shop_remind_word_goods
    where remind_word_id = #{wordId,jdbcType=BIGINT} AND  `type`=#{type}
  </delete>
    <insert id="batchInsertShopRemindWordGoods" parameterType="map" >
        INSERT INTO
        pes_shop_remind_word_goods
        ( shop_id, remind_word_id,goods_id, `type`)
        VALUES
        <foreach collection="remindGoodsLst" item="rg" separator=",">
            (
            #{rg.shopId,jdbcType=BIGINT},
            #{rg.remindWordId,jdbcType=BIGINT},
            #{rg.goodsId,jdbcType=BIGINT},
            #{rg.type,jdbcType=TINYINT}
            )
        </foreach>
    </insert>
    <select id="selectShopRemindWordGoodsByShopId" resultMap="ShopRemindWordGoodsDTO"  >
        select
        <include refid="base_field" />
        from pes_shop_remind_word_goods
        where shop_id = #{shopId,jdbcType=BIGINT}
    </select>

    <select id="selectShopRemindWordGoodsByShopIdByRemindIds" resultMap="ShopRemindWordGoodsDTO"  >
        select
        <include refid="base_field" />
        from pes_shop_remind_word_goods
        where shop_id = #{shopId,jdbcType=BIGINT}
        <if test="remindIds!=null and remindIds.size()>0">
            and remind_word_id in
            <foreach collection="remindIds" open="(" close=")" separator="," item="remindId">
                #{remindId}
            </foreach>
        </if>
        <if test="type!=null">
            AND type =#{type}
        </if>

    </select>

    <delete id="deleteRemindIdByShopIdAndType">
        DELETE
        FROM pes_shop_remind_word_goods
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        <if test="remindId!=null">
            AND remind_word_id =#{remindId}
        </if>
        <if test="goodIds!=null and goodIds.size()>0">
            AND goods_id IN
            <foreach collection="goodIds" open="(" close=")" separator="," item="goodId">
                #{goodId}
            </foreach>
        </if>
        <if test="type!=null">
            AND type =#{type}
        </if>
    </delete>

    <select id="selectRemindIdByShopIdAndType" resultMap="ShopRemindWordGoodsDTO">
        select <include refid="base_field" />
        FROM pes_shop_remind_word_goods
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        <if test="remindId!=null">
            AND remind_word_id =#{remindId}
        </if>
        <if test="goodIds!=null and goodIds.size()>0">
            AND goods_id IN
            <foreach collection="goodIds" open="(" close=")" separator="," item="goodId">
                #{goodId}
            </foreach>
        </if>
        <if test="type!=null">
            AND type =#{type}
        </if>
    </select>

    <select id="selectShopRemindWordGoodsByWordIdAndType" resultMap="ShopRemindWordGoodsDTO">
        select <include refid="base_field" /> from pes_shop_remind_word_goods
    where remind_word_id = #{wordId,jdbcType=BIGINT} AND  `type`=#{type}
    </select>
</mapper>