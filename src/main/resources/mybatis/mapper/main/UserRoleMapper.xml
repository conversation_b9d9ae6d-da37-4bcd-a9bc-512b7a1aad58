<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.UserRoleMapper">

	<resultMap id="UserRoleDO" type="com.pes.jd.model.DO.UserRole">
		<id column="user_id" jdbcType="BIGINT" property="userId" />
		<id column="role_id" jdbcType="BIGINT" property="roleId" />
	</resultMap>
	
	<insert id="insertUserRole" parameterType="com.pes.jd.model.DO.UserRole">
		INSERT INTO pes_user_role (user_id, role_id)
		VALUES (#{userId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT})
	</insert>
	
	<delete id="deleteUserRoleById" parameterType="com.pes.jd.model.DO.UserRole">
		DELETE FROM pes_user_role
		WHERE
			user_id = #{userId,jdbcType=BIGINT}
		AND
			role_id = #{roleId,jdbcType=BIGINT}
	</delete>
	
	 <update id="updateUserRole" parameterType="com.pes.jd.model.DO.UserRole">
    	UPDATE pes_user_role
    	SET role_id = #{roleId,jdbcType=BIGINT}
    	WHERE
    	user_id = #{userId,jdbcType=BIGINT}
    </update>
    
	<select id="getUserRoleById" parameterType="java.lang.Long" resultMap="UserRoleDO">
		SELECT * FROM pes_ws_user
		WHERE
			id = #{id,jdbcType=BIGINT}
	</select>
	
	<select id="getUserRoleIdByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
		SELECT role_id
		FROM pes_user_role
		WHERE user_id = #{userId,jdbcType=BIGINT}
	</select>
	
	<select id="getUserRoleNameByUserId" parameterType="java.lang.String" resultType="java.lang.String">
		SELECT r.role_name
		FROM pes_user_role ur
		LEFT JOIN pes_role r
		ON ur.role_id = r.id
		WHERE ur.user_id = #{userId}
	</select>
</mapper>