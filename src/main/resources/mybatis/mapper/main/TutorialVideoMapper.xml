<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.TutorialVideoMapper">
    <resultMap id="TutorialVideoDTO" type="com.pes.jd.model.DTO.TutorialVideoDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="video_name" jdbcType="VARCHAR" property="videoName"/>
        <result column="video_url" jdbcType="VARCHAR" property="videoUrl"/>
        <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, video_name, video_url,picture_url, create_date
  </sql>

    <insert id="insert" parameterType="com.pes.jd.model.DO.TutorialVideoDO">
        insert into pes_tutorial_video
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="videoName != null">
                video_name,
            </if>
            <if test="videoUrl != null">
                video_url,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="videoName != null">
                #{videoName,jdbcType=VARCHAR},
            </if>
            <if test="videoUrl != null">
                #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_tutorial_video
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.TutorialVideoDO">
        update pes_tutorial_video
        <set>
            <if test="videoName != null">
                video_name = #{videoName,jdbcType=VARCHAR},
            </if>
            <if test="videoUrl != null">
                video_url = #{videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByPrimaryKey" parameterType="java.lang.Long" resultMap="TutorialVideoDTO">
        select
        <include refid="Base_Column_List"/>
        from pes_tutorial_video
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectVideoByName" resultMap="TutorialVideoDTO">
        select
        <include refid="Base_Column_List"/>
        from pes_tutorial_video
        <where>
            <if test="videoName != null">
                video_name LIKE CONCAT('%', #{videoName},'%')
            </if>
        </where>

        order by create_date desc
    </select>

</mapper>