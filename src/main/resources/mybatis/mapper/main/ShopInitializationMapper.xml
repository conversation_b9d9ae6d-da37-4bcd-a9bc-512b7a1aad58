<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopInitializationMapper">

    <insert id="insertEntryGuidance">
        insert into ${tableName}
        (shop_id, date, created, modified, flag)
        values
        (#{shopId}, #{date}, #{created}, #{modified}, #{flag})
    </insert>
    <update id="updateEntryGuidance">
        update ${tableName}
        set flag = 1,
        modified = #{modified}
        where shop_id = #{shopId}
    </update>
    <select id="selectEntryGuidanceByShopId" resultType="com.pes.jd.model.DO.GuidanceRecordDO">
        select * from ${tableName}
        where shop_id = #{shopId}
    </select>
    <select id="getGuidanceRecordByShopId" resultType="com.pes.jd.model.DO.GuidanceRecordDO">
        select flag from ${tableName} where shop_id = #{shopId}
    </select>
</mapper>