<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopGroupMapper">
	
	<resultMap type="com.pes.jd.model.DTO.ShopGroupDTO" id="ShopGroupDTO">
		<id column="group_id" jdbcType="BIGINT" property="groupId"/>
	    <result column="shop_id" jdbcType="BIGINT" property="ownerShopId" />
	    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
	    <result column="status" jdbcType="INTEGER" property="status" />
	    <result column="created" jdbcType="TIMESTAMP" property="createdDate" />
	    <result column="modified" jdbcType="TIMESTAMP" property="modifiedDate" />
		<result column="mutual_watch" jdbcType="BIT" property="mutualWatch" />
	</resultMap>
	
	<insert id="addShopGroupForMainShop" parameterType="map">
		INSERT INTO pes_shop_group (group_name,created,modified,shop_id) 
		VALUES (#{shopGroupName},#{created},#{created},#{shopId});
	</insert>

	<update id="updateShopGroupMutualWatch" parameterType="map">
		UPDATE pes_shop_group
		SET mutual_watch = #{mutualWatch,jdbcType=BIT}
		WHERE group_id = #{shopGroupId}
	</update>
	
	<update id="deleteShopGroupByShopGroupId" parameterType="map">
		UPDATE pes_shop_group
		SET status = 2,
		modified = #{modified,jdbcType=TIMESTAMP}
		WHERE group_id = #{shopGroupId}
	</update>
	
	<update id="updateShowMultiShopStatus" parameterType="map">
		UPDATE pes_user
		SET multi_shop_switch = #{showMultiShopStatus}
		WHERE user_id = #{userId}
	</update>
	
	<update id="updateShopGroupNameByShopGroupId" parameterType="map">
		UPDATE pes_shop_group
		SET group_name = #{shopGroupName,jdbcType=VARCHAR},
			modified = #{modified,jdbcType=TIMESTAMP}
		WHERE group_id = #{shopGroupId}
		AND status = 1
	</update>
	
	<select id="selectShopGroupsByOwnerShopId" parameterType="java.lang.Long" resultMap="ShopGroupDTO">
		SELECT *
		FROM pes_shop_group
		WHERE shop_id = #{shopId}
			AND status = 1
		ORDER BY created DESC
	</select>
	
	<select id="selectShopGroupsByGroupNameAndOwner" parameterType="map" resultMap="ShopGroupDTO">
		SELECT sg.group_id,sg.group_name,sg.created,sg.shop_id
		FROM pes_shop_group sg, pes_shop sp
		WHERE sg.shop_id = sp.shop_id
		AND sg.group_name = #{shopGroupName}
		AND sg.status = 1
		AND sg.shop_id = #{shopId}
	</select>
	
	<select id="selectShopGroupsByShopGroupId" parameterType="java.lang.String" resultMap="ShopGroupDTO">
		SELECT *
		FROM pes_shop_group
		WHERE group_id = #{shopGroupId}
		AND status = 1
	</select>
	
</mapper>