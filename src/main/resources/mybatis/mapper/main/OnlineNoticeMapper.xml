<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.OnlineNoticeMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OnlineNoticeDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="online_version" jdbcType="VARCHAR" property="onlineVersion"/>
        <result column="notice_content" jdbcType="VARCHAR" property="noticeContent"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="enable_switch" jdbcType="BIT" property="enableSwitch"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, online_version, notice_content, create_date, enable_switch
  </sql>

    <insert id="insert" parameterType="com.pes.jd.model.DO.OnlineNoticeDO">
        insert into pes_online_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="onlineVersion != null">
                online_version,
            </if>
            <if test="noticeContent != null">
                notice_content,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="enableSwitch != null">
                enable_switch,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="onlineVersion != null">
                #{onlineVersion,jdbcType=VARCHAR},
            </if>
            <if test="noticeContent != null">
                #{noticeContent,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="enableSwitch != null">
                #{enableSwitch,jdbcType=BIT},
            </if>
        </trim>
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_online_notice
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.OnlineNoticeDO">
        update pes_online_notice
        <set>
            <if test="onlineVersion != null">
                online_version = #{onlineVersion,jdbcType=VARCHAR},
            </if>
            <if test="noticeContent != null">
                notice_content = #{noticeContent,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="enableSwitch != null">
                enable_switch = #{enableSwitch,jdbcType=BIT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_online_notice
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectEnableNotice" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_online_notice
        where enable_switch = 1
        order by create_date desc
    </select>

    <select id="selectNoticeByVersionAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_online_notice
        where create_date between #{startDate} and #{endDate}
        <if test="onlineVersion != null and onlineVersion != ''">
            and online_version like CONCAT('%', #{onlineVersion},'%')
        </if>
        order by create_date desc
    </select>
</mapper>