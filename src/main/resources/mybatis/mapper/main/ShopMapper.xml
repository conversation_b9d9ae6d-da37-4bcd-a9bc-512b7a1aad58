<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopMapper">


	<resultMap id="ShopDO" type="com.pes.jd.model.DO.Shop">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="vender_id" jdbcType="BIGINT" property="venderId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="subuser_num" jdbcType="INTEGER" property="subuserNum" />
		<result column="realtime_switch" jdbcType="BIT" property="realtimeSwitch" />
		<result column="fetch_flag" jdbcType="INTEGER" property="fetchFlag" />
		<result column="init_data_flag" jdbcType="INTEGER" property="initDataFlag" />
		<result column="previous_get_data_time" jdbcType="TIMESTAMP" property="previousGetDataTime" />
		<result column="pre_fetch_realtime" jdbcType="TIMESTAMP" property="preFetchRealtime" />
		<result column="last_consumed_time" jdbcType="BIGINT" property="lastConsumedTime" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
		<result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
		<result column="create_table_flag" jdbcType="INTEGER" property="createTableFlag" />
		<result column="col_type" jdbcType="INTEGER" property="colType" />
	</resultMap>
	<resultMap id="JobShopDTO" type="com.pes.jd.model.DTO.JobShopDTO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="subuser_num" jdbcType="INTEGER" property="subuserNum" />
		<result column="realtime_switch" jdbcType="BIT" property="realtimeSwitch" />
		<result column="fetch_flag" jdbcType="INTEGER" property="fetchFlag" />
		<result column="init_data_flag" jdbcType="INTEGER" property="initDataFlag" />
		<result column="previous_get_data_time" jdbcType="TIMESTAMP" property="previousGetDataTime" />
		<result column="pre_fetch_realtime" jdbcType="TIMESTAMP" property="preFetchRealtime" />
		<result column="last_consumed_time" jdbcType="BIGINT" property="lastConsumedTime" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="col_type" jdbcType="VARCHAR" property="colType" />
		<result column="type" jdbcType="VARCHAR" property="type" />
		<result column="vender_id" jdbcType="BIGINT" property="venderId" />
		<result column="sign_id" jdbcType="VARCHAR" property="signId" />
	</resultMap>
	<resultMap id="ShopVO" type="com.pes.jd.model.VO.ShopVO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="start_time" jdbcType="TIMESTAMP" property="orderDate" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="pop_name" jdbcType="VARCHAR" property="operateManagerName" />
		<result column="erp_id" jdbcType="VARCHAR" property="operateManagerErp" />
	</resultMap>

	<resultMap id="AdminShopVO" type="com.pes.jd.model.VO.AdminShopVO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="start_time" jdbcType="TIMESTAMP" property="orderDate" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="pop_name" jdbcType="VARCHAR" property="operateManagerName" />
		<result column="erp_id" jdbcType="VARCHAR" property="operateManagerErp" />
		<result column="vender_id" jdbcType="BIGINT" property="venderId" />
		<result column="subuser_Num" jdbcType="INTEGER" property="subuserNum" />
		<result column="init_data_flag" jdbcType="INTEGER" property="initDataFlag" />
		<result column="previous_get_data_time" jdbcType="TIMESTAMP" property="previousGetDataTime" />
		<result column="col_type" jdbcType="INTEGER" property="colType" />
		<result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
		<result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
		<result column="subscribe_dead_line" jdbcType="TIMESTAMP" property="subscribeDeadLine" />
		<result column="item_code" jdbcType="VARCHAR" property="itemCode" />
		<result column="interface_type" jdbcType="INTEGER" property="interfaceType" />
		<result column="auth_dead_line" jdbcType="TIMESTAMP" property="authDeadLine" />
		<result column="ip" jdbcType="VARCHAR" property="ip" />

	</resultMap>

	<resultMap id="ShopDTO" type="com.pes.jd.model.DTO.ShopDTO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="vender_id" jdbcType="BIGINT" property="venderId" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="subuser_num" jdbcType="INTEGER" property="subuserNum" />
		<result column="realtime_switch" jdbcType="BIT" property="realtimeSwitch" />
		<result column="fetch_flag" jdbcType="INTEGER" property="fetchFlag" />
		<result column="init_data_flag" jdbcType="INTEGER" property="initDataFlag" />
		<result column="previous_get_data_time" jdbcType="TIMESTAMP" property="previousGetDataTime" />
		<result column="pre_fetch_realtime" jdbcType="TIMESTAMP" property="preFetchRealtime" />
		<result column="last_consumed_time" jdbcType="BIGINT" property="lastConsumedTime" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
		<result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
		<result column="create_table_flag" jdbcType="INTEGER" property="createTableFlag" />
		<result column="col_type" jdbcType="INTEGER" property="colType" />
		<result column="type" jdbcType="INTEGER" property="type" />
		<result column="option_session_key" jdbcType="VARCHAR" property="optionSessionKey" />
		<result column="id" jdbcType="BIGINT" property="id" />
	</resultMap>

	<resultMap id="shopDTO2" type="com.pes.jd.model.DTO.ShopDTO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="auth_dead_line" jdbcType="TIMESTAMP" property="authDeadLine" />
		<result column="rt_db" jdbcType="VARCHAR" property="rtDb" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
	</resultMap>

	<resultMap id="ShopDbAndSchemeIdBO" type="com.pes.jd.model.BO.ShopDbAndSchemeIdBO">
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
	</resultMap>

	<resultMap id="ShopUrge" type="com.pes.jd.ms.domain.Data.master.ShopUrge" >
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="title" property="shopName" jdbcType="VARCHAR" />
		<result column="db" property="db" jdbcType="VARCHAR" />
		<result column="schema_id" property="schemaId" jdbcType="VARCHAR" />
		<result column="rt_db" property="rtDb" jdbcType="VARCHAR" />
		<result column="rt_schema_id" property="rtSchemaId" jdbcType="VARCHAR" />
	</resultMap>

	<resultMap id="ShopSplitKeyDTO" type="com.pes.jd.model.DTO.ShopSplitKeyDTO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId"/>
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId"/>
		<result column="db" jdbcType="VARCHAR" property="db"/>
		<result column="rt_schema_id" jdbcType="VARCHAR" property="rtSchemaId"/>
		<result column="rt_db" jdbcType="VARCHAR" property="rtDb"/>
		<result column="type" jdbcType="VARCHAR" property="type"/>
	</resultMap>

	<sql id="base_field">
		shop_id, user_id,vender_id, seller_nick, title, session_key, `status`, subuser_num,
		realtime_switch,
		fetch_flag, init_data_flag, previous_get_data_time, pre_fetch_realtime,
		last_consumed_time,col_type,
		`schema_id`, db,create_table_flag,rt_schema_id,rt_db,subscribe_dead_line,option_session_key,id
	</sql>

	<insert id="insertShop" parameterType="com.pes.jd.model.DO.Shop">
	INSERT INTO pes_shop (
		shop_id, user_id,vender_id, seller_nick,
		title, session_key, `status`,
		subuser_num, realtime_switch, fetch_flag,
		init_data_flag, previous_get_data_time,
		`schema_id`,db,create_table_flag ,
		col_type,`rt_schema_id`,rt_db,subscribe_dead_line,type
		,refresh_session_key,id
	)
	VALUES
	(
	#{shopId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},#{venderId,jdbcType=BIGINT},
	#{sellerNick,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR},
	#{sessionKey,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
	#{subuserNum,jdbcType=INTEGER}, #{realtimeSwitch,jdbcType=BIT},
	#{fetchFlag,jdbcType=INTEGER},#{initDataFlag,jdbcType=INTEGER},
	#{previousGetDataTime,jdbcType=TIMESTAMP},#{schemaId,jdbcType=VARCHAR},
	#{db,jdbcType=VARCHAR},
	#{createTableFlag,jdbcType=INTEGER},#{colType,jdbcType=INTEGER} ,
	#{rtSchemaId,jdbcType=VARCHAR},
	#{rtDb,jdbcType=VARCHAR},#{subscribeDeadLine},
	#{type,jdbcType=INTEGER},
	#{refreshSessionKey,jdbcType=VARCHAR},
	#{id,jdbcType=BIGINT}
	)
	</insert>

	<delete id="deleteShopByShopId" parameterType="java.lang.Long">
		DELETE FROM pes_shop
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</delete>

	<update id="updateShopById" parameterType="com.pes.jd.model.DO.Shop">
		update pes_shop
		<set>
			<if test="userId != null">
				user_id = #{userId,jdbcType=BIGINT},
			</if>
			<if test="sellerNick != null and sellerNick!=''">
				seller_nick = #{sellerNick,jdbcType=VARCHAR},
			</if>
			<if test="title != null and title!=''">
				title = #{title,jdbcType=VARCHAR},
			</if>
			<if test="sessionKey != null and sessionKey!=''">
				session_key = #{sessionKey,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="subuserNum != null">
				subuser_num = #{subuserNum,jdbcType=INTEGER},
			</if>
			<if test="realtimeSwitch != null">
				realtime_switch = #{realtimeSwitch,jdbcType=BIT},
			</if>
			<if test="fetchFlag != null">
				fetch_flag = #{fetchFlag,jdbcType=INTEGER},
			</if>
			<if test="initDataFlag != null">
				init_data_flag = #{initDataFlag,jdbcType=INTEGER},
			</if>
			<if test="previousGetDataTime != null">
				previous_get_data_time = #{previousGetDataTime,jdbcType=TIMESTAMP},
			</if>
			<if test="preFetchRealtime != null">
				pre_fetch_realtime = #{preFetchRealtime,jdbcType=TIMESTAMP},
			</if>
			<if test="lastConsumedTime != null">
				last_consumed_time = #{lastConsumedTime,jdbcType=BIGINT},
			</if>
			<if test="schemaId != null and schemaId!='' ">
				schemaId = #{schemaId,jdbcType=VARCHAR},
			</if>
			<if test="db != null">
				db = #{db,jdbcType=VARCHAR},
			</if>
			<if test="createTableFlag != null">
				create_table_flag = #{createTableFlag,jdbcType=INTEGER},
			</if>
			<if test="colType != null">
				col_type = #{colType,jdbcType=INTEGER},
			</if>
			<if test="subscribeDeadLine != null">
				subscribe_dead_line = #{subscribeDeadLine,jdbcType=INTEGER},
			</if>
			<if test="refreshSessionKey != null and refreshSessionKey!=''">
				refresh_session_key = #{refreshSessionKey,jdbcType=VARCHAR},
			</if>
			<if test="optionSessionKey != null and optionSessionKey!=''">
				option_session_key = #{optionSessionKey,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</update>

	<update id="updateShopCreateTableFlagByShopId" parameterType="java.lang.Long">
		update pes_shop set create_table_flag=1 where shop_id=#{shopId}
	</update>
	<update id="updateInitFlag" parameterType="java.lang.Long">
		UPDATE pes_shop
		SET
			init_data_flag = 1
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</update>
	<select id="selectShopByShopId" parameterType="java.lang.Long" resultMap="ShopDTO">
		SELECT *
		FROM pes_shop
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</select>

	<select id="getShopInfoForVenderId" parameterType="java.lang.Long" resultMap="ShopDTO">
		SELECT *
		FROM pes_shop
		WHERE
		vender_id = #{venderId,jdbcType=INTEGER}
	</select>

	<select id="selectShopByUserNick" parameterType="java.lang.String" resultMap="ShopDTO">
			SELECT
			<include refid="base_field" />
			FROM pes_shop
			WHERE
				seller_nick = #{userNick,jdbcType=VARCHAR}
	</select>

	<select id="selectShopByNickOrTitle" parameterType="java.lang.String" resultMap="AdminShopVO">
			SELECT ps.shop_id, ps.seller_nick, ps.title, ps.session_key, ps.`status`,ps.`schema_id`, ps.db ,pss.judge_rule judgeRule,ps.type type,
			ps.vender_id, ps.subuser_num, ps.init_data_flag, ps.previous_get_data_time, ps.col_type, ps.rt_schema_id, ps.rt_db,
			us.subscribe_dead_line, us.item_code, us.interface_type, us.auth_dead_line, us.ip
			FROM pes_shop ps, pes_user us, pes_shop_systemsetting pss
			WHERE ps.shop_id = pss.shop_id AND ps.shop_id = us.shop_id AND us.is_main_account = 1
			AND
			    ps.type = #{type}
			    and
				(seller_nick LIKE CONCAT('%', #{nick},'%')
				OR title LIKE CONCAT('%', #{nick},'%'))
	</select>

    <select id="searchAllShop" parameterType="java.lang.String" resultMap="ShopVO">
			SELECT ps.shop_id, ps.seller_nick, ps.title, ps.session_key, ps.`status`,ps.`schema_id`, ps.db ,pss.judge_rule judgeRule
			FROM pes_shop ps
			LEFT JOIN pes_shop_systemsetting pss
			ON ps.shop_id = pss.shop_id
			WHERE
				ps.status = 'active'
	</select>

	<select id="getShopBySellerNickOrTitle" parameterType="java.lang.String" resultMap="ShopDTO">
			SELECT shop_id, seller_nick, title, status,schema_id, db,rt_schema_id ,rt_db,`type`
			FROM pes_shop
			WHERE (seller_nick = #{inviteShopInfo}
				OR title = #{inviteShopInfo})
	</select>

	<select id="selectShopAuthExpiredBySellerNickOrTitleAndTime" parameterType="map" resultMap="shopDTO2">
			SELECT s.shop_id, s.seller_nick, s.title, s.status, u.auth_dead_line
			FROM pes_shop s, pes_user u
			WHERE s.shop_id = u.shop_id AND u.is_main_account = 1
				AND s.status = 'active'
		        AND s.type = #{shopType}
				AND u.auth_dead_line BETWEEN #{startDate} AND #{endDate}
				<if test="inviteShopInfo!=null and inviteShopInfo!=''">
					AND (s.seller_nick LIKE CONCAT('%', #{inviteShopInfo},'%')
					OR s.title LIKE CONCAT('%', #{inviteShopInfo},'%')
					OR s.shop_id LIKE CONCAT('%', #{inviteShopInfo},'%'))
				</if>
	</select>

	<select id="selectAllNotWhiteShopForLst" resultMap="ShopVO">
	SELECT pes_shop.shop_id AS shop_id ,pes_shop.title AS title
	FROM pes_shop
    WHERE pes_shop.`status`='active'
    AND `type`=#{type}
	</select>

	<select id="selectShopByShopNameorShopId" resultMap="ShopDTO">
		SELECT
			shop_id,
			session_key,
			seller_nick,
			title,
			status,
			schema_id,
			db,
			col_type
		FROM pes_shop
			WHERE title=#{shopName} OR shop_id=#{shopName}
	</select>


	<select id="selectShopByShopIdByShopName" resultMap="ShopDTO">
		SELECT *
		FROM pes_shop
		<where>
			<if test="shopIdLst!=null and shopIdLst.size>0">
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>

			<if test="shopName!=null and shopName!=''">
				 and title like concat('%',#{shopName},'%')
			</if>
			<if test="shopId!=null and shopId!=''">
				 and shop_id =#{shopId}
			</if>
			and status ='active'
		</where>
	</select>


	<select id="selectShopByIds" resultMap="ShopDTO">
	SELECT
		<include refid="base_field"/>
		FROM pes_shop
		<where>

			<if test="shopIdLst!=null and shopIdLst.size>0">
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>

			<if test="shopParam!=null and shopParam!=''">
			 and	(title like concat('%',#{shopParam},'%') or shop_id like concat('%',#{shopParam},'%'))
			</if>
			and status='active'
		</where>
	</select>


<select id="selectShopByDateByNickOrTitle" resultMap="ShopVO">

		SELECT ps.shop_id, ps.seller_nick, ps.title, ps.session_key,
						ps.`status`,ps.`schema_id`,
						ss.start_time ,pop.pop_name,pop.erp_id
		FROM pes_shop ps,
-- 		(SELECT shop_id, start_time FROM pes_shop_subcribe  GROUP BY shop_id HAVING max(start_time)) ss,
		(SELECT MAX(start_time) start_time , shop_id FROM pes_shop_subcribe GROUP BY shop_id) ss,
		pes_shop_pop pop
		where  ps.shop_id=ss.shop_id AND ps.shop_id = pop.shop_id AND ps.type= #{type}
		<if test="nick!=null and nick!=''">
			AND (seller_nick LIKE CONCAT('%', #{nick},'%')
			OR title LIKE CONCAT('%', #{nick},'%'))
		</if>

		<if test="status!=null and status!=''">
			AND ps.status=#{status}
		</if>
		<if test="startDate!=null and endDate!=null">
			AND ss.start_time BETWEEN #{startDate} and #{endDate}
		</if>
		<if test="operateManager!=null and operateManager!=''">
			AND (pop_name LIKE CONCAT('%', #{operateManager},'%')
			OR erp_id LIKE CONCAT('%', #{operateManager},'%'))
		</if>
		ORDER BY ss.start_time DESC

	</select>

	<select id="selectShopByDateByNickOrTitleForSelf" resultMap="ShopVO">

		SELECT ps.shop_id, ps.seller_nick, ps.title, ps.session_key,
		ps.`status`,ps.`schema_id`,
		ss.start_time
		FROM pes_shop ps,
		(SELECT MAX(start_time) start_time , shop_id FROM pes_shop_subcribe GROUP BY shop_id) ss
		where  ps.shop_id=ss.shop_id  AND ps.type= #{type}
		<if test="nick!=null and nick!=''">
			AND (seller_nick LIKE CONCAT('%', #{nick},'%')
			OR title LIKE CONCAT('%', #{nick},'%'))
		</if>

		<if test="status!=null and status!=''">
			AND ps.status=#{status}
		</if>
		<if test="startDate!=null and endDate!=null">
			AND ss.start_time BETWEEN #{startDate} and #{endDate}
		</if>
		<if test="operateManager!=null and operateManager!=''">
			AND (pop_name LIKE CONCAT('%', #{operateManager},'%')
			OR erp_id LIKE CONCAT('%', #{operateManager},'%'))
		</if>
		ORDER BY ss.start_time DESC

	</select>

	<select id="selectNoDateShopByDateByNickOrTitle" resultMap="ShopVO">

		SELECT ps.shop_id, ps.seller_nick, ps.title, ps.session_key,
						ps.`status`,ps.`schema_id`, ps.db
		FROM pes_shop ps
		where 1 = 1
		<if test="nick!=null and nick!=''">
			AND (seller_nick LIKE CONCAT('%', #{nick},'%')
			OR title LIKE CONCAT('%', #{nick},'%'))
		</if>
		<if test="status!=null and status!=''">
			AND ps.status=#{status}
		</if>
			AND ps.shop_id NOT in(SELECT shop_id FROM pes_shop_subcribe)
			AND ps.type = #{type}
	</select>
<select id="selectShopInfoForShopUserAnalysis" resultType="com.pes.jd.ms.domain.Data.master.CauseShop">
	SELECT
	ps.shop_id shopId,
	ps.title shopName,
	ps.rt_schema_id rtSchemaId,
	ps.rt_db rtDb,
	ps.schema_id schemaId,
	ps.db db,
	ps.status status,
	pop.erp_id operateManagerErp,
	pop.pop_name operateManagerName
	FROM
	pes_shop ps, pes_shop_pop pop
	where pop.shop_id = ps.shop_id
		and ps.type = #{param.shopType}
	<if test="param.shopStatus!=null and param.shopStatus!='' ">
		AND ps.status=#{param.shopStatus}
	</if>
	<if test="param.shopName!=null and param.shopName!=''">
		AND ( ps.title like concat('%',concat(#{param.shopName},'%')) or  ps.shop_id like concat('%',concat(#{param.shopName},'%'))  )
	</if>
	<if test="param.operateManagerName!=null and param.operateManagerName!='' ">
		AND (pop.pop_name like concat('%',concat(#{param.operateManagerName},'%')) or pop.erp_id like concat('%',concat(#{param.operateManagerName},'%')) )
	</if>

	</select>


		<select id="getShopListByIds" resultType="com.pes.jd.model.DTO.BoardMnoitorParamDTO">
		SELECT
		SHOP_ID,TITLE,SESSION_KEY,SCHEMA_ID,DB,RT_SCHEMA_ID,RT_DB
		FROM pes_shop
		<where>
			<if test="shopIdLst!=null and shopIdLst.size>0">
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>
			and status ='active'
		 <if test="queryParam!=null and queryParam!=''">
            AND( title LIKE concat(concat('%',#{queryParam}),'%')
            OR shop_id LIKE concat(concat('%',#{queryParam}),'%'))
        </if>
        <if test="size!=null and size!=0">
           limit #{currentPage},#{size}
        </if>

		</where>
	</select>


	<select id="selectShopNumsByDeptIdAndTitle" parameterType="map" resultType="java.lang.Integer">
		SELECT
		 count(*)
		FROM pes_shop
		<where>
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			and status ='active'
		 <if test="queryParam!=null and queryParam!=''">
            AND( title LIKE concat(concat('%',#{queryParam}),'%')
            OR shop_id LIKE concat(concat('%',#{queryParam}),'%'))
        </if>
		</where>
	</select>
	<select id="searchAllActiveShopIdsByType" resultMap="shopDTO2" parameterType="string">
	SELECT shop_id,seller_nick,title,status,rt_schema_id,rt_db,schema_id,db
	FROM pes_shop
    WHERE
     `type`=#{type}
	<if test="shopName!=null and shopName!=''">
		AND (title like concat('%',concat(#{shopName},'%')) or shop_id like concat('%',concat(#{shopName},'%')))
	</if>
  	<if test="shopStatus!=null and shopStatus!='' ">
		AND status = #{shopStatus}
  	</if>
	</select>
	<select id="getRtDbLstByIds" resultType="java.lang.String">
		SELECT
		distinct (RT_DB)
		FROM pes_shop
		<where>
			<if test="shopIdLst!=null and shopIdLst.size>0">
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>
			and status ='active'
			<if test="queryParam!=null and queryParam!=''">
				AND( title LIKE concat(concat('%',#{queryParam}),'%')
				OR shop_id LIKE concat(concat('%',#{queryParam}),'%'))
			</if>
			<if test="size!=null and size!=0">
				limit #{currentPage},#{size}
			</if>
		</where>
	</select>
	<select id="getShopListByIdsAndRtdb" resultType="com.pes.jd.model.DTO.BoardMnoitorParamDTO">
		SELECT
		SHOP_ID,TITLE,SESSION_KEY,SCHEMA_ID,DB,RT_SCHEMA_ID,RT_DB
		FROM pes_shop
		<where>
			<if test="shopIdLst!=null and shopIdLst.size>0">
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>
			and status ='active'
			<if test="queryParam!=null and queryParam!=''">
				AND( title LIKE concat(concat('%',#{queryParam}),'%')
				OR shop_id LIKE concat(concat('%',#{queryParam}),'%'))
			</if>
			<if test="rtDb != null and rtDb != ''">
				AND rt_db=#{rtDb}
			</if>
			<if test="size!=null and size!=0">
				limit #{currentPage},#{size}
			</if>
		</where>
	</select>

	<select id="selectShopDbAndSchemaIdByShopTitle" resultMap="ShopDbAndSchemeIdBO" >
		SELECT schema_id,db
		FROM pes_shop
		WHERE
		`type`=#{type}
		<if test="shopTitle!=null and shopTitle!=''">
			AND (title LIKE concat('%', #{shopTitle}, '%') OR shop_id LIKE concat('%', #{shopTitle}, '%'))
		</if>
		group by schema_id,db
	</select>

	<select id="selectDbByShopIds" resultMap="ShopDbAndSchemeIdBO">
		SELECT
			schema_id,db
		FROM pes_shop
		<where>

			<if test="shopIdLst!=null and shopIdLst.size>0">
				shop_id IN
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>
			AND status='active'
		</where>
		GROUP BY schema_id,db
	</select>

	<select id="selectCountByStatus" resultType="integer" >
		SELECT
			COUNT(*)
		FROM
			pes_shop
		WHERE
			STATUS = 'active'
		AND type = 0
	</select>


	<select id="selectUrgeShopByShopIdLstByType" resultMap="ShopUrge"  >
		select
		ps.shop_id,
		ps.title,
		ps.schema_id,
		ps.db,
		ps.rt_schema_id,
		ps.rt_db
		from pes_shop ps
		<where>
			<if test="shopIdLst!=null and shopIdLst.size()>0">
				shop_id in
				<foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
					#{shopId}
				</foreach>
			</if>
			<if test="type!=null">
				AND  ps.type=#{type}
			</if>
		</where>
	</select>

	<select id="queryShopInfoLst" resultMap="ShopDTO" >
		SELECT
			shop_id, vender_id, status
		FROM
			pes_shop
		WHERE
			type = #{shopType}
	</select>

	<select id="queryShopAllActive" resultMap="ShopDTO" >
		SELECT
			shop_id, vender_id,`type`
		FROM
			pes_shop
		WHERE
			STATUS = 'active'
	</select>
	<select id="selectSelfShopCountByStatus" resultType="java.lang.Integer">
		SELECT
			COUNT(*)
		FROM
			pes_shop
		WHERE
			STATUS = 'active'
		AND type = 1
	</select>

	<select id="queryShopInfoList" resultType="com.pes.jd.model.DO.Shop">
		SELECT shop_id, title, session_key, status, schema_id, db, type
		FROM pes_shop
		WHERE type = #{shopType} AND `status` = 'active'
	</select>

	<select id="getJobShopInfoById" parameterType="java.lang.Long" resultMap="JobShopDTO">
		SELECT
				id,
				shop_id,
				seller_nick,
				title,
				session_key,
				status,
				init_data_flag,
				schema_id,
				db,
				col_type,
				vender_id,
				rt_schema_id,
				rt_db,
				option_session_key,
				type,
				sign_id
		FROM pes_shop
		WHERE
				shop_id = #{shopId,jdbcType=BIGINT}
    </select>

	<select id="getAllActiveJobShop" resultMap="JobShopDTO">
		SELECT
		shop_id, seller_nick, title, session_key, status,init_data_flag, schema_id, db,
		col_type, vender_id,rt_schema_id,rt_db,type
		FROM pes_shop
		WHERE
		status = "active"

		<if test="shopType != null and shopType !=''">
			AND type = #{shopType}
		</if>
	</select>

	<select id="getAllActiveJobShopByShopId" resultType="com.pes.jd.model.DTO.PullSubscribeDTO">
		SELECT
				shop_id, session_key
		FROM pes_shop
		WHERE
				status = "active"
		  and shop_id = #{shopId}
	</select>

	<select id="getShopSplitKeyInfo" parameterType="java.lang.Long" resultMap="ShopSplitKeyDTO">
		SELECT
			shop_id, schema_id, db, rt_schema_id, rt_db,`type`, `level`
		FROM pes_shop
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</select>


	<select id="getTypeByShopId" resultType="int">
		SELECT type
		FROM pes_shop p
		WHERE p.shop_id = #{shopId,jdbcType=BIGINT}
	</select>

	<select id="getShopSplitByShopId" parameterType="java.lang.Long" resultMap="JobShopDTO">
		SELECT
				shop_id,
				title,
				session_key,
				option_session_key,
				status,
				schema_id,
				db,
				rt_schema_id,
				rt_db,
				col_type,
				vender_id,
				type
		FROM pes_shop
		WHERE
				shop_id = #{shopId,jdbcType=BIGINT}
	</select>

	<select id="selectShopBySessionKey"  resultMap="ShopDTO">
		SELECT *
		FROM pes_shop
		WHERE
			session_key = #{sessionKey}
	</select>
	<select id="listShopInfoByShopIds" resultType="com.pes.jd.model.DTO.ShopDTO">
		select
		shop_id, vender_id, session_key, option_session_key,type,schema_id,db
		from pes_shop
		where
		shop_id in
		<foreach collection="shopIdList" item="shopId" open="(" close=")" separator=",">
			#{shopId}
		</foreach>
	</select>
</mapper>
