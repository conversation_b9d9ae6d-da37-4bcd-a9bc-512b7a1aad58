<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.KeywordSensitiveMapper">
    <resultMap id="KeywordSensitiveDTO" type="com.pes.jd.model.DTO.KeywordSensitiveDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="keyword" jdbcType="VARCHAR" property="keyword"/>
        <result column="scope" jdbcType="TINYINT" property="scope"/>
    </resultMap>
    <sql id="base_field">
    id, keyword, scope
  </sql>

    <insert id="insert" parameterType="com.pes.jd.model.DTO.KeywordSensitiveDTO">
        INSERT INTO pes_keyword_sensitive (keyword, scope)
        VALUES (#{dto.keyword,jdbcType=VARCHAR}, #{dto.scope,jdbcType=TINYINT})
    </insert>


    <select id="selectKeyWordByScope" resultType="String">
        SELECT keyword FROM pes_keyword_sensitive WHERE scope = #{scope}
    </select>
</mapper>