<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ReportPropertyMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ReportPropertyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="property" jdbcType="VARCHAR" property="property" />
    <result column="filter_flag" jdbcType="TINYINT" property="filterFlag" />
    <result column="filter_json" jdbcType="VARCHAR" property="filterJson" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>
    <resultMap id="ReportPropertyDTO" type="com.pes.jd.model.DTO.ReportPropertyDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="category_id" jdbcType="BIGINT" property="categoryId" />
        <result column="title" jdbcType="VARCHAR" property="title" />
        <result column="titleLong" jdbcType="VARCHAR" property="titlelong" />
        <result column="property" jdbcType="VARCHAR" property="property" />
        <result column="filter_flag" jdbcType="TINYINT" property="filterFlag" />
        <result column="filter_json" jdbcType="VARCHAR" property="filterJson" />
        <result column="source" jdbcType="VARCHAR" property="source" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="desc" jdbcType="VARCHAR" property="desc" />
        <result column="categoryName" jdbcType="VARCHAR" property="categoryName" />
    </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_id, title, property, filter_flag, filter_json, source, status, type
  </sql>
  <select id="searchAllWithCategory" resultMap="ReportPropertyDTO" parameterType="java.lang.String">
    select
    property.*,category.title categoryName
    from pes_report_property property , pes_report_category category
    where property.category_id =  category.id
    <if test="name != null">
      and titleLong Like concat('%',#{name},'%')
    </if>
    AND property.status = 1
    AND category.status = 1
    <if test="property != null and property.size() != 0">
      AND property.property in
      <foreach collection="property" item="pro" open="(" close=")" separator=",">
        #{pro}
      </foreach>
    </if>
    <if test="type != null">
      and property.type = #{type}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_report_property
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_report_property
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ReportPropertyDO">
    insert into pes_report_property (id, category_id, title, 
      property, filter_flag, filter_json, 
      source, status, type
      )
    values (#{id,jdbcType=BIGINT}, #{categoryId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, 
      #{property,jdbcType=VARCHAR}, #{filterFlag,jdbcType=TINYINT}, #{filterJson,jdbcType=VARCHAR}, 
      #{source,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ReportPropertyDO">
    insert into pes_report_property
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="property != null">
        property,
      </if>
      <if test="filterFlag != null">
        filter_flag,
      </if>
      <if test="filterJson != null">
        filter_json,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="property != null">
        #{property,jdbcType=VARCHAR},
      </if>
      <if test="filterFlag != null">
        #{filterFlag,jdbcType=TINYINT},
      </if>
      <if test="filterJson != null">
        #{filterJson,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ReportPropertyDO">
    update pes_report_property
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="property != null">
        property = #{property,jdbcType=VARCHAR},
      </if>
      <if test="filterFlag != null">
        filter_flag = #{filterFlag,jdbcType=TINYINT},
      </if>
      <if test="filterJson != null">
        filter_json = #{filterJson,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ReportPropertyDO">
    update pes_report_property
    set category_id = #{categoryId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      property = #{property,jdbcType=VARCHAR},
      filter_flag = #{filterFlag,jdbcType=TINYINT},
      filter_json = #{filterJson,jdbcType=VARCHAR},
      source = #{source,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>