<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesAppVersionMenuMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesAppVersionMenuDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_version_id" jdbcType="BIGINT" property="appVersionId"/>
        <result column="resource_menu_id" jdbcType="BIGINT" property="resourceMenuId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
    </resultMap>

    <resultMap id="PesAppVersionMenuDTO" type="com.pes.jd.model.DTO.PesAppVersionMenuDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_version_id" jdbcType="BIGINT" property="appVersionId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="status" jdbcType="TINYINT" property="status"/>

    </resultMap>

    <select id="selectsSystemMenuByStatus" parameterType="long" resultMap="PesAppVersionMenuDTO">
       SELECT * from
	   (SELECT
        id,app_version_id,`name`,`status`
        FROM pes_app_version_menu
        WHERE status=#{status}
        AND app_version_id=#{appVersionId}) as a
	    WHERE id not in (SELECT id FROM pes_app_version_menu WHERE is_function_spot =1
		AND  status=#{status}
	    AND app_version_id=#{appVersionId} )
    </select>


    <select id="selectAppVersionMenuByVersionId" parameterType="java.lang.Long" resultMap="PesAppVersionMenuDTO">
        SELECT id,app_version_id,`name`,`status`
        FROM pes_app_version_menu
        WHERE
        app_version_id=#{appVersionId}
        AND
        NOT
        status=3
    </select>


    <select id="selectFunctionSpotByVersionId" parameterType="long" resultMap="PesAppVersionMenuDTO">
        SELECT  id,app_version_id,`name`,`status`
        FROM pes_app_version_menu
        WHERE
        app_version_id=#{appVersionId}
        AND
        is_function_spot = 1
        <if test="status!=null">
        AND status =#{status}
        </if>
    </select>


    <select id="selectVersionNameLst" parameterType="list" resultType="string">
        SELECT `name` FROM pes_app_version_menu where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </select>

    <select id="selectVersionIdByName" resultType="long" >
        select id from pes_app_version_menu where `name`=#{name}
        and app_version_id=#{versionId}
    </select>


    <update id="batchUpdateVersionControl">
        UPDATE
        pes_app_version_menu
        <set>
            status= #{status}
        </set>
        WHERE
        <if test="ids!=null">
            id
            <if test="status==2">
                NOT IN
            </if>
            <if test="status==1">
                IN
            </if>
        </if>
        <if test="ids!=null">
            <if test="ids.size()!=0">
                <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="ids!=null">
            AND
        </if>
         app_version_id=#{versionIdType}
        AND
        <if test="status==1">
            status = 2
        </if>
        <if test="ids!=null">
            <if test="status==2">
                status = 1
            </if>
        </if>
        <if test="ids==null">
           NOT
            status = 3
        </if>
    </update>

    <select id="selectVersionCodeById" resultType="string">
        SELECT item_code FROM pes_itemcode_subuser WHERE app_version_id=#{versionId}
    </select>
    <select id="selectMenuNameByWhiteId" resultType="java.lang.String">
        SELECT `name` FROM pes_app_version_menu WHERE white_id=#{whiteId}
    </select>
</mapper>