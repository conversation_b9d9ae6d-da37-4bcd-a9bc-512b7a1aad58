<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.MarketingActivityV2Mapper">

    <sql id="Base_Column_List">
        id,
        activity_name,
        activity_content,
        start_date,
        end_date,
        order_type,
        effect_date,
        frequency,
        version,
        url,
        enable_switch,
        activity_sort,
        created
    </sql>

    <insert id="insert" parameterType="com.pes.jd.model.DO.MarketingActivityV2">
        INSERT INTO pes_marketing_activity_v2 (
            activity_name,
            activity_content,
            start_date,
            end_date,
            order_type,
            effect_date,
            frequency,
            version,
            url,
            enable_switch,
            activity_sort,
            created
        )
        VALUES (
            #{activityName, jdbcType=VARCHAR},
            #{activityContent, jdbcType=VARCHAR},
            #{startDate, jdbcType=TIMESTAMP},
            #{endDate, jdbcType=TIMESTAMP},
            #{orderType, jdbcType=INTEGER},
            #{effectDate, jdbcType=INTEGER},
            #{frequency, jdbcType=INTEGER},
            #{version, jdbcType=INTEGER},
            #{url, jdbcType=VARCHAR},
            #{enableSwitch, jdbcType=BIT},
            #{activitySort, jdbcType=INTEGER},
            #{created, jdbcType=TIMESTAMP}
        )
    </insert>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from pes_marketing_activity_v2
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.MarketingActivityV2">
        UPDATE pes_marketing_activity_v2
        <set>
            <if test="activityName != null and activityName != ''">
                activity_name = #{activityName, jdbcType=VARCHAR},
            </if>
            <if test="activityContent != null and activityContent != ''">
                activity_content = #{activityContent, jdbcType=VARCHAR},
            </if>
            <if test="startDate != null">
                start_date = #{startDate, jdbcType=TIMESTAMP},
            </if>
            <if test="endDate != null">
                end_date = #{endDate, jdbcType=TIMESTAMP},
            </if>
            <if test="orderType != null">
                order_type = #{orderType, jdbcType=INTEGER},
            </if>
            <if test="effectDate != null">
                effect_date = #{effectDate, jdbcType=INTEGER},
            </if>
            <if test="frequency != null">
                frequency = #{frequency, jdbcType=INTEGER},
            </if>
            <if test="version != null">
                version = #{version, jdbcType=INTEGER},
            </if>
            <if test="url != null and url != ''">
                url = #{url, jdbcType=VARCHAR},
            </if>
            <if test="enableSwitch != null">
                enable_switch = #{enableSwitch, jdbcType=BIT},
            </if>
            <if test="activitySort != null">
                activity_sort = #{activitySort, jdbcType=INTEGER},
            </if>
            <if test="created != null">
                created = #{created, jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE id = #{id, jdbcType=BIGINT}
    </update>

    <select id="getByPrimaryKey" parameterType="java.lang.Long" resultType="com.pes.jd.model.DO.MarketingActivityV2">
        select
        <include refid="Base_Column_List"/>
        from pes_marketing_activity_v2
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectEnableActivity" resultType="com.pes.jd.model.DO.MarketingActivityV2" parameterType="java.util.HashMap">
        select
        <include refid="Base_Column_List"/>
        from pes_marketing_activity_v2
        where enable_switch = 1
        and #{currentTime, jdbcType=TIMESTAMP} between start_date and end_date

        order by activity_sort
    </select>

    <select id="selectActivityByActivityNameAndDate" resultType="com.pes.jd.model.DO.MarketingActivityV2">
        select
        <include refid="Base_Column_List"/>
        from pes_marketing_activity_v2
        where created between #{startDate} and #{endDate}
        <if test="activityName != null and activityName != ''">
            and activity_name like CONCAT('%', #{activityName},'%')
        </if>
        order by created desc
    </select>

    <select id="queryListByParams" parameterType="com.pes.jd.model.DO.MarketingActivityV2" resultType="com.pes.jd.model.DO.MarketingActivityV2">
        SELECT
        <include refid="Base_Column_List" />
        FROM pes_marketing_activity_v2
        WHERE 1 = 1
        <if test="effectDate != null">
            AND effect_date = #{effectDate, jdbcType=INTEGER}
        </if>
        <if test="frequency != null">
            AND frequency = #{frequency, jdbcType=INTEGER}
        </if>
        <if test="version != null">
            AND version = #{version, jdbcType=INTEGER}
        </if>
        <if test="enableSwitch != null">
            AND enable_switch = #{enableSwitch, jdbcType=BIT}
        </if>
    </select>
</mapper>