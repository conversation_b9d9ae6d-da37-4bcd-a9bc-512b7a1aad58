<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.SmsOrderMapper">
  <resultMap id="smsOrderDTO" type="com.pes.jd.model.DTO.SmsOrderDTO">
    <id column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="shop_title" jdbcType="VARCHAR" property="shopTitle" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="order_fee" jdbcType="DOUBLE" property="orderFee" />
    <result column="pay_way" jdbcType="TINYINT" property="payWay" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
  </resultMap>
  <sql id="Base_Column_List">
order_id,shop_id,shop_title,operator,date,number,order_fee,pay_way,pay_type,order_type,create_time,order_status
    </sql>

  <insert id="recharge" parameterType="com.pes.jd.model.DO.SmsOrderDO">
    insert into pes_shop_sms_order (order_id, shop_id, shop_title, 
      operator, date, number, 
      order_fee, pay_way, pay_type, create_time, order_status, order_type
      )
    values (#{orderId,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, #{shopTitle,jdbcType=VARCHAR},
      #{operator,jdbcType=VARCHAR}, #{date,jdbcType=TIMESTAMP}, #{number,jdbcType=INTEGER},
      #{orderFee,jdbcType=DOUBLE}, #{payWay,jdbcType=TINYINT}, #{payType,jdbcType=TINYINT},
      #{createTime,jdbcType=TIMESTAMP}, #{orderStatus,jdbcType=TINYINT}, #{orderType,jdbcType=TINYINT}
      )
  </insert>
  <update id="updateDateAndStatusByOrderId">
    update
        pes_shop_sms_order
    <set>
      <if test="status != null">
        order_status=#{status},
      </if>
      <if test="date != null">
        date=#{date}
      </if>
    </set>
    where order_id = #{orderId}
    </update>

    <select id="queryRechargeRecord" resultMap="smsOrderDTO">
        select order_id, shop_id, shop_title,
            operator, date, number, order_fee, pay_way, pay_type, order_type, create_time, order_status
        from pes_shop_sms_order
        where 1=1
          <if test="nick != null and nick != '' ">
            and shop_title like CONCAT('%', #{nick},'%')
          </if>
          <if test="orderId != null and orderId != '' ">
            and order_id = #{orderId,jdbcType=VARCHAR}
          </if>
          <if test="payWay != -1">
            and pay_way = #{payWay,jdbcType=TINYINT}
          </if>
          <if test="orderStatus != -1">
            and order_status = #{orderStatus,jdbcType=TINYINT}
          </if>
          <if test="orderStatus == -1">
            and order_status in (1, 2)
          </if>
          and  date between #{startDate,jdbcType=TIMESTAMP} and #{endDate,jdbcType=TIMESTAMP}
          order by date desc
  </select>
  <select id="selectSmsOrderByShopId" resultMap="smsOrderDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM pes_shop_sms_order
    WHERE shop_id =#{shopId}
    AND order_status=1
    ORDER BY date desc
  </select>

  <select id="selectOrderStatusBYOrderId" resultType="java.lang.Byte">
    select order_status FROM pes_shop_sms_order
    where order_id=#{orderId}
  </select>
  <select id="selectSmsOrderByOrderId" resultMap="smsOrderDTO">
    SELECT
    <include refid="Base_Column_List" />
    FROM pes_shop_sms_order
    WHERE order_id =#{orderId}
  </select>

  <select id="selectSmsSuccessOrderCountByShopId" resultType="int">
    select count(order_id)
     FROM pes_shop_sms_order
    WHERE shop_id =#{shopId}
    AND order_status=1
  </select>
</mapper>