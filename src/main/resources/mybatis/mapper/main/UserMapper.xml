<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.UserMapper">

	<resultMap id="UserDO" type="com.pes.jd.model.DO.User">
		<id column="id" jdbcType="BIGINT" property="id" />
		<id column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="nick" jdbcType="VARCHAR" property="nick" />
		<result column="show_nick" jdbcType="VARCHAR" property="showNick" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="type" jdbcType="VARCHAR" property="type" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="subscribe_dead_line" jdbcType="TIMESTAMP" property="subscribeDeadLine" />
		<result column="item_code" jdbcType="VARCHAR" property="itemCode" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="lock_flag" jdbcType="INTEGER" property="lockFlag" />
		<result column="read_maessage_time" jdbcType="TIMESTAMP" property="readMaessageTime" />
		<result column="multi_shop_switch" jdbcType="BIT" property="multiShopSwitch" />
		<result column="is_main_account" jdbcType="BIT" property="mainAccount" />
	</resultMap>

	<sql id="base_field">
		id,user_id, shop_id, nick, type, created, subscribe_dead_line, item_code,
		status, lock_flag,is_main_account,multi_shop_switch,show_nick
	</sql>

	<insert id="insertUser" parameterType="com.pes.jd.model.DO.User" useGeneratedKeys="true" keyProperty="id">
	 INSERT INTO pes_user (
	 user_id,shop_id,nick,show_nick,session_key,
	 created,subscribe_dead_line,item_code,
	 status,type,multi_shop_switch,is_main_account,open_id,ip,refresh_session_key)
	VALUES
		(
			#{userId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR},
			#{showNick,jdbcType=VARCHAR},
			#{sessionKey,jdbcType=VARCHAR},
			#{created,jdbcType=TIMESTAMP},
			#{subscribeDeadLine,jdbcType=TIMESTAMP}, #{itemCode,jdbcType=VARCHAR},
			#{status,jdbcType=VARCHAR},
			#{type,jdbcType=TIMESTAMP},
			#{multiShopSwitch,jdbcType=BIT},#{mainAccount,jdbcType=BIT},#{openId,jdbcType=VARCHAR},#{ip,jdbcType=VARCHAR},
			#{refreshSessionKey,jdbcType=VARCHAR}
		)
	</insert>
	
	<delete id="deleteUserById" parameterType="java.lang.Long">
		DELETE FROM pes_user
		WHERE 
			id = #{id,jdbcType=BIGINT}
	</delete>

	<delete id="deleteUserByNick" parameterType="java.lang.String">
		DELETE FROM pes_user
		WHERE 
			nick = #{userNick}
	</delete>
	<update id="updateUserNickById" parameterType="map">
		update pes_user set nick=#{userNick} , show_nick=#{showNick} where id=#{id}
	</update>
	
	<update id="updateUserById" parameterType="com.pes.jd.model.DO.User">
		UPDATE pes_user 
		<set>
		 <if test="userId != null">
				user_id = #{userId,jdbcType=BIGINT},
			</if>
			<if test="sessionKey != null and sessionKey!=''">
				session_key = #{sessionKey,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="subscribeDeadLine != null">
				subscribe_dead_line = #{subscribeDeadLine,jdbcType=TIMESTAMP},
			</if>
			<if test="authDeadLine != null">
				auth_dead_line = #{authDeadLine,jdbcType=TIMESTAMP},
			</if>
			<if test="itemCode != null and itemCode!='' ">
				item_code = #{itemCode,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="lockFlag != null">
				lock_flag = #{lockFlag,jdbcType=INTEGER},
			</if>
			<if test="readMaessageTime != null">
				read_maessage_time = #{readMaessageTime,jdbcType=TIMESTAMP},
			</if>
			<if test="multiShopSwitch != null">
				multi_shop_switch = #{multiShopSwitch,jdbcType=BIT},
			</if>
			<if test="mainAccount != null">
				is_main_account = #{mainAccount,jdbcType=BIT},
			</if> 
			<if test="showNick!=null and showNick!=''">
				show_nick = #{showNick,jdbcType=VARCHAR},
			</if>
			<if test="nick!=null and nick!=''">
				nick = #{nick,jdbcType=VARCHAR},
			</if>
			<if test="openId!=null and openId!=''">
				open_id=#{openId,jdbcType=VARCHAR},

			</if>
			<if test="ip!=null and ip!=''">
				ip=#{ip,jdbcType=VARCHAR},
			</if>
			<if test="refreshSessionKey != null and refreshSessionKey!=''">
				refresh_session_key = #{refreshSessionKey,jdbcType=VARCHAR}
			</if>
		</set>
		<where>
				id = #{id}
		</where>
		
		
	</update>

	<update id="saveUserInterfaceType" parameterType="map">
		update pes_user set interface_type=#{interfaceType} where nick=#{nick}
	</update>
	
	<select id="getUserById" parameterType="java.lang.Long" resultMap="UserDO">
		SELECT
		<include refid="base_field" />
		FROM pes_user
		WHERE id = #{id,jdbcType=BIGINT}
	</select>
	<select id="getUserInfoByShopIdByUserNick"  resultType="com.pes.jd.model.DTO.ShopUserDTO">
		SELECT id userId,user_id sellerId,shop_id shopId, session_key sessionKey, nick nick,show_nick showNick, created, subscribe_dead_line subscribeDeadLine, 
			status status, type type, lock_flag lockFlag, item_code  itemCode, multi_shop_switch  multiShopSwitch,is_main_account as mainAccount,open_id  as openId,
			refresh_session_key  as refreshSessionKey
		FROM pes_user
		WHERE  nick=#{pin} and shop_id=#{shopId}
	</select>

	<select id="getSubUserByUserNick"  resultType="com.pes.jd.model.DTO.ShopUserDTO">
		SELECT id userId,user_id sellerId,shop_id shopId, session_key sessionKey, nick nick,show_nick showNick, created, subscribe_dead_line subscribeDeadLine,
			status status, type type, lock_flag lockFlag, item_code  itemCode, multi_shop_switch  multiShopSwitch,is_main_account as mainAccount,open_id openId
		FROM pes_user
		WHERE  nick=#{pin} and is_main_account = 0
	</select>


	<select id="selectUserByShopIdByMainAccount" parameterType="java.lang.Long" resultType="com.pes.jd.model.DTO.ShopUserDTO">
	SELECT id userId, user_id sellerId,shop_id shopId,session_key sessionKey, nick nick,show_nick showNick, created, subscribe_dead_line subscribeDeadLine, 
			status status, type type, lock_flag lockFlag, item_code as itemCode, multi_shop_switch as multiShopSwitch,
			is_main_account as mainAccount,open_id as openId, auth_dead_line as authDeadLine, interface_type as interfaceType,refresh_session_key  as refreshSessionKey
		FROM pes_user
		WHERE shop_id = #{shopId,jdbcType=BIGINT} and is_main_account=1
	</select>
	<select id="selectUserByshopId" resultType="com.pes.jd.model.DTO.ShopUserDTO">
		select 
			id userId,user_id sellerId, shop_id shopId,session_key sessionKey, nick nick,show_nick showNick, created, subscribe_dead_line subscribeDeadLine, 
			status status, type type, lock_flag lockFlag, item_code as itemCode, multi_shop_switch as multiShopSwitch, auth_dead_line authDeadLine,
			is_main_account as mainAccount,refresh_session_key as refreshSesionKey
 			from pes_user
 			<where>
	 			<if test="shopId!=null and shopId!=''">
	 				shop_id=#{shopId}
	 			</if>
 			</where>
 			
 			
	</select>


	<select id="selectShopIdByVersionId" resultType="long">
		select shop_id FROM pes_user where
		pes_user.item_code=#{itemCode}
		and is_main_account=1
	</select>

	<select id="getUserInterfaceTypeByNick" resultType="integer">
		select interface_type FROM pes_user where
		pes_user.nick=#{nick}
		limit 0,1
	</select>

    <select id="getUserByUserNick" resultType="com.pes.jd.model.DTO.ShopUserDTO">
		select id userId,user_id sellerId,shop_id shopId, session_key sessionKey, nick nick,show_nick showNick, created, subscribe_dead_line subscribeDeadLine,
			status status, type type, lock_flag lockFlag, item_code  itemCode, multi_shop_switch  multiShopSwitch,is_main_account as mainAccount,open_id  as openId,
			refresh_session_key  as refreshSessionKey
		FROM pes_user where
		pes_user.nick=#{nick}
	</select>
	<select id="getSessionkey" resultType="java.lang.String">
		select session_key
		from pes_user where
		shop_id=#{shopId}
		<if test="nick!=null">
		AND	nick=#{nick}
		</if>
		<if test="mainAccount!=null">
		AND is_main_account=#{mainAccount}
		</if>


	</select>
    <select id="getMainAccountList" resultType="com.pes.jd.model.DTO.ShopUserDTO">
		SELECT user_id, shop_id, nick, type, created, subscribe_dead_line, item_code,
		status, lock_flag, is_main_account, multi_shop_switch
		FROM pes_user
		<where>
			is_main_account=1
			AND status ='active'
			<if test="shopId!=null">
				AND  shop_id=#{shopId}
			</if>
		</where>
	</select>
</mapper>