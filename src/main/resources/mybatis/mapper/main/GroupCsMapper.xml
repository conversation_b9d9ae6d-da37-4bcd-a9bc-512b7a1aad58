<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.GroupCsMapper" >
  <resultMap id="GroupCsDO" type="com.pes.jd.model.DO.GroupCs" >
    <id column="group_id" property="groupId" jdbcType="BIGINT" />
    <id column="nick" property="nick" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
  </resultMap>

  <resultMap id="GroupCsDTO" type="com.pes.jd.model.DTO.GroupCsDTO" >
    <id column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="nick" property="nick" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
     <result column="group_name" property="groupName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="base_field" >
    group_id, nick, shop_id
  </sql>
  
  <insert id="insertGroupCs" parameterType="com.pes.jd.model.DO.GroupCs" >
    INSERT INTO pes_group_cs (group_id, nick, shop_id)
    VALUES 
    (
   	 	 #{groupId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR},
   	 	 #{shopId,jdbcType=BIGINT}
    )
  </insert>
  <insert id="batchInsertGroupcs"  parameterType="list">
	INSERT INTO pes_group_cs(group_id,nick,shop_id)
	VALUES
	<foreach collection="list" item="itm" separator="," index="index">
	(#{itm.groupId},#{itm.nick},#{itm.shopId})
	</foreach>
</insert>
  <delete id="deleteGroupCsByGroupId" parameterType="long" >
    DELETE from pes_group_cs
    WHERE group_id = #{groupId,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteGroupCsByNickByShopId" parameterType="map" >
    DELETE from pes_group_cs
    WHERE shop_id = #{shopId,jdbcType=BIGINT}
    and nick=#{nick,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteGroupCsByGroupIdsAndCsId" parameterType="map">
	DELETE FROM pes_group_cs
	WHERE
	group_id IN
	<foreach collection="groupIds" item="groupId" separator="," open="(" close=")">
		#{groupId}
	</foreach>
	AND nick = #{nick,jdbcType=VARCHAR}
</delete>
  <update id="updateGroupCsBySelective" parameterType="com.pes.jd.model.DO.GroupCs" >
    UPDATE pes_group_cs
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
    </set>
    WHERE group_id = #{groupId,jdbcType=BIGINT}
    AND nick = #{nick,jdbcType=VARCHAR}
  </update>
  <update id="updateGroupCsByshopIdByNick" parameterType="map">
   UPDATE pes_group_cs
    <set >
    	group_id = #{groupId,jdbcType=BIGINT}
    </set>
    WHERE  shop_id = #{shopId,jdbcType=BIGINT}
    AND nick = #{nick,jdbcType=VARCHAR}
  </update>
    <select id="getGroupCsByGroupIdAndCsId" parameterType="map" resultMap="GroupCsDTO">
        SELECT
        <include refid="base_field" />
        FROM pes_group_cs
        WHERE
        group_id = #{groupId}
        AND nick = #{nick}
    </select>
    <select id="selectGroupCsByGroupId" resultMap="GroupCsDTO" parameterType="map" >
        SELECT
        <include refid="base_field" />
        FROM pes_group_cs
        WHERE group_id = #{groupId,jdbcType=BIGINT}
    </select>
  <select id="selectGroupCsByGroupIdMap" resultType="java.util.List" parameterType="java.util.Map" >
    SELECT
      nick ,shop_id shopId
    FROM pes_group_cs
    WHERE group_id = #{groupId,jdbcType=BIGINT}
  </select>

<select id="selectGroupCsByShopId" parameterType="map" resultMap="GroupCsDTO">
		select  pge.group_id, pge.nick, pge.shop_id from pes_group_cs pge 
		where pge.shop_id=#{shopId,jdbcType=BIGINT}
</select>


<select id="selectGroupCsByGroupParam" parameterType="map" resultMap="GroupCsDTO">
		SELECT  pge.group_id,
		 pge.nick, 
		 pge.shop_id
		 from pes_group_cs pge
		 WHERE
		 	pge.shop_id=#{param.shopId} 
		 <if test="param.csNick!=null and param.csNick!=''">
		   and pge.nick=#{param.csNick}
		 </if>
		
</select>

<update id="updateOldCsNickByNewCsNick" parameterType="map">
	<foreach collection="groupCsLst" item="itm" separator=";">
		update pes_group_cs set nick=#{itm.nick} where group_id=#{itm.groupId} and shop_id=#{itm.shopId}
	</foreach>
</update>

<select id="selectGroupCsByCsNick" resultMap="GroupCsDTO">
	select * from pes_group_cs where nick=#{csNick} and shop_id=#{shopId}
</select>

    <delete id="deleteGroupCsByCsNickLst">
        delete from pes_group_cs
        WHERE
             shop_id=#{shopId}
        AND  nick in
        <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
               #{csNick}
        </foreach>
 </delete>


</mapper>