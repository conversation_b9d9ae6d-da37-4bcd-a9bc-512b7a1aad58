<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopWipFunctionModuleMapper">
  <resultMap id="ShopWipFnctionModuleDO" type="com.pes.jd.model.DO.ShopWipFunctionModuleDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="function" jdbcType="VARCHAR" property="function" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>

  <resultMap id="ShopWipFunctionModuleDTO" type="com.pes.jd.model.DTO.ShopWipFunctionModuleDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="function" jdbcType="VARCHAR" property="function" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>


  <sql id="base_field">
    id, function, `desc`, `status`
  </sql>

  <select id="selectAllWipFunction" resultMap="ShopWipFunctionModuleDTO">
    SELECT
    <include refid="base_field"/>
    FROM
    pes_shop_wip_function_module
  </select>

  <select id="selectWipFunctionModuleById" parameterType="list">

    SELECT function FROM pes_shop_wip_function_module
    WHERE id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
       #{id}
    </foreach>
  </select>

  <select id="selectHaveWipByShopIdAndFuncationName" resultType="int">
      SELECT count(1)
      FROM pes_function_module_white white,
           pes_shop_wip_function_module wip
      WHERE wip.id = white.function_id
        AND white.shop_id = #{shopId}
        AND wip.`function` = #{function}
  </select>
</mapper>