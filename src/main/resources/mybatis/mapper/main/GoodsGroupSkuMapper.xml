<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.GoodsGroupSkuMapper" >
  <resultMap id="GoodsGroupSkuDO" type="com.pes.jd.model.DO.GoodsGroupSkuDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
      <result column="dimension" jdbcType="TINYINT" property="dimension" />
  </resultMap>
   <resultMap id="GoodsGroupSkuDTO" type="com.pes.jd.model.DTO.GoodsGroupSkuDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="group_id" property="groupId" jdbcType="BIGINT" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
       <result column="dimension" jdbcType="TINYINT" property="dimension" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id,group_id, sku_id,dimension
  </sql>
 
  <insert id="insertGoodsGroupSku" parameterType="com.pes.jd.model.DO.GoodsGroupSkuDO" >
    insert into pes_goods_group_sku (id,shop_id, group_id, sku_id
      )
    values (#{id,jdbcType=BIGINT},#{shopId,jdbcType=BIGINT}, #{groupId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="batchInsertGoodsGroupSku" parameterType="map">
  	INSERT INTO pes_goods_group_sku (shop_id,group_id, sku_id,dimension) VALUES
    <foreach collection="goodsGroupSkuLst" item="itm"  separator=",">
      (#{itm.shopId,jdbcType=BIGINT},#{itm.groupId,jdbcType=BIGINT}, #{itm.skuId,jdbcType=BIGINT}, #{itm.dimension,jdbcType=TINYINT})
     </foreach>
  </insert>
  
 <delete id="deleteGoodsGroupSkuByGroupId" parameterType="map">
 	DELETE FROM pes_goods_group_sku
    WHERE group_id = #{groupId,jdbcType=BIGINT}
 </delete>
  <delete id="deleteGoodsGroupSkuById" parameterType="java.lang.Long" >
    delete from pes_goods_group_sku
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateGoodsGroupSku" parameterType="com.pes.jd.model.DO.GoodsGroupSkuDO" >
    update pes_goods_group_sku
    <set >
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="selectGoodsGroupSkuById" resultMap="GoodsGroupSkuDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_goods_group_sku
    where id = #{id,jdbcType=BIGINT}
  </select>
  
   <select id="selectGoodsGroupSkuByGroupId" resultMap="GoodsGroupSkuDTO" parameterType="map" >
    select 
    <include refid="base_field" />
    from pes_goods_group_sku
    where group_id = #{groupId,jdbcType=BIGINT}
  </select>
  <select id="selectGoodsGroupSkuByShopId" resultMap="GoodsGroupSkuDTO" parameterType="map" >
    select 
    <include refid="base_field" />
    from pes_goods_group_sku
    where shop_id = #{shopId,jdbcType=BIGINT}
  </select>
</mapper>