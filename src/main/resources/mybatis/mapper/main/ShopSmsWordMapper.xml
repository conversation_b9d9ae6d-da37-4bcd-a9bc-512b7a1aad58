<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSmsWordMapper">
    <resultMap id="ShopSmsWordDO" type="com.pes.jd.model.DO.ShopSmsWordDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="sms_sign" property="smsSign" jdbcType="VARCHAR"/>
        <result column="sms_suffix" property="smsSuffix" jdbcType="VARCHAR"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR"/>
        <result column="is_using" property="using" jdbcType="BIT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>

    </resultMap>
    <resultMap id="ShopSmsWord" type="com.pes.jd.ms.domain.Data.master.ShopSmsWord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="sms_sign" property="smsSign" jdbcType="VARCHAR"/>
        <result column="sms_suffix" property="smsSuffix" jdbcType="VARCHAR"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR"/>
        <result column="is_using" property="using" jdbcType="BIT"/>
    </resultMap>
    <resultMap id="ShopSmsWordDTO" type="com.pes.jd.model.DTO.ShopSmsWordDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="sms_sign" property="smsSign" jdbcType="VARCHAR"/>
        <result column="sms_suffix" property="smsSuffix" jdbcType="VARCHAR"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="audit_status" property="auditStatus" jdbcType="INTEGER"/>
        <result column="audit_time" property="auditTime" jdbcType="TIMESTAMP"/>
        <result column="audit_remark" property="auditRemark" jdbcType="VARCHAR"/>
        <result column="is_using" property="using" jdbcType="BIT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="title" property="shopNick" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="base_field">
    id, shop_id, type, content, sms_sign, sms_suffix, created, modified, audit_status, 
    audit_time, audit_remark,is_using,cs_nick,name,template_id
  </sql>
    <select id="selectShopSmsWordById" resultMap="ShopSmsWord" parameterType="java.lang.Long">
        select
        <include refid="base_field"/>
        from pes_shop_sms_word
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteShopSmsWordById" parameterType="java.lang.Long">
    delete from pes_shop_sms_word
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insertShopSmsWord" keyProperty="id" parameterType="com.pes.jd.model.DO.ShopSmsWordDO" useGeneratedKeys="true">
    insert into pes_shop_sms_word (id, shop_id, type, 
      content, sms_sign, sms_suffix, 
      created, modified, audit_status, 
      audit_time, audit_remark,is_using,name,cs_nick,template_id)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER},
      #{content,jdbcType=VARCHAR}, #{smsSign,jdbcType=VARCHAR}, #{smsSuffix,jdbcType=VARCHAR}, 
      #{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}, #{auditStatus,jdbcType=INTEGER},
      #{auditTime,jdbcType=TIMESTAMP}, #{auditRemark,jdbcType=VARCHAR},#{using,jdbcType=BIT},#{name,jdbcType=VARCHAR},
      #{csNick,jdbcType=VARCHAR},#{templateId,jdbcType=VARCHAR})
  </insert>

    <update id="updateShopSmsWord" parameterType="com.pes.jd.model.DO.ShopSmsWordDO">
        update pes_shop_sms_word
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="content != null and content!=''">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name!=''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="csNick != null and csNick!=''">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="smsSign != null and smsSign!=''">
                sms_sign = #{smsSign,jdbcType=VARCHAR},
            </if>
            <if test="smsSuffix != null and smsSuffix!=''">
                sms_suffix = #{smsSuffix,jdbcType=VARCHAR},
            </if>
            <if test="created != null">
                created = #{created,jdbcType=TIMESTAMP},
            </if>
            <if test="modified != null">
                modified = #{modified,jdbcType=TIMESTAMP},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=TINYINT},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditRemark != null">
                audit_remark = #{auditRemark,jdbcType=VARCHAR},
            </if>
            <if test="using != null">
                is_using = #{using,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateToNoUsingByShopId">
    update pes_shop_sms_word
    set is_using = 0
    where shop_id = #{shopId}
  </update>

    <select id="selectShopSmsWordByShopId" resultMap="ShopSmsWord">
        SELECT
        <include refid="base_field"/>
        FROM pes_shop_sms_word
        <where>
            shop_id=#{shopId}
            <if test="type!=null and type!=''">
                AND type=#{type}
            </if>
        </where>
        order by is_using desc,created desc
    </select>

    <select id="selectShopSmsWordByIdByType" resultMap="ShopSmsWord">
        SELECT
        <include refid="base_field"/>
        FROM pes_shop_sms_word
        <where>
            id=#{id}
            <if test="type!=null and type!=''">
                AND type=#{type}
            </if>
        </where>
        order by is_using desc,created desc
    </select>

    <update id="updateShopWordUsingByIds">
        update pes_shop_sms_word set is_using=#{using}
        <where>
            id in
            <foreach collection="wordIdLst" item="wordId" open="(" close=")" separator=",">
                #{wordId}
            </foreach>
        </where>
    </update>

    <select id="selectShopSmsWordByShopIdByAuditstatus" resultMap="ShopSmsWord">
        select
        id,
        shop_id,
        type,
        content,
        name,
        is_using,
        audit_time
        from pes_shop_sms_word
        <where>
         shop_id=#{shopId} AND audit_status in (#{statusOne},#{statusTwo})
        </where>
        order by audit_time desc
    </select>

    <select id="selectShopSmsWordByShopIdByMap"  parameterType="java.util.Map" resultMap="ShopSmsWord">
        select
        id,
        shop_id,
        type,
        content,
        name,
        is_using,
        audit_time
        from pes_shop_sms_word
        WHERE
            shop_id=#{shopId} AND audit_status in
        <foreach collection="list" item="status" index="index" open="(" close=")" separator=",">
            #{status}
        </foreach>
        order by audit_time desc
    </select>

    <select id="selectSmsWordByNick" resultMap="ShopSmsWordDTO">
        SELECT
        w.id, w.shop_id, w.type, w.content, w.sms_sign, w.sms_suffix, w.created, w.modified, w.audit_status,
        w.audit_time, w.audit_remark, w.is_using, w.cs_nick, s.title
        FROM pes_shop_sms_word w, pes_shop s
        <where>
            w.shop_id = s.shop_id
            <if test="nick!=null and nick!=''">
                AND (s.seller_nick LIKE CONCAT('%', #{nick},'%')
                OR s.title LIKE CONCAT('%', #{nick},'%'))
            </if>
        </where>
        ORDER BY w.created DESC
    </select>

    <select id="queryNotAiditSmsWordCount" resultType="int">
        SELECT
        count(1)
        FROM pes_shop_sms_word
        <where>
            audit_status = 1
        </where>
    </select>

    <select id="selectSmsWordCountByShopIdByName" resultType="int">
        select count(id) from pes_shop_sms_word
        <where>
            shop_id=#{shopId}
            and name=#{name}
            and id!=#{id}
        </where>
    </select>

    <select id="selectWordByShopIds" resultMap="ShopSmsWordDTO">
        select * from pes_shop_sms_word
        where shop_id in
        <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
            #{shopId}
        </foreach>
    </select>

    <select id="queryShopSmsWordTemplateIdById" resultType="java.lang.String">
        select template_id
        FROM pes_shop_sms_word
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="queryShopSmsWordById" resultMap="ShopSmsWordDTO">
        select
        <include refid="base_field"/>
        from pes_shop_sms_word
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="searchByIds" resultMap="ShopSmsWordDTO">
        select * from pes_shop_sms_word
        where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

</mapper>