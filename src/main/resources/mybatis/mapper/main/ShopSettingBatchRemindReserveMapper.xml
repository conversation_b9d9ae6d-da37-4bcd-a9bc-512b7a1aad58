<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSettingBatchRemindReserveMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="is_reserve_remind" property="isReserveRemind" jdbcType="BIT" />
    <result column="rse_remind_dot" property="rseRemindDot" jdbcType="INTEGER" />
    <result column="rse_remind_word_id" property="rseRemindWordId" jdbcType="VARCHAR" />
    <result column="is_rse_unc_remind" property="isRseUncRemind" jdbcType="BIT" />
    <result column="cons_unc_remind_dot" property="consUncRemindDot" jdbcType="INTEGER" />
    <result column="cons_unc_remind_word_id" property="consUncRemindWordId" jdbcType="VARCHAR" />
    <result column="is_sec_cons_unc_remind" property="isSecConsUncRemind" jdbcType="BIT" />
    <result column="sec_cons_unc_remind_dot" property="secConsUncRemindDot" jdbcType="INTEGER" />
    <result column="sec_cons_unc_remind_word_id" property="secConsUncRemindWordId" jdbcType="VARCHAR" />
    <result column="silent_unc_remind_dot" property="silentUncRemindDot" jdbcType="INTEGER" />
    <result column="silent_unc_remind_word_id" property="silentUncRemindWordId" jdbcType="VARCHAR" />
    <result column="silent_unc_cs_nick" property="silentUncCsNick" jdbcType="VARCHAR" />
    <result column="silent_unc_group_id" property="silentUncGroupId" jdbcType="BIGINT" />
    <result column="silent_unc_flag" property="silentUncFlag" jdbcType="TINYINT" />
    <result column="silent_unc_word_id" property="silentUncWordId" jdbcType="VARCHAR" />
    <result column="silent_unc_spare_cs_nick" property="silentUncSpareCsNick" jdbcType="VARCHAR" />
    <result column="silent_unc_spare_group_id" property="silentUncSpareGroupId" jdbcType="BIGINT" />
    <result column="is_sec_silent_unc_remind" property="isSecSilentUncRemind" jdbcType="BIT" />
    <result column="sec_silent_unc_remind_dot" property="secSilentUncRemindDot" jdbcType="INTEGER" />
    <result column="sec_silent_unc_remind_word_id" property="secSilentUncRemindWordId" jdbcType="VARCHAR" />
    <result column="cons_unrse_unc_remind_dot" property="consUnrseUncRemindDot" jdbcType="INTEGER" />
    <result column="cons_unrse_unc_remind_word_id" property="consUnrseUncRemindWordId" jdbcType="VARCHAR" />
    <result column="is_sec_cons_unrse_unc_remind" property="isSecConsUnrseUncRemind" jdbcType="BIT" />
    <result column="sec_cons_unrse_unc_remind_dot" property="secConsUnrseUncRemindDot" jdbcType="INTEGER" />
    <result column="sec_cons_unrse_unc_remind_word_id" property="secConsUnrseUncRemindWordId" jdbcType="VARCHAR" />
    <result column="is_rse_unp_remind" property="isRseUnpRemind" jdbcType="BIT" />
    <result column="cons_unp_remind_dot" property="consUnpRemindDot" jdbcType="INTEGER" />
    <result column="cons_unp_remind_word_id" property="consUnpRemindWordId" jdbcType="VARCHAR" />
    <result column="is_sec_cons_unp_remind" property="isSecConsUnpRemind" jdbcType="BIT" />
    <result column="sec_cons_unp_remind_dot" property="secConsUnpRemindDot" jdbcType="INTEGER" />
    <result column="sec_cons_unp_remind_word_id" property="secConsUnpRemindWordId" jdbcType="VARCHAR" />
    <result column="silent_unp_remind_dot" property="silentUnpRemindDot" jdbcType="INTEGER" />
    <result column="silent_unp_remind_word_id" property="silentUnpRemindWordId" jdbcType="VARCHAR" />
    <result column="silent_unp_cs_nick" property="silentUnpCsNick" jdbcType="VARCHAR" />
    <result column="silent_unp_group_id" property="silentUnpGroupId" jdbcType="BIGINT" />
    <result column="silent_unp_flag" property="silentUnpFlag" jdbcType="TINYINT" />
    <result column="silent_unp_word_id" property="silentUnpWordId" jdbcType="VARCHAR" />
    <result column="silent_unp_spare_cs_nick" property="silentUnpSpareCsNick" jdbcType="VARCHAR" />
    <result column="silent_unp_spare_group_id" property="silentUnpSpareGroupId" jdbcType="BIGINT" />
    <result column="is_sec_silent_unp_remind" property="isSecSilentUnpRemind" jdbcType="BIT" />
    <result column="sec_silent_unp_remind_dot" property="secSilentUnpRemindDot" jdbcType="INTEGER" />
    <result column="sec_silent_unp_remind_word_id" property="secSilentUnpRemindWordId" jdbcType="VARCHAR" />
    <result column="remind_start_dot" property="remindStartDot" jdbcType="INTEGER" />
    <result column="remind_end_dot" property="remindEndDot" jdbcType="INTEGER" />
    <result column="is_send_profit_point" property="isSendProfitPoint" jdbcType="BIT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, is_reserve_remind, rse_remind_dot, rse_remind_word_id, is_rse_unc_remind, 
    cons_unc_remind_dot, cons_unc_remind_word_id, is_sec_cons_unc_remind, sec_cons_unc_remind_dot, 
    sec_cons_unc_remind_word_id, silent_unc_remind_dot, silent_unc_remind_word_id, silent_unc_cs_nick, 
    silent_unc_group_id, silent_unc_flag, silent_unc_spare_cs_nick,
    silent_unc_spare_group_id, is_sec_silent_unc_remind, sec_silent_unc_remind_dot, sec_silent_unc_remind_word_id, 
    cons_unrse_unc_remind_dot, cons_unrse_unc_remind_word_id, is_sec_cons_unrse_unc_remind, 
    sec_cons_unrse_unc_remind_dot, sec_cons_unrse_unc_remind_word_id, is_rse_unp_remind, 
    cons_unp_remind_dot, cons_unp_remind_word_id, is_sec_cons_unp_remind, sec_cons_unp_remind_dot, 
    sec_cons_unp_remind_word_id, silent_unp_remind_dot, silent_unp_remind_word_id, silent_unp_cs_nick, 
    silent_unp_group_id, silent_unp_flag, silent_unp_spare_cs_nick,
    silent_unp_spare_group_id, is_sec_silent_unp_remind, sec_silent_unp_remind_dot, sec_silent_unp_remind_word_id, 
    remind_start_dot, remind_end_dot, is_send_profit_point, created, modified
  </sql>
  <select id="selectShopSettingBatchRemindReserveByShopId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pes_shop_setting_batch_remind_reserve
    where shop_id = #{shopId,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO" >
    insert into pes_shop_setting_batch_remind_reserve
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="isReserveRemind != null" >
        is_reserve_remind,
      </if>
      <if test="rseRemindDot != null" >
        rse_remind_dot,
      </if>
      <if test="rseRemindWordId != null" >
        rse_remind_word_id,
      </if>
      <if test="isRseUncRemind != null" >
        is_rse_unc_remind,
      </if>
      <if test="consUncRemindDot != null" >
        cons_unc_remind_dot,
      </if>
      <if test="consUncRemindWordId != null" >
        cons_unc_remind_word_id,
      </if>
      <if test="isSecConsUncRemind != null" >
        is_sec_cons_unc_remind,
      </if>
      <if test="secConsUncRemindDot != null" >
        sec_cons_unc_remind_dot,
      </if>
      <if test="secConsUncRemindWordId != null" >
        sec_cons_unc_remind_word_id,
      </if>
      <if test="silentUncRemindDot != null" >
        silent_unc_remind_dot,
      </if>
      <if test="silentUncRemindWordId != null" >
        silent_unc_remind_word_id,
      </if>
      <if test="silentUncCsNick != null" >
        silent_unc_cs_nick,
      </if>
      <if test="silentUncGroupId != null" >
        silent_unc_group_id,
      </if>
      <if test="silentUncFlag != null" >
        silent_unc_flag,
      </if>
      <if test="silentUncWordId != null" >
        silent_unc_word_id,
      </if>
      <if test="silentUncSpareCsNick != null" >
        silent_unc_spare_cs_nick,
      </if>
      <if test="silentUncSpareGroupId != null" >
        silent_unc_spare_group_id,
      </if>
      <if test="isSecSilentUncRemind != null" >
        is_sec_silent_unc_remind,
      </if>
      <if test="secSilentUncRemindDot != null" >
        sec_silent_unc_remind_dot,
      </if>
      <if test="secSilentUncRemindWordId != null" >
        sec_silent_unc_remind_word_id,
      </if>
      <if test="consUnrseUncRemindDot != null" >
        cons_unrse_unc_remind_dot,
      </if>
      <if test="consUnrseUncRemindWordId != null" >
        cons_unrse_unc_remind_word_id,
      </if>
      <if test="isSecConsUnrseUncRemind != null" >
        is_sec_cons_unrse_unc_remind,
      </if>
      <if test="secConsUnrseUncRemindDot != null" >
        sec_cons_unrse_unc_remind_dot,
      </if>
      <if test="secConsUnrseUncRemindWordId != null" >
        sec_cons_unrse_unc_remind_word_id,
      </if>
      <if test="isRseUnpRemind != null" >
        is_rse_unp_remind,
      </if>
      <if test="consUnpRemindDot != null" >
        cons_unp_remind_dot,
      </if>
      <if test="consUnpRemindWordId != null" >
        cons_unp_remind_word_id,
      </if>
      <if test="isSecConsUnpRemind != null" >
        is_sec_cons_unp_remind,
      </if>
      <if test="secConsUnpRemindDot != null" >
        sec_cons_unp_remind_dot,
      </if>
      <if test="secConsUnpRemindWordId != null" >
        sec_cons_unp_remind_word_id,
      </if>
      <if test="silentUnpRemindDot != null" >
        silent_unp_remind_dot,
      </if>
      <if test="silentUnpRemindWordId != null" >
        silent_unp_remind_word_id,
      </if>
      <if test="silentUnpCsNick != null" >
        silent_unp_cs_nick,
      </if>
      <if test="silentUnpGroupId != null" >
        silent_unp_group_id,
      </if>
      <if test="silentUnpFlag != null" >
        silent_unp_flag,
      </if>
      <if test="silentUnpWordId != null" >
        silent_unp_word_id,
      </if>
      <if test="silentUnpSpareCsNick != null" >
        silent_unp_spare_cs_nick,
      </if>
      <if test="silentUnpSpareGroupId != null" >
        silent_unp_spare_group_id,
      </if>
      <if test="isSecSilentUnpRemind != null" >
        is_sec_silent_unp_remind,
      </if>
      <if test="secSilentUnpRemindDot != null" >
        sec_silent_unp_remind_dot,
      </if>
      <if test="secSilentUnpRemindWordId != null" >
        sec_silent_unp_remind_word_id,
      </if>
      <if test="remindStartDot != null" >
        remind_start_dot,
      </if>
      <if test="remindEndDot != null" >
        remind_end_dot,
      </if>
      <if test="isSendProfitPoint != null" >
        is_send_profit_point,
      </if>
      <if test="created != null" >
        created,
      </if>
      <if test="modified != null" >
        modified,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isReserveRemind != null" >
        #{isReserveRemind,jdbcType=BIT},
      </if>
      <if test="rseRemindDot != null" >
        #{rseRemindDot,jdbcType=INTEGER},
      </if>
      <if test="rseRemindWordId != null" >
        #{rseRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isRseUncRemind != null" >
        #{isRseUncRemind,jdbcType=BIT},
      </if>
      <if test="consUncRemindDot != null" >
        #{consUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="consUncRemindWordId != null" >
        #{consUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecConsUncRemind != null" >
        #{isSecConsUncRemind,jdbcType=BIT},
      </if>
      <if test="secConsUncRemindDot != null" >
        #{secConsUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secConsUncRemindWordId != null" >
        #{secConsUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUncRemindDot != null" >
        #{silentUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="silentUncRemindWordId != null" >
        #{silentUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUncCsNick != null" >
        #{silentUncCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUncGroupId != null" >
        #{silentUncGroupId,jdbcType=BIGINT},
      </if>
      <if test="silentUncFlag != null" >
        #{silentUncFlag,jdbcType=TINYINT},
      </if>
      <if test="silentUncWordId != null" >
        #{silentUncWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUncSpareCsNick != null" >
        #{silentUncSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUncSpareGroupId != null" >
        #{silentUncSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isSecSilentUncRemind != null" >
        #{isSecSilentUncRemind,jdbcType=BIT},
      </if>
      <if test="secSilentUncRemindDot != null" >
        #{secSilentUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secSilentUncRemindWordId != null" >
        #{secSilentUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="consUnrseUncRemindDot != null" >
        #{consUnrseUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="consUnrseUncRemindWordId != null" >
        #{consUnrseUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecConsUnrseUncRemind != null" >
        #{isSecConsUnrseUncRemind,jdbcType=BIT},
      </if>
      <if test="secConsUnrseUncRemindDot != null" >
        #{secConsUnrseUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secConsUnrseUncRemindWordId != null" >
        #{secConsUnrseUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isRseUnpRemind != null" >
        #{isRseUnpRemind,jdbcType=BIT},
      </if>
      <if test="consUnpRemindDot != null" >
        #{consUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="consUnpRemindWordId != null" >
        #{consUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecConsUnpRemind != null" >
        #{isSecConsUnpRemind,jdbcType=BIT},
      </if>
      <if test="secConsUnpRemindDot != null" >
        #{secConsUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secConsUnpRemindWordId != null" >
        #{secConsUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpRemindDot != null" >
        #{silentUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="silentUnpRemindWordId != null" >
        #{silentUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpCsNick != null" >
        #{silentUnpCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpGroupId != null" >
        #{silentUnpGroupId,jdbcType=BIGINT},
      </if>
      <if test="silentUnpFlag != null" >
        #{silentUnpFlag,jdbcType=TINYINT},
      </if>
      <if test="silentUnpWordId != null" >
        #{silentUnpWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpSpareCsNick != null" >
        #{silentUnpSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpSpareGroupId != null" >
        #{silentUnpSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isSecSilentUnpRemind != null" >
        #{isSecSilentUnpRemind,jdbcType=BIT},
      </if>
      <if test="secSilentUnpRemindDot != null" >
        #{secSilentUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secSilentUnpRemindWordId != null" >
        #{secSilentUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="remindStartDot != null" >
        #{remindStartDot,jdbcType=INTEGER},
      </if>
      <if test="remindEndDot != null" >
        #{remindEndDot,jdbcType=INTEGER},
      </if>
      <if test="isSendProfitPoint != null" >
        #{isSendProfitPoint,jdbcType=BIT},
      </if>
      <if test="created != null" >
        #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        #{modified,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByShopId" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopSettingBatchRemindReserveDTO" >
    update pes_shop_setting_batch_remind_reserve
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isReserveRemind != null" >
        is_reserve_remind = #{isReserveRemind,jdbcType=BIT},
      </if>
      <if test="rseRemindDot != null" >
        rse_remind_dot = #{rseRemindDot,jdbcType=INTEGER},
      </if>
      <if test="rseRemindWordId != null" >
        rse_remind_word_id = #{rseRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isRseUncRemind != null" >
        is_rse_unc_remind = #{isRseUncRemind,jdbcType=BIT},
      </if>
      <if test="consUncRemindDot != null" >
        cons_unc_remind_dot = #{consUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="consUncRemindWordId != null" >
        cons_unc_remind_word_id = #{consUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecConsUncRemind != null" >
        is_sec_cons_unc_remind = #{isSecConsUncRemind,jdbcType=BIT},
      </if>
      <if test="secConsUncRemindDot != null" >
        sec_cons_unc_remind_dot = #{secConsUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secConsUncRemindWordId != null" >
        sec_cons_unc_remind_word_id = #{secConsUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUncRemindDot != null" >
        silent_unc_remind_dot = #{silentUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="silentUncRemindWordId != null" >
        silent_unc_remind_word_id = #{silentUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUncCsNick != null" >
        silent_unc_cs_nick = #{silentUncCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUncGroupId != null" >
        silent_unc_group_id = #{silentUncGroupId,jdbcType=BIGINT},
      </if>
      <if test="silentUncFlag != null" >
        silent_unc_flag = #{silentUncFlag,jdbcType=TINYINT},
      </if>
      <if test="silentUncWordId != null" >
        silent_unc_word_id = #{silentUncWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUncSpareCsNick != null" >
        silent_unc_spare_cs_nick = #{silentUncSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUncSpareGroupId != null" >
        silent_unc_spare_group_id = #{silentUncSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isSecSilentUncRemind != null" >
        is_sec_silent_unc_remind = #{isSecSilentUncRemind,jdbcType=BIT},
      </if>
      <if test="secSilentUncRemindDot != null" >
        sec_silent_unc_remind_dot = #{secSilentUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secSilentUncRemindWordId != null" >
        sec_silent_unc_remind_word_id = #{secSilentUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="consUnrseUncRemindDot != null" >
        cons_unrse_unc_remind_dot = #{consUnrseUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="consUnrseUncRemindWordId != null" >
        cons_unrse_unc_remind_word_id = #{consUnrseUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecConsUnrseUncRemind != null" >
        is_sec_cons_unrse_unc_remind = #{isSecConsUnrseUncRemind,jdbcType=BIT},
      </if>
      <if test="secConsUnrseUncRemindDot != null" >
        sec_cons_unrse_unc_remind_dot = #{secConsUnrseUncRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secConsUnrseUncRemindWordId != null" >
        sec_cons_unrse_unc_remind_word_id = #{secConsUnrseUncRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isRseUnpRemind != null" >
        is_rse_unp_remind = #{isRseUnpRemind,jdbcType=BIT},
      </if>
      <if test="consUnpRemindDot != null" >
        cons_unp_remind_dot = #{consUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="consUnpRemindWordId != null" >
        cons_unp_remind_word_id = #{consUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="isSecConsUnpRemind != null" >
        is_sec_cons_unp_remind = #{isSecConsUnpRemind,jdbcType=BIT},
      </if>
      <if test="secConsUnpRemindDot != null" >
        sec_cons_unp_remind_dot = #{secConsUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secConsUnpRemindWordId != null" >
        sec_cons_unp_remind_word_id = #{secConsUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpRemindDot != null" >
        silent_unp_remind_dot = #{silentUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="silentUnpRemindWordId != null" >
        silent_unp_remind_word_id = #{silentUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpCsNick != null" >
        silent_unp_cs_nick = #{silentUnpCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpGroupId != null" >
        silent_unp_group_id = #{silentUnpGroupId,jdbcType=BIGINT},
      </if>
      <if test="silentUnpFlag != null" >
        silent_unp_flag = #{silentUnpFlag,jdbcType=TINYINT},
      </if>
      <if test="silentUnpWordId != null" >
        silent_unp_word_id = #{silentUnpWordId,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpSpareCsNick != null" >
        silent_unp_spare_cs_nick = #{silentUnpSpareCsNick,jdbcType=VARCHAR},
      </if>
      <if test="silentUnpSpareGroupId != null" >
        silent_unp_spare_group_id = #{silentUnpSpareGroupId,jdbcType=BIGINT},
      </if>
      <if test="isSecSilentUnpRemind != null" >
        is_sec_silent_unp_remind = #{isSecSilentUnpRemind,jdbcType=BIT},
      </if>
      <if test="secSilentUnpRemindDot != null" >
        sec_silent_unp_remind_dot = #{secSilentUnpRemindDot,jdbcType=INTEGER},
      </if>
      <if test="secSilentUnpRemindWordId != null" >
        sec_silent_unp_remind_word_id = #{secSilentUnpRemindWordId,jdbcType=VARCHAR},
      </if>
      <if test="remindStartDot != null" >
        remind_start_dot = #{remindStartDot,jdbcType=INTEGER},
      </if>
      <if test="remindEndDot != null" >
        remind_end_dot = #{remindEndDot,jdbcType=INTEGER},
      </if>
      <if test="isSendProfitPoint != null" >
        is_send_profit_point = #{isSendProfitPoint,jdbcType=BIT},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where shop_id = #{shopId,jdbcType=BIGINT}
  </update>

  <select id="selecttShopSettingBatchRemindByShopId" resultMap="BaseResultMap">
    SELECT
    is_reserve_remind, is_rse_unc_remind, is_rse_unp_remind
    FROM pes_shop_setting_batch_remind_reserve
    WHERE shop_id = #{shopId}
  </select>

  <update id="updateBatchRemindByShopIdAndIsRemind">
    update pes_shop_setting_batch_remind_reserve
    set is_reserve_remind = #{isRemind},
        is_rse_unc_remind = #{isRemind},
        is_rse_unp_remind = #{isRemind}
    where shop_id = #{shopId}
  </update>
</mapper>