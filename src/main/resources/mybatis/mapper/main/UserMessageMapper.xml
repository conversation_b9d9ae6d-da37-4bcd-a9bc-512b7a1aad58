<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.UserMessageMapper">

	<resultMap id="UserMessageDO" type="com.pes.jd.model.DO.UserMessage">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="message" jdbcType="VARCHAR" property="message" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="msg_level" jdbcType="INTEGER" property="msgLevel" />
	</resultMap>

	<sql id="base_field">
		message, create_date, msg_level
	</sql>

	<insert id="insertUserMessage" parameterType="com.pes.jd.model.DO.UserMessage">
		INSERT INTO pes_user_message (id, message, create_date,msg_level)
		VALUES 
		(
			#{id,jdbcType=BIGINT}, #{message,jdbcType=VARCHAR},
			#{createDate,jdbcType=TIMESTAMP},
			#{msgLevel,jdbcType=INTEGER}
		)
	</insert>
	
	<delete id="deleteUserMessageById" parameterType="java.lang.Long">
		delete from pes_user_message
		where id = #{id,jdbcType=BIGINT}
	</delete>

	<update id="updateUserMessageById" parameterType="com.pes.jd.model.DO.UserMessage">
		UPDATE pes_user_message
		<set>
			<if test="message != null">
				message = #{message,jdbcType=VARCHAR},
			</if>
			<if test="createDate != null">
				create_date = #{createDate,jdbcType=TIMESTAMP},
			</if>
			<if test="msgLevel != null">
				msg_level = #{msgLevel,jdbcType=INTEGER},
			</if>
		</set>
		WHERE
			id = #{id,jdbcType=BIGINT}
	</update>
	
	<select id="getUserMessageById" parameterType="java.lang.Long" resultMap="UserMessageDO">
		SELECT
			<include refid="base_field" />
		FROM pes_user_message
		WHERE
			id = #{id,jdbcType=BIGINT}
	</select>
</mapper>