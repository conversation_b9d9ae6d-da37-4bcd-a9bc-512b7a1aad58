<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesMenuResourceMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesMenuResource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="is_personal_data" jdbcType="BIT" property="isPersonalData" />
    <result column="is_cs_team_data" jdbcType="BIT" property="isCsTeamData" />
    <result column="is_shop_data" jdbcType="BIT" property="isShopData" />
    <result column="is_shop_multiple_data" jdbcType="BIT" property="isShopMultipleData" />
    <result column="shop_id" jdbcType="INTEGER" property="shopId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, title, name, sort, parent_id, note, is_default, is_personal_data, is_cs_team_data, 
    is_shop_data, is_shop_multiple_data,shop_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_menu_resource
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_menu_resource
    where shop_id in ( '-1'
     <if test="shopId!=null">
       ,#{shopId}
     </if>)
    <if test="list !=null and list.size()>0">
      AND id in
      <foreach collection="list" item="id" separator="," close=")" open="(">
        #{id}
      </foreach>
    </if>
    <if test="type != null and type != ''">
      AND type != 1
    </if>
  </select>

  <select id="searchAllForSelf" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_menu_resource
    where
         type = 0
    and shop_id in ( '-1'
    <if test="shopId!=null">
      ,#{shopId}
    </if>)
    <if test="list !=null and list.size()>0">
      AND id in
      <foreach collection="list" item="id" separator="," close=")" open="(">
        #{id}
      </foreach>
    </if>
  </select>



  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_menu_resource
    <if test="name!=null and !''.equals(name)">
      where name = #{name}
    </if>
  </select>
    <select id="selectByNameTitle" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from pes_menu_resource
        where 1 =  1
        <if test="name!=null and !''.equals(name)">
           and  name = #{name}
        </if>
        <if test="title!=null and !''.equals(title)">
            and  title = #{title}
        </if>
    </select>
  <select id="searchPerson" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_menu_resource
    where is_personal_data = 1 and shop_id = -1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_menu_resource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByName">
    delete from pes_menu_resource
    where name = #{name}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.PesMenuResource" useGeneratedKeys="true" keyProperty="id">
    insert into pes_menu_resource (id, title, name, 
      sort, parent_id, note, 
      is_default, is_personal_data, is_cs_team_data, 
      is_shop_data, is_shop_multiple_data,shop_id)
    values (#{id,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{note,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=BIT}, #{isPersonalData,jdbcType=BIT}, #{isCsTeamData,jdbcType=BIT}, 
      #{isShopData,jdbcType=BIT}, #{isShopMultipleData,jdbcType=BIT},#{shopId})
  </insert>
  <delete id="updatePermission">
    update pes_jd.pes_menu_resource set is_cs_team_data = #{isCsTeamData}
     , is_personal_data = #{isPersonalData}  ,is_shop_data = #{isShopData}
    where name = #{name}
  </delete>
    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.PesMenuResource">
    update pes_menu_resource
    set title = #{title,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=INTEGER},
      note = #{note,jdbcType=VARCHAR},
      is_default = #{isDefault,jdbcType=BIT},
      is_personal_data = #{isPersonalData,jdbcType=BIT},
      is_cs_team_data = #{isCsTeamData,jdbcType=BIT},
      is_shop_data = #{isShopData,jdbcType=BIT},
      is_shop_multiple_data = #{isShopMultipleData,jdbcType=BIT},
      shop_id = #{shopId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="getMaxSort" resultType="integer">
        select sort from pes_menu_resource where parent_id = #{parentId} order by sort desc limit 1
    </select>
    <select id="selectSystemJurisdicteIdByTitle" resultType="long">
        select id from pes_menu_resource where title = #{title} and shop_id= #{shopId} limit 1
    </select>


  <select id="queryMenuByIds" resultType="com.pes.jd.model.DO.PesMenuResource">
    SELECT id, title, `name` FROM pes_menu_resource WHERE id in
    <foreach collection="list" item="id" separator="," close=")" open="(">
      #{id}
    </foreach>
  </select>
</mapper>