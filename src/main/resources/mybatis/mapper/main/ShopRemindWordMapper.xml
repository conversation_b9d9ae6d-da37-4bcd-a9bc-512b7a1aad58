<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopRemindWordMapper">
    <resultMap id="ShopRemindWordDTO" type="com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="is_send_goods_url" jdbcType="BIT" property="isSendGoodsUrl"/>
        <result column="is_default" jdbcType="BIT" property="isDefault"/>
        <result column="scope" jdbcType="INTEGER" property="scope"/>
        <result column="goods_type" jdbcType="INTEGER" property="goodsType"/>
        <result column="is_using" jdbcType="BIT" property="using"/>
        <result column="ware_type" jdbcType="INTEGER" property="wareType" />
        <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
        <result column="is_permanent" jdbcType="TINYINT" property="isPermanent" />
        <result column="limitation_name" jdbcType="VARCHAR" property="limitationName" />
        <result column="coupon_id" jdbcType="VARCHAR" property="couponId" />
        <result column="coupon_start_time" jdbcType="TIMESTAMP" property="couponStartTime" />
        <result column="coupon_end_time" jdbcType="TIMESTAMP" property="couponEndTime" />
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
        <result column="top_time" jdbcType="TIMESTAMP" property="topTime" />
    </resultMap>
    <sql id="base_field">
            id, shop_id, `name`, `type`, created, content, is_send_goods_url, is_default,scope,goods_type,is_using,ware_type,
            begin_time,end_time,is_permanent,limitation_name,coupon_id,coupon_start_time,coupon_end_time
    </sql>

    <insert id="insert" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
	INSERT INTO pes_shop_cs_remind_word (
	shop_id, `name`, `type`, created, content, is_send_goods_url,
	is_default,scope,goods_type,
	ware_type, begin_time,end_time,is_permanent,limitation_name,
	coupon_id,coupon_start_time,coupon_end_time, is_using,cs_nick,top_time
	)
	VALUES (
	#{shopId,jdbcType=BIGINT},
	#{name,jdbcType=VARCHAR},
	#{type,jdbcType=TINYINT},
	#{created,jdbcType=TIMESTAMP},
	#{content,jdbcType=VARCHAR},
	#{isSendGoodsUrl,jdbcType=BIT},
	#{isDefault,jdbcType=BIT},
	#{scope,jdbcType=TINYINT},
	#{goodsType,jdbcType=TINYINT},

	#{wareType,jdbcType=TINYINT},
	#{beginTime,jdbcType=TIMESTAMP},
	#{endTime,jdbcType=TIMESTAMP},
	#{isPermanent,jdbcType=TINYINT},
	#{limitationName,jdbcType=TINYINT},
	#{couponId,jdbcType=VARCHAR},
	#{couponStartTime,jdbcType=TIMESTAMP},
	#{couponEndTime,jdbcType=TIMESTAMP},
	#{using,jdbcType=BIT},
	#{csNick,jdbcType=VARCHAR},
	#{topTime,jdbcType=TIMESTAMP}
	)
    </insert>

    <delete id="deleteById" parameterType="long">
        DELETE FROM pes_shop_cs_remind_word WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <update id="updateById" parameterType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordDTO">
        UPDATE pes_shop_cs_remind_word
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="isSendGoodsUrl != null">
                is_send_goods_url = #{isSendGoodsUrl,jdbcType=BIT},
            </if>
            <if test="isDefault != null">
                is_default = #{isDefault,jdbcType=BIT},
            </if>
            <if test="scope != null">
                scope = #{scope,jdbcType=TINYINT},
            </if>
            <if test="goodsType != null">
                goods_type = #{goodsType,jdbcType=TINYINT},
            </if>

            <if test="wareType != null">
                ware_type = #{wareType,jdbcType=TINYINT},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isPermanent != null">
                is_permanent = #{isPermanent,jdbcType=TINYINT},
            </if>
            <if test="limitationName != null">
                limitation_name = #{limitationName,jdbcType=VARCHAR},
            </if>
            <if test="csNick != null and csNick!=''">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="topTime != null">
                top_time = #{topTime},
            </if>
            coupon_id = #{couponId,jdbcType=VARCHAR},
            coupon_start_time = #{couponStartTime,jdbcType=TIMESTAMP},
            coupon_end_time = #{couponEndTime,jdbcType=TIMESTAMP}
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND shop_id = #{shopId,jdbcType=BIGINT} and cs_nick=#{csNick}
    </update>

    <update id="updateDefaultByShopIdAndType">
        UPDATE
                pes_shop_cs_remind_word
        SET
            is_default = 0
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
        AND
            `type` = #{type,jdbcType=INTEGER}
        AND
            is_default = 1
        AND
        cs_nick=#{csNick,jdbcType=VARCHAR}
    </update>

    <select id="selectById" resultMap="ShopRemindWordDTO">
        SELECT
        <include refid="base_field"/>
        FROM
        pes_shop_cs_remind_word
        WHERE
        id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByIds" resultMap="ShopRemindWordDTO">
        SELECT
        <include refid="base_field"/>
        FROM
        pes_shop_remind_word
        WHERE
        id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="selectCountByShopIdAndNameAndType" resultType="int">
        SELECT
        COUNT(1)
        FROM
        pes_shop_remind_word
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT} AND is_default = 1 FOR UPDATE
    </select>

    <select id="selectByShopIdAndNameAndType" resultMap="ShopRemindWordDTO">
        SELECT
        <include refid="base_field"/>
        FROM
        pes_shop_cs_remind_word
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        <if test="name != null and name.trim().length() > 0">
            AND `name` LIKE #{name,jdbcType=VARCHAR}
        </if>
        <if test="type != null">
            AND `type` = #{type,jdbcType=INTEGER}
        </if>
        <if test="isDefault != null">
            AND is_default = #{isDefault}
        </if>
        <if test="scope!= null">
            AND scope  = #{scope}
        </if>
        <if test="wareType!= null">
            AND ware_type  = #{wareType}
        </if>
        <if test="csNick!= null and csNick!=''">
            AND cs_nick  = #{csNick}
        </if>
        ORDER BY top_time DESC
    </select>

    <select id="selectByShopIdAndNameAndTypeForPlugin" resultMap="ShopRemindWordDTO">
        SELECT
        <include refid="base_field"/>
        FROM
        pes_shop_cs_remind_word
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        <if test="csNick!= null and csNick!=''">
            AND cs_nick  = #{csNick}
        </if>
        and top_time > '2021-03-01 00:00:00'
        ORDER BY top_time DESC
    </select>

    <select id="selectByShopIdAndCsNickForNotTopTime" resultMap="ShopRemindWordDTO">
        SELECT
        <include refid="base_field"/>
        FROM
        pes_shop_cs_remind_word
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        <if test="csNick!= null and csNick!=''">
            AND cs_nick  = #{csNick}
        </if>
        and top_time is null
        ORDER BY created DESC
    </select>

    <select id="selectShopRemindWordLstByShopIdAndNameAndType" resultMap="ShopRemindWordDTO">
        SELECT
        <include refid="base_field"/>
        FROM
        pes_shop_remind_word
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        <if test="name != null and name.trim().length() > 0">
            AND `name` LIKE #{name,jdbcType=VARCHAR}
        </if>
        <if test="type != null">
            AND `type` = #{type,jdbcType=INTEGER}
        </if>
        <if test="isDefault != null">
            AND is_default = #{isDefault}
        </if>
        <if test="scope!= null">
            AND scope  = #{scope}
        </if>
        <if test="wareType!= null">
            AND ware_type  = #{wareType}
        </if>
    </select>

    <select id="selectSameWordForGoodsByShopIdByTypeByGoodsSet" resultType="int">
        select count(rgg.id) from pes_shop_remind_word rg
        inner join pes_shop_remind_word_goods rgg on rg.id=rgg.remind_word_id
        <where>
            rg.shop_id=#{shopId}
            and rg.type=#{type}
            and rgg.goods_id in
            <foreach collection="goodsIdSet" item="goodsId" open="(" close=")" separator=",">
                #{goodsId}
            </foreach>
            and rgg.type =#{goodsType}
        </where>
        <if test="remindId!=null">
            and rg.id!=#{remindId}
        </if>

    </select>
    <update id="updateRemindWordNoUsingByShopIdAndType">
        UPDATE
            pes_shop_remind_word
        SET
            is_using = 0
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
        AND
            `type` = #{type,jdbcType=INTEGER}
        AND
            is_using = 1
    </update>

    <update id="updateRemindWordUsingByShopIdByWordIds">
        UPDATE
        pes_shop_remind_word
        SET
        is_using = 1
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND
        id in
        <foreach collection="wordIds" item="wordId" open="(" close=")" separator=",">
            #{wordId}
        </foreach>
    </update>
    <update id="updateRemindStatusToFalse">
        UPDATE
        pes_shop_remind_word
        SET
        is_using = 0
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND
        type = #{type,jdbcType=INTEGER}
    </update>

    <update id="updateIsUsingToFalseByShopId">
        update pes_shop_remind_word
        set is_using = 0
        where shop_id = #{shopId}
    </update>

    <update id="updateIsUsingToCsFalseByShopId">
        update pes_shop_cs_remind_word
        set is_using = 0
        where shop_id = #{shopId}
    </update>
    <select id="selectWordByShopIds" resultMap="ShopRemindWordDTO">
        select shop_id,`name`,`type`,created,content,is_send_goods_url,is_default,scope,goods_type,is_using,ware_type,begin_time,end_time,is_permanent,limitation_name,coupon_id,coupon_start_time,coupon_end_time from pes_shop_remind_word where
        shop_id in
        <foreach collection="shopIds" item="shopId" close=")" open="(" separator=",">
            #{shopId}
        </foreach>
    </select>

    <delete id="delete">
         DELETE FROM pes_shop_cs_remind_word where
         shop_id=#{shopId}
        AND id in
        <foreach collection="ids" item="id" close=")" open="(" separator=",">
            #{id}
        </foreach>
        AND is_default = false
    </delete>

    <select id="selectWordGoodsByWordIds" resultType="com.pes.jd.ms.domain.Data.service.usrmgr.ShopRemindWordGoodsDTO">
        SELECT
        shop_id  as shopId,
        remind_word_id  as remindWordId,
        goods_id  as goodsId,
        `type`
        FROM
        pes_shop_remind_word_goods
        WHERE
        shop_id=#{shopId}
        and
        remind_word_id IN
        <foreach collection="wordIds" item="wordId" open="(" close=")" separator=",">
            #{wordId}
        </foreach>
    </select>

    <select id="selectShopBatchRemindSettingWord" resultMap="ShopRemindWordDTO">
        select * from pes_shop_cs_remind_word where
        shop_id = #{shopId}
        AND name = #{name}
        and cs_nick=#{csNick}
    </select>

    <select id="selectShopRemindWordLstByShopIdAndOrderType" resultMap="ShopRemindWordDTO">
        select * from pes_shop_cs_remind_word where
        shop_id = #{shopId}
        <if test="type != null and type != ''">
            AND
            ware_type = #{type}
        </if>
    </select>

    <insert id="insertDefaultRemindWord" parameterType="java.util.ArrayList" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into pes_shop_remind_word
        (shop_id, name, created, content, type, is_send_goods_url, is_default, scope, goods_type, is_using, ware_type, is_permanent)
        values
        <foreach collection="list" item="shopRemindWord" separator=",">
        (#{shopRemindWord.shopId},
        #{shopRemindWord.name},
        #{shopRemindWord.created},
        #{shopRemindWord.content},
        #{shopRemindWord.type},
        #{shopRemindWord.isSendGoodsUrl},
        #{shopRemindWord.isDefault},
        #{shopRemindWord.scope},
        #{shopRemindWord.goodsType},
        #{shopRemindWord.using},
        #{shopRemindWord.wareType},
        #{shopRemindWord.isPermanent})
        </foreach>
    </insert>

    <update id="updateTopTimeIsNowByshopIdAndId">
        UPDATE pes_shop_cs_remind_word
        <set>
            <if test="nowDate != null">
                top_time = #{nowDate}
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT} AND shop_id = #{shopId,jdbcType=BIGINT}
    </update>

    <select id="selectShopRemindWordLstFromCsRemindWord" resultMap="ShopRemindWordDTO">
        select * from pes_shop_cs_remind_word where  shop_id={shopId} and cs_nick=#{csNick};
    </select>

    <select id="selectShopRemindWordLstFromShopRemindWord" resultMap="ShopRemindWordDTO">
        select * from pes_shop_remind_word where  shop_id=#{shopId};
    </select>

    <insert id="batchInsertShopCsRemindWord">
        INSERT INTO pes_shop_cs_remind_word(
        shop_id,`name`,`type`,created,content,is_send_goods_url,is_default,scope,goods_type,is_using,ware_type,begin_time,end_time,is_permanent,limitation_name,coupon_id,coupon_start_time,coupon_end_time,cs_nick)
        VALUES
        <foreach collection="list" item="itm" separator=",">
           (#{itm.shopId},
            #{itm.name},
            #{itm.type},
            #{itm.created},
            #{itm.content},
            #{itm.isSendGoodsUrl},
            #{itm.isDefault},
            #{itm.scope},
            #{itm.goodsType},
            #{itm.using},
            #{itm.wareType},
            #{itm.beginTime},
            #{itm.endTime},
            #{itm.isPermanent},
            #{itm.limitationName},
            #{itm.couponId},
            #{itm.couponStartTime},
            #{itm.couponEndTime},
            #{itm.csNick})
        </foreach>
    </insert>

    <select id="selectShopRemindWordCountFromCsRemindWord" resultType="int">
         select count(*) from pes_shop_cs_remind_word where  shop_id=#{shopId} and cs_nick=#{csNick};
    </select>

    <select id="selectShopCsWordByShopIds" resultMap="ShopRemindWordDTO">
        select * from  pes_shop_cs_remind_word
        where shop_id in
        <foreach collection="shopIds" item="shopId" close=")" open="(" separator=",">
            #{shopId}
        </foreach>
    </select>

    <select id="selectWordByShopIdAndCsNick" resultMap="ShopRemindWordDTO">
        select shop_id,`name`,`type`,created,content,is_send_goods_url,is_default,scope,goods_type,is_using,ware_type,begin_time,end_time,is_permanent,limitation_name,coupon_id,coupon_start_time,coupon_end_time
        from pes_shop_cs_remind_word where
        shop_id =#{shopId} and cs_nick=#{csNick}
    </select>
</mapper>