<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.AppointmentAutoAllocatedSettingMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.AppointmentAutoAllocatedSettingDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="modify" jdbcType="TIMESTAMP" property="modify" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_auto_allocated" jdbcType="BIT" property="isAutoAllocated" />
    <result column="cno_not_app_flag" jdbcType="TINYINT" property="cnoNotAppFlag" />
    <result column="cno_not_app_group_id" jdbcType="BIGINT" property="cnoNotAppGroupId" />
    <result column="cno_not_app_cs_nick" jdbcType="VARCHAR" property="cnoNotAppCsNick" />
    <result column="cno_not_app_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotAppSpareCsNick" />
    <result column="cno_not_app_spare_group_id" jdbcType="BIGINT" property="cnoNotAppSpareGroupId" />
    <result column="cno_not_order_flag" jdbcType="TINYINT" property="cnoNotOrderFlag" />
    <result column="cno_not_order_group_id" jdbcType="BIGINT" property="cnoNotOrderGroupId" />
    <result column="cno_not_order_cs_nick" jdbcType="VARCHAR" property="cnoNotOrderCsNick" />
    <result column="cno_not_order_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotOrderSpareCsNick" />
    <result column="cno_not_order_spare_group_id" jdbcType="BIGINT" property="cnoNotOrderSpareGroupId" />
    <result column="snp_app_not_order_flag" jdbcType="TINYINT" property="snpAppNotOrderFlag" />
    <result column="snp_app_not_order_group_id" jdbcType="BIGINT" property="snpAppNotOrderGroupId" />
    <result column="snp_app_not_order_cs_nick" jdbcType="VARCHAR" property="snpAppNotOrderCsNick" />
    <result column="snp_app_not_order_spare_cs_nick" jdbcType="VARCHAR" property="snpAppNotOrderSpareCsNick" />
    <result column="snp_app_not_order_spare_group_id" jdbcType="BIGINT" property="snpAppNotOrderSpareGroupId" />
    <result column="cno_not_app_not_pay_flag" jdbcType="TINYINT" property="cnoNotAppNotPayFlag" />
    <result column="cno_not_app_not_pay_group_id" jdbcType="BIGINT" property="cnoNotAppNotPayGroupId" />
    <result column="cno_not_app_not_pay_cs_nick" jdbcType="VARCHAR" property="cnoNotAppNotPayCsNick" />
    <result column="cno_not_app_not_pay_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotAppNotPaySpareCsNick" />
    <result column="cno_not_app_not_pay_spare_group_id" jdbcType="BIGINT" property="cnoNotAppNotPaySpareGroupId" />
    <result column="cno_not_pay_flag" jdbcType="TINYINT" property="cnoNotPayFlag" />
    <result column="cno_not_pay_group_id" jdbcType="BIGINT" property="cnoNotPayGroupId" />
    <result column="cno_not_pay_cs_nick" jdbcType="VARCHAR" property="cnoNotPayCsNick" />
    <result column="cno_not_pay_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotPaySpareCsNick" />
    <result column="cno_not_pay_spare_group_id" jdbcType="BIGINT" property="cnoNotPaySpareGroupId" />
    <result column="snp_app_not_pay_flag" jdbcType="TINYINT" property="snpAppNotPayFlag" />
    <result column="snp_app_not_pay_group_id" jdbcType="BIGINT" property="snpAppNotPayGroupId" />
    <result column="snp_app_not_pay_cs_nick" jdbcType="VARCHAR" property="snpAppNotPayCsNick" />
    <result column="snp_app_not_pay_spare_cs_nick" jdbcType="VARCHAR" property="snpAppNotPaySpareCsNick" />
    <result column="snp_app_not_pay_spare_group_id" jdbcType="BIGINT" property="snpAppNotPaySpareGroupId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, created, modify, status, is_auto_allocated, cno_not_app_flag, cno_not_app_group_id,
    cno_not_app_cs_nick, cno_not_app_spare_cs_nick, cno_not_app_spare_group_id, cno_not_order_flag,
    cno_not_order_group_id, cno_not_order_cs_nick, cno_not_order_spare_cs_nick, cno_not_order_spare_group_id,
    snp_app_not_order_flag, snp_app_not_order_group_id, snp_app_not_order_cs_nick, snp_app_not_order_spare_cs_nick,
    snp_app_not_order_spare_group_id, cno_not_app_not_pay_flag, cno_not_app_not_pay_group_id,
    cno_not_app_not_pay_cs_nick, cno_not_app_not_pay_spare_cs_nick, cno_not_app_not_pay_spare_group_id,
    cno_not_pay_flag, cno_not_pay_group_id, cno_not_pay_cs_nick, cno_not_pay_spare_cs_nick,
    cno_not_pay_spare_group_id, snp_app_not_pay_flag, snp_app_not_pay_group_id, snp_app_not_pay_cs_nick,
    snp_app_not_pay_spare_cs_nick, snp_app_not_pay_spare_group_id
  </sql>

  <select id="selectShopAutoAllocatedSettingByShopId" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from pes_shop_auto_appointment_allocated_setting
    where shop_id=#{shopId}
  </select>

   <insert id="insertShopAutoAllocatedSetting" >
    insert into pes_shop_auto_appointment_allocated_setting ( shop_id, created,
      modify, status, is_auto_allocated,
      cno_not_app_flag, cno_not_app_group_id, cno_not_app_cs_nick,
      cno_not_app_spare_cs_nick, cno_not_app_spare_group_id,
      cno_not_order_flag, cno_not_order_group_id, cno_not_order_cs_nick,
      cno_not_order_spare_cs_nick, cno_not_order_spare_group_id,
      snp_app_not_order_flag, snp_app_not_order_group_id,
      snp_app_not_order_cs_nick, snp_app_not_order_spare_cs_nick,
      snp_app_not_order_spare_group_id, cno_not_app_not_pay_flag,
      cno_not_app_not_pay_group_id, cno_not_app_not_pay_cs_nick,
      cno_not_app_not_pay_spare_cs_nick, cno_not_app_not_pay_spare_group_id,
      cno_not_pay_flag, cno_not_pay_group_id, cno_not_pay_cs_nick,
      cno_not_pay_spare_cs_nick, cno_not_pay_spare_group_id,
      snp_app_not_pay_flag, snp_app_not_pay_group_id, snp_app_not_pay_cs_nick,
      snp_app_not_pay_spare_cs_nick, snp_app_not_pay_spare_group_id
      )
    values ( #{shopId,jdbcType=BIGINT}, #{created,jdbcType=TIMESTAMP},
      #{modify,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{isAutoAllocated,jdbcType=BIT},
      #{cnoNotAppFlag,jdbcType=TINYINT}, #{cnoNotAppGroupId,jdbcType=BIGINT}, #{cnoNotAppCsNick,jdbcType=VARCHAR},
      #{cnoNotAppSpareCsNick,jdbcType=VARCHAR}, #{cnoNotAppSpareGroupId,jdbcType=BIGINT},
      #{cnoNotOrderFlag,jdbcType=TINYINT}, #{cnoNotOrderGroupId,jdbcType=BIGINT}, #{cnoNotOrderCsNick,jdbcType=VARCHAR},
      #{cnoNotOrderSpareCsNick,jdbcType=VARCHAR}, #{cnoNotOrderSpareGroupId,jdbcType=BIGINT},
      #{snpAppNotOrderFlag,jdbcType=TINYINT}, #{snpAppNotOrderGroupId,jdbcType=BIGINT},
      #{snpAppNotOrderCsNick,jdbcType=VARCHAR}, #{snpAppNotOrderSpareCsNick,jdbcType=VARCHAR},
      #{snpAppNotOrderSpareGroupId,jdbcType=BIGINT}, #{cnoNotAppNotPayFlag,jdbcType=TINYINT},
      #{cnoNotAppNotPayGroupId,jdbcType=BIGINT}, #{cnoNotAppNotPayCsNick,jdbcType=VARCHAR},
      #{cnoNotAppNotPaySpareCsNick,jdbcType=VARCHAR}, #{cnoNotAppNotPaySpareGroupId,jdbcType=BIGINT},
      #{cnoNotPayFlag,jdbcType=TINYINT}, #{cnoNotPayGroupId,jdbcType=BIGINT}, #{cnoNotPayCsNick,jdbcType=VARCHAR},
      #{cnoNotPaySpareCsNick,jdbcType=VARCHAR}, #{cnoNotPaySpareGroupId,jdbcType=BIGINT},
      #{snpAppNotPayFlag,jdbcType=TINYINT}, #{snpAppNotPayGroupId,jdbcType=BIGINT}, #{snpAppNotPayCsNick,jdbcType=VARCHAR},
      #{snpAppNotPaySpareCsNick,jdbcType=VARCHAR}, #{snpAppNotPaySpareGroupId,jdbcType=BIGINT}
      )
  </insert>

   <update id="updateShopAutoAllocatedSettingById" >
    update pes_shop_auto_appointment_allocated_setting
    <set>
      <if test="created != null">
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modify != null">
        modify = #{modify,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isAutoAllocated != null">
        is_auto_allocated = #{isAutoAllocated,jdbcType=BIT},
      </if>

        cno_not_app_flag = #{cnoNotAppFlag,jdbcType=TINYINT},
        cno_not_app_group_id = #{cnoNotAppGroupId,jdbcType=BIGINT},

        cno_not_app_cs_nick = #{cnoNotAppCsNick,jdbcType=VARCHAR},

        cno_not_app_spare_cs_nick = #{cnoNotAppSpareCsNick,jdbcType=VARCHAR},

        cno_not_app_spare_group_id = #{cnoNotAppSpareGroupId,jdbcType=BIGINT},

        cno_not_order_flag = #{cnoNotOrderFlag,jdbcType=TINYINT},

        cno_not_order_group_id = #{cnoNotOrderGroupId,jdbcType=BIGINT},

        cno_not_order_cs_nick = #{cnoNotOrderCsNick,jdbcType=VARCHAR},

        cno_not_order_spare_cs_nick = #{cnoNotOrderSpareCsNick,jdbcType=VARCHAR},

        cno_not_order_spare_group_id = #{cnoNotOrderSpareGroupId,jdbcType=BIGINT},

        snp_app_not_order_flag = #{snpAppNotOrderFlag,jdbcType=TINYINT},

        snp_app_not_order_group_id = #{snpAppNotOrderGroupId,jdbcType=BIGINT},

        snp_app_not_order_cs_nick = #{snpAppNotOrderCsNick,jdbcType=VARCHAR},

        snp_app_not_order_spare_cs_nick = #{snpAppNotOrderSpareCsNick,jdbcType=VARCHAR},

        snp_app_not_order_spare_group_id = #{snpAppNotOrderSpareGroupId,jdbcType=BIGINT},

        cno_not_app_not_pay_flag = #{cnoNotAppNotPayFlag,jdbcType=TINYINT},

        cno_not_app_not_pay_group_id = #{cnoNotAppNotPayGroupId,jdbcType=BIGINT},

        cno_not_app_not_pay_cs_nick = #{cnoNotAppNotPayCsNick,jdbcType=VARCHAR},

        cno_not_app_not_pay_spare_cs_nick = #{cnoNotAppNotPaySpareCsNick,jdbcType=VARCHAR},

        cno_not_app_not_pay_spare_group_id = #{cnoNotAppNotPaySpareGroupId,jdbcType=BIGINT},

        cno_not_pay_flag = #{cnoNotPayFlag,jdbcType=TINYINT},

        cno_not_pay_group_id = #{cnoNotPayGroupId,jdbcType=BIGINT},

        cno_not_pay_cs_nick = #{cnoNotPayCsNick,jdbcType=VARCHAR},

        cno_not_pay_spare_cs_nick = #{cnoNotPaySpareCsNick,jdbcType=VARCHAR},

        cno_not_pay_spare_group_id = #{cnoNotPaySpareGroupId,jdbcType=BIGINT},

        snp_app_not_pay_flag = #{snpAppNotPayFlag,jdbcType=TINYINT},

        snp_app_not_pay_group_id = #{snpAppNotPayGroupId,jdbcType=BIGINT},

        snp_app_not_pay_cs_nick = #{snpAppNotPayCsNick,jdbcType=VARCHAR},

        snp_app_not_pay_spare_cs_nick = #{snpAppNotPaySpareCsNick,jdbcType=VARCHAR},

        snp_app_not_pay_spare_group_id = #{snpAppNotPaySpareGroupId,jdbcType=BIGINT},

    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <update id="updateCloseAppointmentAutoAllocatedByShopId" >
         update pes_shop_auto_appointment_allocated_setting set  is_auto_allocated =#{autoAllocated} where shop_id=#{shopId}
  </update>
</mapper>