<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.GroupMapper">
  <resultMap id="GroupDO" type="com.pes.jd.model.DO.Group">
    <id column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
  </resultMap>
  
    <resultMap id="GroupDTO" type="com.pes.jd.model.DTO.GroupDTO">
    <id column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
     <result column="db" jdbcType="VARCHAR" property="dbName" />
  	<result column="title" jdbcType="VARCHAR" property="title" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
  </resultMap>
  <sql id="base_field">
    group_id, group_name, shop_id, created, modified, is_default
  </sql>
  
  <insert id="insertGroup" useGeneratedKeys="true" keyProperty="groupId" keyColumn="group_id" parameterType="com.pes.jd.model.DO.Group">
    INSERT INTO pes_group (group_id, group_name, shop_id, created, modified, is_default)
    VALUES 
    	(  
    		#{groupId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, 
      		#{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}, #{isDefault,jdbcType=BIT}
      	)
  </insert>
  
  <delete id="deleteGroupByGroupId" parameterType="java.lang.Long">
    DELETE from pes_group
    WHERE 
    	group_id = #{groupId,jdbcType=BIGINT}
  </delete>
  
  <update id="updateGroupBySelective" parameterType="com.pes.jd.model.DO.Group">
    UPDATE pes_group
    <set>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null">
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
    </set>
    WHERE group_id = #{groupId,jdbcType=BIGINT}
  </update>
  
  <select id="getGroupByGroupId" parameterType="java.lang.Long" resultMap="GroupDO">
    SELECT 
   		 <include refid="base_field" />
    FROM pes_group
   	WHERE 
   		group_id = #{groupId,jdbcType=BIGINT}
  </select>
  
<select id="selectGroup" parameterType="map" resultMap="GroupDTO">
	SELECT 
	<include refid="base_field"></include>
	FROM pes_group 
	WHERE  shop_id =#{shopId}
</select>

<select id="queryShopCsGroupsByShopIdLstByGroupName" parameterType="map" resultMap="GroupDTO">
	SELECT 
		group_id, 
		group_name,
		is_default, 
		s.shop_id,
		title, 
		s.db, 
		g.modified ,
		g.created
	FROM pes_group g 
	LEFT JOIN pes_shop s
	ON g.shop_id=s.shop_id
	<where> 
	 s.shop_id  in
	 <foreach collection="shopLst" open="(" close=")" separator="," item="itm">
            #{itm.shopId}
        </foreach>
	<if test="groupName!=null and groupName!=''">
		and  group_name like CONCAT(CONCAT('%', #{groupName}), '%')
	</if>
	</where>
</select>
<select id="getCsGroupByGroupName" parameterType="map" resultMap="GroupDTO">
	SELECT group_id, group_name, shop_id
	FROM
	pes_group
	WHERE
	shop_id = #{shopId}
	AND group_name = #{groupName}
	<if test="groupId!=null and groupId!=''">
	AND	group_id !=#{groupId}
	</if>
</select>
 <select id="getDefaulutGroupByshopId" parameterType="long" resultMap="GroupDTO">
	SELECT <include refid="base_field" />
	FROM pes_group 
	WHERE  shop_id =#{shopId}
	and is_default=1
</select>

    <select id="selectGroupByShopIdByGroupIds" resultMap="GroupDTO">
        select group_id, group_name, shop_id
        from pes_group
        <where>
            and group_id in
            <foreach collection="groupIdSet" item="groupId" open="(" close=")" separator=",">
                #{groupId}
            </foreach>
        </where>
    </select>
    <select id="selectGroupIdByShopId" resultType="java.lang.Long">
        select group_id from pes_group
        where shop_id = #{shopId}
        and is_default = 1
    </select>
</mapper>