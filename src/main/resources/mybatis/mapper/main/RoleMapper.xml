<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.RoleMapper">
  <resultMap id="RoleDO" type="com.pes.jd.model.DO.Role">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
  </resultMap>
  <sql id="base_field">
    id, role_name, desc
  </sql>
  
   <insert id="insertRole" parameterType="com.pes.jd.model.DO.Role">
    INSERT INTO pes_role  (	role_name, desc)
    VALUES (#{roleName,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR} )
  </insert>
  
  <delete id="deleteRoleById" parameterType="java.lang.Long">
    DELETE FROM pes_role
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>

  <update id="updateRoleBySelective" parameterType="com.pes.jd.model.DO.Role">
    UPDATE pes_role
    <set>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        desc = #{desc,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="getRoleById" parameterType="java.lang.Long" resultMap="RoleDO">
    SELECT 
    	<include refid="base_field" />
    FROM pes_role
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
</mapper>