<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSmsSettingMapper" >
  <resultMap id="ShopSmsSettingDO" type="com.pes.jd.model.DO.ShopSmsSettingDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="is_remind" property="isRemind" jdbcType="BIT" />
    <result column="remind_time" property="remindTime" jdbcType="INTEGER" />
    <result column="unpc_word_id" property="unpcWordId" jdbcType="BIGINT" />
    <result column="slinet_word_id" property="slinetWordId" jdbcType="BIGINT" />
    <result column="remind_start_dot" property="remindStartDot" jdbcType="INTEGER" />
    <result column="remind_end_dot" property="remindEndDot" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ShopSmsSetting" type="com.pes.jd.ms.domain.Data.master.ShopSmsSetting" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="is_remind" property="isRemind" jdbcType="BIT" />
    <result column="remind_time" property="remindTime" jdbcType="INTEGER" />
    <result column="unpc_word_id" property="unpcWordId" jdbcType="BIGINT" />
    <result column="slinet_word_id" property="slinetWordId" jdbcType="BIGINT" />
    <result column="remind_start_dot" property="remindStartDot" jdbcType="INTEGER" />
    <result column="remind_end_dot" property="remindEndDot" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="base_filed" >
    id, shop_id, is_remind, remind_time, unpc_word_id, slinet_word_id, remind_start_dot, 
    remind_end_dot, created, modified
  </sql>
  <select id="selectShopSmsSettingByShopId" resultMap="ShopSmsSetting"  >
    select 
    <include refid="base_filed" />
    from pes_shop_sms_setting
    where shop_id = #{shopId,jdbcType=INTEGER}
  </select>

  <insert id="insertShopSmsSetting" parameterType="com.pes.jd.model.DO.ShopSmsSettingDO" >
    insert into pes_shop_sms_setting (id, shop_id, is_remind, 
      remind_time, unpc_word_id, slinet_word_id, 
      remind_start_dot, remind_end_dot, created, 
      modified)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{isRemind,jdbcType=BIT},
      #{remindTime,jdbcType=INTEGER}, #{unpcWordId,jdbcType=BIGINT}, #{slinetWordId,jdbcType=BIGINT}, 
      #{remindStartDot,jdbcType=INTEGER}, #{remindEndDot,jdbcType=INTEGER}, #{created,jdbcType=TIMESTAMP}, 
      #{modified,jdbcType=TIMESTAMP})
  </insert>

  <update id="updateShopSmsSetting" parameterType="com.pes.jd.model.DO.ShopSmsSettingDO" >
    update pes_shop_sms_setting
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isRemind != null" >
        is_remind = #{isRemind,jdbcType=BIT},
      </if>
      <if test="remindTime != null" >
        remind_time = #{remindTime,jdbcType=INTEGER},
      </if>
      <if test="unpcWordId != null" >
        unpc_word_id = #{unpcWordId,jdbcType=BIGINT},
      </if>
      <if test="slinetWordId != null" >
        slinet_word_id = #{slinetWordId,jdbcType=BIGINT},
      </if>
      <if test="remindStartDot != null" >
        remind_start_dot = #{remindStartDot,jdbcType=INTEGER},
      </if>
      <if test="remindEndDot != null" >
        remind_end_dot = #{remindEndDot,jdbcType=INTEGER},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateSmsSettingEndDotFor618">
        UPDATE
            pes_shop_sms_setting
        SET
            remind_end_dot = #{endDot,jdbcType=INTEGER}
        WHERE
            remind_end_dot <![CDATA[ <= ]]> #{whereEndDot,jdbcType=INTEGER}
    </update>
  <update id="updateSmsSettingEndDotForDeflult">
        UPDATE
            pes_shop_sms_setting
        SET
            remind_end_dot = #{endDot,jdbcType=INTEGER}
        WHERE
            remind_end_dot <![CDATA[ >= ]]> #{whereEndDot,jdbcType=INTEGER}
    </update>
    <select id="selectSettingByShopIds" resultMap="ShopSmsSettingDO">
        select * from pes_shop_sms_setting where shop_id in
        <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
            #{shopId}
        </foreach>
    </select>
</mapper>