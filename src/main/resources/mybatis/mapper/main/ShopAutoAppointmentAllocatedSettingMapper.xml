<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopAutoAppointmentAllocatedSettingMapper" >
  <resultMap id="ShopAutoAppointmentAllocatedSettingDTO" type="com.pes.jd.model.DTO.ShopAutoAppointmentAllocatedSettingDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modify" property="modify" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="is_auto_allocated" property="isAutoAllocated" jdbcType="BIT" />
    <result column="cno_not_app_flag" property="cnoNotAppFlag" jdbcType="TINYINT" />
    <result column="cno_not_app_group_id" property="cnoNotAppGroupId" jdbcType="BIGINT" />
    <result column="cno_not_app_cs_nick" property="cnoNotAppCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_app_spare_cs_nick" property="cnoNotAppSpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_app_spare_group_id" property="cnoNotAppSpareGroupId" jdbcType="BIGINT" />
    <result column="cno_not_order_flag" property="cnoNotOrderFlag" jdbcType="TINYINT" />
    <result column="cno_not_order_group_id" property="cnoNotOrderGroupId" jdbcType="BIGINT" />
    <result column="cno_not_order_cs_nick" property="cnoNotOrderCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_order_spare_cs_nick" property="cnoNotOrderSpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_order_spare_group_id" property="cnoNotOrderSpareGroupId" jdbcType="BIGINT" />
    <result column="snp_app_not_order_flag" property="snpAppNotOrderFlag" jdbcType="TINYINT" />
    <result column="snp_app_not_order_group_id" property="snpAppNotOrderGroupId" jdbcType="BIGINT" />
    <result column="snp_app_not_order_cs_nick" property="snpAppNotOrderCsNick" jdbcType="VARCHAR" />
    <result column="snp_app_not_order_spare_cs_nick" property="snpAppNotOrderSpareCsNick" jdbcType="VARCHAR" />
    <result column="snp_app_not_order_spare_group_id" property="snpAppNotOrderSpareGroupId" jdbcType="BIGINT" />
    <result column="cno_not_app_not_pay_flag" property="cnoNotAppNotPayFlag" jdbcType="TINYINT" />
    <result column="cno_not_app_not_pay_group_id" property="cnoNotAppNotPayGroupId" jdbcType="BIGINT" />
    <result column="cno_not_app_not_pay_cs_nick" property="cnoNotAppNotPayCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_app_not_pay_spare_cs_nick" property="cnoNotAppNotPaySpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_app_not_pay_spare_group_id" property="cnoNotAppNotPaySpareGroupId" jdbcType="BIGINT" />
    <result column="cno_not_pay_flag" property="cnoNotPayFlag" jdbcType="TINYINT" />
    <result column="cno_not_pay_group_id" property="cnoNotPayGroupId" jdbcType="BIGINT" />
    <result column="cno_not_pay_cs_nick" property="cnoNotPayCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_pay_spare_cs_nick" property="cnoNotPaySpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_pay_spare_group_id" property="cnoNotPaySpareGroupId" jdbcType="BIGINT" />
    <result column="snp_app_not_pay_flag" property="snpAppNotPayFlag" jdbcType="TINYINT" />
    <result column="snp_app_not_pay_group_id" property="snpAppNotPayGroupId" jdbcType="BIGINT" />
    <result column="snp_app_not_pay_cs_nick" property="snpAppNotPayCsNick" jdbcType="VARCHAR" />
    <result column="snp_app_not_pay_spare_cs_nick" property="snpAppNotPaySpareCsNick" jdbcType="VARCHAR" />
    <result column="snp_app_not_pay_spare_group_id" property="snpAppNotPaySpareGroupId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, created, modify, status, is_auto_allocated, cno_not_app_flag, cno_not_app_group_id, 
    cno_not_app_cs_nick, cno_not_app_spare_cs_nick, cno_not_app_spare_group_id, cno_not_order_flag, 
    cno_not_order_group_id, cno_not_order_cs_nick, cno_not_order_spare_cs_nick, cno_not_order_spare_group_id, 
    snp_app_not_order_flag, snp_app_not_order_group_id, snp_app_not_order_cs_nick, snp_app_not_order_spare_cs_nick, 
    snp_app_not_order_spare_group_id, cno_not_app_not_pay_flag, cno_not_app_not_pay_group_id, 
    cno_not_app_not_pay_cs_nick, cno_not_app_not_pay_spare_cs_nick, cno_not_app_not_pay_spare_group_id, 
    cno_not_pay_flag, cno_not_pay_group_id, cno_not_pay_cs_nick, cno_not_pay_spare_cs_nick, 
    cno_not_pay_spare_group_id, snp_app_not_pay_flag, snp_app_not_pay_group_id, snp_app_not_pay_cs_nick, 
    snp_app_not_pay_spare_cs_nick, snp_app_not_pay_spare_group_id
  </sql>
  <select id="selectShopAutoAppointmentAllocatedSettingByShopId" resultMap="ShopAutoAppointmentAllocatedSettingDTO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_shop_auto_appointment_allocated_setting
    where shop_id = #{shop_id,jdbcType=BIGINT}
    and status=1
  </select>

</mapper>