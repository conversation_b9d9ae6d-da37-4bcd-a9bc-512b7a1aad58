<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopAccountMapper">

  <resultMap id="ShopAccountDO" type="com.pes.jd.model.DO.ShopAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="role" jdbcType="VARCHAR" property="role" />
     <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="status" jdbcType="INTEGER" property="status" />
     <result column="is_account" jdbcType="BIT" property="isAccount" />
       <result column="source" jdbcType="INTEGER" property="source" />
  </resultMap>
  
   <resultMap id="ShopAccountDTO" type="com.pes.jd.model.DTO.ShopAccountDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="role" jdbcType="VARCHAR" property="role" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
	<result column="source" jdbcType="INTEGER" property="source" />
     <result column="user_name" jdbcType="VARCHAR" property="userName" />
      <result column="is_account" jdbcType="BIT" property="isAccount" />
  </resultMap>

  <sql id="base_field">
    id, seller_id, nick, shop_id, role, status, user_name, is_account, created, modified
  </sql>

  
  <insert id="batchInsertSubUsersOfShop" parameterType="list">
  	INSERT INTO pes_shop_account (seller_id, nick, shop_id, role, status,user_name,is_account,created,source)
    VALUES 
    <foreach collection="list" item="itm" separator=",">
	    (
	    	#{itm.sellerId,jdbcType=BIGINT}, #{itm.nick,jdbcType=VARCHAR}, 
	      	#{itm.shopId,jdbcType=BIGINT}, #{itm.role,jdbcType=VARCHAR}, 
	      	#{itm.status,jdbcType=INTEGER},#{itm.userName,jdbcType=VARCHAR},#{itm.isAccount},#{itm.created},#{itm.source}
	     )
    </foreach>
  
  </insert>
  
  <delete id="deleteShopAccountByShopId" parameterType="java.lang.String">
    DELETE FROM pes_shop_account
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
  </delete>
    <delete id="deleteShopAccountByNick">
        DELETE FROM pes_shop_account
    WHERE
    	nick = #{nick}
    </delete>
  <update id="updateSubUserRole" parameterType="java.lang.String">
	UPDATE pes_shop_account
	SET role = #{type}
	WHERE
	nick = #{nick}
  </update>
  
  <select id="selectShopAccountOfShop" parameterType="java.lang.String" resultMap="ShopAccountDO">
		SELECT 
		seller_id, nick, shop_id, role, status,user_name,is_account,source 
		FROM pes_shop_account
		WHERE
			shop_id =#{shopId}
		ORDER BY nick
	</select>

    <select id="selectShopAccountOfShopStatus" parameterType="long" resultMap="ShopAccountDTO">
		SELECT
		seller_id, nick, shop_id, role, status,user_name,is_account,source
		FROM pes_shop_account
		WHERE
			shop_id =#{shopId}
			<if test="nick != null and nick != ''">
                <choose>
                    <when test="exactSearch">
                        and nick = #{nick}
                    </when>
                    <otherwise>
                        and nick like CONCAT('%',#{nick},'%')
                    </otherwise>
                </choose>
            </if>
<!--		and status = 1-->
	</select>
  
  <select id="selectShopAccountOfGroup" parameterType="java.lang.String" resultMap="ShopAccountDO">
		SELECT 
		sa.seller_id, sa.nick, sa.shop_id, sa.role, sa.status
		FROM pes_shop_account sa
		INNER JOIN pes_group_cs gs
		ON sa.nick = gs.nick
		WHERE
			shop_id =#{shopId}
			AND group_id = #{groupId}
			<if test="nick!=null and nick!=''">
				AND nick = #{nick}
			</if>
		ORDER BY nick
	</select>
	
	<select id="getShopAccountByNick" parameterType="java.lang.String" resultMap="ShopAccountDO">
		SELECT shop_id as shopId , nick , role
		FROM pes_shop_account
		WHERE
			nick = #{nick}
	</select>

	<select id="selectShopAccountByShopIdByNickByStatus" parameterType="map" resultMap="ShopAccountDTO">
		SELECT shop_id, nick, role, status,is_account
		FROM pes_shop_account
		<where>
			<if test="shopId!=null and shopId!=''.toString()">
					shop_id =#{shopId}
			</if>
			<if test="nick!=null and nick!=''.toString()">
				<if test="flag != null and flag == 1">
                    AND nick = #{nick}
                </if>
                <if test="flag != null and flag == 2">
                    AND nick like CONCAT('%',#{nick},'%')
                </if>
			</if>
			<if test="flag==1">
                AND (
					nick not in (select DISTINCT cs_nick from (
					select (cs_nick) from pes_user_service_permission
					UNION
					select (cs_nick) from pes_user_menu_permission
					) tmp)
                <if test="managerOverlay">
                  OR is_account =1 or role = 'M'
                </if>
                )
            </if>
            <if test="flag==2">
                AND ( nick in (select DISTINCT cs_nick from (
                select (cs_nick) from pes_user_service_permission
                UNION
                select (cs_nick) from pes_user_menu_permission
                ) tmp)
                <if test="managerOverlay">
                  OR is_account =1 or role = 'M'
                  </if>
                )
            </if>
			<if test="status != null">
				AND status = #{status}
			</if>
			ORDER BY nick
		</where>
	</select>
<!--    <select id="test" parameterType="map" resultMap="ShopAccountDTO">-->
<!--        SELECT shop_id, nick, role, status,is_account-->
<!--        FROM pes_shop_account-->
<!--        <where>-->
<!--            <if test="shopId!=null and shopId!=''.toString()">-->
<!--                shop_id =#{shopId}-->
<!--            </if>-->
<!--            <if test="nick!=null and nick!=''.toString()">-->
<!--                <if test="flag != null and flag == 1">-->
<!--                    AND nick = #{nick}-->
<!--                </if>-->
<!--                <if test="flag != null and flag == 2">-->
<!--                    AND nick like CONCAT('%',#{nick},'%')-->
<!--                </if>-->
<!--            </if>-->
<!--            <if test="flag==2">-->
<!--                AND ( nick in (select DISTINCT cs_nick from (-->
<!--                select (cs_nick) from pes_user_service_permission-->
<!--                UNION-->
<!--                select (cs_nick) from pes_user_menu_permission-->
<!--                ) tmp)-->
<!--                <if test="managerOverlay">-->
<!--                    OR is_account =1 or role = 'M'-->
<!--                </if>-->
<!--                )-->
<!--            </if>-->
<!--            <if test="status != null">-->
<!--                AND status = #{status}-->
<!--            </if>-->
<!--            ORDER BY nick-->
<!--        </where>-->
<!--    </select>-->
	<!-- 该方法用于客服管理，勿动，如下有需要请另写方法 -->
	<select id="selectShopAccountByShopIdByNickByStatusForCsManager" parameterType="map" resultMap="ShopAccountDTO">
		SELECT shop_id, nick, role, status,is_account
		FROM pes_shop_account
		<where>
			<if test="shopId!=null and shopId!=''.toString()">
					shop_id =#{shopId}
			</if>
			<if test="nick!=null and nick!=''.toString()">
				AND nick like CONCAT(CONCAT('%',#{nick}),'%') 
			</if>
			<if test="status != null">
				AND status = #{status}
			</if>
			ORDER BY nick
		</where>
	</select>
	<select id="getShopMainAccount" parameterType="java.lang.String" resultMap="ShopAccountDO">
		SELECT sa.shop_id as shopId , sa.nick, sa.role
		FROM pes_user u
		LEFT JOIN pes_shop_account sa
		ON u.shop_id = sa.shop_id
		AND u.nick = sa.nick
		WHERE
			sa.shop_id = #{shopId}
			AND u.is_main_account = 1
	</select>

	<select id="getShopMainAccountByShopId" parameterType="java.lang.String" resultMap="ShopAccountDO">
		SELECT sa.shop_id , sa.nick, sa.role, sa.seller_id
		FROM pes_shop_account sa
		WHERE
			sa.shop_id = #{shopId}
			AND sa.is_account = 1
	</select>
	
	<update id="updateShopMainAccountNick">
		UPDATE  pes_shop_account 
			SET 
				nick=#{nick} ,
				user_name=#{userName} ,
				modified=#{modified}
			WHERE 
				shop_id=#{shopId} 
			AND is_account=1
	</update>

  <select id="selectShopAccountByShopId" resultMap="ShopAccountDTO">
	  SELECT id,seller_id sellerId,nick,shop_id shopId,role,status
	  FROM pes_shop_account
	  WHERE
			  shop_id = #{shopId,jdbcType=BIGINT}
  </select>
</mapper>