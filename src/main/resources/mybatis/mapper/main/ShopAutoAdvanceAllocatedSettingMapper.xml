<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopAutoAdvanceAllocatedSettingMapper" >
  <resultMap id="ShopAutoAdvanceAllocatedSettingDTO" type="com.pes.jd.model.DTO.ShopAutoAdvanceAllocatedSettingDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modify" property="modify" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="is_auto_allocated" property="isAutoAllocated" jdbcType="BIT" />
    <result column="cno_not_order_flag" property="cnoNotOrderFlag" jdbcType="TINYINT" />
    <result column="cno_not_order_group_id" property="cnoNotOrderGroupId" jdbcType="BIGINT" />
    <result column="cno_not_order_cs_nick" property="cnoNotOrderCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_order_spare_cs_nick" property="cnoNotOrderSpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_order_spare_group_id" property="cnoNotOrderSpareGroupId" jdbcType="BIGINT" />
    <result column="cno_not_pay_earnest_flag" property="cnoNotPayEarnestFlag" jdbcType="TINYINT" />
    <result column="cno_not_pay_earnest_group_id" property="cnoNotPayEarnestGroupId" jdbcType="BIGINT" />
    <result column="cno_not_pay_earnest_cs_nick" property="cnoNotPayEarnestCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_pay_earnest_spare_cs_nick" property="cnoNotPayEarnestSpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_pay_earnest_spare_group_id" property="cnoNotPayEarnestSpareGroupId" jdbcType="BIGINT" />
    <result column="snp_not_pay_earnest_flag" property="snpNotPayEarnestFlag" jdbcType="TINYINT" />
    <result column="snp_not_pay_earnest_group_id" property="snpNotPayEarnestGroupId" jdbcType="BIGINT" />
    <result column="snp_not_pay_earnest_cs_nick" property="snpNotPayEarnestCsNick" jdbcType="VARCHAR" />
    <result column="snp_not_pay_earnest_spare_cs_nick" property="snpNotPayEarnestSpareCsNick" jdbcType="VARCHAR" />
    <result column="snp_not_pay_earnest_spare_group_id" property="snpNotPayEarnestSpareGroupId" jdbcType="BIGINT" />
    <result column="cno_not_pay_tail_flag" property="cnoNotPayTailFlag" jdbcType="TINYINT" />
    <result column="cno_not_pay_tail_group_id" property="cnoNotPayTailGroupId" jdbcType="BIGINT" />
    <result column="cno_not_pay_tail_cs_nick" property="cnoNotPayTailCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_pay_tail_spare_cs_nick" property="cnoNotPayTailSpareCsNick" jdbcType="VARCHAR" />
    <result column="cno_not_pay_tail_spare_group_id" property="cnoNotPayTailSpareGroupId" jdbcType="BIGINT" />
    <result column="snp_not_pay_tail_flag" property="snpNotPayTailFlag" jdbcType="TINYINT" />
    <result column="snp_not_pay_tail_group_id" property="snpNotPayTailGroupId" jdbcType="BIGINT" />
    <result column="snp_not_pay_tail_cs_nick" property="snpNotPayTailCsNick" jdbcType="VARCHAR" />
    <result column="snp_not_pay_tail_spare_cs_nick" property="snpNotPayTailSpareCsNick" jdbcType="VARCHAR" />
    <result column="snp_not_pay_tail_spare_group_id" property="snpNotPayTailSpareGroupId" jdbcType="BIGINT" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, created, modify, status, is_auto_allocated, cno_not_order_flag, cno_not_order_group_id, 
    cno_not_order_cs_nick, cno_not_order_spare_cs_nick, cno_not_order_spare_group_id, 
    cno_not_pay_earnest_flag, cno_not_pay_earnest_group_id, cno_not_pay_earnest_cs_nick, 
    cno_not_pay_earnest_spare_cs_nick, cno_not_pay_earnest_spare_group_id, snp_not_pay_earnest_flag, 
    snp_not_pay_earnest_group_id, snp_not_pay_earnest_cs_nick, snp_not_pay_earnest_spare_cs_nick, 
    snp_not_pay_earnest_spare_group_id, cno_not_pay_tail_flag, cno_not_pay_tail_group_id, 
    cno_not_pay_tail_cs_nick, cno_not_pay_tail_spare_cs_nick, cno_not_pay_tail_spare_group_id, 
    snp_not_pay_tail_flag, snp_not_pay_tail_group_id, snp_not_pay_tail_cs_nick, snp_not_pay_tail_spare_cs_nick, 
    snp_not_pay_tail_spare_group_id
  </sql>
  <select id="selectShopAutoAdvanceAllocatedSettingByShopId" resultMap="ShopAutoAdvanceAllocatedSettingDTO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_shop_auto_advance_allocated_setting
    where shop_id = #{shopId,jdbcType=BIGINT}
    and status=1
  </select>

</mapper>