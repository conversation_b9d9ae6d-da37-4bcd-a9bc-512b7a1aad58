<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.SysettingAppVersionMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.SysettingAppVersionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>

  <resultMap id="SysettingAppVersionDTO" type="com.pes.jd.model.DTO.SysettingAppVersionDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>

  <sql id="Base_Column_List">
    id,`desc`, `status`
  </sql>

  <select id="seletAppVersionForLst" resultMap="SysettingAppVersionDTO">
    SELECT
     <include refid="Base_Column_List"/>
    FROM pes_sysetting_app_version
  </select>

  <select id="seletAppVersionByVersionCode" resultType="long" parameterType="java.lang.String">

    SELECT psav.id FROM  pes_sysetting_app_version AS psav
    LEFT JOIN pes_itemcode_subuser AS pis
    ON
		psav.id=pis.app_version_id
    WHERE
     pis.item_code=#{versionCode}
  </select>


</mapper>