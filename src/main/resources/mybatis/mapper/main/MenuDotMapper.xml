<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.MenuDotMapper">

    <resultMap id="MenuDotDO" type="com.pes.jd.model.DO.MenuDotDO">
        <id column="dot_id" jdbcType="BIGINT" property="dotId" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="nick" jdbcType="VARCHAR" property="nick" />
        <result column="page_visit_name" jdbcType="VARCHAR" property="pageVisitName" />
        <result column="page_stop_time" jdbcType="VARCHAR" property="pageStopTime" />
        <result column="dot_time" jdbcType="TIMESTAMP" property="dotTime" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="login_num" jdbcType="BIGINT" property="loginNum" />
    </resultMap>

    <resultMap id="MenuDotDTO" type="com.pes.jd.model.DTO.MenuDotDTO">
        <id column="dot_id" jdbcType="BIGINT" property="dotId" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="nick" jdbcType="VARCHAR" property="nick" />
        <result column="page_visit_name" jdbcType="VARCHAR" property="pageVisitName" />
        <result column="page_stop_time" jdbcType="VARCHAR" property="pageStopTime" />
        <result column="dot_time" jdbcType="TIMESTAMP" property="dotTime" />
        <result column="shop_name" jdbcType="VARCHAR" property="shopName" />
        <result column="login_num" jdbcType="BIGINT" property="loginNum" />
    </resultMap>

    <insert id="inserMenuDot" parameterType="com.pes.jd.model.DO.MenuDotDO" >
        insert into pes_menu_dot (nick,page_visit_name,page_stop_time,dot_time,shop_id,shop_name,login_num
        )
        values (  #{nick,jdbcType=VARCHAR}, #{pageVisitName,jdbcType=VARCHAR},
                 #{pageStopTime,jdbcType=VARCHAR},#{dotTime,jdbcType=TIMESTAMP},
                 #{shopId,jdbcType=BIGINT},#{shopName,jdbcType=VARCHAR},#{loginNum,jdbcType=BIGINT}
        )
    </insert>

    <select id="listMenuDotByDate" resultMap="MenuDotDTO">
        SELECT
            dot_id,shop_id,nick,page_visit_name,page_stop_time,dot_time,login_num
        FROM
            pes_menu_dot
        WHERE
            dot_time BETWEEN #{sDate} and #{eDate}
            <choose>
                <when test="pageName != null and pageName != '' ">
                    and page_visit_name
                    in
                    ('首页-已产生中差评', '首页-已产生慢响应', '首页-客服违规量', '首页-顾客辱骂量', '首页-广告页点击次数', '首页-催拍催付效果统计', '热点咨询分析-昨日', '热点咨询分析-近7天', '热点咨询分析-近两周',
                     '热点咨询分析-导出', '热点咨询分析-获取更多店铺热点', '热点咨询分析-获取更多行业热点', '客服绩效提升-敏感词拦截', '客服绩效提升-敏感词拦截-新增', '热点咨询分析-筛选项-商品')
                </when>
                <when test="pageName == '' ">
                    and page_visit_name
                    not in
                    ('首页-已产生中差评', '首页-已产生慢响应', '首页-客服违规量', '首页-顾客辱骂量', '首页-广告页点击次数', '首页-催拍催付效果统计', '热点咨询分析-昨日', '热点咨询分析-近7天', '热点咨询分析-近两周',
                    '热点咨询分析-导出', '热点咨询分析-获取更多店铺热点', '热点咨询分析-获取更多行业热点', '客服绩效提升-敏感词拦截', '客服绩效提升-敏感词拦截-新增', '热点咨询分析-筛选项-商品')
                </when>
            </choose>
    </select>

    <select id="listMenuDotByShopNameAndNick" resultMap="MenuDotDTO">
        SELECT
            dot_id,shop_id,nick,page_visit_name,page_stop_time,dot_time,shop_name,login_num
        FROM
            pes_menu_dot
        WHERE
            page_visit_name = #{pageVisitName}
        <if test="shopName!=null and shopName!=''">
        AND
             shop_name LIKE concat(concat('%',#{shopName}),'%')
        </if>
        <if test="nick!=null and nick!=''">
        AND
             nick LIKE concat(concat('%',#{nick}),'%')
        </if>
        <if test="startDate !=null and endDate != null" >
        AND
            dot_time BETWEEN #{startDate} and #{endDate}
        </if>
    </select>

    <select id="listMenuDotByShopId" resultMap="MenuDotDTO">
         SELECT
            dot_id,shop_id,nick,page_visit_name,page_stop_time,dot_time,shop_name,login_num
        FROM
            pes_menu_dot
        WHERE
           shop_id = #{shopId}
    </select>

    <select id="listAllMenuDot" resultMap="MenuDotDTO">
        SELECT
            dot_id,shop_id,nick,page_visit_name,page_stop_time,dot_time,shop_name,login_num
        FROM
            pes_menu_dot
    </select>

    <delete id="deleteMenuDotByDate" >
        DELETE
        FROM
            pes_menu_dot
        WHERE
            dot_time &lt;
        #{deleteDate}
    </delete>

</mapper>