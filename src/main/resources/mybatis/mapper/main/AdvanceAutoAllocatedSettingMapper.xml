<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.AdvanceAutoAllocatedSettingMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.AdvanceAutoAllocatedSettingDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="created" jdbcType="TIMESTAMP" property="created" />
        <result column="modify" jdbcType="TIMESTAMP" property="modify" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="is_auto_allocated" jdbcType="BIT" property="isAutoAllocated" />
        <result column="cno_not_order_flag" jdbcType="TINYINT" property="cnoNotOrderFlag" />
        <result column="cno_not_order_group_id" jdbcType="BIGINT" property="cnoNotOrderGroupId" />
        <result column="cno_not_order_cs_nick" jdbcType="VARCHAR" property="cnoNotOrderCsNick" />
        <result column="cno_not_order_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotOrderSpareCsNick" />
        <result column="cno_not_order_spare_group_id" jdbcType="BIGINT" property="cnoNotOrderSpareGroupId" />
        <result column="cno_not_pay_earnest_flag" jdbcType="TINYINT" property="cnoNotPayEarnestFlag" />
        <result column="cno_not_pay_earnest_group_id" jdbcType="BIGINT" property="cnoNotPayEarnestGroupId" />
        <result column="cno_not_pay_earnest_cs_nick" jdbcType="VARCHAR" property="cnoNotPayEarnestCsNick" />
        <result column="cno_not_pay_earnest_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotPayEarnestSpareCsNick" />
        <result column="cno_not_pay_earnest_spare_group_id" jdbcType="BIGINT" property="cnoNotPayEarnestSpareGroupId" />
        <result column="snp_not_pay_earnest_flag" jdbcType="TINYINT" property="snpNotPayEarnestFlag" />
        <result column="snp_not_pay_earnest_group_id" jdbcType="BIGINT" property="snpNotPayEarnestGroupId" />
        <result column="snp_not_pay_earnest_cs_nick" jdbcType="VARCHAR" property="snpNotPayEarnestCsNick" />
        <result column="snp_not_pay_earnest_spare_cs_nick" jdbcType="VARCHAR" property="snpNotPayEarnestSpareCsNick" />
        <result column="snp_not_pay_earnest_spare_group_id" jdbcType="BIGINT" property="snpNotPayEarnestSpareGroupId" />
        <result column="cno_not_pay_tail_flag" jdbcType="TINYINT" property="cnoNotPayTailFlag" />
        <result column="cno_not_pay_tail_group_id" jdbcType="BIGINT" property="cnoNotPayTailGroupId" />
        <result column="cno_not_pay_tail_cs_nick" jdbcType="VARCHAR" property="cnoNotPayTailCsNick" />
        <result column="cno_not_pay_tail_spare_cs_nick" jdbcType="VARCHAR" property="cnoNotPayTailSpareCsNick" />
        <result column="cno_not_pay_tail_spare_group_id" jdbcType="BIGINT" property="cnoNotPayTailSpareGroupId" />
        <result column="snp_not_pay_tail_flag" jdbcType="TINYINT" property="snpNotPayTailFlag" />
        <result column="snp_not_pay_tail_group_id" jdbcType="BIGINT" property="snpNotPayTailGroupId" />
        <result column="snp_not_pay_tail_cs_nick" jdbcType="VARCHAR" property="snpNotPayTailCsNick" />
        <result column="snp_not_pay_tail_spare_cs_nick" jdbcType="VARCHAR" property="snpNotPayTailSpareCsNick" />
        <result column="snp_not_pay_tail_spare_group_id" jdbcType="BIGINT" property="snpNotPayTailSpareGroupId" />
    </resultMap>
    <sql id="Base_Column_List">
    id, shop_id, created, modify, status, is_auto_allocated, cno_not_order_flag, cno_not_order_group_id,
    cno_not_order_cs_nick, cno_not_order_spare_cs_nick, cno_not_order_spare_group_id,
    cno_not_pay_earnest_flag, cno_not_pay_earnest_group_id, cno_not_pay_earnest_cs_nick,
    cno_not_pay_earnest_spare_cs_nick, cno_not_pay_earnest_spare_group_id, snp_not_pay_earnest_flag,
    snp_not_pay_earnest_group_id, snp_not_pay_earnest_cs_nick, snp_not_pay_earnest_spare_cs_nick,
    snp_not_pay_earnest_spare_group_id, cno_not_pay_tail_flag, cno_not_pay_tail_group_id,
    cno_not_pay_tail_cs_nick, cno_not_pay_tail_spare_cs_nick, cno_not_pay_tail_spare_group_id,
    snp_not_pay_tail_flag, snp_not_pay_tail_group_id, snp_not_pay_tail_cs_nick, snp_not_pay_tail_spare_cs_nick,
    snp_not_pay_tail_spare_group_id
  </sql>

    <select id="selectShopAutoAllocatedSettingByShopId" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from pes_shop_auto_advance_allocated_setting
        where shop_id=#{shopId}
    </select>



    <insert id="insertShopAutoAllocatedSetting" parameterType="com.pes.jd.model.DTO.AdvanceAutoAllocatedSettingDTO">
    insert into pes_shop_auto_advance_allocated_setting (shop_id, created,
      modify, status, is_auto_allocated,
      cno_not_order_flag, cno_not_order_group_id, cno_not_order_cs_nick,
      cno_not_order_spare_cs_nick, cno_not_order_spare_group_id,
      cno_not_pay_earnest_flag, cno_not_pay_earnest_group_id,
      cno_not_pay_earnest_cs_nick, cno_not_pay_earnest_spare_cs_nick,
      cno_not_pay_earnest_spare_group_id, snp_not_pay_earnest_flag,
      snp_not_pay_earnest_group_id, snp_not_pay_earnest_cs_nick,
      snp_not_pay_earnest_spare_cs_nick, snp_not_pay_earnest_spare_group_id,
      cno_not_pay_tail_flag, cno_not_pay_tail_group_id,
      cno_not_pay_tail_cs_nick, cno_not_pay_tail_spare_cs_nick,
      cno_not_pay_tail_spare_group_id, snp_not_pay_tail_flag,
      snp_not_pay_tail_group_id, snp_not_pay_tail_cs_nick,
      snp_not_pay_tail_spare_cs_nick, snp_not_pay_tail_spare_group_id
      )
    values (#{shopId,jdbcType=BIGINT}, #{created,jdbcType=TIMESTAMP},
      #{modify,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{isAutoAllocated,jdbcType=BIT},
      #{cnoNotOrderFlag,jdbcType=TINYINT}, #{cnoNotOrderGroupId,jdbcType=BIGINT}, #{cnoNotOrderCsNick,jdbcType=VARCHAR},
      #{cnoNotOrderSpareCsNick,jdbcType=VARCHAR}, #{cnoNotOrderSpareGroupId,jdbcType=BIGINT},
      #{cnoNotPayEarnestFlag,jdbcType=TINYINT}, #{cnoNotPayEarnestGroupId,jdbcType=BIGINT},
      #{cnoNotPayEarnestCsNick,jdbcType=VARCHAR}, #{cnoNotPayEarnestSpareCsNick,jdbcType=VARCHAR},
      #{cnoNotPayEarnestSpareGroupId,jdbcType=BIGINT}, #{snpNotPayEarnestFlag,jdbcType=TINYINT},
      #{snpNotPayEarnestGroupId,jdbcType=BIGINT}, #{snpNotPayEarnestCsNick,jdbcType=VARCHAR},
      #{snpNotPayEarnestSpareCsNick,jdbcType=VARCHAR}, #{snpNotPayEarnestSpareGroupId,jdbcType=BIGINT},
      #{cnoNotPayTailFlag,jdbcType=TINYINT}, #{cnoNotPayTailGroupId,jdbcType=BIGINT},
      #{cnoNotPayTailCsNick,jdbcType=VARCHAR}, #{cnoNotPayTailSpareCsNick,jdbcType=VARCHAR},
      #{cnoNotPayTailSpareGroupId,jdbcType=BIGINT}, #{snpNotPayTailFlag,jdbcType=TINYINT},
      #{snpNotPayTailGroupId,jdbcType=BIGINT}, #{snpNotPayTailCsNick,jdbcType=VARCHAR},
      #{snpNotPayTailSpareCsNick,jdbcType=VARCHAR}, #{snpNotPayTailSpareGroupId,jdbcType=BIGINT}
      )
  </insert>

    <update id="updateShopAutoAllocatedSettingById" parameterType="com.pes.jd.model.DTO.AdvanceAutoAllocatedSettingDTO">
        update pes_shop_auto_advance_allocated_setting
        <set>
            <if test="created != null">
                created = #{created,jdbcType=TIMESTAMP},
            </if>
            <if test="modify != null">
                modify = #{modify,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="isAutoAllocated != null">
                is_auto_allocated = #{isAutoAllocated,jdbcType=BIT},
            </if>

                cno_not_order_flag = #{cnoNotOrderFlag,jdbcType=TINYINT},

                cno_not_order_group_id = #{cnoNotOrderGroupId,jdbcType=BIGINT},

                cno_not_order_cs_nick = #{cnoNotOrderCsNick,jdbcType=VARCHAR},

                cno_not_order_spare_cs_nick = #{cnoNotOrderSpareCsNick,jdbcType=VARCHAR},

                cno_not_order_spare_group_id = #{cnoNotOrderSpareGroupId,jdbcType=BIGINT},

                cno_not_pay_earnest_flag = #{cnoNotPayEarnestFlag,jdbcType=TINYINT},

                cno_not_pay_earnest_group_id = #{cnoNotPayEarnestGroupId,jdbcType=BIGINT},

                cno_not_pay_earnest_cs_nick = #{cnoNotPayEarnestCsNick,jdbcType=VARCHAR},

                cno_not_pay_earnest_spare_cs_nick = #{cnoNotPayEarnestSpareCsNick,jdbcType=VARCHAR},

                cno_not_pay_earnest_spare_group_id = #{cnoNotPayEarnestSpareGroupId,jdbcType=BIGINT},

                snp_not_pay_earnest_flag = #{snpNotPayEarnestFlag,jdbcType=TINYINT},

                snp_not_pay_earnest_group_id = #{snpNotPayEarnestGroupId,jdbcType=BIGINT},

                snp_not_pay_earnest_cs_nick = #{snpNotPayEarnestCsNick,jdbcType=VARCHAR},

                snp_not_pay_earnest_spare_cs_nick = #{snpNotPayEarnestSpareCsNick,jdbcType=VARCHAR},

                snp_not_pay_earnest_spare_group_id = #{snpNotPayEarnestSpareGroupId,jdbcType=BIGINT},

                cno_not_pay_tail_flag = #{cnoNotPayTailFlag,jdbcType=TINYINT},

                cno_not_pay_tail_group_id = #{cnoNotPayTailGroupId,jdbcType=BIGINT},

                cno_not_pay_tail_cs_nick = #{cnoNotPayTailCsNick,jdbcType=VARCHAR},

                cno_not_pay_tail_spare_cs_nick = #{cnoNotPayTailSpareCsNick,jdbcType=VARCHAR},

                cno_not_pay_tail_spare_group_id = #{cnoNotPayTailSpareGroupId,jdbcType=BIGINT},

                snp_not_pay_tail_flag = #{snpNotPayTailFlag,jdbcType=TINYINT},

                snp_not_pay_tail_group_id = #{snpNotPayTailGroupId,jdbcType=BIGINT},

                snp_not_pay_tail_cs_nick = #{snpNotPayTailCsNick,jdbcType=VARCHAR},

                snp_not_pay_tail_spare_cs_nick = #{snpNotPayTailSpareCsNick,jdbcType=VARCHAR},


                snp_not_pay_tail_spare_group_id = #{snpNotPayTailSpareGroupId,jdbcType=BIGINT},

        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateCloseAdvanceAutoAllocatedByShopId" >
         update pes_shop_auto_advance_allocated_setting set  is_auto_allocated =#{autoAllocated} where shop_id=#{shopId}
  </update>
</mapper>