<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.MemberCentreMapper">

	<resultMap id="MemberCentreDTO" type="com.pes.jd.model.DTO.MemberCentreDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="shop_title" jdbcType="VARCHAR" property="shopTitle" />
		<result column="user_nick" jdbcType="VARCHAR" property="userNick" />
		<result column="contact" jdbcType="VARCHAR" property="contact" />
		<result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
		<result column="position" jdbcType="TINYINT" property="position" />
		<result column="start_time" jdbcType="TIMESTAMP" property="orderDate" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="shopTitle" />
		<result column="status" jdbcType="VARCHAR" property="shopStatus" />
		<result column="qq" jdbcType="VARCHAR" property="qq" />
		<result column="email" jdbcType="VARCHAR" property="email" />
	</resultMap>

	<insert id="insert" parameterType="com.pes.jd.model.DO.MemberCentreDO" >
	 	INSERT INTO pes_member_centre (
			shop_id, shop_title, user_nick, contact, phone_number, position, created, qq, email, eject_time
			)
		VALUES
			(
				#{shopId,jdbcType=BIGINT},
				#{shopTitle,jdbcType=VARCHAR},
				#{userNick,jdbcType=VARCHAR},
				#{contact,jdbcType=VARCHAR},
				#{phoneNumber,jdbcType=VARCHAR},
				#{position,jdbcType=TINYINT},
				#{created,jdbcType=TIMESTAMP},
			 	#{qq},
			 	#{email},
			 	#{ejectTime}
			)
	</insert>

    <insert id="insertForInitialization">
		insert into pes_member_centre
		(shop_id, shop_title, user_nick, phone_number, created, qq, email, eject_time)
		values
		(#{shopId}, #{title}, #{csNick}, #{phone}, #{date}, #{qq}, #{email}, #{date})
	</insert>
	<insert id="insertMemberCentre">
		insert into pes_member_centre
			(shop_id, shop_title, user_nick, eject_time, created)
		values
			(#{memberCentre.shopId}, #{memberCentre.shopTitle}, #{memberCentre.userNick}, #{memberCentre.ejectTime}, #{memberCentre.created})
	</insert>

	<delete id="deleteById" parameterType="java.lang.Long">
		DELETE FROM pes_member_centre
		WHERE 
			id = #{id,jdbcType=BIGINT}
	</delete>

	<update id="update" parameterType="map">
		update pes_member_centre set
			contact=#{contact},
			<if test="phoneNumber != null and phoneNumber != ''">
				phone_number=#{phoneNumber},
			</if>
			position=#{position},
			qq = #{qq},
			email = #{email},
		    eject_time = #{ejectTime}
			where id=#{id}
	</update>

	<update id="updateEjectTimeByNick">
		update pes_member_centre set eject_time = #{date} where shop_id = #{shopId} and user_nick = #{nick}
	</update>

	<select id="getMemberInfoByShopId" resultMap="MemberCentreDTO">
		select id, shop_id, shop_title, user_nick, contact, phone_number, position, created, qq, email
		FROM
			pes_member_centre
		where
			shop_id = #{shopId} and user_nick = #{nick}
	</select>

	<select id="selectMemberInfoBySubDateAndShopAndPosition" resultMap="MemberCentreDTO">
		select 	m.id, m.shop_id, m.shop_title, m.user_nick, m.contact, m.phone_number, m.position, m.qq, m.email,
				s.seller_nick, s.status, sub.start_time
		FROM
			pes_member_centre m, pes_shop s,
			(select max(start_time) start_time, shop_id from pes_shop_subcribe group by shop_id) sub
		where
			m.shop_id = s.shop_id and sub.shop_id = s.shop_id and s.type =  #{shopType}  
			<if test="startDate != null and endDate != null">
				and m.created between #{startDate} and #{endDate}
			</if>
			<if test="nick != null and nick != ''">
				and (s.seller_nick like concat('%', #{nick}, '%')
				or s.title like concat('%', #{nick}, '%'))
			</if>
			<if test="position != -1">
				and m.position = #{position}
			</if>
			<if test="status != null and status != ''">
				and s.status = #{status}
			</if>
			order by m.created desc
	</select>

    <select id="listMemberInfosByShopIdCsNick" resultType="com.pes.jd.model.DTO.NewMemberCentreDTO">
		select * from pes_member_centre
		where shop_id = #{shopId}
		and user_nick = #{nick}
	</select>

</mapper>