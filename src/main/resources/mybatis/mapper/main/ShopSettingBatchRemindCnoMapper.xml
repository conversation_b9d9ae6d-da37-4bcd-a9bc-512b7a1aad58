<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.main.ShopSettingBatchRemindCnoMapper" >
  <resultMap id="ShopSettingBatchRemindCnoDO" type="com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="is_remind" property="isRemind" jdbcType="BIT" />
    <result column="cno_time" property="cnoTime" jdbcType="INTEGER" />
    <result column="cno_word_id" property="cnoWordId" jdbcType="VARCHAR" />
    <result column="remind_start_dot" property="remindStartDot" jdbcType="INTEGER" />
    <result column="remind_end_dot" property="remindEndDot" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
  </resultMap>

  <resultMap id="ShopSettingBatchRemindCno" type="com.pes.jd.ms.domain.Data.master.ShopSettingBatchRemindCno" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="is_remind" property="isRemind" jdbcType="BIT" />
    <result column="cno_time" property="cnoTime" jdbcType="INTEGER" />
    <result column="cno_word_id" property="cnoWordId" jdbcType="VARCHAR" />
    <result column="remind_start_dot" property="remindStartDot" jdbcType="INTEGER" />
    <result column="remind_end_dot" property="remindEndDot" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, is_remind, cno_time, cno_word_id, remind_start_dot, remind_end_dot, 
    created, modified
  </sql>
  <select id="selectShopSettingBatchRemindCnoByshopId" resultMap="ShopSettingBatchRemindCno" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_shop_setting_batch_remind_cno
    where shop_id = #{shopId,jdbcType=BIGINT}
  </select>

  <insert id="insertShopSettingBatchRemindCno" parameterType="com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO" >
    insert into pes_shop_setting_batch_remind_cno (id, shop_id, is_remind, 
      cno_time, cno_word_id, remind_start_dot, 
      remind_end_dot, created, modified
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{isRemind,jdbcType=BIT}, 
      #{cnoTime,jdbcType=INTEGER}, #{cnoWordId,jdbcType=VARCHAR}, #{remindStartDot,jdbcType=INTEGER},
      #{remindEndDot,jdbcType=INTEGER}, #{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}
      )
  </insert>

  <update id="updateShopSettingBatchRemindCno" parameterType="com.pes.jd.model.DO.ShopSettingBatchRemindCnoDO" >
    update pes_shop_setting_batch_remind_cno
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isRemind != null" >
        is_remind = #{isRemind,jdbcType=BIT},
      </if>
      <if test="cnoTime != null" >
        cno_time = #{cnoTime,jdbcType=INTEGER},
      </if>
      cno_word_id = #{cnoWordId,jdbcType=VARCHAR},
      <if test="remindStartDot != null" >
        remind_start_dot = #{remindStartDot,jdbcType=INTEGER},
      </if>
      <if test="remindEndDot != null" >
        remind_end_dot = #{remindEndDot,jdbcType=INTEGER},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectSettingByShopIds" resultMap="ShopSettingBatchRemindCnoDO">
        select * from pes_shop_setting_batch_remind_cno where shop_id in
        <foreach collection="shopIds" item="shopId" separator="," open="(" close=")">
            #{shopId}
        </foreach>
    </select>

  <select id="selectShopSettingBatchRemindCnoByShopId" resultMap="ShopSettingBatchRemindCno">
    select
    <include refid="base_field" />
    from pes_shop_setting_batch_remind_cno
    where shop_id = #{shopId,jdbcType=BIGINT}
  </select>

  <select id="selecttShopSettingBatchRemindCnoByShopId" resultMap="ShopSettingBatchRemindCno">
    select
    is_remind
    from
    pes_shop_setting_batch_remind_cno
    where shop_id = #{shopId}
  </select>

  <update id="updateShopSettingBatchRemindCnoByShopIdAndIsRemind">
    update pes_shop_setting_batch_remind_cno
    set is_remind=#{isRemind}
    where shop_id = #{shopId}
  </update>
</mapper>