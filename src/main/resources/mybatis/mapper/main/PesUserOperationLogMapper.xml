<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.PesUserOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesUserOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="datetime" jdbcType="TIMESTAMP" property="datetime" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="opt_type" jdbcType="VARCHAR" property="optType" />
    <result column="opt_content" jdbcType="VARCHAR" property="optContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, nick, datetime, shop_id, opt_type, opt_content
  </sql>
  <!-- 查询操作日志 BEGIN-->
  <select id="searchOperationLogByTypeTimeNick" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pes_user_operation_log
    <where>
      datetime between  #{startDate} and #{endDate}
      <if test=" nick != null and nick != ''">
        and nick LIKE concat('%',#{nick},'%')
      </if>
      <if test=" optType!=null and optType!='' ">
        and opt_type = #{optType}
      </if>
      and shop_id = #{shopId}
    </where>
  </select>
  <!-- 查询操作日志 END-->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_user_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_user_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.PesUserOperationLog">
    insert into pes_user_operation_log (id, user_id, nick, 
      datetime, shop_id, opt_type, 
      opt_content)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR}, 
      #{datetime,jdbcType=TIMESTAMP}, #{shopId,jdbcType=BIGINT}, #{optType,jdbcType=VARCHAR}, 
      #{optContent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.PesUserOperationLog">
    insert into pes_user_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="nick != null">
        nick,
      </if>
      <if test="datetime != null">
        datetime,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="optType != null">
        opt_type,
      </if>
      <if test="optContent != null">
        opt_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="nick != null">
        #{nick,jdbcType=VARCHAR},
      </if>
      <if test="datetime != null">
        #{datetime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="optType != null">
        #{optType,jdbcType=VARCHAR},
      </if>
      <if test="optContent != null">
        #{optContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.PesUserOperationLog">
    update pes_user_operation_log
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="nick != null">
        nick = #{nick,jdbcType=VARCHAR},
      </if>
      <if test="datetime != null">
        datetime = #{datetime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="optType != null">
        opt_type = #{optType,jdbcType=VARCHAR},
      </if>
      <if test="optContent != null">
        opt_content = #{optContent,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.PesUserOperationLog">
    update pes_user_operation_log
    set user_id = #{userId,jdbcType=BIGINT},
      nick = #{nick,jdbcType=VARCHAR},
      datetime = #{datetime,jdbcType=TIMESTAMP},
      shop_id = #{shopId,jdbcType=BIGINT},
      opt_type = #{optType,jdbcType=VARCHAR},
      opt_content = #{optContent,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 查询首次登录操作日志 ( 根据客服昵称 ) BEGIN -->
  <select id="getUserOperationLogByNickAndShop" resultType="int">
    select
    count(1)
    from  pes_user_operation_log
    <where>
      <if test=" nick != null and nick != '' ">
        and nick = #{nick}
      </if>
      and opt_type = #{type}
      and shop_id = #{shopId}
    </where>
  </select>
  <!-- 查询日志 ( 根据客服昵称 ) END -->
  <!-- 查询操作日志 ( 根据时间段和客服昵称 ) BEGIN -->
  <select id="getUserOperationLogByNickAndShopAndTime" resultType="int">
    select
    count(1)
    from  pes_user_operation_log
    <where>
      datetime between #{startDate} and #{endDate}
      <if test=" nick != null and nick != '' ">
        and nick = #{nick}
      </if>
      and opt_type = #{type}
      and shop_id = #{shopId}
    </where>
  </select>
  <!-- 查询日志 ( 根据时间段和客服昵称 ) END -->
</mapper>