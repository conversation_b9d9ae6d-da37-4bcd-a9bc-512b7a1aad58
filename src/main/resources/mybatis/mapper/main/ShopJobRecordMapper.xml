<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.ShopJobRecordMapper">

	<resultMap id="ShopJobRecordDO" type="com.pes.jd.model.DO.ShopJobRecord">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="date" jdbcType="DATE" property="date" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="modified" jdbcType="TIMESTAMP" property="modified" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="type" jdbcType="INTEGER" property="type" />
	</resultMap>

	<sql id="base_field">
		id, shop_id, date, created, modified, status, type
	</sql>

	<insert id="insertShopJobRecord" parameterType="com.pes.jd.model.DO.ShopJobRecord">
		insert into pes_shop_job_record (id, shop_id, date,
      created, modified, status,
      type)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE},
      #{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER},
      #{type,jdbcType=INTEGER})
	</insert>
		
	<delete id="deleteShopJobRecordById" parameterType="java.lang.Long">
		DELETE FROM pes_shop_job_record
		WHERE 
			id = #{id,jdbcType=BIGINT}
	</delete>
	
	
	<update id="updateShopJobRecordById" parameterType="com.pes.jd.model.DO.ShopJobRecord">
		UPDATE pes_shop_job_record
		<set>
			<if test="shopId != null">
				shop_id = #{shopId,jdbcType=BIGINT},
			</if>
			<if test="date != null">
				date = #{date,jdbcType=DATE},
			</if>
			<if test="created != null">
				created = #{created,jdbcType=TIMESTAMP},
			</if>
			<if test="modified != null">
				modified = #{modified,jdbcType=TIMESTAMP},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=INTEGER},
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=BIGINT}
	</update>

	<select id="getShopJobRecordById" parameterType="java.lang.Long" resultMap="ShopJobRecordDO">
		select
		<include refid="base_field" />
		from pes_shop_job_record
		where id = #{id,jdbcType=BIGINT}
	</select>

	<select id="getByDateAndShop" parameterType="com.pes.jd.model.DO.ShopJobRecord" resultMap="ShopJobRecordDO">
		select
		<include refid="base_field" />
		from pes_shop_job_record
		where shop_id = #{shopId,jdbcType=BIGINT} AND date = #{date}
	</select>

</mapper>