<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.main.CsManagerMapper">
  <resultMap id="CsDO" type="com.pes.jd.model.DO.Cs">
    <id column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="simple_name" jdbcType="VARCHAR" property="simpleName" />
      <result column="cs_status" jdbcType="INTEGER" property="csStatus" />
    <result column="lock_time" jdbcType="TIMESTAMP" property="lockTime" />
    <result column="modified_date" jdbcType="TIMESTAMP" property="modifiedDate" />
     <result column="source" jdbcType="INTEGER" property="source" />
  </resultMap>
  <sql id="base_field">
    nick, shop_id, type, simple_name
  </sql>
  
 <select id="selectGroupCsByShopIdByGroupIdByNick" parameterType="map" resultType="com.pes.jd.model.DTO.CsDTO">
	SELECT distinct cs.nick  nick ,
		cs.simple_name csSimpleNick,
	 	cs.shop_id shopId, 
	 	cs.type type, 
	 	cs.cs_status csStatus,
	 	cs.modified_date modifiedDate,
	 	cs.source,
		shop.title title,
		shop.db dbName
	FROM pes_cs cs
		LEFT JOIN pes_shop shop
		ON shop.shop_id=cs.shop_id
		LEFT JOIN pes_group_cs gcs
		ON gcs.nick=cs.nick
		<where>
		<if test="shopId!=null and shopId!=''">
				cs.shop_id =#{shopId}
		</if>
		<if test="groupId != null and groupId!=''">
			AND	gcs.group_id = #{groupId}
		</if>
		<if test="operateType!=null and operateType!=''">
			and cs.cs_status=#{operateType}
		</if>
		<if test="nick != null and nick!=''">
			AND	(cs.nick like concat(concat('%',#{nick}),'%') or cs.simple_name like concat(concat('%',#{nick}),'%')) 
		</if>
		<if test="type!=null and type!=''">
			and cs.type=#{type}
		</if>
		</where>
		ORDER BY cs.nick
	</select>

	<select id="selectShopGroupCsByShopIdByGroupIdByNick"  resultType="com.pes.jd.model.DTO.GroupCsDTO">
		SELECT distinct gcs.group_id groupId,
		gcs.nick nick,
		gcs.shop_id shopId,
		pg.group_name groupName
		from pes_group_cs gcs
		INNER JOIN pes_group pg
		ON gcs.group_id=pg.group_id
		INNER JOIN pes_cs cs
		on cs.nick=gcs.nick
		<where>
			<if test="shopId!=null and shopId!=''">
				gcs.shop_id =#{shopId}
			</if>
			<if test="groupId != null and groupId!=''">
				AND gcs.group_id = #{groupId}
			</if>
			<if test="nick != null and nick!=''">
				AND gcs.nick like concat(concat('%',#{nick}),'%') or cs.simple_name like concat(concat('%',#{nick}),'%')
			</if>
		</where>
	</select>
	<select id="selectGroupDataList" parameterType="map" resultType="com.pes.jd.model.DTO.GroupCsDTO">
	SELECT pge.group_id groupId, pce.nick,pce.type type, pce.simple_name simpleName, pce.shop_id shopId from
	pes_cs pce
	inner join pes_group_cs pge
	on pge.nick = pce.nick
	<where>
		pce.shop_id = #{shopId}
		and pce.cs_status=1
	</where>
	</select>
	
	
	
</mapper>