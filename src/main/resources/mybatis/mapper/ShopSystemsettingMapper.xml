<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopSystemsettingMapper">
	
	<resultMap id="ShopSystemsettingDTO" type="com.pes.jd.model.DTO.ShopSystemsettingDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
     <result column="shop_id" jdbcType="BIGINT" property="shopId" />
     <result column="auto_reply_switch" jdbcType="BIT" property="autoReplySwitch" />
     <result column="auto_reply_mark" jdbcType="VARCHAR" property="autoReplyMark" />
     <result column="aftersell_acount_filter" jdbcType="BIT" property="aftersellAcountFilter" />
     <result column="sell_after" jdbcType="INTEGER" property="sellAfter" />
     <result column="max_wait_time" jdbcType="INTEGER" property="maxWaitTime" />
     <result column="slow_response_time" jdbcType="INTEGER" property="slowResponseTime" />
     <result column="slow_response_times_num" jdbcType="INTEGER" property="slowResponseTimesNum" />
     <result column="quick_response_time" jdbcType="INTEGER" property="quickResponseTime" />
     <result column="long_reception_time" jdbcType="INTEGER" property="longReceptionTime" />
     <result column="scheduling_time_dot" jdbcType="INTEGER" property="schedulingTimeDot" />
     <result column="cs_recommend_switch" jdbcType="BIT" property="csRecommendSwitch" />
     <result column="cs_recommend_mark" jdbcType="VARCHAR" property="csRecommendMark" />
     <result column="duty_record_export_unit" jdbcType="VARCHAR" property="dutyRecordExportUnit" />
     <result column="value_service_sale_amout_switch" jdbcType="BIT" property="valueServiceSaleAmoutSwitch" />
     <result column="value_service_goods_num_switch" jdbcType="BIT" property="valueServiceGoodsNumSwitch" />
     <result column="judge_rule_ascription" jdbcType="TINYINT" property="judgeRuleAscription" />
     <result column="judge_rule" jdbcType="INTEGER" property="judgeRule" />
     <result column="enquiry_valid_duration_time" jdbcType="INTEGER" property="enquiryValidDurationTime" />
     <result column="out_stock_valid_duration_time" jdbcType="INTEGER" property="outStockValidDurationTime" />
     <result column="silent_all_switch" jdbcType="BIT" property="silentAllSwitch" />
     <result column="silent_all_follow_up_time" jdbcType="INTEGER" property="silentAllFollowUpTime" />
     <result column="is_bind_silent_all_order" jdbcType="BIT" property="isBindSilentAllOrder" />
     <result column="active_follow_up_switch" jdbcType="BIT" property="activeFollowUpSwitch" />
     <result column="cust_first_reply_day" jdbcType="INTEGER" property="custFirstReplyDay" />
     <result column="silent_urgepay_switch" jdbcType="BIT" property="silentUrgepaySwitch" />
     <result column="silent_urgepay_time" jdbcType="INTEGER" property="silentUrgepayTime" />
     <result column="order_flag_switch" jdbcType="BIT" property="orderFlagSwitch" />
     <result column="order_flag" jdbcType="BIGINT" property="orderFlag" />
     <result column="enquiry_loss_switch" jdbcType="BIT" property="enquiryLossSwitch" />
     <result column="min_reply_num" jdbcType="INTEGER" property="minReplyNum" />
     <result column="team_chat_filte_switch" jdbcType="BIT" property="teamChatFilteSwitch" />
     <result column="sys_cs_chat_filte_switch" jdbcType="BIT" property="sysCsChatFilteSwitch" />
     <result column="non_chat_filte_switch" jdbcType="BIT" property="nonChatFilteSwitch" />
     <result column="cs_offline_filte_cust_msg_switch" jdbcType="BIT" property="csOfflineFilteCustMsgSwitch" />
     <result column="cs_forward_switch" jdbcType="BIT" property="csForwardSwitch" />
     <result column="cs_forward_num" jdbcType="INTEGER" property="csForwardNum" />
     <result column="jixiao_time" jdbcType="VARCHAR" property="jixiaoTime" />
     <result column="cs_watchword_switch" jdbcType="BIT" property="csWatchwordSwitch" />
     <result column="cs_watchword" jdbcType="VARCHAR" property="csWatchword" />
     <result column="cs_watchword_send_times_num" jdbcType="INTEGER" property="csWatchwordSendTimesNum" />
     <result column="main_account_auto_reply_switch" jdbcType="BIT" property="mainAccountAutoReplySwitch" />
     <result column="main_account_auto_reply_content" jdbcType="VARCHAR" property="mainAccountAutoReplyContent" />
     <result column="cs_to_csut_first_lost_switch" jdbcType="BIT" property="csToCsutFirstLostSwitch" />
     <result column="cust_sigchat_switch" jdbcType="BIT" property="custSigchatSwitch" />
     <result column="sigchat_min_reply_num" jdbcType="INTEGER" property="sigchatMinReplyNum" />
     <result column="cs_single_chat_filter" jdbcType="BIT" property="csSingleChatFilter" />
     <result column="platform_cs_filte_switch" jdbcType="BIT" property="platformCsFilteSwitch" />
     <result column="cust_single_chat_num" jdbcType="INTEGER" property="custSingleChatNum" />
     <result column="cust_chat_word_switch" jdbcType="BIT" property="custChatWordSwitch" />
     <result column="cust_watchword" jdbcType="VARCHAR" property="custWatchword" />
		<result column="cs_forward_aftersell_switch" jdbcType="BIT" property="csForwardAftersellSwitch" />
	</resultMap>
	
	<sql id="base_field">
	id, shop_id, auto_reply_switch, auto_reply_mark, aftersell_acount_filter,
	sell_after,
	max_wait_time, slow_response_time, slow_response_times_num, quick_response_time,
	long_reception_time, scheduling_time_dot, cs_recommend_switch,
	cs_recommend_mark,
	duty_record_export_unit, value_service_sale_amout_switch, value_service_goods_num_switch,
	judge_rule_ascription, judge_rule, enquiry_valid_duration_time,
	out_stock_valid_duration_time,
	silent_all_switch, silent_all_follow_up_time, is_bind_silent_all_order,
	active_follow_up_switch,
	cust_first_reply_day, silent_urgepay_switch, silent_urgepay_time, order_flag_switch,
	order_flag, enquiry_loss_switch, min_reply_num, team_chat_filte_switch,
	sys_cs_chat_filte_switch,
	non_chat_filte_switch, cs_offline_filte_cust_msg_switch, cs_forward_switch, cs_forward_num,
	jixiao_time, cs_watchword_switch, cs_watchword,
	cs_watchword_send_times_num, main_account_auto_reply_switch,
	main_account_auto_reply_content, cs_to_csut_first_lost_switch,
	cust_sigchat_switch,
	sigchat_min_reply_num, cs_single_chat_filter, platform_cs_filte_switch, cust_single_chat_num,
	cust_chat_word_switch, cust_watchword
</sql>
	
	<insert id="insertShopSystemsetting" parameterType="com.pes.jd.model.DO.ShopSystemsettingDO">
		  insert into pes_shop_systemsetting (shop_id, auto_reply_switch, 
      sell_after, max_wait_time, scheduling_time_dot, 
      jixiao_time, judge_rule_ascription, judge_rule, 
      silent_all_switch, silent_all_follow_up_time, min_reply_num, 
      active_follow_up_switch, silent_urgepay_switch, silent_urgepay_time, 
      banner_switch, banner_flag, filter_non_chat, 
      team_chat_filter, sys_cs_chat_filter, cs_watchword_switch, 
      cs_watchword, cs_single_chat_filter, single_chat_num, 
      buyer_chatlog_word_switch, urge_pay_time, urge_order_word, 
      urge_pay_word, filter_buyer_sigchat, filter_eservice_fwd, 
      sigchat_min_reply_num, cs_fwd_chat_num)
    values (#{shopId,jdbcType=BIGINT}, #{autoReplyFilter,jdbcType=BIT}, 
      #{sellAfter,jdbcType=INTEGER}, #{maxWaitTime,jdbcType=INTEGER}, #{schedulingTimeDot,jdbcType=INTEGER}, 
      #{jixiaoTime,jdbcType=VARCHAR}, #{judgeRuleAscription,jdbcType=INTEGER}, #{judgeRule,jdbcType=INTEGER}, 
      #{silentAllSwitch,jdbcType=BIT}, #{silentAllFollowUpTime,jdbcType=INTEGER}, #{minReplyNum,jdbcType=INTEGER}, 
      #{activeFollowUpSwitch,jdbcType=BIT}, #{silentUrgepaySwitch,jdbcType=BIT}, #{silentUrgepayTime,jdbcType=INTEGER}, 
      #{bannerSwitch,jdbcType=BIT}, #{bannerFlag,jdbcType=BIGINT}, #{filterNonChat,jdbcType=BIT}, 
      #{teamChatFilter,jdbcType=BIT}, #{sysCsChatFilter,jdbcType=BIT}, #{csWatchwordSwitch,jdbcType=BIT}, 
      #{csWatchword,jdbcType=VARCHAR}, #{csSingleChatFilter,jdbcType=BIT}, #{singleChatNum,jdbcType=INTEGER}, 
      #{buyerChatlogWordSwitch,jdbcType=BIT}, #{urgePayTime,jdbcType=INTEGER}, #{urgeOrderWord,jdbcType=VARCHAR}, 
      #{urgePayWord,jdbcType=VARCHAR}, #{filterBuyerSigchat,jdbcType=BIT}, #{filterEserviceFwd,jdbcType=BIT}, 
      #{sigchatMinReplyNum,jdbcType=INTEGER}, #{csFwdChatNum,jdbcType=INTEGER})
	</insert>
	
	
	<delete id="deleteShopSystemsettingById" parameterType="java.lang.Integer">
		DELETE FROM pes_shop_systemsetting
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</delete>
	
	<update id="updateShopSystemsettingById" parameterType="com.pes.jd.model.DO.ShopSystemsettingDO">
 		update pes_shop_systemsetting
    <set >
	      <if test="shopId != null" >
	        shop_id = #{shopId,jdbcType=BIGINT},
	      </if>
	      <if test="autoReplyFilter != null" >
	        auto_reply_switch = #{autoReplyFilter,jdbcType=BIT},
	      </if>
	      <if test="sellAfter != null" >
	        sell_after = #{sellAfter,jdbcType=INTEGER},
	      </if>
	      <if test="maxWaitTime != null" >
	        max_wait_time = #{maxWaitTime,jdbcType=INTEGER},
	      </if>
	      <if test="schedulingTimeDot != null" >
	        scheduling_time_dot = #{schedulingTimeDot,jdbcType=INTEGER},
	      </if>
	      <if test="jixiaoTime != null" >
	        jixiao_time = #{jixiaoTime,jdbcType=VARCHAR},
	      </if>
	      <if test="judgeRuleAscription != null" >
	        judge_rule_ascription = #{judgeRuleAscription,jdbcType=INTEGER},
	      </if>
	      <if test="judgeRule != null" >
	        judge_rule = #{judgeRule,jdbcType=INTEGER},
	      </if>
	      <if test="silentAllSwitch != null" >
	        silent_all_switch = #{silentAllSwitch,jdbcType=BIT},
	      </if>
	      <if test="silentAllFollowUpTime != null" >
	        silent_all_follow_up_time = #{silentAllFollowUpTime,jdbcType=INTEGER},
	      </if>
	      <if test="minReplyNum != null" >
	        min_reply_num = #{minReplyNum,jdbcType=INTEGER},
	      </if>
	      <if test="activeFollowUpSwitch != null" >
	        active_follow_up_switch = #{activeFollowUpSwitch,jdbcType=BIT},
	      </if>
	      <if test="silentUrgepaySwitch != null" >
	        silent_urgepay_switch = #{silentUrgepaySwitch,jdbcType=BIT},
	      </if>
	      <if test="silentUrgepayTime != null" >
	        silent_urgepay_time = #{silentUrgepayTime,jdbcType=INTEGER},
	      </if>
	      <if test="bannerSwitch != null" >
	        banner_switch = #{bannerSwitch,jdbcType=BIT},
	      </if>
	      <if test="bannerFlag != null" >
	        banner_flag = #{bannerFlag,jdbcType=BIGINT},
	      </if>
	      <if test="filterNonChat != null" >
	        filter_non_chat = #{filterNonChat,jdbcType=BIT},
	      </if>
	      <if test="teamChatFilter != null" >
	        team_chat_filter = #{teamChatFilter,jdbcType=BIT},
	      </if>
	      <if test="sysCsChatFilter != null" >
	        sys_cs_chat_filter = #{sysCsChatFilter,jdbcType=BIT},
	      </if>
	      <if test="csWatchwordSwitch != null" >
	        cs_watchword_switch = #{csWatchwordSwitch,jdbcType=BIT},
	      </if>
	      <if test="csWatchword != null" >
	        cs_watchword = #{csWatchword,jdbcType=VARCHAR},
	      </if>
	      <if test="csSingleChatFilter != null" >
	        cs_single_chat_filter = #{csSingleChatFilter,jdbcType=BIT},
	      </if>
	      <if test="singleChatNum != null" >
	        single_chat_num = #{singleChatNum,jdbcType=INTEGER},
	      </if>
	      <if test="buyerChatlogWordSwitch != null" >
	        buyer_chatlog_word_switch = #{buyerChatlogWordSwitch,jdbcType=BIT},
	      </if>
	      <if test="urgePayTime != null" >
	        urge_pay_time = #{urgePayTime,jdbcType=INTEGER},
	      </if>
	      <if test="urgeOrderWord != null" >
	        urge_order_word = #{urgeOrderWord,jdbcType=VARCHAR},
	      </if>
	      <if test="urgePayWord != null" >
	        urge_pay_word = #{urgePayWord,jdbcType=VARCHAR},
	      </if>
	      <if test="filterBuyerSigchat != null" >
	        filter_buyer_sigchat = #{filterBuyerSigchat,jdbcType=BIT},
	      </if>
	      <if test="filterEserviceFwd != null" >
	        filter_eservice_fwd = #{filterEserviceFwd,jdbcType=BIT},
	      </if>
	      <if test="sigchatMinReplyNum != null" >
	        sigchat_min_reply_num = #{sigchatMinReplyNum,jdbcType=INTEGER},
	      </if>
	      <if test="csFwdChatNum != null" >
	        cs_fwd_chat_num = #{csFwdChatNum,jdbcType=INTEGER},
	      </if>
    </set>
    	where id = #{id,jdbcType=BIGINT}
	</update>
	
	<select id="getShopSystemsettingById" parameterType="java.lang.Integer"
		resultMap="ShopSystemsettingDTO">
		SELECT
			<include refid="base_field" />
		FROM pes_shop_systemsetting
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</select>
	
	<select id="getShopSystemsettingByShop"  resultMap="ShopSystemsettingDTO">
		SELECT
			<include refid="base_field" />
		FROM pes_shop_systemsetting
		WHERE 
			shop_id = #{shop.shopId,jdbcType=BIGINT}
	</select>
	
	<select id="getShopSystemsetting"  resultMap="ShopSystemsettingDTO">
		SELECT
			 *
		FROM pes_shop_systemsetting
		WHERE 
			shop_id = #{shop.shopId,jdbcType=BIGINT}
	</select>
	
	
</mapper>