<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.UserPortraitAiReportMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.UserPortraitAiReport">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="report_date" property="reportDate" />
        <result column="report_type" property="reportType" />
        <result column="chart_type" property="chartType" />
        <result column="report_content" property="reportContent" />
        <result column="created_time" property="createdTime" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, shop_id, report_date, report_type, chart_type, report_content, created_time
    </sql>

    <!-- 插入用户画像AI报告数据 -->
    <insert id="insertUserPortraitAiReport" parameterType="com.pes.jd.model.DO.UserPortraitAiReport">
        INSERT INTO ${tableName} (
            shop_id, report_date, report_type, chart_type, report_content, created_time
        ) VALUES (
            #{report.shopId}, #{report.reportDate}, #{report.reportType}, 
            #{report.chartType}, #{report.reportContent}, #{report.createdTime}
        )
    </insert>

    <!-- 批量插入用户画像AI报告数据 -->
    <insert id="batchInsertUserPortraitAiReport" parameterType="java.util.List">
        INSERT INTO ${tableName} (
            shop_id, report_date, report_type, chart_type, report_content, created_time
        ) VALUES
        <foreach collection="reportList" item="report" separator=",">
            (#{report.shopId}, #{report.reportDate}, #{report.reportType}, 
             #{report.chartType}, #{report.reportContent}, #{report.createdTime})
        </foreach>
    </insert>

    <!-- 根据店铺ID和日期删除用户画像AI报告数据 -->
    <delete id="deleteUserPortraitAiReportByShopIdAndDate">
        DELETE FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND report_date = #{reportDate}
    </delete>

    <!-- 根据店铺ID和日期范围删除用户画像AI报告数据 -->
    <delete id="deleteUserPortraitAiReportByShopIdAndDateRange">
        DELETE FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND report_date BETWEEN #{startDate} AND #{endDate}
    </delete>

    <!-- 根据店铺ID删除所有用户画像AI报告数据 -->
    <delete id="deleteAllUserPortraitAiReportByShopId">
        DELETE FROM ${tableName}
        WHERE shop_id = #{shopId}
    </delete>

    <!-- 根据主键ID查询用户画像AI报告数据 -->
    <select id="selectUserPortraitAiReportById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${tableName}
        WHERE id = #{id}
    </select>

    <!-- 根据店铺ID和日期范围查询用户画像AI报告数据 -->
    <select id="selectUserPortraitAiReportByShopIdAndDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND report_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY report_date DESC, created_time DESC
    </select>

    <!-- 根据店铺ID、报告类型和图表类型查询用户画像AI报告数据 -->
    <select id="selectUserPortraitAiReportByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <if test="reportType != null">
            AND report_type = #{reportType}
        </if>
        <if test="chartType != null">
            AND chart_type = #{chartType}
        </if>
        <if test="startDate != null and endDate != null">
            AND report_date BETWEEN #{startDate} AND #{endDate}
        </if>
        ORDER BY report_date DESC, created_time DESC
    </select>

</mapper>
