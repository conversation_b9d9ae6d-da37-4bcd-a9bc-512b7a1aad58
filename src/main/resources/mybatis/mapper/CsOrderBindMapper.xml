<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsOrderBindMapper">

    <resultMap id="CsOrderBindInfoDTO" type="com.pes.jd.model.DTO.CsOrderBindInfoDTO">
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_created" jdbcType="TIMESTAMP" property="orderCreated"/>
        <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="ac_first_reply_date" jdbcType="TIMESTAMP" property="acFirstReplyDate"/>
        <result column="ac_first_chat_date" jdbcType="TIMESTAMP" property="acFirstChatDate"/>
        <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate"/>
        <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate"/>
        <result column="is_goods_filter" jdbcType="BIT" property="isGoodsFilter"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="is_presale" jdbcType="BIT" property="presale"/>
        <result column="order_valid_pay_time" jdbcType="TIMESTAMP" property="orderValidPayTime" />
    </resultMap>

    <resultMap id="CsOrderBindDTO" type="com.pes.jd.model.DTO.CsOrderBindDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_created" jdbcType="TIMESTAMP" property="orderCreated"/>
        <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
        <result column="type" jdbcType="BIT" property="type"/>
        <result column="is_pes_order" jdbcType="BIT" property="isPesOrder"/>
        <result column="silent_flag" jdbcType="BIT" property="silentFlag"/>
        <result column="is_presale" jdbcType="BIT" property="presale"/>
        <result column="to_tail_paid_cs" jdbcType="VARCHAR" property="toTailPaidCs"/>
        <result column="order_valid_payment" jdbcType="DOUBLE" property="orderValidPayment"/>
    </resultMap>

    <resultMap id="CsSaleOrderDTO" type="com.pes.jd.model.DTO.CsSaleOrderDTO">
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="order_valid_payment" jdbcType="DOUBLE" property="orderValidPayment"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
    </resultMap>

    <sql id="base_field">
    id, shop_id, date, trade_id, cs_nick, buyer_nick, type
  </sql>

    <insert id="batchInsertCsOrderBind">
        INSERT INTO ${tableName} (shop_id,`date`,cs_nick,buyer_nick,
        order_id,order_created,order_pay_date,order_payment,
        order_goods_num,order_post_fee,`type`,is_pes_order,
        silent_flag,order_filte_flag,is_presale,is_goods_filter,
        pay_type,is_balance_pay,order_valid_pay_time,order_valid_payment)
        VALUES
        <foreach collection="dailyOrderBindLst" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.csNick,jdbcType=VARCHAR},
            #{itm.buyerNick,jdbcType=VARCHAR},
            #{itm.orderId,jdbcType=BIGINT},
            #{itm.orderCreated,jdbcType=TIMESTAMP},
            #{itm.orderPayDate,jdbcType=TIMESTAMP},
            #{itm.orderPayment,jdbcType=DOUBLE},
            #{itm.orderGoodsNum,jdbcType=INTEGER},
            #{itm.orderPostFee,jdbcType=DOUBLE},
            #{itm.type,jdbcType=TINYINT},
            #{itm.isPesOrder,jdbcType=BIT},
            #{itm.silentFlag,jdbcType=INTEGER},
            #{itm.orderFilteFlag,jdbcType=INTEGER},
            #{itm.presale,jdbcType=BIT},
            #{itm.isGoodsFilter,jdbcType=BIT},
            #{itm.payType,jdbcType=INTEGER},
            #{itm.balancePay,jdbcType=BIT},
            #{itm.orderValidPayTime,jdbcType=TIMESTAMP},
            #{itm.orderValidPayment,jdbcType=DOUBLE}
            )
        </foreach>
    </insert>

    <delete id="deleteCsOrderBindById" parameterType="java.lang.Long">
    DELETE FROM  ${tableName}
    WHERE
    	id = #{id,jdbcType=BIGINT}
  </delete>


    <delete id="deleteShopCsOrderBindByDate" parameterType="map">
    DELETE FROM  ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date}
  </delete>

    <delete id="deleteCsOrderBindByShopAndDate" parameterType="map">
    DELETE FROM  ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>


    <update id="batchUpdateCsOrderBindOrderValidPayInfo" parameterType="map">
        <foreach collection="orderLst" item="itm" separator=";">
            UPDATE ${tableName}
            SET
            order_valid_pay_time = #{itm.balancePayTime,jdbcType=TIMESTAMP},
            order_valid_payment = #{itm.payment,jdbcType=DOUBLE}
            WHERE
            order_id = #{itm.orderId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchUpdateCsOrderBindOrderValidPayInfoForBargain" parameterType="map">
        <foreach collection="orderLst" item="itm" separator=";">
            UPDATE ${tableName}
            SET
            order_valid_pay_time = #{itm.balancePayTime,jdbcType=TIMESTAMP},
            order_valid_payment = #{itm.payment,jdbcType=DOUBLE}
            WHERE
            order_id = #{itm.orderId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateCsOrderBindBySelective" parameterType="com.pes.jd.model.DO.CsOrderBindDO">
        UPDATE ${tableName}
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="tradeId != null">
                trade_id = #{tradeId,jdbcType=BIGINT},
            </if>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="buyerNick != null">
                buyer_nick = #{buyerNick,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

<!--    <update id="updateCsOrderBindPesOrderById" parameterType="map">-->
<!--         UPDATE ${tableName}-->
<!--         set is_pes_order=#{pesOrder}-->
<!--         WHERE id IN-->
<!--         <foreach collection="ids" open="(" close=")" separator="," item="id">-->
<!--             #{id}-->
<!--         </foreach>-->
<!--    </update>-->

    <select id="selectShopCsSaleOrderLst" resultMap="CsSaleOrderDTO">
    SELECT
    	order_id,buyer_nick,order_goods_num,order_valid_payment,order_post_fee
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND order_valid_pay_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
 	AND is_pes_order = 1
 	AND pay_type != 1
  </select>

    <select id="selectShopCsPresaleBalancePaySaleOrderLst" resultMap="CsSaleOrderDTO">
    SELECT
    	order_id,buyer_nick,order_goods_num,order_payment,order_post_fee
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND order_pay_date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
 	AND is_pes_order = 1
 	AND is_presale = 1
  </select>

    <select id="selectShopGoodsToPayCsSaleOrderLst" resultMap="CsSaleOrderDTO">
    SELECT
    	order_id,buyer_nick,order_goods_num,order_valid_payment,order_post_fee
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
 	AND is_pes_order = 1
 	AND pay_type = 1
  </select>

    <!--    <select id="getCsTeamPaidSaleIndex"  resultType="com.pes.jd.model.DTO.CsSaleOrderDTO" >
        SELECT
            count(distinct(order_id)) as saleOrderNum,
            count(distinct(buyer_nick)) as SaleBuyerNum,
            sum(order_goods_num) as saleGoodsNum,
            sum(order_payment) as saleAmount,
            sum(order_post_fee) as postFee
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
        AND cs_nick = #{csNick,jdbcType=VARCHAR}
        AND order_pay_date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
         AND is_pes_order = 1

      </select> -->

    <select id="selectShopCsToOrderBindBuyerNickLstForEnquiry" resultType="string">
    SELECT buyer_nick
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND	cs_nick = #{csNick,jdbcType=VARCHAR}
    AND date = #{date,jdbcType=DATE}
    AND type = 1
  </select>

    <select id="selectShopCurrentDayCsOrderBindLstForPesCal" resultMap="CsOrderBindInfoDTO">
    SELECT buyer_nick,order_payment,order_created,order_pay_date,order_goods_num,is_goods_filter
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND	cs_nick = #{csNick,jdbcType=VARCHAR}
    AND date = #{date,jdbcType=DATE}
    AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    AND type = 1
  </select>

    <select id="selectShopCsOrderBindLstForPesCal" resultMap="CsOrderBindInfoDTO">
    SELECT buyer_nick,order_id,order_payment,order_created,order_pay_date,order_goods_num,type,is_goods_filter,pay_type
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND date = #{date,jdbcType=DATE}
    AND order_created BETWEEN #{enquiryValidStartDate,jdbcType=TIMESTAMP} AND #{enquiryValidEndDate,jdbcType=TIMESTAMP}
    AND type = 1
  </select>

    <select id="selectShopCsOrderBindByOrderPayDateAndType" resultMap="CsOrderBindInfoDTO">
    SELECT buyer_nick,order_id,order_payment,order_created,order_pay_date,order_goods_num,type,is_goods_filter,pay_type
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND date = #{date,jdbcType=DATE}
    AND order_pay_date BETWEEN #{enquiryValidStartDate,jdbcType=TIMESTAMP} AND #{enquiryValidEndDate,jdbcType=TIMESTAMP}
    AND type in (${type})
  </select>


    <select id="selectShopCsOrderBindLstForEnquiryLoss" resultType="string">
        SELECT DISTINCT buyer_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND cs_nick IN
        <foreach collection="csNicks" item="csNick" separator="," open="(" close=")">
            #{csNick}
        </foreach>
        AND `date` = #{date,jdbcType=DATE}
        AND order_created BETWEEN #{enquiryValidStartDate,jdbcType=TIMESTAMP} AND
        #{enquiryValidEndDate,jdbcType=TIMESTAMP}
        AND `type` IN(1, 2)
    </select>


    <select id="selectShopBindOrderIdsByOrderIdSet" resultType="long">
        SELECT order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND order_id IN
        <foreach collection="orderIdSet" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND is_pes_order = 1
    </select>

    <select id="selectShopCsOrderBindBuyerNickLstForPesCal" resultType="string">
    SELECT buyer_nick
    FROM ${tableName}
    WHERE
   	 	shop_id = #{shopId,jdbcType=BIGINT}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND date = #{date,jdbcType=DATE}
    AND (type = 1)
  </select>

    <select id="selectCurrentDayToOrderedOrderLstForOrder" resultMap="CsOrderBindInfoDTO">
    SELECT buyer_nick,order_payment,order_created,order_pay_date,order_goods_num,pay_type,order_id
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND	cs_nick = #{csNick,jdbcType=VARCHAR}
    AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    AND type = 1
  </select>

    <select id="selectToOrderedAndPaidOrderLstForOrder" resultMap="CsOrderBindInfoDTO">
    SELECT buyer_nick,order_id,order_payment,order_created,order_pay_date,order_goods_num
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND	cs_nick = #{csNick,jdbcType=VARCHAR}
    AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    AND order_pay_date is not null
    AND type = 1
    AND pay_type != 1
  </select>

    <select id="selectGoodsToPayToOrderedAndPaidOrderLstForOrder" resultMap="CsOrderBindInfoDTO">
    SELECT buyer_nick,order_id,order_payment,order_created,order_pay_date,order_goods_num
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND	cs_nick = #{csNick,jdbcType=VARCHAR}
    AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    AND type = 1
    AND pay_type = 1
  </select>

    <select id="selectShopCsOrderBindOrderIdLstByDateRange" resultType="long">
        SELECT DISTINCT order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND cs_nick IN
        <foreach collection="csLst" item="cs" open="(" close=")" separator=",">
            #{cs.nick}
        </foreach>
        AND date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE}
        AND order_pay_date is not null
        AND (type = 1)
    </select>

    <select id="selectShopCsOrderBindOrderLstByDateRange" resultMap="CsOrderBindInfoDTO">
        SELECT order_id,cs_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND cs_nick IN
        <foreach collection="csLst" item="cs" open="(" close=")" separator=",">
            #{cs.nick}
        </foreach>
        AND date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE}
        AND order_pay_date is not null
        AND (type = 1)
    </select>

    <select id="selectShopCsOrderBindOrderIdLstForPesCs" resultMap="CsOrderBindDTO">
  	SELECT distinct date,order_id,cs_nick,buyer_nick
  	FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
  	AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    AND is_pes_order = #{pesOrderFlag}

  </select>
    <!--  货到付款 -->
    <select id="selectShopCsOrderBindOrderIdLstForCashPesCs" resultMap="CsOrderBindDTO">
  	SELECT distinct date,order_id,cs_nick,buyer_nick
  	FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
  	AND cs_nick = #{csNick,jdbcType=VARCHAR}
  	AND
  	pay_type = 1
  	AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
    AND is_pes_order = #{pesOrderFlag}
  </select>
    <!-- 普通订单（在线支付）和预售订单 -->
    <select id="selectShopCsOrderBindOrderIdLstByValidPayTimeForPesCs" resultMap="CsOrderBindDTO">
  	SELECT  date,order_id,cs_nick,buyer_nick, order_payment
  	FROM ${tableName}
    WHERE

    	shop_id = #{shopId,jdbcType=BIGINT}
  	AND cs_nick = #{csNick,jdbcType=VARCHAR}
  	AND is_pes_order = #{pesOrderFlag}
  	AND order_valid_pay_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
   	AND pay_type != 1

  </select>

    <select id="selectShopCsOrderBindByOrderCreatedDateForLossRecord" parameterType="map"
            resultMap="CsOrderBindInfoDTO">
        SELECT
        shop_id,date,cs_nick,buyer_nick,order_id,order_payment,order_created,order_pay_date,order_goods_num,is_presale
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND cs_nick IN
        <foreach collection="csLst" item="cs" open="(" close=")" separator=",">
            #{cs.nick}
        </foreach>
        AND date BETWEEN #{finalEnquiryStartDate} AND #{endDate}
        AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND order_pay_date IS NULL
        AND type = 1
        AND pay_type != 1
    </select>

    <select id="selectShopCsOrderBindCsNickByOrderIdLst" parameterType="map"
            resultType="com.pes.jd.model.DO.ShopGoodsReviewDO">
        SELECT cs_nick csNick,order_id orderId
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderIdLst" item="orderId" open="(" close=")"
                 separator=",">
            #{orderId}
        </foreach>
        AND is_pes_order = 1
    </select>

    <select id="searchShopDate" resultMap="CsOrderBindDTO">
    SELECT
    	id, shop_id, date, cs_nick, buyer_nick, order_id, order_created, order_pay_date,
    	order_payment, order_goods_num, order_post_fee, type, is_pes_order, silent_flag,
    	is_presale, to_tail_paid_cs, order_valid_payment
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}

    </select>

    <select id="selectPresaleBindByOrderIdsForPresalePerformance" resultType="com.pes.jd.model.DO.CsOrderBindDO">
        SELECT cs_nick,order_id,buyer_nick,order_payment
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND `type` = #{type}
    </select>

    <select id="selectCsOrderBinds" resultMap="CsOrderBindDTO">
        SELECT order_id, order_pay_date, order_payment
        FROM ${tableName}
        WHERE shop_id = #{shopId} AND order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND is_pes_order = 1
    </select>
    <select id="selectOrderBindPesOrderByShopIdAndDate" resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        SELECT id,`date`,order_id,type,cs_nick,buyer_nick,is_presale as presale,order_valid_pay_time
        FROM ${tableName} WHERE shop_id=#{shopId} AND date BETWEEN #{startDate} AND #{endDate} AND is_pes_order = 1
    </select>

    <delete id="deleteCsOrderBindPesOrderById">
        delete from ${tableName}
        WHERE id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </delete>

    <select id="selectShopCsOrderBindLst" resultType="com.pes.jd.model.DO.CsOrderBindDO">
    select  shop_id,`date`,cs_nick,buyer_nick,
        order_id,order_created,order_pay_date,order_payment,
        order_goods_num,order_post_fee,`type`,is_pes_order,
        silent_flag,order_filte_flag,is_presale,is_goods_filter,
        pay_type,is_balance_pay balance_pay,order_valid_pay_time,order_valid_payment
       FROM  ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date}
    </select>

    <select id="selectShopBindOrderIdsByOrderIdSetAndDate" resultType="java.lang.Long">
        select order_id
        from ${tableName}
        where shop_id=#{shopId}
        and order_id in
        (
        <foreach collection="orderIds" item="orderId" separator=",">
            #{orderId}
        </foreach>
        )
    </select>

    <insert id="batchInsertCsOrderBind2">
        INSERT INTO ${tableName} (shop_id,`date`,cs_nick,buyer_nick,
        order_id,order_created,order_pay_date,order_payment,
        order_goods_num,order_post_fee,`type`,is_pes_order,
        silent_flag,order_filte_flag,is_presale,is_goods_filter,
        pay_type,is_balance_pay,order_valid_pay_time,order_valid_payment)
        VALUES
        <foreach collection="dailyOrderBindLst" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.csNick,jdbcType=VARCHAR},
            #{itm.buyerNick,jdbcType=VARCHAR},
            #{itm.orderId,jdbcType=BIGINT},
            #{itm.orderCreated,jdbcType=TIMESTAMP},
            #{itm.orderPayDate,jdbcType=TIMESTAMP},
            #{itm.orderPayment,jdbcType=DOUBLE},
            #{itm.orderGoodsNum,jdbcType=INTEGER},
            #{itm.orderPostFee,jdbcType=DOUBLE},
            #{itm.type,jdbcType=TINYINT},
            #{itm.isPesOrder,jdbcType=BIT},
            #{itm.silentFlag,jdbcType=INTEGER},
            #{itm.orderFilteFlag,jdbcType=INTEGER},
            #{itm.presale,jdbcType=BIT},
            #{itm.isGoodsFilter,jdbcType=BIT},
            #{itm.payType,jdbcType=INTEGER},
            #{itm.balancePay,jdbcType=BIT},
            #{itm.orderValidPayTime,jdbcType=TIMESTAMP},
            #{itm.orderValidPayment,jdbcType=DOUBLE}
            )
        </foreach>
    </insert>

    <select id="selectBindOrderByOrderIdAndType" resultMap="CsOrderBindInfoDTO">
        select id,order_id,date
        from ${tableName}
        where shop_id=#{shopId}
        and order_id in
        (
        <foreach collection="tOrderLst" item="orderId" separator=",">
            #{orderId}
        </foreach>
        )
        and type=#{type}
        and date between #{startDate} and #{endDate}
    </select>

    <select id="selectIsPesTradeCanceledOrder" resultType="java.lang.Long">
        select order_id
        from ${tableName}
        where shop_id=#{shopId}
        and order_id in
        (
        <foreach collection="orderIds" item="orderId" separator=",">
            #{orderId}
        </foreach>
        )
        and cs_nick =#{csNick}
        and is_pes_order=1
    </select>

    <select id="selectDistinctBuyerOfBargainPayNoInEnquiryAndAfterSale" resultType="java.lang.String">
        SELECT DISTINCT buyer_nick
        FROM ${tableName}
        WHERE shop_id = #{shopId}
          AND order_created BETWEEN #{startDate} AND
                #{endDate}
          AND `type` IN (1, 2)
          AND is_presale = 1
          AND is_balance_pay = 0
<!--            '2021-07-11 00:00:00.0', '2021-07-07 00:00:00.0'  业务上算五天 datediff()算出来是四天所以要加一天-->
          AND datediff(#{date}, date)+1 &lt; #{sellAfter}
          AND order_pay_date!=order_valid_pay_time
    </select>

    <select id="selectBargainPaymentPresaleBindByOrderPayDate" resultMap="CsOrderBindInfoDTO">
        SELECT
        shop_id,
        cs_nick,
        buyer_nick,
        order_id,
        order_created,
        order_pay_date,
        order_valid_pay_time
        FROM ${tableName}
        WHERE shop_id=#{shopId} and buyer_nick
        in
        <foreach collection="csNickLst" item="csNick" separator="," open="(" close=")">
            #{csNick}
        </foreach>
        and (order_pay_date between #{startDate} and #{endDate}
        or order_valid_pay_time between #{startDate} and #{endDate})
        and is_presale=1 GROUP BY order_created;
    </select>

    <select id="selectPaymentBindToday" resultMap="CsOrderBindInfoDTO">
        SELECT
        shop_id,
        cs_nick,
        buyer_nick,
        order_id,
        order_created,
        order_pay_date,
        order_valid_pay_time
        FROM ${tableName}
        WHERE shop_id=#{shopId} and date=#{date} and buyer_nick
        in
        <foreach collection="buyerLst" item="buyer" separator="," open="(" close=")">
            #{buyer}
        </foreach>
    </select>

    <select id="selectBalancePayOrderBindByDateAndTypeAndPesOrderAndOrderIds" resultMap="CsOrderBindDTO">
        select
        id,
        shop_id,
        date,
        cs_nick,
        buyer_nick,
        order_id,
        order_created,
        order_pay_date,
        order_payment,
        order_goods_num,
        order_post_fee,
        type,
        is_pes_order,
        silent_flag,
        is_presale,
        to_tail_paid_cs
        from ${tableName}
        where shop_id = #{shopId}
        and
        date between #{startDate}
        and #{endDate}
        and type =#{type}
        and is_pes_order=#{isPesOrder}
        <if test="orderIds != null and orderIds.size() != 0">
            and order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateCsOrderBindOrderPesOrder">
        update
        ${tableName}
        set is_pes_order=#{pesOrder}
        WHERE id IN
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <select id="selectDistinctBuyerOfBindPayBalance" resultType="java.lang.String">
        SELECT DISTINCT buyer_nick
        FROM ${tableName}
        WHERE shop_id = #{shopId}
          AND `date` = #{date}
          AND `type` = 4
    </select>
    <select id="selectShopCsOrderBindByOrderPayDate" resultMap="CsOrderBindDTO">
        SELECT cs_nick,buyer_nick,order_id,order_payment,order_created,order_pay_date,order_goods_num,type,is_goods_filter,pay_type
        FROM ${tableName}
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
          AND order_pay_date BETWEEN #{enquiryValidStartDate,jdbcType=TIMESTAMP} AND #{enquiryValidEndDate,jdbcType=TIMESTAMP}
          AND type = 2
    </select>
</mapper>