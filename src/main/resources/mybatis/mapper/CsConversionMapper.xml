<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsConversionMapper" >

  <resultMap id="CsConversionDTO" type="com.pes.jd.model.DTO.CsConversionDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="trade_id" property="tradeId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="allocated_cs_nick" property="allocatedCsNick" jdbcType="VARCHAR" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="BIT" />
  </resultMap>
  
  <sql id="base_field" >
    id, trade_id, shop_id, cs_nick, buyer_nick, allocated_cs_nick, created, modified, 
    type, status
  </sql>
  
  <insert id="insertCsConversion" parameterType="com.pes.jd.model.DO.CsConversion" >
    INSERT INTO pes_cs_conversion (id, trade_id, shop_id, 
      cs_nick, buyer_nick, allocated_cs_nick, 
      created, modified, type, 
      status)
    VALUES (#{id,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, 
      #{csNick,jdbcType=VARCHAR}, #{buyerNick,jdbcType=VARCHAR}, #{allocatedCsNick,jdbcType=VARCHAR}, 
      #{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}, #{type,jdbcType=VARCHAR}, 
      #{status,jdbcType=BIT})
  </insert>
  
  <delete id="deleteCsConversionById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_conversion
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateCsConversionById" parameterType="com.pes.jd.model.DO.CsConversion" >
    UPDATE pes_cs_conversion
    <set >
      <if test="tradeId != null" >
        trade_id = #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="buyerNick != null" >
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="allocatedCsNick != null" >
        allocated_cs_nick = #{allocatedCsNick,jdbcType=VARCHAR},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=BIT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>