<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsWordnumMapper" >
<!-- 
  <resultMap id="CsWordnumDO" type="com.pes.jd.model.DO.CsWordnumDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="cs_word_num" property="csWordNum" jdbcType="INTEGER" />
    <result column="cs_chat_num" property="csChatNum" jdbcType="INTEGER" />
    <result column="buyer_chat_num" property="buyerChatNum" jdbcType="INTEGER" />
    <result column="qa_rate" property="qaRate" jdbcType="DOUBLE" />
  </resultMap> -->
  
  <sql id="base_field" >
    id, shop_id, date, cs_nick, cs_word_num, cs_chat_num, buyer_chat_num, qa_rate
  </sql>
 
  <insert id="insertCsWordnum" parameterType="com.pes.jd.model.DO.CsWordnumDO" >
    INSERT INTO ${tableName} (shop_id, date, cs_nick, cs_word_num, cs_chat_num,  buyer_chat_num, qa_rate)
    VALUES 
    (	  
    	  #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
	      #{csNick,jdbcType=VARCHAR}, #{csWordNum,jdbcType=INTEGER}, #{csChatNum,jdbcType=INTEGER}, 
	      #{buyerChatNum,jdbcType=INTEGER}, #{qaRate,jdbcType=DOUBLE}
	      
	  )
  </insert>
  
  <insert id="batchInsertCsWordnum" >
    INSERT INTO ${tableName} (shop_id, date, cs_nick, cs_word_num, cs_chat_num,  buyer_chat_num, qa_rate)
    VALUES 
    <foreach collection="csWordnumLst" item="itm" separator=",">
    (	  
    	  #{itm.shopId,jdbcType=BIGINT}, 
    	  #{itm.date,jdbcType=DATE}, 
	      #{itm.csNick,jdbcType=VARCHAR}, 
	      #{itm.csWordNum,jdbcType=INTEGER}, 
	      #{itm.csChatNum,jdbcType=INTEGER}, 
	      #{itm.buyerChatNum,jdbcType=INTEGER}, 
	      #{itm.qaRate,jdbcType=DOUBLE}
	 )
    </foreach>
  </insert>
  
  
  <delete id="deleteCsWordnumById" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteShopCsWordnumByDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
  
  <update id="updateCsWordnumBySelective" parameterType="com.pes.jd.model.DO.CsWordnumDO" >
    UPDATE ${tableName}
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="csWordNum != null" >
        cs_word_num = #{csWordNum,jdbcType=INTEGER},
      </if>
      <if test="csChatNum != null" >
        cs_chat_num = #{csChatNum,jdbcType=INTEGER},
      </if>
      <if test="buyerChatNum != null" >
        buyer_chat_num = #{buyerChatNum,jdbcType=INTEGER},
      </if>
      <if test="qaRate != null" >
        qa_rate = #{qaRate,jdbcType=DOUBLE},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>