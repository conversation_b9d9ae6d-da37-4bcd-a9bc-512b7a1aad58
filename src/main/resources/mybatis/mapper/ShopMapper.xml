<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopMapper">

	<resultMap id="ShopDO" type="com.pes.jd.model.DO.Shop">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="subuser_num" jdbcType="INTEGER" property="subuserNum" />
		<result column="realtime_switch" jdbcType="BIT" property="realtimeSwitch" />
		<result column="fetch_flag" jdbcType="INTEGER" property="fetchFlag" />
		<result column="init_data_flag" jdbcType="INTEGER" property="initDataFlag" />
		<result column="previous_get_data_time" jdbcType="TIMESTAMP" property="previousGetDataTime" />
		<result column="pre_fetch_realtime" jdbcType="TIMESTAMP" property="preFetchRealtime" />
		<result column="last_consumed_time" jdbcType="BIGINT" property="lastConsumedTime" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="vender_id" jdbcType="BIGINT" property="venderId" />
	</resultMap>
	
	<resultMap id="JobShopDTO" type="com.pes.jd.model.DTO.JobShopDTO">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="seller_nick" jdbcType="VARCHAR" property="sellerNick" />
		<result column="title" jdbcType="VARCHAR" property="title" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="subuser_num" jdbcType="INTEGER" property="subuserNum" />
		<result column="realtime_switch" jdbcType="BIT" property="realtimeSwitch" />
		<result column="fetch_flag" jdbcType="INTEGER" property="fetchFlag" />
		<result column="init_data_flag" jdbcType="INTEGER" property="initDataFlag" />
		<result column="previous_get_data_time" jdbcType="TIMESTAMP" property="previousGetDataTime" />
		<result column="pre_fetch_realtime" jdbcType="TIMESTAMP" property="preFetchRealtime" />
		<result column="last_consumed_time" jdbcType="BIGINT" property="lastConsumedTime" />
		<result column="schema_id" jdbcType="VARCHAR" property="schemaId" />
		<result column="db" jdbcType="VARCHAR" property="db" />
		<result column="col_type" jdbcType="VARCHAR" property="colType" />
		<result column="vender_id" jdbcType="BIGINT" property="venderId" />
	</resultMap>

	<sql id="base_field">
		shop_id, user_id, seller_nick, title, session_key, status, subuser_num,
		realtime_switch,
		fetch_flag, init_data_flag, previous_get_data_time, pre_fetch_realtime,
		last_consumed_time,
		schema_id, db
	</sql>

	<insert id="insertShop" parameterType="com.pes.jd.model.DO.Shop">
		INSERT INTO pes_shop (shop_id, user_id, seller_nick,
		title, session_key, status,
		subuser_num, realtime_switch, fetch_flag,
		init_data_flag, previous_get_data_time, pre_fetch_realtime,
		last_consumed_time, schema_id, db
		)
		VALUES 
		(
			#{shopId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},
			#{sellerNick,jdbcType=VARCHAR},#{title,jdbcType=VARCHAR}, #{sessionKey,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
			#{subuserNum,jdbcType=INTEGER}, #{realtimeSwitch,jdbcType=BIT},#{fetchFlag,jdbcType=INTEGER},
			#{initDataFlag,jdbcType=INTEGER}, #{previousGetDataTime,jdbcType=TIMESTAMP},
			#{preFetchRealtime,jdbcType=TIMESTAMP},	#{lastConsumedTime,jdbcType=BIGINT}, #{schema_id,jdbcType=VARCHAR},#{db,jdbcType=VARCHAR}
		)
	</insert>

	<delete id="deleteShopById" parameterType="java.lang.Long">
		DELETE FROM pes_shop
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</delete>
	
	<update id="updateShopById" parameterType="com.pes.jd.model.DO.Shop">
		update pes_shop
		<set>
			<if test="userId != null">
				user_id = #{userId,jdbcType=BIGINT},
			</if>
			<if test="sellerNick != null">
				seller_nick = #{sellerNick,jdbcType=VARCHAR},
			</if>
			<if test="title != null">
				title = #{title,jdbcType=VARCHAR},
			</if>
			<if test="sessionKey != null">
				session_key = #{sessionKey,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="subuserNum != null">
				subuser_num = #{subuserNum,jdbcType=INTEGER},
			</if>
			<if test="realtimeSwitch != null">
				realtime_switch = #{realtimeSwitch,jdbcType=BIT},
			</if>
			<if test="fetchFlag != null">
				fetch_flag = #{fetchFlag,jdbcType=INTEGER},
			</if>
			<if test="initDataFlag != null">
				init_data_flag = #{initDataFlag,jdbcType=INTEGER},
			</if>
			<if test="previousGetDataTime != null">
				previous_get_data_time = #{previousGetDataTime,jdbcType=TIMESTAMP},
			</if>
			<if test="preFetchRealtime != null">
				pre_fetch_realtime = #{preFetchRealtime,jdbcType=TIMESTAMP},
			</if>
			<if test="lastConsumedTime != null">
				last_consumed_time = #{lastConsumedTime,jdbcType=BIGINT},
			</if>
			<if test="schema_id != null">
				schema_id = #{schema_id,jdbcType=VARCHAR},
			</if>
			<if test="db != null">
				db = #{db,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</update>

	<select id="selectShopByShopId" parameterType="java.lang.Long" resultMap="ShopDO">
		SELECT
		<include refid="base_field" />
		FROM pes_shop
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</select>
	
	<select id="getJobShopInfoById" parameterType="java.lang.Long" resultMap="JobShopDTO">
		SELECT
			shop_id, user_id, seller_nick, title, session_key, status, subuser_num,realtime_switch,
		fetch_flag, init_data_flag, previous_get_data_time, pre_fetch_realtime,last_consumed_time, schema_id, db,
        col_type, vender_id
		FROM pes_shop
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</select>
	
	<select id="getJobShopInfoByVenderId" parameterType="java.lang.Long" resultMap="JobShopDTO">
		SELECT
			shop_id, user_id, seller_nick, title, session_key, status, subuser_num,realtime_switch,
		fetch_flag, init_data_flag, previous_get_data_time, pre_fetch_realtime,last_consumed_time, schema_id, db,
        col_type, vender_id
		FROM pes_shop
		WHERE
			vender_id = #{venderId,jdbcType=BIGINT}
	</select>
	
	<select id="getActiveJobShopInfoByDelayTime" parameterType="java.lang.Integer" resultMap="JobShopDTO">
		SELECT
			s.shop_id, s.user_id, s.seller_nick, s.title, s.session_key, s.status, s.subuser_num,s.realtime_switch,
		s.fetch_flag, s.init_data_flag, s.previous_get_data_time, s.pre_fetch_realtime,s.last_consumed_time, s.schema_id, s.db,
        s.col_type, s.vender_id
		FROM pes_shop s
		RIGHT JOIN
			(SELECT shop_id
			FROM pes_shop_systemsetting 
			WHERE scheduling_time_dot = #{hours}) sys
		ON s.shop_id = sys.shop_id
		WHERE s.status = "active"
	</select>
	
	<select id="getAllActiveJobShopInfo" resultMap="JobShopDTO">
		SELECT
			shop_id, user_id, seller_nick, title, session_key, status, subuser_num,realtime_switch,
		fetch_flag, init_data_flag, previous_get_data_time, pre_fetch_realtime,last_consumed_time, schema_id, db,
        col_type, vender_id
		FROM pes_shop
		WHERE status = "active"
	</select>

</mapper>