<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.CsCustRecommendConsultSkuMapper">
  <resultMap id="CsCustRecommendConsultSkuDO" type="com.pes.jd.model.DO.CsCustRecommendConsultSkuDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>
  <sql id="base_filed">
    id, shop_id, date, cs_nick, buyer_nick, sku_id, type
  </sql>
  <select id="selectCsCustRecommendConsultSku" parameterType="java.lang.Long" resultMap="CsCustRecommendConsultSkuDO">
    select 
    <include refid="base_filed" />
    from pes_cs_cust_recommend_consult_sku
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteCsCustRecommendConsultSku" parameterType="java.lang.Long">
    delete from pes_cs_cust_recommend_consult_sku
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertCsCustRecommendConsultSku" parameterType="com.pes.jd.model.DO.CsCustRecommendConsultSkuDO">
    insert into pes_cs_cust_recommend_consult_sku (id, shop_id, date, 
      cs_nick, buyer_nick, sku_id, 
      type)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{buyerNick,jdbcType=VARCHAR}, #{skuId,jdbcType=BIGINT}, 
      #{type,jdbcType=TINYINT})
  </insert>


  <insert id="batchInsertCsCustRecommendConsultSku" parameterType="map">
    INSERT INTO ${tableName}
    (
        shop_id,
        date,
        cs_nick,
        buyer_nick,
        sku_id,
        type
    )
    VALUES
    <foreach collection="skuLst" item="item" separator=",">
      (#{item.shopId},
      #{item.date},
      #{item.csNick},
      #{item.buyerNick},
      #{item.skuId},
      #{item.type}

      )

    </foreach>
  </insert>
  <update id="updateCsCustRecommendConsultSku" parameterType="com.pes.jd.model.DO.CsCustRecommendConsultSkuDO">
    update pes_cs_cust_recommend_consult_sku
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="buyerNick != null">
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <delete id="deleteCsCustRecommendConsultSkuByShopId">
        DELETE FROM ${tableName}
        WHERE
        shop_id=#{shopId} AND date=#{date}

  </delete>

  <select id="selectLstByBuyerNick" resultType="com.pes.jd.model.DTO.CsCustRecommendConsultSkuDTO">
    SELECT
      id id,
      shop_id shopId,
      date date,
      cs_nick csNick,
      buyer_nick buyerNick,
      sku_id skuId,
      type type
        FROM ${tableName}
    WHERE
        shop_id=#{shopId}
        AND date=#{date}
        AND type=2
        AND buyer_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")"
                         separator=",">
                  #{csNick}
                </foreach>
  </select>

  <select id="selectCsCustRecommendConsultSkuByShopIdAndDate" resultMap="CsCustRecommendConsultSkuDO">
      SELECT
      <include refid="base_filed"/>
      FROM ${tableName}
      WHERE
      shop_id=#{shopId}
      AND date=#{date}
  </select>
  <select id="selectByEnquiryDaysAndType" resultMap="CsCustRecommendConsultSkuDO">
    SELECT
    <include refid="base_filed"/>
    FROM ${tableName}
    WHERE
    shop_id = #{shopId}
    AND date between #{startDate} and #{endDate}
    AND type = #{type,jdbcType=INTEGER}
  </select>
</mapper>