<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.CsServiceIndexMapper">

  <resultMap id="CsServiceIndexDTO" type="com.pes.jd.model.DTO.CsServiceIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="avg_wait_time_first" jdbcType="DOUBLE" property="avgWaitTimeFirst" />
    <result column="avg_wait_time" jdbcType="DOUBLE" property="avgWaitTime" />
    <result column="session_time" jdbcType="BIGINT" property="sessionTime" />
    <result column="non_reply_num" jdbcType="INTEGER" property="nonReplyNum" />
    <result column="cs_word_num" jdbcType="INTEGER" property="csWordNum" />
    <result column="cs_reply_num" jdbcType="INTEGER" property="csReplyNum" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
    <result column="qa_rate" jdbcType="DOUBLE" property="qaRate" />
  </resultMap>
  
  <sql id="base_field">
     shop_id, date, cs_nick, avg_wait_time_first, avg_wait_time, session_time, non_reply_num, 
    cs_word_num, cs_reply_num, buyer_chat_num, qa_rate
  </sql>
  
  <insert id="batchInsertCsServiceIndex" parameterType="map">
    INSERT INTO ${tableName} (shop_id, date, 
      cs_nick, avg_wait_time_first, avg_wait_time, 
      session_time, non_reply_num, cs_word_num, 
      cs_reply_num, buyer_chat_num, qa_rate)
    VALUES
    <foreach collection="csServiceIndexLst" item="itm" separator=",">
    (
    	#{itm.shopId,jdbcType=BIGINT}, 
    	#{itm.date,jdbcType=DATE}, 
      	#{itm.csNick,jdbcType=VARCHAR}, 
      	#{itm.avgWaitTimeFirst,jdbcType=DOUBLE}, 
      	#{itm.avgWaitTime,jdbcType=DOUBLE}, 
      	#{itm.sessionTime,jdbcType=BIGINT}, 
      	#{itm.nonReplyNum,jdbcType=INTEGER}, 
      	#{itm.csWordNum,jdbcType=INTEGER}, 
      	#{itm.csReplyNum,jdbcType=INTEGER}, 
      	#{itm.buyerChatNum,jdbcType=INTEGER}, 
      	#{itm.qaRate,jdbcType=DOUBLE}
     )
    </foreach>

  </insert>
    
  <delete id="deleteShopCsServiceIndexByDate" parameterType="map">
    DELETE FROM 
    	${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>
  
  <update id="updateCsServiceIndexById" parameterType="com.pes.jd.model.DO.CsServiceIndexDO">
    UPDATE ${tableName}
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="avgWaitTimeFirst != null">
        avg_wait_time_first = #{avgWaitTimeFirst,jdbcType=DOUBLE},
      </if>
      <if test="avgWaitTime != null">
        avg_wait_time = #{avgWaitTime,jdbcType=DOUBLE},
      </if>
      <if test="sessionTime != null">
        session_time = #{sessionTime,jdbcType=BIGINT},
      </if>
      <if test="nonReplyNum != null">
        non_reply_num = #{nonReplyNum,jdbcType=INTEGER},
      </if>
      <if test="csWordNum != null">
        cs_word_num = #{csWordNum,jdbcType=INTEGER},
      </if>
      <if test="csReplyNum != null">
        cs_reply_num = #{csReplyNum,jdbcType=INTEGER},
      </if>
      <if test="buyerChatNum != null">
        buyer_chat_num = #{buyerChatNum,jdbcType=INTEGER},
      </if>
      <if test="qaRate != null">
        qa_rate = #{qaRate,jdbcType=DOUBLE},
      </if>
    </set>
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </update>
  
</mapper>