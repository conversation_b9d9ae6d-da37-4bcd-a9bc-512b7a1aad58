<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.OrderConsignMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderConsignDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="full_name" jdbcType="VARCHAR" property="fullName" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="full_address" jdbcType="VARCHAR" property="fullAddress" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="town" jdbcType="VARCHAR" property="town" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="county_id" jdbcType="VARCHAR" property="countyId" />
    <result column="town_id" jdbcType="VARCHAR" property="townId" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, full_name, telephone, mobile, full_address, province, city, county, 
    town, province_id, city_id, county_id, town_id, email, post_code, created_time, modified_time
  </sql>


<insert id="insertOrderConsign" parameterType="map">
    insert into ${tableName}
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderConsign.orderId != null">
        order_id,
      </if>
      <if test="orderConsign.fullName != null">
        full_name,
      </if>
      <if test="orderConsign.telephone != null">
        telephone,
      </if>
      <if test="orderConsign.mobile != null">
        mobile,
      </if>
      <if test="orderConsign.fullAddress != null">
        full_address,
      </if>
      <if test="orderConsign.province != null">
        province,
      </if>
      <if test="orderConsign.city != null">
        city,
      </if>
      <if test="orderConsign.county != null">
        county,
      </if>
      <if test="orderConsign.town != null">
        town,
      </if>
      <if test="orderConsign.provinceId != null">
        province_id,
      </if>
      <if test="orderConsign.cityId != null">
        city_id,
      </if>
      <if test="orderConsign.countyId != null">
        county_id,
      </if>
      <if test="orderConsign.townId != null">
        town_id,
      </if>
      <if test="orderConsign.email != null">
        email,
      </if>
      <if test="orderConsign.postCode != null">
        post_code,
      </if>
      <if test="orderConsign.createdTime != null">
        created_time,
      </if>
      <if test="orderConsign.modifiedTime != null">
        modified_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderConsign.orderId != null">
        #{orderConsign.orderId,jdbcType=BIGINT},
      </if>
      <if test="orderConsign.fullName != null">
        #{orderConsign.fullName,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.telephone != null">
        #{orderConsign.telephone,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.mobile != null">
        #{orderConsign.mobile,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.fullAddress != null">
        #{orderConsign.fullAddress,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.province != null">
        #{orderConsign.province,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.city != null">
        #{orderConsign.city,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.county != null">
        #{orderConsign.county,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.town != null">
        #{orderConsign.town,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.provinceId != null">
        #{orderConsign.provinceId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.cityId != null">
        #{orderConsign.cityId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.countyId != null">
        #{orderConsign.countyId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.townId != null">
        #{orderConsign.townId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.email != null">
        #{orderConsign.email,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.postCode != null">
        #{orderConsign.postCode,jdbcType=VARCHAR},
      </if>
      <if test="orderConsign.createdTime != null">
        #{orderConsign.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderConsign.modifiedTime != null">
        #{orderConsign.modifiedTime,jdbcType=TIMESTAMP},
        </if>
    </trim>
  </insert>

  
  
   <delete id="deleteOrderByOrderId" parameterType="map">
        DELETE FROM ${tableName}
        where
		order_id = #{oid,jdbcType=BIGINT}
    </delete>
  
</mapper>