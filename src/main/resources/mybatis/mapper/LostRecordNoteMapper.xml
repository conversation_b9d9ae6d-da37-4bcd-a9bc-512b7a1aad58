<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.LostRecordNoteMapper" >

  <resultMap id="LostRecordNoteDO" type="com.pes.jd.model.DO.LostRecordNote" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="lost_flag" property="lostFlag" jdbcType="BIT" />
    <result column="note" property="note" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, buyer_nick, order_id, date, lost_flag, note
  </sql>
  
  <insert id="insertLostRecordNote" parameterType="com.pes.jd.model.DO.LostRecordNote" >
    INSERT INTO pes_lost_record_note 
    	(shop_id, buyer_nick,  order_id, date, lost_flag, note )
    VALUES 
    	(#{shopId,jdbcType=BIGINT}, #{buyerNick,jdbcType=VARCHAR}, 
      	#{orderId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, #{lostFlag,jdbcType=BIT}, #{note,jdbcType=VARCHAR} )
  </insert>
  
  <delete id="deleteLostRecordNoteById" parameterType="java.lang.Long" >
    DELETE FROM pes_lost_record_note
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateLostRecordNoteBySelective" parameterType="com.pes.jd.model.DO.LostRecordNote" >
    UPDATE pes_lost_record_note
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="buyerNick != null" >
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="lostFlag != null" >
        lost_flag = #{lostFlag,jdbcType=BIT},
      </if>
      <if test="note != null" >
        note = #{note,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
 <select id="getLostRecordNoteById" resultMap="LostRecordNoteDO" parameterType="java.lang.Long" >
    SELECT 
    	<include refid="base_field" />
    FROM pes_lost_record_note
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
  
</mapper>