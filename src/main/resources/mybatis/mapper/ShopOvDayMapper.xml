<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopOvDayMapper">

    <resultMap id="ShopDayOverviewDTO" type="com.pes.jd.model.DTO.ShopDayOverviewDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount"/>
        <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum"/>
        <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum"/>
        <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
        <result column="ordered_num" jdbcType="INTEGER" property="orderedNum"/>
        <result column="ordered_amount" jdbcType="DOUBLE" property="orderedAmount"/>
        <result column="ordered_goods_num" jdbcType="INTEGER" property="orderedGoodsNum"/>
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum"/>
        <result column="consign_num" jdbcType="INTEGER" property="consignNum"/>
        <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum"/>
        <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum"/>
        <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum"/>
        <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount"/>
        <result column="cfm_goods_o_amount" jdbcType="DOUBLE" property="cfmGoodsOAmount"/>
        <result column="cfm_goodst_o_num" jdbcType="INTEGER" property="cfmGoodstONum"/>
        <result column="sale_amount_presale" jdbcType="DOUBLE" property="saleAmountPresale"/>
        <result column="sale_amount_preordain" jdbcType="DOUBLE" property="saleAmountPreordain"/>
    </resultMap>


    <sql id="base_field">
    id, shop_id, date, sale_amount, sale_order_num, sale_goods_num, sale_buyer_num, order_post_fee, 
    ordered_num, ordered_amount, ordered_goods_num, ordered_buyer_num, consign_num, out_stock_order_num, 
    out_stock_goods_num, out_stock_num, out_stock_amount, cfm_goods_o_amount, cfm_goodst_o_num,
    sale_amount_presale,sale_amount_preordain
  </sql>

    <insert id="batchInsertShopDayOverview" parameterType="map">
        INSERT INTO ${tableName}
        ( shop_id, date,
        sale_amount, sale_order_num, sale_goods_num,
        sale_buyer_num, order_post_fee, ordered_num,
        ordered_amount, ordered_goods_num, ordered_buyer_num,
        consign_num, out_stock_order_num, out_stock_goods_num,
        out_stock_num, out_stock_amount, cfm_goods_o_amount,
        cfm_goodst_o_num,sale_amount_presale,sale_amount_preordain
        )
        VALUES
        <foreach collection="dayOverviewLst" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.saleAmount,jdbcType=DOUBLE},
            #{itm.saleOrderNum,jdbcType=INTEGER},
            #{itm.saleGoodsNum,jdbcType=INTEGER},
            #{itm.saleBuyerNum,jdbcType=INTEGER},
            #{itm.orderPostFee,jdbcType=DOUBLE},
            #{itm.orderedNum,jdbcType=INTEGER},
            #{itm.orderedAmount,jdbcType=DOUBLE},
            #{itm.orderedGoodsNum,jdbcType=INTEGER},
            #{itm.orderedBuyerNum,jdbcType=INTEGER},
            #{itm.consignNum,jdbcType=INTEGER},
            #{itm.outStockOrderNum,jdbcType=INTEGER},
            #{itm.outStockGoodsNum,jdbcType=INTEGER},
            #{itm.outStockNum,jdbcType=INTEGER},
            #{itm.outStockAmount,jdbcType=DOUBLE},
            #{itm.cfmGoodsOAmount,jdbcType=DOUBLE},
            #{itm.cfmGoodstONum,jdbcType=INTEGER},
            #{itm.saleAmountPresale,jdbcType=DOUBLE},
            #{itm.saleAmountPreordain,jdbcType=DOUBLE}
            )
        </foreach>

    </insert>

    <delete id="deleteShopDayOverviewByDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date}
  </delete>

    <delete id="deleteShopDayOverviewByDateRange">
    DELETE FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>


    <!--   <select id="getShopDayOverviewById" parameterType="java.lang.Long" resultMap="ShopDayOverviewDTO" >
        SELECT
            <include refid="base_field" />
        FROM ${tableName}
        WHERE
            id = #{id,jdbcType=BIGINT}
      </select> -->

    <select id="selectShopShopDayOverviewByShopIdAndDate" parameterType="map" resultMap="ShopDayOverviewDTO">
    SELECT
    	shop_id, date,
	    sale_amount, sale_amount_presale,sale_amount_preordain
    FROM ${tableName}
    WHERE
    	shop_id = #{shopId}
    	AND date = #{date}
  </select>
</mapper>