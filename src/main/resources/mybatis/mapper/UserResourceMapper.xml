<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.UserManageMapper">

<!-- 	<resultMap id="UserResourceDTO" type="com.pes.jd.model.DTO.UserResource">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="nick" jdbcType="VARCHAR" property="nick" />
		<result column="resource_id" jdbcType="BIGINT" property="resourceId" />
	</resultMap>
	
	<resultMap id="UserDTO" type="com.pes.jd.model.DTO.UserDTO">
		<id column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="nick" jdbcType="VARCHAR" property="nick" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="type" jdbcType="VARCHAR" property="type" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="subscribe_dead_line" jdbcType="TIMESTAMP" property="subscribeDeadLine" />
		<result column="item_code" jdbcType="VARCHAR" property="itemCode" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="lock_flag" jdbcType="INTEGER" property="lockFlag" />
		<result column="level" jdbcType="BIGINT" property="level" />
		<result column="read_maessage_time" jdbcType="TIMESTAMP" property="readMaessageTime" />
		<result column="multi_shop_switch" jdbcType="BIT" property="multiShopSwitch" />
	</resultMap>
 -->

</mapper>