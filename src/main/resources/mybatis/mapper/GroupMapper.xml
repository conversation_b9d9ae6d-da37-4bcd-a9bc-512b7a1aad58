<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.GroupMapper">

  <resultMap id="GroupDTO" type="com.pes.jd.model.DTO.GroupDTO">
    <id column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="created" jdbcType="TIMESTAMP" property="created" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
  </resultMap>
  
  <sql id="base_field">
    group_id, group_name, shop_id, created, modified, is_default
  </sql>
  
  <insert id="insertGroup" parameterType="com.pes.jd.model.DO.Group">
    INSERT INTO pes_group (group_id, group_name, shop_id, created, modified, is_default)
    VALUES 
    	(  
    		#{groupId,jdbcType=BIGINT}, #{groupName,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, 
      		#{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}, #{isDefault,jdbcType=BIT}
      	)
  </insert>
  
  <delete id="deleteGroupByGroupId" parameterType="java.lang.Long">
    DELETE from pes_group
    WHERE 
    	group_id = #{groupId,jdbcType=BIGINT}
  </delete>
  
  <update id="updateGroupBySelective" parameterType="com.pes.jd.model.DO.Group">
    UPDATE pes_group
    <set>
      <if test="groupName != null">
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null">
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
    </set>
    WHERE group_id = #{groupId,jdbcType=BIGINT}
  </update>
  
  <select id="getGroupByGroupId" parameterType="java.lang.Long" resultMap="GroupDTO">
    SELECT 
   		 <include refid="base_field" />
    FROM pes_group
   	WHERE 
   		group_id = #{groupId,jdbcType=BIGINT}
  </select>
</mapper>