<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.CsChatSessionMapper">
    <resultMap id="CsChatSessionDO" type="com.pes.jd.model.DTO.CsChatSessionDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sid" jdbcType="VARCHAR" property="sid"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="customer" jdbcType="VARCHAR" property="customer"/>
        <result column="session_begin_time" jdbcType="TIMESTAMP" property="sessionBeginTime"/>
        <result column="session_end_time" jdbcType="TIMESTAMP" property="sessionEndTime"/>
        <result column="begin_datetime" jdbcType="TIMESTAMP" property="beginDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
        <result column="reply_datetime" jdbcType="TIMESTAMP" property="replyDatetime"/>
        <result column="session_type" jdbcType="BIT" property="sessionType"/>
        <result column="is_transfer" jdbcType="BIT" property="isTransfer"/>
        <result column="is_non_reply" jdbcType="BIT" property="isNonReply"/>
        <result column="is_slow_resp" jdbcType="BIT" property="isSlowResp"/>
        <result column="is_long_receive" jdbcType="BIT" property="isLongReceive"/>
        <result column="send_eval_num" jdbcType="INTEGER" property="sendEvalNum"/>
        <result column="eval_num" jdbcType="INTEGER" property="evalNum"/>
        <result column="eval_code" jdbcType="INTEGER" property="evalCode"/>
        <result column="satisfied_eval_num" jdbcType="INTEGER" property="satisfiedEvalNum"/>
        <result column="is_consult" jdbcType="BIT" property="isConsult"/>
        <result column="is_receive" jdbcType="BIT" property="isReceive"/>
        <result column="is_assign" jdbcType="BIT" property="isAssign"/>
        <result column="session_duration_time" jdbcType="DOUBLE" property="sessionDurationTime"/>
        <result column="session_receive_duration_time" jdbcType="DOUBLE" property="sessionReceiveDurationTime"/>
        <result column="avg_resp_time_first" jdbcType="DOUBLE" property="avgRespTimeFirst"/>
        <result column="avg_resp_time" jdbcType="DOUBLE" property="avgRespTime"/>
        <result column="cs_chat_num" jdbcType="INTEGER" property="csChatNum"/>
        <result column="cust_chat_num" jdbcType="INTEGER" property="custChatNum"/>
        <result column="receive_start_type" jdbcType="BIT" property="receiveStartType"/>
        <result column="forward_type" jdbcType="BIT" property="forwardType"/>
        <result column="leave_msg_filter" jdbcType="BIT" property="leaveMsgFilter"/>
    </resultMap>
    <resultMap id="crossChatSessionDTO" type="com.pes.jd.model.DTO.CrossChatSessionDTO">
        <result column="sid" jdbcType="VARCHAR" property="sid"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="customer" jdbcType="VARCHAR" property="customer"/>
        <result column="leave_msg_filter" jdbcType="BIT" property="leaveMsgFilter"/>
    </resultMap>
    <sql id="base_field">
    id, sid, shop_id, date, cs_nick, customer, session_begin_time, session_end_time,
    begin_datetime, end_datetime, reply_datetime, session_type, is_transfer, is_non_reply,
    is_slow_resp, is_long_receive, send_eval_num, eval_num, eval_code, satisfied_eval_num,
    is_consult, is_receive, is_assign, session_duration_time, session_receive_duration_time,
    avg_resp_time_first, avg_resp_time, cs_chat_num, cust_chat_num, receive_start_type,
    forward_type,leave_msg_filter
  </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="CsChatSessionDO">
        select
        <include refid="base_field"/>
        from pes_cs_chat_session
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="searchAllByTime" resultMap="CsChatSessionDO">
        select
        sid,sku_id
        from ${tableName}
        where date between #{beginDate} and #{endDate}
        and shop_id = #{shopId}
        <if test="nick != null">
            and cs_nick = #{nick}
        </if>
        order by session_begin_time
    </select>

    <select id="searchForwordOutSidByTime" resultType="java.lang.String">
        select
        sid
        from ${tableName}
        where date between #{beginDate} and #{endDate}
        and shop_id = #{shopId}
        <if test="nick != null">
            and cs_nick = #{nick}
        </if>
        and forward_type=2
    </select>

    <select id="searchSidByTimeForwordType" resultType="java.lang.String">
        select
        sid
        from ${tableName}
        where date between #{beginDate} and #{endDate}
        and shop_id = #{shopId}
        <if test="nick != null">
            and cs_nick = #{nick}
        </if>
        <if test="forwordType != null">
            and forward_type = #{forwordType}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_chat_session
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <insert id="insertBatchCsChatSession">
        insert into ${tableName} (sid, shop_id,
        date, cs_nick, customer,
        session_begin_time, session_end_time, reply_datetime,
        session_type, is_transfer,sku_id)
        values
        <foreach collection="csChatSessionLst" item="itm" separator=",">
            (#{itm.sid,jdbcType=VARCHAR}, #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE}, #{itm.csNick,jdbcType=VARCHAR}, #{itm.customer,jdbcType=VARCHAR},
            #{itm.sessionBeginTime,jdbcType=TIMESTAMP}, #{itm.sessionEndTime,jdbcType=TIMESTAMP},
            #{itm.replyDatetime,jdbcType=TIMESTAMP},
            #{itm.sessionType,jdbcType=INTEGER}, #{itm.isTransfer,jdbcType=BIT},#{itm.skuId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsChatSessionDO">
        <foreach collection="recordLst" separator=";" item="record">
            update ${tableName}
            set sid = #{record.sid,jdbcType=VARCHAR},
            shop_id = #{record.shopId,jdbcType=BIGINT},
            date = #{record.date,jdbcType=DATE},
            cs_nick = #{record.csNick,jdbcType=VARCHAR},
            customer = #{record.customer,jdbcType=VARCHAR},
                session_begin_time = #{record.sessionBeginTime,jdbcType=TIMESTAMP},
                session_end_time = #{record.sessionEndTime,jdbcType=TIMESTAMP},
                begin_datetime = #{record.beginDatetime,jdbcType=TIMESTAMP},
                end_datetime = #{record.endDatetime,jdbcType=TIMESTAMP},
            reply_datetime = #{record.replyDatetime,jdbcType=TIMESTAMP},
            session_type = #{record.sessionType,jdbcType=BIT},
            is_transfer = #{record.isTransfer,jdbcType=BIT},
            is_non_reply = #{record.isNonReply,jdbcType=BIT},
            is_slow_resp = #{record.isSlowResp,jdbcType=BIT},
            is_long_receive = #{record.isLongReceive,jdbcType=BIT},
            send_eval_num = #{record.sendEvalNum,jdbcType=INTEGER},
            eval_num = #{record.evalNum,jdbcType=INTEGER},
            eval_code = #{record.evalCode,jdbcType=INTEGER},
            satisfied_eval_num = #{record.satisfiedEvalNum,jdbcType=INTEGER},
            is_consult = #{record.isConsult,jdbcType=BIT},
            is_receive = #{record.isReceive,jdbcType=BIT},
            is_assign = #{record.isAssign,jdbcType=BIT},
            session_duration_time = #{record.sessionDurationTime,jdbcType=DOUBLE},
            session_receive_duration_time = #{record.sessionReceiveDurationTime,jdbcType=DOUBLE},
            avg_resp_time_first = #{record.avgRespTimeFirst,jdbcType=DOUBLE},
            avg_resp_time = #{record.avgRespTime,jdbcType=DOUBLE},
            cs_chat_num = #{record.csChatNum,jdbcType=INTEGER},
            cust_chat_num = #{record.custChatNum,jdbcType=INTEGER},
            receive_start_type = #{record.receiveStartType,jdbcType=BIT},
            forward_type = #{record.forwardType,jdbcType=BIT},
            slow_resp_num = #{record.slowRespNum,jdbcType=INTEGER},
            max_slow_resp_time = #{record.maxSlowRespTime,jdbcType=DOUBLE},
            leave_msg_filter = #{record.leaveMsgFilter,jdbcType=BIT}
            where id = #{record.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <delete id="deleteChatSessionByShopIdAndDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date = #{date}
	</delete>

    <select id="getCsChatSessionBySidByDate" parameterType="map" resultType="com.pes.jd.model.DO.CsChatSessionDO">
        SELECT session_begin_time beginDatetime,session_end_time endDatetime
        FROM ${tableName}
        WHERE sid = #{sid}
        <!-- 	sid IN
            <foreach collection="sids" item="sid" open="(" close=")" >
                 #{sid}
            </foreach> -->
        AND shop_id = #{shopId}
        LIMIT 0,1
    </select>

    <update id="updateSetForwordFlag">
        update ${tableName} set forward_type = #{flag} where sid in
        <foreach collection="sid" item="ssid" open="(" close=")" separator=",">
            #{ssid}
        </foreach>
    </update>
    <select id="searchForWordInOutAndDirect" resultType="int">

        select count(1) from ${tableName} where forward_type = 2 and shop_id = #{shopId} and cs_nick = #{nick} and date = #{date}  and is_receive = 1
        <if test="sessionType != null">
            and session_type = #{sessionType}
        </if><!-- 转出接待-->
        and leave_msg_filter!=1
        union all
        select count(1) from ${tableName} where forward_type = 1 and shop_id = #{shopId} and cs_nick = #{nick} and date = #{date} and is_receive = 1
        and leave_msg_filter!=1<!-- 转入接待-->
        union all
        select count(1) from ${tableName} where forward_type = 0 and shop_id = #{shopId} and cs_nick = #{nick} and date = #{date} and is_receive = 1
        and leave_msg_filter!=1<!-- 直接接待-->

    </select>

    <select id="selectReceiveCsChatSessionSidByDate" resultType="java.lang.String">
        SELECT DISTINCT(sid)
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND cs_nick IN
        <foreach collection="csLst" item="cs" open="(" close=")" separator=",">
            #{cs.nick}
        </foreach>
        AND date = #{date}
        AND is_receive = 1
    </select>

    <select id="selectReceiveCsChatSessionLstByDate" resultMap="CsChatSessionDO">
        SELECT *
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND cs_nick IN
        <foreach collection="csLst" item="cs" open="(" close=")" separator=",">
            #{cs.nick}
        </foreach>
        AND is_receive = 1
        AND `date` BETWEEN #{beginDate} AND #{endDate}
    </select>

    <select id="selectCrossChatCsSessionByShopIdForEndTimeEqual" resultMap="crossChatSessionDTO">
        SELECT
        sid,
        shop_id,
        date,
        cs_nick,
        customer

        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND date=#{date}
        AND session_end_time=end_datetime
        <!-- AND session_type=1 -->
    </select>

    <select id="selectCrossChatCsSessionByShopIdByDate" resultMap="crossChatSessionDTO">
        SELECT
        sid,
        shop_id,
        date,
        cs_nick,
        customer
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND date=#{date}
        AND session_begin_time <![CDATA[ < #{targetDate}]]>
        AND session_end_time <![CDATA[ >= #{targetDate}]]>
        <!-- AND session_type=1 -->
    </select>


    <select id="selectChatSessionIdsByShopIdByDate" resultType="java.lang.Long">
	    SELECT
	    	id
		FROM ${tableName}
		WHERE
		 	shop_id = #{shopId}
		AND date = #{date}
  	</select>
    <delete id="deleteChatSessionByIds" parameterType="map">
        DELETE FROM ${tableName}
        WHERE
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="selectChatCsSessionByShopIdByDate" resultMap="crossChatSessionDTO">
    	SELECT
	    	shop_id
	    	date,
	    	cs_nick,
	    	customer

    	FROM ${tableName}
    	WHERE
	    	shop_id=#{shopId}
	    	AND date=#{date}
    </select>

    <select id="selectByShopIdAndDateAndSkuIds" resultMap="CsChatSessionDO">
        SELECT
        `date`,cs_nick,customer,sku_id
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND `date` BETWEEN #{start} AND #{end}
        AND sku_id IN
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        AND is_consult = 1
    </select>

    <select id="selectEvaluationBySids" resultMap="CsChatSessionDO">
        SELECT sid, shop_id, `date`, cs_nick, session_begin_time, session_end_time, begin_datetime, end_datetime
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND sid IN
        <foreach collection="sids" item="sid" open="(" close=")" separator=",">
            #{sid}
        </foreach>
    </select>

    <select id="selectShopCsChatSessionLstByBuyerNickLstForConsultHandle" resultMap="CsChatSessionDO">
        SELECT distinct customer,cs_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND cs_nick = #{csNick}
        AND customer IN
        <foreach item="buyerNick" collection="buyerNickLst" open="(" close=")" separator=",">
            #{buyerNick}
        </foreach>
        AND session_begin_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="findLatestSessions" resultType="com.pes.jd.model.DO.CsChatSessionDO">
        SELECT sid,session_begin_time,session_end_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
          AND cs_nick = #{csNick}
          AND customer = #{customer}
          AND session_end_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY session_end_time DESC
            LIMIT 1;

    </select>
</mapper>
