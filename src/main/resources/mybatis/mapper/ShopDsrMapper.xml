<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopDsrMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopDsrDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="item_score" property="itemScore" jdbcType="NUMERIC" />
    <result column="service_score" property="serviceScore" jdbcType="NUMERIC" />
    <result column="delivery_score" property="deliveryScore" jdbcType="NUMERIC" />
      <result column="after_sale_score" property="deliveryScore" jdbcType="NUMERIC" />
      <result column="dispute_score" property="deliveryScore" jdbcType="NUMERIC" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, date, item_score, service_score, delivery_score,after_sale_score,dispute_score
  </sql>
  <insert id="persistShopDsr" parameterType="map">
		INSERT INTO ${tableName}(shop_id,date,item_score,service_score,delivery_score,after_sale_score,dispute_score)
		VALUES
		(#{dsr.shopId},#{dsr.date},#{dsr.itemScore},#{dsr.serviceScore},#{dsr.deliveryScore},#{dsr.afterSaleScore},#{dsr.disputeScore})
  </insert>
  <delete id="deleteShopDsrByShopIdAndDate" parameterType="java.util.HashMap">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date BETWEEN #{startDate} AND #{endDate}
  </delete>

    <select id="selectShopDsrByShopIdAndDate" parameterType="java.util.HashMap" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"/>
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date BETWEEN #{startDate} AND #{endDate}
    </select>
</mapper>