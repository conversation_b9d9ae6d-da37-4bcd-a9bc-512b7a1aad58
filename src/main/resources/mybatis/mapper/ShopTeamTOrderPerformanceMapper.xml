<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopTeamTOrderPerformanceMapper">

  <resultMap id="ShopTeamTOrderPerformanceDTO" type="com.pes.jd.model.DTO.ShopTeamTOrderPerformanceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="to_ordered_num" jdbcType="INTEGER" property="toOrderedNum" />
    <result column="to_ordered_order_num" jdbcType="INTEGER" property="toOrderedOrderNum" />
    <result column="to_ordered_goods_num" jdbcType="INTEGER" property="toOrderedGoodsNum" />
    <result column="to_ordered_amount" jdbcType="DOUBLE" property="toOrderedAmount" />
    <result column="to_ordered_paid_num_today" jdbcType="INTEGER" property="toOrderedPaidNumToday" />
    <result column="to_ordered_paid_order_num_today" jdbcType="INTEGER" property="toOrderedPaidOrderNumToday" />
    <result column="to_ordered_paid_goods_today" jdbcType="INTEGER" property="toOrderedPaidGoodsToday" />
    <result column="to_ordered_paid_amount_today" jdbcType="DOUBLE" property="toOrderedPaidAmountToday" />
    <result column="to_ordered_paid_num_final" jdbcType="INTEGER" property="toOrderedPaidNumFinal" />
    <result column="to_ordered_paid_order_num_final" jdbcType="INTEGER" property="toOrderedPaidOrderNumFinal" />
    <result column="to_ordered_paid_goods_final" jdbcType="INTEGER" property="toOrderedPaidGoodsFinal" />
    <result column="to_ordered_paid_amount_final" jdbcType="DOUBLE" property="toOrderedPaidAmountFinal" />
    <result column="to_ordered_out_stock_num" jdbcType="INTEGER" property="toOrderedOutStockNum" />
    <result column="to_ordered_out_stock_amount" jdbcType="DOUBLE" property="toOrderedOutStockAmount" />
    <result column="to_ordered_out_stock_goods_num" jdbcType="INTEGER" property="toOrderedOutStockGoodsNum" />
    <result column="to_ordered_out_stock_order_num" jdbcType="INTEGER" property="toOrderedOutStockOrderNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
    <result column="sale_sku_num" jdbcType="INTEGER" property="saleSkuNum" />
    <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
    <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
    <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
    <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
    <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
    <result column="cfm_goods_order_num" jdbcType="INTEGER" property="cfmGoodsOrderNum" />
    <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
    <result column="cfm_goods_num" jdbcType="INTEGER" property="cfmGoodsNum" />
    <result column="cfm_goods_buyer_num" jdbcType="INTEGER" property="cfmGoodsBuyerNum" />
  </resultMap>

  <resultMap id="shopTeamTOrderPerformanceDO" type="com.pes.jd.model.DO.ShopTeamTOrderPerformanceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="to_ordered_num" jdbcType="INTEGER" property="toOrderedNum" />
    <result column="to_ordered_order_num" jdbcType="INTEGER" property="toOrderedOrderNum" />
    <result column="to_ordered_goods_num" jdbcType="INTEGER" property="toOrderedGoodsNum" />
    <result column="to_ordered_amount" jdbcType="DOUBLE" property="toOrderedAmount" />
    <result column="to_ordered_paid_num_today" jdbcType="INTEGER" property="toOrderedPaidNumToday" />
    <result column="to_ordered_paid_order_num_today" jdbcType="INTEGER" property="toOrderedPaidOrderNumToday" />
    <result column="to_ordered_paid_goods_today" jdbcType="INTEGER" property="toOrderedPaidGoodsToday" />
    <result column="to_ordered_paid_amount_today" jdbcType="DOUBLE" property="toOrderedPaidAmountToday" />
    <result column="to_ordered_paid_num_final" jdbcType="INTEGER" property="toOrderedPaidNumFinal" />
    <result column="to_ordered_paid_order_num_final" jdbcType="INTEGER" property="toOrderedPaidOrderNumFinal" />
    <result column="to_ordered_paid_goods_final" jdbcType="INTEGER" property="toOrderedPaidGoodsFinal" />
    <result column="to_ordered_paid_amount_final" jdbcType="DOUBLE" property="toOrderedPaidAmountFinal" />
    <result column="to_ordered_out_stock_num" jdbcType="INTEGER" property="toOrderedOutStockNum" />
    <result column="to_ordered_out_stock_amount" jdbcType="DOUBLE" property="toOrderedOutStockAmount" />
    <result column="to_ordered_out_stock_goods_num" jdbcType="INTEGER" property="toOrderedOutStockGoodsNum" />
    <result column="to_ordered_out_stock_order_num" jdbcType="INTEGER" property="toOrderedOutStockOrderNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
    <result column="sale_sku_num" jdbcType="INTEGER" property="saleSkuNum" />
    <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
    <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
    <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
    <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
    <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
    <result column="cfm_goods_order_num" jdbcType="INTEGER" property="cfmGoodsOrderNum" />
    <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
    <result column="cfm_goods_num" jdbcType="INTEGER" property="cfmGoodsNum" />
    <result column="cfm_goods_buyer_num" jdbcType="INTEGER" property="cfmGoodsBuyerNum" />
  </resultMap>

  <insert id="insertShopTeamTOrderPerformances" parameterType="map">
    INSERT INTO ${tableName} 
    (
      shop_id, 
      date, 
      to_ordered_num, 
      to_ordered_order_num, 
      to_ordered_goods_num, 
      to_ordered_amount, 
      to_ordered_paid_num_today, 
      to_ordered_paid_order_num_today, 
      to_ordered_paid_goods_today, 
      to_ordered_paid_amount_today, 
      to_ordered_paid_num_final, 
      to_ordered_paid_order_num_final, 
      to_ordered_paid_goods_final, 
      to_ordered_paid_amount_final, 
      to_ordered_out_stock_num, 
      to_ordered_out_stock_amount, 
      to_ordered_out_stock_goods_num, 
      to_ordered_out_stock_order_num, 
      sale_amount, sale_order_num, 
      sale_goods_num, sale_buyer_num, 
      sale_sku_num, 
      post_fee, out_stock_num, 
      out_stock_amount, 
      out_stock_goods_num, 
      out_stock_order_num, 
      cfm_goods_order_num, 
      cfm_goods_amount, 
      cfm_goods_num, 
      cfm_goods_buyer_num
      )
    VALUES 
    <foreach item="itm" collection="recordLst" open="" close="" separator=",">
    (
      #{itm.shopId,jdbcType=BIGINT}, 
      #{itm.date,jdbcType=DATE}, 
      #{itm.toOrderedNum,jdbcType=INTEGER}, 
      #{itm.toOrderedOrderNum,jdbcType=INTEGER}, 
      #{itm.toOrderedGoodsNum,jdbcType=INTEGER}, 
      #{itm.toOrderedAmount,jdbcType=DOUBLE}, 
      #{itm.toOrderedPaidNumToday,jdbcType=INTEGER}, 
      #{itm.toOrderedPaidOrderNumToday,jdbcType=INTEGER}, 
      #{itm.toOrderedPaidGoodsToday,jdbcType=INTEGER}, 
      #{itm.toOrderedPaidAmountToday,jdbcType=DOUBLE}, 
      #{itm.toOrderedPaidNumFinal,jdbcType=INTEGER}, 
      #{itm.toOrderedPaidOrderNumFinal,jdbcType=INTEGER}, 
      #{itm.toOrderedPaidGoodsFinal,jdbcType=INTEGER}, 
      #{itm.toOrderedPaidAmountFinal,jdbcType=DOUBLE}, 
      #{itm.toOrderedOutStockNum,jdbcType=INTEGER}, 
      #{itm.toOrderedOutStockAmount,jdbcType=DOUBLE}, 
      #{itm.toOrderedOutStockGoodsNum,jdbcType=INTEGER}, 
      #{itm.toOrderedOutStockOrderNum,jdbcType=INTEGER}, 
      #{itm.saleAmount,jdbcType=DOUBLE}, 
      #{itm.saleOrderNum,jdbcType=INTEGER}, 
      #{itm.saleGoodsNum,jdbcType=INTEGER}, 
      #{itm.saleBuyerNum,jdbcType=INTEGER}, 
      #{itm.saleSkuNum,jdbcType=INTEGER}, 
      #{itm.postFee,jdbcType=DOUBLE}, 
      #{itm.outStockNum,jdbcType=INTEGER}, 
      #{itm.outStockAmount,jdbcType=DOUBLE}, 
      #{itm.outStockGoodsNum,jdbcType=INTEGER}, 
      #{itm.outStockOrderNum,jdbcType=INTEGER}, 
      #{itm.cfmGoodsOrderNum,jdbcType=INTEGER}, 
      #{itm.cfmGoodsAmount,jdbcType=DOUBLE}, 
      #{itm.cfmGoodsNum,jdbcType=INTEGER}, 
      #{itm.cfmGoodsBuyerNum,jdbcType=INTEGER}
     )
    </foreach>
    
  </insert>

  <delete id="deleteShopTeamTOrderPerformance" parameterType="java.lang.Long">
    DELETE FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>
  
  <update id="updateShopTeamTOrderPerformanceSelective" parameterType="map">
    UPDATE ${tableName} 
    <set>
      <if test="record.toOrderedNum != null">
        to_ordered_num = #{record.toOrderedNum,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedOrderNum != null">
        to_ordered_order_num = #{record.toOrderedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedGoodsNum != null">
        to_ordered_goods_num = #{toOrderedGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedAmount != null">
        to_ordered_amount = #{record.toOrderedAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.toOrderedPaidNumToday != null">
        to_ordered_paid_num_today = #{record.toOrderedPaidNumToday,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedPaidOrderNumToday != null">
        to_ordered_paid_order_num_today = #{record.toOrderedPaidOrderNumToday,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedPaidGoodsToday != null">
        to_ordered_paid_goods_today = #{record.toOrderedPaidGoodsToday,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedPaidAmountToday != null">
        to_ordered_paid_amount_today = #{record.toOrderedPaidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="record.toOrderedPaidNumFinal != null">
        to_ordered_paid_num_final = #{record.toOrderedPaidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedPaidOrderNumFinal != null">
        to_ordered_paid_order_num_final = #{record.toOrderedPaidOrderNumFinal,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedPaidGoodsFinal != null">
        to_ordered_paid_goods_final = #{record.toOrderedPaidGoodsFinal,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedPaidAmountFinal != null">
        to_ordered_paid_amount_final = #{record.toOrderedPaidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="record.toOrderedOutStockNum != null">
        to_ordered_out_stock_num = #{record.toOrderedOutStockNum,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedOutStockAmount != null">
        to_ordered_out_stock_amount = #{record.toOrderedOutStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.toOrderedOutStockGoodsNum != null">
        to_ordered_out_stock_goods_num = #{record.toOrderedOutStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="record.toOrderedOutStockOrderNum != null">
        to_ordered_out_stock_order_num = #{record.toOrderedOutStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="record.saleAmount != null">
        sale_amount = #{record.saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.saleOrderNum != null">
        sale_order_num = #{record.saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="record.saleGoodsNum != null">
        sale_goods_num = #{record.saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="record.saleBuyerNum != null">
        sale_buyer_num = #{record.saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="record.saleSkuNum != null">
        sale_sku_num = #{record.saleSkuNum,jdbcType=INTEGER},
      </if>
      <if test="record.postFee != null">
        post_fee = #{record.postFee,jdbcType=DOUBLE},
      </if>
      <if test="record.outStockNum != null">
        out_stock_num = #{record.outStockNum,jdbcType=INTEGER},
      </if>
      <if test="record.outStockAmount != null">
        out_stock_amount = #{record.outStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.outStockGoodsNum != null">
        out_stock_goods_num = #{record.outStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="record.outStockOrderNum != null">
        out_stock_order_num = #{record.outStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="record.cfmGoodsOrderNum != null">
        cfm_goods_order_num = #{record.cfmGoodsOrderNum,jdbcType=INTEGER},
      </if>
      <if test="record.cfmGoodsAmount != null">
        cfm_goods_amount = #{record.cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="record.cfmGoodsNum != null">
        cfm_goods_num = #{record.cfmGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="record.cfmGoodsBuyerNum != null">
        cfm_goods_buyer_num = #{record.cfmGoodsBuyerNum,jdbcType=INTEGER},
      </if>
    </set>
    WHERE 
    	id = #{record.id,jdbcType=BIGINT}
  </update>


  <update id="updateShopTeamSaleAndOutStackData" parameterType="map">
    UPDATE ${tableName}
    SET
        sale_amount = #{record.saleAmount,jdbcType=DOUBLE},
        sale_order_num = #{record.saleOrderNum,jdbcType=INTEGER},
        sale_goods_num = #{record.saleGoodsNum,jdbcType=INTEGER},
        sale_buyer_num = #{record.saleBuyerNum,jdbcType=INTEGER},
        sale_sku_num = #{record.saleSkuNum,jdbcType=INTEGER},
        post_fee = #{record.postFee,jdbcType=DOUBLE},
        out_stock_num = #{record.outStockNum,jdbcType=INTEGER},
        out_stock_amount = #{record.outStockAmount,jdbcType=DOUBLE},
        out_stock_goods_num = #{record.outStockGoodsNum,jdbcType=INTEGER},
        out_stock_order_num = #{record.outStockOrderNum,jdbcType=INTEGER}
    WHERE
      id = #{record.id,jdbcType=BIGINT}
  </update>

  <update id="updateShopTeamOutStackData" parameterType="map">
    UPDATE ${tableName}
    SET
        out_stock_num = #{record.outStockNum,jdbcType=INTEGER},
        out_stock_amount = #{record.outStockAmount,jdbcType=DOUBLE},
        out_stock_goods_num = #{record.outStockGoodsNum,jdbcType=INTEGER},
        out_stock_order_num = #{record.outStockOrderNum,jdbcType=INTEGER}
    WHERE
      id = #{record.id,jdbcType=BIGINT}
  </update>
  <select id="selectShopTeamTOrderPerformanceForUpdate" parameterType="map" resultType="com.pes.jd.model.DO.ShopTeamTOrderPerformanceDO">
    SELECT id,shop_id as shopId,date
    FROM ${tableName}
    WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
      AND date = #{date,jdbcType=DATE}
  </select>

  <select id="selectShopTeamTOrderPerformance" resultMap="shopTeamTOrderPerformanceDO">
        SELECT *
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          AND date = #{date,jdbcType=DATE}
</select>
</mapper>