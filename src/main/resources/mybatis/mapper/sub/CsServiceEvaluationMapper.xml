<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsServiceEvaluationMapper">

    <resultMap id="CsServiceEvaluationDO" type="com.pes.jd.model.DO.CsServiceEvaluationDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="medal_num" jdbcType="INTEGER" property="medalNum"/>
        <result column="very_satisfied_num" jdbcType="INTEGER" property="verySatisfiedNum"/>
        <result column="satisfied_num" jdbcType="INTEGER" property="satisfiedNum"/>
        <result column="general_num" jdbcType="INTEGER" property="generalNum"/>
        <result column="dissatisfied_num" jdbcType="INTEGER" property="dissatisfiedNum"/>
        <result column="very_dissatisfied_num" jdbcType="INTEGER" property="veryDissatisfiedNum"/>
    </resultMap>

    <resultMap id="CsServiceEvaluationDTO" type="com.pes.jd.model.DTO.CsServiceEvaluationDTO">
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="medal_num" jdbcType="INTEGER" property="medalNum"/>
        <result column="very_satisfied_num" jdbcType="INTEGER" property="verySatisfiedNum"/>
        <result column="satisfied_num" jdbcType="INTEGER" property="satisfiedNum"/>
        <result column="general_num" jdbcType="INTEGER" property="generalNum"/>
        <result column="dissatisfied_num" jdbcType="INTEGER" property="dissatisfiedNum"/>
        <result column="very_dissatisfied_num" jdbcType="INTEGER" property="veryDissatisfiedNum"/>
        <result column="eval_reply_num" jdbcType="INTEGER" property="evalReplyNum" />
        <result column="eval_send_num" jdbcType="INTEGER" property="evalSendNum" />
    </resultMap>

    <sql id="base_field">
        id, shop_id, cs_nick, date, medal_num, very_satisfied_num, satisfied_num, general_num,
    dissatisfied_num, very_dissatisfied_num
  </sql>

    <sql id="field_for_performance">
    id, shop_id, cs_nick, date, medal_num, very_satisfied_num, satisfied_num, general_num,
    dissatisfied_num, very_dissatisfied_num, eval_reply_num, eval_send_num
  </sql>

    <select id="selectByDateAndShopNick" resultType="java.util.Map" parameterType="map">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE eval_time BETWEEN ${begin} AND ${end}
        AND shop_id = #{shopId} AND cs_nick = #{nick}
    </select>

    <select id="selectPerformanceData" resultType="com.pes.jd.model.DTO.NickPerformanceDTO">
        SELECT
        count(1) evalDataSize, -- 每组总数
        count(eval_code) evalCode,
        DATE_FORMAT(send_time,'%Y-%m-%d') date,
        cs_nick nick,
        count(CASE WHEN eval_code >=75 THEN 1 ELSE NULL END) goodEvaluationCount -- 满意评价数量
        FROM ${tableName}
        WHERE
        shop_id IN
        <foreach collection="nicks" item="item" separator="," open="(" close=")">
            #{item.shopId}
        </foreach>
        AND cs_nick IN
        <foreach collection="nicks" item="item" separator="," open="(" close=")">
            #{item.nick}
        </foreach>
        AND send_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY ${groupBy}
    </select>

    <select id="getCsServiceEvaluationById" resultMap="CsServiceEvaluationDO" parameterType="java.lang.Long">
        SELECT
        <include refid="base_field"/>
        FROM pes_cs_service_evaluation
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectShopQNDailyEvalLst" resultMap="CsServiceEvaluationDO" parameterType="java.util.Map">
    SELECT
			DATE_FORMAT(send_time,'%Y-%m-%d') as date,
			avg(eval_code) as avgEvalCode,
			count(send_time) as count,
			sum(eval_code) as totalEvalCode
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND send_time BETWEEN #{beginDate} AND #{endDate}
		AND eval_time is not null
		GROUP BY date
  </select>

    <select id="selectShopQNDailyEvalLstReturnMap" resultType="java.util.Map" parameterType="java.util.Map">
    SELECT
    DATE_FORMAT(send_time,'%Y-%m-%d') as date,
    avg(eval_code) as avgEvalCode,
    count(send_time) as count,
    sum(eval_code) as totalEvalCode
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND send_time BETWEEN #{beginDate} AND #{endDate}
    AND eval_time is not null
    GROUP BY date
  </select>

    <select id="selectCsServiceEvaluationByNick" resultMap="CsServiceEvaluationDO" parameterType="java.util.Map">
        SELECT
        <foreach collection="columns" item="item" separator=",">
            ${item}
        </foreach>
        , cs_id
        FROM pes_cs_service_evaluation
        WHERE
        cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
            #{nick}
        </foreach>
    </select>

    <delete id="deleteCsServiceEvaluationById" parameterType="java.lang.Long">
    DELETE FROM pes_cs_service_evaluation
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>

    <!--<insert id="insertCsServiceEvaluation" parameterType="com.pes.jd.model.DO.CsServiceEvaluationDO" >-->
    <!--INSERT INTO pes_cs_service_evaluation (id, shop_id, cs_nick, -->
    <!--buyer_nick, send_time, eval_time, -->
    <!--eval_code)-->
    <!--VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, -->
    <!--#{buyerNick,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{evalTime,jdbcType=TIMESTAMP}, -->
    <!--#{evalCode,jdbcType=INTEGER})-->
    <!--</insert>-->
    <!--<update id="updateCsServiceEvaluationById" parameterType="com.pes.jd.model.DO.CsServiceEvaluationDO" >-->
    <!--UPDATE pes_cs_service_evaluation-->
    <!--<set >-->
    <!--<if test="shopId != null" >-->
    <!--shop_id = #{shopId,jdbcType=BIGINT},-->
    <!--</if>-->
    <!--<if test="csNick != null" >-->
    <!--cs_nick = #{csNick,jdbcType=VARCHAR},-->
    <!--</if>-->
    <!--<if test="buyerNick != null" >-->
    <!--buyer_nick = #{buyerNick,jdbcType=VARCHAR},-->
    <!--</if>-->
    <!--<if test="sendTime != null" >-->
    <!--send_time = #{sendTime,jdbcType=TIMESTAMP},-->
    <!--</if>-->
    <!--<if test="evalTime != null" >-->
    <!--eval_time = #{evalTime,jdbcType=TIMESTAMP},-->
    <!--</if>-->
    <!--<if test="evalCode != null" >-->
    <!--eval_code = #{evalCode,jdbcType=INTEGER},-->
    <!--</if>-->
    <!--</set>-->
    <!--WHERE id = #{id,jdbcType=BIGINT}-->
    <!--</update>-->

    <insert id="insertCsServiceEvaluation" parameterType="com.pes.jd.model.DO.CsServiceEvaluationDO">
    INSERT INTO pes_cs_service_evaluation (id, shop_id, cs_nick, 
      buyer_nick, send_time, eval_time, 
      eval_code)
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR},
      #{buyerNick,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{evalTime,jdbcType=TIMESTAMP},
      #{evalCode,jdbcType=INTEGER})
  </insert>
    <update id="updateCsServiceEvaluationById" parameterType="com.pes.jd.model.DO.CsServiceEvaluationDO">
        UPDATE pes_cs_service_evaluation
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="buyerNick != null">
                buyer_nick = #{buyerNick,jdbcType=VARCHAR},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="evalTime != null">
                eval_time = #{evalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="evalCode != null">
                eval_code = #{evalCode,jdbcType=INTEGER},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectCsNickServiceEvalByEvaltimeCount" parameterType="map"
            resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
        SELECT cs_nick csNick,buyer_nick buyerNick,DATE_FORMAT(eval_time,'%Y-%m-%d') as evalTime, count(eval_time) as
        count
        FROM ${tableName}
        WHERE
        eval_time BETWEEN #{startDate} AND #{endDate}
        AND shop_id = #{shopId}
        <!-- AND cs_nick=#{csNick}
        AND buyer_nick=#{buyerNick} -->
        AND eval_time is not null
        GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick,buyer_nick

    </select>

    <!--<insert id="insertCsServiceEvaluation" parameterType="com.pes.jd.model.DO.CsServiceEvaluation">-->
    <!--INSERT INTO pes_cs_service_evaluation (id, shop_id, cs_nick,-->
    <!--buyer_nick, send_time, eval_time,-->
    <!--eval_code)-->
    <!--VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR},-->
    <!--#{buyerNick,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, #{evalTime,jdbcType=TIMESTAMP},-->
    <!--#{evalCode,jdbcType=INTEGER})-->
    <!--</insert>-->
    <!--<update id="updateCsServiceEvaluationById" parameterType="com.pes.jd.model.DO.CsServiceEvaluation">-->
    <!--UPDATE pes_cs_service_evaluation-->
    <!--<set>-->
    <!--<if test="shopId != null">-->
    <!--shop_id = #{shopId,jdbcType=BIGINT},-->
    <!--</if>-->
    <!--<if test="csNick != null">-->
    <!--cs_nick = #{csNick,jdbcType=VARCHAR},-->
    <!--</if>-->
    <!--<if test="buyerNick != null">-->
    <!--buyer_nick = #{buyerNick,jdbcType=VARCHAR},-->
    <!--</if>-->
    <!--<if test="sendTime != null">-->
    <!--send_time = #{sendTime,jdbcType=TIMESTAMP},-->
    <!--</if>-->
    <!--<if test="evalTime != null">-->
    <!--eval_time = #{evalTime,jdbcType=TIMESTAMP},-->
    <!--</if>-->
    <!--<if test="evalCode != null">-->
    <!--eval_code = #{evalCode,jdbcType=INTEGER},-->
    <!--</if>-->
    <!--</set>-->
    <!--WHERE id = #{id,jdbcType=BIGINT}-->
    <!--</update>-->

    <select id="searchAllDateShopGroup" resultMap="CsServiceEvaluationDTO">
        select
        <include refid="field_for_performance"/>
        from ${tableName}
        where date between #{startDate} and #{endDate}
        <if test="nicks!=null and nicks.size()>0">
            and cs_nick in
            <foreach collection="nicks" item="nick" close=")" open="(" separator=",">
                #{nick}
            </foreach>
        </if>
        <if test="filterDates != null and filterDates.size()>0">
            and date not in
            <foreach collection="filterDates" open="(" close=")" separator="," item="d">
                #{d}
            </foreach>
        </if>
        and shop_id = #{shopId}
        order by date desc
    </select>

    <!--  客服邀评数量 -->
    <!--
         <select id="selectCsNickServiceEvalBySendTimeCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
          SELECT  cs_nick csNick,count(send_time) as count
            FROM ${tableName}
            <where>
                <if test="csNickLst!=null and csNickLst.size()>0">
                     cs_nick in
                      <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                          #{csNick}
                      </foreach>
                </if>
                And send_time BETWEEN #{startDate} AND #{endDate}
                AND shop_id=#{shopId}
                AND send_time is not null
            </where>
            GROUP BY DATE_FORMAT(send_time,'%Y-%m-%d'),cs_nick
      </select>
    -->
    <!-- 客服对客户评价的数量 -->
    <!--
      <select id="selectCsNickServiceEvalByEvalTimeCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
          SELECT cs_nick csNick, count(eval_time) as count
            FROM ${tableName}
            <where>
                <if test="csNickLst!=null and csNickLst.size()>0">
                     cs_nick in
                      <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                          #{csNick}
                      </foreach>
                </if>
                AND  eval_time BETWEEN #{startDate} AND #{endDate}
                AND shop_id=#{shopId}
                AND eval_time is not null
            </where>
            GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick
      </select>
    -->
    <!-- 评价满意的数量 -->
    <select id="selectCsNickServiceEvalSatisfactionCount" parameterType="map"
            resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
        SELECT cs_nick csNick, count(eval_time) as count
        FROM ${tableName}
        <where>
            <if test="csNickLst!=null and csNickLst.size()>0">
                cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
            </if>
            AND eval_time BETWEEN #{startDate} AND #{endDate}
            AND shop_id=#{shopId}
            AND (eval_code = 100 or eval_code = 75)
            AND eval_time is not null
        </where>
        GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick
    </select>


    <select id="selectByShopIdAndDateForShopPerformance" resultType="com.pes.jd.model.DTO.CsServiceEvaluationDTO">
        SELECT
        shop_id,
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                `date`, `date` AS dateStr,
            </when>
            <!--月份-->
            <when test="dateType == 2">
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        SUM( `eval_send_num` ) `eval_send_num`,
        SUM( `eval_reply_num` ) `eval_reply_num`,
        SUM( `very_satisfied_num` ) `very_satisfied_num`,
        SUM( `satisfied_num` ) `satisfied_num`,
        SUM( `general_num` ) `general_num`,
        SUM( `dissatisfied_num` ) `dissatisfied_num`,
        SUM( `very_dissatisfied_num` ) `very_dissatisfied_num`
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY `date`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>

</mapper>