<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.JobRecordMapper" >

  <resultMap id="JobPullRecordDTO" type="com.pes.jd.model.DTO.JobPullRecordDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_peer_flag" jdbcType="BIT" property="chatPeerFlag" />
    <result column="shop_category_flag" jdbcType="BIT" property="shopCategoryFlag" />
    <result column="shop_sku_flag" jdbcType="BIT" property="shopSkuFlag" />
    <result column="shop_good_flag" jdbcType="BIT" property="shopGoodFlag" />
    <result column="shop_dsr_flag" jdbcType="BIT" property="shopDsrFlag" />
    <result column="no_pay_order_flag" jdbcType="BIT" property="noPayOrderFlag" />
    <result column="order_created_flag" jdbcType="BIT" property="orderCreatedFlag" />
    <result column="order_modify_flag" jdbcType="BIT" property="orderModifyFlag" />
    <result column="order_presale_flag" jdbcType="BIT" property="orderPresaleFlag" />
    <result column="order_evaluation_flag" jdbcType="BIT" property="orderEvaluationFlag" />
    <result column="shop_pv_uv_flag" jdbcType="BIT" property="shopPvUvFlag" />
    <result column="order_refund_apply_flag" jdbcType="BIT" property="orderRefundApplyFlag" />
    <result column="order_refund_check_flag" jdbcType="BIT" property="orderRefundCheckFlag" />
    <result column="asc_order_refund_apply_flag" jdbcType="BIT" property="ascOrderRefundApplyFlag" />
    <result column="asc_order_refund_check_flag" jdbcType="BIT" property="ascOrderRefundCheckFlag" />
    <result column="leave_msg_flag" jdbcType="BIT" property="leaveMsgFlag" />
    <result column="cs_send_eval_flag" jdbcType="BIT" property="csSendEvalFlag" />
    <result column="update_cs_eval_flag" jdbcType="BIT" property="updateCsEvalFlag" />
    <result column="cs_eval_flag" jdbcType="BIT" property="csEvalFlag" />
    <result column="order_remark_flag" jdbcType="BIT" property="orderRemarkFlag" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>


  <resultMap id="JobCalRecordDTO" type="com.pes.jd.model.DTO.JobCalRecordDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="common_chat_flag" jdbcType="BIT" property="commonChatFlag" />
    <result column="receive_quality_flag" jdbcType="BIT" property="receiveQualityFlag" />
    <result column="cs_order_index_flag" jdbcType="BIT" property="csOrderIndexFlag" />
    <result column="cs_order_bind_flag" jdbcType="BIT" property="csOrderBindFlag" />
    <result column="enquiry_chat_flag" jdbcType="BIT" property="enquiryChatFlag" />
    <result column="final_chat_data_flag" jdbcType="BIT" property="finalChatDataFlag" />
    <result column="enquiry_loss_flag" jdbcType="BIT" property="enquiryLossFlag" />
    <result column="cs_performance_flag" jdbcType="BIT" property="csPerformanceFlag" />
    <result column="cs_torder_performance" jdbcType="BIT" property="csTorderPerformance" />
    <result column="shop_day_overview_flag" jdbcType="BIT" property="shopDayOverviewFlag" />
    <result column="assit_index_flag" jdbcType="BIT" property="assitIndexFlag" />
    <result column="order_filte_flag" jdbcType="BIT" property="orderFilteFlag" />
    <result column="order_loss_flag" jdbcType="BIT" property="orderLossFlag" />
    <result column="outstock_loss_flag" jdbcType="BIT" property="outstockLossFlag" />
    <result column="team_loss_flag" jdbcType="BIT" property="teamLossFlag" />
    <result column="cs_order_eval_flag" jdbcType="BIT" property="csOrderEvalFlag" />
    <result column="cs_order_bind_index_flag" jdbcType="BIT" property="csOrderBindIndexFlag" />
    <result column="cs_goods_handle_flag" jdbcType="BIT" property="csGoodsHandleFlag" />
    <result column="cs_goods_sum_flag" jdbcType="BIT" property="csGoodsSumFlag" />
    <result column="cs_slient_sale_flag" jdbcType="BIT" property="csSlientSaleFlag" />
    <result column="cs_silent_goods_sum_flag" jdbcType="BIT" property="csSilentGoodsSumFlag" />
    <result column="team_day_refund_flag" jdbcType="BIT" property="teamDayRefundFlag" />
    <result column="shop_day_refund_flag" jdbcType="BIT" property="shopDayRefundFlag" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>

  <resultMap id="jobRecordVO" type="com.pes.jd.model.VO.JobRecordVO">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>

  <resultMap id="jobPullRecordVO" type="com.pes.jd.model.VO.JobPullRecordVO">
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="run_status" jdbcType="TINYINT" property="status" />
  </resultMap>


  <sql id="base_field_pull">
    id, shop_id, date, chat_peer_flag, shop_category_flag, shop_sku_flag, shop_good_flag,
    shop_dsr_flag, no_pay_order_flag, order_created_flag, order_modify_flag, order_presale_flag,
    order_evaluation_flag, shop_pv_uv_flag, order_refund_apply_flag, order_refund_check_flag,
    asc_order_refund_apply_flag, asc_order_refund_check_flag, leave_msg_flag, cs_send_eval_flag,
    update_cs_eval_flag, cs_eval_flag, order_remark_flag, result, modified, msg
  </sql>

  <sql id="base_field_cal">
    id, shop_id, date, result, common_chat_flag, receive_quality_flag, cs_order_index_flag,
    cs_order_bind_flag, enquiry_chat_flag, final_chat_data_flag, enquiry_loss_flag, cs_performance_flag,
    cs_torder_performance, shop_day_overview_flag, assit_index_flag, order_filte_flag,
    order_loss_flag, outstock_loss_flag, team_loss_flag, cs_order_eval_flag,
    cs_order_bind_index_flag, cs_goods_handle_flag, cs_goods_sum_flag, cs_silent_goods_sum_flag,
    team_day_refund_flag, shop_day_refund_flag, modified, msg
  </sql>

  <select id="searchJobPullRecordByDateAndTableName" parameterType="map" resultType="Long" >
    SELECT DISTINCT shop_id
    FROM ${jobPullRecordTableName}
    WHERE
      date = #{date,jdbcType=DATE} AND result=0
  </select>

  <select id="getJobPullShopNum" parameterType="map" resultType="java.lang.Integer" >
    SELECT count(1)
    FROM ${jobPullRecordTableName}
    WHERE
      date BETWEEN #{startDate} AND #{endDate}
  </select>

  <select id="getJobCalShopNum" parameterType="map" resultType="java.lang.Integer" >
    SELECT count(1)
    FROM ${jobCalRecordTableName}
    WHERE
      date BETWEEN #{startDate} AND #{endDate}
  </select>

  <select id="searchJobCalRecordByDateAndTableName" parameterType="map" resultType="Long">
    SELECT DISTINCT shop_id
    FROM ${jobCalRecordTableName}
    WHERE
      date = #{date,jdbcType=DATE} AND result=0
  </select>

  <select id="searchJobRecordByDateAndTableName" parameterType="map" resultMap="jobRecordVO">
    SELECT shop_id,date,modified,msg
    FROM ${jobRecordTableName}
    WHERE
      date = #{date,jdbcType=DATE} AND result=0
    <if test="handleFailShopJob !=null and handleFailShopJob">
      and run_status = 4
    </if>
  </select>

  <select id="searchJobRecordShopByTypeAndShopIdLst" parameterType="map" resultMap="jobRecordVO">
    SELECT shop_id,date,modified,msg
    FROM ${jobRecordTableName}
    WHERE
      date = #{date,jdbcType=DATE} AND result=0
      AND shop_id in
        <foreach collection="shopIdLst" open="(" close=")" separator="," item="shopId">
        #{shopId}
        </foreach>
  </select>

  <update id="cleanJobPullRecord" parameterType="map">
    UPDATE ${jobRecordTableName}
    <set>
        run_status = 5
    </set>
    WHERE
        date = #{date,jdbcType=DATE}
        AND run_status = 1
        AND shop_id in
        <foreach collection="shopIdLst" open="(" close=")" separator="," item="shopId">
          #{shopId}
        </foreach>
  </update>


  <select id="searchJobPullRecord" parameterType="map" resultMap="jobPullRecordVO">
    SELECT shop_id,date,modified,msg,run_status
    FROM ${jobRecordTableName}
    WHERE
      date = #{date,jdbcType=DATE}
      <if test="status != -1">
        AND run_status = #{status,jdbcType=TINYINT}
      </if>
      AND shop_id in
      <foreach collection="shopIdLst" open="(" close=")" separator="," item="shopId">
        #{shopId}
      </foreach>
  </select>

</mapper>