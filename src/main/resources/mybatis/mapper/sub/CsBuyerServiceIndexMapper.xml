<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsBuyerServiceIndexMapper">

  <resultMap id="CsBuyerServiceIndexDTO" type="com.pes.jd.model.DTO.CsBuyerServiceIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="avg_wait_time_first" jdbcType="DOUBLE" property="avgWaitTimeFirst" />
    <result column="avg_wait_time" jdbcType="DOUBLE" property="avgWaitTime" />
    <result column="session_time" jdbcType="BIGINT" property="sessionTime" />
    <result column="cs_reply_num" jdbcType="INTEGER" property="csReplyNum" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
    <result column="is_non_reply" jdbcType="BIT" property="isNonReply" />
    <result column="first_receive_date" jdbcType="TIMESTAMP" property="firstReceiveDate" />
     <result column="last_receive_date" jdbcType="TIMESTAMP" property="lastReceiveDate" />
  </resultMap>
  
  
  
  <sql id="base_field">
    shop_id, date, cs_nick, buyer_nick, avg_wait_time_first, avg_wait_time, session_time, 
    cs_reply_num, buyer_chat_num, is_non_reply,first_receive_date,last_receive_date
  </sql>
  
  <insert id="batchInsertCsBuyerServiceIndex" parameterType="map">
    INSERT INTO ${tableName} (shop_id, date, 
      cs_nick, buyer_nick, avg_wait_time_first, 
      avg_wait_time, session_time, cs_word_num, cs_reply_num, 
      buyer_chat_num, is_non_reply,first_receive_date,last_receive_date)
    VALUES
    <foreach collection="csBuyerServiceIndexLst" item="itm" separator=",">
     (
    	#{itm.shopId,jdbcType=BIGINT}, 
    	#{itm.date,jdbcType=DATE}, 
      	#{itm.csNick,jdbcType=VARCHAR}, 
      	#{itm.buyerNick,jdbcType=VARCHAR}, 
      	#{itm.avgWaitTimeFirst,jdbcType=DOUBLE}, 
      	#{itm.avgWaitTime,jdbcType=DOUBLE}, 
      	#{itm.sessionTime,jdbcType=BIGINT}, 
      	#{itm.csWordNum,jdbcType=INTEGER}, 
      	#{itm.csReplyNum,jdbcType=INTEGER}, 
      	#{itm.buyerChatNum,jdbcType=INTEGER}, 
      	#{itm.isNonReply,jdbcType=BIT}
      	#{itm.firstReceiveDate,jdbcType=TIMESTAMP}
      	#{itm.lastReceiveDate,jdbcType=TIMESTAMP}
      )
    </foreach> 
    
  </insert>
  
  <delete id="deleteShopCsBuyerServiceIndexByDate" parameterType="map">
    DELETE FROM 
    	${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>

  <update id="updateCsBuyerServiceIndex" parameterType="map">
    UPDATE ${tableName}
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="buyerNick != null">
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="avgWaitTimeFirst != null">
        avg_wait_time_first = #{avgWaitTimeFirst,jdbcType=DOUBLE},
      </if>
      <if test="avgWaitTime != null">
        avg_wait_time = #{avgWaitTime,jdbcType=DOUBLE},
      </if>
      <if test="sessionTime != null">
        session_time = #{sessionTime,jdbcType=BIGINT},
      </if>
      <if test="csReplyNum != null">
        cs_reply_num = #{csReplyNum,jdbcType=INTEGER},
      </if>
      <if test="buyerChatNum != null">
        buyer_chat_num = #{buyerChatNum,jdbcType=INTEGER},
      </if>
      <if test="isNonReply != null">
        is_non_reply = #{isNonReply,jdbcType=BIT},
      </if>
    </set>
    WHERE 
    	 buyer_nick = #{buyerNick,jdbcType=VARCHAR}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND shop_id = #{shopId,jdbcType=BIGINT}
  </update>

	<select id="selectCsBuyerServiceByShopIdByDate" parameterType="map" resultMap="CsBuyerServiceIndexDTO">
	 SELECT *
			FROM ${tableName}
			WHERE 
			date BETWEEN #{startDate} AND #{endDate}
			AND shop_id = #{shopId} 
</select>
    
    <select id="selectPerformanceData" resultType="com.pes.jd.model.DTO.NickPerformanceDTO">
        SELECT
        shop_id shopId,cs_nick nick,date,
        sum(cs_reply_num) csBuyerReplyNum,
        count(1) csBuyerDataSize,
        sum(session_time) csBuyerSessionTime
        FROM ${tableName}
        <where>
            AND shop_id IN
            <foreach collection="nicks" item="item" separator="," open="(" close=")">
                #{item.shopId}
            </foreach>
            AND date BETWEEN #{startDate} AND #{endDate}
            AND cs_nick IN
            <foreach collection="nicks" item="item" separator="," open="(" close=")">
                #{item.nick}
            </foreach>
            GROUP BY ${groupBy}
        </where>
    </select>
    
    <select id="selectDetail" resultType="map" parameterType="map">
    SELECT
    buyer_nick buyerNick, date
    FROM ${tableName}
    WHERE
    date BETWEEN #{begin} AND #{end}
    AND cs_nick = #{nick}
    AND shop_id = #{shopId}
    AND is_non_reply = 1
    </select>
   
   <!-- 接待未邀评分析数据count -->
     <select id="selectReceiveUnSendEvalCount" resultType="Integer">
    	SELECT
			count(chat.sid)
		FROM (select * from (<foreach collection="csBuyerServiceTables" item="table" separator="union">
			SELECT date,cs_nick,customer,shop_id,sid,
			session_begin_time,session_end_time,
			begin_datetime,end_datetime
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickLst!=null and csNickLst.size>0">
    				and cs_nick in
	    			<foreach collection="csNickLst" item = "nick" open = "(" close = ")" separator=",">
	    				#{nick}
	    			</foreach>
    			</if>
    			<if test="buyerNick!=null and buyerNick!=''">
					and customer = #{buyerNick}
				</if>
				and is_receive = 1
				and date BETWEEN #{startDate} and #{endDate}
			</where>
		</foreach>
		) ch
		<where>
			not EXISTS (select sid from 
					(<foreach collection="sendEvalTables" item = "table" separator="union">
						SELECT sid
						FROM ${table.getTableName}
						<where>
							<if test="shopId!=null">
								shop_id = #{shopId}
							</if>
							<if test="csNickLst!=null and csNickLst.size>0">
			    				and cs_nick in
				    			<foreach collection="csNickLst" item = "nick" open = "(" close = ")" separator=",">
				    				#{nick}
				    			</foreach>
			    			</if>
							and send_time BETWEEN #{startDate} and #{endDate}
						</where>
					</foreach>) send where send.sid = ch.sid ) 
		</where>) chat
		left join 
		(<foreach collection="evalDetailTables" item="table" separator="union">
			select cs_nick,shop_id,buyer_nick,eval_time,sid
			 from ${table.getTableName}
			 <where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickLst!=null and csNickLst.size>0">
    				and cs_nick in
	    			<foreach collection="csNickLst" item = "nick" open = "(" close = ")" separator=",">
	    				#{nick}
	    			</foreach>
    			</if>
				and eval_time BETWEEN #{startDate} and #{endDate}
			</where>
		</foreach>) eval on chat.sid = eval.sid
					<where>
						<if test="buyerEvalInit==1">
							and eval.eval_time is not null
						</if>
						<if test="buyerEvalInit==2">
							and eval.eval_time is null
						</if>
					</where>
    </select>
    
    
    <!-- 接待未邀评分析查询 -->
    <select id="selectReceiveUnSendEvalByDateByCsNickByBuyer" resultType="com.pes.jd.model.DTO.ReceiveUnSendDTO">
    	SELECT
			chat.date,chat.shop_id,chat.customer as buyer_nick,chat.cs_nick,
			chat.session_begin_time as sessionBeginDateTime,chat.session_end_time as sessionEndDateTime,
			eval.eval_time,
			chat.sid,chat.begin_datetime beginDateTime,chat.end_datetime endDateTime,
			(case when chat.sid = eval.sid then '是'
				else '否' end) as buyerEvalInit
		FROM (select * from (<foreach collection="csBuyerServiceTables" item="table" separator="union">
			SELECT date,cs_nick,customer,shop_id,sid,
			session_begin_time,session_end_time,
			begin_datetime,end_datetime
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickLst!=null and csNickLst.size>0">
    				and cs_nick in
	    			<foreach collection="csNickLst" item = "nick" open = "(" close = ")" separator=",">
	    				#{nick}
	    			</foreach>
    			</if>
    			<if test="buyerNick!=null and buyerNick!=''">
					and customer = #{buyerNick}
				</if>
				and is_receive = 1
				and date BETWEEN #{startDate} and #{endDate}
			</where>
		</foreach>
		) ch
		<where>
			not EXISTS (select sid from 
					(<foreach collection="sendEvalTables" item = "table" separator="union">
						SELECT sid
						FROM ${table.getTableName}
						<where>
							<if test="shopId!=null">
								shop_id = #{shopId}
							</if>
							<if test="csNickLst!=null and csNickLst.size>0">
			    				and cs_nick in
				    			<foreach collection="csNickLst" item = "nick" open = "(" close = ")" separator=",">
				    				#{nick}
				    			</foreach>
			    			</if>
							and send_time BETWEEN #{startDate} and #{endDate}
						</where>
					</foreach>) send where send.sid = ch.sid ) 
		</where>) chat
		left join 
		(<foreach collection="evalDetailTables" item="table" separator="union">
			select cs_nick,shop_id,buyer_nick,eval_time,sid
			 from ${table.getTableName}
			 <where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickLst!=null and csNickLst.size>0">
    				and cs_nick in
	    			<foreach collection="csNickLst" item = "nick" open = "(" close = ")" separator=",">
	    				#{nick}
	    			</foreach>
    			</if>
				and eval_time BETWEEN #{startDate} and #{endDate}
			</where>
		</foreach>) eval on chat.sid = eval.sid
					<where>
						<if test="buyerEvalInit==1">
							and eval.eval_time is not null
						</if>
						<if test="buyerEvalInit==2">
							and eval.eval_time is null
						</if>
					</where>
		<if test="sortPageQuery.sort">
   			order by chat.${sortPageQuery.field} ${sortPageQuery.sortDirection}
	   	</if>
	   	 <if test="sortPageQuery.currentPage != null and sortPageQuery.size != 0">
	        LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
	     </if>
    </select>
    
</mapper>