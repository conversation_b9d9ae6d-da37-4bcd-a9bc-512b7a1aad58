<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.ShopGoodsSkuLabelMapper">
  <resultMap id="ShopGoodsSkuLabelDO" type="com.pes.jd.model.DO.ShopGoodsSkuLabelDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
  </resultMap>
  <resultMap id="ShopGoodsSkuLabelDTO" type="com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
  </resultMap>

  <sql id="Base_Column_List">
    id, shop_id, sku_id, label
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ShopGoodsSkuLabelDO">
    select 
    <include refid="Base_Column_List" />
    from pes_shop_goods_sku_label_2019_05
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="searchBySkuIds" resultMap="ShopGoodsSkuLabelDTO">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where
        shop_id = #{shopId} and
         sku_id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>

    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ${tableName}
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ShopGoodsSkuLabelDO" keyProperty="entity.id" useGeneratedKeys="true">
    insert into ${tableName} (id, shop_id, sku_id,
      label)
    values (#{entity.id,jdbcType=BIGINT}, #{entity.shopId,jdbcType=BIGINT}, #{entity.skuId,jdbcType=BIGINT},
      #{entity.label,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopGoodsSkuLabelDO">
    insert into pes_shop_goods_sku_label_2019_05
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="label != null">
        label,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopGoodsSkuLabelDO">
    update pes_shop_goods_sku_label_2019_05
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO">
    update ${tableName}
    set shop_id = #{record.shopId} ,
      sku_id = #{record.skuId},
      label = #{record.label}
    where id = #{record.id}
  </update>
    <select id="searchByShopId" resultType="java.lang.Long">
        select  distinct sku_id
        from ${tableName}
        where shop_id=#{shopId}
    </select>
    <insert id="insertSkuLabelLst">
        insert into ${tableName} (id, shop_id, sku_id,label)
        values
        <foreach collection="gkLst" item="gk" index="index" separator=",">
            (#{gk.id},
            #{gk.shopId},
            #{gk.skuId},
            #{gk.label})
        </foreach>
    </insert>
    <update id="updateSkuLabelByShopIdAndSkuIdsAndLabel">
        update ${tableName}
        set shop_id = #{shopId,jdbcType=BIGINT},label = #{newLabel,jdbcType=VARCHAR}
        where shop_id = #{shopId,jdbcType=BIGINT} and sku_id in
        <foreach collection="skuIds" item="sku" open="(" separator="," close=")">
            #{sku,jdbcType=BIGINT}
        </foreach>
        and label = #{oldLabel,jdbcType=VARCHAR}
    </update>
    <delete id="deleteSKuLabelByShopIdAndSkuIdsAndLabel">
        delete from ${tableName}
        where shop_id = #{shopId,jdbcType=BIGINT} and sku_id in
        <foreach collection="skuIds" item="sku" open="(" separator="," close=")">
            #{sku,jdbcType=BIGINT}
        </foreach>
        and label = #{label,jdbcType=VARCHAR}
    </delete>
    <select id="searchGoodsLabelBySkuIds" resultType="java.lang.Long">
        select
        sku_id
        from ${tableName}
        where
        shop_id = #{shopId}
        and
        sku_id in
        <foreach collection="skuIds" item="sku" open="(" separator="," close=")">
            #{sku,jdbcType=BIGINT}
        </foreach>
        and
        label = #{label}
    </select>
</mapper>