<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ChatClassifyMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.entity.ChatClassify" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sid" property="sid" jdbcType="VARCHAR" />
    <result column="chatlog_id" property="chatlogId" jdbcType="BIGINT" />
    <result column="classify" property="classify" jdbcType="VARCHAR" />
    <result column="classify_extra" property="classifyExtra" jdbcType="VARCHAR" />
    <result column="buyer_content" property="buyerContent" jdbcType="VARCHAR" />
    <result column="cs_content" property="csContent" jdbcType="VARCHAR" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="total_tokens" property="totalTokens" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, shop_id, date, sid, chatlog_id, classify, classify_extra, buyer_content, cs_content,
    sku_id, total_tokens, created
  </sql>

  <sql id="No_content_Column_List" >
    id, shop_id, date, sid, chatlog_id, classify, classify_extra, sku_id, total_tokens, created
  </sql>

  <select id="queryClassifyByShopAndDate" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date = #{date}
    ORDER BY id DESC
  </select>

  <select id="queryClassifyByShopAndDateRange" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date BETWEEN #{startDate} AND #{endDate}
    ORDER BY id DESC
  </select>

  <select id="queryClassifyByShopAndClassify" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date = #{date}
    AND classify = #{classify}
    ORDER BY id DESC
  </select>

  <select id="queryClassifyBySidsAndDateRange" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date BETWEEN #{startDate} AND #{endDate}
    AND sid IN
    <foreach collection="sids" item="sid" open="(" separator="," close=")">
      #{sid}
    </foreach>
    ORDER BY id DESC
  </select>

  <!-- Query classify by shop_id, date range and classify type -->
  <select id="queryClassifyByShopAndDateRangeAndClassify" resultMap="BaseResultMap">
    SELECT
    <include refid="No_content_Column_List" />
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date BETWEEN #{startDate} AND #{endDate}
    AND classify = #{classify}
    ORDER BY id DESC
  </select>

  <!-- Define a result map for CsServiceEvaluationDetail -->
  <resultMap id="EvaluationResultMap" type="com.pes.jd.model.DO.CsServiceEvaluationDetail">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="eval_time" property="evalTime" jdbcType="TIMESTAMP" />
    <result column="eval_code" property="evalCode" jdbcType="INTEGER" />
    <result column="sid" property="sid" jdbcType="VARCHAR" />
  </resultMap>

  <!-- Query low-rated evaluations by date range and shop ID -->
  <select id="queryLowRatedEvaluationsByDateRange" resultMap="EvaluationResultMap">
    SELECT
    id, shop_id, cs_nick, buyer_nick, eval_time, eval_code, sid
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND eval_time BETWEEN #{startDate} AND #{endDate}
    AND eval_code &lt; 100
    ORDER BY eval_code
  </select>

  <select id="queryLowRatedEvaluationsByDateRangeAndScore" resultMap="EvaluationResultMap">
    SELECT
      id, eval_code, sid
    FROM ${tableName}
    WHERE shop_id = #{shopId}
      AND eval_time BETWEEN #{startDate} AND #{endDate}
      AND eval_code = #{score}
  </select>


  <!-- Query all evaluations by shop_id, date range and list of SIDs -->
  <select id="queryAllEvaluationsBySidsAndDateRange" resultMap="EvaluationResultMap">
    SELECT
    id, shop_id, cs_nick, buyer_nick, eval_time, eval_code, sid
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND eval_time BETWEEN #{startDate} AND #{endDate}
    AND sid IN
    <foreach collection="sids" item="sid" open="(" separator="," close=")">
      #{sid}
    </foreach>
    ORDER BY eval_time
  </select>

  <!-- Query classify by shop_id, classify, classifyExtra and date range -->
  <select id="queryClassifyByShopAndClassifyAndClassifyExtraAndDateRange" resultMap="BaseResultMap">
    SELECT
    shop_id, date, sid, chatlog_id, classify, classify_extra, sku_id, created, buyer_content
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date BETWEEN #{startDate} AND #{endDate}
    AND classify = #{classify}
    <if test="classifyExtra != null and classifyExtra != ''">
      AND classify_extra = #{classifyExtra}
    </if>
    ORDER BY id DESC
  </select>



  <select id="queryClassifyByShopAndClassifyAndClassifyExtraAndDateRangeAndSids" resultMap="BaseResultMap">
    SELECT
    shop_id, date, sid, chatlog_id, classify, classify_extra, sku_id, created, buyer_content
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND classify = #{classify}
    <if test="classifyExtra != null and classifyExtra != ''">
      AND classify_extra = #{classifyExtra}
    </if>
    <if test="sids != null and sids.size() > 0">
      AND sid IN
      <foreach collection="sids" item="sid" open="(" separator="," close=")">
        #{sid}
      </foreach>
    </if>
    ORDER BY id DESC
  </select>


  <!-- Define a result map for CsChatSessionDO -->
  <resultMap id="ChatSessionResultMap" type="com.pes.jd.model.DO.CsChatSessionDO">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sid" property="sid" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="session_begin_time" property="sessionBeginTime" jdbcType="TIMESTAMP" />
    <result column="session_end_time" property="sessionEndTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <!-- Query chat session by shopId, sid, and date range -->
  <select id="queryChatSessionByShopIdAndSidAndDateRange" resultMap="ChatSessionResultMap">
    SELECT
     sid, shop_id, date, cs_nick, customer, session_begin_time, session_end_time
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND date BETWEEN #{startDate} AND #{endDate}
    <if test="sid != null and sid != ''">
      AND sid = #{sid}
    </if>
    ORDER BY session_begin_time
  </select>

  <!-- Define a result map for CsChatlog -->
  <resultMap id="ChatlogResultMap" type="com.pes.jd.model.DO.CsChatlog">
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer" property="buyer" jdbcType="VARCHAR" />
    <result column="time" property="time" jdbcType="TIMESTAMP" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="direction" property="direction" jdbcType="BIT" />
    <result column="length" property="length" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="BIT" />
  </resultMap>

  <!-- Query chatlogs by shopId and time range -->
  <select id="queryChatlogsByShopIdAndTimeRange" resultMap="ChatlogResultMap">
    SELECT
    id, shop_id, cs_nick, buyer, time, content, direction, length, type
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND time BETWEEN #{startTime} AND #{endTime}
    ORDER BY time
  </select>



  <select id="querySessionIdByDateRange" resultType="com.pes.jd.model.entity.ChatClassify">

    SELECT
      Distinct sid
    FROM ${tableName}
    WHERE shop_id = #{shopId}
      AND date BETWEEN #{startDate} AND #{endDate}

  </select>
</mapper>
