<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsTorderPerformanceMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsTorderPerformanceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="to_ordered_num" jdbcType="INTEGER" property="toOrderedNum" />
    <result column="to_ordered_order_num" jdbcType="INTEGER" property="toOrderedOrderNum" />
    <result column="to_ordered_goods_num" jdbcType="INTEGER" property="toOrderedGoodsNum" />
    <result column="to_ordered_amount" jdbcType="DOUBLE" property="toOrderedAmount" />
    <result column="to_ordered_paid_num_today" jdbcType="INTEGER" property="toOrderedPaidNumToday" />
    <result column="to_ordered_paid_order_num_today" jdbcType="INTEGER" property="toOrderedPaidOrderNumToday" />
    <result column="to_ordered_paid_goods_today" jdbcType="INTEGER" property="toOrderedPaidGoodsToday" />
    <result column="to_ordered_paid_amount_today" jdbcType="DOUBLE" property="toOrderedPaidAmountToday" />
    <result column="to_ordered_paid_num_final" jdbcType="INTEGER" property="toOrderedPaidNumFinal" />
    <result column="to_ordered_paid_order_num_final" jdbcType="INTEGER" property="toOrderedPaidOrderNumFinal" />
    <result column="to_ordered_paid_goods_final" jdbcType="INTEGER" property="toOrderedPaidGoodsFinal" />
    <result column="to_ordered_paid_amount_final" jdbcType="DOUBLE" property="toOrderedPaidAmountFinal" />
    <result column="to_ordered_out_stock_num" jdbcType="INTEGER" property="toOrderedOutStockNum" />
    <result column="to_ordered_out_stock_amount" jdbcType="DOUBLE" property="toOrderedOutStockAmount" />
    <result column="to_ordered_out_stock_goods_num" jdbcType="INTEGER" property="toOrderedOutStockGoodsNum" />
    <result column="to_ordered_out_stock_order_num" jdbcType="INTEGER" property="toOrderedOutStockOrderNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
    <result column="sale_sku_num" jdbcType="INTEGER" property="saleSkuNum" />
    <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
    <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
    <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
    <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
    <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
    <result column="cfm_goods_order_num" jdbcType="INTEGER" property="cfmGoodsOrderNum" />
    <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
    <result column="cfm_goods_num" jdbcType="INTEGER" property="cfmGoodsNum" />
    <result column="cfm_goods_buyer_num" jdbcType="INTEGER" property="cfmGoodsBuyerNum" />
  </resultMap>
  <resultMap id="CsTorderPerformanceDTO" type="com.pes.jd.model.DTO.CsTorderPerformanceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="to_ordered_num" jdbcType="INTEGER" property="toOrderedNum" />
    <result column="to_ordered_order_num" jdbcType="INTEGER" property="toOrderedOrderNum" />
    <result column="to_ordered_goods_num" jdbcType="INTEGER" property="toOrderedGoodsNum" />
    <result column="to_ordered_amount" jdbcType="DOUBLE" property="toOrderedAmount" />
    <result column="to_ordered_paid_num_today" jdbcType="INTEGER" property="toOrderedPaidNumToday" />
    <result column="to_ordered_paid_order_num_today" jdbcType="INTEGER" property="toOrderedPaidOrderNumToday" />
    <result column="to_ordered_paid_goods_today" jdbcType="INTEGER" property="toOrderedPaidGoodsToday" />
    <result column="to_ordered_paid_amount_today" jdbcType="DOUBLE" property="toOrderedPaidAmountToday" />
    <result column="to_ordered_paid_num_final" jdbcType="INTEGER" property="toOrderedPaidNumFinal" />
    <result column="to_ordered_paid_order_num_final" jdbcType="INTEGER" property="toOrderedPaidOrderNumFinal" />
    <result column="to_ordered_paid_goods_final" jdbcType="INTEGER" property="toOrderedPaidGoodsFinal" />
    <result column="to_ordered_paid_amount_final" jdbcType="DOUBLE" property="toOrderedPaidAmountFinal" />
    <result column="to_ordered_out_stock_num" jdbcType="INTEGER" property="toOrderedOutStockNum" />
    <result column="to_ordered_out_stock_amount" jdbcType="DOUBLE" property="toOrderedOutStockAmount" />
    <result column="to_ordered_out_stock_goods_num" jdbcType="INTEGER" property="toOrderedOutStockGoodsNum" />
    <result column="to_ordered_out_stock_order_num" jdbcType="INTEGER" property="toOrderedOutStockOrderNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
    <result column="sale_sku_num" jdbcType="INTEGER" property="saleSkuNum" />
    <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
    <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
    <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
    <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
    <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
    <result column="cfm_goods_order_num" jdbcType="INTEGER" property="cfmGoodsOrderNum" />
    <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
    <result column="cfm_goods_num" jdbcType="INTEGER" property="cfmGoodsNum" />
    <result column="cfm_goods_buyer_num" jdbcType="INTEGER" property="cfmGoodsBuyerNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, to_ordered_num, to_ordered_order_num, to_ordered_goods_num, 
    to_ordered_amount, to_ordered_paid_num_today, to_ordered_paid_order_num_today, to_ordered_paid_goods_today, 
    to_ordered_paid_amount_today, to_ordered_paid_num_final, to_ordered_paid_order_num_final, 
    to_ordered_paid_goods_final, to_ordered_paid_amount_final, to_ordered_out_stock_num, 
    to_ordered_out_stock_amount, to_ordered_out_stock_goods_num, to_ordered_out_stock_order_num, 
    sale_amount, sale_order_num, sale_goods_num, sale_buyer_num, sale_sku_num, post_fee, 
    out_stock_num, out_stock_amount, out_stock_goods_num, out_stock_order_num, cfm_goods_order_num, 
    cfm_goods_amount, cfm_goods_num, cfm_goods_buyer_num
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_cs_torder_performance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchByDateShopNicks" resultMap="CsTorderPerformanceDTO">
    select
    <include refid="Base_Column_List"/>
    from ${tableName}
    where shop_id = #{shopId}
    <if test="nicks!=null and nicks.size()>0">
      AND cs_nick in
      <foreach collection="nicks" open="(" close=")" separator="," item="nick">
        #{nick}
      </foreach>
    </if>
    AND date between #{startDate} and #{endDate}
    <if test="filterDates != null and filterDates.size()>0">
      and date not in
      <foreach collection="filterDates" open="(" close=")" separator="," item="d">
        #{d}
      </foreach>
    </if>
    order by date desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_torder_performance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CsTorderPerformanceDO">
    insert into pes_cs_torder_performance (id, shop_id, date, 
      cs_nick, to_ordered_num, to_ordered_order_num, 
      to_ordered_goods_num, to_ordered_amount, to_ordered_paid_num_today, 
      to_ordered_paid_order_num_today, to_ordered_paid_goods_today, 
      to_ordered_paid_amount_today, to_ordered_paid_num_final, 
      to_ordered_paid_order_num_final, to_ordered_paid_goods_final, 
      to_ordered_paid_amount_final, to_ordered_out_stock_num, 
      to_ordered_out_stock_amount, to_ordered_out_stock_goods_num, 
      to_ordered_out_stock_order_num, sale_amount, sale_order_num, 
      sale_goods_num, sale_buyer_num, sale_sku_num, 
      post_fee, out_stock_num, out_stock_amount, 
      out_stock_goods_num, out_stock_order_num, cfm_goods_order_num, 
      cfm_goods_amount, cfm_goods_num, cfm_goods_buyer_num
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{toOrderedNum,jdbcType=INTEGER}, #{toOrderedOrderNum,jdbcType=INTEGER}, 
      #{toOrderedGoodsNum,jdbcType=INTEGER}, #{toOrderedAmount,jdbcType=DOUBLE}, #{toOrderedPaidNumToday,jdbcType=INTEGER}, 
      #{toOrderedPaidOrderNumToday,jdbcType=INTEGER}, #{toOrderedPaidGoodsToday,jdbcType=INTEGER}, 
      #{toOrderedPaidAmountToday,jdbcType=DOUBLE}, #{toOrderedPaidNumFinal,jdbcType=INTEGER}, 
      #{toOrderedPaidOrderNumFinal,jdbcType=INTEGER}, #{toOrderedPaidGoodsFinal,jdbcType=INTEGER}, 
      #{toOrderedPaidAmountFinal,jdbcType=DOUBLE}, #{toOrderedOutStockNum,jdbcType=INTEGER}, 
      #{toOrderedOutStockAmount,jdbcType=DOUBLE}, #{toOrderedOutStockGoodsNum,jdbcType=INTEGER}, 
      #{toOrderedOutStockOrderNum,jdbcType=INTEGER}, #{saleAmount,jdbcType=DOUBLE}, #{saleOrderNum,jdbcType=INTEGER}, 
      #{saleGoodsNum,jdbcType=INTEGER}, #{saleBuyerNum,jdbcType=INTEGER}, #{saleSkuNum,jdbcType=INTEGER}, 
      #{postFee,jdbcType=DOUBLE}, #{outStockNum,jdbcType=INTEGER}, #{outStockAmount,jdbcType=DOUBLE}, 
      #{outStockGoodsNum,jdbcType=INTEGER}, #{outStockOrderNum,jdbcType=INTEGER}, #{cfmGoodsOrderNum,jdbcType=INTEGER}, 
      #{cfmGoodsAmount,jdbcType=DOUBLE}, #{cfmGoodsNum,jdbcType=INTEGER}, #{cfmGoodsBuyerNum,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsTorderPerformanceDO">
    insert into pes_cs_torder_performance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="toOrderedNum != null">
        to_ordered_num,
      </if>
      <if test="toOrderedOrderNum != null">
        to_ordered_order_num,
      </if>
      <if test="toOrderedGoodsNum != null">
        to_ordered_goods_num,
      </if>
      <if test="toOrderedAmount != null">
        to_ordered_amount,
      </if>
      <if test="toOrderedPaidNumToday != null">
        to_ordered_paid_num_today,
      </if>
      <if test="toOrderedPaidOrderNumToday != null">
        to_ordered_paid_order_num_today,
      </if>
      <if test="toOrderedPaidGoodsToday != null">
        to_ordered_paid_goods_today,
      </if>
      <if test="toOrderedPaidAmountToday != null">
        to_ordered_paid_amount_today,
      </if>
      <if test="toOrderedPaidNumFinal != null">
        to_ordered_paid_num_final,
      </if>
      <if test="toOrderedPaidOrderNumFinal != null">
        to_ordered_paid_order_num_final,
      </if>
      <if test="toOrderedPaidGoodsFinal != null">
        to_ordered_paid_goods_final,
      </if>
      <if test="toOrderedPaidAmountFinal != null">
        to_ordered_paid_amount_final,
      </if>
      <if test="toOrderedOutStockNum != null">
        to_ordered_out_stock_num,
      </if>
      <if test="toOrderedOutStockAmount != null">
        to_ordered_out_stock_amount,
      </if>
      <if test="toOrderedOutStockGoodsNum != null">
        to_ordered_out_stock_goods_num,
      </if>
      <if test="toOrderedOutStockOrderNum != null">
        to_ordered_out_stock_order_num,
      </if>
      <if test="saleAmount != null">
        sale_amount,
      </if>
      <if test="saleOrderNum != null">
        sale_order_num,
      </if>
      <if test="saleGoodsNum != null">
        sale_goods_num,
      </if>
      <if test="saleBuyerNum != null">
        sale_buyer_num,
      </if>
      <if test="saleSkuNum != null">
        sale_sku_num,
      </if>
      <if test="postFee != null">
        post_fee,
      </if>
      <if test="outStockNum != null">
        out_stock_num,
      </if>
      <if test="outStockAmount != null">
        out_stock_amount,
      </if>
      <if test="outStockGoodsNum != null">
        out_stock_goods_num,
      </if>
      <if test="outStockOrderNum != null">
        out_stock_order_num,
      </if>
      <if test="cfmGoodsOrderNum != null">
        cfm_goods_order_num,
      </if>
      <if test="cfmGoodsAmount != null">
        cfm_goods_amount,
      </if>
      <if test="cfmGoodsNum != null">
        cfm_goods_num,
      </if>
      <if test="cfmGoodsBuyerNum != null">
        cfm_goods_buyer_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="toOrderedNum != null">
        #{toOrderedNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOrderNum != null">
        #{toOrderedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedGoodsNum != null">
        #{toOrderedGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedAmount != null">
        #{toOrderedAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedPaidNumToday != null">
        #{toOrderedPaidNumToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNumToday != null">
        #{toOrderedPaidOrderNumToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsToday != null">
        #{toOrderedPaidGoodsToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmountToday != null">
        #{toOrderedPaidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedPaidNumFinal != null">
        #{toOrderedPaidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNumFinal != null">
        #{toOrderedPaidOrderNumFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsFinal != null">
        #{toOrderedPaidGoodsFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmountFinal != null">
        #{toOrderedPaidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedOutStockNum != null">
        #{toOrderedOutStockNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOutStockAmount != null">
        #{toOrderedOutStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedOutStockGoodsNum != null">
        #{toOrderedOutStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOutStockOrderNum != null">
        #{toOrderedOutStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleAmount != null">
        #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="saleOrderNum != null">
        #{saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null">
        #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleBuyerNum != null">
        #{saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="saleSkuNum != null">
        #{saleSkuNum,jdbcType=INTEGER},
      </if>
      <if test="postFee != null">
        #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockNum != null">
        #{outStockNum,jdbcType=INTEGER},
      </if>
      <if test="outStockAmount != null">
        #{outStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="outStockGoodsNum != null">
        #{outStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockOrderNum != null">
        #{outStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsOrderNum != null">
        #{cfmGoodsOrderNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsAmount != null">
        #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="cfmGoodsNum != null">
        #{cfmGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsBuyerNum != null">
        #{cfmGoodsBuyerNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsTorderPerformanceDO">
    update pes_cs_torder_performance
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="toOrderedNum != null">
        to_ordered_num = #{toOrderedNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOrderNum != null">
        to_ordered_order_num = #{toOrderedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedGoodsNum != null">
        to_ordered_goods_num = #{toOrderedGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedAmount != null">
        to_ordered_amount = #{toOrderedAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedPaidNumToday != null">
        to_ordered_paid_num_today = #{toOrderedPaidNumToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNumToday != null">
        to_ordered_paid_order_num_today = #{toOrderedPaidOrderNumToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsToday != null">
        to_ordered_paid_goods_today = #{toOrderedPaidGoodsToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmountToday != null">
        to_ordered_paid_amount_today = #{toOrderedPaidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedPaidNumFinal != null">
        to_ordered_paid_num_final = #{toOrderedPaidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNumFinal != null">
        to_ordered_paid_order_num_final = #{toOrderedPaidOrderNumFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsFinal != null">
        to_ordered_paid_goods_final = #{toOrderedPaidGoodsFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmountFinal != null">
        to_ordered_paid_amount_final = #{toOrderedPaidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedOutStockNum != null">
        to_ordered_out_stock_num = #{toOrderedOutStockNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOutStockAmount != null">
        to_ordered_out_stock_amount = #{toOrderedOutStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedOutStockGoodsNum != null">
        to_ordered_out_stock_goods_num = #{toOrderedOutStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOutStockOrderNum != null">
        to_ordered_out_stock_order_num = #{toOrderedOutStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleAmount != null">
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="saleOrderNum != null">
        sale_order_num = #{saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null">
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleBuyerNum != null">
        sale_buyer_num = #{saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="saleSkuNum != null">
        sale_sku_num = #{saleSkuNum,jdbcType=INTEGER},
      </if>
      <if test="postFee != null">
        post_fee = #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockNum != null">
        out_stock_num = #{outStockNum,jdbcType=INTEGER},
      </if>
      <if test="outStockAmount != null">
        out_stock_amount = #{outStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="outStockGoodsNum != null">
        out_stock_goods_num = #{outStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockOrderNum != null">
        out_stock_order_num = #{outStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsOrderNum != null">
        cfm_goods_order_num = #{cfmGoodsOrderNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsAmount != null">
        cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="cfmGoodsNum != null">
        cfm_goods_num = #{cfmGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsBuyerNum != null">
        cfm_goods_buyer_num = #{cfmGoodsBuyerNum,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsTorderPerformanceDO">
    update pes_cs_torder_performance
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      to_ordered_num = #{toOrderedNum,jdbcType=INTEGER},
      to_ordered_order_num = #{toOrderedOrderNum,jdbcType=INTEGER},
      to_ordered_goods_num = #{toOrderedGoodsNum,jdbcType=INTEGER},
      to_ordered_amount = #{toOrderedAmount,jdbcType=DOUBLE},
      to_ordered_paid_num_today = #{toOrderedPaidNumToday,jdbcType=INTEGER},
      to_ordered_paid_order_num_today = #{toOrderedPaidOrderNumToday,jdbcType=INTEGER},
      to_ordered_paid_goods_today = #{toOrderedPaidGoodsToday,jdbcType=INTEGER},
      to_ordered_paid_amount_today = #{toOrderedPaidAmountToday,jdbcType=DOUBLE},
      to_ordered_paid_num_final = #{toOrderedPaidNumFinal,jdbcType=INTEGER},
      to_ordered_paid_order_num_final = #{toOrderedPaidOrderNumFinal,jdbcType=INTEGER},
      to_ordered_paid_goods_final = #{toOrderedPaidGoodsFinal,jdbcType=INTEGER},
      to_ordered_paid_amount_final = #{toOrderedPaidAmountFinal,jdbcType=DOUBLE},
      to_ordered_out_stock_num = #{toOrderedOutStockNum,jdbcType=INTEGER},
      to_ordered_out_stock_amount = #{toOrderedOutStockAmount,jdbcType=DOUBLE},
      to_ordered_out_stock_goods_num = #{toOrderedOutStockGoodsNum,jdbcType=INTEGER},
      to_ordered_out_stock_order_num = #{toOrderedOutStockOrderNum,jdbcType=INTEGER},
      sale_amount = #{saleAmount,jdbcType=DOUBLE},
      sale_order_num = #{saleOrderNum,jdbcType=INTEGER},
      sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      sale_buyer_num = #{saleBuyerNum,jdbcType=INTEGER},
      sale_sku_num = #{saleSkuNum,jdbcType=INTEGER},
      post_fee = #{postFee,jdbcType=DOUBLE},
      out_stock_num = #{outStockNum,jdbcType=INTEGER},
      out_stock_amount = #{outStockAmount,jdbcType=DOUBLE},
      out_stock_goods_num = #{outStockGoodsNum,jdbcType=INTEGER},
      out_stock_order_num = #{outStockOrderNum,jdbcType=INTEGER},
      cfm_goods_order_num = #{cfmGoodsOrderNum,jdbcType=INTEGER},
      cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      cfm_goods_num = #{cfmGoodsNum,jdbcType=INTEGER},
      cfm_goods_buyer_num = #{cfmGoodsBuyerNum,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <select id="selectCsSaleAmount" resultType="DOUBLE" >
    SELECT
    sum(sale_amount) as csSaleAmountForMonth
    FROM ${tableName}
    WHERE
    date BETWEEN #{startDate}	AND #{endDate}
    and shop_id=#{shopId}
  </select>
</mapper>