<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.SentimentAnalysisMapper" >

  <resultMap id="SentimentAnalysisDTO" type="com.pes.jd.model.DTO.SentimentAnalysisDTO" >
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="shop_id" jdbcType="BIGINT" property="shopId" />
      <result column="shop_title" jdbcType="VARCHAR" property="shopTitle" />
      <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
      <result column="customer" jdbcType="VARCHAR" property="customer" />
      <result column="sid" jdbcType="VARCHAR" property="sid" />
      <result column="warning_time" jdbcType="TIMESTAMP" property="warningTime" />
      <result column="warning_type" jdbcType="TINYINT" property="warningType" />
      <result column="keyword" jdbcType="VARCHAR" property="keyword" />
      <result column="content" jdbcType="VARCHAR" property="content" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="sentiment_type" jdbcType="TINYINT" property="sentimentType" />
      <result column="date" jdbcType="DATE" property="date" />
  </resultMap>

    <select id="selectSentimentAnalysisByShopAndDate" parameterType="map" resultMap="SentimentAnalysisDTO">
    SELECT * FROM ${tableName}
    WHERE 1=1
    <if test="nick !=null and nick != ''">
        and (shop_title like concat('%', #{nick}, '%') or shop_id like concat('%', #{nick}, '%'))
    </if>
    <if test="warningType != -1">
        and warning_type = #{warningType}
    </if>
    <if test="keyword !=null and keyword != ''">
        and keyword like concat ('%', #{keyword}, '%')
    </if>
    <if test="status != -1">
        and status = #{status}
    </if>
    AND warning_time BETWEEN #{startDate} AND #{endDate}
    order by status asc, warning_time desc
  </select>

    <update id="auditSentimentAnalysis" parameterType="map">
        update
            ${tableName}
        set sentiment_type = #{sentimentType}, status = 2
        where id = #{id}
    </update>

    <update id="batchAuditSentimentAnalysis" parameterType="map">
        update
            ${tableName}
        set sentiment_type = #{sentimentType}, status = 2
        where id in
        <foreach collection="idLst" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
  
</mapper>