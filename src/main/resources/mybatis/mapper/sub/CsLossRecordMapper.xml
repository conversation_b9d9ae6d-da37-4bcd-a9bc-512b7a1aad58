<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsLossRecordMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsLossRecordDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="customer_num" jdbcType="INTEGER" property="customerNum"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_sale_amount" jdbcType="DOUBLE" property="orderSaleAmount"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
    </resultMap>
    <resultMap id="CsLossRecordDTO" type="com.pes.jd.model.DTO.CsLossRecordDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="customer_num" jdbcType="INTEGER" property="customerNum"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_sale_amount" jdbcType="DOUBLE" property="orderSaleAmount"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, cs_nick, shop_id, date, order_num, customer_num, order_goods_num, order_sale_amount, 
    type
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_cs_loss_record
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="searchByDateShopNicks" resultMap="CsLossRecordDTO">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where shop_id = #{shopId}
        <if test="nicks!=null and nicks.size()>0">
            AND cs_nick in
            <foreach collection="nicks" open="(" close=")" separator="," item="nick">
                #{nick}
            </foreach>
        </if>
        <if test="filterDates != null and filterDates.size()>0">
            and date not in
            <foreach collection="filterDates" open="(" close=")" separator="," item="d">
                #{d}
            </foreach>
        </if>
        AND date between #{startDate} and #{endDate}
        order by date desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_loss_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.pes.jd.model.DO.CsLossRecordDO">
    insert into pes_cs_loss_record (id, cs_nick, shop_id, 
      date, order_num, customer_num, 
      order_goods_num, order_sale_amount, type
      )
    values (#{id,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, 
      #{date,jdbcType=DATE}, #{orderNum,jdbcType=INTEGER}, #{customerNum,jdbcType=INTEGER}, 
      #{orderGoodsNum,jdbcType=INTEGER}, #{orderSaleAmount,jdbcType=DOUBLE}, #{type,jdbcType=TINYINT}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsLossRecordDO">
        insert into pes_cs_loss_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="csNick != null">
                cs_nick,
            </if>
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="orderNum != null">
                order_num,
            </if>
            <if test="customerNum != null">
                customer_num,
            </if>
            <if test="orderGoodsNum != null">
                order_goods_num,
            </if>
            <if test="orderSaleAmount != null">
                order_sale_amount,
            </if>
            <if test="type != null">
                type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="csNick != null">
                #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="orderNum != null">
                #{orderNum,jdbcType=INTEGER},
            </if>
            <if test="customerNum != null">
                #{customerNum,jdbcType=INTEGER},
            </if>
            <if test="orderGoodsNum != null">
                #{orderGoodsNum,jdbcType=INTEGER},
            </if>
            <if test="orderSaleAmount != null">
                #{orderSaleAmount,jdbcType=DOUBLE},
            </if>
            <if test="type != null">
                #{type,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsLossRecordDO">
        update pes_cs_loss_record
        <set>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum,jdbcType=INTEGER},
            </if>
            <if test="customerNum != null">
                customer_num = #{customerNum,jdbcType=INTEGER},
            </if>
            <if test="orderGoodsNum != null">
                order_goods_num = #{orderGoodsNum,jdbcType=INTEGER},
            </if>
            <if test="orderSaleAmount != null">
                order_sale_amount = #{orderSaleAmount,jdbcType=DOUBLE},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsLossRecordDO">
    update pes_cs_loss_record
    set cs_nick = #{csNick,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      order_num = #{orderNum,jdbcType=INTEGER},
      customer_num = #{customerNum,jdbcType=INTEGER},
      order_goods_num = #{orderGoodsNum,jdbcType=INTEGER},
      order_sale_amount = #{orderSaleAmount,jdbcType=DOUBLE},
      type = #{type,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>