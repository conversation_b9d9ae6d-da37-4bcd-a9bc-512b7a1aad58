<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.JdAddressMapper">

    <select id="selectAllAddresses" resultType="com.pes.jd.model.DO.JdAddress">
        SELECT id, area_id, area_name, parent_id, level
        FROM pes_jd_address
        ORDER BY level, area_id
    </select>

</mapper>
