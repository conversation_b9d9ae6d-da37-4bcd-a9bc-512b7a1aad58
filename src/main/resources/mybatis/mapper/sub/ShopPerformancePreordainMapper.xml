<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.ShopPerformancePreordainMapper">

    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.ShopPerformancePreordainDTO">
        <id column="id" jdbcType="BIGINT" property="id"></id>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum"/>
        <result column="ordered_sku_num" jdbcType="INTEGER" property="orderedSkuNum"/>
        <result column="ordered_paid_buyer_num" jdbcType="INTEGER" property="orderedPaidBuyerNum"/>
        <result column="ordered_paid_sku_num" jdbcType="INTEGER" property="orderedPaidSkuNum"/>
        <result column="ordered_paid_amount" jdbcType="DOUBLE" property="orderedPaidAmount"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,shop_id,`date`,sku_id,sku_name,ordered_buyer_num,activity_id,
        ordered_sku_num,ordered_paid_buyer_num,ordered_paid_sku_num,ordered_paid_amount
    </sql>


    <select id="selectPerformancePreordainCycle" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM
        ${tableName}
        <trim prefix="where" prefixOverrides="and">
            shop_id = #{shopId}
            <if test="startDate!=null and endDate!=null">
                AND  `date` BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="activityId!=null">
                AND activity_id=#{activityId}
            </if>
            <if test="skuIds!=null and skuIds.size()>0">
                AND sku_id IN
                <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="sku!=null and sku!=''">
                AND ( sku_name=#{sku}
                OR sku_id=#{sku})
            </if>
        </trim>
    </select>


    <select id="selectPerformancePreordain" resultMap="BaseResultMap">
        SELECT `date`,sku_id,sku_name,activity_id,shop_id,
        SUM(ordered_buyer_num) AS ordered_buyer_num ,SUM(ordered_sku_num) AS ordered_sku_num,
        SUM(ordered_paid_buyer_num) AS  ordered_paid_buyer_num, SUM(ordered_paid_sku_num) AS ordered_paid_sku_num,
        SUM(ordered_paid_amount) AS ordered_paid_amount
        FROM
        ${tableName}
        <trim prefix="where" prefixOverrides="and">
            shop_id = #{shopId}
            <if test="startDate!=null and endDate!=null">
                AND `date`
                BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="activityId!=null">
                AND activity_id=#{activityId}
            </if>
            <if test="skuIds!=null and skuIds.size()>0">
                AND sku_id IN
                <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="sku!=null and sku!=''">
                AND ( sku_name LIKE concat('%',#{sku},"%")
                OR sku_id LIKE concat('%',#{sku},'%'))
            </if>
        </trim>
        GROUP By activity_id,sku_id
    </select>
    <select id="selectShopPerformancePreordainSkuDetailed" resultMap="BaseResultMap">

        SELECT <include refid="Base_Column_List"/>
        FROM
        ${tableName}
        <trim prefix="where" prefixOverrides="and">
            <if test="shopId!=null">
                shop_id = #{shopId}
            </if>
            <if test="startDate!=null and endDate!=null">
                AND  `date` BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="activityId!=null">
                AND  activity_id=#{activityId}
            </if>
            <if test="skuId!=null">
                AND sku_id=#{skuId}
            </if>
            <if test="skuName!=null">
                AND sku_name=#{skuName}
            </if>
        </trim>
        ORDER BY `date` DESC
    </select>
</mapper>