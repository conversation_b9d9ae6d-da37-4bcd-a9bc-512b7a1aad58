<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsrReplyQualityMapper" >
  <resultMap id="CsrReplyQualityDO" type="com.pes.jd.model.DO.CsrReplyQuality" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="avg_wait_time_first" property="avgWaitTimeFirst" jdbcType="DOUBLE" />
    <result column="avg_wait_time" property="avgWaitTime" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, cs_nick, avg_wait_time_first, avg_wait_time
  </sql>
  <select id="getCsrReplyQualityId" resultMap="CsrReplyQualityDO" parameterType="java.lang.Long" >
    SELECT 
    <include refid="base_field" />
    FROM pes_cs_reply_quality
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectCsrReplyQualityByCsNick" resultMap="CsrReplyQualityDO" parameterType="java.util.Map" >
    SELECT
    <foreach collection="columns" item="item" separator=",">
      ${item}
    </foreach>
    , cs_nick
    FROM pes_cs_reply_quality
    WHERE
    cs_nick in
    <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
      #{nick}
    </foreach>
  </select>

  <!-- for client performance -->
  <select id="selectCsrReplyQualityByShopIdNickSchema" resultType="java.util.Map" parameterType="map" >
    SELECT cs_nick nick,shop_id shopId,
    avg(avg_wait_time_first) avgResponseTime,
    avg(avg_wait_time) avgWaitTime FROM(
        SELECT cs_nick,shop_id,date,
        avg(avg_wait_time_first) avg_wait_time_first,
        avg(avg_wait_time) avg_wait_time
        FROM ${tableName}
        WHERE
        shop_id IN
        <foreach collection="nicks" item="item" separator="," open="(" close=")">
          #{item.shopId}
        </foreach>
          AND cs_nick IN
        <foreach collection="nicks" item="item" separator="," open="(" close=")">
          #{item.nick}
        </foreach>
        AND date BETWEEN #{startDate} AND #{endDate}
--         AND avg_wait_time !=0
        GROUP BY ${groupBy}
    ) tab GROUP BY ${groupBy}
  </select>

  <delete id="deleteCsrReplyQualityId" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_reply_quality
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertCsrReplyQuality" parameterType="com.pes.jd.model.DO.CsrReplyQuality" >
    INSERT INTO pes_cs_reply_quality (id, shop_id, date, 
      cs_nick, avg_wait_time_first, avg_wait_time
      )
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{avgWaitTimeFirst,jdbcType=DOUBLE}, #{avgWaitTime,jdbcType=DOUBLE}
      )
  </insert>
  <update id="updateCsrReplyQualityId" parameterType="com.pes.jd.model.DO.CsrReplyQuality" >
    UPDATE pes_cs_reply_quality
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="avgWaitTimeFirst != null" >
        avg_wait_time_first = #{avgWaitTimeFirst,jdbcType=DOUBLE},
      </if>
      <if test="avgWaitTime != null" >
        avg_wait_time = #{avgWaitTime,jdbcType=DOUBLE},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>