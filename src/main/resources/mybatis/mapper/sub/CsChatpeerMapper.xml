<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsChatpeerMapper" >
  <resultMap id="CsChatpeerDO" type="com.pes.jd.model.DO.CsChatpeer" >
	  <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="chat_flag" property="chatFlag" jdbcType="INTEGER" />
    <result column="forward_flag" property="forwardFlag" jdbcType="INTEGER" />
    <result column="forward_filte_flag" property="forwardFilteFlag" jdbcType="INTEGER" />
    <result column="is_watchword_buyer" property="isWatchwordBuyer" jdbcType="BIT" />
    <result column="is_filtered_buyer" property="isFilteredBuyer" jdbcType="BIT" />
    <result column="is_cs_single_chat_filter" property="isCsSingleChatFilter" jdbcType="BIT" />
    <result column="is_cust_single_chat_filter" property="isCustSingleChatFilter" jdbcType="BIT" />
    <result column="is_consult" property="isConsult" jdbcType="BIT" />
    <result column="is_receive" property="isReceive" jdbcType="BIT" />
    <result column="is_enquiry" property="isEnquiry" jdbcType="BIT" />
    <result column="is_cs_consult_first" property="isCsConsultFirst" jdbcType="BIT" />
    <result column="is_pes" property="isPes" jdbcType="BIT" />
    <result column="is_team_pes" property="isTeamPes" jdbcType="BIT" />
    <result column="is_next_day_pes" property="isNextDayPes" jdbcType="BIT" />
    <result column="is_assist" property="isAssist" jdbcType="BIT" />
    <result column="is_after_sale" property="isAfterSale" jdbcType="BIT" />
    <result column="is_order_created" property="isOrderCreated" jdbcType="BIT" />
    <result column="is_cs_offline_msg_filter" property="isCsOfflineMsgFilter" jdbcType="BIT" />
    <result column="is_ma_auto_reply_filter" property="isMaAutoReplyFilter" jdbcType="BIT" />
    <result column="chat_num" property="chatNum" jdbcType="INTEGER" />
    <result column="is_cross_chat_filter" property="isCrossChatFilter" jdbcType="BIT" />
    <result column="is_cross_chat" property="isCrossChat" jdbcType="BIT" />
    <result column="buyer_chat_num" property="buyerChatNum" jdbcType="INTEGER" />
    <result column="first_chat_date" property="firstChatDate" jdbcType="TIMESTAMP" />
    <result column="last_chat_date" property="lastChatDate" jdbcType="TIMESTAMP" />
    <result column="cs_chat_first_flag" property="csChatFirstFlag" jdbcType="INTEGER" />
    <result column="cs_active_chat_fail" property="csActiveChatFail" jdbcType="BIT" />
    <result column="cs_active_urgepay_fail" property="csActiveUrgepayFail" jdbcType="BIT" />
  </resultMap>
  
  
  <resultMap id="CsChatpeerDTO" type="com.pes.jd.model.DTO.ChatPeerDTO" >
	  <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="chat_flag" property="chatFlag" jdbcType="INTEGER" />
    <result column="forward_flag" property="forwardFlag" jdbcType="INTEGER" />
    <result column="forward_filte_flag" property="forwardFilteFlag" jdbcType="INTEGER" />
    <result column="is_watchword_buyer" property="isWatchwordBuyer" jdbcType="BIT" />
    <result column="is_filtered_buyer" property="isFilteredBuyer" jdbcType="BIT" />
    <result column="is_cs_single_chat_filter" property="isCsSingleChatFilter" jdbcType="BIT" />
    <result column="is_cust_single_chat_filter" property="isCustSingleChatFilter" jdbcType="BIT" />
    <result column="is_consult" property="isConsult" jdbcType="BIT" />
    <result column="is_receive" property="isReceive" jdbcType="BIT" />
    <result column="is_enquiry" property="isEnquiry" jdbcType="BIT" />
    <result column="is_cs_consult_first" property="isCsConsultFirst" jdbcType="BIT" />
    <result column="is_pes" property="isPes" jdbcType="BIT" />
    <result column="is_team_pes" property="isTeamPes" jdbcType="BIT" />
    <result column="is_next_day_pes" property="isNextDayPes" jdbcType="BIT" />
    <result column="is_assist" property="isAssist" jdbcType="BIT" />
    <result column="is_after_sale" property="isAfterSale" jdbcType="BIT" />
    <result column="is_order_created" property="isOrderCreated" jdbcType="BIT" />
    <result column="is_cs_offline_msg_filter" property="isCsOfflineMsgFilter" jdbcType="BIT" />
    <result column="is_ma_auto_reply_filter" property="isMaAutoReplyFilter" jdbcType="BIT" />
    <result column="chat_num" property="chatNum" jdbcType="INTEGER" />
    <result column="buyer_chat_num" property="buyerChatNum" jdbcType="INTEGER" />
    <result column="first_chat_date" property="firstChatDate" jdbcType="TIMESTAMP" />
    <result column="last_chat_date" property="lastChatDate" jdbcType="TIMESTAMP" />
     <result column="is_cross_chat_filter" property="isCrossChatFilter" jdbcType="BIT" />
    <result column="is_cross_chat" property="crossChat" jdbcType="BIT" />
  	<result column="cs_chat_first_flag" property="csChatFirstFlag" jdbcType="INTEGER" />
    <result column="cs_active_chat_fail" property="csActiveChatFail" jdbcType="BIT" />
    <result column="cs_active_urgepay_fail" property="csActiveUrgepayFail" jdbcType="BIT" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, cs_nick, buyer_nick, date, chat_flag, forward_flag, forward_filte_flag, 
    is_watchword_buyer, is_filtered_buyer, is_cs_single_chat_filter, is_cust_single_chat_filter, 
    is_consult, is_receive, is_enquiry, is_cs_consult_first, is_pes, is_team_pes, is_next_day_pes, 
    is_assist, is_after_sale, is_order_created, is_cs_offline_msg_filter, is_ma_auto_reply_filter, 
    chat_num, buyer_chat_num, first_chat_date, last_chat_date,is_cross_chat_filter,is_cross_chat,cs_chat_first_flag,cs_active_chat_fail,cs_active_urgepay_fail
  </sql>
  
 <!-- <select id="getCsChatpeerById" resultMap="CsChatpeerDO" parameterType="java.lang.Long" >
    SELECT 
    shop_id,<include refid="base_field" />
    FROM pes_cs_chatpeer
    WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteCsChatpeerById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_chatpeer
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertCsChatpeer" parameterType="com.pes.jd.model.DO.CsChatpeer" >
    insert into pes_cs_chatpeer (id, shop_id, cs_nick, 
      buyer_nick, date, chat_flag, 
      forward_flag, forward_filte_flag, is_watchword_buyer, 
      is_filtered_buyer, is_cs_single_chat_filter, is_cust_single_chat_filter, 
      is_consult, is_receive, is_enquiry, 
      is_cs_consult_first, is_pes, is_team_pes, 
      is_next_day_pes, is_assist, is_after_sale, 
      is_order_created, is_cs_offline_msg_filter, is_ma_auto_reply_filter, 
      chat_num, buyer_chat_num, first_chat_date, 
      last_chat_date)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, 
      #{buyerNick,jdbcType=VARCHAR}, #{date,jdbcType=DATE}, #{chatFlag,jdbcType=TINYINT}, 
      #{forwardFlag,jdbcType=TINYINT}, #{forwardFilteFlag,jdbcType=TINYINT}, #{isWatchwordBuyer,jdbcType=BIT}, 
      #{isFilteredBuyer,jdbcType=BIT}, #{isCsSingleChatFilter,jdbcType=BIT}, #{isCustSingleChatFilter,jdbcType=BIT}, 
      #{isConsult,jdbcType=BIT}, #{isReceive,jdbcType=BIT}, #{isEnquiry,jdbcType=BIT}, 
      #{isCsConsultFirst,jdbcType=BIT}, #{isPes,jdbcType=BIT}, #{isTeamPes,jdbcType=BIT}, 
      #{isNextDayPes,jdbcType=BIT}, #{isAssist,jdbcType=BIT}, #{isAfterSale,jdbcType=BIT}, 
      #{isOrderCreated,jdbcType=BIT}, #{isCsOfflineMsgFilter,jdbcType=BIT}, #{isMaAutoReplyFilter,jdbcType=BIT}, 
      #{chatNum,jdbcType=INTEGER}, #{buyerChatNum,jdbcType=INTEGER}, #{firstChatDate,jdbcType=TIMESTAMP}, 
      #{lastChatDate,jdbcType=TIMESTAMP})
  </insert>
  <update id="updateCsChatpeerById" parameterType="com.pes.jd.model.DO.CsChatpeer" >
     update pes_cs_chatpeer
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="buyerNick != null" >
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="chatFlag != null" >
        chat_flag = #{chatFlag,jdbcType=TINYINT},
      </if>
      <if test="forwardFlag != null" >
        forward_flag = #{forwardFlag,jdbcType=TINYINT},
      </if>
      <if test="forwardFilteFlag != null" >
        forward_filte_flag = #{forwardFilteFlag,jdbcType=TINYINT},
      </if>
      <if test="isWatchwordBuyer != null" >
        is_watchword_buyer = #{isWatchwordBuyer,jdbcType=BIT},
      </if>
      <if test="isFilteredBuyer != null" >
        is_filtered_buyer = #{isFilteredBuyer,jdbcType=BIT},
      </if>
      <if test="isCsSingleChatFilter != null" >
        is_cs_single_chat_filter = #{isCsSingleChatFilter,jdbcType=BIT},
      </if>
      <if test="isCustSingleChatFilter != null" >
        is_cust_single_chat_filter = #{isCustSingleChatFilter,jdbcType=BIT},
      </if>
      <if test="isConsult != null" >
        is_consult = #{isConsult,jdbcType=BIT},
      </if>
      <if test="isReceive != null" >
        is_receive = #{isReceive,jdbcType=BIT},
      </if>
      <if test="isEnquiry != null" >
        is_enquiry = #{isEnquiry,jdbcType=BIT},
      </if>
      <if test="isCsConsultFirst != null" >
        is_cs_consult_first = #{isCsConsultFirst,jdbcType=BIT},
      </if>
      <if test="isPes != null" >
        is_pes = #{isPes,jdbcType=BIT},
      </if>
      <if test="isTeamPes != null" >
        is_team_pes = #{isTeamPes,jdbcType=BIT},
      </if>
      <if test="isNextDayPes != null" >
        is_next_day_pes = #{isNextDayPes,jdbcType=BIT},
      </if>
      <if test="isAssist != null" >
        is_assist = #{isAssist,jdbcType=BIT},
      </if>
      <if test="isAfterSale != null" >
        is_after_sale = #{isAfterSale,jdbcType=BIT},
      </if>
      <if test="isOrderCreated != null" >
        is_order_created = #{isOrderCreated,jdbcType=BIT},
      </if>
      <if test="isCsOfflineMsgFilter != null" >
        is_cs_offline_msg_filter = #{isCsOfflineMsgFilter,jdbcType=BIT},
      </if>
      <if test="isMaAutoReplyFilter != null" >
        is_ma_auto_reply_filter = #{isMaAutoReplyFilter,jdbcType=BIT},
      </if>
      <if test="chatNum != null" >
        chat_num = #{chatNum,jdbcType=INTEGER},
      </if>
      <if test="buyerChatNum != null" >
        buyer_chat_num = #{buyerChatNum,jdbcType=INTEGER},
      </if>
      <if test="firstChatDate != null" >
        first_chat_date = #{firstChatDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastChatDate != null" >
        last_chat_date = #{lastChatDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>-->
  <select id="selectChatPeersCountByCsNickByBuyerByDateForReceiveFilter"  resultType="java.lang.Integer" parameterType="map">
	SELECT count(id)
	FROM ${tableName} cp 
	<where>
		<if test="buyerNick != null and '' != buyerNick">
			buyer_nick =#{buyerNick} 
		</if>
		AND cp.cs_nick in
		<foreach collection="csNickLst" item="csNick" open="(" close=")"
			separator=",">
			#{csNick}
		</foreach>
		AND cp.date between #{startDate} and #{endDate}
	</where>
  </select>
  
  <select id="selectChatPeerByCsNickByBuyerByDateForReceiveFilter"  resultMap="CsChatpeerDTO" parameterType="map">
	SELECT
		cp.shop_id,
		cp.cs_nick, 
		cp.buyer_nick, 
		cp.date,
		cp.chat_flag,
		cp.is_watchword_buyer,
		cp.is_filtered_buyer,
		cp.forward_filte_flag,
		cp.is_cs_single_chat_filter,
		cp.is_cust_single_chat_filter,
		cp.is_cs_offline_msg_filter,
		cp.is_ma_auto_reply_filter,
	    cp.is_cust_leave_message_filter,
		cp.first_chat_date, 
		cp.last_chat_date
		FROM
	(
    <foreach collection="tableNames" item="itm" separator="union all">
	SELECT
		shop_id, 
		cs_nick, 
		buyer_nick, 
		date,
		chat_flag,
		is_watchword_buyer,
		is_filtered_buyer,
		is_cs_single_chat_filter,
		forward_filte_flag,
		is_cust_single_chat_filter,
		is_cs_offline_msg_filter,
		is_ma_auto_reply_filter,
		is_cust_leave_message_filter,
		first_chat_date, 
		last_chat_date
		 FROM ${itm.tableName} 
    <where>
   		  shop_id=#{shopId}
		<if test="buyerNick != null and '' != buyerNick">
		AND	buyer_nick=#{buyerNick} 
		</if>
		AND cs_nick in
		<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
			#{csNick}
		</foreach>
		AND is_receive=0
		AND date between #{itm.beginDate} and #{itm.endDate}
		
	</where>
    </foreach>
    ) cp
  </select>
  
  <select id="selectReceiveChatpeer" resultType="java.lang.String" parameterType="map">
	SELECT buyer_nick buyerNick
	FROM ${tableName}
	<where>
	     shop_id=#{shopId}
		<if test="buyerNickKeyword != null and '' != buyerNickKeyword">
			<!-- buyer_nick like  CONCAT(CONCAT('%',#{buyerNickKeyword}),'%')  -->
		  and buyer_nick = #{buyerNickKeyword}
		</if>
		AND cs_nick in
		<foreach collection="csNickLst" item="itm" open="(" close=")"
			separator=",">
			#{itm.nick}
		</foreach>
		AND date BETWEEN #{startDate} and #{endDate}
		AND chat_flag!=1
	</where>
	ORDER BY date desc,buyer_nick desc
  </select>
  <select id="selectCsBuyerChatpeersByParamFromChatlog" parameterType="map" resultType="java.lang.String">
		SELECT clog.buyer as buyerNick 
		FROM  ${tableName} clog
		<where>
		<if test="buyerNickKeyword != null and '' != buyerNickKeyword">
			<!--  clog.buyer like CONCAT(CONCAT('%',#{buyerNickKeyword}),'%')  -->
			clog.buyer = #{buyerNickKeyword}
		</if>
		AND clog.cs_nick in
		<foreach collection="csNickLst" item="itm" open="(" close=")"
			separator=",">
			#{itm.nick}
		</foreach>
		AND time BETWEEN #{startDate} AND #{endDate}
		<if test="keyWord != null and '' != keyWord">
		AND clog.content like CONCAT(CONCAT('%',#{keyWord}),'%') 
		AND clog.shop_id = #{shopId}
		</if>
		</where>
		ORDER BY clog.cs_nick desc
	</select>
	<select id="selectBuyerChatPeerLstByDate" parameterType="map" resultMap="CsChatpeerDTO">
		SELECT *
		FROM ${tableName}
		WHERE
		buyer_nick = #{buyerNick}
		AND date between #{adjustSDate} and #{endDate}
		AND shop_id = #{shopId}
	</select>
	
	<select id="selectBuyerNicksByDateAndBuyerNickForLostRecord" parameterType="map" resultMap="CsChatpeerDTO">
		SELECT buyer_nick
		FROM ${tableName}
		WHERE
		date BETWEEN #{adjustSDate} AND #{endDate}
		<if test='buyerNick != null and buyerNick != ""'>
			AND buyer_nick = #{buyerNick}
		</if>
		AND chat_flag = 0
		AND shop_id = #{shopId}
	</select>


	<select id="selectChatPeerByDateAndNickAndReceiveParam" parameterType="map" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">
		SELECT 
			cp.buyer_nick buyerNick,
			cp.cs_nick csNick ,
			cp.first_chat_date firstChatDate,
			cp.last_chat_date lastChatDate,
			cp.shop_id shopId,
			cp.date date,
			cp.cross_chat_fail crossChatFail,
			cp.is_after_sale isAfterSale,
			cp.cs_active_chat_fail csActiveChatFail,
			cp.cs_active_urgepay_fail csActiveUrgepayFail,
			cp.is_order_created isOrderCreated
		FROM 
		(
			<foreach collection="cpTableNames" item="cp" separator="union all">
				select 
					shop_id, 
					cs_nick, 
					buyer_nick, 
					date,
					first_chat_date, 
					last_chat_date,
					is_after_sale,
					cross_chat_fail,
					cs_active_chat_fail,
					cs_active_urgepay_fail,
					is_order_created
				from ${cp.tableName}
				<where>
					 	shop_id = #{shopId}
					 AND   cs_nick in
					<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
							#{csNick}
					</foreach>
					<if test="param.buyerNick!=null and param.buyerNick!=''">
					AND	buyer_nick =#{param.buyerNick}
					</if>
					<if test="param.isAfterSale!=null">
					AND	is_after_sale =#{param.isAfterSale}
					</if>
					<if test="param.crossChatFail!=null ">
					AND	cross_chat_fail =#{param.crossChatFail}
					</if>
					<if test="param.csActiveChatFail!=null ">
					AND	cs_active_chat_fail =#{param.csActiveChatFail}
					</if>
					<if test="param.csActiveUrgepayFail!=null ">
					AND	cs_active_urgepay_fail =#{param.csActiveUrgepayFail}
					</if>
					AND is_receive=1
					
					AND date BETWEEN #{cp.beginDate} AND #{cp.endDate}
					
					</where>
			</foreach>
		) cp
		<if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
  		 </if>
		<if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
		            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
		 </if>
	</select>
	
	
	<select id="selectChatPeerCountByDateAndNickAndReceiveParam" parameterType="map" resultType="java.lang.Integer">
		SELECT 
			count(cp.id)
		FROM 
		(
			<foreach collection="cpTableNames" item="cp" separator="union all">
				select 
					id,
					shop_id, 
					cs_nick, 
					buyer_nick, 
					date,is_after_sale,cross_chat_fail
				from ${cp.tableName}
				<where>
				 	shop_id = #{shopId}
					AND cs_nick in
					<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
							#{csNick}
					</foreach>
					<if test="param.buyerNick!=null and param.buyerNick!=''">
					AND	buyer_nick =#{param.buyerNick}
					</if>
					<if test="param.isAfterSale!=null and param.isAfterSale!=''">
					AND	is_after_sale =#{param.isAfterSale}
					</if>
					<if test="param.crossChatFail!=null ">
					AND	cross_chat_fail =#{param.crossChatFail}
					</if>
					<if test="param.csActiveChatFail!=null ">
					AND	cs_active_chat_fail =#{param.csActiveChatFail}
					</if>
					<if test="param.csActiveUrgepayFail!=null ">
					AND	cs_active_urgepay_fail =#{param.csActiveUrgepayFail}
					</if>
					AND is_receive=1
					AND date BETWEEN #{cp.beginDate} AND #{cp.endDate}
					
					</where>
			</foreach>
		) cp
		
	</select>
	 <select id="selectChatPeerByCsNickByBuyerByDateForCustomerReceive"  resultType="com.pes.jd.model.DTO.CustomerReceiveDTO" parameterType="map">
		SELECT
			cp.shop_id shopId,
			cp.cs_nick csNick,
			cp.buyer_nick buyerNick, 
			cp.date date,
			cp.first_chat_date firstChatDate, 
			cp.last_chat_date lastChatDate,
			cp.cross_chat_fail crossChatFail,
			cp.is_after_sale isAfterSale,
			cp.cs_active_chat_fail csActiveChatFail,
			cp.cs_active_urgepay_fail csActiveUrgepayFail,
			cp.is_order_created isOrderCreated
		FROM
		(
		<foreach collection="cpTableNames" item="table" separator="union all">
				SELECT
					shop_id, 
					cs_nick, 
					buyer_nick, 
					date,
					first_chat_date, 
					last_chat_date,
					is_after_sale,
					cross_chat_fail,
					cs_active_chat_fail,
					cs_active_urgepay_fail,
					is_order_created
				FROM ${table.tableName}
				<where>
						 shop_id=#{shopId}
					AND	cs_nick in
					<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
						#{csNick}
					</foreach>
					<if test="buyerNick != null and buyerNick !=''">
						AND	buyer_nick =#{buyerNick}
					</if>
					AND is_receive=1
					AND date between #{table.beginDate} and #{table.endDate}
				</where>
		</foreach>
		)
	 cp
	<if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!='' ">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
  		 </if>
		<if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
		     LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
		 </if>
  </select>
  
   <select id="selectCountByCsNickByBuyerByDateForCustomerReceive"   parameterType="map" resultType="int">
	SELECT
			count(distinct cp.id)
		FROM
		(
		<foreach collection="cpTableNames" item="table" separator="union all">
				SELECT id,cs_nick,buyer_nick,date
				
				FROM ${table.tableName}
				<where>
					 shop_id=#{shopId}
					AND	cs_nick in
					<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
						#{csNick}
					</foreach>
					<if test="buyerNick!=null and buyerNick!=''">
						AND	buyer_nick =#{buyerNick}
					</if>
					AND is_receive=1
					AND date between #{table.beginDate} and #{table.endDate}
				</where>
		</foreach>
		)
	 cp
	
  </select>

	<select id="selectChatPeerByCsNickByBuyerLstByDate"  resultType="com.pes.jd.model.DTO.CustomerReceiveDTO" parameterType="map">
			SELECT
			shop_id shopId,
			cs_nick csNick,
			buyer_nick buyerNick,
			date date,
			first_chat_date firstChatDate,
			last_chat_date lastChatDate,
			cross_chat_fail crossChatFail,
			is_after_sale isAfterSale,
			cs_active_chat_fail csActiveChatFail,
			cs_active_urgepay_fail csActiveUrgepayFail,
			is_order_created isOrderCreated
			FROM ${tableName}
			<where>
				shop_id=#{shopId}
				AND	cs_nick = #{csNick}
				AND	buyer_nick in
				<foreach collection="buyerNickLst" item="buyerNick" open="(" close=")" separator=",">
						 #{buyerNick}
				</foreach>
				AND is_receive=1
				AND date between #{startDate} and #{endDate}
			</where>
	</select>
</mapper>