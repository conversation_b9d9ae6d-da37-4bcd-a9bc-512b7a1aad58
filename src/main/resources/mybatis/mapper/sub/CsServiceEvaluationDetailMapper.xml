<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsServiceEvaluationDetailMapper" >

  <resultMap id="CsServiceEvaluationDetailDO" type="com.pes.jd.model.DO.CsServiceEvaluationDetail" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="eval_time" property="evalTime" jdbcType="TIMESTAMP" />
    <result column="eval_code" property="evalCode" jdbcType="INTEGER" />
  </resultMap>
  
   <resultMap id="CsServiceEvaluateDetailDTO" type="com.pes.jd.model.DTO.CsServiceEvaluateDetailDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="eval_time" property="evalTime" jdbcType="TIMESTAMP" />
    <result column="eval_code" property="evalCode" jdbcType="INTEGER" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, cs_nick, buyer_nick, send_time, eval_time, eval_code
  </sql>

  <select id="selectByDateAndShopNick" resultType="java.util.Map" parameterType="map" >
    SELECT
    <include refid="base_field" />
    FROM ${tableName}
    WHERE eval_time BETWEEN ${begin} AND ${end}
    AND shop_id = #{shopId} AND cs_nick = #{nick}
  </select>

    <select id="selectPerformanceData" resultType="com.pes.jd.model.DTO.NickPerformanceDTO" >
        SELECT
        count(1) evalDataSize, -- 每组总数
        count(eval_code) evalCode,
        DATE_FORMAT(send_time,'%Y-%m-%d') date,
        cs_nick nick,
        count(CASE WHEN eval_code >=75 THEN 1 ELSE NULL END) goodEvaluationCount -- 满意评价数量
        FROM ${tableName}
        WHERE
        shop_id IN
        <foreach collection="nicks" item="item" separator="," open="(" close=")">
            #{item.shopId}
        </foreach>
        AND cs_nick IN
        <foreach collection="nicks" item="item" separator="," open="(" close=")">
            #{item.nick}
        </foreach>
        AND send_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY ${groupBy}
    </select>
  
  <select id="getCsServiceEvaluationById" resultMap="CsServiceEvaluationDetailDO" parameterType="java.lang.Long" >
    SELECT
    <include refid="base_field" />
    FROM pes_cs_service_evaluation
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectShopQNDailyEvalLst" resultMap="CsServiceEvaluationDetailDO" parameterType="java.util.Map">
    SELECT
			DATE_FORMAT(send_time,'%Y-%m-%d') as date,
			avg(eval_code) as avgEvalCode,
			count(send_time) as count,
			sum(eval_code) as totalEvalCode
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND send_time BETWEEN #{beginDate} AND #{endDate}
		AND eval_time is not null
		GROUP BY date
  </select>

  <select id="selectShopQNDailyEvalLstReturnMap" resultType="java.util.Map" parameterType="java.util.Map">
    SELECT
    DATE_FORMAT(send_time,'%Y-%m-%d') as date,
    avg(eval_code) as avgEvalCode,
    count(send_time) as count,
    sum(eval_code) as totalEvalCode
    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND send_time BETWEEN #{beginDate} AND #{endDate}
    AND eval_time is not null
    GROUP BY date
  </select>

  <select id="selectCsServiceEvaluationByNick" resultMap="CsServiceEvaluationDetailDO" parameterType="java.util.Map" >
      SELECT
      <foreach collection="columns" item="item" separator=",">
        ${item}
      </foreach>
      , cs_id
      FROM pes_cs_service_evaluation
      WHERE
    cs_nick in
      <foreach collection="nicks" item="nick" open="(" close=")" separator=",">
        #{nick}
      </foreach>
  </select>
  
  <delete id="deleteCsServiceEvaluationById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_service_evaluation
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
 <select id="selectCsNickServiceEvalByEvaltimeCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
  SELECT cs_nick csNick,buyer_nick buyerNick,DATE_FORMAT(eval_time,'%Y-%m-%d') as evalTime, count(eval_time) as count
		FROM ${tableName}
		WHERE 
			eval_time BETWEEN #{startDate} AND #{endDate}
		AND shop_id = #{shopId} 
		<!-- AND cs_nick=#{csNick}
		AND buyer_nick=#{buyerNick} -->
		AND eval_time is not null 
		GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick,buyer_nick
  
  </select>
 
	<select id="selectCsNickServiceEvalSatisfactionCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
	 SELECT cs_nick csNick,buyer_nick buyerNick, DATE_FORMAT(eval_time,'%Y-%m-%d') as evalTime, count(eval_time) as count
			FROM ${tableName}
			WHERE 
				eval_time BETWEEN #{startDate} AND #{endDate}
			AND shop_id = #{shopId} 
			<!-- AND cs_nick=#{csNick} 
			AND buyer_nick=#{buyerNick} -->
			AND (eval_code = 100 or eval_code = 75)
			AND eval_time is not null 
			GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick,buyer_nick
	 
	 </select> 
	 
	 
	 
	<!--  客服邀评数量 -->
	 <select id="selectCsNickServiceEvalBySendTimeCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
  	SELECT  cs_nick csNick,count(send_time) as count
		FROM ${tableName}
		<where>
			<if test="csNickLst!=null and csNickLst.size()>0">
				 cs_nick in
  				<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
  					#{csNick}
  				</foreach>
			</if>
			And send_time BETWEEN #{startDate} AND #{endDate}
			AND shop_id=#{shopId}
			AND send_time is not null 
		</where>
		GROUP BY DATE_FORMAT(send_time,'%Y-%m-%d'),cs_nick
  </select>
 <!-- 客服对客户评价的数量 -->
  <select id="selectCsNickServiceEvalByEvalTimeCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
  	SELECT cs_nick csNick, count(eval_time) as count
		FROM ${tableName}
		<where>
			<if test="csNickLst!=null and csNickLst.size()>0">
				 cs_nick in
  				<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
  					#{csNick}
  				</foreach>
			</if>
			AND  eval_time BETWEEN #{startDate} AND #{endDate}
			AND shop_id=#{shopId}
			AND eval_time is not null 
		</where>
		GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick
  </select>
  <!-- 评价满意的数量 -->
	<select id="selectCsNickServiceEvalSatisfactionCount" parameterType="map" resultType="com.pes.jd.model.DTO.CsServiceStatisCountDTO">
	 SELECT cs_nick csNick, count(eval_time) as count
			FROM ${tableName}
			<where>
				<if test="csNickLst!=null and csNickLst.size()>0">
				 cs_nick in
  				<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
  					#{csNick}
  				</foreach>
			</if>
				AND eval_time BETWEEN #{startDate} AND #{endDate}
				AND shop_id=#{shopId}
				AND (eval_code = 100 or eval_code = 75)
				AND eval_time is not null 
			</where>
			GROUP BY DATE_FORMAT(eval_time,'%Y-%m-%d'),cs_nick
	 </select> 
	 
	 <!-- 满意率分析数据count -->
	 <select id="selectCsServiceEvalCount" resultType="Integer">
	 	SELECT count(chat.sid)
		FROM
		(<foreach collection="chatSessionTables" item="table" separator="union">
			select sid,begin_datetime,end_datetime
			from ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size()>0">
					and cs_nick in
	  				<foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
	  					#{csNick}
	  				</foreach>
	  			</if>
	  			<if test="buyerNick!=null and buyerNick!=''">
	  				and customer = #{buyerNick}
	  			</if>
				and is_receive = 1
				and date between #{startDate} and #{endDate}
			</where>
		</foreach>) chat
		left join 
		(<foreach collection="evalTables" item="table" separator="union">
			SELECT cs_nick,buyer_nick,eval_time,shop_id,sid
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size()>0">
					and cs_nick in
	  				<foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
	  					#{csNick}
	  				</foreach>
	  			</if>
				<if test="evalCode==1">
					and eval_code = 100
				</if>
				<if test="evalCode==2">
					and eval_code = 75
				</if>
				<if test="evalCode==3">
					and eval_code = 50
				</if>
				<if test="evalCode==4">
					and eval_code = 25
				</if>
				<if test="evalCode==5">
					and eval_code = 0
				</if>
			</where>
		</foreach>
		) eval on eval.sid = chat.sid
		 left join 
		(<foreach collection="sendEvalTables" item="table" separator="union">
			SELECT cs_nick,customer,send_time,eval_time,sid
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size()>0">
					and cs_nick in
	  				<foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
	  					#{csNick}
	  				</foreach>
	  			</if>
	  		</where>
		</foreach>
		) sendEval on chat.sid = sendEval.sid
		<where>
			eval.sid IS NOT NULL
		</where>
	 </select>
	 
	 <!-- 满意率分析查询 -->
	 <select id="selectCsServiceEvalByCsNickByBuyerNickByEvalCode" resultMap="CsServiceEvaluateDetailDTO">
	SELECT
		eval.cs_nick,eval.eval_time,eval.eval_code,eval.shop_id,
		eval.sid,chat.begin_datetime beginDateTime,chat.end_datetime endDateTime,
		eval.buyer_nick,
		(CASE WHEN 
			DATE_ADD(sendEval.send_time,INTERVAL 3 day) &lt;= sendEval.eval_time THEN '' 
			ELSE sendEval.send_time END) as send_time
	FROM
	(<foreach collection="chatSessionTables" item="table" separator="union">
		select sid,begin_datetime,end_datetime
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size()>0">
				and cs_nick in
  				<foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
  					#{csNick}
  				</foreach>
  			</if>
  			<if test="buyerNick!=null and buyerNick!=''">
  				and customer = #{buyerNick}
  			</if>
			and is_receive = 1
			and date between #{startDate} and #{endDate}
		</where>
	</foreach>) chat
	left join 
	(<foreach collection="evalTables" item="table" separator="union">
		SELECT cs_nick,buyer_nick,eval_time,shop_id,sid,
		(case
			when eval_code = 100 then '非常满意'
			when eval_code = 75 then '满意'
			when eval_code = 50 then '一般'
			when eval_code = 25 then '不满意'
			else '非常不满意' end) as eval_code
		FROM ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size()>0">
				and cs_nick in
  				<foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
  					#{csNick}
  				</foreach>
  			</if>
  		</where>
	</foreach>
	) eval on eval.sid = chat.sid
	 left join 
	(<foreach collection="sendEvalTables" item="table" separator="union">
		SELECT cs_nick,customer,send_time,eval_time,sid
		FROM ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size()>0">
				and cs_nick in
  				<foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
  					#{csNick}
  				</foreach>
  			</if>
  		</where>
	</foreach>
	) sendEval on chat.sid = sendEval.sid
	<where>
		eval.sid IS NOT NULL
		<if test="evalCode==1">
			and eval.eval_code = '非常满意'
		</if>
		<if test="evalCode==2">
			and eval.eval_code = '满意'
		</if>
		<if test="evalCode==3">
			and eval.eval_code = '一般'
		</if>
		<if test="evalCode==4">
			and eval.eval_code = '不满意'
		</if>
		<if test="evalCode==5">
			and eval.eval_code = '非常不满意'
		</if>
	</where>
   	 <if test="sortPageQuery.currentPage != null and sortPageQuery.size != 0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
     </if>
	 </select>
	 
	 
	<select id="selectCsServiceEvalReceiveChatSession" resultMap="CsServiceEvaluateDetailDTO">
		select * from ${tableName}
		<where>
			 eval_time is not null
			AND  eval_time BETWEEN #{startDate} AND #{endDate}
			AND shop_id=#{shopId}
		</where>
	</select>
	
</mapper>