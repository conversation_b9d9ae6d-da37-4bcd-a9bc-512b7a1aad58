<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopPerformancePresaleMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopPerformancePresaleDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="activity_id" property="activityId" jdbcType="BIGINT"/>
        <result column="ordered_buyer_num" property="orderedBuyerNum" jdbcType="INTEGER"/>
        <result column="ordered_sku_num" property="orderedSkuNum" jdbcType="INTEGER"/>
        <result column="ordered_bargain_buyer_num" property="orderedBargainBuyerNum" jdbcType="INTEGER"/>
        <result column="ordered_bargain_sku_num" property="orderedBargainSkuNum" jdbcType="INTEGER"/>
        <result column="ordered_bargain_amount" property="orderedBargainAmount" jdbcType="DOUBLE"/>
        <result column="ordered_balance_buyer_num" property="orderedBalanceBuyerNum" jdbcType="INTEGER"/>
        <result column="ordered_balance_sku_num" property="orderedBalanceSkuNum" jdbcType="INTEGER"/>
        <result column="ordered_balance_amount" property="orderedBalanceAmount" jdbcType="DOUBLE"/>
        <result column="to_ordered_bargain_buyer_num" property="toOrderedBargainBuyerNum" jdbcType="INTEGER"/>
        <result column="to_ordered_balance_buyer_num" property="toOrderedBalanceBuyerNum" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="base_field">
  	id,shop_id,`date`,sku_id,sku_name,activity_id,
  	ordered_buyer_num,ordered_sku_num,ordered_bargain_buyer_num,ordered_bargain_sku_num,ordered_bargain_amount,
  	ordered_balance_buyer_num,ordered_balance_sku_num,ordered_balance_amount,to_ordered_bargain_buyer_num,to_ordered_balance_buyer_num
    </sql>

    <select id="selectByActivityIdAndSku" resultType="com.pes.jd.model.DO.ShopPerformancePresaleDO">
        SELECT
        activity_id, sku_id, sku_name,shop_id,
        SUM(ordered_buyer_num) AS ordered_buyer_num,
        SUM(ordered_sku_num) AS ordered_sku_num,
        SUM(ordered_bargain_buyer_num) AS ordered_bargain_buyer_num,
        SUM(ordered_bargain_sku_num) AS ordered_bargain_sku_num,
        SUM(ordered_bargain_amount) AS ordered_bargain_amount,
        SUM(ordered_balance_buyer_num) AS ordered_balance_buyer_num,
        SUM(ordered_balance_sku_num) AS ordered_balance_sku_num,
        SUM(ordered_balance_amount) AS ordered_balance_amount,
        SUM(to_ordered_bargain_buyer_num) AS to_ordered_bargain_buyer_num,
        SUM(to_ordered_balance_buyer_num) AS to_ordered_balance_buyer_num
        FROM ${tableName}
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        AND activity_id IN
        <foreach collection="activityIds" item="activityId" open="(" close=")" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        <if test="skuId != null">
            AND sku_id = #{skuId,jdbcType=BIGINT}
        </if>
        <if test="skuName != null and skuName != ''">
            AND sku_name = #{skuName,jdbcType=VARCHAR}
        </if>
        GROUP BY activity_id, sku_id
    </select>


    <select id="selectByActivityIdAndSkuAndDate" resultType="com.pes.jd.model.DO.ShopPerformancePresaleDO">
        SELECT
        <include refid="base_field"></include>
        FROM ${tableName}
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        AND activity_id = #{activityId,jdbcType=VARCHAR}
        AND sku_id = #{skuId,jdbcType=BIGINT}
        <if test="startDate != null and endDate != null">
            AND `date` between #{startDate} AND #{endDate}
        </if>
    </select>

</mapper>