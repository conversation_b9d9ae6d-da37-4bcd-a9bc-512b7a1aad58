<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CustomerInfBusinessMapper">
    <resultMap id="customerInfoDTO" type="com.pes.jd.model.DTO.CustomerInfoDTO">
        <id column="id" property="id"/>
        <result column="shopId" property="shopId"/>
        <result column="customer" property="customer"/>
        <result column="evaluate" property="evaluate"/>
        <result column="customer_label" property="customerLabel"/>
        <result column="imageUrl" property="imageUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <insert id="insertOrderCustomerInfo" parameterType="com.pes.jd.model.DTO.CustomerInfoDTO">
        insert INTO ${tableName}
        (shopId,customer,evaluate,create_time)
        values
        (#{customerInfoDTO.shopId},
         #{customerInfoDTO.customer},
         #{customerInfoDTO.evaluate},
         #{customerInfoDTO.createTime}
         )
    </insert>
    <delete id="delete">
        DELETE FROM ${tableName} WHERE id
        IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>


    <select id="selectCustomerInfoByShopIdAndOrderId" parameterType="map" resultMap="customerInfoDTO">
        select
        id,
        shopId,
        customer,
        evaluate,
        customer_label,
        imageUrl,
        create_time,
        update_time
        from ${tableName}
        where shopId=#{shopId}
        and   customer=#{customer}
    </select>

    <update id="updateCustomerInfo" parameterType="map">
        update ${tableName}
        set evaluate=#{evaluate},
            customer_label=#{customerLabel},
            update_time=#{updateTime}
        where customer=#{customer}
        and shopId=#{shopId}
    </update>

</mapper>