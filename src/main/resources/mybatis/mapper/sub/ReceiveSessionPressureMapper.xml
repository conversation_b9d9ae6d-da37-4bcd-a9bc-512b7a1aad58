<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.ReceiveSessionPressureMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ReceiveSessionPressureDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="receive_dot" jdbcType="TIMESTAMP" property="receiveDot" />
    <result column="minute_flag" jdbcType="INTEGER" property="minuteFlag" />
    <result column="receive_session_num" jdbcType="INTEGER" property="receiveSessionNum" />
  </resultMap>
  <resultMap id="ReceiveSessionPressureDTO" type="com.pes.jd.model.DTO.ReceiveSessionPressureDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="receive_dot" jdbcType="TIMESTAMP" property="receiveDot" />
    <result column="minute_flag" jdbcType="INTEGER" property="minuteFlag" />
    <result column="receive_session_num" jdbcType="INTEGER" property="receiveSessionNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, cs_nick, receive_dot, minute_flag, receive_session_num
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_receive_session_pressure
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchByDateShopNicks" resultMap="ReceiveSessionPressureDTO">

    select 
    <include refid="Base_Column_List"/>
    from ${tableName}
    where receive_dot between #{startDate} and #{endDate}
    and shop_id = #{shopId}
    <if test="nicks!=null and nicks.size()>0">
      and cs_nick in
      <foreach collection="nicks" separator="," open="(" close=")" item="nick">
        #{nick}
      </foreach>
    </if>

  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_receive_session_pressure
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ReceiveSessionPressureDO">
    insert into pes_receive_session_pressure (id, shop_id, cs_nick, 
      receive_dot, minute_flag, receive_session_num
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, 
      #{receiveDot,jdbcType=TIMESTAMP}, #{minuteFlag,jdbcType=INTEGER}, #{receiveSessionNum,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ReceiveSessionPressureDO">
    insert into pes_receive_session_pressure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="receiveDot != null">
        receive_dot,
      </if>
      <if test="minuteFlag != null">
        minute_flag,
      </if>
      <if test="receiveSessionNum != null">
        receive_session_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="receiveDot != null">
        #{receiveDot,jdbcType=TIMESTAMP},
      </if>
      <if test="minuteFlag != null">
        #{minuteFlag,jdbcType=INTEGER},
      </if>
      <if test="receiveSessionNum != null">
        #{receiveSessionNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ReceiveSessionPressureDO">
    update pes_receive_session_pressure
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="receiveDot != null">
        receive_dot = #{receiveDot,jdbcType=TIMESTAMP},
      </if>
      <if test="minuteFlag != null">
        minute_flag = #{minuteFlag,jdbcType=INTEGER},
      </if>
      <if test="receiveSessionNum != null">
        receive_session_num = #{receiveSessionNum,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ReceiveSessionPressureDO">
    update pes_receive_session_pressure
    set shop_id = #{shopId,jdbcType=BIGINT},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      receive_dot = #{receiveDot,jdbcType=TIMESTAMP},
      minute_flag = #{minuteFlag,jdbcType=INTEGER},
      receive_session_num = #{receiveSessionNum,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>