<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsLeaveMessageMapper">
    <sql id="columns">
    id, cs_nick, customer, leave_msg_time, allocation_time, start_time, end_time, shop_id,
    sid
  </sql>
    <select id="selectConsultByShopIdAndCustomerAndLeaveMsgTime" resultType="com.pes.jd.model.DO.CsLeaveMessageDO">
        SELECT
        <include refid="columns"/>
        FROM ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="customer != null and customer.trim().length() > 0">
                AND customer = #{customer}
            </if>
            AND leave_msg_time BETWEEN #{startDate} AND #{endDate}
            ORDER BY leave_msg_time ASC
        </where>
    </select>

    <select id="selectAssignByShopIdAndCsNickAndCustomerAndAllocationTime"
            resultType="com.pes.jd.model.DO.CsLeaveMessageDO">
        select
        <include refid="columns"/>
        FROM ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="customer != null and customer.trim().length() > 0">
                AND customer = #{customer}
            </if>
            <if test="csNickLst != null">
                AND cs_nick IN
                <foreach collection="csNickLst" item="csNick" open="(" separator="," close=")">
                    #{csNick}
                </foreach>
            </if>
            AND allocation_time IS NOT NULL
            AND allocation_time BETWEEN #{startDate} AND #{endDate}
            ORDER BY leave_msg_time ASC
        </where>
    </select>
    <select id="selectByShopIdAndDateAndSids" resultType="com.pes.jd.model.DO.CsLeaveMessageDO">
        SELECT
        <include refid="columns"/>
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <if test="sidSet != null and sidSet.size() > 0">
            AND sid IN
            <foreach collection="sidSet" item="sid" open="(" separator="," close=")">
                #{sid}
            </foreach>
        </if>
    </select>
</mapper>