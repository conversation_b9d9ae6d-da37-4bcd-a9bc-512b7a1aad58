<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsPerformancePreordainMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.CsPerformancePreordainDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="date" jdbcType="DATE" property="date" />
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
        <result column="sku_id" jdbcType="BIGINT" property="skuId" />
        <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
        <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
        <result column="consult_buyer_num" jdbcType="INTEGER" property="consultBuyerNum" />
        <result column="enquiry_buyer_num" jdbcType="INTEGER" property="enquiryBuyerNum" />
        <result column="enquiry_ordered_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBuyerNum" />
        <result column="enquiry_ordered_sku_num" jdbcType="INTEGER" property="enquiryOrderedSkuNum" />
        <result column="enquiry_ordered_paid_buyer_num" jdbcType="INTEGER" property="enquiryOrderedPaidBuyerNum" />
        <result column="enquiry_ordered_paid_sku_num" jdbcType="INTEGER" property="enquiryOrderedPaidSkuNum" />
        <result column="enquiry_ordered_paid_amount" jdbcType="DOUBLE" property="enquiryOrderedPaidAmount" />
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum" />
        <result column="ordered_sku_num" jdbcType="INTEGER" property="orderedSkuNum" />
        <result column="ordered_paid_buyer_num" jdbcType="INTEGER" property="orderedPaidBuyerNum" />
        <result column="ordered_paid_sku_num" jdbcType="INTEGER" property="orderedPaidSkuNum" />
        <result column="ordered_paid_amount" jdbcType="DOUBLE" property="orderedPaidAmount" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, sku_id, sku_name, activity_id, consult_buyer_num, enquiry_buyer_num,
    enquiry_ordered_buyer_num, enquiry_ordered_sku_num, enquiry_ordered_paid_buyer_num,
    enquiry_ordered_paid_sku_num, enquiry_ordered_paid_amount, ordered_buyer_num, ordered_sku_num,
    ordered_paid_buyer_num, ordered_paid_sku_num, ordered_paid_amount
  </sql>
    <select id="selectPerformancePreordain" resultMap="BaseResultMap">
        select
        shop_id,cs_nick,sku_id,sku_name,activity_id,sum(consult_buyer_num) as consult_buyer_num,
        sum(enquiry_buyer_num) as  enquiry_buyer_num , sum(enquiry_ordered_buyer_num) as enquiry_ordered_buyer_num,
        sum(enquiry_ordered_sku_num) as enquiry_ordered_sku_num ,sum(enquiry_ordered_paid_buyer_num) as enquiry_ordered_paid_buyer_num,
        sum(enquiry_ordered_paid_sku_num) as enquiry_ordered_paid_sku_num ,sum(enquiry_ordered_paid_amount) as enquiry_ordered_paid_amount
        ,sum(ordered_buyer_num) as ordered_buyer_num ,sum(ordered_sku_num) as ordered_sku_num,sum(ordered_paid_buyer_num) as ordered_paid_buyer_num,
        sum(ordered_paid_sku_num) as ordered_paid_sku_num ,sum(ordered_paid_amount) as ordered_paid_amount
        from
        pes_cs_performance_preordain
        <trim prefix="where" prefixOverrides="and">
            shop_id = #{shopId}
            <if test="startDate!=null and endDate!=null">
                AND
                `date` BETWEEN #{startDate} and #{endDate}
            </if>
            <if test="activityId!=null">
              AND  activity_id=#{activityId}
            </if>
            <if test="skuIds!=null and skuIds.size()>0">
                AND sku_id IN
                <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="sku!=null and sku!=''">
                AND ( sku_name LIKE concat('%',#{sku},'%')
                OR sku_id LIKE concat('%',#{sku},'%'))
            </if>
        </trim>
       GROUP BY activity_id,sku_id
    </select>


    <select id="selectPerformancePreordainSkuId" resultType="java.lang.Long">
        select sku_id
        from pes_cs_performance_preordain
        <trim prefix="where" prefixOverrides="and">
            shop_id = #{shopId}
            AND
            `date` BETWEEN #{startDate} AND #{endDate}
            <if test="activityId!=null">
                AND  activity_id=#{activityId}
            </if>
            <if test="skuIds!=null and skuIds.size()>0">
                AND sku_id IN
                <foreach collection="skuIds" item="skuId"  open="(" close=")" separator="," >
                    #{skuId}
                </foreach>
            </if>
            <if test="sku!=null and sku!=''">
                AND ( sku_name LIKE concat('%',#{sku},'%')
                OR sku_id LIKE concat('%',#{sku},'%'))
            </if>
        </trim>
         GROUP BY sku_id
    </select>

    <select id="selectPerformancePreordainDetailed" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ${tableName}
        <trim prefix="where" prefixOverrides="and">
            <if test="shopId!=null">
                shop_id=#{shopId}
            </if>
            <if test="startDate!=null and endDate!=null">
            AND `date` BETWEEN #{startDate} and #{endDate}
            </if>
            <if test="activityId!=null">
                AND  activity_id=#{activityId}
            </if>
            <if test="skuName!=null">
                AND sku_name=#{skuName}
            </if>
            <if test="csNick!=null">
                AND cs_nick=#{csNick}
            </if>
            <if test="skuId!=null">
               AND sku_id= #{skuId}
            </if>
        </trim>
        ORDER BY `date` DESC
    </select>


    <select id="selectCsPerformancePreordain" resultMap="BaseResultMap">
        select
        shop_id,cs_nick,sku_id,sku_name,activity_id,sum(consult_buyer_num) as consult_buyer_num,
        sum(enquiry_buyer_num) as  enquiry_buyer_num , sum(enquiry_ordered_buyer_num) as enquiry_ordered_buyer_num,
        sum(enquiry_ordered_sku_num) as enquiry_ordered_sku_num ,sum(enquiry_ordered_paid_buyer_num) as enquiry_ordered_paid_buyer_num,
        sum(enquiry_ordered_paid_sku_num) as enquiry_ordered_paid_sku_num ,sum(enquiry_ordered_paid_amount) as enquiry_ordered_paid_amount
        ,sum(ordered_buyer_num) as ordered_buyer_num ,sum(ordered_sku_num) as ordered_sku_num,sum(ordered_paid_buyer_num) as ordered_paid_buyer_num,
        sum(ordered_paid_sku_num) as ordered_paid_sku_num ,sum(ordered_paid_amount) as ordered_paid_amount
        from
        pes_cs_performance_preordain
        <trim prefix="where" prefixOverrides="and">
            <if test="shopId!=null">
                shop_id = #{shopId}
            </if>
            <if test="startDate!=null and endDate!=null">
                AND `date` BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="activityId!=null">
                AND  activity_id=#{activityId}
            </if>
            <if test="csNick!=null and csNick.size()>0">
                AND cs_nick IN
                <foreach collection="csNick" item="item"  open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="sku!=null and sku!=''">
                AND ( sku_name LIKE concat('%',#{sku},'%')
                OR sku_id LIKE concat('%',#{sku},'%'))
            </if>
        </trim>
        GROUP BY activity_id,sku_id
    </select>
    <select id="selectCsPerformancePreordainDaily" resultMap="BaseResultMap">
        select
        shop_id,cs_nick,sku_id,sku_name,activity_id,sum(consult_buyer_num) as consult_buyer_num,
        sum(enquiry_buyer_num) as  enquiry_buyer_num , sum(enquiry_ordered_buyer_num) as enquiry_ordered_buyer_num,
        sum(enquiry_ordered_sku_num) as enquiry_ordered_sku_num ,sum(enquiry_ordered_paid_buyer_num) as enquiry_ordered_paid_buyer_num,
        sum(enquiry_ordered_paid_sku_num) as enquiry_ordered_paid_sku_num ,sum(enquiry_ordered_paid_amount) as enquiry_ordered_paid_amount
        ,sum(ordered_buyer_num) as ordered_buyer_num ,sum(ordered_sku_num) as ordered_sku_num,sum(ordered_paid_buyer_num) as ordered_paid_buyer_num,
        sum(ordered_paid_sku_num) as ordered_paid_sku_num ,sum(ordered_paid_amount) as ordered_paid_amount
        from
        ${tableName}
        <trim prefix="where" prefixOverrides="and">
            <if test="shopId!=null">
                shop_id = #{shopId}
            </if>
            <if test="startDate!=null and endDate!=null">
                AND `date` BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="activityId!=null">
                AND  activity_id=#{activityId}
            </if>
            <if test="csNick!=null and csNick.size()>0">
                AND cs_nick IN
                <foreach collection="csNick" item="item"  open="(" close=")" separator="," >
                    #{item}
                </foreach>
            </if>
            <if test="sku!=null and sku!=''">
                AND (sku_name=#{sku}
                OR sku_id=#{sku})
            </if>
        </trim>
        GROUP BY cs_nick
    </select>

</mapper>