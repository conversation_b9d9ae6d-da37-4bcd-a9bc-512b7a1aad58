<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsConsultGoodsMapper" >
  <resultMap id="CustConsultGoodsDTO" type="com.pes.jd.model.DTO.CustConsultGoodsDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
  </resultMap>

	<resultMap id="CustConsultGoodsV2DTO" type="com.pes.jd.model.DTO.CustConsultGoodsV2DTO" >
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="sku_id" property="skuId" jdbcType="BIGINT" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="is_enquiry" property="isEnquiry" jdbcType="INTEGER" />
		<result column="result" property="result" jdbcType="INTEGER" />
		<result column="sku_name" property="skuName" jdbcType="VARCHAR" />
		<result column="order_id" property="orderId" jdbcType="BIGINT" />
	</resultMap>

  <select id ="selectCustConsultGoodsByDateByCsNickByCustomerByResultBySku" resultMap = "CustConsultGoodsDTO">
  	select
  		consult.date,consult.cs_nick,consult.customer,consult.shop_id,consult.order_id,
  		consult.sku_id,consult.result,goods.sku_name
  	from
  	(<foreach collection="consultDetailTables" item="table" separator="union">
  		select date,sku_id,customer,cs_nick,shop_id,
		  (case when result = 1 then '成交'
		  else '未成交' end
		  ) result,order_id
  		from ${table.getTableName}
  		<where>
  			<if test="shopId!=null">
		    	shop_id = #{shopId}
		    </if>
		    <if test="csNickList!=null and csNickList.size>0">
		    	and cs_nick in
		    	<foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
		    		#{nick}
		    	</foreach>
		    </if>
		    <if test="skuLst!=null and skuLst.size>0">
		    	and sku_id in
		    	<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
		    		#{skuId}
		    	</foreach>
		    </if>
		  	<if test = "customer!=null and customer!=''">
		  		and customer = #{customer}
		  	</if>
		  	and date between #{startDate} and #{endDate}
  		</where>
  	</foreach>
  	) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
  	<where>
	  	<if test = "result==1">
	  		and consult.result = '成交'
	  	</if>
	  	<if test = "result==0">
	  		and consult.result = '未成交'
	  	</if>
  	</where>
  	order by consult.date
  </select>

	<select id="selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuByCategoryId" resultMap="CustConsultGoodsDTO">
		select
		consult.date,consult.cs_nick,consult.customer,consult.shop_id,consult.order_id,
		consult.sku_id,consult.result,goods.sku_name
		from
		(<foreach collection="consultDetailTables" item="table" separator="union">
		select date,consult.sku_id,consult.customer,consult.cs_nick,consult.shop_id,
		(case when result = 1 then '成交'
		else '未成交' end
		) result,order_id
		from ${table.getTableName} consult
		inner join ${goodsSkuTables} gs on consult.shop_id = gs.shop_id and consult.sku_id = gs.sku_id
		<where>
			<if test="shopId!=null">
				consult.shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size>0">
				and consult.cs_nick in
				<foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
					#{nick}
				</foreach>
			</if>
			<if test="categoryId!=null and categoryId.size>0">
				and gs.category_id in
				<foreach collection="categoryId" item="catId" separator="," open="(" close=")">
					#{catId}
				</foreach>
			</if>
			<if test="skuLst!=null and skuLst.size>0">
				and consult.sku_id in
				<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
					#{skuId}
				</foreach>
			</if>
			<if test="customer!=null and customer!=''">
				and consult.customer = #{customer}
			</if>
			and consult.date between #{startDate} and #{endDate}
		</where>
	</foreach>
		) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
		<where>
			<if test="result==1">
				and consult.result = '成交'
			</if>
			<if test="result==0">
				and consult.result = '未成交'
			</if>
		</where>
		order by consult.date
	</select>



	<select id ="selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV2" resultMap = "CustConsultGoodsV2DTO">
		select
		consult.date,consult.cs_nick,consult.customer,consult.shop_id,consult.order_id,
		consult.sku_id,consult.result,consult.is_enquiry,goods.sku_name
		from
		(<foreach collection="consultDetailTables" item="table" separator="union">
		select date,sku_id,customer,cs_nick,shop_id,
		(case when result = 1 then '成交' else '未成交' end) result,
		(case when is_enquiry = 1 then '询单' else '非询单' end) is_enquiry,order_id
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size>0">
				and cs_nick in
				<foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
					#{nick}
				</foreach>
			</if>
			<if test="skuLst!=null and skuLst.size>0">
				and sku_id in
				<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
					#{skuId}
				</foreach>
			</if>
			<if test = "customer!=null and customer!=''">
				and customer = #{customer}
			</if>
			and date between #{startDate} and #{endDate}
		</where>
	</foreach>
		) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
		<where>
			<if test = "result==1">
				and consult.result = '成交'
			</if>
			<if test = "result==0">
				and consult.result = '未成交'
			</if>
		</where>
		order by consult.date
	</select>


	<select id="selectCustConsultGoodsByDateByCsNickByCustomerByResultBySkuV4" resultMap="CustConsultGoodsV2DTO">
		select
		consult.date,consult.cs_nick,consult.customer,consult.shop_id,consult.order_id,
		consult.sku_id,consult.result,consult.is_enquiry,goods.sku_name,goods.category_id
		from
		(
		<foreach collection="consultDetailTables" item="table" separator="union">
			select date,sku_id,customer,cs_nick,shop_id,
			(case when result = 1 then '成交' else '未成交' end) result,
			(case when is_enquiry = 1 then '询单' else '非询单' end) is_enquiry,order_id
			from ${table.getTableName} consult
			<if test="categoryId != null and categoryId.size > 0">
				inner join (
				select sku_id, category_id
				from ${goodsSkuTables}
				where shop_id = #{shopId}
				) gs on consult.sku_id = gs.sku_id
			</if>
			<where>
				<if test="shopId!=null">
					consult.shop_id = #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and consult.cs_nick in
					<foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and consult.sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				<if test="customer!=null and customer!=''">
					and consult.customer = #{customer}
				</if>
				<if test="categoryId != null and categoryId.size > 0">
					and gs.category_id in
					<foreach collection="categoryId" item="catId" open="(" close=")" separator=",">
						#{catId}
					</foreach>
				</if>
				and consult.date between #{startDate} and #{endDate}
			</where>
		</foreach>
		) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
		<where>
			<if test="result==1">
				and consult.result = '成交'
			</if>
			<if test="result==0">
				and consult.result = '未成交'
			</if>
		</where>
		order by consult.date
	</select>


</mapper>
