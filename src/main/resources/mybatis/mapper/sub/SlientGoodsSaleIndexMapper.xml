<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.SlientGoodsSaleIndexMapper">
	<resultMap id="SlientGoodsSaleIndexDTO" type="com.pes.jd.model.DTO.SlientGoodsSaleIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="purchase_buyer_num" jdbcType="VARCHAR" property="purchaseBuyerNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
  </resultMap>
  
  <select id="selectGoodsSaleIndexByDateBySku" resultMap="SlientGoodsSaleIndexDTO">
  	SELECT 
  		sale.sku_id,goods.sku_name,sale.shop_id,sale.date,
  		sum(sale.purchase_buyer_num) purchase_buyer_num,
  		sum(sale.sale_goods_num) sale_goods_num,
  		sum(sale.sale_amount) sale_amount,goods.image_url,goods.category_id
		FROM 
		(<foreach collection="tableNames" item="table" separator="union">
			select 
				sku_id,shop_id,date,
				purchase_buyer_num,sale_goods_num,sale_amount
		  	from ${table.getTableName}
		  	<where>
		  		<if test="shopId!=null and shopId!=''">
					shop_id = #{shopId}
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in 
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
		  	</where>
		</foreach>) sale left join ${tableName2} goods ON sale.sku_id = goods.sku_id 
		  	group by sale.sku_id
		  	order by sale.sku_id
  </select>
</mapper>