<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopTeamOvDayMapper">


    <select id="selectByShopIdAndDateForShopPerformance" resultType="com.pes.jd.model.DTO.ShopTeamOvDayDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr,
                `duty_cs_num`, `login_duration_time`, rceive_duration_time
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `duty_cs_num` ) `duty_cs_num`,
                SUM( `login_duration_time` ) `login_duration_time`,
                SUM( `rceive_duration_time` ) `rceive_duration_time`
            </when>
            <otherwise>
                `shop_id`,
                SUM( `duty_cs_num` ) `duty_cs_num`,
                SUM( `login_duration_time` ) `login_duration_time`,
                SUM( `rceive_duration_time` ) `rceive_duration_time`
            </otherwise>
        </choose>

        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>
</mapper>