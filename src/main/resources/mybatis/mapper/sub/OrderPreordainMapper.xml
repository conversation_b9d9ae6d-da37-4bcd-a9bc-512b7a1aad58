<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderPreordainMapper" >

    <resultMap id="OrderPreordainDTO" type="com.pes.jd.model.DTO.OrderPreordainDTO" >
        <result column="order_id" property="orderId" jdbcType="BIGINT" />
        <result column="shop_id" property="shopId" jdbcType="BIGINT" />
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
        <result column="payment" property="payment" jdbcType="DOUBLE" />
        <result column="date" property="date" jdbcType="DATE" />
        <result column="created" property="created" jdbcType="TIMESTAMP" />
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
        <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
        <result column="status" property="orderStatus" jdbcType="VARCHAR" />
        <result column="num" property="goodsNum" jdbcType="INTEGER" />
        <result column="fulfil_order_cs" property="fulfilOrderCs" jdbcType="VARCHAR" />
        <result column="fulfil_pay_cs" property="fulfilPayCs" jdbcType="VARCHAR" />
        <result column="pes_belong_cs" property="pesBelongCs" jdbcType="VARCHAR" />
        <result column="pay_type" property="payType" jdbcType="INTEGER" />
    </resultMap>


    <select id="selectCountOrderPreordainAndCsOrderBindForOrderPreordainAnalysis"
            resultType="int">
        SELECT
        COUNT(1)
        FROM (
        <foreach collection="orderPreordainTables" item="table" separator="UNION">
            SELECT
            order_id
            FROM ${table.tableName}
            WHERE shop_id = #{shopId}
            AND order_type=2
            <!--订单编号-->
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <!--顾客ID-->
            <if test="buyerNick != null and buyerNick.trim().length() > 0">
                AND buyer_nick = #{buyerNick}
            </if>
            <choose>
                <!--时间-->
                <when test="dateType == 1"><!--下单时间-->
                    AND created between #{table.beginDate} AND #{table.endDate}
                </when>
                <when test="dateType == 2"><!--付尾款时间-->
                    AND pay_time between #{table.beginDate} AND #{table.endDate}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <!--交易状态-->
                <when test="tradeType == 1"><!--成交-->
                    AND status IN('FINISHED_L','WanCheng')
                </when>
                <when test="tradeType == 2"><!--待付款-->
                    AND status IN('NO_PAY')
                </when>
                <when test="tradeType == 3"><!--下单流失就是取消状态-->
                    AND status IN ('TRADE_CANCELED')
                </when>
            </choose>
        </foreach>
        ) op
        <if test="groupId != null and groupId.trim().length() > 0">
            INNER JOIN
            (
            <foreach collection="csOrderBindTables" item="table" separator="UNION">
                SELECT
                DISTINCT order_id
                FROM ${table.tableName}
                <where>
                    <!--订单编号-->
                    <if test="orderId != null">
                        AND order_id = #{orderId}
                    </if>
                    AND shop_id = #{shopId}
                    <!--顾客ID-->
                    <if test="buyerNick != null and buyerNick.trim().length() > 0">
                        AND buyer_nick = #{buyerNick}
                    </if>
                    <if test="csNickLst != null and csNickLst.size() > 0">
                        AND cs_nick IN
                        <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                            #{nick}
                        </foreach>
                    </if>
                    AND `date` BETWEEN #{table.beginDate} AND #{table.endDate}
                    AND order_type = 2
                </where>
            </foreach>
            ) cob ON op.order_id = cob.order_id
        </if>
    </select>


    <select id="selectOrderPreordainAndCsOrderBindForOrderPreordainAnalysis" resultMap="OrderPreordainDTO">
        SELECT
        op.order_id,
        op.buyer_nick,
        op.created,
        op.payment,
        op.pay_time,
        op.num,
        op.out_stock_time,
        op.status,
        op.pay_type,
        cob.fulfil_order_cs,
        cob.fulfil_pay_cs,
        cob.pes_belong_cs
        FROM (
        <foreach collection="orderPreordainTables" item="table" separator="UNION">
            SELECT
            order_id,buyer_nick, created, payment,pay_time,num,out_stock_time,status,pay_type
            FROM ${table.tableName}
            WHERE shop_id = #{shopId}
            AND order_type=2
            <!--订单编号-->
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <!--顾客ID-->
            <if test="buyerNick != null and buyerNick.trim().length() > 0">
                AND buyer_nick = #{buyerNick}
            </if>
            <choose>
                <!--时间-->
                <when test="dateType == 1"><!--下单时间-->
                    AND created between #{table.beginDate} AND #{table.endDate}
                </when>
                <when test="dateType == 2"><!--付尾款时间-->
                    AND pay_time between #{table.beginDate} AND #{table.endDate}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <!--交易状态-->
                <when test="tradeType == 1"><!--成交-->
                    AND status IN('FINISHED_L','WanCheng')
                </when>
                <when test="tradeType == 2"><!--待付款-->
                    AND status IN('NO_PAY')
                </when>
                <when test="tradeType == 3"><!--下单流失就是取消状态-->
                    AND status IN ('TRADE_CANCELED')
                </when>
            </choose>
        </foreach>
        ) op
        <choose>
            <when test="groupId != null and groupId.trim().length() > 0">
                INNER JOIN
            </when>
            <otherwise>
                LEFT JOIN
            </otherwise>
        </choose>
        (
        SELECT order_id,
        GROUP_CONCAT( IF ( type = 1, cs_nick, NULL ) SEPARATOR '' ) AS 'fulfil_order_cs',
        GROUP_CONCAT( IF ( type = 2, cs_nick, NULL ) SEPARATOR '' ) AS 'fulfil_pay_cs',
        GROUP_CONCAT( IF ( is_pes_order = 1, cs_nick, '' ) SEPARATOR '' ) AS 'pes_belong_cs' FROM (
        <foreach collection="csOrderBindTables" item="table" separator="UNION">
            SELECT order_id, type, cs_nick, is_pes_order FROM ${table.tableName}
            <where>
                <!--订单编号-->
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                AND shop_id = #{shopId}
                <!--顾客ID-->
                <if test="buyerNick != null and buyerNick.trim().length() > 0">
                    AND buyer_nick = #{buyerNick}
                </if>
                <if test="csNickLst != null and csNickLst.size() > 0">
                    AND cs_nick IN
                    <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                        #{nick}
                    </foreach>
                </if>
                AND `date` BETWEEN #{table.beginDate} AND #{table.endDate}
                AND order_type = 2
            </where>
        </foreach>) bind GROUP BY order_id
        ) cob ON op.order_id = cob.order_id
        ORDER BY
        <choose>
            <!--时间-->
            <when test="dateType == 1"><!--下单时间-->
                op.created ASC
            </when>
            <when test="dateType == 2"><!--付款时间-->
                op.pay_time ASC
            </when>

            <otherwise>
            </otherwise>
        </choose>
        <if test="sortPageQuery != null and sortPageQuery.size != null and sortPageQuery.size > 0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>
</mapper>