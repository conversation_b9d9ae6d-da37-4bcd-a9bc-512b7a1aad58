<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopPvUvDayMapper">


    <select id="selectPvUvNumByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopPvUvDayDTO">
        SELECT
        `shop_id`,
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                `dt` AS 'date',
            </when>
            <!--月份-->
            <when test="dateType == 2">
                DATE_FORMAT( `dt`, '%Y-%m' ) `date`,
            </when>
            <otherwise>

            </otherwise>
        </choose>
        SUM( `pv` ) pv,
        SUM( `uv` ) uv
        FROM ${shopPvUvDayTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `dt` IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
                GROUP BY `dt`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`dt`,'%Y-%m') IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
                GROUP BY DATE_FORMAT( `dt`, '%Y-%m' )
            </when>
            <otherwise>
                AND `dt` IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>


</mapper>