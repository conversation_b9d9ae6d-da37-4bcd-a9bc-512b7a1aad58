<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.LossEnquiryRecordMapper" >
	<resultMap  id="LossEnquiryEnquiryDO" type="com.pes.jd.model.DO.LossEnquiryRecordDO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="chat_num" property="chatNum" jdbcType="INTEGER" />
		<result column="chat_type" property="chatType" jdbcType="INTEGER" />
		<result column="start_datetime" property="startDateTime" jdbcType="TIMESTAMP" />
		<result column="end_datetime" property="endDateTime" jdbcType="TIMESTAMP" />
		
	</resultMap>
	<resultMap  id="LossEnquiryEnquiryDTO" type="com.pes.jd.model.DTO.LossEnquiryRecordDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="chat_num" property="chatNum" jdbcType="INTEGER" />
		<result column="chat_type" property="chatType" jdbcType="INTEGER" />
		<result column="start_datetime" property="startDateTime" jdbcType="TIMESTAMP" />
		<result column="end_datetime" property="endDateTime" jdbcType="TIMESTAMP" />
		
	</resultMap>
	<select id="selectEnquiryLossByDateAndCsNickForCustReceiveAll" resultMap="LossEnquiryEnquiryDTO">
		select 
			el.shop_id,
			el.date,
			el.customer,
			el.cs_nick,
			el.chat_type 
		FROM 
		(
			<foreach collection="elTableNames" item="el" separator="UNION ALL" >
 			SELECT 
	 			shop_id,
	 			date,
	 			customer,
	 			cs_nick,
	 			chat_type
 			FROM ${el.tableName}
 			<where>
 			 		shop_id=#{shopId}
 				AND cs_nick in
	 			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator="," >
					#{csNick}
				</foreach>
				<if test="buyerNick!=null and buyerNick!=''">
				AND customer =#{buyerNick}
				</if>
				AND date between #{el.beginDate} and #{el.endDate}
 			</where>
 		</foreach>
		)
		el
	</select>
	
	<select id="selectEnquiryLossByDateAndCsNickForCustReceiveEnquiryLoss" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">
 	SELECT DISTINCT
		cp.buyer_nick buyerNick,
		cp.cs_nick csNick ,
		cp.first_chat_date firstChatDate,
		cp.last_chat_date lastChatDate,
		cp.shop_id shopId,
		cp.date date,
		cp.cross_chat_fail crossChatFail,
		cp.is_after_sale isAfterSale,
		cp.cs_active_chat_fail csActiveChatFail,
		cp.cs_active_urgepay_fail csActiveUrgepayFail,
		cp.is_order_created isOrderCreated
 	FROM 
 	(
 		<foreach collection="elTableNames" item="el" separator="UNION ALL" >
 			SELECT 
	 			shop_id,
	 			date,
	 			customer,
	 			cs_nick
 			FROM ${el.tableName}
 			<where>
 				 	shop_id=#{shopId}
 				AND	cs_nick in
	 			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator="," >
					#{csNick}
				</foreach>
				<if test="param.buyerNick!=null and param.buyerNick!=''">
				AND customer =#{param.buyerNick}
				</if>
				AND date between #{el.beginDate} and #{el.endDate}
 			</where>
 		</foreach>
 	) el
 	
 	INNER JOIN 
 	(
		<foreach collection="cpTableNames" item="cp" separator="UNION ALL" >
			SELECT 
				id,
				shop_id,
				date,
				cs_nick ,
				buyer_nick,
				first_chat_date, 
				last_chat_date,
				cross_chat_fail,
				is_after_sale ,
				cs_active_chat_fail,
				cs_active_urgepay_fail,
				is_order_created
			FROM ${cp.tableName}
			<where>
			    shop_id=#{shopId}
			AND cs_nick in
			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
				#{csNick}
			</foreach>
			<if test="param.buyerNick!=null and param.buyerNick!=''">
			AND	buyer_nick =#{param.buyerNick}
			</if>
			AND is_receive=1
			AND date between #{cp.beginDate} and #{cp.endDate}
			</where>
		</foreach>
	)
	cp
	ON 
			el.customer=cp.buyer_nick
    WHERE 
		   el.date=cp.date
		   AND el.cs_nick=cp.cs_nick
        <if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
        </if>
        <if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
 	</select>
 	
 	<select id="selectEnquiryLossCountByDateAndCsNickForCustReceiveEnquiryLoss" resultType="java.lang.Integer">
 	SELECT 
		COUNT(DISTINCT cp.id)
 	FROM 
 	(
 		<foreach collection="elTableNames" item="el" separator="UNION ALL" >
 			SELECT 
	 			shop_id,
	 			date,
	 			customer,
	 			cs_nick
 			FROM ${el.tableName}
 			<where>
 			 	    shop_id=#{shopId}
 				AND cs_nick in
	 			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator="," >
					#{csNick}
				</foreach>
				<if test="param.buyerNick!=null and param.buyerNick!=''">
				AND customer =#{param.buyerNick}
				</if>
				AND date between #{el.beginDate} and #{el.endDate}
 			</where>
 		</foreach>
 	) el
 	
 	INNER JOIN 
 	(
		<foreach collection="cpTableNames" item="cp" separator="UNION ALL">
			SELECT 
				id,
				shop_id,
				date,
				cs_nick ,
				buyer_nick,
				first_chat_date, 
				last_chat_date 
			FROM ${cp.tableName}
			<where>
			    shop_id=#{shopId}
			AND cs_nick in
			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
				#{csNick}
			</foreach>
			<if test="param.buyerNick!=null and param.buyerNick!=''">
			AND	buyer_nick =#{param.buyerNick}
			</if>
			AND is_receive=1
			AND date between #{cp.beginDate} and #{cp.endDate}
			</where>
		</foreach>
	)
	cp
	
	ON el.customer=cp.buyer_nick
	WHERE 
			el.date=cp.date
		and	el.cs_nick=cp.cs_nick
 	</select>

	<select id="selectEnquiryLossByDateAndCsNickForEnquiryLoss" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">
		SELECT
		el.shop_id shopId,
		el.date date,
		el.cs_nick csNick,
		el.customer buyerNick
		FROM
		(
		<foreach collection="elTableNames" item="el" separator="UNION ALL" >
			SELECT
			shop_id,
			date,
			customer,
			cs_nick
			FROM ${el.tableName}
			<where>
				shop_id=#{shopId}
				AND	cs_nick in
				<foreach collection="csNickLst" item="csNick" open="(" close=")" separator="," >
					#{csNick}
				</foreach>
				<if test="param.buyerNick!=null and param.buyerNick!=''">
					AND customer =#{param.buyerNick}
				</if>
				AND date between #{el.beginDate} and #{el.endDate}
			</where>
		</foreach>
		) el

		<if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
			ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
		</if>
		<if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
			LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
		</if>
	</select>
</mapper>