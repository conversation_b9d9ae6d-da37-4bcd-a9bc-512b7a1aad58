<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.ShopGoodsLabelMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.ShopGoodsLabelDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="ware_id" jdbcType="BIGINT" property="wareId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
  </resultMap>

    <resultMap id="shopGoodsSkuLabelDTO" type="com.pes.jd.model.DTO.ShopGoodsSkuLabelDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="ware_id" jdbcType="BIGINT" property="skuId" />
        <result column="label" jdbcType="VARCHAR" property="label" />
    </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, ware_id, label
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${tableName}
    where id = #{entity.id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey">
    delete from  ${tableName}
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ShopGoodsLabelDO" keyProperty="entity.id" useGeneratedKeys="true">
    insert into ${tableName} (id, shop_id, ware_id,
      label)
    values (#{entity.id,jdbcType=BIGINT}, #{entity.shopId,jdbcType=BIGINT}, #{entity.wareId,jdbcType=BIGINT},
      #{entity.label,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopGoodsLabelDO">
    insert into ${tableName}
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="wareId != null">
        ware_id,
      </if>
      <if test="label != null">
        label,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="wareId != null">
        #{wareId,jdbcType=BIGINT},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKey" parameterType="map">
      update ${tableName}
      set
      <if test="record.shopId != null">
          shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.wareId != null">
          ware_id = #{record.wareId,jdbcType=BIGINT},
      </if>
      <if test="record.label != null">
          label = #{record.label,jdbcType=VARCHAR}
      </if>
      where id = #{record.id,jdbcType=BIGINT}
  </update>
    <select id="searchByShopId" resultType="java.lang.Long">
        select  distinct ware_id
        from ${tableName}
        where shop_id=#{shopId}
    </select>
    <select id="selectGoodsLabelById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        where id=#{id,jdbcType=BIGINT}
    </select>
    <select id="searchByWareIds" resultMap="shopGoodsSkuLabelDTO">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where
        shop_id = #{shopId} and
        ware_id in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>

    </select>
</mapper>