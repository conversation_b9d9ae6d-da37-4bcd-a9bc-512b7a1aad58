<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsChatlogMapper" >
  <resultMap id="ChatLogDTO" type="com.pes.jd.model.DTO.ChatLogDTO" >
	  <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer" property="buyerNick" jdbcType="VARCHAR" />
    <result column="time" property="time" jdbcType="TIMESTAMP" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="direction" property="direction" jdbcType="TINYINT" />
	  <result column="sid" property="sid" jdbcType="VARCHAR" />
  </resultMap>

	<resultMap id="ReceiveBuyerVO" type="com.pes.jd.model.VO.ReceiveBuyerVO" >
		<result column="buyer" property="buyerNick" jdbcType="VARCHAR" />
		<result column="firstTime" property="time" jdbcType="TIMESTAMP" />
	</resultMap>
  
  <sql id="base_field" >
    id, shop_id, cs_nick, buyer, time, content, direction,sid
  </sql>
  
 
  <select id="selectBuyerChatlogs" parameterType="map" resultMap="ChatLogDTO">
	SELECT <include refid="base_field" />
	FROM ${tableName}
	<where>
		shop_id = #{shopId}
		<choose>
			<when test="sid != null and '' != sid">
				AND sid = #{sid}
			</when>
			<otherwise>
				AND time BETWEEN #{startDate} AND #{endDate}
			</otherwise>
		</choose>
		<if test="csNickLst != null and csNickLst.size()>0">
			AND cs_nick IN
			<foreach collection="csNickLst" item="csNick" open="(" close=")"
				separator=",">
				#{csNick}
			</foreach>
		</if>
		<if test="buyerNick != null and '' != buyerNick">
			AND buyer = #{buyerNick} 
		</if>
		<if test="keyWord != null and '' != keyWord">
			AND content LIKE CONCAT(CONCAT('%',#{keyWord}),'%') 
		</if>
		ORDER BY buyer,time ASC , direction DESC
	</where>
  </select>
 
  <select id="searchCsChatlogs" parameterType="map" resultMap="ChatLogDTO">
	SELECT <include refid="base_field" />
	FROM ${tableName}
	<where>
		time BETWEEN #{startDate} AND #{endDate}
		<if test="buyerLst != null and buyerLst.size()>0">
			AND buyer IN
			<foreach collection="buyerLst" item="buyer" open="(" close=")"
				separator=",">
				#{buyer}
			</foreach>
		</if>
		<if test="csNick != null and '' != csNick">
			AND cs_nick = #{csNick} 
		</if>
		AND shop_id = #{shopId}
		ORDER BY buyer,time ASC
	</where>
  </select>
 
  <select id="searchReceiveNotEmptyChatLogByChatPeerLst" parameterType="map" resultType="java.lang.String">
	SELECT DISTINCT buyer
	FROM ${tableName}
	<where>
		time BETWEEN #{startDate} AND #{endDate}
		<if test="receiveBuyerLst != null and receiveBuyerLst.size()>0">
			AND buyer IN
			<foreach collection="receiveBuyerLst" item="buyer" open="(" close=")"
				separator=",">
				#{buyer}
			</foreach>
		</if>
		<if test="csNick != null and '' != csNick">
			AND cs_nick = #{csNick} 
		</if>
		AND shop_id = #{shopId}
		ORDER BY buyer,time ASC
	</where>
  </select>

	<select id="searchReceiveNotEmptyChatLogVOByChatPeerLst" parameterType="map" resultMap="ReceiveBuyerVO">
		SELECT buyer, min(time) as firstTime
		FROM ${tableName}
		<where>
			time BETWEEN #{startDate} AND #{endDate}
			<if test="receiveBuyerLst != null and receiveBuyerLst.size()>0">
				AND buyer IN
				<foreach collection="receiveBuyerLst" item="buyer" open="(" close=")"
						 separator=",">
					#{buyer}
				</foreach>
			</if>
			<if test="csNick != null and '' != csNick">
				AND cs_nick = #{csNick}
			</if>
			AND shop_id = #{shopId}
			GROUP BY buyer
			ORDER BY buyer,time ASC
		</where>
	</select>
  
</mapper>