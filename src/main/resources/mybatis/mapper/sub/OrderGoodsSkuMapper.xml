<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.OrderGoodsSkuMapper">

    <select id="selectByOrderIdsForRefundDataAnalysis" resultType="com.pes.jd.model.DO.OrderGoodsSkuDO">
        select
        order_id,item_sku_id
        from ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <!--订单编号-->
            <if test="orderId != null and orderId.size() > 0">
                AND order_id IN
                <foreach collection="orderId" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="skuIds != null and skuIds.size() > 0">
                AND item_sku_id IN
                <foreach collection="skuIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <!--日期-->
            <if test="startDate != null and endDate != null">
                <!--订单下单时间-->
                <if test="dateType != null and dateType == 3">
                    AND created BETWEEN #{startDate} AND #{endDate}
                </if>
                <!--订单付款时间-->
                <if test="dateType != null and dateType == 4">
                    AND pay_time BETWEEN #{startDate} AND #{endDate}
                </if>
            </if>
        </where>
    </select>

    <select id="selectOrderGoodsSkuByOrderIds" resultType="com.pes.jd.model.DTO.OrderDetailDTO">
        select
        order_id as orderId,item_sku_id as itemSkuId
        from ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            AND created BETWEEN #{startDate} AND #{endDate}
            <!--订单编号-->
                AND order_id IN
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
        </where>
    </select>
</mapper>