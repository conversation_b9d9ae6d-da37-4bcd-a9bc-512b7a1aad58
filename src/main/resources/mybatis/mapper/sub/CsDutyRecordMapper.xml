<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsDutyRecordMapper">
    <resultMap id="CsDutyRecordDO" type="com.pes.jd.model.DO.CsDutyRecordDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="first_online_datetime" property="firstOnlineDateTime" jdbcType="TIMESTAMP"/>
        <result column="last_offline_datetime" property="lastOfflineDateTime" jdbcType="TIMESTAMP"/>
        <result column="login_times_num" property="loginTimesNum" jdbcType="INTEGER"/>
        <result column="login_duration_time" property="loginDurationTime" jdbcType="BIGINT"/>
        <result column="rceive_duration_time" property="rceiveDurationTime" jdbcType="BIGINT"/>
        <result column="hangup_duration_time" property="hangupDurationTime" jdbcType="BIGINT"/>
        <result column="offline_duration_time" property="offlineDurationTime" jdbcType="BIGINT"/>
        <result column="suspend_num" property="suspendNum" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="CsDutyRecordPerformanceDTO" type="com.pes.jd.model.DTO.CsDutyRecordPerformanceDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="first_online_datetime" property="firstOnlineDateTime" jdbcType="TIMESTAMP"/>
        <result column="last_offline_datetime" property="lastOfflineDateTime" jdbcType="TIMESTAMP"/>
        <result column="login_times_num" property="loginTimesNum" jdbcType="INTEGER"/>
        <result column="login_duration_time" property="loginDurationTime" jdbcType="BIGINT"/>
        <result column="rceive_duration_time" property="rceiveDurationTime" jdbcType="BIGINT"/>
        <result column="hangup_duration_time" property="hangupDurationTime" jdbcType="BIGINT"/>
        <result column="offline_duration_time" property="offlineDurationTime" jdbcType="BIGINT"/>
        <result column="suspend_num" property="suspendNum" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="performance">

    id, shop_id, cs_nick, date, first_online_datetime, last_offline_datetime, login_times_num,
    login_duration_time, rceive_duration_time, hangup_duration_time, offline_duration_time,suspend_num

  </sql>

    <select id="queryCsDutyRecordByCsNicksByDate" parameterType="map" resultMap="CsDutyRecordDO">
        SELECT *
        FROM ${tableName}
        WHERE date BETWEEN #{startDate} AND #{endDate}
        AND cs_nick IN
        <foreach collection="csNicks" item="csNick" open="(" close=")" separator=",">
            #{csNick}
        </foreach>
        AND shop_id = #{shopId}
    </select>


    <select id="searchShopDate" resultMap="CsDutyRecordPerformanceDTO">
        select
        <include refid="performance"/>
        from ${tableName}
        where shop_id = #{shopId}
        and date between #{startDate} and #{endDate}
        <if test="nicks!=null and nicks.size()>0">
            and cs_nick in
            <foreach collection="nicks" item="nick" separator="," open="(" close=")">
                #{nick}
            </foreach>
        </if>
        <if test="filterDates != null and filterDates.size()>0">
            and date not in
            <foreach collection="filterDates" open="(" close=")" separator="," item="d">
                #{d}
            </foreach>
        </if>
        order by date desc
    </select>
    <select id="search" resultMap="CsDutyRecordPerformanceDTO">
      select * from pes_jd_sub1.pes_cs_duty_record
    </select>


</mapper>