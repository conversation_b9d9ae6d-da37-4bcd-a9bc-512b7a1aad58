<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.OrderRefundMapper">


    <!--查订单退款表-->
    <select id="selectByRefundIdAndOrderIdMoreForRefundDataAnalysis" resultType="com.pes.jd.model.DTO.OrderRefundDTO">
        SELECT
        refund_id,
        order_id,
        created,
        modified,
        `status`,
        refund_amount,
        refund_num,
        buyer_nick,
        reason,
        `type`,
        sku_id,
        complete_time
        FROM
        ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>

            <!--订单编号-->
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <!--退款编号-->
            <if test="refundId != null">
                AND refund_id = #{refundId}
            </if>

            <!--顾客ID-->
            <if test="customer != null and customer.trim().length() > 0">
                AND buyer_nick = #{customer}
            </if>
            <!--退款原因-->
            <!--<if test="reason != null and reason.trim().length() > 0">-->
            <!--AND reason = #{reason}-->
            <!--</if>-->

            <!--退款状态-->
            <choose>
                <!--退款处理中-->
                <when test='refundStatus == "1"'>
                    AND ((`type` = 1 AND `status` NOT IN (3, 4)) OR (`type` = 2 AND `status` NOT IN (13, 14)))
                </when>
                <!--退款已关闭-->
                <when test='refundStatus == "2"'>
                    AND ((`type` = 1 AND `status` = 4) OR (`type` = 2 AND `status` = 14))
                </when>
                <!--退款已完成-->
                <when test='refundStatus == "3"'>
                    AND ((`status` = 3) OR (`status` = 13))
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <!--日期-->
                <!--退款成功时间-->
                <when test="dateType == 1">
                    AND (( status = 3 AND modified BETWEEN #{startDate} AND #{endDate})
                    OR (status = 13 AND complete_time BETWEEN #{startDate} AND #{endDate}))
                </when>
                <!--退款申请时间-->
                <when test="dateType == 2">
                    AND created BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType == 3">
                    AND created BETWEEN #{startDate} AND #{endDate}
                </when>
                <when test="dateType == 4">
                    AND created BETWEEN #{startDate} AND #{endDate}
                </when>
            </choose>

            <if test="skuIds != null and skuIds.size() > 0">
                AND ( ISNULL(sku_id) OR sku_id IN
                <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
                )
            </if>
        </where>
    </select>

    <select id="selectShopIdAndOrderIdsAndDate" resultType="long">
        SELECT order_id
        FROM
        ${tableName}
        WHERE shop_id = #{shopId}
        <!--订单编号-->
        <if test="orderIds != null and orderIds.size() > 0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        AND created BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>