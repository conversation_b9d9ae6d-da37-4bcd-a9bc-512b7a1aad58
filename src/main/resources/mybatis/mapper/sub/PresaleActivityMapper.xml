<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.PresaleActivityMapper">

    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.PresaleActivityDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="VARCHAR"/>
        <result column="activity_name" property="activityName" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="earnest" property="earnest" jdbcType="DOUBLE"/>
        <result column="step_one_num" property="stepOneNum" jdbcType="INTEGER"/>
        <result column="step_two_num" property="stepTwoNum" jdbcType="INTEGER"/>
        <result column="step_three_num" property="stepThreeNum" jdbcType="INTEGER"/>
        <result column="step_one_price" property="stepOnePrice" jdbcType="DOUBLE"/>
        <result column="step_two_price" property="stepTwoPrice" jdbcType="DOUBLE"/>
        <result column="step_three_price" property="stepThreePrice" jdbcType="DOUBLE"/>
        <result column="presale_start_time" property="presaleStartTime" jdbcType="TIMESTAMP"/>
        <result column="presale_end_time" property="presaleEndTime" jdbcType="TIMESTAMP"/>
        <result column="balance_start_time" property="balanceStartTime" jdbcType="TIMESTAMP"/>
        <result column="balance_end_time" property="balanceEndTime" jdbcType="TIMESTAMP"/>
        <result column="ship_time" property="shipTime" jdbcType="TIMESTAMP"/>
        <result column="current_price" property="currentPrice" jdbcType="DOUBLE"/>
        <result column="current_step" property="currentStep" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="banlance_price" property="banlancePrice" jdbcType="DOUBLE"/>
        <result column="presale_pay_type" property="presalePayType" jdbcType="INTEGER"/>
        <result column="ori_price" property="oriPrice" jdbcType="DOUBLE"/>
        <result column="presale_count" property="presaleCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, activity_id, activity_name, sku_id, shop_id, `type`, earnest, step_one_num, step_two_num, step_three_num,
    step_one_price, step_two_price, step_three_price, presale_start_time, presale_end_time,
    balance_start_time, balance_end_time, ship_time, current_price, current_step, create_time,
    banlance_price, presale_pay_type, ori_price, presale_count, status, cancel_time
  </sql>


    <select id="selectPresaleActivity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where shop_id=#{shopId} and
        activity_id = #{activityId}
    </select>


    <select id="selectPresaleActivityNow" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where shop_id=#{shopId} and (
        (
        #{nowDate} between
        presale_start_time and presale_end_time
        )

        or

        (
        #{nowDate} between balance_start_time and balance_end_time
        )

        ) and status = 1

        and balance_start_time is not null
        and balance_end_time is not null


    </select>


    <select id="selectShopReservePresaleByActId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where shop_id=#{shopId} and
        activity_id in
        <foreach collection="activityId" item="actId" open="(" close=")" separator=",">
            #{actId}
        </foreach>
        group by activity_id
    </select>


    <select id="selectShopPresaleActivityByShopId" resultMap="BaseResultMap">
        select
        activity_id,
        sku_id,
        shop_id,
        presale_start_time,
        presale_end_time,
        balance_start_time,
        balance_end_time
        from ${tableName}
        <where>
            shop_id=#{shopId}
        </where>
    </select>

    <select id="selectShopPresaleActivityByShopIdByActiveityId" resultMap="BaseResultMap">
        select
        activity_id,
        sku_id,
        shop_id,
        presale_start_time,
        presale_end_time,
        balance_start_time,
        balance_end_time
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and activity_id=#{activityId}
        </where>
    </select>
    <select id="selectByShopIdAndDateForPresalePerformance" resultType="com.pes.jd.model.DTO.PresaleActivityDTO">
        SELECT id, activity_id, activity_name, sku_id, shop_id, type, earnest, step_one_num, step_two_num,
        step_three_num,
        step_one_price, step_two_price, step_three_price, presale_start_time, presale_end_time,
        balance_start_time, balance_end_time, ship_time, current_price, current_step, create_time,
        banlance_price, presale_pay_type, ori_price, presale_count, status, cancel_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND NOT ((balance_end_time &lt; #{startDate}) OR (presale_start_time &gt; #{endDate}))
    </select>
    <select id="selectByShopIdAndSkuId" resultType="com.pes.jd.model.DTO.PresaleActivityDTO">
        SELECT id, activity_id, activity_name, sku_id, shop_id, type, earnest, step_one_num, step_two_num,
        step_three_num,
        step_one_price, step_two_price, step_three_price, presale_start_time, presale_end_time,
        balance_start_time, balance_end_time, ship_time, current_price, current_step, create_time,
        banlance_price, presale_pay_type, ori_price, presale_count, status, cancel_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND sku_id = #{skuId,jdbcType=BIGINT}
        ORDER BY balance_end_time DESC
    </select>


</mapper>