<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.ShopGoodsSkuAssociativeMapper">
  <resultMap id="ShopGoodsSkuAssociativeDO" type="com.pes.jd.model.DO.ShopGoodsSkuAssociativeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="sku1_id" jdbcType="BIGINT" property="sku1Id" />
    <result column="sku2_id" jdbcType="BIGINT" property="sku2Id" />
  </resultMap>

  <resultMap id="ShopGoodsSkuAssociativeDTO"
             type="com.pes.jd.model.DTO.ShopGoodsSkuAssociativeDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="sku1_id" jdbcType="BIGINT" property="sku1Id" />
    <result column="sku2_id" jdbcType="BIGINT" property="sku2Id" />
  </resultMap>


  <sql id="Base_Column_List">
    id, shop_id, sku1_id, sku2_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ShopGoodsSkuAssociativeDO">
    select 
    <include refid="Base_Column_List" />
    from pes_shop_goods_sku_associative_2019_05
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="searchLstBySkuIds" resultMap="ShopGoodsSkuAssociativeDTO">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where sku2_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="skuId">
            #{skuId}
        </foreach>
    </select>
    <select id="searchLstBySkuIds2" resultMap="ShopGoodsSkuAssociativeDTO">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where sku1_id in
        <foreach collection="skuIds" open="(" close=")" separator="," item="skuId">
            #{skuId}
        </foreach>
    </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_shop_goods_sku_associative_2019_05
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteBySkuId" parameterType="java.lang.Long">
    delete from ${tableName}
    where sku1_id = #{skuId} or sku2_id = #{skuId}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ShopGoodsSkuAssociativeDO">
    insert into ${tableName} (id, shop_id, sku1_id,
      sku2_id)
    values (#{record.id,jdbcType=BIGINT}, #{record.shopId,jdbcType=BIGINT}, #{record.sku1Id,jdbcType=BIGINT},
      #{record.sku2Id,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopGoodsSkuAssociativeDO">
    insert into pes_shop_goods_sku_associative_2019_05
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="sku1Id != null">
        sku1_id,
      </if>
      <if test="sku2Id != null">
        sku2_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="sku1Id != null">
        #{sku1Id,jdbcType=BIGINT},
      </if>
      <if test="sku2Id != null">
        #{sku2Id,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopGoodsSkuAssociativeDO">
    update pes_shop_goods_sku_associative_2019_05
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="sku1Id != null">
        sku1_id = #{sku1Id,jdbcType=BIGINT},
      </if>
      <if test="sku2Id != null">
        sku2_id = #{sku2Id,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ShopGoodsSkuAssociativeDO">
    update pes_shop_goods_sku_associative_2019_05
    set shop_id = #{shopId,jdbcType=BIGINT},
      sku1_id = #{sku1Id,jdbcType=BIGINT},
      sku2_id = #{sku2Id,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>