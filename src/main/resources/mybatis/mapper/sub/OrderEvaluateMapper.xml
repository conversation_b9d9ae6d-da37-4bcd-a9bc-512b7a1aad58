<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderEvaluateMapper">

	<resultMap id="OrderEvaluateDO" type="com.pes.jd.model.DO.OrderEvaluate">
		<id column="id" property="id" jdbcType="INTEGER" />
		<id column="trade_id" property="tradeId" jdbcType="BIGINT" />
		<result column="order_id" property="orderId" jdbcType="BIGINT" />
		<result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
		<result column="result" property="result" jdbcType="VARCHAR" />
		<result column="created" property="created" jdbcType="TIMESTAMP" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
	</resultMap>

	<sql id="base_field">
		id, trade_id, order_id, buyer_nick, result, created, shop_id
	</sql>
	
	<insert id="insertOrderEvaluate" parameterType="com.pes.jd.model.DO.OrderEvaluate">
		INSERT INTO pes_trade_evaluate (id, trade_id, order_id, buyer_nick, result, created, shop_id)
		VALUES 
		(
			#{id,jdbcType=INTEGER}, #{tradeId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{buyerNick,jdbcType=VARCHAR}, #{result,jdbcType=VARCHAR}, #{created,jdbcType=TIMESTAMP}, #{shopId,jdbcType=BIGINT}
		)
	</insert>
		
	<delete id="deleteOrderEvaluateById" parameterType="com.pes.jd.model.DO.OrderEvaluate">
		DELETE FROM pes_trade_evaluate
		WHERE
			id = #{id,jdbcType=BIGINT}
	</delete>
	
	<update id="updateOrderEvaluateById" parameterType="com.pes.jd.model.DO.OrderEvaluate">
		UPDATE pes_trade_evaluate
		<set>
			<if test="orderId != null">
				order_id = #{orderId,jdbcType=BIGINT},
			</if>
			<if test="buyerNick != null">
				buyer_nick = #{buyerNick,jdbcType=VARCHAR},
			</if>
			<if test="result != null">
				result = #{result,jdbcType=VARCHAR},
			</if>
			<if test="created != null">
				created = #{created,jdbcType=TIMESTAMP},
			</if>
			<if test="shopId != null">
				shop_id = #{shopId,jdbcType=BIGINT},
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=BIGINT}
	</update>
	
	<select id="getOrderEvaluateById" resultMap="OrderEvaluateDO" parameterType="com.pes.jd.model.DO.OrderEvaluate">
		SELECT
			<include refid="base_field" />
		FROM pes_trade_evaluate
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</select>

	<select id="selectDateShopNick" resultType="java.util.Map" parameterType="map">
		SELECT
		<include refid="base_field" />
		FROM ${tableName}
		WHERE
		created BETWEEN ${begin} AND ${end}
		AND shop_id = #{shopId}
		AND  buyer_nick = #{nick}
	</select>

    <!-- for client performance -->
	<select id="selectOrderEvaluateByNickAndShop" resultType="java.util.ArrayList" parameterType="java.util.Map">
        SELECT eval.cs_nick,eval.eval_date,
        sum(eval.good_eval_num) good_eval_num,
        sum(eval.neutral_eval_num) neutral_eval_num,
        sum(eval.bad_eval_num) bad_eval_num
        FROM
        (
        <foreach collection="schemaIds" item="schemaId" open="" close="" separator="UNION ALL">
            SELECT cs_nick,eval_date,neutral_eval_num,bad_eval_num,good_eval_num
            FROM ${schemaId}.pes_cs_evaluation
            WHERE
            shopId = #{shopId}
            <if test="nick != null and '' != nick">
                AND cs_nick = #{nick}
            </if>
            AND eval_date BETWEEN #{startDate} AND #{endDate}
            group by cs_nick,eval_date
        </foreach>
        ) eval
        GROUP BY  eval.cs_nick
	</select>
</mapper>