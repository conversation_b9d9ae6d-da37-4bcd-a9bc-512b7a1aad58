<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.OrderPresaleMapper">

    <resultMap id="OrderPresaleDTO" type="com.pes.jd.model.VO.OrderPresaleVO">
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="bargain_time" property="bargainTime" jdbcType="TIMESTAMP"/>
        <result column="balance_time" property="balanceTime" jdbcType="TIMESTAMP"/>
        <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="BIT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="to_tail_paid_cs" property="tailPaidCs" jdbcType="VARCHAR"/>
        <result column="is_pes_order" property="isPesOrder" jdbcType="BIT"/>
        <result column="goods_num" property="goodsNum" jdbcType="INTEGER"/>
        <result column="pay_balance_real" property="payment" jdbcType="DOUBLE"/>
    </resultMap>

    <select id="selectCountForOrderPresaleAnalysis" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--订单编号-->
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <!--顾客ID-->
        <if test="buyerNick != null and buyerNick.trim().length() > 0">
            AND buyer_nick = #{buyerNick}
        </if>
        <choose>
            <!--时间-->
            <when test="dateType == 1"><!--付尾款时间-->
                AND balance_time between #{startDate} AND #{endDate}
            </when>
            <when test="dateType == 2"><!--付定金时间-->
                AND bargain_time between #{startDate} AND #{endDate}
            </when>
            <when test="dateType == 3"><!--下单时间-->
                AND create_time between #{startDate} AND #{endDate}
            </when>
            <!--交易状态-->
            <when test="tradeType == 1"><!--成交-->
                AND order_status IN(2,3,23,33)
            </when>
            <when test="tradeType == 2"><!--待付定金/全款-->
                AND order_status IN(0,12,31)
            </when>
            <when test="tradeType == 3"><!--待付尾款-->
                AND order_status IN(1,13,22)
            </when>
            <when test="tradeType == 4"><!--付定金流失-->
                AND order_status IN(11,121,990,992)
            </when>
            <when test="tradeType == 5"><!--付尾款流失-->
                AND order_status IN(21,131,221,991,993,994)
            </when>
            <when test="tradeType == 6"><!--付全款流失-->
                AND order_status IN(311,995)
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="selectForOrderPresaleAnalysis" resultMap="OrderPresaleDTO">
        SELECT
        order_id,buyer_nick, sku_id, sku_name, create_time, bargain_time, balance_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--订单编号-->
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <!--顾客ID-->
        <if test="buyerNick != null and buyerNick.trim().length() > 0">
            AND buyer_nick = #{buyerNick}
        </if>
        <choose>
            <!--时间-->
            <when test="dateType == 1"><!--付尾款时间-->
                AND balance_time between #{startDate} AND #{endDate}
            </when>
            <when test="dateType == 2"><!--付定金时间-->
                AND bargain_time between #{startDate} AND #{endDate}
            </when>
            <when test="dateType == 3"><!--下单时间-->
                AND create_time between #{startDate} AND #{endDate}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <choose>
            <!--交易状态-->
            <when test="tradeType == 1"><!--成交-->
                AND order_status IN(2,3,23,33)
            </when>
            <when test="tradeType == 2"><!--待付定金/全款-->
                AND order_status IN(0,12,31)
            </when>
            <when test="tradeType == 3"><!--待付尾款-->
                AND order_status IN(1,13,22)
            </when>
            <when test="tradeType == 4"><!--付定金流失-->
                AND order_status IN(11,121,990,992)
            </when>
            <when test="tradeType == 5"><!--付尾款流失-->
                AND order_status IN(21,131,221,991,993,994)
            </when>
            <when test="tradeType == 6"><!--付全款流失-->
                AND order_status IN(311,995)
            </when>
        </choose>
        <if test="length > 0">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <select id="selectOrderIdByShopIdAndOrderIdsAndDate" resultType="long">
        SELECT order_id
        FROM
        ${tableName}
        WHERE shop_id = #{shopId}
        <!--订单编号-->
        <if test="orderIds != null and orderIds.size() > 0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        AND create_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectCsNickByOrderIdsForPresaleAnalysis" resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        SELECT order_id, cs_nick, `type`, is_pes_order
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--订单编号-->
        <if test="orderIds != null and orderIds.size() > 0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        <if test="csNickLst != null and csNickLst.size() > 0">
            AND cs_nick IN
            <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                #{nick}
            </foreach>
        </if>
        AND `date` BETWEEN #{startDate} AND #{endDate}
        AND is_presale = 1
    </select>

    <select id="selectCountOrderPresaleAndCsOrderBindForOrderPresaleAnalysis"
            resultType="int">
        SELECT
        COUNT(1)
        FROM (
        <foreach collection="orderPresaleTables" item="table" separator="UNION">
            SELECT
            order_id
            FROM ${table.tableName}
            WHERE shop_id = #{shopId}
            <!--订单编号-->
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <!--顾客ID-->
            <if test="buyerNick != null and buyerNick.trim().length() > 0">
                AND buyer_nick = #{buyerNick}
            </if>
            <choose>
                <!--时间-->
                <when test="dateType == 1"><!--付尾款时间-->
                    AND balance_time between #{startDate} AND #{endDate}
                </when>
                <when test="dateType == 2"><!--付定金时间-->
                    AND bargain_time between #{startDate} AND #{endDate}
                </when>
                <when test="dateType == 3"><!--下单时间-->
                    AND create_time between #{startDate} AND #{endDate}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <!--交易状态-->
                <when test="tradeType == 1"><!--成交-->
                    AND order_status IN(2,3,23,33)
                </when>
                <when test="tradeType == 2"><!--待付定金/全款-->
                    AND order_status IN(0,12,31)
                </when>
                <when test="tradeType == 3"><!--待付尾款-->
                    AND order_status IN(1,13,22)
                </when>
                <when test="tradeType == 4"><!--付定金流失-->
                    AND order_status IN(11,121,990,992)
                </when>
                <when test="tradeType == 5"><!--付尾款流失-->
                    AND order_status IN(21,131,221,991,993,994)
                </when>
                <when test="tradeType == 6"><!--付全款流失-->
                    AND order_status IN(311,995)
                </when>
            </choose>
            <if test="filterOrderIds!=null and filterOrderIds.size()>0">
                 AND order_id NOT IN
                <foreach collection="filterOrderIds" item="filterOrderId" open="(" close=")" separator=",">
                    #{filterOrderId}
                </foreach>
            </if>
        </foreach>
        ) op
        <if test="groupId != null and groupId.trim().length() > 0">
            INNER JOIN
            (
            <foreach collection="csOrderBindTables" item="table" separator="UNION">
                SELECT
                DISTINCT order_id
                FROM ${table.tableName}
                <where>
                    <!--订单编号-->
                    <if test="orderId != null">
                        AND order_id = #{orderId}
                    </if>
                    AND shop_id = #{shopId}
                    <!--顾客ID-->
                    <if test="buyerNick != null and buyerNick.trim().length() > 0">
                        AND buyer_nick = #{buyerNick}
                    </if>
                    <if test="csNickLst != null and csNickLst.size() > 0">
                        AND cs_nick IN
                        <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                            #{nick}
                        </foreach>
                    </if>
                    AND `date` BETWEEN #{table.beginDate} AND #{table.endDate}
                    AND is_presale = 1
                </where>
            </foreach>
            ) cob ON op.order_id = cob.order_id
        </if>
    </select>

    <select id="selectOrderPresaleForOrderPresaleAnalysis"
            resultType="com.pes.jd.model.VO.OrderPresaleVO">
        SELECT
        op.order_id, op.buyer_nick, op.sku_id, op.create_time, op.bargain_time, op.balance_time,op.goods_num,
        op.pay_bargain_real, op.pay_balance_real
        FROM (
        <foreach collection="orderPresaleTables" item="table" separator="UNION">
            SELECT
            order_id,buyer_nick, sku_id, sku_name, create_time, bargain_time, balance_time,goods_num,pay_bargain_real,pay_balance_real
            FROM ${table.tableName}
            WHERE shop_id = #{shopId}
            <!--订单编号-->
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <!--顾客ID-->
            <if test="buyerNick != null and buyerNick.trim().length() > 0">
                AND buyer_nick = #{buyerNick}
            </if>
            <choose>
                <!--时间-->
                <when test="dateType == 1"><!--付尾款时间-->
                    AND balance_time between #{startDate} AND #{endDate}
                </when>
                <when test="dateType == 2"><!--付定金时间-->
                    AND bargain_time between #{startDate} AND #{endDate}
                </when>
                <when test="dateType == 3"><!--下单时间-->
                    AND create_time between #{startDate} AND #{endDate}
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <choose>
                <!--交易状态-->
                <when test="tradeType == 1"><!--成交-->
                    AND order_status IN(2,3,23,33)
                </when>
                <when test="tradeType == 2"><!--待付定金/全款-->
                    AND order_status IN(0,12,31)
                </when>
                <when test="tradeType == 3"><!--待付尾款-->
                    AND order_status IN(1,13,22)
                </when>
                <when test="tradeType == 4"><!--付定金流失-->
                    AND order_status IN(11,121,990,992)
                </when>
                <when test="tradeType == 5"><!--付尾款流失-->
                    AND order_status IN(21,131,221,991,993,994)
                </when>
                <when test="tradeType == 6"><!--付全款流失-->
                    AND order_status IN(311,995)
                </when>
            </choose>
            <if test="filterOrderIds!=null and filterOrderIds.size()>0">
                AND order_id NOT IN
                <foreach collection="filterOrderIds" item="filterOrderId" open="(" close=")" separator=",">
                    #{filterOrderId}
                </foreach>
            </if>
        </foreach>
        ) op
        ORDER BY
        <choose>
            <!--时间-->
            <when test="dateType == 1"><!--付尾款时间-->
                balance_time ASC, create_time ASC
            </when>
            <when test="dateType == 2"><!--付定金时间-->
                bargain_time ASC, create_time ASC
            </when>
            <when test="dateType == 3"><!--下单时间-->
                create_time ASC
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="sortPageQuery != null and sortPageQuery.size != null and sortPageQuery.size > 0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>

    <select id="selectOrderPresaleUnbalance" resultType="com.pes.jd.model.VO.OrderPresaleUnbalanceVO">
        SELECT order_id, buyer_nick
        FROM $(tableName)
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        AND sku_id = #{skuId,jdbcType=BIGINT}
        AND balance_end_time = #{balanceEndTime,jdbcType=TIMESTAMP}
        AND balance_time IS NULL
        AND bargain_time IS NOT NULL
    </select>


    <select id="selectPageByShopIdAndDateAndOrderIdAndBuyerNick" resultType="com.pes.jd.model.VO.OrderPresaleVO">
        SELECT
        order_id, ifnull(pay_bargain_real,0)+ifnull(pay_balance_real,0)   as payment, order_status, create_time, bargain_time, balance_time, buyer_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <if test="buyerNick != null and buyerNick != ''">
            AND buyer_nick = #{buyerNick}
        </if>
        <if test="filterOrderIds!=null and filterOrderIds.size()>0">
            AND order_id NOT IN
            <foreach collection="filterOrderIds" item="filterOrderId" open="(" close=")" separator=",">
                #{filterOrderId}
            </foreach>
        </if>
        AND create_time BETWEEN #{beginDate} AND #{endDate}
        ORDER BY create_time DESC
        <if test="startIndex != null and length != null and length > 0">
            LIMIT #{startIndex}, #{length}
        </if>
    </select>

    <select id="selectCountByShopIdAndDateAndOrderIdAndBuyerNick" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <if test="buyerNick != null and buyerNick != ''">
            AND buyer_nick = #{buyerNick}
        </if>
        <if test="filterOrderIds!=null and filterOrderIds.size()>0">
            AND order_id NOT IN
            <foreach collection="filterOrderIds" item="filterOrderId" open="(" close=")" separator=",">
                #{filterOrderId}
            </foreach>
        </if>
        AND create_time BETWEEN #{beginDate} AND #{endDate}
    </select>

    <select id="selectOrderInfoByOrderIds" resultType="com.pes.jd.model.VO.OrderPresaleVO">
        SELECT
        order_id, ifnull(pay_bargain_real,0)+ifnull(pay_balance_real,0) as payment, order_status, create_time, bargain_time, balance_time, buyer_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <choose>
            <when test="orderIds != null and orderIds.size() > 0">
                <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                (0)
            </otherwise>
        </choose>
    </select>
    <select id="selectFullPayOrderInfoByOrderIds" resultMap="OrderPresaleDTO">
        SELECT
        shop_id,buyer_nick,presale_id,sku_id,sku_name,goods_num,order_id,yushou_price,freight,
        order_pay_type,order_type,order_status,create_time,update_time,pay_bargain_real,pay_bargain_plan,
        bargain_time,pay_balance_real,pay_balance_plan,balance_time,
        balance_start_time,balance_end_time,yn,order_time,balance_end_time_plan,company_id,out_stock_time
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
<!--        and order_pay_type = 1-->
        AND order_id IN
        <choose>
            <when test="orderIds != null and orderIds.size() > 0">
                <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                (0)
            </otherwise>
        </choose>
    </select>

    <select id="selectOrderIdsUnbalance" resultType="java.lang.Long">
        SELECT
        order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND create_time between #{beginDate} AND #{endDate}
        AND balance_time IS NULL
        AND bargain_time IS NOT NULL
    </select>

    <select id="selectOrderIdsByCreated" resultType="java.lang.Long">
        select order_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          and create_time between  #{startDate} and #{endDate}
    </select>
</mapper>