<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsOrderIndexMapper">

    <resultMap id="CsPerformanceOrderIndexDTO"
               type="com.pes.jd.model.DTO.CsPerformanceOrderIndexDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_flag" jdbcType="TINYINT" property="orderFlag"/>
        <result column="order_created" jdbcType="TIMESTAMP" property="orderCreated"/>
        <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
        <result column="is_goods_filte" jdbcType="BIT" property="isGoodsFilte"/>
        <result column="is_mrn_filter" jdbcType="BIT" property="isMrnFilter"/>
        <result column="bc_chat_num" jdbcType="INTEGER" property="bcChatNum"/>
        <result column="bc_reply_num" jdbcType="INTEGER" property="bcReplyNum"/>
        <result column="bc_chat_round_num" jdbcType="INTEGER" property="bcChatRoundNum"/>
        <result column="bc_first_reply_date" jdbcType="TIMESTAMP"
                property="bcFirstReplyDate"/>
        <result column="bc_last_reply_date" jdbcType="TIMESTAMP"
                property="bcLastReplyDate"/>
        <result column="bc_first_chat_date" jdbcType="TIMESTAMP"
                property="bcFirstChatDate"/>
        <result column="bc_last_chat_date" jdbcType="TIMESTAMP"
                property="bcLastChatDate"/>
        <result column="bp_reply_num" jdbcType="INTEGER" property="bpReplyNum"/>
        <result column="bp_chat_round_num" jdbcType="INTEGER" property="bpChatRoundNum"/>
        <result column="bp_first_reply_date" jdbcType="TIMESTAMP"
                property="bpFirstReplyDate"/>
        <result column="bp_last_chat_date" jdbcType="TIMESTAMP"
                property="bpLastChatDate"/>
        <result column="bp_last_reply_date" jdbcType="TIMESTAMP"
                property="bpLastReplyDate"/>
        <result column="bp_chat_num" jdbcType="INTEGER" property="bpChatNum"/>
        <result column="ac_first_reply_date" jdbcType="TIMESTAMP"
                property="acFirstReplyDate"/>
        <result column="ac_first_chat_date" jdbcType="TIMESTAMP"
                property="acFirstChatDate"/>
        <result column="ap_first_chat_date" jdbcType="TIMESTAMP"
                property="apFirstChatDate"/>
        <result column="ap_first_reply_date" jdbcType="TIMESTAMP"
                property="apFirstReplyDate"/>
        <result column="last_reply_date" jdbcType="TIMESTAMP" property="lastReplyDate"/>
        <result column="first_reply_date" jdbcType="TIMESTAMP"
                property="firstReplyDate"/>
        <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate"/>
        <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate"/>
        <result column="is_assit_order_create" jdbcType="BIT"
                property="isAssitOrderCreate"/>
        <result column="is_assit_order_in_followup" jdbcType="BIT"
                property="isAssitOrderInFollowup"/>
        <result column="is_assit_order_pay" jdbcType="BIT" property="isAssitOrderPay"/>
        <result column="aidOrderNum" jdbcType="INTEGER" property="aidOrderNum"/>
        <result column="aidPayNum" jdbcType="INTEGER" property="aidPayNum"/>
        <result column="aidFollowNum" jdbcType="INTEGER" property="aidFollowNum"/>
        <result column="aidOrderAmount" jdbcType="DOUBLE" property="aidOrderAmount"/>
        <result column="aidPayAmount" jdbcType="DOUBLE" property="aidPayAmount"/>
        <result column="aidFollowAmount" jdbcType="DOUBLE" property="aidFollowAmount"/>
    </resultMap>


    <resultMap id="CsOrderIndexDO" type="com.pes.jd.model.DO.CsOrderIndex">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="banner_flag" jdbcType="TINYINT" property="bannerFlag"/>
        <result column="order_created" jdbcType="TIMESTAMP" property="orderCreated"/>
        <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
        <result column="is_goods_filte" jdbcType="BIT" property="isGoodsFilte"/>
        <result column="is_mrn_filter" jdbcType="BIT" property="isMrnFilter"/>
        <result column="bc_chat_num" jdbcType="INTEGER" property="bcChatNum"/>
        <result column="bc_reply_num" jdbcType="INTEGER" property="bcReplyNum"/>
        <result column="bc_chat_round_num" jdbcType="INTEGER" property="bcChatRoundNum"/>
        <result column="bc_first_reply_date" jdbcType="TIMESTAMP"
                property="bcFirstReplyDate"/>
        <result column="bc_last_reply_date" jdbcType="TIMESTAMP"
                property="bcLastReplyDate"/>
        <result column="bc_first_chat_date" jdbcType="TIMESTAMP"
                property="bcFirstChatDate"/>
        <result column="bc_last_chat_date" jdbcType="TIMESTAMP"
                property="bcLastChatDate"/>
        <result column="bp_reply_num" jdbcType="INTEGER" property="bpReplyNum"/>
        <result column="bp_chat_round_num" jdbcType="INTEGER" property="bpChatRoundNum"/>
        <result column="bp_first_reply_date" jdbcType="TIMESTAMP"
                property="bpFirstReplyDate"/>
        <result column="bp_last_chat_date" jdbcType="TIMESTAMP"
                property="bpLastChatDate"/>
        <result column="bp_last_reply_date" jdbcType="TIMESTAMP"
                property="bpLastReplyDate"/>
        <result column="bp_chat_num" jdbcType="INTEGER" property="bpChatNum"/>
        <result column="ac_first_reply_date" jdbcType="TIMESTAMP"
                property="acFirstReplyDate"/>
        <result column="ac_first_chat_date" jdbcType="TIMESTAMP"
                property="acFirstChatDate"/>
        <result column="ap_first_chat_date" jdbcType="TIMESTAMP"
                property="apFirstChatDate"/>
        <result column="ap_first_reply_date" jdbcType="TIMESTAMP"
                property="apFirstReplyDate"/>
        <result column="last_reply_date" jdbcType="TIMESTAMP" property="lastReplyDate"/>
        <result column="first_reply_date" jdbcType="TIMESTAMP"
                property="firstReplyDate"/>
        <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate"/>
        <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate"/>
    </resultMap>

    <resultMap id="DealAnalysisVo" type="com.pes.jd.model.VO.DealAnalysisVo">
        <id column="orderId" property="orderId" jdbcType="BIGINT"/>
        <result column="shopId" property="shopId" jdbcType="BIGINT"/>
        <result column="csNick" property="csNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="payTime" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="buyerNick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="isMrnFilter" property="isMrnFilter" jdbcType="BIT"/>
    </resultMap>

    <resultMap id="CsOrderIndexDTO" type="com.pes.jd.model.DTO.CsOrderIndexDTO">
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="is_assit_order_create" jdbcType="BIT"
                property="isAssitOrderCreate"/>
        <result column="is_assit_order_in_followup" jdbcType="BIT"
                property="isAssitOrderInFollowup"/>
        <result column="is_assit_order_pay" jdbcType="BIT" property="isAssitOrderPay"/>
        <result column="out_stock_time" jdbcType="DATE" property="outStockTime"/>
        <result column="silent_flag" jdbcType="INTEGER" property="slientFlag"/>


    </resultMap>
    <sql id="csOrder">
		shop_id,date,cs_nick,buyer_nick,order_id,order_pay_date,order_payment,is_assit_order_create,
		is_assit_order_in_followup,is_assit_order_pay
	</sql>
    <sql id="base_field">
		id, shop_id, date, cs_nick, buyer_nick, order_id, banner_flag,
		order_created, order_pay_date,
		order_payment, order_goods_num, order_post_fee, is_goods_filte, is_mrn_filter,
		bc_chat_num,
		bc_reply_num, bc_chat_round_num, bc_first_reply_date, bc_last_reply_date,
		bc_first_chat_date,
		bc_last_chat_date, bp_reply_num, bp_chat_round_num, bp_first_reply_date,
		bp_last_chat_date,
		bp_last_reply_date, bp_chat_num, ac_first_reply_date, ac_first_chat_date,
		ap_first_chat_date,
		ap_first_reply_date, last_reply_date, first_reply_date, first_chat_date, last_chat_date
	</sql>

    <sql id="deal_trade">
		order_id as orderId, shop_id as shopId, cs_nick as csNick, last_reply_date as
		date, order_payment as payment,
		order_goods_num as num, order_created as created, order_pay_date as payTime,
		buyer_nick as buyerNick, is_mrn_filter as isMrnFilter
	</sql>


    <select id="getCsOrderIndexById" resultMap="CsOrderIndexDO"
            parameterType="java.lang.Long">
        SELECT
        <include refid="base_field"/>
        FROM pes_cs_order_index
        WHERE
        id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getCsSaleTradeByDateAndBuyNickAndIsMrnFilterAndShopId"
            resultMap="DealAnalysisVo" parameterType="map">
        SELECT order_id as orderId, shop_id as shopId, cs_nick as csNick,
        last_reply_date as date, order_payment as payment, order_goods_num as
        num, order_created as created, order_pay_date as payTime, buyer_nick
        as buyerNick, is_mrn_filter as isMrnFilter
        FROM ${tableName}
        WHERE
        order_created
        BETWEEN #{startDate} AND #{endDate}
        AND is_mrn_filter =
        #{isMrnFilter}
        AND cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")"
                 separator=",">
            #{nick}
        </foreach>
        <if test="buyerNick != null and buyerNick!=''">
            AND buyer_nick = #{buyerNick}
        </if>
        order by order_pay_date desc
    </select>

    <delete id="deleteCsOrderIndexById" parameterType="java.lang.Long">
        DELETE FROM pes_cs_order_index
        WHERE
        id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertCsOrderIndex" parameterType="com.pes.jd.model.DO.CsOrderIndex">
        INSERT INTO pes_cs_order_index (id, shop_id, date,
        cs_nick, buyer_nick, order_id,
        banner_flag, order_created, earliest_reply_date,
        latest_reply_date, min_reply_num, is_goods_filte,
        chat_round_num, latest_chat_date)
        VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT},
        #{date,jdbcType=DATE},
        #{csNick,jdbcType=VARCHAR}, #{buyerNick,jdbcType=VARCHAR}, #{orderId,jdbcType=BIGINT},
        #{bannerFlag,jdbcType=BIT}, #{orderCreated,jdbcType=TIMESTAMP},
        #{earliestReplyDate,jdbcType=TIMESTAMP},
        #{latestReplyDate,jdbcType=TIMESTAMP},
        #{minReplyNum,jdbcType=INTEGER}, #{isGoodsFilte,jdbcType=BIT},
        #{chatRoundNum,jdbcType=INTEGER},
        #{latestChatDate,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateCsOrderIndexById" parameterType="com.pes.jd.model.DO.CsOrderIndex">
        UPDATE pes_cs_order_index
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="buyerNick != null">
                buyer_nick = #{buyerNick,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="bannerFlag != null">
                banner_flag = #{bannerFlag,jdbcType=BIT},
            </if>
            <if test="orderCreated != null">
                order_created = #{orderCreated,jdbcType=TIMESTAMP},
            </if>
            <if test="earliestReplyDate != null">
                earliest_reply_date = #{earliestReplyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="latestReplyDate != null">
                latest_reply_date = #{latestReplyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="minReplyNum != null">
                min_reply_num = #{minReplyNum,jdbcType=INTEGER},
            </if>
            <if test="isGoodsFilte != null">
                is_goods_filte = #{isGoodsFilte,jdbcType=BIT},
            </if>
            <if test="chatRoundNum != null">
                chat_round_num = #{chatRoundNum,jdbcType=INTEGER},
            </if>
            <if test="latestChatDate != null">
                latest_chat_date = #{latestChatDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询下单未付款的 -->
    <select id="selectByNonPay" resultMap="CsOrderIndexDO">
        SELECT
        <include refid="base_field"/>
        FROM
        ${tableName}
        WHERE order_pay_date = '0000-00-00 00:00:00' OR order_pay_date is null
        AND cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")"
                 separator=",">
            #{nick}
        </foreach>
        <if test="buyerNick != null and buyerNick != '' ">
            AND buyer_nick = #{buyerNick}
        </if>
        AND date BETWEEN #{startDate} AND #{endDate}

    </select>

    <!-- 查询协助服务分析数据总条数 -->
    <select id="selectCsOrderIndexByDateByCsNickByBuyerByOrderByTypeCount" resultType="Integer">
        select
        count(id)
        from ${tableName}
        <where>
            <if test="shopId!=null and shopId!=''">
                shop_id = #{shopId}
            </if>

            <if test="assistServiceQuery.orderId!=null and assistServiceQuery.orderId!=''">
                and order_id = #{assistServiceQuery.orderId}
            </if>

            <if test="csNickList!=null and csNickList.size>0">
                and cs_nick in
                <foreach collection="csNickList" item="nick" open="(" close=")"
                         separator=",">
                    #{nick}
                </foreach>
            </if>
            AND `date` BETWEEN #{startDate} AND #{endDate}
            <if test="assistServiceQuery.customerId!=null and assistServiceQuery.customerId!=''">
                and buyer_nick = #{assistServiceQuery.customerId}
            </if>

            <choose>
                <when test="assistServiceQuery.assistType==1"><!-- 协助下单 -->
                    AND is_assit_order_create = 1
                </when>
                <when test="assistServiceQuery.assistType==2"><!-- 协助付款 -->
                    AND is_assit_order_pay = 1
                </when>
                <when test="assistServiceQuery.assistType==3"><!-- 协助跟进 -->
                    AND is_assit_order_in_followup = 1
                </when>
                <otherwise>
                    AND (is_assit_order_create = 1 OR is_assit_order_pay = 1 OR is_assit_order_in_followup = 1)
                </otherwise>
            </choose>
        </where>
    </select>

    <!-- 协助服务分析查询 -->
    <select id="selectCsOrderIndexByDateByCsNickByBuyerByOrderByType" resultMap="CsOrderIndexDTO">
        <foreach collection="orderIndexTables" item="table" separator="UNION">
            SELECT shop_id,`date`,cs_nick,buyer_nick,order_id,order_pay_date,order_created,order_payment,
            is_assit_order_create,is_assit_order_pay,is_assit_order_in_followup
            FROM ${table.getTableName}
            WHERE shop_id = #{shopId}

            <if test="assistServiceQuery.orderId!=null and assistServiceQuery.orderId!=''">
                AND order_id = #{assistServiceQuery.orderId}
            </if>
            <if test="csNickList!=null and csNickList.size>0">
                and cs_nick in
                <foreach collection="csNickList" item="nick" open="(" close=")"
                         separator=",">
                    #{nick}
                </foreach>
            </if>
            AND `date` BETWEEN #{startDate} and #{endDate}

            <if test="assistServiceQuery.customerId!=null and assistServiceQuery.customerId!=''">
                and buyer_nick = #{assistServiceQuery.customerId}
            </if>

            <choose>
                <when test="assistServiceQuery.assistType==1"><!-- 协助下单 -->
                    AND is_assit_order_create = 1
                </when>
                <when test="assistServiceQuery.assistType==2"><!-- 协助付款 -->
                    AND is_assit_order_pay = 1
                </when>
                <when test="assistServiceQuery.assistType==3"><!-- 协助跟进 -->
                    AND is_assit_order_in_followup = 1
                </when>
                <otherwise>
                    AND (is_assit_order_create = 1 OR is_assit_order_pay = 1 OR is_assit_order_in_followup = 1)
                </otherwise>
            </choose>
        </foreach>
        <if test="sortPageQuery.sort">
            ORDER BY #{sortPageQuery.field,jdbcType=VARCHAR} #{sortPageQuery.sortDirection}
        </if>
        <if test="sortPageQuery.currentPage != null and sortPageQuery.size !=0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>

    <select id="searchDateShop" resultMap="CsPerformanceOrderIndexDTO">
        select cs_nick,date,
        <if test="type == 1">
            <!-- 协助下单 -->
            sum(order_payment) aidOrderAmount,SUM(1) aidOrderNum
        </if>
        <if test="type == 2">
            <!-- 协助跟进 -->
            sum(order_payment) aidPayAmount,SUM(1) aidPayNum
        </if>
        <if test="type == 3">
            <!-- 协助付款 -->
            sum(order_payment) aidFollowAmount,SUM(1) aidFollowNum
        </if>
        from pes_cs_order_index
        where cs_nick in
        <foreach collection="nicks" item="nick" close=")" open="("
                 separator=",">
            #{nick}
        </foreach>
        <if test="type == 1">
            <!-- 协助下单 -->
            and is_assit_order_create = 1
        </if>
        <if test="type == 2">
            <!-- 协助跟进 -->
            and is_assit_order_in_followup = 1
        </if>
        <if test="type == 3">
            <!-- 协助付款 -->
            and is_assit_order_pay = 1
        </if>
        GROUP BY
        <if test="isDate == 'date'">
            date,
        </if>
        cs_nick

    </select>

    <select id="selectOrderIndexByDateAndCsNickForCustReceiveAll" resultType="com.pes.jd.model.DTO.CsOrderIndexDTO">
        select
        ci.shop_id,
        ci.date,
        ci.cs_nick ,
        ci.buyer_nick ,
        ci.is_assit_order_create,
        ci.is_assit_order_in_followup,
        ci.is_assit_order_pay,
        ci.silent_flag,
        ci.is_presale,
        ci.is_balance_pay
        FROM
        (
        <foreach collection="ciTableNames" item="ci" separator="union all">
            select shop_id,
            date,
            cs_nick ,
            buyer_nick ,
            is_assit_order_create,
            is_assit_order_in_followup,
            is_assit_order_pay,
            silent_flag,
            is_presale,
            is_balance_pay
            from ${ci.tableName}
            where cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")"
                     separator=",">
                #{csNick}
            </foreach>
            <if test="buyerNick!=null and buyerNick!=''">
                AND buyer_nick =#{buyerNick}
            </if>
            AND shop_id=#{shopId}
            AND date between #{ci.beginDate} and #{ci.endDate}
        </foreach>
        )
        ci
    </select>

    <select id="selectFieldsByOrderIds" resultType="com.pes.jd.model.DO.CsOrderIndex">
        SELECT
        order_id,
        <if test="fields != null and fields.trim().length() > 0">
            ${fields}
        </if>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <choose>
            <when test="orderIds != null and orderIds.size() > 0">
                <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                (0)
            </otherwise>
        </choose>
    </select>


    <!--协助跟进-新加的sql**********************************************************************-->
    <select id="selectOrderIndexForCustReceiveAssist" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">

        SELECT
        distinct
        el.shop_id shopId,
        el.date date,
        el.cs_nick csNick,
        el.buyer_nick buyerNick
        FROM
        (
        <foreach collection="ciTableNames" item="ci" separator="UNION ALL">
            SELECT
            distinct
            shop_id,
            date,
            buyer_nick,
            cs_nick
            FROM ${ci.tableName}
            where
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.buyerNick!=null and param.buyerNick!=''">
                AND buyer_nick =#{param.buyerNick}
            </if>
            <if test="param.isAssitOrderInFollowup!=null">
                AND is_assit_order_in_followup=#{param.isAssitOrderInFollowup}
            </if>

            AND silent_flag!=1
            AND date between #{ci.beginDate} and #{ci.endDate}

            or (
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.buyerNick!=null and param.buyerNick!=''">
                AND buyer_nick =#{param.buyerNick}
            </if>
            AND is_presale=1 and is_balance_pay=1
            AND silent_flag!=1
            AND date between #{ci.beginDate} and #{ci.endDate}
            )
        </foreach>
        ) el
        <if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
        </if>
        <if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>


    <select id="selectOrderIndexCountForCustReceiveAssist" resultType="java.lang.Integer">

        SELECT
        count(ci.id)
        FROM
        (
        <foreach collection="ciTableNames" item="ci" separator="UNION ALL">
            SELECT
            distinct
            id
            FROM ${ci.tableName}
            where
            (
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.buyerNick!=null and param.buyerNick!=''">
                AND buyer_nick =#{param.buyerNick}
            </if>
            <if test="param.isAssitOrderInFollowup!=null">
                AND is_assit_order_in_followup=#{param.isAssitOrderInFollowup}
            </if>

            AND silent_flag!=1
            AND date between #{ci.beginDate} and #{ci.endDate}
            )

            OR (
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.buyerNick!=null and param.buyerNick!=''">
                AND buyer_nick =#{param.buyerNick}
            </if>
            AND is_presale=1 and is_balance_pay=1
            AND silent_flag!=1
            AND date between #{ci.beginDate} and #{ci.endDate}
            )
        </foreach>
        ) ci

    </select>


    <select id="selectOrderIndexForCustReceiveAssistOrderOrPay" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">

        SELECT
        distinct
        el.shop_id ,
        el.date ,
        el.cs_nick ,
        el.buyer_nick
        FROM
        (
        <foreach collection="ciTableNames" item="ci" separator="UNION ALL">
            SELECT
            distinct
            shop_id,
            date,
            buyer_nick,
            cs_nick
            FROM ${ci.tableName}
            where
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.buyerNick!=null and param.buyerNick!=''">
                AND buyer_nick =#{param.buyerNick}
            </if>
            <if test="param.isAssitOrderCreate!=null">
                AND is_assit_order_create=#{param.isAssitOrderCreate}
            </if>
            <if test="param.isAssitOrderPay!=null">
                AND is_assit_order_pay=#{param.isAssitOrderPay}
            </if>
            AND silent_flag!=1
            AND date between #{ci.beginDate} and #{ci.endDate}
        </foreach>
        ) el

        <if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
        </if>
        <if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>

    <select id="selectOrderIndexCountForCustReceiveAssistOrderOrPay" resultType="java.lang.Integer">
        SELECT
        count(el.id)
        FROM
        (
        <foreach collection="ciTableNames" item="ci" separator="UNION ALL">
            SELECT
            distinct
            id
            FROM ${ci.tableName}
            where
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.buyerNick!=null and param.buyerNick!=''">
                AND buyer_nick =#{param.buyerNick}
            </if>
            <if test="param.isAssitOrderCreate!=null">
                AND is_assit_order_create=#{param.isAssitOrderCreate}
            </if>
            <if test="param.isAssitOrderPay!=null">
                AND is_assit_order_pay=#{param.isAssitOrderPay}
            </if>
            AND silent_flag!=1
            AND date between #{ci.beginDate} and #{ci.endDate}
        </foreach>
        ) el
    </select>

    <!--协助跟进-新加的sql**********************************************************************-->


    <!--静默销售分析-查询静默订单插旗过滤count数-->
    <select id="selectOrderFilteCountForSilenceSaleAnalysis" resultType="int">
        SELECT count(DISTINCT order_id)
        FROM ${tableName}
        <where>
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="buyerNick != null and buyerNick != ''">
                AND buyer_nick = #{buyerNick}
            </if>
            AND (order_pay_date BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
            #{startDate} AND #{endDate}))
            AND order_flag != 0
        </where>
    </select>
    <!--静默销售分析-分页查询静默订单插旗过滤-->
    <select id="selectOrderFilteForSilenceSaleAnalysis" resultType="long">
        SELECT DISTINCT cob.order_id FROM (
        <foreach collection="tableNames" item="name" separator="UNION">
            SELECT order_id, order_created
            FROM ${name.tableName}
            <where>
                <if test="orderIds != null and orderIds.size() > 0">
                    AND order_id IN
                    <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                        #{id,jdbcType=BIGINT}
                    </foreach>
                </if>
                <if test="shopId != null">
                    AND shop_id = #{shopId,jdbcType=BIGINT}
                </if>
                <if test="buyerNick != null and buyerNick != ''">
                    AND buyer_nick = #{buyerNick,jdbcType=VARCHAR}
                </if>
                AND (order_pay_date BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
                #{startDate} AND #{endDate}))
                AND order_flag != 0
            </where>
        </foreach>
        ) cob ORDER BY order_created ASC
        <if test="start != null and length != null and length > 0">
            LIMIT #{start}, #{length}
        </if>
    </select>


</mapper>