<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.ShopOverviewMapper">


<!-- 	<select id="selectShopByShopId" parameterType="java.lang.Long" resultMap="ShopDO">
		SELECT
		<include refid="base_field" />
		FROM pes_shop
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
	</select> -->

</mapper>