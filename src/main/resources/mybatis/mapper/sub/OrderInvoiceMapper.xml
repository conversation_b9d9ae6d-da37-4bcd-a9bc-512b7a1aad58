<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.OrderInvoiceMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderInvoiceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content_id" jdbcType="VARCHAR" property="contentId" />
    <result column="consignee_email" jdbcType="VARCHAR" property="consigneeEmail" />
    <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="vat_no" jdbcType="VARCHAR" property="vatNo" />
    <result column="vat_address_registered" jdbcType="VARCHAR" property="vatAddressRegistered" />
    <result column="vat_phone_registered" jdbcType="VARCHAR" property="vatPhoneRegistered" />
    <result column="vat_deposit_bank" jdbcType="VARCHAR" property="vatDepositBank" />
    <result column="vat_bank_account" jdbcType="VARCHAR" property="vatBankAccount" />
    <result column="vat_user_address" jdbcType="VARCHAR" property="vatUserAddress" />
    <result column="vat_user_name" jdbcType="VARCHAR" property="vatUserName" />
    <result column="vat_user_phone" jdbcType="VARCHAR" property="vatUserPhone" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
    <result column="invoice_info" jdbcType="VARCHAR" property="invoiceInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, invoice_type, title, content_id, consignee_email, consignee_phone, 
    invoice_code, vat_no, vat_address_registered, vat_phone_registered, vat_deposit_bank, 
    vat_bank_account, vat_user_address, vat_user_name, vat_user_phone, created_time, 
    modified_time,invoice_info
  </sql>
	
	<insert id="insertOrderInvoice"  parameterType="map">
    insert into ${tableName}
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderInvoiceDTO.orderId != null">
        order_id,
      </if>
      <if test="orderInvoiceDTO.invoiceType != null">
        invoice_type,
      </if>
      <if test="orderInvoiceDTO.title != null">
        title,
      </if>
      <if test="orderInvoiceDTO.contentId != null">
        content_id,
      </if>
      <if test="orderInvoiceDTO.consigneeEmail != null">
        consignee_email,
      </if>
      <if test="orderInvoiceDTO.consigneePhone != null">
        consignee_phone,
      </if>
      <if test="orderInvoiceDTO.invoiceCode != null">
        invoice_code,
      </if>
      <if test="orderInvoiceDTO.vatNo != null">
        vat_no,
      </if>
      <if test="orderInvoiceDTO.vatAddressRegistered != null">
        vat_address_registered,
      </if>
      <if test="orderInvoiceDTO.vatPhoneRegistered != null">
        vat_phone_registered,
      </if>
      <if test="orderInvoiceDTO.vatDepositBank != null">
        vat_deposit_bank,
      </if>
      <if test="orderInvoiceDTO.vatBankAccount != null">
        vat_bank_account,
      </if>
      <if test="orderInvoiceDTO.vatUserAddress != null">
        vat_user_address,
      </if>
      <if test="orderInvoiceDTO.vatUserName != null">
        vat_user_name,
      </if>
      <if test="orderInvoiceDTO.vatUserPhone != null">
        vat_user_phone,
      </if>
      <if test="orderInvoiceDTO.createdTime != null">
        created_time,
      </if>
      <if test="orderInvoiceDTO.modifiedTime != null">
        modified_time,
      </if>
       <if test="orderInvoiceDTO.invoiceInfo != null">
        invoice_info,
      </if>
      
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
    	   <if test="orderInvoiceDTO.orderId != null">
        #{orderInvoiceDTO.orderId,jdbcType=BIGINT},
      </if>
      <if test="orderInvoiceDTO.invoiceType != null">
        #{orderInvoiceDTO.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="orderInvoiceDTO.title != null">
        #{orderInvoiceDTO.title,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.contentId != null">
        #{orderInvoiceDTO.contentId,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.consigneeEmail != null">
        #{orderInvoiceDTO.consigneeEmail,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.consigneePhone != null">
        #{orderInvoiceDTO.consigneePhone,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.invoiceCode != null">
        #{orderInvoiceDTO.invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatNo != null">
        #{orderInvoiceDTO.vatNo,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatAddressRegistered != null">
        #{orderInvoiceDTO.vatAddressRegistered,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatPhoneRegistered != null">
        #{orderInvoiceDTO.vatPhoneRegistered,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatDepositBank != null">
        #{orderInvoiceDTO.vatDepositBank,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatBankAccount != null">
        #{vatBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatUserAddress != null">
        #{orderInvoiceDTO.vatUserAddress,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatUserName != null">
        #{orderInvoiceDTO.vatUserName,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.vatUserPhone != null">
        #{orderInvoiceDTO.vatUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="orderInvoiceDTO.createdTime != null">
        #{orderInvoiceDTO.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderInvoiceDTO.modifiedTime != null">
        #{orderInvoiceDTO.modifiedTime,jdbcType=TIMESTAMP},
      </if>
        <if test="orderInvoiceDTO.invoiceInfo != null">
        #{orderInvoiceDTO.invoiceInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  	<select id="selectOrderInvoice" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${tableName}
    where order_id = #{oid,jdbcType=BIGINT}
  </select>
  	
  	
   <delete id="deleteOrderByOrderId" parameterType="map">
        DELETE FROM ${tableName}
        where
		order_id = #{oid,jdbcType=BIGINT}
    </delete>
  
</mapper>