<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsOrderBindMapper">

    <resultMap id="CsOrderBindDTO" type="com.pes.jd.model.DTO.CsOrderBindDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_created" jdbcType="TIMESTAMP" property="orderCreated"/>
        <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate"/>
        <result column="order_payment" jdbcType="DOUBLE" property="orderPayment"/>
        <result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum"/>
        <result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="is_pes_order" jdbcType="BIT" property="isPesOrder"/>
        <result column="silent_flag" jdbcType="TINYINT" property="silentFlag"/>
        <result column="order_filte_flag" jdbcType="TINYINT" property="orderFilteFlag"/>
        <result column="is_presale" jdbcType="BIT" property="isPresale"/>
        <result column="to_tail_paid_cs" jdbcType="VARCHAR" property="toTailPaidCs"/>
        <result column="pay_type" jdbcType="TINYINT" property="payType"/>
        <result column="is_balance_pay" jdbcType="VARCHAR" property="isBalancePay"/>
        <result column="order_valid_pay_time" jdbcType="TIMESTAMP" property="orderValidPayTime"/>
        <result column="order_valid_payment" jdbcType="DOUBLE" property="orderValidPayment"/>
    </resultMap>

    <resultMap id="CsSaleAnalysisVO" type="com.pes.jd.model.VO.CsSaleAnalysisVO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="order_created" property="orderCreated" jdbcType="TIMESTAMP"/>
        <result column="order_pay_date" property="orderPayDate" jdbcType="TIMESTAMP"/>
        <result column="order_payment" property="orderPayment" jdbcType="DOUBLE"/>
        <result column="order_goods_num" property="orderGoodsNum" jdbcType="INTEGER"/>
        <result column="order_post_fee" property="orderPostFee" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="base_field">
    id, shop_id, date, cs_nick, buyer_nick, order_id, order_created, order_pay_date, 
    order_payment, order_goods_num, order_post_fee, type, is_pes_order, silent_flag
  </sql>

    <insert id="insertCsOrderBind" parameterType="com.pes.jd.model.DO.CsOrderBind">
    INSERT INTO pes_cs_order_bind (shop_id, date, trade_id, cs_nick, buyer_nick, type)
    VALUES 
    (	 
          #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
	      #{tradeId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR},
	      #{buyerNick,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}
	 )
  </insert>

    <delete id="deleteCsOrderBindById" parameterType="java.lang.Long">
    DELETE FROM pes_cs_order_bind
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="updateCsOrderBindBySelective" parameterType="com.pes.jd.model.DO.CsOrderBind">
        UPDATE pes_cs_order_bind
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="tradeId != null">
                trade_id = #{tradeId,jdbcType=BIGINT},
            </if>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="buyerNick != null">
                buyer_nick = #{buyerNick,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getCsOrderBindById" resultMap="CsOrderBindDTO" parameterType="java.lang.Long">
        SELECT
        <include refid="base_field"/>
        FROM pes_cs_order_bind
        WHERE
        id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectDayOrder" resultType="java.util.Map" parameterType="map">
    SELECT
    order_id orderId,buyer_nick buyerNick,order_created orderCreated,order_pay_date orderPayDate,order_payment orderPayment
    FROM ${tableName}
    WHERE
    order_pay_date BETWEEN #{begin} AND date_add(#{end}, interval 1 day)
    AND cs_nick = #{nick}
    AND shop_id = #{shopId}
    AND is_pes_order = 1
  </select>

    <select id="selectFieldsByOrderIds" resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        SELECT
        order_id,
        <if test="fields != null and fields.trim().length() > 0">
            ${fields}
        </if>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <choose>
            <when test="orderIds != null and orderIds.size() > 0">
                <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                (0)
            </otherwise>
        </choose>
    </select>

    <select id="selectCsSaleTradeByDateAndBuyNickAndShopId" resultMap="CsSaleAnalysisVO" parameterType="map">
        SELECT
        order_id as orderId, shop_id as shopId, cs_nick as csNick, date as date, order_payment as payment,
        order_goods_num as num, order_created as created, order_pay_date as payTime, buyer_nick as buyerNick
        FROM ${tableName}
        WHERE
        order_pay_date BETWEEN #{startDate} AND #{endDate}
        AND is_pes_order = 1
        AND
        cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")"
                 separator=",">
            #{nick}
        </foreach>
        AND shop_id = #{shopId}
        <if test="buyerNick != null and buyerNick!=''">
            AND buyer_nick = #{buyerNick}
        </if>
        ORDER BY
        order_pay_date DESC
        <if test="start!=-1">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <select id="getCsSaleTradeByDateAndBuyNickAndShopIdAcount" resultType="java.lang.Integer" parameterType="map">
        SELECT
        count(order_id)
        FROM ${tableName}
        WHERE
        order_pay_date BETWEEN #{startDate} AND #{endDate}
        AND is_pes_order = 1
        AND
        cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")"
                 separator=",">
            #{nick}
        </foreach>
        AND shop_id = #{shopId}
        <if test="buyerNick != null and buyerNick!=''">
            AND buyer_nick = #{buyerNick}
        </if>
    </select>

    <select id="selectCountByShopIdAndDateForShopSaleAnalysis" resultType="int">
        SELECT COUNT(1) FROM (
        <foreach collection="tableNames" item="name" separator="UNION">
            SELECT
            order_id,
            GROUP_CONCAT(IF(is_pes_order = 1 , cs_nick, '') separator '') AS 'ascriptNick',
            GROUP_CONCAT(IF(type = 1 , cs_nick, NULL) separator '') AS 'orderCreatedNick',
            GROUP_CONCAT(IF(type = 2 , cs_nick, NULL) separator '') AS 'orderPayNick',
            GROUP_CONCAT(IF(type = 3 , cs_nick, NULL) separator '') AS 'totalSilenceNick'
            FROM ${name.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                AND shop_id = #{shopId}
                <if test="buyerNick != null and buyerNick != ''">
                    AND buyer_nick = #{buyerNick}
                </if>
                AND order_created BETWEEN #{startDate} AND #{endDate}
                AND is_presale = #{orderType}
                <if test="parentOrderId !=null and parentOrderId.size()>0 ">
                    and order_id not in
                    <foreach collection="parentOrderId" item="pid" index="index"
                             open="(" close=")" separator=",">
                        #{pid}
                    </foreach>
                </if>
            </where>
            GROUP BY order_id
        </foreach>
        ) cob
        <where>
            <if test="ascriptNick != null and ascriptNick != ''">
                AND cob.ascriptNick = #{ascriptNick}
            </if>
            <if test="orderCreatedNick != null and orderCreatedNick != ''">
                AND cob.orderCreatedNick = #{orderCreatedNick}
            </if>
            <if test="orderPayNick != null and orderPayNick != ''">
                AND cob.orderPayNick = #{orderPayNick}
            </if>
            <if test="totalSilenceNick != null and totalSilenceNick != ''">
                AND cob.totalSilenceNick = #{totalSilenceNick}
            </if>
        </where>
    </select>

    <select id="selectOrderIdForShopSaleAnalysis" resultType="Long">
        SELECT cob.order_id FROM (
        <foreach collection="tableNames" item="name" separator="UNION">
            SELECT
            order_id,
            GROUP_CONCAT(IF(is_pes_order = 1 , cs_nick, '') separator '') AS 'ascriptNick',
            GROUP_CONCAT(IF(type = 1 , cs_nick, NULL) separator '') AS 'orderCreatedNick',
            GROUP_CONCAT(IF(type = 2 , cs_nick, NULL) separator '') AS 'orderPayNick',
            GROUP_CONCAT(IF(type = 3 , cs_nick, NULL) separator '') AS 'totalSilenceNick',
            order_created
            FROM ${name.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                AND shop_id = #{shopId}
                <if test="buyerNick != null and buyerNick != ''">
                    AND buyer_nick = #{buyerNick}
                </if>
                AND order_created BETWEEN #{startDate} AND #{endDate}
                AND is_presale = #{orderType}
                <if test="parentOrderId !=null and parentOrderId.size()>0 ">
                    and order_id not in
                    <foreach collection="parentOrderId" item="pid" index="index"
                             open="(" close=")" separator=",">
                        #{pid}
                    </foreach>
                </if>
            </where>
            GROUP BY order_id
        </foreach>
        ) cob
        <where>
            <if test="ascriptNick != null and ascriptNick != ''">
                AND cob.ascriptNick = #{ascriptNick}
            </if>
            <if test="orderCreatedNick != null and orderCreatedNick != ''">
                AND cob.orderCreatedNick = #{orderCreatedNick}
            </if>
            <if test="orderPayNick != null and orderPayNick != ''">
                AND cob.orderPayNick = #{orderPayNick}
            </if>
            <if test="totalSilenceNick != null and totalSilenceNick != ''">
                AND cob.totalSilenceNick = #{totalSilenceNick}
            </if>
        </where>
        ORDER BY cob.order_created DESC
        <if test="start != null and length != null and length > 0 ">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <select id="selectCsNickAndTypeAndIsPesOrderByOrderIds" resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        <foreach collection="tableNames" item="name" separator="UNION">
            select order_id, cs_nick, `type`, is_pes_order,pay_type
            FROM ${name.tableName}
            WHERE shop_id = #{shopId} AND order_id IN
            <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </foreach>
    </select>


    <select id="selectCsOrderBindByOrderIds" resultMap="CsOrderBindDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_created BETWEEN #{beginDate} AND #{endDate}
        <if test="orderIds != null and orderIds.size() > 0">
            AND order_id IN
            <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectOrderFilteCountForSilenceSaleAnalysis" resultType="int">
        SELECT count(*)
        FROM ${tableName}
        <where>
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="buyerNick != null and buyerNick != ''">
                AND buyer_nick = #{buyerNick}
            </if>
            AND (order_valid_pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
            #{startDate} AND #{endDate}))
            AND is_pes_order = 0
            AND order_filte_flag = 1
            <if test="orderIds!=null and orderIds.size()>0">
                AND order_id NOT IN
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectOrderFilteForSilenceSaleAnalysis" resultType="com.pes.jd.model.VO.SilenceSaleAnalysisVO">
        SELECT cob.order_id, cob.buyer_nick, cob.order_created, cob.order_pay_date AS orderPayTime,
        cob.order_payment AS payMoney, orderType FROM (
        <foreach collection="tableNames" item="name" separator="UNION">
            SELECT order_id, buyer_nick, order_created, order_pay_date, order_payment,
            "指定订单不算客服业绩" AS orderType
            FROM ${name.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                <if test="shopId != null">
                    AND shop_id = #{shopId}
                </if>
                <if test="buyerNick != null and buyerNick != ''">
                    AND buyer_nick = #{buyerNick}
                </if>
                AND (order_valid_pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
                #{startDate} AND #{endDate}))
                AND is_pes_order = 0
                AND order_filte_flag = 1
                <if test="orderIds!=null and orderIds.size()>0">
                    AND order_id NOT IN
                    <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                        #{orderId}
                    </foreach>
                </if>
            </where>
        </foreach>
        ) cob group by order_id ORDER BY order_created ASC
        <if test="start != null and length != null and length > 0">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <select id="selectCountForCsSaleAnalysis" resultType="int">
        SELECT count(1)
        FROM ${tableName}
        <where>
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="buyerNick != null and buyerNick != ''">
                AND buyer_nick = #{buyerNick}
            </if>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="csNickLst != null and csNickLst.size() > 0">
                AND cs_nick IN
                <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                    #{nick}
                </foreach>
            </if>
            AND is_pes_order = 1
            <if test="parentOrderId !=null and parentOrderId.size()>0 ">
                and order_id not in
                <foreach collection="parentOrderId" item="pid" index="index"
                         open="(" close=")" separator=",">
                    #{pid}
                </foreach>
            </if>
            AND (
            (order_valid_pay_time BETWEEN #{startDate} AND #{endDate} AND pay_type != 1)
            OR (order_created BETWEEN #{startDate} AND #{endDate} AND pay_type = 1 )
            )
        </where>

    </select>

    <select id="selectForCsSaleAnalysis" resultType="com.pes.jd.model.VO.CsSaleAnalysisVO">
        <foreach collection="tableNames" item="name" separator="UNION ALL">
            SELECT order_id, buyer_nick, order_created, order_valid_pay_time orderPayTime, order_valid_payment payMoney,
            cs_nick
            FROM ${name.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                <if test="buyerNick != null and buyerNick != ''">
                    AND buyer_nick = #{buyerNick}
                </if>
                <if test="shopId != null">
                    AND shop_id = #{shopId}
                </if>
                <if test="csNickLst != null and csNickLst.size() > 0">
                    AND cs_nick IN
                    <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                        #{nick}
                    </foreach>
                </if>
                AND is_pes_order = 1
                <if test="parentOrderId !=null and parentOrderId.size()>0 ">
                    and order_id not in
                    <foreach collection="parentOrderId" item="pid" index="index"
                             open="(" close=")" separator=",">
                        #{pid}
                    </foreach>
                </if>
                AND order_valid_pay_time BETWEEN #{startDate} AND #{endDate} AND pay_type != 1
            </where>
            UNION ALL
            SELECT order_id, buyer_nick, order_created, order_valid_pay_time orderPayTime, order_valid_payment payMoney,
            cs_nick
            FROM ${name.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                <if test="buyerNick != null and buyerNick != ''">
                    AND buyer_nick = #{buyerNick}
                </if>
                <if test="shopId != null">
                    AND shop_id = #{shopId}
                </if>
                <if test="csNickLst != null and csNickLst.size() > 0">
                    AND cs_nick IN
                    <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                        #{nick}
                    </foreach>
                </if>
                AND is_pes_order = 1
                <if test="parentOrderId !=null and parentOrderId.size()>0 ">
                    and order_id not in
                    <foreach collection="parentOrderId" item="pid" index="index"
                             open="(" close=")" separator=",">
                        #{pid}
                    </foreach>
                </if>
                AND order_created BETWEEN #{startDate} AND #{endDate} AND pay_type = 1
            </where>

        </foreach>
        ORDER BY order_created ASC
        <if test="start != null and length != null and length > 0">
            LIMIT #{start}, #{length}
        </if>
    </select>

    <select id="searchShopDate" resultMap="CsOrderBindDTO">
        select
        <include refid="base_field"/>
        from ${tableName}
        where shop_id = #{shopId}
        and date between #{startDate} and #{endDate}
        and cs_nick in
        <foreach collection="nicks" item="nick" separator="," open="(" close=")">
            #{nick}
        </foreach>
    </select>

    <select id="selectOrderBindOfBuyerNickForCustReceiveAll" resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        SELECT
        ob.shop_id,
        ob.date,
        ob.cs_nick ,
        ob.buyer_nick,
        ob.type
        FROM
        (
        <foreach collection="obTableNames" item="ob" separator="UNION ALL">
            SELECT
            shop_id,
            date,
            cs_nick ,
            buyer_nick,
            type
            FROM ${ob.tableName}
            <where>
                shop_id=#{shopId}
                AND cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
                <if test="buyerNick!=null and buyerNick!=''">
                    AND buyer_nick =#{buyerNick}
                </if>
                AND date between #{ob.beginDate} and #{ob.endDate}
            </where>
        </foreach>
        )ob
    </select>

    <select id="selectOrderBindOfBuyerNickForCustReceiveLuoshi" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">
        select DISTINCT
        cp.buyer_nick buyerNick,
        cp.cs_nick csNick ,
        cp.first_chat_date firstChatDate,
        cp.last_chat_date lastChatDate,
        cp.shop_id shopId,
        cp.date date,
        cp.cross_chat_fail crossChatFail,
        cp.is_after_sale isAfterSale,
        cp.cs_active_chat_fail csActiveChatFail,
        cp.cs_active_urgepay_fail csActiveUrgepayFail,
        cp.is_order_created isOrderCreated

        FROM
        (
        <foreach collection="obTableNames" item="ob" separator="UNION ALL">
            SELECT
            shop_id,
            date,
            cs_nick ,
            buyer_nick
            FROM ${ob.tableName}
            <where>
                shop_id=#{shopId}
                AND cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
                <if test="buyerNick!=null and buyerNick!=''">
                    AND buyer_nick =#{buyerNick}
                </if>
                AND date between #{ob.beginDate} and #{ob.endDate}
                AND type =#{type}
            </where>
        </foreach>
        )
        ob
        INNER JOIN
        (
        <foreach collection="cpTableNames" item="cp" separator="UNION ALL">
            SELECT
            shop_id,
            date,
            cs_nick ,
            buyer_nick,
            first_chat_date,
            last_chat_date,
            cross_chat_fail,
            is_after_sale ,
            cs_active_chat_fail,
            cs_active_urgepay_fail,
            is_order_created
            FROM ${cp.tableName}
            <where>
                shop_id=#{shopId}
                AND cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
                <if test="buyerNick!=null and buyerNick!=''">
                    AND buyer_nick =#{buyerNick}
                </if>
                AND is_receive=1
                AND date between #{cp.beginDate} and #{cp.endDate}
            </where>
        </foreach>
        ) cp
        ON ob.buyer_nick=cp.buyer_nick
        WHERE
        cp.date=ob.date
        AND cp.cs_nick=ob.cs_nick
        <if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
        </if>
        <if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>

    <select id="selectCountForCustReceiveLuoshi" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT cp.id)
        from
        (
        <foreach collection="obTableNames" item="ob" separator="UNION ALL">
            SELECT
            shop_id,
            date,
            cs_nick ,
            buyer_nick,
            type
            FROM ${ob.tableName}
            <where>
                shop_id=#{shopId}
                AND cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
                <if test="buyerNick!=null and buyerNick!=''">
                    AND buyer_nick =#{buyerNick}
                </if>
                AND date between #{ob.beginDate} and #{ob.endDate}
                AND type =#{type}
            </where>
        </foreach>
        )
        ob
        INNER JOIN
        (
        <foreach collection="cpTableNames" item="cp" separator="UNION ALL">
            SELECT id,
            shop_id,
            date,
            cs_nick ,
            buyer_nick,
            first_chat_date,
            last_chat_date,
            is_order_created
            FROM ${cp.tableName}
            <where>
                shop_id=#{shopId}
                AND cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
                <if test="buyerNick!=null and buyerNick!=''">
                    AND buyer_nick =#{buyerNick}
                </if>
                AND is_receive=1
                AND date between #{cp.beginDate} and #{cp.endDate}
            </where>
        </foreach>
        ) cp
        ON ob.buyer_nick=cp.buyer_nick
        WHERE
        cp.date=ob.date
        AND cp.cs_nick=ob.cs_nick
    </select>
    <select id="selectByOrderIdAndCsNickAndSilentFlagForRefundDataAnalysis"
            resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        SELECT
        order_id,
        cs_nick,
        order_pay_date,
        order_payment
        FROM
        ${tableName}
        <!--订单编号-->
        WHERE shop_id = #{shopId} AND order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        <!--客服昵称-->
        <if test="csNickList != null and csNickList.size() > 0">
            AND cs_nick IN
            <foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
                #{nick}
            </foreach>
        </if>
        <!--绩效订单-->
        AND is_pes_order = 1
        <!--日期-->
        <choose>
<!--            <when test="dateType == 1">-->
<!--                AND `date` BETWEEN #{startDate} AND #{endDate}-->
<!--            </when>-->
            <!--<when test="dateType == 2">
                AND `date` BETWEEN #{startDate} AND #{endDate}
            </when>-->
            <when test="dateType == 3">
                AND order_created BETWEEN #{startDate} AND #{endDate}
            </when>
            <when test="dateType == 4">
                AND order_valid_pay_time BETWEEN #{startDate} AND #{endDate}
            </when>
        </choose>
    </select>
    <select id="selectCsOrderBindForOrderPresaleAnalysis" resultType="com.pes.jd.model.DTO.CsOrderBindDTO">
        <foreach collection="csOrderBindTables" item="table" separator="UNION">
        SELECT order_id, type, cs_nick, is_pes_order FROM ${table.tableName}
        <where>
         shop_id = #{shopId}
         <!--订单编号-->
         AND order_id IN
         <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
             #{orderId}
         </foreach>
        <!--顾客ID-->
        <if test="buyerNick != null and buyerNick.trim().length() > 0">
            AND buyer_nick = #{buyerNick}
        </if>
        <if test="csNickLst != null and csNickLst.size() > 0">
            AND cs_nick IN
            <foreach collection="csNickLst" item="nick" open="(" close=")" separator=",">
                #{nick}
            </foreach>
        </if>
        AND `date` BETWEEN #{table.beginDate} AND #{table.endDate}
        </where>
        </foreach>
    </select>

</mapper>