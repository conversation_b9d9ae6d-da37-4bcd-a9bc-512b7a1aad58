<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopOvDayMapper">

    <resultMap id="ShopDayOverviewDTO" type="com.pes.jd.model.DTO.ShopDayOverviewDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount"/>
        <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum"/>
        <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum"/>
        <result column="consign_num" jdbcType="INTEGER" property="consignNum"/>
        <result column="ordered_num" jdbcType="INTEGER" property="orderedNum"/>
        <result column="ordered_amount" jdbcType="DOUBLE" property="orderedAmount"/>
        <result column="cfm_goods_o_amount" jdbcType="DOUBLE" property="cfmGoodsOAmount"/>
        <result column="cfm_goodst_o_num" jdbcType="INTEGER" property="cfmGoodstONum"/>
        <result column="team_receive_num" jdbcType="INTEGER" property="teamReceiveNum"/>
        <result column="team_enquiry_num" jdbcType="INTEGER" property="teamEnquiryNum"/>
    </resultMap>

    <sql id="fields">
    id, shop_id, date, sale_amount, sale_order_num, sale_buyer_num, consign_num, ordered_num, 
    ordered_amount, cfm_goods_o_amount, cfm_goodst_o_num, team_receive_num, team_enquiry_num
  </sql>

    <sql id="fields1">
    id, shop_id, date, sale_amount shopSaleAmount, sale_order_num shopPaidTradeNum, sale_buyer_num shopSaleBuyerNum, consign_num shopConsignNum, ordered_num shopOrderedNum,
    ordered_amount shopCreatedTradeAmount, cfm_goods_o_amount shopConfirmGoodsAmount, cfm_goodst_o_num, team_receive_num, team_enquiry_num
  </sql>

    <insert id="batchInsertShopDayOverview" parameterType="com.pes.jd.model.DO.ShopDayOverviewDO">
        INSERT INTO ${tableName} (shop_id, date,
        sale_amount, sale_order_num, sale_buyer_num, consign_num,
        ordered_num,ordered_amount,cfm_goodst_o_num,cfm_goods_o_amount,
        team_receive_num,team_enquiry_num)
        VALUES
        <foreach collection="dayOverviewLst" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.saleAmount,jdbcType=DOUBLE},
            #{itm.saleOrderNum,jdbcType=INTEGER},
            #{itm.saleBuyerNum,jdbcType=INTEGER},
            #{itm.consignNum,jdbcType=INTEGER},
            #{itm.orderedNum,jdbcType=INTEGER},
            #{itm.orderedAmount,jdbcType=DOUBLE},
            #{itm.cfmGoodstONum,jdbcType=DOUBLE},
            #{itm.cfmGoodsOAmount,jdbcType=INTEGER},
            #{itm.teamReceiveNum,jdbcType=INTEGER},
            #{itm.teamEnquiryNum,jdbcType=INTEGER}
            )
        </foreach>

    </insert>

    <delete id="deleteShopDayOverviewByDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>


    <select id="selectByShopId" resultMap="ShopDayOverviewDTO" parameterType="map">
        select
        <include refid="fields"/>
        from #{tableName}
        where shop_id = #{shopId}
        AND date BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectCsPerformanceData" resultType="com.pes.jd.model.DTO.ShopPerformanceDTO" parameterType="map">
        select
        <include refid="fields1"/>
        from ${tableName}
        where shop_id = #{shopId}
        AND date BETWEEN #{startDate} AND #{endDate}
    </select>


    <!--   <select id="getShopDayOverviewById" parameterType="java.lang.Long" resultMap="ShopDayOverviewDTO" >
        SELECT
            <include refid="fields" />
        FROM ${tableName}
        WHERE
            id = #{id,jdbcType=BIGINT}
      </select> -->
    <select id="selectByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopOvDayDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                `shop_id`, `date`, `sale_amount`, `sale_order_num`, `sale_goods_num`, `sale_buyer_num`,
                `order_post_fee`,
                `ordered_num`, `ordered_amount`, `consign_num`, `out_stock_order_num`, `out_stock_goods_num`,
                `out_stock_num`,
                `out_stock_amount`, `cfm_goods_o_amount`, `cfm_goodst_o_num`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                `shop_id`,
                DATE_FORMAT( `date`, '%Y-%m' ) `date`,
                SUM( `sale_amount` ) `sale_amount`,
                SUM( `sale_order_num` ) sale_order_num,
                SUM( `sale_goods_num` ) sale_goods_num,
                SUM( `sale_buyer_num` ) sale_buyer_num,
                SUM( `order_post_fee` ) order_post_fee,
                SUM( `ordered_num` ) ordered_num,
                SUM( `ordered_amount` ) ordered_amount,
                SUM( `consign_num` ) consign_num,
                SUM( `out_stock_order_num` ) out_stock_order_num,
                SUM( `out_stock_goods_num` ) out_stock_goods_num,
                SUM( `out_stock_num` ) out_stock_num,
                SUM( `out_stock_amount` ) out_stock_amount,
                SUM( `cfm_goods_o_amount` ) cfm_goods_o_amount,
                SUM( `cfm_goodst_o_num` ) cfm_goodst_o_num
            </when>
            <otherwise>
                `shop_id`,
                SUM( `sale_amount` ) `sale_amount`,
                SUM( `sale_order_num` ) sale_order_num,
                SUM( `sale_goods_num` ) sale_goods_num,
                SUM( `sale_buyer_num` ) sale_buyer_num,
                SUM( `order_post_fee` ) order_post_fee,
                SUM( `ordered_num` ) ordered_num,
                SUM( `ordered_amount` ) ordered_amount,
                SUM( `consign_num` ) consign_num,
                SUM( `out_stock_order_num` ) out_stock_order_num,
                SUM( `out_stock_goods_num` ) out_stock_goods_num,
                SUM( `out_stock_num` ) out_stock_num,
                SUM( `out_stock_amount` ) out_stock_amount,
                SUM( `cfm_goods_o_amount` ) cfm_goods_o_amount,
                SUM( `cfm_goodst_o_num` ) cfm_goodst_o_num
            </otherwise>
        </choose>

        FROM ${shopOvDayTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>

    <select id="selectShopSaleAmountByShopIdByDate" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopOvDay" >
        SELECT
        shop_id shopId,
        sum(sale_amount)  saleAmount
        FROM ${tableName}
        WHERE
        shop_id in
        <foreach collection="shopIdLst" item="shopId" open="(" close=")" separator=",">
            #{shopId}
        </foreach>
        AND date BETWEEN #{startDate}	AND #{endDate}
        group by shop_id

    </select>

    <select id="selectShopOrderSaleAmountByShopIdByDate" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopOvDay" >
        SELECT
        shop_id  as shopId,
        sale_amount  as saleAmount,
        sale_amount_presale as saleAmountPresale,
        sale_amount_preordain as saleAmountPreordain
        FROM ${tableName}
        WHERE
        shop_id in
        <foreach collection="shopIdSet" item="shopId" open="(" close=")" separator=",">
            #{shopId}
        </foreach>
        AND date =#{date}
    </select>

    <select id="selectShopSaleAmountByShopIdByDateNew" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopOvDay">
        SELECT
        sale_amount
        FROM
        ${tableName}
        where
        shop_id = #{shopId}
        AND date = #{date}
    </select>
    <select id="selectSaleAmountAndBuyerNumByShopId" resultType="com.pes.jd.model.DTO.ShopOvDayDTO">
        select
        sale_amount, sale_buyer_num
        from ${tableName}
        where
        shop_id = #{shopId}
        and date = #{date}
    </select>
</mapper>