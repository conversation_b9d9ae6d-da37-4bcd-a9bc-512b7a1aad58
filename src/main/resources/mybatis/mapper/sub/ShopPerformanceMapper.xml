<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopPerformanceMapper">


    <select id="selectShopRefundByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopRefundDayDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr,
                `completed_refund_num`, `completed_refund_goods_num`,
                `completed_refund_buyer_num`, `completed_refund_amount`, `completed_refund_sku_num`,
                `total_refund_duration`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `completed_refund_num` ) `completed_refund_num`,
                SUM( `completed_refund_goods_num` ) `completed_refund_goods_num`,
                SUM( `completed_refund_buyer_num` ) `completed_refund_buyer_num`,
                SUM( `completed_refund_amount` ) `completed_refund_amount`,
                SUM( `completed_refund_sku_num` ) `completed_refund_sku_num`,
                SUM( `total_refund_duration` ) `total_refund_duration`
            </when>
            <otherwise>
                `shop_id`,
                SUM( `completed_refund_num` ) `completed_refund_num`,
                SUM( `completed_refund_goods_num` ) `completed_refund_goods_num`,
                SUM( `completed_refund_buyer_num` ) `completed_refund_buyer_num`,
                SUM( `completed_refund_amount` ) `completed_refund_amount`,
                SUM( `completed_refund_sku_num` ) `completed_refund_sku_num`,
                SUM( `total_refund_duration` ) `total_refund_duration`
            </otherwise>
        </choose>

        FROM ${shopRefunderDayTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>

    <select id="selectShopTeamPerByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopTeamPerformanceDTO">
        SELECT

        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr,
                direct_receive_num, forward_in_num, forward_out_num,
                consult_num, receive_num, enquiry_num,
                ordered_num_today, ordered_goods_num_today,
                ordered_amount_today, ordered_num_final, ordered_goods_num_final,
                ordered_amount_final, paid_num_today, paid_amount_today,
                paid_goods_num_today, paid_num_today_next, paid_num_final,
                paid_goods_num_final, paid_amount_final, out_stock_order_buyer_num_final,
                out_stock_order_num_final, out_stock_order_goods_num_final,
                out_stock_order_amount_final
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `direct_receive_num` ) `direct_receive_num`,
                SUM( `forward_in_num` ) `forward_in_num`,
                SUM( `forward_out_num` ) `forward_out_num`,
                SUM( `consult_num` ) `consult_num`,
                SUM( `receive_num` ) `receive_num`,
                SUM( `enquiry_num` ) `enquiry_num`,
                SUM( `ordered_num_today` ) `ordered_num_today`,
                SUM( `ordered_goods_num_today` ) `ordered_goods_num_today`,
                SUM( `ordered_amount_today` ) `ordered_amount_today`,
                SUM( `ordered_num_final` ) `ordered_num_final`,
                SUM( `ordered_goods_num_final` ) `ordered_goods_num_final`,
                SUM( `ordered_amount_final` ) `ordered_amount_final`,
                SUM( `paid_num_today` ) `paid_num_today`,
                SUM( `paid_amount_today` ) `paid_amount_today`,
                SUM( `paid_goods_num_today` ) `paid_goods_num_today`,
                SUM( `paid_num_today_next` ) `paid_num_today_next`,
                SUM( `paid_num_final` ) `paid_num_final`,
                SUM( `paid_goods_num_final` ) `paid_goods_num_final`,
                SUM( `paid_amount_final` ) `paid_amount_final`,
                SUM( `out_stock_order_buyer_num_final` ) `out_stock_order_buyer_num_final`,
                SUM( `out_stock_order_num_final` ) `out_stock_order_num_final`,
                SUM( `out_stock_order_goods_num_final` ) `out_stock_order_goods_num_final`,
                SUM( `out_stock_order_amount_final` ) `out_stock_order_amount_final`
            </when>
            <otherwise>
                `shop_id`,
                SUM( `direct_receive_num` ) `direct_receive_num`,
                SUM( `forward_in_num` ) `forward_in_num`,
                SUM( `forward_out_num` ) `forward_out_num`,
                SUM( `consult_num` ) `consult_num`,
                SUM( `receive_num` ) `receive_num`,
                SUM( `enquiry_num` ) `enquiry_num`,
                SUM( `ordered_num_today` ) `ordered_num_today`,
                SUM( `ordered_goods_num_today` ) `ordered_goods_num_today`,
                SUM( `ordered_amount_today` ) `ordered_amount_today`,
                SUM( `ordered_num_final` ) `ordered_num_final`,
                SUM( `ordered_goods_num_final` ) `ordered_goods_num_final`,
                SUM( `ordered_amount_final` ) `ordered_amount_final`,
                SUM( `paid_num_today` ) `paid_num_today`,
                SUM( `paid_amount_today` ) `paid_amount_today`,
                SUM( `paid_goods_num_today` ) `paid_goods_num_today`,
                SUM( `paid_num_today_next` ) `paid_num_today_next`,
                SUM( `paid_num_final` ) `paid_num_final`,
                SUM( `paid_goods_num_final` ) `paid_goods_num_final`,
                SUM( `paid_amount_final` ) `paid_amount_final`,
                SUM( `out_stock_order_buyer_num_final` ) `out_stock_order_buyer_num_final`,
                SUM( `out_stock_order_num_final` ) `out_stock_order_num_final`,
                SUM( `out_stock_order_goods_num_final` ) `out_stock_order_goods_num_final`,
                SUM( `out_stock_order_amount_final` ) `out_stock_order_amount_final`
            </otherwise>
        </choose>

        FROM ${shopTeamPerTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>


    <select id="selectShopTeamOrderPerByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopTeamOrderPerformanceDTO">
        SELECT

        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr,
                to_ordered_num, to_ordered_order_num, to_ordered_goods_num, to_ordered_amount,
                to_ordered_paid_num_today, to_ordered_paid_order_num_today, to_ordered_paid_goods_today,
                to_ordered_paid_amount_today, to_ordered_paid_num_final, to_ordered_paid_order_num_final,

                to_ordered_paid_goods_final, to_ordered_paid_amount_final, to_ordered_out_stock_num,

                to_ordered_out_stock_amount, to_ordered_out_stock_goods_num, to_ordered_out_stock_order_num,

                sale_amount, sale_order_num, sale_goods_num, sale_buyer_num, sale_sku_num, post_fee,

                out_stock_num, out_stock_amount, out_stock_goods_num, out_stock_order_num, cfm_goods_order_num,
                cfm_goods_amount, cfm_goods_num, cfm_goods_buyer_num
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `to_ordered_num` ) `to_ordered_num`,
                SUM( `to_ordered_order_num` ) `to_ordered_order_num`,
                SUM( `to_ordered_goods_num` ) `to_ordered_goods_num`,
                SUM( `to_ordered_amount` ) `to_ordered_amount`,
                SUM( `to_ordered_paid_num_today` ) `to_ordered_paid_num_today`,
                SUM( `to_ordered_paid_order_num_today` ) `to_ordered_paid_order_num_today`,
                SUM( `to_ordered_paid_goods_today` ) `to_ordered_paid_goods_today`,
                SUM( `to_ordered_paid_amount_today` ) `to_ordered_paid_amount_today`,
                SUM( `to_ordered_paid_num_final` ) `to_ordered_paid_num_final`,
                SUM( `to_ordered_paid_order_num_final` ) `to_ordered_paid_order_num_final`,
                SUM( `to_ordered_paid_goods_final` ) `to_ordered_paid_goods_final`,
                SUM( `to_ordered_paid_amount_final` ) `to_ordered_paid_amount_final`,
                SUM( `to_ordered_out_stock_num` ) `to_ordered_out_stock_num`,
                SUM( `to_ordered_out_stock_amount` ) `to_ordered_out_stock_amount`,
                SUM( `to_ordered_out_stock_goods_num` ) `to_ordered_out_stock_goods_num`,
                SUM( `to_ordered_out_stock_order_num` ) `to_ordered_out_stock_order_num`,
                SUM( `sale_amount` ) `sale_amount`,
                SUM( `sale_order_num` ) `sale_order_num`,
                SUM( `sale_goods_num` ) `sale_goods_num`,
                SUM( `sale_buyer_num` ) `sale_buyer_num`,
                SUM( `sale_sku_num` ) `sale_sku_num`,
                SUM( `post_fee` ) `post_fee`,
                SUM( `out_stock_num` ) `out_stock_num`,
                SUM( `out_stock_amount` ) `out_stock_amount`,
                SUM( `out_stock_goods_num` ) `out_stock_goods_num`,
                SUM( `out_stock_order_num` ) `out_stock_order_num`,
                SUM( `cfm_goods_order_num` ) `cfm_goods_order_num`,
                SUM( `cfm_goods_amount` ) `cfm_goods_amount`,
                SUM( `cfm_goods_num` ) `cfm_goods_num`,
                SUM( `cfm_goods_buyer_num` ) `cfm_goods_buyer_num`
            </when>
            <otherwise>
                shop_id,
                SUM( `to_ordered_num` ) `to_ordered_num`,
                SUM( `to_ordered_order_num` ) `to_ordered_order_num`,
                SUM( `to_ordered_goods_num` ) `to_ordered_goods_num`,
                SUM( `to_ordered_amount` ) `to_ordered_amount`,
                SUM( `to_ordered_paid_num_today` ) `to_ordered_paid_num_today`,
                SUM( `to_ordered_paid_order_num_today` ) `to_ordered_paid_order_num_today`,
                SUM( `to_ordered_paid_goods_today` ) `to_ordered_paid_goods_today`,
                SUM( `to_ordered_paid_amount_today` ) `to_ordered_paid_amount_today`,
                SUM( `to_ordered_paid_num_final` ) `to_ordered_paid_num_final`,
                SUM( `to_ordered_paid_order_num_final` ) `to_ordered_paid_order_num_final`,
                SUM( `to_ordered_paid_goods_final` ) `to_ordered_paid_goods_final`,
                SUM( `to_ordered_paid_amount_final` ) `to_ordered_paid_amount_final`,
                SUM( `to_ordered_out_stock_num` ) `to_ordered_out_stock_num`,
                SUM( `to_ordered_out_stock_amount` ) `to_ordered_out_stock_amount`,
                SUM( `to_ordered_out_stock_goods_num` ) `to_ordered_out_stock_goods_num`,
                SUM( `to_ordered_out_stock_order_num` ) `to_ordered_out_stock_order_num`,
                SUM( `sale_amount` ) `sale_amount`,
                SUM( `sale_order_num` ) `sale_order_num`,
                SUM( `sale_goods_num` ) `sale_goods_num`,
                SUM( `sale_buyer_num` ) `sale_buyer_num`,
                SUM( `sale_sku_num` ) `sale_sku_num`,
                SUM( `post_fee` ) `post_fee`,
                SUM( `out_stock_num` ) `out_stock_num`,
                SUM( `out_stock_amount` ) `out_stock_amount`,
                SUM( `out_stock_goods_num` ) `out_stock_goods_num`,
                SUM( `out_stock_order_num` ) `out_stock_order_num`,
                SUM( `cfm_goods_order_num` ) `cfm_goods_order_num`,
                SUM( `cfm_goods_amount` ) `cfm_goods_amount`,
                SUM( `cfm_goods_num` ) `cfm_goods_num`,
                SUM( `cfm_goods_buyer_num` ) `cfm_goods_buyer_num`
            </otherwise>
        </choose>

        FROM ${shopTeamOrderPerTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>


    <select id="selectShopOrderEvaluateByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopOrderEvaluateDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr,
                `good_evaluate_num`, `neutral_evaluate_num`, `bad_evaluate_num`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `good_evaluate_num` ) `good_evaluate_num`,
                SUM( `neutral_evaluate_num` ) `neutral_evaluate_num`,
                SUM( `bad_evaluate_num` ) `bad_evaluate_num`
            </when>
            <otherwise>
                `shop_id`,
                SUM( `good_evaluate_num` ) `good_evaluate_num`,
                SUM( `neutral_evaluate_num` ) `neutral_evaluate_num`,
                SUM( `bad_evaluate_num` ) `bad_evaluate_num`
            </otherwise>
        </choose>

        FROM ${shopOrderEvaluateTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>

    <select id="selectShopTeamSessionServiceIndexByShopIdAndDate"
            resultType="com.pes.jd.model.DTO.ShopTeamSessionServiceIndexDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr, consult_session_num, receive_session_num,
                receive_session_duration_time,
                direct_receive_session_num, forward_in_session_num, forward_out_session_num, cust_consult_session_num,
                cs_to_cust_session_num, chat_num, cust_chat_num, cs_chat_num, cs_word_num, avg_cs_msg_session_num,
                max_receive_session_num, non_reply_session_num, leave_msg_session_num, leave_msg_receive_session_num,
                slow_resp_session_num, long_resp_session_num, avg_resp_time_first, avg_resp_time,
                avg_session_duration_time, leave_msg_advisory_session_num, avg_resp_in_quick_time,
                session_num,chat_round_num,chat_round_num_no_leave, resp_time_first_count, resp_time_count, session_duration_time_count,empty_chat_num,
                dd_consult_session_num,dd_receive_session_num
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `consult_session_num` ) `consult_session_num`,
                SUM( `receive_session_num` ) `receive_session_num`,
                SUM( `receive_session_duration_time` ) `receive_session_duration_time`,
                SUM( `direct_receive_session_num` ) `direct_receive_session_num`,
                SUM( `forward_in_session_num` ) `forward_in_session_num`,
                SUM( `forward_out_session_num` ) `forward_out_session_num`,
                SUM( `cust_consult_session_num` ) `cust_consult_session_num`,
                SUM( `cs_to_cust_session_num` ) `cs_to_cust_session_num`,
                SUM( `chat_num` ) `chat_num`,
                SUM( `cust_chat_num` ) `cust_chat_num`,
                SUM( `cs_chat_num` ) `cs_chat_num`,
                SUM( `cs_word_num` ) `cs_word_num`,
                SUM( `avg_cs_msg_session_num` ) `avg_cs_msg_session_num`,
                SUM( `max_receive_session_num` ) `max_receive_session_num`,
                SUM( `non_reply_session_num` ) `non_reply_session_num`,
                SUM( `leave_msg_session_num` ) `leave_msg_session_num`,
                SUM( `leave_msg_receive_session_num` ) `leave_msg_receive_session_num`,
                SUM( `slow_resp_session_num` ) `slow_resp_session_num`,
                SUM( `long_resp_session_num` ) `long_resp_session_num`,
                SUM( `avg_resp_time_first` ) `avg_resp_time_first`,
                SUM( `avg_resp_time` ) `avg_resp_time`,
                SUM( `avg_session_duration_time` ) `avg_session_duration_time`,
                SUM( `leave_msg_advisory_session_num` ) `leave_msg_advisory_session_num`,
                SUM( `avg_resp_in_quick_time` ) `avg_resp_in_quick_time`,
                SUM( `session_num` ) `session_num`,
                SUM( `chat_round_num` ) `chat_round_num`,
                SUM( `chat_round_num_no_leave` ) `chat_round_num_no_leave`,
                SUM( `resp_time_first_count` ) `resp_time_first_count`,
                SUM( `resp_time_count` ) `resp_time_count`,
                SUM( `session_duration_time_count` ) `session_duration_time_count`,
                SUM( `empty_chat_num` ) `empty_chat_num`,
                SUM( `dd_consult_session_num` ) `dd_consult_session_num`,
                SUM( `dd_receive_session_num` ) `dd_receive_session_num`
            </when>
            <otherwise>
                `shop_id`,
                SUM( `consult_session_num` ) `consult_session_num`,
                SUM( `receive_session_num` ) `receive_session_num`,
                SUM( `receive_session_duration_time` ) `receive_session_duration_time`,
                SUM( `direct_receive_session_num` ) `direct_receive_session_num`,
                SUM( `forward_in_session_num` ) `forward_in_session_num`,
                SUM( `forward_out_session_num` ) `forward_out_session_num`,
                SUM( `cust_consult_session_num` ) `cust_consult_session_num`,
                SUM( `cs_to_cust_session_num` ) `cs_to_cust_session_num`,
                SUM( `chat_num` ) `chat_num`,
                SUM( `cust_chat_num` ) `cust_chat_num`,
                SUM( `cs_chat_num` ) `cs_chat_num`,
                SUM( `cs_word_num` ) `cs_word_num`,
                SUM( `avg_cs_msg_session_num` ) `avg_cs_msg_session_num`,
                SUM( `max_receive_session_num` ) `max_receive_session_num`,
                SUM( `non_reply_session_num` ) `non_reply_session_num`,
                SUM( `leave_msg_session_num` ) `leave_msg_session_num`,
                SUM( `leave_msg_receive_session_num` ) `leave_msg_receive_session_num`,
                SUM( `slow_resp_session_num` ) `slow_resp_session_num`,
                SUM( `long_resp_session_num` ) `long_resp_session_num`,
                SUM( `avg_resp_time_first` ) `avg_resp_time_first`,
                SUM( `avg_resp_time` ) `avg_resp_time`,
                SUM( `avg_session_duration_time` ) `avg_session_duration_time`,
                SUM( `leave_msg_advisory_session_num` ) `leave_msg_advisory_session_num`,
                SUM( `avg_resp_in_quick_time` ) `avg_resp_in_quick_time`,
                SUM( `session_num` ) `session_num`,
                SUM( `chat_round_num` ) `chat_round_num`,
                SUM( `chat_round_num_no_leave` ) `chat_round_num_no_leave`,
                SUM( `resp_time_first_count` ) `resp_time_first_count`,
                SUM( `resp_time_count` ) `resp_time_count`,
                SUM( `session_duration_time_count` ) `session_duration_time_count`,
                SUM( `empty_chat_num` ) `empty_chat_num`,
                SUM( `dd_consult_session_num` ) `dd_consult_session_num`,
                SUM( `dd_receive_session_num` ) `dd_receive_session_num`
            </otherwise>
        </choose>
        FROM ${shopTeamSessionServiceIndexTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>


    <select id="selectShopTeamAssitIndexByShopIdAndDate"
            resultType="com.pes.jd.model.DTO.ShopTeamAssitIndexDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                shop_id, `date`, `date` AS dateStr,
                assit_order_create_num, assit_order_create_amount, assit_order_pay_num,
                assit_order_pay_amount, assit_order_followup_num, assit_order_followup_amount
            </when>
            <!--月份-->
            <when test="dateType == 2">
                shop_id,
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
                SUM( `assit_order_create_num` ) `assit_order_create_num`,
                SUM( `assit_order_create_amount` ) `assit_order_create_amount`,
                SUM( `assit_order_pay_num` ) `assit_order_pay_num`,
                SUM( `assit_order_pay_amount` ) `assit_order_pay_amount`,
                SUM( `assit_order_followup_num` ) `assit_order_followup_num`,
                SUM( `assit_order_followup_amount` ) `assit_order_followup_amount`

            </when>
            <otherwise>
                `shop_id`,
                SUM( `assit_order_create_num` ) `assit_order_create_num`,
                SUM( `assit_order_create_amount` ) `assit_order_create_amount`,
                SUM( `assit_order_pay_num` ) `assit_order_pay_num`,
                SUM( `assit_order_pay_amount` ) `assit_order_pay_amount`,
                SUM( `assit_order_followup_num` ) `assit_order_followup_num`,
                SUM( `assit_order_followup_amount` ) `assit_order_followup_amount`
            </otherwise>
        </choose>
        FROM ${shopTeamAssitIndexTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>


    <select id="selectCsRefundDayLstByShopIdAndDate"
            resultType="com.pes.jd.model.DTO.CsRefundDayDTO">
        SELECT
        shop_id,
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                `date`, `date` AS dateStr,
            </when>
            <!--月份-->
            <when test="dateType == 2">
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        SUM( `completed_refund_num` ) `completed_refund_num`,
        SUM( `completed_refund_product_num` ) `completed_refund_product_num`,
        SUM( `completed_refund_buyer_num` ) `completed_refund_buyer_num`,
        SUM( `completed_refund_amount` ) `completed_refund_amount`
        FROM ${csRefundDayTableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY `date`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>

    <select id="selectTeamLossRecordByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopTeamLossRecordDTO">
        SELECT shop_id, `date`, order_num, customer_num, order_goods_num, order_sale_amount, `type`
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </when>
            <otherwise>
                <!--日期-->
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
            </otherwise>
        </choose>
    </select>

    <select id="selectShopSaleAmount" resultType="com.pes.jd.model.DTO.ShopTeamOrderPerformanceDTO">
        select to_ordered_num, to_ordered_order_num, to_ordered_paid_num_today, to_ordered_paid_order_num_today, sale_amount, sale_buyer_num
        from ${tableName}
        where
        shop_id = #{shopId}
        and date = #{date}
    </select>

</mapper>