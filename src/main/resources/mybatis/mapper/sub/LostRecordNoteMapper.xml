<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.LostRecordNoteMapper" >
  <resultMap id="LostRecordNoteDO" type="com.pes.jd.model.DO.LostRecordNote" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="lost_type" property="lostType" jdbcType="TINYINT" />
    <result column="note" property="note" jdbcType="VARCHAR" />
  </resultMap>
  
  <resultMap id="LostRecordNoteDTO" type="com.pes.jd.model.DTO.LostRecordNoteDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="lost_type" property="lostType" jdbcType="TINYINT" />
    <result column="note" property="note" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, buyer_nick, order_id, date, lost_type, note
  </sql>
  
  <insert id="insertLostRecordNote" parameterType="com.pes.jd.model.DO.LostRecordNote" >
    INSERT INTO ${tableName}
    	(shop_id, buyer_nick,  order_id, date, lost_type, note )
    VALUES 
    	(#{shopId,jdbcType=BIGINT}, #{buyerNick,jdbcType=VARCHAR}, 
      	#{orderId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, #{lostType,jdbcType=TINYINT}, #{note,jdbcType=VARCHAR} )
  </insert>
  
  <insert id="insertLostRecordNoteForShop" parameterType="map" >
    INSERT INTO ${tableName}
    	(shop_id, buyer_nick,  order_id, date, lost_type, note )
    VALUES 
    	(#{lostRecordNote.shopId,jdbcType=BIGINT}, #{lostRecordNote.buyerNick,jdbcType=VARCHAR}, 
      	#{lostRecordNote.orderId,jdbcType=BIGINT}, #{lostRecordNote.date,jdbcType=DATE}, #{lostRecordNote.lostType}, #{lostRecordNote.note,jdbcType=VARCHAR} )
  </insert>
  
  <delete id="deleteLostRecordNoteById" parameterType="java.lang.Long" >
    DELETE FROM pes_lost_record_note
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateLostRecordNoteForShop" parameterType="map" >
    UPDATE ${tableName}
    SET note = #{lostRecordNote.note,jdbcType=VARCHAR}
    WHERE id = #{lostRecordNote.id,jdbcType=BIGINT}
  </update>
  
 <select id="getLostRecordNoteById" resultMap="LostRecordNoteDO" parameterType="java.lang.Long" >
    SELECT 
    	<include refid="base_field" />
    FROM pes_lost_record_note
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectLostRecordNoteLst"  parameterType="map" resultMap="LostRecordNoteDTO">
	SELECT id, buyer_nick, order_id, date, lost_type, note 
	FROM  ${tableName}
	WHERE
		date = #{date}
	<!--<if test="buyerNick != null and buyerNick!=''" >
	    AND buyer_nick = #{buyerNick}
	    </if> -->
	    <if test="lostType != null" >
	    AND lost_type = #{lostType}
	    </if>
	    AND shop_id = #{shopId}
  </select>
  
  <select id="getShopBuyerLostRecordNoteByDate"  parameterType="map" resultMap="LostRecordNoteDTO">
	SELECT id, buyer_nick, order_id, date, lost_type, note 
	FROM  ${tableName}
	WHERE
		date = #{date}
		<if test="orderId != null and orderId != ''">
	    AND order_id = #{orderId}
		</if>
	    AND buyer_nick = #{buyerNick}
	    AND lost_type = #{lostType}
	    AND shop_id = #{shopId}
  </select>
    
    <select id="selectByBuyerNickDate" resultMap="LostRecordNoteDTO" parameterType="map">
      SELECT
      <include refid="base_field"/>
      FROM ${tableName}
      WHERE
      date BETWEEN #{startDate} AND #{endDate}
        <if test="buyerNick != null">
        AND buyer_nick = #{buyerNick}
        </if>
        AND lost_type = 0
    </select>


        <!-- 根据orderid查备注 -->
    <select id="selectByOrderId" resultMap="LostRecordNoteDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        <where>
            lost_type = 0
            <if test="orderIds.size>0">
            AND order_id IN
            <foreach collection="orderIds" item="o" separator="," close=")" open="(">
                #{o}
            </foreach>
            </if>
        </where>
    </select>

    <!-- 根据orderid查备注 -->
    <select id="selectByBuyerNickDateShop" resultMap="LostRecordNoteDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE
         shop_id = #{shopId}
        <if test="buyerNick!=null and buyerNick!=''">
            AND buyer_nick = #{buyerNick}
        </if>
        AND lost_type = 0
        AND date BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectLostRecordNoteLstByDateByBuyerNickList" parameterType="map" resultMap="LostRecordNoteDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE date BETWEEN #{startDate} AND #{endDate}
        AND buyer_nick IN
        <foreach collection="buyerNickList" item="buyerNick" open="(" close=")" separator=",">
        	#{buyerNick}
        </foreach> 
        AND lost_type = #{lostType}
        AND shop_id = #{shopId}
    </select>

    <select id="selectLostRecordNoteLstByOrderIdList" parameterType="map" resultMap="LostRecordNoteDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
        	#{orderId}
        </foreach> 
        AND shop_id = #{shopId}
    </select>
</mapper>