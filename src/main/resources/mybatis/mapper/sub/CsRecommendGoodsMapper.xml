<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsRecommendGoodsMapper" >
  <resultMap id="CsRecommendGoodsDO" type="com.pes.jd.model.DO.CsRecommendGoodsDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
  </resultMap>

   <resultMap id="CsRecommendGoodsDTO" type="com.pes.jd.model.DTO.CsRecommendGoodsDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
     <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, sku_id, customer, cs_nick, result
  </sql>

  <insert id="insertCsRecommendGoods" parameterType="com.pes.jd.model.DO.CsRecommendGoodsDO" >
      INSERT INTO pes_cs_recommend_goods (id, shop_id, date,
                                          sku_id, customer, cs_nick,
      result)
      VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE},
              #{skuId,jdbcType=BIGINT}, #{customer,jdbcType=VARCHAR}, #{csNick,jdbcType=VARCHAR},
      #{result,jdbcType=INTEGER})
  </insert>
  <delete id="deleteCsRecommendGoodsById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_recommend_goods
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>

  <update id="updateCsRecommendGoods" parameterType="com.pes.jd.model.DO.CsRecommendGoodsDO" >
    UPDATE pes_cs_recommend_goods
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="customer != null" >
        customer = #{customer,jdbcType=VARCHAR},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=INTEGER},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>

   <select id="selectCsRecommendGoodsById" resultMap="CsRecommendGoodsDO" parameterType="java.lang.Long" >
       SELECT
    <include refid="base_field" />
    FROM pes_cs_recommend_goods
    WHERE id = #{id,jdbcType=BIGINT}
  </select>

 <select id="selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLst" resultMap="CsRecommendGoodsDTO">
 select recommend.shop_id,
	   recommend.date,
     recommend.sku_id,
  	   recommend.customer,
   	   recommend.cs_nick,
       recommend.result
     FROM
  	(<foreach collection="tableNames" item="table" separator="union all">
  		select shop_id,
			   date,
     sku_id,
		  	   customer,
		   	   cs_nick,
		       result
       from ${table.tableName}
       <where>
	  		shop_id=#{shopId}
	  	AND date between #{table.beginDate} and #{table.endDate}
	  	<if test="param.csNickLst!=null and param.csNickLst.size>0">
	  		AND	cs_nick in
	  		<foreach collection="param.csNickLst" item="csNick" open="(" close=")" separator=",">
	  			#{csNick}
	  		</foreach>
	  	</if>
	  	<if test="param.skuLst!=null and param.skuLst.size>0">
	  		 AND sku_id in
		  	<foreach collection="param.skuLst" item="skuId" open="(" close=")" separator=",">
		  			#{skuId}
		  	</foreach>
	  	</if>
	  	<if test="param.customer!=null and param.customer!=''">
	  		and customer =#{param.customer}
	  	</if>
	  	<if test="param.result!=null and param.result!='' ">
            and result=#{param.result}
	  	</if>
  	</where>
  	</foreach>
  	)
  	recommend
	<if test="query.sort and query.field!=null and query.field!='' ">
		order by ${query.field} ${query.sortDirection}
	</if>
	<if test="query.currentPage!=null and query.size>0">
		limit #{query.currentPage},#{query.size}
	</if>
 </select>


    <select id="selectCsRecommendGoodsByDateByCsNickByCustomerByResultBySkuLstV2" resultMap="CsRecommendGoodsDTO">
        select
        recommend.shop_id,
        recommend.date,
        recommend.sku_id,
        recommend.customer,
        recommend.cs_nick,
        recommend.result
        FROM
        (
        <foreach collection="tableNames" item="table" separator="union all">
            select
            r.shop_id,
            r.date,
            r.sku_id,
            r.customer,
            r.cs_nick,
            r.result
            from ${table.tableName} r
            <if test="param.catgoryLst!=null and param.catgoryLst.size>0">
                inner join ${goodsSkuTable} gs on r.shop_id = gs.shop_id and r.sku_id = gs.sku_id
            </if>
            <where>
                r.shop_id=#{shopId}
                AND r.date between #{table.beginDate} and #{table.endDate}
                <if test="param.csNickLst!=null and param.csNickLst.size>0">
                    AND r.cs_nick in
                    <foreach collection="param.csNickLst" item="csNick" open="(" close=")" separator=",">
                        #{csNick}
                    </foreach>
                </if>
                <if test="param.skuLst!=null and param.skuLst.size>0">
                    AND r.sku_id in
                    <foreach collection="param.skuLst" item="skuId" open="(" close=")" separator=",">
                        #{skuId}
                    </foreach>
                </if>
                <if test="param.customer!=null and param.customer!=''">
                    and r.customer =#{param.customer}
                </if>
                <if test="param.result!=null and param.result!='' ">
                    and r.result=#{param.result}
                </if>
                <if test="param.catgoryLst!=null and param.catgoryLst.size>0">
                    and gs.category_id in
                    <foreach collection="param.catgoryLst" item="catId" separator="," open="(" close=")">
                        #{catId}
                    </foreach>
                </if>
            </where>
        </foreach>
        ) recommend
        <if test="query.sort and query.field!=null and query.field!='' ">
            order by ${query.field} ${query.sortDirection}
        </if>
        <if test="query.currentPage!=null and query.size>0">
            limit #{query.currentPage},#{query.size}
        </if>
    </select>


    <!-- 查询推荐明细的数据总条数 -->
 <select id="selectCsRecommendGoodsCount" resultType="java.lang.Integer">
 select count(recommend.id)
     FROM
  	(<foreach collection="tableNames" item="table" separator="union all">
  		select id,sku_id
       	from ${table.tableName}
       <where>
  	<if test="shopId!=null and shopId!=''">
  		shop_id=#{shopId}
  	</if>
  	AND date between #{table.beginDate} and #{table.endDate}
  	<if test="param.csNickLst!=null and param.csNickLst.size>0">
  		AND	cs_nick in
  		<foreach collection="param.csNickLst" item="csNick" open="(" close=")" separator=",">
  			#{csNick}
  		</foreach>
  	</if>
  	<if test="param.skuLst!=null and param.skuLst.size>0">
  		 AND sku_id in
	  	<foreach collection="param.skuLst" item="skuId" open="(" close=")" separator=",">
	  			#{skuId}
	  	</foreach>
  	</if>
  	<if test="param.customer!=null and param.customer!=''">
  		and customer =#{param.customer}
  	</if>
  	<if test="param.result!=null and param.result!='' ">
        and result=#{param.result}
  	</if>
  	</where>
  	</foreach>
  	)
  	recommend

 </select>


    <select id="selectCsRecommendGoodsCountV2" resultType="java.lang.Integer">
        select count(recommend.id)
        FROM
        (
        <foreach collection="tableNames" item="table" separator="union all">
            select r.id
            from ${table.tableName} r
            <if test="param.catgoryLst!=null and param.catgoryLst.size>0">
                inner join ${goodsSkuTable} gs on r.shop_id = gs.shop_id and r.sku_id = gs.sku_id
            </if>
            <where>
                <if test="shopId!=null and shopId!=''">
                    r.shop_id=#{shopId}
                </if>
                AND r.date between #{table.beginDate} and #{table.endDate}
                <if test="param.csNickLst!=null and param.csNickLst.size>0">
                    AND r.cs_nick in
                    <foreach collection="param.csNickLst" item="csNick" open="(" close=")" separator=",">
                        #{csNick}
                    </foreach>
                </if>
                <if test="param.skuLst!=null and param.skuLst.size>0">
                    AND r.sku_id in
                    <foreach collection="param.skuLst" item="skuId" open="(" close=")" separator=",">
                        #{skuId}
                    </foreach>
                </if>
                <if test="param.customer!=null and param.customer!=''">
                    and r.customer =#{param.customer}
                </if>
                <if test="param.result!=null and param.result!='' ">
                    and r.result=#{param.result}
                </if>
                <if test="param.catgoryLst!=null and param.catgoryLst.size>0">
                    and gs.category_id in
                    <foreach collection="param.catgoryLst" item="catId" separator="," open="(" close=")">
                        #{catId}
                    </foreach>
                </if>
            </where>
        </foreach>
        ) recommend
    </select>

</mapper>
