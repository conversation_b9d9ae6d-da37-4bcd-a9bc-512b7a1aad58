<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsLoginlogMapper" >
  <resultMap id="CsLoginlogDO" type="com.pes.jd.model.DO.CsLoginlog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="change_time" property="changeTime" jdbcType="TIMESTAMP" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
  </resultMap>
   <resultMap id="ShopCsLoginLogDTO" type="com.pes.jd.model.DTO.ShopCsLoginLogDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="INTEGER" />
  </resultMap>
  <sql id="base_field" >
    id, cs_nick, change_time, shop_id, type
  </sql>
  
  <select id="getCsLoginlogById" resultMap="CsLoginlogDO" parameterType="java.lang.Long" >
    SELECT 
    <include refid="base_field" />
    FROM pes_cs_loginlog
    WHERE id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="queryCsLoginlogByCsNicksByShopIdByDate" resultMap="CsLoginlogDO" parameterType="java.util.Map" >
    SELECT id, cs_nick, change_time, shop_id, type
    FROM ${tableName}
    WHERE 
    cs_nick IN 
    <foreach collection="csNicks" item="csNick" open="(" close=")" separator=",">
    	#{csNick}
    </foreach>
    AND change_time BETWEEN #{sDate,jdbcType=TIMESTAMP} AND #{eDate,jdbcType=TIMESTAMP}
    AND shop_id = #{shopId,jdbcType=BIGINT} ORDER BY change_time ,type
  </select>
  
  <delete id="deleteCsLoginlogById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_loginlog
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <select id="selectCsLoginLogForShopUserAnalysis" resultMap="ShopCsLoginLogDTO">
  	SELECT 
  	id, cs_nick,  
  	date_format(change_time,'%Y-%m-%d') date,
  	shop_id, type
  	FROM ${tableName}
  	<where>
  		shop_id in
  		<foreach collection="shopIdSet" item="shopId" open="(" close=")" separator=",">
  			#{shopId}
  		</foreach>
  			AND type=1
  		    AND change_time BETWEEN #{startDate} AND #{endDate}
     
  	</where>
  	
  </select>
</mapper>