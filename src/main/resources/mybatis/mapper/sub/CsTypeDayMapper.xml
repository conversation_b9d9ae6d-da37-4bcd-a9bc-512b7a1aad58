<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsTypeDayMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsTypeDayDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="cs_type" jdbcType="TINYINT" property="csType" />
    <result column="is_lock" jdbcType="BIT" property="isLock" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, cs_type, is_lock
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_cs_type_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_type_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CsTypeDayDO">
    insert into pes_cs_type_day (id, shop_id, date, 
      cs_nick, cs_type)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{csType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsTypeDayDO">
    insert into pes_cs_type_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="csType != null">
        cs_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="csType != null">
        #{csType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsTypeDayDO">
    update pes_cs_type_day
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="csType != null">
        cs_type = #{csType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsTypeDayDO">
    update pes_cs_type_day
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      cs_type = #{csType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="searchByShopDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from ${tableName}
    where date between #{startDate}  and #{endDate}
    and shop_id = #{shopId}
  </select>

  <select id="searchNoLockCsNickByShopDate" resultType="java.lang.String">
            select
            distinct cs_nick
            from ${tableName}
            where date between #{startDate}  and #{endDate}
            and shop_id = #{shopId}
            and is_lock =0
</select>
</mapper>