<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.SlientGoodsSaleIndexDetailMapper">
	<resultMap id="SlientGoodsSaleIndexDetailDTO" type="com.pes.jd.model.DTO.SlientGoodsSaleIndexDetailDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="customer" jdbcType="VARCHAR" property="customer" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="out_stock_time" jdbcType="TIMESTAMP" property="outStockTime" />
  </resultMap>
  
  <select id="selectGoodsSaleIndexDetailCount" resultType="Integer">
  	SELECT 
  		count(sale.id)
		FROM 
		(<foreach collection="tableNames" item="table" separator="union">
			select id ,sku_id from ${table.getTableName}
			<where>
				<if test="shopId!=null and shopId!=''">
					shop_id = #{shopId}
				</if>
                <if test="orderId!=null and orderId!=''">
					and order_id = #{orderId}
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in 
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
			</where>
		</foreach>) sale LEFT join ${tableName2} goods ON sale.sku_id = goods.sku_id
		
  </select>
  
  <select id="selectGoodsSaleIndexDetailBySkuByDate" resultMap="SlientGoodsSaleIndexDetailDTO">
  	SELECT 
  		sale.date,sale.sku_id,sale.customer,sale.order_id,
  		sale.sale_goods_num,sale.sale_amount,goods.sku_name,
  		sale.out_stock_time
		FROM 
		(<foreach collection="tableNames" item="table" separator="union">
			select date,sku_id,customer,order_id,sale_goods_num,shop_id,
					sale_amount,out_stock_time
			 from ${table.getTableName}
			<where>
				<if test="shopId!=null and shopId!=''">
					shop_id = #{shopId}
				</if>
                <if test="orderId!=null and orderId!=''">
                    and order_id = #{orderId}
                </if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in 
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
			</where>
		</foreach>) sale LEFT join ${tableName2} goods ON sale.sku_id = goods.sku_id
			<if test="sortPageQuery.sort">
				order by sale.${sortPageQuery.field} ${sortPageQuery.sortDirection}
			</if>
			<if test="sortPageQuery.currentPage != null and sortPageQuery.size != 0">
				LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
			</if>
  </select>

	<select id="selectGoodsSaleIndexDetailByShopIdBySkuByDate" resultMap="SlientGoodsSaleIndexDetailDTO">
		SELECT
		sale.date,sale.sku_id,sale.customer,sale.order_id,
		sale.sale_goods_num,sale.sale_amount
		FROM
		(<foreach collection="tableNames" item="table" separator="union">
		select date,sku_id,customer,order_id,sale_goods_num,shop_id,
		sale_amount
		from ${table.getTableName}
		<where>
			<if test="shopId!=null and shopId!=''">
				shop_id = #{shopId}
			</if>
			<if test="orderId!=null and orderId!=''">
				and order_id = #{orderId}
			</if>
			<if test="skuLst!=null and skuLst.size>0">
				and sku_id in
				<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
					#{skuId}
				</foreach>
			</if>
			<if test="bargainOrders!=null and bargainOrders.size>0">
				and order_id not in
				<foreach collection="bargainOrders" item="orderId" open="(" close=")" separator=",">
					#{orderId}
				</foreach>
			</if>
			and date between #{startDate} and #{endDate}
		</where>
	</foreach>) sale
		<if test="sortPageQuery.sort">
			order by sale.${sortPageQuery.field} ${sortPageQuery.sortDirection}
		</if>
		<if test="sortPageQuery.currentPage != null and sortPageQuery.size != 0">
			LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
		</if>
	</select>
</mapper>