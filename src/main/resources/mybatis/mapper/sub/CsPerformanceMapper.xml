<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsPerformanceMapper">
  <resultMap id="CsPerformanceDO" type="com.pes.jd.model.DO.CsPerformanceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="direct_receive_num" jdbcType="INTEGER" property="directReceiveNum" />
    <result column="forward_in_num" jdbcType="INTEGER" property="forwardInNum" />
    <result column="forward_out_num" jdbcType="INTEGER" property="forwardOutNum" />
    <result column="consult_num" jdbcType="INTEGER" property="consultNum" />
    <result column="receive_num" jdbcType="INTEGER" property="receiveNum" />
    <result column="enquiry_num" jdbcType="INTEGER" property="enquiryNum" />
    <result column="ordered_num_today" jdbcType="INTEGER" property="orderedNumToday" />
    <result column="ordered_goods_num_today" jdbcType="INTEGER" property="orderedGoodsNumToday" />
    <result column="ordered_amount_today" jdbcType="DOUBLE" property="orderedAmountToday" />
    <result column="ordered_num_final" jdbcType="INTEGER" property="orderedNumFinal" />
    <result column="ordered_goods_num_final" jdbcType="INTEGER" property="orderedGoodsNumFinal" />
    <result column="ordered_amount_final" jdbcType="DOUBLE" property="orderedAmountFinal" />
    <result column="paid_num_today" jdbcType="INTEGER" property="paidNumToday" />
    <result column="paid_amount_today" jdbcType="DOUBLE" property="paidAmountToday" />
    <result column="paid_goods_num_today" jdbcType="INTEGER" property="paidGoodsNumToday" />
    <result column="paid_num_today_next" jdbcType="INTEGER" property="paidNumTodayNext" />
    <result column="paid_num_final" jdbcType="INTEGER" property="paidNumFinal" />
    <result column="paid_goods_num_final" jdbcType="INTEGER" property="paidGoodsNumFinal" />
    <result column="paid_amount_final" jdbcType="DOUBLE" property="paidAmountFinal" />
    <result column="receive_rate" jdbcType="DOUBLE" property="receiveRate" />
    <result column="conversion_rate" jdbcType="DOUBLE" property="conversionRate" />
    <result column="out_stock_order_buyer_num_final" jdbcType="INTEGER" property="outStockOrderBuyerNumFinal" />
    <result column="out_stock_order_num_final" jdbcType="INTEGER" property="outStockOrderNumFinal" />
    <result column="out_stock_order_goods_num_final" jdbcType="INTEGER" property="outStockOrderGoodsNumFinal" />
    <result column="out_stock_order_amount_final" jdbcType="INTEGER" property="outStockOrderAmountFinal" />
  </resultMap>
  <resultMap id="CsPerformanceDTO" type="com.pes.jd.model.DTO.CsPerformanceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="direct_receive_num" jdbcType="INTEGER" property="directReceiveNum" />
    <result column="forward_in_num" jdbcType="INTEGER" property="forwardInNum" />
    <result column="forward_out_num" jdbcType="INTEGER" property="forwardOutNum" />
    <result column="consult_num" jdbcType="INTEGER" property="consultNum" />
    <result column="receive_num" jdbcType="INTEGER" property="receiveNum" />
    <result column="enquiry_num" jdbcType="INTEGER" property="enquiryNum" />
    <result column="ordered_num_today" jdbcType="INTEGER" property="orderedNumToday" />
    <result column="ordered_goods_num_today" jdbcType="INTEGER" property="orderedGoodsNumToday" />
    <result column="ordered_amount_today" jdbcType="DOUBLE" property="orderedAmountToday" />
    <result column="ordered_num_final" jdbcType="INTEGER" property="orderedNumFinal" />
    <result column="ordered_goods_num_final" jdbcType="INTEGER" property="orderedGoodsNumFinal" />
    <result column="ordered_amount_final" jdbcType="DOUBLE" property="orderedAmountFinal" />
    <result column="paid_num_today" jdbcType="INTEGER" property="paidNumToday" />
    <result column="paid_amount_today" jdbcType="DOUBLE" property="paidAmountToday" />
    <result column="paid_goods_num_today" jdbcType="INTEGER" property="paidGoodsNumToday" />
    <result column="paid_num_today_next" jdbcType="INTEGER" property="paidNumTodayNext" />
    <result column="paid_num_final" jdbcType="INTEGER" property="paidNumFinal" />
    <result column="paid_goods_num_final" jdbcType="INTEGER" property="paidGoodsNumFinal" />
    <result column="paid_amount_final" jdbcType="DOUBLE" property="paidAmountFinal" />
    <result column="receive_rate" jdbcType="DOUBLE" property="receiveRate" />
    <result column="conversion_rate" jdbcType="DOUBLE" property="conversionRate" />
    <result column="out_stock_order_buyer_num_final" jdbcType="INTEGER" property="outStockOrderBuyerNumFinal" />
    <result column="out_stock_order_num_final" jdbcType="INTEGER" property="outStockOrderNumFinal" />
    <result column="out_stock_order_goods_num_final" jdbcType="INTEGER" property="outStockOrderGoodsNumFinal" />
    <result column="out_stock_order_amount_final" jdbcType="INTEGER" property="outStockOrderAmountFinal" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="base_field">
    id, shop_id, date, cs_nick, direct_receive_num, forward_in_num, forward_out_num,
    consult_num, receive_num, enquiry_num, ordered_num_today, ordered_goods_num_today,
    ordered_amount_today, ordered_num_final, ordered_goods_num_final, ordered_amount_final,
    paid_num_today, paid_amount_today, paid_goods_num_today, paid_num_today_next, paid_num_final,
    paid_goods_num_final, paid_amount_final, receive_rate, conversion_rate, out_stock_order_buyer_num_final,
    out_stock_order_num_final, out_stock_order_goods_num_final, out_stock_order_amount_final
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="CsPerformanceDO">
    select 
    <include refid="base_field" />
    from pes_cs_performance
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchByDateShopCs" resultMap="CsPerformanceDTO">
    select
    <include refid="base_field"/>
    from ${tableName}
    where shop_id = #{shopId}
    <if test="nicks!=null and nicks.size()>0">
      AND cs_nick in
      <foreach collection="nicks" open="(" close=")" separator="," item="nick">
        #{nick}
      </foreach>
    </if>
    AND date between #{startDate} and #{endDate}
    <if test="filterDates != null and filterDates.size()>0">
      and date not in 
      <foreach collection="filterDates" open="(" close=")" separator="," item="d">
        #{d}
      </foreach>
    </if>
    order by date desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_performance
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CsPerformanceDO">
    insert into pes_cs_performance (id, shop_id, date, 
      cs_nick, direct_receive_num, forward_in_num, 
      forward_out_num, consult_num, receive_num, 
      enquiry_num, ordered_num_today, paid_num_today, 
      ordered_amount_today, paid_amount_today, ordered_num_final, 
      ordered_amount_final, ordered_goods_num_final, 
      paid_num_today_next, paid_num_final, paid_amount_final, 
      paid_goods_num_final, receive_rate, conversion_rate, 
      sale_amount, sale_order_num, sale_goods_num, 
      sale_buyer_num, cfm_goods_o_num, cfm_goods_amount, 
      post_fee, out_stock_num, out_stock_amount, 
      out_stock_goods_num, out_stock_order_num, out_stock_loss_num, 
      out_stock_loss_goods_num, out_stock_loss_order_num, 
      out_stock_loss_amount)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{directReceiveNum,jdbcType=INTEGER}, #{forwardInNum,jdbcType=INTEGER}, 
      #{forwardOutNum,jdbcType=INTEGER}, #{consultNum,jdbcType=INTEGER}, #{receiveNum,jdbcType=INTEGER}, 
      #{enquiryNum,jdbcType=INTEGER}, #{orderedNumToday,jdbcType=INTEGER}, #{paidNumToday,jdbcType=INTEGER}, 
      #{orderedAmountToday,jdbcType=DOUBLE}, #{paidAmountToday,jdbcType=DOUBLE}, #{orderedNumFinal,jdbcType=INTEGER}, 
      #{orderedAmountFinal,jdbcType=DOUBLE}, #{orderedGoodsNumFinal,jdbcType=INTEGER}, 
      #{paidNumTodayNext,jdbcType=INTEGER}, #{paidNumFinal,jdbcType=INTEGER}, #{paidAmountFinal,jdbcType=DOUBLE}, 
      #{paidGoodsNumFinal,jdbcType=INTEGER}, #{receiveRate,jdbcType=DOUBLE}, #{conversionRate,jdbcType=DOUBLE}, 
      #{saleAmount,jdbcType=DOUBLE}, #{saleOrderNum,jdbcType=INTEGER}, #{saleGoodsNum,jdbcType=INTEGER}, 
      #{saleBuyerNum,jdbcType=INTEGER}, #{cfmGoodsONum,jdbcType=INTEGER}, #{cfmGoodsAmount,jdbcType=DOUBLE}, 
      #{postFee,jdbcType=DOUBLE}, #{outStockNum,jdbcType=INTEGER}, #{outStockAmount,jdbcType=DOUBLE}, 
      #{outStockGoodsNum,jdbcType=INTEGER}, #{outStockOrderNum,jdbcType=INTEGER}, #{outStockLossNum,jdbcType=INTEGER}, 
      #{outStockLossGoodsNum,jdbcType=INTEGER}, #{outStockLossOrderNum,jdbcType=INTEGER}, 
      #{outStockLossAmount,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsPerformanceDO">
    insert into pes_cs_performance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="directReceiveNum != null">
        direct_receive_num,
      </if>
      <if test="forwardInNum != null">
        forward_in_num,
      </if>
      <if test="forwardOutNum != null">
        forward_out_num,
      </if>
      <if test="consultNum != null">
        consult_num,
      </if>
      <if test="receiveNum != null">
        receive_num,
      </if>
      <if test="enquiryNum != null">
        enquiry_num,
      </if>
      <if test="orderedNumToday != null">
        ordered_num_today,
      </if>
      <if test="paidNumToday != null">
        paid_num_today,
      </if>
      <if test="orderedAmountToday != null">
        ordered_amount_today,
      </if>
      <if test="paidAmountToday != null">
        paid_amount_today,
      </if>
      <if test="orderedNumFinal != null">
        ordered_num_final,
      </if>
      <if test="orderedAmountFinal != null">
        ordered_amount_final,
      </if>
      <if test="orderedGoodsNumFinal != null">
        ordered_goods_num_final,
      </if>
      <if test="paidNumTodayNext != null">
        paid_num_today_next,
      </if>
      <if test="paidNumFinal != null">
        paid_num_final,
      </if>
      <if test="paidAmountFinal != null">
        paid_amount_final,
      </if>
      <if test="paidGoodsNumFinal != null">
        paid_goods_num_final,
      </if>
      <if test="receiveRate != null">
        receive_rate,
      </if>
      <if test="conversionRate != null">
        conversion_rate,
      </if>
      <if test="saleAmount != null">
        sale_amount,
      </if>
      <if test="saleOrderNum != null">
        sale_order_num,
      </if>
      <if test="saleGoodsNum != null">
        sale_goods_num,
      </if>
      <if test="saleBuyerNum != null">
        sale_buyer_num,
      </if>
      <if test="cfmGoodsONum != null">
        cfm_goods_o_num,
      </if>
      <if test="cfmGoodsAmount != null">
        cfm_goods_amount,
      </if>
      <if test="postFee != null">
        post_fee,
      </if>
      <if test="outStockNum != null">
        out_stock_num,
      </if>
      <if test="outStockAmount != null">
        out_stock_amount,
      </if>
      <if test="outStockGoodsNum != null">
        out_stock_goods_num,
      </if>
      <if test="outStockOrderNum != null">
        out_stock_order_num,
      </if>
      <if test="outStockLossNum != null">
        out_stock_loss_num,
      </if>
      <if test="outStockLossGoodsNum != null">
        out_stock_loss_goods_num,
      </if>
      <if test="outStockLossOrderNum != null">
        out_stock_loss_order_num,
      </if>
      <if test="outStockLossAmount != null">
        out_stock_loss_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="directReceiveNum != null">
        #{directReceiveNum,jdbcType=INTEGER},
      </if>
      <if test="forwardInNum != null">
        #{forwardInNum,jdbcType=INTEGER},
      </if>
      <if test="forwardOutNum != null">
        #{forwardOutNum,jdbcType=INTEGER},
      </if>
      <if test="consultNum != null">
        #{consultNum,jdbcType=INTEGER},
      </if>
      <if test="receiveNum != null">
        #{receiveNum,jdbcType=INTEGER},
      </if>
      <if test="enquiryNum != null">
        #{enquiryNum,jdbcType=INTEGER},
      </if>
      <if test="orderedNumToday != null">
        #{orderedNumToday,jdbcType=INTEGER},
      </if>
      <if test="paidNumToday != null">
        #{paidNumToday,jdbcType=INTEGER},
      </if>
      <if test="orderedAmountToday != null">
        #{orderedAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="paidAmountToday != null">
        #{paidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="orderedNumFinal != null">
        #{orderedNumFinal,jdbcType=INTEGER},
      </if>
      <if test="orderedAmountFinal != null">
        #{orderedAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="orderedGoodsNumFinal != null">
        #{orderedGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="paidNumTodayNext != null">
        #{paidNumTodayNext,jdbcType=INTEGER},
      </if>
      <if test="paidNumFinal != null">
        #{paidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="paidAmountFinal != null">
        #{paidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="paidGoodsNumFinal != null">
        #{paidGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="receiveRate != null">
        #{receiveRate,jdbcType=DOUBLE},
      </if>
      <if test="conversionRate != null">
        #{conversionRate,jdbcType=DOUBLE},
      </if>
      <if test="saleAmount != null">
        #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="saleOrderNum != null">
        #{saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null">
        #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleBuyerNum != null">
        #{saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsONum != null">
        #{cfmGoodsONum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsAmount != null">
        #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="postFee != null">
        #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockNum != null">
        #{outStockNum,jdbcType=INTEGER},
      </if>
      <if test="outStockAmount != null">
        #{outStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="outStockGoodsNum != null">
        #{outStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockOrderNum != null">
        #{outStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossNum != null">
        #{outStockLossNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossGoodsNum != null">
        #{outStockLossGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossOrderNum != null">
        #{outStockLossOrderNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossAmount != null">
        #{outStockLossAmount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsPerformanceDO">
    update pes_cs_performance
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="directReceiveNum != null">
        direct_receive_num = #{directReceiveNum,jdbcType=INTEGER},
      </if>
      <if test="forwardInNum != null">
        forward_in_num = #{forwardInNum,jdbcType=INTEGER},
      </if>
      <if test="forwardOutNum != null">
        forward_out_num = #{forwardOutNum,jdbcType=INTEGER},
      </if>
      <if test="consultNum != null">
        consult_num = #{consultNum,jdbcType=INTEGER},
      </if>
      <if test="receiveNum != null">
        receive_num = #{receiveNum,jdbcType=INTEGER},
      </if>
      <if test="enquiryNum != null">
        enquiry_num = #{enquiryNum,jdbcType=INTEGER},
      </if>
      <if test="orderedNumToday != null">
        ordered_num_today = #{orderedNumToday,jdbcType=INTEGER},
      </if>
      <if test="paidNumToday != null">
        paid_num_today = #{paidNumToday,jdbcType=INTEGER},
      </if>
      <if test="orderedAmountToday != null">
        ordered_amount_today = #{orderedAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="paidAmountToday != null">
        paid_amount_today = #{paidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="orderedNumFinal != null">
        ordered_num_final = #{orderedNumFinal,jdbcType=INTEGER},
      </if>
      <if test="orderedAmountFinal != null">
        ordered_amount_final = #{orderedAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="orderedGoodsNumFinal != null">
        ordered_goods_num_final = #{orderedGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="paidNumTodayNext != null">
        paid_num_today_next = #{paidNumTodayNext,jdbcType=INTEGER},
      </if>
      <if test="paidNumFinal != null">
        paid_num_final = #{paidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="paidAmountFinal != null">
        paid_amount_final = #{paidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="paidGoodsNumFinal != null">
        paid_goods_num_final = #{paidGoodsNumFinal,jdbcType=INTEGER},
      </if>
      <if test="receiveRate != null">
        receive_rate = #{receiveRate,jdbcType=DOUBLE},
      </if>
      <if test="conversionRate != null">
        conversion_rate = #{conversionRate,jdbcType=DOUBLE},
      </if>
      <if test="saleAmount != null">
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="saleOrderNum != null">
        sale_order_num = #{saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null">
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleBuyerNum != null">
        sale_buyer_num = #{saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsONum != null">
        cfm_goods_o_num = #{cfmGoodsONum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsAmount != null">
        cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="postFee != null">
        post_fee = #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockNum != null">
        out_stock_num = #{outStockNum,jdbcType=INTEGER},
      </if>
      <if test="outStockAmount != null">
        out_stock_amount = #{outStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="outStockGoodsNum != null">
        out_stock_goods_num = #{outStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockOrderNum != null">
        out_stock_order_num = #{outStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossNum != null">
        out_stock_loss_num = #{outStockLossNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossGoodsNum != null">
        out_stock_loss_goods_num = #{outStockLossGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossOrderNum != null">
        out_stock_loss_order_num = #{outStockLossOrderNum,jdbcType=INTEGER},
      </if>
      <if test="outStockLossAmount != null">
        out_stock_loss_amount = #{outStockLossAmount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsPerformanceDO">
    update pes_cs_performance
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      direct_receive_num = #{directReceiveNum,jdbcType=INTEGER},
      forward_in_num = #{forwardInNum,jdbcType=INTEGER},
      forward_out_num = #{forwardOutNum,jdbcType=INTEGER},
      consult_num = #{consultNum,jdbcType=INTEGER},
      receive_num = #{receiveNum,jdbcType=INTEGER},
      enquiry_num = #{enquiryNum,jdbcType=INTEGER},
      ordered_num_today = #{orderedNumToday,jdbcType=INTEGER},
      paid_num_today = #{paidNumToday,jdbcType=INTEGER},
      ordered_amount_today = #{orderedAmountToday,jdbcType=DOUBLE},
      paid_amount_today = #{paidAmountToday,jdbcType=DOUBLE},
      ordered_num_final = #{orderedNumFinal,jdbcType=INTEGER},
      ordered_amount_final = #{orderedAmountFinal,jdbcType=DOUBLE},
      ordered_goods_num_final = #{orderedGoodsNumFinal,jdbcType=INTEGER},
      paid_num_today_next = #{paidNumTodayNext,jdbcType=INTEGER},
      paid_num_final = #{paidNumFinal,jdbcType=INTEGER},
      paid_amount_final = #{paidAmountFinal,jdbcType=DOUBLE},
      paid_goods_num_final = #{paidGoodsNumFinal,jdbcType=INTEGER},
      receive_rate = #{receiveRate,jdbcType=DOUBLE},
      conversion_rate = #{conversionRate,jdbcType=DOUBLE},
      sale_amount = #{saleAmount,jdbcType=DOUBLE},
      sale_order_num = #{saleOrderNum,jdbcType=INTEGER},
      sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      sale_buyer_num = #{saleBuyerNum,jdbcType=INTEGER},
      cfm_goods_o_num = #{cfmGoodsONum,jdbcType=INTEGER},
      cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      post_fee = #{postFee,jdbcType=DOUBLE},
      out_stock_num = #{outStockNum,jdbcType=INTEGER},
      out_stock_amount = #{outStockAmount,jdbcType=DOUBLE},
      out_stock_goods_num = #{outStockGoodsNum,jdbcType=INTEGER},
      out_stock_order_num = #{outStockOrderNum,jdbcType=INTEGER},
      out_stock_loss_num = #{outStockLossNum,jdbcType=INTEGER},
      out_stock_loss_goods_num = #{outStockLossGoodsNum,jdbcType=INTEGER},
      out_stock_loss_order_num = #{outStockLossOrderNum,jdbcType=INTEGER},
      out_stock_loss_amount = #{outStockLossAmount,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectCsPerformanceByCsNickByDateForRealTime"  resultMap="CsPerformanceDTO">
  	SELECT  <include refid="base_field" /> from 
  	${tableName}
  	<where>
	  		<if test="csNickLst!=null and csNickLst.size()>0">
					cs_nick in
			  	<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
			  		#{csNick}
			  	</foreach>
			</if>
	AND shop_id=#{shopId}
  	AND date BETWEEN #{startDate} AND #{endDate}
  	</where>  
  	
  </select>

</mapper>