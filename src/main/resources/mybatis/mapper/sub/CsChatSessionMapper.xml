<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsChatSessionMapper">
    <resultMap id="CsChatSessionDTO" type="com.pes.jd.model.DTO.CsChatSessionDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="customer" property="customer" jdbcType="VARCHAR"/>
        <result column="begin_datetime" property="beginDatetime" jdbcType="TIMESTAMP"/>
        <result column="end_datetime" property="endDatetime" jdbcType="TIMESTAMP"/>
        <result column="session_begin_time" property="sessionBeginTime" jdbcType="TIMESTAMP"/>
        <result column="session_end_time" property="sessionEndTime" jdbcType="TIMESTAMP"/>
        <result column="reply_datetime" property="replyDatetime" jdbcType="TIMESTAMP"/>
        <result column="session_type" property="sessionType" jdbcType="BIT"/>
        <result column="is_transfer" property="isTransfer" jdbcType="BIT"/>
        <result column="is_non_reply" property="isNonReply" jdbcType="BIT"/>
        <result column="is_slow_resp" property="isSlowResp" jdbcType="BIT"/>
        <result column="is_long_receive" property="isLongReceive" jdbcType="BIT"/>
        <result column="eval_code" property="evalCode" jdbcType="INTEGER"/>
        <result column="is_consult" property="isConsult" jdbcType="BIT"/>
        <result column="is_receive" property="isReceive" jdbcType="BIT"/>
        <result column="is_assign" property="isAssign" jdbcType="BIT"/>
        <result column="session_duration_time" property="sessionDurationTime" jdbcType="DOUBLE"/>
        <result column="avg_resp_time_first" property="avgRespTimeFirst" jdbcType="DOUBLE"/>
        <result column="avg_resp_time" property="avgRespTime" jdbcType="DOUBLE"/>
        <result column="cs_chat_num" property="csChatNum" jdbcType="INTEGER"/>
        <result column="cust_chat_num" property="custChatNum" jdbcType="INTEGER"/>
        <result column="receive_start_type" property="receiveStartType" jdbcType="BIGINT"/>
        <result column="forward_type" property="forwardType" jdbcType="BIGINT"/>
        <result column="eval_num" property="evalNum" jdbcType="INTEGER"/>
        <result column="send_eval_num" property="sendEvalNum" jdbcType="INTEGER"/>
        <result column="satisfied_eval_num" property="satisfiedEvalNum" jdbcType="INTEGER"/>
        <result column="slow_resp_num" property="slowRespNum" jdbcType="INTEGER"/>
        <result column="max_slow_resp_time" property="maxSlowRespTime" jdbcType="DOUBLE"/>
        <result column="leave_msg_filter" property="leaveMsgFilter" jdbcType="BIT"/>
    </resultMap>

    <sql id="base_field">
    id, sid, shop_id, date, cs_nick, customer, session_begin_time, session_end_time,
    begin_datetime, end_datetime, reply_datetime, session_type, is_transfer, is_non_reply,
    is_slow_resp, is_long_receive, send_eval_num, eval_num, eval_code, satisfied_eval_num,
    is_consult, is_receive, is_assign, session_duration_time, session_receive_duration_time,
    avg_resp_time_first, avg_resp_time, cs_chat_num, cust_chat_num, receive_start_type,
    forward_type
  </sql>
    <!--查询聊天关系  forward_type 是2 的话就是转出-->
    <select id="selectChatSessionLst" resultType="com.pes.jd.model.DTO.CsChatSessionDTO">
        SELECT
        sid, shop_id, `date`, begin_datetime, end_datetime, customer, cs_nick, session_duration_time,
        session_begin_time,session_end_time,slow_resp_num,max_slow_resp_time,avg_resp_time,avg_resp_time
        FROM ${tableName} s
        WHERE shop_id = #{shopId}
        <if test="customer != null and customer != ''">
            AND customer = #{customer}
        </if>
        <choose>
            <when test='type == "1"'>
                AND is_non_reply = 1
                AND is_receive = 1
            </when>
            <when test='type == "2"'>
                AND is_slow_resp = 1
                AND is_receive = 1
                AND leave_msg_filter = 0
            </when>
            <when test='type == "3"'>
                AND is_long_receive = 1
                AND is_receive = 1
            </when>
            <otherwise>

            </otherwise>
        </choose>

        <if test="csNickList != null">
            AND cs_nick IN
            <foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
                #{nick}
            </foreach>
        </if>
        <if test="startDate != null">
            AND date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND date &lt;= #{endDate}
        </if>
        ORDER BY date ASC, begin_datetime ASC
    </select>

    <select id="selectChatSessionForReceive" resultMap="CsChatSessionDTO">
        select
        sid,shop_id, date,
        cs_nick, customer,
        session_begin_time,session_end_time,
        begin_datetime, end_datetime,
        eval_num,send_eval_num,satisfied_eval_num,eval_code,
        session_type,session_duration_time, avg_resp_time_first,
        avg_resp_time, cs_chat_num, cust_chat_num,
        receive_start_type,forward_type,is_assign, leave_msg_filter
        from
        (
        <foreach collection="tableNames" item="itm" separator="union all">
            select
            sid,shop_id, date,
            cs_nick, customer,
            session_begin_time,session_end_time,
            begin_datetime, end_datetime,
            eval_num,send_eval_num,satisfied_eval_num,eval_code,
            session_type,session_duration_time, avg_resp_time_first,
            avg_resp_time, cs_chat_num, cust_chat_num,
            receive_start_type,forward_type,is_assign, leave_msg_filter
            FROM ${itm.tableName}
            <where>
                shop_id=#{shopId}
                and is_receive=1
                <if test="csNickLst!=null and csNickLst.size()>0">
                    AND cs_nick in
                    <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                        #{csNick}
                    </foreach>
                </if>
                <if test="param.buyerNick != null and param.buyerNick != ''">
                    AND customer = #{param.buyerNick}
                </if>
                <if test="param.consultType!=null and param.consultType!=''">
                    AND session_type=#{param.consultType}
                </if>
                <if test="param.receiveType!=null and param.receiveType!=''">
                    AND forward_type=#{param.receiveType}
                </if>
                    AND leave_msg_filter !=1
                AND session_begin_time BETWEEN #{itm.beginDate} AND #{itm.endDate}
            </where>
        </foreach>
        ) cs

        <if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field !=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
        </if>
        <if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>




    <select id="selectChatSessionForReciveFilter" resultMap="CsChatSessionDTO">
        select
        sid,shop_id, date,
        cs_nick, customer,
        session_begin_time,session_end_time,
        begin_datetime, end_datetime,
        eval_num,send_eval_num,satisfied_eval_num,eval_code,
        session_type,session_duration_time, avg_resp_time_first,
        avg_resp_time, cs_chat_num, cust_chat_num,
        receive_start_type,forward_type,is_assign, leave_msg_filter
        from
        (
        <foreach collection="tableNames" item="itm" separator="union all">
            select
            sid,shop_id, date,
            cs_nick, customer,
            session_begin_time,session_end_time,
            begin_datetime, end_datetime,
            eval_num,send_eval_num,satisfied_eval_num,eval_code,
            session_type,session_duration_time, avg_resp_time_first,
            avg_resp_time, cs_chat_num, cust_chat_num,
            receive_start_type,forward_type,is_assign, leave_msg_filter
            FROM ${itm.tableName}
            <where>
                shop_id=#{shopId}
                and is_receive=0
                <if test="csNickList!=null and csNickList.size()>0">
                    AND cs_nick in
                    <foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
                        #{csNick}
                    </foreach>
                </if>
                <if test="param.buyerNick != null and param.buyerNick != ''">
                    AND customer = #{param.buyerNick}
                </if>

                AND session_begin_time BETWEEN #{itm.beginDate} AND #{itm.endDate}
            </where>
        </foreach>
        ) cs
    </select>





    <select id="selectChatSessionCountForReceive" resultType="java.lang.Integer">
        SELECT COUNT(cs.id) FROM
        (
        <foreach collection="tableNames" item="itm" separator="union all">
            select id from ${itm.tableName}
            <where>
                shop_id=#{shopId}
                and is_receive=1
                <if test="csNickLst!=null and csNickLst.size()>0">
                    AND cs_nick in
                    <foreach collection="param.csNickLst" item="csNick" open="(" close=")"
                             separator=",">
                        #{csNick}
                    </foreach>
                </if>
                <if test="param.buyerNick != null and param.buyerNick != ''">
                    AND customer = #{param.buyerNick}
                </if>
                <if test="param.consultType!=null and param.consultType!=''">
                    AND session_type=#{param.consultType}
                </if>
                <if test="param.receiveType!=null and param.receiveType!=''">
                    AND forward_type=#{param.receiveType}
                </if>
                AND leave_msg_filter !=1
                AND session_begin_time BETWEEN #{itm.beginDate} AND #{itm.endDate}
            </where>
        </foreach>
        ) cs
    </select>


    <select id="selectByShopIdAndDateAndCsNicksAndCustomerForLeaveMessage" resultMap="CsChatSessionDTO">
        SELECT
        sid, shop_id, `date`, cs_nick, customer, begin_datetime, end_datetime, session_begin_time,session_end_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <if test="csNickList!=null and csNickList.size() > 0">
            AND cs_nick IN
            <foreach collection="csNickList" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
        </if>
        AND `date` BETWEEN #{startDate} AND #{endDate}
        <if test="customer != null and customer != ''">
            AND customer = #{customer}
        </if>
        AND session_type= 2
        AND is_receive = 1
        ORDER BY date ASC, session_begin_time ASC
    </select>

    <select id="selectReceiveCsChatSessionLstByDate" resultType="java.lang.String">
        SELECT
        cs.sid
        FROM

        (
        <foreach collection="tableNames" item="itm" separator="union all">
            select
            sid
            FROM ${itm.tableName}
            WHERE
                shop_id=#{shopId}
                and forward_type=2
                AND session_begin_time BETWEEN #{itm.beginDate} AND #{itm.endDate}
        </foreach>
        ) cs
    </select>


</mapper>