<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.LossRecordMapper">
    <resultMap id="LossRecordDTO" type="com.pes.jd.model.DTO.LossRecordDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="customer" property="customer" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="TIMESTAMP"/>
        <result column="chat_num" property="chatNum" jdbcType="INTEGER"/>
        <result column="chat_type" property="chatType" jdbcType="INTEGER"/>
        <result column="start_datetime" property="startDateTime" jdbcType="TIMESTAMP"/>
        <result column="end_datetime" property="endDateTime" jdbcType="TIMESTAMP"/>
        <result column="consume_time" property="consumeTime" jdbcType="BIGINT"/>
        <result column="sku_ids" property="skuIds" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectEnquiryLossRecordLstByDateByType" parameterType="map" resultMap="LossRecordDTO">
        SELECT shop_id,cs_nick,customer,date,start_datetime,end_datetime,chat_num,consume_time,sku_ids
        FROM ${tableName}
        WHERE date BETWEEN #{startDate} AND #{endDate}
            AND shop_id = #{shopId}
        <if test="csNickLst != null">
            AND cs_nick IN
            <foreach collection="csNickLst" item="csNickInfo" open="(" close=")" separator=",">
                #{csNickInfo.nick}
            </foreach>
        </if>
        <if test="buyerNick != '' and buyerNick != null">
            AND customer = #{buyerNick}
        </if>


        <choose>
            <when test="chatLimitNum == 4">
                AND (chat_num >= 1 AND chat_num &lt;= 4)
            </when>
            <when test="chatLimitNum == 8">
                AND (chat_num >= 5 AND chat_num &lt;= 8)
            </when>
            <when test="chatLimitNum == 9">
                AND chat_num >8
            </when>
            <otherwise>
                AND chat_num > #{chatLimitNum}
            </otherwise>
        </choose>
        <choose>
            <when test="sessionDuration == 1">
                AND (consume_time &lt;= 60)
            </when>
            <when test="sessionDuration == 2">
                AND (consume_time >= 60 AND consume_time &lt;= 120)
            </when>
            <when test="sessionDuration == 4">
                AND (consume_time >= 120 AND consume_time &lt;= 240)
            </when>
            <when test="sessionDuration == 6">
                AND (consume_time >= 240 AND consume_time &lt;= 360)
            </when>
            <when test="sessionDuration == 10">
                AND (consume_time >= 360 AND consume_time &lt;= 600)
            </when>
            <when test="sessionDuration == 11">
                AND (consume_time > 600)
            </when>
            <otherwise>
                AND consume_time > #{sessionDuration}
            </otherwise>
        </choose>
        <if test="skuIds!=null and skuIds!=''">
            AND
            <foreach item="item" index="index" collection="skuIds.split(',')" open="(" separator="or" close=")">
                sku_ids LIKE CONCAT('%',#{item},'%')
            </foreach>
        </if>
        ORDER BY
        <choose>
            <when test="sortPageQuery.field != null">
                ${sortPageQuery.field}
                <if test="sortPageQuery.sortDirection != null">
                    ${sortPageQuery.sortDirection}
                </if>
            </when>
            <otherwise>
                date
                <if test="sortPageQuery.sortDirection != null">
                    ${sortPageQuery.sortDirection}
                </if>
                , start_datetime
                <if test="sortPageQuery.sortDirection != null">
                    ${sortPageQuery.sortDirection}
                </if>
            </otherwise>
        </choose>
        <if test="startIndex != null and endIndex !=null">
            Limit #{startIndex},#{endIndex}
        </if>
    </select>

    <select id="selectEnquiryLossRecordCountByDateByTypeForPage" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(id)
        FROM ${tableName}
        WHERE date BETWEEN #{startDate} AND #{endDate}
        AND shop_id = #{shopId}
        <if test="csNickLst != null">
            AND cs_nick IN
            <foreach collection="csNickLst" item="csNickInfo" open="(" close=")" separator=",">
                #{csNickInfo.nick}
            </foreach>
        </if>
        <if test="buyerNick != '' and buyerNick != null">
            AND customer = #{buyerNick}
        </if>
        <choose>
            <when test="chatLimitNum == 4">
                AND (chat_num >= 1 AND chat_num &lt;= 4)
            </when>
            <when test="chatLimitNum == 8">
                AND (chat_num >= 5 AND chat_num &lt;= 8)
            </when>
            <when test="chatLimitNum == 9">
                AND chat_num >8
            </when>
            <otherwise>
                AND chat_num > #{chatLimitNum}
            </otherwise>
        </choose>
        <choose>
            <when test="sessionDuration == 1">
                AND (consume_time &lt;= 60)
            </when>
            <when test="sessionDuration == 2">
                AND (consume_time >= 60 AND consume_time &lt;= 120)
            </when>
            <when test="sessionDuration == 4">
                AND (consume_time >= 120 AND consume_time &lt;= 240)
            </when>
            <when test="sessionDuration == 6">
                AND (consume_time >= 240 AND consume_time &lt;= 360)
            </when>
            <when test="sessionDuration == 10">
                AND (consume_time >= 360 AND consume_time &lt;= 600)
            </when>
            <when test="sessionDuration == 11">
                AND (consume_time > 600)
            </when>
            <otherwise>
                AND consume_time > #{sessionDuration}
            </otherwise>
        </choose>
        <if test="skuIds!=null and skuIds!=''">
            AND
            <foreach item="item" index="index" collection="skuIds.split(',')" open="(" separator="or" close=")">
             sku_ids LIKE CONCAT('%',#{item},'%')
            </foreach>
        </if>
    </select>
</mapper>