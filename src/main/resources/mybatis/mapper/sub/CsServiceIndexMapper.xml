<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsServiceIndexMapper">

  <resultMap id="CsServiceIndexDTO" type="com.pes.jd.model.DTO.CsServiceIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="avg_wait_time_first" jdbcType="DOUBLE" property="avgWaitTimeFirst" />
    <result column="avg_wait_time" jdbcType="DOUBLE" property="avgWaitTime" />
    <result column="session_time" jdbcType="BIGINT" property="sessionTime" />
    <result column="cs_reply_num" jdbcType="INTEGER" property="csReplyNum" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
    <result column="is_non_reply" jdbcType="BIT" property="isNonReply" />
  </resultMap>
  
  <sql id="base_field">
    shop_id, date, cs_nick, buyer_nick, avg_wait_time_first, avg_wait_time, session_time, 
    cs_reply_num, buyer_chat_num, is_non_reply
  </sql>
  
  <insert id="batchInsertCsServiceIndex" parameterType="map">
    INSERT INTO ${tableName} (shop_id, date, 
      cs_nick, buyer_nick, avg_wait_time_first, 
      avg_wait_time, session_time, cs_reply_num, 
      buyer_chat_num, is_non_reply)
    VALUES
    <foreach collection="csServiceIndexLst" item="itm" separator=",">
     (
    	#{itm.shopId,jdbcType=BIGINT}, 
    	#{itm.date,jdbcType=DATE}, 
      	#{itm.csNick,jdbcType=VARCHAR}, 
      	#{itm.buyerNick,jdbcType=VARCHAR}, 
      	#{itm.avgWaitTimeFirst,jdbcType=DOUBLE}, 
      	#{itm.avgWaitTime,jdbcType=DOUBLE}, 
      	#{itm.sessionTime,jdbcType=BIGINT}, 
      	#{itm.csReplyNum,jdbcType=INTEGER}, 
      	#{itm.buyerChatNum,jdbcType=INTEGER}, 
      	#{itm.isNonReply,jdbcType=BIT}
      )
    </foreach> 
    
  </insert>
  
  <delete id="deleteShopCsServiceIndexByDate" parameterType="map">
    DELETE FROM 
    	${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>

  <update id="updateByPrimaryKeySelective" parameterType="map">
    UPDATE ${tableName}
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="buyerNick != null">
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="avgWaitTimeFirst != null">
        avg_wait_time_first = #{avgWaitTimeFirst,jdbcType=DOUBLE},
      </if>
      <if test="avgWaitTime != null">
        avg_wait_time = #{avgWaitTime,jdbcType=DOUBLE},
      </if>
      <if test="sessionTime != null">
        session_time = #{sessionTime,jdbcType=BIGINT},
      </if>
      <if test="csReplyNum != null">
        cs_reply_num = #{csReplyNum,jdbcType=INTEGER},
      </if>
      <if test="buyerChatNum != null">
        buyer_chat_num = #{buyerChatNum,jdbcType=INTEGER},
      </if>
      <if test="isNonReply != null">
        is_non_reply = #{isNonReply,jdbcType=BIT},
      </if>
    </set>
    WHERE 
    	 buyer_nick = #{buyerNick,jdbcType=VARCHAR}
    AND cs_nick = #{csNick,jdbcType=VARCHAR}
    AND shop_id = #{shopId,jdbcType=BIGINT}
  </update>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="CsServiceIndexDTO">
    SELECT 
    	<include refid="base_field" />
    FROM pes_cs_service_index_774906
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByShopNickDateList" parameterType="map" resultType="map">
    SELECT

    FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND cs_nick = #{nick}
    AND date BETWEEN #{startDate} AND #{endDate}
    AND non_reply_num > 0
  </select>

  <select id="selectByShopNickDate" parameterType="map" resultType="com.pes.jd.model.DTO.NickPerformanceDTO">
    SELECT
    shop_id shopId, date, cs_nick nick,

    avg(avg_wait_time_first) avgResponseTime, avg(avg_wait_time) avgWaitTime,
    sum(cs_reply_num) csChatNum, sum(buyer_chat_num) buyerChatNum, sum(non_reply_num) noReplyNum,
    sum(cs_word_num) wordsNum,avg(qa_rate) qaRate
    FROM ${tableName}
    <where>
      AND shop_id IN
      <foreach collection="nicks" item="item" separator="," open="(" close=")">
        #{item.shopId}
      </foreach>
      AND date BETWEEN #{startDate} AND #{endDate}
      AND cs_nick IN
      <foreach collection="nicks" item="item" separator="," open="(" close=")">
        #{item.nick}
      </foreach>
      GROUP BY ${groupBy}
    </where>
  </select>

<select id="selectCsServiceIndexByCsNickByDateForRealTime" resultMap="CsServiceIndexDTO">
	SELECT <include refid="base_field"></include>
	FROM ${tableName}
	WHERE cs_nick =#{csNick}
	AND date BETWEEN #{startDate} AND #{endDate}
</select>
</mapper>