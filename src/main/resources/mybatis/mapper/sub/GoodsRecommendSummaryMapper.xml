<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.GoodsRecommendSummaryMapper" >
  <resultMap id="GoodsRecommendSummaryDO" type="com.pes.jd.model.DO.GoodsRecommendSummaryDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="recommend_num" property="recommendNum" jdbcType="INTEGER" />
    <result column="purchases_buyer_num" property="purchasesBuyerNum" jdbcType="INTEGER" />
    <result column="purchases_goods_num" property="purchasesGoodsNum" jdbcType="INTEGER" />
    <result column="purchases_amount" property="purchasesAmount" jdbcType="DOUBLE" />
  </resultMap>

  <resultMap id="GoodsRecommendSummaryDTO" type="com.pes.jd.model.DTO.GoodsRecommendSummaryDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
    <result column="recommend_num" property="recommendNum" jdbcType="INTEGER" />
    <result column="purchases_buyer_num" property="purchasesBuyerNum" jdbcType="INTEGER" />
    <result column="purchases_goods_num" property="purchasesGoodsNum" jdbcType="INTEGER" />
    <result column="purchases_amount" property="purchasesAmount" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="base_field" >
      id
      , shop_id, date, sku_id, recommend_num, purchases_buyer_num, purchases_goods_num,
    purchases_amount
  </sql>

 <insert id="insertGoodsRecommendSummary" parameterType="com.pes.jd.model.DO.GoodsRecommendSummaryDO" >
     insert into pes_goods_recommend_summary (id, shop_id, date,
                                              sku_id, recommend_num, purchases_buyer_num,
      purchases_goods_num, purchases_amount)
     values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE},
             #{skuId,jdbcType=BIGINT}, #{recommendNum,jdbcType=INTEGER}, #{purchasesBuyerNum,jdbcType=INTEGER},
      #{purchasesGoodsNum,jdbcType=INTEGER}, #{purchasesAmount,jdbcType=DOUBLE})
  </insert>

  <delete id="deleteGoodsRecommendSummaryById" parameterType="java.lang.Long" >
    delete from pes_goods_recommend_summary
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="updateGoodsRecommendSummary" parameterType="com.pes.jd.model.DO.GoodsRecommendSummaryDO">
    update pes_goods_recommend_summary
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="recommendNum != null" >
        recommend_num = #{recommendNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesBuyerNum != null" >
        purchases_buyer_num = #{purchasesBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesGoodsNum != null" >
        purchases_goods_num = #{purchasesGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesAmount != null" >
        purchases_amount = #{purchasesAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectGoodsRecommendSummaryById" resultMap="GoodsRecommendSummaryDO" parameterType="java.lang.Long">
        select
    <include refid="base_field" />
    from pes_goods_recommend_summary
    where id = #{id,jdbcType=BIGINT}
  </select>

    <select id="selectGoodsRecommendSummaryCountByDateBySkuIdByCsNick" resultMap="GoodsRecommendSummaryDTO"
            parameterType="map">
        SELECT
        cs_nick,
	  	sum(recommend_num) recommend_num,
	  	sum(purchases_buyer_num) purchases_buyer_num,
	  	sum(purchases_goods_num) purchases_goods_num,
	  	sum(purchases_amount) purchases_amount
	FROM ${tableName}
  	<where>
  		shop_id=#{shopId}
	  	<if test="csNickLst!=null and csNickLst.size>0">
	  		AND	cs_nick in
	  		<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
	  			#{csNick}
	  		</foreach>
	  	</if>
	  	<if test="skuLst!=null and skuLst.size>0">
	  		 AND sku_id in
		  	<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
		  			#{skuId}
		  	</foreach>
	  	</if>
	  	AND date between #{startDate} and #{endDate}
	  	</where>
	  	GROUP  BY cs_nick
  </select>


    <select id="selectGoodsRecommendSummaryCountByDateBySkuIdByCsNickV2" resultMap="GoodsRecommendSummaryDTO"
            parameterType="map">
        SELECT
        recommend.cs_nick,
        sum(recommend.recommend_num) recommend_num,
        sum(recommend.purchases_buyer_num) purchases_buyer_num,
        sum(recommend.purchases_goods_num) purchases_goods_num,
        sum(recommend.purchases_amount) purchases_amount
        FROM ${tableName} recommend
        INNER JOIN ${goodsSkuTable} gs ON recommend.shop_id = gs.shop_id AND recommend.sku_id = gs.sku_id
        <where>
            recommend.shop_id=#{shopId}
            <if test="csNickLst!=null and csNickLst.size>0">
                AND recommend.cs_nick in
                <foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
                    #{csNick}
                </foreach>
            </if>
            <if test="categoryId!=null and categoryId.size>0">
                AND gs.category_id in
                <foreach collection="categoryId" item="catId" separator="," open="(" close=")">
                    #{catId}
                </foreach>
            </if>
            <if test="skuLst!=null and skuLst.size>0">
                AND recommend.sku_id in
                <foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            AND recommend.date between #{startDate} and #{endDate}
        </where>
        GROUP BY recommend.cs_nick
    </select>

    <select id="selectGoodsRecommendSummaryByDateByShopIdByCsNick" resultMap="GoodsRecommendSummaryDTO" parameterType="map">
        select
        summary.shop_id,
        summary.cs_nick,
        summary.date,
        summary.sku_id,
        summary.recommend_num,
        summary.purchases_buyer_num,
        summary.purchases_goods_num,
        summary.purchases_amount
        FROM
        (
        <foreach collection="tableNames" item="table" separator="union all">
            SELECT
            shop_id,
            cs_nick,
            date,
            sku_id,
            recommend_num,
            purchases_buyer_num,
            purchases_goods_num,
            purchases_amount
            FROM  ${table.tableName}
            WHERE
            shop_id=#{shopId}
            AND cs_nick in
            <foreach collection="param.csNickLst" item="csNick" open="(" close=")" separator=",">
                #{csNick}
            </foreach>
            <if test="param.skuLst!=null and param.skuLst.size()>0">
                and sku_id in
                <foreach collection="param.skuLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            AND date between #{table.beginDate} and #{table.endDate}
        </foreach>
        )
        summary

    </select>
</mapper>
