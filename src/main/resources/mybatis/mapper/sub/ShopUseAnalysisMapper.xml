<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopUseAnalysisMapper" >
  <resultMap id="ShopUseAnalysisDO" type="com.pes.jd.model.DO.ShopUseAnalysisDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="screen_valid_subscribe_num" property="screenValidSubscribeNum" jdbcType="INTEGER" />
    <result column="login_num" property="loginNum" jdbcType="INTEGER" />
    <result column="login_use_num" property="loginUseNum" jdbcType="INTEGER" />
    <result column="one_step_user_num" property="oneStepUserNum" jdbcType="INTEGER" />
    <result column="one_step_execute_num" property="oneStepExecuteNum" jdbcType="INTEGER" />
    <result column="one_step_allocated_num" property="oneStepAllocatedNum" jdbcType="INTEGER" />
    <result column="one_step_urge_amount" property="oneStepUrgeAmount" jdbcType="DOUBLE" />
    <result column="batch_remind_user_num" property="batchRemindUserNum" jdbcType="INTEGER" />
    <result column="batch_remind_execute_num" property="batchRemindExecuteNum" jdbcType="INTEGER" />
    <result column="batch_remind_allocated_num" property="batchRemindAllocatedNum" jdbcType="INTEGER" />
    <result column="batch_remind_urge_amount" property="batchRemindUrgeAmount" jdbcType="DOUBLE" />
    <result column="shop_sale_amount" property="shopSaleAmount" jdbcType="DOUBLE" />
  </resultMap>
  <resultMap id="ShopUseConversion" type="com.pes.jd.ms.domain.Data.shopdata.ShopUseAnalysis" >
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="screen_valid_subscribe_num" property="screenValidSubscribeNum" jdbcType="INTEGER" />
    <result column="login_num" property="loginNum" jdbcType="INTEGER" />
    <result column="login_use_num" property="loginUseNum" jdbcType="INTEGER" />
    <result column="one_step_user_num" property="oneStepUserNum" jdbcType="INTEGER" />
    <result column="one_step_execute_num" property="oneStepExecuteNum" jdbcType="INTEGER" />
    <result column="one_step_allocated_num" property="oneStepAllocatedNum" jdbcType="INTEGER" />
    <result column="one_step_urge_amount" property="oneStepUrgeAmount" jdbcType="DOUBLE" />
    <result column="batch_remind_user_num" property="batchRemindUserNum" jdbcType="INTEGER" />
    <result column="batch_remind_execute_num" property="batchRemindExecuteNum" jdbcType="INTEGER" />
    <result column="batch_remind_allocated_num" property="batchRemindAllocatedNum" jdbcType="INTEGER" />
    <result column="batch_remind_urge_amount" property="batchRemindUrgeAmount" jdbcType="DOUBLE" />
    <result column="shop_sale_amount" property="shopSaleAmount" jdbcType="DOUBLE" />
    <result column="sms_use_num" property="smsUseNum" jdbcType="INTEGER" />
    <result column="sms_send_success_num" property="smsSendSuccessNum" jdbcType="INTEGER" />
    <result column="sms_send_valid_num" property="smsSendValidNum" jdbcType="INTEGER" />
    <result column="sms_urge_amount" property="smsUrgeAmount" jdbcType="DOUBLE" />
    <result column="batch_urge_order_amount" property="batchUrgeOrderAmount" jdbcType="DOUBLE" />
    <result column="auto_urge_amount" property="auotUrgeAmount" jdbcType="DOUBLE" />
    <result column="all_allocated_num" property="allAllocateNum" jdbcType="INTEGER" />
    <result column="all_execute_num" property="allExecutedNum" jdbcType="INTEGER" />

    <result column="reserve_batch_remind_execute_num" property="reserveBatchRemindExecuteNum" jdbcType="INTEGER" />
    <result column="reserve_batch_remind_user_num" property="reserveBatchRemindUserNum" jdbcType="INTEGER" />
    <result column="reserve_batch_remind_allocated_num" property="reserveBatchRemindAllocatedNum" jdbcType="INTEGER" />
    <result column="reserve_batch_remind_urge_amount" property="reserveBatchRemindUrgeAmount" jdbcType="DOUBLE" />
    <result column="presale_batch_remind_execute_num" property="presaleBatchRemindExecuteNum" jdbcType="INTEGER" />
    <result column="presale_batch_remind_user_num" property="presaleBatchRemindUserNum" jdbcType="INTEGER" />
    <result column="presale_batch_remind_allocated_num" property="presaleBatchRemindAllocatedNum" jdbcType="INTEGER" />
    <result column="presale_batch_remind_urge_amount" property="presaleBatchRemindUrgeAmount" jdbcType="DOUBLE" />

    <result column="reserve_batch_urge_order_amount" property="reserveBatchUrgeOrderAmount" jdbcType="DOUBLE" />
    <result column="presale_batch_urge_order_amount" property="presaleBatchUrgeOrderAmount" jdbcType="DOUBLE" />
    <result column="reserve_auto_urge_amount" property="reserveAuotUrgeAmount" jdbcType="DOUBLE" />
    <result column="presale_auto_urge_amount" property="presaleAuotUrgeAmount" jdbcType="DOUBLE" />
    <result column="reserve_shop_sale_amount" property="reserveShopSaleAmount" jdbcType="DOUBLE" />
    <result column="presale_shop_sale_amount" property="presaleShopSaleAmount" jdbcType="DOUBLE" />
    <result column="reserve_all_allocated_num" property="reserveAllAllocateNum" jdbcType="DOUBLE" />
    <result column="presale_all_allocated_num" property="presaleAllAllocateNum" jdbcType="DOUBLE" />
    <result column="reserve_all_execute_num" property="reserveAllExecutedNum" jdbcType="INTEGER" />
    <result column="presale_all_execute_num" property="presalAllExecutedNum" jdbcType="INTEGER" />
    <result column="sum_urge_amount" property="sumUrgeAmount" jdbcType="DOUBLE" />
    <result column="reserve_sum_urge_amount" property="reserveSumUrgeAmount" jdbcType="DOUBLE" />
    <result column="presale_sum_urge_amount" property="presaleSumUrgeAmount" jdbcType="DOUBLE" />
    <result column="reserve_one_step_urge_amount" property="reserveOneStepUrgeAmount" jdbcType="DOUBLE" />
    <result column="presale_one_step_urge_amount" property="presaleOneStepUrgeAmount" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="base_filed" >
    id, shop_id, date, screen_valid_subscribe_num, login_num, login_use_num, one_step_user_num, 
    one_step_execute_num, one_step_allocated_num, one_step_urge_amount, batch_remind_user_num,
    batch_remind_execute_num, batch_remind_allocated_num, batch_remind_urge_amount, shop_sale_amount,
    sms_use_num,sms_send_success_num,sms_send_valid_num,sms_urge_amount,batch_urge_order_amount,auto_urge_amount,
    all_allocated_num,all_execute_num,
    reserve_batch_remind_execute_num,reserve_batch_remind_user_num,reserve_batch_remind_allocated_num,reserve_batch_remind_urge_amount,
    presale_batch_remind_execute_num,presale_batch_remind_user_num,presale_batch_remind_allocated_num,presale_batch_remind_urge_amount,
    reserve_batch_urge_order_amount,presale_batch_urge_order_amount,reserve_auto_urge_amount,presale_auto_urge_amount,reserve_shop_sale_amount,
    presale_shop_sale_amount,reserve_all_allocated_num,presale_all_allocated_num,reserve_all_execute_num,presale_all_execute_num,
    sum_urge_amount,reserve_sum_urge_amount,presale_sum_urge_amount,reserve_one_step_urge_amount,presale_one_step_urge_amount


  </sql>
  <select id="selectShopUseAnalysisByShopIdSetByDate" resultMap="ShopUseConversion"  >
    select 
    <include refid="base_filed" />
    from ${tableName}
    <where>
      shop_id in
      <foreach collection="shopIdSet" open="(" close=")" separator="," item="shopId">
        #{shopId}
      </foreach>
      and date between #{startDate} and #{endDate}
    </where>
  </select>
  <select id="selectProblemShopIdLst" resultType="java.lang.Long">
      select
      shop_id , sum(sum_urge_amount), sum(one_step_user_num), sum(batch_remind_user_num)
      from ${tableName}
      where
      date between #{startDate} and #{endDate}
      group by shop_id
      having sum(sum_urge_amount) = 0
      and
      (
        sum(one_step_user_num) > 0
        or
        sum(batch_remind_user_num) > 0
      )
    </select>


</mapper>