<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderSkuEvaluateMapper" >
  <resultMap id="OrderSkuEvaluateDTO" type="com.pes.jd.model.DTO.OrderSkuEvaluateDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="score" property="score" jdbcType="INTEGER" />
    <result column="created" property="created" jdbcType="DATE" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="is_reply" property="isReply" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="order_pay_time" property="orderPayTime" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <resultMap id="CsOrderIndexDTO" type="com.pes.jd.model.DTO.CsOrderIndexDTO" >
     <result column="date" jdbcType="DATE" property="date" />
     <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
     <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
     <result column="order_id" jdbcType="BIGINT" property="orderId" />
     <result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate" />
     <result column="order_payment" jdbcType="DOUBLE" property="orderPayment" />
	 <result column="assist_type" jdbcType="INTEGER" property="assistType" />
	 <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
  </resultMap>
  
  <select id ="selectOrderSkuEvaluateByDateByCsNickByScore" resultMap = "OrderSkuEvaluateDTO">
  	select 
  		evaluate.id,
  		evaluate.order_id,
  		evaluate.buyer_nick,
	   	evaluate.cs_nick,
	   	evaluate.order_created as created,
	    evaluate.score,
	   	evaluate.content,
	   	evaluate.sku_id,
	   	evaluate.shop_id,
	   	evaluate.send_time,
	   	evaluate.order_pay_time,
	   goods.sku_name
  	from
  	(<foreach collection="orderSkuEvalTables" item = "table" separator="union">
  		select id,order_id,buyer_nick,cs_nick,order_created,shop_id,send_time,order_pay_time,
  		(case 
	   		when score = 1 then '差评'
	   		else '中评' end) score,content,sku_id
	   	from ${table.getTableName}
	   	<where>
	   		<if test="shopId!=null">
		    	shop_id = #{shopId}
		    </if>
		    <if test="param.csNickList!=null and param.csNickList.size>0">
		    	and (cs_nick in
		    	<foreach collection="param.csNickList" item="nick" open="(" close=")" separator=",">
		    		#{nick}
		    	</foreach>
		    	<if test="param.type!=null and param.type==4">
			    	or cs_nick is null)
			    </if>
			    <if test="param.type==null">
			    	)
			    </if>
		    </if>
			<if test="param.customer != null and '' != param.customer">
				AND buyer_nick like CONCAT('%',#{param.customer},'%')
			</if>
			<if test="param.evaluateKeyword != null and '' != param.evaluateKeyword">
				AND content like CONCAT('%',#{param.evaluateKeyword},'%')
			</if>
		    and score not in (4,5)
		  	and send_time between #{param.startDate} and #{param.endDate}
	   	</where>
  	</foreach>
  	) evaluate left join ${skuGoodsTables} goods on evaluate.sku_id = goods.sku_id
	<where>
	  	<if test="param.evaluateType==1">
			and evaluate.score = '中评'
		</if>
		<if test="param.evaluateType==2">
			and evaluate.score = '差评'
		</if>
		<if test="param.skuIdLst != null and param.skuIdLst.size() > 0">
			AND goods.sku_id IN
			<foreach collection="param.skuIdLst" item="skuId" open="(" close=")" separator=",">
				#{skuId}
			</foreach>
		</if>
	</where>
	order by evaluate.send_time
  </select>

	<select id="selectEvaluateInfoByOrderIdByBuyer" resultMap="OrderSkuEvaluateDTO">
  	SELECT
  		se.id,
  		se.order_id,
  		se.buyer_nick,
	    se.cs_nick,
	    se.send_time,
	    se.content,
	    se.sku_id,
	    goods.sku_name,
	    (case when se.score = 1 then '差评'
	   		else '中评' end) score
  	FROM ${orderSkuEvalTable} se
  	LEFT JOIN ${skuGoodsTable} goods on se.sku_id = goods.sku_id
  	WHERE se.id = #{id} and se.shop_id = #{shopId} AND se.order_id = #{orderId}
	GROUP BY se.id
  </select>
  
  <select id="selectCsReceptionByBuyerByOrderId" resultMap="CsOrderIndexDTO">
  	SELECT 
  		cs_nick,shop_id,buyer_nick,date,
  		order_pay_date,order_created,
  		first_chat_date
  		
  	FROM ${tableName}
  	<where>
  		shop_id = #{shopId}
  		and order_id = #{orderId}
  		and order_created between #{startDate} and #{endDate}
  	</where>
  </select>
  
</mapper>