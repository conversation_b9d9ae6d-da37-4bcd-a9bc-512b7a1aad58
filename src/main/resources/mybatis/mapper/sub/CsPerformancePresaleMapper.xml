<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CsPerformancePresaleMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsPerformancePresaleDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="consult_buyer_num" jdbcType="INTEGER" property="consultBuyerNum"/>
        <result column="enquiry_buyer_num" jdbcType="INTEGER" property="enquiryBuyerNum"/>
        <result column="enquiry_ordered_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBuyerNum"/>
        <result column="enquiry_ordered_sku_num" jdbcType="INTEGER" property="enquiryOrderedSkuNum"/>
        <result column="enquiry_ordered_bargain_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBargainBuyerNum"/>
        <result column="enquiry_ordered_bargain_sku_num" jdbcType="INTEGER" property="enquiryOrderedBargainSkuNum"/>
        <result column="enquiry_ordered_bargain_amount" jdbcType="DOUBLE" property="enquiryOrderedBargainAmount"/>
        <result column="enquiry_ordered_balance_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBalanceBuyerNum"/>
        <result column="enquiry_ordered_balance_sku_num" jdbcType="INTEGER" property="enquiryOrderedBalanceSkuNum"/>
        <result column="enquiry_ordered_balance_amount" jdbcType="DOUBLE" property="enquiryOrderedBalanceAmount"/>
        <result column="to_ordered_bargain_buyer_num" jdbcType="INTEGER" property="toOrderedBargainBuyerNum"/>
        <result column="to_ordered_balance_buyer_num" jdbcType="INTEGER" property="toOrderedBalanceBuyerNum"/>
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum"/>
        <result column="ordered_sku_num" jdbcType="INTEGER" property="orderedSkuNum"/>
        <result column="ordered_bargain_buyer_num" jdbcType="INTEGER" property="orderedBargainBuyerNum"/>
        <result column="ordered_bargain_sku_num" jdbcType="INTEGER" property="orderedBargainSkuNum"/>
        <result column="ordered_bargain_amount" jdbcType="DOUBLE" property="orderedBargainAmount"/>
        <result column="ordered_balance_buyer_num" jdbcType="INTEGER" property="orderedBalanceBuyerNum"/>
        <result column="ordered_balance_sku_num" jdbcType="INTEGER" property="orderedBalanceSkuNum"/>
        <result column="ordered_balance_amount" jdbcType="DOUBLE" property="orderedBalanceAmount"/>
    </resultMap>

    <sql id="base_field">
  	    id,shop_id,`date`,cs_nick,sku_id,sku_name,activity_id,consult_buyer_num,enquiry_buyer_num,
  	    enquiry_ordered_buyer_num,
        enquiry_ordered_sku_num,
        enquiry_ordered_bargain_buyer_num,
        enquiry_ordered_bargain_sku_num,
        enquiry_ordered_bargain_amount,
        enquiry_ordered_balance_buyer_num,
        enquiry_ordered_balance_sku_num,
        enquiry_ordered_balance_amount,
  	    to_ordered_bargain_buyer_num,
  	    to_ordered_balance_buyer_num,
  	    ordered_buyer_num,
  	    ordered_sku_num,
  	    ordered_bargain_buyer_num,
  	    ordered_bargain_sku_num,
  	    ordered_bargain_amount,
  	    ordered_balance_buyer_num,
  	    ordered_balance_sku_num,
  	    ordered_balance_amount
    </sql>


    <select id="selectByActivityIdAndSku" resultType="com.pes.jd.model.DO.CsPerformancePresaleDO">
        SELECT
        sku_id,sku_name,activity_id,shop_id,
        <if test="csNicks != null and csNicks.size() != 0">
            cs_nick,
        </if>
        SUM(consult_buyer_num) AS consultBuyerNum,
        SUM(enquiry_buyer_num) AS enquiryBuyerNum,
        SUM(enquiry_ordered_buyer_num) AS enquiryOrderedBuyerNum,
        SUM(enquiry_ordered_sku_num) AS enquiryOrderedSkuNum,
        SUM(enquiry_ordered_bargain_buyer_num) AS enquiryOrderedBargainBuyerNum,
        SUM(enquiry_ordered_bargain_sku_num) AS enquiryOrderedBargainSkuNum,
        SUM(enquiry_ordered_bargain_amount) AS enquiryOrderedBargainAmount,
        SUM(enquiry_ordered_balance_buyer_num) AS enquiryOrderedBalanceBuyerNum,
        SUM(enquiry_ordered_balance_sku_num) AS enquiryOrderedBalanceSkuNum,
        SUM(enquiry_ordered_balance_amount) AS enquiryOrderedBalanceAmount,
        SUM(to_ordered_bargain_buyer_num) AS toOrderedBargainBuyerNum,
        SUM(to_ordered_balance_buyer_num) AS toOrderedBalanceBuyerNum,
        SUM(ordered_buyer_num) AS orderedBuyerNum,
        SUM(ordered_sku_num) AS orderedSkuNum,
        SUM(ordered_bargain_buyer_num) AS orderedBargainBuyerNum,
        SUM(ordered_bargain_sku_num) AS orderedBargainSkuNum,
        SUM(ordered_bargain_amount) AS orderedBargainAmount,
        SUM(ordered_balance_buyer_num) AS orderedBalanceBuyerNum,
        SUM(ordered_balance_sku_num) AS orderedBalanceSkuNum,
        SUM(ordered_balance_amount) AS orderedBalanceAmount
        FROM ${tableName}
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        AND activity_id IN
        <foreach collection="activityIds" item="activityId" open="(" close=")" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        <if test="skuId != null">
            AND sku_id = #{skuId,jdbcType=BIGINT}
        </if>
        <if test="skuName != null and skuName != ''">
            AND sku_name = #{skuName,jdbcType=VARCHAR}
        </if>
        <if test="csNicks != null and csNicks.size() != 0">
            AND cs_nick IN
            <foreach collection="csNicks" item="nick" open="(" close=")" separator=",">
                #{nick,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY activity_id, sku_id
        <if test="csNicks != null and csNicks.size() != 0">
            ,cs_nick
        </if>
    </select>

    <select id="selectByActivityIdAndSkuAndDate" resultType="com.pes.jd.model.DO.CsPerformancePresaleDO">
        SELECT
        sku_id,sku_name,activity_id,`date`,
        SUM(consult_buyer_num) AS consultBuyerNum,
        SUM(enquiry_buyer_num) AS enquiryBuyerNum,
        SUM(enquiry_ordered_buyer_num) AS enquiryOrderedBuyerNum,
        SUM(enquiry_ordered_sku_num) AS enquiryOrderedSkuNum,
        SUM(enquiry_ordered_bargain_buyer_num) AS enquiryOrderedBargainBuyerNum,
        SUM(enquiry_ordered_bargain_sku_num) AS enquiryOrderedBargainSkuNum,
        SUM(enquiry_ordered_bargain_amount) AS enquiryOrderedBargainAmount,
        SUM(enquiry_ordered_balance_buyer_num) AS enquiryOrderedBalanceBuyerNum,
        SUM(enquiry_ordered_balance_sku_num) AS enquiryOrderedBalanceSkuNum,
        SUM(enquiry_ordered_balance_amount) AS enquiryOrderedBalanceAmount,
        SUM(to_ordered_bargain_buyer_num) AS toOrderedBargainBuyerNum,
        SUM(to_ordered_balance_buyer_num) AS toOrderedBalanceBuyerNum,
        SUM(ordered_buyer_num) AS orderedBuyerNum,
        SUM(ordered_sku_num) AS orderedSkuNum,
        SUM(ordered_bargain_buyer_num) AS orderedBargainBuyerNum,
        SUM(ordered_bargain_sku_num) AS orderedBargainSkuNum,
        SUM(ordered_bargain_amount) AS orderedBargainAmount,
        SUM(ordered_balance_buyer_num) AS orderedBalanceBuyerNum,
        SUM(ordered_balance_sku_num) AS orderedBalanceSkuNum,
        SUM(ordered_balance_amount) AS orderedBalanceAmount
        FROM ${tableName}
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        AND activity_id = #{activityId,jdbcType=VARCHAR}
        AND sku_id = #{skuId,jdbcType=BIGINT}
        <if test="startDate != null and endDate != null">
            AND `date` between #{startDate} AND #{endDate}
        </if>
        GROUP BY `date`
    </select>


    <select id="selectByActivityIdAndSkuAndDateAndCsNick" resultType="com.pes.jd.model.DO.CsPerformancePresaleDO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND activity_id = #{activityId,jdbcType=VARCHAR}
        AND sku_id = #{skuId,jdbcType=BIGINT}
        <if test="startDate != null and endDate != null">
            AND `date` between #{startDate} AND #{endDate}
        </if>
        AND cs_nick = #{csNick};
    </select>

</mapper>