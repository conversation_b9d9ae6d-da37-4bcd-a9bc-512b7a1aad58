<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderLatelyMapper">

	<resultMap id="OrderDTO" type="com.pes.jd.model.DTO.OrderLatelyDTO">
		<id column="order_id" property="orderId" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="created" property="created"
			jdbcType="TIMESTAMP" />
		<result column="buyer_nick" property="buyerNick"
			jdbcType="VARCHAR" />
	</resultMap>


	<sql id="base_field">
		order_id,shop_id,created,buyer_nick
	</sql>



	<select id="getOrderLatelyDate" resultMap="OrderDTO">
		SELECT
		<include refid="base_field" />
		FROM ${tableName}
		where
		buyer_nick=#{buyerNick}
		and shop_id=#{shopId}
		and created between #{begin} and #{end}
	</select>




</mapper>