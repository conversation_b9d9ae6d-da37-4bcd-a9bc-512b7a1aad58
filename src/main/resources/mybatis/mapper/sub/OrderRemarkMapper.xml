<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderRemarkMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderRemarkDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="flag" property="flag" jdbcType="TINYINT"/>
    </resultMap>
    
    
    <select id="getOrderRemark" parameterType="map" resultMap="BaseResultMap">
    select remark,flag
    from ${tableName}
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
    
    
    
    <insert id="insertOrderRemark" parameterType="map">
		INSERT INTO ${tableName} (shop_id,order_id,created,modified,remark,flag)
		VALUES
		(#{orderRemark.shopId},#{orderRemark.orderId},#{orderRemark.created},#{orderRemark.modified},#{orderRemark.remark},#{orderRemark.flag})
	</insert>
	
	<update id="updateOrderRemark" parameterType="map">
        update ${tableName} set flag = #{orderRemark.flag},
      	remark = #{orderRemark.remark}  
     	where order_id = #{orderRemark.orderId}  
    </update>
    
</mapper>