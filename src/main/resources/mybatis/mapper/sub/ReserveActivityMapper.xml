<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ReserveActivityMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.ReserveActivityDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="plus_type" property="plusType" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="presel_num" property="preselNum" jdbcType="INTEGER"/>
        <result column="isJ" property="isj" jdbcType="BIGINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="panicbuying_stime" property="panicbuyingStime" jdbcType="TIMESTAMP"/>
        <result column="panicbuying_etime" property="panicbuyingEtime" jdbcType="TIMESTAMP"/>
        <result column="plus_stime" property="plusStime" jdbcType="TIMESTAMP"/>
        <result column="plus_etime" property="plusEtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="ReserveActivityDTO" type="com.pes.jd.model.DTO.ReserveActivityDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="type" jdbcType="TINYINT" property="type"/>
        <result column="plus_type" jdbcType="TINYINT" property="plusType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="presel_num" jdbcType="INTEGER" property="preselNum"/>
        <result column="isJ" jdbcType="INTEGER" property="isj"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="panicbuying_stime" jdbcType="TIMESTAMP" property="panicbuyingStime"/>
        <result column="panicbuying_etime" jdbcType="TIMESTAMP" property="panicbuyingEtime"/>
        <result column="plus_stime" jdbcType="TIMESTAMP" property="plusStime"/>
        <result column="plus_etime" jdbcType="TIMESTAMP" property="plusEtime"/>
        <result column="condition_type" jdbcType="TINYINT" property="conditionType"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, activity_id, sku_id, shop_id, type, plus_type, status, presel_num, isJ, start_time, 
    end_time, 
    panicbuying_stime, panicbuying_etime, plus_stime,
    plus_etime ,condition_type
  </sql>


    <select id="selectReserveActivity" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where shop_id=#{shopId} and
        activity_id = #{activityId}
    </select>


    <select id="selectReserveActivityNow" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where shop_id=#{shopId} and
        (
        (
        #{nowDate} between start_time and end_time
        )
        or

        (
        #{nowDate} between panicbuying_stime and panicbuying_etime
        )
        )

        and condition_type = 1

        <!--         不预约可购买 -->
        and type in (1,4)

        and panicbuying_stime is not null
        and panicbuying_etime is not null

    </select>


    <select id="selectShopReservePresaleByActId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where shop_id=#{shopId} and
        activity_id in
        <foreach collection="activityId" item="actId" open="(" close=")" separator=",">
            #{actId}
        </foreach>
        group by activity_id
    </select>


    <select id="selectShopReserveByShopId" resultMap="BaseResultMap">
        select
        activity_id, sku_id, shop_id, start_time,
        end_time,
        panicbuying_stime, panicbuying_etime
        from
        ${tableName}
        <where>
            shop_id=#{shopId}
        </where>
    </select>

    <select id="selectShopReserveByShopIdByActiveityId" resultMap="BaseResultMap">
        select
        activity_id,
        sku_id,
        shop_id,
        start_time,
        end_time,
        panicbuying_stime,
        panicbuying_etime
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and activity_id=#{activityId}
        </where>
    </select>

    <select id="selectReserveActivityByShop" resultMap="ReserveActivityDTO">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <trim prefix="WHERE" prefixOverrides="AND">
            <if test="shopId!=null">
                shop_id=#{shopId}
            </if>
            <if test="startDate!=null and endDate!=null">
                AND ( (panicbuying_etime &gt;= #{startDate})
                AND (start_time &lt;= #{endDate}))
            </if>
            <if test="activityId!=null">
                AND activity_id =#{activityId}
            </if>
            <if test="skuId!=null and skuId.size()>0">
                AND sku_id in
                <foreach collection="skuId" item="itme" open="(" close=")" separator=",">
                    #{itme}
                </foreach>
            </if>
            <if test="type!=null">
                AND type IN (${type})
            </if>
            <if test="status!=null">
                AND status IN (${status})
            </if>
            <if test="conditionType!=null">
                AND condition_type =#{conditionType}
            </if>
        </trim>
        ORDER BY start_time DESC
    </select>


    <select id="selectReserveActivityByConditionType" resultMap="ReserveActivityDTO">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            shop_id = #{shopId}
            AND
            condition_type = #{conditionType};
            <if test="activityId!=null">
                and activity_id= #{activityId}
            </if>
        </where>

    </select>

    <select id="selectReserveActivtyByActivityId" resultMap="ReserveActivityDTO">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            shop_id = #{shopId}
            AND
            activity_id = #{activityId}
        </where>
    </select>


    <select id="selectReserveActivityByActivityIdAndSkuId" resultMap="ReserveActivityDTO">

        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        <where>
            shop_id = #{shopId}
            AND
            activity_id = #{activityId}
            <if test="skuId!=null">
                AND sku_id=#{skuId}
            </if>
        </where>
        LIMIT 0,1

    </select>
    <select id="selectReserveGoodsFromPra" resultMap="ReserveActivityDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ${tableName}
        where
        shop_id = #{shopId}
    </select>

    <select id="getPresaleActivityIds" resultType="String">
        SELECT activity_id
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND create_time BETWEEN #{startDate} AND #{endDate}
        AND presale_pay_type = 1
    </select>
</mapper>