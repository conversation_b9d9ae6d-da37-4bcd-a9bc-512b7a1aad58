<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.LossOrderRecordMapper" >
  <resultMap id="LossOrderRecordDTO" type="com.pes.jd.model.DTO.LossOrderRecordDTO" >
  	
  	<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="customer" property="customer" jdbcType="VARCHAR" />
		<result column="order_id" property="orderId" jdbcType="BIGINT" />
		<result column="order_payment" property="orderPayment" jdbcType="DOUBLE" />
		<result column="order_created" property="orderCreated" jdbcType="TIMESTAMP" />
		<result column="order_goods_num" property="orderGoodsNum" jdbcType="INTEGER" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="start_datetime" property="startDateTime" jdbcType="TIMESTAMP" />
		<result column="end_datetime" property="endDateTime" jdbcType="TIMESTAMP" />
		<result column="is_chat_after_ordered" property="isChatAfterOrdered" jdbcType="BIT" />
		<result column="type" property="type" jdbcType="INTEGER" />
		<result column="order_chat_type" property="chatType" jdbcType="INTEGER" />
  	
  </resultMap>
 	
 	<select id="selectEnquiryOrderLostRecordLstByDate" parameterType="map" resultMap="LossOrderRecordDTO">
 		SELECT * 
 		FROM ${tableName}
 		WHERE 
 		shop_id = #{shopId}
 		AND order_created BETWEEN #{startDate} AND #{endDate}
 		<if test="lossOrderParam.csNickInfo != null and lossOrderParam.csNickInfo.size() >0">
	 		AND cs_nick IN 
	 		<foreach collection="lossOrderParam.csNickInfo" item="csNickInfo" open="(" close=")" separator=",">
	 			#{csNickInfo.nick}
	 		</foreach>
 		</if>
 		<if test="lossOrderParam.buyerNick != '' and lossOrderParam.buyerNick != ''">
 			AND customer = #{lossOrderParam.buyerNick}
 		</if>
 		<if test="lossOrderParam.orderId != null and lossOrderParam.orderId != ''">
 			AND order_id = #{lossOrderParam.orderId}
 		</if>
 		AND type = 1
 	</select>
 	
 	<select id="selectOrderLossByDateAndCsNickForCustReceiveAll" resultType="com.pes.jd.model.DTO.LossOrderRecordDTO">
 		select 
	 		cl.shop_id shopId,
	 		cl.date date ,
	 		cl.customer customer,
	 		cl.cs_nick csNick,
	 		cl.type type,
	 		cl.order_chat_type chatType
 		from 
 		(
 			<foreach collection="olTableNames" item="ol" separator="union all">
	 			select 
		 			shop_id,
		 			date,
		 			customer,
		 			cs_nick,
		 			type,
		 			order_chat_type 
	 			from ${ol.tableName}
	 			<where>
	 				cs_nick in
					<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
						#{csNick}
					</foreach>
					<if test="buyerNick!=null and buyerNick!=''">
						AND	customer =#{buyerNick}
					</if>
					AND shop_id=#{shopId}
					AND date between #{ol.beginDate} and #{ol.endDate}
	 			</where>
 			</foreach>
 		)cl
 	
 	</select>
 	<select id="selectOrderLossByDateAndCsNickForCustReceiveOrderLoss" resultType="com.pes.jd.model.DTO.CustomerReceiveDTO">
 	SELECT DISTINCT
	 		cp.buyer_nick buyerNick,
			cp.cs_nick csNick ,
			cp.first_chat_date firstChatDate,
			cp.last_chat_date lastChatDate,
			cp.shop_id shopId,
			cp.date date,
			cp.cross_chat_fail crossChatFail,
			cp.is_after_sale isAfterSale,
			cp.cs_active_chat_fail csActiveChatFail,
			cp.cs_active_urgepay_fail csActiveUrgepayFail ,
			cp.is_order_created isOrderCreated
			
		FROM
 	
 	(
 		<foreach collection="olTableNames" item="ol" separator="union all">
 			SELECT 
	 			shop_id,
	 			date,
	 			customer,
	 			cs_nick 
 			FROM ${ol.tableName}
 			<where>
 					 	shop_id=#{shopId}
 					AND cs_nick in
				<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
					#{csNick}
				</foreach>
				<if test="param.buyerNick!=null and param.buyerNick!=''">
					AND	customer =#{param.buyerNick}
				</if>
					AND	type in (1,2)
					AND date between #{ol.beginDate} and #{ol.endDate}
 			</where>
 		</foreach>
 	)ol
	INNER JOIN
	(
		<foreach collection="cpTableNames" item="cp" separator="UNION ALL" >
			SELECT 
				shop_id,
				date,
				cs_nick ,
				buyer_nick,
				first_chat_date, 
				last_chat_date,
				is_after_sale,
				cross_chat_fail ,
				cs_active_chat_fail,
				cs_active_urgepay_fail,
				is_order_created
			FROM ${cp.tableName}
			<where>
			 	shop_id=#{shopId}
			AND cs_nick in
			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
				#{csNick}
			</foreach>
			<if test="param.buyerNick!=null and param.buyerNick!=''">
			AND	buyer_nick =#{param.buyerNick}
			</if>
			AND is_receive=1
			AND date between #{cp.beginDate} and #{cp.endDate}
			</where>
		</foreach>
	)
	cp
	ON  ol.customer=cp.buyer_nick
	WHERE 
		ol.date=cp.date
	AND	ol.cs_nick=cp.cs_nick
	<if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!=''">
            ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
   </if>
	<if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
	            LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
	 </if>
 	</select>
 	
 	<select id="selectOrderLossCountByDateAndCsNickForCustReceiveOrderLoss" resultType="java.lang.Integer">
 	select 
 		count(distinct cp.id)
		FROM
 	
 	(
 		<foreach collection="olTableNames" item="ol" separator="union all">
 			select
	 			shop_id,
	 			date,
	 			customer,
	 			cs_nick 
 			FROM ${ol.tableName}
 			<where>
 				cs_nick in
				<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
					#{csNick}
				</foreach>
				<if test="param.buyerNick!=null and param.buyerNick!=''">
					AND	customer =#{param.buyerNick}
				</if>
				AND	type in (1,2)
				AND shop_id=#{shopId}
				AND date between #{ol.beginDate} and #{ol.endDate}
 			</where>
 		</foreach>
 	)ol
	INNER JOIN
	(
		<foreach collection="cpTableNames" item="cp" separator="UNION ALL" >
			SELECT 
				id,
				shop_id,
				date,
				cs_nick ,
				buyer_nick 
			FROM ${cp.tableName}
			<where>
			   shop_id=#{shopId}
			AND cs_nick in
			<foreach collection="csNickLst" item="csNick" open="(" close=")" separator=",">
				#{csNick}
			</foreach>
			<if test="param.buyerNick!=null and param.buyerNick!=''">
			AND	buyer_nick =#{param.buyerNick}
			</if>
			AND is_receive=1
			AND date between #{cp.beginDate} and #{cp.endDate}
			</where>
		</foreach>
	)
	cp
	on ol.customer=cp.buyer_nick
	where 
		ol.date=cp.date
	and	ol.cs_nick=cp.cs_nick
 	</select>
 	  
  <select id="selectSilentOrderLostRecordLstByDateByType" parameterType="map" resultMap="LossOrderRecordDTO">
	SELECT shop_id,cs_nick,customer,date,start_datetime,end_datetime,type,order_id,order_created,order_payment,is_chat_after_ordered
	FROM ${tableName}
	WHERE date BETWEEN #{startDate} AND #{endDate}
	<if test="orderId != null and orderId != ''">
		AND order_id = #{orderId}
	</if>
	<if test="buyerNick != '' and buyerNick != null">
		AND customer = #{buyerNick}
	</if>
	AND type = #{type}
	AND shop_id = #{shopId}
	<!-- ORDER BY start_datetime -->
  </select>
 	
</mapper>