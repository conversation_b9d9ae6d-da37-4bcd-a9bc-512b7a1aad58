<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.OrderConsignMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderConsignDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="full_name" jdbcType="VARCHAR" property="fullName" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="full_address" jdbcType="VARCHAR" property="fullAddress" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="town" jdbcType="VARCHAR" property="town" />
    <result column="province_id" jdbcType="VARCHAR" property="provinceId" />
    <result column="city_id" jdbcType="VARCHAR" property="cityId" />
    <result column="county_id" jdbcType="VARCHAR" property="countyId" />
    <result column="town_id" jdbcType="VARCHAR" property="townId" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="post_code" jdbcType="VARCHAR" property="postCode" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="modified_time" jdbcType="TIMESTAMP" property="modifiedTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, full_name, telephone, mobile, full_address, province, city, county, 
    town, province_id, city_id, county_id, town_id, email, post_code, created_time, modified_time
  </sql>



  <update id="updateConsignAddr" parameterType="map">
    update ${tableName} 
    set 
      full_name = #{shippingAddress.customerName,jdbcType=VARCHAR},
      telephone = #{shippingAddress.customerPhone,jdbcType=VARCHAR},
      mobile = #{shippingAddress.customerMobile,jdbcType=VARCHAR},
      full_address = #{shippingAddress.detailAddr,jdbcType=VARCHAR} ,
<!--       province = #{province,jdbcType=VARCHAR}, -->
<!--       city = #{city,jdbcType=VARCHAR}, -->
<!--       county = #{county,jdbcType=VARCHAR}, -->
<!--       town = #{town,jdbcType=VARCHAR}, -->
<!--       province_id = #{provinceId,jdbcType=VARCHAR}, -->
<!--       city_id = #{cityId,jdbcType=VARCHAR}, -->
<!--       county_id = #{countyId,jdbcType=VARCHAR}, -->
<!--       town_id = #{townId,jdbcType=VARCHAR}, -->
<!--       email = #{email,jdbcType=VARCHAR}, -->
<!--       post_code = #{postCode,jdbcType=VARCHAR}, -->
<!--       created_time = #{createdTime,jdbcType=TIMESTAMP}, -->
      modified_time = now()  
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>


    <select id="selectConsignDTO" parameterType="map" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${tableName}
    where order_id = #{oid,jdbcType=BIGINT}
  </select>
  

<insert id="insertOrderConsign" parameterType="map">
    insert into ${tableName}
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderConsignDTO.orderId != null">
        order_id,
      </if>
      <if test="orderConsignDTO.fullName != null">
        full_name,
      </if>
      <if test="orderConsignDTO.telephone != null">
        telephone,
      </if>
      <if test="orderConsignDTO.mobile != null">
        mobile,
      </if>
      <if test="orderConsignDTO.fullAddress != null">
        full_address,
      </if>
      <if test="orderConsignDTO.province != null">
        province,
      </if>
      <if test="orderConsignDTO.city != null">
        city,
      </if>
      <if test="orderConsignDTO.county != null">
        county,
      </if>
      <if test="orderConsignDTO.town != null">
        town,
      </if>
      <if test="orderConsignDTO.provinceId != null">
        province_id,
      </if>
      <if test="orderConsignDTO.cityId != null">
        city_id,
      </if>
      <if test="orderConsignDTO.countyId != null">
        county_id,
      </if>
      <if test="orderConsignDTO.townId != null">
        town_id,
      </if>
      <if test="orderConsignDTO.email != null">
        email,
      </if>
      <if test="orderConsignDTO.postCode != null">
        post_code,
      </if>
      <if test="orderConsignDTO.createdTime != null">
        created_time,
      </if>
      <if test="orderConsignDTO.modifiedTime != null">
        modified_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderConsignDTO.orderId != null">
        #{orderConsignDTO.orderId,jdbcType=BIGINT},
      </if>
      <if test="orderConsignDTO.fullName != null">
        #{orderConsignDTO.fullName,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.telephone != null">
        #{orderConsignDTO.telephone,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.mobile != null">
        #{orderConsignDTO.mobile,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.fullAddress != null">
        #{orderConsignDTO.fullAddress,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.province != null">
        #{orderConsignDTO.province,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.city != null">
        #{orderConsignDTO.city,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.county != null">
        #{orderConsignDTO.county,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.town != null">
        #{orderConsignDTO.town,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.provinceId != null">
        #{orderConsignDTO.provinceId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.cityId != null">
        #{orderConsignDTO.cityId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.countyId != null">
        #{orderConsignDTO.countyId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.townId != null">
        #{orderConsignDTO.townId,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.email != null">
        #{orderConsignDTO.email,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.postCode != null">
        #{orderConsignDTO.postCode,jdbcType=VARCHAR},
      </if>
      <if test="orderConsignDTO.createdTime != null">
        #{orderConsignDTO.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderConsignDTO.modifiedTime != null">
        #{orderConsignDTO.modifiedTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  
  
   <delete id="delConsign" parameterType="map">
        DELETE FROM ${tableName}
        where
		order_id = #{orderId,jdbcType=BIGINT}
    </delete>
  
</mapper>