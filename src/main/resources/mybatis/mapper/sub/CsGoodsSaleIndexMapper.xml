<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsGoodsSaleIndexMapper">
	<resultMap id="CsGoodsSaleIndexDTO" type="com.pes.jd.model.DTO.CsGoodsSaleIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="purchase_buyer_num" jdbcType="INTEGER" property="purchaseBuyerNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
  </resultMap>

  <select id="selectCsGoodsSaleIndexCountByCsNickBySku" resultMap="CsGoodsSaleIndexDTO">
	  SELECT
  		sale.cs_nick,sale.sku_id,sale.shop_id,sale.date,goods.sku_name,
  		sum(sale.purchase_buyer_num) purchase_buyer_num,
  		sum(sale.sale_goods_num) sale_goods_num,
  		sum(sale.sale_amount) sale_amount,goods.image_url,goods.category_id
		FROM
		(<foreach collection="csGoodsSaleTables" item="table" separator="union">
			select cs_nick,sku_id,shop_id,date,
				purchase_buyer_num,sale_goods_num,sale_amount
	  		from ${table.getTableName}
	  		<where>
	  			<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and cs_nick in
					<foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
	  		</where>
		</foreach>) sale left join ${goodsSkuTables} goods on sale.sku_id = goods.sku_id
	  		group by sale.cs_nick,sale.sku_id
  </select>


	<select id="selectCsGoodsSaleIndexCountByCsNickBySkuV2" resultMap="CsGoodsSaleIndexDTO">
		SELECT
		sale.cs_nick,sale.sku_id,sale.shop_id,sale.date,goods.sku_name,
		sum(sale.purchase_buyer_num) purchase_buyer_num,
		sum(sale.sale_goods_num) sale_goods_num,
		sum(sale.sale_amount) sale_amount,goods.image_url,goods.category_id
		FROM(
		<foreach collection="csGoodsSaleTables" item="table" separator="union">
			select sale.cs_nick,sale.sku_id,sale.shop_id,sale.date,
			sale.purchase_buyer_num,sale.sale_goods_num,sale.sale_amount
			from ${table.getTableName} sale
			<if test="categoryId != null and categoryId.size > 0">
				inner join ${goodsSkuTables} gs on sale.shop_id = gs.shop_id and sale.sku_id = gs.sku_id
			</if>
			<where>
				<if test="shopId!=null">
					sale.shop_id = #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and sale.cs_nick in
					<foreach collection="csNickList" item="nick" open="(" close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="categoryId!=null and categoryId.size>0">
					and gs.category_id in
					<foreach collection="categoryId" item="catId" separator="," open="(" close=")">
						#{catId}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sale.sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				and sale.date between #{startDate} and #{endDate}
			</where>
		</foreach>
		) sale
		left join ${goodsSkuTables} goods on sale.sku_id = goods.sku_id
		group by sale.cs_nick,sale.sku_id
	</select>


	<select id="selectCsGoodsSaleIndexByCsNickByDateBySku" resultMap="CsGoodsSaleIndexDTO">
		SELECT
  		sale.cs_nick,sale.sku_id,sale.date,
  		sale.purchase_buyer_num,
  		sale.sale_goods_num,
  		sale.sale_amount,
  		goods.sku_name
		FROM (<foreach collection="csGoodsSaleTables" item="table" separator="union">
			select cs_nick,sku_id,date,
				purchase_buyer_num,
		  		sale_goods_num,
		  		sale_amount
	  		from ${table.getTableName}
	  		<where>
	  			<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="csNick!=null">
					and cs_nick = #{csNick}
				</if>
				<if test="skuId!=null">
					and sku_id = #{skuId}
				</if>
				and date between #{startDate} and #{endDate}
	  		</where>
		</foreach>) sale left join ${goodsSkuTables} goods on sale.sku_id = goods.sku_id
	</select>
</mapper>
