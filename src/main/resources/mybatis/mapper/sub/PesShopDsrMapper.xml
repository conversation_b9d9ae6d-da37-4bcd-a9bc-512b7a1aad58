<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.PesShopDsrMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.PesShopDsr">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="item_score" jdbcType="DOUBLE" property="itemScore"/>
        <result column="service_score" jdbcType="DOUBLE" property="serviceScore"/>
        <result column="delivery_score" jdbcType="DOUBLE" property="deliveryScore"/>
    </resultMap>

    <sql id="base_field">
    id, shop_id, date, item_score, service_score, delivery_score
  </sql>

    <select id="selectByShopId" resultMap="BaseResultMap">
        SELECT
        <include refid="base_field"/>
        FROM
        ${tableName}
        WHERE shop_id = #{shopId}
        AND date BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectShopPerformance" resultType="com.pes.jd.model.DTO.ShopPerformanceDTO" parameterType="map">
    SELECT
   id, shop_id shopId, date, item_score dsrItemRating, service_score dsrServiceRating, delivery_score dsrDeliveryRating
    FROM
    ${tableName}
    WHERE  shop_id = #{shopId}
    AND date BETWEEN #{startDate} AND #{endDate}
  </select>


    <select id="selectByShopIdAndDate" resultType="com.pes.jd.model.DTO.ShopDsrDTO">
        SELECT
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                `shop_id`, `date`, `date` AS 'dateStr', `item_score`, `service_score`, `delivery_score`, `after_sale_score`, `dispute_score`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                `shop_id`,
                DATE_FORMAT( `date`, '%Y-%m' ) AS 'dateStr',
                SUM( `item_score` ) `item_score`,
                SUM( `service_score` ) `service_score`,
                SUM( `delivery_score` ) delivery_score,
                SUM( `after_sale_score` ) after_sale_score,
                SUM( `dispute_score` ) dispute_score
            </when>
            <!--店铺-->
            <otherwise>
                `shop_id`,
                SUM( `service_score` ) `service_score`,
                SUM( `delivery_score` ) delivery_score,
                SUM( `after_sale_score` ) after_sale_score,
                SUM( `dispute_score` ) dispute_score
            </otherwise>
        </choose>

        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
                GROUP BY DATE_FORMAT( `date`, '%Y-%m' )
            </when>
            <!--店铺-->
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="date" open="(" close=")" separator=",">
                    #{date}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>

</mapper>