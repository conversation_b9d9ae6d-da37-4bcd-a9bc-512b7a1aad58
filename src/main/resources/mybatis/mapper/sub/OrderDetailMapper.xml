<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.OrderDetailMapper">
  <resultMap id="OrderDetailDTO" type="com.pes.jd.model.DTO.OrderDetailDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="item_sku_id" jdbcType="VARCHAR" property="itemSkuId" />
    <result column="item_price" jdbcType="DOUBLE" property="itemPrice" />
    <result column="item_num" jdbcType="INTEGER" property="itemNum" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="payment" jdbcType="DOUBLE" property="payment" />
    <result column="ware_id" jdbcType="BIGINT" property="wareId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="seller_discount" jdbcType="DOUBLE" property="sellerDiscount" />
    <result column="total_fee" jdbcType="DOUBLE" property="totalFee" />
  </resultMap>

  <sql id="base_field">
  	id,shop_id,order_id,item_sku_id,created,pay_time,item_num,item_price,buyer_nick,payment,shop_id,seller_discount,total_fee
  </sql>

  <select id="selectOrderGoodsSkuByDateByCustomerBySkuLst" parameterType="map" resultMap="OrderDetailDTO">
  	SELECT 
	  	shop_id,
	  	buyer_nick,
	  	order_id,
	  	item_sku_id,
	  	created,
	  	pay_time,
	  	item_num,
	  	item_price,
	  	payment,
	  	seller_discount,
	  	total_fee
  	FROM ${orderSkuTableName} 
  	<where>
  		 shop_id=#{shopId}
  		 AND created between #{startDate} and #{endDate} 
  	<if test="customer!=null and customer!=''">
  		AND	buyer_nick=#{customer}
  	</if>
  	 <if test="skuSet!=null and skuSet.size>0">
  		AND item_sku_id in 
  		<foreach collection="skuSet" open="(" close=")" item="skuId" separator=",">
  			#{skuId}
  		</foreach>
  	</if> 
  	</where>
  </select>
</mapper>