<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.CustomerOrderMapper">

    <resultMap id="OrderDTO" type="com.pes.jd.model.DTO.OrderDTO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="seller_nick" property="sellerNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="post_fee" property="postFee" jdbcType="DOUBLE"/>
        <result column="consign_time" property="consignTime" jdbcType="TIMESTAMP"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="total_fee" property="totalFee" jdbcType="DOUBLE"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="seller_flag" property="sellerFlag" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="pay_type" jdbcType="TINYINT" property="payType"/>
        <result column="seller_discount" jdbcType="DOUBLE" property="sellerDiscount"/>
        <result column="order_type" jdbcType="TINYINT" property="orderType"/>
    </resultMap>
    
    
        <resultMap id="GoodsDTO" type="com.pes.jd.model.DTO.GoodsDTO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <id column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <id column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <id  column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <id column="item_sku_id" property="itemSkuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="item_price" property="itemPrice" jdbcType="BIGINT"/>
        <result column="item_num"  property="itemNum" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
   	 </resultMap>

    <sql id="base_field">
		order_id,seller_nick,date,shop_id,payment,
		post_fee,consign_time,num,status,total_fee,
		created,pay_time,modified,end_time,buyer_nick,
		seller_flag,type,pay_type,seller_discount,order_type
	</sql>

	
	
	   <select id="selectOrderInfoByBuyerNickAndShop" resultMap="OrderDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        where
        buyer_nick=#{buyerNick}
        and shop_id=#{shopId}
           and  date != STR_TO_DATE(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d')
         order by created desc  
        LIMIT #{start}, #{length}
    	</select>
    	
    	
    	
    	   <select id="selectOrderById" resultMap="OrderDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        where
         order_id=#{orderId}
        
    	</select>
    	
    	
     <select id="selectOrderStatu" resultType="int">
        SELECT count(1)
        FROM ${tableName}
        where
         order_id=#{orderId}
    	</select>
   
   
   	  <select id="selectOrderDetail"  parameterType="map" resultType="com.pes.jd.model.DTO.OrderDetailInfoDTO">
    select cust_remark  from ${tableName} 
    where  order_id=#{orderId} 
  	</select>	
   
      <select id="detOrderDetail" resultType="int">
        DELETE  
        FROM ${tableName}
        where
         order_id=#{orderId}
    	</select>
   
   
   	<insert id="insertOrderDetail" parameterType="map">
    insert into ${tableName} ( shop_id, order_id, 
      cust_remark, order_created)
    values ( #{orderDetailInfoDTO.shopId}, #{orderDetailInfoDTO.orderId}, 
      #{orderDetailInfoDTO.custRemark},now())
  </insert>
   
    	
    	
    	
    	
 <select id="selectOrderInfoByBuyerNickAndShopNow" resultMap="OrderDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        where
        buyer_nick=#{buyerNick}
        and shop_id=#{shopId}
        and  date = STR_TO_DATE(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d')
        
<!--          <if test="flag == '0'.toString()"> -->
<!--            and  date != STR_TO_DATE(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d') -->
<!--         </if> -->
        
         order by created desc  
        LIMIT #{start}, #{length}
    	</select>
    	

	
	 <select id="selectOrderInfoByBuyerNickAndShopCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM ${tableName}
        WHERE
        buyer_nick=#{buyerNick}
        and shop_id=#{shopId}
          <if test="flag == '1'.toString()">
           and  date = STR_TO_DATE(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d')
        </if>
        
         <if test="flag == '0'.toString()">
           and  date != STR_TO_DATE(DATE_FORMAT(NOW(),'%Y-%m-%d'),'%Y-%m-%d')
        </if>
    </select>
	
	
	  <select id="selectGoodSInfoByOrderId" resultMap="GoodsDTO">
      SELECT
      order_id,
      shop_id,
      item_sku_id,
      item_price,
      item_num from ${tableName}
      where order_id  = 
          #{orderId}
     </select>
	
	
	    <select id="selectGoodsDetailInfoBySkuId" resultMap="GoodsDTO">
        select ware_id,sku_id,sku_name,image_url from  ${tableName} where sku_id 
        = #{skuId}
    </select>
	
	<insert id="insertRecord" parameterType="map">
    insert into ${tableName} ( shop_id, order_id, 
      buyer_nick, create_time)
    values ( #{shopId}, #{orderId}, 
      #{buyerNick},now())
  </insert>
	
	 <select id="selectAddrFlag" parameterType="map" resultType="int">
      SELECT count(1) 
      from ${tableName} 
      where order_id = #{orderId}
     </select>
	
	
  <select id="selectOrderCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM ${tableName}
        WHERE  shop_id=#{shopId}  and 
        buyer_nick=#{buyerNick}
        and date <![CDATA[ <]]>  STR_TO_DATE(#{endTimeStr},'%Y-%m-%d') 
    </select>
	
	<select id="getOrderList" resultMap="OrderDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        where  shop_id=#{shopId} and
        buyer_nick=#{buyerNick}
         and  date  <![CDATA[ <]]>  STR_TO_DATE(#{endTimeStr},'%Y-%m-%d') 
         order by created desc  
<!--         LIMIT #{start}, #{length} -->
    	</select>
    	
	
	
	
</mapper>