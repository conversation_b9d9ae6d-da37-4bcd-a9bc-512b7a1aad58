<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderCustomerInfoMapper" >
  <resultMap id="OrderCustomerInfoDO" type="com.pes.jd.model.DO.OrderCustomerInfoDO" >
    <id column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="consignee_telp" property="consigneeTelp" jdbcType="VARCHAR" />
    <result column="consignee" property="consignee" jdbcType="VARCHAR" />
  </resultMap>

    <resultMap id="OrderCustomerInfoDTO" type="com.pes.jd.model.DTO.OrderCustomerInfoDTO" >
        <id column="order_id" property="orderId" jdbcType="BIGINT" />
        <result column="shop_id" property="shopId" jdbcType="BIGINT" />
        <result column="date" property="date" jdbcType="DATE" />
        <result column="consignee_telp" property="consigneeTelp" jdbcType="VARCHAR" />
        <result column="consignee" property="consignee" jdbcType="VARCHAR" />
    </resultMap>
  <sql id="base_field" >
    order_id, shop_id, date, consignee_telp, consignee
  </sql>
  <select id="selectCustomerInfoByOrderIds" resultMap="OrderCustomerInfoDTO"  >
    select
      order_id, shop_id, date, consignee_telp, consignee
    from ${tableName}
    where
        shop_id=#{shopId}
      and order_id
      in
      <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
          #{orderId}
      </foreach>
  </select>

</mapper>