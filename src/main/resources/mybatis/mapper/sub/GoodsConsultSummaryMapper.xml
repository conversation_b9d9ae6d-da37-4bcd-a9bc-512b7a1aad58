<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.GoodsConsultSummaryMapper" >
	<resultMap id="GoodsConsultSummaryDTO" type="com.pes.jd.model.DTO.GoodsConsultSummaryDTO" >
	<result column="sku_id" property="skuId" jdbcType="BIGINT" />
	<result column="sku_name" property="skuName" jdbcType="VARCHAR" />
	<result column="price" property="price" jdbcType="DOUBLE" />
	<result column="consult_num" property="consultNum" jdbcType="INTEGER" />
	<result column="purchases_buyer_num" property="purchasesBuyerNum" jdbcType="INTEGER" />
	<result column="purchases_goods_num" property="purchasesGoodsNum" jdbcType="INTEGER" />
	<result column="purchases_amount" property="purchasesAmount" jdbcType="DOUBLE" />
	</resultMap>

	<resultMap id="GoodsConsultSummaryV2DTO" type="com.pes.jd.model.DTO.GoodsConsultSummaryV2DTO" >
		<result column="sku_id" property="skuId" jdbcType="BIGINT" />
		<result column="sku_name" property="skuName" jdbcType="VARCHAR" />
		<result column="receive_num" property="receiveNum" jdbcType="INTEGER" />
		<result column="enquiry_num" property="enquiryNum" jdbcType="INTEGER" />
		<result column="pay_num" property="payNum" jdbcType="INTEGER" />
		<result column="pay_goods_num" property="payGoodsNum" jdbcType="INTEGER" />
		<result column="pay_amount" property="payAmount" jdbcType="DOUBLE" />
	</resultMap>

  <select id="selectGoodsConsultSummaryCountByDateBySkuIdByCsNick" resultMap="GoodsConsultSummaryDTO">
	select
		consult.sku_id,consult.shop_id,consult.cs_nick,consult.date,
		sum(consult.consult_num) consult_num,
		sum(consult.purchases_buyer_num) purchases_buyer_num,
		sum(consult.purchases_goods_num) purchases_goods_num,
		sum(consult.purchases_amount) purchases_amount,
		goods.id,goods.sku_name,goods.category_id,goods.image_url
	from
	(<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
		select sku_id,shop_id,cs_nick,date,
			consult_num,
			purchases_buyer_num,
			purchases_goods_num,
			purchases_amount
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size>0">
		    	and cs_nick in
		   		<foreach collection="csNickList" item = "nick" separator="," open="(" close=")">
		   			#{nick}
		   		</foreach>
		    </if>
		    <if test="skuLst!=null and skuLst.size>0">
		  		 AND sku_id in
			  	<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
			  			#{skuId}
			  	</foreach>
		  	</if>
		  	AND date between #{startDate} and #{endDate}
		</where>
	</foreach>
	) consult left join ${goodsSkuTable} goods on consult.sku_id = goods.sku_id
	group by consult.sku_id
	order by consult.sku_id
  </select>



	<select id="selectGoodsConsultSummaryCountByDateBySkuIdByCsNickByCategoryId" resultMap="GoodsConsultSummaryDTO">
		select
		consult.sku_id,consult.shop_id,consult.cs_nick,consult.date,
		sum(consult.consult_num) consult_num,
		sum(consult.purchases_buyer_num) purchases_buyer_num,
		sum(consult.purchases_goods_num) purchases_goods_num,
		sum(consult.purchases_amount) purchases_amount,
		goods.id,goods.sku_name,goods.category_id,goods.image_url
		from
		(<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
		select gs.sku_id,gs.shop_id,consult.cs_nick,date,
		consult_num,
		purchases_buyer_num,
		purchases_goods_num,
		purchases_amount
		from ${table.getTableName} consult
		inner join ${goodsSkuTable} gs on consult.shop_id = gs.shop_id and consult.sku_id = gs.sku_id
		<where>
			<if test="shopId!=null">
				consult.shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size>0">
				and consult.cs_nick in
				<foreach collection="csNickList" item="nick" separator="," open="(" close=")">
					#{nick}
				</foreach>
			</if>
			<if test="categoryId!=null and categoryId.size>0">
				and gs.category_id in
				<foreach collection="categoryId" item="catId" separator="," open="(" close=")">
					#{catId}
				</foreach>
			</if>
			<if test="skuLst!=null and skuLst.size>0">
				AND consult.sku_id in
				<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
					#{skuId}
				</foreach>
			</if>
			AND consult.date between #{startDate} and #{endDate}
		</where>
	</foreach>
		) consult left join ${goodsSkuTable} goods on consult.sku_id = goods.sku_id
		group by consult.sku_id
		order by consult.sku_id
	</select>



	<select id="selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV2" resultMap="GoodsConsultSummaryV2DTO">
		select
		consult.sku_id,consult.shop_id,consult.cs_nick,consult.date,
		sum(consult.receive_num) receive_num,
		sum(consult.enquiry_num) enquiry_num,
		sum(consult.pay_num) pay_num,
		sum(consult.pay_goods_num) pay_goods_num,
		sum(consult.pay_amount) pay_amount,
		goods.id,goods.sku_name,goods.category_id,goods.image_url
		from
		(<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
		select sku_id,shop_id,cs_nick,date,
		receive_num,
		enquiry_num,
		pay_num,
		pay_goods_num,
		pay_amount
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="csNickList!=null and csNickList.size>0">
				and cs_nick in
				<foreach collection="csNickList" item = "nick" separator="," open="(" close=")">
					#{nick}
				</foreach>
			</if>
			<if test="skuLst!=null and skuLst.size>0">
				AND sku_id in
				<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
					#{skuId}
				</foreach>
			</if>
			AND date between #{startDate} and #{endDate}
		</where>
	</foreach>
		) consult left join ${goodsSkuTable} goods on consult.sku_id = goods.sku_id
		group by consult.sku_id
		order by consult.sku_id
	</select>


	<select id="selectGoodsConsultSummaryCountByDateBySkuIdByCsNickV3" resultMap="GoodsConsultSummaryV2DTO">
		select
		consult.sku_id,consult.shop_id,consult.cs_nick,consult.date,
		sum(consult.receive_num) receive_num,
		sum(consult.enquiry_num) enquiry_num,
		sum(consult.pay_num) pay_num,
		sum(consult.pay_goods_num) pay_goods_num,
		sum(consult.pay_amount) pay_amount,
		goods.id,goods.sku_name,goods.category_id,goods.image_url
		from
		(
		<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
			select
			<choose>
				<when test="categoryId != null and categoryId.size > 0">
					gs.sku_id, gs.shop_id, consult.cs_nick, consult.date,
					consult.receive_num,
					consult.enquiry_num,
					consult.pay_num,
					consult.pay_goods_num,
					consult.pay_amount
				</when>
				<otherwise>
					sku_id, shop_id, cs_nick, date,
					receive_num,
					enquiry_num,
					pay_num,
					pay_goods_num,
					pay_amount
				</otherwise>
			</choose>
			from ${table.getTableName}
			<if test="categoryId != null and categoryId.size > 0">
				consult inner join
				(
				select sku_id, shop_id, category_id
				from ${goodsSkuTable}
				<where>
					<if test="shopId != null">
						shop_id = #{shopId}
					</if>
					<if test="categoryId != null and categoryId.size > 0">
						and category_id in
						<foreach collection="categoryId" item="catId" separator="," open="(" close=")">
							#{catId}
						</foreach>
					</if>
				</where>
				) gs on consult.shop_id = gs.shop_id and consult.sku_id = gs.sku_id
			</if>
			<where>
				<if test="shopId!=null">
					<if test="categoryId != null and categoryId.size > 0">consult.shop_id</if>
					<if test="categoryId == null or categoryId.size == 0">shop_id</if>
					= #{shopId}
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and
					<if test="categoryId != null and categoryId.size > 0">consult.cs_nick</if>
					<if test="categoryId == null or categoryId.size == 0">cs_nick</if>
					in
					<foreach collection="csNickList" item="nick" separator="," open="(" close=")">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					AND
					<if test="categoryId != null and categoryId.size > 0">consult.sku_id</if>
					<if test="categoryId == null or categoryId.size == 0">sku_id</if>
					in
					<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
						#{skuId}
					</foreach>
				</if>
				AND
				<if test="categoryId != null and categoryId.size > 0">consult.date</if>
				<if test="categoryId == null or categoryId.size == 0">date</if>
				between #{startDate} and #{endDate}
			</where>
		</foreach>
		) consult left join ${goodsSkuTable} goods on consult.sku_id = goods.sku_id
		group by consult.sku_id
		order by consult.sku_id
	</select>


	<select id="selectGoodsConsultSummaryByShopIdByDateByskuId" resultMap="GoodsConsultSummaryDTO">
  	select
		goods.sku_name,consult.date,
		consult.cs_nick,consult.consult_num,
		consult.purchases_buyer_num,
		consult.purchases_goods_num,
		consult.purchases_amount
	from
	(<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
		select
			consult_num,cs_nick,sku_id,date,
			purchases_buyer_num,
			purchases_goods_num,
			purchases_amount
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
		    <if test="skuId!=null">
		  		 AND sku_id = #{skuId}
		  	</if>
		  	<if test="csNickList!=null and csNickList.size>0">
		    	and cs_nick in
		   		<foreach collection="csNickList" item = "nick" separator="," open="(" close=")">
		   			#{nick}
		   		</foreach>
		    </if>
		  	AND date between #{startDate} and #{endDate}
		</where>
	</foreach>
	) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
  </select>


	<select id="selectGoodsConsultSummaryByShopIdByDateByskuIdV3" resultMap="GoodsConsultSummaryDTO">
		select
		goods.sku_name,consult.date,
		consult.cs_nick,consult.consult_num,
		consult.purchases_buyer_num,
		consult.purchases_goods_num,
		consult.purchases_amount
		from(
		<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
			<if test="categoryId!=null and categoryId.size>0">
				select
				t.consult_num,t.cs_nick,t.sku_id,t.shop_id,t.date,
				t.purchases_buyer_num,
				t.purchases_goods_num,
				t.purchases_amount
				from ${table.getTableName} t
				left join (
				select * from ${goodsSkuTables} where shop_id = #{shopId}
				) gs on t.sku_id = gs.sku_id
				<where>
					<if test="shopId!=null">
						t.shop_id = #{shopId}
					</if>
					<if test="skuId!=null">
						AND t.sku_id = #{skuId}
					</if>
					<if test="csNickList!=null and csNickList.size>0">
						and t.cs_nick in
						<foreach collection="csNickList" item="nick" separator="," open="(" close=")">
							#{nick}
						</foreach>
					</if>
					<if test="categoryId!=null and categoryId.size>0">
						and gs.category_id in
						<foreach collection="categoryId" item="catId" separator="," open="(" close=")">
							#{catId}
						</foreach>
					</if>
					AND t.date between #{startDate} and #{endDate}
				</where>
			</if>
			<if test="categoryId==null or categoryId.size==0">
				select
				consult_num,cs_nick,sku_id,shop_id,date,
				purchases_buyer_num,
				purchases_goods_num,
				purchases_amount
				from ${table.getTableName}
				<where>
					<if test="shopId!=null">
						shop_id = #{shopId}
					</if>
					<if test="skuId!=null">
						AND sku_id = #{skuId}
					</if>
					<if test="csNickList!=null and csNickList.size>0">
						and cs_nick in
						<foreach collection="csNickList" item="nick" separator="," open="(" close=")">
							#{nick}
						</foreach>
					</if>
					AND date between #{startDate} and #{endDate}
				</where>
			</if>
		</foreach>
		) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
	</select>


	<select id="selectGoodsConsultSummaryByShopIdByDateByskuIdV2" resultMap="GoodsConsultSummaryV2DTO">
		select
		goods.sku_name,consult.date,
		consult.cs_nick,
		consult.receive_num,
		consult.enquiry_num,
		consult.pay_num,
		consult.pay_goods_num,
		consult.pay_amount
		from
		(<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
		select
		receive_num,enquiry_num,cs_nick,sku_id,date,
		pay_num,
		pay_goods_num,
		pay_amount
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="skuId!=null">
				AND sku_id = #{skuId}
			</if>
			<if test="csNickList!=null and csNickList.size>0">
				and cs_nick in
				<foreach collection="csNickList" item = "nick" separator="," open="(" close=")">
					#{nick}
				</foreach>
			</if>
			AND date between #{startDate} and #{endDate}
		</where>
	</foreach>
		) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
	</select>

    <select id="selectGoodsConsultSummaryByShopIdByDateByskuIds" resultMap="GoodsConsultSummaryDTO">
        select
        goods.sku_name,consult.date,
        consult.cs_nick,consult.consult_num,
        consult.purchases_buyer_num,
        consult.purchases_goods_num,
        consult.purchases_amount
        from
        (<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
        select
        consult_num,cs_nick,sku_id,date,
        purchases_buyer_num,
        purchases_goods_num,
        purchases_amount
        from ${table.getTableName}
        <where>
            <if test="shopId!=null">
                shop_id = #{shopId}
            </if>
            <if test="skuIds!=null and skuIds.size>0">
                AND sku_id in
                <foreach collection="skuIds" item = "skuId" separator="," open="(" close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test="csNickList!=null and csNickList.size>0">
                and cs_nick in
                <foreach collection="csNickList" item = "nick" separator="," open="(" close=")">
                    #{nick}
                </foreach>
            </if>
            AND date between #{startDate} and #{endDate}
        </where>
    </foreach>
        ) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
    </select>
	<select id="selectGoodsConsultSummaryByShopIdByDateByskuIdsV2" resultMap="GoodsConsultSummaryV2DTO">
		select
		goods.sku_name,consult.date,
		consult.cs_nick,
		consult.receive_num,
		consult.enquiry_num
		consult.pay_num,
		consult.pay_goods_num,
		consult.pay_amount
		from
		(<foreach collection="goodsConsultSummaryTables" item="table" separator="union">
		select
		receive_num,
		enquiry_num,cs_nick,sku_id,date,
		pay_num,
		pay_goods_num,
		pay_amount
		from ${table.getTableName}
		<where>
			<if test="shopId!=null">
				shop_id = #{shopId}
			</if>
			<if test="skuIds!=null and skuIds.size>0">
				AND sku_id in
				<foreach collection="skuIds" item = "skuId" separator="," open="(" close=")">
					#{skuId}
				</foreach>
			</if>
			<if test="csNickList!=null and csNickList.size>0">
				and cs_nick in
				<foreach collection="csNickList" item = "nick" separator="," open="(" close=")">
					#{nick}
				</foreach>
			</if>
			AND date between #{startDate} and #{endDate}
		</where>
	</foreach>
		) consult left join ${goodsSkuTables} goods on consult.sku_id = goods.sku_id
	</select>
</mapper>
