<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopGoodsFeedbackRateMapper" >
  <resultMap id="ShopGoodsFeedbackRateDO" type="com.pes.jd.model.DO.ShopGoodsFeedbackRateDO" >
  <id column="id" property="id" jdbcType="BIGINT" />
  <result column="shop_id" property="shopId" jdbcType="BIGINT" />
  <result column="date" property="date" jdbcType="DATE" />
  <result column="goods_id" property="goodsId" jdbcType="BIGINT" />
  <result column="good_rate" property="goodRate" jdbcType="DOUBLE" />
  <result column="type" property="type" jdbcType="TINYINT" />
</resultMap>
  <resultMap id="ShopGoodsFeedbackRateDTO" type="com.pes.jd.model.DTO.ShopGoodsFeedbackRateDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="goods_id" property="goodsId" jdbcType="BIGINT" />
    <result column="good_rate" property="goodRate" jdbcType="DOUBLE" />
    <result column="type" property="type" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, date, goods_id, good_rate, type
  </sql>
  <select id="selectShopGoodsFeedbackByShopIdByDateByGoodsByType"  resultMap="ShopGoodsFeedbackRateDTO">
    select 
     ra.id,
     ra.shop_id,
     ra.date,
     ra.goods_id,
     ra.good_rate,
     ra.type
    FROM
        (
          <foreach collection="tableNames" item="tn" separator="union all">
              SELECT
              id, shop_id, date, goods_id, good_rate, type
              FROM ${tn.tableName}
              WHERE shop_id=#{shopId}
              AND date between #{tn.beginDate} and #{tn.endDate}
                <if test="param.type!=null">
                  and type=#{param.type}
                </if>
                <if test="param.goodsIds!=null and param.goodsIds.size()>0">
                  and goods_id in
                  <foreach collection="param.goodsIds" item="goodsId" open="(" close=")" separator=",">
                    #{goodsId}
                  </foreach>
                </if>
          </foreach>
        ) ra

    <if test="sortPageQuery.sort and sortPageQuery.field!=null and sortPageQuery.field!='' ">
      ORDER BY ${sortPageQuery.field} ${sortPageQuery.sortDirection}
    </if>
    <if test="sortPageQuery.currentPage != null and sortPageQuery.size >0">
      LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
    </if>
  </select>

  <select id="selectCountShopGoodsFeedbackByShopIdByDateByGoodsByType"  resultType="java.lang.Integer" >
    select
    count(id)
    from ${tableName}
    where shop_id=#{shopId}
    and date between #{startDate} and #{endDate}
    <if test="param.type!=null">
      and type=#{param.type}
    </if>
    <if test="param.goodsIds!=null and param.goodsIds.size()>0">
      and goods_id in
      <foreach collection="param.goodsIds" item="goodsId" open="(" close=")" separator=",">
        #{goodsId}
      </foreach>
    </if>
  </select>
</mapper>