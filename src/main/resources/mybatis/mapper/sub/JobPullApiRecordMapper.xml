<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.JobPullApiRecordMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.JobPullApiRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="result" jdbcType="TINYINT" property="result" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>
  <resultMap id="jobPullApiRecordVO" type="com.pes.jd.model.VO.JobPullApiRecordVO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, date, result, type, modified, msg
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_job_pull_api_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_job_pull_api_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.JobPullApiRecordDO">
    insert into pes_job_pull_api_record (id, shop_id, date, 
      result, type, modified, msg
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{result,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}, #{modified,jdbcType=TIMESTAMP}, #{msg,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.JobPullApiRecordDO">
    insert into pes_job_pull_api_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="modified != null">
        modified,
      </if>
      <if test="msg != null">
        msg,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="result != null">
        #{result,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="modified != null">
        #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.JobPullApiRecordDO">
    update pes_job_pull_api_record
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="modified != null">
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.JobPullApiRecordDO">
    update pes_job_pull_api_record
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      result = #{result,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      modified = #{modified,jdbcType=TIMESTAMP},
      msg = #{msg,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="searchJobPullApiRecordRecord" parameterType="map" resultMap="jobPullApiRecordVO">
    SELECT shop_id,date,modified,msg,result
    FROM ${tableName}
    WHERE
    date = #{date,jdbcType=DATE}
    AND type = #{type,jdbcType=TINYINT}
    AND shop_id = #{shopId,jdbcType=BIGINT}
    AND result = 0
  </select>

  <select id="searchJobPullApiRecordRecordBySchemaId" parameterType="map" resultMap="jobPullApiRecordVO">
    SELECT shop_id,date,modified,msg,result
    FROM ${tableName}
    WHERE
    date = #{date,jdbcType=DATE}
    AND type = #{type,jdbcType=TINYINT}
    AND result = 0
  </select>
</mapper>