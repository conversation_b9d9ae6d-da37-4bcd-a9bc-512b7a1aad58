<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopCategoryMapper" >
  <resultMap id="ShopCategoryDTO" type="com.pes.jd.model.DTO.ShopCategoryDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="category_id" property="categoryId" jdbcType="BIGINT" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="level" property="level" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
  </resultMap>
  <sql id="base_filed" >
    id, shop_id, name, category_id, parent_id, level, status
  </sql>

   <select id="selectShopCategoryByShopId" resultMap="ShopCategoryDTO" parameterType="map" >
    SELECT
    	shop_id, name, category_id, parent_id, level, status
    FROM ${tableName}
	    WHERE shop_id = #{shopId,jdbcType=BIGINT}
	    AND status=1
  </select>

  <select id="selectCategoryIdByShopIdByParentId" resultMap="ShopCategoryDTO" parameterType="map" >
    SELECT
    	 shop_id, name, category_id, parent_id, level, status
	FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND parent_id in
		<foreach collection="parentLst" item="parentId" close=")" open="(" separator=",">
			#{parentId}
		</foreach>
	    AND status=1
  </select>
</mapper>
