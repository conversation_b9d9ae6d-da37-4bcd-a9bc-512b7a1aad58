<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsOrderEvaluateMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsOrderEvaluateDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="TIMESTAMP" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="good_evaluate_num_total" jdbcType="INTEGER" property="goodEvaluateNumTotal"/>
        <result column="neutral_evaluate_num_total" jdbcType="INTEGER" property="neutralEvaluateNumTotal"/>
        <result column="bad_evaluate_num_total" jdbcType="INTEGER" property="badEvaluateNumTotal"/>
        <result column="good_evaluate_num_pre_sale" jdbcType="INTEGER" property="goodEvaluateNumPreSale"/>
        <result column="neutral_evaluate_num_pre_sale" jdbcType="INTEGER" property="neutralEvaluateNumPreSale"/>
        <result column="bad_evaluate_num_pre_sale" jdbcType="INTEGER" property="badEvaluateNumPreSale"/>
        <result column="good_evaluate_num_bet_sale" jdbcType="INTEGER" property="goodEvaluateNumBetSale"/>
        <result column="neutral_evaluate_num_bet_sale" jdbcType="INTEGER" property="neutralEvaluateNumBetSale"/>
        <result column="bad_evaluate_num_bet_sale" jdbcType="INTEGER" property="badEvaluateNumBetSale"/>
        <result column="good_evaluate_num_after_sale" jdbcType="INTEGER" property="goodEvaluateNumAfterSale"/>
        <result column="neutral_evaluate_num_after_sale" jdbcType="INTEGER" property="neutralEvaluateNumAfterSale"/>
        <result column="bad_evaluate_num_after_sale" jdbcType="INTEGER" property="badEvaluateNumAfterSale"/>
    </resultMap>
    <resultMap id="CsOrderEvaluateDTO" type="com.pes.jd.model.DTO.CsOrderEvaluateDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="TIMESTAMP" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="good_evaluate_num_total" jdbcType="INTEGER" property="goodEvaluateNumTotal"/>
        <result column="neutral_evaluate_num_total" jdbcType="INTEGER" property="neutralEvaluateNumTotal"/>
        <result column="bad_evaluate_num_total" jdbcType="INTEGER" property="badEvaluateNumTotal"/>
        <result column="good_evaluate_num_pre_sale" jdbcType="INTEGER" property="goodEvaluateNumPreSale"/>
        <result column="neutral_evaluate_num_pre_sale" jdbcType="INTEGER" property="neutralEvaluateNumPreSale"/>
        <result column="bad_evaluate_num_pre_sale" jdbcType="INTEGER" property="badEvaluateNumPreSale"/>
        <result column="good_evaluate_num_bet_sale" jdbcType="INTEGER" property="goodEvaluateNumBetSale"/>
        <result column="neutral_evaluate_num_bet_sale" jdbcType="INTEGER" property="neutralEvaluateNumBetSale"/>
        <result column="bad_evaluate_num_bet_sale" jdbcType="INTEGER" property="badEvaluateNumBetSale"/>
        <result column="good_evaluate_num_after_sale" jdbcType="INTEGER" property="goodEvaluateNumAfterSale"/>
        <result column="neutral_evaluate_num_after_sale" jdbcType="INTEGER" property="neutralEvaluateNumAfterSale"/>
        <result column="bad_evaluate_num_after_sale" jdbcType="INTEGER" property="badEvaluateNumAfterSale"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, good_evaluate_num_total, neutral_evaluate_num_total, 
    bad_evaluate_num_total, good_evaluate_num_pre_sale, neutral_evaluate_num_pre_sale, 
    bad_evaluate_num_pre_sale, good_evaluate_num_bet_sale, neutral_evaluate_num_bet_sale, 
    bad_evaluate_num_bet_sale, good_evaluate_num_after_sale, neutral_evaluate_num_after_sale, 
    bad_evaluate_num_after_sale
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pes_cs_order_evaluate
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_order_evaluate
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.pes.jd.model.DO.CsOrderEvaluateDO">
    insert into pes_cs_order_evaluate (id, shop_id, date, 
      cs_nick, good_evaluate_num_total, neutral_evaluate_num_total, 
      bad_evaluate_num_total, good_evaluate_num_pre_sale, 
      neutral_evaluate_num_pre_sale, bad_evaluate_num_pre_sale, 
      good_evaluate_num_bet_sale, neutral_evaluate_num_bet_sale, 
      bad_evaluate_num_bet_sale, good_evaluate_num_after_sale, 
      neutral_evaluate_num_after_sale, bad_evaluate_num_after_sale
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=TIMESTAMP}, 
      #{csNick,jdbcType=VARCHAR}, #{goodEvaluateNumTotal,jdbcType=INTEGER}, #{neutralEvaluateNumTotal,jdbcType=INTEGER}, 
      #{badEvaluateNumTotal,jdbcType=INTEGER}, #{goodEvaluateNumPreSale,jdbcType=INTEGER}, 
      #{neutralEvaluateNumPreSale,jdbcType=INTEGER}, #{badEvaluateNumPreSale,jdbcType=INTEGER}, 
      #{goodEvaluateNumBetSale,jdbcType=INTEGER}, #{neutralEvaluateNumBetSale,jdbcType=INTEGER}, 
      #{badEvaluateNumBetSale,jdbcType=INTEGER}, #{goodEvaluateNumAfterSale,jdbcType=INTEGER}, 
      #{neutralEvaluateNumAfterSale,jdbcType=INTEGER}, #{badEvaluateNumAfterSale,jdbcType=INTEGER}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsOrderEvaluateDO">
        insert into pes_cs_order_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="shopId != null">
                shop_id,
            </if>
            <if test="date != null">
                date,
            </if>
            <if test="csNick != null">
                cs_nick,
            </if>
            <if test="goodEvaluateNumTotal != null">
                good_evaluate_num_total,
            </if>
            <if test="neutralEvaluateNumTotal != null">
                neutral_evaluate_num_total,
            </if>
            <if test="badEvaluateNumTotal != null">
                bad_evaluate_num_total,
            </if>
            <if test="goodEvaluateNumPreSale != null">
                good_evaluate_num_pre_sale,
            </if>
            <if test="neutralEvaluateNumPreSale != null">
                neutral_evaluate_num_pre_sale,
            </if>
            <if test="badEvaluateNumPreSale != null">
                bad_evaluate_num_pre_sale,
            </if>
            <if test="goodEvaluateNumBetSale != null">
                good_evaluate_num_bet_sale,
            </if>
            <if test="neutralEvaluateNumBetSale != null">
                neutral_evaluate_num_bet_sale,
            </if>
            <if test="badEvaluateNumBetSale != null">
                bad_evaluate_num_bet_sale,
            </if>
            <if test="goodEvaluateNumAfterSale != null">
                good_evaluate_num_after_sale,
            </if>
            <if test="neutralEvaluateNumAfterSale != null">
                neutral_evaluate_num_after_sale,
            </if>
            <if test="badEvaluateNumAfterSale != null">
                bad_evaluate_num_after_sale,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="shopId != null">
                #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                #{date,jdbcType=TIMESTAMP},
            </if>
            <if test="csNick != null">
                #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="goodEvaluateNumTotal != null">
                #{goodEvaluateNumTotal,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumTotal != null">
                #{neutralEvaluateNumTotal,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumTotal != null">
                #{badEvaluateNumTotal,jdbcType=INTEGER},
            </if>
            <if test="goodEvaluateNumPreSale != null">
                #{goodEvaluateNumPreSale,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumPreSale != null">
                #{neutralEvaluateNumPreSale,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumPreSale != null">
                #{badEvaluateNumPreSale,jdbcType=INTEGER},
            </if>
            <if test="goodEvaluateNumBetSale != null">
                #{goodEvaluateNumBetSale,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumBetSale != null">
                #{neutralEvaluateNumBetSale,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumBetSale != null">
                #{badEvaluateNumBetSale,jdbcType=INTEGER},
            </if>
            <if test="goodEvaluateNumAfterSale != null">
                #{goodEvaluateNumAfterSale,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumAfterSale != null">
                #{neutralEvaluateNumAfterSale,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumAfterSale != null">
                #{badEvaluateNumAfterSale,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="searchDateShop" resultMap="CsOrderEvaluateDTO">
        select
        id, shop_id, date, cs_nick,
        sum(good_evaluate_num_total) good_evaluate_num_total ,
        sum(neutral_evaluate_num_total) neutral_evaluate_num_total ,
        sum(bad_evaluate_num_total) bad_evaluate_num_total ,
        sum(good_evaluate_num_pre_sale) good_evaluate_num_pre_sale ,
        sum(neutral_evaluate_num_pre_sale) neutral_evaluate_num_pre_sale ,
        sum(bad_evaluate_num_pre_sale) bad_evaluate_num_pre_sale ,
        sum(good_evaluate_num_bet_sale) good_evaluate_num_bet_sale ,
        sum(neutral_evaluate_num_bet_sale) neutral_evaluate_num_bet_sale ,
        sum(bad_evaluate_num_bet_sale) bad_evaluate_num_bet_sale ,
        sum(good_evaluate_num_after_sale) good_evaluate_num_after_sale ,
        sum(neutral_evaluate_num_after_sale) neutral_evaluate_num_after_sale ,
        sum(bad_evaluate_num_after_sale) bad_evaluate_num_after_sale
        from ${tableName}
        where date between #{startDate} and #{endDate}
        <if test="nicks!=null and nicks.size()>0">
            and cs_nick in
            <foreach collection="nicks" item="nick" close=")" open="(" separator=",">
                #{nick}
            </foreach>
        </if>
        <if test="filterDates != null and filterDates.size()>0">
            and date not in
            <foreach collection="filterDates" open="(" close=")" separator="," item="d">
                #{d}
            </foreach>
        </if>
        and shop_id = #{shopId}
        group by ${groupBy}
        order by date desc

    </select>
    <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsOrderEvaluateDO">
        update pes_cs_order_evaluate
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=TIMESTAMP},
            </if>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="goodEvaluateNumTotal != null">
                good_evaluate_num_total = #{goodEvaluateNumTotal,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumTotal != null">
                neutral_evaluate_num_total = #{neutralEvaluateNumTotal,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumTotal != null">
                bad_evaluate_num_total = #{badEvaluateNumTotal,jdbcType=INTEGER},
            </if>
            <if test="goodEvaluateNumPreSale != null">
                good_evaluate_num_pre_sale = #{goodEvaluateNumPreSale,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumPreSale != null">
                neutral_evaluate_num_pre_sale = #{neutralEvaluateNumPreSale,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumPreSale != null">
                bad_evaluate_num_pre_sale = #{badEvaluateNumPreSale,jdbcType=INTEGER},
            </if>
            <if test="goodEvaluateNumBetSale != null">
                good_evaluate_num_bet_sale = #{goodEvaluateNumBetSale,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumBetSale != null">
                neutral_evaluate_num_bet_sale = #{neutralEvaluateNumBetSale,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumBetSale != null">
                bad_evaluate_num_bet_sale = #{badEvaluateNumBetSale,jdbcType=INTEGER},
            </if>
            <if test="goodEvaluateNumAfterSale != null">
                good_evaluate_num_after_sale = #{goodEvaluateNumAfterSale,jdbcType=INTEGER},
            </if>
            <if test="neutralEvaluateNumAfterSale != null">
                neutral_evaluate_num_after_sale = #{neutralEvaluateNumAfterSale,jdbcType=INTEGER},
            </if>
            <if test="badEvaluateNumAfterSale != null">
                bad_evaluate_num_after_sale = #{badEvaluateNumAfterSale,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsOrderEvaluateDO">
    update pes_cs_order_evaluate
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=TIMESTAMP},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      good_evaluate_num_total = #{goodEvaluateNumTotal,jdbcType=INTEGER},
      neutral_evaluate_num_total = #{neutralEvaluateNumTotal,jdbcType=INTEGER},
      bad_evaluate_num_total = #{badEvaluateNumTotal,jdbcType=INTEGER},
      good_evaluate_num_pre_sale = #{goodEvaluateNumPreSale,jdbcType=INTEGER},
      neutral_evaluate_num_pre_sale = #{neutralEvaluateNumPreSale,jdbcType=INTEGER},
      bad_evaluate_num_pre_sale = #{badEvaluateNumPreSale,jdbcType=INTEGER},
      good_evaluate_num_bet_sale = #{goodEvaluateNumBetSale,jdbcType=INTEGER},
      neutral_evaluate_num_bet_sale = #{neutralEvaluateNumBetSale,jdbcType=INTEGER},
      bad_evaluate_num_bet_sale = #{badEvaluateNumBetSale,jdbcType=INTEGER},
      good_evaluate_num_after_sale = #{goodEvaluateNumAfterSale,jdbcType=INTEGER},
      neutral_evaluate_num_after_sale = #{neutralEvaluateNumAfterSale,jdbcType=INTEGER},
      bad_evaluate_num_after_sale = #{badEvaluateNumAfterSale,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>


    <select id="selectByShoIdAndDateForShopPerformance" resultType="com.pes.jd.model.DTO.CsOrderEvaluateDTO">
        SELECT
        `shop_id`,
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                `date` AS dateStr,

            </when>
            <!--月份-->
            <when test="dateType == 2">
                DATE_FORMAT( `date`, '%Y-%m' ) AS dateStr,
            </when>
            <otherwise>
            </otherwise>
        </choose>
        sum(neutral_evaluate_num_total) neutral_evaluate_num_total ,
        sum(bad_evaluate_num_total) bad_evaluate_num_total
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--时间维度-->
        <choose>
            <!--日期-->
            <when test="dateType == 1">
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY `date`
            </when>
            <!--月份-->
            <when test="dateType == 2">
                AND DATE_FORMAT(`date`,'%Y-%m') IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </when>
            <otherwise>
                AND `date` IN
                <foreach collection="dates" item="dt" open="(" close=")" separator=",">
                    #{dt}
                </foreach>
                GROUP BY shop_id
            </otherwise>
        </choose>

    </select>
</mapper>