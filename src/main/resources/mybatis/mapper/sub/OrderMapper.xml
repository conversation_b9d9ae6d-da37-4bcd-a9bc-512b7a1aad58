<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.OrderMapper">

    <resultMap id="DealAnalysisVo" type="com.pes.jd.model.VO.DealAnalysisVo">
        <id column="orderId" property="orderId" jdbcType="BIGINT"/>
        <result column="csNick" property="csNick" jdbcType="VARCHAR"/>
        <result column="shopId" property="shopId" jdbcType="BIGINT"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="payTime" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="buyerNick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="OrderDTO" type="com.pes.jd.model.DTO.OrderDTO">
        <id column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="seller_nick" property="sellerNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="post_fee" property="postFee" jdbcType="DOUBLE"/>
        <result column="consign_time" property="consignTime" jdbcType="TIMESTAMP"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="total_fee" property="totalFee" jdbcType="DOUBLE"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="seller_flag" property="sellerFlag" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="step_trade_status" property="stepTradeStatus" jdbcType="VARCHAR"/>
        <result column="step_paid_fee" property="stepPaidFee" jdbcType="DOUBLE"/>
        <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" jdbcType="TINYINT" property="payType"/>
        <result column="seller_discount" jdbcType="DOUBLE" property="sellerDiscount"/>
        <result column="order_type" jdbcType="TINYINT" property="orderType"/>
        <result column="modify_address_flag" jdbcType="TINYINT" property="modifyAddressFlag"/>
        <result column="direct_trade_id" jdbcType="BIGINT" property="directTradeId"/>
    </resultMap>

    <resultMap id="AddressDTO" type="com.pes.jd.model.DTO.AddressDTO">
        <id column="shopId" property="shopId" jdbcType="BIGINT"/>
        <id column="orderId" property="orderId" jdbcType="BIGINT"/>
        <result column="phone_code" property="customerPhone" jdbcType="VARCHAR"/>
        <result column="cityId" property="cityId" jdbcType="BIGINT"/>
        <result column="town_id" property="townId" jdbcType="BIGINT"/>
        <result column="province_id" property="provinceId" jdbcType="BIGINT"/>
        <result column="country_id" property="countyId" jdbcType="BIGINT"/>
        <result column="city_name" property="cityName" jdbcType="BIGINT"/>
        <result column="country_name" property="countyName" jdbcType="BIGINT"/>
        <result column="town_name" property="townName" jdbcType="BIGINT"/>
        <result column="fullname" property="fullName" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="GoodsDTO" type="com.pes.jd.model.DTO.GoodsDTO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <id column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <id column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <id column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <id column="item_sku_id" property="itemSkuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="item_price" property="itemPrice" jdbcType="BIGINT"/>
        <result column="item_num" property="itemNum" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="base_field">
		trade_id,
		order_id,
		shop_id,
		seller_nick,
		date,
		payment,
		post_fee,
		consign_time,
		num,
		status,
		total_fee,
		created,
		pay_time,
		modified,
		end_time,
		buyer_nick,
		seller_flag,
		type,
		step_trade_status,
		step_paid_fee,
		out_stock_time,
		pay_type,
		order_type
	</sql>


    <select id="selectAddressInfoByOrderIdAndShopId" resultMap="AddressDTO">
        select
        orderId,
        shopId,
        cityId,
        town_id,
        province_id,
        country_id,
        phone_code,
        city_name,
        country_name,
        town_name,
        fullname
        from ${tableName}
        where shopId=#{shopId}
        and orderId
        in
        <foreach collection="orderId" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="selectGoodsDetailInfoBySkuId" resultMap="GoodsDTO">
        select ware_id,sku_id,sku_name,image_url from pes_jd_sub_01.pes_shop_goods_sku where sku_id
        in
        <foreach collection="skuId" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
    </select>

    <select id="selectGoodSInfoByOrderId" resultMap="GoodsDTO">
        SELECT
        order_id,
        shop_id,
        item_sku_id,
        item_price,
        item_num from ${tableName}
        where order_id in
        <foreach collection="orderId" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <select id="selectGoodsDetailInfoByOrderId" resultMap="GoodsDTO">
        select item_num,item_price,item_sku_Id from #{tableName}
        where shop_id=#{shopId}
        and order_id in
        <foreach collection="orderId" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </select>

    <insert id="persistOrderByFile" parameterType="java.util.Map">
        load data local infile #{filePath} into table ${tableName}
        fields terminated by '``MYPES`' optionally enclosed by '' escaped by ''
        lines terminated by '`MYPES`\n'
        (order_id, trade_id, seller_nick, date, shop_id, payment, post_fee, num, status, total_fee, created, pay_time,
        modified, end_time, type, buyer_nick);
    </insert>

    <delete id="deleteOrdersByTids" parameterType="java.util.Map">
        DELETE FROM ${tableName}
        WHERE trade_id IN
        <foreach collection="tids" index="index" item="tid" open="(" separator="," close=")">
            #{tid}
        </foreach>
    </delete>

    <select id="selectOrderInfoByOrderIds" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT
        <include refid="base_field"/>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <choose>
            <when test="orderIds != null and orderIds.size() > 0">
                <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                (0)
            </otherwise>
        </choose>
    </select>

    <select id="selectFieldsByOrderIds" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT
        order_id,pay_type,
        <if test="fields != null and fields.trim.length > 0">
            ${fields}
        </if>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <choose>
            <when test="orderIds != null and orderIds.size() > 0">
                <foreach collection="orderIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                (0)
            </otherwise>
        </choose>
    </select>

    <select id="selectIdsByTradeIds" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT id FROM ${tableName}
        WHERE trade_id IN
        <foreach collection="trades" index="index" item="item" open="(" separator="," close=")">
            #{item.tid}
        </foreach>
    </select>

    <insert id="persistOrders" parameterType="java.util.Map">
        INSERT INTO ${tableName}(id,shop_id, date, seller_nick, payment,post_fee,consign_time,
        trade_id,num,status,total_fee,created,pay_time,
        modified,end_time,buyer_nick,seller_flag)
        VALUES
        <foreach collection="orders" item="itm" separator="," index="index">
            (#{itm.id},#{itm.shopId},#{itm.date},#{itm.sellerNick},#{itm.payment},#{itm.postFee},#{itm.consignTime},#{itm.tradeId},#{itm.num},#{itm.status},#{itm.totalFee},#{itm.createdTime},#{itm.payTime},#{itm.modified},#{itm.endTime},#{itm.buyerNick},#{itm.sellerFlag})
        </foreach>
    </insert>

    <select id="getSilenceSaleTradeByDateAndBuyNickAndShopId" resultMap="DealAnalysisVo" parameterType="map">
        SELECT
        order_id as orderId, shop_id as shopId, payment as payment,
        num, created, pay_time as payTime, buyer_nick as buyerNick
        FROM ${orderTableName}
        WHERE
        pay_time BETWEEN #{startDate} AND #{endDate}
        AND shop_id = #{shopId}
        <if test="buyerNick != null and buyerNick!=''">
            AND buyer_nick = #{buyerNick}
        </if>
        AND order_id
        not in (
        SELECT
        order_id
        FROM ${csOrderBindTableName}
        WHERE
        order_created BETWEEN #{startDate} AND #{endDate}
        AND shop_id = #{shopId}
        AND is_pes_order = 1
        )
        ORDER BY pay_time DESC
        <if test="start!=-1">
            LIMIT #{start}, #{length}
        </if>
    </select>


    <select id="getSilenceSaleTradeCount" resultType="java.lang.Integer" parameterType="map">
        SELECT
        count(order_id)
        FROM ${orderTableName}
        WHERE
        pay_time BETWEEN #{startDate} AND #{endDate}
        AND shop_id = #{shopId}
        <if test="buyerNick != null and buyerNick!=''">
            AND buyer_nick = #{buyerNick}
        </if>
        AND order_id
        not in (
        SELECT
        order_id
        FROM ${csOrderBindTableName}
        WHERE
        order_pay_date BETWEEN #{startDate} AND #{endDate}
        AND shop_id = #{shopId}
        AND is_pes_order = 1
        )
    </select>

    <select id="selectShopDealTradesByPage" parameterType="map"
            resultType="com.pes.jd.model.VO.DealAnalysisVo">
        SELECT t1.cs_nick as csNick, t1.shop_id as shopId, t1.buyer_nick as
        buyerNick, t1.created as created, t1.pay_time as payTime, t1.payment
        as payment, t1.order_id as orderId, t1.num as num, t1.date as date
        FROM (
        SELECT ct.cs_nick, ct.shop_id, ct.type, t.buyer_nick, t.created,
        t.pay_time, t.payment, t.order_id, ct.date, t.num
        FROM
        ${orderTableName} t
        LEFT JOIN
        (
        SELECT temp.type, temp.shop_id, temp.order_id, temp.cs_nick
        ,temp.order_pay_date, temp.date
        FROM ${csOrderBindTableName} temp
        WHERE
        temp.shop_id = #{shopId}
        AND is_pes_order = 1
        AND temp.order_pay_date between #{startDate} and #{endDate}
        AND temp.cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")"
                 separator=",">
            #{nick}
        </foreach>
        ) ct
        ON ct.order_id = t.order_id
        WHERE
        t.shop_id = #{shopId}
        <if test='buyerNick != null and buyerNick != ""'>
            AND t.buyer_nick = #{buyerNick}
        </if>
        AND t.pay_time between #{startDate} and #{endDate}
        ) t1
        ORDER BY
        t1.pay_time DESC
        <if test="start!=-1">
            LIMIT #{start}, #{length}
        </if>
    </select>


    <select id="getShopDealTradesCount" parameterType="map"
            resultType="java.lang.Integer">
        SELECT count(t1.order_id)
        FROM (
        SELECT ct.cs_nick, ct.shop_id, ct.type, t.buyer_nick, t.created,
        t.pay_time, t.payment, t.order_id, ct.date, t.num
        FROM
        ${orderTableName} t
        LEFT JOIN
        (
        SELECT temp.type, temp.shop_id, temp.order_id, temp.cs_nick
        ,temp.order_pay_date, temp.date
        FROM ${csOrderBindTableName} temp
        WHERE
        temp.shop_id = #{shopId}
        AND is_pes_order = 1
        AND temp.order_pay_date between #{startDate} and #{endDate}
        AND temp.cs_nick in
        <foreach collection="nicks" item="nick" open="(" close=")"
                 separator=",">
            #{nick}
        </foreach>
        ) ct
        ON ct.order_id = t.order_id
        WHERE
        t.shop_id = #{shopId}
        <if test='buyerNick != null and buyerNick != ""'>
            AND t.buyer_nick = #{buyerNick}
        </if>
        AND t.pay_time between #{startDate} and #{endDate}
        ) t1
    </select>

    <select id="selectCountByShopIdAndDateAndOrderIdAndBuyerNick" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND created BETWEEN #{beginDate} AND #{endDate}
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <if test="buyerNick != null and buyerNick != ''">
            AND buyer_nick = #{buyerNick}
        </if>
        <if test="orderType != null">
            <choose>
                <when test="orderType == 0">
                    AND order_type IN (0, 2)
                </when>
                <otherwise>
                    AND order_type = #{orderType}
                </otherwise>
            </choose>
        </if>
        <if test="parentOrderId !=null and parentOrderId.size()>0 ">
            and order_id not in
            <foreach collection="parentOrderId" item="pid" index="index"
                     open="(" close=")" separator=",">
                #{pid}
            </foreach>
        </if>
    </select>
    <select id="selectPageByShopIdAndDateAndOrderIdAndBuyerNick" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT
        order_id,payment,status,created,pay_time,buyer_nick,out_stock_time,pay_type,order_type
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND created BETWEEN #{beginDate} AND #{endDate}
        <if test="orderId != null">
            AND order_id = #{orderId}
        </if>
        <if test="buyerNick != null and buyerNick != ''">
            AND buyer_nick = #{buyerNick}
        </if>
        <if test="orderType != null">
            <choose>
                <when test="orderType == 0">
                    AND order_type IN (0, 2)
                </when>
                <otherwise>
                    AND order_type = #{orderType}
                </otherwise>
            </choose>
        </if>
        <if test="parentOrderId !=null and parentOrderId.size()>0 ">
            and order_id not in
            <foreach collection="parentOrderId" item="pid" index="index"
                     open="(" close=")" separator=",">
                #{pid}
            </foreach>
        </if>
        ORDER BY created DESC
        <if test="startIndex != null and length != null and length > 0">
            LIMIT #{startIndex}, #{length}
        </if>
    </select>


    <select id="selectOrderStatusByOrderIdList" parameterType="map" resultMap="OrderDTO">
        SELECT *
        FROM ${tableName}
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND shop_id = #{shopId}
    </select>

    <select id="selectByOrderIdsAndOutStatusForRefundDataAnalysis" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT
        order_id, payment, pay_time, out_stock_time,pay_type,created
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        <!--订单编号-->
        <if test="orderIds != null and orderIds.size() > 0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        <choose>
            <!--日期-->
<!--            <when test="dateType == 1">-->
<!--                AND created BETWEEN #{startDate} AND #{endDate}-->
<!--            </when>-->
            <!--订单下单时间-->
            <when test="dateType == 3">
                AND created BETWEEN #{startDate} AND #{endDate}
            </when>
            <!--订单付款时间-->
            <when test="dateType == 4">
                AND pay_time BETWEEN #{startDate} AND #{endDate}
            </when>
        </choose>
        <choose>
            <!--出库状态-->
            <!--未出库-->
            <when test='outStatus == "1"'>
                AND (out_stock_time = '0000-00-00 00:00:00' OR out_stock_time IS NULL)
            </when>
            <!--已出库-->
            <when test='outStatus == "2"'>
                AND out_stock_time IS NOT NULL AND out_stock_time != '0000-00-00 00:00:00'
            </when>
        </choose>
    </select>


    <select id="selectSilenceOrderCountForSilenceSaleAnalysis" resultType="int">

        SELECT COUNT(1)
        FROM (
        <foreach collection="orderTables" item="orderTable" separator="UNION">
            SELECT order_id
            FROM ${orderTable.tableName}
            WHERE shop_id = #{shopId}
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="buyerNick != null and buyerNick.trim().length() > 0">
                AND buyer_nick = #{buyerNick}
            </if>
            AND (pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND created BETWEEN #{startDate} AND
            #{endDate}))
            <if test="orderIds!=null and orderIds.size()>0">
                AND order_id NOT IN
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </foreach>
        ) o
        WHERE o.order_id NOT IN (
        <foreach collection="bindTables" item="bindTable" separator="UNION">
            SELECT order_id FROM ${bindTable.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                AND shop_id = #{shopId}
                <if test="buyerNick != null and buyerNick.trim.length() > 0">
                    AND buyer_nick = #{buyerNick}
                </if>
                AND is_pes_order = 1
                AND (order_valid_pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
                #{startDate} AND #{endDate}))
            </where>
        </foreach>
        <if test="orderType == 0">
            UNION
            <foreach collection="indexTables" item="indexTable" separator="UNION">
                SELECT DISTINCT order_id
                FROM ${indexTable.tableName}
                <where>
                    <if test="orderId != null">
                        AND order_id = #{orderId}
                    </if>
                    <if test="shopId != null">
                        AND shop_id = #{shopId,jdbcType=BIGINT}
                    </if>
                    <if test="buyerNick != null and buyerNick != ''">
                        AND buyer_nick = #{buyerNick,jdbcType=VARCHAR}
                    </if>
                    AND (order_pay_date BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
                    #{startDate} AND #{endDate}))
                    AND order_flag != 0
                    <if test="orderIds!=null and orderIds.size()>0">
                        AND order_id NOT IN
                        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                            #{orderId}
                        </foreach>
                    </if>
                </where>
            </foreach>
        </if>
        )

    </select>
    <select id="selectOrderIdsForSilenceSaleAnalysis" resultType="long">
        SELECT order_id
        FROM (
        <foreach collection="orderTables" item="orderTable" separator="UNION">
            SELECT order_id, created
            FROM ${orderTable.tableName}
            WHERE shop_id = #{shopId}
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="buyerNick != null and buyerNick.trim().length() > 0">
                AND buyer_nick = #{buyerNick}
            </if>
            AND (pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND created BETWEEN #{startDate} AND #{endDate}))
            <if test="orderIds!=null and orderIds.size()>0">
                AND order_id NOT IN
                <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </foreach>
        ) o
        WHERE o.order_id NOT IN (
        <foreach collection="bindTables" item="bindTable" separator="UNION">
            SELECT order_id FROM ${bindTable.tableName}
            <where>
                <if test="orderId != null">
                    AND order_id = #{orderId}
                </if>
                AND shop_id = #{shopId}
                <if test="buyerNick != null and buyerNick.trim().length() > 0">
                    AND buyer_nick = #{buyerNick}
                </if>
                AND is_pes_order = 1
                AND (order_valid_pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
                #{startDate} AND #{endDate}))
            </where>
        </foreach>
        <if test="orderType == 0">
            UNION
            <foreach collection="indexTables" item="indexTable" separator="UNION">
                SELECT DISTINCT order_id
                FROM ${indexTable.tableName}
                <where>
                    <if test="orderId != null">
                        AND order_id = #{orderId}
                    </if>
                    <if test="shopId != null">
                        AND shop_id = #{shopId,jdbcType=BIGINT}
                    </if>
                    <if test="buyerNick != null and buyerNick != ''">
                        AND buyer_nick = #{buyerNick,jdbcType=VARCHAR}
                    </if>
                    AND (order_pay_date BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND order_created BETWEEN
                    #{startDate} AND #{endDate}))
                    AND order_flag != 0
                    <if test="orderIds!=null and orderIds.size()>0">
                        AND order_id NOT IN
                        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                            #{orderId}
                        </foreach>
                    </if>
                </where>
            </foreach>
        </if>
        )
        ORDER BY o.created ASC
        <if test="sortPageQuery != null and sortPageQuery.size > 0">
            Limit #{sortPageQuery.currentPage}, #{sortPageQuery.size}
        </if>
    </select>

    <select id="selectOrderByShopIdByDateByOrderLst" resultMap="OrderDTO">
        select order_id,out_stock_time
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and date between #{startDate} and #{endDate}
            and order_id in
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </where>
    </select>
    <select id="selectParentOrderByShopIdAndDateAndBuyerNick" resultMap="OrderDTO">
        SELECT
        order_id,direct_trade_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND created BETWEEN #{beginDate} AND #{endDate}
        <if test="buyerNick != null and buyerNick != ''">
            AND buyer_nick = #{buyerNick}
        </if>
        <if test="orderType != null">
            <choose>
                <when test="orderType == 0">
                    AND order_type IN (0, 2)
                </when>
                <otherwise>
                    AND order_type = #{orderType}
                </otherwise>
            </choose>
        </if>
        AND direct_trade_id > 0
    </select>

    <select id="selectParentOrderByShopIdAndDateAndBuyerNick1" resultMap="OrderDTO">
        SELECT
        order_id, trade_id, direct_trade_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND pay_time BETWEEN #{beginDate} AND #{endDate}
        <if test="buyerNick != null and buyerNick != ''">
            AND buyer_nick = #{buyerNick}
        </if>
        AND order_type = #{orderType}
        AND direct_trade_id > 0
    </select>
    <select id="selectParentOrderForCsSaleAnalysis" resultMap="OrderDTO">
        SELECT
        trade_id,order_id,shop_id,seller_nick,date,payment,post_fee,consign_time,
        num,status,total_fee,created,pay_time,modified,end_time,buyer_nick,
        seller_flag,type,step_trade_status,step_paid_fee,out_stock_time,
        pay_type,seller_discount,order_type,direct_trade_id
        FROM ${tableName}
        <where>
            <if test="buyerNick != null and buyerNick != ''">
                AND buyer_nick = #{buyerNick}
            </if>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            AND direct_trade_id > 0
            AND (
            (created BETWEEN #{startDate} AND #{endDate})
            OR (pay_time BETWEEN #{startDate} AND #{endDate})
            )
        </where>
    </select>
    <select id="selectParentOrderForCsSaleAnalysis2" resultMap="OrderDTO">
        SELECT
        direct_trade_id
        FROM ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="buyerNick != null and buyerNick != ''">
                AND buyer_nick = #{buyerNick}
            </if>
            AND direct_trade_id > 0
            AND created BETWEEN #{startDate} AND #{endDate}
        </where>
        UNION ALL
        SELECT
        direct_trade_id
        FROM ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="buyerNick != null and buyerNick != ''">
                AND buyer_nick = #{buyerNick}
            </if>
            AND direct_trade_id > 0
            AND pay_time BETWEEN #{startDate} AND #{endDate}
        </where>
    </select>

</mapper>