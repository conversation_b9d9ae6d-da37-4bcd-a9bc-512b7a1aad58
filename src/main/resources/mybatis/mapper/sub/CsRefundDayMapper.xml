<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsRefundDayMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsRefundDayDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="apply_refund_num" jdbcType="INTEGER" property="applyRefundNum" />
    <result column="apply_refund_product_num" jdbcType="INTEGER" property="applyRefundProductNum" />
    <result column="apply_refund_buyer_num" jdbcType="INTEGER" property="applyRefundBuyerNum" />
    <result column="apply_refund_amount" jdbcType="DOUBLE" property="applyRefundAmount" />
    <result column="completed_refund_num" jdbcType="INTEGER" property="completedRefundNum" />
    <result column="completed_refund_product_num" jdbcType="INTEGER" property="completedRefundProductNum" />
    <result column="completed_refund_buyer_num" jdbcType="INTEGER" property="completedRefundBuyerNum" />
    <result column="completed_refund_amount" jdbcType="DOUBLE" property="completedRefundAmount" />
    <result column="total_refund_duration" jdbcType="BIGINT" property="totalRefundDuration" />
  </resultMap>
  <resultMap id="CsRefundDayDTO" type="com.pes.jd.model.DTO.CsRefundDayDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="apply_refund_num" jdbcType="INTEGER" property="applyRefundNum" />
    <result column="apply_refund_product_num" jdbcType="INTEGER" property="applyRefundProductNum" />
    <result column="apply_refund_buyer_num" jdbcType="INTEGER" property="applyRefundBuyerNum" />
    <result column="apply_refund_amount" jdbcType="DOUBLE" property="applyRefundAmount" />
    <result column="completed_refund_num" jdbcType="INTEGER" property="completedRefundNum" />
    <result column="completed_refund_product_num" jdbcType="INTEGER" property="completedRefundProductNum" />
    <result column="completed_refund_buyer_num" jdbcType="INTEGER" property="completedRefundBuyerNum" />
    <result column="completed_refund_amount" jdbcType="DOUBLE" property="completedRefundAmount" />
    <result column="total_refund_duration" jdbcType="BIGINT" property="totalRefundDuration" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, cs_nick, date, apply_refund_num, apply_refund_product_num, apply_refund_buyer_num, 
    apply_refund_amount, completed_refund_num, completed_refund_product_num, completed_refund_buyer_num, 
    completed_refund_amount, total_refund_duration
  </sql>
  <sql id="field_for_performance">
   id, shop_id, cs_nick, date,
   (apply_refund_num) apply_refund_num ,
   (apply_refund_product_num) apply_refund_product_num ,
   (apply_refund_buyer_num) apply_refund_buyer_num ,
   (apply_refund_amount) apply_refund_amount ,
   (completed_refund_num) completed_refund_num ,
   (completed_refund_product_num) completed_refund_product_num ,
   (completed_refund_buyer_num) completed_refund_buyer_num ,
   (completed_refund_amount) completed_refund_amount ,
   (total_refund_duration) total_refund_duration
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_cs_refund_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchAllDateShopGroup" resultMap="CsRefundDayDTO">
    select
    <include refid="field_for_performance"/>
    from ${tableName}
    where date between #{startDate} and #{endDate}
    <if test="nicks!=null and nicks.size()>0">
      and cs_nick in
      <foreach collection="nicks" item="nick" close=")" open="(" separator=",">
        #{nick}
      </foreach>
    </if>
    <if test="filterDates != null and filterDates.size()>0">
      and date not in
      <foreach collection="filterDates" open="(" close=")" separator="," item="d">
        #{d}
      </foreach>
    </if>
    and shop_id  = #{shopId}
<!--    group by ${groupBy}-->
    order by date desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_refund_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CsRefundDayDO">
    insert into pes_cs_refund_day (id, shop_id, cs_nick, 
      date, apply_refund_num, apply_refund_product_num, 
      apply_refund_buyer_num, apply_refund_amount, 
      completed_refund_num, completed_refund_product_num, 
      completed_refund_buyer_num, completed_refund_amount, 
      total_refund_duration)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, 
      #{date,jdbcType=DATE}, #{applyRefundNum,jdbcType=INTEGER}, #{applyRefundProductNum,jdbcType=INTEGER}, 
      #{applyRefundBuyerNum,jdbcType=INTEGER}, #{applyRefundAmount,jdbcType=DOUBLE}, 
      #{completedRefundNum,jdbcType=INTEGER}, #{completedRefundProductNum,jdbcType=INTEGER}, 
      #{completedRefundBuyerNum,jdbcType=INTEGER}, #{completedRefundAmount,jdbcType=DOUBLE}, 
      #{totalRefundDuration,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsRefundDayDO">
    insert into pes_cs_refund_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="applyRefundNum != null">
        apply_refund_num,
      </if>
      <if test="applyRefundProductNum != null">
        apply_refund_product_num,
      </if>
      <if test="applyRefundBuyerNum != null">
        apply_refund_buyer_num,
      </if>
      <if test="applyRefundAmount != null">
        apply_refund_amount,
      </if>
      <if test="completedRefundNum != null">
        completed_refund_num,
      </if>
      <if test="completedRefundProductNum != null">
        completed_refund_product_num,
      </if>
      <if test="completedRefundBuyerNum != null">
        completed_refund_buyer_num,
      </if>
      <if test="completedRefundAmount != null">
        completed_refund_amount,
      </if>
      <if test="totalRefundDuration != null">
        total_refund_duration,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="applyRefundNum != null">
        #{applyRefundNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundProductNum != null">
        #{applyRefundProductNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundBuyerNum != null">
        #{applyRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundAmount != null">
        #{applyRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="completedRefundNum != null">
        #{completedRefundNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundProductNum != null">
        #{completedRefundProductNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundBuyerNum != null">
        #{completedRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundAmount != null">
        #{completedRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="totalRefundDuration != null">
        #{totalRefundDuration,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsRefundDayDO">
    update pes_cs_refund_day
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="applyRefundNum != null">
        apply_refund_num = #{applyRefundNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundProductNum != null">
        apply_refund_product_num = #{applyRefundProductNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundBuyerNum != null">
        apply_refund_buyer_num = #{applyRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundAmount != null">
        apply_refund_amount = #{applyRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="completedRefundNum != null">
        completed_refund_num = #{completedRefundNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundProductNum != null">
        completed_refund_product_num = #{completedRefundProductNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundBuyerNum != null">
        completed_refund_buyer_num = #{completedRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundAmount != null">
        completed_refund_amount = #{completedRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="totalRefundDuration != null">
        total_refund_duration = #{totalRefundDuration,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsRefundDayDO">
    update pes_cs_refund_day
    set shop_id = #{shopId,jdbcType=BIGINT},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      date = #{date,jdbcType=DATE},
      apply_refund_num = #{applyRefundNum,jdbcType=INTEGER},
      apply_refund_product_num = #{applyRefundProductNum,jdbcType=INTEGER},
      apply_refund_buyer_num = #{applyRefundBuyerNum,jdbcType=INTEGER},
      apply_refund_amount = #{applyRefundAmount,jdbcType=DOUBLE},
      completed_refund_num = #{completedRefundNum,jdbcType=INTEGER},
      completed_refund_product_num = #{completedRefundProductNum,jdbcType=INTEGER},
      completed_refund_buyer_num = #{completedRefundBuyerNum,jdbcType=INTEGER},
      completed_refund_amount = #{completedRefundAmount,jdbcType=DOUBLE},
      total_refund_duration = #{totalRefundDuration,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>