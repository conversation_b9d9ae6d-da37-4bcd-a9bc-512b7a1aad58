<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopGoodSkuMapper">
    <resultMap id="ShopGoodSkuDO" type="com.pes.jd.model.DO.ShopGoodSkuDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ware_name" property="wareName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="stock_num" property="stockNum" jdbcType="INTEGER"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="ShopGoodSkuDTO" type="com.pes.jd.model.DTO.ShopGoodsSkuDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ware_name" property="wareName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="stock_num" property="stockNum" jdbcType="INTEGER"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="GoodsConsultSummaryDTO" type="com.pes.jd.model.DTO.GoodsConsultSummaryDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="consult_num" property="consultNum" jdbcType="INTEGER"/>
        <result column="purchases_buyer_num" property="purchasesBuyerNum" jdbcType="INTEGER"/>
        <result column="purchases_goods_num" property="purchasesGoodsNum" jdbcType="INTEGER"/>
    </resultMap>
    <resultMap id="ShopGoodNameDTO" type="com.pes.jd.model.DTO.ShopGoodNameDTO" >
        <result column="sku_name" property="name" jdbcType="VARCHAR" />
        <result column="sku_id" property="goodsId" jdbcType="BIGINT" />
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR" />
    </resultMap>
    <resultMap id="ShopGoodSkuDTO2" type="com.pes.jd.model.DTO.ShopGoodsSkuDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="base_field">
    id, sku_name, ware_name, price, ware_id, sku_id, status, stock_num, category_id,
    image_url, shop_id
  </sql>
    <select id="selectShopGoodSkuById" resultMap="ShopGoodSkuDO" parameterType="java.lang.Long">
        select
        <include refid="base_field"/>
        from pes_shop_goods_sku
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectGoodsConsultSummary" resultMap="GoodsConsultSummaryDTO" parameterType="java.lang.Long">
	select
		goods.id,goods.sku_name,goods.price,goods.ware_id,goods.sku_id,
		summ.consult_num,summ.purchases_buyer_num,summ.purchases_goods_num
	from pes_jd_sub1.pes_shop_goods_sku goods
	INNER JOIN pes_jd_sub1.pes_goods_consult_summary summ ON summ.shop_id = goods.shop_id
    where summ.shop_id = #{shopId,jdbcType=BIGINT}
  </select>
    <delete id="deleteShopGoodSkuById" parameterType="java.lang.Long">
    delete from pes_shop_goods_sku
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insertShopGoodSku" parameterType="com.pes.jd.model.DO.ShopGoodSkuDO">
    insert into pes_shop_goods_sku (id, sku_name, ware_name,
      price, ware_id, sku_id,
      status, stock_num, category_id,
      image_url, shop_id)
    values (#{id,jdbcType=BIGINT}, #{skuName,jdbcType=VARCHAR}, #{wareName,jdbcType=VARCHAR},
      #{price,jdbcType=DOUBLE}, #{wareId,jdbcType=BIGINT}, #{skuId,jdbcType=BIGINT},
      #{status,jdbcType=TINYINT}, #{stockNum,jdbcType=INTEGER}, #{categoryId,jdbcType=BIGINT},
      #{imageUrl,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT})
  </insert>
    <update id="updateShopGoodSku" parameterType="com.pes.jd.model.DO.ShopGoodSkuDO">
        update pes_shop_goods_sku
        <set>
            <if test="skuName != null">
                sku_name = #{skuName,jdbcType=VARCHAR},
            </if>
            <if test="wareName != null">
                ware_name = #{wareName,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DOUBLE},
            </if>
            <if test="wareId != null">
                ware_id = #{wareId,jdbcType=BIGINT},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="stockNum != null">
                stock_num = #{stockNum,jdbcType=INTEGER},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="imageUrl != null">
                image_url = #{imageUrl,jdbcType=VARCHAR},
            </if>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectCountShopGoods" resultType="int">
        select count(id) from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="("
                         close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (sku_name like concat(concat('%',#{skuName}),'%')
                or sku_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
            <if test="skuIdLst!=null and skuIdLst.size()>0">
                AND sku_id not in
                <foreach collection="skuIdLst" item="skuId" open="(" close=")"
                         separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectCountShopGoodsForGoods" resultType="int">
        select
        count(1)
        from (
        select
        id,
        shop_id,
        sku_name,
        sku_id,
        status,
        category_id,
        image_url,
        ware_id
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (sku_name like concat(concat('%',#{skuName}),'%')
                or ware_name like concat(concat('%',#{skuName}),'%')
                or sku_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
            <if test="excludeSkuIds!=null and excludeSkuIds.size()>0">
                AND sku_id not in
                <foreach collection="excludeSkuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="includeSkuIds!=null and includeSkuIds.size()>0">
                AND sku_id in
                <foreach collection="includeSkuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
        ) a left join ${joinTableName} b on a.sku_id = b.sku_id
    </select>

    <select id="selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus" resultMap="ShopGoodSkuDTO">
        select
        id,
        shop_id,
        sku_name,
        sku_id,
        status,
        category_id,
        image_url,
        ware_id
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (sku_name like concat(concat('%',#{skuName}),'%')
                or sku_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
            <if test="skuIdLst!=null and skuIdLst.size()>0">
                AND sku_id not in
                <foreach collection="skuIdLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
        LIMIT #{pageNum} , #{pageSize}
    </select>


    <select id="selectShopGoodsSkuIdLstByCategoryIdByStatus" resultMap="ShopGoodSkuDTO">
        select
        sku_id,
        category_id
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
        </where>
    </select>




    <select id="selectShopGoodsSkuLstByShopIdBySkuIdByWareId" resultMap="ShopGoodSkuDTO">
        select
        id,
        shop_id,
        sku_name,
        sku_id,
        status,
        category_id,
        image_url,
        ware_id
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="wareIdSet!=null and wareIdSet.size()>0">
                AND ware_id in
                <foreach collection="wareIdSet" item="wareId" open="(" close=")" separator=",">
                    #{wareId}
                </foreach>
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
            <if test="skuIdLst!=null and skuIdLst.size()>0">
                AND sku_id not in
                <foreach collection="skuIdLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectShopGoodsSkuLstBySkuIdLst" parameterType="map" resultMap="ShopGoodSkuDTO">
        select
        id,
        shop_id,
        sku_name,
        sku_id,
        status,
        category_id,
        image_url
        from ${tableName}
        where
        shop_id = #{shopId}
        AND sku_id in
        <foreach collection="skuIdLst" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="selectBySkuIdsForRefundDataAnalysis" resultMap="ShopGoodSkuDTO">
        SELECT sku_id, sku_name
        FROM ${tableName}
        <where>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
            <if test="skuIds != null and skuIds.size() > 0">
                AND sku_id IN
                <foreach collection="skuIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectShopGoodsSkuByShopIdBySkuId" resultMap="ShopGoodSkuDTO">
        SELECT
        sku_id,sku_name
        FROM
        ${tableName}
        WHERE
        shop_id=#{shopId}
        AND sku_id IN
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="selectShopGoodsSkuByShopIdForLossOrder" resultMap="ShopGoodSkuDTO">
        SELECT
        sku_id,sku_name,ware_name
        FROM
        ${tableName}
        WHERE
        shop_id=#{shopId}
    </select>

    <select id="selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusForGoods" resultMap="ShopGoodSkuDTO">
        select
        a.id,
        a.shop_id,
        a.sku_name,
        a.sku_id,
        a.status,
        a.price,
        a.stock_num,
        a.category_id,
        a.image_url,
        a.ware_id,
        b.sku_id is not null as recommend
        from
        (
        select
        id,
        shop_id,
        sku_name,
        sku_id,
        price,
        status,
        stock_num,
        category_id,
        image_url,
        ware_id
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND ( ware_name like concat(concat('%',#{skuName}),'%')
                or sku_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
            <if test="includeSkuIds!=null and includeSkuIds.size()>0">
                AND sku_id in
                <foreach collection="includeSkuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
        )
        a left join ${joinTableName} b on a.sku_id = b.sku_id
        order by
        <if test="topSku != null and topSku.size() != 0">
            case a.sku_id
            <foreach collection="topSku" item="sku">
                when #{sku} then 0
            </foreach>
            else 1
            end asc ,
        </if>
        <if test="propertity != null and propertity != ''
        and sortDirection != null and sortDirection != ''
         and propertity == 'price' and sortDirection == 'asc' or sortDirection == 'desc'">
            ${propertity} ${sortDirection} ,
        </if>
        b.sku_id desc
    </select>

    <select id="selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods" resultMap="ShopGoodSkuDTO">
        select
        a.id,
        a.shop_id,
        a.sku_name,
        a.sku_id,
        a.status,
        a.price,
        a.stock_num,
        a.category_id,
        a.image_url,
        a.ware_id,
        b.sku_id is not null as recommend
        from
        (
        select
        id,
        shop_id,
        sku_name,
        sku_id,
        price,
        status,
        stock_num,
        category_id,
        image_url,
        ware_id
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (sku_name like concat(concat('%',#{skuName}),'%')
                or ware_name like concat(concat('%',#{skuName}),'%')
                or sku_id like concat(concat('%',#{skuName}),'%')
                or ware_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <if test="status!=null and status!=''">
                AND status=#{status}
            </if>
            <if test="includeSkuIds!=null and includeSkuIds.size()>0">
                AND sku_id in
                <foreach collection="includeSkuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="excludeSkuIds!=null and excludeSkuIds.size()>0">
                AND sku_id not in
                <foreach collection="excludeSkuIds" item="excludeSku" open="(" close=")" separator=",">
                    #{excludeSku}
                </foreach>
            </if>
        </where>
        )
        a left join ${joinTableName} b on a.sku_id = b.sku_id
        order by
        <if test="topSku != null and topSku.size() != 0">
            case a.sku_id
            <foreach collection="topSku" item="sku">
                when #{sku} then 0
            </foreach>
            else 1
            end asc ,
        </if>
        <if test="propertity != null and propertity != ''
        and sortDirection != null and sortDirection != ''
         and propertity == 'price' and sortDirection == 'asc' or sortDirection == 'desc'">
            ${propertity} ${sortDirection} ,
        </if>
        b.sku_id desc
    </select>

     <select id="queryShopGoodsInfoBySkuIdAndShopId" resultMap="ShopGoodSkuDTO">
        select
        	shop_id,  sku_name,ware_name,price,ware_id,sku_id,status,stock_num,category_id,image_url
         from ${tableName}
        where shop_id=#{shopId}
        and sku_id=#{skuId}
    </select>




    <select id="selectShopSkuByShopId" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku">
            select
            sku_name as skuName, price as price, sku_id as skuId, shop_id as shopId
            from ${tableName}
            where shop_id=#{shopId}
            and status = 1
    </select>

    <select id="selectShopGoodsSkuBySkuLst" resultMap="ShopGoodSkuDTO">
    	SELECT
    		shop_id,sku_name,sku_id,status
    	FROM ${tableName}
    	<where>
    			shop_id = #{shopId}
    			AND sku_id in
    			<foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
    				#{skuId}
    			</foreach>

    	</where>
    </select>

    <select id="selectShopGoodsSkuNameBySkuIdLst" parameterType="map" resultMap="ShopGoodNameDTO">
        select sku_name , sku_id,image_url
        from ${tableName}
        where sku_id in
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>
    <select id="selectSkuIdsByWareId" resultType="java.lang.Long">
        select distinct sku_id
        from ${tableName}
        where shop_id=#{shopId}
        and ware_id=#{wareId}
</select>
    <select id="selectShopGoodsSkuByShopIdByWareIds" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku">
        select   ware_id as wareId,sku_id  as skuId,ware_name as wareName,sku_name as skuName
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and ware_id in
            <foreach collection="wareIds" item="wareId" open="(" close=")" separator=",">
                #{wareId}
            </foreach>
        </where>
    </select>

    <select id="selectShopGoodsSkuIdsByWareIdLst" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku">
        select   ware_id as wareId,sku_id  as skuId
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and ware_id in
            <foreach collection="wareIds" item="wareId" open="(" close=")" separator=",">
                #{wareId}
            </foreach>
        </where>
    </select>


      <select id="selectShopSkuByShopIdAndSkuId"  resultType="com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku">
          select
            sku_name as skuName, price as price, sku_id as skuId, shop_id as shopId
            from ${tableName}
    	<where>
    			shop_id =#{shopId}
            <if test="skuLst!=null and skuLst.size()>0">
                AND sku_id in
                <foreach collection="skuLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>

    	</where>
    </select>

    <select id="selectShopSkuByShopIdAndskuName" resultMap="ShopGoodSkuDTO2">
        select sku_id,sku_name,shop_id
        from ${tableName}
        <where>
            <if test="shopId!=null">
                shop_id= #{shopId}
            </if>
            <if test="skuName!=null">
                and sku_name like CONCAT('%',#{skuName},'%')
            </if>
        </where>
    </select>
    <select id="selectShopSkuBySpu" resultType="com.pes.jd.ms.domain.Data.shopdata.ShopGoodsSku">
        select
        sku_id, ware_id
        from ${tableName}
        where
        shop_id = #{shopId}
        and ware_id in
        <foreach collection="goodsIdList" item="spu" open="(" close=")" separator=",">
            #{spu}
        </foreach>
    </select>
</mapper>
