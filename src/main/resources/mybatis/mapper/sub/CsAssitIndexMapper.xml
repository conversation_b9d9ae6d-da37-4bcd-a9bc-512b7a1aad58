<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsAssitIndexMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsAssitIndexDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="assit_order_create_num" jdbcType="INTEGER" property="assitOrderCreateNum" />
    <result column="assit_order_create_amount" jdbcType="DOUBLE" property="assitOrderCreateAmount" />
    <result column="assit_order_pay_num" jdbcType="INTEGER" property="assitOrderPayNum" />
    <result column="assit_order_pay_amount" jdbcType="DOUBLE" property="assitOrderPayAmount" />
    <result column="assit_order_followup_num" jdbcType="INTEGER" property="assitOrderFollowupNum" />
    <result column="assit_order_followup_amount" jdbcType="DOUBLE" property="assitOrderFollowupAmount" />
  </resultMap>
  <resultMap id="CsAssitIndexDTO" type="com.pes.jd.model.DTO.CsAssitIndexDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="assit_order_create_num" jdbcType="INTEGER" property="assitOrderCreateNum" />
    <result column="assit_order_create_amount" jdbcType="DOUBLE" property="assitOrderCreateAmount" />
    <result column="assit_order_pay_num" jdbcType="INTEGER" property="assitOrderPayNum" />
    <result column="assit_order_pay_amount" jdbcType="DOUBLE" property="assitOrderPayAmount" />
    <result column="assit_order_followup_num" jdbcType="INTEGER" property="assitOrderFollowupNum" />
    <result column="assit_order_followup_amount" jdbcType="DOUBLE" property="assitOrderFollowupAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, assit_order_create_num, assit_order_create_amount, assit_order_pay_num, 
    assit_order_pay_amount, assit_order_followup_num, assit_order_followup_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_cs_assit_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="searchByDateShopNicks" resultMap="CsAssitIndexDTO">
    select
    <include refid="Base_Column_List"/>
    from ${tableName}
    where date between #{startDate} and #{endDate}
    <if test="nicks!=null and nicks.size()>0">
      and cs_nick in
      <foreach collection="nicks" separator="," open="(" close=")" item="nick">
        #{nick}
      </foreach>
    </if>
    <if test="filterDates != null and filterDates.size()>0">
      and date not in
      <foreach collection="filterDates" open="(" close=")" separator="," item="d">
        #{d}
      </foreach>
    </if>
    order by date desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_cs_assit_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.CsAssitIndexDO">
    insert into pes_cs_assit_index (id, shop_id, date, 
      cs_nick, assit_order_create_num, assit_order_create_amount, 
      assit_order_pay_num, assit_order_pay_amount, assit_order_followup_num, 
      assit_order_followup_amount)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=TIMESTAMP}, 
      #{csNick,jdbcType=VARCHAR}, #{assitOrderCreateNum,jdbcType=INTEGER}, #{assitOrderCreateAmount,jdbcType=DOUBLE}, 
      #{assitOrderPayNum,jdbcType=INTEGER}, #{assitOrderPayAmount,jdbcType=DOUBLE}, #{assitOrderFollowupNum,jdbcType=INTEGER}, 
      #{assitOrderFollowupAmount,jdbcType=DOUBLE})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.CsAssitIndexDO">
    insert into pes_cs_assit_index
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="assitOrderCreateNum != null">
        assit_order_create_num,
      </if>
      <if test="assitOrderCreateAmount != null">
        assit_order_create_amount,
      </if>
      <if test="assitOrderPayNum != null">
        assit_order_pay_num,
      </if>
      <if test="assitOrderPayAmount != null">
        assit_order_pay_amount,
      </if>
      <if test="assitOrderFollowupNum != null">
        assit_order_followup_num,
      </if>
      <if test="assitOrderFollowupAmount != null">
        assit_order_followup_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="assitOrderCreateNum != null">
        #{assitOrderCreateNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderCreateAmount != null">
        #{assitOrderCreateAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderPayNum != null">
        #{assitOrderPayNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderPayAmount != null">
        #{assitOrderPayAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderFollowupNum != null">
        #{assitOrderFollowupNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderFollowupAmount != null">
        #{assitOrderFollowupAmount,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.CsAssitIndexDO">
    update pes_cs_assit_index
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="assitOrderCreateNum != null">
        assit_order_create_num = #{assitOrderCreateNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderCreateAmount != null">
        assit_order_create_amount = #{assitOrderCreateAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderPayNum != null">
        assit_order_pay_num = #{assitOrderPayNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderPayAmount != null">
        assit_order_pay_amount = #{assitOrderPayAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderFollowupNum != null">
        assit_order_followup_num = #{assitOrderFollowupNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderFollowupAmount != null">
        assit_order_followup_amount = #{assitOrderFollowupAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.CsAssitIndexDO">
    update pes_cs_assit_index
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=TIMESTAMP},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      assit_order_create_num = #{assitOrderCreateNum,jdbcType=INTEGER},
      assit_order_create_amount = #{assitOrderCreateAmount,jdbcType=DOUBLE},
      assit_order_pay_num = #{assitOrderPayNum,jdbcType=INTEGER},
      assit_order_pay_amount = #{assitOrderPayAmount,jdbcType=DOUBLE},
      assit_order_followup_num = #{assitOrderFollowupNum,jdbcType=INTEGER},
      assit_order_followup_amount = #{assitOrderFollowupAmount,jdbcType=DOUBLE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>