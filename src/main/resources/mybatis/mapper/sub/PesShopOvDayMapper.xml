<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.PesShopOvDayMapper" >
  <resultMap id="PesShopOvDayDO" type="com.pes.jd.model.DO.PesShopOvDay" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
    <result column="consign_num" property="consignNum" jdbcType="INTEGER" />
    <result column="paid_trade_num" property="paidTradeNum" jdbcType="INTEGER" />
    <result column="paid_order_num" property="paidOrderNum" jdbcType="INTEGER" />
    <result column="paid_buyer_num" property="paidBuyerNum" jdbcType="INTEGER" />
    <result column="created_trade_num" property="createdTradeNum" jdbcType="INTEGER" />
    <result column="created_order_num" property="createdOrderNum" jdbcType="INTEGER" />
    <result column="created_trade_Amount" property="createdTradeAmount" jdbcType="DOUBLE" />
    <result column="cfm_goods_amount" property="cfmGoodsAmount" jdbcType="DOUBLE" />
    <result column="cfm_goods_t_num" property="cfmGoodsTNum" jdbcType="INTEGER" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, sale_amount, consign_num, paid_trade_num, paid_order_num, paid_buyer_num, 
    created_trade_num, created_order_num, created_trade_Amount, cfm_goods_amount, cfm_goods_t_num
  </sql>
 
    <insert id="insertPesShopOvDay" parameterType="com.pes.jd.model.DO.PesShopOvDay" >
	INSERT INTO pes_shop_ov_day (id, shop_id, date,
	sale_amount, consign_num, paid_trade_num,
	paid_order_num, paid_buyer_num, created_trade_num,
	created_order_num, created_trade_Amount, cfm_goods_amount,
	cfm_goods_t_num)
	VALUES 
	(
		#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT},
		#{date,jdbcType=DATE},
		#{saleAmount,jdbcType=DOUBLE}, #{consignNum,jdbcType=INTEGER}, #{paidTradeNum,jdbcType=INTEGER},
		#{paidOrderNum,jdbcType=INTEGER}, #{paidBuyerNum,jdbcType=INTEGER},
		#{createdTradeNum,jdbcType=INTEGER},
		#{createdOrderNum,jdbcType=INTEGER},
		#{createdTradeAmount,jdbcType=DOUBLE},
		#{cfmGoodsAmount,jdbcType=DOUBLE},
		#{cfmGoodsTNum,jdbcType=INTEGER}
	)
  </insert>
  
  <delete id="deletePesShopOvDayById" parameterType="java.lang.Long" >
    DELETE FROM pes_shop_ov_day
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>

  <update id="updatePesShopOvDayBySelective" parameterType="com.pes.jd.model.DO.PesShopOvDay" >
    UPDATE pes_shop_ov_day
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="saleAmount != null" >
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="consignNum != null" >
        consign_num = #{consignNum,jdbcType=INTEGER},
      </if>
      <if test="paidTradeNum != null" >
        paid_trade_num = #{paidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="paidOrderNum != null" >
        paid_order_num = #{paidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="paidBuyerNum != null" >
        paid_buyer_num = #{paidBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="createdTradeNum != null" >
        created_trade_num = #{createdTradeNum,jdbcType=INTEGER},
      </if>
      <if test="createdOrderNum != null" >
        created_order_num = #{createdOrderNum,jdbcType=INTEGER},
      </if>
      <if test="createdTradeAmount != null" >
        created_trade_Amount = #{createdTradeAmount,jdbcType=DOUBLE},
      </if>
      <if test="cfmGoodsAmount != null" >
        cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="cfmGoodsTNum != null" >
        cfm_goods_t_num = #{cfmGoodsTNum,jdbcType=INTEGER},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="getPesShopOvDayById" resultMap="PesShopOvDayDO" parameterType="java.lang.Long" >
    SELECT 
    	<include refid="base_field" />
    FROM pes_shop_ov_day
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>

    <select id="selectShopSaleAmount" resultType="DOUBLE" >
        SELECT
        sum(sale_amount) as shopSaleAmountForMonth
        FROM ${tableName}
        WHERE
          date BETWEEN #{startDate}	AND #{endDate}
  	      and shop_id=#{shopId}
    </select>
  
</mapper>