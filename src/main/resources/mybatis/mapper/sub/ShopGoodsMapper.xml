<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.sub.ShopGoodsMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.ShopGoodsDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="ware_id" property="wareId" jdbcType="BIGINT" />
    <result column="image_url" property="imageUrl" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="stock_num" property="stockNum" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="BIGINT" />
    <result column="article_number" property="articleNumber" jdbcType="VARCHAR" />
  </resultMap>

    <resultMap id="ShopGoodNameDTO" type="com.pes.jd.model.DTO.ShopGoodNameDTO" >
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="ware_id" property="goodsId" jdbcType="BIGINT" />
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="ShopGoodSkuDTO" type="com.pes.jd.model.DTO.ShopGoodsSkuDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="skuName" jdbcType="VARCHAR"/>
        <result column="name" property="wareName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <result column="ware_id" property="skuId" jdbcType="BIGINT"/>
        <result column="article_number" property="articleNumber" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="stock_num" property="stockNum" jdbcType="INTEGER"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
    </resultMap>
  <sql id="Base_Column_List" >
    id, name, price, ware_id,image_url, status, stock_num, category_id, shop_id, article_number
  </sql>
  
   <select id="selectShopGoodsByShopId" resultMap="BaseResultMap" parameterType="map" >
    select 
    m.id, m.name, m.price, m.sku_id, m.image_url, m.status, m.stock_num, m.category_id, m.shop_id, m.article_number
    from ${tableName} m
    inner join
    ${categoryTableName} n
    ON m.category_id = n.category_id
    where
    m.shop_id = #{shopId,jdbcType=VARCHAR}
    <if test="categoryId !=null and categoryId !=''">
        and m.category_id = #{categoryId,jdbcType=BIGINT}
    </if>
     <if test="status !=null and status !=''">
        and m.status = #{status,jdbcType=INTEGER}
    </if>
     <if test="name !=null and name !=''">
        and m.name LIKE '%#{name}%'
    </if>
  </select>

    <select id="selectShopGoodsByShopIdByWareIds" resultMap="ShopGoodNameDTO">
        select   ware_id, name, image_url
        from ${tableName}
        <where>
            shop_id=#{shopId}
            and ware_id in
            <foreach collection="wareIds" item="wareId" open="(" close=")" separator=",">
                #{wareId}
            </foreach>
        </where>
    </select>

    <select id="selectShopGoodsByCategoryIdBySkuNameByStatus" resultMap="ShopGoodNameDTO">
        select
        ware_id, name, image_url
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="wareName!=null and wareName!='' ">
                AND (article_number like concat(concat('%',#{wareName}),'%')
                or name like concat(concat('%',#{wareName}),'%')
                or ware_id like concat(concat('%',#{wareName}),'%'))
            </if>
            <choose>
                <when test="status!=null and status!='' and status=='1'.toString() ">
                    AND status=8
                </when>
                <when test="status!=null and status!='' and status=='2'.toString() ">
                    AND status in (2,4,1028)
                </when>
                <when test="status!=null and status!='' and status=='4'.toString() ">
                    AND status=-1
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="wareIdLst!=null and wareIdLst.size()>0">
                AND ware_id not in
                <foreach collection="wareIdLst" item="wareId" open="(" close=")" separator=",">
                    #{wareId}
                </foreach>
            </if>
        </where>
        LIMIT #{pageNum} , #{pageSize}
    </select>
    <select id="selectShopGoodsSkuLstByCategoryIdBySkuNameByStatusByAddStatusForGoods"
            resultMap="ShopGoodSkuDTO">
        select
        *
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (name like concat(concat('%',#{skuName}),'%')
                or ware_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <choose>
                <when test="status!=null and status!='' and status=='1'.toString() ">
                    AND status=8
                </when>
                <when test="status!=null and status!='' and status=='2'.toString() ">
                    AND status in (2,4,1028)
                </when>
                <when test="status!=null and status!='' and status=='4'.toString() ">
                    AND status=-1
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="includeSkuIds!=null and includeSkuIds.size()>0">
                AND ware_id in
                <foreach collection="includeSkuIds" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
            <if test="excludeSkuIds!=null and excludeSkuIds.size()>0">
                AND ware_id not in
                <foreach collection="excludeSkuIds" item="excludeSku" open="(" close=")" separator=",">
                    #{excludeSku}
                </foreach>
            </if>
        </where>
        order by
        <if test="propertity != null and propertity != ''
        and sortDirection != null and sortDirection != ''
         and propertity == 'price' and sortDirection == 'asc' or sortDirection == 'desc'">
            ${propertity} ${sortDirection} ,
        </if>
        ware_id desc
    </select>
    <select id="selectShopGoodsSkuLstByCategoryIdBySkuNameByStatus" resultMap="ShopGoodSkuDTO">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (name like concat(concat('%',#{skuName}),'%')
                or ware_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <choose>
                <when test="status!=null and status!='' and status=='1'.toString() ">
                    AND status=8
                </when>
                <when test="status!=null and status!='' and status=='2'.toString() ">
                    AND status in (2,4,1028)
                </when>
                <when test="status!=null and status!='' and status=='4'.toString() ">
                    AND status=-1
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="skuIdLst!=null and skuIdLst.size()>0">
                AND ware_id not in
                <foreach collection="skuIdLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
        LIMIT #{pageNum} , #{pageSize}
    </select>

    <select id="selectCountShopGoods" resultType="int">
        select
        count(1)
        from ${tableName}
        <where>
            shop_id=#{shopId}
            <if test="categoryLst!=null and categoryLst.size()>0">
                AND category_id in
                <foreach collection="categoryLst" item="categoryId" open="(" close=")" separator=",">
                    #{categoryId}
                </foreach>
            </if>
            <if test="skuName!=null and skuName!='' ">
                AND (name like concat(concat('%',#{skuName}),'%')
                or ware_id like concat(concat('%',#{skuName}),'%'))
            </if>
            <choose>
                <when test="status!=null and status!='' and status=='1'.toString() ">
                    AND status=8
                </when>
                <when test="status!=null and status!='' and status=='2'.toString() ">
                    AND status in (2,4,1028)
                </when>
                <when test="status!=null and status!='' and status=='4'.toString() ">
                    AND status=-1
                </when>
                <otherwise>
                </otherwise>
            </choose>
            <if test="skuIdLst!=null and skuIdLst.size()>0">
                AND ware_id not in
                <foreach collection="skuIdLst" item="skuId" open="(" close=")" separator=",">
                    #{skuId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectShopGoodsSkuLstByWareIdsLst" resultMap="ShopGoodSkuDTO">
        select
        <include refid="Base_Column_List"/>
        from ${tableName}
        <where>
            shop_id=#{shopId} and
            ware_id in
            <foreach collection="wareIds" open="(" separator="," close=")" item="wareId">
                #{wareId}
            </foreach>
        </where>

    </select>
</mapper>