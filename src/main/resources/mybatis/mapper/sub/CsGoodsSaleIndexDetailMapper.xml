<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.sub.CsGoodsSaleIndexDetailMapper">
	<resultMap id="CsGoodsSaleIndexDetailDTO"
		type="com.pes.jd.model.DTO.CsGoodsSaleIndexDetailDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="date" jdbcType="DATE" property="date" />
		<result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
		<result column="sku_id" jdbcType="VARCHAR" property="skuId" />
		<result column="customer" jdbcType="VARCHAR" property="customer" />
		<result column="order_id" jdbcType="BIGINT" property="orderId" />
		<result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
		<result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
		<result column="out_stock_time" jdbcType="TIMESTAMP" property="outStockTime" />
		<result column="enquiry_date" jdbcType="TIMESTAMP" property="enquiryDate" />
	</resultMap>

	<select id="selectCsGoodsSaleCount" resultType="java.lang.Integer">
		SELECT
		count(*)
		FROM
		(
		<foreach collection="csSaleDetailTables" item="table"
			separator="UNION ALL">
			SELECT
			id,sku_id
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
                <if test="orderId!=null and orderId!=''">
                    and order_id = #{orderId}
				</if>
				<if test="parentOrderId !=null and parentOrderId.size()>0 ">
					and order_id not in
					<foreach collection="parentOrderId" item="pid" index="index"
							 open="(" close=")" separator=",">
						#{pid}
					</foreach>
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and cs_nick in
					<foreach collection="csNickList" item="nick" open="("
						close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")"
						separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
			</where>
		</foreach>
		) sale left join ${skuGoodsTables} goods on sale.sku_id = goods.sku_id
	</select>


	<select id="selectCsGoodsSaleCountV2" resultType="java.lang.Integer">
		SELECT
		count(*)
		FROM
		(
		<foreach collection="csSaleDetailTables" item="table" separator="UNION ALL">
			SELECT
			sale.id,sale.sku_id
			FROM ${table.getTableName} sale
			<if test="categoryId != null and categoryId.size > 0">
				INNER JOIN ${skuGoodsTables} goods ON sale.sku_id = goods.sku_id
			</if>
			<where>
				<if test="shopId!=null">
					sale.shop_id = #{shopId}
				</if>
				<if test="orderId!=null and orderId!=''">
					and sale.order_id = #{orderId}
				</if>
				<if test="parentOrderId !=null and parentOrderId.size()>0 ">
					and sale.order_id not in
					<foreach collection="parentOrderId" item="pid" index="index"
							 open="(" close=")" separator=",">
						#{pid}
					</foreach>
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and sale.cs_nick in
					<foreach collection="csNickList" item="nick" open="("
							 close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sale.sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")"
							 separator=",">
						#{skuId}
					</foreach>
				</if>
				<if test="categoryId != null and categoryId.size > 0">
					and goods.category_id in
					<foreach collection="categoryId" item="catId" open="(" close=")"
							 separator=",">
						#{catId}
					</foreach>
				</if>
				and sale.date between #{startDate} and #{endDate}
			</where>
		</foreach>
		) sale left join ${skuGoodsTables} goods on sale.sku_id = goods.sku_id
	</select>


	<select id="selectCsGoodsSaleIndexDetailByCsNickByDateBySku"
		resultMap="CsGoodsSaleIndexDetailDTO" parameterType="map">
		SELECT sale.shop_id,
		sale.date,sale.order_id,sale.sku_id,goods.sku_name,sale.customer,sale.cs_nick,
		sale.sale_goods_num,sale.sale_amount,sale.out_stock_time,enquiry_date
		FROM
		(
		<foreach collection="csSaleDetailTables" item="table"
			separator="union">
			SELECT shop_id,
			date,order_id,sku_id,customer,cs_nick,
			sale_goods_num,sale_amount,out_stock_time,enquiry_date
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
                <if test="orderId!=null and orderId!=''">
                    and order_id = #{orderId}
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and cs_nick in
					<foreach collection="csNickList" item="nick" open="("
						close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")"
						separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
			</where>
		</foreach>
		) sale left join ${skuGoodsTables} goods on sale.sku_id = goods.sku_id
<!-- 		left join  -->
<!-- 		(<foreach collection="orderSkuTables" item="table" separator="union"> -->
<!-- 			select order_id,item_sku_id,item_price,item_num,seller_discount,total_fee -->
<!-- 			from ${table.getTableName} -->
<!-- 		</foreach> -->
<!-- 		) orderSku on sale.order_id = orderSku.order_id -->
<!-- 		and sale.sku_id = orderSku.item_sku_id -->
		<if test="sortPageQuery.sort">
			order by sale.${sortPageQuery.field} ${sortPageQuery.sortDirection}
		</if>
		<if test="sortPageQuery.currentPage != null and sortPageQuery.size != 0">
			LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
		</if>
	</select>

	<select id="selectCsGoodsSaleIndexDetailByShopIdByCsNickByDateBySku"
			resultMap="CsGoodsSaleIndexDetailDTO" parameterType="map">
		SELECT
			sale.shop_id,
			sale.date,
			sale.order_id,
			sale.sku_id,
			sale.customer,
			sale.cs_nick,
			sale.sale_goods_num,
			sale.sale_amount,
			sale.enquiry_date
		FROM
		(
		<foreach collection="csSaleDetailTables" item="table"
				 separator="UNION ALL">
			SELECT shop_id,
			date,order_id,sku_id,customer,cs_nick,
			sale_goods_num,sale_amount,enquiry_date
			FROM ${table.getTableName}
			<where>
				<if test="shopId!=null">
					shop_id = #{shopId}
				</if>
				<if test="orderId!=null and orderId!=''">
					and order_id = #{orderId}
				</if>
				<if test="parentOrderId !=null and parentOrderId.size()>0 ">
					and order_id not in
					<foreach collection="parentOrderId" item="pid" index="index"
							 open="(" close=")" separator=",">
						#{pid}
					</foreach>
				</if>
				<if test="csNickList!=null and csNickList.size>0">
					and cs_nick in
					<foreach collection="csNickList" item="nick" open="("
							 close=")" separator=",">
						#{nick}
					</foreach>
				</if>
				<if test="skuLst!=null and skuLst.size>0">
					and sku_id in
					<foreach collection="skuLst" item="skuId" open="(" close=")"
							 separator=",">
						#{skuId}
					</foreach>
				</if>
				and date between #{startDate} and #{endDate}
			</where>
		</foreach>
		) sale

		<if test="sortPageQuery.sort">
			order by sale.${sortPageQuery.field} ${sortPageQuery.sortDirection}
		</if>
		<if test="sortPageQuery.currentPage != null and sortPageQuery.size != 0">
			LIMIT #{sortPageQuery.currentPage}, #{sortPageQuery.size}
		</if>
	</select>
</mapper>
