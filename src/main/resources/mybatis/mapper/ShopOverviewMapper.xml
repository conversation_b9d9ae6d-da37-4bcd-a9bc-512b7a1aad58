<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopOverviewMapper">
    <!--
    <resultMap id="ShopDayOverviewDTO" type="com.pes.jd.model.DTO.ShopDayOverviewDTO" >
        <result property="" column="" jdbcType=""/>
    </resultMap> -->


    <select id="selectShopCreatedOredrLst" parameterType="map" resultType="com.pes.jd.model.DTO.CreatedOrderDTO">
		SELECT 
			order_id as orderId,
			num as orderGoodsNum,
			buyer_nick as buyerNick,
			payment as orderPayment,
			post_fee as orderPostFee,
		    pay_type as payType,
            order_type as orderType
        FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND	created BETWEEN #{startDate} AND #{endDate}
	</select>

    <select id="selectShopOredredDataOverview" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDayOverviewDTO">
		SELECT 
			sum(payment) orderedAmount,
			count(order_id) orderedNum
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND	created BETWEEN #{startDate} AND #{endDate}
	</select>

    <select id="selectShopConsignOrderOverview" parameterType="map"
            resultType="com.pes.jd.model.DTO.ShopDayOverviewDTO">
		SELECT 
			count(order_id) consignNum
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND
			consign_time between #{startDate} AND #{endDate}
	</select>

    <select id="selectShopPaidSaleOrderLst" parameterType="map" resultType="com.pes.jd.model.DTO.SaleOrderDTO">
	
		SELECT
			order_id as orderId,
			num as orderGoodsNum,
			buyer_nick as buyerNick,
			payment as orderPayment,
			post_fee as orderPostFee
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND	(pay_time BETWEEN #{startDate} AND #{endDate} OR (pay_type = 1 AND  created BETWEEN #{startDate} AND #{endDate} ))
		
	</select>


    <select id="selectShopSaleOrderLst" parameterType="map" resultType="com.pes.jd.model.DTO.SaleOrderDTO">

		SELECT
			order_id as orderId,
			num as orderGoodsNum,
			buyer_nick as buyerNick,
			payment as orderPayment,
			post_fee as orderPostFee,
		    order_type as orderType
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND	pay_time BETWEEN #{startDate} AND #{endDate}

	</select>

    <select id="selectShopGoodsToPaySaleOrderLst" parameterType="map" resultType="com.pes.jd.model.DTO.SaleOrderDTO">

		SELECT
			order_id as orderId,
			num as orderGoodsNum,
			buyer_nick as buyerNick,
			payment as orderPayment,
			post_fee AS orderPostFee
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND created BETWEEN #{startDate} AND #{endDate}
		AND pay_type = 1

	</select>

    <select id="selectShopPaidDataOverview" parameterType="map" resultType="com.pes.jd.model.DTO.ShopDayOverviewDTO">

        SELECT
        <!-- 			DATE_FORMAT(pay_time,'%Y-%m-%d') date,  -->
        count(order_id) saleOrderNum,
        sum(num) saleGoodsNum,
        count(distinct(buyer_nick)) saleBuyerNum,
        sum(payment) saleAmount,
        sum(post_fee) orderPostFee
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND pay_time BETWEEN #{startDate} AND #{endDate}

    </select>

    <select id="selectShopConfirmGoodsOrderLst" parameterType="map"
            resultType="com.pes.jd.model.DTO.ConfirmGoodsOrderDTO">

        SELECT
        order_id as orderId,
        num as num,
        buyer_nick as buyerNick,
        payment as payment
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND
        end_time between #{startDate} and #{endDate}
        <!-- 			AND (status = 'FINISHED_L') -->
    </select>

    <select id="selectShopConfirmGoodsDataOverview" parameterType="map"
            resultType="com.pes.jd.model.DTO.ShopDayOverviewDTO">

        SELECT
        <!-- 				DATE_FORMAT(end_time,'%Y-%m-%d') date,  -->
        count(order_id) cfmGoodstONum,
        sum(payment) cfmGoodsOAmount
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND
        end_time between #{startDate} and #{endDate}
        <!-- 			AND (status = 'FINISHED_L') -->
    </select>

    <select id="selectShopOutStockOrderLst" parameterType="map" resultType="com.pes.jd.model.DTO.OutStockOrderDTO">

        SELECT
        order_id as orderId,
        num as num,
        buyer_nick as buyerNick,
        payment as payment
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND
        out_stock_time between #{startDate} and #{endDate}
        <!-- 			AND (status = 'FINISHED_L') -->
    </select>

    <select id="selectPaymentByOrderIds" resultType="com.pes.jd.model.DTO.SaleOrderDTO">

        SELECT
        order_id as orderId,
        num as orderGoodsNum,
        buyer_nick as buyerNick,
        payment as orderPayment,
        post_fee as orderPostFee,
        order_type as orderType
        FROM ${tableName}
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

</mapper>