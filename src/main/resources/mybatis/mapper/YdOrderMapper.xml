<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.YdOrderMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.YdOrderDTO" >
    <result column="pushModified" property="pushmodified" jdbcType="TIMESTAMP" />
    <result column="pin" property="pin" jdbcType="VARCHAR" />
    <result column="orderId" property="orderid" jdbcType="BIGINT" />
    <result column="state" property="state" jdbcType="VARCHAR" />
    <result column="orderType" property="ordertype" jdbcType="VARCHAR" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
    <result column="pushCreated" property="pushcreated" jdbcType="TIMESTAMP" />
    <result column="hashcode" property="hashcode" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="responseJson" property="responsejson" jdbcType="LONGVARCHAR" />
    <result column="venderId" property="venderId" jdbcType="BIGINT"/>
  </resultMap>
  <sql id="Base_Column_List" >
    venderId, pushModified, pin, orderId, state, orderType, created, modified, pushCreated, hashcode,
    version, responseJson
  </sql>

  <select id="selectOrderByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from yd_pop_order
    <where>
      <if test="orderIds!=null and orderIds.size()!=0">
        orderId in
        <foreach collection="orderIds" open="(" close=")" separator="," item="orderId">
          #{orderId}
        </foreach>
      </if>
    </where>
  </select>


  <select id="selectOrderIdByVendIdAndDate" resultType="java.lang.Long" parameterType="map" >
    select
    orderId
    from yd_pop_order
    where venderId = #{venderId,jdbcType=BIGINT}
    <choose>
      <when test='type=="0"'>
        AND created between #{startDate} and #{endDate}
      </when>
      <otherwise>
        AND modified between #{startDate} and #{endDate}
      </otherwise>
    </choose>
  </select>


</mapper>