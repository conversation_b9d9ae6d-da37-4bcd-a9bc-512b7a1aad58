<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsLoginlogMapper" >

  <resultMap id="CsLoginlogDTO" type="com.pes.jd.model.DTO.CsLoginlogDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="change_time" property="changeTime" jdbcType="TIMESTAMP" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="type" property="type" jdbcType="TINYINT" />
  </resultMap>
  
  <sql id="base_field" >
    id, cs_nick, change_time, shop_id, type
  </sql>
  
  <delete id="deleteCsLoginlogById" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteCsLoginlogByDateByShopId" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE shop_id = #{shopId,jdbcType=BIGINT}
    AND change_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
  </delete>
  
  <insert id="insertCsLoginlog" parameterType="com.pes.jd.model.DTO.CsLoginlogDTO" >
    INSERT INTO ${tableName} (cs_nick, change_time, shop_id,type)
    VALUES (#{csNick,jdbcType=VARCHAR}, #{changeTime,jdbcType=TIMESTAMP},
      #{shopId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT})
  </insert>
  
  <insert id="insertCsLoginlogList" parameterType="com.pes.jd.model.DTO.CsLoginlogDTO" >
    INSERT INTO ${tableName} (cs_nick, change_time, shop_id,type)
    VALUES 
    <foreach collection="loginlogList" item="loginlog" separator="," index="index">
	    (#{loginlog.csNick,jdbcType=VARCHAR}, #{loginlog.changeTime,jdbcType=TIMESTAMP}, 
	      #{loginlog.shopId,jdbcType=BIGINT}, #{loginlog.type,jdbcType=TINYINT})
    </foreach>
  </insert>
  
  <update id="updateCsLoginlogById" parameterType="com.pes.jd.model.DTO.CsLoginlogDTO" >
    UPDATE ${tableName}
    <set >
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="changeTime != null" >
        change_time = #{changeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="queryCsLoginlogByCsNicksByShopIdByDate" parameterType="map" resultMap="CsLoginlogDTO">
  	SELECT <include refid="base_field" />
	FROM ${tableName}
	WHERE cs_nick IN
	<foreach collection="csLst" item="cs" open="(" close=")" separator=",">
		#{cs.nick}
	</foreach>
	AND shop_id = #{shopId}
	AND change_time BETWEEN #{delayStartDate,jdbcType=TIMESTAMP} AND #{delayEndDate,jdbcType=TIMESTAMP}
  	ORDER BY change_time,type
  </select>
  
  <select id="getCsLoginlogByFirstTime" parameterType="map" resultMap="CsLoginlogDTO">
  	SELECT <include refid="base_field" />
	FROM ${tableName}
	WHERE cs_nick = #{csNick}
	AND shop_id = #{shopId}
	AND change_time &lt; #{changeTime}
	AND type != 5
	ORDER BY change_time DESC LIMIT 0,1
  </select>
  
</mapper>