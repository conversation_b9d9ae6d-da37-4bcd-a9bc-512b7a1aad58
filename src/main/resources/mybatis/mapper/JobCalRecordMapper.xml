<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.JobCalRecordMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.JobCalRecordDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="handle_chat_flag" property="handleChatFlag" jdbcType="BIT" />
    <result column="handle_receive_quality_flag" property="handleReceiveQualityFlag" jdbcType="BIT" />
    <result column="handle_csorder_index_flag" property="handleCsorderIndexFlag" jdbcType="BIT" />
    <result column="handle_shop_cs_bind_flag" property="handleShopCsBindFlag" jdbcType="BIT" />
    <result column="handle_enquiry_chat_flag" property="handleEnquiryChatFlag" jdbcType="BIT" />
    <result column="handle_cs_performance_flag" property="handleCsPerformanceFlag" jdbcType="BIT" />
    <result column="handle_csperformance_fororder" property="handleCsperformanceFororder" jdbcType="BIT" />
    <result column="handle_shopday_overview_flag" property="handleShopdayOverviewFlag" jdbcType="BIT" />
    <result column="handle_assit_index_flag" property="handleAssitIndexFlag" jdbcType="BIT" />
    <result column="handle_goods_cal_flag" property="handleGoodsCalFlag" jdbcType="BIT" />
    <result column="handle_sum_goods_cal_flag" property="handleSumGoodsCalFlag" jdbcType="BIT" />
    <result column="handle_slien_sale_flag" property="handleSlienSaleFlag" jdbcType="BIT" />
    <result column="handle_sum_slien_sale_flag" property="handleSumSlienSaleFlag" jdbcType="BIT" />
    <result column="order_filter_falg" property="orderFilterFalg" jdbcType="BIT" />
    <result column="handle_order_loss_flag" property="jj0handleOrderLossFlag" jdbcType="BIT" />
    <result column="handle_enquiry_loss_flag" property="handleEnquiryLossFlag" jdbcType="BIT" />
    <result column="handle_enquiry_order_loss_flag" property="handleEnquiryOrderLossFlag" jdbcType="BIT" />
    <result column="handle_silent_order_loss_flag" property="handleSilentOrderLossFlag" jdbcType="BIT" />
    <result column="handle_order_outstock_loss_flag" property="handleOrderOutstockLossFlag" jdbcType="BIT" />
    <result column="handle_cs_loss_flag" property="handleCsLossFlag" jdbcType="BIT" />
    <result column="handle_order_eval_bind_flag" property="handleOrderEvalBindFlag" jdbcType="BIT" />
    <result column="handle_shopcs_order_bind_flag" property="handleShopcsOrderBindFlag" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, date, handle_chat_flag, handle_receive_quality_flag, 
    handle_csorder_index_flag, handle_shop_cs_bind_flag, handle_enquiry_chat_flag, handle_cs_performance_flag, 
    handle_csperformance_fororder, handle_shopday_overview_flag, handle_assit_index_flag, 
    handle_goods_cal_flag, handle_sum_goods_cal_flag, handle_slien_sale_flag, handle_sum_slien_sale_flag, 
    order_filter_falg, handle_order_loss_flag, handle_enquiry_loss_flag, handle_enquiry_order_loss_flag, 
    handle_silent_order_loss_flag, handle_order_outstock_loss_flag, handle_cs_loss_flag, 
    handle_order_eval_bind_flag, handle_shopcs_order_bind_flag
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pes_job_cal_record_2019_04
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteJobCalRecordByShopIdAndDate" parameterType="map" >
     delete from ${tableName}
    WHERE shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>
  <insert id="insertJobCalRecord" parameterType="map" >
    insert into ${tableName}
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="record.shopId != null" >
        shop_id,
      </if>
      <if test="record.date != null" >
        date,
      </if>
      <if test="record.handleChatFlag != null" >
        handle_chat_flag,
      </if>
      <if test="record.handleReceiveQualityFlag != null" >
        handle_receive_quality_flag,
      </if>
      <if test="record.handleCsorderIndexFlag != null" >
        handle_csorder_index_flag,
      </if>
      <if test="record.handleShopCsBindFlag != null" >
        handle_shop_cs_bind_flag,
      </if>
      <if test="record.handleEnquiryChatFlag != null" >
        handle_enquiry_chat_flag,
      </if>
      <if test="record.handleCsPerformanceFlag != null" >
        handle_cs_performance_flag,
      </if>
      <if test="record.handleCsperformanceFororder != null" >
        handle_csperformance_fororder,
      </if>
      <if test="record.handleShopdayOverviewFlag != null" >
        handle_shopday_overview_flag,
      </if>
      <if test="record.handleAssitIndexFlag != null" >
        handle_assit_index_flag,
      </if>
      <if test="record.handleGoodsCalFlag != null" >
        handle_goods_cal_flag,
      </if>
      <if test="record.handleSumGoodsCalFlag != null" >
        handle_sum_goods_cal_flag,
      </if>
      <if test="record.handleSlienSaleFlag != null" >
        handle_slien_sale_flag,
      </if>
      <if test="record.handleSumSlienSaleFlag != null" >
        handle_sum_slien_sale_flag,
      </if>
      <if test="record.orderFilterFalg != null" >
        order_filter_falg,
      </if>
      <if test="record.handleOrderLossFlag != null" >
        handle_order_loss_flag,
      </if>
      <if test="record.handleEnquiryLossFlag != null" >
        handle_enquiry_loss_flag,
      </if>
      <if test="record.handleEnquiryOrderLossFlag != null" >
        handle_enquiry_order_loss_flag,
      </if>
      <if test="record.handleSilentOrderLossFlag != null" >
        handle_silent_order_loss_flag,
      </if>
      <if test="record.handleOrderOutstockLossFlag != null" >
        handle_order_outstock_loss_flag,
      </if>
      <if test="record.handleCsLossFlag != null" >
        handle_cs_loss_flag,
      </if>
      <if test="record.handleOrderEvalBindFlag != null" >
        handle_order_eval_bind_flag,
      </if>
      <if test="record.handleShopcsOrderBindFlag != null" >
        handle_shopcs_order_bind_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="record.shopId != null" >
        #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.date != null" >
        #{record.date,jdbcType=DATE},
      </if>
      <if test="record.handleChatFlag != null" >
        #{record.handleChatFlag,jdbcType=BIT},
      </if>
      <if test="record.handleReceiveQualityFlag != null" >
        #{record.handleReceiveQualityFlag,jdbcType=BIT},
      </if>
      <if test="record.handleCsorderIndexFlag != null" >
        #{record.handleCsorderIndexFlag,jdbcType=BIT},
      </if>
      <if test="record.handleShopCsBindFlag != null" >
        #{record.handleShopCsBindFlag,jdbcType=BIT},
      </if>
      <if test="record.handleEnquiryChatFlag != null" >
        #{record.handleEnquiryChatFlag,jdbcType=BIT},
      </if>
      <if test="record.handleCsPerformanceFlag != null" >
        #{record.handleCsPerformanceFlag,jdbcType=BIT},
      </if>
      <if test="record.handleCsperformanceFororder != null" >
        #{record.handleCsperformanceFororder,jdbcType=BIT},
      </if>
      <if test="record.handleShopdayOverviewFlag != null" >
        #{record.handleShopdayOverviewFlag,jdbcType=BIT},
      </if>
      <if test="record.handleAssitIndexFlag != null" >
        #{record.handleAssitIndexFlag,jdbcType=BIT},
      </if>
      <if test="record.handleGoodsCalFlag != null" >
        #{record.handleGoodsCalFlag,jdbcType=BIT},
      </if>
      <if test="record.handleSumGoodsCalFlag != null" >
        #{record.handleSumGoodsCalFlag,jdbcType=BIT},
      </if>
      <if test="record.handleSlienSaleFlag != null" >
        #{record.handleSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="record.handleSumSlienSaleFlag != null" >
        #{record.handleSumSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="record.orderFilterFalg != null" >
        #{record.orderFilterFalg,jdbcType=BIT},
      </if>
      <if test="record.handleOrderLossFlag != null" >
        #{record.handleOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="record.handleEnquiryLossFlag != null" >
        #{record.handleEnquiryLossFlag,jdbcType=BIT},
      </if>
      <if test="record.handleEnquiryOrderLossFlag != null" >
        #{record.handleEnquiryOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="record.handleSilentOrderLossFlag != null" >
        #{record.handleSilentOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="record.handleOrderOutstockLossFlag != null" >
        #{record.handleOrderOutstockLossFlag,jdbcType=BIT},
      </if>
      <if test="record.handleCsLossFlag != null" >
        #{record.handleCsLossFlag,jdbcType=BIT},
      </if>
      <if test="record.handleOrderEvalBindFlag != null" >
        #{record.handleOrderEvalBindFlag,jdbcType=BIT},
      </if>
      <if test="record.handleShopcsOrderBindFlag != null" >
        #{record.handleShopcsOrderBindFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
</mapper>