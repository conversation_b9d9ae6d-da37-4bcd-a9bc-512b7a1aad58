<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopPerformancePresaleMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopPerformancePresaleDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum"/>
        <result column="ordered_sku_num" jdbcType="INTEGER" property="orderedSkuNum"/>
        <result column="ordered_bargain_buyer_num" jdbcType="INTEGER" property="orderedBargainBuyerNum"/>
        <result column="ordered_bargain_sku_num" jdbcType="INTEGER" property="orderedBargainSkuNum"/>
        <result column="ordered_bargain_amount" jdbcType="DOUBLE" property="orderedBargainAmount"/>
        <result column="ordered_balance_buyer_num" jdbcType="INTEGER" property="orderedBalanceBuyerNum"/>
        <result column="ordered_balance_sku_num" jdbcType="INTEGER" property="orderedBalanceSkuNum"/>
        <result column="ordered_balance_amount" jdbcType="DOUBLE" property="orderedBalanceAmount"/>
        <result column="to_ordered_bargain_buyer_num" jdbcType="INTEGER" property="toOrderedBargainBuyerNum"/>
        <result column="to_ordered_balance_buyer_num" jdbcType="INTEGER" property="toOrderedBalanceBuyerNum"/>
    </resultMap>

    <sql id="base_field">
  	id,shop_id,`date`,sku_id,sku_name,activity_id,
  	ordered_buyer_num,ordered_sku_num,ordered_bargain_buyer_num,ordered_bargain_sku_num,ordered_bargain_amount,
  	ordered_balance_buyer_num,ordered_balance_sku_num,ordered_balance_amount,to_ordered_bargain_buyer_num,to_ordered_balance_buyer_num
  </sql>

    <insert id="batchInsertShopPerformancePresale" parameterType="com.pes.jd.model.DO.ShopPerformancePresaleDO">
        INSERT INTO ${tableName}
        (
        shop_id,`date`,sku_id,sku_name,activity_id,
        ordered_buyer_num,
        ordered_sku_num,
        ordered_bargain_buyer_num,
        ordered_bargain_sku_num,
        ordered_bargain_amount,
        ordered_balance_buyer_num,
        ordered_balance_sku_num,
        ordered_balance_amount,
        to_ordered_bargain_buyer_num,
        to_ordered_balance_buyer_num
        )
        VALUES
        <foreach collection="performances" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.skuId,jdbcType=INTEGER},
            #{itm.skuName,jdbcType=VARCHAR},
            #{itm.activityId,jdbcType=VARCHAR},
            #{itm.orderedBuyerNum,jdbcType=INTEGER},
            #{itm.orderedSkuNum,jdbcType=INTEGER},
            #{itm.orderedBargainBuyerNum,jdbcType=INTEGER},
            #{itm.orderedBargainSkuNum,jdbcType=INTEGER},
            #{itm.orderedBargainAmount,jdbcType=DOUBLE},
            #{itm.orderedBalanceBuyerNum,jdbcType=INTEGER},
            #{itm.orderedBalanceSkuNum,jdbcType=INTEGER},
            #{itm.orderedBalanceAmount,jdbcType=DOUBLE},
            #{itm.toOrderedBargainBuyerNum,jdbcType=DOUBLE},
            #{itm.toOrderedBalanceBuyerNum,jdbcType=DOUBLE}
            )
        </foreach>

    </insert>

    <delete id="deleteByShopIdAndDateAndActivityId">
    DELETE FROM ${tableName}
    WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND `date` BETWEEN #{startDate} AND #{endDate}
        AND activity_id = #{activityId,jdbcType=VARCHAR}
  </delete>

</mapper>