<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.CsTOrderPerformanceMapper">

  <resultMap id="CsTOrderPerformanceDTO" type="com.pes.jd.model.DTO.CsTOrderPerformanceDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="to_ordered_num" jdbcType="INTEGER" property="toOrderedNum" />
    <result column="to_ordered_order_num" jdbcType="INTEGER" property="toOrderedOrderNum" />
    <result column="to_ordered_goods_num" jdbcType="INTEGER" property="toOrderedGoodsNum" />
    <result column="to_ordered_amount" jdbcType="DOUBLE" property="toOrderedAmount" />
    <result column="to_ordered_paid_num_today" jdbcType="INTEGER" property="toOrderedPaidNumToday" />
    <result column="to_ordered_paid_order_num_today" jdbcType="INTEGER" property="toOrderedPaidOrderNumToday" />
    <result column="to_ordered_paid_goods_today" jdbcType="INTEGER" property="toOrderedPaidGoodsToday" />
    <result column="to_ordered_paid_amount_today" jdbcType="DOUBLE" property="toOrderedPaidAmountToday" />
    <result column="to_ordered_paid_num_final" jdbcType="INTEGER" property="toOrderedPaidNumFinal" />
    <result column="to_ordered_paid_order_num_final" jdbcType="INTEGER" property="toOrderedPaidOrderNumFinal" />
    <result column="to_ordered_paid_goods_final" jdbcType="INTEGER" property="toOrderedPaidGoodsFinal" />
    <result column="to_ordered_paid_amount_final" jdbcType="DOUBLE" property="toOrderedPaidAmountFinal" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
    <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
    <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
    <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
    <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
    <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
    <result column="cfm_goods_order_num" jdbcType="INTEGER" property="cfmGoodsOrderNum" />
    <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
    <result column="cfm_goods_num" jdbcType="INTEGER" property="cfmGoodsNum" />
    <result column="cfm_goods_buyer_num" jdbcType="INTEGER" property="cfmGoodsBuyerNum" />
  </resultMap>
  <resultMap id="csTOrderPerformanceDO" type="com.pes.jd.model.DO.CsTOrderPerformanceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="to_ordered_num" jdbcType="INTEGER" property="toOrderedNum" />
    <result column="to_ordered_order_num" jdbcType="INTEGER" property="toOrderedOrderNum" />
    <result column="to_ordered_goods_num" jdbcType="INTEGER" property="toOrderedGoodsNum" />
    <result column="to_ordered_amount" jdbcType="DOUBLE" property="toOrderedAmount" />
    <result column="to_ordered_paid_num_today" jdbcType="INTEGER" property="toOrderedPaidNumToday" />
    <result column="to_ordered_paid_order_num_today" jdbcType="INTEGER" property="toOrderedPaidOrderNumToday" />
    <result column="to_ordered_paid_goods_today" jdbcType="INTEGER" property="toOrderedPaidGoodsToday" />
    <result column="to_ordered_paid_amount_today" jdbcType="DOUBLE" property="toOrderedPaidAmountToday" />
    <result column="to_ordered_paid_num_final" jdbcType="INTEGER" property="toOrderedPaidNumFinal" />
    <result column="to_ordered_paid_order_num_final" jdbcType="INTEGER" property="toOrderedPaidOrderNumFinal" />
    <result column="to_ordered_paid_goods_final" jdbcType="INTEGER" property="toOrderedPaidGoodsFinal" />
    <result column="to_ordered_paid_amount_final" jdbcType="DOUBLE" property="toOrderedPaidAmountFinal" />
    <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
    <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
    <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
    <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
    <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
    <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
    <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
    <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
    <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
    <result column="cfm_goods_order_num" jdbcType="INTEGER" property="cfmGoodsOrderNum" />
    <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
    <result column="cfm_goods_num" jdbcType="INTEGER" property="cfmGoodsNum" />
    <result column="cfm_goods_buyer_num" jdbcType="INTEGER" property="cfmGoodsBuyerNum" />
  </resultMap>

  <insert id="insertCsTOrderPerformances" parameterType="map">
	INSERT INTO ${tableName}
	    (
	      shop_id, date,
	      cs_nick, to_ordered_num,
	      to_ordered_order_num,
	      to_ordered_goods_num,
	      to_ordered_amount,
	      to_ordered_paid_num_today,
	      to_ordered_paid_order_num_today,
	      to_ordered_paid_goods_today,
	      to_ordered_paid_amount_today,
	      to_ordered_paid_num_final,
	      to_ordered_paid_order_num_final,
	      to_ordered_paid_goods_final,
	      to_ordered_paid_amount_final,
          to_ordered_out_stock_num,
          to_ordered_out_stock_amount,
          to_ordered_out_stock_goods_num,
          to_ordered_out_stock_order_num,
	      sale_amount, sale_order_num,
	      sale_goods_num, sale_buyer_num,
	      post_fee,
	      out_stock_num,
	      out_stock_amount,
	      out_stock_goods_num,
	      out_stock_order_num,
	      cfm_goods_order_num,
	      cfm_goods_amount,
	      cfm_goods_num,
	      cfm_goods_buyer_num
	      )
	    VALUES
	    <foreach collection="csTOrderPerformanceLst" item="itm" separator=",">
	    (
	      #{itm.shopId,jdbcType=BIGINT},
	      #{itm.date,jdbcType=DATE},
	      #{itm.csNick,jdbcType=VARCHAR},
	      #{itm.toOrderedNum,jdbcType=INTEGER},
	      #{itm.toOrderedOrderNum,jdbcType=INTEGER},
	      #{itm.toOrderedGoodsNum,jdbcType=INTEGER},
	      #{itm.toOrderedAmount,jdbcType=DOUBLE},
	      #{itm.toOrderedPaidNumToday,jdbcType=INTEGER},
	      #{itm.toOrderedPaidOrderNumToday,jdbcType=INTEGER},
	      #{itm.toOrderedPaidGoodsToday,jdbcType=INTEGER},
	      #{itm.toOrderedPaidAmountToday,jdbcType=DOUBLE},
	      #{itm.toOrderedPaidNumFinal,jdbcType=INTEGER},
	      #{itm.toOrderedPaidOrderNumFinal,jdbcType=INTEGER},
	      #{itm.toOrderedPaidGoodsFinal,jdbcType=INTEGER},
	      #{itm.toOrderedPaidAmountFinal,jdbcType=DOUBLE},
          #{itm.toOrderedOutStockNum,jdbcType=DOUBLE},
          #{itm.toOrderedOutStockAmount,jdbcType=DOUBLE},
          #{itm.toOrderedOutStockGoodsNum,jdbcType=DOUBLE},
          #{itm.toOrderedOutStockOrderNum,jdbcType=DOUBLE},
	      #{itm.saleAmount,jdbcType=DOUBLE},
	      #{itm.saleOrderNum,jdbcType=INTEGER},
	      #{itm.saleGoodsNum,jdbcType=INTEGER},
	      #{itm.saleBuyerNum,jdbcType=INTEGER},
	      #{itm.postFee,jdbcType=DOUBLE},
	      #{itm.outStockNum,jdbcType=INTEGER},
	      #{itm.outStockAmount,jdbcType=DOUBLE},
	      #{itm.outStockGoodsNum,jdbcType=INTEGER},
	      #{itm.outStockOrderNum,jdbcType=INTEGER},
	      #{itm.cfmGoodsOrderNum,jdbcType=INTEGER},
	      #{itm.cfmGoodsAmount,jdbcType=DOUBLE},
	      #{itm.cfmGoodsNum,jdbcType=INTEGER},
	      #{itm.cfmGoodsBuyerNum,jdbcType=INTEGER}
	     )
	    </foreach>

  </insert>

  <delete id="deleteCsTOrderPerformances" parameterType="map">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date}
  </delete>

    <update id="batchUpdateShopCsSaleAndOutStackData" parameterType="map">

      <foreach collection="csSaleDataLst" item="itm" close="" open="" separator=";">
          UPDATE ${tableName}
          SET
              sale_amount = #{itm.saleAmount,jdbcType=DOUBLE},
              sale_order_num = #{itm.saleOrderNum,jdbcType=INTEGER},
              sale_goods_num = #{itm.saleGoodsNum,jdbcType=INTEGER},
              sale_buyer_num = #{itm.saleBuyerNum,jdbcType=INTEGER},
              post_fee = #{itm.postFee,jdbcType=DOUBLE},
              out_stock_num = #{itm.outStockNum,jdbcType=INTEGER},
              out_stock_amount = #{itm.outStockAmount,jdbcType=DOUBLE},
              out_stock_goods_num = #{itm.outStockGoodsNum,jdbcType=INTEGER},
              out_stock_order_num = #{itm.outStockOrderNum,jdbcType=INTEGER}
          WHERE
            id = #{itm.id,jdbcType=BIGINT}
      </foreach>
    </update>

    <update id="batchUpdateShopCsOutStackData" parameterType="map">

      <foreach collection="csSaleDataLst" item="itm" close="" open="" separator=";">
          UPDATE ${tableName}
          SET
              out_stock_num = #{itm.outStockNum,jdbcType=INTEGER},
              out_stock_amount = #{itm.outStockAmount,jdbcType=DOUBLE},
              out_stock_goods_num = #{itm.outStockGoodsNum,jdbcType=INTEGER},
              out_stock_order_num = #{itm.outStockOrderNum,jdbcType=INTEGER}
          WHERE
            id = #{itm.id,jdbcType=BIGINT}
      </foreach>
    </update>

  <update id="updateCsTOrderPerformanceBySelective" parameterType="com.pes.jd.model.DO.CsTOrderPerformanceDO">
    update ${tableName}
    <set>
      <if test="toOrderedNum != null">
        to_ordered_num = #{toOrderedNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedOrderNum != null">
        to_ordered_order_num = #{toOrderedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedGoodsNum != null">
        to_ordered_goods_num = #{toOrderedGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedAmount != null">
        to_ordered_amount = #{toOrderedAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedPaidNumToday != null">
        to_ordered_paid_num_today = #{toOrderedPaidNumToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNumToday != null">
        to_ordered_paid_order_num_today = #{toOrderedPaidOrderNumToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsToday != null">
        to_ordered_paid_goods_today = #{toOrderedPaidGoodsToday,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmountToday != null">
        to_ordered_paid_amount_today = #{toOrderedPaidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedPaidNumFinal != null">
        to_ordered_paid_num_final = #{toOrderedPaidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNumFinal != null">
        to_ordered_paid_order_num_final = #{toOrderedPaidOrderNumFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsFinal != null">
        to_ordered_paid_goods_final = #{toOrderedPaidGoodsFinal,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmountFinal != null">
        to_ordered_paid_amount_final = #{toOrderedPaidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="saleAmount != null">
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="saleOrderNum != null">
        sale_order_num = #{saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null">
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleBuyerNum != null">
        sale_buyer_num = #{saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="postFee != null">
        post_fee = #{postFee,jdbcType=DOUBLE},
      </if>
      <if test="outStockNum != null">
        out_stock_num = #{outStockNum,jdbcType=INTEGER},
      </if>
      <if test="outStockAmount != null">
        out_stock_amount = #{outStockAmount,jdbcType=DOUBLE},
      </if>
      <if test="outStockGoodsNum != null">
        out_stock_goods_num = #{outStockGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="outStockOrderNum != null">
        out_stock_order_num = #{outStockOrderNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsOrderNum != null">
        cfm_goods_order_num = #{cfmGoodsOrderNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsAmount != null">
        cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="cfmGoodsNum != null">
        cfm_goods_num = #{cfmGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsBuyerNum != null">
        cfm_goods_buyer_num = #{cfmGoodsBuyerNum,jdbcType=INTEGER},
      </if>
    </set>
    WHERE
    	id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectCsTOrderPerformanceLst" resultMap="CsTOrderPerformanceDTO">
  	SELECT * FROM ${tableName}
  	WHERE 
  		shop_id = #{shopId,jdbcType=BIGINT}
  	AND date = #{date,jdbcType=DATE}
  </select>

    <select id="selectCsTOrderPerformanceLstForUpdate" resultType="com.pes.jd.model.DO.CsTOrderPerformanceDO">
  	SELECT id,shop_id as shopId,date,cs_nick as csNick
  	FROM ${tableName}
  	WHERE
  		shop_id = #{shopId,jdbcType=BIGINT}
  	AND date = #{date,jdbcType=DATE}
  </select>

  <select id="selectCsTOrderPerformanceByDateAndCsNick" resultMap="csTOrderPerformanceDO">
                SELECT * FROM ${tableName}
            WHERE
                shop_id = #{shopId,jdbcType=BIGINT}
            AND cs_nick =#{csNick}
            AND date = #{date,jdbcType=DATE}
</select>
</mapper>