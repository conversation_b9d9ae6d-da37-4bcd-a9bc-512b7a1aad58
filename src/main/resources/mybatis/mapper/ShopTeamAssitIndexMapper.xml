<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopTeamAssitIndexMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopTeamAssitIndexDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="assit_order_create_num" jdbcType="INTEGER" property="assitOrderCreateNum" />
    <result column="assit_order_create_amount" jdbcType="DOUBLE" property="assitOrderCreateAmount" />
    <result column="assit_order_pay_num" jdbcType="INTEGER" property="assitOrderPayNum" />
    <result column="assit_order_pay_amount" jdbcType="DOUBLE" property="assitOrderPayAmount" />
    <result column="assit_order_followup_num" jdbcType="INTEGER" property="assitOrderFollowupNum" />
    <result column="assit_order_followup_amount" jdbcType="DOUBLE" property="assitOrderFollowupAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, assit_order_create_num, assit_order_create_amount, assit_order_pay_num, 
    assit_order_pay_amount, assit_order_followup_num, assit_order_followup_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_shop_team_assit_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_shop_team_assit_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ShopTeamAssitIndexDO">
insert into ${tableName} (id, shop_id, date, assit_order_create_num, assit_order_create_amount,
      assit_order_pay_num, assit_order_pay_amount, assit_order_followup_num,
      assit_order_followup_amount)
    values (#{record.id,jdbcType=BIGINT}, #{record.shopId,jdbcType=BIGINT}, #{record.date,jdbcType=TIMESTAMP},
      #{record.assitOrderCreateNum,jdbcType=INTEGER}, #{record.assitOrderCreateAmount,jdbcType=DOUBLE},
      #{record.assitOrderPayNum,jdbcType=INTEGER}, #{record.assitOrderPayAmount,jdbcType=DOUBLE}, #{record.assitOrderFollowupNum,jdbcType=INTEGER},
      #{record.assitOrderFollowupAmount,jdbcType=DOUBLE})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopTeamAssitIndexDO">
    insert into pes_shop_team_assit_index
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="assitOrderCreateNum != null">
        assit_order_create_num,
      </if>
      <if test="assitOrderCreateAmount != null">
        assit_order_create_amount,
      </if>
      <if test="assitOrderPayNum != null">
        assit_order_pay_num,
      </if>
      <if test="assitOrderPayAmount != null">
        assit_order_pay_amount,
      </if>
      <if test="assitOrderFollowupNum != null">
        assit_order_followup_num,
      </if>
      <if test="assitOrderFollowupAmount != null">
        assit_order_followup_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="assitOrderCreateNum != null">
        #{assitOrderCreateNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderCreateAmount != null">
        #{assitOrderCreateAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderPayNum != null">
        #{assitOrderPayNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderPayAmount != null">
        #{assitOrderPayAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderFollowupNum != null">
        #{assitOrderFollowupNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderFollowupAmount != null">
        #{assitOrderFollowupAmount,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopTeamAssitIndexDO">
    update pes_shop_team_assit_index
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="assitOrderCreateNum != null">
        assit_order_create_num = #{assitOrderCreateNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderCreateAmount != null">
        assit_order_create_amount = #{assitOrderCreateAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderPayNum != null">
        assit_order_pay_num = #{assitOrderPayNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderPayAmount != null">
        assit_order_pay_amount = #{assitOrderPayAmount,jdbcType=DOUBLE},
      </if>
      <if test="assitOrderFollowupNum != null">
        assit_order_followup_num = #{assitOrderFollowupNum,jdbcType=INTEGER},
      </if>
      <if test="assitOrderFollowupAmount != null">
        assit_order_followup_amount = #{assitOrderFollowupAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ShopTeamAssitIndexDO">
    update pes_shop_team_assit_index
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=TIMESTAMP},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      assit_order_create_num = #{assitOrderCreateNum,jdbcType=INTEGER},
      assit_order_create_amount = #{assitOrderCreateAmount,jdbcType=DOUBLE},
      assit_order_pay_num = #{assitOrderPayNum,jdbcType=INTEGER},
      assit_order_pay_amount = #{assitOrderPayAmount,jdbcType=DOUBLE},
      assit_order_followup_num = #{assitOrderFollowupNum,jdbcType=INTEGER},
      assit_order_followup_amount = #{assitOrderFollowupAmount,jdbcType=DOUBLE}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <delete id="deleteByDateShopId">
    delete from ${tableName}
    where shop_id = #{shopId} and date = #{date}
  </delete>
</mapper>