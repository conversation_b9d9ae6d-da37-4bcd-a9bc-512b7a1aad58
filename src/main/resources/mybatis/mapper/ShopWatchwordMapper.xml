<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopWatchwordMapper">

  <resultMap id="ShopWatchwordDTO" type="com.pes.jd.model.DTO.ShopWatchwordDTO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  
  <sql id="base_field">
      buyer_nick, date, type
  </sql>
  
  <insert id="insertShopWatchword" parameterType="com.pes.jd.model.DO.ShopWatchwordDO">
    INSERT INTO pes_shop_watchword (shop_id, cs_nick, 
      buyer_nick, date, type)
    VALUES (#{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR}, 
      #{buyerNick,jdbcType=VARCHAR}, #{date,jdbcType=DATE}, #{type,jdbcType=INTEGER})
  </insert>
  
  
	<insert id="batchInsertShopWatchword" parameterType="map">
		  INSERT INTO ${tableName} (shop_id, cs_nick, buyer_nick, date, type) VALUES
		<foreach collection="shopWatchwordLst" item="itm" separator=",">
			(#{itm.shopId,jdbcType=BIGINT}, #{itm.csNick,jdbcType=VARCHAR}, 
      		#{itm.buyerNick,jdbcType=VARCHAR}, #{itm.date,jdbcType=DATE}, 
      		#{itm.type,jdbcType=INTEGER})
		</foreach>
	</insert>
  <delete id="deleteShopWatchwordById" parameterType="java.lang.Integer">
    DELETE FROM pes_shop_watchword
    WHERE 
    	id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByshopIdAndDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND
			date =#{date}
	</delete>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopWatchwordDO">
    UPDATE pes_shop_watchword
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="buyerNick != null">
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=BIT},
      </if>
    </set>
    WHERE 
    	id = #{id,jdbcType=INTEGER}
  </update>
  
  <select id="getShopWatchwordById" parameterType="com.pes.jd.model.DO.ShopWatchwordDO">
	  SELECT   
	  	buyer_nick, date, type
	  FROM pes_shop_watchword
	  WHERE 
	  	id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectShopWatchwordLstByShopAndDate" resultMap="ShopWatchwordDTO">
	  SELECT   
	  	cs_nick, buyer_nick, `date`, `type`
	  FROM ${tableName}
	  WHERE 
	  	shop_id = #{shopId,jdbcType=BIGINT}
	  AND date = #{date,jdbcType=DATE}
  </select>
</mapper>