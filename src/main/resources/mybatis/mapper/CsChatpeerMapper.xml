<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsChatpeerMapper" >

   <resultMap id="CsOrderIndexChatpeerDTO" type="com.pes.jd.model.DTO.CsOrderIndexChatpeerDTO" >
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_flag" jdbcType="TINYINT" property="chatFlag" />
    <result column="forward_flag" jdbcType="TINYINT" property="forwardFlag" />
    <result column="is_watchword_buyer" jdbcType="BIT" property="isWatchwordBuyer" />
    <result column="is_filtered_buyer" jdbcType="BIT" property="isFilteredBuyer" />
    <result column="is_cs_single_chat_filter" jdbcType="BIT" property="isCsSingleChatFilter" />
    <result column="is_consult" jdbcType="BIT" property="isConsult" />
    <result column="is_receive" jdbcType="BIT" property="isReceive" />
    <result column="is_enquiry" jdbcType="BIT" property="isEnquiry" />
    <result column="is_cs_consult_first" jdbcType="BIT" property="isCsConsultFirst" />
    <result column="is_pes" jdbcType="BIT" property="isPes" />
    <result column="is_team_pes" jdbcType="BIT" property="isTeamPes" />
    <result column="is_next_day_pes" jdbcType="BIT" property="isNextDayPes" />
    <result column="is_assist" jdbcType="BIT" property="isAssist" />
    <result column="is_after_sale" jdbcType="BIT" property="isAfterSale" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
  </resultMap>

   <resultMap id="CommonCsChatpeerDTO" type="com.pes.jd.model.DTO.CommonCsChatpeerDTO" >
   	<id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_flag" jdbcType="TINYINT" property="chatFlag" />
    <result column="forward_flag" jdbcType="TINYINT" property="forwardFlag" />
    <result column="is_watchword_buyer" jdbcType="BIT" property="isWatchwordBuyer" />
    <result column="is_filtered_buyer" jdbcType="BIT" property="isFilteredBuyer" />
    <result column="is_cs_single_chat_filter" jdbcType="BIT" property="isCsSingleChatFilter" />
    <result column="is_cust_single_chat_filter" jdbcType="BIT" property="isCustSingleChatFilter" />
    <result column="is_ma_auto_reply_filter" jdbcType="BIT" property="isMainAccountAutoReplyFilter" />
    <result column="is_cs_offline_msg_filter" jdbcType="BIT" property="isCsOfflineMsgFilter" />
    <result column="is_consult" jdbcType="BIT" property="isConsult" />
    <result column="is_receive" jdbcType="BIT" property="isReceive" />
    <result column="is_enquiry" jdbcType="BIT" property="isEnquiry" />
    <result column="is_cs_consult_first" jdbcType="BIT" property="isCsFirstConsult" />
    <result column="is_assist" jdbcType="BIT" property="isAssist" />
    <result column="is_after_sale" jdbcType="BIT" property="isAfterSale" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
    <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
    <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate" />
  </resultMap>

  <resultMap id="ReceivedChatpeerDTO" type="com.pes.jd.model.DTO.ReceivedChatpeerDTO" >
 	<id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_flag" jdbcType="TINYINT" property="chatFlag" />
    <result column="forward_flag" jdbcType="TINYINT" property="forwardFlag" />
    <result column="is_watchword_buyer" jdbcType="BIT" property="isWatchwordBuyer" />
    <result column="is_filtered_buyer" jdbcType="BIT" property="isFilteredBuyer" />
    <result column="is_cs_single_chat_filter" jdbcType="BIT" property="isCsSingleChatFilter" />
    <result column="is_consult" jdbcType="BIT" property="isConsult" />
    <result column="is_receive" jdbcType="BIT" property="isReceive" />
    <result column="is_enquiry" jdbcType="BIT" property="isEnquiry" />
    <result column="is_cs_consult_first" jdbcType="BIT" property="isCsConsultFirst" />
	<result column="cs_chat_first_flag" jdbcType="TINYINT" property="csChatFirstFlag" />
    <result column="is_pes" jdbcType="BIT" property="isPes" />
    <result column="is_team_pes" jdbcType="BIT" property="isTeamPes" />
    <result column="is_next_day_pes" jdbcType="BIT" property="isNextDayPes" />
    <result column="is_assist" jdbcType="BIT" property="isAssist" />
    <result column="is_after_sale" jdbcType="BIT" property="isAfterSale" />
    <result column="is_order_created" jdbcType="BIT" property="isOrderCreated" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
    <result column="chat_num" jdbcType="INTEGER" property="chatNum" />
    <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
    <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate" />
	<result column="is_cross_chat_filter" jdbcType="BIT" property="crossChatFilter" />
	<result column="is_cross_chat" jdbcType="BIT" property="crossChat" />

  </resultMap>

   <resultMap id="CsOrderBindChatpeerDTO" type="com.pes.jd.model.DTO.CsOrderBindChatpeerDTO" >
 	<id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_flag" jdbcType="TINYINT" property="chatFlag" />
    <result column="forward_flag" jdbcType="TINYINT" property="forwardFlag" />
    <result column="is_watchword_buyer" jdbcType="BIT" property="isWatchwordBuyer" />
    <result column="is_filtered_buyer" jdbcType="BIT" property="isFilteredBuyer" />
    <result column="is_cs_single_chat_filter" jdbcType="BIT" property="isCsSingleChatFilter" />
    <result column="is_consult" jdbcType="BIT" property="isConsult" />
    <result column="is_receive" jdbcType="BIT" property="isReceive" />
    <result column="is_enquiry" jdbcType="BIT" property="isEnquiry" />
    <result column="is_cs_consult_first" jdbcType="BIT" property="isCsConsultFirst" />
    <result column="is_pes" jdbcType="BIT" property="isPes" />
    <result column="is_team_pes" jdbcType="BIT" property="isTeamPes" />
    <result column="is_next_day_pes" jdbcType="BIT" property="isNextDayPes" />
    <result column="is_assist" jdbcType="BIT" property="isAssist" />
    <result column="is_after_sale" jdbcType="BIT" property="isAfterSale" />
    <result column="is_order_created" jdbcType="BIT" property="isOrderCreated" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
	<result column="is_cross_chat" jdbcType="BIT" property="crossChat" />
  </resultMap>

  <resultMap id="EnquiryChatpeerDTO" type="com.pes.jd.model.DTO.EnquiryChatpeerDTO" >
     <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_flag" jdbcType="TINYINT" property="chatFlag" />
    <result column="forward_flag" jdbcType="TINYINT" property="forwardFlag" />
    <result column="is_watchword_buyer" jdbcType="BIT" property="isWatchwordBuyer" />
    <result column="is_filtered_buyer" jdbcType="BIT" property="isFilteredBuyer" />
    <result column="is_cs_single_chat_filter" jdbcType="BIT" property="isCsSingleChatFilter" />
    <result column="is_consult" jdbcType="BIT" property="isConsult" />
    <result column="is_receive" jdbcType="BIT" property="isReceive" />
    <result column="is_enquiry" jdbcType="BIT" property="isEnquiry" />
    <result column="cs_chat_first_flag" jdbcType="TINYINT" property="csChatFirstFlag" />
    <result column="is_pes" jdbcType="BIT" property="isPes" />
    <result column="is_team_pes" jdbcType="BIT" property="isTeamPes" />
    <result column="is_next_day_pes" jdbcType="BIT" property="isNextDayPes" />
    <result column="is_assist" jdbcType="BIT" property="isAssist" />
    <result column="is_after_sale" jdbcType="BIT" property="isAfterSale" />
    <result column="is_order_created" jdbcType="BIT" property="isOrderCreated" />
    <result column="chat_num" jdbcType="INTEGER" property="chatNum" />
    <result column="buyer_chat_num" jdbcType="INTEGER" property="buyerChatNum" />
    <result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
    <result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate" />
  </resultMap>

  <resultMap id="PesCalChatpeerDTO" type="com.pes.jd.model.DTO.PesCalChatpeerDTO" >
     <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_flag" jdbcType="TINYINT" property="chatFlag" />
    <result column="forward_flag" jdbcType="TINYINT" property="forwardFlag" />
    <result column="is_watchword_buyer" jdbcType="BIT" property="isWatchwordBuyer" />
    <result column="is_filtered_buyer" jdbcType="BIT" property="isFilteredBuyer" />
    <result column="is_cs_single_chat_filter" jdbcType="BIT" property="isCsSingleChatFilter" />
    <result column="is_consult" jdbcType="BIT" property="isConsult" />
    <result column="is_receive" jdbcType="BIT" property="isReceive" />
    <result column="is_enquiry" jdbcType="BIT" property="isEnquiry" />
    <result column="is_cs_consult_first" jdbcType="BIT" property="isCsConsultFirst" />
    <result column="is_pes" jdbcType="BIT" property="isPes" />
    <result column="is_team_pes" jdbcType="BIT" property="isTeamPes" />
    <result column="is_next_day_pes" jdbcType="BIT" property="isNextDayPes" />
    <result column="is_assist" jdbcType="BIT" property="isAssist" />
    <result column="is_after_sale" jdbcType="BIT" property="isAfterSale" />
  </resultMap>


  <resultMap id="SimpleChatpeerDTO" type="com.pes.jd.model.DTO.SimpleChatpeerDTO" >
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
		<result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
		<result column="date" jdbcType="DATE" property="date" />
	    <result column="is_after_sale" jdbcType="BIT" property="afterSale" />
	    <result column="cs_chat_first_flag" jdbcType="TINYINT" property="csChatFirstFlag" />
	    <result column="is_cross_chat_filter" jdbcType="BIT" property="crossChatFilter" />
	    <result column="is_cross_chat" jdbcType="BIT" property="crossChat" />
	</resultMap>


  <sql id="base_field" >
    	shop_id, cs_nick, buyer_nick, date, chat_flag, forward_flag, is_watchword_buyer,
    is_filtered_buyer, is_cs_single_chat_filter, is_consult, is_receive, is_enquiry,
    is_cs_consult_first, is_pes, is_team_pes, is_next_day_pes, is_assist, is_after_sale,is_order_created
  </sql>

  <insert id="batchInsertChatpeer" parameterType="map">
	INSERT INTO ${tableName}(shop_id, cs_nick,
		buyer_nick, date)
	VALUES
	<foreach collection="chatpeerLst" item="itm" separator="," >
	   	(
	   	  #{itm.shopId,jdbcType=BIGINT},
	      #{itm.csNick,jdbcType=VARCHAR},
	      #{itm.buyerNick,jdbcType=VARCHAR},
	      #{itm.date,jdbcType=DATE}
      	)
      </foreach>
	</insert>

	<delete id="deleteChatpeerByShopIdAndDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date = #{date}
	</delete>

	<delete id="deleteChatpeerByShopIdAndDateRange" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date BETWEEN #{startDate} AND #{endDate}
	</delete>


 	<update id="updateChatpeerInfoForCommonChat" parameterType="map" >
	  <foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
		  UPDATE ${tableName}
		  SET
			  chat_flag = #{itm.chatFlag,jdbcType=TINYINT},
			  forward_flag = #{itm.forwardFlag,jdbcType=TINYINT},
			  forward_filte_flag = #{itm.forwardFilteFlag,jdbcType=TINYINT},
			  is_watchword_buyer = #{itm.isWatchwordBuyer,jdbcType=BIT},
			  is_filtered_buyer = #{itm.isFilteredBuyer,jdbcType=BIT},
			  is_cs_single_chat_filter = #{itm.isCsSingleChatFilter,jdbcType=BIT},
			  is_cust_single_chat_filter = #{itm.isCustSingleChatFilter,jdbcType=BIT},
			  is_consult = #{itm.isConsult,jdbcType=BIT},
			  is_receive = #{itm.isReceive,jdbcType=BIT},
			  is_enquiry = #{itm.isEnquiry,jdbcType=BIT},
			  is_cs_consult_first = #{itm.isCsConsultFirst,jdbcType=BIT},
		      cs_chat_first_flag = #{itm.csChatFirstFlag,jdbcType=INTEGER},
			  cs_active_chat_fail = #{itm.csActiveChatFail,jdbcType=INTEGER},
			  cs_active_urgepay_fail = #{itm.csActiveUrgepayFail,jdbcType=INTEGER},
			  is_pes = #{itm.isPes,jdbcType=BIT},
			  is_team_pes = #{itm.isTeamPes,jdbcType=BIT},
			  is_next_day_pes = #{itm.isNextDayPes,jdbcType=BIT},
			  is_assist = #{itm.isAssist,jdbcType=BIT},
			  is_after_sale = #{itm.isAfterSale,jdbcType=BIT},
			  is_order_created = #{itm.isOrderCreated,jdbcType=BIT},
			  is_cs_offline_msg_filter = #{itm.isCsOfflineMsgFilter,jdbcType=BIT},
			  is_ma_auto_reply_filter = #{itm.isMaAutoReplyFilter,jdbcType=BIT},
			  chat_num = #{itm.chatNum,jdbcType=INTEGER},
			  buyer_chat_num = #{itm.buyerChatNum,jdbcType=INTEGER},
			  first_chat_date = #{itm.firstChatDate,jdbcType=TIMESTAMP},
			  last_chat_date = #{itm.lastChatDate,jdbcType=TIMESTAMP},
		      is_cross_chat_filter = #{itm.crossChatFilter,jdbcType=BIT},
		  	  is_cust_leave_message_filter = #{itm.isCustLeaveMessageFilter,jdbcType=BIT}
		  WHERE
		  	  id = #{itm.id,jdbcType=BIGINT}
	  </foreach>
    </update>


	<update id="updateChatpeersCrossChat" parameterType="map" >
		UPDATE ${tableName}
		SET is_cross_chat = 1
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND cs_nick = #{csNick,jdbcType=VARCHAR}
		AND date = #{date,jdbcType=DATE}
		AND buyer_nick IN
		<foreach collection="crossChatBuyerNickLst" item="buyerNick" open="(" close=")" separator=",">
			#{buyerNick,jdbcType=VARCHAR}
		</foreach>
	</update>

	<update id="updateChatpeersCrossChatRecount" parameterType="map" >
		UPDATE ${tableName}
		SET is_cross_chat = 0
		WHERE
		shop_id = #{shopId,jdbcType=BIGINT}
		AND cs_nick = #{csNick,jdbcType=VARCHAR}
		AND date = #{date,jdbcType=DATE}
		AND buyer_nick IN
		<foreach collection="crossChatBuyerNickLst" item="buyerNick" open="(" close=")" separator=",">
			#{buyerNick,jdbcType=VARCHAR}
		</foreach>
	</update>

	<update id="updateShopForwardChatpeerInfoForCommonChat" parameterType="map" >

		<foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
			UPDATE ${tableName}
			<set>
				<if test="itm.forward_flag != null">
					forward_flag = #{itm.forward_flag,jdbcType=INTEGERBIT}
				</if>
			</set>
			<where>
				id = #{itm.id,jdbcType=BIGINT}
			</where>
		</foreach>

	</update>

  <update id="updateChatpeerInfoForCsOrderIndex" parameterType="map" >

	  <foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		<set>
		  <if test="itm.isWatchwordBuyer != null">
	        is_watchword_buyer = #{itm.isWatchwordBuyer,jdbcType=BIT},
	      </if>
	      <if test="itm.isFilteredBuyer != null">
	        is_filtered_buyer = #{itm.isFilteredBuyer,jdbcType=BIT},
	      </if>
	      <if test="itm.isCsSingleChatFilter != null">
	        is_cs_single_chat_filter = #{itm.isCsSingleChatFilter,jdbcType=BIT}
	      </if>
	    </set>
    	<where>
    		id = #{itm.id,jdbcType=BIGINT}
    	</where>
	  </foreach>

    </update>

  <update id="updateChatpeerInfoForEnquiry" parameterType="map" >

	  <foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		  <set>
			  <if test="itm.isEnquiry != null">
				  is_enquiry = #{itm.isEnquiry,jdbcType=BIT},
			  </if>
			  <if test="itm.isAfterSale != null">
				  is_after_sale = #{itm.isAfterSale,jdbcType=BIT},
			  </if>
			  <if test="itm.isOrderCreated != null">
				  is_order_created = #{itm.isOrderCreated,jdbcType=BIT},
			  </if>
			  <if test="itm.isAssist != null">
				  is_assist = #{itm.isAssist,jdbcType=BIT},
			  </if>
			  <if test="itm.csActiveChatFail != null">
				  cs_active_chat_fail = #{itm.csActiveChatFail,jdbcType=BIT},
			  </if>
			  <if test="itm.csActiveUrgepayFail != null">
				  cs_active_urgepay_fail = #{itm.csActiveUrgepayFail,jdbcType=BIT},
			  </if>
			  <if test="itm.crossChatFail != null">
				  cross_chat_fail = #{itm.crossChatFail,jdbcType=BIT}
			  </if>

		  </set>
    	WHERE
    		id = #{itm.id,jdbcType=BIGINT}
	  </foreach>

    </update>



	<update id="updateChatpeerInfoForFinalData" parameterType="map" >

		<foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
			UPDATE ${tableName}
			<set>
				<if test="itm.orderCreated != null">
					is_order_created = #{itm.orderCreated,jdbcType=BIT},
				</if>
				<if test="itm.csActiveChatFail != null">
					cs_active_chat_fail = #{itm.csActiveChatFail,jdbcType=BIT},
				</if>
				<if test="itm.csActiveUrgepayFail != null">
					cs_active_urgepay_fail = #{itm.csActiveUrgepayFail,jdbcType=BIT},
				</if>
				<if test="itm.crossChatFail != null">
					cross_chat_fail = #{itm.crossChatFail,jdbcType=BIT},
				</if>
				<if test="itm.enquiry != null">
					is_enquiry = #{itm.enquiry,jdbcType=BIT}
				</if>

			</set>
			WHERE
				id = #{itm.id,jdbcType=BIGINT}
		</foreach>

	</update>


	<update id="batchUpdateCsAfterSaleReceive" parameterType="map" >

		<foreach collection="aftersaleReceiveLst" item="itm" open="" close="" separator=";">
			UPDATE ${tableName}
			SET
			   is_after_sale = #{itm.afterSale,jdbcType=BIT}
			WHERE
				shop_id = #{itm.shopId,jdbcType=BIGINT}
			 AND cs_nick = #{itm.csNick,jdbcType=VARCHAR}
			 AND buyer_nick = #{itm.buyerNick,jdbcType=VARCHAR}
			 AND date = #{itm.date,jdbcType=DATE}
		</foreach>

	</update>

  <update id="updateChatpeerInfoForCsOrderBind" parameterType="map" >

	  <foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		<set>
	      <if test="itm.isConsult != null">
	        is_consult = #{itm.isConsult,jdbcType=BIT},
	      </if>
	      <if test="itm.isReceive != null">
	        is_receive = #{itm.isReceive,jdbcType=BIT},
	      </if>
	      <if test="itm.isEnquiry != null">
	        is_enquiry = #{itm.isEnquiry,jdbcType=BIT},
	      </if>
	      <if test="itm.isAfterSale != null">
	        is_after_sale = #{itm.isAfterSale,jdbcType=BIT},
	      </if>
	      <if test="itm.isOrderCreated != null">
	        is_order_created = #{itm.isOrderCreated,jdbcType=BIT},
	      </if>
	      <if test="itm.isNextDayPes != null">
	        is_next_day_pes = #{itm.isNextDayPes,jdbcType=BIT},
	      </if>
	      <if test="itm.isCsConsultFirst != null">
	        is_cs_consult_first = #{itm.isCsConsultFirst,jdbcType=BIT},
	      </if>
	      <if test="itm.isAssist != null">
	        is_assist = #{itm.isAssist,jdbcType=BIT}
	      </if>
<!-- 	      <if test="itm.isPes != null"> -->
<!-- 	        is_pes = #{itm.isPes,jdbcType=BIT}, -->
<!-- 	      </if> -->
	<!--       <if test="isTeamPes != null"> -->
	<!--         is_team_pes = #{isTeamPes,jdbcType=BIT}, -->
	<!--       </if> -->

	    </set>
    	WHERE
    		id = #{itm.id,jdbcType=BIGINT}
	  </foreach>

    </update>


    <update id="updateChatpeerInfoForCrossDayChat" parameterType="map" >
	  <foreach collection="chatpeerLst" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		<set>
	       <if test="itm.crossChat != null">
	        cross_chat = #{itm.crossChat,jdbcType=INTEGER}
	      </if>
	    </set>
    	<where>
    		cs_nick = #{itm.csNick, jdbcType=VARCHAR}
    		AND buyer_nick = #{itm.buyerNick, jdbcType=VARCHAR}
    	</where>
	  </foreach>

    </update>

    <update id="updateChatPeerInfoIsEnquiryStatusByIdLst" parameterType="map">
		<foreach collection="ids" item="id" open="" close="" separator=";">
			UPDATE ${tableName}
			SET is_enquiry = 0
			WHERE id = #{id}
		</foreach>
	</update>

  <select id="selectShopCsChatpeerLstByDate" resultMap="CommonCsChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date,
    	chat_flag,forward_flag,is_filtered_buyer,is_watchword_buyer,is_cs_single_chat_filter
	FROM ${tableName}
	WHERE
	 	shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
  </select>


  <select id="selectShopCsEnquiryBuyerNickLstByDate" resultType="String" >
    SELECT
    	distinct buyer_nick
	FROM ${tableName}
	WHERE
	 	shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
  </select>

  <select id="selectShopCsChatpeerLstOfReceiveByDate" resultMap="CsOrderBindChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date, is_order_created,is_filtered_buyer,is_watchword_buyer,is_cs_single_chat_filter
	FROM ${tableName}
	WHERE
	 	shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
	AND chat_flag = 0
  </select>

  <select id="selectReceiveChatpeersByShopAndDateForCsOrderIndex" resultMap="CsOrderIndexChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date,buyer_chat_num
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date BETWEEN #{startDate} AND #{endDate}
	AND is_receive = 1
  </select>

  <select id="selectShopChatpeerLstByDateForCsOrderBind" resultMap="CsOrderBindChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date, is_order_created
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	date = #{date}
	AND chat_flag = 0
  </select>

  <select id="selectShopCsFirstToConsultBuyerNickLst" resultMap="EnquiryChatpeerDTO" >
    SELECT shop_id,cs_nick,buyer_nick,date,is_enquiry,is_order_created,
    		chat_num,first_chat_date,last_chat_date
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
	AND is_receive = 1
	AND cs_chat_first_flag = 1
  </select>

  <select id="selectShopCsChatpeerLstByDateForEnquiry" resultMap="ReceivedChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date, cs_chat_first_flag,is_receive,forward_flag,
    	is_order_created,is_filtered_buyer,is_watchword_buyer,
			is_cs_single_chat_filter,is_cross_chat_filter,
			is_cross_chat,first_chat_date,last_chat_date
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
	AND is_receive = 1
  </select>

  <select id="selectShopCsOrderUnCreatedChatpeerLst" resultMap="SimpleChatpeerDTO" >
    SELECT
    	id, cs_nick, buyer_nick, date, is_after_sale,cs_chat_first_flag,is_cross_chat_filter,is_cross_chat
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
	AND is_order_created = 0
  </select>

  <select id="selectShopCsReceivedChatpeerLstByDateForCsOrderBind" resultMap="CsOrderBindChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date, is_enquiry,
    	is_order_created,is_filtered_buyer,is_watchword_buyer,is_cs_single_chat_filter,is_cross_chat,is_receive
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date BETWEEN #{startDate} AND #{endDate}
	AND is_receive = 1
  </select>

  <select id="selectConsultByDateAndCsNickAndBuyer" resultMap="CsOrderBindChatpeerDTO" >
    SELECT
    	id,shop_id, cs_nick, buyer_nick, date, is_receive, is_order_created,is_filtered_buyer,is_watchword_buyer,is_cs_single_chat_filter
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	buyer_nick IN
	  <foreach collection="buyerNicks" item="nick" open="(" close=")" separator=",">
		  #{nick}
	  </foreach>
	AND date between #{startDate} and #{endDate}
	AND is_consult = 1
  </select>

  <select id="selectEnquiryChatpeerLst" resultMap="EnquiryChatpeerDTO" >
    SELECT
    	shop_id, cs_nick, buyer_nick, date,
    	chat_flag,is_filtered_buyer,is_watchword_buyer,is_cs_single_chat_filter,is_pes,is_team_pes,is_next_day_pes
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
	AND chat_flag = 0
  </select>

  <select id="selectShopCsChatpeerLstForCsPesCal" resultMap="PesCalChatpeerDTO" >
    SELECT
    	shop_id,
    	cs_nick,
    	buyer_nick,
    	chat_flag,
    	is_consult,
    	is_receive,
    	is_enquiry,
    	is_assist,
    	forward_flag,
    	is_filtered_buyer,
    	is_watchword_buyer,
    	is_cs_single_chat_filter
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date = #{date}
  </select>

  <select id="selectShopCsForwordOutBuyerByDate" resultType="string" >
    SELECT
    	shop_id, cs_nick, buyer_nick, date,
    	chat_flag,is_filtered_buyer,is_watchword_buyer,is_cs_single_chat_filter
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date BETWEEN #{startDate} AND #{endDate}
	AND forward_flag = 2
  </select>

  <select id="selectTargetDateReceiveBuyersByBuyerNickLst" resultType="string" >
    SELECT
    	buyer_nick
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND	cs_nick = #{csNick}
	AND date =#{date}
	AND forward_flag = 2
  </select>

  <select id="selectShopCsChatpeerLstByDateForEnquiryLoss" resultMap="EnquiryChatpeerDTO" >
    SELECT id,shop_id,cs_nick,buyer_nick,date,is_enquiry,is_order_created,
    		chat_num,first_chat_date,last_chat_date,is_cs_consult_first,cs_chat_first_flag
	FROM ${tableName}
	WHERE
		shop_id = #{shopId}
	AND cs_nick IN
	<foreach collection="csLst" item="cs" open="(" close=")" separator=",">
		#{cs.nick}
	</foreach>
	AND date = #{date}
	AND is_enquiry = 1
  </select>

	<!-- 按日期查询当天是否存在客服与买家的聊天关系 -->
	<select id="selectCustFirstReplyByCsByCustomerByDate" resultMap="EnquiryChatpeerDTO" >
		SELECT id,shop_id,cs_nick,buyer_nick,date,cs_chat_first_flag,buyer_chat_num
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND cs_nick = #{csNick}
		AND date = #{date}
		AND buyer_nick IN
		<foreach collection="enquiryChatpeerLst" item="chatpeer" open="(" close=")" separator=",">
			#{chatpeer.buyerNick}
		</foreach>
	</select>

	<select id="selectChatPeerLstByDateByCsNickForReceive" resultType="java.lang.String">
		SELECT 	buyer_nick
     	FROM ${tableName}
     <where>
     	shop_id=#{shopId}
     	AND date=#{date}
     	AND cs_nick=#{csNick}
     	AND is_receive=1
     </where>
	</select>

	<select id="searchFillterBuyer" resultType="string">
		SELECT buyer_nick
		FROM ${tableName}
		where shop_id = #{shopId}
		and date = #{date}
		and cs_nick = #{nick}
		and is_receive = 0;
	</select>
	  <select id="selectChatPeerIdsByShopIdByDate" resultType="java.lang.Long" >
    SELECT
    	id
	FROM ${tableName}
	WHERE
	 	shop_id = #{shopId}
	AND date = #{date}
  </select>

  <delete id="deleteChatpeerByIds" parameterType="map">
		DELETE FROM ${tableName}
		WHERE
		 id in
		<foreach collection="ids" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</delete>

    <select id="selectChatPeerByShopIdByDate" resultMap="CommonCsChatpeerDTO">
        SELECT shop_id, cs_nick, buyer_nick, date, is_receive, is_enquiry
        FROM ${tableName}
        <where>
            shop_id=#{shopId}
            AND date=#{date}
        </where>
    </select>

    <select id="selectByShopIdAndDateAndCsNicks" resultMap="CommonCsChatpeerDTO">
        SELECT cs_nick, buyer_nick, is_consult, is_receive, is_enquiry
        FROM ${tableName}
        WHERE shop_id=#{shopId,jdbcType=BIGINT}
        AND `date`=#{date}
        AND cs_nick IN
        <foreach collection="csNicks" item="csNick" open="(" close=")" separator=",">
            #{csNick,jdbcType=VARCHAR}
        </foreach>
    </select>

	<select id="selectcsChatFirstChatPeerLstByDateByCsNick" resultMap="SimpleChatpeerDTO">
		SELECT cs_nick, buyer_nick, date
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND	cs_nick = #{csNick}
		AND date between #{startDate} and #{endDate}
		AND cs_chat_first_flag = 1
	</select>
    <select id="selectFilterChatPeer" resultMap="CommonCsChatpeerDTO">
		SELECT
			shop_id,
			cs_nick,
			buyer_nick,
			date,
			first_chat_date,
			last_chat_date
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND	cs_nick = #{csNick}
		and buyer_nick = #{buyNick}
		AND date between #{startDate} AND #{endDate}
		AND(is_cs_single_chat_filter = 1
		OR forward_filte_flag = 1)
	</select>

   <select id="selectShopCsReceivedChatpeerLstByDateForCsOrderBindByCsNick" resultMap="CsOrderBindChatpeerDTO">
	   SELECT
	   shop_id, cs_nick, buyer_nick, date
	   FROM ${tableName}
	   WHERE
	   shop_id = #{shopId}
	   AND cs_nick in
	   <foreach collection="csLst" open="(" close=")" separator="," item="csNick">
		   #{csNick}
	   </foreach>
	   AND date BETWEEN #{startDate} AND #{endDate}
	   AND is_receive = 1
   </select>

   <select id="selectReceiveChatpeersByShopAndDateAndBuyerAndCustomer" resultMap="ReceivedChatpeerDTO">
	   SELECT
	   id,shop_id, cs_nick, buyer_nick, date,first_chat_date
	   FROM ${tableName}
	   WHERE
	   shop_id = #{shopId}
	   AND date = #{date}
	   AND is_receive = 1
	   AND
	   <foreach collection="csBuyerOrderIndexLst" open="(" close=")" separator="or" item="csBuyerOrderIndex">
		   buyer_nick=#{csBuyerOrderIndex.buyerNick} and cs_nick =#{csBuyerOrderIndex.csNick}
	   </foreach>
   </select>

   <update id="batchUpdateAfterSale">
	   update ${tableName}
	   SET is_after_sale=1
	   WHERE shop_id=#{shopId}
	   and date=#{date}
	   and id in
	   <foreach collection="afterSaleChatPeerLst" open="(" close=")" separator="," item="afterSaleChatPeer">
		   #{afterSaleChatPeer.id}
	   </foreach>
   </update>

   <select id="selectWatchwordFilterChatPeer" resultMap="CommonCsChatpeerDTO">
	   SELECT
	   shop_id, cs_nick, buyer_nick, date
	   FROM ${tableName}
	   WHERE
	   shop_id = #{shopId}
	   AND cs_nick =#{csNick}
	   AND buyer_nick =#{buyerNick}
	   AND date BETWEEN #{startDate} AND #{endDate}
	   AND is_watchword_buyer = 1
	</select>

   <select id="selectReceiveFilterChatPeerByDateAndCsNickAndBuyerLst" resultMap="CommonCsChatpeerDTO">
       select
       shop_id, cs_nick, buyer_nick, date, first_chat_date,last_chat_date
	   from ${tableName}
       where
       shop_id = #{shopId}
       AND cs_nick =#{csNick}
       AND buyer_nick in
       <foreach collection="buyerNickLst" open="(" close=")" separator="," item="buyerNick">
           #{buyerNick}
       </foreach>
       AND date BETWEEN #{startDate} AND #{endDate}
       AND is_receive != 1
   </select>

	<select id="selectBuyerNickByDate" resultType="com.pes.jd.model.DTO.CommonCsChatpeerDTO">
		select
		  DISTINCT buyer_nick
		from ${tableName}
		where
		shop_id = #{shopId}
		AND date BETWEEN #{startDate} AND #{endDate}
	</select>
</mapper>
