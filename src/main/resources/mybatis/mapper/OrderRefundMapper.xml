<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderRefundMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.OrderRefundDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="refund_id" property="refundId" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="modified" property="modified" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="refund_amount" property="refundAmount" jdbcType="DOUBLE" />
    <result column="refund_num" property="refundNum" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, refund_id, order_id, shop_id, created, modified, status, refund_amount, refund_num
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pes_order_refund
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pes_order_refund
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.OrderRefundDO" >
    insert into pes_order_refund (id, refund_id, order_id, 
      shop_id, created, modified, 
      status, refund_amount, refund_num
      )
    values (#{id,jdbcType=BIGINT}, #{refundId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{shopId,jdbcType=BIGINT}, #{created,jdbcType=TIMESTAMP}, #{modified,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=VARCHAR}, #{refundAmount,jdbcType=DOUBLE}, #{refundNum,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.OrderRefundDO" >
    insert into pes_order_refund
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="refundId != null" >
        refund_id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="created != null" >
        created,
      </if>
      <if test="modified != null" >
        modified,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="refundAmount != null" >
        refund_amount,
      </if>
      <if test="refundNum != null" >
        refund_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="refundId != null" >
        #{refundId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="created != null" >
        #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null" >
        #{refundAmount,jdbcType=DOUBLE},
      </if>
      <if test="refundNum != null" >
        #{refundNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.OrderRefundDO" >
    update pes_order_refund
    <set >
      <if test="refundId != null" >
        refund_id = #{refundId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="modified != null" >
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null" >
        refund_amount = #{refundAmount,jdbcType=DOUBLE},
      </if>
      <if test="refundNum != null" >
        refund_num = #{refundNum,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.OrderRefundDO" >
    update pes_order_refund
    set refund_id = #{refundId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      created = #{created,jdbcType=TIMESTAMP},
      modified = #{modified,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=DOUBLE},
      refund_num = #{refundNum,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <delete id="deleteOrderRefundByRids" parameterType="java.util.Map">
		DELETE FROM ${tableName}
		WHERE refund_id IN
		<foreach collection="rids" index="index" item="rid" open="("
			separator="," close=")">
			#{rid}
		</foreach>
   </delete>

	<delete id="deleteOrderRefundByOrderIds" parameterType="java.util.Map">
		DELETE FROM ${tableName}
		WHERE status = '3' and reason='快退拦截退款' and order_id IN
		<foreach collection="oids" index="index" item="oid" open="("
				 separator="," close=")">
			#{oid}
		</foreach>
	</delete>
	
	<insert id="persistOrderRefundByFile" parameterType="java.util.Map">
		load data local
		infile #{filePath} into table ${tableName}
		fields terminated by
		'``MYPES`' optionally enclosed by '' escaped by ''
		lines terminated by '`MYPES`\n'
		(refund_id, order_id, shop_id, created, modified, status, refund_amount, buyer_nick, reason, refund_num, type);
	</insert>
	
	<insert id="persistAscServiceOrderRefundByFile" parameterType="java.util.Map">
		load data local
		infile #{filePath} into table ${tableName}
		fields terminated by
		'``MYPES`' optionally enclosed by '' escaped by ''
		lines terminated by '`MYPES`\n'
		(refund_id, order_id, shop_id, created, modified, status, refund_amount, buyer_nick, reason, refund_num, type, sku_id, service_id, afsApply_id, complete_time);
	</insert>
	
	 <select id = "queryCsRelatedApplyRefund" parameterType="map" resultType="com.pes.jd.model.DTO.CsOrderRefundDTO">
		SELECT 
			ptr.order_id as orderId,
			pct.cs_nick as csNick,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount, 
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			ptr.type
		FROM 
		(
		<foreach collection="csTableNames" item="csTableName"  index="index" open="" close="" separator="UNION ALL">
		 SELECT type, shop_id, order_id, cs_nick, is_pes_order
		 FROM ${csTableName} 
		 WHERE 
		   shop_id = #{shopId} AND is_pes_order = 1
		 </foreach>
		) pct,	
	    ${orderRefundTableName} ptr
		WHERE ptr.shop_id = #{shopId} 
		AND ptr.created BETWEEN #{startDate} AND #{endDate}
		AND pct.order_id = ptr.order_id
	</select>
	
	
	<select id="queryCsRelatedCompletedRefund" parameterType="map" resultType="com.pes.jd.model.DTO.CsOrderRefundDTO">
		SELECT 
		    ptr.order_id as orderId,
			pct.cs_nick as csNick,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount, 
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			TIMESTAMPDIFF(SECOND,ptr.created,ptr.modified) as totalRefundDuration,
			ptr.type
		FROM 
		(
		<foreach collection="csTableNames" item="csTableName"  index="index" open="" close="" separator="UNION ALL">
		 SELECT type, shop_id, order_id, cs_nick, is_pes_order
		 FROM ${csTableName} 
		 WHERE 
		    shop_id = #{shopId} AND is_pes_order = 1
		</foreach>
		) pct,
		${orderRefundTableName} ptr
		WHERE 
		 pct.order_id = ptr.order_id
		AND ptr.shop_id = #{shopId}
	    AND ptr.modified BETWEEN #{startDate} AND #{endDate}
	    AND ptr.status = 3
	</select>
	
	
	<select id="queryCsRelatedCompletedAscServiceRefund" parameterType="map" resultType="com.pes.jd.model.DTO.CsOrderRefundDTO">
		SELECT 
		    ptr.order_id as orderId,
			pct.cs_nick as csNick,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount, 
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			TIMESTAMPDIFF(SECOND,ptr.created,ptr.complete_time) as totalRefundDuration,
			ptr.type
		FROM 
		(
		<foreach collection="csTableNames" item="csTableName"  index="index" open="" close="" separator="UNION ALL">
		 SELECT type, shop_id, order_id, cs_nick, is_pes_order
		 FROM ${csTableName} 
		 WHERE 
		    shop_id = #{shopId} AND is_pes_order = 1
		</foreach>
		) pct,
		${orderRefundTableName} ptr
		WHERE 
		 pct.order_id = ptr.order_id
		AND ptr.shop_id = #{shopId}
	    AND ptr.complete_time BETWEEN #{startDate} AND #{endDate}
	    AND ptr.status = 13
	</select>
	
		
	<select id ="selectShopApplyRefunds" parameterType="map" resultType="com.pes.jd.model.DTO.ShopOrderRefundDTO">
		SELECT 
			ptr.order_id as orderId,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount,
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			ptr.type
		FROM ${tableName} ptr
		WHERE 
			ptr.shop_id = #{shopId} 
		AND  
			ptr.created BETWEEN #{startDate} AND #{endDate}
	</select>
	
	<select id="selectShopCompletedRefunds" parameterType="map" resultType="com.pes.jd.model.DTO.ShopOrderRefundDTO">
		SELECT 
			ptr.order_id as orderId,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount,
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			TIMESTAMPDIFF(SECOND,ptr.created,ptr.modified) as totalRefundDuration,
			ptr.type
		FROM ${tableName} ptr
		WHERE 
			ptr.shop_id = #{shopId}
	    AND 
	    	ptr.modified BETWEEN #{startDate} AND #{endDate}
	    AND ptr.status = 3
	</select>
	
	<select id="selectShopCompletedAscServiceRefunds" parameterType="map" resultType="com.pes.jd.model.DTO.ShopOrderRefundDTO">
		SELECT 
			ptr.order_id as orderId,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount,
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			TIMESTAMPDIFF(SECOND,ptr.created,ptr.complete_time) as totalRefundDuration,
			ptr.type
		FROM ${tableName} ptr
		WHERE 
			ptr.shop_id = #{shopId}
	    AND 
	    	ptr.complete_time BETWEEN #{startDate} AND #{endDate}
	    AND ptr.status = 13
	</select>
	
	<select id="selectAscOrderRefundByDate" parameterType="map" resultType="com.pes.jd.model.DTO.OrderRefundDTO">
		SELECT 
			ptr.order_id as orderId,
			ptr.buyer_nick as buyerNick,
			ptr.refund_amount as refundAmount,
			ptr.created, 
			ptr.modified,
			ptr.status,
			ptr.refund_num as refundNum,
			ptr.type
		FROM ${tableName} ptr
		WHERE 
			ptr.shop_id = #{shopId}
	    AND 
	    	ptr.created BETWEEN #{startDate} AND #{endDate}
	    AND ptr.type = 2 
	    AND ptr.status != 13
	</select>

	<select id="selectAscOrderRefundByDateAndOrderIds" parameterType="map" resultType="com.pes.jd.model.DTO.OrderRefundDTO">
		SELECT
			ptr.order_id as orderId
		FROM ${tableName} ptr
		WHERE
			ptr.shop_id = #{shopId}
		  AND
			ptr.created BETWEEN #{startDate} AND #{endDate}
		  AND
		    ptr.order_id IN
			<foreach collection="orderIds" index="index" item="oid" open="("
					 separator="," close=")">
				#{oid}
			</foreach>
	</select>

	<insert id="persistOrderRefundBySQL" parameterType="com.pes.jd.model.DO.OrderRefundDO">
		INSERT INTO ${tableName} (
		refund_id,
		order_id,
		shop_id,
		created,
		modified,
		status,
		refund_amount,
		refund_num,
		buyer_nick,
		reason,
		type)
		VALUES
		<foreach collection="list" item="itm" separator=",">
			(
			#{itm.refundId,jdbcType=BIGINT},
			#{itm.orderId,jdbcType=BIGINT},
			#{itm.shopId,jdbcType=BIGINT},
			#{itm.created,jdbcType=TIMESTAMP},
			#{itm.modified,jdbcType=TIMESTAMP},
			#{itm.status,jdbcType=VARCHAR},
			#{itm.refundAmount,jdbcType=DOUBLE},
			#{itm.refundNum,jdbcType=INTEGER},
			#{itm.buyerNick,jdbcType=VARCHAR},
			#{itm.reason,jdbcType=VARCHAR},
			#{itm.type,jdbcType=INTEGER}
			)
		</foreach>
	</insert>
</mapper>