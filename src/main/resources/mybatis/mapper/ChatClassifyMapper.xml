<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ChatClassifyMapper">



    <sql id="Base_Column_List">
        id, shop_id, date, sid, chatlog_id, classify, classify_extra, buyer_content, cs_content, sku_id, total_tokens, created
    </sql>


    <insert id="insertchatClassify" parameterType="com.pes.jd.model.DO.ChatClassifyDO">
        INSERT INTO ${chatClassifyTableName} (
            shop_id, date, sid, chatlog_id, classify, classify_extra, buyer_content, cs_content, sku_id, total_tokens, created
        ) VALUES (
                     #{chatClassify.shopId}, #{chatClassify.date}, #{chatClassify.sid}, #{chatClassify.chatlogId},
                     #{chatClassify.classify}, #{chatClassify.classifyExtra}, #{chatClassify.buyerContent},
                     #{chatClassify.csContent}, #{chatClassify.skuId}, #{chatClassify.totalTokens}, #{chatClassify.created}
                 )
    </insert>


    <update id="updateChatClassifyById">
        UPDATE ${chatClassifyTableName}
        SET
            shop_id = #{entry.shopId},
            `date` = #{entry.date},
            sid = #{entry.sid},
            chatlog_id = #{entry.chatlogId},
            classify = #{entry.classify},
            classify_extra = #{entry.classifyExtra},
            buyer_content = #{entry.buyerContent},
            cs_content = #{entry.csContent},
            sku_id = #{entry.skuId},
            total_tokens = #{entry.totalTokens},
            created = #{entry.created}
        WHERE
            id = #{entry.id}
    </update>


    <delete id="delChatClassify">
        DELETE FROM ${chatClassifyTableName}
        WHERE shop_id = #{shopId}
          AND date = #{date}
    </delete>

    <select id="findByShopIdAndClassify" resultType="com.pes.jd.model.DO.ChatClassifyDO">
        SELECT
            *
        FROM
        ${chatClassifyTableName}
        WHERE
        shop_id = #{shopId}
        AND classify = #{category}
        AND date = #{date}
    </select>


</mapper>
