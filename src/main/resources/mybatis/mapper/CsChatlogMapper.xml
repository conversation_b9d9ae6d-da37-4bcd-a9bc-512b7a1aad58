<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsChatlogMapper">

    <resultMap id="CsChatlogDTO" type="com.pes.jd.model.DTO.CsChatlogDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="buyer" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="time" property="chatTime" jdbcType="TIMESTAMP"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="direction" property="direction" jdbcType="INTEGER"/>
        <result column="length" property="length" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="mt" property="mt" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="ChatlogDTO" type="com.pes.jd.model.DTO.ChatlogDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="buyer" property="buyer" jdbcType="VARCHAR"/>
        <result column="time" property="chatTime" jdbcType="TIMESTAMP"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="direction" property="direction" jdbcType="BIT"/>
        <result column="length" property="length" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="BIT"/>
        <result column="sid" property="sid" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="mt" property="mt" jdbcType="TINYINT"/>
    </resultMap>

    <resultMap id="ReceiveQualityChatlogDTO" type="com.pes.jd.model.DTO.ReceiveQualityChatlogDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="buyer" property="buyer" jdbcType="VARCHAR"/>
        <result column="time" property="chatTime" jdbcType="TIMESTAMP"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="direction" property="direction" jdbcType="INTEGER"/>
        <result column="length" property="length" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="mt" property="mt" jdbcType="TINYINT"/>
    </resultMap>


    <resultMap id="OrderChatlogInfoDTO" type="com.pes.jd.model.DTO.OrderChatlogInfoDTO">
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="buyer" property="buyer" jdbcType="VARCHAR"/>
        <result column="time" property="time" jdbcType="TIMESTAMP"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="direction" property="direction" jdbcType="INTEGER"/>
        <result column="length" property="length" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="mt" property="mt" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="base_field">
    id, shop_id, cs_nick, buyer, time, content, direction, length, type, sku_id,sid,mt
  </sql>
    <delete id="deleteCsChatlogById" parameterType="java.lang.Long">
    DELETE FROM pes_cs_chatlog
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteChatlogIdsByShopIdAndDate" parameterType="map">
		DELETE FROM  ${tableName}
		WHERE shop_id = #{shopId}
			AND time BETWEEN #{startDate} AND #{endDate}
	</delete>
    <insert id="insertCsChatlog" parameterType="com.pes.jd.model.DO.CsChatlogDO">
    INSERT INTO pes_cs_chatlog (id, shop_id, cs_nick,
      buyer, time, content,
      direction, length, type)
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{csNick,jdbcType=VARCHAR},
      #{buyer,jdbcType=VARCHAR}, #{time,jdbcType=TIMESTAMP}, #{content,jdbcType=VARCHAR},
      #{direction,jdbcType=BIT}, #{length,jdbcType=INTEGER}, #{type,jdbcType=BIT})
  </insert>
    <insert id="batchInsertCsChatLog" parameterType="map">
        INSERT INTO ${tableName}(time,cs_nick,buyer,content,direction,length,shop_id,type,sid,sku_id,mt)
        VALUES
        <foreach collection="chatLogList" item="item" separator=","
                 index="index">
            (#{item.time},#{item.csNick},#{item.buyer},#{item.content},#{item.direction},#{item.length},#{item.shopId},#{item.type},#{item.sid},#{item.skuId},#{item.mt})
        </foreach>
    </insert>

    <insert id="insertChatlogByFile" parameterType="map">
		load data local
		infile #{filePath} into table ${tableName}
		fields terminated by
		'``MYPES`' optionally enclosed by '' escaped by ''
	    lines terminated by '`MYPES`\n'
	    (time, cs_nick, buyer, content, direction, shop_id, length, type, sid, sku_id,mt);
	</insert>

    <update id="updateCsChatlogById" parameterType="com.pes.jd.model.DO.CsChatlogDO">
        UPDATE pes_cs_chatlog
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="csNick != null">
                cs_nick = #{csNick,jdbcType=VARCHAR},
            </if>
            <if test="buyer != null">
                buyer = #{buyer,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                time = #{time,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="direction != null">
                direction = #{direction,jdbcType=BIT},
            </if>
            <if test="length != null">
                length = #{length,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectBuyerChatlogByShopIdAndDate" parameterType="map" resultMap="ChatlogDTO">
        SELECT
        	shop_id, cs_nick, buyer, time, content,direction,mt
        FROM ${tableName}
        WHERE
          shop_id = #{shopId}
        AND time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectBuyerChatlogByCSNickAndDate" parameterType="map" resultMap="ChatlogDTO">
        SELECT
        shop_id, cs_nick, buyer, time, content,direction,mt
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND buyer in
        <foreach collection="receiveBuyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick}
        </foreach>
        AND cs_nick = #{csNick}
        AND time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="searchAllByTime" resultMap="ChatlogDTO">
        select
        <include refid="base_field"/>
        FROM ${tableName}
        where shop_id = #{shopId}
        and time BETWEEN #{beginDate} AND #{endDate}
        <if test="nick != null">
            and cs_nick = #{nick}
        </if>
        order by time

    </select>

    <select id="selectChatLogLstByCsAndBuyerAndDate" parameterType="map" resultMap="ChatlogDTO">
		SELECT time,cs_nick,direction,type ,content,mt
		FROM ${tableName}
		WHERE
		    shop_id = #{shopId}
		AND buyer = #{buyerNick}
		AND time BETWEEN #{startDate} and #{endDate}
		AND cs_nick != #{csNick}
		ORDER BY time DESC,direction DESC
	</select>

    <select id="selectShopForwardChatLogLstByBuyerLstAndDate" parameterType="map" resultMap="ChatlogDTO">
        SELECT time,cs_nick,direction,`type` ,content,mt
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND buyer IN
        <foreach item="buyerNick" collection="buyerNickLst" close=")" open="(" separator=",">
            #{buyerNick}
        </foreach>
        AND `time` BETWEEN #{startDate} AND #{endDate}
        AND (`type` = 24 or `type` = 25)
        ORDER BY `time` DESC,direction DESC
    </select>

    <select id="selectShopCsChatLogLst" parameterType="map" resultMap="ReceiveQualityChatlogDTO">
		SELECT time,cs_nick,buyer,direction,type,content, mt
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND cs_nick = #{csNick}
		AND time BETWEEN #{startDate} AND #{endDate}
		ORDER BY time ASC,direction DESC
	</select>

    <select id="selectShopCsChatLogLstByShopIdAndSid" parameterType="map" resultMap="CsChatlogDTO">
        SELECT time,id,cs_nick,buyer,direction,type,content,sid,mt
        FROM ${tableName}
        WHERE
            shop_id = #{shopId}
          AND sid = #{sid}
          AND time BETWEEN #{startDate} AND #{endDate}
        ORDER BY time ASC,direction DESC
    </select>

    <select id="selectShopCsChatLogLstForConsultHandle" parameterType="map" resultMap="CsChatlogDTO">
		SELECT time,cs_nick,buyer,direction,type,content,sid,mt
		FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND cs_nick = #{csNick}
		AND time BETWEEN #{startDate} AND #{endDate}
		ORDER BY time ASC,direction DESC
	</select>


    <select id="selectShopCsChatLogLstByBuyerNickLstForConsultHandle" parameterType="map" resultMap="CsChatlogDTO">
        SELECT time,cs_nick,buyer,direction,type,content,sid,mt
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND cs_nick = #{csNick}
        AND buyer IN
        <foreach item="buyerNick" collection="buyerNickLst" open="(" close=")" separator=",">
            #{buyerNick}
        </foreach>
        AND time BETWEEN #{startDate} AND #{endDate}
        ORDER BY time ASC,direction DESC
    </select>

    <select id="selectChatLogLstByCsAndBuyerAndDateForCsOrerIndex" parameterType="map" resultMap="OrderChatlogInfoDTO">
		SELECT time,direction,type,content, mt,cs_nick,buyer
		FROM ${tableName}
		WHERE
		      shop_id = #{shopId}
		  AND buyer = #{buyerNick}
		  AND cs_nick = #{csNick}
		  AND time BETWEEN #{startDate} AND #{endDate}
		  ORDER BY time ASC,direction DESC

	</select>

    <select id="selectChatLogLstByBuyerAndDateForCsOrerIndex" parameterType="map" resultMap="OrderChatlogInfoDTO">
        SELECT time,direction,type,content, mt,cs_nick,buyer
        FROM ${tableName}
        WHERE
            shop_id = #{shopId}
          AND buyer = #{buyerNick}
          AND time BETWEEN #{startDate} AND #{endDate}
        ORDER BY time ASC,direction DESC limit 1

    </select>

    <select id="selectChatLogIdsByShopIdByDate" resultType="java.lang.Long">
	    SELECT
	    	id
		FROM ${tableName}
		WHERE
		 	shop_id = #{shopId}
		AND time  BETWEEN #{startDate} AND #{endDate}
  	</select>

    <select id="selectChatLogByShopIdByDate" resultMap="ChatlogDTO">
	    SELECT
	    	*
		FROM ${tableName}
		WHERE
		 	shop_id = #{shopId}
		AND time  BETWEEN #{startDate} AND #{endDate}
  	</select>

    <delete id="deleteChatLogByIds" parameterType="map">
        DELETE FROM ${tableName}
        WHERE
        id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>
