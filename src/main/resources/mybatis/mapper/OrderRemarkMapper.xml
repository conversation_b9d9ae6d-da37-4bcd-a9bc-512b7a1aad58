<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderRemarkMapper">

	<resultMap id="OrderRemarkDTO" type="com.pes.jd.model.DTO.OrderRemarkDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="order_id" jdbcType="BIGINT" property="orderId" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="modified" jdbcType="TIMESTAMP" property="modified" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="flag" jdbcType="INTEGER" property="flag" />
	</resultMap>
	
	<insert id="batchInsertOrderRemark" parameterType="map">
		INSERT INTO ${tableName} (shop_id,order_id,created,modified,remark,flag)
		VALUES
		<foreach collection="orderRemarkList" item="orderRemark" separator="," index="index">
			(#{orderRemark.shopId},#{orderRemark.orderId},#{orderRemark.created},#{orderRemark.modified},#{orderRemark.remark},#{orderRemark.flag})
		</foreach>
	</insert>
	
	<delete id="batchDeleteOrderRemarkByShopByDate" parameterType="map" >
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND created BETWEEN #{startDate} AND #{endDate}
	</delete>
	
	<update id="updateOrderRemarkByObjList" parameterType="map" >
		<foreach collection="orderRemarkList" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		<set>
	      <if test="itm.modified != null">
	        modified = #{itm.modified},
	      </if>
	      <if test="itm.remark != null">
	        remark = #{itm.remark},
	      </if>
	       <if test="itm.flag != null">
	        flag = #{itm.flag},
	      </if>
	    </set>
    	WHERE order_id = #{itm.orderId}
	  </foreach>
	</update>
	
	<select id="selectOrderRemarkByOrderIdList" parameterType="map" resultType="java.lang.Long">
		SELECT order_id 
		FROM ${tableName}
		WHERE created BETWEEN #{startDate} AND #{endDate}
		AND order_id IN
		<foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
			#{orderId}
		</foreach>
		AND shop_id = #{shopId}
	</select>
	
	
	<select id="selectOrderRemarkInfoByOrderIdList" parameterType="map" resultType="com.pes.jd.model.DTO.OrderRemarkDTO">
		SELECT shop_id, order_id, created, modified, remark, flag
		FROM ${tableName}
		WHERE  order_id IN
		<foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
			#{orderId}
		</foreach>
		AND shop_id = #{shopId}
	</select>

</mapper>