<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopTeamToOrderIndexMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopTeamToOrderIndexDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="TIMESTAMP" property="date" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="to_ordered_paid_num" jdbcType="INTEGER" property="toOrderedPaidNum" />
    <result column="to_ordered_paid_goods_num" jdbcType="INTEGER" property="toOrderedPaidGoodsNum" />
    <result column="to_ordered_paid_order_num" jdbcType="INTEGER" property="toOrderedPaidOrderNum" />
    <result column="to_ordered_paid_amount" jdbcType="DOUBLE" property="toOrderedPaidAmount" />
    <result column="to_ordered_not_paid_num" jdbcType="INTEGER" property="toOrderedNotPaidNum" />
    <result column="to_ordered_not_paid_goods_num" jdbcType="INTEGER" property="toOrderedNotPaidGoodsNum" />
    <result column="to_ordered_not_paid_order_num" jdbcType="INTEGER" property="toOrderedNotPaidOrderNum" />
    <result column="to_ordered_not_paid_amount" jdbcType="DOUBLE" property="toOrderedNotPaidAmount" />
    <result column="to_paid_num" jdbcType="INTEGER" property="toPaidNum" />
    <result column="to_paid_goods_num" jdbcType="INTEGER" property="toPaidGoodsNum" />
    <result column="to_paid_order_num" jdbcType="INTEGER" property="toPaidOrderNum" />
    <result column="to_paid_amount" jdbcType="DOUBLE" property="toPaidAmount" />
    <result column="silent_order_to_paid_num" jdbcType="INTEGER" property="silentOrderToPaidNum" />
    <result column="silent_order_to_paid_goods_num" jdbcType="INTEGER" property="silentOrderToPaidGoodsNum" />
    <result column="silent_order_to_paid_order_num" jdbcType="INTEGER" property="silentOrderToPaidOrderNum" />
    <result column="silent_order_to_paid_amount" jdbcType="DOUBLE" property="silentOrderToPaidAmount" />
    <result column="silent_to_folowup_num" jdbcType="INTEGER" property="silentToFolowupNum" />
    <result column="silent_to_folowup_goods_num" jdbcType="INTEGER" property="silentToFolowupGoodsNum" />
    <result column="silent_to_folowup_order_num" jdbcType="INTEGER" property="silentToFolowupOrderNum" />
    <result column="silent_to_folowup_amount" jdbcType="DOUBLE" property="silentToFolowupAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, date, cs_nick, to_ordered_paid_num, to_ordered_paid_goods_num, to_ordered_paid_order_num, 
    to_ordered_paid_amount, to_ordered_not_paid_num, to_ordered_not_paid_goods_num, to_ordered_not_paid_order_num, 
    to_ordered_not_paid_amount, to_paid_num, to_paid_goods_num, to_paid_order_num, to_paid_amount, 
    silent_order_to_paid_num, silent_order_to_paid_goods_num, silent_order_to_paid_order_num, 
    silent_order_to_paid_amount, silent_to_folowup_num, silent_to_folowup_goods_num, 
    silent_to_folowup_order_num, silent_to_folowup_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_shop_team_to_order_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_shop_team_to_order_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByShopIdDate">
    delete from ${tableName}
    where shop_id = #{shopId} and date = #{date}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ShopTeamToOrderIndexDO">
    insert into ${tableName} (id, shop_id, date,
      cs_nick, to_ordered_paid_num, to_ordered_paid_goods_num, 
      to_ordered_paid_order_num, to_ordered_paid_amount, 
      to_ordered_not_paid_num, to_ordered_not_paid_goods_num, 
      to_ordered_not_paid_order_num, to_ordered_not_paid_amount, 
      to_paid_num, to_paid_goods_num, to_paid_order_num, 
      to_paid_amount, silent_order_to_paid_num, silent_order_to_paid_goods_num, 
      silent_order_to_paid_order_num, silent_order_to_paid_amount, 
      silent_to_folowup_num, silent_to_folowup_goods_num, 
      silent_to_folowup_order_num, silent_to_folowup_amount
      )
    values (#{record.id,jdbcType=BIGINT}, #{record.shopId,jdbcType=BIGINT}, #{record.date,jdbcType=TIMESTAMP},
      #{record.csNick,jdbcType=VARCHAR}, #{record.toOrderedPaidNum,jdbcType=INTEGER}, #{record.toOrderedPaidGoodsNum,jdbcType=INTEGER},
      #{record.toOrderedPaidOrderNum,jdbcType=INTEGER}, #{record.toOrderedPaidAmount,jdbcType=DOUBLE},
      #{record.toOrderedNotPaidNum,jdbcType=INTEGER}, #{record.toOrderedNotPaidGoodsNum,jdbcType=INTEGER},
      #{record.toOrderedNotPaidOrderNum,jdbcType=INTEGER}, #{record.toOrderedNotPaidAmount,jdbcType=DOUBLE},
      #{record.toPaidNum,jdbcType=INTEGER}, #{record.toPaidGoodsNum,jdbcType=INTEGER}, #{record.toPaidOrderNum,jdbcType=INTEGER},
      #{record.toPaidAmount,jdbcType=DOUBLE}, #{record.silentOrderToPaidNum,jdbcType=INTEGER}, #{record.silentOrderToPaidGoodsNum,jdbcType=INTEGER},
      #{record.silentOrderToPaidOrderNum,jdbcType=INTEGER}, #{record.silentOrderToPaidAmount,jdbcType=DOUBLE},
      #{record.silentToFolowupNum,jdbcType=INTEGER}, #{record.silentToFolowupGoodsNum,jdbcType=INTEGER},
      #{record.silentToFolowupOrderNum,jdbcType=INTEGER}, #{record.silentToFolowupAmount,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopTeamToOrderIndexDO">
    insert into pes_shop_team_to_order_index
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="toOrderedPaidNum != null">
        to_ordered_paid_num,
      </if>
      <if test="toOrderedPaidGoodsNum != null">
        to_ordered_paid_goods_num,
      </if>
      <if test="toOrderedPaidOrderNum != null">
        to_ordered_paid_order_num,
      </if>
      <if test="toOrderedPaidAmount != null">
        to_ordered_paid_amount,
      </if>
      <if test="toOrderedNotPaidNum != null">
        to_ordered_not_paid_num,
      </if>
      <if test="toOrderedNotPaidGoodsNum != null">
        to_ordered_not_paid_goods_num,
      </if>
      <if test="toOrderedNotPaidOrderNum != null">
        to_ordered_not_paid_order_num,
      </if>
      <if test="toOrderedNotPaidAmount != null">
        to_ordered_not_paid_amount,
      </if>
      <if test="toPaidNum != null">
        to_paid_num,
      </if>
      <if test="toPaidGoodsNum != null">
        to_paid_goods_num,
      </if>
      <if test="toPaidOrderNum != null">
        to_paid_order_num,
      </if>
      <if test="toPaidAmount != null">
        to_paid_amount,
      </if>
      <if test="silentOrderToPaidNum != null">
        silent_order_to_paid_num,
      </if>
      <if test="silentOrderToPaidGoodsNum != null">
        silent_order_to_paid_goods_num,
      </if>
      <if test="silentOrderToPaidOrderNum != null">
        silent_order_to_paid_order_num,
      </if>
      <if test="silentOrderToPaidAmount != null">
        silent_order_to_paid_amount,
      </if>
      <if test="silentToFolowupNum != null">
        silent_to_folowup_num,
      </if>
      <if test="silentToFolowupGoodsNum != null">
        silent_to_folowup_goods_num,
      </if>
      <if test="silentToFolowupOrderNum != null">
        silent_to_folowup_order_num,
      </if>
      <if test="silentToFolowupAmount != null">
        silent_to_folowup_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="toOrderedPaidNum != null">
        #{toOrderedPaidNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsNum != null">
        #{toOrderedPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNum != null">
        #{toOrderedPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmount != null">
        #{toOrderedPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedNotPaidNum != null">
        #{toOrderedNotPaidNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedNotPaidGoodsNum != null">
        #{toOrderedNotPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedNotPaidOrderNum != null">
        #{toOrderedNotPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedNotPaidAmount != null">
        #{toOrderedNotPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="toPaidNum != null">
        #{toPaidNum,jdbcType=INTEGER},
      </if>
      <if test="toPaidGoodsNum != null">
        #{toPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toPaidOrderNum != null">
        #{toPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toPaidAmount != null">
        #{toPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="silentOrderToPaidNum != null">
        #{silentOrderToPaidNum,jdbcType=INTEGER},
      </if>
      <if test="silentOrderToPaidGoodsNum != null">
        #{silentOrderToPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="silentOrderToPaidOrderNum != null">
        #{silentOrderToPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="silentOrderToPaidAmount != null">
        #{silentOrderToPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="silentToFolowupNum != null">
        #{silentToFolowupNum,jdbcType=INTEGER},
      </if>
      <if test="silentToFolowupGoodsNum != null">
        #{silentToFolowupGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="silentToFolowupOrderNum != null">
        #{silentToFolowupOrderNum,jdbcType=INTEGER},
      </if>
      <if test="silentToFolowupAmount != null">
        #{silentToFolowupAmount,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopTeamToOrderIndexDO">
    update pes_shop_team_to_order_index
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=TIMESTAMP},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="toOrderedPaidNum != null">
        to_ordered_paid_num = #{toOrderedPaidNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidGoodsNum != null">
        to_ordered_paid_goods_num = #{toOrderedPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidOrderNum != null">
        to_ordered_paid_order_num = #{toOrderedPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedPaidAmount != null">
        to_ordered_paid_amount = #{toOrderedPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="toOrderedNotPaidNum != null">
        to_ordered_not_paid_num = #{toOrderedNotPaidNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedNotPaidGoodsNum != null">
        to_ordered_not_paid_goods_num = #{toOrderedNotPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedNotPaidOrderNum != null">
        to_ordered_not_paid_order_num = #{toOrderedNotPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toOrderedNotPaidAmount != null">
        to_ordered_not_paid_amount = #{toOrderedNotPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="toPaidNum != null">
        to_paid_num = #{toPaidNum,jdbcType=INTEGER},
      </if>
      <if test="toPaidGoodsNum != null">
        to_paid_goods_num = #{toPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="toPaidOrderNum != null">
        to_paid_order_num = #{toPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="toPaidAmount != null">
        to_paid_amount = #{toPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="silentOrderToPaidNum != null">
        silent_order_to_paid_num = #{silentOrderToPaidNum,jdbcType=INTEGER},
      </if>
      <if test="silentOrderToPaidGoodsNum != null">
        silent_order_to_paid_goods_num = #{silentOrderToPaidGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="silentOrderToPaidOrderNum != null">
        silent_order_to_paid_order_num = #{silentOrderToPaidOrderNum,jdbcType=INTEGER},
      </if>
      <if test="silentOrderToPaidAmount != null">
        silent_order_to_paid_amount = #{silentOrderToPaidAmount,jdbcType=DOUBLE},
      </if>
      <if test="silentToFolowupNum != null">
        silent_to_folowup_num = #{silentToFolowupNum,jdbcType=INTEGER},
      </if>
      <if test="silentToFolowupGoodsNum != null">
        silent_to_folowup_goods_num = #{silentToFolowupGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="silentToFolowupOrderNum != null">
        silent_to_folowup_order_num = #{silentToFolowupOrderNum,jdbcType=INTEGER},
      </if>
      <if test="silentToFolowupAmount != null">
        silent_to_folowup_amount = #{silentToFolowupAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ShopTeamToOrderIndexDO">
    update pes_shop_team_to_order_index
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=TIMESTAMP},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      to_ordered_paid_num = #{toOrderedPaidNum,jdbcType=INTEGER},
      to_ordered_paid_goods_num = #{toOrderedPaidGoodsNum,jdbcType=INTEGER},
      to_ordered_paid_order_num = #{toOrderedPaidOrderNum,jdbcType=INTEGER},
      to_ordered_paid_amount = #{toOrderedPaidAmount,jdbcType=DOUBLE},
      to_ordered_not_paid_num = #{toOrderedNotPaidNum,jdbcType=INTEGER},
      to_ordered_not_paid_goods_num = #{toOrderedNotPaidGoodsNum,jdbcType=INTEGER},
      to_ordered_not_paid_order_num = #{toOrderedNotPaidOrderNum,jdbcType=INTEGER},
      to_ordered_not_paid_amount = #{toOrderedNotPaidAmount,jdbcType=DOUBLE},
      to_paid_num = #{toPaidNum,jdbcType=INTEGER},
      to_paid_goods_num = #{toPaidGoodsNum,jdbcType=INTEGER},
      to_paid_order_num = #{toPaidOrderNum,jdbcType=INTEGER},
      to_paid_amount = #{toPaidAmount,jdbcType=DOUBLE},
      silent_order_to_paid_num = #{silentOrderToPaidNum,jdbcType=INTEGER},
      silent_order_to_paid_goods_num = #{silentOrderToPaidGoodsNum,jdbcType=INTEGER},
      silent_order_to_paid_order_num = #{silentOrderToPaidOrderNum,jdbcType=INTEGER},
      silent_order_to_paid_amount = #{silentOrderToPaidAmount,jdbcType=DOUBLE},
      silent_to_folowup_num = #{silentToFolowupNum,jdbcType=INTEGER},
      silent_to_folowup_goods_num = #{silentToFolowupGoodsNum,jdbcType=INTEGER},
      silent_to_folowup_order_num = #{silentToFolowupOrderNum,jdbcType=INTEGER},
      silent_to_folowup_amount = #{silentToFolowupAmount,jdbcType=DOUBLE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>