<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.GoodsRecommendSummaryMapper" >
  <resultMap id="GoodsRecommendSummaryDO" type="com.pes.jd.model.DO.GoodsRecommendSummaryDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
     <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="recommend_num" property="recommendNum" jdbcType="INTEGER" />
    <result column="purchases_buyer_num" property="purchasesBuyerNum" jdbcType="INTEGER" />
    <result column="purchases_goods_num" property="purchasesGoodsNum" jdbcType="INTEGER" />
    <result column="purchases_amount" property="purchasesAmount" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, cs_nick,sku_id, recommend_num, purchases_buyer_num, purchases_goods_num, 
    purchases_amount
  </sql>
 
 <insert id="insertGoodsRecommendSummary" parameterType="com.pes.jd.model.DO.GoodsRecommendSummaryDO" >
    insert into pes_goods_recommend_summary (id, shop_id, date, 
      cs_nick,sku_id, recommend_num, purchases_buyer_num, 
      purchases_goods_num, purchases_amount)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR},#{skuId,jdbcType=BIGINT}, #{recommendNum,jdbcType=INTEGER}, #{purchasesBuyerNum,jdbcType=INTEGER}, 
      #{purchasesGoodsNum,jdbcType=INTEGER}, #{purchasesAmount,jdbcType=DOUBLE})
  </insert>
 
 <insert id="batchInsertGoodsRecommendSummary" parameterType="map" >
	INSERT INTO  ${tableName} 
    (`shop_id`, `date`, `cs_nick`, `sku_id`, `recommend_num`, `purchases_buyer_num`, `purchases_goods_num`,`purchases_amount`)
     VALUES
     <foreach collection="goodsRecommendSummaryLst" item="itm" separator=",">
	    (
		    #{itm.shopId,jdbcType=BIGINT}, 
		    #{itm.date,jdbcType=DATE}, 
		    #{itm.csNick,jdbcType=VARCHAR}, 
		    #{itm.skuId,jdbcType=BIGINT},
		    #{itm.recommendNum,jdbcType=INTEGER}, 
		    #{itm.purchasesBuyerNum,jdbcType=INTEGER}, 
		    #{itm.purchasesGoodsNum,jdbcType=INTEGER},
		    #{itm.purchasesAmount,jdbcType=DOUBLE}
	    )
      </foreach>
  </insert>
  
  <delete id="deleteGoodsRecommendSummaryByShopIdAndByDate" parameterType="map">
  	DELETE FROM ${tableName} 
  	WHERE shop_id=#{shopId}
  	AND date BETWEEN #{startDate} and #{endDate}
  </delete>
  
  <delete id="deleteGoodsRecommendSummaryById" parameterType="java.lang.Long" >
    delete from pes_goods_recommend_summary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateGoodsRecommendSummary" parameterType="com.pes.jd.model.DO.GoodsRecommendSummaryDO" >
    update pes_goods_recommend_summary
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        csNick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="recommendNum != null" >
        recommend_num = #{recommendNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesBuyerNum != null" >
        purchases_buyer_num = #{purchasesBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesGoodsNum != null" >
        purchases_goods_num = #{purchasesGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesAmount != null" >
        purchases_amount = #{purchasesAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="selectGoodsRecommendSummaryById" resultMap="GoodsRecommendSummaryDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_goods_recommend_summary
    where id = #{id,jdbcType=BIGINT}
  </select>
  
 
</mapper>