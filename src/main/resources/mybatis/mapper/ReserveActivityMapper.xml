<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ReserveActivityMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.ReserveActivityDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="plus_type" property="plusType" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="presel_num" property="preselNum" jdbcType="INTEGER"/>
        <result column="isJ" property="isj" jdbcType="BIGINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="panicbuying_stime" property="panicbuyingStime" jdbcType="TIMESTAMP"/>
        <result column="panicbuying_etime" property="panicbuyingEtime" jdbcType="TIMESTAMP"/>
        <result column="plus_stime" property="plusStime" jdbcType="TIMESTAMP"/>
        <result column="plus_etime" property="plusEtime" jdbcType="TIMESTAMP"/>
        <result column="condition_type" property="conditionType" jdbcType="INTEGER"/>
        <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP"/>

    </resultMap>
    <sql id="Base_Column_List">
    id, activity_id, sku_id, shop_id, type, plus_type, status, presel_num, isJ, start_time, 
    end_time, panicbuying_stime, panicbuying_etime, plus_stime,plus_etime,condition_type,cancel_time
  </sql>

    <delete id="deleteByActivityIdAndSkuId" parameterType="java.util.Map">
        delete from ${tableName} where
        <foreach collection="reserveActivityLst" separator=" or " item="item" index="index">
            ( sku_id =#{item.skuId}
            and
            activity_id =#{item.activityId}
            )
        </foreach>
    </delete>

    <delete id="deleteByShopId" parameterType="java.util.Map">
        delete from ${tableName} where
        shop_id = #{shopId,jdbcType=BIGINT}
    </delete>

    <update id="updateByActivityIdAndSkuId" parameterType="map">
        <foreach collection="reserveActivityLst" item="itm" open="" close="" separator=";">
            UPDATE ${tableName}
            set condition_type = 1
            WHERE
            sku_id =#{itm.skuId}
            and
            activity_id =#{itm.activityId}
        </foreach>
    </update>

    <update id="updateByActivityIdAndStatus" parameterType="map">
      UPDATE ${tableName}
      set condition_type = #{status}, status = 5
      WHERE
      shop_id =#{shopId}
  </update>

    <insert id="batchInsert" parameterType="java.util.Map">
        insert into ${tableName} (activity_id, sku_id,
        shop_id, type, plus_type,
        status, presel_num, isJ,
        start_time, end_time, panicbuying_stime,
        panicbuying_etime,
        plus_stime, plus_etime, condition_type, cancel_time)
        values
        <foreach collection="reserveActivityLst" item="itm" separator=","
                 index="index">
            (#{itm.activityId,jdbcType=VARCHAR}, #{itm.skuId,jdbcType=BIGINT},
            #{itm.shopId,jdbcType=BIGINT}, #{itm.type,jdbcType=TINYINT}, #{itm.plusType,jdbcType=TINYINT},
            #{itm.status,jdbcType=TINYINT}, #{itm.preselNum,jdbcType=INTEGER}, #{itm.isj,jdbcType=BIGINT},
            #{itm.startTime,jdbcType=TIMESTAMP}, #{itm.endTime,jdbcType=TIMESTAMP},
            #{itm.panicbuyingStime,jdbcType=TIMESTAMP},
            #{itm.panicbuyingEtime,jdbcType=TIMESTAMP},
            #{itm.plusStime,jdbcType=TIMESTAMP}, #{itm.plusEtime,jdbcType=TIMESTAMP},
            #{itm.conditionType,jdbcType=INTEGER}, #{itm.cancelTime})
        </foreach>
    </insert>

    <select id="selectByShopIdAndDateForReservePerformance" resultType="com.pes.jd.model.DTO.ReserveActivityDTO">
        SELECT id,activity_id,sku_id,shop_id,`type`,plus_type,status,presel_num,isJ,start_time,end_time,
            panicbuying_stime,panicbuying_etime,plus_stime,plus_etime,condition_type,cancel_time
        FROM ${tableName}
            WHERE shop_id = #{shopId}
            AND NOT ((panicbuying_etime &lt; #{dateStart}) OR (start_time &gt; #{dateEnd}))
    </select>

    <select id="selectCountByShopIdAndDate" resultType="java.lang.Integer">
       SELECT count(id)
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND NOT ((panicbuying_etime &lt; #{dateStart}) OR (start_time &gt; #{dateEnd}))
    </select>
</mapper>