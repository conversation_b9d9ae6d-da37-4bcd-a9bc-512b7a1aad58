<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsNoreplyMapper" >

<!--   <resultMap id="CsNoreplyDO" type="com.pes.jd.model.DO.CsNoreplyDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="no_reply_num" property="noReplyNum" jdbcType="INTEGER" />
    <result column="no_reply_cs_nick" property="noReplyCsNick" jdbcType="LONGVARCHAR" />
  </resultMap>
   -->
  <sql id="base_field" >
   	shop_id, date, cs_nick, no_reply_num, no_reply_cs_nick
  </sql>
  
  <insert id="insertCsNoreply" parameterType="com.pes.jd.model.DO.CsNoreplyDO" >
    INSERT INTO ${tableName} (shop_id, date, 
      cs_nick, no_reply_num, no_reply_cs_nick
      )
    VALUES (#{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{noReplyNum,jdbcType=INTEGER}, #{noReplyCsNick,jdbcType=LONGVARCHAR}
      )
  </insert>
  
   <insert id="batchInsertCsNoreply">
    INSERT INTO ${tableName} (shop_id, date, cs_nick, no_reply_num, no_reply_cs_nick)
    VALUES 
    <foreach collection="csNoreplyLst" item="itm" separator=",">
     (
    	#{itm.shopId,jdbcType=BIGINT}, 
    	#{itm.date,jdbcType=DATE}, 
      	#{itm.csNick,jdbcType=VARCHAR}, 
      	#{itm.noReplyNum,jdbcType=INTEGER}, 
      	#{itm.noReplyCsNick,jdbcType=LONGVARCHAR}
     )
    </foreach>
  </insert>
  
  
  <delete id="deleteCsNoreplyById" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteCsNoreplyByDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
  
  <update id="updateCsNoreplyById" parameterType="com.pes.jd.model.DO.CsNoreplyDO" >
    UPDATE ${tableName}
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="noReplyNum != null" >
        no_reply_num = #{noReplyNum,jdbcType=INTEGER},
      </if>
      <if test="noReplyCsNick != null" >
        no_reply_cs_nick = #{noReplyCsNick,jdbcType=LONGVARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>