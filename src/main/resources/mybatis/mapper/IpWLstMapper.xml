<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.IpWLstMapper">

  <resultMap id="IpWLstDTO" type="com.pes.jd.model.DTO.IpWLstDTO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="type" jdbcType="BIT" property="type" />
    <result column="resources" jdbcType="VARCHAR" property="resources" />
  </resultMap>
  <sql id="base_field">
    id, ip, type, resources
  </sql>
  
  <insert id="insertIpWLst" parameterType="com.pes.jd.model.DO.IpWLst">
    INSERT INTO pes_ip_w_lst (ip, type, resources )
    VALUES (#{ip,jdbcType=VARCHAR}, #{type,jdbcType=BIT}, #{resources,jdbcType=VARCHAR})
  </insert>
  
  <delete id="deleteIpWLstById" parameterType="java.lang.Integer">
    DELETE from pes_ip_w_lst
    WHERE 
    	id = #{id,jdbcType=INTEGER}
  </delete>
  
  
  <update id="updateIpWLstBySelective" parameterType="com.pes.jd.model.DO.IpWLst">
    UPDATE pes_ip_w_lst
    <set>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=BIT},
      </if>
      <if test="resources != null">
        resources = #{resources,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=INTEGER}
  </update>
  
   <select id="getIpWLstById" parameterType="java.lang.Integer" resultMap="IpWLstDTO">
    SELECT 
   		 <include refid="base_field" />
    FROM pes_ip_w_lst
    WHERE 
    	id = #{id,jdbcType=INTEGER}
  </select>
</mapper>