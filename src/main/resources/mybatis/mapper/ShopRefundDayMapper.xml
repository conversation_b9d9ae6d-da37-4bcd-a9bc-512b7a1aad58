<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopRefundDayMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopRefundDayDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="apply_refund_num" property="applyRefundNum" jdbcType="INTEGER" />
    <result column="apply_refund_product_num" property="applyRefundProductNum" jdbcType="INTEGER" />
    <result column="apply_refund_buyer_num" property="applyRefundBuyerNum" jdbcType="INTEGER" />
    <result column="apply_refund_amount" property="applyRefundAmount" jdbcType="DOUBLE" />
    <result column="completed_refund_num" property="completedRefundNum" jdbcType="INTEGER" />
    <result column="completed_refund_goods_num" property="completedRefundGoodsNum" jdbcType="INTEGER" />
    <result column="completed_refund_buyer_num" property="completedRefundBuyerNum" jdbcType="INTEGER" />
    <result column="completed_refund_amount" property="completedRefundAmount" jdbcType="DOUBLE" />
    <result column="total_refund_duration" property="totalRefundDuration" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, date, apply_refund_num, apply_refund_product_num, apply_refund_buyer_num, 
    apply_refund_amount, completed_refund_num, completed_refund_goods_num, completed_refund_buyer_num, 
    completed_refund_amount, total_refund_duration
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pes_shop_refund_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pes_shop_refund_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ShopRefundDayDO" >
    insert into pes_shop_refund_day (id, shop_id, date, 
      apply_refund_num, apply_refund_product_num, 
      apply_refund_buyer_num, apply_refund_amount, 
      completed_refund_num, completed_refund_goods_num, 
      completed_refund_buyer_num, completed_refund_amount, 
      total_refund_duration)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{applyRefundNum,jdbcType=INTEGER}, #{applyRefundProductNum,jdbcType=INTEGER}, 
      #{applyRefundBuyerNum,jdbcType=INTEGER}, #{applyRefundAmount,jdbcType=DOUBLE}, 
      #{completedRefundNum,jdbcType=INTEGER}, #{completedRefundGoodsNum,jdbcType=INTEGER}, 
      #{completedRefundBuyerNum,jdbcType=INTEGER}, #{completedRefundAmount,jdbcType=DOUBLE}, 
      #{totalRefundDuration,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ShopRefundDayDO" >
    insert into pes_shop_refund_day
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="date != null" >
        date,
      </if>
      <if test="applyRefundNum != null" >
        apply_refund_num,
      </if>
      <if test="applyRefundProductNum != null" >
        apply_refund_product_num,
      </if>
      <if test="applyRefundBuyerNum != null" >
        apply_refund_buyer_num,
      </if>
      <if test="applyRefundAmount != null" >
        apply_refund_amount,
      </if>
      <if test="completedRefundNum != null" >
        completed_refund_num,
      </if>
      <if test="completedRefundGoodsNum != null" >
        completed_refund_goods_num,
      </if>
      <if test="completedRefundBuyerNum != null" >
        completed_refund_buyer_num,
      </if>
      <if test="completedRefundAmount != null" >
        completed_refund_amount,
      </if>
      <if test="totalRefundDuration != null" >
        total_refund_duration,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        #{date,jdbcType=DATE},
      </if>
      <if test="applyRefundNum != null" >
        #{applyRefundNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundProductNum != null" >
        #{applyRefundProductNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundBuyerNum != null" >
        #{applyRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundAmount != null" >
        #{applyRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="completedRefundNum != null" >
        #{completedRefundNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundGoodsNum != null" >
        #{completedRefundGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundBuyerNum != null" >
        #{completedRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundAmount != null" >
        #{completedRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="totalRefundDuration != null" >
        #{totalRefundDuration,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopRefundDayDO" >
    update pes_shop_refund_day
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="applyRefundNum != null" >
        apply_refund_num = #{applyRefundNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundProductNum != null" >
        apply_refund_product_num = #{applyRefundProductNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundBuyerNum != null" >
        apply_refund_buyer_num = #{applyRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="applyRefundAmount != null" >
        apply_refund_amount = #{applyRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="completedRefundNum != null" >
        completed_refund_num = #{completedRefundNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundGoodsNum != null" >
        completed_refund_goods_num = #{completedRefundGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundBuyerNum != null" >
        completed_refund_buyer_num = #{completedRefundBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="completedRefundAmount != null" >
        completed_refund_amount = #{completedRefundAmount,jdbcType=DOUBLE},
      </if>
      <if test="totalRefundDuration != null" >
        total_refund_duration = #{totalRefundDuration,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ShopRefundDayDO" >
    update pes_shop_refund_day
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      apply_refund_num = #{applyRefundNum,jdbcType=INTEGER},
      apply_refund_product_num = #{applyRefundProductNum,jdbcType=INTEGER},
      apply_refund_buyer_num = #{applyRefundBuyerNum,jdbcType=INTEGER},
      apply_refund_amount = #{applyRefundAmount,jdbcType=DOUBLE},
      completed_refund_num = #{completedRefundNum,jdbcType=INTEGER},
      completed_refund_goods_num = #{completedRefundGoodsNum,jdbcType=INTEGER},
      completed_refund_buyer_num = #{completedRefundBuyerNum,jdbcType=INTEGER},
      completed_refund_amount = #{completedRefundAmount,jdbcType=DOUBLE},
      total_refund_duration = #{totalRefundDuration,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  	<insert id="batchInsertShopRefundDay" parameterType="map">
			INSERT INTO ${tableName}
			(
			`shop_id`,
			`date`,
			`apply_refund_num`,
			`apply_refund_product_num`,
			`apply_refund_buyer_num`,
			`apply_refund_amount`,
			`completed_refund_num`,
			`completed_refund_goods_num`,
			`completed_refund_buyer_num`,
			`completed_refund_amount`,
			`total_refund_duration`
			)
			VALUES
			<foreach collection="shopRefundDayLst" item="itm" separator="," index="index">
			(
			 #{itm.shopId},
			 #{itm.date},
			 #{itm.applyRefundNum},
			 #{itm.applyRefundProductNum},
			 #{itm.applyRefundBuyerNum},
			 #{itm.applyRefundAmount},
			 #{itm.completedRefundNum},
			 #{itm.completedRefundGoodsNum},
			 #{itm.completedRefundBuyerNum},
			 #{itm.completedRefundAmount},
			 #{itm.totalRefundDuration}
			)
			</foreach>
	</insert>
	
	
	<delete  id="deleteShopRefundDayByDateAndShopId" parameterType="map">
		DELETE FROM ${tableName} 
		WHERE 
			shop_id = #{shopId}
		AND date = #{date}
	</delete>
</mapper>