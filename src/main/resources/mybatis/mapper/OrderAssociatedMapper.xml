<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderAssociatedMapper" >


	<select id="selectCsTeamConfirmGoodsIndex"  resultType="com.pes.jd.model.DTO.CsSaleIndexPfDTO">
		SELECT 
			DATE_FORMAT(t2.end_time,'%Y-%m-%d') date, 
			count(t1.order_id) cfmGoodsONum, 
			sum(t2.payment) cfmGoodsAmount
		
		FROM ${orderTableName} t2
		ON t1.order_id = t2.order_id
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
		AND t1.cs_nick = #{csNick,jdbcType=VARCHAR}
		AND t1.date BETWEEN #{adjustDate} AND #{endDate}
		AND t2.end_time BETWEEN #{startDate} AND #{endDate}
		AND t1.is_pes_order = 1
	</select>
	
	<select id="selectShopCsOutStockOrderInfoLst"  resultType="com.pes.jd.model.DTO.OutStockOrderDTO">
		SELECT 
			t.order_id as orderId,
			t.payment as payment,
			t.buyer_nick as buyerNick,
			t.num as num
		FROM ${orderTableName} t
		WHERE 
			t.shop_id = #{shopId,jdbcType=BIGINT}
		AND t.out_stock_time BETWEEN #{startDate} AND #{endDate}
		AND EXISTS
		(
			SELECT 1 FROM ${csOrderBindTableName} cob 
			WHERE 
				cob.order_id = t.order_id
			AND cob.cs_nick = #{csNick,jdbcType=VARCHAR} 
			AND cob.is_pes_order = 1
		)
	</select>
	
	<select id="selectShopCsConfirmGoodsOrderInfoLst"  resultType="com.pes.jd.model.DTO.ConfirmGoodsOrderDTO">
		SELECT 
			t.order_id as orderId,
			t.payment as payment,
			t.buyer_nick as buyerNick,
			t.num as num
		FROM ${orderTableName} t
		WHERE 
			t.shop_id = #{shopId,jdbcType=BIGINT}
		AND t.end_time BETWEEN #{startDate} AND #{endDate}
		AND EXISTS
		(
			SELECT 1 FROM ${csOrderBindTableName} cob 
			WHERE 
				cob.order_id = t.order_id
			AND cob.cs_nick = #{csNick,jdbcType=VARCHAR} 
			AND cob.is_pes_order = 1
		)
	</select>


	<select id="selectShopCsPresaleSaleOrderLst" resultType="com.pes.jd.model.DTO.CsSaleOrderDTO">
		SELECT
			order_id as orderId,
			buyer_nick as buyerNick,
			goods_num as orderGoodsNum,
			pay_bargain_real as orderPayment,
			pay_balance_real as orderBalancePayment,
			order_post_fee as orderPostFee
		FROM ${presaleOrderTableName} t
		WHERE
			t.shop_id = #{shopId,jdbcType=BIGINT}
		AND t.balance_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND EXISTS
		(
			SELECT 1 FROM ${csOrderBindTableName} cob
			WHERE
				cob.order_id = t.order_id
			AND cob.cs_nick = #{csNick,jdbcType=VARCHAR}
			AND (cob.is_pes_order = 1 or cob.is_balance_pay = 1)
		)
	</select>


	<select id="selectShopCsPresaleBalanePayOrderLst" resultType="com.pes.jd.model.DTO.CsSaleOrderDTO">
		SELECT
			order_id as orderId,
			buyer_nick as buyerNick,
			goods_num as orderGoodsNum,
			pay_bargain_real as orderPayment,
			pay_balance_real as orderBalancePayment,
			freight as orderPostFee
		FROM ${presaleOrderTableName} t
		WHERE
			t.shop_id = #{shopId,jdbcType=BIGINT}
		AND t.balance_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND EXISTS
		(
			SELECT 1 FROM ${csOrderBindTableName} cob
			WHERE
				cob.order_id = t.order_id
			AND cob.cs_nick = #{csNick,jdbcType=VARCHAR}
			AND (cob.is_pes_order = 1 or cob.is_balance_pay = 1)
		)
	</select>


	<select id="selectShopCsPresaleOutStackOrderLst" resultType="com.pes.jd.model.DTO.OutStockOrderDTO">
		SELECT
			order_id as orderId,
			buyer_nick as buyerNick,
			goods_num as num,
			pay_bargain_real as orderBargainPayment,
			pay_balance_real as orderBalancePayment
		FROM ${presaleOrderTableName} t
		WHERE
			t.shop_id = #{shopId,jdbcType=BIGINT}
		AND t.out_stock_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND EXISTS
		(
			SELECT 1 FROM ${csOrderBindTableName} cob
			WHERE
				cob.order_id = t.order_id
			AND cob.cs_nick = #{csNick,jdbcType=VARCHAR}
			AND (cob.is_pes_order = 1 or cob.is_balance_pay = 1)
		)
	</select>

	<select id="selectShopPresaleBalancePayBindOrderLst" resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
		SELECT
			t.order_id as orderId,
			t.payment as payment,
			t.created as created,
			t1.balance_time as balancePayTime,
			t.buyer_nick as buyerNick,
			t.post_fee as post_fee
		FROM ${orderTableName} t LEFT JOIN ${orderPresaleTableName} t1 on t.order_id = t1.order_id
		WHERE
			t.shop_id = #{shopId,jdbcType=BIGINT}
	    AND t1.balance_time is not null
		AND t1.balance_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND t.order_type = 1
		AND EXISTS
		(
			SELECT 1 FROM ${csOrderBindTableName} cob
			WHERE
				cob.order_id = t.order_id
			  AND (cob.is_pes_order = 1 or cob.is_balance_pay = 1)
		)
	</select>

	<select id="selectShopPresaleBargainPayBindOrderLst" resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
		SELECT
			order_id as orderId,
			create_time as created,
			bargain_time as balancePayTime,
			pay_bargain_real as payment
		FROM ${orderTableName} t
		WHERE
			t.shop_id = #{shopId,jdbcType=BIGINT}
		  AND t.balance_start_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		  AND bargain_time is not null AND balance_time is null
		  AND EXISTS
			(
				SELECT 1 FROM ${csOrderBindTableName} cob
				WHERE
					cob.order_id = t.order_id
				  AND cob.is_balance_pay = 0 and cob.pay_type = 0
			)
	</select>

</mapper>