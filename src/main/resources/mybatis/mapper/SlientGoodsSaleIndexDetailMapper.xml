<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.SlientGoodsSaleIndexDetailMapper" >
  <resultMap id="SlientGoodsSaleIndexDetailDO" type="com.pes.jd.model.DO.SlientGoodsSaleIndexDetailDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="sale_goods_num" property="saleGoodsNum" jdbcType="INTEGER" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
     <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <resultMap id="SlientGoodsSaleIndexDetailDTO" type="com.pes.jd.model.DTO.SlientGoodsSaleIndexDetailDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="sale_goods_num" property="saleGoodsNum" jdbcType="INTEGER" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
     <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="base_flied" >
    id, shop_id, date, sku_id, sale_goods_num, sale_amount,order_id,out_stock_time
  </sql>
  <select id="selectSlientGoodsSaleIndexDetailById" resultMap="SlientGoodsSaleIndexDetailDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_flied" />
    from pes_slient_goods_sale_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteSlientGoodsSaleIndexDetail" parameterType="java.lang.Long" >
    delete from pes_slient_goods_sale_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSlientGoodsSaleIndexDetail" parameterType="com.pes.jd.model.DO.SlientGoodsSaleIndexDetailDO" >
    insert into pes_cs_goods_sale_index_detail (id, shop_id, date, 
      sku_id, customer, 
      sale_goods_num, sale_amount, order_id
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=BIGINT}, #{customer,jdbcType=VARCHAR}, 
      #{saleGoodsNum,jdbcType=INTEGER}, #{saleAmount,jdbcType=DOUBLE}, #{orderId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="batchInsertSlientGoodsSaleIndexDetail" parameterType="map" >
    INSERT INTO ${tableName} 
    (`shop_id`, `date`, `sku_id`, `customer`, `sale_goods_num`, `sale_amount`, `order_id`,out_stock_time) 
    VALUES 
    <foreach collection="slienGoodsDetailLst" item="itm" separator=",">
	(
		#{itm.shopId,jdbcType=BIGINT},
		#{itm.date,jdbcType=DATE},
		#{itm.skuId,jdbcType=BIGINT},
		#{itm.customer,jdbcType=INTEGER},
		#{itm.saleGoodsNum,jdbcType=INTEGER},
		#{itm.saleAmount,jdbcType=DOUBLE},
		#{itm.orderId,jdbcType=BIGINT},
		#{itm.outStockTime,jdbcType=TIMESTAMP}
	)
    </foreach>
  </insert>
 
 <delete id="deleteSlientGoodsSaleIndexDetailByShopIdByDate" parameterType="map">
 	DELETE FROM ${tableName}
 	WHERE shop_id=#{shopId}
 	AND date between #{startDate} and #{endDate}
 </delete>
  <update id="updateSlientGoodsSaleIndexDetail" parameterType="com.pes.jd.model.DO.SlientGoodsSaleIndexDetailDO" >
    update pes_slient_goods_sale_index
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="customer != null" >
        customer = #{customer,jdbcType=VARCHAR},
      </if>
      <if test="saleGoodsNum != null" >
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleAmount != null" >
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectSlientGoodsSaleIndexByShopIdByDate" resultMap="SlientGoodsSaleIndexDetailDTO">
  	SELECT 
	  	shop_id, 
	  	date, 
	  	sku_id, 
	  	sale_goods_num,
	  	customer, 
	  	sale_amount,
	  	order_id,
	  	out_stock_time
  	FROM ${tableName}
  	WHERE 
  		shop_id=#{shopId}
  	AND date between #{startDate} and #{endDate}
  </select>
</mapper>