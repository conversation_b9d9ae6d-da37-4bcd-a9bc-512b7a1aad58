<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.SchemaMapper">
  <resultMap id="SchemaDO" type="com.pes.jd.model.DO.Schema">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="schema_main" jdbcType="VARCHAR" property="schemaMain" />
    <result column="schema_sub" jdbcType="VARCHAR" property="schemaSub" />
    <result column="error_shop_flag" jdbcType="INTEGER" property="errorShopFlag" />
    <result column="shop_flag" jdbcType="INTEGER" property="shopFlag" />
    <result column="instance_url" jdbcType="VARCHAR" property="instanceUrl" />
  </resultMap>
  <sql id="base_field">
    id, schema_main, schema_sub, error_shop_flag, shop_flag, instance_url
  </sql>
 
   <insert id="insertSchema" parameterType="com.pes.jd.model.DO.Schema">
    INSERT INTO pes_schema 
	  (		schema_main, schema_sub, 
	      	error_shop_flag, shop_flag, instance_url )
    VALUES 
	    ( #{schemaMain,jdbcType=VARCHAR}, #{schemaSub,jdbcType=VARCHAR}, 
	      #{errorShopFlag,jdbcType=INTEGER}, #{shopFlag,jdbcType=INTEGER}, #{instanceUrl,jdbcType=VARCHAR}
	      )
  </insert>
  
  <delete id="deleteSchemaById" parameterType="java.lang.Integer">
    DELETE FROM pes_schema
    WHERE 
    	id = #{id,jdbcType=INTEGER}
  </delete>
 
  <update id="updateSchemaBySelective" parameterType="com.pes.jd.model.DO.Schema">
    UPDATE pes_schema
    <set>
      <if test="schemaMain != null">
        schema_main = #{schemaMain,jdbcType=VARCHAR},
      </if>
      <if test="schemaSub != null">
        schema_sub = #{schemaSub,jdbcType=VARCHAR},
      </if>
      <if test="errorShopFlag != null">
        error_shop_flag = #{errorShopFlag,jdbcType=INTEGER},
      </if>
      <if test="shopFlag != null">
        shop_flag = #{shopFlag,jdbcType=INTEGER},
      </if>
      <if test="instanceUrl != null">
        instance_url = #{instanceUrl,jdbcType=VARCHAR},
      </if>
    </set>
    WHERE id = #{id,jdbcType=INTEGER}
  </update>
  
<!--    <select id="getSchemaById" parameterType="java.lang.Integer" resultMap="SchemaDO">
    SELECT 
    	<include refid="base_field" />
    FROM pes_schema
    WHERE 
    	id = #{id,jdbcType=INTEGER}
  </select> -->
</mapper>