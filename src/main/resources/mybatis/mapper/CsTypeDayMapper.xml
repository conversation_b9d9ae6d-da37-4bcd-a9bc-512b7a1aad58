<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsTypeDayMapper">

    <sql id="base_column">
    id, shop_id, date, cs_nick, cs_type, is_lock
  </sql>

    <resultMap id="CsTypeDayDTO" type="com.pes.jd.model.DTO.CsTypeDayDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="cs_type" property="csType" jdbcType="TINYINT"/>
        <result column="is_lock" property="isLock" jdbcType="BIT"/>
    </resultMap>

    <delete id="deleteByShopIdAndDate">
		DELETE FROM ${tableName}
        WHERE shop_id = #{shopId}
		AND `date` BETWEEN #{startDate} AND #{endDate}
	</delete>

    <insert id="batchInsertCsTypeDay">
        INSERT INTO ${tableName} (shop_id, `date`, cs_nick, cs_type, is_lock) VALUES
        <foreach collection="csTypeDayLst" item="csTypeDay" separator=",">
            (#{csTypeDay.shopId,jdbcType=BIGINT}, #{csTypeDay.date,jdbcType=DATE},
            #{csTypeDay.csNick,jdbcType=VARCHAR}, #{csTypeDay.csType,jdbcType=TINYINT},
            #{csTypeDay.isLock,jdbcType=BIT})
        </foreach>
    </insert>

    <select id="selectCsLockStatusByShopByDate" parameterType="map" resultMap="CsTypeDayDTO">
        SELECT * 
        FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date = #{date}
    </select>

</mapper>