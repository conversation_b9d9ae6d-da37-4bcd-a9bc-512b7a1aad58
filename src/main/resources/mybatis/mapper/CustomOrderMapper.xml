<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CustomerOrderMapper">

	<sql id="Base_Column_List">
		id, order_id, seller_nick, date, shop_id, payment, post_fee, consign_time,
		num, status,
		total_fee, created, pay_time, modified, end_time, buyer_nick, seller_flag, type,
		pay_type, seller_discount, order_type, address_flag
	</sql>
	
    
    
    <insert id="insertCustomerOrder" parameterType="map">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="order.orderId != null">
                order_id,
            </if>
            <if test="order.tradeId != null">
                trade_id,
            </if>
            <if test="order.sellerNick != null">
                seller_nick,
            </if>
            <if test="order.date != null">
                date,
            </if>
            <if test="order.shopId != null">
                shop_id,
            </if>
            <if test="order.payment != null">
                payment,
            </if>
            <if test="order.postFee != null">
                post_fee,
            </if>
            <if test="order.consignTime != null">
                consign_time,
            </if>
            <if test="order.num != null">
                num,
            </if>
            <if test="order.status != null">
                status,
            </if>
            <if test="order.totalFee != null">
                total_fee,
            </if>
            <if test="order.created != null">
                created,
            </if>
            <if test="order.payTime != null">
                pay_time,
            </if>
            <if test="order.modified != null">
                modified,
            </if>
            <if test="order.endTime != null">
                end_time,
            </if>
            <if test="order.buyerNick != null">
                buyer_nick,
            </if>
            <if test="order.sellerFlag != null">
                seller_flag,
            </if>
            <if test="order.type != null">
                type,
            </if>
            <if test="order.stepTradeStatus != null">
                step_trade_status,
            </if>
            <if test="order.stepPaidFee != null">
                step_paid_fee,
            </if>
            <if test="order.outStockTime != null">
                out_stock_time,
            </if>
            <if test="order.payType != null">
                pay_type,
            </if>
            <if test="order.sellerDiscount != null">
                seller_discount,
            </if>
            <if test="order.orderType != null">
                order_type,
            </if>
            <if test="order.flag!=null">
                falg,
            </if>
            <if test="order.memo!=null">
                memo,
            </if>
            
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="order.orderId != null">
                #{order.orderId,jdbcType=BIGINT},
            </if>
            <if test="order.tradeId != null">
                #{order.tradeId,jdbcType=BIGINT},
            </if>
            <if test="order.sellerNick != null">
                #{order.sellerNick,jdbcType=VARCHAR},
            </if>
            <if test="order.date != null">
                #{order.date,jdbcType=DATE},
            </if>
            <if test="order.shopId != null">
                #{order.shopId,jdbcType=BIGINT},
            </if>
            <if test="order.payment != null">
                #{order.payment,jdbcType=DOUBLE},
            </if>
            <if test="order.postFee != null">
                #{order.postFee,jdbcType=DOUBLE},
            </if>
            <if test="order.consignTime != null">
                #{order.consignTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.num != null">
                #{order.num,jdbcType=INTEGER},
            </if>
            <if test="order.status != null">
                #{order.status,jdbcType=VARCHAR},
            </if>
            <if test="order.totalFee != null">
                #{order.totalFee,jdbcType=DOUBLE},
            </if>
            <if test="order.created != null">
                #{order.created,jdbcType=TIMESTAMP},
            </if>
            <if test="order.payTime != null">
                #{order.payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.modified != null">
                #{order.modified,jdbcType=TIMESTAMP},
            </if>
            <if test="order.endTime != null">
                #{order.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.buyerNick != null">
                #{order.buyerNick,jdbcType=VARCHAR},
            </if>
            <if test="order.sellerFlag != null">
                #{order.sellerFlag,jdbcType=BIGINT},
            </if>
            <if test="order.type != null">
                #{order.type,jdbcType=VARCHAR},
            </if>
            <if test="order.stepTradeStatus != null">
                #{order.stepTradeStatus,jdbcType=VARCHAR},
            </if>
            <if test="order.stepPaidFee != null">
                #{order.stepPaidFee,jdbcType=DOUBLE},
            </if>
            <if test="order.outStockTime != null">
                #{order.outStockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.payType != null">
                #{order.payType,jdbcType=TINYINT},
            </if>
            <if test="order.sellerDiscount != null">
                #{order.sellerDiscount,jdbcType=DOUBLE},
            </if>
            <if test="order.orderType != null">
                #{order.orderType,jdbcType=TINYINT},
            </if>
            <if test="order.flag!=null">
                #{order.flag,jdbcType=BIGINT},
            </if>
            <if test="order.memo!=null">
                #{order.memo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    
	
	 <select id="selectOrderById" parameterType="map" resultType="int">
        select count(1) FROM ${tableName} 
        where 
		order_id = #{oid,jdbcType=BIGINT}
    </select>
	
    <delete id="deleteOrderByOrderId" parameterType="map">
        DELETE FROM ${tableName}
        where
		order_id = #{oid,jdbcType=BIGINT}
    </delete>

	  <insert id="insertOrderDetail"  parameterType="map">
    insert into ${tableName}(order_id, shop_id, cust_remark, 
      order_created)
    values (#{orderDetail.orderId,jdbcType=BIGINT}, #{orderDetail.shopId,jdbcType=BIGINT}, #{orderDetail.custRemark,jdbcType=VARCHAR}, 
      #{orderDetail.orderCreated,jdbcType=TIMESTAMP})
  	</insert>	
		
		
	  <insert id="delOrderDetail"  parameterType="map">
    DELETE from ${tableName}
    where
	order_id =#{orderDetail.orderId,} 
  	</insert>	
		
   
</mapper>