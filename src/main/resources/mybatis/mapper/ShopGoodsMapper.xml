<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopGoodMapper">
	<resultMap id="BaseResultMap"
		type="com.pes.jd.model.DO.ShopGoodsDO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="name" property="name" jdbcType="VARCHAR" />
		<result column="price" property="price" jdbcType="DOUBLE" />
		<result column="ware_id" property="wareId" jdbcType="BIGINT" />
		<result column="image_url" property="imageUrl" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="TINYINT" />
		<result column="stock_num" property="stockNum" jdbcType="INTEGER" />
		<result column="category_id" property="categoryId" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
	</resultMap>
	<sql id="Base_Column_List">
		id, name, price, ware_id, image_url, status, stock_num, category_id,
		shop_id, created, modified, online_time, offline_time
	</sql>
	<delete id="deleteByShopGoodsName" parameterType="map">
		delete from ${tableName}
		WHERE shop_id = #{shopId}
	</delete>

	<delete id="deleteByShopGoodsNameAndWareId" parameterType="map">
		delete from ${tableName}
		WHERE shop_id = #{shopId} AND ware_id=#{wareId}
	</delete>

	<delete id="deleteByShopIdAndShopGood" parameterType="map">
		delete from ${tableName}
		WHERE shop_id = #{shopId}
		AND category_id = #{shopGood.categoryId} And
			ware_id=#{shopGood.wareId} And
			name=#{shopGood.name} And
			image_url=#{shopGood.imageUrl} And
			status=#{shopGood.status} And
			category_id=#{shopGood.categoryId} And
			stock_num=#{shopGood.stockNum}
	</delete>

	<insert id="batchShopGood" parameterType="map">
		INSERT INTO ${tableName}(name, price, ware_id, image_url, shop_id, status, category_id, stock_num,article_number)
		VALUES
		<foreach collection="shopGoodLst" item="itm" separator=",">
			(#{itm.name}, #{itm.price}, #{itm.wareId}, #{itm.imageUrl},
			#{itm.shopId}, #{itm.status}, #{itm.categoryId}, #{itm.stockNum}, #{itm.articleNumber})
		</foreach>
	</insert>

	<update id="batchUpdateShopGood" parameterType="map">
		<foreach collection="shopGoodLst" item="item" open="" close=""
			separator=";">
			UPDATE ${tableName}
			SET category_id = #{item.categoryId},
			stock_num = #{item.stockNum}, status = #{item.status}
			WHERE
			ware_id = #{item.wareId}
		</foreach>
	</update>
    <select id="selectShopGoodNumByShopId" resultType="int">
        select count(1)
        from ${tableName}
        where shop_id=#{shopId}
    </select>
    <delete id="deleteByShopGoodsNameAndWareIds">
        delete from ${tableName}
        WHERE shop_id = #{shopId} AND
        ware_id in
        <foreach collection="wareIds" item="wareId" open="(" close=")" separator=",">
            #{wareId}
        </foreach>
    </delete>

</mapper>