<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopErrorUserAnalysisMapper">

  <insert id="insertErrorShopInfo">
    insert into ${tableName}
    (shop_id, start_date, end_date, created)
    values
    (
     #{shopUser.shopId, jdbcType=BIGINT},
     #{shopUser.startDate, jdbcType=DATE},
     #{shopUser.endDate, jdbcType=DATE},
     #{shopUser.created, jdbcType=TIMESTAMP}
     )
  </insert>
</mapper>