<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.UserMapper">

	<resultMap id="UserDO" type="com.pes.jd.model.DO.User">
		<id column="user_id" jdbcType="BIGINT" property="userId" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="nick" jdbcType="VARCHAR" property="nick" />
		<result column="session_key" jdbcType="VARCHAR" property="sessionKey" />
		<result column="type" jdbcType="VARCHAR" property="type" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="subscribe_dead_line" jdbcType="TIMESTAMP" property="subscribeDeadLine" />
		<result column="item_code" jdbcType="VARCHAR" property="itemCode" />
		<result column="status" jdbcType="VARCHAR" property="status" />
		<result column="lock_flag" jdbcType="INTEGER" property="lockFlag" />
		<result column="level" jdbcType="BIGINT" property="level" />
		<result column="read_maessage_time" jdbcType="TIMESTAMP" property="readMaessageTime" />
		<result column="multi_shop_switch" jdbcType="BIT" property="multiShopSwitch" />
	</resultMap>

	<sql id="base_field">
		user_id, shop_id, nick, type, created, subscribe_dead_line, item_code,
		status, lock_flag, level
	</sql>

	<insert id="insertUser" parameterType="com.pes.jd.model.DO.User">
		INSERT INTO pes_user (user_id, shop_id, nick, session_key, type, created,
		subscribe_dead_line, item_code, status,lock_flag, level, read_maessage_time,multi_shop_switch)
		VALUES
		(
			#{userId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR},
			#{sessionKey,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
			#{created,jdbcType=TIMESTAMP},
			#{subscribeDeadLine,jdbcType=TIMESTAMP}, #{itemCode,jdbcType=VARCHAR},
			#{status,jdbcType=VARCHAR},
			#{lockFlag,jdbcType=INTEGER}, #{level,jdbcType=BIGINT}, #{readMaessageTime,jdbcType=TIMESTAMP},
			#{multiShopSwitch,jdbcType=BIT}
		)
	</insert>
	
	<delete id="deleteUserById" parameterType="java.lang.Long">
		DELETE FROM pes_user
		WHERE 
			user_id = #{userId,jdbcType=BIGINT}
	</delete>

	<update id="updateUserById" parameterType="com.pes.jd.model.DO.User">
		UPDATE pes_user
		<set>
			<if test="nick != null">
				nick = #{nick,jdbcType=VARCHAR},
			</if>
			<if test="sessionKey != null">
				session_key = #{sessionKey,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=VARCHAR},
			</if>
			<if test="subscribeDeadLine != null">
				subscribe_dead_line = #{subscribeDeadLine,jdbcType=TIMESTAMP},
			</if>
			<if test="itemCode != null">
				item_code = #{itemCode,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				status = #{status,jdbcType=VARCHAR},
			</if>
			<if test="lockFlag != null">
				lock_flag = #{lockFlag,jdbcType=INTEGER},
			</if>
			<if test="level != null">
				level = #{level,jdbcType=BIGINT},
			</if>
			<if test="readMaessageTime != null">
				read_maessage_time = #{readMaessageTime,jdbcType=TIMESTAMP},
			</if>
			<if test="multiShopSwitch != null">
				multi_shop_switch = #{multiShopSwitch,jdbcType=BIT},
			</if>
		</set>
		WHERE
		user_id = #{userId,jdbcType=BIGINT}
	</update>
	
<!-- 	<select id="getUserById" parameterType="java.lang.Long" resultMap="UserDO">
		SELECT
		<include refid="base_field" />
		FROM pes_user
		WHERE user_id = #{userId,jdbcType=BIGINT}
	</select> -->
</mapper>