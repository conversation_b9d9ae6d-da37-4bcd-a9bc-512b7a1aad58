<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.WsUserMapper">

	<resultMap id="WsUserDO" type="com.pes.jd.model.DO.WsUser">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="username" jdbcType="VARCHAR" property="username" />
		<result column="password" jdbcType="VARCHAR" property="password" />
	</resultMap>

	<sql id="base_field">
		id, username, password
	</sql>

	<insert id="insertWsUser" parameterType="com.pes.jd.model.DO.WsUser">
		INSERT INTO pes_ws_user (id, username, password)
		VALUES
		(
		#{id,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}
		)
	</insert>
	
	<delete id="deleteWsUserById" parameterType="java.lang.Long">
		DELETE FROM pes_ws_user
		WHERE
		id = #{id,jdbcType=BIGINT}
	</delete>

	<update id="updateWsUserPasswordById" parameterType="com.pes.jd.model.DO.WsUser">
		UPDATE pes_ws_user
		SET password = #{password,jdbcType=VARCHAR}
		WHERE
		id = #{id,jdbcType=BIGINT}
	</update>

<!-- 	<select id="selectWsUserById" parameterType="java.lang.Long"
		resultMap="WsUserDO">
		SELECT
		<include refid="base_field" />
		FROM pes_ws_user
		WHERE
		id = #{id,jdbcType=BIGINT}
	</select> -->

</mapper>