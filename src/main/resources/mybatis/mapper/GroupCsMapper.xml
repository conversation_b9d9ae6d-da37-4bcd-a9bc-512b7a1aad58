<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.GroupCsMapper" >

  <resultMap id="GroupCsDTO" type="com.pes.jd.model.DTO.GroupCsDTO" >
    <id column="group_id" property="groupId" jdbcType="BIGINT" />
    <id column="nick" property="nick" jdbcType="VARCHAR" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="BIT" />
  </resultMap>
  
  <sql id="base_field" >
    group_id, nick, shop_id, status
  </sql>
  
  <insert id="insertGroupCs" parameterType="com.pes.jd.model.DO.GroupCs" >
    INSERT INTO pes_group_cs (group_id, nick, shop_id, status)
    VALUES 
    (
   	 	 #{groupId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR},
   	 	 #{shopId,jdbcType=BIGINT},  #{status,jdbcType=BIT}
    )
  </insert>
  
  <delete id="deleteGroupCsByGroupId" parameterType="long" >
    DELETE from pes_group_cs
    WHERE group_id = #{groupId,jdbcType=BIGINT}
  </delete>
  
  <update id="updateGroupCsBySelective" parameterType="com.pes.jd.model.DO.GroupCs" >
    UPDATE pes_group_cs
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=BIT},
      </if>
    </set>
    WHERE group_id = #{groupId,jdbcType=BIGINT}
    AND nick = #{nick,jdbcType=VARCHAR}
  </update>
  
  <select id="selectGroupCsByGroupId"  parameterType="com.pes.jd.model.DO.GroupCs" resultMap="GroupCsDTO">
    SELECT 
    <include refid="base_field" />
    FROM pes_group_cs
    WHERE group_id = #{groupId,jdbcType=BIGINT}
  </select>
</mapper>