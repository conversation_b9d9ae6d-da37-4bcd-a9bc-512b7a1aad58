<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.GoodsConsultSummaryMapper" >
  <resultMap id="GoodsConsultSummaryDO" type="com.pes.jd.model.DO.GoodsConsultSummaryDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
     <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="consult_num" property="consultNum" jdbcType="INTEGER" />
    <result column="purchases_buyer_num" property="purchasesBuyerNum" jdbcType="INTEGER" />
    <result column="purchases_goods_num" property="purchasesGoodsNum" jdbcType="INTEGER" />
    <result column="purchases_amount" property="purchasesAmount" jdbcType="DOUBLE" />
    
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, sku_id, cs_nick,consult_num, purchases_buyer_num, purchases_goods_num,purchases_amount
  </sql>
  
  <insert id="insertGoodsConsultSummary" parameterType="com.pes.jd.model.DO.GoodsConsultSummaryDO" >
    insert into pes_goods_consult_summary (id, shop_id,cs_nick, date, 
      sku_id, consult_num, purchases_buyer_num, purchases_amount
      purchases_goods_num)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT},#{csNick,jdbcType=VARCHAR}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=BIGINT}, #{consultNum,jdbcType=INTEGER}, #{purchasesBuyerNum,jdbcType=INTEGER}, #{purchasesAmount,jdbcType=DOUBLE}
      #{purchasesGoodsNum,jdbcType=INTEGER})
  </insert>
 <insert id="batchInsertGoodsConsultSummary" parameterType="map" >
    INSERT INTO ${tableName} (
    shop_id, 
    date,
    cs_nick, 
    sku_id,
    consult_num, 
    purchases_buyer_num,
    purchases_amount,
    purchases_goods_num)
    VALUES 
     <foreach collection="goodsConsultSummaryLst" item="itm" separator=",">
	    (
		    #{itm.shopId,jdbcType=BIGINT}, 
		    #{itm.date,jdbcType=DATE}, 
		    #{itm.csNick,jdbcType=VARCHAR},
		    #{itm.skuId,jdbcType=BIGINT}, 
		    #{itm.consultNum,jdbcType=INTEGER}, 
		    #{itm.purchasesBuyerNum,jdbcType=INTEGER}, 
		    #{itm.purchasesAmount,jdbcType=DOUBLE},
		    #{itm.purchasesGoodsNum,jdbcType=INTEGER}
	    )
      </foreach>
  </insert>
  
  <delete id="deleteGoodsConsultSummaryByShopIdAndByDate" parameterType="map">
  	DELETE FROM ${tableName} 
  	WHERE shop_id=#{shopId}
  	AND date BETWEEN #{startDate} and #{endDate}
  </delete>
  
  <delete id="deleteGoodsConsultSummaryById" parameterType="java.lang.Long" >
    delete from pes_goods_consult_summary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateGoodsConsultSummaryById" parameterType="com.pes.jd.model.DO.GoodsConsultSummaryDO" >
    update pes_goods_consult_summary
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="consultNum != null" >
        consult_num = #{consultNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesBuyerNum != null" >
        purchases_buyer_num = #{purchasesBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="purchasesGoodsNum != null" >
        purchases_goods_num = #{purchasesGoodsNum,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="selectGoodsConsultSummaryById" resultMap="GoodsConsultSummaryDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_goods_consult_summary
    where id = #{id,jdbcType=BIGINT}
   </select>

    <insert id="batchInsertGoodsConsultSummaryV2" parameterType="map" >
        INSERT INTO ${tableName} (
        shop_id,
        date,
        sku_id,
        cs_nick,
        receive_num,
        enquiry_num,
        pay_num,
        pay_goods_num,
        pay_amount)
        VALUES
        <foreach collection="goodsConsultSummaryLst" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.skuId,jdbcType=BIGINT},
            #{itm.csNick,jdbcType=VARCHAR},
            #{itm.receiveNum,jdbcType=INTEGER},
            #{itm.enquiryNum,jdbcType=INTEGER},
            #{itm.payNum,jdbcType=INTEGER},
            #{itm.payGoodsNum,jdbcType=INTEGER},
            #{itm.payAmount,jdbcType=DOUBLE}
            )
        </foreach>
    </insert>
    <delete id="deleteGoodsConsultSummaryByShopIdAndByDateV2" parameterType="map">
        DELETE FROM ${tableName}
        WHERE shop_id=#{shopId}
          AND date BETWEEN #{startDate} and #{endDate}
    </delete>
</mapper>