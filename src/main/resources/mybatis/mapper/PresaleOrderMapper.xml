<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.PresaleOrderMapper">

    <resultMap id="PresaleOrderDTO" type="com.pes.jd.model.DTO.PresaleOrderDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
        <result column="presale_id" jdbcType="BIGINT" property="presaleId"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="goods_num" jdbcType="INTEGER" property="goodsNum"/>
        <result column="yushou_price" jdbcType="DOUBLE" property="yushouPrice"/>
        <result column="freight" jdbcType="DOUBLE" property="freight"/>
        <result column="order_pay_type" jdbcType="INTEGER" property="orderPayType"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="pay_bargain_real" jdbcType="DOUBLE" property="payBargainReal"/>
        <result column="pay_bargain_plan" jdbcType="DOUBLE" property="payBargainPlan"/>
        <result column="bargain_time" jdbcType="TIMESTAMP" property="bargainTime"/>
        <result column="pay_balance_real" jdbcType="DOUBLE" property="payBalanceReal"/>
        <result column="pay_balance_plan" jdbcType="DOUBLE" property="payBalancePlan"/>
        <result column="balance_time" jdbcType="TIMESTAMP" property="balanceTime"/>
        <result column="balance_start_time" jdbcType="TIMESTAMP" property="balanceStartTime"/>
        <result column="balance_end_time" jdbcType="TIMESTAMP" property="balanceEndTime"/>
        <result column="yn" jdbcType="INTEGER" property="yn"/>
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="balance_end_time_plan" jdbcType="TIMESTAMP" property="balanceEndTimePlan"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
    </resultMap>

    <resultMap id="BuyerOrderDTO" type="com.pes.jd.model.DTO.BuyerOrderDTO">
        <id column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="seller_nick" property="sellerNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="post_fee" property="postFee" jdbcType="DOUBLE"/>
        <result column="consign_time" property="consignTime" jdbcType="TIMESTAMP"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="total_fee" property="totalFee" jdbcType="DOUBLE"/>
        <result column="create_time" property="created" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="seller_flag" property="sellerFlag" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="step_trade_status" property="stepTradeStatus" jdbcType="VARCHAR"/>
        <result column="step_paid_fee" property="stepPaidFee" jdbcType="DOUBLE"/>
        <result column="orders_num" property="ordersNum" jdbcType="INTEGER"/>
        <result column="balance_time" jdbcType="TIMESTAMP" property="balanceTime"/>
        <result column="pay_bargain_real" jdbcType="DOUBLE" property="payBargainReal"/>
        <result column="pay_balance_real" jdbcType="DOUBLE" property="payBalanceReal"/>
    </resultMap>

    <insert id="batchInsertPresaleOrderByPresaleOrderList" parameterType="map">
        INSERT INTO ${tableName} (`shop_id`,`buyer_nick`,`presale_id`,`sku_id`,`sku_name`,
        `goods_num`,`order_id`,`yushou_price`,`freight`,`order_pay_type`,
        `order_type`,`order_status`,`create_time`,`update_time`,`pay_bargain_real`,
        `pay_bargain_plan`,`bargain_time`,`pay_balance_real`,`pay_balance_plan`,`balance_time`,
        `balance_start_time`,`balance_end_time`,`yn`,`order_time`,`balance_end_time_plan`,
        `company_id`)
        VALUES
        <foreach collection="presaleList" item="presale" separator="," index="index">
            (#{presale.shopId},#{presale.buyerNick},#{presale.presaleId},#{presale.skuId},#{presale.skuName},
            #{presale.goodsNum},#{presale.orderId},#{presale.yushouPrice},#{presale.freight},#{presale.orderPayType},
            #{presale.orderType},#{presale.orderStatus},#{presale.createTime},#{presale.updateTime},#{presale.payBargainReal},
            #{presale.payBargainPlan},#{presale.bargainTime},#{presale.payBalanceReal},#{presale.payBalancePlan},#{presale.balanceTime},
            #{presale.balanceStartTime},#{presale.balanceEndTime},#{presale.yn},#{presale.orderTime},#{presale.balanceEndTimePlan},
            #{presale.companyId})
        </foreach>
    </insert>

    <delete id="batchDeleteShopPresaleOrderByDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND update_time BETWEEN #{startDate} AND #{endDate}

	</delete>

    <delete id="deleteShopPresaleOrderByOrderIdLst" parameterType="java.util.Map">
        DELETE FROM ${tableName}
        WHERE order_id IN
        <foreach collection="tids" index="index" item="tid" open="("
                 separator="," close=")">
            #{tid}
        </foreach>
    </delete>

    <update id="batchUpdateShopPresaleOrderByPresaleOrderList" parameterType="map">
        <foreach collection="presaleOrderLst" item="order" open=""
                 close="" separator=";">
            UPDATE ${tableName}
            <set>
                <if test="order.freight != null">
                    freight = #{order.freight},
                </if>
                <if test="order.orderPayType != null">
                    order_pay_type = #{order.orderPayType},
                </if>
                <if test="order.orderStatus != null">
                    order_status = #{order.orderStatus},
                </if>
                <if test="order.updateTime != null">
                    update_time = #{order.updateTime},
                </if>
                <if test="order.payBargainReal != null">
                    pay_bargain_real = #{order.payBargainReal},
                </if>
                <if test="order.payBargainPlan != null">
                    pay_bargain_plan = #{order.payBargainPlan},
                </if>
                <if test="order.bargainTime != null">
                    bargain_time = #{order.bargainTime},
                </if>
                <if test="order.payBalanceReal != null">
                    pay_balance_real = #{order.payBalanceReal},
                </if>
                <if test="order.payBalancePlan != null">
                    pay_balance_plan = #{order.payBalancePlan},
                </if>
                <if test="order.balanceTime != null">
                    balance_time = #{order.balanceTime},
                </if>
                <if test="order.balanceStartTime != null">
                    balance_start_time = #{order.balanceStartTime},
                </if>
                <if test="order.balanceEndTime != null">
                    balance_end_time = #{order.balanceEndTime},
                </if>
                <if test="order.yn != null">
                    yn = #{order.yn},
                </if>
            </set>
            WHERE order_id = #{order.orderId}
            AND shop_id = #{shopId}
        </foreach>
    </update>

    <select id="selectPresaleOrderByOrderLst" parameterType="map" resultType="java.lang.Long">
        SELECT DISTINCT order_id
        FROM ${tableName}
        WHERE create_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND shop_id = #{shopId,jdbcType=BIGINT}
    </select>

    <select id="selectPresaleOrderByOrderIdAndOrderPayType" parameterType="map" resultMap="PresaleOrderDTO">
        SELECT buyer_nick,sku_id,sku_name,goods_num,order_id,create_time,pay_bargain_real,bargain_time,pay_balance_real,balance_time
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND order_pay_type=#{orderPayType}
    </select>
    <select id="selectPresaleOrderInfoByOrderLst" parameterType="map"
            resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
        SELECT
        order_id as orderId,
        pay_bargain_real as bargainPayment,
        pay_balance_real as balancePayment,
        create_time as created,
        balance_time as balancePayTime,
        bargain_time as bargainTime
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND order_id IN
        <foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="selectPresaleOrderInfoByOrderLstAll" parameterType="map"
            resultMap="PresaleOrderDTO">
        SELECT
        *
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND order_id IN
        <foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <!--预售下单-->
    <select id="selectShopCreatedPresaleOrderLstByBuyersAndDate" resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
        SELECT
        order_id as orderId,
        pay_bargain_real as bargainPayment,
        pay_balance_real as balancePayment,
        goods_num as num,
        create_time as created,
        bargain_time as bargainTime,
        balance_time as balancePayTime,
        buyer_nick as buyerNick,
        order_type as type,
        freight as postFee,
        order_pay_type as orderPayType
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND create_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
        <if test="filterOrderIds!=null and filterOrderIds.size()>0">
            AND order_id NOT IN
            <foreach collection="filterOrderIds" item="filterOrderId" open="(" close=")" separator=",">
                #{filterOrderId,jdbcType=BIGINT}
            </foreach>
        </if>
        <!--		AND balance_time IS NULL-->
    </select>

    <!--付定金-->
    <select id="selectShopBargainPaidOrderLstByBuyersAndDate" resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
        SELECT
        order_id as orderId,
        pay_bargain_real as bargainPayment,
        pay_balance_real as balancePayment,
        goods_num as num,
        create_time as created,
        bargain_time as bargainTime,
        balance_time as balancePayTime,
        buyer_nick as buyerNick,
        order_type as type,
        freight as postFee,
        order_pay_type as orderPayType
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND bargain_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
        <if test="filterOrderIds!=null and filterOrderIds.size()>0">
            AND order_id NOT IN
            <foreach collection="filterOrderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>

        <!--		AND balance_time IS NULL-->
    </select>


    <!--付定金-->
    <select id="selectShopBargainPaidOrderLstByBuyersAndDateForCalEnquiry"
            resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
        SELECT
        order_id as orderId,
        pay_bargain_real as bargainPayment,
        pay_balance_real as balancePayment,
        goods_num as num,
        create_time as created,
        bargain_time as bargainTime,
        balance_time as balancePayTime,
        buyer_nick as buyerNick,
        order_type as type,
        freight as postFee
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND bargain_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--付尾款-->
    <select id="selectShopBalancePaidOrderLstByBuyersAndDate" resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
        SELECT
        order_id as orderId,
        pay_bargain_real as bargainPayment,
        pay_balance_real as balancePayment,
        goods_num as num,
        create_time as created,
        bargain_time as bargainTime,
        balance_time as balancePayTime,
        buyer_nick as buyerNick,
        order_type as type,
        freight as postFee
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND balance_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
    </select>


    <update id="updateBatchPresaleOrderByOrderId"
            parameterType="map">
        <foreach collection="orderList" item="item" index="index" open="" close="" separator=";">
            UPDATE ${tableName}
            <set>
                <if test="item.outStockTime != null">
                    out_stock_time = #{item.outStockTime,jdbcType=TIMESTAMP}
                </if>
            </set>
            where shop_id = #{item.shopId,jdbcType=BIGINT}
            AND order_id = #{item.orderId,jdbcType=BIGINT}
        </foreach>
    </update>
    <!--付尾款-->
    <select id="selectShopBalancePaidOrderLstByBuyersAndDateForPesBind" resultMap="BuyerOrderDTO">
        SELECT
        order_id, pay_balance_real as balancePayment,
        create_time as created, balance_time as balancePayTime, buyer_nick, freight as post_fee
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND balance_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
    </select>


    <select id="selectDownPaymentOrderIdByCreateTime" resultType="java.lang.Long">
		SELECT
			order_id
		FROM ${tableName}
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND
			create_time
		BETWEEN #{startDate,jdbcType=TIMESTAMP}
		AND #{endDate,jdbcType=TIMESTAMP}
		AND bargain_time
			IS NOT NULL

	</select>

    <select id="selectPayEarnestMoneyPresaleOrderByOrderLst" parameterType="map" resultType="java.lang.Long">
        SELECT DISTINCT order_id
        FROM ${tableName}
        WHERE create_time
        BETWEEN #{startDate,jdbcType=TIMESTAMP}
        AND #{endDate,jdbcType=TIMESTAMP}
        AND order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND shop_id = #{shopId,jdbcType=BIGINT}
        AND bargain_time
        IS NOT NULL
        AND order_status not in(121,131,221,1000,1001,2000)
    </select>


    <select id="selectOrderIdByShopIdAndBalanceTime" resultType="long">
		SELECT DISTINCT order_id
		FROM ${tableName}
		WHERE shop_id = #{shopId,jdbcType=BIGINT}
		AND balance_time BETWEEN #{startDate} AND #{endDate}
	</select>
    <select id="selectByTimeAndBuyerNicksForPresalePerformance" resultMap="PresaleOrderDTO">
        SELECT
        buyer_nick,sku_id,sku_name,goods_num,order_id,create_time,pay_bargain_real,bargain_time,pay_balance_real,balance_time
        FROM ${tableName}
        WHERE shop_id = #{shopId,jdbcType=BIGINT}
        AND ${timeType} BETWEEN #{startDate} AND #{endDate}
        <if test="buyerSet != null and buyerSet.size() != 0">
            AND buyer_nick IN
            <foreach collection="buyerSet" item="buyer" open="(" close=")" separator=",">
                #{buyer}
            </foreach>
        </if>
        <if test="orderIds != null and orderIds.size() != 0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        <if test="skuIds != null and skuIds.size() != 0">
            AND sku_id IN
            <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
    </select>
    <select id="selectFullPaymentOrderLstByBuyersAndDate" resultMap="BuyerOrderDTO">
        SELECT
        shop_id,buyer_nick,presale_id,sku_id,
        sku_name,goods_num,order_id,yushou_price,
        freight,order_pay_type,order_type,order_status,
        create_time,update_time,pay_bargain_real,
        pay_bargain_plan,bargain_time,
        pay_balance_real,pay_balance_plan,balance_time,balance_start_time,
        balance_end_time,order_time,balance_end_time_plan,company_id,out_stock_time
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND create_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
        and order_pay_type=1
    </select>

    <select id="selectPresaleOrderByOrderId" resultMap="PresaleOrderDTO">
        SELECT buyer_nick,sku_id,sku_name,goods_num,order_id,create_time,pay_bargain_real,bargain_time,pay_balance_real,balance_time,balance_start_time,balance_end_time,order_pay_type
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="selectShopOrderBlancePayTimeInAdjustDate" resultMap="PresaleOrderDTO">
        select *
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        and balance_time between  #{startDate} and #{endDate}
    </select>

    <select id="selectOrderIdByShopIdAndPayBalanceTime" resultMap="PresaleOrderDTO">
        select order_id,create_time, pay_bargain_real, pay_balance_real
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        and balance_time between  #{startDate} and #{endDate}
    </select>

    <select id="selectOrderIdByShopIdAndPayBalanceTime2" resultMap="PresaleOrderDTO">
        select order_id,create_time, pay_bargain_real, pay_balance_real
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          and create_time between  #{startDate} and #{endDate}
          and pay_balance_real = 0
    </select>

    <select id="selectOrderIdsUnbalance" resultType="java.lang.Long">
        select order_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          and create_time between  #{startDate} and #{endDate}
          and balance_time is null
          and bargain_time is not null
    </select>

    <select id="selectOrderIdsByCreated" resultType="java.lang.Long">
        select order_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          and create_time between  #{startDate} and #{endDate}
    </select>

    <select id="selectShopBargainNoBalanceOdIds" resultType="java.lang.Long">
        select order_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
            and bargain_time between #{startDate} and #{endDate}
            and balance_time is null
    </select>
</mapper>
