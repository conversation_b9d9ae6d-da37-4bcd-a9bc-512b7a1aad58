<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CustConsultGoodsMapper" >
  <resultMap id="CustConsultGoodsDO" type="com.pes.jd.model.DO.CustConsultGoodsDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
  </resultMap>
   <resultMap id="CustConsultGoodsDTO" type="com.pes.jd.model.DTO.CustConsultGoodsDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
     <result column="order_id" jdbcType="BIGINT" property="orderId" />
     <result column="item_price" jdbcType="DOUBLE" property="itemPrice" />
    <result column="item_num" jdbcType="INTEGER" property="itemNum" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, sku_id, customer, cs_nick, result
  </sql>
 
  <delete id="deleteCustConsultGoodsById" parameterType="java.lang.Long" >
    delete from pes_cust_consult_goods
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insertCustConsultGoods" parameterType="com.pes.jd.model.DO.CustConsultGoodsDO" >
    insert into pes_cust_consult_goods (id, shop_id, date, 
      sku_id, customer, cs_nick, 
      result)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=BIGINT}, #{customer,jdbcType=VARCHAR}, #{csNick,jdbcType=VARCHAR}, 
      #{result,jdbcType=INTEGER})
  </insert>
  
    <insert id="batchInsertCustConsultGoods" parameterType="map" >
        INSERT INTO  ${tableName} (shop_id, date, sku_id, customer, cs_nick, result,order_id)
        VALUES
        <foreach collection="custConsultGoodsLst" item="itm" separator=",">
         (
              #{itm.shopId,jdbcType=BIGINT},
              #{itm.date,jdbcType=DATE},
              #{itm.skuId,jdbcType=BIGINT},
              #{itm.customer,jdbcType=VARCHAR},
              #{itm.csNick,jdbcType=VARCHAR},
              #{itm.result,jdbcType=INTEGER},
              #{itm.orderId,jdbcType=BIGINT}
          )
        </foreach>
    </insert>
    <insert id="batchInsertCustConsultGoodsV2" parameterType="map" >
        INSERT INTO  ${tableName} (shop_id, date, sku_id, customer, cs_nick, result, order_id, is_enquiry)
        VALUES
        <foreach collection="custConsultGoodsLst" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.skuId,jdbcType=BIGINT},
            #{itm.customer,jdbcType=VARCHAR},
            #{itm.csNick,jdbcType=VARCHAR},
            #{itm.result,jdbcType=INTEGER},
            #{itm.orderId,jdbcType=BIGINT},
            #{itm.isEnquiry,jdbcType=BIGINT}
            )
        </foreach>
    </insert>
   <delete id="deleteCustConsultGoodsByShopIdAndDate" parameterType="map">
  	DELETE FROM ${tableName}
  	WHERE shop_id=#{shopId}
  	AND date BETWEEN #{startDate} and #{endDate}
  </delete>
  
 <update id="batchUpdateCustConsultGoods" parameterType="map" >
 	 <foreach collection="resultLst" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		 <set >
	        result = #{itm.result,jdbcType=INTEGER}
	    </set>
	    <where>
	     	 id=#{itm.id}
	    </where>
	</foreach>
  </update>
  
   <select id="selectCustConsultGoodsById" resultMap="CustConsultGoodsDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_cust_consult_goods
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  
   <select id="selectConsultNumByShopIdByCsNickByDate" parameterType="map" resultType="com.pes.jd.model.DTO.GoodsConsultSummaryDTO">
  	SELECT  sku_id skuId, count(DISTINCT customer) consultNum
  	FROM ${tableName}
  	where 
  	cs_nick=#{csNick}
  	AND shop_id=#{shopId}
  	AND date between #{startDate} and #{endDate}
  	GROUP BY sku_id 
  </select>
  
  <select id="selectBuyerPurchasesGoodsNumByShopIdByCsNickByDate" parameterType="map" resultType="com.pes.jd.model.DTO.GoodsConsultSummaryDTO">
  SELECT  sku_id skuId, count(sku_id) purchasesBuyerNum 
  FROM ${tableName}
  WHERE 
    cs_nick=#{csNick}
 	AND shop_id=#{shopId}
  	AND date between #{startDate} and #{endDate} 
 	AND result=1 
  GROUP BY sku_id 
  </select>
  
  <select id="selectConsultGoodsPurchaseResultByCsNickAndDate" parameterType="map" resultMap="CustConsultGoodsDTO">
  select consult.shop_id,
   	consult.date, 
   	consult.sku_id, 
   	consult.customer,
    consult.cs_nick, 
    consult.result,
    consult.order_id
    FROM 
  (
	  <foreach collection="tableNames" item="table" separator="UNION ALL">
	  	select shop_id, date, sku_id, customer, cs_nick, result,order_id
	  	from ${table.tableName}
	  	where 
	  		cs_nick =#{csNick}
	  	AND shop_id=#{shopId}
	  	AND date BETWEEN #{table.beginDate} AND #{table.endDate}
	  </foreach>
  
  )consult
 
  </select>
  
   <select id="selectCsConsultByshopIdByDate" resultType="com.pes.jd.model.DTO.CsPurchaseGoodsResultDTO">
  	select id,shop_id shopId, 
  	date date, 
  	sku_id skuId, 
  	customer buyerNick, 
  	cs_nick csNick, 
  	result result,
  	order_id orderId
  	from ${tableName}
  	where 
  		 shop_id=#{shopId}
  	and  date between #{startDate} and #{endDate} 
  	and  result=0
  </select>

    <delete id="deleteCustConsultGoodsByShopIdAndDateV2" parameterType="map">
        DELETE FROM ${tableName}
        WHERE shop_id=#{shopId}
          AND date BETWEEN #{startDate} and #{endDate}
    </delete>
</mapper>