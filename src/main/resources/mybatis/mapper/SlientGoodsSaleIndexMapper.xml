<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.SlientGoodsSaleIndexMapper" >
  <resultMap id="SlientGoodsSaleIndexDO" type="com.pes.jd.model.DO.SlientGoodsSaleIndexDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="purchase_buyer_num" property="purchaseBuyerNum" jdbcType="INTEGER" />
    <result column="sale_goods_num" property="saleGoodsNum" jdbcType="INTEGER" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="basel_field" >
    id, shop_id, date, sku_id, purchase_buyer_num, sale_goods_num, sale_amount
  </sql>
  <select id="selectSlientGoodsSaleIndexById" resultMap="SlientGoodsSaleIndexDO" parameterType="java.lang.Long" >
    select 
    <include refid="basel_field" />
    from pes_slient_goods_sale_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteSlientGoodsSaleIndex" parameterType="java.lang.Long" >
    delete from pes_slient_goods_sale_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSlientGoodsSaleIndex" parameterType="com.pes.jd.model.DO.SlientGoodsSaleIndexDO" >
    insert into pes_slient_goods_sale_index (id, shop_id, date, 
      sku_id, purchase_buyer_num, sale_goods_num, 
      sale_amount)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=BIGINT}, #{purchaseBuyerNum,jdbcType=INTEGER}, #{saleGoodsNum,jdbcType=INTEGER}, 
      #{saleAmount,jdbcType=DOUBLE})
  </insert>
  
  
   <insert id="batchInsertSlientGoodsSaleIndex" parameterType="map" >
    INSERT INTO  ${tableName} ( shop_id, date, 
      sku_id, purchase_buyer_num, sale_goods_num, 
      sale_amount)
    VALUES 
    <foreach collection="slienGoodsLst" item="itm" separator=",">
	(
		#{itm.shopId,jdbcType=BIGINT},
		#{itm.date,jdbcType=DATE},
		#{itm.skuId,jdbcType=BIGINT},
		#{itm.purchaseBuyerNum,jdbcType=INTEGER},
		#{itm.saleGoodsNum,jdbcType=INTEGER},
		#{itm.saleAmount,jdbcType=DOUBLE}
	)
    </foreach>
  </insert>
 
 <delete id="deleteSlientGoodsSaleIndexByShopIdByDate" parameterType="map">
 	DELETE FROM ${tableName}
 	WHERE shop_id=#{shopId}
 	AND date between #{startDate} and #{endDate}
 </delete>
  <update id="updateSlientGoodsSaleIndex" parameterType="com.pes.jd.model.DO.SlientGoodsSaleIndexDO" >
    update pes_slient_goods_sale_index
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="purchaseBuyerNum != null" >
        purchase_buyer_num = #{purchaseBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null" >
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleAmount != null" >
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>