<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.JobRecordMapper" >

  <resultMap id="JobPullRecordDTO" type="com.pes.jd.model.DTO.JobPullRecordDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_peer_flag" jdbcType="BIT" property="chatPeerFlag" />
    <result column="shop_category_flag" jdbcType="BIT" property="shopCategoryFlag" />
    <result column="shop_sku_flag" jdbcType="BIT" property="shopSkuFlag" />
    <result column="shop_good_flag" jdbcType="BIT" property="shopGoodFlag" />
    <result column="shop_dsr_flag" jdbcType="BIT" property="shopDsrFlag" />
    <result column="no_pay_order_flag" jdbcType="BIT" property="noPayOrderFlag" />
    <result column="order_created_flag" jdbcType="BIT" property="orderCreatedFlag" />
    <result column="order_modify_flag" jdbcType="BIT" property="orderModifyFlag" />
    <result column="order_presale_flag" jdbcType="BIT" property="orderPresaleFlag" />
    <result column="order_evaluation_flag" jdbcType="BIT" property="orderEvaluationFlag" />
    <result column="shop_pv_uv_flag" jdbcType="BIT" property="shopPvUvFlag" />
    <result column="order_refund_apply_flag" jdbcType="BIT" property="orderRefundApplyFlag" />
    <result column="order_refund_check_flag" jdbcType="BIT" property="orderRefundCheckFlag" />
    <result column="asc_order_refund_apply_flag" jdbcType="BIT" property="ascOrderRefundApplyFlag" />
    <result column="asc_order_refund_check_flag" jdbcType="BIT" property="ascOrderRefundCheckFlag" />
    <result column="leave_msg_flag" jdbcType="BIT" property="leaveMsgFlag" />
    <result column="cs_send_eval_flag" jdbcType="BIT" property="csSendEvalFlag" />
    <result column="update_cs_eval_flag" jdbcType="BIT" property="updateCsEvalFlag" />
    <result column="cs_eval_flag" jdbcType="BIT" property="csEvalFlag" />
    <result column="order_remark_flag" jdbcType="BIT" property="orderRemarkFlag" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="consume_time" jdbcType="BIGINT" property="consumeTime" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>


  <resultMap id="JobCalRecordDTO" type="com.pes.jd.model.DTO.JobCalRecordDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="common_chat_flag" jdbcType="BIT" property="commonChatFlag" />
    <result column="receive_quality_flag" jdbcType="BIT" property="receiveQualityFlag" />
    <result column="cs_order_index_flag" jdbcType="BIT" property="csOrderIndexFlag" />
    <result column="cs_order_bind_flag" jdbcType="BIT" property="csOrderBindFlag" />
    <result column="enquiry_chat_flag" jdbcType="BIT" property="enquiryChatFlag" />
    <result column="final_chat_data_flag" jdbcType="BIT" property="finalChatDataFlag" />
    <result column="enquiry_loss_flag" jdbcType="BIT" property="enquiryLossFlag" />
    <result column="cs_performance_flag" jdbcType="BIT" property="csPerformanceFlag" />
    <result column="cs_torder_performance" jdbcType="BIT" property="csTorderPerformance" />
    <result column="shop_day_overview_flag" jdbcType="BIT" property="shopDayOverviewFlag" />
    <result column="assit_index_flag" jdbcType="BIT" property="assitIndexFlag" />
    <result column="order_filte_flag" jdbcType="BIT" property="orderFilteFlag" />
    <result column="order_loss_flag" jdbcType="BIT" property="orderLossFlag" />
    <result column="outstock_loss_flag" jdbcType="BIT" property="outstockLossFlag" />
    <result column="team_loss_flag" jdbcType="BIT" property="teamLossFlag" />
    <result column="cs_order_eval_flag" jdbcType="BIT" property="csOrderEvalFlag" />
    <result column="cs_order_bind_index_flag" jdbcType="BIT" property="csOrderBindIndexFlag" />
    <result column="cs_goods_handle_flag" jdbcType="BIT" property="csGoodsHandleFlag" />
    <result column="cs_goods_sum_flag" jdbcType="BIT" property="csGoodsSumFlag" />
    <result column="cs_slient_sale_flag" jdbcType="BIT" property="csSlientSaleFlag" />
    <result column="cs_silent_goods_sum_flag" jdbcType="BIT" property="csSilentGoodsSumFlag" />
    <result column="team_day_refund_flag" jdbcType="BIT" property="teamDayRefundFlag" />
    <result column="shop_day_refund_flag" jdbcType="BIT" property="shopDayRefundFlag" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
  </resultMap>


  <sql id="base_field_pull">
    id, shop_id, date, chat_peer_flag, shop_category_flag, shop_sku_flag, shop_good_flag,
    shop_dsr_flag, no_pay_order_flag, order_created_flag, order_modify_flag, order_presale_flag,
    order_evaluation_flag, shop_pv_uv_flag, order_refund_apply_flag, order_refund_check_flag,
    asc_order_refund_apply_flag, asc_order_refund_check_flag, leave_msg_flag, cs_send_eval_flag,
    update_cs_eval_flag, cs_eval_flag, order_remark_flag, result,modified,msg
  </sql>

  <sql id="base_field_cal">
    id, shop_id, date, result, common_chat_flag, receive_quality_flag, cs_order_index_flag,
    cs_order_bind_flag, enquiry_chat_flag, final_chat_data_flag, enquiry_loss_flag, cs_performance_flag,
    cs_torder_performance, shop_day_overview_flag, assit_index_flag, order_filte_flag,
    order_loss_flag, outstock_loss_flag, team_loss_flag, cs_order_eval_flag,
    cs_order_bind_index_flag, cs_goods_handle_flag, cs_goods_sum_flag, cs_silent_goods_sum_flag,
    team_day_refund_flag, shop_day_refund_flag,modified,msg
  </sql>

  <insert id="insertPullJobRecord" parameterType="map">
    INSERT INTO ${jobPullRecordTableName} (
      shop_id,
      date,
      result,
      chat_peer_flag,
      shop_category_flag,
      shop_sku_flag,
      shop_good_flag,
      shop_dsr_flag,
      no_pay_order_flag,
      order_created_flag,
      order_modify_flag,
      order_presale_flag,
      order_evaluation_flag,
      shop_pv_uv_flag,
      order_refund_apply_flag,
      order_refund_check_flag,
      asc_order_refund_apply_flag,
      asc_order_refund_check_flag,
      leave_msg_flag,
      cs_send_eval_flag,
      update_cs_eval_flag,
      cs_eval_flag,
      order_remark_flag,
      modified,
      msg,
      consume_time,
      run_status
      )
    VALUES
    (
      #{record.shopId,jdbcType=BIGINT},
      #{record.date,jdbcType=DATE},
      #{record.result,jdbcType=BIT},
      #{record.chatPeerFlag,jdbcType=BIT},
      #{record.shopCategoryFlag,jdbcType=BIT},
      #{record.shopSkuFlag,jdbcType=BIT},
      #{record.shopGoodFlag,jdbcType=BIT},
      #{record.shopDsrFlag,jdbcType=BIT},
      #{record.noPayOrderFlag,jdbcType=BIT},
      #{record.orderCreatedFlag,jdbcType=BIT},
      #{record.orderModifyFlag,jdbcType=BIT},
      #{record.orderPresaleFlag,jdbcType=BIT},
      #{record.orderEvaluationFlag,jdbcType=BIT},
      #{record.shopPvUvFlag,jdbcType=BIT},
      #{record.orderRefundApplyFlag,jdbcType=BIT},
      #{record.orderRefundCheckFlag,jdbcType=BIT},
      #{record.ascOrderRefundApplyFlag,jdbcType=BIT},
      #{record.ascOrderRefundCheckFlag,jdbcType=BIT},
      #{record.leaveMsgFlag,jdbcType=BIT},
      #{record.csSendEvalFlag,jdbcType=BIT},
      #{record.updateCsEvalFlag,jdbcType=BIT},
      #{record.csEvalFlag,jdbcType=BIT},
      #{record.orderRemarkFlag,jdbcType=BIT},
       #{record.modified,jdbcType=TIMESTAMP},
       #{record.msg,jdbcType=VARCHAR},
       #{record.consumeTime,jdbcType=BIGINT},
       #{record.runStatus,jdbcType=TINYINT}
      )
  </insert>



  <insert id="insertJobCalRecord" parameterType="map">
    INSERT INTO ${jobCalRecordTableName} (
      shop_id,
      date,
      result,
      common_chat_flag,
      receive_quality_flag,
      cs_order_index_flag,
      cs_order_bind_flag,
      enquiry_chat_flag,
      final_chat_data_flag,
      enquiry_loss_flag,
      cs_performance_flag,
      cs_torder_performance,
      shop_day_overview_flag,
      assit_index_flag,
      order_filte_flag,
      order_loss_flag,
      outstock_loss_flag,
      team_loss_flag,
      cs_order_eval_flag,
      cs_order_bind_index_flag,
      cs_goods_handle_flag,
      cs_goods_sum_flag,
      cs_slient_sale_flag,
      cs_silent_goods_sum_flag,
      team_day_refund_flag,
      shop_day_refund_flag,
      modified,
      msg
      )
    VALUES (
      #{record.shopId,jdbcType=BIGINT},
      #{record.date,jdbcType=DATE},
      #{record.result,jdbcType=BIT},
      #{record.commonChatFlag,jdbcType=BIT},
      #{record.receiveQualityFlag,jdbcType=BIT},
      #{record.csOrderIndexFlag,jdbcType=BIT},
      #{record.csOrderBindFlag,jdbcType=BIT},
      #{record.enquiryChatFlag,jdbcType=BIT},
      #{record.finalChatDataFlag,jdbcType=BIT},
      #{record.enquiryLossFlag,jdbcType=BIT},
      #{record.csPerformanceFlag,jdbcType=BIT},
      #{record.csTorderPerformance,jdbcType=BIT},
      #{record.shopDayOverviewFlag,jdbcType=BIT},
      #{record.assitIndexFlag,jdbcType=BIT},
      #{record.orderFilteFlag,jdbcType=BIT},
      #{record.orderLossFlag,jdbcType=BIT},
      #{record.outstockLossFlag,jdbcType=BIT},
      #{record.teamLossFlag,jdbcType=BIT},
      #{record.csOrderEvalFlag,jdbcType=BIT},
      #{record.csOrderBindIndexFlag,jdbcType=BIT},
      #{record.csGoodsHandleFlag,jdbcType=BIT},
      #{record.csGoodsSumFlag,jdbcType=BIT},
      #{record.csSlientSaleFlag,jdbcType=BIT},
      #{record.csSilentGoodsSumFlag,jdbcType=BIT},
      #{record.teamDayRefundFlag,jdbcType=BIT},
      #{record.shopDayRefundFlag,jdbcType=BIT},
       #{record.modified,jdbcType=TIMESTAMP},
       #{record.msg,jdbcType=VARCHAR}
      )
  </insert>

  <delete id="deleteJobPullRecordByShopIdAndDate" parameterType="map" >
    DELETE FROM ${jobPullRecordTableName}
    WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
      AND date = #{date,jdbcType=DATE}
  </delete>

  <delete id="deleteJobCalRecordByShopIdAndDate" parameterType="map">
    DELETE FROM ${jobCalRecordTableName}
    WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
      AND date = #{date,jdbcType=DATE}
  </delete>

  <update id="updateJobPullRecordStatusByShopIdAndDate" parameterType="map">
    UPDATE ${jobPullRecordTableName}
    <set>
      run_status = #{status,jdbcType=TINYINT},
      modified = #{modified}
    </set>
    WHERE
    shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </update>

  <update id="updateJobPullRecordByShopIdAndDate" parameterType="map">
    UPDATE ${jobPullRecordTableName}
    <set>
      <if test="record.result != null" >
        result = #{record.result,jdbcType=BIT},
      </if>
      <if test="record.chatPeerFlag != null" >
        chat_peer_flag = #{record.chatPeerFlag,jdbcType=BIT},
      </if>
      <if test="record.shopCategoryFlag != null" >
        shop_category_flag = #{record.shopCategoryFlag,jdbcType=BIT},
      </if>
      <if test="record.shopSkuFlag != null" >
        shop_sku_flag = #{record.shopSkuFlag,jdbcType=BIT},
      </if>
      <if test="record.shopGoodFlag != null" >
        shop_good_flag = #{record.shopGoodFlag,jdbcType=BIT},
      </if>
      <if test="record.shopDsrFlag != null" >
        shop_dsr_flag = #{record.shopDsrFlag,jdbcType=BIT},
      </if>
      <if test="record.noPayOrderFlag != null" >
        no_pay_order_flag = #{record.noPayOrderFlag,jdbcType=BIT},
      </if>
      <if test="record.orderCreatedFlag != null" >
        order_created_flag = #{record.orderCreatedFlag,jdbcType=BIT},
      </if>
      <if test="record.orderModifyFlag != null" >
        order_modify_flag = #{record.orderModifyFlag,jdbcType=BIT},
      </if>
      <if test="record.orderPresaleFlag != null" >
        order_presale_flag = #{record.orderPresaleFlag,jdbcType=BIT},
      </if>
      <if test="record.orderEvaluationFlag != null" >
        order_evaluation_flag = #{record.orderEvaluationFlag,jdbcType=BIT},
      </if>
      <if test="record.shopPvUvFlag != null" >
        shop_pv_uv_flag = #{record.shopPvUvFlag,jdbcType=BIT},
      </if>
      <if test="record.orderRefundApplyFlag != null" >
        order_refund_apply_flag = #{record.orderRefundApplyFlag,jdbcType=BIT},
      </if>
      <if test="record.orderRefundCheckFlag != null" >
        order_refund_check_flag = #{record.orderRefundCheckFlag,jdbcType=BIT},
      </if>
      <if test="record.ascOrderRefundApplyFlag != null" >
        asc_order_refund_apply_flag = #{record.ascOrderRefundApplyFlag,jdbcType=BIT},
      </if>
      <if test="record.ascOrderRefundCheckFlag != null" >
        asc_order_refund_check_flag = #{record.ascOrderRefundCheckFlag,jdbcType=BIT},
      </if>
      <if test="record.leaveMsgFlag != null" >
        leave_msg_flag = #{record.leaveMsgFlag,jdbcType=BIT},
      </if>
      <if test="record.csSendEvalFlag != null" >
        cs_send_eval_flag = #{record.csSendEvalFlag,jdbcType=BIT},
      </if>
      <if test="record.updateCsEvalFlag != null" >
        update_cs_eval_flag = #{record.updateCsEvalFlag,jdbcType=BIT},
      </if>
      <if test="record.csEvalFlag != null" >
        cs_eval_flag = #{record.csEvalFlag,jdbcType=BIT},
      </if>
      <if test="record.orderRemarkFlag != null" >
        order_remark_flag = #{record.orderRemarkFlag,jdbcType=BIT},
      </if>
      <if test="record.modified != null" >
        modified = #{record.modified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.msg != null" >
        msg = #{record.msg,jdbcType=VARCHAR},
      </if>
       <if test="record.runStatus != null" >
           run_status = #{record.runStatus,jdbcType=TINYINT},
      </if>
      <if test="record.consumeTime != null" >
        consume_time = #{record.consumeTime,jdbcType=BIGINT}
      </if>
    </set>
    WHERE
    shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </update>

  <select id="seleteJobPullRecordByShopIdAndDate" resultMap="JobPullRecordDTO" parameterType="map" >
    SELECT  *
    FROM ${jobPullRecordTableName}
    WHERE
      shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </select>

  <select id="selectJobCalRecordByShopIdAndDate" parameterType="map" resultMap="JobCalRecordDTO">
    SELECT *
    FROM ${jobCalRecordTableName}
    WHERE
      id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getJobPullRecordByShopIdAndDate" parameterType="map" resultMap="JobPullRecordDTO">
    SELECT *
    FROM ${tableName}
    WHERE
      shop_id = #{shopId,jdbcType=BIGINT}
      AND date = #{date,jdbcType=DATE}
  </select>

  <select id="getJobCalRecordByShopIdAndDate" parameterType="map" resultMap="JobCalRecordDTO">
    SELECT *
    FROM ${tableName}
    WHERE
      shop_id = #{shopId,jdbcType=BIGINT}
      AND date = #{date,jdbcType=DATE}
  </select>
</mapper>