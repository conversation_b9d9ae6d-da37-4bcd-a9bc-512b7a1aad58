<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderFilterMapper">

    <resultMap id="OrderFilterDTO" type="com.pes.jd.model.DTO.OrderFilterDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="VARCHAR"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="base_field">
		id, shop_id, date, order_id, trade_id, sku_id, created, num
	</sql>

    <insert id="insertOrderFilter"
            parameterType="com.pes.jd.model.DO.OrderFilter">
		INSERT INTO pes_order_filter
		( shop_id, date,
		order_id, trade_id, num_iid,
		created, type, num
		)
		VALUES
		(
		#{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE},
		#{orderId,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT}, #{numIid,jdbcType=BIGINT},
		#{created,jdbcType=TIMESTAMP}, #{type,jdbcType=VARCHAR},
		#{num,jdbcType=INTEGER}
		)
	</insert>

    <delete id="deleteOrderFilterById"
            parameterType="java.lang.Long">
		DELETE FROM pes_order_filter
		WHERE id = #{id,jdbcType=BIGINT}
	</delete>

    <update id="updateOrderFilterBySelective"
            parameterType="com.pes.jd.model.DO.OrderFilter">
        UPDATE pes_order_filter
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="tradeId != null">
                trade_id = #{tradeId,jdbcType=BIGINT},
            </if>
            <if test="numIid != null">
                num_iid = #{numIid,jdbcType=BIGINT},
            </if>
            <if test="created != null">
                created = #{created,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=INTEGER},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>


    <insert id="persistOrderFilters" parameterType="java.util.Map">
        INSERT INTO
        ${tableName}(order_id,shop_id,trade_id,num_iid,created,date)
        VALUES
        <foreach collection="orders" item="itm" separator=","
                 index="index">
            (#{itm.orderId},#{itm.shopId},#{itm.tradeId},#{itm.numIid},#{itm.created},#{itm.date},#{itm.num})
        </foreach>
    </insert>

    <!-- 以文件流形式插入数据 -->
    <insert id="persistOrderFilterByFile"
            parameterType="java.util.Map">
		load data local infile #{filePath} into table
		${tableName}
		fields terminated by '``MYPES`' optionally enclosed by ''
		escaped by ''
		lines terminated by '`MYPES`\n'
		(order_id,shop_id,sku_id,price,created,date,buyer_nick,num);
	</insert>

    <select id="selectIdsByOrderFilterIds"
            parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT id FROM ${tableName}
        WHERE 
        shop_id = #{shopId}
        AND
        order_id IN
        <foreach collection="oids" item="oid" open="(" separator=","
                 close=")">
            #{oid}
        </foreach>
    </select>

    <select id="queryPesOrdersByShopId" parameterType="map" resultMap="OrderFilterDTO">
		SELECT id, shop_id, date, order_id, trade_id, sku_id, created, num, price
		FROM 
		    ${tableName} pof
		WHERE 
		    pof.shop_id = #{shopId}
		    AND date BETWEEN #{startDate} and #{endDate} 
		ORDER BY 
		    pof.created desc		
	</select>

    <!-- 根据orderIds删除数据 -->
    <delete id="deleteOrdersFilterByIds"
            parameterType="java.util.Map">
        DELETE FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND
        id IN
        <foreach collection="ids" index="index" item="id" open="("
                 separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
     <select id="selectOrderFilterByOrderIds"
            parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT id FROM ${tableName}
        WHERE 
        shop_id = #{shopId}
        AND
        order_id IN
        <foreach collection="oids" item="oid" open="(" separator=","
                 close=")">
            #{oid}
        </foreach>
    </select>
</mapper>