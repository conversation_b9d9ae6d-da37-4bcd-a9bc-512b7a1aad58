<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsOrderEvaluateMapper">

	<resultMap id="CsOrderEvaluateDTO" type="com.pes.jd.model.DTO.CsOrderEvaluateDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="date" property="date" jdbcType="DATE" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="good_evaluate_num_total" property="totalGoodEvaluateNum" jdbcType="INTEGER" />
		<result column="neutral_evaluate_num_total" property="totalNeutralEvaluateNum" jdbcType="INTEGER" />
		<result column="bad_evaluate_num_total" property="totalBadEvaluateNum" jdbcType="INTEGER" />
		<result column="good_evaluate_num_pre_sale" property="preGoodEvaluateNum" jdbcType="INTEGER" />
		<result column="neutral_evaluate_num_pre_sale" property="preNeutralEvaluateNum" jdbcType="INTEGER" />
		<result column="bad_evaluate_num_pre_sale" property="preBadEvaluateNum" jdbcType="INTEGER" />
		<result column="good_evaluate_num_bet_sale" property="betGoodEvaluateNum" jdbcType="INTEGER" />
		<result column="neutral_evaluate_num_bet_sale" property="betNeutralEvaluateNum" jdbcType="INTEGER" />
		<result column="bad_evaluate_num_bet_sale" property="betBadEvaluateNum" jdbcType="INTEGER" />
		<result column="good_evaluate_num_after_sale" property="afterGoodEvaluateNum" jdbcType="INTEGER" />
		<result column="neutral_evaluate_num_after_sale" property="afterNeutralEvaluateNum" jdbcType="INTEGER" />
		<result column="bad_evaluate_num_after_sale" property="afterBadEvaluateNum" jdbcType="INTEGER" />
	</resultMap>

	<insert id="insertCsOrderEvaluate" parameterType="map">
		INSERT INTO ${tableName} (`shop_id`, `date`, `cs_nick`, 
			`good_evaluate_num_total`,`neutral_evaluate_num_total`,`bad_evaluate_num_total`, 
			`good_evaluate_num_pre_sale`,`neutral_evaluate_num_pre_sale`,`bad_evaluate_num_pre_sale`, 
			`good_evaluate_num_bet_sale`,`neutral_evaluate_num_bet_sale`,`bad_evaluate_num_bet_sale`, 
			`good_evaluate_num_after_sale`,`neutral_evaluate_num_after_sale`,`bad_evaluate_num_after_sale`)
		VALUES
		<foreach collection="orderEvaluateDOLst" item="item" separator=",">
			(#{item.shopId,jdbcType=BIGINT},#{item.date,jdbcType=DATE},#{item.csNick,jdbcType=VARCHAR},
			#{item.totalGoodEvaluateNum,jdbcType=VARCHAR},#{item.totalNeutralEvaluateNum,jdbcType=VARCHAR},#{item.totalBadEvaluateNum,jdbcType=VARCHAR},
			#{item.preGoodEvaluateNum,jdbcType=VARCHAR},#{item.preNeutralEvaluateNum,jdbcType=VARCHAR},#{item.preBadEvaluateNum,jdbcType=VARCHAR},
			#{item.betGoodEvaluateNum,jdbcType=VARCHAR},#{item.betNeutralEvaluateNum,jdbcType=VARCHAR},#{item.betBadEvaluateNum,jdbcType=VARCHAR},
			#{item.afterGoodEvaluateNum,jdbcType=VARCHAR},#{item.afterNeutralEvaluateNum,jdbcType=VARCHAR},#{item.afterBadEvaluateNum,jdbcType=INTEGER})
		</foreach>
	</insert>

	<delete id="deleteCsOrderEvaluateByDateByShop" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date = #{date}
	</delete>

</mapper>