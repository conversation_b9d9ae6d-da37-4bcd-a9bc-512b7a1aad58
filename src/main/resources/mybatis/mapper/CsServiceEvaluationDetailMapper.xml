<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsServiceEvaluationDetailMapper" >

	<resultMap id="CsServiceEvaluationDetailDTO" type="com.pes.jd.model.DTO.CsServiceEvaluationDetailDTO" >
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
		<result column="eval_time" property="evalTime" jdbcType="TIMESTAMP" />
		<result column="eval_code" property="evalCode" jdbcType="INTEGER" />
    	<result column="sid" property="sid" jdbcType="VARCHAR" />
    	<result column="desc" property="desc" jdbcType="VARCHAR" />
	</resultMap>
  
  <sql id="base_field" >
    id, shop_id, cs_nick, buyer_nick, eval_time, eval_code,`desc`,sid
  </sql>
  
  	<insert id="batchCsEvals" parameterType="map">
		INSERT INTO ${tableName}(shop_id, cs_nick, buyer_nick, eval_time, eval_code,`desc`,sid)
		VALUES
		<foreach collection="csEvals" item="itm" separator="," >
			(#{itm.shopId}, #{itm.csNick}, #{itm.buyerNick}, #{itm.evalTime}, #{itm.evalCode}, #{itm.desc}, #{itm.sid})
		</foreach>
	</insert>
	<update id="updateSidById">
		UPDATE ${tableName}
		SET sid = #{sid}
		WHERE id = #{id} AND shop_id = #{shopId}
	</update>

	<delete id="deleteCsEvalsByShopIdAndDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE
			shop_id = #{shopId}
		AND 
			eval_time BETWEEN #{startDate} AND #{endDate}
	</delete>
	<select id="searchByDate" resultMap="CsServiceEvaluationDetailDTO">
		SELECT
		<include refid="base_field"/>
		FROM ${tableName}
		WHERE eval_time BETWEEN #{startDate} AND #{endDate} 
		AND shop_id = #{shopId} and cs_nick = #{nick}
	</select>

	<select id="searchByDateAndShopId" resultMap="CsServiceEvaluationDetailDTO">
		SELECT
		<include refid="base_field"/>
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND sid IN
		<foreach collection="sids" item="sid" open="(" close=")" separator=",">
			#{sid}
		</foreach>
	</select>
    <select id="findEmptySidEvaluations" resultType="com.pes.jd.model.DTO.CsServiceEvaluationDetailDTO">
		SELECT *
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		  AND sid IS NULL
		  AND eval_time BETWEEN #{startTime} AND #{endTime};
	</select>


</mapper>