<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsSendEvalMapper" >

  <resultMap id="CsSendEvalDTO" type="com.pes.jd.model.DTO.CsSendEvalDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="send_time" property="sendTime" jdbcType="TIMESTAMP" />
    <result column="eval_time" property="evalTime" jdbcType="TIMESTAMP" />
    <result column="eval_code" property="evalCode" jdbcType="INTEGER" />
    <result column="sid" property="sid" jdbcType="VARCHAR" />
    <result column="desc" property="desc" jdbcType="VARCHAR" />
  </resultMap>
  
  <insert id="batchInsertCsSendEvals" parameterType="map" >
    INSERT INTO ${tableName} (shop_id,cs_nick, customer, send_time, eval_time, eval_code, sid,`desc`)
    VALUES 
    <foreach collection="csSendEvalList" item="csSendEval" separator=",">
	    (#{csSendEval.shopId},#{csSendEval.csNick}, #{csSendEval.customer}, #{csSendEval.sendTime}, 
	      #{csSendEval.evalTime}, #{csSendEval.evalCode}, #{csSendEval.sid}, #{csSendEval.desc})
    </foreach>
  </insert>

  <delete id="deleteCsSendEvalsByShopIdByDate" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE send_time BETWEEN #{startDate} AND #{endDate}
    AND shop_id = #{shopId}
  </delete>
  
  <select id="selectCsSendEvalByDate" parameterType="map" resultMap="CsSendEvalDTO">
  	SELECT * FROM ${tableName}
    WHERE shop_id = #{shopId}
    AND sid IN
    <foreach collection="sids" item="sid" open="(" close=")" separator=",">
      #{sid}
    </foreach>
  </select>
  
</mapper>