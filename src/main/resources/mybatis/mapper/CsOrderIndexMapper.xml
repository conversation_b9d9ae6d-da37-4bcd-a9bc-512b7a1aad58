<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsOrderIndexMapper" >
	    	
  <resultMap id="CsOrderIndexDTO" type="com.pes.jd.model.DTO.CsOrderIndexDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="date" jdbcType="DATE" property="date" />
		<result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
		<result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
		<result column="order_id" jdbcType="BIGINT" property="orderId" />
		<result column="order_flag" jdbcType="INTEGER" property="orderFlag" />
		<result column="order_created" jdbcType="TIMESTAMP" property="orderCreated" />
		<result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate" />
		<result column="order_payment" jdbcType="DOUBLE" property="orderPayment" />
		<result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum" />
		<result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee" />
	    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
		<result column="first_reply_date" jdbcType="TIMESTAMP" property="firstReplyDate" />
		<result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
		<result column="last_reply_date" jdbcType="TIMESTAMP" property="lastReplyDate" />
		<result column="bc_last_reply_date" jdbcType="TIMESTAMP" property="bcLastReplyDate" />
		<result column="bc_first_chat_date" jdbcType="TIMESTAMP" property="bcFirstChatDate" />
	    <result column="bc_first_reply_date" jdbcType="TIMESTAMP" property="bcFirstReplyDate" />
		<result column="bp_last_reply_date" jdbcType="TIMESTAMP" property="bpLastReplyDate" />
		<result column="ap_first_chat_date" jdbcType="TIMESTAMP" property="apFirstChatDate" />
		<result column="ap_first_reply_date" jdbcType="TIMESTAMP" property="apFirstReplyDate" />
		<result column="bc_chat_round_num" jdbcType="INTEGER" property="bcChatRoundNum" />
		<result column="bp_chat_round_num" jdbcType="INTEGER" property="bpChatRoundNum" />
		<result column="ac_first_reply_date" jdbcType="TIMESTAMP" property="acFirstReplyDate" />
		<result column="ac_first_chat_date" jdbcType="TIMESTAMP" property="acFirstChatDate" />
	    <result column="is_assit_order_create" jdbcType="BIT" property="assitOrderCreate" />
	    <result column="is_assit_order_in_followup" jdbcType="BIT" property="assitOrderInFollowup" />
	    <result column="is_assit_order_pay" jdbcType="BIT" property="assitOrderPay" />
	    <result column="silent_flag" jdbcType="INTEGER" property="silentFlag" />
	    <result column="urgepay_flag" jdbcType="INTEGER" property="urgepayFlag" />
	  	<result column="is_aftersale" jdbcType="BIT" property="afterSale" />
	  	<result column="is_goods_filte" jdbcType="BIT" property="isGoodsFilte" />
	  	<result column="is_presale" jdbcType="BIT" property="preSale" />
	    <result column="pay_type" jdbcType="INTEGER" property="payType" />
	    <result column="is_balance_pay" jdbcType="BIT" property="balancePay" />
   </resultMap>

	<resultMap id="CsOrderIndexForSearchDTO" type="com.pes.jd.model.DTO.CsOrderIndexForSearchDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="date" jdbcType="DATE" property="date" />
		<result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
		<result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
		<result column="order_id" jdbcType="BIGINT" property="orderId" />
		<result column="order_flag" jdbcType="TINYINT" property="orderFlag" />
		<result column="order_created" jdbcType="TIMESTAMP" property="orderCreated" />
		<result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate" />
		<result column="order_payment" jdbcType="DOUBLE" property="orderPayment" />
		<result column="order_goods_num" jdbcType="INTEGER" property="orderGoodsNum" />
		<result column="order_post_fee" jdbcType="DOUBLE" property="orderPostFee" />
		<result column="is_goods_filte" jdbcType="BIT" property="isGoodsFilte" />
		<result column="has_urgepay" jdbcType="BIT" property="hasUrgepay" />
		<result column="bc_chat_num" jdbcType="INTEGER" property="bcChatNum" />
		<result column="bc_reply_num" jdbcType="INTEGER" property="bcReplyNum" />
		<result column="bc_chat_round_num" jdbcType="INTEGER" property="bcChatRoundNum" />
		<result column="bc_first_reply_date" jdbcType="TIMESTAMP" property="bcFirstReplyDate" />
		<result column="bc_last_reply_date" jdbcType="TIMESTAMP" property="bcLastReplyDate" />
		<result column="bc_first_chat_date" jdbcType="TIMESTAMP" property="bcFirstChatDate" />
		<result column="bc_last_chat_date" jdbcType="TIMESTAMP" property="bcLastChatDate" />
		<result column="bp_reply_num" jdbcType="INTEGER" property="bpReplyNum" />
		<result column="bp_chat_round_num" jdbcType="INTEGER" property="bpChatRoundNum" />
		<result column="bp_first_reply_date" jdbcType="TIMESTAMP" property="bpFirstReplyDate" />
		<result column="bp_last_chat_date" jdbcType="TIMESTAMP" property="bpLastChatDate" />
		<result column="bp_last_reply_date" jdbcType="TIMESTAMP" property="bpLastReplyDate" />
		<result column="bp_chat_num" jdbcType="INTEGER" property="bpChatNum" />
		<result column="ac_first_reply_date" jdbcType="TIMESTAMP" property="acFirstReplyDate" />
		<result column="ac_first_chat_date" jdbcType="TIMESTAMP" property="acFirstChatDate" />
		<result column="ap_first_chat_date" jdbcType="TIMESTAMP" property="apFirstChatDate" />
		<result column="ap_first_reply_date" jdbcType="TIMESTAMP" property="apFirstReplyDate" />
		<result column="last_reply_date" jdbcType="TIMESTAMP" property="lastReplyDate" />
		<result column="first_reply_date" jdbcType="TIMESTAMP" property="firstReplyDate" />
		<result column="first_chat_date" jdbcType="TIMESTAMP" property="firstChatDate" />
		<result column="last_chat_date" jdbcType="TIMESTAMP" property="lastChatDate" />
		<result column="is_assit_order_create" jdbcType="BIT" property="isAssitOrderCreate" />
		<result column="is_assit_order_in_followup" jdbcType="BIT" property="isAssitOrderInFollowup" />
		<result column="is_assit_order_pay" jdbcType="BIT" property="isAssitOrderPay" />
		<result column="silent_flag" jdbcType="TINYINT" property="silentFlag" />
	</resultMap>

	<resultMap id="GoodsAnalysisOrderIndexDTO" type="com.pes.jd.model.DTO.GoodsAnalysisOrderIndexDTO">
		<id column="id" jdbcType="BIGINT" property="id"/>
		<result column="shop_id" jdbcType="BIGINT" property="shopId"/>
		<result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
		<result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
		<result column="date" jdbcType="DATE" property="date"/>
		<result column="order_id" jdbcType="BIGINT" property="orderId"/>
		<result column="bc_last_reply_date" jdbcType="TIMESTAMP" property="bcLastReplyDate"/>
		<result column="order_created" jdbcType="TIMESTAMP" property="orderCreated"/>
	</resultMap>

	<resultMap id="SimpleOrderIndexDTO" type="com.pes.jd.model.DTO.SimpleOrderIndexDTO">
		<result column="shop_id" jdbcType="BIGINT" property="shopId"/>
		<result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
		<result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick"/>
		<result column="date" jdbcType="DATE" property="date"/>
		<result column="order_id" jdbcType="BIGINT" property="orderId"/>
		<result column="urgepay_flag" jdbcType="BIT" property="urgepayFlag" />
		<result column="order_created" jdbcType="TIMESTAMP" property="orderCreated" />
		<result column="order_pay_date" jdbcType="TIMESTAMP" property="orderPayDate" />
	</resultMap>

  <sql id="base_field">
    shop_id, date, cs_nick, buyer_nick, order_id, order_flag, order_created, order_pay_date, 
    order_payment, order_goods_num, order_post_fee, is_goods_filte, has_urgepay, bc_chat_num, 
    bc_reply_num, bc_chat_round_num, bc_first_reply_date, bc_last_reply_date, bc_first_chat_date, 
    bc_last_chat_date, bp_reply_num, bp_chat_round_num, bp_first_reply_date, bp_last_chat_date, 
    bp_last_reply_date, bp_chat_num, ac_first_reply_date, ac_first_chat_date, ap_first_chat_date, 
    ap_first_reply_date, last_reply_date, first_reply_date, first_chat_date, last_chat_date, 
    is_assit_order_create, is_assit_order_in_followup, is_assit_order_pay, silent_flag
  </sql>

	<sql id="Base_Column_List">
    id, shop_id, date, cs_nick, buyer_nick, order_id, order_flag, order_created, order_pay_date,
    order_payment, order_goods_num, order_post_fee, is_goods_filte, has_urgepay, bc_chat_num,
    bc_reply_num, bc_chat_round_num, bc_first_reply_date, bc_last_reply_date, bc_first_chat_date,
    bc_last_chat_date, bp_reply_num, bp_chat_round_num, bp_first_reply_date, bp_last_chat_date,
    bp_last_reply_date, bp_chat_num, ac_first_reply_date, ac_first_chat_date, ap_first_chat_date,
    ap_first_reply_date, last_reply_date, first_reply_date, first_chat_date, last_chat_date,
    is_assit_order_create, is_assit_order_in_followup, is_assit_order_pay, silent_flag
  </sql>



	<insert id="batchInsertCsOrderIndex" parameterType="map">
    	INSERT INTO ${tableName} (shop_id, date, 
	      cs_nick, buyer_nick, order_id, 
	      order_flag, order_created, order_pay_date, 
	      order_payment, order_goods_num, order_post_fee,order_status,
		  is_goods_filte,bc_chat_num,
	      bc_reply_num, bc_chat_round_num, bc_first_reply_date, 
	      bc_last_reply_date, bc_first_chat_date, bc_last_chat_date, 
	      bp_reply_num, bp_chat_round_num, bp_first_reply_date, 
	      bp_last_chat_date, bp_last_reply_date, bp_chat_num, 
	      ac_first_reply_date, ac_first_chat_date, 
	      ap_first_chat_date, ap_first_reply_date, 
	      last_reply_date, first_reply_date, first_chat_date,last_chat_date,
		  silent_flag,is_assit_order_create,is_assit_order_in_followup,is_assit_order_pay,
		  is_aftersale,is_presale,urgepay_flag,pay_type,is_balance_pay

	    )
	    VALUES 
	    <foreach collection="csOrderIndexLst" item="itm" separator=",">
	      (
		      #{itm.shopId,jdbcType=BIGINT}, 
		      #{itm.date,jdbcType=DATE}, 
		      #{itm.csNick,jdbcType=VARCHAR}, 
		      #{itm.buyerNick,jdbcType=VARCHAR}, 
		      #{itm.orderId,jdbcType=BIGINT}, 
		      #{itm.orderFlag,jdbcType=TINYINT}, 
		      #{itm.orderCreated,jdbcType=TIMESTAMP}, 
		      #{itm.orderPayDate,jdbcType=TIMESTAMP}, 
		      #{itm.orderPayment,jdbcType=DOUBLE}, 
		      #{itm.orderGoodsNum,jdbcType=INTEGER}, 
		      #{itm.orderPostFee,jdbcType=DOUBLE},
			  #{itm.orderStatus,jdbcType=VARCHAR},
			  #{itm.isGoodsFilte,jdbcType=BIT},
			  #{itm.bcChatNum,jdbcType=INTEGER},
		      #{itm.bcReplyNum,jdbcType=INTEGER}, 
		      #{itm.bcChatRoundNum,jdbcType=INTEGER}, 
		      #{itm.bcFirstReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.bcLastReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.bcFirstChatDate,jdbcType=TIMESTAMP}, 
		      #{itm.bcLastChatDate,jdbcType=TIMESTAMP}, 
		      #{itm.bpReplyNum,jdbcType=INTEGER}, 
		      #{itm.bpChatRoundNum,jdbcType=INTEGER}, 
		      #{itm.bpFirstReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.bpLastChatDate,jdbcType=TIMESTAMP}, 
		      #{itm.bpLastReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.bpChatNum,jdbcType=INTEGER}, 
		      #{itm.acFirstReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.acFirstChatDate,jdbcType=TIMESTAMP}, 
		      #{itm.apFirstChatDate,jdbcType=TIMESTAMP}, 
		      #{itm.apFirstReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.lastReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.firstReplyDate,jdbcType=TIMESTAMP}, 
		      #{itm.firstChatDate,jdbcType=TIMESTAMP}, 
		      #{itm.lastChatDate,jdbcType=TIMESTAMP},
			  #{itm.silentFlag,jdbcType=INTEGER},
			  #{itm.assitOrderCreate,jdbcType=BIT},
			  #{itm.assitOrderInFollowup,jdbcType=BIT},
			  #{itm.assitOrderPay,jdbcType=BIT},
			  #{itm.aftersale,jdbcType=BIT},
			  #{itm.preSale,jdbcType=BIT},
			  #{itm.urgepayFlag,jdbcType=INTEGER},
			  #{itm.payType,jdbcType=INTEGER},
			  #{itm.balancePay,jdbcType=BIT}
	      )
	    </foreach>
  </insert>
  
  <delete id="deleteCsOrderIndexById" parameterType="java.lang.Long">
    DELETE FROM ${tableName}
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteShopCsOrderIndexByDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date,jdbcType=DATE}
  </delete>
    
  <delete id="deleteCsOrderIndexByShopAndDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
  
   <update id="updateCsOrderIndexsForAssist" parameterType="map">
   
    <foreach collection="csOrderIndeLst" item="itm" open="" close="" separator=";">
		UPDATE ${tableName}
		<set>
			<if test="itm.assitOrderCreate != null">
				is_assit_order_create = #{itm.assitOrderCreate,jdbcType=BIT},
			</if>
			<if test="itm.assitOrderInFollowup != null">
				is_assit_order_in_followup = #{itm.assitOrderInFollowup,jdbcType=BIT},
			</if>
			<if test="itm.assitOrderPay != null">
				is_assit_order_pay = #{itm.assitOrderPay,jdbcType=BIT}
			</if>
		</set>
		WHERE
	    	id = #{itm.id,jdbcType=BIGINT}
	</foreach>
	
  </update>
  
  <select id="getCsOrderIndexById" parameterType="java.lang.Long" resultMap="CsOrderIndexDTO">
    SELECT 
    	<include refid="base_field" />
    FROM ${tableName}
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>
  
	<select id="selectShopCsOrderIndexLstByDate" resultMap="CsOrderIndexDTO">
		SELECT
			*
		FROM ${tableName}
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
	</select>
	
  
	<select id="selectShopCsOrderIndexLstByCsAndDate" resultMap="CsOrderIndexDTO">
		SELECT
			*
		FROM ${tableName}
		WHERE
			cs_nick = #{csNick,jdbcType=VARCHAR}
		AND date = #{date,jdbcType=DATE}
		AND shop_id = #{shopId,jdbcType=BIGINT}
<!-- 		AND buyer_nick IN -->
<!-- 		<foreach collection="buyerLst" item="buyerNick" open="(" close=")"  separator=","> -->
<!-- 		 	#{buyerNick,jdbcType=VARCHAR} -->
<!-- 		</foreach> -->
	</select>

	<select id="searchByDateShop" resultMap="CsOrderIndexForSearchDTO">
		select <include refid="Base_Column_List" />
		from ${tableName}
		where shop_id = #{shopId} and date between #{startDate} and #{endDate}
	</select>
	
	<select id="selectShopCsOrderIndexLstByCsAndDateAndBuyerLst" resultMap="CsOrderIndexDTO">
		SELECT *
		FROM ${tableName}
		WHERE
			cs_nick = #{csNick,jdbcType=VARCHAR}
		AND date = #{date,jdbcType=DATE}
		AND buyer_nick IN
		<foreach collection="buyerNickLst" item="buyerNick" open="(" close=")"  separator=",">
		 	#{buyerNick,jdbcType=VARCHAR}
		</foreach>
		AND shop_id = #{shopId,jdbcType=BIGINT}
	</select>
	
	<select id="selectShopCsOrderIndexLstByDateAndBuyerLst" resultMap="CsOrderIndexDTO">
		SELECT 
			id,
			date,
			shop_id,
			cs_nick, 
			buyer_nick, 
			order_id, 
			order_flag, 
			order_created, 
			order_pay_date, 
			order_payment, 
			order_goods_num, 
	    	order_post_fee, 
	    	first_chat_date, 
	    	first_reply_date,
			last_reply_date, 
			bc_last_reply_date, 
			bc_first_chat_date, 
			bp_last_reply_date, 
			ap_first_chat_date, 
	    	ap_first_reply_date,
	    	bc_chat_round_num,
	    	bp_chat_round_num, 
	    	ac_first_reply_date,
			silent_flag,
			is_aftersale,
			is_goods_filte,
			is_presale
		FROM ${tableName}
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND date = #{date,jdbcType=DATE}
		AND buyer_nick IN
		<foreach collection="buyerNickLst" item="buyerNick" open="(" close=")"  separator=",">
		 	#{buyerNick,jdbcType=VARCHAR}
		</foreach>
		 
	</select>

	<select id="selectShopCsOrderIndexLstByDateRangeAndBuyerLst" resultMap="CsOrderIndexDTO">
		SELECT
		id,
		date,
		shop_id,
		cs_nick,
		buyer_nick,
		order_id,
		order_flag,
		order_created,
		order_pay_date,
		order_payment,
		order_goods_num,
		order_post_fee,
		first_chat_date,
		first_reply_date,
		last_reply_date,
		bc_last_reply_date,
		bc_first_chat_date,
		bp_last_reply_date,
		ap_first_chat_date,
		ap_first_reply_date,
		bc_chat_round_num,
		bp_chat_round_num,
		ac_first_reply_date,
		ac_first_chat_date,
		silent_flag,
		urgepay_flag,
		is_aftersale,
		is_goods_filte,
		is_presale,
		pay_type,
		is_balance_pay
		FROM ${tableName}
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND buyer_nick IN
		<foreach collection="buyerNickLst" item="buyerNick" open="(" close=")"  separator=",">
			#{buyerNick,jdbcType=VARCHAR}
		</foreach>
		order by date
	</select>

	<select id="selectShopCsOrderIndexLstForEnquiry" resultMap="CsOrderIndexDTO">
		SELECT
		id,
		date,
		shop_id,
		cs_nick,
		buyer_nick,
		order_id,
		order_flag,
		order_created,
		order_pay_date,
		order_payment,
		order_goods_num,
		order_post_fee,
		order_status,
		first_chat_date,
		first_reply_date
		last_reply_date,
		bc_last_reply_date,
		bc_first_chat_date,
		bc_first_reply_date,
		bp_last_reply_date,
		ap_first_chat_date,
		ap_first_reply_date,
		bc_chat_round_num,
		bp_chat_round_num,
		ac_first_reply_date,
		is_aftersale,
		urgepay_flag,
		is_assit_order_create,
		is_assit_order_in_followup,
		is_assit_order_pay

		FROM ${tableName}
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND date = #{date,jdbcType=DATE}
		AND cs_nick = #{csNick,jdbcType=VARCHAR}

	</select>


	<select id="selectShopCsOrderIndexLstForTest" resultMap="CsOrderIndexDTO">
		SELECT
		id,
		date,
		shop_id,
		cs_nick,
		buyer_nick,
		order_id,
		order_flag,
		order_created,
		order_pay_date,
		order_payment,
		order_goods_num,
		order_post_fee,
		order_status,
		first_chat_date,
		first_reply_date
		last_reply_date,
		bc_last_reply_date,
		bc_first_chat_date,
		bc_first_reply_date,
		bp_last_reply_date,
		ap_first_chat_date,
		ap_first_reply_date,
		bc_chat_round_num,
		bp_chat_round_num,
		ac_first_reply_date,
		is_aftersale,
		silent_flag,
		urgepay_flag,
		is_assit_order_create,
		is_assit_order_in_followup,
		is_assit_order_pay

		FROM ${tableName}
		WHERE
			order_id = #{orderId,jdbcType=BIGINT}

	</select>


	<select id="selectShopCsOrderIndexOrderIdLstByCsAndDateAndBuyerLst" resultMap="SimpleOrderIndexDTO">
		SELECT cs_nick,date,buyer_nick,order_id,urgepay_flag,order_created,order_pay_date
		FROM ${tableName}
		WHERE
			shop_id = #{shopId,jdbcType=BIGINT}
		AND cs_nick = #{csNick,jdbcType=VARCHAR}
		AND date = #{date}
		AND order_created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND buyer_nick IN
		<foreach collection="buyerNickLst" item="buyerNick" open="(" close=")" separator=",">
			#{buyerNick,jdbcType=VARCHAR}
		</foreach>
	</select>
	
	<select id="selectShopAllCsOrderIndexLstByCsAndDate" resultMap="CsOrderIndexDTO">
		SELECT
			*
		FROM ${tableName}
		WHERE
			 date = #{date,jdbcType=DATE}
		AND shop_id = #{shopId,jdbcType=BIGINT}
<!-- 		AND buyer_nick IN -->
<!-- 		<foreach collection="buyerLst" item="buyerNick" open="(" close=")"  separator=","> -->
<!-- 		 	#{buyerNick,jdbcType=VARCHAR} -->
<!-- 		</foreach> -->
	</select>
	
	<select id="selectOrderIndexByOrderIdByCs" parameterType="map" resultMap="CsOrderIndexDTO">
		SELECT shop_id,cs_nick,order_id,order_created,order_pay_date,first_chat_date
		FROM ${tableName}
		WHERE date BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND cs_nick = #{sellerNick}
		AND order_id IN
		<foreach collection="shopGoodsReviewLst" item="shopGoodsReview" open="(" close=")"  separator=",">
		 	#{shopGoodsReview.orderId,jdbcType=BIGINT}
		</foreach>
		AND shop_id = #{shopId,jdbcType=BIGINT}
	</select>

	<select id="selectShopCsOrderIndexLstByCsNickAndOrderIdLstAndDate" resultMap="GoodsAnalysisOrderIndexDTO">
		SELECT id, shop_id, date, cs_nick, buyer_nick, order_id,bc_last_reply_date,order_created
		FROM ${tableName}
		WHERE
			 order_created  BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
		AND shop_id = #{shopId,jdbcType=BIGINT}
		AND cs_nick=#{csNick}
		AND order_id in
		  <foreach collection="orderIdSet" item="orderId" separator="," open="(" close=")">
			#{orderId}
		</foreach>
	</select> 

	<select id="selectShopSilentCreatedOrderHasChatLossByDate" parameterType="map" resultType="com.pes.jd.model.DTO.SilentOrderLossDTO">
		SELECT shop_id shopId,date,cs_nick csNick,buyer_nick buyerNick,
		order_goods_num orderGoodsNum,ac_first_reply_date acFirstReplyDate,
		ac_first_chat_date acFirstChatDate,order_id orderId,order_created created,
		order_payment payment,
		first_chat_date firstChatDate,last_chat_date lastChatDate,is_presale presale
		FROM ${tableName}
		WHERE order_created BETWEEN #{startDate} AND #{endDate}
		AND first_chat_date > order_created
		AND order_pay_date IS NULL
		AND shop_id = #{shopId}
		AND pay_type !=1
	</select>

	<select id="selectAfterSaleBuyer" parameterType="map"  resultType="java.lang.String" >
        SELECT DISTINCT tb.buyer_nick buyerNick
        FROM ${tableName} tb
        WHERE tb.shop_id = #{shopId}
        AND tb.date = #{date}
        AND tb.is_aftersale =1
        AND tb.buyer_nick IN
        <foreach collection="buyerNickSet" item="buyerNick" open="(" separator="," close=")">
            #{buyerNick}
        </foreach>
    </select>

	<select id="selectShopCsOrderIndexByOrderIdLstForLossRecord" parameterType="map" resultType="com.pes.jd.model.DTO.CsOrderBindInfoDTO">
		SELECT shop_id shopId,order_id orderId,ac_first_reply_date acFirstReplyDate,ac_first_chat_date acFirstChatDate,first_chat_date firstChatDate,last_chat_date lastChatDate
		FROM ${tableName}
		WHERE order_id IN
		<foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
			#{orderId}
		</foreach>
	</select>

	<select id="selectBalancePayShopCsOrderIndexLstByDate" resultMap="CsOrderIndexDTO">
                    SELECT *
                    FROM ${tableName}
                    WHERE
                    shop_id = #{shopId,jdbcType=BIGINT}
                    and date = #{date}
                    and is_balance_pay=1
	</select>

  <select id="selectPresaleOrderIndexByShopIdAndDate" resultMap="CsOrderIndexDTO">
	  				SELECT *
                    FROM ${tableName}
                    WHERE
                    shop_id = #{shopId,jdbcType=BIGINT}
                    and date between  #{startDate} and #{endDate}
                    and is_presale=1
    </select>
</mapper>