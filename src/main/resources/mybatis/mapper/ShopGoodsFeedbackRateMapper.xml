<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopGoodsFeedbackRateMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopGoodsFeedbackRateDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="goods_id" property="goodsId" jdbcType="BIGINT" />
    <result column="good_rate" property="goodRate" jdbcType="DOUBLE" />
    <result column="type" property="type" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, date, goods_id, good_rate, type
  </sql>
  <select id="selectShopGoodsFeedbackIdsByShopIdByDate"  resultType="java.lang.Long" >
    select 
    id
    from ${tableName}
    where shop_id=#{shopId}
    and date=#{date}
  </select>
  <delete id="deleteByShopIdByIds"  >
    delete from ${tableName}
    where shop_id=#{shopId}
    and id in
    <foreach collection="ids" item="id" separator="," open="(" close=")">
      #{id}
    </foreach>
  </delete>
  <insert id="batchInsertShopGoodsFeedbackRate"  >
    insert into ${tableName}
    ( shop_id, date, goods_id, good_rate, type)
    values
    <foreach collection="list" item="itm" separator="," >
        (
          #{itm.shopId,jdbcType=BIGINT},
          #{itm.date,jdbcType=DATE},
          #{itm.goodsId,jdbcType=BIGINT},
          #{itm.goodRate,jdbcType=DOUBLE},
          #{itm.type,jdbcType=TINYINT}
        )
    </foreach>
  </insert>
  <insert id="insertShopGoodsFeedbackRateByFile" parameterType="map">
		load data local
		infile #{filePath} into table ${tableName}
		fields terminated by
		'``MYPES`' optionally enclosed by '' escaped by ''
	    lines terminated by '`MYPES`\n'
	    (shop_id, date, goods_id, good_rate, type);
	</insert>
</mapper>