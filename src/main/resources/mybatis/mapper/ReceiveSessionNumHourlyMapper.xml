<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ReceiveSessionNumHourlyMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ReceiveSessionNumHourlyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="hour_0" jdbcType="INTEGER" property="hour0" />
    <result column="hour_1" jdbcType="INTEGER" property="hour1" />
    <result column="hour_2" jdbcType="INTEGER" property="hour2" />
    <result column="hour_3" jdbcType="INTEGER" property="hour3" />
    <result column="hour_4" jdbcType="INTEGER" property="hour4" />
    <result column="hour_5" jdbcType="INTEGER" property="hour5" />
    <result column="hour_6" jdbcType="INTEGER" property="hour6" />
    <result column="hour_7" jdbcType="INTEGER" property="hour7" />
    <result column="hour_8" jdbcType="INTEGER" property="hour8" />
    <result column="hour_9" jdbcType="INTEGER" property="hour9" />
    <result column="hour_10" jdbcType="INTEGER" property="hour10" />
    <result column="hour_11" jdbcType="INTEGER" property="hour11" />
    <result column="hour_12" jdbcType="INTEGER" property="hour12" />
    <result column="hour_13" jdbcType="INTEGER" property="hour13" />
    <result column="hour_14" jdbcType="INTEGER" property="hour14" />
    <result column="hour_15" jdbcType="INTEGER" property="hour15" />
    <result column="hour_16" jdbcType="INTEGER" property="hour16" />
    <result column="hour_17" jdbcType="INTEGER" property="hour17" />
    <result column="hour_18" jdbcType="INTEGER" property="hour18" />
    <result column="hour_19" jdbcType="INTEGER" property="hour19" />
    <result column="hour_20" jdbcType="INTEGER" property="hour20" />
    <result column="hour_21" jdbcType="INTEGER" property="hour21" />
    <result column="hour_22" jdbcType="INTEGER" property="hour22" />
    <result column="hour_23" jdbcType="INTEGER" property="hour23" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, cs_nick, date, hour_0, hour_1, hour_2, hour_3, hour_4, hour_5, hour_6, 
    hour_7, hour_8, hour_9, hour_10, hour_11, hour_12, hour_13, hour_14, hour_15, hour_16, 
    hour_17, hour_18, hour_19, hour_20, hour_21, hour_22, hour_23
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_receive_session_num_hourly
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_receive_session_num_hourly
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByTimePoint">
    delete from ${tableName}
    where date = #{date} and shop_id = #{shopId}
    <if test="nick != null">
      and cs_nick = #{nick}
    </if>
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DO.ReceiveSessionNumHourlyDO">
    insert into ${tableName} (id, shop_id, cs_nick,
      date, hour_0, hour_1, hour_2, 
      hour_3, hour_4, hour_5, 
      hour_6, hour_7, hour_8, 
      hour_9, hour_10, hour_11, 
      hour_12, hour_13, hour_14, 
      hour_15, hour_16, hour_17, 
      hour_18, hour_19, hour_20, 
      hour_21, hour_22, hour_23
      )
    values (#{record.id,jdbcType=BIGINT}, #{record.shopId,jdbcType=BIGINT}, #{record.csNick,jdbcType=VARCHAR},
      #{record.date,jdbcType=DATE}, #{record.hour0,jdbcType=INTEGER}, #{record.hour1,jdbcType=INTEGER}, #{record.hour2,jdbcType=INTEGER},
      #{record.hour3,jdbcType=INTEGER}, #{record.hour4,jdbcType=INTEGER}, #{record.hour5,jdbcType=INTEGER},
      #{record.hour6,jdbcType=INTEGER}, #{record.hour7,jdbcType=INTEGER}, #{record.hour8,jdbcType=INTEGER},
      #{record.hour9,jdbcType=INTEGER}, #{record.hour10,jdbcType=INTEGER}, #{record.hour11,jdbcType=INTEGER},
      #{record.hour12,jdbcType=INTEGER}, #{record.hour13,jdbcType=INTEGER}, #{record.hour14,jdbcType=INTEGER},
      #{record.hour15,jdbcType=INTEGER}, #{record.hour16,jdbcType=INTEGER}, #{record.hour17,jdbcType=INTEGER},
      #{record.hour18,jdbcType=INTEGER}, #{record.hour19,jdbcType=INTEGER}, #{record.hour20,jdbcType=INTEGER},
      #{record.hour21,jdbcType=INTEGER}, #{record.hour22,jdbcType=INTEGER}, #{record.hour23,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DO.ReceiveSessionNumHourlyDO">
    insert into pes_receive_session_num_hourly
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="csNick != null">
        cs_nick,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="hour0 != null">
        hour_0,
      </if>
      <if test="hour1 != null">
        hour_1,
      </if>
      <if test="hour2 != null">
        hour_2,
      </if>
      <if test="hour3 != null">
        hour_3,
      </if>
      <if test="hour4 != null">
        hour_4,
      </if>
      <if test="hour5 != null">
        hour_5,
      </if>
      <if test="hour6 != null">
        hour_6,
      </if>
      <if test="hour7 != null">
        hour_7,
      </if>
      <if test="hour8 != null">
        hour_8,
      </if>
      <if test="hour9 != null">
        hour_9,
      </if>
      <if test="hour10 != null">
        hour_10,
      </if>
      <if test="hour11 != null">
        hour_11,
      </if>
      <if test="hour12 != null">
        hour_12,
      </if>
      <if test="hour13 != null">
        hour_13,
      </if>
      <if test="hour14 != null">
        hour_14,
      </if>
      <if test="hour15 != null">
        hour_15,
      </if>
      <if test="hour16 != null">
        hour_16,
      </if>
      <if test="hour17 != null">
        hour_17,
      </if>
      <if test="hour18 != null">
        hour_18,
      </if>
      <if test="hour19 != null">
        hour_19,
      </if>
      <if test="hour20 != null">
        hour_20,
      </if>
      <if test="hour21 != null">
        hour_21,
      </if>
      <if test="hour22 != null">
        hour_22,
      </if>
      <if test="hour23 != null">
        hour_23,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="hour0 != null">
        #{hour0,jdbcType=INTEGER},
      </if>
      <if test="hour1 != null">
        #{hour1,jdbcType=INTEGER},
      </if>
      <if test="hour2 != null">
        #{hour2,jdbcType=INTEGER},
      </if>
      <if test="hour3 != null">
        #{hour3,jdbcType=INTEGER},
      </if>
      <if test="hour4 != null">
        #{hour4,jdbcType=INTEGER},
      </if>
      <if test="hour5 != null">
        #{hour5,jdbcType=INTEGER},
      </if>
      <if test="hour6 != null">
        #{hour6,jdbcType=INTEGER},
      </if>
      <if test="hour7 != null">
        #{hour7,jdbcType=INTEGER},
      </if>
      <if test="hour8 != null">
        #{hour8,jdbcType=INTEGER},
      </if>
      <if test="hour9 != null">
        #{hour9,jdbcType=INTEGER},
      </if>
      <if test="hour10 != null">
        #{hour10,jdbcType=INTEGER},
      </if>
      <if test="hour11 != null">
        #{hour11,jdbcType=INTEGER},
      </if>
      <if test="hour12 != null">
        #{hour12,jdbcType=INTEGER},
      </if>
      <if test="hour13 != null">
        #{hour13,jdbcType=INTEGER},
      </if>
      <if test="hour14 != null">
        #{hour14,jdbcType=INTEGER},
      </if>
      <if test="hour15 != null">
        #{hour15,jdbcType=INTEGER},
      </if>
      <if test="hour16 != null">
        #{hour16,jdbcType=INTEGER},
      </if>
      <if test="hour17 != null">
        #{hour17,jdbcType=INTEGER},
      </if>
      <if test="hour18 != null">
        #{hour18,jdbcType=INTEGER},
      </if>
      <if test="hour19 != null">
        #{hour19,jdbcType=INTEGER},
      </if>
      <if test="hour20 != null">
        #{hour20,jdbcType=INTEGER},
      </if>
      <if test="hour21 != null">
        #{hour21,jdbcType=INTEGER},
      </if>
      <if test="hour22 != null">
        #{hour22,jdbcType=INTEGER},
      </if>
      <if test="hour23 != null">
        #{hour23,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ReceiveSessionNumHourlyDO">
    update pes_receive_session_num_hourly
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="csNick != null">
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="hour0 != null">
        hour_0 = #{hour0,jdbcType=INTEGER},
      </if>
      <if test="hour1 != null">
        hour_1 = #{hour1,jdbcType=INTEGER},
      </if>
      <if test="hour2 != null">
        hour_2 = #{hour2,jdbcType=INTEGER},
      </if>
      <if test="hour3 != null">
        hour_3 = #{hour3,jdbcType=INTEGER},
      </if>
      <if test="hour4 != null">
        hour_4 = #{hour4,jdbcType=INTEGER},
      </if>
      <if test="hour5 != null">
        hour_5 = #{hour5,jdbcType=INTEGER},
      </if>
      <if test="hour6 != null">
        hour_6 = #{hour6,jdbcType=INTEGER},
      </if>
      <if test="hour7 != null">
        hour_7 = #{hour7,jdbcType=INTEGER},
      </if>
      <if test="hour8 != null">
        hour_8 = #{hour8,jdbcType=INTEGER},
      </if>
      <if test="hour9 != null">
        hour_9 = #{hour9,jdbcType=INTEGER},
      </if>
      <if test="hour10 != null">
        hour_10 = #{hour10,jdbcType=INTEGER},
      </if>
      <if test="hour11 != null">
        hour_11 = #{hour11,jdbcType=INTEGER},
      </if>
      <if test="hour12 != null">
        hour_12 = #{hour12,jdbcType=INTEGER},
      </if>
      <if test="hour13 != null">
        hour_13 = #{hour13,jdbcType=INTEGER},
      </if>
      <if test="hour14 != null">
        hour_14 = #{hour14,jdbcType=INTEGER},
      </if>
      <if test="hour15 != null">
        hour_15 = #{hour15,jdbcType=INTEGER},
      </if>
      <if test="hour16 != null">
        hour_16 = #{hour16,jdbcType=INTEGER},
      </if>
      <if test="hour17 != null">
        hour_17 = #{hour17,jdbcType=INTEGER},
      </if>
      <if test="hour18 != null">
        hour_18 = #{hour18,jdbcType=INTEGER},
      </if>
      <if test="hour19 != null">
        hour_19 = #{hour19,jdbcType=INTEGER},
      </if>
      <if test="hour20 != null">
        hour_20 = #{hour20,jdbcType=INTEGER},
      </if>
      <if test="hour21 != null">
        hour_21 = #{hour21,jdbcType=INTEGER},
      </if>
      <if test="hour22 != null">
        hour_22 = #{hour22,jdbcType=INTEGER},
      </if>
      <if test="hour23 != null">
        hour_23 = #{hour23,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ReceiveSessionNumHourlyDO">
    update pes_receive_session_num_hourly
    set shop_id = #{shopId,jdbcType=BIGINT},
      cs_nick = #{csNick,jdbcType=VARCHAR},
      date = #{date,jdbcType=DATE},
      hour_0 = #{hour0,jdbcType=INTEGER},
      hour_1 = #{hour1,jdbcType=INTEGER},
      hour_2 = #{hour2,jdbcType=INTEGER},
      hour_3 = #{hour3,jdbcType=INTEGER},
      hour_4 = #{hour4,jdbcType=INTEGER},
      hour_5 = #{hour5,jdbcType=INTEGER},
      hour_6 = #{hour6,jdbcType=INTEGER},
      hour_7 = #{hour7,jdbcType=INTEGER},
      hour_8 = #{hour8,jdbcType=INTEGER},
      hour_9 = #{hour9,jdbcType=INTEGER},
      hour_10 = #{hour10,jdbcType=INTEGER},
      hour_11 = #{hour11,jdbcType=INTEGER},
      hour_12 = #{hour12,jdbcType=INTEGER},
      hour_13 = #{hour13,jdbcType=INTEGER},
      hour_14 = #{hour14,jdbcType=INTEGER},
      hour_15 = #{hour15,jdbcType=INTEGER},
      hour_16 = #{hour16,jdbcType=INTEGER},
      hour_17 = #{hour17,jdbcType=INTEGER},
      hour_18 = #{hour18,jdbcType=INTEGER},
      hour_19 = #{hour19,jdbcType=INTEGER},
      hour_20 = #{hour20,jdbcType=INTEGER},
      hour_21 = #{hour21,jdbcType=INTEGER},
      hour_22 = #{hour22,jdbcType=INTEGER},
      hour_23 = #{hour23,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>