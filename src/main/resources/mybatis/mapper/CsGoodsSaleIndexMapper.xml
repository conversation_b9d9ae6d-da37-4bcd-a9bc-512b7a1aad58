<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsGoodsSaleIndexMapper" >
  <resultMap id="CsGoodsSaleIndexDO" type="com.pes.jd.model.DO.CsGoodsSaleIndexDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="VARCHAR" />
    <result column="purchase_buyer_num" property="purchaseBuyerNum" jdbcType="INTEGER" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="sale_goods_num" property="saleGoodsNum" jdbcType="INTEGER" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, sku_id, purchase_buyer_num, cs_nick, sale_goods_num, sale_amount
  </sql>
  <select id="selectCsGoodsSaleIndexById" resultMap="CsGoodsSaleIndexDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_cs_goods_sale_index
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteCsGoodsSaleIndexById" parameterType="java.lang.Long" >
    delete from pes_cs_goods_sale_index
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertCsGoodsSaleIndex" parameterType="com.pes.jd.model.DO.CsGoodsSaleIndexDO" >
    insert into pes_cs_goods_sale_index (id, shop_id, date, 
      sku_id, purchase_buyer_num, cs_nick, 
      sale_goods_num, sale_amount)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=VARCHAR}, #{purchaseBuyerNum,jdbcType=INTEGER}, #{csNick,jdbcType=VARCHAR}, 
      #{saleGoodsNum,jdbcType=INTEGER}, #{saleAmount,jdbcType=DOUBLE})
  </insert>
  
  <insert id="batchInsertCsGoodsSaleIndex" parameterType="map"  >
    insert into ${tableName} (
    shop_id, date, sku_id, purchase_buyer_num,cs_nick, 
	 sale_goods_num, sale_amount )
      VALUES
      <foreach collection="csSaleIndexLst" item="itm" separator=",">
		(   #{itm.shopId,jdbcType=BIGINT},
			#{itm.date,jdbcType=DATE},
			#{itm.skuId,jdbcType=BIGINT},
			#{itm.purchaseBuyerNum,jdbcType=INTEGER},
			#{itm.csNick,jdbcType=VARCHAR},
			#{itm.saleGoodsNum,jdbcType=INTEGER},
			#{itm.saleAmount,jdbcType=DOUBLE}
		)
      </foreach>
  </insert>
  
  <delete id="deleteCsGoodsSaleIndexByShopIdByDate" parameterType="map">
 	DELETE FROM ${tableName}
 	WHERE shop_id=#{shopId}
 	AND date between #{startDate} and #{endDate}
 </delete>
  <update id="updateCsGoodsSaleIndex" parameterType="com.pes.jd.model.DO.CsGoodsSaleIndexDO" >
    update pes_cs_goods_sale_index
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBuyerNum != null" >
        purchase_buyer_num = #{purchaseBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="saleGoodsNum != null" >
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleAmount != null" >
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
</mapper>