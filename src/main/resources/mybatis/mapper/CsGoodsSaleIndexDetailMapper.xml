<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsGoodsSaleIndexDetailMapper" >
  <resultMap id="CsGoodsSaleIndexDetailDO" type="com.pes.jd.model.DO.CsGoodsSaleIndexDetailDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="sale_goods_num" property="saleGoodsNum" jdbcType="INTEGER" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
     <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
     <result column="enquiry_date" property="enquiryDate" jdbcType="DATE" />
  </resultMap>
  <resultMap id="CsGoodsSaleIndexDetailDTO" type="com.pes.jd.model.DTO.CsGoodsSaleIndexDetailDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="sale_goods_num" property="saleGoodsNum" jdbcType="INTEGER" />
    <result column="sale_amount" property="saleAmount" jdbcType="DOUBLE" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
     <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
      <result column="enquiry_date" property="enquiryDate" jdbcType="DATE" />
  </resultMap>
  <sql id="base_field" >
    id, shop_id, date, sku_id, customer, cs_nick, sale_goods_num, sale_amount, order_id,out_stock_time,enquiry_date
  </sql>
  
  <insert id="insertCsGoodsSaleIndexDetail" parameterType="com.pes.jd.model.DO.CsGoodsSaleIndexDetailDO" >
    insert into pes_cs_goods_sale_index_detail (id, shop_id, date, 
      sku_id, customer, cs_nick, 
      sale_goods_num, sale_amount, order_id
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=BIGINT}, #{customer,jdbcType=VARCHAR}, #{csNick,jdbcType=VARCHAR}, 
      #{saleGoodsNum,jdbcType=INTEGER}, #{saleAmount,jdbcType=DOUBLE}, #{orderId,jdbcType=BIGINT}
      )
  </insert>
  
  <insert id="batchInsertCsGoodsSaleIndexDetail" parameterType="map"  >
    insert into ${tableName} (
    shop_id, date, sku_id, customer, cs_nick, 
	 sale_goods_num, sale_amount, order_id ,out_stock_time,enquiry_date)
      VALUES
      <foreach collection="csSaleDetailLst" item="itm" separator=",">
		(   #{itm.shopId,jdbcType=BIGINT},
			#{itm.date,jdbcType=DATE},
			#{itm.skuId,jdbcType=BIGINT},
			#{itm.customer,jdbcType=VARCHAR},
			#{itm.csNick,jdbcType=VARCHAR},
			#{itm.saleGoodsNum,jdbcType=INTEGER},
			#{itm.saleAmount,jdbcType=DOUBLE},
			#{itm.orderId,jdbcType=BIGINT},
			#{itm.outStockTime,jdbcType=TIMESTAMP},
			#{itm.enquiryDate,jdbcType=DATE}
		)
      </foreach>
  </insert>
  
  <delete id="deleteCsGoodsSaleIndexDetailById" parameterType="java.lang.Long" >
    delete from pes_cs_goods_sale_index_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteCsGoodsSaleIndexDetailByShopIdByDate" parameterType="map">
  	DELETE FROM ${tableName}
  	WHERE shop_id=#{shopId}
  	AND date between #{startDate} and #{endDate}
  </delete>
  <update id="updateCsGoodsSaleIndexDetail" parameterType="com.pes.jd.model.DO.CsGoodsSaleIndexDetailDO" >
    update pes_cs_goods_sale_index_detail
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="skuId != null" >
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="customer != null" >
        customer = #{customer,jdbcType=VARCHAR},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="saleGoodsNum != null" >
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleAmount != null" >
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
   <select id="selectCsGoodsSaleIndexDetailById" resultMap="CsGoodsSaleIndexDetailDO" parameterType="java.lang.Long" >
    select 
    <include refid="base_field" />
    from pes_cs_goods_sale_index_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectCsGoodsSaleIndexDetailByCsNickByDate" resultMap="CsGoodsSaleIndexDetailDTO">
  	SELECT <include refid="base_field"></include>
  	FROM ${tableName}
  	WHERE 
  	 cs_nick=#{csNick}
  	AND	shop_id=#{shopId}
  	AND date between #{startDate} and #{endDate}
  </select>
</mapper>