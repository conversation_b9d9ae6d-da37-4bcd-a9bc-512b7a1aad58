<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.CsMapper">

    <sql id="base_field">
    nick, shop_id, type, simple_name, lock_time, cs_status, modified_date
  </sql>

    <resultMap id="CsDTO" type="com.pes.jd.model.DTO.CsDTO">
        <id column="nick" property="nick" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="simple_name" property="simpleName" jdbcType="VARCHAR"/>
        <result column="lock_time" property="lockTime" jdbcType="TIMESTAMP"/>
        <result column="cs_status" property="csStatus" jdbcType="TINYINT"/>
        <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insertCs" parameterType="com.pes.jd.model.DO.Cs">
    INSERT INTO pes_cs (nick, shop_id, type,
      simple_name)
    VALUES (#{nick,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT},
      #{simpleName,jdbcType=VARCHAR})
  </insert>

    <delete id="deleteCsById" parameterType="java.lang.String">
    DELETE FROM pes_cs
    WHERE nick = #{nick,jdbcType=VARCHAR}
  </delete>

    <update id="updateCsById" parameterType="com.pes.jd.model.DO.Cs">
        UPDATE pes_cs
        <set>
            <if test="shopId != null">
                shop_id = #{shopId,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=TINYINT},
            </if>
            <if test="simpleName != null">
                simple_name = #{simpleName,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE nick = #{nick,jdbcType=VARCHAR}
    </update>

    <!--   查询店铺下的客服 -->
    <select id="selectCsLstByShop" resultMap="CsDTO">
        SELECT
        <include refid="base_field"></include>
        FROM pes_cs
        WHERE
        shop_id = #{shop.shopId,jdbcType=BIGINT}
        AND type = #{csType,jdbcType=TINYINT}
    </select>

    <select id="selectCsLstByShopIdAndType" resultMap="CsDTO">
        SELECT
        <include refid="base_field"></include>
        FROM pes_cs
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        <!--   AND type = #{csType,jdbcType=TINYINT} -->
    </select>


    <!--测试mybatisPlus分页-->
    <select id="selectCsLstByShopIdAndTypePage" resultMap="CsDTO">
        SELECT
        <include refid="base_field"></include>
        FROM pes_cs
        WHERE
        shop_id = #{shopId}
        AND type = #{csType}
    </select>

</mapper>