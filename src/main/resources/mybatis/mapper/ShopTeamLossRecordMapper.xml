<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopTeamLossRecordMapper" >

  <resultMap id="ShopTeamLossRecordDTO" type="com.pes.jd.model.DTO.ShopTeamLossRecordDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="order_num" property="orderNum" jdbcType="INTEGER" />
    <result column="customer_num" property="customerNum" jdbcType="INTEGER" />
    <result column="order_goods_num" property="orderGoodsNum" jdbcType="INTEGER" />
    <result column="order_sale_amount" property="orderSaleAmount" jdbcType="DOUBLE" />
    <result column="type" property="type" jdbcType="INTEGER" />
  </resultMap>

  <delete id="deleteShopTeamLossRecordByShopByDate" parameterType="map" >
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId}
    AND date = #{date}
    AND type = #{lossType}
  </delete>

  <insert id="insertShopTeamLossRecord" parameterType="map" >
    INSERT INTO ${tableName} (shop_id,date,order_num,customer_num,order_goods_num,order_sale_amount,type)
    VALUES 
    	(#{shopLoss.shopId},#{shopLoss.date},#{shopLoss.orderNum},#{shopLoss.customerNum},
    	#{shopLoss.orderGoodsNum},#{shopLoss.orderSaleAmount},#{shopLoss.type})
  </insert>
  
</mapper>