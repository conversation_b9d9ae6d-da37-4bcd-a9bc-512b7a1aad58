<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsPerformancePresaleMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsPerformancePresaleDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="shop_id" jdbcType="BIGINT" property="shopId"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick"/>
        <result column="sku_id" jdbcType="BIGINT" property="skuId"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="activity_id" jdbcType="VARCHAR" property="activityId"/>
        <result column="consult_buyer_num" jdbcType="INTEGER" property="consultBuyerNum"/>
        <result column="enquiry_buyer_num" jdbcType="INTEGER" property="enquiryBuyerNum"/>
        <result column="enquiry_ordered_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBuyerNum"/>
        <result column="enquiry_ordered_sku_num" jdbcType="INTEGER" property="enquiryOrderedSkuNum"/>
        <result column="enquiry_ordered_bargain_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBargainBuyerNum"/>
        <result column="enquiry_ordered_bargain_sku_num" jdbcType="INTEGER" property="enquiryOrderedBargainSkuNum"/>
        <result column="enquiry_ordered_bargain_amount" jdbcType="DOUBLE" property="enquiryOrderedBargainAmount"/>
        <result column="enquiry_ordered_balance_buyer_num" jdbcType="INTEGER" property="enquiryOrderedBalanceBuyerNum"/>
        <result column="enquiry_ordered_balance_sku_num" jdbcType="INTEGER" property="enquiryOrderedBalanceSkuNum"/>
        <result column="enquiry_ordered_balance_amount" jdbcType="DOUBLE" property="enquiryOrderedBalanceAmount"/>
        <result column="to_ordered_bargain_buyer_num" jdbcType="INTEGER" property="toOrderedBargainBuyerNum"/>
        <result column="to_ordered_balance_buyer_num" jdbcType="INTEGER" property="toOrderedBalanceBuyerNum"/>
        <result column="ordered_buyer_num" jdbcType="INTEGER" property="orderedBuyerNum"/>
        <result column="ordered_sku_num" jdbcType="INTEGER" property="orderedSkuNum"/>
        <result column="ordered_bargain_buyer_num" jdbcType="INTEGER" property="orderedBargainBuyerNum"/>
        <result column="ordered_bargain_sku_num" jdbcType="INTEGER" property="orderedBargainSkuNum"/>
        <result column="ordered_bargain_amount" jdbcType="DOUBLE" property="orderedBargainAmount"/>
        <result column="ordered_balance_buyer_num" jdbcType="INTEGER" property="orderedBalanceBuyerNum"/>
        <result column="ordered_balance_sku_num" jdbcType="INTEGER" property="orderedBalanceSkuNum"/>
        <result column="ordered_balance_amount" jdbcType="DOUBLE" property="orderedBalanceAmount"/>

    </resultMap>

    <sql id="base_field">
  	    id,shop_id,`date`,cs_nick,sku_id,sku_name,activity_id,consult_buyer_num,enquiry_buyer_num,
  	    enquiry_ordered_buyer_num,
        enquiry_ordered_sku_num,
        enquiry_ordered_bargain_buyer_num,
        enquiry_ordered_bargain_sku_num,
        enquiry_ordered_bargain_amount,
        enquiry_ordered_balance_buyer_num,
        enquiry_ordered_balance_sku_num,
        enquiry_ordered_balance_amount,
  	    to_ordered_bargain_buyer_num,
  	    to_ordered_balance_buyer_num,
  	    ordered_buyer_num,
  	    ordered_sku_num,
  	    ordered_bargain_buyer_num,
  	    ordered_bargain_sku_num,
  	    ordered_bargain_amount,
  	    ordered_balance_buyer_num,
  	    ordered_balance_sku_num,
  	    ordered_balance_amount
  </sql>

    <insert id="batchInsertCsPerformancePresale" parameterType="com.pes.jd.model.DO.CsPerformancePresaleDO">
        INSERT INTO ${tableName}
        (
        shop_id,`date`,cs_nick,sku_id,sku_name,activity_id,consult_buyer_num,enquiry_buyer_num,
        enquiry_ordered_buyer_num,
        enquiry_ordered_sku_num,
        enquiry_ordered_bargain_buyer_num,
        enquiry_ordered_bargain_sku_num,
        enquiry_ordered_bargain_amount,
        enquiry_ordered_balance_buyer_num,
        enquiry_ordered_balance_sku_num,
        enquiry_ordered_balance_amount,
        to_ordered_bargain_buyer_num,
        to_ordered_balance_buyer_num,
        ordered_buyer_num,
        ordered_sku_num,
        ordered_bargain_buyer_num,
        ordered_bargain_sku_num,
        ordered_bargain_amount,
        ordered_balance_buyer_num,
        ordered_balance_sku_num,
        ordered_balance_amount
        )
        VALUES
        <foreach collection="performances" item="itm" separator=",">
            (
            #{itm.shopId,jdbcType=BIGINT},
            #{itm.date,jdbcType=DATE},
            #{itm.csNick,jdbcType=VARCHAR},
            #{itm.skuId,jdbcType=INTEGER},
            #{itm.skuName,jdbcType=VARCHAR},
            #{itm.activityId,jdbcType=VARCHAR},
            #{itm.consultBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedSkuNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBargainBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBargainSkuNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBargainAmount,jdbcType=DOUBLE},
            #{itm.enquiryOrderedBalanceBuyerNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBalanceSkuNum,jdbcType=INTEGER},
            #{itm.enquiryOrderedBalanceAmount,jdbcType=DOUBLE},
            #{itm.toOrderedBargainBuyerNum,jdbcType=DOUBLE},
            #{itm.toOrderedBalanceBuyerNum,jdbcType=DOUBLE},
            #{itm.orderedBuyerNum,jdbcType=INTEGER},
            #{itm.orderedSkuNum,jdbcType=INTEGER},
            #{itm.orderedBargainBuyerNum,jdbcType=INTEGER},
            #{itm.orderedBargainSkuNum,jdbcType=INTEGER},
            #{itm.orderedBargainAmount,jdbcType=DOUBLE},
            #{itm.orderedBalanceBuyerNum,jdbcType=INTEGER},
            #{itm.orderedBalanceSkuNum,jdbcType=INTEGER},
            #{itm.orderedBalanceAmount,jdbcType=DOUBLE}
            )
        </foreach>

    </insert>

    <delete id="deleteByShopIdAndDateAndActivityId">
        DELETE FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND `date` BETWEEN #{startDate} AND #{endDate}
        AND activity_id = #{activityId,jdbcType=VARCHAR}
    </delete>

</mapper>