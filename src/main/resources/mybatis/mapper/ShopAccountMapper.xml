<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopAccountMapper">

  <resultMap id="ShopAccountDO" type="com.pes.jd.model.DO.ShopAccount">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
    <result column="nick" jdbcType="VARCHAR" property="nick" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="role" jdbcType="VARCHAR" property="role" />
     <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_account" jdbcType="BIT" property="isAccount" />
  </resultMap>
  
  <sql id="base_field">
    id, seller_id, nick, shop_id, role, status,user_name,is_account
  </sql>
  
  <insert id="insertShopAccount" parameterType="com.pes.jd.model.DO.ShopAccount">
    INSERT INTO pes_shop_account (id, seller_id, nick, shop_id, role, status,user_name)
    VALUES 
    (
    	#{id,jdbcType=BIGINT}, #{sellerId,jdbcType=BIGINT}, #{nick,jdbcType=VARCHAR}, 
      	#{shopId,jdbcType=BIGINT}, #{role,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},#{userName}
     )
  </insert>
  
  <delete id="deleteShopAccountById" parameterType="java.lang.Long">
    DELETE FROM pes_shop_account
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>
  
  <update id="updateShopAccountById" parameterType="com.pes.jd.model.DO.ShopAccount">
    UPDATE pes_shop_account
    <set>
      <if test="sellerId != null">
        seller_id = #{sellerId,jdbcType=BIGINT},
      </if>
      <if test="nick != null">
        nick = #{nick,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </update>
  
  
  <select id="selectShopAccountByShopId" parameterType="java.lang.Long" resultType="com.pes.jd.model.DTO.ShopAccountDTO">
    SELECT id,seller_id sellerId,nick,shop_id shopId,role,status
    FROM pes_shop_account
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
  </select>
	
	 <insert id="insertShopAccountOfShop" parameterType="java.util.List">
        INSERT INTO pes_shop_account (seller_id,shop_id,nick,role,status,user_name,is_account)
        values
        <foreach collection="subUsers" item="itm" index="index" separator=",">
            (#{itm.sellerId},#{itm.shopId},#{itm.nick},#{itm.role},#{itm.status},#{itm.userName},#{itm.isAccount})
        </foreach>
    </insert>

	
	<delete id="deleteShopAccountOfShop" parameterType="java.lang.String">
		DELETE
		FROM
		pes_shop_account
		WHERE
		shop_id = #{shopId}
	</delete>
</mapper>