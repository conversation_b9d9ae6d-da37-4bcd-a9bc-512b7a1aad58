<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderMapper">

    <resultMap id="OrderDTO" type="com.pes.jd.model.DTO.OrderDTO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <result column="seller_nick" property="sellerNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="post_fee" property="postFee" jdbcType="DOUBLE"/>
        <result column="consign_time" property="consignTime" jdbcType="TIMESTAMP"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="total_fee" property="totalFee" jdbcType="DOUBLE"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="seller_flag" property="sellerFlag" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="step_trade_status" property="stepTradeStatus" jdbcType="VARCHAR"/>
        <result column="step_paid_fee" property="stepPaidFee" jdbcType="DOUBLE"/>
        <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" property="payType" jdbcType="TINYINT"/>
        <result column="seller_discount" property="sellerDiscount" jdbcType="DOUBLE"/>
        <result column="flag" property="flag" jdbcType="BIGINT"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="direct_trade_id" jdbcType="BIGINT" property="directTradeId"/>
    </resultMap>
    <resultMap id="DirectTradeOrderDTO" type="com.pes.jd.model.DTO.OrderDTO">
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="direct_trade_id" jdbcType="BIGINT" property="directTradeId"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
    </resultMap>

    <resultMap id="BuyerOrderDTO" type="com.pes.jd.model.DTO.BuyerOrderDTO">
        <id column="trade_id" property="tradeId" jdbcType="BIGINT"/>
        <id column="order_id" property="orderId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="seller_nick" property="sellerNick" jdbcType="VARCHAR"/>
        <result column="date" property="date" jdbcType="DATE"/>
        <result column="payment" property="payment" jdbcType="DOUBLE"/>
        <result column="post_fee" property="postFee" jdbcType="DOUBLE"/>
        <result column="consign_time" property="consignTime" jdbcType="TIMESTAMP"/>
        <result column="num" property="num" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="total_fee" property="totalFee" jdbcType="DOUBLE"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR"/>
        <result column="seller_flag" property="sellerFlag" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="step_trade_status" property="stepTradeStatus" jdbcType="VARCHAR"/>
        <result column="step_paid_fee" property="stepPaidFee" jdbcType="DOUBLE"/>
        <result column="orders_num" property="ordersNum" jdbcType="INTEGER"/>
        <result column="pay_type" property="payType" jdbcType="INTEGER"/>
        <result column="order_type" property="orderType" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="base_field">
        trade_id, shop_id, payment, num, status,
        created,pay_time,buyer_nick, type
    </sql>
    <sql id="all_field">
        order_id,trade_id,seller_nick,date,shop_id,payment,post_fee,consign_time,num,status,total_fee,created,pay_time,modified,
        end_time,buyer_nick,seller_flag,type,step_trade_status,step_paid_fee,out_stock_time,pay_type,seller_discount,order_type,
        direct_trade_id
    </sql>

    <insert id="insertOrderCancel" parameterType="map">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="order.orderId != null">
                order_id,
            </if>
            <if test="order.tradeId != null">
                trade_id,
            </if>
            <if test="order.sellerNick != null">
                seller_nick,
            </if>
            <if test="order.date != null">
                date,
            </if>
            <if test="order.shopId != null">
                shop_id,
            </if>
            <if test="order.payment != null">
                payment,
            </if>
            <if test="order.postFee != null">
                post_fee,
            </if>
            <if test="order.consignTime != null">
                consign_time,
            </if>
            <if test="order.num != null">
                num,
            </if>
            <if test="order.status != null">
                status,
            </if>
            <if test="order.totalFee != null">
                total_fee,
            </if>
            <if test="order.created != null">
                created,
            </if>
            <if test="order.payTime != null">
                pay_time,
            </if>
            <if test="order.modified != null">
                modified,
            </if>
            <if test="order.endTime != null">
                end_time,
            </if>
            <if test="order.buyerNick != null">
                buyer_nick,
            </if>
            <if test="order.sellerFlag != null">
                seller_flag,
            </if>
            <if test="order.type != null">
                type,
            </if>
            <if test="order.stepTradeStatus != null">
                step_trade_status,
            </if>
            <if test="order.stepPaidFee != null">
                step_paid_fee,
            </if>
            <if test="order.outStockTime != null">
                out_stock_time,
            </if>
            <if test="order.payType != null">
                pay_type,
            </if>
            <if test="order.sellerDiscount != null">
                seller_discount,
            </if>
            <if test="order.orderType != null">
                order_type,
            </if>
            <if test="order.flag!=null">
                falg,
            </if>
            <if test="order.memo!=null">
                memo,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="order.orderId != null">
                #{order.orderId,jdbcType=BIGINT},
            </if>
            <if test="order.tradeId != null">
                #{order.tradeId,jdbcType=BIGINT},
            </if>
            <if test="order.sellerNick != null">
                #{order.sellerNick,jdbcType=VARCHAR},
            </if>
            <if test="order.date != null">
                #{order.date,jdbcType=DATE},
            </if>
            <if test="order.shopId != null">
                #{order.shopId,jdbcType=BIGINT},
            </if>
            <if test="order.payment != null">
                #{order.payment,jdbcType=DOUBLE},
            </if>
            <if test="order.postFee != null">
                #{order.postFee,jdbcType=DOUBLE},
            </if>
            <if test="order.consignTime != null">
                #{order.consignTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.num != null">
                #{order.num,jdbcType=INTEGER},
            </if>
            <if test="order.status != null">
                #{order.status,jdbcType=VARCHAR},
            </if>
            <if test="order.totalFee != null">
                #{order.totalFee,jdbcType=DOUBLE},
            </if>
            <if test="order.created != null">
                #{order.created,jdbcType=TIMESTAMP},
            </if>
            <if test="order.payTime != null">
                #{order.payTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.modified != null">
                #{order.modified,jdbcType=TIMESTAMP},
            </if>
            <if test="order.endTime != null">
                #{order.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.buyerNick != null">
                #{order.buyerNick,jdbcType=VARCHAR},
            </if>
            <if test="order.sellerFlag != null">
                #{order.sellerFlag,jdbcType=BIGINT},
            </if>
            <if test="order.type != null">
                #{order.type,jdbcType=VARCHAR},
            </if>
            <if test="order.stepTradeStatus != null">
                #{order.stepTradeStatus,jdbcType=VARCHAR},
            </if>
            <if test="order.stepPaidFee != null">
                #{order.stepPaidFee,jdbcType=DOUBLE},
            </if>
            <if test="order.outStockTime != null">
                #{order.outStockTime,jdbcType=TIMESTAMP},
            </if>
            <if test="order.payType != null">
                #{order.payType,jdbcType=TINYINT},
            </if>
            <if test="order.sellerDiscount != null">
                #{order.sellerDiscount,jdbcType=DOUBLE},
            </if>
            <if test="order.orderType != null">
                #{order.orderType,jdbcType=TINYINT},
            </if>
            <if test="order.flag!=null">
                #{order.flag,jdbcType=BIGINT},
            </if>
            <if test="order.memo!=null">
                #{order.memo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <delete id="deleteOrderInvoiceInfo" parameterType="map">
        DELETE  FROM  ${tableName}
        where shopId=#{shopId}
        and orderId=#{oid}
    </delete>

    <insert id="insertOrderInvoiceInfo" parameterType="map">
        insert into ${tableName}
        (shopId,orderId,invoiceCode,invoiceType,invoiceTitle,invoiceContentId,invoiceConsigneeEmail,invoiceConsigneePhone)
        values
        (#{invoiceInfoDTO.shopId},
        #{invoiceInfoDTO.orderId},
        #{invoiceInfoDTO.invoiceCode},
        #{invoiceInfoDTO.invoiceType},
        #{invoiceInfoDTO.invoiceTitle},
        #{invoiceInfoDTO.invoiceContentId},
        #{invoiceInfoDTO.invoiceConsigneeEmail},
        #{invoiceInfoDTO.invoiceConsigneePhone})
    </insert>

    <insert id="persistOrderByFile" parameterType="java.util.Map">
        load data local
        infile #{filePath} into table ${tableName}
        fields terminated by
        '``MYPES`' optionally enclosed by '' escaped by ''
        lines terminated by '`MYPES`\n'
		(order_id, trade_id, direct_trade_id, seller_nick, date, shop_id, payment, post_fee, num, status,
        total_fee, created, pay_time, modified, end_time, type, buyer_nick, out_stock_time, order_type, pay_type,
        seller_discount);
    </insert>

    <delete id="deleteOrderCancelByOrderId" parameterType="map">
        DELETE FROM ${tableName}
        WHERE
        order_id = #{oid,jdbcType=BIGINT}
    </delete>

    <update id="updateOrderStatusByOrderIdAndDate" parameterType="map">
        update ${tableName} set status = #{status}, payment = 0, pay_time = '0000-00-00 00:00:00'
        WHERE order_id IN
        <foreach collection="tids" index="index" item="tid"
                 open="(" separator="," close=")">
            #{tid}
        </foreach>
    </update>

    <delete id="deleteOrderByOrderId" parameterType="map">
        DELETE FROM ${tableName}
        where
		order_id = #{oid,jdbcType=BIGINT}
    </delete>

    <delete id="deleteAddressByOrderIdAndShopId" parameterType="map">
        DELETE FROM ${tableName}
        where orderId=#{oid,jdbcType=BIGINT}
        and shopId=#{shopId,jdbcType=BIGINT}
    </delete>

    <delete id="deleteOrderCustomerInfo" parameterType="map">
        DELETE FROM ${tableName}
        where orderId=#{oid,jdbcType=BIGINT}
        and shopId=#{shopId,jdbcType=BIGINT}
    </delete>

    <insert id="insertOrderCustomerInfo" parameterType="map">
        insert INTO ${tableName}
        (shopId,orderId,buyer_nick)
        values
        (#{customerInfoDTO.shopId,jdbcType=BIGINT},
         #{customerInfoDTO.orderId,jdbcType=BIGINT},
         #{customerInfoDTO.buyer_nick,jdbcType=BIGINT})
    </insert>

    <delete id="deleteJcqMessageByOrderIdAndShopId" parameterType="map">
        DELETE FROM ${tableName}
        where order_id=#{oid,jdbcType=BIGINT}
        and shop_id=#{shopId,jdbcType=BIGINT}
    </delete>

    <insert id="insertJcqMessage" parameterType="map">
        insert  into ${tableName}
        (order_id,shop_id,message,creat,status)
        values
        (#{jcqMessage.orderId,jdbcType=BIGINT},
        #{jcqMessage.shopId,jdbcType=BIGINT},
        #{jcqMessage.create,jdbcType=BIGINT},
        #{jcqMessage.status,jdbcType=BIGINT})
    </insert>

    <insert id="insertAddress" parameterType="map">
        insert into ${tableName}
        (shopId,orderId,address)
        values
        (#{address.shopId,jdbcType=BIGINT},
        #{address.orderId,jdbcType=BIGINT},
        #{address.address,jdbcType=VARCHAR})
    </insert>

    <delete id="deleteOrdersByTids" parameterType="java.util.Map">
        DELETE FROM ${tableName}
        WHERE order_id IN
        <foreach collection="tids" index="index" item="tid" open="("
                 separator="," close=")">
            #{tid}
        </foreach>
    </delete>

    <select id="selectIdsByTradeIds" parameterType="map"
            resultType="java.lang.Long">
        SELECT id FROM ${tableName}
        WHERE order_id IN
        <foreach collection="tids" index="index" item="tid"
                 open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>

    <select id="selectShopCreatedOrderLstByBuyersAndDateForFirstConsult" resultMap="BuyerOrderDTO">
        SELECT
        order_id,buyer_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectShopCreatedOrderLstByBuyersAndDate" resultMap="BuyerOrderDTO">
        SELECT
        order_id, shop_id, payment, num, status,
        created, pay_time, buyer_nick, type, post_fee, pay_type, order_type
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
        <if test="filterOrderIds!=null and filterOrderIds.size()>0">
            AND order_id NOT IN
            <foreach collection="filterOrderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
        AND order_type IN(0,2)
    </select>

    <select id="selectShopOrderLstByBuyersAndDateForAfterSale" resultMap="BuyerOrderDTO">
        SELECT
        pay_time, buyer_nick, pay_type, order_type, order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectShopPaidOrderLstByBuyersAndDate" resultMap="BuyerOrderDTO">
        SELECT
        order_id, shop_id, payment, num, status,
        created, pay_time, buyer_nick, type, post_fee, pay_type, order_type
        FROM ${tableName}
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND  pay_time BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
        AND buyer_nick IN
        <foreach collection="buyerLst" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
        <if test="filterOrderIds!=null and filterOrderIds.size()>0">
            AND order_id NOT IN
            <foreach collection="filterOrderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
    </select>

    <!-- 	TODO(check) -->
    <select id="selectShopCsConfirmGoodsOrderIdLst" resultType="long">
		SELECT order_id
		FROM ${tableName}
		WHERE 
			shop_id = #{shopId}
		AND end_time between #{startDate} and #{endDate}
	</select>

    <select id="selectShopOutStockOrderIdLstByOrderIdLst" resultType="long">
        SELECT order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
            #{orderId,jdbcType=BIGINT}
        </foreach>
        AND out_stock_time != "0000-00-00 00:00:00" and out_stock_time &lt;=#{endOutValid}
    </select>

    <select id="selectPresaleBalancePayOrderLstByOrderLst" parameterType="map"
            resultType="com.pes.jd.model.DTO.BuyerOrderDTO">
        SELECT
        order_id as orderId,
        payment as payment,
        created as created,
        pay_time as balancePayTime
        FROM ${tableName}
        WHERE
        order_id IN
        <foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND pay_time != "0000-00-00 00:00:00"
    </select>

    <select id="selectShopCsOrderByCsByDate" parameterType="map" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT order_id orderId,seller_nick sellerNick,date date
        FROM ${tableName}
        WHERE date = #{date}
        AND shop_id = #{shopId}
        AND seller_nick IN
        <foreach collection="csLst" item="item" open="(" close=")" separator=",">
            #{item.nick}
        </foreach>
    </select>

    <!-- 查询静默下单，静默流失的订单（下单后无客服介入） pesCsOrderIndexs-->
    <select id="selectShopSilentCreatedOrderNoChatLossByDate" parameterType="map"
            resultType="com.pes.jd.model.DTO.SilentOrderLossDTO">
        SELECT DISTINCT o.order_id orderId,o.date,o.shop_id shopId,o.buyer_nick buyerNick,o.created,o.num
        orderGoodsNum,o.total_fee payment,o.order_type orderType
        FROM ${pesOrder} o
        WHERE NOT EXISTS
        (
        <foreach collection="pesCsOrderIndexs" item="csOrderIndex" separator="UNION ALL">
            SELECT 1 FROM ${csOrderIndex.tableName} coi WHERE o.order_id = coi.order_id
        </foreach>
        )
        AND o.shop_id = #{shopId}
        AND o.pay_time = "0000-00-00 00:00:00"
        AND o.date = #{date}
        AND o.pay_type != 1
    </select>

    <!-- 查询所有当天下单并且当天付过款的客服 -->
    <select id="selectBuyerNickWasTodayPlacedAndPaid" parameterType="map" resultType="java.lang.String">
        SELECT DISTINCT o.buyer_nick buyerNick
        FROM ${pesOrder} o
        WHERE o.shop_id = #{shopId}
        AND o.date = #{date}
        AND o.pay_type != 1
        AND o.pay_time != "0000-00-00 00:00:00"
        AND o.buyer_nick IN
        <foreach collection="buyerNickSet" item="buyerNick" open="(" separator="," close=")">
            #{buyerNick}
        </foreach>
    </select>

    <select id="selectShopPayOrderListByOrderId" parameterType="map" resultType="java.lang.Long">
        SELECT order_id
        FROM ${tableName}
        WHERE
        order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND shop_id = #{shopId}
        AND pay_time != "0000-00-00 00:00:00"
    </select>

    <select id="selectShopOutStockOrderLstByOrderIdLst" parameterType="map" resultType="java.lang.Long">
        SELECT order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND out_stock_time != "0000-00-00 00:00:00" AND out_stock_time &lt;=#{endOutValidDate}
    </select>

    <update id="updateOrderByOrderId"
            parameterType="map">
        UPDATE ${tableName}
        <set>
            out_stock_time = #{outStockTime,jdbcType=TIMESTAMP},
            status = #{status,jdbcType=VARCHAR}
        </set>
        WHERE
        shop_id = #{shopId,jdbcType=BIGINT}
        AND
        order_id = #{orderId,jdbcType=BIGINT}
    </update>

    <update id="updateBatchOrderByOrderId"
            parameterType="map">
        <foreach collection="orderList" item="item" index="index" open="" close="" separator=";">
            UPDATE ${tableName}
            <set>
                <if test="item.outStockTime != null">
                    out_stock_time = #{item.outStockTime,jdbcType=TIMESTAMP}
                </if>
            </set>
            where shop_id = #{item.shopId,jdbcType=BIGINT}
            AND order_id = #{item.orderId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectShopCsOutStockLossOrderByOrderId" parameterType="map" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT order_id orderId,payment,num,created,buyer_nick buyerNick,seller_nick sellerNick,total_fee
        totalFee,out_stock_time outStockTime
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderIdList" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND (out_stock_time = "0000-00-00 00:00:00" OR out_stock_time &gt;#{endOutValidDate})
    </select>

    <select id="selectShopOrderRefundByOrderId" parameterType="map" resultType="com.pes.jd.model.DTO.OrderDTO">
        SELECT order_id, trade_id, seller_nick, date, shop_id, payment, post_fee, num, status,
        total_fee, created, pay_time, modified, end_time, type, buyer_nick, out_stock_time, pay_type
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderRefundIdSet" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="selectShopOrderByShopIdByPayTime" resultMap="OrderDTO">
		SELECT
									shop_id ,
									order_id ,
									out_stock_time,
									direct_trade_id,
		                            status,
		                            payment,
		                            seller_discount,
		                            total_fee
							FROM ${tableName}
							WHERE
							 	shop_id = #{shopId}
								AND pay_time BETWEEN #{startDate} AND #{endDate}
								AND pay_type !=1
	</select>

    <select id="selectShopOrderByShopIdByCreated" parameterType="map" resultType="com.pes.jd.model.DTO.OrderDTO">
	SELECT
								shop_id,
								order_id ,
								payment,
								num,
								created,
								buyer_nick ,
								seller_nick ,
								out_stock_time
						FROM ${tableName}
						WHERE
						 	shop_id = #{shopId}
                            AND created BETWEEN #{startDate} AND #{endDate}
						 	AND order_type IN(0,2)
							AND pay_type = #{type}

	</select>

    <select id="selectTotalFeeByOrderId" parameterType="map" resultType="java.lang.Double">

        select total_fee from (
        <foreach collection="tableNames" item="table" separator="UNION ALL">
            SELECT
            shop_id,
            total_fee
            FROM ${table.tableName}
            WHERE
            shop_id = #{shopId}
            AND order_id =#{orderId}
        </foreach>
        ) od
    </select>

    <select id="selectShopCashOrderByShopIdByOrderIdByCreated" parameterType="map" resultType="java.lang.Long">
        SELECT
        order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        AND created BETWEEN #{startDate} AND #{endDate}
        AND order_type IN(0,2)
        AND pay_type = #{type}

    </select>

    <select id="selectCanceledOrderByOrderLst" parameterType="map" resultMap="OrderDTO">
        SELECT
        <include refid="all_field"/>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="selectPreordainOrderByCreatedAndBuyers" resultMap="OrderDTO">
        SELECT
        <include refid="all_field"/>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        And `created` between #{beginDate} and #{endDate}
        <if test="buyers != null and buyers.size() != 0">
            AND buyer_nick IN
            <foreach collection="buyers" item="buyer" open="(" close=")" separator=",">
                #{buyer}
            </foreach>
        </if>
        AND order_type = 2
    </select>

    <select id="selectBuyerNickAndOrderIdByOrderIds" resultType="com.pes.jd.model.DO.CsNickAndOrderIdDo">
        SELECT
        order_id AS orderId, buyer_nick AS buyerNick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND`created` BETWEEN #{beginDate} AND #{endDate}
        <if test="orderIds != null and orderIds.size() != 0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
    </select>

    <select id="selectOrderDTOByShopIdAndOrderId" resultMap="OrderDTO">
        SELECT
        <include refid="all_field"/>
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        <if test="orderIds!=null and orderIds.size()!=0">
            AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </if>
    </select>

    <select id="selectParentOrderToPayCsSaleOrderLst" resultMap="OrderDTO">
        SELECT
            order_id,direct_trade_id
        FROM ${tableName}
        WHERE
    	    shop_id = #{shopId,jdbcType=BIGINT}
            AND direct_trade_id > 0
            AND created BETWEEN #{startDate} AND #{endDate}
        union
        SELECT
            order_id,direct_trade_id
        FROM ${tableName}
        WHERE
    	    shop_id = #{shopId,jdbcType=BIGINT}
            AND direct_trade_id > 0
            AND pay_time BETWEEN #{startDate} AND #{endDate}
    </select>

     <select id="selectParentOrderToPayCsSaleOrderLstNew" resultMap="OrderDTO">
        SELECT
            order_id,direct_trade_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          AND direct_trade_id > 0
          AND created BETWEEN #{startDate} AND #{endDate}
        union
        SELECT
            order_id,direct_trade_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          AND direct_trade_id > 0
          AND pay_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectParentOrderByShopIdAndDateAndBuyerNick" resultMap="OrderDTO">
        SELECT
            order_id, trade_id, direct_trade_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          AND direct_trade_id > 0
          AND order_type = 1
          AND pay_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectParentOrderByShopIdAndDateAndBuyerNickTwo" resultMap="OrderDTO">
        SELECT
            order_id, trade_id, direct_trade_id
        FROM ${tableName}
        WHERE
            shop_id = #{shopId,jdbcType=BIGINT}
          AND direct_trade_id > 0
          AND order_type = 1
          AND created BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="selectShopOrderByShopIdByCreatedNew" resultMap="OrderDTO">
        SELECT
									shop_id ,
									order_id ,
									out_stock_time,
									created,
									pay_time
							FROM ${tableName}
							WHERE
							 	shop_id = #{shopId}

								AND created BETWEEN #{beginDate} AND #{endDate}
								AND pay_time != '0000-00-00 00:00:00'
								AND pay_type !=1
    </select>

    <select id="selectShopPresaleOrderByShopIdByDate" resultMap="OrderDTO">

        select
            shop_id ,
            order_id ,
            out_stock_time
        from ${tableName}
        WHERE
            shop_id = #{shopId}
            AND pay_time between #{startDate} and #{endDate}
            AND order_type = 1

            AND pay_type != 1
    </select>

    <select id="selectOrderByShopIdAndOrderIds" resultMap="OrderDTO">
        <foreach collection="tableNames" item="tableName" open="" close="" separator="UNION">
            SELECT order_id,shop_id,payment,pay_time,pay_type
            FROM ${tableName}
            WHERE shop_id = #{shopId} AND order_id IN
            <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
                #{orderId}
            </foreach>
        </foreach>
    </select>

    <select id="selectParentOrderToPayCsSaleOrderLstByShopId" resultMap="OrderDTO">
         SELECT
            order_id,direct_trade_id
        FROM ${tableName}
        WHERE
    	    shop_id = #{shopId,jdbcType=BIGINT}
            AND direct_trade_id > 0
            AND created BETWEEN #{startDate} AND #{endDate}
            and order_type !=1
        union
        SELECT
            order_id,direct_trade_id
        FROM ${tableName}
        WHERE
    	    shop_id = #{shopId,jdbcType=BIGINT}
            AND direct_trade_id > 0
            AND pay_time BETWEEN #{startDate} AND #{endDate}
            and order_type !=1
    </select>

    <select id="selectOrderDirectTradeIdByOrderId" parameterType="map" resultMap="DirectTradeOrderDTO">
        SELECT
        order_id,trade_id,`date`,shop_id,direct_trade_id,payment,num
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND direct_trade_id IN
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>

    <select id="selectOrderDirectTradeId" parameterType="map" resultType="long">
        SELECT
        distinct direct_trade_id
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND order_type=#{orderType}
        AND `date` BETWEEN #{startDate} AND #{endDate}
        AND direct_trade_id>0
    </select>

    <select id="selectShopOrderByShopIdByCreatedByOrderIds" resultMap="OrderDTO">
        SELECT
        shop_id,
        order_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_type IN(0,2)
        AND created BETWEEN #{startDate} AND #{endDate}
        AND pay_type = #{type}
        AND order_id IN
        <foreach collection="orderIdSet" index="index" item="tid"
                 open="(" separator="," close=")">
            #{tid}
        </foreach>
    </select>

    <select id="selectTradeCanceledOrderByOrderIdLst" resultMap="OrderDTO">
        SELECT
        shop_id,
        order_id,
        `date`
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND order_id IN
        <foreach collection="orderIdLst" index="index" item="tid"
                 open="(" separator="," close=")">
            #{tid}
        </foreach>
        AND status in ('TRADE_CANCELED','NO_PAY')
    </select>

    <select id="selectOrdersByDateScopeAndBuyerNicks" resultMap="OrderDTO">
        SELECT
        buyer_nick
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND pay_time BETWEEN #{startDate} AND #{endDate}
        AND buyer_nick IN
        <foreach collection="buyers" item="buyerNick" open="(" close=")" separator=",">
            #{buyerNick,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectParentOrderByOrderIds" resultType="java.lang.Long">
        SELECT
        trade_id
        FROM ${tableName}
        WHERE
        shop_id = #{shopId}
        AND
        trade_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
    </select>
</mapper>
