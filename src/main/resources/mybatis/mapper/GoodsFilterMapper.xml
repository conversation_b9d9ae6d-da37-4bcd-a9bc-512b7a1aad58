<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.GoodsFilterMapper">

	<resultMap id="GoodsFilterDTO" type="com.pes.jd.model.DTO.GoodsFilterDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="num_iid" jdbcType="BIGINT" property="numIid" />
		<result column="goods_url" jdbcType="VARCHAR" property="goodsUrl" />
		<result column="created" jdbcType="TIMESTAMP" property="created" />
		<result column="name" jdbcType="VARCHAR" property="name" />
		<result column="type" jdbcType="VARCHAR" property="type" />
		<result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
	</resultMap>

	<sql id="base_field">
		id, shop_id, num_iid, goods_url, created, name, type
	</sql>

	<insert id="insertGoodsFilter"
		parameterType="com.pes.jd.model.DO.GoodsFilter">
		INSERT INTO pes_goods_filter (shop_id, num_iid, goods_url, created, name,
		type)
		VALUES
		(
		#{shopId,jdbcType=BIGINT}, #{numIid,jdbcType=BIGINT},
		#{goodsUrl,jdbcType=VARCHAR}, #{created,jdbcType=TIMESTAMP}, #{name,jdbcType=VARCHAR},
		#{type,jdbcType=VARCHAR}
		)
	</insert>

	<delete id="deleteGoodsFilterById"
		parameterType="java.lang.Long">
		DELETE FROM pes_goods_filter
		WHERE
		id = #{id,jdbcType=BIGINT}
	</delete>

	<update id="updateGoodsFilterBySelective"
		parameterType="com.pes.jd.model.DO.GoodsFilter">
		UPDATE pes_goods_filter
		<set>
			<if test="shopId != null">
				shop_id = #{shopId,jdbcType=BIGINT},
			</if>
			<if test="numIid != null">
				num_iid = #{numIid,jdbcType=BIGINT},
			</if>
			<if test="goodsUrl != null">
				goods_url = #{goodsUrl,jdbcType=VARCHAR},
			</if>
			<if test="created != null">
				created = #{created,jdbcType=TIMESTAMP},
			</if>
			<if test="name != null">
				name = #{name,jdbcType=VARCHAR},
			</if>
			<if test="type != null">
				type = #{type,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE id = #{id,jdbcType=BIGINT}
	</update>

	<select id="getGoodsFilterById" parameterType="java.lang.Long"
		resultMap="GoodsFilterDTO">
		SELECT
		<include refid="base_field" />
		FROM pes_goods_filter
		WHERE
		id = #{id,jdbcType=BIGINT}
	</select>

	<!-- 根据店铺id查询商品过滤信息 -->
	<select id="queryPesGoodsFilterByShopId" parameterType="java.lang.Long" resultMap="GoodsFilterDTO">
		SELECT id, shop_id, num_iid, goods_url ,
		type, created, name, end_time
		FROM
		pes_goods_filter 
		WHERE
		shop_id = #{shopId}
		ORDER BY
		created desc
	</select>
</mapper>