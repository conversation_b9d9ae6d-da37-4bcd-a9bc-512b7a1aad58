<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopOrderEvaluateMapper" >

  <resultMap id="ShopOrderEvaluateDTO" type="com.pes.jd.model.DTO.ShopOrderEvaluateDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="good_evaluate_num" property="goodEvaluateNum" jdbcType="INTEGER" />
    <result column="neutral_evaluate_num" property="neutralEvaluateNum" jdbcType="INTEGER" />
    <result column="bad_evaluate_num" property="badEvaluateNum" jdbcType="INTEGER" />
  </resultMap>
  
  <insert id="insertShopOrderEvaluate" parameterType="com.pes.jd.model.DTO.ShopOrderEvaluateDTO">
  	INSERT INTO ${tableName} (shop_id,date,good_evaluate_num,neutral_evaluate_num,bad_evaluate_num)
  	VALUES (#{shopOrderEvaluate.shopId},#{shopOrderEvaluate.date},#{shopOrderEvaluate.goodEvaluateNum},#{shopOrderEvaluate.neutralEvaluateNum},#{shopOrderEvaluate.badEvaluateNum})
  </insert>
  
  <delete id="deleteShopOrderEvaluateByDateByShopId" parameterType="map">
	DELETE FROM ${tableName} 
	WHERE shop_id = #{shopId} 
	AND date = #{date}
  </delete>
  
  
</mapper>