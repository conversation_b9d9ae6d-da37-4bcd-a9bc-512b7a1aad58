<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.SentimentAnalysisMapper" >

  <insert id="batchInsertSentimentAnalysis" parameterType="map" >
    INSERT INTO ${tableName} (shop_id, shop_title, cs_nick, customer, sid,
      warning_time, warning_type, keyword, content,
      status, sentiment_type, date)
    VALUES 
    <foreach collection="sentimrntAnalysisList" item="sentimrntAnalysis" separator=",">
	    (#{sentimrntAnalysis.shopId,jdbcType=BIGINT}, #{sentimrntAnalysis.shopTitle,jdbcType=BIGINT},
        #{sentimrntAnalysis.csNick,jdbcType=VARCHAR}, #{sentimrntAnalysis.customer,jdbcType=VARCHAR},
        #{sentimrntAnalysis.sid,jdbcType=VARCHAR},#{sentimrntAnalysis.warningTime,jdbcType=TIMESTAMP},
        #{sentimrntAnalysis.warningType,jdbcType=TINYINT},#{sentimrntAnalysis.keyword,jdbcType=VARCHAR},
        #{sentimrntAnalysis.content,jdbcType=VARCHAR}, #{sentimrntAnalysis.status,jdbcType=TINYINT},
        #{sentimrntAnalysis.sentimentType,jdbcType=TINYINT}, #{sentimrntAnalysis.date,jdbcType=DATE})
    </foreach>
  </insert>

  <delete id="batchDeleteSentimentAnalysisByDate" parameterType="map" >
    DELETE FROM ${tableName} 
    WHERE date = #{date}
    AND shop_id = #{shopId,jdbcType=BIGINT}
  </delete>
  
</mapper>