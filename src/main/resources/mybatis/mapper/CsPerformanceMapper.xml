<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsPerformanceMapper" >
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.CsPerformanceDO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="date" jdbcType="DATE" property="date" />
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
        <result column="direct_receive_num" jdbcType="INTEGER" property="directReceiveNum" />
        <result column="forward_in_num" jdbcType="INTEGER" property="forwardInNum" />
        <result column="forward_out_num" jdbcType="INTEGER" property="forwardOutNum" />
        <result column="consult_num" jdbcType="INTEGER" property="consultNum" />
        <result column="receive_num" jdbcType="INTEGER" property="receiveNum" />
        <result column="enquiry_num" jdbcType="INTEGER" property="enquiryNum" />
        <result column="ordered_num_today" jdbcType="INTEGER" property="orderedNumToday" />
        <result column="paid_num_today" jdbcType="INTEGER" property="paidNumToday" />
        <result column="ordered_amount_today" jdbcType="DOUBLE" property="orderedAmountToday" />
        <result column="paid_amount_today" jdbcType="DOUBLE" property="paidAmountToday" />
        <result column="ordered_num_final" jdbcType="INTEGER" property="orderedNumFinal" />
        <result column="ordered_amount_final" jdbcType="DOUBLE" property="orderedAmountFinal" />
        <result column="ordered_goods_num_final" jdbcType="INTEGER" property="orderedGoodsNumFinal" />
        <result column="paid_num_today_next" jdbcType="INTEGER" property="paidNumTodayNext" />
        <result column="paid_num_final" jdbcType="INTEGER" property="paidNumFinal" />
        <result column="paid_amount_final" jdbcType="DOUBLE" property="paidAmountFinal" />
        <result column="paid_goods_num_final" jdbcType="INTEGER" property="paidGoodsNumFinal" />
        <result column="receive_rate" jdbcType="DOUBLE" property="receiveRate" />
        <result column="conversion_rate" jdbcType="DOUBLE" property="conversionRate" />
        <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
        <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
        <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
        <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
        <result column="cfm_goods_o_num" jdbcType="INTEGER" property="cfmGoodsONum" />
        <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
        <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
        <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
        <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
        <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
        <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
        <result column="out_stock_loss_num" jdbcType="INTEGER" property="outStockLossNum" />
        <result column="out_stock_loss_goods_num" jdbcType="INTEGER" property="outStockLossGoodsNum" />
        <result column="out_stock_loss_order_num" jdbcType="INTEGER" property="outStockLossOrderNum" />
        <result column="out_stock_loss_amount" jdbcType="INTEGER" property="outStockLossAmount" />
    </resultMap>
    <resultMap id="CsPerformanceDTO" type="com.pes.jd.model.DTO.CsPerformanceDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="shop_id" jdbcType="BIGINT" property="shopId" />
        <result column="date" jdbcType="DATE" property="date" />
        <result column="cs_nick" jdbcType="VARCHAR" property="csNick" />
        <result column="direct_receive_num" jdbcType="INTEGER" property="directReceiveNum" />
        <result column="forward_in_num" jdbcType="INTEGER" property="forwardInNum" />
        <result column="forward_out_num" jdbcType="INTEGER" property="forwardOutNum" />
        <result column="consult_num" jdbcType="INTEGER" property="consultNum" />
        <result column="receive_num" jdbcType="INTEGER" property="receiveNum" />
        <result column="enquiry_num" jdbcType="INTEGER" property="enquiryNum" />
        <result column="ordered_num_today" jdbcType="INTEGER" property="orderedNumToday" />
        <result column="paid_num_today" jdbcType="INTEGER" property="paidNumToday" />
        <result column="ordered_amount_today" jdbcType="DOUBLE" property="orderedAmountToday" />
        <result column="paid_amount_today" jdbcType="DOUBLE" property="paidAmountToday" />
        <result column="ordered_num_final" jdbcType="INTEGER" property="orderedNumFinal" />
        <result column="ordered_amount_final" jdbcType="DOUBLE" property="orderedAmountFinal" />
        <result column="ordered_goods_num_final" jdbcType="INTEGER" property="orderedGoodsNumFinal" />
        <result column="paid_num_today_next" jdbcType="INTEGER" property="paidNumTodayNext" />
        <result column="paid_num_final" jdbcType="INTEGER" property="paidNumFinal" />
        <result column="paid_amount_final" jdbcType="DOUBLE" property="paidAmountFinal" />
        <result column="paid_goods_num_final" jdbcType="INTEGER" property="paidGoodsNumFinal" />
        <result column="receive_rate" jdbcType="DOUBLE" property="receiveRate" />
        <result column="conversion_rate" jdbcType="DOUBLE" property="conversionRate" />
        <result column="sale_amount" jdbcType="DOUBLE" property="saleAmount" />
        <result column="sale_order_num" jdbcType="INTEGER" property="saleOrderNum" />
        <result column="sale_goods_num" jdbcType="INTEGER" property="saleGoodsNum" />
        <result column="sale_buyer_num" jdbcType="INTEGER" property="saleBuyerNum" />
        <result column="cfm_goods_o_num" jdbcType="INTEGER" property="cfmGoodsONum" />
        <result column="cfm_goods_amount" jdbcType="DOUBLE" property="cfmGoodsAmount" />
        <result column="post_fee" jdbcType="DOUBLE" property="postFee" />
        <result column="out_stock_num" jdbcType="INTEGER" property="outStockNum" />
        <result column="out_stock_amount" jdbcType="DOUBLE" property="outStockAmount" />
        <result column="out_stock_goods_num" jdbcType="INTEGER" property="outStockGoodsNum" />
        <result column="out_stock_order_num" jdbcType="INTEGER" property="outStockOrderNum" />
        <result column="out_stock_loss_num" jdbcType="INTEGER" property="outStockLossNum" />
        <result column="out_stock_loss_goods_num" jdbcType="INTEGER" property="outStockLossGoodsNum" />
        <result column="out_stock_loss_order_num" jdbcType="INTEGER" property="outStockLossOrderNum" />
        <result column="out_stock_loss_amount" jdbcType="INTEGER" property="outStockLossAmount" />
    </resultMap>
  
  <sql id="base_field" >
  	shop_id, date, cs_nick, direct_receive_num, forward_in_num, forward_out_num, 
    consult_num, receive_num, enquiry_num, 
    ordered_num_today, ordered_amount_today, ordered_num_final, ordered_amount_final, 
    ordered_goods_num_final, paid_num_today, paid_amount_today, paid_num_final, paid_amount_final, 
    paid_goods_num_final, receive_rate, conversion_rate, sale_amount, sale_order_num, 
    sale_goods_num, sale_buyer_num, cfm_goods_o_num, cfm_goods_amount, post_fee
  </sql>

  <insert id="batchInsertCsPerformance" parameterType="map">
	    INSERT INTO ${tableName} 
	    (
	    	shop_id, 
	    	date,
	    	cs_nick, 
	    	direct_receive_num, 
	    	forward_in_num, 
	      	forward_out_num, 
	      	consult_num, 
	      	receive_num, 
	      	enquiry_num, 
	      	ordered_num_today, 
	      	ordered_goods_num_today, 
	      	ordered_amount_today, 
	      	ordered_num_final, 
	      	ordered_goods_num_final, 
	      	ordered_amount_final, 
	      	paid_num_today, 
	      	paid_amount_today, 
	      	paid_goods_num_today, 
	      	paid_num_today_next, 
	      	paid_num_final, 
	      	paid_goods_num_final, 
	      	paid_amount_final, 
	      	receive_rate, 
	      	conversion_rate, 
	      	out_stock_order_buyer_num_final, 
	      	out_stock_order_num_final, 
	      	out_stock_order_goods_num_final, 
	      	out_stock_order_amount_final
	     )
	    VALUES 
	    <foreach collection="csDayPerformanceLst" item="itm" separator=","> 
	     (
	      #{itm.shopId,jdbcType=BIGINT}, 
	      #{itm.date,jdbcType=DATE}, 
	      #{itm.csNick,jdbcType=VARCHAR}, 
	      #{itm.directReceiveNum,jdbcType=INTEGER}, 
	      #{itm.forwardInNum,jdbcType=INTEGER}, 
	      #{itm.forwardOutNum,jdbcType=INTEGER}, 
	      #{itm.consultNum,jdbcType=INTEGER}, 
	      #{itm.receiveNum,jdbcType=INTEGER}, 
	      #{itm.enquiryNum,jdbcType=INTEGER}, 
	      #{itm.orderedNumToday,jdbcType=INTEGER}, 
	      #{itm.orderedGoodsNumToday,jdbcType=INTEGER}, 
	      #{itm.orderedAmountToday,jdbcType=DOUBLE}, 
	      #{itm.orderedNumFinal,jdbcType=INTEGER}, 
	      #{itm.orderedGoodsNumFinal,jdbcType=INTEGER}, 
	      #{itm.orderedAmountFinal,jdbcType=DOUBLE}, 
	      #{itm.paidNumToday,jdbcType=INTEGER}, 
	      #{itm.paidAmountToday,jdbcType=DOUBLE}, 
	      #{itm.paidGoodsNumToday,jdbcType=INTEGER}, 
	      #{itm.paidNumTodayNext,jdbcType=INTEGER}, 
	      #{itm.paidNumFinal,jdbcType=INTEGER}, 
	      #{itm.paidGoodsNumFinal,jdbcType=INTEGER}, 
	      #{itm.paidAmountFinal,jdbcType=DOUBLE}, 
	      #{itm.receiveRate,jdbcType=DOUBLE}, 
	      #{itm.conversionRate,jdbcType=DOUBLE}, 
	      #{itm.outStockOrderBuyerNumFinal,jdbcType=INTEGER}, 
	      #{itm.outStockOrderNumFinal,jdbcType=INTEGER}, 
	      #{itm.outStockOrderGoodsNumFinal,jdbcType=INTEGER}, 
	      #{itm.outStockOrderAmountFinal,jdbcType=INTEGER}
	     )
	    </foreach>
	    
  </insert>

  <delete id="deleteCsPerformanceById" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </delete>
  
   <delete id="deleteShopCsPerformanceByDate" >
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date = #{date}
  </delete>  
  
  <delete id="deleteShopCsPerformanceByDateRange" >
    DELETE FROM ${tableName}
    WHERE
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
  
  <update id="updateCsPerformanceById" parameterType="com.pes.jd.model.DO.CsPerformanceDO" >
    update ${tableName}
    <set>
      <if test="directReceiveNum != null">
        direct_receive_num = #{directReceiveNum,jdbcType=INTEGER},
      </if>
      <if test="forwardInNum != null">
        forward_in_num = #{forwardInNum,jdbcType=INTEGER},
      </if>
      <if test="forwardOutNum != null">
        forward_out_num = #{forwardOutNum,jdbcType=INTEGER},
      </if>
      <if test="receiveNum != null">
        receive_num = #{receiveNum,jdbcType=INTEGER},
      </if>
      <if test="enquiryNum != null">
        enquiry_num = #{enquiryNum,jdbcType=INTEGER},
      </if>
      <if test="orderedPayBuyerNum != null">
        ordered_pay_buyer_num = #{orderedPayBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="orderedNumToday != null">
        ordered_num_today = #{orderedNumToday,jdbcType=INTEGER},
      </if>
      <if test="orderedAmountToday != null">
        ordered_amount_today = #{orderedAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="orderedNumFinal != null">
        ordered_num_final = #{orderedNumFinal,jdbcType=INTEGER},
      </if>
      <if test="orderedAmountFinal != null">
        ordered_amount_final = #{orderedAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="paidNumToday != null">
        paid_num_today = #{paidNumToday,jdbcType=INTEGER},
      </if>
      <if test="paidAmountToday != null">
        paid_amount_today = #{paidAmountToday,jdbcType=DOUBLE},
      </if>
      <if test="paidNumFinal != null">
        paid_num_final = #{paidNumFinal,jdbcType=INTEGER},
      </if>
      <if test="paidAmountFinal != null">
        paid_amount_final = #{paidAmountFinal,jdbcType=DOUBLE},
      </if>
      <if test="receiveRate != null">
        receive_rate = #{receiveRate,jdbcType=DOUBLE},
      </if>
      <if test="conversionRate != null">
        conversion_rate = #{conversionRate,jdbcType=DOUBLE},
      </if>
      <if test="saleAmount != null">
        sale_amount = #{saleAmount,jdbcType=DOUBLE},
      </if>
      <if test="saleOrderNum != null">
        sale_order_num = #{saleOrderNum,jdbcType=INTEGER},
      </if>
      <if test="saleGoodsNum != null">
        sale_goods_num = #{saleGoodsNum,jdbcType=INTEGER},
      </if>
      <if test="saleBuyerNum != null">
        sale_buyer_num = #{saleBuyerNum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsONum != null">
        cfm_goods_o_num = #{cfmGoodsONum,jdbcType=INTEGER},
      </if>
      <if test="cfmGoodsAmount != null">
        cfm_goods_amount = #{cfmGoodsAmount,jdbcType=DOUBLE},
      </if>
      <if test="postFee != null">
        post_fee = #{postFee,jdbcType=DOUBLE},
      </if>
    </set>
    WHERE 
    	id = #{id,jdbcType=BIGINT}
   </update> 	
   
  <select id="getCsPerformanceById" parameterType="java.lang.Long" resultMap="CsPerformanceDTO">
    SELECT 
    	<include refid="base_field" />
    FROM ${tableName}
    WHERE 
    	id = #{id,jdbcType=BIGINT}
  </select>

    <select id="searchByDateShopCs" resultMap="CsPerformanceDTO">
        select
        id, shop_id, date, cs_nick, direct_receive_num, forward_in_num, forward_out_num,
        consult_num, receive_num, enquiry_num, ordered_num_today, paid_num_today, ordered_amount_today,
        paid_amount_today, ordered_num_final, ordered_amount_final, ordered_goods_num_final,
        paid_num_today_next, paid_num_final, paid_amount_final, paid_goods_num_final, receive_rate,
        conversion_rate, count(sale_amount) sale_amount, count(sale_order_num) sale_order_num, count(sale_goods_num) sale_goods_num, count(sale_buyer_num) sale_buyer_num, cfm_goods_o_num,
        cfm_goods_amount, post_fee, out_stock_num, out_stock_amount, out_stock_goods_num,
        out_stock_order_num, out_stock_loss_num, out_stock_loss_goods_num, out_stock_loss_order_num,
        out_stock_loss_amount
        from ${tableName} <!-- pes_cs_performance -->
        where shop_id = #{shopId}
        <if test="nicks!=null and nicks.size()>0">
            AND cs_nick in
            <foreach collection="nicks" open="(" close=")" separator="," item="nick">
                #{nick}
            </foreach>
        </if>
        AND date between #{startDate} and #{endDate}
        group by ${groupBy}
        order by date desc
    </select>


</mapper>