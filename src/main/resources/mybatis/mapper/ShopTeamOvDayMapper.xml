<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopTeamOvDayMapper">

	<resultMap id="ShopTeamOvDayDTO" type="com.pes.jd.model.DTO.ShopTeamOvDayDTO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="date" jdbcType="DATE" property="date" />
		<result column="duty_cs_num" jdbcType="INTEGER" property="dutyCsNum" />
		<result column="login_duration_time" jdbcType="BIGINT" property="loginDurationTime" />
		<result column="rceive_duration_time" jdbcType="BIGINT" property="rceiveDurationTime" />
	</resultMap>

	<sql id="base_field">
		shop_id,date,duty_cs_num
	</sql>

	<insert id="insertShopTeamOvDay" parameterType="com.pes.jd.model.DTO.ShopTeamOvDayDTO">
		INSERT INTO ${tableName} (shop_id, date,duty_cs_num,login_duration_time,rceive_duration_time)
		VALUES (#{shopTeamOvDay.shopId},#{shopTeamOvDay.date}, #{shopTeamOvDay.dutyCsNum}, #{shopTeamOvDay.loginDurationTime}, #{shopTeamOvDay.rceiveDurationTime})
	</insert>

	<delete id="deleteShopTeamOvDayByShopIdByDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE
		shop_id = #{shopId}
		AND date = #{date}
	</delete>

</mapper>