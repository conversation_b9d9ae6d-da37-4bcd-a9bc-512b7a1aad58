<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.OrderDetailMapper">
  <resultMap id="OrderDetailDO" type="com.pes.jd.model.DO.OrderDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="item_sku_id" jdbcType="VARCHAR" property="itemSkuId" />
    <result column="item_price" jdbcType="DOUBLE" property="itemPrice" />
    <result column="item_num" jdbcType="INTEGER" property="itemNum" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="ware_id" jdbcType="BIGINT" property="wareId" />
    <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
    <result column="seller_discount" jdbcType="DOUBLE" property="sellerDiscount" />
    <result column="total_fee" jdbcType="DOUBLE" property="totalFee" />
  </resultMap>
   <resultMap id="OrderDetailDTO" type="com.pes.jd.model.DTO.OrderDetailDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="item_sku_id" jdbcType="VARCHAR" property="itemSkuId" />
    <result column="item_price" jdbcType="DOUBLE" property="itemPrice" />
    <result column="item_num" jdbcType="INTEGER" property="itemNum" />
    <result column="buyer_nick" jdbcType="VARCHAR" property="buyerNick" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="ware_id" jdbcType="BIGINT" property="wareId" />
    <result column="payment" jdbcType="DOUBLE" property="payment" />
    <result column="out_stock_time" property="outStockTime" jdbcType="TIMESTAMP" />
     <result column="seller_discount" jdbcType="DOUBLE" property="sellerDiscount" />
    <result column="total_fee" jdbcType="DOUBLE" property="totalFee" />
  </resultMap>

  <insert id="insertOrderCancelGoodsSku" parameterType="map">
	INSERT INTO ${tableName}(order_id, shop_id, item_sku_id, item_price, item_num, buyer_nick, created, pay_time, 
    payment, seller_discount, total_fee)
	VALUES
	<foreach collection="orderDetailList" item="order" separator="," >
	   	(
        #{order.orderId,jdbcType=BIGINT},
        #{order.shopId,jdbcType=BIGINT},
        #{order.itemSkuId,jdbcType=VARCHAR},
        #{order.itemPrice,jdbcType=DOUBLE},
        #{order.itemNum,jdbcType=INTEGER},
        #{order.buyerNick,jdbcType=VARCHAR},
        #{order.created,jdbcType=TIMESTAMP},
        #{order.payTime,jdbcType=TIMESTAMP},
        #{order.payment,jdbcType=DOUBLE},
        #{order.sellerDiscount,jdbcType=DOUBLE},
        #{order.totalFee,jdbcType=DOUBLE}
      	)
      </foreach> 
	</insert>
  	<insert id="insertOrderDetailByFile" parameterType="java.util.Map">
		load data local
		infile #{filePath} into table ${tableName}
		fields terminated by
		'``MYPES`' optionally enclosed by '' escaped by ''
		lines terminated by '`MYPES`\n'
		(order_id, item_sku_id, item_price, item_num, created, buyer_nick, pay_time, payment, shop_id, ware_id, seller_discount, total_fee);
	</insert>
	
	<delete id="deleteOrdersDetailByOids" parameterType="java.util.Map">
		DELETE FROM ${tableName}
		WHERE order_id IN
		<foreach collection="oids" index="index" item="oid" open="("
			separator="," close=")">
			#{oid}
		</foreach>
	</delete>
	
	<delete id="deleteOrderCancelGoodsSkuByOid" parameterType="map">
		DELETE FROM ${tableName}
		WHERE order_id  = #{oid,jdbcType=BIGINT}
	</delete>
	
	<select id="selectOrderGoodsSkuByTableNameAndCreated"
		parameterType="map" resultMap="OrderDetailDO">
		SELECT order_id, created, item_sku_id, item_price, item_num, buyer_nick, ware_id
		FROM ${tableName}
		where 
		shop_id =#{shopId}
		AND 
		created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
	</select>
	
	<select id="selectOrderCreatedDetailByCreated" resultMap="OrderDetailDTO">
		SELECT order_id,buyer_nick,item_sku_id,created,item_num,item_price 
		FROM ${tableName} 
		<where>
		<if test="skuIdSet!=null and skuIdSet.size()>0">
			item_sku_id in
			<foreach collection="skuIdSet" item="skuId" open="(" separator="," close=")">
					#{skuId}
			</foreach>
		</if>
		AND created BETWEEN #{startDate} AND #{endDate}
		AND shop_id =#{shopId}
		</where>
	</select>
	<select id="selectOrderPaidDetailByPayTime" resultMap="OrderDetailDTO">
		SELECT order_id,buyer_nick,item_sku_id,created,item_num,item_price 
		FROM ${tableName} 
		<where>
		 pay_time BETWEEN #{startDate} AND #{endDate}
		AND shop_id =#{shopId}
		</where>
	</select>
	
	<select id="selectOrderGoodsSkuByShopIdLstAndDate" resultMap="OrderDetailDTO">
	
	SELECT 	detail.order_id,
			detail.buyer_nick,
			detail.item_sku_id,
			detail.created,
			detail.item_num,
			detail.item_price,
			detail.shop_id,
			detail.seller_discount,
			detail.total_fee
			
	from 
	(
	<foreach collection="tableNames" item="table" separator="UNION ALL">
		SELECT 
			shop_id,
			order_id,
			buyer_nick,
			item_sku_id,
			created,
			item_num,
			item_price,
			seller_discount,
			total_fee
		FROM ${table.tableName}  
		WHERE 
		    shop_id =#{shopId}
		    AND order_id IN
			<foreach collection="orderIdSet" item="orderId" open="(" close=")" separator=",">
								#{orderId}
			</foreach>
			AND pay_time BETWEEN #{startDate} AND #{endDate}
	</foreach>
	)
	detail
	
	</select>
	
	<select id="selectOrderGoodsSkuByShopIdLstAndCreateDate" resultMap="OrderDetailDTO">
	
		SELECT 
			shop_id,
			order_id,
			buyer_nick,
			item_sku_id,
			created,
			item_num,
			item_price,
			seller_discount,
			total_fee
		FROM ${tableName}  
		WHERE 
		    shop_id =#{shopId}
		    AND order_id IN
			<foreach collection="orderIdSet" item="orderId" open="(" close=")" separator=",">
								#{orderId}
			</foreach>
			AND created BETWEEN #{startDate} AND #{endDate}
	</select>
	
	<select id="selectSlientOrderGoodsSkuByOrderIdLstAndDate" resultMap="OrderDetailDTO">
	
		SELECT order_id,buyer_nick,item_sku_id,created,item_num,item_price ,out_stock_time,seller_discount,
			total_fee
		FROM ${tableName}  
		WHERE 
		order_id not in
		<foreach collection="orderIdLst" item="orderId" open="(" separator="," close=")">
				#{orderId}
		</foreach>
		AND pay_time BETWEEN #{startDate} AND #{endDate}
		AND shop_id =#{shopId}
	</select>
	<select id="selectOrderIdListBySkuidList" resultType="com.pes.jd.model.DO.ShopGoodsReviewDO">
		SELECT order_id orderId,buyer_nick buyerNick,item_sku_id skuId,created orderCreated,pay_time orderPayTime
		FROM ${tableName}
		WHERE  shop_id =#{shopId}
		AND item_sku_id IN
		<foreach collection="skuIdSets" item="skuId" open="(" close=")" separator=",">
			#{skuId}
		</foreach>
		ORDER BY created DESC
		<!-- 	WHERE gr.pay_time IS NOT NULL
            INNER JOIN ${bindTableName} bt
            ON gr.order_id = bt.order_id
            WHERE bt.type = 1 -->
	</select>
	
	<update id="updateOrderByOrderId"
		parameterType="map">
		UPDATE ${tableName}
		<set>
			<if test="outStockTime != null">
				out_stock_time = #{outStockTime,jdbcType=TIMESTAMP}
			</if>
		</set>
		WHERE
		shop_id = #{shopId,jdbcType=BIGINT} 
		AND
		order_id = #{orderId,jdbcType=BIGINT}
	</update>
	
	<select id="selectShopOrderDetailForGoodsAnalysis" resultMap="OrderDetailDTO">
		SELECT 
			ol.order_id, 
			ol.created, 
			ol.item_sku_id, 
			ol.item_price, 
			ol.item_num, 
			ol.buyer_nick,
			ol.seller_discount,
			ol.total_fee
		from 
		(
			<foreach collection="otTableNames" item="table" separator="UNION ALL">
				SELECT 
					order_id, 
					created, 
					item_sku_id, 
					item_price, 
					item_num, 
					buyer_nick,
					seller_discount,
					total_fee
				from ${table.tableName}
				<where>
					shop_id=#{shopId}
					and created between #{table.beginDate} and #{table.endDate}
				</where>
			</foreach>
		) ol
		
	</select>
	<select id="selectOrderGoodsSkuByShopIdLstAndDateNew" resultMap="OrderDetailDTO">


		SELECT 	detail.order_id,
		detail.buyer_nick,
		detail.item_sku_id,
		detail.created,
		detail.item_num,
		detail.item_price,
		detail.shop_id,
		detail.seller_discount,
		detail.total_fee

		from
		(
		<foreach collection="tableNames" item="table" separator="UNION ALL">
			SELECT
			shop_id,
			order_id,
			buyer_nick,
			item_sku_id,
			created,
			item_num,
			item_price,
			seller_discount,
			total_fee
			FROM ${table.tableName}
			WHERE
			shop_id =#{shopId}
			AND order_id IN
			<foreach collection="yesterDaySkuSet" item="orderId" open="(" close=")" separator=",">
				#{orderId}
			</foreach>
			AND pay_time BETWEEN #{startDate} AND #{endDate}
		</foreach>
		)
		detail
	</select>

  <select id="selectOrderGoodsSkuByOrderIdLst" resultMap="OrderDetailDTO">

	  SELECT 	detail.order_id,
	  detail.buyer_nick,
	  detail.item_sku_id,
	  detail.created,
	  detail.item_num,
	  detail.item_price,
	  detail.shop_id,
	  detail.seller_discount,
	  detail.total_fee

	  from
	  (
	  <foreach collection="tableNames" item="table" separator="UNION ALL">
		  SELECT
		  shop_id,
		  order_id,
		  buyer_nick,
		  item_sku_id,
		  created,
		  item_num,
		  item_price,
		  seller_discount,
		  total_fee
		  FROM ${table.tableName}
		  WHERE
		  shop_id =#{shopId}
		  AND order_id IN
		  <foreach collection="orderIdSet" item="orderId" open="(" close=")" separator=",">
			  #{orderId}
		  </foreach>
	  </foreach>
	  )
	  detail

  </select>

  <select id="selectOrderGoodSkuByOrderIdLst" resultMap="OrderDetailDTO">
	  SELECT
	  shop_id,
	  order_id,
	  buyer_nick,
	  item_sku_id,
	  created,
	  item_num,
	  item_price,
	  seller_discount,
	  total_fee
	  FROM ${tableName}
	  WHERE
	  shop_id =#{shopId}
	  AND order_id IN
	  <foreach collection="orderIdLst" item="orderId" open="(" close=")" separator=",">
		  #{orderId}
	  </foreach>
  </select>
</mapper>