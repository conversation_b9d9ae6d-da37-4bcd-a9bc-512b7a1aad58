<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsServiceEvaluationMapper">

	<resultMap id="CsServiceEvaluationDTO" type="com.pes.jd.model.DTO.CsServiceEvaluationDTO">
		<id column="id" property="id" jdbcType="BIGINT" />
		<result column="shop_id" property="shopId" jdbcType="BIGINT" />
		<result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
		<result column="date" property="date" jdbcType="TIMESTAMP" />
		<result column="very_satisfied_num" property="verySatisfiedNum"
			jdbcType="INTEGER" />
		<result column="satisfied_num" property="satisfiedNum"
			jdbcType="INTEGER" />
		<result column="general_num" property="generalNum" jdbcType="INTEGER" />
		<result column="dissatisfied_num" property="dissatisfiedNum"
			jdbcType="INTEGER" />
		<result column="very_dissatisfied_num" property="veryDissatisfiedNum"
			jdbcType="INTEGER" />
		<result column="eval_reply_num" property="evalReplyNum"
			jdbcType="INTEGER" />
		<result column="eval_send_num" property="evalSendNum"
			jdbcType="INTEGER" />
	</resultMap>

	<sql id="base_field">
		id, shop_id, cs_nick, date, very_satisfied_num,
		satisfied_num,
		general_num, dissatisfied_num, very_dissatisfied_num,eval_reply_num,eval_send_num
	</sql>

	<insert id="batchCsEvals" parameterType="map">
		INSERT INTO ${tableName}
		(shop_id,cs_nick,date,very_satisfied_num,
		satisfied_num,general_num,dissatisfied_num,very_dissatisfied_num,eval_reply_num,eval_send_num)
		VALUES
		<foreach collection="csEvalList" item="csEval" separator=",">
			(#{csEval.shopId},#{csEval.csNick},#{csEval.date},#{csEval.verySatisfiedNum},#{csEval.satisfiedNum},#{csEval.generalNum},#{csEval.dissatisfiedNum},#{csEval.veryDissatisfiedNum},#{csEval.evalReplyNum},#{csEval.evalSendNum})
		</foreach>
	</insert>

	<delete id="deleteCsEvalsByShopIdByDate" parameterType="map">
		DELETE
		FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND date BETWEEN
		#{startDate} AND #{endDate}
	</delete>
	<select id="searchByShopNicks" resultType="com.pes.jd.model.DTO.CsServiceEvaluationDTO">

	</select>

</mapper>