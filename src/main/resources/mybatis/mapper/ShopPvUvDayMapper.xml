<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopPvUvDayMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopPvUvDayDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="dt" property="dt" jdbcType="DATE" />
    <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
    <result column="vender_id" property="venderId" jdbcType="BIGINT" />
    <result column="vender_name" property="venderName" jdbcType="VARCHAR" />
    <result column="platform_desc" property="platformDesc" jdbcType="VARCHAR" />
    <result column="avg_rt" property="avgRt" jdbcType="DOUBLE" />
    <result column="avg_rt_newuser" property="avgRtNewuser" jdbcType="DOUBLE" />
    <result column="avg_rt_olduser" property="avgRtOlduser" jdbcType="DOUBLE" />
    <result column="pv" property="pv" jdbcType="BIGINT" />
    <result column="uv" property="uv" jdbcType="BIGINT" />
    <result column="landing_times" property="landingTimes" jdbcType="BIGINT" />
    <result column="quit_times" property="quitTimes" jdbcType="BIGINT" />
    <result column="item_pv" property="itemPv" jdbcType="BIGINT" />
    <result column="item_uv" property="itemUv" jdbcType="BIGINT" />
    <result column="homepage_pv" property="homepagePv" jdbcType="BIGINT" />
    <result column="homepage_uv" property="homepageUv" jdbcType="BIGINT" />
    <result column="add_to_cart_skunum" property="addToCartSkunum" jdbcType="BIGINT" />
    <result column="add_to_cart_skutypenum" property="addToCartSkutypenum" jdbcType="BIGINT" />
    <result column="add_to_cart_users" property="addToCartUsers" jdbcType="BIGINT" />
    <result column="ord_num_deal" property="ordNumDeal" jdbcType="BIGINT" />
    <result column="sale_qtty_deal" property="saleQttyDeal" jdbcType="BIGINT" />
    <result column="before_prefr_amount_deal" property="beforePrefrAmountDeal" jdbcType="DOUBLE" />
    <result column="after_prefr_amount_deal" property="afterPrefrAmountDeal" jdbcType="DOUBLE" />
    <result column="ord_user_num_deal" property="ordUserNumDeal" jdbcType="BIGINT" />
    <result column="pv_old_user" property="pvOldUser" jdbcType="BIGINT" />
    <result column="uv_old_user" property="uvOldUser" jdbcType="BIGINT" />
    <result column="visits_old_user" property="visitsOldUser" jdbcType="BIGINT" />
    <result column="pv_newuser" property="pvNewuser" jdbcType="BIGINT" />
    <result column="uv_newuser" property="uvNewuser" jdbcType="BIGINT" />
    <result column="visits_newuser" property="visitsNewuser" jdbcType="BIGINT" />
    <result column="tp" property="tp" jdbcType="VARCHAR" />
    <result column="visits" property="visits" jdbcType="BIGINT" />
    <result column="bounce_times" property="bounceTimes" jdbcType="BIGINT" />
    <result column="total_pv" property="totalPv" jdbcType="BIGINT" />
    <result column="total_uv" property="totalUv" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, shop_id, dt, shop_name, vender_id, vender_name, platform_desc, avg_rt, avg_rt_newuser, 
    avg_rt_olduser, pv, uv, landing_times, quit_times, item_pv, item_uv, homepage_pv, 
    homepage_uv, add_to_cart_skunum, add_to_cart_skutypenum, add_to_cart_users, ord_num_deal, 
    sale_qtty_deal, before_prefr_amount_deal, after_prefr_amount_deal, ord_user_num_deal, 
    pv_old_user, uv_old_user, visits_old_user, pv_newuser, uv_newuser, visits_newuser, 
    tp, visits, bounce_times
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from pes_shop_pv_uv_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteShopPvUvDay" parameterType="map" >
    delete from ${tableName}
    where dt = #{date} and shop_id = #{shopId}
  </delete>
   <insert id="batchShopPvUvDay" parameterType="map" >
    insert into ${tableName} (`shop_id`, `dt`, `shop_name`, 
      `vender_id`, `vender_name`, `platform_desc`, 
      `avg_rt`, `avg_rt_newuser`, `avg_rt_olduser`, 
      `pv`, `uv`, `landing_times`, 
      `quit_times`, `item_pv`, `item_uv`, 
      `homepage_pv`, `homepage_uv`, `add_to_cart_skunum`, 
      `add_to_cart_skutypenum`, `add_to_cart_users`, `ord_num_deal`, 
      `sale_qtty_deal`, `before_prefr_amount_deal`, `after_prefr_amount_deal`, 
      `ord_user_num_deal`, `pv_old_user`, `uv_old_user`, 
      `visits_old_user`, `pv_newuser`, `uv_newuser`, 
      `visits_newuser`, `tp`, `visits`, 
      `bounce_times`, `total_pv`, `total_uv`)
     values
    <foreach collection="shopPvUvDayLst" item="itm" separator=",">
     (#{itm.shopId,jdbcType=BIGINT}, #{itm.dt,jdbcType=DATE}, #{itm.shopName,jdbcType=VARCHAR}, 
      #{itm.venderId,jdbcType=BIGINT}, #{itm.venderName,jdbcType=VARCHAR}, #{itm.platformDesc,jdbcType=VARCHAR}, 
      #{itm.avgRt,jdbcType=DOUBLE}, #{itm.avgRtNewuser,jdbcType=DOUBLE}, #{itm.avgRtOlduser,jdbcType=DOUBLE}, 
      #{itm.pv,jdbcType=BIGINT}, #{itm.uv,jdbcType=BIGINT}, #{itm.landingTimes,jdbcType=BIGINT}, 
      #{itm.quitTimes,jdbcType=BIGINT}, #{itm.itemPv,jdbcType=BIGINT}, #{itm.itemUv,jdbcType=BIGINT}, 
      #{itm.homepagePv,jdbcType=BIGINT}, #{itm.homepageUv,jdbcType=BIGINT}, #{itm.addToCartSkunum,jdbcType=BIGINT}, 
      #{itm.addToCartSkutypenum,jdbcType=BIGINT}, #{itm.addToCartUsers,jdbcType=BIGINT}, #{itm.ordNumDeal,jdbcType=BIGINT}, 
      #{itm.saleQttyDeal,jdbcType=BIGINT}, #{itm.beforePrefrAmountDeal,jdbcType=DOUBLE}, #{itm.afterPrefrAmountDeal,jdbcType=DOUBLE}, 
      #{itm.ordUserNumDeal,jdbcType=BIGINT}, #{itm.pvOldUser,jdbcType=BIGINT}, #{itm.uvOldUser,jdbcType=BIGINT}, 
      #{itm.visitsOldUser,jdbcType=BIGINT}, #{itm.pvNewuser,jdbcType=BIGINT}, #{itm.uvNewuser,jdbcType=BIGINT}, 
      #{itm.visitsNewuser,jdbcType=BIGINT}, #{itm.tp,jdbcType=VARCHAR}, #{itm.visits,jdbcType=BIGINT}, 
      #{itm.bounceTimes,jdbcType=BIGINT}, #{itm.totalPv,jdbcType=BIGINT}, #{itm.totalUv,jdbcType=BIGINT})
      </foreach>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DO.ShopPvUvDayDO" >
    update pes_shop_pv_uv_day
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="dt != null" >
        dt = #{dt,jdbcType=DATE},
      </if>
      <if test="shopName != null" >
        shop_name = #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="venderId != null" >
        vender_id = #{venderId,jdbcType=BIGINT},
      </if>
      <if test="venderName != null" >
        vender_name = #{venderName,jdbcType=VARCHAR},
      </if>
      <if test="platformDesc != null" >
        platform_desc = #{platformDesc,jdbcType=VARCHAR},
      </if>
      <if test="avgRt != null" >
        avg_rt = #{avgRt,jdbcType=DOUBLE},
      </if>
      <if test="avgRtNewuser != null" >
        avg_rt_newuser = #{avgRtNewuser,jdbcType=DOUBLE},
      </if>
      <if test="avgRtOlduser != null" >
        avg_rt_olduser = #{avgRtOlduser,jdbcType=DOUBLE},
      </if>
      <if test="pv != null" >
        pv = #{pv,jdbcType=BIGINT},
      </if>
      <if test="uv != null" >
        uv = #{uv,jdbcType=BIGINT},
      </if>
      <if test="landingTimes != null" >
        landing_times = #{landingTimes,jdbcType=BIGINT},
      </if>
      <if test="quitTimes != null" >
        quit_times = #{quitTimes,jdbcType=BIGINT},
      </if>
      <if test="itemPv != null" >
        item_pv = #{itemPv,jdbcType=BIGINT},
      </if>
      <if test="itemUv != null" >
        item_uv = #{itemUv,jdbcType=BIGINT},
      </if>
      <if test="homepagePv != null" >
        homepage_pv = #{homepagePv,jdbcType=BIGINT},
      </if>
      <if test="homepageUv != null" >
        homepage_uv = #{homepageUv,jdbcType=BIGINT},
      </if>
      <if test="addToCartSkunum != null" >
        add_to_cart_skunum = #{addToCartSkunum,jdbcType=BIGINT},
      </if>
      <if test="addToCartSkutypenum != null" >
        add_to_cart_skutypenum = #{addToCartSkutypenum,jdbcType=BIGINT},
      </if>
      <if test="addToCartUsers != null" >
        add_to_cart_users = #{addToCartUsers,jdbcType=BIGINT},
      </if>
      <if test="ordNumDeal != null" >
        ord_num_deal = #{ordNumDeal,jdbcType=BIGINT},
      </if>
      <if test="saleQttyDeal != null" >
        sale_qtty_deal = #{saleQttyDeal,jdbcType=BIGINT},
      </if>
      <if test="beforePrefrAmountDeal != null" >
        before_prefr_amount_deal = #{beforePrefrAmountDeal,jdbcType=DOUBLE},
      </if>
      <if test="afterPrefrAmountDeal != null" >
        after_prefr_amount_deal = #{afterPrefrAmountDeal,jdbcType=DOUBLE},
      </if>
      <if test="ordUserNumDeal != null" >
        ord_user_num_deal = #{ordUserNumDeal,jdbcType=BIGINT},
      </if>
      <if test="pvOldUser != null" >
        pv_old_user = #{pvOldUser,jdbcType=BIGINT},
      </if>
      <if test="uvOldUser != null" >
        uv_old_user = #{uvOldUser,jdbcType=BIGINT},
      </if>
      <if test="visitsOldUser != null" >
        visits_old_user = #{visitsOldUser,jdbcType=BIGINT},
      </if>
      <if test="pvNewuser != null" >
        pv_newuser = #{pvNewuser,jdbcType=BIGINT},
      </if>
      <if test="uvNewuser != null" >
        uv_newuser = #{uvNewuser,jdbcType=BIGINT},
      </if>
      <if test="visitsNewuser != null" >
        visits_newuser = #{visitsNewuser,jdbcType=BIGINT},
      </if>
      <if test="tp != null" >
        tp = #{tp,jdbcType=VARCHAR},
      </if>
      <if test="visits != null" >
        visits = #{visits,jdbcType=BIGINT},
      </if>
      <if test="bounceTimes != null" >
        bounce_times = #{bounceTimes,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DO.ShopPvUvDayDO" >
    update pes_shop_pv_uv_day
    set shop_id = #{shopId,jdbcType=BIGINT},
      dt = #{dt,jdbcType=DATE},
      shop_name = #{shopName,jdbcType=VARCHAR},
      vender_id = #{venderId,jdbcType=BIGINT},
      vender_name = #{venderName,jdbcType=VARCHAR},
      platform_desc = #{platformDesc,jdbcType=VARCHAR},
      avg_rt = #{avgRt,jdbcType=DOUBLE},
      avg_rt_newuser = #{avgRtNewuser,jdbcType=DOUBLE},
      avg_rt_olduser = #{avgRtOlduser,jdbcType=DOUBLE},
      pv = #{pv,jdbcType=BIGINT},
      uv = #{uv,jdbcType=BIGINT},
      landing_times = #{landingTimes,jdbcType=BIGINT},
      quit_times = #{quitTimes,jdbcType=BIGINT},
      item_pv = #{itemPv,jdbcType=BIGINT},
      item_uv = #{itemUv,jdbcType=BIGINT},
      homepage_pv = #{homepagePv,jdbcType=BIGINT},
      homepage_uv = #{homepageUv,jdbcType=BIGINT},
      add_to_cart_skunum = #{addToCartSkunum,jdbcType=BIGINT},
      add_to_cart_skutypenum = #{addToCartSkutypenum,jdbcType=BIGINT},
      add_to_cart_users = #{addToCartUsers,jdbcType=BIGINT},
      ord_num_deal = #{ordNumDeal,jdbcType=BIGINT},
      sale_qtty_deal = #{saleQttyDeal,jdbcType=BIGINT},
      before_prefr_amount_deal = #{beforePrefrAmountDeal,jdbcType=DOUBLE},
      after_prefr_amount_deal = #{afterPrefrAmountDeal,jdbcType=DOUBLE},
      ord_user_num_deal = #{ordUserNumDeal,jdbcType=BIGINT},
      pv_old_user = #{pvOldUser,jdbcType=BIGINT},
      uv_old_user = #{uvOldUser,jdbcType=BIGINT},
      visits_old_user = #{visitsOldUser,jdbcType=BIGINT},
      pv_newuser = #{pvNewuser,jdbcType=BIGINT},
      uv_newuser = #{uvNewuser,jdbcType=BIGINT},
      visits_newuser = #{visitsNewuser,jdbcType=BIGINT},
      tp = #{tp,jdbcType=VARCHAR},
      visits = #{visits,jdbcType=BIGINT},
      bounce_times = #{bounceTimes,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>