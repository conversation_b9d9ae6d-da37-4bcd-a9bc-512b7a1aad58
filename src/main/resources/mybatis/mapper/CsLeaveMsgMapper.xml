<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsLeaveMsgMapper">

    <resultMap id="CsLeaveMsgDTO" type="com.pes.jd.model.DTO.CsLeaveMsgDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="cs_nick" property="csNick" jdbcType="VARCHAR"/>
        <result column="customer" property="customer" jdbcType="VARCHAR"/>
        <result column="leave_msg_time" property="leaveMsgTime" jdbcType="TIMESTAMP"/>
        <result column="allocation_time" property="allocationTime" jdbcType="TIMESTAMP"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="sid" property="sid" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="batchInsertCsLeaveMsg" parameterType="map">
        INSERT INTO ${tableName} (shop_id,cs_nick,customer,leave_msg_time,allocation_time,start_time,end_time,sid)
        VALUES
        <foreach collection="csLeaveMsgList" item="csLeaveMsg" separator=",">
            (#{csLeaveMsg.shopId},#{csLeaveMsg.csNick},#{csLeaveMsg.customer},#{csLeaveMsg.leaveMsgTime},
            #{csLeaveMsg.allocationTime},#{csLeaveMsg.startTime},#{csLeaveMsg.endTime},#{csLeaveMsg.sid})
        </foreach>
    </insert>

    <delete id="deleteCsLeaveMsgByShopIdByDate" parameterType="map">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND leave_msg_time BETWEEN #{startDate} AND #{endDate}
	</delete>

    <update id="batchUpdateCsLeaveMsg" parameterType="map">
        <foreach collection="csLeaveMsgList" item="csLeaveMsg" open="" close="" separator=";">
            UPDATE ${tableName}
            SET allocation_time = #{csLeaveMsg.allocationTime},
            cs_nick = #{csLeaveMsg.csNick},
            start_time = #{csLeaveMsg.startTime},
            end_time = #{csLeaveMsg.endTime}
            WHERE id = #{csLeaveMsg.id}
        </foreach>
    </update>

    <select id="selectCsLeaveMsgByDate" parameterType="map"
            resultMap="CsLeaveMsgDTO">
		SELECT * FROM ${tableName}
		WHERE shop_id = #{shopId}
		AND leave_msg_time BETWEEN #{startDate} AND #{endDate}
	</select>

    <select id="searchByDate"
            resultMap="CsLeaveMsgDTO">
		SELECT * FROM ${tableName}
		WHERE leave_msg_time BETWEEN #{startDate} AND #{endDate}
		AND shop_id = #{shopId} and cs_nick = #{nick}
	</select>

    <select id="leaveConsult" resultType="int">
		SELECT
			count(1)
		FROM ${tableName}
		WHERE  shop_id = #{shopId}
		  AND leave_msg_time BETWEEN #{startDate}  AND #{endDate}  ;
    </select>

    <select id="leaveAssign" resultType="int">
		select
			count(1)
		FROM ${tableName}
		WHERE  shop_id = #{shopId}
		  AND cs_nick = #{nick}
		  AND allocation_time IS NOT NULL
		  AND allocation_time BETWEEN #{startDate}  AND #{endDate}
	</select>

    <select id="leaveAssignByDateAndSid" resultType="int">
        select
        count(1)
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND cs_nick = #{nick}
        AND sid in
        <foreach collection="sids" open="(" close=")" separator="," item="sid">
            #{sid}
        </foreach>
        AND allocation_time IS NOT NULL
        AND allocation_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <select id="leaveAssignSidByDateAndSid" resultType="string">
        select
        sid
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND cs_nick = #{nick}
        AND sid in
        <foreach collection="sids" open="(" close=")" separator="," item="sid">
            #{sid}
        </foreach>
        AND allocation_time IS NOT NULL
        AND allocation_time BETWEEN #{startDate} AND #{endDate}
    </select>

</mapper>