<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.UserRoleMapper">

	<resultMap id="UserRoleDO" type="com.pes.jd.model.DO.UserRole">
		<id column="user_id" jdbcType="VARCHAR" property="userId" />
		<id column="role_id" jdbcType="BIGINT" property="roleId" />
	</resultMap>
	
	<insert id="insertUserRole" parameterType="com.pes.jd.model.DO.UserRole">
		INSERT INTO pes_user_role (user_id, role_id)
		VALUES (#{userId,jdbcType=VARCHAR}, #{roleId,jdbcType=BIGINT})
	</insert>
	
	<delete id="deleteUserRoleById" parameterType="com.pes.jd.model.DO.UserRole">
		DELETE FROM pes_user_role
		WHERE
			user_id = #{userId,jdbcType=VARCHAR}
		AND
			role_id = #{roleId,jdbcType=BIGINT}
	</delete>
	
<!-- 	<select id="getUserRoleById" parameterType="java.lang.Long" resultMap="WsUserDO">
		SELECT
			<include refid="base_field" />
		FROM pes_ws_user
		WHERE
			id = #{id,jdbcType=BIGINT}
	</select> -->
	

</mapper>