<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.PresaleActivityMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.PresaleActivityDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="VARCHAR"/>
        <result column="activity_name" property="activityName" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <result column="type" property="type" jdbcType="TINYINT"/>
        <result column="earnest" property="earnest" jdbcType="DOUBLE"/>
        <result column="step_one_num" property="stepOneNum" jdbcType="INTEGER"/>
        <result column="step_two_num" property="stepTwoNum" jdbcType="INTEGER"/>
        <result column="step_three_num" property="stepThreeNum" jdbcType="INTEGER"/>
        <result column="step_one_price" property="stepOnePrice" jdbcType="DOUBLE"/>
        <result column="step_two_price" property="stepTwoPrice" jdbcType="DOUBLE"/>
        <result column="step_three_price" property="stepThreePrice" jdbcType="DOUBLE"/>
        <result column="presale_start_time" property="presaleStartTime" jdbcType="TIMESTAMP"/>
        <result column="presale_end_time" property="presaleEndTime" jdbcType="TIMESTAMP"/>
        <result column="balance_start_time" property="balanceStartTime" jdbcType="TIMESTAMP"/>
        <result column="balance_end_time" property="balanceEndTime" jdbcType="TIMESTAMP"/>
        <result column="ship_time" property="shipTime" jdbcType="TIMESTAMP"/>
        <result column="current_price" property="currentPrice" jdbcType="DOUBLE"/>
        <result column="current_step" property="currentStep" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="banlance_price" property="banlancePrice" jdbcType="DOUBLE"/>
        <result column="presale_pay_type" property="presalePayType" jdbcType="INTEGER"/>
        <result column="ori_price" property="oriPrice" jdbcType="DOUBLE"/>
        <result column="presale_count" property="presaleCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="cancel_time" property="cancelTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, activity_id, activity_name, sku_id, shop_id, `type`, earnest, step_one_num, step_two_num, step_three_num,
    step_one_price, step_two_price, step_three_price, presale_start_time, presale_end_time, 
    balance_start_time, balance_end_time, ship_time, current_price, current_step, create_time, 
    banlance_price, presale_pay_type, ori_price, presale_count, status, cancel_time
  </sql>


    <delete id="deleteByActivityIdAndSkuId" parameterType="java.util.Map">
        delete from ${tableName} where
        <foreach collection="presaleActivityLst" separator=" or " item="item" index="index">
            ( sku_id =#{item.skuId}
            and
            activity_id =#{item.activityId}
            )
        </foreach>
    </delete>

    <delete id="deleteByShopId" parameterType="java.util.Map">
        delete from ${tableName} where
        shop_id = #{shopId,jdbcType=BIGINT}
    </delete>

    <update id="updateByActivityIdAndSkuId" parameterType="map">
        <foreach collection="presaleActivityLst" item="itm" open="" close="" separator=";">
            UPDATE ${tableName}
            set status = 1
            WHERE
            sku_id =#{itm.skuId}
            and
            activity_id =#{itm.activityId}
        </foreach>
    </update>


    <update id="updateByActivityIdAndStatus" parameterType="map">
      UPDATE ${tableName}
      set status = #{status}
      WHERE
      shop_id =#{shopId}
      and balance_end_time &lt; #{date}
  </update>

    <insert id="batchInsert" parameterType="java.util.Map">
        insert into ${tableName} (activity_id, sku_id,
        shop_id, type, earnest,
        step_one_num, step_two_num, step_three_num,
        step_one_price, step_two_price, step_three_price,
        presale_start_time, presale_end_time, balance_start_time,
        balance_end_time, ship_time, current_price,
        current_step, create_time, banlance_price,
        presale_pay_type, ori_price, presale_count,
        activity_name,status)
        values
        <foreach collection="presaleActivityLst" item="itm" separator=","
                 index="index">
            (#{itm.activityId,jdbcType=VARCHAR}, #{itm.skuId,jdbcType=BIGINT},
            #{itm.shopId,jdbcType=BIGINT}, #{itm.type,jdbcType=TINYINT}, #{itm.earnest,jdbcType=DOUBLE},
            #{itm.stepOneNum,jdbcType=INTEGER}, #{itm.stepTwoNum,jdbcType=INTEGER},
            #{itm.stepThreeNum,jdbcType=INTEGER},
            #{itm.stepOnePrice,jdbcType=DOUBLE}, #{itm.stepTwoPrice,jdbcType=DOUBLE},
            #{itm.stepThreePrice,jdbcType=DOUBLE},
            #{itm.presaleStartTime,jdbcType=TIMESTAMP}, #{itm.presaleEndTime,jdbcType=TIMESTAMP},
            #{itm.balanceStartTime,jdbcType=TIMESTAMP},
            #{itm.balanceEndTime,jdbcType=TIMESTAMP}, #{itm.shipTime,jdbcType=TIMESTAMP},
            #{itm.currentPrice,jdbcType=DOUBLE},
            #{itm.currentStep,jdbcType=INTEGER}, #{itm.createTime,jdbcType=TIMESTAMP},
            #{itm.banlancePrice,jdbcType=DOUBLE},
            #{itm.presalePayType,jdbcType=INTEGER}, #{itm.oriPrice,jdbcType=DOUBLE},
            #{itm.presaleCount,jdbcType=INTEGER},
            #{itm.activityName,jdbcType=VARCHAR},
            #{itm.status,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="selectByShopIdAndDateForPresalePerformance" resultType="com.pes.jd.model.DTO.PresaleActivityDTO">
        SELECT id, activity_id, activity_name, sku_id, shop_id, type, earnest, step_one_num, step_two_num,
        step_three_num,
        step_one_price, step_two_price, step_three_price, presale_start_time, presale_end_time,
        balance_start_time, balance_end_time, ship_time, current_price, current_step, create_time,
        banlance_price, presale_pay_type, ori_price, presale_count, status, cancel_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND NOT ((balance_end_time &lt; #{dateStart}) OR (presale_start_time &gt; #{dateEnd}))
    </select>


    <select id="selectActivityPeriodByShopIdAndDateForPresalePerformance"
            resultType="com.pes.jd.model.DTO.PresaleActivityDTO">
        SELECT MIN(presale_start_time) presale_start_time, MAX(presale_end_time) presale_end_time
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND NOT ((balance_end_time &lt; #{dateStart}) OR (presale_start_time &gt; #{dateEnd}))
    </select>


    <select id="selectCountByShopIdAndDate" resultType="java.lang.Integer">
       SELECT count(id)
        FROM ${tableName}
        WHERE shop_id = #{shopId}
        AND NOT ((balance_end_time &lt; #{dateStart}) OR (presale_start_time &gt; #{dateEnd}))
    </select>

</mapper>