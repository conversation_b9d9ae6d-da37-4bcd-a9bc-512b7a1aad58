<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderCustomerInfoMapper" >
  <resultMap id="OrderCustomerInfoDO" type="com.pes.jd.model.DO.OrderCustomerInfoDO" >
    <id column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="consignee_telp" property="consigneeTelp" jdbcType="VARCHAR" />
    <result column="consignee" property="consignee" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="base_field" >
    order_id, shop_id, date, consignee_telp, consignee
  </sql>

  <insert id="persistOrderCustomerInfoByFile" parameterType="java.util.Map">
        load data local
        infile #{filePath} into table ${tableName}
        fields terminated by
        '``MYPES`' optionally enclosed by '' escaped by ''
        lines terminated by '`MYPES`\n'
		(order_id, shop_id, date, consignee_telp, consignee);
   </insert>

  <delete id="deleteOrderCustomerInfoByOrderIdLst" parameterType="java.util.Map">
    DELETE FROM ${tableName}
    WHERE order_id IN
    <foreach collection="tids" index="index" item="tid" open="("
             separator="," close=")">
      #{tid}
    </foreach>
  </delete>

</mapper>