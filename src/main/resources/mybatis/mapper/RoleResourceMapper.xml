<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.RoleResourceMapper">
  <resultMap id="RoleResourceDO" type="com.pes.jd.model.DO.RoleResource">
    <id column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <id column="role_id" jdbcType="BIGINT" property="roleId" />
  </resultMap>
 
  <delete id="deleteRoleResource" parameterType="com.pes.jd.model.DO.RoleResource">
    DELETE FROM pes_role_resource
    WHERE 
      resource_id = #{resourceId,jdbcType=BIGINT}
      AND role_id = #{roleId,jdbcType=BIGINT}
  </delete>
  <insert id="insertRoleResource" parameterType="com.pes.jd.model.DO.RoleResource">
    INSERT INTO pes_role_resource (resource_id, role_id)
    VALUES (#{resourceId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT})
  </insert>
<!--   <select id="selectRoleResourceByRoleId" parameterType="long" resultMap="RoleResourceDO">
  	SELECT resource_id,role_id FROM pes_role_resource WHERE role_id = #{roleId,jdbcType=BIGINT}
  </select> -->
  
</mapper>