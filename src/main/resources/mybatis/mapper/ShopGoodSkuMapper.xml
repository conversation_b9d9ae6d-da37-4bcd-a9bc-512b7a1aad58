<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.ShopGoodSkuMapper">
    <resultMap id="BaseResultMap" type="com.pes.jd.model.DO.ShopGoodSkuDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ware_name" property="wareName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="stock_num" property="stockNum" jdbcType="INTEGER"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <!--      <result column="created" jdbcType="TIMESTAMP" property="created" />-->
        <!--      <result column="modified" jdbcType="TIMESTAMP" property="modified" />-->
    </resultMap>
    <resultMap id="ShopGoodSkuDTO" type="com.pes.jd.model.DTO.ShopGoodSkuDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="ware_name" property="wareName" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="ware_id" property="wareId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="stock_num" property="stockNum" jdbcType="INTEGER"/>
        <result column="category_id" property="categoryId" jdbcType="BIGINT"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="shop_id" property="shopId" jdbcType="BIGINT"/>
        <!--       <result column="created" jdbcType="TIMESTAMP" property="created" />-->
        <!--       <result column="modified" jdbcType="TIMESTAMP" property="modified" />-->
    </resultMap>
    <sql id="Base_Column_List">
    id, sku_name, ware_name, price, ware_id, sku_id, status, stock_num, category_id, 
    image_url, shop_id,created, modified
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from pes_shop_goods_sku
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByShopGoodsSkuName" parameterType="map">
		delete from ${tableName}
		WHERE shop_id = #{shopId}
   </delete>

    <delete id="deleteByShopGoodsSkuNameBySkuId" parameterType="map">
		delete from ${tableName}
		WHERE shop_id = #{shopId} and sku_id = #{skuId}
   </delete>

    <delete id="deleteByShopIdAndSkuIdLst" parameterType="map">
        delete from ${tableName}
        WHERE shop_id = #{shopId} and sku_id in
        <foreach collection="skuIdLst" item="skuId" index="index"
                 open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </delete>

    <insert id="batchShopGoodsSku" parameterType="map">
        INSERT INTO ${tableName}(sku_name, ware_name, price, ware_id, sku_id, status, stock_num, category_id,
        image_url, shop_id)
        VALUES
        <foreach collection="shopGoodsSkuLst" item="itm" separator=",">
            (#{itm.skuName}, #{itm.wareName}, #{itm.price}, #{itm.wareId},
            #{itm.skuId}, #{itm.status}, #{itm.stockNum}, #{itm.categoryId}, #{itm.imageUrl}, #{itm.shopId})
        </foreach>
    </insert>
    <select id="selectShopGoodsSkuByShopId" resultMap="ShopGoodSkuDTO">
    	select shop_id,sku_name,sku_id, price from ${tableName}
    	where shop_id=#{shopId}
    </select>
    <select id="selectShopGoodsSkuByShopIdAndSkuIdLst" resultMap="ShopGoodSkuDTO">
        SELECT
        sku_id, price
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND sku_id IN
        <foreach collection="skuIdLst" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>
    <select id="selectByShopIdAndSkuIds" resultMap="ShopGoodSkuDTO">
        SELECT
        sku_name, sku_id,price
        FROM ${tableName}
        WHERE
        shop_id=#{shopId}
        AND sku_id IN
        <foreach collection="skuIds" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
        AND sku_name IS NOT NULL
    </select>
    <select id="selectShopGoodsSkuNumByShopId" resultType="java.lang.Long">
    	select count(1) from ${tableName}
    	where shop_id=#{shopId}
    </select>

    <insert id="insertShopGoodSkuByFile" parameterType="map">
		load data local
		infile #{filePath} into table ${tableName}
		fields terminated by
		'``MYPES`' optionally enclosed by '' escaped by ''
	    lines terminated by '`MYPES`\n'
	    (shop_id, sku_name, ware_name, price, ware_id, sku_id, status, stock_num, category_id, image_url);
	</insert>

    <select id="queryShopGoodsInfoBySkuIdAndShopId" resultMap="ShopGoodSkuDTO">
        select shop_id,sku_name,sku_id,price from ${tableName}
        where shop_id=#{shopId}
        and sku_id=#{skuId}
    </select>

    <select id="selectShopGoodsSkuIdByShopId" resultType="java.lang.Long">
            select sku_id from ${tableName}
            where shop_id=#{shopId}
    </select>

    <select id="selectShopGoodsSkuIdByShopIdBySkuIds" resultType="java.lang.Long">
        select sku_id from ${tableName}
        where shop_id=#{shopId}
        and sku_id in
        <foreach collection="skuIdSet" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="selectShopGoodsSkuIdByShopIdByStatus" resultType="java.lang.Long">
        select sku_id from ${tableName}
        where shop_id=#{shopId}
        and status =#{status}
    </select>

    <update id="updateShopGoodsSkuIdByShopIdBySkuIds" parameterType="map">
        update ${tableName}
        set sku_mark = #{status}
        where shop_id=#{shopId}
        and sku_id in
        <foreach collection="skuIdSet" item="skuId" open="(" close=")" separator=",">
            #{skuId}
        </foreach>
    </update>

    <select id="selectShopGoodsSkuByShopIdByStatus" resultMap="ShopGoodSkuDTO">
    	select shop_id,ware_id,sku_id from ${tableName}
    	where shop_id=#{shopId}
    	and status=#{status}
    	limit #{currentPage},#{pageSize}
    </select>

    <select id="selectSkuCountByShopIdByStatus" resultType="java.lang.Integer">
    	select count(id) from ${tableName}
    	where shop_id=#{shopId}
    	and status=#{status}
    </select>
</mapper>