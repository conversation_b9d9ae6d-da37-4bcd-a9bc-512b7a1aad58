<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.shopDetailMapper">

	<resultMap id="ShopDetailDO" type="com.pes.jd.model.DO.shopDetail">
		<id column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="first_name" jdbcType="VARCHAR" property="firstName" />
		<result column="phone" jdbcType="VARCHAR" property="phone" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="contact_qq" jdbcType="VARCHAR" property="contactQq" />
	</resultMap>

	<sql id="base_field">
		shop_id, first_name, phone, email, contact_qq
	</sql>

	<insert id="insertShopDetail" parameterType="com.pes.jd.model.DO.shopDetail">
		INSERT INTO pes_shop_detail (shop_id, first_name, phone, email, contact_qq)
		VALUES 
		(
			#{shopId,jdbcType=BIGINT}, #{firstName,jdbcType=VARCHAR},
			#{phone,jdbcType=VARCHAR},
			#{email,jdbcType=VARCHAR}, #{contactQq,jdbcType=VARCHAR}
		)
	</insert>
	
	<delete id="deleteShopDetailById" parameterType="java.lang.Long">
		DELETE FROM pes_shop_detail
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</delete>

	<update id="updateShopDetailById" parameterType="com.pes.jd.model.DO.shopDetail">
		UPDATE pes_shop_detail
		<set>
			<if test="firstName != null">
				first_name = #{firstName,jdbcType=VARCHAR},
			</if>
			<if test="phone != null">
				phone = #{phone,jdbcType=VARCHAR},
			</if>
			<if test="email != null">
				email = #{email,jdbcType=VARCHAR},
			</if>
			<if test="contactQq != null">
				contact_qq = #{contactQq,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</update>
<!-- 	
	<select id="getShopDetailById" parameterType="java.lang.Long" resultMap="BaseResultMap">
		SELECT
			<include refid="base_field" />
		FROM 
			pes_shop_detail
		WHERE 
			shop_id = #{shopId,jdbcType=BIGINT}
	</select> -->
</mapper>