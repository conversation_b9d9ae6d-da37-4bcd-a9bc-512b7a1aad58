<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsRecommendGoodsMapper" >
  <resultMap id="CsRecommendGoodsDO" type="com.pes.jd.model.DO.CsRecommendGoodsDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="CsRecommendGoodsDTO" type="com.pes.jd.model.DTO.CsRecommendGoodsDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="customer" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="item_price" jdbcType="DOUBLE" property="itemPrice" />
    <result column="item_num" jdbcType="INTEGER" property="itemNum" />
  </resultMap>
  
  <resultMap id="CsPurchaseGoodsResultDTO" type="com.pes.jd.model.DTO.CsPurchaseGoodsResultDTO" >
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="sku_id" property="skuId" jdbcType="BIGINT" />
    <result column="customer" property="buyerNick" jdbcType="VARCHAR" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
  </resultMap>
  
  <sql id="base_field" >
    id, shop_id, date, sku_id, customer, cs_nick, result,order_id
  </sql>
  
  <insert id="insertCsRecommendGoods" parameterType="com.pes.jd.model.DO.CsRecommendGoodsDO" >
    INSERT INTO pes_cs_recommend_goods (id, shop_id, date, 
      sku_id, customer, cs_nick, 
      result)
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{skuId,jdbcType=BIGINT}, #{customer,jdbcType=VARCHAR}, #{csNick,jdbcType=VARCHAR}, 
      #{result,jdbcType=INTEGER})
  </insert>
  
  <insert id="batchInsertCsRecommendGoods" parameterType="map" >
    INSERT INTO ${tableName} (shop_id, date,  sku_id, customer, cs_nick, result,order_id) VALUES
     <foreach collection="csRecommendGoodsLst" item="itm" separator=",">
     (
     	  #{itm.shopId,jdbcType=BIGINT}, 
	      #{itm.date,jdbcType=DATE}, 
	      #{itm.skuId,jdbcType=BIGINT}, 
	      #{itm.customer,jdbcType=VARCHAR}, 
	      #{itm.csNick,jdbcType=VARCHAR}, 
	      #{itm.result,jdbcType=INTEGER},
	      #{itm.orderId,jdbcType=BIGINT}
      )
    </foreach>
    
  </insert>
    
  <delete id="deleteTargetDateShopCsRecommendGoods" parameterType="map" >
	DELETE FROM ${tableName}
	WHERE 
		shop_id = #{shopId}
	<!-- AND 
		date = #{date} -->
  </delete>
  
  <delete id="deleteCsRecommendGoodsByShopIdAndDate" parameterType="map" >
		DELETE FROM  ${tableName}
		WHERE shop_id = #{shopId}
			AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
 
  <delete id="deleteCsRecommendGoodsById" parameterType="java.lang.Long" >
    DELETE FROM pes_cs_recommend_goods
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
 
 
  <update id="batchUpdateCsRecommendGoods" parameterType="map" >
 	 <foreach collection="resultLst" item="itm" open="" close="" separator=";">
	  	    UPDATE ${tableName}
		 <set >
	        result = #{itm.result,jdbcType=INTEGER}
	    </set>
	    <where>
	     	id=#{itm.id}
	    </where>
	</foreach>
  </update>
  
   <select id="selectCsRecommendGoodsById" resultMap="CsRecommendGoodsDO" parameterType="java.lang.Long" >
    SELECT 
    <include refid="base_field" />
    FROM pes_cs_recommend_goods
    WHERE id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectCsRecommentNumByShopIdAndCsNickAndDate" parameterType="map" resultType="com.pes.jd.model.DTO.csRecommentGoodsSummaryDTO">
  	SELECT  sku_id sku_Id, count(DISTINCT customer) recommendNum
  	FROM ${tableName}
  	where
  	 cs_nick=#{csNick}
  	AND shop_id=#{shopId}
  	AND date between #{startDate} and #{endDate}
  	
  	GROUP BY sku_id 
  </select>
  
  <select id="selectPurchasesBuyerNumByShopIdAndCsNickAndDate" parameterType="map" resultType="com.pes.jd.model.DTO.csRecommentGoodsSummaryDTO">
  SELECT  sku_id skuId, count(DISTINCT customer) purchasesBuyerNum 
  FROM ${tableName}
  WHERE
     cs_nick=#{csNick}
  AND shop_id=#{shopId}
  AND date between #{startDate} and #{endDate} 
  AND result=1 
  GROUP BY sku_id 
  </select>
  
  <select id="selectCsRecommentGoodsPurchaseResultByCsNickAndDate" parameterType="map" resultMap="CsRecommendGoodsDTO">
		  	select 
			  	shop_id, 
			  	date, 
			  	sku_id, 
			  	customer, 
			  	cs_nick, 
			  	result,
			  	order_id
		  	from ${tableName}
		  	where 
		  		shop_id=#{shopId}
		  	AND	cs_nick =#{csNick}
		  	 
		  	AND date BETWEEN #{startDate} AND #{endDate}
  
  </select>
  
  <select id="selectCsRecommentByshopIdByDate" resultType="com.pes.jd.model.DTO.CsPurchaseGoodsResultDTO">
  	select 
  	id,
  	shop_id shopId,
	date date, 
	sku_id skuId, 
	customer buyerNick, 
	cs_nick csNick, 
	result result,
	order_id orderId
  	from ${tableName}
  	where 
  		 shop_id=#{shopId}
  	and  date between #{startDate} and #{endDate} 
  	and  result=0
  </select>
</mapper>