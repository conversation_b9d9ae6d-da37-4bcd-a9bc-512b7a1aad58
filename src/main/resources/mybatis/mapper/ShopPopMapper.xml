<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.ShopPopMapper">

	<resultMap id="ShopPopDO" type="com.pes.jd.model.DO.ShopPopDO">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="shop_id" jdbcType="BIGINT" property="shopId" />
		<result column="status" jdbcType="INTEGER" property="status" />
		<result column="pop_name" jdbcType="VARCHAR" property="popName" />
		<result column="erp_id" jdbcType="VARCHAR" property="erpId" />
	</resultMap>

	<sql id="base_field">
		id, shop_id, status,pop_name,erp_id
	</sql>

	<insert id="insert" parameterType="com.pes.jd.model.DO.ShopPopDO">
		INSERT INTO pes_shop_pop (id, shop_id, pop_name,status,erp_id)
		VALUES
		(
		#{record.id,jdbcType=BIGINT}, #{record.shopId,jdbcType=BIGINT}, #{record.popName,jdbcType=VARCHAR},
		#{record.status,jdbcType=INTEGER},#{record.erpId,jdbcType=VARCHAR}
		)
	</insert>
	
<!-- 	<delete id="deleteWsUserById" parameterType="java.lang.Long"> -->
<!-- 		DELETE FROM pes_ws_user -->
<!-- 		WHERE -->
<!-- 		id = #{id,jdbcType=BIGINT} -->
<!-- 	</delete> -->

	<update id="update" parameterType="com.pes.jd.model.DO.ShopPopDO">
		UPDATE pes_shop_pop
		SET pop_name = #{record.popName,jdbcType=VARCHAR},
		 status = #{record.status,jdbcType=INTEGER},
		 erp_id = #{record.erpId,jdbcType=VARCHAR}
		WHERE
		shop_id = #{record.shopId,jdbcType=BIGINT}
	</update>

	<select id="selectShopPopByShopId" resultType="com.pes.jd.model.DTO.ShopPopDTO">
		select * from pes_shop_pop where shop_id = #{shopId}
	</select>

</mapper>