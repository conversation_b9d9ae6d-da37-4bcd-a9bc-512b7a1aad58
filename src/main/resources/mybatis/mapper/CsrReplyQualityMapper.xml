<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.CsrReplyQualityMapper" >

<!--   <resultMap id="CsrReplyQualityDO" type="com.pes.jd.model.DO.CsrReplyQualityDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="date" property="date" jdbcType="DATE" />
    <result column="cs_nick" property="csNick" jdbcType="VARCHAR" />
    <result column="avg_wait_time_first" property="avgWaitTimeFirst" jdbcType="DOUBLE" />
    <result column="avg_wait_time" property="avgWaitTime" jdbcType="DOUBLE" />
  </resultMap> -->
  
  <sql id="base_field" >
    id, shop_id, date, cs_nick, avg_wait_time_first, avg_wait_time
  </sql>
  
  <insert id="insertCsrReplyQuality" parameterType="com.pes.jd.model.DO.CsrReplyQualityDO" >
    INSERT INTO ${tableName} (id, shop_id, date, 
      cs_nick, avg_wait_time_first, avg_wait_time
      )
    VALUES (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{csNick,jdbcType=VARCHAR}, #{avgWaitTimeFirst,jdbcType=DOUBLE}, #{avgWaitTime,jdbcType=DOUBLE}
      )
  </insert>
  
  <insert id="batchInsertCsrReplyQuality">
    INSERT INTO ${tableName} (shop_id, date, cs_nick, avg_wait_time_first, avg_wait_time)
    VALUES 
    <foreach collection="csReplyQualityLst" item="itm" separator=",">
    (
    	#{itm.shopId,jdbcType=BIGINT}, 
    	#{itm.date,jdbcType=DATE}, 
      	#{itm.csNick,jdbcType=VARCHAR}, 
      	#{itm.avgWaitTimeFirst,jdbcType=DOUBLE}, 
      	#{itm.avgWaitTime,jdbcType=DOUBLE}
    )
    </foreach>

  </insert>
  
  
  <delete id="deleteCsrReplyQualityId" parameterType="java.lang.Long" >
    DELETE FROM ${tableName}
    WHERE id = #{id,jdbcType=BIGINT}
  </delete>
  
  <delete id="deleteCsrReplyQualityByDate">
    DELETE FROM ${tableName}
    WHERE 
    	shop_id = #{shopId,jdbcType=BIGINT}
    AND date BETWEEN #{startDate} AND #{endDate}
  </delete>
  
  <update id="updateCsrReplyQualityId" parameterType="com.pes.jd.model.DO.CsrReplyQualityDO" >
    UPDATE ${tableName}
    <set >
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null" >
        date = #{date,jdbcType=DATE},
      </if>
      <if test="csNick != null" >
        cs_nick = #{csNick,jdbcType=VARCHAR},
      </if>
      <if test="avgWaitTimeFirst != null" >
        avg_wait_time_first = #{avgWaitTimeFirst,jdbcType=DOUBLE},
      </if>
      <if test="avgWaitTime != null" >
        avg_wait_time = #{avgWaitTime,jdbcType=DOUBLE},
      </if>
    </set>
    WHERE id = #{id,jdbcType=BIGINT}
  </update>
</mapper>