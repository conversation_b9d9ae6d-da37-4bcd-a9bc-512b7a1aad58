<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.mapper.UserPortraitStatisticsMapper">


	<!-- 插入用户画像统计数据 -->
	<insert id="insertUserPortraitStatistics" parameterType="com.pes.jd.model.DO.UserPortraitStatistics">
		INSERT INTO ${tableName} (
			shop_id,
			statistics_date,
			age_0_to_15,
			age_16_to_20,
			age_21_to_25,
			age_26_to_30,
			age_31_to_35,
			age_36_to_40,
			age_41_to_45,
			age_46_to_50,
			age_51_to_55,
			age_56_to_60,
			age_61_to_65,
			age_66_to_70,
			age_over_71,
			gender_male,
			gender_female,
			marriage_single,
			marriage_married,
			profession_finance,
			profession_medical,
			profession_employee,
			profession_worker,
			profession_teacher,
			profession_farmer,
			profession_student,
			profession_individual,
			profession_urban_other,
			profession_rural_other,
			children_unknown,
			children_one,
			children_two,
			children_three,
			children_four,
			price_sensitivity_luxury,
			price_sensitivity_high_collar,
			price_sensitivity_high_quality,
			price_sensitivity_urban_collar,
			price_sensitivity_mass,
			price_sensitivity_low_power,
			price_sensitivity_inactive,
			promotion_stable,
			promotion_rising,
			promotion_declining,
			promotion_hesitant,
			promotion_low_value,
			presale_yes,
			presale_no,
			promotion_not_sensitive,
			promotion_lightly_sensitive,
			promotion_moderately_sensitive,
			promotion_highly_sensitive,
			promotion_very_sensitive,
			promotion_sensitivity_unknown,
			high_consumption_yes,
			high_consumption_no,
			new_product_potential_yes,
			new_product_potential_no,
			new_product_medium_yes,
			new_product_medium_no,
			new_product_severe_yes,
			new_product_severe_no,
			comment_high,
			comment_medium,
			comment_low,
			comment_unknown,
			region_distribution,
			avg_customer_unit_price,
			high_unit_price_users,
			total_users,
			student_group,
			elderly_group,
			urban_family_group,
			town_family_group,
			urban_gen_z_group,
			urban_middle_class_group,
			urban_blue_collar_group,
			town_middle_aged_group,
			town_youth_group,
			town_middle_class_group
		) VALUES (
					 #{statistics.shopId},
					 #{statistics.statisticsDate},
					 #{statistics.age0To15},
					 #{statistics.age16To20},
					 #{statistics.age21To25},
					 #{statistics.age26To30},
					 #{statistics.age31To35},
					 #{statistics.age36To40},
					 #{statistics.age41To45},
					 #{statistics.age46To50},
					 #{statistics.age51To55},
					 #{statistics.age56To60},
					 #{statistics.age61To65},
					 #{statistics.age66To70},
					 #{statistics.ageOver71},
					 #{statistics.genderMale},
					 #{statistics.genderFemale},
					 #{statistics.marriageSingle},
					 #{statistics.marriageMarried},
					 #{statistics.professionFinance},
					 #{statistics.professionMedical},
					 #{statistics.professionEmployee},
					 #{statistics.professionWorker},
					 #{statistics.professionTeacher},
					 #{statistics.professionFarmer},
					 #{statistics.professionStudent},
					 #{statistics.professionIndividual},
					 #{statistics.professionUrbanOther},
					 #{statistics.professionRuralOther},
					 #{statistics.childrenUnknown},
					 #{statistics.childrenOne},
					 #{statistics.childrenTwo},
					 #{statistics.childrenThree},
					 #{statistics.childrenFour},
					 #{statistics.priceSensitivityLuxury},
					 #{statistics.priceSensitivityHighCollar},
					 #{statistics.priceSensitivityHighQuality},
					 #{statistics.priceSensitivityUrbanCollar},
					 #{statistics.priceSensitivityMass},
					 #{statistics.priceSensitivityLowPower},
					 #{statistics.priceSensitivityInactive},
					 #{statistics.promotionStable},
					 #{statistics.promotionRising},
					 #{statistics.promotionDeclining},
					 #{statistics.promotionHesitant},
					 #{statistics.promotionLowValue},
					 #{statistics.presaleYes},
					 #{statistics.presaleNo},
					 #{statistics.promotionNotSensitive},
					 #{statistics.promotionLightlySensitive},
					 #{statistics.promotionModeratelySensitive},
					 #{statistics.promotionHighlySensitive},
					 #{statistics.promotionVerySensitive},
					 #{statistics.promotionSensitivityUnknown},
					 #{statistics.highConsumptionYes},
					 #{statistics.highConsumptionNo},
					 #{statistics.newProductPotentialYes},
					 #{statistics.newProductPotentialNo},
					 #{statistics.newProductMediumYes},
					 #{statistics.newProductMediumNo},
					 #{statistics.newProductSevereYes},
					 #{statistics.newProductSevereNo},
					 #{statistics.commentHigh},
					 #{statistics.commentMedium},
					 #{statistics.commentLow},
					 #{statistics.commentUnknown},
					 #{statistics.regionDistribution},
					 #{statistics.avgCustomerUnitPrice},
					 #{statistics.highUnitPriceUsers},
					 #{statistics.totalUsers},
					 #{statistics.studentGroup},
					 #{statistics.elderlyGroup},
					 #{statistics.urbanFamilyGroup},
					 #{statistics.townFamilyGroup},
					 #{statistics.urbanGenZGroup},
					 #{statistics.urbanMiddleClassGroup},
					 #{statistics.urbanBlueCollarGroup},
					 #{statistics.townMiddleAgedGroup},
					 #{statistics.townYouthGroup},
					 #{statistics.townMiddleClassGroup}
				 )
	</insert>

	<!-- 删除用户画像统计数据 -->
	<delete id="deleteUserPortraitStatistics">
		DELETE FROM ${tableName}
		WHERE shop_id = #{shopId}
		  AND statistics_date = #{startDate}
	</delete>

</mapper>
