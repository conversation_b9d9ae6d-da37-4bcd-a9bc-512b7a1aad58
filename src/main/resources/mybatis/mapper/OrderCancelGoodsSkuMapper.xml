<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pes.jd.mapper.OrderCancelGoodsSkuMapper" >
  <resultMap id="BaseResultMap" type="com.pes.jd.model.DTO.OrderCancelGoodsSkuDTO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="shop_id" property="shopId" jdbcType="BIGINT" />
    <result column="item_sku_id" property="itemSkuId" jdbcType="VARCHAR" />
    <result column="item_price" property="itemPrice" jdbcType="DOUBLE" />
    <result column="item_num" property="itemNum" jdbcType="INTEGER" />
    <result column="buyer_nick" property="buyerNick" jdbcType="VARCHAR" />
    <result column="created" property="created" jdbcType="TIMESTAMP" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="ware_id" property="wareId" jdbcType="BIGINT" />
    <result column="payment" property="payment" jdbcType="DOUBLE" />
    <result column="seller_discount" property="sellerDiscount" jdbcType="DOUBLE" />
    <result column="total_fee" property="totalFee" jdbcType="DOUBLE" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, order_id, shop_id, item_sku_id, item_price, item_num, buyer_nick, created, pay_time, 
    ware_id, payment, seller_discount, total_fee
  </sql>
  <select id="selectOrderCancelGoodsSkuByShopIdAndDate" resultMap="BaseResultMap" parameterType="map" >
    select 
    <include refid="Base_Column_List" />
    from ${tableName}
    where 
    shop_id = #{shopId,jdbcType=BIGINT}
    AND
    created BETWEEN #{startDate,jdbcType=TIMESTAMP} AND #{endDate,jdbcType=TIMESTAMP}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from pes_order_cancel_goods_sku_2019_04
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.model.DTO.OrderCancelGoodsSkuDTO" >
    insert into pes_order_cancel_goods_sku_2019_04 (id, order_id, shop_id, 
      item_sku_id, item_price, item_num, 
      buyer_nick, created, pay_time, 
      ware_id, payment, seller_discount, 
      total_fee)
    values (#{id,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, 
      #{itemSkuId,jdbcType=VARCHAR}, #{itemPrice,jdbcType=DOUBLE}, #{itemNum,jdbcType=INTEGER}, 
      #{buyerNick,jdbcType=VARCHAR}, #{created,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP}, 
      #{wareId,jdbcType=BIGINT}, #{payment,jdbcType=DOUBLE}, #{sellerDiscount,jdbcType=DOUBLE}, 
      #{totalFee,jdbcType=DOUBLE})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.model.DTO.OrderCancelGoodsSkuDTO" >
    insert into pes_order_cancel_goods_sku_2019_04
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="shopId != null" >
        shop_id,
      </if>
      <if test="itemSkuId != null" >
        item_sku_id,
      </if>
      <if test="itemPrice != null" >
        item_price,
      </if>
      <if test="itemNum != null" >
        item_num,
      </if>
      <if test="buyerNick != null" >
        buyer_nick,
      </if>
      <if test="created != null" >
        created,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="wareId != null" >
        ware_id,
      </if>
      <if test="payment != null" >
        payment,
      </if>
      <if test="sellerDiscount != null" >
        seller_discount,
      </if>
      <if test="totalFee != null" >
        total_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="itemSkuId != null" >
        #{itemSkuId,jdbcType=VARCHAR},
      </if>
      <if test="itemPrice != null" >
        #{itemPrice,jdbcType=DOUBLE},
      </if>
      <if test="itemNum != null" >
        #{itemNum,jdbcType=INTEGER},
      </if>
      <if test="buyerNick != null" >
        #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="created != null" >
        #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="wareId != null" >
        #{wareId,jdbcType=BIGINT},
      </if>
      <if test="payment != null" >
        #{payment,jdbcType=DOUBLE},
      </if>
      <if test="sellerDiscount != null" >
        #{sellerDiscount,jdbcType=DOUBLE},
      </if>
      <if test="totalFee != null" >
        #{totalFee,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.model.DTO.OrderCancelGoodsSkuDTO" >
    update pes_order_cancel_goods_sku_2019_04
    <set >
      <if test="orderId != null" >
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null" >
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="itemSkuId != null" >
        item_sku_id = #{itemSkuId,jdbcType=VARCHAR},
      </if>
      <if test="itemPrice != null" >
        item_price = #{itemPrice,jdbcType=DOUBLE},
      </if>
      <if test="itemNum != null" >
        item_num = #{itemNum,jdbcType=INTEGER},
      </if>
      <if test="buyerNick != null" >
        buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      </if>
      <if test="created != null" >
        created = #{created,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="wareId != null" >
        ware_id = #{wareId,jdbcType=BIGINT},
      </if>
      <if test="payment != null" >
        payment = #{payment,jdbcType=DOUBLE},
      </if>
      <if test="sellerDiscount != null" >
        seller_discount = #{sellerDiscount,jdbcType=DOUBLE},
      </if>
      <if test="totalFee != null" >
        total_fee = #{totalFee,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.model.DTO.OrderCancelGoodsSkuDTO" >
    update pes_order_cancel_goods_sku_2019_04
    set order_id = #{orderId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      item_sku_id = #{itemSkuId,jdbcType=VARCHAR},
      item_price = #{itemPrice,jdbcType=DOUBLE},
      item_num = #{itemNum,jdbcType=INTEGER},
      buyer_nick = #{buyerNick,jdbcType=VARCHAR},
      created = #{created,jdbcType=TIMESTAMP},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      ware_id = #{wareId,jdbcType=BIGINT},
      payment = #{payment,jdbcType=DOUBLE},
      seller_discount = #{sellerDiscount,jdbcType=DOUBLE},
      total_fee = #{totalFee,jdbcType=DOUBLE}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>