<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
  <title>客服魔方</title>
  <style>
    html,
    body {
      height: 100%;
    }

    body {
      font-size: 16px;
    }

    body,
    img,
    p {
      margin: 0;
    }

    .container {
      display: flex;
      flex-direction: column;
      min-height: 100%;
      /* justify-content: space-evenly; */
      align-items: stretch;
      font-family: sans-serif;
      /* padding: 30px 0; */
      transition: transform .5s, opacity 1s;
    }
    .container.hidden {
      opacity: 0;
      /* transform: translateY(50px); */
      transform: scale(.9);
    }

    .loading-overlay {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      transition: opacity .5s;
    }
    .loading-overlay.hidden {
      opacity: 0;
    }

    .container > div {
      text-align: center;
    }

    .container > div:first-child,
    .container > div:last-child {
      margin-top: auto;
      margin-bottom: auto;
    }

    .first-time-use .title-img,
    .pc-instructions .title-img {
      width: 41.07%;
    }
    .first-time-use .desc,
    .pc-instructions .desc {
      /* font-size: 3.2vw; */
      margin-top: 1em;
      margin-bottom: 1.5em;
    }

    .pc-instructions {
      margin-bottom: 2em;
    }
    .pc-instructions .support-img {
      width: 24.13vw;
      margin-bottom: 1em;
    }

    .tips {
      /* font-size: 24px; */
      /* width: 62.67vw; */
      /* min-height: 100px; */
      height: 150px;
      padding: 0 40px;
      width: 280px;
      line-height: 1.5;
      align-self: center;
      background-color: #e6f0fe;
      border: 1px solid #2174fb;
      border-radius: 20px;
    }
    .tips-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .tips-title .img-wrap {
      display: inline;
      line-height: 24px;
    }
    .tips-title img {
      width: 50px;
      vertical-align: -40%;
    }

    .pc-steps {
      position: relative;
      text-align: left;
      margin: 0 auto;
      max-width: 500px;
      width: 86%;
      padding-left: 30px;
    }
    .pc-steps::before {
      content: '';
      display: block;
      position: absolute;
      left: 9px;
      top: 0;
      bottom: 0;
      border-left: 2px dashed #2174fb;
    }
    .steps-title {
      /* font-size: 25px; */
      font-weight: bold;
      margin-bottom: 0.5em;
    }
    .steps-title::before {
      content: '';
      position: absolute;
      width: 14px;
      height: 14px;
      margin-top: 3px;
      background-color: #2174fb;
      transform: rotate(45deg);
      transform-origin: 0 0;
      left: 10px;
    }
    .steps-title .number {
      color: #ffae01;
      margin-right: 10px;
    }
    .steps-content {
      margin-bottom: 2em;
    }
    .pc-step-2 .steps-content {
      margin-left: 33px;
    }
    .pc-step-3 .steps-content img {
      max-width: 100%;
    }

    .auth-toast {
      position: fixed;
      top: 0;
      line-height: 42px;
      background-color: #67c23a;
      color: #fff;
      text-align: center;
      left: 0;
      right: 0;
      transition: all 0.5s;
    }
    .auth-toast.hidden {
      top: -42px;
    }
  </style>
</head>

<body>
  <div class="loading-overlay">
    <div><img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/loading.gif" alt="Loading"></div>
    <div>正在进入客服魔方</div>
  </div>
  <div class="container hidden">
    <div class="first-time-use">
      <div>
        <img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/first_time_use.png" alt="首次使用" class="title-img">
      </div>
      <div class="desc">请先登录电脑端客服魔方完成初始化配置 </div>
    </div>
    <div class="pc-instructions">
      <div>
        <img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/pc_setting.png" alt="PC操作路径" class="title-img">
      </div>
      <div class="desc">遇到问题，请联系专属客服协助</div>
      <div>
        <a href="http://chat.jd.com/pop/MTAwNzUyNjkx" target="_blank">
          <img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/online_support.png" alt="在线客服" class="support-img">
        </a>
      </div>
      <div class="pc-steps">
        <div class="pc-steps-item pc-step-1" style="height: 3em;">
          <div class="steps-title">
            <span class="number">01</span>
            <span>打开【京东服务市场】</span>
          </div>
        </div>
        <div class="pc-steps-item pc-step-2">
          <div class="steps-title">
            <span class="number">02</span>
            <span>点击【我的服务】</span>
          </div>
          <div class="steps-content">
            <img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/my_services.png" height="35" alt="我的服务">
          </div>
        </div>
        <div class="pc-steps-item pc-step-3">
          <div class="steps-title">
            <span class="number">03</span>
            <span>点击【客服魔方】，立即使用</span>
          </div>
          <div class="steps-content">
            <img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/use_mofang.png" alt="客服魔方">
          </div>
        </div>
      </div>
    </div>
    <div class="tips">
      <div class="tips-title">
        <div class="img-wrap"><img src="http://oss.wkefu.com/jixiao/joyi/img/mobile/bulb.png" alt="icon"></div>
        <span>移动端仅展示基础店铺数据</span></div>
      <div>
        更多高级功能：如催拍催付、客服数据报表以及客服数据的更多相关设置请至<span style="color: red">[电脑端]</span>商家后台操作 
      </div>
    </div>
  </div>

  <div class="auth-toast hidden">授权成功</div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 假的加载动画和toast
      let $overlay = document.querySelector('.loading-overlay')
      function fadeInContent() {
          $overlay.style.display = 'none'
          document.querySelector('.auth-toast').classList.remove('hidden')
          document.querySelector('.container').classList.remove('hidden')
          setTimeout(function() {
            document.querySelector('.auth-toast').classList.add('hidden')
          }, 2000)
      }
      setTimeout(function() {
        $overlay.addEventListener('transitionend', fadeInContent)
        $overlay.classList.add('hidden')
      }, 2000)
    })
  </script>
</body>

</html>