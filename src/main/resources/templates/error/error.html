<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link rel="apple-touch-icon" sizes="76x76" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <title>error</title>
    <style>
      html,
      body {
        height: 100%;
      }

      body,
      img,
      p {
        margin: 0;
      }

      .container {
        display: table;
        width: 800px;
        height: 100%;
        margin: 0 auto;
        font-family: "微软雅黑", sans-serif;
      }

      .img-wrap {
        display: table-cell;
        vertical-align: middle;
      }

      .img {
        float: left;
        width: 258px;
        margin-right: 50px;
      }

      .error-title {
        font-weight: bold;
        font-size: 28px;
        color: #000000;
        margin-top: 70px;
        margin-bottom: 10px;
      }
      .error-detail {
        font-size: 14px;
        color: #000000;
      }

      @media (max-width: 480px) {
        .container {
          width: 100%;
        }
        .img {
          float: none;
          max-width: 100%;
          margin-right: 0;
        }
        .img-wrap {
          text-align: center;
        }
        .error-title {
          font-size: 18px;
          margin-top: 30px;
        }
      }
    </style>
  </head>
  <body
    th:attr="data-nick=${result.nick}, data-shop-id=${result.shopId}, data-open-id=${result.openId}, data-err-msg=${result.errMsg}"
  >
    <div class="container">
      <div class="img-wrap">
        <img class="img" src="/images/error.png" alt="访问出现错误" />
        <p class="error-title" th:utext="${result.errMsg}">
          非常抱歉，访问出现错误
        </p>
        <p class="error-detail" th:utext="${result.msg}">
          如需帮助请联系在线客服
        </p>
      </div>
    </div>
    <script>
      !(function() {
        window.pin = document.body.dataset.openId;
        var nick = document.body.dataset.nick,
          shopId = document.body.dataset.shopId,
          errMsg = document.body.dataset.errMsg;
        // 宙斯日志
        window.JOS_LOGGER = {
          shouldUploadLog: window.pin && nick,
          init: function() {
            if (!this.shouldUploadLog) return;
            var self = this;

            setTimeout(function() {
              // try {
              getJdEid(function(eid, fp, udfp) {
                self.uploadLog(eid);
              });
              // } catch (e) {
              //   // 异常处理
              // }
            }, 1000);
          },
          uploadLog: function(deviceId) {
            var xhr = new XMLHttpRequest();
            var paramObj = {
              code: 1,
              msg: errMsg,
              loginTime: Date.now(),
              nick: nick,
              shopId: shopId,
              deviceId: deviceId
            };
            var paramArr = [];
            for (var key in paramObj) {
              paramArr.push(key + "=" + encodeURIComponent(paramObj[key]));
            }
            xhr.open("POST", "/web-report/login/uploadLoginLogForPlugin");
            xhr.setRequestHeader(
              "Content-Type",
              "application/x-www-form-urlencoded"
            );
            xhr.send(paramArr.join("&"));
          }
        };
      })();
    </script>
    <script src="//gias.jd.com/js/td.js"></script>
    <script>
      JOS_LOGGER.init();
    </script>
  </body>
</html>
