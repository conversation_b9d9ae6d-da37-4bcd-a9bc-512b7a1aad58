
                你的任务是对电商客服对话进行意图分类，需要严格按照给定的流程和规则执行。

                预定义的分类标签列表如下：

                <分类标签>

                {category_list}

                </分类标签>

                分类规则和步骤如下：

                1. 完整阅读整个对话，识别客户的核心诉求和最终解决的问题类型。

                2. 重点关注客户首次提问和最终确认的内容，兼顾客服的响应类型。

                3. 若对话涉及多个分类标签，按以下优先级排序：

                    (1) 按照分类标签列表顺序依次对比（从"尺码推荐"到"补差价"）。

                    (2) 当存在并列优先级时，按对话中讨论时间最长的问题归类。

                4. 仅当内容明显不属于所有预定义标签时使用"其他"标签。

                5. 每个分类标签最多出现一次，必须来自预定义列表。

                6. cust_content标签中不要出现空值。

                7. tag_desc标签中展示用户的详细意图15字以内

                8. 如果tag为 其他 则tag_desc中如果无法判断 则填写 无

                9. 只允许存在唯一chatlog_id, 且chatlog_id标签中不要出现多个id

                以下是jsonl文件数据:

                <jsonl_data>

                {chat_text}

                </jsonl_data>

                请在<results>标签中按以下格式输出：

                <results>

                <result>

                <chatlog_id>jsonL文件对应的id，唯一</chatlog_id>

                <cust_content>jsonL中customer的对话内容 只需要customer的意图</cust_content>

                <cs_content>jsonL中customer_service的对话内容 接在customer对话后的第一句</cs_content>

                <tag>首要分类标签</tag>

                <tag_desc>用户详细的意图，15个字以内</tag_desc>

                </result>

                <result>

                    <chatlog_id>jsonL文件对应的id</chatlog_id>

                    <cust_content>jsonL中customer的对话内容 只需要customer的意图</cust_content>

                    <cs_content>jsonL中customer_service的对话内容 接在customer对话后的第一句</cs_content>

                    <tag>次要分类标签</tag>

                    <tag_desc>用户详细的意图，15个字以内</tag_desc>

                </result>

                <result>

                    <chatlog_id>jsonL文件对应的id</chatlog_id>

                    <cust_content>jsonL中customer的对话内容 只需要customer的意图</cust_content>

                    <cs_content>jsonL中customer_service的对话内容 接在customer对话后的第一句</cs_content>

                    <tag>第三类分类标签</tag>

                    <tag_desc>用户详细的意图，15个字以内</tag_desc>

                </result>

                </results>

                请确保：

                - 严格使用预定义标签，不添加新标签。

                - 输出仅包含XML标签内容，不添加解释。

                - 至少输出1个，最多3个分类标签。

                - 标签名称与列表完全一致。

                - 按优先级顺序排列标签。

                - 不要出现 <chatlog_id>1310459,1310460,1310461,1310465</chatlog_id> 这样的数据
