<?xml version="1.0" encoding="UTF-8" ?>
    <!DOCTYPE generatorConfiguration 
      PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
      "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<!--数据库驱动路径 -->
	<context id="mysql" targetRuntime="MyBatis3">
		<commentGenerator>
			<property name="suppressDate" value="true"/>
			<!-- 是否去除自动生成的注释 true：是 ： false:否 -->
			<property name="suppressAllComments" value="true"/>
		</commentGenerator>

		<!--数据库链接URL，用户名、密码 -->
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
			connectionURL="******************************************" userId="wangmeng"
			password="Ywc202004">
		</jdbcConnection>
		
		<javaTypeResolver>
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>

		<!-- 生成模型的包名和位置-->
		<javaModelGenerator targetPackage="com.pes.jd.generate" targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!--XML映射文件,生成的位置（目标包）,源代码文件夹 -->
		<sqlMapGenerator targetPackage="generate"  targetProject="src/main/resources">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<!-- 生成DAO的包名和XML位置-->
		<javaClientGenerator type="XMLMAPPER" targetPackage="com.pes.jd.generate" targetProject="src/main/java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<!-- 要生成的表 tableName是数据库中的表名或视图名 domainObjectName是实体类名-->
		<table tableName="pes_job_cal_record_2019" domainObjectName="JobCalRecordDO" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>

	</context>

</generatorConfiguration>