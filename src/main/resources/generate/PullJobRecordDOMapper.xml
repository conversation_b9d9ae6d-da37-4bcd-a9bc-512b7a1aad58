<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.generate.PullJobRecordDOMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.generate.PullJobRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="chat_peer_flag" jdbcType="BIT" property="chatPeerFlag" />
    <result column="shop_category_flag" jdbcType="BIT" property="shopCategoryFlag" />
    <result column="shop_sku_flag" jdbcType="BIT" property="shopSkuFlag" />
    <result column="shop_good_flag" jdbcType="BIT" property="shopGoodFlag" />
    <result column="shop_dsr_flag" jdbcType="BIT" property="shopDsrFlag" />
    <result column="no_pay_order_flag" jdbcType="BIT" property="noPayOrderFlag" />
    <result column="order_created_flag" jdbcType="BIT" property="orderCreatedFlag" />
    <result column="order_modify_flag" jdbcType="BIT" property="orderModifyFlag" />
    <result column="order_presale_flag" jdbcType="BIT" property="orderPresaleFlag" />
    <result column="order_evaluation_flag" jdbcType="BIT" property="orderEvaluationFlag" />
    <result column="shop_pv_uv_flag" jdbcType="BIT" property="shopPvUvFlag" />
    <result column="order_refund_apply_flag" jdbcType="BIT" property="orderRefundApplyFlag" />
    <result column="order_refund_check_flag" jdbcType="BIT" property="orderRefundCheckFlag" />
    <result column="asc_order_refund_apply_flag" jdbcType="BIT" property="ascOrderRefundApplyFlag" />
    <result column="asc_order_refund_check_flag" jdbcType="BIT" property="ascOrderRefundCheckFlag" />
    <result column="leave_msg_flag" jdbcType="BIT" property="leaveMsgFlag" />
    <result column="cs_send_eval_flag" jdbcType="BIT" property="csSendEvalFlag" />
    <result column="update_cs_eval_flag" jdbcType="BIT" property="updateCsEvalFlag" />
    <result column="cs_eval_flag" jdbcType="BIT" property="csEvalFlag" />
    <result column="order_remark_flag" jdbcType="BIT" property="orderRemarkFlag" />
    <result column="result" jdbcType="BIT" property="result" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, date, chat_peer_flag, shop_category_flag, shop_sku_flag, shop_good_flag, 
    shop_dsr_flag, no_pay_order_flag, order_created_flag, order_modify_flag, order_presale_flag, 
    order_evaluation_flag, shop_pv_uv_flag, order_refund_apply_flag, order_refund_check_flag, 
    asc_order_refund_apply_flag, asc_order_refund_check_flag, leave_msg_flag, cs_send_eval_flag, 
    update_cs_eval_flag, cs_eval_flag, order_remark_flag, result
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_pull_job_record_2019
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_pull_job_record_2019
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.pes.jd.generate.PullJobRecordDO">
    insert into pes_pull_job_record_2019 (id, shop_id, date, 
      chat_peer_flag, shop_category_flag, shop_sku_flag, 
      shop_good_flag, shop_dsr_flag, no_pay_order_flag, 
      order_created_flag, order_modify_flag, order_presale_flag, 
      order_evaluation_flag, shop_pv_uv_flag, order_refund_apply_flag, 
      order_refund_check_flag, asc_order_refund_apply_flag, 
      asc_order_refund_check_flag, leave_msg_flag, cs_send_eval_flag, 
      update_cs_eval_flag, cs_eval_flag, order_remark_flag, 
      result)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{chatPeerFlag,jdbcType=BIT}, #{shopCategoryFlag,jdbcType=BIT}, #{shopSkuFlag,jdbcType=BIT}, 
      #{shopGoodFlag,jdbcType=BIT}, #{shopDsrFlag,jdbcType=BIT}, #{noPayOrderFlag,jdbcType=BIT}, 
      #{orderCreatedFlag,jdbcType=BIT}, #{orderModifyFlag,jdbcType=BIT}, #{orderPresaleFlag,jdbcType=BIT}, 
      #{orderEvaluationFlag,jdbcType=BIT}, #{shopPvUvFlag,jdbcType=BIT}, #{orderRefundApplyFlag,jdbcType=BIT}, 
      #{orderRefundCheckFlag,jdbcType=BIT}, #{ascOrderRefundApplyFlag,jdbcType=BIT}, 
      #{ascOrderRefundCheckFlag,jdbcType=BIT}, #{leaveMsgFlag,jdbcType=BIT}, #{csSendEvalFlag,jdbcType=BIT}, 
      #{updateCsEvalFlag,jdbcType=BIT}, #{csEvalFlag,jdbcType=BIT}, #{orderRemarkFlag,jdbcType=BIT}, 
      #{result,jdbcType=BIT})
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.pes.jd.generate.PullJobRecordDO">
    update pes_pull_job_record_2019
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      chat_peer_flag = #{chatPeerFlag,jdbcType=BIT},
      shop_category_flag = #{shopCategoryFlag,jdbcType=BIT},
      shop_sku_flag = #{shopSkuFlag,jdbcType=BIT},
      shop_good_flag = #{shopGoodFlag,jdbcType=BIT},
      shop_dsr_flag = #{shopDsrFlag,jdbcType=BIT},
      no_pay_order_flag = #{noPayOrderFlag,jdbcType=BIT},
      order_created_flag = #{orderCreatedFlag,jdbcType=BIT},
      order_modify_flag = #{orderModifyFlag,jdbcType=BIT},
      order_presale_flag = #{orderPresaleFlag,jdbcType=BIT},
      order_evaluation_flag = #{orderEvaluationFlag,jdbcType=BIT},
      shop_pv_uv_flag = #{shopPvUvFlag,jdbcType=BIT},
      order_refund_apply_flag = #{orderRefundApplyFlag,jdbcType=BIT},
      order_refund_check_flag = #{orderRefundCheckFlag,jdbcType=BIT},
      asc_order_refund_apply_flag = #{ascOrderRefundApplyFlag,jdbcType=BIT},
      asc_order_refund_check_flag = #{ascOrderRefundCheckFlag,jdbcType=BIT},
      leave_msg_flag = #{leaveMsgFlag,jdbcType=BIT},
      cs_send_eval_flag = #{csSendEvalFlag,jdbcType=BIT},
      update_cs_eval_flag = #{updateCsEvalFlag,jdbcType=BIT},
      cs_eval_flag = #{csEvalFlag,jdbcType=BIT},
      order_remark_flag = #{orderRemarkFlag,jdbcType=BIT},
      result = #{result,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>