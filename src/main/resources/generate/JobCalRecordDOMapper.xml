<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pes.jd.generate.JobCalRecordDOMapper">
  <resultMap id="BaseResultMap" type="com.pes.jd.generate.JobCalRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="common_chat_flag" jdbcType="BIT" property="commonChatFlag" />
    <result column="receive_quality_flag" jdbcType="BIT" property="receiveQualityFlag" />
    <result column="cs_order_index_flag" jdbcType="BIT" property="csOrderIndexFlag" />
    <result column="cs_order_bind_flag" jdbcType="BIT" property="csOrderBindFlag" />
    <result column="enquiry_chat_flag" jdbcType="BIT" property="enquiryChatFlag" />
    <result column="final_chat_data_flag" jdbcType="BIT" property="finalChatDataFlag" />
    <result column="enquiry_loss_flag" jdbcType="BIT" property="enquiryLossFlag" />
    <result column="cs_performance_flag" jdbcType="BIT" property="csPerformanceFlag" />
    <result column="cs_torder_performance" jdbcType="BIT" property="csTorderPerformance" />
    <result column="shop_day_overview_flag" jdbcType="BIT" property="shopDayOverviewFlag" />
    <result column="assit_index_flag" jdbcType="BIT" property="assitIndexFlag" />
    <result column="order_filte_flag" jdbcType="BIT" property="orderFilteFlag" />
    <result column="order_loss_flag" jdbcType="BIT" property="orderLossFlag" />
    <result column="outstock_loss_flag" jdbcType="BIT" property="outstockLossFlag" />
    <result column="team_loss_flag" jdbcType="BIT" property="teamLossFlag" />
    <result column="cs_order_eval_flag" jdbcType="BIT" property="csOrderEvalFlag" />
    <result column="cs_order_bind_index_flag" jdbcType="BIT" property="csOrderBindIndexFlag" />
    <result column="cs_goods_handle_flag" jdbcType="BIT" property="csGoodsHandleFlag" />
    <result column="cs_goods_sum_flag" jdbcType="BIT" property="csGoodsSumFlag" />
    <result column="cs_slient_sale_flag" jdbcType="BIT" property="csSlientSaleFlag" />
    <result column="cs_silent_goods_sum_flag" jdbcType="BIT" property="csSilentGoodsSumFlag" />
    <result column="team_day_refund_flag" jdbcType="BIT" property="teamDayRefundFlag" />
    <result column="shop_day_refund_flag" jdbcType="BIT" property="shopDayRefundFlag" />
    <result column="modified" jdbcType="TIMESTAMP" property="modified" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="consume_time" jdbcType="BIGINT" property="consumeTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, date, result, common_chat_flag, receive_quality_flag, cs_order_index_flag, 
    cs_order_bind_flag, enquiry_chat_flag, final_chat_data_flag, enquiry_loss_flag, cs_performance_flag, 
    cs_torder_performance, shop_day_overview_flag, assit_index_flag, order_filte_flag, 
    order_loss_flag, outstock_loss_flag, team_loss_flag, cs_order_eval_flag, cs_order_bind_index_flag, 
    cs_goods_handle_flag, cs_goods_sum_flag, cs_slient_sale_flag, cs_silent_goods_sum_flag, 
    team_day_refund_flag, shop_day_refund_flag, modified, msg, consume_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_job_cal_record_2019
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_job_cal_record_2019
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.generate.JobCalRecordDO">
    insert into pes_job_cal_record_2019 (id, shop_id, date, 
      result, common_chat_flag, receive_quality_flag, 
      cs_order_index_flag, cs_order_bind_flag, enquiry_chat_flag, 
      final_chat_data_flag, enquiry_loss_flag, cs_performance_flag, 
      cs_torder_performance, shop_day_overview_flag, assit_index_flag, 
      order_filte_flag, order_loss_flag, outstock_loss_flag, 
      team_loss_flag, cs_order_eval_flag, cs_order_bind_index_flag, 
      cs_goods_handle_flag, cs_goods_sum_flag, cs_slient_sale_flag, 
      cs_silent_goods_sum_flag, team_day_refund_flag, shop_day_refund_flag, 
      modified, msg, consume_time
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{result,jdbcType=BIT}, #{commonChatFlag,jdbcType=BIT}, #{receiveQualityFlag,jdbcType=BIT}, 
      #{csOrderIndexFlag,jdbcType=BIT}, #{csOrderBindFlag,jdbcType=BIT}, #{enquiryChatFlag,jdbcType=BIT}, 
      #{finalChatDataFlag,jdbcType=BIT}, #{enquiryLossFlag,jdbcType=BIT}, #{csPerformanceFlag,jdbcType=BIT}, 
      #{csTorderPerformance,jdbcType=BIT}, #{shopDayOverviewFlag,jdbcType=BIT}, #{assitIndexFlag,jdbcType=BIT}, 
      #{orderFilteFlag,jdbcType=BIT}, #{orderLossFlag,jdbcType=BIT}, #{outstockLossFlag,jdbcType=BIT}, 
      #{teamLossFlag,jdbcType=BIT}, #{csOrderEvalFlag,jdbcType=BIT}, #{csOrderBindIndexFlag,jdbcType=BIT}, 
      #{csGoodsHandleFlag,jdbcType=BIT}, #{csGoodsSumFlag,jdbcType=BIT}, #{csSlientSaleFlag,jdbcType=BIT}, 
      #{csSilentGoodsSumFlag,jdbcType=BIT}, #{teamDayRefundFlag,jdbcType=BIT}, #{shopDayRefundFlag,jdbcType=BIT}, 
      #{modified,jdbcType=TIMESTAMP}, #{msg,jdbcType=VARCHAR}, #{consumeTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.generate.JobCalRecordDO">
    insert into pes_job_cal_record_2019
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="commonChatFlag != null">
        common_chat_flag,
      </if>
      <if test="receiveQualityFlag != null">
        receive_quality_flag,
      </if>
      <if test="csOrderIndexFlag != null">
        cs_order_index_flag,
      </if>
      <if test="csOrderBindFlag != null">
        cs_order_bind_flag,
      </if>
      <if test="enquiryChatFlag != null">
        enquiry_chat_flag,
      </if>
      <if test="finalChatDataFlag != null">
        final_chat_data_flag,
      </if>
      <if test="enquiryLossFlag != null">
        enquiry_loss_flag,
      </if>
      <if test="csPerformanceFlag != null">
        cs_performance_flag,
      </if>
      <if test="csTorderPerformance != null">
        cs_torder_performance,
      </if>
      <if test="shopDayOverviewFlag != null">
        shop_day_overview_flag,
      </if>
      <if test="assitIndexFlag != null">
        assit_index_flag,
      </if>
      <if test="orderFilteFlag != null">
        order_filte_flag,
      </if>
      <if test="orderLossFlag != null">
        order_loss_flag,
      </if>
      <if test="outstockLossFlag != null">
        outstock_loss_flag,
      </if>
      <if test="teamLossFlag != null">
        team_loss_flag,
      </if>
      <if test="csOrderEvalFlag != null">
        cs_order_eval_flag,
      </if>
      <if test="csOrderBindIndexFlag != null">
        cs_order_bind_index_flag,
      </if>
      <if test="csGoodsHandleFlag != null">
        cs_goods_handle_flag,
      </if>
      <if test="csGoodsSumFlag != null">
        cs_goods_sum_flag,
      </if>
      <if test="csSlientSaleFlag != null">
        cs_slient_sale_flag,
      </if>
      <if test="csSilentGoodsSumFlag != null">
        cs_silent_goods_sum_flag,
      </if>
      <if test="teamDayRefundFlag != null">
        team_day_refund_flag,
      </if>
      <if test="shopDayRefundFlag != null">
        shop_day_refund_flag,
      </if>
      <if test="modified != null">
        modified,
      </if>
      <if test="msg != null">
        msg,
      </if>
      <if test="consumeTime != null">
        consume_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="result != null">
        #{result,jdbcType=BIT},
      </if>
      <if test="commonChatFlag != null">
        #{commonChatFlag,jdbcType=BIT},
      </if>
      <if test="receiveQualityFlag != null">
        #{receiveQualityFlag,jdbcType=BIT},
      </if>
      <if test="csOrderIndexFlag != null">
        #{csOrderIndexFlag,jdbcType=BIT},
      </if>
      <if test="csOrderBindFlag != null">
        #{csOrderBindFlag,jdbcType=BIT},
      </if>
      <if test="enquiryChatFlag != null">
        #{enquiryChatFlag,jdbcType=BIT},
      </if>
      <if test="finalChatDataFlag != null">
        #{finalChatDataFlag,jdbcType=BIT},
      </if>
      <if test="enquiryLossFlag != null">
        #{enquiryLossFlag,jdbcType=BIT},
      </if>
      <if test="csPerformanceFlag != null">
        #{csPerformanceFlag,jdbcType=BIT},
      </if>
      <if test="csTorderPerformance != null">
        #{csTorderPerformance,jdbcType=BIT},
      </if>
      <if test="shopDayOverviewFlag != null">
        #{shopDayOverviewFlag,jdbcType=BIT},
      </if>
      <if test="assitIndexFlag != null">
        #{assitIndexFlag,jdbcType=BIT},
      </if>
      <if test="orderFilteFlag != null">
        #{orderFilteFlag,jdbcType=BIT},
      </if>
      <if test="orderLossFlag != null">
        #{orderLossFlag,jdbcType=BIT},
      </if>
      <if test="outstockLossFlag != null">
        #{outstockLossFlag,jdbcType=BIT},
      </if>
      <if test="teamLossFlag != null">
        #{teamLossFlag,jdbcType=BIT},
      </if>
      <if test="csOrderEvalFlag != null">
        #{csOrderEvalFlag,jdbcType=BIT},
      </if>
      <if test="csOrderBindIndexFlag != null">
        #{csOrderBindIndexFlag,jdbcType=BIT},
      </if>
      <if test="csGoodsHandleFlag != null">
        #{csGoodsHandleFlag,jdbcType=BIT},
      </if>
      <if test="csGoodsSumFlag != null">
        #{csGoodsSumFlag,jdbcType=BIT},
      </if>
      <if test="csSlientSaleFlag != null">
        #{csSlientSaleFlag,jdbcType=BIT},
      </if>
      <if test="csSilentGoodsSumFlag != null">
        #{csSilentGoodsSumFlag,jdbcType=BIT},
      </if>
      <if test="teamDayRefundFlag != null">
        #{teamDayRefundFlag,jdbcType=BIT},
      </if>
      <if test="shopDayRefundFlag != null">
        #{shopDayRefundFlag,jdbcType=BIT},
      </if>
      <if test="modified != null">
        #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="msg != null">
        #{msg,jdbcType=VARCHAR},
      </if>
      <if test="consumeTime != null">
        #{consumeTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.generate.JobCalRecordDO">
    update pes_job_cal_record_2019
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=BIT},
      </if>
      <if test="commonChatFlag != null">
        common_chat_flag = #{commonChatFlag,jdbcType=BIT},
      </if>
      <if test="receiveQualityFlag != null">
        receive_quality_flag = #{receiveQualityFlag,jdbcType=BIT},
      </if>
      <if test="csOrderIndexFlag != null">
        cs_order_index_flag = #{csOrderIndexFlag,jdbcType=BIT},
      </if>
      <if test="csOrderBindFlag != null">
        cs_order_bind_flag = #{csOrderBindFlag,jdbcType=BIT},
      </if>
      <if test="enquiryChatFlag != null">
        enquiry_chat_flag = #{enquiryChatFlag,jdbcType=BIT},
      </if>
      <if test="finalChatDataFlag != null">
        final_chat_data_flag = #{finalChatDataFlag,jdbcType=BIT},
      </if>
      <if test="enquiryLossFlag != null">
        enquiry_loss_flag = #{enquiryLossFlag,jdbcType=BIT},
      </if>
      <if test="csPerformanceFlag != null">
        cs_performance_flag = #{csPerformanceFlag,jdbcType=BIT},
      </if>
      <if test="csTorderPerformance != null">
        cs_torder_performance = #{csTorderPerformance,jdbcType=BIT},
      </if>
      <if test="shopDayOverviewFlag != null">
        shop_day_overview_flag = #{shopDayOverviewFlag,jdbcType=BIT},
      </if>
      <if test="assitIndexFlag != null">
        assit_index_flag = #{assitIndexFlag,jdbcType=BIT},
      </if>
      <if test="orderFilteFlag != null">
        order_filte_flag = #{orderFilteFlag,jdbcType=BIT},
      </if>
      <if test="orderLossFlag != null">
        order_loss_flag = #{orderLossFlag,jdbcType=BIT},
      </if>
      <if test="outstockLossFlag != null">
        outstock_loss_flag = #{outstockLossFlag,jdbcType=BIT},
      </if>
      <if test="teamLossFlag != null">
        team_loss_flag = #{teamLossFlag,jdbcType=BIT},
      </if>
      <if test="csOrderEvalFlag != null">
        cs_order_eval_flag = #{csOrderEvalFlag,jdbcType=BIT},
      </if>
      <if test="csOrderBindIndexFlag != null">
        cs_order_bind_index_flag = #{csOrderBindIndexFlag,jdbcType=BIT},
      </if>
      <if test="csGoodsHandleFlag != null">
        cs_goods_handle_flag = #{csGoodsHandleFlag,jdbcType=BIT},
      </if>
      <if test="csGoodsSumFlag != null">
        cs_goods_sum_flag = #{csGoodsSumFlag,jdbcType=BIT},
      </if>
      <if test="csSlientSaleFlag != null">
        cs_slient_sale_flag = #{csSlientSaleFlag,jdbcType=BIT},
      </if>
      <if test="csSilentGoodsSumFlag != null">
        cs_silent_goods_sum_flag = #{csSilentGoodsSumFlag,jdbcType=BIT},
      </if>
      <if test="teamDayRefundFlag != null">
        team_day_refund_flag = #{teamDayRefundFlag,jdbcType=BIT},
      </if>
      <if test="shopDayRefundFlag != null">
        shop_day_refund_flag = #{shopDayRefundFlag,jdbcType=BIT},
      </if>
      <if test="modified != null">
        modified = #{modified,jdbcType=TIMESTAMP},
      </if>
      <if test="msg != null">
        msg = #{msg,jdbcType=VARCHAR},
      </if>
      <if test="consumeTime != null">
        consume_time = #{consumeTime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.generate.JobCalRecordDO">
    update pes_job_cal_record_2019
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      result = #{result,jdbcType=BIT},
      common_chat_flag = #{commonChatFlag,jdbcType=BIT},
      receive_quality_flag = #{receiveQualityFlag,jdbcType=BIT},
      cs_order_index_flag = #{csOrderIndexFlag,jdbcType=BIT},
      cs_order_bind_flag = #{csOrderBindFlag,jdbcType=BIT},
      enquiry_chat_flag = #{enquiryChatFlag,jdbcType=BIT},
      final_chat_data_flag = #{finalChatDataFlag,jdbcType=BIT},
      enquiry_loss_flag = #{enquiryLossFlag,jdbcType=BIT},
      cs_performance_flag = #{csPerformanceFlag,jdbcType=BIT},
      cs_torder_performance = #{csTorderPerformance,jdbcType=BIT},
      shop_day_overview_flag = #{shopDayOverviewFlag,jdbcType=BIT},
      assit_index_flag = #{assitIndexFlag,jdbcType=BIT},
      order_filte_flag = #{orderFilteFlag,jdbcType=BIT},
      order_loss_flag = #{orderLossFlag,jdbcType=BIT},
      outstock_loss_flag = #{outstockLossFlag,jdbcType=BIT},
      team_loss_flag = #{teamLossFlag,jdbcType=BIT},
      cs_order_eval_flag = #{csOrderEvalFlag,jdbcType=BIT},
      cs_order_bind_index_flag = #{csOrderBindIndexFlag,jdbcType=BIT},
      cs_goods_handle_flag = #{csGoodsHandleFlag,jdbcType=BIT},
      cs_goods_sum_flag = #{csGoodsSumFlag,jdbcType=BIT},
      cs_slient_sale_flag = #{csSlientSaleFlag,jdbcType=BIT},
      cs_silent_goods_sum_flag = #{csSilentGoodsSumFlag,jdbcType=BIT},
      team_day_refund_flag = #{teamDayRefundFlag,jdbcType=BIT},
      shop_day_refund_flag = #{shopDayRefundFlag,jdbcType=BIT},
      modified = #{modified,jdbcType=TIMESTAMP},
      msg = #{msg,jdbcType=VARCHAR},
      consume_time = #{consumeTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <resultMap id="BaseResultMap" type="com.pes.jd.generate.JobCalRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="result" jdbcType="BIT" property="result" />
    <result column="common_chat_flag" jdbcType="BIT" property="commonChatFlag" />
    <result column="receive_quality_flag" jdbcType="BIT" property="receiveQualityFlag" />
    <result column="cs_order_index_flag" jdbcType="BIT" property="csOrderIndexFlag" />
    <result column="cs_order_bind_flag" jdbcType="BIT" property="csOrderBindFlag" />
    <result column="enquiry_chat_flag" jdbcType="BIT" property="enquiryChatFlag" />
    <result column="final_chat_data_flag" jdbcType="BIT" property="finalChatDataFlag" />
    <result column="enquiry_loss_flag" jdbcType="BIT" property="enquiryLossFlag" />
    <result column="cs_performance_flag" jdbcType="BIT" property="csPerformanceFlag" />
    <result column="cs_torder_performance" jdbcType="BIT" property="csTorderPerformance" />
    <result column="shop_day_overview_flag" jdbcType="BIT" property="shopDayOverviewFlag" />
    <result column="assit_index_flag" jdbcType="BIT" property="assitIndexFlag" />
    <result column="order_filte_flag" jdbcType="BIT" property="orderFilteFlag" />
    <result column="handle_slien_sale_flag" jdbcType="BIT" property="handleSlienSaleFlag" />
    <result column="order_loss_flag" jdbcType="BIT" property="orderLossFlag" />
    <result column="outstock_loss_flag" jdbcType="BIT" property="outstockLossFlag" />
    <result column="team_loss_flag" jdbcType="BIT" property="teamLossFlag" />
    <result column="cs_order_eval_flag" jdbcType="BIT" property="csOrderEvalFlag" />
    <result column="cs_order_bind_index_flag" jdbcType="BIT" property="csOrderBindIndexFlag" />
    <result column="cs_goods_handle_flag" jdbcType="BIT" property="csGoodsHandleFlag" />
    <result column="cs_goods_sum_flag" jdbcType="BIT" property="csGoodsSumFlag" />
    <result column="cs_silent_goods_sum_flag" jdbcType="BIT" property="csSilentGoodsSumFlag" />
    <result column="team_day_refund_flag" jdbcType="BIT" property="teamDayRefundFlag" />
    <result column="shop_day_refund_flag" jdbcType="BIT" property="shopDayRefundFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, date, result, common_chat_flag, receive_quality_flag, cs_order_index_flag, 
    cs_order_bind_flag, enquiry_chat_flag, final_chat_data_flag, enquiry_loss_flag, cs_performance_flag, 
    cs_torder_performance, shop_day_overview_flag, assit_index_flag, order_filte_flag, 
    handle_slien_sale_flag, order_loss_flag, outstock_loss_flag, team_loss_flag, cs_order_eval_flag, 
    cs_order_bind_index_flag, cs_goods_handle_flag, cs_goods_sum_flag, cs_silent_goods_sum_flag, 
    team_day_refund_flag, shop_day_refund_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_job_cal_record_2019
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_job_cal_record_2019
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.generate.JobCalRecordDO">
    insert into pes_job_cal_record_2019 (id, shop_id, date, 
      result, common_chat_flag, receive_quality_flag, 
      cs_order_index_flag, cs_order_bind_flag, enquiry_chat_flag, 
      final_chat_data_flag, enquiry_loss_flag, cs_performance_flag, 
      cs_torder_performance, shop_day_overview_flag, assit_index_flag, 
      order_filte_flag, handle_slien_sale_flag, order_loss_flag, 
      outstock_loss_flag, team_loss_flag, cs_order_eval_flag, 
      cs_order_bind_index_flag, cs_goods_handle_flag, cs_goods_sum_flag, 
      cs_silent_goods_sum_flag, team_day_refund_flag, shop_day_refund_flag
      )
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{result,jdbcType=BIT}, #{commonChatFlag,jdbcType=BIT}, #{receiveQualityFlag,jdbcType=BIT}, 
      #{csOrderIndexFlag,jdbcType=BIT}, #{csOrderBindFlag,jdbcType=BIT}, #{enquiryChatFlag,jdbcType=BIT}, 
      #{finalChatDataFlag,jdbcType=BIT}, #{enquiryLossFlag,jdbcType=BIT}, #{csPerformanceFlag,jdbcType=BIT}, 
      #{csTorderPerformance,jdbcType=BIT}, #{shopDayOverviewFlag,jdbcType=BIT}, #{assitIndexFlag,jdbcType=BIT}, 
      #{orderFilteFlag,jdbcType=BIT}, #{handleSlienSaleFlag,jdbcType=BIT}, #{orderLossFlag,jdbcType=BIT}, 
      #{outstockLossFlag,jdbcType=BIT}, #{teamLossFlag,jdbcType=BIT}, #{csOrderEvalFlag,jdbcType=BIT}, 
      #{csOrderBindIndexFlag,jdbcType=BIT}, #{csGoodsHandleFlag,jdbcType=BIT}, #{csGoodsSumFlag,jdbcType=BIT}, 
      #{csSilentGoodsSumFlag,jdbcType=BIT}, #{teamDayRefundFlag,jdbcType=BIT}, #{shopDayRefundFlag,jdbcType=BIT}
      )
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.generate.JobCalRecordDO">
    update pes_job_cal_record_2019
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=BIT},
      </if>
      <if test="commonChatFlag != null">
        common_chat_flag = #{commonChatFlag,jdbcType=BIT},
      </if>
      <if test="receiveQualityFlag != null">
        receive_quality_flag = #{receiveQualityFlag,jdbcType=BIT},
      </if>
      <if test="csOrderIndexFlag != null">
        cs_order_index_flag = #{csOrderIndexFlag,jdbcType=BIT},
      </if>
      <if test="csOrderBindFlag != null">
        cs_order_bind_flag = #{csOrderBindFlag,jdbcType=BIT},
      </if>
      <if test="enquiryChatFlag != null">
        enquiry_chat_flag = #{enquiryChatFlag,jdbcType=BIT},
      </if>
      <if test="finalChatDataFlag != null">
        final_chat_data_flag = #{finalChatDataFlag,jdbcType=BIT},
      </if>
      <if test="enquiryLossFlag != null">
        enquiry_loss_flag = #{enquiryLossFlag,jdbcType=BIT},
      </if>
      <if test="csPerformanceFlag != null">
        cs_performance_flag = #{csPerformanceFlag,jdbcType=BIT},
      </if>
      <if test="csTorderPerformance != null">
        cs_torder_performance = #{csTorderPerformance,jdbcType=BIT},
      </if>
      <if test="shopDayOverviewFlag != null">
        shop_day_overview_flag = #{shopDayOverviewFlag,jdbcType=BIT},
      </if>
      <if test="assitIndexFlag != null">
        assit_index_flag = #{assitIndexFlag,jdbcType=BIT},
      </if>
      <if test="orderFilteFlag != null">
        order_filte_flag = #{orderFilteFlag,jdbcType=BIT},
      </if>
      <if test="handleSlienSaleFlag != null">
        handle_slien_sale_flag = #{handleSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="orderLossFlag != null">
        order_loss_flag = #{orderLossFlag,jdbcType=BIT},
      </if>
      <if test="outstockLossFlag != null">
        outstock_loss_flag = #{outstockLossFlag,jdbcType=BIT},
      </if>
      <if test="teamLossFlag != null">
        team_loss_flag = #{teamLossFlag,jdbcType=BIT},
      </if>
      <if test="csOrderEvalFlag != null">
        cs_order_eval_flag = #{csOrderEvalFlag,jdbcType=BIT},
      </if>
      <if test="csOrderBindIndexFlag != null">
        cs_order_bind_index_flag = #{csOrderBindIndexFlag,jdbcType=BIT},
      </if>
      <if test="csGoodsHandleFlag != null">
        cs_goods_handle_flag = #{csGoodsHandleFlag,jdbcType=BIT},
      </if>
      <if test="csGoodsSumFlag != null">
        cs_goods_sum_flag = #{csGoodsSumFlag,jdbcType=BIT},
      </if>
      <if test="csSilentGoodsSumFlag != null">
        cs_silent_goods_sum_flag = #{csSilentGoodsSumFlag,jdbcType=BIT},
      </if>
      <if test="teamDayRefundFlag != null">
        team_day_refund_flag = #{teamDayRefundFlag,jdbcType=BIT},
      </if>
      <if test="shopDayRefundFlag != null">
        shop_day_refund_flag = #{shopDayRefundFlag,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.generate.JobCalRecordDO">
    update pes_job_cal_record_2019
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      result = #{result,jdbcType=BIT},
      common_chat_flag = #{commonChatFlag,jdbcType=BIT},
      receive_quality_flag = #{receiveQualityFlag,jdbcType=BIT},
      cs_order_index_flag = #{csOrderIndexFlag,jdbcType=BIT},
      cs_order_bind_flag = #{csOrderBindFlag,jdbcType=BIT},
      enquiry_chat_flag = #{enquiryChatFlag,jdbcType=BIT},
      final_chat_data_flag = #{finalChatDataFlag,jdbcType=BIT},
      enquiry_loss_flag = #{enquiryLossFlag,jdbcType=BIT},
      cs_performance_flag = #{csPerformanceFlag,jdbcType=BIT},
      cs_torder_performance = #{csTorderPerformance,jdbcType=BIT},
      shop_day_overview_flag = #{shopDayOverviewFlag,jdbcType=BIT},
      assit_index_flag = #{assitIndexFlag,jdbcType=BIT},
      order_filte_flag = #{orderFilteFlag,jdbcType=BIT},
      handle_slien_sale_flag = #{handleSlienSaleFlag,jdbcType=BIT},
      order_loss_flag = #{orderLossFlag,jdbcType=BIT},
      outstock_loss_flag = #{outstockLossFlag,jdbcType=BIT},
      team_loss_flag = #{teamLossFlag,jdbcType=BIT},
      cs_order_eval_flag = #{csOrderEvalFlag,jdbcType=BIT},
      cs_order_bind_index_flag = #{csOrderBindIndexFlag,jdbcType=BIT},
      cs_goods_handle_flag = #{csGoodsHandleFlag,jdbcType=BIT},
      cs_goods_sum_flag = #{csGoodsSumFlag,jdbcType=BIT},
      cs_silent_goods_sum_flag = #{csSilentGoodsSumFlag,jdbcType=BIT},
      team_day_refund_flag = #{teamDayRefundFlag,jdbcType=BIT},
      shop_day_refund_flag = #{shopDayRefundFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <resultMap id="BaseResultMap" type="com.pes.jd.generate.JobCalRecordDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="handle_chat_flag" jdbcType="BIT" property="handleChatFlag" />
    <result column="handle_receive_quality_flag" jdbcType="BIT" property="handleReceiveQualityFlag" />
    <result column="handle_csorder_index_flag" jdbcType="BIT" property="handleCsorderIndexFlag" />
    <result column="handle_shop_cs_bind_flag" jdbcType="BIT" property="handleShopCsBindFlag" />
    <result column="handle_enquiry_chat_flag" jdbcType="BIT" property="handleEnquiryChatFlag" />
    <result column="handle_cs_performance_flag" jdbcType="BIT" property="handleCsPerformanceFlag" />
    <result column="handle_csperformance_fororder" jdbcType="BIT" property="handleCsperformanceFororder" />
    <result column="handle_shopday_overview_flag" jdbcType="BIT" property="handleShopdayOverviewFlag" />
    <result column="handle_assit_index_flag" jdbcType="BIT" property="handleAssitIndexFlag" />
    <result column="handle_goods_cal_flag" jdbcType="BIT" property="handleGoodsCalFlag" />
    <result column="handle_sum_goods_cal_flag" jdbcType="BIT" property="handleSumGoodsCalFlag" />
    <result column="handle_slien_sale_flag" jdbcType="BIT" property="handleSlienSaleFlag" />
    <result column="handle_sum_slien_sale_flag" jdbcType="BIT" property="handleSumSlienSaleFlag" />
    <result column="order_filter_falg" jdbcType="BIT" property="orderFilterFalg" />
    <result column="handle_order_loss_flag" jdbcType="BIT" property="handleOrderLossFlag" />
    <result column="handle_enquiry_loss_flag" jdbcType="BIT" property="handleEnquiryLossFlag" />
    <result column="handle_enquiry_order_loss_flag" jdbcType="BIT" property="handleEnquiryOrderLossFlag" />
    <result column="handle_silent_order_loss_flag" jdbcType="BIT" property="handleSilentOrderLossFlag" />
    <result column="handle_order_outstock_loss_flag" jdbcType="BIT" property="handleOrderOutstockLossFlag" />
    <result column="handle_cs_loss_flag" jdbcType="BIT" property="handleCsLossFlag" />
    <result column="handle_order_eval_bind_flag" jdbcType="BIT" property="handleOrderEvalBindFlag" />
    <result column="handle_shopcs_order_bind_flag" jdbcType="BIT" property="handleShopcsOrderBindFlag" />
    <result column="result" jdbcType="BIT" property="result" />
  </resultMap>

  <sql id="Base_Column_List">
    id, shop_id, date, handle_chat_flag, handle_receive_quality_flag, handle_csorder_index_flag, 
    handle_shop_cs_bind_flag, handle_enquiry_chat_flag, handle_cs_performance_flag, handle_csperformance_fororder, 
    handle_shopday_overview_flag, handle_assit_index_flag, handle_goods_cal_flag, handle_sum_goods_cal_flag, 
    handle_slien_sale_flag, handle_sum_slien_sale_flag, order_filter_falg, handle_order_loss_flag, 
    handle_enquiry_loss_flag, handle_enquiry_order_loss_flag, handle_silent_order_loss_flag, 
    handle_order_outstock_loss_flag, handle_cs_loss_flag, handle_order_eval_bind_flag, 
    handle_shopcs_order_bind_flag, result
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pes_job_cal_record_2019
    where id = #{id,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pes_job_cal_record_2019
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.pes.jd.generate.JobCalRecordDO">
    insert into pes_job_cal_record_2019 (id, shop_id, date, 
      handle_chat_flag, handle_receive_quality_flag, handle_csorder_index_flag, 
      handle_shop_cs_bind_flag, handle_enquiry_chat_flag, handle_cs_performance_flag, 
      handle_csperformance_fororder, handle_shopday_overview_flag, 
      handle_assit_index_flag, handle_goods_cal_flag, handle_sum_goods_cal_flag, 
      handle_slien_sale_flag, handle_sum_slien_sale_flag, order_filter_falg, 
      handle_order_loss_flag, handle_enquiry_loss_flag, handle_enquiry_order_loss_flag, 
      handle_silent_order_loss_flag, handle_order_outstock_loss_flag, 
      handle_cs_loss_flag, handle_order_eval_bind_flag, handle_shopcs_order_bind_flag, 
      result)
    values (#{id,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{date,jdbcType=DATE}, 
      #{handleChatFlag,jdbcType=BIT}, #{handleReceiveQualityFlag,jdbcType=BIT}, #{handleCsorderIndexFlag,jdbcType=BIT}, 
      #{handleShopCsBindFlag,jdbcType=BIT}, #{handleEnquiryChatFlag,jdbcType=BIT}, #{handleCsPerformanceFlag,jdbcType=BIT}, 
      #{handleCsperformanceFororder,jdbcType=BIT}, #{handleShopdayOverviewFlag,jdbcType=BIT}, 
      #{handleAssitIndexFlag,jdbcType=BIT}, #{handleGoodsCalFlag,jdbcType=BIT}, #{handleSumGoodsCalFlag,jdbcType=BIT}, 
      #{handleSlienSaleFlag,jdbcType=BIT}, #{handleSumSlienSaleFlag,jdbcType=BIT}, #{orderFilterFalg,jdbcType=BIT}, 
      #{handleOrderLossFlag,jdbcType=BIT}, #{handleEnquiryLossFlag,jdbcType=BIT}, #{handleEnquiryOrderLossFlag,jdbcType=BIT}, 
      #{handleSilentOrderLossFlag,jdbcType=BIT}, #{handleOrderOutstockLossFlag,jdbcType=BIT}, 
      #{handleCsLossFlag,jdbcType=BIT}, #{handleOrderEvalBindFlag,jdbcType=BIT}, #{handleShopcsOrderBindFlag,jdbcType=BIT}, 
      #{result,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.pes.jd.generate.JobCalRecordDO">
    insert into pes_job_cal_record_2019
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="date != null">
        date,
      </if>
      <if test="handleChatFlag != null">
        handle_chat_flag,
      </if>
      <if test="handleReceiveQualityFlag != null">
        handle_receive_quality_flag,
      </if>
      <if test="handleCsorderIndexFlag != null">
        handle_csorder_index_flag,
      </if>
      <if test="handleShopCsBindFlag != null">
        handle_shop_cs_bind_flag,
      </if>
      <if test="handleEnquiryChatFlag != null">
        handle_enquiry_chat_flag,
      </if>
      <if test="handleCsPerformanceFlag != null">
        handle_cs_performance_flag,
      </if>
      <if test="handleCsperformanceFororder != null">
        handle_csperformance_fororder,
      </if>
      <if test="handleShopdayOverviewFlag != null">
        handle_shopday_overview_flag,
      </if>
      <if test="handleAssitIndexFlag != null">
        handle_assit_index_flag,
      </if>
      <if test="handleGoodsCalFlag != null">
        handle_goods_cal_flag,
      </if>
      <if test="handleSumGoodsCalFlag != null">
        handle_sum_goods_cal_flag,
      </if>
      <if test="handleSlienSaleFlag != null">
        handle_slien_sale_flag,
      </if>
      <if test="handleSumSlienSaleFlag != null">
        handle_sum_slien_sale_flag,
      </if>
      <if test="orderFilterFalg != null">
        order_filter_falg,
      </if>
      <if test="handleOrderLossFlag != null">
        handle_order_loss_flag,
      </if>
      <if test="handleEnquiryLossFlag != null">
        handle_enquiry_loss_flag,
      </if>
      <if test="handleEnquiryOrderLossFlag != null">
        handle_enquiry_order_loss_flag,
      </if>
      <if test="handleSilentOrderLossFlag != null">
        handle_silent_order_loss_flag,
      </if>
      <if test="handleOrderOutstockLossFlag != null">
        handle_order_outstock_loss_flag,
      </if>
      <if test="handleCsLossFlag != null">
        handle_cs_loss_flag,
      </if>
      <if test="handleOrderEvalBindFlag != null">
        handle_order_eval_bind_flag,
      </if>
      <if test="handleShopcsOrderBindFlag != null">
        handle_shopcs_order_bind_flag,
      </if>
      <if test="result != null">
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="handleChatFlag != null">
        #{handleChatFlag,jdbcType=BIT},
      </if>
      <if test="handleReceiveQualityFlag != null">
        #{handleReceiveQualityFlag,jdbcType=BIT},
      </if>
      <if test="handleCsorderIndexFlag != null">
        #{handleCsorderIndexFlag,jdbcType=BIT},
      </if>
      <if test="handleShopCsBindFlag != null">
        #{handleShopCsBindFlag,jdbcType=BIT},
      </if>
      <if test="handleEnquiryChatFlag != null">
        #{handleEnquiryChatFlag,jdbcType=BIT},
      </if>
      <if test="handleCsPerformanceFlag != null">
        #{handleCsPerformanceFlag,jdbcType=BIT},
      </if>
      <if test="handleCsperformanceFororder != null">
        #{handleCsperformanceFororder,jdbcType=BIT},
      </if>
      <if test="handleShopdayOverviewFlag != null">
        #{handleShopdayOverviewFlag,jdbcType=BIT},
      </if>
      <if test="handleAssitIndexFlag != null">
        #{handleAssitIndexFlag,jdbcType=BIT},
      </if>
      <if test="handleGoodsCalFlag != null">
        #{handleGoodsCalFlag,jdbcType=BIT},
      </if>
      <if test="handleSumGoodsCalFlag != null">
        #{handleSumGoodsCalFlag,jdbcType=BIT},
      </if>
      <if test="handleSlienSaleFlag != null">
        #{handleSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="handleSumSlienSaleFlag != null">
        #{handleSumSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="orderFilterFalg != null">
        #{orderFilterFalg,jdbcType=BIT},
      </if>
      <if test="handleOrderLossFlag != null">
        #{handleOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="handleEnquiryLossFlag != null">
        #{handleEnquiryLossFlag,jdbcType=BIT},
      </if>
      <if test="handleEnquiryOrderLossFlag != null">
        #{handleEnquiryOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="handleSilentOrderLossFlag != null">
        #{handleSilentOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="handleOrderOutstockLossFlag != null">
        #{handleOrderOutstockLossFlag,jdbcType=BIT},
      </if>
      <if test="handleCsLossFlag != null">
        #{handleCsLossFlag,jdbcType=BIT},
      </if>
      <if test="handleOrderEvalBindFlag != null">
        #{handleOrderEvalBindFlag,jdbcType=BIT},
      </if>
      <if test="handleShopcsOrderBindFlag != null">
        #{handleShopcsOrderBindFlag,jdbcType=BIT},
      </if>
      <if test="result != null">
        #{result,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pes.jd.generate.JobCalRecordDO">
    update pes_job_cal_record_2019
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="handleChatFlag != null">
        handle_chat_flag = #{handleChatFlag,jdbcType=BIT},
      </if>
      <if test="handleReceiveQualityFlag != null">
        handle_receive_quality_flag = #{handleReceiveQualityFlag,jdbcType=BIT},
      </if>
      <if test="handleCsorderIndexFlag != null">
        handle_csorder_index_flag = #{handleCsorderIndexFlag,jdbcType=BIT},
      </if>
      <if test="handleShopCsBindFlag != null">
        handle_shop_cs_bind_flag = #{handleShopCsBindFlag,jdbcType=BIT},
      </if>
      <if test="handleEnquiryChatFlag != null">
        handle_enquiry_chat_flag = #{handleEnquiryChatFlag,jdbcType=BIT},
      </if>
      <if test="handleCsPerformanceFlag != null">
        handle_cs_performance_flag = #{handleCsPerformanceFlag,jdbcType=BIT},
      </if>
      <if test="handleCsperformanceFororder != null">
        handle_csperformance_fororder = #{handleCsperformanceFororder,jdbcType=BIT},
      </if>
      <if test="handleShopdayOverviewFlag != null">
        handle_shopday_overview_flag = #{handleShopdayOverviewFlag,jdbcType=BIT},
      </if>
      <if test="handleAssitIndexFlag != null">
        handle_assit_index_flag = #{handleAssitIndexFlag,jdbcType=BIT},
      </if>
      <if test="handleGoodsCalFlag != null">
        handle_goods_cal_flag = #{handleGoodsCalFlag,jdbcType=BIT},
      </if>
      <if test="handleSumGoodsCalFlag != null">
        handle_sum_goods_cal_flag = #{handleSumGoodsCalFlag,jdbcType=BIT},
      </if>
      <if test="handleSlienSaleFlag != null">
        handle_slien_sale_flag = #{handleSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="handleSumSlienSaleFlag != null">
        handle_sum_slien_sale_flag = #{handleSumSlienSaleFlag,jdbcType=BIT},
      </if>
      <if test="orderFilterFalg != null">
        order_filter_falg = #{orderFilterFalg,jdbcType=BIT},
      </if>
      <if test="handleOrderLossFlag != null">
        handle_order_loss_flag = #{handleOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="handleEnquiryLossFlag != null">
        handle_enquiry_loss_flag = #{handleEnquiryLossFlag,jdbcType=BIT},
      </if>
      <if test="handleEnquiryOrderLossFlag != null">
        handle_enquiry_order_loss_flag = #{handleEnquiryOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="handleSilentOrderLossFlag != null">
        handle_silent_order_loss_flag = #{handleSilentOrderLossFlag,jdbcType=BIT},
      </if>
      <if test="handleOrderOutstockLossFlag != null">
        handle_order_outstock_loss_flag = #{handleOrderOutstockLossFlag,jdbcType=BIT},
      </if>
      <if test="handleCsLossFlag != null">
        handle_cs_loss_flag = #{handleCsLossFlag,jdbcType=BIT},
      </if>
      <if test="handleOrderEvalBindFlag != null">
        handle_order_eval_bind_flag = #{handleOrderEvalBindFlag,jdbcType=BIT},
      </if>
      <if test="handleShopcsOrderBindFlag != null">
        handle_shopcs_order_bind_flag = #{handleShopcsOrderBindFlag,jdbcType=BIT},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pes.jd.generate.JobCalRecordDO">
    update pes_job_cal_record_2019
    set shop_id = #{shopId,jdbcType=BIGINT},
      date = #{date,jdbcType=DATE},
      handle_chat_flag = #{handleChatFlag,jdbcType=BIT},
      handle_receive_quality_flag = #{handleReceiveQualityFlag,jdbcType=BIT},
      handle_csorder_index_flag = #{handleCsorderIndexFlag,jdbcType=BIT},
      handle_shop_cs_bind_flag = #{handleShopCsBindFlag,jdbcType=BIT},
      handle_enquiry_chat_flag = #{handleEnquiryChatFlag,jdbcType=BIT},
      handle_cs_performance_flag = #{handleCsPerformanceFlag,jdbcType=BIT},
      handle_csperformance_fororder = #{handleCsperformanceFororder,jdbcType=BIT},
      handle_shopday_overview_flag = #{handleShopdayOverviewFlag,jdbcType=BIT},
      handle_assit_index_flag = #{handleAssitIndexFlag,jdbcType=BIT},
      handle_goods_cal_flag = #{handleGoodsCalFlag,jdbcType=BIT},
      handle_sum_goods_cal_flag = #{handleSumGoodsCalFlag,jdbcType=BIT},
      handle_slien_sale_flag = #{handleSlienSaleFlag,jdbcType=BIT},
      handle_sum_slien_sale_flag = #{handleSumSlienSaleFlag,jdbcType=BIT},
      order_filter_falg = #{orderFilterFalg,jdbcType=BIT},
      handle_order_loss_flag = #{handleOrderLossFlag,jdbcType=BIT},
      handle_enquiry_loss_flag = #{handleEnquiryLossFlag,jdbcType=BIT},
      handle_enquiry_order_loss_flag = #{handleEnquiryOrderLossFlag,jdbcType=BIT},
      handle_silent_order_loss_flag = #{handleSilentOrderLossFlag,jdbcType=BIT},
      handle_order_outstock_loss_flag = #{handleOrderOutstockLossFlag,jdbcType=BIT},
      handle_cs_loss_flag = #{handleCsLossFlag,jdbcType=BIT},
      handle_order_eval_bind_flag = #{handleOrderEvalBindFlag,jdbcType=BIT},
      handle_shopcs_order_bind_flag = #{handleShopcsOrderBindFlag,jdbcType=BIT},
      result = #{result,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>