<?xml version="1.0" encoding="UTF-8" ?>
    <!DOCTYPE generatorConfiguration 
      PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
      "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<!--数据库驱动路径 -->
	<context id="mysql" targetRuntime="MyBatis3">
		<commentGenerator>
			<property name="suppressAllComments" value="true" />
		</commentGenerator>
		
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
			connectionURL="***********************************" userId="wangmeng"
			password="Ywc202004">
		</jdbcConnection>
		
		<javaTypeResolver>
			<property name="forceBigDecimals" value="false" />
		</javaTypeResolver>
		
		<!--域模型层,生成的目标包,项目目标源文件 -->
		<javaModelGenerator targetPackage="com.pes.jd.generate"
			targetProject="D:\Developer\Git Codebase\YiYi\Gitee\ms-pes-jd-family\ms-pes-jd-master-provider\src\main\java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!--XML映射文件,生成的位置（目标包）,源代码文件夹 -->
		<sqlMapGenerator targetPackage="com.pes.jd.generate"
			targetProject="D:\Developer\Git Codebase\YiYi\Gitee\ms-pes-jd-family\ms-pes-jd-master-provider\src\main\java">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>
		
		<!--XML对应的Mapper类 -->
		<javaClientGenerator type="XMLMAPPER"
			targetPackage="com.pes.jd.generate" 
			targetProject="D:\Developer\Git Codebase\YiYi\Gitee\ms-pes-jd-family\ms-pes-jd-master-provider\src\main\java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>
		<!--下面是数据库表名和项目中需要生成类的名称，建议和数据库保持一致，如果有多个表，添加多个节点即可 -->
		<table tableName="pes_shop"
		domainObjectName="pesShopDTO"  enableSelectByPrimaryKey="true" enableInsert="true"
		enableUpdateByPrimaryKey="true" enableDeleteByPrimaryKey="true">
		</table>
		
	</context>

</generatorConfiguration>