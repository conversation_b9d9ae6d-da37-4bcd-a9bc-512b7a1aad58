spring.cloud.nacos.config.server-addr=${yiyitech.config.server-addr}

spring.application.name=${yiyitech.app.name}
spring.cloud.nacos.config.prefix=${yiyitech.prefix.name}
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.config.enabled=true
## 服务配置动态刷新
spring.cloud.nacos.config.refresh-enabled=true

## namespace区分不同的项目
spring.cloud.nacos.config.namespace=${yiyitech.config.namespace}
## group区分项目的不同环境
spring.cloud.nacos.config.group=${yiyitech.config.group}
## 共享配置文件配置
spring.cloud.nacos.config.ext-config[0].data-id=register.properties
spring.cloud.nacos.config.ext-config[0].group=${yiyitech.register.name}
spring.cloud.nacos.config.ext-config[0].refresh=true

spring.cloud.nacos.config.ext-config[1].data-id=app.properties
spring.cloud.nacos.config.ext-config[1].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[1].refresh=true

spring.cloud.nacos.config.ext-config[2].data-id=log.properties
spring.cloud.nacos.config.ext-config[2].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[2].refresh=true

spring.cloud.nacos.config.ext-config[3].data-id=redis.properties
spring.cloud.nacos.config.ext-config[3].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[3].refresh=true

spring.cloud.nacos.config.ext-config[4].data-id=logback.xml
spring.cloud.nacos.config.ext-config[4].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[4].refresh=true

spring.cloud.nacos.config.ext-config[5].data-id=kafka.properties
spring.cloud.nacos.config.ext-config[5].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[5].refresh=true
