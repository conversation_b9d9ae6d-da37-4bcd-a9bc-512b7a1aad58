spring.cloud.nacos.config.server-addr=${yiyitech.config.server-addr}

spring.application.name=${yiyitech.app.name}
spring.cloud.nacos.config.prefix=${yiyitech.prefix.name}
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.config.enabled=true
## æå¡éç½®å¨æå·æ°
spring.cloud.nacos.config.refresh-enabled=true

## namespaceåºåä¸åçé¡¹ç®
spring.cloud.nacos.config.namespace=${yiyitech.config.namespace}
## groupåºåé¡¹ç®çä¸åç¯å¢
spring.cloud.nacos.config.group=${yiyitech.config.group}
## å±äº«éç½®æä»¶éç½®
spring.cloud.nacos.config.ext-config[0].data-id=register.properties
spring.cloud.nacos.config.ext-config[0].group=${yiyitech.register.name}
spring.cloud.nacos.config.ext-config[0].refresh=true

spring.cloud.nacos.config.ext-config[1].data-id=app.properties
spring.cloud.nacos.config.ext-config[1].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[1].refresh=true

spring.cloud.nacos.config.ext-config[2].data-id=log.properties
spring.cloud.nacos.config.ext-config[2].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[2].refresh=true

spring.cloud.nacos.config.ext-config[3].data-id=redis.properties
spring.cloud.nacos.config.ext-config[3].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[3].refresh=true

spring.cloud.nacos.config.ext-config[4].data-id=logback.xml
spring.cloud.nacos.config.ext-config[4].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[4].refresh=true
