mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

yiyitech.app.name=insight.job
yiyitech.prefix.name=insight-job

yiyitech.config.server-addr=localhost:8848
yiyitech.config.namespace=6182abea-c20c-4002-8d50-4b226a836c7b
yiyitech.config.group=prod
yiyitech.register.name=prod
yiyitech.cluster.name=default

yiyitech.datasource.enabled=true
yiyitech.ds.names=db1,db2

yiyitech.db1.username=test_ro
yiyitech.db1.password=Test!qaz234
yiyitech.db1.url=***********************************************************************************************************************************************************************************************************
yiyitech.db1.driver=com.mysql.jdbc.Driver

yiyitech.db2.username=test_ro
yiyitech.db2.password=Test!qaz234
yiyitech.db2.url=***********************************************************************************************************************************************************************************************************
yiyitech.db2.driver=com.mysql.jdbc.Driver

spring.cloud.nacos.config.server-addr=${yiyitech.config.server-addr}

spring.application.name=${yiyitech.app.name}
spring.cloud.nacos.config.prefix=${yiyitech.prefix.name}
spring.cloud.nacos.config.file-extension=properties
spring.cloud.nacos.config.enabled=true
spring.cloud.nacos.config.refresh-enabled=true

spring.cloud.nacos.config.namespace=${yiyitech.config.namespace}
spring.cloud.nacos.config.group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[0].data-id=register.properties
spring.cloud.nacos.config.ext-config[0].group=${yiyitech.register.name}
spring.cloud.nacos.config.ext-config[0].refresh=true

spring.cloud.nacos.config.ext-config[1].data-id=app.properties
spring.cloud.nacos.config.ext-config[1].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[1].refresh=true

spring.cloud.nacos.config.ext-config[2].data-id=log.properties
spring.cloud.nacos.config.ext-config[2].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[2].refresh=true

spring.cloud.nacos.config.ext-config[3].data-id=redis.properties
spring.cloud.nacos.config.ext-config[3].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[3].refresh=true

spring.cloud.nacos.config.ext-config[4].data-id=kafka.properties
spring.cloud.nacos.config.ext-config[4].group=${yiyitech.config.group}
spring.cloud.nacos.config.ext-config[4].refresh=true



