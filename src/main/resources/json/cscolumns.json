[{"field": "saleAmount", "detail": "客服销售额", "tableName": "pes_cs_performance", "type": "double"}, {"field": "saleOrderNum", "detail": "销售单数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "saleGoodsNum", "detail": "销售件数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "saleBuyerNum", "detail": "销售人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "perCusGoodsNum", "detail": "付款客件数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "perCusPrice", "detail": "客单价", "tableName": "pes_cs_performance", "type": "double"}, {"field": "orderedAmountToday", "detail": "当日接待当日下单金额", "tableName": "pes_cs_performance", "type": "double"}, {"field": "paidAmountToday", "detail": "当日接待当日付款金额", "tableName": "pes_cs_performance", "type": "double"}, {"field": "cfmGoodsAmount", "detail": "确认收货金额", "tableName": "pes_cs_performance", "type": "double"}, {"field": "enquiryNum", "detail": "咨询人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "receiveNum", "detail": "接待人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "orderedNumToday", "detail": "当日咨询当日下单人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "paidNumToday", "detail": "当日咨询当日付款人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "orderedNumFinal", "detail": "最终下单人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "finalPaidNum", "detail": "最终付款人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "enquiryToO<PERSON>red", "detail": "询单->下单转化率", "tableName": "pes_cs_performance", "type": "double"}, {"field": "orderedToPaid", "detail": "下单->付款转化率", "tableName": "pes_cs_performance", "type": "double"}, {"field": "enquiryToPaid", "detail": "询单->付款转化率", "tableName": "pes_cs_performance", "type": "double"}, {"field": "receiveRate", "detail": "接待占比", "tableName": "pes_cs_performance", "type": "double"}, {"field": "avgResponseTime", "detail": "首响时间", "tableName": "pes_cs_service_index", "type": "double"}, {"field": "avgWaitTime", "detail": "平响时间", "tableName": "pes_cs_service_index", "type": "double"}, {"field": "wordsNum", "detail": "客服字数", "tableName": "pes_cs_service_index", "type": "bigdecimal"}, {"field": "qaRate", "detail": "答问比", "tableName": "pes_cs_service_index", "type": "double"}, {"field": "noReplyNum", "detail": "未回复人数", "tableName": "pes_cs_service_index", "type": "bigdecimal"}, {"field": "recoveryRate", "detail": "回复率", "type": "double"}, {"field": "directReceiveNum", "detail": "直接接待人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "forwardInNum", "detail": "转入人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "forwardOutNum", "detail": "转出人数", "tableName": "pes_cs_performance", "type": "bigdecimal"}, {"field": "avgReplyNum", "detail": "平均回复数量", "tableName": "pes_cs_buyer_service_index", "type": "double"}, {"field": "avgSessionTime", "detail": "平均会话时长", "tableName": "pes_cs_buyer_service_index", "type": "double"}, {"field": "inviteCommentPercent", "detail": "邀评率", "tableName": "pes_cs_service_evaluation", "type": "double"}, {"field": "goodCommentPercent", "detail": "满意率", "tableName": "pes_cs_service_evaluation", "type": "double"}, {"field": "consultNum", "detail": "咨询量", "tableName": "pes_cs_performance", "type": "bigdecimal"}]