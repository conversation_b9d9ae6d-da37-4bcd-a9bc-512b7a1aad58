package com.pes.jd.dao.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.pes.jd.model.BO.ShopPerformanceAvg;
import com.pes.jd.model.Response.ApiResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: aiJun
 * @Date: 2020/12/3 9:23
 * @Version 1.0
 */
public class TestUploadData {
    public static final String AUTHORIZATION = "0d85952f9d4e750dbd9b5834ee83ec51";
    private static String popShop = "web-report";
    private static String selfShop = "report-self";


    public static void main(String[] args) {
        Map<Long, String> map = initPopData();
        for (Map.Entry<Long, String> entry : map.entrySet()) {
            uploadData(entry.getKey(), entry.getValue(), popShop);
        }
    }


    private static void uploadData(Long shopId, String title, String shopType) {
        System.out.print(title);
        Map<String, Object> param = new HashMap<>();
        param.put("dateType", 1);
        param.put("shopId", shopId);
        String property = "consultPercent," +
                "shopDealPercent," +
                "csSaleAmountPercent," +
                "queryToFinalPaid," +
                "silenceDealPercent," +
                "quickResponseRate," +
                "avgRespTime," +
                "satisfactionRate";
        param.put("property", property);
        param.put("filterTime", null);
        param.put("startDate", "2020-11-01");
        param.put("endDate", "2020-11-30");
        param.put("Authorization", "0d85952f9d4e750dbd9b5834ee83ec51");
        //换取凭证
//获取凭证
        String result21 = HttpRequest.post("http://gwjoyi.yiyitech.com/" + shopType + "/admin/sysLoginEntrance")
                .header("Authorization", AUTHORIZATION)//头信息，多个头信息多次调用此方法即可
                .form(param)//表单内容
                .timeout(20000)//超时，毫秒
                .execute().body();
        String urlString = "http://gwjoyi.yiyitech.com/" + shopType + "/performance/shop/selectByProperty";
//获取数据
        String result2 = HttpRequest.post(urlString)
                .header("Authorization", AUTHORIZATION)//头信息，多个头信息多次调用此方法即可
                .form(param)//表单内容
                .timeout(20000)//超时，毫秒
                .execute().body();
        ApiResponse apiResponse = JSONObject.parseObject(result2, ApiResponse.class);
        Map<String, Object> data = apiResponse.getData();
        ShopPerformanceAvg str = JSONObject.parseObject(JSONObject.toJSONString(data.get("avg")), ShopPerformanceAvg.class);

        System.out.print("-->" + formatDouble(str.getConsultPercent(), 5) + "|||||");
        System.out.print(formatDouble(str.getShopDealPercent(), 5) + "||||");
        System.out.print(formatDouble(str.getCsSaleAmountPercent(), 5) + "|||&&");
        System.out.print(formatDouble(str.getQueryToFinalPaid(), 5) + "|||&&||");
        System.out.print(formatDouble(str.getSilenceDealPercent(), 5) + "-|||&&-");
        System.out.print(formatDouble(str.getQuickResponseRate(), 5) + "=|||&&=");
        System.out.print(formatDouble(str.getAvgRespTime(), 5) + "||");
        System.out.println(formatDouble(str.getSatisfactionRate(), 5));
    }

    /**
     * 保留指定小数数位
     *
     * @param d
     * @param place
     * @return
     */
    public static double formatDouble(double d, int place) {
        // 新方法，如果不需要四舍五入，可以使用RoundingMode.DOWN
        BigDecimal bg = new BigDecimal(d).setScale(place, RoundingMode.HALF_UP);
        return bg.doubleValue();
    }

    private static Map<Long, String> initPopData() {
        Map<Long, String> map = new TreeMap<>();
        map.put(56190L, "硕扬DIY电脑旗舰店");
        map.put(81477L, "武极电脑DIY旗舰店");
        map.put(97682L, "AFTERSHOKZ骨传导耳机旗舰店");
        map.put(112925L, "镭威视安防设备旗舰店");
        map.put(117534L, "千百回数码旗舰店");
        map.put(119849L, "微软升昌专卖店");
        map.put(120524L, "攀升DIY电脑旗舰店");
        map.put(120706L, "一品国度DIY旗舰店");
        map.put(143981L, "镭威视智能旗舰店");
        map.put(177868L, "E人E本平板旗舰店");
        map.put(186296L, "霸天安官方旗舰店");
        map.put(193662L, "RODE闪拍专卖店");
        map.put(197259L, "畅联数码专营店");
        map.put(202679L, "真帮办公专营店");
        map.put(204120L, "绿联数码旗舰店");
        map.put(585910L, "天景视安防专营店");
        map.put(598390L, "霸天安安防旗舰店");
        map.put(620574L, "茂双笔记本旗舰店");
        map.put(628361L, "科族数码专营店");
        map.put(630866L, "华硕电脑官方旗舰店");
        map.put(643818L, "戴尔商用地升专卖店");
        map.put(647755L, "Thunderobot雷神旗舰店");
        map.put(674765L, "汉步办公专营店");
        map.put(678347L, "京天DIY电脑旗舰店");
        map.put(681592L, "华为千百回专卖店");
        map.put(685447L, "志仕数码专营店");
        map.put(699317L, "阿思翠影音官方旗舰店");
        map.put(701931L, "虎牌集团官方专卖店");
        map.put(702219L, "联普影音娱乐专营店");
        map.put(733497L, "APPLE翔合专卖店");
        map.put(751749L, "山灵数码旗舰店");
        map.put(762861L, "惠普授权旗舰店");
        map.put(785811L, "宏码办公专营店");
        map.put(799794L, "dunu旗舰店");
        map.put(805781L, "千百回电脑专营店");
        map.put(807571L, "曼哈动力旗舰店");
        map.put(807819L, "机械革命旗舰店");
        map.put(837608L, "AFTERSHOKZ京东自营官方旗舰店");
        map.put(894150L, "宏量官方旗舰店");
        map.put(929051L, "神舟海神专卖店");
        map.put(936818L, "奇联京东自营旗舰店");
        map.put(940131L, "鑫晨星平板专营店");
        map.put(949992L, "烈驹旗舰店");
        map.put(984777L, "得胜旗舰店");
        map.put(995398L, "ThinkPad京东官方自营旗舰店");
        map.put(10021424L, "创新科技旗舰店");
        map.put(10094656L, "SMEG海外旗舰店");
        map.put(10124592L, "cleer数码旗舰店");
        map.put(10171401L, "金峰伊卡迪旗舰店");
        map.put(10178038L, "百乐越欣专卖店");
        map.put(10188843L, "华硕电脑京东自营官方旗舰店");
        map.put(10221304L, "疆界旗舰店");
        map.put(10261117L, "WOKE沃客数码海外官方旗舰店");
        map.put(10262671L, "QYS官方旗舰店");
        map.put(10265914L, "LEWITT莱维特旗舰店");
        map.put(10273907L, "DEVIALET京东自营旗舰店");
        map.put(10312401L, "七彩虹旗舰店");
        map.put(10327433L, "小壁虎智能设备专营店");
        map.put(10332221L, "罗技G官方自营旗舰店");
        map.put(10336124L, "cooyes笔记本官方旗舰店");
        return map;
    }

    private static Map<Long, String> initSelfData() {
        Map<Long, String> map = new TreeMap<>();
        map.put(1000000140L, "戴尔京东自营官方旗舰店");
        map.put(1000000142L, "天章京东自营官方旗舰店");
        map.put(1000000155L, "惠普京东自营官方旗舰店");
        map.put(1000000157L, "联想京东自营旗舰店");
        map.put(1000000158L, "ThinkPad京东自营旗舰店");
        map.put(1000000174L, "希捷Seagate京东自营旗舰店");
        map.put(1000000182L, "华硕京东自营官方旗舰店");
        map.put(1000000243L, "赛睿京东自营旗舰店");
        map.put(1000000267L, "七彩虹京东自营旗舰店");
        map.put(1000000287L, "七喜京东自营旗舰店");
        map.put(1000000307L, "JBL自营官方旗舰店");
        map.put(1000000326L, "微软京东自营官方旗舰店");
        map.put(1000000331L, "BOSE京东自营旗舰店");
        map.put(1000000400L, "航世京东自营旗舰店");
        map.put(1000000425L, "爱普生京东自营旗舰店");
        map.put(1000000437L, "明基显示器京东自营旗舰店");
        map.put(1000000492L, "绿巨能京东自营旗舰店");
        map.put(1000000557L, "明基投影京东自营旗舰店");
        map.put(1000000567L, "坚果投影京东自营官方旗舰店");
        map.put(1000000653L, "微星游戏本京东自营官方旗舰店");
        map.put(1000000680L, "仙视（Goodview）京东自营旗舰店");
        map.put(1000000714L, "威刚京东自营旗舰店");
        map.put(1000000750L, "秋叶原线材京东自营官方旗舰店");
        map.put(1000000834L, "360网络京东自营官方旗舰店");
        map.put(1000000866L, "360京东自营旗舰店");
        map.put(1000000921L, "SONY京东自营官方旗舰店");
        map.put(1000001129L, "松下投影京东自营官方旗舰店");
        map.put(1000001132L, "得力京东自营官方旗舰店");
        map.put(1000001143L, "晨光京东自营官方旗舰店");
        map.put(1000002065L, "HIFIMAN京东自营旗舰店");
        map.put(1000002717L, "绿联（UGREEN）京东自营旗舰店");
        map.put(1000003175L, "腾龙京东自营官方旗舰店");
        map.put(1000006061L, "比比牛京东自营官方旗舰店");
        map.put(1000007005L, "糖猫京东自营官方旗舰店");
        map.put(1000010645L, "兰士顿京东自营旗舰店");
        map.put(1000012543L, "AMAZFIT京东自营官方旗舰店");
        map.put(1000039982L, "全程通京东自营旗舰店");
        map.put(1000076225L, "ikbc京东自营旗舰店");
        map.put(1000081425L, "飞智京东自营旗舰店");
        map.put(1000091255L, "ZOWIE GEAR京东自营旗舰店");
        map.put(1000091598L, "IPASON攀升电脑京东自营旗舰店");
        map.put(1000100667L, "搜狗京东自营旗舰店");
        map.put(1000100771L, "纽赛京东自营官方旗舰店");
        map.put(1000101379L, "飞利浦PHILIPS商用显示器京东自营旗舰店");
        map.put(1000103558L, "海康威视京东自营旗舰店");
        map.put(1000107821L, "AOC商用显示京东自营旗舰店");
        map.put(1000111073L, "阿尔法蛋京东自营旗舰店");
        map.put(1000113002L, "英菲克（INPHIC）京东自营旗舰店");
        map.put(1000123061L, "JRC京东自营旗舰店");
        map.put(1000132001L, "宁美国度一体机京东自营旗舰店");
        map.put(1000139176L, "戴浦（DAIPU）京东自营旗舰店");
        map.put(1000147581L, "摩托罗拉（Motorola）数码京东自营旗舰店");
        map.put(1000167261L, "标拓京东自营旗舰店");
        map.put(1000196101L, "企业微信官方京东自营店");
        map.put(1000196865L, "首千（SHOCHAN）京东自营旗舰店");
        map.put(1000225401L, "牧士（MUSICSOOTH）京东自营旗舰店");
        map.put(1000225582L, "得力保险柜京东自营旗舰店");
        map.put(1000282141L, "吉屋轻智京东自营旗舰店");
        map.put(1000286150L, "雷蛇电脑京东自营旗舰店");
        map.put(1000334170L, "漾菲斯（YOUFES）京东自营旗舰店");
        return map;
    }
}
