#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGSEGV (0xb) at pc=0x0000000101310660, pid=53790, tid=0x000000000000d003
#
# JRE version: OpenJDK Runtime Environment (8.0_452-b11) (build 1.8.0_452-b11)
# Java VM: OpenJDK 64-Bit Server VM (25.452-b11 mixed mode bsd-aarch64 compressed oops)
# Problematic frame:
# C  [libasyncProfiler.dylib+0x54660]  ThreadFilter::accept(int)+0x10
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   https://bell-sw.com/support
#

---------------  T H R E A D  ---------------

Current thread is native thread

siginfo: si_signo: 11 (SIGSEGV), si_code: 2 (SEGV_ACCERR), si_addr: 0x1e020b300204000e

Registers:
 x0=0x0000000128008108  x1=0x00000000ffffffff  x2=0x0000001800001513  x3=0x0000cf0300000203
 x4=0x00000d4a00000000  x5=0x0000cf0300000000  x6=0x0000000000000040  x7=0x0000000000000000
 x8=0x1e020b3002030012  x9=0x0000000000003fff x10=0x000000000000006c x11=0x00003059aa9c0000
x12=0x0000000000000020 x13=0x0000600004eb4328 x14=0x00000000001ff800 x15=0x00000000000007fb
x16=0xffffffffffffffd1 x17=0x0000000203085e70 x18=0x0000000000000000 x19=0x0000000000000000
x20=0x0000000000000001 x21=0x000000000000d003 x22=0x0000600004a0c380 x23=0x0000000000764c81
x24=0x00000000000186a0 x25=0x0000000000000002 x26=0x00000000ffffffff x27=0x0000000000000000
x28=0x0000000000000001  fp=0x000000016fe82fb0  lr=0x0000000101314698  sp=0x000000016fe82f10
pc=0x0000000101310660 cpsr=0x00000000a0001000
Top of Stack: (sp=0x000000016fe82f10)
0x000000016fe82f10:   0000000101330ed8 0000000128008000
0x000000016fe82f20:   0001e56918bbf778 000000016fe82f40
0x000000016fe82f30:   0000000128008108 000000016fe82f40
0x000000016fe82f40:   0000000000000000 0000000000000000
0x000000016fe82f50:   0000000000000000 0000000000000000
0x000000016fe82f60:   0000000000000000 0000000000000000
0x000000016fe82f70:   0000000000000000 0000000000000000
0x000000016fe82f80:   0000000000000000 0000000000000000
0x000000016fe82f90:   0000000000000000 0000000000000000
0x000000016fe82fa0:   0000000000000000 000000016fe83000
0x000000016fe82fb0:   000000016fe82fc0 0000000101314544
0x000000016fe82fc0:   000000016fe82fe0 0000000193f26c0c
0x000000016fe82fd0:   0000000000000000 0000000000000000
0x000000016fe82fe0:   0000000000000000 0000000193f21b80
0x000000016fe82ff0:   0000000000000000 0000000000000000
0x000000016fe83000:   0bda2e3c7d8f4e63 0000000000000000
0x000000016fe83010:   000000033e687000 000000016ff0f010
0x000000016fe83020:   0000000000000000 0000000000000000
0x000000016fe83030:   0000000000000101 0000000a0000001f
0x000000016fe83040:   0000000000000000 0001000000000000
0x000000016fe83050:   0000000000000000 0000000000000000
0x000000016fe83060:   0000000000000000 0000000000000000
0x000000016fe83070:   0000000000000000 0000000000000000
0x000000016fe83080:   0000000000000000 0000000000000000
0x000000016fe83090:   0000000101314538 0000000101330ed8
0x000000016fe830a0:   0003000000000000 0000003c00000000
0x000000016fe830b0:   000000016fe83000 000000016fe00000
0x000000016fe830c0:   000000016fdfc000 000000000008c000
0x000000016fe830d0:   0000000000004000 0000000000b3401a
0x000000016fe830e0:   000000016fe83000 000000016fe830ac
0x000000016fe830f0:   000000000000cf03 000000000000d003
0x000000016fe83100:   00000000000008ff 0000000000000000 

Instructions: (pc=0x0000000101310660)
0x0000000101310640:   3f 21 40 f1 41 ff ff 54 e0 03 08 aa c0 03 5f d6
0x0000000101310650:   28 7c 13 53 08 58 68 f8 c8 00 00 b4 29 48 05 53
0x0000000101310660:   08 59 69 b8 08 25 c1 1a 00 01 00 12 c0 03 5f d6
0x0000000101310670:   00 00 80 52 c0 03 5f d6 28 7c 13 53 08 58 68 f8 


Register to memory mapping:

 x0=0x0000000128008108 is an unknown value
 x1=0x00000000ffffffff is an unknown value
 x2=0x0000001800001513 is an unknown value
 x3=0x0000cf0300000203 is an unknown value
 x4=0x00000d4a00000000 is an unknown value
 x5=0x0000cf0300000000 is an unknown value
 x6=0x0000000000000040 is an unknown value
 x7=0x0000000000000000 is an unknown value
 x8=0x1e020b3002030012 is an unknown value
 x9=0x0000000000003fff is an unknown value
x10=0x000000000000006c is an unknown value
x11=0x00003059aa9c0000 is an unknown value
x12=0x0000000000000020 is an unknown value
x13=0x0000600004eb4328 is an unknown value
x14=0x00000000001ff800 is an unknown value
x15=0x00000000000007fb is an unknown value
x16=0xffffffffffffffd1 is an unknown value
x17=0x0000000203085e70 is an unknown value
x18=0x0000000000000000 is an unknown value
x19=0x0000000000000000 is an unknown value
x20=0x0000000000000001 is an unknown value
x21=0x000000000000d003 is an unknown value
x22=0x0000600004a0c380 is an unknown value
x23=0x0000000000764c81 is an unknown value
x24=0x00000000000186a0 is an unknown value
x25=0x0000000000000002 is an unknown value
x26=0x00000000ffffffff is an unknown value
x27=0x0000000000000000 is an unknown value
x28=0x0000000000000001 is an unknown value


Stack: [0x000000016fe00000,0x000000016fe83000],  sp=0x000000016fe82f10,  free space=523k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
C  [libasyncProfiler.dylib+0x54660]  ThreadFilter::accept(int)+0x10
C  [libasyncProfiler.dylib+0x58544]  WallClock::threadEntry(void*)+0xc
C  [libsystem_pthread.dylib+0x6c0c]  _pthread_start+0x88


---------------  P R O C E S S  ---------------

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x0000000580000000, size: 9216 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0
Compressed class space size: 1073741824 Address: 0x0000000800000000

Heap:
 PSYoungGen      total 461824K, used 302817K [0x0000000700000000, 0x0000000726a80000, 0x00000007c0000000)
  eden space 449536K, 64% used [0x0000000700000000,0x0000000711c003f0,0x000000071b700000)
  from space 12288K, 97% used [0x0000000723e00000,0x00000007249b8060,0x0000000724a00000)
  to   space 27648K, 0% used [0x0000000724f80000,0x0000000724f80000,0x0000000726a80000)
 ParOldGen       total 500224K, used 65617K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x0000000584014440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K

Card table byte_map: [0x0000000105200000,0x0000000106404000] byte_map_base: 0x0000000102600000

Marking Bits: (ParMarkBitMap*) 0x0000000102465ac8
 Begin Bits: [0x0000000320830000, 0x0000000329830000)
 End Bits:   [0x0000000329830000, 0x0000000332830000)

Polling page: 0x0000000100f70000

CodeCache: size=131072Kb used=20006Kb max_used=20017Kb free=111065Kb
 bounds [0x0000000318000000, 0x00000003193a0000, 0x0000000320000000]
 total_blobs=9351 nmethods=8732 adapters=542
 compilation: enabled

Compilation events (10 events):
Event: 24811.122 Thread 0x0000000149828000 8968       1       io.netty.buffer.PoolThreadCache$MemoryRegionCache$Entry::recycle (23 bytes)
Event: 24811.122 Thread 0x0000000149828000 nmethod 8968 0x0000000318e3a950 code [0x0000000318e3aac0, 0x0000000318e3abe8]
Event: 28007.226 Thread 0x0000000149828000 8970       1       java.util.concurrent.ConcurrentHashMap::remove (32 bytes)
Event: 28007.226 Thread 0x0000000149828000 nmethod 8970 0x0000000318e3a490 code [0x0000000318e3a600, 0x0000000318e3a7c8]
Event: 28213.899 Thread 0x0000000149828000 8971 %     1       java.lang.ref.Reference$ReferenceHandler::run @ 0 (8 bytes)
Event: 28213.899 Thread 0x0000000149828000 nmethod 8971% 0x0000000318e3a110 code [0x0000000318e3a280, 0x0000000318e3a3a8]
Event: 30291.396 Thread 0x0000000149828000 8972 % !   1       org.apache.tomcat.util.net.NioEndpoint$Poller::run @ 0 (263 bytes)
Event: 30291.404 Thread 0x0000000149828000 nmethod 8972% 0x0000000318e38210 code [0x0000000318e38500, 0x0000000318e38f98]
Event: 30803.880 Thread 0x0000000149828000 8973   !   1       org.apache.tomcat.util.net.NioEndpoint$Poller::run (263 bytes)
Event: 30803.883 Thread 0x0000000149828000 nmethod 8973 0x0000000318e36410 code [0x0000000318e36700, 0x0000000318e37118]

GC Heap History (10 events):
Event: 8785.938 GC heap before
{Heap before GC invocations=16 (full 3):
 PSYoungGen      total 525312K, used 525126K [0x0000000700000000, 0x000000072af80000, 0x00000007c0000000)
  eden space 513536K, 100% used [0x0000000700000000,0x000000071f580000,0x000000071f580000)
  from space 11776K, 98% used [0x000000072a400000,0x000000072af51960,0x000000072af80000)
  to   space 32256K, 0% used [0x0000000727080000,0x0000000727080000,0x0000000729000000)
 ParOldGen       total 500224K, used 65577K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x000000058400a440,0x000000059e880000)
 Metaspace       used 66371K, capacity 68074K, committed 68272K, reserved 1110016K
  class space    used 7928K, capacity 8285K, committed 8368K, reserved 1048576K
Event: 8785.974 GC heap after
Heap after GC invocations=16 (full 3):
 PSYoungGen      total 512512K, used 12624K [0x0000000700000000, 0x000000072a480000, 0x00000007c0000000)
  eden space 499712K, 0% used [0x0000000700000000,0x0000000700000000,0x000000071e800000)
  from space 12800K, 98% used [0x0000000727080000,0x0000000727cd4070,0x0000000727d00000)
  to   space 31232K, 0% used [0x0000000728600000,0x0000000728600000,0x000000072a480000)
 ParOldGen       total 500224K, used 65585K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x000000058400c440,0x000000059e880000)
 Metaspace       used 66371K, capacity 68074K, committed 68272K, reserved 1110016K
  class space    used 7928K, capacity 8285K, committed 8368K, reserved 1048576K
}
Event: 13620.999 GC heap before
{Heap before GC invocations=17 (full 3):
 PSYoungGen      total 512512K, used 512336K [0x0000000700000000, 0x000000072a480000, 0x00000007c0000000)
  eden space 499712K, 100% used [0x0000000700000000,0x000000071e800000,0x000000071e800000)
  from space 12800K, 98% used [0x0000000727080000,0x0000000727cd4070,0x0000000727d00000)
  to   space 31232K, 0% used [0x0000000728600000,0x0000000728600000,0x000000072a480000)
 ParOldGen       total 500224K, used 65585K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x000000058400c440,0x000000059e880000)
 Metaspace       used 66386K, capacity 68080K, committed 68272K, reserved 1110016K
  class space    used 7930K, capacity 8286K, committed 8368K, reserved 1048576K
Event: 13621.041 GC heap after
Heap after GC invocations=17 (full 3):
 PSYoungGen      total 499200K, used 12624K [0x0000000700000000, 0x0000000729280000, 0x00000007c0000000)
  eden space 486400K, 0% used [0x0000000700000000,0x0000000700000000,0x000000071db00000)
  from space 12800K, 98% used [0x0000000728600000,0x0000000729254070,0x0000000729280000)
  to   space 30208K, 0% used [0x0000000725780000,0x0000000725780000,0x0000000727500000)
 ParOldGen       total 500224K, used 65593K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x000000058400e440,0x000000059e880000)
 Metaspace       used 66386K, capacity 68080K, committed 68272K, reserved 1110016K
  class space    used 7930K, capacity 8286K, committed 8368K, reserved 1048576K
}
Event: 18397.276 GC heap before
{Heap before GC invocations=18 (full 3):
 PSYoungGen      total 499200K, used 499024K [0x0000000700000000, 0x0000000729280000, 0x00000007c0000000)
  eden space 486400K, 100% used [0x0000000700000000,0x000000071db00000,0x000000071db00000)
  from space 12800K, 98% used [0x0000000728600000,0x0000000729254070,0x0000000729280000)
  to   space 30208K, 0% used [0x0000000725780000,0x0000000725780000,0x0000000727500000)
 ParOldGen       total 500224K, used 65593K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x000000058400e440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K
Event: 18397.324 GC heap after
Heap after GC invocations=18 (full 3):
 PSYoungGen      total 485888K, used 12128K [0x0000000700000000, 0x0000000728680000, 0x00000007c0000000)
  eden space 473600K, 0% used [0x0000000700000000,0x0000000700000000,0x000000071ce80000)
  from space 12288K, 98% used [0x0000000725780000,0x0000000726358080,0x0000000726380000)
  to   space 29184K, 0% used [0x0000000726a00000,0x0000000726a00000,0x0000000728680000)
 ParOldGen       total 500224K, used 65601K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x0000000584010440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K
}
Event: 23367.681 GC heap before
{Heap before GC invocations=19 (full 3):
 PSYoungGen      total 485888K, used 485728K [0x0000000700000000, 0x0000000728680000, 0x00000007c0000000)
  eden space 473600K, 100% used [0x0000000700000000,0x000000071ce80000,0x000000071ce80000)
  from space 12288K, 98% used [0x0000000725780000,0x0000000726358080,0x0000000726380000)
  to   space 29184K, 0% used [0x0000000726a00000,0x0000000726a00000,0x0000000728680000)
 ParOldGen       total 500224K, used 65601K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x0000000584010440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K
Event: 23367.740 GC heap after
Heap after GC invocations=19 (full 3):
 PSYoungGen      total 473600K, used 12096K [0x0000000700000000, 0x0000000727600000, 0x00000007c0000000)
  eden space 461312K, 0% used [0x0000000700000000,0x0000000700000000,0x000000071c280000)
  from space 12288K, 98% used [0x0000000726a00000,0x00000007275d0060,0x0000000727600000)
  to   space 28672K, 0% used [0x0000000723e00000,0x0000000723e00000,0x0000000725a00000)
 ParOldGen       total 500224K, used 65609K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x0000000584012440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K
}
Event: 28213.854 GC heap before
{Heap before GC invocations=20 (full 3):
 PSYoungGen      total 473600K, used 473408K [0x0000000700000000, 0x0000000727600000, 0x00000007c0000000)
  eden space 461312K, 100% used [0x0000000700000000,0x000000071c280000,0x000000071c280000)
  from space 12288K, 98% used [0x0000000726a00000,0x00000007275d0060,0x0000000727600000)
  to   space 28672K, 0% used [0x0000000723e00000,0x0000000723e00000,0x0000000725a00000)
 ParOldGen       total 500224K, used 65609K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x0000000584012440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K
Event: 28213.896 GC heap after
Heap after GC invocations=20 (full 3):
 PSYoungGen      total 461824K, used 12000K [0x0000000700000000, 0x0000000726a80000, 0x00000007c0000000)
  eden space 449536K, 0% used [0x0000000700000000,0x0000000700000000,0x000000071b700000)
  from space 12288K, 97% used [0x0000000723e00000,0x00000007249b8060,0x0000000724a00000)
  to   space 27648K, 0% used [0x0000000724f80000,0x0000000724f80000,0x0000000726a80000)
 ParOldGen       total 500224K, used 65617K [0x0000000580000000, 0x000000059e880000, 0x0000000700000000)
  object space 500224K, 13% used [0x0000000580000000,0x0000000584014440,0x000000059e880000)
 Metaspace       used 66403K, capacity 68086K, committed 68272K, reserved 1110016K
  class space    used 7931K, capacity 8287K, committed 8368K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (1 events):
Event: 0.458 Thread 0x0000000147057000 redefined class name=java.lang.Throwable, count=1

Internal exceptions (10 events):
Event: 31173.994 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x000000071161a1f8) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31173.994 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x000000071161a898) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31183.993 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x0000000711623480) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31183.993 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x0000000711623b20) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31194.015 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x000000071162c708) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31194.016 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x000000071162cda8) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31204.000 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x0000000711635990) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31204.000 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x0000000711636030) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31214.022 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x000000071163ec18) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]
Event: 31214.022 Thread 0x000000014a6f6800 Exception <a 'java/io/FileNotFoundException'> (0x000000071163f2b8) thrown at [/System/Volumes/Data/ws/workspace/8u452/jdk-8u452/CT/std/label/macosx-aarch64/type/b8-20/b/hotspot/src/share/vm/prims/jni.cpp, line 712]

Events (10 events):
Event: 31213.687 Executing VM operation: RevokeBias done
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT PACKING pc=0x00000003185d86a0 sp=0x000000033511a440
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT UNPACKING pc=0x0000000318039480 sp=0x000000033511a1e0 mode 1
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT PACKING pc=0x00000003192ce080 sp=0x000000033511a4d0
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT UNPACKING pc=0x0000000318039480 sp=0x000000033511a490 mode 1
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT PACKING pc=0x00000003185d86a0 sp=0x000000033511a5f0
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT UNPACKING pc=0x0000000318039480 sp=0x000000033511a390 mode 1
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT PACKING pc=0x0000000318201220 sp=0x000000033511a680
Event: 31214.022 Thread 0x000000014a6f6800 DEOPT UNPACKING pc=0x0000000318039480 sp=0x000000033511a460 mode 1
Event: 31215.013 Thread 0x00000001271fd800 Thread exited: 0x00000001271fd800


Dynamic libraries:
0x00000001b0736000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x0000000197efd000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x000000019b1ec000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x0000000195585000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00000001a2169000 	/usr/lib/libSystem.B.dylib
0x000000019938f000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x000000024249f000 	/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore
0x00000001a975c000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x000000019fcfd000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00000001a4933000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00000001a4c8a000 	/System/Library/PrivateFrameworks/UserActivity.framework/Versions/A/UserActivity
0x0000000270f0f000 	/System/Library/PrivateFrameworks/UIIntelligenceSupport.framework/Versions/A/UIIntelligenceSupport
0x00000001fae85000 	/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore
0x0000000275f88000 	/System/Library/PrivateFrameworks/WritingTools.framework/Versions/A/WritingTools
0x0000000274fd0000 	/System/Library/PrivateFrameworks/WindowManagement.framework/Versions/A/WindowManagement
0x00000001951e9000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00000001a3d97000 	/usr/lib/libspindump.dylib
0x0000000199541000 	/System/Library/Frameworks/UniformTypeIdentifiers.framework/Versions/A/UniformTypeIdentifiers
0x00000001a155b000 	/usr/lib/libbsm.0.dylib
0x000000019d780000 	/usr/lib/libapp_launch_measurement.dylib
0x000000019cb29000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x000000019d784000 	/System/Library/PrivateFrameworks/CoreAutoLayout.framework/Versions/A/CoreAutoLayout
0x000000019f317000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00000001a053b000 	/usr/lib/liblangid.dylib
0x000000019fd03000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x0000000199f67000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x000000019a48d000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00000001a9e3b000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00000001a3bda000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x000000019f2f4000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x000000019cb5a000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00000001a20b3000 	/usr/lib/libz.1.dylib
0x00000001add4e000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x000000019fce8000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x0000000197762000 	/usr/lib/libicucore.A.dylib
0x00000001a5d11000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00000001a4c3b000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00000001c0ca5000 	/System/Library/PrivateFrameworks/TextInput.framework/Versions/A/TextInput
0x0000000199eb2000 	/usr/lib/libMobileGestalt.dylib
0x000000019f9e1000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x000000019d061000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x0000000197357000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00000001a9798000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x000000019d487000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x0000000196c22000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x000000019cc48000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00000001a4200000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x0000000199eb0000 	/usr/lib/libenergytrace.dylib
0x00000001b504b000 	/System/Library/PrivateFrameworks/RenderBox.framework/Versions/A/RenderBox
0x0000000197dad000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00000001a9b8f000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x000000019d711000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00000001f4855000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x000000019d7ce000 	/usr/lib/libxml2.2.dylib
0x00000001a143f000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x0000000193b2c000 	/usr/lib/libobjc.A.dylib
0x0000000193e39000 	/usr/lib/libc++.1.dylib
0x00000001a9b10000 	/System/Library/Frameworks/Accessibility.framework/Versions/A/Accessibility
0x000000019abbd000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x0000000193f95000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00000001a00bd000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x0000000196a03000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00000001f5d9a000 	/System/Library/Frameworks/CoreTransferable.framework/Versions/A/CoreTransferable
0x00000001f631e000 	/System/Library/Frameworks/DataDetection.framework/Versions/A/DataDetection
0x00000001f6321000 	/System/Library/Frameworks/DeveloperToolsSupport.framework/Versions/A/DeveloperToolsSupport
0x000000019fd3e000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00000001fba48000 	/System/Library/Frameworks/Symbols.framework/Versions/A/Symbols
0x00000001e8064000 	/System/Library/PrivateFrameworks/FeatureFlags.framework/Versions/A/FeatureFlags
0x00000001a216e000 	/System/Library/PrivateFrameworks/SoftLinking.framework/Versions/A/SoftLinking
0x00000001d3978000 	/usr/lib/swift/libswiftAccelerate.dylib
0x00000001a56a6000 	/usr/lib/swift/libswiftCore.dylib
0x00000001bd34e000 	/usr/lib/swift/libswiftCoreFoundation.dylib
0x00000001bd3a8000 	/usr/lib/swift/libswiftCoreImage.dylib
0x00000001ba762000 	/usr/lib/swift/libswiftDarwin.dylib
0x000000027cb9d000 	/usr/lib/swift/libswiftDataDetection.dylib
0x00000001ab687000 	/usr/lib/swift/libswiftDispatch.dylib
0x00000001bd3a9000 	/usr/lib/swift/libswiftIOKit.dylib
0x00000001c9eff000 	/usr/lib/swift/libswiftMetal.dylib
0x00000001d8ed5000 	/usr/lib/swift/libswiftOSLog.dylib
0x00000001ae2f4000 	/usr/lib/swift/libswiftObjectiveC.dylib
0x000000027cbca000 	/usr/lib/swift/libswiftObservation.dylib
0x00000001cf326000 	/usr/lib/swift/libswiftQuartzCore.dylib
0x00000001d3969000 	/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib
0x00000001bd360000 	/usr/lib/swift/libswiftXPC.dylib
0x000000027ccb0000 	/usr/lib/swift/libswift_Builtin_float.dylib
0x000000027ccb3000 	/usr/lib/swift/libswift_Concurrency.dylib
0x000000027ce12000 	/usr/lib/swift/libswift_StringProcessing.dylib
0x000000027cea5000 	/usr/lib/swift/libswift_errno.dylib
0x000000027cea7000 	/usr/lib/swift/libswift_math.dylib
0x000000027ceaa000 	/usr/lib/swift/libswift_signal.dylib
0x000000027ceab000 	/usr/lib/swift/libswift_stdio.dylib
0x000000027ceac000 	/usr/lib/swift/libswift_time.dylib
0x00000001ae2f8000 	/usr/lib/swift/libswiftos.dylib
0x00000001c0bfc000 	/usr/lib/swift/libswiftsimd.dylib
0x000000027cead000 	/usr/lib/swift/libswiftsys_time.dylib
0x000000027ceae000 	/usr/lib/swift/libswiftunistd.dylib
0x00000001a2396000 	/usr/lib/libcompression.dylib
0x00000001a4893000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00000001a3879000 	/usr/lib/libate.dylib
0x00000001a2163000 	/usr/lib/system/libcache.dylib
0x00000001a211e000 	/usr/lib/system/libcommonCrypto.dylib
0x00000001a2149000 	/usr/lib/system/libcompiler_rt.dylib
0x00000001a213e000 	/usr/lib/system/libcopyfile.dylib
0x0000000193c87000 	/usr/lib/system/libcorecrypto.dylib
0x0000000193d6d000 	/usr/lib/system/libdispatch.dylib
0x0000000193f2d000 	/usr/lib/system/libdyld.dylib
0x00000001a2159000 	/usr/lib/system/libkeymgr.dylib
0x00000001a2101000 	/usr/lib/system/libmacho.dylib
0x00000001a1534000 	/usr/lib/system/libquarantine.dylib
0x00000001a2156000 	/usr/lib/system/libremovefile.dylib
0x0000000199f2c000 	/usr/lib/system/libsystem_asl.dylib
0x0000000193c1c000 	/usr/lib/system/libsystem_blocks.dylib
0x0000000193db7000 	/usr/lib/system/libsystem_c.dylib
0x00000001a214d000 	/usr/lib/system/libsystem_collections.dylib
0x00000001a0528000 	/usr/lib/system/libsystem_configuration.dylib
0x000000019f2c3000 	/usr/lib/system/libsystem_containermanager.dylib
0x00000001a1c44000 	/usr/lib/system/libsystem_coreservices.dylib
0x0000000197a2e000 	/usr/lib/system/libsystem_darwin.dylib
0x000000027cfe5000 	/usr/lib/system/libsystem_darwindirectory.dylib
0x00000001a215a000 	/usr/lib/system/libsystem_dnssd.dylib
0x000000027cfe9000 	/usr/lib/system/libsystem_eligibility.dylib
0x0000000193db4000 	/usr/lib/system/libsystem_featureflags.dylib
0x0000000193f65000 	/usr/lib/system/libsystem_info.dylib
0x00000001a20c2000 	/usr/lib/system/libsystem_m.dylib
0x0000000193d26000 	/usr/lib/system/libsystem_malloc.dylib
0x0000000199e95000 	/usr/lib/system/libsystem_networkextension.dylib
0x0000000197e90000 	/usr/lib/system/libsystem_notify.dylib
0x00000001a052d000 	/usr/lib/system/libsystem_sandbox.dylib
0x000000027cff1000 	/usr/lib/system/libsystem_sanitizers.dylib
0x00000001a2152000 	/usr/lib/system/libsystem_secinit.dylib
0x0000000193ee4000 	/usr/lib/system/libsystem_kernel.dylib
0x0000000193f5d000 	/usr/lib/system/libsystem_platform.dylib
0x0000000193f20000 	/usr/lib/system/libsystem_pthread.dylib
0x000000019ba86000 	/usr/lib/system/libsystem_symptoms.dylib
0x0000000193c6b000 	/usr/lib/system/libsystem_trace.dylib
0x00000001a212c000 	/usr/lib/system/libunwind.dylib
0x0000000193c20000 	/usr/lib/system/libxpc.dylib
0x0000000193ec6000 	/usr/lib/libc++abi.dylib
0x000000027bbbd000 	/usr/lib/libRosetta.dylib
0x0000000197d2c000 	/System/Library/PrivateFrameworks/CoreServicesInternal.framework/Versions/A/CoreServicesInternal
0x00000001a2136000 	/usr/lib/liboah.dylib
0x00000001a216b000 	/usr/lib/libfakelink.dylib
0x00000001ad801000 	/System/Library/PrivateFrameworks/DiskImages.framework/Versions/A/DiskImages
0x00000001ba28b000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x0000000199aca000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x000000019d748000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x0000000197a39000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x000000019cbbd000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00000001a1c4b000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00000001a22b8000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x000000019ba01000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x00000001944d4000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00000001a36ce000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x000000019d756000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00000001a234d000 	/usr/lib/libapple_nghttp2.dylib
0x000000019b662000 	/usr/lib/libsqlite3.dylib
0x00000001a6389000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x000000019b998000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x000000019ba8f000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00000001a2105000 	/usr/lib/system/libkxld.dylib
0x000000023e059000 	/System/Library/PrivateFrameworks/AppleKeyStore.framework/Versions/A/AppleKeyStore
0x000000027ba15000 	/usr/lib/libCoreEntitlements.dylib
0x0000000259495000 	/System/Library/PrivateFrameworks/MessageSecurity.framework/Versions/A/MessageSecurity
0x000000019b647000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00000001a1c2b000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00000001a1543000 	/usr/lib/libcoretls.dylib
0x00000001a3744000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00000001a2390000 	/usr/lib/libpam.2.dylib
0x00000001a37b8000 	/usr/lib/libxar.1.dylib
0x00000001a21c0000 	/usr/lib/libarchive.2.dylib
0x00000001a7a8b000 	/System/Library/Frameworks/Combine.framework/Versions/A/Combine
0x00000002424c3000 	/System/Library/PrivateFrameworks/CollectionsInternal.framework/Versions/A/CollectionsInternal
0x00000002624bc000 	/System/Library/PrivateFrameworks/ReflectionInternal.framework/Versions/A/ReflectionInternal
0x000000026318e000 	/System/Library/PrivateFrameworks/RuntimeInternal.framework/Versions/A/RuntimeInternal
0x000000027cc41000 	/usr/lib/swift/libswiftSystem.dylib
0x00000001a0536000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00000001bd239000 	/System/Library/PrivateFrameworks/LoggingSupport.framework/Versions/A/LoggingSupport
0x00000001aab48000 	/System/Library/PrivateFrameworks/PowerLog.framework/Versions/A/PowerLog
0x00000001fad4a000 	/System/Library/Frameworks/SwiftData.framework/Versions/A/SwiftData
0x000000019d959000 	/System/Library/PrivateFrameworks/UserManagement.framework/Versions/A/UserManagement
0x00000001999f8000 	/usr/lib/libboringssl.dylib
0x000000019ba75000 	/usr/lib/libdns_services.dylib
0x00000001bc3ed000 	/usr/lib/libquic.dylib
0x00000001a5635000 	/usr/lib/libusrtcp.dylib
0x00000001e0d3f000 	/System/Library/PrivateFrameworks/InternalSwiftProtobuf.framework/Versions/A/InternalSwiftProtobuf
0x000000027cb9e000 	/usr/lib/swift/libswiftDistributed.dylib
0x000000027cc37000 	/usr/lib/swift/libswiftSynchronization.dylib
0x0000000199ac9000 	/usr/lib/libnetwork.dylib
0x00000001cdf65000 	/System/Library/PrivateFrameworks/OSAnalytics.framework/Versions/A/OSAnalytics
0x00000001a2328000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00000001abe1d000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00000001dca0b000 	/System/Library/PrivateFrameworks/Symbolication.framework/Versions/A/Symbolication
0x00000001abdf9000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000027b8e0000 	/usr/lib/libAppleArchive.dylib
0x00000001a1c37000 	/usr/lib/libbz2.1.0.dylib
0x00000001a3725000 	/usr/lib/liblzma.5.dylib
0x00000001a1233000 	/System/Library/PrivateFrameworks/IOMobileFramebuffer.framework/Versions/A/IOMobileFramebuffer
0x00000001bd2c2000 	/System/Library/PrivateFrameworks/MallocStackLogging.framework/Versions/A/MallocStackLogging
0x00000001a3913000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x0000000194fe0000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00000002517e5000 	/System/Library/PrivateFrameworks/IO80211.framework/Versions/A/IO80211
0x00000001ab91f000 	/System/Library/PrivateFrameworks/FrontBoardServices.framework/Versions/A/FrontBoardServices
0x00000001a146b000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00000001a15cd000 	/usr/lib/libgermantok.dylib
0x00000001a065a000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x000000019b8b3000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00000001ab9ec000 	/System/Library/PrivateFrameworks/BoardServices.framework/Versions/A/BoardServices
0x00000001a145c000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00000001ad63c000 	/System/Library/PrivateFrameworks/BackBoardServices.framework/Versions/A/BackBoardServices
0x000000019f70e000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x0000000199f44000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00000001b11d7000 	/System/Library/PrivateFrameworks/IOSurfaceAccelerator.framework/Versions/A/IOSurfaceAccelerator
0x00000001a41fe000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x000000019652a000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x000000019f5b2000 	/System/Library/Frameworks/CoreMedia.framework/Versions/A/CoreMedia
0x000000019f30d000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x000000019d8b8000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00000001a238e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x000000026180d000 	/System/Library/PrivateFrameworks/ProDisplayLibrary.framework/Versions/A/ProDisplayLibrary
0x00000001a4246000 	/System/Library/Frameworks/VideoToolbox.framework/Versions/A/VideoToolbox
0x00000001a0534000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00000001a3746000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00000001a37c7000 	/usr/lib/libutil.dylib
0x000000026c6cc000 	/System/Library/PrivateFrameworks/SwiftASN1Internal.framework/Versions/A/SwiftASN1Internal
0x000000019cc51000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00000001a9b6a000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00000001a37ff000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x000000019494c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00000001a5f60000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x000000019ad73000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00000001a481c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00000001a6353000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00000001a634a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00000001a5f32000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00000001a1200000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00000002488be000 	/System/Library/PrivateFrameworks/FontServices.framework/Versions/A/FontServices
0x00000001a3d4c000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x000000019d435000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x000000027bfa1000 	/usr/lib/libhvf.dylib
0x000000025d71b000 	/System/Library/PrivateFrameworks/ParsingInternal.framework/Versions/A/ParsingInternal
0x000000027cbe7000 	/usr/lib/swift/libswiftRegexBuilder.dylib
0x000000027cd6f000 	/usr/lib/swift/libswift_RegexParser.dylib
0x00000001a40c7000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00000001a3b68000 	/usr/lib/libexpat.1.dylib
0x00000001a46f2000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00000001a471d000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00000001a4807000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00000001a410d000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00000001a47ae000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00000001a47a5000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x000000024d58c000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libllvm-flatbuffers.dylib
0x0000000248989000 	/System/Library/PrivateFrameworks/FramePacing.framework/Versions/A/FramePacing
0x00000001f4847000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00000002494fe000 	/System/Library/PrivateFrameworks/GPUCompiler.framework/Versions/32023/Libraries/libGPUCompilerUtils.dylib
0x00000002488bf000 	/System/Library/PrivateFrameworks/FontServices.framework/libXTFontStaticRegistryData.dylib
0x00000001af43d000 	/System/Library/PrivateFrameworks/ASEProcessing.framework/Versions/A/ASEProcessing
0x000000026097c000 	/System/Library/PrivateFrameworks/PhotosensitivityProcessing.framework/Versions/A/PhotosensitivityProcessing
0x00000001a3d40000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00000001f48a5000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00000001f4869000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00000001f4a31000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00000001f4872000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00000001f4866000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00000001f484f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00000001a0478000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00000001a1b96000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00000001a15e5000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00000001a1a1c000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00000001a1831000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00000001a1a4e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00000001f813c000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSFunctions.framework/Versions/A/MPSFunctions
0x00000001f811e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/Frameworks/MPSBenchmarkLoop.framework/Versions/A/MPSBenchmarkLoop
0x00000001947c7000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00000001c1fa2000 	/System/Library/PrivateFrameworks/IOAccelMemoryInfo.framework/Versions/A/IOAccelMemoryInfo
0x00000001cf71b000 	/System/Library/PrivateFrameworks/kperf.framework/Versions/A/kperf
0x00000001bd329000 	/System/Library/PrivateFrameworks/GPURawCounter.framework/Versions/A/GPURawCounter
0x00000001a47d9000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00000001a62de000 	/usr/lib/libcups.2.dylib
0x00000001a6378000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00000001a5fde000 	/usr/lib/libresolv.9.dylib
0x00000001a21a5000 	/usr/lib/libiconv.2.dylib
0x00000001a3d9e000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00000001ae24b000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00000001a3b83000 	/usr/lib/libheimdal-asn1.dylib
0x000000019d71b000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00000001a63db000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x000000019d729000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00000001a2100000 	/usr/lib/libcharset.1.dylib
0x00000001f4dcd000 	/System/Library/Frameworks/AVFAudio.framework/Versions/A/AVFAudio
0x00000001b6a11000 	/System/Library/PrivateFrameworks/AXCoreUtilities.framework/Versions/A/AXCoreUtilities
0x0000000255114000 	/System/Library/PrivateFrameworks/IsolatedCoreAudioClient.framework/Versions/A/IsolatedCoreAudioClient
0x00000001a5f19000 	/usr/lib/libAudioStatistics.dylib
0x000000019f58a000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x000000019664b000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00000001b0563000 	/System/Library/Frameworks/CoreMIDI.framework/Versions/A/CoreMIDI
0x00000001a5ebc000 	/System/Library/PrivateFrameworks/AudioSession.framework/Versions/A/AudioSession
0x00000001a77f3000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00000001a3c6d000 	/System/Library/PrivateFrameworks/MediaExperience.framework/Versions/A/MediaExperience
0x000000026f187000 	/System/Library/PrivateFrameworks/Tightbeam.framework/Versions/A/Tightbeam
0x0000000242f80000 	/System/Library/PrivateFrameworks/CoreAudioOrchestration.framework/Versions/A/CoreAudioOrchestration
0x00000001d403b000 	/System/Library/PrivateFrameworks/AFKUser.framework/Versions/A/AFKUser
0x00000001bd32d000 	/usr/lib/swift/libswiftCoreAudio.dylib
0x00000001a3d8d000 	/System/Library/PrivateFrameworks/AppServerSupport.framework/Versions/A/AppServerSupport
0x00000001a635c000 	/System/Library/PrivateFrameworks/perfdata.framework/Versions/A/perfdata
0x00000001bc4e6000 	/System/Library/PrivateFrameworks/SystemPolicy.framework/Versions/A/SystemPolicy
0x00000001a61fd000 	/usr/lib/libSMC.dylib
0x00000001a46bc000 	/usr/lib/libAudioToolboxUtility.dylib
0x00000001a636a000 	/usr/lib/libperfcheck.dylib
0x000000023ee16000 	/System/Library/PrivateFrameworks/AudioAnalytics.framework/Versions/A/AudioAnalytics
0x00000001e0b06000 	/System/Library/Frameworks/OSLog.framework/Versions/A/OSLog
0x00000001bd2f4000 	/usr/lib/libmis.dylib
0x00000001a5cca000 	/System/Library/PrivateFrameworks/AudioSession.framework/libSessionUtility.dylib
0x00000001a480d000 	/System/Library/PrivateFrameworks/CMCaptureCore.framework/Versions/A/CMCaptureCore
0x00000001f646e000 	/System/Library/Frameworks/ExtensionFoundation.framework/Versions/A/ExtensionFoundation
0x00000001abf2a000 	/System/Library/PrivateFrameworks/CoreTime.framework/Versions/A/CoreTime
0x00000001a3a67000 	/System/Library/PrivateFrameworks/PlugInKit.framework/Versions/A/PlugInKit
0x00000001aaa51000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00000001ae24c000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00000001a12c4000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x0000000245f24000 	/System/Library/PrivateFrameworks/CoreUtilsExtras.framework/Versions/A/CoreUtilsExtras
0x00000001a9e8b000 	/System/Library/Frameworks/MediaAccessibility.framework/Versions/A/MediaAccessibility
0x00000001c593e000 	/System/Library/PrivateFrameworks/AttributeGraph.framework/Versions/A/AttributeGraph
0x000000027b85e000 	/usr/lib/libAXSafeCategoryBundle.dylib
0x00000001b6acd000 	/usr/lib/libAccessibility.dylib
0x00000001a110e000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00000001a246c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00000001a15d0000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00000001a2367000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00000001a2467000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00000001a0661000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00000001950ed000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x0000000256885000 	/System/Library/PrivateFrameworks/MIL.framework/Versions/A/MIL
0x00000001a47a0000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00000001a4780000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00000001a47a8000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00000001e0f0d000 	/System/Library/PrivateFrameworks/HIDDisplay.framework/Versions/A/HIDDisplay
0x00000001b5cec000 	/System/Library/PrivateFrameworks/HID.framework/Versions/A/HID
0x00000002729c3000 	/System/Library/PrivateFrameworks/VideoToolboxParavirtualizationSupport.framework/Versions/A/VideoToolboxParavirtualizationSupport
0x00000001a3b1f000 	/System/Library/PrivateFrameworks/AppleVA.framework/Versions/A/AppleVA
0x00000001aba99000 	/System/Library/PrivateFrameworks/GraphicsServices.framework/Versions/A/GraphicsServices
0x00000001a654e000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00000001aac39000 	/System/Library/PrivateFrameworks/MediaKit.framework/Versions/A/MediaKit
0x00000001aab84000 	/System/Library/Frameworks/DiscRecording.framework/Versions/A/DiscRecording
0x00000001a633d000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x000000019d919000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00000001a3b8d000 	/System/Library/PrivateFrameworks/IconFoundation.framework/Versions/A/IconFoundation
0x00000001a390a000 	/usr/lib/libIOReport.dylib
0x000000023ea26000 	/System/Library/PrivateFrameworks/AppleMobileFileIntegrity.framework/Versions/A/AppleMobileFileIntegrity
0x000000027bc20000 	/usr/lib/libTLE.dylib
0x00000001f18a3000 	/System/Library/PrivateFrameworks/ConfigProfileHelper.framework/Versions/A/ConfigProfileHelper
0x00000001a156d000 	/usr/lib/libmecab.dylib
0x000000019527d000 	/usr/lib/libCRFSuite.dylib
0x00000001a053d000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00000001a2320000 	/usr/lib/libThaiTokenizer.dylib
0x00000001a1537000 	/usr/lib/libCheckFix.dylib
0x000000019cb5c000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x00000002520c7000 	/System/Library/PrivateFrameworks/InstalledContentLibrary.framework/Versions/A/InstalledContentLibrary
0x0000000197d6c000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00000001cf825000 	/System/Library/PrivateFrameworks/MobileSystemServices.framework/Versions/A/MobileSystemServices
0x00000001a37cb000 	/usr/lib/libxslt.1.dylib
0x00000001a14fa000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00000001ad9bf000 	/usr/lib/libcurl.4.dylib
0x000000027be54000 	/usr/lib/libcrypto.46.dylib
0x000000027c97c000 	/usr/lib/libssl.48.dylib
0x00000001ad698000 	/System/Library/Frameworks/LDAP.framework/Versions/A/LDAP
0x00000001ad6d4000 	/System/Library/PrivateFrameworks/TrustEvaluationAgent.framework/Versions/A/TrustEvaluationAgent
0x00000001a5ffb000 	/usr/lib/libsasl2.2.dylib
0x00000001ba761000 	/usr/lib/swift/libswiftCoreGraphics.dylib
0x00000001aa4b9000 	/usr/lib/swift/libswiftFoundation.dylib
0x00000001f132d000 	/usr/lib/swift/libswiftSwiftOnoneSupport.dylib
0x00000001dd1bf000 	/System/Library/PrivateFrameworks/CoreMaterial.framework/Versions/A/CoreMaterial
0x0000000263285000 	/System/Library/PrivateFrameworks/SFSymbols.framework/Versions/A/SFSymbols
0x00000001a9785000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x0000000101dfc000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/server/libjvm.dylib
0x0000000100f88000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libverify.dylib
0x0000000100fec000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libjava.dylib
0x0000000101088000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libjdwp.dylib
0x0000000100fa0000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libnpt.dylib
0x0000000101038000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libinstrument.dylib
0x00000001012bc000 	/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib
0x0000000101184000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libzip.dylib
0x0000000101254000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libdt_socket.dylib
0x0000000113328000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libnio.dylib
0x000000011337c000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libnet.dylib
0x00000001137e8000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libmanagement.dylib
0x0000000114644000 	/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/libsunec.dylib

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:61487,suspend=y,server=n -javaagent:/Users/<USER>/Library/Caches/JetBrains/IntelliJIdea2025.1/captureAgent/debugger-agent.jar=file:///var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/capture18364096304538140599.props -agentpath:/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/idea_libasyncProfiler_dylib_temp_folder/libasyncProfiler.dylib=version,jfr,event=wall,interval=10ms,cstack=no,file=/Users/<USER>/IdeaSnapshots/PopSubApplication_2025_06_19_173548.jfr,log=/private/var/folders/00/tm7r46zd2qdgsp1wxs6k43400000gn/T/PopSubApplication_2025_06_19_173548.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dsocket.server.port=33833 -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.pes.jd.application.PopSubApplication
java_class_path (initial): /Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/charsets.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/cldrdata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/dnsns.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/jaccess.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/localedata.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/nashorn.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/sunec.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/sunjce_provider.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/sunpkcs11.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/ext/zipfs.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/jce.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/jfr.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/jsse.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/management-agent.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/resources.jar:/Users/<USER>/Library/Java/JavaVirtualMachines/liberica-1.8.0_452/jre/lib/rt.jar:/Users/<USER>/code/java/MyjavaProject/insight/yiyitech-pop-dailysub/target/classes:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.17.RELEASE/spring-boot-starter-web-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.17.RELEASE/spring-boot-starter-tomcat-1.5.17.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.34/tomcat-embed-core-8.5.34.jar:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/8.5.34/tomcat-annotations-api-8.5.34.jar:/Users/<USER>/.m2/re
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/opt/homebrew/Cellar/node/24.1.0/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/Library/Frameworks/Python.framework/Versions/3.13/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Users/<USER>/.orbstack/bin
SHELL=/bin/zsh

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x51ddec], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x51ddec], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x41e1f0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x41e1f0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x41e1f0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x41e1f0], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x41eb24], sa_mask[0]=00000000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x41cb5c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x41cb5c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x41cb5c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x41cb5c], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 24.5.0 Darwin Kernel Version 24.5.0: Tue Apr 22 19:54:29 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6030 arm64
rlimit: STACK 8176k, CORE 0k, NPROC 6000, NOFILE 10240, AS infinity
load average:4.16 4.78 5.11

CPU:total 12 (initial active 12) simd, crc, lse

Memory: 16k page, physical 37748736k(104416k free)

/proc/meminfo:


vm_info: OpenJDK 64-Bit Server VM (25.452-b11) for bsd-aarch64 JRE (1.8.0_452-b11), built on Apr 12 2025 06:32:47 by "re" with gcc Apple LLVM 13.1.6 (clang-1316.0.21.2.5)

time: Fri Jun 20 13:40:31 2025
timezone: CST
elapsed time: 31215.029789 seconds (0d 8h 40m 15s)

