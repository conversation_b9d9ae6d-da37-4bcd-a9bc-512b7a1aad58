FROM harbor.yiyitech.com:81/insight/my-openjdk:8-jdk-alpine
WORKDIR /opt/local/app/
ENV TZ=Asia/Shanghai
VOLUME /tmp
ARG JAR_FILE
COPY target/${JAR_FILE} app.jar
EXPOSE 9400
ENTRYPOINT [ "sh", "-c", "java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar app.jar" ]
#ENV JAVA_OPTS="\
#-server \
#-Xmx4g \
#-Xms4g \
#-Xmn2g \
#-XX:SurvivorRatio=8 \
#-XX:MetaspaceSize=256m \
#-XX:MaxMetaspaceSize=512m \
#-XX:+UseParallelGC \
#-XX:ParallelGCThreads=4 \
#-XX:+UseParallelOldGC \
#-XX:+UseAdaptiveSizePolicy \
#-XX:+PrintGCDetails \
#-XX:+PrintTenuringDistribution \
#-XX:+PrintGCTimeStamps \
#-XX:+HeapDumpOnOutOfMemoryError \
#-XX:HeapDumpPath=/ \
#-Xloggc:/gc.log \
#-XX:+UseGCLogFileRotation \
#-XX:NumberOfGCLogFiles=5 \
#-XX:GCLogFileSize=10M"
#ENTRYPOINT [ "sh", "-c", "java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /app.jar" ]
